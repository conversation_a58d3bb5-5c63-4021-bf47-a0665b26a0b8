{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\domain\\entities\\asset-relationship.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,iDAAuC;AAEvC;;;GAGG;AAOI,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IA6R5B;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,KAAK,UAAU,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,KAAK,eAAe,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,KAAK,MAAM,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC3D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,yBAAyB;QAC3B,IAAI,CAAC,IAAI,CAAC,cAAc;YAAE,OAAO,QAAQ,CAAC;QAC1C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC7D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,wDAAwD;QACxD,MAAM,qBAAqB,GAAG;YAC5B,QAAQ,EAAE,CAAC,EAAI,SAAS;YACxB,IAAI,EAAE,EAAE,EAAO,UAAU;YACzB,MAAM,EAAE,EAAE,EAAK,UAAU;YACzB,GAAG,EAAE,GAAG,EAAO,WAAW;SAC3B,CAAC;QAEF,MAAM,QAAQ,GAAG,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,yBAAyB,GAAG,QAAQ,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,SAAiB;QACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,SAAiB;QAC1B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAmD,EAAE,SAAiB;QACnF,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAiB;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,OAAY,EAAE,SAAiB;QAC3C,IAAI,CAAC,OAAO,GAAG;YACb,GAAG,IAAI,CAAC,OAAO;YACf,GAAG,OAAO;SACX,CAAC;QACF,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,GAAW,EAAE,KAAU;QACxC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,GAAW;QAC5B,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,8BAA8B;QAC9B,MAAM,iBAAiB,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QACvE,KAAK,IAAI,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE7C,yBAAyB;QACzB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,sBAAsB;QACtB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,oBAAoB;QACpB,MAAM,iBAAiB,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC;QAC5D,KAAK,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE5C,gBAAgB;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,yBAAyB,EAAE,IAAI,CAAC,yBAAyB;YACzD,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE;YACpC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE;YAC/B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,MAAM,UAAU,GAAG;YACjB,UAAU,EAAE,UAAU;YACtB,KAAK,EAAE,WAAW;YAClB,WAAW,EAAE,gBAAgB;YAC7B,iBAAiB,EAAE,mBAAmB;YACtC,OAAO,EAAE,YAAY;YACrB,QAAQ,EAAE,cAAc;YACxB,QAAQ,EAAE,cAAc;YACxB,aAAa,EAAE,iBAAiB;YAChC,aAAa,EAAE,kBAAkB;YACjC,UAAU,EAAE,YAAY;YACxB,kBAAkB,EAAE,eAAe;YACnC,cAAc,EAAE,kBAAkB;YAClC,mBAAmB,EAAE,mBAAmB;YACxC,iBAAiB,EAAE,uBAAuB;YAC1C,iBAAiB,EAAE,uBAAuB;YAC1C,aAAa,EAAE,eAAe;YAC9B,OAAO,EAAE,aAAa;YACtB,iBAAiB,EAAE,mBAAmB;YACtC,aAAa,EAAE,cAAc;YAC7B,OAAO,EAAE,aAAa;YACtB,QAAQ,EAAE,cAAc;YACxB,OAAO,EAAE,UAAU;YACnB,UAAU,EAAE,YAAY;YACxB,QAAQ,EAAE,aAAa;YACvB,MAAM,EAAE,QAAQ;SACjB,CAAC;QAEF,OAAO,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC;IACpE,CAAC;CACF,CAAA;AA3gBY,8CAAiB;AAE5B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;6CACpB;AAoCX;IA/BC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,mBAAmB;QACzB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,YAAY;YACZ,OAAO;YACP,aAAa;YACb,mBAAmB;YACnB,SAAS;YACT,UAAU;YACV,UAAU;YACV,eAAe;YACf,eAAe;YACf,YAAY;YACZ,oBAAoB;YACpB,gBAAgB;YAChB,qBAAqB;YACrB,mBAAmB;YACnB,mBAAmB;YACnB,eAAe;YACf,SAAS;YACT,mBAAmB;YACnB,eAAe;YACf,SAAS;YACT,UAAU;YACV,SAAS;YACT,YAAY;YACZ,UAAU;YACV,QAAQ;SACT;KACF,CAAC;;2DACuB;AAMzB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDACpB;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;mDAC3B;AAUlB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;QAC3C,OAAO,EAAE,QAAQ;KAClB,CAAC;;sDACkD;AAUpD;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,gBAAgB,EAAE,eAAe,CAAC;QACzC,OAAO,EAAE,gBAAgB;KAC1B,CAAC;;oDAC4C;AAM9C;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDA4HxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;iDACnD;AAUf;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;QAC/B,OAAO,EAAE,QAAQ;KAClB,CAAC;;qDACoC;AAWtC;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,kBAAkB;QACxB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,cAAc,EAAE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,CAAC;QACrF,OAAO,EAAE,QAAQ;KAClB,CAAC;;0DACsB;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;+CACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;2DAAc;AAMvC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;uDAAC;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;yDAAC;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;oDAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;oDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;oDAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oBAAK,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;IACnF,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC;kDAC3B,oBAAK,oBAAL,oBAAK;sDAAC;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;wDAC5B;AAItB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oBAAK,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;IACnF,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC;kDAC3B,oBAAK,oBAAL,oBAAK;sDAAC;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;wDAC5B;4BA3RX,iBAAiB;IAN7B,IAAA,gBAAM,EAAC,qBAAqB,CAAC;IAC7B,IAAA,eAAK,EAAC,CAAC,eAAe,CAAC,CAAC;IACxB,IAAA,eAAK,EAAC,CAAC,eAAe,CAAC,CAAC;IACxB,IAAA,eAAK,EAAC,CAAC,kBAAkB,CAAC,CAAC;IAC3B,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;GACV,iBAAiB,CA2gB7B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\domain\\entities\\asset-relationship.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { Asset } from './asset.entity';\r\n\r\n/**\r\n * Asset Relationship entity\r\n * Represents relationships and dependencies between assets\r\n */\r\n@Entity('asset_relationships')\r\n@Index(['sourceAssetId'])\r\n@Index(['targetAssetId'])\r\n@Index(['relationshipType'])\r\n@Index(['isActive'])\r\n@Index(['criticality'])\r\nexport class AssetRelationship {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Type of relationship\r\n   */\r\n  @Column({\r\n    name: 'relationship_type',\r\n    type: 'enum',\r\n    enum: [\r\n      'depends_on',\r\n      'hosts',\r\n      'connects_to',\r\n      'communicates_with',\r\n      'manages',\r\n      'monitors',\r\n      'backs_up',\r\n      'replicates_to',\r\n      'load_balances',\r\n      'proxies_to',\r\n      'authenticates_with',\r\n      'stores_data_on',\r\n      'processes_data_from',\r\n      'serves_content_to',\r\n      'routes_traffic_to',\r\n      'clusters_with',\r\n      'mirrors',\r\n      'synchronizes_with',\r\n      'inherits_from',\r\n      'extends',\r\n      'contains',\r\n      'part_of',\r\n      'similar_to',\r\n      'replaces',\r\n      'custom',\r\n    ],\r\n  })\r\n  relationshipType: string;\r\n\r\n  /**\r\n   * Relationship description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Whether relationship is active\r\n   */\r\n  @Column({ name: 'is_active', default: true })\r\n  isActive: boolean;\r\n\r\n  /**\r\n   * Relationship criticality\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high', 'critical'],\r\n    default: 'medium',\r\n  })\r\n  criticality: 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  /**\r\n   * Relationship direction\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['unidirectional', 'bidirectional'],\r\n    default: 'unidirectional',\r\n  })\r\n  direction: 'unidirectional' | 'bidirectional';\r\n\r\n  /**\r\n   * Relationship details and metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  details?: {\r\n    // Connection details\r\n    connection?: {\r\n      protocol?: string;\r\n      port?: number;\r\n      endpoint?: string;\r\n      authentication?: string;\r\n      encryption?: boolean;\r\n      frequency?: 'continuous' | 'real_time' | 'periodic' | 'on_demand';\r\n      bandwidth?: string;\r\n      latency?: number; // milliseconds\r\n    };\r\n    \r\n    // Dependency details\r\n    dependency?: {\r\n      type: 'hard' | 'soft' | 'optional';\r\n      failureImpact: 'none' | 'degraded' | 'partial' | 'complete';\r\n      recoveryTime?: number; // minutes\r\n      alternativeAssets?: string[]; // Asset IDs\r\n      businessImpact?: string;\r\n      slaRequirements?: {\r\n        availability: number; // percentage\r\n        responseTime: number; // milliseconds\r\n        throughput?: number; // requests per second\r\n      };\r\n    };\r\n    \r\n    // Communication details\r\n    communication?: {\r\n      dataFlow: 'inbound' | 'outbound' | 'bidirectional';\r\n      dataTypes?: string[];\r\n      dataVolume?: string;\r\n      dataClassification?: 'public' | 'internal' | 'confidential' | 'restricted';\r\n      compressionUsed?: boolean;\r\n      encryptionMethod?: string;\r\n    };\r\n    \r\n    // Network details\r\n    network?: {\r\n      sourceIp?: string;\r\n      targetIp?: string;\r\n      sourcePort?: number;\r\n      targetPort?: number;\r\n      protocol?: string;\r\n      vlan?: number;\r\n      networkSegment?: string;\r\n      firewallRules?: Array<{\r\n        rule: string;\r\n        action: 'allow' | 'deny';\r\n        direction: 'inbound' | 'outbound';\r\n      }>;\r\n    };\r\n    \r\n    // Service details\r\n    service?: {\r\n      serviceName?: string;\r\n      serviceType?: string;\r\n      version?: string;\r\n      healthCheckEndpoint?: string;\r\n      healthCheckInterval?: number; // seconds\r\n      expectedResponseTime?: number; // milliseconds\r\n      loadBalancingMethod?: 'round_robin' | 'least_connections' | 'ip_hash' | 'weighted';\r\n    };\r\n    \r\n    // Data flow details\r\n    dataFlow?: {\r\n      direction: 'source_to_target' | 'target_to_source' | 'bidirectional';\r\n      dataTypes: string[];\r\n      frequency: 'real_time' | 'batch' | 'scheduled' | 'event_driven';\r\n      schedule?: string; // cron expression\r\n      transformations?: Array<{\r\n        type: string;\r\n        description: string;\r\n      }>;\r\n      validation?: Array<{\r\n        rule: string;\r\n        action: 'accept' | 'reject' | 'transform';\r\n      }>;\r\n    };\r\n    \r\n    // Monitoring details\r\n    monitoring?: {\r\n      enabled: boolean;\r\n      metrics?: string[];\r\n      alertThresholds?: Record<string, number>;\r\n      healthChecks?: Array<{\r\n        type: string;\r\n        endpoint: string;\r\n        interval: number;\r\n        timeout: number;\r\n        expectedResponse?: string;\r\n      }>;\r\n    };\r\n    \r\n    // Compliance details\r\n    compliance?: {\r\n      frameworks?: string[];\r\n      controls?: string[];\r\n      dataResidency?: string;\r\n      auditRequirements?: string[];\r\n      encryptionRequired?: boolean;\r\n      accessLoggingRequired?: boolean;\r\n    };\r\n    \r\n    // Performance details\r\n    performance?: {\r\n      throughput?: {\r\n        current: number;\r\n        maximum: number;\r\n        unit: string;\r\n      };\r\n      latency?: {\r\n        average: number;\r\n        p95: number;\r\n        p99: number;\r\n        unit: 'ms' | 'seconds';\r\n      };\r\n      availability?: {\r\n        target: number; // percentage\r\n        current: number; // percentage\r\n        measurement_period: string;\r\n      };\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Relationship strength/weight\r\n   */\r\n  @Column({ type: 'decimal', precision: 3, scale: 2, default: 1.0 })\r\n  weight: number;\r\n\r\n  /**\r\n   * Relationship confidence level\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high'],\r\n    default: 'medium',\r\n  })\r\n  confidence: 'low' | 'medium' | 'high';\r\n\r\n  /**\r\n   * How relationship was discovered\r\n   */\r\n  @Column({\r\n    name: 'discovery_method',\r\n    type: 'enum',\r\n    enum: ['network_scan', 'agent', 'configuration', 'manual', 'inference', 'monitoring'],\r\n    default: 'manual',\r\n  })\r\n  discoveryMethod: string;\r\n\r\n  /**\r\n   * Relationship tags\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * Custom attributes\r\n   */\r\n  @Column({ name: 'custom_attributes', type: 'jsonb', nullable: true })\r\n  customAttributes?: Record<string, any>;\r\n\r\n  /**\r\n   * When relationship was first discovered\r\n   */\r\n  @Column({ name: 'discovered_at', type: 'timestamp with time zone' })\r\n  discoveredAt: Date;\r\n\r\n  /**\r\n   * When relationship was last verified\r\n   */\r\n  @Column({ name: 'last_verified_at', type: 'timestamp with time zone', nullable: true })\r\n  lastVerifiedAt?: Date;\r\n\r\n  /**\r\n   * User who created the relationship\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  /**\r\n   * User who last updated the relationship\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => Asset, asset => asset.sourceRelationships, { onDelete: 'CASCADE' })\r\n  @JoinColumn({ name: 'source_asset_id' })\r\n  sourceAsset: Asset;\r\n\r\n  @Column({ name: 'source_asset_id', type: 'uuid' })\r\n  sourceAssetId: string;\r\n\r\n  @ManyToOne(() => Asset, asset => asset.targetRelationships, { onDelete: 'CASCADE' })\r\n  @JoinColumn({ name: 'target_asset_id' })\r\n  targetAsset: Asset;\r\n\r\n  @Column({ name: 'target_asset_id', type: 'uuid' })\r\n  targetAssetId: string;\r\n\r\n  /**\r\n   * Check if relationship is critical\r\n   */\r\n  get isCritical(): boolean {\r\n    return this.criticality === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if relationship is bidirectional\r\n   */\r\n  get isBidirectional(): boolean {\r\n    return this.direction === 'bidirectional';\r\n  }\r\n\r\n  /**\r\n   * Check if relationship is a hard dependency\r\n   */\r\n  get isHardDependency(): boolean {\r\n    return this.details?.dependency?.type === 'hard';\r\n  }\r\n\r\n  /**\r\n   * Get relationship age in days\r\n   */\r\n  get ageInDays(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.discoveredAt.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get days since last verification\r\n   */\r\n  get daysSinceLastVerification(): number {\r\n    if (!this.lastVerifiedAt) return Infinity;\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.lastVerifiedAt.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Check if relationship needs verification\r\n   */\r\n  get needsVerification(): boolean {\r\n    // Relationships should be verified based on criticality\r\n    const verificationIntervals = {\r\n      critical: 7,   // 7 days\r\n      high: 30,      // 30 days\r\n      medium: 90,    // 90 days\r\n      low: 180,      // 180 days\r\n    };\r\n    \r\n    const interval = verificationIntervals[this.criticality];\r\n    return this.daysSinceLastVerification > interval;\r\n  }\r\n\r\n  /**\r\n   * Activate relationship\r\n   */\r\n  activate(updatedBy: string): void {\r\n    this.isActive = true;\r\n    this.updatedBy = updatedBy;\r\n  }\r\n\r\n  /**\r\n   * Deactivate relationship\r\n   */\r\n  deactivate(updatedBy: string): void {\r\n    this.isActive = false;\r\n    this.updatedBy = updatedBy;\r\n  }\r\n\r\n  /**\r\n   * Update relationship criticality\r\n   */\r\n  setCriticality(criticality: 'low' | 'medium' | 'high' | 'critical', updatedBy: string): void {\r\n    this.criticality = criticality;\r\n    this.updatedBy = updatedBy;\r\n  }\r\n\r\n  /**\r\n   * Verify relationship\r\n   */\r\n  verify(updatedBy: string): void {\r\n    this.lastVerifiedAt = new Date();\r\n    this.updatedBy = updatedBy;\r\n  }\r\n\r\n  /**\r\n   * Add tag to relationship\r\n   */\r\n  addTag(tag: string): void {\r\n    if (!this.tags.includes(tag)) {\r\n      this.tags.push(tag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove tag from relationship\r\n   */\r\n  removeTag(tag: string): void {\r\n    this.tags = this.tags.filter(t => t !== tag);\r\n  }\r\n\r\n  /**\r\n   * Update relationship details\r\n   */\r\n  updateDetails(updates: any, updatedBy: string): void {\r\n    this.details = {\r\n      ...this.details,\r\n      ...updates,\r\n    };\r\n    this.updatedBy = updatedBy;\r\n  }\r\n\r\n  /**\r\n   * Set custom attribute\r\n   */\r\n  setCustomAttribute(key: string, value: any): void {\r\n    if (!this.customAttributes) {\r\n      this.customAttributes = {};\r\n    }\r\n    this.customAttributes[key] = value;\r\n  }\r\n\r\n  /**\r\n   * Get custom attribute\r\n   */\r\n  getCustomAttribute(key: string): any {\r\n    return this.customAttributes?.[key];\r\n  }\r\n\r\n  /**\r\n   * Calculate relationship risk score\r\n   */\r\n  calculateRiskScore(): number {\r\n    let score = 0;\r\n\r\n    // Base score from criticality\r\n    const criticalityScores = { low: 2, medium: 5, high: 8, critical: 10 };\r\n    score += criticalityScores[this.criticality];\r\n\r\n    // Dependency type factor\r\n    if (this.isHardDependency) {\r\n      score += 2;\r\n    }\r\n\r\n    // Verification status\r\n    if (this.needsVerification) {\r\n      score += 1;\r\n    }\r\n\r\n    // Confidence factor\r\n    const confidenceFactors = { low: 1, medium: 0, high: -0.5 };\r\n    score += confidenceFactors[this.confidence];\r\n\r\n    // Active status\r\n    if (!this.isActive) {\r\n      score += 1;\r\n    }\r\n\r\n    return Math.max(0, Math.min(10, score));\r\n  }\r\n\r\n  /**\r\n   * Get relationship summary\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      relationshipType: this.relationshipType,\r\n      sourceAssetId: this.sourceAssetId,\r\n      targetAssetId: this.targetAssetId,\r\n      direction: this.direction,\r\n      criticality: this.criticality,\r\n      isActive: this.isActive,\r\n      isCritical: this.isCritical,\r\n      isBidirectional: this.isBidirectional,\r\n      isHardDependency: this.isHardDependency,\r\n      needsVerification: this.needsVerification,\r\n      weight: this.weight,\r\n      confidence: this.confidence,\r\n      discoveryMethod: this.discoveryMethod,\r\n      ageInDays: this.ageInDays,\r\n      daysSinceLastVerification: this.daysSinceLastVerification,\r\n      riskScore: this.calculateRiskScore(),\r\n      discoveredAt: this.discoveredAt,\r\n      lastVerifiedAt: this.lastVerifiedAt,\r\n      tags: this.tags,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export relationship for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      relationship: this.getSummary(),\r\n      details: this.details,\r\n      customAttributes: this.customAttributes,\r\n      exportedAt: new Date().toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get reverse relationship type\r\n   */\r\n  getReverseRelationshipType(): string {\r\n    const reverseMap = {\r\n      depends_on: 'supports',\r\n      hosts: 'hosted_by',\r\n      connects_to: 'connected_from',\r\n      communicates_with: 'communicates_with',\r\n      manages: 'managed_by',\r\n      monitors: 'monitored_by',\r\n      backs_up: 'backed_up_by',\r\n      replicates_to: 'replicated_from',\r\n      load_balances: 'load_balanced_by',\r\n      proxies_to: 'proxied_by',\r\n      authenticates_with: 'authenticates',\r\n      stores_data_on: 'stores_data_from',\r\n      processes_data_from: 'processes_data_to',\r\n      serves_content_to: 'receives_content_from',\r\n      routes_traffic_to: 'receives_traffic_from',\r\n      clusters_with: 'clusters_with',\r\n      mirrors: 'mirrored_by',\r\n      synchronizes_with: 'synchronizes_with',\r\n      inherits_from: 'inherited_by',\r\n      extends: 'extended_by',\r\n      contains: 'contained_by',\r\n      part_of: 'contains',\r\n      similar_to: 'similar_to',\r\n      replaces: 'replaced_by',\r\n      custom: 'custom',\r\n    };\r\n\r\n    return reverseMap[this.relationshipType] || this.relationshipType;\r\n  }\r\n}\r\n"], "version": 3}