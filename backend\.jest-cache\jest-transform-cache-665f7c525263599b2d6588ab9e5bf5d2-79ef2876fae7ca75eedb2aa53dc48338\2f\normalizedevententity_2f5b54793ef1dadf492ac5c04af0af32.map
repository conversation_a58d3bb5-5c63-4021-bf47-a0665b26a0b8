{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\normalized-event.entity.ts", "mappings": ";;;AAAA,6DAA8E;AAG9E,sEAA6D;AAG7D,2GAAoG;AACpG,yHAAiH;AACjH,+HAAuH;AAEvH;;GAEG;AACH,IAAY,mBAMX;AAND,WAAY,mBAAmB;IAC7B,0CAAmB,CAAA;IACnB,kDAA2B,CAAA;IAC3B,8CAAuB,CAAA;IACvB,wCAAiB,CAAA;IACjB,0CAAmB,CAAA;AACrB,CAAC,EANW,mBAAmB,mCAAnB,mBAAmB,QAM9B;AA0GD;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAa,eAAgB,SAAQ,iCAAuC;IAM1E,YAAY,KAA2B,EAAE,EAAmB;QAC1D,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACjB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,KAA2B,EAAE,EAAmB;QAC5D,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAEvD,iDAAiD;QACjD,eAAe,CAAC,cAAc,CAAC,IAAI,yEAAiC,CAClE,eAAe,CAAC,EAAE,EAClB;YACE,eAAe,EAAE,KAAK,CAAC,eAAe;YACtC,SAAS,EAAE,KAAK,CAAC,IAAI;YACrB,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,mBAAmB,EAAE,KAAK,CAAC,mBAAmB;YAC9C,aAAa,EAAE,KAAK,CAAC,aAAa;YAClC,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;YACxC,iBAAiB,EAAE,KAAK,CAAC,YAAY,CAAC,MAAM;YAC5C,oBAAoB,EAAE,KAAK,CAAC,oBAAoB,IAAI,KAAK;SAC1D,CACF,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC;IAES,kBAAkB;QAC1B,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,SAAS;YACzC,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,GAAG,CAAC,EAAE,CAAC;YAC3E,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,SAAS;YAClC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,KAAK,SAAS;YACxC,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,qBAAqB,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,CAAC,EAAE,CAAC;YAC3F,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC3B,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,GAAG,eAAe,CAAC,qBAAqB,EAAE,CAAC;YAC/E,MAAM,IAAI,KAAK,CAAC,yBAAyB,eAAe,CAAC,qBAAqB,oBAAoB,CAAC,CAAC;QACtG,CAAC;QAED,4CAA4C;QAC5C,IAAI,CAAC,sCAAsC,EAAE,CAAC;IAChD,CAAC;IAEO,sCAAsC;QAC5C,qEAAqE;QACrE,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK,mBAAmB,CAAC,SAAS,EAAE,CAAC;YACrE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC;gBACzC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;YAC5E,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;gBACpC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAED,4DAA4D;QAC5D,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK,mBAAmB,CAAC,MAAM,EAAE,CAAC;YAClE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB;gBAClC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC5F,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAED,oEAAoE;QACpE,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACvE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAC7D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;IACH,CAAC;IAED,UAAU;IACV,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IACpC,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;IACxC,CAAC;IAED,IAAI,YAAY;QACd,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;IACxC,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;IAC1C,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACrD,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IACpC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACnE,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,YAAY;QACd,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IACtC,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAC5F,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC;IAC3C,CAAC;IAED,IAAI,wBAAwB;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC;IAC7C,CAAC;IAED,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC;IAC3C,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7E,CAAC;IAED,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,IAAI,KAAK,CAAC;IAClD,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,mBAAmB;IAEnB;;OAEG;IACH,kBAAkB;QAChB,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK,mBAAmB,CAAC,OAAO,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,WAAW,CAAC;QACjE,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,IAAI,IAAI,EAAE,CAAC;QAC/C,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAE/E,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,MAA2B;QAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC;QAC/D,IAAI,CAAC,KAAK,CAAC,wBAAwB,GAAG,IAAI,IAAI,EAAE,CAAC;QACjD,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,MAAM,CAAC;QACxC,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,SAAS,CAAC;QAE9C,+CAA+C;QAC/C,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QAEvC,yCAAyC;QACzC,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAExC,IAAI,CAAC,cAAc,CAAC,IAAI,sFAAuC,CAC7D,IAAI,CAAC,EAAE,EACP;YACE,SAAS,EAAE,mBAAmB,CAAC,WAAW;YAC1C,SAAS,EAAE,mBAAmB,CAAC,SAAS;YACxC,MAAM;YACN,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,IAAI,KAAK;SAC/D,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,KAAa,EAAE,MAAqC;QACpE,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,MAAM,CAAC;QAC5D,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;QAE1C,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG;gBAC/B,OAAO,EAAE,KAAK;gBACd,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE;gBACvC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,EAAE;gBACrC,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE;gBAC/B,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC;gBAChC,oBAAoB,EAAE,MAAM,CAAC,oBAAoB,IAAI,CAAC;gBACtD,eAAe,EAAE,MAAM,CAAC,eAAe,IAAI,CAAC;aAC7C,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,4FAA0C,CAChE,IAAI,CAAC,EAAE,EACP;YACE,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;YAC3C,KAAK;YACL,OAAO,EAAE,IAAI,CAAC,qBAAqB;YACnC,mBAAmB,EAAE,IAAI,CAAC,mCAAmC,EAAE;SAChE,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAc;QAC9B,IAAI,CAAC,CAAC,mBAAmB,CAAC,OAAO,EAAE,mBAAmB,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACxG,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,OAAO,CAAC;QAC7D,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,SAAS,CAAC;QAC9C,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;QAEhC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,IAAI,CAAC,mCAAmC,EAAE,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,OAAO,CAAC;QAC7D,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,SAAS,CAAC;QAC9C,IAAI,CAAC,KAAK,CAAC,wBAAwB,GAAG,SAAS,CAAC;QAChD,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,SAAS,CAAC;QAC9C,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,SAAS,CAAC;QAE3C,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,cAAmC;QACtD,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK,mBAAmB,CAAC,SAAS,EAAE,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,EAAE,GAAG,cAAc,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAuB;QACpC,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;QACzE,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,KAAa;QAClC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC;QACpC,IAAI,CAAC,gCAAgC,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,MAAgB;QAClC,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,EAAE,CAAC;QACxD,MAAM,SAAS,GAAG,CAAC,GAAG,aAAa,EAAE,GAAG,MAAM,CAAC,CAAC;QAEhD,IAAI,SAAS,CAAC,MAAM,GAAG,eAAe,CAAC,qBAAqB,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,yBAAyB,eAAe,CAAC,qBAAqB,oBAAoB,CAAC,CAAC;QACtG,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,SAAS,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,MAAc;QAChC,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,UAAkB,EAAE,KAAc;QACrD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QACnC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;QACjC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,SAAiB;QAC/B,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;QACjC,IAAI,CAAC,gCAAgC,EAAE,CAAC;IAC1C,CAAC;IAED,gBAAgB;IAEhB;;OAEG;IACH,wBAAwB;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK,mBAAmB,CAAC,SAAS,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK,mBAAmB,CAAC,MAAM,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK,mBAAmB,CAAC,WAAW,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK,mBAAmB,CAAC,OAAO,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,CAAC,CAAC,IAAI,eAAe,CAAC,sBAAsB,CAAC;IACtF,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,mCAAmC;QACjC,OAAO,IAAI,CAAC,qBAAqB,IAAI,eAAe,CAAC,0BAA0B,CAAC;IAClF,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,kBAAkB,EAAE;YACzB,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,CAAC,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,eAAe,CAAC,0BAA0B,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,IAAI,IAAI,IAAI,EAAE,CAAC;QAClE,OAAO,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,OAAO,EAAE,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAc;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;IAClE,CAAC;IAED,yBAAyB;IAEjB,yBAAyB,CAAC,MAA2B;QAC3D,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,gCAAgC;QAChC,MAAM,kBAAkB,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE,CAAC;QAC1D,KAAK,IAAI,kBAAkB,CAAC;QAE5B,4BAA4B;QAC5B,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACnD,KAAK,IAAI,eAAe,CAAC;QAEzB,0BAA0B;QAC1B,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;QAChD,KAAK,IAAI,aAAa,CAAC;QAEvB,kCAAkC;QAClC,IAAI,MAAM,CAAC,eAAe,GAAG,EAAE,EAAE,CAAC;YAChC,KAAK,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;QACzC,CAAC;QAED,qCAAqC;QACrC,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAClE,CAAC;IAEO,gCAAgC;QACtC,yCAAyC;QACzC,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACvC,OAAO;QACT,CAAC;QAED,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACvC,OAAO;QACT,CAAC;QAED,sDAAsD;QACtD,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACvC,OAAO;QACT,CAAC;QAED,wCAAwC;QACxC,IAAI,IAAI,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,EAAE,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACvC,OAAO;QACT,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,KAAK,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,UAAU;QAgBR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YACtB,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,EAAE;YACtD,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;YACnD,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;YAC/B,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;YACvC,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM;YACjD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC/C,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;SAChD,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,EAAE;YACtD,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE;YACtC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;YACnD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;YACzC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;YAC/B,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;YAC3C,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;YACvC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,QAAQ,EAAE;YACnD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;YACnD,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;YACvC,sBAAsB,EAAE,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,WAAW,EAAE;YACxE,wBAAwB,EAAE,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE,WAAW,EAAE;YAC5E,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB;YACvD,sBAAsB,EAAE,IAAI,CAAC,KAAK,CAAC,sBAAsB;YACzD,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;YACrD,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,EAAE;YAChD,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;SAC3B,CAAC;IACJ,CAAC;;AA5rBH,0CA6rBC;AA5rByB,0CAA0B,GAAG,CAAC,CAAC;AAC/B,sCAAsB,GAAG,EAAE,CAAC;AAC5B,0CAA0B,GAAG,EAAE,CAAC;AAChC,qCAAqB,GAAG,EAAE,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\normalized-event.entity.ts"], "sourcesContent": ["import { BaseAggregateRoot, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventMetadata } from '../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../enums/event-processing-status.enum';\r\nimport { NormalizedEventCreatedDomainEvent } from '../events/normalized-event-created.domain-event';\r\nimport { NormalizedEventStatusChangedDomainEvent } from '../events/normalized-event-status-changed.domain-event';\r\nimport { NormalizedEventValidationFailedDomainEvent } from '../events/normalized-event-validation-failed.domain-event';\r\n\r\n/**\r\n * Normalization Status Enum\r\n */\r\nexport enum NormalizationStatus {\r\n  PENDING = 'PENDING',\r\n  IN_PROGRESS = 'IN_PROGRESS',\r\n  COMPLETED = 'COMPLETED',\r\n  FAILED = 'FAILED',\r\n  SKIPPED = 'SKIPPED',\r\n}\r\n\r\n/**\r\n * Normalization Rule Interface\r\n */\r\nexport interface NormalizationRule {\r\n  /** Rule identifier */\r\n  id: string;\r\n  /** Rule name */\r\n  name: string;\r\n  /** Rule description */\r\n  description: string;\r\n  /** Rule priority (higher number = higher priority) */\r\n  priority: number;\r\n  /** Whether the rule is required for successful normalization */\r\n  required: boolean;\r\n  /** Rule configuration */\r\n  config?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Normalization Result Interface\r\n */\r\nexport interface NormalizationResult {\r\n  /** Whether normalization was successful */\r\n  success: boolean;\r\n  /** Applied normalization rules */\r\n  appliedRules: string[];\r\n  /** Failed normalization rules */\r\n  failedRules: string[];\r\n  /** Normalization warnings */\r\n  warnings: string[];\r\n  /** Normalization errors */\r\n  errors: string[];\r\n  /** Processing duration in milliseconds */\r\n  processingDurationMs: number;\r\n  /** Confidence score of normalization (0-100) */\r\n  confidenceScore: number;\r\n}\r\n\r\n/**\r\n * NormalizedEvent Entity Properties\r\n */\r\nexport interface NormalizedEventProps {\r\n  /** Original event ID that was normalized */\r\n  originalEventId: UniqueEntityId;\r\n  /** Event metadata containing timestamp, source, and processing information */\r\n  metadata: EventMetadata;\r\n  /** Type of the security event */\r\n  type: EventType;\r\n  /** Severity level of the event */\r\n  severity: EventSeverity;\r\n  /** Current status of the event */\r\n  status: EventStatus;\r\n  /** Current processing status in the pipeline */\r\n  processingStatus: EventProcessingStatus;\r\n  /** Current normalization status */\r\n  normalizationStatus: NormalizationStatus;\r\n  /** Original raw event data */\r\n  originalData: Record<string, any>;\r\n  /** Normalized event data in standard format */\r\n  normalizedData: Record<string, any>;\r\n  /** Event title/summary */\r\n  title: string;\r\n  /** Detailed description of the event */\r\n  description?: string;\r\n  /** Tags for categorization and filtering */\r\n  tags?: string[];\r\n  /** Risk score (0-100) */\r\n  riskScore?: number;\r\n  /** Confidence level (0-100) */\r\n  confidenceLevel?: number;\r\n  /** Additional custom attributes */\r\n  attributes?: Record<string, any>;\r\n  /** Event correlation ID for grouping related events */\r\n  correlationId?: string;\r\n  /** Parent event ID if this is a child event */\r\n  parentEventId?: UniqueEntityId;\r\n  /** Applied normalization rules */\r\n  appliedRules: NormalizationRule[];\r\n  /** Normalization result details */\r\n  normalizationResult?: NormalizationResult;\r\n  /** Schema version used for normalization */\r\n  schemaVersion: string;\r\n  /** When normalization started */\r\n  normalizationStartedAt?: Date;\r\n  /** When normalization completed */\r\n  normalizationCompletedAt?: Date;\r\n  /** Normalization processing attempts */\r\n  normalizationAttempts?: number;\r\n  /** Last normalization error */\r\n  lastNormalizationError?: string;\r\n  /** Data quality score (0-100) */\r\n  dataQualityScore?: number;\r\n  /** Validation errors found during normalization */\r\n  validationErrors?: string[];\r\n  /** Whether the normalized event requires manual review */\r\n  requiresManualReview?: boolean;\r\n  /** Manual review notes */\r\n  reviewNotes?: string;\r\n  /** Who reviewed the event */\r\n  reviewedBy?: string;\r\n  /** When the event was reviewed */\r\n  reviewedAt?: Date;\r\n}\r\n\r\n/**\r\n * NormalizedEvent Entity\r\n * \r\n * Represents a security event that has been processed through the normalization pipeline.\r\n * Normalized events have standardized data formats and validated content.\r\n * \r\n * Key responsibilities:\r\n * - Maintain normalized event state and lifecycle\r\n * - Enforce normalization business rules and data quality standards\r\n * - Track normalization process and applied rules\r\n * - Generate domain events for normalization state changes\r\n * - Support data quality assessment and validation\r\n * - Manage manual review workflow for complex events\r\n * \r\n * Business Rules:\r\n * - Normalized events must reference a valid original event\r\n * - Normalized data must conform to the specified schema version\r\n * - Data quality score must be calculated based on validation results\r\n * - High-risk events may require manual review before processing\r\n * - Normalization attempts are tracked and limited\r\n * - Failed normalization must preserve original data integrity\r\n */\r\nexport class NormalizedEvent extends BaseAggregateRoot<NormalizedEventProps> {\r\n  private static readonly MAX_NORMALIZATION_ATTEMPTS = 3;\r\n  private static readonly MIN_DATA_QUALITY_SCORE = 60;\r\n  private static readonly HIGH_RISK_REVIEW_THRESHOLD = 80;\r\n  private static readonly MAX_VALIDATION_ERRORS = 10;\r\n\r\n  constructor(props: NormalizedEventProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Create a new NormalizedEvent\r\n   */\r\n  static create(props: NormalizedEventProps, id?: UniqueEntityId): NormalizedEvent {\r\n    const normalizedEvent = new NormalizedEvent(props, id);\r\n    \r\n    // Add domain event for normalized event creation\r\n    normalizedEvent.addDomainEvent(new NormalizedEventCreatedDomainEvent(\r\n      normalizedEvent.id,\r\n      {\r\n        originalEventId: props.originalEventId,\r\n        eventType: props.type,\r\n        severity: props.severity,\r\n        normalizationStatus: props.normalizationStatus,\r\n        schemaVersion: props.schemaVersion,\r\n        dataQualityScore: props.dataQualityScore,\r\n        appliedRulesCount: props.appliedRules.length,\r\n        requiresManualReview: props.requiresManualReview || false,\r\n      }\r\n    ));\r\n\r\n    return normalizedEvent;\r\n  }\r\n\r\n  protected validateInvariants(): void {\r\n    super.validateInvariants();\r\n\r\n    if (!this.props.originalEventId) {\r\n      throw new Error('NormalizedEvent must reference an original event');\r\n    }\r\n\r\n    if (!this.props.metadata) {\r\n      throw new Error('NormalizedEvent must have metadata');\r\n    }\r\n\r\n    if (!this.props.type) {\r\n      throw new Error('NormalizedEvent must have a type');\r\n    }\r\n\r\n    if (!this.props.severity) {\r\n      throw new Error('NormalizedEvent must have a severity');\r\n    }\r\n\r\n    if (!this.props.status) {\r\n      throw new Error('NormalizedEvent must have a status');\r\n    }\r\n\r\n    if (!this.props.processingStatus) {\r\n      throw new Error('NormalizedEvent must have a processing status');\r\n    }\r\n\r\n    if (!this.props.normalizationStatus) {\r\n      throw new Error('NormalizedEvent must have a normalization status');\r\n    }\r\n\r\n    if (!this.props.originalData) {\r\n      throw new Error('NormalizedEvent must have original data');\r\n    }\r\n\r\n    if (!this.props.normalizedData) {\r\n      throw new Error('NormalizedEvent must have normalized data');\r\n    }\r\n\r\n    if (!this.props.title || this.props.title.trim().length === 0) {\r\n      throw new Error('NormalizedEvent must have a non-empty title');\r\n    }\r\n\r\n    if (!this.props.schemaVersion || this.props.schemaVersion.trim().length === 0) {\r\n      throw new Error('NormalizedEvent must have a schema version');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.appliedRules)) {\r\n      throw new Error('NormalizedEvent must have applied rules array');\r\n    }\r\n\r\n    if (this.props.dataQualityScore !== undefined && \r\n        (this.props.dataQualityScore < 0 || this.props.dataQualityScore > 100)) {\r\n      throw new Error('Data quality score must be between 0 and 100');\r\n    }\r\n\r\n    if (this.props.riskScore !== undefined && \r\n        (this.props.riskScore < 0 || this.props.riskScore > 100)) {\r\n      throw new Error('Risk score must be between 0 and 100');\r\n    }\r\n\r\n    if (this.props.confidenceLevel !== undefined && \r\n        (this.props.confidenceLevel < 0 || this.props.confidenceLevel > 100)) {\r\n      throw new Error('Confidence level must be between 0 and 100');\r\n    }\r\n\r\n    if (this.props.normalizationAttempts !== undefined && this.props.normalizationAttempts < 0) {\r\n      throw new Error('Normalization attempts cannot be negative');\r\n    }\r\n\r\n    if (this.props.validationErrors && \r\n        this.props.validationErrors.length > NormalizedEvent.MAX_VALIDATION_ERRORS) {\r\n      throw new Error(`Cannot have more than ${NormalizedEvent.MAX_VALIDATION_ERRORS} validation errors`);\r\n    }\r\n\r\n    // Validate normalization status consistency\r\n    this.validateNormalizationStatusConsistency();\r\n  }\r\n\r\n  private validateNormalizationStatusConsistency(): void {\r\n    // If normalization is completed, it should have completion timestamp\r\n    if (this.props.normalizationStatus === NormalizationStatus.COMPLETED) {\r\n      if (!this.props.normalizationCompletedAt) {\r\n        throw new Error('Completed normalization must have completion timestamp');\r\n      }\r\n      if (!this.props.normalizationResult) {\r\n        throw new Error('Completed normalization must have result');\r\n      }\r\n    }\r\n\r\n    // If normalization failed, it should have error information\r\n    if (this.props.normalizationStatus === NormalizationStatus.FAILED) {\r\n      if (!this.props.lastNormalizationError && \r\n          (!this.props.normalizationResult || this.props.normalizationResult.errors.length === 0)) {\r\n        throw new Error('Failed normalization must have error information');\r\n      }\r\n    }\r\n\r\n    // If normalization is in progress, it should have started timestamp\r\n    if (this.props.normalizationStatus === NormalizationStatus.IN_PROGRESS) {\r\n      if (!this.props.normalizationStartedAt) {\r\n        throw new Error('In-progress normalization must have start timestamp');\r\n      }\r\n    }\r\n\r\n    // Manual review consistency\r\n    if (this.props.requiresManualReview && this.props.reviewedAt) {\r\n      if (!this.props.reviewedBy) {\r\n        throw new Error('Reviewed events must have reviewer information');\r\n      }\r\n    }\r\n  }\r\n\r\n  // Getters\r\n  get originalEventId(): UniqueEntityId {\r\n    return this.props.originalEventId;\r\n  }\r\n\r\n  get metadata(): EventMetadata {\r\n    return this.props.metadata;\r\n  }\r\n\r\n  get type(): EventType {\r\n    return this.props.type;\r\n  }\r\n\r\n  get severity(): EventSeverity {\r\n    return this.props.severity;\r\n  }\r\n\r\n  get status(): EventStatus {\r\n    return this.props.status;\r\n  }\r\n\r\n  get processingStatus(): EventProcessingStatus {\r\n    return this.props.processingStatus;\r\n  }\r\n\r\n  get normalizationStatus(): NormalizationStatus {\r\n    return this.props.normalizationStatus;\r\n  }\r\n\r\n  get originalData(): Record<string, any> {\r\n    return { ...this.props.originalData };\r\n  }\r\n\r\n  get normalizedData(): Record<string, any> {\r\n    return { ...this.props.normalizedData };\r\n  }\r\n\r\n  get title(): string {\r\n    return this.props.title;\r\n  }\r\n\r\n  get description(): string | undefined {\r\n    return this.props.description;\r\n  }\r\n\r\n  get tags(): string[] {\r\n    return this.props.tags ? [...this.props.tags] : [];\r\n  }\r\n\r\n  get riskScore(): number | undefined {\r\n    return this.props.riskScore;\r\n  }\r\n\r\n  get confidenceLevel(): number | undefined {\r\n    return this.props.confidenceLevel;\r\n  }\r\n\r\n  get attributes(): Record<string, any> {\r\n    return this.props.attributes ? { ...this.props.attributes } : {};\r\n  }\r\n\r\n  get correlationId(): string | undefined {\r\n    return this.props.correlationId;\r\n  }\r\n\r\n  get parentEventId(): UniqueEntityId | undefined {\r\n    return this.props.parentEventId;\r\n  }\r\n\r\n  get appliedRules(): NormalizationRule[] {\r\n    return [...this.props.appliedRules];\r\n  }\r\n\r\n  get normalizationResult(): NormalizationResult | undefined {\r\n    return this.props.normalizationResult ? { ...this.props.normalizationResult } : undefined;\r\n  }\r\n\r\n  get schemaVersion(): string {\r\n    return this.props.schemaVersion;\r\n  }\r\n\r\n  get normalizationStartedAt(): Date | undefined {\r\n    return this.props.normalizationStartedAt;\r\n  }\r\n\r\n  get normalizationCompletedAt(): Date | undefined {\r\n    return this.props.normalizationCompletedAt;\r\n  }\r\n\r\n  get normalizationAttempts(): number {\r\n    return this.props.normalizationAttempts || 0;\r\n  }\r\n\r\n  get lastNormalizationError(): string | undefined {\r\n    return this.props.lastNormalizationError;\r\n  }\r\n\r\n  get dataQualityScore(): number | undefined {\r\n    return this.props.dataQualityScore;\r\n  }\r\n\r\n  get validationErrors(): string[] {\r\n    return this.props.validationErrors ? [...this.props.validationErrors] : [];\r\n  }\r\n\r\n  get requiresManualReview(): boolean {\r\n    return this.props.requiresManualReview || false;\r\n  }\r\n\r\n  get reviewNotes(): string | undefined {\r\n    return this.props.reviewNotes;\r\n  }\r\n\r\n  get reviewedBy(): string | undefined {\r\n    return this.props.reviewedBy;\r\n  }\r\n\r\n  get reviewedAt(): Date | undefined {\r\n    return this.props.reviewedAt;\r\n  }\r\n\r\n  // Business methods\r\n\r\n  /**\r\n   * Start normalization process\r\n   */\r\n  startNormalization(): void {\r\n    if (this.props.normalizationStatus !== NormalizationStatus.PENDING) {\r\n      throw new Error('Can only start normalization for pending events');\r\n    }\r\n\r\n    this.props.normalizationStatus = NormalizationStatus.IN_PROGRESS;\r\n    this.props.normalizationStartedAt = new Date();\r\n    this.props.normalizationAttempts = (this.props.normalizationAttempts || 0) + 1;\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Complete normalization process\r\n   */\r\n  completeNormalization(result: NormalizationResult): void {\r\n    if (this.props.normalizationStatus !== NormalizationStatus.IN_PROGRESS) {\r\n      throw new Error('Can only complete normalization for in-progress events');\r\n    }\r\n\r\n    this.props.normalizationStatus = NormalizationStatus.COMPLETED;\r\n    this.props.normalizationCompletedAt = new Date();\r\n    this.props.normalizationResult = result;\r\n    this.props.lastNormalizationError = undefined;\r\n\r\n    // Calculate data quality score based on result\r\n    this.calculateDataQualityScore(result);\r\n\r\n    // Determine if manual review is required\r\n    this.determineManualReviewRequirement();\r\n\r\n    this.addDomainEvent(new NormalizedEventStatusChangedDomainEvent(\r\n      this.id,\r\n      {\r\n        oldStatus: NormalizationStatus.IN_PROGRESS,\r\n        newStatus: NormalizationStatus.COMPLETED,\r\n        result,\r\n        dataQualityScore: this.props.dataQualityScore,\r\n        requiresManualReview: this.props.requiresManualReview || false,\r\n      }\r\n    ));\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Fail normalization process\r\n   */\r\n  failNormalization(error: string, result?: Partial<NormalizationResult>): void {\r\n    if (this.props.normalizationStatus !== NormalizationStatus.IN_PROGRESS) {\r\n      throw new Error('Can only fail normalization for in-progress events');\r\n    }\r\n\r\n    this.props.normalizationStatus = NormalizationStatus.FAILED;\r\n    this.props.lastNormalizationError = error;\r\n    \r\n    if (result) {\r\n      this.props.normalizationResult = {\r\n        success: false,\r\n        appliedRules: result.appliedRules || [],\r\n        failedRules: result.failedRules || [],\r\n        warnings: result.warnings || [],\r\n        errors: result.errors || [error],\r\n        processingDurationMs: result.processingDurationMs || 0,\r\n        confidenceScore: result.confidenceScore || 0,\r\n      };\r\n    }\r\n\r\n    this.addDomainEvent(new NormalizedEventValidationFailedDomainEvent(\r\n      this.id,\r\n      {\r\n        originalEventId: this.props.originalEventId,\r\n        error,\r\n        attempt: this.normalizationAttempts,\r\n        maxAttemptsExceeded: this.hasExceededMaxNormalizationAttempts(),\r\n      }\r\n    ));\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Skip normalization process\r\n   */\r\n  skipNormalization(reason: string): void {\r\n    if (![NormalizationStatus.PENDING, NormalizationStatus.FAILED].includes(this.props.normalizationStatus)) {\r\n      throw new Error('Can only skip normalization for pending or failed events');\r\n    }\r\n\r\n    this.props.normalizationStatus = NormalizationStatus.SKIPPED;\r\n    this.props.lastNormalizationError = undefined;\r\n    this.props.reviewNotes = reason;\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Reset normalization for retry\r\n   */\r\n  resetNormalization(): void {\r\n    if (this.hasExceededMaxNormalizationAttempts()) {\r\n      throw new Error('Cannot reset normalization: maximum attempts exceeded');\r\n    }\r\n\r\n    this.props.normalizationStatus = NormalizationStatus.PENDING;\r\n    this.props.normalizationStartedAt = undefined;\r\n    this.props.normalizationCompletedAt = undefined;\r\n    this.props.lastNormalizationError = undefined;\r\n    this.props.normalizationResult = undefined;\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Update normalized data\r\n   */\r\n  updateNormalizedData(normalizedData: Record<string, any>): void {\r\n    if (this.props.normalizationStatus === NormalizationStatus.COMPLETED) {\r\n      throw new Error('Cannot update normalized data for completed normalization');\r\n    }\r\n\r\n    this.props.normalizedData = { ...normalizedData };\r\n  }\r\n\r\n  /**\r\n   * Add applied normalization rule\r\n   */\r\n  addAppliedRule(rule: NormalizationRule): void {\r\n    const existingRule = this.props.appliedRules.find(r => r.id === rule.id);\r\n    if (!existingRule) {\r\n      this.props.appliedRules.push(rule);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update data quality score\r\n   */\r\n  updateDataQualityScore(score: number): void {\r\n    if (score < 0 || score > 100) {\r\n      throw new Error('Data quality score must be between 0 and 100');\r\n    }\r\n\r\n    this.props.dataQualityScore = score;\r\n    this.determineManualReviewRequirement();\r\n  }\r\n\r\n  /**\r\n   * Add validation errors\r\n   */\r\n  addValidationErrors(errors: string[]): void {\r\n    const currentErrors = this.props.validationErrors || [];\r\n    const newErrors = [...currentErrors, ...errors];\r\n\r\n    if (newErrors.length > NormalizedEvent.MAX_VALIDATION_ERRORS) {\r\n      throw new Error(`Cannot have more than ${NormalizedEvent.MAX_VALIDATION_ERRORS} validation errors`);\r\n    }\r\n\r\n    this.props.validationErrors = newErrors;\r\n  }\r\n\r\n  /**\r\n   * Clear validation errors\r\n   */\r\n  clearValidationErrors(): void {\r\n    this.props.validationErrors = [];\r\n  }\r\n\r\n  /**\r\n   * Mark for manual review\r\n   */\r\n  markForManualReview(reason: string): void {\r\n    this.props.requiresManualReview = true;\r\n    this.props.reviewNotes = reason;\r\n  }\r\n\r\n  /**\r\n   * Complete manual review\r\n   */\r\n  completeManualReview(reviewedBy: string, notes?: string): void {\r\n    if (!this.props.requiresManualReview) {\r\n      throw new Error('Event is not marked for manual review');\r\n    }\r\n\r\n    this.props.reviewedBy = reviewedBy;\r\n    this.props.reviewedAt = new Date();\r\n    if (notes) {\r\n      this.props.reviewNotes = notes;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update risk score\r\n   */\r\n  updateRiskScore(riskScore: number): void {\r\n    if (riskScore < 0 || riskScore > 100) {\r\n      throw new Error('Risk score must be between 0 and 100');\r\n    }\r\n\r\n    this.props.riskScore = riskScore;\r\n    this.determineManualReviewRequirement();\r\n  }\r\n\r\n  // Query methods\r\n\r\n  /**\r\n   * Check if normalization is completed\r\n   */\r\n  isNormalizationCompleted(): boolean {\r\n    return this.props.normalizationStatus === NormalizationStatus.COMPLETED;\r\n  }\r\n\r\n  /**\r\n   * Check if normalization failed\r\n   */\r\n  isNormalizationFailed(): boolean {\r\n    return this.props.normalizationStatus === NormalizationStatus.FAILED;\r\n  }\r\n\r\n  /**\r\n   * Check if normalization is in progress\r\n   */\r\n  isNormalizationInProgress(): boolean {\r\n    return this.props.normalizationStatus === NormalizationStatus.IN_PROGRESS;\r\n  }\r\n\r\n  /**\r\n   * Check if normalization was skipped\r\n   */\r\n  isNormalizationSkipped(): boolean {\r\n    return this.props.normalizationStatus === NormalizationStatus.SKIPPED;\r\n  }\r\n\r\n  /**\r\n   * Check if event has high data quality\r\n   */\r\n  hasHighDataQuality(): boolean {\r\n    return (this.props.dataQualityScore || 0) >= NormalizedEvent.MIN_DATA_QUALITY_SCORE;\r\n  }\r\n\r\n  /**\r\n   * Check if event has validation errors\r\n   */\r\n  hasValidationErrors(): boolean {\r\n    return (this.props.validationErrors?.length || 0) > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if event has exceeded max normalization attempts\r\n   */\r\n  hasExceededMaxNormalizationAttempts(): boolean {\r\n    return this.normalizationAttempts >= NormalizedEvent.MAX_NORMALIZATION_ATTEMPTS;\r\n  }\r\n\r\n  /**\r\n   * Check if event is ready for next processing stage\r\n   */\r\n  isReadyForNextStage(): boolean {\r\n    return this.isNormalizationCompleted() && \r\n           this.hasHighDataQuality() && \r\n           !this.hasValidationErrors() &&\r\n           (!this.requiresManualReview || this.reviewedAt !== undefined);\r\n  }\r\n\r\n  /**\r\n   * Check if event is high risk\r\n   */\r\n  isHighRisk(): boolean {\r\n    return (this.props.riskScore || 0) >= NormalizedEvent.HIGH_RISK_REVIEW_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Get normalization duration\r\n   */\r\n  getNormalizationDuration(): number | null {\r\n    if (!this.props.normalizationStartedAt) {\r\n      return null;\r\n    }\r\n\r\n    const endTime = this.props.normalizationCompletedAt || new Date();\r\n    return endTime.getTime() - this.props.normalizationStartedAt.getTime();\r\n  }\r\n\r\n  /**\r\n   * Get applied rule names\r\n   */\r\n  getAppliedRuleNames(): string[] {\r\n    return this.props.appliedRules.map(rule => rule.name);\r\n  }\r\n\r\n  /**\r\n   * Check if specific rule was applied\r\n   */\r\n  hasAppliedRule(ruleId: string): boolean {\r\n    return this.props.appliedRules.some(rule => rule.id === ruleId);\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private calculateDataQualityScore(result: NormalizationResult): void {\r\n    let score = 100;\r\n\r\n    // Reduce score for failed rules\r\n    const failedRulesPenalty = result.failedRules.length * 10;\r\n    score -= failedRulesPenalty;\r\n\r\n    // Reduce score for warnings\r\n    const warningsPenalty = result.warnings.length * 5;\r\n    score -= warningsPenalty;\r\n\r\n    // Reduce score for errors\r\n    const errorsPenalty = result.errors.length * 15;\r\n    score -= errorsPenalty;\r\n\r\n    // Reduce score for low confidence\r\n    if (result.confidenceScore < 70) {\r\n      score -= (70 - result.confidenceScore);\r\n    }\r\n\r\n    // Ensure score is within valid range\r\n    this.props.dataQualityScore = Math.max(0, Math.min(100, score));\r\n  }\r\n\r\n  private determineManualReviewRequirement(): void {\r\n    // High-risk events require manual review\r\n    if (this.isHighRisk()) {\r\n      this.props.requiresManualReview = true;\r\n      return;\r\n    }\r\n\r\n    // Low data quality requires manual review\r\n    if (!this.hasHighDataQuality()) {\r\n      this.props.requiresManualReview = true;\r\n      return;\r\n    }\r\n\r\n    // Events with validation errors require manual review\r\n    if (this.hasValidationErrors()) {\r\n      this.props.requiresManualReview = true;\r\n      return;\r\n    }\r\n\r\n    // Critical events require manual review\r\n    if (this.severity === EventSeverity.CRITICAL) {\r\n      this.props.requiresManualReview = true;\r\n      return;\r\n    }\r\n\r\n    // Otherwise, no manual review required\r\n    this.props.requiresManualReview = false;\r\n  }\r\n\r\n  /**\r\n   * Get event summary for display\r\n   */\r\n  getSummary(): {\r\n    id: string;\r\n    originalEventId: string;\r\n    title: string;\r\n    type: EventType;\r\n    severity: EventSeverity;\r\n    status: EventStatus;\r\n    normalizationStatus: NormalizationStatus;\r\n    dataQualityScore?: number;\r\n    riskScore?: number;\r\n    schemaVersion: string;\r\n    appliedRulesCount: number;\r\n    hasValidationErrors: boolean;\r\n    requiresManualReview: boolean;\r\n    isReadyForNextStage: boolean;\r\n  } {\r\n    return {\r\n      id: this.id.toString(),\r\n      originalEventId: this.props.originalEventId.toString(),\r\n      title: this.props.title,\r\n      type: this.props.type,\r\n      severity: this.props.severity,\r\n      status: this.props.status,\r\n      normalizationStatus: this.props.normalizationStatus,\r\n      dataQualityScore: this.props.dataQualityScore,\r\n      riskScore: this.props.riskScore,\r\n      schemaVersion: this.props.schemaVersion,\r\n      appliedRulesCount: this.props.appliedRules.length,\r\n      hasValidationErrors: this.hasValidationErrors(),\r\n      requiresManualReview: this.requiresManualReview,\r\n      isReadyForNextStage: this.isReadyForNextStage(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      originalEventId: this.props.originalEventId.toString(),\r\n      metadata: this.props.metadata.toJSON(),\r\n      type: this.props.type,\r\n      severity: this.props.severity,\r\n      status: this.props.status,\r\n      processingStatus: this.props.processingStatus,\r\n      normalizationStatus: this.props.normalizationStatus,\r\n      originalData: this.props.originalData,\r\n      normalizedData: this.props.normalizedData,\r\n      title: this.props.title,\r\n      description: this.props.description,\r\n      tags: this.props.tags,\r\n      riskScore: this.props.riskScore,\r\n      confidenceLevel: this.props.confidenceLevel,\r\n      attributes: this.props.attributes,\r\n      correlationId: this.props.correlationId,\r\n      parentEventId: this.props.parentEventId?.toString(),\r\n      appliedRules: this.props.appliedRules,\r\n      normalizationResult: this.props.normalizationResult,\r\n      schemaVersion: this.props.schemaVersion,\r\n      normalizationStartedAt: this.props.normalizationStartedAt?.toISOString(),\r\n      normalizationCompletedAt: this.props.normalizationCompletedAt?.toISOString(),\r\n      normalizationAttempts: this.props.normalizationAttempts,\r\n      lastNormalizationError: this.props.lastNormalizationError,\r\n      dataQualityScore: this.props.dataQualityScore,\r\n      validationErrors: this.props.validationErrors,\r\n      requiresManualReview: this.props.requiresManualReview,\r\n      reviewNotes: this.props.reviewNotes,\r\n      reviewedBy: this.props.reviewedBy,\r\n      reviewedAt: this.props.reviewedAt?.toISOString(),\r\n      summary: this.getSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}