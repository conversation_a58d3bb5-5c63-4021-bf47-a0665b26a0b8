2165e7fe1b24c686ae8720f6d9de50c9
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatIntelligence = exports.ThreatStatus = exports.ThreatType = exports.ThreatConfidence = exports.ThreatSeverity = void 0;
const typeorm_1 = require("typeorm");
const class_validator_1 = require("class-validator");
const indicator_of_compromise_entity_1 = require("./indicator-of-compromise.entity");
const threat_actor_entity_1 = require("./threat-actor.entity");
const threat_campaign_entity_1 = require("./threat-campaign.entity");
/**
 * Threat intelligence severity levels
 */
var ThreatSeverity;
(function (ThreatSeverity) {
    ThreatSeverity["CRITICAL"] = "critical";
    ThreatSeverity["HIGH"] = "high";
    ThreatSeverity["MEDIUM"] = "medium";
    ThreatSeverity["LOW"] = "low";
    ThreatSeverity["INFO"] = "info";
})(ThreatSeverity || (exports.ThreatSeverity = ThreatSeverity = {}));
/**
 * Threat intelligence confidence levels
 */
var ThreatConfidence;
(function (ThreatConfidence) {
    ThreatConfidence["HIGH"] = "high";
    ThreatConfidence["MEDIUM"] = "medium";
    ThreatConfidence["LOW"] = "low";
    ThreatConfidence["UNKNOWN"] = "unknown";
})(ThreatConfidence || (exports.ThreatConfidence = ThreatConfidence = {}));
/**
 * Threat intelligence types
 */
var ThreatType;
(function (ThreatType) {
    ThreatType["MALWARE"] = "malware";
    ThreatType["PHISHING"] = "phishing";
    ThreatType["BOTNET"] = "botnet";
    ThreatType["APT"] = "apt";
    ThreatType["RANSOMWARE"] = "ransomware";
    ThreatType["TROJAN"] = "trojan";
    ThreatType["BACKDOOR"] = "backdoor";
    ThreatType["ROOTKIT"] = "rootkit";
    ThreatType["SPYWARE"] = "spyware";
    ThreatType["ADWARE"] = "adware";
    ThreatType["VULNERABILITY"] = "vulnerability";
    ThreatType["EXPLOIT"] = "exploit";
    ThreatType["SUSPICIOUS_ACTIVITY"] = "suspicious_activity";
    ThreatType["ATTACK_PATTERN"] = "attack_pattern";
    ThreatType["TOOL"] = "tool";
    ThreatType["INFRASTRUCTURE"] = "infrastructure";
    ThreatType["OTHER"] = "other";
})(ThreatType || (exports.ThreatType = ThreatType = {}));
/**
 * Threat intelligence status
 */
var ThreatStatus;
(function (ThreatStatus) {
    ThreatStatus["ACTIVE"] = "active";
    ThreatStatus["INACTIVE"] = "inactive";
    ThreatStatus["EXPIRED"] = "expired";
    ThreatStatus["REVOKED"] = "revoked";
    ThreatStatus["UNDER_REVIEW"] = "under_review";
})(ThreatStatus || (exports.ThreatStatus = ThreatStatus = {}));
/**
 * Threat intelligence entity
 * Represents comprehensive threat intelligence information including IOCs, attribution, and analysis
 */
let ThreatIntelligence = class ThreatIntelligence {
    /**
     * Check if threat intelligence is currently active
     */
    get isActive() {
        return this.status === ThreatStatus.ACTIVE &&
            (!this.expiresAt || this.expiresAt > new Date());
    }
    /**
     * Check if threat intelligence is expired
     */
    get isExpired() {
        return this.expiresAt ? this.expiresAt <= new Date() : false;
    }
    /**
     * Get age in days
     */
    get ageInDays() {
        return Math.floor((Date.now() - this.firstSeen.getTime()) / (1000 * 60 * 60 * 24));
    }
    /**
     * Get threat intelligence summary
     */
    getSummary() {
        return {
            id: this.id,
            title: this.title,
            threatType: this.threatType,
            severity: this.severity,
            confidence: this.confidence,
            status: this.status,
            isActive: this.isActive,
            isExpired: this.isExpired,
            ageInDays: this.ageInDays,
            observationCount: this.observationCount,
            indicatorCount: this.indicators?.length || 0,
            threatActor: this.threatActor?.name,
            threatCampaign: this.threatCampaign?.name,
            dataSource: this.dataSource.name,
            tags: this.tags,
        };
    }
    /**
     * Update observation count and last observed timestamp
     */
    recordObservation() {
        this.observationCount += 1;
        this.lastObserved = new Date();
    }
    /**
     * Calculate risk score based on multiple factors
     */
    calculateRiskScore() {
        let score = 0;
        // Base score from severity
        const severityScores = {
            [ThreatSeverity.CRITICAL]: 9.0,
            [ThreatSeverity.HIGH]: 7.0,
            [ThreatSeverity.MEDIUM]: 5.0,
            [ThreatSeverity.LOW]: 3.0,
            [ThreatSeverity.INFO]: 1.0,
        };
        score += severityScores[this.severity];
        // Confidence modifier
        const confidenceModifiers = {
            [ThreatConfidence.HIGH]: 1.0,
            [ThreatConfidence.MEDIUM]: 0.8,
            [ThreatConfidence.LOW]: 0.6,
            [ThreatConfidence.UNKNOWN]: 0.4,
        };
        score *= confidenceModifiers[this.confidence];
        // Observation frequency boost
        if (this.observationCount > 10) {
            score += 1.0;
        }
        else if (this.observationCount > 5) {
            score += 0.5;
        }
        // Recency factor
        const daysSinceLastSeen = this.lastSeen ?
            Math.floor((Date.now() - this.lastSeen.getTime()) / (1000 * 60 * 60 * 24)) :
            this.ageInDays;
        if (daysSinceLastSeen <= 7) {
            score += 1.0; // Very recent
        }
        else if (daysSinceLastSeen <= 30) {
            score += 0.5; // Recent
        }
        // Attribution factor
        if (this.isAttributed && this.threatActor) {
            score += 0.5;
        }
        // Cap at 10.0
        this.riskScore = Math.min(score, 10.0);
        return this.riskScore;
    }
    /**
     * Check if threat intelligence matches given criteria
     */
    matches(criteria) {
        if (criteria.threatTypes && !criteria.threatTypes.includes(this.threatType)) {
            return false;
        }
        if (criteria.severities && !criteria.severities.includes(this.severity)) {
            return false;
        }
        if (criteria.tags && this.tags) {
            const hasMatchingTag = criteria.tags.some(tag => this.tags.includes(tag));
            if (!hasMatchingTag) {
                return false;
            }
        }
        if (criteria.sectors && this.targetedSectors) {
            const hasMatchingSector = criteria.sectors.some(sector => this.targetedSectors.includes(sector));
            if (!hasMatchingSector) {
                return false;
            }
        }
        if (criteria.platforms && this.affectedPlatforms) {
            const hasMatchingPlatform = criteria.platforms.some(platform => this.affectedPlatforms.includes(platform));
            if (!hasMatchingPlatform) {
                return false;
            }
        }
        return true;
    }
    /**
     * Export threat intelligence for reporting
     */
    exportForReporting() {
        return {
            id: this.id,
            title: this.title,
            description: this.description,
            threatType: this.threatType,
            severity: this.severity,
            confidence: this.confidence,
            status: this.status,
            firstSeen: this.firstSeen,
            lastSeen: this.lastSeen,
            expiresAt: this.expiresAt,
            tags: this.tags,
            dataSource: this.dataSource,
            mitreAttack: this.mitreAttack,
            killChain: this.killChain,
            targetedSectors: this.targetedSectors,
            targetedCountries: this.targetedCountries,
            affectedPlatforms: this.affectedPlatforms,
            riskScore: this.riskScore,
            observationCount: this.observationCount,
            lastObserved: this.lastObserved,
            isIoc: this.isIoc,
            isAttributed: this.isAttributed,
            threatActor: this.threatActor?.name,
            threatCampaign: this.threatCampaign?.name,
            indicatorCount: this.indicators?.length || 0,
            references: this.references,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
    /**
     * Activate threat intelligence
     */
    activate() {
        this.status = ThreatStatus.ACTIVE;
    }
    /**
     * Deactivate threat intelligence
     */
    deactivate() {
        this.status = ThreatStatus.INACTIVE;
    }
    /**
     * Mark as expired
     */
    expire() {
        this.status = ThreatStatus.EXPIRED;
        this.expiresAt = new Date();
    }
    /**
     * Revoke threat intelligence
     */
    revoke(reason) {
        this.status = ThreatStatus.REVOKED;
        if (reason) {
            this.customAttributes = {
                ...this.customAttributes,
                revocationReason: reason,
                revokedAt: new Date(),
            };
        }
    }
};
exports.ThreatIntelligence = ThreatIntelligence;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ThreatType,
    }),
    (0, class_validator_1.IsEnum)(ThreatType),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "threatType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ThreatSeverity,
        default: ThreatSeverity.MEDIUM,
    }),
    (0, class_validator_1.IsEnum)(ThreatSeverity),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ThreatConfidence,
        default: ThreatConfidence.MEDIUM,
    }),
    (0, class_validator_1.IsEnum)(ThreatConfidence),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "confidence", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ThreatStatus,
        default: ThreatStatus.ACTIVE,
    }),
    (0, class_validator_1.IsEnum)(ThreatStatus),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], ThreatIntelligence.prototype, "firstSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ThreatIntelligence.prototype, "lastSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], ThreatIntelligence.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ThreatIntelligence.prototype, "dataSource", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ThreatIntelligence.prototype, "mitreAttack", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "killChain", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "targetedSectors", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "targetedCountries", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "affectedPlatforms", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ThreatIntelligence.prototype, "technicalDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ThreatIntelligence.prototype, "behavioralAnalysis", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "references", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ThreatIntelligence.prototype, "stixData", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 1, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ThreatIntelligence.prototype, "riskScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', default: 0 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ThreatIntelligence.prototype, "observationCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], ThreatIntelligence.prototype, "lastObserved", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ThreatIntelligence.prototype, "isIoc", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ThreatIntelligence.prototype, "isAttributed", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_e = typeof Record !== "undefined" && Record) === "function" ? _e : Object)
], ThreatIntelligence.prototype, "customAttributes", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => indicator_of_compromise_entity_1.IndicatorOfCompromise, (ioc) => ioc.threatIntelligence),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "indicators", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => threat_actor_entity_1.ThreatActor, (actor) => actor.threatIntelligence, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'threat_actor_id' }),
    __metadata("design:type", typeof (_f = typeof threat_actor_entity_1.ThreatActor !== "undefined" && threat_actor_entity_1.ThreatActor) === "function" ? _f : Object)
], ThreatIntelligence.prototype, "threatActor", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "threatActorId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => threat_campaign_entity_1.ThreatCampaign, (campaign) => campaign.threatIntelligence, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'threat_campaign_id' }),
    __metadata("design:type", typeof (_g = typeof threat_campaign_entity_1.ThreatCampaign !== "undefined" && threat_campaign_entity_1.ThreatCampaign) === "function" ? _g : Object)
], ThreatIntelligence.prototype, "threatCampaign", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "threatCampaignId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_h = typeof Date !== "undefined" && Date) === "function" ? _h : Object)
], ThreatIntelligence.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_j = typeof Date !== "undefined" && Date) === "function" ? _j : Object)
], ThreatIntelligence.prototype, "updatedAt", void 0);
exports.ThreatIntelligence = ThreatIntelligence = __decorate([
    (0, typeorm_1.Entity)('threat_intelligence'),
    (0, typeorm_1.Index)(['threatType', 'severity']),
    (0, typeorm_1.Index)(['status', 'firstSeen']),
    (0, typeorm_1.Index)(['confidence', 'severity']),
    (0, typeorm_1.Index)(['tags'], { where: 'tags IS NOT NULL' })
], ThreatIntelligence);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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