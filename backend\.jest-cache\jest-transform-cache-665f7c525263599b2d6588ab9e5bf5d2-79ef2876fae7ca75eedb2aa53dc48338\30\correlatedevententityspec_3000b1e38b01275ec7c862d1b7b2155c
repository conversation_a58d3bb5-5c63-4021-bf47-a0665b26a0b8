9aa066188f8a8a347e8d9b246c9d4d27
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const correlated_event_entity_1 = require("../correlated-event.entity");
const correlation_status_enum_1 = require("../../enums/correlation-status.enum");
const shared_kernel_1 = require("../../../../../shared-kernel");
const event_metadata_value_object_1 = require("../../value-objects/event-metadata/event-metadata.value-object");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const event_status_enum_1 = require("../../enums/event-status.enum");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const confidence_level_enum_1 = require("../../enums/confidence-level.enum");
const event_timestamp_value_object_1 = require("../../value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../value-objects/event-metadata/event-source.value-object");
const node_test_1 = require("node:test");
const node_test_2 = require("node:test");
const node_test_3 = require("node:test");
(0, node_test_2.describe)('CorrelatedEvent', () => {
    let validProps;
    let mockEventMetadata;
    (0, node_test_3.beforeEach)(() => {
        // Create mock event metadata
        mockEventMetadata = event_metadata_value_object_1.EventMetadata.create({
            timestamp: event_timestamp_value_object_1.EventTimestamp.now(),
            source: event_source_value_object_1.EventSource.create({
                type: 'SIEM',
                identifier: 'test-siem-001',
                name: 'Test SIEM System'
            }),
            processingInfo: {
                receivedAt: new Date(),
                processedAt: new Date(),
                processingDuration: 100,
                version: '1.0.0'
            }
        });
        validProps = {
            enrichedEventId: shared_kernel_1.UniqueEntityId.create(),
            metadata: mockEventMetadata,
            type: event_type_enum_1.EventType.SECURITY_ALERT,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            status: event_status_enum_1.EventStatus.ACTIVE,
            processingStatus: event_processing_status_enum_1.EventProcessingStatus.CORRELATED,
            correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
            enrichedData: {
                original_event: 'test_event',
                enriched_at: new Date().toISOString(),
                threat_indicators: ['malicious_ip', 'suspicious_domain']
            },
            correlatedData: {
                correlation_id: 'corr_123',
                related_events: 5,
                attack_chain_detected: true,
                confidence_score: 85
            },
            title: 'Test Correlated Security Event',
            description: 'A test correlated security event for unit testing',
            tags: ['test', 'correlation', 'security'],
            riskScore: 75,
            confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH,
            attributes: {
                test_attribute: 'test_value'
            },
            correlationId: 'corr_test_123',
            childEventIds: [],
            appliedRules: [],
            correlationMatches: [],
            relatedEventIds: [],
            correlationPatterns: ['temporal_sequence', 'ip_clustering'],
            correlationQualityScore: 85,
            requiresManualReview: false
        };
    });
    (0, node_test_2.describe)('creation', () => {
        (0, node_test_1.it)('should create a valid CorrelatedEvent with required properties', () => {
            const correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(validProps);
            expect(correlatedEvent).toBeInstanceOf(correlated_event_entity_1.CorrelatedEvent);
            expect(correlatedEvent.id).toBeDefined();
            expect(correlatedEvent.enrichedEventId).toEqual(validProps.enrichedEventId);
            expect(correlatedEvent.metadata).toEqual(validProps.metadata);
            expect(correlatedEvent.type).toBe(validProps.type);
            expect(correlatedEvent.severity).toBe(validProps.severity);
            expect(correlatedEvent.status).toBe(validProps.status);
            expect(correlatedEvent.processingStatus).toBe(validProps.processingStatus);
            expect(correlatedEvent.correlationStatus).toBe(validProps.correlationStatus);
            expect(correlatedEvent.title).toBe(validProps.title);
            expect(correlatedEvent.confidenceLevel).toBe(validProps.confidenceLevel);
            expect(correlatedEvent.correlationId).toBe(validProps.correlationId);
        });
        (0, node_test_1.it)('should generate domain event when created', () => {
            const correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(validProps);
            const domainEvents = correlatedEvent.getUncommittedEvents();
            expect(domainEvents).toHaveLength(1);
            expect(domainEvents[0].eventName).toBe('CorrelatedEventCreatedDomainEvent');
        });
        (0, node_test_1.it)('should throw error when enrichedEventId is missing', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.enrichedEventId;
            expect(() => correlated_event_entity_1.CorrelatedEvent.create(invalidProps)).toThrow('CorrelatedEvent must reference an enriched event');
        });
        (0, node_test_1.it)('should throw error when metadata is missing', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.metadata;
            expect(() => correlated_event_entity_1.CorrelatedEvent.create(invalidProps)).toThrow('CorrelatedEvent must have metadata');
        });
        (0, node_test_1.it)('should throw error when title is empty', () => {
            const invalidProps = { ...validProps, title: '' };
            expect(() => correlated_event_entity_1.CorrelatedEvent.create(invalidProps)).toThrow('CorrelatedEvent must have a non-empty title');
        });
        (0, node_test_1.it)('should throw error when correlationId is empty', () => {
            const invalidProps = { ...validProps, correlationId: '' };
            expect(() => correlated_event_entity_1.CorrelatedEvent.create(invalidProps)).toThrow('CorrelatedEvent must have a correlation ID');
        });
    });
    (0, node_test_2.describe)('correlation status management', () => {
        let correlatedEvent;
        (0, node_test_3.beforeEach)(() => {
            const pendingProps = { ...validProps, correlationStatus: correlation_status_enum_1.CorrelationStatus.PENDING };
            correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(pendingProps);
        });
        (0, node_test_1.it)('should start correlation process', () => {
            correlatedEvent.startCorrelation();
            expect(correlatedEvent.correlationStatus).toBe(correlation_status_enum_1.CorrelationStatus.IN_PROGRESS);
            expect(correlatedEvent.correlationAttempts).toBe(1);
            expect(correlatedEvent.correlationStartedAt).toBeDefined();
        });
        (0, node_test_1.it)('should complete correlation process', () => {
            correlatedEvent.startCorrelation();
            const result = {
                success: true,
                appliedRules: ['rule1', 'rule2'],
                failedRules: [],
                warnings: [],
                errors: [],
                processingDurationMs: 1500,
                confidenceScore: 85,
                rulesUsed: 2,
                matchesFound: 5,
                patternsIdentified: ['temporal_sequence']
            };
            correlatedEvent.completeCorrelation(result);
            expect(correlatedEvent.correlationStatus).toBe(correlation_status_enum_1.CorrelationStatus.COMPLETED);
            expect(correlatedEvent.correlationCompletedAt).toBeDefined();
            expect(correlatedEvent.correlationResult).toEqual(result);
        });
        (0, node_test_1.it)('should fail correlation process', () => {
            correlatedEvent.startCorrelation();
            const error = 'Correlation engine timeout';
            correlatedEvent.failCorrelation(error);
            expect(correlatedEvent.correlationStatus).toBe(correlation_status_enum_1.CorrelationStatus.FAILED);
            expect(correlatedEvent.lastCorrelationError).toBe(error);
        });
        (0, node_test_1.it)('should skip correlation process', () => {
            const reason = 'Event does not meet correlation criteria';
            correlatedEvent.skipCorrelation(reason);
            expect(correlatedEvent.correlationStatus).toBe(correlation_status_enum_1.CorrelationStatus.SKIPPED);
            expect(correlatedEvent.reviewNotes).toBe(reason);
        });
        (0, node_test_1.it)('should reset correlation for retry', () => {
            correlatedEvent.startCorrelation();
            correlatedEvent.failCorrelation('Test error');
            correlatedEvent.resetCorrelation();
            expect(correlatedEvent.correlationStatus).toBe(correlation_status_enum_1.CorrelationStatus.PENDING);
            expect(correlatedEvent.correlationStartedAt).toBeUndefined();
            expect(correlatedEvent.lastCorrelationError).toBeUndefined();
        });
        (0, node_test_1.it)('should not allow reset when max attempts exceeded', () => {
            // Simulate max attempts exceeded
            for (let i = 0; i < 3; i++) {
                correlatedEvent.startCorrelation();
                correlatedEvent.failCorrelation('Test error');
                if (i < 2) {
                    correlatedEvent.resetCorrelation();
                }
            }
            expect(() => correlatedEvent.resetCorrelation()).toThrow('Cannot reset correlation: maximum attempts exceeded');
        });
    });
    (0, node_test_2.describe)('correlation matches management', () => {
        let correlatedEvent;
        (0, node_test_3.beforeEach)(() => {
            correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(validProps);
        });
        (0, node_test_1.it)('should add correlation match', () => {
            const match = {
                eventId: shared_kernel_1.UniqueEntityId.create(),
                confidence: 80,
                matchType: correlated_event_entity_1.CorrelationMatchType.TEMPORAL,
                ruleId: 'temporal_rule_1',
                details: { timeWindow: 3600000 },
                timestamp: new Date(),
                weight: 0.8
            };
            correlatedEvent.addCorrelationMatch(match);
            expect(correlatedEvent.correlationMatches).toHaveLength(1);
            expect(correlatedEvent.correlationMatches[0]).toEqual(match);
        });
        (0, node_test_1.it)('should update existing match with higher confidence', () => {
            const eventId = shared_kernel_1.UniqueEntityId.create();
            const ruleId = 'test_rule';
            const match1 = {
                eventId,
                confidence: 70,
                matchType: correlated_event_entity_1.CorrelationMatchType.PATTERN,
                ruleId,
                details: {},
                timestamp: new Date(),
                weight: 0.7
            };
            const match2 = {
                eventId,
                confidence: 85,
                matchType: correlated_event_entity_1.CorrelationMatchType.PATTERN,
                ruleId,
                details: { updated: true },
                timestamp: new Date(),
                weight: 0.85
            };
            correlatedEvent.addCorrelationMatch(match1);
            correlatedEvent.addCorrelationMatch(match2);
            expect(correlatedEvent.correlationMatches).toHaveLength(1);
            expect(correlatedEvent.correlationMatches[0].confidence).toBe(85);
        });
        (0, node_test_1.it)('should get matches by rule', () => {
            const ruleId = 'test_rule';
            const match1 = {
                eventId: shared_kernel_1.UniqueEntityId.create(),
                confidence: 80,
                matchType: correlated_event_entity_1.CorrelationMatchType.TEMPORAL,
                ruleId,
                details: {},
                timestamp: new Date(),
                weight: 0.8
            };
            const match2 = {
                eventId: shared_kernel_1.UniqueEntityId.create(),
                confidence: 75,
                matchType: correlated_event_entity_1.CorrelationMatchType.SPATIAL,
                ruleId: 'other_rule',
                details: {},
                timestamp: new Date(),
                weight: 0.75
            };
            correlatedEvent.addCorrelationMatch(match1);
            correlatedEvent.addCorrelationMatch(match2);
            const matchesByRule = correlatedEvent.getMatchesByRule(ruleId);
            expect(matchesByRule).toHaveLength(1);
            expect(matchesByRule[0].ruleId).toBe(ruleId);
        });
        (0, node_test_1.it)('should get matches by type', () => {
            const match1 = {
                eventId: shared_kernel_1.UniqueEntityId.create(),
                confidence: 80,
                matchType: correlated_event_entity_1.CorrelationMatchType.TEMPORAL,
                ruleId: 'rule1',
                details: {},
                timestamp: new Date(),
                weight: 0.8
            };
            const match2 = {
                eventId: shared_kernel_1.UniqueEntityId.create(),
                confidence: 75,
                matchType: correlated_event_entity_1.CorrelationMatchType.TEMPORAL,
                ruleId: 'rule2',
                details: {},
                timestamp: new Date(),
                weight: 0.75
            };
            correlatedEvent.addCorrelationMatch(match1);
            correlatedEvent.addCorrelationMatch(match2);
            const temporalMatches = correlatedEvent.getMatchesByType(correlated_event_entity_1.CorrelationMatchType.TEMPORAL);
            expect(temporalMatches).toHaveLength(2);
        });
        (0, node_test_1.it)('should calculate average match confidence', () => {
            const match1 = {
                eventId: shared_kernel_1.UniqueEntityId.create(),
                confidence: 80,
                matchType: correlated_event_entity_1.CorrelationMatchType.TEMPORAL,
                ruleId: 'rule1',
                details: {},
                timestamp: new Date(),
                weight: 0.8
            };
            const match2 = {
                eventId: shared_kernel_1.UniqueEntityId.create(),
                confidence: 90,
                matchType: correlated_event_entity_1.CorrelationMatchType.SPATIAL,
                ruleId: 'rule2',
                details: {},
                timestamp: new Date(),
                weight: 0.9
            };
            correlatedEvent.addCorrelationMatch(match1);
            correlatedEvent.addCorrelationMatch(match2);
            const avgConfidence = correlatedEvent.getAverageMatchConfidence();
            expect(avgConfidence).toBe(85);
        });
    });
    (0, node_test_2.describe)('attack chain management', () => {
        let correlatedEvent;
        (0, node_test_3.beforeEach)(() => {
            correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(validProps);
        });
        (0, node_test_1.it)('should set attack chain', () => {
            const attackChain = {
                id: 'attack_chain_1',
                name: 'Multi-stage Attack',
                description: 'Coordinated attack with multiple stages',
                stages: [
                    {
                        id: 'stage_1',
                        name: 'Initial Access',
                        description: 'Initial compromise',
                        eventIds: [shared_kernel_1.UniqueEntityId.create()],
                        order: 1,
                        confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                        timestamp: new Date(),
                        tactic: 'Initial Access'
                    }
                ],
                confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                timeline: {
                    startTime: new Date(Date.now() - 3600000),
                    endTime: new Date(),
                    duration: 3600000
                }
            };
            correlatedEvent.setAttackChain(attackChain);
            expect(correlatedEvent.hasAttackChain()).toBe(true);
            expect(correlatedEvent.attackChain).toEqual(attackChain);
        });
        (0, node_test_1.it)('should validate attack chain chronological order', () => {
            const invalidAttackChain = {
                id: 'attack_chain_1',
                name: 'Invalid Attack Chain',
                description: 'Attack chain with invalid stage order',
                stages: [
                    {
                        id: 'stage_1',
                        name: 'Stage 1',
                        description: 'First stage',
                        eventIds: [shared_kernel_1.UniqueEntityId.create()],
                        order: 2, // Invalid order
                        confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                        timestamp: new Date(),
                    },
                    {
                        id: 'stage_2',
                        name: 'Stage 2',
                        description: 'Second stage',
                        eventIds: [shared_kernel_1.UniqueEntityId.create()],
                        order: 1, // Invalid order
                        confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                        timestamp: new Date(),
                    }
                ],
                confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                timeline: {
                    startTime: new Date(Date.now() - 3600000),
                    endTime: new Date(),
                    duration: 3600000
                }
            };
            expect(() => correlatedEvent.setAttackChain(invalidAttackChain)).toThrow('Attack chain stages must be in chronological order');
        });
    });
    (0, node_test_2.describe)('business rule validation', () => {
        (0, node_test_1.it)('should check if correlation is completed', () => {
            const completedProps = { ...validProps, correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED };
            const correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(completedProps);
            expect(correlatedEvent.isCorrelationCompleted()).toBe(true);
        });
        (0, node_test_1.it)('should check if correlation failed', () => {
            const failedProps = { ...validProps, correlationStatus: correlation_status_enum_1.CorrelationStatus.FAILED };
            const correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(failedProps);
            expect(correlatedEvent.isCorrelationFailed()).toBe(true);
        });
        (0, node_test_1.it)('should check if correlation is in progress', () => {
            const inProgressProps = { ...validProps, correlationStatus: correlation_status_enum_1.CorrelationStatus.IN_PROGRESS };
            const correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(inProgressProps);
            expect(correlatedEvent.isCorrelationInProgress()).toBe(true);
        });
        (0, node_test_1.it)('should check if has high correlation quality', () => {
            const highQualityProps = { ...validProps, correlationQualityScore: 85 };
            const correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(highQualityProps);
            expect(correlatedEvent.hasHighCorrelationQuality()).toBe(true);
        });
        (0, node_test_1.it)('should check if has validation errors', () => {
            const errorProps = { ...validProps, validationErrors: ['Error 1', 'Error 2'] };
            const correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(errorProps);
            expect(correlatedEvent.hasValidationErrors()).toBe(true);
        });
        (0, node_test_1.it)('should check if ready for next stage', () => {
            const readyProps = {
                ...validProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                correlationQualityScore: 85,
                validationErrors: [],
                requiresManualReview: false
            };
            const correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(readyProps);
            expect(correlatedEvent.isReadyForNextStage()).toBe(true);
        });
        (0, node_test_1.it)('should check if has high confidence correlation', () => {
            const highConfidenceProps = {
                ...validProps,
                correlationResult: {
                    success: true,
                    appliedRules: [],
                    failedRules: [],
                    warnings: [],
                    errors: [],
                    processingDurationMs: 1000,
                    confidenceScore: 90,
                    rulesUsed: 2,
                    matchesFound: 5,
                    patternsIdentified: []
                }
            };
            const correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(highConfidenceProps);
            expect(correlatedEvent.isHighConfidenceCorrelation()).toBe(true);
        });
    });
    (0, node_test_2.describe)('correlation patterns and relationships', () => {
        let correlatedEvent;
        (0, node_test_3.beforeEach)(() => {
            correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(validProps);
        });
        (0, node_test_1.it)('should add correlation pattern', () => {
            const pattern = 'behavioral_anomaly';
            correlatedEvent.addCorrelationPattern(pattern);
            expect(correlatedEvent.correlationPatterns).toContain(pattern);
        });
        (0, node_test_1.it)('should add related event', () => {
            const relatedEventId = shared_kernel_1.UniqueEntityId.create();
            correlatedEvent.addRelatedEvent(relatedEventId);
            expect(correlatedEvent.relatedEventIds).toContain(relatedEventId);
        });
        (0, node_test_1.it)('should add child event', () => {
            const childEventId = shared_kernel_1.UniqueEntityId.create();
            correlatedEvent.addChildEvent(childEventId);
            expect(correlatedEvent.childEventIds).toContain(childEventId);
        });
        (0, node_test_1.it)('should not add duplicate related events', () => {
            const relatedEventId = shared_kernel_1.UniqueEntityId.create();
            correlatedEvent.addRelatedEvent(relatedEventId);
            correlatedEvent.addRelatedEvent(relatedEventId);
            expect(correlatedEvent.relatedEventIds.filter(id => id.equals(relatedEventId))).toHaveLength(1);
        });
    });
    (0, node_test_2.describe)('correlation duration calculation', () => {
        let correlatedEvent;
        (0, node_test_3.beforeEach)(() => {
            const pendingProps = { ...validProps, correlationStatus: correlation_status_enum_1.CorrelationStatus.PENDING };
            correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(pendingProps);
        });
        (0, node_test_1.it)('should return null when correlation not started', () => {
            expect(correlatedEvent.getCorrelationDuration()).toBeNull();
        });
        (0, node_test_1.it)('should calculate duration when correlation completed', () => {
            correlatedEvent.startCorrelation();
            // Simulate some processing time
            const startTime = correlatedEvent.correlationStartedAt;
            const endTime = new Date(startTime.getTime() + 5000); // 5 seconds later
            const result = {
                success: true,
                appliedRules: [],
                failedRules: [],
                warnings: [],
                errors: [],
                processingDurationMs: 5000,
                confidenceScore: 85,
                rulesUsed: 1,
                matchesFound: 3,
                patternsIdentified: []
            };
            correlatedEvent.completeCorrelation(result);
            const duration = correlatedEvent.getCorrelationDuration();
            expect(duration).toBeGreaterThan(0);
        });
    });
    (0, node_test_2.describe)('applied rules management', () => {
        let correlatedEvent;
        (0, node_test_3.beforeEach)(() => {
            const rule = {
                id: 'test_rule_1',
                name: 'Test Rule',
                description: 'A test correlation rule',
                type: correlated_event_entity_1.CorrelationRuleType.TEMPORAL,
                priority: 100,
                required: false,
                timeWindowMs: 3600000,
                minConfidence: 70
            };
            const propsWithRules = {
                ...validProps,
                appliedRules: [rule]
            };
            correlatedEvent = correlated_event_entity_1.CorrelatedEvent.create(propsWithRules);
        });
        (0, node_test_1.it)('should check if specific rule was applied', () => {
            expect(correlatedEvent.hasAppliedRule('test_rule_1')).toBe(true);
            expect(correlatedEvent.hasAppliedRule('non_existent_rule')).toBe(false);
        });
    });
    (0, node_test_2.describe)('data integrity validation', () => {
        (0, node_test_1.it)('should validate correlation quality score range', () => {
            const invalidProps = { ...validProps, correlationQualityScore: 150 };
            expect(() => correlated_event_entity_1.CorrelatedEvent.create(invalidProps)).toThrow('Correlation quality score must be between 0 and 100');
        });
        (0, node_test_1.it)('should validate risk score range', () => {
            const invalidProps = { ...validProps, riskScore: -10 };
            expect(() => correlated_event_entity_1.CorrelatedEvent.create(invalidProps)).toThrow('Risk score must be between 0 and 100');
        });
        (0, node_test_1.it)('should validate correlation attempts cannot be negative', () => {
            const invalidProps = { ...validProps, correlationAttempts: -1 };
            expect(() => correlated_event_entity_1.CorrelatedEvent.create(invalidProps)).toThrow('Correlation attempts cannot be negative');
        });
        (0, node_test_1.it)('should validate maximum correlation matches limit', () => {
            const tooManyMatches = [];
            for (let i = 0; i < 101; i++) {
                tooManyMatches.push({
                    eventId: shared_kernel_1.UniqueEntityId.create(),
                    confidence: 80,
                    matchType: correlated_event_entity_1.CorrelationMatchType.PATTERN,
                    ruleId: `rule_${i}`,
                    details: {},
                    timestamp: new Date(),
                    weight: 0.8
                });
            }
            const invalidProps = { ...validProps, correlationMatches: tooManyMatches };
            expect(() => correlated_event_entity_1.CorrelatedEvent.create(invalidProps)).toThrow('CorrelatedEvent cannot have more than 100 correlation matches');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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