{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\logging\\__tests__\\logging-integration.test.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAA6D;AAC7D,sDAAkD;AAClD,sDAAkD;AAClD,4EAAuE;AACvE,sEAAiE;AACjE,iEAA6D;AAC7D,6EAAyE;AACzE,yEAAqE;AACrE,iEAA6D;AAC7D,gEAA4D;AAE5D;;;GAGG;AACH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;IAClD,IAAI,MAAqB,CAAC;IAC1B,IAAI,aAA4B,CAAC;IACjC,IAAI,gBAAyC,CAAC;IAC9C,IAAI,kBAAwC,CAAC;IAC7C,IAAI,aAA4B,CAAC;IAEjC,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,GAAG,MAAM,cAAI,CAAC,mBAAmB,CAAC;YACtC,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,IAAI,EAAE,CAAC,8BAAa,CAAC;oBACrB,QAAQ,EAAE,IAAI;iBACf,CAAC;gBACF,8BAAa;aACd;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,aAAa,GAAG,MAAM,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;QACzD,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAA0B,mDAAuB,CAAC,CAAC;QAChF,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAuB,6CAAoB,CAAC,CAAC;QAC5E,aAAa,GAAG,MAAM,CAAC,GAAG,CAAgB,sBAAa,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;YAC3B,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,GAAG,EAAE;gBACV,aAAa,CAAC,GAAG,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;gBACtD,aAAa,CAAC,KAAK,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC;gBACzD,aAAa,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,CAAC;gBAC1D,aAAa,CAAC,KAAK,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,aAAa,GAAG,kBAAkB,CAAC,qBAAqB,EAAE,CAAC;YAEjE,kBAAkB,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,EAAE;gBACzC,MAAM,CAAC,GAAG,EAAE;oBACV,aAAa,CAAC,GAAG,CAAC,+BAA+B,EAAE,aAAa,CAAC,CAAC;gBACpE,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;YAC3B,MAAM,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,UAAU,GAAG,gBAAgB,CAAC,aAAa,EAAE,CAAC;YACpD,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,OAAO,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,CAAC;YAC3D,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;YAC3B,MAAM,CAAC,kBAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,GAAG,GAAG,kBAAkB,CAAC,qBAAqB,EAAE,CAAC;YACvD,MAAM,GAAG,GAAG,kBAAkB,CAAC,qBAAqB,EAAE,CAAC;YAEvD,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1B,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1B,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,aAAa,GAAG,qBAAqB,CAAC;YAE5C,kBAAkB,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,EAAE;gBACzC,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAElE,kBAAkB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAC1C,MAAM,OAAO,GAAG,kBAAkB,CAAC,UAAU,EAAE,CAAC;gBAChD,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC1C,MAAM,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,OAAO,GAAG,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,CAAC;YAChE,MAAM,aAAa,GAAG,kBAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC9D,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;YAC7B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;gBAC3C,MAAM,SAAS,GAAG,IAAI,8BAAa,CAAC;oBAClC,MAAM,EAAE,KAAK;oBACb,YAAY,EAAE,IAAI;oBAClB,eAAe,EAAE,IAAI;iBACtB,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG;oBACf,KAAK,EAAE,MAAM;oBACb,OAAO,EAAE,cAAc;oBACvB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,aAAa,EAAE,kBAAkB;oBACjC,OAAO,EAAE,aAAa;iBACvB,CAAC;gBAEF,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,QAAe,CAAC,CAAC;gBACjD,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7B,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gBAErC,uBAAuB;gBACvB,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACnE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;YACnC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,SAAS,GAAG,IAAI,0CAAmB,CAAC;oBACxC,gBAAgB,EAAE,IAAI;oBACtB,YAAY,EAAE,IAAI;oBAClB,kBAAkB,EAAE,IAAI;iBACzB,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG;oBACf,KAAK,EAAE,MAAM;oBACb,OAAO,EAAE,cAAc;oBACvB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,aAAa,EAAE,kBAAkB;oBACjC,OAAO,EAAE,aAAa;iBACvB,CAAC;gBAEF,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,QAAe,CAAC,CAAC;gBACjD,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7B,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gBACrC,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC7C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBAC/C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;gBAC5C,MAAM,SAAS,GAAG,IAAI,sCAAiB,CAAC;oBACtC,gBAAgB,EAAE,MAAM;oBACxB,iBAAiB,EAAE,IAAI;oBACvB,iBAAiB,EAAE,IAAI;iBACxB,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG;oBACf,KAAK,EAAE,MAAM;oBACb,OAAO,EAAE,yBAAyB;oBAClC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,SAAS,EAAE,oBAAoB;oBAC/B,QAAQ,EAAE,MAAM;oBAChB,MAAM,EAAE,aAAa;oBACrB,aAAa,EAAE,kBAAkB;iBAClC,CAAC;gBAEF,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,QAAe,CAAC,CAAC;gBACjD,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7B,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gBAErC,2CAA2C;gBAC3C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;gBAEjE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAiB,CAAC,CAAC;gBACpD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACpD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;YAC7B,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,MAAM,GAAG;oBACb,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,IAAI;oBACb,KAAK,EAAE,MAAa;oBACpB,OAAO,EAAE;wBACP,QAAQ,EAAE,UAAU;wBACpB,OAAO,EAAE,KAAK;wBACd,QAAQ,EAAE,CAAC;wBACX,QAAQ,EAAE,IAAI;wBACd,aAAa,EAAE,IAAI;qBACpB;iBACF,CAAC;gBAEF,MAAM,CAAC,GAAG,EAAE;oBACV,MAAM,SAAS,GAAG,IAAI,8BAAa,CAAC,MAAM,CAAC,CAAC;oBAC5C,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;oBAChC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\logging\\__tests__\\logging-integration.test.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ConfigModule, ConfigService } from '@nestjs/config';\r\nimport { LoggingModule } from '../logging.module';\r\nimport { LoggerService } from '../logger.service';\r\nimport { TransportManagerService } from '../transport-manager.service';\r\nimport { CorrelationIdService } from '../correlation-id.service';\r\nimport { JsonFormatter } from '../formatters/json.formatter';\r\nimport { StructuredFormatter } from '../formatters/structured.formatter';\r\nimport { SecurityFormatter } from '../formatters/security.formatter';\r\nimport { FileTransport } from '../transports/file.transport';\r\nimport { loggingConfig } from '../../config/logging.config';\r\n\r\n/**\r\n * Integration tests for the logging infrastructure\r\n * Tests formatters, transports, and the complete logging pipeline\r\n */\r\ndescribe('Logging Infrastructure Integration', () => {\r\n  let module: TestingModule;\r\n  let loggerService: LoggerService;\r\n  let transportManager: TransportManagerService;\r\n  let correlationService: CorrelationIdService;\r\n  let configService: ConfigService;\r\n\r\n  beforeAll(async () => {\r\n    module = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          load: [loggingConfig],\r\n          isGlobal: true,\r\n        }),\r\n        LoggingModule,\r\n      ],\r\n    }).compile();\r\n\r\n    loggerService = module.get<LoggerService>(LoggerService);\r\n    transportManager = module.get<TransportManagerService>(TransportManagerService);\r\n    correlationService = module.get<CorrelationIdService>(CorrelationIdService);\r\n    configService = module.get<ConfigService>(ConfigService);\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await module.close();\r\n  });\r\n\r\n  describe('LoggerService', () => {\r\n    it('should be defined', () => {\r\n      expect(loggerService).toBeDefined();\r\n    });\r\n\r\n    it('should log messages with different levels', () => {\r\n      expect(() => {\r\n        loggerService.log('Test info message', 'TestContext');\r\n        loggerService.error('Test error message', 'TestContext');\r\n        loggerService.warn('Test warning message', 'TestContext');\r\n        loggerService.debug('Test debug message', 'TestContext');\r\n      }).not.toThrow();\r\n    });\r\n\r\n    it('should log with correlation ID', () => {\r\n      const correlationId = correlationService.generateCorrelationId();\r\n      \r\n      correlationService.run(correlationId, () => {\r\n        expect(() => {\r\n          loggerService.log('Test message with correlation', 'TestContext');\r\n        }).not.toThrow();\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('TransportManagerService', () => {\r\n    it('should be defined', () => {\r\n      expect(transportManager).toBeDefined();\r\n    });\r\n\r\n    it('should have formatters available', () => {\r\n      const formatters = transportManager.getFormatters();\r\n      expect(formatters.size).toBeGreaterThan(0);\r\n      expect(formatters.has('json')).toBe(true);\r\n      expect(formatters.has('structured')).toBe(true);\r\n      expect(formatters.has('security')).toBe(true);\r\n    });\r\n\r\n    it('should provide configuration summary', () => {\r\n      const summary = transportManager.getConfigurationSummary();\r\n      expect(summary).toHaveProperty('transports');\r\n      expect(summary).toHaveProperty('formatters');\r\n      expect(summary).toHaveProperty('healthMonitoring');\r\n      expect(Array.isArray(summary.transports)).toBe(true);\r\n      expect(Array.isArray(summary.formatters)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('CorrelationIdService', () => {\r\n    it('should be defined', () => {\r\n      expect(correlationService).toBeDefined();\r\n    });\r\n\r\n    it('should generate correlation IDs', () => {\r\n      const id1 = correlationService.generateCorrelationId();\r\n      const id2 = correlationService.generateCorrelationId();\r\n      \r\n      expect(id1).toBeDefined();\r\n      expect(id2).toBeDefined();\r\n      expect(id1).not.toBe(id2);\r\n    });\r\n\r\n    it('should maintain correlation context', () => {\r\n      const correlationId = 'test-correlation-id';\r\n      \r\n      correlationService.run(correlationId, () => {\r\n        expect(correlationService.getCorrelationId()).toBe(correlationId);\r\n        \r\n        correlationService.setUserId('test-user');\r\n        const context = correlationService.getContext();\r\n        expect(context?.userId).toBe('test-user');\r\n        expect(context?.correlationId).toBe(correlationId);\r\n      });\r\n    });\r\n\r\n    it('should create correlation ID from headers', () => {\r\n      const headers = { 'x-correlation-id': 'header-correlation-id' };\r\n      const correlationId = correlationService.fromHeaders(headers);\r\n      expect(correlationId).toBe('header-correlation-id');\r\n    });\r\n  });\r\n\r\n  describe('Formatters', () => {\r\n    describe('JsonFormatter', () => {\r\n      it('should format log entries as JSON', () => {\r\n        const formatter = new JsonFormatter({\r\n          pretty: false,\r\n          includeStack: true,\r\n          includeMetadata: true,\r\n        });\r\n\r\n        const mockInfo = {\r\n          level: 'info',\r\n          message: 'Test message',\r\n          timestamp: new Date().toISOString(),\r\n          correlationId: 'test-correlation',\r\n          context: 'TestContext',\r\n        };\r\n\r\n        const result = formatter.format(mockInfo as any);\r\n        expect(result).toBeDefined();\r\n        expect(result.message).toBeDefined();\r\n        \r\n        // Should be valid JSON\r\n        expect(() => JSON.parse(result.message as string)).not.toThrow();\r\n      });\r\n    });\r\n\r\n    describe('StructuredFormatter', () => {\r\n      it('should format log entries as structured text', () => {\r\n        const formatter = new StructuredFormatter({\r\n          includeTimestamp: true,\r\n          includeLevel: true,\r\n          includeCorrelation: true,\r\n        });\r\n\r\n        const mockInfo = {\r\n          level: 'info',\r\n          message: 'Test message',\r\n          timestamp: new Date().toISOString(),\r\n          correlationId: 'test-correlation',\r\n          context: 'TestContext',\r\n        };\r\n\r\n        const result = formatter.format(mockInfo as any);\r\n        expect(result).toBeDefined();\r\n        expect(result.message).toBeDefined();\r\n        expect(typeof result.message).toBe('string');\r\n        expect(result.message).toContain('level=INFO');\r\n        expect(result.message).toContain('correlationId=test-correlation');\r\n      });\r\n    });\r\n\r\n    describe('SecurityFormatter', () => {\r\n      it('should format security log entries', () => {\r\n        const formatter = new SecurityFormatter({\r\n          complianceFormat: 'json',\r\n          includeIndicators: true,\r\n          maskSensitiveData: true,\r\n        });\r\n\r\n        const mockInfo = {\r\n          level: 'warn',\r\n          message: 'Security event detected',\r\n          timestamp: new Date().toISOString(),\r\n          eventType: 'security_violation',\r\n          severity: 'high',\r\n          source: 'test-source',\r\n          correlationId: 'test-correlation',\r\n        };\r\n\r\n        const result = formatter.format(mockInfo as any);\r\n        expect(result).toBeDefined();\r\n        expect(result.message).toBeDefined();\r\n        \r\n        // Should be valid JSON for security events\r\n        expect(() => JSON.parse(result.message as string)).not.toThrow();\r\n        \r\n        const parsed = JSON.parse(result.message as string);\r\n        expect(parsed.eventType).toBe('security_violation');\r\n        expect(parsed.severity).toBe('high');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Transports', () => {\r\n    describe('FileTransport', () => {\r\n      it('should create file transport with configuration', () => {\r\n        const config = {\r\n          name: 'test-file',\r\n          type: 'file',\r\n          enabled: true,\r\n          level: 'info' as any,\r\n          options: {\r\n            filename: 'test.log',\r\n            maxSize: '10m',\r\n            maxFiles: 5,\r\n            tailable: true,\r\n            zippedArchive: true,\r\n          },\r\n        };\r\n\r\n        expect(() => {\r\n          const transport = new FileTransport(config);\r\n          expect(transport).toBeDefined();\r\n          expect(transport.getName()).toBe('file');\r\n        }).not.toThrow();\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Configuration', () => {\r\n    it('should load logging configuration', () => {\r\n      const config = configService.get('logging');\r\n      expect(config).toBeDefined();\r\n      expect(config).toHaveProperty('level');\r\n      expect(config).toHaveProperty('format');\r\n      expect(config).toHaveProperty('console');\r\n      expect(config).toHaveProperty('file');\r\n      expect(config).toHaveProperty('correlation');\r\n      expect(config).toHaveProperty('masking');\r\n    });\r\n  });\r\n});"], "version": 3}