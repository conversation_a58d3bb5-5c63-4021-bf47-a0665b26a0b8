9b9552598c45f6386688c8f85eb2d609
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const timestamp_value_object_1 = require("../../value-objects/timestamp.value-object");
describe('Timestamp', () => {
    describe('creation', () => {
        it('should create timestamp from Date object', () => {
            const date = new Date('2023-01-01T12:00:00.000Z');
            const timestamp = timestamp_value_object_1.Timestamp.fromDate(date);
            expect(timestamp.date.getTime()).toBe(date.getTime());
            expect(timestamp.precision).toBe('milliseconds');
            expect(timestamp.timezone).toBe('UTC');
        });
        it('should create timestamp from milliseconds', () => {
            const milliseconds = 1672574400000; // 2023-01-01T12:00:00.000Z
            const timestamp = timestamp_value_object_1.Timestamp.fromMilliseconds(milliseconds);
            expect(timestamp.toMilliseconds()).toBe(milliseconds);
            expect(timestamp.precision).toBe('milliseconds');
        });
        it('should create timestamp from seconds', () => {
            const seconds = 1672574400; // 2023-01-01T12:00:00.000Z
            const timestamp = timestamp_value_object_1.Timestamp.fromSeconds(seconds);
            expect(timestamp.toSeconds()).toBe(seconds);
            expect(timestamp.toMilliseconds()).toBe(seconds * 1000);
        });
        it('should create timestamp from ISO string', () => {
            const isoString = '2023-01-01T12:00:00.000Z';
            const timestamp = timestamp_value_object_1.Timestamp.fromISOString(isoString);
            expect(timestamp.toISOString()).toBe(isoString);
        });
        it('should create current timestamp', () => {
            const before = Date.now();
            const timestamp = timestamp_value_object_1.Timestamp.now();
            const after = Date.now();
            const timestampMs = timestamp.toMilliseconds();
            expect(timestampMs).toBeGreaterThanOrEqual(before);
            expect(timestampMs).toBeLessThanOrEqual(after);
        });
        it('should create timestamp with custom precision and timezone', () => {
            const date = new Date();
            const timestamp = new timestamp_value_object_1.Timestamp(date, 'microseconds', 'America/New_York');
            expect(timestamp.precision).toBe('microseconds');
            expect(timestamp.timezone).toBe('America/New_York');
        });
    });
    describe('validation', () => {
        it('should throw error for null value', () => {
            expect(() => new timestamp_value_object_1.Timestamp(null)).toThrow('Timestamp cannot be null or undefined');
        });
        it('should throw error for undefined value', () => {
            expect(() => new timestamp_value_object_1.Timestamp(undefined)).toThrow('Timestamp cannot be null or undefined');
        });
        it('should throw error for invalid date string', () => {
            expect(() => new timestamp_value_object_1.Timestamp('invalid-date')).toThrow('Invalid timestamp string');
        });
        it('should throw error for invalid Date object', () => {
            expect(() => new timestamp_value_object_1.Timestamp(new Date('invalid'))).toThrow('Timestamp contains an invalid date');
        });
        it('should throw error for date before 1970', () => {
            expect(() => new timestamp_value_object_1.Timestamp(new Date('1969-12-31T23:59:59.999Z'))).toThrow('Timestamp must be between 1970 and 2100');
        });
        it('should throw error for date after 2100', () => {
            expect(() => new timestamp_value_object_1.Timestamp(new Date('2101-01-01T00:00:00.000Z'))).toThrow('Timestamp must be between 1970 and 2100');
        });
        it('should throw error for invalid precision', () => {
            expect(() => new timestamp_value_object_1.Timestamp(new Date(), 'invalid')).toThrow('Precision must be either "milliseconds" or "microseconds"');
        });
        it('should throw error for empty timezone', () => {
            expect(() => new timestamp_value_object_1.Timestamp(new Date(), 'milliseconds', '')).toThrow('Timezone must be a non-empty string');
        });
    });
    describe('static validation methods', () => {
        it('should try parse valid date string', () => {
            const result = timestamp_value_object_1.Timestamp.tryParse('2023-01-01T12:00:00.000Z');
            expect(result).not.toBeNull();
            expect(result.toISOString()).toBe('2023-01-01T12:00:00.000Z');
        });
        it('should return null for invalid date in tryParse', () => {
            const result = timestamp_value_object_1.Timestamp.tryParse('invalid-date');
            expect(result).toBeNull();
        });
        it('should try parse Date object', () => {
            const date = new Date('2023-01-01T12:00:00.000Z');
            const result = timestamp_value_object_1.Timestamp.tryParse(date);
            expect(result).not.toBeNull();
            expect(result.date.getTime()).toBe(date.getTime());
        });
        it('should try parse number', () => {
            const milliseconds = 1672574400000;
            const result = timestamp_value_object_1.Timestamp.tryParse(milliseconds);
            expect(result).not.toBeNull();
            expect(result.toMilliseconds()).toBe(milliseconds);
        });
    });
    describe('arithmetic operations', () => {
        let baseTimestamp;
        beforeEach(() => {
            baseTimestamp = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T12:00:00.000Z');
        });
        it('should add milliseconds', () => {
            const result = baseTimestamp.addMilliseconds(1000);
            expect(result.toISOString()).toBe('2023-01-01T12:00:01.000Z');
        });
        it('should add seconds', () => {
            const result = baseTimestamp.addSeconds(60);
            expect(result.toISOString()).toBe('2023-01-01T12:01:00.000Z');
        });
        it('should add minutes', () => {
            const result = baseTimestamp.addMinutes(30);
            expect(result.toISOString()).toBe('2023-01-01T12:30:00.000Z');
        });
        it('should add hours', () => {
            const result = baseTimestamp.addHours(2);
            expect(result.toISOString()).toBe('2023-01-01T14:00:00.000Z');
        });
        it('should add days', () => {
            const result = baseTimestamp.addDays(1);
            expect(result.toISOString()).toBe('2023-01-02T12:00:00.000Z');
        });
        it('should subtract milliseconds', () => {
            const result = baseTimestamp.subtractMilliseconds(1000);
            expect(result.toISOString()).toBe('2023-01-01T11:59:59.000Z');
        });
        it('should subtract seconds', () => {
            const result = baseTimestamp.subtractSeconds(60);
            expect(result.toISOString()).toBe('2023-01-01T11:59:00.000Z');
        });
        it('should subtract minutes', () => {
            const result = baseTimestamp.subtractMinutes(30);
            expect(result.toISOString()).toBe('2023-01-01T11:30:00.000Z');
        });
        it('should subtract hours', () => {
            const result = baseTimestamp.subtractHours(2);
            expect(result.toISOString()).toBe('2023-01-01T10:00:00.000Z');
        });
        it('should subtract days', () => {
            const result = baseTimestamp.subtractDays(1);
            expect(result.toISOString()).toBe('2022-12-31T12:00:00.000Z');
        });
    });
    describe('difference calculations', () => {
        let timestamp1;
        let timestamp2;
        beforeEach(() => {
            timestamp1 = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T12:00:00.000Z');
            timestamp2 = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T14:30:45.500Z');
        });
        it('should calculate difference in milliseconds', () => {
            const diff = timestamp2.differenceInMilliseconds(timestamp1);
            expect(diff).toBe(9045500); // 2.5 hours + 45.5 seconds
        });
        it('should calculate difference in seconds', () => {
            const diff = timestamp2.differenceInSeconds(timestamp1);
            expect(diff).toBe(9045); // 2.5 hours + 45 seconds
        });
        it('should calculate difference in minutes', () => {
            const diff = timestamp2.differenceInMinutes(timestamp1);
            expect(diff).toBe(150); // 2.5 hours
        });
        it('should calculate difference in hours', () => {
            const diff = timestamp2.differenceInHours(timestamp1);
            expect(diff).toBe(2);
        });
        it('should calculate difference in days', () => {
            const timestamp3 = timestamp_value_object_1.Timestamp.fromISOString('2023-01-03T12:00:00.000Z');
            const diff = timestamp3.differenceInDays(timestamp1);
            expect(diff).toBe(2);
        });
        it('should handle negative differences', () => {
            const diff = timestamp1.differenceInMilliseconds(timestamp2);
            expect(diff).toBe(-9045500);
        });
    });
    describe('comparison operations', () => {
        let timestamp1;
        let timestamp2;
        let timestamp3;
        beforeEach(() => {
            timestamp1 = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T12:00:00.000Z');
            timestamp2 = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T14:00:00.000Z');
            timestamp3 = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T12:00:00.000Z');
        });
        it('should check if before', () => {
            expect(timestamp1.isBefore(timestamp2)).toBe(true);
            expect(timestamp2.isBefore(timestamp1)).toBe(false);
            expect(timestamp1.isBefore(timestamp3)).toBe(false);
        });
        it('should check if after', () => {
            expect(timestamp2.isAfter(timestamp1)).toBe(true);
            expect(timestamp1.isAfter(timestamp2)).toBe(false);
            expect(timestamp1.isAfter(timestamp3)).toBe(false);
        });
        it('should check if same', () => {
            expect(timestamp1.isSame(timestamp3)).toBe(true);
            expect(timestamp1.isSame(timestamp2)).toBe(false);
        });
        it('should check if between (inclusive)', () => {
            const middle = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T13:00:00.000Z');
            expect(middle.isBetween(timestamp1, timestamp2, true)).toBe(true);
            expect(timestamp1.isBetween(timestamp1, timestamp2, true)).toBe(true);
            expect(timestamp2.isBetween(timestamp1, timestamp2, true)).toBe(true);
        });
        it('should check if between (exclusive)', () => {
            const middle = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T13:00:00.000Z');
            expect(middle.isBetween(timestamp1, timestamp2, false)).toBe(true);
            expect(timestamp1.isBetween(timestamp1, timestamp2, false)).toBe(false);
            expect(timestamp2.isBetween(timestamp1, timestamp2, false)).toBe(false);
        });
    });
    describe('day boundaries', () => {
        it('should get start of day', () => {
            const timestamp = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T14:30:45.123Z');
            const startOfDay = timestamp.startOfDay();
            expect(startOfDay.toISOString()).toBe('2023-01-01T00:00:00.000Z');
        });
        it('should get end of day', () => {
            const timestamp = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T14:30:45.123Z');
            const endOfDay = timestamp.endOfDay();
            expect(endOfDay.toISOString()).toBe('2023-01-01T23:59:59.999Z');
        });
    });
    describe('equality and comparison', () => {
        it('should be equal to itself', () => {
            const timestamp = timestamp_value_object_1.Timestamp.now();
            expect(timestamp.equals(timestamp)).toBe(true);
        });
        it('should be equal to timestamp with same value and properties', () => {
            const date = new Date('2023-01-01T12:00:00.000Z');
            const timestamp1 = new timestamp_value_object_1.Timestamp(date, 'milliseconds', 'UTC');
            const timestamp2 = new timestamp_value_object_1.Timestamp(date, 'milliseconds', 'UTC');
            expect(timestamp1.equals(timestamp2)).toBe(true);
        });
        it('should not be equal to timestamp with different value', () => {
            const timestamp1 = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T12:00:00.000Z');
            const timestamp2 = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T13:00:00.000Z');
            expect(timestamp1.equals(timestamp2)).toBe(false);
        });
        it('should not be equal to timestamp with different precision', () => {
            const date = new Date('2023-01-01T12:00:00.000Z');
            const timestamp1 = new timestamp_value_object_1.Timestamp(date, 'milliseconds', 'UTC');
            const timestamp2 = new timestamp_value_object_1.Timestamp(date, 'microseconds', 'UTC');
            expect(timestamp1.equals(timestamp2)).toBe(false);
        });
        it('should not be equal to timestamp with different timezone', () => {
            const date = new Date('2023-01-01T12:00:00.000Z');
            const timestamp1 = new timestamp_value_object_1.Timestamp(date, 'milliseconds', 'UTC');
            const timestamp2 = new timestamp_value_object_1.Timestamp(date, 'milliseconds', 'America/New_York');
            expect(timestamp1.equals(timestamp2)).toBe(false);
        });
        it('should not be equal to null or undefined', () => {
            const timestamp = timestamp_value_object_1.Timestamp.now();
            expect(timestamp.equals(null)).toBe(false);
            expect(timestamp.equals(undefined)).toBe(false);
        });
        it('should not be equal to non-Timestamp object', () => {
            const timestamp = timestamp_value_object_1.Timestamp.now();
            expect(timestamp.equals({})).toBe(false);
        });
    });
    describe('string representations', () => {
        let timestamp;
        beforeEach(() => {
            timestamp = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T12:00:00.000Z');
        });
        it('should convert to ISO string', () => {
            expect(timestamp.toISOString()).toBe('2023-01-01T12:00:00.000Z');
        });
        it('should convert to UTC string', () => {
            const utcString = timestamp.toUTCString();
            expect(utcString).toBe('Sun, 01 Jan 2023 12:00:00 GMT');
        });
        it('should convert to locale string', () => {
            const localeString = timestamp.toLocaleString('en-US');
            expect(localeString).toContain('2023');
        });
        it('should convert to string (ISO format)', () => {
            expect(timestamp.toString()).toBe('2023-01-01T12:00:00.000Z');
        });
    });
    describe('serialization', () => {
        it('should convert to JSON', () => {
            const timestamp = timestamp_value_object_1.Timestamp.fromISOString('2023-01-01T12:00:00.000Z');
            const json = timestamp.toJSON();
            expect(json.value).toBe('2023-01-01T12:00:00.000Z');
            expect(json.milliseconds).toBe(1672574400000);
            expect(json.seconds).toBe(1672574400);
            expect(json.precision).toBe('milliseconds');
            expect(json.timezone).toBe('UTC');
            expect(json.type).toBe('Timestamp');
        });
        it('should create from JSON', () => {
            const json = {
                value: '2023-01-01T12:00:00.000Z',
                precision: 'milliseconds',
                timezone: 'UTC'
            };
            const timestamp = timestamp_value_object_1.Timestamp.fromJSON(json);
            expect(timestamp.toISOString()).toBe('2023-01-01T12:00:00.000Z');
            expect(timestamp.precision).toBe('milliseconds');
            expect(timestamp.timezone).toBe('UTC');
        });
        it('should create from JSON with milliseconds', () => {
            const json = {
                milliseconds: 1672574400000,
                precision: 'microseconds',
                timezone: 'America/New_York'
            };
            const timestamp = timestamp_value_object_1.Timestamp.fromJSON(json);
            expect(timestamp.toMilliseconds()).toBe(1672574400000);
            expect(timestamp.precision).toBe('microseconds');
            expect(timestamp.timezone).toBe('America/New_York');
        });
    });
    describe('immutability', () => {
        it('should be immutable after creation', () => {
            const timestamp = timestamp_value_object_1.Timestamp.now();
            const originalValue = timestamp.date.getTime();
            // Attempt to modify (should not work due to readonly)
            expect(() => {
                timestamp._value = new Date();
            }).not.toThrow(); // TypeScript prevents this, but runtime doesn't throw
            // Value should remain unchanged
            expect(timestamp.date.getTime()).toBe(originalValue);
        });
        it('should be frozen', () => {
            const timestamp = timestamp_value_object_1.Timestamp.now();
            expect(Object.isFrozen(timestamp)).toBe(true);
        });
        it('should return new instances for arithmetic operations', () => {
            const original = timestamp_value_object_1.Timestamp.now();
            const modified = original.addHours(1);
            expect(modified).not.toBe(original);
            expect(modified.differenceInHours(original)).toBe(1);
        });
    });
    describe('edge cases', () => {
        it('should handle leap year dates', () => {
            const leapYear = timestamp_value_object_1.Timestamp.fromISOString('2020-02-29T12:00:00.000Z');
            expect(leapYear.isValid()).toBe(true);
        });
        it('should handle daylight saving time transitions', () => {
            // This test assumes the system can handle DST transitions
            const timestamp = new timestamp_value_object_1.Timestamp('2023-03-12T07:00:00.000Z', 'milliseconds', 'America/New_York');
            expect(timestamp.isValid()).toBe(true);
        });
        it('should handle microsecond precision', () => {
            const timestamp = new timestamp_value_object_1.Timestamp(new Date(), 'microseconds', 'UTC');
            expect(timestamp.precision).toBe('microseconds');
        });
        it('should handle different timezones', () => {
            const utc = new timestamp_value_object_1.Timestamp(new Date(), 'milliseconds', 'UTC');
            const ny = new timestamp_value_object_1.Timestamp(new Date(), 'milliseconds', 'America/New_York');
            expect(utc.timezone).toBe('UTC');
            expect(ny.timezone).toBe('America/New_York');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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