f95eddf88a9a5f9231d8248af8716b9f
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TestDataService_1;
var _a, _b, _c, _d, _e, _f, _g, _h, _j;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestDataService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const faker_1 = require("@faker-js/faker");
const workflow_execution_entity_1 = require("../../workflow-execution/entities/workflow-execution.entity");
const workflow_execution_context_entity_1 = require("../../workflow-execution/entities/workflow-execution-context.entity");
const workflow_template_entity_1 = require("../../templates/entities/workflow-template.entity");
const metric_snapshot_entity_1 = require("../../monitoring/entities/metric-snapshot.entity");
const performance_metric_entity_1 = require("../../monitoring/entities/performance-metric.entity");
const health_check_result_entity_1 = require("../../monitoring/entities/health-check-result.entity");
const alert_rule_entity_1 = require("../../monitoring/entities/alert-rule.entity");
const alert_incident_entity_1 = require("../../monitoring/entities/alert-incident.entity");
/**
 * Test Data Service
 *
 * Comprehensive test data generation and management providing:
 * - Realistic test data generation using Faker.js
 * - Database seeding and cleanup utilities
 * - Test fixture management and versioning
 * - Performance test data generation
 * - Cross-module test data consistency
 * - Test data validation and integrity checks
 */
let TestDataService = TestDataService_1 = class TestDataService {
    constructor(executionRepository, contextRepository, templateRepository, metricSnapshotRepository, performanceMetricRepository, healthCheckRepository, alertRuleRepository, alertIncidentRepository, dataSource) {
        this.executionRepository = executionRepository;
        this.contextRepository = contextRepository;
        this.templateRepository = templateRepository;
        this.metricSnapshotRepository = metricSnapshotRepository;
        this.performanceMetricRepository = performanceMetricRepository;
        this.healthCheckRepository = healthCheckRepository;
        this.alertRuleRepository = alertRuleRepository;
        this.alertIncidentRepository = alertIncidentRepository;
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(TestDataService_1.name);
        this.testDataCache = new Map();
    }
    /**
     * Generate test workflow templates
     */
    async generateWorkflowTemplates(count = 10) {
        const templates = [];
        for (let i = 0; i < count; i++) {
            const template = this.templateRepository.create({
                name: faker_1.faker.company.name() + ' Workflow',
                description: faker_1.faker.lorem.paragraph(),
                category: faker_1.faker.helpers.arrayElement(['reporting', 'monitoring', 'automation', 'compliance']),
                version: faker_1.faker.system.semver(),
                isActive: faker_1.faker.datatype.boolean(0.8),
                definition: this.generateWorkflowDefinition(),
                metadata: {
                    author: faker_1.faker.person.fullName(),
                    tags: faker_1.faker.helpers.arrayElements(['test', 'automation', 'reporting', 'monitoring'], { min: 1, max: 3 }),
                    estimatedDuration: faker_1.faker.number.int({ min: 60000, max: 3600000 }),
                    complexity: faker_1.faker.helpers.arrayElement(['low', 'medium', 'high']),
                },
                createdBy: 'test-user',
                updatedBy: 'test-user',
            });
            templates.push(template);
        }
        return await this.templateRepository.save(templates);
    }
    /**
     * Generate test workflow executions
     */
    async generateWorkflowExecutions(templates, count = 50) {
        const executions = [];
        const statusDistribution = ['completed', 'failed', 'running', 'pending', 'cancelled'];
        for (let i = 0; i < count; i++) {
            const template = faker_1.faker.helpers.arrayElement(templates);
            const status = faker_1.faker.helpers.arrayElement(statusDistribution);
            const startedAt = faker_1.faker.date.recent({ days: 30 });
            const duration = status === 'completed' || status === 'failed'
                ? faker_1.faker.number.int({ min: 1000, max: 300000 })
                : null;
            const completedAt = duration ? new Date(startedAt.getTime() + duration) : null;
            const execution = this.executionRepository.create({
                templateId: template.id,
                executionName: `${template.name} - ${faker_1.faker.date.recent().toISOString()}`,
                status: status,
                priority: faker_1.faker.helpers.arrayElement(['low', 'medium', 'high', 'critical']),
                startedAt: status !== 'pending' ? startedAt : null,
                completedAt,
                duration,
                inputData: this.generateExecutionInputData(),
                outputData: status === 'completed' ? this.generateExecutionOutputData() : null,
                contextData: this.generateExecutionContextData(),
                executionState: this.generateExecutionState(template.definition, status),
                performanceMetrics: this.generatePerformanceMetrics(template.definition),
                errorDetails: status === 'failed' ? this.generateErrorDetails() : null,
                retryConfig: faker_1.faker.datatype.boolean(0.3) ? this.generateRetryConfig() : null,
                executionConfig: this.generateExecutionConfig(),
                triggeredBy: faker_1.faker.helpers.arrayElement(['test-admin', 'test-user', 'system']),
                triggerSource: faker_1.faker.helpers.arrayElement(['manual', 'scheduled', 'event', 'api']),
                triggerData: this.generateTriggerData(),
            });
            executions.push(execution);
        }
        return await this.executionRepository.save(executions);
    }
    /**
     * Generate test execution contexts
     */
    async generateExecutionContexts(executions, contextsPerExecution = 3) {
        const contexts = [];
        for (const execution of executions) {
            for (let i = 0; i < contextsPerExecution; i++) {
                const contextType = faker_1.faker.helpers.arrayElement([
                    'global', 'step', 'parallel_group', 'conditional_branch', 'loop'
                ]);
                const context = this.contextRepository.create({
                    executionId: execution.id,
                    contextType: contextType,
                    contextKey: `${contextType}_${i}`,
                    variables: this.generateContextVariables(),
                    stateData: this.generateContextStateData(contextType),
                    environmentData: this.generateEnvironmentData(),
                    metadata: this.generateContextMetadata(),
                    dataSize: faker_1.faker.number.int({ min: 1024, max: 1048576 }), // 1KB to 1MB
                    accessPattern: this.generateAccessPattern(),
                });
                contexts.push(context);
            }
        }
        return await this.contextRepository.save(contexts);
    }
    /**
     * Generate test metric snapshots
     */
    async generateMetricSnapshots(count = 100) {
        const snapshots = [];
        const metricNames = [
            'workflow_executions_total',
            'workflow_execution_duration',
            'workflow_step_duration',
            'system_cpu_usage',
            'system_memory_usage',
            'database_query_duration',
            'external_service_calls',
            'error_rate',
        ];
        for (let i = 0; i < count; i++) {
            const metricName = faker_1.faker.helpers.arrayElement(metricNames);
            const category = this.getMetricCategory(metricName);
            const snapshot = this.metricSnapshotRepository.create({
                metricName,
                category,
                value: this.generateMetricValue(metricName),
                labels: this.generateMetricLabels(metricName),
                timestamp: faker_1.faker.date.recent({ days: 7 }),
                aggregationType: faker_1.faker.helpers.arrayElement(['sum', 'avg', 'max', 'min', 'count']),
                aggregationWindow: faker_1.faker.helpers.arrayElement(['1m', '5m', '15m', '1h', '1d']),
                metadata: {
                    source: faker_1.faker.helpers.arrayElement(['workflow_engine', 'monitoring', 'system']),
                    version: '1.0.0',
                    environment: 'test',
                },
            });
            snapshots.push(snapshot);
        }
        return await this.metricSnapshotRepository.save(snapshots);
    }
    /**
     * Generate test performance metrics
     */
    async generatePerformanceMetricsData(count = 200) {
        const metrics = [];
        const components = ['workflow_engine', 'database', 'external_service', 'system'];
        const operations = ['execute', 'query', 'call', 'process'];
        for (let i = 0; i < count; i++) {
            const component = faker_1.faker.helpers.arrayElement(components);
            const operation = faker_1.faker.helpers.arrayElement(operations);
            const metric = this.performanceMetricRepository.create({
                metricName: `${component}_${operation}_duration`,
                component,
                operation,
                value: faker_1.faker.number.float({ min: 10, max: 5000, precision: 0.01 }),
                unit: faker_1.faker.helpers.arrayElement(['milliseconds', 'seconds', 'bytes', 'count']),
                labels: this.generatePerformanceLabels(component),
                metadata: this.generatePerformanceMetadata(),
                timestamp: faker_1.faker.date.recent({ days: 1 }),
                status: faker_1.faker.helpers.arrayElement(['normal', 'warning', 'critical']),
                anomalyScore: faker_1.faker.number.float({ min: 0, max: 1, precision: 0.01 }),
            });
            metrics.push(metric);
        }
        return await this.performanceMetricRepository.save(metrics);
    }
    /**
     * Generate test health check results
     */
    async generateHealthCheckResults(count = 50) {
        const results = [];
        const checkNames = [
            'database_connectivity',
            'redis_connectivity',
            'external_api_health',
            'disk_space',
            'memory_usage',
            'workflow_engine_health',
        ];
        for (let i = 0; i < count; i++) {
            const checkName = faker_1.faker.helpers.arrayElement(checkNames);
            const status = faker_1.faker.helpers.arrayElement(['healthy', 'degraded', 'unhealthy']);
            const result = this.healthCheckRepository.create({
                checkName,
                status: status,
                responseTime: faker_1.faker.number.int({ min: 1, max: 5000 }),
                message: this.generateHealthCheckMessage(status),
                details: this.generateHealthCheckDetails(checkName, status),
                timestamp: faker_1.faker.date.recent({ days: 1 }),
                critical: faker_1.faker.datatype.boolean(0.3),
                tags: faker_1.faker.helpers.arrayElements(['infrastructure', 'application', 'external'], { min: 1, max: 2 }),
                metadata: {
                    version: '1.0.0',
                    environment: 'test',
                    region: faker_1.faker.helpers.arrayElement(['us-east-1', 'us-west-2', 'eu-west-1']),
                },
            });
            results.push(result);
        }
        return await this.healthCheckRepository.save(results);
    }
    /**
     * Generate test alert rules
     */
    async generateAlertRules(count = 20) {
        const rules = [];
        for (let i = 0; i < count; i++) {
            const rule = this.alertRuleRepository.create({
                name: faker_1.faker.company.buzzPhrase(),
                description: faker_1.faker.lorem.sentence(),
                metricName: faker_1.faker.helpers.arrayElement([
                    'workflow_execution_duration',
                    'error_rate',
                    'system_cpu_usage',
                    'database_query_duration',
                ]),
                condition: this.generateAlertCondition(),
                severity: faker_1.faker.helpers.arrayElement(['info', 'warning', 'critical']),
                enabled: faker_1.faker.datatype.boolean(0.8),
                escalation: this.generateEscalationConfig(),
                recovery: this.generateRecoveryConfig(),
                suppression: this.generateSuppressionConfig(),
                metadata: this.generateAlertRuleMetadata(),
                createdBy: 'test-admin',
                updatedBy: 'test-admin',
            });
            rules.push(rule);
        }
        return await this.alertRuleRepository.save(rules);
    }
    /**
     * Generate test alert incidents
     */
    async generateAlertIncidents(alertRules, count = 30) {
        const incidents = [];
        for (let i = 0; i < count; i++) {
            const rule = faker_1.faker.helpers.arrayElement(alertRules);
            const status = faker_1.faker.helpers.arrayElement(['active', 'acknowledged', 'resolved', 'suppressed']);
            const startTime = faker_1.faker.date.recent({ days: 7 });
            const endTime = status === 'resolved'
                ? faker_1.faker.date.between({ from: startTime, to: new Date() })
                : null;
            const incident = this.alertIncidentRepository.create({
                ruleId: rule.id,
                status: status,
                severity: rule.severity,
                startTime,
                endTime,
                duration: endTime ? endTime.getTime() - startTime.getTime() : null,
                triggerValue: faker_1.faker.number.float({ min: 0, max: 100, precision: 0.01 }),
                description: faker_1.faker.lorem.sentence(),
                escalationLevel: faker_1.faker.number.int({ min: 0, max: 3 }),
                isAcknowledged: status === 'acknowledged',
                acknowledgedBy: status === 'acknowledged' ? 'test-user' : null,
                acknowledgedAt: status === 'acknowledged' ? faker_1.faker.date.recent() : null,
                resolvedBy: status === 'resolved' ? 'test-user' : null,
                resolvedAt: endTime,
                impactLevel: faker_1.faker.helpers.arrayElement(['low', 'medium', 'high', 'critical']),
                affectedServices: faker_1.faker.helpers.arrayElements(['workflow', 'monitoring', 'reporting'], { min: 1, max: 2 }),
                metadata: this.generateIncidentMetadata(),
            });
            incidents.push(incident);
        }
        return await this.alertIncidentRepository.save(incidents);
    }
    /**
     * Seed complete test dataset
     */
    async seedTestData(options) {
        const { templates: templateCount = 10, executions: executionCount = 50, contexts: contextCount = 3, metrics: metricCount = 100, healthChecks: healthCheckCount = 50, alertRules: alertRuleCount = 20, incidents: incidentCount = 30, } = options || {};
        this.logger.log('Starting test data seeding...');
        // Generate data in dependency order
        const templates = await this.generateWorkflowTemplates(templateCount);
        const executions = await this.generateWorkflowExecutions(templates, executionCount);
        const contexts = await this.generateExecutionContexts(executions, contextCount);
        const metricSnapshots = await this.generateMetricSnapshots(metricCount);
        const performanceMetrics = await this.generatePerformanceMetricsData(metricCount * 2);
        const healthChecks = await this.generateHealthCheckResults(healthCheckCount);
        const alertRules = await this.generateAlertRules(alertRuleCount);
        const incidents = await this.generateAlertIncidents(alertRules, incidentCount);
        this.logger.log('Test data seeding completed successfully');
        return {
            templates,
            executions,
            contexts,
            metricSnapshots,
            performanceMetrics,
            healthChecks,
            alertRules,
            incidents,
        };
    }
    /**
     * Clean all test data
     */
    async cleanTestData() {
        this.logger.log('Cleaning test data...');
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            // Clean in reverse dependency order
            await queryRunner.manager.delete(alert_incident_entity_1.AlertIncident, {});
            await queryRunner.manager.delete(alert_rule_entity_1.AlertRule, {});
            await queryRunner.manager.delete(health_check_result_entity_1.HealthCheckResult, {});
            await queryRunner.manager.delete(performance_metric_entity_1.PerformanceMetric, {});
            await queryRunner.manager.delete(metric_snapshot_entity_1.MetricSnapshot, {});
            await queryRunner.manager.delete(workflow_execution_context_entity_1.WorkflowExecutionContext, {});
            await queryRunner.manager.delete(workflow_execution_entity_1.WorkflowExecution, {});
            await queryRunner.manager.delete(workflow_template_entity_1.WorkflowTemplate, {});
            await queryRunner.commitTransaction();
            this.logger.log('Test data cleaned successfully');
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error('Failed to clean test data', error.stack);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    /**
     * Get test fixture by name
     */
    getTestFixture(fixtureName) {
        if (this.testDataCache.has(fixtureName)) {
            return this.testDataCache.get(fixtureName);
        }
        const fixtures = {
            simpleWorkflowTemplate: {
                name: 'Simple Test Workflow',
                description: 'A simple workflow for testing',
                definition: {
                    steps: [
                        { id: 'step1', type: 'notification', config: { message: 'Hello World' } },
                        { id: 'step2', type: 'validation', config: { rules: [{ type: 'required' }] } },
                    ],
                },
            },
            complexWorkflowTemplate: {
                name: 'Complex Test Workflow',
                description: 'A complex workflow with parallel and conditional steps',
                definition: {
                    steps: [
                        {
                            id: 'parallel1',
                            type: 'parallel',
                            config: {
                                steps: [
                                    { id: 'p1', type: 'notification', config: { message: 'Parallel 1' } },
                                    { id: 'p2', type: 'notification', config: { message: 'Parallel 2' } },
                                ],
                            },
                        },
                        {
                            id: 'conditional1',
                            type: 'conditional',
                            config: {
                                condition: { type: 'comparison', left: 'status', operator: 'equals', right: 'success' },
                                trueBranch: [{ id: 'success', type: 'notification', config: { message: 'Success' } }],
                                falseBranch: [{ id: 'failure', type: 'notification', config: { message: 'Failure' } }],
                            },
                        },
                    ],
                },
            },
            testUser: {
                id: 'test-user-id',
                email: '<EMAIL>',
                role: 'user',
                permissions: ['workflow.view', 'workflow.execute'],
            },
            testAdmin: {
                id: 'test-admin-id',
                email: '<EMAIL>',
                role: 'admin',
                permissions: ['*'],
            },
        };
        const fixture = fixtures[fixtureName];
        if (fixture) {
            this.testDataCache.set(fixtureName, fixture);
        }
        return fixture;
    }
    /**
     * Private helper methods for data generation
     */
    generateWorkflowDefinition() {
        const stepTypes = ['notification', 'validation', 'data_transformation', 'external_service'];
        const stepCount = faker_1.faker.number.int({ min: 2, max: 8 });
        const steps = [];
        for (let i = 0; i < stepCount; i++) {
            const stepType = faker_1.faker.helpers.arrayElement(stepTypes);
            steps.push({
                id: `step_${i + 1}`,
                type: stepType,
                config: this.generateStepConfig(stepType),
                outputMapping: faker_1.faker.datatype.boolean(0.5) ? { result: 'output.data' } : undefined,
            });
        }
        return {
            steps,
            inputs: {
                required: ['data'],
                optional: ['options'],
                types: { data: 'object', options: 'object' },
            },
            outputs: {
                result: 'object',
                status: 'string',
            },
        };
    }
    generateStepConfig(stepType) {
        const configs = {
            notification: {
                type: faker_1.faker.helpers.arrayElement(['email', 'slack', 'sms']),
                recipients: [faker_1.faker.internet.email()],
                message: faker_1.faker.lorem.sentence(),
            },
            validation: {
                rules: [
                    { type: 'required', field: 'data' },
                    { type: 'type', field: 'data', expectedType: 'object' },
                ],
            },
            data_transformation: {
                transformation: {
                    type: 'map',
                    function: 'item => ({ ...item, processed: true })',
                },
            },
            external_service: {
                service: faker_1.faker.company.name(),
                endpoint: faker_1.faker.internet.url(),
                method: faker_1.faker.helpers.arrayElement(['GET', 'POST', 'PUT']),
                timeout: faker_1.faker.number.int({ min: 5000, max: 30000 }),
            },
        };
        return configs[stepType] || {};
    }
    generateExecutionInputData() {
        return {
            data: {
                reportType: faker_1.faker.helpers.arrayElement(['monthly', 'weekly', 'daily']),
                period: faker_1.faker.date.recent().toISOString().substring(0, 7),
                recipients: [faker_1.faker.internet.email(), faker_1.faker.internet.email()],
                includeCharts: faker_1.faker.datatype.boolean(),
                format: faker_1.faker.helpers.arrayElement(['pdf', 'excel', 'csv']),
            },
            options: {
                priority: faker_1.faker.helpers.arrayElement(['low', 'medium', 'high']),
                timeout: faker_1.faker.number.int({ min: 60000, max: 3600000 }),
                retryOnFailure: faker_1.faker.datatype.boolean(),
            },
        };
    }
    generateExecutionOutputData() {
        return {
            result: {
                reportId: faker_1.faker.string.uuid(),
                generatedAt: faker_1.faker.date.recent().toISOString(),
                fileSize: faker_1.faker.number.int({ min: 1024, max: 10485760 }),
                downloadUrl: faker_1.faker.internet.url(),
            },
            status: 'completed',
            metrics: {
                processingTime: faker_1.faker.number.int({ min: 1000, max: 300000 }),
                recordsProcessed: faker_1.faker.number.int({ min: 100, max: 100000 }),
                errorsEncountered: faker_1.faker.number.int({ min: 0, max: 10 }),
            },
        };
    }
    generateExecutionContextData() {
        return {
            environment: faker_1.faker.helpers.arrayElement(['development', 'staging', 'production']),
            correlationId: faker_1.faker.string.uuid(),
            userPreferences: {
                timezone: faker_1.faker.location.timeZone(),
                locale: faker_1.faker.helpers.arrayElement(['en-US', 'en-GB', 'fr-FR', 'de-DE']),
                dateFormat: faker_1.faker.helpers.arrayElement(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']),
            },
            systemInfo: {
                version: faker_1.faker.system.semver(),
                buildNumber: faker_1.faker.number.int({ min: 1000, max: 9999 }),
                deploymentId: faker_1.faker.string.uuid(),
            },
        };
    }
    generateExecutionState(definition, status) {
        const steps = definition.steps || [];
        const stepStates = {};
        const completedSteps = [];
        const failedSteps = [];
        const pendingSteps = [];
        steps.forEach((step, index) => {
            let stepStatus;
            if (status === 'completed') {
                stepStatus = 'completed';
                completedSteps.push(step.id);
            }
            else if (status === 'failed' && index < steps.length - 1) {
                stepStatus = 'completed';
                completedSteps.push(step.id);
            }
            else if (status === 'failed' && index === steps.length - 1) {
                stepStatus = 'failed';
                failedSteps.push(step.id);
            }
            else if (status === 'running' && index <= steps.length / 2) {
                stepStatus = 'completed';
                completedSteps.push(step.id);
            }
            else if (status === 'running' && index === Math.floor(steps.length / 2) + 1) {
                stepStatus = 'running';
            }
            else {
                stepStatus = 'pending';
                pendingSteps.push(step.id);
            }
            stepStates[step.id] = {
                status: stepStatus,
                startedAt: stepStatus !== 'pending' ? faker_1.faker.date.recent().toISOString() : undefined,
                completedAt: stepStatus === 'completed' || stepStatus === 'failed'
                    ? faker_1.faker.date.recent().toISOString() : undefined,
                duration: stepStatus === 'completed' || stepStatus === 'failed'
                    ? faker_1.faker.number.int({ min: 100, max: 10000 }) : undefined,
                attempts: faker_1.faker.number.int({ min: 1, max: 3 }),
                output: stepStatus === 'completed' ? { success: true, data: 'test' } : undefined,
                lastError: stepStatus === 'failed' ? faker_1.faker.lorem.sentence() : undefined,
            };
        });
        return {
            currentStep: status === 'running' ? steps[Math.floor(steps.length / 2)]?.id : undefined,
            completedSteps,
            failedSteps,
            skippedSteps: [],
            pendingSteps,
            stepStates,
        };
    }
};
exports.TestDataService = TestDataService;
exports.TestDataService = TestDataService = TestDataService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(workflow_execution_entity_1.WorkflowExecution)),
    __param(1, (0, typeorm_1.InjectRepository)(workflow_execution_context_entity_1.WorkflowExecutionContext)),
    __param(2, (0, typeorm_1.InjectRepository)(workflow_template_entity_1.WorkflowTemplate)),
    __param(3, (0, typeorm_1.InjectRepository)(metric_snapshot_entity_1.MetricSnapshot)),
    __param(4, (0, typeorm_1.InjectRepository)(performance_metric_entity_1.PerformanceMetric)),
    __param(5, (0, typeorm_1.InjectRepository)(health_check_result_entity_1.HealthCheckResult)),
    __param(6, (0, typeorm_1.InjectRepository)(alert_rule_entity_1.AlertRule)),
    __param(7, (0, typeorm_1.InjectRepository)(alert_incident_entity_1.AlertIncident)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _d : Object, typeof (_e = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _e : Object, typeof (_f = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _f : Object, typeof (_g = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _g : Object, typeof (_h = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _h : Object, typeof (_j = typeof typeorm_2.DataSource !== "undefined" && typeorm_2.DataSource) === "function" ? _j : Object])
], TestDataService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************