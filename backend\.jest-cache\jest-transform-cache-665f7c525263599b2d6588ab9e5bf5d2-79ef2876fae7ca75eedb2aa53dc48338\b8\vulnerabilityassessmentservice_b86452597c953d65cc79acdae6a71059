68d456e50b753bcc044a61197cd2bf65
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var VulnerabilityAssessmentService_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityAssessmentService = void 0;
const common_1 = require("@nestjs/common");
const confidence_level_enum_1 = require("../../domain/enums/confidence-level.enum");
const critical_vulnerability_specification_1 = require("../../domain/specifications/critical-vulnerability.specification");
const domain_event_publisher_1 = require("../../../shared-kernel/domain/domain-event-publisher");
const logger_1 = require("../../../shared-kernel/infrastructure/logger");
/**
 * Vulnerability Assessment Application Service
 *
 * Provides comprehensive vulnerability assessment capabilities including
 * risk analysis, exploit assessment, threat intelligence integration,
 * and business impact evaluation.
 *
 * Key responsibilities:
 * - Comprehensive vulnerability risk assessment
 * - Exploit analysis and threat intelligence integration
 * - Business and compliance impact evaluation
 * - Prioritization and recommendation generation
 * - Bulk assessment processing
 */
let VulnerabilityAssessmentService = VulnerabilityAssessmentService_1 = class VulnerabilityAssessmentService {
    constructor(eventPublisher) {
        this.eventPublisher = eventPublisher;
        this.logger = new logger_1.Logger(VulnerabilityAssessmentService_1.name);
        this.criticalVulnSpec = new critical_vulnerability_specification_1.CriticalVulnerabilitySpecification();
    }
    /**
     * Assess a single vulnerability
     */
    async assessVulnerability(request) {
        const startTime = Date.now();
        const vulnerability = request.vulnerability;
        this.logger.info('Starting vulnerability assessment', {
            vulnerabilityId: vulnerability.id.toString(),
            cveId: vulnerability.cveId,
            severity: vulnerability.severity,
        });
        try {
            // Perform risk analysis
            const riskAnalysis = await this.performRiskAnalysis(vulnerability, request.context);
            // Perform exploit analysis if requested
            let exploitAnalysis;
            if (request.options?.includeExploitAnalysis !== false) {
                exploitAnalysis = await this.performExploitAnalysis(vulnerability);
            }
            // Gather threat intelligence if requested
            let threatIntelligence;
            if (request.options?.includeThreatIntelligence) {
                threatIntelligence = await this.gatherThreatIntelligence(vulnerability);
            }
            // Assess business impact if requested
            let businessImpact;
            if (request.options?.includeBusinessImpact) {
                businessImpact = await this.assessBusinessImpact(vulnerability, request.context);
            }
            // Assess compliance impact if requested
            let complianceImpact;
            if (request.options?.includeComplianceImpact) {
                complianceImpact = await this.assessComplianceImpact(vulnerability, request.context);
            }
            // Generate recommendations
            const recommendations = await this.generateRecommendations(vulnerability, riskAnalysis, exploitAnalysis, request.context);
            // Calculate overall assessment summary
            const summary = this.calculateAssessmentSummary(vulnerability, riskAnalysis, exploitAnalysis, businessImpact);
            const result = {
                success: true,
                vulnerabilityId: vulnerability.id.toString(),
                summary,
                riskAnalysis,
                exploitAnalysis,
                threatIntelligence,
                businessImpact,
                complianceImpact,
                recommendations,
                metadata: {
                    assessmentDate: new Date(),
                    assessmentDuration: Date.now() - startTime,
                    dataSourcesUsed: this.getDataSourcesUsed(request.options),
                    assessmentVersion: '1.0',
                    assessor: 'VulnerabilityAssessmentService',
                },
            };
            this.logger.info('Vulnerability assessment completed', {
                vulnerabilityId: vulnerability.id.toString(),
                overallRisk: summary.overallRisk,
                priority: summary.priority,
                duration: result.metadata.assessmentDuration,
            });
            return result;
        }
        catch (error) {
            this.logger.error('Vulnerability assessment failed', {
                vulnerabilityId: vulnerability.id.toString(),
                error: error.message,
                stack: error.stack,
            });
            return {
                success: false,
                vulnerabilityId: vulnerability.id.toString(),
                summary: {
                    overallRisk: 'medium',
                    priority: 'medium',
                    urgency: 'medium',
                    confidence: confidence_level_enum_1.ConfidenceLevel.LOW,
                },
                riskAnalysis: {
                    technicalRisk: 0,
                    businessRisk: 0,
                    complianceRisk: 0,
                    reputationalRisk: 0,
                    combinedRisk: 0,
                },
                recommendations: {
                    immediate: ['Retry assessment', 'Manual review required'],
                    shortTerm: [],
                    longTerm: [],
                    preventive: [],
                },
                metadata: {
                    assessmentDate: new Date(),
                    assessmentDuration: Date.now() - startTime,
                    dataSourcesUsed: [],
                    assessmentVersion: '1.0',
                    assessor: 'VulnerabilityAssessmentService',
                },
            };
        }
    }
    /**
     * Assess multiple vulnerabilities in bulk
     */
    async assessVulnerabilitiesBulk(request) {
        const config = {
            prioritizeCritical: true,
            includeDetailedAnalysis: true,
            batchSize: 10,
            timeoutMs: 300000, // 5 minutes
            ...request.config,
        };
        this.logger.info('Starting bulk vulnerability assessment', {
            vulnerabilityCount: request.vulnerabilities.length,
            batchSize: config.batchSize,
        });
        const results = [];
        const errors = [];
        // Sort vulnerabilities by priority if requested
        let vulnerabilities = [...request.vulnerabilities];
        if (config.prioritizeCritical) {
            vulnerabilities = this.prioritizeVulnerabilities(vulnerabilities);
        }
        // Process in batches
        for (let i = 0; i < vulnerabilities.length; i += config.batchSize) {
            const batch = vulnerabilities.slice(i, i + config.batchSize);
            const batchPromises = batch.map(async (vulnerability) => {
                try {
                    const assessmentRequest = {
                        vulnerability,
                        context: request.context,
                        options: {
                            includeExploitAnalysis: config.includeDetailedAnalysis,
                            includeThreatIntelligence: config.includeDetailedAnalysis,
                            includeBusinessImpact: config.includeDetailedAnalysis,
                            includeComplianceImpact: config.includeDetailedAnalysis,
                        },
                    };
                    return await this.assessVulnerability(assessmentRequest);
                }
                catch (error) {
                    errors.push({
                        vulnerabilityId: vulnerability.id.toString(),
                        error: error.message,
                        retryable: this.isRetryableError(error),
                    });
                    return null;
                }
            });
            const batchResults = await Promise.allSettled(batchPromises);
            for (const result of batchResults) {
                if (result.status === 'fulfilled' && result.value) {
                    results.push(result.value);
                }
            }
        }
        const summary = this.calculateBulkSummary(results);
        this.logger.info('Bulk vulnerability assessment completed', {
            totalVulnerabilities: request.vulnerabilities.length,
            successfulAssessments: results.length,
            failedAssessments: errors.length,
            criticalVulnerabilities: summary.criticalVulnerabilities,
        });
        return {
            success: true,
            totalAssessed: request.vulnerabilities.length,
            successfulAssessments: results.length,
            failedAssessments: errors.length,
            results,
            summary,
            errors,
        };
    }
    /**
     * Perform risk analysis
     */
    async performRiskAnalysis(vulnerability, context) {
        // Technical risk based on CVSS, severity, exploitability
        let technicalRisk = vulnerability.riskAssessment.riskScore;
        // Adjust for CVSS scores
        if (vulnerability.cvssScores.length > 0) {
            const maxCVSS = Math.max(...vulnerability.cvssScores.map(score => score.baseScore));
            technicalRisk = Math.max(technicalRisk, maxCVSS * 10);
        }
        // Business risk based on asset criticality and exposure
        let businessRisk = 50; // Base business risk
        if (vulnerability.affectsCriticalAssets()) {
            businessRisk += 30;
        }
        if (vulnerability.isExternallyExposed()) {
            businessRisk += 20;
        }
        // Compliance risk based on regulations and requirements
        let complianceRisk = 30; // Base compliance risk
        if (context?.complianceRequirements?.length > 0) {
            complianceRisk += context.complianceRequirements.length * 10;
        }
        // Reputational risk based on severity and exposure
        let reputationalRisk = 20; // Base reputational risk
        if (vulnerability.isCritical()) {
            reputationalRisk += 40;
        }
        if (vulnerability.isExternallyExposed()) {
            reputationalRisk += 30;
        }
        // Combined risk calculation
        const combinedRisk = Math.min(100, (technicalRisk * 0.4) +
            (businessRisk * 0.3) +
            (complianceRisk * 0.2) +
            (reputationalRisk * 0.1));
        return {
            technicalRisk: Math.min(100, technicalRisk),
            businessRisk: Math.min(100, businessRisk),
            complianceRisk: Math.min(100, complianceRisk),
            reputationalRisk: Math.min(100, reputationalRisk),
            combinedRisk,
        };
    }
    /**
     * Perform exploit analysis
     */
    async performExploitAnalysis(vulnerability) {
        const exploitation = vulnerability.exploitation;
        if (!exploitation) {
            return {
                exploitability: 'low',
                availableExploits: 0,
                publicExploits: 0,
                weaponizedExploits: 0,
                exploitComplexity: 'high',
                attackVectors: [],
            };
        }
        const publicExploits = exploitation.availableExploits.filter(e => e.type === 'public').length;
        const weaponizedExploits = exploitation.availableExploits.filter(e => e.reliability >= 80).length;
        let exploitability = 'low';
        if (exploitation.status === 'active_exploitation') {
            exploitability = 'critical';
        }
        else if (exploitation.status === 'weaponized') {
            exploitability = 'high';
        }
        else if (publicExploits > 0) {
            exploitability = 'medium';
        }
        return {
            exploitability,
            availableExploits: exploitation.availableExploits.length,
            publicExploits,
            weaponizedExploits,
            exploitComplexity: exploitation.difficulty,
            attackVectors: exploitation.attackVectors,
        };
    }
    /**
     * Gather threat intelligence
     */
    async gatherThreatIntelligence(vulnerability) {
        // This would typically integrate with threat intelligence feeds
        return {
            threatActorInterest: 'medium',
            campaignRelevance: 'low',
            industryTargeting: false,
            geopoliticalRelevance: false,
            trends: [],
        };
    }
    /**
     * Assess business impact
     */
    async assessBusinessImpact(vulnerability, context) {
        let operationalImpact = 'low';
        let financialImpact = 0;
        let serviceAvailability = 100;
        if (vulnerability.affectsCriticalAssets()) {
            operationalImpact = 'high';
            financialImpact = 100000; // Estimated impact
            serviceAvailability = 80;
        }
        return {
            operationalImpact,
            financialImpact,
            customerImpact: operationalImpact,
            reputationalImpact: vulnerability.isCritical() ? 'high' : 'medium',
            serviceAvailability,
        };
    }
    /**
     * Assess compliance impact
     */
    async assessComplianceImpact(vulnerability, context) {
        return {
            regulationsAffected: context?.complianceRequirements || [],
            reportingRequired: vulnerability.isCritical(),
            potentialFines: vulnerability.isCritical() ? 50000 : 0,
            auditImplications: [],
            certificationRisk: vulnerability.affectsCriticalAssets(),
        };
    }
    /**
     * Generate recommendations
     */
    async generateRecommendations(vulnerability, riskAnalysis, exploitAnalysis, context) {
        const immediate = [];
        const shortTerm = [];
        const longTerm = [];
        const preventive = [];
        if (this.criticalVulnSpec.isSatisfiedBy(vulnerability)) {
            const strategies = this.criticalVulnSpec.getRiskMitigationStrategies(vulnerability);
            immediate.push(...strategies.immediate);
            shortTerm.push(...strategies.shortTerm);
            longTerm.push(...strategies.longTerm);
        }
        // Add general recommendations
        if (vulnerability.cveId) {
            shortTerm.push('Apply vendor patches');
        }
        preventive.push('Implement vulnerability scanning', 'Security awareness training');
        return { immediate, shortTerm, longTerm, preventive };
    }
    /**
     * Calculate assessment summary
     */
    calculateAssessmentSummary(vulnerability, riskAnalysis, exploitAnalysis, businessImpact) {
        let overallRisk = 'low';
        let priority = 'low';
        let urgency = 'low';
        if (riskAnalysis.combinedRisk >= 80) {
            overallRisk = 'critical';
            priority = 'critical';
            urgency = 'immediate';
        }
        else if (riskAnalysis.combinedRisk >= 60) {
            overallRisk = 'high';
            priority = 'high';
            urgency = 'high';
        }
        else if (riskAnalysis.combinedRisk >= 40) {
            overallRisk = 'medium';
            priority = 'medium';
            urgency = 'medium';
        }
        return {
            overallRisk,
            priority,
            urgency,
            confidence: vulnerability.confidence,
        };
    }
    /**
     * Prioritize vulnerabilities for assessment
     */
    prioritizeVulnerabilities(vulnerabilities) {
        return vulnerabilities.sort((a, b) => {
            // Critical vulnerabilities first
            if (this.criticalVulnSpec.isSatisfiedBy(a) && !this.criticalVulnSpec.isSatisfiedBy(b)) {
                return -1;
            }
            if (!this.criticalVulnSpec.isSatisfiedBy(a) && this.criticalVulnSpec.isSatisfiedBy(b)) {
                return 1;
            }
            // Then by risk score
            return b.riskAssessment.riskScore - a.riskAssessment.riskScore;
        });
    }
    /**
     * Calculate bulk assessment summary
     */
    calculateBulkSummary(results) {
        const criticalVulnerabilities = results.filter(r => r.summary.overallRisk === 'critical').length;
        const highRiskVulnerabilities = results.filter(r => r.summary.overallRisk === 'high').length;
        const mediumRiskVulnerabilities = results.filter(r => r.summary.overallRisk === 'medium').length;
        const lowRiskVulnerabilities = results.filter(r => r.summary.overallRisk === 'low').length;
        const averageRiskScore = results.length > 0
            ? results.reduce((sum, r) => sum + r.riskAnalysis.combinedRisk, 0) / results.length
            : 0;
        // Collect top recommendations
        const allRecommendations = results.flatMap(r => r.recommendations.immediate);
        const recommendationCounts = allRecommendations.reduce((acc, rec) => {
            acc[rec] = (acc[rec] || 0) + 1;
            return acc;
        }, {});
        const topRecommendations = Object.entries(recommendationCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .map(([rec]) => rec);
        return {
            criticalVulnerabilities,
            highRiskVulnerabilities,
            mediumRiskVulnerabilities,
            lowRiskVulnerabilities,
            averageRiskScore,
            topRecommendations,
        };
    }
    /**
     * Get data sources used for assessment
     */
    getDataSourcesUsed(options) {
        const sources = ['vulnerability_database', 'cvss_scores'];
        if (options?.includeExploitAnalysis) {
            sources.push('exploit_database');
        }
        if (options?.includeThreatIntelligence) {
            sources.push('threat_intelligence_feeds');
        }
        if (options?.includeBusinessImpact) {
            sources.push('asset_management');
        }
        if (options?.includeComplianceImpact) {
            sources.push('compliance_framework');
        }
        return sources;
    }
    /**
     * Check if error is retryable
     */
    isRetryableError(error) {
        const retryableErrors = ['timeout', 'network', 'service_unavailable', 'rate_limit'];
        const errorMessage = error.message?.toLowerCase() || '';
        return retryableErrors.some(retryableError => errorMessage.includes(retryableError));
    }
};
exports.VulnerabilityAssessmentService = VulnerabilityAssessmentService;
exports.VulnerabilityAssessmentService = VulnerabilityAssessmentService = VulnerabilityAssessmentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof domain_event_publisher_1.DomainEventPublisher !== "undefined" && domain_event_publisher_1.DomainEventPublisher) === "function" ? _a : Object])
], VulnerabilityAssessmentService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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