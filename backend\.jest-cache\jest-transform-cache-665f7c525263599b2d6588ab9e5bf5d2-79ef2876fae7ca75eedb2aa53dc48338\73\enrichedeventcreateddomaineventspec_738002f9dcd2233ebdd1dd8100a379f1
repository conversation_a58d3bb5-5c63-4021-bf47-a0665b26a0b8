91129b3aa2bfecccb253d3b0109a299a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const enriched_event_created_domain_event_1 = require("../enriched-event-created.domain-event");
const shared_kernel_1 = require("../../../../../shared-kernel");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const enriched_event_entity_1 = require("../../entities/enriched-event.entity");
describe('EnrichedEventCreatedDomainEvent', () => {
    let eventData;
    let aggregateId;
    let normalizedEventId;
    beforeEach(() => {
        aggregateId = shared_kernel_1.UniqueEntityId.create();
        normalizedEventId = shared_kernel_1.UniqueEntityId.create();
        eventData = {
            normalizedEventId,
            eventType: event_type_enum_1.EventType.THREAT_DETECTED,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
            enrichmentQualityScore: 85,
            appliedRulesCount: 3,
            enrichmentDataCount: 5,
            threatIntelScore: 75,
            requiresManualReview: true,
        };
    });
    describe('creation', () => {
        it('should create domain event with required data', () => {
            const domainEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, eventData);
            expect(domainEvent.aggregateId).toEqual(aggregateId);
            expect(domainEvent.eventData).toEqual(eventData);
            expect(domainEvent.occurredOn).toBeInstanceOf(Date);
            expect(domainEvent.eventId).toBeDefined();
        });
        it('should create domain event with custom options', () => {
            const customEventId = shared_kernel_1.UniqueEntityId.create();
            const customOccurredOn = new Date('2023-01-01');
            const correlationId = 'test-correlation-001';
            const causationId = 'test-causation-001';
            const metadata = { source: 'test' };
            const domainEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, eventData, {
                eventId: customEventId,
                occurredOn: customOccurredOn,
                eventVersion: 2,
                correlationId,
                causationId,
                metadata,
            });
            expect(domainEvent.eventId).toEqual(customEventId);
            expect(domainEvent.occurredOn).toEqual(customOccurredOn);
            expect(domainEvent.eventVersion).toBe(2);
            expect(domainEvent.correlationId).toBe(correlationId);
            expect(domainEvent.causationId).toBe(causationId);
            expect(domainEvent.metadata).toEqual(metadata);
        });
    });
    describe('getters', () => {
        let domainEvent;
        beforeEach(() => {
            domainEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, eventData);
        });
        it('should provide access to normalized event ID', () => {
            expect(domainEvent.normalizedEventId).toEqual(normalizedEventId);
        });
        it('should provide access to event type', () => {
            expect(domainEvent.eventType).toBe(event_type_enum_1.EventType.THREAT_DETECTED);
        });
        it('should provide access to severity', () => {
            expect(domainEvent.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
        });
        it('should provide access to enrichment status', () => {
            expect(domainEvent.enrichmentStatus).toBe(enriched_event_entity_1.EnrichmentStatus.COMPLETED);
        });
        it('should provide access to enrichment quality score', () => {
            expect(domainEvent.enrichmentQualityScore).toBe(85);
        });
        it('should provide access to applied rules count', () => {
            expect(domainEvent.appliedRulesCount).toBe(3);
        });
        it('should provide access to enrichment data count', () => {
            expect(domainEvent.enrichmentDataCount).toBe(5);
        });
        it('should provide access to threat intelligence score', () => {
            expect(domainEvent.threatIntelScore).toBe(75);
        });
        it('should provide access to manual review requirement', () => {
            expect(domainEvent.requiresManualReview).toBe(true);
        });
    });
    describe('query methods', () => {
        let domainEvent;
        beforeEach(() => {
            domainEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, eventData);
        });
        it('should check if event is high severity', () => {
            expect(domainEvent.isHighSeverity()).toBe(true);
            const mediumSeverityData = { ...eventData, severity: event_severity_enum_1.EventSeverity.MEDIUM };
            const mediumEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, mediumSeverityData);
            expect(mediumEvent.isHighSeverity()).toBe(false);
        });
        it('should check if event is critical', () => {
            expect(domainEvent.isCritical()).toBe(false);
            const criticalData = { ...eventData, severity: event_severity_enum_1.EventSeverity.CRITICAL };
            const criticalEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, criticalData);
            expect(criticalEvent.isCritical()).toBe(true);
        });
        it('should check if event has high enrichment quality', () => {
            expect(domainEvent.hasHighEnrichmentQuality()).toBe(true);
            const lowQualityData = { ...eventData, enrichmentQualityScore: 50 };
            const lowQualityEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, lowQualityData);
            expect(lowQualityEvent.hasHighEnrichmentQuality()).toBe(false);
            const noQualityData = { ...eventData, enrichmentQualityScore: undefined };
            const noQualityEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, noQualityData);
            expect(noQualityEvent.hasHighEnrichmentQuality()).toBe(false);
        });
        it('should check if event has threat intelligence', () => {
            expect(domainEvent.hasThreatIntelligence()).toBe(true);
            const noThreatIntelData = { ...eventData, threatIntelScore: undefined };
            const noThreatIntelEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, noThreatIntelData);
            expect(noThreatIntelEvent.hasThreatIntelligence()).toBe(false);
            const zeroThreatIntelData = { ...eventData, threatIntelScore: 0 };
            const zeroThreatIntelEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, zeroThreatIntelData);
            expect(zeroThreatIntelEvent.hasThreatIntelligence()).toBe(false);
        });
        it('should check if enrichment is completed', () => {
            expect(domainEvent.isEnrichmentCompleted()).toBe(true);
            const pendingData = { ...eventData, enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.PENDING };
            const pendingEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, pendingData);
            expect(pendingEvent.isEnrichmentCompleted()).toBe(false);
        });
        it('should check if event is high threat risk', () => {
            expect(domainEvent.isHighThreatRisk()).toBe(false);
            const highThreatData = { ...eventData, threatIntelScore: 90 };
            const highThreatEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, highThreatData);
            expect(highThreatEvent.isHighThreatRisk()).toBe(true);
            const noThreatData = { ...eventData, threatIntelScore: undefined };
            const noThreatEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, noThreatData);
            expect(noThreatEvent.isHighThreatRisk()).toBe(false);
        });
    });
    describe('event summary', () => {
        let domainEvent;
        beforeEach(() => {
            domainEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, eventData);
        });
        it('should generate comprehensive event summary', () => {
            const summary = domainEvent.getEventSummary();
            expect(summary).toEqual({
                enrichedEventId: aggregateId.toString(),
                normalizedEventId: normalizedEventId.toString(),
                eventType: event_type_enum_1.EventType.THREAT_DETECTED,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
                enrichmentQualityScore: 85,
                appliedRulesCount: 3,
                enrichmentDataCount: 5,
                threatIntelScore: 75,
                requiresManualReview: true,
                isHighSeverity: true,
                isCritical: false,
                hasHighEnrichmentQuality: true,
                hasThreatIntelligence: true,
                isEnrichmentCompleted: true,
                isHighThreatRisk: false,
            });
        });
        it('should handle undefined optional values in summary', () => {
            const minimalData = {
                normalizedEventId,
                eventType: event_type_enum_1.EventType.CUSTOM,
                severity: event_severity_enum_1.EventSeverity.LOW,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.PENDING,
                appliedRulesCount: 0,
                enrichmentDataCount: 0,
                requiresManualReview: false,
            };
            const minimalEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, minimalData);
            const summary = minimalEvent.getEventSummary();
            expect(summary.enrichmentQualityScore).toBeUndefined();
            expect(summary.threatIntelScore).toBeUndefined();
            expect(summary.hasHighEnrichmentQuality).toBe(false);
            expect(summary.hasThreatIntelligence).toBe(false);
            expect(summary.isHighThreatRisk).toBe(false);
        });
    });
    describe('serialization', () => {
        let domainEvent;
        beforeEach(() => {
            domainEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, eventData);
        });
        it('should serialize to JSON with event summary', () => {
            const json = domainEvent.toJSON();
            expect(json).toHaveProperty('eventId');
            expect(json).toHaveProperty('aggregateId');
            expect(json).toHaveProperty('occurredOn');
            expect(json).toHaveProperty('eventData');
            expect(json).toHaveProperty('eventSummary');
            expect(json.eventData).toEqual(eventData);
            expect(json.eventSummary).toEqual(domainEvent.getEventSummary());
        });
        it('should include base domain event properties in JSON', () => {
            const json = domainEvent.toJSON();
            expect(json.eventId).toBeDefined();
            expect(json.aggregateId).toBe(aggregateId.toString());
            expect(json.occurredOn).toBeDefined();
            expect(json.eventVersion).toBe(1); // Default version
        });
    });
    describe('edge cases', () => {
        it('should handle all severity levels correctly', () => {
            const severities = [
                { severity: event_severity_enum_1.EventSeverity.LOW, isHigh: false, isCritical: false },
                { severity: event_severity_enum_1.EventSeverity.MEDIUM, isHigh: false, isCritical: false },
                { severity: event_severity_enum_1.EventSeverity.HIGH, isHigh: true, isCritical: false },
                { severity: event_severity_enum_1.EventSeverity.CRITICAL, isHigh: true, isCritical: true },
            ];
            severities.forEach(({ severity, isHigh, isCritical }) => {
                const testData = { ...eventData, severity };
                const testEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, testData);
                expect(testEvent.isHighSeverity()).toBe(isHigh);
                expect(testEvent.isCritical()).toBe(isCritical);
            });
        });
        it('should handle all enrichment statuses correctly', () => {
            const statuses = [
                enriched_event_entity_1.EnrichmentStatus.PENDING,
                enriched_event_entity_1.EnrichmentStatus.IN_PROGRESS,
                enriched_event_entity_1.EnrichmentStatus.COMPLETED,
                enriched_event_entity_1.EnrichmentStatus.FAILED,
                enriched_event_entity_1.EnrichmentStatus.PARTIAL,
                enriched_event_entity_1.EnrichmentStatus.SKIPPED,
            ];
            statuses.forEach(status => {
                const testData = { ...eventData, enrichmentStatus: status };
                const testEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, testData);
                expect(testEvent.isEnrichmentCompleted()).toBe(status === enriched_event_entity_1.EnrichmentStatus.COMPLETED);
            });
        });
        it('should handle boundary values for threat intelligence score', () => {
            const testCases = [
                { score: 0, hasThreatIntel: false, isHighRisk: false },
                { score: 1, hasThreatIntel: true, isHighRisk: false },
                { score: 84, hasThreatIntel: true, isHighRisk: false },
                { score: 85, hasThreatIntel: true, isHighRisk: true },
                { score: 100, hasThreatIntel: true, isHighRisk: true },
            ];
            testCases.forEach(({ score, hasThreatIntel, isHighRisk }) => {
                const testData = { ...eventData, threatIntelScore: score };
                const testEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, testData);
                expect(testEvent.hasThreatIntelligence()).toBe(hasThreatIntel);
                expect(testEvent.isHighThreatRisk()).toBe(isHighRisk);
            });
        });
        it('should handle boundary values for enrichment quality score', () => {
            const testCases = [
                { score: 0, hasHighQuality: false },
                { score: 69, hasHighQuality: false },
                { score: 70, hasHighQuality: true },
                { score: 100, hasHighQuality: true },
            ];
            testCases.forEach(({ score, hasHighQuality }) => {
                const testData = { ...eventData, enrichmentQualityScore: score };
                const testEvent = new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(aggregateId, testData);
                expect(testEvent.hasHighEnrichmentQuality()).toBe(hasHighQuality);
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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