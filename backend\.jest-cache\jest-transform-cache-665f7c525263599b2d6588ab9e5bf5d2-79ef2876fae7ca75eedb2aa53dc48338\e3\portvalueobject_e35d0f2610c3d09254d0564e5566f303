bd9c1326614e4dec91c754ba5fc7be73
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Port = exports.PortRiskLevel = exports.PortClass = exports.PortProtocol = void 0;
const base_value_object_1 = require("../../../../../shared-kernel/value-objects/base-value-object");
/**
 * Port Protocol Type
 */
var PortProtocol;
(function (PortProtocol) {
    PortProtocol["TCP"] = "tcp";
    PortProtocol["UDP"] = "udp";
    PortProtocol["SCTP"] = "sctp";
    PortProtocol["DCCP"] = "dccp";
})(PortProtocol || (exports.PortProtocol = PortProtocol = {}));
/**
 * Port Classification
 */
var PortClass;
(function (PortClass) {
    PortClass["WELL_KNOWN"] = "well_known";
    PortClass["REGISTERED"] = "registered";
    PortClass["DYNAMIC"] = "dynamic";
})(PortClass || (exports.PortClass = PortClass = {}));
/**
 * Port Security Risk Level
 */
var PortRiskLevel;
(function (PortRiskLevel) {
    PortRiskLevel["LOW"] = "low";
    PortRiskLevel["MEDIUM"] = "medium";
    PortRiskLevel["HIGH"] = "high";
    PortRiskLevel["CRITICAL"] = "critical";
})(PortRiskLevel || (exports.PortRiskLevel = PortRiskLevel = {}));
/**
 * Port Value Object
 *
 * Represents a network port with protocol information and security analysis.
 * Provides classification, risk assessment, and service identification capabilities.
 *
 * Key features:
 * - Port number validation (0-65535)
 * - Protocol support (TCP, UDP, SCTP, DCCP)
 * - Port classification (well-known, registered, dynamic)
 * - Security risk assessment
 * - Common service identification
 * - Vulnerability analysis support
 */
class Port extends base_value_object_1.BaseValueObject {
    constructor(props) {
        super(props);
    }
    validate() {
        if (!Number.isInteger(this._value.number)) {
            throw new Error('Port number must be an integer');
        }
        if (this._value.number < Port.MIN_PORT || this._value.number > Port.MAX_PORT) {
            throw new Error(`Port number must be between ${Port.MIN_PORT} and ${Port.MAX_PORT}`);
        }
        if (!Object.values(PortProtocol).includes(this._value.protocol)) {
            throw new Error(`Invalid protocol: ${this._value.protocol}`);
        }
    }
    /**
     * Create a port from number and protocol
     */
    static create(number, protocol) {
        return new Port({ number, protocol });
    }
    /**
     * Create a TCP port
     */
    static tcp(number) {
        return Port.create(number, PortProtocol.TCP);
    }
    /**
     * Create a UDP port
     */
    static udp(number) {
        return Port.create(number, PortProtocol.UDP);
    }
    /**
     * Create from string format "protocol/port" (e.g., "tcp/80")
     */
    static fromString(portString) {
        const parts = portString.toLowerCase().split('/');
        if (parts.length !== 2) {
            throw new Error('Port string must be in format "protocol/port"');
        }
        const [protocolStr, portStr] = parts;
        const protocol = protocolStr;
        const number = parseInt(portStr, 10);
        if (!Object.values(PortProtocol).includes(protocol)) {
            throw new Error(`Invalid protocol: ${protocolStr}`);
        }
        return Port.create(number, protocol);
    }
    /**
     * Get port number
     */
    get number() {
        return this._value.number;
    }
    /**
     * Get protocol
     */
    get protocol() {
        return this._value.protocol;
    }
    /**
     * Check if this is a TCP port
     */
    isTCP() {
        return this._value.protocol === PortProtocol.TCP;
    }
    /**
     * Check if this is a UDP port
     */
    isUDP() {
        return this._value.protocol === PortProtocol.UDP;
    }
    /**
     * Get port classification
     */
    getClassification() {
        if (this._value.number <= Port.WELL_KNOWN_MAX) {
            return PortClass.WELL_KNOWN;
        }
        else if (this._value.number <= Port.REGISTERED_MAX) {
            return PortClass.REGISTERED;
        }
        else {
            return PortClass.DYNAMIC;
        }
    }
    /**
     * Check if this is a well-known port
     */
    isWellKnown() {
        return this.getClassification() === PortClass.WELL_KNOWN;
    }
    /**
     * Check if this is a registered port
     */
    isRegistered() {
        return this.getClassification() === PortClass.REGISTERED;
    }
    /**
     * Check if this is a dynamic/ephemeral port
     */
    isDynamic() {
        return this.getClassification() === PortClass.DYNAMIC;
    }
    /**
     * Get common service name for well-known ports
     */
    getServiceName() {
        const services = Port.getWellKnownServices();
        const key = `${this._value.protocol}/${this._value.number}`;
        return services[key] || null;
    }
    /**
     * Get security risk level
     */
    getRiskLevel() {
        const riskPorts = Port.getHighRiskPorts();
        const key = `${this._value.protocol}/${this._value.number}`;
        if (riskPorts.critical.includes(key)) {
            return PortRiskLevel.CRITICAL;
        }
        else if (riskPorts.high.includes(key)) {
            return PortRiskLevel.HIGH;
        }
        else if (riskPorts.medium.includes(key)) {
            return PortRiskLevel.MEDIUM;
        }
        return PortRiskLevel.LOW;
    }
    /**
     * Check if this is a high-risk port
     */
    isHighRisk() {
        const risk = this.getRiskLevel();
        return risk === PortRiskLevel.HIGH || risk === PortRiskLevel.CRITICAL;
    }
    /**
     * Check if this is a commonly attacked port
     */
    isCommonlyAttacked() {
        const commonTargets = [
            'tcp/21', 'tcp/22', 'tcp/23', 'tcp/25', 'tcp/53', 'tcp/80', 'tcp/110',
            'tcp/135', 'tcp/139', 'tcp/143', 'tcp/443', 'tcp/445', 'tcp/993',
            'tcp/995', 'tcp/1433', 'tcp/1521', 'tcp/3306', 'tcp/3389', 'tcp/5432',
            'tcp/5900', 'tcp/6379', 'tcp/8080', 'tcp/8443', 'tcp/27017',
            'udp/53', 'udp/69', 'udp/123', 'udp/161', 'udp/162', 'udp/1900'
        ];
        const key = `${this._value.protocol}/${this._value.number}`;
        return commonTargets.includes(key);
    }
    /**
     * Check if this port should be monitored for security
     */
    shouldMonitor() {
        return this.isHighRisk() || this.isCommonlyAttacked() || this.isWellKnown();
    }
    /**
     * Get security recommendations for this port
     */
    getSecurityRecommendations() {
        const recommendations = [];
        const serviceName = this.getServiceName();
        const riskLevel = this.getRiskLevel();
        if (riskLevel === PortRiskLevel.CRITICAL) {
            recommendations.push('Consider disabling this service if not required');
            recommendations.push('Implement strict access controls and monitoring');
        }
        if (this.isHighRisk()) {
            recommendations.push('Enable detailed logging and monitoring');
            recommendations.push('Implement rate limiting and intrusion detection');
        }
        if (serviceName) {
            switch (serviceName.toLowerCase()) {
                case 'ssh':
                    recommendations.push('Use key-based authentication');
                    recommendations.push('Disable root login');
                    recommendations.push('Change default port if possible');
                    break;
                case 'telnet':
                    recommendations.push('Replace with SSH for secure remote access');
                    break;
                case 'ftp':
                    recommendations.push('Use SFTP or FTPS instead');
                    break;
                case 'http':
                    recommendations.push('Redirect to HTTPS');
                    break;
                case 'rdp':
                    recommendations.push('Enable Network Level Authentication');
                    recommendations.push('Use VPN for remote access');
                    break;
            }
        }
        if (this.isCommonlyAttacked()) {
            recommendations.push('Implement fail2ban or similar protection');
            recommendations.push('Use non-standard ports when possible');
        }
        return recommendations;
    }
    /**
     * Check if port is typically encrypted
     */
    isEncrypted() {
        const encryptedPorts = [
            'tcp/22', 'tcp/443', 'tcp/465', 'tcp/587', 'tcp/636', 'tcp/993',
            'tcp/995', 'tcp/8443', 'tcp/9443'
        ];
        const key = `${this._value.protocol}/${this._value.number}`;
        return encryptedPorts.includes(key);
    }
    /**
     * Get port range category
     */
    getRangeCategory() {
        if (this._value.number === 0)
            return 'Reserved';
        if (this._value.number <= 1023)
            return 'System/Well-Known';
        if (this._value.number <= 49151)
            return 'User/Registered';
        return 'Dynamic/Private';
    }
    /**
     * Convert to string representation
     */
    toString() {
        return `${this._value.protocol}/${this._value.number}`;
    }
    /**
     * Get detailed port information
     */
    getPortInfo() {
        return {
            number: this._value.number,
            protocol: this._value.protocol,
            classification: this.getClassification(),
            serviceName: this.getServiceName(),
            riskLevel: this.getRiskLevel(),
            isEncrypted: this.isEncrypted(),
            isCommonlyAttacked: this.isCommonlyAttacked(),
            recommendations: this.getSecurityRecommendations(),
        };
    }
    /**
     * Compare ports for equality
     */
    equals(other) {
        if (!other) {
            return false;
        }
        if (this === other) {
            return true;
        }
        return this._value.number === other._value.number &&
            this._value.protocol === other._value.protocol;
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...this.getPortInfo(),
            string: this.toString(),
        };
    }
    /**
     * Create Port from JSON
     */
    static fromJSON(json) {
        return Port.create(json.number, json.protocol);
    }
    /**
     * Get well-known services mapping
     */
    static getWellKnownServices() {
        return {
            'tcp/20': 'FTP Data',
            'tcp/21': 'FTP Control',
            'tcp/22': 'SSH',
            'tcp/23': 'Telnet',
            'tcp/25': 'SMTP',
            'tcp/53': 'DNS',
            'tcp/80': 'HTTP',
            'tcp/110': 'POP3',
            'tcp/143': 'IMAP',
            'tcp/443': 'HTTPS',
            'tcp/465': 'SMTPS',
            'tcp/587': 'SMTP Submission',
            'tcp/993': 'IMAPS',
            'tcp/995': 'POP3S',
            'udp/53': 'DNS',
            'udp/67': 'DHCP Server',
            'udp/68': 'DHCP Client',
            'udp/69': 'TFTP',
            'udp/123': 'NTP',
            'udp/161': 'SNMP',
            'udp/162': 'SNMP Trap',
            'tcp/135': 'RPC Endpoint Mapper',
            'tcp/139': 'NetBIOS Session',
            'tcp/445': 'SMB',
            'tcp/1433': 'SQL Server',
            'tcp/1521': 'Oracle',
            'tcp/3306': 'MySQL',
            'tcp/3389': 'RDP',
            'tcp/5432': 'PostgreSQL',
            'tcp/5900': 'VNC',
            'tcp/6379': 'Redis',
            'tcp/8080': 'HTTP Alternate',
            'tcp/27017': 'MongoDB',
        };
    }
    /**
     * Get high-risk ports categorized by risk level
     */
    static getHighRiskPorts() {
        return {
            critical: [
                'tcp/23', // Telnet (unencrypted)
                'tcp/21', // FTP (unencrypted)
                'tcp/135', // RPC (vulnerable)
                'tcp/139', // NetBIOS (vulnerable)
                'tcp/445', // SMB (frequently attacked)
            ],
            high: [
                'tcp/22', // SSH (commonly attacked)
                'tcp/3389', // RDP (commonly attacked)
                'tcp/1433', // SQL Server
                'tcp/3306', // MySQL
                'tcp/5432', // PostgreSQL
                'tcp/6379', // Redis (often unsecured)
                'tcp/27017', // MongoDB (often unsecured)
                'udp/161', // SNMP (information disclosure)
            ],
            medium: [
                'tcp/25', // SMTP
                'tcp/80', // HTTP (unencrypted)
                'tcp/110', // POP3 (unencrypted)
                'tcp/143', // IMAP (unencrypted)
                'tcp/8080', // HTTP Alternate
                'udp/69', // TFTP (unencrypted)
                'udp/123', // NTP (amplification attacks)
            ],
        };
    }
    /**
     * Validate port format without creating instance
     */
    static isValid(number, protocol) {
        try {
            new Port({ number, protocol });
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Parse port from various string formats
     */
    static parse(input) {
        try {
            // Try "protocol/port" format
            if (input.includes('/')) {
                return Port.fromString(input);
            }
            // Try just port number (assume TCP)
            const number = parseInt(input, 10);
            if (!isNaN(number)) {
                return Port.tcp(number);
            }
            return null;
        }
        catch {
            return null;
        }
    }
}
exports.Port = Port;
Port.MIN_PORT = 0;
Port.MAX_PORT = 65535;
Port.WELL_KNOWN_MAX = 1023;
Port.REGISTERED_MAX = 49151;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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