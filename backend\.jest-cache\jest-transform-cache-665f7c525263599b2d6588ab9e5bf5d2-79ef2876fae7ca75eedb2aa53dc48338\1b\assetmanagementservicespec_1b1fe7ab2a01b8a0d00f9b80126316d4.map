{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\asset-management.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,yEAAoE;AACpE,qEAA2D;AAC3D,qFAA2E;AAC3E,sFAAkF;AAClF,0FAAsF;AAEtF,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,IAAI,OAA+B,CAAC;IACpC,IAAI,eAAkC,CAAC;IACvC,IAAI,uBAAkD,CAAC;IACvD,IAAI,aAA4B,CAAC;IACjC,IAAI,YAA0B,CAAC;IAE/B,MAAM,mBAAmB,GAAG;QAC1B,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC9B,CAAC;IAEF,MAAM,2BAA2B,GAAG;QAClC,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC9B,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;QACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACjB,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB,CAAC;IAEF,MAAM,SAAS,GAAmB;QAChC,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,aAAa;QACnB,IAAI,EAAE,QAAQ;QACd,MAAM,EAAE,QAAQ;QAChB,WAAW,EAAE,QAAQ;QACrB,WAAW,EAAE,YAAY;QACzB,SAAS,EAAE,eAAe;QAC1B,QAAQ,EAAE,yBAAyB;QACnC,SAAS,EAAE,UAAU;QACrB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;QACtB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;QACpB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;KAClB,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,iDAAsB;gBACtB;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,oBAAK,CAAC;oBAClC,QAAQ,EAAE,mBAAmB;iBAC9B;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,oCAAa,CAAC;oBAC1C,QAAQ,EAAE,2BAA2B;iBACtC;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE,gBAAgB;iBAC3B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAyB,iDAAsB,CAAC,CAAC;QACrE,eAAe,GAAG,MAAM,CAAC,GAAG,CAAoB,IAAA,4BAAkB,EAAC,oBAAK,CAAC,CAAC,CAAC;QAC3E,uBAAuB,GAAG,MAAM,CAAC,GAAG,CAA4B,IAAA,4BAAkB,EAAC,oCAAa,CAAC,CAAC,CAAC;QACnG,aAAa,GAAG,MAAM,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;QACzD,YAAY,GAAG,MAAM,CAAC,GAAG,CAAe,4BAAY,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,SAAS,GAAG;gBAChB,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,aAAa;gBAC1B,SAAS,EAAE,eAAe;gBAC1B,QAAQ,EAAE,yBAAyB;gBACnC,WAAW,EAAE,QAAiB;gBAC9B,WAAW,EAAE,YAAqB;aACnC,CAAC;YACF,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,mBAAmB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe;YACpE,mBAAmB,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YACtD,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEtD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAE5D,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACvD,KAAK,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC,SAAS,EAAE;aAC1C,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CACrD,MAAM,CAAC,gBAAgB,CAAC;gBACtB,GAAG,SAAS;gBACZ,MAAM,EAAE,QAAQ;gBAChB,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;gBACjC,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC1B,SAAS,EAAE,MAAM;gBACjB,eAAe,EAAE,QAAQ;aAC1B,CAAC,CACH,CAAC;YACF,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACxD,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YACjE,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,OAAO,EACP,SAAS,CAAC,EAAE,EACZ,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,SAAS,CAAC,SAAS;aAC/B,CAAC,CACH,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,SAAS,GAAG;gBAChB,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,eAAe;aAC3B,CAAC;YACF,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,mBAAmB,CAAC,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,kBAAkB;YAE5E,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;iBACjD,OAAO,CAAC,OAAO,CAAC,oDAAoD,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,OAAO,GAAG,WAAW,CAAC;YAC5B,mBAAmB,CAAC,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAE/C,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACvD,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;gBACtB,SAAS,EAAE,CAAC,iBAAiB,EAAE,aAAa,CAAC;aAC9C,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,OAAO,GAAG,cAAc,CAAC;YAC/B,mBAAmB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAE/C,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,IAAI,EAAE,CAAC,QAAQ,CAAC;gBAChB,MAAM,EAAE,CAAC,QAAQ,CAAC;gBAClB,WAAW,EAAE,CAAC,MAAM,CAAC;gBACrB,MAAM,EAAE,MAAM;aACf,CAAC;YAEF,MAAM,gBAAgB,GAAG;gBACvB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACnC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;aAC/D,CAAC;YAEF,mBAAmB,CAAC,kBAAkB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YAEzE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAE/C,MAAM,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAC7E,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,0BAA0B,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YAC3G,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YACnH,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,wCAAwC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;YACvI,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,+FAA+F,EAC/F,EAAE,MAAM,EAAE,QAAQ,EAAE,CACrB,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,MAAM,EAAE,CAAC,SAAS,CAAC;gBACnB,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE,CAAC;gBACP,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,OAAO,GAAG,WAAW,CAAC;YAC5B,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,MAAe;aAC7B,CAAC;YACF,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,SAAkB,CAAC,CAAC;YACtE,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,GAAG,SAAS,EAAE,GAAG,UAAU,EAAE,CAAC,CAAC;YAE5E,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YAEtE,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,SAAS,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;YACzF,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACxD,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YACjE,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,MAAM,CAAC,gBAAgB,CAAC;gBACtB,OAAO,EAAE,UAAU;aACpB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,OAAO,GAAG,cAAc,CAAC;YAC/B,MAAM,UAAU,GAAG,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;YAC9C,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;iBAC3D,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,OAAO,GAAG,WAAW,CAAC;YAC5B,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,SAAkB,CAAC,CAAC;YACtE,mBAAmB,CAAC,MAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAExD,MAAM,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAE3C,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YACnE,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,SAAS,CAAC,SAAS;aAC/B,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,OAAO,GAAG,cAAc,CAAC;YAC/B,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;iBAC/C,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,aAAa,GAAG;gBACpB;oBACE,SAAS,EAAE,eAAe;oBAC1B,QAAQ,EAAE,wBAAwB;oBAClC,UAAU,EAAE,mBAAmB;oBAC/B,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE;oBACrD,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;oBACrE,eAAe,EAAE,cAAc;iBAChC;aACF,CAAC;YACF,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,mBAAmB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;YACzE,mBAAmB,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YACtD,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEtD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAEnE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACvD,KAAK,EAAE,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;aACjD,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CACrD,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ;gBAC/B,IAAI,EAAE,QAAQ,EAAE,2BAA2B;gBAC3C,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS;gBACrC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ;gBACnC,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,UAAU;gBACvC,eAAe,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,eAAe;gBACjD,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ;gBACnC,eAAe,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,eAAe;gBACjD,SAAS,EAAE,MAAM;aAClB,CAAC,CACH,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,aAAa,GAAG;gBACpB;oBACE,SAAS,EAAE,eAAe;oBAC1B,QAAQ,EAAE,4BAA4B;oBACtC,eAAe,EAAE,cAAc;iBAChC;aACF,CAAC;YACF,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,mBAAmB,CAAC,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB;YAC3E,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEtD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAEnE,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,OAAO,GAAG,WAAW,CAAC;YAC5B,MAAM,cAAc,GAAG;gBACrB,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE;gBACpC,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE;gBAChC,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;aACpC,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,SAAkB,CAAC,CAAC;YAEtE,MAAM,gBAAgB,GAAG;gBACvB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAClC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACrC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACjC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACnC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,cAAc,CAAC;aACxD,CAAC;YAEF,2BAA2B,CAAC,kBAAkB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YACjF,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEtD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;YAEhE,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YACpF,MAAM,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC,oBAAoB,CAAC;gBAC/D,QAAQ,EAAE,CAAC;gBACX,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,EAAE;gBACV,GAAG,EAAE,CAAC;gBACN,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;aACV,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,gBAAgB,GAAG;gBACvB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACjC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACnC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACtC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,CAAC;aAClD,CAAC;YAEF,mBAAmB,CAAC,kBAAkB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YAEzE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,EAAE,CAAC;YAEvD,MAAM,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAC7E,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC,gCAAgC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC3G,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,wBAAwB,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;YACvG,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,mBAAmB,CAAC,KAAK;iBACtB,qBAAqB,CAAC,GAAG,CAAC,CAAC,QAAQ;iBACnC,qBAAqB,CAAC,EAAE,CAAC,CAAE,SAAS;iBACpC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW;YAEzC,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,sBAAsB,CAAC,CAAC,iBAAiB,CAAC;gBACnE,MAAM,EAAE,EAAE;gBACV,WAAW,EAAE,EAAE;gBACf,cAAc,EAAE,EAAE;aACnB,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,6BAA6B,CAAC,CAAC,iBAAiB,CAAC;gBAC1E,UAAU,EAAE,EAAE;gBACd,OAAO,EAAE,EAAE;gBACX,WAAW,EAAE,EAAE;aAChB,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,8BAA8B,CAAC,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAEjF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,aAAa,EAAE,CAAC;YAE7C,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,KAAK,EAAE,GAAG;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE;oBACN,MAAM,EAAE,EAAE;oBACV,WAAW,EAAE,EAAE;oBACf,cAAc,EAAE,EAAE;iBACnB;gBACD,aAAa,EAAE;oBACb,UAAU,EAAE,EAAE;oBACd,OAAO,EAAE,EAAE;oBACX,WAAW,EAAE,EAAE;iBAChB;gBACD,mBAAmB,EAAE,EAAE;gBACvB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\asset-management.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { AssetManagementService } from './asset-management.service';\r\nimport { Asset } from '../../domain/entities/asset.entity';\r\nimport { Vulnerability } from '../../domain/entities/vulnerability.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\n\r\ndescribe('AssetManagementService', () => {\r\n  let service: AssetManagementService;\r\n  let assetRepository: Repository<Asset>;\r\n  let vulnerabilityRepository: Repository<Vulnerability>;\r\n  let loggerService: LoggerService;\r\n  let auditService: AuditService;\r\n\r\n  const mockAssetRepository = {\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    findOne: jest.fn(),\r\n    find: jest.fn(),\r\n    remove: jest.fn(),\r\n    count: jest.fn(),\r\n    createQueryBuilder: jest.fn(),\r\n  };\r\n\r\n  const mockVulnerabilityRepository = {\r\n    createQueryBuilder: jest.fn(),\r\n  };\r\n\r\n  const mockLoggerService = {\r\n    debug: jest.fn(),\r\n    log: jest.fn(),\r\n    warn: jest.fn(),\r\n    error: jest.fn(),\r\n  };\r\n\r\n  const mockAuditService = {\r\n    logUserAction: jest.fn(),\r\n  };\r\n\r\n  const mockAsset: Partial<Asset> = {\r\n    id: 'asset-123',\r\n    name: 'test-server',\r\n    type: 'server',\r\n    status: 'active',\r\n    criticality: 'medium',\r\n    environment: 'production',\r\n    ipAddress: '*************',\r\n    hostname: 'test-server.company.com',\r\n    createdBy: 'user-123',\r\n    calculateRiskScore: jest.fn(),\r\n    updateLastSeen: jest.fn(),\r\n    addSoftware: jest.fn(),\r\n    addService: jest.fn(),\r\n    addTag: jest.fn(),\r\n    removeTag: jest.fn(),\r\n    hasTag: jest.fn(),\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        AssetManagementService,\r\n        {\r\n          provide: getRepositoryToken(Asset),\r\n          useValue: mockAssetRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(Vulnerability),\r\n          useValue: mockVulnerabilityRepository,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: mockLoggerService,\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: mockAuditService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<AssetManagementService>(AssetManagementService);\r\n    assetRepository = module.get<Repository<Asset>>(getRepositoryToken(Asset));\r\n    vulnerabilityRepository = module.get<Repository<Vulnerability>>(getRepositoryToken(Vulnerability));\r\n    loggerService = module.get<LoggerService>(LoggerService);\r\n    auditService = module.get<AuditService>(AuditService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('createAsset', () => {\r\n    it('should create asset successfully', async () => {\r\n      const assetData = {\r\n        name: 'test-server',\r\n        type: 'server',\r\n        description: 'Test server',\r\n        ipAddress: '*************',\r\n        hostname: 'test-server.company.com',\r\n        criticality: 'medium' as const,\r\n        environment: 'production' as const,\r\n      };\r\n      const userId = 'user-123';\r\n\r\n      mockAssetRepository.findOne.mockResolvedValue(null); // No duplicate\r\n      mockAssetRepository.create.mockReturnValue(mockAsset);\r\n      mockAssetRepository.save.mockResolvedValue(mockAsset);\r\n\r\n      const result = await service.createAsset(assetData, userId);\r\n\r\n      expect(mockAssetRepository.findOne).toHaveBeenCalledWith({\r\n        where: { ipAddress: assetData.ipAddress },\r\n      });\r\n      expect(mockAssetRepository.create).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          ...assetData,\r\n          status: 'active',\r\n          firstDiscovered: expect.any(Date),\r\n          lastSeen: expect.any(Date),\r\n          createdBy: userId,\r\n          discoveryMethod: 'manual',\r\n        }),\r\n      );\r\n      expect(mockAsset.calculateRiskScore).toHaveBeenCalled();\r\n      expect(mockAssetRepository.save).toHaveBeenCalledWith(mockAsset);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'create',\r\n        'asset',\r\n        mockAsset.id,\r\n        expect.objectContaining({\r\n          name: assetData.name,\r\n          type: assetData.type,\r\n          ipAddress: assetData.ipAddress,\r\n        }),\r\n      );\r\n      expect(result).toEqual(mockAsset);\r\n    });\r\n\r\n    it('should throw error when duplicate IP address exists', async () => {\r\n      const assetData = {\r\n        name: 'test-server',\r\n        type: 'server',\r\n        ipAddress: '*************',\r\n      };\r\n      const userId = 'user-123';\r\n\r\n      mockAssetRepository.findOne.mockResolvedValue(mockAsset); // Duplicate found\r\n\r\n      await expect(service.createAsset(assetData, userId))\r\n        .rejects.toThrow('Asset with IP address ************* already exists');\r\n    });\r\n  });\r\n\r\n  describe('findById', () => {\r\n    it('should return asset when found', async () => {\r\n      const assetId = 'asset-123';\r\n      mockAssetRepository.findOne.mockResolvedValue(mockAsset);\r\n\r\n      const result = await service.findById(assetId);\r\n\r\n      expect(mockAssetRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: assetId },\r\n        relations: ['vulnerabilities', 'scanResults'],\r\n      });\r\n      expect(result).toEqual(mockAsset);\r\n    });\r\n\r\n    it('should return null when asset not found', async () => {\r\n      const assetId = 'non-existent';\r\n      mockAssetRepository.findOne.mockResolvedValue(null);\r\n\r\n      const result = await service.findById(assetId);\r\n\r\n      expect(result).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('findMany', () => {\r\n    it('should return paginated assets with filters', async () => {\r\n      const options = {\r\n        page: 1,\r\n        limit: 20,\r\n        type: ['server'],\r\n        status: ['active'],\r\n        criticality: ['high'],\r\n        search: 'test',\r\n      };\r\n\r\n      const mockQueryBuilder = {\r\n        andWhere: jest.fn().mockReturnThis(),\r\n        orderBy: jest.fn().mockReturnThis(),\r\n        skip: jest.fn().mockReturnThis(),\r\n        take: jest.fn().mockReturnThis(),\r\n        getManyAndCount: jest.fn().mockResolvedValue([[mockAsset], 1]),\r\n      };\r\n\r\n      mockAssetRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);\r\n\r\n      const result = await service.findMany(options);\r\n\r\n      expect(mockAssetRepository.createQueryBuilder).toHaveBeenCalledWith('asset');\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.type IN (:...type)', { type: options.type });\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.status IN (:...status)', { status: options.status });\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.criticality IN (:...criticality)', { criticality: options.criticality });\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        '(asset.name ILIKE :search OR asset.description ILIKE :search OR asset.hostname ILIKE :search)',\r\n        { search: '%test%' },\r\n      );\r\n      expect(result).toEqual({\r\n        assets: [mockAsset],\r\n        total: 1,\r\n        page: 1,\r\n        totalPages: 1,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('updateAsset', () => {\r\n    it('should update asset successfully', async () => {\r\n      const assetId = 'asset-123';\r\n      const updateData = {\r\n        name: 'updated-server',\r\n        criticality: 'high' as const,\r\n      };\r\n      const userId = 'user-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockAsset as Asset);\r\n      mockAssetRepository.save.mockResolvedValue({ ...mockAsset, ...updateData });\r\n\r\n      const result = await service.updateAsset(assetId, updateData, userId);\r\n\r\n      expect(service.findById).toHaveBeenCalledWith(assetId);\r\n      expect(Object.assign).toHaveBeenCalledWith(mockAsset, updateData, { updatedBy: userId });\r\n      expect(mockAsset.calculateRiskScore).toHaveBeenCalled();\r\n      expect(mockAssetRepository.save).toHaveBeenCalledWith(mockAsset);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'update',\r\n        'asset',\r\n        assetId,\r\n        expect.objectContaining({\r\n          changes: updateData,\r\n        }),\r\n      );\r\n    });\r\n\r\n    it('should throw error when asset not found', async () => {\r\n      const assetId = 'non-existent';\r\n      const updateData = { name: 'updated-server' };\r\n      const userId = 'user-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(null);\r\n\r\n      await expect(service.updateAsset(assetId, updateData, userId))\r\n        .rejects.toThrow('Asset not found');\r\n    });\r\n  });\r\n\r\n  describe('deleteAsset', () => {\r\n    it('should delete asset successfully', async () => {\r\n      const assetId = 'asset-123';\r\n      const userId = 'user-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockAsset as Asset);\r\n      mockAssetRepository.remove.mockResolvedValue(mockAsset);\r\n\r\n      await service.deleteAsset(assetId, userId);\r\n\r\n      expect(service.findById).toHaveBeenCalledWith(assetId);\r\n      expect(mockAssetRepository.remove).toHaveBeenCalledWith(mockAsset);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'delete',\r\n        'asset',\r\n        assetId,\r\n        expect.objectContaining({\r\n          name: mockAsset.name,\r\n          type: mockAsset.type,\r\n          ipAddress: mockAsset.ipAddress,\r\n        }),\r\n      );\r\n    });\r\n\r\n    it('should throw error when asset not found', async () => {\r\n      const assetId = 'non-existent';\r\n      const userId = 'user-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(null);\r\n\r\n      await expect(service.deleteAsset(assetId, userId))\r\n        .rejects.toThrow('Asset not found');\r\n    });\r\n  });\r\n\r\n  describe('discoverAssets', () => {\r\n    it('should discover and create new assets', async () => {\r\n      const discoveryData = [\r\n        {\r\n          ipAddress: '*************',\r\n          hostname: 'new-server.company.com',\r\n          macAddress: '00:1B:44:11:3A:B8',\r\n          operatingSystem: { name: 'Ubuntu', version: '20.04' },\r\n          services: [{ name: 'ssh', port: 22, protocol: 'tcp', state: 'open' }],\r\n          discoveryMethod: 'network_scan',\r\n        },\r\n      ];\r\n      const userId = 'user-123';\r\n\r\n      mockAssetRepository.findOne.mockResolvedValue(null); // No existing asset\r\n      mockAssetRepository.create.mockReturnValue(mockAsset);\r\n      mockAssetRepository.save.mockResolvedValue(mockAsset);\r\n\r\n      const result = await service.discoverAssets(discoveryData, userId);\r\n\r\n      expect(mockAssetRepository.findOne).toHaveBeenCalledWith({\r\n        where: { ipAddress: discoveryData[0].ipAddress },\r\n      });\r\n      expect(mockAssetRepository.create).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          name: discoveryData[0].hostname,\r\n          type: 'server', // Determined from services\r\n          ipAddress: discoveryData[0].ipAddress,\r\n          hostname: discoveryData[0].hostname,\r\n          macAddress: discoveryData[0].macAddress,\r\n          operatingSystem: discoveryData[0].operatingSystem,\r\n          services: discoveryData[0].services,\r\n          discoveryMethod: discoveryData[0].discoveryMethod,\r\n          createdBy: userId,\r\n        }),\r\n      );\r\n      expect(result).toEqual([mockAsset]);\r\n    });\r\n\r\n    it('should update existing assets during discovery', async () => {\r\n      const discoveryData = [\r\n        {\r\n          ipAddress: '*************',\r\n          hostname: 'updated-server.company.com',\r\n          discoveryMethod: 'network_scan',\r\n        },\r\n      ];\r\n      const userId = 'user-123';\r\n\r\n      mockAssetRepository.findOne.mockResolvedValue(mockAsset); // Existing asset\r\n      mockAssetRepository.save.mockResolvedValue(mockAsset);\r\n\r\n      const result = await service.discoverAssets(discoveryData, userId);\r\n\r\n      expect(mockAsset.updateLastSeen).toHaveBeenCalled();\r\n      expect(mockAssetRepository.save).toHaveBeenCalledWith(mockAsset);\r\n      expect(result).toEqual([mockAsset]);\r\n    });\r\n  });\r\n\r\n  describe('updateVulnerabilityCounts', () => {\r\n    it('should update vulnerability counts and risk score', async () => {\r\n      const assetId = 'asset-123';\r\n      const mockVulnCounts = [\r\n        { severity: 'critical', count: '2' },\r\n        { severity: 'high', count: '5' },\r\n        { severity: 'medium', count: '10' },\r\n      ];\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockAsset as Asset);\r\n      \r\n      const mockQueryBuilder = {\r\n        select: jest.fn().mockReturnThis(),\r\n        addSelect: jest.fn().mockReturnThis(),\r\n        where: jest.fn().mockReturnThis(),\r\n        andWhere: jest.fn().mockReturnThis(),\r\n        groupBy: jest.fn().mockReturnThis(),\r\n        getRawMany: jest.fn().mockResolvedValue(mockVulnCounts),\r\n      };\r\n\r\n      mockVulnerabilityRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);\r\n      mockAssetRepository.save.mockResolvedValue(mockAsset);\r\n\r\n      const result = await service.updateVulnerabilityCounts(assetId);\r\n\r\n      expect(service.findById).toHaveBeenCalledWith(assetId);\r\n      expect(mockVulnerabilityRepository.createQueryBuilder).toHaveBeenCalledWith('vuln');\r\n      expect(mockAsset.updateVulnerabilityCounts).toHaveBeenCalledWith({\r\n        critical: 2,\r\n        high: 5,\r\n        medium: 10,\r\n        low: 0,\r\n        info: 0,\r\n        total: 17,\r\n      });\r\n      expect(mockAssetRepository.save).toHaveBeenCalledWith(mockAsset);\r\n      expect(result).toEqual(mockAsset);\r\n    });\r\n  });\r\n\r\n  describe('getAssetsDueForScanning', () => {\r\n    it('should return assets due for scanning', async () => {\r\n      const mockQueryBuilder = {\r\n        where: jest.fn().mockReturnThis(),\r\n        andWhere: jest.fn().mockReturnThis(),\r\n        orderBy: jest.fn().mockReturnThis(),\r\n        addOrderBy: jest.fn().mockReturnThis(),\r\n        getMany: jest.fn().mockResolvedValue([mockAsset]),\r\n      };\r\n\r\n      mockAssetRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);\r\n\r\n      const result = await service.getAssetsDueForScanning();\r\n\r\n      expect(mockAssetRepository.createQueryBuilder).toHaveBeenCalledWith('asset');\r\n      expect(mockQueryBuilder.where).toHaveBeenCalledWith('asset.isScannable = :scannable', { scannable: true });\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.status = :status', { status: 'active' });\r\n      expect(result).toEqual([mockAsset]);\r\n    });\r\n  });\r\n\r\n  describe('getStatistics', () => {\r\n    it('should return asset statistics', async () => {\r\n      mockAssetRepository.count\r\n        .mockResolvedValueOnce(100) // total\r\n        .mockResolvedValueOnce(85)  // active\r\n        .mockResolvedValueOnce(15); // critical\r\n\r\n      jest.spyOn(service as any, 'getAssetCountsByType').mockResolvedValue({\r\n        server: 50,\r\n        workstation: 30,\r\n        network_device: 20,\r\n      });\r\n\r\n      jest.spyOn(service as any, 'getAssetCountsByEnvironment').mockResolvedValue({\r\n        production: 60,\r\n        staging: 25,\r\n        development: 15,\r\n      });\r\n\r\n      jest.spyOn(service as any, 'getAssetsWithVulnerabilities').mockResolvedValue(45);\r\n\r\n      const result = await service.getStatistics();\r\n\r\n      expect(result).toEqual({\r\n        total: 100,\r\n        active: 85,\r\n        critical: 15,\r\n        byType: {\r\n          server: 50,\r\n          workstation: 30,\r\n          network_device: 20,\r\n        },\r\n        byEnvironment: {\r\n          production: 60,\r\n          staging: 25,\r\n          development: 15,\r\n        },\r\n        withVulnerabilities: 45,\r\n        timestamp: expect.any(String),\r\n      });\r\n    });\r\n  });\r\n});\r\n"], "version": 3}