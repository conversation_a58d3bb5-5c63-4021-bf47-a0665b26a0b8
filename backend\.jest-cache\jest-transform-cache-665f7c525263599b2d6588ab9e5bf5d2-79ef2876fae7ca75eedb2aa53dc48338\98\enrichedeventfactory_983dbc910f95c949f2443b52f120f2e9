c97daca7ff87ea78a01c92d77a1489c6
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnrichedEventFactory = void 0;
const enriched_event_entity_1 = require("../entities/enriched-event.entity");
const event_severity_enum_1 = require("../enums/event-severity.enum");
const event_processing_status_enum_1 = require("../enums/event-processing-status.enum");
const enrichment_source_enum_1 = require("../enums/enrichment-source.enum");
/**
 * EnrichedEvent Factory
 *
 * Factory class for creating EnrichedEvent entities with proper validation and defaults.
 * Handles complex enrichment scenarios and ensures all business rules are applied.
 *
 * Key responsibilities:
 * - Create enriched events from normalized events
 * - Apply enrichment rules and validation
 * - Calculate enrichment quality scores
 * - Determine manual review requirements
 * - Handle batch enrichment operations
 * - Manage threat intelligence integration
 * - Support various enrichment sources and contexts
 */
class EnrichedEventFactory {
    /**
     * Create a new EnrichedEvent from a NormalizedEvent
     */
    static create(options) {
        const normalizedEvent = options.normalizedEvent;
        // Build enriched event properties
        const enrichedEventProps = {
            normalizedEventId: normalizedEvent.id,
            metadata: normalizedEvent.metadata,
            type: options.type || normalizedEvent.type,
            severity: options.severity || normalizedEvent.severity,
            status: options.status || normalizedEvent.status,
            processingStatus: options.processingStatus || event_processing_status_enum_1.EventProcessingStatus.ENRICHED,
            enrichmentStatus: options.enrichmentStatus || enriched_event_entity_1.EnrichmentStatus.PENDING,
            normalizedData: normalizedEvent.normalizedData,
            enrichedData: options.enrichedData,
            title: options.title || normalizedEvent.title,
            description: options.description || normalizedEvent.description,
            tags: options.tags || normalizedEvent.tags,
            riskScore: options.riskScore || normalizedEvent.riskScore,
            confidenceLevel: options.confidenceLevel || normalizedEvent.confidenceLevel,
            attributes: {
                ...normalizedEvent.attributes,
                ...options.attributes,
            },
            correlationId: normalizedEvent.correlationId,
            parentEventId: normalizedEvent.parentEventId,
            appliedRules: options.appliedRules || [],
            enrichmentData: options.enrichmentData || [],
            enrichmentQualityScore: options.enrichmentQualityScore,
            threatIntelScore: options.threatIntelScore,
            assetContext: options.assetContext,
            userContext: options.userContext,
            networkContext: options.networkContext,
            geolocationContext: options.geolocationContext,
            reputationScores: options.reputationScores,
            requiresManualReview: options.forceManualReview,
            validationErrors: options.validationErrors,
            enrichmentAttempts: 0,
        };
        return enriched_event_entity_1.EnrichedEvent.create(enrichedEventProps, options.id);
    }
    /**
     * Create an EnrichedEvent with automatic enrichment
     */
    static createWithEnrichment(normalizedEvent, config = {}) {
        const fullConfig = EnrichedEventFactory.getDefaultConfig(config);
        // Apply enrichment rules
        const enrichmentResult = EnrichedEventFactory.applyEnrichmentRules(normalizedEvent.normalizedData, fullConfig.availableRules, fullConfig.enabledSources);
        // Calculate enrichment quality score
        const enrichmentQualityScore = EnrichedEventFactory.calculateEnrichmentQualityScore(enrichmentResult, fullConfig);
        // Calculate threat intelligence score
        const threatIntelScore = EnrichedEventFactory.calculateThreatIntelScore(enrichmentResult.enrichmentData);
        // Determine if manual review is required
        const requiresManualReview = EnrichedEventFactory.determineManualReviewRequirement(normalizedEvent, enrichmentQualityScore, threatIntelScore, enrichmentResult.validationErrors, fullConfig);
        return EnrichedEventFactory.create({
            normalizedEvent,
            enrichedData: enrichmentResult.enrichedData,
            appliedRules: enrichmentResult.appliedRules,
            enrichmentData: enrichmentResult.enrichmentData,
            enrichmentStatus: enrichmentResult.success
                ? enriched_event_entity_1.EnrichmentStatus.COMPLETED
                : enriched_event_entity_1.EnrichmentStatus.FAILED,
            enrichmentQualityScore,
            threatIntelScore,
            assetContext: enrichmentResult.assetContext,
            userContext: enrichmentResult.userContext,
            networkContext: enrichmentResult.networkContext,
            geolocationContext: enrichmentResult.geolocationContext,
            reputationScores: enrichmentResult.reputationScores,
            validationErrors: enrichmentResult.validationErrors,
            forceManualReview: requiresManualReview,
        });
    }
    /**
     * Create an EnrichedEvent with threat intelligence focus
     */
    static createWithThreatIntelligence(normalizedEvent, threatIntelOptions) {
        // Apply threat intelligence specific enrichment
        const threatIntelResult = EnrichedEventFactory.applyThreatIntelligenceEnrichment(normalizedEvent.normalizedData, threatIntelOptions);
        // Calculate threat intelligence score
        const threatIntelScore = EnrichedEventFactory.calculateThreatIntelScore(threatIntelResult.enrichmentData);
        // Build enriched data with threat intelligence context
        const enrichedData = {
            ...normalizedEvent.normalizedData,
            ...threatIntelResult.enrichedData,
            threat_intelligence: {
                score: threatIntelScore,
                sources_used: threatIntelResult.sourcesUsed,
                indicators_found: threatIntelResult.indicatorsFound,
                confidence: threatIntelResult.confidence,
            },
        };
        return EnrichedEventFactory.create({
            normalizedEvent,
            enrichedData,
            appliedRules: threatIntelResult.appliedRules,
            enrichmentData: threatIntelResult.enrichmentData,
            enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
            threatIntelScore,
            reputationScores: threatIntelResult.reputationScores,
            geolocationContext: threatIntelResult.geolocationContext,
            enrichmentQualityScore: threatIntelResult.qualityScore,
        });
    }
    /**
     * Create multiple EnrichedEvents in batch
     */
    static createBatch(options) {
        const startTime = Date.now();
        const successful = [];
        const failed = [];
        for (const normalizedEvent of options.normalizedEvents) {
            try {
                const enrichedEvent = EnrichedEventFactory.create({
                    normalizedEvent,
                    enrichedData: EnrichedEventFactory.enrichEventData(normalizedEvent.normalizedData, options.sources),
                    appliedRules: options.rules,
                });
                successful.push(enrichedEvent);
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                failed.push({ normalizedEvent, error: errorMessage });
                if (options.stopOnFailure) {
                    break;
                }
            }
        }
        const processingTimeMs = Date.now() - startTime;
        return {
            successful,
            failed,
            summary: {
                total: options.normalizedEvents.length,
                successful: successful.length,
                failed: failed.length,
                processingTimeMs,
            },
        };
    }
    /**
     * Create an EnrichedEvent for testing purposes
     */
    static createForTesting(overrides = {}) {
        // Create a mock normalized event if not provided
        const mockNormalizedEvent = overrides.normalizedEvent ||
            EnrichedEventFactory.createMockNormalizedEvent();
        const defaultOptions = {
            normalizedEvent: mockNormalizedEvent,
            enrichedData: {
                ...mockNormalizedEvent.normalizedData,
                enriched: true,
                enrichment_timestamp: new Date().toISOString(),
                threat_intelligence: {
                    score: 45,
                    sources_used: 3,
                    indicators_found: 2,
                },
                reputation: {
                    ip_reputation: 75,
                    domain_reputation: 80,
                },
                geolocation: {
                    country: 'US',
                    city: 'New York',
                    coordinates: { lat: 40.7128, lng: -74.0060 },
                },
            },
            appliedRules: [],
            enrichmentData: [
                {
                    source: enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION,
                    type: 'reputation',
                    data: { score: 75, category: 'clean' },
                    confidence: 85,
                    timestamp: new Date(),
                },
                {
                    source: enrichment_source_enum_1.EnrichmentSource.IP_GEOLOCATION,
                    type: 'geolocation',
                    data: { country: 'US', city: 'New York' },
                    confidence: 95,
                    timestamp: new Date(),
                },
            ],
            enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
            enrichmentQualityScore: 85,
            threatIntelScore: 45,
        };
        return EnrichedEventFactory.create({
            ...defaultOptions,
            ...overrides,
        });
    }
    // Private helper methods
    static getDefaultConfig(config) {
        return {
            availableRules: [],
            minEnrichmentQualityThreshold: EnrichedEventFactory.DEFAULT_MIN_ENRICHMENT_QUALITY,
            requireManualReviewForHighRisk: true,
            requireManualReviewForCritical: true,
            maxValidationErrors: EnrichedEventFactory.DEFAULT_MAX_VALIDATION_ERRORS,
            enrichmentTimeoutMs: EnrichedEventFactory.DEFAULT_ENRICHMENT_TIMEOUT,
            enabledSources: Object.values(enrichment_source_enum_1.EnrichmentSource),
            sourcePriorities: {},
            maxConcurrentRequests: EnrichedEventFactory.DEFAULT_MAX_CONCURRENT_REQUESTS,
            ...config,
        };
    }
    static applyEnrichmentRules(normalizedData, rules, enabledSources) {
        const enrichedData = { ...normalizedData };
        const appliedRules = [];
        const enrichmentData = [];
        const validationErrors = [];
        // Sort rules by priority (higher priority first)
        const sortedRules = [...rules].sort((a, b) => b.priority - a.priority);
        for (const rule of sortedRules) {
            try {
                // Check if rule sources are enabled
                const enabledRuleSources = rule.sources.filter(source => enabledSources.includes(source));
                if (enabledRuleSources.length === 0) {
                    continue; // Skip rule if no sources are enabled
                }
                // Apply rule logic (simplified implementation)
                const ruleResult = EnrichedEventFactory.applyRule(enrichedData, rule);
                if (ruleResult.success) {
                    appliedRules.push(rule);
                    Object.assign(enrichedData, ruleResult.data);
                    if (ruleResult.enrichmentData) {
                        enrichmentData.push(...ruleResult.enrichmentData);
                    }
                }
                else if (rule.required) {
                    validationErrors.push(`Required rule '${rule.name}' failed: ${ruleResult.error}`);
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                if (rule.required) {
                    validationErrors.push(`Required rule '${rule.name}' threw error: ${errorMessage}`);
                }
            }
        }
        // Extract context information
        const contexts = EnrichedEventFactory.extractContexts(enrichmentData);
        return {
            success: validationErrors.length === 0,
            enrichedData,
            appliedRules,
            enrichmentData,
            validationErrors,
            ...contexts,
        };
    }
    static applyRule(data, rule) {
        // Simplified implementation - in practice, this would be more sophisticated
        switch (rule.id) {
            case 'ip_reputation_enrichment':
                return EnrichedEventFactory.enrichWithIPReputation(data);
            case 'geolocation_enrichment':
                return EnrichedEventFactory.enrichWithGeolocation(data);
            case 'threat_intel_enrichment':
                return EnrichedEventFactory.enrichWithThreatIntel(data);
            case 'asset_context_enrichment':
                return EnrichedEventFactory.enrichWithAssetContext(data);
            default:
                return { success: true, data };
        }
    }
    static enrichWithIPReputation(data) {
        const ipAddress = data.source_ip || data.destination_ip || data.ip_address;
        if (!ipAddress) {
            return { success: false, error: 'No IP address found for reputation check' };
        }
        // Mock reputation data
        const reputationScore = Math.floor(Math.random() * 100);
        const category = reputationScore > 70 ? 'clean' : reputationScore > 30 ? 'suspicious' : 'malicious';
        const enrichmentData = {
            source: enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION,
            type: 'reputation',
            data: {
                ip_address: ipAddress,
                reputation_score: reputationScore,
                category,
                last_seen: new Date().toISOString(),
            },
            confidence: 85,
            timestamp: new Date(),
        };
        return {
            success: true,
            data: {
                ...data,
                ip_reputation: {
                    score: reputationScore,
                    category,
                },
            },
            enrichmentData: [enrichmentData],
        };
    }
    static enrichWithGeolocation(data) {
        const ipAddress = data.source_ip || data.destination_ip || data.ip_address;
        if (!ipAddress) {
            return { success: false, error: 'No IP address found for geolocation' };
        }
        // Mock geolocation data
        const countries = ['US', 'GB', 'DE', 'FR', 'JP', 'CN', 'RU'];
        const country = countries[Math.floor(Math.random() * countries.length)];
        const cities = ['New York', 'London', 'Berlin', 'Paris', 'Tokyo', 'Beijing', 'Moscow'];
        const city = cities[Math.floor(Math.random() * cities.length)];
        const enrichmentData = {
            source: enrichment_source_enum_1.EnrichmentSource.IP_GEOLOCATION,
            type: 'geolocation',
            data: {
                ip_address: ipAddress,
                country,
                city,
                coordinates: {
                    lat: Math.random() * 180 - 90,
                    lng: Math.random() * 360 - 180,
                },
            },
            confidence: 95,
            timestamp: new Date(),
        };
        return {
            success: true,
            data: {
                ...data,
                geolocation: {
                    country,
                    city,
                },
            },
            enrichmentData: [enrichmentData],
        };
    }
    static enrichWithThreatIntel(data) {
        // Mock threat intelligence enrichment
        const threatScore = Math.floor(Math.random() * 100);
        const indicators = Math.floor(Math.random() * 5);
        const enrichmentData = {
            source: enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL,
            type: 'threat_intelligence',
            data: {
                threat_score: threatScore,
                indicators_found: indicators,
                threat_types: ['malware', 'phishing'],
                last_updated: new Date().toISOString(),
            },
            confidence: 80,
            timestamp: new Date(),
        };
        return {
            success: true,
            data: {
                ...data,
                threat_intelligence: {
                    score: threatScore,
                    indicators_found: indicators,
                },
            },
            enrichmentData: [enrichmentData],
        };
    }
    static enrichWithAssetContext(data) {
        // Mock asset context enrichment
        const assetId = data.asset_id || data.hostname || 'unknown';
        const enrichmentData = {
            source: enrichment_source_enum_1.EnrichmentSource.ASSET_MANAGEMENT,
            type: 'asset_context',
            data: {
                asset_id: assetId,
                owner: 'IT Department',
                criticality: 'high',
                location: 'Data Center 1',
                os: 'Windows Server 2019',
            },
            confidence: 90,
            timestamp: new Date(),
        };
        return {
            success: true,
            data: {
                ...data,
                asset_context: {
                    owner: 'IT Department',
                    criticality: 'high',
                },
            },
            enrichmentData: [enrichmentData],
        };
    }
    static applyThreatIntelligenceEnrichment(normalizedData, options) {
        // Simplified threat intelligence enrichment
        const enrichedData = { ...normalizedData };
        const enrichmentData = [];
        const reputationScores = {};
        // Mock threat intelligence data
        for (const source of options.sources) {
            const mockData = {
                source,
                type: 'threat_intelligence',
                data: {
                    threat_score: Math.floor(Math.random() * 100),
                    indicators: Math.floor(Math.random() * 10),
                    confidence: Math.floor(Math.random() * 100),
                },
                confidence: Math.floor(Math.random() * 100),
                timestamp: new Date(),
            };
            if (mockData.confidence >= options.minConfidence) {
                enrichmentData.push(mockData);
                reputationScores[source] = mockData.data.threat_score;
            }
        }
        return {
            enrichedData,
            appliedRules: options.customRules || [],
            enrichmentData,
            sourcesUsed: enrichmentData.length,
            indicatorsFound: enrichmentData.reduce((sum, data) => sum + (data.data.indicators || 0), 0),
            confidence: enrichmentData.length > 0
                ? enrichmentData.reduce((sum, data) => sum + data.confidence, 0) / enrichmentData.length
                : 0,
            reputationScores,
            geolocationContext: options.includeGeolocation ? { country: 'US', city: 'New York' } : {},
            qualityScore: 85,
        };
    }
    static calculateEnrichmentQualityScore(result, config) {
        let score = 100;
        // Reduce score for validation errors
        score -= result.validationErrors.length * 15;
        // Reduce score if enrichment failed
        if (!result.success) {
            score -= 25;
        }
        // Reduce score based on missing required rules
        const requiredRules = config.availableRules.filter(rule => rule.required);
        const appliedRequiredRules = result.appliedRules.filter(rule => rule.required);
        const missingRequiredRules = requiredRules.length - appliedRequiredRules.length;
        score -= missingRequiredRules * 20;
        return Math.max(0, Math.min(100, score));
    }
    static calculateThreatIntelScore(enrichmentData) {
        const threatIntelData = enrichmentData.filter(data => data.type === 'threat_intelligence' ||
            data.source === enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL ||
            data.source === enrichment_source_enum_1.EnrichmentSource.OSINT);
        if (threatIntelData.length === 0)
            return 0;
        const scores = threatIntelData.map(data => data.data.threat_score || 0);
        const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        // Weight by confidence
        const confidenceWeight = threatIntelData.reduce((sum, data) => sum + data.confidence, 0) / threatIntelData.length / 100;
        return Math.floor(avgScore * confidenceWeight);
    }
    static determineManualReviewRequirement(normalizedEvent, enrichmentQualityScore, threatIntelScore, validationErrors, config) {
        // High threat intelligence score requires manual review
        if (threatIntelScore >= 85) {
            return true;
        }
        // High-risk events require manual review
        if (config.requireManualReviewForHighRisk && normalizedEvent.isHighRisk()) {
            return true;
        }
        // Critical events require manual review
        if (config.requireManualReviewForCritical && normalizedEvent.severity === event_severity_enum_1.EventSeverity.CRITICAL) {
            return true;
        }
        // Low enrichment quality requires manual review
        if (enrichmentQualityScore < config.minEnrichmentQualityThreshold) {
            return true;
        }
        // Too many validation errors require manual review
        if (validationErrors.length > config.maxValidationErrors) {
            return true;
        }
        return false;
    }
    static extractContexts(enrichmentData) {
        const contexts = {};
        for (const data of enrichmentData) {
            switch (data.type) {
                case 'asset_context':
                    contexts.assetContext = { ...contexts.assetContext, ...data.data };
                    break;
                case 'user_context':
                    contexts.userContext = { ...contexts.userContext, ...data.data };
                    break;
                case 'network_context':
                    contexts.networkContext = { ...contexts.networkContext, ...data.data };
                    break;
                case 'geolocation':
                    contexts.geolocationContext = { ...contexts.geolocationContext, ...data.data };
                    break;
                case 'reputation':
                    if (!contexts.reputationScores)
                        contexts.reputationScores = {};
                    contexts.reputationScores[data.source] = data.data.reputation_score || data.data.score;
                    break;
            }
        }
        return contexts;
    }
    static enrichEventData(normalizedData, sources) {
        // Basic enrichment - in practice this would be more sophisticated
        return {
            ...normalizedData,
            enriched_at: new Date().toISOString(),
            enrichment_sources: sources || [],
        };
    }
    static createMockNormalizedEvent() {
        // This would create a proper mock NormalizedEvent - simplified for now
        // In practice, you'd use the NormalizedEventFactory to create a proper mock
        return {};
    }
}
exports.EnrichedEventFactory = EnrichedEventFactory;
EnrichedEventFactory.DEFAULT_MIN_ENRICHMENT_QUALITY = 70;
EnrichedEventFactory.DEFAULT_MAX_VALIDATION_ERRORS = 10;
EnrichedEventFactory.DEFAULT_ENRICHMENT_TIMEOUT = 60000; // 60 seconds
EnrichedEventFactory.DEFAULT_MAX_CONCURRENT_REQUESTS = 10;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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