{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\__tests__\\security-headers.middleware.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAA+C;AAE/C,gFAA2E;AAC3E,8CAA0C;AAC1C,gDAA4C;AAE5C,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;IACzC,IAAI,UAAqC,CAAC;IAC1C,IAAI,aAAyC,CAAC;IAC9C,IAAI,SAAiC,CAAC;IACtC,IAAI,UAAmC,CAAC;IACxC,IAAI,WAA6B,CAAC;IAClC,IAAI,YAA+B,CAAC;IACpC,IAAI,YAAuB,CAAC;IAE5B,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,iBAAiB,GAAG;YACxB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;SACf,CAAC;QAEF,MAAM,aAAa,GAAG;YACpB,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;SAC7B,CAAC;QAEF,MAAM,cAAc,GAAG;YACrB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;SAC9B,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,uDAAyB;gBACzB;oBACE,OAAO,EAAE,sBAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD;oBACE,OAAO,EAAE,sBAAS;oBAClB,QAAQ,EAAE,aAAa;iBACxB;gBACD;oBACE,OAAO,EAAE,wBAAU;oBACnB,QAAQ,EAAE,cAAc;iBACzB;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,UAAU,GAAG,MAAM,CAAC,GAAG,CAA4B,uDAAyB,CAAC,CAAC;QAC9E,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAa,CAAC,CAAC;QAC1C,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAS,CAAC,CAAC;QAClC,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,wBAAU,CAAC,CAAC;QAEpC,cAAc;QACd,WAAW,GAAG;YACZ,IAAI,EAAE,cAAc;YACpB,OAAO,EAAE,EAAE;YACX,MAAM,EAAE,KAAK;SACd,CAAC;QAEF,YAAY,GAAG;YACb,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;YACpB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;SACxB,CAAC;QAEF,YAAY,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QAEzB,+BAA+B;QAC/B,SAAS,CAAC,iBAAiB,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;QAClE,UAAU,CAAC,kBAAkB,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;QAClE,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;YACzD,IAAI,GAAG,KAAK,aAAa;gBAAE,OAAO,OAAO,CAAC;YAC1C,OAAO,YAAY,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE;QACnB,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;YAE/E,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,yBAAyB,EAAE,oBAAoB,CAAC,CAAC;YACrG,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAC/E,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;YACzF,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;YACzF,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,iBAAiB,EAAE,iCAAiC,CAAC,CAAC;YAC1G,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YACrF,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;YACpF,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,mCAAmC,EAAE,MAAM,CAAC,CAAC;YACjG,MAAM,CAAC,YAAY,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC;YAE1B,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;YAE/E,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,2BAA2B,EAAE,kBAAkB,CAAC,CAAC;QACvG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,WAAW,CAAC,OAAO,GAAG,EAAE,mBAAmB,EAAE,OAAO,EAAE,CAAC;YAEvD,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;YAE/E,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,2BAA2B,EAAE,kBAAkB,CAAC,CAAC;QACvG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC;YAC3B,WAAW,CAAC,OAAO,GAAG,EAAE,CAAC;YAEzB,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;YAE/E,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,2BAA2B,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,WAAW,CAAC,IAAI,GAAG,aAAa,CAAC;YAEjC,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;YAE/E,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,eAAe,EAAE,8CAA8C,CAAC,CAAC;YACrH,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC1E,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;YAE/E,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YACvE,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,aAAa;oBAAE,OAAO,OAAO,CAAC;gBAC1C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;YAE/E,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,WAAW,CAAC,OAAO,GAAG,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,CAAC;YAEpE,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;YAE/E,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,cAAc,EAAE,qBAAqB,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;YAE/E,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,cAAc,EAAE,MAAM,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC,CAAC;QACpH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;YAE/E,MAAM,qBAAqB,GAAI,YAAY,CAAC,SAAuB,CAAC,IAAI,CAAC,KAAK;iBAC3E,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,oBAAoB,CAAC,CAAC;YAElD,MAAM,CAAC,qBAAqB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAC/D,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YACxD,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,MAAM,kBAAkB,GAAG;YACzB,aAAa;YACb,cAAc;YACd,cAAc;YACd,oBAAoB;YACpB,sBAAsB;YACtB,kBAAkB;YAClB,oBAAoB;SACrB,CAAC;QAEF,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACpC,EAAE,CAAC,iBAAiB,QAAQ,eAAe,EAAE,GAAG,EAAE;gBAChD,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC;gBAE5B,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;gBAE/E,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,eAAe,EAAE,8CAA8C,CAAC,CAAC;YACvH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,qBAAqB,GAAG;YAC5B,gBAAgB;YAChB,yBAAyB;YACzB,iBAAiB;YACjB,UAAU;YACV,OAAO;SACR,CAAC;QAEF,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACvC,EAAE,CAAC,qBAAqB,QAAQ,eAAe,EAAE,GAAG,EAAE;gBACpD,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC;gBAE5B,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;gBAE/E,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,eAAe,EAAE,8CAA8C,CAAC,CAAC;YAC3H,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,SAAS,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,GAAG,EAAE;gBAClD,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE;gBACV,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,YAAY,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC;YAC1B,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,EAAE;gBACpD,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE;gBACV,UAAU,CAAC,GAAG,CAAC,WAAsB,EAAE,YAAwB,EAAE,YAAY,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,YAAY,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\__tests__\\security-headers.middleware.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { Request, Response } from 'express';\r\nimport { SecurityHeadersMiddleware } from '../security-headers.middleware';\r\nimport { CspConfig } from '../csp.config';\r\nimport { HstsConfig } from '../hsts.config';\r\n\r\ndescribe('SecurityHeadersMiddleware', () => {\r\n  let middleware: SecurityHeadersMiddleware;\r\n  let configService: jest.Mocked<ConfigService>;\r\n  let cspConfig: jest.Mocked<CspConfig>;\r\n  let hstsConfig: jest.Mocked<HstsConfig>;\r\n  let mockRequest: Partial<Request>;\r\n  let mockResponse: Partial<Response>;\r\n  let nextFunction: jest.Mock;\r\n\r\n  beforeEach(async () => {\r\n    const mockConfigService = {\r\n      get: jest.fn(),\r\n    };\r\n\r\n    const mockCspConfig = {\r\n      generateCSPHeader: jest.fn(),\r\n    };\r\n\r\n    const mockHstsConfig = {\r\n      generateHSTSHeader: jest.fn(),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        SecurityHeadersMiddleware,\r\n        {\r\n          provide: ConfigService,\r\n          useValue: mockConfigService,\r\n        },\r\n        {\r\n          provide: CspConfig,\r\n          useValue: mockCspConfig,\r\n        },\r\n        {\r\n          provide: HstsConfig,\r\n          useValue: mockHstsConfig,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    middleware = module.get<SecurityHeadersMiddleware>(SecurityHeadersMiddleware);\r\n    configService = module.get(ConfigService);\r\n    cspConfig = module.get(CspConfig);\r\n    hstsConfig = module.get(HstsConfig);\r\n\r\n    // Setup mocks\r\n    mockRequest = {\r\n      path: '/api/v1/test',\r\n      headers: {},\r\n      secure: false,\r\n    };\r\n\r\n    mockResponse = {\r\n      setHeader: jest.fn(),\r\n      removeHeader: jest.fn(),\r\n    };\r\n\r\n    nextFunction = jest.fn();\r\n\r\n    // Default mock implementations\r\n    cspConfig.generateCSPHeader.mockReturnValue(\"default-src 'self'\");\r\n    hstsConfig.generateHSTSHeader.mockReturnValue('max-age=31536000');\r\n    configService.get.mockImplementation((key, defaultValue) => {\r\n      if (key === 'app.version') return '1.0.0';\r\n      return defaultValue;\r\n    });\r\n  });\r\n\r\n  describe('use', () => {\r\n    it('should set all required security headers', () => {\r\n      middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('Content-Security-Policy', \"default-src 'self'\");\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Frame-Options', 'DENY');\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Content-Type-Options', 'nosniff');\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-XSS-Protection', '1; mode=block');\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('Referrer-Policy', 'strict-origin-when-cross-origin');\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-DNS-Prefetch-Control', 'off');\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Download-Options', 'noopen');\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Permitted-Cross-Domain-Policies', 'none');\r\n      expect(nextFunction).toHaveBeenCalled();\r\n    });\r\n\r\n    it('should set HSTS header for HTTPS requests', () => {\r\n      mockRequest.secure = true;\r\n\r\n      middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('Strict-Transport-Security', 'max-age=31536000');\r\n    });\r\n\r\n    it('should set HSTS header for requests with x-forwarded-proto https', () => {\r\n      mockRequest.headers = { 'x-forwarded-proto': 'https' };\r\n\r\n      middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('Strict-Transport-Security', 'max-age=31536000');\r\n    });\r\n\r\n    it('should not set HSTS header for HTTP requests', () => {\r\n      mockRequest.secure = false;\r\n      mockRequest.headers = {};\r\n\r\n      middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n\r\n      expect(mockResponse.setHeader).not.toHaveBeenCalledWith('Strict-Transport-Security', expect.any(String));\r\n    });\r\n\r\n    it('should set cache control headers for sensitive endpoints', () => {\r\n      mockRequest.path = '/auth/login';\r\n\r\n      middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('Cache-Control', 'no-store, no-cache, must-revalidate, private');\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('Pragma', 'no-cache');\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('Expires', '0');\r\n    });\r\n\r\n    it('should remove server identification headers', () => {\r\n      middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n\r\n      expect(mockResponse.removeHeader).toHaveBeenCalledWith('X-Powered-By');\r\n      expect(mockResponse.removeHeader).toHaveBeenCalledWith('Server');\r\n    });\r\n\r\n    it('should set API version header', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'app.version') return '2.1.0';\r\n        return defaultValue;\r\n      });\r\n\r\n      middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-API-Version', '2.1.0');\r\n    });\r\n\r\n    it('should use correlation ID from request headers', () => {\r\n      mockRequest.headers = { 'x-correlation-id': 'test-correlation-id' };\r\n\r\n      middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Request-ID', 'test-correlation-id');\r\n    });\r\n\r\n    it('should generate request ID when correlation ID is not present', () => {\r\n      middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n\r\n      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Request-ID', expect.stringMatching(/^req_\\d+_[a-z0-9]+$/));\r\n    });\r\n\r\n    it('should set comprehensive Permissions Policy', () => {\r\n      middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n\r\n      const permissionsPolicyCall = (mockResponse.setHeader as jest.Mock).mock.calls\r\n        .find(call => call[0] === 'Permissions-Policy');\r\n      \r\n      expect(permissionsPolicyCall).toBeDefined();\r\n      expect(permissionsPolicyCall[1]).toContain('accelerometer=()');\r\n      expect(permissionsPolicyCall[1]).toContain('camera=()');\r\n      expect(permissionsPolicyCall[1]).toContain('microphone=()');\r\n    });\r\n  });\r\n\r\n  describe('sensitive endpoint detection', () => {\r\n    const sensitiveEndpoints = [\r\n      '/auth/login',\r\n      '/auth/logout',\r\n      '/admin/users',\r\n      '/api/v1/auth/token',\r\n      '/api/v2/auth/refresh',\r\n      '/health/detailed',\r\n      '/metrics/sensitive',\r\n    ];\r\n\r\n    sensitiveEndpoints.forEach(endpoint => {\r\n      it(`should detect ${endpoint} as sensitive`, () => {\r\n        mockRequest.path = endpoint;\r\n\r\n        middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n\r\n        expect(mockResponse.setHeader).toHaveBeenCalledWith('Cache-Control', 'no-store, no-cache, must-revalidate, private');\r\n      });\r\n    });\r\n\r\n    const nonSensitiveEndpoints = [\r\n      '/api/v1/health',\r\n      '/api/v1/vulnerabilities',\r\n      '/api/v2/threats',\r\n      '/metrics',\r\n      '/docs',\r\n    ];\r\n\r\n    nonSensitiveEndpoints.forEach(endpoint => {\r\n      it(`should not detect ${endpoint} as sensitive`, () => {\r\n        mockRequest.path = endpoint;\r\n\r\n        middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n\r\n        expect(mockResponse.setHeader).not.toHaveBeenCalledWith('Cache-Control', 'no-store, no-cache, must-revalidate, private');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle CSP config errors gracefully', () => {\r\n      cspConfig.generateCSPHeader.mockImplementation(() => {\r\n        throw new Error('CSP configuration error');\r\n      });\r\n\r\n      expect(() => {\r\n        middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n      }).not.toThrow();\r\n\r\n      expect(nextFunction).toHaveBeenCalled();\r\n    });\r\n\r\n    it('should handle HSTS config errors gracefully', () => {\r\n      mockRequest.secure = true;\r\n      hstsConfig.generateHSTSHeader.mockImplementation(() => {\r\n        throw new Error('HSTS configuration error');\r\n      });\r\n\r\n      expect(() => {\r\n        middleware.use(mockRequest as Request, mockResponse as Response, nextFunction);\r\n      }).not.toThrow();\r\n\r\n      expect(nextFunction).toHaveBeenCalled();\r\n    });\r\n  });\r\n});"], "version": 3}