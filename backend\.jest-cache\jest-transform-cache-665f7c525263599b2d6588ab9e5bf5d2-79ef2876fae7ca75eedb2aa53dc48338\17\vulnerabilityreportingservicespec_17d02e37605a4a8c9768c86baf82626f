6b9c3c5889c3e9907837942cbe14e857
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const vulnerability_reporting_service_1 = require("../vulnerability-reporting.service");
const vulnerability_entity_1 = require("../../../domain/entities/vulnerability.entity");
const vulnerability_assessment_entity_1 = require("../../../domain/entities/vulnerability-assessment.entity");
const vulnerability_scan_entity_1 = require("../../../domain/entities/vulnerability-scan.entity");
const logger_service_1 = require("../../../../../infrastructure/logging/logger.service");
describe('VulnerabilityReportingService', () => {
    let service;
    let vulnerabilityRepository;
    let assessmentRepository;
    let scanRepository;
    let loggerService;
    const mockVulnerability = {
        id: 'vuln-123',
        identifier: 'CVE-2023-1234',
        title: 'Test Vulnerability',
        severity: 'high',
        cvssScore: 7.5,
        publishedDate: new Date('2023-01-01'),
        exploitable: true,
        hasExploit: false,
        patchAvailable: true,
    };
    const mockAssessment = {
        id: 'assessment-123',
        vulnerabilityId: 'vuln-123',
        status: 'completed',
        assessedSeverity: 'high',
        assessedCvssScore: 7.5,
        assessedAt: new Date('2023-01-02'),
        assessedBy: 'user-123',
    };
    const mockScan = {
        id: 'scan-123',
        name: 'Test Scan',
        scanType: 'network',
        status: 'completed',
        startedAt: new Date('2023-01-01'),
        completedAt: new Date('2023-01-01T02:00:00Z'),
        summary: {
            totalVulnerabilities: 10,
            criticalCount: 2,
            highCount: 3,
            mediumCount: 3,
            lowCount: 2,
        },
    };
    beforeEach(async () => {
        const mockRepository = {
            find: jest.fn(),
            findOne: jest.fn(),
            count: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
                select: jest.fn().mockReturnThis(),
                addSelect: jest.fn().mockReturnThis(),
                leftJoin: jest.fn().mockReturnThis(),
                leftJoinAndSelect: jest.fn().mockReturnThis(),
                where: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                groupBy: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                addOrderBy: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getRawMany: jest.fn(),
                getRawOne: jest.fn(),
                getMany: jest.fn(),
                getManyAndCount: jest.fn(),
            })),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                vulnerability_reporting_service_1.VulnerabilityReportingService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_entity_1.Vulnerability),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_assessment_entity_1.VulnerabilityAssessment),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_scan_entity_1.VulnerabilityScan),
                    useValue: mockRepository,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: {
                        debug: jest.fn(),
                        log: jest.fn(),
                        warn: jest.fn(),
                        error: jest.fn(),
                    },
                },
            ],
        }).compile();
        service = module.get(vulnerability_reporting_service_1.VulnerabilityReportingService);
        vulnerabilityRepository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_entity_1.Vulnerability));
        assessmentRepository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_assessment_entity_1.VulnerabilityAssessment));
        scanRepository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_scan_entity_1.VulnerabilityScan));
        loggerService = module.get(logger_service_1.LoggerService);
    });
    it('should be defined', () => {
        expect(service).toBeDefined();
    });
    describe('generateExecutiveSummary', () => {
        it('should generate executive summary report', async () => {
            const mockSeverityCounts = [
                { severity: 'critical', count: '5' },
                { severity: 'high', count: '10' },
                { severity: 'medium', count: '15' },
                { severity: 'low', count: '20' },
            ];
            const mockTrendData = [
                { date: '2023-01-01', count: '45' },
                { date: '2023-01-02', count: '50' },
            ];
            vulnerabilityRepository.createQueryBuilder().getRawMany
                .mockResolvedValueOnce(mockSeverityCounts)
                .mockResolvedValueOnce(mockTrendData);
            vulnerabilityRepository.count
                .mockResolvedValueOnce(3) // exploitable
                .mockResolvedValueOnce(2) // with exploits
                .mockResolvedValueOnce(25) // patched
                .mockResolvedValueOnce(8); // recent
            assessmentRepository.count
                .mockResolvedValueOnce(40) // completed assessments
                .mockResolvedValueOnce(5); // pending assessments
            scanRepository.count.mockResolvedValueOnce(12); // total scans
            const result = await service.generateExecutiveSummary();
            expect(result).toHaveProperty('summary');
            expect(result).toHaveProperty('breakdown');
            expect(result).toHaveProperty('trends');
            expect(result).toHaveProperty('assessmentMetrics');
            expect(result).toHaveProperty('scanMetrics');
            expect(result).toHaveProperty('generatedAt');
            expect(result.summary.total).toBe(50);
            expect(result.summary.critical).toBe(5);
            expect(result.summary.exploitable).toBe(3);
            expect(result.assessmentMetrics.completed).toBe(40);
        });
        it('should handle date range filters', async () => {
            const dateRange = {
                startDate: new Date('2023-01-01'),
                endDate: new Date('2023-01-31'),
            };
            vulnerabilityRepository.createQueryBuilder().getRawMany.mockResolvedValue([]);
            vulnerabilityRepository.count.mockResolvedValue(0);
            assessmentRepository.count.mockResolvedValue(0);
            scanRepository.count.mockResolvedValue(0);
            const result = await service.generateExecutiveSummary(dateRange);
            const queryBuilder = vulnerabilityRepository.createQueryBuilder();
            expect(queryBuilder.andWhere).toHaveBeenCalledWith('vuln.publishedDate >= :startDate', { startDate: dateRange.startDate });
            expect(queryBuilder.andWhere).toHaveBeenCalledWith('vuln.publishedDate <= :endDate', { endDate: dateRange.endDate });
        });
    });
    describe('generateDetailedReport', () => {
        it('should generate detailed vulnerability report', async () => {
            const reportOptions = {
                includeAssessments: true,
                includeScans: true,
                severityFilter: ['critical', 'high'],
                format: 'json',
            };
            vulnerabilityRepository.find.mockResolvedValue([mockVulnerability]);
            const result = await service.generateDetailedReport(reportOptions);
            expect(result).toHaveProperty('vulnerabilities');
            expect(result).toHaveProperty('metadata');
            expect(result.vulnerabilities).toHaveLength(1);
            expect(result.metadata.format).toBe('json');
            expect(result.metadata.includeAssessments).toBe(true);
            expect(vulnerabilityRepository.find).toHaveBeenCalledWith({
                where: expect.objectContaining({
                    severity: expect.any(Object),
                }),
                relations: expect.arrayContaining(['assessments']),
                order: { publishedDate: 'DESC' },
            });
        });
        it('should apply filters correctly', async () => {
            const reportOptions = {
                severityFilter: ['critical'],
                exploitableOnly: true,
                patchedOnly: false,
                dateRange: {
                    startDate: new Date('2023-01-01'),
                    endDate: new Date('2023-01-31'),
                },
            };
            vulnerabilityRepository.find.mockResolvedValue([]);
            await service.generateDetailedReport(reportOptions);
            expect(vulnerabilityRepository.find).toHaveBeenCalledWith({
                where: expect.objectContaining({
                    severity: expect.any(Object),
                    exploitable: true,
                    patchAvailable: false,
                    publishedDate: expect.any(Object),
                }),
                relations: [],
                order: { publishedDate: 'DESC' },
            });
        });
    });
    describe('generateComplianceReport', () => {
        it('should generate compliance report for NIST framework', async () => {
            const complianceOptions = {
                framework: 'nist',
                includeEvidence: true,
                includeRemediation: true,
            };
            vulnerabilityRepository.find.mockResolvedValue([
                {
                    ...mockVulnerability,
                    complianceFrameworks: {
                        nist: ['SC-7', 'SI-2'],
                    },
                },
            ]);
            assessmentRepository.find.mockResolvedValue([mockAssessment]);
            const result = await service.generateComplianceReport(complianceOptions);
            expect(result).toHaveProperty('framework');
            expect(result).toHaveProperty('controls');
            expect(result).toHaveProperty('summary');
            expect(result).toHaveProperty('vulnerabilities');
            expect(result.framework).toBe('nist');
            expect(result.controls).toContain('SC-7');
            expect(result.controls).toContain('SI-2');
        });
        it('should handle multiple compliance frameworks', async () => {
            const complianceOptions = {
                framework: 'iso27001',
            };
            vulnerabilityRepository.find.mockResolvedValue([
                {
                    ...mockVulnerability,
                    complianceFrameworks: {
                        iso27001: ['A.12.6.1', 'A.14.2.3'],
                    },
                },
            ]);
            const result = await service.generateComplianceReport(complianceOptions);
            expect(result.framework).toBe('iso27001');
            expect(result.controls).toContain('A.12.6.1');
            expect(result.controls).toContain('A.14.2.3');
        });
    });
    describe('generateTrendAnalysis', () => {
        it('should generate trend analysis report', async () => {
            const trendOptions = {
                period: 'monthly',
                metrics: ['total', 'severity', 'exploitable'],
                dateRange: {
                    startDate: new Date('2023-01-01'),
                    endDate: new Date('2023-12-31'),
                },
            };
            const mockTrendData = [
                { period: '2023-01', total: '45', critical: '5', high: '10' },
                { period: '2023-02', total: '50', critical: '6', high: '12' },
            ];
            vulnerabilityRepository.createQueryBuilder().getRawMany.mockResolvedValue(mockTrendData);
            const result = await service.generateTrendAnalysis(trendOptions);
            expect(result).toHaveProperty('period');
            expect(result).toHaveProperty('metrics');
            expect(result).toHaveProperty('data');
            expect(result).toHaveProperty('analysis');
            expect(result.period).toBe('monthly');
            expect(result.data).toHaveLength(2);
        });
        it('should calculate trend analysis correctly', async () => {
            const trendOptions = {
                period: 'weekly',
                metrics: ['total'],
            };
            const mockTrendData = [
                { period: '2023-W01', total: '40' },
                { period: '2023-W02', total: '45' },
                { period: '2023-W03', total: '50' },
            ];
            vulnerabilityRepository.createQueryBuilder().getRawMany.mockResolvedValue(mockTrendData);
            const result = await service.generateTrendAnalysis(trendOptions);
            expect(result.analysis).toHaveProperty('trend');
            expect(result.analysis).toHaveProperty('growthRate');
            expect(result.analysis.trend).toBe('increasing');
            expect(result.analysis.growthRate).toBeGreaterThan(0);
        });
    });
    describe('generateScanReport', () => {
        it('should generate scan performance report', async () => {
            const scanOptions = {
                includeScanDetails: true,
                includeVulnerabilities: true,
                scanTypes: ['network', 'web_application'],
            };
            scanRepository.find.mockResolvedValue([mockScan]);
            const mockScanStats = [
                { scanType: 'network', count: '5', avgDuration: '120' },
                { scanType: 'web_application', count: '3', avgDuration: '180' },
            ];
            scanRepository.createQueryBuilder().getRawMany.mockResolvedValue(mockScanStats);
            const result = await service.generateScanReport(scanOptions);
            expect(result).toHaveProperty('scans');
            expect(result).toHaveProperty('statistics');
            expect(result).toHaveProperty('performance');
            expect(result.scans).toHaveLength(1);
            expect(result.statistics).toHaveLength(2);
        });
    });
    describe('exportReport', () => {
        it('should export report in JSON format', async () => {
            const reportData = {
                title: 'Test Report',
                data: [mockVulnerability],
                generatedAt: new Date(),
            };
            const result = await service.exportReport(reportData, 'json');
            expect(result).toHaveProperty('format');
            expect(result).toHaveProperty('content');
            expect(result).toHaveProperty('filename');
            expect(result.format).toBe('json');
            expect(typeof result.content).toBe('string');
            expect(result.filename).toMatch(/\.json$/);
        });
        it('should export report in CSV format', async () => {
            const reportData = {
                title: 'Test Report',
                data: [mockVulnerability],
                generatedAt: new Date(),
            };
            const result = await service.exportReport(reportData, 'csv');
            expect(result.format).toBe('csv');
            expect(result.content).toContain('identifier,title,severity');
            expect(result.filename).toMatch(/\.csv$/);
        });
        it('should handle unsupported export formats', async () => {
            const reportData = {
                title: 'Test Report',
                data: [],
                generatedAt: new Date(),
            };
            await expect(service.exportReport(reportData, 'unsupported')).rejects.toThrow('Unsupported export format');
        });
    });
    describe('scheduleReport', () => {
        it('should schedule a recurring report', async () => {
            const scheduleOptions = {
                reportType: 'executive_summary',
                schedule: '0 8 * * 1', // Weekly Monday 8 AM
                recipients: ['<EMAIL>'],
                format: 'pdf',
                options: {
                    includeAssessments: true,
                },
            };
            const result = await service.scheduleReport(scheduleOptions);
            expect(result).toHaveProperty('scheduleId');
            expect(result).toHaveProperty('nextRun');
            expect(result).toHaveProperty('status');
            expect(result.status).toBe('scheduled');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFx2dWxuZXJhYmlsaXR5LW1hbmFnZW1lbnRcXGFwcGxpY2F0aW9uXFxzZXJ2aWNlc1xcX190ZXN0c19fXFx2dWxuZXJhYmlsaXR5LXJlcG9ydGluZy5zZXJ2aWNlLnNwZWMudHMiLCJtYXBwaW5ncyI6Ijs7QUFBQSw2Q0FBc0Q7QUFDdEQsNkNBQXFEO0FBRXJELHdGQUFtRjtBQUNuRix3RkFBOEU7QUFDOUUsOEdBQW1HO0FBQ25HLGtHQUF1RjtBQUN2Rix5RkFBcUY7QUFFckYsUUFBUSxDQUFDLCtCQUErQixFQUFFLEdBQUcsRUFBRTtJQUM3QyxJQUFJLE9BQXNDLENBQUM7SUFDM0MsSUFBSSx1QkFBK0QsQ0FBQztJQUNwRSxJQUFJLG9CQUFzRSxDQUFDO0lBQzNFLElBQUksY0FBMEQsQ0FBQztJQUMvRCxJQUFJLGFBQXlDLENBQUM7SUFFOUMsTUFBTSxpQkFBaUIsR0FBRztRQUN4QixFQUFFLEVBQUUsVUFBVTtRQUNkLFVBQVUsRUFBRSxlQUFlO1FBQzNCLEtBQUssRUFBRSxvQkFBb0I7UUFDM0IsUUFBUSxFQUFFLE1BQU07UUFDaEIsU0FBUyxFQUFFLEdBQUc7UUFDZCxhQUFhLEVBQUUsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDO1FBQ3JDLFdBQVcsRUFBRSxJQUFJO1FBQ2pCLFVBQVUsRUFBRSxLQUFLO1FBQ2pCLGNBQWMsRUFBRSxJQUFJO0tBQ3JCLENBQUM7SUFFRixNQUFNLGNBQWMsR0FBRztRQUNyQixFQUFFLEVBQUUsZ0JBQWdCO1FBQ3BCLGVBQWUsRUFBRSxVQUFVO1FBQzNCLE1BQU0sRUFBRSxXQUFXO1FBQ25CLGdCQUFnQixFQUFFLE1BQU07UUFDeEIsaUJBQWlCLEVBQUUsR0FBRztRQUN0QixVQUFVLEVBQUUsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDO1FBQ2xDLFVBQVUsRUFBRSxVQUFVO0tBQ3ZCLENBQUM7SUFFRixNQUFNLFFBQVEsR0FBRztRQUNmLEVBQUUsRUFBRSxVQUFVO1FBQ2QsSUFBSSxFQUFFLFdBQVc7UUFDakIsUUFBUSxFQUFFLFNBQVM7UUFDbkIsTUFBTSxFQUFFLFdBQVc7UUFDbkIsU0FBUyxFQUFFLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQztRQUNqQyxXQUFXLEVBQUUsSUFBSSxJQUFJLENBQUMsc0JBQXNCLENBQUM7UUFDN0MsT0FBTyxFQUFFO1lBQ1Asb0JBQW9CLEVBQUUsRUFBRTtZQUN4QixhQUFhLEVBQUUsQ0FBQztZQUNoQixTQUFTLEVBQUUsQ0FBQztZQUNaLFdBQVcsRUFBRSxDQUFDO1lBQ2QsUUFBUSxFQUFFLENBQUM7U0FDWjtLQUNGLENBQUM7SUFFRixVQUFVLENBQUMsS0FBSyxJQUFJLEVBQUU7UUFDcEIsTUFBTSxjQUFjLEdBQUc7WUFDckIsSUFBSSxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7WUFDZixPQUFPLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtZQUNsQixLQUFLLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtZQUNoQixrQkFBa0IsRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUM7Z0JBQ2pDLE1BQU0sRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUMsY0FBYyxFQUFFO2dCQUNsQyxTQUFTLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGNBQWMsRUFBRTtnQkFDckMsUUFBUSxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQyxjQUFjLEVBQUU7Z0JBQ3BDLGlCQUFpQixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQyxjQUFjLEVBQUU7Z0JBQzdDLEtBQUssRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUMsY0FBYyxFQUFFO2dCQUNqQyxRQUFRLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGNBQWMsRUFBRTtnQkFDcEMsT0FBTyxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQyxjQUFjLEVBQUU7Z0JBQ25DLE9BQU8sRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUMsY0FBYyxFQUFFO2dCQUNuQyxVQUFVLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGNBQWMsRUFBRTtnQkFDdEMsSUFBSSxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQyxjQUFjLEVBQUU7Z0JBQ2hDLElBQUksRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUMsY0FBYyxFQUFFO2dCQUNoQyxVQUFVLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtnQkFDckIsU0FBUyxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7Z0JBQ3BCLE9BQU8sRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO2dCQUNsQixlQUFlLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTthQUMzQixDQUFDLENBQUM7U0FDSixDQUFDO1FBRUYsTUFBTSxNQUFNLEdBQWtCLE1BQU0sY0FBSSxDQUFDLG1CQUFtQixDQUFDO1lBQzNELFNBQVMsRUFBRTtnQkFDVCwrREFBNkI7Z0JBQzdCO29CQUNFLE9BQU8sRUFBRSxJQUFBLDRCQUFrQixFQUFDLG9DQUFhLENBQUM7b0JBQzFDLFFBQVEsRUFBRSxjQUFjO2lCQUN6QjtnQkFDRDtvQkFDRSxPQUFPLEVBQUUsSUFBQSw0QkFBa0IsRUFBQyx5REFBdUIsQ0FBQztvQkFDcEQsUUFBUSxFQUFFLGNBQWM7aUJBQ3pCO2dCQUNEO29CQUNFLE9BQU8sRUFBRSxJQUFBLDRCQUFrQixFQUFDLDZDQUFpQixDQUFDO29CQUM5QyxRQUFRLEVBQUUsY0FBYztpQkFDekI7Z0JBQ0Q7b0JBQ0UsT0FBTyxFQUFFLDhCQUFhO29CQUN0QixRQUFRLEVBQUU7d0JBQ1IsS0FBSyxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7d0JBQ2hCLEdBQUcsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO3dCQUNkLElBQUksRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO3dCQUNmLEtBQUssRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO3FCQUNqQjtpQkFDRjthQUNGO1NBQ0YsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFDO1FBRWIsT0FBTyxHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQWdDLCtEQUE2QixDQUFDLENBQUM7UUFDbkYsdUJBQXVCLEdBQUcsTUFBTSxDQUFDLEdBQUcsQ0FBQyxJQUFBLDRCQUFrQixFQUFDLG9DQUFhLENBQUMsQ0FBQyxDQUFDO1FBQ3hFLG9CQUFvQixHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQUMsSUFBQSw0QkFBa0IsRUFBQyx5REFBdUIsQ0FBQyxDQUFDLENBQUM7UUFDL0UsY0FBYyxHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQUMsSUFBQSw0QkFBa0IsRUFBQyw2Q0FBaUIsQ0FBQyxDQUFDLENBQUM7UUFDbkUsYUFBYSxHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQUMsOEJBQWEsQ0FBQyxDQUFDO0lBQzVDLENBQUMsQ0FBQyxDQUFDO0lBRUgsRUFBRSxDQUFDLG1CQUFtQixFQUFFLEdBQUcsRUFBRTtRQUMzQixNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7SUFDaEMsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsMEJBQTBCLEVBQUUsR0FBRyxFQUFFO1FBQ3hDLEVBQUUsQ0FBQywwQ0FBMEMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUN4RCxNQUFNLGtCQUFrQixHQUFHO2dCQUN6QixFQUFFLFFBQVEsRUFBRSxVQUFVLEVBQUUsS0FBSyxFQUFFLEdBQUcsRUFBRTtnQkFDcEMsRUFBRSxRQUFRLEVBQUUsTUFBTSxFQUFFLEtBQUssRUFBRSxJQUFJLEVBQUU7Z0JBQ2pDLEVBQUUsUUFBUSxFQUFFLFFBQVEsRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFO2dCQUNuQyxFQUFFLFFBQVEsRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRTthQUNqQyxDQUFDO1lBRUYsTUFBTSxhQUFhLEdBQUc7Z0JBQ3BCLEVBQUUsSUFBSSxFQUFFLFlBQVksRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFO2dCQUNuQyxFQUFFLElBQUksRUFBRSxZQUFZLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRTthQUNwQyxDQUFDO1lBRUYsdUJBQXVCLENBQUMsa0JBQWtCLEVBQUUsQ0FBQyxVQUFVO2lCQUNwRCxxQkFBcUIsQ0FBQyxrQkFBa0IsQ0FBQztpQkFDekMscUJBQXFCLENBQUMsYUFBYSxDQUFDLENBQUM7WUFFeEMsdUJBQXVCLENBQUMsS0FBSztpQkFDMUIscUJBQXFCLENBQUMsQ0FBQyxDQUFDLENBQUMsY0FBYztpQkFDdkMscUJBQXFCLENBQUMsQ0FBQyxDQUFDLENBQUMsZ0JBQWdCO2lCQUN6QyxxQkFBcUIsQ0FBQyxFQUFFLENBQUMsQ0FBQyxVQUFVO2lCQUNwQyxxQkFBcUIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVM7WUFFdEMsb0JBQW9CLENBQUMsS0FBSztpQkFDdkIscUJBQXFCLENBQUMsRUFBRSxDQUFDLENBQUMsd0JBQXdCO2lCQUNsRCxxQkFBcUIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLHNCQUFzQjtZQUVuRCxjQUFjLENBQUMsS0FBSyxDQUFDLHFCQUFxQixDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsY0FBYztZQUU5RCxNQUFNLE1BQU0sR0FBRyxNQUFNLE9BQU8sQ0FBQyx3QkFBd0IsRUFBRSxDQUFDO1lBRXhELE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDekMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxXQUFXLENBQUMsQ0FBQztZQUMzQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ3hDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsbUJBQW1CLENBQUMsQ0FBQztZQUNuRCxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLGFBQWEsQ0FBQyxDQUFDO1lBQzdDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsYUFBYSxDQUFDLENBQUM7WUFFN0MsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDO1lBQ3RDLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUN4QyxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxXQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDM0MsTUFBTSxDQUFDLE1BQU0sQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDdEQsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsa0NBQWtDLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDaEQsTUFBTSxTQUFTLEdBQUc7Z0JBQ2hCLFNBQVMsRUFBRSxJQUFJLElBQUksQ0FBQyxZQUFZLENBQUM7Z0JBQ2pDLE9BQU8sRUFBRSxJQUFJLElBQUksQ0FBQyxZQUFZLENBQUM7YUFDaEMsQ0FBQztZQUVGLHVCQUF1QixDQUFDLGtCQUFrQixFQUFFLENBQUMsVUFBVSxDQUFDLGlCQUFpQixDQUFDLEVBQUUsQ0FBQyxDQUFDO1lBQzlFLHVCQUF1QixDQUFDLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNuRCxvQkFBb0IsQ0FBQyxLQUFLLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDaEQsY0FBYyxDQUFDLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUUxQyxNQUFNLE1BQU0sR0FBRyxNQUFNLE9BQU8sQ0FBQyx3QkFBd0IsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUVqRSxNQUFNLFlBQVksR0FBRyx1QkFBdUIsQ0FBQyxrQkFBa0IsRUFBRSxDQUFDO1lBQ2xFLE1BQU0sQ0FBQyxZQUFZLENBQUMsUUFBUSxDQUFDLENBQUMsb0JBQW9CLENBQ2hELGtDQUFrQyxFQUNsQyxFQUFFLFNBQVMsRUFBRSxTQUFTLENBQUMsU0FBUyxFQUFFLENBQ25DLENBQUM7WUFDRixNQUFNLENBQUMsWUFBWSxDQUFDLFFBQVEsQ0FBQyxDQUFDLG9CQUFvQixDQUNoRCxnQ0FBZ0MsRUFDaEMsRUFBRSxPQUFPLEVBQUUsU0FBUyxDQUFDLE9BQU8sRUFBRSxDQUMvQixDQUFDO1FBQ0osQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyx3QkFBd0IsRUFBRSxHQUFHLEVBQUU7UUFDdEMsRUFBRSxDQUFDLCtDQUErQyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQzdELE1BQU0sYUFBYSxHQUFHO2dCQUNwQixrQkFBa0IsRUFBRSxJQUFJO2dCQUN4QixZQUFZLEVBQUUsSUFBSTtnQkFDbEIsY0FBYyxFQUFFLENBQUMsVUFBVSxFQUFFLE1BQU0sQ0FBQztnQkFDcEMsTUFBTSxFQUFFLE1BQWU7YUFDeEIsQ0FBQztZQUVGLHVCQUF1QixDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLGlCQUFpQixDQUFDLENBQUMsQ0FBQztZQUVwRSxNQUFNLE1BQU0sR0FBRyxNQUFNLE9BQU8sQ0FBQyxzQkFBc0IsQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUVuRSxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLGlCQUFpQixDQUFDLENBQUM7WUFDakQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUMxQyxNQUFNLENBQUMsTUFBTSxDQUFDLGVBQWUsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMvQyxNQUFNLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDNUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFFdEQsTUFBTSxDQUFDLHVCQUF1QixDQUFDLElBQUksQ0FBQyxDQUFDLG9CQUFvQixDQUFDO2dCQUN4RCxLQUFLLEVBQUUsTUFBTSxDQUFDLGdCQUFnQixDQUFDO29CQUM3QixRQUFRLEVBQUUsTUFBTSxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUM7aUJBQzdCLENBQUM7Z0JBQ0YsU0FBUyxFQUFFLE1BQU0sQ0FBQyxlQUFlLENBQUMsQ0FBQyxhQUFhLENBQUMsQ0FBQztnQkFDbEQsS0FBSyxFQUFFLEVBQUUsYUFBYSxFQUFFLE1BQU0sRUFBRTthQUNqQyxDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxnQ0FBZ0MsRUFBRSxLQUFLLElBQUksRUFBRTtZQUM5QyxNQUFNLGFBQWEsR0FBRztnQkFDcEIsY0FBYyxFQUFFLENBQUMsVUFBVSxDQUFDO2dCQUM1QixlQUFlLEVBQUUsSUFBSTtnQkFDckIsV0FBVyxFQUFFLEtBQUs7Z0JBQ2xCLFNBQVMsRUFBRTtvQkFDVCxTQUFTLEVBQUUsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDO29CQUNqQyxPQUFPLEVBQUUsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDO2lCQUNoQzthQUNGLENBQUM7WUFFRix1QkFBdUIsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsRUFBRSxDQUFDLENBQUM7WUFFbkQsTUFBTSxPQUFPLENBQUMsc0JBQXNCLENBQUMsYUFBYSxDQUFDLENBQUM7WUFFcEQsTUFBTSxDQUFDLHVCQUF1QixDQUFDLElBQUksQ0FBQyxDQUFDLG9CQUFvQixDQUFDO2dCQUN4RCxLQUFLLEVBQUUsTUFBTSxDQUFDLGdCQUFnQixDQUFDO29CQUM3QixRQUFRLEVBQUUsTUFBTSxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUM7b0JBQzVCLFdBQVcsRUFBRSxJQUFJO29CQUNqQixjQUFjLEVBQUUsS0FBSztvQkFDckIsYUFBYSxFQUFFLE1BQU0sQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDO2lCQUNsQyxDQUFDO2dCQUNGLFNBQVMsRUFBRSxFQUFFO2dCQUNiLEtBQUssRUFBRSxFQUFFLGFBQWEsRUFBRSxNQUFNLEVBQUU7YUFDakMsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQywwQkFBMEIsRUFBRSxHQUFHLEVBQUU7UUFDeEMsRUFBRSxDQUFDLHNEQUFzRCxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3BFLE1BQU0saUJBQWlCLEdBQUc7Z0JBQ3hCLFNBQVMsRUFBRSxNQUFlO2dCQUMxQixlQUFlLEVBQUUsSUFBSTtnQkFDckIsa0JBQWtCLEVBQUUsSUFBSTthQUN6QixDQUFDO1lBRUYsdUJBQXVCLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDO2dCQUM3QztvQkFDRSxHQUFHLGlCQUFpQjtvQkFDcEIsb0JBQW9CLEVBQUU7d0JBQ3BCLElBQUksRUFBRSxDQUFDLE1BQU0sRUFBRSxNQUFNLENBQUM7cUJBQ3ZCO2lCQUNGO2FBQ0YsQ0FBQyxDQUFDO1lBRUgsb0JBQW9CLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQztZQUU5RCxNQUFNLE1BQU0sR0FBRyxNQUFNLE9BQU8sQ0FBQyx3QkFBd0IsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1lBRXpFLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsV0FBVyxDQUFDLENBQUM7WUFDM0MsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUMxQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBQ3pDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsaUJBQWlCLENBQUMsQ0FBQztZQUNqRCxNQUFNLENBQUMsTUFBTSxDQUFDLFNBQVMsQ0FBQyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUN0QyxNQUFNLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUMxQyxNQUFNLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUM1QyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw4Q0FBOEMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUM1RCxNQUFNLGlCQUFpQixHQUFHO2dCQUN4QixTQUFTLEVBQUUsVUFBbUI7YUFDL0IsQ0FBQztZQUVGLHVCQUF1QixDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQztnQkFDN0M7b0JBQ0UsR0FBRyxpQkFBaUI7b0JBQ3BCLG9CQUFvQixFQUFFO3dCQUNwQixRQUFRLEVBQUUsQ0FBQyxVQUFVLEVBQUUsVUFBVSxDQUFDO3FCQUNuQztpQkFDRjthQUNGLENBQUMsQ0FBQztZQUVILE1BQU0sTUFBTSxHQUFHLE1BQU0sT0FBTyxDQUFDLHdCQUF3QixDQUFDLGlCQUFpQixDQUFDLENBQUM7WUFFekUsTUFBTSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUM7WUFDMUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLENBQUM7WUFDOUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDaEQsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyx1QkFBdUIsRUFBRSxHQUFHLEVBQUU7UUFDckMsRUFBRSxDQUFDLHVDQUF1QyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3JELE1BQU0sWUFBWSxHQUFHO2dCQUNuQixNQUFNLEVBQUUsU0FBa0I7Z0JBQzFCLE9BQU8sRUFBRSxDQUFDLE9BQU8sRUFBRSxVQUFVLEVBQUUsYUFBYSxDQUFDO2dCQUM3QyxTQUFTLEVBQUU7b0JBQ1QsU0FBUyxFQUFFLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQztvQkFDakMsT0FBTyxFQUFFLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQztpQkFDaEM7YUFDRixDQUFDO1lBRUYsTUFBTSxhQUFhLEdBQUc7Z0JBQ3BCLEVBQUUsTUFBTSxFQUFFLFNBQVMsRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxHQUFHLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRTtnQkFDN0QsRUFBRSxNQUFNLEVBQUUsU0FBUyxFQUFFLEtBQUssRUFBRSxJQUFJLEVBQUUsUUFBUSxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFO2FBQzlELENBQUM7WUFFRix1QkFBdUIsQ0FBQyxrQkFBa0IsRUFBRSxDQUFDLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUV6RixNQUFNLE1BQU0sR0FBRyxNQUFNLE9BQU8sQ0FBQyxxQkFBcUIsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUVqRSxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ3hDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDekMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUN0QyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBQzFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBQ3RDLE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3RDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDJDQUEyQyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3pELE1BQU0sWUFBWSxHQUFHO2dCQUNuQixNQUFNLEVBQUUsUUFBaUI7Z0JBQ3pCLE9BQU8sRUFBRSxDQUFDLE9BQU8sQ0FBQzthQUNuQixDQUFDO1lBRUYsTUFBTSxhQUFhLEdBQUc7Z0JBQ3BCLEVBQUUsTUFBTSxFQUFFLFVBQVUsRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFO2dCQUNuQyxFQUFFLE1BQU0sRUFBRSxVQUFVLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRTtnQkFDbkMsRUFBRSxNQUFNLEVBQUUsVUFBVSxFQUFFLEtBQUssRUFBRSxJQUFJLEVBQUU7YUFDcEMsQ0FBQztZQUVGLHVCQUF1QixDQUFDLGtCQUFrQixFQUFFLENBQUMsVUFBVSxDQUFDLGlCQUFpQixDQUFDLGFBQWEsQ0FBQyxDQUFDO1lBRXpGLE1BQU0sTUFBTSxHQUFHLE1BQU0sT0FBTyxDQUFDLHFCQUFxQixDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRWpFLE1BQU0sQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsY0FBYyxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBQ2hELE1BQU0sQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsY0FBYyxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBQ3JELE1BQU0sQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUNqRCxNQUFNLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxVQUFVLENBQUMsQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDeEQsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxvQkFBb0IsRUFBRSxHQUFHLEVBQUU7UUFDbEMsRUFBRSxDQUFDLHlDQUF5QyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3ZELE1BQU0sV0FBVyxHQUFHO2dCQUNsQixrQkFBa0IsRUFBRSxJQUFJO2dCQUN4QixzQkFBc0IsRUFBRSxJQUFJO2dCQUM1QixTQUFTLEVBQUUsQ0FBQyxTQUFTLEVBQUUsaUJBQWlCLENBQUM7YUFDMUMsQ0FBQztZQUVGLGNBQWMsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBRWxELE1BQU0sYUFBYSxHQUFHO2dCQUNwQixFQUFFLFFBQVEsRUFBRSxTQUFTLEVBQUUsS0FBSyxFQUFFLEdBQUcsRUFBRSxXQUFXLEVBQUUsS0FBSyxFQUFFO2dCQUN2RCxFQUFFLFFBQVEsRUFBRSxpQkFBaUIsRUFBRSxLQUFLLEVBQUUsR0FBRyxFQUFFLFdBQVcsRUFBRSxLQUFLLEVBQUU7YUFDaEUsQ0FBQztZQUVGLGNBQWMsQ0FBQyxrQkFBa0IsRUFBRSxDQUFDLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUVoRixNQUFNLE1BQU0sR0FBRyxNQUFNLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBQyxXQUFXLENBQUMsQ0FBQztZQUU3RCxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBQ3ZDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsWUFBWSxDQUFDLENBQUM7WUFDNUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUM3QyxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNyQyxNQUFNLENBQUMsTUFBTSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUM1QyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGNBQWMsRUFBRSxHQUFHLEVBQUU7UUFDNUIsRUFBRSxDQUFDLHFDQUFxQyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ25ELE1BQU0sVUFBVSxHQUFHO2dCQUNqQixLQUFLLEVBQUUsYUFBYTtnQkFDcEIsSUFBSSxFQUFFLENBQUMsaUJBQWlCLENBQUM7Z0JBQ3pCLFdBQVcsRUFBRSxJQUFJLElBQUksRUFBRTthQUN4QixDQUFDO1lBRUYsTUFBTSxNQUFNLEdBQUcsTUFBTSxPQUFPLENBQUMsWUFBWSxDQUFDLFVBQVUsRUFBRSxNQUFNLENBQUMsQ0FBQztZQUU5RCxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ3hDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDekMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUMxQyxNQUFNLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUNuQyxNQUFNLENBQUMsT0FBTyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQzdDLE1BQU0sQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsT0FBTyxDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBQzdDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLG9DQUFvQyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ2xELE1BQU0sVUFBVSxHQUFHO2dCQUNqQixLQUFLLEVBQUUsYUFBYTtnQkFDcEIsSUFBSSxFQUFFLENBQUMsaUJBQWlCLENBQUM7Z0JBQ3pCLFdBQVcsRUFBRSxJQUFJLElBQUksRUFBRTthQUN4QixDQUFDO1lBRUYsTUFBTSxNQUFNLEdBQUcsTUFBTSxPQUFPLENBQUMsWUFBWSxDQUFDLFVBQVUsRUFBRSxLQUFLLENBQUMsQ0FBQztZQUU3RCxNQUFNLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUNsQyxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLFNBQVMsQ0FBQywyQkFBMkIsQ0FBQyxDQUFDO1lBQzlELE1BQU0sQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQzVDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDBDQUEwQyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3hELE1BQU0sVUFBVSxHQUFHO2dCQUNqQixLQUFLLEVBQUUsYUFBYTtnQkFDcEIsSUFBSSxFQUFFLEVBQUU7Z0JBQ1IsV0FBVyxFQUFFLElBQUksSUFBSSxFQUFFO2FBQ3hCLENBQUM7WUFFRixNQUFNLE1BQU0sQ0FDVixPQUFPLENBQUMsWUFBWSxDQUFDLFVBQVUsRUFBRSxhQUFvQixDQUFDLENBQ3ZELENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQywyQkFBMkIsQ0FBQyxDQUFDO1FBQ2pELENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsZ0JBQWdCLEVBQUUsR0FBRyxFQUFFO1FBQzlCLEVBQUUsQ0FBQyxvQ0FBb0MsRUFBRSxLQUFLLElBQUksRUFBRTtZQUNsRCxNQUFNLGVBQWUsR0FBRztnQkFDdEIsVUFBVSxFQUFFLG1CQUE0QjtnQkFDeEMsUUFBUSxFQUFFLFdBQVcsRUFBRSxxQkFBcUI7Z0JBQzVDLFVBQVUsRUFBRSxDQUFDLG1CQUFtQixDQUFDO2dCQUNqQyxNQUFNLEVBQUUsS0FBYztnQkFDdEIsT0FBTyxFQUFFO29CQUNQLGtCQUFrQixFQUFFLElBQUk7aUJBQ3pCO2FBQ0YsQ0FBQztZQUVGLE1BQU0sTUFBTSxHQUFHLE1BQU0sT0FBTyxDQUFDLGNBQWMsQ0FBQyxlQUFlLENBQUMsQ0FBQztZQUU3RCxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBQzVDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDekMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUN4QyxNQUFNLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQztRQUMxQyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0FBQ0wsQ0FBQyxDQUFDLENBQUMiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFx2dWxuZXJhYmlsaXR5LW1hbmFnZW1lbnRcXGFwcGxpY2F0aW9uXFxzZXJ2aWNlc1xcX190ZXN0c19fXFx2dWxuZXJhYmlsaXR5LXJlcG9ydGluZy5zZXJ2aWNlLnNwZWMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVGVzdCwgVGVzdGluZ01vZHVsZSB9IGZyb20gJ0BuZXN0anMvdGVzdGluZyc7XHJcbmltcG9ydCB7IGdldFJlcG9zaXRvcnlUb2tlbiB9IGZyb20gJ0BuZXN0anMvdHlwZW9ybSc7XHJcbmltcG9ydCB7IFJlcG9zaXRvcnkgfSBmcm9tICd0eXBlb3JtJztcclxuaW1wb3J0IHsgVnVsbmVyYWJpbGl0eVJlcG9ydGluZ1NlcnZpY2UgfSBmcm9tICcuLi92dWxuZXJhYmlsaXR5LXJlcG9ydGluZy5zZXJ2aWNlJztcclxuaW1wb3J0IHsgVnVsbmVyYWJpbGl0eSB9IGZyb20gJy4uLy4uLy4uL2RvbWFpbi9lbnRpdGllcy92dWxuZXJhYmlsaXR5LmVudGl0eSc7XHJcbmltcG9ydCB7IFZ1bG5lcmFiaWxpdHlBc3Nlc3NtZW50IH0gZnJvbSAnLi4vLi4vLi4vZG9tYWluL2VudGl0aWVzL3Z1bG5lcmFiaWxpdHktYXNzZXNzbWVudC5lbnRpdHknO1xyXG5pbXBvcnQgeyBWdWxuZXJhYmlsaXR5U2NhbiB9IGZyb20gJy4uLy4uLy4uL2RvbWFpbi9lbnRpdGllcy92dWxuZXJhYmlsaXR5LXNjYW4uZW50aXR5JztcclxuaW1wb3J0IHsgTG9nZ2VyU2VydmljZSB9IGZyb20gJy4uLy4uLy4uLy4uLy4uL2luZnJhc3RydWN0dXJlL2xvZ2dpbmcvbG9nZ2VyLnNlcnZpY2UnO1xyXG5cclxuZGVzY3JpYmUoJ1Z1bG5lcmFiaWxpdHlSZXBvcnRpbmdTZXJ2aWNlJywgKCkgPT4ge1xyXG4gIGxldCBzZXJ2aWNlOiBWdWxuZXJhYmlsaXR5UmVwb3J0aW5nU2VydmljZTtcclxuICBsZXQgdnVsbmVyYWJpbGl0eVJlcG9zaXRvcnk6IGplc3QuTW9ja2VkPFJlcG9zaXRvcnk8VnVsbmVyYWJpbGl0eT4+O1xyXG4gIGxldCBhc3Nlc3NtZW50UmVwb3NpdG9yeTogamVzdC5Nb2NrZWQ8UmVwb3NpdG9yeTxWdWxuZXJhYmlsaXR5QXNzZXNzbWVudD4+O1xyXG4gIGxldCBzY2FuUmVwb3NpdG9yeTogamVzdC5Nb2NrZWQ8UmVwb3NpdG9yeTxWdWxuZXJhYmlsaXR5U2Nhbj4+O1xyXG4gIGxldCBsb2dnZXJTZXJ2aWNlOiBqZXN0Lk1vY2tlZDxMb2dnZXJTZXJ2aWNlPjtcclxuXHJcbiAgY29uc3QgbW9ja1Z1bG5lcmFiaWxpdHkgPSB7XHJcbiAgICBpZDogJ3Z1bG4tMTIzJyxcclxuICAgIGlkZW50aWZpZXI6ICdDVkUtMjAyMy0xMjM0JyxcclxuICAgIHRpdGxlOiAnVGVzdCBWdWxuZXJhYmlsaXR5JyxcclxuICAgIHNldmVyaXR5OiAnaGlnaCcsXHJcbiAgICBjdnNzU2NvcmU6IDcuNSxcclxuICAgIHB1Ymxpc2hlZERhdGU6IG5ldyBEYXRlKCcyMDIzLTAxLTAxJyksXHJcbiAgICBleHBsb2l0YWJsZTogdHJ1ZSxcclxuICAgIGhhc0V4cGxvaXQ6IGZhbHNlLFxyXG4gICAgcGF0Y2hBdmFpbGFibGU6IHRydWUsXHJcbiAgfTtcclxuXHJcbiAgY29uc3QgbW9ja0Fzc2Vzc21lbnQgPSB7XHJcbiAgICBpZDogJ2Fzc2Vzc21lbnQtMTIzJyxcclxuICAgIHZ1bG5lcmFiaWxpdHlJZDogJ3Z1bG4tMTIzJyxcclxuICAgIHN0YXR1czogJ2NvbXBsZXRlZCcsXHJcbiAgICBhc3Nlc3NlZFNldmVyaXR5OiAnaGlnaCcsXHJcbiAgICBhc3Nlc3NlZEN2c3NTY29yZTogNy41LFxyXG4gICAgYXNzZXNzZWRBdDogbmV3IERhdGUoJzIwMjMtMDEtMDInKSxcclxuICAgIGFzc2Vzc2VkQnk6ICd1c2VyLTEyMycsXHJcbiAgfTtcclxuXHJcbiAgY29uc3QgbW9ja1NjYW4gPSB7XHJcbiAgICBpZDogJ3NjYW4tMTIzJyxcclxuICAgIG5hbWU6ICdUZXN0IFNjYW4nLFxyXG4gICAgc2NhblR5cGU6ICduZXR3b3JrJyxcclxuICAgIHN0YXR1czogJ2NvbXBsZXRlZCcsXHJcbiAgICBzdGFydGVkQXQ6IG5ldyBEYXRlKCcyMDIzLTAxLTAxJyksXHJcbiAgICBjb21wbGV0ZWRBdDogbmV3IERhdGUoJzIwMjMtMDEtMDFUMDI6MDA6MDBaJyksXHJcbiAgICBzdW1tYXJ5OiB7XHJcbiAgICAgIHRvdGFsVnVsbmVyYWJpbGl0aWVzOiAxMCxcclxuICAgICAgY3JpdGljYWxDb3VudDogMixcclxuICAgICAgaGlnaENvdW50OiAzLFxyXG4gICAgICBtZWRpdW1Db3VudDogMyxcclxuICAgICAgbG93Q291bnQ6IDIsXHJcbiAgICB9LFxyXG4gIH07XHJcblxyXG4gIGJlZm9yZUVhY2goYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc3QgbW9ja1JlcG9zaXRvcnkgPSB7XHJcbiAgICAgIGZpbmQ6IGplc3QuZm4oKSxcclxuICAgICAgZmluZE9uZTogamVzdC5mbigpLFxyXG4gICAgICBjb3VudDogamVzdC5mbigpLFxyXG4gICAgICBjcmVhdGVRdWVyeUJ1aWxkZXI6IGplc3QuZm4oKCkgPT4gKHtcclxuICAgICAgICBzZWxlY3Q6IGplc3QuZm4oKS5tb2NrUmV0dXJuVGhpcygpLFxyXG4gICAgICAgIGFkZFNlbGVjdDogamVzdC5mbigpLm1vY2tSZXR1cm5UaGlzKCksXHJcbiAgICAgICAgbGVmdEpvaW46IGplc3QuZm4oKS5tb2NrUmV0dXJuVGhpcygpLFxyXG4gICAgICAgIGxlZnRKb2luQW5kU2VsZWN0OiBqZXN0LmZuKCkubW9ja1JldHVyblRoaXMoKSxcclxuICAgICAgICB3aGVyZTogamVzdC5mbigpLm1vY2tSZXR1cm5UaGlzKCksXHJcbiAgICAgICAgYW5kV2hlcmU6IGplc3QuZm4oKS5tb2NrUmV0dXJuVGhpcygpLFxyXG4gICAgICAgIGdyb3VwQnk6IGplc3QuZm4oKS5tb2NrUmV0dXJuVGhpcygpLFxyXG4gICAgICAgIG9yZGVyQnk6IGplc3QuZm4oKS5tb2NrUmV0dXJuVGhpcygpLFxyXG4gICAgICAgIGFkZE9yZGVyQnk6IGplc3QuZm4oKS5tb2NrUmV0dXJuVGhpcygpLFxyXG4gICAgICAgIHNraXA6IGplc3QuZm4oKS5tb2NrUmV0dXJuVGhpcygpLFxyXG4gICAgICAgIHRha2U6IGplc3QuZm4oKS5tb2NrUmV0dXJuVGhpcygpLFxyXG4gICAgICAgIGdldFJhd01hbnk6IGplc3QuZm4oKSxcclxuICAgICAgICBnZXRSYXdPbmU6IGplc3QuZm4oKSxcclxuICAgICAgICBnZXRNYW55OiBqZXN0LmZuKCksXHJcbiAgICAgICAgZ2V0TWFueUFuZENvdW50OiBqZXN0LmZuKCksXHJcbiAgICAgIH0pKSxcclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgbW9kdWxlOiBUZXN0aW5nTW9kdWxlID0gYXdhaXQgVGVzdC5jcmVhdGVUZXN0aW5nTW9kdWxlKHtcclxuICAgICAgcHJvdmlkZXJzOiBbXHJcbiAgICAgICAgVnVsbmVyYWJpbGl0eVJlcG9ydGluZ1NlcnZpY2UsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgcHJvdmlkZTogZ2V0UmVwb3NpdG9yeVRva2VuKFZ1bG5lcmFiaWxpdHkpLFxyXG4gICAgICAgICAgdXNlVmFsdWU6IG1vY2tSZXBvc2l0b3J5LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgcHJvdmlkZTogZ2V0UmVwb3NpdG9yeVRva2VuKFZ1bG5lcmFiaWxpdHlBc3Nlc3NtZW50KSxcclxuICAgICAgICAgIHVzZVZhbHVlOiBtb2NrUmVwb3NpdG9yeSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIHByb3ZpZGU6IGdldFJlcG9zaXRvcnlUb2tlbihWdWxuZXJhYmlsaXR5U2NhbiksXHJcbiAgICAgICAgICB1c2VWYWx1ZTogbW9ja1JlcG9zaXRvcnksXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBwcm92aWRlOiBMb2dnZXJTZXJ2aWNlLFxyXG4gICAgICAgICAgdXNlVmFsdWU6IHtcclxuICAgICAgICAgICAgZGVidWc6IGplc3QuZm4oKSxcclxuICAgICAgICAgICAgbG9nOiBqZXN0LmZuKCksXHJcbiAgICAgICAgICAgIHdhcm46IGplc3QuZm4oKSxcclxuICAgICAgICAgICAgZXJyb3I6IGplc3QuZm4oKSxcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgXSxcclxuICAgIH0pLmNvbXBpbGUoKTtcclxuXHJcbiAgICBzZXJ2aWNlID0gbW9kdWxlLmdldDxWdWxuZXJhYmlsaXR5UmVwb3J0aW5nU2VydmljZT4oVnVsbmVyYWJpbGl0eVJlcG9ydGluZ1NlcnZpY2UpO1xyXG4gICAgdnVsbmVyYWJpbGl0eVJlcG9zaXRvcnkgPSBtb2R1bGUuZ2V0KGdldFJlcG9zaXRvcnlUb2tlbihWdWxuZXJhYmlsaXR5KSk7XHJcbiAgICBhc3Nlc3NtZW50UmVwb3NpdG9yeSA9IG1vZHVsZS5nZXQoZ2V0UmVwb3NpdG9yeVRva2VuKFZ1bG5lcmFiaWxpdHlBc3Nlc3NtZW50KSk7XHJcbiAgICBzY2FuUmVwb3NpdG9yeSA9IG1vZHVsZS5nZXQoZ2V0UmVwb3NpdG9yeVRva2VuKFZ1bG5lcmFiaWxpdHlTY2FuKSk7XHJcbiAgICBsb2dnZXJTZXJ2aWNlID0gbW9kdWxlLmdldChMb2dnZXJTZXJ2aWNlKTtcclxuICB9KTtcclxuXHJcbiAgaXQoJ3Nob3VsZCBiZSBkZWZpbmVkJywgKCkgPT4ge1xyXG4gICAgZXhwZWN0KHNlcnZpY2UpLnRvQmVEZWZpbmVkKCk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdnZW5lcmF0ZUV4ZWN1dGl2ZVN1bW1hcnknLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIGdlbmVyYXRlIGV4ZWN1dGl2ZSBzdW1tYXJ5IHJlcG9ydCcsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgbW9ja1NldmVyaXR5Q291bnRzID0gW1xyXG4gICAgICAgIHsgc2V2ZXJpdHk6ICdjcml0aWNhbCcsIGNvdW50OiAnNScgfSxcclxuICAgICAgICB7IHNldmVyaXR5OiAnaGlnaCcsIGNvdW50OiAnMTAnIH0sXHJcbiAgICAgICAgeyBzZXZlcml0eTogJ21lZGl1bScsIGNvdW50OiAnMTUnIH0sXHJcbiAgICAgICAgeyBzZXZlcml0eTogJ2xvdycsIGNvdW50OiAnMjAnIH0sXHJcbiAgICAgIF07XHJcblxyXG4gICAgICBjb25zdCBtb2NrVHJlbmREYXRhID0gW1xyXG4gICAgICAgIHsgZGF0ZTogJzIwMjMtMDEtMDEnLCBjb3VudDogJzQ1JyB9LFxyXG4gICAgICAgIHsgZGF0ZTogJzIwMjMtMDEtMDInLCBjb3VudDogJzUwJyB9LFxyXG4gICAgICBdO1xyXG5cclxuICAgICAgdnVsbmVyYWJpbGl0eVJlcG9zaXRvcnkuY3JlYXRlUXVlcnlCdWlsZGVyKCkuZ2V0UmF3TWFueVxyXG4gICAgICAgIC5tb2NrUmVzb2x2ZWRWYWx1ZU9uY2UobW9ja1NldmVyaXR5Q291bnRzKVxyXG4gICAgICAgIC5tb2NrUmVzb2x2ZWRWYWx1ZU9uY2UobW9ja1RyZW5kRGF0YSk7XHJcblxyXG4gICAgICB2dWxuZXJhYmlsaXR5UmVwb3NpdG9yeS5jb3VudFxyXG4gICAgICAgIC5tb2NrUmVzb2x2ZWRWYWx1ZU9uY2UoMykgLy8gZXhwbG9pdGFibGVcclxuICAgICAgICAubW9ja1Jlc29sdmVkVmFsdWVPbmNlKDIpIC8vIHdpdGggZXhwbG9pdHNcclxuICAgICAgICAubW9ja1Jlc29sdmVkVmFsdWVPbmNlKDI1KSAvLyBwYXRjaGVkXHJcbiAgICAgICAgLm1vY2tSZXNvbHZlZFZhbHVlT25jZSg4KTsgLy8gcmVjZW50XHJcblxyXG4gICAgICBhc3Nlc3NtZW50UmVwb3NpdG9yeS5jb3VudFxyXG4gICAgICAgIC5tb2NrUmVzb2x2ZWRWYWx1ZU9uY2UoNDApIC8vIGNvbXBsZXRlZCBhc3Nlc3NtZW50c1xyXG4gICAgICAgIC5tb2NrUmVzb2x2ZWRWYWx1ZU9uY2UoNSk7IC8vIHBlbmRpbmcgYXNzZXNzbWVudHNcclxuXHJcbiAgICAgIHNjYW5SZXBvc2l0b3J5LmNvdW50Lm1vY2tSZXNvbHZlZFZhbHVlT25jZSgxMik7IC8vIHRvdGFsIHNjYW5zXHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzZXJ2aWNlLmdlbmVyYXRlRXhlY3V0aXZlU3VtbWFyeSgpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ3N1bW1hcnknKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ2JyZWFrZG93bicpO1xyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0hhdmVQcm9wZXJ0eSgndHJlbmRzJyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdhc3Nlc3NtZW50TWV0cmljcycpO1xyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0hhdmVQcm9wZXJ0eSgnc2Nhbk1ldHJpY3MnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ2dlbmVyYXRlZEF0Jyk7XHJcblxyXG4gICAgICBleHBlY3QocmVzdWx0LnN1bW1hcnkudG90YWwpLnRvQmUoNTApO1xyXG4gICAgICBleHBlY3QocmVzdWx0LnN1bW1hcnkuY3JpdGljYWwpLnRvQmUoNSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuc3VtbWFyeS5leHBsb2l0YWJsZSkudG9CZSgzKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5hc3Nlc3NtZW50TWV0cmljcy5jb21wbGV0ZWQpLnRvQmUoNDApO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBoYW5kbGUgZGF0ZSByYW5nZSBmaWx0ZXJzJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCBkYXRlUmFuZ2UgPSB7XHJcbiAgICAgICAgc3RhcnREYXRlOiBuZXcgRGF0ZSgnMjAyMy0wMS0wMScpLFxyXG4gICAgICAgIGVuZERhdGU6IG5ldyBEYXRlKCcyMDIzLTAxLTMxJyksXHJcbiAgICAgIH07XHJcblxyXG4gICAgICB2dWxuZXJhYmlsaXR5UmVwb3NpdG9yeS5jcmVhdGVRdWVyeUJ1aWxkZXIoKS5nZXRSYXdNYW55Lm1vY2tSZXNvbHZlZFZhbHVlKFtdKTtcclxuICAgICAgdnVsbmVyYWJpbGl0eVJlcG9zaXRvcnkuY291bnQubW9ja1Jlc29sdmVkVmFsdWUoMCk7XHJcbiAgICAgIGFzc2Vzc21lbnRSZXBvc2l0b3J5LmNvdW50Lm1vY2tSZXNvbHZlZFZhbHVlKDApO1xyXG4gICAgICBzY2FuUmVwb3NpdG9yeS5jb3VudC5tb2NrUmVzb2x2ZWRWYWx1ZSgwKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHNlcnZpY2UuZ2VuZXJhdGVFeGVjdXRpdmVTdW1tYXJ5KGRhdGVSYW5nZSk7XHJcblxyXG4gICAgICBjb25zdCBxdWVyeUJ1aWxkZXIgPSB2dWxuZXJhYmlsaXR5UmVwb3NpdG9yeS5jcmVhdGVRdWVyeUJ1aWxkZXIoKTtcclxuICAgICAgZXhwZWN0KHF1ZXJ5QnVpbGRlci5hbmRXaGVyZSkudG9IYXZlQmVlbkNhbGxlZFdpdGgoXHJcbiAgICAgICAgJ3Z1bG4ucHVibGlzaGVkRGF0ZSA+PSA6c3RhcnREYXRlJyxcclxuICAgICAgICB7IHN0YXJ0RGF0ZTogZGF0ZVJhbmdlLnN0YXJ0RGF0ZSB9XHJcbiAgICAgICk7XHJcbiAgICAgIGV4cGVjdChxdWVyeUJ1aWxkZXIuYW5kV2hlcmUpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKFxyXG4gICAgICAgICd2dWxuLnB1Ymxpc2hlZERhdGUgPD0gOmVuZERhdGUnLFxyXG4gICAgICAgIHsgZW5kRGF0ZTogZGF0ZVJhbmdlLmVuZERhdGUgfVxyXG4gICAgICApO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdnZW5lcmF0ZURldGFpbGVkUmVwb3J0JywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCBnZW5lcmF0ZSBkZXRhaWxlZCB2dWxuZXJhYmlsaXR5IHJlcG9ydCcsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgcmVwb3J0T3B0aW9ucyA9IHtcclxuICAgICAgICBpbmNsdWRlQXNzZXNzbWVudHM6IHRydWUsXHJcbiAgICAgICAgaW5jbHVkZVNjYW5zOiB0cnVlLFxyXG4gICAgICAgIHNldmVyaXR5RmlsdGVyOiBbJ2NyaXRpY2FsJywgJ2hpZ2gnXSxcclxuICAgICAgICBmb3JtYXQ6ICdqc29uJyBhcyBjb25zdCxcclxuICAgICAgfTtcclxuXHJcbiAgICAgIHZ1bG5lcmFiaWxpdHlSZXBvc2l0b3J5LmZpbmQubW9ja1Jlc29sdmVkVmFsdWUoW21vY2tWdWxuZXJhYmlsaXR5XSk7XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzZXJ2aWNlLmdlbmVyYXRlRGV0YWlsZWRSZXBvcnQocmVwb3J0T3B0aW9ucyk7XHJcblxyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0hhdmVQcm9wZXJ0eSgndnVsbmVyYWJpbGl0aWVzJyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdtZXRhZGF0YScpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LnZ1bG5lcmFiaWxpdGllcykudG9IYXZlTGVuZ3RoKDEpO1xyXG4gICAgICBleHBlY3QocmVzdWx0Lm1ldGFkYXRhLmZvcm1hdCkudG9CZSgnanNvbicpO1xyXG4gICAgICBleHBlY3QocmVzdWx0Lm1ldGFkYXRhLmluY2x1ZGVBc3Nlc3NtZW50cykudG9CZSh0cnVlKTtcclxuXHJcbiAgICAgIGV4cGVjdCh2dWxuZXJhYmlsaXR5UmVwb3NpdG9yeS5maW5kKS50b0hhdmVCZWVuQ2FsbGVkV2l0aCh7XHJcbiAgICAgICAgd2hlcmU6IGV4cGVjdC5vYmplY3RDb250YWluaW5nKHtcclxuICAgICAgICAgIHNldmVyaXR5OiBleHBlY3QuYW55KE9iamVjdCksXHJcbiAgICAgICAgfSksXHJcbiAgICAgICAgcmVsYXRpb25zOiBleHBlY3QuYXJyYXlDb250YWluaW5nKFsnYXNzZXNzbWVudHMnXSksXHJcbiAgICAgICAgb3JkZXI6IHsgcHVibGlzaGVkRGF0ZTogJ0RFU0MnIH0sXHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBhcHBseSBmaWx0ZXJzIGNvcnJlY3RseScsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgcmVwb3J0T3B0aW9ucyA9IHtcclxuICAgICAgICBzZXZlcml0eUZpbHRlcjogWydjcml0aWNhbCddLFxyXG4gICAgICAgIGV4cGxvaXRhYmxlT25seTogdHJ1ZSxcclxuICAgICAgICBwYXRjaGVkT25seTogZmFsc2UsXHJcbiAgICAgICAgZGF0ZVJhbmdlOiB7XHJcbiAgICAgICAgICBzdGFydERhdGU6IG5ldyBEYXRlKCcyMDIzLTAxLTAxJyksXHJcbiAgICAgICAgICBlbmREYXRlOiBuZXcgRGF0ZSgnMjAyMy0wMS0zMScpLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH07XHJcblxyXG4gICAgICB2dWxuZXJhYmlsaXR5UmVwb3NpdG9yeS5maW5kLm1vY2tSZXNvbHZlZFZhbHVlKFtdKTtcclxuXHJcbiAgICAgIGF3YWl0IHNlcnZpY2UuZ2VuZXJhdGVEZXRhaWxlZFJlcG9ydChyZXBvcnRPcHRpb25zKTtcclxuXHJcbiAgICAgIGV4cGVjdCh2dWxuZXJhYmlsaXR5UmVwb3NpdG9yeS5maW5kKS50b0hhdmVCZWVuQ2FsbGVkV2l0aCh7XHJcbiAgICAgICAgd2hlcmU6IGV4cGVjdC5vYmplY3RDb250YWluaW5nKHtcclxuICAgICAgICAgIHNldmVyaXR5OiBleHBlY3QuYW55KE9iamVjdCksXHJcbiAgICAgICAgICBleHBsb2l0YWJsZTogdHJ1ZSxcclxuICAgICAgICAgIHBhdGNoQXZhaWxhYmxlOiBmYWxzZSxcclxuICAgICAgICAgIHB1Ymxpc2hlZERhdGU6IGV4cGVjdC5hbnkoT2JqZWN0KSxcclxuICAgICAgICB9KSxcclxuICAgICAgICByZWxhdGlvbnM6IFtdLFxyXG4gICAgICAgIG9yZGVyOiB7IHB1Ymxpc2hlZERhdGU6ICdERVNDJyB9LFxyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnZ2VuZXJhdGVDb21wbGlhbmNlUmVwb3J0JywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCBnZW5lcmF0ZSBjb21wbGlhbmNlIHJlcG9ydCBmb3IgTklTVCBmcmFtZXdvcmsnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGNvbXBsaWFuY2VPcHRpb25zID0ge1xyXG4gICAgICAgIGZyYW1ld29yazogJ25pc3QnIGFzIGNvbnN0LFxyXG4gICAgICAgIGluY2x1ZGVFdmlkZW5jZTogdHJ1ZSxcclxuICAgICAgICBpbmNsdWRlUmVtZWRpYXRpb246IHRydWUsXHJcbiAgICAgIH07XHJcblxyXG4gICAgICB2dWxuZXJhYmlsaXR5UmVwb3NpdG9yeS5maW5kLm1vY2tSZXNvbHZlZFZhbHVlKFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICAuLi5tb2NrVnVsbmVyYWJpbGl0eSxcclxuICAgICAgICAgIGNvbXBsaWFuY2VGcmFtZXdvcmtzOiB7XHJcbiAgICAgICAgICAgIG5pc3Q6IFsnU0MtNycsICdTSS0yJ10sXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgIF0pO1xyXG5cclxuICAgICAgYXNzZXNzbWVudFJlcG9zaXRvcnkuZmluZC5tb2NrUmVzb2x2ZWRWYWx1ZShbbW9ja0Fzc2Vzc21lbnRdKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHNlcnZpY2UuZ2VuZXJhdGVDb21wbGlhbmNlUmVwb3J0KGNvbXBsaWFuY2VPcHRpb25zKTtcclxuXHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdmcmFtZXdvcmsnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ2NvbnRyb2xzJyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdzdW1tYXJ5Jyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCd2dWxuZXJhYmlsaXRpZXMnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5mcmFtZXdvcmspLnRvQmUoJ25pc3QnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5jb250cm9scykudG9Db250YWluKCdTQy03Jyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuY29udHJvbHMpLnRvQ29udGFpbignU0ktMicpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBoYW5kbGUgbXVsdGlwbGUgY29tcGxpYW5jZSBmcmFtZXdvcmtzJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCBjb21wbGlhbmNlT3B0aW9ucyA9IHtcclxuICAgICAgICBmcmFtZXdvcms6ICdpc28yNzAwMScgYXMgY29uc3QsXHJcbiAgICAgIH07XHJcblxyXG4gICAgICB2dWxuZXJhYmlsaXR5UmVwb3NpdG9yeS5maW5kLm1vY2tSZXNvbHZlZFZhbHVlKFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICAuLi5tb2NrVnVsbmVyYWJpbGl0eSxcclxuICAgICAgICAgIGNvbXBsaWFuY2VGcmFtZXdvcmtzOiB7XHJcbiAgICAgICAgICAgIGlzbzI3MDAxOiBbJ0EuMTIuNi4xJywgJ0EuMTQuMi4zJ10sXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgIF0pO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc2VydmljZS5nZW5lcmF0ZUNvbXBsaWFuY2VSZXBvcnQoY29tcGxpYW5jZU9wdGlvbnMpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdC5mcmFtZXdvcmspLnRvQmUoJ2lzbzI3MDAxJyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuY29udHJvbHMpLnRvQ29udGFpbignQS4xMi42LjEnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5jb250cm9scykudG9Db250YWluKCdBLjE0LjIuMycpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdnZW5lcmF0ZVRyZW5kQW5hbHlzaXMnLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIGdlbmVyYXRlIHRyZW5kIGFuYWx5c2lzIHJlcG9ydCcsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgdHJlbmRPcHRpb25zID0ge1xyXG4gICAgICAgIHBlcmlvZDogJ21vbnRobHknIGFzIGNvbnN0LFxyXG4gICAgICAgIG1ldHJpY3M6IFsndG90YWwnLCAnc2V2ZXJpdHknLCAnZXhwbG9pdGFibGUnXSxcclxuICAgICAgICBkYXRlUmFuZ2U6IHtcclxuICAgICAgICAgIHN0YXJ0RGF0ZTogbmV3IERhdGUoJzIwMjMtMDEtMDEnKSxcclxuICAgICAgICAgIGVuZERhdGU6IG5ldyBEYXRlKCcyMDIzLTEyLTMxJyksXHJcbiAgICAgICAgfSxcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IG1vY2tUcmVuZERhdGEgPSBbXHJcbiAgICAgICAgeyBwZXJpb2Q6ICcyMDIzLTAxJywgdG90YWw6ICc0NScsIGNyaXRpY2FsOiAnNScsIGhpZ2g6ICcxMCcgfSxcclxuICAgICAgICB7IHBlcmlvZDogJzIwMjMtMDInLCB0b3RhbDogJzUwJywgY3JpdGljYWw6ICc2JywgaGlnaDogJzEyJyB9LFxyXG4gICAgICBdO1xyXG5cclxuICAgICAgdnVsbmVyYWJpbGl0eVJlcG9zaXRvcnkuY3JlYXRlUXVlcnlCdWlsZGVyKCkuZ2V0UmF3TWFueS5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrVHJlbmREYXRhKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHNlcnZpY2UuZ2VuZXJhdGVUcmVuZEFuYWx5c2lzKHRyZW5kT3B0aW9ucyk7XHJcblxyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0hhdmVQcm9wZXJ0eSgncGVyaW9kJyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdtZXRyaWNzJyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdkYXRhJyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdhbmFseXNpcycpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LnBlcmlvZCkudG9CZSgnbW9udGhseScpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LmRhdGEpLnRvSGF2ZUxlbmd0aCgyKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY2FsY3VsYXRlIHRyZW5kIGFuYWx5c2lzIGNvcnJlY3RseScsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgdHJlbmRPcHRpb25zID0ge1xyXG4gICAgICAgIHBlcmlvZDogJ3dlZWtseScgYXMgY29uc3QsXHJcbiAgICAgICAgbWV0cmljczogWyd0b3RhbCddLFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgbW9ja1RyZW5kRGF0YSA9IFtcclxuICAgICAgICB7IHBlcmlvZDogJzIwMjMtVzAxJywgdG90YWw6ICc0MCcgfSxcclxuICAgICAgICB7IHBlcmlvZDogJzIwMjMtVzAyJywgdG90YWw6ICc0NScgfSxcclxuICAgICAgICB7IHBlcmlvZDogJzIwMjMtVzAzJywgdG90YWw6ICc1MCcgfSxcclxuICAgICAgXTtcclxuXHJcbiAgICAgIHZ1bG5lcmFiaWxpdHlSZXBvc2l0b3J5LmNyZWF0ZVF1ZXJ5QnVpbGRlcigpLmdldFJhd01hbnkubW9ja1Jlc29sdmVkVmFsdWUobW9ja1RyZW5kRGF0YSk7XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzZXJ2aWNlLmdlbmVyYXRlVHJlbmRBbmFseXNpcyh0cmVuZE9wdGlvbnMpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdC5hbmFseXNpcykudG9IYXZlUHJvcGVydHkoJ3RyZW5kJyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuYW5hbHlzaXMpLnRvSGF2ZVByb3BlcnR5KCdncm93dGhSYXRlJyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuYW5hbHlzaXMudHJlbmQpLnRvQmUoJ2luY3JlYXNpbmcnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5hbmFseXNpcy5ncm93dGhSYXRlKS50b0JlR3JlYXRlclRoYW4oMCk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2dlbmVyYXRlU2NhblJlcG9ydCcsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgZ2VuZXJhdGUgc2NhbiBwZXJmb3JtYW5jZSByZXBvcnQnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHNjYW5PcHRpb25zID0ge1xyXG4gICAgICAgIGluY2x1ZGVTY2FuRGV0YWlsczogdHJ1ZSxcclxuICAgICAgICBpbmNsdWRlVnVsbmVyYWJpbGl0aWVzOiB0cnVlLFxyXG4gICAgICAgIHNjYW5UeXBlczogWyduZXR3b3JrJywgJ3dlYl9hcHBsaWNhdGlvbiddLFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgc2NhblJlcG9zaXRvcnkuZmluZC5tb2NrUmVzb2x2ZWRWYWx1ZShbbW9ja1NjYW5dKTtcclxuXHJcbiAgICAgIGNvbnN0IG1vY2tTY2FuU3RhdHMgPSBbXHJcbiAgICAgICAgeyBzY2FuVHlwZTogJ25ldHdvcmsnLCBjb3VudDogJzUnLCBhdmdEdXJhdGlvbjogJzEyMCcgfSxcclxuICAgICAgICB7IHNjYW5UeXBlOiAnd2ViX2FwcGxpY2F0aW9uJywgY291bnQ6ICczJywgYXZnRHVyYXRpb246ICcxODAnIH0sXHJcbiAgICAgIF07XHJcblxyXG4gICAgICBzY2FuUmVwb3NpdG9yeS5jcmVhdGVRdWVyeUJ1aWxkZXIoKS5nZXRSYXdNYW55Lm1vY2tSZXNvbHZlZFZhbHVlKG1vY2tTY2FuU3RhdHMpO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc2VydmljZS5nZW5lcmF0ZVNjYW5SZXBvcnQoc2Nhbk9wdGlvbnMpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ3NjYW5zJyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdzdGF0aXN0aWNzJyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdwZXJmb3JtYW5jZScpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LnNjYW5zKS50b0hhdmVMZW5ndGgoMSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuc3RhdGlzdGljcykudG9IYXZlTGVuZ3RoKDIpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdleHBvcnRSZXBvcnQnLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIGV4cG9ydCByZXBvcnQgaW4gSlNPTiBmb3JtYXQnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHJlcG9ydERhdGEgPSB7XHJcbiAgICAgICAgdGl0bGU6ICdUZXN0IFJlcG9ydCcsXHJcbiAgICAgICAgZGF0YTogW21vY2tWdWxuZXJhYmlsaXR5XSxcclxuICAgICAgICBnZW5lcmF0ZWRBdDogbmV3IERhdGUoKSxcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHNlcnZpY2UuZXhwb3J0UmVwb3J0KHJlcG9ydERhdGEsICdqc29uJyk7XHJcblxyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0hhdmVQcm9wZXJ0eSgnZm9ybWF0Jyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdjb250ZW50Jyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdmaWxlbmFtZScpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LmZvcm1hdCkudG9CZSgnanNvbicpO1xyXG4gICAgICBleHBlY3QodHlwZW9mIHJlc3VsdC5jb250ZW50KS50b0JlKCdzdHJpbmcnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5maWxlbmFtZSkudG9NYXRjaCgvXFwuanNvbiQvKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgZXhwb3J0IHJlcG9ydCBpbiBDU1YgZm9ybWF0JywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCByZXBvcnREYXRhID0ge1xyXG4gICAgICAgIHRpdGxlOiAnVGVzdCBSZXBvcnQnLFxyXG4gICAgICAgIGRhdGE6IFttb2NrVnVsbmVyYWJpbGl0eV0sXHJcbiAgICAgICAgZ2VuZXJhdGVkQXQ6IG5ldyBEYXRlKCksXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzZXJ2aWNlLmV4cG9ydFJlcG9ydChyZXBvcnREYXRhLCAnY3N2Jyk7XHJcblxyXG4gICAgICBleHBlY3QocmVzdWx0LmZvcm1hdCkudG9CZSgnY3N2Jyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuY29udGVudCkudG9Db250YWluKCdpZGVudGlmaWVyLHRpdGxlLHNldmVyaXR5Jyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuZmlsZW5hbWUpLnRvTWF0Y2goL1xcLmNzdiQvKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgaGFuZGxlIHVuc3VwcG9ydGVkIGV4cG9ydCBmb3JtYXRzJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCByZXBvcnREYXRhID0ge1xyXG4gICAgICAgIHRpdGxlOiAnVGVzdCBSZXBvcnQnLFxyXG4gICAgICAgIGRhdGE6IFtdLFxyXG4gICAgICAgIGdlbmVyYXRlZEF0OiBuZXcgRGF0ZSgpLFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgYXdhaXQgZXhwZWN0KFxyXG4gICAgICAgIHNlcnZpY2UuZXhwb3J0UmVwb3J0KHJlcG9ydERhdGEsICd1bnN1cHBvcnRlZCcgYXMgYW55KVxyXG4gICAgICApLnJlamVjdHMudG9UaHJvdygnVW5zdXBwb3J0ZWQgZXhwb3J0IGZvcm1hdCcpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdzY2hlZHVsZVJlcG9ydCcsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgc2NoZWR1bGUgYSByZWN1cnJpbmcgcmVwb3J0JywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCBzY2hlZHVsZU9wdGlvbnMgPSB7XHJcbiAgICAgICAgcmVwb3J0VHlwZTogJ2V4ZWN1dGl2ZV9zdW1tYXJ5JyBhcyBjb25zdCxcclxuICAgICAgICBzY2hlZHVsZTogJzAgOCAqICogMScsIC8vIFdlZWtseSBNb25kYXkgOCBBTVxyXG4gICAgICAgIHJlY2lwaWVudHM6IFsnYWRtaW5AZXhhbXBsZS5jb20nXSxcclxuICAgICAgICBmb3JtYXQ6ICdwZGYnIGFzIGNvbnN0LFxyXG4gICAgICAgIG9wdGlvbnM6IHtcclxuICAgICAgICAgIGluY2x1ZGVBc3Nlc3NtZW50czogdHJ1ZSxcclxuICAgICAgICB9LFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc2VydmljZS5zY2hlZHVsZVJlcG9ydChzY2hlZHVsZU9wdGlvbnMpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ3NjaGVkdWxlSWQnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ25leHRSdW4nKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ3N0YXR1cycpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LnN0YXR1cykudG9CZSgnc2NoZWR1bGVkJyk7XHJcbiAgICB9KTtcclxuICB9KTtcclxufSk7XHJcbiJdLCJ2ZXJzaW9uIjozfQ==