3f9bcea4558f652c6cb87917811343c0
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HighRiskVulnerabilitySpecification = exports.CriticalVulnerabilitySpecification = void 0;
const base_specification_1 = require("../../../shared-kernel/domain/base-specification");
const threat_severity_enum_1 = require("../enums/threat-severity.enum");
const confidence_level_enum_1 = require("../enums/confidence-level.enum");
/**
 * Critical Vulnerability Specification
 *
 * Defines business rules for identifying critical vulnerabilities that require
 * immediate attention and emergency response procedures.
 *
 * Critical vulnerability criteria:
 * - Critical or high severity with high confidence
 * - Actively exploited vulnerabilities
 * - Vulnerabilities affecting critical assets
 * - High CVSS scores (9.0+)
 * - Externally exposed vulnerabilities
 * - Zero-day vulnerabilities
 * - Vulnerabilities with public exploits
 */
class CriticalVulnerabilitySpecification extends base_specification_1.BaseSpecification {
    /**
     * Check if the vulnerability meets critical criteria
     */
    isSatisfiedBy(vulnerability) {
        return this.hasCriticalSeverityAndConfidence(vulnerability) ||
            this.isActivelyExploited(vulnerability) ||
            this.affectsCriticalAssets(vulnerability) ||
            this.hasHighCVSSScore(vulnerability) ||
            this.isExternallyExposed(vulnerability) ||
            this.hasCriticalType(vulnerability) ||
            this.hasCriticalCategory(vulnerability) ||
            this.hasPublicExploits(vulnerability) ||
            this.hasHighRiskScore(vulnerability);
    }
    /**
     * Check if vulnerability has critical severity and high confidence
     */
    hasCriticalSeverityAndConfidence(vulnerability) {
        return vulnerability.severity === threat_severity_enum_1.ThreatSeverity.CRITICAL &&
            this.isHighConfidence(vulnerability.confidence);
    }
    /**
     * Check if vulnerability is actively exploited
     */
    isActivelyExploited(vulnerability) {
        const exploitation = vulnerability.exploitation;
        return exploitation?.status === 'active_exploitation' ||
            exploitation?.status === 'weaponized';
    }
    /**
     * Check if vulnerability affects critical assets
     */
    affectsCriticalAssets(vulnerability) {
        return vulnerability.affectedAssets.some(asset => asset.criticality === 'critical');
    }
    /**
     * Check if vulnerability has high CVSS score
     */
    hasHighCVSSScore(vulnerability) {
        return vulnerability.cvssScores.some(score => score.baseScore >= CriticalVulnerabilitySpecification.CRITICAL_CVSS_THRESHOLD);
    }
    /**
     * Check if vulnerability is externally exposed
     */
    isExternallyExposed(vulnerability) {
        return vulnerability.affectedAssets.some(asset => asset.exposure === 'external' || asset.exposure === 'cloud');
    }
    /**
     * Check if vulnerability has critical type
     */
    hasCriticalType(vulnerability) {
        const type = vulnerability.type.toLowerCase();
        return CriticalVulnerabilitySpecification.CRITICAL_VULNERABILITY_TYPES.some(criticalType => type.includes(criticalType));
    }
    /**
     * Check if vulnerability has critical category
     */
    hasCriticalCategory(vulnerability) {
        const category = vulnerability.category.toLowerCase();
        return CriticalVulnerabilitySpecification.CRITICAL_CATEGORIES.some(criticalCategory => category.includes(criticalCategory));
    }
    /**
     * Check if vulnerability has public exploits
     */
    hasPublicExploits(vulnerability) {
        const exploitation = vulnerability.exploitation;
        return exploitation?.availableExploits.some(exploit => exploit.type === 'public' && exploit.reliability >= 70) || false;
    }
    /**
     * Check if vulnerability has high risk score
     */
    hasHighRiskScore(vulnerability) {
        return vulnerability.riskAssessment.riskScore >= CriticalVulnerabilitySpecification.CRITICAL_RISK_THRESHOLD;
    }
    /**
     * Check if confidence level is high
     */
    isHighConfidence(confidence) {
        return confidence === confidence_level_enum_1.ConfidenceLevel.HIGH ||
            confidence === confidence_level_enum_1.ConfidenceLevel.VERY_HIGH ||
            confidence === confidence_level_enum_1.ConfidenceLevel.CONFIRMED;
    }
    /**
     * Get specification description
     */
    getDescription() {
        return 'Identifies critical vulnerabilities requiring immediate attention based on severity, exploitation status, asset criticality, CVSS scores, and exposure.';
    }
    /**
     * Get specification criteria
     */
    getCriteria() {
        return [
            'Critical severity with high confidence',
            'Active exploitation or weaponization',
            'Affects critical assets',
            `CVSS score >= ${CriticalVulnerabilitySpecification.CRITICAL_CVSS_THRESHOLD}`,
            'External or cloud exposure',
            'Critical vulnerability types (RCE, privilege escalation, etc.)',
            'Critical categories (injection, authentication bypass, etc.)',
            'Public exploits available',
            `Risk score >= ${CriticalVulnerabilitySpecification.CRITICAL_RISK_THRESHOLD}`,
        ];
    }
    /**
     * Get remediation urgency for critical vulnerabilities
     */
    getRemediationUrgency(vulnerability) {
        if (this.isActivelyExploited(vulnerability) ||
            this.hasPublicExploits(vulnerability)) {
            return 'immediate';
        }
        if (this.hasCriticalSeverityAndConfidence(vulnerability) ||
            this.hasHighCVSSScore(vulnerability)) {
            return 'urgent';
        }
        return 'high';
    }
    /**
     * Get response timeline in hours
     */
    getResponseTimeline(vulnerability) {
        const urgency = this.getRemediationUrgency(vulnerability);
        switch (urgency) {
            case 'immediate': return 2; // 2 hours
            case 'urgent': return 8; // 8 hours
            case 'high': return 24; // 24 hours
            default: return 72; // 72 hours
        }
    }
    /**
     * Get escalation requirements
     */
    getEscalationRequirements(vulnerability) {
        const isActivelyExploited = this.isActivelyExploited(vulnerability);
        const affectsCritical = this.affectsCriticalAssets(vulnerability);
        const isExternallyExposed = this.isExternallyExposed(vulnerability);
        const hasPublicExploits = this.hasPublicExploits(vulnerability);
        return {
            executiveNotification: isActivelyExploited ||
                (affectsCritical && isExternallyExposed),
            emergencyResponse: isActivelyExploited && affectsCritical,
            externalConsultation: isActivelyExploited ||
                (hasPublicExploits && affectsCritical),
            complianceReporting: affectsCritical || isExternallyExposed,
            publicDisclosure: isActivelyExploited && isExternallyExposed,
        };
    }
    /**
     * Get containment actions
     */
    getContainmentActions(vulnerability) {
        const actions = [];
        if (this.isActivelyExploited(vulnerability)) {
            actions.push('immediate_isolation', 'traffic_blocking', 'service_shutdown');
        }
        if (this.isExternallyExposed(vulnerability)) {
            actions.push('firewall_rules', 'waf_protection', 'access_restriction');
        }
        if (this.affectsCriticalAssets(vulnerability)) {
            actions.push('asset_isolation', 'network_segmentation', 'monitoring_enhancement');
        }
        if (this.hasPublicExploits(vulnerability)) {
            actions.push('signature_deployment', 'behavior_monitoring', 'threat_hunting');
        }
        return actions;
    }
    /**
     * Get notification channels
     */
    getNotificationChannels(vulnerability) {
        const channels = ['email', 'webhook'];
        const escalation = this.getEscalationRequirements(vulnerability);
        if (escalation.emergencyResponse) {
            channels.push('sms', 'pager', 'phone', 'emergency_hotline');
        }
        else if (escalation.executiveNotification) {
            channels.push('sms', 'executive_dashboard');
        }
        if (escalation.complianceReporting) {
            channels.push('compliance_portal');
        }
        channels.push('slack', 'security_dashboard');
        return channels;
    }
    /**
     * Get monitoring requirements
     */
    getMonitoringRequirements(vulnerability) {
        const isActivelyExploited = this.isActivelyExploited(vulnerability);
        const hasPublicExploits = this.hasPublicExploits(vulnerability);
        const affectsCritical = this.affectsCriticalAssets(vulnerability);
        return {
            continuousMonitoring: isActivelyExploited || hasPublicExploits,
            threatHunting: isActivelyExploited || (hasPublicExploits && affectsCritical),
            behaviorAnalysis: isActivelyExploited,
            networkMonitoring: this.isExternallyExposed(vulnerability),
            endpointMonitoring: affectsCritical,
            logAnalysis: true, // Always required for critical vulnerabilities
        };
    }
    /**
     * Get compliance requirements
     */
    getComplianceRequirements(vulnerability) {
        const escalation = this.getEscalationRequirements(vulnerability);
        const affectsCritical = this.affectsCriticalAssets(vulnerability);
        const immediateReporting = [];
        const scheduledReporting = [];
        if (escalation.complianceReporting) {
            if (this.isActivelyExploited(vulnerability)) {
                immediateReporting.push('CISA', 'SEC', 'GDPR_DPA');
            }
            if (affectsCritical) {
                scheduledReporting.push('SOX', 'PCI_DSS', 'HIPAA');
            }
        }
        return {
            immediateReporting,
            scheduledReporting,
            documentationRequired: true,
            auditTrailRequired: true,
            riskAssessmentRequired: affectsCritical,
        };
    }
    /**
     * Get risk mitigation strategies
     */
    getRiskMitigationStrategies(vulnerability) {
        const immediate = [];
        const shortTerm = [];
        const longTerm = [];
        if (this.isActivelyExploited(vulnerability)) {
            immediate.push('isolate_affected_systems', 'block_attack_vectors', 'activate_incident_response');
        }
        if (this.hasPublicExploits(vulnerability)) {
            immediate.push('deploy_detection_signatures', 'enhance_monitoring');
            shortTerm.push('apply_emergency_patches', 'implement_workarounds');
        }
        if (this.isExternallyExposed(vulnerability)) {
            immediate.push('restrict_external_access', 'deploy_waf_rules');
            shortTerm.push('review_network_architecture', 'implement_zero_trust');
        }
        if (this.affectsCriticalAssets(vulnerability)) {
            shortTerm.push('asset_hardening', 'privilege_review', 'backup_verification');
            longTerm.push('architecture_review', 'security_controls_enhancement');
        }
        longTerm.push('vulnerability_management_improvement', 'security_awareness_training');
        return { immediate, shortTerm, longTerm };
    }
}
exports.CriticalVulnerabilitySpecification = CriticalVulnerabilitySpecification;
CriticalVulnerabilitySpecification.CRITICAL_CVSS_THRESHOLD = 9.0;
CriticalVulnerabilitySpecification.HIGH_CVSS_THRESHOLD = 7.0;
CriticalVulnerabilitySpecification.CRITICAL_RISK_THRESHOLD = 80;
CriticalVulnerabilitySpecification.HIGH_RISK_THRESHOLD = 70;
CriticalVulnerabilitySpecification.CRITICAL_VULNERABILITY_TYPES = [
    'remote_code_execution',
    'privilege_escalation',
    'authentication_bypass',
    'sql_injection',
    'buffer_overflow',
    'zero_day',
    'wormable',
];
CriticalVulnerabilitySpecification.CRITICAL_CATEGORIES = [
    'remote_code_execution',
    'privilege_escalation',
    'authentication',
    'injection',
    'cryptographic',
    'memory_corruption',
];
/**
 * High Risk Vulnerability Specification
 *
 * Extended specification for vulnerabilities that are high risk but not necessarily critical.
 */
class HighRiskVulnerabilitySpecification extends base_specification_1.BaseSpecification {
    /**
     * Check if the vulnerability meets high risk criteria
     */
    isSatisfiedBy(vulnerability) {
        return this.hasHighSeverityAndConfidence(vulnerability) ||
            this.hasHighCVSSScore(vulnerability) ||
            this.hasHighRiskScore(vulnerability) ||
            this.hasExploitAvailable(vulnerability) ||
            this.affectsHighCriticalityAssets(vulnerability);
    }
    /**
     * Check if vulnerability has high severity and confidence
     */
    hasHighSeverityAndConfidence(vulnerability) {
        return vulnerability.severity === threat_severity_enum_1.ThreatSeverity.HIGH &&
            (vulnerability.confidence === confidence_level_enum_1.ConfidenceLevel.HIGH ||
                vulnerability.confidence === confidence_level_enum_1.ConfidenceLevel.VERY_HIGH ||
                vulnerability.confidence === confidence_level_enum_1.ConfidenceLevel.CONFIRMED);
    }
    /**
     * Check if vulnerability has high CVSS score
     */
    hasHighCVSSScore(vulnerability) {
        return vulnerability.cvssScores.some(score => score.baseScore >= HighRiskVulnerabilitySpecification.HIGH_CVSS_THRESHOLD);
    }
    /**
     * Check if vulnerability has high risk score
     */
    hasHighRiskScore(vulnerability) {
        return vulnerability.riskAssessment.riskScore >= HighRiskVulnerabilitySpecification.HIGH_RISK_THRESHOLD;
    }
    /**
     * Check if vulnerability has available exploits
     */
    hasExploitAvailable(vulnerability) {
        return vulnerability.exploitation?.availableExploits.length > 0 || false;
    }
    /**
     * Check if vulnerability affects high criticality assets
     */
    affectsHighCriticalityAssets(vulnerability) {
        return vulnerability.affectedAssets.some(asset => asset.criticality === 'high' || asset.criticality === 'critical');
    }
    /**
     * Get specification description
     */
    getDescription() {
        return 'Identifies high-risk vulnerabilities that require prioritized attention and accelerated remediation timelines.';
    }
    /**
     * Get specification criteria
     */
    getCriteria() {
        return [
            'High severity with high confidence',
            `CVSS score >= ${HighRiskVulnerabilitySpecification.HIGH_CVSS_THRESHOLD}`,
            `Risk score >= ${HighRiskVulnerabilitySpecification.HIGH_RISK_THRESHOLD}`,
            'Exploits available',
            'Affects high or critical assets',
        ];
    }
}
exports.HighRiskVulnerabilitySpecification = HighRiskVulnerabilitySpecification;
HighRiskVulnerabilitySpecification.HIGH_CVSS_THRESHOLD = 7.0;
HighRiskVulnerabilitySpecification.HIGH_RISK_THRESHOLD = 70;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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