{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\infrastructure\\jobs\\__tests__\\threat-analysis.processor.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAIrD,4EAA8F;AAC9F,oGAAuI;AACvI,sFAA2E;AAC3E,4GAAgG;AAChG,2GAAsG;AACtG,yFAAqF;AACrF,0GAAsG;AAEtG,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;IACvC,IAAI,SAAkC,CAAC;IACvC,IAAI,gBAA6D,CAAC;IAClE,IAAI,eAAqD,CAAC;IAC1D,IAAI,aAA6D,CAAC;IAClE,IAAI,yBAAiE,CAAC;IACtE,IAAI,aAAyC,CAAC;IAC9C,IAAI,mBAAqD,CAAC;IAE1D,MAAM,sBAAsB,GAAgC;QAC1D,EAAE,EAAE,sCAAsC;QAC1C,KAAK,EAAE,mBAAmB;QAC1B,WAAW,EAAE,sCAAsC;QACnD,UAAU,EAAE,uCAAU,CAAC,GAAG;QAC1B,QAAQ,EAAE,2CAAc,CAAC,IAAI;QAC7B,UAAU,EAAE,6CAAgB,CAAC,IAAI;QACjC,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,QAAQ,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QAChC,eAAe,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC;QAC5C,iBAAiB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QAC/B,WAAW,EAAE;YACX,OAAO,EAAE,CAAC,gBAAgB,EAAE,aAAa,CAAC;YAC1C,UAAU,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;SACvC;QACD,gBAAgB,EAAE;YAChB,aAAa,EAAE,eAAe;YAC9B,cAAc,EAAE,CAAC,eAAe,CAAC;SAClC;QACD,gBAAgB,EAAE,CAAC;QACnB,SAAS,EAAE,GAAG;QACd,YAAY,EAAE,KAAK;QACnB,UAAU,EAAE,EAAE;KACf,CAAC;IAEF,MAAM,eAAe,GAAyB;QAC5C,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;QACnC,SAAS,EAAE,KAAK;QAChB,mBAAmB,EAAE,UAAU;QAC/B,QAAQ,EAAE,IAAI;QACd,eAAe,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;QAC7C,iBAAiB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QACrC,cAAc,EAAE;YACd,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,0BAA0B,EAAE;YAC1D,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAE;SACjD;QACD,KAAK,EAAE;YACL,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,UAAU,EAAE;SAC5C;KACF,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,cAAc,GAAG;YACrB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;YAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;SAClB,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,mDAAuB;gBACvB;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,+CAAkB,CAAC;oBAC/C,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,iCAAW,CAAC;oBACxC,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,sDAAqB,CAAC;oBAClD,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,uDAAyB;oBAClC,QAAQ,EAAE;wBACR,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;qBAC9B;iBACF;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE;wBACR,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;wBAChB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;wBACf,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;wBACd,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;qBACjB;iBACF;gBACD;oBACE,OAAO,EAAE,0CAAmB;oBAC5B,QAAQ,EAAE;wBACR,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;wBAC/B,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;qBAChC;iBACF;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,SAAS,GAAG,MAAM,CAAC,GAAG,CAA0B,mDAAuB,CAAC,CAAC;QACzE,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,+CAAkB,CAAC,CAAC,CAAC;QACtE,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,iCAAW,CAAC,CAAC,CAAC;QAC9D,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,sDAAqB,CAAC,CAAC,CAAC;QACtE,yBAAyB,GAAG,MAAM,CAAC,GAAG,CAAC,uDAAyB,CAAC,CAAC;QAClE,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,8BAAa,CAAC,CAAC;QAC1C,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,0CAAmB,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,OAAO,GAA0B;gBACrC,QAAQ,EAAE,sBAAsB,CAAC,EAAE;gBACnC,YAAY,EAAE,aAAa;gBAC3B,MAAM,EAAE,SAAS;aAClB,CAAC;YAEF,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;aACqB,CAAC;YAE3C,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC;gBACzC,GAAG,sBAAsB;gBACzB,UAAU,EAAE,EAAE;aACO,CAAC,CAAC;YAEzB,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAkB,CAAC,CAAC;YAE3E,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;YAElE,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACpD,KAAK,EAAE,EAAE,EAAE,EAAE,sBAAsB,CAAC,EAAE,EAAE;gBACxC,SAAS,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;aACzC,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAChD,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,QAAQ,EAAE,sBAAsB,CAAC,EAAE;gBACnC,eAAe,EAAE,MAAM,CAAC,eAAe,CAAC;oBACtC,MAAM,CAAC,gBAAgB,CAAC;wBACtB,OAAO,EAAE,eAAe,CAAC,EAAE;wBAC3B,SAAS,EAAE,eAAe,CAAC,IAAI;wBAC/B,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC9B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;qBAC3B,CAAC;iBACH,CAAC;gBACF,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC9B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC/B,CAAC,CAAC;YAEH,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,OAAO,GAA0B;gBACrC,QAAQ,EAAE,aAAa;gBACvB,YAAY,EAAE,aAAa;gBAC3B,MAAM,EAAE,SAAS;aAClB,CAAC;YAEF,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;aACqB,CAAC;YAE3C,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEjD,MAAM,MAAM,CAAC,SAAS,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;iBACvD,OAAO,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,KAAK,IAAI,EAAE;YAC7E,MAAM,OAAO,GAA0B;gBACrC,QAAQ,EAAE,sBAAsB,CAAC,EAAE;gBACnC,YAAY,EAAE,aAAa;gBAC3B,MAAM,EAAE,SAAS;aAClB,CAAC;YAEF,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;aACqB,CAAC;YAE3C,MAAM,MAAM,GAAG;gBACb,GAAG,sBAAsB;gBACzB,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,IAAI;aACE,CAAC;YAExB,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YACnD,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAkB,CAAC,CAAC;YAE3E,6BAA6B;YAC7B,IAAI,CAAC,KAAK,CAAC,SAAgB,EAAE,qBAAqB,CAAC,CAAC,eAAe,CAAC;gBAClE,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,CAAC;aAChE,CAAC,CAAC;YAEH,MAAM,SAAS,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;YAEnD,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAChD,MAAM,CAAC,gBAAgB,CAAC;gBACtB,aAAa,EAAE,eAAe,CAAC,EAAE;gBACjC,YAAY,EAAE,IAAI;gBAClB,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBACxC,mBAAmB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACvC,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;iBAClC,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,OAAO,GAA0B;gBACrC,QAAQ,EAAE,sBAAsB,CAAC,EAAE;gBACnC,YAAY,EAAE,SAAS;gBACvB,MAAM,EAAE,SAAS;aAClB,CAAC;YAEF,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;aACqB,CAAC;YAE3C,MAAM,MAAM,GAAG;gBACb,GAAG,sBAAsB;gBACzB,SAAS,EAAE,GAAG;gBACd,UAAU,EAAE,EAAE;aACO,CAAC;YAExB,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAEnD,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAE5D,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,QAAQ,EAAE,sBAAsB,CAAC,EAAE;gBACnC,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC5B,OAAO,EAAE,MAAM,CAAC,eAAe,CAAC;oBAC9B,MAAM,CAAC,gBAAgB,CAAC;wBACtB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACzB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC3B,CAAC;iBACH,CAAC;gBACF,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC/B,CAAC,CAAC;YAEH,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAChD,MAAM,CAAC,gBAAgB,CAAC;gBACtB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CACH,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,OAAO,GAA0B;gBACrC,QAAQ,EAAE,sBAAsB,CAAC,EAAE;gBACnC,YAAY,EAAE,SAAS;gBACvB,MAAM,EAAE,SAAS;aAClB,CAAC;YAEF,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;aACqB,CAAC;YAE3C,MAAM,MAAM,GAAG;gBACb,GAAG,sBAAsB;gBACzB,SAAS,EAAE,GAAG,EAAE,YAAY;gBAC5B,UAAU,EAAE,EAAE;aACO,CAAC;YAExB,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAEnD,oDAAoD;YACpD,IAAI,CAAC,KAAK,CAAC,SAAgB,EAAE,wBAAwB,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAE5E,MAAM,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAE7C,MAAM,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAAC;gBACpE,MAAM,EAAE,SAAS;gBACjB,KAAK,EAAE,sBAAsB;gBAC7B,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC;gBAC7D,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC5B,QAAQ,EAAE,sBAAsB,CAAC,EAAE;oBACnC,QAAQ,EAAE,GAAG;oBACb,QAAQ,EAAE,GAAG;oBACb,MAAM,EAAE,GAAG;iBACZ,CAAC;aACH,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YACxC,MAAM,OAAO,GAA0B;gBACrC,MAAM;gBACN,YAAY,EAAE,YAAY;gBAC1B,MAAM,EAAE,SAAS;aAClB,CAAC;YAEF,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;aACqB,CAAC;YAE3C,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,MAAM;gBACV,OAAO,EAAE,YAAY;gBACrB,KAAK,EAAE,eAAe;gBACtB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;aACzB,CAAC;YAEF,aAAa,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAc,CAAC,CAAC;YAExD,MAAM,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAE7C,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY;YAC/D,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa;YAChE,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAChC,MAAM,OAAO,GAA0B;gBACrC,MAAM;gBACN,YAAY,EAAE,YAAY;gBAC1B,MAAM,EAAE,SAAS;aAClB,CAAC;YAEF,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;aACqB,CAAC;YAE3C,aAAa,CAAC,OAAO;iBAClB,qBAAqB,CAAC,EAAE,EAAE,EAAE,MAAM,EAAS,CAAC;iBAC5C,qBAAqB,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAEtD,MAAM,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAE7C,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAC7C,sBAAsB,EACtB,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,MAAM;gBACb,KAAK,EAAE,gBAAgB;aACxB,CAAC,CACH,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,OAAO,GAA0B;gBACrC,QAAQ,EAAE,sBAAsB,CAAC,EAAE;gBACnC,YAAY,EAAE,aAAa;gBAC3B,MAAM,EAAE,SAAS;aAClB,CAAC;YAEF,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;aACqB,CAAC;YAE3C,MAAM,gBAAgB,GAAG;gBACvB;oBACE,cAAc,EAAE,CAAC,sBAAsB,CAAC;oBACxC,gBAAgB,EAAE,GAAG;oBACrB,eAAe,EAAE,OAAO;iBACzB;aACF,CAAC;YAEF,yBAAyB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAEjF,MAAM,SAAS,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAEjD,MAAM,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC;gBACxE,oBAAoB,EAAE,sBAAsB,CAAC,EAAE;gBAC/C,gBAAgB,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,CAAC;gBACnE,UAAU,EAAE,EAAE;gBACd,mBAAmB,EAAE,GAAG;aACzB,CAAC,CAAC;YAEH,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,OAAO,GAA0B;gBACrC,QAAQ,EAAE,YAAY;gBACtB,YAAY,EAAE,aAAa;gBAC3B,MAAM,EAAE,SAAS;aAClB,CAAC;YAEF,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;aACqB,CAAC;YAE3C,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC;YAEpF,MAAM,MAAM,CAAC,SAAS,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;iBACvD,OAAO,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;YAEjD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAC9C,6BAA6B,EAC7B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,QAAQ;gBACf,QAAQ,EAAE,YAAY;gBACtB,KAAK,EAAE,4BAA4B;aACpC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,OAAO,GAAG;gBACd,eAAe,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC;gBAC5C,UAAU,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;gBACtC,iBAAiB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;gBAC/B,aAAa,EAAE,eAAe;aAC/B,CAAC;YAEF,MAAM,KAAK,GAAI,SAAiB,CAAC,mBAAmB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YAE/E,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;gBACpB,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC9B,OAAO,EAAE,MAAM,CAAC,eAAe,CAAC;oBAC9B,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;oBAC1C,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC;oBAC7C,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;oBAC5C,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;iBAC3C,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,MAAM,GAAG;gBACb,GAAG,sBAAsB;gBACzB,QAAQ,EAAE,2CAAc,CAAC,QAAQ;gBACjC,UAAU,EAAE,6CAAgB,CAAC,IAAI;gBACjC,YAAY,EAAE,IAAI;gBAClB,gBAAgB,EAAE,EAAE;gBACpB,QAAQ,EAAE,IAAI,IAAI,EAAE;gBACpB,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC;gBAC5C,eAAe,EAAE,CAAC,YAAY,CAAC;aACV,CAAC;YAExB,MAAM,OAAO,GAAG,MAAO,SAAiB,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YAEzE,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CACrB,MAAM,CAAC,eAAe,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC;oBACtB,MAAM,EAAE,UAAU;oBAClB,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,EAAE,EAAE,gBAAgB;oBAC3B,MAAM,EAAE,GAAG,EAAE,YAAY;iBAC1B,CAAC;gBACF,MAAM,CAAC,gBAAgB,CAAC;oBACtB,MAAM,EAAE,YAAY;oBACpB,MAAM,EAAE,GAAG;oBACX,KAAK,EAAE,EAAE,EAAE,YAAY;oBACvB,MAAM,EAAE,GAAG,EAAE,WAAW;iBACzB,CAAC;gBACF,MAAM,CAAC,gBAAgB,CAAC;oBACtB,MAAM,EAAE,aAAa;oBACrB,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,CAAC,EAAE,iBAAiB;oBAC3B,MAAM,EAAE,GAAG,EAAE,WAAW;iBACzB,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\infrastructure\\jobs\\__tests__\\threat-analysis.processor.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { Job } from 'bull';\r\n\r\nimport { ThreatAnalysisProcessor, ThreatAnalysisJobData } from '../threat-analysis.processor';\r\nimport { ThreatIntelligence, ThreatType, ThreatSeverity, ThreatConfidence } from '../../../domain/entities/threat-intelligence.entity';\r\nimport { ThreatActor } from '../../../domain/entities/threat-actor.entity';\r\nimport { IndicatorOfCompromise } from '../../../domain/entities/indicator-of-compromise.entity';\r\nimport { ThreatIntelligenceService } from '../../../application/services/threat-intelligence.service';\r\nimport { LoggerService } from '../../../../../infrastructure/logging/logger.service';\r\nimport { NotificationService } from '../../../../../infrastructure/notification/notification.service';\r\n\r\ndescribe('ThreatAnalysisProcessor', () => {\r\n  let processor: ThreatAnalysisProcessor;\r\n  let threatRepository: jest.Mocked<Repository<ThreatIntelligence>>;\r\n  let actorRepository: jest.Mocked<Repository<ThreatActor>>;\r\n  let iocRepository: jest.Mocked<Repository<IndicatorOfCompromise>>;\r\n  let threatIntelligenceService: jest.Mocked<ThreatIntelligenceService>;\r\n  let loggerService: jest.Mocked<LoggerService>;\r\n  let notificationService: jest.Mocked<NotificationService>;\r\n\r\n  const mockThreatIntelligence: Partial<ThreatIntelligence> = {\r\n    id: '123e4567-e89b-12d3-a456-426614174000',\r\n    title: 'Test APT Campaign',\r\n    description: 'Test threat for attribution analysis',\r\n    threatType: ThreatType.APT,\r\n    severity: ThreatSeverity.HIGH,\r\n    confidence: ThreatConfidence.HIGH,\r\n    firstSeen: new Date('2023-01-01'),\r\n    lastSeen: new Date('2023-01-15'),\r\n    targetedSectors: ['healthcare', 'financial'],\r\n    targetedCountries: ['US', 'UK'],\r\n    mitreAttack: {\r\n      tactics: ['initial-access', 'persistence'],\r\n      techniques: ['T1566.001', 'T1053.005'],\r\n    },\r\n    technicalDetails: {\r\n      malwareFamily: 'Cobalt Strike',\r\n      infrastructure: ['*************'],\r\n    },\r\n    observationCount: 5,\r\n    riskScore: 7.5,\r\n    isAttributed: false,\r\n    indicators: [],\r\n  };\r\n\r\n  const mockThreatActor: Partial<ThreatActor> = {\r\n    id: 'actor-123',\r\n    name: 'APT29',\r\n    aliases: ['Cozy Bear', 'The Dukes'],\r\n    actorType: 'apt',\r\n    sophisticationLevel: 'advanced',\r\n    isActive: true,\r\n    targetedSectors: ['healthcare', 'government'],\r\n    targetedCountries: ['US', 'UK', 'DE'],\r\n    attackPatterns: [\r\n      { mitreId: 'T1566.001', name: 'Spearphishing Attachment' },\r\n      { mitreId: 'T1053.005', name: 'Scheduled Task' },\r\n    ],\r\n    tools: [\r\n      { name: 'Cobalt Strike', type: 'backdoor' },\r\n    ],\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const mockRepository = {\r\n      findOne: jest.fn(),\r\n      find: jest.fn(),\r\n      save: jest.fn(),\r\n      create: jest.fn(),\r\n      remove: jest.fn(),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        ThreatAnalysisProcessor,\r\n        {\r\n          provide: getRepositoryToken(ThreatIntelligence),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(ThreatActor),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(IndicatorOfCompromise),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: ThreatIntelligenceService,\r\n          useValue: {\r\n            findRelatedThreats: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: {\r\n            error: jest.fn(),\r\n            warn: jest.fn(),\r\n            log: jest.fn(),\r\n            debug: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: NotificationService,\r\n          useValue: {\r\n            sendUserNotification: jest.fn(),\r\n            sendRoleNotification: jest.fn(),\r\n          },\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    processor = module.get<ThreatAnalysisProcessor>(ThreatAnalysisProcessor);\r\n    threatRepository = module.get(getRepositoryToken(ThreatIntelligence));\r\n    actorRepository = module.get(getRepositoryToken(ThreatActor));\r\n    iocRepository = module.get(getRepositoryToken(IndicatorOfCompromise));\r\n    threatIntelligenceService = module.get(ThreatIntelligenceService);\r\n    loggerService = module.get(LoggerService);\r\n    notificationService = module.get(NotificationService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('handleAttributionAnalysis', () => {\r\n    it('should perform attribution analysis successfully', async () => {\r\n      const jobData: ThreatAnalysisJobData = {\r\n        threatId: mockThreatIntelligence.id,\r\n        analysisType: 'attribution',\r\n        userId: 'user123',\r\n      };\r\n\r\n      const mockJob = {\r\n        id: 'job123',\r\n        data: jobData,\r\n        progress: jest.fn(),\r\n      } as unknown as Job<ThreatAnalysisJobData>;\r\n\r\n      threatRepository.findOne.mockResolvedValue({\r\n        ...mockThreatIntelligence,\r\n        indicators: [],\r\n      } as ThreatIntelligence);\r\n\r\n      actorRepository.find.mockResolvedValue([mockThreatActor] as ThreatActor[]);\r\n\r\n      const result = await processor.handleAttributionAnalysis(mockJob);\r\n\r\n      expect(threatRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: mockThreatIntelligence.id },\r\n        relations: ['indicators', 'threatActor'],\r\n      });\r\n\r\n      expect(actorRepository.find).toHaveBeenCalledWith({\r\n        where: { isActive: true },\r\n      });\r\n\r\n      expect(result).toEqual({\r\n        threatId: mockThreatIntelligence.id,\r\n        suggestedActors: expect.arrayContaining([\r\n          expect.objectContaining({\r\n            actorId: mockThreatActor.id,\r\n            actorName: mockThreatActor.name,\r\n            confidence: expect.any(Number),\r\n            reasons: expect.any(Array),\r\n          }),\r\n        ]),\r\n        confidence: expect.any(Number),\r\n        analysisDate: expect.any(Date),\r\n      });\r\n\r\n      expect(mockJob.progress).toHaveBeenCalledWith(100);\r\n    });\r\n\r\n    it('should throw error when threat not found', async () => {\r\n      const jobData: ThreatAnalysisJobData = {\r\n        threatId: 'nonexistent',\r\n        analysisType: 'attribution',\r\n        userId: 'user123',\r\n      };\r\n\r\n      const mockJob = {\r\n        id: 'job123',\r\n        data: jobData,\r\n        progress: jest.fn(),\r\n      } as unknown as Job<ThreatAnalysisJobData>;\r\n\r\n      threatRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(processor.handleAttributionAnalysis(mockJob))\r\n        .rejects.toThrow('Threat intelligence not found: nonexistent');\r\n    });\r\n\r\n    it('should update threat with attribution when confidence is high', async () => {\r\n      const jobData: ThreatAnalysisJobData = {\r\n        threatId: mockThreatIntelligence.id,\r\n        analysisType: 'attribution',\r\n        userId: 'user123',\r\n      };\r\n\r\n      const mockJob = {\r\n        id: 'job123',\r\n        data: jobData,\r\n        progress: jest.fn(),\r\n      } as unknown as Job<ThreatAnalysisJobData>;\r\n\r\n      const threat = {\r\n        ...mockThreatIntelligence,\r\n        indicators: [],\r\n        threatActorId: null,\r\n      } as ThreatIntelligence;\r\n\r\n      threatRepository.findOne.mockResolvedValue(threat);\r\n      actorRepository.find.mockResolvedValue([mockThreatActor] as ThreatActor[]);\r\n\r\n      // Mock high confidence match\r\n      jest.spyOn(processor as any, 'calculateActorMatch').mockReturnValue({\r\n        confidence: 0.9,\r\n        reasons: ['Targets similar sectors', 'Uses similar techniques'],\r\n      });\r\n\r\n      await processor.handleAttributionAnalysis(mockJob);\r\n\r\n      expect(threatRepository.save).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          threatActorId: mockThreatActor.id,\r\n          isAttributed: true,\r\n          customAttributes: expect.objectContaining({\r\n            attributionAnalysis: expect.any(Object),\r\n            attributionDate: expect.any(Date),\r\n          }),\r\n        }),\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('handleThreatScoring', () => {\r\n    it('should calculate and update threat score', async () => {\r\n      const jobData: ThreatAnalysisJobData = {\r\n        threatId: mockThreatIntelligence.id,\r\n        analysisType: 'scoring',\r\n        userId: 'user123',\r\n      };\r\n\r\n      const mockJob = {\r\n        id: 'job123',\r\n        data: jobData,\r\n        progress: jest.fn(),\r\n      } as unknown as Job<ThreatAnalysisJobData>;\r\n\r\n      const threat = {\r\n        ...mockThreatIntelligence,\r\n        riskScore: 6.0,\r\n        indicators: [],\r\n      } as ThreatIntelligence;\r\n\r\n      threatRepository.findOne.mockResolvedValue(threat);\r\n\r\n      const result = await processor.handleThreatScoring(mockJob);\r\n\r\n      expect(result).toEqual({\r\n        threatId: mockThreatIntelligence.id,\r\n        oldScore: 6.0,\r\n        newScore: expect.any(Number),\r\n        factors: expect.arrayContaining([\r\n          expect.objectContaining({\r\n            factor: expect.any(String),\r\n            weight: expect.any(Number),\r\n            value: expect.any(Number),\r\n            impact: expect.any(Number),\r\n          }),\r\n        ]),\r\n        analysisDate: expect.any(Date),\r\n      });\r\n\r\n      expect(threatRepository.save).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          riskScore: expect.any(Number),\r\n        }),\r\n      );\r\n\r\n      expect(mockJob.progress).toHaveBeenCalledWith(100);\r\n    });\r\n\r\n    it('should send notification for significant score changes', async () => {\r\n      const jobData: ThreatAnalysisJobData = {\r\n        threatId: mockThreatIntelligence.id,\r\n        analysisType: 'scoring',\r\n        userId: 'user123',\r\n      };\r\n\r\n      const mockJob = {\r\n        id: 'job123',\r\n        data: jobData,\r\n        progress: jest.fn(),\r\n      } as unknown as Job<ThreatAnalysisJobData>;\r\n\r\n      const threat = {\r\n        ...mockThreatIntelligence,\r\n        riskScore: 5.0, // Old score\r\n        indicators: [],\r\n      } as ThreatIntelligence;\r\n\r\n      threatRepository.findOne.mockResolvedValue(threat);\r\n\r\n      // Mock scoring to return significantly higher score\r\n      jest.spyOn(processor as any, 'calculateWeightedScore').mockReturnValue(8.5);\r\n\r\n      await processor.handleThreatScoring(mockJob);\r\n\r\n      expect(notificationService.sendUserNotification).toHaveBeenCalledWith({\r\n        userId: 'user123',\r\n        title: 'Threat Score Updated',\r\n        message: expect.stringContaining('increased from 5.0 to 8.5'),\r\n        type: 'warning',\r\n        data: expect.objectContaining({\r\n          threatId: mockThreatIntelligence.id,\r\n          oldScore: 5.0,\r\n          newScore: 8.5,\r\n          change: 3.5,\r\n        }),\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('handleIOCEnrichment', () => {\r\n    it('should enrich multiple IOCs successfully', async () => {\r\n      const iocIds = ['ioc1', 'ioc2', 'ioc3'];\r\n      const jobData: ThreatAnalysisJobData = {\r\n        iocIds,\r\n        analysisType: 'enrichment',\r\n        userId: 'user123',\r\n      };\r\n\r\n      const mockJob = {\r\n        id: 'job123',\r\n        data: jobData,\r\n        progress: jest.fn(),\r\n      } as unknown as Job<ThreatAnalysisJobData>;\r\n\r\n      const mockIOC = {\r\n        id: 'ioc1',\r\n        iocType: 'ip_address',\r\n        value: '*************',\r\n        addEnrichment: jest.fn(),\r\n      };\r\n\r\n      iocRepository.findOne.mockResolvedValue(mockIOC as any);\r\n\r\n      await processor.handleIOCEnrichment(mockJob);\r\n\r\n      expect(iocRepository.findOne).toHaveBeenCalledTimes(3);\r\n      expect(mockJob.progress).toHaveBeenCalledWith(33); // First IOC\r\n      expect(mockJob.progress).toHaveBeenCalledWith(67); // Second IOC\r\n      expect(mockJob.progress).toHaveBeenCalledWith(100); // Third IOC\r\n    });\r\n\r\n    it('should handle IOC enrichment errors gracefully', async () => {\r\n      const iocIds = ['ioc1', 'ioc2'];\r\n      const jobData: ThreatAnalysisJobData = {\r\n        iocIds,\r\n        analysisType: 'enrichment',\r\n        userId: 'user123',\r\n      };\r\n\r\n      const mockJob = {\r\n        id: 'job123',\r\n        data: jobData,\r\n        progress: jest.fn(),\r\n      } as unknown as Job<ThreatAnalysisJobData>;\r\n\r\n      iocRepository.findOne\r\n        .mockResolvedValueOnce({ id: 'ioc1' } as any)\r\n        .mockRejectedValueOnce(new Error('Database error'));\r\n\r\n      await processor.handleIOCEnrichment(mockJob);\r\n\r\n      expect(loggerService.warn).toHaveBeenCalledWith(\r\n        'Failed to enrich IOC',\r\n        expect.objectContaining({\r\n          iocId: 'ioc2',\r\n          error: 'Database error',\r\n        }),\r\n      );\r\n\r\n      expect(mockJob.progress).toHaveBeenCalledWith(100);\r\n    });\r\n  });\r\n\r\n  describe('handleThreatCorrelation', () => {\r\n    it('should perform threat correlation analysis', async () => {\r\n      const jobData: ThreatAnalysisJobData = {\r\n        threatId: mockThreatIntelligence.id,\r\n        analysisType: 'correlation',\r\n        userId: 'user123',\r\n      };\r\n\r\n      const mockJob = {\r\n        id: 'job123',\r\n        data: jobData,\r\n        progress: jest.fn(),\r\n      } as unknown as Job<ThreatAnalysisJobData>;\r\n\r\n      const mockCorrelations = [\r\n        {\r\n          relatedThreats: [mockThreatIntelligence],\r\n          correlationScore: 0.8,\r\n          correlationType: 'actor',\r\n        },\r\n      ];\r\n\r\n      threatIntelligenceService.findRelatedThreats.mockResolvedValue(mockCorrelations);\r\n\r\n      await processor.handleThreatCorrelation(mockJob);\r\n\r\n      expect(threatIntelligenceService.findRelatedThreats).toHaveBeenCalledWith({\r\n        threatIntelligenceId: mockThreatIntelligence.id,\r\n        correlationTypes: ['actor', 'campaign', 'indicators', 'techniques'],\r\n        timeWindow: 90,\r\n        confidenceThreshold: 0.6,\r\n      });\r\n\r\n      expect(mockJob.progress).toHaveBeenCalledWith(100);\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle job failures gracefully', async () => {\r\n      const jobData: ThreatAnalysisJobData = {\r\n        threatId: 'invalid-id',\r\n        analysisType: 'attribution',\r\n        userId: 'user123',\r\n      };\r\n\r\n      const mockJob = {\r\n        id: 'job123',\r\n        data: jobData,\r\n        progress: jest.fn(),\r\n      } as unknown as Job<ThreatAnalysisJobData>;\r\n\r\n      threatRepository.findOne.mockRejectedValue(new Error('Database connection failed'));\r\n\r\n      await expect(processor.handleAttributionAnalysis(mockJob))\r\n        .rejects.toThrow('Database connection failed');\r\n\r\n      expect(loggerService.error).toHaveBeenCalledWith(\r\n        'Attribution analysis failed',\r\n        expect.objectContaining({\r\n          jobId: 'job123',\r\n          threatId: 'invalid-id',\r\n          error: 'Database connection failed',\r\n        }),\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('private helper methods', () => {\r\n    it('should calculate actor match correctly', async () => {\r\n      const factors = {\r\n        targetedSectors: ['healthcare', 'financial'],\r\n        techniques: ['T1566.001', 'T1053.005'],\r\n        targetedCountries: ['US', 'UK'],\r\n        malwareFamily: 'Cobalt Strike',\r\n      };\r\n\r\n      const match = (processor as any).calculateActorMatch(mockThreatActor, factors);\r\n\r\n      expect(match).toEqual({\r\n        confidence: expect.any(Number),\r\n        reasons: expect.arrayContaining([\r\n          expect.stringContaining('similar sectors'),\r\n          expect.stringContaining('similar techniques'),\r\n          expect.stringContaining('similar countries'),\r\n          expect.stringContaining('similar malware'),\r\n        ]),\r\n      });\r\n\r\n      expect(match.confidence).toBeGreaterThan(0);\r\n      expect(match.confidence).toBeLessThanOrEqual(1);\r\n    });\r\n\r\n    it('should calculate scoring factors correctly', async () => {\r\n      const threat = {\r\n        ...mockThreatIntelligence,\r\n        severity: ThreatSeverity.CRITICAL,\r\n        confidence: ThreatConfidence.HIGH,\r\n        isAttributed: true,\r\n        observationCount: 10,\r\n        lastSeen: new Date(),\r\n        indicators: [{ id: 'ioc1' }, { id: 'ioc2' }],\r\n        targetedSectors: ['healthcare'],\r\n      } as ThreatIntelligence;\r\n\r\n      const factors = await (processor as any).calculateScoringFactors(threat);\r\n\r\n      expect(factors).toEqual(\r\n        expect.arrayContaining([\r\n          expect.objectContaining({\r\n            factor: 'severity',\r\n            weight: 0.25,\r\n            value: 10, // Critical = 10\r\n            impact: 2.5, // 10 * 0.25\r\n          }),\r\n          expect.objectContaining({\r\n            factor: 'confidence',\r\n            weight: 0.2,\r\n            value: 10, // High = 10\r\n            impact: 2.0, // 10 * 0.2\r\n          }),\r\n          expect.objectContaining({\r\n            factor: 'attribution',\r\n            weight: 0.15,\r\n            value: 8, // Attributed = 8\r\n            impact: 1.2, // 8 * 0.15\r\n          }),\r\n        ]),\r\n      );\r\n    });\r\n  });\r\n});\r\n"], "version": 3}