{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\__tests__\\rate-limiting-integration.spec.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAAsD;AAEtD,2CAA8C;AAC9C,mDAAqC;AACrC,2CAAkE;AAClE,wEAAmE;AACnE,oFAAsE;AACtE,iDAAoE;AAEpE,+DAA+D;AAE/D,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAG3B,UAAU;QACR,OAAO,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;IACpD,CAAC;IAKD,SAAS;QACP,OAAO,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACnD,CAAC;IAKD,UAAU;QACR,OAAO,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;IACpD,CAAC;IAKD,UAAU;QACR,OAAO,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;IACxD,CAAC;IASD,UAAU;QACR,OAAO,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IACrD,CAAC;IAGD,UAAU;QACR,OAAO,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;IAC/C,CAAC;CACF,CAAA;AAxCC;IAFC,IAAA,YAAG,EAAC,SAAS,CAAC;IACd,IAAA,kBAAS,EAAC,iCAAc,CAAC;;;;yDAGzB;AAKD;IAHC,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,kBAAS,EAAC,iCAAc,CAAC;IACzB,IAAA,gCAAS,EAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,wBAAwB;;;;;wDAGjE;AAKD;IAHC,IAAA,YAAG,EAAC,SAAS,CAAC;IACd,IAAA,kBAAS,EAAC,iCAAc,CAAC;IACzB,IAAA,gCAAS,EAAC,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,0BAA0B;;;;;yDAGrE;AAKD;IAHC,IAAA,aAAI,EAAC,QAAQ,CAAC;IACd,IAAA,kBAAS,EAAC,iCAAc,CAAC;IACzB,IAAA,gCAAS,EAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC,2BAA2B;;;;;yDAGrE;AASD;IAPC,IAAA,YAAG,EAAC,UAAU,CAAC;IACf,IAAA,kBAAS,EAAC,iCAAc,CAAC;IACzB,IAAA,gCAAS,EAAC;QACT,KAAK,EAAE,EAAE;QACT,QAAQ,EAAE,KAAK;QACf,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,QAAQ,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE;KACxD,CAAC;;;;yDAGD;AAGD;IADC,IAAA,YAAG,EAAC,UAAU,CAAC;;;;yDAGf;AA1CG,uBAAuB;IAD5B,IAAA,mBAAU,EAAC,iBAAiB,CAAC;GACxB,uBAAuB,CA2C5B;AAED,wBAAwB;AACxB,MAAM,kBAAkB;IAGtB,WAAW,CAAC,OAAY;QACtB,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QACrC,MAAM,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QAElE,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,yBAAyB;YACzB,eAAe,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;QACnD,CAAC;QAED,MAAM,GAAG,GAAG,eAAe,CAAC,YAAY;YACtC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC;YACvC,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAE1C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,GAAG,GAAG,eAAe,CAAC,QAAQ,CAAC;QAEnD,IAAI,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEvD,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,SAAS,GAAG,WAAW,EAAE,CAAC;YACxD,WAAW,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,eAAe,CAAC,QAAQ,EAAE,CAAC;QACxE,CAAC;QAED,IAAI,WAAW,CAAC,KAAK,IAAI,eAAe,CAAC,KAAK,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC;YACtD,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACxB,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,mBAAmB;gBAC5B,KAAK,EAAE,qBAAqB;gBAC5B,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;aAC5D,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;QAED,WAAW,CAAC,KAAK,EAAE,CAAC;QACpB,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QAElD,yBAAyB;QACzB,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC;QACtD,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC;QAC/D,QAAQ,CAAC,SAAS,CAAC,uBAAuB,EAAE,eAAe,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;QACvF,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC;QAEjF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,KAAK;QACV,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;;AAlDc,2BAAQ,GAAG,IAAI,GAAG,EAAgD,CAAC;AAqDpF,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;IAC/C,IAAI,GAAqB,CAAC;IAE1B,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;iBACf,CAAC;gBACF,2BAAe,CAAC,OAAO,CAAC;oBACtB,GAAG,EAAE,EAAE;oBACP,KAAK,EAAE,EAAE;iBACV,CAAC;aACH;YACD,WAAW,EAAE,CAAC,uBAAuB,CAAC;YACtC,SAAS,EAAE;gBACT;oBACE,OAAO,EAAE,iCAAc;oBACvB,QAAQ,EAAE,kBAAkB;iBAC7B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;IACnB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,GAAG,EAAE;QACd,kBAAkB,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,+CAA+C;YAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,0BAA0B,CAAC;qBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;gBAClE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzD,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,gCAAgC;YAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5B,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,0BAA0B,CAAC;qBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;YAED,iCAAiC;YACjC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,0BAA0B,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACxD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACxD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,0BAA0B,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC5D,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,iCAAiC;YACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,yBAAyB,CAAC;qBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;YAED,gCAAgC;YAChC,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,yBAAyB,CAAC;iBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,8CAA8C;YAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5B,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,0BAA0B,CAAC;qBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;YAED,0BAA0B;YAC1B,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,0BAA0B,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,+BAA+B;YAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,yBAAyB,CAAC;qBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;YAED,oCAAoC;YACpC,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,yBAAyB,CAAC;iBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,yCAAyC;YACzC,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,0BAA0B,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,sCAAsC;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,IAAI,CAAC,yBAAyB,CAAC;qBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;YAED,gCAAgC;YAChC,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,yBAAyB,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;YAC1E,6BAA6B;YAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,IAAI,CAAC,yBAAyB,CAAC;qBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;YAED,yBAAyB;YACzB,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,yBAAyB,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,4BAA4B;YAC5B,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,0BAA0B,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,yCAAyC;YACzC,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CACjD,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzB,GAAG,CAAC,2BAA2B,CAAC;iBAChC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAC7B,CAAC;YAEF,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CACjD,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzB,GAAG,CAAC,2BAA2B,CAAC;iBAChC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAC7B,CAAC;YAEF,mDAAmD;YACnD,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC;YAExD,8CAA8C;YAC9C,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,2BAA2B,CAAC;iBAChC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC;iBACzB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,2BAA2B,CAAC;iBAChC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC;iBACzB,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,uDAAuD;YACvD,MAAM,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAC7C,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAC9D,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE9C,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBAC7D,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;YAChE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,SAAS,GAAG,EAAE,CAAC;YAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,0BAA0B,CAAC;qBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3B,CAAC;YAED,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACpC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzD,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACnF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;YACzC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,0BAA0B,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,SAAS,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;YAClE,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAElD,MAAM,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC,kBAAkB;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,mBAAmB;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5B,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,0BAA0B,CAAC;qBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;YAED,0CAA0C;YAC1C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,0BAA0B,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAC7C,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAC7D,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE9C,gDAAgD;YAChD,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,iCAAiC;YACjC,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,0BAA0B,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,gEAAgE;YAChE,MAAM,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAC7C,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAC5D,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CACrD,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;iBACtC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,MAAM,IAAI,GAAG,EAAE,CAAC,CAAC,CAC3D,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;YACpE,MAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;YAExE,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB;YACtD,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,8BAA8B;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,mBAAmB;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5B,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,0BAA0B,CAAC;qBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,0BAA0B,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,mBAAmB;gBAC5B,KAAK,EAAE,qBAAqB;gBAC5B,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC/B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,sBAAsB;YACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,yBAAyB,CAAC;qBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,yBAAyB,CAAC;iBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACxD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,yBAAyB;YACzB,MAAM,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAC7C,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAC7D,CAAC;YAEF,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE5B,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAErC,kDAAkD;YAClD,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,0BAA0B,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAErC,4CAA4C;YAC5C,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\__tests__\\rate-limiting-integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport * as request from 'supertest';\r\nimport { Controller, Get, Post, UseGuards } from '@nestjs/common';\r\nimport { RateLimitGuard } from '../common/guards/rate-limit.guard';\r\nimport { RateLimit } from '../common/decorators/rate-limit.decorator';\r\nimport { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';\r\n\r\n// Test controllers with different rate limiting configurations\r\n@Controller('rate-limit-test')\r\nclass RateLimitTestController {\r\n  @Get('default')\r\n  @UseGuards(RateLimitGuard)\r\n  getDefault() {\r\n    return { message: 'Default rate limit endpoint' };\r\n  }\r\n\r\n  @Get('strict')\r\n  @UseGuards(RateLimitGuard)\r\n  @RateLimit({ limit: 3, windowMs: 60000 }) // 3 requests per minute\r\n  getStrict() {\r\n    return { message: 'Strict rate limit endpoint' };\r\n  }\r\n\r\n  @Get('lenient')\r\n  @UseGuards(RateLimitGuard)\r\n  @RateLimit({ limit: 100, windowMs: 60000 }) // 100 requests per minute\r\n  getLenient() {\r\n    return { message: 'Lenient rate limit endpoint' };\r\n  }\r\n\r\n  @Post('create')\r\n  @UseGuards(RateLimitGuard)\r\n  @RateLimit({ limit: 5, windowMs: 300000 }) // 5 requests per 5 minutes\r\n  postCreate() {\r\n    return { message: 'Create endpoint with rate limit' };\r\n  }\r\n\r\n  @Get('per-user')\r\n  @UseGuards(RateLimitGuard)\r\n  @RateLimit({ \r\n    limit: 10, \r\n    windowMs: 60000,\r\n    keyGenerator: (req) => `user:${req.user?.id || req.ip}` \r\n  })\r\n  getPerUser() {\r\n    return { message: 'Per-user rate limit endpoint' };\r\n  }\r\n\r\n  @Get('no-limit')\r\n  getNoLimit() {\r\n    return { message: 'No rate limit endpoint' };\r\n  }\r\n}\r\n\r\n// Mock rate limit guard\r\nclass MockRateLimitGuard {\r\n  private static requests = new Map<string, { count: number; resetTime: number }>();\r\n\r\n  canActivate(context: any): boolean {\r\n    const request = context.switchToHttp().getRequest();\r\n    const handler = context.getHandler();\r\n    const rateLimitConfig = Reflect.getMetadata('rateLimit', handler);\r\n    \r\n    if (!rateLimitConfig) {\r\n      // Use default rate limit\r\n      rateLimitConfig = { limit: 10, windowMs: 60000 };\r\n    }\r\n\r\n    const key = rateLimitConfig.keyGenerator \r\n      ? rateLimitConfig.keyGenerator(request)\r\n      : `${request.ip}:${request.route.path}`;\r\n\r\n    const now = Date.now();\r\n    const windowStart = now - rateLimitConfig.windowMs;\r\n    \r\n    let requestData = MockRateLimitGuard.requests.get(key);\r\n    \r\n    if (!requestData || requestData.resetTime < windowStart) {\r\n      requestData = { count: 0, resetTime: now + rateLimitConfig.windowMs };\r\n    }\r\n\r\n    if (requestData.count >= rateLimitConfig.limit) {\r\n      const response = context.switchToHttp().getResponse();\r\n      response.status(429).json({\r\n        statusCode: 429,\r\n        message: 'Too Many Requests',\r\n        error: 'Rate limit exceeded',\r\n        retryAfter: Math.ceil((requestData.resetTime - now) / 1000),\r\n      });\r\n      return false;\r\n    }\r\n\r\n    requestData.count++;\r\n    MockRateLimitGuard.requests.set(key, requestData);\r\n    \r\n    // Add rate limit headers\r\n    const response = context.switchToHttp().getResponse();\r\n    response.setHeader('X-RateLimit-Limit', rateLimitConfig.limit);\r\n    response.setHeader('X-RateLimit-Remaining', rateLimitConfig.limit - requestData.count);\r\n    response.setHeader('X-RateLimit-Reset', Math.ceil(requestData.resetTime / 1000));\r\n\r\n    return true;\r\n  }\r\n\r\n  static reset() {\r\n    this.requests.clear();\r\n  }\r\n}\r\n\r\ndescribe('Rate Limiting Integration Tests', () => {\r\n  let app: INestApplication;\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n        }),\r\n        ThrottlerModule.forRoot({\r\n          ttl: 60,\r\n          limit: 10,\r\n        }),\r\n      ],\r\n      controllers: [RateLimitTestController],\r\n      providers: [\r\n        {\r\n          provide: RateLimitGuard,\r\n          useClass: MockRateLimitGuard,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    await app.init();\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  beforeEach(() => {\r\n    MockRateLimitGuard.reset();\r\n  });\r\n\r\n  describe('Default Rate Limiting', () => {\r\n    it('should allow requests within limit', async () => {\r\n      // Make 5 requests (within default limit of 10)\r\n      for (let i = 0; i < 5; i++) {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/rate-limit-test/default')\r\n          .expect(200);\r\n\r\n        expect(response.body.message).toBe('Default rate limit endpoint');\r\n        expect(response.headers['x-ratelimit-limit']).toBe('10');\r\n        expect(response.headers['x-ratelimit-remaining']).toBe(String(10 - (i + 1)));\r\n      }\r\n    });\r\n\r\n    it('should block requests exceeding limit', async () => {\r\n      // Make requests up to the limit\r\n      for (let i = 0; i < 10; i++) {\r\n        await request(app.getHttpServer())\r\n          .get('/rate-limit-test/default')\r\n          .expect(200);\r\n      }\r\n\r\n      // Next request should be blocked\r\n      const response = await request(app.getHttpServer())\r\n        .get('/rate-limit-test/default')\r\n        .expect(429);\r\n\r\n      expect(response.body.statusCode).toBe(429);\r\n      expect(response.body.message).toBe('Too Many Requests');\r\n      expect(response.body.error).toBe('Rate limit exceeded');\r\n      expect(response.body.retryAfter).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should include rate limit headers', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/rate-limit-test/default')\r\n        .expect(200);\r\n\r\n      expect(response.headers['x-ratelimit-limit']).toBe('10');\r\n      expect(response.headers['x-ratelimit-remaining']).toBe('9');\r\n      expect(response.headers['x-ratelimit-reset']).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('Custom Rate Limiting', () => {\r\n    it('should enforce strict rate limits', async () => {\r\n      // Make 3 requests (at the limit)\r\n      for (let i = 0; i < 3; i++) {\r\n        await request(app.getHttpServer())\r\n          .get('/rate-limit-test/strict')\r\n          .expect(200);\r\n      }\r\n\r\n      // 4th request should be blocked\r\n      await request(app.getHttpServer())\r\n        .get('/rate-limit-test/strict')\r\n        .expect(429);\r\n    });\r\n\r\n    it('should allow more requests with lenient limits', async () => {\r\n      // Make 50 requests (well within limit of 100)\r\n      for (let i = 0; i < 50; i++) {\r\n        await request(app.getHttpServer())\r\n          .get('/rate-limit-test/lenient')\r\n          .expect(200);\r\n      }\r\n\r\n      // Should still be allowed\r\n      await request(app.getHttpServer())\r\n        .get('/rate-limit-test/lenient')\r\n        .expect(200);\r\n    });\r\n\r\n    it('should handle different limits for different endpoints', async () => {\r\n      // Use up strict endpoint limit\r\n      for (let i = 0; i < 3; i++) {\r\n        await request(app.getHttpServer())\r\n          .get('/rate-limit-test/strict')\r\n          .expect(200);\r\n      }\r\n\r\n      // Strict endpoint should be blocked\r\n      await request(app.getHttpServer())\r\n        .get('/rate-limit-test/strict')\r\n        .expect(429);\r\n\r\n      // But lenient endpoint should still work\r\n      await request(app.getHttpServer())\r\n        .get('/rate-limit-test/lenient')\r\n        .expect(200);\r\n    });\r\n  });\r\n\r\n  describe('HTTP Method Specific Limits', () => {\r\n    it('should apply different limits to POST requests', async () => {\r\n      // Make 5 POST requests (at the limit)\r\n      for (let i = 0; i < 5; i++) {\r\n        await request(app.getHttpServer())\r\n          .post('/rate-limit-test/create')\r\n          .expect(201);\r\n      }\r\n\r\n      // 6th request should be blocked\r\n      await request(app.getHttpServer())\r\n        .post('/rate-limit-test/create')\r\n        .expect(429);\r\n    });\r\n\r\n    it('should not affect other endpoints when one is rate limited', async () => {\r\n      // Use up POST endpoint limit\r\n      for (let i = 0; i < 5; i++) {\r\n        await request(app.getHttpServer())\r\n          .post('/rate-limit-test/create')\r\n          .expect(201);\r\n      }\r\n\r\n      // POST should be blocked\r\n      await request(app.getHttpServer())\r\n        .post('/rate-limit-test/create')\r\n        .expect(429);\r\n\r\n      // But GET should still work\r\n      await request(app.getHttpServer())\r\n        .get('/rate-limit-test/default')\r\n        .expect(200);\r\n    });\r\n  });\r\n\r\n  describe('Per-User Rate Limiting', () => {\r\n    it('should track limits per user', async () => {\r\n      // Simulate requests from different users\r\n      const user1Requests = Array(5).fill(null).map(() =>\r\n        request(app.getHttpServer())\r\n          .get('/rate-limit-test/per-user')\r\n          .set('X-User-ID', 'user1')\r\n      );\r\n\r\n      const user2Requests = Array(5).fill(null).map(() =>\r\n        request(app.getHttpServer())\r\n          .get('/rate-limit-test/per-user')\r\n          .set('X-User-ID', 'user2')\r\n      );\r\n\r\n      // Both users should be able to make their requests\r\n      await Promise.all([...user1Requests, ...user2Requests]);\r\n\r\n      // Each user should still have remaining quota\r\n      await request(app.getHttpServer())\r\n        .get('/rate-limit-test/per-user')\r\n        .set('X-User-ID', 'user1')\r\n        .expect(200);\r\n\r\n      await request(app.getHttpServer())\r\n        .get('/rate-limit-test/per-user')\r\n        .set('X-User-ID', 'user2')\r\n        .expect(200);\r\n    });\r\n  });\r\n\r\n  describe('No Rate Limiting', () => {\r\n    it('should not apply rate limits to unguarded endpoints', async () => {\r\n      // Make many requests to endpoint without rate limiting\r\n      const requests = Array(20).fill(null).map(() =>\r\n        request(app.getHttpServer()).get('/rate-limit-test/no-limit')\r\n      );\r\n\r\n      const responses = await Promise.all(requests);\r\n      \r\n      responses.forEach(response => {\r\n        expect(response.status).toBe(200);\r\n        expect(response.body.message).toBe('No rate limit endpoint');\r\n        expect(response.headers['x-ratelimit-limit']).toBeUndefined();\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Rate Limit Headers', () => {\r\n    it('should provide accurate remaining count', async () => {\r\n      const responses = [];\r\n      \r\n      for (let i = 0; i < 5; i++) {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/rate-limit-test/default')\r\n          .expect(200);\r\n        \r\n        responses.push(response);\r\n      }\r\n\r\n      responses.forEach((response, index) => {\r\n        expect(response.headers['x-ratelimit-limit']).toBe('10');\r\n        expect(response.headers['x-ratelimit-remaining']).toBe(String(10 - (index + 1)));\r\n      });\r\n    });\r\n\r\n    it('should provide reset time', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/rate-limit-test/default')\r\n        .expect(200);\r\n\r\n      const resetTime = parseInt(response.headers['x-ratelimit-reset']);\r\n      const currentTime = Math.floor(Date.now() / 1000);\r\n      \r\n      expect(resetTime).toBeGreaterThan(currentTime);\r\n      expect(resetTime).toBeLessThanOrEqual(currentTime + 60); // Within 1 minute\r\n    });\r\n\r\n    it('should provide retry-after header when rate limited', async () => {\r\n      // Use up the limit\r\n      for (let i = 0; i < 10; i++) {\r\n        await request(app.getHttpServer())\r\n          .get('/rate-limit-test/default')\r\n          .expect(200);\r\n      }\r\n\r\n      // Next request should include retry-after\r\n      const response = await request(app.getHttpServer())\r\n        .get('/rate-limit-test/default')\r\n        .expect(429);\r\n\r\n      expect(response.body.retryAfter).toBeGreaterThan(0);\r\n      expect(response.body.retryAfter).toBeLessThanOrEqual(60);\r\n    });\r\n  });\r\n\r\n  describe('Concurrent Requests', () => {\r\n    it('should handle concurrent requests correctly', async () => {\r\n      // Make 10 concurrent requests (at the limit)\r\n      const requests = Array(10).fill(null).map(() =>\r\n        request(app.getHttpServer()).get('/rate-limit-test/default')\r\n      );\r\n\r\n      const responses = await Promise.all(requests);\r\n      \r\n      // All should succeed since they're at the limit\r\n      responses.forEach(response => {\r\n        expect(response.status).toBe(200);\r\n      });\r\n\r\n      // Next request should be blocked\r\n      await request(app.getHttpServer())\r\n        .get('/rate-limit-test/default')\r\n        .expect(429);\r\n    });\r\n\r\n    it('should handle race conditions properly', async () => {\r\n      // Make requests that would exceed limit if not handled properly\r\n      const requests = Array(15).fill(null).map(() =>\r\n        request(app.getHttpServer()).get('/rate-limit-test/strict')\r\n      );\r\n\r\n      const responses = await Promise.all(requests.map(req => \r\n        req.then(res => ({ status: res.status }))\r\n          .catch(err => ({ status: err.response?.status || 500 }))\r\n      ));\r\n\r\n      const successCount = responses.filter(r => r.status === 200).length;\r\n      const rateLimitedCount = responses.filter(r => r.status === 429).length;\r\n\r\n      expect(successCount).toBe(3); // Only 3 should succeed\r\n      expect(rateLimitedCount).toBe(12); // Rest should be rate limited\r\n    });\r\n  });\r\n\r\n  describe('Error Handling', () => {\r\n    it('should handle rate limit errors gracefully', async () => {\r\n      // Use up the limit\r\n      for (let i = 0; i < 10; i++) {\r\n        await request(app.getHttpServer())\r\n          .get('/rate-limit-test/default')\r\n          .expect(200);\r\n      }\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .get('/rate-limit-test/default')\r\n        .expect(429);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 429,\r\n        message: 'Too Many Requests',\r\n        error: 'Rate limit exceeded',\r\n        retryAfter: expect.any(Number),\r\n      });\r\n    });\r\n\r\n    it('should provide helpful error messages', async () => {\r\n      // Use up strict limit\r\n      for (let i = 0; i < 3; i++) {\r\n        await request(app.getHttpServer())\r\n          .get('/rate-limit-test/strict')\r\n          .expect(200);\r\n      }\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .get('/rate-limit-test/strict')\r\n        .expect(429);\r\n\r\n      expect(response.body.message).toBe('Too Many Requests');\r\n      expect(response.body.error).toBe('Rate limit exceeded');\r\n      expect(typeof response.body.retryAfter).toBe('number');\r\n    });\r\n  });\r\n\r\n  describe('Performance', () => {\r\n    it('should handle rate limiting efficiently', async () => {\r\n      const startTime = Date.now();\r\n      \r\n      // Make multiple requests\r\n      const requests = Array(10).fill(null).map(() =>\r\n        request(app.getHttpServer()).get('/rate-limit-test/default')\r\n      );\r\n\r\n      await Promise.all(requests);\r\n      \r\n      const endTime = Date.now();\r\n      const duration = endTime - startTime;\r\n\r\n      // Should complete quickly even with rate limiting\r\n      expect(duration).toBeLessThan(1000);\r\n    });\r\n\r\n    it('should not significantly impact response time', async () => {\r\n      const startTime = Date.now();\r\n      \r\n      await request(app.getHttpServer())\r\n        .get('/rate-limit-test/default')\r\n        .expect(200);\r\n      \r\n      const endTime = Date.now();\r\n      const duration = endTime - startTime;\r\n\r\n      // Rate limiting should add minimal overhead\r\n      expect(duration).toBeLessThan(100);\r\n    });\r\n  });\r\n});"], "version": 3}