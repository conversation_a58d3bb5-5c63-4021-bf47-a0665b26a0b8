c5a85dfc33e2bc8a5ba5f5154700317b
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const is_severity_level_validator_1 = require("../is-severity-level.validator");
class TestSeverityDto {
}
__decorate([
    (0, is_severity_level_validator_1.IsSeverityLevel)(),
    __metadata("design:type", Object)
], TestSeverityDto.prototype, "severity", void 0);
__decorate([
    (0, is_severity_level_validator_1.IsStringSeverityLevel)(),
    __metadata("design:type", String)
], TestSeverityDto.prototype, "stringSeverity", void 0);
__decorate([
    (0, is_severity_level_validator_1.IsNumericSeverityLevel)(),
    __metadata("design:type", Number)
], TestSeverityDto.prototype, "numericSeverity", void 0);
describe('Severity Level Validator', () => {
    let constraint;
    beforeEach(() => {
        constraint = new is_severity_level_validator_1.IsSeverityLevelConstraint();
    });
    describe('IsSeverityLevelConstraint', () => {
        describe('validate', () => {
            it('should validate string severity levels', () => {
                const validLevels = [
                    'critical',
                    'high',
                    'medium',
                    'low',
                    'informational',
                    'none',
                    'CRITICAL', // Case insensitive by default
                    'High',
                ];
                validLevels.forEach(level => {
                    expect(constraint.validate(level, { constraints: [{}] })).toBe(true);
                });
            });
            it('should validate numeric severity levels', () => {
                const validNumbers = [0, 1.5, 3.9, 4.0, 6.9, 7.0, 8.9, 9.0, 10.0];
                validNumbers.forEach(num => {
                    expect(constraint.validate(num, { constraints: [{}] })).toBe(true);
                });
            });
            it('should reject invalid severity levels', () => {
                const invalidLevels = [
                    'invalid',
                    'extreme',
                    -1,
                    10.1,
                    NaN,
                    Infinity,
                    null,
                    undefined,
                    {},
                    [],
                ];
                invalidLevels.forEach(level => {
                    expect(constraint.validate(level, { constraints: [{}] })).toBe(false);
                });
            });
            it('should respect allowNumeric option', () => {
                const options = { allowNumeric: false, allowString: true };
                expect(constraint.validate('high', { constraints: [options] })).toBe(true);
                expect(constraint.validate(7.5, { constraints: [options] })).toBe(false);
            });
            it('should respect allowString option', () => {
                const options = { allowNumeric: true, allowString: false };
                expect(constraint.validate(7.5, { constraints: [options] })).toBe(true);
                expect(constraint.validate('high', { constraints: [options] })).toBe(false);
            });
            it('should validate custom levels', () => {
                const options = { customLevels: ['urgent', 'normal', 'low'] };
                expect(constraint.validate('urgent', { constraints: [options] })).toBe(true);
                expect(constraint.validate('high', { constraints: [options] })).toBe(false);
            });
        });
    });
    describe('SeverityLevelUtils', () => {
        describe('numericToString', () => {
            it('should convert numeric severity to string correctly', () => {
                expect(is_severity_level_validator_1.SeverityLevelUtils.numericToString(0)).toBe(is_severity_level_validator_1.SeverityLevel.NONE);
                expect(is_severity_level_validator_1.SeverityLevelUtils.numericToString(2.5)).toBe(is_severity_level_validator_1.SeverityLevel.LOW);
                expect(is_severity_level_validator_1.SeverityLevelUtils.numericToString(5.0)).toBe(is_severity_level_validator_1.SeverityLevel.MEDIUM);
                expect(is_severity_level_validator_1.SeverityLevelUtils.numericToString(7.5)).toBe(is_severity_level_validator_1.SeverityLevel.HIGH);
                expect(is_severity_level_validator_1.SeverityLevelUtils.numericToString(9.5)).toBe(is_severity_level_validator_1.SeverityLevel.CRITICAL);
            });
        });
        describe('stringToNumericRange', () => {
            it('should convert string severity to numeric range', () => {
                expect(is_severity_level_validator_1.SeverityLevelUtils.stringToNumericRange('low')).toEqual([0.1, 3.9]);
                expect(is_severity_level_validator_1.SeverityLevelUtils.stringToNumericRange('medium')).toEqual([4.0, 6.9]);
                expect(is_severity_level_validator_1.SeverityLevelUtils.stringToNumericRange('high')).toEqual([7.0, 8.9]);
                expect(is_severity_level_validator_1.SeverityLevelUtils.stringToNumericRange('critical')).toEqual([9.0, 10.0]);
                expect(is_severity_level_validator_1.SeverityLevelUtils.stringToNumericRange('none')).toEqual([0, 0]);
            });
        });
        describe('getPriority', () => {
            it('should return correct priority for string levels', () => {
                expect(is_severity_level_validator_1.SeverityLevelUtils.getPriority('critical')).toBe(10);
                expect(is_severity_level_validator_1.SeverityLevelUtils.getPriority('high')).toBe(8);
                expect(is_severity_level_validator_1.SeverityLevelUtils.getPriority('medium')).toBe(5);
                expect(is_severity_level_validator_1.SeverityLevelUtils.getPriority('low')).toBe(2);
                expect(is_severity_level_validator_1.SeverityLevelUtils.getPriority('none')).toBe(0);
            });
            it('should return numeric value for numeric input', () => {
                expect(is_severity_level_validator_1.SeverityLevelUtils.getPriority(7.5)).toBe(7.5);
                expect(is_severity_level_validator_1.SeverityLevelUtils.getPriority(0)).toBe(0);
                expect(is_severity_level_validator_1.SeverityLevelUtils.getPriority(10)).toBe(10);
            });
        });
        describe('compare', () => {
            it('should compare severity levels correctly', () => {
                expect(is_severity_level_validator_1.SeverityLevelUtils.compare('low', 'high')).toBe(-1);
                expect(is_severity_level_validator_1.SeverityLevelUtils.compare('high', 'low')).toBe(1);
                expect(is_severity_level_validator_1.SeverityLevelUtils.compare('medium', 'medium')).toBe(0);
                expect(is_severity_level_validator_1.SeverityLevelUtils.compare(5.0, 7.0)).toBe(-1);
                expect(is_severity_level_validator_1.SeverityLevelUtils.compare('critical', 10.0)).toBe(0);
                expect(is_severity_level_validator_1.SeverityLevelUtils.compare(9.5, 9.5)).toBe(0);
            });
        });
        describe('sortByPriority', () => {
            it('should sort severity levels by priority (highest first)', () => {
                const severities = ['low', 'critical', 'medium', 'high'];
                const sorted = is_severity_level_validator_1.SeverityLevelUtils.sortByPriority(severities);
                expect(sorted).toEqual(['critical', 'high', 'medium', 'low']);
            });
            it('should sort mixed string and numeric severities', () => {
                const severities = ['low', 9.5, 'medium', 7.0];
                const sorted = is_severity_level_validator_1.SeverityLevelUtils.sortByPriority(severities);
                expect(sorted).toEqual([9.5, 7.0, 'medium', 'low']);
            });
        });
        describe('isValid', () => {
            it('should validate severity levels correctly', () => {
                expect(is_severity_level_validator_1.SeverityLevelUtils.isValid('high')).toBe(true);
                expect(is_severity_level_validator_1.SeverityLevelUtils.isValid(7.5)).toBe(true);
                expect(is_severity_level_validator_1.SeverityLevelUtils.isValid('invalid')).toBe(false);
                expect(is_severity_level_validator_1.SeverityLevelUtils.isValid(-1)).toBe(false);
            });
        });
        describe('normalize', () => {
            it('should normalize string severity levels', () => {
                expect(is_severity_level_validator_1.SeverityLevelUtils.normalize('HIGH')).toBe('high');
                expect(is_severity_level_validator_1.SeverityLevelUtils.normalize('Critical')).toBe('critical');
            });
            it('should return numeric values unchanged', () => {
                expect(is_severity_level_validator_1.SeverityLevelUtils.normalize(7.5)).toBe(7.5);
                expect(is_severity_level_validator_1.SeverityLevelUtils.normalize(0)).toBe(0);
            });
            it('should return null for invalid values', () => {
                expect(is_severity_level_validator_1.SeverityLevelUtils.normalize('invalid')).toBeNull();
                expect(is_severity_level_validator_1.SeverityLevelUtils.normalize(-1)).toBeNull();
            });
        });
        describe('getColorCode', () => {
            it('should return appropriate color codes', () => {
                expect(is_severity_level_validator_1.SeverityLevelUtils.getColorCode('critical')).toBe('#dc3545');
                expect(is_severity_level_validator_1.SeverityLevelUtils.getColorCode('high')).toBe('#fd7e14');
                expect(is_severity_level_validator_1.SeverityLevelUtils.getColorCode('medium')).toBe('#ffc107');
                expect(is_severity_level_validator_1.SeverityLevelUtils.getColorCode('low')).toBe('#28a745');
                expect(is_severity_level_validator_1.SeverityLevelUtils.getColorCode('none')).toBe('#6c757d');
            });
        });
        describe('getDescription', () => {
            it('should return appropriate descriptions', () => {
                const criticalDesc = is_severity_level_validator_1.SeverityLevelUtils.getDescription('critical');
                const lowDesc = is_severity_level_validator_1.SeverityLevelUtils.getDescription('low');
                expect(criticalDesc).toContain('immediate attention');
                expect(lowDesc).toContain('time permits');
            });
        });
    });
    describe('DTO Validation', () => {
        it('should validate DTO with valid severity levels', async () => {
            const dto = (0, class_transformer_1.plainToClass)(TestSeverityDto, {
                severity: 'high',
                stringSeverity: 'critical',
                numericSeverity: 7.5,
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
        });
        it('should reject DTO with invalid severity levels', async () => {
            const dto = (0, class_transformer_1.plainToClass)(TestSeverityDto, {
                severity: 'invalid',
                stringSeverity: 123, // Should be string
                numericSeverity: 'high', // Should be number
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors.length).toBeGreaterThan(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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