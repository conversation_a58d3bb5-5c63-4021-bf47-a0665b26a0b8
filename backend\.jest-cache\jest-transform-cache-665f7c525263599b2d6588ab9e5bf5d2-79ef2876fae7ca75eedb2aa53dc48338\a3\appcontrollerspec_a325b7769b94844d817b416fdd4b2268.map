{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\app.controller.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,qDAAiD;AACjD,+CAA2C;AAE3C,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,IAAI,aAA4B,CAAC;IAEjC,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,GAAG,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YACxD,WAAW,EAAE,CAAC,8BAAa,CAAC;YAC5B,SAAS,EAAE,CAAC,wBAAU,CAAC;SACxB,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,aAAa,GAAG,GAAG,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE;QACpB,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\app.controller.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { AppController } from './app.controller';\r\nimport { AppService } from './app.service';\r\n\r\ndescribe('AppController', () => {\r\n  let appController: AppController;\r\n\r\n  beforeEach(async () => {\r\n    const app: TestingModule = await Test.createTestingModule({\r\n      controllers: [AppController],\r\n      providers: [AppService],\r\n    }).compile();\r\n\r\n    appController = app.get<AppController>(AppController);\r\n  });\r\n\r\n  describe('root', () => {\r\n    it('should return \"Hello World!\"', () => {\r\n      expect(appController.getHello()).toBe('Hello World!');\r\n    });\r\n  });\r\n});\r\n"], "version": 3}