1dab13698a225bf611f8fcb2a6aefb1d
"use strict";
/**
 * Sort Types Tests
 */
Object.defineProperty(exports, "__esModule", { value: true });
const node_test_1 = require("node:test");
const node_test_2 = require("node:test");
const sort_types_1 = require("../../types/sort.types");
(0, node_test_2.describe)('SortUtils', () => {
    (0, node_test_2.describe)('validateSort', () => {
        (0, node_test_1.it)('should validate sort parameters successfully', () => {
            const sortParams = {
                sort: [
                    { field: 'name', direction: sort_types_1.SortDirection.ASC },
                    { field: 'date', direction: sort_types_1.SortDirection.DESC },
                ],
            };
            const config = {
                maxSortFields: 5,
                sortableFields: ['name', 'date'],
                defaultDirection: sort_types_1.SortDirection.ASC,
                allowCaseSensitive: true,
                allowCustomSort: false,
            };
            const result = sort_types_1.SortUtils.validateSort(sortParams, config);
            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });
        (0, node_test_1.it)('should fail validation when exceeding max sort fields', () => {
            const sortParams = {
                sort: Array.from({ length: 6 }, (_, i) => ({
                    field: `field${i}`,
                    direction: sort_types_1.SortDirection.ASC,
                })),
            };
            const config = {
                maxSortFields: 5,
                sortableFields: [],
                defaultDirection: sort_types_1.SortDirection.ASC,
                allowCaseSensitive: true,
                allowCustomSort: false,
            };
            const result = sort_types_1.SortUtils.validateSort(sortParams, config);
            expect(result.isValid).toBe(false);
            expect(result.errors).toContain('Number of sort fields (6) exceeds maximum allowed (5)');
        });
        (0, node_test_1.it)('should fail validation for non-sortable fields', () => {
            const sortParams = {
                sort: [{ field: 'restricted', direction: sort_types_1.SortDirection.ASC }],
            };
            const config = {
                maxSortFields: 5,
                sortableFields: ['name', 'date'],
                defaultDirection: sort_types_1.SortDirection.ASC,
                allowCaseSensitive: true,
                allowCustomSort: false,
            };
            const result = sort_types_1.SortUtils.validateSort(sortParams, config);
            expect(result.isValid).toBe(false);
            expect(result.errors).toContain("Field 'restricted' is not sortable");
        });
        (0, node_test_1.it)('should detect duplicate sort fields', () => {
            const sortParams = {
                sort: [
                    { field: 'name', direction: sort_types_1.SortDirection.ASC },
                    { field: 'name', direction: sort_types_1.SortDirection.DESC },
                ],
            };
            const result = sort_types_1.SortUtils.validateSort(sortParams);
            expect(result.isValid).toBe(false);
            expect(result.errors).toContain("Duplicate sort field 'name' found");
        });
    });
    (0, node_test_2.describe)('fromQuery', () => {
        (0, node_test_1.it)('should parse sort from query string', () => {
            const query = { sort: 'name,-date,+age' };
            const result = sort_types_1.SortUtils.fromQuery(query);
            expect(result.sort).toHaveLength(3);
            expect(result.sort[0]).toEqual({
                field: 'name',
                direction: sort_types_1.SortDirection.ASC,
                priority: 0,
            });
            expect(result.sort[1]).toEqual({
                field: 'date',
                direction: sort_types_1.SortDirection.DESC,
                priority: 1,
            });
            expect(result.sort[2]).toEqual({
                field: 'age',
                direction: sort_types_1.SortDirection.ASC,
                priority: 2,
            });
        });
        (0, node_test_1.it)('should handle array sort parameter', () => {
            const query = { sort: ['name', '-date'] };
            const result = sort_types_1.SortUtils.fromQuery(query);
            expect(result.sort).toHaveLength(2);
            expect(result.sort[0].field).toBe('name');
            expect(result.sort[1].field).toBe('date');
            expect(result.sort[1].direction).toBe(sort_types_1.SortDirection.DESC);
        });
    });
    (0, node_test_2.describe)('toQueryString', () => {
        (0, node_test_1.it)('should convert sort parameters to query string', () => {
            const sortParams = {
                sort: [
                    { field: 'name', direction: sort_types_1.SortDirection.ASC },
                    { field: 'date', direction: sort_types_1.SortDirection.DESC },
                ],
            };
            const result = sort_types_1.SortUtils.toQueryString(sortParams);
            expect(result).toBe('sort=name%2C-date');
        });
        (0, node_test_1.it)('should return empty string for empty sort', () => {
            const sortParams = { sort: [] };
            const result = sort_types_1.SortUtils.toQueryString(sortParams);
            expect(result).toBe('');
        });
    });
    (0, node_test_2.describe)('applySort', () => {
        const testData = [
            { name: 'Charlie', age: 30, date: new Date('2023-01-01') },
            { name: 'Alice', age: 25, date: new Date('2023-02-01') },
            { name: 'Bob', age: 35, date: new Date('2022-12-01') },
        ];
        (0, node_test_1.it)('should sort by string field ascending', () => {
            const sortParams = {
                sort: [{ field: 'name', direction: sort_types_1.SortDirection.ASC }],
            };
            const result = sort_types_1.SortUtils.applySort(testData, sortParams);
            expect(result[0].name).toBe('Alice');
            expect(result[1].name).toBe('Bob');
            expect(result[2].name).toBe('Charlie');
        });
        (0, node_test_1.it)('should sort by number field descending', () => {
            const sortParams = {
                sort: [{ field: 'age', direction: sort_types_1.SortDirection.DESC }],
            };
            const result = sort_types_1.SortUtils.applySort(testData, sortParams);
            expect(result[0].age).toBe(35);
            expect(result[1].age).toBe(30);
            expect(result[2].age).toBe(25);
        });
        (0, node_test_1.it)('should sort by date field', () => {
            const sortParams = {
                sort: [{ field: 'date', direction: sort_types_1.SortDirection.ASC }],
            };
            const result = sort_types_1.SortUtils.applySort(testData, sortParams);
            expect(result[0].date.getTime()).toBe(new Date('2022-12-01').getTime());
            expect(result[1].date.getTime()).toBe(new Date('2023-01-01').getTime());
            expect(result[2].date.getTime()).toBe(new Date('2023-02-01').getTime());
        });
        (0, node_test_1.it)('should handle multiple sort fields', () => {
            const data = [
                { name: 'Alice', age: 30 },
                { name: 'Bob', age: 25 },
                { name: 'Alice', age: 25 },
            ];
            const sortParams = {
                sort: [
                    { field: 'name', direction: sort_types_1.SortDirection.ASC },
                    { field: 'age', direction: sort_types_1.SortDirection.DESC },
                ],
            };
            const result = sort_types_1.SortUtils.applySort(data, sortParams);
            expect(result[0]).toEqual({ name: 'Alice', age: 30 });
            expect(result[1]).toEqual({ name: 'Alice', age: 25 });
            expect(result[2]).toEqual({ name: 'Bob', age: 25 });
        });
        (0, node_test_1.it)('should handle custom sort function', () => {
            const sortParams = {
                sort: [{
                        field: 'name',
                        direction: sort_types_1.SortDirection.ASC,
                        customSort: (a, b) => a.name.length - b.name.length,
                    }],
            };
            const result = sort_types_1.SortUtils.applySort(testData, sortParams);
            expect(result[0].name).toBe('Bob'); // 3 characters
            expect(result[1].name).toBe('Alice'); // 5 characters
            expect(result[2].name).toBe('Charlie'); // 7 characters
        });
    });
    (0, node_test_2.describe)('helper methods', () => {
        (0, node_test_1.it)('should create ascending sort field', () => {
            const field = sort_types_1.SortUtils.asc('name', 1);
            expect(field.field).toBe('name');
            expect(field.direction).toBe(sort_types_1.SortDirection.ASC);
            expect(field.priority).toBe(1);
        });
        (0, node_test_1.it)('should create descending sort field', () => {
            const field = sort_types_1.SortUtils.desc('date', 2);
            expect(field.field).toBe('date');
            expect(field.direction).toBe(sort_types_1.SortDirection.DESC);
            expect(field.priority).toBe(2);
        });
        (0, node_test_1.it)('should create sort parameters from fields', () => {
            const sortParams = sort_types_1.SortUtils.create(sort_types_1.SortUtils.asc('name', 1), sort_types_1.SortUtils.desc('date', 0));
            expect(sortParams.sort).toHaveLength(2);
            expect(sortParams.sort[0].field).toBe('date'); // Priority 0 comes first
            expect(sortParams.sort[1].field).toBe('name'); // Priority 1 comes second
        });
        (0, node_test_1.it)('should reverse sort direction', () => {
            expect(sort_types_1.SortUtils.reverseDirection(sort_types_1.SortDirection.ASC)).toBe(sort_types_1.SortDirection.DESC);
            expect(sort_types_1.SortUtils.reverseDirection(sort_types_1.SortDirection.DESC)).toBe(sort_types_1.SortDirection.ASC);
        });
        (0, node_test_1.it)('should check if sort parameters are empty', () => {
            expect(sort_types_1.SortUtils.isEmpty({ sort: [] })).toBe(true);
            expect(sort_types_1.SortUtils.isEmpty({ sort: [sort_types_1.SortUtils.asc('name')] })).toBe(false);
        });
        (0, node_test_1.it)('should get referenced fields', () => {
            const sortParams = {
                sort: [
                    sort_types_1.SortUtils.asc('name'),
                    sort_types_1.SortUtils.desc('date'),
                ],
            };
            const fields = sort_types_1.SortUtils.getReferencedFields(sortParams);
            expect(fields).toEqual(['name', 'date']);
        });
    });
    (0, node_test_2.describe)('SQL and MongoDB conversion', () => {
        (0, node_test_1.it)('should convert to SQL ORDER BY clause', () => {
            const sortParams = {
                sort: [
                    { field: 'name', direction: sort_types_1.SortDirection.ASC },
                    { field: 'date', direction: sort_types_1.SortDirection.DESC },
                ],
            };
            const result = sort_types_1.SortUtils.toSqlOrderBy(sortParams, 'users');
            expect(result).toBe('ORDER BY users.name ASC, users.date DESC');
        });
        (0, node_test_1.it)('should convert to MongoDB sort object', () => {
            const sortParams = {
                sort: [
                    { field: 'name', direction: sort_types_1.SortDirection.ASC },
                    { field: 'date', direction: sort_types_1.SortDirection.DESC },
                ],
            };
            const result = sort_types_1.SortUtils.toMongoSort(sortParams);
            expect(result).toEqual({
                name: 1,
                date: -1,
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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