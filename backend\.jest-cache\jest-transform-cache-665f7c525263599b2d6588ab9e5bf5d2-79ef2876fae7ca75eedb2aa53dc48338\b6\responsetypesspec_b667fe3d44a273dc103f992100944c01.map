{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\types\\response.types.spec.ts", "mappings": ";AAAA;;GAEG;;AAEH,+DAOoC;AAEpC,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YACrC,MAAM,QAAQ,GAAG,8BAAa,CAAC,OAAO,CAAC,IAAI,EAAE,sBAAsB,CAAC,CAAC;YAErE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,2BAAU,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;YACvB,MAAM,QAAQ,GAAG,8BAAa,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,2BAAU,CAAC,OAAO,CAAC,CAAC;YAE5E,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,2BAAU,CAAC,OAAO,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,8BAAa,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,2BAAU,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YAE7E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE;QACrB,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,KAAK,GAAgB;gBACzB,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,qBAAqB;aAC/B,CAAC;YAEF,MAAM,QAAQ,GAAG,8BAAa,CAAC,KAAK,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;YAEhE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,2BAAU,CAAC,qBAAqB,CAAC,CAAC;YACnE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,YAAY,GAAgB;gBAChC,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,eAAe;aACzB,CAAC;YAEF,MAAM,gBAAgB,GAAkB;gBACtC,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,iBAAiB,EAAE;aACxD,CAAC;YAEF,MAAM,QAAQ,GAAG,8BAAa,CAAC,KAAK,CAClC,YAAY,EACZ,iBAAiB,EACjB,2BAAU,CAAC,WAAW,EACtB,gBAAgB,CACjB,CAAC;YAEF,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,gBAAgB,GAAsB;gBAC1C;oBACE,IAAI,EAAE,gBAAgB;oBACtB,OAAO,EAAE,kBAAkB;oBAC3B,KAAK,EAAE,MAAM;iBACd;gBACD;oBACE,IAAI,EAAE,eAAe;oBACrB,OAAO,EAAE,sBAAsB;oBAC/B,KAAK,EAAE,OAAO;oBACd,KAAK,EAAE,eAAe;iBACvB;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,8BAAa,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YAEjE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,2BAAU,CAAC,oBAAoB,CAAC,CAAC;YAClE,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,QAAQ,GAAG,8BAAa,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YAErD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,2BAAU,CAAC,SAAS,CAAC,CAAC;YACvD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YAC5E,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,QAAQ,GAAG,8BAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEhD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,QAAQ,GAAG,8BAAa,CAAC,YAAY,EAAE,CAAC;YAE9C,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,2BAAU,CAAC,YAAY,CAAC,CAAC;YAC1D,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,QAAQ,GAAG,8BAAa,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;YAE7D,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,QAAQ,GAAG,8BAAa,CAAC,SAAS,EAAE,CAAC;YAE3C,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,2BAAU,CAAC,SAAS,CAAC,CAAC;YACvD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,OAAO,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;YACpC,MAAM,QAAQ,GAAG,8BAAa,CAAC,QAAQ,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;YAE5E,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,2BAAU,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,QAAQ,GAAG,8BAAa,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAE/C,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,2BAAU,CAAC,iBAAiB,CAAC,CAAC;YAC/D,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,KAAK,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACrC,MAAM,UAAU,GAAmB;gBACjC,WAAW,EAAE,CAAC;gBACd,YAAY,EAAE,EAAE;gBAChB,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,CAAC;gBACb,WAAW,EAAE,IAAI;gBACjB,eAAe,EAAE,KAAK;gBACtB,QAAQ,EAAE,CAAC;gBACX,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,CAAC;gBACjB,aAAa,EAAE,EAAE;aAClB,CAAC;YAEF,MAAM,QAAQ,GAAG,8BAAa,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;YAE1E,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAChE,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC/D,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,SAAkB,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;gBAC/D,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,OAAgB,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE,OAAO,EAAE,cAAc,EAAE,EAAE;gBACjG,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,SAAkB,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;aAChE,CAAC;YAEF,MAAM,QAAQ,GAAG,8BAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAEtD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC;YAC/D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,OAAO,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,OAAO,GAAG;gBACd,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,SAAkB,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;gBAC/D,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,SAAkB,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;aAChE,CAAC;YAEF,MAAM,QAAQ,GAAG,8BAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAEtD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,2BAAU,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,MAAM,GAAG;gBACb,QAAQ,EAAE;oBACR,MAAM,EAAE,SAAkB;oBAC1B,YAAY,EAAE,EAAE;iBACjB;gBACD,KAAK,EAAE;oBACL,MAAM,EAAE,SAAkB;oBAC1B,YAAY,EAAE,EAAE;iBACjB;aACF,CAAC;YAEF,MAAM,UAAU,GAAG;gBACjB,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;gBAClC,GAAG,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;aACnB,CAAC;YAEF,MAAM,QAAQ,GAAG,8BAAa,CAAC,WAAW,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAE/D,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,2BAAU,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,MAAM,GAAG;gBACb,QAAQ,EAAE;oBACR,MAAM,EAAE,WAAoB;oBAC5B,OAAO,EAAE,mBAAmB;iBAC7B;aACF,CAAC;YAEF,MAAM,UAAU,GAAG;gBACjB,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;gBAClC,GAAG,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;aACnB,CAAC;YAEF,MAAM,QAAQ,GAAG,8BAAa,CAAC,WAAW,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAE/D,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,2BAAU,CAAC,mBAAmB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,QAAQ,GAAG;gBACf,YAAY,EAAE,cAAc;gBAC5B,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,iBAAiB;gBAC3B,GAAG,EAAE,qBAAqB;gBAC1B,IAAI,EAAE,YAAY;aACnB,CAAC;YAEF,MAAM,QAAQ,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;YAE3C,MAAM,QAAQ,GAAG,8BAAa,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAE9D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAc,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,2BAAU,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,eAAe,GAAG,8BAAa,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACzD,MAAM,aAAa,GAAG,8BAAa,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YAE/E,MAAM,CAAC,8BAAa,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,8BAAa,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,eAAe,GAAG,8BAAa,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACzD,MAAM,aAAa,GAAG,8BAAa,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YAE/E,MAAM,CAAC,8BAAa,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,8BAAa,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,aAAa,GAAG,8BAAa,CAAC,KAAK,CACvC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,EAC3C,kBAAkB,CACnB,CAAC;YAEF,MAAM,OAAO,GAAG,8BAAa,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAE7D,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,aAAa,GAAG,8BAAa,CAAC,eAAe,CAAC;gBAClD,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE;gBAC7D,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE;aAC9D,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,8BAAa,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;YAElE,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;YACrE,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC5C,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,QAAQ,GAAG,8BAAa,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YAClD,MAAM,aAAa,GAAG,8BAAa,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YAEvE,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,QAAQ,GAAG,8BAAa,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YAClD,MAAM,WAAW,GAAG,8BAAa,CAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAEhE,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\types\\response.types.spec.ts"], "sourcesContent": ["/**\r\n * Response Types Tests\r\n */\r\n\r\nimport {\r\n  HttpStatus,\r\n  ResponseStatus,\r\n  ResponseUtils,\r\n  ErrorDetail,\r\n  ValidationError,\r\n  PaginationMeta,\r\n} from '../../types/response.types';\r\n\r\ndescribe('ResponseUtils', () => {\r\n  describe('success', () => {\r\n    it('should create a success response', () => {\r\n      const data = { id: 1, name: 'Test' };\r\n      const response = ResponseUtils.success(data, 'Operation successful');\r\n\r\n      expect(response.status).toBe(ResponseStatus.SUCCESS);\r\n      expect(response.statusCode).toBe(HttpStatus.OK);\r\n      expect(response.message).toBe('Operation successful');\r\n      expect(response.data).toEqual(data);\r\n      expect(response.timestamp).toBeDefined();\r\n    });\r\n\r\n    it('should create a success response with custom status code', () => {\r\n      const data = { id: 1 };\r\n      const response = ResponseUtils.success(data, 'Created', HttpStatus.CREATED);\r\n\r\n      expect(response.statusCode).toBe(HttpStatus.CREATED);\r\n    });\r\n\r\n    it('should create a success response with metadata', () => {\r\n      const data = { id: 1 };\r\n      const meta = { version: '1.0' };\r\n      const response = ResponseUtils.success(data, 'Success', HttpStatus.OK, meta);\r\n\r\n      expect(response.meta).toEqual(meta);\r\n    });\r\n  });\r\n\r\n  describe('error', () => {\r\n    it('should create an error response', () => {\r\n      const error: ErrorDetail = {\r\n        code: 'TEST_ERROR',\r\n        message: 'Test error occurred',\r\n      };\r\n\r\n      const response = ResponseUtils.error(error, 'Operation failed');\r\n\r\n      expect(response.status).toBe(ResponseStatus.ERROR);\r\n      expect(response.statusCode).toBe(HttpStatus.INTERNAL_SERVER_ERROR);\r\n      expect(response.message).toBe('Operation failed');\r\n      expect(response.error).toEqual(error);\r\n      expect(response.timestamp).toBeDefined();\r\n    });\r\n\r\n    it('should create an error response with additional errors', () => {\r\n      const primaryError: ErrorDetail = {\r\n        code: 'PRIMARY_ERROR',\r\n        message: 'Primary error',\r\n      };\r\n\r\n      const additionalErrors: ErrorDetail[] = [\r\n        { code: 'SECONDARY_ERROR', message: 'Secondary error' },\r\n      ];\r\n\r\n      const response = ResponseUtils.error(\r\n        primaryError,\r\n        'Multiple errors',\r\n        HttpStatus.BAD_REQUEST,\r\n        additionalErrors\r\n      );\r\n\r\n      expect(response.errors).toEqual(additionalErrors);\r\n    });\r\n  });\r\n\r\n  describe('validationError', () => {\r\n    it('should create a validation error response', () => {\r\n      const validationErrors: ValidationError[] = [\r\n        {\r\n          code: 'REQUIRED_FIELD',\r\n          message: 'Name is required',\r\n          field: 'name',\r\n        },\r\n        {\r\n          code: 'INVALID_EMAIL',\r\n          message: 'Invalid email format',\r\n          field: 'email',\r\n          value: 'invalid-email',\r\n        },\r\n      ];\r\n\r\n      const response = ResponseUtils.validationError(validationErrors);\r\n\r\n      expect(response.status).toBe(ResponseStatus.ERROR);\r\n      expect(response.statusCode).toBe(HttpStatus.UNPROCESSABLE_ENTITY);\r\n      expect(response.error.code).toBe('VALIDATION_ERROR');\r\n      expect(response.errors).toEqual(validationErrors);\r\n    });\r\n  });\r\n\r\n  describe('notFound', () => {\r\n    it('should create a not found response', () => {\r\n      const response = ResponseUtils.notFound('User', 123);\r\n\r\n      expect(response.status).toBe(ResponseStatus.ERROR);\r\n      expect(response.statusCode).toBe(HttpStatus.NOT_FOUND);\r\n      expect(response.error.code).toBe('NOT_FOUND');\r\n      expect(response.error.message).toBe(\"User with identifier '123' not found\");\r\n      expect(response.error.context).toEqual({ resource: 'User', identifier: 123 });\r\n    });\r\n\r\n    it('should create a not found response without identifier', () => {\r\n      const response = ResponseUtils.notFound('User');\r\n\r\n      expect(response.error.message).toBe('User not found');\r\n    });\r\n  });\r\n\r\n  describe('unauthorized', () => {\r\n    it('should create an unauthorized response', () => {\r\n      const response = ResponseUtils.unauthorized();\r\n\r\n      expect(response.status).toBe(ResponseStatus.ERROR);\r\n      expect(response.statusCode).toBe(HttpStatus.UNAUTHORIZED);\r\n      expect(response.error.code).toBe('UNAUTHORIZED');\r\n      expect(response.error.message).toBe('Authentication required');\r\n    });\r\n\r\n    it('should create an unauthorized response with custom message', () => {\r\n      const response = ResponseUtils.unauthorized('Invalid token');\r\n\r\n      expect(response.error.message).toBe('Invalid token');\r\n    });\r\n  });\r\n\r\n  describe('forbidden', () => {\r\n    it('should create a forbidden response', () => {\r\n      const response = ResponseUtils.forbidden();\r\n\r\n      expect(response.status).toBe(ResponseStatus.ERROR);\r\n      expect(response.statusCode).toBe(HttpStatus.FORBIDDEN);\r\n      expect(response.error.code).toBe('FORBIDDEN');\r\n      expect(response.error.message).toBe('Access denied');\r\n    });\r\n  });\r\n\r\n  describe('conflict', () => {\r\n    it('should create a conflict response', () => {\r\n      const context = { existingId: 123 };\r\n      const response = ResponseUtils.conflict('Resource already exists', context);\r\n\r\n      expect(response.status).toBe(ResponseStatus.ERROR);\r\n      expect(response.statusCode).toBe(HttpStatus.CONFLICT);\r\n      expect(response.error.code).toBe('CONFLICT');\r\n      expect(response.error.context).toEqual(context);\r\n    });\r\n  });\r\n\r\n  describe('rateLimited', () => {\r\n    it('should create a rate limited response', () => {\r\n      const response = ResponseUtils.rateLimited(60);\r\n\r\n      expect(response.status).toBe(ResponseStatus.ERROR);\r\n      expect(response.statusCode).toBe(HttpStatus.TOO_MANY_REQUESTS);\r\n      expect(response.error.code).toBe('RATE_LIMITED');\r\n      expect(response.error.context).toEqual({ retryAfter: 60 });\r\n    });\r\n  });\r\n\r\n  describe('paginated', () => {\r\n    it('should create a paginated response', () => {\r\n      const items = [{ id: 1 }, { id: 2 }];\r\n      const pagination: PaginationMeta = {\r\n        currentPage: 1,\r\n        itemsPerPage: 10,\r\n        totalItems: 25,\r\n        totalPages: 3,\r\n        hasNextPage: true,\r\n        hasPreviousPage: false,\r\n        nextPage: 2,\r\n        previousPage: null,\r\n        firstItemIndex: 1,\r\n        lastItemIndex: 10,\r\n      };\r\n\r\n      const response = ResponseUtils.paginated(items, pagination, '/api/users');\r\n\r\n      expect(response.status).toBe(ResponseStatus.SUCCESS);\r\n      expect(response.data).toEqual(items);\r\n      expect(response.pagination).toEqual(pagination);\r\n      expect(response.links.first).toBe('/api/users?page=1&limit=10');\r\n      expect(response.links.next).toBe('/api/users?page=2&limit=10');\r\n      expect(response.links.self).toBe('/api/users?page=1&limit=10');\r\n    });\r\n  });\r\n\r\n  describe('bulkOperation', () => {\r\n    it('should create a bulk operation response', () => {\r\n      const results = [\r\n        { id: 1, status: 'success' as const, data: { name: 'User 1' } },\r\n        { id: 2, status: 'error' as const, error: { code: 'VALIDATION_ERROR', message: 'Invalid data' } },\r\n        { id: 3, status: 'success' as const, data: { name: 'User 3' } },\r\n      ];\r\n\r\n      const response = ResponseUtils.bulkOperation(results);\r\n\r\n      expect(response.total).toBe(3);\r\n      expect(response.successful).toBe(2);\r\n      expect(response.failed).toBe(1);\r\n      expect(response.results).toEqual(results);\r\n      expect(response.errorSummary).toEqual({ VALIDATION_ERROR: 1 });\r\n      expect(response.status).toBe(ResponseStatus.WARNING);\r\n    });\r\n\r\n    it('should create a successful bulk operation response', () => {\r\n      const results = [\r\n        { id: 1, status: 'success' as const, data: { name: 'User 1' } },\r\n        { id: 2, status: 'success' as const, data: { name: 'User 2' } },\r\n      ];\r\n\r\n      const response = ResponseUtils.bulkOperation(results);\r\n\r\n      expect(response.status).toBe(ResponseStatus.SUCCESS);\r\n      expect(response.statusCode).toBe(HttpStatus.OK);\r\n      expect(response.failed).toBe(0);\r\n    });\r\n  });\r\n\r\n  describe('healthCheck', () => {\r\n    it('should create a healthy system response', () => {\r\n      const checks = {\r\n        database: {\r\n          status: 'healthy' as const,\r\n          responseTime: 50,\r\n        },\r\n        redis: {\r\n          status: 'healthy' as const,\r\n          responseTime: 10,\r\n        },\r\n      };\r\n\r\n      const systemInfo = {\r\n        uptime: 3600,\r\n        memory: { used: 512, total: 1024 },\r\n        cpu: { usage: 25 },\r\n      };\r\n\r\n      const response = ResponseUtils.healthCheck(checks, systemInfo);\r\n\r\n      expect(response.healthy).toBe(true);\r\n      expect(response.status).toBe(ResponseStatus.SUCCESS);\r\n      expect(response.statusCode).toBe(HttpStatus.OK);\r\n      expect(response.checks.database.lastChecked).toBeDefined();\r\n      expect(response.system.memory.percentage).toBe(50);\r\n    });\r\n\r\n    it('should create an unhealthy system response', () => {\r\n      const checks = {\r\n        database: {\r\n          status: 'unhealthy' as const,\r\n          message: 'Connection failed',\r\n        },\r\n      };\r\n\r\n      const systemInfo = {\r\n        uptime: 3600,\r\n        memory: { used: 512, total: 1024 },\r\n        cpu: { usage: 25 },\r\n      };\r\n\r\n      const response = ResponseUtils.healthCheck(checks, systemInfo);\r\n\r\n      expect(response.healthy).toBe(false);\r\n      expect(response.status).toBe(ResponseStatus.ERROR);\r\n      expect(response.statusCode).toBe(HttpStatus.SERVICE_UNAVAILABLE);\r\n    });\r\n  });\r\n\r\n  describe('fileUpload', () => {\r\n    it('should create a file upload response', () => {\r\n      const fileInfo = {\r\n        originalName: 'document.pdf',\r\n        filename: 'abc123.pdf',\r\n        size: 1024,\r\n        mimeType: 'application/pdf',\r\n        url: '/uploads/abc123.pdf',\r\n        hash: 'sha256hash',\r\n      };\r\n\r\n      const metadata = { uploadedBy: 'user123' };\r\n\r\n      const response = ResponseUtils.fileUpload(fileInfo, metadata);\r\n\r\n      expect(response.status).toBe(ResponseStatus.SUCCESS);\r\n      expect(response.statusCode).toBe(HttpStatus.CREATED);\r\n      expect(response.data.file).toEqual(fileInfo);\r\n      expect(response.data.metadata).toEqual(metadata);\r\n    });\r\n  });\r\n\r\n  describe('utility methods', () => {\r\n    it('should check if response is successful', () => {\r\n      const successResponse = ResponseUtils.success({ id: 1 });\r\n      const errorResponse = ResponseUtils.error({ code: 'ERROR', message: 'Error' });\r\n\r\n      expect(ResponseUtils.isSuccess(successResponse)).toBe(true);\r\n      expect(ResponseUtils.isSuccess(errorResponse)).toBe(false);\r\n    });\r\n\r\n    it('should check if response is an error', () => {\r\n      const successResponse = ResponseUtils.success({ id: 1 });\r\n      const errorResponse = ResponseUtils.error({ code: 'ERROR', message: 'Error' });\r\n\r\n      expect(ResponseUtils.isError(successResponse)).toBe(false);\r\n      expect(ResponseUtils.isError(errorResponse)).toBe(true);\r\n    });\r\n\r\n    it('should extract error message', () => {\r\n      const errorResponse = ResponseUtils.error(\r\n        { code: 'ERROR', message: 'Primary error' },\r\n        'Operation failed'\r\n      );\r\n\r\n      const message = ResponseUtils.getErrorMessage(errorResponse);\r\n\r\n      expect(message).toBe('Primary error');\r\n    });\r\n\r\n    it('should extract all error messages', () => {\r\n      const errorResponse = ResponseUtils.validationError([\r\n        { code: 'REQUIRED', message: 'Name required', field: 'name' },\r\n        { code: 'INVALID', message: 'Email invalid', field: 'email' },\r\n      ]);\r\n\r\n      const messages = ResponseUtils.getAllErrorMessages(errorResponse);\r\n\r\n      expect(messages).toContain('One or more validation errors occurred');\r\n      expect(messages).toContain('Name required');\r\n      expect(messages).toContain('Email invalid');\r\n    });\r\n\r\n    it('should add request ID to response', () => {\r\n      const response = ResponseUtils.success({ id: 1 });\r\n      const withRequestId = ResponseUtils.withRequestId(response, 'req-123');\r\n\r\n      expect(withRequestId.requestId).toBe('req-123');\r\n    });\r\n\r\n    it('should add version to response', () => {\r\n      const response = ResponseUtils.success({ id: 1 });\r\n      const withVersion = ResponseUtils.withVersion(response, 'v1.0');\r\n\r\n      expect(withVersion.version).toBe('v1.0');\r\n    });\r\n  });\r\n});"], "version": 3}