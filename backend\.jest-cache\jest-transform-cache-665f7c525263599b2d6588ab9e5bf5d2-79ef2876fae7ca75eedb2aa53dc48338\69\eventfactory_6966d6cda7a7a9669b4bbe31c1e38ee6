b20794568b3851b4fa5f54c1463fd939
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventFactory = void 0;
const event_entity_1 = require("../entities/event.entity");
const event_metadata_value_object_1 = require("../value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../value-objects/event-metadata/event-source.value-object");
const event_type_enum_1 = require("../enums/event-type.enum");
const event_severity_enum_1 = require("../enums/event-severity.enum");
const event_status_enum_1 = require("../enums/event-status.enum");
const event_processing_status_enum_1 = require("../enums/event-processing-status.enum");
const event_source_type_enum_1 = require("../enums/event-source-type.enum");
/**
 * Event Factory
 *
 * Factory class for creating Event entities with proper validation and defaults.
 * Handles complex event creation scenarios and ensures all business rules are applied.
 *
 * Key responsibilities:
 * - Create events from various input formats
 * - Apply default values and business rules
 * - Validate event data consistency
 * - Generate proper metadata and timestamps
 * - Handle event relationships and correlations
 */
class EventFactory {
    /**
     * Create a new Event with the provided options
     */
    static create(options) {
        // Create event source
        const eventSource = event_source_value_object_1.EventSource.create(options.sourceType, options.sourceIdentifier, options.sourceMetadata);
        // Create event timestamp
        const eventTimestamp = options.timestamp
            ? event_timestamp_value_object_1.EventTimestamp.fromDate(options.timestamp)
            : event_timestamp_value_object_1.EventTimestamp.create();
        // Create event metadata
        const eventMetadata = event_metadata_value_object_1.EventMetadata.create(eventTimestamp, eventSource, {
            processingHints: options.processingHints,
        });
        // Build event properties
        const eventProps = {
            metadata: eventMetadata,
            type: options.type,
            severity: options.severity,
            status: options.status || event_status_enum_1.EventStatus.ACTIVE,
            processingStatus: options.processingStatus || event_processing_status_enum_1.EventProcessingStatus.RAW,
            rawData: options.rawData,
            title: options.title,
            description: options.description,
            tags: options.tags,
            riskScore: options.riskScore,
            confidenceLevel: options.confidenceLevel,
            attributes: options.attributes,
            correlationId: options.correlationId,
            parentEventId: options.parentEventId,
            processingAttempts: 0,
        };
        return event_entity_1.Event.create(eventProps, options.id);
    }
    /**
     * Create an Event from raw log data
     */
    static fromRawLog(logData, sourceType, sourceIdentifier, options) {
        // Extract common fields from log data
        const title = EventFactory.extractTitle(logData);
        const description = EventFactory.extractDescription(logData);
        const severity = EventFactory.inferSeverity(logData);
        const eventType = EventFactory.inferEventType(logData);
        const timestamp = EventFactory.extractTimestamp(logData);
        const tags = EventFactory.extractTags(logData);
        return EventFactory.create({
            type: eventType,
            severity,
            title,
            description,
            rawData: logData,
            sourceType,
            sourceIdentifier,
            timestamp,
            tags,
            ...options,
        });
    }
    /**
     * Create a high-severity security Event
     */
    static createSecurityAlert(type, title, rawData, sourceType, sourceIdentifier, options) {
        return EventFactory.create({
            type,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            title,
            rawData,
            sourceType,
            sourceIdentifier,
            riskScore: 80, // Default high risk score for security alerts
            processingHints: {
                priority: 'high',
            },
            ...options,
        });
    }
    /**
     * Create a critical security Event
     */
    static createCriticalAlert(type, title, rawData, sourceType, sourceIdentifier, options) {
        return EventFactory.create({
            type,
            severity: event_severity_enum_1.EventSeverity.CRITICAL,
            title,
            rawData,
            sourceType,
            sourceIdentifier,
            riskScore: 95, // Default critical risk score
            processingHints: {
                priority: 'critical',
            },
            tags: ['critical', 'security-alert'],
            ...options,
        });
    }
    /**
     * Create a correlated Event (child of another event)
     */
    static createCorrelatedEvent(parentEvent, type, title, rawData, options) {
        return EventFactory.create({
            type,
            severity: parentEvent.severity, // Inherit severity from parent
            title,
            rawData,
            sourceType: parentEvent.metadata.source.type,
            sourceIdentifier: parentEvent.metadata.source.identifier,
            correlationId: parentEvent.correlationId || parentEvent.id.toString(),
            parentEventId: parentEvent.id,
            tags: [...parentEvent.tags, 'correlated'],
            ...options,
        });
    }
    /**
     * Create an Event from external threat intelligence
     */
    static fromThreatIntelligence(threatData, sourceIdentifier, options) {
        const title = `Threat Intelligence: ${threatData.indicator || 'Unknown Threat'}`;
        const severity = EventFactory.mapThreatSeverity(threatData.severity);
        const riskScore = EventFactory.calculateThreatRiskScore(threatData);
        return EventFactory.create({
            type: event_type_enum_1.EventType.THREAT_DETECTED,
            severity,
            title,
            description: threatData.description,
            rawData: threatData,
            sourceType: event_source_type_enum_1.EventSourceType.THREAT_INTELLIGENCE,
            sourceIdentifier,
            riskScore,
            tags: ['threat-intelligence', 'external'],
            attributes: {
                indicator: threatData.indicator,
                indicatorType: threatData.indicatorType,
                confidence: threatData.confidence,
                source: threatData.source,
            },
            ...options,
        });
    }
    /**
     * Create an Event from vulnerability scan results
     */
    static fromVulnerabilityScan(vulnData, sourceIdentifier, options) {
        const title = `Vulnerability: ${vulnData.cve || vulnData.title || 'Unknown Vulnerability'}`;
        const severity = EventFactory.mapVulnerabilitySeverity(vulnData.severity);
        const riskScore = EventFactory.calculateVulnerabilityRiskScore(vulnData);
        return EventFactory.create({
            type: event_type_enum_1.EventType.VULNERABILITY_DETECTED,
            severity,
            title,
            description: vulnData.description,
            rawData: vulnData,
            sourceType: event_source_type_enum_1.EventSourceType.VULNERABILITY_SCANNER,
            sourceIdentifier,
            riskScore,
            tags: ['vulnerability', 'scan-result'],
            attributes: {
                cve: vulnData.cve,
                cvssScore: vulnData.cvssScore,
                affectedAsset: vulnData.affectedAsset,
                solution: vulnData.solution,
            },
            ...options,
        });
    }
    /**
     * Create a manual Event (created by analyst)
     */
    static createManualEvent(type, severity, title, description, createdBy, rawData, options) {
        return EventFactory.create({
            type,
            severity,
            title,
            description,
            rawData: rawData || { createdBy, createdAt: new Date().toISOString() },
            sourceType: event_source_type_enum_1.EventSourceType.MANUAL,
            sourceIdentifier: createdBy,
            tags: ['manual', 'analyst-created'],
            attributes: {
                createdBy,
                createdAt: new Date().toISOString(),
            },
            ...options,
        });
    }
    // Helper methods for data extraction and inference
    static extractTitle(logData) {
        // Try common title fields
        const titleFields = ['title', 'message', 'summary', 'event_name', 'alert_name'];
        for (const field of titleFields) {
            if (logData[field] && typeof logData[field] === 'string') {
                return logData[field].substring(0, 200); // Truncate to max length
            }
        }
        // Fallback to generic title
        return 'Security Event';
    }
    static extractDescription(logData) {
        const descFields = ['description', 'details', 'full_message', 'event_description'];
        for (const field of descFields) {
            if (logData[field] && typeof logData[field] === 'string') {
                return logData[field].substring(0, 2000); // Truncate to max length
            }
        }
        return undefined;
    }
    static extractTimestamp(logData) {
        const timestampFields = ['timestamp', '@timestamp', 'event_time', 'created_at', 'occurred_at'];
        for (const field of timestampFields) {
            if (logData[field]) {
                const date = new Date(logData[field]);
                if (!isNaN(date.getTime())) {
                    return date;
                }
            }
        }
        return undefined;
    }
    static extractTags(logData) {
        const tagFields = ['tags', 'labels', 'categories'];
        for (const field of tagFields) {
            if (Array.isArray(logData[field])) {
                return logData[field].filter(tag => typeof tag === 'string').slice(0, 20);
            }
        }
        return undefined;
    }
    static inferSeverity(logData) {
        const severityFields = ['severity', 'level', 'priority', 'criticality'];
        for (const field of severityFields) {
            if (logData[field]) {
                const severity = String(logData[field]).toLowerCase();
                if (severity.includes('critical') || severity.includes('fatal')) {
                    return event_severity_enum_1.EventSeverity.CRITICAL;
                }
                if (severity.includes('high') || severity.includes('error')) {
                    return event_severity_enum_1.EventSeverity.HIGH;
                }
                if (severity.includes('medium') || severity.includes('warn')) {
                    return event_severity_enum_1.EventSeverity.MEDIUM;
                }
                if (severity.includes('low') || severity.includes('info')) {
                    return event_severity_enum_1.EventSeverity.LOW;
                }
            }
        }
        // Default to medium severity
        return event_severity_enum_1.EventSeverity.MEDIUM;
    }
    static inferEventType(logData) {
        const message = JSON.stringify(logData).toLowerCase();
        // Authentication events
        if (message.includes('login') || message.includes('authentication')) {
            if (message.includes('failed') || message.includes('failure')) {
                return event_type_enum_1.EventType.LOGIN_FAILURE;
            }
            return event_type_enum_1.EventType.LOGIN_SUCCESS;
        }
        // Network events
        if (message.includes('connection') || message.includes('network')) {
            return event_type_enum_1.EventType.CONNECTION_ESTABLISHED;
        }
        // Malware events
        if (message.includes('malware') || message.includes('virus')) {
            return event_type_enum_1.EventType.MALWARE_DETECTED;
        }
        // Vulnerability events
        if (message.includes('vulnerability') || message.includes('cve')) {
            return event_type_enum_1.EventType.VULNERABILITY_DETECTED;
        }
        // Threat events
        if (message.includes('threat') || message.includes('attack')) {
            return event_type_enum_1.EventType.THREAT_DETECTED;
        }
        // Default to custom event
        return event_type_enum_1.EventType.CUSTOM;
    }
    static mapThreatSeverity(severity) {
        if (typeof severity === 'string') {
            const sev = severity.toLowerCase();
            if (sev.includes('critical') || sev.includes('high'))
                return event_severity_enum_1.EventSeverity.CRITICAL;
            if (sev.includes('medium'))
                return event_severity_enum_1.EventSeverity.MEDIUM;
            if (sev.includes('low'))
                return event_severity_enum_1.EventSeverity.LOW;
        }
        if (typeof severity === 'number') {
            if (severity >= 8)
                return event_severity_enum_1.EventSeverity.CRITICAL;
            if (severity >= 6)
                return event_severity_enum_1.EventSeverity.HIGH;
            if (severity >= 4)
                return event_severity_enum_1.EventSeverity.MEDIUM;
            return event_severity_enum_1.EventSeverity.LOW;
        }
        return event_severity_enum_1.EventSeverity.MEDIUM;
    }
    static mapVulnerabilitySeverity(severity) {
        if (typeof severity === 'string') {
            const sev = severity.toLowerCase();
            if (sev.includes('critical'))
                return event_severity_enum_1.EventSeverity.CRITICAL;
            if (sev.includes('high'))
                return event_severity_enum_1.EventSeverity.HIGH;
            if (sev.includes('medium'))
                return event_severity_enum_1.EventSeverity.MEDIUM;
            if (sev.includes('low'))
                return event_severity_enum_1.EventSeverity.LOW;
        }
        if (typeof severity === 'number') {
            if (severity >= 9)
                return event_severity_enum_1.EventSeverity.CRITICAL;
            if (severity >= 7)
                return event_severity_enum_1.EventSeverity.HIGH;
            if (severity >= 4)
                return event_severity_enum_1.EventSeverity.MEDIUM;
            return event_severity_enum_1.EventSeverity.LOW;
        }
        return event_severity_enum_1.EventSeverity.MEDIUM;
    }
    static calculateThreatRiskScore(threatData) {
        let score = 50; // Base score
        // Adjust based on confidence
        if (threatData.confidence) {
            score += Math.floor(threatData.confidence * 0.3);
        }
        // Adjust based on severity
        if (threatData.severity) {
            const severity = typeof threatData.severity === 'number'
                ? threatData.severity
                : EventFactory.severityToNumber(threatData.severity);
            score += severity * 5;
        }
        // Adjust based on indicator type
        if (threatData.indicatorType === 'ip' || threatData.indicatorType === 'domain') {
            score += 10; // Network indicators are higher risk
        }
        return Math.min(100, Math.max(0, score));
    }
    static calculateVulnerabilityRiskScore(vulnData) {
        let score = 30; // Base score
        // Adjust based on CVSS score
        if (vulnData.cvssScore) {
            score += vulnData.cvssScore * 7; // CVSS 0-10 scale
        }
        // Adjust based on exploitability
        if (vulnData.exploitable) {
            score += 20;
        }
        // Adjust based on asset criticality
        if (vulnData.assetCriticality === 'high') {
            score += 15;
        }
        else if (vulnData.assetCriticality === 'medium') {
            score += 10;
        }
        return Math.min(100, Math.max(0, score));
    }
    static severityToNumber(severity) {
        const sev = severity.toLowerCase();
        if (sev.includes('critical'))
            return 10;
        if (sev.includes('high'))
            return 8;
        if (sev.includes('medium'))
            return 6;
        if (sev.includes('low'))
            return 4;
        return 5;
    }
}
exports.EventFactory = EventFactory;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFxmYWN0b3JpZXNcXGV2ZW50LmZhY3RvcnkudHMiLCJtYXBwaW5ncyI6Ijs7O0FBQUEsMkRBQTZEO0FBRTdELDZHQUE0RjtBQUM1RiwrR0FBOEY7QUFDOUYseUdBQXdGO0FBQ3hGLDhEQUFxRDtBQUNyRCxzRUFBNkQ7QUFDN0Qsa0VBQXlEO0FBQ3pELHdGQUE4RTtBQUM5RSw0RUFBa0U7QUEwRGxFOzs7Ozs7Ozs7Ozs7R0FZRztBQUNILE1BQWEsWUFBWTtJQUN2Qjs7T0FFRztJQUNILE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBMkI7UUFDdkMsc0JBQXNCO1FBQ3RCLE1BQU0sV0FBVyxHQUFHLHVDQUFXLENBQUMsTUFBTSxDQUNwQyxPQUFPLENBQUMsVUFBVSxFQUNsQixPQUFPLENBQUMsZ0JBQWdCLEVBQ3hCLE9BQU8sQ0FBQyxjQUFjLENBQ3ZCLENBQUM7UUFFRix5QkFBeUI7UUFDekIsTUFBTSxjQUFjLEdBQUcsT0FBTyxDQUFDLFNBQVM7WUFDdEMsQ0FBQyxDQUFDLDZDQUFjLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxTQUFTLENBQUM7WUFDNUMsQ0FBQyxDQUFDLDZDQUFjLENBQUMsTUFBTSxFQUFFLENBQUM7UUFFNUIsd0JBQXdCO1FBQ3hCLE1BQU0sYUFBYSxHQUFHLDJDQUFhLENBQUMsTUFBTSxDQUN4QyxjQUFjLEVBQ2QsV0FBVyxFQUNYO1lBQ0UsZUFBZSxFQUFFLE9BQU8sQ0FBQyxlQUFlO1NBQ3pDLENBQ0YsQ0FBQztRQUVGLHlCQUF5QjtRQUN6QixNQUFNLFVBQVUsR0FBZTtZQUM3QixRQUFRLEVBQUUsYUFBYTtZQUN2QixJQUFJLEVBQUUsT0FBTyxDQUFDLElBQUk7WUFDbEIsUUFBUSxFQUFFLE9BQU8sQ0FBQyxRQUFRO1lBQzFCLE1BQU0sRUFBRSxPQUFPLENBQUMsTUFBTSxJQUFJLCtCQUFXLENBQUMsTUFBTTtZQUM1QyxnQkFBZ0IsRUFBRSxPQUFPLENBQUMsZ0JBQWdCLElBQUksb0RBQXFCLENBQUMsR0FBRztZQUN2RSxPQUFPLEVBQUUsT0FBTyxDQUFDLE9BQU87WUFDeEIsS0FBSyxFQUFFLE9BQU8sQ0FBQyxLQUFLO1lBQ3BCLFdBQVcsRUFBRSxPQUFPLENBQUMsV0FBVztZQUNoQyxJQUFJLEVBQUUsT0FBTyxDQUFDLElBQUk7WUFDbEIsU0FBUyxFQUFFLE9BQU8sQ0FBQyxTQUFTO1lBQzVCLGVBQWUsRUFBRSxPQUFPLENBQUMsZUFBZTtZQUN4QyxVQUFVLEVBQUUsT0FBTyxDQUFDLFVBQVU7WUFDOUIsYUFBYSxFQUFFLE9BQU8sQ0FBQyxhQUFhO1lBQ3BDLGFBQWEsRUFBRSxPQUFPLENBQUMsYUFBYTtZQUNwQyxrQkFBa0IsRUFBRSxDQUFDO1NBQ3RCLENBQUM7UUFFRixPQUFPLG9CQUFLLENBQUMsTUFBTSxDQUFDLFVBQVUsRUFBRSxPQUFPLENBQUMsRUFBRSxDQUFDLENBQUM7SUFDOUMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsTUFBTSxDQUFDLFVBQVUsQ0FDZixPQUE0QixFQUM1QixVQUEyQixFQUMzQixnQkFBd0IsRUFDeEIsT0FBcUM7UUFFckMsc0NBQXNDO1FBQ3RDLE1BQU0sS0FBSyxHQUFHLFlBQVksQ0FBQyxZQUFZLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDakQsTUFBTSxXQUFXLEdBQUcsWUFBWSxDQUFDLGtCQUFrQixDQUFDLE9BQU8sQ0FBQyxDQUFDO1FBQzdELE1BQU0sUUFBUSxHQUFHLFlBQVksQ0FBQyxhQUFhLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDckQsTUFBTSxTQUFTLEdBQUcsWUFBWSxDQUFDLGNBQWMsQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUN2RCxNQUFNLFNBQVMsR0FBRyxZQUFZLENBQUMsZ0JBQWdCLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDekQsTUFBTSxJQUFJLEdBQUcsWUFBWSxDQUFDLFdBQVcsQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUUvQyxPQUFPLFlBQVksQ0FBQyxNQUFNLENBQUM7WUFDekIsSUFBSSxFQUFFLFNBQVM7WUFDZixRQUFRO1lBQ1IsS0FBSztZQUNMLFdBQVc7WUFDWCxPQUFPLEVBQUUsT0FBTztZQUNoQixVQUFVO1lBQ1YsZ0JBQWdCO1lBQ2hCLFNBQVM7WUFDVCxJQUFJO1lBQ0osR0FBRyxPQUFPO1NBQ1gsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0gsTUFBTSxDQUFDLG1CQUFtQixDQUN4QixJQUFlLEVBQ2YsS0FBYSxFQUNiLE9BQTRCLEVBQzVCLFVBQTJCLEVBQzNCLGdCQUF3QixFQUN4QixPQUFxQztRQUVyQyxPQUFPLFlBQVksQ0FBQyxNQUFNLENBQUM7WUFDekIsSUFBSTtZQUNKLFFBQVEsRUFBRSxtQ0FBYSxDQUFDLElBQUk7WUFDNUIsS0FBSztZQUNMLE9BQU87WUFDUCxVQUFVO1lBQ1YsZ0JBQWdCO1lBQ2hCLFNBQVMsRUFBRSxFQUFFLEVBQUUsOENBQThDO1lBQzdELGVBQWUsRUFBRTtnQkFDZixRQUFRLEVBQUUsTUFBTTthQUNqQjtZQUNELEdBQUcsT0FBTztTQUNYLENBQUMsQ0FBQztJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNILE1BQU0sQ0FBQyxtQkFBbUIsQ0FDeEIsSUFBZSxFQUNmLEtBQWEsRUFDYixPQUE0QixFQUM1QixVQUEyQixFQUMzQixnQkFBd0IsRUFDeEIsT0FBcUM7UUFFckMsT0FBTyxZQUFZLENBQUMsTUFBTSxDQUFDO1lBQ3pCLElBQUk7WUFDSixRQUFRLEVBQUUsbUNBQWEsQ0FBQyxRQUFRO1lBQ2hDLEtBQUs7WUFDTCxPQUFPO1lBQ1AsVUFBVTtZQUNWLGdCQUFnQjtZQUNoQixTQUFTLEVBQUUsRUFBRSxFQUFFLDhCQUE4QjtZQUM3QyxlQUFlLEVBQUU7Z0JBQ2YsUUFBUSxFQUFFLFVBQVU7YUFDckI7WUFDRCxJQUFJLEVBQUUsQ0FBQyxVQUFVLEVBQUUsZ0JBQWdCLENBQUM7WUFDcEMsR0FBRyxPQUFPO1NBQ1gsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0gsTUFBTSxDQUFDLHFCQUFxQixDQUMxQixXQUFrQixFQUNsQixJQUFlLEVBQ2YsS0FBYSxFQUNiLE9BQTRCLEVBQzVCLE9BQXFDO1FBRXJDLE9BQU8sWUFBWSxDQUFDLE1BQU0sQ0FBQztZQUN6QixJQUFJO1lBQ0osUUFBUSxFQUFFLFdBQVcsQ0FBQyxRQUFRLEVBQUUsK0JBQStCO1lBQy9ELEtBQUs7WUFDTCxPQUFPO1lBQ1AsVUFBVSxFQUFFLFdBQVcsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLElBQUk7WUFDNUMsZ0JBQWdCLEVBQUUsV0FBVyxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsVUFBVTtZQUN4RCxhQUFhLEVBQUUsV0FBVyxDQUFDLGFBQWEsSUFBSSxXQUFXLENBQUMsRUFBRSxDQUFDLFFBQVEsRUFBRTtZQUNyRSxhQUFhLEVBQUUsV0FBVyxDQUFDLEVBQUU7WUFDN0IsSUFBSSxFQUFFLENBQUMsR0FBRyxXQUFXLENBQUMsSUFBSSxFQUFFLFlBQVksQ0FBQztZQUN6QyxHQUFHLE9BQU87U0FDWCxDQUFDLENBQUM7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxNQUFNLENBQUMsc0JBQXNCLENBQzNCLFVBQStCLEVBQy9CLGdCQUF3QixFQUN4QixPQUFxQztRQUVyQyxNQUFNLEtBQUssR0FBRyx3QkFBd0IsVUFBVSxDQUFDLFNBQVMsSUFBSSxnQkFBZ0IsRUFBRSxDQUFDO1FBQ2pGLE1BQU0sUUFBUSxHQUFHLFlBQVksQ0FBQyxpQkFBaUIsQ0FBQyxVQUFVLENBQUMsUUFBUSxDQUFDLENBQUM7UUFDckUsTUFBTSxTQUFTLEdBQUcsWUFBWSxDQUFDLHdCQUF3QixDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBRXBFLE9BQU8sWUFBWSxDQUFDLE1BQU0sQ0FBQztZQUN6QixJQUFJLEVBQUUsMkJBQVMsQ0FBQyxlQUFlO1lBQy9CLFFBQVE7WUFDUixLQUFLO1lBQ0wsV0FBVyxFQUFFLFVBQVUsQ0FBQyxXQUFXO1lBQ25DLE9BQU8sRUFBRSxVQUFVO1lBQ25CLFVBQVUsRUFBRSx3Q0FBZSxDQUFDLG1CQUFtQjtZQUMvQyxnQkFBZ0I7WUFDaEIsU0FBUztZQUNULElBQUksRUFBRSxDQUFDLHFCQUFxQixFQUFFLFVBQVUsQ0FBQztZQUN6QyxVQUFVLEVBQUU7Z0JBQ1YsU0FBUyxFQUFFLFVBQVUsQ0FBQyxTQUFTO2dCQUMvQixhQUFhLEVBQUUsVUFBVSxDQUFDLGFBQWE7Z0JBQ3ZDLFVBQVUsRUFBRSxVQUFVLENBQUMsVUFBVTtnQkFDakMsTUFBTSxFQUFFLFVBQVUsQ0FBQyxNQUFNO2FBQzFCO1lBQ0QsR0FBRyxPQUFPO1NBQ1gsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0gsTUFBTSxDQUFDLHFCQUFxQixDQUMxQixRQUE2QixFQUM3QixnQkFBd0IsRUFDeEIsT0FBcUM7UUFFckMsTUFBTSxLQUFLLEdBQUcsa0JBQWtCLFFBQVEsQ0FBQyxHQUFHLElBQUksUUFBUSxDQUFDLEtBQUssSUFBSSx1QkFBdUIsRUFBRSxDQUFDO1FBQzVGLE1BQU0sUUFBUSxHQUFHLFlBQVksQ0FBQyx3QkFBd0IsQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDLENBQUM7UUFDMUUsTUFBTSxTQUFTLEdBQUcsWUFBWSxDQUFDLCtCQUErQixDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBRXpFLE9BQU8sWUFBWSxDQUFDLE1BQU0sQ0FBQztZQUN6QixJQUFJLEVBQUUsMkJBQVMsQ0FBQyxzQkFBc0I7WUFDdEMsUUFBUTtZQUNSLEtBQUs7WUFDTCxXQUFXLEVBQUUsUUFBUSxDQUFDLFdBQVc7WUFDakMsT0FBTyxFQUFFLFFBQVE7WUFDakIsVUFBVSxFQUFFLHdDQUFlLENBQUMscUJBQXFCO1lBQ2pELGdCQUFnQjtZQUNoQixTQUFTO1lBQ1QsSUFBSSxFQUFFLENBQUMsZUFBZSxFQUFFLGFBQWEsQ0FBQztZQUN0QyxVQUFVLEVBQUU7Z0JBQ1YsR0FBRyxFQUFFLFFBQVEsQ0FBQyxHQUFHO2dCQUNqQixTQUFTLEVBQUUsUUFBUSxDQUFDLFNBQVM7Z0JBQzdCLGFBQWEsRUFBRSxRQUFRLENBQUMsYUFBYTtnQkFDckMsUUFBUSxFQUFFLFFBQVEsQ0FBQyxRQUFRO2FBQzVCO1lBQ0QsR0FBRyxPQUFPO1NBQ1gsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0gsTUFBTSxDQUFDLGlCQUFpQixDQUN0QixJQUFlLEVBQ2YsUUFBdUIsRUFDdkIsS0FBYSxFQUNiLFdBQW1CLEVBQ25CLFNBQWlCLEVBQ2pCLE9BQTZCLEVBQzdCLE9BQXFDO1FBRXJDLE9BQU8sWUFBWSxDQUFDLE1BQU0sQ0FBQztZQUN6QixJQUFJO1lBQ0osUUFBUTtZQUNSLEtBQUs7WUFDTCxXQUFXO1lBQ1gsT0FBTyxFQUFFLE9BQU8sSUFBSSxFQUFFLFNBQVMsRUFBRSxTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUUsRUFBRTtZQUN0RSxVQUFVLEVBQUUsd0NBQWUsQ0FBQyxNQUFNO1lBQ2xDLGdCQUFnQixFQUFFLFNBQVM7WUFDM0IsSUFBSSxFQUFFLENBQUMsUUFBUSxFQUFFLGlCQUFpQixDQUFDO1lBQ25DLFVBQVUsRUFBRTtnQkFDVixTQUFTO2dCQUNULFNBQVMsRUFBRSxJQUFJLElBQUksRUFBRSxDQUFDLFdBQVcsRUFBRTthQUNwQztZQUNELEdBQUcsT0FBTztTQUNYLENBQUMsQ0FBQztJQUNMLENBQUM7SUFFRCxtREFBbUQ7SUFFM0MsTUFBTSxDQUFDLFlBQVksQ0FBQyxPQUE0QjtRQUN0RCwwQkFBMEI7UUFDMUIsTUFBTSxXQUFXLEdBQUcsQ0FBQyxPQUFPLEVBQUUsU0FBUyxFQUFFLFNBQVMsRUFBRSxZQUFZLEVBQUUsWUFBWSxDQUFDLENBQUM7UUFFaEYsS0FBSyxNQUFNLEtBQUssSUFBSSxXQUFXLEVBQUUsQ0FBQztZQUNoQyxJQUFJLE9BQU8sQ0FBQyxLQUFLLENBQUMsSUFBSSxPQUFPLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxRQUFRLEVBQUUsQ0FBQztnQkFDekQsT0FBTyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDLHlCQUF5QjtZQUNwRSxDQUFDO1FBQ0gsQ0FBQztRQUVELDRCQUE0QjtRQUM1QixPQUFPLGdCQUFnQixDQUFDO0lBQzFCLENBQUM7SUFFTyxNQUFNLENBQUMsa0JBQWtCLENBQUMsT0FBNEI7UUFDNUQsTUFBTSxVQUFVLEdBQUcsQ0FBQyxhQUFhLEVBQUUsU0FBUyxFQUFFLGNBQWMsRUFBRSxtQkFBbUIsQ0FBQyxDQUFDO1FBRW5GLEtBQUssTUFBTSxLQUFLLElBQUksVUFBVSxFQUFFLENBQUM7WUFDL0IsSUFBSSxPQUFPLENBQUMsS0FBSyxDQUFDLElBQUksT0FBTyxPQUFPLENBQUMsS0FBSyxDQUFDLEtBQUssUUFBUSxFQUFFLENBQUM7Z0JBQ3pELE9BQU8sT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUMsQ0FBQyx5QkFBeUI7WUFDckUsQ0FBQztRQUNILENBQUM7UUFFRCxPQUFPLFNBQVMsQ0FBQztJQUNuQixDQUFDO0lBRU8sTUFBTSxDQUFDLGdCQUFnQixDQUFDLE9BQTRCO1FBQzFELE1BQU0sZUFBZSxHQUFHLENBQUMsV0FBVyxFQUFFLFlBQVksRUFBRSxZQUFZLEVBQUUsWUFBWSxFQUFFLGFBQWEsQ0FBQyxDQUFDO1FBRS9GLEtBQUssTUFBTSxLQUFLLElBQUksZUFBZSxFQUFFLENBQUM7WUFDcEMsSUFBSSxPQUFPLENBQUMsS0FBSyxDQUFDLEVBQUUsQ0FBQztnQkFDbkIsTUFBTSxJQUFJLEdBQUcsSUFBSSxJQUFJLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7Z0JBQ3RDLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFDLEVBQUUsQ0FBQztvQkFDM0IsT0FBTyxJQUFJLENBQUM7Z0JBQ2QsQ0FBQztZQUNILENBQUM7UUFDSCxDQUFDO1FBRUQsT0FBTyxTQUFTLENBQUM7SUFDbkIsQ0FBQztJQUVPLE1BQU0sQ0FBQyxXQUFXLENBQUMsT0FBNEI7UUFDckQsTUFBTSxTQUFTLEdBQUcsQ0FBQyxNQUFNLEVBQUUsUUFBUSxFQUFFLFlBQVksQ0FBQyxDQUFDO1FBRW5ELEtBQUssTUFBTSxLQUFLLElBQUksU0FBUyxFQUFFLENBQUM7WUFDOUIsSUFBSSxLQUFLLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUM7Z0JBQ2xDLE9BQU8sT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLE9BQU8sR0FBRyxLQUFLLFFBQVEsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUM7WUFDNUUsQ0FBQztRQUNILENBQUM7UUFFRCxPQUFPLFNBQVMsQ0FBQztJQUNuQixDQUFDO0lBRU8sTUFBTSxDQUFDLGFBQWEsQ0FBQyxPQUE0QjtRQUN2RCxNQUFNLGNBQWMsR0FBRyxDQUFDLFVBQVUsRUFBRSxPQUFPLEVBQUUsVUFBVSxFQUFFLGFBQWEsQ0FBQyxDQUFDO1FBRXhFLEtBQUssTUFBTSxLQUFLLElBQUksY0FBYyxFQUFFLENBQUM7WUFDbkMsSUFBSSxPQUFPLENBQUMsS0FBSyxDQUFDLEVBQUUsQ0FBQztnQkFDbkIsTUFBTSxRQUFRLEdBQUcsTUFBTSxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO2dCQUV0RCxJQUFJLFFBQVEsQ0FBQyxRQUFRLENBQUMsVUFBVSxDQUFDLElBQUksUUFBUSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDO29CQUNoRSxPQUFPLG1DQUFhLENBQUMsUUFBUSxDQUFDO2dCQUNoQyxDQUFDO2dCQUNELElBQUksUUFBUSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsSUFBSSxRQUFRLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7b0JBQzVELE9BQU8sbUNBQWEsQ0FBQyxJQUFJLENBQUM7Z0JBQzVCLENBQUM7Z0JBQ0QsSUFBSSxRQUFRLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxJQUFJLFFBQVEsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLEVBQUUsQ0FBQztvQkFDN0QsT0FBTyxtQ0FBYSxDQUFDLE1BQU0sQ0FBQztnQkFDOUIsQ0FBQztnQkFDRCxJQUFJLFFBQVEsQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLElBQUksUUFBUSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDO29CQUMxRCxPQUFPLG1DQUFhLENBQUMsR0FBRyxDQUFDO2dCQUMzQixDQUFDO1lBQ0gsQ0FBQztRQUNILENBQUM7UUFFRCw2QkFBNkI7UUFDN0IsT0FBTyxtQ0FBYSxDQUFDLE1BQU0sQ0FBQztJQUM5QixDQUFDO0lBRU8sTUFBTSxDQUFDLGNBQWMsQ0FBQyxPQUE0QjtRQUN4RCxNQUFNLE9BQU8sR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1FBRXRELHdCQUF3QjtRQUN4QixJQUFJLE9BQU8sQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLElBQUksT0FBTyxDQUFDLFFBQVEsQ0FBQyxnQkFBZ0IsQ0FBQyxFQUFFLENBQUM7WUFDcEUsSUFBSSxPQUFPLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxJQUFJLE9BQU8sQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQztnQkFDOUQsT0FBTywyQkFBUyxDQUFDLGFBQWEsQ0FBQztZQUNqQyxDQUFDO1lBQ0QsT0FBTywyQkFBUyxDQUFDLGFBQWEsQ0FBQztRQUNqQyxDQUFDO1FBRUQsaUJBQWlCO1FBQ2pCLElBQUksT0FBTyxDQUFDLFFBQVEsQ0FBQyxZQUFZLENBQUMsSUFBSSxPQUFPLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUM7WUFDbEUsT0FBTywyQkFBUyxDQUFDLHNCQUFzQixDQUFDO1FBQzFDLENBQUM7UUFFRCxpQkFBaUI7UUFDakIsSUFBSSxPQUFPLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxJQUFJLE9BQU8sQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztZQUM3RCxPQUFPLDJCQUFTLENBQUMsZ0JBQWdCLENBQUM7UUFDcEMsQ0FBQztRQUVELHVCQUF1QjtRQUN2QixJQUFJLE9BQU8sQ0FBQyxRQUFRLENBQUMsZUFBZSxDQUFDLElBQUksT0FBTyxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsRUFBRSxDQUFDO1lBQ2pFLE9BQU8sMkJBQVMsQ0FBQyxzQkFBc0IsQ0FBQztRQUMxQyxDQUFDO1FBRUQsZ0JBQWdCO1FBQ2hCLElBQUksT0FBTyxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsSUFBSSxPQUFPLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUM7WUFDN0QsT0FBTywyQkFBUyxDQUFDLGVBQWUsQ0FBQztRQUNuQyxDQUFDO1FBRUQsMEJBQTBCO1FBQzFCLE9BQU8sMkJBQVMsQ0FBQyxNQUFNLENBQUM7SUFDMUIsQ0FBQztJQUVPLE1BQU0sQ0FBQyxpQkFBaUIsQ0FBQyxRQUF5QjtRQUN4RCxJQUFJLE9BQU8sUUFBUSxLQUFLLFFBQVEsRUFBRSxDQUFDO1lBQ2pDLE1BQU0sR0FBRyxHQUFHLFFBQVEsQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUNuQyxJQUFJLEdBQUcsQ0FBQyxRQUFRLENBQUMsVUFBVSxDQUFDLElBQUksR0FBRyxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUM7Z0JBQUUsT0FBTyxtQ0FBYSxDQUFDLFFBQVEsQ0FBQztZQUNwRixJQUFJLEdBQUcsQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDO2dCQUFFLE9BQU8sbUNBQWEsQ0FBQyxNQUFNLENBQUM7WUFDeEQsSUFBSSxHQUFHLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQztnQkFBRSxPQUFPLG1DQUFhLENBQUMsR0FBRyxDQUFDO1FBQ3BELENBQUM7UUFFRCxJQUFJLE9BQU8sUUFBUSxLQUFLLFFBQVEsRUFBRSxDQUFDO1lBQ2pDLElBQUksUUFBUSxJQUFJLENBQUM7Z0JBQUUsT0FBTyxtQ0FBYSxDQUFDLFFBQVEsQ0FBQztZQUNqRCxJQUFJLFFBQVEsSUFBSSxDQUFDO2dCQUFFLE9BQU8sbUNBQWEsQ0FBQyxJQUFJLENBQUM7WUFDN0MsSUFBSSxRQUFRLElBQUksQ0FBQztnQkFBRSxPQUFPLG1DQUFhLENBQUMsTUFBTSxDQUFDO1lBQy9DLE9BQU8sbUNBQWEsQ0FBQyxHQUFHLENBQUM7UUFDM0IsQ0FBQztRQUVELE9BQU8sbUNBQWEsQ0FBQyxNQUFNLENBQUM7SUFDOUIsQ0FBQztJQUVPLE1BQU0sQ0FBQyx3QkFBd0IsQ0FBQyxRQUF5QjtRQUMvRCxJQUFJLE9BQU8sUUFBUSxLQUFLLFFBQVEsRUFBRSxDQUFDO1lBQ2pDLE1BQU0sR0FBRyxHQUFHLFFBQVEsQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUNuQyxJQUFJLEdBQUcsQ0FBQyxRQUFRLENBQUMsVUFBVSxDQUFDO2dCQUFFLE9BQU8sbUNBQWEsQ0FBQyxRQUFRLENBQUM7WUFDNUQsSUFBSSxHQUFHLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQztnQkFBRSxPQUFPLG1DQUFhLENBQUMsSUFBSSxDQUFDO1lBQ3BELElBQUksR0FBRyxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUM7Z0JBQUUsT0FBTyxtQ0FBYSxDQUFDLE1BQU0sQ0FBQztZQUN4RCxJQUFJLEdBQUcsQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDO2dCQUFFLE9BQU8sbUNBQWEsQ0FBQyxHQUFHLENBQUM7UUFDcEQsQ0FBQztRQUVELElBQUksT0FBTyxRQUFRLEtBQUssUUFBUSxFQUFFLENBQUM7WUFDakMsSUFBSSxRQUFRLElBQUksQ0FBQztnQkFBRSxPQUFPLG1DQUFhLENBQUMsUUFBUSxDQUFDO1lBQ2pELElBQUksUUFBUSxJQUFJLENBQUM7Z0JBQUUsT0FBTyxtQ0FBYSxDQUFDLElBQUksQ0FBQztZQUM3QyxJQUFJLFFBQVEsSUFBSSxDQUFDO2dCQUFFLE9BQU8sbUNBQWEsQ0FBQyxNQUFNLENBQUM7WUFDL0MsT0FBTyxtQ0FBYSxDQUFDLEdBQUcsQ0FBQztRQUMzQixDQUFDO1FBRUQsT0FBTyxtQ0FBYSxDQUFDLE1BQU0sQ0FBQztJQUM5QixDQUFDO0lBRU8sTUFBTSxDQUFDLHdCQUF3QixDQUFDLFVBQStCO1FBQ3JFLElBQUksS0FBSyxHQUFHLEVBQUUsQ0FBQyxDQUFDLGFBQWE7UUFFN0IsNkJBQTZCO1FBQzdCLElBQUksVUFBVSxDQUFDLFVBQVUsRUFBRSxDQUFDO1lBQzFCLEtBQUssSUFBSSxJQUFJLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQyxVQUFVLEdBQUcsR0FBRyxDQUFDLENBQUM7UUFDbkQsQ0FBQztRQUVELDJCQUEyQjtRQUMzQixJQUFJLFVBQVUsQ0FBQyxRQUFRLEVBQUUsQ0FBQztZQUN4QixNQUFNLFFBQVEsR0FBRyxPQUFPLFVBQVUsQ0FBQyxRQUFRLEtBQUssUUFBUTtnQkFDdEQsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxRQUFRO2dCQUNyQixDQUFDLENBQUMsWUFBWSxDQUFDLGdCQUFnQixDQUFDLFVBQVUsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUN2RCxLQUFLLElBQUksUUFBUSxHQUFHLENBQUMsQ0FBQztRQUN4QixDQUFDO1FBRUQsaUNBQWlDO1FBQ2pDLElBQUksVUFBVSxDQUFDLGFBQWEsS0FBSyxJQUFJLElBQUksVUFBVSxDQUFDLGFBQWEsS0FBSyxRQUFRLEVBQUUsQ0FBQztZQUMvRSxLQUFLLElBQUksRUFBRSxDQUFDLENBQUMscUNBQXFDO1FBQ3BELENBQUM7UUFFRCxPQUFPLElBQUksQ0FBQyxHQUFHLENBQUMsR0FBRyxFQUFFLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLEtBQUssQ0FBQyxDQUFDLENBQUM7SUFDM0MsQ0FBQztJQUVPLE1BQU0sQ0FBQywrQkFBK0IsQ0FBQyxRQUE2QjtRQUMxRSxJQUFJLEtBQUssR0FBRyxFQUFFLENBQUMsQ0FBQyxhQUFhO1FBRTdCLDZCQUE2QjtRQUM3QixJQUFJLFFBQVEsQ0FBQyxTQUFTLEVBQUUsQ0FBQztZQUN2QixLQUFLLElBQUksUUFBUSxDQUFDLFNBQVMsR0FBRyxDQUFDLENBQUMsQ0FBQyxrQkFBa0I7UUFDckQsQ0FBQztRQUVELGlDQUFpQztRQUNqQyxJQUFJLFFBQVEsQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUN6QixLQUFLLElBQUksRUFBRSxDQUFDO1FBQ2QsQ0FBQztRQUVELG9DQUFvQztRQUNwQyxJQUFJLFFBQVEsQ0FBQyxnQkFBZ0IsS0FBSyxNQUFNLEVBQUUsQ0FBQztZQUN6QyxLQUFLLElBQUksRUFBRSxDQUFDO1FBQ2QsQ0FBQzthQUFNLElBQUksUUFBUSxDQUFDLGdCQUFnQixLQUFLLFFBQVEsRUFBRSxDQUFDO1lBQ2xELEtBQUssSUFBSSxFQUFFLENBQUM7UUFDZCxDQUFDO1FBRUQsT0FBTyxJQUFJLENBQUMsR0FBRyxDQUFDLEdBQUcsRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRSxLQUFLLENBQUMsQ0FBQyxDQUFDO0lBQzNDLENBQUM7SUFFTyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsUUFBZ0I7UUFDOUMsTUFBTSxHQUFHLEdBQUcsUUFBUSxDQUFDLFdBQVcsRUFBRSxDQUFDO1FBQ25DLElBQUksR0FBRyxDQUFDLFFBQVEsQ0FBQyxVQUFVLENBQUM7WUFBRSxPQUFPLEVBQUUsQ0FBQztRQUN4QyxJQUFJLEdBQUcsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDO1lBQUUsT0FBTyxDQUFDLENBQUM7UUFDbkMsSUFBSSxHQUFHLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQztZQUFFLE9BQU8sQ0FBQyxDQUFDO1FBQ3JDLElBQUksR0FBRyxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUM7WUFBRSxPQUFPLENBQUMsQ0FBQztRQUNsQyxPQUFPLENBQUMsQ0FBQztJQUNYLENBQUM7Q0FDRjtBQXpjRCxvQ0F5Y0MiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFxmYWN0b3JpZXNcXGV2ZW50LmZhY3RvcnkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRXZlbnQsIEV2ZW50UHJvcHMgfSBmcm9tICcuLi9lbnRpdGllcy9ldmVudC5lbnRpdHknO1xyXG5pbXBvcnQgeyBVbmlxdWVFbnRpdHlJZCB9IGZyb20gJy4uLy4uLy4uLy4uL3NoYXJlZC1rZXJuZWwnO1xyXG5pbXBvcnQgeyBFdmVudE1ldGFkYXRhIH0gZnJvbSAnLi4vdmFsdWUtb2JqZWN0cy9ldmVudC1tZXRhZGF0YS9ldmVudC1tZXRhZGF0YS52YWx1ZS1vYmplY3QnO1xyXG5pbXBvcnQgeyBFdmVudFRpbWVzdGFtcCB9IGZyb20gJy4uL3ZhbHVlLW9iamVjdHMvZXZlbnQtbWV0YWRhdGEvZXZlbnQtdGltZXN0YW1wLnZhbHVlLW9iamVjdCc7XHJcbmltcG9ydCB7IEV2ZW50U291cmNlIH0gZnJvbSAnLi4vdmFsdWUtb2JqZWN0cy9ldmVudC1tZXRhZGF0YS9ldmVudC1zb3VyY2UudmFsdWUtb2JqZWN0JztcclxuaW1wb3J0IHsgRXZlbnRUeXBlIH0gZnJvbSAnLi4vZW51bXMvZXZlbnQtdHlwZS5lbnVtJztcclxuaW1wb3J0IHsgRXZlbnRTZXZlcml0eSB9IGZyb20gJy4uL2VudW1zL2V2ZW50LXNldmVyaXR5LmVudW0nO1xyXG5pbXBvcnQgeyBFdmVudFN0YXR1cyB9IGZyb20gJy4uL2VudW1zL2V2ZW50LXN0YXR1cy5lbnVtJztcclxuaW1wb3J0IHsgRXZlbnRQcm9jZXNzaW5nU3RhdHVzIH0gZnJvbSAnLi4vZW51bXMvZXZlbnQtcHJvY2Vzc2luZy1zdGF0dXMuZW51bSc7XHJcbmltcG9ydCB7IEV2ZW50U291cmNlVHlwZSB9IGZyb20gJy4uL2VudW1zL2V2ZW50LXNvdXJjZS10eXBlLmVudW0nO1xyXG5cclxuLyoqXHJcbiAqIEV2ZW50IENyZWF0aW9uIE9wdGlvbnNcclxuICovXHJcbmV4cG9ydCBpbnRlcmZhY2UgQ3JlYXRlRXZlbnRPcHRpb25zIHtcclxuICAvKiogRXZlbnQgSUQgKG9wdGlvbmFsLCB3aWxsIGJlIGdlbmVyYXRlZCBpZiBub3QgcHJvdmlkZWQpICovXHJcbiAgaWQ/OiBVbmlxdWVFbnRpdHlJZDtcclxuICAvKiogRXZlbnQgdHlwZSAqL1xyXG4gIHR5cGU6IEV2ZW50VHlwZTtcclxuICAvKiogRXZlbnQgc2V2ZXJpdHkgKi9cclxuICBzZXZlcml0eTogRXZlbnRTZXZlcml0eTtcclxuICAvKiogRXZlbnQgdGl0bGUgKi9cclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIC8qKiBFdmVudCBkZXNjcmlwdGlvbiAqL1xyXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIC8qKiBSYXcgZXZlbnQgZGF0YSAqL1xyXG4gIHJhd0RhdGE6IFJlY29yZDxzdHJpbmcsIGFueT47XHJcbiAgLyoqIEV2ZW50IHNvdXJjZSB0eXBlICovXHJcbiAgc291cmNlVHlwZTogRXZlbnRTb3VyY2VUeXBlO1xyXG4gIC8qKiBFdmVudCBzb3VyY2UgaWRlbnRpZmllciAqL1xyXG4gIHNvdXJjZUlkZW50aWZpZXI6IHN0cmluZztcclxuICAvKiogRXZlbnQgdGltZXN0YW1wIChvcHRpb25hbCwgd2lsbCB1c2UgY3VycmVudCB0aW1lIGlmIG5vdCBwcm92aWRlZCkgKi9cclxuICB0aW1lc3RhbXA/OiBEYXRlO1xyXG4gIC8qKiBJbml0aWFsIHRhZ3MgKi9cclxuICB0YWdzPzogc3RyaW5nW107XHJcbiAgLyoqIEluaXRpYWwgcmlzayBzY29yZSAqL1xyXG4gIHJpc2tTY29yZT86IG51bWJlcjtcclxuICAvKiogSW5pdGlhbCBjb25maWRlbmNlIGxldmVsICovXHJcbiAgY29uZmlkZW5jZUxldmVsPzogbnVtYmVyO1xyXG4gIC8qKiBJbml0aWFsIGF0dHJpYnV0ZXMgKi9cclxuICBhdHRyaWJ1dGVzPzogUmVjb3JkPHN0cmluZywgYW55PjtcclxuICAvKiogQ29ycmVsYXRpb24gSUQgKi9cclxuICBjb3JyZWxhdGlvbklkPzogc3RyaW5nO1xyXG4gIC8qKiBQYXJlbnQgZXZlbnQgSUQgKi9cclxuICBwYXJlbnRFdmVudElkPzogVW5pcXVlRW50aXR5SWQ7XHJcbiAgLyoqIEluaXRpYWwgc3RhdHVzIChvcHRpb25hbCwgZGVmYXVsdHMgdG8gQUNUSVZFKSAqL1xyXG4gIHN0YXR1cz86IEV2ZW50U3RhdHVzO1xyXG4gIC8qKiBJbml0aWFsIHByb2Nlc3Npbmcgc3RhdHVzIChvcHRpb25hbCwgZGVmYXVsdHMgdG8gUkFXKSAqL1xyXG4gIHByb2Nlc3NpbmdTdGF0dXM/OiBFdmVudFByb2Nlc3NpbmdTdGF0dXM7XHJcbiAgLyoqIFNvdXJjZSBtZXRhZGF0YSAqL1xyXG4gIHNvdXJjZU1ldGFkYXRhPzoge1xyXG4gICAgbmFtZT86IHN0cmluZztcclxuICAgIHZlcnNpb24/OiBzdHJpbmc7XHJcbiAgICB2ZW5kb3I/OiBzdHJpbmc7XHJcbiAgICBsb2NhdGlvbj86IHN0cmluZztcclxuICAgIG1ldGFkYXRhPzogUmVjb3JkPHN0cmluZywgYW55PjtcclxuICB9O1xyXG4gIC8qKiBQcm9jZXNzaW5nIGhpbnRzICovXHJcbiAgcHJvY2Vzc2luZ0hpbnRzPzoge1xyXG4gICAgcHJpb3JpdHk/OiAnbG93JyB8ICdub3JtYWwnIHwgJ2hpZ2gnIHwgJ2NyaXRpY2FsJztcclxuICAgIHNraXBOb3JtYWxpemF0aW9uPzogYm9vbGVhbjtcclxuICAgIHNraXBFbnJpY2htZW50PzogYm9vbGVhbjtcclxuICAgIHNraXBDb3JyZWxhdGlvbj86IGJvb2xlYW47XHJcbiAgICByZXRlbnRpb25EYXlzPzogbnVtYmVyO1xyXG4gIH07XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBFdmVudCBGYWN0b3J5XHJcbiAqIFxyXG4gKiBGYWN0b3J5IGNsYXNzIGZvciBjcmVhdGluZyBFdmVudCBlbnRpdGllcyB3aXRoIHByb3BlciB2YWxpZGF0aW9uIGFuZCBkZWZhdWx0cy5cclxuICogSGFuZGxlcyBjb21wbGV4IGV2ZW50IGNyZWF0aW9uIHNjZW5hcmlvcyBhbmQgZW5zdXJlcyBhbGwgYnVzaW5lc3MgcnVsZXMgYXJlIGFwcGxpZWQuXHJcbiAqIFxyXG4gKiBLZXkgcmVzcG9uc2liaWxpdGllczpcclxuICogLSBDcmVhdGUgZXZlbnRzIGZyb20gdmFyaW91cyBpbnB1dCBmb3JtYXRzXHJcbiAqIC0gQXBwbHkgZGVmYXVsdCB2YWx1ZXMgYW5kIGJ1c2luZXNzIHJ1bGVzXHJcbiAqIC0gVmFsaWRhdGUgZXZlbnQgZGF0YSBjb25zaXN0ZW5jeVxyXG4gKiAtIEdlbmVyYXRlIHByb3BlciBtZXRhZGF0YSBhbmQgdGltZXN0YW1wc1xyXG4gKiAtIEhhbmRsZSBldmVudCByZWxhdGlvbnNoaXBzIGFuZCBjb3JyZWxhdGlvbnNcclxuICovXHJcbmV4cG9ydCBjbGFzcyBFdmVudEZhY3Rvcnkge1xyXG4gIC8qKlxyXG4gICAqIENyZWF0ZSBhIG5ldyBFdmVudCB3aXRoIHRoZSBwcm92aWRlZCBvcHRpb25zXHJcbiAgICovXHJcbiAgc3RhdGljIGNyZWF0ZShvcHRpb25zOiBDcmVhdGVFdmVudE9wdGlvbnMpOiBFdmVudCB7XHJcbiAgICAvLyBDcmVhdGUgZXZlbnQgc291cmNlXHJcbiAgICBjb25zdCBldmVudFNvdXJjZSA9IEV2ZW50U291cmNlLmNyZWF0ZShcclxuICAgICAgb3B0aW9ucy5zb3VyY2VUeXBlLFxyXG4gICAgICBvcHRpb25zLnNvdXJjZUlkZW50aWZpZXIsXHJcbiAgICAgIG9wdGlvbnMuc291cmNlTWV0YWRhdGFcclxuICAgICk7XHJcblxyXG4gICAgLy8gQ3JlYXRlIGV2ZW50IHRpbWVzdGFtcFxyXG4gICAgY29uc3QgZXZlbnRUaW1lc3RhbXAgPSBvcHRpb25zLnRpbWVzdGFtcCBcclxuICAgICAgPyBFdmVudFRpbWVzdGFtcC5mcm9tRGF0ZShvcHRpb25zLnRpbWVzdGFtcClcclxuICAgICAgOiBFdmVudFRpbWVzdGFtcC5jcmVhdGUoKTtcclxuXHJcbiAgICAvLyBDcmVhdGUgZXZlbnQgbWV0YWRhdGFcclxuICAgIGNvbnN0IGV2ZW50TWV0YWRhdGEgPSBFdmVudE1ldGFkYXRhLmNyZWF0ZShcclxuICAgICAgZXZlbnRUaW1lc3RhbXAsXHJcbiAgICAgIGV2ZW50U291cmNlLFxyXG4gICAgICB7XHJcbiAgICAgICAgcHJvY2Vzc2luZ0hpbnRzOiBvcHRpb25zLnByb2Nlc3NpbmdIaW50cyxcclxuICAgICAgfVxyXG4gICAgKTtcclxuXHJcbiAgICAvLyBCdWlsZCBldmVudCBwcm9wZXJ0aWVzXHJcbiAgICBjb25zdCBldmVudFByb3BzOiBFdmVudFByb3BzID0ge1xyXG4gICAgICBtZXRhZGF0YTogZXZlbnRNZXRhZGF0YSxcclxuICAgICAgdHlwZTogb3B0aW9ucy50eXBlLFxyXG4gICAgICBzZXZlcml0eTogb3B0aW9ucy5zZXZlcml0eSxcclxuICAgICAgc3RhdHVzOiBvcHRpb25zLnN0YXR1cyB8fCBFdmVudFN0YXR1cy5BQ1RJVkUsXHJcbiAgICAgIHByb2Nlc3NpbmdTdGF0dXM6IG9wdGlvbnMucHJvY2Vzc2luZ1N0YXR1cyB8fCBFdmVudFByb2Nlc3NpbmdTdGF0dXMuUkFXLFxyXG4gICAgICByYXdEYXRhOiBvcHRpb25zLnJhd0RhdGEsXHJcbiAgICAgIHRpdGxlOiBvcHRpb25zLnRpdGxlLFxyXG4gICAgICBkZXNjcmlwdGlvbjogb3B0aW9ucy5kZXNjcmlwdGlvbixcclxuICAgICAgdGFnczogb3B0aW9ucy50YWdzLFxyXG4gICAgICByaXNrU2NvcmU6IG9wdGlvbnMucmlza1Njb3JlLFxyXG4gICAgICBjb25maWRlbmNlTGV2ZWw6IG9wdGlvbnMuY29uZmlkZW5jZUxldmVsLFxyXG4gICAgICBhdHRyaWJ1dGVzOiBvcHRpb25zLmF0dHJpYnV0ZXMsXHJcbiAgICAgIGNvcnJlbGF0aW9uSWQ6IG9wdGlvbnMuY29ycmVsYXRpb25JZCxcclxuICAgICAgcGFyZW50RXZlbnRJZDogb3B0aW9ucy5wYXJlbnRFdmVudElkLFxyXG4gICAgICBwcm9jZXNzaW5nQXR0ZW1wdHM6IDAsXHJcbiAgICB9O1xyXG5cclxuICAgIHJldHVybiBFdmVudC5jcmVhdGUoZXZlbnRQcm9wcywgb3B0aW9ucy5pZCk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDcmVhdGUgYW4gRXZlbnQgZnJvbSByYXcgbG9nIGRhdGFcclxuICAgKi9cclxuICBzdGF0aWMgZnJvbVJhd0xvZyhcclxuICAgIGxvZ0RhdGE6IFJlY29yZDxzdHJpbmcsIGFueT4sXHJcbiAgICBzb3VyY2VUeXBlOiBFdmVudFNvdXJjZVR5cGUsXHJcbiAgICBzb3VyY2VJZGVudGlmaWVyOiBzdHJpbmcsXHJcbiAgICBvcHRpb25zPzogUGFydGlhbDxDcmVhdGVFdmVudE9wdGlvbnM+XHJcbiAgKTogRXZlbnQge1xyXG4gICAgLy8gRXh0cmFjdCBjb21tb24gZmllbGRzIGZyb20gbG9nIGRhdGFcclxuICAgIGNvbnN0IHRpdGxlID0gRXZlbnRGYWN0b3J5LmV4dHJhY3RUaXRsZShsb2dEYXRhKTtcclxuICAgIGNvbnN0IGRlc2NyaXB0aW9uID0gRXZlbnRGYWN0b3J5LmV4dHJhY3REZXNjcmlwdGlvbihsb2dEYXRhKTtcclxuICAgIGNvbnN0IHNldmVyaXR5ID0gRXZlbnRGYWN0b3J5LmluZmVyU2V2ZXJpdHkobG9nRGF0YSk7XHJcbiAgICBjb25zdCBldmVudFR5cGUgPSBFdmVudEZhY3RvcnkuaW5mZXJFdmVudFR5cGUobG9nRGF0YSk7XHJcbiAgICBjb25zdCB0aW1lc3RhbXAgPSBFdmVudEZhY3RvcnkuZXh0cmFjdFRpbWVzdGFtcChsb2dEYXRhKTtcclxuICAgIGNvbnN0IHRhZ3MgPSBFdmVudEZhY3RvcnkuZXh0cmFjdFRhZ3MobG9nRGF0YSk7XHJcblxyXG4gICAgcmV0dXJuIEV2ZW50RmFjdG9yeS5jcmVhdGUoe1xyXG4gICAgICB0eXBlOiBldmVudFR5cGUsXHJcbiAgICAgIHNldmVyaXR5LFxyXG4gICAgICB0aXRsZSxcclxuICAgICAgZGVzY3JpcHRpb24sXHJcbiAgICAgIHJhd0RhdGE6IGxvZ0RhdGEsXHJcbiAgICAgIHNvdXJjZVR5cGUsXHJcbiAgICAgIHNvdXJjZUlkZW50aWZpZXIsXHJcbiAgICAgIHRpbWVzdGFtcCxcclxuICAgICAgdGFncyxcclxuICAgICAgLi4ub3B0aW9ucyxcclxuICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ3JlYXRlIGEgaGlnaC1zZXZlcml0eSBzZWN1cml0eSBFdmVudFxyXG4gICAqL1xyXG4gIHN0YXRpYyBjcmVhdGVTZWN1cml0eUFsZXJ0KFxyXG4gICAgdHlwZTogRXZlbnRUeXBlLFxyXG4gICAgdGl0bGU6IHN0cmluZyxcclxuICAgIHJhd0RhdGE6IFJlY29yZDxzdHJpbmcsIGFueT4sXHJcbiAgICBzb3VyY2VUeXBlOiBFdmVudFNvdXJjZVR5cGUsXHJcbiAgICBzb3VyY2VJZGVudGlmaWVyOiBzdHJpbmcsXHJcbiAgICBvcHRpb25zPzogUGFydGlhbDxDcmVhdGVFdmVudE9wdGlvbnM+XHJcbiAgKTogRXZlbnQge1xyXG4gICAgcmV0dXJuIEV2ZW50RmFjdG9yeS5jcmVhdGUoe1xyXG4gICAgICB0eXBlLFxyXG4gICAgICBzZXZlcml0eTogRXZlbnRTZXZlcml0eS5ISUdILFxyXG4gICAgICB0aXRsZSxcclxuICAgICAgcmF3RGF0YSxcclxuICAgICAgc291cmNlVHlwZSxcclxuICAgICAgc291cmNlSWRlbnRpZmllcixcclxuICAgICAgcmlza1Njb3JlOiA4MCwgLy8gRGVmYXVsdCBoaWdoIHJpc2sgc2NvcmUgZm9yIHNlY3VyaXR5IGFsZXJ0c1xyXG4gICAgICBwcm9jZXNzaW5nSGludHM6IHtcclxuICAgICAgICBwcmlvcml0eTogJ2hpZ2gnLFxyXG4gICAgICB9LFxyXG4gICAgICAuLi5vcHRpb25zLFxyXG4gICAgfSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDcmVhdGUgYSBjcml0aWNhbCBzZWN1cml0eSBFdmVudFxyXG4gICAqL1xyXG4gIHN0YXRpYyBjcmVhdGVDcml0aWNhbEFsZXJ0KFxyXG4gICAgdHlwZTogRXZlbnRUeXBlLFxyXG4gICAgdGl0bGU6IHN0cmluZyxcclxuICAgIHJhd0RhdGE6IFJlY29yZDxzdHJpbmcsIGFueT4sXHJcbiAgICBzb3VyY2VUeXBlOiBFdmVudFNvdXJjZVR5cGUsXHJcbiAgICBzb3VyY2VJZGVudGlmaWVyOiBzdHJpbmcsXHJcbiAgICBvcHRpb25zPzogUGFydGlhbDxDcmVhdGVFdmVudE9wdGlvbnM+XHJcbiAgKTogRXZlbnQge1xyXG4gICAgcmV0dXJuIEV2ZW50RmFjdG9yeS5jcmVhdGUoe1xyXG4gICAgICB0eXBlLFxyXG4gICAgICBzZXZlcml0eTogRXZlbnRTZXZlcml0eS5DUklUSUNBTCxcclxuICAgICAgdGl0bGUsXHJcbiAgICAgIHJhd0RhdGEsXHJcbiAgICAgIHNvdXJjZVR5cGUsXHJcbiAgICAgIHNvdXJjZUlkZW50aWZpZXIsXHJcbiAgICAgIHJpc2tTY29yZTogOTUsIC8vIERlZmF1bHQgY3JpdGljYWwgcmlzayBzY29yZVxyXG4gICAgICBwcm9jZXNzaW5nSGludHM6IHtcclxuICAgICAgICBwcmlvcml0eTogJ2NyaXRpY2FsJyxcclxuICAgICAgfSxcclxuICAgICAgdGFnczogWydjcml0aWNhbCcsICdzZWN1cml0eS1hbGVydCddLFxyXG4gICAgICAuLi5vcHRpb25zLFxyXG4gICAgfSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDcmVhdGUgYSBjb3JyZWxhdGVkIEV2ZW50IChjaGlsZCBvZiBhbm90aGVyIGV2ZW50KVxyXG4gICAqL1xyXG4gIHN0YXRpYyBjcmVhdGVDb3JyZWxhdGVkRXZlbnQoXHJcbiAgICBwYXJlbnRFdmVudDogRXZlbnQsXHJcbiAgICB0eXBlOiBFdmVudFR5cGUsXHJcbiAgICB0aXRsZTogc3RyaW5nLFxyXG4gICAgcmF3RGF0YTogUmVjb3JkPHN0cmluZywgYW55PixcclxuICAgIG9wdGlvbnM/OiBQYXJ0aWFsPENyZWF0ZUV2ZW50T3B0aW9ucz5cclxuICApOiBFdmVudCB7XHJcbiAgICByZXR1cm4gRXZlbnRGYWN0b3J5LmNyZWF0ZSh7XHJcbiAgICAgIHR5cGUsXHJcbiAgICAgIHNldmVyaXR5OiBwYXJlbnRFdmVudC5zZXZlcml0eSwgLy8gSW5oZXJpdCBzZXZlcml0eSBmcm9tIHBhcmVudFxyXG4gICAgICB0aXRsZSxcclxuICAgICAgcmF3RGF0YSxcclxuICAgICAgc291cmNlVHlwZTogcGFyZW50RXZlbnQubWV0YWRhdGEuc291cmNlLnR5cGUsXHJcbiAgICAgIHNvdXJjZUlkZW50aWZpZXI6IHBhcmVudEV2ZW50Lm1ldGFkYXRhLnNvdXJjZS5pZGVudGlmaWVyLFxyXG4gICAgICBjb3JyZWxhdGlvbklkOiBwYXJlbnRFdmVudC5jb3JyZWxhdGlvbklkIHx8IHBhcmVudEV2ZW50LmlkLnRvU3RyaW5nKCksXHJcbiAgICAgIHBhcmVudEV2ZW50SWQ6IHBhcmVudEV2ZW50LmlkLFxyXG4gICAgICB0YWdzOiBbLi4ucGFyZW50RXZlbnQudGFncywgJ2NvcnJlbGF0ZWQnXSxcclxuICAgICAgLi4ub3B0aW9ucyxcclxuICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ3JlYXRlIGFuIEV2ZW50IGZyb20gZXh0ZXJuYWwgdGhyZWF0IGludGVsbGlnZW5jZVxyXG4gICAqL1xyXG4gIHN0YXRpYyBmcm9tVGhyZWF0SW50ZWxsaWdlbmNlKFxyXG4gICAgdGhyZWF0RGF0YTogUmVjb3JkPHN0cmluZywgYW55PixcclxuICAgIHNvdXJjZUlkZW50aWZpZXI6IHN0cmluZyxcclxuICAgIG9wdGlvbnM/OiBQYXJ0aWFsPENyZWF0ZUV2ZW50T3B0aW9ucz5cclxuICApOiBFdmVudCB7XHJcbiAgICBjb25zdCB0aXRsZSA9IGBUaHJlYXQgSW50ZWxsaWdlbmNlOiAke3RocmVhdERhdGEuaW5kaWNhdG9yIHx8ICdVbmtub3duIFRocmVhdCd9YDtcclxuICAgIGNvbnN0IHNldmVyaXR5ID0gRXZlbnRGYWN0b3J5Lm1hcFRocmVhdFNldmVyaXR5KHRocmVhdERhdGEuc2V2ZXJpdHkpO1xyXG4gICAgY29uc3Qgcmlza1Njb3JlID0gRXZlbnRGYWN0b3J5LmNhbGN1bGF0ZVRocmVhdFJpc2tTY29yZSh0aHJlYXREYXRhKTtcclxuXHJcbiAgICByZXR1cm4gRXZlbnRGYWN0b3J5LmNyZWF0ZSh7XHJcbiAgICAgIHR5cGU6IEV2ZW50VHlwZS5USFJFQVRfREVURUNURUQsXHJcbiAgICAgIHNldmVyaXR5LFxyXG4gICAgICB0aXRsZSxcclxuICAgICAgZGVzY3JpcHRpb246IHRocmVhdERhdGEuZGVzY3JpcHRpb24sXHJcbiAgICAgIHJhd0RhdGE6IHRocmVhdERhdGEsXHJcbiAgICAgIHNvdXJjZVR5cGU6IEV2ZW50U291cmNlVHlwZS5USFJFQVRfSU5URUxMSUdFTkNFLFxyXG4gICAgICBzb3VyY2VJZGVudGlmaWVyLFxyXG4gICAgICByaXNrU2NvcmUsXHJcbiAgICAgIHRhZ3M6IFsndGhyZWF0LWludGVsbGlnZW5jZScsICdleHRlcm5hbCddLFxyXG4gICAgICBhdHRyaWJ1dGVzOiB7XHJcbiAgICAgICAgaW5kaWNhdG9yOiB0aHJlYXREYXRhLmluZGljYXRvcixcclxuICAgICAgICBpbmRpY2F0b3JUeXBlOiB0aHJlYXREYXRhLmluZGljYXRvclR5cGUsXHJcbiAgICAgICAgY29uZmlkZW5jZTogdGhyZWF0RGF0YS5jb25maWRlbmNlLFxyXG4gICAgICAgIHNvdXJjZTogdGhyZWF0RGF0YS5zb3VyY2UsXHJcbiAgICAgIH0sXHJcbiAgICAgIC4uLm9wdGlvbnMsXHJcbiAgICB9KTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENyZWF0ZSBhbiBFdmVudCBmcm9tIHZ1bG5lcmFiaWxpdHkgc2NhbiByZXN1bHRzXHJcbiAgICovXHJcbiAgc3RhdGljIGZyb21WdWxuZXJhYmlsaXR5U2NhbihcclxuICAgIHZ1bG5EYXRhOiBSZWNvcmQ8c3RyaW5nLCBhbnk+LFxyXG4gICAgc291cmNlSWRlbnRpZmllcjogc3RyaW5nLFxyXG4gICAgb3B0aW9ucz86IFBhcnRpYWw8Q3JlYXRlRXZlbnRPcHRpb25zPlxyXG4gICk6IEV2ZW50IHtcclxuICAgIGNvbnN0IHRpdGxlID0gYFZ1bG5lcmFiaWxpdHk6ICR7dnVsbkRhdGEuY3ZlIHx8IHZ1bG5EYXRhLnRpdGxlIHx8ICdVbmtub3duIFZ1bG5lcmFiaWxpdHknfWA7XHJcbiAgICBjb25zdCBzZXZlcml0eSA9IEV2ZW50RmFjdG9yeS5tYXBWdWxuZXJhYmlsaXR5U2V2ZXJpdHkodnVsbkRhdGEuc2V2ZXJpdHkpO1xyXG4gICAgY29uc3Qgcmlza1Njb3JlID0gRXZlbnRGYWN0b3J5LmNhbGN1bGF0ZVZ1bG5lcmFiaWxpdHlSaXNrU2NvcmUodnVsbkRhdGEpO1xyXG5cclxuICAgIHJldHVybiBFdmVudEZhY3RvcnkuY3JlYXRlKHtcclxuICAgICAgdHlwZTogRXZlbnRUeXBlLlZVTE5FUkFCSUxJVFlfREVURUNURUQsXHJcbiAgICAgIHNldmVyaXR5LFxyXG4gICAgICB0aXRsZSxcclxuICAgICAgZGVzY3JpcHRpb246IHZ1bG5EYXRhLmRlc2NyaXB0aW9uLFxyXG4gICAgICByYXdEYXRhOiB2dWxuRGF0YSxcclxuICAgICAgc291cmNlVHlwZTogRXZlbnRTb3VyY2VUeXBlLlZVTE5FUkFCSUxJVFlfU0NBTk5FUixcclxuICAgICAgc291cmNlSWRlbnRpZmllcixcclxuICAgICAgcmlza1Njb3JlLFxyXG4gICAgICB0YWdzOiBbJ3Z1bG5lcmFiaWxpdHknLCAnc2Nhbi1yZXN1bHQnXSxcclxuICAgICAgYXR0cmlidXRlczoge1xyXG4gICAgICAgIGN2ZTogdnVsbkRhdGEuY3ZlLFxyXG4gICAgICAgIGN2c3NTY29yZTogdnVsbkRhdGEuY3Zzc1Njb3JlLFxyXG4gICAgICAgIGFmZmVjdGVkQXNzZXQ6IHZ1bG5EYXRhLmFmZmVjdGVkQXNzZXQsXHJcbiAgICAgICAgc29sdXRpb246IHZ1bG5EYXRhLnNvbHV0aW9uLFxyXG4gICAgICB9LFxyXG4gICAgICAuLi5vcHRpb25zLFxyXG4gICAgfSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDcmVhdGUgYSBtYW51YWwgRXZlbnQgKGNyZWF0ZWQgYnkgYW5hbHlzdClcclxuICAgKi9cclxuICBzdGF0aWMgY3JlYXRlTWFudWFsRXZlbnQoXHJcbiAgICB0eXBlOiBFdmVudFR5cGUsXHJcbiAgICBzZXZlcml0eTogRXZlbnRTZXZlcml0eSxcclxuICAgIHRpdGxlOiBzdHJpbmcsXHJcbiAgICBkZXNjcmlwdGlvbjogc3RyaW5nLFxyXG4gICAgY3JlYXRlZEJ5OiBzdHJpbmcsXHJcbiAgICByYXdEYXRhPzogUmVjb3JkPHN0cmluZywgYW55PixcclxuICAgIG9wdGlvbnM/OiBQYXJ0aWFsPENyZWF0ZUV2ZW50T3B0aW9ucz5cclxuICApOiBFdmVudCB7XHJcbiAgICByZXR1cm4gRXZlbnRGYWN0b3J5LmNyZWF0ZSh7XHJcbiAgICAgIHR5cGUsXHJcbiAgICAgIHNldmVyaXR5LFxyXG4gICAgICB0aXRsZSxcclxuICAgICAgZGVzY3JpcHRpb24sXHJcbiAgICAgIHJhd0RhdGE6IHJhd0RhdGEgfHwgeyBjcmVhdGVkQnksIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpIH0sXHJcbiAgICAgIHNvdXJjZVR5cGU6IEV2ZW50U291cmNlVHlwZS5NQU5VQUwsXHJcbiAgICAgIHNvdXJjZUlkZW50aWZpZXI6IGNyZWF0ZWRCeSxcclxuICAgICAgdGFnczogWydtYW51YWwnLCAnYW5hbHlzdC1jcmVhdGVkJ10sXHJcbiAgICAgIGF0dHJpYnV0ZXM6IHtcclxuICAgICAgICBjcmVhdGVkQnksXHJcbiAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIH0sXHJcbiAgICAgIC4uLm9wdGlvbnMsXHJcbiAgICB9KTtcclxuICB9XHJcblxyXG4gIC8vIEhlbHBlciBtZXRob2RzIGZvciBkYXRhIGV4dHJhY3Rpb24gYW5kIGluZmVyZW5jZVxyXG5cclxuICBwcml2YXRlIHN0YXRpYyBleHRyYWN0VGl0bGUobG9nRGF0YTogUmVjb3JkPHN0cmluZywgYW55Pik6IHN0cmluZyB7XHJcbiAgICAvLyBUcnkgY29tbW9uIHRpdGxlIGZpZWxkc1xyXG4gICAgY29uc3QgdGl0bGVGaWVsZHMgPSBbJ3RpdGxlJywgJ21lc3NhZ2UnLCAnc3VtbWFyeScsICdldmVudF9uYW1lJywgJ2FsZXJ0X25hbWUnXTtcclxuICAgIFxyXG4gICAgZm9yIChjb25zdCBmaWVsZCBvZiB0aXRsZUZpZWxkcykge1xyXG4gICAgICBpZiAobG9nRGF0YVtmaWVsZF0gJiYgdHlwZW9mIGxvZ0RhdGFbZmllbGRdID09PSAnc3RyaW5nJykge1xyXG4gICAgICAgIHJldHVybiBsb2dEYXRhW2ZpZWxkXS5zdWJzdHJpbmcoMCwgMjAwKTsgLy8gVHJ1bmNhdGUgdG8gbWF4IGxlbmd0aFxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRmFsbGJhY2sgdG8gZ2VuZXJpYyB0aXRsZVxyXG4gICAgcmV0dXJuICdTZWN1cml0eSBFdmVudCc7XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIHN0YXRpYyBleHRyYWN0RGVzY3JpcHRpb24obG9nRGF0YTogUmVjb3JkPHN0cmluZywgYW55Pik6IHN0cmluZyB8IHVuZGVmaW5lZCB7XHJcbiAgICBjb25zdCBkZXNjRmllbGRzID0gWydkZXNjcmlwdGlvbicsICdkZXRhaWxzJywgJ2Z1bGxfbWVzc2FnZScsICdldmVudF9kZXNjcmlwdGlvbiddO1xyXG4gICAgXHJcbiAgICBmb3IgKGNvbnN0IGZpZWxkIG9mIGRlc2NGaWVsZHMpIHtcclxuICAgICAgaWYgKGxvZ0RhdGFbZmllbGRdICYmIHR5cGVvZiBsb2dEYXRhW2ZpZWxkXSA9PT0gJ3N0cmluZycpIHtcclxuICAgICAgICByZXR1cm4gbG9nRGF0YVtmaWVsZF0uc3Vic3RyaW5nKDAsIDIwMDApOyAvLyBUcnVuY2F0ZSB0byBtYXggbGVuZ3RoXHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gdW5kZWZpbmVkO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBzdGF0aWMgZXh0cmFjdFRpbWVzdGFtcChsb2dEYXRhOiBSZWNvcmQ8c3RyaW5nLCBhbnk+KTogRGF0ZSB8IHVuZGVmaW5lZCB7XHJcbiAgICBjb25zdCB0aW1lc3RhbXBGaWVsZHMgPSBbJ3RpbWVzdGFtcCcsICdAdGltZXN0YW1wJywgJ2V2ZW50X3RpbWUnLCAnY3JlYXRlZF9hdCcsICdvY2N1cnJlZF9hdCddO1xyXG4gICAgXHJcbiAgICBmb3IgKGNvbnN0IGZpZWxkIG9mIHRpbWVzdGFtcEZpZWxkcykge1xyXG4gICAgICBpZiAobG9nRGF0YVtmaWVsZF0pIHtcclxuICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUobG9nRGF0YVtmaWVsZF0pO1xyXG4gICAgICAgIGlmICghaXNOYU4oZGF0ZS5nZXRUaW1lKCkpKSB7XHJcbiAgICAgICAgICByZXR1cm4gZGF0ZTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gdW5kZWZpbmVkO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBzdGF0aWMgZXh0cmFjdFRhZ3MobG9nRGF0YTogUmVjb3JkPHN0cmluZywgYW55Pik6IHN0cmluZ1tdIHwgdW5kZWZpbmVkIHtcclxuICAgIGNvbnN0IHRhZ0ZpZWxkcyA9IFsndGFncycsICdsYWJlbHMnLCAnY2F0ZWdvcmllcyddO1xyXG4gICAgXHJcbiAgICBmb3IgKGNvbnN0IGZpZWxkIG9mIHRhZ0ZpZWxkcykge1xyXG4gICAgICBpZiAoQXJyYXkuaXNBcnJheShsb2dEYXRhW2ZpZWxkXSkpIHtcclxuICAgICAgICByZXR1cm4gbG9nRGF0YVtmaWVsZF0uZmlsdGVyKHRhZyA9PiB0eXBlb2YgdGFnID09PSAnc3RyaW5nJykuc2xpY2UoMCwgMjApO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHVuZGVmaW5lZDtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgc3RhdGljIGluZmVyU2V2ZXJpdHkobG9nRGF0YTogUmVjb3JkPHN0cmluZywgYW55Pik6IEV2ZW50U2V2ZXJpdHkge1xyXG4gICAgY29uc3Qgc2V2ZXJpdHlGaWVsZHMgPSBbJ3NldmVyaXR5JywgJ2xldmVsJywgJ3ByaW9yaXR5JywgJ2NyaXRpY2FsaXR5J107XHJcbiAgICBcclxuICAgIGZvciAoY29uc3QgZmllbGQgb2Ygc2V2ZXJpdHlGaWVsZHMpIHtcclxuICAgICAgaWYgKGxvZ0RhdGFbZmllbGRdKSB7XHJcbiAgICAgICAgY29uc3Qgc2V2ZXJpdHkgPSBTdHJpbmcobG9nRGF0YVtmaWVsZF0pLnRvTG93ZXJDYXNlKCk7XHJcbiAgICAgICAgXHJcbiAgICAgICAgaWYgKHNldmVyaXR5LmluY2x1ZGVzKCdjcml0aWNhbCcpIHx8IHNldmVyaXR5LmluY2x1ZGVzKCdmYXRhbCcpKSB7XHJcbiAgICAgICAgICByZXR1cm4gRXZlbnRTZXZlcml0eS5DUklUSUNBTDtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHNldmVyaXR5LmluY2x1ZGVzKCdoaWdoJykgfHwgc2V2ZXJpdHkuaW5jbHVkZXMoJ2Vycm9yJykpIHtcclxuICAgICAgICAgIHJldHVybiBFdmVudFNldmVyaXR5LkhJR0g7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChzZXZlcml0eS5pbmNsdWRlcygnbWVkaXVtJykgfHwgc2V2ZXJpdHkuaW5jbHVkZXMoJ3dhcm4nKSkge1xyXG4gICAgICAgICAgcmV0dXJuIEV2ZW50U2V2ZXJpdHkuTUVESVVNO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoc2V2ZXJpdHkuaW5jbHVkZXMoJ2xvdycpIHx8IHNldmVyaXR5LmluY2x1ZGVzKCdpbmZvJykpIHtcclxuICAgICAgICAgIHJldHVybiBFdmVudFNldmVyaXR5LkxPVztcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBEZWZhdWx0IHRvIG1lZGl1bSBzZXZlcml0eVxyXG4gICAgcmV0dXJuIEV2ZW50U2V2ZXJpdHkuTUVESVVNO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBzdGF0aWMgaW5mZXJFdmVudFR5cGUobG9nRGF0YTogUmVjb3JkPHN0cmluZywgYW55Pik6IEV2ZW50VHlwZSB7XHJcbiAgICBjb25zdCBtZXNzYWdlID0gSlNPTi5zdHJpbmdpZnkobG9nRGF0YSkudG9Mb3dlckNhc2UoKTtcclxuICAgIFxyXG4gICAgLy8gQXV0aGVudGljYXRpb24gZXZlbnRzXHJcbiAgICBpZiAobWVzc2FnZS5pbmNsdWRlcygnbG9naW4nKSB8fCBtZXNzYWdlLmluY2x1ZGVzKCdhdXRoZW50aWNhdGlvbicpKSB7XHJcbiAgICAgIGlmIChtZXNzYWdlLmluY2x1ZGVzKCdmYWlsZWQnKSB8fCBtZXNzYWdlLmluY2x1ZGVzKCdmYWlsdXJlJykpIHtcclxuICAgICAgICByZXR1cm4gRXZlbnRUeXBlLkxPR0lOX0ZBSUxVUkU7XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuIEV2ZW50VHlwZS5MT0dJTl9TVUNDRVNTO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBOZXR3b3JrIGV2ZW50c1xyXG4gICAgaWYgKG1lc3NhZ2UuaW5jbHVkZXMoJ2Nvbm5lY3Rpb24nKSB8fCBtZXNzYWdlLmluY2x1ZGVzKCduZXR3b3JrJykpIHtcclxuICAgICAgcmV0dXJuIEV2ZW50VHlwZS5DT05ORUNUSU9OX0VTVEFCTElTSEVEO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBNYWx3YXJlIGV2ZW50c1xyXG4gICAgaWYgKG1lc3NhZ2UuaW5jbHVkZXMoJ21hbHdhcmUnKSB8fCBtZXNzYWdlLmluY2x1ZGVzKCd2aXJ1cycpKSB7XHJcbiAgICAgIHJldHVybiBFdmVudFR5cGUuTUFMV0FSRV9ERVRFQ1RFRDtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gVnVsbmVyYWJpbGl0eSBldmVudHNcclxuICAgIGlmIChtZXNzYWdlLmluY2x1ZGVzKCd2dWxuZXJhYmlsaXR5JykgfHwgbWVzc2FnZS5pbmNsdWRlcygnY3ZlJykpIHtcclxuICAgICAgcmV0dXJuIEV2ZW50VHlwZS5WVUxORVJBQklMSVRZX0RFVEVDVEVEO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBUaHJlYXQgZXZlbnRzXHJcbiAgICBpZiAobWVzc2FnZS5pbmNsdWRlcygndGhyZWF0JykgfHwgbWVzc2FnZS5pbmNsdWRlcygnYXR0YWNrJykpIHtcclxuICAgICAgcmV0dXJuIEV2ZW50VHlwZS5USFJFQVRfREVURUNURUQ7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIERlZmF1bHQgdG8gY3VzdG9tIGV2ZW50XHJcbiAgICByZXR1cm4gRXZlbnRUeXBlLkNVU1RPTTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgc3RhdGljIG1hcFRocmVhdFNldmVyaXR5KHNldmVyaXR5OiBzdHJpbmcgfCBudW1iZXIpOiBFdmVudFNldmVyaXR5IHtcclxuICAgIGlmICh0eXBlb2Ygc2V2ZXJpdHkgPT09ICdzdHJpbmcnKSB7XHJcbiAgICAgIGNvbnN0IHNldiA9IHNldmVyaXR5LnRvTG93ZXJDYXNlKCk7XHJcbiAgICAgIGlmIChzZXYuaW5jbHVkZXMoJ2NyaXRpY2FsJykgfHwgc2V2LmluY2x1ZGVzKCdoaWdoJykpIHJldHVybiBFdmVudFNldmVyaXR5LkNSSVRJQ0FMO1xyXG4gICAgICBpZiAoc2V2LmluY2x1ZGVzKCdtZWRpdW0nKSkgcmV0dXJuIEV2ZW50U2V2ZXJpdHkuTUVESVVNO1xyXG4gICAgICBpZiAoc2V2LmluY2x1ZGVzKCdsb3cnKSkgcmV0dXJuIEV2ZW50U2V2ZXJpdHkuTE9XO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAodHlwZW9mIHNldmVyaXR5ID09PSAnbnVtYmVyJykge1xyXG4gICAgICBpZiAoc2V2ZXJpdHkgPj0gOCkgcmV0dXJuIEV2ZW50U2V2ZXJpdHkuQ1JJVElDQUw7XHJcbiAgICAgIGlmIChzZXZlcml0eSA+PSA2KSByZXR1cm4gRXZlbnRTZXZlcml0eS5ISUdIO1xyXG4gICAgICBpZiAoc2V2ZXJpdHkgPj0gNCkgcmV0dXJuIEV2ZW50U2V2ZXJpdHkuTUVESVVNO1xyXG4gICAgICByZXR1cm4gRXZlbnRTZXZlcml0eS5MT1c7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBFdmVudFNldmVyaXR5Lk1FRElVTTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgc3RhdGljIG1hcFZ1bG5lcmFiaWxpdHlTZXZlcml0eShzZXZlcml0eTogc3RyaW5nIHwgbnVtYmVyKTogRXZlbnRTZXZlcml0eSB7XHJcbiAgICBpZiAodHlwZW9mIHNldmVyaXR5ID09PSAnc3RyaW5nJykge1xyXG4gICAgICBjb25zdCBzZXYgPSBzZXZlcml0eS50b0xvd2VyQ2FzZSgpO1xyXG4gICAgICBpZiAoc2V2LmluY2x1ZGVzKCdjcml0aWNhbCcpKSByZXR1cm4gRXZlbnRTZXZlcml0eS5DUklUSUNBTDtcclxuICAgICAgaWYgKHNldi5pbmNsdWRlcygnaGlnaCcpKSByZXR1cm4gRXZlbnRTZXZlcml0eS5ISUdIO1xyXG4gICAgICBpZiAoc2V2LmluY2x1ZGVzKCdtZWRpdW0nKSkgcmV0dXJuIEV2ZW50U2V2ZXJpdHkuTUVESVVNO1xyXG4gICAgICBpZiAoc2V2LmluY2x1ZGVzKCdsb3cnKSkgcmV0dXJuIEV2ZW50U2V2ZXJpdHkuTE9XO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAodHlwZW9mIHNldmVyaXR5ID09PSAnbnVtYmVyJykge1xyXG4gICAgICBpZiAoc2V2ZXJpdHkgPj0gOSkgcmV0dXJuIEV2ZW50U2V2ZXJpdHkuQ1JJVElDQUw7XHJcbiAgICAgIGlmIChzZXZlcml0eSA+PSA3KSByZXR1cm4gRXZlbnRTZXZlcml0eS5ISUdIO1xyXG4gICAgICBpZiAoc2V2ZXJpdHkgPj0gNCkgcmV0dXJuIEV2ZW50U2V2ZXJpdHkuTUVESVVNO1xyXG4gICAgICByZXR1cm4gRXZlbnRTZXZlcml0eS5MT1c7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBFdmVudFNldmVyaXR5Lk1FRElVTTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgc3RhdGljIGNhbGN1bGF0ZVRocmVhdFJpc2tTY29yZSh0aHJlYXREYXRhOiBSZWNvcmQ8c3RyaW5nLCBhbnk+KTogbnVtYmVyIHtcclxuICAgIGxldCBzY29yZSA9IDUwOyAvLyBCYXNlIHNjb3JlXHJcbiAgICBcclxuICAgIC8vIEFkanVzdCBiYXNlZCBvbiBjb25maWRlbmNlXHJcbiAgICBpZiAodGhyZWF0RGF0YS5jb25maWRlbmNlKSB7XHJcbiAgICAgIHNjb3JlICs9IE1hdGguZmxvb3IodGhyZWF0RGF0YS5jb25maWRlbmNlICogMC4zKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gQWRqdXN0IGJhc2VkIG9uIHNldmVyaXR5XHJcbiAgICBpZiAodGhyZWF0RGF0YS5zZXZlcml0eSkge1xyXG4gICAgICBjb25zdCBzZXZlcml0eSA9IHR5cGVvZiB0aHJlYXREYXRhLnNldmVyaXR5ID09PSAnbnVtYmVyJyBcclxuICAgICAgICA/IHRocmVhdERhdGEuc2V2ZXJpdHkgXHJcbiAgICAgICAgOiBFdmVudEZhY3Rvcnkuc2V2ZXJpdHlUb051bWJlcih0aHJlYXREYXRhLnNldmVyaXR5KTtcclxuICAgICAgc2NvcmUgKz0gc2V2ZXJpdHkgKiA1O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBBZGp1c3QgYmFzZWQgb24gaW5kaWNhdG9yIHR5cGVcclxuICAgIGlmICh0aHJlYXREYXRhLmluZGljYXRvclR5cGUgPT09ICdpcCcgfHwgdGhyZWF0RGF0YS5pbmRpY2F0b3JUeXBlID09PSAnZG9tYWluJykge1xyXG4gICAgICBzY29yZSArPSAxMDsgLy8gTmV0d29yayBpbmRpY2F0b3JzIGFyZSBoaWdoZXIgcmlza1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICByZXR1cm4gTWF0aC5taW4oMTAwLCBNYXRoLm1heCgwLCBzY29yZSkpO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBzdGF0aWMgY2FsY3VsYXRlVnVsbmVyYWJpbGl0eVJpc2tTY29yZSh2dWxuRGF0YTogUmVjb3JkPHN0cmluZywgYW55Pik6IG51bWJlciB7XHJcbiAgICBsZXQgc2NvcmUgPSAzMDsgLy8gQmFzZSBzY29yZVxyXG4gICAgXHJcbiAgICAvLyBBZGp1c3QgYmFzZWQgb24gQ1ZTUyBzY29yZVxyXG4gICAgaWYgKHZ1bG5EYXRhLmN2c3NTY29yZSkge1xyXG4gICAgICBzY29yZSArPSB2dWxuRGF0YS5jdnNzU2NvcmUgKiA3OyAvLyBDVlNTIDAtMTAgc2NhbGVcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gQWRqdXN0IGJhc2VkIG9uIGV4cGxvaXRhYmlsaXR5XHJcbiAgICBpZiAodnVsbkRhdGEuZXhwbG9pdGFibGUpIHtcclxuICAgICAgc2NvcmUgKz0gMjA7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIEFkanVzdCBiYXNlZCBvbiBhc3NldCBjcml0aWNhbGl0eVxyXG4gICAgaWYgKHZ1bG5EYXRhLmFzc2V0Q3JpdGljYWxpdHkgPT09ICdoaWdoJykge1xyXG4gICAgICBzY29yZSArPSAxNTtcclxuICAgIH0gZWxzZSBpZiAodnVsbkRhdGEuYXNzZXRDcml0aWNhbGl0eSA9PT0gJ21lZGl1bScpIHtcclxuICAgICAgc2NvcmUgKz0gMTA7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBNYXRoLm1pbigxMDAsIE1hdGgubWF4KDAsIHNjb3JlKSk7XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIHN0YXRpYyBzZXZlcml0eVRvTnVtYmVyKHNldmVyaXR5OiBzdHJpbmcpOiBudW1iZXIge1xyXG4gICAgY29uc3Qgc2V2ID0gc2V2ZXJpdHkudG9Mb3dlckNhc2UoKTtcclxuICAgIGlmIChzZXYuaW5jbHVkZXMoJ2NyaXRpY2FsJykpIHJldHVybiAxMDtcclxuICAgIGlmIChzZXYuaW5jbHVkZXMoJ2hpZ2gnKSkgcmV0dXJuIDg7XHJcbiAgICBpZiAoc2V2LmluY2x1ZGVzKCdtZWRpdW0nKSkgcmV0dXJuIDY7XHJcbiAgICBpZiAoc2V2LmluY2x1ZGVzKCdsb3cnKSkgcmV0dXJuIDQ7XHJcbiAgICByZXR1cm4gNTtcclxuICB9XHJcbn0iXSwidmVyc2lvbiI6M30=