{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\encryption\\rsa.encryption.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAC/C,+CAAiC;AAc1B,IAAM,aAAa,GAAnB,MAAM,aAAa;IAKxB,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAJxC,YAAO,GAAG,IAAI,CAAC;QACf,mBAAc,GAAG,MAAM,CAAC,SAAS,CAAC,sBAAsB,CAAC;QACzD,oBAAe,GAAG,QAAQ,CAAC;IAEgB,CAAC;IAE7D;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,UAAkB,IAAI,CAAC,OAAO;QAClD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,CAAC,eAAe,CACpB,KAAK,EACL;gBACE,aAAa,EAAE,OAAO;gBACtB,iBAAiB,EAAE;oBACjB,IAAI,EAAE,MAAM;oBACZ,MAAM,EAAE,KAAK;iBACd;gBACD,kBAAkB,EAAE;oBAClB,IAAI,EAAE,OAAO;oBACb,MAAM,EAAE,KAAK;iBACd;aACF,EACD,CAAC,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,EAAE;gBAC7B,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,IAAI,KAAK,CAAC,oCAAoC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBACvE,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACX,IAAY,EACZ,SAAiB,EACjB,UAAgC,EAAE;QAElC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,CAAC,aAAa,CACpC;gBACE,GAAG,EAAE,SAAS;gBACd,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc;gBAC/C,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe;aACnD,EACD,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAC1B,CAAC;YAEF,OAAO,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACX,aAAqB,EACrB,UAAkB,EAClB,UAAgC,EAAE;QAElC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,CAAC,cAAc,CACrC;gBACE,GAAG,EAAE,UAAU;gBACf,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc;gBAC/C,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe;aACnD,EACD,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CACrC,CAAC;YAEF,OAAO,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI,CAAC,IAAY,EAAE,UAAkB,EAAE,YAAoB,QAAQ;QACvE,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YACjE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAClB,IAAI,CAAC,GAAG,EAAE,CAAC;YAEX,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAClD,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACV,IAAY,EACZ,SAAiB,EACjB,SAAiB,EACjB,YAAoB,QAAQ;QAE5B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YACrE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACpB,MAAM,CAAC,GAAG,EAAE,CAAC;YAEb,OAAO,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,UAAkB;QACjC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACtD,MAAM,SAAS,GAAG,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAEpD,OAAO,SAAS,CAAC,MAAM,CAAC;gBACtB,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,KAAK;aACd,CAAW,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAkB;QACnC,IAAI,CAAC;YACH,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,SAAiB;QACjC,IAAI,CAAC;YACH,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,GAAW;QACpB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAC9C,OAAO,SAAS,CAAC,iBAAkB,GAAG,CAAC,CAAC,CAAC,wBAAwB;QACnE,CAAC;QAAC,MAAM,CAAC;YACP,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAC/C,OAAO,SAAS,CAAC,iBAAkB,GAAG,CAAC,CAAC,CAAC,wBAAwB;YACnE,CAAC;YAAC,MAAM,CAAC;gBACP,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,IAAY,EAAE,SAAiB;QACpD,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,2BAA2B;QAE9E,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,YAAY,EAAE,CAAC;YACnD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC;YAC9C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,eAAyB,EAAE,UAAkB;QAClE,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;YACpC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YAC7D,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;CACF,CAAA;AAhNY,sCAAa;wBAAb,aAAa;IADzB,IAAA,mBAAU,GAAE;yDAMiC,sBAAa,oBAAb,sBAAa;GAL9C,aAAa,CAgNzB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\encryption\\rsa.encryption.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport * as crypto from 'crypto';\r\n\r\nexport interface RSAKeyPair {\r\n  publicKey: string;\r\n  privateKey: string;\r\n}\r\n\r\nexport interface RSAEncryptionOptions {\r\n  padding?: number;\r\n  oaepHash?: string;\r\n  mgf?: number;\r\n}\r\n\r\n@Injectable()\r\nexport class RSAEncryption {\r\n  private readonly keySize = 4096;\r\n  private readonly defaultPadding = crypto.constants.RSA_PKCS1_OAEP_PADDING;\r\n  private readonly defaultOaepHash = 'sha256';\r\n\r\n  constructor(private readonly configService: ConfigService) {}\r\n\r\n  /**\r\n   * Generate RSA key pair\r\n   */\r\n  async generateKeyPair(keySize: number = this.keySize): Promise<RSAKeyPair> {\r\n    return new Promise((resolve, reject) => {\r\n      crypto.generateKeyPair(\r\n        'rsa',\r\n        {\r\n          modulusLength: keySize,\r\n          publicKeyEncoding: {\r\n            type: 'spki',\r\n            format: 'pem',\r\n          },\r\n          privateKeyEncoding: {\r\n            type: 'pkcs8',\r\n            format: 'pem',\r\n          },\r\n        },\r\n        (err, publicKey, privateKey) => {\r\n          if (err) {\r\n            reject(new Error(`Failed to generate RSA key pair: ${err.message}`));\r\n          } else {\r\n            resolve({ publicKey, privateKey });\r\n          }\r\n        },\r\n      );\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Encrypt data using RSA public key\r\n   */\r\n  async encrypt(\r\n    data: string, \r\n    publicKey: string, \r\n    options: RSAEncryptionOptions = {}\r\n  ): Promise<string> {\r\n    try {\r\n      const encrypted = crypto.publicEncrypt(\r\n        {\r\n          key: publicKey,\r\n          padding: options.padding || this.defaultPadding,\r\n          oaepHash: options.oaepHash || this.defaultOaepHash,\r\n        },\r\n        Buffer.from(data, 'utf8'),\r\n      );\r\n\r\n      return encrypted.toString('base64');\r\n    } catch (error) {\r\n      throw new Error(`RSA encryption failed: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Decrypt data using RSA private key\r\n   */\r\n  async decrypt(\r\n    encryptedData: string, \r\n    privateKey: string, \r\n    options: RSAEncryptionOptions = {}\r\n  ): Promise<string> {\r\n    try {\r\n      const decrypted = crypto.privateDecrypt(\r\n        {\r\n          key: privateKey,\r\n          padding: options.padding || this.defaultPadding,\r\n          oaepHash: options.oaepHash || this.defaultOaepHash,\r\n        },\r\n        Buffer.from(encryptedData, 'base64'),\r\n      );\r\n\r\n      return decrypted.toString('utf8');\r\n    } catch (error) {\r\n      throw new Error(`RSA decryption failed: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sign data using RSA private key\r\n   */\r\n  async sign(data: string, privateKey: string, algorithm: string = 'sha256'): Promise<string> {\r\n    try {\r\n      const sign = crypto.createSign(`RSA-${algorithm.toUpperCase()}`);\r\n      sign.update(data);\r\n      sign.end();\r\n\r\n      const signature = sign.sign(privateKey, 'base64');\r\n      return signature;\r\n    } catch (error) {\r\n      throw new Error(`RSA signing failed: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Verify signature using RSA public key\r\n   */\r\n  async verify(\r\n    data: string, \r\n    signature: string, \r\n    publicKey: string, \r\n    algorithm: string = 'sha256'\r\n  ): Promise<boolean> {\r\n    try {\r\n      const verify = crypto.createVerify(`RSA-${algorithm.toUpperCase()}`);\r\n      verify.update(data);\r\n      verify.end();\r\n\r\n      return verify.verify(publicKey, signature, 'base64');\r\n    } catch (error) {\r\n      throw new Error(`RSA verification failed: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Extract public key from private key\r\n   */\r\n  extractPublicKey(privateKey: string): string {\r\n    try {\r\n      const keyObject = crypto.createPrivateKey(privateKey);\r\n      const publicKey = crypto.createPublicKey(keyObject);\r\n      \r\n      return publicKey.export({\r\n        type: 'spki',\r\n        format: 'pem',\r\n      }) as string;\r\n    } catch (error) {\r\n      throw new Error(`Failed to extract public key: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate RSA key format\r\n   */\r\n  validatePrivateKey(privateKey: string): boolean {\r\n    try {\r\n      crypto.createPrivateKey(privateKey);\r\n      return true;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate RSA public key format\r\n   */\r\n  validatePublicKey(publicKey: string): boolean {\r\n    try {\r\n      crypto.createPublicKey(publicKey);\r\n      return true;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get key size from RSA key\r\n   */\r\n  getKeySize(key: string): number {\r\n    try {\r\n      const keyObject = crypto.createPublicKey(key);\r\n      return keyObject.asymmetricKeySize! * 8; // Convert bytes to bits\r\n    } catch {\r\n      try {\r\n        const keyObject = crypto.createPrivateKey(key);\r\n        return keyObject.asymmetricKeySize! * 8; // Convert bytes to bits\r\n      } catch {\r\n        throw new Error('Invalid RSA key format');\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Encrypt large data by chunking (RSA has size limitations)\r\n   */\r\n  async encryptLargeData(data: string, publicKey: string): Promise<string[]> {\r\n    const keySize = this.getKeySize(publicKey);\r\n    const maxChunkSize = Math.floor(keySize / 8) - 42; // Account for OAEP padding\r\n    \r\n    const chunks: string[] = [];\r\n    for (let i = 0; i < data.length; i += maxChunkSize) {\r\n      const chunk = data.slice(i, i + maxChunkSize);\r\n      const encryptedChunk = await this.encrypt(chunk, publicKey);\r\n      chunks.push(encryptedChunk);\r\n    }\r\n    \r\n    return chunks;\r\n  }\r\n\r\n  /**\r\n   * Decrypt large data from chunks\r\n   */\r\n  async decryptLargeData(encryptedChunks: string[], privateKey: string): Promise<string> {\r\n    const decryptedChunks: string[] = [];\r\n    \r\n    for (const chunk of encryptedChunks) {\r\n      const decryptedChunk = await this.decrypt(chunk, privateKey);\r\n      decryptedChunks.push(decryptedChunk);\r\n    }\r\n    \r\n    return decryptedChunks.join('');\r\n  }\r\n}"], "version": 3}