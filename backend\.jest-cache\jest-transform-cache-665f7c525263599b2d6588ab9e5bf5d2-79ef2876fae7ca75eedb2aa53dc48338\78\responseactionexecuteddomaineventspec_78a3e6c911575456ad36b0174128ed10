cbb5704240143680b1883ed77544ad04
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const response_action_executed_domain_event_1 = require("../response-action-executed.domain-event");
const shared_kernel_1 = require("../../../../../shared-kernel");
const action_type_enum_1 = require("../../enums/action-type.enum");
describe('ResponseActionExecutedDomainEvent', () => {
    let aggregateId;
    let eventData;
    let event;
    beforeEach(() => {
        aggregateId = shared_kernel_1.UniqueEntityId.generate();
        eventData = {
            actionType: action_type_enum_1.ActionType.BLOCK_IP,
            executedBy: 'system@automation',
            executedAt: new Date(),
            executionResults: {
                blocked: true,
                ruleId: 'rule-123',
                affectedConnections: 5,
            },
            successCriteriaMet: true,
            actualDurationMinutes: 2,
        };
        event = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, eventData);
    });
    describe('creation', () => {
        it('should create event with required data', () => {
            expect(event).toBeInstanceOf(response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent);
            expect(event.aggregateId).toBe(aggregateId);
            expect(event.eventData).toBe(eventData);
            expect(event.occurredOn).toBeInstanceOf(Date);
        });
        it('should create event with custom options', () => {
            const customOptions = {
                eventId: shared_kernel_1.UniqueEntityId.generate(),
                occurredOn: new Date('2023-01-01'),
                eventVersion: 2,
                correlationId: 'corr-123',
                causationId: 'cause-456',
                metadata: { source: 'test' },
            };
            const customEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, eventData, customOptions);
            expect(customEvent.eventId).toBe(customOptions.eventId);
            expect(customEvent.occurredOn).toBe(customOptions.occurredOn);
            expect(customEvent.eventVersion).toBe(customOptions.eventVersion);
            expect(customEvent.correlationId).toBe(customOptions.correlationId);
            expect(customEvent.causationId).toBe(customOptions.causationId);
            expect(customEvent.metadata).toEqual(customOptions.metadata);
        });
    });
    describe('getters', () => {
        it('should provide access to event data properties', () => {
            expect(event.actionType).toBe(action_type_enum_1.ActionType.BLOCK_IP);
            expect(event.executedBy).toBe('system@automation');
            expect(event.executedAt).toBe(eventData.executedAt);
            expect(event.executionResults).toEqual(eventData.executionResults);
            expect(event.successCriteriaMet).toBe(true);
            expect(event.actualDurationMinutes).toBe(2);
        });
    });
    describe('execution status analysis', () => {
        it('should identify successful executions', () => {
            expect(event.isSuccessful()).toBe(true);
            expect(event.isFailed()).toBe(false);
            expect(event.isPartial()).toBe(false);
        });
        it('should identify failed executions', () => {
            const failedEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                successCriteriaMet: false,
            });
            expect(failedEvent.isSuccessful()).toBe(false);
            expect(failedEvent.isFailed()).toBe(true);
            expect(failedEvent.isPartial()).toBe(false);
        });
        it('should identify partial executions', () => {
            const partialEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                successCriteriaMet: undefined,
                executionResults: { partial: true },
            });
            expect(partialEvent.isSuccessful()).toBe(false);
            expect(partialEvent.isFailed()).toBe(false);
            expect(partialEvent.isPartial()).toBe(true);
        });
    });
    describe('execution type analysis', () => {
        it('should identify automated executions', () => {
            expect(event.isAutomatedExecution()).toBe(true);
            expect(event.isManualExecution()).toBe(false);
            const systemEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                executedBy: 'automation-bot',
            });
            expect(systemEvent.isAutomatedExecution()).toBe(true);
            const botEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                executedBy: 'security-bot',
            });
            expect(botEvent.isAutomatedExecution()).toBe(true);
        });
        it('should identify manual executions', () => {
            const manualEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                executedBy: '<EMAIL>',
            });
            expect(manualEvent.isAutomatedExecution()).toBe(false);
            expect(manualEvent.isManualExecution()).toBe(true);
        });
    });
    describe('action type classification', () => {
        it('should identify security-critical actions', () => {
            expect(event.isSecurityCritical()).toBe(true);
            const isolateEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.ISOLATE_SYSTEM,
            });
            expect(isolateEvent.isSecurityCritical()).toBe(true);
            const shutdownEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SHUTDOWN_SYSTEM,
            });
            expect(shutdownEvent.isSecurityCritical()).toBe(true);
            const deleteEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.DELETE_FILE,
            });
            expect(deleteEvent.isSecurityCritical()).toBe(true);
            const emailEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(emailEvent.isSecurityCritical()).toBe(false);
        });
        it('should identify containment actions', () => {
            expect(event.isContainmentAction()).toBe(true);
            const quarantineEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
            });
            expect(quarantineEvent.isContainmentAction()).toBe(true);
            const blockDomainEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.BLOCK_DOMAIN,
            });
            expect(blockDomainEvent.isContainmentAction()).toBe(true);
            const disableAccountEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.DISABLE_ACCOUNT,
            });
            expect(disableAccountEvent.isContainmentAction()).toBe(true);
            const emailEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(emailEvent.isContainmentAction()).toBe(false);
        });
        it('should identify recovery actions', () => {
            const backupEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            expect(backupEvent.isRecoveryAction()).toBe(true);
            const rebuildEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.REBUILD_SYSTEM,
            });
            expect(rebuildEvent.isRecoveryAction()).toBe(true);
            const enableEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.ENABLE_SERVICE,
            });
            expect(enableEvent.isRecoveryAction()).toBe(true);
            expect(event.isRecoveryAction()).toBe(false);
        });
    });
    describe('performance analysis', () => {
        it('should identify fast executions', () => {
            const fastEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actualDurationMinutes: 3,
            });
            expect(fastEvent.isFastExecution()).toBe(true);
            expect(event.isFastExecution()).toBe(true); // 2 minutes
        });
        it('should identify slow executions', () => {
            const slowEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actualDurationMinutes: 45,
            });
            expect(slowEvent.isSlowExecution()).toBe(true);
            expect(event.isSlowExecution()).toBe(false);
        });
        it('should categorize performance correctly', () => {
            expect(event.getPerformanceCategory()).toBe('fast'); // 2 minutes
            const normalEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actualDurationMinutes: 15,
            });
            expect(normalEvent.getPerformanceCategory()).toBe('normal');
            const slowEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actualDurationMinutes: 45,
            });
            expect(slowEvent.getPerformanceCategory()).toBe('slow');
            const verySlowEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actualDurationMinutes: 150,
            });
            expect(verySlowEvent.getPerformanceCategory()).toBe('very_slow');
        });
        it('should handle missing duration', () => {
            const noDurationEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actualDurationMinutes: undefined,
            });
            expect(noDurationEvent.isFastExecution()).toBe(true); // 0 < 5
            expect(noDurationEvent.isSlowExecution()).toBe(false); // 0 < 30
            expect(noDurationEvent.getPerformanceCategory()).toBe('fast');
        });
    });
    describe('impact assessment', () => {
        it('should assess impact for successful security-critical actions', () => {
            expect(event.getImpactLevel()).toBe('high');
        });
        it('should assess impact for failed security-critical actions', () => {
            const failedCriticalEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                successCriteriaMet: false,
            });
            expect(failedCriticalEvent.getImpactLevel()).toBe('critical');
        });
        it('should assess impact for successful containment actions', () => {
            const containmentEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
            });
            expect(containmentEvent.getImpactLevel()).toBe('high');
        });
        it('should assess impact for failed containment actions', () => {
            const failedContainmentEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
                successCriteriaMet: false,
            });
            expect(failedContainmentEvent.getImpactLevel()).toBe('critical');
        });
        it('should assess impact for successful recovery actions', () => {
            const recoveryEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            expect(recoveryEvent.getImpactLevel()).toBe('medium');
        });
        it('should assess impact for failed recovery actions', () => {
            const failedRecoveryEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
                successCriteriaMet: false,
            });
            expect(failedRecoveryEvent.getImpactLevel()).toBe('high');
        });
        it('should assess impact for low-impact actions', () => {
            const emailEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(emailEvent.getImpactLevel()).toBe('low');
        });
    });
    describe('post-execution recommendations', () => {
        it('should recommend post-actions for successful executions', () => {
            const actions = event.getRecommendedPostActions();
            expect(actions).toContain('Validate action effectiveness');
            expect(actions).toContain('Update related security status');
        });
        it('should recommend post-actions for successful containment', () => {
            const actions = event.getRecommendedPostActions();
            expect(actions).toContain('Monitor for containment bypass');
            expect(actions).toContain('Assess containment completeness');
        });
        it('should recommend post-actions for successful recovery', () => {
            const recoveryEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            const actions = recoveryEvent.getRecommendedPostActions();
            expect(actions).toContain('Verify system functionality');
            expect(actions).toContain('Test recovered services');
        });
        it('should recommend post-actions for failed executions', () => {
            const failedEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                successCriteriaMet: false,
            });
            const actions = failedEvent.getRecommendedPostActions();
            expect(actions).toContain('Analyze execution failure');
            expect(actions).toContain('Assess security impact of failure');
            expect(actions).toContain('Consider alternative actions');
        });
        it('should recommend post-actions for failed security-critical actions', () => {
            const failedCriticalEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                successCriteriaMet: false,
            });
            const actions = failedCriticalEvent.getRecommendedPostActions();
            expect(actions).toContain('Escalate to incident response team');
            expect(actions).toContain('Implement manual fallback procedures');
        });
        it('should recommend post-actions for slow executions', () => {
            const slowEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actualDurationMinutes: 45,
            });
            const actions = slowEvent.getRecommendedPostActions();
            expect(actions).toContain('Investigate performance issues');
            expect(actions).toContain('Optimize action execution');
        });
    });
    describe('notification targets', () => {
        it('should identify targets for successful executions', () => {
            const targets = event.getNotificationTargets();
            expect(targets).toContain('action-requestor');
            expect(targets).toContain('security-team');
        });
        it('should identify targets for failed executions', () => {
            const failedEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                successCriteriaMet: false,
            });
            const targets = failedEvent.getNotificationTargets();
            expect(targets).toContain('action-requestor');
            expect(targets).toContain('security-team');
        });
        it('should identify targets for failed security-critical actions', () => {
            const failedCriticalEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                successCriteriaMet: false,
            });
            const targets = failedCriticalEvent.getNotificationTargets();
            expect(targets).toContain('incident-response-team');
            expect(targets).toContain('security-managers');
        });
        it('should identify targets for slow executions', () => {
            const slowEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actualDurationMinutes: 45,
            });
            const targets = slowEvent.getNotificationTargets();
            expect(targets).toContain('performance-team');
        });
    });
    describe('execution metrics', () => {
        it('should generate comprehensive execution metrics', () => {
            const metrics = event.getExecutionMetrics();
            expect(metrics.actionType).toBe(action_type_enum_1.ActionType.BLOCK_IP);
            expect(metrics.executionTime).toBe(2);
            expect(metrics.performanceCategory).toBe('fast');
            expect(metrics.impactLevel).toBe('high');
            expect(metrics.isSuccessful).toBe(true);
            expect(metrics.isAutomated).toBe(true);
            expect(metrics.isSecurityCritical).toBe(true);
        });
        it('should generate metrics for failed executions', () => {
            const failedEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                successCriteriaMet: false,
                executedBy: '<EMAIL>',
            });
            const metrics = failedEvent.getExecutionMetrics();
            expect(metrics.isSuccessful).toBe(false);
            expect(metrics.isAutomated).toBe(false);
            expect(metrics.impactLevel).toBe('critical');
        });
    });
    describe('compliance information', () => {
        it('should provide compliance info for security-critical actions', () => {
            const compliance = event.getComplianceInfo();
            expect(compliance.requiresDocumentation).toBe(true);
            expect(compliance.auditLevel).toBe('comprehensive');
            expect(compliance.retentionPeriod).toBe('permanent');
        });
        it('should provide compliance info for containment actions', () => {
            const containmentEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
            });
            const compliance = containmentEvent.getComplianceInfo();
            expect(compliance.auditLevel).toBe('comprehensive');
        });
        it('should provide compliance info for failed actions', () => {
            const failedEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
                successCriteriaMet: false,
            });
            const compliance = failedEvent.getComplianceInfo();
            expect(compliance.requiresDocumentation).toBe(true);
            expect(compliance.retentionPeriod).toBe('extended');
        });
        it('should provide compliance info for basic actions', () => {
            const emailEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            const compliance = emailEvent.getComplianceInfo();
            expect(compliance.requiresDocumentation).toBe(false);
            expect(compliance.auditLevel).toBe('basic');
            expect(compliance.retentionPeriod).toBe('standard');
        });
    });
    describe('security implications', () => {
        it('should identify implications for successful containment', () => {
            const implications = event.getSecurityImplications();
            expect(implications).toContain('Threat containment achieved');
            expect(implications).toContain('Risk exposure reduced');
        });
        it('should identify implications for successful recovery', () => {
            const recoveryEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            const implications = recoveryEvent.getSecurityImplications();
            expect(implications).toContain('System recovery completed');
            expect(implications).toContain('Service availability restored');
        });
        it('should identify implications for successful vulnerability patching', () => {
            const patchEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.PATCH_VULNERABILITY,
            });
            const implications = patchEvent.getSecurityImplications();
            expect(implications).toContain('Vulnerability remediated');
            expect(implications).toContain('Attack surface reduced');
        });
        it('should identify implications for failed containment', () => {
            const failedContainmentEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                successCriteriaMet: false,
            });
            const implications = failedContainmentEvent.getSecurityImplications();
            expect(implications).toContain('Containment failure - threat may spread');
            expect(implications).toContain('Increased risk exposure');
        });
        it('should identify implications for failed recovery', () => {
            const failedRecoveryEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
                successCriteriaMet: false,
            });
            const implications = failedRecoveryEvent.getSecurityImplications();
            expect(implications).toContain('Recovery failure - service disruption continues');
            expect(implications).toContain('Business impact ongoing');
        });
        it('should identify implications for failed security-critical actions', () => {
            const failedCriticalEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                successCriteriaMet: false,
            });
            const implications = failedCriticalEvent.getSecurityImplications();
            expect(implications).toContain('Critical security action failed');
            expect(implications).toContain('Manual intervention required');
        });
    });
    describe('integration event conversion', () => {
        it('should convert to integration event format', () => {
            const integrationEvent = event.toIntegrationEvent();
            expect(integrationEvent.eventType).toBe('ResponseActionExecuted');
            expect(integrationEvent.action).toBe('response_action_executed');
            expect(integrationEvent.resource).toBe('ResponseAction');
            expect(integrationEvent.resourceId).toBe(aggregateId.toString());
            expect(integrationEvent.data).toBe(eventData);
            expect(integrationEvent.metadata).toEqual({
                isSuccessful: true,
                performanceCategory: 'fast',
                impactLevel: 'high',
                isSecurityCritical: true,
                isAutomated: true,
                executionMetrics: {
                    actionType: action_type_enum_1.ActionType.BLOCK_IP,
                    executionTime: 2,
                    performanceCategory: 'fast',
                    impactLevel: 'high',
                    isSuccessful: true,
                    isAutomated: true,
                    isSecurityCritical: true,
                },
            });
        });
    });
    describe('edge cases', () => {
        it('should handle events without execution results', () => {
            const noResultsEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                executionResults: undefined,
            });
            expect(noResultsEvent.executionResults).toBeUndefined();
            expect(noResultsEvent.isPartial()).toBe(false);
        });
        it('should handle events without success criteria status', () => {
            const noSuccessEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                successCriteriaMet: undefined,
            });
            expect(noSuccessEvent.isSuccessful()).toBe(false);
            expect(noSuccessEvent.isFailed()).toBe(false);
            expect(noSuccessEvent.isPartial()).toBe(true);
        });
        it('should handle events without duration', () => {
            const noDurationEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actualDurationMinutes: undefined,
            });
            expect(noDurationEvent.actualDurationMinutes).toBeUndefined();
            expect(noDurationEvent.getExecutionMetrics().executionTime).toBe(0);
        });
        it('should handle unknown action types gracefully', () => {
            const unknownEvent = new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(aggregateId, {
                ...eventData,
                actionType: 'UNKNOWN_ACTION',
            });
            expect(unknownEvent.isSecurityCritical()).toBe(false);
            expect(unknownEvent.isContainmentAction()).toBe(false);
            expect(unknownEvent.isRecoveryAction()).toBe(false);
            expect(unknownEvent.getImpactLevel()).toBe('low');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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