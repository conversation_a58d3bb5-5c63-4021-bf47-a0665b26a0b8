{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\event-timestamp.value-object.ts", "mappings": ";;;AAAA,iGAA4F;AAoB5F;;;;;;;;;;;;;GAaG;AACH,MAAa,cAAe,SAAQ,mCAAoC;IAItE,YAAY,KAA0B;QACpC,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IAES,QAAQ;QAChB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,YAAY,IAAI,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,kCAAkC;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC3B,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,YAAY,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;gBACzF,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC5B,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,YAAY,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;gBAC3F,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YAC7C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC7C,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,CAAC,GAAG;gBACjC,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;YACrF,CAAC;QACH,CAAC;QAED,wCAAwC;QACxC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,cAAc,CAAC,uBAAuB,CAAC,CAAC;QACnF,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,cAAc,CAAC,qBAAqB,CAAC,CAAC;QAE/E,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,SAAS,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,OAAO,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;QAC5E,CAAC;QAED,8BAA8B;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC9E,uCAAuC;YACvC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACjF,IAAI,IAAI,GAAG,KAAK,EAAE,CAAC,CAAC,qBAAqB;gBACvC,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU;YACjD,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,OAA0D;QACtE,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,OAAO,IAAI,cAAc,CAAC;YACxB,UAAU,EAAE,GAAG;YACf,UAAU,EAAE,GAAG;YACf,cAAc,EAAE,GAAG,CAAC,iBAAiB,EAAE;YACvC,SAAS,EAAE,aAAa;YACxB,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CACb,UAAgB,EAChB,OAA0D;QAE1D,OAAO,IAAI,cAAc,CAAC;YACxB,UAAU;YACV,UAAU,EAAE,IAAI,IAAI,EAAE;YACtB,cAAc,EAAE,UAAU,CAAC,iBAAiB,EAAE;YAC9C,SAAS,EAAE,aAAa;YACxB,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAClB,SAAiB,EACjB,OAA0D;QAE1D,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QACvC,OAAO,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE;YACzC,iBAAiB,EAAE,SAAS;YAC5B,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CACtB,SAAiB,EACjB,OAA0D;QAE1D,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;QAC9C,OAAO,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE;YACzC,iBAAiB,EAAE,SAAS,CAAC,QAAQ,EAAE;YACvC,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CACvB,iBAAyB,EACzB,OAAgF;QAEhF,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC/C,OAAO,IAAI,cAAc,CAAC;YACxB,UAAU;YACV,iBAAiB;YACjB,UAAU,EAAE,IAAI,IAAI,EAAE;YACtB,cAAc,EAAE,UAAU,CAAC,iBAAiB,EAAE;YAC9C,SAAS,EAAE,aAAa;YACxB,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACzF,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC3F,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,aAAa,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,WAAmB,MAAM;QAChC,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,QAAQ,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,cAAsB,QAAQ;QACpC,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,WAAW,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YACxD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,cAAsB,KAAK;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxC,OAAO,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC;QACxF,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,aAAqB;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QACzB,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,cAAc,CAAC;YACxB,GAAG,IAAI,CAAC,MAAM;YACd,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,cAAc,CAAC;YACxB,GAAG,IAAI,CAAC,MAAM;YACd,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,UAAiB,EAAE,WAAkB;QAC5D,OAAO,IAAI,cAAc,CAAC;YACxB,GAAG,IAAI,CAAC,MAAM;YACd,UAAU,EAAE,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU;YAChD,WAAW,EAAE,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW;SACpD,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB;QAYd,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE;YAChD,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW,EAAE;YACjD,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,WAAW,EAAE;YACnD,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE;YAClB,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,SAAS;YACvD,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,SAAS;YACrD,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,SAAS;YAC7C,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;YACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;SACxB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAsB;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;IAChF,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE;YAChD,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW,EAAE;YACjD,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,WAAW,EAAE;YACnD,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;YAC1C,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB;YAChD,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;YAChC,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE;SACjC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,OAAO,IAAI,cAAc,CAAC;YACxB,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACrC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS;YACnE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;YACtE,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,UAAgB;QAC7B,IAAI,CAAC;YACH,IAAI,cAAc,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;;AAhbH,wCAibC;AAhbyB,sCAAuB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY;AACrD,oCAAqB,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\event-timestamp.value-object.ts"], "sourcesContent": ["import { BaseValueObject } from '../../../../shared-kernel/value-objects/base-value-object';\r\n\r\n/**\r\n * Event Timestamp Properties\r\n */\r\nexport interface EventTimestampProps {\r\n  /** The actual timestamp when the event occurred */\r\n  occurredAt: Date;\r\n  /** When the event was received by the system */\r\n  receivedAt?: Date;\r\n  /** When the event was processed */\r\n  processedAt?: Date;\r\n  /** Timezone offset in minutes from UTC */\r\n  timezoneOffset?: number;\r\n  /** Original timestamp string from source (for forensics) */\r\n  originalTimestamp?: string;\r\n  /** Precision level of the timestamp */\r\n  precision?: 'second' | 'millisecond' | 'microsecond' | 'nanosecond';\r\n}\r\n\r\n/**\r\n * Event Timestamp Value Object\r\n * \r\n * Represents timing information for security events with high precision and forensic capabilities.\r\n * Tracks event occurrence, receipt, and processing times for accurate timeline analysis.\r\n * \r\n * Key features:\r\n * - High-precision timestamp tracking\r\n * - Multiple timestamp types (occurred, received, processed)\r\n * - Timezone awareness and conversion\r\n * - Original timestamp preservation for forensics\r\n * - Time drift detection and analysis\r\n * - Event aging and freshness calculations\r\n */\r\nexport class EventTimestamp extends BaseValueObject<EventTimestampProps> {\r\n  private static readonly MAX_FUTURE_TOLERANCE_MS = 5 * 60 * 1000; // 5 minutes\r\n  private static readonly MAX_PAST_TOLERANCE_MS = 365 * 24 * 60 * 60 * 1000; // 1 year\r\n\r\n  constructor(props: EventTimestampProps) {\r\n    super(props);\r\n  }\r\n\r\n  protected validate(): void {\r\n    if (!this._value.occurredAt) {\r\n      throw new Error('Event timestamp must have an occurredAt date');\r\n    }\r\n\r\n    if (!(this._value.occurredAt instanceof Date)) {\r\n      throw new Error('occurredAt must be a valid Date object');\r\n    }\r\n\r\n    if (isNaN(this._value.occurredAt.getTime())) {\r\n      throw new Error('occurredAt must be a valid date');\r\n    }\r\n\r\n    // Validate receivedAt if provided\r\n    if (this._value.receivedAt) {\r\n      if (!(this._value.receivedAt instanceof Date) || isNaN(this._value.receivedAt.getTime())) {\r\n        throw new Error('receivedAt must be a valid Date object');\r\n      }\r\n    }\r\n\r\n    // Validate processedAt if provided\r\n    if (this._value.processedAt) {\r\n      if (!(this._value.processedAt instanceof Date) || isNaN(this._value.processedAt.getTime())) {\r\n        throw new Error('processedAt must be a valid Date object');\r\n      }\r\n    }\r\n\r\n    // Validate timezone offset\r\n    if (this._value.timezoneOffset !== undefined) {\r\n      if (!Number.isInteger(this._value.timezoneOffset) || \r\n          this._value.timezoneOffset < -720 || \r\n          this._value.timezoneOffset > 720) {\r\n        throw new Error('Timezone offset must be an integer between -720 and 720 minutes');\r\n      }\r\n    }\r\n\r\n    // Check for reasonable timestamp bounds\r\n    const now = new Date();\r\n    const maxFuture = new Date(now.getTime() + EventTimestamp.MAX_FUTURE_TOLERANCE_MS);\r\n    const maxPast = new Date(now.getTime() - EventTimestamp.MAX_PAST_TOLERANCE_MS);\r\n\r\n    if (this._value.occurredAt > maxFuture) {\r\n      throw new Error('Event timestamp cannot be more than 5 minutes in the future');\r\n    }\r\n\r\n    if (this._value.occurredAt < maxPast) {\r\n      throw new Error('Event timestamp cannot be more than 1 year in the past');\r\n    }\r\n\r\n    // Validate timestamp ordering\r\n    if (this._value.receivedAt && this._value.occurredAt > this._value.receivedAt) {\r\n      // Allow small tolerance for clock skew\r\n      const skew = this._value.occurredAt.getTime() - this._value.receivedAt.getTime();\r\n      if (skew > 60000) { // 1 minute tolerance\r\n        throw new Error('Event occurredAt cannot be significantly after receivedAt');\r\n      }\r\n    }\r\n\r\n    if (this._value.processedAt && this._value.receivedAt && \r\n        this._value.receivedAt > this._value.processedAt) {\r\n      throw new Error('Event receivedAt cannot be after processedAt');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create an event timestamp for the current time\r\n   */\r\n  static create(options?: Partial<Omit<EventTimestampProps, 'occurredAt'>>): EventTimestamp {\r\n    const now = new Date();\r\n    return new EventTimestamp({\r\n      occurredAt: now,\r\n      receivedAt: now,\r\n      timezoneOffset: now.getTimezoneOffset(),\r\n      precision: 'millisecond',\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an event timestamp from a specific date\r\n   */\r\n  static fromDate(\r\n    occurredAt: Date,\r\n    options?: Partial<Omit<EventTimestampProps, 'occurredAt'>>\r\n  ): EventTimestamp {\r\n    return new EventTimestamp({\r\n      occurredAt,\r\n      receivedAt: new Date(),\r\n      timezoneOffset: occurredAt.getTimezoneOffset(),\r\n      precision: 'millisecond',\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an event timestamp from ISO string\r\n   */\r\n  static fromISOString(\r\n    isoString: string,\r\n    options?: Partial<Omit<EventTimestampProps, 'occurredAt'>>\r\n  ): EventTimestamp {\r\n    const occurredAt = new Date(isoString);\r\n    return EventTimestamp.fromDate(occurredAt, {\r\n      originalTimestamp: isoString,\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an event timestamp from Unix timestamp\r\n   */\r\n  static fromUnixTimestamp(\r\n    timestamp: number,\r\n    options?: Partial<Omit<EventTimestampProps, 'occurredAt'>>\r\n  ): EventTimestamp {\r\n    const occurredAt = new Date(timestamp * 1000);\r\n    return EventTimestamp.fromDate(occurredAt, {\r\n      originalTimestamp: timestamp.toString(),\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an event timestamp with original string preservation\r\n   */\r\n  static fromOriginalString(\r\n    originalTimestamp: string,\r\n    options?: Partial<Omit<EventTimestampProps, 'occurredAt' | 'originalTimestamp'>>\r\n  ): EventTimestamp {\r\n    const occurredAt = new Date(originalTimestamp);\r\n    return new EventTimestamp({\r\n      occurredAt,\r\n      originalTimestamp,\r\n      receivedAt: new Date(),\r\n      timezoneOffset: occurredAt.getTimezoneOffset(),\r\n      precision: 'millisecond',\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get when the event occurred\r\n   */\r\n  get occurredAt(): Date {\r\n    return new Date(this._value.occurredAt.getTime());\r\n  }\r\n\r\n  /**\r\n   * Get when the event was received\r\n   */\r\n  get receivedAt(): Date | undefined {\r\n    return this._value.receivedAt ? new Date(this._value.receivedAt.getTime()) : undefined;\r\n  }\r\n\r\n  /**\r\n   * Get when the event was processed\r\n   */\r\n  get processedAt(): Date | undefined {\r\n    return this._value.processedAt ? new Date(this._value.processedAt.getTime()) : undefined;\r\n  }\r\n\r\n  /**\r\n   * Get timezone offset in minutes\r\n   */\r\n  get timezoneOffset(): number | undefined {\r\n    return this._value.timezoneOffset;\r\n  }\r\n\r\n  /**\r\n   * Get original timestamp string\r\n   */\r\n  get originalTimestamp(): string | undefined {\r\n    return this._value.originalTimestamp;\r\n  }\r\n\r\n  /**\r\n   * Get timestamp precision\r\n   */\r\n  get precision(): string {\r\n    return this._value.precision || 'millisecond';\r\n  }\r\n\r\n  /**\r\n   * Get event age in milliseconds\r\n   */\r\n  getAge(): number {\r\n    return Date.now() - this._value.occurredAt.getTime();\r\n  }\r\n\r\n  /**\r\n   * Get event age in seconds\r\n   */\r\n  getAgeInSeconds(): number {\r\n    return Math.floor(this.getAge() / 1000);\r\n  }\r\n\r\n  /**\r\n   * Get event age in minutes\r\n   */\r\n  getAgeInMinutes(): number {\r\n    return Math.floor(this.getAge() / (1000 * 60));\r\n  }\r\n\r\n  /**\r\n   * Get event age in hours\r\n   */\r\n  getAgeInHours(): number {\r\n    return Math.floor(this.getAge() / (1000 * 60 * 60));\r\n  }\r\n\r\n  /**\r\n   * Check if event is recent (within specified time)\r\n   */\r\n  isRecent(withinMs: number = 300000): boolean { // Default 5 minutes\r\n    return this.getAge() <= withinMs;\r\n  }\r\n\r\n  /**\r\n   * Check if event is stale (older than specified time)\r\n   */\r\n  isStale(olderThanMs: number = 86400000): boolean { // Default 24 hours\r\n    return this.getAge() > olderThanMs;\r\n  }\r\n\r\n  /**\r\n   * Check if event occurred in the future\r\n   */\r\n  isInFuture(): boolean {\r\n    return this._value.occurredAt.getTime() > Date.now();\r\n  }\r\n\r\n  /**\r\n   * Get processing delay (time between occurrence and receipt)\r\n   */\r\n  getProcessingDelay(): number | null {\r\n    if (!this._value.receivedAt) {\r\n      return null;\r\n    }\r\n    return this._value.receivedAt.getTime() - this._value.occurredAt.getTime();\r\n  }\r\n\r\n  /**\r\n   * Get ingestion delay (time between receipt and processing)\r\n   */\r\n  getIngestionDelay(): number | null {\r\n    if (!this._value.receivedAt || !this._value.processedAt) {\r\n      return null;\r\n    }\r\n    return this._value.processedAt.getTime() - this._value.receivedAt.getTime();\r\n  }\r\n\r\n  /**\r\n   * Get total delay (time between occurrence and processing)\r\n   */\r\n  getTotalDelay(): number | null {\r\n    if (!this._value.processedAt) {\r\n      return null;\r\n    }\r\n    return this._value.processedAt.getTime() - this._value.occurredAt.getTime();\r\n  }\r\n\r\n  /**\r\n   * Check if there's significant clock skew\r\n   */\r\n  hasClockSkew(toleranceMs: number = 60000): boolean { // Default 1 minute\r\n    const delay = this.getProcessingDelay();\r\n    return delay !== null && Math.abs(delay) > toleranceMs;\r\n  }\r\n\r\n  /**\r\n   * Convert to UTC\r\n   */\r\n  toUTC(): Date {\r\n    if (this._value.timezoneOffset === undefined) {\r\n      return this.occurredAt;\r\n    }\r\n    \r\n    const utcTime = this._value.occurredAt.getTime() + (this._value.timezoneOffset * 60000);\r\n    return new Date(utcTime);\r\n  }\r\n\r\n  /**\r\n   * Convert to specific timezone\r\n   */\r\n  toTimezone(offsetMinutes: number): Date {\r\n    const utc = this.toUTC();\r\n    return new Date(utc.getTime() - (offsetMinutes * 60000));\r\n  }\r\n\r\n  /**\r\n   * Get ISO string representation\r\n   */\r\n  toISOString(): string {\r\n    return this._value.occurredAt.toISOString();\r\n  }\r\n\r\n  /**\r\n   * Get Unix timestamp\r\n   */\r\n  toUnixTimestamp(): number {\r\n    return Math.floor(this._value.occurredAt.getTime() / 1000);\r\n  }\r\n\r\n  /**\r\n   * Mark as received now\r\n   */\r\n  markAsReceived(): EventTimestamp {\r\n    return new EventTimestamp({\r\n      ...this._value,\r\n      receivedAt: new Date(),\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Mark as processed now\r\n   */\r\n  markAsProcessed(): EventTimestamp {\r\n    return new EventTimestamp({\r\n      ...this._value,\r\n      processedAt: new Date(),\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Update with processing timestamps\r\n   */\r\n  withProcessingTimestamps(receivedAt?: Date, processedAt?: Date): EventTimestamp {\r\n    return new EventTimestamp({\r\n      ...this._value,\r\n      receivedAt: receivedAt || this._value.receivedAt,\r\n      processedAt: processedAt || this._value.processedAt,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get timestamp summary for analysis\r\n   */\r\n  getTimingSummary(): {\r\n    occurredAt: string;\r\n    receivedAt?: string;\r\n    processedAt?: string;\r\n    age: number;\r\n    processingDelay?: number;\r\n    ingestionDelay?: number;\r\n    totalDelay?: number;\r\n    hasClockSkew: boolean;\r\n    isRecent: boolean;\r\n    isStale: boolean;\r\n  } {\r\n    return {\r\n      occurredAt: this._value.occurredAt.toISOString(),\r\n      receivedAt: this._value.receivedAt?.toISOString(),\r\n      processedAt: this._value.processedAt?.toISOString(),\r\n      age: this.getAge(),\r\n      processingDelay: this.getProcessingDelay() || undefined,\r\n      ingestionDelay: this.getIngestionDelay() || undefined,\r\n      totalDelay: this.getTotalDelay() || undefined,\r\n      hasClockSkew: this.hasClockSkew(),\r\n      isRecent: this.isRecent(),\r\n      isStale: this.isStale(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Compare timestamps for equality\r\n   */\r\n  public equals(other?: EventTimestamp): boolean {\r\n    if (!other) {\r\n      return false;\r\n    }\r\n\r\n    if (this === other) {\r\n      return true;\r\n    }\r\n\r\n    return this._value.occurredAt.getTime() === other._value.occurredAt.getTime();\r\n  }\r\n\r\n  /**\r\n   * Get string representation\r\n   */\r\n  public toString(): string {\r\n    return this._value.occurredAt.toISOString();\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      occurredAt: this._value.occurredAt.toISOString(),\r\n      receivedAt: this._value.receivedAt?.toISOString(),\r\n      processedAt: this._value.processedAt?.toISOString(),\r\n      timezoneOffset: this._value.timezoneOffset,\r\n      originalTimestamp: this._value.originalTimestamp,\r\n      precision: this._value.precision,\r\n      summary: this.getTimingSummary(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create EventTimestamp from JSON\r\n   */\r\n  static fromJSON(json: Record<string, any>): EventTimestamp {\r\n    return new EventTimestamp({\r\n      occurredAt: new Date(json.occurredAt),\r\n      receivedAt: json.receivedAt ? new Date(json.receivedAt) : undefined,\r\n      processedAt: json.processedAt ? new Date(json.processedAt) : undefined,\r\n      timezoneOffset: json.timezoneOffset,\r\n      originalTimestamp: json.originalTimestamp,\r\n      precision: json.precision,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Validate timestamp format without creating instance\r\n   */\r\n  static isValid(occurredAt: Date): boolean {\r\n    try {\r\n      new EventTimestamp({ occurredAt });\r\n      return true;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}