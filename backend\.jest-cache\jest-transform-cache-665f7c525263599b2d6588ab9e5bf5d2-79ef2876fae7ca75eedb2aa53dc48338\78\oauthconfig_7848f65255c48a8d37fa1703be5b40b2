a63142b5c466db66a9c7375ded419f0b
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OAuthConfigHelper = exports.oauthConfig = exports.OAuthConfigValidation = void 0;
const config_1 = require("@nestjs/config");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
// Validation class for OAuth configuration
class OAuthConfigValidation {
}
exports.OAuthConfigValidation = OAuthConfigValidation;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OAuthConfigValidation.prototype, "googleClientId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OAuthConfigValidation.prototype, "googleClientSecret", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], OAuthConfigValidation.prototype, "googleCallbackUrl", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OAuthConfigValidation.prototype, "microsoftClientId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OAuthConfigValidation.prototype, "microsoftClientSecret", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], OAuthConfigValidation.prototype, "microsoftCallbackUrl", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OAuthConfigValidation.prototype, "microsoftTenant", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OAuthConfigValidation.prototype, "githubClientId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OAuthConfigValidation.prototype, "githubClientSecret", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], OAuthConfigValidation.prototype, "githubCallbackUrl", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OAuthConfigValidation.prototype, "linkedinClientId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OAuthConfigValidation.prototype, "linkedinClientSecret", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], OAuthConfigValidation.prototype, "linkedinCallbackUrl", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true'),
    __metadata("design:type", Boolean)
], OAuthConfigValidation.prototype, "enablePKCE", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true'),
    __metadata("design:type", Boolean)
], OAuthConfigValidation.prototype, "enableNonce", void 0);
// Utility functions
const parseBoolean = (value, fallback = false) => value?.toLowerCase() === 'true' || fallback;
const parseNumber = (value, fallback) => {
    if (!value)
        return fallback;
    const parsed = Number(value);
    return Number.isNaN(parsed) ? fallback : parsed;
};
const parseArray = (value, fallback = []) => {
    if (!value)
        return fallback;
    return value.split(',').map(s => s.trim()).filter(Boolean);
};
const env = {
    getString: (key, fallback = '') => process.env[key] ?? fallback,
    getNumber: (key, fallback = 0) => parseNumber(process.env[key], fallback),
    getBoolean: (key, fallback = false) => parseBoolean(process.env[key], fallback),
    getArray: (key, fallback = []) => parseArray(process.env[key], fallback),
    isProduction: () => process.env['NODE_ENV'] === 'production',
};
// Configuration defaults
const DEFAULTS = {
    STATE_EXPIRATION: 30 * 60 * 1000, // 30 minutes
    CALLBACK_TIMEOUT: 60 * 1000, // 1 minute
    MAX_RETRIES: 3,
    ENABLE_PKCE: true,
    ENABLE_NONCE: true,
    DEFAULT_SCOPES: ['openid', 'email', 'profile'],
    GOOGLE_SCOPES: ['openid', 'email', 'profile'],
    MICROSOFT_SCOPES: ['openid', 'email', 'profile'],
    GITHUB_SCOPES: ['user:email', 'read:user'],
    LINKEDIN_SCOPES: ['r_liteprofile', 'r_emailaddress'],
    FACEBOOK_SCOPES: ['email', 'public_profile'],
    TWITTER_SCOPES: ['tweet.read', 'users.read'],
};
// Helper function to create OAuth provider configuration
const createProviderConfig = (provider, defaultScopes = DEFAULTS.DEFAULT_SCOPES) => {
    const prefix = provider.toUpperCase();
    const clientId = env.getString(`${prefix}_CLIENT_ID`);
    const clientSecret = env.getString(`${prefix}_CLIENT_SECRET`);
    const callbackUrl = env.getString(`${prefix}_CALLBACK_URL`);
    const tenant = env.getString(`${prefix}_TENANT`); // For Microsoft
    const scope = env.getArray(`${prefix}_SCOPE`, [...defaultScopes]);
    const enabled = env.getBoolean(`${prefix}_ENABLED`) && !!(clientId && clientSecret);
    return {
        clientId,
        clientSecret,
        callbackUrl,
        tenant,
        scope,
        enabled,
    };
};
/**
 * OAuth configuration factory
 * Creates OAuth configuration with environment variable support and validation
 */
exports.oauthConfig = (0, config_1.registerAs)('oauth', () => {
    // Warn about missing configuration in production
    if (env.isProduction()) {
        const requiredProviders = ['GOOGLE', 'MICROSOFT'];
        const missingProviders = requiredProviders.filter(provider => {
            const clientId = env.getString(`${provider}_CLIENT_ID`);
            const clientSecret = env.getString(`${provider}_CLIENT_SECRET`);
            return !clientId || !clientSecret;
        });
        if (missingProviders.length > 0) {
            console.warn(`⚠️  WARNING: Missing OAuth configuration for providers: ${missingProviders.join(', ')}`);
        }
    }
    return {
        google: createProviderConfig('google', DEFAULTS.GOOGLE_SCOPES),
        microsoft: createProviderConfig('microsoft', DEFAULTS.MICROSOFT_SCOPES),
        github: createProviderConfig('github', DEFAULTS.GITHUB_SCOPES),
        linkedin: createProviderConfig('linkedin', DEFAULTS.LINKEDIN_SCOPES),
        facebook: createProviderConfig('facebook', DEFAULTS.FACEBOOK_SCOPES),
        twitter: createProviderConfig('twitter', DEFAULTS.TWITTER_SCOPES),
        apple: createProviderConfig('apple'),
        okta: createProviderConfig('okta'),
        auth0: createProviderConfig('auth0'),
        generic: createProviderConfig('generic'),
        settings: {
            stateExpiration: env.getNumber('OAUTH_STATE_EXPIRATION', DEFAULTS.STATE_EXPIRATION),
            callbackTimeout: env.getNumber('OAUTH_CALLBACK_TIMEOUT', DEFAULTS.CALLBACK_TIMEOUT),
            maxRetries: env.getNumber('OAUTH_MAX_RETRIES', DEFAULTS.MAX_RETRIES),
            enablePKCE: env.getBoolean('OAUTH_ENABLE_PKCE', DEFAULTS.ENABLE_PKCE),
            enableNonce: env.getBoolean('OAUTH_ENABLE_NONCE', DEFAULTS.ENABLE_NONCE),
            defaultScopes: env.getArray('OAUTH_DEFAULT_SCOPES', [...DEFAULTS.DEFAULT_SCOPES]),
        },
    };
});
/**
 * OAuth configuration helper functions
 */
exports.OAuthConfigHelper = {
    /**
     * Get enabled OAuth providers
     */
    getEnabledProviders: (config) => {
        return Object.entries(config)
            .filter(([key, value]) => key !== 'settings' && value.enabled)
            .map(([key]) => key);
    },
    /**
     * Get provider configuration
     */
    getProviderConfig: (config, provider) => {
        const providerConfig = config[provider];
        return (providerConfig && typeof providerConfig === 'object' && 'enabled' in providerConfig)
            ? providerConfig
            : null;
    },
    /**
     * Check if provider is enabled
     */
    isProviderEnabled: (config, provider) => {
        const providerConfig = exports.OAuthConfigHelper.getProviderConfig(config, provider);
        return providerConfig?.enabled || false;
    },
    /**
     * Get callback URL for provider
     */
    getCallbackUrl: (config, provider, baseUrl) => {
        const providerConfig = exports.OAuthConfigHelper.getProviderConfig(config, provider);
        if (providerConfig?.callbackUrl) {
            return providerConfig.callbackUrl;
        }
        // Generate default callback URL
        const base = baseUrl || env.getString('APP_URL', 'http://localhost:3000');
        return `${base}/auth/${provider}/callback`;
    },
    /**
     * Validate OAuth configuration
     */
    validate: (config) => {
        const errors = [];
        // Check settings
        if (config.settings.stateExpiration <= 0) {
            errors.push('OAuth state expiration must be positive');
        }
        if (config.settings.callbackTimeout <= 0) {
            errors.push('OAuth callback timeout must be positive');
        }
        if (config.settings.maxRetries < 0) {
            errors.push('OAuth max retries cannot be negative');
        }
        // Check enabled providers
        const enabledProviders = exports.OAuthConfigHelper.getEnabledProviders(config);
        for (const provider of enabledProviders) {
            const providerConfig = exports.OAuthConfigHelper.getProviderConfig(config, provider);
            if (!providerConfig)
                continue;
            if (!providerConfig.clientId) {
                errors.push(`OAuth provider '${provider}' missing client ID`);
            }
            if (!providerConfig.clientSecret) {
                errors.push(`OAuth provider '${provider}' missing client secret`);
            }
            if (!providerConfig.scope || providerConfig.scope.length === 0) {
                errors.push(`OAuth provider '${provider}' missing scope configuration`);
            }
        }
        return errors;
    },
    /**
     * Check if configuration is secure for production
     */
    isSecure: (config) => {
        const enabledProviders = exports.OAuthConfigHelper.getEnabledProviders(config);
        // Must have at least one enabled provider
        if (enabledProviders.length === 0) {
            return false;
        }
        // Check that all enabled providers have proper configuration
        for (const provider of enabledProviders) {
            const providerConfig = exports.OAuthConfigHelper.getProviderConfig(config, provider);
            if (!providerConfig?.clientId || !providerConfig?.clientSecret) {
                return false;
            }
            // Check for default/example values
            if (providerConfig.clientId.includes('example') ||
                providerConfig.clientSecret.includes('example')) {
                return false;
            }
        }
        // Security settings should be enabled
        return config.settings.enablePKCE && config.settings.enableNonce;
    },
    /**
     * Get provider display name
     */
    getProviderDisplayName: (provider) => {
        const displayNames = {
            google: 'Google',
            microsoft: 'Microsoft',
            github: 'GitHub',
            linkedin: 'LinkedIn',
            facebook: 'Facebook',
            twitter: 'Twitter',
            apple: 'Apple',
            okta: 'Okta',
            auth0: 'Auth0',
            generic: 'Generic OAuth',
        };
        return displayNames[provider] || provider.charAt(0).toUpperCase() + provider.slice(1);
    },
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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