51efbbae4a1342713a2940381315147a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventProcessingStatusChangedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const event_processing_status_enum_1 = require("../enums/event-processing-status.enum");
/**
 * Event Processing Status Changed Domain Event
 *
 * Raised when a security event's processing status changes in the pipeline.
 * This event triggers various downstream processes including:
 * - Pipeline stage transitions
 * - Processing metrics updates
 * - Performance monitoring
 * - Error handling and retries
 * - Processing notifications
 */
class EventProcessingStatusChangedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the previous processing status
     */
    get oldStatus() {
        return this.eventData.oldStatus;
    }
    /**
     * Get the new processing status
     */
    get newStatus() {
        return this.eventData.newStatus;
    }
    /**
     * Get when the processing status was changed
     */
    get timestamp() {
        return this.eventData.timestamp;
    }
    /**
     * Check if processing completed successfully
     */
    wasCompleted() {
        return event_processing_status_enum_1.EventProcessingStatusUtils.isSuccess(this.newStatus);
    }
    /**
     * Check if processing failed
     */
    hasFailed() {
        return event_processing_status_enum_1.EventProcessingStatusUtils.isFailure(this.newStatus);
    }
    /**
     * Check if processing is in progress
     */
    isInProgress() {
        return event_processing_status_enum_1.EventProcessingStatusUtils.isInProgress(this.newStatus);
    }
    /**
     * Check if processing reached terminal state
     */
    isTerminal() {
        return event_processing_status_enum_1.EventProcessingStatusUtils.isTerminal(this.newStatus);
    }
    /**
     * Check if processing requires attention
     */
    requiresAttention() {
        return event_processing_status_enum_1.EventProcessingStatusUtils.requiresAttention(this.newStatus);
    }
    /**
     * Check if this is a forward progression in the pipeline
     */
    isForwardProgression() {
        const progressionOrder = [
            event_processing_status_enum_1.EventProcessingStatus.RAW,
            event_processing_status_enum_1.EventProcessingStatus.NORMALIZING,
            event_processing_status_enum_1.EventProcessingStatus.NORMALIZED,
            event_processing_status_enum_1.EventProcessingStatus.ENRICHING,
            event_processing_status_enum_1.EventProcessingStatus.ENRICHED,
            event_processing_status_enum_1.EventProcessingStatus.CORRELATING,
            event_processing_status_enum_1.EventProcessingStatus.CORRELATED,
            event_processing_status_enum_1.EventProcessingStatus.ANALYZING,
            event_processing_status_enum_1.EventProcessingStatus.ANALYZED,
            event_processing_status_enum_1.EventProcessingStatus.PENDING_REVIEW,
            event_processing_status_enum_1.EventProcessingStatus.INVESTIGATING,
            event_processing_status_enum_1.EventProcessingStatus.RESOLVED,
            event_processing_status_enum_1.EventProcessingStatus.ARCHIVED,
        ];
        const oldIndex = progressionOrder.indexOf(this.oldStatus);
        const newIndex = progressionOrder.indexOf(this.newStatus);
        return oldIndex !== -1 && newIndex !== -1 && newIndex > oldIndex;
    }
    /**
     * Check if this is a backward movement (retry/reprocessing)
     */
    isBackwardMovement() {
        return this.newStatus === event_processing_status_enum_1.EventProcessingStatus.REPROCESSING ||
            (this.oldStatus === event_processing_status_enum_1.EventProcessingStatus.FAILED &&
                [event_processing_status_enum_1.EventProcessingStatus.RAW, event_processing_status_enum_1.EventProcessingStatus.NORMALIZING].includes(this.newStatus));
    }
    /**
     * Get processing stage category
     */
    getStageCategory() {
        const ingestionStages = [event_processing_status_enum_1.EventProcessingStatus.RAW];
        const processingStages = [
            event_processing_status_enum_1.EventProcessingStatus.NORMALIZING,
            event_processing_status_enum_1.EventProcessingStatus.NORMALIZED,
            event_processing_status_enum_1.EventProcessingStatus.ENRICHING,
            event_processing_status_enum_1.EventProcessingStatus.ENRICHED,
            event_processing_status_enum_1.EventProcessingStatus.CORRELATING,
            event_processing_status_enum_1.EventProcessingStatus.CORRELATED,
        ];
        const analysisStages = [
            event_processing_status_enum_1.EventProcessingStatus.ANALYZING,
            event_processing_status_enum_1.EventProcessingStatus.ANALYZED,
        ];
        const resolutionStages = [
            event_processing_status_enum_1.EventProcessingStatus.PENDING_REVIEW,
            event_processing_status_enum_1.EventProcessingStatus.INVESTIGATING,
            event_processing_status_enum_1.EventProcessingStatus.RESOLVED,
        ];
        const storageStages = [event_processing_status_enum_1.EventProcessingStatus.ARCHIVED];
        const errorStages = [
            event_processing_status_enum_1.EventProcessingStatus.FAILED,
            event_processing_status_enum_1.EventProcessingStatus.TIMEOUT,
            event_processing_status_enum_1.EventProcessingStatus.ON_HOLD,
        ];
        if (ingestionStages.includes(this.newStatus))
            return 'ingestion';
        if (processingStages.includes(this.newStatus))
            return 'processing';
        if (analysisStages.includes(this.newStatus))
            return 'analysis';
        if (resolutionStages.includes(this.newStatus))
            return 'resolution';
        if (storageStages.includes(this.newStatus))
            return 'storage';
        if (errorStages.includes(this.newStatus))
            return 'error';
        return 'other';
    }
    /**
     * Get notification priority based on status change
     */
    getNotificationPriority() {
        if (this.hasFailed())
            return 'high';
        if (this.requiresAttention())
            return 'medium';
        if (this.wasCompleted())
            return 'low';
        return 'low';
    }
    /**
     * Check if change requires notification
     */
    requiresNotification() {
        // Notify on failures, completions, and attention-requiring states
        return this.hasFailed() || this.wasCompleted() || this.requiresAttention();
    }
    /**
     * Get processing metrics for this change
     */
    getProcessingMetrics() {
        return {
            oldStatus: this.oldStatus,
            newStatus: this.newStatus,
            isForwardProgression: this.isForwardProgression(),
            isBackwardMovement: this.isBackwardMovement(),
            stageCategory: this.getStageCategory(),
            isTerminal: this.isTerminal(),
            requiresAttention: this.requiresAttention(),
            processingTime: this.getAge(), // Time since this event occurred
        };
    }
    /**
     * Get change summary for handlers
     */
    getChangeSummary() {
        return {
            eventId: this.aggregateId.toString(),
            oldStatus: this.oldStatus,
            newStatus: this.newStatus,
            oldStatusDescription: event_processing_status_enum_1.EventProcessingStatusUtils.getDescription(this.oldStatus),
            newStatusDescription: event_processing_status_enum_1.EventProcessingStatusUtils.getDescription(this.newStatus),
            stageCategory: this.getStageCategory(),
            priority: this.getNotificationPriority(),
            requiresNotification: this.requiresNotification(),
            wasCompleted: this.wasCompleted(),
            hasFailed: this.hasFailed(),
            isInProgress: this.isInProgress(),
            isTerminal: this.isTerminal(),
            requiresAttention: this.requiresAttention(),
            timestamp: this.timestamp.toISOString(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            changeSummary: this.getChangeSummary(),
            processingMetrics: this.getProcessingMetrics(),
        };
    }
}
exports.EventProcessingStatusChangedDomainEvent = EventProcessingStatusChangedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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