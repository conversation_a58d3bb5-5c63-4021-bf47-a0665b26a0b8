d3f31518843f62a7f2ea4d42a03278b4
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const event_type_enum_1 = require("../../../enums/event-type.enum");
const event_severity_enum_1 = require("../../../enums/event-severity.enum");
const event_status_enum_1 = require("../../../enums/event-status.enum");
const event_processing_status_enum_1 = require("../../../enums/event-processing-status.enum");
const event_source_type_enum_1 = require("../../../enums/event-source-type.enum");
describe('SecurityEventData Interface', () => {
    let mockSecurityEventData;
    let mockSourceMetadata;
    let mockNetworkInfo;
    let mockAssetInfo;
    let mockUserInfo;
    let mockGeoLocation;
    let mockThreatIndicators;
    let mockComplianceInfo;
    let mockRetentionInfo;
    beforeEach(() => {
        mockSourceMetadata = {
            systemName: 'Test SIEM',
            systemVersion: '1.0.0',
            agent: 'test-agent',
            configId: 'config-123',
            dataFormat: 'json',
            timezone: 'UTC',
            collectedAt: '2024-01-01T00:00:00Z',
            processedAt: '2024-01-01T00:01:00Z',
            reliability: 95,
            sourceIds: { 'internal_id': '12345' },
        };
        mockNetworkInfo = {
            sourceIp: '*************',
            destinationIp: '********',
            sourcePort: 443,
            destinationPort: 80,
            protocol: 'TCP',
            networkSegment: '***********/24',
            macAddresses: ['00:11:22:33:44:55'],
            domains: ['example.com'],
            urls: ['https://example.com/path'],
            flowDirection: 'outbound',
            bytesTransferred: 1024,
            packetsCount: 10,
            connectionDuration: 30,
        };
        mockAssetInfo = {
            assetId: 'asset-123',
            assetName: 'Web Server 01',
            assetType: 'server',
            assetOwner: 'IT Team',
            criticality: 'high',
            location: 'Data Center A',
            environment: 'production',
            operatingSystem: 'Ubuntu 20.04',
            applications: ['nginx', 'nodejs'],
            assetTags: ['web', 'public-facing'],
        };
        mockUserInfo = {
            userId: 'user-123',
            username: 'john.doe',
            email: '<EMAIL>',
            roles: ['admin', 'user'],
            groups: ['administrators'],
            department: 'IT',
            location: 'New York',
            authMethod: 'SAML',
            sessionId: 'session-456',
            userAgent: 'Mozilla/5.0...',
        };
        mockGeoLocation = {
            country: 'US',
            region: 'NY',
            city: 'New York',
            latitude: 40.7128,
            longitude: -74.0060,
            isp: 'Example ISP',
            organization: 'Example Corp',
            timezone: 'America/New_York',
            accuracyRadius: 10,
        };
        mockThreatIndicators = {
            iocs: ['*************', 'malicious.com'],
            malwareHashes: ['abc123def456'],
            suspiciousDomains: ['malicious.com'],
            suspiciousIps: ['*************'],
            signatures: ['MALWARE_DETECTED'],
            mitreAttackTechniques: ['T1055'],
            threatActor: 'APT29',
            malwareFamily: 'Cobalt Strike',
            campaignId: 'campaign-123',
        };
        mockComplianceInfo = {
            frameworks: ['SOX', 'PCI-DSS'],
            requirements: ['Data Protection', 'Access Control'],
            dataClassification: 'Confidential',
            privacyImpact: 'high',
            regulatoryNotifications: ['SEC', 'PCI Council'],
            auditRequirements: ['Log Retention', 'Access Monitoring'],
        };
        mockRetentionInfo = {
            retentionDays: 2555, // 7 years
            legalHold: false,
            archivalRequired: true,
            deletionDate: '2031-01-01T00:00:00Z',
            policyId: 'retention-policy-001',
            justification: 'Regulatory compliance requirement',
        };
        mockSecurityEventData = {
            version: '1.0.0',
            externalId: 'ext-event-123',
            sourceSystem: 'Test SIEM',
            sourceType: event_source_type_enum_1.EventSourceType.SIEM,
            timestamp: '2024-01-01T00:00:00Z',
            type: event_type_enum_1.EventType.LOGIN_FAILURE,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            status: event_status_enum_1.EventStatus.OPEN,
            processingStatus: event_processing_status_enum_1.EventProcessingStatus.NORMALIZED,
            title: 'Failed Login Attempt',
            description: 'Multiple failed login attempts detected',
            rawData: { original: 'raw event data' },
            normalizedData: { normalized: 'processed data' },
            tags: ['authentication', 'security'],
            riskScore: 85,
            confidenceLevel: 90,
            attributes: { custom: 'attribute' },
            correlationId: 'corr-123',
            parentEventId: 'parent-456',
            sourceMetadata: mockSourceMetadata,
            networkInfo: mockNetworkInfo,
            assetInfo: mockAssetInfo,
            userInfo: mockUserInfo,
            geoLocation: mockGeoLocation,
            threatIndicators: mockThreatIndicators,
            complianceInfo: mockComplianceInfo,
            retentionInfo: mockRetentionInfo,
        };
    });
    describe('SecurityEventData Structure', () => {
        it('should have all required fields', () => {
            expect(mockSecurityEventData.version).toBeDefined();
            expect(mockSecurityEventData.externalId).toBeDefined();
            expect(mockSecurityEventData.sourceSystem).toBeDefined();
            expect(mockSecurityEventData.sourceType).toBeDefined();
            expect(mockSecurityEventData.timestamp).toBeDefined();
            expect(mockSecurityEventData.type).toBeDefined();
            expect(mockSecurityEventData.severity).toBeDefined();
            expect(mockSecurityEventData.title).toBeDefined();
            expect(mockSecurityEventData.rawData).toBeDefined();
            expect(mockSecurityEventData.sourceMetadata).toBeDefined();
        });
        it('should support versioning', () => {
            expect(mockSecurityEventData.version).toBe('1.0.0');
            expect(typeof mockSecurityEventData.version).toBe('string');
        });
        it('should be serializable to JSON', () => {
            const jsonString = JSON.stringify(mockSecurityEventData);
            expect(jsonString).toBeDefined();
            expect(jsonString.length).toBeGreaterThan(0);
            const parsed = JSON.parse(jsonString);
            expect(parsed.version).toBe(mockSecurityEventData.version);
            expect(parsed.externalId).toBe(mockSecurityEventData.externalId);
        });
        it('should maintain data integrity after serialization', () => {
            const jsonString = JSON.stringify(mockSecurityEventData);
            const parsed = JSON.parse(jsonString);
            expect(parsed.version).toBe(mockSecurityEventData.version);
            expect(parsed.externalId).toBe(mockSecurityEventData.externalId);
            expect(parsed.sourceSystem).toBe(mockSecurityEventData.sourceSystem);
            expect(parsed.type).toBe(mockSecurityEventData.type);
            expect(parsed.severity).toBe(mockSecurityEventData.severity);
            expect(parsed.sourceMetadata.systemName).toBe(mockSecurityEventData.sourceMetadata.systemName);
        });
    });
    describe('SecurityEventSourceMetadata', () => {
        it('should contain required source information', () => {
            expect(mockSourceMetadata.systemName).toBe('Test SIEM');
            expect(mockSourceMetadata.dataFormat).toBe('json');
            expect(mockSourceMetadata.collectedAt).toBeDefined();
        });
        it('should support reliability scoring', () => {
            expect(mockSourceMetadata.reliability).toBe(95);
            expect(typeof mockSourceMetadata.reliability).toBe('number');
        });
    });
    describe('SecurityEventNetworkInfo', () => {
        it('should contain network-related information', () => {
            expect(mockNetworkInfo.sourceIp).toBe('*************');
            expect(mockNetworkInfo.destinationIp).toBe('********');
            expect(mockNetworkInfo.protocol).toBe('TCP');
        });
        it('should support flow direction classification', () => {
            expect(['inbound', 'outbound', 'internal']).toContain(mockNetworkInfo.flowDirection);
        });
    });
    describe('SecurityEventAssetInfo', () => {
        it('should contain asset identification', () => {
            expect(mockAssetInfo.assetId).toBe('asset-123');
            expect(mockAssetInfo.assetName).toBe('Web Server 01');
            expect(mockAssetInfo.assetType).toBe('server');
        });
        it('should support criticality levels', () => {
            expect(['low', 'medium', 'high', 'critical']).toContain(mockAssetInfo.criticality);
        });
        it('should support environment classification', () => {
            expect(['production', 'staging', 'development', 'test']).toContain(mockAssetInfo.environment);
        });
    });
    describe('SecurityEventUserInfo', () => {
        it('should contain user identification', () => {
            expect(mockUserInfo.userId).toBe('user-123');
            expect(mockUserInfo.username).toBe('john.doe');
            expect(mockUserInfo.email).toBe('<EMAIL>');
        });
        it('should support role-based information', () => {
            expect(Array.isArray(mockUserInfo.roles)).toBe(true);
            expect(mockUserInfo.roles).toContain('admin');
        });
    });
    describe('SecurityEventThreatIndicators', () => {
        it('should contain threat intelligence data', () => {
            expect(Array.isArray(mockThreatIndicators.iocs)).toBe(true);
            expect(mockThreatIndicators.iocs).toContain('*************');
        });
        it('should support MITRE ATT&CK mapping', () => {
            expect(Array.isArray(mockThreatIndicators.mitreAttackTechniques)).toBe(true);
            expect(mockThreatIndicators.mitreAttackTechniques).toContain('T1055');
        });
    });
    describe('SecurityEventComplianceInfo', () => {
        it('should contain compliance framework information', () => {
            expect(Array.isArray(mockComplianceInfo.frameworks)).toBe(true);
            expect(mockComplianceInfo.frameworks).toContain('SOX');
        });
        it('should support privacy impact assessment', () => {
            expect(['none', 'low', 'medium', 'high']).toContain(mockComplianceInfo.privacyImpact);
        });
    });
    describe('SecurityEventRetentionInfo', () => {
        it('should contain retention requirements', () => {
            expect(typeof mockRetentionInfo.retentionDays).toBe('number');
            expect(mockRetentionInfo.retentionDays).toBeGreaterThan(0);
        });
        it('should support legal hold status', () => {
            expect(typeof mockRetentionInfo.legalHold).toBe('boolean');
        });
    });
    describe('Data Validation Interface', () => {
        it('should define validation methods', () => {
            // Mock validator implementation
            const mockValidator = {
                validate: jest.fn().mockReturnValue({
                    isValid: true,
                    errors: [],
                    warnings: [],
                    sanitizedData: mockSecurityEventData,
                }),
                validateVersion: jest.fn().mockReturnValue(true),
                sanitize: jest.fn().mockReturnValue(mockSecurityEventData),
            };
            expect(mockValidator.validate).toBeDefined();
            expect(mockValidator.validateVersion).toBeDefined();
            expect(mockValidator.sanitize).toBeDefined();
        });
    });
    describe('Data Serialization Interface', () => {
        it('should define serialization methods', () => {
            // Mock serializer implementation
            const mockSerializer = {
                toJson: jest.fn().mockReturnValue('{}'),
                fromJson: jest.fn().mockReturnValue(mockSecurityEventData),
                toBinary: jest.fn().mockReturnValue(Buffer.from('test')),
                fromBinary: jest.fn().mockReturnValue(mockSecurityEventData),
                getSupportedVersions: jest.fn().mockReturnValue(['1.0.0']),
            };
            expect(mockSerializer.toJson).toBeDefined();
            expect(mockSerializer.fromJson).toBeDefined();
            expect(mockSerializer.toBinary).toBeDefined();
            expect(mockSerializer.fromBinary).toBeDefined();
            expect(mockSerializer.getSupportedVersions).toBeDefined();
        });
    });
    describe('Data Transformation Interface', () => {
        it('should define transformation methods', () => {
            // Mock transformer implementation
            const mockTransformer = {
                transform: jest.fn().mockReturnValue(mockSecurityEventData),
                transformToExternal: jest.fn().mockReturnValue({}),
                getSupportedSourceTypes: jest.fn().mockReturnValue([event_source_type_enum_1.EventSourceType.SIEM]),
                getSupportedTargetFormats: jest.fn().mockReturnValue(['json', 'xml']),
            };
            expect(mockTransformer.transform).toBeDefined();
            expect(mockTransformer.transformToExternal).toBeDefined();
            expect(mockTransformer.getSupportedSourceTypes).toBeDefined();
            expect(mockTransformer.getSupportedTargetFormats).toBeDefined();
        });
    });
    describe('Version Compatibility', () => {
        it('should support version checking', () => {
            const versions = ['1.0.0', '1.1.0', '2.0.0'];
            versions.forEach(version => {
                const versionedData = { ...mockSecurityEventData, version };
                expect(versionedData.version).toBe(version);
            });
        });
        it('should maintain backward compatibility', () => {
            const legacyData = {
                ...mockSecurityEventData,
                version: '0.9.0',
            };
            // Should still be valid SecurityEventData structure
            expect(legacyData.version).toBeDefined();
            expect(legacyData.externalId).toBeDefined();
            expect(legacyData.sourceSystem).toBeDefined();
        });
    });
    describe('External Integration Support', () => {
        it('should support multiple source types', () => {
            const sourceTypes = Object.values(event_source_type_enum_1.EventSourceType);
            sourceTypes.forEach(sourceType => {
                const eventData = { ...mockSecurityEventData, sourceType };
                expect(eventData.sourceType).toBe(sourceType);
            });
        });
        it('should support custom attributes', () => {
            const customAttributes = {
                customField1: 'value1',
                customField2: 123,
                customField3: { nested: 'object' },
            };
            const eventWithCustom = {
                ...mockSecurityEventData,
                attributes: customAttributes,
            };
            expect(eventWithCustom.attributes).toEqual(customAttributes);
        });
        it('should support extensible metadata', () => {
            const extendedMetadata = {
                ...mockSourceMetadata,
                sourceIds: {
                    ...mockSourceMetadata.sourceIds,
                    'vendor_id': 'vendor-123',
                    'correlation_id': 'corr-456',
                },
            };
            const eventWithExtended = {
                ...mockSecurityEventData,
                sourceMetadata: extendedMetadata,
            };
            expect(eventWithExtended.sourceMetadata.sourceIds).toHaveProperty('vendor_id');
            expect(eventWithExtended.sourceMetadata.sourceIds).toHaveProperty('correlation_id');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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