{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\entities\\report-definition.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAQiB;AACjB,uEAA4D;AAE5D;;;;;;;;;;;GAWG;AAKI,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IA6P3B,yBAAyB;IAEzB;;OAEG;IACH,aAAa,CAAC,MAAc,EAAE,SAAmB,EAAE,UAAkB;QACnE,mBAAmB;QACnB,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;YAC3E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,kCAAkC;QAClC,IAAI,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;QAC7D,CAAC;QAED,+BAA+B;QAC/B,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QACvF,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;QAC7D,CAAC;QAED,iCAAiC;QACjC,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,KAAK,cAAc,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACnG,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;QAC7D,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,MAAc,EAAE,SAAmB,EAAE,cAAmC;QACzF,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;YACtC,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YAC/C,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QAC1B,CAAC;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YAC/C,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;QAChC,CAAC;QAED,4BAA4B;QAC5B,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAEjE,iCAAiC;YACjC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBACnD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC/E,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACzC,OAAO,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,YAAY;QACV,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC;QACvD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE,CAAC;QAChE,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,0BAA0B;QAEzF,OAAO,QAAQ,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,oBAAoB,IAAI,KAAK,CAAC,CAAC,qBAAqB;IACjF,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE;YAC3D,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;YAC9D,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,SAAS;YAClD,OAAO,aAAa,GAAG,UAAU,CAAC;QACpC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC;QAEf,uDAAuD;QACvD,OAAO,gBAAgB,GAAG,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,aAAqB;QACpC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;QACrC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9C,gCAAgC;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,oBAAoB,IAAI,CAAC,CAAC;QACjE,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC;QACjD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,oBAAoB;YACtC,CAAC,UAAU,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC,GAAG,KAAK,CAAC;QAErD,mEAAmE;QACnE,MAAM,sBAAsB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAC3H,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,sBAAsB,GAAG,CAAC,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,qCAAqC;QACrC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjF,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrF,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzE,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC9D,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,KAAK,QAAQ;YAC1C,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC;YAC5C,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QACpE,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAe,EAAE,UAAkB;QACvC,OAAO;YACL,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACnE,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACzE,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC3D,aAAa,EAAE;gBACb,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACjD,UAAU,EAAE,SAAS;gBACrB,YAAY,EAAE,EAAE;gBAChB,YAAY,EAAE,EAAE;aACjB;YACD,gBAAgB,EAAE,IAAI;YACtB,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACrE,QAAQ,EAAE,IAAI;YACd,UAAU,EAAE,KAAK;YACjB,UAAU,EAAE,IAAI,CAAC,EAAE;YACnB,OAAO,EAAE,UAAU;YACnB,QAAQ,EAAE;gBACR,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC7B,OAAO,EAAE,KAAK;gBACd,cAAc,EAAE,UAAU;gBAC1B,SAAS,EAAE,EAAE;gBACb,KAAK,EAAE;oBACL,cAAc,EAAE,CAAC;oBACjB,YAAY,EAAE,IAAI;oBAClB,oBAAoB,EAAE,CAAC;oBACvB,eAAe,EAAE,CAAC;iBACnB;gBACD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;aACjE;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AAvcY,4CAAgB;AAE3B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;4CACpB;AAIX;IAFC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;IACvB,IAAA,eAAK,GAAE;;8CACK;AAGb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDACrB;AAoBpB;IAlBC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,sBAAsB;YACtB,mBAAmB;YACnB,eAAe;YACf,eAAe;YACf,iBAAiB;YACjB,kBAAkB;YAClB,uBAAuB;YACvB,oBAAoB;YACpB,gBAAgB;YAChB,mBAAmB;YACnB,mBAAmB;YACnB,eAAe;SAChB;KACF,CAAC;IACD,IAAA,eAAK,GAAE;;oDACW;AAgBnB;IAdC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,YAAY;YACZ,UAAU;YACV,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;YACX,WAAW;YACX,YAAY;SACb;KACF,CAAC;IACD,IAAA,eAAK,GAAE;;kDACS;AAOjB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;QAC3C,OAAO,EAAE,QAAQ;KAClB,CAAC;;kDACe;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;0DAoBxB;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;6DAyCxB;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;sDAuBxB;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;uDAkBxB;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DAajC;AAGT;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;2DAuBxB;AAIF;IAFC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IACzB,IAAA,eAAK,GAAE;;kDACU;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;oDACP;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDACR;AAInB;IAFC,IAAA,gBAAM,GAAE;IACR,IAAA,eAAK,GAAE;;iDACQ;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;kDAuBxB;AAGF;IADC,IAAA,0BAAgB,GAAE;kDACR,IAAI,oBAAJ,IAAI;mDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;kDACR,IAAI,oBAAJ,IAAI;mDAAC;AAIhB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,yCAAe,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,gBAAgB,CAAC;;oDAC5C;2BA3PnB,gBAAgB;IAJ5B,IAAA,gBAAM,EAAC,oBAAoB,CAAC;IAC5B,IAAA,eAAK,EAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAC/B,IAAA,eAAK,EAAC,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;IAClC,IAAA,eAAK,EAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;GAClB,gBAAgB,CAuc5B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\entities\\report-definition.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n} from 'typeorm';\r\nimport { ReportExecution } from './report-execution.entity';\r\n\r\n/**\r\n * Report Definition Entity\r\n * \r\n * Defines the structure and configuration for reports including:\r\n * - Report metadata and categorization\r\n * - Data source configuration and query definitions\r\n * - Visualization settings and chart configurations\r\n * - Export formats and delivery options\r\n * - Access control and sharing permissions\r\n * - Scheduling and automation settings\r\n * - Performance optimization parameters\r\n */\r\n@Entity('report_definitions')\r\n@Index(['category', 'isActive'])\r\n@Index(['reportType', 'createdAt'])\r\n@Index(['ownerId', 'isActive'])\r\nexport class ReportDefinition {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  @Column({ length: 255 })\r\n  @Index()\r\n  name: string;\r\n\r\n  @Column({ type: 'text', nullable: true })\r\n  description: string;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'compliance_dashboard',\r\n      'assessment_report',\r\n      'audit_summary',\r\n      'risk_analysis',\r\n      'evidence_report',\r\n      'framework_status',\r\n      'control_effectiveness',\r\n      'violation_analysis',\r\n      'trend_analysis',\r\n      'executive_summary',\r\n      'regulatory_report',\r\n      'custom_report',\r\n    ],\r\n  })\r\n  @Index()\r\n  reportType: string;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'compliance',\r\n      'security',\r\n      'risk',\r\n      'audit',\r\n      'operational',\r\n      'financial',\r\n      'executive',\r\n      'regulatory',\r\n    ],\r\n  })\r\n  @Index()\r\n  category: string;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high', 'critical'],\r\n    default: 'medium',\r\n  })\r\n  priority: string;\r\n\r\n  @Column({ type: 'jsonb' })\r\n  dataSourceConfig: {\r\n    sources: {\r\n      type: 'compliance' | 'audit' | 'asset' | 'user' | 'external';\r\n      endpoint?: string;\r\n      query?: string;\r\n      filters?: Record<string, any>;\r\n      aggregations?: Record<string, any>;\r\n    }[];\r\n    joinConditions?: {\r\n      leftSource: string;\r\n      rightSource: string;\r\n      joinType: 'inner' | 'left' | 'right' | 'full';\r\n      condition: string;\r\n    }[];\r\n    cacheConfig?: {\r\n      enabled: boolean;\r\n      ttl: number;\r\n      refreshStrategy: 'manual' | 'scheduled' | 'event_driven';\r\n    };\r\n  };\r\n\r\n  @Column({ type: 'jsonb' })\r\n  visualizationConfig: {\r\n    chartType: 'bar' | 'line' | 'pie' | 'donut' | 'scatter' | 'heatmap' | 'gauge' | 'table' | 'card';\r\n    layout: {\r\n      width: number;\r\n      height: number;\r\n      position?: { x: number; y: number };\r\n      responsive: boolean;\r\n    };\r\n    styling: {\r\n      colors: string[];\r\n      theme: 'light' | 'dark' | 'auto';\r\n      fontSize: number;\r\n      fontFamily: string;\r\n    };\r\n    axes?: {\r\n      xAxis: {\r\n        label: string;\r\n        type: 'category' | 'numeric' | 'datetime';\r\n        format?: string;\r\n      };\r\n      yAxis: {\r\n        label: string;\r\n        type: 'numeric' | 'percentage';\r\n        format?: string;\r\n        min?: number;\r\n        max?: number;\r\n      };\r\n    };\r\n    series: {\r\n      name: string;\r\n      dataField: string;\r\n      aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max';\r\n      color?: string;\r\n    }[];\r\n    interactivity: {\r\n      drillDown: boolean;\r\n      filtering: boolean;\r\n      sorting: boolean;\r\n      export: boolean;\r\n    };\r\n  };\r\n\r\n  @Column({ type: 'jsonb' })\r\n  exportConfig: {\r\n    formats: ('pdf' | 'excel' | 'csv' | 'json' | 'png' | 'svg')[];\r\n    defaultFormat: string;\r\n    pdfSettings?: {\r\n      orientation: 'portrait' | 'landscape';\r\n      pageSize: 'A4' | 'A3' | 'letter' | 'legal';\r\n      margins: { top: number; right: number; bottom: number; left: number };\r\n      includeCharts: boolean;\r\n      includeData: boolean;\r\n    };\r\n    excelSettings?: {\r\n      includeCharts: boolean;\r\n      includeRawData: boolean;\r\n      sheetNames: string[];\r\n      formatting: boolean;\r\n    };\r\n    deliveryOptions: {\r\n      email: boolean;\r\n      download: boolean;\r\n      webhook: boolean;\r\n      storage: boolean;\r\n    };\r\n  };\r\n\r\n  @Column({ type: 'jsonb' })\r\n  accessControl: {\r\n    visibility: 'private' | 'shared' | 'public' | 'organization';\r\n    allowedRoles: string[];\r\n    allowedUsers: string[];\r\n    permissions: {\r\n      view: boolean;\r\n      edit: boolean;\r\n      delete: boolean;\r\n      export: boolean;\r\n      share: boolean;\r\n    };\r\n    dataFiltering?: {\r\n      userBased: boolean;\r\n      roleBased: boolean;\r\n      attributeBased: boolean;\r\n      filters: Record<string, any>;\r\n    };\r\n  };\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  schedulingConfig: {\r\n    enabled: boolean;\r\n    frequency: 'hourly' | 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';\r\n    cronExpression?: string;\r\n    timezone: string;\r\n    recipients: {\r\n      users: string[];\r\n      roles: string[];\r\n      emails: string[];\r\n    };\r\n    deliveryMethod: 'email' | 'webhook' | 'storage' | 'dashboard';\r\n    retentionDays: number;\r\n  } | null;\r\n\r\n  @Column({ type: 'jsonb' })\r\n  performanceConfig: {\r\n    caching: {\r\n      enabled: boolean;\r\n      strategy: 'memory' | 'redis' | 'database';\r\n      ttl: number;\r\n      invalidationRules: string[];\r\n    };\r\n    optimization: {\r\n      dataLimit: number;\r\n      queryTimeout: number;\r\n      parallelProcessing: boolean;\r\n      indexHints: string[];\r\n    };\r\n    monitoring: {\r\n      trackExecutionTime: boolean;\r\n      trackDataVolume: boolean;\r\n      alertThresholds: {\r\n        executionTime: number;\r\n        dataVolume: number;\r\n        errorRate: number;\r\n      };\r\n    };\r\n  };\r\n\r\n  @Column({ default: true })\r\n  @Index()\r\n  isActive: boolean;\r\n\r\n  @Column({ default: false })\r\n  isTemplate: boolean;\r\n\r\n  @Column({ nullable: true })\r\n  templateId: string;\r\n\r\n  @Column()\r\n  @Index()\r\n  ownerId: string;\r\n\r\n  @Column({ type: 'jsonb' })\r\n  metadata: {\r\n    tags: string[];\r\n    version: string;\r\n    lastModifiedBy: string;\r\n    changeLog: {\r\n      version: string;\r\n      changes: string[];\r\n      modifiedBy: string;\r\n      modifiedAt: Date;\r\n    }[];\r\n    usage: {\r\n      executionCount: number;\r\n      lastExecuted: Date;\r\n      averageExecutionTime: number;\r\n      popularityScore: number;\r\n    };\r\n    compliance: {\r\n      frameworks: string[];\r\n      dataClassification: 'public' | 'internal' | 'confidential' | 'restricted';\r\n      retentionPeriod: number;\r\n      auditRequired: boolean;\r\n    };\r\n  };\r\n\r\n  @CreateDateColumn()\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn()\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @OneToMany(() => ReportExecution, execution => execution.reportDefinition)\r\n  executions: ReportExecution[];\r\n\r\n  // Business Logic Methods\r\n\r\n  /**\r\n   * Check if user has permission to access this report\r\n   */\r\n  hasUserAccess(userId: string, userRoles: string[], permission: string): boolean {\r\n    // Check visibility\r\n    if (this.accessControl.visibility === 'private' && this.ownerId !== userId) {\r\n      return false;\r\n    }\r\n\r\n    // Check user-specific permissions\r\n    if (this.accessControl.allowedUsers.includes(userId)) {\r\n      return this.accessControl.permissions[permission] || false;\r\n    }\r\n\r\n    // Check role-based permissions\r\n    const hasRole = userRoles.some(role => this.accessControl.allowedRoles.includes(role));\r\n    if (hasRole) {\r\n      return this.accessControl.permissions[permission] || false;\r\n    }\r\n\r\n    // Check organization-wide access\r\n    if (this.accessControl.visibility === 'organization' || this.accessControl.visibility === 'public') {\r\n      return this.accessControl.permissions[permission] || false;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Get data filters for user-specific data access\r\n   */\r\n  getUserDataFilters(userId: string, userRoles: string[], userAttributes: Record<string, any>): Record<string, any> {\r\n    const filters: Record<string, any> = {};\r\n\r\n    if (!this.accessControl.dataFiltering) {\r\n      return filters;\r\n    }\r\n\r\n    // User-based filtering\r\n    if (this.accessControl.dataFiltering.userBased) {\r\n      filters.userId = userId;\r\n    }\r\n\r\n    // Role-based filtering\r\n    if (this.accessControl.dataFiltering.roleBased) {\r\n      filters.userRoles = userRoles;\r\n    }\r\n\r\n    // Attribute-based filtering\r\n    if (this.accessControl.dataFiltering.attributeBased) {\r\n      Object.assign(filters, this.accessControl.dataFiltering.filters);\r\n      \r\n      // Replace attribute placeholders\r\n      for (const [key, value] of Object.entries(filters)) {\r\n        if (typeof value === 'string' && value.startsWith('${') && value.endsWith('}')) {\r\n          const attributeName = value.slice(2, -1);\r\n          filters[key] = userAttributes[attributeName];\r\n        }\r\n      }\r\n    }\r\n\r\n    return filters;\r\n  }\r\n\r\n  /**\r\n   * Check if report needs refresh based on cache configuration\r\n   */\r\n  needsRefresh(): boolean {\r\n    if (!this.dataSourceConfig.cacheConfig?.enabled) {\r\n      return true;\r\n    }\r\n\r\n    const lastExecution = this.metadata.usage.lastExecuted;\r\n    if (!lastExecution) {\r\n      return true;\r\n    }\r\n\r\n    const cacheAge = Date.now() - new Date(lastExecution).getTime();\r\n    const cacheTtl = this.dataSourceConfig.cacheConfig.ttl * 1000; // Convert to milliseconds\r\n\r\n    return cacheAge > cacheTtl;\r\n  }\r\n\r\n  /**\r\n   * Get estimated execution time based on historical data\r\n   */\r\n  getEstimatedExecutionTime(): number {\r\n    return this.metadata.usage.averageExecutionTime || 30000; // Default 30 seconds\r\n  }\r\n\r\n  /**\r\n   * Check if report execution should be throttled\r\n   */\r\n  shouldThrottle(): boolean {\r\n    const recentExecutions = this.executions?.filter(execution => {\r\n      const executionTime = new Date(execution.startedAt).getTime();\r\n      const oneHourAgo = Date.now() - 3600000; // 1 hour\r\n      return executionTime > oneHourAgo;\r\n    }).length || 0;\r\n\r\n    // Throttle if more than 10 executions in the last hour\r\n    return recentExecutions > 10;\r\n  }\r\n\r\n  /**\r\n   * Update usage statistics\r\n   */\r\n  updateUsageStats(executionTime: number): void {\r\n    this.metadata.usage.executionCount++;\r\n    this.metadata.usage.lastExecuted = new Date();\r\n    \r\n    // Update average execution time\r\n    const currentAvg = this.metadata.usage.averageExecutionTime || 0;\r\n    const count = this.metadata.usage.executionCount;\r\n    this.metadata.usage.averageExecutionTime = \r\n      (currentAvg * (count - 1) + executionTime) / count;\r\n\r\n    // Update popularity score (simple algorithm based on recent usage)\r\n    const daysSinceLastExecution = (Date.now() - new Date(this.metadata.usage.lastExecuted).getTime()) / (1000 * 60 * 60 * 24);\r\n    this.metadata.usage.popularityScore = Math.max(0, 100 - daysSinceLastExecution * 2);\r\n  }\r\n\r\n  /**\r\n   * Validate report configuration\r\n   */\r\n  validateConfiguration(): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    // Validate data source configuration\r\n    if (!this.dataSourceConfig.sources || this.dataSourceConfig.sources.length === 0) {\r\n      errors.push('At least one data source must be configured');\r\n    }\r\n\r\n    // Validate visualization configuration\r\n    if (!this.visualizationConfig.chartType) {\r\n      errors.push('Chart type must be specified');\r\n    }\r\n\r\n    if (!this.visualizationConfig.series || this.visualizationConfig.series.length === 0) {\r\n      errors.push('At least one data series must be configured');\r\n    }\r\n\r\n    // Validate export configuration\r\n    if (!this.exportConfig.formats || this.exportConfig.formats.length === 0) {\r\n      errors.push('At least one export format must be specified');\r\n    }\r\n\r\n    // Validate access control\r\n    if (this.accessControl.visibility === 'shared' && \r\n        this.accessControl.allowedUsers.length === 0 && \r\n        this.accessControl.allowedRoles.length === 0) {\r\n      errors.push('Shared reports must specify allowed users or roles');\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clone report definition for templating\r\n   */\r\n  clone(newName: string, newOwnerId: string): Partial<ReportDefinition> {\r\n    return {\r\n      name: newName,\r\n      description: this.description,\r\n      reportType: this.reportType,\r\n      category: this.category,\r\n      priority: this.priority,\r\n      dataSourceConfig: JSON.parse(JSON.stringify(this.dataSourceConfig)),\r\n      visualizationConfig: JSON.parse(JSON.stringify(this.visualizationConfig)),\r\n      exportConfig: JSON.parse(JSON.stringify(this.exportConfig)),\r\n      accessControl: {\r\n        ...JSON.parse(JSON.stringify(this.accessControl)),\r\n        visibility: 'private',\r\n        allowedUsers: [],\r\n        allowedRoles: [],\r\n      },\r\n      schedulingConfig: null,\r\n      performanceConfig: JSON.parse(JSON.stringify(this.performanceConfig)),\r\n      isActive: true,\r\n      isTemplate: false,\r\n      templateId: this.id,\r\n      ownerId: newOwnerId,\r\n      metadata: {\r\n        tags: [...this.metadata.tags],\r\n        version: '1.0',\r\n        lastModifiedBy: newOwnerId,\r\n        changeLog: [],\r\n        usage: {\r\n          executionCount: 0,\r\n          lastExecuted: null,\r\n          averageExecutionTime: 0,\r\n          popularityScore: 0,\r\n        },\r\n        compliance: JSON.parse(JSON.stringify(this.metadata.compliance)),\r\n      },\r\n    };\r\n  }\r\n}\r\n"], "version": 3}