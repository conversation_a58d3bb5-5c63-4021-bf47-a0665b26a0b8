{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\__tests__\\database.config.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAA+C;AAE/C,wDAS4B;AAE5B,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;IAC5C,IAAI,OAAqC,CAAC;IAC1C,IAAI,aAAyC,CAAC;IAC9C,IAAI,kBAAwC,CAAC;IAC7C,IAAI,WAA8B,CAAC;IAEnC,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,6BAA6B;QAC7B,WAAW,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;QAEjC,0BAA0B;QAC1B,OAAO,CAAC,GAAG,GAAG;YACZ,QAAQ,EAAE,MAAM;YAChB,aAAa,EAAE,UAAU;YACzB,aAAa,EAAE,WAAW;YAC1B,aAAa,EAAE,MAAM;YACrB,iBAAiB,EAAE,UAAU;YAC7B,iBAAiB,EAAE,UAAU;YAC7B,aAAa,EAAE,QAAQ;YACvB,eAAe,EAAE,QAAQ;SAC1B,CAAC;QAEF,uBAAuB;QACvB,kBAAkB,GAAG;YACnB,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,UAAU;YACpB,QAAQ,EAAE,UAAU;YACpB,QAAQ,EAAE,QAAQ;YAClB,MAAM,EAAE,QAAQ;YAChB,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,EAAE;YACd,WAAW,EAAE,EAAE;YACf,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,qBAAqB;QACrB,aAAa,GAAG;YACd,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,kBAAkB,CAAC;SAC5C,CAAC;QAET,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,8CAA4B;gBAC5B;oBACE,OAAO,EAAE,sBAAa;oBACtB,QAAQ,EAAE,aAAa;iBACxB;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAA+B,8CAA4B,CAAC,CAAC;IACnF,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,+BAA+B;QAC/B,OAAO,CAAC,GAAG,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;YAC3B,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;YACxB,MAAM,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACtC,MAAM,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;YACpC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;YAEhC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,OAAO,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAE5C,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC5C,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,gBAAgB,GAAG,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAEvD,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE3C,wBAAwB;YACxB,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,MAAM,CAAC;YACrC,MAAM,UAAU,GAAG,IAAI,8CAA4B,CAAC,aAAa,CAAC,CAAC;YACnE,MAAM,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjD,gCAAgC;YAChC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,GAAG,OAAO,CAAC;YACjD,MAAM,gBAAgB,GAAG,IAAI,8CAA4B,CAAC,aAAa,CAAC,CAAC;YACzE,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,iFAAiF;YACjF,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEpD,kCAAkC;YAClC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,MAAM,CAAC;YAC7C,MAAM,cAAc,GAAG,IAAI,8CAA4B,CAAC,aAAa,CAAC,CAAC;YACvE,MAAM,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,aAAa,CAAC;YAExC,MAAM,UAAU,GAAG,IAAI,8CAA4B,CAAC,aAAa,CAAC,CAAC;YAEnE,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;YAEvC,MAAM,WAAW,GAAG,IAAI,8CAA4B,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC;YAEjC,MAAM,WAAW,GAAG,IAAI,8CAA4B,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;IAC9C,IAAI,WAA8B,CAAC;IAEnC,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,OAAO,CAAC,GAAG,GAAG,WAAW,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,MAAM;aACjB,CAAC;YAEF,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAEhC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,mBAAmB;YAClE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,aAAa;gBACvB,aAAa,EAAE,OAAO;gBACtB,aAAa,EAAE,gBAAgB;gBAC/B,aAAa,EAAE,MAAM;gBACrB,iBAAiB,EAAE,QAAQ;gBAC3B,iBAAiB,EAAE,QAAQ;gBAC3B,aAAa,EAAE,MAAM;gBACrB,eAAe,EAAE,UAAU;gBAC3B,YAAY,EAAE,MAAM;gBACpB,oBAAoB,EAAE,MAAM;gBAC5B,wBAAwB,EAAE,MAAM;gBAChC,oBAAoB,EAAE,MAAM;aAC7B,CAAC;YAEF,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAEhC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,YAAY;gBACtB,YAAY,EAAE,MAAM;gBACpB,gCAAgC,EAAE,MAAM;gBACxC,eAAe,EAAE,SAAS;gBAC1B,iBAAiB,EAAE,aAAa;gBAChC,gBAAgB,EAAE,YAAY;aAC/B,CAAC;YAEF,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAEhC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;gBACzB,kBAAkB,EAAE,IAAI;gBACxB,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,aAAa;gBACnB,GAAG,EAAE,YAAY;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,YAAY;gBACtB,iBAAiB,EAAE,GAAG;gBACtB,iBAAiB,EAAE,IAAI;gBACvB,6BAA6B,EAAE,OAAO;gBACtC,4BAA4B,EAAE,OAAO;aACtC,CAAC;YAEF,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAEhC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBACnD,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,EAAE;gBACP,oBAAoB,EAAE,KAAK;gBAC3B,mBAAmB,EAAE,KAAK;aAC3B,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,YAAY;gBACtB,uBAAuB,EAAE,MAAM;gBAC/B,yBAAyB,EAAE,mBAAmB;gBAC9C,6CAA6C,EAAE,OAAO;aACvD,CAAC;YAEF,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAEhC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,YAAY;gBACtB,aAAa,EAAE,MAAM;gBACrB,UAAU,EAAE,mBAAmB;gBAC/B,UAAU,EAAE,MAAM;gBAClB,cAAc,EAAE,WAAW;gBAC3B,cAAc,EAAE,GAAG;gBACnB,uBAAuB,EAAE,OAAO;aACjC,CAAC;YAEF,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAS,CAAC;YAEvC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;gBAC3B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE;oBACP,IAAI,EAAE,mBAAmB;oBACzB,IAAI,EAAE,IAAI;oBACV,QAAQ,EAAE,WAAW;oBACrB,EAAE,EAAE,CAAC;iBACN;gBACD,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,YAAY;gBACtB,aAAa,EAAE,OAAO;aACvB,CAAC;YAEF,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAS,CAAC;YAEvC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,MAAM;gBAChB,aAAa,EAAE,cAAc,EAAE,8BAA8B;gBAC7D,aAAa,EAAE,cAAc,EAAE,0BAA0B;gBACzD,iBAAiB,EAAE,gBAAgB,EAAE,6BAA6B;aACnE,CAAC;YAEF,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAEhC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW;YACjD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YAC3C,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,0BAA0B;YAC1B,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,aAAa;gBACvB,wBAAwB,EAAE,MAAM;gBAChC,oBAAoB,EAAE,MAAM;aAC7B,CAAC;YAEF,IAAI,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAC9B,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;YAEnE,yBAAyB;YACzB,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,YAAY;gBACtB,wBAAwB,EAAE,MAAM;aACjC,CAAC;YAEF,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAC1B,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YAE1C,mBAAmB;YACnB,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,aAAa;gBACvB,wBAAwB,EAAE,OAAO;aAClC,CAAC;YAEF,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAC1B,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAEhC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC;gBACrD,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC;aACvC,CAAC,CAAC,CAAC;YACJ,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC;gBACvD,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,CAAC;aACjD,CAAC,CAAC,CAAC;YACJ,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC;gBACxD,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC;aAClD,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,UAAU,GAAmB,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAEpE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACxB,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;gBACpC,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;gBAChC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,iBAAiB,CAAC;YAEjD,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,MAAM;gBAChB,aAAa,EAAE,OAAO;aACvB,CAAC;YAEF,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,aAAa;gBACvB,aAAa,EAAE,OAAO;aACvB,CAAC;YAEF,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,OAAO,CAAC,GAAG,GAAG;gBACZ,QAAQ,EAAE,MAAM;aACjB,CAAC;YAEF,MAAM,MAAM,GAAG,IAAA,gCAAc,GAAE,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\__tests__\\database.config.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { TypeOrmModuleOptions } from '@nestjs/typeorm';\r\nimport {\r\n  DatabaseConfigurationService,\r\n  databaseConfig,\r\n  DatabaseType,\r\n  DatabaseConfig,\r\n  DatabasePoolConfig,\r\n  DatabaseSSLConfig,\r\n  DatabaseMigrationConfig,\r\n  DatabaseLoggingConfig\r\n} from '../database.config';\r\n\r\ndescribe('DatabaseConfigurationService', () => {\r\n  let service: DatabaseConfigurationService;\r\n  let configService: jest.Mocked<ConfigService>;\r\n  let mockTypeOrmOptions: TypeOrmModuleOptions;\r\n  let originalEnv: NodeJS.ProcessEnv;\r\n\r\n  beforeEach(async () => {\r\n    // Store original environment\r\n    originalEnv = { ...process.env };\r\n\r\n    // Set up test environment\r\n    process.env = {\r\n      NODE_ENV: 'test',\r\n      DATABASE_TYPE: 'postgres',\r\n      DATABASE_HOST: 'localhost',\r\n      DATABASE_PORT: '5432',\r\n      DATABASE_USERNAME: 'testuser',\r\n      DATABASE_PASSWORD: 'testpass',\r\n      DATABASE_NAME: 'testdb',\r\n      DATABASE_SCHEMA: 'public',\r\n    };\r\n\r\n    // Mock TypeORM options\r\n    mockTypeOrmOptions = {\r\n      type: 'postgres',\r\n      host: 'localhost',\r\n      port: 5432,\r\n      username: 'testuser',\r\n      password: 'testpass',\r\n      database: 'testdb',\r\n      schema: 'public',\r\n      entities: [],\r\n      migrations: [],\r\n      subscribers: [],\r\n      synchronize: false,\r\n      logging: false,\r\n    };\r\n\r\n    // Mock ConfigService\r\n    configService = {\r\n      get: jest.fn().mockReturnValue(mockTypeOrmOptions),\r\n    } as any;\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        DatabaseConfigurationService,\r\n        {\r\n          provide: ConfigService,\r\n          useValue: configService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<DatabaseConfigurationService>(DatabaseConfigurationService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    // Restore original environment\r\n    process.env = originalEnv;\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('constructor', () => {\r\n    it('should be defined', () => {\r\n      expect(service).toBeDefined();\r\n    });\r\n\r\n    it('should load configuration from environment', () => {\r\n      expect(service.type).toBe('postgres');\r\n      expect(service.host).toBe('localhost');\r\n      expect(service.port).toBe(5432);\r\n      expect(service.username).toBe('testuser');\r\n      expect(service.database).toBe('testdb');\r\n    });\r\n  });\r\n\r\n  describe('configuration getters', () => {\r\n    it('should return database type', () => {\r\n      expect(service.type).toBe('postgres');\r\n    });\r\n\r\n    it('should return database host', () => {\r\n      expect(service.host).toBe('localhost');\r\n    });\r\n\r\n    it('should return database port', () => {\r\n      expect(service.port).toBe(5432);\r\n    });\r\n\r\n    it('should return database username', () => {\r\n      expect(service.username).toBe('testuser');\r\n    });\r\n\r\n    it('should return database password', () => {\r\n      expect(service.password).toBe('testpass');\r\n    });\r\n\r\n    it('should return database name', () => {\r\n      expect(service.database).toBe('testdb');\r\n    });\r\n\r\n    it('should return database schema', () => {\r\n      expect(service.schema).toBe('public');\r\n    });\r\n\r\n    it('should return SSL configuration', () => {\r\n      const ssl = service.ssl;\r\n      expect(ssl).toHaveProperty('enabled');\r\n      expect(ssl).toHaveProperty('rejectUnauthorized');\r\n    });\r\n\r\n    it('should return pool configuration', () => {\r\n      const pool = service.pool;\r\n      expect(pool).toHaveProperty('min');\r\n      expect(pool).toHaveProperty('max');\r\n      expect(pool).toHaveProperty('acquireTimeoutMillis');\r\n    });\r\n\r\n    it('should return migration configuration', () => {\r\n      const migration = service.migration;\r\n      expect(migration).toHaveProperty('run');\r\n      expect(migration).toHaveProperty('tableName');\r\n      expect(migration).toHaveProperty('directory');\r\n    });\r\n\r\n    it('should return logging configuration', () => {\r\n      const logging = service.logging;\r\n      expect(logging).toHaveProperty('enabled');\r\n      expect(logging).toHaveProperty('logQueries');\r\n      expect(logging).toHaveProperty('logErrors');\r\n    });\r\n  });\r\n\r\n  describe('utility methods', () => {\r\n    it('should return complete configuration', () => {\r\n      const config = service.getAll();\r\n      \r\n      expect(config).toHaveProperty('type');\r\n      expect(config).toHaveProperty('host');\r\n      expect(config).toHaveProperty('port');\r\n      expect(config).toHaveProperty('ssl');\r\n      expect(config).toHaveProperty('pool');\r\n      expect(config).toHaveProperty('migration');\r\n      expect(config).toHaveProperty('logging');\r\n    });\r\n\r\n    it('should return TypeORM options', () => {\r\n      const options = service.getTypeOrmOptions();\r\n      \r\n      expect(options).toEqual(mockTypeOrmOptions);\r\n      expect(configService.get).toHaveBeenCalledWith('database');\r\n    });\r\n\r\n    it('should generate connection string', () => {\r\n      const connectionString = service.getConnectionString();\r\n      \r\n      expect(connectionString).toBe('postgres://testuser:testpass@localhost:5432/testdb');\r\n    });\r\n\r\n    it('should check SSL status', () => {\r\n      expect(service.isSSLEnabled()).toBe(false);\r\n      \r\n      // Test with SSL enabled\r\n      process.env['DATABASE_SSL'] = 'true';\r\n      const sslService = new DatabaseConfigurationService(configService);\r\n      expect(sslService.isSSLEnabled()).toBe(true);\r\n    });\r\n\r\n    it('should check migration status', () => {\r\n      expect(service.shouldRunMigrations()).toBe(true);\r\n      \r\n      // Test with migrations disabled\r\n      process.env['DATABASE_MIGRATIONS_RUN'] = 'false';\r\n      const migrationService = new DatabaseConfigurationService(configService);\r\n      expect(migrationService.shouldRunMigrations()).toBe(false);\r\n    });\r\n\r\n    it('should check query logging status', () => {\r\n      // In test environment, logging should be enabled but queries disabled by default\r\n      expect(service.isQueryLoggingEnabled()).toBe(false);\r\n      \r\n      // Test with query logging enabled\r\n      process.env['DATABASE_LOG_QUERIES'] = 'true';\r\n      const loggingService = new DatabaseConfigurationService(configService);\r\n      expect(loggingService.isQueryLoggingEnabled()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('environment-specific behavior', () => {\r\n    it('should configure for development environment', () => {\r\n      process.env['NODE_ENV'] = 'development';\r\n      \r\n      const devService = new DatabaseConfigurationService(configService);\r\n      \r\n      expect(devService.synchronize).toBe(true);\r\n      expect(devService.logging.enabled).toBe(true);\r\n    });\r\n\r\n    it('should configure for production environment', () => {\r\n      process.env['NODE_ENV'] = 'production';\r\n      \r\n      const prodService = new DatabaseConfigurationService(configService);\r\n      \r\n      expect(prodService.synchronize).toBe(false);\r\n      expect(prodService.logging.enabled).toBe(false);\r\n    });\r\n\r\n    it('should configure for test environment', () => {\r\n      process.env['NODE_ENV'] = 'test';\r\n      \r\n      const testService = new DatabaseConfigurationService(configService);\r\n      \r\n      expect(testService.synchronize).toBe(false);\r\n      expect(testService.logging.enabled).toBe(false);\r\n    });\r\n  });\r\n});\r\n\r\ndescribe('Database Configuration Factory', () => {\r\n  let originalEnv: NodeJS.ProcessEnv;\r\n\r\n  beforeEach(() => {\r\n    originalEnv = { ...process.env };\r\n  });\r\n\r\n  afterEach(() => {\r\n    process.env = originalEnv;\r\n  });\r\n\r\n  describe('databaseConfig factory', () => {\r\n    it('should create TypeORM configuration with defaults', () => {\r\n      process.env = {\r\n        NODE_ENV: 'test',\r\n      };\r\n\r\n      const config = databaseConfig();\r\n      \r\n      expect(config.type).toBe('postgres');\r\n      expect(config.host).toBe('localhost');\r\n      expect(config.port).toBe(5432);\r\n      expect(config.username).toBe('sentinel_user');\r\n      expect(config.password).toBe('sentinel_password');\r\n      expect(config.database).toBe('sentinel_test'); // Test environment\r\n      expect(config.schema).toBe('public');\r\n      expect(config.ssl).toBe(false);\r\n      expect(config.synchronize).toBe(false);\r\n      expect(config.logging).toBe(false);\r\n    });\r\n\r\n    it('should create TypeORM configuration from environment variables', () => {\r\n      process.env = {\r\n        NODE_ENV: 'development',\r\n        DATABASE_TYPE: 'mysql',\r\n        DATABASE_HOST: 'db.example.com',\r\n        DATABASE_PORT: '3306',\r\n        DATABASE_USERNAME: 'myuser',\r\n        DATABASE_PASSWORD: 'mypass',\r\n        DATABASE_NAME: 'mydb',\r\n        DATABASE_SCHEMA: 'myschema',\r\n        DATABASE_SSL: 'true',\r\n        DATABASE_SYNCHRONIZE: 'true',\r\n        DATABASE_LOGGING_ENABLED: 'true',\r\n        DATABASE_LOG_QUERIES: 'true',\r\n      };\r\n\r\n      const config = databaseConfig();\r\n      \r\n      expect(config.type).toBe('mysql');\r\n      expect(config.host).toBe('db.example.com');\r\n      expect(config.port).toBe(3306);\r\n      expect(config.username).toBe('myuser');\r\n      expect(config.password).toBe('mypass');\r\n      expect(config.database).toBe('mydb');\r\n      expect(config.schema).toBe('myschema');\r\n      expect(config.ssl).toBeTruthy();\r\n      expect(config.synchronize).toBe(true);\r\n      expect(config.logging).toEqual(['query', 'error', 'warn', 'info']);\r\n    });\r\n\r\n    it('should configure SSL properly', () => {\r\n      process.env = {\r\n        NODE_ENV: 'production',\r\n        DATABASE_SSL: 'true',\r\n        DATABASE_SSL_REJECT_UNAUTHORIZED: 'true',\r\n        DATABASE_SSL_CA: 'ca-cert',\r\n        DATABASE_SSL_CERT: 'client-cert',\r\n        DATABASE_SSL_KEY: 'client-key',\r\n      };\r\n\r\n      const config = databaseConfig();\r\n      \r\n      expect(config.ssl).toEqual({\r\n        rejectUnauthorized: true,\r\n        ca: 'ca-cert',\r\n        cert: 'client-cert',\r\n        key: 'client-key',\r\n      });\r\n    });\r\n\r\n    it('should configure connection pool', () => {\r\n      process.env = {\r\n        NODE_ENV: 'production',\r\n        DATABASE_POOL_MIN: '5',\r\n        DATABASE_POOL_MAX: '50',\r\n        DATABASE_POOL_ACQUIRE_TIMEOUT: '30000',\r\n        DATABASE_POOL_CREATE_TIMEOUT: '15000',\r\n      };\r\n\r\n      const config = databaseConfig();\r\n      \r\n      expect(config.extra).toEqual(expect.objectContaining({\r\n        min: 5,\r\n        max: 50,\r\n        acquireTimeoutMillis: 30000,\r\n        createTimeoutMillis: 15000,\r\n      }));\r\n    });\r\n\r\n    it('should configure migrations', () => {\r\n      process.env = {\r\n        NODE_ENV: 'production',\r\n        DATABASE_MIGRATIONS_RUN: 'true',\r\n        DATABASE_MIGRATIONS_TABLE: 'custom_migrations',\r\n        DATABASE_MIGRATIONS_TRANSACTION_PER_MIGRATION: 'false',\r\n      };\r\n\r\n      const config = databaseConfig();\r\n      \r\n      expect(config.migrationsRun).toBe(true);\r\n      expect(config.migrationsTableName).toBe('custom_migrations');\r\n      expect(config.migrationsTransactionMode).toBe('all');\r\n    });\r\n\r\n    it('should configure Redis cache when enabled', () => {\r\n      process.env = {\r\n        NODE_ENV: 'production',\r\n        REDIS_ENABLED: 'true',\r\n        REDIS_HOST: 'redis.example.com',\r\n        REDIS_PORT: '6380',\r\n        REDIS_PASSWORD: 'redispass',\r\n        REDIS_CACHE_DB: '2',\r\n        DATABASE_CACHE_DURATION: '60000',\r\n      };\r\n\r\n      const config = databaseConfig() as any;\r\n      \r\n      expect(config.cache).toEqual({\r\n        type: 'redis',\r\n        options: {\r\n          host: 'redis.example.com',\r\n          port: 6380,\r\n          password: 'redispass',\r\n          db: 2,\r\n        },\r\n        duration: 60000,\r\n      });\r\n    });\r\n\r\n    it('should not configure Redis cache when disabled', () => {\r\n      process.env = {\r\n        NODE_ENV: 'production',\r\n        REDIS_ENABLED: 'false',\r\n      };\r\n\r\n      const config = databaseConfig() as any;\r\n      \r\n      expect(config.cache).toBeUndefined();\r\n    });\r\n\r\n    it('should handle invalid environment values gracefully', () => {\r\n      process.env = {\r\n        NODE_ENV: 'test',\r\n        DATABASE_TYPE: 'invalid-type', // Should fallback to postgres\r\n        DATABASE_PORT: 'invalid-port', // Should fallback to 5432\r\n        DATABASE_POOL_MAX: 'invalid-number', // Should fallback to default\r\n      };\r\n\r\n      const config = databaseConfig();\r\n      \r\n      expect(config.type).toBe('postgres'); // Fallback\r\n      expect(config.port).toBe(5432); // Fallback\r\n      expect(config.extra.max).toBe(100); // Default value\r\n    });\r\n\r\n    it('should configure logging based on environment', () => {\r\n      // Development environment\r\n      process.env = {\r\n        NODE_ENV: 'development',\r\n        DATABASE_LOGGING_ENABLED: 'true',\r\n        DATABASE_LOG_QUERIES: 'true',\r\n      };\r\n\r\n      let config = databaseConfig();\r\n      expect(config.logging).toEqual(['query', 'error', 'warn', 'info']);\r\n\r\n      // Production environment\r\n      process.env = {\r\n        NODE_ENV: 'production',\r\n        DATABASE_LOGGING_ENABLED: 'true',\r\n      };\r\n\r\n      config = databaseConfig();\r\n      expect(config.logging).toEqual(['error']);\r\n\r\n      // Logging disabled\r\n      process.env = {\r\n        NODE_ENV: 'development',\r\n        DATABASE_LOGGING_ENABLED: 'false',\r\n      };\r\n\r\n      config = databaseConfig();\r\n      expect(config.logging).toBe(false);\r\n    });\r\n\r\n    it('should set correct entity, migration, and subscriber paths', () => {\r\n      const config = databaseConfig();\r\n      \r\n      expect(config.entities).toEqual(expect.arrayContaining([\r\n        expect.stringContaining('**/*.entity'),\r\n      ]));\r\n      expect(config.migrations).toEqual(expect.arrayContaining([\r\n        expect.stringContaining('database/migrations/*'),\r\n      ]));\r\n      expect(config.subscribers).toEqual(expect.arrayContaining([\r\n        expect.stringContaining('database/subscribers/*'),\r\n      ]));\r\n    });\r\n  });\r\n\r\n  describe('Database Type Validation', () => {\r\n    it('should accept valid database types', () => {\r\n      const validTypes: DatabaseType[] = ['postgres', 'mysql', 'mariadb'];\r\n      \r\n      validTypes.forEach(type => {\r\n        process.env['DATABASE_TYPE'] = type;\r\n        const config = databaseConfig();\r\n        expect(config.type).toBe(type);\r\n      });\r\n    });\r\n\r\n    it('should fallback to postgres for invalid database type', () => {\r\n      process.env['DATABASE_TYPE'] = 'invalid-db-type';\r\n      \r\n      const config = databaseConfig();\r\n      expect(config.type).toBe('postgres');\r\n    });\r\n  });\r\n\r\n  describe('Environment-specific Database Names', () => {\r\n    it('should append _test to database name in test environment', () => {\r\n      process.env = {\r\n        NODE_ENV: 'test',\r\n        DATABASE_NAME: 'myapp',\r\n      };\r\n\r\n      const config = databaseConfig();\r\n      expect(config.database).toBe('myapp');\r\n    });\r\n\r\n    it('should use regular database name in non-test environments', () => {\r\n      process.env = {\r\n        NODE_ENV: 'development',\r\n        DATABASE_NAME: 'myapp',\r\n      };\r\n\r\n      const config = databaseConfig();\r\n      expect(config.database).toBe('myapp');\r\n    });\r\n\r\n    it('should use default test database name when not specified', () => {\r\n      process.env = {\r\n        NODE_ENV: 'test',\r\n      };\r\n\r\n      const config = databaseConfig();\r\n      expect(config.database).toBe('sentinel_test');\r\n    });\r\n  });\r\n});"], "version": 3}