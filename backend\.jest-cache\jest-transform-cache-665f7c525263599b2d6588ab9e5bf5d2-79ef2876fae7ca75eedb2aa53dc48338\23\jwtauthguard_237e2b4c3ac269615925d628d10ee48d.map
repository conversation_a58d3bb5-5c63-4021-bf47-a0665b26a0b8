{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\guards\\jwt-auth.guard.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAKwB;AACxB,+CAA6C;AAC7C,uCAAyC;AAuBzC,6BAA6B;AAC7B,MAAM,UAAU,GAAG,UAAU,CAAC;AAC9B,MAAM,iBAAiB,GAAG,gBAAgB,CAAC;AAE3C;;;GAGG;AAEI,IAAM,YAAY,oBAAlB,MAAM,YAAa,SAAQ,IAAA,oBAAS,EAAC,KAAK,CAAC;IAGhD,YAA6B,SAAoB;QAC/C,KAAK,EAAE,CAAC;QADmB,cAAS,GAAT,SAAS,CAAW;QAFhC,WAAM,GAAG,IAAI,eAAM,CAAC,cAAY,CAAC,IAAI,CAAC,CAAC;IAIxD,CAAC;IAEQ,WAAW,CAClB,OAAyB;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAU,UAAU,EAAE;YACrE,OAAO,CAAC,UAAU,EAAE;YACpB,OAAO,CAAC,QAAQ,EAAE;SACnB,CAAC,CAAC;QAEH,IAAI,QAAQ;YAAE,OAAO,IAAI,CAAC;QAE1B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAU,iBAAiB,EAAE;YAC9E,OAAO,CAAC,UAAU,EAAE;YACpB,OAAO,CAAC,QAAQ,EAAE;SACnB,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACpF,CAAC;IAEQ,aAAa,CACpB,GAAQ,EACR,IAAS,EACT,IAAS,EACT,OAAyB;QAEzB,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAW,CAAC;QAC7D,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAEjD,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,EAAE,IAAgB,CAAC,CAAC;QAE3D,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YACjB,MAAM,YAAY,GAAG,GAAG,EAAE,OAAO,IAAI,IAAI,EAAE,OAAO,IAAI,cAAc,CAAC;YACrE,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAC/C,MAAM,GAAG,IAAI,IAAI,8BAAqB,CAAC,YAAY,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACxD,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAyB;QACxD,IAAI,CAAC;YACH,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAW,CAAC;YAE7D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yDAAyD,EAAE;gBAC3E,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC/B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC,CAAC;YAEH,iDAAiD;YAChD,OAAe,CAAC,IAAI,GAAG,IAAI,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,OAAgB;QACrC,OAAO;YACL,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,SAAS,EAAE,OAAO,CAAC,EAAE,IAAI,SAAS;YAClC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,SAAS;YACjD,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa;SAC1C,CAAC;IACJ,CAAC;IAEO,cAAc,CACpB,WAAmD,EACnD,OAAgB,EAChB,IAAe;QAEf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;YAC9C,GAAG,WAAW;YACd,MAAM,EAAE,IAAI,EAAE,EAAE;SACjB,CAAC,CAAC;IACL,CAAC;IAEO,cAAc,CACpB,WAAmD,EACnD,YAAoB;QAEpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;YAC5C,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,KAAK,EAAE,YAAY;YACnB,YAAY,EAAE,WAAW,CAAC,QAAQ;SACnC,CAAC,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,IAAc,EAAE,WAAmD;QACxF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;YACjD,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,SAAS,EAAE,WAAW,CAAC,SAAS;SACjC,CAAC,CAAC;IACL,CAAC;IAEO,sBAAsB,CAC5B,IAAc,EACd,WAAmD;QAEnD,OAAO;YACL,GAAG,IAAI;YACP,eAAe,EAAE;gBACf,SAAS,EAAE,WAAW,CAAC,SAAS;gBAChC,SAAS,EAAE,WAAW,CAAC,SAAS;gBAChC,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,GAAG,EAAE,WAAW,CAAC,GAAG;gBACpB,MAAM,EAAE,WAAW,CAAC,MAAM;aAC3B;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AA1HY,oCAAY;uBAAZ,YAAY;IADxB,IAAA,mBAAU,GAAE;yDAI6B,gBAAS,oBAAT,gBAAS;GAHtC,YAAY,CA0HxB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\guards\\jwt-auth.guard.ts"], "sourcesContent": ["import {\r\n  Injectable,\r\n  ExecutionContext,\r\n  UnauthorizedException,\r\n  Logger,\r\n} from '@nestjs/common';\r\nimport { AuthGuard } from '@nestjs/passport';\r\nimport { Reflector } from '@nestjs/core';\r\nimport { Request } from 'express';\r\nimport { Observable } from 'rxjs';\r\n\r\n// Type definitions\r\ninterface AuthUser {\r\n  id: string;\r\n  email: string;\r\n  requestMetadata?: RequestMetadata;\r\n}\r\n\r\ninterface RequestMetadata {\r\n  ipAddress: string;\r\n  userAgent: string;\r\n  timestamp: Date;\r\n  url: string;\r\n  method: string;\r\n}\r\n\r\ninterface AuthInfo {\r\n  message?: string;\r\n}\r\n\r\n// Metadata keys as constants\r\nconst PUBLIC_KEY = 'isPublic';\r\nconst OPTIONAL_AUTH_KEY = 'isOptionalAuth';\r\n\r\n/**\r\n * JWT authentication guard with enhanced type safety and optimization\r\n * Protects routes by validating JWT tokens with support for public and optional endpoints\r\n */\r\n@Injectable()\r\nexport class JwtAuthGuard extends AuthGuard('jwt') {\r\n  private readonly logger = new Logger(JwtAuthGuard.name);\r\n\r\n  constructor(private readonly reflector: Reflector) {\r\n    super();\r\n  }\r\n\r\n  override canActivate(\r\n    context: ExecutionContext,\r\n  ): boolean | Promise<boolean> | Observable<boolean> {\r\n    const isPublic = this.reflector.getAllAndOverride<boolean>(PUBLIC_KEY, [\r\n      context.getHandler(),\r\n      context.getClass(),\r\n    ]);\r\n\r\n    if (isPublic) return true;\r\n\r\n    const isOptional = this.reflector.getAllAndOverride<boolean>(OPTIONAL_AUTH_KEY, [\r\n      context.getHandler(),\r\n      context.getClass(),\r\n    ]);\r\n\r\n    return isOptional ? this.handleOptionalAuth(context) : super.canActivate(context);\r\n  }\r\n\r\n  override handleRequest(\r\n    err: any,\r\n    user: any,\r\n    info: any,\r\n    context: ExecutionContext,\r\n  ): any {\r\n    const request = context.switchToHttp().getRequest<Request>();\r\n    const requestInfo = this.getRequestInfo(request);\r\n\r\n    this.logAuthAttempt(requestInfo, !!user, user as AuthUser);\r\n\r\n    if (err || !user) {\r\n      const errorMessage = err?.message || info?.message || 'Unauthorized';\r\n      this.logAuthFailure(requestInfo, errorMessage);\r\n      throw err || new UnauthorizedException(errorMessage);\r\n    }\r\n\r\n    this.logAuthSuccess(user, requestInfo);\r\n    return this.enrichUserWithMetadata(user, requestInfo);\r\n  }\r\n\r\n  private async handleOptionalAuth(context: ExecutionContext): Promise<boolean> {\r\n    try {\r\n      return !!(await super.canActivate(context));\r\n    } catch (error) {\r\n      const request = context.switchToHttp().getRequest<Request>();\r\n      \r\n      this.logger.debug('Optional auth failed, proceeding without authentication', {\r\n        ...this.getRequestInfo(request),\r\n        error: error instanceof Error ? error.message : 'Unknown error',\r\n      });\r\n\r\n      // Set user to null to indicate no authentication\r\n      (request as any).user = null;\r\n      return true;\r\n    }\r\n  }\r\n\r\n  private getRequestInfo(request: Request) {\r\n    return {\r\n      url: request.url,\r\n      method: request.method,\r\n      ipAddress: request.ip || 'unknown',\r\n      userAgent: request.get('User-Agent') || 'unknown',\r\n      hasToken: !!request.headers.authorization,\r\n    };\r\n  }\r\n\r\n  private logAuthAttempt(\r\n    requestInfo: ReturnType<typeof this.getRequestInfo>,\r\n    hasUser: boolean,\r\n    user?: AuthUser,\r\n  ): void {\r\n    this.logger.debug('JWT authentication attempt', {\r\n      ...requestInfo,\r\n      userId: user?.id,\r\n    });\r\n  }\r\n\r\n  private logAuthFailure(\r\n    requestInfo: ReturnType<typeof this.getRequestInfo>,\r\n    errorMessage: string,\r\n  ): void {\r\n    this.logger.warn('JWT authentication failed', {\r\n      url: requestInfo.url,\r\n      method: requestInfo.method,\r\n      ipAddress: requestInfo.ipAddress,\r\n      error: errorMessage,\r\n      tokenPresent: requestInfo.hasToken,\r\n    });\r\n  }\r\n\r\n  private logAuthSuccess(user: AuthUser, requestInfo: ReturnType<typeof this.getRequestInfo>): void {\r\n    this.logger.debug('JWT authentication successful', {\r\n      userId: user.id,\r\n      email: user.email,\r\n      url: requestInfo.url,\r\n      method: requestInfo.method,\r\n      ipAddress: requestInfo.ipAddress,\r\n    });\r\n  }\r\n\r\n  private enrichUserWithMetadata(\r\n    user: AuthUser,\r\n    requestInfo: ReturnType<typeof this.getRequestInfo>,\r\n  ): AuthUser {\r\n    return {\r\n      ...user,\r\n      requestMetadata: {\r\n        ipAddress: requestInfo.ipAddress,\r\n        userAgent: requestInfo.userAgent,\r\n        timestamp: new Date(),\r\n        url: requestInfo.url,\r\n        method: requestInfo.method,\r\n      },\r\n    };\r\n  }\r\n}"], "version": 3}