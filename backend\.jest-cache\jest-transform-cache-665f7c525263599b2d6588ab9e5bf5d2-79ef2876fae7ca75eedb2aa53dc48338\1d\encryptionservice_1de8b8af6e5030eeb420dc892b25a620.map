{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\encryption\\encryption.service.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAC/C,qDAA4F;AAC5F,qDAAmF;AACnF,iDAA4F;AAC5F,+CAAiC;AAcjC,IAAY,aAKX;AALD,WAAY,aAAa;IACvB,kCAAiB,CAAA;IACjB,kCAAiB,CAAA;IACjB,8BAAa,CAAA;IACb,4BAAW,CAAA;AACb,CAAC,EALW,aAAa,6BAAb,aAAa,QAKxB;AAGM,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC5B,YACmB,aAA4B,EAC5B,aAA4B,EAC5B,aAA4B,EAC5B,WAAwB;QAHxB,kBAAa,GAAb,aAAa,CAAe;QAC5B,kBAAa,GAAb,aAAa,CAAe;QAC5B,kBAAa,GAAb,aAAa,CAAe;QAC5B,gBAAW,GAAX,WAAW,CAAa;IACxC,CAAC;IAEJ,yBAAyB;IACzB;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,IAAY,EAAE,GAAY;QACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC3D,OAAO;YACL,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,GAAG,EAAE,MAAM,CAAC,GAAG;YACf,SAAS,EAAE,MAAM,CAAC,SAAS;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,aAAqB,EAAE,EAAU,EAAE,GAAW,EAAE,GAAY;QAC3E,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YACrD,aAAa;YACb,EAAE;YACF,GAAG;YACH,GAAG;SACJ,CAAC,CAAC;QAEH,OAAO;YACL,aAAa;YACb,SAAS,EAAE,aAAa;SACzB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;IAC1C,CAAC;IAED,yBAAyB;IACzB;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,OAAgB;QACvC,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,IAAY,EAAE,SAAiB,EAAE,OAA8B;QAC9E,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,aAAqB,EAAE,UAAkB,EAAE,OAA8B;QACxF,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,IAAY,EAAE,UAAkB,EAAE,SAAkB;QAChE,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,IAAY,EAAE,SAAiB,EAAE,SAAiB,EAAE,SAAkB;QACpF,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,IAAY,EAAE,SAAiB;QACnD,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,eAAyB,EAAE,UAAkB;QACjE,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;IAC1E,CAAC;IAED,eAAe;IACf;;OAEG;IACH,IAAI,CAAC,IAAY,EAAE,YAA2B,aAAa,CAAC,MAAM;QAChE,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAY;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAY;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,IAAY,EAAE,GAAW,EAAE,YAA2B,aAAa,CAAC,MAAM;QAC7E,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAA6B;QAChE,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,IAAY;QACjD,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,QAAgB,EAAE,IAAY,EAAE,UAAmB,EAAE,SAAkB;QAClF,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IACxE,CAAC;IAED,kBAAkB;IAClB;;OAEG;IACH,oBAAoB,CAAC,SAAiB,EAAE;QACtC,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAAiB,EAAE;QAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,GAAW;QACxB,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IAED,qBAAqB,CAAC,UAAkB;QACtC,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;IAC3D,CAAC;IAED,oBAAoB,CAAC,SAAiB;QACpC,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,IAAY,EAAE,YAA2B,aAAa,CAAC,MAAM;QAC5E,OAAO,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAY,EAAE,gBAAwB,EAAE,YAA2B,aAAa,CAAC,MAAM;QACpG,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;IAC5E,CAAC;CACF,CAAA;AA7LY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;yDAGuB,sBAAa,oBAAb,sBAAa,oDACb,8BAAa,oBAAb,8BAAa,oDACb,8BAAa,oBAAb,8BAAa,oDACf,0BAAW,oBAAX,0BAAW;GALhC,iBAAiB,CA6L7B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\encryption\\encryption.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { AESEncryption, AESEncryptionResult, AESDecryptionOptions } from './aes.encryption';\r\nimport { RSAEncryption, RSAKeyPair, RSAEncryptionOptions } from './rsa.encryption';\r\nimport { HashService, HashOptions, PasswordHashOptions, HMACOptions } from './hash.service';\r\nimport * as crypto from 'crypto';\r\n\r\nexport interface EncryptionResult {\r\n  encryptedData: string;\r\n  iv?: string;\r\n  tag?: string;\r\n  algorithm: string;\r\n}\r\n\r\nexport interface DecryptionResult {\r\n  decryptedData: string;\r\n  algorithm: string;\r\n}\r\n\r\nexport enum HashAlgorithm {\r\n  SHA256 = 'sha256',\r\n  SHA512 = 'sha512',\r\n  SHA1 = 'sha1',\r\n  MD5 = 'md5',\r\n}\r\n\r\n@Injectable()\r\nexport class EncryptionService {\r\n  constructor(\r\n    private readonly configService: ConfigService,\r\n    private readonly aesEncryption: AESEncryption,\r\n    private readonly rsaEncryption: RSAEncryption,\r\n    private readonly hashService: HashService,\r\n  ) {}\r\n\r\n  // AES Encryption Methods\r\n  /**\r\n   * Encrypt data using AES-256-GCM\r\n   */\r\n  async encryptAES(data: string, key?: string): Promise<EncryptionResult> {\r\n    const result = await this.aesEncryption.encrypt(data, key);\r\n    return {\r\n      encryptedData: result.encryptedData,\r\n      iv: result.iv,\r\n      tag: result.tag,\r\n      algorithm: result.algorithm,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Decrypt data using AES-256-GCM\r\n   */\r\n  async decryptAES(encryptedData: string, iv: string, tag: string, key?: string): Promise<DecryptionResult> {\r\n    const decryptedData = await this.aesEncryption.decrypt({\r\n      encryptedData,\r\n      iv,\r\n      tag,\r\n      key,\r\n    });\r\n    \r\n    return {\r\n      decryptedData,\r\n      algorithm: 'aes-256-gcm',\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate AES encryption key\r\n   */\r\n  generateAESKey(): string {\r\n    return this.aesEncryption.generateKey();\r\n  }\r\n\r\n  // RSA Encryption Methods\r\n  /**\r\n   * Generate RSA key pair\r\n   */\r\n  async generateRSAKeyPair(keySize?: number): Promise<RSAKeyPair> {\r\n    return this.rsaEncryption.generateKeyPair(keySize);\r\n  }\r\n\r\n  /**\r\n   * Encrypt data using RSA\r\n   */\r\n  async encryptRSA(data: string, publicKey: string, options?: RSAEncryptionOptions): Promise<string> {\r\n    return this.rsaEncryption.encrypt(data, publicKey, options);\r\n  }\r\n\r\n  /**\r\n   * Decrypt data using RSA\r\n   */\r\n  async decryptRSA(encryptedData: string, privateKey: string, options?: RSAEncryptionOptions): Promise<string> {\r\n    return this.rsaEncryption.decrypt(encryptedData, privateKey, options);\r\n  }\r\n\r\n  /**\r\n   * Sign data using RSA\r\n   */\r\n  async signRSA(data: string, privateKey: string, algorithm?: string): Promise<string> {\r\n    return this.rsaEncryption.sign(data, privateKey, algorithm);\r\n  }\r\n\r\n  /**\r\n   * Verify RSA signature\r\n   */\r\n  async verifyRSA(data: string, signature: string, publicKey: string, algorithm?: string): Promise<boolean> {\r\n    return this.rsaEncryption.verify(data, signature, publicKey, algorithm);\r\n  }\r\n\r\n  /**\r\n   * Encrypt large data using RSA (chunked)\r\n   */\r\n  async encryptRSALarge(data: string, publicKey: string): Promise<string[]> {\r\n    return this.rsaEncryption.encryptLargeData(data, publicKey);\r\n  }\r\n\r\n  /**\r\n   * Decrypt large data using RSA (chunked)\r\n   */\r\n  async decryptRSALarge(encryptedChunks: string[], privateKey: string): Promise<string> {\r\n    return this.rsaEncryption.decryptLargeData(encryptedChunks, privateKey);\r\n  }\r\n\r\n  // Hash Methods\r\n  /**\r\n   * Hash data using specified algorithm\r\n   */\r\n  hash(data: string, algorithm: HashAlgorithm = HashAlgorithm.SHA256): string {\r\n    return this.hashService.hash(data, { algorithm });\r\n  }\r\n\r\n  /**\r\n   * Hash data using SHA-256\r\n   */\r\n  hashSHA256(data: string): string {\r\n    return this.hashService.sha256(data);\r\n  }\r\n\r\n  /**\r\n   * Hash data using SHA-512\r\n   */\r\n  hashSHA512(data: string): string {\r\n    return this.hashService.sha512(data);\r\n  }\r\n\r\n  /**\r\n   * Create HMAC\r\n   */\r\n  hmac(data: string, key: string, algorithm: HashAlgorithm = HashAlgorithm.SHA256): string {\r\n    return this.hashService.hmac(data, key, { algorithm });\r\n  }\r\n\r\n  /**\r\n   * Hash password using bcrypt\r\n   */\r\n  async hashPassword(password: string, options?: PasswordHashOptions): Promise<string> {\r\n    return this.hashService.hashPassword(password, options);\r\n  }\r\n\r\n  /**\r\n   * Verify password against hash\r\n   */\r\n  async verifyPassword(password: string, hash: string): Promise<boolean> {\r\n    return this.hashService.verifyPassword(password, hash);\r\n  }\r\n\r\n  /**\r\n   * Generate PBKDF2 hash\r\n   */\r\n  async pbkdf2(password: string, salt: string, iterations?: number, keyLength?: number): Promise<string> {\r\n    return this.hashService.pbkdf2(password, salt, iterations, keyLength);\r\n  }\r\n\r\n  // Utility Methods\r\n  /**\r\n   * Generate a secure random string\r\n   */\r\n  generateSecureRandom(length: number = 32): string {\r\n    return crypto.randomBytes(length).toString('hex');\r\n  }\r\n\r\n  /**\r\n   * Generate cryptographically secure salt\r\n   */\r\n  generateSalt(length: number = 32): string {\r\n    return this.hashService.generateRandomSalt(length);\r\n  }\r\n\r\n  /**\r\n   * Validate encryption keys\r\n   */\r\n  validateAESKey(key: string): boolean {\r\n    return this.aesEncryption.validateKey(key);\r\n  }\r\n\r\n  validateRSAPrivateKey(privateKey: string): boolean {\r\n    return this.rsaEncryption.validatePrivateKey(privateKey);\r\n  }\r\n\r\n  validateRSAPublicKey(publicKey: string): boolean {\r\n    return this.rsaEncryption.validatePublicKey(publicKey);\r\n  }\r\n\r\n  /**\r\n   * Generate checksum for data integrity\r\n   */\r\n  generateChecksum(data: string, algorithm: HashAlgorithm = HashAlgorithm.SHA256): string {\r\n    return this.hashService.generateChecksum(data, algorithm);\r\n  }\r\n\r\n  /**\r\n   * Verify data integrity using checksum\r\n   */\r\n  verifyChecksum(data: string, expectedChecksum: string, algorithm: HashAlgorithm = HashAlgorithm.SHA256): boolean {\r\n    return this.hashService.verifyChecksum(data, expectedChecksum, algorithm);\r\n  }\r\n} "], "version": 3}