d4e9445f932b6e0779d5fb8c8336e057
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const compliance_monitoring_service_1 = require("./compliance-monitoring.service");
const compliance_framework_entity_1 = require("../../domain/entities/compliance-framework.entity");
const compliance_assessment_entity_1 = require("../../domain/entities/compliance-assessment.entity");
const policy_definition_entity_1 = require("../../domain/entities/policy-definition.entity");
const policy_violation_entity_1 = require("../../domain/entities/policy-violation.entity");
const audit_log_entity_1 = require("../../domain/entities/audit-log.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../infrastructure/notification/notification.service");
const policy_enforcement_service_1 = require("./policy-enforcement.service");
const violation_detection_service_1 = require("./violation-detection.service");
describe('ComplianceMonitoringService', () => {
    let service;
    let frameworkRepository;
    let assessmentRepository;
    let policyRepository;
    let violationRepository;
    let auditLogRepository;
    let loggerService;
    let auditService;
    let notificationService;
    let policyEnforcementService;
    let violationDetectionService;
    const mockFrameworkRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        count: jest.fn(),
        createQueryBuilder: jest.fn(),
    };
    const mockAssessmentRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        count: jest.fn(),
        createQueryBuilder: jest.fn(),
    };
    const mockPolicyRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        count: jest.fn(),
    };
    const mockViolationRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        count: jest.fn(),
        createQueryBuilder: jest.fn(),
    };
    const mockAuditLogRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        count: jest.fn(),
    };
    const mockLoggerService = {
        debug: jest.fn(),
        log: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
    };
    const mockAuditService = {
        logUserAction: jest.fn(),
    };
    const mockNotificationService = {
        sendComplianceNotification: jest.fn(),
        sendAssessmentOverdueNotification: jest.fn(),
        sendPolicyViolationNotification: jest.fn(),
    };
    const mockPolicyEnforcementService = {
        evaluateControl: jest.fn(),
        evaluatePolicyCompliance: jest.fn(),
    };
    const mockViolationDetectionService = {
        checkPolicyCompliance: jest.fn(),
        scanForViolations: jest.fn(),
        createViolation: jest.fn(),
    };
    const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn(),
        getMany: jest.fn(),
        getCount: jest.fn(),
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn(),
        getRawOne: jest.fn(),
    };
    const mockFramework = {
        id: 'framework-123',
        name: 'SOC 2 Type II',
        type: 'SOC2',
        version: '2017',
        isActive: true,
        configuration: {
            domains: [
                {
                    id: 'security',
                    name: 'Security',
                    description: 'Security controls',
                    controls: [
                        {
                            id: 'CC6.1',
                            name: 'Logical Access Controls',
                            description: 'Access controls are implemented',
                            type: 'preventive',
                            priority: 'high',
                            frequency: 'continuous',
                            automatable: true,
                            evidenceRequirements: ['access_logs', 'configuration'],
                        },
                    ],
                },
            ],
        },
        totalControls: 1,
        automatableControls: [
            {
                id: 'CC6.1',
                name: 'Logical Access Controls',
                domainId: 'security',
                domainName: 'Security',
            },
        ],
        getControlsByPriority: jest.fn(),
        getDomainById: jest.fn(),
        getControlById: jest.fn(),
        validateConfiguration: jest.fn().mockReturnValue({ isValid: true, errors: [] }),
        calculateComplexityScore: jest.fn().mockReturnValue(10),
    };
    const mockAssessment = {
        id: 'assessment-123',
        name: 'Q4 2023 SOC 2 Assessment',
        frameworkId: 'framework-123',
        assessmentType: 'internal_audit',
        status: 'planned',
        assessmentDate: new Date('2023-12-15'),
        nextAssessmentDate: new Date('2024-03-15'),
        scope: {
            description: 'Full organizational assessment',
        },
        isOverdue: false,
        isCompliant: false,
        compliancePercentage: 0,
        criticalFindingsCount: 0,
        highFindingsCount: 0,
        ageInDays: 30,
        start: jest.fn(),
        complete: jest.fn(),
        generateSummary: jest.fn().mockReturnValue({
            id: 'assessment-123',
            name: 'Q4 2023 SOC 2 Assessment',
            status: 'planned',
        }),
    };
    const mockViolation = {
        id: 'violation-123',
        title: 'Access Control Violation',
        severity: 'high',
        status: 'open',
        detectedAt: new Date(),
        details: {
            violatedControls: ['CC6.1'],
            compliance: {
                frameworks: ['framework-123'],
                controls: ['CC6.1'],
            },
        },
        generateSummary: jest.fn().mockReturnValue({
            id: 'violation-123',
            title: 'Access Control Violation',
            severity: 'high',
            status: 'open',
        }),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                compliance_monitoring_service_1.ComplianceMonitoringService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(compliance_framework_entity_1.ComplianceFramework),
                    useValue: mockFrameworkRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(compliance_assessment_entity_1.ComplianceAssessment),
                    useValue: mockAssessmentRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(policy_definition_entity_1.PolicyDefinition),
                    useValue: mockPolicyRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(policy_violation_entity_1.PolicyViolation),
                    useValue: mockViolationRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(audit_log_entity_1.AuditLog),
                    useValue: mockAuditLogRepository,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: mockLoggerService,
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: mockAuditService,
                },
                {
                    provide: notification_service_1.NotificationService,
                    useValue: mockNotificationService,
                },
                {
                    provide: policy_enforcement_service_1.PolicyEnforcementService,
                    useValue: mockPolicyEnforcementService,
                },
                {
                    provide: violation_detection_service_1.ViolationDetectionService,
                    useValue: mockViolationDetectionService,
                },
            ],
        }).compile();
        service = module.get(compliance_monitoring_service_1.ComplianceMonitoringService);
        frameworkRepository = module.get((0, typeorm_1.getRepositoryToken)(compliance_framework_entity_1.ComplianceFramework));
        assessmentRepository = module.get((0, typeorm_1.getRepositoryToken)(compliance_assessment_entity_1.ComplianceAssessment));
        policyRepository = module.get((0, typeorm_1.getRepositoryToken)(policy_definition_entity_1.PolicyDefinition));
        violationRepository = module.get((0, typeorm_1.getRepositoryToken)(policy_violation_entity_1.PolicyViolation));
        auditLogRepository = module.get((0, typeorm_1.getRepositoryToken)(audit_log_entity_1.AuditLog));
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
        notificationService = module.get(notification_service_1.NotificationService);
        policyEnforcementService = module.get(policy_enforcement_service_1.PolicyEnforcementService);
        violationDetectionService = module.get(violation_detection_service_1.ViolationDetectionService);
        // Setup query builder mocks
        mockFrameworkRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
        mockAssessmentRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
        mockViolationRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('getComplianceDashboard', () => {
        it('should return compliance dashboard data', async () => {
            // Mock repository responses
            mockFrameworkRepository.count.mockResolvedValue(5); // activeFrameworks
            mockAssessmentRepository.count.mockResolvedValue(20); // totalAssessments
            mockViolationRepository.count
                .mockResolvedValueOnce(15) // openViolations
                .mockResolvedValueOnce(3); // criticalViolations
            mockFrameworkRepository.find.mockResolvedValue([mockFramework]);
            mockAssessmentRepository.find.mockResolvedValue([mockAssessment]);
            mockViolationRepository.find.mockResolvedValue([mockViolation]);
            // Mock helper methods
            jest.spyOn(service, 'getLatestAssessment').mockResolvedValue(mockAssessment);
            jest.spyOn(service, 'calculateFrameworkComplianceScore').mockResolvedValue(85);
            const result = await service.getComplianceDashboard();
            expect(result).toHaveProperty('summary');
            expect(result.summary).toEqual({
                activeFrameworks: 5,
                totalAssessments: 20,
                openViolations: 15,
                criticalViolations: 3,
                overallComplianceScore: expect.any(Number),
            });
            expect(result).toHaveProperty('complianceByFramework');
            expect(result).toHaveProperty('trends');
            expect(result).toHaveProperty('riskMetrics');
            expect(result).toHaveProperty('timestamp');
        });
        it('should handle errors gracefully', async () => {
            mockFrameworkRepository.count.mockRejectedValue(new Error('Database error'));
            await expect(service.getComplianceDashboard()).rejects.toThrow('Database error');
            expect(mockLoggerService.error).toHaveBeenCalledWith('Failed to generate compliance dashboard', expect.objectContaining({
                error: 'Database error',
            }));
        });
    });
    describe('createAssessment', () => {
        const assessmentData = {
            name: 'Test Assessment',
            description: 'Test assessment description',
            assessmentType: 'internal_audit',
            assessmentDate: new Date(),
            scope: {
                description: 'Test scope',
            },
        };
        it('should create assessment successfully', async () => {
            const userId = 'user-123';
            const savedAssessment = { ...mockAssessment, id: 'assessment-456' };
            mockFrameworkRepository.findOne.mockResolvedValue(mockFramework);
            mockAssessmentRepository.create.mockReturnValue(mockAssessment);
            mockAssessmentRepository.save.mockResolvedValue(savedAssessment);
            const result = await service.createAssessment('framework-123', assessmentData, userId);
            expect(mockFrameworkRepository.findOne).toHaveBeenCalledWith({
                where: { id: 'framework-123' },
            });
            expect(mockAssessmentRepository.create).toHaveBeenCalledWith({
                ...assessmentData,
                frameworkId: 'framework-123',
                status: 'planned',
                createdBy: userId,
            });
            expect(mockAssessmentRepository.save).toHaveBeenCalledWith(mockAssessment);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'create', 'compliance_assessment', savedAssessment.id, expect.objectContaining({
                frameworkId: 'framework-123',
                assessmentType: assessmentData.assessmentType,
                assessmentName: assessmentData.name,
            }));
            expect(result).toEqual(savedAssessment);
        });
        it('should throw error when framework not found', async () => {
            const userId = 'user-123';
            mockFrameworkRepository.findOne.mockResolvedValue(null);
            await expect(service.createAssessment('non-existent', assessmentData, userId))
                .rejects.toThrow('Framework not found');
        });
    });
    describe('startAssessment', () => {
        it('should start assessment successfully', async () => {
            const assessmentId = 'assessment-123';
            const userId = 'user-123';
            const assessment = { ...mockAssessment, framework: mockFramework };
            mockAssessmentRepository.findOne.mockResolvedValue(assessment);
            mockAssessmentRepository.save.mockResolvedValue(assessment);
            // Mock helper method
            jest.spyOn(service, 'initializeAssessmentControls').mockResolvedValue(undefined);
            const result = await service.startAssessment(assessmentId, userId);
            expect(mockAssessmentRepository.findOne).toHaveBeenCalledWith({
                where: { id: assessmentId },
                relations: ['framework'],
            });
            expect(assessment.start).toHaveBeenCalledWith(userId);
            expect(mockAssessmentRepository.save).toHaveBeenCalledWith(assessment);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'start', 'compliance_assessment', assessmentId, expect.objectContaining({
                frameworkId: assessment.frameworkId,
                assessmentType: assessment.assessmentType,
            }));
            expect(result).toEqual(assessment);
        });
        it('should throw error when assessment not found', async () => {
            const assessmentId = 'non-existent';
            const userId = 'user-123';
            mockAssessmentRepository.findOne.mockResolvedValue(null);
            await expect(service.startAssessment(assessmentId, userId))
                .rejects.toThrow('Assessment not found');
        });
    });
    describe('completeAssessment', () => {
        it('should complete assessment successfully', async () => {
            const assessmentId = 'assessment-123';
            const userId = 'user-123';
            const results = {
                overallScore: 85,
                overallStatus: 'compliant',
                domainResults: [],
            };
            const assessment = { ...mockAssessment, framework: mockFramework };
            mockAssessmentRepository.findOne.mockResolvedValue(assessment);
            mockAssessmentRepository.save.mockResolvedValue(assessment);
            // Mock helper methods
            jest.spyOn(service, 'generateViolationsFromAssessment').mockResolvedValue(undefined);
            const result = await service.completeAssessment(assessmentId, results, userId);
            expect(mockAssessmentRepository.findOne).toHaveBeenCalledWith({
                where: { id: assessmentId },
                relations: ['framework'],
            });
            expect(assessment.complete).toHaveBeenCalledWith(userId, results);
            expect(mockAssessmentRepository.save).toHaveBeenCalledWith(assessment);
            expect(mockNotificationService.sendComplianceNotification).toHaveBeenCalledWith(assessment, 'assessment_completed');
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'complete', 'compliance_assessment', assessmentId, expect.objectContaining({
                frameworkId: assessment.frameworkId,
                complianceScore: results.overallScore,
                complianceStatus: results.overallStatus,
            }));
            expect(result).toEqual(assessment);
        });
    });
    describe('getFrameworkComplianceStatus', () => {
        it('should return framework compliance status', async () => {
            const frameworkId = 'framework-123';
            mockFrameworkRepository.findOne.mockResolvedValue(mockFramework);
            // Mock helper methods
            jest.spyOn(service, 'getLatestAssessment').mockResolvedValue(mockAssessment);
            jest.spyOn(service, 'getFrameworkViolations').mockResolvedValue([mockViolation]);
            jest.spyOn(service, 'getFrameworkPolicies').mockResolvedValue([]);
            jest.spyOn(service, 'calculateFrameworkComplianceScore').mockResolvedValue(85);
            jest.spyOn(service, 'calculateFrameworkRiskLevel').mockResolvedValue('medium');
            const result = await service.getFrameworkComplianceStatus(frameworkId);
            expect(result).toHaveProperty('framework');
            expect(result).toHaveProperty('latestAssessment');
            expect(result).toHaveProperty('violations');
            expect(result).toHaveProperty('policies');
            expect(result).toHaveProperty('complianceScore');
            expect(result).toHaveProperty('riskLevel');
            expect(result.framework.id).toBe(frameworkId);
            expect(result.complianceScore).toBe(85);
            expect(result.riskLevel).toBe('medium');
        });
        it('should throw error when framework not found', async () => {
            const frameworkId = 'non-existent';
            mockFrameworkRepository.findOne.mockResolvedValue(null);
            await expect(service.getFrameworkComplianceStatus(frameworkId))
                .rejects.toThrow('Framework not found');
        });
    });
    describe('getComplianceTrends', () => {
        it('should return compliance trends', async () => {
            const days = 90;
            const assessments = [mockAssessment];
            const violations = [mockViolation];
            mockAssessmentRepository.find.mockResolvedValue(assessments);
            mockViolationRepository.find.mockResolvedValue(violations);
            // Mock helper methods
            jest.spyOn(service, 'groupTrendsByWeek').mockReturnValue([
                {
                    weekStart: new Date(),
                    weekEnd: new Date(),
                    assessments: 1,
                    violations: 1,
                    averageComplianceScore: 85,
                },
            ]);
            jest.spyOn(service, 'calculateAverageComplianceScore').mockReturnValue(85);
            jest.spyOn(service, 'calculateTrendDirection').mockReturnValue('improving');
            const result = await service.getComplianceTrends(days);
            expect(result).toHaveProperty('period');
            expect(result).toHaveProperty('trends');
            expect(result).toHaveProperty('summary');
            expect(result.period.days).toBe(days);
            expect(result.summary.totalAssessments).toBe(1);
            expect(result.summary.totalViolations).toBe(1);
            expect(result.summary.averageComplianceScore).toBe(85);
            expect(result.summary.trendDirection).toBe('improving');
        });
    });
    describe('monitorCompliance', () => {
        it('should monitor compliance for all active frameworks', async () => {
            const activeFrameworks = [mockFramework];
            mockFrameworkRepository.find.mockResolvedValue(activeFrameworks);
            // Mock helper methods
            jest.spyOn(service, 'monitorFrameworkCompliance').mockResolvedValue(undefined);
            jest.spyOn(service, 'checkOverdueAssessments').mockResolvedValue(undefined);
            jest.spyOn(service, 'checkPolicyViolations').mockResolvedValue(undefined);
            await service.monitorCompliance();
            expect(mockFrameworkRepository.find).toHaveBeenCalledWith({
                where: { isActive: true },
            });
            expect(service.monitorFrameworkCompliance).toHaveBeenCalledWith(mockFramework.id);
            expect(mockLoggerService.log).toHaveBeenCalledWith('Compliance monitoring cycle completed', expect.objectContaining({
                frameworksMonitored: 1,
            }));
        });
        it('should handle framework monitoring errors gracefully', async () => {
            const activeFrameworks = [mockFramework];
            mockFrameworkRepository.find.mockResolvedValue(activeFrameworks);
            jest.spyOn(service, 'monitorFrameworkCompliance').mockRejectedValue(new Error('Monitoring error'));
            jest.spyOn(service, 'checkOverdueAssessments').mockResolvedValue(undefined);
            jest.spyOn(service, 'checkPolicyViolations').mockResolvedValue(undefined);
            await service.monitorCompliance();
            expect(mockLoggerService.error).toHaveBeenCalledWith('Failed to monitor framework compliance', expect.objectContaining({
                frameworkId: mockFramework.id,
                frameworkName: mockFramework.name,
                error: 'Monitoring error',
            }));
        });
    });
    describe('monitorFrameworkCompliance', () => {
        it('should monitor framework compliance successfully', async () => {
            const frameworkId = 'framework-123';
            const framework = { ...mockFramework, assessments: [], policies: [] };
            mockFrameworkRepository.findOne.mockResolvedValue(framework);
            // Mock helper methods
            jest.spyOn(service, 'getLatestAssessment').mockResolvedValue(mockAssessment);
            jest.spyOn(service, 'isAssessmentDue').mockReturnValue(false);
            jest.spyOn(service, 'monitorContinuousControls').mockResolvedValue(undefined);
            jest.spyOn(service, 'checkPolicyCompliance').mockResolvedValue(undefined);
            jest.spyOn(service, 'updateComplianceMetrics').mockResolvedValue(undefined);
            await service.monitorFrameworkCompliance(frameworkId);
            expect(mockFrameworkRepository.findOne).toHaveBeenCalledWith({
                where: { id: frameworkId },
                relations: ['assessments', 'policies'],
            });
            expect(mockLoggerService.debug).toHaveBeenCalledWith('Framework compliance monitoring completed', expect.objectContaining({
                frameworkId,
            }));
        });
        it('should throw error when framework not found', async () => {
            const frameworkId = 'non-existent';
            mockFrameworkRepository.findOne.mockResolvedValue(null);
            await expect(service.monitorFrameworkCompliance(frameworkId))
                .rejects.toThrow('Framework not found');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFxjb21wbGlhbmNlLWF1ZGl0XFxhcHBsaWNhdGlvblxcc2VydmljZXNcXGNvbXBsaWFuY2UtbW9uaXRvcmluZy5zZXJ2aWNlLnNwZWMudHMiLCJtYXBwaW5ncyI6Ijs7QUFBQSw2Q0FBc0Q7QUFDdEQsNkNBQXFEO0FBRXJELG1GQUE4RTtBQUM5RSxtR0FBd0Y7QUFDeEYscUdBQTBGO0FBQzFGLDZGQUFrRjtBQUNsRiwyRkFBZ0Y7QUFDaEYsNkVBQWtFO0FBQ2xFLHNGQUFrRjtBQUNsRiwwRkFBc0Y7QUFDdEYsdUdBQW1HO0FBQ25HLDZFQUF3RTtBQUN4RSwrRUFBMEU7QUFFMUUsUUFBUSxDQUFDLDZCQUE2QixFQUFFLEdBQUcsRUFBRTtJQUMzQyxJQUFJLE9BQW9DLENBQUM7SUFDekMsSUFBSSxtQkFBb0QsQ0FBQztJQUN6RCxJQUFJLG9CQUFzRCxDQUFDO0lBQzNELElBQUksZ0JBQThDLENBQUM7SUFDbkQsSUFBSSxtQkFBZ0QsQ0FBQztJQUNyRCxJQUFJLGtCQUF3QyxDQUFDO0lBQzdDLElBQUksYUFBNEIsQ0FBQztJQUNqQyxJQUFJLFlBQTBCLENBQUM7SUFDL0IsSUFBSSxtQkFBd0MsQ0FBQztJQUM3QyxJQUFJLHdCQUFrRCxDQUFDO0lBQ3ZELElBQUkseUJBQW9ELENBQUM7SUFFekQsTUFBTSx1QkFBdUIsR0FBRztRQUM5QixJQUFJLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNmLE9BQU8sRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2xCLE1BQU0sRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2pCLElBQUksRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2YsS0FBSyxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDaEIsa0JBQWtCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtLQUM5QixDQUFDO0lBRUYsTUFBTSx3QkFBd0IsR0FBRztRQUMvQixJQUFJLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNmLE9BQU8sRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2xCLE1BQU0sRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2pCLElBQUksRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2YsS0FBSyxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDaEIsa0JBQWtCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtLQUM5QixDQUFDO0lBRUYsTUFBTSxvQkFBb0IsR0FBRztRQUMzQixJQUFJLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNmLE9BQU8sRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2xCLE1BQU0sRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2pCLElBQUksRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2YsS0FBSyxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7S0FDakIsQ0FBQztJQUVGLE1BQU0sdUJBQXVCLEdBQUc7UUFDOUIsSUFBSSxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDZixPQUFPLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNsQixNQUFNLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNqQixJQUFJLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNmLEtBQUssRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2hCLGtCQUFrQixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7S0FDOUIsQ0FBQztJQUVGLE1BQU0sc0JBQXNCLEdBQUc7UUFDN0IsSUFBSSxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDZixPQUFPLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNsQixNQUFNLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNqQixJQUFJLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNmLEtBQUssRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO0tBQ2pCLENBQUM7SUFFRixNQUFNLGlCQUFpQixHQUFHO1FBQ3hCLEtBQUssRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2hCLEdBQUcsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2QsSUFBSSxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDZixLQUFLLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtLQUNqQixDQUFDO0lBRUYsTUFBTSxnQkFBZ0IsR0FBRztRQUN2QixhQUFhLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtLQUN6QixDQUFDO0lBRUYsTUFBTSx1QkFBdUIsR0FBRztRQUM5QiwwQkFBMEIsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ3JDLGlDQUFpQyxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDNUMsK0JBQStCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtLQUMzQyxDQUFDO0lBRUYsTUFBTSw0QkFBNEIsR0FBRztRQUNuQyxlQUFlLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUMxQix3QkFBd0IsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO0tBQ3BDLENBQUM7SUFFRixNQUFNLDZCQUE2QixHQUFHO1FBQ3BDLHFCQUFxQixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDaEMsaUJBQWlCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUM1QixlQUFlLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtLQUMzQixDQUFDO0lBRUYsTUFBTSxnQkFBZ0IsR0FBRztRQUN2QixLQUFLLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGNBQWMsRUFBRTtRQUNqQyxRQUFRLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGNBQWMsRUFBRTtRQUNwQyxPQUFPLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGNBQWMsRUFBRTtRQUNuQyxJQUFJLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGNBQWMsRUFBRTtRQUNoQyxJQUFJLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGNBQWMsRUFBRTtRQUNoQyxlQUFlLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUMxQixPQUFPLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNsQixRQUFRLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNuQixNQUFNLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGNBQWMsRUFBRTtRQUNsQyxTQUFTLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGNBQWMsRUFBRTtRQUNyQyxPQUFPLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGNBQWMsRUFBRTtRQUNuQyxVQUFVLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNyQixTQUFTLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtLQUNyQixDQUFDO0lBRUYsTUFBTSxhQUFhLEdBQWlDO1FBQ2xELEVBQUUsRUFBRSxlQUFlO1FBQ25CLElBQUksRUFBRSxlQUFlO1FBQ3JCLElBQUksRUFBRSxNQUFNO1FBQ1osT0FBTyxFQUFFLE1BQU07UUFDZixRQUFRLEVBQUUsSUFBSTtRQUNkLGFBQWEsRUFBRTtZQUNiLE9BQU8sRUFBRTtnQkFDUDtvQkFDRSxFQUFFLEVBQUUsVUFBVTtvQkFDZCxJQUFJLEVBQUUsVUFBVTtvQkFDaEIsV0FBVyxFQUFFLG1CQUFtQjtvQkFDaEMsUUFBUSxFQUFFO3dCQUNSOzRCQUNFLEVBQUUsRUFBRSxPQUFPOzRCQUNYLElBQUksRUFBRSx5QkFBeUI7NEJBQy9CLFdBQVcsRUFBRSxpQ0FBaUM7NEJBQzlDLElBQUksRUFBRSxZQUFZOzRCQUNsQixRQUFRLEVBQUUsTUFBTTs0QkFDaEIsU0FBUyxFQUFFLFlBQVk7NEJBQ3ZCLFdBQVcsRUFBRSxJQUFJOzRCQUNqQixvQkFBb0IsRUFBRSxDQUFDLGFBQWEsRUFBRSxlQUFlLENBQUM7eUJBQ3ZEO3FCQUNGO2lCQUNGO2FBQ0Y7U0FDRjtRQUNELGFBQWEsRUFBRSxDQUFDO1FBQ2hCLG1CQUFtQixFQUFFO1lBQ25CO2dCQUNFLEVBQUUsRUFBRSxPQUFPO2dCQUNYLElBQUksRUFBRSx5QkFBeUI7Z0JBQy9CLFFBQVEsRUFBRSxVQUFVO2dCQUNwQixVQUFVLEVBQUUsVUFBVTthQUN2QjtTQUNGO1FBQ0QscUJBQXFCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNoQyxhQUFhLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUN4QixjQUFjLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUN6QixxQkFBcUIsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUMsZUFBZSxDQUFDLEVBQUUsT0FBTyxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsRUFBRSxFQUFFLENBQUM7UUFDL0Usd0JBQXdCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGVBQWUsQ0FBQyxFQUFFLENBQUM7S0FDeEQsQ0FBQztJQUVGLE1BQU0sY0FBYyxHQUFrQztRQUNwRCxFQUFFLEVBQUUsZ0JBQWdCO1FBQ3BCLElBQUksRUFBRSwwQkFBMEI7UUFDaEMsV0FBVyxFQUFFLGVBQWU7UUFDNUIsY0FBYyxFQUFFLGdCQUFnQjtRQUNoQyxNQUFNLEVBQUUsU0FBUztRQUNqQixjQUFjLEVBQUUsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDO1FBQ3RDLGtCQUFrQixFQUFFLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQztRQUMxQyxLQUFLLEVBQUU7WUFDTCxXQUFXLEVBQUUsZ0NBQWdDO1NBQzlDO1FBQ0QsU0FBUyxFQUFFLEtBQUs7UUFDaEIsV0FBVyxFQUFFLEtBQUs7UUFDbEIsb0JBQW9CLEVBQUUsQ0FBQztRQUN2QixxQkFBcUIsRUFBRSxDQUFDO1FBQ3hCLGlCQUFpQixFQUFFLENBQUM7UUFDcEIsU0FBUyxFQUFFLEVBQUU7UUFDYixLQUFLLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNoQixRQUFRLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNuQixlQUFlLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLGVBQWUsQ0FBQztZQUN6QyxFQUFFLEVBQUUsZ0JBQWdCO1lBQ3BCLElBQUksRUFBRSwwQkFBMEI7WUFDaEMsTUFBTSxFQUFFLFNBQVM7U0FDbEIsQ0FBQztLQUNILENBQUM7SUFFRixNQUFNLGFBQWEsR0FBNkI7UUFDOUMsRUFBRSxFQUFFLGVBQWU7UUFDbkIsS0FBSyxFQUFFLDBCQUEwQjtRQUNqQyxRQUFRLEVBQUUsTUFBTTtRQUNoQixNQUFNLEVBQUUsTUFBTTtRQUNkLFVBQVUsRUFBRSxJQUFJLElBQUksRUFBRTtRQUN0QixPQUFPLEVBQUU7WUFDUCxnQkFBZ0IsRUFBRSxDQUFDLE9BQU8sQ0FBQztZQUMzQixVQUFVLEVBQUU7Z0JBQ1YsVUFBVSxFQUFFLENBQUMsZUFBZSxDQUFDO2dCQUM3QixRQUFRLEVBQUUsQ0FBQyxPQUFPLENBQUM7YUFDcEI7U0FDRjtRQUNELGVBQWUsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUMsZUFBZSxDQUFDO1lBQ3pDLEVBQUUsRUFBRSxlQUFlO1lBQ25CLEtBQUssRUFBRSwwQkFBMEI7WUFDakMsUUFBUSxFQUFFLE1BQU07WUFDaEIsTUFBTSxFQUFFLE1BQU07U0FDZixDQUFDO0tBQ0gsQ0FBQztJQUVGLFVBQVUsQ0FBQyxLQUFLLElBQUksRUFBRTtRQUNwQixNQUFNLE1BQU0sR0FBa0IsTUFBTSxjQUFJLENBQUMsbUJBQW1CLENBQUM7WUFDM0QsU0FBUyxFQUFFO2dCQUNULDJEQUEyQjtnQkFDM0I7b0JBQ0UsT0FBTyxFQUFFLElBQUEsNEJBQWtCLEVBQUMsaURBQW1CLENBQUM7b0JBQ2hELFFBQVEsRUFBRSx1QkFBdUI7aUJBQ2xDO2dCQUNEO29CQUNFLE9BQU8sRUFBRSxJQUFBLDRCQUFrQixFQUFDLG1EQUFvQixDQUFDO29CQUNqRCxRQUFRLEVBQUUsd0JBQXdCO2lCQUNuQztnQkFDRDtvQkFDRSxPQUFPLEVBQUUsSUFBQSw0QkFBa0IsRUFBQywyQ0FBZ0IsQ0FBQztvQkFDN0MsUUFBUSxFQUFFLG9CQUFvQjtpQkFDL0I7Z0JBQ0Q7b0JBQ0UsT0FBTyxFQUFFLElBQUEsNEJBQWtCLEVBQUMseUNBQWUsQ0FBQztvQkFDNUMsUUFBUSxFQUFFLHVCQUF1QjtpQkFDbEM7Z0JBQ0Q7b0JBQ0UsT0FBTyxFQUFFLElBQUEsNEJBQWtCLEVBQUMsMkJBQVEsQ0FBQztvQkFDckMsUUFBUSxFQUFFLHNCQUFzQjtpQkFDakM7Z0JBQ0Q7b0JBQ0UsT0FBTyxFQUFFLDhCQUFhO29CQUN0QixRQUFRLEVBQUUsaUJBQWlCO2lCQUM1QjtnQkFDRDtvQkFDRSxPQUFPLEVBQUUsNEJBQVk7b0JBQ3JCLFFBQVEsRUFBRSxnQkFBZ0I7aUJBQzNCO2dCQUNEO29CQUNFLE9BQU8sRUFBRSwwQ0FBbUI7b0JBQzVCLFFBQVEsRUFBRSx1QkFBdUI7aUJBQ2xDO2dCQUNEO29CQUNFLE9BQU8sRUFBRSxxREFBd0I7b0JBQ2pDLFFBQVEsRUFBRSw0QkFBNEI7aUJBQ3ZDO2dCQUNEO29CQUNFLE9BQU8sRUFBRSx1REFBeUI7b0JBQ2xDLFFBQVEsRUFBRSw2QkFBNkI7aUJBQ3hDO2FBQ0Y7U0FDRixDQUFDLENBQUMsT0FBTyxFQUFFLENBQUM7UUFFYixPQUFPLEdBQUcsTUFBTSxDQUFDLEdBQUcsQ0FBOEIsMkRBQTJCLENBQUMsQ0FBQztRQUMvRSxtQkFBbUIsR0FBRyxNQUFNLENBQUMsR0FBRyxDQUFrQyxJQUFBLDRCQUFrQixFQUFDLGlEQUFtQixDQUFDLENBQUMsQ0FBQztRQUMzRyxvQkFBb0IsR0FBRyxNQUFNLENBQUMsR0FBRyxDQUFtQyxJQUFBLDRCQUFrQixFQUFDLG1EQUFvQixDQUFDLENBQUMsQ0FBQztRQUM5RyxnQkFBZ0IsR0FBRyxNQUFNLENBQUMsR0FBRyxDQUErQixJQUFBLDRCQUFrQixFQUFDLDJDQUFnQixDQUFDLENBQUMsQ0FBQztRQUNsRyxtQkFBbUIsR0FBRyxNQUFNLENBQUMsR0FBRyxDQUE4QixJQUFBLDRCQUFrQixFQUFDLHlDQUFlLENBQUMsQ0FBQyxDQUFDO1FBQ25HLGtCQUFrQixHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQXVCLElBQUEsNEJBQWtCLEVBQUMsMkJBQVEsQ0FBQyxDQUFDLENBQUM7UUFDcEYsYUFBYSxHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQWdCLDhCQUFhLENBQUMsQ0FBQztRQUN6RCxZQUFZLEdBQUcsTUFBTSxDQUFDLEdBQUcsQ0FBZSw0QkFBWSxDQUFDLENBQUM7UUFDdEQsbUJBQW1CLEdBQUcsTUFBTSxDQUFDLEdBQUcsQ0FBc0IsMENBQW1CLENBQUMsQ0FBQztRQUMzRSx3QkFBd0IsR0FBRyxNQUFNLENBQUMsR0FBRyxDQUEyQixxREFBd0IsQ0FBQyxDQUFDO1FBQzFGLHlCQUF5QixHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQTRCLHVEQUF5QixDQUFDLENBQUM7UUFFN0YsNEJBQTRCO1FBQzVCLHVCQUF1QixDQUFDLGtCQUFrQixDQUFDLGVBQWUsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO1FBQzdFLHdCQUF3QixDQUFDLGtCQUFrQixDQUFDLGVBQWUsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO1FBQzlFLHVCQUF1QixDQUFDLGtCQUFrQixDQUFDLGVBQWUsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO0lBQy9FLENBQUMsQ0FBQyxDQUFDO0lBRUgsU0FBUyxDQUFDLEdBQUcsRUFBRTtRQUNiLElBQUksQ0FBQyxhQUFhLEVBQUUsQ0FBQztJQUN2QixDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyx3QkFBd0IsRUFBRSxHQUFHLEVBQUU7UUFDdEMsRUFBRSxDQUFDLHlDQUF5QyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3ZELDRCQUE0QjtZQUM1Qix1QkFBdUIsQ0FBQyxLQUFLLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxtQkFBbUI7WUFDdkUsd0JBQXdCLENBQUMsS0FBSyxDQUFDLGlCQUFpQixDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsbUJBQW1CO1lBQ3pFLHVCQUF1QixDQUFDLEtBQUs7aUJBQzFCLHFCQUFxQixDQUFDLEVBQUUsQ0FBQyxDQUFDLGlCQUFpQjtpQkFDM0MscUJBQXFCLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxxQkFBcUI7WUFFbEQsdUJBQXVCLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQztZQUNoRSx3QkFBd0IsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxjQUFjLENBQUMsQ0FBQyxDQUFDO1lBQ2xFLHVCQUF1QixDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUM7WUFFaEUsc0JBQXNCO1lBQ3RCLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBYyxFQUFFLHFCQUFxQixDQUFDLENBQUMsaUJBQWlCLENBQUMsY0FBYyxDQUFDLENBQUM7WUFDcEYsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFjLEVBQUUsbUNBQW1DLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUV0RixNQUFNLE1BQU0sR0FBRyxNQUFNLE9BQU8sQ0FBQyxzQkFBc0IsRUFBRSxDQUFDO1lBRXRELE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDekMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxPQUFPLENBQUM7Z0JBQzdCLGdCQUFnQixFQUFFLENBQUM7Z0JBQ25CLGdCQUFnQixFQUFFLEVBQUU7Z0JBQ3BCLGNBQWMsRUFBRSxFQUFFO2dCQUNsQixrQkFBa0IsRUFBRSxDQUFDO2dCQUNyQixzQkFBc0IsRUFBRSxNQUFNLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQzthQUMzQyxDQUFDLENBQUM7WUFDSCxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLHVCQUF1QixDQUFDLENBQUM7WUFDdkQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUN4QyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLGFBQWEsQ0FBQyxDQUFDO1lBQzdDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsV0FBVyxDQUFDLENBQUM7UUFDN0MsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsaUNBQWlDLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDL0MsdUJBQXVCLENBQUMsS0FBSyxDQUFDLGlCQUFpQixDQUFDLElBQUksS0FBSyxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQztZQUU3RSxNQUFNLE1BQU0sQ0FBQyxPQUFPLENBQUMsc0JBQXNCLEVBQUUsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsZ0JBQWdCLENBQUMsQ0FBQztZQUNqRixNQUFNLENBQUMsaUJBQWlCLENBQUMsS0FBSyxDQUFDLENBQUMsb0JBQW9CLENBQ2xELHlDQUF5QyxFQUN6QyxNQUFNLENBQUMsZ0JBQWdCLENBQUM7Z0JBQ3RCLEtBQUssRUFBRSxnQkFBZ0I7YUFDeEIsQ0FBQyxDQUNILENBQUM7UUFDSixDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGtCQUFrQixFQUFFLEdBQUcsRUFBRTtRQUNoQyxNQUFNLGNBQWMsR0FBRztZQUNyQixJQUFJLEVBQUUsaUJBQWlCO1lBQ3ZCLFdBQVcsRUFBRSw2QkFBNkI7WUFDMUMsY0FBYyxFQUFFLGdCQUF5QjtZQUN6QyxjQUFjLEVBQUUsSUFBSSxJQUFJLEVBQUU7WUFDMUIsS0FBSyxFQUFFO2dCQUNMLFdBQVcsRUFBRSxZQUFZO2FBQzFCO1NBQ0YsQ0FBQztRQUVGLEVBQUUsQ0FBQyx1Q0FBdUMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUNyRCxNQUFNLE1BQU0sR0FBRyxVQUFVLENBQUM7WUFDMUIsTUFBTSxlQUFlLEdBQUcsRUFBRSxHQUFHLGNBQWMsRUFBRSxFQUFFLEVBQUUsZ0JBQWdCLEVBQUUsQ0FBQztZQUVwRSx1QkFBdUIsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsYUFBYSxDQUFDLENBQUM7WUFDakUsd0JBQXdCLENBQUMsTUFBTSxDQUFDLGVBQWUsQ0FBQyxjQUFjLENBQUMsQ0FBQztZQUNoRSx3QkFBd0IsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsZUFBZSxDQUFDLENBQUM7WUFFakUsTUFBTSxNQUFNLEdBQUcsTUFBTSxPQUFPLENBQUMsZ0JBQWdCLENBQUMsZUFBZSxFQUFFLGNBQWMsRUFBRSxNQUFNLENBQUMsQ0FBQztZQUV2RixNQUFNLENBQUMsdUJBQXVCLENBQUMsT0FBTyxDQUFDLENBQUMsb0JBQW9CLENBQUM7Z0JBQzNELEtBQUssRUFBRSxFQUFFLEVBQUUsRUFBRSxlQUFlLEVBQUU7YUFDL0IsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxDQUFDLHdCQUF3QixDQUFDLE1BQU0sQ0FBQyxDQUFDLG9CQUFvQixDQUFDO2dCQUMzRCxHQUFHLGNBQWM7Z0JBQ2pCLFdBQVcsRUFBRSxlQUFlO2dCQUM1QixNQUFNLEVBQUUsU0FBUztnQkFDakIsU0FBUyxFQUFFLE1BQU07YUFDbEIsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxDQUFDLHdCQUF3QixDQUFDLElBQUksQ0FBQyxDQUFDLG9CQUFvQixDQUFDLGNBQWMsQ0FBQyxDQUFDO1lBQzNFLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxhQUFhLENBQUMsQ0FBQyxvQkFBb0IsQ0FDekQsTUFBTSxFQUNOLFFBQVEsRUFDUix1QkFBdUIsRUFDdkIsZUFBZSxDQUFDLEVBQUUsRUFDbEIsTUFBTSxDQUFDLGdCQUFnQixDQUFDO2dCQUN0QixXQUFXLEVBQUUsZUFBZTtnQkFDNUIsY0FBYyxFQUFFLGNBQWMsQ0FBQyxjQUFjO2dCQUM3QyxjQUFjLEVBQUUsY0FBYyxDQUFDLElBQUk7YUFDcEMsQ0FBQyxDQUNILENBQUM7WUFDRixNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsT0FBTyxDQUFDLGVBQWUsQ0FBQyxDQUFDO1FBQzFDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDZDQUE2QyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQzNELE1BQU0sTUFBTSxHQUFHLFVBQVUsQ0FBQztZQUMxQix1QkFBdUIsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLENBQUM7WUFFeEQsTUFBTSxNQUFNLENBQUMsT0FBTyxDQUFDLGdCQUFnQixDQUFDLGNBQWMsRUFBRSxjQUFjLEVBQUUsTUFBTSxDQUFDLENBQUM7aUJBQzNFLE9BQU8sQ0FBQyxPQUFPLENBQUMscUJBQXFCLENBQUMsQ0FBQztRQUM1QyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGlCQUFpQixFQUFFLEdBQUcsRUFBRTtRQUMvQixFQUFFLENBQUMsc0NBQXNDLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDcEQsTUFBTSxZQUFZLEdBQUcsZ0JBQWdCLENBQUM7WUFDdEMsTUFBTSxNQUFNLEdBQUcsVUFBVSxDQUFDO1lBQzFCLE1BQU0sVUFBVSxHQUFHLEVBQUUsR0FBRyxjQUFjLEVBQUUsU0FBUyxFQUFFLGFBQWEsRUFBRSxDQUFDO1lBRW5FLHdCQUF3QixDQUFDLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUMvRCx3QkFBd0IsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsVUFBVSxDQUFDLENBQUM7WUFFNUQscUJBQXFCO1lBQ3JCLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBYyxFQUFFLDhCQUE4QixDQUFDLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLENBQUM7WUFFeEYsTUFBTSxNQUFNLEdBQUcsTUFBTSxPQUFPLENBQUMsZUFBZSxDQUFDLFlBQVksRUFBRSxNQUFNLENBQUMsQ0FBQztZQUVuRSxNQUFNLENBQUMsd0JBQXdCLENBQUMsT0FBTyxDQUFDLENBQUMsb0JBQW9CLENBQUM7Z0JBQzVELEtBQUssRUFBRSxFQUFFLEVBQUUsRUFBRSxZQUFZLEVBQUU7Z0JBQzNCLFNBQVMsRUFBRSxDQUFDLFdBQVcsQ0FBQzthQUN6QixDQUFDLENBQUM7WUFDSCxNQUFNLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxDQUFDLG9CQUFvQixDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQ3RELE1BQU0sQ0FBQyx3QkFBd0IsQ0FBQyxJQUFJLENBQUMsQ0FBQyxvQkFBb0IsQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUN2RSxNQUFNLENBQUMsZ0JBQWdCLENBQUMsYUFBYSxDQUFDLENBQUMsb0JBQW9CLENBQ3pELE1BQU0sRUFDTixPQUFPLEVBQ1AsdUJBQXVCLEVBQ3ZCLFlBQVksRUFDWixNQUFNLENBQUMsZ0JBQWdCLENBQUM7Z0JBQ3RCLFdBQVcsRUFBRSxVQUFVLENBQUMsV0FBVztnQkFDbkMsY0FBYyxFQUFFLFVBQVUsQ0FBQyxjQUFjO2FBQzFDLENBQUMsQ0FDSCxDQUFDO1lBQ0YsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUNyQyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw4Q0FBOEMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUM1RCxNQUFNLFlBQVksR0FBRyxjQUFjLENBQUM7WUFDcEMsTUFBTSxNQUFNLEdBQUcsVUFBVSxDQUFDO1lBRTFCLHdCQUF3QixDQUFDLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUV6RCxNQUFNLE1BQU0sQ0FBQyxPQUFPLENBQUMsZUFBZSxDQUFDLFlBQVksRUFBRSxNQUFNLENBQUMsQ0FBQztpQkFDeEQsT0FBTyxDQUFDLE9BQU8sQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDO1FBQzdDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsb0JBQW9CLEVBQUUsR0FBRyxFQUFFO1FBQ2xDLEVBQUUsQ0FBQyx5Q0FBeUMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUN2RCxNQUFNLFlBQVksR0FBRyxnQkFBZ0IsQ0FBQztZQUN0QyxNQUFNLE1BQU0sR0FBRyxVQUFVLENBQUM7WUFDMUIsTUFBTSxPQUFPLEdBQUc7Z0JBQ2QsWUFBWSxFQUFFLEVBQUU7Z0JBQ2hCLGFBQWEsRUFBRSxXQUFXO2dCQUMxQixhQUFhLEVBQUUsRUFBRTthQUNsQixDQUFDO1lBQ0YsTUFBTSxVQUFVLEdBQUcsRUFBRSxHQUFHLGNBQWMsRUFBRSxTQUFTLEVBQUUsYUFBYSxFQUFFLENBQUM7WUFFbkUsd0JBQXdCLENBQUMsT0FBTyxDQUFDLGlCQUFpQixDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBQy9ELHdCQUF3QixDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUU1RCxzQkFBc0I7WUFDdEIsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFjLEVBQUUsa0NBQWtDLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUU1RixNQUFNLE1BQU0sR0FBRyxNQUFNLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBQyxZQUFZLEVBQUUsT0FBTyxFQUFFLE1BQU0sQ0FBQyxDQUFDO1lBRS9FLE1BQU0sQ0FBQyx3QkFBd0IsQ0FBQyxPQUFPLENBQUMsQ0FBQyxvQkFBb0IsQ0FBQztnQkFDNUQsS0FBSyxFQUFFLEVBQUUsRUFBRSxFQUFFLFlBQVksRUFBRTtnQkFDM0IsU0FBUyxFQUFFLENBQUMsV0FBVyxDQUFDO2FBQ3pCLENBQUMsQ0FBQztZQUNILE1BQU0sQ0FBQyxVQUFVLENBQUMsUUFBUSxDQUFDLENBQUMsb0JBQW9CLENBQUMsTUFBTSxFQUFFLE9BQU8sQ0FBQyxDQUFDO1lBQ2xFLE1BQU0sQ0FBQyx3QkFBd0IsQ0FBQyxJQUFJLENBQUMsQ0FBQyxvQkFBb0IsQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUN2RSxNQUFNLENBQUMsdUJBQXVCLENBQUMsMEJBQTBCLENBQUMsQ0FBQyxvQkFBb0IsQ0FDN0UsVUFBVSxFQUNWLHNCQUFzQixDQUN2QixDQUFDO1lBQ0YsTUFBTSxDQUFDLGdCQUFnQixDQUFDLGFBQWEsQ0FBQyxDQUFDLG9CQUFvQixDQUN6RCxNQUFNLEVBQ04sVUFBVSxFQUNWLHVCQUF1QixFQUN2QixZQUFZLEVBQ1osTUFBTSxDQUFDLGdCQUFnQixDQUFDO2dCQUN0QixXQUFXLEVBQUUsVUFBVSxDQUFDLFdBQVc7Z0JBQ25DLGVBQWUsRUFBRSxPQUFPLENBQUMsWUFBWTtnQkFDckMsZ0JBQWdCLEVBQUUsT0FBTyxDQUFDLGFBQWE7YUFDeEMsQ0FBQyxDQUNILENBQUM7WUFDRixNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQ3JDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsOEJBQThCLEVBQUUsR0FBRyxFQUFFO1FBQzVDLEVBQUUsQ0FBQywyQ0FBMkMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUN6RCxNQUFNLFdBQVcsR0FBRyxlQUFlLENBQUM7WUFFcEMsdUJBQXVCLENBQUMsT0FBTyxDQUFDLGlCQUFpQixDQUFDLGFBQWEsQ0FBQyxDQUFDO1lBRWpFLHNCQUFzQjtZQUN0QixJQUFJLENBQUMsS0FBSyxDQUFDLE9BQWMsRUFBRSxxQkFBcUIsQ0FBQyxDQUFDLGlCQUFpQixDQUFDLGNBQWMsQ0FBQyxDQUFDO1lBQ3BGLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBYyxFQUFFLHdCQUF3QixDQUFDLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDO1lBQ3hGLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBYyxFQUFFLHNCQUFzQixDQUFDLENBQUMsaUJBQWlCLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDekUsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFjLEVBQUUsbUNBQW1DLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUN0RixJQUFJLENBQUMsS0FBSyxDQUFDLE9BQWMsRUFBRSw2QkFBNkIsQ0FBQyxDQUFDLGlCQUFpQixDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBRXRGLE1BQU0sTUFBTSxHQUFHLE1BQU0sT0FBTyxDQUFDLDRCQUE0QixDQUFDLFdBQVcsQ0FBQyxDQUFDO1lBRXZFLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsV0FBVyxDQUFDLENBQUM7WUFDM0MsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDO1lBQ2xELE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsWUFBWSxDQUFDLENBQUM7WUFDNUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUMxQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLGlCQUFpQixDQUFDLENBQUM7WUFDakQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxXQUFXLENBQUMsQ0FBQztZQUMzQyxNQUFNLENBQUMsTUFBTSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUM7WUFDOUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxlQUFlLENBQUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDeEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUM7UUFDMUMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsNkNBQTZDLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDM0QsTUFBTSxXQUFXLEdBQUcsY0FBYyxDQUFDO1lBQ25DLHVCQUF1QixDQUFDLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUV4RCxNQUFNLE1BQU0sQ0FBQyxPQUFPLENBQUMsNEJBQTRCLENBQUMsV0FBVyxDQUFDLENBQUM7aUJBQzVELE9BQU8sQ0FBQyxPQUFPLENBQUMscUJBQXFCLENBQUMsQ0FBQztRQUM1QyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLHFCQUFxQixFQUFFLEdBQUcsRUFBRTtRQUNuQyxFQUFFLENBQUMsaUNBQWlDLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDL0MsTUFBTSxJQUFJLEdBQUcsRUFBRSxDQUFDO1lBQ2hCLE1BQU0sV0FBVyxHQUFHLENBQUMsY0FBYyxDQUFDLENBQUM7WUFDckMsTUFBTSxVQUFVLEdBQUcsQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUVuQyx3QkFBd0IsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsV0FBVyxDQUFDLENBQUM7WUFDN0QsdUJBQXVCLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBRTNELHNCQUFzQjtZQUN0QixJQUFJLENBQUMsS0FBSyxDQUFDLE9BQWMsRUFBRSxtQkFBbUIsQ0FBQyxDQUFDLGVBQWUsQ0FBQztnQkFDOUQ7b0JBQ0UsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFO29CQUNyQixPQUFPLEVBQUUsSUFBSSxJQUFJLEVBQUU7b0JBQ25CLFdBQVcsRUFBRSxDQUFDO29CQUNkLFVBQVUsRUFBRSxDQUFDO29CQUNiLHNCQUFzQixFQUFFLEVBQUU7aUJBQzNCO2FBQ0YsQ0FBQyxDQUFDO1lBQ0gsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFjLEVBQUUsaUNBQWlDLENBQUMsQ0FBQyxlQUFlLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDbEYsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFjLEVBQUUseUJBQXlCLENBQUMsQ0FBQyxlQUFlLENBQUMsV0FBVyxDQUFDLENBQUM7WUFFbkYsTUFBTSxNQUFNLEdBQUcsTUFBTSxPQUFPLENBQUMsbUJBQW1CLENBQUMsSUFBSSxDQUFDLENBQUM7WUFFdkQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUN4QyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ3hDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDekMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3RDLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLGdCQUFnQixDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2hELE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLGVBQWUsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMvQyxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUN2RCxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxjQUFjLENBQUMsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUM7UUFDMUQsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxtQkFBbUIsRUFBRSxHQUFHLEVBQUU7UUFDakMsRUFBRSxDQUFDLHFEQUFxRCxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ25FLE1BQU0sZ0JBQWdCLEdBQUcsQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUN6Qyx1QkFBdUIsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsZ0JBQWdCLENBQUMsQ0FBQztZQUVqRSxzQkFBc0I7WUFDdEIsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsNEJBQTRCLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUMvRSxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQWMsRUFBRSx5QkFBeUIsQ0FBQyxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBQ25GLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBYyxFQUFFLHVCQUF1QixDQUFDLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLENBQUM7WUFFakYsTUFBTSxPQUFPLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztZQUVsQyxNQUFNLENBQUMsdUJBQXVCLENBQUMsSUFBSSxDQUFDLENBQUMsb0JBQW9CLENBQUM7Z0JBQ3hELEtBQUssRUFBRSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUU7YUFDMUIsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxDQUFDLE9BQU8sQ0FBQywwQkFBMEIsQ0FBQyxDQUFDLG9CQUFvQixDQUFDLGFBQWEsQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUNsRixNQUFNLENBQUMsaUJBQWlCLENBQUMsR0FBRyxDQUFDLENBQUMsb0JBQW9CLENBQ2hELHVDQUF1QyxFQUN2QyxNQUFNLENBQUMsZ0JBQWdCLENBQUM7Z0JBQ3RCLG1CQUFtQixFQUFFLENBQUM7YUFDdkIsQ0FBQyxDQUNILENBQUM7UUFDSixDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxzREFBc0QsRUFBRSxLQUFLLElBQUksRUFBRTtZQUNwRSxNQUFNLGdCQUFnQixHQUFHLENBQUMsYUFBYSxDQUFDLENBQUM7WUFDekMsdUJBQXVCLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLGdCQUFnQixDQUFDLENBQUM7WUFFakUsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsNEJBQTRCLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLEtBQUssQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLENBQUM7WUFDbkcsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFjLEVBQUUseUJBQXlCLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUNuRixJQUFJLENBQUMsS0FBSyxDQUFDLE9BQWMsRUFBRSx1QkFBdUIsQ0FBQyxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBRWpGLE1BQU0sT0FBTyxDQUFDLGlCQUFpQixFQUFFLENBQUM7WUFFbEMsTUFBTSxDQUFDLGlCQUFpQixDQUFDLEtBQUssQ0FBQyxDQUFDLG9CQUFvQixDQUNsRCx3Q0FBd0MsRUFDeEMsTUFBTSxDQUFDLGdCQUFnQixDQUFDO2dCQUN0QixXQUFXLEVBQUUsYUFBYSxDQUFDLEVBQUU7Z0JBQzdCLGFBQWEsRUFBRSxhQUFhLENBQUMsSUFBSTtnQkFDakMsS0FBSyxFQUFFLGtCQUFrQjthQUMxQixDQUFDLENBQ0gsQ0FBQztRQUNKLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsNEJBQTRCLEVBQUUsR0FBRyxFQUFFO1FBQzFDLEVBQUUsQ0FBQyxrREFBa0QsRUFBRSxLQUFLLElBQUksRUFBRTtZQUNoRSxNQUFNLFdBQVcsR0FBRyxlQUFlLENBQUM7WUFDcEMsTUFBTSxTQUFTLEdBQUcsRUFBRSxHQUFHLGFBQWEsRUFBRSxXQUFXLEVBQUUsRUFBRSxFQUFFLFFBQVEsRUFBRSxFQUFFLEVBQUUsQ0FBQztZQUV0RSx1QkFBdUIsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLENBQUM7WUFFN0Qsc0JBQXNCO1lBQ3RCLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBYyxFQUFFLHFCQUFxQixDQUFDLENBQUMsaUJBQWlCLENBQUMsY0FBYyxDQUFDLENBQUM7WUFDcEYsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFjLEVBQUUsaUJBQWlCLENBQUMsQ0FBQyxlQUFlLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDckUsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFjLEVBQUUsMkJBQTJCLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUNyRixJQUFJLENBQUMsS0FBSyxDQUFDLE9BQWMsRUFBRSx1QkFBdUIsQ0FBQyxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBQ2pGLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBYyxFQUFFLHlCQUF5QixDQUFDLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLENBQUM7WUFFbkYsTUFBTSxPQUFPLENBQUMsMEJBQTBCLENBQUMsV0FBVyxDQUFDLENBQUM7WUFFdEQsTUFBTSxDQUFDLHVCQUF1QixDQUFDLE9BQU8sQ0FBQyxDQUFDLG9CQUFvQixDQUFDO2dCQUMzRCxLQUFLLEVBQUUsRUFBRSxFQUFFLEVBQUUsV0FBVyxFQUFFO2dCQUMxQixTQUFTLEVBQUUsQ0FBQyxhQUFhLEVBQUUsVUFBVSxDQUFDO2FBQ3ZDLENBQUMsQ0FBQztZQUNILE1BQU0sQ0FBQyxpQkFBaUIsQ0FBQyxLQUFLLENBQUMsQ0FBQyxvQkFBb0IsQ0FDbEQsMkNBQTJDLEVBQzNDLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQztnQkFDdEIsV0FBVzthQUNaLENBQUMsQ0FDSCxDQUFDO1FBQ0osQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsNkNBQTZDLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDM0QsTUFBTSxXQUFXLEdBQUcsY0FBYyxDQUFDO1lBQ25DLHVCQUF1QixDQUFDLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUV4RCxNQUFNLE1BQU0sQ0FBQyxPQUFPLENBQUMsMEJBQTBCLENBQUMsV0FBVyxDQUFDLENBQUM7aUJBQzFELE9BQU8sQ0FBQyxPQUFPLENBQUMscUJBQXFCLENBQUMsQ0FBQztRQUM1QyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0FBQ0wsQ0FBQyxDQUFDLENBQUMiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFxjb21wbGlhbmNlLWF1ZGl0XFxhcHBsaWNhdGlvblxcc2VydmljZXNcXGNvbXBsaWFuY2UtbW9uaXRvcmluZy5zZXJ2aWNlLnNwZWMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVGVzdCwgVGVzdGluZ01vZHVsZSB9IGZyb20gJ0BuZXN0anMvdGVzdGluZyc7XHJcbmltcG9ydCB7IGdldFJlcG9zaXRvcnlUb2tlbiB9IGZyb20gJ0BuZXN0anMvdHlwZW9ybSc7XHJcbmltcG9ydCB7IFJlcG9zaXRvcnkgfSBmcm9tICd0eXBlb3JtJztcclxuaW1wb3J0IHsgQ29tcGxpYW5jZU1vbml0b3JpbmdTZXJ2aWNlIH0gZnJvbSAnLi9jb21wbGlhbmNlLW1vbml0b3Jpbmcuc2VydmljZSc7XHJcbmltcG9ydCB7IENvbXBsaWFuY2VGcmFtZXdvcmsgfSBmcm9tICcuLi8uLi9kb21haW4vZW50aXRpZXMvY29tcGxpYW5jZS1mcmFtZXdvcmsuZW50aXR5JztcclxuaW1wb3J0IHsgQ29tcGxpYW5jZUFzc2Vzc21lbnQgfSBmcm9tICcuLi8uLi9kb21haW4vZW50aXRpZXMvY29tcGxpYW5jZS1hc3Nlc3NtZW50LmVudGl0eSc7XHJcbmltcG9ydCB7IFBvbGljeURlZmluaXRpb24gfSBmcm9tICcuLi8uLi9kb21haW4vZW50aXRpZXMvcG9saWN5LWRlZmluaXRpb24uZW50aXR5JztcclxuaW1wb3J0IHsgUG9saWN5VmlvbGF0aW9uIH0gZnJvbSAnLi4vLi4vZG9tYWluL2VudGl0aWVzL3BvbGljeS12aW9sYXRpb24uZW50aXR5JztcclxuaW1wb3J0IHsgQXVkaXRMb2cgfSBmcm9tICcuLi8uLi9kb21haW4vZW50aXRpZXMvYXVkaXQtbG9nLmVudGl0eSc7XHJcbmltcG9ydCB7IExvZ2dlclNlcnZpY2UgfSBmcm9tICcuLi8uLi8uLi8uLi9pbmZyYXN0cnVjdHVyZS9sb2dnaW5nL2xvZ2dlci5zZXJ2aWNlJztcclxuaW1wb3J0IHsgQXVkaXRTZXJ2aWNlIH0gZnJvbSAnLi4vLi4vLi4vLi4vaW5mcmFzdHJ1Y3R1cmUvbG9nZ2luZy9hdWRpdC9hdWRpdC5zZXJ2aWNlJztcclxuaW1wb3J0IHsgTm90aWZpY2F0aW9uU2VydmljZSB9IGZyb20gJy4uLy4uLy4uLy4uL2luZnJhc3RydWN0dXJlL25vdGlmaWNhdGlvbi9ub3RpZmljYXRpb24uc2VydmljZSc7XHJcbmltcG9ydCB7IFBvbGljeUVuZm9yY2VtZW50U2VydmljZSB9IGZyb20gJy4vcG9saWN5LWVuZm9yY2VtZW50LnNlcnZpY2UnO1xyXG5pbXBvcnQgeyBWaW9sYXRpb25EZXRlY3Rpb25TZXJ2aWNlIH0gZnJvbSAnLi92aW9sYXRpb24tZGV0ZWN0aW9uLnNlcnZpY2UnO1xyXG5cclxuZGVzY3JpYmUoJ0NvbXBsaWFuY2VNb25pdG9yaW5nU2VydmljZScsICgpID0+IHtcclxuICBsZXQgc2VydmljZTogQ29tcGxpYW5jZU1vbml0b3JpbmdTZXJ2aWNlO1xyXG4gIGxldCBmcmFtZXdvcmtSZXBvc2l0b3J5OiBSZXBvc2l0b3J5PENvbXBsaWFuY2VGcmFtZXdvcms+O1xyXG4gIGxldCBhc3Nlc3NtZW50UmVwb3NpdG9yeTogUmVwb3NpdG9yeTxDb21wbGlhbmNlQXNzZXNzbWVudD47XHJcbiAgbGV0IHBvbGljeVJlcG9zaXRvcnk6IFJlcG9zaXRvcnk8UG9saWN5RGVmaW5pdGlvbj47XHJcbiAgbGV0IHZpb2xhdGlvblJlcG9zaXRvcnk6IFJlcG9zaXRvcnk8UG9saWN5VmlvbGF0aW9uPjtcclxuICBsZXQgYXVkaXRMb2dSZXBvc2l0b3J5OiBSZXBvc2l0b3J5PEF1ZGl0TG9nPjtcclxuICBsZXQgbG9nZ2VyU2VydmljZTogTG9nZ2VyU2VydmljZTtcclxuICBsZXQgYXVkaXRTZXJ2aWNlOiBBdWRpdFNlcnZpY2U7XHJcbiAgbGV0IG5vdGlmaWNhdGlvblNlcnZpY2U6IE5vdGlmaWNhdGlvblNlcnZpY2U7XHJcbiAgbGV0IHBvbGljeUVuZm9yY2VtZW50U2VydmljZTogUG9saWN5RW5mb3JjZW1lbnRTZXJ2aWNlO1xyXG4gIGxldCB2aW9sYXRpb25EZXRlY3Rpb25TZXJ2aWNlOiBWaW9sYXRpb25EZXRlY3Rpb25TZXJ2aWNlO1xyXG5cclxuICBjb25zdCBtb2NrRnJhbWV3b3JrUmVwb3NpdG9yeSA9IHtcclxuICAgIGZpbmQ6IGplc3QuZm4oKSxcclxuICAgIGZpbmRPbmU6IGplc3QuZm4oKSxcclxuICAgIGNyZWF0ZTogamVzdC5mbigpLFxyXG4gICAgc2F2ZTogamVzdC5mbigpLFxyXG4gICAgY291bnQ6IGplc3QuZm4oKSxcclxuICAgIGNyZWF0ZVF1ZXJ5QnVpbGRlcjogamVzdC5mbigpLFxyXG4gIH07XHJcblxyXG4gIGNvbnN0IG1vY2tBc3Nlc3NtZW50UmVwb3NpdG9yeSA9IHtcclxuICAgIGZpbmQ6IGplc3QuZm4oKSxcclxuICAgIGZpbmRPbmU6IGplc3QuZm4oKSxcclxuICAgIGNyZWF0ZTogamVzdC5mbigpLFxyXG4gICAgc2F2ZTogamVzdC5mbigpLFxyXG4gICAgY291bnQ6IGplc3QuZm4oKSxcclxuICAgIGNyZWF0ZVF1ZXJ5QnVpbGRlcjogamVzdC5mbigpLFxyXG4gIH07XHJcblxyXG4gIGNvbnN0IG1vY2tQb2xpY3lSZXBvc2l0b3J5ID0ge1xyXG4gICAgZmluZDogamVzdC5mbigpLFxyXG4gICAgZmluZE9uZTogamVzdC5mbigpLFxyXG4gICAgY3JlYXRlOiBqZXN0LmZuKCksXHJcbiAgICBzYXZlOiBqZXN0LmZuKCksXHJcbiAgICBjb3VudDogamVzdC5mbigpLFxyXG4gIH07XHJcblxyXG4gIGNvbnN0IG1vY2tWaW9sYXRpb25SZXBvc2l0b3J5ID0ge1xyXG4gICAgZmluZDogamVzdC5mbigpLFxyXG4gICAgZmluZE9uZTogamVzdC5mbigpLFxyXG4gICAgY3JlYXRlOiBqZXN0LmZuKCksXHJcbiAgICBzYXZlOiBqZXN0LmZuKCksXHJcbiAgICBjb3VudDogamVzdC5mbigpLFxyXG4gICAgY3JlYXRlUXVlcnlCdWlsZGVyOiBqZXN0LmZuKCksXHJcbiAgfTtcclxuXHJcbiAgY29uc3QgbW9ja0F1ZGl0TG9nUmVwb3NpdG9yeSA9IHtcclxuICAgIGZpbmQ6IGplc3QuZm4oKSxcclxuICAgIGZpbmRPbmU6IGplc3QuZm4oKSxcclxuICAgIGNyZWF0ZTogamVzdC5mbigpLFxyXG4gICAgc2F2ZTogamVzdC5mbigpLFxyXG4gICAgY291bnQ6IGplc3QuZm4oKSxcclxuICB9O1xyXG5cclxuICBjb25zdCBtb2NrTG9nZ2VyU2VydmljZSA9IHtcclxuICAgIGRlYnVnOiBqZXN0LmZuKCksXHJcbiAgICBsb2c6IGplc3QuZm4oKSxcclxuICAgIHdhcm46IGplc3QuZm4oKSxcclxuICAgIGVycm9yOiBqZXN0LmZuKCksXHJcbiAgfTtcclxuXHJcbiAgY29uc3QgbW9ja0F1ZGl0U2VydmljZSA9IHtcclxuICAgIGxvZ1VzZXJBY3Rpb246IGplc3QuZm4oKSxcclxuICB9O1xyXG5cclxuICBjb25zdCBtb2NrTm90aWZpY2F0aW9uU2VydmljZSA9IHtcclxuICAgIHNlbmRDb21wbGlhbmNlTm90aWZpY2F0aW9uOiBqZXN0LmZuKCksXHJcbiAgICBzZW5kQXNzZXNzbWVudE92ZXJkdWVOb3RpZmljYXRpb246IGplc3QuZm4oKSxcclxuICAgIHNlbmRQb2xpY3lWaW9sYXRpb25Ob3RpZmljYXRpb246IGplc3QuZm4oKSxcclxuICB9O1xyXG5cclxuICBjb25zdCBtb2NrUG9saWN5RW5mb3JjZW1lbnRTZXJ2aWNlID0ge1xyXG4gICAgZXZhbHVhdGVDb250cm9sOiBqZXN0LmZuKCksXHJcbiAgICBldmFsdWF0ZVBvbGljeUNvbXBsaWFuY2U6IGplc3QuZm4oKSxcclxuICB9O1xyXG5cclxuICBjb25zdCBtb2NrVmlvbGF0aW9uRGV0ZWN0aW9uU2VydmljZSA9IHtcclxuICAgIGNoZWNrUG9saWN5Q29tcGxpYW5jZTogamVzdC5mbigpLFxyXG4gICAgc2NhbkZvclZpb2xhdGlvbnM6IGplc3QuZm4oKSxcclxuICAgIGNyZWF0ZVZpb2xhdGlvbjogamVzdC5mbigpLFxyXG4gIH07XHJcblxyXG4gIGNvbnN0IG1vY2tRdWVyeUJ1aWxkZXIgPSB7XHJcbiAgICB3aGVyZTogamVzdC5mbigpLm1vY2tSZXR1cm5UaGlzKCksXHJcbiAgICBhbmRXaGVyZTogamVzdC5mbigpLm1vY2tSZXR1cm5UaGlzKCksXHJcbiAgICBvcmRlckJ5OiBqZXN0LmZuKCkubW9ja1JldHVyblRoaXMoKSxcclxuICAgIHNraXA6IGplc3QuZm4oKS5tb2NrUmV0dXJuVGhpcygpLFxyXG4gICAgdGFrZTogamVzdC5mbigpLm1vY2tSZXR1cm5UaGlzKCksXHJcbiAgICBnZXRNYW55QW5kQ291bnQ6IGplc3QuZm4oKSxcclxuICAgIGdldE1hbnk6IGplc3QuZm4oKSxcclxuICAgIGdldENvdW50OiBqZXN0LmZuKCksXHJcbiAgICBzZWxlY3Q6IGplc3QuZm4oKS5tb2NrUmV0dXJuVGhpcygpLFxyXG4gICAgYWRkU2VsZWN0OiBqZXN0LmZuKCkubW9ja1JldHVyblRoaXMoKSxcclxuICAgIGdyb3VwQnk6IGplc3QuZm4oKS5tb2NrUmV0dXJuVGhpcygpLFxyXG4gICAgZ2V0UmF3TWFueTogamVzdC5mbigpLFxyXG4gICAgZ2V0UmF3T25lOiBqZXN0LmZuKCksXHJcbiAgfTtcclxuXHJcbiAgY29uc3QgbW9ja0ZyYW1ld29yazogUGFydGlhbDxDb21wbGlhbmNlRnJhbWV3b3JrPiA9IHtcclxuICAgIGlkOiAnZnJhbWV3b3JrLTEyMycsXHJcbiAgICBuYW1lOiAnU09DIDIgVHlwZSBJSScsXHJcbiAgICB0eXBlOiAnU09DMicsXHJcbiAgICB2ZXJzaW9uOiAnMjAxNycsXHJcbiAgICBpc0FjdGl2ZTogdHJ1ZSxcclxuICAgIGNvbmZpZ3VyYXRpb246IHtcclxuICAgICAgZG9tYWluczogW1xyXG4gICAgICAgIHtcclxuICAgICAgICAgIGlkOiAnc2VjdXJpdHknLFxyXG4gICAgICAgICAgbmFtZTogJ1NlY3VyaXR5JyxcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnU2VjdXJpdHkgY29udHJvbHMnLFxyXG4gICAgICAgICAgY29udHJvbHM6IFtcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgIGlkOiAnQ0M2LjEnLFxyXG4gICAgICAgICAgICAgIG5hbWU6ICdMb2dpY2FsIEFjY2VzcyBDb250cm9scycsXHJcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICdBY2Nlc3MgY29udHJvbHMgYXJlIGltcGxlbWVudGVkJyxcclxuICAgICAgICAgICAgICB0eXBlOiAncHJldmVudGl2ZScsXHJcbiAgICAgICAgICAgICAgcHJpb3JpdHk6ICdoaWdoJyxcclxuICAgICAgICAgICAgICBmcmVxdWVuY3k6ICdjb250aW51b3VzJyxcclxuICAgICAgICAgICAgICBhdXRvbWF0YWJsZTogdHJ1ZSxcclxuICAgICAgICAgICAgICBldmlkZW5jZVJlcXVpcmVtZW50czogWydhY2Nlc3NfbG9ncycsICdjb25maWd1cmF0aW9uJ10sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICBdLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIF0sXHJcbiAgICB9LFxyXG4gICAgdG90YWxDb250cm9sczogMSxcclxuICAgIGF1dG9tYXRhYmxlQ29udHJvbHM6IFtcclxuICAgICAge1xyXG4gICAgICAgIGlkOiAnQ0M2LjEnLFxyXG4gICAgICAgIG5hbWU6ICdMb2dpY2FsIEFjY2VzcyBDb250cm9scycsXHJcbiAgICAgICAgZG9tYWluSWQ6ICdzZWN1cml0eScsXHJcbiAgICAgICAgZG9tYWluTmFtZTogJ1NlY3VyaXR5JyxcclxuICAgICAgfSxcclxuICAgIF0sXHJcbiAgICBnZXRDb250cm9sc0J5UHJpb3JpdHk6IGplc3QuZm4oKSxcclxuICAgIGdldERvbWFpbkJ5SWQ6IGplc3QuZm4oKSxcclxuICAgIGdldENvbnRyb2xCeUlkOiBqZXN0LmZuKCksXHJcbiAgICB2YWxpZGF0ZUNvbmZpZ3VyYXRpb246IGplc3QuZm4oKS5tb2NrUmV0dXJuVmFsdWUoeyBpc1ZhbGlkOiB0cnVlLCBlcnJvcnM6IFtdIH0pLFxyXG4gICAgY2FsY3VsYXRlQ29tcGxleGl0eVNjb3JlOiBqZXN0LmZuKCkubW9ja1JldHVyblZhbHVlKDEwKSxcclxuICB9O1xyXG5cclxuICBjb25zdCBtb2NrQXNzZXNzbWVudDogUGFydGlhbDxDb21wbGlhbmNlQXNzZXNzbWVudD4gPSB7XHJcbiAgICBpZDogJ2Fzc2Vzc21lbnQtMTIzJyxcclxuICAgIG5hbWU6ICdRNCAyMDIzIFNPQyAyIEFzc2Vzc21lbnQnLFxyXG4gICAgZnJhbWV3b3JrSWQ6ICdmcmFtZXdvcmstMTIzJyxcclxuICAgIGFzc2Vzc21lbnRUeXBlOiAnaW50ZXJuYWxfYXVkaXQnLFxyXG4gICAgc3RhdHVzOiAncGxhbm5lZCcsXHJcbiAgICBhc3Nlc3NtZW50RGF0ZTogbmV3IERhdGUoJzIwMjMtMTItMTUnKSxcclxuICAgIG5leHRBc3Nlc3NtZW50RGF0ZTogbmV3IERhdGUoJzIwMjQtMDMtMTUnKSxcclxuICAgIHNjb3BlOiB7XHJcbiAgICAgIGRlc2NyaXB0aW9uOiAnRnVsbCBvcmdhbml6YXRpb25hbCBhc3Nlc3NtZW50JyxcclxuICAgIH0sXHJcbiAgICBpc092ZXJkdWU6IGZhbHNlLFxyXG4gICAgaXNDb21wbGlhbnQ6IGZhbHNlLFxyXG4gICAgY29tcGxpYW5jZVBlcmNlbnRhZ2U6IDAsXHJcbiAgICBjcml0aWNhbEZpbmRpbmdzQ291bnQ6IDAsXHJcbiAgICBoaWdoRmluZGluZ3NDb3VudDogMCxcclxuICAgIGFnZUluRGF5czogMzAsXHJcbiAgICBzdGFydDogamVzdC5mbigpLFxyXG4gICAgY29tcGxldGU6IGplc3QuZm4oKSxcclxuICAgIGdlbmVyYXRlU3VtbWFyeTogamVzdC5mbigpLm1vY2tSZXR1cm5WYWx1ZSh7XHJcbiAgICAgIGlkOiAnYXNzZXNzbWVudC0xMjMnLFxyXG4gICAgICBuYW1lOiAnUTQgMjAyMyBTT0MgMiBBc3Nlc3NtZW50JyxcclxuICAgICAgc3RhdHVzOiAncGxhbm5lZCcsXHJcbiAgICB9KSxcclxuICB9O1xyXG5cclxuICBjb25zdCBtb2NrVmlvbGF0aW9uOiBQYXJ0aWFsPFBvbGljeVZpb2xhdGlvbj4gPSB7XHJcbiAgICBpZDogJ3Zpb2xhdGlvbi0xMjMnLFxyXG4gICAgdGl0bGU6ICdBY2Nlc3MgQ29udHJvbCBWaW9sYXRpb24nLFxyXG4gICAgc2V2ZXJpdHk6ICdoaWdoJyxcclxuICAgIHN0YXR1czogJ29wZW4nLFxyXG4gICAgZGV0ZWN0ZWRBdDogbmV3IERhdGUoKSxcclxuICAgIGRldGFpbHM6IHtcclxuICAgICAgdmlvbGF0ZWRDb250cm9sczogWydDQzYuMSddLFxyXG4gICAgICBjb21wbGlhbmNlOiB7XHJcbiAgICAgICAgZnJhbWV3b3JrczogWydmcmFtZXdvcmstMTIzJ10sXHJcbiAgICAgICAgY29udHJvbHM6IFsnQ0M2LjEnXSxcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBnZW5lcmF0ZVN1bW1hcnk6IGplc3QuZm4oKS5tb2NrUmV0dXJuVmFsdWUoe1xyXG4gICAgICBpZDogJ3Zpb2xhdGlvbi0xMjMnLFxyXG4gICAgICB0aXRsZTogJ0FjY2VzcyBDb250cm9sIFZpb2xhdGlvbicsXHJcbiAgICAgIHNldmVyaXR5OiAnaGlnaCcsXHJcbiAgICAgIHN0YXR1czogJ29wZW4nLFxyXG4gICAgfSksXHJcbiAgfTtcclxuXHJcbiAgYmVmb3JlRWFjaChhc3luYyAoKSA9PiB7XHJcbiAgICBjb25zdCBtb2R1bGU6IFRlc3RpbmdNb2R1bGUgPSBhd2FpdCBUZXN0LmNyZWF0ZVRlc3RpbmdNb2R1bGUoe1xyXG4gICAgICBwcm92aWRlcnM6IFtcclxuICAgICAgICBDb21wbGlhbmNlTW9uaXRvcmluZ1NlcnZpY2UsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgcHJvdmlkZTogZ2V0UmVwb3NpdG9yeVRva2VuKENvbXBsaWFuY2VGcmFtZXdvcmspLFxyXG4gICAgICAgICAgdXNlVmFsdWU6IG1vY2tGcmFtZXdvcmtSZXBvc2l0b3J5LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgcHJvdmlkZTogZ2V0UmVwb3NpdG9yeVRva2VuKENvbXBsaWFuY2VBc3Nlc3NtZW50KSxcclxuICAgICAgICAgIHVzZVZhbHVlOiBtb2NrQXNzZXNzbWVudFJlcG9zaXRvcnksXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBwcm92aWRlOiBnZXRSZXBvc2l0b3J5VG9rZW4oUG9saWN5RGVmaW5pdGlvbiksXHJcbiAgICAgICAgICB1c2VWYWx1ZTogbW9ja1BvbGljeVJlcG9zaXRvcnksXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBwcm92aWRlOiBnZXRSZXBvc2l0b3J5VG9rZW4oUG9saWN5VmlvbGF0aW9uKSxcclxuICAgICAgICAgIHVzZVZhbHVlOiBtb2NrVmlvbGF0aW9uUmVwb3NpdG9yeSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIHByb3ZpZGU6IGdldFJlcG9zaXRvcnlUb2tlbihBdWRpdExvZyksXHJcbiAgICAgICAgICB1c2VWYWx1ZTogbW9ja0F1ZGl0TG9nUmVwb3NpdG9yeSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIHByb3ZpZGU6IExvZ2dlclNlcnZpY2UsXHJcbiAgICAgICAgICB1c2VWYWx1ZTogbW9ja0xvZ2dlclNlcnZpY2UsXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBwcm92aWRlOiBBdWRpdFNlcnZpY2UsXHJcbiAgICAgICAgICB1c2VWYWx1ZTogbW9ja0F1ZGl0U2VydmljZSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIHByb3ZpZGU6IE5vdGlmaWNhdGlvblNlcnZpY2UsXHJcbiAgICAgICAgICB1c2VWYWx1ZTogbW9ja05vdGlmaWNhdGlvblNlcnZpY2UsXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBwcm92aWRlOiBQb2xpY3lFbmZvcmNlbWVudFNlcnZpY2UsXHJcbiAgICAgICAgICB1c2VWYWx1ZTogbW9ja1BvbGljeUVuZm9yY2VtZW50U2VydmljZSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIHByb3ZpZGU6IFZpb2xhdGlvbkRldGVjdGlvblNlcnZpY2UsXHJcbiAgICAgICAgICB1c2VWYWx1ZTogbW9ja1Zpb2xhdGlvbkRldGVjdGlvblNlcnZpY2UsXHJcbiAgICAgICAgfSxcclxuICAgICAgXSxcclxuICAgIH0pLmNvbXBpbGUoKTtcclxuXHJcbiAgICBzZXJ2aWNlID0gbW9kdWxlLmdldDxDb21wbGlhbmNlTW9uaXRvcmluZ1NlcnZpY2U+KENvbXBsaWFuY2VNb25pdG9yaW5nU2VydmljZSk7XHJcbiAgICBmcmFtZXdvcmtSZXBvc2l0b3J5ID0gbW9kdWxlLmdldDxSZXBvc2l0b3J5PENvbXBsaWFuY2VGcmFtZXdvcms+PihnZXRSZXBvc2l0b3J5VG9rZW4oQ29tcGxpYW5jZUZyYW1ld29yaykpO1xyXG4gICAgYXNzZXNzbWVudFJlcG9zaXRvcnkgPSBtb2R1bGUuZ2V0PFJlcG9zaXRvcnk8Q29tcGxpYW5jZUFzc2Vzc21lbnQ+PihnZXRSZXBvc2l0b3J5VG9rZW4oQ29tcGxpYW5jZUFzc2Vzc21lbnQpKTtcclxuICAgIHBvbGljeVJlcG9zaXRvcnkgPSBtb2R1bGUuZ2V0PFJlcG9zaXRvcnk8UG9saWN5RGVmaW5pdGlvbj4+KGdldFJlcG9zaXRvcnlUb2tlbihQb2xpY3lEZWZpbml0aW9uKSk7XHJcbiAgICB2aW9sYXRpb25SZXBvc2l0b3J5ID0gbW9kdWxlLmdldDxSZXBvc2l0b3J5PFBvbGljeVZpb2xhdGlvbj4+KGdldFJlcG9zaXRvcnlUb2tlbihQb2xpY3lWaW9sYXRpb24pKTtcclxuICAgIGF1ZGl0TG9nUmVwb3NpdG9yeSA9IG1vZHVsZS5nZXQ8UmVwb3NpdG9yeTxBdWRpdExvZz4+KGdldFJlcG9zaXRvcnlUb2tlbihBdWRpdExvZykpO1xyXG4gICAgbG9nZ2VyU2VydmljZSA9IG1vZHVsZS5nZXQ8TG9nZ2VyU2VydmljZT4oTG9nZ2VyU2VydmljZSk7XHJcbiAgICBhdWRpdFNlcnZpY2UgPSBtb2R1bGUuZ2V0PEF1ZGl0U2VydmljZT4oQXVkaXRTZXJ2aWNlKTtcclxuICAgIG5vdGlmaWNhdGlvblNlcnZpY2UgPSBtb2R1bGUuZ2V0PE5vdGlmaWNhdGlvblNlcnZpY2U+KE5vdGlmaWNhdGlvblNlcnZpY2UpO1xyXG4gICAgcG9saWN5RW5mb3JjZW1lbnRTZXJ2aWNlID0gbW9kdWxlLmdldDxQb2xpY3lFbmZvcmNlbWVudFNlcnZpY2U+KFBvbGljeUVuZm9yY2VtZW50U2VydmljZSk7XHJcbiAgICB2aW9sYXRpb25EZXRlY3Rpb25TZXJ2aWNlID0gbW9kdWxlLmdldDxWaW9sYXRpb25EZXRlY3Rpb25TZXJ2aWNlPihWaW9sYXRpb25EZXRlY3Rpb25TZXJ2aWNlKTtcclxuXHJcbiAgICAvLyBTZXR1cCBxdWVyeSBidWlsZGVyIG1vY2tzXHJcbiAgICBtb2NrRnJhbWV3b3JrUmVwb3NpdG9yeS5jcmVhdGVRdWVyeUJ1aWxkZXIubW9ja1JldHVyblZhbHVlKG1vY2tRdWVyeUJ1aWxkZXIpO1xyXG4gICAgbW9ja0Fzc2Vzc21lbnRSZXBvc2l0b3J5LmNyZWF0ZVF1ZXJ5QnVpbGRlci5tb2NrUmV0dXJuVmFsdWUobW9ja1F1ZXJ5QnVpbGRlcik7XHJcbiAgICBtb2NrVmlvbGF0aW9uUmVwb3NpdG9yeS5jcmVhdGVRdWVyeUJ1aWxkZXIubW9ja1JldHVyblZhbHVlKG1vY2tRdWVyeUJ1aWxkZXIpO1xyXG4gIH0pO1xyXG5cclxuICBhZnRlckVhY2goKCkgPT4ge1xyXG4gICAgamVzdC5jbGVhckFsbE1vY2tzKCk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdnZXRDb21wbGlhbmNlRGFzaGJvYXJkJywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCByZXR1cm4gY29tcGxpYW5jZSBkYXNoYm9hcmQgZGF0YScsIGFzeW5jICgpID0+IHtcclxuICAgICAgLy8gTW9jayByZXBvc2l0b3J5IHJlc3BvbnNlc1xyXG4gICAgICBtb2NrRnJhbWV3b3JrUmVwb3NpdG9yeS5jb3VudC5tb2NrUmVzb2x2ZWRWYWx1ZSg1KTsgLy8gYWN0aXZlRnJhbWV3b3Jrc1xyXG4gICAgICBtb2NrQXNzZXNzbWVudFJlcG9zaXRvcnkuY291bnQubW9ja1Jlc29sdmVkVmFsdWUoMjApOyAvLyB0b3RhbEFzc2Vzc21lbnRzXHJcbiAgICAgIG1vY2tWaW9sYXRpb25SZXBvc2l0b3J5LmNvdW50XHJcbiAgICAgICAgLm1vY2tSZXNvbHZlZFZhbHVlT25jZSgxNSkgLy8gb3BlblZpb2xhdGlvbnNcclxuICAgICAgICAubW9ja1Jlc29sdmVkVmFsdWVPbmNlKDMpOyAvLyBjcml0aWNhbFZpb2xhdGlvbnNcclxuXHJcbiAgICAgIG1vY2tGcmFtZXdvcmtSZXBvc2l0b3J5LmZpbmQubW9ja1Jlc29sdmVkVmFsdWUoW21vY2tGcmFtZXdvcmtdKTtcclxuICAgICAgbW9ja0Fzc2Vzc21lbnRSZXBvc2l0b3J5LmZpbmQubW9ja1Jlc29sdmVkVmFsdWUoW21vY2tBc3Nlc3NtZW50XSk7XHJcbiAgICAgIG1vY2tWaW9sYXRpb25SZXBvc2l0b3J5LmZpbmQubW9ja1Jlc29sdmVkVmFsdWUoW21vY2tWaW9sYXRpb25dKTtcclxuXHJcbiAgICAgIC8vIE1vY2sgaGVscGVyIG1ldGhvZHNcclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlIGFzIGFueSwgJ2dldExhdGVzdEFzc2Vzc21lbnQnKS5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrQXNzZXNzbWVudCk7XHJcbiAgICAgIGplc3Quc3B5T24oc2VydmljZSBhcyBhbnksICdjYWxjdWxhdGVGcmFtZXdvcmtDb21wbGlhbmNlU2NvcmUnKS5tb2NrUmVzb2x2ZWRWYWx1ZSg4NSk7XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzZXJ2aWNlLmdldENvbXBsaWFuY2VEYXNoYm9hcmQoKTtcclxuXHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdzdW1tYXJ5Jyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuc3VtbWFyeSkudG9FcXVhbCh7XHJcbiAgICAgICAgYWN0aXZlRnJhbWV3b3JrczogNSxcclxuICAgICAgICB0b3RhbEFzc2Vzc21lbnRzOiAyMCxcclxuICAgICAgICBvcGVuVmlvbGF0aW9uczogMTUsXHJcbiAgICAgICAgY3JpdGljYWxWaW9sYXRpb25zOiAzLFxyXG4gICAgICAgIG92ZXJhbGxDb21wbGlhbmNlU2NvcmU6IGV4cGVjdC5hbnkoTnVtYmVyKSxcclxuICAgICAgfSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdjb21wbGlhbmNlQnlGcmFtZXdvcmsnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ3RyZW5kcycpO1xyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0hhdmVQcm9wZXJ0eSgncmlza01ldHJpY3MnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ3RpbWVzdGFtcCcpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBoYW5kbGUgZXJyb3JzIGdyYWNlZnVsbHknLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIG1vY2tGcmFtZXdvcmtSZXBvc2l0b3J5LmNvdW50Lm1vY2tSZWplY3RlZFZhbHVlKG5ldyBFcnJvcignRGF0YWJhc2UgZXJyb3InKSk7XHJcblxyXG4gICAgICBhd2FpdCBleHBlY3Qoc2VydmljZS5nZXRDb21wbGlhbmNlRGFzaGJvYXJkKCkpLnJlamVjdHMudG9UaHJvdygnRGF0YWJhc2UgZXJyb3InKTtcclxuICAgICAgZXhwZWN0KG1vY2tMb2dnZXJTZXJ2aWNlLmVycm9yKS50b0hhdmVCZWVuQ2FsbGVkV2l0aChcclxuICAgICAgICAnRmFpbGVkIHRvIGdlbmVyYXRlIGNvbXBsaWFuY2UgZGFzaGJvYXJkJyxcclxuICAgICAgICBleHBlY3Qub2JqZWN0Q29udGFpbmluZyh7XHJcbiAgICAgICAgICBlcnJvcjogJ0RhdGFiYXNlIGVycm9yJyxcclxuICAgICAgICB9KSxcclxuICAgICAgKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnY3JlYXRlQXNzZXNzbWVudCcsICgpID0+IHtcclxuICAgIGNvbnN0IGFzc2Vzc21lbnREYXRhID0ge1xyXG4gICAgICBuYW1lOiAnVGVzdCBBc3Nlc3NtZW50JyxcclxuICAgICAgZGVzY3JpcHRpb246ICdUZXN0IGFzc2Vzc21lbnQgZGVzY3JpcHRpb24nLFxyXG4gICAgICBhc3Nlc3NtZW50VHlwZTogJ2ludGVybmFsX2F1ZGl0JyBhcyBjb25zdCxcclxuICAgICAgYXNzZXNzbWVudERhdGU6IG5ldyBEYXRlKCksXHJcbiAgICAgIHNjb3BlOiB7XHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdUZXN0IHNjb3BlJyxcclxuICAgICAgfSxcclxuICAgIH07XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjcmVhdGUgYXNzZXNzbWVudCBzdWNjZXNzZnVsbHknLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHVzZXJJZCA9ICd1c2VyLTEyMyc7XHJcbiAgICAgIGNvbnN0IHNhdmVkQXNzZXNzbWVudCA9IHsgLi4ubW9ja0Fzc2Vzc21lbnQsIGlkOiAnYXNzZXNzbWVudC00NTYnIH07XHJcblxyXG4gICAgICBtb2NrRnJhbWV3b3JrUmVwb3NpdG9yeS5maW5kT25lLm1vY2tSZXNvbHZlZFZhbHVlKG1vY2tGcmFtZXdvcmspO1xyXG4gICAgICBtb2NrQXNzZXNzbWVudFJlcG9zaXRvcnkuY3JlYXRlLm1vY2tSZXR1cm5WYWx1ZShtb2NrQXNzZXNzbWVudCk7XHJcbiAgICAgIG1vY2tBc3Nlc3NtZW50UmVwb3NpdG9yeS5zYXZlLm1vY2tSZXNvbHZlZFZhbHVlKHNhdmVkQXNzZXNzbWVudCk7XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzZXJ2aWNlLmNyZWF0ZUFzc2Vzc21lbnQoJ2ZyYW1ld29yay0xMjMnLCBhc3Nlc3NtZW50RGF0YSwgdXNlcklkKTtcclxuXHJcbiAgICAgIGV4cGVjdChtb2NrRnJhbWV3b3JrUmVwb3NpdG9yeS5maW5kT25lKS50b0hhdmVCZWVuQ2FsbGVkV2l0aCh7XHJcbiAgICAgICAgd2hlcmU6IHsgaWQ6ICdmcmFtZXdvcmstMTIzJyB9LFxyXG4gICAgICB9KTtcclxuICAgICAgZXhwZWN0KG1vY2tBc3Nlc3NtZW50UmVwb3NpdG9yeS5jcmVhdGUpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKHtcclxuICAgICAgICAuLi5hc3Nlc3NtZW50RGF0YSxcclxuICAgICAgICBmcmFtZXdvcmtJZDogJ2ZyYW1ld29yay0xMjMnLFxyXG4gICAgICAgIHN0YXR1czogJ3BsYW5uZWQnLFxyXG4gICAgICAgIGNyZWF0ZWRCeTogdXNlcklkLFxyXG4gICAgICB9KTtcclxuICAgICAgZXhwZWN0KG1vY2tBc3Nlc3NtZW50UmVwb3NpdG9yeS5zYXZlKS50b0hhdmVCZWVuQ2FsbGVkV2l0aChtb2NrQXNzZXNzbWVudCk7XHJcbiAgICAgIGV4cGVjdChtb2NrQXVkaXRTZXJ2aWNlLmxvZ1VzZXJBY3Rpb24pLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKFxyXG4gICAgICAgIHVzZXJJZCxcclxuICAgICAgICAnY3JlYXRlJyxcclxuICAgICAgICAnY29tcGxpYW5jZV9hc3Nlc3NtZW50JyxcclxuICAgICAgICBzYXZlZEFzc2Vzc21lbnQuaWQsXHJcbiAgICAgICAgZXhwZWN0Lm9iamVjdENvbnRhaW5pbmcoe1xyXG4gICAgICAgICAgZnJhbWV3b3JrSWQ6ICdmcmFtZXdvcmstMTIzJyxcclxuICAgICAgICAgIGFzc2Vzc21lbnRUeXBlOiBhc3Nlc3NtZW50RGF0YS5hc3Nlc3NtZW50VHlwZSxcclxuICAgICAgICAgIGFzc2Vzc21lbnROYW1lOiBhc3Nlc3NtZW50RGF0YS5uYW1lLFxyXG4gICAgICAgIH0pLFxyXG4gICAgICApO1xyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0VxdWFsKHNhdmVkQXNzZXNzbWVudCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IGVycm9yIHdoZW4gZnJhbWV3b3JrIG5vdCBmb3VuZCcsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgdXNlcklkID0gJ3VzZXItMTIzJztcclxuICAgICAgbW9ja0ZyYW1ld29ya1JlcG9zaXRvcnkuZmluZE9uZS5tb2NrUmVzb2x2ZWRWYWx1ZShudWxsKTtcclxuXHJcbiAgICAgIGF3YWl0IGV4cGVjdChzZXJ2aWNlLmNyZWF0ZUFzc2Vzc21lbnQoJ25vbi1leGlzdGVudCcsIGFzc2Vzc21lbnREYXRhLCB1c2VySWQpKVxyXG4gICAgICAgIC5yZWplY3RzLnRvVGhyb3coJ0ZyYW1ld29yayBub3QgZm91bmQnKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnc3RhcnRBc3Nlc3NtZW50JywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCBzdGFydCBhc3Nlc3NtZW50IHN1Y2Nlc3NmdWxseScsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgYXNzZXNzbWVudElkID0gJ2Fzc2Vzc21lbnQtMTIzJztcclxuICAgICAgY29uc3QgdXNlcklkID0gJ3VzZXItMTIzJztcclxuICAgICAgY29uc3QgYXNzZXNzbWVudCA9IHsgLi4ubW9ja0Fzc2Vzc21lbnQsIGZyYW1ld29yazogbW9ja0ZyYW1ld29yayB9O1xyXG5cclxuICAgICAgbW9ja0Fzc2Vzc21lbnRSZXBvc2l0b3J5LmZpbmRPbmUubW9ja1Jlc29sdmVkVmFsdWUoYXNzZXNzbWVudCk7XHJcbiAgICAgIG1vY2tBc3Nlc3NtZW50UmVwb3NpdG9yeS5zYXZlLm1vY2tSZXNvbHZlZFZhbHVlKGFzc2Vzc21lbnQpO1xyXG5cclxuICAgICAgLy8gTW9jayBoZWxwZXIgbWV0aG9kXHJcbiAgICAgIGplc3Quc3B5T24oc2VydmljZSBhcyBhbnksICdpbml0aWFsaXplQXNzZXNzbWVudENvbnRyb2xzJykubW9ja1Jlc29sdmVkVmFsdWUodW5kZWZpbmVkKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHNlcnZpY2Uuc3RhcnRBc3Nlc3NtZW50KGFzc2Vzc21lbnRJZCwgdXNlcklkKTtcclxuXHJcbiAgICAgIGV4cGVjdChtb2NrQXNzZXNzbWVudFJlcG9zaXRvcnkuZmluZE9uZSkudG9IYXZlQmVlbkNhbGxlZFdpdGgoe1xyXG4gICAgICAgIHdoZXJlOiB7IGlkOiBhc3Nlc3NtZW50SWQgfSxcclxuICAgICAgICByZWxhdGlvbnM6IFsnZnJhbWV3b3JrJ10sXHJcbiAgICAgIH0pO1xyXG4gICAgICBleHBlY3QoYXNzZXNzbWVudC5zdGFydCkudG9IYXZlQmVlbkNhbGxlZFdpdGgodXNlcklkKTtcclxuICAgICAgZXhwZWN0KG1vY2tBc3Nlc3NtZW50UmVwb3NpdG9yeS5zYXZlKS50b0hhdmVCZWVuQ2FsbGVkV2l0aChhc3Nlc3NtZW50KTtcclxuICAgICAgZXhwZWN0KG1vY2tBdWRpdFNlcnZpY2UubG9nVXNlckFjdGlvbikudG9IYXZlQmVlbkNhbGxlZFdpdGgoXHJcbiAgICAgICAgdXNlcklkLFxyXG4gICAgICAgICdzdGFydCcsXHJcbiAgICAgICAgJ2NvbXBsaWFuY2VfYXNzZXNzbWVudCcsXHJcbiAgICAgICAgYXNzZXNzbWVudElkLFxyXG4gICAgICAgIGV4cGVjdC5vYmplY3RDb250YWluaW5nKHtcclxuICAgICAgICAgIGZyYW1ld29ya0lkOiBhc3Nlc3NtZW50LmZyYW1ld29ya0lkLFxyXG4gICAgICAgICAgYXNzZXNzbWVudFR5cGU6IGFzc2Vzc21lbnQuYXNzZXNzbWVudFR5cGUsXHJcbiAgICAgICAgfSksXHJcbiAgICAgICk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvRXF1YWwoYXNzZXNzbWVudCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IGVycm9yIHdoZW4gYXNzZXNzbWVudCBub3QgZm91bmQnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGFzc2Vzc21lbnRJZCA9ICdub24tZXhpc3RlbnQnO1xyXG4gICAgICBjb25zdCB1c2VySWQgPSAndXNlci0xMjMnO1xyXG5cclxuICAgICAgbW9ja0Fzc2Vzc21lbnRSZXBvc2l0b3J5LmZpbmRPbmUubW9ja1Jlc29sdmVkVmFsdWUobnVsbCk7XHJcblxyXG4gICAgICBhd2FpdCBleHBlY3Qoc2VydmljZS5zdGFydEFzc2Vzc21lbnQoYXNzZXNzbWVudElkLCB1c2VySWQpKVxyXG4gICAgICAgIC5yZWplY3RzLnRvVGhyb3coJ0Fzc2Vzc21lbnQgbm90IGZvdW5kJyk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2NvbXBsZXRlQXNzZXNzbWVudCcsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgY29tcGxldGUgYXNzZXNzbWVudCBzdWNjZXNzZnVsbHknLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGFzc2Vzc21lbnRJZCA9ICdhc3Nlc3NtZW50LTEyMyc7XHJcbiAgICAgIGNvbnN0IHVzZXJJZCA9ICd1c2VyLTEyMyc7XHJcbiAgICAgIGNvbnN0IHJlc3VsdHMgPSB7XHJcbiAgICAgICAgb3ZlcmFsbFNjb3JlOiA4NSxcclxuICAgICAgICBvdmVyYWxsU3RhdHVzOiAnY29tcGxpYW50JyxcclxuICAgICAgICBkb21haW5SZXN1bHRzOiBbXSxcclxuICAgICAgfTtcclxuICAgICAgY29uc3QgYXNzZXNzbWVudCA9IHsgLi4ubW9ja0Fzc2Vzc21lbnQsIGZyYW1ld29yazogbW9ja0ZyYW1ld29yayB9O1xyXG5cclxuICAgICAgbW9ja0Fzc2Vzc21lbnRSZXBvc2l0b3J5LmZpbmRPbmUubW9ja1Jlc29sdmVkVmFsdWUoYXNzZXNzbWVudCk7XHJcbiAgICAgIG1vY2tBc3Nlc3NtZW50UmVwb3NpdG9yeS5zYXZlLm1vY2tSZXNvbHZlZFZhbHVlKGFzc2Vzc21lbnQpO1xyXG5cclxuICAgICAgLy8gTW9jayBoZWxwZXIgbWV0aG9kc1xyXG4gICAgICBqZXN0LnNweU9uKHNlcnZpY2UgYXMgYW55LCAnZ2VuZXJhdGVWaW9sYXRpb25zRnJvbUFzc2Vzc21lbnQnKS5tb2NrUmVzb2x2ZWRWYWx1ZSh1bmRlZmluZWQpO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc2VydmljZS5jb21wbGV0ZUFzc2Vzc21lbnQoYXNzZXNzbWVudElkLCByZXN1bHRzLCB1c2VySWQpO1xyXG5cclxuICAgICAgZXhwZWN0KG1vY2tBc3Nlc3NtZW50UmVwb3NpdG9yeS5maW5kT25lKS50b0hhdmVCZWVuQ2FsbGVkV2l0aCh7XHJcbiAgICAgICAgd2hlcmU6IHsgaWQ6IGFzc2Vzc21lbnRJZCB9LFxyXG4gICAgICAgIHJlbGF0aW9uczogWydmcmFtZXdvcmsnXSxcclxuICAgICAgfSk7XHJcbiAgICAgIGV4cGVjdChhc3Nlc3NtZW50LmNvbXBsZXRlKS50b0hhdmVCZWVuQ2FsbGVkV2l0aCh1c2VySWQsIHJlc3VsdHMpO1xyXG4gICAgICBleHBlY3QobW9ja0Fzc2Vzc21lbnRSZXBvc2l0b3J5LnNhdmUpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKGFzc2Vzc21lbnQpO1xyXG4gICAgICBleHBlY3QobW9ja05vdGlmaWNhdGlvblNlcnZpY2Uuc2VuZENvbXBsaWFuY2VOb3RpZmljYXRpb24pLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKFxyXG4gICAgICAgIGFzc2Vzc21lbnQsXHJcbiAgICAgICAgJ2Fzc2Vzc21lbnRfY29tcGxldGVkJyxcclxuICAgICAgKTtcclxuICAgICAgZXhwZWN0KG1vY2tBdWRpdFNlcnZpY2UubG9nVXNlckFjdGlvbikudG9IYXZlQmVlbkNhbGxlZFdpdGgoXHJcbiAgICAgICAgdXNlcklkLFxyXG4gICAgICAgICdjb21wbGV0ZScsXHJcbiAgICAgICAgJ2NvbXBsaWFuY2VfYXNzZXNzbWVudCcsXHJcbiAgICAgICAgYXNzZXNzbWVudElkLFxyXG4gICAgICAgIGV4cGVjdC5vYmplY3RDb250YWluaW5nKHtcclxuICAgICAgICAgIGZyYW1ld29ya0lkOiBhc3Nlc3NtZW50LmZyYW1ld29ya0lkLFxyXG4gICAgICAgICAgY29tcGxpYW5jZVNjb3JlOiByZXN1bHRzLm92ZXJhbGxTY29yZSxcclxuICAgICAgICAgIGNvbXBsaWFuY2VTdGF0dXM6IHJlc3VsdHMub3ZlcmFsbFN0YXR1cyxcclxuICAgICAgICB9KSxcclxuICAgICAgKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9FcXVhbChhc3Nlc3NtZW50KTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnZ2V0RnJhbWV3b3JrQ29tcGxpYW5jZVN0YXR1cycsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgcmV0dXJuIGZyYW1ld29yayBjb21wbGlhbmNlIHN0YXR1cycsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgZnJhbWV3b3JrSWQgPSAnZnJhbWV3b3JrLTEyMyc7XHJcblxyXG4gICAgICBtb2NrRnJhbWV3b3JrUmVwb3NpdG9yeS5maW5kT25lLm1vY2tSZXNvbHZlZFZhbHVlKG1vY2tGcmFtZXdvcmspO1xyXG5cclxuICAgICAgLy8gTW9jayBoZWxwZXIgbWV0aG9kc1xyXG4gICAgICBqZXN0LnNweU9uKHNlcnZpY2UgYXMgYW55LCAnZ2V0TGF0ZXN0QXNzZXNzbWVudCcpLm1vY2tSZXNvbHZlZFZhbHVlKG1vY2tBc3Nlc3NtZW50KTtcclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlIGFzIGFueSwgJ2dldEZyYW1ld29ya1Zpb2xhdGlvbnMnKS5tb2NrUmVzb2x2ZWRWYWx1ZShbbW9ja1Zpb2xhdGlvbl0pO1xyXG4gICAgICBqZXN0LnNweU9uKHNlcnZpY2UgYXMgYW55LCAnZ2V0RnJhbWV3b3JrUG9saWNpZXMnKS5tb2NrUmVzb2x2ZWRWYWx1ZShbXSk7XHJcbiAgICAgIGplc3Quc3B5T24oc2VydmljZSBhcyBhbnksICdjYWxjdWxhdGVGcmFtZXdvcmtDb21wbGlhbmNlU2NvcmUnKS5tb2NrUmVzb2x2ZWRWYWx1ZSg4NSk7XHJcbiAgICAgIGplc3Quc3B5T24oc2VydmljZSBhcyBhbnksICdjYWxjdWxhdGVGcmFtZXdvcmtSaXNrTGV2ZWwnKS5tb2NrUmVzb2x2ZWRWYWx1ZSgnbWVkaXVtJyk7XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzZXJ2aWNlLmdldEZyYW1ld29ya0NvbXBsaWFuY2VTdGF0dXMoZnJhbWV3b3JrSWQpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ2ZyYW1ld29yaycpO1xyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0hhdmVQcm9wZXJ0eSgnbGF0ZXN0QXNzZXNzbWVudCcpO1xyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0hhdmVQcm9wZXJ0eSgndmlvbGF0aW9ucycpO1xyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0hhdmVQcm9wZXJ0eSgncG9saWNpZXMnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ2NvbXBsaWFuY2VTY29yZScpO1xyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0hhdmVQcm9wZXJ0eSgncmlza0xldmVsJyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuZnJhbWV3b3JrLmlkKS50b0JlKGZyYW1ld29ya0lkKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5jb21wbGlhbmNlU2NvcmUpLnRvQmUoODUpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LnJpc2tMZXZlbCkudG9CZSgnbWVkaXVtJyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IGVycm9yIHdoZW4gZnJhbWV3b3JrIG5vdCBmb3VuZCcsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgZnJhbWV3b3JrSWQgPSAnbm9uLWV4aXN0ZW50JztcclxuICAgICAgbW9ja0ZyYW1ld29ya1JlcG9zaXRvcnkuZmluZE9uZS5tb2NrUmVzb2x2ZWRWYWx1ZShudWxsKTtcclxuXHJcbiAgICAgIGF3YWl0IGV4cGVjdChzZXJ2aWNlLmdldEZyYW1ld29ya0NvbXBsaWFuY2VTdGF0dXMoZnJhbWV3b3JrSWQpKVxyXG4gICAgICAgIC5yZWplY3RzLnRvVGhyb3coJ0ZyYW1ld29yayBub3QgZm91bmQnKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnZ2V0Q29tcGxpYW5jZVRyZW5kcycsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgcmV0dXJuIGNvbXBsaWFuY2UgdHJlbmRzJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCBkYXlzID0gOTA7XHJcbiAgICAgIGNvbnN0IGFzc2Vzc21lbnRzID0gW21vY2tBc3Nlc3NtZW50XTtcclxuICAgICAgY29uc3QgdmlvbGF0aW9ucyA9IFttb2NrVmlvbGF0aW9uXTtcclxuXHJcbiAgICAgIG1vY2tBc3Nlc3NtZW50UmVwb3NpdG9yeS5maW5kLm1vY2tSZXNvbHZlZFZhbHVlKGFzc2Vzc21lbnRzKTtcclxuICAgICAgbW9ja1Zpb2xhdGlvblJlcG9zaXRvcnkuZmluZC5tb2NrUmVzb2x2ZWRWYWx1ZSh2aW9sYXRpb25zKTtcclxuXHJcbiAgICAgIC8vIE1vY2sgaGVscGVyIG1ldGhvZHNcclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlIGFzIGFueSwgJ2dyb3VwVHJlbmRzQnlXZWVrJykubW9ja1JldHVyblZhbHVlKFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICB3ZWVrU3RhcnQ6IG5ldyBEYXRlKCksXHJcbiAgICAgICAgICB3ZWVrRW5kOiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgICAgYXNzZXNzbWVudHM6IDEsXHJcbiAgICAgICAgICB2aW9sYXRpb25zOiAxLFxyXG4gICAgICAgICAgYXZlcmFnZUNvbXBsaWFuY2VTY29yZTogODUsXHJcbiAgICAgICAgfSxcclxuICAgICAgXSk7XHJcbiAgICAgIGplc3Quc3B5T24oc2VydmljZSBhcyBhbnksICdjYWxjdWxhdGVBdmVyYWdlQ29tcGxpYW5jZVNjb3JlJykubW9ja1JldHVyblZhbHVlKDg1KTtcclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlIGFzIGFueSwgJ2NhbGN1bGF0ZVRyZW5kRGlyZWN0aW9uJykubW9ja1JldHVyblZhbHVlKCdpbXByb3ZpbmcnKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHNlcnZpY2UuZ2V0Q29tcGxpYW5jZVRyZW5kcyhkYXlzKTtcclxuXHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdwZXJpb2QnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ3RyZW5kcycpO1xyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0hhdmVQcm9wZXJ0eSgnc3VtbWFyeScpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LnBlcmlvZC5kYXlzKS50b0JlKGRheXMpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LnN1bW1hcnkudG90YWxBc3Nlc3NtZW50cykudG9CZSgxKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5zdW1tYXJ5LnRvdGFsVmlvbGF0aW9ucykudG9CZSgxKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5zdW1tYXJ5LmF2ZXJhZ2VDb21wbGlhbmNlU2NvcmUpLnRvQmUoODUpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LnN1bW1hcnkudHJlbmREaXJlY3Rpb24pLnRvQmUoJ2ltcHJvdmluZycpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdtb25pdG9yQ29tcGxpYW5jZScsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgbW9uaXRvciBjb21wbGlhbmNlIGZvciBhbGwgYWN0aXZlIGZyYW1ld29ya3MnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGFjdGl2ZUZyYW1ld29ya3MgPSBbbW9ja0ZyYW1ld29ya107XHJcbiAgICAgIG1vY2tGcmFtZXdvcmtSZXBvc2l0b3J5LmZpbmQubW9ja1Jlc29sdmVkVmFsdWUoYWN0aXZlRnJhbWV3b3Jrcyk7XHJcblxyXG4gICAgICAvLyBNb2NrIGhlbHBlciBtZXRob2RzXHJcbiAgICAgIGplc3Quc3B5T24oc2VydmljZSwgJ21vbml0b3JGcmFtZXdvcmtDb21wbGlhbmNlJykubW9ja1Jlc29sdmVkVmFsdWUodW5kZWZpbmVkKTtcclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlIGFzIGFueSwgJ2NoZWNrT3ZlcmR1ZUFzc2Vzc21lbnRzJykubW9ja1Jlc29sdmVkVmFsdWUodW5kZWZpbmVkKTtcclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlIGFzIGFueSwgJ2NoZWNrUG9saWN5VmlvbGF0aW9ucycpLm1vY2tSZXNvbHZlZFZhbHVlKHVuZGVmaW5lZCk7XHJcblxyXG4gICAgICBhd2FpdCBzZXJ2aWNlLm1vbml0b3JDb21wbGlhbmNlKCk7XHJcblxyXG4gICAgICBleHBlY3QobW9ja0ZyYW1ld29ya1JlcG9zaXRvcnkuZmluZCkudG9IYXZlQmVlbkNhbGxlZFdpdGgoe1xyXG4gICAgICAgIHdoZXJlOiB7IGlzQWN0aXZlOiB0cnVlIH0sXHJcbiAgICAgIH0pO1xyXG4gICAgICBleHBlY3Qoc2VydmljZS5tb25pdG9yRnJhbWV3b3JrQ29tcGxpYW5jZSkudG9IYXZlQmVlbkNhbGxlZFdpdGgobW9ja0ZyYW1ld29yay5pZCk7XHJcbiAgICAgIGV4cGVjdChtb2NrTG9nZ2VyU2VydmljZS5sb2cpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKFxyXG4gICAgICAgICdDb21wbGlhbmNlIG1vbml0b3JpbmcgY3ljbGUgY29tcGxldGVkJyxcclxuICAgICAgICBleHBlY3Qub2JqZWN0Q29udGFpbmluZyh7XHJcbiAgICAgICAgICBmcmFtZXdvcmtzTW9uaXRvcmVkOiAxLFxyXG4gICAgICAgIH0pLFxyXG4gICAgICApO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBoYW5kbGUgZnJhbWV3b3JrIG1vbml0b3JpbmcgZXJyb3JzIGdyYWNlZnVsbHknLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGFjdGl2ZUZyYW1ld29ya3MgPSBbbW9ja0ZyYW1ld29ya107XHJcbiAgICAgIG1vY2tGcmFtZXdvcmtSZXBvc2l0b3J5LmZpbmQubW9ja1Jlc29sdmVkVmFsdWUoYWN0aXZlRnJhbWV3b3Jrcyk7XHJcblxyXG4gICAgICBqZXN0LnNweU9uKHNlcnZpY2UsICdtb25pdG9yRnJhbWV3b3JrQ29tcGxpYW5jZScpLm1vY2tSZWplY3RlZFZhbHVlKG5ldyBFcnJvcignTW9uaXRvcmluZyBlcnJvcicpKTtcclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlIGFzIGFueSwgJ2NoZWNrT3ZlcmR1ZUFzc2Vzc21lbnRzJykubW9ja1Jlc29sdmVkVmFsdWUodW5kZWZpbmVkKTtcclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlIGFzIGFueSwgJ2NoZWNrUG9saWN5VmlvbGF0aW9ucycpLm1vY2tSZXNvbHZlZFZhbHVlKHVuZGVmaW5lZCk7XHJcblxyXG4gICAgICBhd2FpdCBzZXJ2aWNlLm1vbml0b3JDb21wbGlhbmNlKCk7XHJcblxyXG4gICAgICBleHBlY3QobW9ja0xvZ2dlclNlcnZpY2UuZXJyb3IpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKFxyXG4gICAgICAgICdGYWlsZWQgdG8gbW9uaXRvciBmcmFtZXdvcmsgY29tcGxpYW5jZScsXHJcbiAgICAgICAgZXhwZWN0Lm9iamVjdENvbnRhaW5pbmcoe1xyXG4gICAgICAgICAgZnJhbWV3b3JrSWQ6IG1vY2tGcmFtZXdvcmsuaWQsXHJcbiAgICAgICAgICBmcmFtZXdvcmtOYW1lOiBtb2NrRnJhbWV3b3JrLm5hbWUsXHJcbiAgICAgICAgICBlcnJvcjogJ01vbml0b3JpbmcgZXJyb3InLFxyXG4gICAgICAgIH0pLFxyXG4gICAgICApO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdtb25pdG9yRnJhbWV3b3JrQ29tcGxpYW5jZScsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgbW9uaXRvciBmcmFtZXdvcmsgY29tcGxpYW5jZSBzdWNjZXNzZnVsbHknLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGZyYW1ld29ya0lkID0gJ2ZyYW1ld29yay0xMjMnO1xyXG4gICAgICBjb25zdCBmcmFtZXdvcmsgPSB7IC4uLm1vY2tGcmFtZXdvcmssIGFzc2Vzc21lbnRzOiBbXSwgcG9saWNpZXM6IFtdIH07XHJcblxyXG4gICAgICBtb2NrRnJhbWV3b3JrUmVwb3NpdG9yeS5maW5kT25lLm1vY2tSZXNvbHZlZFZhbHVlKGZyYW1ld29yayk7XHJcblxyXG4gICAgICAvLyBNb2NrIGhlbHBlciBtZXRob2RzXHJcbiAgICAgIGplc3Quc3B5T24oc2VydmljZSBhcyBhbnksICdnZXRMYXRlc3RBc3Nlc3NtZW50JykubW9ja1Jlc29sdmVkVmFsdWUobW9ja0Fzc2Vzc21lbnQpO1xyXG4gICAgICBqZXN0LnNweU9uKHNlcnZpY2UgYXMgYW55LCAnaXNBc3Nlc3NtZW50RHVlJykubW9ja1JldHVyblZhbHVlKGZhbHNlKTtcclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlIGFzIGFueSwgJ21vbml0b3JDb250aW51b3VzQ29udHJvbHMnKS5tb2NrUmVzb2x2ZWRWYWx1ZSh1bmRlZmluZWQpO1xyXG4gICAgICBqZXN0LnNweU9uKHNlcnZpY2UgYXMgYW55LCAnY2hlY2tQb2xpY3lDb21wbGlhbmNlJykubW9ja1Jlc29sdmVkVmFsdWUodW5kZWZpbmVkKTtcclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlIGFzIGFueSwgJ3VwZGF0ZUNvbXBsaWFuY2VNZXRyaWNzJykubW9ja1Jlc29sdmVkVmFsdWUodW5kZWZpbmVkKTtcclxuXHJcbiAgICAgIGF3YWl0IHNlcnZpY2UubW9uaXRvckZyYW1ld29ya0NvbXBsaWFuY2UoZnJhbWV3b3JrSWQpO1xyXG5cclxuICAgICAgZXhwZWN0KG1vY2tGcmFtZXdvcmtSZXBvc2l0b3J5LmZpbmRPbmUpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKHtcclxuICAgICAgICB3aGVyZTogeyBpZDogZnJhbWV3b3JrSWQgfSxcclxuICAgICAgICByZWxhdGlvbnM6IFsnYXNzZXNzbWVudHMnLCAncG9saWNpZXMnXSxcclxuICAgICAgfSk7XHJcbiAgICAgIGV4cGVjdChtb2NrTG9nZ2VyU2VydmljZS5kZWJ1ZykudG9IYXZlQmVlbkNhbGxlZFdpdGgoXHJcbiAgICAgICAgJ0ZyYW1ld29yayBjb21wbGlhbmNlIG1vbml0b3JpbmcgY29tcGxldGVkJyxcclxuICAgICAgICBleHBlY3Qub2JqZWN0Q29udGFpbmluZyh7XHJcbiAgICAgICAgICBmcmFtZXdvcmtJZCxcclxuICAgICAgICB9KSxcclxuICAgICAgKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgZXJyb3Igd2hlbiBmcmFtZXdvcmsgbm90IGZvdW5kJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCBmcmFtZXdvcmtJZCA9ICdub24tZXhpc3RlbnQnO1xyXG4gICAgICBtb2NrRnJhbWV3b3JrUmVwb3NpdG9yeS5maW5kT25lLm1vY2tSZXNvbHZlZFZhbHVlKG51bGwpO1xyXG5cclxuICAgICAgYXdhaXQgZXhwZWN0KHNlcnZpY2UubW9uaXRvckZyYW1ld29ya0NvbXBsaWFuY2UoZnJhbWV3b3JrSWQpKVxyXG4gICAgICAgIC5yZWplY3RzLnRvVGhyb3coJ0ZyYW1ld29yayBub3QgZm91bmQnKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG59KTtcclxuIl0sInZlcnNpb24iOjN9