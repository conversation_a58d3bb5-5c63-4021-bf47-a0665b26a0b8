262be01842ba6b459f57c216b4f2b35b
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var LoggerService_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const common_2 = require("@nestjs/common");
const winston_1 = require("winston");
const correlation_id_service_1 = require("./correlation-id.service");
/**
 * Enhanced logger service that wraps Winston logger
 * Provides structured logging with correlation ID support
 */
let LoggerService = LoggerService_1 = class LoggerService {
    constructor(logger, correlationIdService) {
        this.logger = logger;
        this.correlationIdService = correlationIdService;
    }
    /**
     * Log a message at info level
     * @param message Log message
     * @param context Optional context or metadata
     */
    log(message, context) {
        this.info(message, context);
    }
    /**
     * Log a message at error level
     * @param message Error message
     * @param trace Optional stack trace
     * @param context Optional context or metadata
     */
    error(message, trace, context) {
        const logData = this.buildLogData(message, context);
        if (trace) {
            logData.stack = trace;
        }
        this.logger.error(logData);
    }
    /**
     * Log a message at warn level
     * @param message Warning message
     * @param context Optional context or metadata
     */
    warn(message, context) {
        const logData = this.buildLogData(message, context);
        this.logger.warn(logData);
    }
    /**
     * Log a message at debug level
     * @param message Debug message
     * @param context Optional context or metadata
     */
    debug(message, context) {
        const logData = this.buildLogData(message, context);
        this.logger.debug(logData);
    }
    /**
     * Log a message at verbose level
     * @param message Verbose message
     * @param context Optional context or metadata
     */
    verbose(message, context) {
        const logData = this.buildLogData(message, context);
        this.logger.verbose(logData);
    }
    /**
     * Log a message at info level
     * @param message Info message
     * @param context Optional context or metadata
     */
    info(message, context) {
        const logData = this.buildLogData(message, context);
        this.logger.info(logData);
    }
    /**
     * Log HTTP request information
     * @param method HTTP method
     * @param url Request URL
     * @param statusCode Response status code
     * @param duration Request duration in milliseconds
     * @param metadata Additional metadata
     */
    logHttpRequest(method, url, statusCode, duration, metadata) {
        const logData = {
            message: `${method} ${url} ${statusCode} - ${duration}ms`,
            type: 'HTTP_REQUEST',
            method,
            url,
            statusCode,
            duration,
            ...metadata,
        };
        if (statusCode >= 500) {
            this.logger.error(logData);
        }
        else if (statusCode >= 400) {
            this.logger.warn(logData);
        }
        else {
            this.logger.info(logData);
        }
    }
    /**
     * Log database operation
     * @param operation Database operation type
     * @param table Table name
     * @param duration Operation duration in milliseconds
     * @param metadata Additional metadata
     */
    logDatabaseOperation(operation, table, duration, metadata) {
        const logData = {
            message: `Database ${operation} on ${table} - ${duration}ms`,
            type: 'DATABASE_OPERATION',
            operation,
            table,
            duration,
            ...metadata,
        };
        if (duration > 1000) {
            this.logger.warn(logData);
        }
        else {
            this.logger.debug(logData);
        }
    }
    /**
     * Log external service call
     * @param service Service name
     * @param operation Operation performed
     * @param duration Call duration in milliseconds
     * @param success Whether the call was successful
     * @param metadata Additional metadata
     */
    logExternalServiceCall(service, operation, duration, success, metadata) {
        const logData = {
            message: `External service call to ${service}.${operation} - ${duration}ms - ${success ? 'SUCCESS' : 'FAILED'}`,
            type: 'EXTERNAL_SERVICE_CALL',
            service,
            operation,
            duration,
            success,
            ...metadata,
        };
        if (!success) {
            this.logger.error(logData);
        }
        else if (duration > 5000) {
            this.logger.warn(logData);
        }
        else {
            this.logger.info(logData);
        }
    }
    /**
     * Log performance metrics
     * @param metric Metric name
     * @param value Metric value
     * @param unit Metric unit
     * @param metadata Additional metadata
     */
    logPerformanceMetric(metric, value, unit, metadata) {
        const logData = {
            message: `Performance metric: ${metric} = ${value}${unit}`,
            type: 'PERFORMANCE_METRIC',
            metric,
            value,
            unit,
            timestamp: new Date().toISOString(),
            ...metadata,
        };
        this.logger.info(logData);
    }
    /**
     * Log business event
     * @param event Event name
     * @param entity Entity type
     * @param entityId Entity ID
     * @param metadata Additional metadata
     */
    logBusinessEvent(event, entity, entityId, metadata) {
        const logData = {
            message: `Business event: ${event} on ${entity}:${entityId}`,
            type: 'BUSINESS_EVENT',
            event,
            entity,
            entityId,
            timestamp: new Date().toISOString(),
            ...metadata,
        };
        this.logger.info(logData);
    }
    /**
     * Create a child logger with additional context
     * @param context Context to add to all log messages
     * @returns Child logger instance
     */
    child(context) {
        const childLogger = this.logger.child(context);
        const childService = new LoggerService_1(childLogger);
        return childService;
    }
    /**
     * Build log data object from message and context
     * @param message Log message
     * @param context Context or metadata
     * @returns Log data object
     */
    buildLogData(message, context) {
        const logData = {
            message,
            timestamp: new Date().toISOString(),
        };
        if (context) {
            if (typeof context === 'string') {
                logData.context = context;
            }
            else {
                Object.assign(logData, context);
            }
        }
        // Add correlation ID if available in async context
        const correlationId = this.getCorrelationId();
        if (correlationId) {
            logData.correlationId = correlationId;
        }
        return logData;
    }
    /**
     * Get correlation ID from async context
     * @returns Correlation ID or undefined
     */
    getCorrelationId() {
        return this.correlationIdService.getCorrelationId();
    }
};
exports.LoggerService = LoggerService;
exports.LoggerService = LoggerService = LoggerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_2.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeof (_a = typeof winston_1.Logger !== "undefined" && winston_1.Logger) === "function" ? _a : Object, typeof (_b = typeof correlation_id_service_1.CorrelationIdService !== "undefined" && correlation_id_service_1.CorrelationIdService) === "function" ? _b : Object])
], LoggerService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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