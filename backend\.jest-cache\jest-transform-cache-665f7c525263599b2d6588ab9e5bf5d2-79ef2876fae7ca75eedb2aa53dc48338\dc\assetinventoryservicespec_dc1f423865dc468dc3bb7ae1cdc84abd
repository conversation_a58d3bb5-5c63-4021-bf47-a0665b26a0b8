542037ddb971c70d2f3e920d79afe76c
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const asset_inventory_service_1 = require("./asset-inventory.service");
const asset_entity_1 = require("../../domain/entities/asset.entity");
const asset_group_entity_1 = require("../../domain/entities/asset-group.entity");
const asset_configuration_entity_1 = require("../../domain/entities/asset-configuration.entity");
const asset_vulnerability_entity_1 = require("../../domain/entities/asset-vulnerability.entity");
const asset_relationship_entity_1 = require("../../domain/entities/asset-relationship.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../infrastructure/notification/notification.service");
const asset_discovery_service_1 = require("./asset-discovery.service");
const asset_classification_service_1 = require("./asset-classification.service");
describe('AssetInventoryService', () => {
    let service;
    let assetRepository;
    let assetGroupRepository;
    let configurationRepository;
    let vulnerabilityRepository;
    let relationshipRepository;
    let loggerService;
    let auditService;
    let notificationService;
    let assetDiscoveryService;
    let assetClassificationService;
    const mockAssetRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        remove: jest.fn(),
        count: jest.fn(),
        update: jest.fn(),
        createQueryBuilder: jest.fn(),
    };
    const mockAssetGroupRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
        save: jest.fn(),
    };
    const mockConfigurationRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
        save: jest.fn(),
    };
    const mockVulnerabilityRepository = {
        find: jest.fn(),
        count: jest.fn(),
        createQueryBuilder: jest.fn(),
    };
    const mockRelationshipRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
        save: jest.fn(),
    };
    const mockLoggerService = {
        debug: jest.fn(),
        log: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
    };
    const mockAuditService = {
        logUserAction: jest.fn(),
    };
    const mockNotificationService = {
        sendAssetOfflineNotification: jest.fn(),
    };
    const mockAssetDiscoveryService = {
        scheduledDiscovery: jest.fn(),
    };
    const mockAssetClassificationService = {
        classifyAsset: jest.fn(),
    };
    const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn(),
        getMany: jest.fn(),
        getCount: jest.fn(),
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn(),
        getRawOne: jest.fn(),
    };
    const mockAsset = {
        id: 'asset-123',
        name: 'Test Server',
        type: 'server',
        status: 'active',
        criticality: 'high',
        environment: 'production',
        ipAddress: '*************',
        hostname: 'test-server-01',
        location: 'Data Center 1',
        discoveredAt: new Date('2023-01-01'),
        lastSeen: new Date(),
        createdBy: 'user-123',
        tags: ['web-server', 'production'],
        isOnline: true,
        isCritical: true,
        hasAgent: true,
        ageInDays: 30,
        getSummary: jest.fn().mockReturnValue({
            id: 'asset-123',
            name: 'Test Server',
            type: 'server',
            status: 'active',
            criticality: 'high',
            isOnline: true,
            hasAgent: true,
        }),
        exportForReporting: jest.fn().mockReturnValue({
            asset: { id: 'asset-123', name: 'Test Server' },
            exportedAt: new Date().toISOString(),
        }),
    };
    const mockAssetGroup = {
        id: 'group-123',
        name: 'Production Servers',
        type: 'organizational',
        isActive: true,
        matchesAutoAssignmentRules: jest.fn().mockReturnValue(true),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                asset_inventory_service_1.AssetInventoryService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(asset_entity_1.Asset),
                    useValue: mockAssetRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(asset_group_entity_1.AssetGroup),
                    useValue: mockAssetGroupRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(asset_configuration_entity_1.AssetConfiguration),
                    useValue: mockConfigurationRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(asset_vulnerability_entity_1.AssetVulnerability),
                    useValue: mockVulnerabilityRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(asset_relationship_entity_1.AssetRelationship),
                    useValue: mockRelationshipRepository,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: mockLoggerService,
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: mockAuditService,
                },
                {
                    provide: notification_service_1.NotificationService,
                    useValue: mockNotificationService,
                },
                {
                    provide: asset_discovery_service_1.AssetDiscoveryService,
                    useValue: mockAssetDiscoveryService,
                },
                {
                    provide: asset_classification_service_1.AssetClassificationService,
                    useValue: mockAssetClassificationService,
                },
            ],
        }).compile();
        service = module.get(asset_inventory_service_1.AssetInventoryService);
        assetRepository = module.get((0, typeorm_1.getRepositoryToken)(asset_entity_1.Asset));
        assetGroupRepository = module.get((0, typeorm_1.getRepositoryToken)(asset_group_entity_1.AssetGroup));
        configurationRepository = module.get((0, typeorm_1.getRepositoryToken)(asset_configuration_entity_1.AssetConfiguration));
        vulnerabilityRepository = module.get((0, typeorm_1.getRepositoryToken)(asset_vulnerability_entity_1.AssetVulnerability));
        relationshipRepository = module.get((0, typeorm_1.getRepositoryToken)(asset_relationship_entity_1.AssetRelationship));
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
        notificationService = module.get(notification_service_1.NotificationService);
        assetDiscoveryService = module.get(asset_discovery_service_1.AssetDiscoveryService);
        assetClassificationService = module.get(asset_classification_service_1.AssetClassificationService);
        // Setup query builder mocks
        mockAssetRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
        mockVulnerabilityRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('getInventoryDashboard', () => {
        it('should return inventory dashboard data', async () => {
            // Mock repository responses
            mockAssetRepository.count.mockResolvedValue(100); // totalAssets
            mockQueryBuilder.getCount
                .mockResolvedValueOnce(80) // onlineAssets
                .mockResolvedValueOnce(60) // assetsWithAgents
                .mockResolvedValueOnce(25); // vulnerableAssets
            mockQueryBuilder.getRawMany
                .mockResolvedValueOnce([
                { type: 'server', count: '50' },
                { type: 'workstation', count: '30' },
                { type: 'network_device', count: '20' },
            ]) // assetsByType
                .mockResolvedValueOnce([
                { status: 'active', count: '80' },
                { status: 'inactive', count: '15' },
                { status: 'maintenance', count: '5' },
            ]) // assetsByStatus
                .mockResolvedValueOnce([
                { criticality: 'critical', count: '10' },
                { criticality: 'high', count: '30' },
                { criticality: 'medium', count: '40' },
                { criticality: 'low', count: '20' },
            ]) // assetsByCriticality
                .mockResolvedValueOnce([
                { environment: 'production', count: '60' },
                { environment: 'staging', count: '20' },
                { environment: 'development', count: '20' },
            ]); // assetsByEnvironment
            mockAssetRepository.find.mockResolvedValue([mockAsset]); // recentlyDiscovered
            const result = await service.getInventoryDashboard();
            expect(result).toHaveProperty('summary');
            expect(result.summary).toEqual({
                totalAssets: 100,
                onlineAssets: 80,
                offlineAssets: 20,
                assetsWithAgents: 60,
                vulnerableAssets: 25,
                agentCoverage: 60,
                vulnerabilityRate: 25,
            });
            expect(result).toHaveProperty('breakdown');
            expect(result.breakdown.byType).toEqual({
                server: 50,
                workstation: 30,
                network_device: 20,
            });
            expect(result).toHaveProperty('recentActivity');
            expect(result).toHaveProperty('timestamp');
        });
        it('should handle errors gracefully', async () => {
            mockAssetRepository.count.mockRejectedValue(new Error('Database error'));
            await expect(service.getInventoryDashboard()).rejects.toThrow('Database error');
            expect(mockLoggerService.error).toHaveBeenCalledWith('Failed to generate asset inventory dashboard', expect.objectContaining({
                error: 'Database error',
            }));
        });
    });
    describe('searchAssets', () => {
        it('should search assets with filters', async () => {
            const criteria = {
                page: 1,
                limit: 50,
                types: ['server'],
                statuses: ['active'],
                criticalities: ['high'],
                environments: ['production'],
                searchText: 'test',
            };
            const assets = [mockAsset];
            const total = 1;
            mockQueryBuilder.getManyAndCount.mockResolvedValue([assets, total]);
            const result = await service.searchAssets(criteria);
            expect(result).toEqual({
                assets,
                total,
                page: 1,
                totalPages: 1,
            });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.type IN (:...types)', { types: ['server'] });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.status IN (:...statuses)', { statuses: ['active'] });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.criticality IN (:...criticalities)', { criticalities: ['high'] });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.environment IN (:...environments)', { environments: ['production'] });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('(asset.name ILIKE :searchText OR asset.hostname ILIKE :searchText OR asset.ipAddress ILIKE :searchText OR asset.description ILIKE :searchText)', { searchText: '%test%' });
        });
        it('should handle pagination correctly', async () => {
            const criteria = { page: 2, limit: 25 };
            const assets = [mockAsset];
            const total = 50;
            mockQueryBuilder.getManyAndCount.mockResolvedValue([assets, total]);
            const result = await service.searchAssets(criteria);
            expect(result.totalPages).toBe(2);
            expect(mockQueryBuilder.skip).toHaveBeenCalledWith(25);
            expect(mockQueryBuilder.take).toHaveBeenCalledWith(25);
        });
        it('should handle online/offline filtering', async () => {
            const criteria = { isOnline: true };
            mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);
            await service.searchAssets(criteria);
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.lastSeen > :fiveMinutesAgo', expect.objectContaining({ fiveMinutesAgo: expect.any(Date) }));
        });
        it('should handle agent filtering', async () => {
            const criteria = { hasAgent: true };
            mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);
            await service.searchAssets(criteria);
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith("asset.discovery->>'agentInstalled' = 'true'");
        });
    });
    describe('createAsset', () => {
        const assetData = {
            name: 'New Server',
            type: 'server',
            criticality: 'high',
            environment: 'production',
            ipAddress: '*************',
            hostname: 'new-server-01',
            tags: ['new', 'server'],
        };
        it('should create asset successfully', async () => {
            const userId = 'user-123';
            const savedAsset = { ...mockAsset, id: 'asset-456' };
            mockAssetRepository.create.mockReturnValue(mockAsset);
            mockAssetRepository.save.mockResolvedValue(savedAsset);
            mockAssetClassificationService.classifyAsset.mockResolvedValue(undefined);
            const result = await service.createAsset(assetData, userId);
            expect(mockAssetRepository.create).toHaveBeenCalledWith({
                ...assetData,
                status: 'unknown',
                discoveredAt: expect.any(Date),
                lastSeen: expect.any(Date),
                createdBy: userId,
                ipAddresses: [assetData.ipAddress],
                tags: assetData.tags,
            });
            expect(mockAssetRepository.save).toHaveBeenCalledWith(mockAsset);
            expect(mockAssetClassificationService.classifyAsset).toHaveBeenCalledWith(savedAsset.id);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'create', 'asset', savedAsset.id, expect.objectContaining({
                assetName: assetData.name,
                assetType: assetData.type,
            }));
            expect(result).toEqual(savedAsset);
        });
        it('should validate group if specified', async () => {
            const assetDataWithGroup = { ...assetData, groupId: 'group-123' };
            const userId = 'user-123';
            mockAssetGroupRepository.findOne.mockResolvedValue(mockAssetGroup);
            mockAssetRepository.create.mockReturnValue(mockAsset);
            mockAssetRepository.save.mockResolvedValue(mockAsset);
            mockAssetClassificationService.classifyAsset.mockResolvedValue(undefined);
            await service.createAsset(assetDataWithGroup, userId);
            expect(mockAssetGroupRepository.findOne).toHaveBeenCalledWith({
                where: { id: 'group-123', isActive: true },
            });
        });
        it('should throw error when group not found', async () => {
            const assetDataWithGroup = { ...assetData, groupId: 'non-existent' };
            const userId = 'user-123';
            mockAssetGroupRepository.findOne.mockResolvedValue(null);
            await expect(service.createAsset(assetDataWithGroup, userId))
                .rejects.toThrow('Asset group not found or inactive');
        });
    });
    describe('updateAsset', () => {
        const updates = {
            name: 'Updated Server',
            criticality: 'critical',
        };
        it('should update asset successfully', async () => {
            const assetId = 'asset-123';
            const userId = 'user-123';
            const asset = { ...mockAsset };
            mockAssetRepository.findOne.mockResolvedValue(asset);
            mockAssetRepository.save.mockResolvedValue({ ...asset, ...updates });
            const result = await service.updateAsset(assetId, updates, userId);
            expect(mockAssetRepository.findOne).toHaveBeenCalledWith({
                where: { id: assetId },
                relations: ['group'],
            });
            expect(mockAssetRepository.save).toHaveBeenCalledWith({
                ...asset,
                ...updates,
                updatedBy: userId,
            });
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'update', 'asset', assetId, expect.objectContaining({
                assetName: asset.name,
                changes: expect.any(Object),
            }));
        });
        it('should throw error when asset not found', async () => {
            const assetId = 'non-existent';
            const userId = 'user-123';
            mockAssetRepository.findOne.mockResolvedValue(null);
            await expect(service.updateAsset(assetId, updates, userId))
                .rejects.toThrow('Asset not found');
        });
    });
    describe('deleteAsset', () => {
        it('should delete asset successfully', async () => {
            const assetId = 'asset-123';
            const userId = 'user-123';
            const asset = { ...mockAsset };
            mockAssetRepository.findOne.mockResolvedValue(asset);
            mockAssetRepository.remove.mockResolvedValue(asset);
            await service.deleteAsset(assetId, userId);
            expect(mockAssetRepository.findOne).toHaveBeenCalledWith({
                where: { id: assetId },
            });
            expect(mockAssetRepository.remove).toHaveBeenCalledWith(asset);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'delete', 'asset', assetId, expect.objectContaining({
                assetName: asset.name,
                assetType: asset.type,
            }));
        });
        it('should throw error when asset not found', async () => {
            const assetId = 'non-existent';
            const userId = 'user-123';
            mockAssetRepository.findOne.mockResolvedValue(null);
            await expect(service.deleteAsset(assetId, userId))
                .rejects.toThrow('Asset not found');
        });
    });
    describe('getAssetDetails', () => {
        it('should return asset with full details', async () => {
            const assetId = 'asset-123';
            const asset = { ...mockAsset };
            mockAssetRepository.findOne.mockResolvedValue(asset);
            const result = await service.getAssetDetails(assetId);
            expect(mockAssetRepository.findOne).toHaveBeenCalledWith({
                where: { id: assetId },
                relations: [
                    'group',
                    'configurations',
                    'vulnerabilities',
                    'sourceRelationships',
                    'targetRelationships',
                    'dependencies',
                    'relatedAssets',
                ],
            });
            expect(result).toEqual(asset);
        });
        it('should throw error when asset not found', async () => {
            const assetId = 'non-existent';
            mockAssetRepository.findOne.mockResolvedValue(null);
            await expect(service.getAssetDetails(assetId))
                .rejects.toThrow('Asset not found');
        });
    });
    describe('updateLastSeen', () => {
        it('should update asset last seen timestamp', async () => {
            const assetId = 'asset-123';
            mockAssetRepository.update.mockResolvedValue({ affected: 1 });
            await service.updateLastSeen(assetId);
            expect(mockAssetRepository.update).toHaveBeenCalledWith(assetId, {
                lastSeen: expect.any(Date),
            });
        });
    });
    describe('bulkUpdateStatus', () => {
        it('should update multiple asset statuses', async () => {
            const assetIds = ['asset-1', 'asset-2', 'asset-3'];
            const status = 'maintenance';
            const userId = 'user-123';
            mockAssetRepository.update.mockResolvedValue({ affected: 3 });
            await service.bulkUpdateStatus(assetIds, status, userId);
            expect(mockAssetRepository.update).toHaveBeenCalledWith({ id: expect.anything() }, { status, updatedBy: userId });
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'bulk_update', 'asset', null, expect.objectContaining({
                assetIds,
                status,
                assetCount: 3,
            }));
        });
    });
    describe('monitorInventory', () => {
        it('should complete monitoring cycle', async () => {
            // Mock helper methods
            jest.spyOn(service, 'checkOfflineAssets').mockResolvedValue(undefined);
            jest.spyOn(service, 'checkAssetsWithoutAgents').mockResolvedValue(undefined);
            jest.spyOn(service, 'checkUnclassifiedAssets').mockResolvedValue(undefined);
            jest.spyOn(service, 'updateInventoryStatistics').mockResolvedValue(undefined);
            await service.monitorInventory();
            expect(mockLoggerService.log).toHaveBeenCalledWith('Inventory monitoring completed');
        });
        it('should handle monitoring errors gracefully', async () => {
            jest.spyOn(service, 'checkOfflineAssets').mockRejectedValue(new Error('Monitoring error'));
            await service.monitorInventory();
            expect(mockLoggerService.error).toHaveBeenCalledWith('Failed to complete inventory monitoring', expect.objectContaining({
                error: 'Monitoring error',
            }));
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************