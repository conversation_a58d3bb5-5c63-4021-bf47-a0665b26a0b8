{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\__tests__\\event-timestamp.value-object.spec.ts", "mappings": ";;AAAA,kFAAiE;AAEjE,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;IAC3C,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM;YACN,MAAM,SAAS,GAAG,6CAAc,CAAC,MAAM,EAAE,CAAC;YAE1C,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,UAAU;YACV,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAE1D,MAAM;YACN,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAExD,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACnD,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,aAAa,EAAE,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,UAAU;YACV,MAAM,SAAS,GAAG,0BAA0B,CAAC;YAE7C,MAAM;YACN,MAAM,SAAS,GAAG,6CAAc,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAE1D,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,UAAU;YACV,MAAM,aAAa,GAAG,UAAU,CAAC,CAAC,2BAA2B;YAE7D,MAAM;YACN,MAAM,SAAS,GAAG,6CAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAElE,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC;YACrE,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,UAAU;YACV,MAAM,cAAc,GAAG,0BAA0B,CAAC;YAElD,MAAM;YACN,MAAM,SAAS,GAAG,6CAAc,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YAEpE,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YAC/D,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACzD,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,6CAAc,CAAC,EAAS,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,6CAAc,CAAC,EAAE,UAAU,EAAE,SAAgB,EAAE,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,6CAAc,CAAC,EAAE,UAAU,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,6CAAc,CAAC;oBACjB,UAAU,EAAE,IAAI,IAAI,EAAE;oBACtB,UAAU,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC;iBAChC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,6CAAc,CAAC;oBACjB,UAAU,EAAE,IAAI,IAAI,EAAE;oBACtB,WAAW,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC;iBACjC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,6CAAc,CAAC;oBACjB,UAAU,EAAE,IAAI,IAAI,EAAE;oBACtB,cAAc,EAAE,GAAG,EAAE,iBAAiB;iBACvC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,iEAAiE,CAAC,CAAC;YAE9E,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,6CAAc,CAAC;oBACjB,UAAU,EAAE,IAAI,IAAI,EAAE;oBACtB,cAAc,EAAE,CAAC,GAAG,EAAE,kBAAkB;iBACzC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,iEAAiE,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,uBAAuB;YAEjF,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,6CAAc,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,OAAO,CAAC,6DAA6D,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,UAAU;YACV,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,cAAc;YAErF,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,6CAAc,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,kBAAkB;YAErF,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,6CAAc,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,OAAO,CAAC,2DAA2D,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,UAAU;YACV,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YAC/B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,iBAAiB;YAEjF,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,6CAAc,CAAC;oBACjB,UAAU,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;oBACvD,UAAU;oBACV,WAAW;iBACZ,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,UAAU;YACV,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC9D,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEpD,MAAM;YACN,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YAE/B,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB;YACjE,MAAM,CAAC,GAAG,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,UAAU;YACV,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC9D,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEpD,MAAM;YACN,MAAM,YAAY,GAAG,SAAS,CAAC,eAAe,EAAE,CAAC;YAEjD,SAAS;YACT,MAAM,CAAC,YAAY,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,YAAY,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,UAAU;YACV,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB;YACvE,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEpD,MAAM;YACN,MAAM,YAAY,GAAG,SAAS,CAAC,eAAe,EAAE,CAAC;YAEjD,SAAS;YACT,MAAM,CAAC,YAAY,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,YAAY,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,UAAU;YACV,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,cAAc;YAC1E,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEpD,MAAM;YACN,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;YAE7C,SAAS;YACT,MAAM,CAAC,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB;YACzE,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAEtD,eAAe;YACf,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,UAAU;YACV,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,eAAe;YAC7E,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAErD,eAAe;YACf,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,yCAAyC;YAClG,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAEtD,eAAe;YACf,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,iBAAiB;YAC3E,MAAM,SAAS,GAAG,IAAI,6CAAc,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC;YAEjE,MAAM;YACN,MAAM,KAAK,GAAG,SAAS,CAAC,kBAAkB,EAAE,CAAC;YAE7C,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,UAAU;YACV,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;YAEtD,MAAM;YACN,MAAM,KAAK,GAAG,SAAS,CAAC,kBAAkB,EAAE,CAAC;YAE7C,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YACzD,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC;YACzD,MAAM,SAAS,GAAG,IAAI,6CAAc,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,CAAC;YAE9E,MAAM;YACN,MAAM,KAAK,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE5C,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YAC1D,MAAM,SAAS,GAAG,IAAI,6CAAc,CAAC,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,CAAC;YAElE,MAAM;YACN,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;YAExC,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,mBAAmB;YACtF,MAAM,SAAS,GAAG,IAAI,6CAAc,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC;YAEjE,eAAe;YACf,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,qBAAqB;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,mBAAmB;YAClF,MAAM,SAAS,GAAG,IAAI,6CAAc,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC;YAEjE,eAAe;YACf,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACxD,MAAM,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc;YAC3C,MAAM,SAAS,GAAG,IAAI,6CAAc,CAAC,EAAE,UAAU,EAAE,cAAc,EAAE,CAAC,CAAC;YAErE,MAAM;YACN,MAAM,GAAG,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;YAE9B,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,IAAI,6CAAc,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC;YAErD,MAAM;YACN,MAAM,GAAG,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc;YAErD,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,UAAU;YACV,MAAM,QAAQ,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;YAErD,MAAM;YACN,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC;YAE1C,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,UAAU;YACV,MAAM,QAAQ,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;YAErD,MAAM;YACN,MAAM,OAAO,GAAG,QAAQ,CAAC,eAAe,EAAE,CAAC;YAE3C,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,UAAU;YACV,MAAM,QAAQ,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;YACrD,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YAE/B,MAAM;YACN,MAAM,OAAO,GAAG,QAAQ,CAAC,wBAAwB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAE3E,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,UAAU;YACV,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAClD,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEhD,MAAM;YACN,MAAM,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YAE1C,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,UAAU;YACV,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAClD,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEhD,MAAM;YACN,MAAM,aAAa,GAAG,SAAS,CAAC,eAAe,EAAE,CAAC;YAElD,SAAS;YACT,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACxD,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,IAAI,6CAAc,CAAC;gBACnC,UAAU;gBACV,UAAU;gBACV,cAAc,EAAE,CAAC,GAAG;gBACpB,iBAAiB,EAAE,0BAA0B;gBAC7C,SAAS,EAAE,aAAa;aACzB,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YAEhC,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAChE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,UAAU;YACV,MAAM,IAAI,GAAG;gBACX,UAAU,EAAE,0BAA0B;gBACtC,UAAU,EAAE,0BAA0B;gBACtC,cAAc,EAAE,CAAC,GAAG;gBACpB,iBAAiB,EAAE,0BAA0B;gBAC7C,SAAS,EAAE,aAAa;aACzB,CAAC;YAEF,MAAM;YACN,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEhD,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACrE,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,UAAU;YACV,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAClD,MAAM,UAAU,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,UAAU,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,UAAU,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC;YAEjF,eAAe;YACf,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjD,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,UAAU;YACV,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAClD,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEhD,MAAM;YACN,MAAM,GAAG,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;YAEjC,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,UAAU;YACV,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;YAExC,eAAe;YACf,MAAM,CAAC,6CAAc,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,6CAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACxD,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACxD,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACzD,MAAM,SAAS,GAAG,IAAI,6CAAc,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,CAAC;YAE9E,MAAM;YACN,MAAM,OAAO,GAAG,SAAS,CAAC,gBAAgB,EAAE,CAAC;YAE7C,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC7D,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\__tests__\\event-timestamp.value-object.spec.ts"], "sourcesContent": ["import { EventTimestamp } from '../event-timestamp.value-object';\r\n\r\ndescribe('EventTimestamp Value Object', () => {\r\n  describe('creation', () => {\r\n    it('should create a valid event timestamp with current time', () => {\r\n      // Act\r\n      const timestamp = EventTimestamp.create();\r\n\r\n      // Assert\r\n      expect(timestamp).toBeDefined();\r\n      expect(timestamp.occurredAt).toBeInstanceOf(Date);\r\n      expect(timestamp.receivedAt).toBeInstanceOf(Date);\r\n      expect(timestamp.precision).toBe('millisecond');\r\n    });\r\n\r\n    it('should create a valid event timestamp from a specific date', () => {\r\n      // Arrange\r\n      const specificDate = new Date('2024-01-15T10:30:00.000Z');\r\n\r\n      // Act\r\n      const timestamp = EventTimestamp.fromDate(specificDate);\r\n\r\n      // Assert\r\n      expect(timestamp.occurredAt).toEqual(specificDate);\r\n      expect(timestamp.receivedAt).toBeInstanceOf(Date);\r\n      expect(timestamp.originalTimestamp).toBeUndefined();\r\n    });\r\n\r\n    it('should create a valid event timestamp from ISO string', () => {\r\n      // Arrange\r\n      const isoString = '2024-01-15T10:30:00.000Z';\r\n\r\n      // Act\r\n      const timestamp = EventTimestamp.fromISOString(isoString);\r\n\r\n      // Assert\r\n      expect(timestamp.occurredAt).toEqual(new Date(isoString));\r\n      expect(timestamp.originalTimestamp).toBe(isoString);\r\n    });\r\n\r\n    it('should create a valid event timestamp from Unix timestamp', () => {\r\n      // Arrange\r\n      const unixTimestamp = 1705316200; // 2024-01-15T10:30:00.000Z\r\n\r\n      // Act\r\n      const timestamp = EventTimestamp.fromUnixTimestamp(unixTimestamp);\r\n\r\n      // Assert\r\n      expect(timestamp.occurredAt).toEqual(new Date(unixTimestamp * 1000));\r\n      expect(timestamp.originalTimestamp).toBe(unixTimestamp.toString());\r\n    });\r\n\r\n    it('should create a valid event timestamp from original string', () => {\r\n      // Arrange\r\n      const originalString = '2024-01-15T10:30:00.000Z';\r\n\r\n      // Act\r\n      const timestamp = EventTimestamp.fromOriginalString(originalString);\r\n\r\n      // Assert\r\n      expect(timestamp.occurredAt).toEqual(new Date(originalString));\r\n      expect(timestamp.originalTimestamp).toBe(originalString);\r\n      expect(timestamp.receivedAt).toBeInstanceOf(Date);\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should throw error when occurredAt is not provided', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventTimestamp({} as any);\r\n      }).toThrow('Event timestamp must have an occurredAt date');\r\n    });\r\n\r\n    it('should throw error when occurredAt is not a Date object', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventTimestamp({ occurredAt: 'invalid' as any });\r\n      }).toThrow('occurredAt must be a valid Date object');\r\n    });\r\n\r\n    it('should throw error when occurredAt is an invalid date', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventTimestamp({ occurredAt: new Date('invalid') });\r\n      }).toThrow('occurredAt must be a valid date');\r\n    });\r\n\r\n    it('should throw error when receivedAt is invalid', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventTimestamp({\r\n          occurredAt: new Date(),\r\n          receivedAt: new Date('invalid'),\r\n        });\r\n      }).toThrow('receivedAt must be a valid Date object');\r\n    });\r\n\r\n    it('should throw error when processedAt is invalid', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventTimestamp({\r\n          occurredAt: new Date(),\r\n          processedAt: new Date('invalid'),\r\n        });\r\n      }).toThrow('processedAt must be a valid Date object');\r\n    });\r\n\r\n    it('should throw error when timezone offset is out of range', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventTimestamp({\r\n          occurredAt: new Date(),\r\n          timezoneOffset: 800, // Invalid: > 720\r\n        });\r\n      }).toThrow('Timezone offset must be an integer between -720 and 720 minutes');\r\n\r\n      expect(() => {\r\n        new EventTimestamp({\r\n          occurredAt: new Date(),\r\n          timezoneOffset: -800, // Invalid: < -720\r\n        });\r\n      }).toThrow('Timezone offset must be an integer between -720 and 720 minutes');\r\n    });\r\n\r\n    it('should throw error when timestamp is too far in the future', () => {\r\n      // Arrange\r\n      const futureDate = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes in future\r\n\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventTimestamp({ occurredAt: futureDate });\r\n      }).toThrow('Event timestamp cannot be more than 5 minutes in the future');\r\n    });\r\n\r\n    it('should throw error when timestamp is too far in the past', () => {\r\n      // Arrange\r\n      const pastDate = new Date(Date.now() - 2 * 365 * 24 * 60 * 60 * 1000); // 2 years ago\r\n\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventTimestamp({ occurredAt: pastDate });\r\n      }).toThrow('Event timestamp cannot be more than 1 year in the past');\r\n    });\r\n\r\n    it('should throw error when occurredAt is significantly after receivedAt', () => {\r\n      // Arrange\r\n      const receivedAt = new Date();\r\n      const occurredAt = new Date(receivedAt.getTime() + 2 * 60 * 1000); // 2 minutes after\r\n\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventTimestamp({ occurredAt, receivedAt });\r\n      }).toThrow('Event occurredAt cannot be significantly after receivedAt');\r\n    });\r\n\r\n    it('should throw error when receivedAt is after processedAt', () => {\r\n      // Arrange\r\n      const processedAt = new Date();\r\n      const receivedAt = new Date(processedAt.getTime() + 60 * 1000); // 1 minute after\r\n\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventTimestamp({\r\n          occurredAt: new Date(processedAt.getTime() - 60 * 1000),\r\n          receivedAt,\r\n          processedAt,\r\n        });\r\n      }).toThrow('Event receivedAt cannot be after processedAt');\r\n    });\r\n  });\r\n\r\n  describe('age calculations', () => {\r\n    it('should calculate event age correctly', () => {\r\n      // Arrange\r\n      const pastDate = new Date(Date.now() - 5000); // 5 seconds ago\r\n      const timestamp = EventTimestamp.fromDate(pastDate);\r\n\r\n      // Act\r\n      const age = timestamp.getAge();\r\n\r\n      // Assert\r\n      expect(age).toBeGreaterThanOrEqual(4900); // Allow some tolerance\r\n      expect(age).toBeLessThanOrEqual(5100);\r\n    });\r\n\r\n    it('should calculate age in seconds correctly', () => {\r\n      // Arrange\r\n      const pastDate = new Date(Date.now() - 5000); // 5 seconds ago\r\n      const timestamp = EventTimestamp.fromDate(pastDate);\r\n\r\n      // Act\r\n      const ageInSeconds = timestamp.getAgeInSeconds();\r\n\r\n      // Assert\r\n      expect(ageInSeconds).toBeGreaterThanOrEqual(4);\r\n      expect(ageInSeconds).toBeLessThanOrEqual(6);\r\n    });\r\n\r\n    it('should calculate age in minutes correctly', () => {\r\n      // Arrange\r\n      const pastDate = new Date(Date.now() - 3 * 60 * 1000); // 3 minutes ago\r\n      const timestamp = EventTimestamp.fromDate(pastDate);\r\n\r\n      // Act\r\n      const ageInMinutes = timestamp.getAgeInMinutes();\r\n\r\n      // Assert\r\n      expect(ageInMinutes).toBeGreaterThanOrEqual(2);\r\n      expect(ageInMinutes).toBeLessThanOrEqual(4);\r\n    });\r\n\r\n    it('should calculate age in hours correctly', () => {\r\n      // Arrange\r\n      const pastDate = new Date(Date.now() - 2 * 60 * 60 * 1000); // 2 hours ago\r\n      const timestamp = EventTimestamp.fromDate(pastDate);\r\n\r\n      // Act\r\n      const ageInHours = timestamp.getAgeInHours();\r\n\r\n      // Assert\r\n      expect(ageInHours).toBeGreaterThanOrEqual(1);\r\n      expect(ageInHours).toBeLessThanOrEqual(3);\r\n    });\r\n  });\r\n\r\n  describe('freshness checks', () => {\r\n    it('should identify recent events correctly', () => {\r\n      // Arrange\r\n      const recentDate = new Date(Date.now() - 2 * 60 * 1000); // 2 minutes ago\r\n      const timestamp = EventTimestamp.fromDate(recentDate);\r\n\r\n      // Act & Assert\r\n      expect(timestamp.isRecent()).toBe(true);\r\n      expect(timestamp.isRecent(60000)).toBe(false); // Within 1 minute\r\n    });\r\n\r\n    it('should identify stale events correctly', () => {\r\n      // Arrange\r\n      const staleDate = new Date(Date.now() - 25 * 60 * 60 * 1000); // 25 hours ago\r\n      const timestamp = EventTimestamp.fromDate(staleDate);\r\n\r\n      // Act & Assert\r\n      expect(timestamp.isStale()).toBe(true);\r\n      expect(timestamp.isStale(48 * 60 * 60 * 1000)).toBe(false); // Within 48 hours\r\n    });\r\n\r\n    it('should identify future events correctly', () => {\r\n      // Arrange\r\n      const futureDate = new Date(Date.now() + 2 * 60 * 1000); // 2 minutes in future (within tolerance)\r\n      const timestamp = EventTimestamp.fromDate(futureDate);\r\n\r\n      // Act & Assert\r\n      expect(timestamp.isInFuture()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('delay calculations', () => {\r\n    it('should calculate processing delay correctly', () => {\r\n      // Arrange\r\n      const occurredAt = new Date();\r\n      const receivedAt = new Date(occurredAt.getTime() + 1000); // 1 second later\r\n      const timestamp = new EventTimestamp({ occurredAt, receivedAt });\r\n\r\n      // Act\r\n      const delay = timestamp.getProcessingDelay();\r\n\r\n      // Assert\r\n      expect(delay).toBe(1000);\r\n    });\r\n\r\n    it('should return null for processing delay when receivedAt is not set', () => {\r\n      // Arrange\r\n      const timestamp = EventTimestamp.fromDate(new Date());\r\n\r\n      // Act\r\n      const delay = timestamp.getProcessingDelay();\r\n\r\n      // Assert\r\n      expect(delay).toBeNull();\r\n    });\r\n\r\n    it('should calculate ingestion delay correctly', () => {\r\n      // Arrange\r\n      const occurredAt = new Date();\r\n      const receivedAt = new Date(occurredAt.getTime() + 1000);\r\n      const processedAt = new Date(receivedAt.getTime() + 500);\r\n      const timestamp = new EventTimestamp({ occurredAt, receivedAt, processedAt });\r\n\r\n      // Act\r\n      const delay = timestamp.getIngestionDelay();\r\n\r\n      // Assert\r\n      expect(delay).toBe(500);\r\n    });\r\n\r\n    it('should calculate total delay correctly', () => {\r\n      // Arrange\r\n      const occurredAt = new Date();\r\n      const processedAt = new Date(occurredAt.getTime() + 1500);\r\n      const timestamp = new EventTimestamp({ occurredAt, processedAt });\r\n\r\n      // Act\r\n      const delay = timestamp.getTotalDelay();\r\n\r\n      // Assert\r\n      expect(delay).toBe(1500);\r\n    });\r\n  });\r\n\r\n  describe('clock skew detection', () => {\r\n    it('should detect clock skew correctly', () => {\r\n      // Arrange\r\n      const occurredAt = new Date();\r\n      const receivedAt = new Date(occurredAt.getTime() - 2 * 60 * 1000); // 2 minutes before\r\n      const timestamp = new EventTimestamp({ occurredAt, receivedAt });\r\n\r\n      // Act & Assert\r\n      expect(timestamp.hasClockSkew()).toBe(true);\r\n      expect(timestamp.hasClockSkew(3 * 60 * 1000)).toBe(false); // 3 minute tolerance\r\n    });\r\n\r\n    it('should not detect clock skew for normal delays', () => {\r\n      // Arrange\r\n      const occurredAt = new Date();\r\n      const receivedAt = new Date(occurredAt.getTime() + 30 * 1000); // 30 seconds later\r\n      const timestamp = new EventTimestamp({ occurredAt, receivedAt });\r\n\r\n      // Act & Assert\r\n      expect(timestamp.hasClockSkew()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('timezone handling', () => {\r\n    it('should convert to UTC correctly', () => {\r\n      // Arrange\r\n      const occurredAt = new Date('2024-01-15T10:30:00.000Z');\r\n      const timezoneOffset = -300; // EST (UTC-5)\r\n      const timestamp = new EventTimestamp({ occurredAt, timezoneOffset });\r\n\r\n      // Act\r\n      const utc = timestamp.toUTC();\r\n\r\n      // Assert\r\n      expect(utc.getTime()).toBe(occurredAt.getTime() + (timezoneOffset * 60000));\r\n    });\r\n\r\n    it('should convert to specific timezone correctly', () => {\r\n      // Arrange\r\n      const occurredAt = new Date('2024-01-15T10:30:00.000Z');\r\n      const timestamp = new EventTimestamp({ occurredAt });\r\n\r\n      // Act\r\n      const pst = timestamp.toTimezone(480); // PST (UTC-8)\r\n\r\n      // Assert\r\n      expect(pst.getTime()).toBe(occurredAt.getTime() - (480 * 60000));\r\n    });\r\n  });\r\n\r\n  describe('immutability and updates', () => {\r\n    it('should create new instance when marking as received', () => {\r\n      // Arrange\r\n      const original = EventTimestamp.fromDate(new Date());\r\n\r\n      // Act\r\n      const updated = original.markAsReceived();\r\n\r\n      // Assert\r\n      expect(updated).not.toBe(original);\r\n      expect(updated.receivedAt).toBeInstanceOf(Date);\r\n      expect(updated.occurredAt).toEqual(original.occurredAt);\r\n    });\r\n\r\n    it('should create new instance when marking as processed', () => {\r\n      // Arrange\r\n      const original = EventTimestamp.fromDate(new Date());\r\n\r\n      // Act\r\n      const updated = original.markAsProcessed();\r\n\r\n      // Assert\r\n      expect(updated).not.toBe(original);\r\n      expect(updated.processedAt).toBeInstanceOf(Date);\r\n      expect(updated.occurredAt).toEqual(original.occurredAt);\r\n    });\r\n\r\n    it('should create new instance with processing timestamps', () => {\r\n      // Arrange\r\n      const original = EventTimestamp.fromDate(new Date());\r\n      const receivedAt = new Date();\r\n      const processedAt = new Date();\r\n\r\n      // Act\r\n      const updated = original.withProcessingTimestamps(receivedAt, processedAt);\r\n\r\n      // Assert\r\n      expect(updated).not.toBe(original);\r\n      expect(updated.receivedAt).toEqual(receivedAt);\r\n      expect(updated.processedAt).toEqual(processedAt);\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    it('should convert to ISO string correctly', () => {\r\n      // Arrange\r\n      const date = new Date('2024-01-15T10:30:00.000Z');\r\n      const timestamp = EventTimestamp.fromDate(date);\r\n\r\n      // Act\r\n      const isoString = timestamp.toISOString();\r\n\r\n      // Assert\r\n      expect(isoString).toBe('2024-01-15T10:30:00.000Z');\r\n    });\r\n\r\n    it('should convert to Unix timestamp correctly', () => {\r\n      // Arrange\r\n      const date = new Date('2024-01-15T10:30:00.000Z');\r\n      const timestamp = EventTimestamp.fromDate(date);\r\n\r\n      // Act\r\n      const unixTimestamp = timestamp.toUnixTimestamp();\r\n\r\n      // Assert\r\n      expect(unixTimestamp).toBe(Math.floor(date.getTime() / 1000));\r\n    });\r\n\r\n    it('should convert to JSON correctly', () => {\r\n      // Arrange\r\n      const occurredAt = new Date('2024-01-15T10:30:00.000Z');\r\n      const receivedAt = new Date('2024-01-15T10:30:01.000Z');\r\n      const timestamp = new EventTimestamp({\r\n        occurredAt,\r\n        receivedAt,\r\n        timezoneOffset: -300,\r\n        originalTimestamp: '2024-01-15T10:30:00.000Z',\r\n        precision: 'millisecond',\r\n      });\r\n\r\n      // Act\r\n      const json = timestamp.toJSON();\r\n\r\n      // Assert\r\n      expect(json.occurredAt).toBe('2024-01-15T10:30:00.000Z');\r\n      expect(json.receivedAt).toBe('2024-01-15T10:30:01.000Z');\r\n      expect(json.timezoneOffset).toBe(-300);\r\n      expect(json.originalTimestamp).toBe('2024-01-15T10:30:00.000Z');\r\n      expect(json.precision).toBe('millisecond');\r\n      expect(json.summary).toBeDefined();\r\n    });\r\n\r\n    it('should create from JSON correctly', () => {\r\n      // Arrange\r\n      const json = {\r\n        occurredAt: '2024-01-15T10:30:00.000Z',\r\n        receivedAt: '2024-01-15T10:30:01.000Z',\r\n        timezoneOffset: -300,\r\n        originalTimestamp: '2024-01-15T10:30:00.000Z',\r\n        precision: 'millisecond',\r\n      };\r\n\r\n      // Act\r\n      const timestamp = EventTimestamp.fromJSON(json);\r\n\r\n      // Assert\r\n      expect(timestamp.occurredAt).toEqual(new Date('2024-01-15T10:30:00.000Z'));\r\n      expect(timestamp.receivedAt).toEqual(new Date('2024-01-15T10:30:01.000Z'));\r\n      expect(timestamp.timezoneOffset).toBe(-300);\r\n      expect(timestamp.originalTimestamp).toBe('2024-01-15T10:30:00.000Z');\r\n      expect(timestamp.precision).toBe('millisecond');\r\n    });\r\n  });\r\n\r\n  describe('equality and comparison', () => {\r\n    it('should compare timestamps for equality correctly', () => {\r\n      // Arrange\r\n      const date = new Date('2024-01-15T10:30:00.000Z');\r\n      const timestamp1 = EventTimestamp.fromDate(date);\r\n      const timestamp2 = EventTimestamp.fromDate(date);\r\n      const timestamp3 = EventTimestamp.fromDate(new Date('2024-01-15T10:31:00.000Z'));\r\n\r\n      // Act & Assert\r\n      expect(timestamp1.equals(timestamp2)).toBe(true);\r\n      expect(timestamp1.equals(timestamp3)).toBe(false);\r\n      expect(timestamp1.equals(undefined)).toBe(false);\r\n      expect(timestamp1.equals(timestamp1)).toBe(true);\r\n    });\r\n\r\n    it('should convert to string correctly', () => {\r\n      // Arrange\r\n      const date = new Date('2024-01-15T10:30:00.000Z');\r\n      const timestamp = EventTimestamp.fromDate(date);\r\n\r\n      // Act\r\n      const str = timestamp.toString();\r\n\r\n      // Assert\r\n      expect(str).toBe('2024-01-15T10:30:00.000Z');\r\n    });\r\n  });\r\n\r\n  describe('validation utility', () => {\r\n    it('should validate timestamp without creating instance', () => {\r\n      // Arrange\r\n      const validDate = new Date();\r\n      const invalidDate = new Date('invalid');\r\n\r\n      // Act & Assert\r\n      expect(EventTimestamp.isValid(validDate)).toBe(true);\r\n      expect(EventTimestamp.isValid(invalidDate)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('timing summary', () => {\r\n    it('should provide comprehensive timing summary', () => {\r\n      // Arrange\r\n      const occurredAt = new Date('2024-01-15T10:30:00.000Z');\r\n      const receivedAt = new Date('2024-01-15T10:30:01.000Z');\r\n      const processedAt = new Date('2024-01-15T10:30:02.000Z');\r\n      const timestamp = new EventTimestamp({ occurredAt, receivedAt, processedAt });\r\n\r\n      // Act\r\n      const summary = timestamp.getTimingSummary();\r\n\r\n      // Assert\r\n      expect(summary.occurredAt).toBe('2024-01-15T10:30:00.000Z');\r\n      expect(summary.receivedAt).toBe('2024-01-15T10:30:01.000Z');\r\n      expect(summary.processedAt).toBe('2024-01-15T10:30:02.000Z');\r\n      expect(summary.processingDelay).toBe(1000);\r\n      expect(summary.ingestionDelay).toBe(1000);\r\n      expect(summary.totalDelay).toBe(2000);\r\n      expect(summary.hasClockSkew).toBe(false);\r\n      expect(typeof summary.age).toBe('number');\r\n      expect(typeof summary.isRecent).toBe('boolean');\r\n      expect(typeof summary.isStale).toBe('boolean');\r\n    });\r\n  });\r\n});"], "version": 3}