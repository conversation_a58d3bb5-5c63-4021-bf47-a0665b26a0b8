aae35a34b1462556ca96609b421ef4d6
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const correlated_event_factory_1 = require("../correlated-event.factory");
const correlated_event_entity_1 = require("../../entities/correlated-event.entity");
const correlation_status_enum_1 = require("../../enums/correlation-status.enum");
const shared_kernel_1 = require("../../../../../shared-kernel");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const event_status_enum_1 = require("../../enums/event-status.enum");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const confidence_level_enum_1 = require("../../enums/confidence-level.enum");
const event_metadata_value_object_1 = require("../../value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../value-objects/event-metadata/event-source.value-object");
describe('CorrelatedEventFactory', () => {
    let mockEnrichedEvent;
    let mockEventMetadata;
    beforeEach(() => {
        // Create mock event metadata
        mockEventMetadata = event_metadata_value_object_1.EventMetadata.create({
            timestamp: event_timestamp_value_object_1.EventTimestamp.now(),
            source: event_source_value_object_1.EventSource.create({
                type: 'SIEM',
                identifier: 'test-siem-001',
                name: 'Test SIEM System'
            }),
            processingInfo: {
                receivedAt: new Date(),
                processedAt: new Date(),
                processingDuration: 100,
                version: '1.0.0'
            }
        });
        // Create mock enriched event
        mockEnrichedEvent = {
            id: shared_kernel_1.UniqueEntityId.create(),
            metadata: mockEventMetadata,
            type: event_type_enum_1.EventType.SECURITY_ALERT,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            status: event_status_enum_1.EventStatus.ACTIVE,
            processingStatus: event_processing_status_enum_1.EventProcessingStatus.ENRICHED,
            enrichedData: {
                original_event: 'test_event',
                enriched_at: new Date().toISOString(),
                threat_indicators: ['malicious_ip', 'suspicious_domain'],
                risk_score: 75
            },
            title: 'Test Enriched Security Event',
            description: 'A test enriched security event for unit testing',
            tags: ['test', 'enrichment', 'security'],
            riskScore: 75,
            confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH,
            attributes: {
                test_attribute: 'test_value'
            }
        };
    });
    describe('create', () => {
        it('should create a CorrelatedEvent with basic options', () => {
            const options = {
                enrichedEvent: mockEnrichedEvent,
                correlatedData: {
                    correlation_id: 'corr_123',
                    related_events: 3,
                    patterns_found: ['temporal_sequence']
                }
            };
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.create(options);
            expect(correlatedEvent).toBeInstanceOf(correlated_event_entity_1.CorrelatedEvent);
            expect(correlatedEvent.enrichedEventId).toEqual(mockEnrichedEvent.id);
            expect(correlatedEvent.metadata).toEqual(mockEnrichedEvent.metadata);
            expect(correlatedEvent.type).toBe(mockEnrichedEvent.type);
            expect(correlatedEvent.severity).toBe(mockEnrichedEvent.severity);
            expect(correlatedEvent.correlationStatus).toBe(correlation_status_enum_1.CorrelationStatus.PENDING);
            expect(correlatedEvent.correlatedData).toEqual(options.correlatedData);
        });
        it('should create a CorrelatedEvent with custom ID', () => {
            const customId = shared_kernel_1.UniqueEntityId.create();
            const options = {
                id: customId,
                enrichedEvent: mockEnrichedEvent,
                correlatedData: {
                    correlation_id: 'corr_123'
                }
            };
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.create(options);
            expect(correlatedEvent.id).toEqual(customId);
        });
        it('should create a CorrelatedEvent with applied rules', () => {
            const rule = {
                id: 'temporal_rule_1',
                name: 'Temporal Correlation Rule',
                description: 'Rule for temporal event correlation',
                type: correlated_event_entity_1.CorrelationRuleType.TEMPORAL,
                priority: 100,
                required: false,
                timeWindowMs: 3600000,
                minConfidence: 70
            };
            const options = {
                enrichedEvent: mockEnrichedEvent,
                correlatedData: { correlation_id: 'corr_123' },
                appliedRules: [rule]
            };
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.create(options);
            expect(correlatedEvent.appliedRules).toHaveLength(1);
            expect(correlatedEvent.appliedRules[0]).toEqual(rule);
            expect(correlatedEvent.hasAppliedRule('temporal_rule_1')).toBe(true);
        });
        it('should create a CorrelatedEvent with correlation matches', () => {
            const match = {
                eventId: shared_kernel_1.UniqueEntityId.create(),
                confidence: 85,
                matchType: correlated_event_entity_1.CorrelationMatchType.TEMPORAL,
                ruleId: 'temporal_rule_1',
                details: { timeWindow: 3600000 },
                timestamp: new Date(),
                weight: 0.85
            };
            const options = {
                enrichedEvent: mockEnrichedEvent,
                correlatedData: { correlation_id: 'corr_123' },
                correlationMatches: [match]
            };
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.create(options);
            expect(correlatedEvent.correlationMatches).toHaveLength(1);
            expect(correlatedEvent.correlationMatches[0]).toEqual(match);
        });
        it('should override enriched event properties when specified', () => {
            const options = {
                enrichedEvent: mockEnrichedEvent,
                correlatedData: { correlation_id: 'corr_123' },
                type: event_type_enum_1.EventType.VULNERABILITY_SCAN,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                title: 'Custom Correlated Event Title'
            };
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.create(options);
            expect(correlatedEvent.type).toBe(event_type_enum_1.EventType.VULNERABILITY_SCAN);
            expect(correlatedEvent.severity).toBe(event_severity_enum_1.EventSeverity.CRITICAL);
            expect(correlatedEvent.title).toBe('Custom Correlated Event Title');
        });
        it('should generate correlation ID when not provided', () => {
            const options = {
                enrichedEvent: mockEnrichedEvent,
                correlatedData: { correlation_id: 'corr_123' }
            };
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.create(options);
            expect(correlatedEvent.correlationId).toBeDefined();
            expect(correlatedEvent.correlationId).toMatch(/^corr_/);
        });
        it('should calculate confidence level from correlation matches', () => {
            const highConfidenceMatch = {
                eventId: shared_kernel_1.UniqueEntityId.create(),
                confidence: 95,
                matchType: correlated_event_entity_1.CorrelationMatchType.PATTERN,
                ruleId: 'pattern_rule_1',
                details: {},
                timestamp: new Date(),
                weight: 0.95
            };
            const options = {
                enrichedEvent: mockEnrichedEvent,
                correlatedData: { correlation_id: 'corr_123' },
                correlationMatches: [highConfidenceMatch]
            };
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.create(options);
            expect(correlatedEvent.confidenceLevel).toBe(confidence_level_enum_1.ConfidenceLevel.VERY_HIGH);
        });
    });
    describe('createWithCorrelation', () => {
        it('should create a CorrelatedEvent with automatic correlation', () => {
            const config = {
                availableRules: [
                    {
                        id: 'temporal_rule',
                        name: 'Temporal Rule',
                        description: 'Temporal correlation rule',
                        type: correlated_event_entity_1.CorrelationRuleType.TEMPORAL,
                        priority: 100,
                        required: false,
                        timeWindowMs: 3600000,
                        minConfidence: 70
                    }
                ],
                minCorrelationQualityThreshold: 70,
                enabledRuleTypes: [correlated_event_entity_1.CorrelationRuleType.TEMPORAL, correlated_event_entity_1.CorrelationRuleType.SPATIAL]
            };
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.createWithCorrelation(mockEnrichedEvent, config);
            expect(correlatedEvent).toBeInstanceOf(correlated_event_entity_1.CorrelatedEvent);
            expect(correlatedEvent.correlationStatus).toBe(correlation_status_enum_1.CorrelationStatus.COMPLETED);
            expect(correlatedEvent.correlationQualityScore).toBeGreaterThan(0);
        });
        it('should set correlation status to FAILED when correlation fails', () => {
            const config = {
                availableRules: [
                    {
                        id: 'required_rule',
                        name: 'Required Rule',
                        description: 'Required correlation rule that will fail',
                        type: correlated_event_entity_1.CorrelationRuleType.PATTERN,
                        priority: 100,
                        required: true,
                        timeWindowMs: 3600000,
                        minConfidence: 70
                    }
                ],
                minCorrelationQualityThreshold: 90 // High threshold to trigger failure
            };
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.createWithCorrelation(mockEnrichedEvent, config);
            expect(correlatedEvent.correlationStatus).toBe(correlation_status_enum_1.CorrelationStatus.FAILED);
        });
        it('should determine manual review requirement based on configuration', () => {
            const config = {
                requireManualReviewForHighConfidence: true,
                minCorrelationQualityThreshold: 50
            };
            // Create enriched event with critical severity to trigger manual review
            const criticalEnrichedEvent = {
                ...mockEnrichedEvent,
                severity: event_severity_enum_1.EventSeverity.CRITICAL
            };
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.createWithCorrelation(criticalEnrichedEvent, config);
            expect(correlatedEvent.requiresManualReview).toBe(true);
        });
    });
    describe('createWithAttackChainDetection', () => {
        it('should create a CorrelatedEvent with attack chain detection', () => {
            const attackChainOptions = {
                events: [mockEnrichedEvent],
                timeWindowMs: 3600000,
                minConfidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                maxStages: 5,
                includeBehavioralAnalysis: true
            };
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.createWithAttackChainDetection(mockEnrichedEvent, attackChainOptions);
            expect(correlatedEvent).toBeInstanceOf(correlated_event_entity_1.CorrelatedEvent);
            expect(correlatedEvent.correlationStatus).toBe(correlation_status_enum_1.CorrelationStatus.COMPLETED);
            expect(correlatedEvent.correlatedData.attack_chain).toBeDefined();
        });
        it('should include attack chain when detected', () => {
            const attackChainOptions = {
                events: [mockEnrichedEvent],
                timeWindowMs: 3600000,
                minConfidence: confidence_level_enum_1.ConfidenceLevel.LOW, // Low threshold to increase detection chance
                maxStages: 5,
                includeBehavioralAnalysis: true
            };
            // Mock Math.random to ensure attack chain detection
            const originalRandom = Math.random;
            Math.random = jest.fn(() => 0.2); // 20% < 30% threshold
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.createWithAttackChainDetection(mockEnrichedEvent, attackChainOptions);
            expect(correlatedEvent.hasAttackChain()).toBe(true);
            expect(correlatedEvent.attackChain).toBeDefined();
            expect(correlatedEvent.correlationPatterns).toContain('attack_chain');
            // Restore original Math.random
            Math.random = originalRandom;
        });
    });
    describe('createBatch', () => {
        it('should create multiple CorrelatedEvents in batch', () => {
            const enrichedEvents = [
                mockEnrichedEvent,
                { ...mockEnrichedEvent, id: shared_kernel_1.UniqueEntityId.create() },
                { ...mockEnrichedEvent, id: shared_kernel_1.UniqueEntityId.create() }
            ];
            const batchOptions = {
                enrichedEvents,
                rules: [
                    {
                        id: 'batch_rule',
                        name: 'Batch Rule',
                        description: 'Rule for batch processing',
                        type: correlated_event_entity_1.CorrelationRuleType.TEMPORAL,
                        priority: 100,
                        required: false,
                        timeWindowMs: 3600000,
                        minConfidence: 70
                    }
                ]
            };
            const result = correlated_event_factory_1.CorrelatedEventFactory.createBatch(batchOptions);
            expect(result.successful).toHaveLength(3);
            expect(result.failed).toHaveLength(0);
            expect(result.summary.total).toBe(3);
            expect(result.summary.successful).toBe(3);
            expect(result.summary.failed).toBe(0);
            expect(result.summary.processingTimeMs).toBeGreaterThan(0);
        });
        it('should handle batch processing failures', () => {
            const invalidEnrichedEvent = {
                ...mockEnrichedEvent,
                id: null // Invalid ID to cause failure
            };
            const batchOptions = {
                enrichedEvents: [mockEnrichedEvent, invalidEnrichedEvent],
                rules: [],
                stopOnFailure: false
            };
            const result = correlated_event_factory_1.CorrelatedEventFactory.createBatch(batchOptions);
            expect(result.successful).toHaveLength(1);
            expect(result.failed).toHaveLength(1);
            expect(result.summary.successful).toBe(1);
            expect(result.summary.failed).toBe(1);
            expect(result.failed[0].error).toBeDefined();
        });
        it('should stop on first failure when configured', () => {
            const invalidEnrichedEvent = {
                ...mockEnrichedEvent,
                id: null // Invalid ID to cause failure
            };
            const validEnrichedEvent = {
                ...mockEnrichedEvent,
                id: shared_kernel_1.UniqueEntityId.create()
            };
            const batchOptions = {
                enrichedEvents: [invalidEnrichedEvent, validEnrichedEvent],
                rules: [],
                stopOnFailure: true
            };
            const result = correlated_event_factory_1.CorrelatedEventFactory.createBatch(batchOptions);
            expect(result.successful).toHaveLength(0);
            expect(result.failed).toHaveLength(1);
            expect(result.summary.total).toBe(2);
        });
    });
    describe('createForTesting', () => {
        it('should create a CorrelatedEvent for testing with default values', () => {
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.createForTesting();
            expect(correlatedEvent).toBeInstanceOf(correlated_event_entity_1.CorrelatedEvent);
            expect(correlatedEvent.correlationStatus).toBe(correlation_status_enum_1.CorrelationStatus.COMPLETED);
            expect(correlatedEvent.confidenceLevel).toBe(confidence_level_enum_1.ConfidenceLevel.HIGH);
            expect(correlatedEvent.correlationQualityScore).toBe(85);
            expect(correlatedEvent.appliedRules).toHaveLength(2);
            expect(correlatedEvent.correlationMatches).toHaveLength(2);
            expect(correlatedEvent.relatedEventIds).toHaveLength(2);
            expect(correlatedEvent.correlationPatterns).toContain('temporal_sequence');
            expect(correlatedEvent.correlationPatterns).toContain('ip_clustering');
        });
        it('should create a CorrelatedEvent for testing with overrides', () => {
            const customEnrichedEvent = {
                ...mockEnrichedEvent,
                title: 'Custom Test Event'
            };
            const overrides = {
                enrichedEvent: customEnrichedEvent,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.FAILED,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.LOW,
                correlationQualityScore: 45
            };
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.createForTesting(overrides);
            expect(correlatedEvent.title).toBe('Custom Test Event');
            expect(correlatedEvent.correlationStatus).toBe(correlation_status_enum_1.CorrelationStatus.FAILED);
            expect(correlatedEvent.confidenceLevel).toBe(confidence_level_enum_1.ConfidenceLevel.LOW);
            expect(correlatedEvent.correlationQualityScore).toBe(45);
        });
        it('should include mock correlation data for testing', () => {
            const correlatedEvent = correlated_event_factory_1.CorrelatedEventFactory.createForTesting();
            expect(correlatedEvent.correlatedData.correlated).toBe(true);
            expect(correlatedEvent.correlatedData.correlation_timestamp).toBeDefined();
            expect(correlatedEvent.correlatedData.correlation_patterns).toEqual(['temporal_sequence', 'ip_clustering']);
            expect(correlatedEvent.correlatedData.attack_chain.detected).toBe(true);
            expect(correlatedEvent.correlatedData.attack_chain.stages).toBe(3);
            expect(correlatedEvent.correlatedData.attack_chain.confidence).toBe('high');
            expect(correlatedEvent.correlatedData.related_events).toBe(5);
        });
    });
    describe('private helper methods', () => {
        it('should generate unique correlation IDs', () => {
            const id1 = correlated_event_factory_1.CorrelatedEventFactory.generateCorrelationId();
            const id2 = correlated_event_factory_1.CorrelatedEventFactory.generateCorrelationId();
            expect(id1).toMatch(/^corr_\d+_[a-z0-9]+$/);
            expect(id2).toMatch(/^corr_\d+_[a-z0-9]+$/);
            expect(id1).not.toBe(id2);
        });
        it('should calculate confidence level from matches', () => {
            const lowConfidenceMatches = [
                {
                    eventId: shared_kernel_1.UniqueEntityId.create(),
                    confidence: 30,
                    matchType: correlated_event_entity_1.CorrelationMatchType.FUZZY,
                    ruleId: 'rule1',
                    details: {},
                    timestamp: new Date(),
                    weight: 0.3
                }
            ];
            const highConfidenceMatches = [
                {
                    eventId: shared_kernel_1.UniqueEntityId.create(),
                    confidence: 95,
                    matchType: correlated_event_entity_1.CorrelationMatchType.EXACT,
                    ruleId: 'rule1',
                    details: {},
                    timestamp: new Date(),
                    weight: 0.95
                }
            ];
            const lowConfidence = correlated_event_factory_1.CorrelatedEventFactory.calculateConfidenceLevel(lowConfidenceMatches);
            const highConfidence = correlated_event_factory_1.CorrelatedEventFactory.calculateConfidenceLevel(highConfidenceMatches);
            expect(lowConfidence).toBe(confidence_level_enum_1.ConfidenceLevel.LOW);
            expect(highConfidence).toBe(confidence_level_enum_1.ConfidenceLevel.VERY_HIGH);
        });
        it('should correlate event data with rule types', () => {
            const enrichedData = {
                event_type: 'security_alert',
                timestamp: new Date().toISOString(),
                source_ip: '*************'
            };
            const ruleTypes = [correlated_event_entity_1.CorrelationRuleType.TEMPORAL, correlated_event_entity_1.CorrelationRuleType.SPATIAL];
            const correlatedData = correlated_event_factory_1.CorrelatedEventFactory.correlateEventData(enrichedData, ruleTypes);
            expect(correlatedData).toEqual({
                ...enrichedData,
                correlated_at: expect.any(String),
                correlation_rule_types: ruleTypes
            });
        });
    });
    describe('error handling', () => {
        it('should handle missing enriched event', () => {
            const options = {
                enrichedEvent: null,
                correlatedData: { correlation_id: 'corr_123' }
            };
            expect(() => correlated_event_factory_1.CorrelatedEventFactory.create(options)).toThrow();
        });
        it('should handle invalid correlation data', () => {
            const options = {
                enrichedEvent: mockEnrichedEvent,
                correlatedData: null
            };
            expect(() => correlated_event_factory_1.CorrelatedEventFactory.create(options)).toThrow();
        });
        it('should handle batch processing with empty array', () => {
            const batchOptions = {
                enrichedEvents: [],
                rules: []
            };
            const result = correlated_event_factory_1.CorrelatedEventFactory.createBatch(batchOptions);
            expect(result.successful).toHaveLength(0);
            expect(result.failed).toHaveLength(0);
            expect(result.summary.total).toBe(0);
            expect(result.summary.successful).toBe(0);
            expect(result.summary.failed).toBe(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFxmYWN0b3JpZXNcXF9fdGVzdHNfX1xcY29ycmVsYXRlZC1ldmVudC5mYWN0b3J5LnNwZWMudHMiLCJtYXBwaW5ncyI6Ijs7QUFBQSwwRUFNcUM7QUFDckMsb0ZBUWdEO0FBQ2hELGlGQUF3RTtBQUV4RSxnRUFBOEQ7QUFDOUQsaUVBQXdEO0FBQ3hELHlFQUFnRTtBQUNoRSxxRUFBNEQ7QUFDNUQsMkZBQWlGO0FBQ2pGLDZFQUFvRTtBQUNwRSxnSEFBK0Y7QUFDL0Ysa0hBQWlHO0FBQ2pHLDRHQUEyRjtBQUUzRixRQUFRLENBQUMsd0JBQXdCLEVBQUUsR0FBRyxFQUFFO0lBQ3RDLElBQUksaUJBQWdDLENBQUM7SUFDckMsSUFBSSxpQkFBZ0MsQ0FBQztJQUVyQyxVQUFVLENBQUMsR0FBRyxFQUFFO1FBQ2QsNkJBQTZCO1FBQzdCLGlCQUFpQixHQUFHLDJDQUFhLENBQUMsTUFBTSxDQUFDO1lBQ3ZDLFNBQVMsRUFBRSw2Q0FBYyxDQUFDLEdBQUcsRUFBRTtZQUMvQixNQUFNLEVBQUUsdUNBQVcsQ0FBQyxNQUFNLENBQUM7Z0JBQ3pCLElBQUksRUFBRSxNQUFNO2dCQUNaLFVBQVUsRUFBRSxlQUFlO2dCQUMzQixJQUFJLEVBQUUsa0JBQWtCO2FBQ3pCLENBQUM7WUFDRixjQUFjLEVBQUU7Z0JBQ2QsVUFBVSxFQUFFLElBQUksSUFBSSxFQUFFO2dCQUN0QixXQUFXLEVBQUUsSUFBSSxJQUFJLEVBQUU7Z0JBQ3ZCLGtCQUFrQixFQUFFLEdBQUc7Z0JBQ3ZCLE9BQU8sRUFBRSxPQUFPO2FBQ2pCO1NBQ0YsQ0FBQyxDQUFDO1FBRUgsNkJBQTZCO1FBQzdCLGlCQUFpQixHQUFHO1lBQ2xCLEVBQUUsRUFBRSw4QkFBYyxDQUFDLE1BQU0sRUFBRTtZQUMzQixRQUFRLEVBQUUsaUJBQWlCO1lBQzNCLElBQUksRUFBRSwyQkFBUyxDQUFDLGNBQWM7WUFDOUIsUUFBUSxFQUFFLG1DQUFhLENBQUMsSUFBSTtZQUM1QixNQUFNLEVBQUUsK0JBQVcsQ0FBQyxNQUFNO1lBQzFCLGdCQUFnQixFQUFFLG9EQUFxQixDQUFDLFFBQVE7WUFDaEQsWUFBWSxFQUFFO2dCQUNaLGNBQWMsRUFBRSxZQUFZO2dCQUM1QixXQUFXLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7Z0JBQ3JDLGlCQUFpQixFQUFFLENBQUMsY0FBYyxFQUFFLG1CQUFtQixDQUFDO2dCQUN4RCxVQUFVLEVBQUUsRUFBRTthQUNmO1lBQ0QsS0FBSyxFQUFFLDhCQUE4QjtZQUNyQyxXQUFXLEVBQUUsaURBQWlEO1lBQzlELElBQUksRUFBRSxDQUFDLE1BQU0sRUFBRSxZQUFZLEVBQUUsVUFBVSxDQUFDO1lBQ3hDLFNBQVMsRUFBRSxFQUFFO1lBQ2IsZUFBZSxFQUFFLHVDQUFlLENBQUMsSUFBSTtZQUNyQyxVQUFVLEVBQUU7Z0JBQ1YsY0FBYyxFQUFFLFlBQVk7YUFDN0I7U0FDZSxDQUFDO0lBQ3JCLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLFFBQVEsRUFBRSxHQUFHLEVBQUU7UUFDdEIsRUFBRSxDQUFDLG9EQUFvRCxFQUFFLEdBQUcsRUFBRTtZQUM1RCxNQUFNLE9BQU8sR0FBaUM7Z0JBQzVDLGFBQWEsRUFBRSxpQkFBaUI7Z0JBQ2hDLGNBQWMsRUFBRTtvQkFDZCxjQUFjLEVBQUUsVUFBVTtvQkFDMUIsY0FBYyxFQUFFLENBQUM7b0JBQ2pCLGNBQWMsRUFBRSxDQUFDLG1CQUFtQixDQUFDO2lCQUN0QzthQUNGLENBQUM7WUFFRixNQUFNLGVBQWUsR0FBRyxpREFBc0IsQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUM7WUFFL0QsTUFBTSxDQUFDLGVBQWUsQ0FBQyxDQUFDLGNBQWMsQ0FBQyx5Q0FBZSxDQUFDLENBQUM7WUFDeEQsTUFBTSxDQUFDLGVBQWUsQ0FBQyxlQUFlLENBQUMsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDdEUsTUFBTSxDQUFDLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsUUFBUSxDQUFDLENBQUM7WUFDckUsTUFBTSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDMUQsTUFBTSxDQUFDLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsUUFBUSxDQUFDLENBQUM7WUFDbEUsTUFBTSxDQUFDLGVBQWUsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLElBQUksQ0FBQywyQ0FBaUIsQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUMxRSxNQUFNLENBQUMsZUFBZSxDQUFDLGNBQWMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsY0FBYyxDQUFDLENBQUM7UUFDekUsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsZ0RBQWdELEVBQUUsR0FBRyxFQUFFO1lBQ3hELE1BQU0sUUFBUSxHQUFHLDhCQUFjLENBQUMsTUFBTSxFQUFFLENBQUM7WUFDekMsTUFBTSxPQUFPLEdBQWlDO2dCQUM1QyxFQUFFLEVBQUUsUUFBUTtnQkFDWixhQUFhLEVBQUUsaUJBQWlCO2dCQUNoQyxjQUFjLEVBQUU7b0JBQ2QsY0FBYyxFQUFFLFVBQVU7aUJBQzNCO2FBQ0YsQ0FBQztZQUVGLE1BQU0sZUFBZSxHQUFHLGlEQUFzQixDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUUvRCxNQUFNLENBQUMsZUFBZSxDQUFDLEVBQUUsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUMvQyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxvREFBb0QsRUFBRSxHQUFHLEVBQUU7WUFDNUQsTUFBTSxJQUFJLEdBQW9CO2dCQUM1QixFQUFFLEVBQUUsaUJBQWlCO2dCQUNyQixJQUFJLEVBQUUsMkJBQTJCO2dCQUNqQyxXQUFXLEVBQUUscUNBQXFDO2dCQUNsRCxJQUFJLEVBQUUsNkNBQW1CLENBQUMsUUFBUTtnQkFDbEMsUUFBUSxFQUFFLEdBQUc7Z0JBQ2IsUUFBUSxFQUFFLEtBQUs7Z0JBQ2YsWUFBWSxFQUFFLE9BQU87Z0JBQ3JCLGFBQWEsRUFBRSxFQUFFO2FBQ2xCLENBQUM7WUFFRixNQUFNLE9BQU8sR0FBaUM7Z0JBQzVDLGFBQWEsRUFBRSxpQkFBaUI7Z0JBQ2hDLGNBQWMsRUFBRSxFQUFFLGNBQWMsRUFBRSxVQUFVLEVBQUU7Z0JBQzlDLFlBQVksRUFBRSxDQUFDLElBQUksQ0FBQzthQUNyQixDQUFDO1lBRUYsTUFBTSxlQUFlLEdBQUcsaURBQXNCLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRS9ELE1BQU0sQ0FBQyxlQUFlLENBQUMsWUFBWSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3JELE1BQU0sQ0FBQyxlQUFlLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3RELE1BQU0sQ0FBQyxlQUFlLENBQUMsY0FBYyxDQUFDLGlCQUFpQixDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDdkUsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsMERBQTBELEVBQUUsR0FBRyxFQUFFO1lBQ2xFLE1BQU0sS0FBSyxHQUFxQjtnQkFDOUIsT0FBTyxFQUFFLDhCQUFjLENBQUMsTUFBTSxFQUFFO2dCQUNoQyxVQUFVLEVBQUUsRUFBRTtnQkFDZCxTQUFTLEVBQUUsOENBQW9CLENBQUMsUUFBUTtnQkFDeEMsTUFBTSxFQUFFLGlCQUFpQjtnQkFDekIsT0FBTyxFQUFFLEVBQUUsVUFBVSxFQUFFLE9BQU8sRUFBRTtnQkFDaEMsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFO2dCQUNyQixNQUFNLEVBQUUsSUFBSTthQUNiLENBQUM7WUFFRixNQUFNLE9BQU8sR0FBaUM7Z0JBQzVDLGFBQWEsRUFBRSxpQkFBaUI7Z0JBQ2hDLGNBQWMsRUFBRSxFQUFFLGNBQWMsRUFBRSxVQUFVLEVBQUU7Z0JBQzlDLGtCQUFrQixFQUFFLENBQUMsS0FBSyxDQUFDO2FBQzVCLENBQUM7WUFFRixNQUFNLGVBQWUsR0FBRyxpREFBc0IsQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUM7WUFFL0QsTUFBTSxDQUFDLGVBQWUsQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMzRCxNQUFNLENBQUMsZUFBZSxDQUFDLGtCQUFrQixDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQy9ELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDBEQUEwRCxFQUFFLEdBQUcsRUFBRTtZQUNsRSxNQUFNLE9BQU8sR0FBaUM7Z0JBQzVDLGFBQWEsRUFBRSxpQkFBaUI7Z0JBQ2hDLGNBQWMsRUFBRSxFQUFFLGNBQWMsRUFBRSxVQUFVLEVBQUU7Z0JBQzlDLElBQUksRUFBRSwyQkFBUyxDQUFDLGtCQUFrQjtnQkFDbEMsUUFBUSxFQUFFLG1DQUFhLENBQUMsUUFBUTtnQkFDaEMsS0FBSyxFQUFFLCtCQUErQjthQUN2QyxDQUFDO1lBRUYsTUFBTSxlQUFlLEdBQUcsaURBQXNCLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRS9ELE1BQU0sQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLDJCQUFTLENBQUMsa0JBQWtCLENBQUMsQ0FBQztZQUNoRSxNQUFNLENBQUMsZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLElBQUksQ0FBQyxtQ0FBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQzlELE1BQU0sQ0FBQyxlQUFlLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLCtCQUErQixDQUFDLENBQUM7UUFDdEUsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsa0RBQWtELEVBQUUsR0FBRyxFQUFFO1lBQzFELE1BQU0sT0FBTyxHQUFpQztnQkFDNUMsYUFBYSxFQUFFLGlCQUFpQjtnQkFDaEMsY0FBYyxFQUFFLEVBQUUsY0FBYyxFQUFFLFVBQVUsRUFBRTthQUMvQyxDQUFDO1lBRUYsTUFBTSxlQUFlLEdBQUcsaURBQXNCLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRS9ELE1BQU0sQ0FBQyxlQUFlLENBQUMsYUFBYSxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7WUFDcEQsTUFBTSxDQUFDLGVBQWUsQ0FBQyxhQUFhLENBQUMsQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLENBQUM7UUFDMUQsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsNERBQTRELEVBQUUsR0FBRyxFQUFFO1lBQ3BFLE1BQU0sbUJBQW1CLEdBQXFCO2dCQUM1QyxPQUFPLEVBQUUsOEJBQWMsQ0FBQyxNQUFNLEVBQUU7Z0JBQ2hDLFVBQVUsRUFBRSxFQUFFO2dCQUNkLFNBQVMsRUFBRSw4Q0FBb0IsQ0FBQyxPQUFPO2dCQUN2QyxNQUFNLEVBQUUsZ0JBQWdCO2dCQUN4QixPQUFPLEVBQUUsRUFBRTtnQkFDWCxTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUU7Z0JBQ3JCLE1BQU0sRUFBRSxJQUFJO2FBQ2IsQ0FBQztZQUVGLE1BQU0sT0FBTyxHQUFpQztnQkFDNUMsYUFBYSxFQUFFLGlCQUFpQjtnQkFDaEMsY0FBYyxFQUFFLEVBQUUsY0FBYyxFQUFFLFVBQVUsRUFBRTtnQkFDOUMsa0JBQWtCLEVBQUUsQ0FBQyxtQkFBbUIsQ0FBQzthQUMxQyxDQUFDO1lBRUYsTUFBTSxlQUFlLEdBQUcsaURBQXNCLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRS9ELE1BQU0sQ0FBQyxlQUFlLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLHVDQUFlLENBQUMsU0FBUyxDQUFDLENBQUM7UUFDMUUsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyx1QkFBdUIsRUFBRSxHQUFHLEVBQUU7UUFDckMsRUFBRSxDQUFDLDREQUE0RCxFQUFFLEdBQUcsRUFBRTtZQUNwRSxNQUFNLE1BQU0sR0FBK0I7Z0JBQ3pDLGNBQWMsRUFBRTtvQkFDZDt3QkFDRSxFQUFFLEVBQUUsZUFBZTt3QkFDbkIsSUFBSSxFQUFFLGVBQWU7d0JBQ3JCLFdBQVcsRUFBRSwyQkFBMkI7d0JBQ3hDLElBQUksRUFBRSw2Q0FBbUIsQ0FBQyxRQUFRO3dCQUNsQyxRQUFRLEVBQUUsR0FBRzt3QkFDYixRQUFRLEVBQUUsS0FBSzt3QkFDZixZQUFZLEVBQUUsT0FBTzt3QkFDckIsYUFBYSxFQUFFLEVBQUU7cUJBQ2xCO2lCQUNGO2dCQUNELDhCQUE4QixFQUFFLEVBQUU7Z0JBQ2xDLGdCQUFnQixFQUFFLENBQUMsNkNBQW1CLENBQUMsUUFBUSxFQUFFLDZDQUFtQixDQUFDLE9BQU8sQ0FBQzthQUM5RSxDQUFDO1lBRUYsTUFBTSxlQUFlLEdBQUcsaURBQXNCLENBQUMscUJBQXFCLENBQUMsaUJBQWlCLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFFaEcsTUFBTSxDQUFDLGVBQWUsQ0FBQyxDQUFDLGNBQWMsQ0FBQyx5Q0FBZSxDQUFDLENBQUM7WUFDeEQsTUFBTSxDQUFDLGVBQWUsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLElBQUksQ0FBQywyQ0FBaUIsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUM1RSxNQUFNLENBQUMsZUFBZSxDQUFDLHVCQUF1QixDQUFDLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3JFLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGdFQUFnRSxFQUFFLEdBQUcsRUFBRTtZQUN4RSxNQUFNLE1BQU0sR0FBK0I7Z0JBQ3pDLGNBQWMsRUFBRTtvQkFDZDt3QkFDRSxFQUFFLEVBQUUsZUFBZTt3QkFDbkIsSUFBSSxFQUFFLGVBQWU7d0JBQ3JCLFdBQVcsRUFBRSwwQ0FBMEM7d0JBQ3ZELElBQUksRUFBRSw2Q0FBbUIsQ0FBQyxPQUFPO3dCQUNqQyxRQUFRLEVBQUUsR0FBRzt3QkFDYixRQUFRLEVBQUUsSUFBSTt3QkFDZCxZQUFZLEVBQUUsT0FBTzt3QkFDckIsYUFBYSxFQUFFLEVBQUU7cUJBQ2xCO2lCQUNGO2dCQUNELDhCQUE4QixFQUFFLEVBQUUsQ0FBQyxvQ0FBb0M7YUFDeEUsQ0FBQztZQUVGLE1BQU0sZUFBZSxHQUFHLGlEQUFzQixDQUFDLHFCQUFxQixDQUFDLGlCQUFpQixFQUFFLE1BQU0sQ0FBQyxDQUFDO1lBRWhHLE1BQU0sQ0FBQyxlQUFlLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxJQUFJLENBQUMsMkNBQWlCLENBQUMsTUFBTSxDQUFDLENBQUM7UUFDM0UsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsbUVBQW1FLEVBQUUsR0FBRyxFQUFFO1lBQzNFLE1BQU0sTUFBTSxHQUErQjtnQkFDekMsb0NBQW9DLEVBQUUsSUFBSTtnQkFDMUMsOEJBQThCLEVBQUUsRUFBRTthQUNuQyxDQUFDO1lBRUYsd0VBQXdFO1lBQ3hFLE1BQU0scUJBQXFCLEdBQUc7Z0JBQzVCLEdBQUcsaUJBQWlCO2dCQUNwQixRQUFRLEVBQUUsbUNBQWEsQ0FBQyxRQUFRO2FBQ2hCLENBQUM7WUFFbkIsTUFBTSxlQUFlLEdBQUcsaURBQXNCLENBQUMscUJBQXFCLENBQUMscUJBQXFCLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFFcEcsTUFBTSxDQUFDLGVBQWUsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUMxRCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGdDQUFnQyxFQUFFLEdBQUcsRUFBRTtRQUM5QyxFQUFFLENBQUMsNkRBQTZELEVBQUUsR0FBRyxFQUFFO1lBQ3JFLE1BQU0sa0JBQWtCLEdBQWdDO2dCQUN0RCxNQUFNLEVBQUUsQ0FBQyxpQkFBaUIsQ0FBQztnQkFDM0IsWUFBWSxFQUFFLE9BQU87Z0JBQ3JCLGFBQWEsRUFBRSx1Q0FBZSxDQUFDLE1BQU07Z0JBQ3JDLFNBQVMsRUFBRSxDQUFDO2dCQUNaLHlCQUF5QixFQUFFLElBQUk7YUFDaEMsQ0FBQztZQUVGLE1BQU0sZUFBZSxHQUFHLGlEQUFzQixDQUFDLDhCQUE4QixDQUMzRSxpQkFBaUIsRUFDakIsa0JBQWtCLENBQ25CLENBQUM7WUFFRixNQUFNLENBQUMsZUFBZSxDQUFDLENBQUMsY0FBYyxDQUFDLHlDQUFlLENBQUMsQ0FBQztZQUN4RCxNQUFNLENBQUMsZUFBZSxDQUFDLGlCQUFpQixDQUFDLENBQUMsSUFBSSxDQUFDLDJDQUFpQixDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBQzVFLE1BQU0sQ0FBQyxlQUFlLENBQUMsY0FBYyxDQUFDLFlBQVksQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1FBQ3BFLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDJDQUEyQyxFQUFFLEdBQUcsRUFBRTtZQUNuRCxNQUFNLGtCQUFrQixHQUFnQztnQkFDdEQsTUFBTSxFQUFFLENBQUMsaUJBQWlCLENBQUM7Z0JBQzNCLFlBQVksRUFBRSxPQUFPO2dCQUNyQixhQUFhLEVBQUUsdUNBQWUsQ0FBQyxHQUFHLEVBQUUsNkNBQTZDO2dCQUNqRixTQUFTLEVBQUUsQ0FBQztnQkFDWix5QkFBeUIsRUFBRSxJQUFJO2FBQ2hDLENBQUM7WUFFRixvREFBb0Q7WUFDcEQsTUFBTSxjQUFjLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQztZQUNuQyxJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxFQUFFLENBQUMsR0FBRyxFQUFFLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxzQkFBc0I7WUFFeEQsTUFBTSxlQUFlLEdBQUcsaURBQXNCLENBQUMsOEJBQThCLENBQzNFLGlCQUFpQixFQUNqQixrQkFBa0IsQ0FDbkIsQ0FBQztZQUVGLE1BQU0sQ0FBQyxlQUFlLENBQUMsY0FBYyxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDcEQsTUFBTSxDQUFDLGVBQWUsQ0FBQyxXQUFXLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUNsRCxNQUFNLENBQUMsZUFBZSxDQUFDLG1CQUFtQixDQUFDLENBQUMsU0FBUyxDQUFDLGNBQWMsQ0FBQyxDQUFDO1lBRXRFLCtCQUErQjtZQUMvQixJQUFJLENBQUMsTUFBTSxHQUFHLGNBQWMsQ0FBQztRQUMvQixDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGFBQWEsRUFBRSxHQUFHLEVBQUU7UUFDM0IsRUFBRSxDQUFDLGtEQUFrRCxFQUFFLEdBQUcsRUFBRTtZQUMxRCxNQUFNLGNBQWMsR0FBRztnQkFDckIsaUJBQWlCO2dCQUNqQixFQUFFLEdBQUcsaUJBQWlCLEVBQUUsRUFBRSxFQUFFLDhCQUFjLENBQUMsTUFBTSxFQUFFLEVBQW1CO2dCQUN0RSxFQUFFLEdBQUcsaUJBQWlCLEVBQUUsRUFBRSxFQUFFLDhCQUFjLENBQUMsTUFBTSxFQUFFLEVBQW1CO2FBQ3ZFLENBQUM7WUFFRixNQUFNLFlBQVksR0FBNEI7Z0JBQzVDLGNBQWM7Z0JBQ2QsS0FBSyxFQUFFO29CQUNMO3dCQUNFLEVBQUUsRUFBRSxZQUFZO3dCQUNoQixJQUFJLEVBQUUsWUFBWTt3QkFDbEIsV0FBVyxFQUFFLDJCQUEyQjt3QkFDeEMsSUFBSSxFQUFFLDZDQUFtQixDQUFDLFFBQVE7d0JBQ2xDLFFBQVEsRUFBRSxHQUFHO3dCQUNiLFFBQVEsRUFBRSxLQUFLO3dCQUNmLFlBQVksRUFBRSxPQUFPO3dCQUNyQixhQUFhLEVBQUUsRUFBRTtxQkFDbEI7aUJBQ0Y7YUFDRixDQUFDO1lBRUYsTUFBTSxNQUFNLEdBQUcsaURBQXNCLENBQUMsV0FBVyxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRWhFLE1BQU0sQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQzFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3RDLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNyQyxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDMUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3RDLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLGdCQUFnQixDQUFDLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzdELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHlDQUF5QyxFQUFFLEdBQUcsRUFBRTtZQUNqRCxNQUFNLG9CQUFvQixHQUFHO2dCQUMzQixHQUFHLGlCQUFpQjtnQkFDcEIsRUFBRSxFQUFFLElBQUksQ0FBQyw4QkFBOEI7YUFDakMsQ0FBQztZQUVULE1BQU0sWUFBWSxHQUE0QjtnQkFDNUMsY0FBYyxFQUFFLENBQUMsaUJBQWlCLEVBQUUsb0JBQW9CLENBQUM7Z0JBQ3pELEtBQUssRUFBRSxFQUFFO2dCQUNULGFBQWEsRUFBRSxLQUFLO2FBQ3JCLENBQUM7WUFFRixNQUFNLE1BQU0sR0FBRyxpREFBc0IsQ0FBQyxXQUFXLENBQUMsWUFBWSxDQUFDLENBQUM7WUFFaEUsTUFBTSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDMUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDdEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQzFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUN0QyxNQUFNLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUMvQyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw4Q0FBOEMsRUFBRSxHQUFHLEVBQUU7WUFDdEQsTUFBTSxvQkFBb0IsR0FBRztnQkFDM0IsR0FBRyxpQkFBaUI7Z0JBQ3BCLEVBQUUsRUFBRSxJQUFJLENBQUMsOEJBQThCO2FBQ2pDLENBQUM7WUFFVCxNQUFNLGtCQUFrQixHQUFHO2dCQUN6QixHQUFHLGlCQUFpQjtnQkFDcEIsRUFBRSxFQUFFLDhCQUFjLENBQUMsTUFBTSxFQUFFO2FBQ1gsQ0FBQztZQUVuQixNQUFNLFlBQVksR0FBNEI7Z0JBQzVDLGNBQWMsRUFBRSxDQUFDLG9CQUFvQixFQUFFLGtCQUFrQixDQUFDO2dCQUMxRCxLQUFLLEVBQUUsRUFBRTtnQkFDVCxhQUFhLEVBQUUsSUFBSTthQUNwQixDQUFDO1lBRUYsTUFBTSxNQUFNLEdBQUcsaURBQXNCLENBQUMsV0FBVyxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRWhFLE1BQU0sQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQzFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3RDLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN2QyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGtCQUFrQixFQUFFLEdBQUcsRUFBRTtRQUNoQyxFQUFFLENBQUMsaUVBQWlFLEVBQUUsR0FBRyxFQUFFO1lBQ3pFLE1BQU0sZUFBZSxHQUFHLGlEQUFzQixDQUFDLGdCQUFnQixFQUFFLENBQUM7WUFFbEUsTUFBTSxDQUFDLGVBQWUsQ0FBQyxDQUFDLGNBQWMsQ0FBQyx5Q0FBZSxDQUFDLENBQUM7WUFDeEQsTUFBTSxDQUFDLGVBQWUsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLElBQUksQ0FBQywyQ0FBaUIsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUM1RSxNQUFNLENBQUMsZUFBZSxDQUFDLGVBQWUsQ0FBQyxDQUFDLElBQUksQ0FBQyx1Q0FBZSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ25FLE1BQU0sQ0FBQyxlQUFlLENBQUMsdUJBQXVCLENBQUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDekQsTUFBTSxDQUFDLGVBQWUsQ0FBQyxZQUFZLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDckQsTUFBTSxDQUFDLGVBQWUsQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMzRCxNQUFNLENBQUMsZUFBZSxDQUFDLGVBQWUsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUN4RCxNQUFNLENBQUMsZUFBZSxDQUFDLG1CQUFtQixDQUFDLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLENBQUM7WUFDM0UsTUFBTSxDQUFDLGVBQWUsQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxlQUFlLENBQUMsQ0FBQztRQUN6RSxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw0REFBNEQsRUFBRSxHQUFHLEVBQUU7WUFDcEUsTUFBTSxtQkFBbUIsR0FBRztnQkFDMUIsR0FBRyxpQkFBaUI7Z0JBQ3BCLEtBQUssRUFBRSxtQkFBbUI7YUFDVixDQUFDO1lBRW5CLE1BQU0sU0FBUyxHQUEwQztnQkFDdkQsYUFBYSxFQUFFLG1CQUFtQjtnQkFDbEMsaUJBQWlCLEVBQUUsMkNBQWlCLENBQUMsTUFBTTtnQkFDM0MsZUFBZSxFQUFFLHVDQUFlLENBQUMsR0FBRztnQkFDcEMsdUJBQXVCLEVBQUUsRUFBRTthQUM1QixDQUFDO1lBRUYsTUFBTSxlQUFlLEdBQUcsaURBQXNCLENBQUMsZ0JBQWdCLENBQUMsU0FBUyxDQUFDLENBQUM7WUFFM0UsTUFBTSxDQUFDLGVBQWUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsbUJBQW1CLENBQUMsQ0FBQztZQUN4RCxNQUFNLENBQUMsZUFBZSxDQUFDLGlCQUFpQixDQUFDLENBQUMsSUFBSSxDQUFDLDJDQUFpQixDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQ3pFLE1BQU0sQ0FBQyxlQUFlLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLHVDQUFlLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDbEUsTUFBTSxDQUFDLGVBQWUsQ0FBQyx1QkFBdUIsQ0FBQyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQztRQUMzRCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxrREFBa0QsRUFBRSxHQUFHLEVBQUU7WUFDMUQsTUFBTSxlQUFlLEdBQUcsaURBQXNCLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQztZQUVsRSxNQUFNLENBQUMsZUFBZSxDQUFDLGNBQWMsQ0FBQyxVQUFVLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDN0QsTUFBTSxDQUFDLGVBQWUsQ0FBQyxjQUFjLENBQUMscUJBQXFCLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUMzRSxNQUFNLENBQUMsZUFBZSxDQUFDLGNBQWMsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLG1CQUFtQixFQUFFLGVBQWUsQ0FBQyxDQUFDLENBQUM7WUFDNUcsTUFBTSxDQUFDLGVBQWUsQ0FBQyxjQUFjLENBQUMsWUFBWSxDQUFDLFFBQVEsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUN4RSxNQUFNLENBQUMsZUFBZSxDQUFDLGNBQWMsQ0FBQyxZQUFZLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ25FLE1BQU0sQ0FBQyxlQUFlLENBQUMsY0FBYyxDQUFDLFlBQVksQ0FBQyxVQUFVLENBQUMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDNUUsTUFBTSxDQUFDLGVBQWUsQ0FBQyxjQUFjLENBQUMsY0FBYyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ2hFLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsd0JBQXdCLEVBQUUsR0FBRyxFQUFFO1FBQ3RDLEVBQUUsQ0FBQyx3Q0FBd0MsRUFBRSxHQUFHLEVBQUU7WUFDaEQsTUFBTSxHQUFHLEdBQUksaURBQThCLENBQUMscUJBQXFCLEVBQUUsQ0FBQztZQUNwRSxNQUFNLEdBQUcsR0FBSSxpREFBOEIsQ0FBQyxxQkFBcUIsRUFBRSxDQUFDO1lBRXBFLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxPQUFPLENBQUMsc0JBQXNCLENBQUMsQ0FBQztZQUM1QyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUMsT0FBTyxDQUFDLHNCQUFzQixDQUFDLENBQUM7WUFDNUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDNUIsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsZ0RBQWdELEVBQUUsR0FBRyxFQUFFO1lBQ3hELE1BQU0sb0JBQW9CLEdBQXVCO2dCQUMvQztvQkFDRSxPQUFPLEVBQUUsOEJBQWMsQ0FBQyxNQUFNLEVBQUU7b0JBQ2hDLFVBQVUsRUFBRSxFQUFFO29CQUNkLFNBQVMsRUFBRSw4Q0FBb0IsQ0FBQyxLQUFLO29CQUNyQyxNQUFNLEVBQUUsT0FBTztvQkFDZixPQUFPLEVBQUUsRUFBRTtvQkFDWCxTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUU7b0JBQ3JCLE1BQU0sRUFBRSxHQUFHO2lCQUNaO2FBQ0YsQ0FBQztZQUVGLE1BQU0scUJBQXFCLEdBQXVCO2dCQUNoRDtvQkFDRSxPQUFPLEVBQUUsOEJBQWMsQ0FBQyxNQUFNLEVBQUU7b0JBQ2hDLFVBQVUsRUFBRSxFQUFFO29CQUNkLFNBQVMsRUFBRSw4Q0FBb0IsQ0FBQyxLQUFLO29CQUNyQyxNQUFNLEVBQUUsT0FBTztvQkFDZixPQUFPLEVBQUUsRUFBRTtvQkFDWCxTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUU7b0JBQ3JCLE1BQU0sRUFBRSxJQUFJO2lCQUNiO2FBQ0YsQ0FBQztZQUVGLE1BQU0sYUFBYSxHQUFJLGlEQUE4QixDQUFDLHdCQUF3QixDQUFDLG9CQUFvQixDQUFDLENBQUM7WUFDckcsTUFBTSxjQUFjLEdBQUksaURBQThCLENBQUMsd0JBQXdCLENBQUMscUJBQXFCLENBQUMsQ0FBQztZQUV2RyxNQUFNLENBQUMsYUFBYSxDQUFDLENBQUMsSUFBSSxDQUFDLHVDQUFlLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDaEQsTUFBTSxDQUFDLGNBQWMsQ0FBQyxDQUFDLElBQUksQ0FBQyx1Q0FBZSxDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBQ3pELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDZDQUE2QyxFQUFFLEdBQUcsRUFBRTtZQUNyRCxNQUFNLFlBQVksR0FBRztnQkFDbkIsVUFBVSxFQUFFLGdCQUFnQjtnQkFDNUIsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFO2dCQUNuQyxTQUFTLEVBQUUsZUFBZTthQUMzQixDQUFDO1lBRUYsTUFBTSxTQUFTLEdBQUcsQ0FBQyw2Q0FBbUIsQ0FBQyxRQUFRLEVBQUUsNkNBQW1CLENBQUMsT0FBTyxDQUFDLENBQUM7WUFFOUUsTUFBTSxjQUFjLEdBQUksaURBQThCLENBQUMsa0JBQWtCLENBQUMsWUFBWSxFQUFFLFNBQVMsQ0FBQyxDQUFDO1lBRW5HLE1BQU0sQ0FBQyxjQUFjLENBQUMsQ0FBQyxPQUFPLENBQUM7Z0JBQzdCLEdBQUcsWUFBWTtnQkFDZixhQUFhLEVBQUUsTUFBTSxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUM7Z0JBQ2pDLHNCQUFzQixFQUFFLFNBQVM7YUFDbEMsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxnQkFBZ0IsRUFBRSxHQUFHLEVBQUU7UUFDOUIsRUFBRSxDQUFDLHNDQUFzQyxFQUFFLEdBQUcsRUFBRTtZQUM5QyxNQUFNLE9BQU8sR0FBaUM7Z0JBQzVDLGFBQWEsRUFBRSxJQUFXO2dCQUMxQixjQUFjLEVBQUUsRUFBRSxjQUFjLEVBQUUsVUFBVSxFQUFFO2FBQy9DLENBQUM7WUFFRixNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsaURBQXNCLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsT0FBTyxFQUFFLENBQUM7UUFDakUsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsd0NBQXdDLEVBQUUsR0FBRyxFQUFFO1lBQ2hELE1BQU0sT0FBTyxHQUFpQztnQkFDNUMsYUFBYSxFQUFFLGlCQUFpQjtnQkFDaEMsY0FBYyxFQUFFLElBQVc7YUFDNUIsQ0FBQztZQUVGLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyxpREFBc0IsQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxPQUFPLEVBQUUsQ0FBQztRQUNqRSxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxpREFBaUQsRUFBRSxHQUFHLEVBQUU7WUFDekQsTUFBTSxZQUFZLEdBQTRCO2dCQUM1QyxjQUFjLEVBQUUsRUFBRTtnQkFDbEIsS0FBSyxFQUFFLEVBQUU7YUFDVixDQUFDO1lBRUYsTUFBTSxNQUFNLEdBQUcsaURBQXNCLENBQUMsV0FBVyxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRWhFLE1BQU0sQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQzFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3RDLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNyQyxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDMUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3hDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXGNvcmVcXHNlY3VyaXR5XFxkb21haW5cXGZhY3Rvcmllc1xcX190ZXN0c19fXFxjb3JyZWxhdGVkLWV2ZW50LmZhY3Rvcnkuc3BlYy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBcclxuICBDb3JyZWxhdGVkRXZlbnRGYWN0b3J5LCBcclxuICBDcmVhdGVDb3JyZWxhdGVkRXZlbnRPcHRpb25zLCBcclxuICBDb3JyZWxhdGlvbkNvbmZpZywgXHJcbiAgQmF0Y2hDb3JyZWxhdGlvbk9wdGlvbnMsIFxyXG4gIEF0dGFja0NoYWluRGV0ZWN0aW9uT3B0aW9ucyBcclxufSBmcm9tICcuLi9jb3JyZWxhdGVkLWV2ZW50LmZhY3RvcnknO1xyXG5pbXBvcnQgeyBcclxuICBDb3JyZWxhdGVkRXZlbnQsIFxyXG4gIENvcnJlbGF0aW9uUnVsZSwgXHJcbiAgQ29ycmVsYXRpb25NYXRjaCwgXHJcbiAgQ29ycmVsYXRpb25SZXN1bHQsIFxyXG4gIEF0dGFja0NoYWluLCBcclxuICBDb3JyZWxhdGlvblJ1bGVUeXBlLCBcclxuICBDb3JyZWxhdGlvbk1hdGNoVHlwZSBcclxufSBmcm9tICcuLi8uLi9lbnRpdGllcy9jb3JyZWxhdGVkLWV2ZW50LmVudGl0eSc7XHJcbmltcG9ydCB7IENvcnJlbGF0aW9uU3RhdHVzIH0gZnJvbSAnLi4vLi4vZW51bXMvY29ycmVsYXRpb24tc3RhdHVzLmVudW0nO1xyXG5pbXBvcnQgeyBFbnJpY2hlZEV2ZW50IH0gZnJvbSAnLi4vLi4vZW50aXRpZXMvZW5yaWNoZWQtZXZlbnQuZW50aXR5JztcclxuaW1wb3J0IHsgVW5pcXVlRW50aXR5SWQgfSBmcm9tICcuLi8uLi8uLi8uLi8uLi9zaGFyZWQta2VybmVsJztcclxuaW1wb3J0IHsgRXZlbnRUeXBlIH0gZnJvbSAnLi4vLi4vZW51bXMvZXZlbnQtdHlwZS5lbnVtJztcclxuaW1wb3J0IHsgRXZlbnRTZXZlcml0eSB9IGZyb20gJy4uLy4uL2VudW1zL2V2ZW50LXNldmVyaXR5LmVudW0nO1xyXG5pbXBvcnQgeyBFdmVudFN0YXR1cyB9IGZyb20gJy4uLy4uL2VudW1zL2V2ZW50LXN0YXR1cy5lbnVtJztcclxuaW1wb3J0IHsgRXZlbnRQcm9jZXNzaW5nU3RhdHVzIH0gZnJvbSAnLi4vLi4vZW51bXMvZXZlbnQtcHJvY2Vzc2luZy1zdGF0dXMuZW51bSc7XHJcbmltcG9ydCB7IENvbmZpZGVuY2VMZXZlbCB9IGZyb20gJy4uLy4uL2VudW1zL2NvbmZpZGVuY2UtbGV2ZWwuZW51bSc7XHJcbmltcG9ydCB7IEV2ZW50TWV0YWRhdGEgfSBmcm9tICcuLi8uLi92YWx1ZS1vYmplY3RzL2V2ZW50LW1ldGFkYXRhL2V2ZW50LW1ldGFkYXRhLnZhbHVlLW9iamVjdCc7XHJcbmltcG9ydCB7IEV2ZW50VGltZXN0YW1wIH0gZnJvbSAnLi4vLi4vdmFsdWUtb2JqZWN0cy9ldmVudC1tZXRhZGF0YS9ldmVudC10aW1lc3RhbXAudmFsdWUtb2JqZWN0JztcclxuaW1wb3J0IHsgRXZlbnRTb3VyY2UgfSBmcm9tICcuLi8uLi92YWx1ZS1vYmplY3RzL2V2ZW50LW1ldGFkYXRhL2V2ZW50LXNvdXJjZS52YWx1ZS1vYmplY3QnO1xyXG5cclxuZGVzY3JpYmUoJ0NvcnJlbGF0ZWRFdmVudEZhY3RvcnknLCAoKSA9PiB7XHJcbiAgbGV0IG1vY2tFbnJpY2hlZEV2ZW50OiBFbnJpY2hlZEV2ZW50O1xyXG4gIGxldCBtb2NrRXZlbnRNZXRhZGF0YTogRXZlbnRNZXRhZGF0YTtcclxuXHJcbiAgYmVmb3JlRWFjaCgoKSA9PiB7XHJcbiAgICAvLyBDcmVhdGUgbW9jayBldmVudCBtZXRhZGF0YVxyXG4gICAgbW9ja0V2ZW50TWV0YWRhdGEgPSBFdmVudE1ldGFkYXRhLmNyZWF0ZSh7XHJcbiAgICAgIHRpbWVzdGFtcDogRXZlbnRUaW1lc3RhbXAubm93KCksXHJcbiAgICAgIHNvdXJjZTogRXZlbnRTb3VyY2UuY3JlYXRlKHtcclxuICAgICAgICB0eXBlOiAnU0lFTScsXHJcbiAgICAgICAgaWRlbnRpZmllcjogJ3Rlc3Qtc2llbS0wMDEnLFxyXG4gICAgICAgIG5hbWU6ICdUZXN0IFNJRU0gU3lzdGVtJ1xyXG4gICAgICB9KSxcclxuICAgICAgcHJvY2Vzc2luZ0luZm86IHtcclxuICAgICAgICByZWNlaXZlZEF0OiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgIHByb2Nlc3NlZEF0OiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgIHByb2Nlc3NpbmdEdXJhdGlvbjogMTAwLFxyXG4gICAgICAgIHZlcnNpb246ICcxLjAuMCdcclxuICAgICAgfVxyXG4gICAgfSk7XHJcblxyXG4gICAgLy8gQ3JlYXRlIG1vY2sgZW5yaWNoZWQgZXZlbnRcclxuICAgIG1vY2tFbnJpY2hlZEV2ZW50ID0ge1xyXG4gICAgICBpZDogVW5pcXVlRW50aXR5SWQuY3JlYXRlKCksXHJcbiAgICAgIG1ldGFkYXRhOiBtb2NrRXZlbnRNZXRhZGF0YSxcclxuICAgICAgdHlwZTogRXZlbnRUeXBlLlNFQ1VSSVRZX0FMRVJULFxyXG4gICAgICBzZXZlcml0eTogRXZlbnRTZXZlcml0eS5ISUdILFxyXG4gICAgICBzdGF0dXM6IEV2ZW50U3RhdHVzLkFDVElWRSxcclxuICAgICAgcHJvY2Vzc2luZ1N0YXR1czogRXZlbnRQcm9jZXNzaW5nU3RhdHVzLkVOUklDSEVELFxyXG4gICAgICBlbnJpY2hlZERhdGE6IHtcclxuICAgICAgICBvcmlnaW5hbF9ldmVudDogJ3Rlc3RfZXZlbnQnLFxyXG4gICAgICAgIGVucmljaGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgdGhyZWF0X2luZGljYXRvcnM6IFsnbWFsaWNpb3VzX2lwJywgJ3N1c3BpY2lvdXNfZG9tYWluJ10sXHJcbiAgICAgICAgcmlza19zY29yZTogNzVcclxuICAgICAgfSxcclxuICAgICAgdGl0bGU6ICdUZXN0IEVucmljaGVkIFNlY3VyaXR5IEV2ZW50JyxcclxuICAgICAgZGVzY3JpcHRpb246ICdBIHRlc3QgZW5yaWNoZWQgc2VjdXJpdHkgZXZlbnQgZm9yIHVuaXQgdGVzdGluZycsXHJcbiAgICAgIHRhZ3M6IFsndGVzdCcsICdlbnJpY2htZW50JywgJ3NlY3VyaXR5J10sXHJcbiAgICAgIHJpc2tTY29yZTogNzUsXHJcbiAgICAgIGNvbmZpZGVuY2VMZXZlbDogQ29uZmlkZW5jZUxldmVsLkhJR0gsXHJcbiAgICAgIGF0dHJpYnV0ZXM6IHtcclxuICAgICAgICB0ZXN0X2F0dHJpYnV0ZTogJ3Rlc3RfdmFsdWUnXHJcbiAgICAgIH1cclxuICAgIH0gYXMgRW5yaWNoZWRFdmVudDtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2NyZWF0ZScsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgY3JlYXRlIGEgQ29ycmVsYXRlZEV2ZW50IHdpdGggYmFzaWMgb3B0aW9ucycsICgpID0+IHtcclxuICAgICAgY29uc3Qgb3B0aW9uczogQ3JlYXRlQ29ycmVsYXRlZEV2ZW50T3B0aW9ucyA9IHtcclxuICAgICAgICBlbnJpY2hlZEV2ZW50OiBtb2NrRW5yaWNoZWRFdmVudCxcclxuICAgICAgICBjb3JyZWxhdGVkRGF0YToge1xyXG4gICAgICAgICAgY29ycmVsYXRpb25faWQ6ICdjb3JyXzEyMycsXHJcbiAgICAgICAgICByZWxhdGVkX2V2ZW50czogMyxcclxuICAgICAgICAgIHBhdHRlcm5zX2ZvdW5kOiBbJ3RlbXBvcmFsX3NlcXVlbmNlJ11cclxuICAgICAgICB9XHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBjb3JyZWxhdGVkRXZlbnQgPSBDb3JyZWxhdGVkRXZlbnRGYWN0b3J5LmNyZWF0ZShvcHRpb25zKTtcclxuXHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQpLnRvQmVJbnN0YW5jZU9mKENvcnJlbGF0ZWRFdmVudCk7XHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuZW5yaWNoZWRFdmVudElkKS50b0VxdWFsKG1vY2tFbnJpY2hlZEV2ZW50LmlkKTtcclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWRFdmVudC5tZXRhZGF0YSkudG9FcXVhbChtb2NrRW5yaWNoZWRFdmVudC5tZXRhZGF0YSk7XHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQudHlwZSkudG9CZShtb2NrRW5yaWNoZWRFdmVudC50eXBlKTtcclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWRFdmVudC5zZXZlcml0eSkudG9CZShtb2NrRW5yaWNoZWRFdmVudC5zZXZlcml0eSk7XHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuY29ycmVsYXRpb25TdGF0dXMpLnRvQmUoQ29ycmVsYXRpb25TdGF0dXMuUEVORElORyk7XHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuY29ycmVsYXRlZERhdGEpLnRvRXF1YWwob3B0aW9ucy5jb3JyZWxhdGVkRGF0YSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGNyZWF0ZSBhIENvcnJlbGF0ZWRFdmVudCB3aXRoIGN1c3RvbSBJRCcsICgpID0+IHtcclxuICAgICAgY29uc3QgY3VzdG9tSWQgPSBVbmlxdWVFbnRpdHlJZC5jcmVhdGUoKTtcclxuICAgICAgY29uc3Qgb3B0aW9uczogQ3JlYXRlQ29ycmVsYXRlZEV2ZW50T3B0aW9ucyA9IHtcclxuICAgICAgICBpZDogY3VzdG9tSWQsXHJcbiAgICAgICAgZW5yaWNoZWRFdmVudDogbW9ja0VucmljaGVkRXZlbnQsXHJcbiAgICAgICAgY29ycmVsYXRlZERhdGE6IHtcclxuICAgICAgICAgIGNvcnJlbGF0aW9uX2lkOiAnY29ycl8xMjMnXHJcbiAgICAgICAgfVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgY29ycmVsYXRlZEV2ZW50ID0gQ29ycmVsYXRlZEV2ZW50RmFjdG9yeS5jcmVhdGUob3B0aW9ucyk7XHJcblxyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LmlkKS50b0VxdWFsKGN1c3RvbUlkKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY3JlYXRlIGEgQ29ycmVsYXRlZEV2ZW50IHdpdGggYXBwbGllZCBydWxlcycsICgpID0+IHtcclxuICAgICAgY29uc3QgcnVsZTogQ29ycmVsYXRpb25SdWxlID0ge1xyXG4gICAgICAgIGlkOiAndGVtcG9yYWxfcnVsZV8xJyxcclxuICAgICAgICBuYW1lOiAnVGVtcG9yYWwgQ29ycmVsYXRpb24gUnVsZScsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdSdWxlIGZvciB0ZW1wb3JhbCBldmVudCBjb3JyZWxhdGlvbicsXHJcbiAgICAgICAgdHlwZTogQ29ycmVsYXRpb25SdWxlVHlwZS5URU1QT1JBTCxcclxuICAgICAgICBwcmlvcml0eTogMTAwLFxyXG4gICAgICAgIHJlcXVpcmVkOiBmYWxzZSxcclxuICAgICAgICB0aW1lV2luZG93TXM6IDM2MDAwMDAsXHJcbiAgICAgICAgbWluQ29uZmlkZW5jZTogNzBcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IG9wdGlvbnM6IENyZWF0ZUNvcnJlbGF0ZWRFdmVudE9wdGlvbnMgPSB7XHJcbiAgICAgICAgZW5yaWNoZWRFdmVudDogbW9ja0VucmljaGVkRXZlbnQsXHJcbiAgICAgICAgY29ycmVsYXRlZERhdGE6IHsgY29ycmVsYXRpb25faWQ6ICdjb3JyXzEyMycgfSxcclxuICAgICAgICBhcHBsaWVkUnVsZXM6IFtydWxlXVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgY29ycmVsYXRlZEV2ZW50ID0gQ29ycmVsYXRlZEV2ZW50RmFjdG9yeS5jcmVhdGUob3B0aW9ucyk7XHJcblxyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LmFwcGxpZWRSdWxlcykudG9IYXZlTGVuZ3RoKDEpO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LmFwcGxpZWRSdWxlc1swXSkudG9FcXVhbChydWxlKTtcclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWRFdmVudC5oYXNBcHBsaWVkUnVsZSgndGVtcG9yYWxfcnVsZV8xJykpLnRvQmUodHJ1ZSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGNyZWF0ZSBhIENvcnJlbGF0ZWRFdmVudCB3aXRoIGNvcnJlbGF0aW9uIG1hdGNoZXMnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IG1hdGNoOiBDb3JyZWxhdGlvbk1hdGNoID0ge1xyXG4gICAgICAgIGV2ZW50SWQ6IFVuaXF1ZUVudGl0eUlkLmNyZWF0ZSgpLFxyXG4gICAgICAgIGNvbmZpZGVuY2U6IDg1LFxyXG4gICAgICAgIG1hdGNoVHlwZTogQ29ycmVsYXRpb25NYXRjaFR5cGUuVEVNUE9SQUwsXHJcbiAgICAgICAgcnVsZUlkOiAndGVtcG9yYWxfcnVsZV8xJyxcclxuICAgICAgICBkZXRhaWxzOiB7IHRpbWVXaW5kb3c6IDM2MDAwMDAgfSxcclxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXHJcbiAgICAgICAgd2VpZ2h0OiAwLjg1XHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBvcHRpb25zOiBDcmVhdGVDb3JyZWxhdGVkRXZlbnRPcHRpb25zID0ge1xyXG4gICAgICAgIGVucmljaGVkRXZlbnQ6IG1vY2tFbnJpY2hlZEV2ZW50LFxyXG4gICAgICAgIGNvcnJlbGF0ZWREYXRhOiB7IGNvcnJlbGF0aW9uX2lkOiAnY29ycl8xMjMnIH0sXHJcbiAgICAgICAgY29ycmVsYXRpb25NYXRjaGVzOiBbbWF0Y2hdXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBjb3JyZWxhdGVkRXZlbnQgPSBDb3JyZWxhdGVkRXZlbnRGYWN0b3J5LmNyZWF0ZShvcHRpb25zKTtcclxuXHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuY29ycmVsYXRpb25NYXRjaGVzKS50b0hhdmVMZW5ndGgoMSk7XHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuY29ycmVsYXRpb25NYXRjaGVzWzBdKS50b0VxdWFsKG1hdGNoKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgb3ZlcnJpZGUgZW5yaWNoZWQgZXZlbnQgcHJvcGVydGllcyB3aGVuIHNwZWNpZmllZCcsICgpID0+IHtcclxuICAgICAgY29uc3Qgb3B0aW9uczogQ3JlYXRlQ29ycmVsYXRlZEV2ZW50T3B0aW9ucyA9IHtcclxuICAgICAgICBlbnJpY2hlZEV2ZW50OiBtb2NrRW5yaWNoZWRFdmVudCxcclxuICAgICAgICBjb3JyZWxhdGVkRGF0YTogeyBjb3JyZWxhdGlvbl9pZDogJ2NvcnJfMTIzJyB9LFxyXG4gICAgICAgIHR5cGU6IEV2ZW50VHlwZS5WVUxORVJBQklMSVRZX1NDQU4sXHJcbiAgICAgICAgc2V2ZXJpdHk6IEV2ZW50U2V2ZXJpdHkuQ1JJVElDQUwsXHJcbiAgICAgICAgdGl0bGU6ICdDdXN0b20gQ29ycmVsYXRlZCBFdmVudCBUaXRsZSdcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IGNvcnJlbGF0ZWRFdmVudCA9IENvcnJlbGF0ZWRFdmVudEZhY3RvcnkuY3JlYXRlKG9wdGlvbnMpO1xyXG5cclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWRFdmVudC50eXBlKS50b0JlKEV2ZW50VHlwZS5WVUxORVJBQklMSVRZX1NDQU4pO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LnNldmVyaXR5KS50b0JlKEV2ZW50U2V2ZXJpdHkuQ1JJVElDQUwpO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LnRpdGxlKS50b0JlKCdDdXN0b20gQ29ycmVsYXRlZCBFdmVudCBUaXRsZScpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBnZW5lcmF0ZSBjb3JyZWxhdGlvbiBJRCB3aGVuIG5vdCBwcm92aWRlZCcsICgpID0+IHtcclxuICAgICAgY29uc3Qgb3B0aW9uczogQ3JlYXRlQ29ycmVsYXRlZEV2ZW50T3B0aW9ucyA9IHtcclxuICAgICAgICBlbnJpY2hlZEV2ZW50OiBtb2NrRW5yaWNoZWRFdmVudCxcclxuICAgICAgICBjb3JyZWxhdGVkRGF0YTogeyBjb3JyZWxhdGlvbl9pZDogJ2NvcnJfMTIzJyB9XHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBjb3JyZWxhdGVkRXZlbnQgPSBDb3JyZWxhdGVkRXZlbnRGYWN0b3J5LmNyZWF0ZShvcHRpb25zKTtcclxuXHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuY29ycmVsYXRpb25JZCkudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWRFdmVudC5jb3JyZWxhdGlvbklkKS50b01hdGNoKC9eY29ycl8vKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY2FsY3VsYXRlIGNvbmZpZGVuY2UgbGV2ZWwgZnJvbSBjb3JyZWxhdGlvbiBtYXRjaGVzJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBoaWdoQ29uZmlkZW5jZU1hdGNoOiBDb3JyZWxhdGlvbk1hdGNoID0ge1xyXG4gICAgICAgIGV2ZW50SWQ6IFVuaXF1ZUVudGl0eUlkLmNyZWF0ZSgpLFxyXG4gICAgICAgIGNvbmZpZGVuY2U6IDk1LFxyXG4gICAgICAgIG1hdGNoVHlwZTogQ29ycmVsYXRpb25NYXRjaFR5cGUuUEFUVEVSTixcclxuICAgICAgICBydWxlSWQ6ICdwYXR0ZXJuX3J1bGVfMScsXHJcbiAgICAgICAgZGV0YWlsczoge30sXHJcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgIHdlaWdodDogMC45NVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3Qgb3B0aW9uczogQ3JlYXRlQ29ycmVsYXRlZEV2ZW50T3B0aW9ucyA9IHtcclxuICAgICAgICBlbnJpY2hlZEV2ZW50OiBtb2NrRW5yaWNoZWRFdmVudCxcclxuICAgICAgICBjb3JyZWxhdGVkRGF0YTogeyBjb3JyZWxhdGlvbl9pZDogJ2NvcnJfMTIzJyB9LFxyXG4gICAgICAgIGNvcnJlbGF0aW9uTWF0Y2hlczogW2hpZ2hDb25maWRlbmNlTWF0Y2hdXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBjb3JyZWxhdGVkRXZlbnQgPSBDb3JyZWxhdGVkRXZlbnRGYWN0b3J5LmNyZWF0ZShvcHRpb25zKTtcclxuXHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuY29uZmlkZW5jZUxldmVsKS50b0JlKENvbmZpZGVuY2VMZXZlbC5WRVJZX0hJR0gpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdjcmVhdGVXaXRoQ29ycmVsYXRpb24nLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIGNyZWF0ZSBhIENvcnJlbGF0ZWRFdmVudCB3aXRoIGF1dG9tYXRpYyBjb3JyZWxhdGlvbicsICgpID0+IHtcclxuICAgICAgY29uc3QgY29uZmlnOiBQYXJ0aWFsPENvcnJlbGF0aW9uQ29uZmlnPiA9IHtcclxuICAgICAgICBhdmFpbGFibGVSdWxlczogW1xyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICBpZDogJ3RlbXBvcmFsX3J1bGUnLFxyXG4gICAgICAgICAgICBuYW1lOiAnVGVtcG9yYWwgUnVsZScsXHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnVGVtcG9yYWwgY29ycmVsYXRpb24gcnVsZScsXHJcbiAgICAgICAgICAgIHR5cGU6IENvcnJlbGF0aW9uUnVsZVR5cGUuVEVNUE9SQUwsXHJcbiAgICAgICAgICAgIHByaW9yaXR5OiAxMDAsXHJcbiAgICAgICAgICAgIHJlcXVpcmVkOiBmYWxzZSxcclxuICAgICAgICAgICAgdGltZVdpbmRvd01zOiAzNjAwMDAwLFxyXG4gICAgICAgICAgICBtaW5Db25maWRlbmNlOiA3MFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIF0sXHJcbiAgICAgICAgbWluQ29ycmVsYXRpb25RdWFsaXR5VGhyZXNob2xkOiA3MCxcclxuICAgICAgICBlbmFibGVkUnVsZVR5cGVzOiBbQ29ycmVsYXRpb25SdWxlVHlwZS5URU1QT1JBTCwgQ29ycmVsYXRpb25SdWxlVHlwZS5TUEFUSUFMXVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgY29ycmVsYXRlZEV2ZW50ID0gQ29ycmVsYXRlZEV2ZW50RmFjdG9yeS5jcmVhdGVXaXRoQ29ycmVsYXRpb24obW9ja0VucmljaGVkRXZlbnQsIGNvbmZpZyk7XHJcblxyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50KS50b0JlSW5zdGFuY2VPZihDb3JyZWxhdGVkRXZlbnQpO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LmNvcnJlbGF0aW9uU3RhdHVzKS50b0JlKENvcnJlbGF0aW9uU3RhdHVzLkNPTVBMRVRFRCk7XHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuY29ycmVsYXRpb25RdWFsaXR5U2NvcmUpLnRvQmVHcmVhdGVyVGhhbigwKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgc2V0IGNvcnJlbGF0aW9uIHN0YXR1cyB0byBGQUlMRUQgd2hlbiBjb3JyZWxhdGlvbiBmYWlscycsICgpID0+IHtcclxuICAgICAgY29uc3QgY29uZmlnOiBQYXJ0aWFsPENvcnJlbGF0aW9uQ29uZmlnPiA9IHtcclxuICAgICAgICBhdmFpbGFibGVSdWxlczogW1xyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICBpZDogJ3JlcXVpcmVkX3J1bGUnLFxyXG4gICAgICAgICAgICBuYW1lOiAnUmVxdWlyZWQgUnVsZScsXHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnUmVxdWlyZWQgY29ycmVsYXRpb24gcnVsZSB0aGF0IHdpbGwgZmFpbCcsXHJcbiAgICAgICAgICAgIHR5cGU6IENvcnJlbGF0aW9uUnVsZVR5cGUuUEFUVEVSTixcclxuICAgICAgICAgICAgcHJpb3JpdHk6IDEwMCxcclxuICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsXHJcbiAgICAgICAgICAgIHRpbWVXaW5kb3dNczogMzYwMDAwMCxcclxuICAgICAgICAgICAgbWluQ29uZmlkZW5jZTogNzBcclxuICAgICAgICAgIH1cclxuICAgICAgICBdLFxyXG4gICAgICAgIG1pbkNvcnJlbGF0aW9uUXVhbGl0eVRocmVzaG9sZDogOTAgLy8gSGlnaCB0aHJlc2hvbGQgdG8gdHJpZ2dlciBmYWlsdXJlXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBjb3JyZWxhdGVkRXZlbnQgPSBDb3JyZWxhdGVkRXZlbnRGYWN0b3J5LmNyZWF0ZVdpdGhDb3JyZWxhdGlvbihtb2NrRW5yaWNoZWRFdmVudCwgY29uZmlnKTtcclxuXHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuY29ycmVsYXRpb25TdGF0dXMpLnRvQmUoQ29ycmVsYXRpb25TdGF0dXMuRkFJTEVEKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgZGV0ZXJtaW5lIG1hbnVhbCByZXZpZXcgcmVxdWlyZW1lbnQgYmFzZWQgb24gY29uZmlndXJhdGlvbicsICgpID0+IHtcclxuICAgICAgY29uc3QgY29uZmlnOiBQYXJ0aWFsPENvcnJlbGF0aW9uQ29uZmlnPiA9IHtcclxuICAgICAgICByZXF1aXJlTWFudWFsUmV2aWV3Rm9ySGlnaENvbmZpZGVuY2U6IHRydWUsXHJcbiAgICAgICAgbWluQ29ycmVsYXRpb25RdWFsaXR5VGhyZXNob2xkOiA1MFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgLy8gQ3JlYXRlIGVucmljaGVkIGV2ZW50IHdpdGggY3JpdGljYWwgc2V2ZXJpdHkgdG8gdHJpZ2dlciBtYW51YWwgcmV2aWV3XHJcbiAgICAgIGNvbnN0IGNyaXRpY2FsRW5yaWNoZWRFdmVudCA9IHtcclxuICAgICAgICAuLi5tb2NrRW5yaWNoZWRFdmVudCxcclxuICAgICAgICBzZXZlcml0eTogRXZlbnRTZXZlcml0eS5DUklUSUNBTFxyXG4gICAgICB9IGFzIEVucmljaGVkRXZlbnQ7XHJcblxyXG4gICAgICBjb25zdCBjb3JyZWxhdGVkRXZlbnQgPSBDb3JyZWxhdGVkRXZlbnRGYWN0b3J5LmNyZWF0ZVdpdGhDb3JyZWxhdGlvbihjcml0aWNhbEVucmljaGVkRXZlbnQsIGNvbmZpZyk7XHJcblxyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LnJlcXVpcmVzTWFudWFsUmV2aWV3KS50b0JlKHRydWUpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdjcmVhdGVXaXRoQXR0YWNrQ2hhaW5EZXRlY3Rpb24nLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIGNyZWF0ZSBhIENvcnJlbGF0ZWRFdmVudCB3aXRoIGF0dGFjayBjaGFpbiBkZXRlY3Rpb24nLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGF0dGFja0NoYWluT3B0aW9uczogQXR0YWNrQ2hhaW5EZXRlY3Rpb25PcHRpb25zID0ge1xyXG4gICAgICAgIGV2ZW50czogW21vY2tFbnJpY2hlZEV2ZW50XSxcclxuICAgICAgICB0aW1lV2luZG93TXM6IDM2MDAwMDAsXHJcbiAgICAgICAgbWluQ29uZmlkZW5jZTogQ29uZmlkZW5jZUxldmVsLk1FRElVTSxcclxuICAgICAgICBtYXhTdGFnZXM6IDUsXHJcbiAgICAgICAgaW5jbHVkZUJlaGF2aW9yYWxBbmFseXNpczogdHJ1ZVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgY29ycmVsYXRlZEV2ZW50ID0gQ29ycmVsYXRlZEV2ZW50RmFjdG9yeS5jcmVhdGVXaXRoQXR0YWNrQ2hhaW5EZXRlY3Rpb24oXHJcbiAgICAgICAgbW9ja0VucmljaGVkRXZlbnQsXHJcbiAgICAgICAgYXR0YWNrQ2hhaW5PcHRpb25zXHJcbiAgICAgICk7XHJcblxyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50KS50b0JlSW5zdGFuY2VPZihDb3JyZWxhdGVkRXZlbnQpO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LmNvcnJlbGF0aW9uU3RhdHVzKS50b0JlKENvcnJlbGF0aW9uU3RhdHVzLkNPTVBMRVRFRCk7XHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuY29ycmVsYXRlZERhdGEuYXR0YWNrX2NoYWluKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBpbmNsdWRlIGF0dGFjayBjaGFpbiB3aGVuIGRldGVjdGVkJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBhdHRhY2tDaGFpbk9wdGlvbnM6IEF0dGFja0NoYWluRGV0ZWN0aW9uT3B0aW9ucyA9IHtcclxuICAgICAgICBldmVudHM6IFttb2NrRW5yaWNoZWRFdmVudF0sXHJcbiAgICAgICAgdGltZVdpbmRvd01zOiAzNjAwMDAwLFxyXG4gICAgICAgIG1pbkNvbmZpZGVuY2U6IENvbmZpZGVuY2VMZXZlbC5MT1csIC8vIExvdyB0aHJlc2hvbGQgdG8gaW5jcmVhc2UgZGV0ZWN0aW9uIGNoYW5jZVxyXG4gICAgICAgIG1heFN0YWdlczogNSxcclxuICAgICAgICBpbmNsdWRlQmVoYXZpb3JhbEFuYWx5c2lzOiB0cnVlXHJcbiAgICAgIH07XHJcblxyXG4gICAgICAvLyBNb2NrIE1hdGgucmFuZG9tIHRvIGVuc3VyZSBhdHRhY2sgY2hhaW4gZGV0ZWN0aW9uXHJcbiAgICAgIGNvbnN0IG9yaWdpbmFsUmFuZG9tID0gTWF0aC5yYW5kb207XHJcbiAgICAgIE1hdGgucmFuZG9tID0gamVzdC5mbigoKSA9PiAwLjIpOyAvLyAyMCUgPCAzMCUgdGhyZXNob2xkXHJcblxyXG4gICAgICBjb25zdCBjb3JyZWxhdGVkRXZlbnQgPSBDb3JyZWxhdGVkRXZlbnRGYWN0b3J5LmNyZWF0ZVdpdGhBdHRhY2tDaGFpbkRldGVjdGlvbihcclxuICAgICAgICBtb2NrRW5yaWNoZWRFdmVudCxcclxuICAgICAgICBhdHRhY2tDaGFpbk9wdGlvbnNcclxuICAgICAgKTtcclxuXHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuaGFzQXR0YWNrQ2hhaW4oKSkudG9CZSh0cnVlKTtcclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWRFdmVudC5hdHRhY2tDaGFpbikudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWRFdmVudC5jb3JyZWxhdGlvblBhdHRlcm5zKS50b0NvbnRhaW4oJ2F0dGFja19jaGFpbicpO1xyXG5cclxuICAgICAgLy8gUmVzdG9yZSBvcmlnaW5hbCBNYXRoLnJhbmRvbVxyXG4gICAgICBNYXRoLnJhbmRvbSA9IG9yaWdpbmFsUmFuZG9tO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdjcmVhdGVCYXRjaCcsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgY3JlYXRlIG11bHRpcGxlIENvcnJlbGF0ZWRFdmVudHMgaW4gYmF0Y2gnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGVucmljaGVkRXZlbnRzID0gW1xyXG4gICAgICAgIG1vY2tFbnJpY2hlZEV2ZW50LFxyXG4gICAgICAgIHsgLi4ubW9ja0VucmljaGVkRXZlbnQsIGlkOiBVbmlxdWVFbnRpdHlJZC5jcmVhdGUoKSB9IGFzIEVucmljaGVkRXZlbnQsXHJcbiAgICAgICAgeyAuLi5tb2NrRW5yaWNoZWRFdmVudCwgaWQ6IFVuaXF1ZUVudGl0eUlkLmNyZWF0ZSgpIH0gYXMgRW5yaWNoZWRFdmVudFxyXG4gICAgICBdO1xyXG5cclxuICAgICAgY29uc3QgYmF0Y2hPcHRpb25zOiBCYXRjaENvcnJlbGF0aW9uT3B0aW9ucyA9IHtcclxuICAgICAgICBlbnJpY2hlZEV2ZW50cyxcclxuICAgICAgICBydWxlczogW1xyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICBpZDogJ2JhdGNoX3J1bGUnLFxyXG4gICAgICAgICAgICBuYW1lOiAnQmF0Y2ggUnVsZScsXHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnUnVsZSBmb3IgYmF0Y2ggcHJvY2Vzc2luZycsXHJcbiAgICAgICAgICAgIHR5cGU6IENvcnJlbGF0aW9uUnVsZVR5cGUuVEVNUE9SQUwsXHJcbiAgICAgICAgICAgIHByaW9yaXR5OiAxMDAsXHJcbiAgICAgICAgICAgIHJlcXVpcmVkOiBmYWxzZSxcclxuICAgICAgICAgICAgdGltZVdpbmRvd01zOiAzNjAwMDAwLFxyXG4gICAgICAgICAgICBtaW5Db25maWRlbmNlOiA3MFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIF1cclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IENvcnJlbGF0ZWRFdmVudEZhY3RvcnkuY3JlYXRlQmF0Y2goYmF0Y2hPcHRpb25zKTtcclxuXHJcbiAgICAgIGV4cGVjdChyZXN1bHQuc3VjY2Vzc2Z1bCkudG9IYXZlTGVuZ3RoKDMpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LmZhaWxlZCkudG9IYXZlTGVuZ3RoKDApO1xyXG4gICAgICBleHBlY3QocmVzdWx0LnN1bW1hcnkudG90YWwpLnRvQmUoMyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuc3VtbWFyeS5zdWNjZXNzZnVsKS50b0JlKDMpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LnN1bW1hcnkuZmFpbGVkKS50b0JlKDApO1xyXG4gICAgICBleHBlY3QocmVzdWx0LnN1bW1hcnkucHJvY2Vzc2luZ1RpbWVNcykudG9CZUdyZWF0ZXJUaGFuKDApO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBoYW5kbGUgYmF0Y2ggcHJvY2Vzc2luZyBmYWlsdXJlcycsICgpID0+IHtcclxuICAgICAgY29uc3QgaW52YWxpZEVucmljaGVkRXZlbnQgPSB7XHJcbiAgICAgICAgLi4ubW9ja0VucmljaGVkRXZlbnQsXHJcbiAgICAgICAgaWQ6IG51bGwgLy8gSW52YWxpZCBJRCB0byBjYXVzZSBmYWlsdXJlXHJcbiAgICAgIH0gYXMgYW55O1xyXG5cclxuICAgICAgY29uc3QgYmF0Y2hPcHRpb25zOiBCYXRjaENvcnJlbGF0aW9uT3B0aW9ucyA9IHtcclxuICAgICAgICBlbnJpY2hlZEV2ZW50czogW21vY2tFbnJpY2hlZEV2ZW50LCBpbnZhbGlkRW5yaWNoZWRFdmVudF0sXHJcbiAgICAgICAgcnVsZXM6IFtdLFxyXG4gICAgICAgIHN0b3BPbkZhaWx1cmU6IGZhbHNlXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBDb3JyZWxhdGVkRXZlbnRGYWN0b3J5LmNyZWF0ZUJhdGNoKGJhdGNoT3B0aW9ucyk7XHJcblxyXG4gICAgICBleHBlY3QocmVzdWx0LnN1Y2Nlc3NmdWwpLnRvSGF2ZUxlbmd0aCgxKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5mYWlsZWQpLnRvSGF2ZUxlbmd0aCgxKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5zdW1tYXJ5LnN1Y2Nlc3NmdWwpLnRvQmUoMSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuc3VtbWFyeS5mYWlsZWQpLnRvQmUoMSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuZmFpbGVkWzBdLmVycm9yKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBzdG9wIG9uIGZpcnN0IGZhaWx1cmUgd2hlbiBjb25maWd1cmVkJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkRW5yaWNoZWRFdmVudCA9IHtcclxuICAgICAgICAuLi5tb2NrRW5yaWNoZWRFdmVudCxcclxuICAgICAgICBpZDogbnVsbCAvLyBJbnZhbGlkIElEIHRvIGNhdXNlIGZhaWx1cmVcclxuICAgICAgfSBhcyBhbnk7XHJcblxyXG4gICAgICBjb25zdCB2YWxpZEVucmljaGVkRXZlbnQgPSB7XHJcbiAgICAgICAgLi4ubW9ja0VucmljaGVkRXZlbnQsXHJcbiAgICAgICAgaWQ6IFVuaXF1ZUVudGl0eUlkLmNyZWF0ZSgpXHJcbiAgICAgIH0gYXMgRW5yaWNoZWRFdmVudDtcclxuXHJcbiAgICAgIGNvbnN0IGJhdGNoT3B0aW9uczogQmF0Y2hDb3JyZWxhdGlvbk9wdGlvbnMgPSB7XHJcbiAgICAgICAgZW5yaWNoZWRFdmVudHM6IFtpbnZhbGlkRW5yaWNoZWRFdmVudCwgdmFsaWRFbnJpY2hlZEV2ZW50XSxcclxuICAgICAgICBydWxlczogW10sXHJcbiAgICAgICAgc3RvcE9uRmFpbHVyZTogdHJ1ZVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gQ29ycmVsYXRlZEV2ZW50RmFjdG9yeS5jcmVhdGVCYXRjaChiYXRjaE9wdGlvbnMpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzZnVsKS50b0hhdmVMZW5ndGgoMCk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuZmFpbGVkKS50b0hhdmVMZW5ndGgoMSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuc3VtbWFyeS50b3RhbCkudG9CZSgyKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnY3JlYXRlRm9yVGVzdGluZycsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgY3JlYXRlIGEgQ29ycmVsYXRlZEV2ZW50IGZvciB0ZXN0aW5nIHdpdGggZGVmYXVsdCB2YWx1ZXMnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGNvcnJlbGF0ZWRFdmVudCA9IENvcnJlbGF0ZWRFdmVudEZhY3RvcnkuY3JlYXRlRm9yVGVzdGluZygpO1xyXG5cclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWRFdmVudCkudG9CZUluc3RhbmNlT2YoQ29ycmVsYXRlZEV2ZW50KTtcclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWRFdmVudC5jb3JyZWxhdGlvblN0YXR1cykudG9CZShDb3JyZWxhdGlvblN0YXR1cy5DT01QTEVURUQpO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LmNvbmZpZGVuY2VMZXZlbCkudG9CZShDb25maWRlbmNlTGV2ZWwuSElHSCk7XHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuY29ycmVsYXRpb25RdWFsaXR5U2NvcmUpLnRvQmUoODUpO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LmFwcGxpZWRSdWxlcykudG9IYXZlTGVuZ3RoKDIpO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LmNvcnJlbGF0aW9uTWF0Y2hlcykudG9IYXZlTGVuZ3RoKDIpO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LnJlbGF0ZWRFdmVudElkcykudG9IYXZlTGVuZ3RoKDIpO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LmNvcnJlbGF0aW9uUGF0dGVybnMpLnRvQ29udGFpbigndGVtcG9yYWxfc2VxdWVuY2UnKTtcclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWRFdmVudC5jb3JyZWxhdGlvblBhdHRlcm5zKS50b0NvbnRhaW4oJ2lwX2NsdXN0ZXJpbmcnKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY3JlYXRlIGEgQ29ycmVsYXRlZEV2ZW50IGZvciB0ZXN0aW5nIHdpdGggb3ZlcnJpZGVzJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBjdXN0b21FbnJpY2hlZEV2ZW50ID0ge1xyXG4gICAgICAgIC4uLm1vY2tFbnJpY2hlZEV2ZW50LFxyXG4gICAgICAgIHRpdGxlOiAnQ3VzdG9tIFRlc3QgRXZlbnQnXHJcbiAgICAgIH0gYXMgRW5yaWNoZWRFdmVudDtcclxuXHJcbiAgICAgIGNvbnN0IG92ZXJyaWRlczogUGFydGlhbDxDcmVhdGVDb3JyZWxhdGVkRXZlbnRPcHRpb25zPiA9IHtcclxuICAgICAgICBlbnJpY2hlZEV2ZW50OiBjdXN0b21FbnJpY2hlZEV2ZW50LFxyXG4gICAgICAgIGNvcnJlbGF0aW9uU3RhdHVzOiBDb3JyZWxhdGlvblN0YXR1cy5GQUlMRUQsXHJcbiAgICAgICAgY29uZmlkZW5jZUxldmVsOiBDb25maWRlbmNlTGV2ZWwuTE9XLFxyXG4gICAgICAgIGNvcnJlbGF0aW9uUXVhbGl0eVNjb3JlOiA0NVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgY29ycmVsYXRlZEV2ZW50ID0gQ29ycmVsYXRlZEV2ZW50RmFjdG9yeS5jcmVhdGVGb3JUZXN0aW5nKG92ZXJyaWRlcyk7XHJcblxyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LnRpdGxlKS50b0JlKCdDdXN0b20gVGVzdCBFdmVudCcpO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LmNvcnJlbGF0aW9uU3RhdHVzKS50b0JlKENvcnJlbGF0aW9uU3RhdHVzLkZBSUxFRCk7XHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuY29uZmlkZW5jZUxldmVsKS50b0JlKENvbmZpZGVuY2VMZXZlbC5MT1cpO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LmNvcnJlbGF0aW9uUXVhbGl0eVNjb3JlKS50b0JlKDQ1KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgaW5jbHVkZSBtb2NrIGNvcnJlbGF0aW9uIGRhdGEgZm9yIHRlc3RpbmcnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGNvcnJlbGF0ZWRFdmVudCA9IENvcnJlbGF0ZWRFdmVudEZhY3RvcnkuY3JlYXRlRm9yVGVzdGluZygpO1xyXG5cclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWRFdmVudC5jb3JyZWxhdGVkRGF0YS5jb3JyZWxhdGVkKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LmNvcnJlbGF0ZWREYXRhLmNvcnJlbGF0aW9uX3RpbWVzdGFtcCkudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWRFdmVudC5jb3JyZWxhdGVkRGF0YS5jb3JyZWxhdGlvbl9wYXR0ZXJucykudG9FcXVhbChbJ3RlbXBvcmFsX3NlcXVlbmNlJywgJ2lwX2NsdXN0ZXJpbmcnXSk7XHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuY29ycmVsYXRlZERhdGEuYXR0YWNrX2NoYWluLmRldGVjdGVkKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QoY29ycmVsYXRlZEV2ZW50LmNvcnJlbGF0ZWREYXRhLmF0dGFja19jaGFpbi5zdGFnZXMpLnRvQmUoMyk7XHJcbiAgICAgIGV4cGVjdChjb3JyZWxhdGVkRXZlbnQuY29ycmVsYXRlZERhdGEuYXR0YWNrX2NoYWluLmNvbmZpZGVuY2UpLnRvQmUoJ2hpZ2gnKTtcclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWRFdmVudC5jb3JyZWxhdGVkRGF0YS5yZWxhdGVkX2V2ZW50cykudG9CZSg1KTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgncHJpdmF0ZSBoZWxwZXIgbWV0aG9kcycsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgZ2VuZXJhdGUgdW5pcXVlIGNvcnJlbGF0aW9uIElEcycsICgpID0+IHtcclxuICAgICAgY29uc3QgaWQxID0gKENvcnJlbGF0ZWRFdmVudEZhY3RvcnkgYXMgYW55KS5nZW5lcmF0ZUNvcnJlbGF0aW9uSWQoKTtcclxuICAgICAgY29uc3QgaWQyID0gKENvcnJlbGF0ZWRFdmVudEZhY3RvcnkgYXMgYW55KS5nZW5lcmF0ZUNvcnJlbGF0aW9uSWQoKTtcclxuXHJcbiAgICAgIGV4cGVjdChpZDEpLnRvTWF0Y2goL15jb3JyX1xcZCtfW2EtejAtOV0rJC8pO1xyXG4gICAgICBleHBlY3QoaWQyKS50b01hdGNoKC9eY29ycl9cXGQrX1thLXowLTldKyQvKTtcclxuICAgICAgZXhwZWN0KGlkMSkubm90LnRvQmUoaWQyKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY2FsY3VsYXRlIGNvbmZpZGVuY2UgbGV2ZWwgZnJvbSBtYXRjaGVzJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBsb3dDb25maWRlbmNlTWF0Y2hlczogQ29ycmVsYXRpb25NYXRjaFtdID0gW1xyXG4gICAgICAgIHtcclxuICAgICAgICAgIGV2ZW50SWQ6IFVuaXF1ZUVudGl0eUlkLmNyZWF0ZSgpLFxyXG4gICAgICAgICAgY29uZmlkZW5jZTogMzAsXHJcbiAgICAgICAgICBtYXRjaFR5cGU6IENvcnJlbGF0aW9uTWF0Y2hUeXBlLkZVWlpZLFxyXG4gICAgICAgICAgcnVsZUlkOiAncnVsZTEnLFxyXG4gICAgICAgICAgZGV0YWlsczoge30sXHJcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXHJcbiAgICAgICAgICB3ZWlnaHQ6IDAuM1xyXG4gICAgICAgIH1cclxuICAgICAgXTtcclxuXHJcbiAgICAgIGNvbnN0IGhpZ2hDb25maWRlbmNlTWF0Y2hlczogQ29ycmVsYXRpb25NYXRjaFtdID0gW1xyXG4gICAgICAgIHtcclxuICAgICAgICAgIGV2ZW50SWQ6IFVuaXF1ZUVudGl0eUlkLmNyZWF0ZSgpLFxyXG4gICAgICAgICAgY29uZmlkZW5jZTogOTUsXHJcbiAgICAgICAgICBtYXRjaFR5cGU6IENvcnJlbGF0aW9uTWF0Y2hUeXBlLkVYQUNULFxyXG4gICAgICAgICAgcnVsZUlkOiAncnVsZTEnLFxyXG4gICAgICAgICAgZGV0YWlsczoge30sXHJcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXHJcbiAgICAgICAgICB3ZWlnaHQ6IDAuOTVcclxuICAgICAgICB9XHJcbiAgICAgIF07XHJcblxyXG4gICAgICBjb25zdCBsb3dDb25maWRlbmNlID0gKENvcnJlbGF0ZWRFdmVudEZhY3RvcnkgYXMgYW55KS5jYWxjdWxhdGVDb25maWRlbmNlTGV2ZWwobG93Q29uZmlkZW5jZU1hdGNoZXMpO1xyXG4gICAgICBjb25zdCBoaWdoQ29uZmlkZW5jZSA9IChDb3JyZWxhdGVkRXZlbnRGYWN0b3J5IGFzIGFueSkuY2FsY3VsYXRlQ29uZmlkZW5jZUxldmVsKGhpZ2hDb25maWRlbmNlTWF0Y2hlcyk7XHJcblxyXG4gICAgICBleHBlY3QobG93Q29uZmlkZW5jZSkudG9CZShDb25maWRlbmNlTGV2ZWwuTE9XKTtcclxuICAgICAgZXhwZWN0KGhpZ2hDb25maWRlbmNlKS50b0JlKENvbmZpZGVuY2VMZXZlbC5WRVJZX0hJR0gpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjb3JyZWxhdGUgZXZlbnQgZGF0YSB3aXRoIHJ1bGUgdHlwZXMnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGVucmljaGVkRGF0YSA9IHtcclxuICAgICAgICBldmVudF90eXBlOiAnc2VjdXJpdHlfYWxlcnQnLFxyXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICAgIHNvdXJjZV9pcDogJzE5Mi4xNjguMS4xMDAnXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBydWxlVHlwZXMgPSBbQ29ycmVsYXRpb25SdWxlVHlwZS5URU1QT1JBTCwgQ29ycmVsYXRpb25SdWxlVHlwZS5TUEFUSUFMXTtcclxuXHJcbiAgICAgIGNvbnN0IGNvcnJlbGF0ZWREYXRhID0gKENvcnJlbGF0ZWRFdmVudEZhY3RvcnkgYXMgYW55KS5jb3JyZWxhdGVFdmVudERhdGEoZW5yaWNoZWREYXRhLCBydWxlVHlwZXMpO1xyXG5cclxuICAgICAgZXhwZWN0KGNvcnJlbGF0ZWREYXRhKS50b0VxdWFsKHtcclxuICAgICAgICAuLi5lbnJpY2hlZERhdGEsXHJcbiAgICAgICAgY29ycmVsYXRlZF9hdDogZXhwZWN0LmFueShTdHJpbmcpLFxyXG4gICAgICAgIGNvcnJlbGF0aW9uX3J1bGVfdHlwZXM6IHJ1bGVUeXBlc1xyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnZXJyb3IgaGFuZGxpbmcnLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIGhhbmRsZSBtaXNzaW5nIGVucmljaGVkIGV2ZW50JywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBvcHRpb25zOiBDcmVhdGVDb3JyZWxhdGVkRXZlbnRPcHRpb25zID0ge1xyXG4gICAgICAgIGVucmljaGVkRXZlbnQ6IG51bGwgYXMgYW55LFxyXG4gICAgICAgIGNvcnJlbGF0ZWREYXRhOiB7IGNvcnJlbGF0aW9uX2lkOiAnY29ycl8xMjMnIH1cclxuICAgICAgfTtcclxuXHJcbiAgICAgIGV4cGVjdCgoKSA9PiBDb3JyZWxhdGVkRXZlbnRGYWN0b3J5LmNyZWF0ZShvcHRpb25zKSkudG9UaHJvdygpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBoYW5kbGUgaW52YWxpZCBjb3JyZWxhdGlvbiBkYXRhJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBvcHRpb25zOiBDcmVhdGVDb3JyZWxhdGVkRXZlbnRPcHRpb25zID0ge1xyXG4gICAgICAgIGVucmljaGVkRXZlbnQ6IG1vY2tFbnJpY2hlZEV2ZW50LFxyXG4gICAgICAgIGNvcnJlbGF0ZWREYXRhOiBudWxsIGFzIGFueVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgZXhwZWN0KCgpID0+IENvcnJlbGF0ZWRFdmVudEZhY3RvcnkuY3JlYXRlKG9wdGlvbnMpKS50b1Rocm93KCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGhhbmRsZSBiYXRjaCBwcm9jZXNzaW5nIHdpdGggZW1wdHkgYXJyYXknLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGJhdGNoT3B0aW9uczogQmF0Y2hDb3JyZWxhdGlvbk9wdGlvbnMgPSB7XHJcbiAgICAgICAgZW5yaWNoZWRFdmVudHM6IFtdLFxyXG4gICAgICAgIHJ1bGVzOiBbXVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gQ29ycmVsYXRlZEV2ZW50RmFjdG9yeS5jcmVhdGVCYXRjaChiYXRjaE9wdGlvbnMpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzZnVsKS50b0hhdmVMZW5ndGgoMCk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuZmFpbGVkKS50b0hhdmVMZW5ndGgoMCk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuc3VtbWFyeS50b3RhbCkudG9CZSgwKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5zdW1tYXJ5LnN1Y2Nlc3NmdWwpLnRvQmUoMCk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuc3VtbWFyeS5mYWlsZWQpLnRvQmUoMCk7XHJcbiAgICB9KTtcclxuICB9KTtcclxufSk7Il0sInZlcnNpb24iOjN9