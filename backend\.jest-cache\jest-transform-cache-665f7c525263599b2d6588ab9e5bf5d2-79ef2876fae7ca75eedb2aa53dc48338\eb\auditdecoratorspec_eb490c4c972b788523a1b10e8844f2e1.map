{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\decorators\\audit.decorator.spec.ts", "mappings": ";;;;;;;;;;;AAAA,sEAA2G;AAC3G,iGAAgF;AAChF,mFAAkE;AAClE,uFAAsE;AAEtE,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,IAAI,eAAyC,CAAC;IAC9C,IAAI,SAAqB,CAAC;IAE1B,UAAU,CAAC,GAAG,EAAE;QACd,SAAS,GAAG,EAAE,CAAC;QACf,eAAe,GAAG;YAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,GAAa,EAAE,EAAE;gBAClD,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACpB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;YAC3B,CAAC,CAAC;SACH,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;;YACtD,MAAM,WAAW;gBAAjB;oBACE,gBAAW,GAAG,eAAe,CAAC;gBAMhC,CAAC;gBAHO,AAAN,KAAK,CAAC,UAAU,CAAC,KAAa;oBAC5B,OAAO,UAAU,KAAK,EAAE,CAAC;gBAC3B,CAAC;aACF;YAHO;gBADL,IAAA,uBAAK,EAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;;;oEACjC,OAAO,oBAAP,OAAO;yDAEvC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAEjD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACpC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAErD,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;;YAClD,MAAM,WAAW;gBAAjB;oBACE,gBAAW,GAAG,eAAe,CAAC;gBAMhC,CAAC;gBAHO,AAAN,KAAK,CAAC,aAAa;oBACjB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;gBAChC,CAAC;aACF;YAHO;gBADL,IAAA,uBAAK,EAAC,EAAE,SAAS,EAAE,mBAAmB,EAAE,CAAC;;;oEACnB,OAAO,oBAAP,OAAO;4DAE7B;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,MAAM,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACpE,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAErD,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;;YACtD,MAAM,MAAM,GAAG,6BAAM,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,iCAAQ,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,aAAa,GAAG,2CAAa,CAAC,MAAM,EAAE,CAAC;YAE7C,MAAM,WAAW;gBAAjB;oBACE,gBAAW,GAAG,eAAe,CAAC;gBAWhC,CAAC;gBAHO,AAAN,KAAK,CAAC,aAAa;oBACjB,wBAAwB;gBAC1B,CAAC;aACF;YAHO;gBANL,IAAA,uBAAK,EAAC;oBACL,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,SAAS,EAAE,mBAAmB;iBAC/B,CAAC;;;oEACqB,OAAO,oBAAP,OAAO;4DAE7B;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,OAAO,CAAC,aAAa,EAAE,CAAC;YAE9B,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;;YAC7D,MAAM,WAAW;gBACf,kBAAkB;gBAGZ,AAAN,KAAK,CAAC,mBAAmB;oBACvB,OAAO,SAAS,CAAC;gBACnB,CAAC;aACF;YAHO;gBADL,IAAA,uBAAK,EAAC,EAAE,SAAS,EAAE,qBAAqB,EAAE,CAAC;;;oEACf,OAAO,oBAAP,OAAO;kEAEnC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAEnD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,6CAA6C;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;;YAC5E,MAAM,WAAW;gBAAjB;oBACE,gBAAW,GAAG,eAAe,CAAC;gBAMhC,CAAC;gBAHO,AAAN,KAAK,CAAC,iBAAiB;oBACrB,wBAAwB;gBAC1B,CAAC;aACF;YAHO;gBADL,IAAA,uBAAK,GAAE;;;oEACmB,OAAO,oBAAP,OAAO;gEAEjC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAElC,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YACjE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;;YAC9C,MAAM,cAAc,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAElE,MAAM,WAAW;gBAAjB;oBACE,gBAAW,GAAG,eAAe,CAAC;gBAMhC,CAAC;gBAHO,AAAN,KAAK,CAAC,cAAc;oBAClB,wBAAwB;gBAC1B,CAAC;aACF;YAHO;gBADL,IAAA,uBAAK,EAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC;;;oEACZ,OAAO,oBAAP,OAAO;6DAE9B;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,OAAO,CAAC,cAAc,EAAE,CAAC;YAE/B,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;;YACxD,MAAM,WAAW;gBAAjB;oBACE,gBAAW,GAAG,eAAe,CAAC;gBAWhC,CAAC;gBARO,AAAN,KAAK,CAAC,iBAAiB,CACrB,GAAW,EACX,GAAW,EACX,GAAqB,EACrB,GAAa;oBAEb,wBAAwB;gBAC1B,CAAC;aACF;YARO;gBADL,IAAA,uBAAK,GAAE;;;oEAML,OAAO,oBAAP,OAAO;gEAET;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,OAAO,CAAC,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAE3E,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;;YACtD,MAAM,WAAW;gBAAjB;oBACE,gBAAW,GAAG,eAAe,CAAC;gBAMhC,CAAC;gBAHO,AAAN,KAAK,CAAC,YAAY;oBAChB,OAAO,eAAe,CAAC;gBACzB,CAAC;aACF;YAHO;gBADL,IAAA,6BAAW,EAAC,kBAAkB,CAAC;;;oEACV,OAAO,oBAAP,OAAO;2DAE5B;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,EAAE,CAAC;YAE5C,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACrC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAErD,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACpD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;;YACnE,MAAM,WAAW;gBAAjB;oBACE,gBAAW,GAAG,eAAe,CAAC;gBAMhC,CAAC;gBAHO,AAAN,KAAK,CAAC,mBAAmB;oBACvB,wBAAwB;gBAC1B,CAAC;aACF;YAHO;gBADL,IAAA,6BAAW,GAAE;;;oEACe,OAAO,oBAAP,OAAO;kEAEnC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAEpC,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;;YACrE,MAAM,aAAa,GAAgB;gBACjC,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;aAC9D,CAAC;YAEF,MAAM,WAAW;gBAAjB;oBACE,gBAAW,GAAG,aAAa,CAAC;gBAM9B,CAAC;gBAHO,AAAN,KAAK,CAAC,uBAAuB;oBAC3B,OAAO,eAAe,CAAC;gBACzB,CAAC;aACF;YAHO;gBADL,IAAA,uBAAK,GAAE;;;oEACyB,OAAO,oBAAP,OAAO;sEAEvC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,EAAE,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACrC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;;YAChE,MAAM,WAAW;gBAAjB;oBACE,gBAAW,GAAG,eAAe,CAAC;gBAMhC,CAAC;gBAHO,AAAN,KAAK,CAAC,gBAAgB;oBACpB,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBACpC,CAAC;aACF;YAHO;gBADL,IAAA,uBAAK,GAAE;;;oEACkB,OAAO,oBAAP,OAAO;+DAEhC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,MAAM,MAAM,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAE3E,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;;YAC5D,MAAM,WAAW;gBAAjB;oBACE,gBAAW,GAAG,eAAe,CAAC;gBAMhC,CAAC;gBAHO,AAAN,KAAK,CAAC,UAAU;oBACd,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACzD,CAAC;aACF;YAHO;gBADL,IAAA,uBAAK,GAAE;;;oEACY,OAAO,oBAAP,OAAO;yDAE1B;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,OAAO,CAAC,UAAU,EAAE,CAAC;YAE3B,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,sBAAsB;YAC5E,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\decorators\\audit.decorator.spec.ts"], "sourcesContent": ["import { <PERSON>t, SimpleAudit, AuditContext, AuditLogger, AuditLog } from '../../decorators/audit.decorator';\r\nimport { CorrelationId } from '../../value-objects/correlation-id.value-object';\r\nimport { UserId } from '../../value-objects/user-id.value-object';\r\nimport { TenantId } from '../../value-objects/tenant-id.value-object';\r\n\r\ndescribe('Audit Decorator', () => {\r\n  let mockAuditLogger: jest.Mocked<AuditLogger>;\r\n  let auditLogs: AuditLog[];\r\n\r\n  beforeEach(() => {\r\n    auditLogs = [];\r\n    mockAuditLogger = {\r\n      log: jest.fn().mockImplementation((log: AuditLog) => {\r\n        auditLogs.push(log);\r\n        return Promise.resolve();\r\n      }),\r\n    };\r\n  });\r\n\r\n  describe('@Audit decorator', () => {\r\n    it('should log successful method execution', async () => {\r\n      class TestService {\r\n        auditLogger = mockAuditLogger;\r\n\r\n        @Audit({ operation: 'test-operation', resource: 'test-resource' })\r\n        async testMethod(param: string): Promise<string> {\r\n          return `result-${param}`;\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      const result = await service.testMethod('input');\r\n\r\n      expect(result).toBe('result-input');\r\n      expect(mockAuditLogger.log).toHaveBeenCalledTimes(1);\r\n      \r\n      const auditLog = auditLogs[0];\r\n      expect(auditLog.operation).toBe('test-operation');\r\n      expect(auditLog.resource).toBe('test-resource');\r\n      expect(auditLog.status).toBe('SUCCESS');\r\n      expect(auditLog.duration).toBeGreaterThanOrEqual(0);\r\n      expect(auditLog.metadata?.args).toEqual(['input']);\r\n    });\r\n\r\n    it('should log failed method execution', async () => {\r\n      class TestService {\r\n        auditLogger = mockAuditLogger;\r\n\r\n        @Audit({ operation: 'failing-operation' })\r\n        async failingMethod(): Promise<void> {\r\n          throw new Error('Test error');\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      await expect(service.failingMethod()).rejects.toThrow('Test error');\r\n      expect(mockAuditLogger.log).toHaveBeenCalledTimes(1);\r\n      \r\n      const auditLog = auditLogs[0];\r\n      expect(auditLog.operation).toBe('failing-operation');\r\n      expect(auditLog.status).toBe('FAILURE');\r\n      expect(auditLog.error).toBe('Test error');\r\n    });\r\n\r\n    it('should include user and tenant context', async () => {\r\n      const userId = UserId.create();\r\n      const tenantId = TenantId.create();\r\n      const correlationId = CorrelationId.create();\r\n\r\n      class TestService {\r\n        auditLogger = mockAuditLogger;\r\n\r\n        @Audit({ \r\n          userId, \r\n          tenantId, \r\n          correlationId,\r\n          operation: 'context-operation' \r\n        })\r\n        async contextMethod(): Promise<void> {\r\n          // Method implementation\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      await service.contextMethod();\r\n\r\n      const auditLog = auditLogs[0];\r\n      expect(auditLog.userId).toBe(userId);\r\n      expect(auditLog.tenantId).toBe(tenantId);\r\n      expect(auditLog.correlationId).toBe(correlationId);\r\n    });\r\n\r\n    it('should handle missing audit logger gracefully', async () => {\r\n      class TestService {\r\n        // No audit logger\r\n\r\n        @Audit({ operation: 'no-logger-operation' })\r\n        async methodWithoutLogger(): Promise<string> {\r\n          return 'success';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      const result = await service.methodWithoutLogger();\r\n\r\n      expect(result).toBe('success');\r\n      // Should not throw error even without logger\r\n    });\r\n\r\n    it('should generate default operation name from class and method', async () => {\r\n      class TestService {\r\n        auditLogger = mockAuditLogger;\r\n\r\n        @Audit()\r\n        async defaultNameMethod(): Promise<void> {\r\n          // Method implementation\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      await service.defaultNameMethod();\r\n\r\n      const auditLog = auditLogs[0];\r\n      expect(auditLog.operation).toBe('TestService.defaultNameMethod');\r\n      expect(auditLog.resource).toBe('TestService');\r\n    });\r\n\r\n    it('should include custom metadata', async () => {\r\n      const customMetadata = { customField: 'customValue', number: 42 };\r\n\r\n      class TestService {\r\n        auditLogger = mockAuditLogger;\r\n\r\n        @Audit({ metadata: customMetadata })\r\n        async metadataMethod(): Promise<void> {\r\n          // Method implementation\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      await service.metadataMethod();\r\n\r\n      const auditLog = auditLogs[0];\r\n      expect(auditLog.metadata).toMatchObject(customMetadata);\r\n    });\r\n\r\n    it('should handle complex arguments properly', async () => {\r\n      class TestService {\r\n        auditLogger = mockAuditLogger;\r\n\r\n        @Audit()\r\n        async complexArgsMethod(\r\n          str: string, \r\n          num: number, \r\n          obj: { prop: string }, \r\n          arr: number[]\r\n        ): Promise<void> {\r\n          // Method implementation\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      await service.complexArgsMethod('test', 123, { prop: 'value' }, [1, 2, 3]);\r\n\r\n      const auditLog = auditLogs[0];\r\n      expect(auditLog.metadata?.args).toEqual(['test', 123, '[Object]', '[Object]']);\r\n    });\r\n  });\r\n\r\n  describe('@SimpleAudit decorator', () => {\r\n    it('should work with custom operation name', async () => {\r\n      class TestService {\r\n        auditLogger = mockAuditLogger;\r\n\r\n        @SimpleAudit('simple-operation')\r\n        async simpleMethod(): Promise<string> {\r\n          return 'simple-result';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      const result = await service.simpleMethod();\r\n\r\n      expect(result).toBe('simple-result');\r\n      expect(mockAuditLogger.log).toHaveBeenCalledTimes(1);\r\n      \r\n      const auditLog = auditLogs[0];\r\n      expect(auditLog.operation).toBe('simple-operation');\r\n      expect(auditLog.status).toBe('SUCCESS');\r\n    });\r\n\r\n    it('should use default operation name when not provided', async () => {\r\n      class TestService {\r\n        auditLogger = mockAuditLogger;\r\n\r\n        @SimpleAudit()\r\n        async defaultSimpleMethod(): Promise<void> {\r\n          // Method implementation\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      await service.defaultSimpleMethod();\r\n\r\n      const auditLog = auditLogs[0];\r\n      expect(auditLog.operation).toBe('TestService.defaultSimpleMethod');\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should continue execution even if audit logging fails', async () => {\r\n      const failingLogger: AuditLogger = {\r\n        log: jest.fn().mockRejectedValue(new Error('Logging failed')),\r\n      };\r\n\r\n      class TestService {\r\n        auditLogger = failingLogger;\r\n\r\n        @Audit()\r\n        async methodWithFailingLogger(): Promise<string> {\r\n          return 'method-result';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      const result = await service.methodWithFailingLogger();\r\n\r\n      expect(result).toBe('method-result');\r\n      expect(failingLogger.log).toHaveBeenCalledTimes(1);\r\n    });\r\n\r\n    it('should preserve original error when method fails', async () => {\r\n      class TestService {\r\n        auditLogger = mockAuditLogger;\r\n\r\n        @Audit()\r\n        async methodThatThrows(): Promise<void> {\r\n          throw new Error('Original error');\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      await expect(service.methodThatThrows()).rejects.toThrow('Original error');\r\n      \r\n      const auditLog = auditLogs[0];\r\n      expect(auditLog.status).toBe('FAILURE');\r\n      expect(auditLog.error).toBe('Original error');\r\n    });\r\n  });\r\n\r\n  describe('performance', () => {\r\n    it('should measure execution duration accurately', async () => {\r\n      class TestService {\r\n        auditLogger = mockAuditLogger;\r\n\r\n        @Audit()\r\n        async slowMethod(): Promise<void> {\r\n          await new Promise(resolve => setTimeout(resolve, 100));\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      await service.slowMethod();\r\n\r\n      const auditLog = auditLogs[0];\r\n      expect(auditLog.duration).toBeGreaterThanOrEqual(90); // Allow some variance\r\n      expect(auditLog.duration).toBeLessThan(200);\r\n    });\r\n  });\r\n});"], "version": 3}