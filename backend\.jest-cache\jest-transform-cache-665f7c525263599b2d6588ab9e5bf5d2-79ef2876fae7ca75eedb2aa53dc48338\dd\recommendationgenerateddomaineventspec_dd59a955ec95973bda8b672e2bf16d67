1025664ed17047e18f02ca6b9e8d3cb3
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const recommendation_generated_domain_event_1 = require("../recommendation-generated.domain-event");
const shared_kernel_1 = require("../../../../../shared-kernel");
const action_type_enum_1 = require("../../enums/action-type.enum");
describe('RecommendationGeneratedDomainEvent', () => {
    let aggregateId;
    let eventData;
    beforeEach(() => {
        aggregateId = shared_kernel_1.UniqueEntityId.generate();
        eventData = {
            recommendationId: 'rec-001',
            recommendationType: 'security_action',
            title: 'Implement Multi-Factor Authentication',
            description: 'Deploy MFA across all critical systems to enhance security posture',
            priority: 'high',
            confidenceScore: 85,
            riskReductionPotential: 75,
            implementationEffort: 'medium',
            estimatedImplementationTime: 24,
            costEstimate: {
                amount: 15000,
                currency: 'USD',
                category: 'medium'
            },
            source: {
                type: 'ai_analysis',
                identifier: 'security-ai-engine',
                version: '2.1.0'
            },
            context: {
                triggerType: 'vulnerability_found',
                triggerIds: ['vuln-001', 'vuln-002'],
                relatedAssets: ['asset-001', 'asset-002'],
                affectedSystems: ['auth-system', 'user-portal']
            },
            recommendedActions: [
                {
                    actionType: action_type_enum_1.ActionType.UPDATE_CONFIGURATION,
                    description: 'Configure MFA settings in authentication system',
                    priority: 'high',
                    estimatedDuration: 120,
                    prerequisites: ['admin_access', 'maintenance_window'],
                    risks: ['temporary_service_disruption']
                },
                {
                    actionType: action_type_enum_1.ActionType.ENABLE_SERVICE,
                    description: 'Enable MFA enforcement for all users',
                    priority: 'medium',
                    estimatedDuration: 60,
                    prerequisites: ['user_notification', 'training_completion']
                }
            ],
            successCriteria: [
                'MFA enabled for 100% of privileged accounts',
                'MFA adoption rate >95% within 30 days',
                'Zero authentication-related incidents post-implementation'
            ],
            kpis: [
                {
                    name: 'MFA Adoption Rate',
                    description: 'Percentage of users with MFA enabled',
                    targetValue: '95%',
                    measurementMethod: 'Authentication system metrics'
                },
                {
                    name: 'Authentication Incidents',
                    description: 'Number of authentication-related security incidents',
                    targetValue: '0',
                    measurementMethod: 'Security incident tracking'
                }
            ],
            complianceFrameworks: ['SOC2', 'ISO27001', 'NIST'],
            businessJustification: {
                riskMitigation: 'Reduces risk of credential-based attacks by 80%',
                businessValue: 'Protects customer data and maintains regulatory compliance',
                costBenefit: 'ROI of 300% through prevented security incidents'
            },
            timeline: {
                recommendedStartDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
                recommendedCompletionDate: new Date(Date.now() + 37 * 24 * 60 * 60 * 1000), // 5+ weeks from now
                milestones: [
                    {
                        name: 'Planning Phase',
                        description: 'Complete planning and resource allocation',
                        targetDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
                        dependencies: ['budget_approval', 'team_assignment']
                    },
                    {
                        name: 'Implementation Phase',
                        description: 'Deploy MFA infrastructure',
                        targetDate: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),
                        dependencies: ['planning_complete']
                    }
                ]
            },
            stakeholders: [
                {
                    role: 'security_team',
                    responsibility: 'Technical implementation and configuration',
                    required: true
                },
                {
                    role: 'it_operations',
                    responsibility: 'Infrastructure support and monitoring',
                    required: true
                },
                {
                    role: 'user_training',
                    responsibility: 'User education and support',
                    required: false
                }
            ],
            alternatives: [
                {
                    title: 'Single Sign-On (SSO) Implementation',
                    description: 'Implement SSO with built-in MFA capabilities',
                    pros: ['Simplified user experience', 'Centralized management'],
                    cons: ['Higher initial cost', 'Vendor dependency'],
                    effort: 'high',
                    riskReduction: 70
                }
            ],
            dependencies: ['budget_approval', 'maintenance_window', 'user_communication'],
            implementationRisks: [
                {
                    risk: 'User resistance to MFA adoption',
                    likelihood: 'medium',
                    impact: 'medium',
                    mitigation: 'Comprehensive training and gradual rollout'
                },
                {
                    risk: 'Technical integration issues',
                    likelihood: 'low',
                    impact: 'high',
                    mitigation: 'Thorough testing in staging environment'
                }
            ],
            validationRequirements: [
                'Penetration testing of MFA implementation',
                'User acceptance testing',
                'Performance impact assessment'
            ],
            rollbackPlan: {
                description: 'Disable MFA enforcement and revert to previous authentication method',
                steps: [
                    'Disable MFA enforcement in authentication system',
                    'Notify users of temporary reversion',
                    'Investigate and resolve issues',
                    'Plan re-implementation'
                ],
                estimatedTime: 30
            }
        };
    });
    describe('constructor', () => {
        it('should create event with valid data', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.aggregateId).toEqual(aggregateId);
            expect(event.recommendationId).toBe('rec-001');
            expect(event.title).toBe('Implement Multi-Factor Authentication');
            expect(event.priority).toBe('high');
            expect(event.confidenceScore).toBe(85);
            expect(event.riskReductionPotential).toBe(75);
        });
        it('should set correct metadata', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.metadata.eventType).toBe('RecommendationGenerated');
            expect(event.metadata.domain).toBe('Security');
            expect(event.metadata.aggregateType).toBe('Recommendation');
            expect(event.metadata.processingStage).toBe('generation');
        });
    });
    describe('priority checks', () => {
        it('should identify critical priority recommendations', () => {
            const criticalData = { ...eventData, priority: 'critical' };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, criticalData);
            expect(event.isCriticalPriority()).toBe(true);
            expect(event.isHighPriorityOrAbove()).toBe(true);
        });
        it('should identify high priority recommendations', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.isCriticalPriority()).toBe(false);
            expect(event.isHighPriorityOrAbove()).toBe(true);
        });
        it('should identify medium priority recommendations', () => {
            const mediumData = { ...eventData, priority: 'medium' };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, mediumData);
            expect(event.isHighPriorityOrAbove()).toBe(false);
        });
    });
    describe('confidence checks', () => {
        it('should identify high confidence recommendations', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.hasHighConfidence()).toBe(true);
            expect(event.hasLowConfidence()).toBe(false);
        });
        it('should identify low confidence recommendations', () => {
            const lowConfidenceData = { ...eventData, confidenceScore: 45 };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, lowConfidenceData);
            expect(event.hasHighConfidence()).toBe(false);
            expect(event.hasLowConfidence()).toBe(true);
        });
    });
    describe('risk reduction checks', () => {
        it('should identify high risk reduction potential', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.hasHighRiskReduction()).toBe(true);
        });
        it('should identify low risk reduction potential', () => {
            const lowRiskData = { ...eventData, riskReductionPotential: 45 };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, lowRiskData);
            expect(event.hasHighRiskReduction()).toBe(false);
        });
    });
    describe('effort checks', () => {
        it('should identify low effort recommendations', () => {
            const lowEffortData = { ...eventData, implementationEffort: 'low' };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, lowEffortData);
            expect(event.isLowEffort()).toBe(true);
            expect(event.isHighEffort()).toBe(false);
        });
        it('should identify high effort recommendations', () => {
            const highEffortData = { ...eventData, implementationEffort: 'high' };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, highEffortData);
            expect(event.isLowEffort()).toBe(false);
            expect(event.isHighEffort()).toBe(true);
        });
    });
    describe('implementation time checks', () => {
        it('should identify quick implementation recommendations', () => {
            const quickData = { ...eventData, estimatedImplementationTime: 6 };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, quickData);
            expect(event.isQuickImplementation()).toBe(true);
            expect(event.isLongImplementation()).toBe(false);
        });
        it('should identify long implementation recommendations', () => {
            const longData = { ...eventData, estimatedImplementationTime: 50 };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, longData);
            expect(event.isQuickImplementation()).toBe(false);
            expect(event.isLongImplementation()).toBe(true);
        });
    });
    describe('cost checks', () => {
        it('should identify recommendations with cost estimates', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.hasCostEstimate()).toBe(true);
        });
        it('should identify high cost recommendations', () => {
            const highCostData = {
                ...eventData,
                costEstimate: { ...eventData.costEstimate, category: 'high' }
            };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, highCostData);
            expect(event.isHighCost()).toBe(true);
        });
        it('should handle recommendations without cost estimates', () => {
            const noCostData = { ...eventData };
            delete noCostData.costEstimate;
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, noCostData);
            expect(event.hasCostEstimate()).toBe(false);
            expect(event.isHighCost()).toBe(false);
        });
    });
    describe('compliance checks', () => {
        it('should identify compliance-related recommendations', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.addressesCompliance()).toBe(true);
        });
        it('should handle recommendations without compliance frameworks', () => {
            const noComplianceData = { ...eventData };
            delete noComplianceData.complianceFrameworks;
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, noComplianceData);
            expect(event.addressesCompliance()).toBe(false);
        });
    });
    describe('alternatives and dependencies', () => {
        it('should identify recommendations with alternatives', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.hasAlternatives()).toBe(true);
        });
        it('should identify recommendations with dependencies', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.hasDependencies()).toBe(true);
        });
        it('should identify recommendations with high implementation risks', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.hasHighImplementationRisks()).toBe(false); // Current risks are medium/low
        });
        it('should identify recommendations with rollback plans', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.hasRollbackPlan()).toBe(true);
        });
    });
    describe('source and trigger checks', () => {
        it('should identify AI-generated recommendations', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.isAIGenerated()).toBe(true);
        });
        it('should identify vulnerability-triggered recommendations', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.isTriggeredByVulnerability()).toBe(true);
            expect(event.isTriggeredByThreat()).toBe(false);
            expect(event.isTriggeredByIncident()).toBe(false);
        });
        it('should identify threat-triggered recommendations', () => {
            const threatData = {
                ...eventData,
                context: { ...eventData.context, triggerType: 'threat_detected' }
            };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, threatData);
            expect(event.isTriggeredByThreat()).toBe(true);
            expect(event.isTriggeredByVulnerability()).toBe(false);
        });
    });
    describe('value score calculation', () => {
        it('should calculate value score correctly', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            const valueScore = event.getValueScore();
            expect(valueScore).toBeGreaterThan(0);
            expect(valueScore).toBeLessThanOrEqual(100);
            // With high risk reduction (75), high confidence (85), medium effort, high priority
            // Should result in a good value score
            expect(valueScore).toBeGreaterThan(70);
        });
        it('should calculate lower value score for low confidence recommendations', () => {
            const lowValueData = {
                ...eventData,
                confidenceScore: 30,
                riskReductionPotential: 40,
                implementationEffort: 'high',
                priority: 'low'
            };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, lowValueData);
            const valueScore = event.getValueScore();
            expect(valueScore).toBeLessThan(50);
        });
    });
    describe('implementation urgency', () => {
        it('should set appropriate urgency for critical priority', () => {
            const criticalData = { ...eventData, priority: 'critical' };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, criticalData);
            expect(event.getImplementationUrgency()).toBe(1); // 1 day
        });
        it('should set appropriate urgency for high priority', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            expect(event.getImplementationUrgency()).toBe(7); // 1 week
        });
        it('should set appropriate urgency for medium priority', () => {
            const mediumData = { ...eventData, priority: 'medium' };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, mediumData);
            expect(event.getImplementationUrgency()).toBe(30); // 1 month
        });
        it('should set appropriate urgency for low priority', () => {
            const lowData = { ...eventData, priority: 'low' };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, lowData);
            expect(event.getImplementationUrgency()).toBe(90); // 3 months
        });
    });
    describe('approval requirements', () => {
        it('should require executive approval for critical priority', () => {
            const criticalData = { ...eventData, priority: 'critical' };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, criticalData);
            const approval = event.getApprovalRequirements();
            expect(approval.requiresApproval).toBe(true);
            expect(approval.approvalLevel).toBe('executive');
            expect(approval.requiredApprovers).toContain('security_director');
            expect(approval.requiredApprovers).toContain('ciso');
        });
        it('should require director approval for high priority', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            const approval = event.getApprovalRequirements();
            expect(approval.requiresApproval).toBe(true);
            expect(approval.approvalLevel).toBe('director');
            expect(approval.requiredApprovers).toContain('security_director');
        });
        it('should include compliance officer for compliance recommendations', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            const approval = event.getApprovalRequirements();
            expect(approval.requiredApprovers).toContain('compliance_officer');
        });
    });
    describe('notification targets', () => {
        it('should include appropriate targets for high priority recommendations', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            const targets = event.getNotificationTargets();
            expect(targets).toContain('security_team');
            expect(targets).toContain('security_managers');
            expect(targets).toContain('compliance_team');
            expect(targets).toContain('security_team'); // From stakeholders
            expect(targets).toContain('it_operations'); // From stakeholders
        });
        it('should include threat analysts for threat-triggered recommendations', () => {
            const threatData = {
                ...eventData,
                context: { ...eventData.context, triggerType: 'threat_detected' }
            };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, threatData);
            const targets = event.getNotificationTargets();
            expect(targets).toContain('threat_analysts');
        });
    });
    describe('complexity level', () => {
        it('should calculate complexity correctly', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            const complexity = event.getComplexityLevel();
            // Medium effort + dependencies + 2 actions + 3 stakeholders = moderate complexity
            expect(complexity).toBe('moderate');
        });
        it('should identify simple recommendations', () => {
            const simpleData = {
                ...eventData,
                implementationEffort: 'low',
                dependencies: [],
                recommendedActions: [eventData.recommendedActions[0]],
                stakeholders: [eventData.stakeholders[0]],
                implementationRisks: []
            };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, simpleData);
            const complexity = event.getComplexityLevel();
            expect(complexity).toBe('simple');
        });
    });
    describe('recommendation metrics', () => {
        it('should generate comprehensive metrics', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            const metrics = event.getRecommendationMetrics();
            expect(metrics.recommendationId).toBe('rec-001');
            expect(metrics.type).toBe('security_action');
            expect(metrics.priority).toBe('high');
            expect(metrics.confidenceScore).toBe(85);
            expect(metrics.riskReductionPotential).toBe(75);
            expect(metrics.valueScore).toBeGreaterThan(0);
            expect(metrics.implementationEffort).toBe('medium');
            expect(metrics.estimatedHours).toBe(24);
            expect(metrics.complexityLevel).toBe('moderate');
            expect(metrics.hasAlternatives).toBe(true);
            expect(metrics.hasDependencies).toBe(true);
            expect(metrics.addressesCompliance).toBe(true);
            expect(metrics.requiresApproval).toBe(true);
            expect(metrics.implementationUrgency).toBe(7);
        });
    });
    describe('integration event conversion', () => {
        it('should convert to integration event format', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            const integrationEvent = event.toIntegrationEvent();
            expect(integrationEvent.eventType).toBe('RecommendationGenerated');
            expect(integrationEvent.version).toBe('1.0');
            expect(integrationEvent.data.recommendationId).toBe('rec-001');
            expect(integrationEvent.data.recommendation.title).toBe('Implement Multi-Factor Authentication');
            expect(integrationEvent.data.recommendation.priority).toBe('high');
            expect(integrationEvent.data.implementation.effort).toBe('medium');
            expect(integrationEvent.data.source.type).toBe('ai_analysis');
            expect(integrationEvent.data.context.triggerType).toBe('vulnerability_found');
            expect(integrationEvent.data.business.addressesCompliance).toBe(true);
            expect(integrationEvent.data.approval.requiresApproval).toBe(true);
            expect(integrationEvent.data.actions.count).toBe(2);
        });
    });
    describe('JSON serialization', () => {
        it('should serialize to JSON correctly', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            const json = event.toJSON();
            expect(json.eventData).toEqual(eventData);
            expect(json.analysis.isHighPriorityOrAbove).toBe(true);
            expect(json.analysis.hasHighConfidence).toBe(true);
            expect(json.analysis.addressesCompliance).toBe(true);
            expect(json.analysis.valueScore).toBeGreaterThan(0);
        });
        it('should deserialize from JSON correctly', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            const json = event.toJSON();
            const deserializedEvent = recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent.fromJSON(json);
            expect(deserializedEvent.aggregateId.equals(event.aggregateId)).toBe(true);
            expect(deserializedEvent.recommendationId).toBe(event.recommendationId);
            expect(deserializedEvent.title).toBe(event.title);
            expect(deserializedEvent.priority).toBe(event.priority);
        });
    });
    describe('human-readable description', () => {
        it('should generate appropriate description', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            const description = event.getDescription();
            expect(description).toContain('HIGH priority');
            expect(description).toContain('security_action');
            expect(description).toContain('Implement Multi-Factor Authentication');
            expect(description).toContain('high confidence');
            expect(description).toContain('medium effort');
            expect(description).toContain('vulnerability found');
        });
    });
    describe('event summary', () => {
        it('should generate comprehensive summary', () => {
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, eventData);
            const summary = event.getSummary();
            expect(summary.eventType).toBe('RecommendationGenerated');
            expect(summary.recommendationId).toBe('rec-001');
            expect(summary.type).toBe('security_action');
            expect(summary.title).toBe('Implement Multi-Factor Authentication');
            expect(summary.priority).toBe('high');
            expect(summary.confidenceScore).toBe(85);
            expect(summary.riskReductionPotential).toBe(75);
            expect(summary.implementationEffort).toBe('medium');
            expect(summary.triggerType).toBe('vulnerability_found');
            expect(summary.requiresApproval).toBe(true);
        });
    });
    describe('edge cases', () => {
        it('should handle recommendations without alternatives', () => {
            const noAlternativesData = { ...eventData };
            delete noAlternativesData.alternatives;
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, noAlternativesData);
            expect(event.hasAlternatives()).toBe(false);
        });
        it('should handle recommendations without dependencies', () => {
            const noDependenciesData = { ...eventData, dependencies: [] };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, noDependenciesData);
            expect(event.hasDependencies()).toBe(false);
        });
        it('should handle recommendations without rollback plan', () => {
            const noRollbackData = { ...eventData };
            delete noRollbackData.rollbackPlan;
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, noRollbackData);
            expect(event.hasRollbackPlan()).toBe(false);
        });
        it('should handle high implementation risks', () => {
            const highRiskData = {
                ...eventData,
                implementationRisks: [
                    {
                        risk: 'System failure',
                        likelihood: 'high',
                        impact: 'high',
                        mitigation: 'Comprehensive testing'
                    }
                ]
            };
            const event = new recommendation_generated_domain_event_1.RecommendationGeneratedDomainEvent(aggregateId, highRiskData);
            expect(event.hasHighImplementationRisks()).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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