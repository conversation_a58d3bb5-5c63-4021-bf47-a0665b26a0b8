{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\correlated-event.specification.spec.ts", "mappings": ";;AAAA,sFA+B2C;AAC3C,oFASgD;AAChD,iFAAwE;AACxE,gEAA8D;AAC9D,gHAA+F;AAC/F,iEAAwD;AACxD,yEAAgE;AAChE,qEAA4D;AAC5D,2FAAiF;AACjF,6EAAoE;AACpE,kHAAiG;AACjG,4GAA2F;AAE3F,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;IAC9C,IAAI,iBAAgC,CAAC;IACrC,IAAI,SAA+B,CAAC;IAEpC,UAAU,CAAC,GAAG,EAAE;QACd,6BAA6B;QAC7B,iBAAiB,GAAG,2CAAa,CAAC,MAAM,CAAC;YACvC,SAAS,EAAE,6CAAc,CAAC,GAAG,EAAE;YAC/B,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;gBACzB,IAAI,EAAE,MAAM;gBACZ,UAAU,EAAE,eAAe;gBAC3B,IAAI,EAAE,kBAAkB;aACzB,CAAC;YACF,cAAc,EAAE;gBACd,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,kBAAkB,EAAE,GAAG;gBACvB,OAAO,EAAE,OAAO;aACjB;SACF,CAAC,CAAC;QAEH,SAAS,GAAG;YACV,eAAe,EAAE,8BAAc,CAAC,MAAM,EAAE;YACxC,QAAQ,EAAE,iBAAiB;YAC3B,IAAI,EAAE,2BAAS,CAAC,cAAc;YAC9B,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,MAAM,EAAE,+BAAW,CAAC,MAAM;YAC1B,gBAAgB,EAAE,oDAAqB,CAAC,UAAU;YAClD,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;YAC9C,YAAY,EAAE;gBACZ,cAAc,EAAE,YAAY;gBAC5B,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,UAAU;gBAC1B,cAAc,EAAE,CAAC;aAClB;YACD,KAAK,EAAE,gCAAgC;YACvC,eAAe,EAAE,uCAAe,CAAC,IAAI;YACrC,aAAa,EAAE,eAAe;YAC9B,aAAa,EAAE,EAAE;YACjB,YAAY,EAAE,EAAE;YAChB,kBAAkB,EAAE,EAAE;YACtB,eAAe,EAAE,EAAE;YACnB,mBAAmB,EAAE,CAAC,mBAAmB,CAAC;YAC1C,uBAAuB,EAAE,EAAE;SAC5B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,IAAI,GAAG,IAAI,kEAAiC,EAAE,CAAC;YACrD,MAAM,cAAc,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC5C,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;aAC/C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,IAAI,GAAG,IAAI,kEAAiC,EAAE,CAAC;YACrD,MAAM,YAAY,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,OAAO;aAC7C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,IAAI,GAAG,IAAI,+DAA8B,EAAE,CAAC;YAClD,MAAM,WAAW,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,MAAM;aAC5C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,IAAI,GAAG,IAAI,+DAA8B,EAAE,CAAC;YAClD,MAAM,cAAc,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC5C,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;aAC/C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,IAAI,GAAG,IAAI,mEAAkC,EAAE,CAAC;YACtD,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC7C,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,WAAW;aACjD,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,IAAI,GAAG,IAAI,gEAA+B,EAAE,CAAC;YACnD,MAAM,YAAY,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,OAAO;aAC7C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,IAAI,GAAG,IAAI,oEAAmC,EAAE,CAAC;YACvD,MAAM,gBAAgB,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC9C,GAAG,SAAS;gBACZ,uBAAuB,EAAE,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,IAAI,GAAG,IAAI,oEAAmC,EAAE,CAAC;YACvD,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC7C,GAAG,SAAS;gBACZ,uBAAuB,EAAE,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,IAAI,GAAG,IAAI,iEAAgC,EAAE,CAAC;YACpD,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC7C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;aACzC,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,IAAI,GAAG,IAAI,iEAAgC,EAAE,CAAC;YACpD,MAAM,kBAAkB,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAChD,GAAG,SAAS;gBACZ,gBAAgB,EAAE,EAAE;aACrB,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,IAAI,GAAG,IAAI,kEAAiC,EAAE,CAAC;YACrD,MAAM,WAAW,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,oBAAoB,EAAE,IAAI;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,IAAI,GAAG,IAAI,kEAAiC,EAAE,CAAC;YACrD,MAAM,aAAa,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC3C,GAAG,SAAS;gBACZ,oBAAoB,EAAE,KAAK;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,IAAI,GAAG,IAAI,+DAA8B,EAAE,CAAC;YAClD,MAAM,UAAU,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACxC,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;gBAC9C,uBAAuB,EAAE,EAAE;gBAC3B,gBAAgB,EAAE,EAAE;gBACpB,oBAAoB,EAAE,KAAK;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,IAAI,GAAG,IAAI,+DAA8B,EAAE,CAAC;YAClD,MAAM,aAAa,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC3C,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,MAAM;gBAC3C,uBAAuB,EAAE,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wCAAwC,EAAE,GAAG,EAAE;QACtD,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,IAAI,GAAG,IAAI,uEAAsC,EAAE,CAAC;YAC1D,MAAM,mBAAmB,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACjD,GAAG,SAAS;gBACZ,iBAAiB,EAAE;oBACjB,OAAO,EAAE,IAAI;oBACb,YAAY,EAAE,EAAE;oBAChB,WAAW,EAAE,EAAE;oBACf,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,EAAE;oBACV,oBAAoB,EAAE,IAAI;oBAC1B,eAAe,EAAE,EAAE;oBACnB,SAAS,EAAE,CAAC;oBACZ,YAAY,EAAE,CAAC;oBACf,kBAAkB,EAAE,EAAE;iBACvB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,IAAI,GAAG,IAAI,4DAA2B,EAAE,CAAC;YAC/C,MAAM,WAAW,GAAgB;gBAC/B,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,yCAAyC;gBACtD,MAAM,EAAE;oBACN;wBACE,EAAE,EAAE,SAAS;wBACb,IAAI,EAAE,gBAAgB;wBACtB,WAAW,EAAE,oBAAoB;wBACjC,QAAQ,EAAE,CAAC,8BAAc,CAAC,MAAM,EAAE,CAAC;wBACnC,KAAK,EAAE,CAAC;wBACR,UAAU,EAAE,uCAAe,CAAC,IAAI;wBAChC,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB;iBACF;gBACD,UAAU,EAAE,uCAAe,CAAC,IAAI;gBAChC,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,QAAQ,EAAE;oBACR,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC;oBACzC,OAAO,EAAE,IAAI,IAAI,EAAE;oBACnB,QAAQ,EAAE,OAAO;iBAClB;aACF,CAAC;YAEF,MAAM,oBAAoB,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAClD,GAAG,SAAS;gBACZ,WAAW;aACZ,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,IAAI,GAAG,IAAI,4DAA2B,EAAE,CAAC;YAC/C,MAAM,uBAAuB,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAElE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,IAAI,GAAG,IAAI,oEAAmC,EAAE,CAAC;YACvD,MAAM,iBAAiB,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC/C,GAAG,SAAS;gBACZ,mBAAmB,EAAE;oBACnB,UAAU,EAAE,OAAO;oBACnB,aAAa,EAAE,CAAC,8BAAc,CAAC,MAAM,EAAE,CAAC;oBACxC,WAAW,EAAE,UAAU;oBACvB,UAAU,EAAE,EAAE;iBACf;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,IAAI,GAAG,IAAI,mEAAkC,EAAE,CAAC;YACtD,MAAM,gBAAgB,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC9C,GAAG,SAAS;gBACZ,kBAAkB,EAAE;oBAClB,SAAS,EAAE,CAAC,eAAe,CAAC;oBAC5B,SAAS,EAAE,CAAC,WAAW,CAAC;oBACxB,eAAe,EAAE,CAAC,gBAAgB,CAAC;oBACnC,iBAAiB,EAAE,CAAC,SAAS,CAAC;oBAC9B,UAAU,EAAE,EAAE;iBACf;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uCAAuC,EAAE,GAAG,EAAE;QACrD,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,IAAI,GAAG,IAAI,sEAAqC,EAAE,CAAC;YACzD,MAAM,mBAAmB,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACjD,GAAG,SAAS;gBACZ,qBAAqB,EAAE;oBACrB,YAAY,EAAE,CAAC,eAAe,CAAC;oBAC/B,cAAc,EAAE,CAAC,gBAAgB,CAAC;oBAClC,YAAY,EAAE,EAAE;oBAChB,iBAAiB,EAAE,GAAG;oBACtB,UAAU,EAAE,EAAE;iBACf;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,IAAI,GAAG,IAAI,+DAA8B,CAAC;gBAC9C,2CAAiB,CAAC,SAAS;gBAC3B,2CAAiB,CAAC,OAAO;aAC1B,CAAC,CAAC;YACH,MAAM,cAAc,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC5C,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;aAC/C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,IAAI,GAAG,IAAI,+DAA8B,CAAC,CAAC,2CAAiB,CAAC,SAAS,CAAC,CAAC,CAAC;YAC/E,MAAM,WAAW,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,MAAM;aAC5C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,IAAI,GAAG,IAAI,6DAA4B,CAAC;gBAC5C,uCAAe,CAAC,IAAI;gBACpB,uCAAe,CAAC,SAAS;aAC1B,CAAC,CAAC;YACH,MAAM,mBAAmB,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACjD,GAAG,SAAS;gBACZ,eAAe,EAAE,uCAAe,CAAC,IAAI;aACtC,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2CAA2C,EAAE,GAAG,EAAE;QACzD,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,IAAI,GAAG,IAAI,0EAAyC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACnE,MAAM,YAAY,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,uBAAuB,EAAE,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,IAAI,GAAG,IAAI,0EAAyC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACnE,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC7C,GAAG,SAAS;gBACZ,uBAAuB,EAAE,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,IAAI,GAAG,IAAI,0EAAyC,CAAC,EAAE,CAAC,CAAC;YAC/D,MAAM,YAAY,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,uBAAuB,EAAE,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,IAAI,GAAG,IAAI,0EAAyC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAC1E,MAAM,YAAY,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,uBAAuB,EAAE,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,IAAI,GAAoB;gBAC5B,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,yBAAyB;gBACtC,IAAI,EAAE,6CAAmB,CAAC,QAAQ;gBAClC,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,KAAK;gBACf,YAAY,EAAE,OAAO;gBACrB,aAAa,EAAE,EAAE;aAClB,CAAC;YAEF,MAAM,IAAI,GAAG,IAAI,yDAAwB,CAAC,aAAa,CAAC,CAAC;YACzD,MAAM,aAAa,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC3C,GAAG,SAAS;gBACZ,YAAY,EAAE,CAAC,IAAI,CAAC;aACrB,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,mEAAmE,EAAE,GAAG,EAAE;YAC3E,MAAM,eAAe,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAChD,MAAM,IAAI,GAAG,IAAI,2DAA0B,CAAC,eAAe,CAAC,CAAC;YAC7D,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,eAAe;aAChB,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,+CAA+C,eAAe,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAClH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,aAAa,GAAG,eAAe,CAAC;YACtC,MAAM,IAAI,GAAG,IAAI,2DAA0B,CAAC,aAAa,CAAC,CAAC;YAC3D,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,aAAa;aACd,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,IAAI,GAAG,IAAI,8DAA6B,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,eAAe,EAAE,CAAC,8BAAc,CAAC,MAAM,EAAE,EAAE,8BAAc,CAAC,MAAM,EAAE,EAAE,8BAAc,CAAC,MAAM,EAAE,CAAC;aAC7F,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,IAAI,GAAG,IAAI,8DAA6B,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,eAAe,EAAE,CAAC,8BAAc,CAAC,MAAM,EAAE,CAAC;aAC3C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,IAAI,GAAG,IAAI,4DAA2B,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,aAAa,EAAE,CAAC,8BAAc,CAAC,MAAM,EAAE,CAAC;aACzC,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,IAAI,GAAG,IAAI,gEAA+B,CAAC,CAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC,CAAC;YACzF,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,CAAC;aACjE,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;QACxG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,KAAK,GAAqB;gBAC9B,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;gBAChC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,QAAQ;gBACxC,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,GAAG;aACZ,CAAC;YAEF,MAAM,IAAI,GAAG,IAAI,uDAAsB,CAAC,CAAC,8CAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC;YACzE,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,kBAAkB,EAAE,CAAC,KAAK,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,IAAI,GAAG,IAAI,iEAAgC,EAAE,CAAC;YACpD,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,mBAAmB,EAAE,CAAC,CAAC,oBAAoB;aAC5C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,IAAI,GAAG,IAAI,2DAA0B,EAAE,CAAC;YAC9C,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,UAAU,EAAE,qBAAqB;aAClC,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,IAAI,GAAG,IAAI,2DAA0B,EAAE,CAAC;YAC9C,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAEhD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,IAAI,GAAG,IAAI,2DAA0B,EAAE,CAAC;YAC9C,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,oBAAoB,EAAE,IAAI;gBAC1B,0BAA0B;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,IAAI,GAAG,IAAI,2DAA0B,EAAE,CAAC;YAC9C,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,oBAAoB,EAAE,IAAI;gBAC1B,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uCAAuC,EAAE,GAAG,EAAE;QACrD,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,IAAI,GAAG,IAAI,sEAAqC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAEnE,yCAAyC;YACzC,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,oBAAoB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;gBACjD,sBAAsB,EAAE,IAAI,IAAI,EAAE;aACnC,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,IAAI,GAAG,IAAI,sEAAqC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACnE,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAEhD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACxD,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,MAAM,GAAqB;gBAC/B,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;gBAChC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,QAAQ;gBACxC,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,GAAG;aACZ,CAAC;YAEF,MAAM,MAAM,GAAqB;gBAC/B,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;gBAChC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,OAAO;gBACvC,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,GAAG;aACZ,CAAC;YAEF,MAAM,IAAI,GAAG,IAAI,yEAAwC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClE,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,kBAAkB,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;aACrC,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;QACvG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,IAAI,GAAG,oEAAmC,CAAC,MAAM,EAAE;iBACtD,oBAAoB,EAAE;iBACtB,sBAAsB,EAAE;iBACxB,mBAAmB,CAAC,uCAAe,CAAC,IAAI,CAAC;iBACzC,KAAK,EAAE,CAAC;YAEX,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;gBAC9C,uBAAuB,EAAE,EAAE;gBAC3B,eAAe,EAAE,uCAAe,CAAC,IAAI;aACtC,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,IAAI,GAAG,oEAAmC,CAAC,MAAM,EAAE;iBACtD,oBAAoB,EAAE;iBACtB,kBAAkB,EAAE;iBACpB,WAAW,EAAE,CAAC;YAEjB,MAAM,cAAc,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC5C,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;aAC/C,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,OAAO;aAC7C,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,MAAM;aAC5C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,OAAO,GAAG,oEAAmC,CAAC,MAAM,EAAE,CAAC;YAE7D,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,IAAI,GAAG,oEAAmC,CAAC,MAAM,EAAE;iBACtD,oBAAoB,EAAE;iBACtB,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,kEAAiC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,OAAO,GAAG,oEAAmC,CAAC,MAAM,EAAE;iBACzD,oBAAoB,EAAE;iBACtB,iBAAiB,EAAE;iBACnB,qBAAqB,EAAE;iBACvB,kBAAkB,EAAE;iBACpB,sBAAsB,EAAE;iBACxB,mBAAmB,EAAE;iBACrB,oBAAoB,EAAE;iBACtB,iBAAiB,EAAE;iBACnB,yBAAyB,EAAE;iBAC3B,cAAc,EAAE;iBAChB,sBAAsB,EAAE;iBACxB,qBAAqB,EAAE;iBACvB,wBAAwB,EAAE;iBAC1B,qBAAqB,CAAC,2CAAiB,CAAC,SAAS,CAAC;iBAClD,mBAAmB,CAAC,uCAAe,CAAC,IAAI,CAAC;iBACzC,4BAA4B,CAAC,EAAE,EAAE,EAAE,CAAC;iBACpC,eAAe,CAAC,WAAW,CAAC;iBAC5B,iBAAiB,CAAC,8BAAc,CAAC,MAAM,EAAE,CAAC;iBAC1C,iBAAiB,CAAC,UAAU,CAAC;iBAC7B,gBAAgB,CAAC,CAAC,CAAC;iBACnB,cAAc,CAAC,CAAC,CAAC;iBACjB,uBAAuB,CAAC,UAAU,CAAC;iBACnC,cAAc,CAAC,8CAAoB,CAAC,QAAQ,CAAC;iBAC7C,mBAAmB,EAAE;iBACrB,QAAQ,EAAE;iBACV,aAAa,EAAE;iBACf,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAAC;iBACpC,2BAA2B,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAEvC,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,oEAAmC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,KAAK,GAAG,IAAI,kEAAiC,EAAE,CAAC;YACtD,MAAM,KAAK,GAAG,IAAI,oEAAmC,EAAE,CAAC;YACxD,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAEtC,MAAM,KAAK,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACnC,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;gBAC9C,uBAAuB,EAAE,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,KAAK,GAAG,IAAI,kEAAiC,EAAE,CAAC;YACtD,MAAM,KAAK,GAAG,IAAI,gEAA+B,EAAE,CAAC;YACpD,MAAM,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;YAErC,MAAM,cAAc,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC5C,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;aAC/C,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,OAAO;aAC7C,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,MAAM;aAC5C,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,IAAI,GAAG,IAAI,kEAAiC,EAAE,CAAC;YACrD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE3B,MAAM,cAAc,GAAG,yCAAe,CAAC,MAAM,CAAC;gBAC5C,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;aAC/C,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,yCAAe,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,MAAM;aAC5C,CAAC,CAAC;YAEH,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\correlated-event.specification.spec.ts"], "sourcesContent": ["import {\r\n  CorrelatedEventSpecification,\r\n  CorrelationCompletedSpecification,\r\n  CorrelationFailedSpecification,\r\n  CorrelationInProgressSpecification,\r\n  CorrelationPartialSpecification,\r\n  HighCorrelationQualitySpecification,\r\n  HasValidationErrorsSpecification,\r\n  RequiresManualReviewSpecification,\r\n  ReadyForNextStageSpecification,\r\n  HighConfidenceCorrelationSpecification,\r\n  HasAttackChainSpecification,\r\n  HasTemporalCorrelationSpecification,\r\n  HasSpatialCorrelationSpecification,\r\n  HasBehavioralCorrelationSpecification,\r\n  CorrelationStatusSpecification,\r\n  ConfidenceLevelSpecification,\r\n  CorrelationQualityScoreRangeSpecification,\r\n  AppliedRuleSpecification,\r\n  EnrichedEventSpecification,\r\n  CorrelationIdSpecification,\r\n  HasRelatedEventsSpecification,\r\n  HasChildEventsSpecification,\r\n  CorrelationPatternSpecification,\r\n  MatchTypeSpecification,\r\n  ExceededMaxAttemptsSpecification,\r\n  ReviewedEventSpecification,\r\n  PendingReviewSpecification,\r\n  CorrelationDurationRangeSpecification,\r\n  AverageMatchConfidenceRangeSpecification,\r\n  CorrelatedEventSpecificationBuilder\r\n} from '../correlated-event.specification';\r\nimport { \r\n  CorrelatedEvent, \r\n  CorrelatedEventProps, \r\n  CorrelationRule, \r\n  CorrelationMatch, \r\n  CorrelationResult, \r\n  AttackChain, \r\n  CorrelationRuleType, \r\n  CorrelationMatchType \r\n} from '../../entities/correlated-event.entity';\r\nimport { CorrelationStatus } from '../../enums/correlation-status.enum';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { EventMetadata } from '../../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventStatus } from '../../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\nimport { EventTimestamp } from '../../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../value-objects/event-metadata/event-source.value-object';\r\n\r\ndescribe('CorrelatedEvent Specifications', () => {\r\n  let mockEventMetadata: EventMetadata;\r\n  let baseProps: CorrelatedEventProps;\r\n\r\n  beforeEach(() => {\r\n    // Create mock event metadata\r\n    mockEventMetadata = EventMetadata.create({\r\n      timestamp: EventTimestamp.now(),\r\n      source: EventSource.create({\r\n        type: 'SIEM',\r\n        identifier: 'test-siem-001',\r\n        name: 'Test SIEM System'\r\n      }),\r\n      processingInfo: {\r\n        receivedAt: new Date(),\r\n        processedAt: new Date(),\r\n        processingDuration: 100,\r\n        version: '1.0.0'\r\n      }\r\n    });\r\n\r\n    baseProps = {\r\n      enrichedEventId: UniqueEntityId.create(),\r\n      metadata: mockEventMetadata,\r\n      type: EventType.SECURITY_ALERT,\r\n      severity: EventSeverity.HIGH,\r\n      status: EventStatus.ACTIVE,\r\n      processingStatus: EventProcessingStatus.CORRELATED,\r\n      correlationStatus: CorrelationStatus.COMPLETED,\r\n      enrichedData: {\r\n        original_event: 'test_event',\r\n        enriched_at: new Date().toISOString()\r\n      },\r\n      correlatedData: {\r\n        correlation_id: 'corr_123',\r\n        related_events: 5\r\n      },\r\n      title: 'Test Correlated Security Event',\r\n      confidenceLevel: ConfidenceLevel.HIGH,\r\n      correlationId: 'corr_test_123',\r\n      childEventIds: [],\r\n      appliedRules: [],\r\n      correlationMatches: [],\r\n      relatedEventIds: [],\r\n      correlationPatterns: ['temporal_sequence'],\r\n      correlationQualityScore: 85\r\n    };\r\n  });\r\n\r\n  describe('CorrelationCompletedSpecification', () => {\r\n    it('should be satisfied by completed correlation events', () => {\r\n      const spec = new CorrelationCompletedSpecification();\r\n      const completedEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.COMPLETED\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(completedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has completed correlation');\r\n    });\r\n\r\n    it('should not be satisfied by non-completed correlation events', () => {\r\n      const spec = new CorrelationCompletedSpecification();\r\n      const pendingEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.PENDING\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(pendingEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('CorrelationFailedSpecification', () => {\r\n    it('should be satisfied by failed correlation events', () => {\r\n      const spec = new CorrelationFailedSpecification();\r\n      const failedEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.FAILED\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(failedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has failed correlation');\r\n    });\r\n\r\n    it('should not be satisfied by non-failed correlation events', () => {\r\n      const spec = new CorrelationFailedSpecification();\r\n      const completedEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.COMPLETED\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(completedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('CorrelationInProgressSpecification', () => {\r\n    it('should be satisfied by in-progress correlation events', () => {\r\n      const spec = new CorrelationInProgressSpecification();\r\n      const inProgressEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.IN_PROGRESS\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(inProgressEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event is currently being correlated');\r\n    });\r\n  });\r\n\r\n  describe('CorrelationPartialSpecification', () => {\r\n    it('should be satisfied by partial correlation events', () => {\r\n      const spec = new CorrelationPartialSpecification();\r\n      const partialEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.PARTIAL\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(partialEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has partial correlation results');\r\n    });\r\n  });\r\n\r\n  describe('HighCorrelationQualitySpecification', () => {\r\n    it('should be satisfied by high quality correlation events', () => {\r\n      const spec = new HighCorrelationQualitySpecification();\r\n      const highQualityEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationQualityScore: 85\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(highQualityEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has high correlation quality (>= 70)');\r\n    });\r\n\r\n    it('should not be satisfied by low quality correlation events', () => {\r\n      const spec = new HighCorrelationQualitySpecification();\r\n      const lowQualityEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationQualityScore: 50\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(lowQualityEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('HasValidationErrorsSpecification', () => {\r\n    it('should be satisfied by events with validation errors', () => {\r\n      const spec = new HasValidationErrorsSpecification();\r\n      const eventWithErrors = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        validationErrors: ['Error 1', 'Error 2']\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(eventWithErrors)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has validation errors');\r\n    });\r\n\r\n    it('should not be satisfied by events without validation errors', () => {\r\n      const spec = new HasValidationErrorsSpecification();\r\n      const eventWithoutErrors = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        validationErrors: []\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(eventWithoutErrors)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('RequiresManualReviewSpecification', () => {\r\n    it('should be satisfied by events requiring manual review', () => {\r\n      const spec = new RequiresManualReviewSpecification();\r\n      const reviewEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        requiresManualReview: true\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(reviewEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event requires manual review');\r\n    });\r\n\r\n    it('should not be satisfied by events not requiring manual review', () => {\r\n      const spec = new RequiresManualReviewSpecification();\r\n      const noReviewEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        requiresManualReview: false\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(noReviewEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('ReadyForNextStageSpecification', () => {\r\n    it('should be satisfied by events ready for next stage', () => {\r\n      const spec = new ReadyForNextStageSpecification();\r\n      const readyEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.COMPLETED,\r\n        correlationQualityScore: 85,\r\n        validationErrors: [],\r\n        requiresManualReview: false\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(readyEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event is ready for next processing stage');\r\n    });\r\n\r\n    it('should not be satisfied by events not ready for next stage', () => {\r\n      const spec = new ReadyForNextStageSpecification();\r\n      const notReadyEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.FAILED,\r\n        correlationQualityScore: 50\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(notReadyEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('HighConfidenceCorrelationSpecification', () => {\r\n    it('should be satisfied by high confidence correlation events', () => {\r\n      const spec = new HighConfidenceCorrelationSpecification();\r\n      const highConfidenceEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationResult: {\r\n          success: true,\r\n          appliedRules: [],\r\n          failedRules: [],\r\n          warnings: [],\r\n          errors: [],\r\n          processingDurationMs: 1000,\r\n          confidenceScore: 90,\r\n          rulesUsed: 2,\r\n          matchesFound: 5,\r\n          patternsIdentified: []\r\n        }\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(highConfidenceEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has high confidence correlation');\r\n    });\r\n  });\r\n\r\n  describe('HasAttackChainSpecification', () => {\r\n    it('should be satisfied by events with attack chains', () => {\r\n      const spec = new HasAttackChainSpecification();\r\n      const attackChain: AttackChain = {\r\n        id: 'attack_chain_1',\r\n        name: 'Multi-stage Attack',\r\n        description: 'Coordinated attack with multiple stages',\r\n        stages: [\r\n          {\r\n            id: 'stage_1',\r\n            name: 'Initial Access',\r\n            description: 'Initial compromise',\r\n            eventIds: [UniqueEntityId.create()],\r\n            order: 1,\r\n            confidence: ConfidenceLevel.HIGH,\r\n            timestamp: new Date()\r\n          }\r\n        ],\r\n        confidence: ConfidenceLevel.HIGH,\r\n        severity: EventSeverity.HIGH,\r\n        timeline: {\r\n          startTime: new Date(Date.now() - 3600000),\r\n          endTime: new Date(),\r\n          duration: 3600000\r\n        }\r\n      };\r\n\r\n      const eventWithAttackChain = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        attackChain\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(eventWithAttackChain)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has an attack chain');\r\n    });\r\n\r\n    it('should not be satisfied by events without attack chains', () => {\r\n      const spec = new HasAttackChainSpecification();\r\n      const eventWithoutAttackChain = CorrelatedEvent.create(baseProps);\r\n\r\n      expect(spec.isSatisfiedBy(eventWithoutAttackChain)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('HasTemporalCorrelationSpecification', () => {\r\n    it('should be satisfied by events with temporal correlation', () => {\r\n      const spec = new HasTemporalCorrelationSpecification();\r\n      const eventWithTemporal = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        temporalCorrelation: {\r\n          timeWindow: 3600000,\r\n          eventSequence: [UniqueEntityId.create()],\r\n          patternType: 'sequence',\r\n          confidence: 80\r\n        }\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(eventWithTemporal)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has temporal correlation data');\r\n    });\r\n  });\r\n\r\n  describe('HasSpatialCorrelationSpecification', () => {\r\n    it('should be satisfied by events with spatial correlation', () => {\r\n      const spec = new HasSpatialCorrelationSpecification();\r\n      const eventWithSpatial = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        spatialCorrelation: {\r\n          sourceIps: ['*************'],\r\n          targetIps: ['*********'],\r\n          networkSegments: ['***********/24'],\r\n          geographicRegions: ['US-East'],\r\n          confidence: 75\r\n        }\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(eventWithSpatial)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has spatial correlation data');\r\n    });\r\n  });\r\n\r\n  describe('HasBehavioralCorrelationSpecification', () => {\r\n    it('should be satisfied by events with behavioral correlation', () => {\r\n      const spec = new HasBehavioralCorrelationSpecification();\r\n      const eventWithBehavioral = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        behavioralCorrelation: {\r\n          userPatterns: ['login_anomaly'],\r\n          systemPatterns: ['resource_spike'],\r\n          anomalyScore: 85,\r\n          baselineDeviation: 2.5,\r\n          confidence: 80\r\n        }\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(eventWithBehavioral)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has behavioral correlation data');\r\n    });\r\n  });\r\n\r\n  describe('CorrelationStatusSpecification', () => {\r\n    it('should be satisfied by events with matching status', () => {\r\n      const spec = new CorrelationStatusSpecification([\r\n        CorrelationStatus.COMPLETED,\r\n        CorrelationStatus.PARTIAL\r\n      ]);\r\n      const completedEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.COMPLETED\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(completedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event status is one of: COMPLETED, PARTIAL');\r\n    });\r\n\r\n    it('should not be satisfied by events with non-matching status', () => {\r\n      const spec = new CorrelationStatusSpecification([CorrelationStatus.COMPLETED]);\r\n      const failedEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.FAILED\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(failedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('ConfidenceLevelSpecification', () => {\r\n    it('should be satisfied by events with matching confidence level', () => {\r\n      const spec = new ConfidenceLevelSpecification([\r\n        ConfidenceLevel.HIGH,\r\n        ConfidenceLevel.VERY_HIGH\r\n      ]);\r\n      const highConfidenceEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        confidenceLevel: ConfidenceLevel.HIGH\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(highConfidenceEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event confidence level is one of: HIGH, VERY_HIGH');\r\n    });\r\n  });\r\n\r\n  describe('CorrelationQualityScoreRangeSpecification', () => {\r\n    it('should be satisfied by events within quality score range', () => {\r\n      const spec = new CorrelationQualityScoreRangeSpecification(70, 90);\r\n      const qualityEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationQualityScore: 85\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(qualityEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event quality score is between 70 and 90');\r\n    });\r\n\r\n    it('should not be satisfied by events outside quality score range', () => {\r\n      const spec = new CorrelationQualityScoreRangeSpecification(70, 90);\r\n      const lowQualityEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationQualityScore: 50\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(lowQualityEvent)).toBe(false);\r\n    });\r\n\r\n    it('should handle minimum score only', () => {\r\n      const spec = new CorrelationQualityScoreRangeSpecification(70);\r\n      const qualityEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationQualityScore: 85\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(qualityEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event quality score is at least 70');\r\n    });\r\n\r\n    it('should handle maximum score only', () => {\r\n      const spec = new CorrelationQualityScoreRangeSpecification(undefined, 90);\r\n      const qualityEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationQualityScore: 85\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(qualityEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event quality score is at most 90');\r\n    });\r\n  });\r\n\r\n  describe('AppliedRuleSpecification', () => {\r\n    it('should be satisfied by events with applied rule', () => {\r\n      const rule: CorrelationRule = {\r\n        id: 'test_rule_1',\r\n        name: 'Test Rule',\r\n        description: 'A test correlation rule',\r\n        type: CorrelationRuleType.TEMPORAL,\r\n        priority: 100,\r\n        required: false,\r\n        timeWindowMs: 3600000,\r\n        minConfidence: 70\r\n      };\r\n\r\n      const spec = new AppliedRuleSpecification('test_rule_1');\r\n      const eventWithRule = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        appliedRules: [rule]\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(eventWithRule)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has applied rule: test_rule_1');\r\n    });\r\n  });\r\n\r\n  describe('EnrichedEventSpecification', () => {\r\n    it('should be satisfied by events referencing specific enriched event', () => {\r\n      const enrichedEventId = UniqueEntityId.create();\r\n      const spec = new EnrichedEventSpecification(enrichedEventId);\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        enrichedEventId\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(true);\r\n      expect(spec.getDescription()).toBe(`Correlated event references enriched event: ${enrichedEventId.toString()}`);\r\n    });\r\n  });\r\n\r\n  describe('CorrelationIdSpecification', () => {\r\n    it('should be satisfied by events with matching correlation ID', () => {\r\n      const correlationId = 'corr_test_123';\r\n      const spec = new CorrelationIdSpecification(correlationId);\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationId\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has correlation ID: corr_test_123');\r\n    });\r\n  });\r\n\r\n  describe('HasRelatedEventsSpecification', () => {\r\n    it('should be satisfied by events with sufficient related events', () => {\r\n      const spec = new HasRelatedEventsSpecification(2);\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        relatedEventIds: [UniqueEntityId.create(), UniqueEntityId.create(), UniqueEntityId.create()]\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has at least 2 related events');\r\n    });\r\n\r\n    it('should not be satisfied by events with insufficient related events', () => {\r\n      const spec = new HasRelatedEventsSpecification(5);\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        relatedEventIds: [UniqueEntityId.create()]\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('HasChildEventsSpecification', () => {\r\n    it('should be satisfied by events with sufficient child events', () => {\r\n      const spec = new HasChildEventsSpecification(1);\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        childEventIds: [UniqueEntityId.create()]\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has at least 1 child events');\r\n    });\r\n  });\r\n\r\n  describe('CorrelationPatternSpecification', () => {\r\n    it('should be satisfied by events with matching patterns', () => {\r\n      const spec = new CorrelationPatternSpecification(['temporal_sequence', 'ip_clustering']);\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationPatterns: ['temporal_sequence', 'behavioral_anomaly']\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has patterns: temporal_sequence, ip_clustering');\r\n    });\r\n  });\r\n\r\n  describe('MatchTypeSpecification', () => {\r\n    it('should be satisfied by events with matching match types', () => {\r\n      const match: CorrelationMatch = {\r\n        eventId: UniqueEntityId.create(),\r\n        confidence: 80,\r\n        matchType: CorrelationMatchType.TEMPORAL,\r\n        ruleId: 'rule1',\r\n        details: {},\r\n        timestamp: new Date(),\r\n        weight: 0.8\r\n      };\r\n\r\n      const spec = new MatchTypeSpecification([CorrelationMatchType.TEMPORAL]);\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationMatches: [match]\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has matches of types: TEMPORAL');\r\n    });\r\n  });\r\n\r\n  describe('ExceededMaxAttemptsSpecification', () => {\r\n    it('should be satisfied by events that exceeded max attempts', () => {\r\n      const spec = new ExceededMaxAttemptsSpecification();\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationAttempts: 5 // Assuming max is 3\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has exceeded maximum correlation attempts');\r\n    });\r\n  });\r\n\r\n  describe('ReviewedEventSpecification', () => {\r\n    it('should be satisfied by reviewed events', () => {\r\n      const spec = new ReviewedEventSpecification();\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        reviewedAt: new Date(),\r\n        reviewedBy: '<EMAIL>'\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event has been manually reviewed');\r\n    });\r\n\r\n    it('should not be satisfied by unreviewed events', () => {\r\n      const spec = new ReviewedEventSpecification();\r\n      const event = CorrelatedEvent.create(baseProps);\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('PendingReviewSpecification', () => {\r\n    it('should be satisfied by events pending review', () => {\r\n      const spec = new PendingReviewSpecification();\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        requiresManualReview: true\r\n        // reviewedAt is undefined\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event is pending manual review');\r\n    });\r\n\r\n    it('should not be satisfied by reviewed events', () => {\r\n      const spec = new PendingReviewSpecification();\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        requiresManualReview: true,\r\n        reviewedAt: new Date()\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('CorrelationDurationRangeSpecification', () => {\r\n    it('should be satisfied by events within duration range', () => {\r\n      const spec = new CorrelationDurationRangeSpecification(1000, 5000);\r\n      \r\n      // Create event with correlation duration\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStartedAt: new Date(Date.now() - 3000),\r\n        correlationCompletedAt: new Date()\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event duration is between 1000ms and 5000ms');\r\n    });\r\n\r\n    it('should not be satisfied by events without duration', () => {\r\n      const spec = new CorrelationDurationRangeSpecification(1000, 5000);\r\n      const event = CorrelatedEvent.create(baseProps);\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('AverageMatchConfidenceRangeSpecification', () => {\r\n    it('should be satisfied by events within confidence range', () => {\r\n      const match1: CorrelationMatch = {\r\n        eventId: UniqueEntityId.create(),\r\n        confidence: 80,\r\n        matchType: CorrelationMatchType.TEMPORAL,\r\n        ruleId: 'rule1',\r\n        details: {},\r\n        timestamp: new Date(),\r\n        weight: 0.8\r\n      };\r\n\r\n      const match2: CorrelationMatch = {\r\n        eventId: UniqueEntityId.create(),\r\n        confidence: 90,\r\n        matchType: CorrelationMatchType.SPATIAL,\r\n        ruleId: 'rule2',\r\n        details: {},\r\n        timestamp: new Date(),\r\n        weight: 0.9\r\n      };\r\n\r\n      const spec = new AverageMatchConfidenceRangeSpecification(70, 95);\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationMatches: [match1, match2]\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Correlated event average match confidence is between 70 and 95');\r\n    });\r\n  });\r\n\r\n  describe('CorrelatedEventSpecificationBuilder', () => {\r\n    it('should build complex specifications with AND logic', () => {\r\n      const spec = CorrelatedEventSpecificationBuilder.create()\r\n        .correlationCompleted()\r\n        .highCorrelationQuality()\r\n        .withConfidenceLevel(ConfidenceLevel.HIGH)\r\n        .build();\r\n\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.COMPLETED,\r\n        correlationQualityScore: 85,\r\n        confidenceLevel: ConfidenceLevel.HIGH\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(event)).toBe(true);\r\n    });\r\n\r\n    it('should build complex specifications with OR logic', () => {\r\n      const spec = CorrelatedEventSpecificationBuilder.create()\r\n        .correlationCompleted()\r\n        .correlationPartial()\r\n        .buildWithOr();\r\n\r\n      const completedEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.COMPLETED\r\n      });\r\n\r\n      const partialEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.PARTIAL\r\n      });\r\n\r\n      const failedEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.FAILED\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(completedEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(partialEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(failedEvent)).toBe(false);\r\n    });\r\n\r\n    it('should throw error when building without specifications', () => {\r\n      const builder = CorrelatedEventSpecificationBuilder.create();\r\n\r\n      expect(() => builder.build()).toThrow('At least one specification must be added');\r\n    });\r\n\r\n    it('should return single specification when only one is added', () => {\r\n      const spec = CorrelatedEventSpecificationBuilder.create()\r\n        .correlationCompleted()\r\n        .build();\r\n\r\n      expect(spec).toBeInstanceOf(CorrelationCompletedSpecification);\r\n    });\r\n\r\n    it('should support fluent interface for all specification types', () => {\r\n      const builder = CorrelatedEventSpecificationBuilder.create()\r\n        .correlationCompleted()\r\n        .correlationFailed()\r\n        .correlationInProgress()\r\n        .correlationPartial()\r\n        .highCorrelationQuality()\r\n        .hasValidationErrors()\r\n        .requiresManualReview()\r\n        .readyForNextStage()\r\n        .highConfidenceCorrelation()\r\n        .hasAttackChain()\r\n        .hasTemporalCorrelation()\r\n        .hasSpatialCorrelation()\r\n        .hasBehavioralCorrelation()\r\n        .withCorrelationStatus(CorrelationStatus.COMPLETED)\r\n        .withConfidenceLevel(ConfidenceLevel.HIGH)\r\n        .correlationQualityScoreRange(70, 90)\r\n        .withAppliedRule('test_rule')\r\n        .fromEnrichedEvent(UniqueEntityId.create())\r\n        .withCorrelationId('corr_123')\r\n        .hasRelatedEvents(2)\r\n        .hasChildEvents(1)\r\n        .withCorrelationPatterns('pattern1')\r\n        .withMatchTypes(CorrelationMatchType.TEMPORAL)\r\n        .exceededMaxAttempts()\r\n        .reviewed()\r\n        .pendingReview()\r\n        .correlationDurationRange(1000, 5000)\r\n        .averageMatchConfidenceRange(70, 90);\r\n\r\n      expect(builder).toBeInstanceOf(CorrelatedEventSpecificationBuilder);\r\n    });\r\n  });\r\n\r\n  describe('specification composition', () => {\r\n    it('should support AND composition', () => {\r\n      const spec1 = new CorrelationCompletedSpecification();\r\n      const spec2 = new HighCorrelationQualitySpecification();\r\n      const composedSpec = spec1.and(spec2);\r\n\r\n      const event = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.COMPLETED,\r\n        correlationQualityScore: 85\r\n      });\r\n\r\n      expect(composedSpec.isSatisfiedBy(event)).toBe(true);\r\n    });\r\n\r\n    it('should support OR composition', () => {\r\n      const spec1 = new CorrelationCompletedSpecification();\r\n      const spec2 = new CorrelationPartialSpecification();\r\n      const composedSpec = spec1.or(spec2);\r\n\r\n      const completedEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.COMPLETED\r\n      });\r\n\r\n      const partialEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.PARTIAL\r\n      });\r\n\r\n      const failedEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.FAILED\r\n      });\r\n\r\n      expect(composedSpec.isSatisfiedBy(completedEvent)).toBe(true);\r\n      expect(composedSpec.isSatisfiedBy(partialEvent)).toBe(true);\r\n      expect(composedSpec.isSatisfiedBy(failedEvent)).toBe(false);\r\n    });\r\n\r\n    it('should support NOT composition', () => {\r\n      const spec = new CorrelationCompletedSpecification();\r\n      const notSpec = spec.not();\r\n\r\n      const completedEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.COMPLETED\r\n      });\r\n\r\n      const failedEvent = CorrelatedEvent.create({\r\n        ...baseProps,\r\n        correlationStatus: CorrelationStatus.FAILED\r\n      });\r\n\r\n      expect(notSpec.isSatisfiedBy(completedEvent)).toBe(false);\r\n      expect(notSpec.isSatisfiedBy(failedEvent)).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}