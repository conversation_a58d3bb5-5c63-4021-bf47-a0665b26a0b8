83476c7adf0d259b55f35a1deef496fa
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorrelatedEventCorrelationFailedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
/**
 * Correlated Event Correlation Failed Domain Event
 *
 * Raised when correlation processing fails for a correlated event.
 * This event triggers various error handling and recovery processes including:
 * - Error logging and monitoring
 * - Retry scheduling (if applicable)
 * - Alert generation for persistent failures
 * - Metrics collection for failure analysis
 * - Dead letter queue management
 * - Escalation to operations team
 * - Fallback processing workflows
 */
class CorrelatedEventCorrelationFailedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the enriched event ID
     */
    get enrichedEventId() {
        return this.eventData.enrichedEventId;
    }
    /**
     * Get the error message
     */
    get error() {
        return this.eventData.error;
    }
    /**
     * Get the current attempt number
     */
    get attempt() {
        return this.eventData.attempt;
    }
    /**
     * Check if maximum attempts have been exceeded
     */
    get maxAttemptsExceeded() {
        return this.eventData.maxAttemptsExceeded;
    }
    /**
     * Get the failure timestamp
     */
    get failedAt() {
        return this.eventData.failedAt || this.occurredOn;
    }
    /**
     * Get the error code
     */
    get errorCode() {
        return this.eventData.errorCode;
    }
    /**
     * Get detailed error information
     */
    get errorDetails() {
        return this.eventData.errorDetails;
    }
    /**
     * Get the failed rules
     */
    get failedRules() {
        return this.eventData.failedRules || [];
    }
    /**
     * Get the processing context
     */
    get processingContext() {
        return this.eventData.processingContext || {};
    }
    /**
     * Check if this failure is retryable
     */
    get isRetryable() {
        return this.eventData.isRetryable !== false; // Default to true unless explicitly false
    }
    /**
     * Get suggested retry delay
     */
    get retryDelayMs() {
        return this.eventData.retryDelayMs || this.calculateRetryDelay();
    }
    /**
     * Check if this is the first failure
     */
    isFirstFailure() {
        return this.attempt === 1;
    }
    /**
     * Check if this is a repeated failure
     */
    isRepeatedFailure() {
        return this.attempt > 1;
    }
    /**
     * Check if this failure should trigger an alert
     */
    shouldTriggerAlert() {
        return this.maxAttemptsExceeded || this.attempt >= 2 || !this.isRetryable;
    }
    /**
     * Check if this is a critical failure
     */
    isCriticalFailure() {
        const criticalErrorCodes = [
            'CORRELATION_ENGINE_DOWN',
            'DATABASE_CONNECTION_FAILED',
            'MEMORY_EXHAUSTED',
            'SECURITY_VIOLATION',
        ];
        return this.maxAttemptsExceeded ||
            (this.errorCode && criticalErrorCodes.includes(this.errorCode)) ||
            !this.isRetryable;
    }
    /**
     * Get failure category
     */
    getFailureCategory() {
        if (!this.errorCode) {
            return 'unknown';
        }
        const categoryMap = {
            // Transient errors
            'TIMEOUT': 'transient',
            'NETWORK_ERROR': 'transient',
            'SERVICE_UNAVAILABLE': 'transient',
            'RATE_LIMIT_EXCEEDED': 'transient',
            'TEMPORARY_FAILURE': 'transient',
            // Configuration errors
            'INVALID_RULE_CONFIG': 'configuration',
            'MISSING_CONFIGURATION': 'configuration',
            'INVALID_PARAMETERS': 'configuration',
            'RULE_VALIDATION_FAILED': 'configuration',
            // Data errors
            'INVALID_EVENT_DATA': 'data',
            'MISSING_REQUIRED_FIELDS': 'data',
            'DATA_VALIDATION_FAILED': 'data',
            'CORRUPTED_DATA': 'data',
            // System errors
            'CORRELATION_ENGINE_DOWN': 'system',
            'DATABASE_CONNECTION_FAILED': 'system',
            'MEMORY_EXHAUSTED': 'system',
            'SECURITY_VIOLATION': 'system',
        };
        return categoryMap[this.errorCode] || 'unknown';
    }
    /**
     * Get failure severity
     */
    getFailureSeverity() {
        if (this.isCriticalFailure()) {
            return 'critical';
        }
        if (this.maxAttemptsExceeded) {
            return 'high';
        }
        if (this.isRepeatedFailure()) {
            return 'medium';
        }
        return 'low';
    }
    /**
     * Get recommended recovery actions
     */
    getRecoveryActions() {
        const actions = [];
        if (this.isRetryable && !this.maxAttemptsExceeded) {
            actions.push(`Schedule retry in ${this.retryDelayMs}ms`);
        }
        switch (this.getFailureCategory()) {
            case 'transient':
                actions.push('Monitor system resources');
                actions.push('Check network connectivity');
                actions.push('Verify service availability');
                break;
            case 'configuration':
                actions.push('Review correlation rule configuration');
                actions.push('Validate rule parameters');
                actions.push('Check configuration syntax');
                break;
            case 'data':
                actions.push('Validate input event data');
                actions.push('Check data format and schema');
                actions.push('Review data transformation logic');
                break;
            case 'system':
                actions.push('Check system health and resources');
                actions.push('Verify database connectivity');
                actions.push('Review system logs');
                actions.push('Escalate to operations team');
                break;
            case 'unknown':
                actions.push('Investigate error details');
                actions.push('Review correlation engine logs');
                actions.push('Contact development team');
                break;
        }
        if (this.maxAttemptsExceeded) {
            actions.push('Move to dead letter queue');
            actions.push('Generate manual review task');
            actions.push('Notify operations team');
        }
        if (this.shouldTriggerAlert()) {
            actions.push('Generate failure alert');
            actions.push('Update monitoring dashboards');
        }
        return actions;
    }
    /**
     * Get escalation requirements
     */
    getEscalationRequirements() {
        const severity = this.getFailureSeverity();
        if (severity === 'critical') {
            return {
                shouldEscalate: true,
                escalationLevel: 'management',
                urgency: 'critical',
                timeoutMinutes: 15,
            };
        }
        if (severity === 'high' || this.maxAttemptsExceeded) {
            return {
                shouldEscalate: true,
                escalationLevel: 'ops',
                urgency: 'high',
                timeoutMinutes: 30,
            };
        }
        if (severity === 'medium' && this.getFailureCategory() === 'system') {
            return {
                shouldEscalate: true,
                escalationLevel: 'ops',
                urgency: 'medium',
                timeoutMinutes: 60,
            };
        }
        if (this.getFailureCategory() === 'configuration') {
            return {
                shouldEscalate: true,
                escalationLevel: 'dev',
                urgency: 'medium',
                timeoutMinutes: 120,
            };
        }
        return {
            shouldEscalate: false,
            escalationLevel: 'ops',
            urgency: 'low',
            timeoutMinutes: 240,
        };
    }
    /**
     * Get metrics to update based on failure
     */
    getMetricsToUpdate() {
        const metrics = [];
        // Failure counter
        metrics.push({
            metric: 'correlation_failures',
            value: 1,
            tags: {
                attempt: this.attempt.toString(),
                category: this.getFailureCategory(),
                severity: this.getFailureSeverity(),
                error_code: this.errorCode || 'unknown',
            },
        });
        // Max attempts exceeded counter
        if (this.maxAttemptsExceeded) {
            metrics.push({
                metric: 'correlation_max_attempts_exceeded',
                value: 1,
            });
        }
        // Retry counter
        if (this.isRetryable && !this.maxAttemptsExceeded) {
            metrics.push({
                metric: 'correlation_retries_scheduled',
                value: 1,
                tags: {
                    attempt: this.attempt.toString(),
                    delay_ms: this.retryDelayMs.toString(),
                },
            });
        }
        // Alert counter
        if (this.shouldTriggerAlert()) {
            metrics.push({
                metric: 'correlation_failure_alerts',
                value: 1,
                tags: {
                    severity: this.getFailureSeverity(),
                },
            });
        }
        return metrics;
    }
    /**
     * Calculate retry delay based on attempt number
     */
    calculateRetryDelay() {
        // Exponential backoff: 2^attempt * 1000ms, capped at 5 minutes
        const baseDelay = 1000; // 1 second
        const maxDelay = 300000; // 5 minutes
        const delay = Math.min(baseDelay * Math.pow(2, this.attempt - 1), maxDelay);
        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 0.1 * delay;
        return Math.floor(delay + jitter);
    }
    /**
     * Get event summary for handlers
     */
    getEventSummary() {
        return {
            correlatedEventId: this.aggregateId.toString(),
            enrichedEventId: this.enrichedEventId.toString(),
            error: this.error,
            attempt: this.attempt,
            maxAttemptsExceeded: this.maxAttemptsExceeded,
            failedAt: this.failedAt,
            errorCode: this.errorCode,
            errorDetails: this.errorDetails,
            failedRules: this.failedRules,
            isRetryable: this.isRetryable,
            retryDelayMs: this.retryDelayMs,
            isFirstFailure: this.isFirstFailure(),
            isRepeatedFailure: this.isRepeatedFailure(),
            shouldTriggerAlert: this.shouldTriggerAlert(),
            isCriticalFailure: this.isCriticalFailure(),
            failureCategory: this.getFailureCategory(),
            failureSeverity: this.getFailureSeverity(),
            recoveryActions: this.getRecoveryActions(),
            escalationRequirements: this.getEscalationRequirements(),
            metricsToUpdate: this.getMetricsToUpdate(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventSummary: this.getEventSummary(),
        };
    }
}
exports.CorrelatedEventCorrelationFailedDomainEvent = CorrelatedEventCorrelationFailedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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