{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\application\\services\\__tests__\\threat-intelligence.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,2CAA+C;AAC/C,2CAAsE;AAEtE,gFAA2E;AAC3E,oGAAqJ;AACrJ,4GAAgG;AAChG,sFAA2E;AAC3E,4FAAiF;AACjF,yFAAqF;AACrF,6FAAyF;AACzF,0GAAsG;AAEtG,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;IACzC,IAAI,OAAkC,CAAC;IACvC,IAAI,gBAA6D,CAAC;IAClE,IAAI,aAA6D,CAAC;IAClE,IAAI,eAAqD,CAAC;IAC1D,IAAI,kBAA2D,CAAC;IAChE,IAAI,aAAyC,CAAC;IAC9C,IAAI,YAAuC,CAAC;IAC5C,IAAI,mBAAqD,CAAC;IAC1D,IAAI,aAAyC,CAAC;IAE9C,MAAM,sBAAsB,GAAgC;QAC1D,EAAE,EAAE,sCAAsC;QAC1C,KAAK,EAAE,0BAA0B;QACjC,WAAW,EAAE,kBAAkB;QAC/B,UAAU,EAAE,uCAAU,CAAC,GAAG;QAC1B,QAAQ,EAAE,2CAAc,CAAC,IAAI;QAC7B,UAAU,EAAE,6CAAgB,CAAC,IAAI;QACjC,MAAM,EAAE,yCAAY,CAAC,MAAM;QAC3B,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,UAAU,EAAE;YACV,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,GAAG;YAChB,GAAG,EAAE,kBAAkB;YACvB,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,UAAU,EAAE,6CAAgB,CAAC,IAAI;SAClC;QACD,gBAAgB,EAAE,CAAC;QACnB,KAAK,EAAE,KAAK;QACZ,YAAY,EAAE,KAAK;QACnB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC;QAClD,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC7B,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,cAAc,GAAG;YACrB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;YAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;YAChB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;SAC9B,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,uDAAyB;gBACzB;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,+CAAkB,CAAC;oBAC/C,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,sDAAqB,CAAC;oBAClD,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,iCAAW,CAAC;oBACxC,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,uCAAc,CAAC;oBAC3C,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE;wBACR,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;wBAChB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;wBACf,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;wBACd,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;qBACjB;iBACF;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE;wBACR,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;wBACxB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;qBAC5B;iBACF;gBACD;oBACE,OAAO,EAAE,0CAAmB;oBAC5B,QAAQ,EAAE;wBACR,uBAAuB,EAAE,IAAI,CAAC,EAAE,EAAE;wBAClC,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;wBAC/B,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;qBAChC;iBACF;gBACD;oBACE,OAAO,EAAE,sBAAa;oBACtB,QAAQ,EAAE;wBACR,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;qBACf;iBACF;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAA4B,uDAAyB,CAAC,CAAC;QAC3E,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,+CAAkB,CAAC,CAAC,CAAC;QACtE,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,sDAAqB,CAAC,CAAC,CAAC;QACtE,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,iCAAW,CAAC,CAAC,CAAC;QAC9D,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,uCAAc,CAAC,CAAC,CAAC;QACpE,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,8BAAa,CAAC,CAAC;QAC1C,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,4BAAY,CAAC,CAAC;QACxC,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,0CAAmB,CAAC,CAAC;QACtD,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAa,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,YAAY;gBACnB,WAAW,EAAE,wBAAwB;gBACrC,UAAU,EAAE,uCAAU,CAAC,OAAO;gBAC9B,QAAQ,EAAE,2CAAc,CAAC,MAAM;gBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,sBAAsB,CAAC,UAAU;aAC9C,CAAC;YAEF,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACjD,gBAAgB,CAAC,MAAM,CAAC,eAAe,CAAC,sBAA4C,CAAC,CAAC;YACtF,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,sBAA4C,CAAC,CAAC;YAEtF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAE7E,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACpD,KAAK,EAAE;oBACL,KAAK,EAAE,UAAU,CAAC,KAAK;oBACvB,UAAU,EAAE,UAAU,CAAC,UAAU;iBAClC;aACF,CAAC,CAAC;YACH,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBAC3E,GAAG,UAAU;gBACb,MAAM,EAAE,yCAAY,CAAC,MAAM;gBAC3B,UAAU,EAAE,6CAAgB,CAAC,MAAM;gBACnC,QAAQ,EAAE,2CAAc,CAAC,MAAM;gBAC/B,gBAAgB,EAAE,CAAC;gBACnB,KAAK,EAAE,KAAK;gBACZ,YAAY,EAAE,KAAK;aACpB,CAAC,CAAC,CAAC;YACJ,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACjD,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,SAAS,EACT,QAAQ,EACR,qBAAqB,EACrB,sBAAsB,CAAC,EAAE,EACzB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,KAAK,IAAI,EAAE;YAChF,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,iBAAiB;gBACxB,WAAW,EAAE,aAAa;gBAC1B,UAAU,EAAE,uCAAU,CAAC,OAAO;gBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,sBAAsB,CAAC,UAAU;aAC9C,CAAC;YAEF,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,sBAA4C,CAAC,CAAC;YAEzF,MAAM,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;iBAClE,OAAO,CAAC,OAAO,CAAC,0BAAiB,CAAC,CAAC;YAEtC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;YACvD,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,iBAAiB;gBACxB,WAAW,EAAE,6BAA6B;gBAC1C,UAAU,EAAE,uCAAU,CAAC,GAAG;gBAC1B,QAAQ,EAAE,2CAAc,CAAC,QAAQ;gBACjC,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,sBAAsB,CAAC,UAAU;aAC9C,CAAC;YAEF,MAAM,cAAc,GAAG;gBACrB,GAAG,sBAAsB;gBACzB,QAAQ,EAAE,2CAAc,CAAC,QAAQ;aAClC,CAAC;YAEF,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACjD,gBAAgB,CAAC,MAAM,CAAC,eAAe,CAAC,cAAoC,CAAC,CAAC;YAC9E,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAoC,CAAC,CAAC;YAE9E,MAAM,OAAO,CAAC,wBAAwB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAE9D,MAAM,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,CAAC,oBAAoB,CAAC;gBACvE,QAAQ,EAAE,cAAc,CAAC,EAAE;gBAC3B,KAAK,EAAE,cAAc,CAAC,KAAK;gBAC3B,UAAU,EAAE,cAAc,CAAC,UAAU;gBACrC,QAAQ,EAAE,cAAc,CAAC,QAAQ;gBACjC,UAAU,EAAE,cAAc,CAAC,UAAU,CAAC,IAAI;aAC3C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,sBAA4C,CAAC,CAAC;YAEzF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,CAAC,sCAAsC,CAAC,CAAC;YAE/F,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACpD,KAAK,EAAE,EAAE,EAAE,EAAE,sCAAsC,EAAE;gBACrD,SAAS,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,gBAAgB,CAAC;aAC3D,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mEAAmE,EAAE,KAAK,IAAI,EAAE;YACjF,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEjD,MAAM,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAC;iBAC3D,OAAO,CAAC,OAAO,CAAC,0BAAiB,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,gBAAgB,GAAG;gBACvB,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAC7C,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACnC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACtC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC;aAC5E,CAAC;YAEF,gBAAgB,CAAC,kBAAkB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YAEtE,MAAM,QAAQ,GAAG;gBACf,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,WAAW,EAAE,CAAC,uCAAU,CAAC,GAAG,CAAC;gBAC7B,UAAU,EAAE,CAAC,2CAAc,CAAC,IAAI,CAAC;gBACjC,UAAU,EAAE,MAAM;aACnB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YAEhE,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC3E,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,wCAAwC,EACxC,EAAE,WAAW,EAAE,CAAC,uCAAU,CAAC,GAAG,CAAC,EAAE,CAClC,CAAC;YACF,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,qCAAqC,EACrC,EAAE,UAAU,EAAE,CAAC,2CAAc,CAAC,IAAI,CAAC,EAAE,CACtC,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,OAAO,EAAE,CAAC,sBAAsB,CAAC;gBACjC,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE,CAAC;gBACP,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,gBAAgB,GAAG;gBACvB,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAC7C,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACnC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACtC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;aACtD,CAAC;YAEF,gBAAgB,CAAC,kBAAkB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YAEtE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;YAE9E,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE,CAAC;gBACP,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,2CAAc,CAAC,QAAQ;aAClC,CAAC;YAEF,MAAM,cAAc,GAAG,EAAE,GAAG,sBAAsB,EAAE,CAAC;YACrD,MAAM,aAAa,GAAG,EAAE,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE,CAAC;YAE3D,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,cAAoC,CAAC,CAAC;YACjF,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAmC,CAAC,CAAC;YAE7E,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,CACnD,sCAAsC,EACtC,UAAU,EACV,SAAS,CACV,CAAC;YAEF,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAChD,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CACpC,CAAC;YACF,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,SAAS,EACT,QAAQ,EACR,qBAAqB,EACrB,sCAAsC,EACtC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,cAAc,CAAC,KAAK;gBAC3B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC5B,CAAC,CACH,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;YACzE,MAAM,UAAU,GAAG;gBACjB,QAAQ,EAAE,2CAAc,CAAC,QAAQ;gBACjC,UAAU,EAAE,6CAAgB,CAAC,IAAI;aAClC,CAAC;YAEF,MAAM,cAAc,GAAG;gBACrB,GAAG,sBAAsB;gBACzB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC;aACnD,CAAC;YAEF,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,cAAoC,CAAC,CAAC;YACjF,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAoC,CAAC,CAAC;YAE9E,MAAM,OAAO,CAAC,wBAAwB,CACpC,sCAAsC,EACtC,UAAU,EACV,SAAS,CACV,CAAC;YAEF,MAAM,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,sBAA4C,CAAC,CAAC;YACzF,gBAAgB,CAAC,MAAM,CAAC,iBAAiB,CAAC,sBAA4C,CAAC,CAAC;YAExF,MAAM,OAAO,CAAC,wBAAwB,CAAC,sCAAsC,EAAE,SAAS,CAAC,CAAC;YAE1F,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;YAC7E,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,SAAS,EACT,QAAQ,EACR,qBAAqB,EACrB,sCAAsC,EACtC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,sBAAsB,CAAC,KAAK;gBACnC,UAAU,EAAE,sBAAsB,CAAC,UAAU;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,MAAM,MAAM,GAAG;gBACb,GAAG,sBAAsB;gBACzB,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;gBAC5B,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC;aACnD,CAAC;YAEF,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAA4B,CAAC,CAAC;YACzE,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAA4B,CAAC,CAAC;YAEtE,MAAM,OAAO,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC;YAC1D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,sCAAsC,EAAE,OAAO,CAAC,CAAC;YAEhG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACrD,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,gBAAgB,CAAC,KAAK;iBACnB,qBAAqB,CAAC,GAAG,CAAC,CAAC,QAAQ;iBACnC,qBAAqB,CAAC,EAAE,CAAC,CAAE,SAAS;iBACpC,qBAAqB,CAAC,EAAE,CAAC,CAAE,WAAW;iBACtC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAE,SAAS;YAEvC,gDAAgD;YAChD,MAAM,4BAA4B,GAAG,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,2BAA2B,CAAC;iBACzF,iBAAiB,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;YACnD,MAAM,qBAAqB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,oBAAoB,CAAC;iBAC3E,iBAAiB,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;YAE3D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,OAAO,EAAE;oBACP,KAAK,EAAE,GAAG;oBACV,MAAM,EAAE,EAAE;oBACV,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACV;gBACD,YAAY,EAAE;oBACZ,WAAW,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;oBACzC,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC;iBAChD;gBACD,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;YAEH,4BAA4B,CAAC,WAAW,EAAE,CAAC;YAC3C,qBAAqB,CAAC,WAAW,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,aAAa;gBACpB,WAAW,EAAE,aAAa;gBAC1B,UAAU,EAAE,uCAAU,CAAC,OAAO;gBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,sBAAsB,CAAC,UAAU;aAC9C,CAAC;YAEF,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACjD,gBAAgB,CAAC,MAAM,CAAC,eAAe,CAAC,sBAA4C,CAAC,CAAC;YACtF,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAErE,MAAM,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;iBAClE,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAErC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAC9C,sCAAsC,EACtC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,gBAAgB;gBACvB,MAAM,EAAE,SAAS;aAClB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\application\\services\\__tests__\\threat-intelligence.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { NotFoundException, ConflictException } from '@nestjs/common';\r\n\r\nimport { ThreatIntelligenceService } from '../threat-intelligence.service';\r\nimport { ThreatIntelligence, ThreatType, ThreatSeverity, ThreatStatus, ThreatConfidence } from '../../../domain/entities/threat-intelligence.entity';\r\nimport { IndicatorOfCompromise } from '../../../domain/entities/indicator-of-compromise.entity';\r\nimport { ThreatActor } from '../../../domain/entities/threat-actor.entity';\r\nimport { ThreatCampaign } from '../../../domain/entities/threat-campaign.entity';\r\nimport { LoggerService } from '../../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../../infrastructure/notification/notification.service';\r\n\r\ndescribe('ThreatIntelligenceService', () => {\r\n  let service: ThreatIntelligenceService;\r\n  let threatRepository: jest.Mocked<Repository<ThreatIntelligence>>;\r\n  let iocRepository: jest.Mocked<Repository<IndicatorOfCompromise>>;\r\n  let actorRepository: jest.Mocked<Repository<ThreatActor>>;\r\n  let campaignRepository: jest.Mocked<Repository<ThreatCampaign>>;\r\n  let loggerService: jest.Mocked<LoggerService>;\r\n  let auditService: jest.Mocked<AuditService>;\r\n  let notificationService: jest.Mocked<NotificationService>;\r\n  let configService: jest.Mocked<ConfigService>;\r\n\r\n  const mockThreatIntelligence: Partial<ThreatIntelligence> = {\r\n    id: '123e4567-e89b-12d3-a456-426614174000',\r\n    title: 'Test Threat Intelligence',\r\n    description: 'Test description',\r\n    threatType: ThreatType.APT,\r\n    severity: ThreatSeverity.HIGH,\r\n    confidence: ThreatConfidence.HIGH,\r\n    status: ThreatStatus.ACTIVE,\r\n    firstSeen: new Date('2023-01-01'),\r\n    dataSource: {\r\n      name: 'Test Source',\r\n      type: 'commercial',\r\n      reliability: 'A',\r\n      url: 'https://test.com',\r\n      lastUpdated: new Date(),\r\n      confidence: ThreatConfidence.HIGH,\r\n    },\r\n    observationCount: 0,\r\n    isIoc: false,\r\n    isAttributed: false,\r\n    calculateRiskScore: jest.fn().mockReturnValue(7.5),\r\n    recordObservation: jest.fn(),\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const mockRepository = {\r\n      create: jest.fn(),\r\n      save: jest.fn(),\r\n      findOne: jest.fn(),\r\n      find: jest.fn(),\r\n      remove: jest.fn(),\r\n      count: jest.fn(),\r\n      createQueryBuilder: jest.fn(),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        ThreatIntelligenceService,\r\n        {\r\n          provide: getRepositoryToken(ThreatIntelligence),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(IndicatorOfCompromise),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(ThreatActor),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(ThreatCampaign),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: {\r\n            error: jest.fn(),\r\n            warn: jest.fn(),\r\n            log: jest.fn(),\r\n            debug: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: {\r\n            logUserAction: jest.fn(),\r\n            logSecurityEvent: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: NotificationService,\r\n          useValue: {\r\n            sendCriticalThreatAlert: jest.fn(),\r\n            sendUserNotification: jest.fn(),\r\n            sendRoleNotification: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: ConfigService,\r\n          useValue: {\r\n            get: jest.fn(),\r\n          },\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<ThreatIntelligenceService>(ThreatIntelligenceService);\r\n    threatRepository = module.get(getRepositoryToken(ThreatIntelligence));\r\n    iocRepository = module.get(getRepositoryToken(IndicatorOfCompromise));\r\n    actorRepository = module.get(getRepositoryToken(ThreatActor));\r\n    campaignRepository = module.get(getRepositoryToken(ThreatCampaign));\r\n    loggerService = module.get(LoggerService);\r\n    auditService = module.get(AuditService);\r\n    notificationService = module.get(NotificationService);\r\n    configService = module.get(ConfigService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('createThreatIntelligence', () => {\r\n    it('should create threat intelligence successfully', async () => {\r\n      const createData = {\r\n        title: 'New Threat',\r\n        description: 'New threat description',\r\n        threatType: ThreatType.MALWARE,\r\n        severity: ThreatSeverity.MEDIUM,\r\n        firstSeen: new Date(),\r\n        dataSource: mockThreatIntelligence.dataSource,\r\n      };\r\n\r\n      threatRepository.findOne.mockResolvedValue(null);\r\n      threatRepository.create.mockReturnValue(mockThreatIntelligence as ThreatIntelligence);\r\n      threatRepository.save.mockResolvedValue(mockThreatIntelligence as ThreatIntelligence);\r\n\r\n      const result = await service.createThreatIntelligence(createData, 'user123');\r\n\r\n      expect(threatRepository.findOne).toHaveBeenCalledWith({\r\n        where: {\r\n          title: createData.title,\r\n          dataSource: createData.dataSource,\r\n        },\r\n      });\r\n      expect(threatRepository.create).toHaveBeenCalledWith(expect.objectContaining({\r\n        ...createData,\r\n        status: ThreatStatus.ACTIVE,\r\n        confidence: ThreatConfidence.MEDIUM,\r\n        severity: ThreatSeverity.MEDIUM,\r\n        observationCount: 0,\r\n        isIoc: false,\r\n        isAttributed: false,\r\n      }));\r\n      expect(threatRepository.save).toHaveBeenCalled();\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user123',\r\n        'create',\r\n        'threat_intelligence',\r\n        mockThreatIntelligence.id,\r\n        expect.any(Object),\r\n      );\r\n      expect(result).toEqual(mockThreatIntelligence);\r\n    });\r\n\r\n    it('should throw ConflictException for duplicate threat intelligence', async () => {\r\n      const createData = {\r\n        title: 'Existing Threat',\r\n        description: 'Description',\r\n        threatType: ThreatType.MALWARE,\r\n        firstSeen: new Date(),\r\n        dataSource: mockThreatIntelligence.dataSource,\r\n      };\r\n\r\n      threatRepository.findOne.mockResolvedValue(mockThreatIntelligence as ThreatIntelligence);\r\n\r\n      await expect(service.createThreatIntelligence(createData, 'user123'))\r\n        .rejects.toThrow(ConflictException);\r\n\r\n      expect(threatRepository.create).not.toHaveBeenCalled();\r\n      expect(threatRepository.save).not.toHaveBeenCalled();\r\n    });\r\n\r\n    it('should send critical threat alert for critical severity', async () => {\r\n      const createData = {\r\n        title: 'Critical Threat',\r\n        description: 'Critical threat description',\r\n        threatType: ThreatType.APT,\r\n        severity: ThreatSeverity.CRITICAL,\r\n        firstSeen: new Date(),\r\n        dataSource: mockThreatIntelligence.dataSource,\r\n      };\r\n\r\n      const criticalThreat = {\r\n        ...mockThreatIntelligence,\r\n        severity: ThreatSeverity.CRITICAL,\r\n      };\r\n\r\n      threatRepository.findOne.mockResolvedValue(null);\r\n      threatRepository.create.mockReturnValue(criticalThreat as ThreatIntelligence);\r\n      threatRepository.save.mockResolvedValue(criticalThreat as ThreatIntelligence);\r\n\r\n      await service.createThreatIntelligence(createData, 'user123');\r\n\r\n      expect(notificationService.sendCriticalThreatAlert).toHaveBeenCalledWith({\r\n        threatId: criticalThreat.id,\r\n        title: criticalThreat.title,\r\n        threatType: criticalThreat.threatType,\r\n        severity: criticalThreat.severity,\r\n        dataSource: criticalThreat.dataSource.name,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('getThreatIntelligenceById', () => {\r\n    it('should return threat intelligence by ID', async () => {\r\n      threatRepository.findOne.mockResolvedValue(mockThreatIntelligence as ThreatIntelligence);\r\n\r\n      const result = await service.getThreatIntelligenceById('123e4567-e89b-12d3-a456-426614174000');\r\n\r\n      expect(threatRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: '123e4567-e89b-12d3-a456-426614174000' },\r\n        relations: ['indicators', 'threatActor', 'threatCampaign'],\r\n      });\r\n      expect(result).toEqual(mockThreatIntelligence);\r\n    });\r\n\r\n    it('should throw NotFoundException when threat intelligence not found', async () => {\r\n      threatRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(service.getThreatIntelligenceById('nonexistent'))\r\n        .rejects.toThrow(NotFoundException);\r\n    });\r\n  });\r\n\r\n  describe('searchThreatIntelligence', () => {\r\n    it('should search threat intelligence with criteria', async () => {\r\n      const mockQueryBuilder = {\r\n        leftJoinAndSelect: jest.fn().mockReturnThis(),\r\n        andWhere: jest.fn().mockReturnThis(),\r\n        orderBy: jest.fn().mockReturnThis(),\r\n        addOrderBy: jest.fn().mockReturnThis(),\r\n        skip: jest.fn().mockReturnThis(),\r\n        take: jest.fn().mockReturnThis(),\r\n        getManyAndCount: jest.fn().mockResolvedValue([[mockThreatIntelligence], 1]),\r\n      };\r\n\r\n      threatRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);\r\n\r\n      const criteria = {\r\n        page: 1,\r\n        limit: 10,\r\n        threatTypes: [ThreatType.APT],\r\n        severities: [ThreatSeverity.HIGH],\r\n        searchText: 'test',\r\n      };\r\n\r\n      const result = await service.searchThreatIntelligence(criteria);\r\n\r\n      expect(threatRepository.createQueryBuilder).toHaveBeenCalledWith('threat');\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        'threat.threatType IN (:...threatTypes)',\r\n        { threatTypes: [ThreatType.APT] },\r\n      );\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        'threat.severity IN (:...severities)',\r\n        { severities: [ThreatSeverity.HIGH] },\r\n      );\r\n      expect(result).toEqual({\r\n        threats: [mockThreatIntelligence],\r\n        total: 1,\r\n        page: 1,\r\n        totalPages: 1,\r\n      });\r\n    });\r\n\r\n    it('should handle empty search results', async () => {\r\n      const mockQueryBuilder = {\r\n        leftJoinAndSelect: jest.fn().mockReturnThis(),\r\n        andWhere: jest.fn().mockReturnThis(),\r\n        orderBy: jest.fn().mockReturnThis(),\r\n        addOrderBy: jest.fn().mockReturnThis(),\r\n        skip: jest.fn().mockReturnThis(),\r\n        take: jest.fn().mockReturnThis(),\r\n        getManyAndCount: jest.fn().mockResolvedValue([[], 0]),\r\n      };\r\n\r\n      threatRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);\r\n\r\n      const result = await service.searchThreatIntelligence({ page: 1, limit: 10 });\r\n\r\n      expect(result).toEqual({\r\n        threats: [],\r\n        total: 0,\r\n        page: 1,\r\n        totalPages: 0,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('updateThreatIntelligence', () => {\r\n    it('should update threat intelligence successfully', async () => {\r\n      const updateData = {\r\n        title: 'Updated Title',\r\n        severity: ThreatSeverity.CRITICAL,\r\n      };\r\n\r\n      const existingThreat = { ...mockThreatIntelligence };\r\n      const updatedThreat = { ...existingThreat, ...updateData };\r\n\r\n      threatRepository.findOne.mockResolvedValue(existingThreat as ThreatIntelligence);\r\n      threatRepository.save.mockResolvedValue(updatedThreat as ThreatIntelligence);\r\n\r\n      const result = await service.updateThreatIntelligence(\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        updateData,\r\n        'user123',\r\n      );\r\n\r\n      expect(threatRepository.save).toHaveBeenCalledWith(\r\n        expect.objectContaining(updateData),\r\n      );\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user123',\r\n        'update',\r\n        'threat_intelligence',\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        expect.objectContaining({\r\n          title: existingThreat.title,\r\n          changes: expect.any(Object),\r\n        }),\r\n      );\r\n      expect(result).toEqual(updatedThreat);\r\n    });\r\n\r\n    it('should recalculate risk score when relevant fields change', async () => {\r\n      const updateData = {\r\n        severity: ThreatSeverity.CRITICAL,\r\n        confidence: ThreatConfidence.HIGH,\r\n      };\r\n\r\n      const existingThreat = {\r\n        ...mockThreatIntelligence,\r\n        calculateRiskScore: jest.fn().mockReturnValue(9.0),\r\n      };\r\n\r\n      threatRepository.findOne.mockResolvedValue(existingThreat as ThreatIntelligence);\r\n      threatRepository.save.mockResolvedValue(existingThreat as ThreatIntelligence);\r\n\r\n      await service.updateThreatIntelligence(\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        updateData,\r\n        'user123',\r\n      );\r\n\r\n      expect(existingThreat.calculateRiskScore).toHaveBeenCalled();\r\n    });\r\n  });\r\n\r\n  describe('deleteThreatIntelligence', () => {\r\n    it('should delete threat intelligence successfully', async () => {\r\n      threatRepository.findOne.mockResolvedValue(mockThreatIntelligence as ThreatIntelligence);\r\n      threatRepository.remove.mockResolvedValue(mockThreatIntelligence as ThreatIntelligence);\r\n\r\n      await service.deleteThreatIntelligence('123e4567-e89b-12d3-a456-426614174000', 'user123');\r\n\r\n      expect(threatRepository.remove).toHaveBeenCalledWith(mockThreatIntelligence);\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user123',\r\n        'delete',\r\n        'threat_intelligence',\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        expect.objectContaining({\r\n          title: mockThreatIntelligence.title,\r\n          threatType: mockThreatIntelligence.threatType,\r\n        }),\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('recordObservation', () => {\r\n    it('should record observation and update threat intelligence', async () => {\r\n      const threat = {\r\n        ...mockThreatIntelligence,\r\n        recordObservation: jest.fn(),\r\n        calculateRiskScore: jest.fn().mockReturnValue(8.0),\r\n      };\r\n\r\n      threatRepository.findOne.mockResolvedValue(threat as ThreatIntelligence);\r\n      threatRepository.save.mockResolvedValue(threat as ThreatIntelligence);\r\n\r\n      const context = { source: 'test', timestamp: new Date() };\r\n      const result = await service.recordObservation('123e4567-e89b-12d3-a456-426614174000', context);\r\n\r\n      expect(threat.recordObservation).toHaveBeenCalled();\r\n      expect(threat.calculateRiskScore).toHaveBeenCalled();\r\n      expect(threatRepository.save).toHaveBeenCalledWith(threat);\r\n      expect(result).toEqual(threat);\r\n    });\r\n  });\r\n\r\n  describe('getDashboardData', () => {\r\n    it('should return dashboard data', async () => {\r\n      threatRepository.count\r\n        .mockResolvedValueOnce(100) // total\r\n        .mockResolvedValueOnce(80)  // active\r\n        .mockResolvedValueOnce(15)  // critical\r\n        .mockResolvedValueOnce(5);  // recent\r\n\r\n      // Mock private methods by spying on the service\r\n      const getThreatTypeDistributionSpy = jest.spyOn(service as any, 'getThreatTypeDistribution')\r\n        .mockResolvedValue([{ type: 'apt', count: 25 }]);\r\n      const getTopThreatActorsSpy = jest.spyOn(service as any, 'getTopThreatActors')\r\n        .mockResolvedValue([{ name: 'APT29', threatCount: 10 }]);\r\n\r\n      const result = await service.getDashboardData();\r\n\r\n      expect(result).toEqual({\r\n        summary: {\r\n          total: 100,\r\n          active: 80,\r\n          critical: 15,\r\n          recent: 5,\r\n        },\r\n        distribution: {\r\n          threatTypes: [{ type: 'apt', count: 25 }],\r\n          topActors: [{ name: 'APT29', threatCount: 10 }],\r\n        },\r\n        timestamp: expect.any(Date),\r\n      });\r\n\r\n      getThreatTypeDistributionSpy.mockRestore();\r\n      getTopThreatActorsSpy.mockRestore();\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle database errors gracefully', async () => {\r\n      const createData = {\r\n        title: 'Test Threat',\r\n        description: 'Description',\r\n        threatType: ThreatType.MALWARE,\r\n        firstSeen: new Date(),\r\n        dataSource: mockThreatIntelligence.dataSource,\r\n      };\r\n\r\n      threatRepository.findOne.mockResolvedValue(null);\r\n      threatRepository.create.mockReturnValue(mockThreatIntelligence as ThreatIntelligence);\r\n      threatRepository.save.mockRejectedValue(new Error('Database error'));\r\n\r\n      await expect(service.createThreatIntelligence(createData, 'user123'))\r\n        .rejects.toThrow('Database error');\r\n\r\n      expect(loggerService.error).toHaveBeenCalledWith(\r\n        'Failed to create threat intelligence',\r\n        expect.objectContaining({\r\n          error: 'Database error',\r\n          userId: 'user123',\r\n        }),\r\n      );\r\n    });\r\n  });\r\n});\r\n"], "version": 3}