{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\seeds\\production.seed.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,2CAAwC;AACxC,+CAAiC;AAEjC;;;GAGG;AACH,MAAa,gBAAgB;IAG3B,YAA6B,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;QAFlC,WAAM,GAAG,IAAI,eAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAEN,CAAC;IAEvD;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAE3D,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAErC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACtD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAE7C,MAAM,KAAK,GAAG;YACZ;gBACE,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,6CAA6C;gBAC1D,WAAW,EAAE;oBACX,cAAc;oBACd,SAAS;oBACT,SAAS;oBACT,mBAAmB;oBACnB,mBAAmB;oBACnB,UAAU;oBACV,SAAS;oBACT,WAAW;oBACX,SAAS;oBACT,iBAAiB;iBAClB;gBACD,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,WAAW,EAAE,sBAAsB;gBACnC,WAAW,EAAE;oBACX,YAAY,EAAE,aAAa;oBAC3B,YAAY;oBACZ,mBAAmB;oBACnB,mBAAmB;oBACnB,UAAU;oBACV,SAAS;oBACT,WAAW;oBACX,YAAY;iBACb;gBACD,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,0CAA0C;gBACvD,WAAW,EAAE;oBACX,sBAAsB,EAAE,uBAAuB;oBAC/C,sBAAsB,EAAE,uBAAuB;oBAC/C,aAAa,EAAE,cAAc;oBAC7B,YAAY,EAAE,aAAa;oBAC3B,cAAc,EAAE,eAAe;iBAChC;gBACD,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,+BAA+B;gBAC5C,WAAW,EAAE;oBACX,sBAAsB,EAAE,uBAAuB;oBAC/C,sBAAsB;oBACtB,aAAa;oBACb,cAAc;iBACf;gBACD,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,wCAAwC;gBACrD,WAAW,EAAE;oBACX,sBAAsB;oBACtB,sBAAsB;oBACtB,aAAa;oBACb,YAAY;oBACZ,cAAc;oBACd,YAAY;iBACb;gBACD,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,4BAA4B;gBACzC,WAAW,EAAE;oBACX,sBAAsB;oBACtB,sBAAsB;oBACtB,aAAa;oBACb,cAAc;iBACf;gBACD,cAAc,EAAE,IAAI;aACrB;SACF,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;OAQ3B,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAC3F,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,KAAK,CAAC,MAAM,eAAe,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;QAExD,iCAAiC;QACjC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;KAElD,CAAC,CAAC;QAEH,IAAI,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAChF,OAAO;QACT,CAAC;QAED,oDAAoD;QACpD,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,cAAc,CAAC;QAC7E,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;QAE5D,MAAM,YAAY,GAAG;YACnB,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,sBAAsB;YAChE,aAAa,EAAE,YAAY;YAC3B,UAAU,EAAE,QAAQ;YACpB,SAAS,EAAE,eAAe;YAC1B,IAAI,EAAE,aAAa;YACnB,SAAS,EAAE,IAAI;YACf,cAAc,EAAE,IAAI;SACrB,CAAC;QAEF,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;KAG3B,EAAE;YACD,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,aAAa,EAAE,YAAY,CAAC,UAAU;YACvE,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,SAAS;YACjE,YAAY,CAAC,cAAc;SAC5B,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;YAC7C,KAAK,EAAE,YAAY,CAAC,KAAK;YACzB,OAAO,EAAE,kEAAkE;SAC5E,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;QAEpD,MAAM,IAAI,GAAG;YACX,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,wDAAwD,EAAE,KAAK,EAAE,SAAS,EAAE;YAC7G,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,EAAE,+BAA+B,EAAE,KAAK,EAAE,SAAS,EAAE;YACzF,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,0BAA0B,EAAE,KAAK,EAAE,SAAS,EAAE;YAC9E,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,iCAAiC,EAAE,KAAK,EAAE,SAAS,EAAE;YACvF,EAAE,IAAI,EAAE,aAAa,EAAE,WAAW,EAAE,6BAA6B,EAAE,KAAK,EAAE,SAAS,EAAE;YACrF,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,uCAAuC,EAAE,KAAK,EAAE,SAAS,EAAE;YACxF,EAAE,IAAI,EAAE,mBAAmB,EAAE,WAAW,EAAE,oCAAoC,EAAE,KAAK,EAAE,SAAS,EAAE;YAClG,EAAE,IAAI,EAAE,sBAAsB,EAAE,WAAW,EAAE,+BAA+B,EAAE,KAAK,EAAE,SAAS,EAAE;YAChG,EAAE,IAAI,EAAE,kBAAkB,EAAE,WAAW,EAAE,iCAAiC,EAAE,KAAK,EAAE,SAAS,EAAE;YAC9F,EAAE,IAAI,EAAE,aAAa,EAAE,WAAW,EAAE,qCAAqC,EAAE,KAAK,EAAE,SAAS,EAAE;YAC7F,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,mCAAmC,EAAE,KAAK,EAAE,SAAS,EAAE;YAC9F,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,iCAAiC,EAAE,KAAK,EAAE,SAAS,EAAE;YAC5F,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,0BAA0B,EAAE,KAAK,EAAE,SAAS,EAAE;YAC/E,EAAE,IAAI,EAAE,qBAAqB,EAAE,WAAW,EAAE,sCAAsC,EAAE,KAAK,EAAE,SAAS,EAAE;YACtG,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,2BAA2B,EAAE,KAAK,EAAE,SAAS,EAAE;YACjF,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAE,KAAK,EAAE,SAAS,EAAE;YAC7E,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,yBAAyB,EAAE,KAAK,EAAE,SAAS,EAAE;YAC9E,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,yBAAyB,EAAE,KAAK,EAAE,SAAS,EAAE;YAC9E,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,oCAAoC,EAAE,KAAK,EAAE,SAAS,EAAE;YAC3F,EAAE,IAAI,EAAE,kBAAkB,EAAE,WAAW,EAAE,4BAA4B,EAAE,KAAK,EAAE,SAAS,EAAE;SAC1F,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;OAM3B,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,MAAM,sBAAsB,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB;QACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAErD,wDAAwD;QACxD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;KAW3B,CAAC,CAAC;QAEH,yBAAyB;QACzB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;KAI3B,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG;YACrB;gBACE,GAAG,EAAE,gBAAgB;gBACrB,KAAK,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE;gBAChD,WAAW,EAAE,4BAA4B;gBACzC,QAAQ,EAAE,QAAQ;gBAClB,YAAY,EAAE,KAAK;aACpB;YACD;gBACE,GAAG,EAAE,0BAA0B;gBAC/B,KAAK,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE;gBAC9B,WAAW,EAAE,iCAAiC;gBAC9C,QAAQ,EAAE,UAAU;gBACpB,YAAY,EAAE,KAAK;aACpB;YACD;gBACE,GAAG,EAAE,0BAA0B;gBAC/B,KAAK,EAAE;oBACL,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,IAAI;oBACvB,iBAAiB,EAAE,IAAI;oBACvB,eAAe,EAAE,IAAI;oBACrB,qBAAqB,EAAE,IAAI;oBAC3B,YAAY,EAAE,EAAE;iBACjB;gBACD,WAAW,EAAE,8BAA8B;gBAC3C,QAAQ,EAAE,UAAU;gBACpB,YAAY,EAAE,KAAK;aACpB;YACD;gBACE,GAAG,EAAE,4BAA4B;gBACjC,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE;gBAChD,WAAW,EAAE,6BAA6B;gBAC1C,QAAQ,EAAE,QAAQ;gBAClB,YAAY,EAAE,KAAK;aACpB;YACD;gBACE,GAAG,EAAE,2BAA2B;gBAChC,KAAK,EAAE;oBACL,iCAAiC,EAAE,EAAE;oBACrC,2BAA2B,EAAE,CAAC;oBAC9B,8BAA8B,EAAE,GAAG;iBACpC;gBACD,WAAW,EAAE,4BAA4B;gBACzC,QAAQ,EAAE,UAAU;gBACpB,YAAY,EAAE,KAAK;aACpB;YACD;gBACE,GAAG,EAAE,8BAA8B;gBACnC,KAAK,EAAE;oBACL,eAAe,EAAE,GAAG;oBACpB,UAAU,EAAE,IAAI,EAAE,UAAU;oBAC5B,mBAAmB,EAAE,EAAE;oBACvB,WAAW,EAAE,EAAE;iBAChB;gBACD,WAAW,EAAE,yBAAyB;gBACtC,QAAQ,EAAE,WAAW;gBACrB,YAAY,EAAE,KAAK;aACpB;SACF,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE,CAAC;YACpC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;OAS3B,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;QAC3G,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,cAAc,CAAC,MAAM,wBAAwB,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAE1D,IAAI,CAAC;YACH,+BAA+B;YAC/B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;OAE7C,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBACrC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBACpD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,2BAA2B;YAC3B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;OAE9C,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBAC1C,OAAO,KAAK,CAAC;YACf,CAAC;YAED,8BAA8B;YAC9B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;OAE/C,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;gBACnD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,EAAE;gBACjE,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAhXD,4CAgXC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\seeds\\production.seed.ts"], "sourcesContent": ["import { DataSource } from 'typeorm';\r\nimport { Logger } from '@nestjs/common';\r\nimport * as bcrypt from 'bcrypt';\r\n\r\n/**\r\n * Production environment database seeder\r\n * Creates essential system data for production deployment\r\n */\r\nexport class ProductionSeeder {\r\n  private readonly logger = new Logger(ProductionSeeder.name);\r\n\r\n  constructor(private readonly dataSource: DataSource) {}\r\n\r\n  /**\r\n   * Execute production seeds\r\n   */\r\n  async seed(): Promise<void> {\r\n    this.logger.log('Starting production database seeding...');\r\n\r\n    try {\r\n      await this.seedSystemRoles();\r\n      await this.seedDefaultAdmin();\r\n      await this.seedSecurityEventTags();\r\n      await this.seedSystemConfiguration();\r\n\r\n      this.logger.log('Production database seeding completed successfully');\r\n    } catch (error) {\r\n      this.logger.error('Production database seeding failed', {\r\n        error: error.message,\r\n        stack: error.stack,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Seed essential system roles\r\n   */\r\n  private async seedSystemRoles(): Promise<void> {\r\n    this.logger.debug('Seeding system roles...');\r\n\r\n    const roles = [\r\n      {\r\n        name: 'super_admin',\r\n        description: 'Super administrator with full system access',\r\n        permissions: [\r\n          'system:admin',\r\n          'users:*',\r\n          'roles:*',\r\n          'security-events:*',\r\n          'vulnerabilities:*',\r\n          'assets:*',\r\n          'scans:*',\r\n          'reports:*',\r\n          'audit:*',\r\n          'configuration:*'\r\n        ],\r\n        is_system_role: true,\r\n      },\r\n      {\r\n        name: 'admin',\r\n        description: 'System administrator',\r\n        permissions: [\r\n          'users:read', 'users:write',\r\n          'roles:read',\r\n          'security-events:*',\r\n          'vulnerabilities:*',\r\n          'assets:*',\r\n          'scans:*',\r\n          'reports:*',\r\n          'audit:read'\r\n        ],\r\n        is_system_role: true,\r\n      },\r\n      {\r\n        name: 'security_analyst',\r\n        description: 'Security analyst with operational access',\r\n        permissions: [\r\n          'security-events:read', 'security-events:write',\r\n          'vulnerabilities:read', 'vulnerabilities:write',\r\n          'assets:read', 'assets:write',\r\n          'scans:read', 'scans:write',\r\n          'reports:read', 'reports:write'\r\n        ],\r\n        is_system_role: true,\r\n      },\r\n      {\r\n        name: 'incident_responder',\r\n        description: 'Incident response team member',\r\n        permissions: [\r\n          'security-events:read', 'security-events:write',\r\n          'vulnerabilities:read',\r\n          'assets:read',\r\n          'reports:read'\r\n        ],\r\n        is_system_role: true,\r\n      },\r\n      {\r\n        name: 'auditor',\r\n        description: 'Security auditor with read-only access',\r\n        permissions: [\r\n          'security-events:read',\r\n          'vulnerabilities:read',\r\n          'assets:read',\r\n          'scans:read',\r\n          'reports:read',\r\n          'audit:read'\r\n        ],\r\n        is_system_role: true,\r\n      },\r\n      {\r\n        name: 'viewer',\r\n        description: 'Read-only dashboard access',\r\n        permissions: [\r\n          'security-events:read',\r\n          'vulnerabilities:read',\r\n          'assets:read',\r\n          'reports:read'\r\n        ],\r\n        is_system_role: true,\r\n      },\r\n    ];\r\n\r\n    for (const role of roles) {\r\n      await this.dataSource.query(`\r\n        INSERT INTO roles (name, description, permissions, is_system_role)\r\n        VALUES ($1, $2, $3, $4)\r\n        ON CONFLICT (name) DO UPDATE SET\r\n          description = EXCLUDED.description,\r\n          permissions = EXCLUDED.permissions,\r\n          is_system_role = EXCLUDED.is_system_role,\r\n          updated_at = CURRENT_TIMESTAMP\r\n      `, [role.name, role.description, JSON.stringify(role.permissions), role.is_system_role]);\r\n    }\r\n\r\n    this.logger.debug(`Seeded ${roles.length} system roles`);\r\n  }\r\n\r\n  /**\r\n   * Seed default admin user (if not exists)\r\n   */\r\n  private async seedDefaultAdmin(): Promise<void> {\r\n    this.logger.debug('Checking for default admin user...');\r\n\r\n    // Check if any admin users exist\r\n    const existingAdmins = await this.dataSource.query(`\r\n      SELECT COUNT(*) as count FROM users WHERE role IN ('super_admin', 'admin')\r\n    `);\r\n\r\n    if (parseInt(existingAdmins[0].count) > 0) {\r\n      this.logger.debug('Admin users already exist, skipping default admin creation');\r\n      return;\r\n    }\r\n\r\n    // Create default admin only if no admin users exist\r\n    const defaultPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'ChangeMe123!';\r\n    const passwordHash = await bcrypt.hash(defaultPassword, 12);\r\n\r\n    const defaultAdmin = {\r\n      email: process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>',\r\n      password_hash: passwordHash,\r\n      first_name: 'System',\r\n      last_name: 'Administrator',\r\n      role: 'super_admin',\r\n      is_active: true,\r\n      email_verified: true,\r\n    };\r\n\r\n    await this.dataSource.query(`\r\n      INSERT INTO users (email, password_hash, first_name, last_name, role, is_active, email_verified)\r\n      VALUES ($1, $2, $3, $4, $5, $6, $7)\r\n    `, [\r\n      defaultAdmin.email, defaultAdmin.password_hash, defaultAdmin.first_name,\r\n      defaultAdmin.last_name, defaultAdmin.role, defaultAdmin.is_active,\r\n      defaultAdmin.email_verified\r\n    ]);\r\n\r\n    this.logger.warn('Default admin user created', {\r\n      email: defaultAdmin.email,\r\n      message: 'Please change the default password immediately after first login'\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Seed essential security event tags\r\n   */\r\n  private async seedSecurityEventTags(): Promise<void> {\r\n    this.logger.debug('Seeding security event tags...');\r\n\r\n    const tags = [\r\n      { name: 'critical', description: 'Critical security events requiring immediate attention', color: '#dc3545' },\r\n      { name: 'high-priority', description: 'High priority security events', color: '#fd7e14' },\r\n      { name: 'malware', description: 'Malware detection events', color: '#6f42c1' },\r\n      { name: 'intrusion', description: 'Intrusion attempts and breaches', color: '#e83e8c' },\r\n      { name: 'brute-force', description: 'Brute force attack attempts', color: '#dc3545' },\r\n      { name: 'ddos', description: 'Distributed denial of service attacks', color: '#fd7e14' },\r\n      { name: 'data-exfiltration', description: 'Potential data exfiltration events', color: '#6f42c1' },\r\n      { name: 'privilege-escalation', description: 'Privilege escalation attempts', color: '#e83e8c' },\r\n      { name: 'lateral-movement', description: 'Lateral movement within network', color: '#fd7e14' },\r\n      { name: 'persistence', description: 'Persistence mechanism establishment', color: '#6f42c1' },\r\n      { name: 'reconnaissance', description: 'Network reconnaissance activities', color: '#6c757d' },\r\n      { name: 'false-positive', description: 'Confirmed false positive events', color: '#28a745' },\r\n      { name: 'resolved', description: 'Resolved security events', color: '#17a2b8' },\r\n      { name: 'under-investigation', description: 'Events currently under investigation', color: '#ffc107' },\r\n      { name: 'automated', description: 'Automated attack patterns', color: '#6c757d' },\r\n      { name: 'manual', description: 'Manual attack activities', color: '#dc3545' },\r\n      { name: 'external', description: 'External threat sources', color: '#fd7e14' },\r\n      { name: 'internal', description: 'Internal threat sources', color: '#e83e8c' },\r\n      { name: 'compliance', description: 'Compliance-related security events', color: '#17a2b8' },\r\n      { name: 'policy-violation', description: 'Security policy violations', color: '#ffc107' },\r\n    ];\r\n\r\n    for (const tag of tags) {\r\n      await this.dataSource.query(`\r\n        INSERT INTO security_event_tags (name, description, color)\r\n        VALUES ($1, $2, $3)\r\n        ON CONFLICT (name) DO UPDATE SET\r\n          description = EXCLUDED.description,\r\n          color = EXCLUDED.color\r\n      `, [tag.name, tag.description, tag.color]);\r\n    }\r\n\r\n    this.logger.debug(`Seeded ${tags.length} security event tags`);\r\n  }\r\n\r\n  /**\r\n   * Seed system configuration\r\n   */\r\n  private async seedSystemConfiguration(): Promise<void> {\r\n    this.logger.debug('Seeding system configuration...');\r\n\r\n    // Create system configuration table if it doesn't exist\r\n    await this.dataSource.query(`\r\n      CREATE TABLE IF NOT EXISTS system_configuration (\r\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\r\n        key VARCHAR(255) UNIQUE NOT NULL,\r\n        value JSONB NOT NULL,\r\n        description TEXT,\r\n        category VARCHAR(100) NOT NULL DEFAULT 'general',\r\n        is_sensitive BOOLEAN NOT NULL DEFAULT false,\r\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,\r\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP\r\n      )\r\n    `);\r\n\r\n    // Add updated_at trigger\r\n    await this.dataSource.query(`\r\n      CREATE TRIGGER update_system_configuration_updated_at \r\n      BEFORE UPDATE ON system_configuration \r\n      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();\r\n    `);\r\n\r\n    const configurations = [\r\n      {\r\n        key: 'system.version',\r\n        value: { version: '1.0.0', build: 'production' },\r\n        description: 'System version information',\r\n        category: 'system',\r\n        is_sensitive: false\r\n      },\r\n      {\r\n        key: 'security.session_timeout',\r\n        value: { timeout_minutes: 30 },\r\n        description: 'User session timeout in minutes',\r\n        category: 'security',\r\n        is_sensitive: false\r\n      },\r\n      {\r\n        key: 'security.password_policy',\r\n        value: {\r\n          min_length: 12,\r\n          require_uppercase: true,\r\n          require_lowercase: true,\r\n          require_numbers: true,\r\n          require_special_chars: true,\r\n          max_age_days: 90\r\n        },\r\n        description: 'Password policy requirements',\r\n        category: 'security',\r\n        is_sensitive: false\r\n      },\r\n      {\r\n        key: 'alerts.email_notifications',\r\n        value: { enabled: true, smtp_configured: false },\r\n        description: 'Email notification settings',\r\n        category: 'alerts',\r\n        is_sensitive: false\r\n      },\r\n      {\r\n        key: 'scanning.default_schedule',\r\n        value: { \r\n          vulnerability_scan_interval_hours: 24,\r\n          network_scan_interval_hours: 6,\r\n          compliance_scan_interval_hours: 168\r\n        },\r\n        description: 'Default scanning schedules',\r\n        category: 'scanning',\r\n        is_sensitive: false\r\n      },\r\n      {\r\n        key: 'retention.log_retention_days',\r\n        value: { \r\n          security_events: 365,\r\n          audit_logs: 2555, // 7 years\r\n          vulnerability_scans: 90,\r\n          system_logs: 30\r\n        },\r\n        description: 'Data retention policies',\r\n        category: 'retention',\r\n        is_sensitive: false\r\n      },\r\n    ];\r\n\r\n    for (const config of configurations) {\r\n      await this.dataSource.query(`\r\n        INSERT INTO system_configuration (key, value, description, category, is_sensitive)\r\n        VALUES ($1, $2, $3, $4, $5)\r\n        ON CONFLICT (key) DO UPDATE SET\r\n          value = EXCLUDED.value,\r\n          description = EXCLUDED.description,\r\n          category = EXCLUDED.category,\r\n          is_sensitive = EXCLUDED.is_sensitive,\r\n          updated_at = CURRENT_TIMESTAMP\r\n      `, [config.key, JSON.stringify(config.value), config.description, config.category, config.is_sensitive]);\r\n    }\r\n\r\n    this.logger.debug(`Seeded ${configurations.length} system configurations`);\r\n  }\r\n\r\n  /**\r\n   * Verify production seed integrity\r\n   */\r\n  async verify(): Promise<boolean> {\r\n    this.logger.log('Verifying production seed integrity...');\r\n\r\n    try {\r\n      // Verify essential roles exist\r\n      const roleCount = await this.dataSource.query(`\r\n        SELECT COUNT(*) as count FROM roles WHERE is_system_role = true\r\n      `);\r\n\r\n      if (parseInt(roleCount[0].count) < 6) {\r\n        this.logger.error('Missing essential system roles');\r\n        return false;\r\n      }\r\n\r\n      // Verify admin user exists\r\n      const adminCount = await this.dataSource.query(`\r\n        SELECT COUNT(*) as count FROM users WHERE role IN ('super_admin', 'admin')\r\n      `);\r\n\r\n      if (parseInt(adminCount[0].count) === 0) {\r\n        this.logger.error('No admin users found');\r\n        return false;\r\n      }\r\n\r\n      // Verify system configuration\r\n      const configCount = await this.dataSource.query(`\r\n        SELECT COUNT(*) as count FROM system_configuration\r\n      `);\r\n\r\n      if (parseInt(configCount[0].count) === 0) {\r\n        this.logger.error('No system configuration found');\r\n        return false;\r\n      }\r\n\r\n      this.logger.log('Production seed integrity verification passed');\r\n      return true;\r\n\r\n    } catch (error) {\r\n      this.logger.error('Production seed integrity verification failed', {\r\n        error: error.message,\r\n      });\r\n      return false;\r\n    }\r\n  }\r\n}"], "version": 3}