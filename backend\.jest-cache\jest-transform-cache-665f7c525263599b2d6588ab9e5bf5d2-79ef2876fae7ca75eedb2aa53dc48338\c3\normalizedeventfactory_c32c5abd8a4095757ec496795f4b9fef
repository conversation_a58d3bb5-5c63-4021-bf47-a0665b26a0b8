acc837d1aeb465dffe318ac9f1fbe3ec
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NormalizedEventFactory = void 0;
const normalized_event_entity_1 = require("../entities/normalized-event.entity");
const shared_kernel_1 = require("../../../../shared-kernel");
const event_type_enum_1 = require("../enums/event-type.enum");
const event_severity_enum_1 = require("../enums/event-severity.enum");
const event_status_enum_1 = require("../enums/event-status.enum");
const event_processing_status_enum_1 = require("../enums/event-processing-status.enum");
/**
 * NormalizedEvent Factory
 *
 * Factory class for creating NormalizedEvent entities with proper validation and defaults.
 * Handles complex normalization scenarios and ensures all business rules are applied.
 *
 * Key responsibilities:
 * - Create normalized events from original events
 * - Apply normalization rules and validation
 * - Calculate data quality scores
 * - Determine manual review requirements
 * - Handle batch normalization operations
 * - Manage schema versioning and compatibility
 */
class NormalizedEventFactory {
    /**
     * Create a new NormalizedEvent from an original Event
     */
    static create(options) {
        const originalEvent = options.originalEvent;
        // Build normalized event properties
        const normalizedEventProps = {
            originalEventId: originalEvent.id,
            metadata: originalEvent.metadata,
            type: options.type || originalEvent.type,
            severity: options.severity || originalEvent.severity,
            status: options.status || originalEvent.status,
            processingStatus: options.processingStatus || event_processing_status_enum_1.EventProcessingStatus.NORMALIZED,
            normalizationStatus: options.normalizationStatus || normalized_event_entity_1.NormalizationStatus.PENDING,
            originalData: originalEvent.rawData,
            normalizedData: options.normalizedData,
            title: options.title || originalEvent.title,
            description: options.description || originalEvent.description,
            tags: options.tags || originalEvent.tags,
            riskScore: options.riskScore || originalEvent.riskScore,
            confidenceLevel: options.confidenceLevel || originalEvent.confidenceLevel,
            attributes: {
                ...originalEvent.attributes,
                ...options.attributes,
            },
            correlationId: originalEvent.correlationId,
            parentEventId: originalEvent.parentEventId,
            appliedRules: options.appliedRules || [],
            schemaVersion: options.schemaVersion,
            dataQualityScore: options.dataQualityScore,
            validationErrors: options.validationErrors,
            requiresManualReview: options.forceManualReview,
            normalizationAttempts: 0,
        };
        return normalized_event_entity_1.NormalizedEvent.create(normalizedEventProps, options.id);
    }
    /**
     * Create a NormalizedEvent with automatic normalization
     */
    static createWithNormalization(originalEvent, config = {}) {
        const fullConfig = NormalizedEventFactory.getDefaultConfig(config);
        // Apply normalization rules
        const normalizationResult = NormalizedEventFactory.applyNormalizationRules(originalEvent.rawData, fullConfig.availableRules);
        // Calculate data quality score
        const dataQualityScore = NormalizedEventFactory.calculateDataQualityScore(normalizationResult, fullConfig);
        // Determine if manual review is required
        const requiresManualReview = NormalizedEventFactory.determineManualReviewRequirement(originalEvent, dataQualityScore, normalizationResult.validationErrors, fullConfig);
        return NormalizedEventFactory.create({
            originalEvent,
            normalizedData: normalizationResult.normalizedData,
            schemaVersion: fullConfig.defaultSchemaVersion,
            appliedRules: normalizationResult.appliedRules,
            normalizationStatus: normalizationResult.success
                ? normalized_event_entity_1.NormalizationStatus.COMPLETED
                : normalized_event_entity_1.NormalizationStatus.FAILED,
            dataQualityScore,
            validationErrors: normalizationResult.validationErrors,
            forceManualReview: requiresManualReview,
        });
    }
    /**
     * Create a NormalizedEvent from raw event data
     */
    static fromRawData(rawData, originalEventId, schemaVersion, config = {}) {
        const fullConfig = NormalizedEventFactory.getDefaultConfig(config);
        // Apply normalization rules to raw data
        const normalizationResult = NormalizedEventFactory.applyNormalizationRules(rawData, fullConfig.availableRules);
        // Extract basic event information from normalized data
        const eventInfo = NormalizedEventFactory.extractEventInfo(normalizationResult.normalizedData);
        // Calculate data quality score
        const dataQualityScore = NormalizedEventFactory.calculateDataQualityScore(normalizationResult, fullConfig);
        // Create minimal metadata (this would typically come from the original event)
        const metadata = NormalizedEventFactory.createMinimalMetadata();
        const normalizedEventProps = {
            originalEventId,
            metadata,
            type: eventInfo.type,
            severity: eventInfo.severity,
            status: event_status_enum_1.EventStatus.ACTIVE,
            processingStatus: event_processing_status_enum_1.EventProcessingStatus.NORMALIZED,
            normalizationStatus: normalizationResult.success
                ? normalized_event_entity_1.NormalizationStatus.COMPLETED
                : normalized_event_entity_1.NormalizationStatus.FAILED,
            originalData: rawData,
            normalizedData: normalizationResult.normalizedData,
            title: eventInfo.title,
            description: eventInfo.description,
            tags: eventInfo.tags,
            riskScore: eventInfo.riskScore,
            confidenceLevel: eventInfo.confidenceLevel,
            attributes: eventInfo.attributes,
            appliedRules: normalizationResult.appliedRules,
            schemaVersion,
            dataQualityScore,
            validationErrors: normalizationResult.validationErrors,
            normalizationAttempts: 0,
        };
        return normalized_event_entity_1.NormalizedEvent.create(normalizedEventProps);
    }
    /**
     * Create multiple NormalizedEvents in batch
     */
    static createBatch(options) {
        const startTime = Date.now();
        const successful = [];
        const failed = [];
        for (const event of options.events) {
            try {
                const normalizedEvent = NormalizedEventFactory.create({
                    originalEvent: event,
                    normalizedData: NormalizedEventFactory.normalizeEventData(event.rawData),
                    schemaVersion: options.schemaVersion,
                    appliedRules: options.rules,
                });
                successful.push(normalizedEvent);
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                failed.push({ event, error: errorMessage });
                if (options.stopOnFailure) {
                    break;
                }
            }
        }
        const processingTimeMs = Date.now() - startTime;
        return {
            successful,
            failed,
            summary: {
                total: options.events.length,
                successful: successful.length,
                failed: failed.length,
                processingTimeMs,
            },
        };
    }
    /**
     * Create a NormalizedEvent for testing purposes
     */
    static createForTesting(overrides = {}) {
        // Create a mock original event if not provided
        const mockOriginalEvent = overrides.originalEvent || NormalizedEventFactory.createMockEvent();
        const defaultOptions = {
            originalEvent: mockOriginalEvent,
            normalizedData: {
                event_type: 'security_alert',
                severity: 'medium',
                title: 'Test Security Event',
                timestamp: new Date().toISOString(),
                source: 'test-source',
                normalized: true,
            },
            schemaVersion: NormalizedEventFactory.DEFAULT_SCHEMA_VERSION,
            appliedRules: [],
            normalizationStatus: normalized_event_entity_1.NormalizationStatus.COMPLETED,
            dataQualityScore: 85,
        };
        return NormalizedEventFactory.create({
            ...defaultOptions,
            ...overrides,
        });
    }
    // Private helper methods
    static getDefaultConfig(config) {
        return {
            defaultSchemaVersion: NormalizedEventFactory.DEFAULT_SCHEMA_VERSION,
            availableRules: [],
            minDataQualityThreshold: NormalizedEventFactory.DEFAULT_MIN_DATA_QUALITY,
            requireManualReviewForHighRisk: true,
            requireManualReviewForCritical: true,
            maxValidationErrors: NormalizedEventFactory.DEFAULT_MAX_VALIDATION_ERRORS,
            normalizationTimeoutMs: NormalizedEventFactory.DEFAULT_NORMALIZATION_TIMEOUT,
            ...config,
        };
    }
    static applyNormalizationRules(rawData, rules) {
        const normalizedData = { ...rawData };
        const appliedRules = [];
        const validationErrors = [];
        // Sort rules by priority (higher priority first)
        const sortedRules = [...rules].sort((a, b) => b.priority - a.priority);
        for (const rule of sortedRules) {
            try {
                // Apply rule logic (this would be implemented based on specific rule types)
                const ruleResult = NormalizedEventFactory.applyRule(normalizedData, rule);
                if (ruleResult.success) {
                    appliedRules.push(rule);
                    Object.assign(normalizedData, ruleResult.data);
                }
                else if (rule.required) {
                    validationErrors.push(`Required rule '${rule.name}' failed: ${ruleResult.error}`);
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                if (rule.required) {
                    validationErrors.push(`Required rule '${rule.name}' threw error: ${errorMessage}`);
                }
            }
        }
        return {
            success: validationErrors.length === 0,
            normalizedData,
            appliedRules,
            validationErrors,
        };
    }
    static applyRule(data, rule) {
        // This is a simplified implementation - in practice, this would be more sophisticated
        switch (rule.id) {
            case 'timestamp_normalization':
                return NormalizedEventFactory.normalizeTimestamp(data);
            case 'severity_normalization':
                return NormalizedEventFactory.normalizeSeverity(data);
            case 'field_mapping':
                return NormalizedEventFactory.mapFields(data, rule.config);
            default:
                return { success: true, data };
        }
    }
    static normalizeTimestamp(data) {
        const timestampFields = ['timestamp', '@timestamp', 'event_time', 'created_at'];
        for (const field of timestampFields) {
            if (data[field]) {
                try {
                    const normalizedTimestamp = new Date(data[field]).toISOString();
                    return {
                        success: true,
                        data: { ...data, normalized_timestamp: normalizedTimestamp }
                    };
                }
                catch (error) {
                    return { success: false, error: `Invalid timestamp format in field ${field}` };
                }
            }
        }
        return { success: false, error: 'No valid timestamp field found' };
    }
    static normalizeSeverity(data) {
        const severityFields = ['severity', 'level', 'priority'];
        for (const field of severityFields) {
            if (data[field]) {
                const severity = String(data[field]).toLowerCase();
                let normalizedSeverity;
                if (severity.includes('critical') || severity.includes('fatal')) {
                    normalizedSeverity = 'critical';
                }
                else if (severity.includes('high') || severity.includes('error')) {
                    normalizedSeverity = 'high';
                }
                else if (severity.includes('medium') || severity.includes('warn')) {
                    normalizedSeverity = 'medium';
                }
                else if (severity.includes('low') || severity.includes('info')) {
                    normalizedSeverity = 'low';
                }
                else {
                    normalizedSeverity = 'medium'; // default
                }
                return {
                    success: true,
                    data: { ...data, normalized_severity: normalizedSeverity }
                };
            }
        }
        return { success: false, error: 'No severity field found' };
    }
    static mapFields(data, config) {
        if (!config || !config.fieldMappings) {
            return { success: true, data };
        }
        const mappedData = { ...data };
        for (const [sourceField, targetField] of Object.entries(config.fieldMappings)) {
            if (data[sourceField] !== undefined) {
                mappedData[targetField] = data[sourceField];
            }
        }
        return { success: true, data: mappedData };
    }
    static calculateDataQualityScore(result, config) {
        let score = 100;
        // Reduce score for validation errors
        score -= result.validationErrors.length * 10;
        // Reduce score if normalization failed
        if (!result.success) {
            score -= 20;
        }
        // Reduce score based on missing required rules
        const requiredRules = config.availableRules.filter(rule => rule.required);
        const appliedRequiredRules = result.appliedRules.filter(rule => rule.required);
        const missingRequiredRules = requiredRules.length - appliedRequiredRules.length;
        score -= missingRequiredRules * 15;
        return Math.max(0, Math.min(100, score));
    }
    static determineManualReviewRequirement(originalEvent, dataQualityScore, validationErrors, config) {
        // High-risk events require manual review
        if (config.requireManualReviewForHighRisk && originalEvent.isHighRisk()) {
            return true;
        }
        // Critical events require manual review
        if (config.requireManualReviewForCritical && originalEvent.isCritical()) {
            return true;
        }
        // Low data quality requires manual review
        if (dataQualityScore < config.minDataQualityThreshold) {
            return true;
        }
        // Too many validation errors require manual review
        if (validationErrors.length > config.maxValidationErrors) {
            return true;
        }
        return false;
    }
    static extractEventInfo(normalizedData) {
        return {
            type: NormalizedEventFactory.mapEventType(normalizedData.event_type || normalizedData.type),
            severity: NormalizedEventFactory.mapEventSeverity(normalizedData.severity || normalizedData.level),
            title: normalizedData.title || normalizedData.message || 'Normalized Security Event',
            description: normalizedData.description || normalizedData.details,
            tags: normalizedData.tags || normalizedData.labels,
            riskScore: normalizedData.risk_score || normalizedData.riskScore,
            confidenceLevel: normalizedData.confidence || normalizedData.confidenceLevel,
            attributes: normalizedData.attributes || {},
        };
    }
    static mapEventType(type) {
        if (!type)
            return event_type_enum_1.EventType.CUSTOM;
        const typeStr = type.toLowerCase();
        if (typeStr.includes('threat'))
            return event_type_enum_1.EventType.THREAT_DETECTED;
        if (typeStr.includes('malware'))
            return event_type_enum_1.EventType.MALWARE_DETECTED;
        if (typeStr.includes('vulnerability'))
            return event_type_enum_1.EventType.VULNERABILITY_DETECTED;
        if (typeStr.includes('login'))
            return event_type_enum_1.EventType.LOGIN_FAILURE;
        if (typeStr.includes('connection'))
            return event_type_enum_1.EventType.CONNECTION_ESTABLISHED;
        return event_type_enum_1.EventType.CUSTOM;
    }
    static mapEventSeverity(severity) {
        if (!severity)
            return event_severity_enum_1.EventSeverity.MEDIUM;
        const sev = severity.toLowerCase();
        if (sev.includes('critical'))
            return event_severity_enum_1.EventSeverity.CRITICAL;
        if (sev.includes('high'))
            return event_severity_enum_1.EventSeverity.HIGH;
        if (sev.includes('medium'))
            return event_severity_enum_1.EventSeverity.MEDIUM;
        if (sev.includes('low'))
            return event_severity_enum_1.EventSeverity.LOW;
        return event_severity_enum_1.EventSeverity.MEDIUM;
    }
    static normalizeEventData(rawData) {
        // Basic normalization - in practice this would be more sophisticated
        return {
            ...rawData,
            normalized_at: new Date().toISOString(),
            schema_version: NormalizedEventFactory.DEFAULT_SCHEMA_VERSION,
        };
    }
    static createMinimalMetadata() {
        // This would typically create proper EventMetadata - simplified for now
        return {
            timestamp: new Date(),
            source: 'normalized-event-factory',
            processingId: shared_kernel_1.UniqueEntityId.create().toString(),
        };
    }
    static createMockEvent() {
        // This would create a proper mock Event - simplified for now
        // In practice, you'd use the EventFactory to create a proper mock
        return {};
    }
}
exports.NormalizedEventFactory = NormalizedEventFactory;
NormalizedEventFactory.DEFAULT_SCHEMA_VERSION = '1.0.0';
NormalizedEventFactory.DEFAULT_MIN_DATA_QUALITY = 60;
NormalizedEventFactory.DEFAULT_MAX_VALIDATION_ERRORS = 10;
NormalizedEventFactory.DEFAULT_NORMALIZATION_TIMEOUT = 30000; // 30 seconds
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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