{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\unique-entity-id.spec.ts", "mappings": ";;AAAA,qGAAmF;AACnF,+BAA8D;AAE9D,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,IAAI,GAAG,IAAA,SAAM,GAAE,CAAC;YACtB,MAAM,EAAE,GAAG,IAAI,8CAAc,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8CAAc,CAAC,IAAW,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8CAAc,CAAC,SAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8CAAc,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8CAAc,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8CAAc,CAAC,GAAU,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8CAAc,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,MAAM,GAAG,sCAAsC,CAAC,CAAC,iBAAiB;YACxE,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8CAAc,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,SAAS,GAAG,sCAAsC,CAAC,CAAC,iBAAiB;YAC3E,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8CAAc,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,8CAAc,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAA,eAAY,EAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,GAAG,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACtC,MAAM,GAAG,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACtC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,IAAI,GAAG,IAAA,SAAM,GAAE,CAAC;YACtB,MAAM,EAAE,GAAG,8CAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,QAAQ,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,IAAI,GAAG,8CAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,GAAG,GAAG,8CAAc,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY,8CAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjE,uBAAuB;YACvB,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,GAAG,GAAG,8CAAc,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,GAAG,EAAE,CAAC,8CAAc,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,GAAG,GAAG,8CAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,GAAG,GAAG,8CAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,GAAG,GAAG,8CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAEtD,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,IAAI,GAAG,IAAA,SAAM,GAAE,CAAC;YACtB,MAAM,CAAC,8CAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,8CAAc,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,8CAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,8CAAc,CAAC,OAAO,CAAC,IAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,8CAAc,CAAC,OAAO,CAAC,SAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,IAAI,GAAG,IAAA,SAAM,GAAE,CAAC;YACtB,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,8CAAc,CAAC,CAAC;YAC1C,MAAM,CAAC,EAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC9C,MAAM,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,IAAI,GAAG,IAAA,SAAM,GAAE,CAAC;YACtB,MAAM,EAAE,GAAG,IAAI,8CAAc,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,WAAW,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;YACvC,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,WAAW,GAAG,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,EAAE,GAAG,IAAI,8CAAc,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,EAAE,GAAG,IAAI,8CAAc,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,EAAE,GAAG,IAAI,8CAAc,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,IAAI,GAAG,IAAA,SAAM,GAAE,CAAC;YACtB,MAAM,GAAG,GAAG,IAAI,8CAAc,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,GAAG,GAAG,IAAI,8CAAc,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,GAAG,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACtC,MAAM,GAAG,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACtC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,GAAG,GAAG,IAAI,8CAAc,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YACnD,MAAM,GAAG,GAAG,IAAI,8CAAc,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YACnD,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,KAAK,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC;YAClC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,KAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,IAAI,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;YAEzB,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,QAAQ,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,YAAY,GAAG,8CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,qBAAqB,EAAE,GAAG,EAAE;YAC7B,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC;YAE/B,mEAAmE;YACnE,IAAI,CAAC;gBACF,EAAU,CAAC,MAAM,GAAG,UAAU,CAAC;YAClC,CAAC;YAAC,MAAM,CAAC;gBACP,0BAA0B;YAC5B,CAAC;YAED,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,IAAI,GAAG,IAAA,SAAM,GAAE,CAAC;YACtB,MAAM,GAAG,GAAG,IAAI,8CAAc,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,GAAG,GAAG,IAAI,8CAAc,CAAC,IAAI,CAAC,CAAC;YAErC,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,GAAG,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACtC,MAAM,GAAG,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAEtC,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;YAEzB,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3B,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,WAAW,GAAG,IAAI,8CAAc,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YAC3D,MAAM,WAAW,GAAG,IAAI,8CAAc,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YAE3D,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,SAAS,GAAG,sCAAsC,CAAC;YACzD,MAAM,EAAE,GAAG,IAAI,8CAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,8CAAc,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAA,eAAY,EAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACvC,MAAM,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,8CAAc,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAA,eAAY,EAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\unique-entity-id.spec.ts"], "sourcesContent": ["import { UniqueEntityId } from '../../value-objects/unique-entity-id.value-object';\r\nimport { v4 as uuidv4, validate as validateUuid } from 'uuid';\r\n\r\ndescribe('UniqueEntityId', () => {\r\n  describe('construction and validation', () => {\r\n    it('should create a valid UniqueEntityId with valid UUID', () => {\r\n      const uuid = uuidv4();\r\n      const id = new UniqueEntityId(uuid);\r\n      expect(id.value).toBe(uuid);\r\n    });\r\n\r\n    it('should throw error for null value', () => {\r\n      expect(() => new UniqueEntityId(null as any)).toThrow('UniqueEntityId cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for undefined value', () => {\r\n      expect(() => new UniqueEntityId(undefined as any)).toThrow('UniqueEntityId cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for empty string', () => {\r\n      expect(() => new UniqueEntityId('')).toThrow('UniqueEntityId cannot be empty');\r\n    });\r\n\r\n    it('should throw error for whitespace string', () => {\r\n      expect(() => new UniqueEntityId('   ')).toThrow('UniqueEntityId cannot be empty');\r\n    });\r\n\r\n    it('should throw error for non-string value', () => {\r\n      expect(() => new UniqueEntityId(123 as any)).toThrow('UniqueEntityId must be a string');\r\n    });\r\n\r\n    it('should throw error for invalid UUID format', () => {\r\n      expect(() => new UniqueEntityId('invalid-uuid')).toThrow('UniqueEntityId must be a valid UUID');\r\n    });\r\n\r\n    it('should throw error for non-v4 UUID', () => {\r\n      const v1Uuid = '550e8400-e29b-11d4-a716-************'; // UUID v1 format\r\n      expect(() => new UniqueEntityId(v1Uuid)).toThrow('UniqueEntityId must be a valid UUID v4 format');\r\n    });\r\n\r\n    it('should accept valid UUID v4', () => {\r\n      const validUuid = '550e8400-e29b-41d4-a716-************'; // UUID v4 format\r\n      expect(() => new UniqueEntityId(validUuid)).not.toThrow();\r\n    });\r\n  });\r\n\r\n  describe('factory methods', () => {\r\n    it('should generate a new unique ID', () => {\r\n      const id = UniqueEntityId.generate();\r\n      expect(id).toBeInstanceOf(UniqueEntityId);\r\n      expect(validateUuid(id.value)).toBe(true);\r\n    });\r\n\r\n    it('should generate different IDs each time', () => {\r\n      const id1 = UniqueEntityId.generate();\r\n      const id2 = UniqueEntityId.generate();\r\n      expect(id1.equals(id2)).toBe(false);\r\n    });\r\n\r\n    it('should create from string', () => {\r\n      const uuid = uuidv4();\r\n      const id = UniqueEntityId.fromString(uuid);\r\n      expect(id.value).toBe(uuid);\r\n    });\r\n\r\n    it('should create from existing ID', () => {\r\n      const original = UniqueEntityId.generate();\r\n      const copy = UniqueEntityId.fromId(original);\r\n      expect(copy.equals(original)).toBe(true);\r\n      expect(copy).not.toBe(original); // Different instances\r\n    });\r\n\r\n    it('should generate multiple IDs', () => {\r\n      const ids = UniqueEntityId.generateMany(5);\r\n      expect(ids).toHaveLength(5);\r\n      expect(ids.every(id => id instanceof UniqueEntityId)).toBe(true);\r\n      \r\n      // All should be unique\r\n      const values = ids.map(id => id.value);\r\n      const uniqueValues = new Set(values);\r\n      expect(uniqueValues.size).toBe(5);\r\n    });\r\n\r\n    it('should handle zero count for generateMany', () => {\r\n      const ids = UniqueEntityId.generateMany(0);\r\n      expect(ids).toHaveLength(0);\r\n    });\r\n\r\n    it('should throw error for negative count', () => {\r\n      expect(() => UniqueEntityId.generateMany(-1)).toThrow('Count must be non-negative');\r\n    });\r\n\r\n    it('should create deterministic ID from seed', () => {\r\n      const id1 = UniqueEntityId.fromSeed('test-seed');\r\n      const id2 = UniqueEntityId.fromSeed('test-seed');\r\n      const id3 = UniqueEntityId.fromSeed('different-seed');\r\n      \r\n      expect(id1.equals(id2)).toBe(true);\r\n      expect(id1.equals(id3)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('validation utilities', () => {\r\n    it('should validate valid UUID string', () => {\r\n      const uuid = uuidv4();\r\n      expect(UniqueEntityId.isValid(uuid)).toBe(true);\r\n    });\r\n\r\n    it('should reject invalid UUID string', () => {\r\n      expect(UniqueEntityId.isValid('invalid')).toBe(false);\r\n      expect(UniqueEntityId.isValid('')).toBe(false);\r\n      expect(UniqueEntityId.isValid(null as any)).toBe(false);\r\n      expect(UniqueEntityId.isValid(undefined as any)).toBe(false);\r\n    });\r\n\r\n    it('should try parse valid UUID', () => {\r\n      const uuid = uuidv4();\r\n      const id = UniqueEntityId.tryParse(uuid);\r\n      expect(id).toBeInstanceOf(UniqueEntityId);\r\n      expect(id!.value).toBe(uuid);\r\n    });\r\n\r\n    it('should return null for invalid UUID in tryParse', () => {\r\n      const id = UniqueEntityId.tryParse('invalid');\r\n      expect(id).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('UUID information', () => {\r\n    it('should get UUID version', () => {\r\n      const id = UniqueEntityId.generate();\r\n      expect(id.getVersion()).toBe(4);\r\n    });\r\n\r\n    it('should get UUID variant', () => {\r\n      const id = UniqueEntityId.generate();\r\n      expect(id.getVariant()).toBe('RFC4122');\r\n    });\r\n\r\n    it('should return null timestamp for v4 UUID', () => {\r\n      const id = UniqueEntityId.generate();\r\n      expect(id.getTimestamp()).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('string representations', () => {\r\n    it('should convert to string', () => {\r\n      const uuid = uuidv4();\r\n      const id = new UniqueEntityId(uuid);\r\n      expect(id.toString()).toBe(uuid);\r\n    });\r\n\r\n    it('should create short string representation', () => {\r\n      const id = UniqueEntityId.generate();\r\n      const shortString = id.toShortString();\r\n      expect(shortString).toHaveLength(8);\r\n      expect(id.value.startsWith(shortString)).toBe(true);\r\n    });\r\n\r\n    it('should create short string with custom length', () => {\r\n      const id = UniqueEntityId.generate();\r\n      const shortString = id.toShortString(12);\r\n      expect(shortString).toHaveLength(12);\r\n    });\r\n\r\n    it('should create compact string without hyphens', () => {\r\n      const uuid = '550e8400-e29b-41d4-a716-************';\r\n      const id = new UniqueEntityId(uuid);\r\n      expect(id.toCompactString()).toBe('550e8400e29b41d4a716************');\r\n    });\r\n\r\n    it('should convert to uppercase', () => {\r\n      const uuid = '550e8400-e29b-41d4-a716-************';\r\n      const id = new UniqueEntityId(uuid);\r\n      expect(id.toUpperCase()).toBe('550E8400-E29B-41D4-A716-************');\r\n    });\r\n\r\n    it('should convert to lowercase', () => {\r\n      const uuid = '550E8400-E29B-41D4-A716-************';\r\n      const id = new UniqueEntityId(uuid);\r\n      expect(id.toLowerCase()).toBe('550e8400-e29b-41d4-a716-************');\r\n    });\r\n  });\r\n\r\n  describe('equality comparison', () => {\r\n    it('should be equal to itself', () => {\r\n      const id = UniqueEntityId.generate();\r\n      expect(id.equals(id)).toBe(true);\r\n    });\r\n\r\n    it('should be equal to another ID with same value', () => {\r\n      const uuid = uuidv4();\r\n      const id1 = new UniqueEntityId(uuid);\r\n      const id2 = new UniqueEntityId(uuid);\r\n      expect(id1.equals(id2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to ID with different value', () => {\r\n      const id1 = UniqueEntityId.generate();\r\n      const id2 = UniqueEntityId.generate();\r\n      expect(id1.equals(id2)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to null or undefined', () => {\r\n      const id = UniqueEntityId.generate();\r\n      expect(id.equals(null)).toBe(false);\r\n      expect(id.equals(undefined)).toBe(false);\r\n    });\r\n\r\n    it('should handle case-insensitive comparison', () => {\r\n      const uuid = '550e8400-e29b-41d4-a716-************';\r\n      const id1 = new UniqueEntityId(uuid.toLowerCase());\r\n      const id2 = new UniqueEntityId(uuid.toUpperCase());\r\n      expect(id1.equals(id2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to non-UniqueEntityId object', () => {\r\n      const id = UniqueEntityId.generate();\r\n      const other = { value: id.value };\r\n      expect(id.equals(other as any)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('JSON serialization', () => {\r\n    it('should serialize to JSON', () => {\r\n      const id = UniqueEntityId.generate();\r\n      const json = id.toJSON();\r\n      \r\n      expect(json).toHaveProperty('value', id.value);\r\n      expect(json).toHaveProperty('version', 4);\r\n      expect(json).toHaveProperty('variant', 'RFC4122');\r\n      expect(json).toHaveProperty('shortString');\r\n      expect(json).toHaveProperty('compactString');\r\n    });\r\n\r\n    it('should deserialize from JSON', () => {\r\n      const original = UniqueEntityId.generate();\r\n      const json = original.toJSON();\r\n      const deserialized = UniqueEntityId.fromJSON(json);\r\n      \r\n      expect(deserialized.equals(original)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('immutability', () => {\r\n    it('should be immutable', () => {\r\n      const id = UniqueEntityId.generate();\r\n      expect(Object.isFrozen(id)).toBe(true);\r\n    });\r\n\r\n    it('should not allow value modification', () => {\r\n      const id = UniqueEntityId.generate();\r\n      const originalValue = id.value;\r\n      \r\n      // Attempt to modify (should fail silently or throw in strict mode)\r\n      try {\r\n        (id as any)._value = 'modified';\r\n      } catch {\r\n        // Expected in strict mode\r\n      }\r\n      \r\n      expect(id.value).toBe(originalValue);\r\n    });\r\n  });\r\n\r\n  describe('hash code', () => {\r\n    it('should generate consistent hash codes', () => {\r\n      const uuid = uuidv4();\r\n      const id1 = new UniqueEntityId(uuid);\r\n      const id2 = new UniqueEntityId(uuid);\r\n      \r\n      expect(id1.getHashCode()).toBe(id2.getHashCode());\r\n    });\r\n\r\n    it('should generate different hash codes for different values', () => {\r\n      const id1 = UniqueEntityId.generate();\r\n      const id2 = UniqueEntityId.generate();\r\n      \r\n      expect(id1.getHashCode()).not.toBe(id2.getHashCode());\r\n    });\r\n  });\r\n\r\n  describe('cloning', () => {\r\n    it('should create a clone with same value', () => {\r\n      const id = UniqueEntityId.generate();\r\n      const clone = id.clone();\r\n      \r\n      expect(clone).not.toBe(id);\r\n      expect(clone.equals(id)).toBe(true);\r\n      expect(clone.value).toBe(id.value);\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle UUID with different casing in constructor', () => {\r\n      const uuid = '550e8400-e29b-41d4-a716-************';\r\n      const upperCaseId = new UniqueEntityId(uuid.toUpperCase());\r\n      const lowerCaseId = new UniqueEntityId(uuid.toLowerCase());\r\n      \r\n      expect(upperCaseId.equals(lowerCaseId)).toBe(true);\r\n    });\r\n\r\n    it('should maintain original casing in value', () => {\r\n      const upperUuid = '550E8400-E29B-41D4-A716-************';\r\n      const id = new UniqueEntityId(upperUuid);\r\n      expect(id.value).toBe(upperUuid);\r\n    });\r\n\r\n    it('should handle very long seed strings', () => {\r\n      const longSeed = 'a'.repeat(1000);\r\n      const id = UniqueEntityId.fromSeed(longSeed);\r\n      expect(id).toBeInstanceOf(UniqueEntityId);\r\n      expect(validateUuid(id.value)).toBe(true);\r\n    });\r\n\r\n    it('should handle empty seed string', () => {\r\n      const id = UniqueEntityId.fromSeed('');\r\n      expect(id).toBeInstanceOf(UniqueEntityId);\r\n      expect(validateUuid(id.value)).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}