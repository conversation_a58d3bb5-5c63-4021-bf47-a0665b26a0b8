{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\domain\\entities\\audit-log.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAMiB;AAEjB;;;GAGG;AAUI,IAAM,QAAQ,GAAd,MAAM,QAAQ;IAkNnB;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO,KAAK,CAAC;QAEjC,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAChD,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;QAEtE,OAAO,IAAI,IAAI,EAAE,GAAG,cAAc,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACxD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU;YAC5B,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,eAAe;YACtC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,eAAe;YACxC,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,IAAI,CAAC,qBAAqB,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,UAAqB,EAAE,QAAmB;QACjE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC;QAC/B,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC;QAClD,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAAe;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,GAAG,MAAM,CAAC;QAC/C,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAE/D,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,UAAkB,EAAE,MAAe;QAClD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC/C,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,GAAG,UAAU,CAAC;QACvD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,oBAAoB,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACtE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,sBAAsB,GAAG,MAAM,CAAC;QAEtD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,MAAM,IAAI,GAAG;YACX,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YACvC,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC;QAEF,4DAA4D;QAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACxC,OAAO,UAAU,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,eAAe;QACb,IAAI,CAAC,IAAI,CAAC,aAAa;YAAE,OAAO,KAAK,CAAC;QACtC,OAAO,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,kCAAkC;YAClC,IAAI,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC;gBACpC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,YAAY,CAAC;YAChD,CAAC;YAED,IAAI,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;gBACtC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,YAAY,CAAC;YAClD,CAAC;YAED,IAAI,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;gBACrC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC;YACjD,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,SAAS,EAAE,IAAI,CAAC,kBAAkB;YAClC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,oBAAoB,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,IAAI,EAAE;YAC/D,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;YAC3D,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,eAAe,IAAI,KAAK;YAClE,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,kBAAkB;YAC/D,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,QAUrB;QACC,aAAa;QACb,IAAI,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,EAAE,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,gBAAgB;QAChB,IAAI,QAAQ,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAChE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,uBAAuB;QACvB,IAAI,QAAQ,CAAC,aAAa,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,iBAAiB;QACjB,IAAI,QAAQ,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,mBAAmB;QACnB,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;gBACzF,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,aAAa;QACb,IAAI,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACzE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,kBAAkB,KAAK,SAAS,IAAI,IAAI,CAAC,kBAAkB,KAAK,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YACzG,OAAO,KAAK,CAAC;QACf,CAAC;QAED,uBAAuB;QACvB,IAAI,QAAQ,CAAC,qBAAqB,KAAK,SAAS,IAAI,IAAI,CAAC,qBAAqB,KAAK,QAAQ,CAAC,qBAAqB,EAAE,CAAC;YAClH,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oBAAoB;QACpB,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YACxB,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YACrD,MAAM,cAAc,GAAG,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC;YAC/F,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzC,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CACrB,MAAc,EACd,MAAc,EACd,YAAoB,EACpB,UAAkB,EAClB,WAAmB,EACnB,OAAa;QAEb,OAAO;YACL,MAAM;YACN,MAAM;YACN,YAAY;YACZ,UAAU;YACV,WAAW;YACX,OAAO,EAAE,OAAO,IAAI,EAAE;YACtB,QAAQ,EAAE,MAAM;YAChB,kBAAkB,EAAE,KAAK;YACzB,qBAAqB,EAAE,KAAK;YAC5B,IAAI,EAAE,CAAC,aAAa,CAAC;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CACvB,MAAc,EACd,YAAoB,EACpB,UAAkB,EAClB,WAAmB,EACnB,OAAa;QAEb,OAAO;YACL,MAAM,EAAE,IAAI;YACZ,MAAM;YACN,YAAY;YACZ,UAAU;YACV,WAAW;YACX,OAAO,EAAE,OAAO,IAAI,EAAE;YACtB,QAAQ,EAAE,MAAM;YAChB,kBAAkB,EAAE,KAAK;YACzB,qBAAqB,EAAE,KAAK;YAC5B,IAAI,EAAE,CAAC,eAAe,CAAC;SACxB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CACxB,MAAc,EACd,MAAc,EACd,WAAmB,EACnB,QAAmD,EACnD,OAAa;QAEb,OAAO;YACL,MAAM;YACN,MAAM;YACN,YAAY,EAAE,UAAU;YACxB,WAAW;YACX,OAAO,EAAE,OAAO,IAAI,EAAE;YACtB,QAAQ;YACR,kBAAkB,EAAE,IAAI;YACxB,qBAAqB,EAAE,IAAI;YAC3B,IAAI,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;SACjC,CAAC;IACJ,CAAC;CACF,CAAA;AArhBY,4BAAQ;AAEnB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;oCACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCAC1C;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2CAC5B;AA4BnB;IAvBC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,SAAS;YACT,WAAW;YACX,QAAQ;YACR,SAAS;YACT,OAAO;SACR;KACF,CAAC;;wCACa;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;;8CACb;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CAC5B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;6CACL;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;yCAwFxB;AAUF;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,CAAC;QAC9C,OAAO,EAAE,MAAM;KAChB,CAAC;;0CACkD;AAMpD;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;oDAC5B;AAM5B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,yBAAyB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;uDAC7B;AAM/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,kBAAkB;;;+CAChE;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;2CAC5B;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CAC5B;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CAC5B;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;sCACtC;AAGf;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;kDAC7B,IAAI,oBAAJ,IAAI;2CAAC;mBAhNL,QAAQ;IATpB,IAAA,gBAAM,EAAC,YAAY,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,cAAc,CAAC,CAAC;IACvB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,oBAAoB,CAAC,CAAC;IAC7B,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;GACR,QAAQ,CAqhBpB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\domain\\entities\\audit-log.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  Index,\r\n} from 'typeorm';\r\n\r\n/**\r\n * Audit Log entity\r\n * Represents comprehensive audit trail for all system activities\r\n */\r\n@Entity('audit_logs')\r\n@Index(['userId'])\r\n@Index(['action'])\r\n@Index(['resourceType'])\r\n@Index(['resourceId'])\r\n@Index(['timestamp'])\r\n@Index(['severity'])\r\n@Index(['complianceRelevant'])\r\n@Index(['sessionId'])\r\nexport class AuditLog {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * User who performed the action\r\n   */\r\n  @Column({ name: 'user_id', type: 'uuid', nullable: true })\r\n  userId?: string;\r\n\r\n  /**\r\n   * Session identifier\r\n   */\r\n  @Column({ name: 'session_id', nullable: true })\r\n  sessionId?: string;\r\n\r\n  /**\r\n   * Action performed\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'create',\r\n      'read',\r\n      'update',\r\n      'delete',\r\n      'login',\r\n      'logout',\r\n      'access',\r\n      'export',\r\n      'import',\r\n      'approve',\r\n      'reject',\r\n      'escalate',\r\n      'assign',\r\n      'execute',\r\n      'configure',\r\n      'backup',\r\n      'restore',\r\n      'other',\r\n    ],\r\n  })\r\n  action: string;\r\n\r\n  /**\r\n   * Type of resource affected\r\n   */\r\n  @Column({ name: 'resource_type' })\r\n  resourceType: string;\r\n\r\n  /**\r\n   * ID of the specific resource\r\n   */\r\n  @Column({ name: 'resource_id', nullable: true })\r\n  resourceId?: string;\r\n\r\n  /**\r\n   * Human-readable description of the action\r\n   */\r\n  @Column({ type: 'text' })\r\n  description: string;\r\n\r\n  /**\r\n   * Detailed audit information\r\n   */\r\n  @Column({ type: 'jsonb' })\r\n  details: {\r\n    // Request information\r\n    request?: {\r\n      method?: string;\r\n      url?: string;\r\n      userAgent?: string;\r\n      ipAddress?: string;\r\n      headers?: Record<string, string>;\r\n      body?: any;\r\n      queryParams?: Record<string, string>;\r\n    };\r\n    \r\n    // Response information\r\n    response?: {\r\n      statusCode?: number;\r\n      responseTime?: number; // milliseconds\r\n      size?: number; // bytes\r\n      headers?: Record<string, string>;\r\n    };\r\n    \r\n    // Data changes\r\n    changes?: {\r\n      before?: any;\r\n      after?: any;\r\n      fields?: string[];\r\n      changeType?: 'create' | 'update' | 'delete' | 'bulk_update';\r\n    };\r\n    \r\n    // Security context\r\n    security?: {\r\n      authenticationMethod?: string;\r\n      permissions?: string[];\r\n      roles?: string[];\r\n      mfaUsed?: boolean;\r\n      riskScore?: number;\r\n      anomalyDetected?: boolean;\r\n    };\r\n    \r\n    // Business context\r\n    business?: {\r\n      businessProcess?: string;\r\n      businessJustification?: string;\r\n      approvalRequired?: boolean;\r\n      approvedBy?: string;\r\n      workflowId?: string;\r\n      transactionId?: string;\r\n    };\r\n    \r\n    // Technical context\r\n    technical?: {\r\n      systemComponent?: string;\r\n      version?: string;\r\n      environment?: 'development' | 'staging' | 'production';\r\n      serverInstance?: string;\r\n      databaseInstance?: string;\r\n      executionTime?: number;\r\n      memoryUsage?: number;\r\n      cpuUsage?: number;\r\n    };\r\n    \r\n    // Compliance context\r\n    compliance?: {\r\n      frameworks?: string[];\r\n      controls?: string[];\r\n      policyViolation?: boolean;\r\n      dataClassification?: string;\r\n      retentionPeriod?: number; // days\r\n      legalHold?: boolean;\r\n      privacyImpact?: boolean;\r\n    };\r\n    \r\n    // Error information\r\n    error?: {\r\n      errorCode?: string;\r\n      errorMessage?: string;\r\n      stackTrace?: string;\r\n      errorCategory?: 'validation' | 'authorization' | 'system' | 'business' | 'external';\r\n    };\r\n    \r\n    // Additional metadata\r\n    metadata?: {\r\n      correlationId?: string;\r\n      parentAuditId?: string;\r\n      childAuditIds?: string[];\r\n      tags?: string[];\r\n      customFields?: Record<string, any>;\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Severity level of the audit event\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['info', 'warning', 'error', 'critical'],\r\n    default: 'info',\r\n  })\r\n  severity: 'info' | 'warning' | 'error' | 'critical';\r\n\r\n  /**\r\n   * Whether this audit log is relevant for compliance\r\n   */\r\n  @Column({ name: 'compliance_relevant', default: false })\r\n  complianceRelevant: boolean;\r\n\r\n  /**\r\n   * Whether this audit log contains sensitive data\r\n   */\r\n  @Column({ name: 'contains_sensitive_data', default: false })\r\n  containsSensitiveData: boolean;\r\n\r\n  /**\r\n   * Data retention period in days\r\n   */\r\n  @Column({ name: 'retention_days', type: 'integer', default: 2555 }) // 7 years default\r\n  retentionDays: number;\r\n\r\n  /**\r\n   * Whether this log is under legal hold\r\n   */\r\n  @Column({ name: 'legal_hold', default: false })\r\n  legalHold: boolean;\r\n\r\n  /**\r\n   * Hash of the audit log for integrity verification\r\n   */\r\n  @Column({ name: 'integrity_hash', nullable: true })\r\n  integrityHash?: string;\r\n\r\n  /**\r\n   * Previous audit log hash for chain verification\r\n   */\r\n  @Column({ name: 'previous_hash', nullable: true })\r\n  previousHash?: string;\r\n\r\n  /**\r\n   * Audit log tags for categorization\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  @CreateDateColumn({ name: 'timestamp' })\r\n  timestamp: Date;\r\n\r\n  /**\r\n   * Check if audit log is expired\r\n   */\r\n  get isExpired(): boolean {\r\n    if (this.legalHold) return false;\r\n    \r\n    const expirationDate = new Date(this.timestamp);\r\n    expirationDate.setDate(expirationDate.getDate() + this.retentionDays);\r\n    \r\n    return new Date() > expirationDate;\r\n  }\r\n\r\n  /**\r\n   * Get audit log age in days\r\n   */\r\n  get ageInDays(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.timestamp.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Check if audit log is high risk\r\n   */\r\n  get isHighRisk(): boolean {\r\n    return this.severity === 'critical' || \r\n           this.details.security?.anomalyDetected ||\r\n           this.details.compliance?.policyViolation ||\r\n           this.action === 'delete' && this.containsSensitiveData;\r\n  }\r\n\r\n  /**\r\n   * Get formatted timestamp\r\n   */\r\n  get formattedTimestamp(): string {\r\n    return this.timestamp.toISOString();\r\n  }\r\n\r\n  /**\r\n   * Add tag to audit log\r\n   */\r\n  addTag(tag: string): void {\r\n    if (!this.tags.includes(tag)) {\r\n      this.tags.push(tag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove tag from audit log\r\n   */\r\n  removeTag(tag: string): void {\r\n    this.tags = this.tags.filter(t => t !== tag);\r\n  }\r\n\r\n  /**\r\n   * Mark as compliance relevant\r\n   */\r\n  markAsComplianceRelevant(frameworks?: string[], controls?: string[]): void {\r\n    this.complianceRelevant = true;\r\n    \r\n    if (!this.details.compliance) {\r\n      this.details.compliance = {};\r\n    }\r\n    \r\n    if (frameworks) {\r\n      this.details.compliance.frameworks = frameworks;\r\n    }\r\n    \r\n    if (controls) {\r\n      this.details.compliance.controls = controls;\r\n    }\r\n    \r\n    this.addTag('compliance');\r\n  }\r\n\r\n  /**\r\n   * Set legal hold\r\n   */\r\n  setLegalHold(reason?: string): void {\r\n    this.legalHold = true;\r\n    \r\n    if (!this.details.metadata) {\r\n      this.details.metadata = {};\r\n    }\r\n    \r\n    this.details.metadata.legalHoldReason = reason;\r\n    this.details.metadata.legalHoldDate = new Date().toISOString();\r\n    \r\n    this.addTag('legal_hold');\r\n  }\r\n\r\n  /**\r\n   * Release legal hold\r\n   */\r\n  releaseLegalHold(releasedBy: string, reason?: string): void {\r\n    this.legalHold = false;\r\n    \r\n    if (!this.details.metadata) {\r\n      this.details.metadata = {};\r\n    }\r\n    \r\n    this.details.metadata.legalHoldReleased = true;\r\n    this.details.metadata.legalHoldReleasedBy = releasedBy;\r\n    this.details.metadata.legalHoldReleaseDate = new Date().toISOString();\r\n    this.details.metadata.legalHoldReleaseReason = reason;\r\n    \r\n    this.removeTag('legal_hold');\r\n  }\r\n\r\n  /**\r\n   * Calculate integrity hash\r\n   */\r\n  calculateIntegrityHash(): string {\r\n    const data = {\r\n      userId: this.userId,\r\n      action: this.action,\r\n      resourceType: this.resourceType,\r\n      resourceId: this.resourceId,\r\n      description: this.description,\r\n      timestamp: this.timestamp.toISOString(),\r\n      details: this.details,\r\n    };\r\n    \r\n    // In a real implementation, use a proper cryptographic hash\r\n    const dataString = JSON.stringify(data);\r\n    return `sha256-${Buffer.from(dataString).toString('base64').slice(0, 32)}`;\r\n  }\r\n\r\n  /**\r\n   * Verify integrity\r\n   */\r\n  verifyIntegrity(): boolean {\r\n    if (!this.integrityHash) return false;\r\n    return this.integrityHash === this.calculateIntegrityHash();\r\n  }\r\n\r\n  /**\r\n   * Sanitize sensitive data for display\r\n   */\r\n  sanitizeForDisplay(): any {\r\n    const sanitized = { ...this };\r\n    \r\n    if (this.containsSensitiveData) {\r\n      // Remove or mask sensitive fields\r\n      if (sanitized.details.request?.body) {\r\n        sanitized.details.request.body = '[REDACTED]';\r\n      }\r\n      \r\n      if (sanitized.details.changes?.before) {\r\n        sanitized.details.changes.before = '[REDACTED]';\r\n      }\r\n      \r\n      if (sanitized.details.changes?.after) {\r\n        sanitized.details.changes.after = '[REDACTED]';\r\n      }\r\n    }\r\n    \r\n    return sanitized;\r\n  }\r\n\r\n  /**\r\n   * Export audit log for compliance reporting\r\n   */\r\n  exportForCompliance(): any {\r\n    return {\r\n      id: this.id,\r\n      timestamp: this.formattedTimestamp,\r\n      userId: this.userId,\r\n      action: this.action,\r\n      resourceType: this.resourceType,\r\n      resourceId: this.resourceId,\r\n      description: this.description,\r\n      severity: this.severity,\r\n      complianceRelevant: this.complianceRelevant,\r\n      complianceFrameworks: this.details.compliance?.frameworks || [],\r\n      complianceControls: this.details.compliance?.controls || [],\r\n      policyViolation: this.details.compliance?.policyViolation || false,\r\n      dataClassification: this.details.compliance?.dataClassification,\r\n      legalHold: this.legalHold,\r\n      integrityHash: this.integrityHash,\r\n      tags: this.tags,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check if audit log matches search criteria\r\n   */\r\n  matchesSearchCriteria(criteria: {\r\n    userId?: string;\r\n    actions?: string[];\r\n    resourceTypes?: string[];\r\n    severity?: string[];\r\n    dateRange?: { start: Date; end: Date };\r\n    tags?: string[];\r\n    complianceRelevant?: boolean;\r\n    containsSensitiveData?: boolean;\r\n    searchText?: string;\r\n  }): boolean {\r\n    // Check user\r\n    if (criteria.userId && this.userId !== criteria.userId) {\r\n      return false;\r\n    }\r\n\r\n    // Check actions\r\n    if (criteria.actions && !criteria.actions.includes(this.action)) {\r\n      return false;\r\n    }\r\n\r\n    // Check resource types\r\n    if (criteria.resourceTypes && !criteria.resourceTypes.includes(this.resourceType)) {\r\n      return false;\r\n    }\r\n\r\n    // Check severity\r\n    if (criteria.severity && !criteria.severity.includes(this.severity)) {\r\n      return false;\r\n    }\r\n\r\n    // Check date range\r\n    if (criteria.dateRange) {\r\n      if (this.timestamp < criteria.dateRange.start || this.timestamp > criteria.dateRange.end) {\r\n        return false;\r\n      }\r\n    }\r\n\r\n    // Check tags\r\n    if (criteria.tags && !criteria.tags.some(tag => this.tags.includes(tag))) {\r\n      return false;\r\n    }\r\n\r\n    // Check compliance relevance\r\n    if (criteria.complianceRelevant !== undefined && this.complianceRelevant !== criteria.complianceRelevant) {\r\n      return false;\r\n    }\r\n\r\n    // Check sensitive data\r\n    if (criteria.containsSensitiveData !== undefined && this.containsSensitiveData !== criteria.containsSensitiveData) {\r\n      return false;\r\n    }\r\n\r\n    // Check search text\r\n    if (criteria.searchText) {\r\n      const searchText = criteria.searchText.toLowerCase();\r\n      const searchableText = `${this.description} ${this.resourceType} ${this.action}`.toLowerCase();\r\n      if (!searchableText.includes(searchText)) {\r\n        return false;\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Create audit log for user action\r\n   */\r\n  static createUserAction(\r\n    userId: string,\r\n    action: string,\r\n    resourceType: string,\r\n    resourceId: string,\r\n    description: string,\r\n    details?: any,\r\n  ): Partial<AuditLog> {\r\n    return {\r\n      userId,\r\n      action,\r\n      resourceType,\r\n      resourceId,\r\n      description,\r\n      details: details || {},\r\n      severity: 'info',\r\n      complianceRelevant: false,\r\n      containsSensitiveData: false,\r\n      tags: ['user_action'],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create audit log for system action\r\n   */\r\n  static createSystemAction(\r\n    action: string,\r\n    resourceType: string,\r\n    resourceId: string,\r\n    description: string,\r\n    details?: any,\r\n  ): Partial<AuditLog> {\r\n    return {\r\n      userId: null,\r\n      action,\r\n      resourceType,\r\n      resourceId,\r\n      description,\r\n      details: details || {},\r\n      severity: 'info',\r\n      complianceRelevant: false,\r\n      containsSensitiveData: false,\r\n      tags: ['system_action'],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create audit log for security event\r\n   */\r\n  static createSecurityEvent(\r\n    userId: string,\r\n    action: string,\r\n    description: string,\r\n    severity: 'info' | 'warning' | 'error' | 'critical',\r\n    details?: any,\r\n  ): Partial<AuditLog> {\r\n    return {\r\n      userId,\r\n      action,\r\n      resourceType: 'security',\r\n      description,\r\n      details: details || {},\r\n      severity,\r\n      complianceRelevant: true,\r\n      containsSensitiveData: true,\r\n      tags: ['security', 'compliance'],\r\n    };\r\n  }\r\n}\r\n"], "version": 3}