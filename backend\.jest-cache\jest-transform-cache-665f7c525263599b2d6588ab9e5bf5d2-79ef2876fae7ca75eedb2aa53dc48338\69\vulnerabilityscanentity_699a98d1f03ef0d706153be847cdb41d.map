{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\vulnerability-scan.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAUiB;AACjB,uFAA4E;AAC5E,yFAA+E;AAE/E;;;GAGG;AAQI,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IA0R5B;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QACtD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC;QACtC,IAAI,QAAQ,KAAK,IAAI;YAAE,OAAO,IAAI,CAAC;QAEnC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACnD,MAAM,OAAO,GAAG,QAAQ,GAAG,EAAE,CAAC;QAE9B,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACd,OAAO,GAAG,KAAK,KAAK,OAAO,KAAK,OAAO,GAAG,CAAC;QAC7C,CAAC;aAAM,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,GAAG,OAAO,KAAK,OAAO,GAAG,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,OAAO,GAAG,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,oBAAoB,IAAI,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,IAAI,0BAA0B;QAC5B,OAAO,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO,CAAC,CAAC;QAEhC,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QACrF,OAAO,CAAC,aAAa,GAAG,EAAE,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;IACrF,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,OAAY;QACnB,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,KAAa;QAChB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACnB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,aAAa;YACnB,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAe;QACpB,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;YACnB,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACf,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAe;QACnB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QAEvB,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;YACnB,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACf,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,aAAa;gBACnB,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAAa;QAC1B,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,IAAkC,EAAE,IAAY,EAAE,OAAe,EAAE,MAAe;QACzF,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACnB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,IAAI;YACJ,IAAI;YACJ,OAAO;YACP,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,0BAA0B,EAAE,IAAI,CAAC,0BAA0B;YAC3D,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,uBAAuB,EAAE,IAAI,CAAC,uBAAuB;YACrD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;CACF,CAAA;AAzhBY,8CAAiB;AAE5B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;6CACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;+CACX;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDACpB;AAUrB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,SAAS,EAAE,iBAAiB,EAAE,UAAU,EAAE,gBAAgB,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,CAAC;KACxH,CAAC;;mDACgI;AAUlI;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,cAAc;QACpB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;KACtG,CAAC;;sDACiH;AAUnH;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC;QAC1E,OAAO,EAAE,SAAS;KACnB,CAAC;;iDAC8E;AAMhF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yDAC5B;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;qDAsC7C;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAkD9D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6DAUrE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAU/D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACjC,KAAK,oBAAL,KAAK;iDAOX;AAMH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2DAUnE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDASxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;+CACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;oDAAC;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;sDAAC;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;sDAAC;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;sDAC3B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAC1C;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;2DAAc;AAGvC;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;oDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;oDAAC;AAIhB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,yDAAuB,EAAE,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC;;sDACjC;AAQvC;IANC,IAAA,oBAAU,EAAC,GAAG,EAAE,CAAC,oBAAK,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC;IAC1D,IAAA,mBAAS,EAAC;QACT,IAAI,EAAE,aAAa;QACnB,UAAU,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,IAAI,EAAE;QAC3D,iBAAiB,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,oBAAoB,EAAE,IAAI,EAAE;KACpE,CAAC;;wDACqB;4BAxRZ,iBAAiB;IAP7B,IAAA,gBAAM,EAAC,qBAAqB,CAAC;IAC7B,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;GACV,iBAAiB,CAyhB7B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\vulnerability-scan.entity.ts"], "sourcesContent": ["import {\r\n  <PERSON><PERSON>ty,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n  ManyToMany,\r\n  JoinTable,\r\n} from 'typeorm';\r\nimport { VulnerabilityAssessment } from './vulnerability-assessment.entity';\r\nimport { Asset } from '../../../asset-management/domain/entities/asset.entity';\r\n\r\n/**\r\n * Vulnerability Scan entity\r\n * Represents a vulnerability scan operation and its results\r\n */\r\n@Entity('vulnerability_scans')\r\n@Index(['status'])\r\n@Index(['scanType'])\r\n@Index(['startedAt'])\r\n@Index(['completedAt'])\r\n@Index(['severity'])\r\n@Index(['scannerType'])\r\nexport class VulnerabilityScan {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Scan name/identifier\r\n   */\r\n  @Column({ length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Scan description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Type of vulnerability scan\r\n   */\r\n  @Column({\r\n    name: 'scan_type',\r\n    type: 'enum',\r\n    enum: ['network', 'web_application', 'database', 'infrastructure', 'container', 'cloud', 'mobile', 'api', 'compliance'],\r\n  })\r\n  scanType: 'network' | 'web_application' | 'database' | 'infrastructure' | 'container' | 'cloud' | 'mobile' | 'api' | 'compliance';\r\n\r\n  /**\r\n   * Scanner tool/engine used\r\n   */\r\n  @Column({\r\n    name: 'scanner_type',\r\n    type: 'enum',\r\n    enum: ['nessus', 'openvas', 'qualys', 'rapid7', 'burp_suite', 'owasp_zap', 'nmap', 'custom', 'other'],\r\n  })\r\n  scannerType: 'nessus' | 'openvas' | 'qualys' | 'rapid7' | 'burp_suite' | 'owasp_zap' | 'nmap' | 'custom' | 'other';\r\n\r\n  /**\r\n   * Scan status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['pending', 'running', 'completed', 'failed', 'cancelled', 'paused'],\r\n    default: 'pending',\r\n  })\r\n  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused';\r\n\r\n  /**\r\n   * Scanner version\r\n   */\r\n  @Column({ name: 'scanner_version', nullable: true })\r\n  scannerVersion?: string;\r\n\r\n  /**\r\n   * Scan configuration\r\n   */\r\n  @Column({ name: 'scan_config', type: 'jsonb' })\r\n  scanConfig: {\r\n    targets: Array<{\r\n      type: 'ip_range' | 'hostname' | 'url' | 'asset_group';\r\n      value: string;\r\n      ports?: number[];\r\n      credentials?: {\r\n        type: 'ssh' | 'snmp' | 'http' | 'database';\r\n        username?: string;\r\n        credentialId?: string; // Reference to stored credentials\r\n      };\r\n    }>;\r\n    scanProfile: {\r\n      name: string;\r\n      type: 'discovery' | 'full' | 'compliance' | 'custom';\r\n      plugins?: string[];\r\n      policies?: string[];\r\n      intensity: 'light' | 'normal' | 'aggressive';\r\n      timeout?: number;\r\n      maxHosts?: number;\r\n      maxChecks?: number;\r\n    };\r\n    scheduling?: {\r\n      type: 'immediate' | 'scheduled' | 'recurring';\r\n      scheduledTime?: string;\r\n      recurrence?: {\r\n        frequency: 'daily' | 'weekly' | 'monthly';\r\n        interval: number;\r\n        daysOfWeek?: number[];\r\n        dayOfMonth?: number;\r\n      };\r\n    };\r\n    notifications?: {\r\n      onStart: boolean;\r\n      onComplete: boolean;\r\n      onError: boolean;\r\n      recipients: string[];\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Scan results summary\r\n   */\r\n  @Column({ name: 'scan_results', type: 'jsonb', nullable: true })\r\n  scanResults?: {\r\n    summary: {\r\n      totalVulnerabilities: number;\r\n      criticalCount: number;\r\n      highCount: number;\r\n      mediumCount: number;\r\n      lowCount: number;\r\n      infoCount: number;\r\n      hostsScanned: number;\r\n      hostsUp: number;\r\n      hostsDown: number;\r\n      portsScanned: number;\r\n      openPorts: number;\r\n    };\r\n    vulnerabilityBreakdown: Array<{\r\n      vulnerabilityId: string;\r\n      identifier: string;\r\n      title: string;\r\n      severity: string;\r\n      cvssScore?: number;\r\n      affectedHosts: number;\r\n      firstFound: string;\r\n      lastSeen: string;\r\n      status: 'new' | 'existing' | 'fixed';\r\n    }>;\r\n    hostResults: Array<{\r\n      host: string;\r\n      status: 'up' | 'down' | 'unreachable';\r\n      vulnerabilityCount: number;\r\n      criticalCount: number;\r\n      highCount: number;\r\n      mediumCount: number;\r\n      lowCount: number;\r\n      operatingSystem?: string;\r\n      openPorts?: number[];\r\n      services?: Array<{\r\n        port: number;\r\n        protocol: string;\r\n        service: string;\r\n        version?: string;\r\n      }>;\r\n    }>;\r\n    complianceResults?: Array<{\r\n      framework: string;\r\n      policy: string;\r\n      status: 'pass' | 'fail' | 'warning' | 'not_applicable';\r\n      score?: number;\r\n      findings: number;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Scan performance metrics\r\n   */\r\n  @Column({ name: 'performance_metrics', type: 'jsonb', nullable: true })\r\n  performanceMetrics?: {\r\n    duration: number; // seconds\r\n    hostsPerSecond: number;\r\n    checksPerSecond: number;\r\n    dataTransferred: number; // bytes\r\n    memoryUsage: number; // MB\r\n    cpuUsage: number; // percentage\r\n    networkUtilization: number; // percentage\r\n    errorRate: number; // percentage\r\n  };\r\n\r\n  /**\r\n   * Scan progress information\r\n   */\r\n  @Column({ name: 'progress_info', type: 'jsonb', nullable: true })\r\n  progressInfo?: {\r\n    currentPhase: string;\r\n    overallProgress: number; // percentage\r\n    hostsCompleted: number;\r\n    hostsTotal: number;\r\n    checksCompleted: number;\r\n    checksTotal: number;\r\n    estimatedTimeRemaining?: number; // seconds\r\n    currentTarget?: string;\r\n  };\r\n\r\n  /**\r\n   * Scan errors and warnings\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  errors?: Array<{\r\n    type: 'error' | 'warning' | 'info';\r\n    code: string;\r\n    message: string;\r\n    target?: string;\r\n    timestamp: string;\r\n    details?: any;\r\n  }>;\r\n\r\n  /**\r\n   * Quality assurance information\r\n   */\r\n  @Column({ name: 'quality_assurance', type: 'jsonb', nullable: true })\r\n  qualityAssurance?: {\r\n    falsePositiveRate?: number;\r\n    coverageScore?: number; // percentage of attack surface covered\r\n    accuracyScore?: number; // percentage of accurate findings\r\n    completenessScore?: number; // percentage of known vulnerabilities found\r\n    reviewStatus: 'pending' | 'in_review' | 'approved' | 'rejected';\r\n    reviewedBy?: string;\r\n    reviewedAt?: string;\r\n    reviewNotes?: string;\r\n  };\r\n\r\n  /**\r\n   * Comparison with previous scans\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  comparison?: {\r\n    previousScanId?: string;\r\n    newVulnerabilities: number;\r\n    fixedVulnerabilities: number;\r\n    persistentVulnerabilities: number;\r\n    regressionVulnerabilities: number;\r\n    riskTrend: 'improving' | 'stable' | 'degrading';\r\n    scoreChange?: number;\r\n  };\r\n\r\n  /**\r\n   * Scan tags for categorization\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * When scan was started\r\n   */\r\n  @Column({ name: 'started_at', type: 'timestamp with time zone', nullable: true })\r\n  startedAt?: Date;\r\n\r\n  /**\r\n   * When scan was completed\r\n   */\r\n  @Column({ name: 'completed_at', type: 'timestamp with time zone', nullable: true })\r\n  completedAt?: Date;\r\n\r\n  /**\r\n   * When scan was scheduled\r\n   */\r\n  @Column({ name: 'scheduled_at', type: 'timestamp with time zone', nullable: true })\r\n  scheduledAt?: Date;\r\n\r\n  /**\r\n   * User who initiated the scan\r\n   */\r\n  @Column({ name: 'initiated_by', type: 'uuid' })\r\n  initiatedBy: string;\r\n\r\n  /**\r\n   * User who reviewed the scan\r\n   */\r\n  @Column({ name: 'reviewed_by', type: 'uuid', nullable: true })\r\n  reviewedBy?: string;\r\n\r\n  /**\r\n   * Custom attributes\r\n   */\r\n  @Column({ name: 'custom_attributes', type: 'jsonb', nullable: true })\r\n  customAttributes?: Record<string, any>;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @OneToMany(() => VulnerabilityAssessment, assessment => assessment.scan)\r\n  assessments: VulnerabilityAssessment[];\r\n\r\n  @ManyToMany(() => Asset, asset => asset.vulnerabilityScans)\r\n  @JoinTable({\r\n    name: 'scan_assets',\r\n    joinColumn: { name: 'scan_id', referencedColumnName: 'id' },\r\n    inverseJoinColumn: { name: 'asset_id', referencedColumnName: 'id' },\r\n  })\r\n  scannedAssets: Asset[];\r\n\r\n  /**\r\n   * Check if scan is running\r\n   */\r\n  get isRunning(): boolean {\r\n    return this.status === 'running';\r\n  }\r\n\r\n  /**\r\n   * Check if scan is completed\r\n   */\r\n  get isCompleted(): boolean {\r\n    return this.status === 'completed';\r\n  }\r\n\r\n  /**\r\n   * Check if scan failed\r\n   */\r\n  get isFailed(): boolean {\r\n    return this.status === 'failed';\r\n  }\r\n\r\n  /**\r\n   * Get scan duration in seconds\r\n   */\r\n  get durationSeconds(): number | null {\r\n    if (!this.startedAt || !this.completedAt) return null;\r\n    return Math.floor((this.completedAt.getTime() - this.startedAt.getTime()) / 1000);\r\n  }\r\n\r\n  /**\r\n   * Get scan duration in human readable format\r\n   */\r\n  get durationFormatted(): string | null {\r\n    const duration = this.durationSeconds;\r\n    if (duration === null) return null;\r\n\r\n    const hours = Math.floor(duration / 3600);\r\n    const minutes = Math.floor((duration % 3600) / 60);\r\n    const seconds = duration % 60;\r\n\r\n    if (hours > 0) {\r\n      return `${hours}h ${minutes}m ${seconds}s`;\r\n    } else if (minutes > 0) {\r\n      return `${minutes}m ${seconds}s`;\r\n    } else {\r\n      return `${seconds}s`;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get total vulnerability count\r\n   */\r\n  get totalVulnerabilities(): number {\r\n    return this.scanResults?.summary.totalVulnerabilities || 0;\r\n  }\r\n\r\n  /**\r\n   * Get critical vulnerability count\r\n   */\r\n  get criticalVulnerabilities(): number {\r\n    return this.scanResults?.summary.criticalCount || 0;\r\n  }\r\n\r\n  /**\r\n   * Get high vulnerability count\r\n   */\r\n  get highVulnerabilities(): number {\r\n    return this.scanResults?.summary.highCount || 0;\r\n  }\r\n\r\n  /**\r\n   * Check if scan has critical vulnerabilities\r\n   */\r\n  get hasCriticalVulnerabilities(): boolean {\r\n    return this.criticalVulnerabilities > 0;\r\n  }\r\n\r\n  /**\r\n   * Get scan risk score\r\n   */\r\n  get riskScore(): number {\r\n    if (!this.scanResults) return 0;\r\n\r\n    const { criticalCount, highCount, mediumCount, lowCount } = this.scanResults.summary;\r\n    return (criticalCount * 10) + (highCount * 7) + (mediumCount * 4) + (lowCount * 1);\r\n  }\r\n\r\n  /**\r\n   * Start scan\r\n   */\r\n  start(): void {\r\n    this.status = 'running';\r\n    this.startedAt = new Date();\r\n  }\r\n\r\n  /**\r\n   * Complete scan\r\n   */\r\n  complete(results: any): void {\r\n    this.status = 'completed';\r\n    this.completedAt = new Date();\r\n    this.scanResults = results;\r\n  }\r\n\r\n  /**\r\n   * Fail scan\r\n   */\r\n  fail(error: string): void {\r\n    this.status = 'failed';\r\n    this.completedAt = new Date();\r\n\r\n    if (!this.errors) {\r\n      this.errors = [];\r\n    }\r\n\r\n    this.errors.push({\r\n      type: 'error',\r\n      code: 'SCAN_FAILED',\r\n      message: error,\r\n      timestamp: new Date().toISOString(),\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Cancel scan\r\n   */\r\n  cancel(reason?: string): void {\r\n    this.status = 'cancelled';\r\n    this.completedAt = new Date();\r\n\r\n    if (reason) {\r\n      if (!this.errors) {\r\n        this.errors = [];\r\n      }\r\n\r\n      this.errors.push({\r\n        type: 'info',\r\n        code: 'SCAN_CANCELLED',\r\n        message: reason,\r\n        timestamp: new Date().toISOString(),\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Pause scan\r\n   */\r\n  pause(reason?: string): void {\r\n    this.status = 'paused';\r\n\r\n    if (reason) {\r\n      if (!this.errors) {\r\n        this.errors = [];\r\n      }\r\n\r\n      this.errors.push({\r\n        type: 'info',\r\n        code: 'SCAN_PAUSED',\r\n        message: reason,\r\n        timestamp: new Date().toISOString(),\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Resume scan\r\n   */\r\n  resume(): void {\r\n    this.status = 'running';\r\n  }\r\n\r\n  /**\r\n   * Update progress\r\n   */\r\n  updateProgress(progress: any): void {\r\n    this.progressInfo = progress;\r\n  }\r\n\r\n  /**\r\n   * Add error\r\n   */\r\n  addError(type: 'error' | 'warning' | 'info', code: string, message: string, target?: string): void {\r\n    if (!this.errors) {\r\n      this.errors = [];\r\n    }\r\n\r\n    this.errors.push({\r\n      type,\r\n      code,\r\n      message,\r\n      target,\r\n      timestamp: new Date().toISOString(),\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Add tag to scan\r\n   */\r\n  addTag(tag: string): void {\r\n    if (!this.tags.includes(tag)) {\r\n      this.tags.push(tag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove tag from scan\r\n   */\r\n  removeTag(tag: string): void {\r\n    this.tags = this.tags.filter(t => t !== tag);\r\n  }\r\n\r\n  /**\r\n   * Get scan summary\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      name: this.name,\r\n      scanType: this.scanType,\r\n      scannerType: this.scannerType,\r\n      status: this.status,\r\n      isRunning: this.isRunning,\r\n      isCompleted: this.isCompleted,\r\n      isFailed: this.isFailed,\r\n      hasCriticalVulnerabilities: this.hasCriticalVulnerabilities,\r\n      totalVulnerabilities: this.totalVulnerabilities,\r\n      criticalVulnerabilities: this.criticalVulnerabilities,\r\n      highVulnerabilities: this.highVulnerabilities,\r\n      riskScore: this.riskScore,\r\n      durationSeconds: this.durationSeconds,\r\n      durationFormatted: this.durationFormatted,\r\n      startedAt: this.startedAt,\r\n      completedAt: this.completedAt,\r\n      scheduledAt: this.scheduledAt,\r\n      tags: this.tags,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export scan for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      scan: this.getSummary(),\r\n      description: this.description,\r\n      scanConfig: this.scanConfig,\r\n      scanResults: this.scanResults,\r\n      performanceMetrics: this.performanceMetrics,\r\n      qualityAssurance: this.qualityAssurance,\r\n      comparison: this.comparison,\r\n      errors: this.errors,\r\n      customAttributes: this.customAttributes,\r\n      exportedAt: new Date().toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}