dbe5945e8779962436c1259015f0e097
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatActor = void 0;
const typeorm_1 = require("typeorm");
const ioc_entity_1 = require("./ioc.entity");
/**
 * Threat Actor entity
 * Represents known threat actors, APT groups, and malicious entities
 */
let ThreatActor = class ThreatActor {
    /**
     * Check if threat actor is currently active
     */
    get isActive() {
        return this.status === 'active';
    }
    /**
     * Get age of threat actor in days
     */
    get ageInDays() {
        const now = new Date();
        const diffMs = now.getTime() - this.firstSeen.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Get days since last activity
     */
    get daysSinceLastSeen() {
        const now = new Date();
        const diffMs = now.getTime() - this.lastSeen.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Get risk score based on sophistication, threat level, and activity
     */
    get riskScore() {
        const sophisticationWeights = {
            minimal: 1,
            intermediate: 2,
            advanced: 3,
            expert: 4,
            innovator: 5,
            strategic: 6,
        };
        const threatLevelWeights = {
            low: 1,
            medium: 2,
            high: 3,
            critical: 4,
        };
        const sophisticationWeight = sophisticationWeights[this.sophistication];
        const threatLevelWeight = threatLevelWeights[this.threatLevel];
        const confidenceWeight = this.confidence / 100;
        const activityWeight = this.isActive ? 1.2 : 0.8;
        const baseScore = (sophisticationWeight + threatLevelWeight) * confidenceWeight * activityWeight;
        return Math.round(baseScore * 10) / 10;
    }
    /**
     * Check if actor targets specific sector
     */
    targetsSecor(sector) {
        return this.targetedSectors ? this.targetedSectors.includes(sector) : false;
    }
    /**
     * Check if actor targets specific country
     */
    targetsCountry(country) {
        return this.targetedCountries ? this.targetedCountries.includes(country) : false;
    }
    /**
     * Check if actor uses specific MITRE technique
     */
    usesTechnique(technique) {
        return this.mitreTechniques ? this.mitreTechniques.includes(technique) : false;
    }
    /**
     * Check if actor uses specific malware family
     */
    usesMalware(malwareFamily) {
        return this.malwareFamilies ? this.malwareFamilies.includes(malwareFamily) : false;
    }
    /**
     * Update last seen timestamp
     */
    updateLastSeen() {
        this.lastSeen = new Date();
    }
    /**
     * Add new campaign
     */
    addCampaign(campaign) {
        if (!this.campaigns) {
            this.campaigns = [];
        }
        this.campaigns.push(campaign);
    }
    /**
     * Add MITRE technique
     */
    addMitreTechnique(technique) {
        if (!this.mitreTechniques) {
            this.mitreTechniques = [];
        }
        if (!this.mitreTechniques.includes(technique)) {
            this.mitreTechniques.push(technique);
        }
    }
    /**
     * Add targeted sector
     */
    addTargetedSector(sector) {
        if (!this.targetedSectors) {
            this.targetedSectors = [];
        }
        if (!this.targetedSectors.includes(sector)) {
            this.targetedSectors.push(sector);
        }
    }
    /**
     * Add malware family
     */
    addMalwareFamily(malwareFamily) {
        if (!this.malwareFamilies) {
            this.malwareFamilies = [];
        }
        if (!this.malwareFamilies.includes(malwareFamily)) {
            this.malwareFamilies.push(malwareFamily);
        }
    }
};
exports.ThreatActor = ThreatActor;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ThreatActor.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, length: 255 }),
    __metadata("design:type", String)
], ThreatActor.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "aliases", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ThreatActor.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'apt',
            'cybercriminal',
            'hacktivist',
            'nation_state',
            'insider_threat',
            'script_kiddie',
            'terrorist',
            'unknown',
        ],
        default: 'unknown',
    }),
    __metadata("design:type", String)
], ThreatActor.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['minimal', 'intermediate', 'advanced', 'expert', 'innovator', 'strategic'],
        default: 'intermediate',
    }),
    __metadata("design:type", String)
], ThreatActor.prototype, "sophistication", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['active', 'inactive', 'dormant', 'disbanded', 'unknown'],
        default: 'active',
    }),
    __metadata("design:type", String)
], ThreatActor.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "motivations", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['individual', 'club', 'contest', 'team', 'organization', 'government'],
        nullable: true,
    }),
    __metadata("design:type", String)
], ThreatActor.prototype, "resourceLevel", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "goals", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "capabilities", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'targeted_sectors', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "targetedSectors", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'targeted_countries', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "targetedCountries", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'attributed_country', nullable: true }),
    __metadata("design:type", String)
], ThreatActor.prototype, "attributedCountry", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mitre_techniques', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "mitreTechniques", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mitre_tactics', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "mitreTactics", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'malware_families', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "malwareFamilies", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "tools", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'attack_patterns', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ThreatActor.prototype, "attackPatterns", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "campaigns", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'first_seen', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], ThreatActor.prototype, "firstSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_seen', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ThreatActor.prototype, "lastSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', default: 50 }),
    __metadata("design:type", Number)
], ThreatActor.prototype, "confidence", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'threat_level',
        type: 'enum',
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium',
    }),
    __metadata("design:type", String)
], ThreatActor.prototype, "threatLevel", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "references", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "sources", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatActor.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['white', 'green', 'amber', 'red'],
        default: 'white',
    }),
    __metadata("design:type", String)
], ThreatActor.prototype, "tlp", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ThreatActor.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Record !== "undefined" && Record) === "function" ? _c : Object)
], ThreatActor.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ThreatActor.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ThreatActor.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], ThreatActor.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], ThreatActor.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => ioc_entity_1.IOC, ioc => ioc.threatActors),
    __metadata("design:type", Array)
], ThreatActor.prototype, "iocs", void 0);
exports.ThreatActor = ThreatActor = __decorate([
    (0, typeorm_1.Entity)('threat_actors'),
    (0, typeorm_1.Index)(['name'], { unique: true }),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['sophistication']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['firstSeen']),
    (0, typeorm_1.Index)(['lastSeen'])
], ThreatActor);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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