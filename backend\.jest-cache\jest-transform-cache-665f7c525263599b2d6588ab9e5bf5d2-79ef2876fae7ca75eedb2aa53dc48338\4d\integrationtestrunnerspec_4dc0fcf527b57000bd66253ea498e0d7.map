{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\integration-test-runner.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,4GAAuG;AAEvG,wEAAoE;AAIpE,iFAA6E;AAC7E,mFAA+E;AAC/E,iGAA6F;AAC7F,qGAAgG;AAChG,0GAA4F;AAC5F,0GAA4F;AAC5F,sHAAwG;AACxG,8GAAgG;AAChG,uHAAsG;AACtG,yHAAwG;AACxG,mHAAkG;AAIlG,wEAA+D;AAC/D,gFAAuE;AACvE,4EAAmE;AACnE,sFAA4E;AAC5E,kFAAyE;AAIzE,yHAAuG;AACvG,qHAAoG;AACpG,2GAA0F;AAC1F,uGAAsF;AAEtF;;;;;GAKG;AAEH,MAAM,4BAA4B;IAAlC;QACU,WAAM,GAAuB,IAAI,GAAG,EAAE,CAAC;QACvC,YAAO,GAAG;YAChB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,UAAU,EAAE,CAAC;SACd,CAAC;IA0EJ,CAAC;IAxEC,KAAK,CAAC,IAAI,CAAC,KAAY;QACrB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAkB;QAC/B,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAkB;QAC7B,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAW,EAAE,GAAS;QAC1C,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACrD,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAC3C,OAAO,SAAS,IAAI,KAAK,IAAI,SAAS,IAAI,GAAG,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAmB;QACpC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACrD,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAC5B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAuB;QAC1C,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACrD,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAC5B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,YAAoB,EAAE,UAAwB,EAAE,WAA2B;QACxG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAC1B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,CAAC;QACvD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACrD,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAC3C,MAAM,SAAS,GAAG,SAAS,IAAI,UAAU,CAAC;YAC1C,MAAM,SAAS,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,aAAa,GAAG,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;YAC7F,OAAO,SAAS,IAAI,SAAS,IAAI,aAAa,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,CAAgB,EAAE,CAAgB;QACxD,MAAM,aAAa,GAAG,CAAC,mCAAa,CAAC,GAAG,EAAE,mCAAa,CAAC,MAAM,EAAE,mCAAa,CAAC,IAAI,EAAE,mCAAa,CAAC,QAAQ,CAAC,CAAC;QAC5G,OAAO,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED,UAAU;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED,KAAK;QACH,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG;YACb,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,UAAU,EAAE,CAAC;SACd,CAAC;IACJ,CAAC;CACF;AAED,MAAM,2BAA2B;IAAjC;QACU,YAAO,GAAG;YAChB,iBAAiB,EAAE,CAAC;YACpB,kBAAkB,EAAE,CAAC;YACrB,mBAAmB,EAAE,CAAC;SACvB,CAAC;IA0DJ,CAAC;IAxDC,KAAK,CAAC,YAAY,CAAC,KAAY,EAAE,OAAY;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAEjC,qCAAqC;QACrC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QAEtD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,mBAAmB,IAAI,QAAQ,CAAC;QAE7C,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC5B,eAAe,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC;YACrD,QAAQ;YACR,eAAe,EAAE;gBACf,EAAE,EAAE,8CAAc,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;gBACtC,eAAe,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;gBACpC,YAAY,EAAE,IAAI,IAAI,EAAE;aACzB;YACD,aAAa,EAAE;gBACb,EAAE,EAAE,8CAAc,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;gBACtC,eAAe,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;gBACpC,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,iBAAiB,EAAE,CAAC,cAAc,EAAE,cAAc,CAAC;aACpD;YACD,gBAAgB,EAAE,EAAE;YACpB,MAAM,EAAE,EAAE;SACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAe,EAAE,OAAY;QAC/C,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAClC,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC/B,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CACvD,CAAC;QACF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,MAAM,CAAC,MAAM;YAC1B,eAAe,EAAE,OAAO,CAAC,MAAM;YAC/B,OAAO;SACR,CAAC;IACJ,CAAC;IAED,UAAU;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED,KAAK;QACH,IAAI,CAAC,OAAO,GAAG;YACb,iBAAiB,EAAE,CAAC;YACpB,kBAAkB,EAAE,CAAC;YACrB,mBAAmB,EAAE,CAAC;SACvB,CAAC;IACJ,CAAC;CACF;AAED,MAAM,2BAA2B;IAAjC;QACU,YAAO,GAAG;YAChB,iBAAiB,EAAE,CAAC;YACpB,kBAAkB,EAAE,CAAC;YACrB,eAAe,EAAE,CAAC;YAClB,iBAAiB,EAAE,CAAC;SACrB,CAAC;IA6GJ,CAAC;IA3GC,KAAK,CAAC,YAAY,CAAC,KAAY,EAAE,OAAY;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAEjC,mCAAmC;QACnC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QAEtD,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,MAAM,CAAC;QAE/C,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3B,MAAM,YAAY,GAAG,OAAO,GAAG,SAAS,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,YAAY,CAAC;QAE/C,OAAO;YACL,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC5B,OAAO;YACP,UAAU,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACxC,YAAY;YACZ,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,CAAC;YACpD,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;SACvD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAe,EAAE,OAAY;QAC/C,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAClC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAChC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CACvD,CAAC;QACF,OAAO;YACL,WAAW,EAAE,MAAM,CAAC,MAAM;YAC1B,QAAQ;YACR,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;SACpD,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,KAAY;QAClC,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,IAAI,KAAK,CAAC,QAAQ,KAAK,mCAAa,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,EAAE,CAAC;YACvF,OAAO,CAAC,IAAI,CAAC;gBACX,EAAE,EAAE,8CAAc,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;gBACtC,SAAS,EAAE,UAAU,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC/C,QAAQ,EAAE,KAAK,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,qCAAc,CAAC,IAAI;gBACnG,UAAU,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE;gBACnC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC;gBACnD,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC;gBACpD,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,2BAAS,CAAC,iBAAiB,EAAE,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC;gBACX,EAAE,EAAE,8CAAc,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;gBACtC,SAAS,EAAE,0BAA0B;gBACrC,QAAQ,EAAE,qCAAc,CAAC,IAAI;gBAC7B,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,CAAC,6BAA6B,EAAE,eAAe,CAAC;gBAC5D,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;gBACnC,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,kBAAkB,CAAC,SAAoB;QAC7C,MAAM,YAAY,GAAG;YACnB,CAAC,2BAAS,CAAC,iBAAiB,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;YACjD,CAAC,2BAAS,CAAC,gBAAgB,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;YAChD,CAAC,2BAAS,CAAC,sBAAsB,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;YACtD,CAAC,2BAAS,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;YAC3C,CAAC,2BAAS,CAAC,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;SAC/C,CAAC;QACF,OAAO,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAEO,uBAAuB,CAAC,OAAc;QAC5C,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAEpC,MAAM,eAAe,GAAG,CAAC,yBAAyB,CAAC,CAAC;QAEpD,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9D,eAAe,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAClD,eAAe,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,2BAA2B,CAAC,CAAC,EAAE,CAAC;YAC3E,eAAe,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAChD,eAAe,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,UAAU;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED,KAAK;QACH,IAAI,CAAC,OAAO,GAAG;YACb,iBAAiB,EAAE,CAAC;YACpB,kBAAkB,EAAE,CAAC;YACrB,eAAe,EAAE,CAAC;YAClB,iBAAiB,EAAE,CAAC;SACrB,CAAC;IACJ,CAAC;CACF;AAED,QAAQ,CAAC,uCAAuC,EAAE,GAAG,EAAE;IACrD,IAAI,MAAqB,CAAC;IAC1B,IAAI,mBAAgD,CAAC;IACrD,IAAI,eAA6C,CAAC;IAClD,IAAI,cAA2C,CAAC;IAChD,IAAI,cAA2C,CAAC;IAEhD,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,eAAe,GAAG,IAAI,4BAA4B,EAAE,CAAC;QACrD,cAAc,GAAG,IAAI,2BAA2B,EAAE,CAAC;QACnD,cAAc,GAAG,IAAI,2BAA2B,EAAE,CAAC;QAEnD,MAAM,GAAG,MAAM,cAAI,CAAC,mBAAmB,CAAC;YACtC,SAAS,EAAE;gBACT,2DAA2B;gBAC3B,EAAE,OAAO,EAAE,kCAAe,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACvD,EAAE,OAAO,EAAE,oCAAgB,EAAE,QAAQ,EAAE,EAAE,EAAE;gBAC3C,EAAE,OAAO,EAAE,kDAAuB,EAAE,QAAQ,EAAE,EAAE,EAAE;gBAClD,EAAE,OAAO,EAAE,qDAAwB,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACnD,EAAE,OAAO,EAAE,0CAAc,EAAE,QAAQ,EAAE,cAAc,EAAE;gBACrD,EAAE,OAAO,EAAE,0CAAc,EAAE,QAAQ,EAAE,cAAc,EAAE;gBACrD,EAAE,OAAO,EAAE,sDAAoB,EAAE,QAAQ,EAAE,EAAE,EAAE;gBAC/C,EAAE,OAAO,EAAE,8CAAgB,EAAE,QAAQ,EAAE,EAAE,EAAE;aAC5C;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAA8B,2DAA2B,CAAC,CAAC;IAC7F,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,eAAe,CAAC,KAAK,EAAE,CAAC;QACxB,cAAc,CAAC,KAAK,EAAE,CAAC;QACvB,cAAc,CAAC,KAAK,EAAE,CAAC;QACvB,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACxD,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;YAC3E,0DAA0D;YAC1D,MAAM,QAAQ,GAAG,iCAAQ,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,6BAAM,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,2CAAa,CAAC,MAAM,EAAE,CAAC;YAC7C,MAAM,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAEhC,kCAAkC;YAClC,MAAM,qBAAqB,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChD,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC;oBAC7B,MAAM,EAAE,UAAU;oBAClB,OAAO,EAAE,KAAK;oBACd,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,WAAW,CAAC;oBAC1C,YAAY,EAAE;wBACZ,QAAQ,EAAE,MAAM;wBAChB,aAAa,EAAE,SAAS;wBACxB,WAAW,EAAE,eAAe;qBAC7B;iBACF,CAAC;gBACF,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC9C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,QAAQ;oBAC9B,UAAU,EAAE,YAAY;oBACxB,IAAI,EAAE,cAAc;iBACrB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,iBAAiB;gBACjC,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE;oBACP,QAAQ,EAAE,eAAe;oBACzB,QAAQ,EAAE,cAAc;oBACxB,UAAU,EAAE,GAAG;oBACf,QAAQ,EAAE,KAAK;oBACf,YAAY,EAAE,eAAe;iBAC9B;gBACD,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,oCAAoC;YACpC,MAAM,YAAY,GAAG,4BAAY,CAAC,MAAM,CAAC;gBACvC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC;oBAC7B,MAAM,EAAE,qBAAqB;oBAC7B,OAAO,EAAE,KAAK;oBACd,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC;oBACzC,YAAY,EAAE;wBACZ,QAAQ,EAAE,UAAU;wBACpB,QAAQ,EAAE,eAAe;wBACzB,aAAa,EAAE,QAAQ;qBACxB;iBACF,CAAC;gBACF,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC,EAAE,kBAAkB;gBAC/F,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,QAAQ;oBAC9B,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,8BAA8B;iBACrC,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,gBAAgB;gBAChC,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE;oBACP,MAAM,EAAE,eAAe;oBACvB,WAAW,EAAE,gBAAgB;oBAC7B,QAAQ,EAAE,qBAAqB;oBAC/B,IAAI,EAAE,0CAA0C;iBACjD;gBACD,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,oBAAoB;YACpB,MAAM,eAAe,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAC1C,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC;oBAC7B,MAAM,EAAE,YAAY;oBACpB,OAAO,EAAE,KAAK;oBACd,IAAI,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,YAAY,CAAC;oBAC/C,YAAY,EAAE;wBACZ,QAAQ,EAAE,UAAU;wBACpB,kBAAkB,EAAE,KAAK;wBACzB,mBAAmB,EAAE,MAAM;qBAC5B;iBACF,CAAC;gBACF,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC,EAAE,mBAAmB;gBAChG,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,GAAG;oBACzB,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,6BAA6B;iBACpC,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,WAAW;gBAC3B,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE;oBACP,QAAQ,EAAE,kBAAkB;oBAC5B,WAAW,EAAE,IAAI;oBACjB,kBAAkB,EAAE,kBAAkB;oBACtC,aAAa,EAAE,eAAe;iBAC/B;gBACD,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,CAAC,qBAAqB,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;YAE9E,gDAAgD;YAChD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,cAAc,EAAE;gBACnF,QAAQ,EAAE,QAAQ,CAAC,KAAK;gBACxB,MAAM,EAAE,MAAM,CAAC,KAAK;gBACpB,aAAa,EAAE,aAAa,CAAC,KAAK;gBAClC,QAAQ,EAAE,UAAiB;gBAC3B,IAAI,EAAE,WAAkB;gBACxB,YAAY,EAAE,IAAI;gBAClB,mBAAmB,EAAE,IAAI;gBACzB,2BAA2B,EAAE,IAAI;gBACjC,cAAc,EAAE;oBACd,qBAAqB,EAAE,IAAI;oBAC3B,qBAAqB,EAAE,IAAI;oBAC3B,2BAA2B,EAAE,IAAI;oBACjC,uBAAuB,EAAE,IAAI;oBAC7B,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;oBACtB,SAAS,EAAE,CAAC;oBACZ,uBAAuB,EAAE,CAAC;oBAC1B,aAAa,EAAE,CAAC;oBAChB,SAAS,EAAE,MAAM;iBAClB;gBACD,WAAW,EAAE;oBACX;wBACE,EAAE,EAAE,wBAAwB;wBAC5B,IAAI,EAAE,qCAAqC;wBAC3C,WAAW,EAAE,oDAAoD;wBACjE,SAAS,EAAE;4BACT,UAAU,EAAE,CAAC,2BAAS,CAAC,WAAW,EAAE,2BAAS,CAAC,gBAAgB,CAAC;4BAC/D,cAAc,EAAE,CAAC,mCAAa,CAAC,QAAQ,CAAC;4BACxC,kBAAkB,EAAE,GAAG;yBACxB;wBACD,MAAM,EAAE,cAAqB;wBAC7B,QAAQ,EAAE,CAAC;wBACX,OAAO,EAAE,IAAI;qBACd;iBACF;aACF,CAAC,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE3B,oCAAoC;YACpC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,gCAAgC;YAChC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACxD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,oCAAoC;YAE9F,8BAA8B;YAC9B,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC9D,mBAAmB,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACrD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBACtD,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBACnD,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;YAEH,8BAA8B;YAC9B,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE/D,MAAM,eAAe,GAAG,mBAAmB,CAAC,cAAc;iBACvD,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;iBACrC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC,CAAC;YACjE,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAElD,gCAAgC;YAChC,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5D,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE/D,yBAAyB;YACzB,MAAM,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAE1E,4BAA4B;YAC5B,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEnD,iCAAiC;YACjC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAClE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE1D,6BAA6B;YAC7B,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1D,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACtE,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;YAEjF,gCAAgC;YAChC,MAAM,WAAW,GAAG,eAAe,CAAC,UAAU,EAAE,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAElD,6BAA6B;YAC7B,MAAM,gBAAgB,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;YACrD,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnD,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAEhE,MAAM,eAAe,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;YACpD,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE7D,yBAAyB;YACzB,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1D,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,eAAe,CAAC,mBAAmB,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;QACzG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,KAAK,IAAI,EAAE;YAC/E,4DAA4D;YAC5D,MAAM,UAAU,GAAG,GAAG,CAAC;YACvB,MAAM,QAAQ,GAAG,iCAAQ,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,6BAAM,CAAC,MAAM,EAAE,CAAC;YAE/B,MAAM,UAAU,GAAG;gBACjB,2BAAS,CAAC,sBAAsB;gBAChC,2BAAS,CAAC,iBAAiB;gBAC3B,2BAAS,CAAC,gBAAgB;gBAC1B,2BAAS,CAAC,cAAc;gBACxB,2BAAS,CAAC,WAAW;aACtB,CAAC;YAEF,MAAM,cAAc,GAAG;gBACrB,mCAAa,CAAC,GAAG;gBACjB,mCAAa,CAAC,MAAM;gBACpB,mCAAa,CAAC,IAAI;gBAClB,mCAAa,CAAC,QAAQ;aACvB,CAAC;YAEF,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACzD,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;gBACpD,MAAM,QAAQ,GAAG,cAAc,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;gBAE3D,OAAO,4BAAY,CAAC,MAAM,CAAC;oBACzB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC;wBAC7B,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,EAAE;wBAC1B,OAAO,EAAE,KAAK;wBACd,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;wBAC/B,YAAY,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE;qBAC9C,CAAC;oBACF,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;oBACjE,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;wBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;wBACjC,UAAU,EAAE,OAAO,CAAC,GAAG,EAAE,EAAE;wBAC3B,IAAI,EAAE,eAAe,CAAC,GAAG,EAAE,EAAE;qBAC9B,CAAC;oBACF,IAAI,EAAE,SAAS;oBACf,QAAQ;oBACR,MAAM,EAAE,+BAAW,CAAC,QAAQ;oBAC5B,OAAO,EAAE;wBACP,UAAU,EAAE,CAAC;wBACb,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC;wBAC3B,QAAQ,EAAE,WAAW,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE;qBACtD;oBACD,QAAQ;oBACR,MAAM;iBACP,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,yDAAyD;YACzD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,MAAM,EAAE;gBAC3E,QAAQ,EAAE,QAAQ,CAAC,KAAK;gBACxB,MAAM,EAAE,MAAM,CAAC,KAAK;gBACpB,QAAQ,EAAE,QAAe;gBACzB,IAAI,EAAE,WAAkB;gBACxB,YAAY,EAAE,KAAK,EAAE,6CAA6C;gBAClE,mBAAmB,EAAE,IAAI;gBACzB,2BAA2B,EAAE,KAAK,EAAE,sCAAsC;gBAC1E,cAAc,EAAE;oBACd,qBAAqB,EAAE,IAAI;oBAC3B,qBAAqB,EAAE,IAAI;oBAC3B,2BAA2B,EAAE,KAAK;oBAClC,uBAAuB,EAAE,KAAK;oBAC9B,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;oBACtB,SAAS,EAAE,EAAE;oBACb,uBAAuB,EAAE,CAAC;oBAC1B,aAAa,EAAE,CAAC;oBAChB,SAAS,EAAE,MAAM,EAAE,YAAY;iBAChC;aACF,CAAC,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAG,OAAO,GAAG,SAAS,CAAC;YAEtC,kDAAkD;YAClD,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,yBAAyB;YACzB,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7D,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YACvE,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAEpE,yBAAyB;YACzB,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,oCAAoC;YAC3E,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAExD,MAAM,UAAU,GAAG,UAAU,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,oBAAoB;YACxE,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,+BAA+B;YAEtE,2CAA2C;YAC3C,MAAM,oBAAoB,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YAC1F,MAAM,CAAC,oBAAoB,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAEtD,mEAAmE;YACnE,MAAM,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC3C,CAAC,CAAC,QAAQ,KAAK,mCAAa,CAAC,IAAI,IAAI,CAAC,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,CAC3E,CAAC;YACF,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAE9F,sCAAsC;YACtC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEnD,gCAAgC;YAChC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1D,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAClE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAClF,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,+BAA+B;YAElH,oCAAoC;YACpC,MAAM,WAAW,GAAG,eAAe,CAAC,UAAU,EAAE,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YAEjE,iCAAiC;YACjC,MAAM,gBAAgB,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;YACrD,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC5D,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAEhE,MAAM,eAAe,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;YACpD,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3D,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,KAAK,IAAI,EAAE;YAC9E,gEAAgE;YAChE,MAAM,QAAQ,GAAG,iCAAQ,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,6BAAM,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,2CAAa,CAAC,MAAM,EAAE,CAAC;YAE7C,MAAM,MAAM,GAAG;gBACb,yBAAyB;gBACzB,4BAAY,CAAC,MAAM,CAAC;oBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC;wBAC7B,MAAM,EAAE,iBAAiB;wBACzB,OAAO,EAAE,KAAK;wBACd,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;qBAC9B,CAAC;oBACF,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;oBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;wBACzB,IAAI,EAAE,wCAAe,CAAC,QAAQ;wBAC9B,UAAU,EAAE,QAAQ;wBACpB,IAAI,EAAE,eAAe;qBACtB,CAAC;oBACF,IAAI,EAAE,2BAAS,CAAC,iBAAiB;oBACjC,QAAQ,EAAE,mCAAa,CAAC,IAAI;oBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;oBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,EAAE;oBACnD,QAAQ;oBACR,MAAM;iBACP,CAAC;gBAEF,6BAA6B;gBAC7B,4BAAY,CAAC,MAAM,CAAC;oBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC;wBAC7B,MAAM,EAAE,cAAc;wBACtB,OAAO,EAAE,KAAK;wBACd,IAAI,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC;qBACxC,CAAC;oBACF,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;oBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;wBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;wBACjC,UAAU,EAAE,SAAS;wBACrB,IAAI,EAAE,iBAAiB;qBACxB,CAAC;oBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;oBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;oBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;oBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE;oBAC3C,QAAQ;oBACR,MAAM;iBACP,CAAC;aACH,CAAC;YAEF,2CAA2C;YAC3C,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,MAAM,EAAE;gBAC3E,QAAQ,EAAE,QAAQ,CAAC,KAAK;gBACxB,MAAM,EAAE,MAAM,CAAC,KAAK;gBACpB,aAAa,EAAE,aAAa,CAAC,KAAK;gBAClC,QAAQ,EAAE,MAAa;gBACvB,YAAY,EAAE,IAAI;gBAClB,mBAAmB,EAAE,IAAI;gBACzB,2BAA2B,EAAE,IAAI;gBACjC,cAAc,EAAE;oBACd,qBAAqB,EAAE,IAAI;oBAC3B,qBAAqB,EAAE,IAAI;oBAC3B,2BAA2B,EAAE,IAAI;oBACjC,uBAAuB,EAAE,IAAI;oBAC7B,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;oBACtB,SAAS,EAAE,CAAC;oBACZ,uBAAuB,EAAE,CAAC;oBAC1B,aAAa,EAAE,CAAC;oBAChB,SAAS,EAAE,KAAK;iBACjB;aACF,CAAC,CAAC;YAEH,uCAAuC;YACvC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,4CAA4C;YAC5C,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1D,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;YAE9G,sCAAsC;YACtC,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAChE,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAE1E,mBAAmB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC9D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACzD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,qCAAqC;YACrC,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAEvE,mBAAmB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBAC7D,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC3D,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBACtD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;gBACrD,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YAEH,6BAA6B;YAC7B,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAClE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE1D,+CAA+C;YAC/C,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACnD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAEnD,uCAAuC;YACvC,MAAM,WAAW,GAAG,MAAM,eAAe,CAAC,OAAO,EAAE,CAAC;YACpD,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAEjE,uDAAuD;YACvD,mBAAmB,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACrD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gBACrC,uEAAuE;YACzE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wCAAwC,EAAE,GAAG,EAAE;QACtD,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,UAAU;YACV,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACjD,4BAAY,CAAC,MAAM,CAAC;gBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,gBAAgB,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAC/E,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,eAAe,CAAC,EAAE;oBAC9B,IAAI,EAAE,eAAe,CAAC,EAAE;iBACzB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,cAAc;gBAC9B,QAAQ,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAa,CAAC,MAAM;gBACjE,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;gBACzB,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CACH,CAAC;YAEF,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,MAAM,EAAE;gBAC3E,QAAQ,EAAE,QAAe;gBACzB,cAAc,EAAE;oBACd,qBAAqB,EAAE,IAAI;oBAC3B,qBAAqB,EAAE,IAAI;oBAC3B,2BAA2B,EAAE,KAAK;oBAClC,uBAAuB,EAAE,KAAK;oBAC9B,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;oBACtB,SAAS,EAAE,CAAC;oBACZ,uBAAuB,EAAE,CAAC;oBAC1B,aAAa,EAAE,CAAC;oBAChB,SAAS,EAAE,KAAK;iBACjB;aACF,CAAC,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE/B,4CAA4C;YAC5C,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,yBAAyB;YACzB,MAAM,YAAY,GAAG,WAAW,GAAG,aAAa,CAAC;YACjD,MAAM,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,oCAAoC;YAE9E,wBAAwB;YACxB,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAClF,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAClE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1D,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEtD,6DAA6D;YAC7D,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;YACtE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC3F,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAE9F,6BAA6B;YAC7B,MAAM,WAAW,GAAG,eAAe,CAAC,UAAU,EAAE,CAAC;YACjD,MAAM,gBAAgB,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;YACrD,MAAM,eAAe,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;YAEpD,qBAAqB;YACrB,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAElD,oBAAoB;YACpB,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC/D,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAEhE,mBAAmB;YACnB,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE7D,4CAA4C;YAC5C,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,mBAAmB,GAAG,gBAAgB,CAAC,iBAAiB,CAAC;YACpG,MAAM,eAAe,GAAG,eAAe,CAAC,iBAAiB,GAAG,eAAe,CAAC,iBAAiB,CAAC;YAE9F,MAAM,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,kCAAkC;YAC/E,MAAM,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,qCAAqC;YAEhF,2BAA2B;YAC3B,MAAM,sBAAsB,GAAG;gBAC7B,YAAY;gBACZ,eAAe,EAAE,mBAAmB,CAAC,eAAe;gBACpD,eAAe,EAAE,mBAAmB,CAAC,eAAe;gBACpD,eAAe,EAAE,mBAAmB,CAAC,eAAe;gBACpD,UAAU,EAAE,mBAAmB,CAAC,MAAM,CAAC,MAAM;gBAC7C,WAAW,EAAE,mBAAmB,CAAC,OAAO,CAAC,WAAW;gBACpD,UAAU,EAAE,mBAAmB,CAAC,OAAO,CAAC,UAAU;gBAClD,iBAAiB;gBACjB,eAAe;gBACf,oBAAoB,EAAE,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,SAAS;aAC7F,CAAC;YAEF,oCAAoC;YACpC,MAAM,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACnE,MAAM,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACrD,MAAM,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC7D,MAAM,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAEvE,0DAA0D;YAC1D,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,sBAAsB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\integration-test-runner.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { SecurityOrchestratorService } from '../../application/services/security-orchestrator.service';\r\nimport { Event } from '../../domain/entities/event.entity';\r\nimport { EventFactory } from '../../domain/factories/event.factory';\r\nimport { ThreatFactory } from '../../domain/factories/threat.factory';\r\nimport { VulnerabilityFactory } from '../../domain/factories/vulnerability.factory';\r\nimport { ResponseActionFactory } from '../../domain/factories/response-action.factory';\r\nimport { EventRepository } from '../../domain/repositories/event.repository';\r\nimport { ThreatRepository } from '../../domain/repositories/threat.repository';\r\nimport { VulnerabilityRepository } from '../../domain/repositories/vulnerability.repository';\r\nimport { ResponseActionRepository } from '../../domain/repositories/response-action.repository';\r\nimport { EventProcessor } from '../../domain/interfaces/services/event-processor.interface';\r\nimport { ThreatDetector } from '../../domain/interfaces/services/threat-detector.interface';\r\nimport { VulnerabilityScanner } from '../../domain/interfaces/services/vulnerability-scanner.interface';\r\nimport { ResponseExecutor } from '../../domain/interfaces/services/response-executor.interface';\r\nimport { EventMetadata } from '../../domain/value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../domain/value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../domain/value-objects/event-metadata/event-source.value-object';\r\nimport { IpAddress } from '../../domain/value-objects/network/ip-address.value-object';\r\nimport { Port } from '../../domain/value-objects/network/port.value-object';\r\nimport { CvssScore } from '../../domain/value-objects/threat-indicators/cvss-score.value-object';\r\nimport { EventType } from '../../domain/enums/event-type.enum';\r\nimport { EventSeverity } from '../../domain/enums/event-severity.enum';\r\nimport { EventStatus } from '../../domain/enums/event-status.enum';\r\nimport { EventSourceType } from '../../domain/enums/event-source-type.enum';\r\nimport { ThreatSeverity } from '../../domain/enums/threat-severity.enum';\r\nimport { VulnerabilitySeverity } from '../../domain/enums/vulnerability-severity.enum';\r\nimport { ActionType } from '../../domain/enums/action-type.enum';\r\nimport { ActionStatus } from '../../domain/enums/action-status.enum';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { CorrelationId } from '../../../../shared-kernel/value-objects/correlation-id.value-object';\r\nimport { TenantId } from '../../../../shared-kernel/value-objects/tenant-id.value-object';\r\nimport { UserId } from '../../../../shared-kernel/value-objects/user-id.value-object';\r\n\r\n/**\r\n * Comprehensive Integration Test Runner\r\n * \r\n * This test suite runs comprehensive integration scenarios that test\r\n * the complete security domain workflow from end to end.\r\n */\r\n\r\nclass ComprehensiveEventRepository implements EventRepository {\r\n  private events: Map<string, Event> = new Map();\r\n  private metrics = {\r\n    saveCount: 0,\r\n    findCount: 0,\r\n    deleteCount: 0,\r\n    queryCount: 0,\r\n  };\r\n\r\n  async save(event: Event): Promise<void> {\r\n    this.metrics.saveCount++;\r\n    this.events.set(event.id.toString(), event);\r\n  }\r\n\r\n  async findById(id: UniqueEntityId): Promise<Event | null> {\r\n    this.metrics.findCount++;\r\n    return this.events.get(id.toString()) || null;\r\n  }\r\n\r\n  async findAll(): Promise<Event[]> {\r\n    this.metrics.queryCount++;\r\n    return Array.from(this.events.values());\r\n  }\r\n\r\n  async delete(id: UniqueEntityId): Promise<void> {\r\n    this.metrics.deleteCount++;\r\n    this.events.delete(id.toString());\r\n  }\r\n\r\n  async findByTimeRange(start: Date, end: Date): Promise<Event[]> {\r\n    this.metrics.queryCount++;\r\n    return Array.from(this.events.values()).filter(event => {\r\n      const eventTime = event.timestamp.toDate();\r\n      return eventTime >= start && eventTime <= end;\r\n    });\r\n  }\r\n\r\n  async findBySource(source: EventSource): Promise<Event[]> {\r\n    this.metrics.queryCount++;\r\n    return Array.from(this.events.values()).filter(event => \r\n      event.source.equals(source)\r\n    );\r\n  }\r\n\r\n  async findBySeverity(severity: EventSeverity): Promise<Event[]> {\r\n    this.metrics.queryCount++;\r\n    return Array.from(this.events.values()).filter(event => \r\n      event.severity === severity\r\n    );\r\n  }\r\n\r\n  async findEventsForCorrelation(timeWindowMs: number, eventTypes?: EventType[], minSeverity?: EventSeverity): Promise<Event[]> {\r\n    this.metrics.queryCount++;\r\n    const cutoffTime = new Date(Date.now() - timeWindowMs);\r\n    return Array.from(this.events.values()).filter(event => {\r\n      const eventTime = event.timestamp.toDate();\r\n      const timeMatch = eventTime >= cutoffTime;\r\n      const typeMatch = !eventTypes || eventTypes.includes(event.type);\r\n      const severityMatch = !minSeverity || this.compareSeverity(event.severity, minSeverity) >= 0;\r\n      return timeMatch && typeMatch && severityMatch;\r\n    });\r\n  }\r\n\r\n  private compareSeverity(a: EventSeverity, b: EventSeverity): number {\r\n    const severityOrder = [EventSeverity.LOW, EventSeverity.MEDIUM, EventSeverity.HIGH, EventSeverity.CRITICAL];\r\n    return severityOrder.indexOf(a) - severityOrder.indexOf(b);\r\n  }\r\n\r\n  getMetrics() {\r\n    return { ...this.metrics };\r\n  }\r\n\r\n  clear(): void {\r\n    this.events.clear();\r\n    this.metrics = {\r\n      saveCount: 0,\r\n      findCount: 0,\r\n      deleteCount: 0,\r\n      queryCount: 0,\r\n    };\r\n  }\r\n}\r\n\r\nclass ComprehensiveEventProcessor implements EventProcessor {\r\n  private metrics = {\r\n    processEventCount: 0,\r\n    processEventsCount: 0,\r\n    totalProcessingTime: 0,\r\n  };\r\n\r\n  async processEvent(event: Event, context: any): Promise<any> {\r\n    const startTime = Date.now();\r\n    this.metrics.processEventCount++;\r\n    \r\n    // Simulate realistic processing time\r\n    await new Promise(resolve => setTimeout(resolve, 10));\r\n    \r\n    const endTime = Date.now();\r\n    const duration = endTime - startTime;\r\n    this.metrics.totalProcessingTime += duration;\r\n\r\n    return {\r\n      success: true,\r\n      eventId: event.id.toString(),\r\n      processingSteps: ['normalize', 'enrich', 'correlate'],\r\n      duration,\r\n      normalizedEvent: {\r\n        id: UniqueEntityId.create().toString(),\r\n        originalEventId: event.id.toString(),\r\n        normalizedAt: new Date(),\r\n      },\r\n      enrichedEvent: {\r\n        id: UniqueEntityId.create().toString(),\r\n        originalEventId: event.id.toString(),\r\n        enrichedAt: new Date(),\r\n        enrichmentSources: ['threat-intel', 'geo-location'],\r\n      },\r\n      correlatedEvents: [],\r\n      errors: [],\r\n    };\r\n  }\r\n\r\n  async processEvents(events: Event[], context: any): Promise<any> {\r\n    this.metrics.processEventsCount++;\r\n    const results = await Promise.all(\r\n      events.map(event => this.processEvent(event, context))\r\n    );\r\n    return {\r\n      success: true,\r\n      totalEvents: events.length,\r\n      processedEvents: results.length,\r\n      results,\r\n    };\r\n  }\r\n\r\n  getMetrics() {\r\n    return { ...this.metrics };\r\n  }\r\n\r\n  clear(): void {\r\n    this.metrics = {\r\n      processEventCount: 0,\r\n      processEventsCount: 0,\r\n      totalProcessingTime: 0,\r\n    };\r\n  }\r\n}\r\n\r\nclass ComprehensiveThreatDetector implements ThreatDetector {\r\n  private metrics = {\r\n    analyzeEventCount: 0,\r\n    analyzeEventsCount: 0,\r\n    threatsDetected: 0,\r\n    totalAnalysisTime: 0,\r\n  };\r\n\r\n  async analyzeEvent(event: Event, context: any): Promise<any> {\r\n    const startTime = Date.now();\r\n    this.metrics.analyzeEventCount++;\r\n    \r\n    // Simulate realistic analysis time\r\n    await new Promise(resolve => setTimeout(resolve, 15));\r\n    \r\n    const threats = this.generateThreats(event);\r\n    this.metrics.threatsDetected += threats.length;\r\n    \r\n    const endTime = Date.now();\r\n    const analysisTime = endTime - startTime;\r\n    this.metrics.totalAnalysisTime += analysisTime;\r\n\r\n    return {\r\n      eventId: event.id.toString(),\r\n      threats,\r\n      confidence: threats.length > 0 ? 85 : 10,\r\n      analysisTime,\r\n      indicators: threats.flatMap(t => t.indicators || []),\r\n      recommendations: this.generateRecommendations(threats),\r\n    };\r\n  }\r\n\r\n  async analyzeEvents(events: Event[], context: any): Promise<any> {\r\n    this.metrics.analyzeEventsCount++;\r\n    const analyses = await Promise.all(\r\n      events.map(event => this.analyzeEvent(event, context))\r\n    );\r\n    return {\r\n      totalEvents: events.length,\r\n      analyses,\r\n      aggregatedThreats: analyses.flatMap(a => a.threats),\r\n    };\r\n  }\r\n\r\n  private generateThreats(event: Event): any[] {\r\n    const threats = [];\r\n    \r\n    if (event.severity === EventSeverity.HIGH || event.severity === EventSeverity.CRITICAL) {\r\n      threats.push({\r\n        id: UniqueEntityId.create().toString(),\r\n        signature: `threat-${event.type}-${Date.now()}`,\r\n        severity: event.severity === EventSeverity.CRITICAL ? ThreatSeverity.CRITICAL : ThreatSeverity.HIGH,\r\n        confidence: 85 + Math.random() * 10,\r\n        indicators: [`${event.type.toLowerCase()}-pattern`],\r\n        mitreTechniques: this.getMitreTechniques(event.type),\r\n        detectedAt: new Date(),\r\n      });\r\n    }\r\n    \r\n    if (event.type === EventType.NETWORK_INTRUSION) {\r\n      threats.push({\r\n        id: UniqueEntityId.create().toString(),\r\n        signature: 'network-intrusion-threat',\r\n        severity: ThreatSeverity.HIGH,\r\n        confidence: 90,\r\n        indicators: ['suspicious-network-activity', 'port-scanning'],\r\n        mitreTechniques: ['T1190', 'T1046'],\r\n        detectedAt: new Date(),\r\n      });\r\n    }\r\n    \r\n    return threats;\r\n  }\r\n\r\n  private getMitreTechniques(eventType: EventType): string[] {\r\n    const techniqueMap = {\r\n      [EventType.NETWORK_INTRUSION]: ['T1190', 'T1046'],\r\n      [EventType.MALWARE_DETECTED]: ['T1055', 'T1059'],\r\n      [EventType.AUTHENTICATION_FAILURE]: ['T1110', 'T1078'],\r\n      [EventType.DATA_BREACH]: ['T1041', 'T1048'],\r\n      [EventType.SECURITY_ALERT]: ['T1082', 'T1083'],\r\n    };\r\n    return techniqueMap[eventType] || ['T1000'];\r\n  }\r\n\r\n  private generateRecommendations(threats: any[]): string[] {\r\n    if (threats.length === 0) return [];\r\n    \r\n    const recommendations = ['Investigate immediately'];\r\n    \r\n    if (threats.some(t => t.severity === ThreatSeverity.CRITICAL)) {\r\n      recommendations.push('Escalate to security team');\r\n      recommendations.push('Consider system isolation');\r\n    }\r\n    \r\n    if (threats.some(t => t.indicators?.includes('network-intrusion-pattern'))) {\r\n      recommendations.push('Block source IP address');\r\n      recommendations.push('Review firewall rules');\r\n    }\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  getMetrics() {\r\n    return { ...this.metrics };\r\n  }\r\n\r\n  clear(): void {\r\n    this.metrics = {\r\n      analyzeEventCount: 0,\r\n      analyzeEventsCount: 0,\r\n      threatsDetected: 0,\r\n      totalAnalysisTime: 0,\r\n    };\r\n  }\r\n}\r\n\r\ndescribe('Comprehensive Integration Test Runner', () => {\r\n  let module: TestingModule;\r\n  let orchestratorService: SecurityOrchestratorService;\r\n  let eventRepository: ComprehensiveEventRepository;\r\n  let eventProcessor: ComprehensiveEventProcessor;\r\n  let threatDetector: ComprehensiveThreatDetector;\r\n\r\n  beforeEach(async () => {\r\n    eventRepository = new ComprehensiveEventRepository();\r\n    eventProcessor = new ComprehensiveEventProcessor();\r\n    threatDetector = new ComprehensiveThreatDetector();\r\n\r\n    module = await Test.createTestingModule({\r\n      providers: [\r\n        SecurityOrchestratorService,\r\n        { provide: EventRepository, useValue: eventRepository },\r\n        { provide: ThreatRepository, useValue: {} },\r\n        { provide: VulnerabilityRepository, useValue: {} },\r\n        { provide: ResponseActionRepository, useValue: {} },\r\n        { provide: EventProcessor, useValue: eventProcessor },\r\n        { provide: ThreatDetector, useValue: threatDetector },\r\n        { provide: VulnerabilityScanner, useValue: {} },\r\n        { provide: ResponseExecutor, useValue: {} },\r\n      ],\r\n    }).compile();\r\n\r\n    orchestratorService = module.get<SecurityOrchestratorService>(SecurityOrchestratorService);\r\n  });\r\n\r\n  afterEach(async () => {\r\n    eventRepository.clear();\r\n    eventProcessor.clear();\r\n    threatDetector.clear();\r\n    await module.close();\r\n  });\r\n\r\n  describe('End-to-End Security Workflow Integration', () => {\r\n    it('should execute complete security incident response workflow', async () => {\r\n      // Arrange - Create a realistic security incident scenario\r\n      const tenantId = TenantId.create();\r\n      const userId = UserId.create();\r\n      const correlationId = CorrelationId.create();\r\n      const incidentTime = new Date();\r\n\r\n      // Initial network intrusion event\r\n      const networkIntrusionEvent = EventFactory.create({\r\n        metadata: EventMetadata.create({\r\n          source: 'firewall',\r\n          version: '1.0',\r\n          tags: ['security', 'network', 'intrusion'],\r\n          customFields: { \r\n            severity: 'high',\r\n            sourceCountry: 'Unknown',\r\n            targetAsset: 'web-server-01'\r\n          },\r\n        }),\r\n        timestamp: EventTimestamp.create(incidentTime),\r\n        source: EventSource.create({\r\n          type: EventSourceType.FIREWALL,\r\n          identifier: 'fw-dmz-001',\r\n          name: 'DMZ Firewall',\r\n        }),\r\n        type: EventType.NETWORK_INTRUSION,\r\n        severity: EventSeverity.HIGH,\r\n        status: EventStatus.RECEIVED,\r\n        payload: {\r\n          sourceIp: '*************',\r\n          targetIp: '************',\r\n          targetPort: 443,\r\n          protocol: 'TCP',\r\n          attackVector: 'port-scanning',\r\n        },\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      // Follow-up malware detection event\r\n      const malwareEvent = EventFactory.create({\r\n        metadata: EventMetadata.create({\r\n          source: 'endpoint-protection',\r\n          version: '1.0',\r\n          tags: ['security', 'malware', 'endpoint'],\r\n          customFields: {\r\n            severity: 'critical',\r\n            hostName: 'web-server-01',\r\n            malwareFamily: 'trojan'\r\n          },\r\n        }),\r\n        timestamp: EventTimestamp.create(new Date(incidentTime.getTime() + 300000)), // 5 minutes later\r\n        source: EventSource.create({\r\n          type: EventSourceType.ENDPOINT,\r\n          identifier: 'epp-001',\r\n          name: 'Endpoint Protection Platform',\r\n        }),\r\n        type: EventType.MALWARE_DETECTED,\r\n        severity: EventSeverity.CRITICAL,\r\n        status: EventStatus.RECEIVED,\r\n        payload: {\r\n          hostId: 'web-server-01',\r\n          malwareType: 'trojan.generic',\r\n          filePath: '/tmp/suspicious.exe',\r\n          hash: 'a1b2c3d4e5f6789012345678901234567890abcd',\r\n        },\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      // Data breach alert\r\n      const dataBreachEvent = EventFactory.create({\r\n        metadata: EventMetadata.create({\r\n          source: 'dlp-system',\r\n          version: '1.0',\r\n          tags: ['security', 'data-breach', 'compliance'],\r\n          customFields: {\r\n            severity: 'critical',\r\n            dataClassification: 'PII',\r\n            complianceFramework: 'GDPR'\r\n          },\r\n        }),\r\n        timestamp: EventTimestamp.create(new Date(incidentTime.getTime() + 600000)), // 10 minutes later\r\n        source: EventSource.create({\r\n          type: EventSourceType.DLP,\r\n          identifier: 'dlp-001',\r\n          name: 'Data Loss Prevention System',\r\n        }),\r\n        type: EventType.DATA_BREACH,\r\n        severity: EventSeverity.CRITICAL,\r\n        status: EventStatus.RECEIVED,\r\n        payload: {\r\n          dataType: 'customer-records',\r\n          recordCount: 1500,\r\n          exfiltrationMethod: 'network-transfer',\r\n          destinationIp: '*************',\r\n        },\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      const incidentEvents = [networkIntrusionEvent, malwareEvent, dataBreachEvent];\r\n\r\n      // Act - Execute comprehensive security workflow\r\n      const startTime = Date.now();\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow(incidentEvents, {\r\n        tenantId: tenantId.value,\r\n        userId: userId.value,\r\n        correlationId: correlationId.value,\r\n        priority: 'CRITICAL' as any,\r\n        mode: 'AUTOMATIC' as any,\r\n        autoResponse: true,\r\n        enableThreatHunting: true,\r\n        enableVulnerabilityScanning: true,\r\n        workflowConfig: {\r\n          enableEventProcessing: true,\r\n          enableThreatDetection: true,\r\n          enableVulnerabilityScanning: true,\r\n          enableResponseExecution: true,\r\n          enableCorrelation: true,\r\n          enableEnrichment: true,\r\n          batchSize: 5,\r\n          maxConcurrentOperations: 3,\r\n          retryAttempts: 2,\r\n          timeoutMs: 120000,\r\n        },\r\n        customRules: [\r\n          {\r\n            id: 'critical-incident-rule',\r\n            name: 'Critical Security Incident Response',\r\n            description: 'Automated response for critical security incidents',\r\n            condition: {\r\n              eventTypes: [EventType.DATA_BREACH, EventType.MALWARE_DETECTED],\r\n              severityLevels: [EventSeverity.CRITICAL],\r\n              riskScoreThreshold: 8.0,\r\n            },\r\n            action: 'AUTO_RESPOND' as any,\r\n            priority: 1,\r\n            enabled: true,\r\n          },\r\n        ],\r\n      });\r\n      const endTime = Date.now();\r\n\r\n      // Assert - Comprehensive validation\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n\r\n      // Workflow execution validation\r\n      expect(orchestrationResult.success).toBe(true);\r\n      expect(orchestrationResult.eventsProcessed).toBe(3);\r\n      expect(orchestrationResult.duration).toBeGreaterThan(0);\r\n      expect(orchestrationResult.duration).toBeLessThan(10000); // Should complete within 10 seconds\r\n\r\n      // Event processing validation\r\n      expect(orchestrationResult.processingResults).toHaveLength(3);\r\n      orchestrationResult.processingResults.forEach(result => {\r\n        expect(result.success).toBe(true);\r\n        expect(result.processingSteps).toContain('normalize');\r\n        expect(result.processingSteps).toContain('enrich');\r\n        expect(result.processingSteps).toContain('correlate');\r\n      });\r\n\r\n      // Threat detection validation\r\n      expect(orchestrationResult.threatAnalyses).toHaveLength(3);\r\n      expect(orchestrationResult.threatsDetected).toBeGreaterThan(0);\r\n      \r\n      const criticalThreats = orchestrationResult.threatAnalyses\r\n        .flatMap(analysis => analysis.threats)\r\n        .filter(threat => threat.severity === ThreatSeverity.CRITICAL);\r\n      expect(criticalThreats.length).toBeGreaterThan(0);\r\n\r\n      // Response execution validation\r\n      expect(orchestrationResult.responseResults).toHaveLength(3);\r\n      expect(orchestrationResult.actionsExecuted).toBeGreaterThan(0);\r\n\r\n      // Correlation validation\r\n      expect(orchestrationResult.correlationsCreated).toBeGreaterThanOrEqual(0);\r\n\r\n      // Error handling validation\r\n      expect(orchestrationResult.errors).toHaveLength(0);\r\n\r\n      // Performance metrics validation\r\n      expect(orchestrationResult.metrics).toBeDefined();\r\n      expect(orchestrationResult.metrics.totalProcessingTime).toBeGreaterThan(0);\r\n      expect(orchestrationResult.metrics.throughput).toBeGreaterThan(0);\r\n      expect(orchestrationResult.metrics.successRate).toBe(100);\r\n\r\n      // Recommendations validation\r\n      expect(orchestrationResult.recommendations).toBeDefined();\r\n      expect(orchestrationResult.recommendations.length).toBeGreaterThan(0);\r\n      expect(orchestrationResult.recommendations).toContain('Investigate immediately');\r\n\r\n      // Repository metrics validation\r\n      const repoMetrics = eventRepository.getMetrics();\r\n      expect(repoMetrics.saveCount).toBeGreaterThan(0);\r\n      expect(repoMetrics.queryCount).toBeGreaterThan(0);\r\n\r\n      // Service metrics validation\r\n      const processorMetrics = eventProcessor.getMetrics();\r\n      expect(processorMetrics.processEventCount).toBe(3);\r\n      expect(processorMetrics.totalProcessingTime).toBeGreaterThan(0);\r\n\r\n      const detectorMetrics = threatDetector.getMetrics();\r\n      expect(detectorMetrics.analyzeEventCount).toBe(3);\r\n      expect(detectorMetrics.threatsDetected).toBeGreaterThan(0);\r\n      expect(detectorMetrics.totalAnalysisTime).toBeGreaterThan(0);\r\n\r\n      // Audit trail validation\r\n      expect(orchestrationResult.orchestrationId).toBeDefined();\r\n      expect(orchestrationResult.startTime).toBeInstanceOf(Date);\r\n      expect(orchestrationResult.endTime).toBeInstanceOf(Date);\r\n      expect(orchestrationResult.endTime.getTime()).toBeGreaterThan(orchestrationResult.startTime.getTime());\r\n    });\r\n\r\n    it('should handle high-volume security event processing efficiently', async () => {\r\n      // Arrange - Generate high volume of diverse security events\r\n      const eventCount = 100;\r\n      const tenantId = TenantId.create();\r\n      const userId = UserId.create();\r\n      \r\n      const eventTypes = [\r\n        EventType.AUTHENTICATION_FAILURE,\r\n        EventType.NETWORK_INTRUSION,\r\n        EventType.MALWARE_DETECTED,\r\n        EventType.SECURITY_ALERT,\r\n        EventType.DATA_BREACH,\r\n      ];\r\n      \r\n      const severityLevels = [\r\n        EventSeverity.LOW,\r\n        EventSeverity.MEDIUM,\r\n        EventSeverity.HIGH,\r\n        EventSeverity.CRITICAL,\r\n      ];\r\n\r\n      const events = Array.from({ length: eventCount }, (_, i) => {\r\n        const eventType = eventTypes[i % eventTypes.length];\r\n        const severity = severityLevels[i % severityLevels.length];\r\n        \r\n        return EventFactory.create({\r\n          metadata: EventMetadata.create({\r\n            source: `source-${i % 10}`,\r\n            version: '1.0',\r\n            tags: ['security', 'bulk-test'],\r\n            customFields: { batchId: Math.floor(i / 10) },\r\n          }),\r\n          timestamp: EventTimestamp.create(new Date(Date.now() + i * 1000)),\r\n          source: EventSource.create({\r\n            type: EventSourceType.APPLICATION,\r\n            identifier: `app-${i % 20}`,\r\n            name: `Application ${i % 20}`,\r\n          }),\r\n          type: eventType,\r\n          severity,\r\n          status: EventStatus.RECEIVED,\r\n          payload: {\r\n            eventIndex: i,\r\n            batchId: Math.floor(i / 10),\r\n            sourceIp: `192.168.${Math.floor(i / 256)}.${i % 256}`,\r\n          },\r\n          tenantId,\r\n          userId,\r\n        });\r\n      });\r\n\r\n      // Act - Process high volume with optimized configuration\r\n      const startTime = Date.now();\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow(events, {\r\n        tenantId: tenantId.value,\r\n        userId: userId.value,\r\n        priority: 'MEDIUM' as any,\r\n        mode: 'AUTOMATIC' as any,\r\n        autoResponse: false, // Disable to focus on processing performance\r\n        enableThreatHunting: true,\r\n        enableVulnerabilityScanning: false, // Disable to focus on core processing\r\n        workflowConfig: {\r\n          enableEventProcessing: true,\r\n          enableThreatDetection: true,\r\n          enableVulnerabilityScanning: false,\r\n          enableResponseExecution: false,\r\n          enableCorrelation: true,\r\n          enableEnrichment: true,\r\n          batchSize: 10,\r\n          maxConcurrentOperations: 5,\r\n          retryAttempts: 1,\r\n          timeoutMs: 300000, // 5 minutes\r\n        },\r\n      });\r\n      const endTime = Date.now();\r\n      const totalTime = endTime - startTime;\r\n\r\n      // Assert - Performance and correctness validation\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n\r\n      // Correctness validation\r\n      expect(orchestrationResult.success).toBe(true);\r\n      expect(orchestrationResult.eventsProcessed).toBe(eventCount);\r\n      expect(orchestrationResult.processingResults).toHaveLength(eventCount);\r\n      expect(orchestrationResult.threatAnalyses).toHaveLength(eventCount);\r\n\r\n      // Performance validation\r\n      expect(totalTime).toBeLessThan(30000); // Should complete within 30 seconds\r\n      expect(orchestrationResult.duration).toBeGreaterThan(0);\r\n      \r\n      const throughput = eventCount / (totalTime / 1000); // events per second\r\n      expect(throughput).toBeGreaterThan(3); // At least 3 events per second\r\n      \r\n      // Verify all events processed successfully\r\n      const successfulProcessing = orchestrationResult.processingResults.filter(r => r.success);\r\n      expect(successfulProcessing).toHaveLength(eventCount);\r\n\r\n      // Verify threat detection worked for high/critical severity events\r\n      const highSeverityEvents = events.filter(e => \r\n        e.severity === EventSeverity.HIGH || e.severity === EventSeverity.CRITICAL\r\n      );\r\n      expect(orchestrationResult.threatsDetected).toBeGreaterThanOrEqual(highSeverityEvents.length);\r\n\r\n      // Verify no errors in bulk processing\r\n      expect(orchestrationResult.errors).toHaveLength(0);\r\n\r\n      // Verify metrics are reasonable\r\n      expect(orchestrationResult.metrics.successRate).toBe(100);\r\n      expect(orchestrationResult.metrics.throughput).toBeGreaterThan(0);\r\n      expect(orchestrationResult.metrics.averageEventProcessingTime).toBeGreaterThan(0);\r\n      expect(orchestrationResult.metrics.averageEventProcessingTime).toBeLessThan(1000); // Less than 1 second per event\r\n\r\n      // Repository performance validation\r\n      const repoMetrics = eventRepository.getMetrics();\r\n      expect(repoMetrics.saveCount).toBeGreaterThanOrEqual(eventCount);\r\n      \r\n      // Service performance validation\r\n      const processorMetrics = eventProcessor.getMetrics();\r\n      expect(processorMetrics.processEventCount).toBe(eventCount);\r\n      expect(processorMetrics.totalProcessingTime).toBeGreaterThan(0);\r\n      \r\n      const detectorMetrics = threatDetector.getMetrics();\r\n      expect(detectorMetrics.analyzeEventCount).toBe(eventCount);\r\n      expect(detectorMetrics.totalAnalysisTime).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should maintain data consistency across all integration points', async () => {\r\n      // Arrange - Create events that will test all integration points\r\n      const tenantId = TenantId.create();\r\n      const userId = UserId.create();\r\n      const correlationId = CorrelationId.create();\r\n\r\n      const events = [\r\n        // Network security event\r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({\r\n            source: 'network-monitor',\r\n            version: '1.0',\r\n            tags: ['network', 'security'],\r\n          }),\r\n          timestamp: EventTimestamp.create(new Date()),\r\n          source: EventSource.create({\r\n            type: EventSourceType.FIREWALL,\r\n            identifier: 'fw-001',\r\n            name: 'Main Firewall',\r\n          }),\r\n          type: EventType.NETWORK_INTRUSION,\r\n          severity: EventSeverity.HIGH,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { sourceIp: '**********', targetPort: 22 },\r\n          tenantId,\r\n          userId,\r\n        }),\r\n        \r\n        // Application security event\r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({\r\n            source: 'app-security',\r\n            version: '1.0',\r\n            tags: ['application', 'authentication'],\r\n          }),\r\n          timestamp: EventTimestamp.create(new Date()),\r\n          source: EventSource.create({\r\n            type: EventSourceType.APPLICATION,\r\n            identifier: 'app-001',\r\n            name: 'Web Application',\r\n          }),\r\n          type: EventType.AUTHENTICATION_FAILURE,\r\n          severity: EventSeverity.MEDIUM,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { username: 'admin', attempts: 5 },\r\n          tenantId,\r\n          userId,\r\n        }),\r\n      ];\r\n\r\n      // Act - Process with full workflow enabled\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow(events, {\r\n        tenantId: tenantId.value,\r\n        userId: userId.value,\r\n        correlationId: correlationId.value,\r\n        priority: 'HIGH' as any,\r\n        autoResponse: true,\r\n        enableThreatHunting: true,\r\n        enableVulnerabilityScanning: true,\r\n        workflowConfig: {\r\n          enableEventProcessing: true,\r\n          enableThreatDetection: true,\r\n          enableVulnerabilityScanning: true,\r\n          enableResponseExecution: true,\r\n          enableCorrelation: true,\r\n          enableEnrichment: true,\r\n          batchSize: 2,\r\n          maxConcurrentOperations: 2,\r\n          retryAttempts: 2,\r\n          timeoutMs: 60000,\r\n        },\r\n      });\r\n\r\n      // Assert - Data consistency validation\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n\r\n      // Verify orchestration metadata consistency\r\n      expect(orchestrationResult.orchestrationId).toBeDefined();\r\n      expect(orchestrationResult.startTime).toBeInstanceOf(Date);\r\n      expect(orchestrationResult.endTime).toBeInstanceOf(Date);\r\n      expect(orchestrationResult.endTime.getTime()).toBeGreaterThanOrEqual(orchestrationResult.startTime.getTime());\r\n\r\n      // Verify event processing consistency\r\n      expect(orchestrationResult.eventsProcessed).toBe(events.length);\r\n      expect(orchestrationResult.processingResults).toHaveLength(events.length);\r\n      \r\n      orchestrationResult.processingResults.forEach((result, index) => {\r\n        expect(result.eventId).toBe(events[index].id.toString());\r\n        expect(result.success).toBe(true);\r\n        expect(result.duration).toBeGreaterThan(0);\r\n      });\r\n\r\n      // Verify threat analysis consistency\r\n      expect(orchestrationResult.threatAnalyses).toHaveLength(events.length);\r\n      \r\n      orchestrationResult.threatAnalyses.forEach((analysis, index) => {\r\n        expect(analysis.eventId).toBe(events[index].id.toString());\r\n        expect(analysis.confidence).toBeGreaterThanOrEqual(0);\r\n        expect(analysis.confidence).toBeLessThanOrEqual(100);\r\n        expect(analysis.analysisTime).toBeGreaterThan(0);\r\n      });\r\n\r\n      // Verify metrics consistency\r\n      expect(orchestrationResult.metrics).toBeDefined();\r\n      expect(orchestrationResult.metrics.totalProcessingTime).toBeGreaterThan(0);\r\n      expect(orchestrationResult.metrics.throughput).toBeGreaterThan(0);\r\n      expect(orchestrationResult.metrics.successRate).toBe(100);\r\n\r\n      // Verify no data corruption or inconsistencies\r\n      expect(orchestrationResult.errors).toHaveLength(0);\r\n      expect(orchestrationResult.warnings).toBeDefined();\r\n\r\n      // Cross-validate with repository state\r\n      const savedEvents = await eventRepository.findAll();\r\n      expect(savedEvents.length).toBeGreaterThanOrEqual(events.length);\r\n\r\n      // Verify tenant and user context maintained throughout\r\n      orchestrationResult.processingResults.forEach(result => {\r\n        expect(result.eventId).toBeDefined();\r\n        // Additional context validation would go here in a real implementation\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Integration Test Metrics and Reporting', () => {\r\n    it('should provide comprehensive integration test metrics', async () => {\r\n      // Arrange\r\n      const testStartTime = Date.now();\r\n      const events = Array.from({ length: 10 }, (_, i) => \r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: `metrics-test-${i}`, version: '1.0' }),\r\n          timestamp: EventTimestamp.create(new Date()),\r\n          source: EventSource.create({\r\n            type: EventSourceType.APPLICATION,\r\n            identifier: `metrics-app-${i}`,\r\n            name: `Metrics App ${i}`,\r\n          }),\r\n          type: EventType.SECURITY_ALERT,\r\n          severity: i % 2 === 0 ? EventSeverity.HIGH : EventSeverity.MEDIUM,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { testIndex: i },\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        })\r\n      );\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow(events, {\r\n        priority: 'MEDIUM' as any,\r\n        workflowConfig: {\r\n          enableEventProcessing: true,\r\n          enableThreatDetection: true,\r\n          enableVulnerabilityScanning: false,\r\n          enableResponseExecution: false,\r\n          enableCorrelation: true,\r\n          enableEnrichment: true,\r\n          batchSize: 3,\r\n          maxConcurrentOperations: 2,\r\n          retryAttempts: 1,\r\n          timeoutMs: 60000,\r\n        },\r\n      });\r\n      const testEndTime = Date.now();\r\n\r\n      // Assert - Comprehensive metrics validation\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n\r\n      // Test execution metrics\r\n      const testDuration = testEndTime - testStartTime;\r\n      expect(testDuration).toBeGreaterThan(0);\r\n      expect(testDuration).toBeLessThan(10000); // Should complete within 10 seconds\r\n\r\n      // Orchestration metrics\r\n      expect(orchestrationResult.metrics).toBeDefined();\r\n      expect(orchestrationResult.metrics.totalProcessingTime).toBeGreaterThan(0);\r\n      expect(orchestrationResult.metrics.averageEventProcessingTime).toBeGreaterThan(0);\r\n      expect(orchestrationResult.metrics.throughput).toBeGreaterThan(0);\r\n      expect(orchestrationResult.metrics.successRate).toBe(100);\r\n      expect(orchestrationResult.metrics.errorRate).toBe(0);\r\n\r\n      // Resource utilization metrics (would be real in production)\r\n      expect(orchestrationResult.metrics.resourceUtilization).toBeDefined();\r\n      expect(orchestrationResult.metrics.resourceUtilization.cpuUsage).toBeGreaterThanOrEqual(0);\r\n      expect(orchestrationResult.metrics.resourceUtilization.memoryUsage).toBeGreaterThanOrEqual(0);\r\n\r\n      // Component-specific metrics\r\n      const repoMetrics = eventRepository.getMetrics();\r\n      const processorMetrics = eventProcessor.getMetrics();\r\n      const detectorMetrics = threatDetector.getMetrics();\r\n\r\n      // Repository metrics\r\n      expect(repoMetrics.saveCount).toBeGreaterThan(0);\r\n      expect(repoMetrics.queryCount).toBeGreaterThan(0);\r\n\r\n      // Processor metrics\r\n      expect(processorMetrics.processEventCount).toBe(events.length);\r\n      expect(processorMetrics.totalProcessingTime).toBeGreaterThan(0);\r\n\r\n      // Detector metrics\r\n      expect(detectorMetrics.analyzeEventCount).toBe(events.length);\r\n      expect(detectorMetrics.threatsDetected).toBeGreaterThan(0);\r\n      expect(detectorMetrics.totalAnalysisTime).toBeGreaterThan(0);\r\n\r\n      // Performance ratios and efficiency metrics\r\n      const avgProcessingTime = processorMetrics.totalProcessingTime / processorMetrics.processEventCount;\r\n      const avgAnalysisTime = detectorMetrics.totalAnalysisTime / detectorMetrics.analyzeEventCount;\r\n      \r\n      expect(avgProcessingTime).toBeGreaterThan(0);\r\n      expect(avgProcessingTime).toBeLessThan(100); // Should be under 100ms per event\r\n      expect(avgAnalysisTime).toBeGreaterThan(0);\r\n      expect(avgAnalysisTime).toBeLessThan(100); // Should be under 100ms per analysis\r\n\r\n      // Integration test summary\r\n      const integrationTestSummary = {\r\n        testDuration,\r\n        eventsProcessed: orchestrationResult.eventsProcessed,\r\n        threatsDetected: orchestrationResult.threatsDetected,\r\n        actionsExecuted: orchestrationResult.actionsExecuted,\r\n        errorCount: orchestrationResult.errors.length,\r\n        successRate: orchestrationResult.metrics.successRate,\r\n        throughput: orchestrationResult.metrics.throughput,\r\n        avgProcessingTime,\r\n        avgAnalysisTime,\r\n        repositoryOperations: repoMetrics.saveCount + repoMetrics.queryCount + repoMetrics.findCount,\r\n      };\r\n\r\n      // Validate integration test summary\r\n      expect(integrationTestSummary.eventsProcessed).toBe(events.length);\r\n      expect(integrationTestSummary.errorCount).toBe(0);\r\n      expect(integrationTestSummary.successRate).toBe(100);\r\n      expect(integrationTestSummary.throughput).toBeGreaterThan(0);\r\n      expect(integrationTestSummary.repositoryOperations).toBeGreaterThan(0);\r\n\r\n      // Log summary for test reporting (in real implementation)\r\n      console.log('Integration Test Summary:', integrationTestSummary);\r\n    });\r\n  });\r\n});"], "version": 3}