{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\__tests__\\is-severity-level.validator.spec.ts", "mappings": ";;;;;;;;;;;AAAA,qDAA2C;AAC3C,yDAAiD;AACjD,gFAOwC;AAExC,MAAM,eAAe;CASpB;AAPC;IADC,IAAA,6CAAe,GAAE;;iDACQ;AAG1B;IADC,IAAA,mDAAqB,GAAE;;uDACD;AAGvB;IADC,IAAA,oDAAsB,GAAE;;wDACD;AAG1B,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;IACxC,IAAI,UAAqC,CAAC;IAE1C,UAAU,CAAC,GAAG,EAAE;QACd,UAAU,GAAG,IAAI,uDAAyB,EAAE,CAAC;IAC/C,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;YACxB,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;gBAChD,MAAM,WAAW,GAAG;oBAClB,UAAU;oBACV,MAAM;oBACN,QAAQ;oBACR,KAAK;oBACL,eAAe;oBACf,MAAM;oBACN,UAAU,EAAE,8BAA8B;oBAC1C,MAAM;iBACP,CAAC;gBAEF,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBAC1B,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9E,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,YAAY,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;gBAElE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBACzB,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5E,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,aAAa,GAAG;oBACpB,SAAS;oBACT,SAAS;oBACT,CAAC,CAAC;oBACF,IAAI;oBACJ,GAAG;oBACH,QAAQ;oBACR,IAAI;oBACJ,SAAS;oBACT,EAAE;oBACF,EAAE;iBACH,CAAC;gBAEF,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBAC5B,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC/E,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;gBAC5C,MAAM,OAAO,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;gBAE3D,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClF,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;gBAC3C,MAAM,OAAO,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;gBAE3D,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/E,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;gBACvC,MAAM,OAAO,GAAG,EAAE,YAAY,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC;gBAE9D,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpF,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;YAC/B,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;gBAC7D,MAAM,CAAC,gDAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,2CAAa,CAAC,IAAI,CAAC,CAAC;gBACvE,MAAM,CAAC,gDAAkB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,2CAAa,CAAC,GAAG,CAAC,CAAC;gBACxE,MAAM,CAAC,gDAAkB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,2CAAa,CAAC,MAAM,CAAC,CAAC;gBAC3E,MAAM,CAAC,gDAAkB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,2CAAa,CAAC,IAAI,CAAC,CAAC;gBACzE,MAAM,CAAC,gDAAkB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,2CAAa,CAAC,QAAQ,CAAC,CAAC;YAC/E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,CAAC,gDAAkB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC3E,MAAM,CAAC,gDAAkB,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC9E,MAAM,CAAC,gDAAkB,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC5E,MAAM,CAAC,gDAAkB,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;gBACjF,MAAM,CAAC,gDAAkB,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;YAC3B,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;gBAC1D,MAAM,CAAC,gDAAkB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC5D,MAAM,CAAC,gDAAkB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACvD,MAAM,CAAC,gDAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACzD,MAAM,CAAC,gDAAkB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACtD,MAAM,CAAC,gDAAkB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;gBACvD,MAAM,CAAC,gDAAkB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACtD,MAAM,CAAC,gDAAkB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAClD,MAAM,CAAC,gDAAkB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;gBAClD,MAAM,CAAC,gDAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,MAAM,CAAC,gDAAkB,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC1D,MAAM,CAAC,gDAAkB,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC/D,MAAM,CAAC,gDAAkB,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtD,MAAM,CAAC,gDAAkB,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC7D,MAAM,CAAC,gDAAkB,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;gBACjE,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACzD,MAAM,MAAM,GAAG,gDAAkB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBAE7D,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;gBAC/C,MAAM,MAAM,GAAG,gDAAkB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBAE7D,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,CAAC,gDAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtD,MAAM,CAAC,gDAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnD,MAAM,CAAC,gDAAkB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC1D,MAAM,CAAC,gDAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;YACzB,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,CAAC,gDAAkB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC1D,MAAM,CAAC,gDAAkB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;gBAChD,MAAM,CAAC,gDAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACpD,MAAM,CAAC,gDAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,CAAC,gDAAkB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAC3D,MAAM,CAAC,gDAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;YAC5B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,CAAC,gDAAkB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACpE,MAAM,CAAC,gDAAkB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAChE,MAAM,CAAC,gDAAkB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAClE,MAAM,CAAC,gDAAkB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC/D,MAAM,CAAC,gDAAkB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;gBAChD,MAAM,YAAY,GAAG,gDAAkB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBACnE,MAAM,OAAO,GAAG,gDAAkB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAEzD,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;gBACtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,GAAG,GAAG,IAAA,gCAAY,EAAC,eAAe,EAAE;gBACxC,QAAQ,EAAE,MAAM;gBAChB,cAAc,EAAE,UAAU;gBAC1B,eAAe,EAAE,GAAG;aACrB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,GAAG,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,GAAG,GAAG,IAAA,gCAAY,EAAC,eAAe,EAAE;gBACxC,QAAQ,EAAE,SAAS;gBACnB,cAAc,EAAE,GAAG,EAAE,mBAAmB;gBACxC,eAAe,EAAE,MAAM,EAAE,mBAAmB;aAC7C,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,GAAG,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\__tests__\\is-severity-level.validator.spec.ts"], "sourcesContent": ["import { validate } from 'class-validator';\r\nimport { plainToClass } from 'class-transformer';\r\nimport {\r\n  IsSeverityLevel,\r\n  IsStringSeverityLevel,\r\n  IsNumericSeverityLevel,\r\n  SeverityLevel,\r\n  SeverityLevelUtils,\r\n  IsSeverityLevelConstraint,\r\n} from '../is-severity-level.validator';\r\n\r\nclass TestSeverityDto {\r\n  @IsSeverityLevel()\r\n  severity: string | number;\r\n\r\n  @IsStringSeverityLevel()\r\n  stringSeverity: string;\r\n\r\n  @IsNumericSeverityLevel()\r\n  numericSeverity: number;\r\n}\r\n\r\ndescribe('Severity Level Validator', () => {\r\n  let constraint: IsSeverityLevelConstraint;\r\n\r\n  beforeEach(() => {\r\n    constraint = new IsSeverityLevelConstraint();\r\n  });\r\n\r\n  describe('IsSeverityLevelConstraint', () => {\r\n    describe('validate', () => {\r\n      it('should validate string severity levels', () => {\r\n        const validLevels = [\r\n          'critical',\r\n          'high',\r\n          'medium',\r\n          'low',\r\n          'informational',\r\n          'none',\r\n          'CRITICAL', // Case insensitive by default\r\n          'High',\r\n        ];\r\n\r\n        validLevels.forEach(level => {\r\n          expect(constraint.validate(level, { constraints: [{}] } as any)).toBe(true);\r\n        });\r\n      });\r\n\r\n      it('should validate numeric severity levels', () => {\r\n        const validNumbers = [0, 1.5, 3.9, 4.0, 6.9, 7.0, 8.9, 9.0, 10.0];\r\n\r\n        validNumbers.forEach(num => {\r\n          expect(constraint.validate(num, { constraints: [{}] } as any)).toBe(true);\r\n        });\r\n      });\r\n\r\n      it('should reject invalid severity levels', () => {\r\n        const invalidLevels = [\r\n          'invalid',\r\n          'extreme',\r\n          -1,\r\n          10.1,\r\n          NaN,\r\n          Infinity,\r\n          null,\r\n          undefined,\r\n          {},\r\n          [],\r\n        ];\r\n\r\n        invalidLevels.forEach(level => {\r\n          expect(constraint.validate(level, { constraints: [{}] } as any)).toBe(false);\r\n        });\r\n      });\r\n\r\n      it('should respect allowNumeric option', () => {\r\n        const options = { allowNumeric: false, allowString: true };\r\n        \r\n        expect(constraint.validate('high', { constraints: [options] } as any)).toBe(true);\r\n        expect(constraint.validate(7.5, { constraints: [options] } as any)).toBe(false);\r\n      });\r\n\r\n      it('should respect allowString option', () => {\r\n        const options = { allowNumeric: true, allowString: false };\r\n        \r\n        expect(constraint.validate(7.5, { constraints: [options] } as any)).toBe(true);\r\n        expect(constraint.validate('high', { constraints: [options] } as any)).toBe(false);\r\n      });\r\n\r\n      it('should validate custom levels', () => {\r\n        const options = { customLevels: ['urgent', 'normal', 'low'] };\r\n        \r\n        expect(constraint.validate('urgent', { constraints: [options] } as any)).toBe(true);\r\n        expect(constraint.validate('high', { constraints: [options] } as any)).toBe(false);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('SeverityLevelUtils', () => {\r\n    describe('numericToString', () => {\r\n      it('should convert numeric severity to string correctly', () => {\r\n        expect(SeverityLevelUtils.numericToString(0)).toBe(SeverityLevel.NONE);\r\n        expect(SeverityLevelUtils.numericToString(2.5)).toBe(SeverityLevel.LOW);\r\n        expect(SeverityLevelUtils.numericToString(5.0)).toBe(SeverityLevel.MEDIUM);\r\n        expect(SeverityLevelUtils.numericToString(7.5)).toBe(SeverityLevel.HIGH);\r\n        expect(SeverityLevelUtils.numericToString(9.5)).toBe(SeverityLevel.CRITICAL);\r\n      });\r\n    });\r\n\r\n    describe('stringToNumericRange', () => {\r\n      it('should convert string severity to numeric range', () => {\r\n        expect(SeverityLevelUtils.stringToNumericRange('low')).toEqual([0.1, 3.9]);\r\n        expect(SeverityLevelUtils.stringToNumericRange('medium')).toEqual([4.0, 6.9]);\r\n        expect(SeverityLevelUtils.stringToNumericRange('high')).toEqual([7.0, 8.9]);\r\n        expect(SeverityLevelUtils.stringToNumericRange('critical')).toEqual([9.0, 10.0]);\r\n        expect(SeverityLevelUtils.stringToNumericRange('none')).toEqual([0, 0]);\r\n      });\r\n    });\r\n\r\n    describe('getPriority', () => {\r\n      it('should return correct priority for string levels', () => {\r\n        expect(SeverityLevelUtils.getPriority('critical')).toBe(10);\r\n        expect(SeverityLevelUtils.getPriority('high')).toBe(8);\r\n        expect(SeverityLevelUtils.getPriority('medium')).toBe(5);\r\n        expect(SeverityLevelUtils.getPriority('low')).toBe(2);\r\n        expect(SeverityLevelUtils.getPriority('none')).toBe(0);\r\n      });\r\n\r\n      it('should return numeric value for numeric input', () => {\r\n        expect(SeverityLevelUtils.getPriority(7.5)).toBe(7.5);\r\n        expect(SeverityLevelUtils.getPriority(0)).toBe(0);\r\n        expect(SeverityLevelUtils.getPriority(10)).toBe(10);\r\n      });\r\n    });\r\n\r\n    describe('compare', () => {\r\n      it('should compare severity levels correctly', () => {\r\n        expect(SeverityLevelUtils.compare('low', 'high')).toBe(-1);\r\n        expect(SeverityLevelUtils.compare('high', 'low')).toBe(1);\r\n        expect(SeverityLevelUtils.compare('medium', 'medium')).toBe(0);\r\n        expect(SeverityLevelUtils.compare(5.0, 7.0)).toBe(-1);\r\n        expect(SeverityLevelUtils.compare('critical', 10.0)).toBe(0);\r\n        expect(SeverityLevelUtils.compare(9.5, 9.5)).toBe(0);\r\n      });\r\n    });\r\n\r\n    describe('sortByPriority', () => {\r\n      it('should sort severity levels by priority (highest first)', () => {\r\n        const severities = ['low', 'critical', 'medium', 'high'];\r\n        const sorted = SeverityLevelUtils.sortByPriority(severities);\r\n        \r\n        expect(sorted).toEqual(['critical', 'high', 'medium', 'low']);\r\n      });\r\n\r\n      it('should sort mixed string and numeric severities', () => {\r\n        const severities = ['low', 9.5, 'medium', 7.0];\r\n        const sorted = SeverityLevelUtils.sortByPriority(severities);\r\n        \r\n        expect(sorted).toEqual([9.5, 7.0, 'medium', 'low']);\r\n      });\r\n    });\r\n\r\n    describe('isValid', () => {\r\n      it('should validate severity levels correctly', () => {\r\n        expect(SeverityLevelUtils.isValid('high')).toBe(true);\r\n        expect(SeverityLevelUtils.isValid(7.5)).toBe(true);\r\n        expect(SeverityLevelUtils.isValid('invalid')).toBe(false);\r\n        expect(SeverityLevelUtils.isValid(-1)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('normalize', () => {\r\n      it('should normalize string severity levels', () => {\r\n        expect(SeverityLevelUtils.normalize('HIGH')).toBe('high');\r\n        expect(SeverityLevelUtils.normalize('Critical')).toBe('critical');\r\n      });\r\n\r\n      it('should return numeric values unchanged', () => {\r\n        expect(SeverityLevelUtils.normalize(7.5)).toBe(7.5);\r\n        expect(SeverityLevelUtils.normalize(0)).toBe(0);\r\n      });\r\n\r\n      it('should return null for invalid values', () => {\r\n        expect(SeverityLevelUtils.normalize('invalid')).toBeNull();\r\n        expect(SeverityLevelUtils.normalize(-1)).toBeNull();\r\n      });\r\n    });\r\n\r\n    describe('getColorCode', () => {\r\n      it('should return appropriate color codes', () => {\r\n        expect(SeverityLevelUtils.getColorCode('critical')).toBe('#dc3545');\r\n        expect(SeverityLevelUtils.getColorCode('high')).toBe('#fd7e14');\r\n        expect(SeverityLevelUtils.getColorCode('medium')).toBe('#ffc107');\r\n        expect(SeverityLevelUtils.getColorCode('low')).toBe('#28a745');\r\n        expect(SeverityLevelUtils.getColorCode('none')).toBe('#6c757d');\r\n      });\r\n    });\r\n\r\n    describe('getDescription', () => {\r\n      it('should return appropriate descriptions', () => {\r\n        const criticalDesc = SeverityLevelUtils.getDescription('critical');\r\n        const lowDesc = SeverityLevelUtils.getDescription('low');\r\n        \r\n        expect(criticalDesc).toContain('immediate attention');\r\n        expect(lowDesc).toContain('time permits');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('DTO Validation', () => {\r\n    it('should validate DTO with valid severity levels', async () => {\r\n      const dto = plainToClass(TestSeverityDto, {\r\n        severity: 'high',\r\n        stringSeverity: 'critical',\r\n        numericSeverity: 7.5,\r\n      });\r\n\r\n      const errors = await validate(dto);\r\n      expect(errors).toHaveLength(0);\r\n    });\r\n\r\n    it('should reject DTO with invalid severity levels', async () => {\r\n      const dto = plainToClass(TestSeverityDto, {\r\n        severity: 'invalid',\r\n        stringSeverity: 123, // Should be string\r\n        numericSeverity: 'high', // Should be number\r\n      });\r\n\r\n      const errors = await validate(dto);\r\n      expect(errors.length).toBeGreaterThan(0);\r\n    });\r\n  });\r\n});"], "version": 3}