e82751a415a35666ca3f46d6948988b6
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j;
Object.defineProperty(exports, "__esModule", { value: true });
exports.Asset = void 0;
const typeorm_1 = require("typeorm");
const vulnerability_entity_1 = require("./vulnerability.entity");
const scan_result_entity_1 = require("./scan-result.entity");
/**
 * Asset entity
 * Represents IT assets that can be scanned for vulnerabilities
 */
let Asset = class Asset {
    /**
     * Check if asset is active
     */
    get isActive() {
        return this.status === 'active';
    }
    /**
     * Check if asset is critical
     */
    get isCritical() {
        return this.criticality === 'critical';
    }
    /**
     * Check if asset is due for scan
     */
    get isDueForScan() {
        if (!this.lastScanDate || !this.isScannable)
            return true;
        const nextScanDate = new Date(this.lastScanDate);
        nextScanDate.setHours(nextScanDate.getHours() + this.scanFrequency);
        return new Date() >= nextScanDate;
    }
    /**
     * Get asset age in days
     */
    get ageInDays() {
        if (!this.firstDiscovered)
            return 0;
        const now = new Date();
        const diffMs = now.getTime() - this.firstDiscovered.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Get days since last scan
     */
    get daysSinceLastScan() {
        if (!this.lastScanDate)
            return null;
        const now = new Date();
        const diffMs = now.getTime() - this.lastScanDate.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Update last seen timestamp
     */
    updateLastSeen() {
        this.lastSeen = new Date();
    }
    /**
     * Update vulnerability counts
     */
    updateVulnerabilityCounts(counts) {
        this.vulnerabilityCounts = counts;
        this.calculateRiskScore();
    }
    /**
     * Calculate risk score based on vulnerabilities and criticality
     */
    calculateRiskScore() {
        if (!this.vulnerabilityCounts) {
            this.riskScore = 0;
            return;
        }
        const { critical, high, medium, low } = this.vulnerabilityCounts;
        // Base score from vulnerabilities
        let baseScore = (critical * 4) + (high * 3) + (medium * 2) + (low * 1);
        // Normalize to 0-10 scale (assuming max 100 vulnerabilities)
        baseScore = Math.min(baseScore / 10, 10);
        // Apply criticality multiplier
        const criticalityMultipliers = {
            low: 0.5,
            medium: 1.0,
            high: 1.5,
            critical: 2.0,
        };
        this.riskScore = Math.min(baseScore * criticalityMultipliers[this.criticality], 10);
        this.riskScore = Math.round(this.riskScore * 10) / 10; // Round to 1 decimal
    }
    /**
     * Add software to inventory
     */
    addSoftware(software) {
        if (!this.softwareInventory) {
            this.softwareInventory = [];
        }
        // Check if software already exists
        const existingIndex = this.softwareInventory.findIndex(s => s.name === software.name && s.version === software.version);
        if (existingIndex >= 0) {
            this.softwareInventory[existingIndex] = software;
        }
        else {
            this.softwareInventory.push(software);
        }
    }
    /**
     * Add service to asset
     */
    addService(service) {
        if (!this.services) {
            this.services = [];
        }
        // Check if service already exists
        const existingIndex = this.services.findIndex(s => s.name === service.name && s.port === service.port && s.protocol === service.protocol);
        if (existingIndex >= 0) {
            this.services[existingIndex] = service;
        }
        else {
            this.services.push(service);
        }
    }
    /**
     * Add tag to asset
     */
    addTag(tag) {
        if (!this.tags) {
            this.tags = [];
        }
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }
    /**
     * Remove tag from asset
     */
    removeTag(tag) {
        if (this.tags) {
            this.tags = this.tags.filter(t => t !== tag);
        }
    }
    /**
     * Check if asset has specific tag
     */
    hasTag(tag) {
        return this.tags ? this.tags.includes(tag) : false;
    }
};
exports.Asset = Asset;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Asset.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Asset.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'server',
            'workstation',
            'network_device',
            'mobile_device',
            'iot_device',
            'container',
            'virtual_machine',
            'cloud_instance',
            'database',
            'web_application',
            'api',
            'service',
        ],
    }),
    __metadata("design:type", String)
], Asset.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['active', 'inactive', 'decommissioned', 'maintenance', 'unknown'],
        default: 'active',
    }),
    __metadata("design:type", String)
], Asset.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium',
    }),
    __metadata("design:type", String)
], Asset.prototype, "criticality", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['production', 'staging', 'development', 'testing', 'sandbox'],
        default: 'production',
    }),
    __metadata("design:type", String)
], Asset.prototype, "environment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ip_address', nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "ipAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ip_addresses', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], Asset.prototype, "ipAddresses", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "hostname", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mac_address', nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "macAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'operating_system', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "operatingSystem", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'network_info', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "networkInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'hardware_info', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "hardwareInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'software_inventory', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Array !== "undefined" && Array) === "function" ? _a : Object)
], Asset.prototype, "softwareInventory", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Array !== "undefined" && Array) === "function" ? _b : Object)
], Asset.prototype, "services", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "owner", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "location", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'compliance_requirements', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], Asset.prototype, "complianceRequirements", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'security_controls', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Array !== "undefined" && Array) === "function" ? _c : Object)
], Asset.prototype, "securityControls", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], Asset.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'discovery_method', nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "discoveryMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'first_discovered', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], Asset.prototype, "firstDiscovered", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_seen', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], Asset.prototype, "lastSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_scan_date', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], Asset.prototype, "lastScanDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'scan_frequency', type: 'integer', default: 168 }) // Weekly by default
    ,
    __metadata("design:type", Number)
], Asset.prototype, "scanFrequency", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_scannable', default: true }),
    __metadata("design:type", Boolean)
], Asset.prototype, "isScannable", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'scan_config', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "scanConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'risk_score', type: 'decimal', precision: 3, scale: 1, default: 0 }),
    __metadata("design:type", Number)
], Asset.prototype, "riskScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vulnerability_counts', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "vulnerabilityCounts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_g = typeof Record !== "undefined" && Record) === "function" ? _g : Object)
], Asset.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], Asset.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_h = typeof Date !== "undefined" && Date) === "function" ? _h : Object)
], Asset.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_j = typeof Date !== "undefined" && Date) === "function" ? _j : Object)
], Asset.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => vulnerability_entity_1.Vulnerability, vulnerability => vulnerability.asset),
    __metadata("design:type", Array)
], Asset.prototype, "vulnerabilities", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => scan_result_entity_1.ScanResult, scanResult => scanResult.asset),
    __metadata("design:type", Array)
], Asset.prototype, "scanResults", void 0);
exports.Asset = Asset = __decorate([
    (0, typeorm_1.Entity)('assets'),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['criticality']),
    (0, typeorm_1.Index)(['environment']),
    (0, typeorm_1.Index)(['ipAddress']),
    (0, typeorm_1.Index)(['hostname']),
    (0, typeorm_1.Index)(['lastScanDate'])
], Asset);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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