{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\strategies\\local.strategy.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAA2E;AAC3E,+CAAoD;AACpD,mDAA0C;AAC1C,kDAA8C;AAE9C;;;GAGG;AAEI,IAAM,aAAa,qBAAnB,MAAM,aAAc,SAAQ,IAAA,2BAAgB,EAAC,yBAAQ,EAAE,OAAO,CAAC;IAGpE,YAA6B,WAAwB;QACnD,KAAK,CAAC;YACJ,aAAa,EAAE,OAAO,EAAE,gCAAgC;YACxD,aAAa,EAAE,UAAU;YACzB,iBAAiB,EAAE,IAAI,EAAE,yCAAyC;SACnE,CAAC,CAAC;QALwB,gBAAW,GAAX,WAAW,CAAa;QAFpC,WAAM,GAAG,IAAI,eAAM,CAAC,eAAa,CAAC,IAAI,CAAC,CAAC;IAQzD,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,QAAQ,CAAC,OAAY,EAAE,KAAa,EAAE,QAAgB;QAC1D,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE;gBAC/D,KAAK;gBACL,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;aACrC,CAAC,CAAC;YAEH,iBAAiB;YACjB,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mDAAmD,EAAE;oBACpE,QAAQ,EAAE,CAAC,CAAC,KAAK;oBACjB,WAAW,EAAE,CAAC,CAAC,QAAQ;oBACvB,SAAS,EAAE,OAAO,CAAC,EAAE;iBACtB,CAAC,CAAC;gBACH,MAAM,IAAI,8BAAqB,CAAC,iCAAiC,CAAC,CAAC;YACrE,CAAC;YAED,kBAAkB;YAClB,MAAM,eAAe,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;YAEnD,wBAAwB;YACxB,MAAM,UAAU,GAAG,4BAA4B,CAAC;YAChD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8CAA8C,EAAE;oBAC/D,KAAK,EAAE,eAAe;oBACtB,SAAS,EAAE,OAAO,CAAC,EAAE;iBACtB,CAAC,CAAC;gBACH,MAAM,IAAI,8BAAqB,CAAC,sBAAsB,CAAC,CAAC;YAC1D,CAAC;YAED,2BAA2B;YAC3B,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,EAAE;oBAC7D,KAAK,EAAE,eAAe;oBACtB,cAAc,EAAE,QAAQ,CAAC,MAAM;oBAC/B,SAAS,EAAE,OAAO,CAAC,EAAE;iBACtB,CAAC,CAAC;gBACH,MAAM,IAAI,8BAAqB,CAAC,qBAAqB,CAAC,CAAC;YACzD,CAAC;YAED,oBAAoB;YACpB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAE5E,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mDAAmD,EAAE;oBACpE,KAAK,EAAE,eAAe;oBACtB,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;iBACrC,CAAC,CAAC;gBACH,MAAM,IAAI,8BAAqB,CAAC,qBAAqB,CAAC,CAAC;YACzD,CAAC;YAED,gCAAgC;YAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,EAAE;gBACjD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;aACrC,CAAC,CAAC;YAEH,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,YAAY,EAAE;oBACZ,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;oBACpC,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,MAAM,EAAE,OAAO;iBAChB;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBAC9C,KAAK;gBACL,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;aACrC,CAAC,CAAC;YAEH,IAAI,KAAK,YAAY,8BAAqB,EAAE,CAAC;gBAC3C,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,8BAAqB,CAAC,uBAAuB,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;CACF,CAAA;AA3GY,sCAAa;wBAAb,aAAa;IADzB,IAAA,mBAAU,GAAE;yDAI+B,0BAAW,oBAAX,0BAAW;GAH1C,aAAa,CA2GzB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\strategies\\local.strategy.ts"], "sourcesContent": ["import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';\r\nimport { PassportStrategy } from '@nestjs/passport';\r\nimport { Strategy } from 'passport-local';\r\nimport { AuthService } from '../auth.service';\r\n\r\n/**\r\n * Local authentication strategy for Passport\r\n * Validates username/password credentials for login\r\n */\r\n@Injectable()\r\nexport class LocalStrategy extends PassportStrategy(Strategy, 'local') {\r\n  private readonly logger = new Logger(LocalStrategy.name);\r\n\r\n  constructor(private readonly authService: AuthService) {\r\n    super({\r\n      usernameField: 'email', // Use email instead of username\r\n      passwordField: 'password',\r\n      passReqToCallback: true, // Pass request object to validate method\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Validate user credentials\r\n   * This method is called automatically by Passport during local authentication\r\n   * \r\n   * @param request Express request object\r\n   * @param email User email address\r\n   * @param password User password\r\n   * @returns Promise<any> User object if credentials are valid\r\n   */\r\n  async validate(request: any, email: string, password: string): Promise<any> {\r\n    try {\r\n      this.logger.debug('Validating local authentication credentials', {\r\n        email,\r\n        ipAddress: request.ip,\r\n        userAgent: request.get('User-Agent'),\r\n      });\r\n\r\n      // Validate input\r\n      if (!email || !password) {\r\n        this.logger.warn('Missing email or password in local authentication', {\r\n          hasEmail: !!email,\r\n          hasPassword: !!password,\r\n          ipAddress: request.ip,\r\n        });\r\n        throw new UnauthorizedException('Email and password are required');\r\n      }\r\n\r\n      // Normalize email\r\n      const normalizedEmail = email.toLowerCase().trim();\r\n\r\n      // Validate email format\r\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n      if (!emailRegex.test(normalizedEmail)) {\r\n        this.logger.warn('Invalid email format in local authentication', {\r\n          email: normalizedEmail,\r\n          ipAddress: request.ip,\r\n        });\r\n        throw new UnauthorizedException('Invalid email format');\r\n      }\r\n\r\n      // Validate password length\r\n      if (password.length < 8) {\r\n        this.logger.warn('Password too short in local authentication', {\r\n          email: normalizedEmail,\r\n          passwordLength: password.length,\r\n          ipAddress: request.ip,\r\n        });\r\n        throw new UnauthorizedException('Invalid credentials');\r\n      }\r\n\r\n      // Authenticate user\r\n      const user = await this.authService.validateUser(normalizedEmail, password);\r\n\r\n      if (!user) {\r\n        this.logger.warn('Local authentication failed - invalid credentials', {\r\n          email: normalizedEmail,\r\n          ipAddress: request.ip,\r\n          userAgent: request.get('User-Agent'),\r\n        });\r\n        throw new UnauthorizedException('Invalid credentials');\r\n      }\r\n\r\n      // Log successful authentication\r\n      this.logger.log('Local authentication successful', {\r\n        userId: user.id,\r\n        email: user.email,\r\n        ipAddress: request.ip,\r\n        userAgent: request.get('User-Agent'),\r\n      });\r\n\r\n      // Return user object with request metadata\r\n      return {\r\n        ...user,\r\n        authMetadata: {\r\n          ipAddress: request.ip,\r\n          userAgent: request.get('User-Agent'),\r\n          timestamp: new Date(),\r\n          method: 'local',\r\n        },\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error('Local authentication error', {\r\n        email,\r\n        error: error.message,\r\n        ipAddress: request.ip,\r\n        userAgent: request.get('User-Agent'),\r\n      });\r\n\r\n      if (error instanceof UnauthorizedException) {\r\n        throw error;\r\n      }\r\n\r\n      throw new UnauthorizedException('Authentication failed');\r\n    }\r\n  }\r\n}\r\n"], "version": 3}