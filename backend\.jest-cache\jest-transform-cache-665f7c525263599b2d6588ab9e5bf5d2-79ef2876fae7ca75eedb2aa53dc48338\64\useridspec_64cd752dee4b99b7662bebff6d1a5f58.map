{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\user-id.spec.ts", "mappings": ";;AAAA,mFAAkE;AAClE,uFAAsE;AACtE,qGAAmF;AAEnF,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;IACtB,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,MAAM,GAAG,6BAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEvC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,MAAM,GAAG,6BAAM,CAAC,QAAQ,EAAE,CAAC;YAEjC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,6BAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,QAAQ,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,MAAM,GAAG,6BAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,QAAQ,GAAG,6BAAM,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,IAAI,GAAG,6BAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAErC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,6BAAM,CAAC,IAAW,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,6BAAM,CAAC,SAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,6BAAM,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,6BAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,6BAAM,CAAC,GAAU,CAAC,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,6BAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,6BAAM,CAAC,sCAAsC,CAAC,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,SAAS,GAAG,sCAAsC,CAAC;YACzD,MAAM,CAAC,6BAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,6BAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,6BAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,6BAAM,CAAC,OAAO,CAAC,IAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,6BAAM,CAAC,OAAO,CAAC,SAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,SAAS,GAAG,sCAAsC,CAAC;YACzD,MAAM,MAAM,GAAG,6BAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,CAAC,MAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,MAAM,GAAG,6BAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,IAAI,MAAc,CAAC;QAEnB,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,6BAAM,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,WAAW,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;YAC3C,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,aAAa,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,MAAM,GAAG,6BAAM,CAAC,QAAQ,EAAE,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,OAAO,GAAG,6BAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,OAAO,GAAG,6BAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAExC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,OAAO,GAAG,6BAAM,CAAC,QAAQ,EAAE,CAAC;YAClC,MAAM,OAAO,GAAG,6BAAM,CAAC,QAAQ,EAAE,CAAC;YAElC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,MAAM,GAAG,6BAAM,CAAC,QAAQ,EAAE,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,MAAM,GAAG,6BAAM,CAAC,QAAQ,EAAE,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,UAAU,GAAG,6BAAM,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,aAAa,GAAG,6BAAM,CAAC,SAAS,EAAE,CAAC;YACzC,MAAM,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,WAAW,GAAG,6BAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACnD,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,GAAG,EAAE,CAAC,6BAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;YACzE,MAAM,CAAC,GAAG,EAAE,CAAC,6BAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,KAAK,GAAG,6BAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,KAAK,GAAG,6BAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,KAAK,GAAG,6BAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,KAAK,GAAG,6BAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,MAAM,GAAG,6BAAM,CAAC,QAAQ,EAAE,CAAC;YACjC,MAAM,QAAQ,GAAG,iCAAQ,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAEhD,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,MAAM,GAAG,6BAAM,CAAC,QAAQ,EAAE,CAAC;YACjC,MAAM,QAAQ,GAAG,iCAAQ,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,QAAQ,GAAG,GAAG,QAAQ,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YAErD,MAAM,MAAM,GAAG,6BAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAE9C,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,CAAC,MAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,MAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,CAAC,6BAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACnD,MAAM,CAAC,6BAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC5C,MAAM,CAAC,6BAAM,CAAC,aAAa,CAAC,IAAW,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACrD,MAAM,CAAC,6BAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,MAAM,GAAG,6BAAM,CAAC,aAAa,CAAC,6BAA6B,CAAC,CAAC;YACnE,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,OAAO,GAAG,6BAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEvC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAErD,uBAAuB;YACvB,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,OAAO,GAAG,6BAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,GAAG,EAAE,CAAC,6BAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,MAAM,GAAG,6BAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,MAAM,GAAG,6BAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAE7B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,IAAI,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,6BAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAErC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,MAAM,GAAG,6BAAM,CAAC,QAAQ,EAAE,CAAC;YACjC,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC;YAEnC,sDAAsD;YACtD,MAAM,CAAC,GAAG,EAAE;gBACT,MAAc,CAAC,MAAM,GAAG,UAAU,CAAC;YACtC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,sDAAsD;YAExE,gCAAgC;YAChC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAC1B,MAAM,MAAM,GAAG,6BAAM,CAAC,QAAQ,EAAE,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,KAAK,GAAG,sCAAsC,CAAC;YACrD,MAAM,KAAK,GAAG,sCAAsC,CAAC;YAErD,MAAM,OAAO,GAAG,6BAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,OAAO,GAAG,6BAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAEzC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,MAAM,GAAG,6BAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,QAAQ,GAAG,6BAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAChD,MAAM,QAAQ,GAAG,6BAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,WAAW,GAAG,6BAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACnD,MAAM,WAAW,GAAG,6BAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACnD,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\user-id.spec.ts"], "sourcesContent": ["import { UserId } from '../../value-objects/user-id.value-object';\r\nimport { TenantId } from '../../value-objects/tenant-id.value-object';\r\nimport { UniqueEntityId } from '../../value-objects/unique-entity-id.value-object';\r\n\r\ndescribe('UserId', () => {\r\n  describe('creation', () => {\r\n    it('should create a valid user ID from UUID string', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const userId = UserId.fromString(uuid);\r\n\r\n      expect(userId.value).toBe(uuid);\r\n      expect(userId.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should generate a new user ID', () => {\r\n      const userId = UserId.generate();\r\n\r\n      expect(userId.value).toBeDefined();\r\n      expect(userId.isValid()).toBe(true);\r\n      expect(UserId.isValid(userId.value)).toBe(true);\r\n    });\r\n\r\n    it('should create user ID from UniqueEntityId', () => {\r\n      const uniqueId = UniqueEntityId.generate();\r\n      const userId = UserId.fromUniqueEntityId(uniqueId);\r\n\r\n      expect(userId.value).toBe(uniqueId.value);\r\n      expect(userId.uniqueId.equals(uniqueId)).toBe(true);\r\n    });\r\n\r\n    it('should create user ID from existing ID', () => {\r\n      const original = UserId.generate();\r\n      const copy = UserId.fromId(original);\r\n\r\n      expect(copy.value).toBe(original.value);\r\n      expect(copy.equals(original)).toBe(true);\r\n      expect(copy).not.toBe(original); // Different instances\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should throw error for null value', () => {\r\n      expect(() => new UserId(null as any)).toThrow('UserId cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for undefined value', () => {\r\n      expect(() => new UserId(undefined as any)).toThrow('UserId cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for empty string', () => {\r\n      expect(() => new UserId('')).toThrow('UserId cannot be empty');\r\n    });\r\n\r\n    it('should throw error for whitespace string', () => {\r\n      expect(() => new UserId('   ')).toThrow('UserId cannot be empty');\r\n    });\r\n\r\n    it('should throw error for non-string value', () => {\r\n      expect(() => new UserId(123 as any)).toThrow('UserId must be a string');\r\n    });\r\n\r\n    it('should throw error for invalid UUID format', () => {\r\n      expect(() => new UserId('invalid-uuid')).toThrow('Invalid UserId format');\r\n    });\r\n\r\n    it('should throw error for non-UUID v4 format', () => {\r\n      expect(() => new UserId('123e4567-e89b-22d3-a456-************')).toThrow('Invalid UserId format');\r\n    });\r\n  });\r\n\r\n  describe('static validation methods', () => {\r\n    it('should validate correct UUID strings', () => {\r\n      const validUuid = '123e4567-e89b-42d3-a456-************';\r\n      expect(UserId.isValid(validUuid)).toBe(true);\r\n    });\r\n\r\n    it('should reject invalid UUID strings', () => {\r\n      expect(UserId.isValid('invalid-uuid')).toBe(false);\r\n      expect(UserId.isValid('')).toBe(false);\r\n      expect(UserId.isValid(null as any)).toBe(false);\r\n      expect(UserId.isValid(undefined as any)).toBe(false);\r\n    });\r\n\r\n    it('should try parse valid UUID', () => {\r\n      const validUuid = '123e4567-e89b-42d3-a456-************';\r\n      const result = UserId.tryParse(validUuid);\r\n\r\n      expect(result).not.toBeNull();\r\n      expect(result!.value).toBe(validUuid);\r\n    });\r\n\r\n    it('should return null for invalid UUID in tryParse', () => {\r\n      const result = UserId.tryParse('invalid-uuid');\r\n      expect(result).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('utility methods', () => {\r\n    let userId: UserId;\r\n\r\n    beforeEach(() => {\r\n      userId = UserId.fromString('123e4567-e89b-42d3-a456-************');\r\n    });\r\n\r\n    it('should get version', () => {\r\n      expect(userId.getVersion()).toBe(1);\r\n    });\r\n\r\n    it('should get variant', () => {\r\n      expect(userId.getVariant()).toBe('RFC4122');\r\n    });\r\n\r\n    it('should get short string representation', () => {\r\n      const shortString = userId.toShortString();\r\n      expect(shortString).toBe('123e4567');\r\n      expect(userId.toShortString(4)).toBe('123e');\r\n    });\r\n\r\n    it('should get compact string representation', () => {\r\n      const compactString = userId.toCompactString();\r\n      expect(compactString).toBe('123e4567e89b42d3a456************');\r\n    });\r\n\r\n    it('should convert to uppercase', () => {\r\n      const upperCase = userId.toUpperCase();\r\n      expect(upperCase).toBe('123e4567-e89b-42d3-a456-************');\r\n    });\r\n\r\n    it('should convert to lowercase', () => {\r\n      const lowerCase = userId.toLowerCase();\r\n      expect(lowerCase).toBe('123e4567-e89b-42d3-a456-************');\r\n    });\r\n  });\r\n\r\n  describe('equality and comparison', () => {\r\n    it('should be equal to itself', () => {\r\n      const userId = UserId.generate();\r\n      expect(userId.equals(userId)).toBe(true);\r\n      expect(userId.matches(userId)).toBe(true);\r\n    });\r\n\r\n    it('should be equal to user ID with same value', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const userId1 = UserId.fromString(uuid);\r\n      const userId2 = UserId.fromString(uuid);\r\n\r\n      expect(userId1.equals(userId2)).toBe(true);\r\n      expect(userId1.matches(userId2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to user ID with different value', () => {\r\n      const userId1 = UserId.generate();\r\n      const userId2 = UserId.generate();\r\n\r\n      expect(userId1.equals(userId2)).toBe(false);\r\n      expect(userId1.matches(userId2)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to null or undefined', () => {\r\n      const userId = UserId.generate();\r\n      expect(userId.equals(null as any)).toBe(false);\r\n      expect(userId.equals(undefined as any)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to non-UserId object', () => {\r\n      const userId = UserId.generate();\r\n      expect(userId.equals({} as any)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('special user IDs', () => {\r\n    it('should create system user ID', () => {\r\n      const systemUser = UserId.system();\r\n      expect(systemUser.isSystem()).toBe(true);\r\n      expect(systemUser.isAnonymous()).toBe(false);\r\n    });\r\n\r\n    it('should create anonymous user ID', () => {\r\n      const anonymousUser = UserId.anonymous();\r\n      expect(anonymousUser.isAnonymous()).toBe(true);\r\n      expect(anonymousUser.isSystem()).toBe(false);\r\n    });\r\n\r\n    it('should create service user ID', () => {\r\n      const serviceUser = UserId.service('auth-service');\r\n      expect(serviceUser.isService()).toBe(true);\r\n      expect(serviceUser.isSystem()).toBe(false);\r\n      expect(serviceUser.isAnonymous()).toBe(false);\r\n    });\r\n\r\n    it('should throw error for empty service name', () => {\r\n      expect(() => UserId.service('')).toThrow('Service name cannot be empty');\r\n      expect(() => UserId.service('   ')).toThrow('Service name cannot be empty');\r\n    });\r\n\r\n    it('should create deterministic user ID from seed', () => {\r\n      const user1 = UserId.fromSeed('test');\r\n      const user2 = UserId.fromSeed('test');\r\n      expect(user1.equals(user2)).toBe(true);\r\n    });\r\n\r\n    it('should create different user IDs from different seeds', () => {\r\n      const user1 = UserId.fromSeed('test1');\r\n      const user2 = UserId.fromSeed('test2');\r\n      expect(user1.equals(user2)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('tenant scoping', () => {\r\n    it('should scope user ID to tenant', () => {\r\n      const userId = UserId.generate();\r\n      const tenantId = TenantId.generate();\r\n      const scopedId = userId.scopeToTenant(tenantId);\r\n\r\n      expect(scopedId).toBe(`${tenantId.value}:${userId.value}`);\r\n    });\r\n\r\n    it('should parse scoped user ID', () => {\r\n      const userId = UserId.generate();\r\n      const tenantId = TenantId.generate();\r\n      const scopedId = `${tenantId.value}:${userId.value}`;\r\n\r\n      const parsed = UserId.parseScopedId(scopedId);\r\n\r\n      expect(parsed).not.toBeNull();\r\n      expect(parsed!.userId.equals(userId)).toBe(true);\r\n      expect(parsed!.tenantId.equals(tenantId)).toBe(true);\r\n    });\r\n\r\n    it('should return null for invalid scoped ID format', () => {\r\n      expect(UserId.parseScopedId('invalid')).toBeNull();\r\n      expect(UserId.parseScopedId('')).toBeNull();\r\n      expect(UserId.parseScopedId(null as any)).toBeNull();\r\n      expect(UserId.parseScopedId('too:many:colons')).toBeNull();\r\n    });\r\n\r\n    it('should return null for invalid UUID parts in scoped ID', () => {\r\n      const result = UserId.parseScopedId('invalid-tenant:invalid-user');\r\n      expect(result).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('bulk operations', () => {\r\n    it('should generate multiple user IDs', () => {\r\n      const userIds = UserId.generateMany(5);\r\n\r\n      expect(userIds).toHaveLength(5);\r\n      expect(userIds.every(id => id.isValid())).toBe(true);\r\n      \r\n      // All should be unique\r\n      const uniqueValues = new Set(userIds.map(id => id.value));\r\n      expect(uniqueValues.size).toBe(5);\r\n    });\r\n\r\n    it('should handle zero count for generateMany', () => {\r\n      const userIds = UserId.generateMany(0);\r\n      expect(userIds).toHaveLength(0);\r\n    });\r\n\r\n    it('should throw error for negative count', () => {\r\n      expect(() => UserId.generateMany(-1)).toThrow('Count must be non-negative');\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    it('should convert to string', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const userId = UserId.fromString(uuid);\r\n      expect(userId.toString()).toBe(uuid);\r\n    });\r\n\r\n    it('should convert to JSON', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const userId = UserId.fromString(uuid);\r\n      const json = userId.toJSON();\r\n\r\n      expect(json.value).toBe(uuid);\r\n      expect(json.type).toBe('UserId');\r\n      expect(json.version).toBe(1);\r\n      expect(json.variant).toBe('RFC4122');\r\n      expect(json.shortString).toBe('123e4567');\r\n      expect(json.compactString).toBe('123e4567e89b42d3a456************');\r\n    });\r\n\r\n    it('should create from JSON', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const json = { value: uuid };\r\n      const userId = UserId.fromJSON(json);\r\n\r\n      expect(userId.value).toBe(uuid);\r\n    });\r\n  });\r\n\r\n  describe('immutability', () => {\r\n    it('should be immutable after creation', () => {\r\n      const userId = UserId.generate();\r\n      const originalValue = userId.value;\r\n\r\n      // Attempt to modify (should not work due to readonly)\r\n      expect(() => {\r\n        (userId as any)._value = 'modified';\r\n      }).not.toThrow(); // TypeScript prevents this, but runtime doesn't throw\r\n\r\n      // Value should remain unchanged\r\n      expect(userId.value).toBe(originalValue);\r\n    });\r\n\r\n    it('should be frozen', () => {\r\n      const userId = UserId.generate();\r\n      expect(Object.isFrozen(userId)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle case-insensitive UUID comparison', () => {\r\n      const uuid1 = '123e4567-e89b-42d3-a456-************';\r\n      const uuid2 = '123e4567-e89b-42d3-a456-************';\r\n      \r\n      const userId1 = UserId.fromString(uuid1);\r\n      const userId2 = UserId.fromString(uuid2);\r\n      \r\n      expect(userId1.equals(userId2)).toBe(true);\r\n    });\r\n\r\n    it('should maintain original case in value', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const userId = UserId.fromString(uuid);\r\n      expect(userId.value).toBe(uuid);\r\n    });\r\n\r\n    it('should create consistent service user IDs', () => {\r\n      const service1 = UserId.service('auth-service');\r\n      const service2 = UserId.service('auth-service');\r\n      expect(service1.equals(service2)).toBe(true);\r\n    });\r\n\r\n    it('should create different service user IDs for different services', () => {\r\n      const authService = UserId.service('auth-service');\r\n      const dataService = UserId.service('data-service');\r\n      expect(authService.equals(dataService)).toBe(false);\r\n    });\r\n  });\r\n});\r\n"], "version": 3}