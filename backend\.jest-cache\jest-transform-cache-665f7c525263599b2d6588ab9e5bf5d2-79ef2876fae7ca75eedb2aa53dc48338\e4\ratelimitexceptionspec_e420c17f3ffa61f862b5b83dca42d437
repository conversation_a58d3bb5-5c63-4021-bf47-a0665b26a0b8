6c19d0b684f6d7da11debef633b9ecbd
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const rate_limit_exception_1 = require("../../exceptions/rate-limit.exception");
describe('RateLimitException', () => {
    const mockResetTime = new Date('2023-01-01T12:00:00Z');
    describe('constructor', () => {
        it('should create exception with required parameters', () => {
            const exception = new rate_limit_exception_1.RateLimitException('Rate limit exceeded', 'api_requests', 'user:123', 100, 105, mockResetTime, 3600);
            expect(exception).toBeInstanceOf(rate_limit_exception_1.RateLimitException);
            expect(exception.message).toBe('Rate limit exceeded');
            expect(exception.code).toBe('RATE_LIMIT_EXCEEDED');
            expect(exception.severity).toBe('medium');
            expect(exception.category).toBe('rate_limit');
            expect(exception.limitType).toBe('api_requests');
            expect(exception.scope).toBe('user:123');
            expect(exception.limit).toBe(100);
            expect(exception.used).toBe(105);
            expect(exception.remaining).toBe(0);
            expect(exception.resetTime).toEqual(mockResetTime);
            expect(exception.windowSize).toBe(3600);
        });
        it('should calculate remaining correctly when not exceeded', () => {
            const exception = new rate_limit_exception_1.RateLimitException('Rate limit exceeded', 'api_requests', 'user:123', 100, 80, mockResetTime, 3600);
            expect(exception.remaining).toBe(20);
        });
        it('should calculate retryAfter correctly', () => {
            const futureResetTime = new Date(Date.now() + 300000); // 5 minutes from now
            const exception = new rate_limit_exception_1.RateLimitException('Rate limit exceeded', 'api_requests', 'user:123', 100, 105, futureResetTime, 3600);
            expect(exception.retryAfter).toBeGreaterThan(290);
            expect(exception.retryAfter).toBeLessThanOrEqual(300);
        });
    });
    describe('static factory methods', () => {
        describe('apiRequestLimit', () => {
            it('should create exception for API request limit with user scope', () => {
                const exception = rate_limit_exception_1.RateLimitException.apiRequestLimit(1000, 1005, mockResetTime, 3600, {
                    userId: 'user-123',
                    endpoint: '/api/users',
                    method: 'GET',
                });
                expect(exception.message).toBe('API request rate limit exceeded for GET /api/users. Limit: 1000 requests per 3600 seconds');
                expect(exception.limitType).toBe('api_requests');
                expect(exception.scope).toBe('user:user-123');
                expect(exception.limit).toBe(1000);
                expect(exception.used).toBe(1005);
                expect(exception.isApiRequestLimit()).toBe(true);
            });
            it('should create exception for API request limit with API key scope', () => {
                const exception = rate_limit_exception_1.RateLimitException.apiRequestLimit(500, 505, mockResetTime, 3600, {
                    apiKey: 'key-456',
                });
                expect(exception.scope).toBe('api_key:key-456');
                expect(exception.isApiRequestLimit()).toBe(true);
            });
            it('should create exception for API request limit with global scope', () => {
                const exception = rate_limit_exception_1.RateLimitException.apiRequestLimit(100, 105, mockResetTime);
                expect(exception.scope).toBe('global');
                expect(exception.message).toBe('API request rate limit exceeded. Limit: 100 requests per 3600 seconds');
            });
        });
        describe('operationLimit', () => {
            it('should create exception for operation limit', () => {
                const exception = rate_limit_exception_1.RateLimitException.operationLimit('file_upload', 10, 12, mockResetTime, 3600, {
                    userId: 'user-123',
                });
                expect(exception.message).toBe("Operation 'file_upload' rate limit exceeded. Limit: 10 operations per 3600 seconds");
                expect(exception.limitType).toBe('operations');
                expect(exception.scope).toBe('user:user-123');
                expect(exception.isOperationLimit()).toBe(true);
            });
        });
        describe('resourceUsageLimit', () => {
            it('should create exception for resource usage limit', () => {
                const exception = rate_limit_exception_1.RateLimitException.resourceUsageLimit('storage', 1000, 1200, mockResetTime, 86400, {
                    tenantId: 'tenant-123',
                    unit: 'MB',
                });
                expect(exception.message).toBe('storage usage rate limit exceeded. Limit: 1000 MB per 86400 seconds');
                expect(exception.limitType).toBe('resource_usage');
                expect(exception.scope).toBe('tenant:tenant-123');
                expect(exception.isResourceUsageLimit()).toBe(true);
            });
        });
        describe('concurrentOperationLimit', () => {
            it('should create exception for concurrent operation limit', () => {
                const exception = rate_limit_exception_1.RateLimitException.concurrentOperationLimit('data_processing', 5, 7, {
                    userId: 'user-123',
                });
                expect(exception.message).toBe('Concurrent data_processing limit exceeded. Limit: 5 concurrent operations');
                expect(exception.limitType).toBe('concurrent_operations');
                expect(exception.scope).toBe('user:user-123');
                expect(exception.windowSize).toBe(0);
                expect(exception.isConcurrentOperationLimit()).toBe(true);
            });
        });
        describe('bandwidthLimit', () => {
            it('should create exception for bandwidth limit', () => {
                const exception = rate_limit_exception_1.RateLimitException.bandwidthLimit(1000000, 1200000, mockResetTime, 3600, {
                    userId: 'user-123',
                    unit: 'bytes',
                    direction: 'upload',
                });
                expect(exception.message).toBe('Bandwidth upload rate limit exceeded. Limit: 1000000 bytes per 3600 seconds');
                expect(exception.limitType).toBe('bandwidth');
                expect(exception.scope).toBe('user:user-123');
                expect(exception.isBandwidthLimit()).toBe(true);
            });
        });
        describe('quotaLimit', () => {
            it('should create exception for quota limit', () => {
                const exception = rate_limit_exception_1.RateLimitException.quotaLimit('API calls', 10000, 10500, mockResetTime, 2592000, // 30 days
                {
                    tenantId: 'tenant-123',
                    unit: 'calls',
                });
                expect(exception.message).toBe('API calls quota exceeded. Limit: 10000 calls per 30 day(s)');
                expect(exception.limitType).toBe('quota');
                expect(exception.scope).toBe('tenant:tenant-123');
                expect(exception.isQuotaLimit()).toBe(true);
            });
        });
    });
    describe('type checking methods', () => {
        it('should correctly identify rate limit types', () => {
            const apiException = rate_limit_exception_1.RateLimitException.apiRequestLimit(100, 105, mockResetTime);
            const operationException = rate_limit_exception_1.RateLimitException.operationLimit('upload', 10, 12, mockResetTime);
            const resourceException = rate_limit_exception_1.RateLimitException.resourceUsageLimit('storage', 1000, 1200, mockResetTime);
            const concurrentException = rate_limit_exception_1.RateLimitException.concurrentOperationLimit('process', 5, 7);
            const bandwidthException = rate_limit_exception_1.RateLimitException.bandwidthLimit(1000, 1200, mockResetTime);
            const quotaException = rate_limit_exception_1.RateLimitException.quotaLimit('API', 10000, 10500, mockResetTime, 86400);
            expect(apiException.isApiRequestLimit()).toBe(true);
            expect(apiException.isOperationLimit()).toBe(false);
            expect(operationException.isOperationLimit()).toBe(true);
            expect(operationException.isResourceUsageLimit()).toBe(false);
            expect(resourceException.isResourceUsageLimit()).toBe(true);
            expect(resourceException.isConcurrentOperationLimit()).toBe(false);
            expect(concurrentException.isConcurrentOperationLimit()).toBe(true);
            expect(concurrentException.isBandwidthLimit()).toBe(false);
            expect(bandwidthException.isBandwidthLimit()).toBe(true);
            expect(bandwidthException.isQuotaLimit()).toBe(false);
            expect(quotaException.isQuotaLimit()).toBe(true);
            expect(quotaException.isApiRequestLimit()).toBe(false);
        });
    });
    describe('utility methods', () => {
        describe('getUsagePercentage', () => {
            it('should calculate usage percentage correctly', () => {
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api_requests', 'user:123', 100, 75, mockResetTime, 3600);
                expect(exception.getUsagePercentage()).toBe(75);
            });
            it('should handle over-limit usage', () => {
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api_requests', 'user:123', 100, 120, mockResetTime, 3600);
                expect(exception.getUsagePercentage()).toBe(120);
            });
        });
        describe('isCompletelyExhausted', () => {
            it('should return true when remaining is 0', () => {
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api_requests', 'user:123', 100, 100, mockResetTime, 3600);
                expect(exception.isCompletelyExhausted()).toBe(true);
            });
            it('should return false when remaining is greater than 0', () => {
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api_requests', 'user:123', 100, 80, mockResetTime, 3600);
                expect(exception.isCompletelyExhausted()).toBe(false);
            });
        });
        describe('getTimeUntilReset', () => {
            it('should return time until reset in seconds', () => {
                const futureResetTime = new Date(Date.now() + 300000); // 5 minutes from now
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api_requests', 'user:123', 100, 105, futureResetTime, 3600);
                const timeUntilReset = exception.getTimeUntilReset();
                expect(timeUntilReset).toBeGreaterThan(290);
                expect(timeUntilReset).toBeLessThanOrEqual(300);
            });
            it('should return 0 for past reset time', () => {
                const pastResetTime = new Date(Date.now() - 300000); // 5 minutes ago
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api_requests', 'user:123', 100, 105, pastResetTime, 3600);
                expect(exception.getTimeUntilReset()).toBe(0);
            });
        });
        describe('getTimeUntilResetFormatted', () => {
            it('should format seconds correctly', () => {
                const futureResetTime = new Date(Date.now() + 30000); // 30 seconds from now
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api_requests', 'user:123', 100, 105, futureResetTime, 3600);
                expect(exception.getTimeUntilResetFormatted()).toMatch(/\d+ second\(s\)/);
            });
            it('should format minutes correctly', () => {
                const futureResetTime = new Date(Date.now() + 300000); // 5 minutes from now
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api_requests', 'user:123', 100, 105, futureResetTime, 3600);
                expect(exception.getTimeUntilResetFormatted()).toMatch(/\d+ minute\(s\)/);
            });
            it('should format hours correctly', () => {
                const futureResetTime = new Date(Date.now() + 7200000); // 2 hours from now
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api_requests', 'user:123', 100, 105, futureResetTime, 3600);
                expect(exception.getTimeUntilResetFormatted()).toMatch(/\d+ hour\(s\)/);
            });
        });
        describe('getWindowSizeFormatted', () => {
            it('should format concurrent window', () => {
                const exception = rate_limit_exception_1.RateLimitException.concurrentOperationLimit('process', 5, 7);
                expect(exception.getWindowSizeFormatted()).toBe('concurrent');
            });
            it('should format seconds', () => {
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 30);
                expect(exception.getWindowSizeFormatted()).toBe('30 second(s)');
            });
            it('should format minutes', () => {
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 300);
                expect(exception.getWindowSizeFormatted()).toBe('5 minute(s)');
            });
            it('should format hours', () => {
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 3600);
                expect(exception.getWindowSizeFormatted()).toBe('1 hour(s)');
            });
            it('should format days', () => {
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 86400);
                expect(exception.getWindowSizeFormatted()).toBe('1 day(s)');
            });
        });
        describe('getScopeInfo', () => {
            it('should parse user scope', () => {
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 3600);
                expect(exception.getScopeInfo()).toEqual({ type: 'user', id: '123' });
            });
            it('should parse tenant scope', () => {
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api', 'tenant:456', 100, 105, mockResetTime, 3600);
                expect(exception.getScopeInfo()).toEqual({ type: 'tenant', id: '456' });
            });
            it('should parse global scope', () => {
                const exception = new rate_limit_exception_1.RateLimitException('Test', 'api', 'global', 100, 105, mockResetTime, 3600);
                expect(exception.getScopeInfo()).toEqual({ type: 'global', id: undefined });
            });
        });
        describe('getRetryRecommendations', () => {
            it('should provide recommendations for API requests', () => {
                const exception = rate_limit_exception_1.RateLimitException.apiRequestLimit(100, 105, mockResetTime);
                const recommendations = exception.getRetryRecommendations();
                expect(recommendations.backoffStrategy).toBe('exponential');
                expect(recommendations.maxRetries).toBe(3);
            });
            it('should provide recommendations for concurrent operations', () => {
                const exception = rate_limit_exception_1.RateLimitException.concurrentOperationLimit('process', 5, 7);
                const recommendations = exception.getRetryRecommendations();
                expect(recommendations.backoffStrategy).toBe('linear');
                expect(recommendations.maxRetries).toBe(10);
            });
        });
    });
    describe('getUserMessage', () => {
        it('should return user-friendly message for API requests', () => {
            const futureResetTime = new Date(Date.now() + 300000);
            const exception = rate_limit_exception_1.RateLimitException.apiRequestLimit(100, 105, futureResetTime);
            expect(exception.getUserMessage()).toMatch(/You have exceeded the API request limit\. Please wait \d+ minute\(s\) before making more requests\./);
        });
        it('should return user-friendly message for operations', () => {
            const futureResetTime = new Date(Date.now() + 300000);
            const exception = rate_limit_exception_1.RateLimitException.operationLimit('upload', 10, 12, futureResetTime);
            expect(exception.getUserMessage()).toMatch(/You have exceeded the operation limit\. Please wait \d+ minute\(s\) before trying again\./);
        });
        it('should return user-friendly message for concurrent operations', () => {
            const exception = rate_limit_exception_1.RateLimitException.concurrentOperationLimit('process', 5, 7);
            expect(exception.getUserMessage()).toBe('You have too many concurrent operations running. Please wait for some to complete before starting new ones.');
        });
    });
    describe('toApiResponse', () => {
        it('should convert to API response format with headers', () => {
            const exception = rate_limit_exception_1.RateLimitException.apiRequestLimit(100, 105, mockResetTime);
            const response = exception.toApiResponse();
            expect(response.error).toMatch(/You have exceeded the API request limit/);
            expect(response.code).toBe('RATE_LIMIT_EXCEEDED');
            expect(response.details).toMatchObject({
                limitType: 'api_requests',
                limit: 100,
                used: 105,
                remaining: 0,
                resetTime: mockResetTime.toISOString(),
            });
            expect(response.headers).toMatchObject({
                'X-RateLimit-Limit': '100',
                'X-RateLimit-Remaining': '0',
                'X-RateLimit-Reset': Math.floor(mockResetTime.getTime() / 1000).toString(),
                'Retry-After': expect.any(String),
            });
        });
    });
    describe('toJSON', () => {
        it('should convert to JSON with detailed information', () => {
            const exception = rate_limit_exception_1.RateLimitException.apiRequestLimit(100, 105, mockResetTime, 3600, {
                userId: 'user-123',
                endpoint: '/api/users',
                method: 'GET',
            });
            const json = exception.toJSON();
            expect(json).toMatchObject({
                name: 'RateLimitException',
                code: 'RATE_LIMIT_EXCEEDED',
                severity: 'medium',
                category: 'rate_limit',
                limitType: 'api_requests',
                scope: 'user:user-123',
                limit: 100,
                used: 105,
                remaining: 0,
                resetTime: mockResetTime.toISOString(),
                windowSize: 3600,
                usagePercentage: 105,
                isCompletelyExhausted: true,
                windowSizeFormatted: '1 hour(s)',
                scopeInfo: { type: 'user', id: 'user-123' },
                isApiRequestLimit: true,
                isOperationLimit: false,
                isResourceUsageLimit: false,
                isConcurrentOperationLimit: false,
                isBandwidthLimit: false,
                isQuotaLimit: false,
            });
        });
    });
    describe('inheritance', () => {
        it('should be instance of Error and DomainException', () => {
            const exception = new rate_limit_exception_1.RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 3600);
            expect(exception).toBeInstanceOf(Error);
            expect(exception.name).toBe('RateLimitException');
            expect(exception.code).toBe('RATE_LIMIT_EXCEEDED');
        });
        it('should maintain proper prototype chain', () => {
            const exception = new rate_limit_exception_1.RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 3600);
            expect(exception instanceof rate_limit_exception_1.RateLimitException).toBe(true);
            expect(exception instanceof Error).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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