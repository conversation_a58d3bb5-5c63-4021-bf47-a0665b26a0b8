{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\vulnerability-reporting.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAA8C;AAC9C,qFAA2E;AAC3E,2GAAgG;AAChG,+FAAoF;AACpF,yGAA8F;AAC9F,yFAA+E;AAC/E,sFAAkF;AAElF;;;GAGG;AAEI,IAAM,6BAA6B,qCAAnC,MAAM,6BAA6B;IAGxC,YAEE,uBAAmE,EAEnE,oBAA0E,EAE1E,cAA8D,EAE9D,mBAAwE,EAExE,eAAmD,EAClC,aAA4B;QAT5B,4BAAuB,GAAvB,uBAAuB,CAA2B;QAElD,yBAAoB,GAApB,oBAAoB,CAAqC;QAEzD,mBAAc,GAAd,cAAc,CAA+B;QAE7C,wBAAmB,GAAnB,mBAAmB,CAAoC;QAEvD,oBAAe,GAAf,eAAe,CAAmB;QAClC,kBAAa,GAAb,aAAa,CAAe;QAb9B,WAAM,GAAG,IAAI,eAAM,CAAC,+BAA6B,CAAC,IAAI,CAAC,CAAC;IActE,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,SAG9B;QACC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,OAAO,EAAE,SAAS,CAAC,OAAO;aAC3B,CAAC,CAAC;YAEH,4BAA4B;YAC5B,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YAE3E,mBAAmB;YACnB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAEzD,yBAAyB;YACzB,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YAErE,kBAAkB;YAClB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAEvD,gBAAgB;YAChB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAE1C,0BAA0B;YAC1B,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;YAEvE,MAAM,MAAM,GAAG;gBACb,UAAU,EAAE,mBAAmB;gBAC/B,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,SAAS;gBACT,OAAO,EAAE;oBACP,oBAAoB,EAAE,oBAAoB,CAAC,KAAK;oBAChD,uBAAuB,EAAE,oBAAoB,CAAC,QAAQ;oBACtD,mBAAmB,EAAE,oBAAoB,CAAC,IAAI;oBAC9C,kBAAkB,EAAE,oBAAoB,CAAC,GAAG;oBAC5C,sBAAsB,EAAE,oBAAoB,CAAC,OAAO;oBACpD,UAAU,EAAE,WAAW,CAAC,KAAK;oBAC7B,eAAe,EAAE,WAAW,CAAC,UAAU;oBACvC,gBAAgB,EAAE,iBAAiB,CAAC,KAAK;oBACzC,oBAAoB,EAAE,iBAAiB,CAAC,SAAS;iBAClD;gBACD,OAAO,EAAE;oBACP,eAAe,EAAE,oBAAoB;oBACrC,KAAK,EAAE,WAAW;oBAClB,WAAW,EAAE,iBAAiB;oBAC9B,WAAW,EAAE,kBAAkB;iBAChC;gBACD,MAAM,EAAE,UAAU;gBAClB,QAAQ;gBACR,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,EAAE,WAAW,EAAE,iBAAiB,CAAC;aACpG,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,EAAE;gBACjE,oBAAoB,EAAE,oBAAoB,CAAC,KAAK;gBAChD,aAAa,EAAE,oBAAoB,CAAC,QAAQ;gBAC5C,SAAS;aACV,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE;gBAC/D,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS;aACV,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mCAAmC,CAAC,QAOzC;QACC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE;gBAC5D,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,UAAU,EAAE,QAAQ,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC;aAC3C,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB;iBAC9C,kBAAkB,CAAC,MAAM,CAAC;iBAC1B,iBAAiB,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;YAEtD,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;gBAChC,YAAY,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;YACpE,CAAC;YAED,IAAI,QAAQ,CAAC,iBAAiB,EAAE,CAAC;gBAC/B,YAAY,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;YAClE,CAAC;YAED,gBAAgB;YAChB,YAAY,CAAC,QAAQ,CAAC,oDAAoD,EAAE;gBAC1E,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,SAAS;gBACvC,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC,OAAO;aACpC,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;gBAChC,YAAY,CAAC,QAAQ,CAAC,mCAAmC,EAAE;oBACzD,UAAU,EAAE,QAAQ,CAAC,UAAU;iBAChC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;gBAC9B,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE;oBACnD,QAAQ,EAAE,QAAQ,CAAC,QAAQ;iBAC5B,CAAC,CAAC;YACL,CAAC;YAED,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC;iBAC1C,UAAU,CAAC,gBAAgB,EAAE,MAAM,CAAC;iBACpC,UAAU,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;YAE5C,MAAM,eAAe,GAAG,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;YAErD,MAAM,MAAM,GAAG;gBACb,UAAU,EAAE,wBAAwB;gBACpC,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,QAAQ;gBACR,OAAO,EAAE;oBACP,oBAAoB,EAAE,eAAe,CAAC,MAAM;oBAC5C,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC;oBACjD,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC;oBAC/C,YAAY,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM;oBAC9D,SAAS,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM;oBAC1D,WAAW,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,MAAM;iBAClE;gBACD,eAAe,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC;aAClE,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sDAAsD,EAAE;gBACtE,kBAAkB,EAAE,eAAe,CAAC,MAAM;gBAC1C,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kDAAkD,EAAE;gBACpE,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,6BAA6B,CAAC,SAGnC;QACC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACtD,SAAS;aACV,CAAC,CAAC;YAEH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBAC3C,KAAK,EAAE;oBACL,SAAS,EAAE,IAAA,iBAAO,EAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,OAAO,CAAC;iBAC3D;gBACD,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;aAC7B,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,KAAK,CAAC,MAAM;gBACnB,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,MAAM;gBAClD,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM;gBAC5C,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM;gBAC9C,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;gBACpC,SAAS,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;gBAC1C,eAAe,EAAE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;gBACrD,yBAAyB,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,oBAAoB,EAAE,CAAC,CAAC;gBACpF,kBAAkB,EAAE,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC;aAC5D,CAAC;YAEF,MAAM,MAAM,GAAG;gBACb,UAAU,EAAE,kBAAkB;gBAC9B,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,SAAS;gBACT,OAAO,EAAE,WAAW;gBACpB,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC;gBAC7C,MAAM,EAAE,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;aAC5C,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gDAAgD,EAAE;gBAChE,SAAS,EAAE,KAAK,CAAC,MAAM;gBACvB,cAAc,EAAE,WAAW,CAAC,SAAS;gBACrC,SAAS;aACV,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE;gBAC9D,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS;aACV,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,QAI9B;QACC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,UAAU,EAAE,QAAQ,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC;aAC3C,CAAC,CAAC;YAEH,kDAAkD;YAClD,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB;iBAC9C,kBAAkB,CAAC,MAAM,CAAC;iBAC1B,iBAAiB,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;YAEtD,IAAI,QAAQ,CAAC,iBAAiB,EAAE,CAAC;gBAC/B,YAAY,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;YAClE,CAAC;YAED,IAAI,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;gBAC9B,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE;oBACnD,QAAQ,EAAE,QAAQ,CAAC,QAAQ;iBAC5B,CAAC,CAAC;YACL,CAAC;YAED,MAAM,eAAe,GAAG,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;YAErD,+BAA+B;YAC/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAErF,MAAM,MAAM,GAAG;gBACb,UAAU,EAAE,YAAY;gBACxB,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,QAAQ;gBACR,OAAO,EAAE,iBAAiB,CAAC,OAAO;gBAClC,UAAU,EAAE,iBAAiB,CAAC,UAAU;gBACxC,MAAM,EAAE,iBAAiB,CAAC,MAAM;gBAChC,eAAe,EAAE,eAAe;qBAC7B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;qBACnD,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC;gBACnC,eAAe,EAAE,IAAI,CAAC,iCAAiC,CAAC,iBAAiB,CAAC;aAC3E,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,EAAE;gBAC1D,kBAAkB,EAAE,eAAe,CAAC,MAAM;gBAC1C,cAAc,EAAE,QAAQ,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC;aACjD,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACxD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,QAAmB;QAC/C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,UAAU,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;aAClC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe;iBACtC,kBAAkB,CAAC,OAAO,CAAC;iBAC3B,iBAAiB,CAAC,uBAAuB,EAAE,iBAAiB,CAAC;iBAC7D,iBAAiB,CAAC,6BAA6B,EAAE,aAAa,CAAC;iBAC/D,iBAAiB,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC;YAEjE,IAAI,QAAQ,EAAE,MAAM,EAAE,CAAC;gBACrB,YAAY,CAAC,QAAQ,CAAC,4BAA4B,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YACpE,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;YAE5C,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;YAEvE,MAAM,MAAM,GAAG;gBACb,UAAU,EAAE,YAAY;gBACxB,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,OAAO,EAAE;oBACP,WAAW,EAAE,MAAM,CAAC,MAAM;oBAC1B,cAAc,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,MAAM;oBACrE,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC,MAAM;oBACzE,aAAa,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC,MAAM;oBACnE,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM;iBAC1F;gBACD,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,UAAU;qBACjB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;qBACzC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;aAChB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,EAAE;gBAC1D,UAAU,EAAE,MAAM,CAAC,MAAM;gBACzB,aAAa,EAAE,MAAM,CAAC,OAAO,CAAC,cAAc;aAC7C,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACxD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,yBAAyB;IAEjB,KAAK,CAAC,uBAAuB,CAAC,SAAc;QAClD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACzD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;QAC/F,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QACvF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;YACxD,KAAK,EAAE,EAAE,aAAa,EAAE,IAAA,iBAAO,EAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,OAAO,CAAC,EAAE;SAC1E,CAAC,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QAE9F,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;IAC3D,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,SAAc;QACzC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;YAC5C,KAAK,EAAE,EAAE,SAAS,EAAE,IAAA,iBAAO,EAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,OAAO,CAAC,EAAE;SACtE,CAAC,CAAC;QACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;YACjD,KAAK,EAAE;gBACL,SAAS,EAAE,IAAA,iBAAO,EAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,OAAO,CAAC;gBAC1D,MAAM,EAAE,WAAW;aACpB;SACF,CAAC,CAAC;QAEH,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,GAAG,UAAU,EAAE,CAAC;IAC3D,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,SAAc;QAC/C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;YAClD,KAAK,EAAE,EAAE,UAAU,EAAE,IAAA,iBAAO,EAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,OAAO,CAAC,EAAE;SACvE,CAAC,CAAC;QACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;YACtD,KAAK,EAAE;gBACL,UAAU,EAAE,IAAA,iBAAO,EAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,OAAO,CAAC;gBAC3D,MAAM,EAAE,WAAW;aACpB;SACF,CAAC,CAAC;QAEH,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,GAAG,SAAS,EAAE,CAAC;IAC1D,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,SAAc;QACxC,uDAAuD;QACvD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;YACvD,KAAK,EAAE;gBACL,EAAE,QAAQ,EAAE,UAAU,EAAE;gBACxB,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE;aACxC;YACD,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE;YAChC,IAAI,EAAE,EAAE;SACT,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;IAC3C,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,SAAc;QAChD,qDAAqD;QACrD,OAAO;YACL,eAAe,EAAE,CAAC;YAClB,sBAAsB,EAAE,CAAC;YACzB,eAAe,EAAE,CAAC;SACnB,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAAC,WAAgB,EAAE,WAAgB,EAAE,iBAAsB;QACxF,MAAM,eAAe,GAAG,EAAE,CAAC;QAE3B,IAAI,WAAW,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YAC7B,eAAe,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;YACtD,eAAe,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,iBAAiB,CAAC,OAAO,GAAG,iBAAiB,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;YAClE,eAAe,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACzE,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,eAAe,CAAC,eAAgC;QACtD,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAC1C,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACnD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEO,cAAc,CAAC,eAAgC;QACrD,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC7B,IAAI,CAAC,uBAAuB,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC/C,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,gBAAgB,CAAC,KAA0B;QACjD,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAChC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACnD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEO,mBAAmB,CAAC,KAA0B;QACpD,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAChC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACzD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEO,wBAAwB,CAAC,KAA0B;QACzD,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,KAAK,IAAI,CAAC,CAAC;QACrE,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE1C,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACpF,OAAO,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC;IAC/C,CAAC;IAEO,2BAA2B,CAAC,KAA0B;QAC5D,8DAA8D;QAC9D,OAAO;YACL,6BAA6B,EAAE,CAAC;YAChC,cAAc,EAAE,CAAC;YACjB,mBAAmB,EAAE,CAAC;SACvB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,SAAc;QACxC,uDAAuD;QACvD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,0BAA0B,CAAC,eAAgC,EAAE,QAAa;QAChF,6DAA6D;QAC7D,OAAO;YACL,OAAO,EAAE,EAAE;YACX,UAAU,EAAE,EAAE;YACd,MAAM,EAAE,EAAE;SACX,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,aAA4B,EAAE,QAAa;QACtE,6EAA6E;QAC7E,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,iCAAiC,CAAC,OAAY;QACpD,oEAAoE;QACpE,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,kBAAkB,CAAC,KAAY;QACrC,MAAM,eAAe,GAAG,KAAK,CAAC,eAAe,IAAI,EAAE,CAAC;QACpD,MAAM,aAAa,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;QACpF,MAAM,SAAS,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QAE5E,MAAM,SAAS,GAAG,CAAC,aAAa,GAAG,EAAE,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAExF,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,SAAS,IAAI,EAAE;YAAE,SAAS,GAAG,MAAM,CAAC;aACnC,IAAI,SAAS,IAAI,EAAE;YAAE,SAAS,GAAG,QAAQ,CAAC;QAE/C,OAAO;YACL,OAAO,EAAE,KAAK,CAAC,EAAE;YACjB,SAAS,EAAE,KAAK,CAAC,IAAI;YACrB,SAAS,EAAE,KAAK,CAAC,IAAI;YACrB,kBAAkB,EAAE,eAAe,CAAC,MAAM;YAC1C,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;SACV,CAAC;IACJ,CAAC;CACF,CAAA;AA/gBY,sEAA6B;wCAA7B,6BAA6B;IADzC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,oCAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,yDAAuB,CAAC,CAAA;IAEzC,WAAA,IAAA,0BAAgB,EAAC,6CAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,uDAAsB,CAAC,CAAA;IAExC,WAAA,IAAA,0BAAgB,EAAC,oBAAK,CAAC,CAAA;yDAPkB,oBAAU,oBAAV,oBAAU,oDAEb,oBAAU,oBAAV,oBAAU,oDAEhB,oBAAU,oBAAV,oBAAU,oDAEL,oBAAU,oBAAV,oBAAU,oDAEd,oBAAU,oBAAV,oBAAU,oDACZ,8BAAa,oBAAb,8BAAa;GAdpC,6BAA6B,CA+gBzC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\vulnerability-reporting.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, Between } from 'typeorm';\r\nimport { Vulnerability } from '../../domain/entities/vulnerability.entity';\r\nimport { VulnerabilityAssessment } from '../../domain/entities/vulnerability-assessment.entity';\r\nimport { VulnerabilityScan } from '../../domain/entities/vulnerability-scan.entity';\r\nimport { VulnerabilityException } from '../../domain/entities/vulnerability-exception.entity';\r\nimport { Asset } from '../../../asset-management/domain/entities/asset.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\n\r\n/**\r\n * Vulnerability Reporting service\r\n * Handles vulnerability reporting and analytics\r\n */\r\n@Injectable()\r\nexport class VulnerabilityReportingService {\r\n  private readonly logger = new Logger(VulnerabilityReportingService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(Vulnerability)\r\n    private readonly vulnerabilityRepository: Repository<Vulnerability>,\r\n    @InjectRepository(VulnerabilityAssessment)\r\n    private readonly assessmentRepository: Repository<VulnerabilityAssessment>,\r\n    @InjectRepository(VulnerabilityScan)\r\n    private readonly scanRepository: Repository<VulnerabilityScan>,\r\n    @InjectRepository(VulnerabilityException)\r\n    private readonly exceptionRepository: Repository<VulnerabilityException>,\r\n    @InjectRepository(Asset)\r\n    private readonly assetRepository: Repository<Asset>,\r\n    private readonly loggerService: LoggerService,\r\n  ) {}\r\n\r\n  /**\r\n   * Generate executive summary report\r\n   */\r\n  async generateExecutiveSummary(dateRange: {\r\n    startDate: Date;\r\n    endDate: Date;\r\n  }): Promise<any> {\r\n    try {\r\n      this.logger.debug('Generating executive summary report', {\r\n        startDate: dateRange.startDate,\r\n        endDate: dateRange.endDate,\r\n      });\r\n\r\n      // Get vulnerability metrics\r\n      const vulnerabilityMetrics = await this.getVulnerabilityMetrics(dateRange);\r\n      \r\n      // Get scan metrics\r\n      const scanMetrics = await this.getScanMetrics(dateRange);\r\n      \r\n      // Get assessment metrics\r\n      const assessmentMetrics = await this.getAssessmentMetrics(dateRange);\r\n      \r\n      // Get risk trends\r\n      const riskTrends = await this.getRiskTrends(dateRange);\r\n      \r\n      // Get top risks\r\n      const topRisks = await this.getTopRisks();\r\n      \r\n      // Get remediation metrics\r\n      const remediationMetrics = await this.getRemediationMetrics(dateRange);\r\n\r\n      const report = {\r\n        reportType: 'executive_summary',\r\n        generatedAt: new Date(),\r\n        dateRange,\r\n        summary: {\r\n          totalVulnerabilities: vulnerabilityMetrics.total,\r\n          criticalVulnerabilities: vulnerabilityMetrics.critical,\r\n          highVulnerabilities: vulnerabilityMetrics.high,\r\n          newVulnerabilities: vulnerabilityMetrics.new,\r\n          patchedVulnerabilities: vulnerabilityMetrics.patched,\r\n          totalScans: scanMetrics.total,\r\n          successfulScans: scanMetrics.successful,\r\n          totalAssessments: assessmentMetrics.total,\r\n          completedAssessments: assessmentMetrics.completed,\r\n        },\r\n        metrics: {\r\n          vulnerabilities: vulnerabilityMetrics,\r\n          scans: scanMetrics,\r\n          assessments: assessmentMetrics,\r\n          remediation: remediationMetrics,\r\n        },\r\n        trends: riskTrends,\r\n        topRisks,\r\n        recommendations: this.generateRecommendations(vulnerabilityMetrics, scanMetrics, assessmentMetrics),\r\n      };\r\n\r\n      this.logger.log('Executive summary report generated successfully', {\r\n        totalVulnerabilities: vulnerabilityMetrics.total,\r\n        criticalCount: vulnerabilityMetrics.critical,\r\n        dateRange,\r\n      });\r\n\r\n      return report;\r\n    } catch (error) {\r\n      this.logger.error('Failed to generate executive summary report', {\r\n        error: error.message,\r\n        dateRange,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate detailed vulnerability report\r\n   */\r\n  async generateDetailedVulnerabilityReport(criteria: {\r\n    dateRange: { startDate: Date; endDate: Date };\r\n    severities?: string[];\r\n    assetIds?: string[];\r\n    includeAssessments?: boolean;\r\n    includeExceptions?: boolean;\r\n    format?: 'json' | 'csv' | 'pdf';\r\n  }): Promise<any> {\r\n    try {\r\n      this.logger.debug('Generating detailed vulnerability report', {\r\n        dateRange: criteria.dateRange,\r\n        severities: criteria.severities,\r\n        assetCount: criteria.assetIds?.length || 0,\r\n      });\r\n\r\n      const queryBuilder = this.vulnerabilityRepository\r\n        .createQueryBuilder('vuln')\r\n        .leftJoinAndSelect('vuln.affectedAssets', 'assets');\r\n\r\n      if (criteria.includeAssessments) {\r\n        queryBuilder.leftJoinAndSelect('vuln.assessments', 'assessments');\r\n      }\r\n\r\n      if (criteria.includeExceptions) {\r\n        queryBuilder.leftJoinAndSelect('vuln.exceptions', 'exceptions');\r\n      }\r\n\r\n      // Apply filters\r\n      queryBuilder.andWhere('vuln.publishedDate BETWEEN :startDate AND :endDate', {\r\n        startDate: criteria.dateRange.startDate,\r\n        endDate: criteria.dateRange.endDate,\r\n      });\r\n\r\n      if (criteria.severities?.length) {\r\n        queryBuilder.andWhere('vuln.severity IN (:...severities)', { \r\n          severities: criteria.severities \r\n        });\r\n      }\r\n\r\n      if (criteria.assetIds?.length) {\r\n        queryBuilder.andWhere('assets.id IN (:...assetIds)', { \r\n          assetIds: criteria.assetIds \r\n        });\r\n      }\r\n\r\n      queryBuilder.orderBy('vuln.severity', 'DESC')\r\n        .addOrderBy('vuln.cvssScore', 'DESC')\r\n        .addOrderBy('vuln.publishedDate', 'DESC');\r\n\r\n      const vulnerabilities = await queryBuilder.getMany();\r\n\r\n      const report = {\r\n        reportType: 'detailed_vulnerability',\r\n        generatedAt: new Date(),\r\n        criteria,\r\n        summary: {\r\n          totalVulnerabilities: vulnerabilities.length,\r\n          bySeverity: this.groupBySeverity(vulnerabilities),\r\n          byProduct: this.groupByProduct(vulnerabilities),\r\n          withExploits: vulnerabilities.filter(v => v.hasExploit).length,\r\n          inTheWild: vulnerabilities.filter(v => v.inTheWild).length,\r\n          withPatches: vulnerabilities.filter(v => v.patchAvailable).length,\r\n        },\r\n        vulnerabilities: vulnerabilities.map(v => v.exportForReporting()),\r\n      };\r\n\r\n      this.logger.log('Detailed vulnerability report generated successfully', {\r\n        vulnerabilityCount: vulnerabilities.length,\r\n        dateRange: criteria.dateRange,\r\n      });\r\n\r\n      return report;\r\n    } catch (error) {\r\n      this.logger.error('Failed to generate detailed vulnerability report', {\r\n        error: error.message,\r\n        criteria,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate scan performance report\r\n   */\r\n  async generateScanPerformanceReport(dateRange: {\r\n    startDate: Date;\r\n    endDate: Date;\r\n  }): Promise<any> {\r\n    try {\r\n      this.logger.debug('Generating scan performance report', {\r\n        dateRange,\r\n      });\r\n\r\n      const scans = await this.scanRepository.find({\r\n        where: {\r\n          startedAt: Between(dateRange.startDate, dateRange.endDate),\r\n        },\r\n        order: { startedAt: 'DESC' },\r\n      });\r\n\r\n      const scanMetrics = {\r\n        total: scans.length,\r\n        completed: scans.filter(s => s.isCompleted).length,\r\n        failed: scans.filter(s => s.isFailed).length,\r\n        running: scans.filter(s => s.isRunning).length,\r\n        byType: this.groupScansByType(scans),\r\n        byScanner: this.groupScansByScanner(scans),\r\n        averageDuration: this.calculateAverageDuration(scans),\r\n        totalVulnerabilitiesFound: scans.reduce((sum, s) => sum + s.totalVulnerabilities, 0),\r\n        performanceMetrics: this.calculatePerformanceMetrics(scans),\r\n      };\r\n\r\n      const report = {\r\n        reportType: 'scan_performance',\r\n        generatedAt: new Date(),\r\n        dateRange,\r\n        summary: scanMetrics,\r\n        scans: scans.map(s => s.exportForReporting()),\r\n        trends: await this.getScanTrends(dateRange),\r\n      };\r\n\r\n      this.logger.log('Scan performance report generated successfully', {\r\n        scanCount: scans.length,\r\n        completedScans: scanMetrics.completed,\r\n        dateRange,\r\n      });\r\n\r\n      return report;\r\n    } catch (error) {\r\n      this.logger.error('Failed to generate scan performance report', {\r\n        error: error.message,\r\n        dateRange,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate compliance report\r\n   */\r\n  async generateComplianceReport(criteria: {\r\n    frameworks?: string[];\r\n    assetIds?: string[];\r\n    includeExceptions?: boolean;\r\n  }): Promise<any> {\r\n    try {\r\n      this.logger.debug('Generating compliance report', {\r\n        frameworks: criteria.frameworks,\r\n        assetCount: criteria.assetIds?.length || 0,\r\n      });\r\n\r\n      // Get all vulnerabilities with compliance context\r\n      const queryBuilder = this.vulnerabilityRepository\r\n        .createQueryBuilder('vuln')\r\n        .leftJoinAndSelect('vuln.affectedAssets', 'assets');\r\n\r\n      if (criteria.includeExceptions) {\r\n        queryBuilder.leftJoinAndSelect('vuln.exceptions', 'exceptions');\r\n      }\r\n\r\n      if (criteria.assetIds?.length) {\r\n        queryBuilder.andWhere('assets.id IN (:...assetIds)', { \r\n          assetIds: criteria.assetIds \r\n        });\r\n      }\r\n\r\n      const vulnerabilities = await queryBuilder.getMany();\r\n\r\n      // Calculate compliance metrics\r\n      const complianceMetrics = this.calculateComplianceMetrics(vulnerabilities, criteria);\r\n\r\n      const report = {\r\n        reportType: 'compliance',\r\n        generatedAt: new Date(),\r\n        criteria,\r\n        summary: complianceMetrics.summary,\r\n        frameworks: complianceMetrics.frameworks,\r\n        assets: complianceMetrics.assets,\r\n        vulnerabilities: vulnerabilities\r\n          .filter(v => this.isComplianceRelevant(v, criteria))\r\n          .map(v => v.exportForReporting()),\r\n        recommendations: this.generateComplianceRecommendations(complianceMetrics),\r\n      };\r\n\r\n      this.logger.log('Compliance report generated successfully', {\r\n        vulnerabilityCount: vulnerabilities.length,\r\n        frameworkCount: criteria.frameworks?.length || 0,\r\n      });\r\n\r\n      return report;\r\n    } catch (error) {\r\n      this.logger.error('Failed to generate compliance report', {\r\n        error: error.message,\r\n        criteria,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate asset risk report\r\n   */\r\n  async generateAssetRiskReport(assetIds?: string[]): Promise<any> {\r\n    try {\r\n      this.logger.debug('Generating asset risk report', {\r\n        assetCount: assetIds?.length || 0,\r\n      });\r\n\r\n      const queryBuilder = this.assetRepository\r\n        .createQueryBuilder('asset')\r\n        .leftJoinAndSelect('asset.vulnerabilities', 'vulnerabilities')\r\n        .leftJoinAndSelect('vulnerabilities.assessments', 'assessments')\r\n        .leftJoinAndSelect('vulnerabilities.exceptions', 'exceptions');\r\n\r\n      if (assetIds?.length) {\r\n        queryBuilder.andWhere('asset.id IN (:...assetIds)', { assetIds });\r\n      }\r\n\r\n      const assets = await queryBuilder.getMany();\r\n\r\n      const assetRisks = assets.map(asset => this.calculateAssetRisk(asset));\r\n\r\n      const report = {\r\n        reportType: 'asset_risk',\r\n        generatedAt: new Date(),\r\n        summary: {\r\n          totalAssets: assets.length,\r\n          highRiskAssets: assetRisks.filter(a => a.riskLevel === 'high').length,\r\n          mediumRiskAssets: assetRisks.filter(a => a.riskLevel === 'medium').length,\r\n          lowRiskAssets: assetRisks.filter(a => a.riskLevel === 'low').length,\r\n          averageRiskScore: assetRisks.reduce((sum, a) => sum + a.riskScore, 0) / assetRisks.length,\r\n        },\r\n        assets: assetRisks,\r\n        topRisks: assetRisks\r\n          .sort((a, b) => b.riskScore - a.riskScore)\r\n          .slice(0, 10),\r\n      };\r\n\r\n      this.logger.log('Asset risk report generated successfully', {\r\n        assetCount: assets.length,\r\n        highRiskCount: report.summary.highRiskAssets,\r\n      });\r\n\r\n      return report;\r\n    } catch (error) {\r\n      this.logger.error('Failed to generate asset risk report', {\r\n        error: error.message,\r\n        assetIds,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private async getVulnerabilityMetrics(dateRange: any): Promise<any> {\r\n    const total = await this.vulnerabilityRepository.count();\r\n    const critical = await this.vulnerabilityRepository.count({ where: { severity: 'critical' } });\r\n    const high = await this.vulnerabilityRepository.count({ where: { severity: 'high' } });\r\n    const newVulns = await this.vulnerabilityRepository.count({\r\n      where: { publishedDate: Between(dateRange.startDate, dateRange.endDate) },\r\n    });\r\n    const patched = await this.vulnerabilityRepository.count({ where: { patchAvailable: true } });\r\n\r\n    return { total, critical, high, new: newVulns, patched };\r\n  }\r\n\r\n  private async getScanMetrics(dateRange: any): Promise<any> {\r\n    const total = await this.scanRepository.count({\r\n      where: { startedAt: Between(dateRange.startDate, dateRange.endDate) },\r\n    });\r\n    const successful = await this.scanRepository.count({\r\n      where: { \r\n        startedAt: Between(dateRange.startDate, dateRange.endDate),\r\n        status: 'completed',\r\n      },\r\n    });\r\n\r\n    return { total, successful, failed: total - successful };\r\n  }\r\n\r\n  private async getAssessmentMetrics(dateRange: any): Promise<any> {\r\n    const total = await this.assessmentRepository.count({\r\n      where: { assessedAt: Between(dateRange.startDate, dateRange.endDate) },\r\n    });\r\n    const completed = await this.assessmentRepository.count({\r\n      where: { \r\n        assessedAt: Between(dateRange.startDate, dateRange.endDate),\r\n        status: 'completed',\r\n      },\r\n    });\r\n\r\n    return { total, completed, pending: total - completed };\r\n  }\r\n\r\n  private async getRiskTrends(dateRange: any): Promise<any[]> {\r\n    // Implementation would calculate risk trends over time\r\n    return [];\r\n  }\r\n\r\n  private async getTopRisks(): Promise<any[]> {\r\n    const topVulns = await this.vulnerabilityRepository.find({\r\n      where: [\r\n        { severity: 'critical' },\r\n        { severity: 'high', exploitable: true },\r\n      ],\r\n      order: { publishedDate: 'DESC' },\r\n      take: 10,\r\n    });\r\n\r\n    return topVulns.map(v => v.getSummary());\r\n  }\r\n\r\n  private async getRemediationMetrics(dateRange: any): Promise<any> {\r\n    // Implementation would calculate remediation metrics\r\n    return {\r\n      totalRemediated: 0,\r\n      averageRemediationTime: 0,\r\n      remediationRate: 0,\r\n    };\r\n  }\r\n\r\n  private generateRecommendations(vulnMetrics: any, scanMetrics: any, assessmentMetrics: any): string[] {\r\n    const recommendations = [];\r\n\r\n    if (vulnMetrics.critical > 0) {\r\n      recommendations.push('Prioritize remediation of critical vulnerabilities');\r\n    }\r\n\r\n    if (scanMetrics.failed > scanMetrics.successful * 0.1) {\r\n      recommendations.push('Investigate scan failures and improve scan reliability');\r\n    }\r\n\r\n    if (assessmentMetrics.pending > assessmentMetrics.completed * 0.2) {\r\n      recommendations.push('Increase assessment capacity to reduce backlog');\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  private groupBySeverity(vulnerabilities: Vulnerability[]): any {\r\n    return vulnerabilities.reduce((acc, vuln) => {\r\n      acc[vuln.severity] = (acc[vuln.severity] || 0) + 1;\r\n      return acc;\r\n    }, {});\r\n  }\r\n\r\n  private groupByProduct(vulnerabilities: Vulnerability[]): any {\r\n    const products = {};\r\n    vulnerabilities.forEach(vuln => {\r\n      vuln.getAffectedProductNames().forEach(product => {\r\n        products[product] = (products[product] || 0) + 1;\r\n      });\r\n    });\r\n    return products;\r\n  }\r\n\r\n  private groupScansByType(scans: VulnerabilityScan[]): any {\r\n    return scans.reduce((acc, scan) => {\r\n      acc[scan.scanType] = (acc[scan.scanType] || 0) + 1;\r\n      return acc;\r\n    }, {});\r\n  }\r\n\r\n  private groupScansByScanner(scans: VulnerabilityScan[]): any {\r\n    return scans.reduce((acc, scan) => {\r\n      acc[scan.scannerType] = (acc[scan.scannerType] || 0) + 1;\r\n      return acc;\r\n    }, {});\r\n  }\r\n\r\n  private calculateAverageDuration(scans: VulnerabilityScan[]): number {\r\n    const completedScans = scans.filter(s => s.durationSeconds !== null);\r\n    if (completedScans.length === 0) return 0;\r\n    \r\n    const totalDuration = completedScans.reduce((sum, s) => sum + s.durationSeconds, 0);\r\n    return totalDuration / completedScans.length;\r\n  }\r\n\r\n  private calculatePerformanceMetrics(scans: VulnerabilityScan[]): any {\r\n    // Implementation would calculate detailed performance metrics\r\n    return {\r\n      averageVulnerabilitiesPerScan: 0,\r\n      scanEfficiency: 0,\r\n      resourceUtilization: 0,\r\n    };\r\n  }\r\n\r\n  private async getScanTrends(dateRange: any): Promise<any[]> {\r\n    // Implementation would calculate scan trends over time\r\n    return [];\r\n  }\r\n\r\n  private calculateComplianceMetrics(vulnerabilities: Vulnerability[], criteria: any): any {\r\n    // Implementation would calculate compliance-specific metrics\r\n    return {\r\n      summary: {},\r\n      frameworks: {},\r\n      assets: {},\r\n    };\r\n  }\r\n\r\n  private isComplianceRelevant(vulnerability: Vulnerability, criteria: any): boolean {\r\n    // Implementation would determine if vulnerability is relevant for compliance\r\n    return true;\r\n  }\r\n\r\n  private generateComplianceRecommendations(metrics: any): string[] {\r\n    // Implementation would generate compliance-specific recommendations\r\n    return [];\r\n  }\r\n\r\n  private calculateAssetRisk(asset: Asset): any {\r\n    const vulnerabilities = asset.vulnerabilities || [];\r\n    const criticalCount = vulnerabilities.filter(v => v.severity === 'critical').length;\r\n    const highCount = vulnerabilities.filter(v => v.severity === 'high').length;\r\n    \r\n    const riskScore = (criticalCount * 10) + (highCount * 7) + (vulnerabilities.length * 1);\r\n    \r\n    let riskLevel = 'low';\r\n    if (riskScore >= 50) riskLevel = 'high';\r\n    else if (riskScore >= 20) riskLevel = 'medium';\r\n\r\n    return {\r\n      assetId: asset.id,\r\n      assetName: asset.name,\r\n      assetType: asset.type,\r\n      vulnerabilityCount: vulnerabilities.length,\r\n      criticalCount,\r\n      highCount,\r\n      riskScore,\r\n      riskLevel,\r\n    };\r\n  }\r\n}\r\n"], "version": 3}