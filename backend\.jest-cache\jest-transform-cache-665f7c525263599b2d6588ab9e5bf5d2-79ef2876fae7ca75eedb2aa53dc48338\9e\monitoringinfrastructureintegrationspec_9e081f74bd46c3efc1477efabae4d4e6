4e1c6663b4b718ae49ee5bda841c1608
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const event_emitter_1 = require("@nestjs/event-emitter");
const base_test_class_1 = require("../base/base-test.class");
const monitoring_module_1 = require("../../monitoring/monitoring.module");
const metrics_collection_service_1 = require("../../monitoring/services/metrics-collection.service");
const health_check_service_1 = require("../../monitoring/services/health-check.service");
const alerting_service_1 = require("../../monitoring/services/alerting.service");
const notification_service_1 = require("../../monitoring/services/notification.service");
const metric_entity_1 = require("../../monitoring/entities/metric.entity");
const health_check_entity_1 = require("../../monitoring/entities/health-check.entity");
const alert_entity_1 = require("../../monitoring/entities/alert.entity");
/**
 * Monitoring Infrastructure Integration Tests
 *
 * Comprehensive monitoring infrastructure testing providing:
 * - Metrics collection validation with Prometheus compatibility
 * - Health check verification with automated execution and alerting
 * - Alerting system testing with multi-channel notification validation
 * - Performance monitoring with resource utilization tracking
 * - Circuit breaker functionality testing with failure simulation
 * - Real-time dashboard API validation with streaming data
 * - Observability infrastructure testing with distributed tracing
 * - Integration with external monitoring systems validation
 */
describe('Monitoring Infrastructure Integration Tests', () => {
    let app;
    let testingModule;
    let metricsService;
    let healthCheckService;
    let alertingService;
    let notificationService;
    let eventEmitter;
    let baseTest;
    // Test entities
    const testEntities = [metric_entity_1.Metric, health_check_entity_1.HealthCheck, alert_entity_1.Alert];
    beforeAll(async () => {
        // Create base test instance
        baseTest = new (class extends base_test_class_1.BaseTestClass {
        })();
        // Setup test environment
        await baseTest.setupTestEnvironment({
            imports: [monitoring_module_1.MonitoringModule],
        }, testEntities);
        app = baseTest.app;
        testingModule = baseTest.testingModule;
        // Get service instances
        metricsService = baseTest.getService(metrics_collection_service_1.MetricsCollectionService);
        healthCheckService = baseTest.getService(health_check_service_1.HealthCheckService);
        alertingService = baseTest.getService(alerting_service_1.AlertingService);
        notificationService = baseTest.getService(notification_service_1.NotificationService);
        eventEmitter = baseTest.getService(event_emitter_1.EventEmitter2);
    });
    afterAll(async () => {
        await baseTest.cleanupTestEnvironment();
    });
    beforeEach(async () => {
        await baseTest.setupTestData();
        jest.clearAllMocks();
    });
    afterEach(async () => {
        await baseTest.cleanupTestData();
    });
    describe('Metrics Collection', () => {
        it('should record and retrieve metrics successfully', async () => {
            // Arrange
            const metricData = {
                name: 'test_counter',
                value: 1,
                category: 'business',
                type: 'counter',
                labels: {
                    service: 'test-service',
                    environment: 'test',
                },
                source: 'integration-test',
            };
            // Act
            const recordedMetric = await metricsService.recordMetric(metricData);
            // Assert
            expect(recordedMetric).toBeDefined();
            expect(recordedMetric.id).toBeDefined();
            expect(recordedMetric.name).toBe(metricData.name);
            expect(recordedMetric.value).toBe(metricData.value);
            // Verify database persistence
            await baseTest.assertDatabaseState(metric_entity_1.Metric, { name: metricData.name }, 1);
            // Verify metric can be retrieved
            const retrievedMetrics = await metricsService.getMetrics({
                name: metricData.name,
                limit: 10,
                offset: 0,
            });
            expect(retrievedMetrics.data).toHaveLength(1);
            expect(retrievedMetrics.data[0]).toMatchObject({
                name: metricData.name,
                value: metricData.value,
                category: metricData.category,
                type: metricData.type,
            });
        });
        it('should aggregate metrics correctly', async () => {
            // Arrange
            const baseMetric = {
                name: 'request_count',
                category: 'technical',
                type: 'counter',
                labels: { service: 'api' },
                source: 'test',
            };
            // Record multiple metrics
            await Promise.all([
                metricsService.recordMetric({ ...baseMetric, value: 10 }),
                metricsService.recordMetric({ ...baseMetric, value: 15 }),
                metricsService.recordMetric({ ...baseMetric, value: 20 }),
            ]);
            // Act
            const aggregatedMetrics = await metricsService.getAggregatedMetrics({
                name: 'request_count',
                aggregation: 'sum',
                groupBy: ['service'],
                timeRange: {
                    start: new Date(Date.now() - 3600000), // 1 hour ago
                    end: new Date(),
                },
            });
            // Assert
            expect(aggregatedMetrics).toBeDefined();
            expect(aggregatedMetrics.data).toHaveLength(1);
            expect(aggregatedMetrics.data[0]).toMatchObject({
                labels: { service: 'api' },
                value: 45, // 10 + 15 + 20
                aggregation: 'sum',
            });
        });
        it('should export metrics in Prometheus format', async () => {
            // Arrange
            await Promise.all([
                metricsService.recordMetric({
                    name: 'http_requests_total',
                    value: 100,
                    category: 'technical',
                    type: 'counter',
                    labels: { method: 'GET', status: '200' },
                    source: 'test',
                }),
                metricsService.recordMetric({
                    name: 'response_time_seconds',
                    value: 0.25,
                    category: 'performance',
                    type: 'histogram',
                    labels: { endpoint: '/api/test' },
                    source: 'test',
                }),
            ]);
            // Act
            const prometheusExport = await metricsService.exportPrometheusMetrics();
            // Assert
            expect(prometheusExport).toContain('# TYPE http_requests_total counter');
            expect(prometheusExport).toContain('http_requests_total{method="GET",status="200"} 100');
            expect(prometheusExport).toContain('# TYPE response_time_seconds histogram');
            expect(prometheusExport).toContain('response_time_seconds{endpoint="/api/test"} 0.25');
        });
        it('should handle high-frequency metric recording', async () => {
            // Arrange
            const metricCount = 1000;
            const baseMetric = {
                name: 'high_frequency_metric',
                category: 'performance',
                type: 'gauge',
                source: 'performance-test',
            };
            // Act
            const { result: metrics, executionTime } = await baseTest.measureExecutionTime(async () => {
                const promises = Array.from({ length: metricCount }, (_, i) => metricsService.recordMetric({
                    ...baseMetric,
                    value: Math.random() * 100,
                    labels: { instance: `instance-${i % 10}` },
                }));
                return await Promise.all(promises);
            });
            // Assert
            expect(metrics).toHaveLength(metricCount);
            expect(executionTime).toBeLessThan(10000); // Should complete within 10 seconds
            // Verify database persistence
            await baseTest.assertDatabaseState(metric_entity_1.Metric, { name: baseMetric.name }, metricCount);
            console.log(`High-Frequency Metrics Test (${metricCount} metrics):`, {
                totalTime: `${executionTime}ms`,
                averageTimePerMetric: `${executionTime / metricCount}ms`,
                metricsPerSecond: Math.round((metricCount / executionTime) * 1000),
            });
        });
    });
    describe('Health Checks', () => {
        it('should register and execute health checks', async () => {
            // Arrange
            const healthCheckConfig = {
                name: 'test-service-health',
                category: 'application',
                description: 'Test service health check',
                endpoint: 'http://localhost:3000/health',
                method: 'GET',
                configuration: {
                    timeout: 5000,
                    retries: 3,
                    expectedStatus: [200],
                },
                schedule: {
                    interval: 60,
                    enabled: true,
                },
                thresholds: {
                    responseTime: { warning: 1000, critical: 3000, emergency: 10000 },
                    availability: { warning: 95, critical: 90, emergency: 80 },
                },
            };
            // Act
            const healthCheck = await healthCheckService.createHealthCheck(healthCheckConfig);
            // Assert
            expect(healthCheck).toBeDefined();
            expect(healthCheck.id).toBeDefined();
            expect(healthCheck.name).toBe(healthCheckConfig.name);
            expect(healthCheck.isEnabled).toBe(true);
            // Verify database persistence
            await baseTest.assertDatabaseState(health_check_entity_1.HealthCheck, { name: healthCheckConfig.name }, 1);
            // Execute health check
            const result = await healthCheckService.executeHealthCheck(healthCheck.id);
            expect(result).toBeDefined();
            expect(result.status).toMatch(/^(healthy|degraded|unhealthy|unknown)$/);
        });
        it('should handle health check failures and alerting', async () => {
            // Arrange
            const healthCheck = await baseTest.createTestHealthCheck({
                name: 'failing-service',
                endpoint: 'http://localhost:9999/nonexistent', // This will fail
                thresholds: {
                    responseTime: { warning: 100, critical: 500, emergency: 1000 },
                    availability: { warning: 95, critical: 90, emergency: 80 },
                },
                isCritical: true,
            });
            // Spy on alerting service
            const createAlertSpy = jest.spyOn(alertingService, 'createAlert');
            // Act
            const result = await healthCheckService.executeHealthCheck(healthCheck.id);
            // Assert
            expect(result.status).toBe('unhealthy');
            expect(result.responseTime).toBeGreaterThan(0);
            // Verify alert was created for critical health check failure
            expect(createAlertSpy).toHaveBeenCalledWith(expect.objectContaining({
                name: expect.stringContaining('Health Check Failed'),
                severity: 'critical',
                category: 'system',
                source: 'health_check',
                sourceId: healthCheck.id,
            }));
        });
        it('should calculate health scores correctly', async () => {
            // Arrange
            const healthChecks = await Promise.all([
                baseTest.createTestHealthCheck({
                    name: 'service-a',
                    lastResult: { status: 'healthy', responseTime: 100 },
                    metrics: { availability: 99.5, averageResponseTime: 150 },
                    isCritical: true,
                }),
                baseTest.createTestHealthCheck({
                    name: 'service-b',
                    lastResult: { status: 'degraded', responseTime: 2000 },
                    metrics: { availability: 95.0, averageResponseTime: 1800 },
                    isCritical: false,
                }),
                baseTest.createTestHealthCheck({
                    name: 'service-c',
                    lastResult: { status: 'unhealthy', responseTime: 5000 },
                    metrics: { availability: 85.0, averageResponseTime: 4500 },
                    isCritical: true,
                }),
            ]);
            // Act
            const overallHealth = await healthCheckService.getOverallHealthStatus();
            // Assert
            expect(overallHealth).toBeDefined();
            expect(overallHealth.status).toMatch(/^(healthy|degraded|unhealthy)$/);
            expect(overallHealth.score).toBeGreaterThanOrEqual(0);
            expect(overallHealth.score).toBeLessThanOrEqual(100);
            expect(overallHealth.checks).toHaveLength(3);
            expect(overallHealth.criticalIssues).toBeGreaterThan(0); // service-c is critical and unhealthy
        });
    });
    describe('Alerting System', () => {
        it('should create and manage alerts', async () => {
            // Arrange
            const alertData = {
                name: 'High CPU Usage',
                description: 'CPU usage exceeded threshold',
                severity: 'warning',
                category: 'system',
                source: 'metrics',
                sourceId: 'cpu-metric-123',
                conditions: {
                    metric: 'cpu_usage_percent',
                    operator: '>',
                    threshold: 80,
                    duration: 300,
                },
                context: {
                    labels: { service: 'api-server', instance: 'api-1' },
                    annotations: {
                        runbook: 'https://wiki.example.com/runbooks/high-cpu',
                        dashboard: 'https://grafana.example.com/cpu-dashboard',
                    },
                },
            };
            // Act
            const alert = await alertingService.createAlert(alertData);
            // Assert
            expect(alert).toBeDefined();
            expect(alert.id).toBeDefined();
            expect(alert.name).toBe(alertData.name);
            expect(alert.severity).toBe(alertData.severity);
            expect(alert.status).toBe('active');
            // Verify database persistence
            await baseTest.assertDatabaseState(alert_entity_1.Alert, { name: alertData.name }, 1);
            // Verify alert creation event was emitted
            await baseTest.assertEventEmitted('alert.created', {
                alertId: alert.id,
                severity: alertData.severity,
            });
        });
        it('should handle alert acknowledgment and resolution', async () => {
            // Arrange
            const alert = await baseTest.createTestAlert({
                name: 'Test Alert',
                severity: 'critical',
                status: 'active',
            });
            // Act - Acknowledge alert
            const acknowledgedAlert = await alertingService.acknowledgeAlert(alert.id, baseTest.testUser.id, 'Investigating the issue', new Date(Date.now() + 3600000) // Expected resolution in 1 hour
            );
            // Assert acknowledgment
            expect(acknowledgedAlert.status).toBe('acknowledged');
            expect(acknowledgedAlert.acknowledgment).toBeDefined();
            expect(acknowledgedAlert.acknowledgment.acknowledgedBy).toBe(baseTest.testUser.id);
            expect(acknowledgedAlert.acknowledgment.notes).toBe('Investigating the issue');
            // Act - Resolve alert
            const resolvedAlert = await alertingService.resolveAlert(alert.id, baseTest.testUser.id, 'fixed', 'Issue resolved by restarting service', 'Service was stuck in deadlock');
            // Assert resolution
            expect(resolvedAlert.status).toBe('resolved');
            expect(resolvedAlert.resolution).toBeDefined();
            expect(resolvedAlert.resolution.resolvedBy).toBe(baseTest.testUser.id);
            expect(resolvedAlert.resolution.resolution).toBe('fixed');
            // Verify resolution event was emitted
            await baseTest.assertEventEmitted('alert.resolved', {
                alertId: alert.id,
                resolvedBy: baseTest.testUser.id,
            });
        });
        it('should handle alert escalation', async () => {
            // Arrange
            const alert = await baseTest.createTestAlert({
                name: 'Escalating Alert',
                severity: 'warning',
                status: 'active',
                escalationLevel: 0,
                createdAt: new Date(Date.now() - 3600000), // 1 hour ago
            });
            // Mock escalation rules
            jest.spyOn(alertingService, 'getEscalationRules').mockReturnValue([
                {
                    level: 1,
                    delayMinutes: 30,
                    severity: 'critical',
                    recipients: ['<EMAIL>'],
                },
                {
                    level: 2,
                    delayMinutes: 60,
                    severity: 'emergency',
                    recipients: ['<EMAIL>'],
                },
            ]);
            // Act
            const escalatedAlert = await alertingService.escalateAlert(alert.id);
            // Assert
            expect(escalatedAlert.escalationLevel).toBe(1);
            expect(escalatedAlert.severity).toBe('critical');
            // Verify escalation event was emitted
            await baseTest.assertEventEmitted('alert.escalated', {
                alertId: alert.id,
                escalationLevel: 1,
                newSeverity: 'critical',
            });
        });
        it('should correlate related alerts', async () => {
            // Arrange
            const baseAlertData = {
                category: 'system',
                source: 'metrics',
                context: {
                    labels: { service: 'api-server' },
                },
            };
            const alerts = await Promise.all([
                baseTest.createTestAlert({
                    ...baseAlertData,
                    name: 'High CPU Usage',
                    conditions: { metric: 'cpu_usage', operator: '>', threshold: 80 },
                }),
                baseTest.createTestAlert({
                    ...baseAlertData,
                    name: 'High Memory Usage',
                    conditions: { metric: 'memory_usage', operator: '>', threshold: 90 },
                }),
                baseTest.createTestAlert({
                    ...baseAlertData,
                    name: 'High Response Time',
                    conditions: { metric: 'response_time', operator: '>', threshold: 2000 },
                }),
            ]);
            // Act
            const correlatedAlerts = await alertingService.correlateAlerts(alerts[0].id);
            // Assert
            expect(correlatedAlerts).toBeDefined();
            expect(correlatedAlerts.length).toBeGreaterThan(0);
            expect(correlatedAlerts.some(alert => alert.name.includes('Memory'))).toBe(true);
            expect(correlatedAlerts.some(alert => alert.name.includes('Response Time'))).toBe(true);
        });
    });
    describe('Notification System', () => {
        it('should send notifications through multiple channels', async () => {
            // Arrange
            const notificationData = {
                type: 'alert',
                severity: 'critical',
                title: 'Critical System Alert',
                message: 'Database connection failed',
                recipients: [
                    { type: 'email', address: '<EMAIL>' },
                    { type: 'slack', address: '#alerts' },
                    { type: 'webhook', address: 'https://webhook.example.com/alerts' },
                ],
                metadata: {
                    alertId: 'alert-123',
                    source: 'health-check',
                    timestamp: new Date().toISOString(),
                },
            };
            // Mock notification providers
            const emailSpy = jest.spyOn(notificationService, 'sendEmailNotification')
                .mockResolvedValue({ success: true, messageId: 'email-123' });
            const slackSpy = jest.spyOn(notificationService, 'sendSlackNotification')
                .mockResolvedValue({ success: true, messageId: 'slack-123' });
            const webhookSpy = jest.spyOn(notificationService, 'sendWebhookNotification')
                .mockResolvedValue({ success: true, messageId: 'webhook-123' });
            // Act
            const results = await notificationService.sendNotification(notificationData);
            // Assert
            expect(results).toHaveLength(3);
            expect(results.every(result => result.success)).toBe(true);
            expect(emailSpy).toHaveBeenCalledWith(expect.objectContaining({
                to: '<EMAIL>',
                subject: notificationData.title,
                body: expect.stringContaining(notificationData.message),
            }));
            expect(slackSpy).toHaveBeenCalledWith(expect.objectContaining({
                channel: '#alerts',
                text: expect.stringContaining(notificationData.message),
            }));
            expect(webhookSpy).toHaveBeenCalledWith(expect.objectContaining({
                url: 'https://webhook.example.com/alerts',
                payload: expect.objectContaining({
                    title: notificationData.title,
                    message: notificationData.message,
                }),
            }));
        });
        it('should handle notification failures with retry mechanism', async () => {
            // Arrange
            const notificationData = {
                type: 'alert',
                severity: 'warning',
                title: 'Test Notification',
                message: 'Test message',
                recipients: [{ type: 'email', address: '<EMAIL>' }],
            };
            // Mock notification failure then success
            let attemptCount = 0;
            jest.spyOn(notificationService, 'sendEmailNotification')
                .mockImplementation(async () => {
                attemptCount++;
                if (attemptCount < 3) {
                    throw new Error('Temporary failure');
                }
                return { success: true, messageId: 'email-retry-123' };
            });
            // Act
            const results = await notificationService.sendNotification(notificationData);
            // Assert
            expect(results).toHaveLength(1);
            expect(results[0].success).toBe(true);
            expect(attemptCount).toBe(3); // Should have retried twice before success
        });
    });
    describe('Performance Monitoring', () => {
        it('should track system resource utilization', async () => {
            // Act
            const resourceMetrics = await metricsService.collectSystemMetrics();
            // Assert
            expect(resourceMetrics).toBeDefined();
            expect(resourceMetrics).toMatchObject({
                memory: {
                    heapUsed: expect.any(Number),
                    heapTotal: expect.any(Number),
                    external: expect.any(Number),
                    rss: expect.any(Number),
                },
                cpu: {
                    user: expect.any(Number),
                    system: expect.any(Number),
                },
                uptime: expect.any(Number),
                loadAverage: expect.any(Array),
            });
            // Verify metrics were recorded
            const memoryMetrics = await metricsService.getMetrics({
                name: 'system_memory_usage',
                limit: 1,
                offset: 0,
            });
            expect(memoryMetrics.data.length).toBeGreaterThan(0);
        });
        it('should detect performance bottlenecks', async () => {
            // Arrange - Simulate high load
            const highLoadOperation = async () => {
                // Simulate CPU-intensive operation
                const start = Date.now();
                while (Date.now() - start < 100) {
                    Math.random() * Math.random();
                }
            };
            // Act
            const { executionTime } = await baseTest.measureExecutionTime(async () => {
                await Promise.all(Array.from({ length: 10 }, () => highLoadOperation()));
            });
            // Record performance metric
            await metricsService.recordMetric({
                name: 'operation_duration',
                value: executionTime,
                category: 'performance',
                type: 'histogram',
                unit: 'ms',
                source: 'performance-test',
            });
            // Act - Analyze performance
            const performanceAnalysis = await metricsService.analyzePerformance({
                timeRange: {
                    start: new Date(Date.now() - 300000), // 5 minutes ago
                    end: new Date(),
                },
                metrics: ['operation_duration'],
            });
            // Assert
            expect(performanceAnalysis).toBeDefined();
            expect(performanceAnalysis.bottlenecks).toBeDefined();
            expect(performanceAnalysis.recommendations).toBeDefined();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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