1cc04e60dc4727f609aeef313063d621
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../../../infrastructure/auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../../../infrastructure/auth/guards/roles.guard");
const roles_decorator_1 = require("../../../../infrastructure/auth/decorators/roles.decorator");
const current_user_decorator_1 = require("../../../../infrastructure/auth/decorators/current-user.decorator");
const vulnerability_service_1 = require("../../application/services/vulnerability.service");
const create_vulnerability_dto_1 = require("../dto/create-vulnerability.dto");
const update_vulnerability_dto_1 = require("../dto/update-vulnerability.dto");
const search_vulnerabilities_dto_1 = require("../dto/search-vulnerabilities.dto");
/**
 * Vulnerability controller
 * Handles vulnerability management API endpoints
 */
let VulnerabilityController = class VulnerabilityController {
    constructor(vulnerabilityService) {
        this.vulnerabilityService = vulnerabilityService;
    }
    /**
     * Get vulnerability dashboard
     */
    async getDashboard() {
        return await this.vulnerabilityService.getVulnerabilityDashboard();
    }
    /**
     * Search vulnerabilities
     */
    async searchVulnerabilities(query) {
        return await this.vulnerabilityService.searchVulnerabilities(query);
    }
    /**
     * Get vulnerability details
     */
    async getVulnerabilityDetails(id) {
        return await this.vulnerabilityService.getVulnerabilityDetails(id);
    }
    /**
     * Create vulnerability
     */
    async createVulnerability(createVulnerabilityDto, user) {
        return await this.vulnerabilityService.createVulnerability(createVulnerabilityDto, user.id);
    }
    /**
     * Update vulnerability
     */
    async updateVulnerability(id, updateVulnerabilityDto, user) {
        return await this.vulnerabilityService.updateVulnerability(id, updateVulnerabilityDto, user.id);
    }
    /**
     * Delete vulnerability
     */
    async deleteVulnerability(id, user) {
        await this.vulnerabilityService.deleteVulnerability(id, user.id);
    }
    /**
     * Get vulnerability summary
     */
    async getVulnerabilitySummary(id) {
        const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);
        return vulnerability.getSummary();
    }
    /**
     * Export vulnerability for reporting
     */
    async exportVulnerability(id) {
        const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);
        return vulnerability.exportForReporting();
    }
    /**
     * Update exploit status
     */
    async updateExploitStatus(id, exploitData, user) {
        const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);
        vulnerability.updateExploitStatus(exploitData.hasExploit, exploitData.inTheWild);
        if (exploitData.exploitInfo) {
            vulnerability.exploitInfo = exploitData.exploitInfo;
        }
        return await this.vulnerabilityService.updateVulnerability(id, vulnerability, user.id);
    }
    /**
     * Update patch status
     */
    async updatePatchStatus(id, patchData, user) {
        const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);
        vulnerability.updatePatchStatus(patchData.patchAvailable, patchData.patchInfo);
        return await this.vulnerabilityService.updateVulnerability(id, vulnerability, user.id);
    }
    /**
     * Add tag to vulnerability
     */
    async addTag(id, tagData, user) {
        const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);
        vulnerability.addTag(tagData.tag);
        return await this.vulnerabilityService.updateVulnerability(id, vulnerability, user.id);
    }
    /**
     * Remove tag from vulnerability
     */
    async removeTag(id, tag, user) {
        const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);
        vulnerability.removeTag(tag);
        return await this.vulnerabilityService.updateVulnerability(id, vulnerability, user.id);
    }
    /**
     * Get vulnerability risk score
     */
    async getRiskScore(id) {
        const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);
        return {
            vulnerabilityId: id,
            riskScore: vulnerability.calculateRiskScore(),
            factors: {
                cvssScore: vulnerability.cvssScore,
                severity: vulnerability.severity,
                exploitable: vulnerability.exploitable,
                hasExploit: vulnerability.hasExploit,
                inTheWild: vulnerability.inTheWild,
                patchAvailable: vulnerability.patchAvailable,
                isRecent: vulnerability.isRecent,
                epssScore: vulnerability.epssScore,
            },
        };
    }
};
exports.VulnerabilityController = VulnerabilityController;
__decorate([
    (0, common_1.Get)('dashboard'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Get vulnerability dashboard data' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Dashboard data retrieved successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VulnerabilityController.prototype, "getDashboard", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager', 'viewer'),
    (0, swagger_1.ApiOperation)({ summary: 'Search vulnerabilities with filtering and pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Page number' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Items per page' }),
    (0, swagger_1.ApiQuery)({ name: 'severities', required: false, type: [String], description: 'Filter by severities' }),
    (0, swagger_1.ApiQuery)({ name: 'exploitable', required: false, type: Boolean, description: 'Filter by exploitability' }),
    (0, swagger_1.ApiQuery)({ name: 'hasExploit', required: false, type: Boolean, description: 'Filter by exploit availability' }),
    (0, swagger_1.ApiQuery)({ name: 'inTheWild', required: false, type: Boolean, description: 'Filter by in-the-wild status' }),
    (0, swagger_1.ApiQuery)({ name: 'patchAvailable', required: false, type: Boolean, description: 'Filter by patch availability' }),
    (0, swagger_1.ApiQuery)({ name: 'searchText', required: false, type: String, description: 'Search text' }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false, type: String, description: 'Sort field' }),
    (0, swagger_1.ApiQuery)({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Vulnerabilities retrieved successfully',
    }),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof search_vulnerabilities_dto_1.SearchVulnerabilitiesDto !== "undefined" && search_vulnerabilities_dto_1.SearchVulnerabilitiesDto) === "function" ? _b : Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityController.prototype, "searchVulnerabilities", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager', 'viewer'),
    (0, swagger_1.ApiOperation)({ summary: 'Get vulnerability details by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Vulnerability ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Vulnerability details retrieved successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Vulnerability not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VulnerabilityController.prototype, "getVulnerabilityDetails", null);
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new vulnerability' }),
    (0, swagger_1.ApiBody)({ type: create_vulnerability_dto_1.CreateVulnerabilityDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Vulnerability created successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid vulnerability data',
    }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_c = typeof create_vulnerability_dto_1.CreateVulnerabilityDto !== "undefined" && create_vulnerability_dto_1.CreateVulnerabilityDto) === "function" ? _c : Object, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityController.prototype, "createVulnerability", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Update vulnerability' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Vulnerability ID' }),
    (0, swagger_1.ApiBody)({ type: update_vulnerability_dto_1.UpdateVulnerabilityDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Vulnerability updated successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Vulnerability not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_d = typeof update_vulnerability_dto_1.UpdateVulnerabilityDto !== "undefined" && update_vulnerability_dto_1.UpdateVulnerabilityDto) === "function" ? _d : Object, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityController.prototype, "updateVulnerability", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('admin', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete vulnerability' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Vulnerability ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NO_CONTENT,
        description: 'Vulnerability deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Vulnerability not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityController.prototype, "deleteVulnerability", null);
__decorate([
    (0, common_1.Get)(':id/summary'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager', 'viewer'),
    (0, swagger_1.ApiOperation)({ summary: 'Get vulnerability summary' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Vulnerability ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Vulnerability summary retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VulnerabilityController.prototype, "getVulnerabilitySummary", null);
__decorate([
    (0, common_1.Get)(':id/export'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Export vulnerability data for reporting' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Vulnerability ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Vulnerability data exported successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VulnerabilityController.prototype, "exportVulnerability", null);
__decorate([
    (0, common_1.Put)(':id/exploit-status'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Update vulnerability exploit status' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Vulnerability ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                hasExploit: { type: 'boolean' },
                inTheWild: { type: 'boolean' },
                exploitInfo: { type: 'object' },
            },
            required: ['hasExploit', 'inTheWild'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Exploit status updated successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityController.prototype, "updateExploitStatus", null);
__decorate([
    (0, common_1.Put)(':id/patch-status'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Update vulnerability patch status' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Vulnerability ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                patchAvailable: { type: 'boolean' },
                patchInfo: { type: 'object' },
            },
            required: ['patchAvailable'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Patch status updated successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityController.prototype, "updatePatchStatus", null);
__decorate([
    (0, common_1.Post)(':id/tags'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Add tag to vulnerability' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Vulnerability ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                tag: { type: 'string' },
            },
            required: ['tag'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Tag added successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityController.prototype, "addTag", null);
__decorate([
    (0, common_1.Delete)(':id/tags/:tag'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Remove tag from vulnerability' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Vulnerability ID' }),
    (0, swagger_1.ApiParam)({ name: 'tag', description: 'Tag to remove' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Tag removed successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('tag')),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityController.prototype, "removeTag", null);
__decorate([
    (0, common_1.Get)(':id/risk-score'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager', 'viewer'),
    (0, swagger_1.ApiOperation)({ summary: 'Calculate vulnerability risk score' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Vulnerability ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Risk score calculated successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VulnerabilityController.prototype, "getRiskScore", null);
exports.VulnerabilityController = VulnerabilityController = __decorate([
    (0, swagger_1.ApiTags)('Vulnerabilities'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('api/vulnerabilities'),
    __metadata("design:paramtypes", [typeof (_a = typeof vulnerability_service_1.VulnerabilityService !== "undefined" && vulnerability_service_1.VulnerabilityService) === "function" ? _a : Object])
], VulnerabilityController);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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