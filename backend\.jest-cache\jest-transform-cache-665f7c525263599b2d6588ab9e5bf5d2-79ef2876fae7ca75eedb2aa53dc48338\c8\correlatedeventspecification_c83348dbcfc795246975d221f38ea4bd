135415cb01caec5f1654af0c60de041b
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorrelatedEventSpecificationBuilder = exports.AverageMatchConfidenceRangeSpecification = exports.CorrelationDurationRangeSpecification = exports.PendingReviewSpecification = exports.ReviewedEventSpecification = exports.ExceededMaxAttemptsSpecification = exports.MatchTypeSpecification = exports.CorrelationPatternSpecification = exports.HasChildEventsSpecification = exports.HasRelatedEventsSpecification = exports.CorrelationIdSpecification = exports.EnrichedEventSpecification = exports.AppliedRuleSpecification = exports.CorrelationQualityScoreRangeSpecification = exports.ConfidenceLevelSpecification = exports.CorrelationStatusSpecification = exports.HasBehavioralCorrelationSpecification = exports.HasSpatialCorrelationSpecification = exports.HasTemporalCorrelationSpecification = exports.HasAttackChainSpecification = exports.HighConfidenceCorrelationSpecification = exports.ReadyForNextStageSpecification = exports.RequiresManualReviewSpecification = exports.HasValidationErrorsSpecification = exports.HighCorrelationQualitySpecification = exports.CorrelationPartialSpecification = exports.CorrelationInProgressSpecification = exports.CorrelationFailedSpecification = exports.CorrelationCompletedSpecification = exports.CorrelatedEventSpecification = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
/**
 * CorrelatedEvent Specification Base Class
 *
 * Base class for all correlated event-related specifications.
 * Provides common functionality for correlated event filtering and validation.
 */
class CorrelatedEventSpecification extends shared_kernel_1.BaseSpecification {
    /**
     * Helper method to check if correlated event matches any of the provided types
     */
    matchesAnyType(event, types) {
        return types.includes(event.type);
    }
    /**
     * Helper method to check if correlated event matches any of the provided severities
     */
    matchesAnySeverity(event, severities) {
        return severities.includes(event.severity);
    }
    /**
     * Helper method to check if correlated event matches any of the provided statuses
     */
    matchesAnyStatus(event, statuses) {
        return statuses.includes(event.status);
    }
    /**
     * Helper method to check if correlated event matches any of the provided processing statuses
     */
    matchesAnyProcessingStatus(event, statuses) {
        return statuses.includes(event.processingStatus);
    }
    /**
     * Helper method to check if correlated event matches any of the provided correlation statuses
     */
    matchesAnyCorrelationStatus(event, statuses) {
        return statuses.includes(event.correlationStatus);
    }
    /**
     * Helper method to check if correlated event matches any of the provided confidence levels
     */
    matchesAnyConfidenceLevel(event, levels) {
        return levels.includes(event.confidenceLevel);
    }
    /**
     * Helper method to check if correlated event has any of the provided tags
     */
    hasAnyTag(event, tags) {
        return event.tags.some(tag => tags.includes(tag));
    }
    /**
     * Helper method to check if correlated event has all of the provided tags
     */
    hasAllTags(event, tags) {
        return tags.every(tag => event.tags.includes(tag));
    }
    /**
     * Helper method to check if correlation quality score is within range
     */
    isCorrelationQualityScoreWithinRange(event, minScore, maxScore) {
        const qualityScore = event.correlationQualityScore;
        if (qualityScore === undefined) {
            return minScore === undefined; // If no min score required, undefined is acceptable
        }
        if (minScore !== undefined && qualityScore < minScore) {
            return false;
        }
        if (maxScore !== undefined && qualityScore > maxScore) {
            return false;
        }
        return true;
    }
    /**
     * Helper method to check if risk score is within range
     */
    isRiskScoreWithinRange(event, minScore, maxScore) {
        const riskScore = event.riskScore;
        if (riskScore === undefined) {
            return minScore === undefined; // If no min score required, undefined is acceptable
        }
        if (minScore !== undefined && riskScore < minScore) {
            return false;
        }
        if (maxScore !== undefined && riskScore > maxScore) {
            return false;
        }
        return true;
    }
    /**
     * Helper method to check if applied rule exists
     */
    hasAppliedRule(event, ruleId) {
        return event.hasAppliedRule(ruleId);
    }
    /**
     * Helper method to check if correlation match by rule exists
     */
    hasMatchByRule(event, ruleId) {
        return event.getMatchesByRule(ruleId).length > 0;
    }
    /**
     * Helper method to check if correlation match by type exists
     */
    hasMatchByType(event, matchType) {
        return event.getMatchesByType(matchType).length > 0;
    }
    /**
     * Helper method to check if correlation pattern exists
     */
    hasCorrelationPattern(event, pattern) {
        return event.correlationPatterns.includes(pattern);
    }
}
exports.CorrelatedEventSpecification = CorrelatedEventSpecification;
/**
 * Correlation Completed Specification
 *
 * Specification for correlated events that have completed correlation.
 */
class CorrelationCompletedSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.isCorrelationCompleted();
    }
    getDescription() {
        return 'Correlated event has completed correlation';
    }
}
exports.CorrelationCompletedSpecification = CorrelationCompletedSpecification;
/**
 * Correlation Failed Specification
 *
 * Specification for correlated events that have failed correlation.
 */
class CorrelationFailedSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.isCorrelationFailed();
    }
    getDescription() {
        return 'Correlated event has failed correlation';
    }
}
exports.CorrelationFailedSpecification = CorrelationFailedSpecification;
/**
 * Correlation In Progress Specification
 *
 * Specification for correlated events that are currently being correlated.
 */
class CorrelationInProgressSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.isCorrelationInProgress();
    }
    getDescription() {
        return 'Correlated event is currently being correlated';
    }
}
exports.CorrelationInProgressSpecification = CorrelationInProgressSpecification;
/**
 * Correlation Partial Specification
 *
 * Specification for correlated events that have partial correlation results.
 */
class CorrelationPartialSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.isCorrelationPartial();
    }
    getDescription() {
        return 'Correlated event has partial correlation results';
    }
}
exports.CorrelationPartialSpecification = CorrelationPartialSpecification;
/**
 * High Correlation Quality Specification
 *
 * Specification for correlated events with high correlation quality scores.
 */
class HighCorrelationQualitySpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasHighCorrelationQuality();
    }
    getDescription() {
        return 'Correlated event has high correlation quality (>= 70)';
    }
}
exports.HighCorrelationQualitySpecification = HighCorrelationQualitySpecification;
/**
 * Has Validation Errors Specification
 *
 * Specification for correlated events that have validation errors.
 */
class HasValidationErrorsSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasValidationErrors();
    }
    getDescription() {
        return 'Correlated event has validation errors';
    }
}
exports.HasValidationErrorsSpecification = HasValidationErrorsSpecification;
/**
 * Requires Manual Review Specification
 *
 * Specification for correlated events that require manual review.
 */
class RequiresManualReviewSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.requiresManualReview;
    }
    getDescription() {
        return 'Correlated event requires manual review';
    }
}
exports.RequiresManualReviewSpecification = RequiresManualReviewSpecification;
/**
 * Ready For Next Stage Specification
 *
 * Specification for correlated events that are ready for the next processing stage.
 */
class ReadyForNextStageSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.isReadyForNextStage();
    }
    getDescription() {
        return 'Correlated event is ready for next processing stage';
    }
}
exports.ReadyForNextStageSpecification = ReadyForNextStageSpecification;
/**
 * High Confidence Correlation Specification
 *
 * Specification for correlated events with high confidence correlation.
 */
class HighConfidenceCorrelationSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.isHighConfidenceCorrelation();
    }
    getDescription() {
        return 'Correlated event has high confidence correlation';
    }
}
exports.HighConfidenceCorrelationSpecification = HighConfidenceCorrelationSpecification;
/**
 * Has Attack Chain Specification
 *
 * Specification for correlated events that have an attack chain.
 */
class HasAttackChainSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasAttackChain();
    }
    getDescription() {
        return 'Correlated event has an attack chain';
    }
}
exports.HasAttackChainSpecification = HasAttackChainSpecification;
/**
 * Has Temporal Correlation Specification
 *
 * Specification for correlated events that have temporal correlation data.
 */
class HasTemporalCorrelationSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasTemporalCorrelation();
    }
    getDescription() {
        return 'Correlated event has temporal correlation data';
    }
}
exports.HasTemporalCorrelationSpecification = HasTemporalCorrelationSpecification;
/**
 * Has Spatial Correlation Specification
 *
 * Specification for correlated events that have spatial correlation data.
 */
class HasSpatialCorrelationSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasSpatialCorrelation();
    }
    getDescription() {
        return 'Correlated event has spatial correlation data';
    }
}
exports.HasSpatialCorrelationSpecification = HasSpatialCorrelationSpecification;
/**
 * Has Behavioral Correlation Specification
 *
 * Specification for correlated events that have behavioral correlation data.
 */
class HasBehavioralCorrelationSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasBehavioralCorrelation();
    }
    getDescription() {
        return 'Correlated event has behavioral correlation data';
    }
}
exports.HasBehavioralCorrelationSpecification = HasBehavioralCorrelationSpecification;
/**
 * Correlation Status Specification
 *
 * Specification for correlated events with specific correlation statuses.
 */
class CorrelationStatusSpecification extends CorrelatedEventSpecification {
    constructor(statuses) {
        super();
        this.statuses = statuses;
    }
    isSatisfiedBy(event) {
        return this.matchesAnyCorrelationStatus(event, this.statuses);
    }
    getDescription() {
        return `Correlated event status is one of: ${this.statuses.join(', ')}`;
    }
}
exports.CorrelationStatusSpecification = CorrelationStatusSpecification;
/**
 * Confidence Level Specification
 *
 * Specification for correlated events with specific confidence levels.
 */
class ConfidenceLevelSpecification extends CorrelatedEventSpecification {
    constructor(levels) {
        super();
        this.levels = levels;
    }
    isSatisfiedBy(event) {
        return this.matchesAnyConfidenceLevel(event, this.levels);
    }
    getDescription() {
        return `Correlated event confidence level is one of: ${this.levels.join(', ')}`;
    }
}
exports.ConfidenceLevelSpecification = ConfidenceLevelSpecification;
/**
 * Correlation Quality Score Range Specification
 *
 * Specification for correlated events within a specific correlation quality score range.
 */
class CorrelationQualityScoreRangeSpecification extends CorrelatedEventSpecification {
    constructor(minScore, maxScore) {
        super();
        this.minScore = minScore;
        this.maxScore = maxScore;
    }
    isSatisfiedBy(event) {
        return this.isCorrelationQualityScoreWithinRange(event, this.minScore, this.maxScore);
    }
    getDescription() {
        if (this.minScore !== undefined && this.maxScore !== undefined) {
            return `Correlated event quality score is between ${this.minScore} and ${this.maxScore}`;
        }
        else if (this.minScore !== undefined) {
            return `Correlated event quality score is at least ${this.minScore}`;
        }
        else if (this.maxScore !== undefined) {
            return `Correlated event quality score is at most ${this.maxScore}`;
        }
        return 'Correlated event has any quality score';
    }
}
exports.CorrelationQualityScoreRangeSpecification = CorrelationQualityScoreRangeSpecification;
/**
 * Applied Rule Specification
 *
 * Specification for correlated events that have specific rules applied.
 */
class AppliedRuleSpecification extends CorrelatedEventSpecification {
    constructor(ruleId) {
        super();
        this.ruleId = ruleId;
    }
    isSatisfiedBy(event) {
        return this.hasAppliedRule(event, this.ruleId);
    }
    getDescription() {
        return `Correlated event has applied rule: ${this.ruleId}`;
    }
}
exports.AppliedRuleSpecification = AppliedRuleSpecification;
/**
 * Enriched Event Specification
 *
 * Specification for correlated events that reference a specific enriched event.
 */
class EnrichedEventSpecification extends CorrelatedEventSpecification {
    constructor(enrichedEventId) {
        super();
        this.enrichedEventId = enrichedEventId;
    }
    isSatisfiedBy(event) {
        return event.enrichedEventId.equals(this.enrichedEventId);
    }
    getDescription() {
        return `Correlated event references enriched event: ${this.enrichedEventId.toString()}`;
    }
}
exports.EnrichedEventSpecification = EnrichedEventSpecification;
/**
 * Correlation ID Specification
 *
 * Specification for correlated events with a specific correlation ID.
 */
class CorrelationIdSpecification extends CorrelatedEventSpecification {
    constructor(correlationId) {
        super();
        this.correlationId = correlationId;
    }
    isSatisfiedBy(event) {
        return event.correlationId === this.correlationId;
    }
    getDescription() {
        return `Correlated event has correlation ID: ${this.correlationId}`;
    }
}
exports.CorrelationIdSpecification = CorrelationIdSpecification;
/**
 * Has Related Events Specification
 *
 * Specification for correlated events that have related events.
 */
class HasRelatedEventsSpecification extends CorrelatedEventSpecification {
    constructor(minCount = 1) {
        super();
        this.minCount = minCount;
    }
    isSatisfiedBy(event) {
        return event.relatedEventIds.length >= this.minCount;
    }
    getDescription() {
        return `Correlated event has at least ${this.minCount} related events`;
    }
}
exports.HasRelatedEventsSpecification = HasRelatedEventsSpecification;
/**
 * Has Child Events Specification
 *
 * Specification for correlated events that have child events.
 */
class HasChildEventsSpecification extends CorrelatedEventSpecification {
    constructor(minCount = 1) {
        super();
        this.minCount = minCount;
    }
    isSatisfiedBy(event) {
        return event.childEventIds.length >= this.minCount;
    }
    getDescription() {
        return `Correlated event has at least ${this.minCount} child events`;
    }
}
exports.HasChildEventsSpecification = HasChildEventsSpecification;
/**
 * Correlation Pattern Specification
 *
 * Specification for correlated events that have specific correlation patterns.
 */
class CorrelationPatternSpecification extends CorrelatedEventSpecification {
    constructor(patterns) {
        super();
        this.patterns = patterns;
    }
    isSatisfiedBy(event) {
        return this.patterns.some(pattern => this.hasCorrelationPattern(event, pattern));
    }
    getDescription() {
        return `Correlated event has patterns: ${this.patterns.join(', ')}`;
    }
}
exports.CorrelationPatternSpecification = CorrelationPatternSpecification;
/**
 * Match Type Specification
 *
 * Specification for correlated events that have matches of specific types.
 */
class MatchTypeSpecification extends CorrelatedEventSpecification {
    constructor(matchTypes) {
        super();
        this.matchTypes = matchTypes;
    }
    isSatisfiedBy(event) {
        return this.matchTypes.some(type => this.hasMatchByType(event, type));
    }
    getDescription() {
        return `Correlated event has matches of types: ${this.matchTypes.join(', ')}`;
    }
}
exports.MatchTypeSpecification = MatchTypeSpecification;
/**
 * Exceeded Max Attempts Specification
 *
 * Specification for correlated events that have exceeded maximum correlation attempts.
 */
class ExceededMaxAttemptsSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasExceededMaxCorrelationAttempts();
    }
    getDescription() {
        return 'Correlated event has exceeded maximum correlation attempts';
    }
}
exports.ExceededMaxAttemptsSpecification = ExceededMaxAttemptsSpecification;
/**
 * Reviewed Event Specification
 *
 * Specification for correlated events that have been manually reviewed.
 */
class ReviewedEventSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.reviewedAt !== undefined;
    }
    getDescription() {
        return 'Correlated event has been manually reviewed';
    }
}
exports.ReviewedEventSpecification = ReviewedEventSpecification;
/**
 * Pending Review Specification
 *
 * Specification for correlated events that are pending manual review.
 */
class PendingReviewSpecification extends CorrelatedEventSpecification {
    isSatisfiedBy(event) {
        return event.requiresManualReview && event.reviewedAt === undefined;
    }
    getDescription() {
        return 'Correlated event is pending manual review';
    }
}
exports.PendingReviewSpecification = PendingReviewSpecification;
/**
 * Correlation Duration Range Specification
 *
 * Specification for correlated events with correlation duration within a specific range.
 */
class CorrelationDurationRangeSpecification extends CorrelatedEventSpecification {
    constructor(minDurationMs, maxDurationMs) {
        super();
        this.minDurationMs = minDurationMs;
        this.maxDurationMs = maxDurationMs;
    }
    isSatisfiedBy(event) {
        const duration = event.getCorrelationDuration();
        if (duration === null) {
            return false; // No duration available
        }
        if (this.minDurationMs !== undefined && duration < this.minDurationMs) {
            return false;
        }
        if (this.maxDurationMs !== undefined && duration > this.maxDurationMs) {
            return false;
        }
        return true;
    }
    getDescription() {
        const formatDuration = (ms) => `${ms}ms`;
        if (this.minDurationMs !== undefined && this.maxDurationMs !== undefined) {
            return `Correlated event duration is between ${formatDuration(this.minDurationMs)} and ${formatDuration(this.maxDurationMs)}`;
        }
        else if (this.minDurationMs !== undefined) {
            return `Correlated event duration is at least ${formatDuration(this.minDurationMs)}`;
        }
        else if (this.maxDurationMs !== undefined) {
            return `Correlated event duration is at most ${formatDuration(this.maxDurationMs)}`;
        }
        return 'Correlated event has any duration';
    }
}
exports.CorrelationDurationRangeSpecification = CorrelationDurationRangeSpecification;
/**
 * Average Match Confidence Range Specification
 *
 * Specification for correlated events with average match confidence within a specific range.
 */
class AverageMatchConfidenceRangeSpecification extends CorrelatedEventSpecification {
    constructor(minConfidence, maxConfidence) {
        super();
        this.minConfidence = minConfidence;
        this.maxConfidence = maxConfidence;
    }
    isSatisfiedBy(event) {
        const avgConfidence = event.getAverageMatchConfidence();
        if (avgConfidence === null) {
            return this.minConfidence === undefined; // If no min confidence required, null is acceptable
        }
        if (this.minConfidence !== undefined && avgConfidence < this.minConfidence) {
            return false;
        }
        if (this.maxConfidence !== undefined && avgConfidence > this.maxConfidence) {
            return false;
        }
        return true;
    }
    getDescription() {
        if (this.minConfidence !== undefined && this.maxConfidence !== undefined) {
            return `Correlated event average match confidence is between ${this.minConfidence} and ${this.maxConfidence}`;
        }
        else if (this.minConfidence !== undefined) {
            return `Correlated event average match confidence is at least ${this.minConfidence}`;
        }
        else if (this.maxConfidence !== undefined) {
            return `Correlated event average match confidence is at most ${this.maxConfidence}`;
        }
        return 'Correlated event has any average match confidence';
    }
}
exports.AverageMatchConfidenceRangeSpecification = AverageMatchConfidenceRangeSpecification;
/**
 * Composite CorrelatedEvent Specification Builder
 *
 * Builder for creating complex correlated event specifications using fluent interface.
 */
class CorrelatedEventSpecificationBuilder {
    constructor() {
        this.specifications = [];
    }
    /**
     * Add correlation completed filter
     */
    correlationCompleted() {
        this.specifications.push(new CorrelationCompletedSpecification());
        return this;
    }
    /**
     * Add correlation failed filter
     */
    correlationFailed() {
        this.specifications.push(new CorrelationFailedSpecification());
        return this;
    }
    /**
     * Add correlation in progress filter
     */
    correlationInProgress() {
        this.specifications.push(new CorrelationInProgressSpecification());
        return this;
    }
    /**
     * Add correlation partial filter
     */
    correlationPartial() {
        this.specifications.push(new CorrelationPartialSpecification());
        return this;
    }
    /**
     * Add high correlation quality filter
     */
    highCorrelationQuality() {
        this.specifications.push(new HighCorrelationQualitySpecification());
        return this;
    }
    /**
     * Add has validation errors filter
     */
    hasValidationErrors() {
        this.specifications.push(new HasValidationErrorsSpecification());
        return this;
    }
    /**
     * Add requires manual review filter
     */
    requiresManualReview() {
        this.specifications.push(new RequiresManualReviewSpecification());
        return this;
    }
    /**
     * Add ready for next stage filter
     */
    readyForNextStage() {
        this.specifications.push(new ReadyForNextStageSpecification());
        return this;
    }
    /**
     * Add high confidence correlation filter
     */
    highConfidenceCorrelation() {
        this.specifications.push(new HighConfidenceCorrelationSpecification());
        return this;
    }
    /**
     * Add has attack chain filter
     */
    hasAttackChain() {
        this.specifications.push(new HasAttackChainSpecification());
        return this;
    }
    /**
     * Add has temporal correlation filter
     */
    hasTemporalCorrelation() {
        this.specifications.push(new HasTemporalCorrelationSpecification());
        return this;
    }
    /**
     * Add has spatial correlation filter
     */
    hasSpatialCorrelation() {
        this.specifications.push(new HasSpatialCorrelationSpecification());
        return this;
    }
    /**
     * Add has behavioral correlation filter
     */
    hasBehavioralCorrelation() {
        this.specifications.push(new HasBehavioralCorrelationSpecification());
        return this;
    }
    /**
     * Add correlation status filter
     */
    withCorrelationStatus(...statuses) {
        this.specifications.push(new CorrelationStatusSpecification(statuses));
        return this;
    }
    /**
     * Add confidence level filter
     */
    withConfidenceLevel(...levels) {
        this.specifications.push(new ConfidenceLevelSpecification(levels));
        return this;
    }
    /**
     * Add correlation quality score range filter
     */
    correlationQualityScoreRange(minScore, maxScore) {
        this.specifications.push(new CorrelationQualityScoreRangeSpecification(minScore, maxScore));
        return this;
    }
    /**
     * Add applied rule filter
     */
    withAppliedRule(ruleId) {
        this.specifications.push(new AppliedRuleSpecification(ruleId));
        return this;
    }
    /**
     * Add enriched event filter
     */
    fromEnrichedEvent(enrichedEventId) {
        this.specifications.push(new EnrichedEventSpecification(enrichedEventId));
        return this;
    }
    /**
     * Add correlation ID filter
     */
    withCorrelationId(correlationId) {
        this.specifications.push(new CorrelationIdSpecification(correlationId));
        return this;
    }
    /**
     * Add has related events filter
     */
    hasRelatedEvents(minCount = 1) {
        this.specifications.push(new HasRelatedEventsSpecification(minCount));
        return this;
    }
    /**
     * Add has child events filter
     */
    hasChildEvents(minCount = 1) {
        this.specifications.push(new HasChildEventsSpecification(minCount));
        return this;
    }
    /**
     * Add correlation pattern filter
     */
    withCorrelationPatterns(...patterns) {
        this.specifications.push(new CorrelationPatternSpecification(patterns));
        return this;
    }
    /**
     * Add match type filter
     */
    withMatchTypes(...matchTypes) {
        this.specifications.push(new MatchTypeSpecification(matchTypes));
        return this;
    }
    /**
     * Add exceeded max attempts filter
     */
    exceededMaxAttempts() {
        this.specifications.push(new ExceededMaxAttemptsSpecification());
        return this;
    }
    /**
     * Add reviewed filter
     */
    reviewed() {
        this.specifications.push(new ReviewedEventSpecification());
        return this;
    }
    /**
     * Add pending review filter
     */
    pendingReview() {
        this.specifications.push(new PendingReviewSpecification());
        return this;
    }
    /**
     * Add correlation duration range filter
     */
    correlationDurationRange(minDurationMs, maxDurationMs) {
        this.specifications.push(new CorrelationDurationRangeSpecification(minDurationMs, maxDurationMs));
        return this;
    }
    /**
     * Add average match confidence range filter
     */
    averageMatchConfidenceRange(minConfidence, maxConfidence) {
        this.specifications.push(new AverageMatchConfidenceRangeSpecification(minConfidence, maxConfidence));
        return this;
    }
    /**
     * Build the final specification using AND logic
     */
    build() {
        if (this.specifications.length === 0) {
            throw new Error('At least one specification must be added');
        }
        if (this.specifications.length === 1) {
            return this.specifications[0];
        }
        // Combine all specifications with AND logic
        let combined = this.specifications[0];
        for (let i = 1; i < this.specifications.length; i++) {
            combined = combined.and(this.specifications[i]);
        }
        return combined;
    }
    /**
     * Build the final specification using OR logic
     */
    buildWithOr() {
        if (this.specifications.length === 0) {
            throw new Error('At least one specification must be added');
        }
        if (this.specifications.length === 1) {
            return this.specifications[0];
        }
        // Combine all specifications with OR logic
        let combined = this.specifications[0];
        for (let i = 1; i < this.specifications.length; i++) {
            combined = combined.or(this.specifications[i]);
        }
        return combined;
    }
    /**
     * Create a new builder instance
     */
    static create() {
        return new CorrelatedEventSpecificationBuilder();
    }
}
exports.CorrelatedEventSpecificationBuilder = CorrelatedEventSpecificationBuilder;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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