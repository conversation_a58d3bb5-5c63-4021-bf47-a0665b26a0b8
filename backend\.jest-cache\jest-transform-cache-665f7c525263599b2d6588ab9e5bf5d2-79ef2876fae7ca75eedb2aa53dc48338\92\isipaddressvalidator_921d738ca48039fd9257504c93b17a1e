ac01740989ab12d0f0d5c1341a611509
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpAddressUtils = exports.IsIpAddressConstraint = void 0;
exports.IsIpAddress = IsIpAddress;
exports.IsIPv4Address = IsIPv4Address;
exports.IsIPv6Address = IsIPv6Address;
exports.IsPublicIpAddress = IsPublicIpAddress;
exports.IsIpAddressArray = IsIpAddressArray;
const class_validator_1 = require("class-validator");
const net_1 = require("net");
/**
 * IP address format validator constraint
 * Validates IPv4 and IPv6 addresses with configurable options
 */
let IsIpAddressConstraint = class IsIpAddressConstraint {
    /**
     * Validate IP address format and type
     * @param value Value to validate
     * @param args Validation arguments
     * @returns boolean indicating if value is valid IP address
     */
    validate(value, args) {
        if (typeof value !== 'string') {
            return false;
        }
        const defaultOptions = {
            allowIPv4: true,
            allowIPv6: true,
            allowPrivate: true,
            allowLoopback: true,
            allowMulticast: false,
            allowReserved: false,
        };
        const options = {
            ...defaultOptions,
            ...(args.constraints[0] || {}),
        };
        // Check if it's a valid IP address
        const ipVersion = (0, net_1.isIP)(value);
        if (ipVersion === 0) {
            return false;
        }
        // Check IP version constraints
        if (ipVersion === 4 && !options.allowIPv4) {
            return false;
        }
        if (ipVersion === 6 && !options.allowIPv6) {
            return false;
        }
        // Additional validations based on options
        if (!options.allowPrivate && this.isPrivateIP(value)) {
            return false;
        }
        if (!options.allowLoopback && this.isLoopbackIP(value)) {
            return false;
        }
        if (!options.allowMulticast && this.isMulticastIP(value)) {
            return false;
        }
        if (!options.allowReserved && this.isReservedIP(value)) {
            return false;
        }
        return true;
    }
    /**
     * Check if IP address is private
     * @param ip IP address string
     * @returns boolean indicating if IP is private
     */
    isPrivateIP(ip) {
        const ipVersion = (0, net_1.isIP)(ip);
        if (ipVersion === 4) {
            // IPv4 private ranges
            const privateRanges = [
                /^10\./, // 10.0.0.0/8
                /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12
                /^192\.168\./, // ***********/16
            ];
            return privateRanges.some(range => range.test(ip));
        }
        else if (ipVersion === 6) {
            // IPv6 private ranges
            const privateRanges = [
                /^fc00:/i, // fc00::/7 (Unique Local Addresses)
                /^fd00:/i, // fd00::/8 (Unique Local Addresses)
                /^fe80:/i, // fe80::/10 (Link-Local)
            ];
            return privateRanges.some(range => range.test(ip));
        }
        return false;
    }
    /**
     * Check if IP address is loopback
     * @param ip IP address string
     * @returns boolean indicating if IP is loopback
     */
    isLoopbackIP(ip) {
        const ipVersion = (0, net_1.isIP)(ip);
        if (ipVersion === 4) {
            return /^127\./.test(ip);
        }
        else if (ipVersion === 6) {
            return ip.toLowerCase() === '::1';
        }
        return false;
    }
    /**
     * Check if IP address is multicast
     * @param ip IP address string
     * @returns boolean indicating if IP is multicast
     */
    isMulticastIP(ip) {
        const ipVersion = (0, net_1.isIP)(ip);
        if (ipVersion === 4) {
            // IPv4 multicast range: *********/4
            return /^2(2[4-9]|3[0-9])\./.test(ip);
        }
        else if (ipVersion === 6) {
            // IPv6 multicast range: ff00::/8
            return /^ff[0-9a-f]{2}:/i.test(ip);
        }
        return false;
    }
    /**
     * Check if IP address is reserved
     * @param ip IP address string
     * @returns boolean indicating if IP is reserved
     */
    isReservedIP(ip) {
        const ipVersion = (0, net_1.isIP)(ip);
        if (ipVersion === 4) {
            const reservedRanges = [
                /^0\./, // 0.0.0.0/8 (This network)
                /^169\.254\./, // ***********/16 (Link-local)
                /^192\.0\.0\./, // *********/24 (IETF Protocol Assignments)
                /^192\.0\.2\./, // *********/24 (TEST-NET-1)
                /^198\.51\.100\./, // ************/24 (TEST-NET-2)
                /^203\.0\.113\./, // ***********/24 (TEST-NET-3)
                /^240\./, // 240.0.0.0/4 (Reserved for future use)
            ];
            return reservedRanges.some(range => range.test(ip));
        }
        else if (ipVersion === 6) {
            const reservedRanges = [
                /^::$/, // :: (Unspecified address, but not ::1)
                /^2001:db8:/i, // 2001:db8::/32 (Documentation)
                /^2001:10:/i, // 2001:10::/28 (ORCHID)
            ];
            return reservedRanges.some(range => range.test(ip));
        }
        return false;
    }
    /**
     * Default error message for IP address validation
     * @param args Validation arguments
     * @returns Error message
     */
    defaultMessage(args) {
        const options = args.constraints[0] || {};
        const allowedTypes = [];
        if (options.allowIPv4 !== false)
            allowedTypes.push('IPv4');
        if (options.allowIPv6 !== false)
            allowedTypes.push('IPv6');
        return `${args.property} must be a valid ${allowedTypes.join(' or ')} address`;
    }
};
exports.IsIpAddressConstraint = IsIpAddressConstraint;
exports.IsIpAddressConstraint = IsIpAddressConstraint = __decorate([
    (0, class_validator_1.ValidatorConstraint)({ name: 'isIpAddress', async: false })
], IsIpAddressConstraint);
/**
 * IP address validation decorator
 * @param options IP address validation options
 * @param validationOptions Validation options
 * @returns Property decorator
 */
function IsIpAddress(options, validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [options],
            validator: IsIpAddressConstraint,
        });
    };
}
/**
 * IPv4 address validation decorator
 * @param options IP address validation options
 * @param validationOptions Validation options
 * @returns Property decorator
 */
function IsIPv4Address(options, validationOptions) {
    return IsIpAddress({ ...options, allowIPv4: true, allowIPv6: false }, validationOptions);
}
/**
 * IPv6 address validation decorator
 * @param options IP address validation options
 * @param validationOptions Validation options
 * @returns Property decorator
 */
function IsIPv6Address(options, validationOptions) {
    return IsIpAddress({ ...options, allowIPv4: false, allowIPv6: true }, validationOptions);
}
/**
 * Public IP address validation decorator
 * @param validationOptions Validation options
 * @returns Property decorator
 */
function IsPublicIpAddress(validationOptions) {
    return IsIpAddress({
        allowIPv4: true,
        allowIPv6: true,
        allowPrivate: false,
        allowLoopback: false,
        allowMulticast: false,
        allowReserved: false,
    }, validationOptions);
}
/**
 * IP address array validation decorator
 * @param options IP address validation options
 * @param validationOptions Validation options
 * @returns Property decorator
 */
function IsIpAddressArray(options, validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            name: 'isIpAddressArray',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [options],
            validator: {
                validate(value, args) {
                    if (!Array.isArray(value)) {
                        return false;
                    }
                    const constraint = new IsIpAddressConstraint();
                    return value.every(item => constraint.validate(item, args));
                },
                defaultMessage(args) {
                    return `${args.property} must be an array of valid IP addresses`;
                },
            },
        });
    };
}
/**
 * Utility functions for IP address handling
 */
class IpAddressUtils {
    /**
     * Get IP address version
     * @param ip IP address string
     * @returns IP version (4, 6, or 0 if invalid)
     */
    static getVersion(ip) {
        return (0, net_1.isIP)(ip);
    }
    /**
     * Check if IP address is valid
     * @param ip IP address string
     * @param options Validation options
     * @returns boolean indicating validity
     */
    static isValid(ip, options) {
        const constraint = new IsIpAddressConstraint();
        return constraint.validate(ip, { constraints: [options] });
    }
    /**
     * Normalize IP address format
     * @param ip IP address string
     * @returns Normalized IP address or null if invalid
     */
    static normalize(ip) {
        if (!this.isValid(ip)) {
            return null;
        }
        const version = (0, net_1.isIP)(ip);
        if (version === 6) {
            // Normalize IPv6 address (expand compressed notation)
            try {
                const parts = ip.split(':');
                const expandedParts = [];
                let doubleColonIndex = -1;
                // Find double colon position
                for (let i = 0; i < parts.length; i++) {
                    if (parts[i] === '' && doubleColonIndex === -1) {
                        doubleColonIndex = i;
                    }
                }
                if (doubleColonIndex !== -1) {
                    // Expand double colon
                    const beforeDoubleColon = parts.slice(0, doubleColonIndex);
                    const afterDoubleColon = parts.slice(doubleColonIndex + 1).filter(p => p !== '');
                    const missingParts = 8 - beforeDoubleColon.length - afterDoubleColon.length;
                    expandedParts.push(...beforeDoubleColon);
                    for (let i = 0; i < missingParts; i++) {
                        expandedParts.push('0000');
                    }
                    expandedParts.push(...afterDoubleColon);
                }
                else {
                    expandedParts.push(...parts);
                }
                // Pad each part to 4 characters
                return expandedParts.map(part => part.padStart(4, '0')).join(':').toLowerCase();
            }
            catch {
                return ip.toLowerCase();
            }
        }
        return ip;
    }
    /**
     * Check if IP is in CIDR range
     * @param ip IP address to check
     * @param cidr CIDR notation (e.g., "***********/24")
     * @returns boolean indicating if IP is in range
     */
    static isInCidrRange(ip, cidr) {
        const [network, prefixLength] = cidr.split('/');
        const prefix = parseInt(prefixLength, 10);
        if ((0, net_1.isIP)(ip) !== (0, net_1.isIP)(network)) {
            return false; // Different IP versions
        }
        if ((0, net_1.isIP)(ip) === 4) {
            return this.isIPv4InCidrRange(ip, network, prefix);
        }
        else if ((0, net_1.isIP)(ip) === 6) {
            return this.isIPv6InCidrRange(ip, network, prefix);
        }
        return false;
    }
    /**
     * Check if IPv4 is in CIDR range
     * @param ip IPv4 address
     * @param network Network address
     * @param prefix Prefix length
     * @returns boolean indicating if IP is in range
     */
    static isIPv4InCidrRange(ip, network, prefix) {
        const ipInt = this.ipv4ToInt(ip);
        const networkInt = this.ipv4ToInt(network);
        const mask = (0xffffffff << (32 - prefix)) >>> 0;
        return (ipInt & mask) === (networkInt & mask);
    }
    /**
     * Check if IPv6 is in CIDR range
     * @param ip IPv6 address
     * @param network Network address
     * @param prefix Prefix length
     * @returns boolean indicating if IP is in range
     */
    static isIPv6InCidrRange(ip, network, prefix) {
        // Simplified IPv6 CIDR check - in production, use a proper IPv6 library
        const normalizedIp = this.normalize(ip);
        const normalizedNetwork = this.normalize(network);
        if (!normalizedIp || !normalizedNetwork) {
            return false;
        }
        const ipHex = normalizedIp.replace(/:/g, '');
        const networkHex = normalizedNetwork.replace(/:/g, '');
        const prefixHexLength = Math.floor(prefix / 4);
        return ipHex.substring(0, prefixHexLength) === networkHex.substring(0, prefixHexLength);
    }
    /**
     * Convert IPv4 address to integer
     * @param ip IPv4 address string
     * @returns Integer representation
     */
    static ipv4ToInt(ip) {
        return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet, 10), 0) >>> 0;
    }
}
exports.IpAddressUtils = IpAddressUtils;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************