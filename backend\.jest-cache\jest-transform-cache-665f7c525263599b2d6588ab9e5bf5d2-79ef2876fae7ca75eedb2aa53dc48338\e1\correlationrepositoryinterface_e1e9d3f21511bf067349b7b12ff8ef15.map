{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\repositories\\correlation.repository.interface.ts", "mappings": "", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\repositories\\correlation.repository.interface.ts"], "sourcesContent": ["import { CorrelatedEvent, CorrelationType } from '../../entities/event/correlated-event.entity';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\nimport { BaseRepository } from '../../../../shared-kernel/domain/base-repository.interface';\r\n\r\n/**\r\n * Correlation Search Criteria\r\n */\r\nexport interface CorrelationSearchCriteria {\r\n  /** Correlation types */\r\n  correlationTypes?: CorrelationType[];\r\n  /** Confidence levels */\r\n  confidenceLevels?: ConfidenceLevel[];\r\n  /** Score range */\r\n  scoreRange?: {\r\n    min: number;\r\n    max: number;\r\n  };\r\n  /** Event count range */\r\n  eventCountRange?: {\r\n    min: number;\r\n    max: number;\r\n  };\r\n  /** Primary event IDs */\r\n  primaryEventIds?: string[];\r\n  /** Related event IDs */\r\n  relatedEventIds?: string[];\r\n  /** Any event IDs (primary or related) */\r\n  anyEventIds?: string[];\r\n  /** Has attack chain */\r\n  hasAttackChain?: boolean;\r\n  /** Has campaign attribution */\r\n  hasCampaignAttribution?: boolean;\r\n  /** Common indicator count range */\r\n  commonIndicatorCountRange?: {\r\n    min: number;\r\n    max: number;\r\n  };\r\n  /** Correlation date range */\r\n  correlationDateRange?: {\r\n    from: Date;\r\n    to: Date;\r\n  };\r\n  /** Processing duration range (ms) */\r\n  processingDurationRange?: {\r\n    min: number;\r\n    max: number;\r\n  };\r\n  /** Engine version */\r\n  engineVersion?: string;\r\n  /** Algorithms used */\r\n  algorithmsUsed?: string[];\r\n  /** Has errors */\r\n  hasErrors?: boolean;\r\n  /** Threat escalation priority */\r\n  threatEscalationPriority?: ('low' | 'medium' | 'high' | 'critical')[];\r\n  /** Pagination */\r\n  pagination?: {\r\n    page: number;\r\n    limit: number;\r\n    sortBy?: string;\r\n    sortOrder?: 'asc' | 'desc';\r\n  };\r\n}\r\n\r\n/**\r\n * Correlation Statistics\r\n */\r\nexport interface CorrelationStatistics {\r\n  /** Total correlations */\r\n  total: number;\r\n  /** Type distribution */\r\n  typeDistribution: Record<CorrelationType, number>;\r\n  /** Confidence distribution */\r\n  confidenceDistribution: Record<ConfidenceLevel, number>;\r\n  /** Score statistics */\r\n  scoreStatistics: {\r\n    average: number;\r\n    median: number;\r\n    min: number;\r\n    max: number;\r\n    standardDeviation: number;\r\n  };\r\n  /** Performance metrics */\r\n  performanceMetrics: {\r\n    averageProcessingDuration: number;\r\n    averageEventCount: number;\r\n    averageCommonIndicators: number;\r\n    successRate: number;\r\n    errorRate: number;\r\n  };\r\n  /** Pattern analysis */\r\n  patternAnalysis: {\r\n    attackChainRate: number;\r\n    campaignAttributionRate: number;\r\n    coordinatedAttackRate: number;\r\n    falsePositiveRate: number;\r\n  };\r\n  /** Trends */\r\n  trends: {\r\n    daily: Array<{ date: string; count: number; avgScore: number }>;\r\n    weekly: Array<{ week: string; count: number; avgScore: number }>;\r\n    monthly: Array<{ month: string; count: number; avgScore: number }>;\r\n  };\r\n}\r\n\r\n/**\r\n * Correlation Performance Metrics\r\n */\r\nexport interface CorrelationPerformanceMetrics {\r\n  /** Processing performance */\r\n  processing: {\r\n    averageDuration: number;\r\n    medianDuration: number;\r\n    p95Duration: number;\r\n    p99Duration: number;\r\n    throughput: number;\r\n    errorRate: number;\r\n  };\r\n  /** Quality metrics */\r\n  quality: {\r\n    averageConfidence: number;\r\n    averageScore: number;\r\n    highConfidenceRate: number;\r\n    highScoreRate: number;\r\n    falsePositiveRate: number;\r\n    truePositiveRate: number;\r\n  };\r\n  /** Algorithm performance */\r\n  algorithmPerformance: Record<string, {\r\n    usage: number;\r\n    averageDuration: number;\r\n    successRate: number;\r\n    averageScore: number;\r\n  }>;\r\n  /** Type effectiveness */\r\n  typeEffectiveness: Record<CorrelationType, {\r\n    count: number;\r\n    averageScore: number;\r\n    averageConfidence: number;\r\n    threatEscalationRate: number;\r\n  }>;\r\n}\r\n\r\n/**\r\n * Correlation Repository Interface\r\n * \r\n * Defines the contract for correlation data persistence and retrieval.\r\n * Supports complex querying, performance analytics, and pattern analysis.\r\n * \r\n * Key responsibilities:\r\n * - Correlation CRUD operations\r\n * - Complex search and filtering\r\n * - Performance metrics and analytics\r\n * - Pattern recognition analysis\r\n * - Algorithm effectiveness tracking\r\n * - Quality assurance metrics\r\n */\r\nexport interface CorrelationRepository extends BaseRepository<CorrelatedEvent> {\r\n  /**\r\n   * Find correlations by search criteria\r\n   */\r\n  findByCriteria(criteria: CorrelationSearchCriteria): Promise<{\r\n    correlations: CorrelatedEvent[];\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n  }>;\r\n\r\n  /**\r\n   * Find correlations by type\r\n   */\r\n  findByType(type: CorrelationType): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find correlations by confidence level\r\n   */\r\n  findByConfidenceLevel(confidence: ConfidenceLevel): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find correlations by score range\r\n   */\r\n  findByScoreRange(minScore: number, maxScore: number): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find correlations involving event\r\n   */\r\n  findByEventId(eventId: string): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find correlations involving multiple events\r\n   */\r\n  findByEventIds(eventIds: string[]): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find correlations with attack chains\r\n   */\r\n  findWithAttackChains(): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find correlations with campaign attribution\r\n   */\r\n  findWithCampaignAttribution(): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find high-confidence correlations\r\n   */\r\n  findHighConfidenceCorrelations(): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find high-score correlations\r\n   */\r\n  findHighScoreCorrelations(threshold: number): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find correlations indicating coordinated attacks\r\n   */\r\n  findCoordinatedAttacks(): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find correlations requiring immediate attention\r\n   */\r\n  findRequiringImmediateAttention(): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find correlations by date range\r\n   */\r\n  findByDateRange(from: Date, to: Date): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find correlations by processing duration\r\n   */\r\n  findByProcessingDuration(minDuration: number, maxDuration: number): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find correlations with errors\r\n   */\r\n  findWithErrors(): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find correlations by algorithm\r\n   */\r\n  findByAlgorithm(algorithm: string): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Find correlations by engine version\r\n   */\r\n  findByEngineVersion(version: string): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Get correlation statistics\r\n   */\r\n  getStatistics(dateRange?: { from: Date; to: Date }): Promise<CorrelationStatistics>;\r\n\r\n  /**\r\n   * Get correlation performance metrics\r\n   */\r\n  getPerformanceMetrics(dateRange?: { from: Date; to: Date }): Promise<CorrelationPerformanceMetrics>;\r\n\r\n  /**\r\n   * Get correlation trends\r\n   */\r\n  getTrends(\r\n    period: 'daily' | 'weekly' | 'monthly',\r\n    dateRange: { from: Date; to: Date }\r\n  ): Promise<Array<{\r\n    date: string;\r\n    correlations: number;\r\n    averageScore: number;\r\n    averageConfidence: number;\r\n    attackChains: number;\r\n    campaignAttributions: number;\r\n  }>>;\r\n\r\n  /**\r\n   * Get algorithm effectiveness analysis\r\n   */\r\n  getAlgorithmEffectiveness(dateRange?: { from: Date; to: Date }): Promise<Record<string, {\r\n    totalUsage: number;\r\n    successRate: number;\r\n    averageScore: number;\r\n    averageProcessingTime: number;\r\n    errorRate: number;\r\n    recommendedUsage: 'high' | 'medium' | 'low';\r\n  }>>;\r\n\r\n  /**\r\n   * Get correlation type analysis\r\n   */\r\n  getTypeAnalysis(dateRange?: { from: Date; to: Date }): Promise<Record<CorrelationType, {\r\n    count: number;\r\n    averageScore: number;\r\n    averageConfidence: number;\r\n    averageEventCount: number;\r\n    threatEscalationRate: number;\r\n    falsePositiveRate: number;\r\n    effectiveness: 'high' | 'medium' | 'low';\r\n  }>>;\r\n\r\n  /**\r\n   * Get pattern recognition analysis\r\n   */\r\n  getPatternAnalysis(dateRange?: { from: Date; to: Date }): Promise<{\r\n    commonPatterns: Array<{\r\n      pattern: string;\r\n      frequency: number;\r\n      averageScore: number;\r\n      threatRelevance: number;\r\n    }>;\r\n    emergingPatterns: Array<{\r\n      pattern: string;\r\n      growthRate: number;\r\n      riskLevel: 'low' | 'medium' | 'high' | 'critical';\r\n    }>;\r\n    patternEffectiveness: Record<string, {\r\n      detectionRate: number;\r\n      falsePositiveRate: number;\r\n      threatAccuracy: number;\r\n    }>;\r\n  }>;\r\n\r\n  /**\r\n   * Get quality metrics\r\n   */\r\n  getQualityMetrics(dateRange?: { from: Date; to: Date }): Promise<{\r\n    overallQuality: number;\r\n    confidenceAccuracy: number;\r\n    scoreReliability: number;\r\n    falsePositiveRate: number;\r\n    truePositiveRate: number;\r\n    precisionRate: number;\r\n    recallRate: number;\r\n    f1Score: number;\r\n    qualityTrend: 'improving' | 'stable' | 'declining';\r\n  }>;\r\n\r\n  /**\r\n   * Get threat escalation analysis\r\n   */\r\n  getThreatEscalationAnalysis(dateRange?: { from: Date; to: Date }): Promise<{\r\n    escalationRate: number;\r\n    escalationsByPriority: Record<string, number>;\r\n    escalationsByType: Record<CorrelationType, number>;\r\n    averageEscalationTime: number;\r\n    escalationAccuracy: number;\r\n    missedThreats: number;\r\n    falseEscalations: number;\r\n  }>;\r\n\r\n  /**\r\n   * Get campaign attribution analysis\r\n   */\r\n  getCampaignAttributionAnalysis(dateRange?: { from: Date; to: Date }): Promise<{\r\n    attributionRate: number;\r\n    attributionAccuracy: number;\r\n    campaignsIdentified: number;\r\n    threatActorsIdentified: number;\r\n    attributionsByConfidence: Record<ConfidenceLevel, number>;\r\n    topCampaigns: Array<{\r\n      campaignId: string;\r\n      correlationCount: number;\r\n      averageConfidence: number;\r\n      threatLevel: string;\r\n    }>;\r\n  }>;\r\n\r\n  /**\r\n   * Get attack chain analysis\r\n   */\r\n  getAttackChainAnalysis(dateRange?: { from: Date; to: Date }): Promise<{\r\n    chainDetectionRate: number;\r\n    averageChainLength: number;\r\n    chainCompleteness: number;\r\n    chainAccuracy: number;\r\n    commonTechniques: Array<{\r\n      technique: string;\r\n      frequency: number;\r\n      chainPosition: number;\r\n    }>;\r\n    killChainCoverage: Record<string, number>;\r\n  }>;\r\n\r\n  /**\r\n   * Search correlations with full-text search\r\n   */\r\n  searchFullText(\r\n    query: string,\r\n    filters?: Partial<CorrelationSearchCriteria>\r\n  ): Promise<{\r\n    correlations: CorrelatedEvent[];\r\n    total: number;\r\n    highlights: Record<string, string[]>;\r\n  }>;\r\n\r\n  /**\r\n   * Find similar correlations\r\n   */\r\n  findSimilarCorrelations(\r\n    correlation: CorrelatedEvent,\r\n    similarity: 'events' | 'patterns' | 'indicators' | 'techniques'\r\n  ): Promise<CorrelatedEvent[]>;\r\n\r\n  /**\r\n   * Bulk update correlation confidence\r\n   */\r\n  bulkUpdateConfidence(\r\n    correlationIds: string[],\r\n    confidence: ConfidenceLevel,\r\n    reason: string,\r\n    updatedBy: string\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Bulk mark as false positive\r\n   */\r\n  bulkMarkFalsePositive(\r\n    correlationIds: string[],\r\n    reason: string,\r\n    markedBy: string\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Get correlation dependencies\r\n   */\r\n  getCorrelationDependencies(correlationId: string): Promise<{\r\n    relatedCorrelations: CorrelatedEvent[];\r\n    triggeredIncidents: string[];\r\n    triggeredThreats: string[];\r\n    followupActions: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Archive old correlations\r\n   */\r\n  archiveOldCorrelations(\r\n    olderThan: Date,\r\n    lowScoreThreshold: number\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Get correlation health metrics\r\n   */\r\n  getHealthMetrics(): Promise<{\r\n    processingRate: number;\r\n    errorRate: number;\r\n    averageQuality: number;\r\n    backlogSize: number;\r\n    falsePositiveRate: number;\r\n    threatDetectionRate: number;\r\n    systemLoad: number;\r\n    algorithmHealth: Record<string, 'healthy' | 'degraded' | 'failed'>;\r\n  }>;\r\n\r\n  /**\r\n   * Export correlations for analysis\r\n   */\r\n  exportCorrelations(\r\n    criteria: CorrelationSearchCriteria,\r\n    format: 'csv' | 'json' | 'xml'\r\n  ): Promise<{\r\n    data: Buffer | string;\r\n    filename: string;\r\n    contentType: string;\r\n  }>;\r\n\r\n  /**\r\n   * Get correlation forecast\r\n   */\r\n  getForecast(\r\n    period: 'daily' | 'weekly' | 'monthly'\r\n  ): Promise<{\r\n    expectedCorrelations: number;\r\n    expectedAttackChains: number;\r\n    expectedCampaignAttributions: number;\r\n    resourceRequirements: {\r\n      processingCapacity: number;\r\n      storageRequirements: number;\r\n      analysisTime: number;\r\n    };\r\n    qualityTrend: 'improving' | 'stable' | 'declining';\r\n    recommendations: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Validate correlation data integrity\r\n   */\r\n  validateDataIntegrity(): Promise<{\r\n    isValid: boolean;\r\n    issues: Array<{\r\n      type: string;\r\n      description: string;\r\n      affectedRecords: number;\r\n      severity: 'low' | 'medium' | 'high' | 'critical';\r\n    }>;\r\n  }>;\r\n\r\n  /**\r\n   * Optimize correlation storage\r\n   */\r\n  optimizeStorage(): Promise<{\r\n    spaceReclaimed: number;\r\n    recordsArchived: number;\r\n    indexesOptimized: number;\r\n    performanceImprovement: number;\r\n  }>;\r\n}\r\n"], "version": 3}