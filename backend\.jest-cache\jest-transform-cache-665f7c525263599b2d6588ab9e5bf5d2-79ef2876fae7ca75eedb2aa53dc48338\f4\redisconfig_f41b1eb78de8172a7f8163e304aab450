e2ac840c6562efbbdfb3a15ab6eee1b3
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.redisConfig = void 0;
const config_1 = require("@nestjs/config");
/**
 * Environment variable parsing utilities with improved type safety
 */
const parseEnv = {
    int: (value, defaultValue) => value ? parseInt(value, 10) || defaultValue : defaultValue,
    bool: (value) => value === 'true',
    string: (value, defaultValue) => value || defaultValue,
    optionalString: (value) => value || undefined,
    serializer: (value) => value === 'msgpack' ? 'msgpack' : 'json',
};
/**
 * Parse Redis nodes from environment string
 */
const parseNodes = (envValue) => {
    if (!envValue)
        return [];
    return envValue.split(',')
        .map(node => node.trim())
        .filter(Boolean)
        .map(node => {
        const [host, portStr] = node.split(':');
        if (!host)
            throw new Error(`Invalid Redis node format: ${node}`);
        return {
            host,
            port: parseEnv.int(portStr, 6379),
        };
    });
};
/**
 * Redis configuration constants
 */
const REDIS_DEFAULTS = {
    PORT: 6379,
    DB: 0,
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000,
    CONNECT_TIMEOUT: 10000,
    COMMAND_TIMEOUT: 5000,
    KEEP_ALIVE: 30000,
    TTL: 3600,
    CACHE_MAX_ITEMS: 10000,
    SESSION_TTL: 86400,
    SESSION_TOUCH_AFTER: 3600,
    RATE_LIMIT_WINDOW: 60000,
    RATE_LIMIT_MAX: 100,
    QUEUE_REMOVE_COMPLETE: 100,
    QUEUE_REMOVE_FAIL: 50,
    QUEUE_ATTEMPTS: 3,
    QUEUE_BACKOFF_DELAY: 2000,
    PUBSUB_MAX_LISTENERS: 100,
    HEALTH_CHECK_INTERVAL: 30000,
    HEALTH_CHECK_TIMEOUT: 5000,
    SLOW_LOG_THRESHOLD: 10000,
};
/**
 * Redis configuration factory with comprehensive type safety
 * Supports single instance, cluster, and sentinel configurations
 */
exports.redisConfig = (0, config_1.registerAs)('redis', () => {
    const env = process.env;
    return {
        primary: {
            host: parseEnv.string(env['REDIS_HOST'], 'localhost'),
            port: parseEnv.int(env['REDIS_PORT'], REDIS_DEFAULTS.PORT),
            password: parseEnv.optionalString(env['REDIS_PASSWORD']),
            db: parseEnv.int(env['REDIS_DB'], REDIS_DEFAULTS.DB),
            keyPrefix: parseEnv.string(env['REDIS_KEY_PREFIX'], 'sentinel:'),
        },
        connection: {
            maxRetriesPerRequest: parseEnv.int(env['REDIS_MAX_RETRIES'], REDIS_DEFAULTS.MAX_RETRIES),
            retryDelayOnFailover: parseEnv.int(env['REDIS_RETRY_DELAY'], REDIS_DEFAULTS.RETRY_DELAY),
            connectTimeout: parseEnv.int(env['REDIS_CONNECT_TIMEOUT'], REDIS_DEFAULTS.CONNECT_TIMEOUT),
            commandTimeout: parseEnv.int(env['REDIS_COMMAND_TIMEOUT'], REDIS_DEFAULTS.COMMAND_TIMEOUT),
            lazyConnect: true,
            keepAlive: REDIS_DEFAULTS.KEEP_ALIVE,
            family: 4,
        },
        cache: {
            ttl: parseEnv.int(env['REDIS_TTL'], REDIS_DEFAULTS.TTL),
            maxItems: parseEnv.int(env['REDIS_CACHE_MAX_ITEMS'], REDIS_DEFAULTS.CACHE_MAX_ITEMS),
            compression: parseEnv.bool(env['REDIS_COMPRESSION']),
            serializer: parseEnv.serializer(env['REDIS_SERIALIZER']),
        },
        session: {
            db: parseEnv.int(env['REDIS_SESSION_DB'], 1),
            keyPrefix: parseEnv.string(env['REDIS_SESSION_PREFIX'], 'sess:'),
            ttl: parseEnv.int(env['REDIS_SESSION_TTL'], REDIS_DEFAULTS.SESSION_TTL),
            touchAfter: parseEnv.int(env['REDIS_SESSION_TOUCH_AFTER'], REDIS_DEFAULTS.SESSION_TOUCH_AFTER),
        },
        rateLimit: {
            db: parseEnv.int(env['REDIS_RATE_LIMIT_DB'], 2),
            keyPrefix: parseEnv.string(env['REDIS_RATE_LIMIT_PREFIX'], 'rl:'),
            windowMs: parseEnv.int(env['REDIS_RATE_LIMIT_WINDOW'], REDIS_DEFAULTS.RATE_LIMIT_WINDOW),
            max: parseEnv.int(env['REDIS_RATE_LIMIT_MAX'], REDIS_DEFAULTS.RATE_LIMIT_MAX),
        },
        queue: {
            db: parseEnv.int(env['REDIS_QUEUE_DB'], 3),
            keyPrefix: parseEnv.string(env['REDIS_QUEUE_PREFIX'], 'bull:'),
            defaultJobOptions: {
                removeOnComplete: parseEnv.int(env['REDIS_QUEUE_REMOVE_ON_COMPLETE'], REDIS_DEFAULTS.QUEUE_REMOVE_COMPLETE),
                removeOnFail: parseEnv.int(env['REDIS_QUEUE_REMOVE_ON_FAIL'], REDIS_DEFAULTS.QUEUE_REMOVE_FAIL),
                attempts: parseEnv.int(env['REDIS_QUEUE_ATTEMPTS'], REDIS_DEFAULTS.QUEUE_ATTEMPTS),
                backoff: {
                    type: 'exponential',
                    delay: parseEnv.int(env['REDIS_QUEUE_BACKOFF_DELAY'], REDIS_DEFAULTS.QUEUE_BACKOFF_DELAY),
                },
            },
        },
        pubsub: {
            db: parseEnv.int(env['REDIS_PUBSUB_DB'], 4),
            keyPrefix: parseEnv.string(env['REDIS_PUBSUB_PREFIX'], 'ps:'),
            maxListeners: parseEnv.int(env['REDIS_PUBSUB_MAX_LISTENERS'], REDIS_DEFAULTS.PUBSUB_MAX_LISTENERS),
        },
        cluster: {
            enabled: parseEnv.bool(env['REDIS_CLUSTER_ENABLED']),
            nodes: parseNodes(env['REDIS_CLUSTER_NODES']),
            options: {
                enableReadyCheck: true,
                redisOptions: {
                    password: parseEnv.optionalString(env['REDIS_PASSWORD']),
                },
                maxRetriesPerRequest: REDIS_DEFAULTS.MAX_RETRIES,
                retryDelayOnFailover: REDIS_DEFAULTS.RETRY_DELAY,
            },
        },
        sentinel: {
            enabled: parseEnv.bool(env['REDIS_SENTINEL_ENABLED']),
            sentinels: parseNodes(env['REDIS_SENTINELS']),
            name: parseEnv.string(env['REDIS_SENTINEL_NAME'], 'mymaster'),
            password: parseEnv.optionalString(env['REDIS_SENTINEL_PASSWORD']),
        },
        healthCheck: {
            enabled: parseEnv.bool(env['REDIS_HEALTH_CHECK_ENABLED']),
            interval: parseEnv.int(env['REDIS_HEALTH_CHECK_INTERVAL'], REDIS_DEFAULTS.HEALTH_CHECK_INTERVAL),
            timeout: parseEnv.int(env['REDIS_HEALTH_CHECK_TIMEOUT'], REDIS_DEFAULTS.HEALTH_CHECK_TIMEOUT),
        },
        monitoring: {
            enabled: parseEnv.bool(env['REDIS_MONITORING_ENABLED']),
            slowLogEnabled: parseEnv.bool(env['REDIS_SLOW_LOG_ENABLED']),
            slowLogThreshold: parseEnv.int(env['REDIS_SLOW_LOG_THRESHOLD'], REDIS_DEFAULTS.SLOW_LOG_THRESHOLD),
            commandStatsEnabled: parseEnv.bool(env['REDIS_COMMAND_STATS_ENABLED']),
        },
    };
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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