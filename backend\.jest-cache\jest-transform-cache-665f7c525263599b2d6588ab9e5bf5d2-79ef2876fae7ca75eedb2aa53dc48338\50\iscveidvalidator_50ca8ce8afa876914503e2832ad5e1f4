d932c89ebb0ba85808183001144ac0f1
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CveIdUtils = exports.IsCveIdConstraint = void 0;
exports.IsCveId = IsCveId;
exports.IsCveIdArray = IsCveIdArray;
const class_validator_1 = require("class-validator");
/**
 * CVE ID format validator constraint
 * Validates CVE identifiers according to MITRE CVE format
 */
let IsCveIdConstraint = class IsCveIdConstraint {
    /**
     * Validate CVE ID format
     * @param value Value to validate
     * @param args Validation arguments
     * @returns boolean indicating if value is valid CVE ID
     */
    validate(value, args) {
        if (typeof value !== 'string') {
            return false;
        }
        // CVE ID format: CVE-YYYY-NNNN or CVE-YYYY-NNNNN+ (where N is a digit)
        // Examples: CVE-2021-1234, CVE-2023-12345, CVE-2024-123456
        const cvePattern = /^CVE-\d{4}-\d{4,}$/;
        if (!cvePattern.test(value)) {
            return false;
        }
        // Extract year and validate it's reasonable
        const yearMatch = value.match(/CVE-(\d{4})-/);
        if (yearMatch) {
            const year = parseInt(yearMatch[1], 10);
            const currentYear = new Date().getFullYear();
            // CVE years should be between 1999 (first CVE year) and current year + 1
            if (year < 1999 || year > currentYear + 1) {
                return false;
            }
        }
        return true;
    }
    /**
     * Default error message for CVE ID validation
     * @param args Validation arguments
     * @returns Error message
     */
    defaultMessage(args) {
        return `${args.property} must be a valid CVE identifier (format: CVE-YYYY-NNNN)`;
    }
};
exports.IsCveIdConstraint = IsCveIdConstraint;
exports.IsCveIdConstraint = IsCveIdConstraint = __decorate([
    (0, class_validator_1.ValidatorConstraint)({ name: 'isCveId', async: false })
], IsCveIdConstraint);
/**
 * CVE ID validation decorator
 * @param validationOptions Validation options
 * @returns Property decorator
 */
function IsCveId(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [],
            validator: IsCveIdConstraint,
        });
    };
}
/**
 * CVE ID array validation decorator
 * Validates an array of CVE IDs
 * @param validationOptions Validation options
 * @returns Property decorator
 */
function IsCveIdArray(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            name: 'isCveIdArray',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [],
            validator: {
                validate(value, args) {
                    if (!Array.isArray(value)) {
                        return false;
                    }
                    const constraint = new IsCveIdConstraint();
                    return value.every(item => constraint.validate(item, args));
                },
                defaultMessage(args) {
                    return `${args.property} must be an array of valid CVE identifiers`;
                },
            },
        });
    };
}
/**
 * Utility functions for CVE ID handling
 */
class CveIdUtils {
    /**
     * Extract year from CVE ID
     * @param cveId CVE identifier
     * @returns Year or null if invalid
     */
    static extractYear(cveId) {
        const match = cveId.match(/^CVE-(\d{4})-\d{4,}$/);
        return match ? parseInt(match[1], 10) : null;
    }
    /**
     * Extract sequence number from CVE ID
     * @param cveId CVE identifier
     * @returns Sequence number or null if invalid
     */
    static extractSequenceNumber(cveId) {
        const match = cveId.match(/^CVE-\d{4}-(\d{4,})$/);
        return match ? parseInt(match[1], 10) : null;
    }
    /**
     * Generate CVE ID from components
     * @param year Year
     * @param sequenceNumber Sequence number
     * @returns CVE ID string
     */
    static generateCveId(year, sequenceNumber) {
        const paddedSequence = sequenceNumber.toString().padStart(4, '0');
        return `CVE-${year}-${paddedSequence}`;
    }
    /**
     * Validate CVE ID format without decorator
     * @param cveId CVE identifier to validate
     * @returns boolean indicating validity
     */
    static isValid(cveId) {
        const constraint = new IsCveIdConstraint();
        return constraint.validate(cveId, {});
    }
    /**
     * Normalize CVE ID format
     * @param cveId CVE identifier
     * @returns Normalized CVE ID or null if invalid
     */
    static normalize(cveId) {
        if (typeof cveId !== 'string') {
            return null;
        }
        const upperCaseId = cveId.toUpperCase();
        if (!this.isValid(upperCaseId)) {
            return null;
        }
        return upperCaseId;
    }
}
exports.CveIdUtils = CveIdUtils;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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