b93d994b65c3b50d3e53cfa75131139f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const vulnerability_analysis_service_1 = require("./vulnerability-analysis.service");
const analysis_job_entity_1 = require("../../domain/entities/analysis-job.entity");
const prediction_entity_1 = require("../../domain/entities/prediction.entity");
const model_configuration_entity_1 = require("../../domain/entities/model-configuration.entity");
const ai_service_provider_service_1 = require("../../infrastructure/services/ai-service-provider.service");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
describe('VulnerabilityAnalysisService', () => {
    let service;
    let analysisJobRepository;
    let predictionRepository;
    let modelConfigRepository;
    let analysisQueue;
    let aiServiceProvider;
    let loggerService;
    let auditService;
    const mockAnalysisJobRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findOne: jest.fn(),
        find: jest.fn(),
        remove: jest.fn(),
    };
    const mockPredictionRepository = {
        find: jest.fn(),
    };
    const mockModelConfigRepository = {
        findOne: jest.fn(),
    };
    const mockAnalysisQueue = {
        add: jest.fn(),
    };
    const mockAIServiceProvider = {
        predict: jest.fn(),
        batchPredict: jest.fn(),
    };
    const mockLoggerService = {
        debug: jest.fn(),
        log: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
    };
    const mockAuditService = {
        logUserAction: jest.fn(),
    };
    const mockModelConfiguration = {
        id: 'model-123',
        name: 'vulnerability_scanner_v1',
        displayName: 'Vulnerability Scanner v1.0',
        type: 'vulnerability_scanner',
        provider: 'openai',
        version: 'gpt-4-1106-preview',
        status: 'active',
        isDefault: true,
    };
    const mockAnalysisJob = {
        id: 'job-123',
        type: 'vulnerability_analysis',
        title: 'Vulnerability Severity Analysis - CVE-2023-12345',
        status: 'pending',
        priority: 'high',
        progress: 0,
        createdBy: 'user-123',
        markAsStarted: jest.fn(),
        markAsCompleted: jest.fn(),
        markAsFailed: jest.fn(),
        updateProgress: jest.fn(),
        addResourceUsage: jest.fn(),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                vulnerability_analysis_service_1.VulnerabilityAnalysisService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(analysis_job_entity_1.AnalysisJob),
                    useValue: mockAnalysisJobRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(prediction_entity_1.Prediction),
                    useValue: mockPredictionRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(model_configuration_entity_1.ModelConfiguration),
                    useValue: mockModelConfigRepository,
                },
                {
                    provide: (0, typeorm_1.getQueueToken)('ai-analysis'),
                    useValue: mockAnalysisQueue,
                },
                {
                    provide: ai_service_provider_service_1.AIServiceProvider,
                    useValue: mockAIServiceProvider,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: mockLoggerService,
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: mockAuditService,
                },
            ],
        }).compile();
        service = module.get(vulnerability_analysis_service_1.VulnerabilityAnalysisService);
        analysisJobRepository = module.get((0, typeorm_1.getRepositoryToken)(analysis_job_entity_1.AnalysisJob));
        predictionRepository = module.get((0, typeorm_1.getRepositoryToken)(prediction_entity_1.Prediction));
        modelConfigRepository = module.get((0, typeorm_1.getRepositoryToken)(model_configuration_entity_1.ModelConfiguration));
        analysisQueue = module.get((0, typeorm_1.getQueueToken)('ai-analysis'));
        aiServiceProvider = module.get(ai_service_provider_service_1.AIServiceProvider);
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('analyzeSeverity', () => {
        it('should create vulnerability severity analysis job successfully', async () => {
            const vulnerabilityData = {
                cveId: 'CVE-2023-12345',
                description: 'Test vulnerability',
                cvssScore: 7.5,
                affectedSoftware: 'Test Software',
                exploitAvailable: true,
                patchAvailable: false,
            };
            const userId = 'user-123';
            mockModelConfigRepository.findOne.mockResolvedValue(mockModelConfiguration);
            mockAnalysisJobRepository.create.mockReturnValue(mockAnalysisJob);
            mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);
            mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });
            const result = await service.analyzeSeverity(vulnerabilityData, userId);
            expect(mockModelConfigRepository.findOne).toHaveBeenCalledWith({
                where: {
                    type: 'vulnerability_scanner',
                    status: 'active',
                    isDefault: true,
                },
            });
            expect(mockAnalysisJobRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                type: 'vulnerability_analysis',
                title: 'Vulnerability Severity Analysis - CVE-2023-12345',
                status: 'pending',
                priority: 'high', // Based on CVSS score and exploit availability
                inputData: vulnerabilityData,
                modelConfigurationId: mockModelConfiguration.id,
                createdBy: userId,
            }));
            expect(mockAnalysisJobRepository.save).toHaveBeenCalledWith(mockAnalysisJob);
            expect(mockAnalysisQueue.add).toHaveBeenCalledWith('analyze-vulnerability-severity', {
                jobId: mockAnalysisJob.id,
                vulnerabilityData,
                modelId: mockModelConfiguration.id,
            }, expect.objectContaining({
                priority: 2, // High priority
                attempts: 3,
                backoff: 'exponential',
            }));
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'create', 'analysis_job', mockAnalysisJob.id, expect.objectContaining({
                type: 'vulnerability_analysis',
                cveId: vulnerabilityData.cveId,
                modelId: mockModelConfiguration.id,
            }));
            expect(result).toEqual(mockAnalysisJob);
        });
        it('should throw error when no default model is found', async () => {
            const vulnerabilityData = {
                description: 'Test vulnerability',
                cvssScore: 5.0,
            };
            const userId = 'user-123';
            mockModelConfigRepository.findOne.mockResolvedValue(null);
            await expect(service.analyzeSeverity(vulnerabilityData, userId))
                .rejects.toThrow('No default model found for type: vulnerability_scanner');
        });
        it('should determine correct priority based on vulnerability data', async () => {
            const testCases = [
                {
                    data: { cvssScore: 9.5, exploitAvailable: true },
                    expectedPriority: 'urgent',
                },
                {
                    data: { cvssScore: 8.0, exploitAvailable: false },
                    expectedPriority: 'high',
                },
                {
                    data: { cvssScore: 5.0, exploitAvailable: false },
                    expectedPriority: 'normal',
                },
                {
                    data: { cvssScore: 2.0, exploitAvailable: false },
                    expectedPriority: 'low',
                },
            ];
            mockModelConfigRepository.findOne.mockResolvedValue(mockModelConfiguration);
            mockAnalysisJobRepository.create.mockReturnValue(mockAnalysisJob);
            mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);
            mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });
            for (const testCase of testCases) {
                const vulnerabilityData = {
                    description: 'Test vulnerability',
                    ...testCase.data,
                };
                await service.analyzeSeverity(vulnerabilityData, 'user-123');
                expect(mockAnalysisJobRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                    priority: testCase.expectedPriority,
                }));
                jest.clearAllMocks();
                mockModelConfigRepository.findOne.mockResolvedValue(mockModelConfiguration);
                mockAnalysisJobRepository.create.mockReturnValue(mockAnalysisJob);
                mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);
                mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });
            }
        });
    });
    describe('analyzeExploitProbability', () => {
        it('should create exploit probability analysis job successfully', async () => {
            const vulnerabilityData = {
                cveId: 'CVE-2023-12345',
                description: 'Test vulnerability',
                cvssScore: 7.5,
                attackVector: 'network',
                attackComplexity: 'low',
                privilegesRequired: 'none',
                userInteraction: 'none',
                publicExploits: true,
                ageInDays: 30,
            };
            const userId = 'user-123';
            mockModelConfigRepository.findOne.mockResolvedValue({
                ...mockModelConfiguration,
                type: 'threat_classifier',
            });
            mockAnalysisJobRepository.create.mockReturnValue(mockAnalysisJob);
            mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);
            mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });
            const result = await service.analyzeExploitProbability(vulnerabilityData, userId);
            expect(mockModelConfigRepository.findOne).toHaveBeenCalledWith({
                where: {
                    type: 'threat_classifier',
                    status: 'active',
                    isDefault: true,
                },
            });
            expect(mockAnalysisJobRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                type: 'vulnerability_analysis',
                title: 'Exploit Probability Analysis - CVE-2023-12345',
                inputData: vulnerabilityData,
            }));
            expect(mockAnalysisQueue.add).toHaveBeenCalledWith('analyze-exploit-probability', expect.objectContaining({
                jobId: mockAnalysisJob.id,
                vulnerabilityData,
            }), expect.any(Object));
            expect(result).toEqual(mockAnalysisJob);
        });
    });
    describe('generateRemediationRecommendations', () => {
        it('should create remediation recommendations job successfully', async () => {
            const vulnerabilityData = {
                cveId: 'CVE-2023-12345',
                description: 'Test vulnerability',
                affectedSoftware: 'Test Software',
                version: '1.0.0',
                severity: 'high',
                exploitAvailable: false,
                patchAvailable: true,
            };
            const environmentContext = {
                operatingSystem: 'Ubuntu 20.04',
                architecture: 'x86_64',
                businessCriticality: 'high',
                maintenanceWindow: 'weekends 02:00-06:00 UTC',
                complianceRequirements: ['PCI-DSS', 'HIPAA'],
            };
            const userId = 'user-123';
            mockModelConfigRepository.findOne.mockResolvedValue({
                ...mockModelConfiguration,
                type: 'nlp_processor',
            });
            mockAnalysisJobRepository.create.mockReturnValue(mockAnalysisJob);
            mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);
            mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });
            const result = await service.generateRemediationRecommendations(vulnerabilityData, environmentContext, userId);
            expect(mockModelConfigRepository.findOne).toHaveBeenCalledWith({
                where: {
                    type: 'nlp_processor',
                    status: 'active',
                    isDefault: true,
                },
            });
            expect(mockAnalysisJobRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                type: 'vulnerability_analysis',
                title: 'Remediation Recommendations - CVE-2023-12345',
                inputData: {
                    vulnerability: vulnerabilityData,
                    environment: environmentContext,
                },
            }));
            expect(mockAnalysisQueue.add).toHaveBeenCalledWith('generate-remediation-recommendations', expect.objectContaining({
                jobId: mockAnalysisJob.id,
                vulnerabilityData,
                environmentContext,
            }), expect.any(Object));
            expect(result).toEqual(mockAnalysisJob);
        });
    });
    describe('batchAnalyze', () => {
        it('should create batch analysis job successfully', async () => {
            const vulnerabilities = [
                {
                    id: 'vuln-1',
                    cveId: 'CVE-2023-12345',
                    description: 'Test vulnerability 1',
                    cvssScore: 7.5,
                },
                {
                    id: 'vuln-2',
                    cveId: 'CVE-2023-12346',
                    description: 'Test vulnerability 2',
                    cvssScore: 8.0,
                },
            ];
            const analysisType = 'severity';
            const userId = 'user-123';
            mockModelConfigRepository.findOne.mockResolvedValue(mockModelConfiguration);
            mockAnalysisJobRepository.create.mockReturnValue({
                ...mockAnalysisJob,
                totalStages: 1,
            });
            mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);
            mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });
            const result = await service.batchAnalyze(vulnerabilities, analysisType, userId);
            expect(mockAnalysisJobRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                type: 'vulnerability_analysis',
                title: 'Batch severity Analysis - 2 vulnerabilities',
                inputData: {
                    vulnerabilities,
                    analysisType,
                    batchSize: 2,
                },
                totalStages: 1,
            }));
            expect(mockAnalysisQueue.add).toHaveBeenCalledWith('batch-vulnerability-analysis', expect.objectContaining({
                jobId: mockAnalysisJob.id,
                vulnerabilities,
                analysisType,
            }), expect.any(Object));
            expect(result).toEqual(mockAnalysisJob);
        });
        it('should limit batch size to maximum of 10', async () => {
            const vulnerabilities = Array.from({ length: 15 }, (_, i) => ({
                id: `vuln-${i}`,
                description: `Test vulnerability ${i}`,
                cvssScore: 5.0,
            }));
            mockModelConfigRepository.findOne.mockResolvedValue(mockModelConfiguration);
            mockAnalysisJobRepository.create.mockReturnValue({
                ...mockAnalysisJob,
                totalStages: 2, // 15 vulnerabilities / 10 batch size = 2 stages
            });
            mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);
            mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });
            await service.batchAnalyze(vulnerabilities, 'severity', 'user-123');
            expect(mockAnalysisJobRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                inputData: expect.objectContaining({
                    batchSize: 10,
                }),
                totalStages: 2,
            }));
        });
    });
    describe('getAnalysisJob', () => {
        it('should return analysis job when found', async () => {
            const jobId = 'job-123';
            mockAnalysisJobRepository.findOne.mockResolvedValue(mockAnalysisJob);
            const result = await service.getAnalysisJob(jobId);
            expect(mockAnalysisJobRepository.findOne).toHaveBeenCalledWith({
                where: { id: jobId },
                relations: ['modelConfiguration'],
            });
            expect(result).toEqual(mockAnalysisJob);
        });
        it('should return null when job not found', async () => {
            const jobId = 'non-existent-job';
            mockAnalysisJobRepository.findOne.mockResolvedValue(null);
            const result = await service.getAnalysisJob(jobId);
            expect(result).toBeNull();
        });
    });
    describe('getJobPredictions', () => {
        it('should return predictions for analysis job', async () => {
            const jobId = 'job-123';
            const mockPredictions = [
                { id: 'pred-1', type: 'vulnerability_severity', confidence: 0.85 },
                { id: 'pred-2', type: 'exploit_probability', confidence: 0.72 },
            ];
            mockPredictionRepository.find.mockResolvedValue(mockPredictions);
            const result = await service.getJobPredictions(jobId);
            expect(mockPredictionRepository.find).toHaveBeenCalledWith({
                where: { analysisJobId: jobId },
                relations: ['modelConfiguration'],
                order: { createdAt: 'DESC' },
            });
            expect(result).toEqual(mockPredictions);
        });
    });
    describe('cancelAnalysisJob', () => {
        it('should cancel analysis job successfully', async () => {
            const jobId = 'job-123';
            const userId = 'user-123';
            const mockJob = {
                ...mockAnalysisJob,
                isTerminal: false,
                markAsCancelled: jest.fn(),
            };
            jest.spyOn(service, 'getAnalysisJob').mockResolvedValue(mockJob);
            mockAnalysisJobRepository.save.mockResolvedValue(mockJob);
            await service.cancelAnalysisJob(jobId, userId);
            expect(service.getAnalysisJob).toHaveBeenCalledWith(jobId);
            expect(mockJob.markAsCancelled).toHaveBeenCalledWith('Cancelled by user');
            expect(mockAnalysisJobRepository.save).toHaveBeenCalledWith(mockJob);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'cancel', 'analysis_job', jobId, { reason: 'user_request' });
        });
        it('should throw error when job not found', async () => {
            const jobId = 'non-existent-job';
            const userId = 'user-123';
            jest.spyOn(service, 'getAnalysisJob').mockResolvedValue(null);
            await expect(service.cancelAnalysisJob(jobId, userId))
                .rejects.toThrow('Analysis job not found');
        });
        it('should throw error when trying to cancel completed job', async () => {
            const jobId = 'job-123';
            const userId = 'user-123';
            const mockJob = {
                ...mockAnalysisJob,
                isTerminal: true,
            };
            jest.spyOn(service, 'getAnalysisJob').mockResolvedValue(mockJob);
            await expect(service.cancelAnalysisJob(jobId, userId))
                .rejects.toThrow('Cannot cancel completed job');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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