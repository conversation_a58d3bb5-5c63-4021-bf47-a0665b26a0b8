{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\unauthorized.exception.spec.ts", "mappings": ";;AAAA,oFAAgF;AAEhF,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,SAAS,GAAG,IAAI,8CAAqB,EAAE,CAAC;YAE9C,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,8CAAqB,CAAC,CAAC;YACxD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YACrF,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,SAAS,GAAG,IAAI,8CAAqB,CAAC,qBAAqB,EAAE;gBACjE,oBAAoB,EAAE,QAAQ;gBAC9B,cAAc,EAAE,KAAK;gBACrB,MAAM,EAAE,qBAAqB;gBAC7B,aAAa,EAAE,qBAAqB;aACrC,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACtD,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;gBAC5D,MAAM,SAAS,GAAG,8CAAqB,CAAC,qBAAqB,CAAC;oBAC5D,oBAAoB,EAAE,QAAQ;oBAC9B,aAAa,EAAE,SAAS;iBACzB,CAAC,CAAC;gBAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;gBAC1E,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACrD,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACtD,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,SAAS,GAAG,8CAAqB,CAAC,kBAAkB,CAAC,OAAO,EAAE;oBAClE,oBAAoB,EAAE,QAAQ;iBAC/B,CAAC,CAAC;gBAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBACzD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACrD,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC/C,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACxD,MAAM,SAAS,GAAG,8CAAqB,CAAC,kBAAkB,CAAC,KAAK,EAAE;oBAChE,cAAc;iBACf,CAAC,CAAC;gBAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACrD,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC7C,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpD,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACxD,MAAM,SAAS,GAAG,8CAAqB,CAAC,kBAAkB,CAAC,SAAS,EAAE;oBACpE,cAAc;iBACf,CAAC,CAAC;gBAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBAC3D,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACrD,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACjD,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpD,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;gBAC3D,MAAM,SAAS,GAAG,8CAAqB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;gBAEpE,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBACzD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBACvD,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC7C,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;gBACxD,MAAM,gBAAgB,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAC7C,MAAM,SAAS,GAAG,8CAAqB,CAAC,iBAAiB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;gBAEtF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,mFAAmF,CACpF,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACpD,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACtD,MAAM,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnD,MAAM,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;gBAChE,MAAM,SAAS,GAAG,8CAAqB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;gBAEpE,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;gBAClF,MAAM,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,SAAS,GAAG,8CAAqB,CAAC,qBAAqB,EAAE,CAAC;YAEhE,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,SAAS,GAAG,8CAAqB,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAEvE,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,SAAS,GAAG,8CAAqB,CAAC,qBAAqB,EAAE,CAAC;YAChE,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,SAAS,GAAG,8CAAqB,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACvE,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,SAAS,GAAG,8CAAqB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,SAAS,GAAG,8CAAqB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;QACxG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,SAAS,GAAG,8CAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACtE,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,SAAS,GAAG,8CAAqB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACpE,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,SAAS,GAAG,IAAI,8CAAqB,CAAC,cAAc,EAAE;gBAC1D,MAAM,EAAE,gBAAgB;aACzB,CAAC,CAAC;YACH,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,SAAS,GAAG,8CAAqB,CAAC,kBAAkB,CAAC,KAAK,EAAE;gBAChE,oBAAoB,EAAE,QAAQ;aAC/B,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;YAE3C,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBACvB,KAAK,EAAE,sCAAsC;gBAC7C,IAAI,EAAE,cAAc;gBACpB,OAAO,EAAE;oBACP,MAAM,EAAE,qBAAqB;oBAC7B,oBAAoB,EAAE,QAAQ;oBAC9B,cAAc,EAAE,KAAK;oBACrB,gBAAgB,EAAE,EAAE;iBACrB;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,8CAAqB,CAAC,kBAAkB,CAAC,KAAK,EAAE;gBAChE,oBAAoB,EAAE,QAAQ;gBAC9B,cAAc;aACf,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;gBACzB,IAAI,EAAE,uBAAuB;gBAC7B,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE,cAAc;gBACpB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,gBAAgB;gBAC1B,oBAAoB,EAAE,QAAQ;gBAC9B,cAAc,EAAE,KAAK;gBACrB,MAAM,EAAE,qBAAqB;gBAC7B,oBAAoB,EAAE,KAAK;gBAC3B,oBAAoB,EAAE,KAAK;gBAC3B,oBAAoB,EAAE,IAAI;gBAC1B,oBAAoB,EAAE,KAAK;gBAC3B,sBAAsB,EAAE,KAAK;gBAC7B,mBAAmB,EAAE,KAAK;gBAC1B,gBAAgB,EAAE,EAAE;gBACpB,cAAc,EAAE,cAAc,CAAC,WAAW,EAAE;aAC7C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,SAAS,GAAG,IAAI,8CAAqB,EAAE,CAAC;YAE9C,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,SAAS,GAAG,IAAI,8CAAqB,EAAE,CAAC;YAE9C,MAAM,CAAC,SAAS,YAAY,8CAAqB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,SAAS,YAAY,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,OAAO,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC;YAC/D,MAAM,aAAa,GAAG,iBAAiB,CAAC;YAExC,MAAM,SAAS,GAAG,IAAI,8CAAqB,CAAC,cAAc,EAAE;gBAC1D,OAAO;gBACP,aAAa;aACd,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\unauthorized.exception.spec.ts"], "sourcesContent": ["import { UnauthorizedException } from '../../exceptions/unauthorized.exception';\r\n\r\ndescribe('UnauthorizedException', () => {\r\n  describe('constructor', () => {\r\n    it('should create exception with default message', () => {\r\n      const exception = new UnauthorizedException();\r\n\r\n      expect(exception).toBeInstanceOf(UnauthorizedException);\r\n      expect(exception.message).toBe('Authentication is required to access this resource');\r\n      expect(exception.code).toBe('UNAUTHORIZED');\r\n      expect(exception.severity).toBe('medium');\r\n      expect(exception.category).toBe('authentication');\r\n    });\r\n\r\n    it('should create exception with custom message and options', () => {\r\n      const exception = new UnauthorizedException('Custom auth message', {\r\n        authenticationScheme: 'Bearer',\r\n        credentialType: 'JWT',\r\n        reason: 'invalid_credentials',\r\n        correlationId: 'test-correlation-id',\r\n      });\r\n\r\n      expect(exception.message).toBe('Custom auth message');\r\n      expect(exception.authenticationScheme).toBe('Bearer');\r\n      expect(exception.credentialType).toBe('JWT');\r\n      expect(exception.reason).toBe('invalid_credentials');\r\n      expect(exception.correlationId).toBe('test-correlation-id');\r\n    });\r\n  });\r\n\r\n  describe('static factory methods', () => {\r\n    describe('missingAuthentication', () => {\r\n      it('should create exception for missing authentication', () => {\r\n        const exception = UnauthorizedException.missingAuthentication({\r\n          authenticationScheme: 'Bearer',\r\n          correlationId: 'test-id',\r\n        });\r\n\r\n        expect(exception.message).toBe('Authentication credentials are required');\r\n        expect(exception.reason).toBe('missing_credentials');\r\n        expect(exception.authenticationScheme).toBe('Bearer');\r\n        expect(exception.isMissingCredentials()).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('invalidCredentials', () => {\r\n      it('should create exception for invalid credentials', () => {\r\n        const exception = UnauthorizedException.invalidCredentials('token', {\r\n          authenticationScheme: 'Bearer',\r\n        });\r\n\r\n        expect(exception.message).toBe('Invalid token provided');\r\n        expect(exception.reason).toBe('invalid_credentials');\r\n        expect(exception.credentialType).toBe('token');\r\n        expect(exception.isInvalidCredentials()).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('expiredCredentials', () => {\r\n      it('should create exception for expired credentials', () => {\r\n        const expirationTime = new Date('2023-01-01T00:00:00Z');\r\n        const exception = UnauthorizedException.expiredCredentials('JWT', {\r\n          expirationTime,\r\n        });\r\n\r\n        expect(exception.message).toBe('JWT has expired');\r\n        expect(exception.reason).toBe('expired_credentials');\r\n        expect(exception.credentialType).toBe('JWT');\r\n        expect(exception.isExpiredCredentials()).toBe(true);\r\n        expect(exception.getExpirationTime()).toEqual(expirationTime);\r\n      });\r\n    });\r\n\r\n    describe('revokedCredentials', () => {\r\n      it('should create exception for revoked credentials', () => {\r\n        const revocationTime = new Date('2023-01-01T00:00:00Z');\r\n        const exception = UnauthorizedException.revokedCredentials('API key', {\r\n          revocationTime,\r\n        });\r\n\r\n        expect(exception.message).toBe('API key has been revoked');\r\n        expect(exception.reason).toBe('revoked_credentials');\r\n        expect(exception.credentialType).toBe('API key');\r\n        expect(exception.isRevokedCredentials()).toBe(true);\r\n        expect(exception.getRevocationTime()).toEqual(revocationTime);\r\n      });\r\n    });\r\n\r\n    describe('malformedCredentials', () => {\r\n      it('should create exception for malformed credentials', () => {\r\n        const exception = UnauthorizedException.malformedCredentials('JWT');\r\n\r\n        expect(exception.message).toBe('Malformed JWT provided');\r\n        expect(exception.reason).toBe('malformed_credentials');\r\n        expect(exception.credentialType).toBe('JWT');\r\n        expect(exception.isMalformedCredentials()).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('unsupportedScheme', () => {\r\n      it('should create exception for unsupported scheme', () => {\r\n        const supportedSchemes = ['Bearer', 'Basic'];\r\n        const exception = UnauthorizedException.unsupportedScheme('Digest', supportedSchemes);\r\n\r\n        expect(exception.message).toBe(\r\n          \"Authentication scheme 'Digest' is not supported. Supported schemes: Bearer, Basic\"\r\n        );\r\n        expect(exception.reason).toBe('unsupported_scheme');\r\n        expect(exception.authenticationScheme).toBe('Digest');\r\n        expect(exception.isUnsupportedScheme()).toBe(true);\r\n        expect(exception.getSupportedSchemes()).toEqual(supportedSchemes);\r\n      });\r\n\r\n      it('should create exception without supported schemes list', () => {\r\n        const exception = UnauthorizedException.unsupportedScheme('Digest');\r\n\r\n        expect(exception.message).toBe(\"Authentication scheme 'Digest' is not supported\");\r\n        expect(exception.getSupportedSchemes()).toEqual([]);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('type checking methods', () => {\r\n    it('should correctly identify missing credentials', () => {\r\n      const exception = UnauthorizedException.missingAuthentication();\r\n      \r\n      expect(exception.isMissingCredentials()).toBe(true);\r\n      expect(exception.isInvalidCredentials()).toBe(false);\r\n      expect(exception.isExpiredCredentials()).toBe(false);\r\n      expect(exception.isRevokedCredentials()).toBe(false);\r\n      expect(exception.isMalformedCredentials()).toBe(false);\r\n      expect(exception.isUnsupportedScheme()).toBe(false);\r\n    });\r\n\r\n    it('should correctly identify invalid credentials', () => {\r\n      const exception = UnauthorizedException.invalidCredentials('password');\r\n      \r\n      expect(exception.isMissingCredentials()).toBe(false);\r\n      expect(exception.isInvalidCredentials()).toBe(true);\r\n      expect(exception.isExpiredCredentials()).toBe(false);\r\n      expect(exception.isRevokedCredentials()).toBe(false);\r\n      expect(exception.isMalformedCredentials()).toBe(false);\r\n      expect(exception.isUnsupportedScheme()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('getUserMessage', () => {\r\n    it('should return user-friendly message for missing credentials', () => {\r\n      const exception = UnauthorizedException.missingAuthentication();\r\n      expect(exception.getUserMessage()).toBe('Please provide valid authentication credentials');\r\n    });\r\n\r\n    it('should return user-friendly message for invalid credentials', () => {\r\n      const exception = UnauthorizedException.invalidCredentials('password');\r\n      expect(exception.getUserMessage()).toBe('The provided credentials are invalid');\r\n    });\r\n\r\n    it('should return user-friendly message for expired credentials', () => {\r\n      const exception = UnauthorizedException.expiredCredentials('token');\r\n      expect(exception.getUserMessage()).toBe('Your authentication has expired. Please sign in again');\r\n    });\r\n\r\n    it('should return user-friendly message for revoked credentials', () => {\r\n      const exception = UnauthorizedException.revokedCredentials('token');\r\n      expect(exception.getUserMessage()).toBe('Your authentication has been revoked. Please sign in again');\r\n    });\r\n\r\n    it('should return user-friendly message for malformed credentials', () => {\r\n      const exception = UnauthorizedException.malformedCredentials('token');\r\n      expect(exception.getUserMessage()).toBe('The authentication format is invalid');\r\n    });\r\n\r\n    it('should return user-friendly message for unsupported scheme', () => {\r\n      const exception = UnauthorizedException.unsupportedScheme('Digest');\r\n      expect(exception.getUserMessage()).toBe('The authentication method is not supported');\r\n    });\r\n\r\n    it('should return default message for unknown reason', () => {\r\n      const exception = new UnauthorizedException('Test message', {\r\n        reason: 'unknown_reason',\r\n      });\r\n      expect(exception.getUserMessage()).toBe('Authentication is required to access this resource');\r\n    });\r\n  });\r\n\r\n  describe('toApiResponse', () => {\r\n    it('should convert to API response format', () => {\r\n      const exception = UnauthorizedException.invalidCredentials('JWT', {\r\n        authenticationScheme: 'Bearer',\r\n      });\r\n\r\n      const response = exception.toApiResponse();\r\n\r\n      expect(response).toEqual({\r\n        error: 'The provided credentials are invalid',\r\n        code: 'UNAUTHORIZED',\r\n        details: {\r\n          reason: 'invalid_credentials',\r\n          authenticationScheme: 'Bearer',\r\n          credentialType: 'JWT',\r\n          supportedSchemes: [],\r\n        },\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('toJSON', () => {\r\n    it('should convert to JSON with detailed information', () => {\r\n      const expirationTime = new Date('2023-01-01T00:00:00Z');\r\n      const exception = UnauthorizedException.expiredCredentials('JWT', {\r\n        authenticationScheme: 'Bearer',\r\n        expirationTime,\r\n      });\r\n\r\n      const json = exception.toJSON();\r\n\r\n      expect(json).toMatchObject({\r\n        name: 'UnauthorizedException',\r\n        message: 'JWT has expired',\r\n        code: 'UNAUTHORIZED',\r\n        severity: 'medium',\r\n        category: 'authentication',\r\n        authenticationScheme: 'Bearer',\r\n        credentialType: 'JWT',\r\n        reason: 'expired_credentials',\r\n        isMissingCredentials: false,\r\n        isInvalidCredentials: false,\r\n        isExpiredCredentials: true,\r\n        isRevokedCredentials: false,\r\n        isMalformedCredentials: false,\r\n        isUnsupportedScheme: false,\r\n        supportedSchemes: [],\r\n        expirationTime: expirationTime.toISOString(),\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('inheritance', () => {\r\n    it('should be instance of Error and DomainException', () => {\r\n      const exception = new UnauthorizedException();\r\n\r\n      expect(exception).toBeInstanceOf(Error);\r\n      expect(exception.name).toBe('UnauthorizedException');\r\n      expect(exception.code).toBe('UNAUTHORIZED');\r\n    });\r\n\r\n    it('should maintain proper prototype chain', () => {\r\n      const exception = new UnauthorizedException();\r\n\r\n      expect(exception instanceof UnauthorizedException).toBe(true);\r\n      expect(exception instanceof Error).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('context and correlation', () => {\r\n    it('should preserve context and correlation ID', () => {\r\n      const context = { userId: 'user123', sessionId: 'session456' };\r\n      const correlationId = 'correlation-123';\r\n\r\n      const exception = new UnauthorizedException('Test message', {\r\n        context,\r\n        correlationId,\r\n      });\r\n\r\n      expect(exception.context).toMatchObject(context);\r\n      expect(exception.correlationId).toBe(correlationId);\r\n    });\r\n  });\r\n});"], "version": 3}