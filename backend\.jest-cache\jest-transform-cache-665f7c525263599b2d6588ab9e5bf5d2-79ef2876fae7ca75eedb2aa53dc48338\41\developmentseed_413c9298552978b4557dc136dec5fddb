b51c972c3e99486b80af283a712beac1
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DevelopmentSeeder = void 0;
const common_1 = require("@nestjs/common");
const bcrypt = __importStar(require("bcrypt"));
/**
 * Development environment database seeder
 * Creates sample data for development and testing purposes
 */
class DevelopmentSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(DevelopmentSeeder.name);
    }
    /**
     * Execute all development seeds
     */
    async seed() {
        this.logger.log('Starting development database seeding...');
        try {
            await this.seedRoles();
            await this.seedUsers();
            await this.seedAssets();
            await this.seedVulnerabilities();
            await this.seedSecurityEvents();
            await this.seedSecurityEventTags();
            this.logger.log('Development database seeding completed successfully');
        }
        catch (error) {
            this.logger.error('Development database seeding failed', {
                error: error.message,
                stack: error.stack,
            });
            throw error;
        }
    }
    /**
     * Seed system roles
     */
    async seedRoles() {
        this.logger.debug('Seeding roles...');
        const roles = [
            {
                name: 'admin',
                description: 'System administrator with full access',
                permissions: [
                    'users:read', 'users:write', 'users:delete',
                    'roles:read', 'roles:write', 'roles:delete',
                    'security-events:read', 'security-events:write', 'security-events:delete',
                    'vulnerabilities:read', 'vulnerabilities:write', 'vulnerabilities:delete',
                    'assets:read', 'assets:write', 'assets:delete',
                    'scans:read', 'scans:write', 'scans:delete',
                    'reports:read', 'reports:write',
                    'system:admin'
                ],
                is_system_role: true,
            },
            {
                name: 'security_analyst',
                description: 'Security analyst with read/write access to security data',
                permissions: [
                    'security-events:read', 'security-events:write',
                    'vulnerabilities:read', 'vulnerabilities:write',
                    'assets:read', 'assets:write',
                    'scans:read', 'scans:write',
                    'reports:read'
                ],
                is_system_role: true,
            },
            {
                name: 'viewer',
                description: 'Read-only access to security data',
                permissions: [
                    'security-events:read',
                    'vulnerabilities:read',
                    'assets:read',
                    'scans:read',
                    'reports:read'
                ],
                is_system_role: true,
            },
            {
                name: 'incident_responder',
                description: 'Incident response team member',
                permissions: [
                    'security-events:read', 'security-events:write',
                    'vulnerabilities:read',
                    'assets:read',
                    'reports:read'
                ],
                is_system_role: true,
            },
        ];
        for (const role of roles) {
            await this.dataSource.query(`
        INSERT INTO roles (name, description, permissions, is_system_role)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (name) DO UPDATE SET
          description = EXCLUDED.description,
          permissions = EXCLUDED.permissions,
          is_system_role = EXCLUDED.is_system_role,
          updated_at = CURRENT_TIMESTAMP
      `, [role.name, role.description, JSON.stringify(role.permissions), role.is_system_role]);
        }
        this.logger.debug(`Seeded ${roles.length} roles`);
    }
    /**
     * Seed development users
     */
    async seedUsers() {
        this.logger.debug('Seeding users...');
        const passwordHash = await bcrypt.hash('dev123456', 10);
        const users = [
            {
                email: '<EMAIL>',
                password_hash: passwordHash,
                first_name: 'System',
                last_name: 'Administrator',
                role: 'admin',
                is_active: true,
                email_verified: true,
            },
            {
                email: '<EMAIL>',
                password_hash: passwordHash,
                first_name: 'Security',
                last_name: 'Analyst',
                role: 'security_analyst',
                is_active: true,
                email_verified: true,
            },
            {
                email: '<EMAIL>',
                password_hash: passwordHash,
                first_name: 'Read',
                last_name: 'Only',
                role: 'viewer',
                is_active: true,
                email_verified: true,
            },
            {
                email: '<EMAIL>',
                password_hash: passwordHash,
                first_name: 'Incident',
                last_name: 'Responder',
                role: 'incident_responder',
                is_active: true,
                email_verified: true,
            },
        ];
        for (const user of users) {
            await this.dataSource.query(`
        INSERT INTO users (email, password_hash, first_name, last_name, role, is_active, email_verified)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (email) DO UPDATE SET
          password_hash = EXCLUDED.password_hash,
          first_name = EXCLUDED.first_name,
          last_name = EXCLUDED.last_name,
          role = EXCLUDED.role,
          is_active = EXCLUDED.is_active,
          email_verified = EXCLUDED.email_verified,
          updated_at = CURRENT_TIMESTAMP
      `, [user.email, user.password_hash, user.first_name, user.last_name, user.role, user.is_active, user.email_verified]);
        }
        this.logger.debug(`Seeded ${users.length} users`);
    }
    /**
     * Seed sample assets
     */
    async seedAssets() {
        this.logger.debug('Seeding assets...');
        const assets = [
            {
                name: 'Web Server 01',
                type: 'SERVER',
                ip_address: '************',
                hostname: 'web01.internal',
                operating_system: 'Ubuntu',
                os_version: '22.04 LTS',
                location: 'Data Center A',
                owner: 'IT Operations',
                criticality: 'HIGH',
                status: 'ACTIVE',
                metadata: {
                    services: ['nginx', 'php-fpm', 'mysql'],
                    environment: 'production',
                    backup_schedule: 'daily'
                }
            },
            {
                name: 'Database Server',
                type: 'DATABASE',
                ip_address: '************',
                hostname: 'db01.internal',
                operating_system: 'CentOS',
                os_version: '8.5',
                location: 'Data Center A',
                owner: 'Database Team',
                criticality: 'CRITICAL',
                status: 'ACTIVE',
                metadata: {
                    database_type: 'PostgreSQL',
                    version: '14.2',
                    backup_schedule: 'hourly'
                }
            },
            {
                name: 'Development Workstation',
                type: 'WORKSTATION',
                ip_address: '************',
                hostname: 'dev-ws-01',
                operating_system: 'Windows',
                os_version: '11 Pro',
                location: 'Office Floor 2',
                owner: 'Development Team',
                criticality: 'MEDIUM',
                status: 'ACTIVE',
                metadata: {
                    user: 'developer',
                    department: 'Engineering'
                }
            },
            {
                name: 'Core Router',
                type: 'NETWORK_DEVICE',
                ip_address: '***********',
                hostname: 'router-core-01',
                operating_system: 'Cisco IOS',
                os_version: '15.7',
                location: 'Network Closet',
                owner: 'Network Team',
                criticality: 'CRITICAL',
                status: 'ACTIVE',
                metadata: {
                    device_type: 'router',
                    model: 'Cisco ISR 4331'
                }
            },
        ];
        for (const asset of assets) {
            await this.dataSource.query(`
        INSERT INTO assets (name, type, ip_address, hostname, operating_system, os_version, location, owner, criticality, status, metadata)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        ON CONFLICT (name) DO UPDATE SET
          type = EXCLUDED.type,
          ip_address = EXCLUDED.ip_address,
          hostname = EXCLUDED.hostname,
          operating_system = EXCLUDED.operating_system,
          os_version = EXCLUDED.os_version,
          location = EXCLUDED.location,
          owner = EXCLUDED.owner,
          criticality = EXCLUDED.criticality,
          status = EXCLUDED.status,
          metadata = EXCLUDED.metadata,
          updated_at = CURRENT_TIMESTAMP
      `, [
                asset.name, asset.type, asset.ip_address, asset.hostname,
                asset.operating_system, asset.os_version, asset.location, asset.owner,
                asset.criticality, asset.status, JSON.stringify(asset.metadata)
            ]);
        }
        this.logger.debug(`Seeded ${assets.length} assets`);
    }
    /**
     * Seed sample vulnerabilities
     */
    async seedVulnerabilities() {
        this.logger.debug('Seeding vulnerabilities...');
        const vulnerabilities = [
            {
                cve_id: 'CVE-2023-0001',
                title: 'Remote Code Execution in Web Framework',
                description: 'A critical vulnerability allowing remote code execution through unsanitized input.',
                severity: 'CRITICAL',
                cvss_score: 9.8,
                cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',
                cwe_id: 'CWE-78',
                affected_software: [
                    { name: 'WebFramework', versions: ['1.0.0', '1.1.0', '1.2.0'] }
                ],
                references: [
                    { url: 'https://nvd.nist.gov/vuln/detail/CVE-2023-0001', type: 'advisory' }
                ],
                exploit_available: true,
                patch_available: true,
                published_date: new Date('2023-01-15'),
                modified_date: new Date('2023-01-20')
            },
            {
                cve_id: 'CVE-2023-0002',
                title: 'SQL Injection in Database Interface',
                description: 'SQL injection vulnerability in database query interface.',
                severity: 'HIGH',
                cvss_score: 8.1,
                cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N',
                cwe_id: 'CWE-89',
                affected_software: [
                    { name: 'DatabaseInterface', versions: ['2.0.0', '2.1.0'] }
                ],
                references: [
                    { url: 'https://nvd.nist.gov/vuln/detail/CVE-2023-0002', type: 'advisory' }
                ],
                exploit_available: false,
                patch_available: true,
                published_date: new Date('2023-02-01'),
                modified_date: new Date('2023-02-05')
            },
            {
                cve_id: 'CVE-2023-0003',
                title: 'Cross-Site Scripting in Admin Panel',
                description: 'Stored XSS vulnerability in administrative interface.',
                severity: 'MEDIUM',
                cvss_score: 6.1,
                cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N',
                cwe_id: 'CWE-79',
                affected_software: [
                    { name: 'AdminPanel', versions: ['3.0.0', '3.1.0', '3.2.0'] }
                ],
                references: [
                    { url: 'https://nvd.nist.gov/vuln/detail/CVE-2023-0003', type: 'advisory' }
                ],
                exploit_available: false,
                patch_available: false,
                published_date: new Date('2023-03-01'),
                modified_date: new Date('2023-03-01')
            },
        ];
        for (const vuln of vulnerabilities) {
            await this.dataSource.query(`
        INSERT INTO vulnerabilities (cve_id, title, description, severity, cvss_score, cvss_vector, cwe_id, affected_software, references, exploit_available, patch_available, published_date, modified_date)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        ON CONFLICT (cve_id) DO UPDATE SET
          title = EXCLUDED.title,
          description = EXCLUDED.description,
          severity = EXCLUDED.severity,
          cvss_score = EXCLUDED.cvss_score,
          cvss_vector = EXCLUDED.cvss_vector,
          cwe_id = EXCLUDED.cwe_id,
          affected_software = EXCLUDED.affected_software,
          references = EXCLUDED.references,
          exploit_available = EXCLUDED.exploit_available,
          patch_available = EXCLUDED.patch_available,
          published_date = EXCLUDED.published_date,
          modified_date = EXCLUDED.modified_date,
          updated_at = CURRENT_TIMESTAMP
      `, [
                vuln.cve_id, vuln.title, vuln.description, vuln.severity,
                vuln.cvss_score, vuln.cvss_vector, vuln.cwe_id,
                JSON.stringify(vuln.affected_software), JSON.stringify(vuln.references),
                vuln.exploit_available, vuln.patch_available,
                vuln.published_date, vuln.modified_date
            ]);
        }
        this.logger.debug(`Seeded ${vulnerabilities.length} vulnerabilities`);
    }
    /**
     * Seed sample security events
     */
    async seedSecurityEvents() {
        this.logger.debug('Seeding security events...');
        const events = [
            {
                event_type: 'INTRUSION_ATTEMPT',
                severity: 'HIGH',
                title: 'Multiple Failed Login Attempts',
                description: 'Multiple failed login attempts detected from external IP address',
                source_ip: '************',
                target_ip: '************',
                source_port: 45123,
                target_port: 22,
                protocol: 'TCP',
                payload: {
                    username_attempts: ['admin', 'root', 'user'],
                    attempt_count: 15
                },
                metadata: {
                    geolocation: { country: 'Unknown', city: 'Unknown' },
                    threat_intelligence: { reputation: 'malicious' }
                },
                status: 'OPEN',
                first_seen_at: new Date(Date.now() - 3600000), // 1 hour ago
                last_seen_at: new Date(Date.now() - 1800000), // 30 minutes ago
                occurrence_count: 15,
                risk_score: 85.5
            },
            {
                event_type: 'MALWARE_DETECTION',
                severity: 'CRITICAL',
                title: 'Malware Detected on Workstation',
                description: 'Trojan horse detected in downloaded file',
                source_ip: '************',
                target_ip: null,
                payload: {
                    file_path: 'C:\\Users\\<USER>\\Downloads\\document.exe',
                    malware_type: 'Trojan.Generic',
                    scanner: 'Windows Defender'
                },
                metadata: {
                    quarantine_status: 'quarantined',
                    scan_engine: 'Microsoft Defender'
                },
                status: 'INVESTIGATING',
                first_seen_at: new Date(Date.now() - 7200000), // 2 hours ago
                last_seen_at: new Date(Date.now() - 7200000),
                occurrence_count: 1,
                risk_score: 95.0
            },
            {
                event_type: 'NETWORK_ANOMALY',
                severity: 'MEDIUM',
                title: 'Unusual Network Traffic Pattern',
                description: 'Abnormal data transfer volume detected',
                source_ip: '************',
                target_ip: '************',
                source_port: 3306,
                target_port: 443,
                protocol: 'TCP',
                payload: {
                    bytes_transferred: 1073741824, // 1GB
                    duration_seconds: 300,
                    normal_baseline: 104857600 // 100MB
                },
                metadata: {
                    baseline_deviation: 10.0,
                    time_of_day: 'off_hours'
                },
                status: 'RESOLVED',
                first_seen_at: new Date(Date.now() - 86400000), // 24 hours ago
                last_seen_at: new Date(Date.now() - 86400000),
                occurrence_count: 1,
                risk_score: 65.0
            },
        ];
        for (const event of events) {
            await this.dataSource.query(`
        INSERT INTO security_events (event_type, severity, title, description, source_ip, target_ip, source_port, target_port, protocol, payload, metadata, status, first_seen_at, last_seen_at, occurrence_count, risk_score)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      `, [
                event.event_type, event.severity, event.title, event.description,
                event.source_ip, event.target_ip, event.source_port, event.target_port,
                event.protocol, JSON.stringify(event.payload), JSON.stringify(event.metadata),
                event.status, event.first_seen_at, event.last_seen_at,
                event.occurrence_count, event.risk_score
            ]);
        }
        this.logger.debug(`Seeded ${events.length} security events`);
    }
    /**
     * Seed security event tags
     */
    async seedSecurityEventTags() {
        this.logger.debug('Seeding security event tags...');
        const tags = [
            { name: 'brute-force', description: 'Brute force attack attempts', color: '#ff4444' },
            { name: 'malware', description: 'Malware related events', color: '#cc0000' },
            { name: 'network-anomaly', description: 'Network traffic anomalies', color: '#ff8800' },
            { name: 'false-positive', description: 'Confirmed false positive events', color: '#888888' },
            { name: 'critical-asset', description: 'Events affecting critical assets', color: '#ff0000' },
            { name: 'external-threat', description: 'Threats from external sources', color: '#9900cc' },
            { name: 'insider-threat', description: 'Potential insider threats', color: '#cc6600' },
            { name: 'automated', description: 'Automated attack patterns', color: '#0066cc' },
        ];
        for (const tag of tags) {
            await this.dataSource.query(`
        INSERT INTO security_event_tags (name, description, color)
        VALUES ($1, $2, $3)
        ON CONFLICT (name) DO UPDATE SET
          description = EXCLUDED.description,
          color = EXCLUDED.color
      `, [tag.name, tag.description, tag.color]);
        }
        this.logger.debug(`Seeded ${tags.length} security event tags`);
    }
}
exports.DevelopmentSeeder = DevelopmentSeeder;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxpbmZyYXN0cnVjdHVyZVxcZGF0YWJhc2VcXHNlZWRzXFxkZXZlbG9wbWVudC5zZWVkLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNBLDJDQUF3QztBQUN4QywrQ0FBaUM7QUFFakM7OztHQUdHO0FBQ0gsTUFBYSxpQkFBaUI7SUFHNUIsWUFBNkIsVUFBc0I7UUFBdEIsZUFBVSxHQUFWLFVBQVUsQ0FBWTtRQUZsQyxXQUFNLEdBQUcsSUFBSSxlQUFNLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLENBQUM7SUFFUCxDQUFDO0lBRXZEOztPQUVHO0lBQ0gsS0FBSyxDQUFDLElBQUk7UUFDUixJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQywwQ0FBMEMsQ0FBQyxDQUFDO1FBRTVELElBQUksQ0FBQztZQUNILE1BQU0sSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFDO1lBQ3ZCLE1BQU0sSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFDO1lBQ3ZCLE1BQU0sSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFDO1lBQ3hCLE1BQU0sSUFBSSxDQUFDLG1CQUFtQixFQUFFLENBQUM7WUFDakMsTUFBTSxJQUFJLENBQUMsa0JBQWtCLEVBQUUsQ0FBQztZQUNoQyxNQUFNLElBQUksQ0FBQyxxQkFBcUIsRUFBRSxDQUFDO1lBRW5DLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLHFEQUFxRCxDQUFDLENBQUM7UUFDekUsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxxQ0FBcUMsRUFBRTtnQkFDdkQsS0FBSyxFQUFFLEtBQUssQ0FBQyxPQUFPO2dCQUNwQixLQUFLLEVBQUUsS0FBSyxDQUFDLEtBQUs7YUFDbkIsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxLQUFLLENBQUM7UUFDZCxDQUFDO0lBQ0gsQ0FBQztJQUVEOztPQUVHO0lBQ0ssS0FBSyxDQUFDLFNBQVM7UUFDckIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsQ0FBQztRQUV0QyxNQUFNLEtBQUssR0FBRztZQUNaO2dCQUNFLElBQUksRUFBRSxPQUFPO2dCQUNiLFdBQVcsRUFBRSx1Q0FBdUM7Z0JBQ3BELFdBQVcsRUFBRTtvQkFDWCxZQUFZLEVBQUUsYUFBYSxFQUFFLGNBQWM7b0JBQzNDLFlBQVksRUFBRSxhQUFhLEVBQUUsY0FBYztvQkFDM0Msc0JBQXNCLEVBQUUsdUJBQXVCLEVBQUUsd0JBQXdCO29CQUN6RSxzQkFBc0IsRUFBRSx1QkFBdUIsRUFBRSx3QkFBd0I7b0JBQ3pFLGFBQWEsRUFBRSxjQUFjLEVBQUUsZUFBZTtvQkFDOUMsWUFBWSxFQUFFLGFBQWEsRUFBRSxjQUFjO29CQUMzQyxjQUFjLEVBQUUsZUFBZTtvQkFDL0IsY0FBYztpQkFDZjtnQkFDRCxjQUFjLEVBQUUsSUFBSTthQUNyQjtZQUNEO2dCQUNFLElBQUksRUFBRSxrQkFBa0I7Z0JBQ3hCLFdBQVcsRUFBRSwwREFBMEQ7Z0JBQ3ZFLFdBQVcsRUFBRTtvQkFDWCxzQkFBc0IsRUFBRSx1QkFBdUI7b0JBQy9DLHNCQUFzQixFQUFFLHVCQUF1QjtvQkFDL0MsYUFBYSxFQUFFLGNBQWM7b0JBQzdCLFlBQVksRUFBRSxhQUFhO29CQUMzQixjQUFjO2lCQUNmO2dCQUNELGNBQWMsRUFBRSxJQUFJO2FBQ3JCO1lBQ0Q7Z0JBQ0UsSUFBSSxFQUFFLFFBQVE7Z0JBQ2QsV0FBVyxFQUFFLG1DQUFtQztnQkFDaEQsV0FBVyxFQUFFO29CQUNYLHNCQUFzQjtvQkFDdEIsc0JBQXNCO29CQUN0QixhQUFhO29CQUNiLFlBQVk7b0JBQ1osY0FBYztpQkFDZjtnQkFDRCxjQUFjLEVBQUUsSUFBSTthQUNyQjtZQUNEO2dCQUNFLElBQUksRUFBRSxvQkFBb0I7Z0JBQzFCLFdBQVcsRUFBRSwrQkFBK0I7Z0JBQzVDLFdBQVcsRUFBRTtvQkFDWCxzQkFBc0IsRUFBRSx1QkFBdUI7b0JBQy9DLHNCQUFzQjtvQkFDdEIsYUFBYTtvQkFDYixjQUFjO2lCQUNmO2dCQUNELGNBQWMsRUFBRSxJQUFJO2FBQ3JCO1NBQ0YsQ0FBQztRQUVGLEtBQUssTUFBTSxJQUFJLElBQUksS0FBSyxFQUFFLENBQUM7WUFDekIsTUFBTSxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQzs7Ozs7Ozs7T0FRM0IsRUFBRSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLFdBQVcsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsRUFBRSxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQztRQUMzRixDQUFDO1FBRUQsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsVUFBVSxLQUFLLENBQUMsTUFBTSxRQUFRLENBQUMsQ0FBQztJQUNwRCxDQUFDO0lBRUQ7O09BRUc7SUFDSyxLQUFLLENBQUMsU0FBUztRQUNyQixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDO1FBRXRDLE1BQU0sWUFBWSxHQUFHLE1BQU0sTUFBTSxDQUFDLElBQUksQ0FBQyxXQUFXLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFFeEQsTUFBTSxLQUFLLEdBQUc7WUFDWjtnQkFDRSxLQUFLLEVBQUUsb0JBQW9CO2dCQUMzQixhQUFhLEVBQUUsWUFBWTtnQkFDM0IsVUFBVSxFQUFFLFFBQVE7Z0JBQ3BCLFNBQVMsRUFBRSxlQUFlO2dCQUMxQixJQUFJLEVBQUUsT0FBTztnQkFDYixTQUFTLEVBQUUsSUFBSTtnQkFDZixjQUFjLEVBQUUsSUFBSTthQUNyQjtZQUNEO2dCQUNFLEtBQUssRUFBRSxzQkFBc0I7Z0JBQzdCLGFBQWEsRUFBRSxZQUFZO2dCQUMzQixVQUFVLEVBQUUsVUFBVTtnQkFDdEIsU0FBUyxFQUFFLFNBQVM7Z0JBQ3BCLElBQUksRUFBRSxrQkFBa0I7Z0JBQ3hCLFNBQVMsRUFBRSxJQUFJO2dCQUNmLGNBQWMsRUFBRSxJQUFJO2FBQ3JCO1lBQ0Q7Z0JBQ0UsS0FBSyxFQUFFLHFCQUFxQjtnQkFDNUIsYUFBYSxFQUFFLFlBQVk7Z0JBQzNCLFVBQVUsRUFBRSxNQUFNO2dCQUNsQixTQUFTLEVBQUUsTUFBTTtnQkFDakIsSUFBSSxFQUFFLFFBQVE7Z0JBQ2QsU0FBUyxFQUFFLElBQUk7Z0JBQ2YsY0FBYyxFQUFFLElBQUk7YUFDckI7WUFDRDtnQkFDRSxLQUFLLEVBQUUsd0JBQXdCO2dCQUMvQixhQUFhLEVBQUUsWUFBWTtnQkFDM0IsVUFBVSxFQUFFLFVBQVU7Z0JBQ3RCLFNBQVMsRUFBRSxXQUFXO2dCQUN0QixJQUFJLEVBQUUsb0JBQW9CO2dCQUMxQixTQUFTLEVBQUUsSUFBSTtnQkFDZixjQUFjLEVBQUUsSUFBSTthQUNyQjtTQUNGLENBQUM7UUFFRixLQUFLLE1BQU0sSUFBSSxJQUFJLEtBQUssRUFBRSxDQUFDO1lBQ3pCLE1BQU0sSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUM7Ozs7Ozs7Ozs7O09BVzNCLEVBQUUsQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFFLElBQUksQ0FBQyxhQUFhLEVBQUUsSUFBSSxDQUFDLFVBQVUsRUFBRSxJQUFJLENBQUMsU0FBUyxFQUFFLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLFNBQVMsRUFBRSxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQztRQUN4SCxDQUFDO1FBRUQsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsVUFBVSxLQUFLLENBQUMsTUFBTSxRQUFRLENBQUMsQ0FBQztJQUNwRCxDQUFDO0lBRUQ7O09BRUc7SUFDSyxLQUFLLENBQUMsVUFBVTtRQUN0QixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDO1FBRXZDLE1BQU0sTUFBTSxHQUFHO1lBQ2I7Z0JBQ0UsSUFBSSxFQUFFLGVBQWU7Z0JBQ3JCLElBQUksRUFBRSxRQUFRO2dCQUNkLFVBQVUsRUFBRSxjQUFjO2dCQUMxQixRQUFRLEVBQUUsZ0JBQWdCO2dCQUMxQixnQkFBZ0IsRUFBRSxRQUFRO2dCQUMxQixVQUFVLEVBQUUsV0FBVztnQkFDdkIsUUFBUSxFQUFFLGVBQWU7Z0JBQ3pCLEtBQUssRUFBRSxlQUFlO2dCQUN0QixXQUFXLEVBQUUsTUFBTTtnQkFDbkIsTUFBTSxFQUFFLFFBQVE7Z0JBQ2hCLFFBQVEsRUFBRTtvQkFDUixRQUFRLEVBQUUsQ0FBQyxPQUFPLEVBQUUsU0FBUyxFQUFFLE9BQU8sQ0FBQztvQkFDdkMsV0FBVyxFQUFFLFlBQVk7b0JBQ3pCLGVBQWUsRUFBRSxPQUFPO2lCQUN6QjthQUNGO1lBQ0Q7Z0JBQ0UsSUFBSSxFQUFFLGlCQUFpQjtnQkFDdkIsSUFBSSxFQUFFLFVBQVU7Z0JBQ2hCLFVBQVUsRUFBRSxjQUFjO2dCQUMxQixRQUFRLEVBQUUsZUFBZTtnQkFDekIsZ0JBQWdCLEVBQUUsUUFBUTtnQkFDMUIsVUFBVSxFQUFFLEtBQUs7Z0JBQ2pCLFFBQVEsRUFBRSxlQUFlO2dCQUN6QixLQUFLLEVBQUUsZUFBZTtnQkFDdEIsV0FBVyxFQUFFLFVBQVU7Z0JBQ3ZCLE1BQU0sRUFBRSxRQUFRO2dCQUNoQixRQUFRLEVBQUU7b0JBQ1IsYUFBYSxFQUFFLFlBQVk7b0JBQzNCLE9BQU8sRUFBRSxNQUFNO29CQUNmLGVBQWUsRUFBRSxRQUFRO2lCQUMxQjthQUNGO1lBQ0Q7Z0JBQ0UsSUFBSSxFQUFFLHlCQUF5QjtnQkFDL0IsSUFBSSxFQUFFLGFBQWE7Z0JBQ25CLFVBQVUsRUFBRSxjQUFjO2dCQUMxQixRQUFRLEVBQUUsV0FBVztnQkFDckIsZ0JBQWdCLEVBQUUsU0FBUztnQkFDM0IsVUFBVSxFQUFFLFFBQVE7Z0JBQ3BCLFFBQVEsRUFBRSxnQkFBZ0I7Z0JBQzFCLEtBQUssRUFBRSxrQkFBa0I7Z0JBQ3pCLFdBQVcsRUFBRSxRQUFRO2dCQUNyQixNQUFNLEVBQUUsUUFBUTtnQkFDaEIsUUFBUSxFQUFFO29CQUNSLElBQUksRUFBRSxXQUFXO29CQUNqQixVQUFVLEVBQUUsYUFBYTtpQkFDMUI7YUFDRjtZQUNEO2dCQUNFLElBQUksRUFBRSxhQUFhO2dCQUNuQixJQUFJLEVBQUUsZ0JBQWdCO2dCQUN0QixVQUFVLEVBQUUsYUFBYTtnQkFDekIsUUFBUSxFQUFFLGdCQUFnQjtnQkFDMUIsZ0JBQWdCLEVBQUUsV0FBVztnQkFDN0IsVUFBVSxFQUFFLE1BQU07Z0JBQ2xCLFFBQVEsRUFBRSxnQkFBZ0I7Z0JBQzFCLEtBQUssRUFBRSxjQUFjO2dCQUNyQixXQUFXLEVBQUUsVUFBVTtnQkFDdkIsTUFBTSxFQUFFLFFBQVE7Z0JBQ2hCLFFBQVEsRUFBRTtvQkFDUixXQUFXLEVBQUUsUUFBUTtvQkFDckIsS0FBSyxFQUFFLGdCQUFnQjtpQkFDeEI7YUFDRjtTQUNGLENBQUM7UUFFRixLQUFLLE1BQU0sS0FBSyxJQUFJLE1BQU0sRUFBRSxDQUFDO1lBQzNCLE1BQU0sSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUM7Ozs7Ozs7Ozs7Ozs7OztPQWUzQixFQUFFO2dCQUNELEtBQUssQ0FBQyxJQUFJLEVBQUUsS0FBSyxDQUFDLElBQUksRUFBRSxLQUFLLENBQUMsVUFBVSxFQUFFLEtBQUssQ0FBQyxRQUFRO2dCQUN4RCxLQUFLLENBQUMsZ0JBQWdCLEVBQUUsS0FBSyxDQUFDLFVBQVUsRUFBRSxLQUFLLENBQUMsUUFBUSxFQUFFLEtBQUssQ0FBQyxLQUFLO2dCQUNyRSxLQUFLLENBQUMsV0FBVyxFQUFFLEtBQUssQ0FBQyxNQUFNLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDO2FBQ2hFLENBQUMsQ0FBQztRQUNMLENBQUM7UUFFRCxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxVQUFVLE1BQU0sQ0FBQyxNQUFNLFNBQVMsQ0FBQyxDQUFDO0lBQ3RELENBQUM7SUFFRDs7T0FFRztJQUNLLEtBQUssQ0FBQyxtQkFBbUI7UUFDL0IsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsNEJBQTRCLENBQUMsQ0FBQztRQUVoRCxNQUFNLGVBQWUsR0FBRztZQUN0QjtnQkFDRSxNQUFNLEVBQUUsZUFBZTtnQkFDdkIsS0FBSyxFQUFFLHdDQUF3QztnQkFDL0MsV0FBVyxFQUFFLG9GQUFvRjtnQkFDakcsUUFBUSxFQUFFLFVBQVU7Z0JBQ3BCLFVBQVUsRUFBRSxHQUFHO2dCQUNmLFdBQVcsRUFBRSw4Q0FBOEM7Z0JBQzNELE1BQU0sRUFBRSxRQUFRO2dCQUNoQixpQkFBaUIsRUFBRTtvQkFDakIsRUFBRSxJQUFJLEVBQUUsY0FBYyxFQUFFLFFBQVEsRUFBRSxDQUFDLE9BQU8sRUFBRSxPQUFPLEVBQUUsT0FBTyxDQUFDLEVBQUU7aUJBQ2hFO2dCQUNELFVBQVUsRUFBRTtvQkFDVixFQUFFLEdBQUcsRUFBRSxnREFBZ0QsRUFBRSxJQUFJLEVBQUUsVUFBVSxFQUFFO2lCQUM1RTtnQkFDRCxpQkFBaUIsRUFBRSxJQUFJO2dCQUN2QixlQUFlLEVBQUUsSUFBSTtnQkFDckIsY0FBYyxFQUFFLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQztnQkFDdEMsYUFBYSxFQUFFLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQzthQUN0QztZQUNEO2dCQUNFLE1BQU0sRUFBRSxlQUFlO2dCQUN2QixLQUFLLEVBQUUscUNBQXFDO2dCQUM1QyxXQUFXLEVBQUUsMERBQTBEO2dCQUN2RSxRQUFRLEVBQUUsTUFBTTtnQkFDaEIsVUFBVSxFQUFFLEdBQUc7Z0JBQ2YsV0FBVyxFQUFFLDhDQUE4QztnQkFDM0QsTUFBTSxFQUFFLFFBQVE7Z0JBQ2hCLGlCQUFpQixFQUFFO29CQUNqQixFQUFFLElBQUksRUFBRSxtQkFBbUIsRUFBRSxRQUFRLEVBQUUsQ0FBQyxPQUFPLEVBQUUsT0FBTyxDQUFDLEVBQUU7aUJBQzVEO2dCQUNELFVBQVUsRUFBRTtvQkFDVixFQUFFLEdBQUcsRUFBRSxnREFBZ0QsRUFBRSxJQUFJLEVBQUUsVUFBVSxFQUFFO2lCQUM1RTtnQkFDRCxpQkFBaUIsRUFBRSxLQUFLO2dCQUN4QixlQUFlLEVBQUUsSUFBSTtnQkFDckIsY0FBYyxFQUFFLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQztnQkFDdEMsYUFBYSxFQUFFLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQzthQUN0QztZQUNEO2dCQUNFLE1BQU0sRUFBRSxlQUFlO2dCQUN2QixLQUFLLEVBQUUscUNBQXFDO2dCQUM1QyxXQUFXLEVBQUUsdURBQXVEO2dCQUNwRSxRQUFRLEVBQUUsUUFBUTtnQkFDbEIsVUFBVSxFQUFFLEdBQUc7Z0JBQ2YsV0FBVyxFQUFFLDhDQUE4QztnQkFDM0QsTUFBTSxFQUFFLFFBQVE7Z0JBQ2hCLGlCQUFpQixFQUFFO29CQUNqQixFQUFFLElBQUksRUFBRSxZQUFZLEVBQUUsUUFBUSxFQUFFLENBQUMsT0FBTyxFQUFFLE9BQU8sRUFBRSxPQUFPLENBQUMsRUFBRTtpQkFDOUQ7Z0JBQ0QsVUFBVSxFQUFFO29CQUNWLEVBQUUsR0FBRyxFQUFFLGdEQUFnRCxFQUFFLElBQUksRUFBRSxVQUFVLEVBQUU7aUJBQzVFO2dCQUNELGlCQUFpQixFQUFFLEtBQUs7Z0JBQ3hCLGVBQWUsRUFBRSxLQUFLO2dCQUN0QixjQUFjLEVBQUUsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDO2dCQUN0QyxhQUFhLEVBQUUsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDO2FBQ3RDO1NBQ0YsQ0FBQztRQUVGLEtBQUssTUFBTSxJQUFJLElBQUksZUFBZSxFQUFFLENBQUM7WUFDbkMsTUFBTSxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7T0FpQjNCLEVBQUU7Z0JBQ0QsSUFBSSxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsS0FBSyxFQUFFLElBQUksQ0FBQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFFBQVE7Z0JBQ3hELElBQUksQ0FBQyxVQUFVLEVBQUUsSUFBSSxDQUFDLFdBQVcsRUFBRSxJQUFJLENBQUMsTUFBTTtnQkFDOUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUM7Z0JBQ3ZFLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxJQUFJLENBQUMsZUFBZTtnQkFDNUMsSUFBSSxDQUFDLGNBQWMsRUFBRSxJQUFJLENBQUMsYUFBYTthQUN4QyxDQUFDLENBQUM7UUFDTCxDQUFDO1FBRUQsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsVUFBVSxlQUFlLENBQUMsTUFBTSxrQkFBa0IsQ0FBQyxDQUFDO0lBQ3hFLENBQUM7SUFFRDs7T0FFRztJQUNLLEtBQUssQ0FBQyxrQkFBa0I7UUFDOUIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsNEJBQTRCLENBQUMsQ0FBQztRQUVoRCxNQUFNLE1BQU0sR0FBRztZQUNiO2dCQUNFLFVBQVUsRUFBRSxtQkFBbUI7Z0JBQy9CLFFBQVEsRUFBRSxNQUFNO2dCQUNoQixLQUFLLEVBQUUsZ0NBQWdDO2dCQUN2QyxXQUFXLEVBQUUsa0VBQWtFO2dCQUMvRSxTQUFTLEVBQUUsY0FBYztnQkFDekIsU0FBUyxFQUFFLGNBQWM7Z0JBQ3pCLFdBQVcsRUFBRSxLQUFLO2dCQUNsQixXQUFXLEVBQUUsRUFBRTtnQkFDZixRQUFRLEVBQUUsS0FBSztnQkFDZixPQUFPLEVBQUU7b0JBQ1AsaUJBQWlCLEVBQUUsQ0FBQyxPQUFPLEVBQUUsTUFBTSxFQUFFLE1BQU0sQ0FBQztvQkFDNUMsYUFBYSxFQUFFLEVBQUU7aUJBQ2xCO2dCQUNELFFBQVEsRUFBRTtvQkFDUixXQUFXLEVBQUUsRUFBRSxPQUFPLEVBQUUsU0FBUyxFQUFFLElBQUksRUFBRSxTQUFTLEVBQUU7b0JBQ3BELG1CQUFtQixFQUFFLEVBQUUsVUFBVSxFQUFFLFdBQVcsRUFBRTtpQkFDakQ7Z0JBQ0QsTUFBTSxFQUFFLE1BQU07Z0JBQ2QsYUFBYSxFQUFFLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUUsR0FBRyxPQUFPLENBQUMsRUFBRSxhQUFhO2dCQUM1RCxZQUFZLEVBQUUsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLE9BQU8sQ0FBQyxFQUFFLGlCQUFpQjtnQkFDL0QsZ0JBQWdCLEVBQUUsRUFBRTtnQkFDcEIsVUFBVSxFQUFFLElBQUk7YUFDakI7WUFDRDtnQkFDRSxVQUFVLEVBQUUsbUJBQW1CO2dCQUMvQixRQUFRLEVBQUUsVUFBVTtnQkFDcEIsS0FBSyxFQUFFLGlDQUFpQztnQkFDeEMsV0FBVyxFQUFFLDBDQUEwQztnQkFDdkQsU0FBUyxFQUFFLGNBQWM7Z0JBQ3pCLFNBQVMsRUFBRSxJQUFJO2dCQUNmLE9BQU8sRUFBRTtvQkFDUCxTQUFTLEVBQUUsMENBQTBDO29CQUNyRCxZQUFZLEVBQUUsZ0JBQWdCO29CQUM5QixPQUFPLEVBQUUsa0JBQWtCO2lCQUM1QjtnQkFDRCxRQUFRLEVBQUU7b0JBQ1IsaUJBQWlCLEVBQUUsYUFBYTtvQkFDaEMsV0FBVyxFQUFFLG9CQUFvQjtpQkFDbEM7Z0JBQ0QsTUFBTSxFQUFFLGVBQWU7Z0JBQ3ZCLGFBQWEsRUFBRSxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFLEdBQUcsT0FBTyxDQUFDLEVBQUUsY0FBYztnQkFDN0QsWUFBWSxFQUFFLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUUsR0FBRyxPQUFPLENBQUM7Z0JBQzVDLGdCQUFnQixFQUFFLENBQUM7Z0JBQ25CLFVBQVUsRUFBRSxJQUFJO2FBQ2pCO1lBQ0Q7Z0JBQ0UsVUFBVSxFQUFFLGlCQUFpQjtnQkFDN0IsUUFBUSxFQUFFLFFBQVE7Z0JBQ2xCLEtBQUssRUFBRSxpQ0FBaUM7Z0JBQ3hDLFdBQVcsRUFBRSx3Q0FBd0M7Z0JBQ3JELFNBQVMsRUFBRSxjQUFjO2dCQUN6QixTQUFTLEVBQUUsY0FBYztnQkFDekIsV0FBVyxFQUFFLElBQUk7Z0JBQ2pCLFdBQVcsRUFBRSxHQUFHO2dCQUNoQixRQUFRLEVBQUUsS0FBSztnQkFDZixPQUFPLEVBQUU7b0JBQ1AsaUJBQWlCLEVBQUUsVUFBVSxFQUFFLE1BQU07b0JBQ3JDLGdCQUFnQixFQUFFLEdBQUc7b0JBQ3JCLGVBQWUsRUFBRSxTQUFTLENBQUMsUUFBUTtpQkFDcEM7Z0JBQ0QsUUFBUSxFQUFFO29CQUNSLGtCQUFrQixFQUFFLElBQUk7b0JBQ3hCLFdBQVcsRUFBRSxXQUFXO2lCQUN6QjtnQkFDRCxNQUFNLEVBQUUsVUFBVTtnQkFDbEIsYUFBYSxFQUFFLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUUsR0FBRyxRQUFRLENBQUMsRUFBRSxlQUFlO2dCQUMvRCxZQUFZLEVBQUUsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLFFBQVEsQ0FBQztnQkFDN0MsZ0JBQWdCLEVBQUUsQ0FBQztnQkFDbkIsVUFBVSxFQUFFLElBQUk7YUFDakI7U0FDRixDQUFDO1FBRUYsS0FBSyxNQUFNLEtBQUssSUFBSSxNQUFNLEVBQUUsQ0FBQztZQUMzQixNQUFNLElBQUksQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDOzs7T0FHM0IsRUFBRTtnQkFDRCxLQUFLLENBQUMsVUFBVSxFQUFFLEtBQUssQ0FBQyxRQUFRLEVBQUUsS0FBSyxDQUFDLEtBQUssRUFBRSxLQUFLLENBQUMsV0FBVztnQkFDaEUsS0FBSyxDQUFDLFNBQVMsRUFBRSxLQUFLLENBQUMsU0FBUyxFQUFFLEtBQUssQ0FBQyxXQUFXLEVBQUUsS0FBSyxDQUFDLFdBQVc7Z0JBQ3RFLEtBQUssQ0FBQyxRQUFRLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDO2dCQUM3RSxLQUFLLENBQUMsTUFBTSxFQUFFLEtBQUssQ0FBQyxhQUFhLEVBQUUsS0FBSyxDQUFDLFlBQVk7Z0JBQ3JELEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSxLQUFLLENBQUMsVUFBVTthQUN6QyxDQUFDLENBQUM7UUFDTCxDQUFDO1FBRUQsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsVUFBVSxNQUFNLENBQUMsTUFBTSxrQkFBa0IsQ0FBQyxDQUFDO0lBQy9ELENBQUM7SUFFRDs7T0FFRztJQUNLLEtBQUssQ0FBQyxxQkFBcUI7UUFDakMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsZ0NBQWdDLENBQUMsQ0FBQztRQUVwRCxNQUFNLElBQUksR0FBRztZQUNYLEVBQUUsSUFBSSxFQUFFLGFBQWEsRUFBRSxXQUFXLEVBQUUsNkJBQTZCLEVBQUUsS0FBSyxFQUFFLFNBQVMsRUFBRTtZQUNyRixFQUFFLElBQUksRUFBRSxTQUFTLEVBQUUsV0FBVyxFQUFFLHdCQUF3QixFQUFFLEtBQUssRUFBRSxTQUFTLEVBQUU7WUFDNUUsRUFBRSxJQUFJLEVBQUUsaUJBQWlCLEVBQUUsV0FBVyxFQUFFLDJCQUEyQixFQUFFLEtBQUssRUFBRSxTQUFTLEVBQUU7WUFDdkYsRUFBRSxJQUFJLEVBQUUsZ0JBQWdCLEVBQUUsV0FBVyxFQUFFLGlDQUFpQyxFQUFFLEtBQUssRUFBRSxTQUFTLEVBQUU7WUFDNUYsRUFBRSxJQUFJLEVBQUUsZ0JBQWdCLEVBQUUsV0FBVyxFQUFFLGtDQUFrQyxFQUFFLEtBQUssRUFBRSxTQUFTLEVBQUU7WUFDN0YsRUFBRSxJQUFJLEVBQUUsaUJBQWlCLEVBQUUsV0FBVyxFQUFFLCtCQUErQixFQUFFLEtBQUssRUFBRSxTQUFTLEVBQUU7WUFDM0YsRUFBRSxJQUFJLEVBQUUsZ0JBQWdCLEVBQUUsV0FBVyxFQUFFLDJCQUEyQixFQUFFLEtBQUssRUFBRSxTQUFTLEVBQUU7WUFDdEYsRUFBRSxJQUFJLEVBQUUsV0FBVyxFQUFFLFdBQVcsRUFBRSwyQkFBMkIsRUFBRSxLQUFLLEVBQUUsU0FBUyxFQUFFO1NBQ2xGLENBQUM7UUFFRixLQUFLLE1BQU0sR0FBRyxJQUFJLElBQUksRUFBRSxDQUFDO1lBQ3ZCLE1BQU0sSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUM7Ozs7OztPQU0zQixFQUFFLENBQUMsR0FBRyxDQUFDLElBQUksRUFBRSxHQUFHLENBQUMsV0FBVyxFQUFFLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO1FBQzdDLENBQUM7UUFFRCxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxVQUFVLElBQUksQ0FBQyxNQUFNLHNCQUFzQixDQUFDLENBQUM7SUFDakUsQ0FBQztDQUNGO0FBMWVELDhDQTBlQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXGluZnJhc3RydWN0dXJlXFxkYXRhYmFzZVxcc2VlZHNcXGRldmVsb3BtZW50LnNlZWQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRGF0YVNvdXJjZSB9IGZyb20gJ3R5cGVvcm0nO1xyXG5pbXBvcnQgeyBMb2dnZXIgfSBmcm9tICdAbmVzdGpzL2NvbW1vbic7XHJcbmltcG9ydCAqIGFzIGJjcnlwdCBmcm9tICdiY3J5cHQnO1xyXG5cclxuLyoqXHJcbiAqIERldmVsb3BtZW50IGVudmlyb25tZW50IGRhdGFiYXNlIHNlZWRlclxyXG4gKiBDcmVhdGVzIHNhbXBsZSBkYXRhIGZvciBkZXZlbG9wbWVudCBhbmQgdGVzdGluZyBwdXJwb3Nlc1xyXG4gKi9cclxuZXhwb3J0IGNsYXNzIERldmVsb3BtZW50U2VlZGVyIHtcclxuICBwcml2YXRlIHJlYWRvbmx5IGxvZ2dlciA9IG5ldyBMb2dnZXIoRGV2ZWxvcG1lbnRTZWVkZXIubmFtZSk7XHJcblxyXG4gIGNvbnN0cnVjdG9yKHByaXZhdGUgcmVhZG9ubHkgZGF0YVNvdXJjZTogRGF0YVNvdXJjZSkge31cclxuXHJcbiAgLyoqXHJcbiAgICogRXhlY3V0ZSBhbGwgZGV2ZWxvcG1lbnQgc2VlZHNcclxuICAgKi9cclxuICBhc3luYyBzZWVkKCk6IFByb21pc2U8dm9pZD4ge1xyXG4gICAgdGhpcy5sb2dnZXIubG9nKCdTdGFydGluZyBkZXZlbG9wbWVudCBkYXRhYmFzZSBzZWVkaW5nLi4uJyk7XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgYXdhaXQgdGhpcy5zZWVkUm9sZXMoKTtcclxuICAgICAgYXdhaXQgdGhpcy5zZWVkVXNlcnMoKTtcclxuICAgICAgYXdhaXQgdGhpcy5zZWVkQXNzZXRzKCk7XHJcbiAgICAgIGF3YWl0IHRoaXMuc2VlZFZ1bG5lcmFiaWxpdGllcygpO1xyXG4gICAgICBhd2FpdCB0aGlzLnNlZWRTZWN1cml0eUV2ZW50cygpO1xyXG4gICAgICBhd2FpdCB0aGlzLnNlZWRTZWN1cml0eUV2ZW50VGFncygpO1xyXG5cclxuICAgICAgdGhpcy5sb2dnZXIubG9nKCdEZXZlbG9wbWVudCBkYXRhYmFzZSBzZWVkaW5nIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHknKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRoaXMubG9nZ2VyLmVycm9yKCdEZXZlbG9wbWVudCBkYXRhYmFzZSBzZWVkaW5nIGZhaWxlZCcsIHtcclxuICAgICAgICBlcnJvcjogZXJyb3IubWVzc2FnZSxcclxuICAgICAgICBzdGFjazogZXJyb3Iuc3RhY2ssXHJcbiAgICAgIH0pO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFNlZWQgc3lzdGVtIHJvbGVzXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBhc3luYyBzZWVkUm9sZXMoKTogUHJvbWlzZTx2b2lkPiB7XHJcbiAgICB0aGlzLmxvZ2dlci5kZWJ1ZygnU2VlZGluZyByb2xlcy4uLicpO1xyXG5cclxuICAgIGNvbnN0IHJvbGVzID0gW1xyXG4gICAgICB7XHJcbiAgICAgICAgbmFtZTogJ2FkbWluJyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogJ1N5c3RlbSBhZG1pbmlzdHJhdG9yIHdpdGggZnVsbCBhY2Nlc3MnLFxyXG4gICAgICAgIHBlcm1pc3Npb25zOiBbXHJcbiAgICAgICAgICAndXNlcnM6cmVhZCcsICd1c2Vyczp3cml0ZScsICd1c2VyczpkZWxldGUnLFxyXG4gICAgICAgICAgJ3JvbGVzOnJlYWQnLCAncm9sZXM6d3JpdGUnLCAncm9sZXM6ZGVsZXRlJyxcclxuICAgICAgICAgICdzZWN1cml0eS1ldmVudHM6cmVhZCcsICdzZWN1cml0eS1ldmVudHM6d3JpdGUnLCAnc2VjdXJpdHktZXZlbnRzOmRlbGV0ZScsXHJcbiAgICAgICAgICAndnVsbmVyYWJpbGl0aWVzOnJlYWQnLCAndnVsbmVyYWJpbGl0aWVzOndyaXRlJywgJ3Z1bG5lcmFiaWxpdGllczpkZWxldGUnLFxyXG4gICAgICAgICAgJ2Fzc2V0czpyZWFkJywgJ2Fzc2V0czp3cml0ZScsICdhc3NldHM6ZGVsZXRlJyxcclxuICAgICAgICAgICdzY2FuczpyZWFkJywgJ3NjYW5zOndyaXRlJywgJ3NjYW5zOmRlbGV0ZScsXHJcbiAgICAgICAgICAncmVwb3J0czpyZWFkJywgJ3JlcG9ydHM6d3JpdGUnLFxyXG4gICAgICAgICAgJ3N5c3RlbTphZG1pbidcclxuICAgICAgICBdLFxyXG4gICAgICAgIGlzX3N5c3RlbV9yb2xlOiB0cnVlLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgbmFtZTogJ3NlY3VyaXR5X2FuYWx5c3QnLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnU2VjdXJpdHkgYW5hbHlzdCB3aXRoIHJlYWQvd3JpdGUgYWNjZXNzIHRvIHNlY3VyaXR5IGRhdGEnLFxyXG4gICAgICAgIHBlcm1pc3Npb25zOiBbXHJcbiAgICAgICAgICAnc2VjdXJpdHktZXZlbnRzOnJlYWQnLCAnc2VjdXJpdHktZXZlbnRzOndyaXRlJyxcclxuICAgICAgICAgICd2dWxuZXJhYmlsaXRpZXM6cmVhZCcsICd2dWxuZXJhYmlsaXRpZXM6d3JpdGUnLFxyXG4gICAgICAgICAgJ2Fzc2V0czpyZWFkJywgJ2Fzc2V0czp3cml0ZScsXHJcbiAgICAgICAgICAnc2NhbnM6cmVhZCcsICdzY2Fuczp3cml0ZScsXHJcbiAgICAgICAgICAncmVwb3J0czpyZWFkJ1xyXG4gICAgICAgIF0sXHJcbiAgICAgICAgaXNfc3lzdGVtX3JvbGU6IHRydWUsXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBuYW1lOiAndmlld2VyJyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogJ1JlYWQtb25seSBhY2Nlc3MgdG8gc2VjdXJpdHkgZGF0YScsXHJcbiAgICAgICAgcGVybWlzc2lvbnM6IFtcclxuICAgICAgICAgICdzZWN1cml0eS1ldmVudHM6cmVhZCcsXHJcbiAgICAgICAgICAndnVsbmVyYWJpbGl0aWVzOnJlYWQnLFxyXG4gICAgICAgICAgJ2Fzc2V0czpyZWFkJyxcclxuICAgICAgICAgICdzY2FuczpyZWFkJyxcclxuICAgICAgICAgICdyZXBvcnRzOnJlYWQnXHJcbiAgICAgICAgXSxcclxuICAgICAgICBpc19zeXN0ZW1fcm9sZTogdHJ1ZSxcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIG5hbWU6ICdpbmNpZGVudF9yZXNwb25kZXInLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnSW5jaWRlbnQgcmVzcG9uc2UgdGVhbSBtZW1iZXInLFxyXG4gICAgICAgIHBlcm1pc3Npb25zOiBbXHJcbiAgICAgICAgICAnc2VjdXJpdHktZXZlbnRzOnJlYWQnLCAnc2VjdXJpdHktZXZlbnRzOndyaXRlJyxcclxuICAgICAgICAgICd2dWxuZXJhYmlsaXRpZXM6cmVhZCcsXHJcbiAgICAgICAgICAnYXNzZXRzOnJlYWQnLFxyXG4gICAgICAgICAgJ3JlcG9ydHM6cmVhZCdcclxuICAgICAgICBdLFxyXG4gICAgICAgIGlzX3N5c3RlbV9yb2xlOiB0cnVlLFxyXG4gICAgICB9LFxyXG4gICAgXTtcclxuXHJcbiAgICBmb3IgKGNvbnN0IHJvbGUgb2Ygcm9sZXMpIHtcclxuICAgICAgYXdhaXQgdGhpcy5kYXRhU291cmNlLnF1ZXJ5KGBcclxuICAgICAgICBJTlNFUlQgSU5UTyByb2xlcyAobmFtZSwgZGVzY3JpcHRpb24sIHBlcm1pc3Npb25zLCBpc19zeXN0ZW1fcm9sZSlcclxuICAgICAgICBWQUxVRVMgKCQxLCAkMiwgJDMsICQ0KVxyXG4gICAgICAgIE9OIENPTkZMSUNUIChuYW1lKSBETyBVUERBVEUgU0VUXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbiA9IEVYQ0xVREVELmRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgcGVybWlzc2lvbnMgPSBFWENMVURFRC5wZXJtaXNzaW9ucyxcclxuICAgICAgICAgIGlzX3N5c3RlbV9yb2xlID0gRVhDTFVERUQuaXNfc3lzdGVtX3JvbGUsXHJcbiAgICAgICAgICB1cGRhdGVkX2F0ID0gQ1VSUkVOVF9USU1FU1RBTVBcclxuICAgICAgYCwgW3JvbGUubmFtZSwgcm9sZS5kZXNjcmlwdGlvbiwgSlNPTi5zdHJpbmdpZnkocm9sZS5wZXJtaXNzaW9ucyksIHJvbGUuaXNfc3lzdGVtX3JvbGVdKTtcclxuICAgIH1cclxuXHJcbiAgICB0aGlzLmxvZ2dlci5kZWJ1ZyhgU2VlZGVkICR7cm9sZXMubGVuZ3RofSByb2xlc2ApO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogU2VlZCBkZXZlbG9wbWVudCB1c2Vyc1xyXG4gICAqL1xyXG4gIHByaXZhdGUgYXN5bmMgc2VlZFVzZXJzKCk6IFByb21pc2U8dm9pZD4ge1xyXG4gICAgdGhpcy5sb2dnZXIuZGVidWcoJ1NlZWRpbmcgdXNlcnMuLi4nKTtcclxuXHJcbiAgICBjb25zdCBwYXNzd29yZEhhc2ggPSBhd2FpdCBiY3J5cHQuaGFzaCgnZGV2MTIzNDU2JywgMTApO1xyXG5cclxuICAgIGNvbnN0IHVzZXJzID0gW1xyXG4gICAgICB7XHJcbiAgICAgICAgZW1haWw6ICdhZG1pbkBzZW50aW5lbC5kZXYnLFxyXG4gICAgICAgIHBhc3N3b3JkX2hhc2g6IHBhc3N3b3JkSGFzaCxcclxuICAgICAgICBmaXJzdF9uYW1lOiAnU3lzdGVtJyxcclxuICAgICAgICBsYXN0X25hbWU6ICdBZG1pbmlzdHJhdG9yJyxcclxuICAgICAgICByb2xlOiAnYWRtaW4nLFxyXG4gICAgICAgIGlzX2FjdGl2ZTogdHJ1ZSxcclxuICAgICAgICBlbWFpbF92ZXJpZmllZDogdHJ1ZSxcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIGVtYWlsOiAnYW5hbHlzdEBzZW50aW5lbC5kZXYnLFxyXG4gICAgICAgIHBhc3N3b3JkX2hhc2g6IHBhc3N3b3JkSGFzaCxcclxuICAgICAgICBmaXJzdF9uYW1lOiAnU2VjdXJpdHknLFxyXG4gICAgICAgIGxhc3RfbmFtZTogJ0FuYWx5c3QnLFxyXG4gICAgICAgIHJvbGU6ICdzZWN1cml0eV9hbmFseXN0JyxcclxuICAgICAgICBpc19hY3RpdmU6IHRydWUsXHJcbiAgICAgICAgZW1haWxfdmVyaWZpZWQ6IHRydWUsXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBlbWFpbDogJ3ZpZXdlckBzZW50aW5lbC5kZXYnLFxyXG4gICAgICAgIHBhc3N3b3JkX2hhc2g6IHBhc3N3b3JkSGFzaCxcclxuICAgICAgICBmaXJzdF9uYW1lOiAnUmVhZCcsXHJcbiAgICAgICAgbGFzdF9uYW1lOiAnT25seScsXHJcbiAgICAgICAgcm9sZTogJ3ZpZXdlcicsXHJcbiAgICAgICAgaXNfYWN0aXZlOiB0cnVlLFxyXG4gICAgICAgIGVtYWlsX3ZlcmlmaWVkOiB0cnVlLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgZW1haWw6ICdyZXNwb25kZXJAc2VudGluZWwuZGV2JyxcclxuICAgICAgICBwYXNzd29yZF9oYXNoOiBwYXNzd29yZEhhc2gsXHJcbiAgICAgICAgZmlyc3RfbmFtZTogJ0luY2lkZW50JyxcclxuICAgICAgICBsYXN0X25hbWU6ICdSZXNwb25kZXInLFxyXG4gICAgICAgIHJvbGU6ICdpbmNpZGVudF9yZXNwb25kZXInLFxyXG4gICAgICAgIGlzX2FjdGl2ZTogdHJ1ZSxcclxuICAgICAgICBlbWFpbF92ZXJpZmllZDogdHJ1ZSxcclxuICAgICAgfSxcclxuICAgIF07XHJcblxyXG4gICAgZm9yIChjb25zdCB1c2VyIG9mIHVzZXJzKSB7XHJcbiAgICAgIGF3YWl0IHRoaXMuZGF0YVNvdXJjZS5xdWVyeShgXHJcbiAgICAgICAgSU5TRVJUIElOVE8gdXNlcnMgKGVtYWlsLCBwYXNzd29yZF9oYXNoLCBmaXJzdF9uYW1lLCBsYXN0X25hbWUsIHJvbGUsIGlzX2FjdGl2ZSwgZW1haWxfdmVyaWZpZWQpXHJcbiAgICAgICAgVkFMVUVTICgkMSwgJDIsICQzLCAkNCwgJDUsICQ2LCAkNylcclxuICAgICAgICBPTiBDT05GTElDVCAoZW1haWwpIERPIFVQREFURSBTRVRcclxuICAgICAgICAgIHBhc3N3b3JkX2hhc2ggPSBFWENMVURFRC5wYXNzd29yZF9oYXNoLFxyXG4gICAgICAgICAgZmlyc3RfbmFtZSA9IEVYQ0xVREVELmZpcnN0X25hbWUsXHJcbiAgICAgICAgICBsYXN0X25hbWUgPSBFWENMVURFRC5sYXN0X25hbWUsXHJcbiAgICAgICAgICByb2xlID0gRVhDTFVERUQucm9sZSxcclxuICAgICAgICAgIGlzX2FjdGl2ZSA9IEVYQ0xVREVELmlzX2FjdGl2ZSxcclxuICAgICAgICAgIGVtYWlsX3ZlcmlmaWVkID0gRVhDTFVERUQuZW1haWxfdmVyaWZpZWQsXHJcbiAgICAgICAgICB1cGRhdGVkX2F0ID0gQ1VSUkVOVF9USU1FU1RBTVBcclxuICAgICAgYCwgW3VzZXIuZW1haWwsIHVzZXIucGFzc3dvcmRfaGFzaCwgdXNlci5maXJzdF9uYW1lLCB1c2VyLmxhc3RfbmFtZSwgdXNlci5yb2xlLCB1c2VyLmlzX2FjdGl2ZSwgdXNlci5lbWFpbF92ZXJpZmllZF0pO1xyXG4gICAgfVxyXG5cclxuICAgIHRoaXMubG9nZ2VyLmRlYnVnKGBTZWVkZWQgJHt1c2Vycy5sZW5ndGh9IHVzZXJzYCk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBTZWVkIHNhbXBsZSBhc3NldHNcclxuICAgKi9cclxuICBwcml2YXRlIGFzeW5jIHNlZWRBc3NldHMoKTogUHJvbWlzZTx2b2lkPiB7XHJcbiAgICB0aGlzLmxvZ2dlci5kZWJ1ZygnU2VlZGluZyBhc3NldHMuLi4nKTtcclxuXHJcbiAgICBjb25zdCBhc3NldHMgPSBbXHJcbiAgICAgIHtcclxuICAgICAgICBuYW1lOiAnV2ViIFNlcnZlciAwMScsXHJcbiAgICAgICAgdHlwZTogJ1NFUlZFUicsXHJcbiAgICAgICAgaXBfYWRkcmVzczogJzE5Mi4xNjguMS4xMCcsXHJcbiAgICAgICAgaG9zdG5hbWU6ICd3ZWIwMS5pbnRlcm5hbCcsXHJcbiAgICAgICAgb3BlcmF0aW5nX3N5c3RlbTogJ1VidW50dScsXHJcbiAgICAgICAgb3NfdmVyc2lvbjogJzIyLjA0IExUUycsXHJcbiAgICAgICAgbG9jYXRpb246ICdEYXRhIENlbnRlciBBJyxcclxuICAgICAgICBvd25lcjogJ0lUIE9wZXJhdGlvbnMnLFxyXG4gICAgICAgIGNyaXRpY2FsaXR5OiAnSElHSCcsXHJcbiAgICAgICAgc3RhdHVzOiAnQUNUSVZFJyxcclxuICAgICAgICBtZXRhZGF0YToge1xyXG4gICAgICAgICAgc2VydmljZXM6IFsnbmdpbngnLCAncGhwLWZwbScsICdteXNxbCddLFxyXG4gICAgICAgICAgZW52aXJvbm1lbnQ6ICdwcm9kdWN0aW9uJyxcclxuICAgICAgICAgIGJhY2t1cF9zY2hlZHVsZTogJ2RhaWx5J1xyXG4gICAgICAgIH1cclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIG5hbWU6ICdEYXRhYmFzZSBTZXJ2ZXInLFxyXG4gICAgICAgIHR5cGU6ICdEQVRBQkFTRScsXHJcbiAgICAgICAgaXBfYWRkcmVzczogJzE5Mi4xNjguMS4yMCcsXHJcbiAgICAgICAgaG9zdG5hbWU6ICdkYjAxLmludGVybmFsJyxcclxuICAgICAgICBvcGVyYXRpbmdfc3lzdGVtOiAnQ2VudE9TJyxcclxuICAgICAgICBvc192ZXJzaW9uOiAnOC41JyxcclxuICAgICAgICBsb2NhdGlvbjogJ0RhdGEgQ2VudGVyIEEnLFxyXG4gICAgICAgIG93bmVyOiAnRGF0YWJhc2UgVGVhbScsXHJcbiAgICAgICAgY3JpdGljYWxpdHk6ICdDUklUSUNBTCcsXHJcbiAgICAgICAgc3RhdHVzOiAnQUNUSVZFJyxcclxuICAgICAgICBtZXRhZGF0YToge1xyXG4gICAgICAgICAgZGF0YWJhc2VfdHlwZTogJ1Bvc3RncmVTUUwnLFxyXG4gICAgICAgICAgdmVyc2lvbjogJzE0LjInLFxyXG4gICAgICAgICAgYmFja3VwX3NjaGVkdWxlOiAnaG91cmx5J1xyXG4gICAgICAgIH1cclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIG5hbWU6ICdEZXZlbG9wbWVudCBXb3Jrc3RhdGlvbicsXHJcbiAgICAgICAgdHlwZTogJ1dPUktTVEFUSU9OJyxcclxuICAgICAgICBpcF9hZGRyZXNzOiAnMTkyLjE2OC4yLjUwJyxcclxuICAgICAgICBob3N0bmFtZTogJ2Rldi13cy0wMScsXHJcbiAgICAgICAgb3BlcmF0aW5nX3N5c3RlbTogJ1dpbmRvd3MnLFxyXG4gICAgICAgIG9zX3ZlcnNpb246ICcxMSBQcm8nLFxyXG4gICAgICAgIGxvY2F0aW9uOiAnT2ZmaWNlIEZsb29yIDInLFxyXG4gICAgICAgIG93bmVyOiAnRGV2ZWxvcG1lbnQgVGVhbScsXHJcbiAgICAgICAgY3JpdGljYWxpdHk6ICdNRURJVU0nLFxyXG4gICAgICAgIHN0YXR1czogJ0FDVElWRScsXHJcbiAgICAgICAgbWV0YWRhdGE6IHtcclxuICAgICAgICAgIHVzZXI6ICdkZXZlbG9wZXInLFxyXG4gICAgICAgICAgZGVwYXJ0bWVudDogJ0VuZ2luZWVyaW5nJ1xyXG4gICAgICAgIH1cclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIG5hbWU6ICdDb3JlIFJvdXRlcicsXHJcbiAgICAgICAgdHlwZTogJ05FVFdPUktfREVWSUNFJyxcclxuICAgICAgICBpcF9hZGRyZXNzOiAnMTkyLjE2OC4xLjEnLFxyXG4gICAgICAgIGhvc3RuYW1lOiAncm91dGVyLWNvcmUtMDEnLFxyXG4gICAgICAgIG9wZXJhdGluZ19zeXN0ZW06ICdDaXNjbyBJT1MnLFxyXG4gICAgICAgIG9zX3ZlcnNpb246ICcxNS43JyxcclxuICAgICAgICBsb2NhdGlvbjogJ05ldHdvcmsgQ2xvc2V0JyxcclxuICAgICAgICBvd25lcjogJ05ldHdvcmsgVGVhbScsXHJcbiAgICAgICAgY3JpdGljYWxpdHk6ICdDUklUSUNBTCcsXHJcbiAgICAgICAgc3RhdHVzOiAnQUNUSVZFJyxcclxuICAgICAgICBtZXRhZGF0YToge1xyXG4gICAgICAgICAgZGV2aWNlX3R5cGU6ICdyb3V0ZXInLFxyXG4gICAgICAgICAgbW9kZWw6ICdDaXNjbyBJU1IgNDMzMSdcclxuICAgICAgICB9XHJcbiAgICAgIH0sXHJcbiAgICBdO1xyXG5cclxuICAgIGZvciAoY29uc3QgYXNzZXQgb2YgYXNzZXRzKSB7XHJcbiAgICAgIGF3YWl0IHRoaXMuZGF0YVNvdXJjZS5xdWVyeShgXHJcbiAgICAgICAgSU5TRVJUIElOVE8gYXNzZXRzIChuYW1lLCB0eXBlLCBpcF9hZGRyZXNzLCBob3N0bmFtZSwgb3BlcmF0aW5nX3N5c3RlbSwgb3NfdmVyc2lvbiwgbG9jYXRpb24sIG93bmVyLCBjcml0aWNhbGl0eSwgc3RhdHVzLCBtZXRhZGF0YSlcclxuICAgICAgICBWQUxVRVMgKCQxLCAkMiwgJDMsICQ0LCAkNSwgJDYsICQ3LCAkOCwgJDksICQxMCwgJDExKVxyXG4gICAgICAgIE9OIENPTkZMSUNUIChuYW1lKSBETyBVUERBVEUgU0VUXHJcbiAgICAgICAgICB0eXBlID0gRVhDTFVERUQudHlwZSxcclxuICAgICAgICAgIGlwX2FkZHJlc3MgPSBFWENMVURFRC5pcF9hZGRyZXNzLFxyXG4gICAgICAgICAgaG9zdG5hbWUgPSBFWENMVURFRC5ob3N0bmFtZSxcclxuICAgICAgICAgIG9wZXJhdGluZ19zeXN0ZW0gPSBFWENMVURFRC5vcGVyYXRpbmdfc3lzdGVtLFxyXG4gICAgICAgICAgb3NfdmVyc2lvbiA9IEVYQ0xVREVELm9zX3ZlcnNpb24sXHJcbiAgICAgICAgICBsb2NhdGlvbiA9IEVYQ0xVREVELmxvY2F0aW9uLFxyXG4gICAgICAgICAgb3duZXIgPSBFWENMVURFRC5vd25lcixcclxuICAgICAgICAgIGNyaXRpY2FsaXR5ID0gRVhDTFVERUQuY3JpdGljYWxpdHksXHJcbiAgICAgICAgICBzdGF0dXMgPSBFWENMVURFRC5zdGF0dXMsXHJcbiAgICAgICAgICBtZXRhZGF0YSA9IEVYQ0xVREVELm1ldGFkYXRhLFxyXG4gICAgICAgICAgdXBkYXRlZF9hdCA9IENVUlJFTlRfVElNRVNUQU1QXHJcbiAgICAgIGAsIFtcclxuICAgICAgICBhc3NldC5uYW1lLCBhc3NldC50eXBlLCBhc3NldC5pcF9hZGRyZXNzLCBhc3NldC5ob3N0bmFtZSxcclxuICAgICAgICBhc3NldC5vcGVyYXRpbmdfc3lzdGVtLCBhc3NldC5vc192ZXJzaW9uLCBhc3NldC5sb2NhdGlvbiwgYXNzZXQub3duZXIsXHJcbiAgICAgICAgYXNzZXQuY3JpdGljYWxpdHksIGFzc2V0LnN0YXR1cywgSlNPTi5zdHJpbmdpZnkoYXNzZXQubWV0YWRhdGEpXHJcbiAgICAgIF0pO1xyXG4gICAgfVxyXG5cclxuICAgIHRoaXMubG9nZ2VyLmRlYnVnKGBTZWVkZWQgJHthc3NldHMubGVuZ3RofSBhc3NldHNgKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFNlZWQgc2FtcGxlIHZ1bG5lcmFiaWxpdGllc1xyXG4gICAqL1xyXG4gIHByaXZhdGUgYXN5bmMgc2VlZFZ1bG5lcmFiaWxpdGllcygpOiBQcm9taXNlPHZvaWQ+IHtcclxuICAgIHRoaXMubG9nZ2VyLmRlYnVnKCdTZWVkaW5nIHZ1bG5lcmFiaWxpdGllcy4uLicpO1xyXG5cclxuICAgIGNvbnN0IHZ1bG5lcmFiaWxpdGllcyA9IFtcclxuICAgICAge1xyXG4gICAgICAgIGN2ZV9pZDogJ0NWRS0yMDIzLTAwMDEnLFxyXG4gICAgICAgIHRpdGxlOiAnUmVtb3RlIENvZGUgRXhlY3V0aW9uIGluIFdlYiBGcmFtZXdvcmsnLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQSBjcml0aWNhbCB2dWxuZXJhYmlsaXR5IGFsbG93aW5nIHJlbW90ZSBjb2RlIGV4ZWN1dGlvbiB0aHJvdWdoIHVuc2FuaXRpemVkIGlucHV0LicsXHJcbiAgICAgICAgc2V2ZXJpdHk6ICdDUklUSUNBTCcsXHJcbiAgICAgICAgY3Zzc19zY29yZTogOS44LFxyXG4gICAgICAgIGN2c3NfdmVjdG9yOiAnQ1ZTUzozLjEvQVY6Ti9BQzpML1BSOk4vVUk6Ti9TOlUvQzpIL0k6SC9BOkgnLFxyXG4gICAgICAgIGN3ZV9pZDogJ0NXRS03OCcsXHJcbiAgICAgICAgYWZmZWN0ZWRfc29mdHdhcmU6IFtcclxuICAgICAgICAgIHsgbmFtZTogJ1dlYkZyYW1ld29yaycsIHZlcnNpb25zOiBbJzEuMC4wJywgJzEuMS4wJywgJzEuMi4wJ10gfVxyXG4gICAgICAgIF0sXHJcbiAgICAgICAgcmVmZXJlbmNlczogW1xyXG4gICAgICAgICAgeyB1cmw6ICdodHRwczovL252ZC5uaXN0Lmdvdi92dWxuL2RldGFpbC9DVkUtMjAyMy0wMDAxJywgdHlwZTogJ2Fkdmlzb3J5JyB9XHJcbiAgICAgICAgXSxcclxuICAgICAgICBleHBsb2l0X2F2YWlsYWJsZTogdHJ1ZSxcclxuICAgICAgICBwYXRjaF9hdmFpbGFibGU6IHRydWUsXHJcbiAgICAgICAgcHVibGlzaGVkX2RhdGU6IG5ldyBEYXRlKCcyMDIzLTAxLTE1JyksXHJcbiAgICAgICAgbW9kaWZpZWRfZGF0ZTogbmV3IERhdGUoJzIwMjMtMDEtMjAnKVxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgY3ZlX2lkOiAnQ1ZFLTIwMjMtMDAwMicsXHJcbiAgICAgICAgdGl0bGU6ICdTUUwgSW5qZWN0aW9uIGluIERhdGFiYXNlIEludGVyZmFjZScsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdTUUwgaW5qZWN0aW9uIHZ1bG5lcmFiaWxpdHkgaW4gZGF0YWJhc2UgcXVlcnkgaW50ZXJmYWNlLicsXHJcbiAgICAgICAgc2V2ZXJpdHk6ICdISUdIJyxcclxuICAgICAgICBjdnNzX3Njb3JlOiA4LjEsXHJcbiAgICAgICAgY3Zzc192ZWN0b3I6ICdDVlNTOjMuMS9BVjpOL0FDOkwvUFI6TC9VSTpOL1M6VS9DOkgvSTpIL0E6TicsXHJcbiAgICAgICAgY3dlX2lkOiAnQ1dFLTg5JyxcclxuICAgICAgICBhZmZlY3RlZF9zb2Z0d2FyZTogW1xyXG4gICAgICAgICAgeyBuYW1lOiAnRGF0YWJhc2VJbnRlcmZhY2UnLCB2ZXJzaW9uczogWycyLjAuMCcsICcyLjEuMCddIH1cclxuICAgICAgICBdLFxyXG4gICAgICAgIHJlZmVyZW5jZXM6IFtcclxuICAgICAgICAgIHsgdXJsOiAnaHR0cHM6Ly9udmQubmlzdC5nb3YvdnVsbi9kZXRhaWwvQ1ZFLTIwMjMtMDAwMicsIHR5cGU6ICdhZHZpc29yeScgfVxyXG4gICAgICAgIF0sXHJcbiAgICAgICAgZXhwbG9pdF9hdmFpbGFibGU6IGZhbHNlLFxyXG4gICAgICAgIHBhdGNoX2F2YWlsYWJsZTogdHJ1ZSxcclxuICAgICAgICBwdWJsaXNoZWRfZGF0ZTogbmV3IERhdGUoJzIwMjMtMDItMDEnKSxcclxuICAgICAgICBtb2RpZmllZF9kYXRlOiBuZXcgRGF0ZSgnMjAyMy0wMi0wNScpXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBjdmVfaWQ6ICdDVkUtMjAyMy0wMDAzJyxcclxuICAgICAgICB0aXRsZTogJ0Nyb3NzLVNpdGUgU2NyaXB0aW5nIGluIEFkbWluIFBhbmVsJyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogJ1N0b3JlZCBYU1MgdnVsbmVyYWJpbGl0eSBpbiBhZG1pbmlzdHJhdGl2ZSBpbnRlcmZhY2UuJyxcclxuICAgICAgICBzZXZlcml0eTogJ01FRElVTScsXHJcbiAgICAgICAgY3Zzc19zY29yZTogNi4xLFxyXG4gICAgICAgIGN2c3NfdmVjdG9yOiAnQ1ZTUzozLjEvQVY6Ti9BQzpML1BSOk4vVUk6Ui9TOkMvQzpML0k6TC9BOk4nLFxyXG4gICAgICAgIGN3ZV9pZDogJ0NXRS03OScsXHJcbiAgICAgICAgYWZmZWN0ZWRfc29mdHdhcmU6IFtcclxuICAgICAgICAgIHsgbmFtZTogJ0FkbWluUGFuZWwnLCB2ZXJzaW9uczogWyczLjAuMCcsICczLjEuMCcsICczLjIuMCddIH1cclxuICAgICAgICBdLFxyXG4gICAgICAgIHJlZmVyZW5jZXM6IFtcclxuICAgICAgICAgIHsgdXJsOiAnaHR0cHM6Ly9udmQubmlzdC5nb3YvdnVsbi9kZXRhaWwvQ1ZFLTIwMjMtMDAwMycsIHR5cGU6ICdhZHZpc29yeScgfVxyXG4gICAgICAgIF0sXHJcbiAgICAgICAgZXhwbG9pdF9hdmFpbGFibGU6IGZhbHNlLFxyXG4gICAgICAgIHBhdGNoX2F2YWlsYWJsZTogZmFsc2UsXHJcbiAgICAgICAgcHVibGlzaGVkX2RhdGU6IG5ldyBEYXRlKCcyMDIzLTAzLTAxJyksXHJcbiAgICAgICAgbW9kaWZpZWRfZGF0ZTogbmV3IERhdGUoJzIwMjMtMDMtMDEnKVxyXG4gICAgICB9LFxyXG4gICAgXTtcclxuXHJcbiAgICBmb3IgKGNvbnN0IHZ1bG4gb2YgdnVsbmVyYWJpbGl0aWVzKSB7XHJcbiAgICAgIGF3YWl0IHRoaXMuZGF0YVNvdXJjZS5xdWVyeShgXHJcbiAgICAgICAgSU5TRVJUIElOVE8gdnVsbmVyYWJpbGl0aWVzIChjdmVfaWQsIHRpdGxlLCBkZXNjcmlwdGlvbiwgc2V2ZXJpdHksIGN2c3Nfc2NvcmUsIGN2c3NfdmVjdG9yLCBjd2VfaWQsIGFmZmVjdGVkX3NvZnR3YXJlLCByZWZlcmVuY2VzLCBleHBsb2l0X2F2YWlsYWJsZSwgcGF0Y2hfYXZhaWxhYmxlLCBwdWJsaXNoZWRfZGF0ZSwgbW9kaWZpZWRfZGF0ZSlcclxuICAgICAgICBWQUxVRVMgKCQxLCAkMiwgJDMsICQ0LCAkNSwgJDYsICQ3LCAkOCwgJDksICQxMCwgJDExLCAkMTIsICQxMylcclxuICAgICAgICBPTiBDT05GTElDVCAoY3ZlX2lkKSBETyBVUERBVEUgU0VUXHJcbiAgICAgICAgICB0aXRsZSA9IEVYQ0xVREVELnRpdGxlLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb24gPSBFWENMVURFRC5kZXNjcmlwdGlvbixcclxuICAgICAgICAgIHNldmVyaXR5ID0gRVhDTFVERUQuc2V2ZXJpdHksXHJcbiAgICAgICAgICBjdnNzX3Njb3JlID0gRVhDTFVERUQuY3Zzc19zY29yZSxcclxuICAgICAgICAgIGN2c3NfdmVjdG9yID0gRVhDTFVERUQuY3Zzc192ZWN0b3IsXHJcbiAgICAgICAgICBjd2VfaWQgPSBFWENMVURFRC5jd2VfaWQsXHJcbiAgICAgICAgICBhZmZlY3RlZF9zb2Z0d2FyZSA9IEVYQ0xVREVELmFmZmVjdGVkX3NvZnR3YXJlLFxyXG4gICAgICAgICAgcmVmZXJlbmNlcyA9IEVYQ0xVREVELnJlZmVyZW5jZXMsXHJcbiAgICAgICAgICBleHBsb2l0X2F2YWlsYWJsZSA9IEVYQ0xVREVELmV4cGxvaXRfYXZhaWxhYmxlLFxyXG4gICAgICAgICAgcGF0Y2hfYXZhaWxhYmxlID0gRVhDTFVERUQucGF0Y2hfYXZhaWxhYmxlLFxyXG4gICAgICAgICAgcHVibGlzaGVkX2RhdGUgPSBFWENMVURFRC5wdWJsaXNoZWRfZGF0ZSxcclxuICAgICAgICAgIG1vZGlmaWVkX2RhdGUgPSBFWENMVURFRC5tb2RpZmllZF9kYXRlLFxyXG4gICAgICAgICAgdXBkYXRlZF9hdCA9IENVUlJFTlRfVElNRVNUQU1QXHJcbiAgICAgIGAsIFtcclxuICAgICAgICB2dWxuLmN2ZV9pZCwgdnVsbi50aXRsZSwgdnVsbi5kZXNjcmlwdGlvbiwgdnVsbi5zZXZlcml0eSxcclxuICAgICAgICB2dWxuLmN2c3Nfc2NvcmUsIHZ1bG4uY3Zzc192ZWN0b3IsIHZ1bG4uY3dlX2lkLFxyXG4gICAgICAgIEpTT04uc3RyaW5naWZ5KHZ1bG4uYWZmZWN0ZWRfc29mdHdhcmUpLCBKU09OLnN0cmluZ2lmeSh2dWxuLnJlZmVyZW5jZXMpLFxyXG4gICAgICAgIHZ1bG4uZXhwbG9pdF9hdmFpbGFibGUsIHZ1bG4ucGF0Y2hfYXZhaWxhYmxlLFxyXG4gICAgICAgIHZ1bG4ucHVibGlzaGVkX2RhdGUsIHZ1bG4ubW9kaWZpZWRfZGF0ZVxyXG4gICAgICBdKTtcclxuICAgIH1cclxuXHJcbiAgICB0aGlzLmxvZ2dlci5kZWJ1ZyhgU2VlZGVkICR7dnVsbmVyYWJpbGl0aWVzLmxlbmd0aH0gdnVsbmVyYWJpbGl0aWVzYCk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBTZWVkIHNhbXBsZSBzZWN1cml0eSBldmVudHNcclxuICAgKi9cclxuICBwcml2YXRlIGFzeW5jIHNlZWRTZWN1cml0eUV2ZW50cygpOiBQcm9taXNlPHZvaWQ+IHtcclxuICAgIHRoaXMubG9nZ2VyLmRlYnVnKCdTZWVkaW5nIHNlY3VyaXR5IGV2ZW50cy4uLicpO1xyXG5cclxuICAgIGNvbnN0IGV2ZW50cyA9IFtcclxuICAgICAge1xyXG4gICAgICAgIGV2ZW50X3R5cGU6ICdJTlRSVVNJT05fQVRURU1QVCcsXHJcbiAgICAgICAgc2V2ZXJpdHk6ICdISUdIJyxcclxuICAgICAgICB0aXRsZTogJ011bHRpcGxlIEZhaWxlZCBMb2dpbiBBdHRlbXB0cycsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdNdWx0aXBsZSBmYWlsZWQgbG9naW4gYXR0ZW1wdHMgZGV0ZWN0ZWQgZnJvbSBleHRlcm5hbCBJUCBhZGRyZXNzJyxcclxuICAgICAgICBzb3VyY2VfaXA6ICcyMDMuMC4xMTMuMTAnLFxyXG4gICAgICAgIHRhcmdldF9pcDogJzE5Mi4xNjguMS4xMCcsXHJcbiAgICAgICAgc291cmNlX3BvcnQ6IDQ1MTIzLFxyXG4gICAgICAgIHRhcmdldF9wb3J0OiAyMixcclxuICAgICAgICBwcm90b2NvbDogJ1RDUCcsXHJcbiAgICAgICAgcGF5bG9hZDoge1xyXG4gICAgICAgICAgdXNlcm5hbWVfYXR0ZW1wdHM6IFsnYWRtaW4nLCAncm9vdCcsICd1c2VyJ10sXHJcbiAgICAgICAgICBhdHRlbXB0X2NvdW50OiAxNVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgbWV0YWRhdGE6IHtcclxuICAgICAgICAgIGdlb2xvY2F0aW9uOiB7IGNvdW50cnk6ICdVbmtub3duJywgY2l0eTogJ1Vua25vd24nIH0sXHJcbiAgICAgICAgICB0aHJlYXRfaW50ZWxsaWdlbmNlOiB7IHJlcHV0YXRpb246ICdtYWxpY2lvdXMnIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIHN0YXR1czogJ09QRU4nLFxyXG4gICAgICAgIGZpcnN0X3NlZW5fYXQ6IG5ldyBEYXRlKERhdGUubm93KCkgLSAzNjAwMDAwKSwgLy8gMSBob3VyIGFnb1xyXG4gICAgICAgIGxhc3Rfc2Vlbl9hdDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDE4MDAwMDApLCAvLyAzMCBtaW51dGVzIGFnb1xyXG4gICAgICAgIG9jY3VycmVuY2VfY291bnQ6IDE1LFxyXG4gICAgICAgIHJpc2tfc2NvcmU6IDg1LjVcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIGV2ZW50X3R5cGU6ICdNQUxXQVJFX0RFVEVDVElPTicsXHJcbiAgICAgICAgc2V2ZXJpdHk6ICdDUklUSUNBTCcsXHJcbiAgICAgICAgdGl0bGU6ICdNYWx3YXJlIERldGVjdGVkIG9uIFdvcmtzdGF0aW9uJyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogJ1Ryb2phbiBob3JzZSBkZXRlY3RlZCBpbiBkb3dubG9hZGVkIGZpbGUnLFxyXG4gICAgICAgIHNvdXJjZV9pcDogJzE5Mi4xNjguMi41MCcsXHJcbiAgICAgICAgdGFyZ2V0X2lwOiBudWxsLFxyXG4gICAgICAgIHBheWxvYWQ6IHtcclxuICAgICAgICAgIGZpbGVfcGF0aDogJ0M6XFxcXFVzZXJzXFxcXHVzZXJcXFxcRG93bmxvYWRzXFxcXGRvY3VtZW50LmV4ZScsXHJcbiAgICAgICAgICBtYWx3YXJlX3R5cGU6ICdUcm9qYW4uR2VuZXJpYycsXHJcbiAgICAgICAgICBzY2FubmVyOiAnV2luZG93cyBEZWZlbmRlcidcclxuICAgICAgICB9LFxyXG4gICAgICAgIG1ldGFkYXRhOiB7XHJcbiAgICAgICAgICBxdWFyYW50aW5lX3N0YXR1czogJ3F1YXJhbnRpbmVkJyxcclxuICAgICAgICAgIHNjYW5fZW5naW5lOiAnTWljcm9zb2Z0IERlZmVuZGVyJ1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAgc3RhdHVzOiAnSU5WRVNUSUdBVElORycsXHJcbiAgICAgICAgZmlyc3Rfc2Vlbl9hdDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDcyMDAwMDApLCAvLyAyIGhvdXJzIGFnb1xyXG4gICAgICAgIGxhc3Rfc2Vlbl9hdDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDcyMDAwMDApLFxyXG4gICAgICAgIG9jY3VycmVuY2VfY291bnQ6IDEsXHJcbiAgICAgICAgcmlza19zY29yZTogOTUuMFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgZXZlbnRfdHlwZTogJ05FVFdPUktfQU5PTUFMWScsXHJcbiAgICAgICAgc2V2ZXJpdHk6ICdNRURJVU0nLFxyXG4gICAgICAgIHRpdGxlOiAnVW51c3VhbCBOZXR3b3JrIFRyYWZmaWMgUGF0dGVybicsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdBYm5vcm1hbCBkYXRhIHRyYW5zZmVyIHZvbHVtZSBkZXRlY3RlZCcsXHJcbiAgICAgICAgc291cmNlX2lwOiAnMTkyLjE2OC4xLjIwJyxcclxuICAgICAgICB0YXJnZXRfaXA6ICcyMDMuMC4xMTMuNTAnLFxyXG4gICAgICAgIHNvdXJjZV9wb3J0OiAzMzA2LFxyXG4gICAgICAgIHRhcmdldF9wb3J0OiA0NDMsXHJcbiAgICAgICAgcHJvdG9jb2w6ICdUQ1AnLFxyXG4gICAgICAgIHBheWxvYWQ6IHtcclxuICAgICAgICAgIGJ5dGVzX3RyYW5zZmVycmVkOiAxMDczNzQxODI0LCAvLyAxR0JcclxuICAgICAgICAgIGR1cmF0aW9uX3NlY29uZHM6IDMwMCxcclxuICAgICAgICAgIG5vcm1hbF9iYXNlbGluZTogMTA0ODU3NjAwIC8vIDEwME1CXHJcbiAgICAgICAgfSxcclxuICAgICAgICBtZXRhZGF0YToge1xyXG4gICAgICAgICAgYmFzZWxpbmVfZGV2aWF0aW9uOiAxMC4wLFxyXG4gICAgICAgICAgdGltZV9vZl9kYXk6ICdvZmZfaG91cnMnXHJcbiAgICAgICAgfSxcclxuICAgICAgICBzdGF0dXM6ICdSRVNPTFZFRCcsXHJcbiAgICAgICAgZmlyc3Rfc2Vlbl9hdDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDg2NDAwMDAwKSwgLy8gMjQgaG91cnMgYWdvXHJcbiAgICAgICAgbGFzdF9zZWVuX2F0OiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gODY0MDAwMDApLFxyXG4gICAgICAgIG9jY3VycmVuY2VfY291bnQ6IDEsXHJcbiAgICAgICAgcmlza19zY29yZTogNjUuMFxyXG4gICAgICB9LFxyXG4gICAgXTtcclxuXHJcbiAgICBmb3IgKGNvbnN0IGV2ZW50IG9mIGV2ZW50cykge1xyXG4gICAgICBhd2FpdCB0aGlzLmRhdGFTb3VyY2UucXVlcnkoYFxyXG4gICAgICAgIElOU0VSVCBJTlRPIHNlY3VyaXR5X2V2ZW50cyAoZXZlbnRfdHlwZSwgc2V2ZXJpdHksIHRpdGxlLCBkZXNjcmlwdGlvbiwgc291cmNlX2lwLCB0YXJnZXRfaXAsIHNvdXJjZV9wb3J0LCB0YXJnZXRfcG9ydCwgcHJvdG9jb2wsIHBheWxvYWQsIG1ldGFkYXRhLCBzdGF0dXMsIGZpcnN0X3NlZW5fYXQsIGxhc3Rfc2Vlbl9hdCwgb2NjdXJyZW5jZV9jb3VudCwgcmlza19zY29yZSlcclxuICAgICAgICBWQUxVRVMgKCQxLCAkMiwgJDMsICQ0LCAkNSwgJDYsICQ3LCAkOCwgJDksICQxMCwgJDExLCAkMTIsICQxMywgJDE0LCAkMTUsICQxNilcclxuICAgICAgYCwgW1xyXG4gICAgICAgIGV2ZW50LmV2ZW50X3R5cGUsIGV2ZW50LnNldmVyaXR5LCBldmVudC50aXRsZSwgZXZlbnQuZGVzY3JpcHRpb24sXHJcbiAgICAgICAgZXZlbnQuc291cmNlX2lwLCBldmVudC50YXJnZXRfaXAsIGV2ZW50LnNvdXJjZV9wb3J0LCBldmVudC50YXJnZXRfcG9ydCxcclxuICAgICAgICBldmVudC5wcm90b2NvbCwgSlNPTi5zdHJpbmdpZnkoZXZlbnQucGF5bG9hZCksIEpTT04uc3RyaW5naWZ5KGV2ZW50Lm1ldGFkYXRhKSxcclxuICAgICAgICBldmVudC5zdGF0dXMsIGV2ZW50LmZpcnN0X3NlZW5fYXQsIGV2ZW50Lmxhc3Rfc2Vlbl9hdCxcclxuICAgICAgICBldmVudC5vY2N1cnJlbmNlX2NvdW50LCBldmVudC5yaXNrX3Njb3JlXHJcbiAgICAgIF0pO1xyXG4gICAgfVxyXG5cclxuICAgIHRoaXMubG9nZ2VyLmRlYnVnKGBTZWVkZWQgJHtldmVudHMubGVuZ3RofSBzZWN1cml0eSBldmVudHNgKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFNlZWQgc2VjdXJpdHkgZXZlbnQgdGFnc1xyXG4gICAqL1xyXG4gIHByaXZhdGUgYXN5bmMgc2VlZFNlY3VyaXR5RXZlbnRUYWdzKCk6IFByb21pc2U8dm9pZD4ge1xyXG4gICAgdGhpcy5sb2dnZXIuZGVidWcoJ1NlZWRpbmcgc2VjdXJpdHkgZXZlbnQgdGFncy4uLicpO1xyXG5cclxuICAgIGNvbnN0IHRhZ3MgPSBbXHJcbiAgICAgIHsgbmFtZTogJ2JydXRlLWZvcmNlJywgZGVzY3JpcHRpb246ICdCcnV0ZSBmb3JjZSBhdHRhY2sgYXR0ZW1wdHMnLCBjb2xvcjogJyNmZjQ0NDQnIH0sXHJcbiAgICAgIHsgbmFtZTogJ21hbHdhcmUnLCBkZXNjcmlwdGlvbjogJ01hbHdhcmUgcmVsYXRlZCBldmVudHMnLCBjb2xvcjogJyNjYzAwMDAnIH0sXHJcbiAgICAgIHsgbmFtZTogJ25ldHdvcmstYW5vbWFseScsIGRlc2NyaXB0aW9uOiAnTmV0d29yayB0cmFmZmljIGFub21hbGllcycsIGNvbG9yOiAnI2ZmODgwMCcgfSxcclxuICAgICAgeyBuYW1lOiAnZmFsc2UtcG9zaXRpdmUnLCBkZXNjcmlwdGlvbjogJ0NvbmZpcm1lZCBmYWxzZSBwb3NpdGl2ZSBldmVudHMnLCBjb2xvcjogJyM4ODg4ODgnIH0sXHJcbiAgICAgIHsgbmFtZTogJ2NyaXRpY2FsLWFzc2V0JywgZGVzY3JpcHRpb246ICdFdmVudHMgYWZmZWN0aW5nIGNyaXRpY2FsIGFzc2V0cycsIGNvbG9yOiAnI2ZmMDAwMCcgfSxcclxuICAgICAgeyBuYW1lOiAnZXh0ZXJuYWwtdGhyZWF0JywgZGVzY3JpcHRpb246ICdUaHJlYXRzIGZyb20gZXh0ZXJuYWwgc291cmNlcycsIGNvbG9yOiAnIzk5MDBjYycgfSxcclxuICAgICAgeyBuYW1lOiAnaW5zaWRlci10aHJlYXQnLCBkZXNjcmlwdGlvbjogJ1BvdGVudGlhbCBpbnNpZGVyIHRocmVhdHMnLCBjb2xvcjogJyNjYzY2MDAnIH0sXHJcbiAgICAgIHsgbmFtZTogJ2F1dG9tYXRlZCcsIGRlc2NyaXB0aW9uOiAnQXV0b21hdGVkIGF0dGFjayBwYXR0ZXJucycsIGNvbG9yOiAnIzAwNjZjYycgfSxcclxuICAgIF07XHJcblxyXG4gICAgZm9yIChjb25zdCB0YWcgb2YgdGFncykge1xyXG4gICAgICBhd2FpdCB0aGlzLmRhdGFTb3VyY2UucXVlcnkoYFxyXG4gICAgICAgIElOU0VSVCBJTlRPIHNlY3VyaXR5X2V2ZW50X3RhZ3MgKG5hbWUsIGRlc2NyaXB0aW9uLCBjb2xvcilcclxuICAgICAgICBWQUxVRVMgKCQxLCAkMiwgJDMpXHJcbiAgICAgICAgT04gQ09ORkxJQ1QgKG5hbWUpIERPIFVQREFURSBTRVRcclxuICAgICAgICAgIGRlc2NyaXB0aW9uID0gRVhDTFVERUQuZGVzY3JpcHRpb24sXHJcbiAgICAgICAgICBjb2xvciA9IEVYQ0xVREVELmNvbG9yXHJcbiAgICAgIGAsIFt0YWcubmFtZSwgdGFnLmRlc2NyaXB0aW9uLCB0YWcuY29sb3JdKTtcclxuICAgIH1cclxuXHJcbiAgICB0aGlzLmxvZ2dlci5kZWJ1ZyhgU2VlZGVkICR7dGFncy5sZW5ndGh9IHNlY3VyaXR5IGV2ZW50IHRhZ3NgKTtcclxuICB9XHJcbn0iXSwidmVyc2lvbiI6M30=