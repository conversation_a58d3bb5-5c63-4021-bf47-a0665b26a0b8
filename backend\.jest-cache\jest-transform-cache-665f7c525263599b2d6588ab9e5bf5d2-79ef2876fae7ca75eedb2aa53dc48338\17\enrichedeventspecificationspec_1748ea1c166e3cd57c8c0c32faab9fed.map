{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\enriched-event.specification.spec.ts", "mappings": ";;AAAA,kFA0ByC;AACzC,gFAA2G;AAC3G,gEAA8D;AAC9D,gHAA+F;AAC/F,kHAAiG;AACjG,4GAA2F;AAC3F,iEAAwD;AACxD,yEAAgE;AAChE,qEAA4D;AAC5D,2FAAiF;AACjF,+EAAqE;AACrE,+EAAsE;AAEtE,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;IAC5C,IAAI,SAA6B,CAAC;IAClC,IAAI,YAA2B,CAAC;IAEhC,UAAU,CAAC,GAAG,EAAE;QACd,uBAAuB;QACvB,MAAM,WAAW,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAC9E,MAAM,cAAc,GAAG,6CAAc,CAAC,MAAM,EAAE,CAAC;QAC/C,YAAY,GAAG,2CAAa,CAAC,MAAM,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAEjE,wCAAwC;QACxC,SAAS,GAAG;YACV,iBAAiB,EAAE,8BAAc,CAAC,MAAM,EAAE;YAC1C,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,2BAAS,CAAC,eAAe;YAC/B,QAAQ,EAAE,mCAAa,CAAC,MAAM;YAC9B,MAAM,EAAE,+BAAW,CAAC,MAAM;YAC1B,gBAAgB,EAAE,oDAAqB,CAAC,QAAQ;YAChD,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;YAC5C,cAAc,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE;YAC1D,YAAY,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE;YACtD,KAAK,EAAE,qBAAqB;YAC5B,YAAY,EAAE,EAAE;YAChB,cAAc,EAAE,EAAE;SACnB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,IAAI,GAAG,IAAI,+DAAgC,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;aAC7C,CAAC,CAAC;YACH,MAAM,YAAY,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACxC,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,OAAO;aAC3C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,IAAI,GAAG,IAAI,4DAA6B,EAAE,CAAC;YACjD,MAAM,WAAW,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACvC,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,MAAM;aAC1C,CAAC,CAAC;YACH,MAAM,cAAc,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;aAC7C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,IAAI,GAAG,IAAI,gEAAiC,EAAE,CAAC;YACrD,MAAM,eAAe,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC3C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,WAAW;aAC/C,CAAC,CAAC;YACH,MAAM,cAAc,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;aAC7C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,IAAI,GAAG,IAAI,6DAA8B,EAAE,CAAC;YAClD,MAAM,YAAY,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACxC,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,OAAO;aAC3C,CAAC,CAAC;YACH,MAAM,cAAc,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;aAC7C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,IAAI,GAAG,IAAI,iEAAkC,EAAE,CAAC;YACtD,MAAM,gBAAgB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC5C,GAAG,SAAS;gBACZ,sBAAsB,EAAE,EAAE;aAC3B,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC3C,GAAG,SAAS;gBACZ,sBAAsB,EAAE,EAAE;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,IAAI,GAAG,IAAI,+DAAgC,EAAE,CAAC;YACpD,MAAM,eAAe,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC3C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;aACzC,CAAC,CAAC;YACH,MAAM,kBAAkB,GAAG,qCAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAE3D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,IAAI,GAAG,IAAI,gEAAiC,EAAE,CAAC;YACrD,MAAM,mBAAmB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC/C,GAAG,SAAS;gBACZ,oBAAoB,EAAE,IAAI;aAC3B,CAAC,CAAC;YACH,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,oBAAoB,EAAE,KAAK;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,IAAI,GAAG,IAAI,6DAA8B,EAAE,CAAC;YAClD,MAAM,UAAU,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACtC,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;gBAC5C,sBAAsB,EAAE,EAAE;gBAC1B,gBAAgB,EAAE,EAAE;gBACpB,oBAAoB,EAAE,KAAK;aAC5B,CAAC,CAAC;YACH,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,MAAM;aAC1C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,IAAI,GAAG,IAAI,0DAA2B,EAAE,CAAC;YAC/C,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,gBAAgB,EAAE,EAAE;aACrB,CAAC,CAAC;YACH,MAAM,YAAY,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACxC,GAAG,SAAS;gBACZ,gBAAgB,EAAE,EAAE;aACrB,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QACjG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,IAAI,GAAG,IAAI,iEAAkC,EAAE,CAAC;YACtD,MAAM,oBAAoB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAChD,GAAG,SAAS;gBACZ,cAAc,EAAE,CAAC;wBACf,MAAM,EAAE,yCAAgB,CAAC,uBAAuB;wBAChD,IAAI,EAAE,qBAAqB;wBAC3B,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;wBACnB,UAAU,EAAE,EAAE;wBACd,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC;aACH,CAAC,CAAC;YACH,MAAM,uBAAuB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACnD,GAAG,SAAS;gBACZ,cAAc,EAAE,CAAC;wBACf,MAAM,EAAE,yCAAgB,CAAC,aAAa;wBACtC,IAAI,EAAE,YAAY;wBAClB,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;wBACnB,UAAU,EAAE,EAAE;wBACd,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,IAAI,GAAG,IAAI,6DAA8B,EAAE,CAAC;YAClD,MAAM,mBAAmB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC/C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;aACjD,CAAC,CAAC;YACH,MAAM,sBAAsB,GAAG,qCAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAE/D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,IAAI,GAAG,IAAI,8DAA+B,EAAE,CAAC;YACnD,MAAM,YAAY,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACxC,GAAG,SAAS;gBACZ,kBAAkB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE;aACxD,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,qCAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAExD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,IAAI,GAAG,IAAI,4DAA6B,CAAC;gBAC7C,wCAAgB,CAAC,SAAS;gBAC1B,wCAAgB,CAAC,OAAO;aACzB,CAAC,CAAC;YACH,MAAM,cAAc,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;aAC7C,CAAC,CAAC;YACH,MAAM,YAAY,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACxC,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,OAAO;aAC3C,CAAC,CAAC;YACH,MAAM,WAAW,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACvC,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,MAAM;aAC1C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACxD,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,IAAI,GAAG,IAAI,uEAAwC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClE,MAAM,gBAAgB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC5C,GAAG,SAAS;gBACZ,sBAAsB,EAAE,EAAE;aAC3B,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC3C,GAAG,SAAS;gBACZ,sBAAsB,EAAE,EAAE;aAC3B,CAAC,CAAC;YACH,MAAM,oBAAoB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAChD,GAAG,SAAS;gBACZ,sBAAsB,EAAE,EAAE;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,IAAI,uEAAwC,CAAC,EAAE,CAAC,CAAC;YAC9D,MAAM,gBAAgB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC5C,GAAG,SAAS;gBACZ,sBAAsB,EAAE,EAAE;aAC3B,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC3C,GAAG,SAAS;gBACZ,sBAAsB,EAAE,EAAE;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,IAAI,uEAAwC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YACzE,MAAM,eAAe,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC3C,GAAG,SAAS;gBACZ,sBAAsB,EAAE,EAAE;aAC3B,CAAC,CAAC;YACH,MAAM,gBAAgB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC5C,GAAG,SAAS;gBACZ,sBAAsB,EAAE,EAAE;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,IAAI,GAAG,IAAI,iEAAkC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAC5D,MAAM,iBAAiB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC7C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,EAAE;aACrB,CAAC,CAAC;YACH,MAAM,cAAc,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,EAAE;aACrB,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC3C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,EAAE;aACrB,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,IAAI,GAAG,IAAI,uDAAwB,CAAC,eAAe,CAAC,CAAC;YAC3D,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,YAAY,EAAE,CAAC;wBACb,EAAE,EAAE,eAAe;wBACnB,IAAI,EAAE,WAAW;wBACjB,WAAW,EAAE,aAAa;wBAC1B,QAAQ,EAAE,GAAG;wBACb,QAAQ,EAAE,KAAK;wBACf,OAAO,EAAE,CAAC,yCAAgB,CAAC,aAAa,CAAC;qBAC1C,CAAC;aACH,CAAC,CAAC;YACH,MAAM,gBAAgB,GAAG,qCAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAEzD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,iBAAiB,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAClD,MAAM,IAAI,GAAG,IAAI,2DAA4B,CAAC,iBAAiB,CAAC,CAAC;YACjE,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,iBAAiB;aAClB,CAAC,CAAC;YACH,MAAM,gBAAgB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC5C,GAAG,SAAS;gBACZ,iBAAiB,EAAE,8BAAc,CAAC,MAAM,EAAE;aAC3C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,+CAA+C,iBAAiB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACpH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,IAAI,GAAG,IAAI,+DAAgC,EAAE,CAAC;YACpD,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,kBAAkB,EAAE,CAAC,EAAE,oBAAoB;aAC5C,CAAC,CAAC;YACH,MAAM,WAAW,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACvC,GAAG,SAAS;gBACZ,kBAAkB,EAAE,CAAC;aACtB,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,IAAI,GAAG,IAAI,yDAA0B,EAAE,CAAC;YAC9C,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,UAAU,EAAE,qBAAqB;aAClC,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,qCAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAExD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,IAAI,GAAG,IAAI,yDAA0B,EAAE,CAAC;YAC9C,MAAM,YAAY,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACxC,GAAG,SAAS;gBACZ,oBAAoB,EAAE,IAAI;gBAC1B,UAAU,EAAE,SAAS;aACtB,CAAC,CAAC;YACH,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,oBAAoB,EAAE,IAAI;gBAC1B,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,oBAAoB,EAAE,KAAK;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sCAAsC,EAAE,GAAG,EAAE;QACpD,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,IAAI,GAAG,IAAI,mEAAoC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAClE,MAAM,SAAS,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACrC,GAAG,SAAS;gBACZ,mBAAmB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;gBAChD,qBAAqB,EAAE,IAAI,IAAI,EAAE;aAClC,CAAC,CAAC;YACH,MAAM,SAAS,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACrC,GAAG,SAAS;gBACZ,mBAAmB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;gBACjD,qBAAqB,EAAE,IAAI,IAAI,EAAE;aAClC,CAAC,CAAC;YACH,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,mBAAmB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC;gBAC/C,qBAAqB,EAAE,IAAI,IAAI,EAAE;aAClC,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,IAAI,GAAG,IAAI,mEAAoC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAClE,MAAM,oBAAoB,GAAG,qCAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAE7D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,IAAI,GAAG,IAAI,4DAA6B,CAAC;gBAC7C,yCAAgB,CAAC,aAAa;gBAC9B,yCAAgB,CAAC,mBAAmB;aACrC,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC3C,GAAG,SAAS;gBACZ,cAAc,EAAE,CAAC;wBACf,MAAM,EAAE,yCAAgB,CAAC,aAAa;wBACtC,IAAI,EAAE,YAAY;wBAClB,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;wBACnB,UAAU,EAAE,EAAE;wBACd,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC;aACH,CAAC,CAAC;YACH,MAAM,kBAAkB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC9C,GAAG,SAAS;gBACZ,cAAc,EAAE,CAAC;wBACf,MAAM,EAAE,yCAAgB,CAAC,cAAc;wBACvC,IAAI,EAAE,aAAa;wBACnB,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;wBACvB,UAAU,EAAE,EAAE;wBACd,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;QACjH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,IAAI,GAAG,IAAI,8DAA+B,CAAC,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC,CAAC;YAChF,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,cAAc,EAAE,CAAC;wBACf,MAAM,EAAE,yCAAgB,CAAC,aAAa;wBACtC,IAAI,EAAE,YAAY;wBAClB,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;wBACnB,UAAU,EAAE,EAAE;wBACd,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC;aACH,CAAC,CAAC;YACH,MAAM,gBAAgB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC5C,GAAG,SAAS;gBACZ,cAAc,EAAE,CAAC;wBACf,MAAM,EAAE,yCAAgB,CAAC,mBAAmB;wBAC5C,IAAI,EAAE,qBAAqB;wBAC3B,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;wBACnB,UAAU,EAAE,EAAE;wBACd,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACxD,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,IAAI,GAAG,IAAI,uEAAwC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClE,MAAM,qBAAqB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACjD,GAAG,SAAS;gBACZ,gBAAgB,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,gBAAgB;aACjE,CAAC,CAAC;YACH,MAAM,kBAAkB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC9C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,cAAc;aAC/D,CAAC,CAAC;YACH,MAAM,mBAAmB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC/C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,gBAAgB;aACjE,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,IAAI,GAAG,IAAI,uEAAwC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClE,MAAM,kBAAkB,GAAG,qCAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAE3D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB;QACrF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,IAAI,GAAG,gEAAiC;iBAC3C,MAAM,EAAE;iBACR,mBAAmB,EAAE;iBACrB,KAAK,EAAE,CAAC;YAEX,MAAM,cAAc,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;aAC7C,CAAC,CAAC;YACH,MAAM,YAAY,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACxC,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,OAAO;aAC3C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,IAAI,GAAG,gEAAiC;iBAC3C,MAAM,EAAE;iBACR,mBAAmB,EAAE;iBACrB,qBAAqB,EAAE;iBACvB,KAAK,EAAE,CAAC;YAEX,MAAM,SAAS,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACrC,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;gBAC5C,sBAAsB,EAAE,EAAE;aAC3B,CAAC,CAAC;YACH,MAAM,wBAAwB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACpD,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;gBAC5C,sBAAsB,EAAE,EAAE;aAC3B,CAAC,CAAC;YACH,MAAM,uBAAuB,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACnD,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,OAAO;gBAC1C,sBAAsB,EAAE,EAAE;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,IAAI,GAAG,gEAAiC;iBAC3C,MAAM,EAAE;iBACR,mBAAmB,EAAE;iBACrB,iBAAiB,EAAE;iBACnB,WAAW,EAAE,CAAC;YAEjB,MAAM,cAAc,GAAG,qCAAa,CAAC,MAAM,CAAC;gBAC1C,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;aAC7C,CAAC,CAAC;YACH,MAAM,YAAY,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACxC,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,OAAO;aAC3C,CAAC,CAAC;YACH,MAAM,WAAW,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACvC,GAAG,SAAS;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,MAAM;aAC1C,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,iBAAiB,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAClD,MAAM,IAAI,GAAG,gEAAiC;iBAC3C,MAAM,EAAE;iBACR,mBAAmB,EAAE;iBACrB,qBAAqB,EAAE;iBACvB,qBAAqB,EAAE;iBACvB,mBAAmB,CAAC,iBAAiB,CAAC;iBACtC,2BAA2B,CAAC,EAAE,EAAE,EAAE,CAAC;iBACnC,qBAAqB,CAAC,yCAAgB,CAAC,uBAAuB,CAAC;iBAC/D,KAAK,EAAE,CAAC;YAEX,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC;gBACzC,GAAG,SAAS;gBACZ,iBAAiB;gBACjB,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;gBAC5C,sBAAsB,EAAE,EAAE;gBAC1B,cAAc,EAAE,CAAC;wBACf,MAAM,EAAE,yCAAgB,CAAC,uBAAuB;wBAChD,IAAI,EAAE,qBAAqB;wBAC3B,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;wBACnB,UAAU,EAAE,EAAE;wBACd,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,CAAC,GAAG,EAAE;gBACV,gEAAiC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC;YACrD,CAAC,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,OAAO,GAAG,gEAAiC,CAAC,MAAM,EAAE,CAAC;YAE3D,wDAAwD;YACxD,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,wCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/E,MAAM,CAAC,OAAO,CAAC,2BAA2B,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClE,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3D,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,8BAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3E,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClE,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpF,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,CAAC,OAAO,CAAC,2BAA2B,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\enriched-event.specification.spec.ts"], "sourcesContent": ["import {\r\n  EnrichedEventSpecificationBuilder,\r\n  EnrichmentCompletedSpecification,\r\n  EnrichmentFailedSpecification,\r\n  EnrichmentInProgressSpecification,\r\n  EnrichmentPartialSpecification,\r\n  HighEnrichmentQualitySpecification,\r\n  HasValidationErrorsSpecification,\r\n  RequiresManualReviewSpecification,\r\n  ReadyForNextStageSpecification,\r\n  HighThreatRiskSpecification,\r\n  HasThreatIntelligenceSpecification,\r\n  HasReputationDataSpecification,\r\n  HasGeolocationDataSpecification,\r\n  EnrichmentStatusSpecification,\r\n  EnrichmentQualityScoreRangeSpecification,\r\n  ThreatIntelScoreRangeSpecification,\r\n  AppliedRuleSpecification,\r\n  NormalizedEventSpecification,\r\n  ExceededMaxAttemptsSpecification,\r\n  ReviewedEventSpecification,\r\n  PendingReviewSpecification,\r\n  EnrichmentDurationRangeSpecification,\r\n  EnrichmentSourceSpecification,\r\n  EnrichmentDataTypeSpecification,\r\n  AverageReputationScoreRangeSpecification,\r\n} from '../enriched-event.specification';\r\nimport { EnrichedEvent, EnrichedEventProps, EnrichmentStatus } from '../../entities/enriched-event.entity';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { EventMetadata } from '../../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../value-objects/event-metadata/event-source.value-object';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventStatus } from '../../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../../enums/event-source-type.enum';\r\nimport { EnrichmentSource } from '../../enums/enrichment-source.enum';\r\n\r\ndescribe('EnrichedEvent Specifications', () => {\r\n  let baseProps: EnrichedEventProps;\r\n  let mockMetadata: EventMetadata;\r\n\r\n  beforeEach(() => {\r\n    // Create mock metadata\r\n    const eventSource = EventSource.create(EventSourceType.SIEM, 'test-siem-001');\r\n    const eventTimestamp = EventTimestamp.create();\r\n    mockMetadata = EventMetadata.create(eventTimestamp, eventSource);\r\n\r\n    // Create base props for enriched events\r\n    baseProps = {\r\n      normalizedEventId: UniqueEntityId.create(),\r\n      metadata: mockMetadata,\r\n      type: EventType.THREAT_DETECTED,\r\n      severity: EventSeverity.MEDIUM,\r\n      status: EventStatus.ACTIVE,\r\n      processingStatus: EventProcessingStatus.ENRICHED,\r\n      enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n      normalizedData: { event_type: 'threat', normalized: true },\r\n      enrichedData: { event_type: 'threat', enriched: true },\r\n      title: 'Test Enriched Event',\r\n      appliedRules: [],\r\n      enrichmentData: [],\r\n    };\r\n  });\r\n\r\n  describe('EnrichmentCompletedSpecification', () => {\r\n    it('should match events with completed enrichment', () => {\r\n      const spec = new EnrichmentCompletedSpecification();\r\n      const completedEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n      });\r\n      const pendingEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.PENDING,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(completedEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(pendingEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has completed enrichment');\r\n    });\r\n  });\r\n\r\n  describe('EnrichmentFailedSpecification', () => {\r\n    it('should match events with failed enrichment', () => {\r\n      const spec = new EnrichmentFailedSpecification();\r\n      const failedEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.FAILED,\r\n      });\r\n      const completedEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(failedEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(completedEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has failed enrichment');\r\n    });\r\n  });\r\n\r\n  describe('EnrichmentInProgressSpecification', () => {\r\n    it('should match events with in-progress enrichment', () => {\r\n      const spec = new EnrichmentInProgressSpecification();\r\n      const inProgressEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.IN_PROGRESS,\r\n      });\r\n      const completedEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(inProgressEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(completedEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event is currently being enriched');\r\n    });\r\n  });\r\n\r\n  describe('EnrichmentPartialSpecification', () => {\r\n    it('should match events with partial enrichment', () => {\r\n      const spec = new EnrichmentPartialSpecification();\r\n      const partialEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.PARTIAL,\r\n      });\r\n      const completedEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(partialEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(completedEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has partial enrichment results');\r\n    });\r\n  });\r\n\r\n  describe('HighEnrichmentQualitySpecification', () => {\r\n    it('should match events with high enrichment quality', () => {\r\n      const spec = new HighEnrichmentQualitySpecification();\r\n      const highQualityEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentQualityScore: 85,\r\n      });\r\n      const lowQualityEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentQualityScore: 50,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(highQualityEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(lowQualityEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has high enrichment quality (>= 70)');\r\n    });\r\n  });\r\n\r\n  describe('HasValidationErrorsSpecification', () => {\r\n    it('should match events with validation errors', () => {\r\n      const spec = new HasValidationErrorsSpecification();\r\n      const eventWithErrors = EnrichedEvent.create({\r\n        ...baseProps,\r\n        validationErrors: ['Error 1', 'Error 2'],\r\n      });\r\n      const eventWithoutErrors = EnrichedEvent.create(baseProps);\r\n\r\n      expect(spec.isSatisfiedBy(eventWithErrors)).toBe(true);\r\n      expect(spec.isSatisfiedBy(eventWithoutErrors)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has validation errors');\r\n    });\r\n  });\r\n\r\n  describe('RequiresManualReviewSpecification', () => {\r\n    it('should match events requiring manual review', () => {\r\n      const spec = new RequiresManualReviewSpecification();\r\n      const reviewRequiredEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        requiresManualReview: true,\r\n      });\r\n      const noReviewEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        requiresManualReview: false,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(reviewRequiredEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(noReviewEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event requires manual review');\r\n    });\r\n  });\r\n\r\n  describe('ReadyForNextStageSpecification', () => {\r\n    it('should match events ready for next stage', () => {\r\n      const spec = new ReadyForNextStageSpecification();\r\n      const readyEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n        enrichmentQualityScore: 85,\r\n        validationErrors: [],\r\n        requiresManualReview: false,\r\n      });\r\n      const notReadyEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.FAILED,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(readyEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(notReadyEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event is ready for next processing stage');\r\n    });\r\n  });\r\n\r\n  describe('HighThreatRiskSpecification', () => {\r\n    it('should match events with high threat risk', () => {\r\n      const spec = new HighThreatRiskSpecification();\r\n      const highRiskEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        threatIntelScore: 90,\r\n      });\r\n      const lowRiskEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        threatIntelScore: 30,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(highRiskEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(lowRiskEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has high threat intelligence risk (>= 85)');\r\n    });\r\n  });\r\n\r\n  describe('HasThreatIntelligenceSpecification', () => {\r\n    it('should match events with threat intelligence data', () => {\r\n      const spec = new HasThreatIntelligenceSpecification();\r\n      const eventWithThreatIntel = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentData: [{\r\n          source: EnrichmentSource.COMMERCIAL_THREAT_INTEL,\r\n          type: 'threat_intelligence',\r\n          data: { score: 75 },\r\n          confidence: 85,\r\n          timestamp: new Date(),\r\n        }],\r\n      });\r\n      const eventWithoutThreatIntel = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentData: [{\r\n          source: EnrichmentSource.IP_REPUTATION,\r\n          type: 'reputation',\r\n          data: { score: 75 },\r\n          confidence: 85,\r\n          timestamp: new Date(),\r\n        }],\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(eventWithThreatIntel)).toBe(true);\r\n      expect(spec.isSatisfiedBy(eventWithoutThreatIntel)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has threat intelligence data');\r\n    });\r\n  });\r\n\r\n  describe('HasReputationDataSpecification', () => {\r\n    it('should match events with reputation data', () => {\r\n      const spec = new HasReputationDataSpecification();\r\n      const eventWithReputation = EnrichedEvent.create({\r\n        ...baseProps,\r\n        reputationScores: { virustotal: 85, shodan: 70 },\r\n      });\r\n      const eventWithoutReputation = EnrichedEvent.create(baseProps);\r\n\r\n      expect(spec.isSatisfiedBy(eventWithReputation)).toBe(true);\r\n      expect(spec.isSatisfiedBy(eventWithoutReputation)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has reputation data');\r\n    });\r\n  });\r\n\r\n  describe('HasGeolocationDataSpecification', () => {\r\n    it('should match events with geolocation data', () => {\r\n      const spec = new HasGeolocationDataSpecification();\r\n      const eventWithGeo = EnrichedEvent.create({\r\n        ...baseProps,\r\n        geolocationContext: { country: 'US', city: 'New York' },\r\n      });\r\n      const eventWithoutGeo = EnrichedEvent.create(baseProps);\r\n\r\n      expect(spec.isSatisfiedBy(eventWithGeo)).toBe(true);\r\n      expect(spec.isSatisfiedBy(eventWithoutGeo)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has geolocation data');\r\n    });\r\n  });\r\n\r\n  describe('EnrichmentStatusSpecification', () => {\r\n    it('should match events with specified statuses', () => {\r\n      const spec = new EnrichmentStatusSpecification([\r\n        EnrichmentStatus.COMPLETED,\r\n        EnrichmentStatus.PARTIAL,\r\n      ]);\r\n      const completedEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n      });\r\n      const partialEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.PARTIAL,\r\n      });\r\n      const failedEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.FAILED,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(completedEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(partialEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(failedEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event status is one of: COMPLETED, PARTIAL');\r\n    });\r\n  });\r\n\r\n  describe('EnrichmentQualityScoreRangeSpecification', () => {\r\n    it('should match events within quality score range', () => {\r\n      const spec = new EnrichmentQualityScoreRangeSpecification(70, 90);\r\n      const highQualityEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentQualityScore: 85,\r\n      });\r\n      const lowQualityEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentQualityScore: 50,\r\n      });\r\n      const veryHighQualityEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentQualityScore: 95,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(highQualityEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(lowQualityEvent)).toBe(false);\r\n      expect(spec.isSatisfiedBy(veryHighQualityEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event quality score is between 70 and 90');\r\n    });\r\n\r\n    it('should handle min-only range', () => {\r\n      const spec = new EnrichmentQualityScoreRangeSpecification(80);\r\n      const highQualityEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentQualityScore: 85,\r\n      });\r\n      const lowQualityEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentQualityScore: 75,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(highQualityEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(lowQualityEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event quality score is at least 80');\r\n    });\r\n\r\n    it('should handle max-only range', () => {\r\n      const spec = new EnrichmentQualityScoreRangeSpecification(undefined, 80);\r\n      const lowQualityEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentQualityScore: 75,\r\n      });\r\n      const highQualityEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentQualityScore: 85,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(lowQualityEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(highQualityEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event quality score is at most 80');\r\n    });\r\n  });\r\n\r\n  describe('ThreatIntelScoreRangeSpecification', () => {\r\n    it('should match events within threat intelligence score range', () => {\r\n      const spec = new ThreatIntelScoreRangeSpecification(50, 80);\r\n      const mediumThreatEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        threatIntelScore: 65,\r\n      });\r\n      const lowThreatEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        threatIntelScore: 30,\r\n      });\r\n      const highThreatEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        threatIntelScore: 90,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(mediumThreatEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(lowThreatEvent)).toBe(false);\r\n      expect(spec.isSatisfiedBy(highThreatEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event threat intelligence score is between 50 and 80');\r\n    });\r\n  });\r\n\r\n  describe('AppliedRuleSpecification', () => {\r\n    it('should match events with specific applied rule', () => {\r\n      const spec = new AppliedRuleSpecification('test-rule-001');\r\n      const eventWithRule = EnrichedEvent.create({\r\n        ...baseProps,\r\n        appliedRules: [{\r\n          id: 'test-rule-001',\r\n          name: 'Test Rule',\r\n          description: 'A test rule',\r\n          priority: 100,\r\n          required: false,\r\n          sources: [EnrichmentSource.IP_REPUTATION],\r\n        }],\r\n      });\r\n      const eventWithoutRule = EnrichedEvent.create(baseProps);\r\n\r\n      expect(spec.isSatisfiedBy(eventWithRule)).toBe(true);\r\n      expect(spec.isSatisfiedBy(eventWithoutRule)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has applied rule: test-rule-001');\r\n    });\r\n  });\r\n\r\n  describe('NormalizedEventSpecification', () => {\r\n    it('should match events from specific normalized event', () => {\r\n      const normalizedEventId = UniqueEntityId.create();\r\n      const spec = new NormalizedEventSpecification(normalizedEventId);\r\n      const matchingEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        normalizedEventId,\r\n      });\r\n      const nonMatchingEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        normalizedEventId: UniqueEntityId.create(),\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(matchingEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(nonMatchingEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe(`Enriched event references normalized event: ${normalizedEventId.toString()}`);\r\n    });\r\n  });\r\n\r\n  describe('ExceededMaxAttemptsSpecification', () => {\r\n    it('should match events that exceeded max attempts', () => {\r\n      const spec = new ExceededMaxAttemptsSpecification();\r\n      const exceededEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentAttempts: 5, // Assuming max is 3\r\n      });\r\n      const normalEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentAttempts: 2,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(exceededEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(normalEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has exceeded maximum enrichment attempts');\r\n    });\r\n  });\r\n\r\n  describe('ReviewedEventSpecification', () => {\r\n    it('should match events that have been reviewed', () => {\r\n      const spec = new ReviewedEventSpecification();\r\n      const reviewedEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        reviewedAt: new Date(),\r\n        reviewedBy: '<EMAIL>',\r\n      });\r\n      const unreviewedEvent = EnrichedEvent.create(baseProps);\r\n\r\n      expect(spec.isSatisfiedBy(reviewedEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(unreviewedEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has been manually reviewed');\r\n    });\r\n  });\r\n\r\n  describe('PendingReviewSpecification', () => {\r\n    it('should match events pending manual review', () => {\r\n      const spec = new PendingReviewSpecification();\r\n      const pendingEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        requiresManualReview: true,\r\n        reviewedAt: undefined,\r\n      });\r\n      const reviewedEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        requiresManualReview: true,\r\n        reviewedAt: new Date(),\r\n      });\r\n      const noReviewEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        requiresManualReview: false,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(pendingEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(reviewedEvent)).toBe(false);\r\n      expect(spec.isSatisfiedBy(noReviewEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event is pending manual review');\r\n    });\r\n  });\r\n\r\n  describe('EnrichmentDurationRangeSpecification', () => {\r\n    it('should match events within duration range', () => {\r\n      const spec = new EnrichmentDurationRangeSpecification(1000, 5000);\r\n      const fastEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStartedAt: new Date(Date.now() - 3000),\r\n        enrichmentCompletedAt: new Date(),\r\n      });\r\n      const slowEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStartedAt: new Date(Date.now() - 10000),\r\n        enrichmentCompletedAt: new Date(),\r\n      });\r\n      const veryFastEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStartedAt: new Date(Date.now() - 500),\r\n        enrichmentCompletedAt: new Date(),\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(fastEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(slowEvent)).toBe(false);\r\n      expect(spec.isSatisfiedBy(veryFastEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event duration is between 1000ms and 5000ms');\r\n    });\r\n\r\n    it('should not match events without duration data', () => {\r\n      const spec = new EnrichmentDurationRangeSpecification(1000, 5000);\r\n      const eventWithoutDuration = EnrichedEvent.create(baseProps);\r\n\r\n      expect(spec.isSatisfiedBy(eventWithoutDuration)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('EnrichmentSourceSpecification', () => {\r\n    it('should match events with data from specified sources', () => {\r\n      const spec = new EnrichmentSourceSpecification([\r\n        EnrichmentSource.IP_REPUTATION,\r\n        EnrichmentSource.THREAT_INTELLIGENCE,\r\n      ]);\r\n      const eventWithSource = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentData: [{\r\n          source: EnrichmentSource.IP_REPUTATION,\r\n          type: 'reputation',\r\n          data: { score: 75 },\r\n          confidence: 85,\r\n          timestamp: new Date(),\r\n        }],\r\n      });\r\n      const eventWithoutSource = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentData: [{\r\n          source: EnrichmentSource.IP_GEOLOCATION,\r\n          type: 'geolocation',\r\n          data: { country: 'US' },\r\n          confidence: 95,\r\n          timestamp: new Date(),\r\n        }],\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(eventWithSource)).toBe(true);\r\n      expect(spec.isSatisfiedBy(eventWithoutSource)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has data from sources: ip_reputation, threat_intelligence');\r\n    });\r\n  });\r\n\r\n  describe('EnrichmentDataTypeSpecification', () => {\r\n    it('should match events with data of specified types', () => {\r\n      const spec = new EnrichmentDataTypeSpecification(['reputation', 'geolocation']);\r\n      const eventWithType = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentData: [{\r\n          source: EnrichmentSource.IP_REPUTATION,\r\n          type: 'reputation',\r\n          data: { score: 75 },\r\n          confidence: 85,\r\n          timestamp: new Date(),\r\n        }],\r\n      });\r\n      const eventWithoutType = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentData: [{\r\n          source: EnrichmentSource.THREAT_INTELLIGENCE,\r\n          type: 'threat_intelligence',\r\n          data: { score: 60 },\r\n          confidence: 80,\r\n          timestamp: new Date(),\r\n        }],\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(eventWithType)).toBe(true);\r\n      expect(spec.isSatisfiedBy(eventWithoutType)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event has data of types: reputation, geolocation');\r\n    });\r\n  });\r\n\r\n  describe('AverageReputationScoreRangeSpecification', () => {\r\n    it('should match events within average reputation score range', () => {\r\n      const spec = new AverageReputationScoreRangeSpecification(60, 80);\r\n      const mediumReputationEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        reputationScores: { source1: 70, source2: 75 }, // Average: 72.5\r\n      });\r\n      const lowReputationEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        reputationScores: { source1: 40, source2: 50 }, // Average: 45\r\n      });\r\n      const highReputationEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        reputationScores: { source1: 90, source2: 95 }, // Average: 92.5\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(mediumReputationEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(lowReputationEvent)).toBe(false);\r\n      expect(spec.isSatisfiedBy(highReputationEvent)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Enriched event average reputation score is between 60 and 80');\r\n    });\r\n\r\n    it('should handle events without reputation scores', () => {\r\n      const spec = new AverageReputationScoreRangeSpecification(60, 80);\r\n      const eventWithoutScores = EnrichedEvent.create(baseProps);\r\n\r\n      expect(spec.isSatisfiedBy(eventWithoutScores)).toBe(true); // No min score required\r\n    });\r\n  });\r\n\r\n  describe('EnrichedEventSpecificationBuilder', () => {\r\n    it('should build specification with single condition', () => {\r\n      const spec = EnrichedEventSpecificationBuilder\r\n        .create()\r\n        .enrichmentCompleted()\r\n        .build();\r\n\r\n      const completedEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n      });\r\n      const pendingEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.PENDING,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(completedEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(pendingEvent)).toBe(false);\r\n    });\r\n\r\n    it('should build specification with multiple AND conditions', () => {\r\n      const spec = EnrichedEventSpecificationBuilder\r\n        .create()\r\n        .enrichmentCompleted()\r\n        .highEnrichmentQuality()\r\n        .build();\r\n\r\n      const goodEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n        enrichmentQualityScore: 85,\r\n      });\r\n      const completedLowQualityEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n        enrichmentQualityScore: 50,\r\n      });\r\n      const pendingHighQualityEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.PENDING,\r\n        enrichmentQualityScore: 85,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(goodEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(completedLowQualityEvent)).toBe(false);\r\n      expect(spec.isSatisfiedBy(pendingHighQualityEvent)).toBe(false);\r\n    });\r\n\r\n    it('should build specification with multiple OR conditions', () => {\r\n      const spec = EnrichedEventSpecificationBuilder\r\n        .create()\r\n        .enrichmentCompleted()\r\n        .enrichmentPartial()\r\n        .buildWithOr();\r\n\r\n      const completedEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n      });\r\n      const partialEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.PARTIAL,\r\n      });\r\n      const failedEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        enrichmentStatus: EnrichmentStatus.FAILED,\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(completedEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(partialEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(failedEvent)).toBe(false);\r\n    });\r\n\r\n    it('should build complex specification with various conditions', () => {\r\n      const normalizedEventId = UniqueEntityId.create();\r\n      const spec = EnrichedEventSpecificationBuilder\r\n        .create()\r\n        .enrichmentCompleted()\r\n        .highEnrichmentQuality()\r\n        .hasThreatIntelligence()\r\n        .fromNormalizedEvent(normalizedEventId)\r\n        .enrichmentQualityScoreRange(80, 95)\r\n        .withEnrichmentSources(EnrichmentSource.COMMERCIAL_THREAT_INTEL)\r\n        .build();\r\n\r\n      const matchingEvent = EnrichedEvent.create({\r\n        ...baseProps,\r\n        normalizedEventId,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n        enrichmentQualityScore: 85,\r\n        enrichmentData: [{\r\n          source: EnrichmentSource.COMMERCIAL_THREAT_INTEL,\r\n          type: 'threat_intelligence',\r\n          data: { score: 75 },\r\n          confidence: 85,\r\n          timestamp: new Date(),\r\n        }],\r\n      });\r\n\r\n      expect(spec.isSatisfiedBy(matchingEvent)).toBe(true);\r\n    });\r\n\r\n    it('should throw error when building without conditions', () => {\r\n      expect(() => {\r\n        EnrichedEventSpecificationBuilder.create().build();\r\n      }).toThrow('At least one specification must be added');\r\n    });\r\n\r\n    it('should support all builder methods', () => {\r\n      const builder = EnrichedEventSpecificationBuilder.create();\r\n\r\n      // Test that all methods return the builder for chaining\r\n      expect(builder.enrichmentCompleted()).toBe(builder);\r\n      expect(builder.enrichmentFailed()).toBe(builder);\r\n      expect(builder.enrichmentInProgress()).toBe(builder);\r\n      expect(builder.enrichmentPartial()).toBe(builder);\r\n      expect(builder.highEnrichmentQuality()).toBe(builder);\r\n      expect(builder.hasValidationErrors()).toBe(builder);\r\n      expect(builder.requiresManualReview()).toBe(builder);\r\n      expect(builder.readyForNextStage()).toBe(builder);\r\n      expect(builder.highThreatRisk()).toBe(builder);\r\n      expect(builder.hasThreatIntelligence()).toBe(builder);\r\n      expect(builder.hasReputationData()).toBe(builder);\r\n      expect(builder.hasGeolocationData()).toBe(builder);\r\n      expect(builder.withEnrichmentStatus(EnrichmentStatus.COMPLETED)).toBe(builder);\r\n      expect(builder.enrichmentQualityScoreRange(70, 90)).toBe(builder);\r\n      expect(builder.threatIntelScoreRange(50, 80)).toBe(builder);\r\n      expect(builder.withAppliedRule('test-rule')).toBe(builder);\r\n      expect(builder.fromNormalizedEvent(UniqueEntityId.create())).toBe(builder);\r\n      expect(builder.exceededMaxAttempts()).toBe(builder);\r\n      expect(builder.reviewed()).toBe(builder);\r\n      expect(builder.pendingReview()).toBe(builder);\r\n      expect(builder.enrichmentDurationRange(1000, 5000)).toBe(builder);\r\n      expect(builder.withEnrichmentSources(EnrichmentSource.IP_REPUTATION)).toBe(builder);\r\n      expect(builder.withEnrichmentDataTypes('reputation')).toBe(builder);\r\n      expect(builder.averageReputationScoreRange(60, 80)).toBe(builder);\r\n    });\r\n  });\r\n});"], "version": 3}