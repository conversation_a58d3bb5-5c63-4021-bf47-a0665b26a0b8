c4a64e2319c6a12ed7ebe69a1d6840a1
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIModelConfigurationUpdatedEvent = void 0;
const base_domain_event_1 = require("../../../../shared-kernel/domain/base-domain-event");
/**
 * AI Model Configuration Updated Domain Event
 *
 * Published when an AI model's configuration is updated.
 * This event can trigger various downstream processes such as
 * model redeployment, configuration validation, and monitoring updates.
 */
class AIModelConfigurationUpdatedEvent extends base_domain_event_1.BaseDomainEvent {
    constructor(modelId, configuration, eventId, occurredOn) {
        super(eventId, occurredOn);
        this.modelId = modelId;
        this.configuration = configuration;
    }
    getEventName() {
        return 'AIModelConfigurationUpdated';
    }
    getEventVersion() {
        return '1.0';
    }
    getEventData() {
        return {
            modelId: this.modelId.toString(),
            configuration: {
                endpoint: this.configuration.endpoint,
                timeout: this.configuration.timeout,
                retries: this.configuration.retries,
                batchSize: this.configuration.batchSize,
                customSettings: this.configuration.customSettings,
                // Note: apiKey is intentionally excluded for security
            },
        };
    }
}
exports.AIModelConfigurationUpdatedEvent = AIModelConfigurationUpdatedEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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