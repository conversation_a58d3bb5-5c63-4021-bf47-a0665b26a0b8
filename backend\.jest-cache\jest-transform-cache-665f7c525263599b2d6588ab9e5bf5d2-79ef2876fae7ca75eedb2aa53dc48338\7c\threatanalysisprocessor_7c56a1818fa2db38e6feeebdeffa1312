cb7ad7f996847c69ae490ef54554e0c1
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ThreatAnalysisProcessor_1;
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatAnalysisProcessor = void 0;
const bull_1 = require("@nestjs/bull");
const common_1 = require("@nestjs/common");
const bull_2 = require("bull");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const threat_intelligence_entity_1 = require("../../domain/entities/threat-intelligence.entity");
const threat_actor_entity_1 = require("../../domain/entities/threat-actor.entity");
const indicator_of_compromise_entity_1 = require("../../domain/entities/indicator-of-compromise.entity");
const threat_intelligence_service_1 = require("../../application/services/threat-intelligence.service");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const notification_service_1 = require("../../../../infrastructure/notification/notification.service");
/**
 * Processor for threat analysis and intelligence operations
 * Handles attribution analysis, threat scoring, and correlation tasks
 */
let ThreatAnalysisProcessor = ThreatAnalysisProcessor_1 = class ThreatAnalysisProcessor {
    constructor(threatRepository, actorRepository, iocRepository, threatIntelligenceService, loggerService, notificationService) {
        this.threatRepository = threatRepository;
        this.actorRepository = actorRepository;
        this.iocRepository = iocRepository;
        this.threatIntelligenceService = threatIntelligenceService;
        this.loggerService = loggerService;
        this.notificationService = notificationService;
        this.logger = new common_1.Logger(ThreatAnalysisProcessor_1.name);
    }
    /**
     * Process threat attribution analysis
     */
    async handleAttributionAnalysis(job) {
        const { threatId, userId } = job.data;
        this.logger.debug('Processing attribution analysis', {
            jobId: job.id,
            threatId,
            userId,
        });
        try {
            const threat = await this.threatRepository.findOne({
                where: { id: threatId },
                relations: ['indicators', 'threatActor'],
            });
            if (!threat) {
                throw new Error(`Threat intelligence not found: ${threatId}`);
            }
            await job.progress(25);
            // Analyze threat characteristics for attribution
            const attributionFactors = await this.analyzeAttributionFactors(threat);
            await job.progress(50);
            // Find potential threat actors based on analysis
            const suggestedActors = await this.findPotentialActors(attributionFactors);
            await job.progress(75);
            // Calculate overall confidence
            const confidence = this.calculateAttributionConfidence(attributionFactors, suggestedActors);
            const result = {
                threatId,
                suggestedActors,
                confidence,
                analysisDate: new Date(),
            };
            // Update threat with attribution if confidence is high enough
            if (confidence > 0.8 && suggestedActors.length > 0 && !threat.threatActorId) {
                const topActor = suggestedActors[0];
                threat.threatActorId = topActor.actorId;
                threat.isAttributed = true;
                threat.customAttributes = {
                    ...threat.customAttributes,
                    attributionAnalysis: result,
                    attributionDate: new Date(),
                };
                await this.threatRepository.save(threat);
                this.logger.log('Threat attributed to actor', {
                    threatId,
                    actorId: topActor.actorId,
                    actorName: topActor.actorName,
                    confidence,
                });
            }
            await job.progress(100);
            this.logger.log('Attribution analysis completed', {
                jobId: job.id,
                threatId,
                suggestedActors: suggestedActors.length,
                confidence,
            });
            return result;
        }
        catch (error) {
            this.loggerService.error('Attribution analysis failed', {
                jobId: job.id,
                threatId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Process threat scoring analysis
     */
    async handleThreatScoring(job) {
        const { threatId, userId } = job.data;
        this.logger.debug('Processing threat scoring', {
            jobId: job.id,
            threatId,
            userId,
        });
        try {
            const threat = await this.threatRepository.findOne({
                where: { id: threatId },
                relations: ['indicators', 'threatActor', 'threatCampaign'],
            });
            if (!threat) {
                throw new Error(`Threat intelligence not found: ${threatId}`);
            }
            const oldScore = threat.riskScore;
            await job.progress(25);
            // Calculate new risk score with detailed factors
            const scoringFactors = await this.calculateScoringFactors(threat);
            await job.progress(50);
            const newScore = this.calculateWeightedScore(scoringFactors);
            await job.progress(75);
            // Update threat with new score
            threat.riskScore = newScore;
            await this.threatRepository.save(threat);
            const result = {
                threatId,
                oldScore,
                newScore,
                factors: scoringFactors,
                analysisDate: new Date(),
            };
            await job.progress(100);
            this.logger.log('Threat scoring completed', {
                jobId: job.id,
                threatId,
                oldScore,
                newScore,
                change: newScore - (oldScore || 0),
            });
            // Send notification for significant score changes
            if (oldScore && Math.abs(newScore - oldScore) > 2.0) {
                await this.sendScoreChangeNotification(threat, oldScore, newScore, userId);
            }
            return result;
        }
        catch (error) {
            this.loggerService.error('Threat scoring failed', {
                jobId: job.id,
                threatId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Process IOC enrichment
     */
    async handleIOCEnrichment(job) {
        const { iocIds, userId } = job.data;
        this.logger.debug('Processing IOC enrichment', {
            jobId: job.id,
            iocCount: iocIds?.length,
            userId,
        });
        try {
            if (!iocIds || iocIds.length === 0) {
                throw new Error('No IOC IDs provided for enrichment');
            }
            const totalIOCs = iocIds.length;
            let processedCount = 0;
            let enrichedCount = 0;
            for (const iocId of iocIds) {
                try {
                    const ioc = await this.iocRepository.findOne({ where: { id: iocId } });
                    if (ioc) {
                        const enrichmentResult = await this.enrichIOC(ioc);
                        if (enrichmentResult.success) {
                            enrichedCount++;
                        }
                    }
                    processedCount++;
                    // Update progress
                    const progress = Math.round((processedCount / totalIOCs) * 100);
                    await job.progress(progress);
                    // Rate limiting delay
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
                catch (error) {
                    this.loggerService.warn('Failed to enrich IOC', {
                        iocId,
                        error: error.message,
                    });
                    processedCount++;
                }
            }
            this.logger.log('IOC enrichment completed', {
                jobId: job.id,
                totalIOCs,
                processedCount,
                enrichedCount,
            });
        }
        catch (error) {
            this.loggerService.error('IOC enrichment failed', {
                jobId: job.id,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Process threat correlation analysis
     */
    async handleThreatCorrelation(job) {
        const { threatId, userId } = job.data;
        this.logger.debug('Processing threat correlation', {
            jobId: job.id,
            threatId,
            userId,
        });
        try {
            const correlations = await this.threatIntelligenceService.findRelatedThreats({
                threatIntelligenceId: threatId,
                correlationTypes: ['actor', 'campaign', 'indicators', 'techniques'],
                timeWindow: 90,
                confidenceThreshold: 0.6,
            });
            await job.progress(100);
            this.logger.log('Threat correlation completed', {
                jobId: job.id,
                threatId,
                correlationCount: correlations.length,
            });
        }
        catch (error) {
            this.loggerService.error('Threat correlation failed', {
                jobId: job.id,
                threatId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Handle job activation
     */
    onActive(job) {
        this.logger.debug('Threat analysis job started', {
            jobId: job.id,
            jobName: job.name,
            analysisType: job.data.analysisType,
        });
    }
    /**
     * Handle job completion
     */
    onCompleted(job, result) {
        this.logger.log('Threat analysis job completed', {
            jobId: job.id,
            jobName: job.name,
            duration: Date.now() - job.timestamp,
            analysisType: job.data.analysisType,
        });
    }
    /**
     * Handle job failure
     */
    onFailed(job, error) {
        this.loggerService.error('Threat analysis job failed', {
            jobId: job.id,
            jobName: job.name,
            error: error.message,
            analysisType: job.data.analysisType,
            attempts: job.attemptsMade,
        });
    }
    // Private helper methods
    async analyzeAttributionFactors(threat) {
        const factors = {
            techniques: threat.mitreAttack?.techniques || [],
            tactics: threat.mitreAttack?.tactics || [],
            targetedSectors: threat.targetedSectors || [],
            targetedCountries: threat.targetedCountries || [],
            malwareFamily: threat.technicalDetails?.malwareFamily,
            infrastructure: threat.technicalDetails?.infrastructure,
            behaviorPatterns: threat.behavioralAnalysis,
            indicators: threat.indicators?.map(ioc => ({
                type: ioc.iocType,
                value: ioc.value,
            })) || [],
        };
        return factors;
    }
    async findPotentialActors(factors) {
        const actors = await this.actorRepository.find({
            where: { isActive: true },
        });
        const matches = [];
        for (const actor of actors) {
            const match = this.calculateActorMatch(actor, factors);
            if (match.confidence > 0.3) {
                matches.push({
                    actorId: actor.id,
                    actorName: actor.name,
                    confidence: match.confidence,
                    reasons: match.reasons,
                });
            }
        }
        return matches.sort((a, b) => b.confidence - a.confidence).slice(0, 5);
    }
    calculateActorMatch(actor, factors) {
        let score = 0;
        const reasons = [];
        const maxScore = 10;
        // Check targeted sectors
        if (factors.targetedSectors.length > 0 && actor.targetedSectors) {
            const sectorMatch = factors.targetedSectors.filter(s => actor.targetedSectors.includes(s)).length;
            if (sectorMatch > 0) {
                score += 2;
                reasons.push(`Targets similar sectors (${sectorMatch} matches)`);
            }
        }
        // Check MITRE techniques
        if (factors.techniques.length > 0 && actor.attackPatterns) {
            const techniqueMatch = factors.techniques.filter(t => actor.attackPatterns.some(p => p.mitreId === t)).length;
            if (techniqueMatch > 0) {
                score += 3;
                reasons.push(`Uses similar techniques (${techniqueMatch} matches)`);
            }
        }
        // Check geographic targeting
        if (factors.targetedCountries.length > 0 && actor.targetedCountries) {
            const countryMatch = factors.targetedCountries.filter(c => actor.targetedCountries.includes(c)).length;
            if (countryMatch > 0) {
                score += 2;
                reasons.push(`Targets similar countries (${countryMatch} matches)`);
            }
        }
        // Check tools and malware
        if (factors.malwareFamily && actor.tools) {
            const toolMatch = actor.tools.some(t => t.name.toLowerCase().includes(factors.malwareFamily.toLowerCase()));
            if (toolMatch) {
                score += 3;
                reasons.push('Uses similar malware/tools');
            }
        }
        return {
            confidence: Math.min(score / maxScore, 1.0),
            reasons,
        };
    }
    calculateAttributionConfidence(factors, suggestedActors) {
        if (suggestedActors.length === 0)
            return 0;
        const topActor = suggestedActors[0];
        let confidence = topActor.confidence;
        // Boost confidence if multiple factors align
        if (topActor.reasons.length > 2) {
            confidence += 0.1;
        }
        // Reduce confidence if there are competing actors with similar scores
        if (suggestedActors.length > 1) {
            const secondBest = suggestedActors[1];
            if (topActor.confidence - secondBest.confidence < 0.2) {
                confidence -= 0.15;
            }
        }
        return Math.min(Math.max(confidence, 0), 1.0);
    }
    async calculateScoringFactors(threat) {
        const factors = [];
        // Severity factor
        const severityValues = { critical: 10, high: 7, medium: 5, low: 3, info: 1 };
        factors.push({
            factor: 'severity',
            weight: 0.25,
            value: severityValues[threat.severity],
            impact: severityValues[threat.severity] * 0.25,
        });
        // Confidence factor
        const confidenceValues = { high: 10, medium: 7, low: 4, unknown: 2 };
        factors.push({
            factor: 'confidence',
            weight: 0.2,
            value: confidenceValues[threat.confidence],
            impact: confidenceValues[threat.confidence] * 0.2,
        });
        // Attribution factor
        const attributionValue = threat.isAttributed ? 8 : 4;
        factors.push({
            factor: 'attribution',
            weight: 0.15,
            value: attributionValue,
            impact: attributionValue * 0.15,
        });
        // Observation frequency
        const observationValue = Math.min(threat.observationCount / 5, 10);
        factors.push({
            factor: 'observation_frequency',
            weight: 0.1,
            value: observationValue,
            impact: observationValue * 0.1,
        });
        // Recency factor
        const daysSinceLastSeen = threat.lastSeen ?
            Math.floor((Date.now() - threat.lastSeen.getTime()) / (1000 * 60 * 60 * 24)) :
            Math.floor((Date.now() - threat.firstSeen.getTime()) / (1000 * 60 * 60 * 24));
        const recencyValue = daysSinceLastSeen <= 7 ? 10 :
            daysSinceLastSeen <= 30 ? 7 :
                daysSinceLastSeen <= 90 ? 4 : 2;
        factors.push({
            factor: 'recency',
            weight: 0.15,
            value: recencyValue,
            impact: recencyValue * 0.15,
        });
        // IOC count factor
        const iocValue = Math.min((threat.indicators?.length || 0) / 3, 10);
        factors.push({
            factor: 'ioc_count',
            weight: 0.1,
            value: iocValue,
            impact: iocValue * 0.1,
        });
        // Critical sector targeting
        const criticalSectors = ['financial', 'healthcare', 'energy', 'government', 'defense'];
        const targetsCritical = threat.targetedSectors?.some(s => criticalSectors.includes(s.toLowerCase()));
        const criticalValue = targetsCritical ? 8 : 5;
        factors.push({
            factor: 'critical_targeting',
            weight: 0.05,
            value: criticalValue,
            impact: criticalValue * 0.05,
        });
        return factors;
    }
    calculateWeightedScore(factors) {
        const totalImpact = factors.reduce((sum, f) => sum + f.impact, 0);
        return Math.min(totalImpact, 10.0);
    }
    async enrichIOC(ioc) {
        try {
            // Placeholder for IOC enrichment logic
            // In a real implementation, this would call external APIs for enrichment
            const enrichmentData = {
                source: 'automated_analysis',
                enrichedAt: new Date(),
                data: {
                    analyzed: true,
                    timestamp: new Date(),
                },
            };
            ioc.addEnrichment('automated_analysis', enrichmentData.data, 0.7);
            await this.iocRepository.save(ioc);
            return { success: true };
        }
        catch (error) {
            return { success: false, error: error.message };
        }
    }
    async sendScoreChangeNotification(threat, oldScore, newScore, userId) {
        try {
            const change = newScore - oldScore;
            const changeType = change > 0 ? 'increased' : 'decreased';
            const notification = {
                title: 'Threat Score Updated',
                message: `Threat "${threat.title}" score ${changeType} from ${oldScore.toFixed(1)} to ${newScore.toFixed(1)}`,
                type: change > 2 ? 'warning' : 'info',
                data: {
                    threatId: threat.id,
                    oldScore,
                    newScore,
                    change,
                },
            };
            if (userId) {
                await this.notificationService.sendUserNotification({
                    userId,
                    ...notification,
                });
            }
            else {
                await this.notificationService.sendRoleNotification({
                    roles: ['threat_analyst', 'security_analyst'],
                    ...notification,
                });
            }
        }
        catch (error) {
            this.loggerService.error('Failed to send score change notification', {
                threatId: threat.id,
                error: error.message,
            });
        }
    }
};
exports.ThreatAnalysisProcessor = ThreatAnalysisProcessor;
__decorate([
    (0, bull_1.Process)('attribution-analysis'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_g = typeof bull_2.Job !== "undefined" && bull_2.Job) === "function" ? _g : Object]),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], ThreatAnalysisProcessor.prototype, "handleAttributionAnalysis", null);
__decorate([
    (0, bull_1.Process)('threat-scoring'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_j = typeof bull_2.Job !== "undefined" && bull_2.Job) === "function" ? _j : Object]),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], ThreatAnalysisProcessor.prototype, "handleThreatScoring", null);
__decorate([
    (0, bull_1.Process)('ioc-enrichment'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_l = typeof bull_2.Job !== "undefined" && bull_2.Job) === "function" ? _l : Object]),
    __metadata("design:returntype", typeof (_m = typeof Promise !== "undefined" && Promise) === "function" ? _m : Object)
], ThreatAnalysisProcessor.prototype, "handleIOCEnrichment", null);
__decorate([
    (0, bull_1.Process)('threat-correlation'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_o = typeof bull_2.Job !== "undefined" && bull_2.Job) === "function" ? _o : Object]),
    __metadata("design:returntype", typeof (_p = typeof Promise !== "undefined" && Promise) === "function" ? _p : Object)
], ThreatAnalysisProcessor.prototype, "handleThreatCorrelation", null);
__decorate([
    (0, bull_1.OnQueueActive)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_q = typeof bull_2.Job !== "undefined" && bull_2.Job) === "function" ? _q : Object]),
    __metadata("design:returntype", void 0)
], ThreatAnalysisProcessor.prototype, "onActive", null);
__decorate([
    (0, bull_1.OnQueueCompleted)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_r = typeof bull_2.Job !== "undefined" && bull_2.Job) === "function" ? _r : Object, Object]),
    __metadata("design:returntype", void 0)
], ThreatAnalysisProcessor.prototype, "onCompleted", null);
__decorate([
    (0, bull_1.OnQueueFailed)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_s = typeof bull_2.Job !== "undefined" && bull_2.Job) === "function" ? _s : Object, typeof (_t = typeof Error !== "undefined" && Error) === "function" ? _t : Object]),
    __metadata("design:returntype", void 0)
], ThreatAnalysisProcessor.prototype, "onFailed", null);
exports.ThreatAnalysisProcessor = ThreatAnalysisProcessor = ThreatAnalysisProcessor_1 = __decorate([
    (0, bull_1.Processor)('threat-analysis'),
    __param(0, (0, typeorm_1.InjectRepository)(threat_intelligence_entity_1.ThreatIntelligence)),
    __param(1, (0, typeorm_1.InjectRepository)(threat_actor_entity_1.ThreatActor)),
    __param(2, (0, typeorm_1.InjectRepository)(indicator_of_compromise_entity_1.IndicatorOfCompromise)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof threat_intelligence_service_1.ThreatIntelligenceService !== "undefined" && threat_intelligence_service_1.ThreatIntelligenceService) === "function" ? _d : Object, typeof (_e = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _e : Object, typeof (_f = typeof notification_service_1.NotificationService !== "undefined" && notification_service_1.NotificationService) === "function" ? _f : Object])
], ThreatAnalysisProcessor);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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