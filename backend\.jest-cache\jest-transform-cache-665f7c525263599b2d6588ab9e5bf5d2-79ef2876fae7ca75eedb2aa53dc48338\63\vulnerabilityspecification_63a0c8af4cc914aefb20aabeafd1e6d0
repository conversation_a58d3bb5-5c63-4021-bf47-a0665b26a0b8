c1ad6990e48866332705c8631a1f1666
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilitySpecificationBuilder = exports.DiscoveryMethodSpecification = exports.AffectedAssetCountSpecification = exports.VulnerabilityAgeRangeSpecification = exports.VulnerabilityCVSSScoreRangeSpecification = exports.VulnerabilityRiskScoreRangeSpecification = exports.VulnerabilityTagSpecification = exports.VulnerabilityTypeSpecification = exports.VulnerabilityCategorySpecification = exports.VulnerabilityStatusSpecification = exports.VulnerabilitySeveritySpecification = exports.RequiresImmediateAttentionSpecification = exports.OverdueRemediationSpecification = exports.HighCVSSVulnerabilitySpecification = exports.CriticalAssetVulnerabilitySpecification = exports.ExternallyExposedVulnerabilitySpecification = exports.ActivelyExploitedVulnerabilitySpecification = exports.ZeroDayVulnerabilitySpecification = exports.CVEVulnerabilitySpecification = exports.StaleVulnerabilitySpecification = exports.RecentVulnerabilitySpecification = exports.HighConfidenceVulnerabilitySpecification = exports.HighRiskVulnerabilitySpecification = exports.RemediatedVulnerabilitySpecification = exports.ActiveVulnerabilitySpecification = exports.HighSeverityVulnerabilitySpecification = exports.CriticalVulnerabilitySpecification = exports.VulnerabilitySpecification = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const vulnerability_entity_1 = require("../entities/vulnerability/vulnerability.entity");
const threat_severity_enum_1 = require("../enums/threat-severity.enum");
const confidence_level_enum_1 = require("../enums/confidence-level.enum");
/**
 * Vulnerability Specification Base Class
 *
 * Base class for all vulnerability-related specifications.
 * Provides common functionality for vulnerability filtering and validation.
 */
class VulnerabilitySpecification extends shared_kernel_1.BaseSpecification {
    /**
     * Helper method to check if vulnerability matches any of the provided severities
     */
    matchesAnySeverity(vulnerability, severities) {
        return severities.includes(vulnerability.severity);
    }
    /**
     * Helper method to check if vulnerability matches any of the provided statuses
     */
    matchesAnyStatus(vulnerability, statuses) {
        return statuses.includes(vulnerability.status);
    }
    /**
     * Helper method to check if vulnerability matches any of the provided categories
     */
    matchesAnyCategory(vulnerability, categories) {
        return categories.includes(vulnerability.category);
    }
    /**
     * Helper method to check if vulnerability matches any of the provided types
     */
    matchesAnyType(vulnerability, types) {
        return types.includes(vulnerability.type);
    }
    /**
     * Helper method to check if vulnerability has any of the provided tags
     */
    hasAnyTag(vulnerability, tags) {
        return vulnerability.tags.some(tag => tags.includes(tag));
    }
    /**
     * Helper method to check if vulnerability has all of the provided tags
     */
    hasAllTags(vulnerability, tags) {
        return tags.every(tag => vulnerability.tags.includes(tag));
    }
    /**
     * Helper method to check if vulnerability age is within range
     */
    isAgeWithinRange(vulnerability, minAgeDays, maxAgeDays) {
        const ageMs = Date.now() - vulnerability.discovery.discoveredAt.getTime();
        const ageDays = ageMs / (1000 * 60 * 60 * 24);
        if (minAgeDays !== undefined && ageDays < minAgeDays) {
            return false;
        }
        if (maxAgeDays !== undefined && ageDays > maxAgeDays) {
            return false;
        }
        return true;
    }
    /**
     * Helper method to check if confidence is within range
     */
    isConfidenceWithinRange(vulnerability, minConfidence, maxConfidence) {
        const confidenceValues = {
            [confidence_level_enum_1.ConfidenceLevel.VERY_LOW]: 1,
            [confidence_level_enum_1.ConfidenceLevel.LOW]: 2,
            [confidence_level_enum_1.ConfidenceLevel.MEDIUM]: 3,
            [confidence_level_enum_1.ConfidenceLevel.HIGH]: 4,
            [confidence_level_enum_1.ConfidenceLevel.VERY_HIGH]: 5,
            [confidence_level_enum_1.ConfidenceLevel.CONFIRMED]: 6,
        };
        const currentValue = confidenceValues[vulnerability.confidence];
        if (minConfidence !== undefined && currentValue < confidenceValues[minConfidence]) {
            return false;
        }
        if (maxConfidence !== undefined && currentValue > confidenceValues[maxConfidence]) {
            return false;
        }
        return true;
    }
    /**
     * Helper method to check if risk score is within range
     */
    isRiskScoreWithinRange(vulnerability, minScore, maxScore) {
        const riskScore = vulnerability.riskAssessment.riskScore;
        if (minScore !== undefined && riskScore < minScore) {
            return false;
        }
        if (maxScore !== undefined && riskScore > maxScore) {
            return false;
        }
        return true;
    }
    /**
     * Helper method to check if CVSS score is within range
     */
    isCVSSScoreWithinRange(vulnerability, minScore, maxScore) {
        if (vulnerability.cvssScores.length === 0) {
            return minScore === undefined || minScore <= 0;
        }
        const maxCVSS = Math.max(...vulnerability.cvssScores.map(score => score.baseScore));
        if (minScore !== undefined && maxCVSS < minScore) {
            return false;
        }
        if (maxScore !== undefined && maxCVSS > maxScore) {
            return false;
        }
        return true;
    }
}
exports.VulnerabilitySpecification = VulnerabilitySpecification;
/**
 * Critical Vulnerability Specification
 *
 * Specification for vulnerabilities with critical severity.
 */
class CriticalVulnerabilitySpecification extends VulnerabilitySpecification {
    isSatisfiedBy(vulnerability) {
        return vulnerability.severity === threat_severity_enum_1.ThreatSeverity.CRITICAL;
    }
    getDescription() {
        return 'Vulnerability has critical severity';
    }
}
exports.CriticalVulnerabilitySpecification = CriticalVulnerabilitySpecification;
/**
 * High Severity Vulnerability Specification
 *
 * Specification for vulnerabilities with high or critical severity.
 */
class HighSeverityVulnerabilitySpecification extends VulnerabilitySpecification {
    isSatisfiedBy(vulnerability) {
        return vulnerability.severity === threat_severity_enum_1.ThreatSeverity.HIGH ||
            vulnerability.severity === threat_severity_enum_1.ThreatSeverity.CRITICAL;
    }
    getDescription() {
        return 'Vulnerability has high or critical severity';
    }
}
exports.HighSeverityVulnerabilitySpecification = HighSeverityVulnerabilitySpecification;
/**
 * Active Vulnerability Specification
 *
 * Specification for vulnerabilities that are not yet remediated.
 */
class ActiveVulnerabilitySpecification extends VulnerabilitySpecification {
    isSatisfiedBy(vulnerability) {
        return ![
            vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
            vulnerability_entity_1.VulnerabilityStatus.VERIFIED,
            vulnerability_entity_1.VulnerabilityStatus.CLOSED,
            vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE,
        ].includes(vulnerability.status);
    }
    getDescription() {
        return 'Vulnerability is active (not remediated, closed, or false positive)';
    }
}
exports.ActiveVulnerabilitySpecification = ActiveVulnerabilitySpecification;
/**
 * Remediated Vulnerability Specification
 *
 * Specification for vulnerabilities that have been remediated.
 */
class RemediatedVulnerabilitySpecification extends VulnerabilitySpecification {
    isSatisfiedBy(vulnerability) {
        return [
            vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
            vulnerability_entity_1.VulnerabilityStatus.VERIFIED,
            vulnerability_entity_1.VulnerabilityStatus.CLOSED,
        ].includes(vulnerability.status);
    }
    getDescription() {
        return 'Vulnerability has been remediated';
    }
}
exports.RemediatedVulnerabilitySpecification = RemediatedVulnerabilitySpecification;
/**
 * High Risk Vulnerability Specification
 *
 * Specification for vulnerabilities with high risk scores.
 */
class HighRiskVulnerabilitySpecification extends VulnerabilitySpecification {
    constructor(minRiskScore = 70) {
        super();
        this.minRiskScore = minRiskScore;
    }
    isSatisfiedBy(vulnerability) {
        return vulnerability.riskAssessment.riskScore >= this.minRiskScore;
    }
    getDescription() {
        return `Vulnerability has high risk score (>= ${this.minRiskScore})`;
    }
}
exports.HighRiskVulnerabilitySpecification = HighRiskVulnerabilitySpecification;
/**
 * High Confidence Vulnerability Specification
 *
 * Specification for vulnerabilities with high confidence levels.
 */
class HighConfidenceVulnerabilitySpecification extends VulnerabilitySpecification {
    isSatisfiedBy(vulnerability) {
        return [
            confidence_level_enum_1.ConfidenceLevel.HIGH,
            confidence_level_enum_1.ConfidenceLevel.VERY_HIGH,
            confidence_level_enum_1.ConfidenceLevel.CONFIRMED,
        ].includes(vulnerability.confidence);
    }
    getDescription() {
        return 'Vulnerability has high confidence level';
    }
}
exports.HighConfidenceVulnerabilitySpecification = HighConfidenceVulnerabilitySpecification;
/**
 * Recent Vulnerability Specification
 *
 * Specification for vulnerabilities discovered recently.
 */
class RecentVulnerabilitySpecification extends VulnerabilitySpecification {
    constructor(withinDays = 7) {
        super();
        this.withinDays = withinDays;
    }
    isSatisfiedBy(vulnerability) {
        return this.isAgeWithinRange(vulnerability, undefined, this.withinDays);
    }
    getDescription() {
        return `Vulnerability was discovered within ${this.withinDays} days`;
    }
}
exports.RecentVulnerabilitySpecification = RecentVulnerabilitySpecification;
/**
 * Stale Vulnerability Specification
 *
 * Specification for vulnerabilities that are considered stale.
 */
class StaleVulnerabilitySpecification extends VulnerabilitySpecification {
    constructor(olderThanDays = 90) {
        super();
        this.olderThanDays = olderThanDays;
    }
    isSatisfiedBy(vulnerability) {
        return this.isAgeWithinRange(vulnerability, this.olderThanDays, undefined);
    }
    getDescription() {
        return `Vulnerability is older than ${this.olderThanDays} days`;
    }
}
exports.StaleVulnerabilitySpecification = StaleVulnerabilitySpecification;
/**
 * CVE Vulnerability Specification
 *
 * Specification for vulnerabilities with CVE identifiers.
 */
class CVEVulnerabilitySpecification extends VulnerabilitySpecification {
    constructor(cveId) {
        super();
        this.cveId = cveId;
    }
    isSatisfiedBy(vulnerability) {
        if (!vulnerability.cveId) {
            return false;
        }
        if (this.cveId) {
            return vulnerability.cveId === this.cveId;
        }
        return true;
    }
    getDescription() {
        return this.cveId
            ? `Vulnerability has CVE ID: ${this.cveId}`
            : 'Vulnerability has a CVE identifier';
    }
}
exports.CVEVulnerabilitySpecification = CVEVulnerabilitySpecification;
/**
 * Zero Day Vulnerability Specification
 *
 * Specification for zero-day vulnerabilities (no CVE).
 */
class ZeroDayVulnerabilitySpecification extends VulnerabilitySpecification {
    isSatisfiedBy(vulnerability) {
        return !vulnerability.cveId || vulnerability.tags.includes('zero-day');
    }
    getDescription() {
        return 'Vulnerability is a zero-day (no CVE identifier)';
    }
}
exports.ZeroDayVulnerabilitySpecification = ZeroDayVulnerabilitySpecification;
/**
 * Actively Exploited Vulnerability Specification
 *
 * Specification for vulnerabilities that are actively exploited.
 */
class ActivelyExploitedVulnerabilitySpecification extends VulnerabilitySpecification {
    isSatisfiedBy(vulnerability) {
        return vulnerability.exploitation?.status === 'active_exploitation' ||
            vulnerability.exploitation?.status === 'weaponized';
    }
    getDescription() {
        return 'Vulnerability is actively exploited or weaponized';
    }
}
exports.ActivelyExploitedVulnerabilitySpecification = ActivelyExploitedVulnerabilitySpecification;
/**
 * Externally Exposed Vulnerability Specification
 *
 * Specification for vulnerabilities affecting externally exposed assets.
 */
class ExternallyExposedVulnerabilitySpecification extends VulnerabilitySpecification {
    isSatisfiedBy(vulnerability) {
        return vulnerability.affectedAssets.some(asset => asset.exposure === 'external' || asset.exposure === 'cloud');
    }
    getDescription() {
        return 'Vulnerability affects externally exposed assets';
    }
}
exports.ExternallyExposedVulnerabilitySpecification = ExternallyExposedVulnerabilitySpecification;
/**
 * Critical Asset Vulnerability Specification
 *
 * Specification for vulnerabilities affecting critical assets.
 */
class CriticalAssetVulnerabilitySpecification extends VulnerabilitySpecification {
    isSatisfiedBy(vulnerability) {
        return vulnerability.affectedAssets.some(asset => asset.criticality === 'critical');
    }
    getDescription() {
        return 'Vulnerability affects critical assets';
    }
}
exports.CriticalAssetVulnerabilitySpecification = CriticalAssetVulnerabilitySpecification;
/**
 * High CVSS Score Vulnerability Specification
 *
 * Specification for vulnerabilities with high CVSS scores.
 */
class HighCVSSVulnerabilitySpecification extends VulnerabilitySpecification {
    constructor(minScore = 7.0) {
        super();
        this.minScore = minScore;
    }
    isSatisfiedBy(vulnerability) {
        return this.isCVSSScoreWithinRange(vulnerability, this.minScore, undefined);
    }
    getDescription() {
        return `Vulnerability has CVSS score >= ${this.minScore}`;
    }
}
exports.HighCVSSVulnerabilitySpecification = HighCVSSVulnerabilitySpecification;
/**
 * Overdue Remediation Specification
 *
 * Specification for vulnerabilities with overdue remediation.
 */
class OverdueRemediationSpecification extends VulnerabilitySpecification {
    isSatisfiedBy(vulnerability) {
        return vulnerability.isRemediationOverdue();
    }
    getDescription() {
        return 'Vulnerability remediation is overdue';
    }
}
exports.OverdueRemediationSpecification = OverdueRemediationSpecification;
/**
 * Requires Immediate Attention Specification
 *
 * Specification for vulnerabilities requiring immediate attention.
 */
class RequiresImmediateAttentionSpecification extends VulnerabilitySpecification {
    isSatisfiedBy(vulnerability) {
        return vulnerability.requiresImmediateAttention();
    }
    getDescription() {
        return 'Vulnerability requires immediate attention';
    }
}
exports.RequiresImmediateAttentionSpecification = RequiresImmediateAttentionSpecification;
/**
 * Vulnerability Severity Specification
 *
 * Specification for vulnerabilities of specific severities.
 */
class VulnerabilitySeveritySpecification extends VulnerabilitySpecification {
    constructor(severities) {
        super();
        this.severities = severities;
    }
    isSatisfiedBy(vulnerability) {
        return this.matchesAnySeverity(vulnerability, this.severities);
    }
    getDescription() {
        return `Vulnerability severity is one of: ${this.severities.join(', ')}`;
    }
}
exports.VulnerabilitySeveritySpecification = VulnerabilitySeveritySpecification;
/**
 * Vulnerability Status Specification
 *
 * Specification for vulnerabilities with specific statuses.
 */
class VulnerabilityStatusSpecification extends VulnerabilitySpecification {
    constructor(statuses) {
        super();
        this.statuses = statuses;
    }
    isSatisfiedBy(vulnerability) {
        return this.matchesAnyStatus(vulnerability, this.statuses);
    }
    getDescription() {
        return `Vulnerability status is one of: ${this.statuses.join(', ')}`;
    }
}
exports.VulnerabilityStatusSpecification = VulnerabilityStatusSpecification;
/**
 * Vulnerability Category Specification
 *
 * Specification for vulnerabilities of specific categories.
 */
class VulnerabilityCategorySpecification extends VulnerabilitySpecification {
    constructor(categories) {
        super();
        this.categories = categories;
    }
    isSatisfiedBy(vulnerability) {
        return this.matchesAnyCategory(vulnerability, this.categories);
    }
    getDescription() {
        return `Vulnerability category is one of: ${this.categories.join(', ')}`;
    }
}
exports.VulnerabilityCategorySpecification = VulnerabilityCategorySpecification;
/**
 * Vulnerability Type Specification
 *
 * Specification for vulnerabilities of specific types.
 */
class VulnerabilityTypeSpecification extends VulnerabilitySpecification {
    constructor(types) {
        super();
        this.types = types;
    }
    isSatisfiedBy(vulnerability) {
        return this.matchesAnyType(vulnerability, this.types);
    }
    getDescription() {
        return `Vulnerability type is one of: ${this.types.join(', ')}`;
    }
}
exports.VulnerabilityTypeSpecification = VulnerabilityTypeSpecification;
/**
 * Vulnerability Tag Specification
 *
 * Specification for vulnerabilities with specific tags.
 */
class VulnerabilityTagSpecification extends VulnerabilitySpecification {
    constructor(tags, requireAll = false) {
        super();
        this.tags = tags;
        this.requireAll = requireAll;
    }
    isSatisfiedBy(vulnerability) {
        return this.requireAll
            ? this.hasAllTags(vulnerability, this.tags)
            : this.hasAnyTag(vulnerability, this.tags);
    }
    getDescription() {
        const operator = this.requireAll ? 'all' : 'any';
        return `Vulnerability has ${operator} of these tags: ${this.tags.join(', ')}`;
    }
}
exports.VulnerabilityTagSpecification = VulnerabilityTagSpecification;
/**
 * Vulnerability Risk Score Range Specification
 *
 * Specification for vulnerabilities within a specific risk score range.
 */
class VulnerabilityRiskScoreRangeSpecification extends VulnerabilitySpecification {
    constructor(minScore, maxScore) {
        super();
        this.minScore = minScore;
        this.maxScore = maxScore;
    }
    isSatisfiedBy(vulnerability) {
        return this.isRiskScoreWithinRange(vulnerability, this.minScore, this.maxScore);
    }
    getDescription() {
        if (this.minScore !== undefined && this.maxScore !== undefined) {
            return `Vulnerability risk score is between ${this.minScore} and ${this.maxScore}`;
        }
        else if (this.minScore !== undefined) {
            return `Vulnerability risk score is at least ${this.minScore}`;
        }
        else if (this.maxScore !== undefined) {
            return `Vulnerability risk score is at most ${this.maxScore}`;
        }
        return 'Vulnerability has any risk score';
    }
}
exports.VulnerabilityRiskScoreRangeSpecification = VulnerabilityRiskScoreRangeSpecification;
/**
 * Vulnerability CVSS Score Range Specification
 *
 * Specification for vulnerabilities within a specific CVSS score range.
 */
class VulnerabilityCVSSScoreRangeSpecification extends VulnerabilitySpecification {
    constructor(minScore, maxScore) {
        super();
        this.minScore = minScore;
        this.maxScore = maxScore;
    }
    isSatisfiedBy(vulnerability) {
        return this.isCVSSScoreWithinRange(vulnerability, this.minScore, this.maxScore);
    }
    getDescription() {
        if (this.minScore !== undefined && this.maxScore !== undefined) {
            return `Vulnerability CVSS score is between ${this.minScore} and ${this.maxScore}`;
        }
        else if (this.minScore !== undefined) {
            return `Vulnerability CVSS score is at least ${this.minScore}`;
        }
        else if (this.maxScore !== undefined) {
            return `Vulnerability CVSS score is at most ${this.maxScore}`;
        }
        return 'Vulnerability has any CVSS score';
    }
}
exports.VulnerabilityCVSSScoreRangeSpecification = VulnerabilityCVSSScoreRangeSpecification;
/**
 * Vulnerability Age Range Specification
 *
 * Specification for vulnerabilities within a specific age range.
 */
class VulnerabilityAgeRangeSpecification extends VulnerabilitySpecification {
    constructor(minAgeDays, maxAgeDays) {
        super();
        this.minAgeDays = minAgeDays;
        this.maxAgeDays = maxAgeDays;
    }
    isSatisfiedBy(vulnerability) {
        return this.isAgeWithinRange(vulnerability, this.minAgeDays, this.maxAgeDays);
    }
    getDescription() {
        if (this.minAgeDays !== undefined && this.maxAgeDays !== undefined) {
            return `Vulnerability age is between ${this.minAgeDays} and ${this.maxAgeDays} days`;
        }
        else if (this.minAgeDays !== undefined) {
            return `Vulnerability is at least ${this.minAgeDays} days old`;
        }
        else if (this.maxAgeDays !== undefined) {
            return `Vulnerability is at most ${this.maxAgeDays} days old`;
        }
        return 'Vulnerability has any age';
    }
}
exports.VulnerabilityAgeRangeSpecification = VulnerabilityAgeRangeSpecification;
/**
 * Affected Asset Count Specification
 *
 * Specification for vulnerabilities affecting a specific number of assets.
 */
class AffectedAssetCountSpecification extends VulnerabilitySpecification {
    constructor(minCount, maxCount) {
        super();
        this.minCount = minCount;
        this.maxCount = maxCount;
    }
    isSatisfiedBy(vulnerability) {
        const assetCount = vulnerability.affectedAssets.length;
        if (this.minCount !== undefined && assetCount < this.minCount) {
            return false;
        }
        if (this.maxCount !== undefined && assetCount > this.maxCount) {
            return false;
        }
        return true;
    }
    getDescription() {
        if (this.minCount !== undefined && this.maxCount !== undefined) {
            return `Vulnerability affects ${this.minCount}-${this.maxCount} assets`;
        }
        else if (this.minCount !== undefined) {
            return `Vulnerability affects at least ${this.minCount} assets`;
        }
        else if (this.maxCount !== undefined) {
            return `Vulnerability affects at most ${this.maxCount} assets`;
        }
        return 'Vulnerability affects any number of assets';
    }
}
exports.AffectedAssetCountSpecification = AffectedAssetCountSpecification;
/**
 * Discovery Method Specification
 *
 * Specification for vulnerabilities discovered by specific methods.
 */
class DiscoveryMethodSpecification extends VulnerabilitySpecification {
    constructor(methods) {
        super();
        this.methods = methods;
    }
    isSatisfiedBy(vulnerability) {
        return this.methods.includes(vulnerability.discovery.method);
    }
    getDescription() {
        return `Vulnerability was discovered by one of: ${this.methods.join(', ')}`;
    }
}
exports.DiscoveryMethodSpecification = DiscoveryMethodSpecification;
/**
 * Composite Vulnerability Specification Builder
 *
 * Builder for creating complex vulnerability specifications using fluent interface.
 */
class VulnerabilitySpecificationBuilder {
    constructor() {
        this.specifications = [];
    }
    /**
     * Add critical severity filter
     */
    critical() {
        this.specifications.push(new CriticalVulnerabilitySpecification());
        return this;
    }
    /**
     * Add high severity filter
     */
    highSeverity() {
        this.specifications.push(new HighSeverityVulnerabilitySpecification());
        return this;
    }
    /**
     * Add active status filter
     */
    active() {
        this.specifications.push(new ActiveVulnerabilitySpecification());
        return this;
    }
    /**
     * Add remediated status filter
     */
    remediated() {
        this.specifications.push(new RemediatedVulnerabilitySpecification());
        return this;
    }
    /**
     * Add high risk filter
     */
    highRisk(minRiskScore = 70) {
        this.specifications.push(new HighRiskVulnerabilitySpecification(minRiskScore));
        return this;
    }
    /**
     * Add high confidence filter
     */
    highConfidence() {
        this.specifications.push(new HighConfidenceVulnerabilitySpecification());
        return this;
    }
    /**
     * Add recent vulnerabilities filter
     */
    recent(withinDays = 7) {
        this.specifications.push(new RecentVulnerabilitySpecification(withinDays));
        return this;
    }
    /**
     * Add stale vulnerabilities filter
     */
    stale(olderThanDays = 90) {
        this.specifications.push(new StaleVulnerabilitySpecification(olderThanDays));
        return this;
    }
    /**
     * Add CVE filter
     */
    withCVE(cveId) {
        this.specifications.push(new CVEVulnerabilitySpecification(cveId));
        return this;
    }
    /**
     * Add zero-day filter
     */
    zeroDay() {
        this.specifications.push(new ZeroDayVulnerabilitySpecification());
        return this;
    }
    /**
     * Add actively exploited filter
     */
    activelyExploited() {
        this.specifications.push(new ActivelyExploitedVulnerabilitySpecification());
        return this;
    }
    /**
     * Add externally exposed filter
     */
    externallyExposed() {
        this.specifications.push(new ExternallyExposedVulnerabilitySpecification());
        return this;
    }
    /**
     * Add critical asset filter
     */
    affectsCriticalAssets() {
        this.specifications.push(new CriticalAssetVulnerabilitySpecification());
        return this;
    }
    /**
     * Add high CVSS score filter
     */
    highCVSS(minScore = 7.0) {
        this.specifications.push(new HighCVSSVulnerabilitySpecification(minScore));
        return this;
    }
    /**
     * Add overdue remediation filter
     */
    overdueRemediation() {
        this.specifications.push(new OverdueRemediationSpecification());
        return this;
    }
    /**
     * Add requires immediate attention filter
     */
    requiresImmediateAttention() {
        this.specifications.push(new RequiresImmediateAttentionSpecification());
        return this;
    }
    /**
     * Add severity filter
     */
    withSeverities(...severities) {
        this.specifications.push(new VulnerabilitySeveritySpecification(severities));
        return this;
    }
    /**
     * Add status filter
     */
    withStatuses(...statuses) {
        this.specifications.push(new VulnerabilityStatusSpecification(statuses));
        return this;
    }
    /**
     * Add category filter
     */
    withCategories(...categories) {
        this.specifications.push(new VulnerabilityCategorySpecification(categories));
        return this;
    }
    /**
     * Add type filter
     */
    withTypes(...types) {
        this.specifications.push(new VulnerabilityTypeSpecification(types));
        return this;
    }
    /**
     * Add tag filter
     */
    withTags(tags, requireAll = false) {
        this.specifications.push(new VulnerabilityTagSpecification(tags, requireAll));
        return this;
    }
    /**
     * Add risk score range filter
     */
    riskScoreRange(minScore, maxScore) {
        this.specifications.push(new VulnerabilityRiskScoreRangeSpecification(minScore, maxScore));
        return this;
    }
    /**
     * Add CVSS score range filter
     */
    cvssScoreRange(minScore, maxScore) {
        this.specifications.push(new VulnerabilityCVSSScoreRangeSpecification(minScore, maxScore));
        return this;
    }
    /**
     * Add age range filter
     */
    ageRange(minDays, maxDays) {
        this.specifications.push(new VulnerabilityAgeRangeSpecification(minDays, maxDays));
        return this;
    }
    /**
     * Add affected asset count filter
     */
    affectedAssetCount(minCount, maxCount) {
        this.specifications.push(new AffectedAssetCountSpecification(minCount, maxCount));
        return this;
    }
    /**
     * Add discovery method filter
     */
    discoveredBy(...methods) {
        this.specifications.push(new DiscoveryMethodSpecification(methods));
        return this;
    }
    /**
     * Build the final specification using AND logic
     */
    build() {
        if (this.specifications.length === 0) {
            throw new Error('At least one specification must be added');
        }
        if (this.specifications.length === 1) {
            return this.specifications[0];
        }
        // Combine all specifications with AND logic
        let combined = this.specifications[0];
        for (let i = 1; i < this.specifications.length; i++) {
            combined = combined.and(this.specifications[i]);
        }
        return combined;
    }
    /**
     * Build the final specification using OR logic
     */
    buildWithOr() {
        if (this.specifications.length === 0) {
            throw new Error('At least one specification must be added');
        }
        if (this.specifications.length === 1) {
            return this.specifications[0];
        }
        // Combine all specifications with OR logic
        let combined = this.specifications[0];
        for (let i = 1; i < this.specifications.length; i++) {
            combined = combined.or(this.specifications[i]);
        }
        return combined;
    }
    /**
     * Create a new builder instance
     */
    static create() {
        return new VulnerabilitySpecificationBuilder();
    }
}
exports.VulnerabilitySpecificationBuilder = VulnerabilitySpecificationBuilder;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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