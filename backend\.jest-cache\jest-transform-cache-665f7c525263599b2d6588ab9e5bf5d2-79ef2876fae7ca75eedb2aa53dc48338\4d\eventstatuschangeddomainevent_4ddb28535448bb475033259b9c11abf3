fbde4d8fa62a6529c8ee972c38b6aca5
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventStatusChangedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const event_status_enum_1 = require("../enums/event-status.enum");
/**
 * Event Status Changed Domain Event
 *
 * Raised when a security event's status changes.
 * This event triggers various downstream processes including:
 * - Status change notifications
 * - Workflow transitions
 * - Metrics updates
 * - Audit logging
 * - SLA tracking
 */
class EventStatusChangedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the previous status
     */
    get oldStatus() {
        return this.eventData.oldStatus;
    }
    /**
     * Get the new status
     */
    get newStatus() {
        return this.eventData.newStatus;
    }
    /**
     * Get who changed the status
     */
    get changedBy() {
        return this.eventData.changedBy;
    }
    /**
     * Get notes about the change
     */
    get notes() {
        return this.eventData.notes;
    }
    /**
     * Get when the status was changed
     */
    get timestamp() {
        return this.eventData.timestamp;
    }
    /**
     * Check if event was resolved
     */
    wasResolved() {
        return this.newStatus === event_status_enum_1.EventStatus.RESOLVED;
    }
    /**
     * Check if event was reopened
     */
    wasReopened() {
        return this.oldStatus === event_status_enum_1.EventStatus.RESOLVED &&
            [event_status_enum_1.EventStatus.ACTIVE, event_status_enum_1.EventStatus.INVESTIGATING].includes(this.newStatus);
    }
    /**
     * Check if event was closed
     */
    wasClosed() {
        return this.newStatus === event_status_enum_1.EventStatus.CLOSED;
    }
    /**
     * Check if event was marked as false positive
     */
    wasMarkedFalsePositive() {
        return this.newStatus === event_status_enum_1.EventStatus.FALSE_POSITIVE;
    }
    /**
     * Check if event was ignored
     */
    wasIgnored() {
        return this.newStatus === event_status_enum_1.EventStatus.IGNORED;
    }
    /**
     * Check if status change indicates escalation
     */
    isEscalation() {
        const escalationTransitions = [
            { from: event_status_enum_1.EventStatus.ACTIVE, to: event_status_enum_1.EventStatus.INVESTIGATING },
        ];
        return escalationTransitions.some(transition => transition.from === this.oldStatus && transition.to === this.newStatus);
    }
    /**
     * Check if status change indicates de-escalation
     */
    isDeEscalation() {
        const deEscalationTransitions = [
            { from: event_status_enum_1.EventStatus.INVESTIGATING, to: event_status_enum_1.EventStatus.ACTIVE },
            { from: event_status_enum_1.EventStatus.INVESTIGATING, to: event_status_enum_1.EventStatus.MITIGATED },
            { from: event_status_enum_1.EventStatus.ACTIVE, to: event_status_enum_1.EventStatus.MITIGATED },
        ];
        return deEscalationTransitions.some(transition => transition.from === this.oldStatus && transition.to === this.newStatus);
    }
    /**
     * Get status change category
     */
    getChangeCategory() {
        if (this.wasResolved())
            return 'resolution';
        if (this.wasReopened())
            return 'reopening';
        if (this.wasClosed() || this.wasMarkedFalsePositive() || this.wasIgnored())
            return 'closure';
        if (this.isEscalation())
            return 'escalation';
        if (this.isDeEscalation())
            return 'de-escalation';
        return 'other';
    }
    /**
     * Get priority level for notifications
     */
    getNotificationPriority() {
        const category = this.getChangeCategory();
        switch (category) {
            case 'escalation':
                return 'high';
            case 'resolution':
                return 'medium';
            case 'reopening':
                return 'high';
            case 'closure':
                return 'low';
            default:
                return 'medium';
        }
    }
    /**
     * Check if change requires notification
     */
    requiresNotification() {
        // All status changes typically require some form of notification
        return true;
    }
    /**
     * Get change summary for handlers
     */
    getChangeSummary() {
        return {
            eventId: this.aggregateId.toString(),
            oldStatus: this.oldStatus,
            newStatus: this.newStatus,
            changedBy: this.changedBy,
            category: this.getChangeCategory(),
            priority: this.getNotificationPriority(),
            requiresNotification: this.requiresNotification(),
            wasResolved: this.wasResolved(),
            wasReopened: this.wasReopened(),
            wasClosed: this.wasClosed(),
            timestamp: this.timestamp.toISOString(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            changeSummary: this.getChangeSummary(),
        };
    }
}
exports.EventStatusChangedDomainEvent = EventStatusChangedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFxldmVudHNcXGV2ZW50LXN0YXR1cy1jaGFuZ2VkLmRvbWFpbi1ldmVudC50cyIsIm1hcHBpbmdzIjoiOzs7QUFBQSw2REFBNEU7QUFDNUUsa0VBQXlEO0FBa0J6RDs7Ozs7Ozs7OztHQVVHO0FBQ0gsTUFBYSw2QkFBOEIsU0FBUSwrQkFBNEM7SUFDN0YsWUFDRSxXQUEyQixFQUMzQixTQUFzQyxFQUN0QyxPQU9DO1FBRUQsS0FBSyxDQUFDLFdBQVcsRUFBRSxTQUFTLEVBQUUsT0FBTyxDQUFDLENBQUM7SUFDekMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxTQUFTO1FBQ1gsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQztJQUNsQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLFNBQVM7UUFDWCxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDO0lBQ2xDLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksU0FBUztRQUNYLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTLENBQUM7SUFDbEMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxLQUFLO1FBQ1AsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQztJQUM5QixDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLFNBQVM7UUFDWCxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDO0lBQ2xDLENBQUM7SUFFRDs7T0FFRztJQUNILFdBQVc7UUFDVCxPQUFPLElBQUksQ0FBQyxTQUFTLEtBQUssK0JBQVcsQ0FBQyxRQUFRLENBQUM7SUFDakQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsV0FBVztRQUNULE9BQU8sSUFBSSxDQUFDLFNBQVMsS0FBSywrQkFBVyxDQUFDLFFBQVE7WUFDdkMsQ0FBQywrQkFBVyxDQUFDLE1BQU0sRUFBRSwrQkFBVyxDQUFDLGFBQWEsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUM7SUFDbEYsQ0FBQztJQUVEOztPQUVHO0lBQ0gsU0FBUztRQUNQLE9BQU8sSUFBSSxDQUFDLFNBQVMsS0FBSywrQkFBVyxDQUFDLE1BQU0sQ0FBQztJQUMvQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxzQkFBc0I7UUFDcEIsT0FBTyxJQUFJLENBQUMsU0FBUyxLQUFLLCtCQUFXLENBQUMsY0FBYyxDQUFDO0lBQ3ZELENBQUM7SUFFRDs7T0FFRztJQUNILFVBQVU7UUFDUixPQUFPLElBQUksQ0FBQyxTQUFTLEtBQUssK0JBQVcsQ0FBQyxPQUFPLENBQUM7SUFDaEQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsWUFBWTtRQUNWLE1BQU0scUJBQXFCLEdBQUc7WUFDNUIsRUFBRSxJQUFJLEVBQUUsK0JBQVcsQ0FBQyxNQUFNLEVBQUUsRUFBRSxFQUFFLCtCQUFXLENBQUMsYUFBYSxFQUFFO1NBQzVELENBQUM7UUFFRixPQUFPLHFCQUFxQixDQUFDLElBQUksQ0FDL0IsVUFBVSxDQUFDLEVBQUUsQ0FBQyxVQUFVLENBQUMsSUFBSSxLQUFLLElBQUksQ0FBQyxTQUFTLElBQUksVUFBVSxDQUFDLEVBQUUsS0FBSyxJQUFJLENBQUMsU0FBUyxDQUNyRixDQUFDO0lBQ0osQ0FBQztJQUVEOztPQUVHO0lBQ0gsY0FBYztRQUNaLE1BQU0sdUJBQXVCLEdBQUc7WUFDOUIsRUFBRSxJQUFJLEVBQUUsK0JBQVcsQ0FBQyxhQUFhLEVBQUUsRUFBRSxFQUFFLCtCQUFXLENBQUMsTUFBTSxFQUFFO1lBQzNELEVBQUUsSUFBSSxFQUFFLCtCQUFXLENBQUMsYUFBYSxFQUFFLEVBQUUsRUFBRSwrQkFBVyxDQUFDLFNBQVMsRUFBRTtZQUM5RCxFQUFFLElBQUksRUFBRSwrQkFBVyxDQUFDLE1BQU0sRUFBRSxFQUFFLEVBQUUsK0JBQVcsQ0FBQyxTQUFTLEVBQUU7U0FDeEQsQ0FBQztRQUVGLE9BQU8sdUJBQXVCLENBQUMsSUFBSSxDQUNqQyxVQUFVLENBQUMsRUFBRSxDQUFDLFVBQVUsQ0FBQyxJQUFJLEtBQUssSUFBSSxDQUFDLFNBQVMsSUFBSSxVQUFVLENBQUMsRUFBRSxLQUFLLElBQUksQ0FBQyxTQUFTLENBQ3JGLENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSCxpQkFBaUI7UUFDZixJQUFJLElBQUksQ0FBQyxXQUFXLEVBQUU7WUFBRSxPQUFPLFlBQVksQ0FBQztRQUM1QyxJQUFJLElBQUksQ0FBQyxXQUFXLEVBQUU7WUFBRSxPQUFPLFdBQVcsQ0FBQztRQUMzQyxJQUFJLElBQUksQ0FBQyxTQUFTLEVBQUUsSUFBSSxJQUFJLENBQUMsc0JBQXNCLEVBQUUsSUFBSSxJQUFJLENBQUMsVUFBVSxFQUFFO1lBQUUsT0FBTyxTQUFTLENBQUM7UUFDN0YsSUFBSSxJQUFJLENBQUMsWUFBWSxFQUFFO1lBQUUsT0FBTyxZQUFZLENBQUM7UUFDN0MsSUFBSSxJQUFJLENBQUMsY0FBYyxFQUFFO1lBQUUsT0FBTyxlQUFlLENBQUM7UUFDbEQsT0FBTyxPQUFPLENBQUM7SUFDakIsQ0FBQztJQUVEOztPQUVHO0lBQ0gsdUJBQXVCO1FBQ3JCLE1BQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1FBRTFDLFFBQVEsUUFBUSxFQUFFLENBQUM7WUFDakIsS0FBSyxZQUFZO2dCQUNmLE9BQU8sTUFBTSxDQUFDO1lBQ2hCLEtBQUssWUFBWTtnQkFDZixPQUFPLFFBQVEsQ0FBQztZQUNsQixLQUFLLFdBQVc7Z0JBQ2QsT0FBTyxNQUFNLENBQUM7WUFDaEIsS0FBSyxTQUFTO2dCQUNaLE9BQU8sS0FBSyxDQUFDO1lBQ2Y7Z0JBQ0UsT0FBTyxRQUFRLENBQUM7UUFDcEIsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILG9CQUFvQjtRQUNsQixpRUFBaUU7UUFDakUsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxnQkFBZ0I7UUFhZCxPQUFPO1lBQ0wsT0FBTyxFQUFFLElBQUksQ0FBQyxXQUFXLENBQUMsUUFBUSxFQUFFO1lBQ3BDLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUztZQUN6QixTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVM7WUFDekIsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTO1lBQ3pCLFFBQVEsRUFBRSxJQUFJLENBQUMsaUJBQWlCLEVBQUU7WUFDbEMsUUFBUSxFQUFFLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtZQUN4QyxvQkFBb0IsRUFBRSxJQUFJLENBQUMsb0JBQW9CLEVBQUU7WUFDakQsV0FBVyxFQUFFLElBQUksQ0FBQyxXQUFXLEVBQUU7WUFDL0IsV0FBVyxFQUFFLElBQUksQ0FBQyxXQUFXLEVBQUU7WUFDL0IsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTLEVBQUU7WUFDM0IsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsV0FBVyxFQUFFO1NBQ3hDLENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSSxNQUFNO1FBQ1gsT0FBTztZQUNMLEdBQUcsS0FBSyxDQUFDLE1BQU0sRUFBRTtZQUNqQixhQUFhLEVBQUUsSUFBSSxDQUFDLGdCQUFnQixFQUFFO1NBQ3ZDLENBQUM7SUFDSixDQUFDO0NBQ0Y7QUFuTUQsc0VBbU1DIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTHVrYVxcc2VudGluZWxcXGJhY2tlbmRcXHNyY1xcY29yZVxcc2VjdXJpdHlcXGRvbWFpblxcZXZlbnRzXFxldmVudC1zdGF0dXMtY2hhbmdlZC5kb21haW4tZXZlbnQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZURvbWFpbkV2ZW50LCBVbmlxdWVFbnRpdHlJZCB9IGZyb20gJy4uLy4uLy4uLy4uL3NoYXJlZC1rZXJuZWwnO1xyXG5pbXBvcnQgeyBFdmVudFN0YXR1cyB9IGZyb20gJy4uL2VudW1zL2V2ZW50LXN0YXR1cy5lbnVtJztcclxuXHJcbi8qKlxyXG4gKiBFdmVudCBTdGF0dXMgQ2hhbmdlZCBEb21haW4gRXZlbnQgRGF0YVxyXG4gKi9cclxuZXhwb3J0IGludGVyZmFjZSBFdmVudFN0YXR1c0NoYW5nZWRFdmVudERhdGEge1xyXG4gIC8qKiBQcmV2aW91cyBzdGF0dXMgb2YgdGhlIGV2ZW50ICovXHJcbiAgb2xkU3RhdHVzOiBFdmVudFN0YXR1cztcclxuICAvKiogTmV3IHN0YXR1cyBvZiB0aGUgZXZlbnQgKi9cclxuICBuZXdTdGF0dXM6IEV2ZW50U3RhdHVzO1xyXG4gIC8qKiBXaG8gY2hhbmdlZCB0aGUgc3RhdHVzICovXHJcbiAgY2hhbmdlZEJ5Pzogc3RyaW5nO1xyXG4gIC8qKiBOb3RlcyBhYm91dCB0aGUgc3RhdHVzIGNoYW5nZSAqL1xyXG4gIG5vdGVzPzogc3RyaW5nO1xyXG4gIC8qKiBXaGVuIHRoZSBzdGF0dXMgd2FzIGNoYW5nZWQgKi9cclxuICB0aW1lc3RhbXA6IERhdGU7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBFdmVudCBTdGF0dXMgQ2hhbmdlZCBEb21haW4gRXZlbnRcclxuICogXHJcbiAqIFJhaXNlZCB3aGVuIGEgc2VjdXJpdHkgZXZlbnQncyBzdGF0dXMgY2hhbmdlcy5cclxuICogVGhpcyBldmVudCB0cmlnZ2VycyB2YXJpb3VzIGRvd25zdHJlYW0gcHJvY2Vzc2VzIGluY2x1ZGluZzpcclxuICogLSBTdGF0dXMgY2hhbmdlIG5vdGlmaWNhdGlvbnNcclxuICogLSBXb3JrZmxvdyB0cmFuc2l0aW9uc1xyXG4gKiAtIE1ldHJpY3MgdXBkYXRlc1xyXG4gKiAtIEF1ZGl0IGxvZ2dpbmdcclxuICogLSBTTEEgdHJhY2tpbmdcclxuICovXHJcbmV4cG9ydCBjbGFzcyBFdmVudFN0YXR1c0NoYW5nZWREb21haW5FdmVudCBleHRlbmRzIEJhc2VEb21haW5FdmVudDxFdmVudFN0YXR1c0NoYW5nZWRFdmVudERhdGE+IHtcclxuICBjb25zdHJ1Y3RvcihcclxuICAgIGFnZ3JlZ2F0ZUlkOiBVbmlxdWVFbnRpdHlJZCxcclxuICAgIGV2ZW50RGF0YTogRXZlbnRTdGF0dXNDaGFuZ2VkRXZlbnREYXRhLFxyXG4gICAgb3B0aW9ucz86IHtcclxuICAgICAgZXZlbnRJZD86IFVuaXF1ZUVudGl0eUlkO1xyXG4gICAgICBvY2N1cnJlZE9uPzogRGF0ZTtcclxuICAgICAgZXZlbnRWZXJzaW9uPzogbnVtYmVyO1xyXG4gICAgICBjb3JyZWxhdGlvbklkPzogc3RyaW5nO1xyXG4gICAgICBjYXVzYXRpb25JZD86IHN0cmluZztcclxuICAgICAgbWV0YWRhdGE/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xyXG4gICAgfVxyXG4gICkge1xyXG4gICAgc3VwZXIoYWdncmVnYXRlSWQsIGV2ZW50RGF0YSwgb3B0aW9ucyk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIHByZXZpb3VzIHN0YXR1c1xyXG4gICAqL1xyXG4gIGdldCBvbGRTdGF0dXMoKTogRXZlbnRTdGF0dXMge1xyXG4gICAgcmV0dXJuIHRoaXMuZXZlbnREYXRhLm9sZFN0YXR1cztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0aGUgbmV3IHN0YXR1c1xyXG4gICAqL1xyXG4gIGdldCBuZXdTdGF0dXMoKTogRXZlbnRTdGF0dXMge1xyXG4gICAgcmV0dXJuIHRoaXMuZXZlbnREYXRhLm5ld1N0YXR1cztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB3aG8gY2hhbmdlZCB0aGUgc3RhdHVzXHJcbiAgICovXHJcbiAgZ2V0IGNoYW5nZWRCeSgpOiBzdHJpbmcgfCB1bmRlZmluZWQge1xyXG4gICAgcmV0dXJuIHRoaXMuZXZlbnREYXRhLmNoYW5nZWRCeTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBub3RlcyBhYm91dCB0aGUgY2hhbmdlXHJcbiAgICovXHJcbiAgZ2V0IG5vdGVzKCk6IHN0cmluZyB8IHVuZGVmaW5lZCB7XHJcbiAgICByZXR1cm4gdGhpcy5ldmVudERhdGEubm90ZXM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgd2hlbiB0aGUgc3RhdHVzIHdhcyBjaGFuZ2VkXHJcbiAgICovXHJcbiAgZ2V0IHRpbWVzdGFtcCgpOiBEYXRlIHtcclxuICAgIHJldHVybiB0aGlzLmV2ZW50RGF0YS50aW1lc3RhbXA7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBldmVudCB3YXMgcmVzb2x2ZWRcclxuICAgKi9cclxuICB3YXNSZXNvbHZlZCgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLm5ld1N0YXR1cyA9PT0gRXZlbnRTdGF0dXMuUkVTT0xWRUQ7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBldmVudCB3YXMgcmVvcGVuZWRcclxuICAgKi9cclxuICB3YXNSZW9wZW5lZCgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLm9sZFN0YXR1cyA9PT0gRXZlbnRTdGF0dXMuUkVTT0xWRUQgJiYgXHJcbiAgICAgICAgICAgW0V2ZW50U3RhdHVzLkFDVElWRSwgRXZlbnRTdGF0dXMuSU5WRVNUSUdBVElOR10uaW5jbHVkZXModGhpcy5uZXdTdGF0dXMpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgZXZlbnQgd2FzIGNsb3NlZFxyXG4gICAqL1xyXG4gIHdhc0Nsb3NlZCgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLm5ld1N0YXR1cyA9PT0gRXZlbnRTdGF0dXMuQ0xPU0VEO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgZXZlbnQgd2FzIG1hcmtlZCBhcyBmYWxzZSBwb3NpdGl2ZVxyXG4gICAqL1xyXG4gIHdhc01hcmtlZEZhbHNlUG9zaXRpdmUoKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5uZXdTdGF0dXMgPT09IEV2ZW50U3RhdHVzLkZBTFNFX1BPU0lUSVZFO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgZXZlbnQgd2FzIGlnbm9yZWRcclxuICAgKi9cclxuICB3YXNJZ25vcmVkKCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMubmV3U3RhdHVzID09PSBFdmVudFN0YXR1cy5JR05PUkVEO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgc3RhdHVzIGNoYW5nZSBpbmRpY2F0ZXMgZXNjYWxhdGlvblxyXG4gICAqL1xyXG4gIGlzRXNjYWxhdGlvbigpOiBib29sZWFuIHtcclxuICAgIGNvbnN0IGVzY2FsYXRpb25UcmFuc2l0aW9ucyA9IFtcclxuICAgICAgeyBmcm9tOiBFdmVudFN0YXR1cy5BQ1RJVkUsIHRvOiBFdmVudFN0YXR1cy5JTlZFU1RJR0FUSU5HIH0sXHJcbiAgICBdO1xyXG5cclxuICAgIHJldHVybiBlc2NhbGF0aW9uVHJhbnNpdGlvbnMuc29tZShcclxuICAgICAgdHJhbnNpdGlvbiA9PiB0cmFuc2l0aW9uLmZyb20gPT09IHRoaXMub2xkU3RhdHVzICYmIHRyYW5zaXRpb24udG8gPT09IHRoaXMubmV3U3RhdHVzXHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgc3RhdHVzIGNoYW5nZSBpbmRpY2F0ZXMgZGUtZXNjYWxhdGlvblxyXG4gICAqL1xyXG4gIGlzRGVFc2NhbGF0aW9uKCk6IGJvb2xlYW4ge1xyXG4gICAgY29uc3QgZGVFc2NhbGF0aW9uVHJhbnNpdGlvbnMgPSBbXHJcbiAgICAgIHsgZnJvbTogRXZlbnRTdGF0dXMuSU5WRVNUSUdBVElORywgdG86IEV2ZW50U3RhdHVzLkFDVElWRSB9LFxyXG4gICAgICB7IGZyb206IEV2ZW50U3RhdHVzLklOVkVTVElHQVRJTkcsIHRvOiBFdmVudFN0YXR1cy5NSVRJR0FURUQgfSxcclxuICAgICAgeyBmcm9tOiBFdmVudFN0YXR1cy5BQ1RJVkUsIHRvOiBFdmVudFN0YXR1cy5NSVRJR0FURUQgfSxcclxuICAgIF07XHJcblxyXG4gICAgcmV0dXJuIGRlRXNjYWxhdGlvblRyYW5zaXRpb25zLnNvbWUoXHJcbiAgICAgIHRyYW5zaXRpb24gPT4gdHJhbnNpdGlvbi5mcm9tID09PSB0aGlzLm9sZFN0YXR1cyAmJiB0cmFuc2l0aW9uLnRvID09PSB0aGlzLm5ld1N0YXR1c1xyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBzdGF0dXMgY2hhbmdlIGNhdGVnb3J5XHJcbiAgICovXHJcbiAgZ2V0Q2hhbmdlQ2F0ZWdvcnkoKTogJ3Jlc29sdXRpb24nIHwgJ2VzY2FsYXRpb24nIHwgJ2RlLWVzY2FsYXRpb24nIHwgJ3Jlb3BlbmluZycgfCAnY2xvc3VyZScgfCAnb3RoZXInIHtcclxuICAgIGlmICh0aGlzLndhc1Jlc29sdmVkKCkpIHJldHVybiAncmVzb2x1dGlvbic7XHJcbiAgICBpZiAodGhpcy53YXNSZW9wZW5lZCgpKSByZXR1cm4gJ3Jlb3BlbmluZyc7XHJcbiAgICBpZiAodGhpcy53YXNDbG9zZWQoKSB8fCB0aGlzLndhc01hcmtlZEZhbHNlUG9zaXRpdmUoKSB8fCB0aGlzLndhc0lnbm9yZWQoKSkgcmV0dXJuICdjbG9zdXJlJztcclxuICAgIGlmICh0aGlzLmlzRXNjYWxhdGlvbigpKSByZXR1cm4gJ2VzY2FsYXRpb24nO1xyXG4gICAgaWYgKHRoaXMuaXNEZUVzY2FsYXRpb24oKSkgcmV0dXJuICdkZS1lc2NhbGF0aW9uJztcclxuICAgIHJldHVybiAnb3RoZXInO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHByaW9yaXR5IGxldmVsIGZvciBub3RpZmljYXRpb25zXHJcbiAgICovXHJcbiAgZ2V0Tm90aWZpY2F0aW9uUHJpb3JpdHkoKTogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJyB8ICdjcml0aWNhbCcge1xyXG4gICAgY29uc3QgY2F0ZWdvcnkgPSB0aGlzLmdldENoYW5nZUNhdGVnb3J5KCk7XHJcbiAgICBcclxuICAgIHN3aXRjaCAoY2F0ZWdvcnkpIHtcclxuICAgICAgY2FzZSAnZXNjYWxhdGlvbic6XHJcbiAgICAgICAgcmV0dXJuICdoaWdoJztcclxuICAgICAgY2FzZSAncmVzb2x1dGlvbic6XHJcbiAgICAgICAgcmV0dXJuICdtZWRpdW0nO1xyXG4gICAgICBjYXNlICdyZW9wZW5pbmcnOlxyXG4gICAgICAgIHJldHVybiAnaGlnaCc7XHJcbiAgICAgIGNhc2UgJ2Nsb3N1cmUnOlxyXG4gICAgICAgIHJldHVybiAnbG93JztcclxuICAgICAgZGVmYXVsdDpcclxuICAgICAgICByZXR1cm4gJ21lZGl1bSc7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBjaGFuZ2UgcmVxdWlyZXMgbm90aWZpY2F0aW9uXHJcbiAgICovXHJcbiAgcmVxdWlyZXNOb3RpZmljYXRpb24oKTogYm9vbGVhbiB7XHJcbiAgICAvLyBBbGwgc3RhdHVzIGNoYW5nZXMgdHlwaWNhbGx5IHJlcXVpcmUgc29tZSBmb3JtIG9mIG5vdGlmaWNhdGlvblxyXG4gICAgcmV0dXJuIHRydWU7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgY2hhbmdlIHN1bW1hcnkgZm9yIGhhbmRsZXJzXHJcbiAgICovXHJcbiAgZ2V0Q2hhbmdlU3VtbWFyeSgpOiB7XHJcbiAgICBldmVudElkOiBzdHJpbmc7XHJcbiAgICBvbGRTdGF0dXM6IEV2ZW50U3RhdHVzO1xyXG4gICAgbmV3U3RhdHVzOiBFdmVudFN0YXR1cztcclxuICAgIGNoYW5nZWRCeT86IHN0cmluZztcclxuICAgIGNhdGVnb3J5OiBzdHJpbmc7XHJcbiAgICBwcmlvcml0eTogc3RyaW5nO1xyXG4gICAgcmVxdWlyZXNOb3RpZmljYXRpb246IGJvb2xlYW47XHJcbiAgICB3YXNSZXNvbHZlZDogYm9vbGVhbjtcclxuICAgIHdhc1Jlb3BlbmVkOiBib29sZWFuO1xyXG4gICAgd2FzQ2xvc2VkOiBib29sZWFuO1xyXG4gICAgdGltZXN0YW1wOiBzdHJpbmc7XHJcbiAgfSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBldmVudElkOiB0aGlzLmFnZ3JlZ2F0ZUlkLnRvU3RyaW5nKCksXHJcbiAgICAgIG9sZFN0YXR1czogdGhpcy5vbGRTdGF0dXMsXHJcbiAgICAgIG5ld1N0YXR1czogdGhpcy5uZXdTdGF0dXMsXHJcbiAgICAgIGNoYW5nZWRCeTogdGhpcy5jaGFuZ2VkQnksXHJcbiAgICAgIGNhdGVnb3J5OiB0aGlzLmdldENoYW5nZUNhdGVnb3J5KCksXHJcbiAgICAgIHByaW9yaXR5OiB0aGlzLmdldE5vdGlmaWNhdGlvblByaW9yaXR5KCksXHJcbiAgICAgIHJlcXVpcmVzTm90aWZpY2F0aW9uOiB0aGlzLnJlcXVpcmVzTm90aWZpY2F0aW9uKCksXHJcbiAgICAgIHdhc1Jlc29sdmVkOiB0aGlzLndhc1Jlc29sdmVkKCksXHJcbiAgICAgIHdhc1Jlb3BlbmVkOiB0aGlzLndhc1Jlb3BlbmVkKCksXHJcbiAgICAgIHdhc0Nsb3NlZDogdGhpcy53YXNDbG9zZWQoKSxcclxuICAgICAgdGltZXN0YW1wOiB0aGlzLnRpbWVzdGFtcC50b0lTT1N0cmluZygpLFxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENvbnZlcnQgdG8gSlNPTiByZXByZXNlbnRhdGlvblxyXG4gICAqL1xyXG4gIHB1YmxpYyB0b0pTT04oKTogUmVjb3JkPHN0cmluZywgYW55PiB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICAuLi5zdXBlci50b0pTT04oKSxcclxuICAgICAgY2hhbmdlU3VtbWFyeTogdGhpcy5nZXRDaGFuZ2VTdW1tYXJ5KCksXHJcbiAgICB9O1xyXG4gIH1cclxufSJdLCJ2ZXJzaW9uIjozfQ==