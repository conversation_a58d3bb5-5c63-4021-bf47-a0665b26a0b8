78330f88a323e782ecd93b55a0c27c9c
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const report_generation_service_1 = require("./report-generation.service");
const report_entity_1 = require("../../domain/entities/report.entity");
const report_execution_entity_1 = require("../../domain/entities/report-execution.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const data_aggregation_service_1 = require("./data-aggregation.service");
const report_export_service_1 = require("./report-export.service");
describe('ReportGenerationService', () => {
    let service;
    let reportRepository;
    let executionRepository;
    let dataSource;
    let loggerService;
    let auditService;
    let dataAggregationService;
    let reportExportService;
    const mockReportRepository = {
        findOne: jest.fn(),
        save: jest.fn(),
    };
    const mockExecutionRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findOne: jest.fn(),
    };
    const mockDataSource = {
        query: jest.fn(),
    };
    const mockLoggerService = {
        debug: jest.fn(),
        log: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
    };
    const mockAuditService = {
        logUserAction: jest.fn(),
    };
    const mockDataAggregationService = {
        aggregateData: jest.fn(),
    };
    const mockReportExportService = {
        exportReport: jest.fn(),
    };
    const mockReport = {
        id: 'report-123',
        name: 'Test Report',
        type: 'vulnerability_summary',
        category: 'security',
        status: 'active',
        configuration: {
            dataSources: ['vulnerabilities'],
            timeRange: {
                type: 'relative',
                value: '30d',
            },
            filters: {
                severities: ['high', 'critical'],
            },
            visualizations: [
                {
                    type: 'chart',
                    chartType: 'bar',
                    title: 'Vulnerabilities by Severity',
                    dataSource: 'vulnerabilities',
                    configuration: {},
                },
            ],
        },
        validateConfiguration: jest.fn().mockReturnValue({ isValid: true, errors: [] }),
        getDataSources: jest.fn().mockReturnValue(['vulnerabilities']),
        getSupportedExportFormats: jest.fn().mockReturnValue(['pdf', 'excel']),
        updateExecutionStats: jest.fn(),
    };
    const mockExecution = {
        id: 'execution-123',
        reportId: 'report-123',
        status: 'pending',
        triggerType: 'manual',
        executedBy: 'user-123',
        start: jest.fn(),
        updateProgress: jest.fn(),
        complete: jest.fn(),
        fail: jest.fn(),
        cancel: jest.fn(),
        isRunning: false,
        isCompleted: false,
        isFailed: false,
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                report_generation_service_1.ReportGenerationService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(report_entity_1.Report),
                    useValue: mockReportRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(report_execution_entity_1.ReportExecution),
                    useValue: mockExecutionRepository,
                },
                {
                    provide: typeorm_2.DataSource,
                    useValue: mockDataSource,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: mockLoggerService,
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: mockAuditService,
                },
                {
                    provide: data_aggregation_service_1.DataAggregationService,
                    useValue: mockDataAggregationService,
                },
                {
                    provide: report_export_service_1.ReportExportService,
                    useValue: mockReportExportService,
                },
            ],
        }).compile();
        service = module.get(report_generation_service_1.ReportGenerationService);
        reportRepository = module.get((0, typeorm_1.getRepositoryToken)(report_entity_1.Report));
        executionRepository = module.get((0, typeorm_1.getRepositoryToken)(report_execution_entity_1.ReportExecution));
        dataSource = module.get(typeorm_2.DataSource);
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
        dataAggregationService = module.get(data_aggregation_service_1.DataAggregationService);
        reportExportService = module.get(report_export_service_1.ReportExportService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('executeReport', () => {
        it('should start report execution successfully', async () => {
            const reportId = 'report-123';
            const parameters = {
                timeRange: {
                    type: 'relative',
                    value: '7d',
                },
                exportFormat: 'pdf',
            };
            const userId = 'user-123';
            mockReportRepository.findOne.mockResolvedValue(mockReport);
            mockExecutionRepository.create.mockReturnValue(mockExecution);
            mockExecutionRepository.save.mockResolvedValue(mockExecution);
            const result = await service.executeReport(reportId, parameters, userId);
            expect(mockReportRepository.findOne).toHaveBeenCalledWith({
                where: { id: reportId },
            });
            expect(mockReport.validateConfiguration).toHaveBeenCalled();
            expect(mockExecutionRepository.create).toHaveBeenCalledWith({
                reportId,
                status: 'pending',
                triggerType: 'manual',
                parameters,
                executedBy: userId,
            });
            expect(mockExecutionRepository.save).toHaveBeenCalledWith(mockExecution);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'execute', 'report', reportId, expect.objectContaining({
                executionId: mockExecution.id,
                triggerType: 'manual',
                parameters,
            }));
            expect(result).toEqual(mockExecution);
        });
        it('should throw error when report not found', async () => {
            const reportId = 'non-existent';
            const userId = 'user-123';
            mockReportRepository.findOne.mockResolvedValue(null);
            await expect(service.executeReport(reportId, {}, userId))
                .rejects.toThrow('Report not found');
        });
        it('should throw error when report is not active', async () => {
            const reportId = 'report-123';
            const userId = 'user-123';
            const inactiveReport = { ...mockReport, status: 'draft' };
            mockReportRepository.findOne.mockResolvedValue(inactiveReport);
            await expect(service.executeReport(reportId, {}, userId))
                .rejects.toThrow('Report is not active');
        });
        it('should throw error when report configuration is invalid', async () => {
            const reportId = 'report-123';
            const userId = 'user-123';
            const invalidReport = {
                ...mockReport,
                validateConfiguration: jest.fn().mockReturnValue({
                    isValid: false,
                    errors: ['Data source is required'],
                }),
            };
            mockReportRepository.findOne.mockResolvedValue(invalidReport);
            await expect(service.executeReport(reportId, {}, userId))
                .rejects.toThrow('Report configuration is invalid: Data source is required');
        });
    });
    describe('getExecution', () => {
        it('should return execution when found', async () => {
            const executionId = 'execution-123';
            mockExecutionRepository.findOne.mockResolvedValue(mockExecution);
            const result = await service.getExecution(executionId);
            expect(mockExecutionRepository.findOne).toHaveBeenCalledWith({
                where: { id: executionId },
                relations: ['report'],
            });
            expect(result).toEqual(mockExecution);
        });
        it('should return null when execution not found', async () => {
            const executionId = 'non-existent';
            mockExecutionRepository.findOne.mockResolvedValue(null);
            const result = await service.getExecution(executionId);
            expect(result).toBeNull();
        });
    });
    describe('cancelExecution', () => {
        it('should cancel running execution successfully', async () => {
            const executionId = 'execution-123';
            const userId = 'user-123';
            const runningExecution = {
                ...mockExecution,
                isRunning: true,
                reportId: 'report-123',
            };
            jest.spyOn(service, 'getExecution').mockResolvedValue(runningExecution);
            mockExecutionRepository.save.mockResolvedValue(runningExecution);
            const result = await service.cancelExecution(executionId, userId);
            expect(service.getExecution).toHaveBeenCalledWith(executionId);
            expect(runningExecution.cancel).toHaveBeenCalledWith('Cancelled by user');
            expect(mockExecutionRepository.save).toHaveBeenCalledWith(runningExecution);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'cancel', 'report_execution', executionId, {
                reportId: runningExecution.reportId,
                reason: 'user_request',
            });
            expect(result).toEqual(runningExecution);
        });
        it('should throw error when execution not found', async () => {
            const executionId = 'non-existent';
            const userId = 'user-123';
            jest.spyOn(service, 'getExecution').mockResolvedValue(null);
            await expect(service.cancelExecution(executionId, userId))
                .rejects.toThrow('Report execution not found');
        });
        it('should throw error when execution is not running', async () => {
            const executionId = 'execution-123';
            const userId = 'user-123';
            const completedExecution = {
                ...mockExecution,
                isRunning: false,
            };
            jest.spyOn(service, 'getExecution').mockResolvedValue(completedExecution);
            await expect(service.cancelExecution(executionId, userId))
                .rejects.toThrow('Report execution is not running');
        });
    });
    describe('processReportExecution', () => {
        it('should process execution successfully', async () => {
            const execution = {
                ...mockExecution,
                start: jest.fn(),
                updateProgress: jest.fn(),
                complete: jest.fn(),
                parameters: {},
            };
            const mockData = {
                vulnerabilities: {
                    data: [
                        { id: 1, severity: 'high', status: 'open' },
                        { id: 2, severity: 'critical', status: 'confirmed' },
                    ],
                },
            };
            const mockProcessedData = {
                totalRecords: 2,
                dataPoints: 2,
                sections: [],
                tables: [
                    {
                        id: 'table-vulnerabilities',
                        title: 'vulnerabilities',
                        headers: ['id', 'severity', 'status'],
                        rows: [
                            [1, 'high', 'open'],
                            [2, 'critical', 'confirmed'],
                        ],
                    },
                ],
                metrics: [],
            };
            const mockVisualizations = {
                charts: [
                    {
                        id: 'chart-123',
                        title: 'Vulnerabilities by Severity',
                        type: 'bar',
                        data: { labels: ['high', 'critical'], datasets: [{ data: [1, 1] }] },
                    },
                ],
            };
            mockDataAggregationService.aggregateData.mockResolvedValue(mockData.vulnerabilities);
            mockReportExportService.exportReport.mockResolvedValue('/path/to/report.pdf');
            mockExecutionRepository.save.mockResolvedValue(execution);
            mockReportRepository.save.mockResolvedValue(mockReport);
            // Mock file size function
            jest.spyOn(service, 'getFileSize').mockResolvedValue(1024);
            await service.processReportExecution(execution, mockReport, 'user-123');
            expect(execution.start).toHaveBeenCalled();
            expect(execution.updateProgress).toHaveBeenCalledWith(10, 'Collecting data');
            expect(execution.updateProgress).toHaveBeenCalledWith(40, 'Processing data');
            expect(execution.updateProgress).toHaveBeenCalledWith(70, 'Generating visualizations');
            expect(execution.updateProgress).toHaveBeenCalledWith(90, 'Exporting report');
            expect(mockDataAggregationService.aggregateData).toHaveBeenCalledWith('vulnerabilities', expect.objectContaining(mockReport.configuration));
            expect(mockReportExportService.exportReport).toHaveBeenCalled();
            expect(execution.complete).toHaveBeenCalled();
            expect(mockReport.updateExecutionStats).toHaveBeenCalled();
        });
        it('should handle execution failure', async () => {
            const execution = {
                ...mockExecution,
                start: jest.fn(),
                updateProgress: jest.fn(),
                fail: jest.fn(),
                parameters: {},
            };
            const error = new Error('Data aggregation failed');
            mockDataAggregationService.aggregateData.mockRejectedValue(error);
            mockExecutionRepository.save.mockResolvedValue(execution);
            mockReportRepository.save.mockResolvedValue(mockReport);
            await service.processReportExecution(execution, mockReport, 'user-123');
            expect(execution.start).toHaveBeenCalled();
            expect(execution.fail).toHaveBeenCalledWith(error);
            expect(mockReport.updateExecutionStats).toHaveBeenCalledWith(expect.any(Number), 0, 'failed');
        });
    });
    describe('data processing methods', () => {
        it('should group data correctly', () => {
            const data = [
                { severity: 'high', status: 'open', count: 1 },
                { severity: 'high', status: 'fixed', count: 2 },
                { severity: 'critical', status: 'open', count: 3 },
            ];
            const groupBy = ['severity'];
            const result = service.groupData(data, groupBy);
            expect(result).toHaveLength(2);
            expect(result[0]).toEqual({
                group: 'high',
                count: 2,
                items: [
                    { severity: 'high', status: 'open', count: 1 },
                    { severity: 'high', status: 'fixed', count: 2 },
                ],
            });
            expect(result[1]).toEqual({
                group: 'critical',
                count: 1,
                items: [
                    { severity: 'critical', status: 'open', count: 3 },
                ],
            });
        });
        it('should aggregate data correctly', () => {
            const data = [
                { severity: 'high', riskScore: 7.5 },
                { severity: 'high', riskScore: 8.0 },
                { severity: 'critical', riskScore: 9.5 },
            ];
            const aggregations = [
                { function: 'count', field: 'severity', alias: 'total_count' },
                { function: 'avg', field: 'riskScore', alias: 'avg_risk' },
                { function: 'max', field: 'riskScore', alias: 'max_risk' },
            ];
            const result = service.aggregateData(data, aggregations);
            expect(result).toHaveLength(3);
            expect(result[0]).toEqual({
                id: 'total_count',
                title: 'total_count',
                value: 3,
                unit: undefined,
            });
            expect(result[1]).toEqual({
                id: 'avg_risk',
                title: 'avg_risk',
                value: 8.333333333333334,
                unit: undefined,
            });
            expect(result[2]).toEqual({
                id: 'max_risk',
                title: 'max_risk',
                value: 9.5,
                unit: undefined,
            });
        });
        it('should generate chart data correctly', async () => {
            const vizConfig = {
                type: 'chart',
                chartType: 'bar',
                title: 'Test Chart',
                dataSource: 'vulnerabilities',
                configuration: {
                    colors: ['#ff0000'],
                },
            };
            const processedData = {
                tables: [
                    {
                        id: 'table-vulnerabilities',
                        title: 'vulnerabilities',
                        headers: ['severity', 'count'],
                        rows: [
                            ['high', 5],
                            ['critical', 3],
                        ],
                    },
                ],
            };
            const result = await service.generateChart(vizConfig, processedData);
            expect(result).toEqual({
                labels: ['high', 'critical'],
                datasets: [{
                        label: 'Test Chart',
                        data: [5, 3],
                        backgroundColor: ['#ff0000'],
                    }],
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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