{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\correlation.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,+DAA2D;AAE3D,iGAAsF;AACtF,oGAA6F;AAE7F,iGAA4F;AAE5F,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,IAAI,OAA2B,CAAC;IAChC,IAAI,kBAAqD,CAAC;IAE1D,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,kBAAkB,GAAG;YACnB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;SACf,CAAC;QAET,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,wCAAkB;gBAClB;oBACE,OAAO,EAAE,6CAAoB;oBAC7B,QAAQ,EAAE,kBAAkB;iBAC7B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAqB,wCAAkB,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,MAAM,GAAG,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEnD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,MAAM,GAAG;gBACb,uBAAuB,CAAC,QAAQ,EAAE,GAAG,CAAC;gBACtC,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,mBAAmB;gBACvF,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,iBAAiB;aACtF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC;gBAC3C,MAAM;gBACN,MAAM,EAAE,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC,YAAY;aAC3C,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,yCAAe,CAAC,QAAQ,CAAC,CAAC;YAClF,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,SAAS,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,eAAe,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;YAEvF,MAAM,MAAM,GAAG;gBACb,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;gBAC1D,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;gBAC1D,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;aAC3D,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE1D,MAAM,oBAAoB,GAAG,MAAM,CAAC,gBAAgB,CAAC,IAAI,CACvD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,KAAK,yCAAe,CAAC,SAAS,CACrD,CAAC;YACF,MAAM,CAAC,oBAAoB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3C,MAAM,CAAC,oBAAoB,EAAE,mBAAmB,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,MAAM,GAAG;gBACb,uBAAuB,CAAC,QAAQ,CAAC;gBACjC,uBAAuB,CAAC,QAAQ,CAAC;aAClC,CAAC;YAEF,mCAAmC;YACnC,kBAAkB,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAE9E,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gDAAgD;YACnF,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,4CAA4C;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,MAAM,GAAG;gBACb,uBAAuB,CAAC,QAAQ,CAAC;gBACjC,uBAAuB,CAAC,QAAQ,CAAC;aAClC,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC;gBAC3C,MAAM;gBACN,MAAM,EAAE;oBACN,SAAS,EAAE,CAAC,EAAE,2BAA2B;oBACzC,YAAY,EAAE,CAAC,yCAAe,CAAC,QAAQ,CAAC;iBACzC;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;YACzE,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,SAAS,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,eAAe,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;YAEvF,MAAM,MAAM,GAAG;gBACb,uBAAuB,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;gBACnD,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;gBAC/E,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;aAChF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC;gBAC3C,MAAM;gBACN,UAAU,EAAE,CAAC,yCAAe,CAAC,SAAS,CAAC,CAAC,6BAA6B;aACtE,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,KAAK,yCAAe,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,MAAM,GAAG;gBACb,uBAAuB,CAAC,QAAQ,EAAE,GAAG,CAAC;gBACtC,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC;gBAClE,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC;aACnE,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YACtE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YACtE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,MAAM,GAAG;gBACb,uBAAuB,CAAC,QAAQ,EAAE,GAAG,CAAC;gBACtC,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC;gBAClE,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC;aACnE,CAAC;YAEF,yCAAyC;YACzC,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YAC1D,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YAE1D,6DAA6D;YAC7D,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,QAAQ,GAAG,uBAAuB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;YAC3D,MAAM,YAAY,GAAG;gBACnB,uBAAuB,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,iBAAiB;gBACtF,uBAAuB,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,eAAe;aACrF,CAAC;YAEF,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,wBAAwB,CACzD,QAAQ,EACR,YAAY,EACZ,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC,YAAY;aACnC,CAAC;YAEF,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YACnC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,QAAQ,GAAG,uBAAuB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;YAC3D,MAAM,YAAY,GAAG;gBACnB,uBAAuB,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,iBAAiB;gBACtF,uBAAuB,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,CAAC,EAAE,cAAc;aACnF,CAAC;YAEF,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,wBAAwB,CACzD,QAAQ,EACR,YAAY,EACZ,EAAE,cAAc,EAAE,EAAE,EAAE,CAAC,aAAa;aACrC,CAAC;YAEF,yDAAyD;YACzD,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mEAAmE,EAAE,KAAK,IAAI,EAAE;YACjF,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,SAAS,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,eAAe,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;YAEvF,MAAM,QAAQ,GAAG,uBAAuB,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;YACxE,MAAM,YAAY,GAAG;gBACnB,uBAAuB,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;aACjF,CAAC;YAEF,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAEpF,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YACnC,sDAAsD;YACtD,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,CAAC,CAAC,yCAAe,CAAC,QAAQ,EAAE,yCAAe,CAAC,SAAS,CAAC,CAAC;qBAC1D,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;YAChD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,aAAa,GAAG;gBACpB,IAAI;gBACJ,SAAS;gBACT,EAAmB;aACpB,CAAC,MAAM,CAAC,OAAO,CAAoB,CAAC;YAErC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC;YAExE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;YAC1E,MAAM,MAAM,GAAG;gBACb,uBAAuB,CAAC,QAAQ,CAAC;gBACjC,uBAAuB,CAAC,QAAQ,CAAC;aAClC,CAAC;YAEF,uEAAuE;YACvE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACjD,uBAAuB,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,CACjD,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC;gBAC3C,MAAM;gBACN,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE;aAC1B,CAAC,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAmC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAClD,uBAAuB,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,CACjD,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC;gBAC3C,MAAM;gBACN,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE;aAC1B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,iDAAiD;IACjD,SAAS,uBAAuB,CAC9B,EAAU,EACV,YAAkB,IAAI,IAAI,EAAE,EAC5B,aAAoB,EAAE;QAEtB,MAAM,SAAS,GAAG;YAChB,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE;YAC1B,SAAS;YACT,UAAU;YACV,eAAe,EAAE,EAAE;YACnB,qBAAqB,EAAE,GAAG,EAAE,CAAC,IAAI;YACjC,eAAe,EAAE,GAAG,EAAE,CAAC,EAAE;YACzB,iBAAiB,EAAE,GAAG,EAAE,GAAE,CAAC;SACrB,CAAC;QAET,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\correlation.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { CorrelationService } from './correlation.service';\r\nimport { EnrichedEvent } from '../../domain/entities/event/enriched-event.entity';\r\nimport { CorrelationType } from '../../domain/entities/event/correlated-event.entity';\r\nimport { IOC, IOCType } from '../../domain/value-objects/threat-indicators/ioc.value-object';\r\nimport { ConfidenceLevel } from '../../domain/enums/confidence-level.enum';\r\nimport { DomainEventPublisher } from '../../../shared-kernel/domain/domain-event-publisher';\r\n\r\ndescribe('CorrelationService', () => {\r\n  let service: CorrelationService;\r\n  let mockEventPublisher: jest.Mocked<DomainEventPublisher>;\r\n\r\n  beforeEach(async () => {\r\n    mockEventPublisher = {\r\n      publishAll: jest.fn(),\r\n    } as any;\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        CorrelationService,\r\n        {\r\n          provide: DomainEventPublisher,\r\n          useValue: mockEventPublisher,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<CorrelationService>(CorrelationService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('correlateEvents', () => {\r\n    it('should return empty result when insufficient events', async () => {\r\n      const events = [createMockEnrichedEvent('event1')];\r\n\r\n      const result = await service.correlateEvents({ events });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.correlatedEvents).toHaveLength(0);\r\n      expect(result.statistics.correlationsFound).toBe(0);\r\n    });\r\n\r\n    it('should correlate events with temporal correlation', async () => {\r\n      const now = new Date();\r\n      const events = [\r\n        createMockEnrichedEvent('event1', now),\r\n        createMockEnrichedEvent('event2', new Date(now.getTime() + 30000)), // 30 seconds later\r\n        createMockEnrichedEvent('event3', new Date(now.getTime() + 60000)), // 1 minute later\r\n      ];\r\n\r\n      const result = await service.correlateEvents({ \r\n        events,\r\n        config: { temporalWindow: 5 } // 5 minutes\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.correlatedEvents.length).toBeGreaterThan(0);\r\n      expect(result.correlatedEvents[0].correlationType).toBe(CorrelationType.TEMPORAL);\r\n      expect(mockEventPublisher.publishAll).toHaveBeenCalled();\r\n    });\r\n\r\n    it('should correlate events with indicator correlation', async () => {\r\n      const commonIOC = IOC.create(IOCType.IP_ADDRESS, '*************', 'high', 'confirmed');\r\n      \r\n      const events = [\r\n        createMockEnrichedEvent('event1', new Date(), [commonIOC]),\r\n        createMockEnrichedEvent('event2', new Date(), [commonIOC]),\r\n        createMockEnrichedEvent('event3', new Date(), [commonIOC]),\r\n      ];\r\n\r\n      const result = await service.correlateEvents({ events });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.correlatedEvents.length).toBeGreaterThan(0);\r\n      \r\n      const indicatorCorrelation = result.correlatedEvents.find(\r\n        c => c.correlationType === CorrelationType.INDICATOR\r\n      );\r\n      expect(indicatorCorrelation).toBeDefined();\r\n      expect(indicatorCorrelation?.correlationAnalysis.commonIndicators).toHaveLength(1);\r\n    });\r\n\r\n    it('should handle correlation errors gracefully', async () => {\r\n      const events = [\r\n        createMockEnrichedEvent('event1'),\r\n        createMockEnrichedEvent('event2'),\r\n      ];\r\n\r\n      // Mock an error in event publisher\r\n      mockEventPublisher.publishAll.mockRejectedValue(new Error('Publisher error'));\r\n\r\n      const result = await service.correlateEvents({ events });\r\n\r\n      expect(result.success).toBe(true); // Should still succeed even if publishing fails\r\n      expect(result.errors.length).toBe(0); // Publisher errors don't affect correlation\r\n    });\r\n\r\n    it('should respect correlation configuration', async () => {\r\n      const events = [\r\n        createMockEnrichedEvent('event1'),\r\n        createMockEnrichedEvent('event2'),\r\n      ];\r\n\r\n      const result = await service.correlateEvents({ \r\n        events,\r\n        config: {\r\n          minEvents: 5, // Require 5 events minimum\r\n          enabledTypes: [CorrelationType.TEMPORAL],\r\n        }\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.correlatedEvents).toHaveLength(0); // Not enough events\r\n    });\r\n\r\n    it('should focus on specific correlation types when requested', async () => {\r\n      const now = new Date();\r\n      const commonIOC = IOC.create(IOCType.IP_ADDRESS, '*************', 'high', 'confirmed');\r\n      \r\n      const events = [\r\n        createMockEnrichedEvent('event1', now, [commonIOC]),\r\n        createMockEnrichedEvent('event2', new Date(now.getTime() + 30000), [commonIOC]),\r\n        createMockEnrichedEvent('event3', new Date(now.getTime() + 60000), [commonIOC]),\r\n      ];\r\n\r\n      const result = await service.correlateEvents({ \r\n        events,\r\n        focusTypes: [CorrelationType.INDICATOR] // Only indicator correlation\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.correlatedEvents.length).toBeGreaterThan(0);\r\n      expect(result.correlatedEvents.every(c => c.correlationType === CorrelationType.INDICATOR)).toBe(true);\r\n    });\r\n\r\n    it('should calculate correlation statistics correctly', async () => {\r\n      const now = new Date();\r\n      const events = [\r\n        createMockEnrichedEvent('event1', now),\r\n        createMockEnrichedEvent('event2', new Date(now.getTime() + 30000)),\r\n        createMockEnrichedEvent('event3', new Date(now.getTime() + 60000)),\r\n      ];\r\n\r\n      const result = await service.correlateEvents({ events });\r\n\r\n      expect(result.statistics.eventsProcessed).toBe(3);\r\n      expect(result.statistics.correlationsFound).toBeGreaterThanOrEqual(0);\r\n      expect(result.statistics.averageConfidence).toBeGreaterThanOrEqual(0);\r\n      expect(result.statistics.typeDistribution).toBeDefined();\r\n    });\r\n\r\n    it('should deduplicate similar correlations', async () => {\r\n      const now = new Date();\r\n      const events = [\r\n        createMockEnrichedEvent('event1', now),\r\n        createMockEnrichedEvent('event2', new Date(now.getTime() + 30000)),\r\n        createMockEnrichedEvent('event3', new Date(now.getTime() + 60000)),\r\n      ];\r\n\r\n      // Run correlation twice with same events\r\n      const result1 = await service.correlateEvents({ events });\r\n      const result2 = await service.correlateEvents({ events });\r\n\r\n      // Should produce same number of correlations (no duplicates)\r\n      expect(result1.correlatedEvents.length).toBe(result2.correlatedEvents.length);\r\n    });\r\n  });\r\n\r\n  describe('correlateStreamingEvents', () => {\r\n    it('should correlate new event with recent events', async () => {\r\n      const now = new Date();\r\n      const newEvent = createMockEnrichedEvent('new_event', now);\r\n      const recentEvents = [\r\n        createMockEnrichedEvent('recent1', new Date(now.getTime() - 30000)), // 30 seconds ago\r\n        createMockEnrichedEvent('recent2', new Date(now.getTime() - 60000)), // 1 minute ago\r\n      ];\r\n\r\n      const correlations = await service.correlateStreamingEvents(\r\n        newEvent,\r\n        recentEvents,\r\n        { temporalWindow: 5 } // 5 minutes\r\n      );\r\n\r\n      expect(correlations).toBeDefined();\r\n      expect(Array.isArray(correlations)).toBe(true);\r\n    });\r\n\r\n    it('should filter events outside time window', async () => {\r\n      const now = new Date();\r\n      const newEvent = createMockEnrichedEvent('new_event', now);\r\n      const recentEvents = [\r\n        createMockEnrichedEvent('recent1', new Date(now.getTime() - 30000)), // 30 seconds ago\r\n        createMockEnrichedEvent('old1', new Date(now.getTime() - 7200000)), // 2 hours ago\r\n      ];\r\n\r\n      const correlations = await service.correlateStreamingEvents(\r\n        newEvent,\r\n        recentEvents,\r\n        { temporalWindow: 60 } // 60 minutes\r\n      );\r\n\r\n      // Should only correlate with recent events, not old ones\r\n      expect(correlations).toBeDefined();\r\n    });\r\n\r\n    it('should focus on temporal and indicator correlations for streaming', async () => {\r\n      const now = new Date();\r\n      const commonIOC = IOC.create(IOCType.IP_ADDRESS, '*************', 'high', 'confirmed');\r\n      \r\n      const newEvent = createMockEnrichedEvent('new_event', now, [commonIOC]);\r\n      const recentEvents = [\r\n        createMockEnrichedEvent('recent1', new Date(now.getTime() - 30000), [commonIOC]),\r\n      ];\r\n\r\n      const correlations = await service.correlateStreamingEvents(newEvent, recentEvents);\r\n\r\n      expect(correlations).toBeDefined();\r\n      // Should focus on temporal and indicator correlations\r\n      if (correlations.length > 0) {\r\n        expect([CorrelationType.TEMPORAL, CorrelationType.INDICATOR])\r\n          .toContain(correlations[0].correlationType);\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle invalid events gracefully', async () => {\r\n      const invalidEvents = [\r\n        null,\r\n        undefined,\r\n        {} as EnrichedEvent,\r\n      ].filter(Boolean) as EnrichedEvent[];\r\n\r\n      const result = await service.correlateEvents({ events: invalidEvents });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.correlatedEvents).toHaveLength(0);\r\n    });\r\n\r\n    it('should continue processing when one correlation type fails', async () => {\r\n      const events = [\r\n        createMockEnrichedEvent('event1'),\r\n        createMockEnrichedEvent('event2'),\r\n      ];\r\n\r\n      // This should not throw even if some correlation types fail internally\r\n      const result = await service.correlateEvents({ events });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('performance', () => {\r\n    it('should handle large number of events efficiently', async () => {\r\n      const events = Array.from({ length: 50 }, (_, i) => \r\n        createMockEnrichedEvent(`event${i}`, new Date())\r\n      );\r\n\r\n      const startTime = Date.now();\r\n      const result = await service.correlateEvents({ \r\n        events,\r\n        config: { maxEvents: 50 }\r\n      });\r\n      const duration = Date.now() - startTime;\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds\r\n    });\r\n\r\n    it('should respect maxEvents configuration', async () => {\r\n      const events = Array.from({ length: 200 }, (_, i) => \r\n        createMockEnrichedEvent(`event${i}`, new Date())\r\n      );\r\n\r\n      const result = await service.correlateEvents({ \r\n        events,\r\n        config: { maxEvents: 50 }\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.statistics.eventsProcessed).toBeLessThanOrEqual(50);\r\n    });\r\n  });\r\n\r\n  // Helper function to create mock enriched events\r\n  function createMockEnrichedEvent(\r\n    id: string, \r\n    createdAt: Date = new Date(),\r\n    indicators: IOC[] = []\r\n  ): EnrichedEvent {\r\n    const mockEvent = {\r\n      id: { toString: () => id },\r\n      createdAt,\r\n      indicators,\r\n      enrichmentScore: 80,\r\n      isReadyForCorrelation: () => true,\r\n      getDomainEvents: () => [],\r\n      clearDomainEvents: () => {},\r\n    } as any;\r\n\r\n    return mockEvent;\r\n  }\r\n});\r\n"], "version": 3}