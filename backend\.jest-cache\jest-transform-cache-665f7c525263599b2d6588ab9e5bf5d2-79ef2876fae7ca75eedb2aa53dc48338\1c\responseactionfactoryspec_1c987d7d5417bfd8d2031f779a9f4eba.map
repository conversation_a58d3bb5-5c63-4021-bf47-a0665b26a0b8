{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\response-action.factory.spec.ts", "mappings": ";;AAAA,wEAAgG;AAChG,kFAAuE;AACvE,gEAA8D;AAC9D,mEAA0D;AAC1D,uEAA8D;AAE9D,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,IAAI,WAAwC,CAAC;IAE7C,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG;YACZ,UAAU,EAAE,6BAAU,CAAC,QAAQ;YAC/B,KAAK,EAAE,oBAAoB;YAC3B,UAAU,EAAE,EAAE,SAAS,EAAE,eAAe,EAAE;YAC1C,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;aACtB;SACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,uCAAc,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB;YAC/D,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,yBAAyB;YACjE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,mDAAmD;YACzF,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,gCAAgC;YACnE,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAC1C,GAAG,WAAW;gBACd,QAAQ,EAAE,UAAU;gBACpB,WAAW,EAAE,KAAK;gBAClB,UAAU,EAAE,CAAC;gBACb,cAAc,EAAE,EAAE;aACnB,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,QAAQ,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAC1C,GAAG,WAAW;gBACd,EAAE,EAAE,QAAQ;aACb,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YAC3E,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,MAAM,GAAG,+CAAqB,CAAC,uBAAuB,CAC1D,6BAAU,CAAC,cAAc,EACzB,4BAA4B,EAC5B,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAE,EACpC,EAAE,QAAQ,EAAE,YAAY,EAAE,CAC3B,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,uBAAuB,CAC3C,6BAAU,CAAC,UAAU,EACrB,YAAY,EACZ,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAE,EACpC,EAAE,SAAS,EAAE,mBAAmB,EAAE,CACnC,CAAC;YACJ,CAAC,CAAC,CAAC,OAAO,CAAC,6CAA6C,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,MAAM,GAAG,+CAAqB,CAAC,uBAAuB,CAC1D,6BAAU,CAAC,cAAc,EACzB,yBAAyB,EACzB,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAE,EACpC,EAAE,SAAS,EAAE,aAAa,EAAE,CAC7B,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,uBAAuB,CAC3C,6BAAU,CAAC,UAAU,EACrB,YAAY,EACZ,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAE,EACpC,EAAE,SAAS,EAAE,mBAAmB,EAAE,CACnC,CAAC;YACJ,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,MAAM,GAAG,+CAAqB,CAAC,oBAAoB,CACvD,6BAAU,CAAC,cAAc,EACzB,qBAAqB,EACrB,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAE,EACpC,EAAE,QAAQ,EAAE,YAAY,EAAE,CAC3B,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,oBAAoB,CACxC,6BAAU,CAAC,UAAU,EACrB,YAAY,EACZ,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAE,EACpC,EAAE,SAAS,EAAE,mBAAmB,EAAE,CACnC,CAAC;YACJ,CAAC,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,MAAM,GAAG,+CAAqB,CAAC,wBAAwB,CAC3D,6BAAU,CAAC,UAAU,EACrB,qBAAqB,EACrB,EAAE,UAAU,EAAE,CAAC,mBAAmB,CAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,CACjE,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,UAAU,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,wBAAwB,CAC5C,6BAAU,CAAC,QAAQ,EACnB,UAAU,EACV,EAAE,SAAS,EAAE,eAAe,EAAE,CAC/B,CAAC;YACJ,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,MAAM,GAAG,+CAAqB,CAAC,qBAAqB,CACxD,6BAAU,CAAC,QAAQ,EACnB,eAAe,EACf,EAAE,SAAS,EAAE,eAAe,EAAE,CAC/B,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,qBAAqB,CACzC,6BAAU,CAAC,oBAAoB,EAC/B,sBAAsB,EACtB,EAAE,MAAM,EAAE,YAAY,EAAE,CACzB,CAAC;YACJ,CAAC,CAAC,CAAC,OAAO,CAAC,sDAAsD,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,MAAM,GAAG,+CAAqB,CAAC,kBAAkB,CACrD,6BAAU,CAAC,oBAAoB,EAC/B,sBAAsB,EACtB,EAAE,UAAU,EAAE,cAAc,EAAE,CAC/B,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,oBAAoB,CAAC,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,kBAAkB,CACtC,6BAAU,CAAC,QAAQ,EACnB,UAAU,EACV,EAAE,SAAS,EAAE,eAAe,EAAE,CAC/B,CAAC;YACJ,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,MAAM,GAAG,+CAAqB,CAAC,wBAAwB,CAC3D,6BAAU,CAAC,QAAQ,EACnB,iBAAiB,EACjB,EAAE,SAAS,EAAE,eAAe,EAAE,CAC/B,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,MAAM,GAAG,+CAAqB,CAAC,oBAAoB,CACvD,6BAAU,CAAC,eAAe,EAC1B,oBAAoB,EACpB,EAAE,QAAQ,EAAE,YAAY,EAAE,CAC3B,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,YAAY,GAAG,+CAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAC/D,MAAM,WAAW,GAAG,+CAAqB,CAAC,mBAAmB,CAC3D,YAAY,EACZ,6BAAU,CAAC,UAAU,EACrB,oBAAoB,EACpB,EAAE,UAAU,EAAE,CAAC,mBAAmB,CAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CACxE,CAAC;YAEF,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvE,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnE,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACzD,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAC9C,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAC1C,MAAM,MAAM,GAAG,+CAAqB,CAAC,iBAAiB,CACpD,OAAO,EACP,6BAAU,CAAC,QAAQ,EACnB,qBAAqB,EACrB,EAAE,SAAS,EAAE,eAAe,EAAE,CAC/B,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,QAAQ,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,MAAM,GAAG,+CAAqB,CAAC,kBAAkB,CACrD,QAAQ,EACR,6BAAU,CAAC,cAAc,EACzB,4BAA4B,EAC5B,EAAE,QAAQ,EAAE,YAAY,EAAE,CAC3B,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,eAAe,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAClD,MAAM,MAAM,GAAG,+CAAqB,CAAC,yBAAyB,CAC5D,eAAe,EACf,6BAAU,CAAC,mBAAmB,EAC9B,qBAAqB,EACrB,EAAE,OAAO,EAAE,WAAW,EAAE,CACzB,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,sBAAsB,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1E,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,cAAc,GAAG,EAAE,GAAG,WAAW,EAAE,CAAC;YAC1C,OAAQ,cAAsB,CAAC,UAAU,CAAC;YAE1C,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,2BAA2B,CAAC,cAAc,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,cAAc,GAAG,EAAE,GAAG,WAAW,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;YAErD,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,2BAA2B,CAAC,cAAc,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,cAAc,GAAG,EAAE,GAAG,WAAW,EAAE,CAAC;YAC1C,OAAQ,cAAsB,CAAC,UAAU,CAAC;YAE1C,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,2BAA2B,CAAC,cAAc,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,UAAU,EAAE,6BAAU,CAAC,QAAQ;oBAC/B,KAAK,EAAE,UAAU;oBACjB,UAAU,EAAE,EAAE,SAAS,EAAE,eAAe,EAAE;iBAC3C,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,UAAU,EAAE,6BAAU,CAAC,QAAQ;oBAC/B,KAAK,EAAE,UAAU;oBACjB,UAAU,EAAE,EAAE;iBACf,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,sDAAsD,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,UAAU,EAAE,6BAAU,CAAC,YAAY;oBACnC,KAAK,EAAE,cAAc;oBACrB,UAAU,EAAE,EAAE,MAAM,EAAE,eAAe,EAAE;iBACxC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,UAAU,EAAE,6BAAU,CAAC,YAAY;oBACnC,KAAK,EAAE,cAAc;oBACrB,UAAU,EAAE,EAAE;iBACf,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,sDAAsD,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,UAAU,EAAE,6BAAU,CAAC,eAAe;oBACtC,KAAK,EAAE,iBAAiB;oBACxB,UAAU,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE;iBACtC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,UAAU,EAAE,6BAAU,CAAC,eAAe;oBACtC,KAAK,EAAE,iBAAiB;oBACxB,UAAU,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;iBACrC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,UAAU,EAAE,6BAAU,CAAC,eAAe;oBACtC,KAAK,EAAE,iBAAiB;oBACxB,UAAU,EAAE,EAAE;iBACf,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,yEAAyE,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,UAAU,EAAE,6BAAU,CAAC,eAAe;oBACtC,KAAK,EAAE,iBAAiB;oBACxB,UAAU,EAAE,EAAE,QAAQ,EAAE,kBAAkB,EAAE;iBAC7C,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,UAAU,EAAE,6BAAU,CAAC,eAAe;oBACtC,KAAK,EAAE,iBAAiB;oBACxB,UAAU,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;iBACnC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,UAAU,EAAE,6BAAU,CAAC,eAAe;oBACtC,KAAK,EAAE,iBAAiB;oBACxB,UAAU,EAAE,EAAE;iBACf,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,oEAAoE,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,UAAU,EAAE,6BAAU,CAAC,UAAU;oBACjC,KAAK,EAAE,YAAY;oBACnB,UAAU,EAAE,EAAE,UAAU,EAAE,CAAC,mBAAmB,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE;iBACpE,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,UAAU,EAAE,6BAAU,CAAC,UAAU;oBACjC,KAAK,EAAE,YAAY;oBACnB,UAAU,EAAE,EAAE,UAAU,EAAE,CAAC,mBAAmB,CAAC,EAAE;iBAClD,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,sEAAsE,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAC1C,UAAU,EAAE,6BAAU,CAAC,eAAe;gBACtC,KAAK,EAAE,oBAAoB;gBAC3B,UAAU,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE;aACvC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAC1C,UAAU,EAAE,6BAAU,CAAC,cAAc;gBACrC,KAAK,EAAE,gBAAgB;gBACvB,UAAU,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE;aACvC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAC1C,UAAU,EAAE,6BAAU,CAAC,UAAU;gBACjC,KAAK,EAAE,mBAAmB;gBAC1B,UAAU,EAAE,EAAE,UAAU,EAAE,CAAC,mBAAmB,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE;aACpE,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\response-action.factory.spec.ts"], "sourcesContent": ["import { ResponseActionFactory, CreateResponseActionOptions } from '../response-action.factory';\r\nimport { ResponseAction } from '../../entities/response-action.entity';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { ActionType } from '../../enums/action-type.enum';\r\nimport { ActionStatus } from '../../enums/action-status.enum';\r\n\r\ndescribe('ResponseActionFactory', () => {\r\n  let baseOptions: CreateResponseActionOptions;\r\n\r\n  beforeEach(() => {\r\n    baseOptions = {\r\n      actionType: ActionType.BLOCK_IP,\r\n      title: 'Block malicious IP',\r\n      parameters: { ipAddress: '*************' },\r\n      target: {\r\n        type: 'system',\r\n        id: 'firewall-001',\r\n        name: 'Main Firewall',\r\n      },\r\n    };\r\n  });\r\n\r\n  describe('create', () => {\r\n    it('should create a ResponseAction with basic options', () => {\r\n      const action = ResponseActionFactory.create(baseOptions);\r\n\r\n      expect(action).toBeInstanceOf(ResponseAction);\r\n      expect(action.actionType).toBe(ActionType.BLOCK_IP);\r\n      expect(action.title).toBe('Block malicious IP');\r\n      expect(action.parameters.ipAddress).toBe('*************');\r\n      expect(action.target?.type).toBe('system');\r\n    });\r\n\r\n    it('should apply defaults based on action type', () => {\r\n      const action = ResponseActionFactory.create(baseOptions);\r\n\r\n      expect(action.isAutomated).toBe(true); // BLOCK_IP is automated\r\n      expect(action.isReversible).toBe(true); // BLOCK_IP is reversible\r\n      expect(action.priority).toBe('high'); // BLOCK_IP is containment action, so high priority\r\n      expect(action.maxRetries).toBe(3); // Default for automated actions\r\n      expect(action.timeoutMinutes).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should override defaults with provided options', () => {\r\n      const action = ResponseActionFactory.create({\r\n        ...baseOptions,\r\n        priority: 'critical',\r\n        isAutomated: false,\r\n        maxRetries: 5,\r\n        timeoutMinutes: 60,\r\n      });\r\n\r\n      expect(action.priority).toBe('critical');\r\n      expect(action.isAutomated).toBe(false);\r\n      expect(action.maxRetries).toBe(5);\r\n      expect(action.timeoutMinutes).toBe(60);\r\n    });\r\n\r\n    it('should create with custom ID', () => {\r\n      const customId = UniqueEntityId.generate();\r\n      const action = ResponseActionFactory.create({\r\n        ...baseOptions,\r\n        id: customId,\r\n      });\r\n\r\n      expect(action.id.equals(customId)).toBe(true);\r\n    });\r\n\r\n    it('should set up rollback info for reversible actions', () => {\r\n      const action = ResponseActionFactory.create(baseOptions);\r\n\r\n      expect(action.rollbackInfo).toBeDefined();\r\n      expect(action.rollbackInfo?.canRollback).toBe(true);\r\n      expect(action.rollbackInfo?.rollbackSteps).toContain('Remove IP from block list');\r\n    });\r\n\r\n    it('should generate appropriate tags based on action characteristics', () => {\r\n      const action = ResponseActionFactory.create(baseOptions);\r\n\r\n      expect(action.tags).toContain('automated');\r\n      expect(action.tags).toContain('reversible');\r\n    });\r\n\r\n    it('should set success criteria based on action type', () => {\r\n      const action = ResponseActionFactory.create(baseOptions);\r\n\r\n      expect(action.successCriteria).toContain('IP address blocked in firewall');\r\n      expect(action.successCriteria).toContain('Traffic from IP stopped');\r\n    });\r\n  });\r\n\r\n  describe('createContainmentAction', () => {\r\n    it('should create a containment action', () => {\r\n      const action = ResponseActionFactory.createContainmentAction(\r\n        ActionType.ISOLATE_SYSTEM,\r\n        'Isolate compromised system',\r\n        { type: 'system', id: 'server-001' },\r\n        { systemId: 'server-001' }\r\n      );\r\n\r\n      expect(action.actionType).toBe(ActionType.ISOLATE_SYSTEM);\r\n      expect(action.priority).toBe('high');\r\n      expect(action.tags).toContain('containment');\r\n      expect(action.tags).toContain('security');\r\n    });\r\n\r\n    it('should throw error for non-containment action type', () => {\r\n      expect(() => {\r\n        ResponseActionFactory.createContainmentAction(\r\n          ActionType.SEND_EMAIL,\r\n          'Send email',\r\n          { type: 'system', id: 'server-001' },\r\n          { recipient: '<EMAIL>' }\r\n        );\r\n      }).toThrow('send_email is not a containment action type');\r\n    });\r\n  });\r\n\r\n  describe('createEradicationAction', () => {\r\n    it('should create an eradication action', () => {\r\n      const action = ResponseActionFactory.createEradicationAction(\r\n        ActionType.REMOVE_MALWARE,\r\n        'Remove detected malware',\r\n        { type: 'system', id: 'server-001' },\r\n        { malwareId: 'malware-123' }\r\n      );\r\n\r\n      expect(action.actionType).toBe(ActionType.REMOVE_MALWARE);\r\n      expect(action.priority).toBe('high');\r\n      expect(action.approvalRequired).toBe(true);\r\n      expect(action.tags).toContain('eradication');\r\n      expect(action.tags).toContain('security');\r\n    });\r\n\r\n    it('should throw error for non-eradication action type', () => {\r\n      expect(() => {\r\n        ResponseActionFactory.createEradicationAction(\r\n          ActionType.SEND_EMAIL,\r\n          'Send email',\r\n          { type: 'system', id: 'server-001' },\r\n          { recipient: '<EMAIL>' }\r\n        );\r\n      }).toThrow('send_email is not an eradication action type');\r\n    });\r\n  });\r\n\r\n  describe('createRecoveryAction', () => {\r\n    it('should create a recovery action', () => {\r\n      const action = ResponseActionFactory.createRecoveryAction(\r\n        ActionType.RESTORE_BACKUP,\r\n        'Restore from backup',\r\n        { type: 'system', id: 'server-001' },\r\n        { backupId: 'backup-123' }\r\n      );\r\n\r\n      expect(action.actionType).toBe(ActionType.RESTORE_BACKUP);\r\n      expect(action.priority).toBe('normal');\r\n      expect(action.tags).toContain('recovery');\r\n      expect(action.tags).toContain('restoration');\r\n    });\r\n\r\n    it('should throw error for non-recovery action type', () => {\r\n      expect(() => {\r\n        ResponseActionFactory.createRecoveryAction(\r\n          ActionType.SEND_EMAIL,\r\n          'Send email',\r\n          { type: 'system', id: 'server-001' },\r\n          { recipient: '<EMAIL>' }\r\n        );\r\n      }).toThrow('send_email is not a recovery action type');\r\n    });\r\n  });\r\n\r\n  describe('createNotificationAction', () => {\r\n    it('should create a notification action', () => {\r\n      const action = ResponseActionFactory.createNotificationAction(\r\n        ActionType.SEND_EMAIL,\r\n        'Send security alert',\r\n        { recipients: ['<EMAIL>'], subject: 'Security Alert' }\r\n      );\r\n\r\n      expect(action.actionType).toBe(ActionType.SEND_EMAIL);\r\n      expect(action.priority).toBe('normal');\r\n      expect(action.isAutomated).toBe(true);\r\n      expect(action.approvalRequired).toBe(false);\r\n      expect(action.tags).toContain('notification');\r\n      expect(action.tags).toContain('communication');\r\n    });\r\n\r\n    it('should throw error for non-notification action type', () => {\r\n      expect(() => {\r\n        ResponseActionFactory.createNotificationAction(\r\n          ActionType.BLOCK_IP,\r\n          'Block IP',\r\n          { ipAddress: '*************' }\r\n        );\r\n      }).toThrow('block_ip is not a notification action type');\r\n    });\r\n  });\r\n\r\n  describe('createAutomatedAction', () => {\r\n    it('should create an automated action', () => {\r\n      const action = ResponseActionFactory.createAutomatedAction(\r\n        ActionType.BLOCK_IP,\r\n        'Auto-block IP',\r\n        { ipAddress: '*************' }\r\n      );\r\n\r\n      expect(action.actionType).toBe(ActionType.BLOCK_IP);\r\n      expect(action.isAutomated).toBe(true);\r\n      expect(action.status).toBe(ActionStatus.APPROVED); // Auto-approved\r\n    });\r\n\r\n    it('should throw error for non-automated action type', () => {\r\n      expect(() => {\r\n        ResponseActionFactory.createAutomatedAction(\r\n          ActionType.MANUAL_INVESTIGATION,\r\n          'Manual investigation',\r\n          { target: 'system-001' }\r\n        );\r\n      }).toThrow('manual_investigation is not an automated action type');\r\n    });\r\n  });\r\n\r\n  describe('createManualAction', () => {\r\n    it('should create a manual action', () => {\r\n      const action = ResponseActionFactory.createManualAction(\r\n        ActionType.MANUAL_INVESTIGATION,\r\n        'Investigate incident',\r\n        { incidentId: 'incident-123' }\r\n      );\r\n\r\n      expect(action.actionType).toBe(ActionType.MANUAL_INVESTIGATION);\r\n      expect(action.isAutomated).toBe(false);\r\n      expect(action.approvalRequired).toBe(true);\r\n      expect(action.tags).toContain('manual');\r\n      expect(action.tags).toContain('human-required');\r\n    });\r\n\r\n    it('should throw error for non-manual action type', () => {\r\n      expect(() => {\r\n        ResponseActionFactory.createManualAction(\r\n          ActionType.BLOCK_IP,\r\n          'Block IP',\r\n          { ipAddress: '*************' }\r\n        );\r\n      }).toThrow('block_ip is not a manual action type');\r\n    });\r\n  });\r\n\r\n  describe('createHighPriorityAction', () => {\r\n    it('should create a high priority action', () => {\r\n      const action = ResponseActionFactory.createHighPriorityAction(\r\n        ActionType.BLOCK_IP,\r\n        'Urgent IP block',\r\n        { ipAddress: '*************' }\r\n      );\r\n\r\n      expect(action.priority).toBe('high');\r\n      expect(action.tags).toContain('high-priority');\r\n      expect(action.tags).toContain('urgent');\r\n    });\r\n  });\r\n\r\n  describe('createCriticalAction', () => {\r\n    it('should create a critical action', () => {\r\n      const action = ResponseActionFactory.createCriticalAction(\r\n        ActionType.SHUTDOWN_SYSTEM,\r\n        'Emergency shutdown',\r\n        { systemId: 'server-001' }\r\n      );\r\n\r\n      expect(action.priority).toBe('critical');\r\n      expect(action.approvalRequired).toBe(true);\r\n      expect(action.approvalLevel).toBe('manager');\r\n      expect(action.tags).toContain('critical');\r\n      expect(action.tags).toContain('emergency');\r\n    });\r\n  });\r\n\r\n  describe('createChainedAction', () => {\r\n    it('should create a chained action', () => {\r\n      const parentAction = ResponseActionFactory.create(baseOptions);\r\n      const childAction = ResponseActionFactory.createChainedAction(\r\n        parentAction,\r\n        ActionType.SEND_EMAIL,\r\n        'Notify of IP block',\r\n        { recipients: ['<EMAIL>'], subject: 'IP Block Notification' }\r\n      );\r\n\r\n      expect(childAction.parentActionId?.equals(parentAction.id)).toBe(true);\r\n      expect(childAction.correlationId).toBe(parentAction.id.toString());\r\n      expect(childAction.priority).toBe(parentAction.priority);\r\n      expect(childAction.tags).toContain('chained');\r\n      expect(parentAction.childActionIds).toContain(childAction.id);\r\n    });\r\n  });\r\n\r\n  describe('fromEventResponse', () => {\r\n    it('should create action from event response', () => {\r\n      const eventId = UniqueEntityId.generate();\r\n      const action = ResponseActionFactory.fromEventResponse(\r\n        eventId,\r\n        ActionType.BLOCK_IP,\r\n        'Block IP from event',\r\n        { ipAddress: '*************' }\r\n      );\r\n\r\n      expect(action.relatedEventId?.equals(eventId)).toBe(true);\r\n      expect(action.target?.type).toBe('event');\r\n      expect(action.target?.id).toBe(eventId.toString());\r\n      expect(action.tags).toContain('event-response');\r\n      expect(action.tags).toContain('automated-response');\r\n    });\r\n  });\r\n\r\n  describe('fromThreatResponse', () => {\r\n    it('should create action from threat response', () => {\r\n      const threatId = UniqueEntityId.generate();\r\n      const action = ResponseActionFactory.fromThreatResponse(\r\n        threatId,\r\n        ActionType.ISOLATE_SYSTEM,\r\n        'Isolate system from threat',\r\n        { systemId: 'server-001' }\r\n      );\r\n\r\n      expect(action.relatedThreatId?.equals(threatId)).toBe(true);\r\n      expect(action.target?.type).toBe('threat');\r\n      expect(action.target?.id).toBe(threatId.toString());\r\n      expect(action.priority).toBe('high');\r\n      expect(action.tags).toContain('threat-response');\r\n      expect(action.tags).toContain('security');\r\n    });\r\n  });\r\n\r\n  describe('fromVulnerabilityResponse', () => {\r\n    it('should create action from vulnerability response', () => {\r\n      const vulnerabilityId = UniqueEntityId.generate();\r\n      const action = ResponseActionFactory.fromVulnerabilityResponse(\r\n        vulnerabilityId,\r\n        ActionType.PATCH_VULNERABILITY,\r\n        'Patch vulnerability',\r\n        { patchId: 'patch-123' }\r\n      );\r\n\r\n      expect(action.relatedVulnerabilityId?.equals(vulnerabilityId)).toBe(true);\r\n      expect(action.target?.type).toBe('vulnerability');\r\n      expect(action.target?.id).toBe(vulnerabilityId.toString());\r\n      expect(action.tags).toContain('vulnerability-response');\r\n      expect(action.tags).toContain('remediation');\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should validate action configuration', () => {\r\n      expect(() => {\r\n        ResponseActionFactory.validateActionConfiguration(baseOptions);\r\n      }).not.toThrow();\r\n    });\r\n\r\n    it('should throw error for missing action type', () => {\r\n      const invalidOptions = { ...baseOptions };\r\n      delete (invalidOptions as any).actionType;\r\n\r\n      expect(() => {\r\n        ResponseActionFactory.validateActionConfiguration(invalidOptions);\r\n      }).toThrow('Action type is required');\r\n    });\r\n\r\n    it('should throw error for empty title', () => {\r\n      const invalidOptions = { ...baseOptions, title: '' };\r\n\r\n      expect(() => {\r\n        ResponseActionFactory.validateActionConfiguration(invalidOptions);\r\n      }).toThrow('Action title is required');\r\n    });\r\n\r\n    it('should throw error for missing parameters', () => {\r\n      const invalidOptions = { ...baseOptions };\r\n      delete (invalidOptions as any).parameters;\r\n\r\n      expect(() => {\r\n        ResponseActionFactory.validateActionConfiguration(invalidOptions);\r\n      }).toThrow('Action parameters are required');\r\n    });\r\n  });\r\n\r\n  describe('action type specific validation', () => {\r\n    it('should validate BLOCK_IP parameters', () => {\r\n      expect(() => {\r\n        ResponseActionFactory.create({\r\n          actionType: ActionType.BLOCK_IP,\r\n          title: 'Block IP',\r\n          parameters: { ipAddress: '*************' },\r\n        });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        ResponseActionFactory.create({\r\n          actionType: ActionType.BLOCK_IP,\r\n          title: 'Block IP',\r\n          parameters: {},\r\n        });\r\n      }).toThrow('IP address parameter is required for BLOCK_IP action');\r\n    });\r\n\r\n    it('should validate BLOCK_DOMAIN parameters', () => {\r\n      expect(() => {\r\n        ResponseActionFactory.create({\r\n          actionType: ActionType.BLOCK_DOMAIN,\r\n          title: 'Block domain',\r\n          parameters: { domain: 'malicious.com' },\r\n        });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        ResponseActionFactory.create({\r\n          actionType: ActionType.BLOCK_DOMAIN,\r\n          title: 'Block domain',\r\n          parameters: {},\r\n        });\r\n      }).toThrow('Domain parameter is required for BLOCK_DOMAIN action');\r\n    });\r\n\r\n    it('should validate DISABLE_ACCOUNT parameters', () => {\r\n      expect(() => {\r\n        ResponseActionFactory.create({\r\n          actionType: ActionType.DISABLE_ACCOUNT,\r\n          title: 'Disable account',\r\n          parameters: { accountId: 'user-123' },\r\n        });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        ResponseActionFactory.create({\r\n          actionType: ActionType.DISABLE_ACCOUNT,\r\n          title: 'Disable account',\r\n          parameters: { username: 'john.doe' },\r\n        });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        ResponseActionFactory.create({\r\n          actionType: ActionType.DISABLE_ACCOUNT,\r\n          title: 'Disable account',\r\n          parameters: {},\r\n        });\r\n      }).toThrow('Account ID or username parameter is required for DISABLE_ACCOUNT action');\r\n    });\r\n\r\n    it('should validate QUARANTINE_FILE parameters', () => {\r\n      expect(() => {\r\n        ResponseActionFactory.create({\r\n          actionType: ActionType.QUARANTINE_FILE,\r\n          title: 'Quarantine file',\r\n          parameters: { filePath: '/tmp/malware.exe' },\r\n        });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        ResponseActionFactory.create({\r\n          actionType: ActionType.QUARANTINE_FILE,\r\n          title: 'Quarantine file',\r\n          parameters: { fileHash: 'abc123' },\r\n        });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        ResponseActionFactory.create({\r\n          actionType: ActionType.QUARANTINE_FILE,\r\n          title: 'Quarantine file',\r\n          parameters: {},\r\n        });\r\n      }).toThrow('File path or hash parameter is required for QUARANTINE_FILE action');\r\n    });\r\n\r\n    it('should validate SEND_EMAIL parameters', () => {\r\n      expect(() => {\r\n        ResponseActionFactory.create({\r\n          actionType: ActionType.SEND_EMAIL,\r\n          title: 'Send email',\r\n          parameters: { recipients: ['<EMAIL>'], subject: 'Alert' },\r\n        });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        ResponseActionFactory.create({\r\n          actionType: ActionType.SEND_EMAIL,\r\n          title: 'Send email',\r\n          parameters: { recipients: ['<EMAIL>'] },\r\n        });\r\n      }).toThrow('Recipients and subject parameters are required for SEND_EMAIL action');\r\n    });\r\n  });\r\n\r\n  describe('default generation', () => {\r\n    it('should generate appropriate defaults for high-risk actions', () => {\r\n      const action = ResponseActionFactory.create({\r\n        actionType: ActionType.SHUTDOWN_SYSTEM,\r\n        title: 'Emergency shutdown',\r\n        parameters: { systemId: 'server-001' },\r\n      });\r\n\r\n      expect(action.priority).toBe('high');\r\n      expect(action.approvalRequired).toBe(true);\r\n      expect(action.tags).toContain('high-risk');\r\n    });\r\n\r\n    it('should generate appropriate defaults for containment actions', () => {\r\n      const action = ResponseActionFactory.create({\r\n        actionType: ActionType.ISOLATE_SYSTEM,\r\n        title: 'Isolate system',\r\n        parameters: { systemId: 'server-001' },\r\n      });\r\n\r\n      expect(action.priority).toBe('high');\r\n      expect(action.tags).toContain('containment');\r\n    });\r\n\r\n    it('should generate appropriate defaults for notification actions', () => {\r\n      const action = ResponseActionFactory.create({\r\n        actionType: ActionType.SEND_EMAIL,\r\n        title: 'Send notification',\r\n        parameters: { recipients: ['<EMAIL>'], subject: 'Alert' },\r\n      });\r\n\r\n      expect(action.tags).toContain('notification');\r\n      expect(action.isAutomated).toBe(true);\r\n      expect(action.approvalRequired).toBe(false);\r\n    });\r\n  });\r\n});"], "version": 3}