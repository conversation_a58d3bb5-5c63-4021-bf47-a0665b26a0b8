{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\incident-response\\domain\\entities\\incident-task.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,uDAA6C;AAE7C;;;GAGG;AAOI,IAAM,YAAY,GAAlB,MAAM,YAAY;IAoNvB;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW;YAAE,OAAO,KAAK,CAAC;QAC/D,OAAO,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,QAAQ,IAAI,KAAK,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,KAAK,SAAS;gBACZ,OAAO,CAAC,CAAC;YACX,KAAK,aAAa;gBAChB,OAAO,EAAE,CAAC;YACZ,KAAK,WAAW;gBACd,OAAO,GAAG,CAAC;YACb,KAAK,QAAQ,CAAC;YACd,KAAK,WAAW,CAAC;YACjB,KAAK,SAAS;gBACZ,OAAO,CAAC,CAAC;YACX;gBACE,OAAO,CAAC,CAAC;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAc;QAClB,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACpD,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,MAAc,EAAE,MAAY,EAAE,OAAa;QAClD,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACtD,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC;QAElC,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC;QACjC,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC;QACnC,CAAC;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YAC7B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACrD,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YACrD,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QACzF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,MAAc,EAAE,YAAoB;QACvC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACtD,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC;QAClC,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY,CAAC;QAE3C,qBAAqB;QACrB,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YAC7B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACrD,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YACrD,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QACzF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAc,EAAE,MAAc;QAClC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,cAAc,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAc,EAAE,MAAe;QACpC,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,gBAAgB,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;QAClE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,UAAkB,EAAE,QAAiC,EAAE,QAAiB;QAClF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YAC9B,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC;QAChC,CAAC;QAED,0CAA0C;QAC1C,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,UAAU,CAAC,CAAC;QAE7F,mBAAmB;QACnB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC;YAC5B,UAAU;YACV,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,QAAQ;SACT,CAAC,CAAC;QAEH,+CAA+C;QAC/C,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS;YAAE,OAAO;QAEjE,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC;QACpD,IAAI,CAAC,cAAc;YAAE,OAAO;QAE5B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;QAC3C,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;QAC9E,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;QAC9E,MAAM,cAAc,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC;QAEvD,QAAQ,cAAc,CAAC,YAAY,EAAE,CAAC;YACpC,KAAK,KAAK;gBACR,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;oBACtB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,mBAAmB;gBAC9C,CAAC;qBAAM,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;oBAC7B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;gBAC1B,CAAC;gBACD,MAAM;YAER,KAAK,KAAK;gBACR,IAAI,aAAa,KAAK,cAAc,EAAE,CAAC;oBACrC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,mBAAmB;gBAC9C,CAAC;qBAAM,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;oBAC7B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;gBAC1B,CAAC;gBACD,MAAM;YAER,KAAK,UAAU;gBACb,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;gBAC/C,IAAI,aAAa,IAAI,QAAQ,EAAE,CAAC;oBAC9B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,mBAAmB;gBAC9C,CAAC;qBAAM,IAAI,aAAa,IAAI,QAAQ,EAAE,CAAC;oBACrC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;gBAC1B,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,KAAgC,EAAE,OAAe,EAAE,IAAU;QAC5E,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,EAAE,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC;YACjC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK;YACL,OAAO;YACP,IAAI;SACL,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,UAAkB;QAC5B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAChC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACrD,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,2CAA2C;QAC3C,IAAI,IAAI,CAAC,aAAa,EAAE,kBAAkB,EAAE,QAAQ,EAAE,CAAC;YACrD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC5E,MAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,IAAI,IAAI,CAAC,aAAa,EAAE,UAAU,EAAE,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YACvF,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;gBAC/D,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,SAAS,CAAC,EAAE,CAAC;oBACjD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,yBAAyB,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC;gBAC/E,CAAC;YACH,CAAC;QACH,CAAC;QAED,8BAA8B;QAC9B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACzD,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,OAAO,IAAI,CAAC;QAExC,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC;QACpD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,SAAS,IAAI,EAAE,CAAC;QAElD,IAAI,CAAC,cAAc;YAAE,OAAO,KAAK,CAAC;QAElC,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;QAC9E,MAAM,cAAc,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC;QAEvD,QAAQ,cAAc,CAAC,YAAY,EAAE,CAAC;YACpC,KAAK,KAAK;gBACR,OAAO,aAAa,GAAG,CAAC,CAAC;YAC3B,KAAK,KAAK;gBACR,OAAO,aAAa,KAAK,cAAc,CAAC;YAC1C,KAAK,UAAU;gBACb,OAAO,aAAa,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;YACxD;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,SAAc;QAChD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAElD,QAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC3B,KAAK,QAAQ;gBACX,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;YAC/C,KAAK,WAAW;gBACd,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,EAAE,CAAC;YAC/D,KAAK,QAAQ;gBACX,OAAO,KAAK,KAAK,SAAS,CAAC,KAAK,CAAC;YACnC,KAAK,cAAc;gBACjB,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACjD,KAAK,WAAW;gBACd,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACjD;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,KAAa;QACjC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,KAAK,GAAQ,IAAI,CAAC;QAEtB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,KAAK,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AA1iBY,oCAAY;AAEvB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;wCACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;2CACV;AAMd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;iDACL;AASpB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,EAAE,aAAa,EAAE,eAAe,CAAC;KAC3F,CAAC;;0CAC4F;AAU9F;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC;QAC/E,OAAO,EAAE,SAAS;KACnB,CAAC;;4CACmF;AAUrF;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;QACzC,OAAO,EAAE,QAAQ;KAClB,CAAC;;8CAC6C;AAM/C;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAC5B;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;+CAC1C;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDA4CxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CAuCxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDAC1C;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAC5B;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;6CAAC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAC7C;AAM3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2CAC1B;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;0CACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;+CAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;+CAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;+CAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,0BAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;IAC9E,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;kDAC1B,0BAAQ,oBAAR,0BAAQ;8CAAC;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;gDAC3B;uBAlNR,YAAY;IANxB,IAAA,gBAAM,EAAC,gBAAgB,CAAC;IACxB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,SAAS,CAAC,CAAC;GACN,YAAY,CA0iBxB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\incident-response\\domain\\entities\\incident-task.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { Incident } from './incident.entity';\r\n\r\n/**\r\n * Incident Task entity\r\n * Represents individual tasks within incident response workflows\r\n */\r\n@Entity('incident_tasks')\r\n@Index(['incidentId'])\r\n@Index(['status'])\r\n@Index(['priority'])\r\n@Index(['assignedTo'])\r\n@Index(['dueDate'])\r\nexport class IncidentTask {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Task title\r\n   */\r\n  @Column({ length: 255 })\r\n  title: string;\r\n\r\n  /**\r\n   * Task description\r\n   */\r\n  @Column({ type: 'text' })\r\n  description: string;\r\n\r\n  /**\r\n   * Task type\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['manual', 'automated', 'approval', 'investigation', 'containment', 'communication'],\r\n  })\r\n  type: 'manual' | 'automated' | 'approval' | 'investigation' | 'containment' | 'communication';\r\n\r\n  /**\r\n   * Task status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['pending', 'in_progress', 'completed', 'failed', 'cancelled', 'blocked'],\r\n    default: 'pending',\r\n  })\r\n  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled' | 'blocked';\r\n\r\n  /**\r\n   * Task priority\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high', 'urgent'],\r\n    default: 'medium',\r\n  })\r\n  priority: 'low' | 'medium' | 'high' | 'urgent';\r\n\r\n  /**\r\n   * Response phase this task belongs to\r\n   */\r\n  @Column({ name: 'response_phase', nullable: true })\r\n  responsePhase?: string;\r\n\r\n  /**\r\n   * Task order within phase\r\n   */\r\n  @Column({ name: 'task_order', type: 'integer', default: 0 })\r\n  taskOrder: number;\r\n\r\n  /**\r\n   * Task configuration and automation\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  configuration?: {\r\n    // Automation settings\r\n    automation?: {\r\n      type: 'script' | 'api_call' | 'notification' | 'data_collection' | 'workflow';\r\n      script?: string;\r\n      apiEndpoint?: string;\r\n      parameters?: Record<string, any>;\r\n      timeout?: number; // seconds\r\n      retryCount?: number;\r\n      retryDelay?: number; // seconds\r\n    };\r\n    \r\n    // Approval settings\r\n    approval?: {\r\n      required: boolean;\r\n      approvers: string[];\r\n      approvalType: 'any' | 'all' | 'majority';\r\n      escalationTime?: number; // minutes\r\n      escalationTo?: string[];\r\n    };\r\n    \r\n    // Evidence collection\r\n    evidenceCollection?: {\r\n      required: boolean;\r\n      types: string[];\r\n      templates?: string[];\r\n      chainOfCustody: boolean;\r\n    };\r\n    \r\n    // Dependencies\r\n    dependencies?: string[]; // task IDs\r\n    prerequisites?: string[]; // conditions that must be met\r\n    \r\n    // Validation rules\r\n    validation?: {\r\n      required: boolean;\r\n      criteria: Array<{\r\n        field: string;\r\n        operator: string;\r\n        value: any;\r\n        message: string;\r\n      }>;\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Task execution details\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  execution?: {\r\n    // Execution tracking\r\n    startedAt?: string;\r\n    completedAt?: string;\r\n    duration?: number; // seconds\r\n    attempts?: number;\r\n    \r\n    // Results\r\n    result?: 'success' | 'failure' | 'partial';\r\n    output?: any;\r\n    errorMessage?: string;\r\n    \r\n    // Automation execution\r\n    automationLogs?: Array<{\r\n      timestamp: string;\r\n      level: 'info' | 'warn' | 'error';\r\n      message: string;\r\n      data?: any;\r\n    }>;\r\n    \r\n    // Approval tracking\r\n    approvals?: Array<{\r\n      approverId: string;\r\n      decision: 'approved' | 'rejected' | 'pending';\r\n      timestamp: string;\r\n      comments?: string;\r\n    }>;\r\n    \r\n    // Evidence collected\r\n    evidenceIds?: string[];\r\n    \r\n    // Quality metrics\r\n    quality?: {\r\n      completeness: number; // 0-1\r\n      accuracy: number; // 0-1\r\n      timeliness: number; // 0-1\r\n      issues?: string[];\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Task assignment\r\n   */\r\n  @Column({ name: 'assigned_to', type: 'uuid', nullable: true })\r\n  assignedTo?: string;\r\n\r\n  /**\r\n   * Task assignment role (if not assigned to specific user)\r\n   */\r\n  @Column({ name: 'assigned_role', nullable: true })\r\n  assignedRole?: string;\r\n\r\n  /**\r\n   * Task due date\r\n   */\r\n  @Column({ name: 'due_date', type: 'timestamp with time zone', nullable: true })\r\n  dueDate?: Date;\r\n\r\n  /**\r\n   * Estimated duration in minutes\r\n   */\r\n  @Column({ name: 'estimated_duration', type: 'integer', nullable: true })\r\n  estimatedDuration?: number;\r\n\r\n  /**\r\n   * Task notes and comments\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  notes?: string;\r\n\r\n  /**\r\n   * Task tags\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * User who created the task\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  /**\r\n   * User who last updated the task\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => Incident, incident => incident.tasks, { onDelete: 'CASCADE' })\r\n  @JoinColumn({ name: 'incident_id' })\r\n  incident: Incident;\r\n\r\n  @Column({ name: 'incident_id', type: 'uuid' })\r\n  incidentId: string;\r\n\r\n  /**\r\n   * Check if task is overdue\r\n   */\r\n  get isOverdue(): boolean {\r\n    if (!this.dueDate || this.status === 'completed') return false;\r\n    return new Date() > this.dueDate;\r\n  }\r\n\r\n  /**\r\n   * Check if task is blocked\r\n   */\r\n  get isBlocked(): boolean {\r\n    return this.status === 'blocked';\r\n  }\r\n\r\n  /**\r\n   * Check if task requires approval\r\n   */\r\n  get requiresApproval(): boolean {\r\n    return this.configuration?.approval?.required || false;\r\n  }\r\n\r\n  /**\r\n   * Check if task is automated\r\n   */\r\n  get isAutomated(): boolean {\r\n    return this.type === 'automated' && !!this.configuration?.automation;\r\n  }\r\n\r\n  /**\r\n   * Get task progress percentage\r\n   */\r\n  get progressPercentage(): number {\r\n    switch (this.status) {\r\n      case 'pending':\r\n        return 0;\r\n      case 'in_progress':\r\n        return 50;\r\n      case 'completed':\r\n        return 100;\r\n      case 'failed':\r\n      case 'cancelled':\r\n      case 'blocked':\r\n        return 0;\r\n      default:\r\n        return 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Start task execution\r\n   */\r\n  start(userId: string): void {\r\n    this.status = 'in_progress';\r\n    this.assignedTo = userId;\r\n    this.updatedBy = userId;\r\n    \r\n    if (!this.execution) {\r\n      this.execution = {};\r\n    }\r\n    \r\n    this.execution.startedAt = new Date().toISOString();\r\n    this.execution.attempts = (this.execution.attempts || 0) + 1;\r\n  }\r\n\r\n  /**\r\n   * Complete task\r\n   */\r\n  complete(userId: string, result?: any, quality?: any): void {\r\n    this.status = 'completed';\r\n    this.updatedBy = userId;\r\n    \r\n    if (!this.execution) {\r\n      this.execution = {};\r\n    }\r\n    \r\n    this.execution.completedAt = new Date().toISOString();\r\n    this.execution.result = 'success';\r\n    \r\n    if (result) {\r\n      this.execution.output = result;\r\n    }\r\n    \r\n    if (quality) {\r\n      this.execution.quality = quality;\r\n    }\r\n    \r\n    // Calculate duration\r\n    if (this.execution.startedAt) {\r\n      const startTime = new Date(this.execution.startedAt);\r\n      const endTime = new Date(this.execution.completedAt);\r\n      this.execution.duration = Math.round((endTime.getTime() - startTime.getTime()) / 1000);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fail task\r\n   */\r\n  fail(userId: string, errorMessage: string): void {\r\n    this.status = 'failed';\r\n    this.updatedBy = userId;\r\n    \r\n    if (!this.execution) {\r\n      this.execution = {};\r\n    }\r\n    \r\n    this.execution.completedAt = new Date().toISOString();\r\n    this.execution.result = 'failure';\r\n    this.execution.errorMessage = errorMessage;\r\n    \r\n    // Calculate duration\r\n    if (this.execution.startedAt) {\r\n      const startTime = new Date(this.execution.startedAt);\r\n      const endTime = new Date(this.execution.completedAt);\r\n      this.execution.duration = Math.round((endTime.getTime() - startTime.getTime()) / 1000);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Block task\r\n   */\r\n  block(userId: string, reason: string): void {\r\n    this.status = 'blocked';\r\n    this.updatedBy = userId;\r\n    this.notes = `${this.notes || ''}\\nBlocked: ${reason}`.trim();\r\n  }\r\n\r\n  /**\r\n   * Cancel task\r\n   */\r\n  cancel(userId: string, reason?: string): void {\r\n    this.status = 'cancelled';\r\n    this.updatedBy = userId;\r\n    \r\n    if (reason) {\r\n      this.notes = `${this.notes || ''}\\nCancelled: ${reason}`.trim();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add approval\r\n   */\r\n  addApproval(approverId: string, decision: 'approved' | 'rejected', comments?: string): void {\r\n    if (!this.execution) {\r\n      this.execution = {};\r\n    }\r\n    \r\n    if (!this.execution.approvals) {\r\n      this.execution.approvals = [];\r\n    }\r\n    \r\n    // Remove existing approval from this user\r\n    this.execution.approvals = this.execution.approvals.filter(a => a.approverId !== approverId);\r\n    \r\n    // Add new approval\r\n    this.execution.approvals.push({\r\n      approverId,\r\n      decision,\r\n      timestamp: new Date().toISOString(),\r\n      comments,\r\n    });\r\n    \r\n    // Check if all required approvals are received\r\n    this.checkApprovalStatus();\r\n  }\r\n\r\n  /**\r\n   * Check approval status and update task accordingly\r\n   */\r\n  private checkApprovalStatus(): void {\r\n    if (!this.requiresApproval || !this.execution?.approvals) return;\r\n    \r\n    const approvalConfig = this.configuration?.approval;\r\n    if (!approvalConfig) return;\r\n    \r\n    const approvals = this.execution.approvals;\r\n    const approvedCount = approvals.filter(a => a.decision === 'approved').length;\r\n    const rejectedCount = approvals.filter(a => a.decision === 'rejected').length;\r\n    const totalApprovers = approvalConfig.approvers.length;\r\n    \r\n    switch (approvalConfig.approvalType) {\r\n      case 'any':\r\n        if (approvedCount > 0) {\r\n          this.status = 'pending'; // Ready to proceed\r\n        } else if (rejectedCount > 0) {\r\n          this.status = 'blocked';\r\n        }\r\n        break;\r\n        \r\n      case 'all':\r\n        if (approvedCount === totalApprovers) {\r\n          this.status = 'pending'; // Ready to proceed\r\n        } else if (rejectedCount > 0) {\r\n          this.status = 'blocked';\r\n        }\r\n        break;\r\n        \r\n      case 'majority':\r\n        const majority = Math.ceil(totalApprovers / 2);\r\n        if (approvedCount >= majority) {\r\n          this.status = 'pending'; // Ready to proceed\r\n        } else if (rejectedCount >= majority) {\r\n          this.status = 'blocked';\r\n        }\r\n        break;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add automation log\r\n   */\r\n  addAutomationLog(level: 'info' | 'warn' | 'error', message: string, data?: any): void {\r\n    if (!this.execution) {\r\n      this.execution = {};\r\n    }\r\n    \r\n    if (!this.execution.automationLogs) {\r\n      this.execution.automationLogs = [];\r\n    }\r\n    \r\n    this.execution.automationLogs.push({\r\n      timestamp: new Date().toISOString(),\r\n      level,\r\n      message,\r\n      data,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Add evidence reference\r\n   */\r\n  addEvidence(evidenceId: string): void {\r\n    if (!this.execution) {\r\n      this.execution = {};\r\n    }\r\n    \r\n    if (!this.execution.evidenceIds) {\r\n      this.execution.evidenceIds = [];\r\n    }\r\n    \r\n    if (!this.execution.evidenceIds.includes(evidenceId)) {\r\n      this.execution.evidenceIds.push(evidenceId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate task completion\r\n   */\r\n  validateCompletion(): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n    \r\n    // Check if evidence collection is required\r\n    if (this.configuration?.evidenceCollection?.required) {\r\n      if (!this.execution?.evidenceIds || this.execution.evidenceIds.length === 0) {\r\n        errors.push('Evidence collection is required but no evidence was collected');\r\n      }\r\n    }\r\n    \r\n    // Check validation criteria\r\n    if (this.configuration?.validation?.required && this.configuration.validation.criteria) {\r\n      for (const criterion of this.configuration.validation.criteria) {\r\n        if (!this.evaluateValidationCriterion(criterion)) {\r\n          errors.push(criterion.message || `Validation failed for ${criterion.field}`);\r\n        }\r\n      }\r\n    }\r\n    \r\n    // Check approval requirements\r\n    if (this.requiresApproval) {\r\n      const hasRequiredApprovals = this.hasRequiredApprovals();\r\n      if (!hasRequiredApprovals) {\r\n        errors.push('Required approvals not received');\r\n      }\r\n    }\r\n    \r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check if task has required approvals\r\n   */\r\n  private hasRequiredApprovals(): boolean {\r\n    if (!this.requiresApproval) return true;\r\n    \r\n    const approvalConfig = this.configuration?.approval;\r\n    const approvals = this.execution?.approvals || [];\r\n    \r\n    if (!approvalConfig) return false;\r\n    \r\n    const approvedCount = approvals.filter(a => a.decision === 'approved').length;\r\n    const totalApprovers = approvalConfig.approvers.length;\r\n    \r\n    switch (approvalConfig.approvalType) {\r\n      case 'any':\r\n        return approvedCount > 0;\r\n      case 'all':\r\n        return approvedCount === totalApprovers;\r\n      case 'majority':\r\n        return approvedCount >= Math.ceil(totalApprovers / 2);\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Evaluate validation criterion\r\n   */\r\n  private evaluateValidationCriterion(criterion: any): boolean {\r\n    const value = this.getFieldValue(criterion.field);\r\n    \r\n    switch (criterion.operator) {\r\n      case 'exists':\r\n        return value !== null && value !== undefined;\r\n      case 'not_empty':\r\n        return value !== null && value !== undefined && value !== '';\r\n      case 'equals':\r\n        return value === criterion.value;\r\n      case 'greater_than':\r\n        return Number(value) > Number(criterion.value);\r\n      case 'less_than':\r\n        return Number(value) < Number(criterion.value);\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get field value for validation\r\n   */\r\n  private getFieldValue(field: string): any {\r\n    const parts = field.split('.');\r\n    let value: any = this;\r\n    \r\n    for (const part of parts) {\r\n      value = value?.[part];\r\n    }\r\n    \r\n    return value;\r\n  }\r\n}\r\n"], "version": 3}