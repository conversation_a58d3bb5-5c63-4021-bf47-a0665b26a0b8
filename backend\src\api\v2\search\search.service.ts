import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Like, ILike } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { ConfigService } from '@nestjs/config';

import { SecurityEvent } from '../../../modules/event-processing/domain/entities/security-event.entity';
import { Vulnerability } from '../../../modules/vulnerability-management/domain/entities/vulnerability.entity';
import { Asset } from '../../../modules/asset-management/domain/entities/asset.entity';
import { ThreatIndicator } from '../../../modules/threat-intelligence/domain/entities/threat-indicator.entity';
import { Incident } from '../../../modules/incident-response/domain/entities/incident.entity';
import { User } from '../../../modules/user-management/domain/entities/user.entity';
import { DistributedCacheService } from '../../../infrastructure/cache/distributed-cache.service';
import { LoggerService } from '../../../infrastructure/logging/logger.service';
import { SearchScope, SearchOperator, SortDirection } from './search.controller';

/**
 * Search result interface
 */
export interface SearchResult {
  id: string;
  type: string;
  module: string;
  title: string;
  description: string;
  score: number;
  highlights?: Record<string, string[]>;
  metadata: Record<string, any>;
  data: any;
}

/**
 * Search facet interface
 */
export interface SearchFacet {
  field: string;
  values: Array<{
    value: string;
    count: number;
    selected?: boolean;
  }>;
}

/**
 * Search response interface
 */
export interface SearchResponse {
  results: SearchResult[];
  facets?: Record<string, SearchFacet>;
  aggregations?: Record<string, any>;
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  suggestions?: string[];
}

/**
 * Advanced search and filtering service
 * Provides comprehensive search capabilities with full-text search, faceting, and aggregations
 */
@Injectable()
export class SearchService {
  private readonly logger = new Logger(SearchService.name);

  constructor(
    @InjectRepository(SecurityEvent)
    private readonly eventRepository: Repository<SecurityEvent>,
    @InjectRepository(Vulnerability)
    private readonly vulnerabilityRepository: Repository<Vulnerability>,
    @InjectRepository(Asset)
    private readonly assetRepository: Repository<Asset>,
    @InjectRepository(ThreatIndicator)
    private readonly threatRepository: Repository<ThreatIndicator>,
    @InjectRepository(Incident)
    private readonly incidentRepository: Repository<Incident>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectQueue('search-processing')
    private readonly searchQueue: Queue,
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService,
    private readonly cacheService: DistributedCacheService,
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * Global search across all modules
   */
  async globalSearch(options: {
    query: string;
    scope: SearchScope;
    limit: number;
    offset: number;
    includeHighlights: boolean;
    includeFacets: boolean;
    userId: string;
    userRoles: string[];
  }): Promise<SearchResponse> {
    const cacheKey = `global_search_${JSON.stringify(options)}`;
    
    try {
      // Check cache first
      const cached = await this.cacheService.get<SearchResponse>(cacheKey);
      if (cached) {
        return cached;
      }

      const results: SearchResult[] = [];
      let totalCount = 0;

      // Search across different modules based on scope
      if (options.scope === SearchScope.ALL || options.scope === SearchScope.EVENTS) {
        const eventResults = await this.searchSecurityEvents(options.query, options.limit, options.offset);
        results.push(...eventResults.results);
        totalCount += eventResults.total;
      }

      if (options.scope === SearchScope.ALL || options.scope === SearchScope.VULNERABILITIES) {
        const vulnResults = await this.searchVulnerabilities(options.query, options.limit, options.offset);
        results.push(...vulnResults.results);
        totalCount += vulnResults.total;
      }

      if (options.scope === SearchScope.ALL || options.scope === SearchScope.ASSETS) {
        const assetResults = await this.searchAssets(options.query, options.limit, options.offset);
        results.push(...assetResults.results);
        totalCount += assetResults.total;
      }

      if (options.scope === SearchScope.ALL || options.scope === SearchScope.THREATS) {
        const threatResults = await this.searchThreats(options.query, options.limit, options.offset);
        results.push(...threatResults.results);
        totalCount += threatResults.total;
      }

      if (options.scope === SearchScope.ALL || options.scope === SearchScope.INCIDENTS) {
        const incidentResults = await this.searchIncidents(options.query, options.limit, options.offset);
        results.push(...incidentResults.results);
        totalCount += incidentResults.total;
      }

      // Sort results by relevance score
      results.sort((a, b) => b.score - a.score);

      // Apply pagination to combined results
      const paginatedResults = results.slice(options.offset, options.offset + options.limit);

      // Generate facets if requested
      let facets: Record<string, SearchFacet> | undefined;
      if (options.includeFacets) {
        facets = await this.generateFacets(results);
      }

      // Generate suggestions
      const suggestions = await this.generateSuggestions(options.query, options.scope);

      const response: SearchResponse = {
        results: paginatedResults,
        facets,
        pagination: {
          total: totalCount,
          limit: options.limit,
          offset: options.offset,
          hasMore: options.offset + options.limit < totalCount,
        },
        suggestions,
      };

      // Cache for 5 minutes
      await this.cacheService.set(cacheKey, response, { ttl: 300 });

      // Log search query
      await this.logSearchQuery(options.query, options.scope, options.userId, totalCount);

      return response;

    } catch (error) {
      this.loggerService.error('Global search failed', {
        options,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Advanced search with complex filters
   */
  async advancedSearch(
    request: any,
    context: { userId: string; userRoles: string[] },
  ): Promise<SearchResponse> {
    try {
      this.logger.log('Executing advanced search', {
        userId: context.userId,
        queryText: request.query.text,
        scope: request.scope,
      });

      // Build search query based on request
      const searchQuery = this.buildAdvancedSearchQuery(request);
      
      // Execute search across specified scope
      const results = await this.executeAdvancedSearch(searchQuery, request.scope || SearchScope.ALL);

      // Apply filters
      const filteredResults = await this.applyFilters(results, request.filters || []);

      // Apply sorting
      const sortedResults = await this.applySorting(filteredResults, request.sort || []);

      // Generate facets
      const facets = await this.generateAdvancedFacets(sortedResults, request.facets || []);

      // Generate aggregations
      const aggregations = await this.generateAggregations(sortedResults, request.aggregations || []);

      // Apply pagination
      const pagination = request.pagination || { limit: 20, offset: 0 };
      const paginatedResults = sortedResults.slice(pagination.offset, pagination.offset + pagination.limit);

      return {
        results: paginatedResults,
        facets,
        aggregations,
        pagination: {
          total: sortedResults.length,
          limit: pagination.limit,
          offset: pagination.offset,
          hasMore: pagination.offset + pagination.limit < sortedResults.length,
        },
      };

    } catch (error) {
      this.loggerService.error('Advanced search failed', {
        request,
        context,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Full-text search with fuzzy matching
   */
  async fullTextSearch(options: {
    query: string;
    fuzziness: number;
    boost?: string;
    analyzer?: string;
    scope: SearchScope;
    userId: string;
  }): Promise<SearchResponse> {
    try {
      // Implement fuzzy search logic
      const fuzzyQuery = this.buildFuzzyQuery(options.query, options.fuzziness);
      
      // Apply field boosting if specified
      const boostedQuery = options.boost ? this.applyFieldBoost(fuzzyQuery, options.boost) : fuzzyQuery;

      // Execute search with fuzzy matching
      const results = await this.executeFuzzySearch(boostedQuery, options.scope);

      return {
        results,
        pagination: {
          total: results.length,
          limit: 20,
          offset: 0,
          hasMore: false,
        },
      };

    } catch (error) {
      this.loggerService.error('Full-text search failed', {
        options,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Faceted search with drill-down capabilities
   */
  async facetedSearch(options: {
    query?: string;
    facets: string[];
    filters: Record<string, any>;
    scope: SearchScope;
    userId: string;
  }): Promise<SearchResponse> {
    try {
      // Execute base search
      let results: SearchResult[] = [];
      
      if (options.query) {
        const searchResponse = await this.globalSearch({
          query: options.query,
          scope: options.scope,
          limit: 1000, // Get more results for faceting
          offset: 0,
          includeHighlights: false,
          includeFacets: false,
          userId: options.userId,
          userRoles: [],
        });
        results = searchResponse.results;
      } else {
        // Get all results for faceting
        results = await this.getAllResults(options.scope);
      }

      // Apply existing filters
      const filteredResults = await this.applyFacetFilters(results, options.filters);

      // Generate facets
      const facets = await this.generateFacetsForFields(filteredResults, options.facets);

      return {
        results: filteredResults.slice(0, 20), // Limit display results
        facets,
        pagination: {
          total: filteredResults.length,
          limit: 20,
          offset: 0,
          hasMore: filteredResults.length > 20,
        },
      };

    } catch (error) {
      this.loggerService.error('Faceted search failed', {
        options,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Generate search ID
   */
  generateSearchId(): string {
    return `search_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  // Private helper methods for module-specific searches

  private async searchSecurityEvents(query: string, limit: number, offset: number): Promise<{ results: SearchResult[]; total: number }> {
    const [events, total] = await this.eventRepository.findAndCount({
      where: [
        { description: ILike(`%${query}%`) },
        { sourceIp: ILike(`%${query}%`) },
        { sourceHostname: ILike(`%${query}%`) },
      ],
      take: limit,
      skip: offset,
      order: { timestamp: 'DESC' },
    });

    const results: SearchResult[] = events.map(event => ({
      id: event.id,
      type: 'security-event',
      module: 'event-processing',
      title: `${event.eventType} - ${event.sourceIp || event.sourceHostname}`,
      description: event.description,
      score: this.calculateRelevanceScore(query, event.description),
      metadata: {
        severity: event.severity,
        timestamp: event.timestamp,
        riskScore: event.riskScore,
      },
      data: event,
    }));

    return { results, total };
  }

  private async searchVulnerabilities(query: string, limit: number, offset: number): Promise<{ results: SearchResult[]; total: number }> {
    const [vulnerabilities, total] = await this.vulnerabilityRepository.findAndCount({
      where: [
        { title: ILike(`%${query}%`) },
        { description: ILike(`%${query}%`) },
        { identifier: ILike(`%${query}%`) },
      ],
      take: limit,
      skip: offset,
      order: { discoveredDate: 'DESC' },
    });

    const results: SearchResult[] = vulnerabilities.map(vuln => ({
      id: vuln.id,
      type: 'vulnerability',
      module: 'vulnerability-management',
      title: vuln.title,
      description: vuln.description,
      score: this.calculateRelevanceScore(query, vuln.title + ' ' + vuln.description),
      metadata: {
        severity: vuln.severity,
        cvssScore: vuln.cvssScore,
        identifier: vuln.identifier,
      },
      data: vuln,
    }));

    return { results, total };
  }

  private async searchAssets(query: string, limit: number, offset: number): Promise<{ results: SearchResult[]; total: number }> {
    const [assets, total] = await this.assetRepository.findAndCount({
      where: [
        { name: ILike(`%${query}%`) },
        { description: ILike(`%${query}%`) },
        { ipAddress: ILike(`%${query}%`) },
        { hostname: ILike(`%${query}%`) },
      ],
      take: limit,
      skip: offset,
      order: { lastSeen: 'DESC' },
    });

    const results: SearchResult[] = assets.map(asset => ({
      id: asset.id,
      type: 'asset',
      module: 'asset-management',
      title: asset.name,
      description: asset.description || `${asset.type} asset`,
      score: this.calculateRelevanceScore(query, asset.name + ' ' + (asset.description || '')),
      metadata: {
        type: asset.type,
        criticality: asset.criticality,
        status: asset.status,
        ipAddress: asset.ipAddress,
        hostname: asset.hostname,
      },
      data: asset,
    }));

    return { results, total };
  }

  private async searchThreats(query: string, limit: number, offset: number): Promise<{ results: SearchResult[]; total: number }> {
    const [threats, total] = await this.threatRepository.findAndCount({
      where: [
        { value: ILike(`%${query}%`) },
        { threatType: ILike(`%${query}%`) },
        { source: ILike(`%${query}%`) },
      ],
      take: limit,
      skip: offset,
      order: { lastSeen: 'DESC' },
    });

    const results: SearchResult[] = threats.map(threat => ({
      id: threat.id,
      type: 'threat-indicator',
      module: 'threat-intelligence',
      title: `${threat.type}: ${threat.value}`,
      description: `${threat.threatType} threat indicator`,
      score: this.calculateRelevanceScore(query, threat.value),
      metadata: {
        type: threat.type,
        threatType: threat.threatType,
        confidence: threat.confidence,
        severity: threat.severity,
        source: threat.source,
      },
      data: threat,
    }));

    return { results, total };
  }

  private async searchIncidents(query: string, limit: number, offset: number): Promise<{ results: SearchResult[]; total: number }> {
    const [incidents, total] = await this.incidentRepository.findAndCount({
      where: [
        { title: ILike(`%${query}%`) },
        { description: ILike(`%${query}%`) },
        { category: ILike(`%${query}%`) },
      ],
      take: limit,
      skip: offset,
      order: { createdAt: 'DESC' },
    });

    const results: SearchResult[] = incidents.map(incident => ({
      id: incident.id,
      type: 'incident',
      module: 'incident-response',
      title: incident.title,
      description: incident.description,
      score: this.calculateRelevanceScore(query, incident.title + ' ' + incident.description),
      metadata: {
        severity: incident.severity,
        status: incident.status,
        category: incident.category,
        assignedTo: incident.assignedTo,
      },
      data: incident,
    }));

    return { results, total };
  }

  private calculateRelevanceScore(query: string, text: string): number {
    if (!text) return 0;
    
    const queryLower = query.toLowerCase();
    const textLower = text.toLowerCase();
    
    // Simple relevance scoring
    let score = 0;
    
    // Exact match bonus
    if (textLower.includes(queryLower)) {
      score += 10;
    }
    
    // Word match scoring
    const queryWords = queryLower.split(' ');
    const textWords = textLower.split(' ');
    
    for (const queryWord of queryWords) {
      for (const textWord of textWords) {
        if (textWord.includes(queryWord)) {
          score += queryWord.length / textWord.length;
        }
      }
    }
    
    return Math.min(score, 10); // Cap at 10
  }

  // Placeholder methods for advanced functionality
  private async generateFacets(results: SearchResult[]): Promise<Record<string, SearchFacet>> {
    const facets: Record<string, SearchFacet> = {};
    
    // Generate module facet
    const moduleCounts: Record<string, number> = {};
    results.forEach(result => {
      moduleCounts[result.module] = (moduleCounts[result.module] || 0) + 1;
    });
    
    facets.modules = {
      field: 'module',
      values: Object.entries(moduleCounts).map(([value, count]) => ({ value, count })),
    };
    
    return facets;
  }

  private async generateSuggestions(query: string, scope: SearchScope): Promise<string[]> {
    // Simple suggestion generation
    const suggestions = [
      `${query} vulnerability`,
      `${query} threat`,
      `${query} incident`,
      `${query} asset`,
    ];
    
    return suggestions.slice(0, 5);
  }

  private async logSearchQuery(query: string, scope: SearchScope, userId: string, resultCount: number): Promise<void> {
    this.logger.log('Search query executed', {
      query,
      scope,
      userId,
      resultCount,
      timestamp: new Date(),
    });
  }

  // Additional placeholder methods
  private buildAdvancedSearchQuery(request: any): any { return {}; }
  private async executeAdvancedSearch(query: any, scope: SearchScope): Promise<SearchResult[]> { return []; }
  private async applyFilters(results: SearchResult[], filters: any[]): Promise<SearchResult[]> { return results; }
  private async applySorting(results: SearchResult[], sort: any[]): Promise<SearchResult[]> { return results; }
  private async generateAdvancedFacets(results: SearchResult[], facets: any[]): Promise<Record<string, SearchFacet>> { return {}; }
  private async generateAggregations(results: SearchResult[], aggregations: any[]): Promise<Record<string, any>> { return {}; }
  private buildFuzzyQuery(query: string, fuzziness: number): any { return {}; }
  private applyFieldBoost(query: any, boost: string): any { return query; }
  private async executeFuzzySearch(query: any, scope: SearchScope): Promise<SearchResult[]> { return []; }
  private async getAllResults(scope: SearchScope): Promise<SearchResult[]> { return []; }
  private async applyFacetFilters(results: SearchResult[], filters: Record<string, any>): Promise<SearchResult[]> { return results; }
  private async generateFacetsForFields(results: SearchResult[], fields: string[]): Promise<Record<string, SearchFacet>> { return {}; }
  private async generateTermSuggestions(query: string, scope: SearchScope, size: number): Promise<any[]> { return []; }
  private async generateAutoCompletions(query: string, scope: SearchScope, size: number): Promise<string[]> { return []; }
  private async storeSavedSearch(search: any): Promise<void> {}
  private async getUserSavedSearches(userId: string): Promise<any[]> { return []; }
  private async getPublicSavedSearches(): Promise<any[]> { return []; }
  private async getTotalSearchCount(timeRange: string): Promise<number> { return 0; }
  private async getPopularQueries(timeRange: string): Promise<any[]> { return []; }
  private async getSearchTrends(timeRange: string): Promise<any> { return {}; }
  private async getUserSearchStats(userId: string, timeRange: string): Promise<any> { return {}; }
}
