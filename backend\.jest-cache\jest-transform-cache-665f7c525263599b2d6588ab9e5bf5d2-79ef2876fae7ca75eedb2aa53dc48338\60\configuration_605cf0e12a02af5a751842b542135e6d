73783437d1bd6f3b0c106af6f4edba58
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SentinelConfigurationService = exports.createSentinelConfiguration = exports.createSecurityConfig = exports.createRedisConfig = exports.createDatabaseConfig = exports.createApplicationConfig = exports.createEnvironmentConfig = void 0;
const config_1 = require("@nestjs/config");
const config_2 = require("@nestjs/config");
const common_1 = require("@nestjs/common");
/**
 * Environment-specific configuration factory
 */
const createEnvironmentConfig = () => {
    const env = (process.env['NODE_ENV'] || 'development');
    return {
        environment: env,
        isDevelopment: env === 'development',
        isProduction: env === 'production',
        isTest: env === 'test',
        isStaging: env === 'staging',
        debug: env === 'development' || env === 'test',
        hotReload: env === 'development',
    };
};
exports.createEnvironmentConfig = createEnvironmentConfig;
/**
 * Application configuration factory
 */
const createApplicationConfig = () => ({
    name: process.env['APP_NAME'] || 'Sentinel Backend',
    version: process.env['APP_VERSION'] || '1.0.0',
    description: process.env['APP_DESCRIPTION'] || 'Sentinel Vulnerability Assessment Platform Backend',
    port: parseInt(process.env['PORT'] || '3000', 10),
    globalPrefix: process.env['API_PREFIX'] || 'api',
    corsOrigin: process.env['CORS_ORIGIN']?.split(',') || ['http://localhost:3001'],
    trustProxy: process.env['TRUST_PROXY'] === 'true',
    shutdownTimeout: parseInt(process.env['SHUTDOWN_TIMEOUT'] || '10000', 10),
});
exports.createApplicationConfig = createApplicationConfig;
/**
 * Database configuration factory
 */
const createDatabaseConfig = () => ({
    type: process.env['DATABASE_TYPE'] || 'postgres',
    host: process.env['DATABASE_HOST'] || 'localhost',
    port: parseInt(process.env['DATABASE_PORT'] || '5432', 10),
    username: process.env['DATABASE_USERNAME'] || 'sentinel',
    password: process.env['DATABASE_PASSWORD'] || 'password',
    database: process.env['DATABASE_NAME'] || 'sentinel',
    schema: process.env['DATABASE_SCHEMA'] || 'public',
    ssl: process.env['DATABASE_SSL'] === 'true',
    logging: process.env['DATABASE_LOGGING'] !== 'false',
    synchronize: process.env['DATABASE_SYNCHRONIZE'] === 'true',
    migrationsRun: process.env['DATABASE_MIGRATIONS_RUN'] !== 'false',
    maxConnections: parseInt(process.env['DATABASE_MAX_CONNECTIONS'] || '100', 10),
    connectionTimeout: parseInt(process.env['DATABASE_CONNECTION_TIMEOUT'] || '60000', 10),
    acquireTimeout: parseInt(process.env['DATABASE_ACQUIRE_TIMEOUT'] || '60000', 10),
    idleTimeout: parseInt(process.env['DATABASE_IDLE_TIMEOUT'] || '10000', 10),
    reapInterval: parseInt(process.env['DATABASE_REAP_INTERVAL'] || '1000', 10),
    createRetryDelay: parseInt(process.env['DATABASE_CREATE_RETRY_DELAY'] || '30000', 10),
    retryAttempts: parseInt(process.env['DATABASE_RETRY_ATTEMPTS'] || '3', 10),
});
exports.createDatabaseConfig = createDatabaseConfig;
/**
 * Redis configuration factory
 */
const createRedisConfig = () => ({
    host: process.env['REDIS_HOST'] || 'localhost',
    port: parseInt(process.env['REDIS_PORT'] || '6379', 10),
    password: process.env['REDIS_PASSWORD'] || undefined,
    db: parseInt(process.env['REDIS_DB'] || '0', 10),
    ttl: parseInt(process.env['REDIS_TTL'] || '3600', 10),
    maxRetries: parseInt(process.env['REDIS_MAX_RETRIES'] || '3', 10),
    retryDelay: parseInt(process.env['REDIS_RETRY_DELAY'] || '1000', 10),
    connectTimeout: parseInt(process.env['REDIS_CONNECT_TIMEOUT'] || '10000', 10),
    lazyConnect: process.env['REDIS_LAZY_CONNECT'] !== 'false',
    keepAlive: parseInt(process.env['REDIS_KEEP_ALIVE'] || '30000', 10),
    family: parseInt(process.env['REDIS_FAMILY'] || '4', 10),
});
exports.createRedisConfig = createRedisConfig;
/**
 * Security configuration factory
 */
const createSecurityConfig = () => ({
    jwtSecret: process.env['JWT_SECRET'] || 'your-super-secret-jwt-key-change-in-production',
    jwtExpiresIn: process.env['JWT_EXPIRES_IN'] || '1h',
    jwtRefreshSecret: process.env['JWT_REFRESH_SECRET'] || 'your-super-secret-refresh-key-change-in-production',
    jwtRefreshExpiresIn: process.env['JWT_REFRESH_EXPIRES_IN'] || '7d',
    bcryptRounds: parseInt(process.env['BCRYPT_ROUNDS'] || '12', 10),
    sessionSecret: process.env['SESSION_SECRET'] || 'your-super-secret-session-key-change-in-production',
    rateLimitTtl: parseInt(process.env['RATE_LIMIT_TTL'] || '60', 10),
    rateLimitLimit: parseInt(process.env['RATE_LIMIT_LIMIT'] || '100', 10),
    corsEnabled: process.env['CORS_ENABLED'] !== 'false',
    corsCredentials: process.env['CORS_CREDENTIALS'] !== 'false',
    hstsEnabled: process.env['SECURITY_HSTS_ENABLED'] === 'true',
    hstsMaxAge: parseInt(process.env['SECURITY_HSTS_MAX_AGE'] || '31536000', 10),
    cspEnabled: process.env['SECURITY_CSP_ENABLED'] === 'true',
    frameOptions: process.env['SECURITY_FRAME_OPTIONS'] || 'DENY',
    contentTypeOptions: process.env['SECURITY_CONTENT_TYPE_OPTIONS'] !== 'false',
});
exports.createSecurityConfig = createSecurityConfig;
/**
 * Complete configuration factory
 */
const createSentinelConfiguration = () => ({
    environment: (0, exports.createEnvironmentConfig)(),
    application: (0, exports.createApplicationConfig)(),
    database: (0, exports.createDatabaseConfig)(),
    redis: (0, exports.createRedisConfig)(),
    security: (0, exports.createSecurityConfig)(),
    logging: {
        level: process.env['LOG_LEVEL'] || 'info',
        format: process.env['LOG_FORMAT'] || 'json',
        fileEnabled: process.env['LOG_FILE_ENABLED'] === 'true',
        filePath: process.env['LOG_FILE_PATH'] || 'logs/app.log',
        fileMaxSize: process.env['LOG_FILE_MAX_SIZE'] || '10m',
        fileMaxFiles: parseInt(process.env['LOG_FILE_MAX_FILES'] || '5', 10),
        consoleEnabled: process.env['LOG_CONSOLE_ENABLED'] !== 'false',
        auditEnabled: process.env['LOG_AUDIT_ENABLED'] === 'true',
        auditPath: process.env['LOG_AUDIT_PATH'] || 'logs/audit.log',
        correlationIdEnabled: process.env['LOG_CORRELATION_ID_ENABLED'] !== 'false',
        structuredLogging: process.env['LOG_STRUCTURED'] !== 'false',
    },
    monitoring: {
        healthCheckEnabled: process.env['HEALTH_CHECK_ENABLED'] !== 'false',
        healthCheckDatabaseEnabled: process.env['HEALTH_CHECK_DATABASE_ENABLED'] !== 'false',
        healthCheckRedisEnabled: process.env['HEALTH_CHECK_REDIS_ENABLED'] !== 'false',
        memoryHeapThreshold: parseInt(process.env['HEALTH_CHECK_MEMORY_HEAP_THRESHOLD'] || '150', 10),
        memoryRssThreshold: parseInt(process.env['HEALTH_CHECK_MEMORY_RSS_THRESHOLD'] || '150', 10),
        metricsEnabled: process.env['METRICS_ENABLED'] === 'true',
        metricsPort: parseInt(process.env['METRICS_PORT'] || '9090', 10),
        metricsPath: process.env['METRICS_PATH'] || '/metrics',
        tracingEnabled: process.env['TRACING_ENABLED'] === 'true',
        tracingServiceName: process.env['TRACING_SERVICE_NAME'] || 'sentinel-backend',
        tracingJaegerEndpoint: process.env['TRACING_JAEGER_ENDPOINT'],
    },
    aiService: {
        enabled: process.env['AI_SERVICE_ENABLED'] === 'true',
        url: process.env['AI_SERVICE_URL'],
        apiKey: process.env['AI_SERVICE_API_KEY'],
        timeout: parseInt(process.env['AI_SERVICE_TIMEOUT'] || '30000', 10),
        retryAttempts: parseInt(process.env['AI_SERVICE_RETRY_ATTEMPTS'] || '3', 10),
        retryDelay: parseInt(process.env['AI_SERVICE_RETRY_DELAY'] || '1000', 10),
        circuitBreakerEnabled: process.env['AI_SERVICE_CIRCUIT_BREAKER_ENABLED'] === 'true',
        circuitBreakerThreshold: parseInt(process.env['AI_SERVICE_CIRCUIT_BREAKER_THRESHOLD'] || '5', 10),
        circuitBreakerTimeout: parseInt(process.env['AI_SERVICE_CIRCUIT_BREAKER_TIMEOUT'] || '60000', 10),
        modelName: process.env['AI_SERVICE_MODEL_NAME'],
        maxTokens: process.env['AI_SERVICE_MAX_TOKENS'] ? parseInt(process.env['AI_SERVICE_MAX_TOKENS'], 10) : undefined,
        temperature: process.env['AI_SERVICE_TEMPERATURE'] ? parseFloat(process.env['AI_SERVICE_TEMPERATURE']) : undefined,
    },
    externalServices: {
        threatIntelApiKey: process.env['THREAT_INTEL_API_KEY'],
        vulnerabilityScannerApiKey: process.env['VULNERABILITY_SCANNER_API_KEY'],
        notificationServiceUrl: process.env['NOTIFICATION_SERVICE_URL'],
        notificationServiceApiKey: process.env['NOTIFICATION_SERVICE_API_KEY'],
        emailServiceEnabled: process.env['EMAIL_SERVICE_ENABLED'] === 'true',
        emailServiceHost: process.env['EMAIL_SERVICE_HOST'],
        emailServicePort: process.env['EMAIL_SERVICE_PORT'] ? parseInt(process.env['EMAIL_SERVICE_PORT'], 10) : undefined,
        emailServiceUser: process.env['EMAIL_SERVICE_USER'],
        emailServicePassword: process.env['EMAIL_SERVICE_PASSWORD'],
    },
    cache: {
        ttlDefault: parseInt(process.env['CACHE_TTL_DEFAULT'] || '300', 10),
        ttlVulnerabilities: parseInt(process.env['CACHE_TTL_VULNERABILITIES'] || '1800', 10),
        ttlThreats: parseInt(process.env['CACHE_TTL_THREATS'] || '900', 10),
        ttlAiResults: parseInt(process.env['CACHE_TTL_AI_RESULTS'] || '3600', 10),
        maxItems: parseInt(process.env['CACHE_MAX_ITEMS'] || '10000', 10),
        compressionEnabled: process.env['CACHE_COMPRESSION_ENABLED'] === 'true',
        serializationFormat: process.env['CACHE_SERIALIZATION_FORMAT'] || 'json',
    },
    validation: {
        whitelist: process.env['VALIDATION_WHITELIST'] !== 'false',
        forbidNonWhitelisted: process.env['VALIDATION_FORBID_NON_WHITELISTED'] !== 'false',
        skipMissingProperties: process.env['VALIDATION_SKIP_MISSING_PROPERTIES'] === 'true',
        transform: process.env['VALIDATION_TRANSFORM'] !== 'false',
        disableErrorMessages: process.env['VALIDATION_DISABLE_ERROR_MESSAGES'] === 'true',
        stopAtFirstError: process.env['VALIDATION_STOP_AT_FIRST_ERROR'] === 'true',
    },
    fileUpload: {
        maxFileSize: parseInt(process.env['UPLOAD_MAX_FILE_SIZE'] || '10485760', 10), // 10MB
        allowedTypes: (process.env['UPLOAD_ALLOWED_TYPES'] || 'application/json,text/csv,application/xml').split(','),
        destination: process.env['UPLOAD_DESTINATION'] || './uploads',
        tempDestination: process.env['UPLOAD_TEMP_DESTINATION'] || './temp',
        cleanupInterval: parseInt(process.env['UPLOAD_CLEANUP_INTERVAL'] || '3600000', 10), // 1 hour
        virusScanEnabled: process.env['UPLOAD_VIRUS_SCAN_ENABLED'] === 'true',
    },
    pagination: {
        defaultLimit: parseInt(process.env['PAGINATION_DEFAULT_LIMIT'] || '20', 10),
        maxLimit: parseInt(process.env['PAGINATION_MAX_LIMIT'] || '100', 10),
        defaultOffset: parseInt(process.env['PAGINATION_DEFAULT_OFFSET'] || '0', 10),
        maxOffset: parseInt(process.env['PAGINATION_MAX_OFFSET'] || '10000', 10),
    },
    development: {
        enablePlayground: process.env['DEV_ENABLE_PLAYGROUND'] === 'true',
        enableIntrospection: process.env['DEV_ENABLE_INTROSPECTION'] === 'true',
        enableDebugLogging: process.env['DEV_ENABLE_DEBUG_LOGGING'] === 'true',
        mockExternalServices: process.env['DEV_MOCK_EXTERNAL_SERVICES'] === 'true',
        seedDatabase: process.env['DEV_SEED_DATABASE'] === 'true',
        enableHotReload: process.env['DEV_ENABLE_HOT_RELOAD'] !== 'false',
        enableProfiler: process.env['DEV_ENABLE_PROFILER'] === 'true',
    },
});
exports.createSentinelConfiguration = createSentinelConfiguration;
/**
 * NestJS configuration registration
 */
exports.default = (0, config_1.registerAs)('sentinel', exports.createSentinelConfiguration);
/**
 * Configuration service for dependency injection
 */
let SentinelConfigurationService = class SentinelConfigurationService {
    constructor(configService) {
        this.configService = configService;
        this.config = this.configService.get('sentinel');
    }
    get environment() {
        return this.config.environment;
    }
    get application() {
        return this.config.application;
    }
    get database() {
        return this.config.database;
    }
    get redis() {
        return this.config.redis;
    }
    get security() {
        return this.config.security;
    }
    get logging() {
        return this.config.logging;
    }
    get monitoring() {
        return this.config.monitoring;
    }
    get aiService() {
        return this.config.aiService;
    }
    get externalServices() {
        return this.config.externalServices;
    }
    get cache() {
        return this.config.cache;
    }
    get validation() {
        return this.config.validation;
    }
    get fileUpload() {
        return this.config.fileUpload;
    }
    get pagination() {
        return this.config.pagination;
    }
    get development() {
        return this.config.development;
    }
    /**
     * Get the complete configuration object
     */
    getAll() {
        return this.config;
    }
    /**
     * Check if running in development mode
     */
    isDevelopment() {
        return this.config.environment.isDevelopment;
    }
    /**
     * Check if running in production mode
     */
    isProduction() {
        return this.config.environment.isProduction;
    }
    /**
     * Check if running in test mode
     */
    isTest() {
        return this.config.environment.isTest;
    }
    /**
     * Check if running in staging mode
     */
    isStaging() {
        return this.config.environment.isStaging;
    }
};
exports.SentinelConfigurationService = SentinelConfigurationService;
exports.SentinelConfigurationService = SentinelConfigurationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_2.ConfigService !== "undefined" && config_2.ConfigService) === "function" ? _a : Object])
], SentinelConfigurationService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxpbmZyYXN0cnVjdHVyZVxcY29uZmlnXFxjb25maWd1cmF0aW9uLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBQSwyQ0FBNEM7QUFDNUMsMkNBQStDO0FBQy9DLDJDQUE0QztBQTJNNUM7O0dBRUc7QUFDSSxNQUFNLHVCQUF1QixHQUFHLEdBQXNCLEVBQUU7SUFDN0QsTUFBTSxHQUFHLEdBQUcsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLFVBQVUsQ0FBQyxJQUFJLGFBQWEsQ0FBZ0IsQ0FBQztJQUV0RSxPQUFPO1FBQ0wsV0FBVyxFQUFFLEdBQUc7UUFDaEIsYUFBYSxFQUFFLEdBQUcsS0FBSyxhQUFhO1FBQ3BDLFlBQVksRUFBRSxHQUFHLEtBQUssWUFBWTtRQUNsQyxNQUFNLEVBQUUsR0FBRyxLQUFLLE1BQU07UUFDdEIsU0FBUyxFQUFFLEdBQUcsS0FBSyxTQUFTO1FBQzVCLEtBQUssRUFBRSxHQUFHLEtBQUssYUFBYSxJQUFJLEdBQUcsS0FBSyxNQUFNO1FBQzlDLFNBQVMsRUFBRSxHQUFHLEtBQUssYUFBYTtLQUNqQyxDQUFDO0FBQ0osQ0FBQyxDQUFDO0FBWlcsUUFBQSx1QkFBdUIsMkJBWWxDO0FBRUY7O0dBRUc7QUFDSSxNQUFNLHVCQUF1QixHQUFHLEdBQXNCLEVBQUUsQ0FBQyxDQUFDO0lBQy9ELElBQUksRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLFVBQVUsQ0FBQyxJQUFJLGtCQUFrQjtJQUNuRCxPQUFPLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxhQUFhLENBQUMsSUFBSSxPQUFPO0lBQzlDLFdBQVcsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLGlCQUFpQixDQUFDLElBQUksb0RBQW9EO0lBQ25HLElBQUksRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsSUFBSSxNQUFNLEVBQUUsRUFBRSxDQUFDO0lBQ2pELFlBQVksRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLFlBQVksQ0FBQyxJQUFJLEtBQUs7SUFDaEQsVUFBVSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsYUFBYSxDQUFDLEVBQUUsS0FBSyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsdUJBQXVCLENBQUM7SUFDL0UsVUFBVSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsYUFBYSxDQUFDLEtBQUssTUFBTTtJQUNqRCxlQUFlLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsa0JBQWtCLENBQUMsSUFBSSxPQUFPLEVBQUUsRUFBRSxDQUFDO0NBQzFFLENBQUMsQ0FBQztBQVRVLFFBQUEsdUJBQXVCLDJCQVNqQztBQUVIOztHQUVHO0FBQ0ksTUFBTSxvQkFBb0IsR0FBRyxHQUE2QixFQUFFLENBQUMsQ0FBQztJQUNuRSxJQUFJLEVBQUcsT0FBTyxDQUFDLEdBQUcsQ0FBQyxlQUFlLENBQVMsSUFBSSxVQUFVO0lBQ3pELElBQUksRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLGVBQWUsQ0FBQyxJQUFJLFdBQVc7SUFDakQsSUFBSSxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLGVBQWUsQ0FBQyxJQUFJLE1BQU0sRUFBRSxFQUFFLENBQUM7SUFDMUQsUUFBUSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsbUJBQW1CLENBQUMsSUFBSSxVQUFVO0lBQ3hELFFBQVEsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLG1CQUFtQixDQUFDLElBQUksVUFBVTtJQUN4RCxRQUFRLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxlQUFlLENBQUMsSUFBSSxVQUFVO0lBQ3BELE1BQU0sRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLGlCQUFpQixDQUFDLElBQUksUUFBUTtJQUNsRCxHQUFHLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxjQUFjLENBQUMsS0FBSyxNQUFNO0lBQzNDLE9BQU8sRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLGtCQUFrQixDQUFDLEtBQUssT0FBTztJQUNwRCxXQUFXLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxzQkFBc0IsQ0FBQyxLQUFLLE1BQU07SUFDM0QsYUFBYSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMseUJBQXlCLENBQUMsS0FBSyxPQUFPO0lBQ2pFLGNBQWMsRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQywwQkFBMEIsQ0FBQyxJQUFJLEtBQUssRUFBRSxFQUFFLENBQUM7SUFDOUUsaUJBQWlCLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsNkJBQTZCLENBQUMsSUFBSSxPQUFPLEVBQUUsRUFBRSxDQUFDO0lBQ3RGLGNBQWMsRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQywwQkFBMEIsQ0FBQyxJQUFJLE9BQU8sRUFBRSxFQUFFLENBQUM7SUFDaEYsV0FBVyxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLHVCQUF1QixDQUFDLElBQUksT0FBTyxFQUFFLEVBQUUsQ0FBQztJQUMxRSxZQUFZLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsd0JBQXdCLENBQUMsSUFBSSxNQUFNLEVBQUUsRUFBRSxDQUFDO0lBQzNFLGdCQUFnQixFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLDZCQUE2QixDQUFDLElBQUksT0FBTyxFQUFFLEVBQUUsQ0FBQztJQUNyRixhQUFhLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMseUJBQXlCLENBQUMsSUFBSSxHQUFHLEVBQUUsRUFBRSxDQUFDO0NBQzNFLENBQUMsQ0FBQztBQW5CVSxRQUFBLG9CQUFvQix3QkFtQjlCO0FBRUg7O0dBRUc7QUFDSSxNQUFNLGlCQUFpQixHQUFHLEdBQTBCLEVBQUUsQ0FBQyxDQUFDO0lBQzdELElBQUksRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLFlBQVksQ0FBQyxJQUFJLFdBQVc7SUFDOUMsSUFBSSxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLFlBQVksQ0FBQyxJQUFJLE1BQU0sRUFBRSxFQUFFLENBQUM7SUFDdkQsUUFBUSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxTQUFTO0lBQ3BELEVBQUUsRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsSUFBSSxHQUFHLEVBQUUsRUFBRSxDQUFDO0lBQ2hELEdBQUcsRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxXQUFXLENBQUMsSUFBSSxNQUFNLEVBQUUsRUFBRSxDQUFDO0lBQ3JELFVBQVUsRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLEdBQUcsRUFBRSxFQUFFLENBQUM7SUFDakUsVUFBVSxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLG1CQUFtQixDQUFDLElBQUksTUFBTSxFQUFFLEVBQUUsQ0FBQztJQUNwRSxjQUFjLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsdUJBQXVCLENBQUMsSUFBSSxPQUFPLEVBQUUsRUFBRSxDQUFDO0lBQzdFLFdBQVcsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLG9CQUFvQixDQUFDLEtBQUssT0FBTztJQUMxRCxTQUFTLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsa0JBQWtCLENBQUMsSUFBSSxPQUFPLEVBQUUsRUFBRSxDQUFDO0lBQ25FLE1BQU0sRUFBRyxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxjQUFjLENBQUMsSUFBSSxHQUFHLEVBQUUsRUFBRSxDQUFXO0NBQ3BFLENBQUMsQ0FBQztBQVpVLFFBQUEsaUJBQWlCLHFCQVkzQjtBQUVIOztHQUVHO0FBQ0ksTUFBTSxvQkFBb0IsR0FBRyxHQUEwQixFQUFFLENBQUMsQ0FBQztJQUNoRSxTQUFTLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxZQUFZLENBQUMsSUFBSSxnREFBZ0Q7SUFDeEYsWUFBWSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxJQUFJO0lBQ25ELGdCQUFnQixFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsb0JBQW9CLENBQUMsSUFBSSxvREFBb0Q7SUFDM0csbUJBQW1CLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyx3QkFBd0IsQ0FBQyxJQUFJLElBQUk7SUFDbEUsWUFBWSxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLGVBQWUsQ0FBQyxJQUFJLElBQUksRUFBRSxFQUFFLENBQUM7SUFDaEUsYUFBYSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxvREFBb0Q7SUFDcEcsWUFBWSxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLGdCQUFnQixDQUFDLElBQUksSUFBSSxFQUFFLEVBQUUsQ0FBQztJQUNqRSxjQUFjLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsa0JBQWtCLENBQUMsSUFBSSxLQUFLLEVBQUUsRUFBRSxDQUFDO0lBQ3RFLFdBQVcsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLGNBQWMsQ0FBQyxLQUFLLE9BQU87SUFDcEQsZUFBZSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsa0JBQWtCLENBQUMsS0FBSyxPQUFPO0lBQzVELFdBQVcsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLHVCQUF1QixDQUFDLEtBQUssTUFBTTtJQUM1RCxVQUFVLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsdUJBQXVCLENBQUMsSUFBSSxVQUFVLEVBQUUsRUFBRSxDQUFDO0lBQzVFLFVBQVUsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLHNCQUFzQixDQUFDLEtBQUssTUFBTTtJQUMxRCxZQUFZLEVBQUcsT0FBTyxDQUFDLEdBQUcsQ0FBQyx3QkFBd0IsQ0FBUyxJQUFJLE1BQU07SUFDdEUsa0JBQWtCLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQywrQkFBK0IsQ0FBQyxLQUFLLE9BQU87Q0FDN0UsQ0FBQyxDQUFDO0FBaEJVLFFBQUEsb0JBQW9CLHdCQWdCOUI7QUFFSDs7R0FFRztBQUNJLE1BQU0sMkJBQTJCLEdBQUcsR0FBMEIsRUFBRSxDQUFDLENBQUM7SUFDdkUsV0FBVyxFQUFFLElBQUEsK0JBQXVCLEdBQUU7SUFDdEMsV0FBVyxFQUFFLElBQUEsK0JBQXVCLEdBQUU7SUFDdEMsUUFBUSxFQUFFLElBQUEsNEJBQW9CLEdBQUU7SUFDaEMsS0FBSyxFQUFFLElBQUEseUJBQWlCLEdBQUU7SUFDMUIsUUFBUSxFQUFFLElBQUEsNEJBQW9CLEdBQUU7SUFDaEMsT0FBTyxFQUFFO1FBQ1AsS0FBSyxFQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFTLElBQUksTUFBTTtRQUNsRCxNQUFNLEVBQUcsT0FBTyxDQUFDLEdBQUcsQ0FBQyxZQUFZLENBQVMsSUFBSSxNQUFNO1FBQ3BELFdBQVcsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLGtCQUFrQixDQUFDLEtBQUssTUFBTTtRQUN2RCxRQUFRLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxlQUFlLENBQUMsSUFBSSxjQUFjO1FBQ3hELFdBQVcsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLG1CQUFtQixDQUFDLElBQUksS0FBSztRQUN0RCxZQUFZLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsb0JBQW9CLENBQUMsSUFBSSxHQUFHLEVBQUUsRUFBRSxDQUFDO1FBQ3BFLGNBQWMsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLHFCQUFxQixDQUFDLEtBQUssT0FBTztRQUM5RCxZQUFZLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxtQkFBbUIsQ0FBQyxLQUFLLE1BQU07UUFDekQsU0FBUyxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxnQkFBZ0I7UUFDNUQsb0JBQW9CLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyw0QkFBNEIsQ0FBQyxLQUFLLE9BQU87UUFDM0UsaUJBQWlCLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxnQkFBZ0IsQ0FBQyxLQUFLLE9BQU87S0FDN0Q7SUFDRCxVQUFVLEVBQUU7UUFDVixrQkFBa0IsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLHNCQUFzQixDQUFDLEtBQUssT0FBTztRQUNuRSwwQkFBMEIsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLCtCQUErQixDQUFDLEtBQUssT0FBTztRQUNwRix1QkFBdUIsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLDRCQUE0QixDQUFDLEtBQUssT0FBTztRQUM5RSxtQkFBbUIsRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxvQ0FBb0MsQ0FBQyxJQUFJLEtBQUssRUFBRSxFQUFFLENBQUM7UUFDN0Ysa0JBQWtCLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsbUNBQW1DLENBQUMsSUFBSSxLQUFLLEVBQUUsRUFBRSxDQUFDO1FBQzNGLGNBQWMsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLGlCQUFpQixDQUFDLEtBQUssTUFBTTtRQUN6RCxXQUFXLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsY0FBYyxDQUFDLElBQUksTUFBTSxFQUFFLEVBQUUsQ0FBQztRQUNoRSxXQUFXLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxjQUFjLENBQUMsSUFBSSxVQUFVO1FBQ3RELGNBQWMsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLGlCQUFpQixDQUFDLEtBQUssTUFBTTtRQUN6RCxrQkFBa0IsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLHNCQUFzQixDQUFDLElBQUksa0JBQWtCO1FBQzdFLHFCQUFxQixFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMseUJBQXlCLENBQUM7S0FDOUQ7SUFDRCxTQUFTLEVBQUU7UUFDVCxPQUFPLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxvQkFBb0IsQ0FBQyxLQUFLLE1BQU07UUFDckQsR0FBRyxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0JBQWdCLENBQUM7UUFDbEMsTUFBTSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsb0JBQW9CLENBQUM7UUFDekMsT0FBTyxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLG9CQUFvQixDQUFDLElBQUksT0FBTyxFQUFFLEVBQUUsQ0FBQztRQUNuRSxhQUFhLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsMkJBQTJCLENBQUMsSUFBSSxHQUFHLEVBQUUsRUFBRSxDQUFDO1FBQzVFLFVBQVUsRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyx3QkFBd0IsQ0FBQyxJQUFJLE1BQU0sRUFBRSxFQUFFLENBQUM7UUFDekUscUJBQXFCLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxvQ0FBb0MsQ0FBQyxLQUFLLE1BQU07UUFDbkYsdUJBQXVCLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsc0NBQXNDLENBQUMsSUFBSSxHQUFHLEVBQUUsRUFBRSxDQUFDO1FBQ2pHLHFCQUFxQixFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLG9DQUFvQyxDQUFDLElBQUksT0FBTyxFQUFFLEVBQUUsQ0FBQztRQUNqRyxTQUFTLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyx1QkFBdUIsQ0FBQztRQUMvQyxTQUFTLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyx1QkFBdUIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyx1QkFBdUIsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTO1FBQ2hILFdBQVcsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLHdCQUF3QixDQUFDLENBQUMsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLHdCQUF3QixDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUztLQUNuSDtJQUNELGdCQUFnQixFQUFFO1FBQ2hCLGlCQUFpQixFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsc0JBQXNCLENBQUM7UUFDdEQsMEJBQTBCLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQywrQkFBK0IsQ0FBQztRQUN4RSxzQkFBc0IsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLDBCQUEwQixDQUFDO1FBQy9ELHlCQUF5QixFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsOEJBQThCLENBQUM7UUFDdEUsbUJBQW1CLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyx1QkFBdUIsQ0FBQyxLQUFLLE1BQU07UUFDcEUsZ0JBQWdCLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxvQkFBb0IsQ0FBQztRQUNuRCxnQkFBZ0IsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLG9CQUFvQixDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLG9CQUFvQixDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVM7UUFDakgsZ0JBQWdCLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxvQkFBb0IsQ0FBQztRQUNuRCxvQkFBb0IsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLHdCQUF3QixDQUFDO0tBQzVEO0lBQ0QsS0FBSyxFQUFFO1FBQ0wsVUFBVSxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLG1CQUFtQixDQUFDLElBQUksS0FBSyxFQUFFLEVBQUUsQ0FBQztRQUNuRSxrQkFBa0IsRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQywyQkFBMkIsQ0FBQyxJQUFJLE1BQU0sRUFBRSxFQUFFLENBQUM7UUFDcEYsVUFBVSxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLG1CQUFtQixDQUFDLElBQUksS0FBSyxFQUFFLEVBQUUsQ0FBQztRQUNuRSxZQUFZLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsc0JBQXNCLENBQUMsSUFBSSxNQUFNLEVBQUUsRUFBRSxDQUFDO1FBQ3pFLFFBQVEsRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLE9BQU8sRUFBRSxFQUFFLENBQUM7UUFDakUsa0JBQWtCLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQywyQkFBMkIsQ0FBQyxLQUFLLE1BQU07UUFDdkUsbUJBQW1CLEVBQUcsT0FBTyxDQUFDLEdBQUcsQ0FBQyw0QkFBNEIsQ0FBUyxJQUFJLE1BQU07S0FDbEY7SUFDRCxVQUFVLEVBQUU7UUFDVixTQUFTLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxzQkFBc0IsQ0FBQyxLQUFLLE9BQU87UUFDMUQsb0JBQW9CLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxtQ0FBbUMsQ0FBQyxLQUFLLE9BQU87UUFDbEYscUJBQXFCLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxvQ0FBb0MsQ0FBQyxLQUFLLE1BQU07UUFDbkYsU0FBUyxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsc0JBQXNCLENBQUMsS0FBSyxPQUFPO1FBQzFELG9CQUFvQixFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsbUNBQW1DLENBQUMsS0FBSyxNQUFNO1FBQ2pGLGdCQUFnQixFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0NBQWdDLENBQUMsS0FBSyxNQUFNO0tBQzNFO0lBQ0QsVUFBVSxFQUFFO1FBQ1YsV0FBVyxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLHNCQUFzQixDQUFDLElBQUksVUFBVSxFQUFFLEVBQUUsQ0FBQyxFQUFFLE9BQU87UUFDckYsWUFBWSxFQUFFLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxzQkFBc0IsQ0FBQyxJQUFJLDJDQUEyQyxDQUFDLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQztRQUM3RyxXQUFXLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLFdBQVc7UUFDN0QsZUFBZSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMseUJBQXlCLENBQUMsSUFBSSxRQUFRO1FBQ25FLGVBQWUsRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyx5QkFBeUIsQ0FBQyxJQUFJLFNBQVMsRUFBRSxFQUFFLENBQUMsRUFBRSxTQUFTO1FBQzdGLGdCQUFnQixFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsMkJBQTJCLENBQUMsS0FBSyxNQUFNO0tBQ3RFO0lBQ0QsVUFBVSxFQUFFO1FBQ1YsWUFBWSxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLDBCQUEwQixDQUFDLElBQUksSUFBSSxFQUFFLEVBQUUsQ0FBQztRQUMzRSxRQUFRLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsc0JBQXNCLENBQUMsSUFBSSxLQUFLLEVBQUUsRUFBRSxDQUFDO1FBQ3BFLGFBQWEsRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQywyQkFBMkIsQ0FBQyxJQUFJLEdBQUcsRUFBRSxFQUFFLENBQUM7UUFDNUUsU0FBUyxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLHVCQUF1QixDQUFDLElBQUksT0FBTyxFQUFFLEVBQUUsQ0FBQztLQUN6RTtJQUNELFdBQVcsRUFBRTtRQUNYLGdCQUFnQixFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsdUJBQXVCLENBQUMsS0FBSyxNQUFNO1FBQ2pFLG1CQUFtQixFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsMEJBQTBCLENBQUMsS0FBSyxNQUFNO1FBQ3ZFLGtCQUFrQixFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsMEJBQTBCLENBQUMsS0FBSyxNQUFNO1FBQ3RFLG9CQUFvQixFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsNEJBQTRCLENBQUMsS0FBSyxNQUFNO1FBQzFFLFlBQVksRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLG1CQUFtQixDQUFDLEtBQUssTUFBTTtRQUN6RCxlQUFlLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyx1QkFBdUIsQ0FBQyxLQUFLLE9BQU87UUFDakUsY0FBYyxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMscUJBQXFCLENBQUMsS0FBSyxNQUFNO0tBQzlEO0NBQ0YsQ0FBQyxDQUFDO0FBakdVLFFBQUEsMkJBQTJCLCtCQWlHckM7QUFFSDs7R0FFRztBQUNILGtCQUFlLElBQUEsbUJBQVUsRUFBQyxVQUFVLEVBQUUsbUNBQTJCLENBQUMsQ0FBQztBQUVuRTs7R0FFRztBQUVJLElBQU0sNEJBQTRCLEdBQWxDLE1BQU0sNEJBQTRCO0lBR3ZDLFlBQTZCLGFBQTRCO1FBQTVCLGtCQUFhLEdBQWIsYUFBYSxDQUFlO1FBQ3ZELElBQUksQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxHQUFHLENBQXdCLFVBQVUsQ0FBRSxDQUFDO0lBQzNFLENBQUM7SUFFRCxJQUFJLFdBQVc7UUFDYixPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDO0lBQ2pDLENBQUM7SUFFRCxJQUFJLFdBQVc7UUFDYixPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDO0lBQ2pDLENBQUM7SUFFRCxJQUFJLFFBQVE7UUFDVixPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDO0lBQzlCLENBQUM7SUFFRCxJQUFJLEtBQUs7UUFDUCxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDO0lBQzNCLENBQUM7SUFFRCxJQUFJLFFBQVE7UUFDVixPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDO0lBQzlCLENBQUM7SUFFRCxJQUFJLE9BQU87UUFDVCxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDO0lBQzdCLENBQUM7SUFFRCxJQUFJLFVBQVU7UUFDWixPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDO0lBQ2hDLENBQUM7SUFFRCxJQUFJLFNBQVM7UUFDWCxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsU0FBUyxDQUFDO0lBQy9CLENBQUM7SUFFRCxJQUFJLGdCQUFnQjtRQUNsQixPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsZ0JBQWdCLENBQUM7SUFDdEMsQ0FBQztJQUVELElBQUksS0FBSztRQUNQLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUM7SUFDM0IsQ0FBQztJQUVELElBQUksVUFBVTtRQUNaLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUM7SUFDaEMsQ0FBQztJQUVELElBQUksVUFBVTtRQUNaLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUM7SUFDaEMsQ0FBQztJQUVELElBQUksVUFBVTtRQUNaLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUM7SUFDaEMsQ0FBQztJQUVELElBQUksV0FBVztRQUNiLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUM7SUFDakMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsTUFBTTtRQUNKLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQztJQUNyQixDQUFDO0lBRUQ7O09BRUc7SUFDSCxhQUFhO1FBQ1gsT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFDLFdBQVcsQ0FBQyxhQUFhLENBQUM7SUFDL0MsQ0FBQztJQUVEOztPQUVHO0lBQ0gsWUFBWTtRQUNWLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUMsWUFBWSxDQUFDO0lBQzlDLENBQUM7SUFFRDs7T0FFRztJQUNILE1BQU07UUFDSixPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDLE1BQU0sQ0FBQztJQUN4QyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxTQUFTO1FBQ1AsT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFDLFdBQVcsQ0FBQyxTQUFTLENBQUM7SUFDM0MsQ0FBQztDQUNGLENBQUE7QUFqR1ksb0VBQTRCO3VDQUE1Qiw0QkFBNEI7SUFEeEMsSUFBQSxtQkFBVSxHQUFFO3lEQUlpQyxzQkFBYSxvQkFBYixzQkFBYTtHQUg5Qyw0QkFBNEIsQ0FpR3hDIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTHVrYVxcc2VudGluZWxcXGJhY2tlbmRcXHNyY1xcaW5mcmFzdHJ1Y3R1cmVcXGNvbmZpZ1xcY29uZmlndXJhdGlvbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZWdpc3RlckFzIH0gZnJvbSAnQG5lc3Rqcy9jb25maWcnO1xyXG5pbXBvcnQgeyBDb25maWdTZXJ2aWNlIH0gZnJvbSAnQG5lc3Rqcy9jb25maWcnO1xyXG5pbXBvcnQgeyBJbmplY3RhYmxlIH0gZnJvbSAnQG5lc3Rqcy9jb21tb24nO1xyXG5cclxuLyoqXHJcbiAqIEVudmlyb25tZW50LXNwZWNpZmljIGNvbmZpZ3VyYXRpb24gc2V0dGluZ3MgZm9yIHRoZSBTZW50aW5lbCBwbGF0Zm9ybVxyXG4gKiBQcm92aWRlcyBjZW50cmFsaXplZCBjb25maWd1cmF0aW9uIG1hbmFnZW1lbnQgd2l0aCB0eXBlIHNhZmV0eSBhbmQgdmFsaWRhdGlvblxyXG4gKi9cclxuXHJcbmV4cG9ydCB0eXBlIEVudmlyb25tZW50ID0gJ2RldmVsb3BtZW50JyB8ICdwcm9kdWN0aW9uJyB8ICd0ZXN0JyB8ICdzdGFnaW5nJztcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgRW52aXJvbm1lbnRDb25maWcge1xyXG4gIGVudmlyb25tZW50OiBFbnZpcm9ubWVudDtcclxuICBpc0RldmVsb3BtZW50OiBib29sZWFuO1xyXG4gIGlzUHJvZHVjdGlvbjogYm9vbGVhbjtcclxuICBpc1Rlc3Q6IGJvb2xlYW47XHJcbiAgaXNTdGFnaW5nOiBib29sZWFuO1xyXG4gIGRlYnVnOiBib29sZWFuO1xyXG4gIGhvdFJlbG9hZDogYm9vbGVhbjtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBBcHBsaWNhdGlvbkNvbmZpZyB7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIHZlcnNpb246IHN0cmluZztcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIHBvcnQ6IG51bWJlcjtcclxuICBnbG9iYWxQcmVmaXg6IHN0cmluZztcclxuICBjb3JzT3JpZ2luOiBzdHJpbmcgfCBzdHJpbmdbXTtcclxuICB0cnVzdFByb3h5OiBib29sZWFuO1xyXG4gIHNodXRkb3duVGltZW91dDogbnVtYmVyO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIERhdGFiYXNlQ29ubmVjdGlvbkNvbmZpZyB7XHJcbiAgdHlwZTogJ3Bvc3RncmVzJyB8ICdteXNxbCcgfCAnbWFyaWFkYic7XHJcbiAgaG9zdDogc3RyaW5nO1xyXG4gIHBvcnQ6IG51bWJlcjtcclxuICB1c2VybmFtZTogc3RyaW5nO1xyXG4gIHBhc3N3b3JkOiBzdHJpbmc7XHJcbiAgZGF0YWJhc2U6IHN0cmluZztcclxuICBzY2hlbWE6IHN0cmluZztcclxuICBzc2w6IGJvb2xlYW47XHJcbiAgbG9nZ2luZzogYm9vbGVhbjtcclxuICBzeW5jaHJvbml6ZTogYm9vbGVhbjtcclxuICBtaWdyYXRpb25zUnVuOiBib29sZWFuO1xyXG4gIG1heENvbm5lY3Rpb25zOiBudW1iZXI7XHJcbiAgY29ubmVjdGlvblRpbWVvdXQ6IG51bWJlcjtcclxuICBhY3F1aXJlVGltZW91dDogbnVtYmVyO1xyXG4gIGlkbGVUaW1lb3V0OiBudW1iZXI7XHJcbiAgcmVhcEludGVydmFsOiBudW1iZXI7XHJcbiAgY3JlYXRlUmV0cnlEZWxheTogbnVtYmVyO1xyXG4gIHJldHJ5QXR0ZW1wdHM6IG51bWJlcjtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBSZWRpc0Nvbm5lY3Rpb25Db25maWcge1xyXG4gIGhvc3Q6IHN0cmluZztcclxuICBwb3J0OiBudW1iZXI7XHJcbiAgcGFzc3dvcmQ/OiBzdHJpbmc7XHJcbiAgZGI6IG51bWJlcjtcclxuICB0dGw6IG51bWJlcjtcclxuICBtYXhSZXRyaWVzOiBudW1iZXI7XHJcbiAgcmV0cnlEZWxheTogbnVtYmVyO1xyXG4gIGNvbm5lY3RUaW1lb3V0OiBudW1iZXI7XHJcbiAgbGF6eUNvbm5lY3Q6IGJvb2xlYW47XHJcbiAga2VlcEFsaXZlOiBudW1iZXI7XHJcbiAgZmFtaWx5OiA0IHwgNjtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBTZWN1cml0eUNvbmZpZ3VyYXRpb24ge1xyXG4gIGp3dFNlY3JldDogc3RyaW5nO1xyXG4gIGp3dEV4cGlyZXNJbjogc3RyaW5nO1xyXG4gIGp3dFJlZnJlc2hTZWNyZXQ6IHN0cmluZztcclxuICBqd3RSZWZyZXNoRXhwaXJlc0luOiBzdHJpbmc7XHJcbiAgYmNyeXB0Um91bmRzOiBudW1iZXI7XHJcbiAgc2Vzc2lvblNlY3JldDogc3RyaW5nO1xyXG4gIHJhdGVMaW1pdFR0bDogbnVtYmVyO1xyXG4gIHJhdGVMaW1pdExpbWl0OiBudW1iZXI7XHJcbiAgY29yc0VuYWJsZWQ6IGJvb2xlYW47XHJcbiAgY29yc0NyZWRlbnRpYWxzOiBib29sZWFuO1xyXG4gIGhzdHNFbmFibGVkOiBib29sZWFuO1xyXG4gIGhzdHNNYXhBZ2U6IG51bWJlcjtcclxuICBjc3BFbmFibGVkOiBib29sZWFuO1xyXG4gIGZyYW1lT3B0aW9uczogJ0RFTlknIHwgJ1NBTUVPUklHSU4nIHwgJ0FMTE9XLUZST00nO1xyXG4gIGNvbnRlbnRUeXBlT3B0aW9uczogYm9vbGVhbjtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBMb2dnaW5nQ29uZmlndXJhdGlvbiB7XHJcbiAgbGV2ZWw6ICdlcnJvcicgfCAnd2FybicgfCAnaW5mbycgfCAnaHR0cCcgfCAndmVyYm9zZScgfCAnZGVidWcnIHwgJ3NpbGx5JztcclxuICBmb3JtYXQ6ICdqc29uJyB8ICdzaW1wbGUnIHwgJ2NvbWJpbmVkJztcclxuICBmaWxlRW5hYmxlZDogYm9vbGVhbjtcclxuICBmaWxlUGF0aDogc3RyaW5nO1xyXG4gIGZpbGVNYXhTaXplOiBzdHJpbmc7XHJcbiAgZmlsZU1heEZpbGVzOiBudW1iZXI7XHJcbiAgY29uc29sZUVuYWJsZWQ6IGJvb2xlYW47XHJcbiAgYXVkaXRFbmFibGVkOiBib29sZWFuO1xyXG4gIGF1ZGl0UGF0aDogc3RyaW5nO1xyXG4gIGNvcnJlbGF0aW9uSWRFbmFibGVkOiBib29sZWFuO1xyXG4gIHN0cnVjdHVyZWRMb2dnaW5nOiBib29sZWFuO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIE1vbml0b3JpbmdDb25maWd1cmF0aW9uIHtcclxuICBoZWFsdGhDaGVja0VuYWJsZWQ6IGJvb2xlYW47XHJcbiAgaGVhbHRoQ2hlY2tEYXRhYmFzZUVuYWJsZWQ6IGJvb2xlYW47XHJcbiAgaGVhbHRoQ2hlY2tSZWRpc0VuYWJsZWQ6IGJvb2xlYW47XHJcbiAgbWVtb3J5SGVhcFRocmVzaG9sZDogbnVtYmVyO1xyXG4gIG1lbW9yeVJzc1RocmVzaG9sZDogbnVtYmVyO1xyXG4gIG1ldHJpY3NFbmFibGVkOiBib29sZWFuO1xyXG4gIG1ldHJpY3NQb3J0OiBudW1iZXI7XHJcbiAgbWV0cmljc1BhdGg6IHN0cmluZztcclxuICB0cmFjaW5nRW5hYmxlZDogYm9vbGVhbjtcclxuICB0cmFjaW5nU2VydmljZU5hbWU6IHN0cmluZztcclxuICB0cmFjaW5nSmFlZ2VyRW5kcG9pbnQ/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQUlTZXJ2aWNlQ29uZmlndXJhdGlvbiB7XHJcbiAgZW5hYmxlZDogYm9vbGVhbjtcclxuICB1cmw/OiBzdHJpbmc7XHJcbiAgYXBpS2V5Pzogc3RyaW5nO1xyXG4gIHRpbWVvdXQ6IG51bWJlcjtcclxuICByZXRyeUF0dGVtcHRzOiBudW1iZXI7XHJcbiAgcmV0cnlEZWxheTogbnVtYmVyO1xyXG4gIGNpcmN1aXRCcmVha2VyRW5hYmxlZDogYm9vbGVhbjtcclxuICBjaXJjdWl0QnJlYWtlclRocmVzaG9sZDogbnVtYmVyO1xyXG4gIGNpcmN1aXRCcmVha2VyVGltZW91dDogbnVtYmVyO1xyXG4gIG1vZGVsTmFtZT86IHN0cmluZztcclxuICBtYXhUb2tlbnM/OiBudW1iZXI7XHJcbiAgdGVtcGVyYXR1cmU/OiBudW1iZXI7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgRXh0ZXJuYWxTZXJ2aWNlc0NvbmZpZ3VyYXRpb24ge1xyXG4gIHRocmVhdEludGVsQXBpS2V5Pzogc3RyaW5nO1xyXG4gIHZ1bG5lcmFiaWxpdHlTY2FubmVyQXBpS2V5Pzogc3RyaW5nO1xyXG4gIG5vdGlmaWNhdGlvblNlcnZpY2VVcmw/OiBzdHJpbmc7XHJcbiAgbm90aWZpY2F0aW9uU2VydmljZUFwaUtleT86IHN0cmluZztcclxuICBlbWFpbFNlcnZpY2VFbmFibGVkOiBib29sZWFuO1xyXG4gIGVtYWlsU2VydmljZUhvc3Q/OiBzdHJpbmc7XHJcbiAgZW1haWxTZXJ2aWNlUG9ydD86IG51bWJlcjtcclxuICBlbWFpbFNlcnZpY2VVc2VyPzogc3RyaW5nO1xyXG4gIGVtYWlsU2VydmljZVBhc3N3b3JkPzogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIENhY2hlQ29uZmlndXJhdGlvbiB7XHJcbiAgdHRsRGVmYXVsdDogbnVtYmVyO1xyXG4gIHR0bFZ1bG5lcmFiaWxpdGllczogbnVtYmVyO1xyXG4gIHR0bFRocmVhdHM6IG51bWJlcjtcclxuICB0dGxBaVJlc3VsdHM6IG51bWJlcjtcclxuICBtYXhJdGVtczogbnVtYmVyO1xyXG4gIGNvbXByZXNzaW9uRW5hYmxlZDogYm9vbGVhbjtcclxuICBzZXJpYWxpemF0aW9uRm9ybWF0OiAnanNvbicgfCAnbXNncGFjayc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVmFsaWRhdGlvbkNvbmZpZ3VyYXRpb24ge1xyXG4gIHdoaXRlbGlzdDogYm9vbGVhbjtcclxuICBmb3JiaWROb25XaGl0ZWxpc3RlZDogYm9vbGVhbjtcclxuICBza2lwTWlzc2luZ1Byb3BlcnRpZXM6IGJvb2xlYW47XHJcbiAgdHJhbnNmb3JtOiBib29sZWFuO1xyXG4gIGRpc2FibGVFcnJvck1lc3NhZ2VzOiBib29sZWFuO1xyXG4gIHN0b3BBdEZpcnN0RXJyb3I6IGJvb2xlYW47XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgRmlsZVVwbG9hZENvbmZpZ3VyYXRpb24ge1xyXG4gIG1heEZpbGVTaXplOiBudW1iZXI7XHJcbiAgYWxsb3dlZFR5cGVzOiBzdHJpbmdbXTtcclxuICBkZXN0aW5hdGlvbjogc3RyaW5nO1xyXG4gIHRlbXBEZXN0aW5hdGlvbjogc3RyaW5nO1xyXG4gIGNsZWFudXBJbnRlcnZhbDogbnVtYmVyO1xyXG4gIHZpcnVzU2NhbkVuYWJsZWQ6IGJvb2xlYW47XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgUGFnaW5hdGlvbkNvbmZpZ3VyYXRpb24ge1xyXG4gIGRlZmF1bHRMaW1pdDogbnVtYmVyO1xyXG4gIG1heExpbWl0OiBudW1iZXI7XHJcbiAgZGVmYXVsdE9mZnNldDogbnVtYmVyO1xyXG4gIG1heE9mZnNldDogbnVtYmVyO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIERldmVsb3BtZW50Q29uZmlndXJhdGlvbiB7XHJcbiAgZW5hYmxlUGxheWdyb3VuZDogYm9vbGVhbjtcclxuICBlbmFibGVJbnRyb3NwZWN0aW9uOiBib29sZWFuO1xyXG4gIGVuYWJsZURlYnVnTG9nZ2luZzogYm9vbGVhbjtcclxuICBtb2NrRXh0ZXJuYWxTZXJ2aWNlczogYm9vbGVhbjtcclxuICBzZWVkRGF0YWJhc2U6IGJvb2xlYW47XHJcbiAgZW5hYmxlSG90UmVsb2FkOiBib29sZWFuO1xyXG4gIGVuYWJsZVByb2ZpbGVyOiBib29sZWFuO1xyXG59XHJcblxyXG4vKipcclxuICogQ29tcGxldGUgYXBwbGljYXRpb24gY29uZmlndXJhdGlvbiBpbnRlcmZhY2VcclxuICovXHJcbmV4cG9ydCBpbnRlcmZhY2UgU2VudGluZWxDb25maWd1cmF0aW9uIHtcclxuICBlbnZpcm9ubWVudDogRW52aXJvbm1lbnRDb25maWc7XHJcbiAgYXBwbGljYXRpb246IEFwcGxpY2F0aW9uQ29uZmlnO1xyXG4gIGRhdGFiYXNlOiBEYXRhYmFzZUNvbm5lY3Rpb25Db25maWc7XHJcbiAgcmVkaXM6IFJlZGlzQ29ubmVjdGlvbkNvbmZpZztcclxuICBzZWN1cml0eTogU2VjdXJpdHlDb25maWd1cmF0aW9uO1xyXG4gIGxvZ2dpbmc6IExvZ2dpbmdDb25maWd1cmF0aW9uO1xyXG4gIG1vbml0b3Jpbmc6IE1vbml0b3JpbmdDb25maWd1cmF0aW9uO1xyXG4gIGFpU2VydmljZTogQUlTZXJ2aWNlQ29uZmlndXJhdGlvbjtcclxuICBleHRlcm5hbFNlcnZpY2VzOiBFeHRlcm5hbFNlcnZpY2VzQ29uZmlndXJhdGlvbjtcclxuICBjYWNoZTogQ2FjaGVDb25maWd1cmF0aW9uO1xyXG4gIHZhbGlkYXRpb246IFZhbGlkYXRpb25Db25maWd1cmF0aW9uO1xyXG4gIGZpbGVVcGxvYWQ6IEZpbGVVcGxvYWRDb25maWd1cmF0aW9uO1xyXG4gIHBhZ2luYXRpb246IFBhZ2luYXRpb25Db25maWd1cmF0aW9uO1xyXG4gIGRldmVsb3BtZW50OiBEZXZlbG9wbWVudENvbmZpZ3VyYXRpb247XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBFbnZpcm9ubWVudC1zcGVjaWZpYyBjb25maWd1cmF0aW9uIGZhY3RvcnlcclxuICovXHJcbmV4cG9ydCBjb25zdCBjcmVhdGVFbnZpcm9ubWVudENvbmZpZyA9ICgpOiBFbnZpcm9ubWVudENvbmZpZyA9PiB7XHJcbiAgY29uc3QgZW52ID0gKHByb2Nlc3MuZW52WydOT0RFX0VOViddIHx8ICdkZXZlbG9wbWVudCcpIGFzIEVudmlyb25tZW50O1xyXG4gIFxyXG4gIHJldHVybiB7XHJcbiAgICBlbnZpcm9ubWVudDogZW52LFxyXG4gICAgaXNEZXZlbG9wbWVudDogZW52ID09PSAnZGV2ZWxvcG1lbnQnLFxyXG4gICAgaXNQcm9kdWN0aW9uOiBlbnYgPT09ICdwcm9kdWN0aW9uJyxcclxuICAgIGlzVGVzdDogZW52ID09PSAndGVzdCcsXHJcbiAgICBpc1N0YWdpbmc6IGVudiA9PT0gJ3N0YWdpbmcnLFxyXG4gICAgZGVidWc6IGVudiA9PT0gJ2RldmVsb3BtZW50JyB8fCBlbnYgPT09ICd0ZXN0JyxcclxuICAgIGhvdFJlbG9hZDogZW52ID09PSAnZGV2ZWxvcG1lbnQnLFxyXG4gIH07XHJcbn07XHJcblxyXG4vKipcclxuICogQXBwbGljYXRpb24gY29uZmlndXJhdGlvbiBmYWN0b3J5XHJcbiAqL1xyXG5leHBvcnQgY29uc3QgY3JlYXRlQXBwbGljYXRpb25Db25maWcgPSAoKTogQXBwbGljYXRpb25Db25maWcgPT4gKHtcclxuICBuYW1lOiBwcm9jZXNzLmVudlsnQVBQX05BTUUnXSB8fCAnU2VudGluZWwgQmFja2VuZCcsXHJcbiAgdmVyc2lvbjogcHJvY2Vzcy5lbnZbJ0FQUF9WRVJTSU9OJ10gfHwgJzEuMC4wJyxcclxuICBkZXNjcmlwdGlvbjogcHJvY2Vzcy5lbnZbJ0FQUF9ERVNDUklQVElPTiddIHx8ICdTZW50aW5lbCBWdWxuZXJhYmlsaXR5IEFzc2Vzc21lbnQgUGxhdGZvcm0gQmFja2VuZCcsXHJcbiAgcG9ydDogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ1BPUlQnXSB8fCAnMzAwMCcsIDEwKSxcclxuICBnbG9iYWxQcmVmaXg6IHByb2Nlc3MuZW52WydBUElfUFJFRklYJ10gfHwgJ2FwaScsXHJcbiAgY29yc09yaWdpbjogcHJvY2Vzcy5lbnZbJ0NPUlNfT1JJR0lOJ10/LnNwbGl0KCcsJykgfHwgWydodHRwOi8vbG9jYWxob3N0OjMwMDEnXSxcclxuICB0cnVzdFByb3h5OiBwcm9jZXNzLmVudlsnVFJVU1RfUFJPWFknXSA9PT0gJ3RydWUnLFxyXG4gIHNodXRkb3duVGltZW91dDogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ1NIVVRET1dOX1RJTUVPVVQnXSB8fCAnMTAwMDAnLCAxMCksXHJcbn0pO1xyXG5cclxuLyoqXHJcbiAqIERhdGFiYXNlIGNvbmZpZ3VyYXRpb24gZmFjdG9yeVxyXG4gKi9cclxuZXhwb3J0IGNvbnN0IGNyZWF0ZURhdGFiYXNlQ29uZmlnID0gKCk6IERhdGFiYXNlQ29ubmVjdGlvbkNvbmZpZyA9PiAoe1xyXG4gIHR5cGU6IChwcm9jZXNzLmVudlsnREFUQUJBU0VfVFlQRSddIGFzIGFueSkgfHwgJ3Bvc3RncmVzJyxcclxuICBob3N0OiBwcm9jZXNzLmVudlsnREFUQUJBU0VfSE9TVCddIHx8ICdsb2NhbGhvc3QnLFxyXG4gIHBvcnQ6IHBhcnNlSW50KHByb2Nlc3MuZW52WydEQVRBQkFTRV9QT1JUJ10gfHwgJzU0MzInLCAxMCksXHJcbiAgdXNlcm5hbWU6IHByb2Nlc3MuZW52WydEQVRBQkFTRV9VU0VSTkFNRSddIHx8ICdzZW50aW5lbCcsXHJcbiAgcGFzc3dvcmQ6IHByb2Nlc3MuZW52WydEQVRBQkFTRV9QQVNTV09SRCddIHx8ICdwYXNzd29yZCcsXHJcbiAgZGF0YWJhc2U6IHByb2Nlc3MuZW52WydEQVRBQkFTRV9OQU1FJ10gfHwgJ3NlbnRpbmVsJyxcclxuICBzY2hlbWE6IHByb2Nlc3MuZW52WydEQVRBQkFTRV9TQ0hFTUEnXSB8fCAncHVibGljJyxcclxuICBzc2w6IHByb2Nlc3MuZW52WydEQVRBQkFTRV9TU0wnXSA9PT0gJ3RydWUnLFxyXG4gIGxvZ2dpbmc6IHByb2Nlc3MuZW52WydEQVRBQkFTRV9MT0dHSU5HJ10gIT09ICdmYWxzZScsXHJcbiAgc3luY2hyb25pemU6IHByb2Nlc3MuZW52WydEQVRBQkFTRV9TWU5DSFJPTklaRSddID09PSAndHJ1ZScsXHJcbiAgbWlncmF0aW9uc1J1bjogcHJvY2Vzcy5lbnZbJ0RBVEFCQVNFX01JR1JBVElPTlNfUlVOJ10gIT09ICdmYWxzZScsXHJcbiAgbWF4Q29ubmVjdGlvbnM6IHBhcnNlSW50KHByb2Nlc3MuZW52WydEQVRBQkFTRV9NQVhfQ09OTkVDVElPTlMnXSB8fCAnMTAwJywgMTApLFxyXG4gIGNvbm5lY3Rpb25UaW1lb3V0OiBwYXJzZUludChwcm9jZXNzLmVudlsnREFUQUJBU0VfQ09OTkVDVElPTl9USU1FT1VUJ10gfHwgJzYwMDAwJywgMTApLFxyXG4gIGFjcXVpcmVUaW1lb3V0OiBwYXJzZUludChwcm9jZXNzLmVudlsnREFUQUJBU0VfQUNRVUlSRV9USU1FT1VUJ10gfHwgJzYwMDAwJywgMTApLFxyXG4gIGlkbGVUaW1lb3V0OiBwYXJzZUludChwcm9jZXNzLmVudlsnREFUQUJBU0VfSURMRV9USU1FT1VUJ10gfHwgJzEwMDAwJywgMTApLFxyXG4gIHJlYXBJbnRlcnZhbDogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ0RBVEFCQVNFX1JFQVBfSU5URVJWQUwnXSB8fCAnMTAwMCcsIDEwKSxcclxuICBjcmVhdGVSZXRyeURlbGF5OiBwYXJzZUludChwcm9jZXNzLmVudlsnREFUQUJBU0VfQ1JFQVRFX1JFVFJZX0RFTEFZJ10gfHwgJzMwMDAwJywgMTApLFxyXG4gIHJldHJ5QXR0ZW1wdHM6IHBhcnNlSW50KHByb2Nlc3MuZW52WydEQVRBQkFTRV9SRVRSWV9BVFRFTVBUUyddIHx8ICczJywgMTApLFxyXG59KTtcclxuXHJcbi8qKlxyXG4gKiBSZWRpcyBjb25maWd1cmF0aW9uIGZhY3RvcnlcclxuICovXHJcbmV4cG9ydCBjb25zdCBjcmVhdGVSZWRpc0NvbmZpZyA9ICgpOiBSZWRpc0Nvbm5lY3Rpb25Db25maWcgPT4gKHtcclxuICBob3N0OiBwcm9jZXNzLmVudlsnUkVESVNfSE9TVCddIHx8ICdsb2NhbGhvc3QnLFxyXG4gIHBvcnQ6IHBhcnNlSW50KHByb2Nlc3MuZW52WydSRURJU19QT1JUJ10gfHwgJzYzNzknLCAxMCksXHJcbiAgcGFzc3dvcmQ6IHByb2Nlc3MuZW52WydSRURJU19QQVNTV09SRCddIHx8IHVuZGVmaW5lZCxcclxuICBkYjogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ1JFRElTX0RCJ10gfHwgJzAnLCAxMCksXHJcbiAgdHRsOiBwYXJzZUludChwcm9jZXNzLmVudlsnUkVESVNfVFRMJ10gfHwgJzM2MDAnLCAxMCksXHJcbiAgbWF4UmV0cmllczogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ1JFRElTX01BWF9SRVRSSUVTJ10gfHwgJzMnLCAxMCksXHJcbiAgcmV0cnlEZWxheTogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ1JFRElTX1JFVFJZX0RFTEFZJ10gfHwgJzEwMDAnLCAxMCksXHJcbiAgY29ubmVjdFRpbWVvdXQ6IHBhcnNlSW50KHByb2Nlc3MuZW52WydSRURJU19DT05ORUNUX1RJTUVPVVQnXSB8fCAnMTAwMDAnLCAxMCksXHJcbiAgbGF6eUNvbm5lY3Q6IHByb2Nlc3MuZW52WydSRURJU19MQVpZX0NPTk5FQ1QnXSAhPT0gJ2ZhbHNlJyxcclxuICBrZWVwQWxpdmU6IHBhcnNlSW50KHByb2Nlc3MuZW52WydSRURJU19LRUVQX0FMSVZFJ10gfHwgJzMwMDAwJywgMTApLFxyXG4gIGZhbWlseTogKHBhcnNlSW50KHByb2Nlc3MuZW52WydSRURJU19GQU1JTFknXSB8fCAnNCcsIDEwKSBhcyA0IHwgNiksXHJcbn0pO1xyXG5cclxuLyoqXHJcbiAqIFNlY3VyaXR5IGNvbmZpZ3VyYXRpb24gZmFjdG9yeVxyXG4gKi9cclxuZXhwb3J0IGNvbnN0IGNyZWF0ZVNlY3VyaXR5Q29uZmlnID0gKCk6IFNlY3VyaXR5Q29uZmlndXJhdGlvbiA9PiAoe1xyXG4gIGp3dFNlY3JldDogcHJvY2Vzcy5lbnZbJ0pXVF9TRUNSRVQnXSB8fCAneW91ci1zdXBlci1zZWNyZXQtand0LWtleS1jaGFuZ2UtaW4tcHJvZHVjdGlvbicsXHJcbiAgand0RXhwaXJlc0luOiBwcm9jZXNzLmVudlsnSldUX0VYUElSRVNfSU4nXSB8fCAnMWgnLFxyXG4gIGp3dFJlZnJlc2hTZWNyZXQ6IHByb2Nlc3MuZW52WydKV1RfUkVGUkVTSF9TRUNSRVQnXSB8fCAneW91ci1zdXBlci1zZWNyZXQtcmVmcmVzaC1rZXktY2hhbmdlLWluLXByb2R1Y3Rpb24nLFxyXG4gIGp3dFJlZnJlc2hFeHBpcmVzSW46IHByb2Nlc3MuZW52WydKV1RfUkVGUkVTSF9FWFBJUkVTX0lOJ10gfHwgJzdkJyxcclxuICBiY3J5cHRSb3VuZHM6IHBhcnNlSW50KHByb2Nlc3MuZW52WydCQ1JZUFRfUk9VTkRTJ10gfHwgJzEyJywgMTApLFxyXG4gIHNlc3Npb25TZWNyZXQ6IHByb2Nlc3MuZW52WydTRVNTSU9OX1NFQ1JFVCddIHx8ICd5b3VyLXN1cGVyLXNlY3JldC1zZXNzaW9uLWtleS1jaGFuZ2UtaW4tcHJvZHVjdGlvbicsXHJcbiAgcmF0ZUxpbWl0VHRsOiBwYXJzZUludChwcm9jZXNzLmVudlsnUkFURV9MSU1JVF9UVEwnXSB8fCAnNjAnLCAxMCksXHJcbiAgcmF0ZUxpbWl0TGltaXQ6IHBhcnNlSW50KHByb2Nlc3MuZW52WydSQVRFX0xJTUlUX0xJTUlUJ10gfHwgJzEwMCcsIDEwKSxcclxuICBjb3JzRW5hYmxlZDogcHJvY2Vzcy5lbnZbJ0NPUlNfRU5BQkxFRCddICE9PSAnZmFsc2UnLFxyXG4gIGNvcnNDcmVkZW50aWFsczogcHJvY2Vzcy5lbnZbJ0NPUlNfQ1JFREVOVElBTFMnXSAhPT0gJ2ZhbHNlJyxcclxuICBoc3RzRW5hYmxlZDogcHJvY2Vzcy5lbnZbJ1NFQ1VSSVRZX0hTVFNfRU5BQkxFRCddID09PSAndHJ1ZScsXHJcbiAgaHN0c01heEFnZTogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ1NFQ1VSSVRZX0hTVFNfTUFYX0FHRSddIHx8ICczMTUzNjAwMCcsIDEwKSxcclxuICBjc3BFbmFibGVkOiBwcm9jZXNzLmVudlsnU0VDVVJJVFlfQ1NQX0VOQUJMRUQnXSA9PT0gJ3RydWUnLFxyXG4gIGZyYW1lT3B0aW9uczogKHByb2Nlc3MuZW52WydTRUNVUklUWV9GUkFNRV9PUFRJT05TJ10gYXMgYW55KSB8fCAnREVOWScsXHJcbiAgY29udGVudFR5cGVPcHRpb25zOiBwcm9jZXNzLmVudlsnU0VDVVJJVFlfQ09OVEVOVF9UWVBFX09QVElPTlMnXSAhPT0gJ2ZhbHNlJyxcclxufSk7XHJcblxyXG4vKipcclxuICogQ29tcGxldGUgY29uZmlndXJhdGlvbiBmYWN0b3J5XHJcbiAqL1xyXG5leHBvcnQgY29uc3QgY3JlYXRlU2VudGluZWxDb25maWd1cmF0aW9uID0gKCk6IFNlbnRpbmVsQ29uZmlndXJhdGlvbiA9PiAoe1xyXG4gIGVudmlyb25tZW50OiBjcmVhdGVFbnZpcm9ubWVudENvbmZpZygpLFxyXG4gIGFwcGxpY2F0aW9uOiBjcmVhdGVBcHBsaWNhdGlvbkNvbmZpZygpLFxyXG4gIGRhdGFiYXNlOiBjcmVhdGVEYXRhYmFzZUNvbmZpZygpLFxyXG4gIHJlZGlzOiBjcmVhdGVSZWRpc0NvbmZpZygpLFxyXG4gIHNlY3VyaXR5OiBjcmVhdGVTZWN1cml0eUNvbmZpZygpLFxyXG4gIGxvZ2dpbmc6IHtcclxuICAgIGxldmVsOiAocHJvY2Vzcy5lbnZbJ0xPR19MRVZFTCddIGFzIGFueSkgfHwgJ2luZm8nLFxyXG4gICAgZm9ybWF0OiAocHJvY2Vzcy5lbnZbJ0xPR19GT1JNQVQnXSBhcyBhbnkpIHx8ICdqc29uJyxcclxuICAgIGZpbGVFbmFibGVkOiBwcm9jZXNzLmVudlsnTE9HX0ZJTEVfRU5BQkxFRCddID09PSAndHJ1ZScsXHJcbiAgICBmaWxlUGF0aDogcHJvY2Vzcy5lbnZbJ0xPR19GSUxFX1BBVEgnXSB8fCAnbG9ncy9hcHAubG9nJyxcclxuICAgIGZpbGVNYXhTaXplOiBwcm9jZXNzLmVudlsnTE9HX0ZJTEVfTUFYX1NJWkUnXSB8fCAnMTBtJyxcclxuICAgIGZpbGVNYXhGaWxlczogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ0xPR19GSUxFX01BWF9GSUxFUyddIHx8ICc1JywgMTApLFxyXG4gICAgY29uc29sZUVuYWJsZWQ6IHByb2Nlc3MuZW52WydMT0dfQ09OU09MRV9FTkFCTEVEJ10gIT09ICdmYWxzZScsXHJcbiAgICBhdWRpdEVuYWJsZWQ6IHByb2Nlc3MuZW52WydMT0dfQVVESVRfRU5BQkxFRCddID09PSAndHJ1ZScsXHJcbiAgICBhdWRpdFBhdGg6IHByb2Nlc3MuZW52WydMT0dfQVVESVRfUEFUSCddIHx8ICdsb2dzL2F1ZGl0LmxvZycsXHJcbiAgICBjb3JyZWxhdGlvbklkRW5hYmxlZDogcHJvY2Vzcy5lbnZbJ0xPR19DT1JSRUxBVElPTl9JRF9FTkFCTEVEJ10gIT09ICdmYWxzZScsXHJcbiAgICBzdHJ1Y3R1cmVkTG9nZ2luZzogcHJvY2Vzcy5lbnZbJ0xPR19TVFJVQ1RVUkVEJ10gIT09ICdmYWxzZScsXHJcbiAgfSxcclxuICBtb25pdG9yaW5nOiB7XHJcbiAgICBoZWFsdGhDaGVja0VuYWJsZWQ6IHByb2Nlc3MuZW52WydIRUFMVEhfQ0hFQ0tfRU5BQkxFRCddICE9PSAnZmFsc2UnLFxyXG4gICAgaGVhbHRoQ2hlY2tEYXRhYmFzZUVuYWJsZWQ6IHByb2Nlc3MuZW52WydIRUFMVEhfQ0hFQ0tfREFUQUJBU0VfRU5BQkxFRCddICE9PSAnZmFsc2UnLFxyXG4gICAgaGVhbHRoQ2hlY2tSZWRpc0VuYWJsZWQ6IHByb2Nlc3MuZW52WydIRUFMVEhfQ0hFQ0tfUkVESVNfRU5BQkxFRCddICE9PSAnZmFsc2UnLFxyXG4gICAgbWVtb3J5SGVhcFRocmVzaG9sZDogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ0hFQUxUSF9DSEVDS19NRU1PUllfSEVBUF9USFJFU0hPTEQnXSB8fCAnMTUwJywgMTApLFxyXG4gICAgbWVtb3J5UnNzVGhyZXNob2xkOiBwYXJzZUludChwcm9jZXNzLmVudlsnSEVBTFRIX0NIRUNLX01FTU9SWV9SU1NfVEhSRVNIT0xEJ10gfHwgJzE1MCcsIDEwKSxcclxuICAgIG1ldHJpY3NFbmFibGVkOiBwcm9jZXNzLmVudlsnTUVUUklDU19FTkFCTEVEJ10gPT09ICd0cnVlJyxcclxuICAgIG1ldHJpY3NQb3J0OiBwYXJzZUludChwcm9jZXNzLmVudlsnTUVUUklDU19QT1JUJ10gfHwgJzkwOTAnLCAxMCksXHJcbiAgICBtZXRyaWNzUGF0aDogcHJvY2Vzcy5lbnZbJ01FVFJJQ1NfUEFUSCddIHx8ICcvbWV0cmljcycsXHJcbiAgICB0cmFjaW5nRW5hYmxlZDogcHJvY2Vzcy5lbnZbJ1RSQUNJTkdfRU5BQkxFRCddID09PSAndHJ1ZScsXHJcbiAgICB0cmFjaW5nU2VydmljZU5hbWU6IHByb2Nlc3MuZW52WydUUkFDSU5HX1NFUlZJQ0VfTkFNRSddIHx8ICdzZW50aW5lbC1iYWNrZW5kJyxcclxuICAgIHRyYWNpbmdKYWVnZXJFbmRwb2ludDogcHJvY2Vzcy5lbnZbJ1RSQUNJTkdfSkFFR0VSX0VORFBPSU5UJ10sXHJcbiAgfSxcclxuICBhaVNlcnZpY2U6IHtcclxuICAgIGVuYWJsZWQ6IHByb2Nlc3MuZW52WydBSV9TRVJWSUNFX0VOQUJMRUQnXSA9PT0gJ3RydWUnLFxyXG4gICAgdXJsOiBwcm9jZXNzLmVudlsnQUlfU0VSVklDRV9VUkwnXSxcclxuICAgIGFwaUtleTogcHJvY2Vzcy5lbnZbJ0FJX1NFUlZJQ0VfQVBJX0tFWSddLFxyXG4gICAgdGltZW91dDogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ0FJX1NFUlZJQ0VfVElNRU9VVCddIHx8ICczMDAwMCcsIDEwKSxcclxuICAgIHJldHJ5QXR0ZW1wdHM6IHBhcnNlSW50KHByb2Nlc3MuZW52WydBSV9TRVJWSUNFX1JFVFJZX0FUVEVNUFRTJ10gfHwgJzMnLCAxMCksXHJcbiAgICByZXRyeURlbGF5OiBwYXJzZUludChwcm9jZXNzLmVudlsnQUlfU0VSVklDRV9SRVRSWV9ERUxBWSddIHx8ICcxMDAwJywgMTApLFxyXG4gICAgY2lyY3VpdEJyZWFrZXJFbmFibGVkOiBwcm9jZXNzLmVudlsnQUlfU0VSVklDRV9DSVJDVUlUX0JSRUFLRVJfRU5BQkxFRCddID09PSAndHJ1ZScsXHJcbiAgICBjaXJjdWl0QnJlYWtlclRocmVzaG9sZDogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ0FJX1NFUlZJQ0VfQ0lSQ1VJVF9CUkVBS0VSX1RIUkVTSE9MRCddIHx8ICc1JywgMTApLFxyXG4gICAgY2lyY3VpdEJyZWFrZXJUaW1lb3V0OiBwYXJzZUludChwcm9jZXNzLmVudlsnQUlfU0VSVklDRV9DSVJDVUlUX0JSRUFLRVJfVElNRU9VVCddIHx8ICc2MDAwMCcsIDEwKSxcclxuICAgIG1vZGVsTmFtZTogcHJvY2Vzcy5lbnZbJ0FJX1NFUlZJQ0VfTU9ERUxfTkFNRSddLFxyXG4gICAgbWF4VG9rZW5zOiBwcm9jZXNzLmVudlsnQUlfU0VSVklDRV9NQVhfVE9LRU5TJ10gPyBwYXJzZUludChwcm9jZXNzLmVudlsnQUlfU0VSVklDRV9NQVhfVE9LRU5TJ10sIDEwKSA6IHVuZGVmaW5lZCxcclxuICAgIHRlbXBlcmF0dXJlOiBwcm9jZXNzLmVudlsnQUlfU0VSVklDRV9URU1QRVJBVFVSRSddID8gcGFyc2VGbG9hdChwcm9jZXNzLmVudlsnQUlfU0VSVklDRV9URU1QRVJBVFVSRSddKSA6IHVuZGVmaW5lZCxcclxuICB9LFxyXG4gIGV4dGVybmFsU2VydmljZXM6IHtcclxuICAgIHRocmVhdEludGVsQXBpS2V5OiBwcm9jZXNzLmVudlsnVEhSRUFUX0lOVEVMX0FQSV9LRVknXSxcclxuICAgIHZ1bG5lcmFiaWxpdHlTY2FubmVyQXBpS2V5OiBwcm9jZXNzLmVudlsnVlVMTkVSQUJJTElUWV9TQ0FOTkVSX0FQSV9LRVknXSxcclxuICAgIG5vdGlmaWNhdGlvblNlcnZpY2VVcmw6IHByb2Nlc3MuZW52WydOT1RJRklDQVRJT05fU0VSVklDRV9VUkwnXSxcclxuICAgIG5vdGlmaWNhdGlvblNlcnZpY2VBcGlLZXk6IHByb2Nlc3MuZW52WydOT1RJRklDQVRJT05fU0VSVklDRV9BUElfS0VZJ10sXHJcbiAgICBlbWFpbFNlcnZpY2VFbmFibGVkOiBwcm9jZXNzLmVudlsnRU1BSUxfU0VSVklDRV9FTkFCTEVEJ10gPT09ICd0cnVlJyxcclxuICAgIGVtYWlsU2VydmljZUhvc3Q6IHByb2Nlc3MuZW52WydFTUFJTF9TRVJWSUNFX0hPU1QnXSxcclxuICAgIGVtYWlsU2VydmljZVBvcnQ6IHByb2Nlc3MuZW52WydFTUFJTF9TRVJWSUNFX1BPUlQnXSA/IHBhcnNlSW50KHByb2Nlc3MuZW52WydFTUFJTF9TRVJWSUNFX1BPUlQnXSwgMTApIDogdW5kZWZpbmVkLFxyXG4gICAgZW1haWxTZXJ2aWNlVXNlcjogcHJvY2Vzcy5lbnZbJ0VNQUlMX1NFUlZJQ0VfVVNFUiddLFxyXG4gICAgZW1haWxTZXJ2aWNlUGFzc3dvcmQ6IHByb2Nlc3MuZW52WydFTUFJTF9TRVJWSUNFX1BBU1NXT1JEJ10sXHJcbiAgfSxcclxuICBjYWNoZToge1xyXG4gICAgdHRsRGVmYXVsdDogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ0NBQ0hFX1RUTF9ERUZBVUxUJ10gfHwgJzMwMCcsIDEwKSxcclxuICAgIHR0bFZ1bG5lcmFiaWxpdGllczogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ0NBQ0hFX1RUTF9WVUxORVJBQklMSVRJRVMnXSB8fCAnMTgwMCcsIDEwKSxcclxuICAgIHR0bFRocmVhdHM6IHBhcnNlSW50KHByb2Nlc3MuZW52WydDQUNIRV9UVExfVEhSRUFUUyddIHx8ICc5MDAnLCAxMCksXHJcbiAgICB0dGxBaVJlc3VsdHM6IHBhcnNlSW50KHByb2Nlc3MuZW52WydDQUNIRV9UVExfQUlfUkVTVUxUUyddIHx8ICczNjAwJywgMTApLFxyXG4gICAgbWF4SXRlbXM6IHBhcnNlSW50KHByb2Nlc3MuZW52WydDQUNIRV9NQVhfSVRFTVMnXSB8fCAnMTAwMDAnLCAxMCksXHJcbiAgICBjb21wcmVzc2lvbkVuYWJsZWQ6IHByb2Nlc3MuZW52WydDQUNIRV9DT01QUkVTU0lPTl9FTkFCTEVEJ10gPT09ICd0cnVlJyxcclxuICAgIHNlcmlhbGl6YXRpb25Gb3JtYXQ6IChwcm9jZXNzLmVudlsnQ0FDSEVfU0VSSUFMSVpBVElPTl9GT1JNQVQnXSBhcyBhbnkpIHx8ICdqc29uJyxcclxuICB9LFxyXG4gIHZhbGlkYXRpb246IHtcclxuICAgIHdoaXRlbGlzdDogcHJvY2Vzcy5lbnZbJ1ZBTElEQVRJT05fV0hJVEVMSVNUJ10gIT09ICdmYWxzZScsXHJcbiAgICBmb3JiaWROb25XaGl0ZWxpc3RlZDogcHJvY2Vzcy5lbnZbJ1ZBTElEQVRJT05fRk9SQklEX05PTl9XSElURUxJU1RFRCddICE9PSAnZmFsc2UnLFxyXG4gICAgc2tpcE1pc3NpbmdQcm9wZXJ0aWVzOiBwcm9jZXNzLmVudlsnVkFMSURBVElPTl9TS0lQX01JU1NJTkdfUFJPUEVSVElFUyddID09PSAndHJ1ZScsXHJcbiAgICB0cmFuc2Zvcm06IHByb2Nlc3MuZW52WydWQUxJREFUSU9OX1RSQU5TRk9STSddICE9PSAnZmFsc2UnLFxyXG4gICAgZGlzYWJsZUVycm9yTWVzc2FnZXM6IHByb2Nlc3MuZW52WydWQUxJREFUSU9OX0RJU0FCTEVfRVJST1JfTUVTU0FHRVMnXSA9PT0gJ3RydWUnLFxyXG4gICAgc3RvcEF0Rmlyc3RFcnJvcjogcHJvY2Vzcy5lbnZbJ1ZBTElEQVRJT05fU1RPUF9BVF9GSVJTVF9FUlJPUiddID09PSAndHJ1ZScsXHJcbiAgfSxcclxuICBmaWxlVXBsb2FkOiB7XHJcbiAgICBtYXhGaWxlU2l6ZTogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ1VQTE9BRF9NQVhfRklMRV9TSVpFJ10gfHwgJzEwNDg1NzYwJywgMTApLCAvLyAxME1CXHJcbiAgICBhbGxvd2VkVHlwZXM6IChwcm9jZXNzLmVudlsnVVBMT0FEX0FMTE9XRURfVFlQRVMnXSB8fCAnYXBwbGljYXRpb24vanNvbix0ZXh0L2NzdixhcHBsaWNhdGlvbi94bWwnKS5zcGxpdCgnLCcpLFxyXG4gICAgZGVzdGluYXRpb246IHByb2Nlc3MuZW52WydVUExPQURfREVTVElOQVRJT04nXSB8fCAnLi91cGxvYWRzJyxcclxuICAgIHRlbXBEZXN0aW5hdGlvbjogcHJvY2Vzcy5lbnZbJ1VQTE9BRF9URU1QX0RFU1RJTkFUSU9OJ10gfHwgJy4vdGVtcCcsXHJcbiAgICBjbGVhbnVwSW50ZXJ2YWw6IHBhcnNlSW50KHByb2Nlc3MuZW52WydVUExPQURfQ0xFQU5VUF9JTlRFUlZBTCddIHx8ICczNjAwMDAwJywgMTApLCAvLyAxIGhvdXJcclxuICAgIHZpcnVzU2NhbkVuYWJsZWQ6IHByb2Nlc3MuZW52WydVUExPQURfVklSVVNfU0NBTl9FTkFCTEVEJ10gPT09ICd0cnVlJyxcclxuICB9LFxyXG4gIHBhZ2luYXRpb246IHtcclxuICAgIGRlZmF1bHRMaW1pdDogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ1BBR0lOQVRJT05fREVGQVVMVF9MSU1JVCddIHx8ICcyMCcsIDEwKSxcclxuICAgIG1heExpbWl0OiBwYXJzZUludChwcm9jZXNzLmVudlsnUEFHSU5BVElPTl9NQVhfTElNSVQnXSB8fCAnMTAwJywgMTApLFxyXG4gICAgZGVmYXVsdE9mZnNldDogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ1BBR0lOQVRJT05fREVGQVVMVF9PRkZTRVQnXSB8fCAnMCcsIDEwKSxcclxuICAgIG1heE9mZnNldDogcGFyc2VJbnQocHJvY2Vzcy5lbnZbJ1BBR0lOQVRJT05fTUFYX09GRlNFVCddIHx8ICcxMDAwMCcsIDEwKSxcclxuICB9LFxyXG4gIGRldmVsb3BtZW50OiB7XHJcbiAgICBlbmFibGVQbGF5Z3JvdW5kOiBwcm9jZXNzLmVudlsnREVWX0VOQUJMRV9QTEFZR1JPVU5EJ10gPT09ICd0cnVlJyxcclxuICAgIGVuYWJsZUludHJvc3BlY3Rpb246IHByb2Nlc3MuZW52WydERVZfRU5BQkxFX0lOVFJPU1BFQ1RJT04nXSA9PT0gJ3RydWUnLFxyXG4gICAgZW5hYmxlRGVidWdMb2dnaW5nOiBwcm9jZXNzLmVudlsnREVWX0VOQUJMRV9ERUJVR19MT0dHSU5HJ10gPT09ICd0cnVlJyxcclxuICAgIG1vY2tFeHRlcm5hbFNlcnZpY2VzOiBwcm9jZXNzLmVudlsnREVWX01PQ0tfRVhURVJOQUxfU0VSVklDRVMnXSA9PT0gJ3RydWUnLFxyXG4gICAgc2VlZERhdGFiYXNlOiBwcm9jZXNzLmVudlsnREVWX1NFRURfREFUQUJBU0UnXSA9PT0gJ3RydWUnLFxyXG4gICAgZW5hYmxlSG90UmVsb2FkOiBwcm9jZXNzLmVudlsnREVWX0VOQUJMRV9IT1RfUkVMT0FEJ10gIT09ICdmYWxzZScsXHJcbiAgICBlbmFibGVQcm9maWxlcjogcHJvY2Vzcy5lbnZbJ0RFVl9FTkFCTEVfUFJPRklMRVInXSA9PT0gJ3RydWUnLFxyXG4gIH0sXHJcbn0pO1xyXG5cclxuLyoqXHJcbiAqIE5lc3RKUyBjb25maWd1cmF0aW9uIHJlZ2lzdHJhdGlvblxyXG4gKi9cclxuZXhwb3J0IGRlZmF1bHQgcmVnaXN0ZXJBcygnc2VudGluZWwnLCBjcmVhdGVTZW50aW5lbENvbmZpZ3VyYXRpb24pO1xyXG5cclxuLyoqXHJcbiAqIENvbmZpZ3VyYXRpb24gc2VydmljZSBmb3IgZGVwZW5kZW5jeSBpbmplY3Rpb25cclxuICovXHJcbkBJbmplY3RhYmxlKClcclxuZXhwb3J0IGNsYXNzIFNlbnRpbmVsQ29uZmlndXJhdGlvblNlcnZpY2Uge1xyXG4gIHByaXZhdGUgcmVhZG9ubHkgY29uZmlnOiBTZW50aW5lbENvbmZpZ3VyYXRpb247XHJcblxyXG4gIGNvbnN0cnVjdG9yKHByaXZhdGUgcmVhZG9ubHkgY29uZmlnU2VydmljZTogQ29uZmlnU2VydmljZSkge1xyXG4gICAgdGhpcy5jb25maWcgPSB0aGlzLmNvbmZpZ1NlcnZpY2UuZ2V0PFNlbnRpbmVsQ29uZmlndXJhdGlvbj4oJ3NlbnRpbmVsJykhO1xyXG4gIH1cclxuXHJcbiAgZ2V0IGVudmlyb25tZW50KCk6IEVudmlyb25tZW50Q29uZmlnIHtcclxuICAgIHJldHVybiB0aGlzLmNvbmZpZy5lbnZpcm9ubWVudDtcclxuICB9XHJcblxyXG4gIGdldCBhcHBsaWNhdGlvbigpOiBBcHBsaWNhdGlvbkNvbmZpZyB7XHJcbiAgICByZXR1cm4gdGhpcy5jb25maWcuYXBwbGljYXRpb247XHJcbiAgfVxyXG5cclxuICBnZXQgZGF0YWJhc2UoKTogRGF0YWJhc2VDb25uZWN0aW9uQ29uZmlnIHtcclxuICAgIHJldHVybiB0aGlzLmNvbmZpZy5kYXRhYmFzZTtcclxuICB9XHJcblxyXG4gIGdldCByZWRpcygpOiBSZWRpc0Nvbm5lY3Rpb25Db25maWcge1xyXG4gICAgcmV0dXJuIHRoaXMuY29uZmlnLnJlZGlzO1xyXG4gIH1cclxuXHJcbiAgZ2V0IHNlY3VyaXR5KCk6IFNlY3VyaXR5Q29uZmlndXJhdGlvbiB7XHJcbiAgICByZXR1cm4gdGhpcy5jb25maWcuc2VjdXJpdHk7XHJcbiAgfVxyXG5cclxuICBnZXQgbG9nZ2luZygpOiBMb2dnaW5nQ29uZmlndXJhdGlvbiB7XHJcbiAgICByZXR1cm4gdGhpcy5jb25maWcubG9nZ2luZztcclxuICB9XHJcblxyXG4gIGdldCBtb25pdG9yaW5nKCk6IE1vbml0b3JpbmdDb25maWd1cmF0aW9uIHtcclxuICAgIHJldHVybiB0aGlzLmNvbmZpZy5tb25pdG9yaW5nO1xyXG4gIH1cclxuXHJcbiAgZ2V0IGFpU2VydmljZSgpOiBBSVNlcnZpY2VDb25maWd1cmF0aW9uIHtcclxuICAgIHJldHVybiB0aGlzLmNvbmZpZy5haVNlcnZpY2U7XHJcbiAgfVxyXG5cclxuICBnZXQgZXh0ZXJuYWxTZXJ2aWNlcygpOiBFeHRlcm5hbFNlcnZpY2VzQ29uZmlndXJhdGlvbiB7XHJcbiAgICByZXR1cm4gdGhpcy5jb25maWcuZXh0ZXJuYWxTZXJ2aWNlcztcclxuICB9XHJcblxyXG4gIGdldCBjYWNoZSgpOiBDYWNoZUNvbmZpZ3VyYXRpb24ge1xyXG4gICAgcmV0dXJuIHRoaXMuY29uZmlnLmNhY2hlO1xyXG4gIH1cclxuXHJcbiAgZ2V0IHZhbGlkYXRpb24oKTogVmFsaWRhdGlvbkNvbmZpZ3VyYXRpb24ge1xyXG4gICAgcmV0dXJuIHRoaXMuY29uZmlnLnZhbGlkYXRpb247XHJcbiAgfVxyXG5cclxuICBnZXQgZmlsZVVwbG9hZCgpOiBGaWxlVXBsb2FkQ29uZmlndXJhdGlvbiB7XHJcbiAgICByZXR1cm4gdGhpcy5jb25maWcuZmlsZVVwbG9hZDtcclxuICB9XHJcblxyXG4gIGdldCBwYWdpbmF0aW9uKCk6IFBhZ2luYXRpb25Db25maWd1cmF0aW9uIHtcclxuICAgIHJldHVybiB0aGlzLmNvbmZpZy5wYWdpbmF0aW9uO1xyXG4gIH1cclxuXHJcbiAgZ2V0IGRldmVsb3BtZW50KCk6IERldmVsb3BtZW50Q29uZmlndXJhdGlvbiB7XHJcbiAgICByZXR1cm4gdGhpcy5jb25maWcuZGV2ZWxvcG1lbnQ7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIGNvbXBsZXRlIGNvbmZpZ3VyYXRpb24gb2JqZWN0XHJcbiAgICovXHJcbiAgZ2V0QWxsKCk6IFNlbnRpbmVsQ29uZmlndXJhdGlvbiB7XHJcbiAgICByZXR1cm4gdGhpcy5jb25maWc7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBydW5uaW5nIGluIGRldmVsb3BtZW50IG1vZGVcclxuICAgKi9cclxuICBpc0RldmVsb3BtZW50KCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMuY29uZmlnLmVudmlyb25tZW50LmlzRGV2ZWxvcG1lbnQ7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBydW5uaW5nIGluIHByb2R1Y3Rpb24gbW9kZVxyXG4gICAqL1xyXG4gIGlzUHJvZHVjdGlvbigpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmNvbmZpZy5lbnZpcm9ubWVudC5pc1Byb2R1Y3Rpb247XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBydW5uaW5nIGluIHRlc3QgbW9kZVxyXG4gICAqL1xyXG4gIGlzVGVzdCgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmNvbmZpZy5lbnZpcm9ubWVudC5pc1Rlc3Q7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBydW5uaW5nIGluIHN0YWdpbmcgbW9kZVxyXG4gICAqL1xyXG4gIGlzU3RhZ2luZygpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmNvbmZpZy5lbnZpcm9ubWVudC5pc1N0YWdpbmc7XHJcbiAgfVxyXG59Il0sInZlcnNpb24iOjN9