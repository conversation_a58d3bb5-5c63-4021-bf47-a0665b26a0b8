8605cfc423c532d0daef21599ee8fabf
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SeedRunnerService_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeedRunnerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const development_seed_1 = require("./development.seed");
const test_seed_1 = require("./test.seed");
const production_seed_1 = require("./production.seed");
/**
 * Seed runner service for managing database seeding operations
 * Handles environment-specific seeding and seed execution tracking
 */
let SeedRunnerService = SeedRunnerService_1 = class SeedRunnerService {
    constructor(dataSource, configService) {
        this.logger = new common_1.Logger(SeedRunnerService_1.name);
        this.dataSource = dataSource;
        this.configService = configService;
    }
    /**
     * Run seeds for the current environment
     */
    async runSeeds(environment) {
        const env = environment || this.configService.get('NODE_ENV', 'development');
        this.logger.log(`Running seeds for environment: ${env}`);
        try {
            await this.createSeedTrackingTable();
            switch (env) {
                case 'development':
                    await this.runDevelopmentSeeds();
                    break;
                case 'test':
                    await this.runTestSeeds();
                    break;
                case 'production':
                    await this.runProductionSeeds();
                    break;
                default:
                    this.logger.warn(`Unknown environment: ${env}, running development seeds`);
                    await this.runDevelopmentSeeds();
            }
            await this.recordSeedExecution(env);
            this.logger.log(`Seeds completed successfully for environment: ${env}`);
        }
        catch (error) {
            this.logger.error(`Seed execution failed for environment: ${env}`, {
                error: error.message,
                stack: error.stack,
            });
            throw error;
        }
    }
    /**
     * Run development environment seeds
     */
    async runDevelopmentSeeds() {
        if (await this.isSeedExecuted('development')) {
            this.logger.log('Development seeds already executed, skipping...');
            return;
        }
        const seeder = new development_seed_1.DevelopmentSeeder(this.dataSource);
        await seeder.seed();
    }
    /**
     * Run test environment seeds
     */
    async runTestSeeds() {
        const seeder = new test_seed_1.TestSeeder(this.dataSource);
        // Always clean up test data first
        try {
            await seeder.cleanup();
        }
        catch (error) {
            // Ignore cleanup errors on first run
            this.logger.debug('Test cleanup failed (expected on first run)', {
                error: error.message,
            });
        }
        await seeder.seed();
    }
    /**
     * Run production environment seeds
     */
    async runProductionSeeds() {
        if (await this.isSeedExecuted('production')) {
            this.logger.log('Production seeds already executed, skipping...');
            return;
        }
        const seeder = new production_seed_1.ProductionSeeder(this.dataSource);
        await seeder.seed();
        // Verify production seed integrity
        const isValid = await seeder.verify();
        if (!isValid) {
            throw new Error('Production seed integrity verification failed');
        }
    }
    /**
     * Force re-run seeds (ignoring previous execution)
     */
    async forceRunSeeds(environment) {
        const env = environment || this.configService.get('NODE_ENV', 'development');
        this.logger.log(`Force running seeds for environment: ${env}`);
        try {
            await this.createSeedTrackingTable();
            await this.clearSeedExecution(env);
            await this.runSeeds(env);
        }
        catch (error) {
            this.logger.error(`Force seed execution failed for environment: ${env}`, {
                error: error.message,
                stack: error.stack,
            });
            throw error;
        }
    }
    /**
     * Clean up test data
     */
    async cleanupTestData() {
        this.logger.log('Cleaning up test data...');
        try {
            const seeder = new test_seed_1.TestSeeder(this.dataSource);
            await seeder.cleanup();
            this.logger.log('Test data cleanup completed successfully');
        }
        catch (error) {
            this.logger.error('Test data cleanup failed', {
                error: error.message,
                stack: error.stack,
            });
            throw error;
        }
    }
    /**
     * Get seed execution status
     */
    async getSeedStatus() {
        try {
            await this.createSeedTrackingTable();
            const results = await this.dataSource.query(`
        SELECT environment, executed_at, success
        FROM seed_executions
        ORDER BY executed_at DESC
      `);
            const status = {
                environments: ['development', 'test', 'production'],
                executions: results,
                current_environment: this.configService.get('NODE_ENV', 'development'),
            };
            return status;
        }
        catch (error) {
            this.logger.error('Failed to get seed status', {
                error: error.message,
            });
            return {
                error: 'Failed to retrieve seed status',
                environments: ['development', 'test', 'production'],
                executions: [],
                current_environment: this.configService.get('NODE_ENV', 'development'),
            };
        }
    }
    /**
     * Create seed tracking table if it doesn't exist
     */
    async createSeedTrackingTable() {
        await this.dataSource.query(`
      CREATE TABLE IF NOT EXISTS seed_executions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        environment VARCHAR(50) NOT NULL,
        executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        success BOOLEAN NOT NULL DEFAULT true,
        error_message TEXT,
        execution_time_ms INTEGER,
        UNIQUE(environment)
      )
    `);
    }
    /**
     * Check if seeds have been executed for an environment
     */
    async isSeedExecuted(environment) {
        try {
            const result = await this.dataSource.query(`
        SELECT COUNT(*) as count 
        FROM seed_executions 
        WHERE environment = $1 AND success = true
      `, [environment]);
            return parseInt(result[0].count) > 0;
        }
        catch (error) {
            // If table doesn't exist or query fails, assume not executed
            return false;
        }
    }
    /**
     * Record seed execution
     */
    async recordSeedExecution(environment, success = true, errorMessage, executionTime) {
        try {
            await this.dataSource.query(`
        INSERT INTO seed_executions (environment, success, error_message, execution_time_ms)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (environment) DO UPDATE SET
          executed_at = CURRENT_TIMESTAMP,
          success = EXCLUDED.success,
          error_message = EXCLUDED.error_message,
          execution_time_ms = EXCLUDED.execution_time_ms
      `, [environment, success, errorMessage, executionTime]);
        }
        catch (error) {
            this.logger.error('Failed to record seed execution', {
                environment,
                error: error.message,
            });
            // Don't throw here to avoid masking the original seed error
        }
    }
    /**
     * Clear seed execution record
     */
    async clearSeedExecution(environment) {
        try {
            await this.dataSource.query(`
        DELETE FROM seed_executions WHERE environment = $1
      `, [environment]);
        }
        catch (error) {
            this.logger.error('Failed to clear seed execution record', {
                environment,
                error: error.message,
            });
        }
    }
    /**
     * Validate database schema before seeding
     */
    async validateSchema() {
        this.logger.log('Validating database schema...');
        try {
            const requiredTables = [
                'users', 'roles', 'user_roles', 'refresh_tokens', 'audit_logs',
                'security_events', 'security_event_tags', 'security_event_tag_assignments',
                'security_event_comments', 'security_event_attachments',
                'assets', 'vulnerabilities', 'asset_vulnerabilities', 'vulnerability_scans'
            ];
            for (const table of requiredTables) {
                const result = await this.dataSource.query(`
          SELECT COUNT(*) as count
          FROM information_schema.tables
          WHERE table_schema = 'public' AND table_name = $1
        `, [table]);
                if (parseInt(result[0].count) === 0) {
                    this.logger.error(`Required table '${table}' not found`);
                    return false;
                }
            }
            this.logger.log('Database schema validation passed');
            return true;
        }
        catch (error) {
            this.logger.error('Database schema validation failed', {
                error: error.message,
            });
            return false;
        }
    }
    /**
     * Get database statistics after seeding
     */
    async getDatabaseStats() {
        try {
            const stats = await Promise.all([
                this.dataSource.query('SELECT COUNT(*) as count FROM users'),
                this.dataSource.query('SELECT COUNT(*) as count FROM roles'),
                this.dataSource.query('SELECT COUNT(*) as count FROM assets'),
                this.dataSource.query('SELECT COUNT(*) as count FROM vulnerabilities'),
                this.dataSource.query('SELECT COUNT(*) as count FROM security_events'),
                this.dataSource.query('SELECT COUNT(*) as count FROM security_event_tags'),
            ]);
            return {
                users: parseInt(stats[0][0].count),
                roles: parseInt(stats[1][0].count),
                assets: parseInt(stats[2][0].count),
                vulnerabilities: parseInt(stats[3][0].count),
                security_events: parseInt(stats[4][0].count),
                security_event_tags: parseInt(stats[5][0].count),
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error('Failed to get database statistics', {
                error: error.message,
            });
            return {
                error: 'Failed to retrieve database statistics',
                timestamp: new Date().toISOString(),
            };
        }
    }
};
exports.SeedRunnerService = SeedRunnerService;
exports.SeedRunnerService = SeedRunnerService = SeedRunnerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.DataSource !== "undefined" && typeorm_2.DataSource) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object])
], SeedRunnerService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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