{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\correlated-event-status-changed.domain-event.spec.ts", "mappings": ";;AAAA,kHAGyD;AAEzD,iFAAwE;AACxE,gEAA8D;AAE9D,QAAQ,CAAC,yCAAyC,EAAE,GAAG,EAAE;IACvD,IAAI,SAAgD,CAAC;IACrD,IAAI,WAA2B,CAAC;IAChC,IAAI,UAA6B,CAAC;IAElC,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;QACtC,UAAU,GAAG;YACX,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;YAChC,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,CAAC,WAAW,CAAC;YACvB,MAAM,EAAE,EAAE;YACV,oBAAoB,EAAE,IAAI;YAC1B,eAAe,EAAE,EAAE;YACnB,SAAS,EAAE,CAAC;YACZ,YAAY,EAAE,CAAC;YACf,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,eAAe,CAAC;SAC3D,CAAC;QAEF,SAAS,GAAG;YACV,SAAS,EAAE,2CAAiB,CAAC,WAAW;YACxC,SAAS,EAAE,2CAAiB,CAAC,SAAS;YACtC,MAAM,EAAE,UAAU;YAClB,uBAAuB,EAAE,EAAE;YAC3B,oBAAoB,EAAE,KAAK;YAC3B,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE,oCAAoC;SAC7C,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,WAAW,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAExF,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YAC9E,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,aAAa,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAC9C,MAAM,gBAAgB,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1D,MAAM,aAAa,GAAG,UAAU,CAAC;YAEjC,MAAM,WAAW,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE,SAAS,EAAE;gBACtF,OAAO,EAAE,aAAa;gBACtB,UAAU,EAAE,gBAAgB;gBAC5B,aAAa;gBACb,YAAY,EAAE,CAAC;gBACf,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;aAC7B,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACzD,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtD,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,IAAI,WAAoD,CAAC;QAEzD,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YAC9B,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,iBAAiB,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBACjF,GAAG,SAAS;gBACZ,cAAc,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE;aACvC,CAAC,CAAC;YAEH,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,qBAAqB,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBACrF,GAAG,SAAS;gBACZ,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;YAEH,MAAM,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,cAAc,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC9E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,SAAS;aACvC,CAAC,CAAC;YAEH,MAAM,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,WAAW,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,MAAM;aACpC,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,eAAe,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,OAAO;gBACpC,SAAS,EAAE,2CAAiB,CAAC,WAAW;aACzC,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,OAAO;aACrC,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,OAAO;aACrC,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,OAAO;aACrC,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,mCAAmC;QAC5F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,eAAe,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,SAAS;gBACtC,MAAM,EAAE,EAAE,GAAG,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE;aACzC,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,iBAAiB,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBACjF,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,SAAS;gBACtC,MAAM,EAAE,EAAE,GAAG,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE;aAC1C,CAAC,CAAC;YAEH,MAAM,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,WAAW,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,MAAM;aACpC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,OAAO;aACrC,CAAC,CAAC;YAEH,MAAM,oBAAoB,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBACpF,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,SAAS;gBACtC,MAAM,EAAE,EAAE,GAAG,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE;aAC1C,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,uBAAuB,EAAE,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,uBAAuB,EAAE,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC9E,GAAG,SAAS;gBACZ,uBAAuB,EAAE,SAAS;aACnC,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,aAAa,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,OAAO;gBACpC,SAAS,EAAE,2CAAiB,CAAC,WAAW;aACzC,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,WAAW;gBACxC,SAAS,EAAE,2CAAiB,CAAC,SAAS;aACvC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,WAAW;gBACxC,SAAS,EAAE,2CAAiB,CAAC,MAAM;aACpC,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,YAAY,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,eAAe,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,SAAS;gBACtC,SAAS,EAAE,2CAAiB,CAAC,OAAO;aACrC,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,OAAO;gBACpC,SAAS,EAAE,2CAAiB,CAAC,WAAW;aACzC,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,eAAe,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,SAAS;gBACtC,MAAM,EAAE,EAAE,GAAG,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE;aACzC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,MAAM;aACpC,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,OAAO;gBACpC,SAAS,EAAE,2CAAiB,CAAC,WAAW;aACzC,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,SAAS;gBACtC,SAAS,EAAE,2CAAiB,CAAC,OAAO;aACrC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,OAAO;gBACpC,SAAS,EAAE,2CAAiB,CAAC,OAAO;aACrC,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC/D,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3D,MAAM,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC/D,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,uEAAuE,EAAE,GAAG,EAAE;YAC/E,MAAM,iBAAiB,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBACjF,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,SAAS;gBACtC,MAAM,EAAE,EAAE,GAAG,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE;gBACxC,uBAAuB,EAAE,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,MAAM;aACpC,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,WAAW,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,oBAAoB,EAAE,IAAI;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,aAAa,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,OAAO;gBACpC,SAAS,EAAE,2CAAiB,CAAC,WAAW;aACzC,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,SAAS;gBACtC,MAAM,EAAE,EAAE,GAAG,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE;gBACxC,oBAAoB,EAAE,IAAI;aAC3B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,WAAW,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,SAAS;gBACtC,MAAM,EAAE,EAAE,GAAG,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE;aAC1C,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,WAAW,CAAC,qBAAqB,EAAE,CAAC;YAEpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,MAAM;aACpC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;YAC7D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yCAAyC,CAAC,CAAC;YACrE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,OAAO;aACrC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,uCAAuC,CAAC,CAAC;YACnE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,OAAO;aACrC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,eAAe,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,WAAW;aACzC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,eAAe,CAAC,qBAAqB,EAAE,CAAC;YAExD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,OAAO;aACrC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,WAAW,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACxF,MAAM,OAAO,GAAG,WAAW,CAAC,kBAAkB,EAAE,CAAC;YAEjD,MAAM,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,gCAAgC,CAAC,CAAC;YAC1F,MAAM,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,CAAC,gBAAiB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,gBAAiB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBACrC,WAAW,EAAE,2CAAiB,CAAC,WAAW;gBAC1C,SAAS,EAAE,2CAAiB,CAAC,SAAS;gBACtC,eAAe,EAAE,YAAY;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,WAAW,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACxF,MAAM,OAAO,GAAG,WAAW,CAAC,kBAAkB,EAAE,CAAC;YAEjD,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,2BAA2B,CAAC,CAAC;YAClF,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,aAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,aAAc,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAClC,MAAM,EAAE,2CAAiB,CAAC,SAAS;aACpC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,MAAM,EAAE,EAAE,GAAG,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE;aACzC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,kBAAkB,EAAE,CAAC;YAClD,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,oCAAoC,CAAC,CAAC;YAC3F,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,aAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,YAAY,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,2CAAiB,CAAC,MAAM;aACpC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,kBAAkB,EAAE,CAAC;YAClD,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,gCAAgC,CAAC,CAAC;YACvF,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,aAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,aAAc,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAClC,YAAY,EAAE,2CAAiB,CAAC,MAAM;aACvC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,WAAW,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACxF,MAAM,OAAO,GAAG,WAAW,CAAC,kBAAkB,EAAE,CAAC;YAEjD,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,oCAAoC,CAAC,CAAC;YAC5F,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;YACrC,MAAM,CAAC,cAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,cAAe,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBACnC,MAAM,EAAE,2CAAiB,CAAC,SAAS;aACpC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,WAAW,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACxF,MAAM,OAAO,GAAG,WAAW,CAAC,kBAAkB,EAAE,CAAC;YAEjD,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,2BAA2B,CAAC,CAAC;YAChF,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,2BAA2B,CAAC,CAAC;YAClF,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,iCAAiC,CAAC,CAAC;YAEzF,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,WAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEnC,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,aAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAErC,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;YACrC,MAAM,CAAC,cAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,WAAW,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACxF,MAAM,OAAO,GAAG,WAAW,CAAC,eAAe,EAAE,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;YAChF,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAC1E,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,WAAW,GAAG,IAAI,sFAAuC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACxF,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;YAElC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YACvE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\correlated-event-status-changed.domain-event.spec.ts"], "sourcesContent": ["import { \r\n  CorrelatedEventStatusChangedDomainEvent, \r\n  CorrelatedEventStatusChangedEventData \r\n} from '../correlated-event-status-changed.domain-event';\r\nimport { CorrelationResult } from '../../entities/correlated-event.entity';\r\nimport { CorrelationStatus } from '../../enums/correlation-status.enum';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\n\r\ndescribe('CorrelatedEventStatusChangedDomainEvent', () => {\r\n  let eventData: CorrelatedEventStatusChangedEventData;\r\n  let aggregateId: UniqueEntityId;\r\n  let mockResult: CorrelationResult;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.create();\r\n    mockResult = {\r\n      success: true,\r\n      appliedRules: ['rule1', 'rule2'],\r\n      failedRules: [],\r\n      warnings: ['Warning 1'],\r\n      errors: [],\r\n      processingDurationMs: 1500,\r\n      confidenceScore: 85,\r\n      rulesUsed: 2,\r\n      matchesFound: 5,\r\n      patternsIdentified: ['temporal_sequence', 'ip_clustering']\r\n    };\r\n\r\n    eventData = {\r\n      oldStatus: CorrelationStatus.IN_PROGRESS,\r\n      newStatus: CorrelationStatus.COMPLETED,\r\n      result: mockResult,\r\n      correlationQualityScore: 85,\r\n      requiresManualReview: false,\r\n      changedAt: new Date(),\r\n      reason: 'Correlation completed successfully'\r\n    };\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create domain event with required data', () => {\r\n      const domainEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);\r\n\r\n      expect(domainEvent.aggregateId).toEqual(aggregateId);\r\n      expect(domainEvent.eventData).toEqual(eventData);\r\n      expect(domainEvent.eventName).toBe('CorrelatedEventStatusChangedDomainEvent');\r\n      expect(domainEvent.occurredOn).toBeInstanceOf(Date);\r\n      expect(domainEvent.eventId).toBeDefined();\r\n    });\r\n\r\n    it('should create domain event with custom options', () => {\r\n      const customEventId = UniqueEntityId.create();\r\n      const customOccurredOn = new Date('2023-01-01T00:00:00Z');\r\n      const correlationId = 'corr_123';\r\n\r\n      const domainEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData, {\r\n        eventId: customEventId,\r\n        occurredOn: customOccurredOn,\r\n        correlationId,\r\n        eventVersion: 2,\r\n        metadata: { custom: 'data' }\r\n      });\r\n\r\n      expect(domainEvent.eventId).toEqual(customEventId);\r\n      expect(domainEvent.occurredOn).toEqual(customOccurredOn);\r\n      expect(domainEvent.correlationId).toBe(correlationId);\r\n      expect(domainEvent.eventVersion).toBe(2);\r\n      expect(domainEvent.metadata).toEqual({ custom: 'data' });\r\n    });\r\n  });\r\n\r\n  describe('property getters', () => {\r\n    let domainEvent: CorrelatedEventStatusChangedDomainEvent;\r\n\r\n    beforeEach(() => {\r\n      domainEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);\r\n    });\r\n\r\n    it('should return old status', () => {\r\n      expect(domainEvent.oldStatus).toBe(eventData.oldStatus);\r\n    });\r\n\r\n    it('should return new status', () => {\r\n      expect(domainEvent.newStatus).toBe(eventData.newStatus);\r\n    });\r\n\r\n    it('should return correlation result', () => {\r\n      expect(domainEvent.result).toEqual(eventData.result);\r\n    });\r\n\r\n    it('should return correlation quality score', () => {\r\n      expect(domainEvent.correlationQualityScore).toBe(eventData.correlationQualityScore);\r\n    });\r\n\r\n    it('should return requires manual review flag', () => {\r\n      expect(domainEvent.requiresManualReview).toBe(eventData.requiresManualReview);\r\n    });\r\n\r\n    it('should return changed at timestamp', () => {\r\n      expect(domainEvent.changedAt).toEqual(eventData.changedAt);\r\n    });\r\n\r\n    it('should return reason', () => {\r\n      expect(domainEvent.reason).toBe(eventData.reason);\r\n    });\r\n\r\n    it('should return change metadata', () => {\r\n      const eventWithMetadata = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        changeMetadata: { custom: 'metadata' }\r\n      });\r\n\r\n      expect(eventWithMetadata.changeMetadata).toEqual({ custom: 'metadata' });\r\n    });\r\n\r\n    it('should return empty object for change metadata when not provided', () => {\r\n      expect(domainEvent.changeMetadata).toEqual({});\r\n    });\r\n\r\n    it('should use occurred on when changed at is not provided', () => {\r\n      const eventWithoutChangedAt = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        changedAt: undefined\r\n      });\r\n\r\n      expect(eventWithoutChangedAt.changedAt).toEqual(eventWithoutChangedAt.occurredOn);\r\n    });\r\n  });\r\n\r\n  describe('status transition detection', () => {\r\n    it('should detect completed transition', () => {\r\n      const completedEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.COMPLETED\r\n      });\r\n\r\n      expect(completedEvent.isCompletedTransition()).toBe(true);\r\n      expect(completedEvent.isFailedTransition()).toBe(false);\r\n      expect(completedEvent.isInProgressTransition()).toBe(false);\r\n    });\r\n\r\n    it('should detect failed transition', () => {\r\n      const failedEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.FAILED\r\n      });\r\n\r\n      expect(failedEvent.isFailedTransition()).toBe(true);\r\n      expect(failedEvent.isCompletedTransition()).toBe(false);\r\n    });\r\n\r\n    it('should detect in progress transition', () => {\r\n      const inProgressEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: CorrelationStatus.PENDING,\r\n        newStatus: CorrelationStatus.IN_PROGRESS\r\n      });\r\n\r\n      expect(inProgressEvent.isInProgressTransition()).toBe(true);\r\n      expect(inProgressEvent.isCompletedTransition()).toBe(false);\r\n    });\r\n\r\n    it('should detect partial transition', () => {\r\n      const partialEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.PARTIAL\r\n      });\r\n\r\n      expect(partialEvent.isPartialTransition()).toBe(true);\r\n      expect(partialEvent.isCompletedTransition()).toBe(false);\r\n    });\r\n\r\n    it('should detect skipped transition', () => {\r\n      const skippedEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.SKIPPED\r\n      });\r\n\r\n      expect(skippedEvent.isSkippedTransition()).toBe(true);\r\n      expect(skippedEvent.isCompletedTransition()).toBe(false);\r\n    });\r\n\r\n    it('should detect timeout transition', () => {\r\n      const timeoutEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.TIMEOUT\r\n      });\r\n\r\n      expect(timeoutEvent.isTimeoutTransition()).toBe(true);\r\n      expect(timeoutEvent.isFailedTransition()).toBe(false); // timeout is not considered failed\r\n    });\r\n  });\r\n\r\n  describe('success and failure detection', () => {\r\n    it('should detect successful completion', () => {\r\n      const successfulEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.COMPLETED,\r\n        result: { ...mockResult, success: true }\r\n      });\r\n\r\n      expect(successfulEvent.isSuccessfulCompletion()).toBe(true);\r\n    });\r\n\r\n    it('should not detect successful completion for failed result', () => {\r\n      const failedResultEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.COMPLETED,\r\n        result: { ...mockResult, success: false }\r\n      });\r\n\r\n      expect(failedResultEvent.isSuccessfulCompletion()).toBe(false);\r\n    });\r\n\r\n    it('should detect failure transitions', () => {\r\n      const failedEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.FAILED\r\n      });\r\n\r\n      const timeoutEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.TIMEOUT\r\n      });\r\n\r\n      const completedFailedEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.COMPLETED,\r\n        result: { ...mockResult, success: false }\r\n      });\r\n\r\n      expect(failedEvent.isFailureTransition()).toBe(true);\r\n      expect(timeoutEvent.isFailureTransition()).toBe(true);\r\n      expect(completedFailedEvent.isFailureTransition()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('quality and progress assessment', () => {\r\n    it('should detect quality improvement', () => {\r\n      const qualityEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        correlationQualityScore: 85\r\n      });\r\n\r\n      const lowQualityEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        correlationQualityScore: 50\r\n      });\r\n\r\n      const noQualityEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        correlationQualityScore: undefined\r\n      });\r\n\r\n      expect(qualityEvent.hasQualityImprovement()).toBe(true);\r\n      expect(lowQualityEvent.hasQualityImprovement()).toBe(false);\r\n      expect(noQualityEvent.hasQualityImprovement()).toBe(false);\r\n    });\r\n\r\n    it('should detect progress transitions', () => {\r\n      const progressEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: CorrelationStatus.PENDING,\r\n        newStatus: CorrelationStatus.IN_PROGRESS\r\n      });\r\n\r\n      const completionEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: CorrelationStatus.IN_PROGRESS,\r\n        newStatus: CorrelationStatus.COMPLETED\r\n      });\r\n\r\n      const failureEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: CorrelationStatus.IN_PROGRESS,\r\n        newStatus: CorrelationStatus.FAILED\r\n      });\r\n\r\n      expect(progressEvent.isProgressTransition()).toBe(true);\r\n      expect(completionEvent.isProgressTransition()).toBe(true);\r\n      expect(failureEvent.isProgressTransition()).toBe(false);\r\n    });\r\n\r\n    it('should detect regression transitions', () => {\r\n      const regressionEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: CorrelationStatus.COMPLETED,\r\n        newStatus: CorrelationStatus.PENDING\r\n      });\r\n\r\n      const progressEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: CorrelationStatus.PENDING,\r\n        newStatus: CorrelationStatus.IN_PROGRESS\r\n      });\r\n\r\n      expect(regressionEvent.isRegressionTransition()).toBe(true);\r\n      expect(progressEvent.isRegressionTransition()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('transition type classification', () => {\r\n    it('should classify transition types correctly', () => {\r\n      const completionEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.COMPLETED,\r\n        result: { ...mockResult, success: true }\r\n      });\r\n\r\n      const failureEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.FAILED\r\n      });\r\n\r\n      const progressEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: CorrelationStatus.PENDING,\r\n        newStatus: CorrelationStatus.IN_PROGRESS\r\n      });\r\n\r\n      const regressionEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: CorrelationStatus.COMPLETED,\r\n        newStatus: CorrelationStatus.PENDING\r\n      });\r\n\r\n      const neutralEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: CorrelationStatus.SKIPPED,\r\n        newStatus: CorrelationStatus.SKIPPED\r\n      });\r\n\r\n      expect(completionEvent.getTransitionType()).toBe('completion');\r\n      expect(failureEvent.getTransitionType()).toBe('failure');\r\n      expect(progressEvent.getTransitionType()).toBe('progress');\r\n      expect(regressionEvent.getTransitionType()).toBe('regression');\r\n      expect(neutralEvent.getTransitionType()).toBe('neutral');\r\n    });\r\n  });\r\n\r\n  describe('notification priority', () => {\r\n    it('should calculate high priority for successful completion with quality', () => {\r\n      const highPriorityEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.COMPLETED,\r\n        result: { ...mockResult, success: true },\r\n        correlationQualityScore: 85\r\n      });\r\n\r\n      expect(highPriorityEvent.getNotificationPriority()).toBe('high');\r\n    });\r\n\r\n    it('should calculate high priority for failure transitions', () => {\r\n      const failureEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.FAILED\r\n      });\r\n\r\n      expect(failureEvent.getNotificationPriority()).toBe('high');\r\n    });\r\n\r\n    it('should calculate medium priority for manual review requirements', () => {\r\n      const reviewEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        requiresManualReview: true\r\n      });\r\n\r\n      expect(reviewEvent.getNotificationPriority()).toBe('medium');\r\n    });\r\n\r\n    it('should calculate low priority for progress transitions', () => {\r\n      const progressEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: CorrelationStatus.PENDING,\r\n        newStatus: CorrelationStatus.IN_PROGRESS\r\n      });\r\n\r\n      expect(progressEvent.getNotificationPriority()).toBe('low');\r\n    });\r\n  });\r\n\r\n  describe('recommended actions', () => {\r\n    it('should recommend actions for successful completion', () => {\r\n      const successEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.COMPLETED,\r\n        result: { ...mockResult, success: true },\r\n        requiresManualReview: true\r\n      });\r\n\r\n      const actions = successEvent.getRecommendedActions();\r\n\r\n      expect(actions).toContain('Review correlation results');\r\n      expect(actions).toContain('Validate identified patterns');\r\n      expect(actions).toContain('Proceed to next processing stage');\r\n      expect(actions).toContain('Schedule manual review');\r\n    });\r\n\r\n    it('should recommend actions for failed completion', () => {\r\n      const failedEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.COMPLETED,\r\n        result: { ...mockResult, success: false }\r\n      });\r\n\r\n      const actions = failedEvent.getRecommendedActions();\r\n\r\n      expect(actions).toContain('Investigate correlation issues');\r\n      expect(actions).toContain('Review failed rules and errors');\r\n    });\r\n\r\n    it('should recommend actions for correlation failure', () => {\r\n      const failureEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.FAILED\r\n      });\r\n\r\n      const actions = failureEvent.getRecommendedActions();\r\n\r\n      expect(actions).toContain('Investigate correlation failure');\r\n      expect(actions).toContain('Review error logs and diagnostics');\r\n      expect(actions).toContain('Consider retry with adjusted parameters');\r\n      expect(actions).toContain('Escalate to correlation engine team');\r\n    });\r\n\r\n    it('should recommend actions for partial correlation', () => {\r\n      const partialEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.PARTIAL\r\n      });\r\n\r\n      const actions = partialEvent.getRecommendedActions();\r\n\r\n      expect(actions).toContain('Review partial correlation results');\r\n      expect(actions).toContain('Identify missing correlation data');\r\n      expect(actions).toContain('Consider additional correlation rules');\r\n      expect(actions).toContain('Evaluate if results are sufficient');\r\n    });\r\n\r\n    it('should recommend actions for timeout', () => {\r\n      const timeoutEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.TIMEOUT\r\n      });\r\n\r\n      const actions = timeoutEvent.getRecommendedActions();\r\n\r\n      expect(actions).toContain('Investigate correlation performance');\r\n      expect(actions).toContain('Review correlation rule complexity');\r\n      expect(actions).toContain('Consider increasing timeout limits');\r\n      expect(actions).toContain('Retry with optimized parameters');\r\n    });\r\n\r\n    it('should recommend actions for in progress status', () => {\r\n      const inProgressEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.IN_PROGRESS\r\n      });\r\n\r\n      const actions = inProgressEvent.getRecommendedActions();\r\n\r\n      expect(actions).toContain('Monitor correlation progress');\r\n      expect(actions).toContain('Track processing metrics');\r\n    });\r\n\r\n    it('should recommend actions for skipped status', () => {\r\n      const skippedEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.SKIPPED\r\n      });\r\n\r\n      const actions = skippedEvent.getRecommendedActions();\r\n\r\n      expect(actions).toContain('Review skip reason');\r\n      expect(actions).toContain('Validate skip criteria');\r\n    });\r\n  });\r\n\r\n  describe('metrics calculation', () => {\r\n    it('should calculate metrics for status transitions', () => {\r\n      const domainEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);\r\n      const metrics = domainEvent.getMetricsToUpdate();\r\n\r\n      const transitionMetric = metrics.find(m => m.metric === 'correlation_status_transitions');\r\n      expect(transitionMetric).toBeDefined();\r\n      expect(transitionMetric!.value).toBe(1);\r\n      expect(transitionMetric!.tags).toEqual({\r\n        from_status: CorrelationStatus.IN_PROGRESS,\r\n        to_status: CorrelationStatus.COMPLETED,\r\n        transition_type: 'completion'\r\n      });\r\n    });\r\n\r\n    it('should calculate quality score metrics', () => {\r\n      const domainEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);\r\n      const metrics = domainEvent.getMetricsToUpdate();\r\n\r\n      const qualityMetric = metrics.find(m => m.metric === 'correlation_quality_score');\r\n      expect(qualityMetric).toBeDefined();\r\n      expect(qualityMetric!.value).toBe(85);\r\n      expect(qualityMetric!.tags).toEqual({\r\n        status: CorrelationStatus.COMPLETED\r\n      });\r\n    });\r\n\r\n    it('should calculate success metrics for successful completion', () => {\r\n      const successEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        result: { ...mockResult, success: true }\r\n      });\r\n\r\n      const metrics = successEvent.getMetricsToUpdate();\r\n      const successMetric = metrics.find(m => m.metric === 'correlation_completions_successful');\r\n      expect(successMetric).toBeDefined();\r\n      expect(successMetric!.value).toBe(1);\r\n    });\r\n\r\n    it('should calculate failure metrics for failed transitions', () => {\r\n      const failureEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        newStatus: CorrelationStatus.FAILED\r\n      });\r\n\r\n      const metrics = failureEvent.getMetricsToUpdate();\r\n      const failureMetric = metrics.find(m => m.metric === 'correlation_completions_failed');\r\n      expect(failureMetric).toBeDefined();\r\n      expect(failureMetric!.value).toBe(1);\r\n      expect(failureMetric!.tags).toEqual({\r\n        failure_type: CorrelationStatus.FAILED\r\n      });\r\n    });\r\n\r\n    it('should calculate processing duration metrics', () => {\r\n      const domainEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);\r\n      const metrics = domainEvent.getMetricsToUpdate();\r\n\r\n      const durationMetric = metrics.find(m => m.metric === 'correlation_processing_duration_ms');\r\n      expect(durationMetric).toBeDefined();\r\n      expect(durationMetric!.value).toBe(1500);\r\n      expect(durationMetric!.tags).toEqual({\r\n        status: CorrelationStatus.COMPLETED\r\n      });\r\n    });\r\n\r\n    it('should calculate rules and matches metrics', () => {\r\n      const domainEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);\r\n      const metrics = domainEvent.getMetricsToUpdate();\r\n\r\n      const rulesMetric = metrics.find(m => m.metric === 'correlation_rules_applied');\r\n      const matchesMetric = metrics.find(m => m.metric === 'correlation_matches_found');\r\n      const patternsMetric = metrics.find(m => m.metric === 'correlation_patterns_identified');\r\n\r\n      expect(rulesMetric).toBeDefined();\r\n      expect(rulesMetric!.value).toBe(2);\r\n\r\n      expect(matchesMetric).toBeDefined();\r\n      expect(matchesMetric!.value).toBe(5);\r\n\r\n      expect(patternsMetric).toBeDefined();\r\n      expect(patternsMetric!.value).toBe(2);\r\n    });\r\n  });\r\n\r\n  describe('event summary', () => {\r\n    it('should provide comprehensive event summary', () => {\r\n      const domainEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);\r\n      const summary = domainEvent.getEventSummary();\r\n\r\n      expect(summary.correlatedEventId).toBe(aggregateId.toString());\r\n      expect(summary.oldStatus).toBe(eventData.oldStatus);\r\n      expect(summary.newStatus).toBe(eventData.newStatus);\r\n      expect(summary.correlationQualityScore).toBe(eventData.correlationQualityScore);\r\n      expect(summary.requiresManualReview).toBe(eventData.requiresManualReview);\r\n      expect(summary.changedAt).toEqual(eventData.changedAt);\r\n      expect(summary.reason).toBe(eventData.reason);\r\n      expect(summary.isCompletedTransition).toBe(true);\r\n      expect(summary.isFailedTransition).toBe(false);\r\n      expect(summary.isSuccessfulCompletion).toBe(true);\r\n      expect(summary.isFailureTransition).toBe(false);\r\n      expect(summary.hasQualityImprovement).toBe(true);\r\n      expect(summary.isProgressTransition).toBe(true);\r\n      expect(summary.isRegressionTransition).toBe(false);\r\n      expect(summary.transitionType).toBe('completion');\r\n      expect(summary.notificationPriority).toBe('high');\r\n      expect(summary.recommendedActions).toBeInstanceOf(Array);\r\n      expect(summary.metricsToUpdate).toBeInstanceOf(Array);\r\n      expect(summary.result).toEqual(eventData.result);\r\n    });\r\n  });\r\n\r\n  describe('JSON serialization', () => {\r\n    it('should serialize to JSON with event summary', () => {\r\n      const domainEvent = new CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);\r\n      const json = domainEvent.toJSON();\r\n\r\n      expect(json.eventName).toBe('CorrelatedEventStatusChangedDomainEvent');\r\n      expect(json.aggregateId).toBe(aggregateId.toString());\r\n      expect(json.eventData).toEqual(eventData);\r\n      expect(json.eventSummary).toBeDefined();\r\n      expect(json.occurredOn).toBeInstanceOf(Date);\r\n    });\r\n  });\r\n});"], "version": 3}