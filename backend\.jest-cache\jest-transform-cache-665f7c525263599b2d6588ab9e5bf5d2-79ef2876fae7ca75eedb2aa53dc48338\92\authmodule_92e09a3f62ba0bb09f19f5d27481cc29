c089285fb41cc8946d625096f9b2c85d
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const jwt_config_1 = require("../config/jwt.config");
const auth_service_1 = require("./auth.service");
const jwt_strategy_1 = require("./strategies/jwt.strategy");
const local_strategy_1 = require("./strategies/local.strategy");
const oauth_google_strategy_1 = require("./strategies/oauth-google.strategy");
const oauth_microsoft_strategy_1 = require("./strategies/oauth-microsoft.strategy");
const saml_strategy_1 = require("./strategies/saml.strategy");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const local_auth_guard_1 = require("./guards/local-auth.guard");
const roles_guard_1 = require("./guards/roles.guard");
const permissions_guard_1 = require("./guards/permissions.guard");
const session_guard_1 = require("./guards/session.guard");
const rbac_service_1 = require("./rbac/rbac.service");
const policy_engine_1 = require("./rbac/policy.engine");
const password_service_1 = require("./services/password.service");
const token_service_1 = require("./services/token.service");
const session_service_1 = require("./services/session.service");
const session_store_1 = require("./services/session.store");
const oauth_service_1 = require("./services/oauth.service");
const password_reset_service_1 = require("./services/password-reset.service");
const email_verification_service_1 = require("./services/email-verification.service");
// Import entities (these will be created later)
const user_entity_1 = require("../../modules/user-management/domain/entities/user.entity");
const role_entity_1 = require("../../modules/user-management/domain/entities/role.entity");
const refresh_token_entity_1 = require("../../modules/user-management/domain/entities/refresh-token.entity");
/**
 * Authentication and authorization module
 * Provides JWT-based authentication with RBAC (Role-Based Access Control)
 *
 * Features:
 * - JWT token authentication
 * - Local username/password authentication
 * - Role-based access control
 * - Permission-based authorization
 * - Session management
 * - Password hashing and validation
 * - Refresh token support
 *
 * @module AuthModule
 */
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            passport_1.PassportModule.register({ defaultStrategy: 'jwt' }),
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: async (configService) => {
                    const jwtConfig = configService.get('jwt');
                    const signOptions = jwt_config_1.JwtConfigHelper.getSignOptions(jwtConfig);
                    const secretOrKey = jwt_config_1.JwtConfigHelper.getSecretOrKey(jwtConfig, true);
                    return {
                        secret: secretOrKey,
                        privateKey: jwtConfig.privateKey,
                        publicKey: jwtConfig.publicKey,
                        signOptions,
                        verifyOptions: jwt_config_1.JwtConfigHelper.getVerifyOptions(jwtConfig),
                    };
                },
            }),
            typeorm_1.TypeOrmModule.forFeature([user_entity_1.User, role_entity_1.Role, refresh_token_entity_1.RefreshToken]),
        ],
        providers: [
            // Core authentication services
            auth_service_1.AuthService,
            password_service_1.PasswordService,
            token_service_1.TokenService,
            session_service_1.SessionService,
            session_store_1.SessionStore,
            oauth_service_1.OAuthService,
            password_reset_service_1.PasswordResetService,
            email_verification_service_1.EmailVerificationService,
            rbac_service_1.RbacService,
            policy_engine_1.PolicyEngine,
            // Passport strategies
            jwt_strategy_1.JwtStrategy,
            local_strategy_1.LocalStrategy,
            oauth_google_strategy_1.GoogleOAuthStrategy,
            oauth_microsoft_strategy_1.MicrosoftOAuthStrategy,
            saml_strategy_1.SamlStrategy,
            // Guards
            jwt_auth_guard_1.JwtAuthGuard,
            local_auth_guard_1.LocalAuthGuard,
            roles_guard_1.RolesGuard,
            permissions_guard_1.PermissionsGuard,
            session_guard_1.SessionGuard,
        ],
        exports: [
            auth_service_1.AuthService,
            password_service_1.PasswordService,
            token_service_1.TokenService,
            session_service_1.SessionService,
            session_store_1.SessionStore,
            oauth_service_1.OAuthService,
            password_reset_service_1.PasswordResetService,
            email_verification_service_1.EmailVerificationService,
            rbac_service_1.RbacService,
            policy_engine_1.PolicyEngine,
            jwt_auth_guard_1.JwtAuthGuard,
            local_auth_guard_1.LocalAuthGuard,
            roles_guard_1.RolesGuard,
            permissions_guard_1.PermissionsGuard,
            session_guard_1.SessionGuard,
            passport_1.PassportModule,
            jwt_1.JwtModule,
        ],
    })
], AuthModule);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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