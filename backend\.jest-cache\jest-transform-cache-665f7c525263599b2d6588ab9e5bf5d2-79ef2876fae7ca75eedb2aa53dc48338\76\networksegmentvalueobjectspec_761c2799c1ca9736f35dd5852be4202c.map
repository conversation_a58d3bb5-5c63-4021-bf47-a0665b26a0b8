{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\network-segment.value-object.spec.ts", "mappings": ";;AAAA,iFAAyG;AACzG,uEAAsD;AAEtD,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;IAC3C,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAE1D,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iDAAkB,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;YAEzD,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iDAAkB,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,OAAO,GAAG,6CAAc,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;YAC7G,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAc,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;YACjH,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAc,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QACjH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAc,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;YACzG,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAc,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAc,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;YACzG,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAc,CAAC,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;QAC5G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,qDAAqD;YACrD,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;QACtH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,aAAa,GAAG;gBACpB,gBAAgB;gBAChB,YAAY;gBACZ,eAAe;gBACf,WAAW;gBACX,kBAAkB;aACnB,CAAC;YAEF,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC3B,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAC5D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,OAAO,GAAG,6CAAc,CAAC,aAAa,EAAE,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,OAAO,GAAG,6CAAc,CAAC,aAAa,EAAE,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,OAAO,GAAG,6CAAc,CAAC,aAAa,EAAE,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,OAAO,GAAG,6CAAc,CAAC,SAAS,EAAE,CAAC;YAE3C,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,kDAAmB,CAAC,QAAQ,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,eAAe,GAAG;gBACtB,YAAY;gBACZ,eAAe;gBACf,gBAAgB;aACjB,CAAC;YAEF,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC7B,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,kDAAmB,CAAC,OAAO,CAAC,CAAC;gBAC7D,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,cAAc,GAAG;gBACrB,YAAY;gBACZ,YAAY;aACb,CAAC;YAEF,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC5B,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,kDAAmB,CAAC,MAAM,CAAC,CAAC;gBAC5D,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAEvD,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,kDAAmB,CAAC,QAAQ,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAEvD,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,kDAAmB,CAAC,SAAS,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAE1D,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YACtD,MAAM,GAAG,GAAG,mCAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAC7C,MAAM,GAAG,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,WAAW,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC9D,MAAM,WAAW,GAAG,aAAa,CAAC;YAElC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,SAAS,GAAG;gBAChB,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE;gBACjD,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE;gBAClD,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE;gBAC/C,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE;aAChD,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACvC,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,SAAS,GAAG;gBAChB,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,gCAAgC;gBACnF,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,EAAI,8BAA8B;gBACjF,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,EAAI,+BAA+B;gBAClF,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,EAAI,aAAa;aACjE,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACvC,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,SAAS,GAAG;gBAChB,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACrD,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE;gBAC7C,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,iBAAiB,EAAE;gBACvD,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE;aAC3C,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACvC,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,SAAS,GAAG;gBAChB,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,WAAW,EAAE;gBACjD,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACjD,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,SAAS,EAAE;aAChD,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACvC,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,SAAS,GAAG;gBAChB,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACrD,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,gBAAgB,EAAE;gBAClD,EAAE,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,eAAe,EAAE;aACxD,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACvC,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,SAAS,GAAG;gBAChB,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE;gBACnD,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAC5C,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE,iBAAiB;gBACtE,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE,aAAa;aACnE,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACvC,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,SAAS,GAAG;gBAChB,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACrD,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE;gBACnD,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE,iBAAiB;gBACtE,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE,aAAa;aACnE,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACvC,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;YAEzD,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;YACjG,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;YACrG,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,CAAC,OAAO,CAAC,uDAAuD,CAAC,CAAC;QAC/G,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC1D,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,6BAA6B;YAEhE,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAE1D,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,8DAA8D,CAAC,CAAC;YACxG,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,8DAA8D,CAAC,CAAC;YACxG,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,QAAQ,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC3D,MAAM,QAAQ,GAAG,6CAAc,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;YAC7D,MAAM,QAAQ,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAE3D,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,MAAM,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YACzD,MAAM,KAAK,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,6CAAc,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAEzD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,WAAW,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC9D,MAAM,WAAW,GAAG,6CAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;YAE7D,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YACtD,MAAM,IAAI,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;YAEvC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4CAA4C,CAAC,CAAC;YAC7E,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YACtD,MAAM,IAAI,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;YAEvC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,IAAI,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;YAEvC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;YAEvC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;YAEtC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iDAAkB,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;YACzD,MAAM,IAAI,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;YAEtC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iDAAkB,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,aAAa,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,aAAa,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAE1D,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,WAAW,GAAG,6CAAc,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAC3D,MAAM,WAAW,GAAG,6CAAc,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAExD,MAAM,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrD,MAAM,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,6CAAc,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACnD,MAAM,CAAC,6CAAc,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,CAAC,6CAAc,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,CAAC,6CAAc,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/D,MAAM,CAAC,6CAAc,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,CAAC,6CAAc,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,QAAQ,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC3D,MAAM,QAAQ,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC3D,MAAM,QAAQ,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC3D,MAAM,QAAQ,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAE3D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;YAE9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC;YACjE,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO;YACvE,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAE1D,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACrE,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAE1D,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACrE,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAc,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YACvF,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAc,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAc,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;QACvG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,OAAO,GAAG,6CAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;YAEzD,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,uCAAuC,CAAC,CAAC;YACjF,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC,OAAO,CAAC,oDAAoD,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\network-segment.value-object.spec.ts"], "sourcesContent": ["import { NetworkSegment, NetworkSegmentType, NetworkSegmentClass } from './network-segment.value-object';\r\nimport { IPAddress } from './ip-address.value-object';\r\n\r\ndescribe('NetworkSegment Value Object', () => {\r\n  describe('Creation and Validation', () => {\r\n    it('should create valid IPv4 network segment', () => {\r\n      const segment = NetworkSegment.fromCIDR('***********/24');\r\n      \r\n      expect(segment.networkAddress).toBe('***********');\r\n      expect(segment.prefixLength).toBe(24);\r\n      expect(segment.type).toBe(NetworkSegmentType.IPv4);\r\n      expect(segment.isIPv4()).toBe(true);\r\n      expect(segment.isIPv6()).toBe(false);\r\n    });\r\n\r\n    it('should create valid IPv6 network segment', () => {\r\n      const segment = NetworkSegment.fromCIDR('2001:db8::/32');\r\n      \r\n      expect(segment.networkAddress).toBe('2001:db8::');\r\n      expect(segment.prefixLength).toBe(32);\r\n      expect(segment.type).toBe(NetworkSegmentType.IPv6);\r\n      expect(segment.isIPv6()).toBe(true);\r\n      expect(segment.isIPv4()).toBe(false);\r\n    });\r\n\r\n    it('should create network segment with create method', () => {\r\n      const segment = NetworkSegment.create('10.0.0.0', 8);\r\n      \r\n      expect(segment.networkAddress).toBe('10.0.0.0');\r\n      expect(segment.prefixLength).toBe(8);\r\n    });\r\n\r\n    it('should validate CIDR format', () => {\r\n      expect(() => NetworkSegment.fromCIDR('invalid')).toThrow('CIDR notation must be in format \"address/prefix\"');\r\n      expect(() => NetworkSegment.fromCIDR('***********')).toThrow('CIDR notation must be in format \"address/prefix\"');\r\n      expect(() => NetworkSegment.fromCIDR('***********/invalid')).toThrow('Invalid prefix length in CIDR notation');\r\n    });\r\n\r\n    it('should validate prefix length range for IPv4', () => {\r\n      expect(() => NetworkSegment.create('***********', -1)).toThrow('Prefix length must be between 0 and 32');\r\n      expect(() => NetworkSegment.create('***********', 33)).toThrow('Prefix length must be between 0 and 32');\r\n    });\r\n\r\n    it('should validate prefix length range for IPv6', () => {\r\n      expect(() => NetworkSegment.create('2001:db8::', -1)).toThrow('Prefix length must be between 0 and 128');\r\n      expect(() => NetworkSegment.create('2001:db8::', 129)).toThrow('Prefix length must be between 0 and 128');\r\n    });\r\n\r\n    it('should validate network address alignment', () => {\r\n      // *********** is not a valid network address for /24\r\n      expect(() => NetworkSegment.fromCIDR('***********/24')).toThrow('Invalid network address. Expected ***********/24');\r\n    });\r\n\r\n    it('should accept valid network addresses', () => {\r\n      const validNetworks = [\r\n        '***********/24',\r\n        '10.0.0.0/8',\r\n        '**********/12',\r\n        '0.0.0.0/0',\r\n        '***********28/25',\r\n      ];\r\n\r\n      validNetworks.forEach(cidr => {\r\n        expect(() => NetworkSegment.fromCIDR(cidr)).not.toThrow();\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Predefined Network Segments', () => {\r\n    it('should create private class A network', () => {\r\n      const segment = NetworkSegment.privateClassA();\r\n      \r\n      expect(segment.toCIDR()).toBe('10.0.0.0/8');\r\n      expect(segment.isPrivate()).toBe(true);\r\n    });\r\n\r\n    it('should create private class B network', () => {\r\n      const segment = NetworkSegment.privateClassB();\r\n      \r\n      expect(segment.toCIDR()).toBe('**********/12');\r\n      expect(segment.isPrivate()).toBe(true);\r\n    });\r\n\r\n    it('should create private class C network', () => {\r\n      const segment = NetworkSegment.privateClassC();\r\n      \r\n      expect(segment.toCIDR()).toBe('***********/16');\r\n      expect(segment.isPrivate()).toBe(true);\r\n    });\r\n\r\n    it('should create localhost network', () => {\r\n      const segment = NetworkSegment.localhost();\r\n      \r\n      expect(segment.toCIDR()).toBe('*********/8');\r\n      expect(segment.classify()).toBe(NetworkSegmentClass.LOOPBACK);\r\n    });\r\n  });\r\n\r\n  describe('Network Classification', () => {\r\n    it('should classify private networks', () => {\r\n      const privateNetworks = [\r\n        '10.0.0.0/8',\r\n        '**********/12',\r\n        '***********/16',\r\n      ];\r\n\r\n      privateNetworks.forEach(cidr => {\r\n        const segment = NetworkSegment.fromCIDR(cidr);\r\n        expect(segment.classify()).toBe(NetworkSegmentClass.PRIVATE);\r\n        expect(segment.isPrivate()).toBe(true);\r\n        expect(segment.isPublic()).toBe(false);\r\n      });\r\n    });\r\n\r\n    it('should classify public networks', () => {\r\n      const publicNetworks = [\r\n        '*******/24',\r\n        '*******/24',\r\n      ];\r\n\r\n      publicNetworks.forEach(cidr => {\r\n        const segment = NetworkSegment.fromCIDR(cidr);\r\n        expect(segment.classify()).toBe(NetworkSegmentClass.PUBLIC);\r\n        expect(segment.isPublic()).toBe(true);\r\n        expect(segment.isPrivate()).toBe(false);\r\n      });\r\n    });\r\n\r\n    it('should classify loopback networks', () => {\r\n      const segment = NetworkSegment.fromCIDR('*********/8');\r\n      \r\n      expect(segment.classify()).toBe(NetworkSegmentClass.LOOPBACK);\r\n    });\r\n\r\n    it('should classify multicast networks', () => {\r\n      const segment = NetworkSegment.fromCIDR('*********/4');\r\n      \r\n      expect(segment.classify()).toBe(NetworkSegmentClass.MULTICAST);\r\n    });\r\n  });\r\n\r\n  describe('IP Address Membership', () => {\r\n    it('should check if IP address belongs to network', () => {\r\n      const segment = NetworkSegment.fromCIDR('***********/24');\r\n      \r\n      expect(segment.contains('***********')).toBe(true);\r\n      expect(segment.contains('***********00')).toBe(true);\r\n      expect(segment.contains('***********55')).toBe(true);\r\n      expect(segment.contains('***********')).toBe(false);\r\n      expect(segment.contains('********')).toBe(false);\r\n    });\r\n\r\n    it('should check IP address membership with IPAddress objects', () => {\r\n      const segment = NetworkSegment.fromCIDR('10.0.0.0/8');\r\n      const ip1 = IPAddress.fromString('********');\r\n      const ip2 = IPAddress.fromString('***********');\r\n      \r\n      expect(segment.contains(ip1)).toBe(true);\r\n      expect(segment.contains(ip2)).toBe(false);\r\n    });\r\n\r\n    it('should handle type mismatches', () => {\r\n      const ipv4Segment = NetworkSegment.fromCIDR('***********/24');\r\n      const ipv6Address = '2001:db8::1';\r\n      \r\n      expect(ipv4Segment.contains(ipv6Address)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Network Calculations', () => {\r\n    it('should calculate total addresses for IPv4', () => {\r\n      const testCases = [\r\n        { cidr: '***********/24', expected: BigInt(256) },\r\n        { cidr: '10.0.0.0/8', expected: BigInt(16777216) },\r\n        { cidr: '***********/30', expected: BigInt(4) },\r\n        { cidr: '***********/32', expected: BigInt(1) },\r\n      ];\r\n\r\n      testCases.forEach(({ cidr, expected }) => {\r\n        const segment = NetworkSegment.fromCIDR(cidr);\r\n        expect(segment.getTotalAddresses()).toBe(expected);\r\n      });\r\n    });\r\n\r\n    it('should calculate usable addresses for IPv4', () => {\r\n      const testCases = [\r\n        { cidr: '***********/24', expected: BigInt(254) }, // 256 - 2 (network + broadcast)\r\n        { cidr: '***********/30', expected: BigInt(2) },   // 4 - 2 (network + broadcast)\r\n        { cidr: '***********/31', expected: BigInt(2) },   // Point-to-point, no broadcast\r\n        { cidr: '***********/32', expected: BigInt(1) },   // Host route\r\n      ];\r\n\r\n      testCases.forEach(({ cidr, expected }) => {\r\n        const segment = NetworkSegment.fromCIDR(cidr);\r\n        expect(segment.getUsableAddresses()).toBe(expected);\r\n      });\r\n    });\r\n\r\n    it('should calculate subnet mask for IPv4', () => {\r\n      const testCases = [\r\n        { cidr: '***********/24', expected: '*************' },\r\n        { cidr: '10.0.0.0/8', expected: '*********' },\r\n        { cidr: '***********/30', expected: '***************' },\r\n        { cidr: '0.0.0.0/0', expected: '0.0.0.0' },\r\n      ];\r\n\r\n      testCases.forEach(({ cidr, expected }) => {\r\n        const segment = NetworkSegment.fromCIDR(cidr);\r\n        expect(segment.getSubnetMask()).toBe(expected);\r\n      });\r\n    });\r\n\r\n    it('should calculate wildcard mask for IPv4', () => {\r\n      const testCases = [\r\n        { cidr: '***********/24', expected: '*********' },\r\n        { cidr: '10.0.0.0/8', expected: '*************' },\r\n        { cidr: '***********/30', expected: '*******' },\r\n      ];\r\n\r\n      testCases.forEach(({ cidr, expected }) => {\r\n        const segment = NetworkSegment.fromCIDR(cidr);\r\n        expect(segment.getWildcardMask()).toBe(expected);\r\n      });\r\n    });\r\n\r\n    it('should calculate broadcast address for IPv4', () => {\r\n      const testCases = [\r\n        { cidr: '***********/24', expected: '***********55' },\r\n        { cidr: '10.0.0.0/8', expected: '1*************' },\r\n        { cidr: '***********28/25', expected: '***********55' },\r\n      ];\r\n\r\n      testCases.forEach(({ cidr, expected }) => {\r\n        const segment = NetworkSegment.fromCIDR(cidr);\r\n        expect(segment.getBroadcastAddress().toString()).toBe(expected);\r\n      });\r\n    });\r\n\r\n    it('should calculate first host address', () => {\r\n      const testCases = [\r\n        { cidr: '***********/24', expected: '***********' },\r\n        { cidr: '10.0.0.0/8', expected: '********' },\r\n        { cidr: '***********/31', expected: '***********' }, // Point-to-point\r\n        { cidr: '***********/32', expected: '***********' }, // Host route\r\n      ];\r\n\r\n      testCases.forEach(({ cidr, expected }) => {\r\n        const segment = NetworkSegment.fromCIDR(cidr);\r\n        expect(segment.getFirstHostAddress().toString()).toBe(expected);\r\n      });\r\n    });\r\n\r\n    it('should calculate last host address', () => {\r\n      const testCases = [\r\n        { cidr: '***********/24', expected: '*************' },\r\n        { cidr: '***********/30', expected: '***********' },\r\n        { cidr: '***********/31', expected: '***********' }, // Point-to-point\r\n        { cidr: '***********/32', expected: '***********' }, // Host route\r\n      ];\r\n\r\n      testCases.forEach(({ cidr, expected }) => {\r\n        const segment = NetworkSegment.fromCIDR(cidr);\r\n        expect(segment.getLastHostAddress().toString()).toBe(expected);\r\n      });\r\n    });\r\n\r\n    it('should throw error for IPv6 operations not implemented', () => {\r\n      const segment = NetworkSegment.fromCIDR('2001:db8::/32');\r\n      \r\n      expect(() => segment.getSubnetMask()).toThrow('Subnet mask is only applicable to IPv4 networks');\r\n      expect(() => segment.getWildcardMask()).toThrow('Wildcard mask is only applicable to IPv4 networks');\r\n      expect(() => segment.getBroadcastAddress()).toThrow('Broadcast address is only applicable to IPv4 networks');\r\n    });\r\n  });\r\n\r\n  describe('Subnet Operations', () => {\r\n    it('should split network into smaller subnets', () => {\r\n      const segment = NetworkSegment.fromCIDR('***********/24');\r\n      const subnets = segment.split(26); // Split /24 into /26 subnets\r\n      \r\n      expect(subnets).toHaveLength(4);\r\n      expect(subnets[0].toCIDR()).toBe('***********/26');\r\n      expect(subnets[1].toCIDR()).toBe('************/26');\r\n      expect(subnets[2].toCIDR()).toBe('***********28/26');\r\n      expect(subnets[3].toCIDR()).toBe('***********92/26');\r\n    });\r\n\r\n    it('should validate subnet splitting parameters', () => {\r\n      const segment = NetworkSegment.fromCIDR('***********/24');\r\n      \r\n      expect(() => segment.split(24)).toThrow('New prefix length must be greater than current prefix length');\r\n      expect(() => segment.split(23)).toThrow('New prefix length must be greater than current prefix length');\r\n      expect(() => segment.split(33)).toThrow('New prefix length cannot exceed 32');\r\n    });\r\n\r\n    it('should check network overlap', () => {\r\n      const segment1 = NetworkSegment.fromCIDR('***********/24');\r\n      const segment2 = NetworkSegment.fromCIDR('***********28/25');\r\n      const segment3 = NetworkSegment.fromCIDR('***********/24');\r\n      \r\n      expect(segment1.overlaps(segment2)).toBe(true);\r\n      expect(segment1.overlaps(segment3)).toBe(false);\r\n    });\r\n\r\n    it('should check subnet relationships', () => {\r\n      const parent = NetworkSegment.fromCIDR('***********/16');\r\n      const child = NetworkSegment.fromCIDR('***********/24');\r\n      const unrelated = NetworkSegment.fromCIDR('10.0.0.0/24');\r\n      \r\n      expect(child.isSubnetOf(parent)).toBe(true);\r\n      expect(parent.isSupernetOf(child)).toBe(true);\r\n      expect(child.isSubnetOf(unrelated)).toBe(false);\r\n      expect(unrelated.isSupernetOf(child)).toBe(false);\r\n    });\r\n\r\n    it('should handle type mismatches in network operations', () => {\r\n      const ipv4Segment = NetworkSegment.fromCIDR('***********/24');\r\n      const ipv6Segment = NetworkSegment.fromCIDR('2001:db8::/32');\r\n      \r\n      expect(ipv4Segment.overlaps(ipv6Segment)).toBe(false);\r\n      expect(ipv4Segment.isSubnetOf(ipv6Segment)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Security Assessment', () => {\r\n    it('should assess public network security risk', () => {\r\n      const segment = NetworkSegment.fromCIDR('*******/24');\r\n      const risk = segment.getSecurityRisk();\r\n      \r\n      expect(risk.riskLevel).toBe('high');\r\n      expect(risk.reasons).toContain('Public network segment exposed to internet');\r\n      expect(risk.recommendations).toContain('Implement strict firewall rules');\r\n    });\r\n\r\n    it('should assess large network security risk', () => {\r\n      const segment = NetworkSegment.fromCIDR('10.0.0.0/8');\r\n      const risk = segment.getSecurityRisk();\r\n      \r\n      expect(risk.reasons).toContain('Very large network segment');\r\n      expect(risk.recommendations).toContain('Consider network segmentation');\r\n    });\r\n\r\n    it('should assess broad network range risk', () => {\r\n      const segment = NetworkSegment.fromCIDR('0.0.0.0/0');\r\n      const risk = segment.getSecurityRisk();\r\n      \r\n      expect(risk.reasons).toContain('Very broad network range');\r\n      expect(risk.recommendations).toContain('Implement network segmentation');\r\n    });\r\n\r\n    it('should provide standard assessment for normal networks', () => {\r\n      const segment = NetworkSegment.fromCIDR('***********/24');\r\n      const risk = segment.getSecurityRisk();\r\n      \r\n      expect(risk.riskLevel).toBe('low');\r\n      expect(risk.reasons).toContain('Standard network segment');\r\n    });\r\n  });\r\n\r\n  describe('Network Information', () => {\r\n    it('should provide comprehensive network information for IPv4', () => {\r\n      const segment = NetworkSegment.fromCIDR('***********/24');\r\n      const info = segment.getNetworkInfo();\r\n      \r\n      expect(info.cidr).toBe('***********/24');\r\n      expect(info.type).toBe(NetworkSegmentType.IPv4);\r\n      expect(info.networkAddress).toBe('***********');\r\n      expect(info.prefixLength).toBe(24);\r\n      expect(info.totalAddresses).toBe('256');\r\n      expect(info.usableAddresses).toBe('254');\r\n      expect(info.subnetMask).toBe('*************');\r\n      expect(info.wildcardMask).toBe('*********');\r\n      expect(info.broadcastAddress).toBe('***********55');\r\n      expect(info.firstHost).toBe('***********');\r\n      expect(info.lastHost).toBe('*************');\r\n      expect(info.isPrivate).toBe(true);\r\n      expect(info.isPublic).toBe(false);\r\n    });\r\n\r\n    it('should provide limited information for IPv6', () => {\r\n      const segment = NetworkSegment.fromCIDR('2001:db8::/32');\r\n      const info = segment.getNetworkInfo();\r\n      \r\n      expect(info.cidr).toBe('2001:db8::/32');\r\n      expect(info.type).toBe(NetworkSegmentType.IPv6);\r\n      expect(info.subnetMask).toBeUndefined();\r\n      expect(info.broadcastAddress).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('String Operations', () => {\r\n    it('should convert to CIDR string', () => {\r\n      const segment = NetworkSegment.fromCIDR('***********/24');\r\n      \r\n      expect(segment.toCIDR()).toBe('***********/24');\r\n      expect(segment.toString()).toBe('***********/24');\r\n    });\r\n\r\n    it('should parse various input formats', () => {\r\n      const cidrSegment = NetworkSegment.parse('***********/24');\r\n      const hostSegment = NetworkSegment.parse('***********');\r\n      \r\n      expect(cidrSegment?.toCIDR()).toBe('***********/24');\r\n      expect(hostSegment?.toCIDR()).toBe('***********/32');\r\n    });\r\n\r\n    it('should return null for invalid parse input', () => {\r\n      expect(NetworkSegment.parse('invalid')).toBeNull();\r\n      expect(NetworkSegment.parse('')).toBeNull();\r\n    });\r\n\r\n    it('should validate CIDR format', () => {\r\n      expect(NetworkSegment.isValidCIDR('***********/24')).toBe(true);\r\n      expect(NetworkSegment.isValidCIDR('2001:db8::/32')).toBe(true);\r\n      expect(NetworkSegment.isValidCIDR('invalid')).toBe(false);\r\n      expect(NetworkSegment.isValidCIDR('***********')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Equality and Comparison', () => {\r\n    it('should compare network segments for equality', () => {\r\n      const segment1 = NetworkSegment.fromCIDR('***********/24');\r\n      const segment2 = NetworkSegment.fromCIDR('***********/24');\r\n      const segment3 = NetworkSegment.fromCIDR('***********/24');\r\n      const segment4 = NetworkSegment.fromCIDR('***********/25');\r\n      \r\n      expect(segment1.equals(segment2)).toBe(true);\r\n      expect(segment1.equals(segment3)).toBe(false);\r\n      expect(segment1.equals(segment4)).toBe(false);\r\n      expect(segment1.equals(undefined)).toBe(false);\r\n    });\r\n\r\n    it('should handle same instance comparison', () => {\r\n      const segment = NetworkSegment.fromCIDR('***********/24');\r\n      expect(segment.equals(segment)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('JSON Serialization', () => {\r\n    it('should serialize to JSON', () => {\r\n      const segment = NetworkSegment.fromCIDR('***********/24');\r\n      const json = segment.toJSON();\r\n      \r\n      expect(json.cidr).toBe('***********/24');\r\n      expect(json.networkAddress).toBe('***********');\r\n      expect(json.prefixLength).toBe(24);\r\n      expect(json.securityRisk).toBeDefined();\r\n      expect(json.securityRisk.riskLevel).toBeDefined();\r\n    });\r\n\r\n    it('should deserialize from JSON', () => {\r\n      const json = { networkAddress: '***********', prefixLength: 24 };\r\n      const segment = NetworkSegment.fromJSON(json);\r\n      \r\n      expect(segment.toCIDR()).toBe('***********/24');\r\n    });\r\n  });\r\n\r\n  describe('Edge Cases', () => {\r\n    it('should handle /0 network (default route)', () => {\r\n      const segment = NetworkSegment.fromCIDR('0.0.0.0/0');\r\n      \r\n      expect(segment.getTotalAddresses()).toBe(BigInt('4294967296')); // 2^32\r\n      expect(segment.getSubnetMask()).toBe('0.0.0.0');\r\n    });\r\n\r\n    it('should handle /32 host route', () => {\r\n      const segment = NetworkSegment.fromCIDR('***********/32');\r\n      \r\n      expect(segment.getTotalAddresses()).toBe(BigInt(1));\r\n      expect(segment.getUsableAddresses()).toBe(BigInt(1));\r\n      expect(segment.getFirstHostAddress().toString()).toBe('***********');\r\n      expect(segment.getLastHostAddress().toString()).toBe('***********');\r\n    });\r\n\r\n    it('should handle /31 point-to-point network', () => {\r\n      const segment = NetworkSegment.fromCIDR('***********/31');\r\n      \r\n      expect(segment.getTotalAddresses()).toBe(BigInt(2));\r\n      expect(segment.getUsableAddresses()).toBe(BigInt(2));\r\n      expect(segment.getFirstHostAddress().toString()).toBe('***********');\r\n      expect(segment.getLastHostAddress().toString()).toBe('***********');\r\n    });\r\n\r\n    it('should handle empty network address validation', () => {\r\n      expect(() => NetworkSegment.create('', 24)).toThrow('Network address cannot be empty');\r\n      expect(() => NetworkSegment.create('   ', 24)).toThrow('Network address cannot be empty');\r\n    });\r\n\r\n    it('should handle non-integer prefix length', () => {\r\n      expect(() => NetworkSegment.create('***********', 24.5)).toThrow('Prefix length must be an integer');\r\n    });\r\n\r\n    it('should handle IPv6 operations not implemented', () => {\r\n      const segment = NetworkSegment.fromCIDR('2001:db8::/32');\r\n      \r\n      expect(() => segment.split(64)).toThrow('IPv6 subnet splitting not implemented');\r\n      expect(() => segment.getLastHostAddress()).toThrow('IPv6 last host address calculation not implemented');\r\n    });\r\n  });\r\n});"], "version": 3}