91322f7f8eb75275e655233a8e907246
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const security_policy_1 = require("../security-policy");
const tenant_id_value_object_1 = require("../../../../../shared-kernel/value-objects/tenant-id.value-object");
const user_id_value_object_1 = require("../../../../../shared-kernel/value-objects/user-id.value-object");
const timestamp_value_object_1 = require("../../../../../shared-kernel/value-objects/timestamp.value-object");
const validation_exception_1 = require("../../../../../shared-kernel/exceptions/validation.exception");
describe('SecurityPolicy', () => {
    let tenantId;
    let userId;
    let validRule;
    let validContext;
    beforeEach(() => {
        tenantId = tenant_id_value_object_1.TenantId.create('tenant-123');
        userId = user_id_value_object_1.UserId.create('user-123');
        validRule = {
            id: 'rule-1',
            name: 'Test Rule',
            description: 'A test rule',
            condition: '{"field":"eventData.type","operator":"equals","value":"login_failed"}',
            action: {
                type: security_policy_1.SecurityActionType.ALERT_ADMIN,
                parameters: { message: 'Alert!' },
                autoExecute: true,
                requiresApproval: false
            },
            severity: security_policy_1.SecurityRuleSeverity.HIGH,
            enabled: true,
            priority: 80
        };
        validContext = {
            eventData: {
                type: 'login_failed',
                attempts: 3,
                sourceIp: '*************'
            },
            userContext: {
                userId,
                tenantId,
                roles: ['user'],
                permissions: ['read']
            },
            systemContext: {
                timestamp: timestamp_value_object_1.Timestamp.now(),
                sourceIp: '*************',
                userAgent: 'Mozilla/5.0'
            }
        };
    });
    describe('creation', () => {
        it('should create a valid security policy', () => {
            const policy = security_policy_1.SecurityPolicy.create({
                name: 'Test Policy',
                description: 'A test policy',
                version: '1.0.0',
                tenantId,
                rules: [validRule],
                enabled: true,
                createdBy: userId
            });
            expect(policy).toBeDefined();
            expect(policy.name).toBe('Test Policy');
            expect(policy.description).toBe('A test policy');
            expect(policy.version).toBe('1.0.0');
            expect(policy.tenantId).toBe(tenantId);
            expect(policy.enabled).toBe(true);
            expect(policy.rules).toHaveLength(1);
            expect(policy.createdBy).toBe(userId);
            expect(policy.lastModifiedBy).toBe(userId);
        });
        it('should throw validation exception for missing name', () => {
            expect(() => {
                security_policy_1.SecurityPolicy.create({
                    name: '',
                    description: 'A test policy',
                    version: '1.0.0',
                    tenantId,
                    rules: [],
                    enabled: true,
                    createdBy: userId
                });
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should throw validation exception for missing description', () => {
            expect(() => {
                security_policy_1.SecurityPolicy.create({
                    name: 'Test Policy',
                    description: '',
                    version: '1.0.0',
                    tenantId,
                    rules: [],
                    enabled: true,
                    createdBy: userId
                });
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should throw validation exception for invalid rule', () => {
            const invalidRule = { ...validRule, condition: 'invalid json' };
            expect(() => {
                security_policy_1.SecurityPolicy.create({
                    name: 'Test Policy',
                    description: 'A test policy',
                    version: '1.0.0',
                    tenantId,
                    rules: [invalidRule],
                    enabled: true,
                    createdBy: userId
                });
            }).toThrow(validation_exception_1.ValidationException);
        });
    });
    describe('rule evaluation', () => {
        let policy;
        beforeEach(() => {
            policy = security_policy_1.SecurityPolicy.create({
                name: 'Test Policy',
                description: 'A test policy',
                version: '1.0.0',
                tenantId,
                rules: [validRule],
                enabled: true,
                createdBy: userId
            });
        });
        it('should evaluate rule and return match when condition is met', () => {
            const results = policy.evaluate(validContext);
            expect(results).toHaveLength(1);
            expect(results[0].matched).toBe(true);
            expect(results[0].ruleId).toBe('rule-1');
            expect(results[0].action).toBeDefined();
            expect(results[0].confidence).toBeGreaterThan(0);
        });
        it('should evaluate rule and return no match when condition is not met', () => {
            const contextWithDifferentType = {
                ...validContext,
                eventData: { ...validContext.eventData, type: 'login_success' }
            };
            const results = policy.evaluate(contextWithDifferentType);
            expect(results).toHaveLength(1);
            expect(results[0].matched).toBe(false);
            expect(results[0].action).toBeUndefined();
            expect(results[0].confidence).toBe(0);
        });
        it('should not evaluate rules when policy is disabled', () => {
            policy.disable(userId);
            const results = policy.evaluate(validContext);
            expect(results).toHaveLength(0);
        });
        it('should not evaluate disabled rules', () => {
            const disabledRule = { ...validRule, id: 'rule-2', enabled: false };
            policy.addRule(disabledRule, userId);
            const results = policy.evaluate(validContext);
            expect(results).toHaveLength(1); // Only the enabled rule
            expect(results[0].ruleId).toBe('rule-1');
        });
        it('should evaluate rules in priority order', () => {
            const highPriorityRule = {
                ...validRule,
                id: 'rule-high',
                priority: 100,
                severity: security_policy_1.SecurityRuleSeverity.HIGH
            };
            const lowPriorityRule = {
                ...validRule,
                id: 'rule-low',
                priority: 10,
                severity: security_policy_1.SecurityRuleSeverity.LOW
            };
            policy.addRule(highPriorityRule, userId);
            policy.addRule(lowPriorityRule, userId);
            const results = policy.evaluate(validContext);
            expect(results).toHaveLength(3);
            expect(results[0].ruleId).toBe('rule-high'); // Highest priority first
            expect(results[1].ruleId).toBe('rule-1'); // Medium priority
            expect(results[2].ruleId).toBe('rule-low'); // Lowest priority
        });
        it('should stop evaluation after critical rule match', () => {
            const criticalRule = {
                ...validRule,
                id: 'rule-critical',
                priority: 100,
                severity: security_policy_1.SecurityRuleSeverity.CRITICAL
            };
            const normalRule = {
                ...validRule,
                id: 'rule-normal',
                priority: 50,
                severity: security_policy_1.SecurityRuleSeverity.MEDIUM
            };
            policy.addRule(criticalRule, userId);
            policy.addRule(normalRule, userId);
            const results = policy.evaluate(validContext);
            // Should stop after critical rule matches
            expect(results).toHaveLength(1);
            expect(results[0].ruleId).toBe('rule-critical');
        });
    });
    describe('complex rule conditions', () => {
        let policy;
        beforeEach(() => {
            policy = security_policy_1.SecurityPolicy.create({
                name: 'Test Policy',
                description: 'A test policy',
                version: '1.0.0',
                tenantId,
                rules: [],
                enabled: true,
                createdBy: userId
            });
        });
        it('should evaluate AND conditions correctly', () => {
            const andRule = {
                ...validRule,
                id: 'and-rule',
                condition: JSON.stringify({
                    and: [
                        { field: 'eventData.type', operator: 'equals', value: 'login_failed' },
                        { field: 'eventData.attempts', operator: 'greater_than', value: 2 }
                    ]
                })
            };
            policy.addRule(andRule, userId);
            const results = policy.evaluate(validContext);
            expect(results[0].matched).toBe(true);
        });
        it('should evaluate OR conditions correctly', () => {
            const orRule = {
                ...validRule,
                id: 'or-rule',
                condition: JSON.stringify({
                    or: [
                        { field: 'eventData.type', operator: 'equals', value: 'login_failed' },
                        { field: 'eventData.type', operator: 'equals', value: 'password_reset' }
                    ]
                })
            };
            policy.addRule(orRule, userId);
            const results = policy.evaluate(validContext);
            expect(results[0].matched).toBe(true);
        });
        it('should evaluate NOT conditions correctly', () => {
            const notRule = {
                ...validRule,
                id: 'not-rule',
                condition: JSON.stringify({
                    not: {
                        field: 'eventData.type',
                        operator: 'equals',
                        value: 'login_success'
                    }
                })
            };
            policy.addRule(notRule, userId);
            const results = policy.evaluate(validContext);
            expect(results[0].matched).toBe(true);
        });
        it('should evaluate nested conditions correctly', () => {
            const nestedRule = {
                ...validRule,
                id: 'nested-rule',
                condition: JSON.stringify({
                    and: [
                        {
                            or: [
                                { field: 'eventData.type', operator: 'equals', value: 'login_failed' },
                                { field: 'eventData.type', operator: 'equals', value: 'password_reset' }
                            ]
                        },
                        { field: 'eventData.attempts', operator: 'greater_than', value: 1 }
                    ]
                })
            };
            policy.addRule(nestedRule, userId);
            const results = policy.evaluate(validContext);
            expect(results[0].matched).toBe(true);
        });
        it('should handle various field operators', () => {
            const testCases = [
                { operator: 'contains', field: 'eventData.sourceIp', value: '192.168', expected: true },
                { operator: 'starts_with', field: 'eventData.sourceIp', value: '192', expected: true },
                { operator: 'ends_with', field: 'eventData.sourceIp', value: '100', expected: true },
                { operator: 'in', field: 'eventData.type', value: ['login_failed', 'login_success'], expected: true },
                { operator: 'regex', field: 'eventData.sourceIp', value: '^192\\.168\\.\\d+\\.\\d+$', expected: true }
            ];
            testCases.forEach(({ operator, field, value, expected }, index) => {
                const rule = {
                    ...validRule,
                    id: `test-rule-${index}`,
                    condition: JSON.stringify({ field, operator, value })
                };
                policy.addRule(rule, userId);
                const results = policy.evaluate(validContext);
                const result = results.find(r => r.ruleId === `test-rule-${index}`);
                expect(result?.matched).toBe(expected);
            });
        });
    });
    describe('rule management', () => {
        let policy;
        beforeEach(() => {
            policy = security_policy_1.SecurityPolicy.create({
                name: 'Test Policy',
                description: 'A test policy',
                version: '1.0.0',
                tenantId,
                rules: [validRule],
                enabled: true,
                createdBy: userId
            });
        });
        it('should add a new rule', () => {
            const newRule = { ...validRule, id: 'rule-2', name: 'New Rule' };
            policy.addRule(newRule, userId);
            expect(policy.rules).toHaveLength(2);
            expect(policy.rules.find(r => r.id === 'rule-2')).toBeDefined();
        });
        it('should throw error when adding duplicate rule ID', () => {
            expect(() => {
                policy.addRule(validRule, userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should update an existing rule', () => {
            policy.updateRule('rule-1', { name: 'Updated Rule' }, userId);
            const updatedRule = policy.rules.find(r => r.id === 'rule-1');
            expect(updatedRule?.name).toBe('Updated Rule');
        });
        it('should throw error when updating non-existent rule', () => {
            expect(() => {
                policy.updateRule('non-existent', { name: 'Updated' }, userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should remove a rule', () => {
            policy.removeRule('rule-1', userId);
            expect(policy.rules).toHaveLength(0);
        });
        it('should throw error when removing non-existent rule', () => {
            expect(() => {
                policy.removeRule('non-existent', userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
    });
    describe('policy state management', () => {
        let policy;
        beforeEach(() => {
            policy = security_policy_1.SecurityPolicy.create({
                name: 'Test Policy',
                description: 'A test policy',
                version: '1.0.0',
                tenantId,
                rules: [validRule],
                enabled: true,
                createdBy: userId
            });
        });
        it('should enable policy', () => {
            policy.disable(userId);
            expect(policy.enabled).toBe(false);
            policy.enable(userId);
            expect(policy.enabled).toBe(true);
            expect(policy.lastModifiedBy).toBe(userId);
        });
        it('should disable policy', () => {
            policy.disable(userId);
            expect(policy.enabled).toBe(false);
            expect(policy.lastModifiedBy).toBe(userId);
        });
    });
    describe('audit trail', () => {
        let policy;
        beforeEach(() => {
            policy = security_policy_1.SecurityPolicy.create({
                name: 'Test Policy',
                description: 'A test policy',
                version: '1.0.0',
                tenantId,
                rules: [validRule],
                enabled: true,
                createdBy: userId
            });
        });
        it('should create audit entries when evaluating rules', () => {
            policy.evaluate(validContext);
            expect(policy.auditTrail).toHaveLength(1);
            expect(policy.auditTrail[0].ruleId).toBe('rule-1');
            expect(policy.auditTrail[0].executionStatus).toBe(security_policy_1.PolicyExecutionStatus.PENDING);
        });
        it('should maintain audit trail history', () => {
            // Evaluate multiple times
            policy.evaluate(validContext);
            policy.evaluate(validContext);
            expect(policy.auditTrail).toHaveLength(2);
        });
    });
    describe('confidence calculation', () => {
        let policy;
        beforeEach(() => {
            policy = security_policy_1.SecurityPolicy.create({
                name: 'Test Policy',
                description: 'A test policy',
                version: '1.0.0',
                tenantId,
                rules: [],
                enabled: true,
                createdBy: userId
            });
        });
        it('should calculate higher confidence for critical severity rules', () => {
            const criticalRule = { ...validRule, id: 'critical', severity: security_policy_1.SecurityRuleSeverity.CRITICAL };
            const lowRule = { ...validRule, id: 'low', severity: security_policy_1.SecurityRuleSeverity.LOW };
            policy.addRule(criticalRule, userId);
            policy.addRule(lowRule, userId);
            const results = policy.evaluate(validContext);
            const criticalResult = results.find(r => r.ruleId === 'critical');
            const lowResult = results.find(r => r.ruleId === 'low');
            expect(criticalResult?.confidence).toBeGreaterThan(lowResult?.confidence || 0);
        });
        it('should adjust confidence based on threat context', () => {
            policy.addRule(validRule, userId);
            const contextWithThreat = {
                ...validContext,
                threatContext: {
                    threatLevel: 'HIGH',
                    indicators: ['malicious-ip'],
                    confidence: 0.8
                }
            };
            const results = policy.evaluate(contextWithThreat);
            expect(results[0].confidence).toBeGreaterThan(0.8);
        });
    });
    describe('error handling', () => {
        let policy;
        beforeEach(() => {
            policy = security_policy_1.SecurityPolicy.create({
                name: 'Test Policy',
                description: 'A test policy',
                version: '1.0.0',
                tenantId,
                rules: [],
                enabled: true,
                createdBy: userId
            });
        });
        it('should handle invalid rule conditions gracefully', () => {
            const invalidRule = { ...validRule, condition: '{"field":{"nested":"invalid"},"operator":"equals","value":"test"}' };
            policy.addRule(invalidRule, userId);
            const results = policy.evaluate(validContext);
            expect(results).toHaveLength(1);
            expect(results[0].matched).toBe(false);
            expect(results[0].reason).toContain('evaluation failed');
        });
        it('should continue evaluation after rule error', () => {
            const invalidRule = { ...validRule, id: 'invalid', condition: '{"invalid": "structure"}' };
            const validRule2 = { ...validRule, id: 'valid' };
            policy.addRule(invalidRule, userId);
            policy.addRule(validRule2, userId);
            const results = policy.evaluate(validContext);
            expect(results).toHaveLength(2);
            expect(results.find(r => r.ruleId === 'invalid')?.matched).toBe(false);
            expect(results.find(r => r.ruleId === 'valid')?.matched).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFxwb2xpY2llc1xcX190ZXN0c19fXFxzZWN1cml0eS1wb2xpY3kuc3BlYy50cyIsIm1hcHBpbmdzIjoiOztBQUFBLHdEQUE0SjtBQUU1Siw4R0FBNkY7QUFDN0YsMEdBQXlGO0FBQ3pGLDhHQUE4RjtBQUM5Rix1R0FBbUc7QUFFbkcsUUFBUSxDQUFDLGdCQUFnQixFQUFFLEdBQUcsRUFBRTtJQUM5QixJQUFJLFFBQWtCLENBQUM7SUFDdkIsSUFBSSxNQUFjLENBQUM7SUFDbkIsSUFBSSxTQUF1QixDQUFDO0lBQzVCLElBQUksWUFBcUMsQ0FBQztJQUUxQyxVQUFVLENBQUMsR0FBRyxFQUFFO1FBQ2QsUUFBUSxHQUFHLGlDQUFRLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDO1FBQ3pDLE1BQU0sR0FBRyw2QkFBTSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUVuQyxTQUFTLEdBQUc7WUFDVixFQUFFLEVBQUUsUUFBUTtZQUNaLElBQUksRUFBRSxXQUFXO1lBQ2pCLFdBQVcsRUFBRSxhQUFhO1lBQzFCLFNBQVMsRUFBRSx1RUFBdUU7WUFDbEYsTUFBTSxFQUFFO2dCQUNOLElBQUksRUFBRSxvQ0FBa0IsQ0FBQyxXQUFXO2dCQUNwQyxVQUFVLEVBQUUsRUFBRSxPQUFPLEVBQUUsUUFBUSxFQUFFO2dCQUNqQyxXQUFXLEVBQUUsSUFBSTtnQkFDakIsZ0JBQWdCLEVBQUUsS0FBSzthQUN4QjtZQUNELFFBQVEsRUFBRSxzQ0FBb0IsQ0FBQyxJQUFJO1lBQ25DLE9BQU8sRUFBRSxJQUFJO1lBQ2IsUUFBUSxFQUFFLEVBQUU7U0FDYixDQUFDO1FBRUYsWUFBWSxHQUFHO1lBQ2IsU0FBUyxFQUFFO2dCQUNULElBQUksRUFBRSxjQUFjO2dCQUNwQixRQUFRLEVBQUUsQ0FBQztnQkFDWCxRQUFRLEVBQUUsZUFBZTthQUMxQjtZQUNELFdBQVcsRUFBRTtnQkFDWCxNQUFNO2dCQUNOLFFBQVE7Z0JBQ1IsS0FBSyxFQUFFLENBQUMsTUFBTSxDQUFDO2dCQUNmLFdBQVcsRUFBRSxDQUFDLE1BQU0sQ0FBQzthQUN0QjtZQUNELGFBQWEsRUFBRTtnQkFDYixTQUFTLEVBQUUsa0NBQVMsQ0FBQyxHQUFHLEVBQUU7Z0JBQzFCLFFBQVEsRUFBRSxlQUFlO2dCQUN6QixTQUFTLEVBQUUsYUFBYTthQUN6QjtTQUNGLENBQUM7SUFDSixDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxVQUFVLEVBQUUsR0FBRyxFQUFFO1FBQ3hCLEVBQUUsQ0FBQyx1Q0FBdUMsRUFBRSxHQUFHLEVBQUU7WUFDL0MsTUFBTSxNQUFNLEdBQUcsZ0NBQWMsQ0FBQyxNQUFNLENBQUM7Z0JBQ25DLElBQUksRUFBRSxhQUFhO2dCQUNuQixXQUFXLEVBQUUsZUFBZTtnQkFDNUIsT0FBTyxFQUFFLE9BQU87Z0JBQ2hCLFFBQVE7Z0JBQ1IsS0FBSyxFQUFFLENBQUMsU0FBUyxDQUFDO2dCQUNsQixPQUFPLEVBQUUsSUFBSTtnQkFDYixTQUFTLEVBQUUsTUFBTTthQUNsQixDQUFDLENBQUM7WUFFSCxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7WUFDN0IsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUM7WUFDeEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUM7WUFDakQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUM7WUFDckMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUM7WUFDdkMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDbEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDckMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDdEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxjQUFjLENBQUMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7UUFDN0MsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsb0RBQW9ELEVBQUUsR0FBRyxFQUFFO1lBQzVELE1BQU0sQ0FBQyxHQUFHLEVBQUU7Z0JBQ1YsZ0NBQWMsQ0FBQyxNQUFNLENBQUM7b0JBQ3BCLElBQUksRUFBRSxFQUFFO29CQUNSLFdBQVcsRUFBRSxlQUFlO29CQUM1QixPQUFPLEVBQUUsT0FBTztvQkFDaEIsUUFBUTtvQkFDUixLQUFLLEVBQUUsRUFBRTtvQkFDVCxPQUFPLEVBQUUsSUFBSTtvQkFDYixTQUFTLEVBQUUsTUFBTTtpQkFDbEIsQ0FBQyxDQUFDO1lBQ0wsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLDBDQUFtQixDQUFDLENBQUM7UUFDbEMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsMkRBQTJELEVBQUUsR0FBRyxFQUFFO1lBQ25FLE1BQU0sQ0FBQyxHQUFHLEVBQUU7Z0JBQ1YsZ0NBQWMsQ0FBQyxNQUFNLENBQUM7b0JBQ3BCLElBQUksRUFBRSxhQUFhO29CQUNuQixXQUFXLEVBQUUsRUFBRTtvQkFDZixPQUFPLEVBQUUsT0FBTztvQkFDaEIsUUFBUTtvQkFDUixLQUFLLEVBQUUsRUFBRTtvQkFDVCxPQUFPLEVBQUUsSUFBSTtvQkFDYixTQUFTLEVBQUUsTUFBTTtpQkFDbEIsQ0FBQyxDQUFDO1lBQ0wsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLDBDQUFtQixDQUFDLENBQUM7UUFDbEMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsb0RBQW9ELEVBQUUsR0FBRyxFQUFFO1lBQzVELE1BQU0sV0FBVyxHQUFHLEVBQUUsR0FBRyxTQUFTLEVBQUUsU0FBUyxFQUFFLGNBQWMsRUFBRSxDQUFDO1lBRWhFLE1BQU0sQ0FBQyxHQUFHLEVBQUU7Z0JBQ1YsZ0NBQWMsQ0FBQyxNQUFNLENBQUM7b0JBQ3BCLElBQUksRUFBRSxhQUFhO29CQUNuQixXQUFXLEVBQUUsZUFBZTtvQkFDNUIsT0FBTyxFQUFFLE9BQU87b0JBQ2hCLFFBQVE7b0JBQ1IsS0FBSyxFQUFFLENBQUMsV0FBVyxDQUFDO29CQUNwQixPQUFPLEVBQUUsSUFBSTtvQkFDYixTQUFTLEVBQUUsTUFBTTtpQkFDbEIsQ0FBQyxDQUFDO1lBQ0wsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLDBDQUFtQixDQUFDLENBQUM7UUFDbEMsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxpQkFBaUIsRUFBRSxHQUFHLEVBQUU7UUFDL0IsSUFBSSxNQUFzQixDQUFDO1FBRTNCLFVBQVUsQ0FBQyxHQUFHLEVBQUU7WUFDZCxNQUFNLEdBQUcsZ0NBQWMsQ0FBQyxNQUFNLENBQUM7Z0JBQzdCLElBQUksRUFBRSxhQUFhO2dCQUNuQixXQUFXLEVBQUUsZUFBZTtnQkFDNUIsT0FBTyxFQUFFLE9BQU87Z0JBQ2hCLFFBQVE7Z0JBQ1IsS0FBSyxFQUFFLENBQUMsU0FBUyxDQUFDO2dCQUNsQixPQUFPLEVBQUUsSUFBSTtnQkFDYixTQUFTLEVBQUUsTUFBTTthQUNsQixDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw2REFBNkQsRUFBRSxHQUFHLEVBQUU7WUFDckUsTUFBTSxPQUFPLEdBQUcsTUFBTSxDQUFDLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUU5QyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2hDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3RDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ3pDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7WUFDeEMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxVQUFVLENBQUMsQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDbkQsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsb0VBQW9FLEVBQUUsR0FBRyxFQUFFO1lBQzVFLE1BQU0sd0JBQXdCLEdBQUc7Z0JBQy9CLEdBQUcsWUFBWTtnQkFDZixTQUFTLEVBQUUsRUFBRSxHQUFHLFlBQVksQ0FBQyxTQUFTLEVBQUUsSUFBSSxFQUFFLGVBQWUsRUFBRTthQUNoRSxDQUFDO1lBRUYsTUFBTSxPQUFPLEdBQUcsTUFBTSxDQUFDLFFBQVEsQ0FBQyx3QkFBd0IsQ0FBQyxDQUFDO1lBRTFELE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDaEMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDdkMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxhQUFhLEVBQUUsQ0FBQztZQUMxQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN4QyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxtREFBbUQsRUFBRSxHQUFHLEVBQUU7WUFDM0QsTUFBTSxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUN2QixNQUFNLE9BQU8sR0FBRyxNQUFNLENBQUMsUUFBUSxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRTlDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDbEMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsb0NBQW9DLEVBQUUsR0FBRyxFQUFFO1lBQzVDLE1BQU0sWUFBWSxHQUFHLEVBQUUsR0FBRyxTQUFTLEVBQUUsRUFBRSxFQUFFLFFBQVEsRUFBRSxPQUFPLEVBQUUsS0FBSyxFQUFFLENBQUM7WUFDcEUsTUFBTSxDQUFDLE9BQU8sQ0FBQyxZQUFZLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFFckMsTUFBTSxPQUFPLEdBQUcsTUFBTSxDQUFDLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUU5QyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsd0JBQXdCO1lBQ3pELE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQzNDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHlDQUF5QyxFQUFFLEdBQUcsRUFBRTtZQUNqRCxNQUFNLGdCQUFnQixHQUFHO2dCQUN2QixHQUFHLFNBQVM7Z0JBQ1osRUFBRSxFQUFFLFdBQVc7Z0JBQ2YsUUFBUSxFQUFFLEdBQUc7Z0JBQ2IsUUFBUSxFQUFFLHNDQUFvQixDQUFDLElBQUk7YUFDcEMsQ0FBQztZQUVGLE1BQU0sZUFBZSxHQUFHO2dCQUN0QixHQUFHLFNBQVM7Z0JBQ1osRUFBRSxFQUFFLFVBQVU7Z0JBQ2QsUUFBUSxFQUFFLEVBQUU7Z0JBQ1osUUFBUSxFQUFFLHNDQUFvQixDQUFDLEdBQUc7YUFDbkMsQ0FBQztZQUVGLE1BQU0sQ0FBQyxPQUFPLENBQUMsZ0JBQWdCLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFDekMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxlQUFlLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFFeEMsTUFBTSxPQUFPLEdBQUcsTUFBTSxDQUFDLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUU5QyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2hDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMseUJBQXlCO1lBQ3RFLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsa0JBQWtCO1lBQzVELE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUMsa0JBQWtCO1FBQ2hFLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGtEQUFrRCxFQUFFLEdBQUcsRUFBRTtZQUMxRCxNQUFNLFlBQVksR0FBRztnQkFDbkIsR0FBRyxTQUFTO2dCQUNaLEVBQUUsRUFBRSxlQUFlO2dCQUNuQixRQUFRLEVBQUUsR0FBRztnQkFDYixRQUFRLEVBQUUsc0NBQW9CLENBQUMsUUFBUTthQUN4QyxDQUFDO1lBRUYsTUFBTSxVQUFVLEdBQUc7Z0JBQ2pCLEdBQUcsU0FBUztnQkFDWixFQUFFLEVBQUUsYUFBYTtnQkFDakIsUUFBUSxFQUFFLEVBQUU7Z0JBQ1osUUFBUSxFQUFFLHNDQUFvQixDQUFDLE1BQU07YUFDdEMsQ0FBQztZQUVGLE1BQU0sQ0FBQyxPQUFPLENBQUMsWUFBWSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1lBQ3JDLE1BQU0sQ0FBQyxPQUFPLENBQUMsVUFBVSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1lBRW5DLE1BQU0sT0FBTyxHQUFHLE1BQU0sQ0FBQyxRQUFRLENBQUMsWUFBWSxDQUFDLENBQUM7WUFFOUMsMENBQTBDO1lBQzFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDaEMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUM7UUFDbEQsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyx5QkFBeUIsRUFBRSxHQUFHLEVBQUU7UUFDdkMsSUFBSSxNQUFzQixDQUFDO1FBRTNCLFVBQVUsQ0FBQyxHQUFHLEVBQUU7WUFDZCxNQUFNLEdBQUcsZ0NBQWMsQ0FBQyxNQUFNLENBQUM7Z0JBQzdCLElBQUksRUFBRSxhQUFhO2dCQUNuQixXQUFXLEVBQUUsZUFBZTtnQkFDNUIsT0FBTyxFQUFFLE9BQU87Z0JBQ2hCLFFBQVE7Z0JBQ1IsS0FBSyxFQUFFLEVBQUU7Z0JBQ1QsT0FBTyxFQUFFLElBQUk7Z0JBQ2IsU0FBUyxFQUFFLE1BQU07YUFDbEIsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsMENBQTBDLEVBQUUsR0FBRyxFQUFFO1lBQ2xELE1BQU0sT0FBTyxHQUFpQjtnQkFDNUIsR0FBRyxTQUFTO2dCQUNaLEVBQUUsRUFBRSxVQUFVO2dCQUNkLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDO29CQUN4QixHQUFHLEVBQUU7d0JBQ0gsRUFBRSxLQUFLLEVBQUUsZ0JBQWdCLEVBQUUsUUFBUSxFQUFFLFFBQVEsRUFBRSxLQUFLLEVBQUUsY0FBYyxFQUFFO3dCQUN0RSxFQUFFLEtBQUssRUFBRSxvQkFBb0IsRUFBRSxRQUFRLEVBQUUsY0FBYyxFQUFFLEtBQUssRUFBRSxDQUFDLEVBQUU7cUJBQ3BFO2lCQUNGLENBQUM7YUFDSCxDQUFDO1lBRUYsTUFBTSxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFDaEMsTUFBTSxPQUFPLEdBQUcsTUFBTSxDQUFDLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUU5QyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUN4QyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx5Q0FBeUMsRUFBRSxHQUFHLEVBQUU7WUFDakQsTUFBTSxNQUFNLEdBQWlCO2dCQUMzQixHQUFHLFNBQVM7Z0JBQ1osRUFBRSxFQUFFLFNBQVM7Z0JBQ2IsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUM7b0JBQ3hCLEVBQUUsRUFBRTt3QkFDRixFQUFFLEtBQUssRUFBRSxnQkFBZ0IsRUFBRSxRQUFRLEVBQUUsUUFBUSxFQUFFLEtBQUssRUFBRSxjQUFjLEVBQUU7d0JBQ3RFLEVBQUUsS0FBSyxFQUFFLGdCQUFnQixFQUFFLFFBQVEsRUFBRSxRQUFRLEVBQUUsS0FBSyxFQUFFLGdCQUFnQixFQUFFO3FCQUN6RTtpQkFDRixDQUFDO2FBQ0gsQ0FBQztZQUVGLE1BQU0sQ0FBQyxPQUFPLENBQUMsTUFBTSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1lBQy9CLE1BQU0sT0FBTyxHQUFHLE1BQU0sQ0FBQyxRQUFRLENBQUMsWUFBWSxDQUFDLENBQUM7WUFFOUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDeEMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsMENBQTBDLEVBQUUsR0FBRyxFQUFFO1lBQ2xELE1BQU0sT0FBTyxHQUFpQjtnQkFDNUIsR0FBRyxTQUFTO2dCQUNaLEVBQUUsRUFBRSxVQUFVO2dCQUNkLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDO29CQUN4QixHQUFHLEVBQUU7d0JBQ0gsS0FBSyxFQUFFLGdCQUFnQjt3QkFDdkIsUUFBUSxFQUFFLFFBQVE7d0JBQ2xCLEtBQUssRUFBRSxlQUFlO3FCQUN2QjtpQkFDRixDQUFDO2FBQ0gsQ0FBQztZQUVGLE1BQU0sQ0FBQyxPQUFPLENBQUMsT0FBTyxFQUFFLE1BQU0sQ0FBQyxDQUFDO1lBQ2hDLE1BQU0sT0FBTyxHQUFHLE1BQU0sQ0FBQyxRQUFRLENBQUMsWUFBWSxDQUFDLENBQUM7WUFFOUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDeEMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsNkNBQTZDLEVBQUUsR0FBRyxFQUFFO1lBQ3JELE1BQU0sVUFBVSxHQUFpQjtnQkFDL0IsR0FBRyxTQUFTO2dCQUNaLEVBQUUsRUFBRSxhQUFhO2dCQUNqQixTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQztvQkFDeEIsR0FBRyxFQUFFO3dCQUNIOzRCQUNFLEVBQUUsRUFBRTtnQ0FDRixFQUFFLEtBQUssRUFBRSxnQkFBZ0IsRUFBRSxRQUFRLEVBQUUsUUFBUSxFQUFFLEtBQUssRUFBRSxjQUFjLEVBQUU7Z0NBQ3RFLEVBQUUsS0FBSyxFQUFFLGdCQUFnQixFQUFFLFFBQVEsRUFBRSxRQUFRLEVBQUUsS0FBSyxFQUFFLGdCQUFnQixFQUFFOzZCQUN6RTt5QkFDRjt3QkFDRCxFQUFFLEtBQUssRUFBRSxvQkFBb0IsRUFBRSxRQUFRLEVBQUUsY0FBYyxFQUFFLEtBQUssRUFBRSxDQUFDLEVBQUU7cUJBQ3BFO2lCQUNGLENBQUM7YUFDSCxDQUFDO1lBRUYsTUFBTSxDQUFDLE9BQU8sQ0FBQyxVQUFVLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFDbkMsTUFBTSxPQUFPLEdBQUcsTUFBTSxDQUFDLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUU5QyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUN4QyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx1Q0FBdUMsRUFBRSxHQUFHLEVBQUU7WUFDL0MsTUFBTSxTQUFTLEdBQUc7Z0JBQ2hCLEVBQUUsUUFBUSxFQUFFLFVBQVUsRUFBRSxLQUFLLEVBQUUsb0JBQW9CLEVBQUUsS0FBSyxFQUFFLFNBQVMsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFO2dCQUN2RixFQUFFLFFBQVEsRUFBRSxhQUFhLEVBQUUsS0FBSyxFQUFFLG9CQUFvQixFQUFFLEtBQUssRUFBRSxLQUFLLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRTtnQkFDdEYsRUFBRSxRQUFRLEVBQUUsV0FBVyxFQUFFLEtBQUssRUFBRSxvQkFBb0IsRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUU7Z0JBQ3BGLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxLQUFLLEVBQUUsZ0JBQWdCLEVBQUUsS0FBSyxFQUFFLENBQUMsY0FBYyxFQUFFLGVBQWUsQ0FBQyxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUU7Z0JBQ3JHLEVBQUUsUUFBUSxFQUFFLE9BQU8sRUFBRSxLQUFLLEVBQUUsb0JBQW9CLEVBQUUsS0FBSyxFQUFFLDJCQUEyQixFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUU7YUFDdkcsQ0FBQztZQUVGLFNBQVMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxFQUFFLFFBQVEsRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFLFFBQVEsRUFBRSxFQUFFLEtBQUssRUFBRSxFQUFFO2dCQUNoRSxNQUFNLElBQUksR0FBaUI7b0JBQ3pCLEdBQUcsU0FBUztvQkFDWixFQUFFLEVBQUUsYUFBYSxLQUFLLEVBQUU7b0JBQ3hCLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLEVBQUUsS0FBSyxFQUFFLFFBQVEsRUFBRSxLQUFLLEVBQUUsQ0FBQztpQkFDdEQsQ0FBQztnQkFFRixNQUFNLENBQUMsT0FBTyxDQUFDLElBQUksRUFBRSxNQUFNLENBQUMsQ0FBQztnQkFDN0IsTUFBTSxPQUFPLEdBQUcsTUFBTSxDQUFDLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQztnQkFDOUMsTUFBTSxNQUFNLEdBQUcsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxNQUFNLEtBQUssYUFBYSxLQUFLLEVBQUUsQ0FBQyxDQUFDO2dCQUVwRSxNQUFNLENBQUMsTUFBTSxFQUFFLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUN6QyxDQUFDLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsaUJBQWlCLEVBQUUsR0FBRyxFQUFFO1FBQy9CLElBQUksTUFBc0IsQ0FBQztRQUUzQixVQUFVLENBQUMsR0FBRyxFQUFFO1lBQ2QsTUFBTSxHQUFHLGdDQUFjLENBQUMsTUFBTSxDQUFDO2dCQUM3QixJQUFJLEVBQUUsYUFBYTtnQkFDbkIsV0FBVyxFQUFFLGVBQWU7Z0JBQzVCLE9BQU8sRUFBRSxPQUFPO2dCQUNoQixRQUFRO2dCQUNSLEtBQUssRUFBRSxDQUFDLFNBQVMsQ0FBQztnQkFDbEIsT0FBTyxFQUFFLElBQUk7Z0JBQ2IsU0FBUyxFQUFFLE1BQU07YUFDbEIsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsdUJBQXVCLEVBQUUsR0FBRyxFQUFFO1lBQy9CLE1BQU0sT0FBTyxHQUFHLEVBQUUsR0FBRyxTQUFTLEVBQUUsRUFBRSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsVUFBVSxFQUFFLENBQUM7WUFDakUsTUFBTSxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFFaEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDckMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUUsS0FBSyxRQUFRLENBQUMsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1FBQ2xFLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGtEQUFrRCxFQUFFLEdBQUcsRUFBRTtZQUMxRCxNQUFNLENBQUMsR0FBRyxFQUFFO2dCQUNWLE1BQU0sQ0FBQyxPQUFPLENBQUMsU0FBUyxFQUFFLE1BQU0sQ0FBQyxDQUFDO1lBQ3BDLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQywwQ0FBbUIsQ0FBQyxDQUFDO1FBQ2xDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGdDQUFnQyxFQUFFLEdBQUcsRUFBRTtZQUN4QyxNQUFNLENBQUMsVUFBVSxDQUFDLFFBQVEsRUFBRSxFQUFFLElBQUksRUFBRSxjQUFjLEVBQUUsRUFBRSxNQUFNLENBQUMsQ0FBQztZQUU5RCxNQUFNLFdBQVcsR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxFQUFFLEtBQUssUUFBUSxDQUFDLENBQUM7WUFDOUQsTUFBTSxDQUFDLFdBQVcsRUFBRSxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUM7UUFDakQsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsb0RBQW9ELEVBQUUsR0FBRyxFQUFFO1lBQzVELE1BQU0sQ0FBQyxHQUFHLEVBQUU7Z0JBQ1YsTUFBTSxDQUFDLFVBQVUsQ0FBQyxjQUFjLEVBQUUsRUFBRSxJQUFJLEVBQUUsU0FBUyxFQUFFLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFDakUsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLDBDQUFtQixDQUFDLENBQUM7UUFDbEMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsc0JBQXNCLEVBQUUsR0FBRyxFQUFFO1lBQzlCLE1BQU0sQ0FBQyxVQUFVLENBQUMsUUFBUSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1lBRXBDLE1BQU0sQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3ZDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLG9EQUFvRCxFQUFFLEdBQUcsRUFBRTtZQUM1RCxNQUFNLENBQUMsR0FBRyxFQUFFO2dCQUNWLE1BQU0sQ0FBQyxVQUFVLENBQUMsY0FBYyxFQUFFLE1BQU0sQ0FBQyxDQUFDO1lBQzVDLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQywwQ0FBbUIsQ0FBQyxDQUFDO1FBQ2xDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMseUJBQXlCLEVBQUUsR0FBRyxFQUFFO1FBQ3ZDLElBQUksTUFBc0IsQ0FBQztRQUUzQixVQUFVLENBQUMsR0FBRyxFQUFFO1lBQ2QsTUFBTSxHQUFHLGdDQUFjLENBQUMsTUFBTSxDQUFDO2dCQUM3QixJQUFJLEVBQUUsYUFBYTtnQkFDbkIsV0FBVyxFQUFFLGVBQWU7Z0JBQzVCLE9BQU8sRUFBRSxPQUFPO2dCQUNoQixRQUFRO2dCQUNSLEtBQUssRUFBRSxDQUFDLFNBQVMsQ0FBQztnQkFDbEIsT0FBTyxFQUFFLElBQUk7Z0JBQ2IsU0FBUyxFQUFFLE1BQU07YUFDbEIsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsc0JBQXNCLEVBQUUsR0FBRyxFQUFFO1lBQzlCLE1BQU0sQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDdkIsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFFbkMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUN0QixNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUNsQyxNQUFNLENBQUMsTUFBTSxDQUFDLGNBQWMsQ0FBQyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUM3QyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx1QkFBdUIsRUFBRSxHQUFHLEVBQUU7WUFDL0IsTUFBTSxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUN2QixNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUNuQyxNQUFNLENBQUMsTUFBTSxDQUFDLGNBQWMsQ0FBQyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUM3QyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGFBQWEsRUFBRSxHQUFHLEVBQUU7UUFDM0IsSUFBSSxNQUFzQixDQUFDO1FBRTNCLFVBQVUsQ0FBQyxHQUFHLEVBQUU7WUFDZCxNQUFNLEdBQUcsZ0NBQWMsQ0FBQyxNQUFNLENBQUM7Z0JBQzdCLElBQUksRUFBRSxhQUFhO2dCQUNuQixXQUFXLEVBQUUsZUFBZTtnQkFDNUIsT0FBTyxFQUFFLE9BQU87Z0JBQ2hCLFFBQVE7Z0JBQ1IsS0FBSyxFQUFFLENBQUMsU0FBUyxDQUFDO2dCQUNsQixPQUFPLEVBQUUsSUFBSTtnQkFDYixTQUFTLEVBQUUsTUFBTTthQUNsQixDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxtREFBbUQsRUFBRSxHQUFHLEVBQUU7WUFDM0QsTUFBTSxDQUFDLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUU5QixNQUFNLENBQUMsTUFBTSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMxQyxNQUFNLENBQUMsTUFBTSxDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUM7WUFDbkQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLHVDQUFxQixDQUFDLE9BQU8sQ0FBQyxDQUFDO1FBQ25GLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHFDQUFxQyxFQUFFLEdBQUcsRUFBRTtZQUM3QywwQkFBMEI7WUFDMUIsTUFBTSxDQUFDLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUM5QixNQUFNLENBQUMsUUFBUSxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRTlCLE1BQU0sQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzVDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsd0JBQXdCLEVBQUUsR0FBRyxFQUFFO1FBQ3RDLElBQUksTUFBc0IsQ0FBQztRQUUzQixVQUFVLENBQUMsR0FBRyxFQUFFO1lBQ2QsTUFBTSxHQUFHLGdDQUFjLENBQUMsTUFBTSxDQUFDO2dCQUM3QixJQUFJLEVBQUUsYUFBYTtnQkFDbkIsV0FBVyxFQUFFLGVBQWU7Z0JBQzVCLE9BQU8sRUFBRSxPQUFPO2dCQUNoQixRQUFRO2dCQUNSLEtBQUssRUFBRSxFQUFFO2dCQUNULE9BQU8sRUFBRSxJQUFJO2dCQUNiLFNBQVMsRUFBRSxNQUFNO2FBQ2xCLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGdFQUFnRSxFQUFFLEdBQUcsRUFBRTtZQUN4RSxNQUFNLFlBQVksR0FBRyxFQUFFLEdBQUcsU0FBUyxFQUFFLEVBQUUsRUFBRSxVQUFVLEVBQUUsUUFBUSxFQUFFLHNDQUFvQixDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQy9GLE1BQU0sT0FBTyxHQUFHLEVBQUUsR0FBRyxTQUFTLEVBQUUsRUFBRSxFQUFFLEtBQUssRUFBRSxRQUFRLEVBQUUsc0NBQW9CLENBQUMsR0FBRyxFQUFFLENBQUM7WUFFaEYsTUFBTSxDQUFDLE9BQU8sQ0FBQyxZQUFZLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFDckMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFFaEMsTUFBTSxPQUFPLEdBQUcsTUFBTSxDQUFDLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUM5QyxNQUFNLGNBQWMsR0FBRyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLE1BQU0sS0FBSyxVQUFVLENBQUMsQ0FBQztZQUNsRSxNQUFNLFNBQVMsR0FBRyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLE1BQU0sS0FBSyxLQUFLLENBQUMsQ0FBQztZQUV4RCxNQUFNLENBQUMsY0FBYyxFQUFFLFVBQVUsQ0FBQyxDQUFDLGVBQWUsQ0FBQyxTQUFTLEVBQUUsVUFBVSxJQUFJLENBQUMsQ0FBQyxDQUFDO1FBQ2pGLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGtEQUFrRCxFQUFFLEdBQUcsRUFBRTtZQUMxRCxNQUFNLENBQUMsT0FBTyxDQUFDLFNBQVMsRUFBRSxNQUFNLENBQUMsQ0FBQztZQUVsQyxNQUFNLGlCQUFpQixHQUFHO2dCQUN4QixHQUFHLFlBQVk7Z0JBQ2YsYUFBYSxFQUFFO29CQUNiLFdBQVcsRUFBRSxNQUFNO29CQUNuQixVQUFVLEVBQUUsQ0FBQyxjQUFjLENBQUM7b0JBQzVCLFVBQVUsRUFBRSxHQUFHO2lCQUNoQjthQUNGLENBQUM7WUFFRixNQUFNLE9BQU8sR0FBRyxNQUFNLENBQUMsUUFBUSxDQUFDLGlCQUFpQixDQUFDLENBQUM7WUFDbkQsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxVQUFVLENBQUMsQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDckQsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxnQkFBZ0IsRUFBRSxHQUFHLEVBQUU7UUFDOUIsSUFBSSxNQUFzQixDQUFDO1FBRTNCLFVBQVUsQ0FBQyxHQUFHLEVBQUU7WUFDZCxNQUFNLEdBQUcsZ0NBQWMsQ0FBQyxNQUFNLENBQUM7Z0JBQzdCLElBQUksRUFBRSxhQUFhO2dCQUNuQixXQUFXLEVBQUUsZUFBZTtnQkFDNUIsT0FBTyxFQUFFLE9BQU87Z0JBQ2hCLFFBQVE7Z0JBQ1IsS0FBSyxFQUFFLEVBQUU7Z0JBQ1QsT0FBTyxFQUFFLElBQUk7Z0JBQ2IsU0FBUyxFQUFFLE1BQU07YUFDbEIsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsa0RBQWtELEVBQUUsR0FBRyxFQUFFO1lBQzFELE1BQU0sV0FBVyxHQUFHLEVBQUUsR0FBRyxTQUFTLEVBQUUsU0FBUyxFQUFFLG1FQUFtRSxFQUFFLENBQUM7WUFDckgsTUFBTSxDQUFDLE9BQU8sQ0FBQyxXQUFXLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFFcEMsTUFBTSxPQUFPLEdBQUcsTUFBTSxDQUFDLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUU5QyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2hDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQ3ZDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsU0FBUyxDQUFDLG1CQUFtQixDQUFDLENBQUM7UUFDM0QsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsNkNBQTZDLEVBQUUsR0FBRyxFQUFFO1lBQ3JELE1BQU0sV0FBVyxHQUFHLEVBQUUsR0FBRyxTQUFTLEVBQUUsRUFBRSxFQUFFLFNBQVMsRUFBRSxTQUFTLEVBQUUsMEJBQTBCLEVBQUUsQ0FBQztZQUMzRixNQUFNLFVBQVUsR0FBRyxFQUFFLEdBQUcsU0FBUyxFQUFFLEVBQUUsRUFBRSxPQUFPLEVBQUUsQ0FBQztZQUVqRCxNQUFNLENBQUMsT0FBTyxDQUFDLFdBQVcsRUFBRSxNQUFNLENBQUMsQ0FBQztZQUNwQyxNQUFNLENBQUMsT0FBTyxDQUFDLFVBQVUsRUFBRSxNQUFNLENBQUMsQ0FBQztZQUVuQyxNQUFNLE9BQU8sR0FBRyxNQUFNLENBQUMsUUFBUSxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRTlDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDaEMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsTUFBTSxLQUFLLFNBQVMsQ0FBQyxFQUFFLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUN2RSxNQUFNLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxNQUFNLEtBQUssT0FBTyxDQUFDLEVBQUUsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQ3RFLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXGNvcmVcXHNlY3VyaXR5XFxkb21haW5cXHBvbGljaWVzXFxfX3Rlc3RzX19cXHNlY3VyaXR5LXBvbGljeS5zcGVjLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFNlY3VyaXR5UG9saWN5LCBTZWN1cml0eVJ1bGUsIFNlY3VyaXR5QWN0aW9uVHlwZSwgU2VjdXJpdHlSdWxlU2V2ZXJpdHksIFBvbGljeUV2YWx1YXRpb25Db250ZXh0LCBQb2xpY3lFeGVjdXRpb25TdGF0dXMgfSBmcm9tICcuLi9zZWN1cml0eS1wb2xpY3knO1xyXG5pbXBvcnQgeyBVbmlxdWVFbnRpdHlJZCB9IGZyb20gJy4uLy4uLy4uLy4uLy4uL3NoYXJlZC1rZXJuZWwvdmFsdWUtb2JqZWN0cy91bmlxdWUtZW50aXR5LWlkLnZhbHVlLW9iamVjdCc7XHJcbmltcG9ydCB7IFRlbmFudElkIH0gZnJvbSAnLi4vLi4vLi4vLi4vLi4vc2hhcmVkLWtlcm5lbC92YWx1ZS1vYmplY3RzL3RlbmFudC1pZC52YWx1ZS1vYmplY3QnO1xyXG5pbXBvcnQgeyBVc2VySWQgfSBmcm9tICcuLi8uLi8uLi8uLi8uLi9zaGFyZWQta2VybmVsL3ZhbHVlLW9iamVjdHMvdXNlci1pZC52YWx1ZS1vYmplY3QnO1xyXG5pbXBvcnQgeyBUaW1lc3RhbXAgfSBmcm9tICcuLi8uLi8uLi8uLi8uLi9zaGFyZWQta2VybmVsL3ZhbHVlLW9iamVjdHMvdGltZXN0YW1wLnZhbHVlLW9iamVjdCc7XHJcbmltcG9ydCB7IFZhbGlkYXRpb25FeGNlcHRpb24gfSBmcm9tICcuLi8uLi8uLi8uLi8uLi9zaGFyZWQta2VybmVsL2V4Y2VwdGlvbnMvdmFsaWRhdGlvbi5leGNlcHRpb24nO1xyXG5cclxuZGVzY3JpYmUoJ1NlY3VyaXR5UG9saWN5JywgKCkgPT4ge1xyXG4gIGxldCB0ZW5hbnRJZDogVGVuYW50SWQ7XHJcbiAgbGV0IHVzZXJJZDogVXNlcklkO1xyXG4gIGxldCB2YWxpZFJ1bGU6IFNlY3VyaXR5UnVsZTtcclxuICBsZXQgdmFsaWRDb250ZXh0OiBQb2xpY3lFdmFsdWF0aW9uQ29udGV4dDtcclxuXHJcbiAgYmVmb3JlRWFjaCgoKSA9PiB7XHJcbiAgICB0ZW5hbnRJZCA9IFRlbmFudElkLmNyZWF0ZSgndGVuYW50LTEyMycpO1xyXG4gICAgdXNlcklkID0gVXNlcklkLmNyZWF0ZSgndXNlci0xMjMnKTtcclxuICAgIFxyXG4gICAgdmFsaWRSdWxlID0ge1xyXG4gICAgICBpZDogJ3J1bGUtMScsXHJcbiAgICAgIG5hbWU6ICdUZXN0IFJ1bGUnLFxyXG4gICAgICBkZXNjcmlwdGlvbjogJ0EgdGVzdCBydWxlJyxcclxuICAgICAgY29uZGl0aW9uOiAne1wiZmllbGRcIjpcImV2ZW50RGF0YS50eXBlXCIsXCJvcGVyYXRvclwiOlwiZXF1YWxzXCIsXCJ2YWx1ZVwiOlwibG9naW5fZmFpbGVkXCJ9JyxcclxuICAgICAgYWN0aW9uOiB7XHJcbiAgICAgICAgdHlwZTogU2VjdXJpdHlBY3Rpb25UeXBlLkFMRVJUX0FETUlOLFxyXG4gICAgICAgIHBhcmFtZXRlcnM6IHsgbWVzc2FnZTogJ0FsZXJ0IScgfSxcclxuICAgICAgICBhdXRvRXhlY3V0ZTogdHJ1ZSxcclxuICAgICAgICByZXF1aXJlc0FwcHJvdmFsOiBmYWxzZVxyXG4gICAgICB9LFxyXG4gICAgICBzZXZlcml0eTogU2VjdXJpdHlSdWxlU2V2ZXJpdHkuSElHSCxcclxuICAgICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgICAgcHJpb3JpdHk6IDgwXHJcbiAgICB9O1xyXG5cclxuICAgIHZhbGlkQ29udGV4dCA9IHtcclxuICAgICAgZXZlbnREYXRhOiB7XHJcbiAgICAgICAgdHlwZTogJ2xvZ2luX2ZhaWxlZCcsXHJcbiAgICAgICAgYXR0ZW1wdHM6IDMsXHJcbiAgICAgICAgc291cmNlSXA6ICcxOTIuMTY4LjEuMTAwJ1xyXG4gICAgICB9LFxyXG4gICAgICB1c2VyQ29udGV4dDoge1xyXG4gICAgICAgIHVzZXJJZCxcclxuICAgICAgICB0ZW5hbnRJZCxcclxuICAgICAgICByb2xlczogWyd1c2VyJ10sXHJcbiAgICAgICAgcGVybWlzc2lvbnM6IFsncmVhZCddXHJcbiAgICAgIH0sXHJcbiAgICAgIHN5c3RlbUNvbnRleHQ6IHtcclxuICAgICAgICB0aW1lc3RhbXA6IFRpbWVzdGFtcC5ub3coKSxcclxuICAgICAgICBzb3VyY2VJcDogJzE5Mi4xNjguMS4xMDAnLFxyXG4gICAgICAgIHVzZXJBZ2VudDogJ01vemlsbGEvNS4wJ1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnY3JlYXRpb24nLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIGNyZWF0ZSBhIHZhbGlkIHNlY3VyaXR5IHBvbGljeScsICgpID0+IHtcclxuICAgICAgY29uc3QgcG9saWN5ID0gU2VjdXJpdHlQb2xpY3kuY3JlYXRlKHtcclxuICAgICAgICBuYW1lOiAnVGVzdCBQb2xpY3knLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQSB0ZXN0IHBvbGljeScsXHJcbiAgICAgICAgdmVyc2lvbjogJzEuMC4wJyxcclxuICAgICAgICB0ZW5hbnRJZCxcclxuICAgICAgICBydWxlczogW3ZhbGlkUnVsZV0sXHJcbiAgICAgICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgICAgICBjcmVhdGVkQnk6IHVzZXJJZFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGV4cGVjdChwb2xpY3kpLnRvQmVEZWZpbmVkKCk7XHJcbiAgICAgIGV4cGVjdChwb2xpY3kubmFtZSkudG9CZSgnVGVzdCBQb2xpY3knKTtcclxuICAgICAgZXhwZWN0KHBvbGljeS5kZXNjcmlwdGlvbikudG9CZSgnQSB0ZXN0IHBvbGljeScpO1xyXG4gICAgICBleHBlY3QocG9saWN5LnZlcnNpb24pLnRvQmUoJzEuMC4wJyk7XHJcbiAgICAgIGV4cGVjdChwb2xpY3kudGVuYW50SWQpLnRvQmUodGVuYW50SWQpO1xyXG4gICAgICBleHBlY3QocG9saWN5LmVuYWJsZWQpLnRvQmUodHJ1ZSk7XHJcbiAgICAgIGV4cGVjdChwb2xpY3kucnVsZXMpLnRvSGF2ZUxlbmd0aCgxKTtcclxuICAgICAgZXhwZWN0KHBvbGljeS5jcmVhdGVkQnkpLnRvQmUodXNlcklkKTtcclxuICAgICAgZXhwZWN0KHBvbGljeS5sYXN0TW9kaWZpZWRCeSkudG9CZSh1c2VySWQpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyB2YWxpZGF0aW9uIGV4Y2VwdGlvbiBmb3IgbWlzc2luZyBuYW1lJywgKCkgPT4ge1xyXG4gICAgICBleHBlY3QoKCkgPT4ge1xyXG4gICAgICAgIFNlY3VyaXR5UG9saWN5LmNyZWF0ZSh7XHJcbiAgICAgICAgICBuYW1lOiAnJyxcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnQSB0ZXN0IHBvbGljeScsXHJcbiAgICAgICAgICB2ZXJzaW9uOiAnMS4wLjAnLFxyXG4gICAgICAgICAgdGVuYW50SWQsXHJcbiAgICAgICAgICBydWxlczogW10sXHJcbiAgICAgICAgICBlbmFibGVkOiB0cnVlLFxyXG4gICAgICAgICAgY3JlYXRlZEJ5OiB1c2VySWRcclxuICAgICAgICB9KTtcclxuICAgICAgfSkudG9UaHJvdyhWYWxpZGF0aW9uRXhjZXB0aW9uKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgdmFsaWRhdGlvbiBleGNlcHRpb24gZm9yIG1pc3NpbmcgZGVzY3JpcHRpb24nLCAoKSA9PiB7XHJcbiAgICAgIGV4cGVjdCgoKSA9PiB7XHJcbiAgICAgICAgU2VjdXJpdHlQb2xpY3kuY3JlYXRlKHtcclxuICAgICAgICAgIG5hbWU6ICdUZXN0IFBvbGljeScsXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJycsXHJcbiAgICAgICAgICB2ZXJzaW9uOiAnMS4wLjAnLFxyXG4gICAgICAgICAgdGVuYW50SWQsXHJcbiAgICAgICAgICBydWxlczogW10sXHJcbiAgICAgICAgICBlbmFibGVkOiB0cnVlLFxyXG4gICAgICAgICAgY3JlYXRlZEJ5OiB1c2VySWRcclxuICAgICAgICB9KTtcclxuICAgICAgfSkudG9UaHJvdyhWYWxpZGF0aW9uRXhjZXB0aW9uKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgdmFsaWRhdGlvbiBleGNlcHRpb24gZm9yIGludmFsaWQgcnVsZScsICgpID0+IHtcclxuICAgICAgY29uc3QgaW52YWxpZFJ1bGUgPSB7IC4uLnZhbGlkUnVsZSwgY29uZGl0aW9uOiAnaW52YWxpZCBqc29uJyB9O1xyXG4gICAgICBcclxuICAgICAgZXhwZWN0KCgpID0+IHtcclxuICAgICAgICBTZWN1cml0eVBvbGljeS5jcmVhdGUoe1xyXG4gICAgICAgICAgbmFtZTogJ1Rlc3QgUG9saWN5JyxcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnQSB0ZXN0IHBvbGljeScsXHJcbiAgICAgICAgICB2ZXJzaW9uOiAnMS4wLjAnLFxyXG4gICAgICAgICAgdGVuYW50SWQsXHJcbiAgICAgICAgICBydWxlczogW2ludmFsaWRSdWxlXSxcclxuICAgICAgICAgIGVuYWJsZWQ6IHRydWUsXHJcbiAgICAgICAgICBjcmVhdGVkQnk6IHVzZXJJZFxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9KS50b1Rocm93KFZhbGlkYXRpb25FeGNlcHRpb24pO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdydWxlIGV2YWx1YXRpb24nLCAoKSA9PiB7XHJcbiAgICBsZXQgcG9saWN5OiBTZWN1cml0eVBvbGljeTtcclxuXHJcbiAgICBiZWZvcmVFYWNoKCgpID0+IHtcclxuICAgICAgcG9saWN5ID0gU2VjdXJpdHlQb2xpY3kuY3JlYXRlKHtcclxuICAgICAgICBuYW1lOiAnVGVzdCBQb2xpY3knLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQSB0ZXN0IHBvbGljeScsXHJcbiAgICAgICAgdmVyc2lvbjogJzEuMC4wJyxcclxuICAgICAgICB0ZW5hbnRJZCxcclxuICAgICAgICBydWxlczogW3ZhbGlkUnVsZV0sXHJcbiAgICAgICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgICAgICBjcmVhdGVkQnk6IHVzZXJJZFxyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgZXZhbHVhdGUgcnVsZSBhbmQgcmV0dXJuIG1hdGNoIHdoZW4gY29uZGl0aW9uIGlzIG1ldCcsICgpID0+IHtcclxuICAgICAgY29uc3QgcmVzdWx0cyA9IHBvbGljeS5ldmFsdWF0ZSh2YWxpZENvbnRleHQpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdHMpLnRvSGF2ZUxlbmd0aCgxKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdHNbMF0ubWF0Y2hlZCkudG9CZSh0cnVlKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdHNbMF0ucnVsZUlkKS50b0JlKCdydWxlLTEnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdHNbMF0uYWN0aW9uKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgICBleHBlY3QocmVzdWx0c1swXS5jb25maWRlbmNlKS50b0JlR3JlYXRlclRoYW4oMCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGV2YWx1YXRlIHJ1bGUgYW5kIHJldHVybiBubyBtYXRjaCB3aGVuIGNvbmRpdGlvbiBpcyBub3QgbWV0JywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBjb250ZXh0V2l0aERpZmZlcmVudFR5cGUgPSB7XHJcbiAgICAgICAgLi4udmFsaWRDb250ZXh0LFxyXG4gICAgICAgIGV2ZW50RGF0YTogeyAuLi52YWxpZENvbnRleHQuZXZlbnREYXRhLCB0eXBlOiAnbG9naW5fc3VjY2VzcycgfVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0cyA9IHBvbGljeS5ldmFsdWF0ZShjb250ZXh0V2l0aERpZmZlcmVudFR5cGUpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdHMpLnRvSGF2ZUxlbmd0aCgxKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdHNbMF0ubWF0Y2hlZCkudG9CZShmYWxzZSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHRzWzBdLmFjdGlvbikudG9CZVVuZGVmaW5lZCgpO1xyXG4gICAgICBleHBlY3QocmVzdWx0c1swXS5jb25maWRlbmNlKS50b0JlKDApO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBub3QgZXZhbHVhdGUgcnVsZXMgd2hlbiBwb2xpY3kgaXMgZGlzYWJsZWQnLCAoKSA9PiB7XHJcbiAgICAgIHBvbGljeS5kaXNhYmxlKHVzZXJJZCk7XHJcbiAgICAgIGNvbnN0IHJlc3VsdHMgPSBwb2xpY3kuZXZhbHVhdGUodmFsaWRDb250ZXh0KTtcclxuXHJcbiAgICAgIGV4cGVjdChyZXN1bHRzKS50b0hhdmVMZW5ndGgoMCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIG5vdCBldmFsdWF0ZSBkaXNhYmxlZCBydWxlcycsICgpID0+IHtcclxuICAgICAgY29uc3QgZGlzYWJsZWRSdWxlID0geyAuLi52YWxpZFJ1bGUsIGlkOiAncnVsZS0yJywgZW5hYmxlZDogZmFsc2UgfTtcclxuICAgICAgcG9saWN5LmFkZFJ1bGUoZGlzYWJsZWRSdWxlLCB1c2VySWQpO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0cyA9IHBvbGljeS5ldmFsdWF0ZSh2YWxpZENvbnRleHQpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdHMpLnRvSGF2ZUxlbmd0aCgxKTsgLy8gT25seSB0aGUgZW5hYmxlZCBydWxlXHJcbiAgICAgIGV4cGVjdChyZXN1bHRzWzBdLnJ1bGVJZCkudG9CZSgncnVsZS0xJyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGV2YWx1YXRlIHJ1bGVzIGluIHByaW9yaXR5IG9yZGVyJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBoaWdoUHJpb3JpdHlSdWxlID0ge1xyXG4gICAgICAgIC4uLnZhbGlkUnVsZSxcclxuICAgICAgICBpZDogJ3J1bGUtaGlnaCcsXHJcbiAgICAgICAgcHJpb3JpdHk6IDEwMCxcclxuICAgICAgICBzZXZlcml0eTogU2VjdXJpdHlSdWxlU2V2ZXJpdHkuSElHSFxyXG4gICAgICB9O1xyXG4gICAgICBcclxuICAgICAgY29uc3QgbG93UHJpb3JpdHlSdWxlID0ge1xyXG4gICAgICAgIC4uLnZhbGlkUnVsZSxcclxuICAgICAgICBpZDogJ3J1bGUtbG93JyxcclxuICAgICAgICBwcmlvcml0eTogMTAsXHJcbiAgICAgICAgc2V2ZXJpdHk6IFNlY3VyaXR5UnVsZVNldmVyaXR5LkxPV1xyXG4gICAgICB9O1xyXG5cclxuICAgICAgcG9saWN5LmFkZFJ1bGUoaGlnaFByaW9yaXR5UnVsZSwgdXNlcklkKTtcclxuICAgICAgcG9saWN5LmFkZFJ1bGUobG93UHJpb3JpdHlSdWxlLCB1c2VySWQpO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0cyA9IHBvbGljeS5ldmFsdWF0ZSh2YWxpZENvbnRleHQpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdHMpLnRvSGF2ZUxlbmd0aCgzKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdHNbMF0ucnVsZUlkKS50b0JlKCdydWxlLWhpZ2gnKTsgLy8gSGlnaGVzdCBwcmlvcml0eSBmaXJzdFxyXG4gICAgICBleHBlY3QocmVzdWx0c1sxXS5ydWxlSWQpLnRvQmUoJ3J1bGUtMScpOyAvLyBNZWRpdW0gcHJpb3JpdHlcclxuICAgICAgZXhwZWN0KHJlc3VsdHNbMl0ucnVsZUlkKS50b0JlKCdydWxlLWxvdycpOyAvLyBMb3dlc3QgcHJpb3JpdHlcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgc3RvcCBldmFsdWF0aW9uIGFmdGVyIGNyaXRpY2FsIHJ1bGUgbWF0Y2gnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGNyaXRpY2FsUnVsZSA9IHtcclxuICAgICAgICAuLi52YWxpZFJ1bGUsXHJcbiAgICAgICAgaWQ6ICdydWxlLWNyaXRpY2FsJyxcclxuICAgICAgICBwcmlvcml0eTogMTAwLFxyXG4gICAgICAgIHNldmVyaXR5OiBTZWN1cml0eVJ1bGVTZXZlcml0eS5DUklUSUNBTFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3Qgbm9ybWFsUnVsZSA9IHtcclxuICAgICAgICAuLi52YWxpZFJ1bGUsXHJcbiAgICAgICAgaWQ6ICdydWxlLW5vcm1hbCcsXHJcbiAgICAgICAgcHJpb3JpdHk6IDUwLFxyXG4gICAgICAgIHNldmVyaXR5OiBTZWN1cml0eVJ1bGVTZXZlcml0eS5NRURJVU1cclxuICAgICAgfTtcclxuXHJcbiAgICAgIHBvbGljeS5hZGRSdWxlKGNyaXRpY2FsUnVsZSwgdXNlcklkKTtcclxuICAgICAgcG9saWN5LmFkZFJ1bGUobm9ybWFsUnVsZSwgdXNlcklkKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdHMgPSBwb2xpY3kuZXZhbHVhdGUodmFsaWRDb250ZXh0KTtcclxuXHJcbiAgICAgIC8vIFNob3VsZCBzdG9wIGFmdGVyIGNyaXRpY2FsIHJ1bGUgbWF0Y2hlc1xyXG4gICAgICBleHBlY3QocmVzdWx0cykudG9IYXZlTGVuZ3RoKDEpO1xyXG4gICAgICBleHBlY3QocmVzdWx0c1swXS5ydWxlSWQpLnRvQmUoJ3J1bGUtY3JpdGljYWwnKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnY29tcGxleCBydWxlIGNvbmRpdGlvbnMnLCAoKSA9PiB7XHJcbiAgICBsZXQgcG9saWN5OiBTZWN1cml0eVBvbGljeTtcclxuXHJcbiAgICBiZWZvcmVFYWNoKCgpID0+IHtcclxuICAgICAgcG9saWN5ID0gU2VjdXJpdHlQb2xpY3kuY3JlYXRlKHtcclxuICAgICAgICBuYW1lOiAnVGVzdCBQb2xpY3knLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQSB0ZXN0IHBvbGljeScsXHJcbiAgICAgICAgdmVyc2lvbjogJzEuMC4wJyxcclxuICAgICAgICB0ZW5hbnRJZCxcclxuICAgICAgICBydWxlczogW10sXHJcbiAgICAgICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgICAgICBjcmVhdGVkQnk6IHVzZXJJZFxyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgZXZhbHVhdGUgQU5EIGNvbmRpdGlvbnMgY29ycmVjdGx5JywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBhbmRSdWxlOiBTZWN1cml0eVJ1bGUgPSB7XHJcbiAgICAgICAgLi4udmFsaWRSdWxlLFxyXG4gICAgICAgIGlkOiAnYW5kLXJ1bGUnLFxyXG4gICAgICAgIGNvbmRpdGlvbjogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgYW5kOiBbXHJcbiAgICAgICAgICAgIHsgZmllbGQ6ICdldmVudERhdGEudHlwZScsIG9wZXJhdG9yOiAnZXF1YWxzJywgdmFsdWU6ICdsb2dpbl9mYWlsZWQnIH0sXHJcbiAgICAgICAgICAgIHsgZmllbGQ6ICdldmVudERhdGEuYXR0ZW1wdHMnLCBvcGVyYXRvcjogJ2dyZWF0ZXJfdGhhbicsIHZhbHVlOiAyIH1cclxuICAgICAgICAgIF1cclxuICAgICAgICB9KVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgcG9saWN5LmFkZFJ1bGUoYW5kUnVsZSwgdXNlcklkKTtcclxuICAgICAgY29uc3QgcmVzdWx0cyA9IHBvbGljeS5ldmFsdWF0ZSh2YWxpZENvbnRleHQpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdHNbMF0ubWF0Y2hlZCkudG9CZSh0cnVlKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgZXZhbHVhdGUgT1IgY29uZGl0aW9ucyBjb3JyZWN0bHknLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IG9yUnVsZTogU2VjdXJpdHlSdWxlID0ge1xyXG4gICAgICAgIC4uLnZhbGlkUnVsZSxcclxuICAgICAgICBpZDogJ29yLXJ1bGUnLFxyXG4gICAgICAgIGNvbmRpdGlvbjogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgb3I6IFtcclxuICAgICAgICAgICAgeyBmaWVsZDogJ2V2ZW50RGF0YS50eXBlJywgb3BlcmF0b3I6ICdlcXVhbHMnLCB2YWx1ZTogJ2xvZ2luX2ZhaWxlZCcgfSxcclxuICAgICAgICAgICAgeyBmaWVsZDogJ2V2ZW50RGF0YS50eXBlJywgb3BlcmF0b3I6ICdlcXVhbHMnLCB2YWx1ZTogJ3Bhc3N3b3JkX3Jlc2V0JyB9XHJcbiAgICAgICAgICBdXHJcbiAgICAgICAgfSlcclxuICAgICAgfTtcclxuXHJcbiAgICAgIHBvbGljeS5hZGRSdWxlKG9yUnVsZSwgdXNlcklkKTtcclxuICAgICAgY29uc3QgcmVzdWx0cyA9IHBvbGljeS5ldmFsdWF0ZSh2YWxpZENvbnRleHQpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdHNbMF0ubWF0Y2hlZCkudG9CZSh0cnVlKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgZXZhbHVhdGUgTk9UIGNvbmRpdGlvbnMgY29ycmVjdGx5JywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBub3RSdWxlOiBTZWN1cml0eVJ1bGUgPSB7XHJcbiAgICAgICAgLi4udmFsaWRSdWxlLFxyXG4gICAgICAgIGlkOiAnbm90LXJ1bGUnLFxyXG4gICAgICAgIGNvbmRpdGlvbjogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgbm90OiB7XHJcbiAgICAgICAgICAgIGZpZWxkOiAnZXZlbnREYXRhLnR5cGUnLFxyXG4gICAgICAgICAgICBvcGVyYXRvcjogJ2VxdWFscycsXHJcbiAgICAgICAgICAgIHZhbHVlOiAnbG9naW5fc3VjY2VzcydcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgcG9saWN5LmFkZFJ1bGUobm90UnVsZSwgdXNlcklkKTtcclxuICAgICAgY29uc3QgcmVzdWx0cyA9IHBvbGljeS5ldmFsdWF0ZSh2YWxpZENvbnRleHQpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdHNbMF0ubWF0Y2hlZCkudG9CZSh0cnVlKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgZXZhbHVhdGUgbmVzdGVkIGNvbmRpdGlvbnMgY29ycmVjdGx5JywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBuZXN0ZWRSdWxlOiBTZWN1cml0eVJ1bGUgPSB7XHJcbiAgICAgICAgLi4udmFsaWRSdWxlLFxyXG4gICAgICAgIGlkOiAnbmVzdGVkLXJ1bGUnLFxyXG4gICAgICAgIGNvbmRpdGlvbjogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgYW5kOiBbXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICBvcjogW1xyXG4gICAgICAgICAgICAgICAgeyBmaWVsZDogJ2V2ZW50RGF0YS50eXBlJywgb3BlcmF0b3I6ICdlcXVhbHMnLCB2YWx1ZTogJ2xvZ2luX2ZhaWxlZCcgfSxcclxuICAgICAgICAgICAgICAgIHsgZmllbGQ6ICdldmVudERhdGEudHlwZScsIG9wZXJhdG9yOiAnZXF1YWxzJywgdmFsdWU6ICdwYXNzd29yZF9yZXNldCcgfVxyXG4gICAgICAgICAgICAgIF1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgeyBmaWVsZDogJ2V2ZW50RGF0YS5hdHRlbXB0cycsIG9wZXJhdG9yOiAnZ3JlYXRlcl90aGFuJywgdmFsdWU6IDEgfVxyXG4gICAgICAgICAgXVxyXG4gICAgICAgIH0pXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBwb2xpY3kuYWRkUnVsZShuZXN0ZWRSdWxlLCB1c2VySWQpO1xyXG4gICAgICBjb25zdCByZXN1bHRzID0gcG9saWN5LmV2YWx1YXRlKHZhbGlkQ29udGV4dCk7XHJcblxyXG4gICAgICBleHBlY3QocmVzdWx0c1swXS5tYXRjaGVkKS50b0JlKHRydWUpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBoYW5kbGUgdmFyaW91cyBmaWVsZCBvcGVyYXRvcnMnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHRlc3RDYXNlcyA9IFtcclxuICAgICAgICB7IG9wZXJhdG9yOiAnY29udGFpbnMnLCBmaWVsZDogJ2V2ZW50RGF0YS5zb3VyY2VJcCcsIHZhbHVlOiAnMTkyLjE2OCcsIGV4cGVjdGVkOiB0cnVlIH0sXHJcbiAgICAgICAgeyBvcGVyYXRvcjogJ3N0YXJ0c193aXRoJywgZmllbGQ6ICdldmVudERhdGEuc291cmNlSXAnLCB2YWx1ZTogJzE5MicsIGV4cGVjdGVkOiB0cnVlIH0sXHJcbiAgICAgICAgeyBvcGVyYXRvcjogJ2VuZHNfd2l0aCcsIGZpZWxkOiAnZXZlbnREYXRhLnNvdXJjZUlwJywgdmFsdWU6ICcxMDAnLCBleHBlY3RlZDogdHJ1ZSB9LFxyXG4gICAgICAgIHsgb3BlcmF0b3I6ICdpbicsIGZpZWxkOiAnZXZlbnREYXRhLnR5cGUnLCB2YWx1ZTogWydsb2dpbl9mYWlsZWQnLCAnbG9naW5fc3VjY2VzcyddLCBleHBlY3RlZDogdHJ1ZSB9LFxyXG4gICAgICAgIHsgb3BlcmF0b3I6ICdyZWdleCcsIGZpZWxkOiAnZXZlbnREYXRhLnNvdXJjZUlwJywgdmFsdWU6ICdeMTkyXFxcXC4xNjhcXFxcLlxcXFxkK1xcXFwuXFxcXGQrJCcsIGV4cGVjdGVkOiB0cnVlIH1cclxuICAgICAgXTtcclxuXHJcbiAgICAgIHRlc3RDYXNlcy5mb3JFYWNoKCh7IG9wZXJhdG9yLCBmaWVsZCwgdmFsdWUsIGV4cGVjdGVkIH0sIGluZGV4KSA9PiB7XHJcbiAgICAgICAgY29uc3QgcnVsZTogU2VjdXJpdHlSdWxlID0ge1xyXG4gICAgICAgICAgLi4udmFsaWRSdWxlLFxyXG4gICAgICAgICAgaWQ6IGB0ZXN0LXJ1bGUtJHtpbmRleH1gLFxyXG4gICAgICAgICAgY29uZGl0aW9uOiBKU09OLnN0cmluZ2lmeSh7IGZpZWxkLCBvcGVyYXRvciwgdmFsdWUgfSlcclxuICAgICAgICB9O1xyXG5cclxuICAgICAgICBwb2xpY3kuYWRkUnVsZShydWxlLCB1c2VySWQpO1xyXG4gICAgICAgIGNvbnN0IHJlc3VsdHMgPSBwb2xpY3kuZXZhbHVhdGUodmFsaWRDb250ZXh0KTtcclxuICAgICAgICBjb25zdCByZXN1bHQgPSByZXN1bHRzLmZpbmQociA9PiByLnJ1bGVJZCA9PT0gYHRlc3QtcnVsZS0ke2luZGV4fWApO1xyXG5cclxuICAgICAgICBleHBlY3QocmVzdWx0Py5tYXRjaGVkKS50b0JlKGV4cGVjdGVkKTtcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ3J1bGUgbWFuYWdlbWVudCcsICgpID0+IHtcclxuICAgIGxldCBwb2xpY3k6IFNlY3VyaXR5UG9saWN5O1xyXG5cclxuICAgIGJlZm9yZUVhY2goKCkgPT4ge1xyXG4gICAgICBwb2xpY3kgPSBTZWN1cml0eVBvbGljeS5jcmVhdGUoe1xyXG4gICAgICAgIG5hbWU6ICdUZXN0IFBvbGljeScsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdBIHRlc3QgcG9saWN5JyxcclxuICAgICAgICB2ZXJzaW9uOiAnMS4wLjAnLFxyXG4gICAgICAgIHRlbmFudElkLFxyXG4gICAgICAgIHJ1bGVzOiBbdmFsaWRSdWxlXSxcclxuICAgICAgICBlbmFibGVkOiB0cnVlLFxyXG4gICAgICAgIGNyZWF0ZWRCeTogdXNlcklkXHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBhZGQgYSBuZXcgcnVsZScsICgpID0+IHtcclxuICAgICAgY29uc3QgbmV3UnVsZSA9IHsgLi4udmFsaWRSdWxlLCBpZDogJ3J1bGUtMicsIG5hbWU6ICdOZXcgUnVsZScgfTtcclxuICAgICAgcG9saWN5LmFkZFJ1bGUobmV3UnVsZSwgdXNlcklkKTtcclxuXHJcbiAgICAgIGV4cGVjdChwb2xpY3kucnVsZXMpLnRvSGF2ZUxlbmd0aCgyKTtcclxuICAgICAgZXhwZWN0KHBvbGljeS5ydWxlcy5maW5kKHIgPT4gci5pZCA9PT0gJ3J1bGUtMicpKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyBlcnJvciB3aGVuIGFkZGluZyBkdXBsaWNhdGUgcnVsZSBJRCcsICgpID0+IHtcclxuICAgICAgZXhwZWN0KCgpID0+IHtcclxuICAgICAgICBwb2xpY3kuYWRkUnVsZSh2YWxpZFJ1bGUsIHVzZXJJZCk7XHJcbiAgICAgIH0pLnRvVGhyb3coVmFsaWRhdGlvbkV4Y2VwdGlvbik7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHVwZGF0ZSBhbiBleGlzdGluZyBydWxlJywgKCkgPT4ge1xyXG4gICAgICBwb2xpY3kudXBkYXRlUnVsZSgncnVsZS0xJywgeyBuYW1lOiAnVXBkYXRlZCBSdWxlJyB9LCB1c2VySWQpO1xyXG5cclxuICAgICAgY29uc3QgdXBkYXRlZFJ1bGUgPSBwb2xpY3kucnVsZXMuZmluZChyID0+IHIuaWQgPT09ICdydWxlLTEnKTtcclxuICAgICAgZXhwZWN0KHVwZGF0ZWRSdWxlPy5uYW1lKS50b0JlKCdVcGRhdGVkIFJ1bGUnKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgZXJyb3Igd2hlbiB1cGRhdGluZyBub24tZXhpc3RlbnQgcnVsZScsICgpID0+IHtcclxuICAgICAgZXhwZWN0KCgpID0+IHtcclxuICAgICAgICBwb2xpY3kudXBkYXRlUnVsZSgnbm9uLWV4aXN0ZW50JywgeyBuYW1lOiAnVXBkYXRlZCcgfSwgdXNlcklkKTtcclxuICAgICAgfSkudG9UaHJvdyhWYWxpZGF0aW9uRXhjZXB0aW9uKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgcmVtb3ZlIGEgcnVsZScsICgpID0+IHtcclxuICAgICAgcG9saWN5LnJlbW92ZVJ1bGUoJ3J1bGUtMScsIHVzZXJJZCk7XHJcblxyXG4gICAgICBleHBlY3QocG9saWN5LnJ1bGVzKS50b0hhdmVMZW5ndGgoMCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IGVycm9yIHdoZW4gcmVtb3Zpbmcgbm9uLWV4aXN0ZW50IHJ1bGUnLCAoKSA9PiB7XHJcbiAgICAgIGV4cGVjdCgoKSA9PiB7XHJcbiAgICAgICAgcG9saWN5LnJlbW92ZVJ1bGUoJ25vbi1leGlzdGVudCcsIHVzZXJJZCk7XHJcbiAgICAgIH0pLnRvVGhyb3coVmFsaWRhdGlvbkV4Y2VwdGlvbik7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ3BvbGljeSBzdGF0ZSBtYW5hZ2VtZW50JywgKCkgPT4ge1xyXG4gICAgbGV0IHBvbGljeTogU2VjdXJpdHlQb2xpY3k7XHJcblxyXG4gICAgYmVmb3JlRWFjaCgoKSA9PiB7XHJcbiAgICAgIHBvbGljeSA9IFNlY3VyaXR5UG9saWN5LmNyZWF0ZSh7XHJcbiAgICAgICAgbmFtZTogJ1Rlc3QgUG9saWN5JyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogJ0EgdGVzdCBwb2xpY3knLFxyXG4gICAgICAgIHZlcnNpb246ICcxLjAuMCcsXHJcbiAgICAgICAgdGVuYW50SWQsXHJcbiAgICAgICAgcnVsZXM6IFt2YWxpZFJ1bGVdLFxyXG4gICAgICAgIGVuYWJsZWQ6IHRydWUsXHJcbiAgICAgICAgY3JlYXRlZEJ5OiB1c2VySWRcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGVuYWJsZSBwb2xpY3knLCAoKSA9PiB7XHJcbiAgICAgIHBvbGljeS5kaXNhYmxlKHVzZXJJZCk7XHJcbiAgICAgIGV4cGVjdChwb2xpY3kuZW5hYmxlZCkudG9CZShmYWxzZSk7XHJcblxyXG4gICAgICBwb2xpY3kuZW5hYmxlKHVzZXJJZCk7XHJcbiAgICAgIGV4cGVjdChwb2xpY3kuZW5hYmxlZCkudG9CZSh0cnVlKTtcclxuICAgICAgZXhwZWN0KHBvbGljeS5sYXN0TW9kaWZpZWRCeSkudG9CZSh1c2VySWQpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBkaXNhYmxlIHBvbGljeScsICgpID0+IHtcclxuICAgICAgcG9saWN5LmRpc2FibGUodXNlcklkKTtcclxuICAgICAgZXhwZWN0KHBvbGljeS5lbmFibGVkKS50b0JlKGZhbHNlKTtcclxuICAgICAgZXhwZWN0KHBvbGljeS5sYXN0TW9kaWZpZWRCeSkudG9CZSh1c2VySWQpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdhdWRpdCB0cmFpbCcsICgpID0+IHtcclxuICAgIGxldCBwb2xpY3k6IFNlY3VyaXR5UG9saWN5O1xyXG5cclxuICAgIGJlZm9yZUVhY2goKCkgPT4ge1xyXG4gICAgICBwb2xpY3kgPSBTZWN1cml0eVBvbGljeS5jcmVhdGUoe1xyXG4gICAgICAgIG5hbWU6ICdUZXN0IFBvbGljeScsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdBIHRlc3QgcG9saWN5JyxcclxuICAgICAgICB2ZXJzaW9uOiAnMS4wLjAnLFxyXG4gICAgICAgIHRlbmFudElkLFxyXG4gICAgICAgIHJ1bGVzOiBbdmFsaWRSdWxlXSxcclxuICAgICAgICBlbmFibGVkOiB0cnVlLFxyXG4gICAgICAgIGNyZWF0ZWRCeTogdXNlcklkXHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjcmVhdGUgYXVkaXQgZW50cmllcyB3aGVuIGV2YWx1YXRpbmcgcnVsZXMnLCAoKSA9PiB7XHJcbiAgICAgIHBvbGljeS5ldmFsdWF0ZSh2YWxpZENvbnRleHQpO1xyXG5cclxuICAgICAgZXhwZWN0KHBvbGljeS5hdWRpdFRyYWlsKS50b0hhdmVMZW5ndGgoMSk7XHJcbiAgICAgIGV4cGVjdChwb2xpY3kuYXVkaXRUcmFpbFswXS5ydWxlSWQpLnRvQmUoJ3J1bGUtMScpO1xyXG4gICAgICBleHBlY3QocG9saWN5LmF1ZGl0VHJhaWxbMF0uZXhlY3V0aW9uU3RhdHVzKS50b0JlKFBvbGljeUV4ZWN1dGlvblN0YXR1cy5QRU5ESU5HKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgbWFpbnRhaW4gYXVkaXQgdHJhaWwgaGlzdG9yeScsICgpID0+IHtcclxuICAgICAgLy8gRXZhbHVhdGUgbXVsdGlwbGUgdGltZXNcclxuICAgICAgcG9saWN5LmV2YWx1YXRlKHZhbGlkQ29udGV4dCk7XHJcbiAgICAgIHBvbGljeS5ldmFsdWF0ZSh2YWxpZENvbnRleHQpO1xyXG5cclxuICAgICAgZXhwZWN0KHBvbGljeS5hdWRpdFRyYWlsKS50b0hhdmVMZW5ndGgoMik7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2NvbmZpZGVuY2UgY2FsY3VsYXRpb24nLCAoKSA9PiB7XHJcbiAgICBsZXQgcG9saWN5OiBTZWN1cml0eVBvbGljeTtcclxuXHJcbiAgICBiZWZvcmVFYWNoKCgpID0+IHtcclxuICAgICAgcG9saWN5ID0gU2VjdXJpdHlQb2xpY3kuY3JlYXRlKHtcclxuICAgICAgICBuYW1lOiAnVGVzdCBQb2xpY3knLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQSB0ZXN0IHBvbGljeScsXHJcbiAgICAgICAgdmVyc2lvbjogJzEuMC4wJyxcclxuICAgICAgICB0ZW5hbnRJZCxcclxuICAgICAgICBydWxlczogW10sXHJcbiAgICAgICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgICAgICBjcmVhdGVkQnk6IHVzZXJJZFxyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY2FsY3VsYXRlIGhpZ2hlciBjb25maWRlbmNlIGZvciBjcml0aWNhbCBzZXZlcml0eSBydWxlcycsICgpID0+IHtcclxuICAgICAgY29uc3QgY3JpdGljYWxSdWxlID0geyAuLi52YWxpZFJ1bGUsIGlkOiAnY3JpdGljYWwnLCBzZXZlcml0eTogU2VjdXJpdHlSdWxlU2V2ZXJpdHkuQ1JJVElDQUwgfTtcclxuICAgICAgY29uc3QgbG93UnVsZSA9IHsgLi4udmFsaWRSdWxlLCBpZDogJ2xvdycsIHNldmVyaXR5OiBTZWN1cml0eVJ1bGVTZXZlcml0eS5MT1cgfTtcclxuXHJcbiAgICAgIHBvbGljeS5hZGRSdWxlKGNyaXRpY2FsUnVsZSwgdXNlcklkKTtcclxuICAgICAgcG9saWN5LmFkZFJ1bGUobG93UnVsZSwgdXNlcklkKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdHMgPSBwb2xpY3kuZXZhbHVhdGUodmFsaWRDb250ZXh0KTtcclxuICAgICAgY29uc3QgY3JpdGljYWxSZXN1bHQgPSByZXN1bHRzLmZpbmQociA9PiByLnJ1bGVJZCA9PT0gJ2NyaXRpY2FsJyk7XHJcbiAgICAgIGNvbnN0IGxvd1Jlc3VsdCA9IHJlc3VsdHMuZmluZChyID0+IHIucnVsZUlkID09PSAnbG93Jyk7XHJcblxyXG4gICAgICBleHBlY3QoY3JpdGljYWxSZXN1bHQ/LmNvbmZpZGVuY2UpLnRvQmVHcmVhdGVyVGhhbihsb3dSZXN1bHQ/LmNvbmZpZGVuY2UgfHwgMCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGFkanVzdCBjb25maWRlbmNlIGJhc2VkIG9uIHRocmVhdCBjb250ZXh0JywgKCkgPT4ge1xyXG4gICAgICBwb2xpY3kuYWRkUnVsZSh2YWxpZFJ1bGUsIHVzZXJJZCk7XHJcblxyXG4gICAgICBjb25zdCBjb250ZXh0V2l0aFRocmVhdCA9IHtcclxuICAgICAgICAuLi52YWxpZENvbnRleHQsXHJcbiAgICAgICAgdGhyZWF0Q29udGV4dDoge1xyXG4gICAgICAgICAgdGhyZWF0TGV2ZWw6ICdISUdIJyxcclxuICAgICAgICAgIGluZGljYXRvcnM6IFsnbWFsaWNpb3VzLWlwJ10sXHJcbiAgICAgICAgICBjb25maWRlbmNlOiAwLjhcclxuICAgICAgICB9XHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCByZXN1bHRzID0gcG9saWN5LmV2YWx1YXRlKGNvbnRleHRXaXRoVGhyZWF0KTtcclxuICAgICAgZXhwZWN0KHJlc3VsdHNbMF0uY29uZmlkZW5jZSkudG9CZUdyZWF0ZXJUaGFuKDAuOCk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2Vycm9yIGhhbmRsaW5nJywgKCkgPT4ge1xyXG4gICAgbGV0IHBvbGljeTogU2VjdXJpdHlQb2xpY3k7XHJcblxyXG4gICAgYmVmb3JlRWFjaCgoKSA9PiB7XHJcbiAgICAgIHBvbGljeSA9IFNlY3VyaXR5UG9saWN5LmNyZWF0ZSh7XHJcbiAgICAgICAgbmFtZTogJ1Rlc3QgUG9saWN5JyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogJ0EgdGVzdCBwb2xpY3knLFxyXG4gICAgICAgIHZlcnNpb246ICcxLjAuMCcsXHJcbiAgICAgICAgdGVuYW50SWQsXHJcbiAgICAgICAgcnVsZXM6IFtdLFxyXG4gICAgICAgIGVuYWJsZWQ6IHRydWUsXHJcbiAgICAgICAgY3JlYXRlZEJ5OiB1c2VySWRcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGhhbmRsZSBpbnZhbGlkIHJ1bGUgY29uZGl0aW9ucyBncmFjZWZ1bGx5JywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkUnVsZSA9IHsgLi4udmFsaWRSdWxlLCBjb25kaXRpb246ICd7XCJmaWVsZFwiOntcIm5lc3RlZFwiOlwiaW52YWxpZFwifSxcIm9wZXJhdG9yXCI6XCJlcXVhbHNcIixcInZhbHVlXCI6XCJ0ZXN0XCJ9JyB9O1xyXG4gICAgICBwb2xpY3kuYWRkUnVsZShpbnZhbGlkUnVsZSwgdXNlcklkKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdHMgPSBwb2xpY3kuZXZhbHVhdGUodmFsaWRDb250ZXh0KTtcclxuXHJcbiAgICAgIGV4cGVjdChyZXN1bHRzKS50b0hhdmVMZW5ndGgoMSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHRzWzBdLm1hdGNoZWQpLnRvQmUoZmFsc2UpO1xyXG4gICAgICBleHBlY3QocmVzdWx0c1swXS5yZWFzb24pLnRvQ29udGFpbignZXZhbHVhdGlvbiBmYWlsZWQnKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY29udGludWUgZXZhbHVhdGlvbiBhZnRlciBydWxlIGVycm9yJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkUnVsZSA9IHsgLi4udmFsaWRSdWxlLCBpZDogJ2ludmFsaWQnLCBjb25kaXRpb246ICd7XCJpbnZhbGlkXCI6IFwic3RydWN0dXJlXCJ9JyB9O1xyXG4gICAgICBjb25zdCB2YWxpZFJ1bGUyID0geyAuLi52YWxpZFJ1bGUsIGlkOiAndmFsaWQnIH07XHJcblxyXG4gICAgICBwb2xpY3kuYWRkUnVsZShpbnZhbGlkUnVsZSwgdXNlcklkKTtcclxuICAgICAgcG9saWN5LmFkZFJ1bGUodmFsaWRSdWxlMiwgdXNlcklkKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdHMgPSBwb2xpY3kuZXZhbHVhdGUodmFsaWRDb250ZXh0KTtcclxuXHJcbiAgICAgIGV4cGVjdChyZXN1bHRzKS50b0hhdmVMZW5ndGgoMik7XHJcbiAgICAgIGV4cGVjdChyZXN1bHRzLmZpbmQociA9PiByLnJ1bGVJZCA9PT0gJ2ludmFsaWQnKT8ubWF0Y2hlZCkudG9CZShmYWxzZSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHRzLmZpbmQociA9PiByLnJ1bGVJZCA9PT0gJ3ZhbGlkJyk/Lm1hdGNoZWQpLnRvQmUodHJ1ZSk7XHJcbiAgICB9KTtcclxuICB9KTtcclxufSk7Il0sInZlcnNpb24iOjN9