{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\monitoring.config.ts", "mappings": ";;;AAAA,2CAA4C;AAqJ5C,4DAA4D;AAC5D,MAAM,YAAY,GAAG,CAAC,KAAyB,EAAE,YAAY,GAAG,KAAK,EAAW,EAAE;IAChF,IAAI,KAAK,KAAK,SAAS;QAAE,OAAO,YAAY,CAAC;IAC7C,OAAO,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC;AACxC,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,KAAyB,EAAE,YAAoB,EAAU,EAAE;IAC9E,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;IACzC,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC;AAC/C,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,KAAyB,EAAE,YAAoB,EAAU,EAAE;IACjF,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;IACvC,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC;AAC/C,CAAC,CAAC;AAEF,MAAM,gBAAgB,GAAG,CAAC,KAAyB,EAAE,SAAS,GAAG,GAAG,EAAY,EAAE;IAChF,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACtE,CAAC,CAAC;AAEF,MAAM,aAAa,GAAG,CAAI,KAAyB,EAAE,YAAe,EAAK,EAAE;IACzE,IAAI,CAAC,KAAK;QAAE,OAAO,YAAY,CAAC;IAChC,IAAI,CAAC;QACH,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAM,CAAC;IAChC,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,YAAY,CAAC;IACtB,CAAC;AACH,CAAC,CAAC;AAEF,uCAAuC;AACvC,MAAM,gBAAgB,GAAG;IACvB,YAAY,EAAE,IAAI;IAClB,QAAQ,EAAE,IAAI;IACd,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,IAAI;IAChB,gBAAgB,EAAE,IAAI;CACd,CAAC;AAEX,MAAM,kBAAkB,GAAG;IACzB,SAAS,EAAE,GAAG;IACd,YAAY,EAAE,EAAE;IAChB,eAAe,EAAE,IAAI;IACrB,oBAAoB,EAAE,EAAE;IACxB,aAAa,EAAE,GAAG;IAClB,UAAU,EAAE,IAAI;IAChB,gBAAgB,EAAE,IAAI;CACd,CAAC;AAEX,MAAM,iBAAiB,GAAG;IACxB,iBAAiB,EAAE,KAAK;IACxB,cAAc,EAAE,KAAK;IACrB,qBAAqB,EAAE,KAAK;IAC5B,YAAY,EAAE,GAAG;CACT,CAAC;AAEX,MAAM,cAAc,GAAmB;IACrC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC1C,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IACpD,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC9C,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;CACpC,CAAC;AAEF;;;GAGG;AACU,QAAA,gBAAgB,GAAG,IAAA,mBAAU,EAAC,YAAY,EAAE,GAAqB,EAAE,CAAC,CAAC;IAChF,MAAM,EAAE;QACN,OAAO,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,IAAI,CAAC;QACjE,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,SAAS;QAC3D,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,gBAAgB,CAAC,YAAY,CAAC;QACxF,MAAM,EAAE;YACN,QAAQ,EAAE;gBACR,OAAO,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,EAAE,IAAI,CAAC;gBAC1E,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC;gBAC7F,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,IAAI,UAAU;aAChE;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,EAAE,IAAI,CAAC;gBACvE,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,EAAE,gBAAgB,CAAC,KAAK,CAAC;gBACvF,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,IAAI,MAAM;aAC7D;YACD,MAAM,EAAE;gBACN,OAAO,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,EAAE,IAAI,CAAC;gBACxE,aAAa,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,EAAE,kBAAkB,CAAC,SAAS,CAAC;gBAC3G,YAAY,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,EAAE,kBAAkB,CAAC,SAAS,CAAC;aAC1G;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;gBAC/D,SAAS,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,EAAE,kBAAkB,CAAC,YAAY,CAAC;gBACnG,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,GAAG;aACnD;YACD,QAAQ,EAAE;gBACR,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBACnE,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,YAAY;wBAClB,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,SAAS;wBAC9C,OAAO,EAAE,gBAAgB,CAAC,gBAAgB;wBAC1C,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;qBACzD;oBACD;wBACE,IAAI,EAAE,kBAAkB;wBACxB,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,SAAS;wBAChD,OAAO,EAAE,gBAAgB,CAAC,gBAAgB;wBAC1C,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;qBAC3D;iBACF,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC;aACrC;SACF;KACF;IAED,OAAO,EAAE;QACP,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACrD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,UAAU;QACvD,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC;QACpD,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,UAAU;QAC/C,UAAU,EAAE;YACV,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YACxD,aAAa,EAAE;gBACb,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,kBAAkB;gBAClD,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,OAAO;gBAC9C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,aAAa;aACtD;YACD,qBAAqB,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,EAAE,IAAI,CAAC;YACrF,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,gBAAgB,CAAC,UAAU,CAAC;SACrF;QACD,MAAM,EAAE;YACN,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YAC5D,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,WAAW;YACpD,OAAO,EAAE,cAAc;YACvB,QAAQ,EAAE;gBACR,IAAI,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,IAAI,CAAC;gBAC9D,QAAQ,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;gBAC/D,KAAK,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;gBACzD,EAAE,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;aACpD;SACF;KACF;IAED,WAAW,EAAE;QACX,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QACpE,UAAU,EAAE;YACV,QAAQ,EAAE;gBACR,OAAO,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,EAAE,IAAI,CAAC;gBACzE,aAAa,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,EAAE,kBAAkB,CAAC,eAAe,CAAC;gBACjH,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBACnE,cAAc,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;aAC1E;YACD,MAAM,EAAE;gBACN,MAAM,EAAE;oBACN,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;oBAChE,QAAQ,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,EAAE,iBAAiB,CAAC,iBAAiB,CAAC;oBACtG,cAAc,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,EAAE,kBAAkB,CAAC,oBAAoB,CAAC;iBACxH;gBACD,GAAG,EAAE;oBACH,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;oBAC7D,QAAQ,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,EAAE,iBAAiB,CAAC,cAAc,CAAC;oBAChG,cAAc,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,EAAE,kBAAkB,CAAC,oBAAoB,CAAC;iBACrH;gBACD,SAAS,EAAE;oBACT,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;oBACpE,QAAQ,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,EAAE,iBAAiB,CAAC,qBAAqB,CAAC;oBAC9G,cAAc,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,EAAE,kBAAkB,CAAC,aAAa,CAAC;iBACrH;aACF;SACF;KACF;IAED,OAAO,EAAE;QACP,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACrD,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,kBAAkB;QACtE,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,OAAO;QACrD,SAAS,EAAE;YACT,MAAM,EAAE;gBACN,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;gBACpD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,mCAAmC;gBACvF,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,WAAW;gBAC1D,SAAS,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC;gBAC9D,YAAY,EAAE,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,GAAG,CAAC;aACvE;YACD,aAAa,EAAE;gBACb,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBAClD,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,EAAE,CAAC;gBAC3G,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,EAAE,CAAC;gBACxG,YAAY,EAAE,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,GAAG,CAAC;aACrE;SACF;QACD,MAAM,EAAE;YACN,YAAY,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC;YACrE,eAAe,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACpE,aAAa,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YAChE,aAAa,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,IAAI,CAAC;YACvE,cAAc,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,EAAE,IAAI,CAAC;SAC3E;KACF;IAED,QAAQ,EAAE;QACR,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACtD,QAAQ,EAAE;YACR,KAAK,EAAE;gBACL,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;gBACzD,UAAU,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;gBACnE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBACnF,QAAQ,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,GAAG,CAAC;gBAC1D,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBACnF,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,CAAC;aAChG;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;gBACzD,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,EAAE,CAAC;gBACrG,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,SAAS;gBACxD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,cAAc;aAChE;YACD,OAAO,EAAE;gBACP,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;gBAC3D,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAClF,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,EAAE,CAAC;aACjE;SACF;QACD,KAAK,EAAE;YACL,aAAa,EAAE;gBACb,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBACnE,SAAS,EAAE,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC;gBACxG,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,EAAE,iBAAiB,CAAC,YAAY,CAAC;aACjG;YACD,gBAAgB,EAAE;gBAChB,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;gBACtE,SAAS,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,EAAE,kBAAkB,CAAC,gBAAgB,CAAC;gBAC9G,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,EAAE,iBAAiB,CAAC,YAAY,CAAC;aACpG;YACD,eAAe,EAAE;gBACf,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;gBACrE,SAAS,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,EAAE,kBAAkB,CAAC,oBAAoB,CAAC;gBACjH,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,EAAE,iBAAiB,CAAC,YAAY,CAAC;aACnG;YACD,yBAAyB,EAAE;gBACzB,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;gBACzE,SAAS,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,EAAE,CAAC,CAAC;gBAC/E,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,EAAE,EAAE,CAAC;aAC3E;SACF;KACF;CACF,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\monitoring.config.ts"], "sourcesContent": ["import { registerAs } from '@nestjs/config';\r\n\r\n// Type definitions for better type safety\r\ninterface HealthCheckService {\r\n  name: string;\r\n  url: string;\r\n  timeout: number;\r\n  enabled: boolean;\r\n}\r\n\r\ninterface AlertChannel {\r\n  enabled: boolean;\r\n}\r\n\r\ninterface EmailChannel extends AlertChannel {\r\n  recipients: string[];\r\n  smtpHost?: string;\r\n  smtpPort: number;\r\n  smtpUser?: string;\r\n  smtpPassword?: string;\r\n}\r\n\r\ninterface SlackChannel extends AlertChannel {\r\n  webhookUrl?: string;\r\n  channel: string;\r\n  username: string;\r\n}\r\n\r\ninterface WebhookChannel extends AlertChannel {\r\n  url?: string;\r\n  headers: Record<string, string>;\r\n}\r\n\r\ninterface AlertRule {\r\n  enabled: boolean;\r\n  threshold: number;\r\n  window: number;\r\n}\r\n\r\ninterface MetricsBuckets {\r\n  http: number[];\r\n  database: number[];\r\n  redis: number[];\r\n  ai: number[];\r\n}\r\n\r\nexport interface MonitoringConfig {\r\n  health: {\r\n    enabled: boolean;\r\n    endpoint: string;\r\n    timeout: number;\r\n    checks: {\r\n      database: {\r\n        enabled: boolean;\r\n        timeout: number;\r\n        query: string;\r\n      };\r\n      redis: {\r\n        enabled: boolean;\r\n        timeout: number;\r\n        command: string;\r\n      };\r\n      memory: {\r\n        enabled: boolean;\r\n        heapThreshold: number;\r\n        rssThreshold: number;\r\n      };\r\n      disk: {\r\n        enabled: boolean;\r\n        threshold: number;\r\n        path: string;\r\n      };\r\n      external: {\r\n        enabled: boolean;\r\n        services: HealthCheckService[];\r\n      };\r\n    };\r\n  };\r\n  metrics: {\r\n    enabled: boolean;\r\n    endpoint: string;\r\n    port: number;\r\n    path: string;\r\n    prometheus: {\r\n      enabled: boolean;\r\n      defaultLabels: Record<string, string>;\r\n      collectDefaultMetrics: boolean;\r\n      timeout: number;\r\n    };\r\n    custom: {\r\n      enabled: boolean;\r\n      prefix: string;\r\n      buckets: MetricsBuckets;\r\n      tracking: {\r\n        http: boolean;\r\n        database: boolean;\r\n        redis: boolean;\r\n        ai: boolean;\r\n      };\r\n    };\r\n  };\r\n  performance: {\r\n    enabled: boolean;\r\n    monitoring: {\r\n      requests: {\r\n        enabled: boolean;\r\n        slowThreshold: number;\r\n        logSlow: boolean;\r\n        trackUserAgent: boolean;\r\n      };\r\n      system: {\r\n        memory: { enabled: boolean; interval: number; alertThreshold: number };\r\n        cpu: { enabled: boolean; interval: number; alertThreshold: number };\r\n        eventLoop: { enabled: boolean; interval: number; alertThreshold: number };\r\n      };\r\n    };\r\n  };\r\n  tracing: {\r\n    enabled: boolean;\r\n    serviceName: string;\r\n    serviceVersion: string;\r\n    providers: {\r\n      jaeger: {\r\n        enabled: boolean;\r\n        endpoint: string;\r\n        agentHost: string;\r\n        agentPort: number;\r\n        samplingRate: number;\r\n      };\r\n      openTelemetry: {\r\n        enabled: boolean;\r\n        endpoint?: string;\r\n        headers?: string;\r\n        samplingRate: number;\r\n      };\r\n    };\r\n    traces: Record<string, boolean>;\r\n  };\r\n  alerting: {\r\n    enabled: boolean;\r\n    channels: {\r\n      email: EmailChannel;\r\n      slack: SlackChannel;\r\n      webhook: WebhookChannel;\r\n    };\r\n    rules: Record<string, AlertRule>;\r\n  };\r\n}\r\n\r\n// Helper functions for cleaner environment variable parsing\r\nconst parseBoolean = (value: string | undefined, defaultValue = false): boolean => {\r\n  if (value === undefined) return defaultValue;\r\n  return value.toLowerCase() === 'true';\r\n};\r\n\r\nconst parseNumber = (value: string | undefined, defaultValue: number): number => {\r\n  const parsed = parseInt(value || '', 10);\r\n  return isNaN(parsed) ? defaultValue : parsed;\r\n};\r\n\r\nconst parseFloatSafe = (value: string | undefined, defaultValue: number): number => {\r\n  const parsed = parseFloat(value || '');\r\n  return isNaN(parsed) ? defaultValue : parsed;\r\n};\r\n\r\nconst parseStringArray = (value: string | undefined, delimiter = ','): string[] => {\r\n  return value ? value.split(delimiter).map(item => item.trim()) : [];\r\n};\r\n\r\nconst parseJsonSafe = <T>(value: string | undefined, defaultValue: T): T => {\r\n  if (!value) return defaultValue;\r\n  try {\r\n    return JSON.parse(value) as T;\r\n  } catch {\r\n    return defaultValue;\r\n  }\r\n};\r\n\r\n// Constants for better maintainability\r\nconst DEFAULT_TIMEOUTS = {\r\n  HEALTH_CHECK: 5000,\r\n  DATABASE: 3000,\r\n  REDIS: 3000,\r\n  PROMETHEUS: 5000,\r\n  EXTERNAL_SERVICE: 5000,\r\n} as const;\r\n\r\nconst DEFAULT_THRESHOLDS = {\r\n  MEMORY_MB: 150,\r\n  DISK_PERCENT: 80,\r\n  SLOW_REQUEST_MS: 1000,\r\n  SYSTEM_ALERT_PERCENT: 80,\r\n  EVENT_LOOP_MS: 100,\r\n  ERROR_RATE: 0.05,\r\n  RESPONSE_TIME_MS: 2000,\r\n} as const;\r\n\r\nconst DEFAULT_INTERVALS = {\r\n  MEMORY_MONITORING: 30000,\r\n  CPU_MONITORING: 30000,\r\n  EVENT_LOOP_MONITORING: 10000,\r\n  ALERT_WINDOW: 300,\r\n} as const;\r\n\r\nconst METRIC_BUCKETS: MetricsBuckets = {\r\n  http: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],\r\n  database: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5],\r\n  redis: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1],\r\n  ai: [0.1, 0.5, 1, 2, 5, 10, 30, 60],\r\n};\r\n\r\n/**\r\n * Monitoring and observability configuration\r\n * Provides settings for health checks, metrics, and performance monitoring\r\n */\r\nexport const monitoringConfig = registerAs('monitoring', (): MonitoringConfig => ({\r\n  health: {\r\n    enabled: !parseBoolean(process.env['HEALTH_CHECK_ENABLED'], true),\r\n    endpoint: process.env['HEALTH_CHECK_ENDPOINT'] || '/health',\r\n    timeout: parseNumber(process.env['HEALTH_CHECK_TIMEOUT'], DEFAULT_TIMEOUTS.HEALTH_CHECK),\r\n    checks: {\r\n      database: {\r\n        enabled: !parseBoolean(process.env['HEALTH_CHECK_DATABASE_ENABLED'], true),\r\n        timeout: parseNumber(process.env['HEALTH_CHECK_DATABASE_TIMEOUT'], DEFAULT_TIMEOUTS.DATABASE),\r\n        query: process.env['HEALTH_CHECK_DATABASE_QUERY'] || 'SELECT 1',\r\n      },\r\n      redis: {\r\n        enabled: !parseBoolean(process.env['HEALTH_CHECK_REDIS_ENABLED'], true),\r\n        timeout: parseNumber(process.env['HEALTH_CHECK_REDIS_TIMEOUT'], DEFAULT_TIMEOUTS.REDIS),\r\n        command: process.env['HEALTH_CHECK_REDIS_COMMAND'] || 'ping',\r\n      },\r\n      memory: {\r\n        enabled: !parseBoolean(process.env['HEALTH_CHECK_MEMORY_ENABLED'], true),\r\n        heapThreshold: parseNumber(process.env['HEALTH_CHECK_MEMORY_HEAP_THRESHOLD'], DEFAULT_THRESHOLDS.MEMORY_MB),\r\n        rssThreshold: parseNumber(process.env['HEALTH_CHECK_MEMORY_RSS_THRESHOLD'], DEFAULT_THRESHOLDS.MEMORY_MB),\r\n      },\r\n      disk: {\r\n        enabled: parseBoolean(process.env['HEALTH_CHECK_DISK_ENABLED']),\r\n        threshold: parseNumber(process.env['HEALTH_CHECK_DISK_THRESHOLD'], DEFAULT_THRESHOLDS.DISK_PERCENT),\r\n        path: process.env['HEALTH_CHECK_DISK_PATH'] || '/',\r\n      },\r\n      external: {\r\n        enabled: parseBoolean(process.env['HEALTH_CHECK_EXTERNAL_ENABLED']),\r\n        services: [\r\n          {\r\n            name: 'ai-service',\r\n            url: `${process.env['AI_SERVICE_URL']}/health`,\r\n            timeout: DEFAULT_TIMEOUTS.EXTERNAL_SERVICE,\r\n            enabled: parseBoolean(process.env['AI_SERVICE_ENABLED']),\r\n          },\r\n          {\r\n            name: 'threat-intel-api',\r\n            url: `${process.env['THREAT_INTEL_URL']}/health`,\r\n            timeout: DEFAULT_TIMEOUTS.EXTERNAL_SERVICE,\r\n            enabled: parseBoolean(process.env['THREAT_INTEL_ENABLED']),\r\n          },\r\n        ].filter(service => service.enabled),\r\n      },\r\n    },\r\n  },\r\n\r\n  metrics: {\r\n    enabled: parseBoolean(process.env['METRICS_ENABLED']),\r\n    endpoint: process.env['METRICS_ENDPOINT'] || '/metrics',\r\n    port: parseNumber(process.env['METRICS_PORT'], 9090),\r\n    path: process.env['METRICS_PATH'] || '/metrics',\r\n    prometheus: {\r\n      enabled: parseBoolean(process.env['PROMETHEUS_ENABLED']),\r\n      defaultLabels: {\r\n        app: process.env['APP_NAME'] || 'sentinel-backend',\r\n        version: process.env['APP_VERSION'] || '1.0.0',\r\n        environment: process.env['NODE_ENV'] || 'development',\r\n      },\r\n      collectDefaultMetrics: !parseBoolean(process.env['PROMETHEUS_COLLECT_DEFAULT'], true),\r\n      timeout: parseNumber(process.env['PROMETHEUS_TIMEOUT'], DEFAULT_TIMEOUTS.PROMETHEUS),\r\n    },\r\n    custom: {\r\n      enabled: parseBoolean(process.env['CUSTOM_METRICS_ENABLED']),\r\n      prefix: process.env['METRICS_PREFIX'] || 'sentinel_',\r\n      buckets: METRIC_BUCKETS,\r\n      tracking: {\r\n        http: !parseBoolean(process.env['HTTP_METRICS_ENABLED'], true),\r\n        database: parseBoolean(process.env['DATABASE_METRICS_ENABLED']),\r\n        redis: parseBoolean(process.env['REDIS_METRICS_ENABLED']),\r\n        ai: parseBoolean(process.env['AI_METRICS_ENABLED']),\r\n      },\r\n    },\r\n  },\r\n\r\n  performance: {\r\n    enabled: parseBoolean(process.env['PERFORMANCE_MONITORING_ENABLED']),\r\n    monitoring: {\r\n      requests: {\r\n        enabled: !parseBoolean(process.env['PERFORMANCE_REQUESTS_ENABLED'], true),\r\n        slowThreshold: parseNumber(process.env['PERFORMANCE_SLOW_REQUEST_THRESHOLD'], DEFAULT_THRESHOLDS.SLOW_REQUEST_MS),\r\n        logSlow: parseBoolean(process.env['PERFORMANCE_LOG_SLOW_REQUESTS']),\r\n        trackUserAgent: parseBoolean(process.env['PERFORMANCE_TRACK_USER_AGENT']),\r\n      },\r\n      system: {\r\n        memory: {\r\n          enabled: parseBoolean(process.env['PERFORMANCE_MEMORY_ENABLED']),\r\n          interval: parseNumber(process.env['PERFORMANCE_MEMORY_INTERVAL'], DEFAULT_INTERVALS.MEMORY_MONITORING),\r\n          alertThreshold: parseNumber(process.env['PERFORMANCE_MEMORY_ALERT_THRESHOLD'], DEFAULT_THRESHOLDS.SYSTEM_ALERT_PERCENT),\r\n        },\r\n        cpu: {\r\n          enabled: parseBoolean(process.env['PERFORMANCE_CPU_ENABLED']),\r\n          interval: parseNumber(process.env['PERFORMANCE_CPU_INTERVAL'], DEFAULT_INTERVALS.CPU_MONITORING),\r\n          alertThreshold: parseNumber(process.env['PERFORMANCE_CPU_ALERT_THRESHOLD'], DEFAULT_THRESHOLDS.SYSTEM_ALERT_PERCENT),\r\n        },\r\n        eventLoop: {\r\n          enabled: parseBoolean(process.env['PERFORMANCE_EVENT_LOOP_ENABLED']),\r\n          interval: parseNumber(process.env['PERFORMANCE_EVENT_LOOP_INTERVAL'], DEFAULT_INTERVALS.EVENT_LOOP_MONITORING),\r\n          alertThreshold: parseNumber(process.env['PERFORMANCE_EVENT_LOOP_ALERT_THRESHOLD'], DEFAULT_THRESHOLDS.EVENT_LOOP_MS),\r\n        },\r\n      },\r\n    },\r\n  },\r\n\r\n  tracing: {\r\n    enabled: parseBoolean(process.env['TRACING_ENABLED']),\r\n    serviceName: process.env['TRACING_SERVICE_NAME'] || 'sentinel-backend',\r\n    serviceVersion: process.env['APP_VERSION'] || '1.0.0',\r\n    providers: {\r\n      jaeger: {\r\n        enabled: parseBoolean(process.env['JAEGER_ENABLED']),\r\n        endpoint: process.env['TRACING_JAEGER_ENDPOINT'] || 'http://localhost:14268/api/traces',\r\n        agentHost: process.env['JAEGER_AGENT_HOST'] || 'localhost',\r\n        agentPort: parseNumber(process.env['JAEGER_AGENT_PORT'], 6832),\r\n        samplingRate: parseFloatSafe(process.env['JAEGER_SAMPLING_RATE'], 0.1),\r\n      },\r\n      openTelemetry: {\r\n        enabled: parseBoolean(process.env['OTEL_ENABLED']),\r\n        ...(process.env['OTEL_EXPORTER_OTLP_ENDPOINT'] && { endpoint: process.env['OTEL_EXPORTER_OTLP_ENDPOINT'] }),\r\n        ...(process.env['OTEL_EXPORTER_OTLP_HEADERS'] && { headers: process.env['OTEL_EXPORTER_OTLP_HEADERS'] }),\r\n        samplingRate: parseFloatSafe(process.env['OTEL_SAMPLING_RATE'], 0.1),\r\n      },\r\n    },\r\n    traces: {\r\n      httpRequests: !parseBoolean(process.env['TRACE_HTTP_REQUESTS'], true),\r\n      databaseQueries: parseBoolean(process.env['TRACE_DATABASE_QUERIES']),\r\n      redisCommands: parseBoolean(process.env['TRACE_REDIS_COMMANDS']),\r\n      externalCalls: !parseBoolean(process.env['TRACE_EXTERNAL_CALLS'], true),\r\n      aiServiceCalls: !parseBoolean(process.env['TRACE_AI_SERVICE_CALLS'], true),\r\n    },\r\n  },\r\n\r\n  alerting: {\r\n    enabled: parseBoolean(process.env['ALERTING_ENABLED']),\r\n    channels: {\r\n      email: {\r\n        enabled: parseBoolean(process.env['ALERT_EMAIL_ENABLED']),\r\n        recipients: parseStringArray(process.env['ALERT_EMAIL_RECIPIENTS']),\r\n        ...(process.env['ALERT_SMTP_HOST'] && { smtpHost: process.env['ALERT_SMTP_HOST'] }),\r\n        smtpPort: parseNumber(process.env['ALERT_SMTP_PORT'], 587),\r\n        ...(process.env['ALERT_SMTP_USER'] && { smtpUser: process.env['ALERT_SMTP_USER'] }),\r\n        ...(process.env['ALERT_SMTP_PASSWORD'] && { smtpPassword: process.env['ALERT_SMTP_PASSWORD'] }),\r\n      },\r\n      slack: {\r\n        enabled: parseBoolean(process.env['ALERT_SLACK_ENABLED']),\r\n        ...(process.env['ALERT_SLACK_WEBHOOK_URL'] && { webhookUrl: process.env['ALERT_SLACK_WEBHOOK_URL'] }),\r\n        channel: process.env['ALERT_SLACK_CHANNEL'] || '#alerts',\r\n        username: process.env['ALERT_SLACK_USERNAME'] || 'Sentinel Bot',\r\n      },\r\n      webhook: {\r\n        enabled: parseBoolean(process.env['ALERT_WEBHOOK_ENABLED']),\r\n        ...(process.env['ALERT_WEBHOOK_URL'] && { url: process.env['ALERT_WEBHOOK_URL'] }),\r\n        headers: parseJsonSafe(process.env['ALERT_WEBHOOK_HEADERS'], {}),\r\n      },\r\n    },\r\n    rules: {\r\n      highErrorRate: {\r\n        enabled: parseBoolean(process.env['ALERT_HIGH_ERROR_RATE_ENABLED']),\r\n        threshold: parseFloatSafe(process.env['ALERT_HIGH_ERROR_RATE_THRESHOLD'], DEFAULT_THRESHOLDS.ERROR_RATE),\r\n        window: parseNumber(process.env['ALERT_HIGH_ERROR_RATE_WINDOW'], DEFAULT_INTERVALS.ALERT_WINDOW),\r\n      },\r\n      highResponseTime: {\r\n        enabled: parseBoolean(process.env['ALERT_HIGH_RESPONSE_TIME_ENABLED']),\r\n        threshold: parseNumber(process.env['ALERT_HIGH_RESPONSE_TIME_THRESHOLD'], DEFAULT_THRESHOLDS.RESPONSE_TIME_MS),\r\n        window: parseNumber(process.env['ALERT_HIGH_RESPONSE_TIME_WINDOW'], DEFAULT_INTERVALS.ALERT_WINDOW),\r\n      },\r\n      highMemoryUsage: {\r\n        enabled: parseBoolean(process.env['ALERT_HIGH_MEMORY_USAGE_ENABLED']),\r\n        threshold: parseNumber(process.env['ALERT_HIGH_MEMORY_USAGE_THRESHOLD'], DEFAULT_THRESHOLDS.SYSTEM_ALERT_PERCENT),\r\n        window: parseNumber(process.env['ALERT_HIGH_MEMORY_USAGE_WINDOW'], DEFAULT_INTERVALS.ALERT_WINDOW),\r\n      },\r\n      databaseConnectionFailure: {\r\n        enabled: parseBoolean(process.env['ALERT_DB_CONNECTION_FAILURE_ENABLED']),\r\n        threshold: parseNumber(process.env['ALERT_DB_CONNECTION_FAILURE_THRESHOLD'], 3),\r\n        window: parseNumber(process.env['ALERT_DB_CONNECTION_FAILURE_WINDOW'], 60),\r\n      },\r\n    },\r\n  },\r\n}));"], "version": 3}