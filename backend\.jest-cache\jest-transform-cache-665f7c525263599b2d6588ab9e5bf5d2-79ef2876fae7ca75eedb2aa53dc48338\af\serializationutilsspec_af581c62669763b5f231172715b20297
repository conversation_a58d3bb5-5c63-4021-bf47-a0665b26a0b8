c0d948d6a690c478639a742cf57bdb5f
"use strict";
/**
 * Serialization Utils Tests
 */
Object.defineProperty(exports, "__esModule", { value: true });
const serialization_utils_1 = require("../../utils/serialization.utils");
describe('SerializationUtils', () => {
    describe('serialize and deserialize', () => {
        it('should serialize and deserialize simple objects', () => {
            const obj = {
                name: '<PERSON>',
                age: 30,
                active: true,
            };
            const serialized = serialization_utils_1.SerializationUtils.serialize(obj);
            const deserialized = serialization_utils_1.SerializationUtils.deserialize(serialized);
            expect(deserialized.name).toBe('John');
            expect(deserialized.age).toBe(30);
            expect(deserialized.active).toBe(true);
        });
        it('should serialize and deserialize nested objects', () => {
            const obj = {
                user: {
                    name: '<PERSON>',
                    profile: {
                        email: '<EMAIL>',
                        preferences: {
                            theme: 'dark',
                        },
                    },
                },
                tags: ['admin', 'user'],
            };
            const serialized = serialization_utils_1.SerializationUtils.serialize(obj);
            const deserialized = serialization_utils_1.SerializationUtils.deserialize(serialized);
            expect(deserialized.user.name).toBe('John');
            expect(deserialized.user.profile.email).toBe('<EMAIL>');
            expect(deserialized.user.profile.preferences.theme).toBe('dark');
            expect(deserialized.tags).toEqual(['admin', 'user']);
        });
        it('should handle dates', () => {
            const obj = {
                createdAt: new Date('2023-01-01T12:00:00Z'),
                updatedAt: new Date('2023-01-02T12:00:00Z'),
            };
            const serialized = serialization_utils_1.SerializationUtils.serialize(obj);
            const deserialized = serialization_utils_1.SerializationUtils.deserialize(serialized);
            expect(deserialized.createdAt).toBeInstanceOf(Date);
            expect(deserialized.updatedAt).toBeInstanceOf(Date);
            expect(deserialized.createdAt.getTime()).toBe(obj.createdAt.getTime());
            expect(deserialized.updatedAt.getTime()).toBe(obj.updatedAt.getTime());
        });
        it('should handle arrays', () => {
            const obj = {
                numbers: [1, 2, 3],
                strings: ['a', 'b', 'c'],
                objects: [
                    { id: 1, name: 'First' },
                    { id: 2, name: 'Second' },
                ],
            };
            const serialized = serialization_utils_1.SerializationUtils.serialize(obj);
            const deserialized = serialization_utils_1.SerializationUtils.deserialize(serialized);
            expect(deserialized.numbers).toEqual([1, 2, 3]);
            expect(deserialized.strings).toEqual(['a', 'b', 'c']);
            expect(deserialized.objects).toEqual([
                { id: 1, name: 'First' },
                { id: 2, name: 'Second' },
            ]);
        });
        it('should handle null and undefined values', () => {
            const obj = {
                nullValue: null,
                undefinedValue: undefined,
                emptyString: '',
                zero: 0,
                false: false,
            };
            const options = {
                includeNulls: true,
                includeUndefined: true,
            };
            const serialized = serialization_utils_1.SerializationUtils.serialize(obj, options);
            const deserialized = serialization_utils_1.SerializationUtils.deserialize(serialized);
            expect(deserialized.nullValue).toBe(null);
            expect(deserialized.emptyString).toBe('');
            expect(deserialized.zero).toBe(0);
            expect(deserialized.false).toBe(false);
        });
        it('should exclude fields when specified', () => {
            const obj = {
                name: 'John',
                password: 'secret',
                email: '<EMAIL>',
            };
            const options = {
                exclude: ['password'],
            };
            const serialized = serialization_utils_1.SerializationUtils.serialize(obj, options);
            const deserialized = serialization_utils_1.SerializationUtils.deserialize(serialized);
            expect(deserialized.name).toBe('John');
            expect(deserialized.email).toBe('<EMAIL>');
            expect(deserialized.password).toBeUndefined();
        });
        it('should include only specified fields', () => {
            const obj = {
                name: 'John',
                password: 'secret',
                email: '<EMAIL>',
                age: 30,
            };
            const options = {
                include: ['name', 'email'],
            };
            const serialized = serialization_utils_1.SerializationUtils.serialize(obj, options);
            const deserialized = serialization_utils_1.SerializationUtils.deserialize(serialized);
            expect(deserialized.name).toBe('John');
            expect(deserialized.email).toBe('<EMAIL>');
            expect(deserialized.password).toBeUndefined();
            expect(deserialized.age).toBeUndefined();
        });
        it('should respect max depth', () => {
            const obj = {
                level1: {
                    level2: {
                        level3: {
                            level4: {
                                value: 'deep',
                            },
                        },
                    },
                },
            };
            const options = {
                maxDepth: 2,
            };
            const serialized = serialization_utils_1.SerializationUtils.serialize(obj, options);
            const deserialized = serialization_utils_1.SerializationUtils.deserialize(serialized);
            expect(deserialized.level1.level2).toBe('[Max Depth Exceeded]');
        });
        it('should handle custom serializers', () => {
            const obj = {
                date: new Date('2023-01-01'),
                name: 'John',
            };
            const customSerializers = new Map();
            customSerializers.set('date', (date) => date.getTime());
            const options = {
                customSerializers,
            };
            const serialized = serialization_utils_1.SerializationUtils.serialize(obj, options);
            const deserialized = serialization_utils_1.SerializationUtils.deserialize(serialized);
            expect(typeof deserialized.date).toBe('number');
            expect(deserialized.date).toBe(obj.date.getTime());
        });
        it('should handle custom deserializers', () => {
            const obj = {
                timestamp: Date.now(),
                name: 'John',
            };
            const customDeserializers = new Map();
            customDeserializers.set('timestamp', (timestamp) => new Date(timestamp));
            const serialized = serialization_utils_1.SerializationUtils.serialize(obj);
            const deserialized = serialization_utils_1.SerializationUtils.deserialize(serialized, {
                customDeserializers,
            });
            expect(deserialized.timestamp).toBeInstanceOf(Date);
        });
        it('should preserve type information when enabled', () => {
            class CustomClass {
                constructor(value) {
                    this.value = value;
                }
            }
            const obj = {
                custom: new CustomClass('test'),
                regular: { value: 'test' },
            };
            const options = {
                preserveTypes: true,
            };
            const serialized = serialization_utils_1.SerializationUtils.serialize(obj, options);
            const parsed = JSON.parse(serialized);
            expect(parsed.data.custom.__type).toBe('CustomClass');
            expect(parsed.data.regular.__type).toBeUndefined();
        });
    });
    describe('deepClone', () => {
        it('should deep clone objects', () => {
            const obj = {
                name: 'John',
                nested: {
                    value: 42,
                    array: [1, 2, 3],
                },
                date: new Date('2023-01-01'),
            };
            const cloned = serialization_utils_1.SerializationUtils.deepClone(obj);
            expect(cloned).not.toBe(obj);
            expect(cloned.nested).not.toBe(obj.nested);
            expect(cloned.nested.array).not.toBe(obj.nested.array);
            expect(cloned.date).not.toBe(obj.date);
            expect(cloned.name).toBe(obj.name);
            expect(cloned.nested.value).toBe(obj.nested.value);
            expect(cloned.nested.array).toEqual(obj.nested.array);
            expect(cloned.date.getTime()).toBe(obj.date.getTime());
        });
        it('should handle primitive values', () => {
            expect(serialization_utils_1.SerializationUtils.deepClone('string')).toBe('string');
            expect(serialization_utils_1.SerializationUtils.deepClone(123)).toBe(123);
            expect(serialization_utils_1.SerializationUtils.deepClone(true)).toBe(true);
            expect(serialization_utils_1.SerializationUtils.deepClone(null)).toBe(null);
            expect(serialization_utils_1.SerializationUtils.deepClone(undefined)).toBe(undefined);
        });
    });
    describe('deepEqual', () => {
        it('should compare objects for deep equality', () => {
            const obj1 = {
                name: 'John',
                nested: {
                    value: 42,
                    array: [1, 2, 3],
                },
            };
            const obj2 = {
                name: 'John',
                nested: {
                    value: 42,
                    array: [1, 2, 3],
                },
            };
            const obj3 = {
                name: 'Jane',
                nested: {
                    value: 42,
                    array: [1, 2, 3],
                },
            };
            expect(serialization_utils_1.SerializationUtils.deepEqual(obj1, obj2)).toBe(true);
            expect(serialization_utils_1.SerializationUtils.deepEqual(obj1, obj3)).toBe(false);
        });
        it('should handle primitive values', () => {
            expect(serialization_utils_1.SerializationUtils.deepEqual('test', 'test')).toBe(true);
            expect(serialization_utils_1.SerializationUtils.deepEqual('test', 'other')).toBe(false);
            expect(serialization_utils_1.SerializationUtils.deepEqual(123, 123)).toBe(true);
            expect(serialization_utils_1.SerializationUtils.deepEqual(123, 456)).toBe(false);
            expect(serialization_utils_1.SerializationUtils.deepEqual(null, null)).toBe(true);
            expect(serialization_utils_1.SerializationUtils.deepEqual(null, undefined)).toBe(false);
        });
        it('should handle dates', () => {
            const date1 = new Date('2023-01-01');
            const date2 = new Date('2023-01-01');
            const date3 = new Date('2023-01-02');
            expect(serialization_utils_1.SerializationUtils.deepEqual(date1, date2)).toBe(true);
            expect(serialization_utils_1.SerializationUtils.deepEqual(date1, date3)).toBe(false);
        });
        it('should handle arrays', () => {
            expect(serialization_utils_1.SerializationUtils.deepEqual([1, 2, 3], [1, 2, 3])).toBe(true);
            expect(serialization_utils_1.SerializationUtils.deepEqual([1, 2, 3], [1, 2, 4])).toBe(false);
            expect(serialization_utils_1.SerializationUtils.deepEqual([1, 2, 3], [1, 2])).toBe(false);
        });
    });
    describe('flatten and unflatten', () => {
        it('should flatten nested objects', () => {
            const obj = {
                name: 'John',
                address: {
                    street: '123 Main St',
                    city: 'Anytown',
                    coordinates: {
                        lat: 40.7128,
                        lng: -74.0060,
                    },
                },
            };
            const flattened = serialization_utils_1.SerializationUtils.flatten(obj);
            expect(flattened).toEqual({
                name: 'John',
                'address.street': '123 Main St',
                'address.city': 'Anytown',
                'address.coordinates.lat': 40.7128,
                'address.coordinates.lng': -74.0060,
            });
        });
        it('should unflatten dot notation objects', () => {
            const flattened = {
                name: 'John',
                'address.street': '123 Main St',
                'address.city': 'Anytown',
                'address.coordinates.lat': 40.7128,
                'address.coordinates.lng': -74.0060,
            };
            const unflattened = serialization_utils_1.SerializationUtils.unflatten(flattened);
            expect(unflattened).toEqual({
                name: 'John',
                address: {
                    street: '123 Main St',
                    city: 'Anytown',
                    coordinates: {
                        lat: 40.7128,
                        lng: -74.0060,
                    },
                },
            });
        });
        it('should handle arrays and dates in flattening', () => {
            const obj = {
                items: [1, 2, 3],
                date: new Date('2023-01-01'),
            };
            const flattened = serialization_utils_1.SerializationUtils.flatten(obj);
            expect(flattened.items).toEqual([1, 2, 3]);
            expect(flattened.date).toBeInstanceOf(Date);
        });
    });
    describe('query string conversion', () => {
        it('should convert object to query string', () => {
            const obj = {
                name: 'John Doe',
                age: 30,
                active: true,
                tags: ['admin', 'user'],
            };
            const queryString = serialization_utils_1.SerializationUtils.toQueryString(obj);
            expect(queryString).toContain('name=John%20Doe');
            expect(queryString).toContain('age=30');
            expect(queryString).toContain('active=true');
            expect(queryString).toContain('tags[0]=admin');
            expect(queryString).toContain('tags[1]=user');
        });
        it('should parse query string to object', () => {
            const queryString = 'name=John%20Doe&age=30&active=true&tags[0]=admin&tags[1]=user';
            const obj = serialization_utils_1.SerializationUtils.fromQueryString(queryString);
            expect(obj.name).toBe('John Doe');
            expect(obj.age).toBe('30'); // Query string values are always strings
            expect(obj.active).toBe('true');
            expect(obj.tags).toEqual(['admin', 'user']);
        });
        it('should handle nested objects in query strings', () => {
            const obj = {
                user: {
                    name: 'John',
                    profile: {
                        email: '<EMAIL>',
                    },
                },
            };
            const queryString = serialization_utils_1.SerializationUtils.toQueryString(obj);
            const parsed = serialization_utils_1.SerializationUtils.fromQueryString(queryString);
            expect(parsed.user.name).toBe('John');
            expect(parsed.user.profile.email).toBe('<EMAIL>');
        });
        it('should skip null and undefined values in query strings', () => {
            const obj = {
                name: 'John',
                nullValue: null,
                undefinedValue: undefined,
                emptyString: '',
            };
            const queryString = serialization_utils_1.SerializationUtils.toQueryString(obj);
            expect(queryString).toContain('name=John');
            expect(queryString).toContain('emptyString=');
            expect(queryString).not.toContain('nullValue');
            expect(queryString).not.toContain('undefinedValue');
        });
    });
    describe('FormData conversion', () => {
        it('should convert object to FormData', () => {
            const obj = {
                name: 'John',
                age: 30,
                files: ['file1.txt', 'file2.txt'],
            };
            const formData = serialization_utils_1.SerializationUtils.toFormData(obj);
            expect(formData.get('name')).toBe('John');
            expect(formData.get('age')).toBe('30');
            expect(formData.get('files[0]')).toBe('file1.txt');
            expect(formData.get('files[1]')).toBe('file2.txt');
        });
        it('should handle nested objects in FormData', () => {
            const obj = {
                user: {
                    name: 'John',
                    profile: {
                        email: '<EMAIL>',
                    },
                },
            };
            const formData = serialization_utils_1.SerializationUtils.toFormData(obj);
            expect(formData.get('user[name]')).toBe('John');
            expect(formData.get('user[profile][email]')).toBe('<EMAIL>');
        });
    });
    describe('utility functions', () => {
        it('should calculate object size', () => {
            const smallObj = { name: 'John' };
            const largeObj = {
                data: Array.from({ length: 1000 }, (_, i) => ({ id: i, value: `item-${i}` })),
            };
            const smallSize = serialization_utils_1.SerializationUtils.getObjectSize(smallObj);
            const largeSize = serialization_utils_1.SerializationUtils.getObjectSize(largeObj);
            expect(smallSize).toBeGreaterThan(0);
            expect(largeSize).toBeGreaterThan(smallSize);
        });
        it('should compress and decompress objects', () => {
            const obj = {
                name: 'John',
                data: Array.from({ length: 100 }, (_, i) => ({ id: i, value: `item-${i}` })),
            };
            const compressed = serialization_utils_1.SerializationUtils.compress(obj);
            const decompressed = serialization_utils_1.SerializationUtils.decompress(compressed);
            expect(typeof compressed).toBe('string');
            expect(decompressed).toEqual(obj);
        });
        it('should create and validate schemas', () => {
            const obj = {
                name: 'John',
                age: 30,
                tags: ['admin', 'user'],
                profile: {
                    email: '<EMAIL>',
                },
            };
            const schema = serialization_utils_1.SerializationUtils.createSchema(obj);
            expect(schema.type).toBe('object');
            expect(schema.properties?.name.type).toBe('string');
            expect(schema.properties?.age.type).toBe('number');
            expect(schema.properties?.tags.type).toBe('array');
            expect(schema.properties?.tags.elementType).toBe('string');
            expect(schema.properties?.profile.type).toBe('object');
            // Validate object against schema
            expect(serialization_utils_1.SerializationUtils.validateSchema(obj, schema)).toBe(true);
            // Validate invalid object
            const invalidObj = {
                name: 123, // Should be string
                age: '30', // Should be number
            };
            expect(serialization_utils_1.SerializationUtils.validateSchema(invalidObj, schema)).toBe(false);
        });
    });
    describe('date handling', () => {
        it('should serialize dates in different formats', () => {
            const obj = {
                isoDate: new Date('2023-01-01T12:00:00Z'),
                timestampDate: new Date('2023-01-01T12:00:00Z'),
            };
            // ISO format (default)
            const isoSerialized = serialization_utils_1.SerializationUtils.serialize(obj);
            const isoDeserialized = serialization_utils_1.SerializationUtils.deserialize(isoSerialized);
            expect(isoDeserialized.isoDate).toBeInstanceOf(Date);
            // Timestamp format
            const timestampSerialized = serialization_utils_1.SerializationUtils.serialize(obj, {
                dateFormat: 'timestamp',
            });
            const timestampDeserialized = serialization_utils_1.SerializationUtils.deserialize(timestampSerialized);
            expect(timestampDeserialized.timestampDate).toBeInstanceOf(Date);
        });
        it('should handle custom date formatter', () => {
            const obj = {
                date: new Date('2023-01-01T12:00:00Z'),
            };
            const customFormatter = (date) => `custom-${date.getTime()}`;
            const serialized = serialization_utils_1.SerializationUtils.serialize(obj, {
                dateFormat: 'custom',
                customDateFormatter: customFormatter,
            });
            const parsed = JSON.parse(serialized);
            expect(parsed.data.date).toMatch(/^custom-\d+$/);
        });
        it('should restore dates based on field names', () => {
            const obj = {
                createdAt: '2023-01-01T12:00:00Z',
                updatedAt: '2023-01-02T12:00:00Z',
                regularField: '2023-01-03T12:00:00Z',
            };
            const serialized = JSON.stringify({ data: obj });
            const deserialized = serialization_utils_1.SerializationUtils.deserialize(serialized, {
                restoreDates: true,
            });
            expect(deserialized.createdAt).toBeInstanceOf(Date);
            expect(deserialized.updatedAt).toBeInstanceOf(Date);
            expect(deserialized.regularField).toBeInstanceOf(Date); // Auto-detected
        });
        it('should restore specific date fields', () => {
            const obj = {
                dateField: '2023-01-01T12:00:00Z',
                stringField: '2023-01-02T12:00:00Z',
            };
            const serialized = JSON.stringify({ data: obj });
            const deserialized = serialization_utils_1.SerializationUtils.deserialize(serialized, {
                restoreDates: true,
                dateFields: ['dateField'],
            });
            expect(deserialized.dateField).toBeInstanceOf(Date);
            expect(typeof deserialized.stringField).toBe('string');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************