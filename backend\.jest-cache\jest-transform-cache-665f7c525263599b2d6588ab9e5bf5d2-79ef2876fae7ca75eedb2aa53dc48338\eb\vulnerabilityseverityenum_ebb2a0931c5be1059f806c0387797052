3e920ecadc99ee96ef2dbfbd462a2f8f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilitySeverityUtils = exports.VulnerabilitySeverity = void 0;
/**
 * Vulnerability Severity Enum
 *
 * Represents the severity level of identified vulnerabilities based on CVSS scoring,
 * potential impact, exploitability, and business risk. Used for vulnerability
 * prioritization, patch management, and risk assessment.
 *
 * Severity Levels (ascending order):
 * NONE -> LOW -> MEDIUM -> HIGH -> CRITICAL
 */
var VulnerabilitySeverity;
(function (VulnerabilitySeverity) {
    /**
     * No vulnerability or informational only
     * - CVSS Score: 0.0
     * - No security impact
     * - Informational findings only
     * - No remediation required
     */
    VulnerabilitySeverity["NONE"] = "none";
    /**
     * Low severity vulnerabilities
     * - CVSS Score: 0.1 - 3.9
     * - Minimal security impact
     * - Difficult to exploit
     * - Low business risk
     * - Routine patching schedule
     */
    VulnerabilitySeverity["LOW"] = "low";
    /**
     * Medium severity vulnerabilities
     * - CVSS Score: 4.0 - 6.9
     * - Moderate security impact
     * - Moderate exploitability
     * - Medium business risk
     * - Planned remediation required
     */
    VulnerabilitySeverity["MEDIUM"] = "medium";
    /**
     * High severity vulnerabilities
     * - CVSS Score: 7.0 - 8.9
     * - Significant security impact
     * - High exploitability
     * - High business risk
     * - Priority remediation required
     */
    VulnerabilitySeverity["HIGH"] = "high";
    /**
     * Critical severity vulnerabilities
     * - CVSS Score: 9.0 - 10.0
     * - Severe security impact
     * - Easy to exploit remotely
     * - Critical business risk
     * - Emergency remediation required
     */
    VulnerabilitySeverity["CRITICAL"] = "critical";
    /**
     * Unknown vulnerability severity
     * - Insufficient information for assessment
     * - Requires further analysis
     * - Default state for new vulnerabilities
     */
    VulnerabilitySeverity["UNKNOWN"] = "unknown";
})(VulnerabilitySeverity || (exports.VulnerabilitySeverity = VulnerabilitySeverity = {}));
/**
 * Vulnerability Severity Utilities
 */
class VulnerabilitySeverityUtils {
    /**
     * Get all vulnerability severity levels
     */
    static getAllSeverities() {
        return Object.values(VulnerabilitySeverity);
    }
    /**
     * Get severity levels requiring immediate remediation
     */
    static getImmediateRemediationSeverities() {
        return [VulnerabilitySeverity.CRITICAL, VulnerabilitySeverity.HIGH];
    }
    /**
     * Get severity levels requiring priority remediation
     */
    static getPriorityRemediationSeverities() {
        return [VulnerabilitySeverity.CRITICAL, VulnerabilitySeverity.HIGH, VulnerabilitySeverity.MEDIUM];
    }
    /**
     * Get severity levels for routine remediation
     */
    static getRoutineRemediationSeverities() {
        return [VulnerabilitySeverity.LOW, VulnerabilitySeverity.NONE];
    }
    /**
     * Check if severity requires immediate remediation
     */
    static requiresImmediateRemediation(severity) {
        return VulnerabilitySeverityUtils.getImmediateRemediationSeverities().includes(severity);
    }
    /**
     * Check if severity is critical
     */
    static isCritical(severity) {
        return severity === VulnerabilitySeverity.CRITICAL;
    }
    /**
     * Check if severity is high or critical
     */
    static isHighOrCritical(severity) {
        return severity === VulnerabilitySeverity.HIGH || severity === VulnerabilitySeverity.CRITICAL;
    }
    /**
     * Check if severity requires priority remediation
     */
    static requiresPriorityRemediation(severity) {
        return VulnerabilitySeverityUtils.getPriorityRemediationSeverities().includes(severity);
    }
    /**
     * Get numeric value for severity (for comparison and scoring)
     */
    static getNumericValue(severity) {
        const values = {
            [VulnerabilitySeverity.NONE]: 0,
            [VulnerabilitySeverity.LOW]: 1,
            [VulnerabilitySeverity.MEDIUM]: 2,
            [VulnerabilitySeverity.HIGH]: 3,
            [VulnerabilitySeverity.CRITICAL]: 4,
            [VulnerabilitySeverity.UNKNOWN]: -1,
        };
        return values[severity];
    }
    /**
     * Get CVSS score range for severity
     */
    static getCVSSRange(severity) {
        const ranges = {
            [VulnerabilitySeverity.NONE]: { min: 0.0, max: 0.0 },
            [VulnerabilitySeverity.LOW]: { min: 0.1, max: 3.9 },
            [VulnerabilitySeverity.MEDIUM]: { min: 4.0, max: 6.9 },
            [VulnerabilitySeverity.HIGH]: { min: 7.0, max: 8.9 },
            [VulnerabilitySeverity.CRITICAL]: { min: 9.0, max: 10.0 },
            [VulnerabilitySeverity.UNKNOWN]: { min: 0.0, max: 10.0 },
        };
        return ranges[severity];
    }
    /**
     * Get severity from CVSS score
     */
    static fromCVSSScore(cvssScore) {
        if (cvssScore < 0 || cvssScore > 10) {
            return VulnerabilitySeverity.UNKNOWN;
        }
        if (cvssScore === 0.0)
            return VulnerabilitySeverity.NONE;
        if (cvssScore >= 9.0)
            return VulnerabilitySeverity.CRITICAL;
        if (cvssScore >= 7.0)
            return VulnerabilitySeverity.HIGH;
        if (cvssScore >= 4.0)
            return VulnerabilitySeverity.MEDIUM;
        if (cvssScore >= 0.1)
            return VulnerabilitySeverity.LOW;
        return VulnerabilitySeverity.NONE;
    }
    /**
     * Compare two vulnerability severities
     */
    static compare(severity1, severity2) {
        return VulnerabilitySeverityUtils.getNumericValue(severity1) - VulnerabilitySeverityUtils.getNumericValue(severity2);
    }
    /**
     * Get the higher of two severities
     */
    static getHigher(severity1, severity2) {
        return VulnerabilitySeverityUtils.compare(severity1, severity2) >= 0 ? severity1 : severity2;
    }
    /**
     * Get the lower of two severities
     */
    static getLower(severity1, severity2) {
        return VulnerabilitySeverityUtils.compare(severity1, severity2) <= 0 ? severity1 : severity2;
    }
    /**
     * Get remediation SLA in days based on vulnerability severity
     */
    static getRemediationSLA(severity) {
        const slaDays = {
            [VulnerabilitySeverity.CRITICAL]: 1, // 1 day
            [VulnerabilitySeverity.HIGH]: 7, // 7 days
            [VulnerabilitySeverity.MEDIUM]: 30, // 30 days
            [VulnerabilitySeverity.LOW]: 90, // 90 days
            [VulnerabilitySeverity.NONE]: 365, // 1 year (informational)
            [VulnerabilitySeverity.UNKNOWN]: 30, // 30 days (default)
        };
        return slaDays[severity];
    }
    /**
     * Get assessment time SLA in hours based on vulnerability severity
     */
    static getAssessmentTimeSLA(severity) {
        const slaHours = {
            [VulnerabilitySeverity.CRITICAL]: 2, // 2 hours
            [VulnerabilitySeverity.HIGH]: 8, // 8 hours
            [VulnerabilitySeverity.MEDIUM]: 24, // 24 hours
            [VulnerabilitySeverity.LOW]: 72, // 3 days
            [VulnerabilitySeverity.NONE]: 168, // 7 days
            [VulnerabilitySeverity.UNKNOWN]: 24, // 24 hours
        };
        return slaHours[severity];
    }
    /**
     * Get escalation time in hours based on vulnerability severity
     */
    static getEscalationTime(severity) {
        const escalationHours = {
            [VulnerabilitySeverity.CRITICAL]: 4, // 4 hours
            [VulnerabilitySeverity.HIGH]: 24, // 24 hours
            [VulnerabilitySeverity.MEDIUM]: 72, // 3 days
            [VulnerabilitySeverity.LOW]: 168, // 7 days
            [VulnerabilitySeverity.NONE]: 720, // 30 days
            [VulnerabilitySeverity.UNKNOWN]: 48, // 48 hours
        };
        return escalationHours[severity];
    }
    /**
     * Get required approval level for remediation
     */
    static getApprovalLevel(severity) {
        const approvals = {
            [VulnerabilitySeverity.CRITICAL]: 'ciso',
            [VulnerabilitySeverity.HIGH]: 'director',
            [VulnerabilitySeverity.MEDIUM]: 'manager',
            [VulnerabilitySeverity.LOW]: 'team_lead',
            [VulnerabilitySeverity.NONE]: 'none',
            [VulnerabilitySeverity.UNKNOWN]: 'manager',
        };
        return approvals[severity];
    }
    /**
     * Get notification channels based on vulnerability severity
     */
    static getNotificationChannels(severity) {
        const channels = {
            [VulnerabilitySeverity.CRITICAL]: ['email', 'sms', 'slack', 'webhook', 'pager'],
            [VulnerabilitySeverity.HIGH]: ['email', 'slack', 'webhook'],
            [VulnerabilitySeverity.MEDIUM]: ['email', 'webhook'],
            [VulnerabilitySeverity.LOW]: ['webhook'],
            [VulnerabilitySeverity.NONE]: [],
            [VulnerabilitySeverity.UNKNOWN]: ['email'],
        };
        return channels[severity];
    }
    /**
     * Get scanning frequency based on vulnerability severity
     */
    static getScanningFrequency(severity) {
        const frequencies = {
            [VulnerabilitySeverity.CRITICAL]: { interval: 1, priority: 'critical' }, // Every hour
            [VulnerabilitySeverity.HIGH]: { interval: 4, priority: 'high' }, // Every 4 hours
            [VulnerabilitySeverity.MEDIUM]: { interval: 24, priority: 'medium' }, // Daily
            [VulnerabilitySeverity.LOW]: { interval: 168, priority: 'low' }, // Weekly
            [VulnerabilitySeverity.NONE]: { interval: 720, priority: 'low' }, // Monthly
            [VulnerabilitySeverity.UNKNOWN]: { interval: 24, priority: 'medium' }, // Daily
        };
        return frequencies[severity];
    }
    /**
     * Get color code for UI display
     */
    static getColorCode(severity) {
        const colors = {
            [VulnerabilitySeverity.CRITICAL]: '#DC2626', // Red
            [VulnerabilitySeverity.HIGH]: '#EA580C', // Orange
            [VulnerabilitySeverity.MEDIUM]: '#D97706', // Amber
            [VulnerabilitySeverity.LOW]: '#16A34A', // Green
            [VulnerabilitySeverity.NONE]: '#10B981', // Emerald
            [VulnerabilitySeverity.UNKNOWN]: '#6B7280', // Gray
        };
        return colors[severity];
    }
    /**
     * Get icon name for UI display
     */
    static getIconName(severity) {
        const icons = {
            [VulnerabilitySeverity.CRITICAL]: 'exclamation-triangle',
            [VulnerabilitySeverity.HIGH]: 'exclamation-circle',
            [VulnerabilitySeverity.MEDIUM]: 'information-circle',
            [VulnerabilitySeverity.LOW]: 'check-circle',
            [VulnerabilitySeverity.NONE]: 'check-circle',
            [VulnerabilitySeverity.UNKNOWN]: 'question-mark-circle',
        };
        return icons[severity];
    }
    /**
     * Get human-readable description
     */
    static getDescription(severity) {
        const descriptions = {
            [VulnerabilitySeverity.CRITICAL]: 'Critical vulnerability requiring emergency remediation within 24 hours',
            [VulnerabilitySeverity.HIGH]: 'High-severity vulnerability requiring priority remediation within 7 days',
            [VulnerabilitySeverity.MEDIUM]: 'Medium-severity vulnerability requiring planned remediation within 30 days',
            [VulnerabilitySeverity.LOW]: 'Low-severity vulnerability requiring routine remediation within 90 days',
            [VulnerabilitySeverity.NONE]: 'No vulnerability or informational finding only',
            [VulnerabilitySeverity.UNKNOWN]: 'Unknown vulnerability severity requiring assessment and classification',
        };
        return descriptions[severity];
    }
    /**
     * Get severity from string (case-insensitive)
     */
    static fromString(value) {
        const normalized = value.toLowerCase().trim();
        const severities = Object.values(VulnerabilitySeverity);
        return severities.find(s => s === normalized) || null;
    }
    /**
     * Validate severity value
     */
    static isValid(severity) {
        return Object.values(VulnerabilitySeverity).includes(severity);
    }
    /**
     * Get processing priority (1-10) based on vulnerability severity
     */
    static getProcessingPriority(severity) {
        const priorities = {
            [VulnerabilitySeverity.CRITICAL]: 10,
            [VulnerabilitySeverity.HIGH]: 8,
            [VulnerabilitySeverity.MEDIUM]: 6,
            [VulnerabilitySeverity.LOW]: 4,
            [VulnerabilitySeverity.NONE]: 2,
            [VulnerabilitySeverity.UNKNOWN]: 5,
        };
        return priorities[severity];
    }
    /**
     * Get business impact based on vulnerability severity
     */
    static getBusinessImpact(severity) {
        const impacts = {
            [VulnerabilitySeverity.CRITICAL]: { confidentiality: 'high', integrity: 'high', availability: 'high' },
            [VulnerabilitySeverity.HIGH]: { confidentiality: 'high', integrity: 'medium', availability: 'medium' },
            [VulnerabilitySeverity.MEDIUM]: { confidentiality: 'medium', integrity: 'low', availability: 'low' },
            [VulnerabilitySeverity.LOW]: { confidentiality: 'low', integrity: 'none', availability: 'none' },
            [VulnerabilitySeverity.NONE]: { confidentiality: 'none', integrity: 'none', availability: 'none' },
            [VulnerabilitySeverity.UNKNOWN]: { confidentiality: 'medium', integrity: 'medium', availability: 'medium' },
        };
        return impacts[severity];
    }
    /**
     * Get remediation complexity based on vulnerability severity
     */
    static getRemediationComplexity(severity) {
        const complexity = {
            [VulnerabilitySeverity.CRITICAL]: { effort: 'critical', risk: 'critical', testing: 'comprehensive' },
            [VulnerabilitySeverity.HIGH]: { effort: 'high', risk: 'high', testing: 'extensive' },
            [VulnerabilitySeverity.MEDIUM]: { effort: 'medium', risk: 'medium', testing: 'standard' },
            [VulnerabilitySeverity.LOW]: { effort: 'low', risk: 'low', testing: 'minimal' },
            [VulnerabilitySeverity.NONE]: { effort: 'low', risk: 'low', testing: 'minimal' },
            [VulnerabilitySeverity.UNKNOWN]: { effort: 'medium', risk: 'medium', testing: 'standard' },
        };
        return complexity[severity];
    }
    /**
     * Calculate combined severity from multiple vulnerabilities
     */
    static calculateCombinedSeverity(severities) {
        if (severities.length === 0) {
            return VulnerabilitySeverity.UNKNOWN;
        }
        // If any vulnerability is critical, combined severity is critical
        if (severities.includes(VulnerabilitySeverity.CRITICAL)) {
            return VulnerabilitySeverity.CRITICAL;
        }
        // Calculate weighted average (excluding unknown)
        const validSeverities = severities.filter(s => s !== VulnerabilitySeverity.UNKNOWN);
        if (validSeverities.length === 0) {
            return VulnerabilitySeverity.UNKNOWN;
        }
        const numericValues = validSeverities.map(s => VulnerabilitySeverityUtils.getNumericValue(s));
        const average = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length;
        // Convert back to severity
        if (average >= 3.5)
            return VulnerabilitySeverity.CRITICAL;
        if (average >= 2.5)
            return VulnerabilitySeverity.HIGH;
        if (average >= 1.5)
            return VulnerabilitySeverity.MEDIUM;
        if (average >= 0.5)
            return VulnerabilitySeverity.LOW;
        return VulnerabilitySeverity.NONE;
    }
    /**
     * Get vulnerability age impact on severity
     */
    static getAgeAdjustedSeverity(originalSeverity, ageInDays) {
        // Vulnerabilities become more severe over time if not remediated
        const ageMultiplier = Math.min(1.5, 1 + (ageInDays / 365)); // Max 1.5x after 1 year
        const originalValue = VulnerabilitySeverityUtils.getNumericValue(originalSeverity);
        const adjustedValue = Math.min(4, Math.floor(originalValue * ageMultiplier));
        const severityMap = {
            0: VulnerabilitySeverity.NONE,
            1: VulnerabilitySeverity.LOW,
            2: VulnerabilitySeverity.MEDIUM,
            3: VulnerabilitySeverity.HIGH,
            4: VulnerabilitySeverity.CRITICAL,
        };
        return severityMap[adjustedValue] || originalSeverity;
    }
}
exports.VulnerabilitySeverityUtils = VulnerabilitySeverityUtils;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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