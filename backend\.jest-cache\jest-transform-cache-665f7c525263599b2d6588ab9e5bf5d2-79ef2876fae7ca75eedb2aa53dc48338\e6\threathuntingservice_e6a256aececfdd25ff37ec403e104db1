da8c3e21275a2cdc50abf7917323dfdd
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatHuntingService = void 0;
const common_1 = require("@nestjs/common");
const security_event_repository_interface_1 = require("../../domain/interfaces/repositories/security-event.repository.interface");
const vulnerability_repository_interface_1 = require("../../domain/interfaces/repositories/vulnerability.repository.interface");
const incident_repository_interface_1 = require("../../domain/interfaces/repositories/incident.repository.interface");
const correlation_repository_interface_1 = require("../../domain/interfaces/repositories/correlation.repository.interface");
const threat_severity_enum_1 = require("../../domain/enums/threat-severity.enum");
const confidence_level_enum_1 = require("../../domain/enums/confidence-level.enum");
const domain_event_publisher_1 = require("../../../shared-kernel/domain/domain-event-publisher");
/**
 * Threat Hunting Service
 *
 * Provides advanced threat hunting capabilities for proactive threat detection
 * and investigation. Supports multiple hunting methodologies and techniques.
 *
 * Key capabilities:
 * - IOC-based hunting
 * - Behavioral analysis
 * - Anomaly detection
 * - Pattern matching
 * - Timeline analysis
 * - MITRE ATT&CK mapping
 * - Threat actor attribution
 */
let ThreatHuntingService = class ThreatHuntingService {
    constructor(securityEventRepository, vulnerabilityRepository, incidentRepository, correlationRepository, eventPublisher) {
        this.securityEventRepository = securityEventRepository;
        this.vulnerabilityRepository = vulnerabilityRepository;
        this.incidentRepository = incidentRepository;
        this.correlationRepository = correlationRepository;
        this.eventPublisher = eventPublisher;
    }
    /**
     * Execute threat hunting query
     */
    async executeHunt(query) {
        const executionId = `hunt-${Date.now()}`;
        const startTime = new Date();
        try {
            // Initialize result structure
            const result = {
                query,
                execution: {
                    executionId,
                    startTime,
                    endTime: new Date(),
                    duration: 0,
                    status: 'running',
                    progress: 0,
                },
                results: {
                    totalMatches: 0,
                    highConfidenceMatches: 0,
                    uniqueAssetsAffected: 0,
                    timeRangeCovered: query.parameters.timeRange,
                    matchesByCategory: {},
                    confidenceDistribution: {},
                },
                findings: [],
                analysis: {
                    threatAssessment: {
                        overallThreatLevel: 'low',
                        threatActorAttribution: [],
                        campaignAttribution: [],
                    },
                    attackTimeline: [],
                    killChainAnalysis: {
                        phases: [],
                        progression: 0,
                    },
                    recommendations: {
                        immediate: [],
                        shortTerm: [],
                        longTerm: [],
                        preventive: [],
                    },
                },
                quality: {
                    dataCoverage: 0,
                    searchCompleteness: 0,
                    resultConfidence: 0,
                    falsePositiveLikelihood: 0,
                },
            };
            // Execute hunt based on query type
            switch (query.type) {
                case 'ioc_search':
                    await this.executeIOCHunt(query, result);
                    break;
                case 'behavioral_analysis':
                    await this.executeBehavioralHunt(query, result);
                    break;
                case 'anomaly_detection':
                    await this.executeAnomalyHunt(query, result);
                    break;
                case 'pattern_matching':
                    await this.executePatternHunt(query, result);
                    break;
                case 'timeline_analysis':
                    await this.executeTimelineHunt(query, result);
                    break;
                default:
                    throw new Error(`Unsupported hunt type: ${query.type}`);
            }
            // Perform analysis
            await this.performThreatAnalysis(result);
            // Calculate quality metrics
            this.calculateQualityMetrics(result);
            // Finalize execution
            const endTime = new Date();
            result.execution.endTime = endTime;
            result.execution.duration = endTime.getTime() - startTime.getTime();
            result.execution.status = 'completed';
            result.execution.progress = 100;
            return result;
        }
        catch (error) {
            const endTime = new Date();
            return {
                ...result,
                execution: {
                    executionId,
                    startTime,
                    endTime,
                    duration: endTime.getTime() - startTime.getTime(),
                    status: 'failed',
                    progress: 0,
                },
            };
        }
    }
    /**
     * Execute IOC-based hunt
     */
    async executeIOCHunt(query, result) {
        if (!query.parameters.iocs || query.parameters.iocs.length === 0) {
            return;
        }
        for (const ioc of query.parameters.iocs) {
            // Search for IOC in security events
            const events = await this.securityEventRepository.findByIOC(ioc.value, ioc.type);
            for (const event of events) {
                const finding = {
                    id: `ioc-${Date.now()}-${Math.random()}`,
                    type: 'ioc_match',
                    confidence: ioc.confidence,
                    severity: this.assessIOCSeverity(ioc, event),
                    description: `IOC match found: ${ioc.type} = ${ioc.value}`,
                    evidence: [{
                            type: 'event',
                            source: event.source.name,
                            timestamp: event.timestamp.value,
                            data: {
                                eventId: event.id.toString(),
                                iocType: ioc.type,
                                iocValue: ioc.value,
                                eventData: event.rawData,
                            },
                        }],
                    affectedAssets: event.affectedAssets?.map(asset => asset.assetId) || [],
                    relatedEvents: [event.id.toString()],
                    mitreAttackMapping: [],
                    recommendedActions: this.getIOCRecommendations(ioc, event),
                };
                result.findings.push(finding);
                result.results.totalMatches++;
                if (ioc.confidence === confidence_level_enum_1.ConfidenceLevel.HIGH || ioc.confidence === confidence_level_enum_1.ConfidenceLevel.VERY_HIGH) {
                    result.results.highConfidenceMatches++;
                }
            }
        }
        // Update progress
        result.execution.progress = 30;
    }
    /**
     * Execute behavioral analysis hunt
     */
    async executeBehavioralHunt(query, result) {
        if (!query.parameters.patterns || query.parameters.patterns.length === 0) {
            return;
        }
        // This would implement behavioral analysis logic
        // For now, we'll simulate some results
        const simulatedFindings = this.generateSimulatedBehavioralFindings(query);
        result.findings.push(...simulatedFindings);
        result.results.totalMatches += simulatedFindings.length;
        result.execution.progress = 60;
    }
    /**
     * Execute anomaly detection hunt
     */
    async executeAnomalyHunt(query, result) {
        // This would implement anomaly detection logic
        // For now, we'll simulate some results
        const simulatedFindings = this.generateSimulatedAnomalyFindings(query);
        result.findings.push(...simulatedFindings);
        result.results.totalMatches += simulatedFindings.length;
        result.execution.progress = 80;
    }
    /**
     * Execute pattern matching hunt
     */
    async executePatternHunt(query, result) {
        // This would implement pattern matching logic
        const simulatedFindings = this.generateSimulatedPatternFindings(query);
        result.findings.push(...simulatedFindings);
        result.results.totalMatches += simulatedFindings.length;
        result.execution.progress = 90;
    }
    /**
     * Execute timeline analysis hunt
     */
    async executeTimelineHunt(query, result) {
        // This would implement timeline analysis logic
        const simulatedFindings = this.generateSimulatedTimelineFindings(query);
        result.findings.push(...simulatedFindings);
        result.results.totalMatches += simulatedFindings.length;
        result.execution.progress = 95;
    }
    /**
     * Perform threat analysis on findings
     */
    async performThreatAnalysis(result) {
        // Assess overall threat level
        result.analysis.threatAssessment.overallThreatLevel = this.assessOverallThreatLevel(result.findings);
        // Build attack timeline
        result.analysis.attackTimeline = this.buildAttackTimeline(result.findings);
        // Analyze kill chain progression
        result.analysis.killChainAnalysis = this.analyzeKillChain(result.findings);
        // Generate recommendations
        result.analysis.recommendations = this.generateRecommendations(result);
        // Calculate unique assets affected
        const uniqueAssets = new Set();
        result.findings.forEach(finding => {
            finding.affectedAssets.forEach(asset => uniqueAssets.add(asset));
        });
        result.results.uniqueAssetsAffected = uniqueAssets.size;
        // Calculate confidence distribution
        result.findings.forEach(finding => {
            result.results.confidenceDistribution[finding.confidence] =
                (result.results.confidenceDistribution[finding.confidence] || 0) + 1;
        });
    }
    /**
     * Calculate quality metrics
     */
    calculateQualityMetrics(result) {
        // Calculate data coverage (simplified)
        result.quality.dataCoverage = Math.min(100, result.results.totalMatches * 10);
        // Calculate search completeness
        result.quality.searchCompleteness = 95; // Would be calculated based on actual search coverage
        // Calculate result confidence
        const avgConfidence = result.findings.length > 0
            ? result.findings.reduce((sum, finding) => {
                const confidenceScore = this.getConfidenceScore(finding.confidence);
                return sum + confidenceScore;
            }, 0) / result.findings.length
            : 0;
        result.quality.resultConfidence = avgConfidence;
        // Calculate false positive likelihood
        result.quality.falsePositiveLikelihood = Math.max(0, 30 - avgConfidence);
    }
    /**
     * Assess IOC severity
     */
    assessIOCSeverity(ioc, event) {
        // Simplified severity assessment
        if (ioc.confidence === confidence_level_enum_1.ConfidenceLevel.CONFIRMED) {
            return threat_severity_enum_1.ThreatSeverity.HIGH;
        }
        if (ioc.confidence === confidence_level_enum_1.ConfidenceLevel.HIGH) {
            return threat_severity_enum_1.ThreatSeverity.MEDIUM;
        }
        return threat_severity_enum_1.ThreatSeverity.LOW;
    }
    /**
     * Get IOC recommendations
     */
    getIOCRecommendations(ioc, event) {
        return [
            'Investigate affected assets',
            'Check for lateral movement',
            'Review network traffic',
            'Update detection rules',
        ];
    }
    /**
     * Generate simulated findings for demonstration
     */
    generateSimulatedBehavioralFindings(query) {
        return [
            {
                id: `behavioral-${Date.now()}`,
                type: 'behavioral_anomaly',
                confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                description: 'Unusual login pattern detected',
                evidence: [],
                affectedAssets: ['asset-1'],
                relatedEvents: [],
                mitreAttackMapping: [],
                recommendedActions: ['Investigate user behavior'],
            },
        ];
    }
    generateSimulatedAnomalyFindings(query) {
        return [];
    }
    generateSimulatedPatternFindings(query) {
        return [];
    }
    generateSimulatedTimelineFindings(query) {
        return [];
    }
    /**
     * Assess overall threat level
     */
    assessOverallThreatLevel(findings) {
        if (findings.some(f => f.severity === threat_severity_enum_1.ThreatSeverity.CRITICAL))
            return 'critical';
        if (findings.some(f => f.severity === threat_severity_enum_1.ThreatSeverity.HIGH))
            return 'high';
        if (findings.some(f => f.severity === threat_severity_enum_1.ThreatSeverity.MEDIUM))
            return 'medium';
        return 'low';
    }
    /**
     * Build attack timeline
     */
    buildAttackTimeline(findings) {
        return findings.flatMap(finding => finding.evidence.map(evidence => ({
            timestamp: evidence.timestamp,
            event: finding.description,
            technique: finding.mitreAttackMapping[0]?.techniqueName || 'Unknown',
            asset: finding.affectedAssets[0] || 'Unknown',
            confidence: finding.confidence,
        }))).sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    }
    /**
     * Analyze kill chain progression
     */
    analyzeKillChain(findings) {
        return {
            phases: [],
            progression: 0,
        };
    }
    /**
     * Generate recommendations
     */
    generateRecommendations(result) {
        const recommendations = {
            immediate: [],
            shortTerm: [],
            longTerm: [],
            preventive: [],
        };
        if (result.analysis.threatAssessment.overallThreatLevel === 'critical') {
            recommendations.immediate.push('Activate incident response team');
        }
        recommendations.shortTerm.push('Review and update detection rules');
        recommendations.longTerm.push('Enhance threat hunting capabilities');
        recommendations.preventive.push('Implement additional monitoring');
        return recommendations;
    }
    /**
     * Get confidence score
     */
    getConfidenceScore(confidence) {
        const scores = {
            [confidence_level_enum_1.ConfidenceLevel.UNKNOWN]: 0,
            [confidence_level_enum_1.ConfidenceLevel.LOW]: 20,
            [confidence_level_enum_1.ConfidenceLevel.MEDIUM]: 50,
            [confidence_level_enum_1.ConfidenceLevel.HIGH]: 80,
            [confidence_level_enum_1.ConfidenceLevel.VERY_HIGH]: 95,
            [confidence_level_enum_1.ConfidenceLevel.CONFIRMED]: 100,
        };
        return scores[confidence] || 0;
    }
    /**
     * Get predefined hunting queries
     */
    async getPredefinedQueries() {
        return [
            {
                name: 'Suspicious PowerShell Activity',
                description: 'Hunt for suspicious PowerShell execution patterns',
                type: 'pattern_matching',
                parameters: {
                    timeRange: {
                        from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                        to: new Date(),
                    },
                    criteria: {
                        processName: 'powershell.exe',
                        commandLinePatterns: ['encoded', 'bypass', 'hidden'],
                    },
                },
                priority: 'high',
                expectedResults: {
                    estimatedMatches: 50,
                    confidenceThreshold: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                    falsePositiveRate: 0.2,
                },
            },
            {
                name: 'Lateral Movement Detection',
                description: 'Detect potential lateral movement activities',
                type: 'behavioral_analysis',
                parameters: {
                    timeRange: {
                        from: new Date(Date.now() - 24 * 60 * 60 * 1000),
                        to: new Date(),
                    },
                    criteria: {
                        loginPatterns: ['multiple_hosts', 'unusual_times'],
                        networkConnections: ['internal_scanning', 'admin_shares'],
                    },
                },
                priority: 'critical',
                expectedResults: {
                    estimatedMatches: 10,
                    confidenceThreshold: confidence_level_enum_1.ConfidenceLevel.HIGH,
                    falsePositiveRate: 0.1,
                },
            },
        ];
    }
};
exports.ThreatHuntingService = ThreatHuntingService;
exports.ThreatHuntingService = ThreatHuntingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof security_event_repository_interface_1.SecurityEventRepository !== "undefined" && security_event_repository_interface_1.SecurityEventRepository) === "function" ? _a : Object, typeof (_b = typeof vulnerability_repository_interface_1.VulnerabilityRepository !== "undefined" && vulnerability_repository_interface_1.VulnerabilityRepository) === "function" ? _b : Object, typeof (_c = typeof incident_repository_interface_1.IncidentRepository !== "undefined" && incident_repository_interface_1.IncidentRepository) === "function" ? _c : Object, typeof (_d = typeof correlation_repository_interface_1.CorrelationRepository !== "undefined" && correlation_repository_interface_1.CorrelationRepository) === "function" ? _d : Object, typeof (_e = typeof domain_event_publisher_1.DomainEventPublisher !== "undefined" && domain_event_publisher_1.DomainEventPublisher) === "function" ? _e : Object])
], ThreatHuntingService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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