{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\confidence-level.enum.ts", "mappings": ";;;AAAA;;;;;;;;;GASG;AACH,IAAY,eA8DX;AA9DD,WAAY,eAAe;IACzB;;;;;;OAMG;IACH,wCAAqB,CAAA;IAErB;;;;;;OAMG;IACH,8BAAW,CAAA;IAEX;;;;;;OAMG;IACH,oCAAiB,CAAA;IAEjB;;;;;;OAMG;IACH,gCAAa,CAAA;IAEb;;;;;;OAMG;IACH,0CAAuB,CAAA;IAEvB;;;;;;OAMG;IACH,0CAAuB,CAAA;IAEvB;;;;;OAKG;IACH,sCAAmB,CAAA;AACrB,CAAC,EA9DW,eAAe,+BAAf,eAAe,QA8D1B;AAED;;GAEG;AACH,MAAa,oBAAoB;IAC/B;;OAEG;IACH,MAAM,CAAC,sBAAsB;QAC3B,OAAO,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,6BAA6B;QAClC,OAAO;YACL,eAAe,CAAC,MAAM;YACtB,eAAe,CAAC,IAAI;YACpB,eAAe,CAAC,SAAS;YACzB,eAAe,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB;QAC5B,OAAO;YACL,eAAe,CAAC,IAAI;YACpB,eAAe,CAAC,SAAS;YACzB,eAAe,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB;QAC3B,OAAO;YACL,eAAe,CAAC,QAAQ;YACxB,eAAe,CAAC,GAAG;YACnB,eAAe,CAAC,MAAM;YACtB,eAAe,CAAC,OAAO;SACxB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,UAA2B;QAChD,MAAM,MAAM,GAAoC;YAC9C,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE;YAC9B,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE;YACzB,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE;YAC5B,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE;YAC1B,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,EAAE;YAC/B,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,EAAE;YAC/B,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC;SAC7B,CAAC;QACF,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,KAAa;QACnC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;YAC7B,OAAO,eAAe,CAAC,OAAO,CAAC;QACjC,CAAC;QAED,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,eAAe,CAAC,SAAS,CAAC;QAClD,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,eAAe,CAAC,SAAS,CAAC;QAClD,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,eAAe,CAAC,IAAI,CAAC;QAC7C,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,eAAe,CAAC,MAAM,CAAC;QAC/C,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,eAAe,CAAC,GAAG,CAAC;QAC5C,IAAI,KAAK,IAAI,CAAC;YAAE,OAAO,eAAe,CAAC,QAAQ,CAAC;QAChD,OAAO,eAAe,CAAC,OAAO,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,WAA4B,EAAE,WAA4B;QACvE,MAAM,MAAM,GAAG,oBAAoB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QACjE,MAAM,MAAM,GAAG,oBAAoB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QACjE,OAAO,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,WAA4B,EAAE,WAA4B;QACzE,OAAO,oBAAoB,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC;IACjG,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,WAA4B,EAAE,WAA4B;QACxE,OAAO,oBAAoB,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC;IACjG,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,UAA2B;QAC7C,OAAO,oBAAoB,CAAC,6BAA6B,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,UAA2B;QACvC,OAAO,oBAAoB,CAAC,uBAAuB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,UAA2B;QACtC,OAAO,oBAAoB,CAAC,sBAAsB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,UAA2B;QAC5C,OAAO,UAAU,KAAK,eAAe,CAAC,SAAS,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,UAA2B;QAC7C,MAAM,MAAM,GAAoC;YAC9C,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,SAAS,EAAM,QAAQ;YACpD,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,SAAS,EAAK,UAAU;YACrD,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,SAAS,EAAU,OAAO;YAClD,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,SAAS,EAAQ,QAAQ;YACnD,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,SAAS,EAAW,MAAM;YACjD,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAM,WAAW;YACtD,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,SAAS,EAAO,OAAO;SACnD,CAAC;QACF,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,UAA2B;QAC5C,MAAM,KAAK,GAAoC;YAC7C,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,cAAc;YAC3C,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,sBAAsB;YACnD,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,WAAW;YACnC,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,aAAa;YACvC,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,cAAc;YACrC,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,UAAU;YACtC,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,sBAAsB;SAClD,CAAC;QACF,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,UAA2B;QAC/C,MAAM,YAAY,GAAoC;YACpD,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,oEAAoE;YACjG,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,iEAAiE;YAC9F,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,iEAAiE;YACzF,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,4DAA4D;YACtF,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,+DAA+D;YACtF,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,qEAAqE;YACjG,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,gEAAgE;SAC5F,CAAC;QACF,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,UAA2B;QACtD,MAAM,OAAO,GAAsC;YACjD,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE;gBAC3B,yCAAyC;gBACzC,gCAAgC;gBAChC,oCAAoC;gBACpC,kCAAkC;aACnC;YACD,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE;gBAC3B,+BAA+B;gBAC/B,+BAA+B;gBAC/B,sBAAsB;gBACtB,2BAA2B;aAC5B;YACD,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;gBACtB,mCAAmC;gBACnC,4BAA4B;gBAC5B,8BAA8B;gBAC9B,wBAAwB;aACzB;YACD,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE;gBACxB,wBAAwB;gBACxB,8BAA8B;gBAC9B,iBAAiB;gBACjB,2BAA2B;aAC5B;YACD,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE;gBACrB,0BAA0B;gBAC1B,sBAAsB;gBACtB,gCAAgC;gBAChC,sBAAsB;aACvB;YACD,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE;gBAC1B,qCAAqC;gBACrC,yBAAyB;gBACzB,8BAA8B;gBAC9B,mCAAmC;aACpC;YACD,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE;gBACzB,8BAA8B;gBAC9B,4BAA4B;gBAC5B,sBAAsB;gBACtB,sBAAsB;aACvB;SACF,CAAC;QACF,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAAC,UAA2B;QACzD,MAAM,UAAU,GAAoE;YAClF,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,UAAU;YACvC,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,MAAM;YACnC,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,MAAM;YAC9B,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,QAAQ;YAClC,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,QAAQ;YAC/B,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,KAAK;YACjC,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,KAAK;SACjC,CAAC;QACF,OAAO,UAAU,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,UAA2B;QACnD,MAAM,SAAS,GAAqE;YAClF,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,WAAW;YACxC,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,MAAM;YACnC,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,MAAM;YAC9B,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,QAAQ;YAClC,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,QAAQ;YAC/B,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,KAAK;YACjC,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,KAAK;SACjC,CAAC;QACF,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,UAA2B;QACvD,0BAA0B;QAC1B,MAAM,UAAU,GAAoC;YAClD,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,GAAG,EAAK,aAAa;YAClD,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC,EAAO,SAAS;YAC9C,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,EAAY,UAAU;YAC/C,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,EAAU,UAAU;YAC/C,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,EAAa,UAAU;YAC/C,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAO,WAAW;YAChD,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,EAAE,EAAQ,WAAW;SACjD,CAAC;QACF,OAAO,UAAU,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,UAAkB;QAC/B,OAAO,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,UAA6B,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,KAAa;QAC7B,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QACtE,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QACxD,OAAO,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,UAAU,CAAC,IAAI,IAAI,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,2BAA2B,CAChC,WAA8D;QAE9D,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,eAAe,CAAC,OAAO,CAAC;QACjC,CAAC;QAED,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC5E,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,eAAe,CAAC,OAAO,CAAC;QACjC,CAAC;QAED,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACnD,MAAM,YAAY,GAAG,oBAAoB,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtE,OAAO,GAAG,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5C,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,MAAM,YAAY,GAAG,WAAW,GAAG,WAAW,CAAC;QAC/C,OAAO,oBAAoB,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CACzB,kBAAmC,EACnC,SAAiB,EACjB,YAAoB,GAAG;QAEvB,MAAM,aAAa,GAAG,oBAAoB,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;QAC/E,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;QACrD,MAAM,YAAY,GAAG,aAAa,GAAG,WAAW,CAAC;QAEjD,OAAO,oBAAoB,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAC1B,kBAAmC,EACnC,gBAA4D;QAE5D,MAAM,aAAa,GAAG,oBAAoB,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;QAE/E,MAAM,MAAM,GAA2B;YACrC,MAAM,EAAE,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,MAAM,EAAE,EAAE;YACV,IAAI,EAAE,EAAE;SACT,CAAC;QAEF,MAAM,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC5C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,GAAG,KAAK,CAAC,CAAC;QAE1D,OAAO,oBAAoB,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;IAC7D,CAAC;CACF;AA/VD,oDA+VC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\confidence-level.enum.ts"], "sourcesContent": ["/**\r\n * Confidence Level Enum\r\n * \r\n * Represents confidence levels for security assessments, threat intelligence,\r\n * and automated analysis results. Used to indicate the reliability and\r\n * trustworthiness of security findings and recommendations.\r\n * \r\n * Confidence levels help security analysts prioritize investigations\r\n * and make informed decisions about response actions.\r\n */\r\nexport enum ConfidenceLevel {\r\n  /**\r\n   * Very Low Confidence (0-20%)\r\n   * - Preliminary or unverified findings\r\n   * - Automated analysis with high uncertainty\r\n   * - Insufficient data for reliable assessment\r\n   * - Requires significant additional investigation\r\n   */\r\n  VERY_LOW = 'very_low',\r\n\r\n  /**\r\n   * Low Confidence (21-40%)\r\n   * - Limited supporting evidence\r\n   * - Single-source information\r\n   * - Automated analysis with moderate uncertainty\r\n   * - Requires additional verification\r\n   */\r\n  LOW = 'low',\r\n\r\n  /**\r\n   * Medium Confidence (41-60%)\r\n   * - Moderate supporting evidence\r\n   * - Multiple sources with some correlation\r\n   * - Automated analysis with reasonable accuracy\r\n   * - Some manual verification completed\r\n   */\r\n  MEDIUM = 'medium',\r\n\r\n  /**\r\n   * High Confidence (61-80%)\r\n   * - Strong supporting evidence\r\n   * - Multiple corroborating sources\r\n   * - Automated analysis with high accuracy\r\n   * - Significant manual verification\r\n   */\r\n  HIGH = 'high',\r\n\r\n  /**\r\n   * Very High Confidence (81-95%)\r\n   * - Extensive supporting evidence\r\n   * - Multiple independent sources\r\n   * - Expert analysis and verification\r\n   * - Comprehensive investigation completed\r\n   */\r\n  VERY_HIGH = 'very_high',\r\n\r\n  /**\r\n   * Confirmed (96-100%)\r\n   * - Definitive evidence\r\n   * - Multiple independent confirmations\r\n   * - Expert validation\r\n   * - No reasonable doubt\r\n   */\r\n  CONFIRMED = 'confirmed',\r\n\r\n  /**\r\n   * Unknown Confidence\r\n   * - Confidence level cannot be determined\r\n   * - Insufficient metadata\r\n   * - Assessment pending\r\n   */\r\n  UNKNOWN = 'unknown',\r\n}\r\n\r\n/**\r\n * Confidence Level Utilities\r\n */\r\nexport class ConfidenceLevelUtils {\r\n  /**\r\n   * Get all confidence levels\r\n   */\r\n  static getAllConfidenceLevels(): ConfidenceLevel[] {\r\n    return Object.values(ConfidenceLevel);\r\n  }\r\n\r\n  /**\r\n   * Get actionable confidence levels (medium and above)\r\n   */\r\n  static getActionableConfidenceLevels(): ConfidenceLevel[] {\r\n    return [\r\n      ConfidenceLevel.MEDIUM,\r\n      ConfidenceLevel.HIGH,\r\n      ConfidenceLevel.VERY_HIGH,\r\n      ConfidenceLevel.CONFIRMED,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get high confidence levels (high and above)\r\n   */\r\n  static getHighConfidenceLevels(): ConfidenceLevel[] {\r\n    return [\r\n      ConfidenceLevel.HIGH,\r\n      ConfidenceLevel.VERY_HIGH,\r\n      ConfidenceLevel.CONFIRMED,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get low confidence levels (medium and below)\r\n   */\r\n  static getLowConfidenceLevels(): ConfidenceLevel[] {\r\n    return [\r\n      ConfidenceLevel.VERY_LOW,\r\n      ConfidenceLevel.LOW,\r\n      ConfidenceLevel.MEDIUM,\r\n      ConfidenceLevel.UNKNOWN,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get numeric value for confidence level (0-100)\r\n   */\r\n  static getNumericValue(confidence: ConfidenceLevel): number {\r\n    const values: Record<ConfidenceLevel, number> = {\r\n      [ConfidenceLevel.VERY_LOW]: 10,\r\n      [ConfidenceLevel.LOW]: 30,\r\n      [ConfidenceLevel.MEDIUM]: 50,\r\n      [ConfidenceLevel.HIGH]: 70,\r\n      [ConfidenceLevel.VERY_HIGH]: 85,\r\n      [ConfidenceLevel.CONFIRMED]: 95,\r\n      [ConfidenceLevel.UNKNOWN]: 0,\r\n    };\r\n    return values[confidence];\r\n  }\r\n\r\n  /**\r\n   * Get confidence level from numeric score (0-100)\r\n   */\r\n  static fromNumericValue(score: number): ConfidenceLevel {\r\n    if (score < 0 || score > 100) {\r\n      return ConfidenceLevel.UNKNOWN;\r\n    }\r\n\r\n    if (score >= 96) return ConfidenceLevel.CONFIRMED;\r\n    if (score >= 81) return ConfidenceLevel.VERY_HIGH;\r\n    if (score >= 61) return ConfidenceLevel.HIGH;\r\n    if (score >= 41) return ConfidenceLevel.MEDIUM;\r\n    if (score >= 21) return ConfidenceLevel.LOW;\r\n    if (score >= 1) return ConfidenceLevel.VERY_LOW;\r\n    return ConfidenceLevel.UNKNOWN;\r\n  }\r\n\r\n  /**\r\n   * Compare two confidence levels\r\n   */\r\n  static compare(confidence1: ConfidenceLevel, confidence2: ConfidenceLevel): number {\r\n    const value1 = ConfidenceLevelUtils.getNumericValue(confidence1);\r\n    const value2 = ConfidenceLevelUtils.getNumericValue(confidence2);\r\n    return value1 - value2;\r\n  }\r\n\r\n  /**\r\n   * Get the higher of two confidence levels\r\n   */\r\n  static getHigher(confidence1: ConfidenceLevel, confidence2: ConfidenceLevel): ConfidenceLevel {\r\n    return ConfidenceLevelUtils.compare(confidence1, confidence2) >= 0 ? confidence1 : confidence2;\r\n  }\r\n\r\n  /**\r\n   * Get the lower of two confidence levels\r\n   */\r\n  static getLower(confidence1: ConfidenceLevel, confidence2: ConfidenceLevel): ConfidenceLevel {\r\n    return ConfidenceLevelUtils.compare(confidence1, confidence2) <= 0 ? confidence1 : confidence2;\r\n  }\r\n\r\n  /**\r\n   * Check if confidence level is actionable\r\n   */\r\n  static isActionable(confidence: ConfidenceLevel): boolean {\r\n    return ConfidenceLevelUtils.getActionableConfidenceLevels().includes(confidence);\r\n  }\r\n\r\n  /**\r\n   * Check if confidence level is high\r\n   */\r\n  static isHigh(confidence: ConfidenceLevel): boolean {\r\n    return ConfidenceLevelUtils.getHighConfidenceLevels().includes(confidence);\r\n  }\r\n\r\n  /**\r\n   * Check if confidence level is low\r\n   */\r\n  static isLow(confidence: ConfidenceLevel): boolean {\r\n    return ConfidenceLevelUtils.getLowConfidenceLevels().includes(confidence);\r\n  }\r\n\r\n  /**\r\n   * Check if confidence level is confirmed\r\n   */\r\n  static isConfirmed(confidence: ConfidenceLevel): boolean {\r\n    return confidence === ConfidenceLevel.CONFIRMED;\r\n  }\r\n\r\n  /**\r\n   * Get color code for UI display\r\n   */\r\n  static getColorCode(confidence: ConfidenceLevel): string {\r\n    const colors: Record<ConfidenceLevel, string> = {\r\n      [ConfidenceLevel.CONFIRMED]: '#059669',     // Green\r\n      [ConfidenceLevel.VERY_HIGH]: '#10B981',    // Emerald\r\n      [ConfidenceLevel.HIGH]: '#3B82F6',         // Blue\r\n      [ConfidenceLevel.MEDIUM]: '#F59E0B',       // Amber\r\n      [ConfidenceLevel.LOW]: '#EF4444',          // Red\r\n      [ConfidenceLevel.VERY_LOW]: '#DC2626',     // Dark Red\r\n      [ConfidenceLevel.UNKNOWN]: '#6B7280',      // Gray\r\n    };\r\n    return colors[confidence];\r\n  }\r\n\r\n  /**\r\n   * Get icon name for UI display\r\n   */\r\n  static getIconName(confidence: ConfidenceLevel): string {\r\n    const icons: Record<ConfidenceLevel, string> = {\r\n      [ConfidenceLevel.CONFIRMED]: 'check-circle',\r\n      [ConfidenceLevel.VERY_HIGH]: 'check-circle-outline',\r\n      [ConfidenceLevel.HIGH]: 'thumbs-up',\r\n      [ConfidenceLevel.MEDIUM]: 'help-circle',\r\n      [ConfidenceLevel.LOW]: 'alert-circle',\r\n      [ConfidenceLevel.VERY_LOW]: 'x-circle',\r\n      [ConfidenceLevel.UNKNOWN]: 'question-mark-circle',\r\n    };\r\n    return icons[confidence];\r\n  }\r\n\r\n  /**\r\n   * Get human-readable description\r\n   */\r\n  static getDescription(confidence: ConfidenceLevel): string {\r\n    const descriptions: Record<ConfidenceLevel, string> = {\r\n      [ConfidenceLevel.CONFIRMED]: 'Definitive evidence with expert validation and no reasonable doubt',\r\n      [ConfidenceLevel.VERY_HIGH]: 'Extensive supporting evidence from multiple independent sources',\r\n      [ConfidenceLevel.HIGH]: 'Strong supporting evidence with significant manual verification',\r\n      [ConfidenceLevel.MEDIUM]: 'Moderate supporting evidence with some manual verification',\r\n      [ConfidenceLevel.LOW]: 'Limited supporting evidence requiring additional verification',\r\n      [ConfidenceLevel.VERY_LOW]: 'Preliminary findings requiring significant additional investigation',\r\n      [ConfidenceLevel.UNKNOWN]: 'Confidence level cannot be determined due to insufficient data',\r\n    };\r\n    return descriptions[confidence];\r\n  }\r\n\r\n  /**\r\n   * Get recommended actions based on confidence level\r\n   */\r\n  static getRecommendedActions(confidence: ConfidenceLevel): string[] {\r\n    const actions: Record<ConfidenceLevel, string[]> = {\r\n      [ConfidenceLevel.CONFIRMED]: [\r\n        'Proceed with immediate response actions',\r\n        'Implement containment measures',\r\n        'Notify stakeholders and management',\r\n        'Document findings for compliance',\r\n      ],\r\n      [ConfidenceLevel.VERY_HIGH]: [\r\n        'Proceed with response actions',\r\n        'Consider containment measures',\r\n        'Notify security team',\r\n        'Prepare incident response',\r\n      ],\r\n      [ConfidenceLevel.HIGH]: [\r\n        'Initiate investigation procedures',\r\n        'Gather additional evidence',\r\n        'Monitor for related activity',\r\n        'Prepare response plans',\r\n      ],\r\n      [ConfidenceLevel.MEDIUM]: [\r\n        'Continue investigation',\r\n        'Seek additional verification',\r\n        'Monitor closely',\r\n        'Prepare contingency plans',\r\n      ],\r\n      [ConfidenceLevel.LOW]: [\r\n        'Conduct further analysis',\r\n        'Gather more evidence',\r\n        'Verify with additional sources',\r\n        'Monitor for patterns',\r\n      ],\r\n      [ConfidenceLevel.VERY_LOW]: [\r\n        'Perform comprehensive investigation',\r\n        'Collect additional data',\r\n        'Verify findings with experts',\r\n        'Consider alternative explanations',\r\n      ],\r\n      [ConfidenceLevel.UNKNOWN]: [\r\n        'Assess available information',\r\n        'Determine confidence level',\r\n        'Gather baseline data',\r\n        'Establish monitoring',\r\n      ],\r\n    };\r\n    return actions[confidence];\r\n  }\r\n\r\n  /**\r\n   * Get investigation priority based on confidence level\r\n   */\r\n  static getInvestigationPriority(confidence: ConfidenceLevel): 'low' | 'medium' | 'high' | 'critical' {\r\n    const priorities: Record<ConfidenceLevel, 'low' | 'medium' | 'high' | 'critical'> = {\r\n      [ConfidenceLevel.CONFIRMED]: 'critical',\r\n      [ConfidenceLevel.VERY_HIGH]: 'high',\r\n      [ConfidenceLevel.HIGH]: 'high',\r\n      [ConfidenceLevel.MEDIUM]: 'medium',\r\n      [ConfidenceLevel.LOW]: 'medium',\r\n      [ConfidenceLevel.VERY_LOW]: 'low',\r\n      [ConfidenceLevel.UNKNOWN]: 'low',\r\n    };\r\n    return priorities[confidence];\r\n  }\r\n\r\n  /**\r\n   * Get response urgency based on confidence level\r\n   */\r\n  static getResponseUrgency(confidence: ConfidenceLevel): 'low' | 'medium' | 'high' | 'immediate' {\r\n    const urgencies: Record<ConfidenceLevel, 'low' | 'medium' | 'high' | 'immediate'> = {\r\n      [ConfidenceLevel.CONFIRMED]: 'immediate',\r\n      [ConfidenceLevel.VERY_HIGH]: 'high',\r\n      [ConfidenceLevel.HIGH]: 'high',\r\n      [ConfidenceLevel.MEDIUM]: 'medium',\r\n      [ConfidenceLevel.LOW]: 'medium',\r\n      [ConfidenceLevel.VERY_LOW]: 'low',\r\n      [ConfidenceLevel.UNKNOWN]: 'low',\r\n    };\r\n    return urgencies[confidence];\r\n  }\r\n\r\n  /**\r\n   * Get escalation threshold\r\n   */\r\n  static getEscalationThreshold(confidence: ConfidenceLevel): number {\r\n    // Hours before escalation\r\n    const thresholds: Record<ConfidenceLevel, number> = {\r\n      [ConfidenceLevel.CONFIRMED]: 0.5,    // 30 minutes\r\n      [ConfidenceLevel.VERY_HIGH]: 1,      // 1 hour\r\n      [ConfidenceLevel.HIGH]: 2,           // 2 hours\r\n      [ConfidenceLevel.MEDIUM]: 4,         // 4 hours\r\n      [ConfidenceLevel.LOW]: 8,            // 8 hours\r\n      [ConfidenceLevel.VERY_LOW]: 24,      // 24 hours\r\n      [ConfidenceLevel.UNKNOWN]: 48,       // 48 hours\r\n    };\r\n    return thresholds[confidence];\r\n  }\r\n\r\n  /**\r\n   * Validate confidence level\r\n   */\r\n  static isValid(confidence: string): boolean {\r\n    return Object.values(ConfidenceLevel).includes(confidence as ConfidenceLevel);\r\n  }\r\n\r\n  /**\r\n   * Get confidence level from string (case-insensitive)\r\n   */\r\n  static fromString(value: string): ConfidenceLevel | null {\r\n    const normalized = value.toLowerCase().trim().replace(/[^a-z]/g, '_');\r\n    const confidenceLevels = Object.values(ConfidenceLevel);\r\n    return confidenceLevels.find(level => level === normalized) || null;\r\n  }\r\n\r\n  /**\r\n   * Calculate combined confidence from multiple sources\r\n   */\r\n  static calculateCombinedConfidence(\r\n    confidences: Array<{ level: ConfidenceLevel; weight: number }>\r\n  ): ConfidenceLevel {\r\n    if (confidences.length === 0) {\r\n      return ConfidenceLevel.UNKNOWN;\r\n    }\r\n\r\n    const totalWeight = confidences.reduce((sum, item) => sum + item.weight, 0);\r\n    if (totalWeight === 0) {\r\n      return ConfidenceLevel.UNKNOWN;\r\n    }\r\n\r\n    const weightedSum = confidences.reduce((sum, item) => {\r\n      const numericValue = ConfidenceLevelUtils.getNumericValue(item.level);\r\n      return sum + (numericValue * item.weight);\r\n    }, 0);\r\n\r\n    const averageScore = weightedSum / totalWeight;\r\n    return ConfidenceLevelUtils.fromNumericValue(averageScore);\r\n  }\r\n\r\n  /**\r\n   * Get confidence decay over time\r\n   */\r\n  static getDecayedConfidence(\r\n    originalConfidence: ConfidenceLevel,\r\n    ageInDays: number,\r\n    decayRate: number = 0.1\r\n  ): ConfidenceLevel {\r\n    const originalValue = ConfidenceLevelUtils.getNumericValue(originalConfidence);\r\n    const decayFactor = Math.exp(-decayRate * ageInDays);\r\n    const decayedValue = originalValue * decayFactor;\r\n    \r\n    return ConfidenceLevelUtils.fromNumericValue(decayedValue);\r\n  }\r\n\r\n  /**\r\n   * Get confidence boost from verification\r\n   */\r\n  static getVerifiedConfidence(\r\n    originalConfidence: ConfidenceLevel,\r\n    verificationType: 'manual' | 'automated' | 'expert' | 'peer'\r\n  ): ConfidenceLevel {\r\n    const originalValue = ConfidenceLevelUtils.getNumericValue(originalConfidence);\r\n    \r\n    const boosts: Record<string, number> = {\r\n      manual: 10,\r\n      automated: 5,\r\n      expert: 20,\r\n      peer: 15,\r\n    };\r\n    \r\n    const boost = boosts[verificationType] || 0;\r\n    const boostedValue = Math.min(100, originalValue + boost);\r\n    \r\n    return ConfidenceLevelUtils.fromNumericValue(boostedValue);\r\n  }\r\n}\r\n"], "version": 3}