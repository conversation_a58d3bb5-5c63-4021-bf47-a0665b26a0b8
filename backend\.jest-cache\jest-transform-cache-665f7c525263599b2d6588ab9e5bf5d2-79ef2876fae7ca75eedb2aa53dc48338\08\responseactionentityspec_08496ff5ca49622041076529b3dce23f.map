{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\response-action.entity.spec.ts", "mappings": ";;AAAA,sEAAgF;AAChF,gEAA8D;AAC9D,mEAA0D;AAC1D,uEAA8D;AAC9D,4GAAqG;AACrG,0HAAkH;AAClH,8GAAuG;AACvG,0GAAmG;AACnG,oHAA4G;AAE5G,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,IAAI,UAA+B,CAAC;IAEpC,UAAU,CAAC,GAAG,EAAE;QACd,UAAU,GAAG;YACX,UAAU,EAAE,6BAAU,CAAC,QAAQ;YAC/B,MAAM,EAAE,iCAAY,CAAC,OAAO;YAC5B,KAAK,EAAE,4BAA4B;YACnC,WAAW,EAAE,0CAA0C;YACvD,UAAU,EAAE,EAAE,SAAS,EAAE,eAAe,EAAE;YAC1C,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;aACtB;YACD,QAAQ,EAAE,MAAM;YAChB,WAAW,EAAE,IAAI;YACjB,YAAY,EAAE,IAAI;YAClB,wBAAwB,EAAE,CAAC;YAC3B,mBAAmB,EAAE,CAAC,wBAAwB,CAAC;YAC/C,gBAAgB,EAAE,KAAK;YACvB,eAAe,EAAE,CAAC,gCAAgC,EAAE,yBAAyB,CAAC;YAC9E,UAAU,EAAE,CAAC;YACb,UAAU,EAAE,CAAC;YACb,iBAAiB,EAAE,CAAC;YACpB,YAAY,EAAE;gBACZ,WAAW,EAAE,IAAI;gBACjB,aAAa,EAAE,CAAC,2BAA2B,EAAE,uBAAuB,CAAC;aACtE;YACD,UAAU,EAAE,KAAK;YACjB,IAAI,EAAE,CAAC,UAAU,EAAE,aAAa,CAAC;YACjC,QAAQ,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE;YACxC,cAAc,EAAE,EAAE;YAClB,cAAc,EAAE,EAAE;YAClB,QAAQ,EAAE,KAAK;SAChB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAEjD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,uCAAc,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAE7C,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,uEAAgC,CAAC,CAAC;YAEnE,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAqC,CAAC;YACnE,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YAC1D,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC9D,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,QAAQ,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAE3D,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,UAAU,CAAC;YAExC,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;QACvG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,MAAM,CAAC;YAEpC,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;QACjG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;YAElD,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;QAC1G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YAE/D,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;QACjH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAEtE,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,0DAA0D,CAAC,CAAC;QACxH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,UAAU,CAAC;YAExC,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,QAAQ,CAAC;YAEtC,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,WAAW,CAAC;YAEzC,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,gDAAgD,CAAC,CAAC;QAC9G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,YAAY,CAAC;YAE1C,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;QAC/G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,mBAAmB,EAAE,cAAqB,EAAE,CAAC;YAEnF,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,qDAAqD,CAAC,CAAC;QACnH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,gBAAgB,CAAC;YAE9C,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,qDAAqD,CAAC,CAAC;QACnH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,eAAe,EAAE,cAAqB,EAAE,CAAC;YAE/E,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;QAC/G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC;YAEvD,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC;YAEvD,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,iBAAiB,EAAE,CAAC,CAAC,EAAE,CAAC;YAE9D,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,IAAI,EAAE,cAAqB,EAAE,CAAC;YAEpE,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAEpE,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;QAC5G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,QAAQ,CAAC;YAEtC,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;QACxG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,cAAc,EAAE,cAAqB,EAAE,CAAC;YAE9E,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;QAC/G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;YAE1D,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,QAAQ,CAAC;YAEtC,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,6CAA6C,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,UAAU,CAAC;YAExC,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;QACjH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,uBAAuB;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,YAAY,CAAC,iCAAY,CAAC,QAAQ,EAAE,OAAO,EAAE,wBAAwB,CAAC,CAAC;YAE9E,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAExC,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,oFAAsC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,YAAY,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;YAE1C,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QACjG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,YAAY,CAAC,iCAAY,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,YAAY,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;YACzC,MAAM,CAAC,YAAY,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;YAC5C,MAAM,CAAC,YAAY,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,iCAAY,CAAC,SAAS,EAAE,CAAC,CAAC;YAClF,MAAM,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,gBAAgB,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;YAE/D,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YAE3C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,yEAAiC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,aAAa,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAExD,MAAM,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;QAC7G,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,iCAAY,CAAC,SAAS,EAAE,CAAC,CAAC;YAClF,MAAM,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,MAAM,KAAK,GAAG,iBAAiB,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAE7B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE9C,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,qEAA+B,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,aAAa,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAExD,MAAM,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC;QAC9G,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,iCAAY,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;YACtH,MAAM,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,mCAAmC;YACnC,MAAM,YAAY,GAAG,uCAAc,CAAC,MAAM,CAAC;gBACzC,GAAG,UAAU;gBACb,MAAM,EAAE,iCAAY,CAAC,MAAM;gBAC3B,UAAU,EAAE,CAAC;gBACb,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,YAAY,CAAC,WAAW,EAAE,CAAC;YAE3B,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAE5B,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,aAAa,EAAE,CAAC;YACpD,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAE/C,MAAM,MAAM,GAAG,YAAY,CAAC,oBAAoB,EAAE,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,oFAAsC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,gBAAgB,GAAG,uCAAc,CAAC,MAAM,CAAC;gBAC7C,GAAG,UAAU;gBACb,MAAM,EAAE,iCAAY,CAAC,MAAM;gBAC3B,UAAU,EAAE,CAAC;gBACb,UAAU,EAAE,CAAC;gBACb,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,YAAY,GAAG,uCAAc,CAAC,MAAM,CAAC;gBACzC,GAAG,UAAU;gBACb,MAAM,EAAE,iCAAY,CAAC,MAAM;gBAC3B,UAAU,EAAE,CAAC;gBACb,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3C,MAAM,gBAAgB,GAAG,uCAAc,CAAC,MAAM,CAAC;gBAC7C,GAAG,UAAU;gBACb,MAAM,EAAE,iCAAY,CAAC,MAAM;gBAC3B,UAAU,EAAE,CAAC;gBACb,UAAU,EAAE,CAAC;gBACb,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC;gBAC7B,GAAG,UAAU;gBACb,MAAM,EAAE,iCAAY,CAAC,SAAS;gBAC9B,YAAY,EAAE,IAAI;gBAClB,YAAY,EAAE;oBACZ,WAAW,EAAE,IAAI;oBACjB,aAAa,EAAE,CAAC,2BAA2B,CAAC;iBAC7C;gBACD,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,4CAA4C;YAC5C,MAAM,eAAe,GAAG,uCAAc,CAAC,MAAM,CAAC;gBAC5C,GAAG,UAAU;gBACb,MAAM,EAAE,iCAAY,CAAC,SAAS;gBAC9B,YAAY,EAAE,IAAI;gBAClB,YAAY,EAAE;oBACZ,WAAW,EAAE,IAAI;oBACjB,aAAa,EAAE,CAAC,2BAA2B,CAAC;iBAC7C;gBACD,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,eAAe,CAAC,WAAW,EAAE,CAAC;YAE9B,MAAM,eAAe,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;YAE5C,eAAe,CAAC,QAAQ,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YAEnD,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACtD,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAElF,MAAM,MAAM,GAAG,eAAe,CAAC,oBAAoB,EAAE,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,8EAAmC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,mBAAmB,GAAG,uCAAc,CAAC,MAAM,CAAC;gBAChD,GAAG,UAAU;gBACb,MAAM,EAAE,iCAAY,CAAC,SAAS;gBAC9B,YAAY,EAAE,KAAK;gBACnB,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE,CAAC,mBAAmB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,eAAe,GAAG,uCAAc,CAAC,MAAM,CAAC;gBAC5C,GAAG,UAAU;gBACb,MAAM,EAAE,iCAAY,CAAC,SAAS;gBAC9B,YAAY,EAAE,IAAI;gBAClB,YAAY,EAAE;oBACZ,WAAW,EAAE,IAAI;oBACjB,aAAa,EAAE,CAAC,2BAA2B,CAAC;iBAC7C;gBACD,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,MAAM,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjD,MAAM,mBAAmB,GAAG,uCAAc,CAAC,MAAM,CAAC;gBAChD,GAAG,UAAU;gBACb,MAAM,EAAE,iCAAY,CAAC,SAAS;gBAC9B,YAAY,EAAE,KAAK;gBACnB,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,OAAO,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAE1C,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAE/B,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,OAAO,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAE1C,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC/B,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAE/B,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,OAAO,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAE1C,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC/B,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAElC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iBAAiB,EAAE,GAAG,EAAE;YACzB,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;YAEvC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;YAEvC,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,UAAU,CAAC,CAAC;YACnE,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAEhE,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;QACjG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,MAAM,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;YAEhC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;YAE9C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,yBAAyB;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAEhC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,eAAe,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,iCAAY,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;YACzH,MAAM,aAAa,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,iCAAY,CAAC,OAAO,EAAE,CAAC,CAAC;YAE7F,MAAM,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,eAAe,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,iCAAY,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;YACzH,MAAM,aAAa,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,iCAAY,CAAC,OAAO,EAAE,CAAC,CAAC;YAE7F,MAAM,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,eAAe,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,iCAAY,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;YACzH,MAAM,YAAY,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,iCAAY,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;YAEnH,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,YAAY,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,iCAAY,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;YACnH,MAAM,eAAe,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,iCAAY,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;YAEzH,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,cAAc,GAAG,uCAAc,CAAC,MAAM,CAAC;gBAC3C,GAAG,UAAU;gBACb,MAAM,EAAE,iCAAY,CAAC,OAAO;gBAC5B,gBAAgB,EAAE,IAAI;aACvB,CAAC,CAAC;YACH,MAAM,gBAAgB,GAAG,uCAAc,CAAC,MAAM,CAAC;gBAC7C,GAAG,UAAU;gBACb,MAAM,EAAE,iCAAY,CAAC,OAAO;gBAC5B,gBAAgB,EAAE,KAAK;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,kBAAkB,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;YACtF,MAAM,oBAAoB,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;YAE1F,MAAM,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,oBAAoB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,aAAa,GAAG,uCAAc,CAAC,MAAM,CAAC;gBAC1C,GAAG,UAAU;gBACb,MAAM,EAAE,iCAAY,CAAC,OAAO;gBAC5B,WAAW,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,aAAa;aAC1D,CAAC,CAAC;YACH,MAAM,gBAAgB,GAAG,uCAAc,CAAC,MAAM,CAAC;gBAC7C,GAAG,UAAU;gBACb,MAAM,EAAE,iCAAY,CAAC,OAAO;gBAC5B,WAAW,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,kBAAkB;aAC/D,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,MAAM,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAE5B,MAAM,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC;gBACnC,GAAG,UAAU;gBACb,wBAAwB,EAAE,EAAE;gBAC5B,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC,CAAC;YACH,MAAM,cAAc,GAAG,MAAM,CAAC,0BAA0B,EAAE,CAAC;YAE3D,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;YACrC,MAAM,CAAC,cAAe,CAAC,OAAO,EAAE,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,eAAe,GAAG,uCAAc,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,iCAAY,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;YACzH,MAAM,cAAc,GAAG,eAAe,CAAC,0BAA0B,EAAE,CAAC;YAEpE,MAAM,CAAC,cAAc,CAAC,CAAC,QAAQ,EAAE,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAE7B,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,aAAa,GAAG,UAAU,CAAC;YAEjC,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAEvC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,OAAO,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,eAAe,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAElD,MAAM,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC;gBACnC,GAAG,UAAU;gBACb,cAAc,EAAE,OAAO;gBACvB,eAAe,EAAE,QAAQ;gBACzB,sBAAsB,EAAE,eAAe;aACxC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,CAAC,sBAAsB,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\response-action.entity.spec.ts"], "sourcesContent": ["import { ResponseAction, ResponseActionProps } from '../response-action.entity';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { ActionType } from '../../enums/action-type.enum';\r\nimport { ActionStatus } from '../../enums/action-status.enum';\r\nimport { ResponseActionCreatedDomainEvent } from '../../events/response-action-created.domain-event';\r\nimport { ResponseActionStatusChangedDomainEvent } from '../../events/response-action-status-changed.domain-event';\r\nimport { ResponseActionExecutedDomainEvent } from '../../events/response-action-executed.domain-event';\r\nimport { ResponseActionFailedDomainEvent } from '../../events/response-action-failed.domain-event';\r\nimport { ResponseActionRolledBackDomainEvent } from '../../events/response-action-rolled-back.domain-event';\r\n\r\ndescribe('ResponseAction Entity', () => {\r\n  let validProps: ResponseActionProps;\r\n\r\n  beforeEach(() => {\r\n    validProps = {\r\n      actionType: ActionType.BLOCK_IP,\r\n      status: ActionStatus.PENDING,\r\n      title: 'Block malicious IP address',\r\n      description: 'Block IP address identified as malicious',\r\n      parameters: { ipAddress: '*************' },\r\n      target: {\r\n        type: 'system',\r\n        id: 'firewall-001',\r\n        name: 'Main Firewall',\r\n      },\r\n      priority: 'high',\r\n      isAutomated: true,\r\n      isReversible: true,\r\n      estimatedDurationMinutes: 5,\r\n      requiredPermissions: ['network.firewall.write'],\r\n      approvalRequired: false,\r\n      successCriteria: ['IP address blocked in firewall', 'Traffic from IP stopped'],\r\n      retryCount: 0,\r\n      maxRetries: 3,\r\n      retryDelayMinutes: 5,\r\n      rollbackInfo: {\r\n        canRollback: true,\r\n        rollbackSteps: ['Remove IP from block list', 'Update firewall rules'],\r\n      },\r\n      rolledBack: false,\r\n      tags: ['security', 'containment'],\r\n      metadata: { source: 'threat-detection' },\r\n      childActionIds: [],\r\n      timeoutMinutes: 30,\r\n      timedOut: false,\r\n    };\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create a valid ResponseAction', () => {\r\n      const action = ResponseAction.create(validProps);\r\n\r\n      expect(action).toBeInstanceOf(ResponseAction);\r\n      expect(action.actionType).toBe(ActionType.BLOCK_IP);\r\n      expect(action.status).toBe(ActionStatus.PENDING);\r\n      expect(action.title).toBe('Block malicious IP address');\r\n      expect(action.priority).toBe('high');\r\n      expect(action.isAutomated).toBe(true);\r\n      expect(action.isReversible).toBe(true);\r\n    });\r\n\r\n    it('should generate ResponseActionCreatedDomainEvent on creation', () => {\r\n      const action = ResponseAction.create(validProps);\r\n      const events = action.getUnpublishedEvents();\r\n\r\n      expect(events).toHaveLength(1);\r\n      expect(events[0]).toBeInstanceOf(ResponseActionCreatedDomainEvent);\r\n      \r\n      const createdEvent = events[0] as ResponseActionCreatedDomainEvent;\r\n      expect(createdEvent.actionType).toBe(ActionType.BLOCK_IP);\r\n      expect(createdEvent.title).toBe('Block malicious IP address');\r\n      expect(createdEvent.priority).toBe('high');\r\n    });\r\n\r\n    it('should create with custom ID', () => {\r\n      const customId = UniqueEntityId.generate();\r\n      const action = ResponseAction.create(validProps, customId);\r\n\r\n      expect(action.id.equals(customId)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should throw error if actionType is missing', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).actionType;\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must have an action type');\r\n    });\r\n\r\n    it('should throw error if status is missing', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).status;\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must have a status');\r\n    });\r\n\r\n    it('should throw error if title is empty', () => {\r\n      const invalidProps = { ...validProps, title: '' };\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must have a non-empty title');\r\n    });\r\n\r\n    it('should throw error if title is too long', () => {\r\n      const invalidProps = { ...validProps, title: 'a'.repeat(201) };\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction title cannot exceed 200 characters');\r\n    });\r\n\r\n    it('should throw error if description is too long', () => {\r\n      const invalidProps = { ...validProps, description: 'a'.repeat(2001) };\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction description cannot exceed 2000 characters');\r\n    });\r\n\r\n    it('should throw error if parameters is missing', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).parameters;\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must have parameters');\r\n    });\r\n\r\n    it('should throw error if priority is missing', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).priority;\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must have a priority');\r\n    });\r\n\r\n    it('should throw error if isAutomated is undefined', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).isAutomated;\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must specify if it is automated');\r\n    });\r\n\r\n    it('should throw error if isReversible is undefined', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).isReversible;\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must specify if it is reversible');\r\n    });\r\n\r\n    it('should throw error if requiredPermissions is not an array', () => {\r\n      const invalidProps = { ...validProps, requiredPermissions: 'not-an-array' as any };\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must have required permissions array');\r\n    });\r\n\r\n    it('should throw error if approvalRequired is undefined', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).approvalRequired;\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must specify if approval is required');\r\n    });\r\n\r\n    it('should throw error if successCriteria is not an array', () => {\r\n      const invalidProps = { ...validProps, successCriteria: 'not-an-array' as any };\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must have success criteria array');\r\n    });\r\n\r\n    it('should throw error if retryCount is negative', () => {\r\n      const invalidProps = { ...validProps, retryCount: -1 };\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('Retry count cannot be negative');\r\n    });\r\n\r\n    it('should throw error if maxRetries is negative', () => {\r\n      const invalidProps = { ...validProps, maxRetries: -1 };\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('Max retries cannot be negative');\r\n    });\r\n\r\n    it('should throw error if retryDelayMinutes is negative', () => {\r\n      const invalidProps = { ...validProps, retryDelayMinutes: -1 };\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('Retry delay cannot be negative');\r\n    });\r\n\r\n    it('should throw error if tags is not an array', () => {\r\n      const invalidProps = { ...validProps, tags: 'not-an-array' as any };\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must have tags array');\r\n    });\r\n\r\n    it('should throw error if too many tags', () => {\r\n      const invalidProps = { ...validProps, tags: Array(21).fill('tag') };\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction cannot have more than 20 tags');\r\n    });\r\n\r\n    it('should throw error if metadata is missing', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).metadata;\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must have metadata object');\r\n    });\r\n\r\n    it('should throw error if childActionIds is not an array', () => {\r\n      const invalidProps = { ...validProps, childActionIds: 'not-an-array' as any };\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must have child action IDs array');\r\n    });\r\n\r\n    it('should throw error if timeoutMinutes is not positive', () => {\r\n      const invalidProps = { ...validProps, timeoutMinutes: 0 };\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('Timeout minutes must be positive');\r\n    });\r\n\r\n    it('should throw error if timedOut is undefined', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).timedOut;\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must specify if it timed out');\r\n    });\r\n\r\n    it('should throw error if rolledBack is undefined', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).rolledBack;\r\n\r\n      expect(() => ResponseAction.create(invalidProps)).toThrow('ResponseAction must specify if it was rolled back');\r\n    });\r\n  });\r\n\r\n  describe('status changes', () => {\r\n    let action: ResponseAction;\r\n\r\n    beforeEach(() => {\r\n      action = ResponseAction.create(validProps);\r\n      action.clearEvents(); // Clear creation event\r\n    });\r\n\r\n    it('should change status successfully', () => {\r\n      action.changeStatus(ActionStatus.APPROVED, 'admin', 'Approved for execution');\r\n\r\n      expect(action.status).toBe(ActionStatus.APPROVED);\r\n      expect(action.approvedBy).toBe('admin');\r\n      expect(action.approvedAt).toBeDefined();\r\n\r\n      const events = action.getUnpublishedEvents();\r\n      expect(events).toHaveLength(1);\r\n      expect(events[0]).toBeInstanceOf(ResponseActionStatusChangedDomainEvent);\r\n    });\r\n\r\n    it('should not change status if same status', () => {\r\n      action.changeStatus(ActionStatus.PENDING);\r\n\r\n      const events = action.getUnpublishedEvents();\r\n      expect(events).toHaveLength(0);\r\n    });\r\n\r\n    it('should throw error for invalid status transition', () => {\r\n      expect(() => action.changeStatus(ActionStatus.COMPLETED)).toThrow('Invalid status transition');\r\n    });\r\n\r\n    it('should handle timeout status change', () => {\r\n      action.changeStatus(ActionStatus.APPROVED);\r\n      action.changeStatus(ActionStatus.QUEUED);\r\n      action.changeStatus(ActionStatus.EXECUTING);\r\n      action.changeStatus(ActionStatus.TIMEOUT);\r\n\r\n      expect(action.status).toBe(ActionStatus.TIMEOUT);\r\n      expect(action.timedOut).toBe(true);\r\n      expect(action.timedOutAt).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('execution', () => {\r\n    let action: ResponseAction;\r\n\r\n    beforeEach(() => {\r\n      action = ResponseAction.create({ ...validProps, status: ActionStatus.EXECUTING });\r\n      action.clearEvents();\r\n    });\r\n\r\n    it('should execute successfully', () => {\r\n      const executionResults = { blocked: true, ruleId: 'rule-123' };\r\n      \r\n      action.execute('system', executionResults);\r\n\r\n      expect(action.status).toBe(ActionStatus.COMPLETED);\r\n      expect(action.executedBy).toBe('system');\r\n      expect(action.executedAt).toBeDefined();\r\n      expect(action.executionResults).toEqual(executionResults);\r\n      expect(action.successCriteriaMet).toBe(true);\r\n\r\n      const events = action.getUnpublishedEvents();\r\n      expect(events).toHaveLength(1);\r\n      expect(events[0]).toBeInstanceOf(ResponseActionExecutedDomainEvent);\r\n    });\r\n\r\n    it('should throw error if not in executing status', () => {\r\n      const pendingAction = ResponseAction.create(validProps);\r\n      \r\n      expect(() => pendingAction.execute('system')).toThrow('Action must be in executing status to be executed');\r\n    });\r\n  });\r\n\r\n  describe('failure handling', () => {\r\n    let action: ResponseAction;\r\n\r\n    beforeEach(() => {\r\n      action = ResponseAction.create({ ...validProps, status: ActionStatus.EXECUTING });\r\n      action.clearEvents();\r\n    });\r\n\r\n    it('should handle failure', () => {\r\n      const error = 'Network timeout';\r\n      \r\n      action.fail(error, 'system');\r\n\r\n      expect(action.status).toBe(ActionStatus.FAILED);\r\n      expect(action.executionError).toBe(error);\r\n      expect(action.successCriteriaMet).toBe(false);\r\n\r\n      const events = action.getUnpublishedEvents();\r\n      expect(events).toHaveLength(1);\r\n      expect(events[0]).toBeInstanceOf(ResponseActionFailedDomainEvent);\r\n    });\r\n\r\n    it('should throw error if not in executing or retrying status', () => {\r\n      const pendingAction = ResponseAction.create(validProps);\r\n      \r\n      expect(() => pendingAction.fail('error')).toThrow('Action must be in executing or retrying status to fail');\r\n    });\r\n  });\r\n\r\n  describe('retry logic', () => {\r\n    let action: ResponseAction;\r\n\r\n    beforeEach(() => {\r\n      action = ResponseAction.create({ ...validProps, status: ActionStatus.FAILED, maxRetries: 3, executedAt: new Date() });\r\n      action.clearEvents();\r\n    });\r\n\r\n    it('should retry successfully', () => {\r\n      // Set executedAt for failed action\r\n      const failedAction = ResponseAction.create({ \r\n        ...validProps, \r\n        status: ActionStatus.FAILED, \r\n        maxRetries: 3,\r\n        executedAt: new Date()\r\n      });\r\n      failedAction.clearEvents();\r\n      \r\n      failedAction.retry('admin');\r\n\r\n      expect(failedAction.status).toBe(ActionStatus.RETRYING);\r\n      expect(failedAction.retryCount).toBe(1);\r\n      expect(failedAction.executionError).toBeUndefined();\r\n      expect(failedAction.nextRetryAt).toBeDefined();\r\n\r\n      const events = failedAction.getUnpublishedEvents();\r\n      expect(events).toHaveLength(1);\r\n      expect(events[0]).toBeInstanceOf(ResponseActionStatusChangedDomainEvent);\r\n    });\r\n\r\n    it('should not retry if max retries exceeded', () => {\r\n      const maxRetriesAction = ResponseAction.create({ \r\n        ...validProps, \r\n        status: ActionStatus.FAILED, \r\n        retryCount: 3, \r\n        maxRetries: 3,\r\n        executedAt: new Date()\r\n      });\r\n\r\n      expect(() => maxRetriesAction.retry('admin')).toThrow('Action cannot be retried');\r\n    });\r\n\r\n    it('should check canRetry correctly', () => {\r\n      const failedAction = ResponseAction.create({ \r\n        ...validProps, \r\n        status: ActionStatus.FAILED, \r\n        maxRetries: 3,\r\n        executedAt: new Date()\r\n      });\r\n      expect(failedAction.canRetry()).toBe(true);\r\n\r\n      const maxRetriesAction = ResponseAction.create({ \r\n        ...validProps, \r\n        status: ActionStatus.FAILED, \r\n        retryCount: 3, \r\n        maxRetries: 3,\r\n        executedAt: new Date()\r\n      });\r\n      expect(maxRetriesAction.canRetry()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('rollback functionality', () => {\r\n    let action: ResponseAction;\r\n\r\n    beforeEach(() => {\r\n      action = ResponseAction.create({ \r\n        ...validProps, \r\n        status: ActionStatus.COMPLETED,\r\n        isReversible: true,\r\n        rollbackInfo: {\r\n          canRollback: true,\r\n          rollbackSteps: ['Remove IP from block list'],\r\n        },\r\n        executedAt: new Date()\r\n      });\r\n      action.clearEvents();\r\n    });\r\n\r\n    it('should rollback successfully', () => {\r\n      // Create a completed action with executedAt\r\n      const completedAction = ResponseAction.create({ \r\n        ...validProps, \r\n        status: ActionStatus.COMPLETED,\r\n        isReversible: true,\r\n        rollbackInfo: {\r\n          canRollback: true,\r\n          rollbackSteps: ['Remove IP from block list'],\r\n        },\r\n        executedAt: new Date()\r\n      });\r\n      completedAction.clearEvents();\r\n      \r\n      const rollbackResults = { unblocked: true };\r\n      \r\n      completedAction.rollback('admin', rollbackResults);\r\n\r\n      expect(completedAction.rolledBack).toBe(true);\r\n      expect(completedAction.rollbackDetails).toBeDefined();\r\n      expect(completedAction.rollbackDetails?.rolledBackBy).toBe('admin');\r\n      expect(completedAction.rollbackDetails?.rollbackResults).toEqual(rollbackResults);\r\n\r\n      const events = completedAction.getUnpublishedEvents();\r\n      expect(events).toHaveLength(1);\r\n      expect(events[0]).toBeInstanceOf(ResponseActionRolledBackDomainEvent);\r\n    });\r\n\r\n    it('should not rollback if not reversible', () => {\r\n      const nonReversibleAction = ResponseAction.create({ \r\n        ...validProps, \r\n        status: ActionStatus.COMPLETED,\r\n        isReversible: false,\r\n        executedAt: new Date()\r\n      });\r\n\r\n      expect(() => nonReversibleAction.rollback('admin')).toThrow('Action cannot be rolled back');\r\n    });\r\n\r\n    it('should check canRollback correctly', () => {\r\n      const completedAction = ResponseAction.create({ \r\n        ...validProps, \r\n        status: ActionStatus.COMPLETED,\r\n        isReversible: true,\r\n        rollbackInfo: {\r\n          canRollback: true,\r\n          rollbackSteps: ['Remove IP from block list'],\r\n        },\r\n        executedAt: new Date()\r\n      });\r\n      expect(completedAction.canRollback()).toBe(true);\r\n\r\n      const nonReversibleAction = ResponseAction.create({ \r\n        ...validProps, \r\n        status: ActionStatus.COMPLETED,\r\n        isReversible: false,\r\n        executedAt: new Date()\r\n      });\r\n      expect(nonReversibleAction.canRollback()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('child action management', () => {\r\n    let action: ResponseAction;\r\n\r\n    beforeEach(() => {\r\n      action = ResponseAction.create(validProps);\r\n    });\r\n\r\n    it('should add child action', () => {\r\n      const childId = UniqueEntityId.generate();\r\n      \r\n      action.addChildAction(childId);\r\n\r\n      expect(action.childActionIds).toContain(childId);\r\n    });\r\n\r\n    it('should not add duplicate child action', () => {\r\n      const childId = UniqueEntityId.generate();\r\n      \r\n      action.addChildAction(childId);\r\n      action.addChildAction(childId);\r\n\r\n      expect(action.childActionIds.filter(id => id.equals(childId))).toHaveLength(1);\r\n    });\r\n\r\n    it('should remove child action', () => {\r\n      const childId = UniqueEntityId.generate();\r\n      \r\n      action.addChildAction(childId);\r\n      action.removeChildAction(childId);\r\n\r\n      expect(action.childActionIds).not.toContain(childId);\r\n    });\r\n  });\r\n\r\n  describe('tag management', () => {\r\n    let action: ResponseAction;\r\n\r\n    beforeEach(() => {\r\n      action = ResponseAction.create(validProps);\r\n    });\r\n\r\n    it('should add tags', () => {\r\n      action.addTags(['urgent', 'critical']);\r\n\r\n      expect(action.tags).toContain('urgent');\r\n      expect(action.tags).toContain('critical');\r\n    });\r\n\r\n    it('should not add duplicate tags', () => {\r\n      action.addTags(['security', 'urgent']);\r\n\r\n      const securityTags = action.tags.filter(tag => tag === 'security');\r\n      expect(securityTags).toHaveLength(1);\r\n    });\r\n\r\n    it('should throw error if too many tags', () => {\r\n      const manyTags = Array(19).fill('tag').map((_, i) => `tag${i}`);\r\n      \r\n      expect(() => action.addTags(manyTags)).toThrow('ResponseAction cannot have more than 20 tags');\r\n    });\r\n\r\n    it('should remove tags', () => {\r\n      action.removeTags(['security']);\r\n\r\n      expect(action.tags).not.toContain('security');\r\n    });\r\n\r\n    it('should check tag presence', () => {\r\n      expect(action.hasTag('security')).toBe(true);\r\n      expect(action.hasTag('nonexistent')).toBe(false);\r\n    });\r\n\r\n    it('should check any tag presence', () => {\r\n      expect(action.hasAnyTag(['security', 'nonexistent'])).toBe(true);\r\n      expect(action.hasAnyTag(['nonexistent', 'another'])).toBe(false);\r\n    });\r\n\r\n    it('should check all tags presence', () => {\r\n      expect(action.hasAllTags(['security', 'containment'])).toBe(true);\r\n      expect(action.hasAllTags(['security', 'nonexistent'])).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('metadata management', () => {\r\n    let action: ResponseAction;\r\n\r\n    beforeEach(() => {\r\n      action = ResponseAction.create(validProps);\r\n    });\r\n\r\n    it('should update metadata', () => {\r\n      action.updateMetadata({ newKey: 'newValue' });\r\n\r\n      expect(action.metadata.newKey).toBe('newValue');\r\n      expect(action.metadata.source).toBe('threat-detection'); // Original should remain\r\n    });\r\n\r\n    it('should remove metadata key', () => {\r\n      action.removeMetadata('source');\r\n\r\n      expect(action.metadata.source).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('query methods', () => {\r\n    it('should check if action is terminal', () => {\r\n      const completedAction = ResponseAction.create({ ...validProps, status: ActionStatus.COMPLETED, executedAt: new Date() });\r\n      const pendingAction = ResponseAction.create({ ...validProps, status: ActionStatus.PENDING });\r\n\r\n      expect(completedAction.isTerminal()).toBe(true);\r\n      expect(pendingAction.isTerminal()).toBe(false);\r\n    });\r\n\r\n    it('should check if action is active', () => {\r\n      const completedAction = ResponseAction.create({ ...validProps, status: ActionStatus.COMPLETED, executedAt: new Date() });\r\n      const pendingAction = ResponseAction.create({ ...validProps, status: ActionStatus.PENDING });\r\n\r\n      expect(completedAction.isActive()).toBe(false);\r\n      expect(pendingAction.isActive()).toBe(true);\r\n    });\r\n\r\n    it('should check if action is successful', () => {\r\n      const completedAction = ResponseAction.create({ ...validProps, status: ActionStatus.COMPLETED, executedAt: new Date() });\r\n      const failedAction = ResponseAction.create({ ...validProps, status: ActionStatus.FAILED, executedAt: new Date() });\r\n\r\n      expect(completedAction.isSuccessful()).toBe(true);\r\n      expect(failedAction.isSuccessful()).toBe(false);\r\n    });\r\n\r\n    it('should check if action has failed', () => {\r\n      const failedAction = ResponseAction.create({ ...validProps, status: ActionStatus.FAILED, executedAt: new Date() });\r\n      const completedAction = ResponseAction.create({ ...validProps, status: ActionStatus.COMPLETED, executedAt: new Date() });\r\n\r\n      expect(failedAction.hasFailed()).toBe(true);\r\n      expect(completedAction.hasFailed()).toBe(false);\r\n    });\r\n\r\n    it('should check if action requires approval', () => {\r\n      const approvalAction = ResponseAction.create({ \r\n        ...validProps, \r\n        status: ActionStatus.PENDING, \r\n        approvalRequired: true \r\n      });\r\n      const noApprovalAction = ResponseAction.create({ \r\n        ...validProps, \r\n        status: ActionStatus.PENDING, \r\n        approvalRequired: false \r\n      });\r\n\r\n      expect(approvalAction.requiresApproval()).toBe(true);\r\n      expect(noApprovalAction.requiresApproval()).toBe(false);\r\n    });\r\n\r\n    it('should check if action is high priority', () => {\r\n      const highPriorityAction = ResponseAction.create({ ...validProps, priority: 'high' });\r\n      const normalPriorityAction = ResponseAction.create({ ...validProps, priority: 'normal' });\r\n\r\n      expect(highPriorityAction.isHighPriority()).toBe(true);\r\n      expect(normalPriorityAction.isHighPriority()).toBe(false);\r\n    });\r\n\r\n    it('should check if action is overdue', () => {\r\n      const overdueAction = ResponseAction.create({ \r\n        ...validProps, \r\n        status: ActionStatus.PENDING,\r\n        scheduledAt: new Date(Date.now() - 3600000) // 1 hour ago\r\n      });\r\n      const notOverdueAction = ResponseAction.create({ \r\n        ...validProps, \r\n        status: ActionStatus.PENDING,\r\n        scheduledAt: new Date(Date.now() + 3600000) // 1 hour from now\r\n      });\r\n\r\n      expect(overdueAction.isOverdue()).toBe(true);\r\n      expect(notOverdueAction.isOverdue()).toBe(false);\r\n    });\r\n\r\n    it('should get action age', () => {\r\n      const action = ResponseAction.create(validProps);\r\n      const age = action.getAge();\r\n\r\n      expect(age).toBeGreaterThanOrEqual(0);\r\n      expect(age).toBeLessThan(1000); // Should be very recent\r\n    });\r\n\r\n    it('should get estimated completion time', () => {\r\n      const action = ResponseAction.create({ \r\n        ...validProps, \r\n        estimatedDurationMinutes: 30,\r\n        scheduledAt: new Date()\r\n      });\r\n      const completionTime = action.getEstimatedCompletionTime();\r\n\r\n      expect(completionTime).toBeDefined();\r\n      expect(completionTime!.getTime()).toBeGreaterThan(Date.now());\r\n    });\r\n\r\n    it('should return null completion time for terminal actions', () => {\r\n      const completedAction = ResponseAction.create({ ...validProps, status: ActionStatus.COMPLETED, executedAt: new Date() });\r\n      const completionTime = completedAction.getEstimatedCompletionTime();\r\n\r\n      expect(completionTime).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('JSON serialization', () => {\r\n    it('should serialize to JSON correctly', () => {\r\n      const action = ResponseAction.create(validProps);\r\n      const json = action.toJSON();\r\n\r\n      expect(json.id).toBeDefined();\r\n      expect(json.actionType).toBe(ActionType.BLOCK_IP);\r\n      expect(json.status).toBe(ActionStatus.PENDING);\r\n      expect(json.title).toBe('Block malicious IP address');\r\n      expect(json.priority).toBe('high');\r\n      expect(json.isAutomated).toBe(true);\r\n      expect(json.isReversible).toBe(true);\r\n      expect(json.summary).toBeDefined();\r\n      expect(json.summary.isActive).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('correlation and relationships', () => {\r\n    it('should set correlation ID', () => {\r\n      const action = ResponseAction.create(validProps);\r\n      const correlationId = 'corr-123';\r\n\r\n      action.setCorrelationId(correlationId);\r\n\r\n      expect(action.correlationId).toBe(correlationId);\r\n    });\r\n\r\n    it('should handle related entities', () => {\r\n      const eventId = UniqueEntityId.generate();\r\n      const threatId = UniqueEntityId.generate();\r\n      const vulnerabilityId = UniqueEntityId.generate();\r\n\r\n      const action = ResponseAction.create({\r\n        ...validProps,\r\n        relatedEventId: eventId,\r\n        relatedThreatId: threatId,\r\n        relatedVulnerabilityId: vulnerabilityId,\r\n      });\r\n\r\n      expect(action.relatedEventId?.equals(eventId)).toBe(true);\r\n      expect(action.relatedThreatId?.equals(threatId)).toBe(true);\r\n      expect(action.relatedVulnerabilityId?.equals(vulnerabilityId)).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}