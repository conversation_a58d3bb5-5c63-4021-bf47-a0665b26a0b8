{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\correlated-event-created.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAE5E,sEAA6D;AAC7D,8EAAqE;AACrE,0EAAiE;AA8BjE;;;;;;;;;;;;GAYG;AACH,MAAa,iCAAkC,SAAQ,+BAAgD;IACrG,YACE,WAA2B,EAC3B,SAA0C,EAC1C,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,CAAC,mCAAa,CAAC,IAAI,EAAE,mCAAa,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,iBAAiB,KAAK,2CAAiB,CAAC,SAAS,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,OAAO,CAAC,uCAAe,CAAC,IAAI,EAAE,uCAAe,CAAC,SAAS,EAAE,uCAAe,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACrH,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,wBAAwB,EAAE,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACtG,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YAC1D,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACjD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,yBAAyB,EAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACrE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YACjD,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACvD,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAE,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC3C,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC3C,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;YACpC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC3C,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YACjD,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YACjD,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,2BAA2B,EAAE,IAAI,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;YAC1E,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,yBAAyB;QAMvB,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO;gBACL,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,YAAY;gBAC7B,cAAc,EAAE,EAAE;gBAClB,MAAM,EAAE,gDAAgD;aACzD,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC7C,OAAO;gBACL,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,OAAO;gBACxB,cAAc,EAAE,EAAE;gBAClB,MAAM,EAAE,sDAAsD;aAC/D,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAE,CAAC;YAChE,OAAO;gBACL,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,OAAO;gBACxB,cAAc,EAAE,EAAE;gBAClB,MAAM,EAAE,sDAAsD;aAC/D,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,yBAAyB,EAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACrE,OAAO;gBACL,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,OAAO;gBACxB,cAAc,EAAE,GAAG;gBACnB,MAAM,EAAE,iDAAiD;aAC1D,CAAC;QACJ,CAAC;QAED,OAAO;YACL,cAAc,EAAE,KAAK;YACrB,eAAe,EAAE,OAAO;YACxB,cAAc,EAAE,GAAG;YACnB,MAAM,EAAE,iCAAiC;SAC1C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,eAAe;QA0Bb,OAAO;YACL,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC9C,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;YAChD,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB;YACrD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB;YACrD,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YACrC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,yBAAyB,EAAE,IAAI,CAAC,yBAAyB,EAAE;YAC3D,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACrD,2BAA2B,EAAE,IAAI,CAAC,2BAA2B,EAAE;YAC/D,wBAAwB,EAAE,IAAI,CAAC,wBAAwB,EAAE;YACzD,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACnD,yBAAyB,EAAE,IAAI,CAAC,yBAAyB,EAAE;YAC3D,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;YAChC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YAChD,sBAAsB,EAAE,IAAI,CAAC,yBAAyB,EAAE;SACzD,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;SACrC,CAAC;IACJ,CAAC;CACF;AAzVD,8EAyVC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\correlated-event-created.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { CorrelationStatus } from '../enums/correlation-status.enum';\r\nimport { ConfidenceLevel } from '../enums/confidence-level.enum';\r\n\r\n/**\r\n * Correlated Event Created Domain Event Data\r\n */\r\nexport interface CorrelatedEventCreatedEventData {\r\n  /** Enriched event ID that was correlated */\r\n  enrichedEventId: UniqueEntityId;\r\n  /** Type of the correlated event */\r\n  eventType: EventType;\r\n  /** Severity of the correlated event */\r\n  severity: EventSeverity;\r\n  /** Current correlation status */\r\n  correlationStatus: CorrelationStatus;\r\n  /** Correlation quality score */\r\n  correlationQualityScore?: number;\r\n  /** Number of applied correlation rules */\r\n  appliedRulesCount: number;\r\n  /** Number of correlation matches found */\r\n  correlationMatchesCount: number;\r\n  /** Number of related events */\r\n  relatedEventsCount: number;\r\n  /** Confidence level of correlation */\r\n  confidenceLevel: ConfidenceLevel;\r\n  /** Whether the event has an attack chain */\r\n  hasAttackChain: boolean;\r\n  /** Whether the event requires manual review */\r\n  requiresManualReview: boolean;\r\n}\r\n\r\n/**\r\n * Correlated Event Created Domain Event\r\n * \r\n * Raised when a new correlated security event is created in the system.\r\n * This event triggers various downstream processes including:\r\n * - Threat analysis workflows\r\n * - Attack chain analysis\r\n * - Response action planning\r\n * - Manual review queue management\r\n * - Metrics collection and monitoring\r\n * - Audit logging and compliance tracking\r\n * - Alert generation for high-confidence correlations\r\n */\r\nexport class CorrelatedEventCreatedDomainEvent extends BaseDomainEvent<CorrelatedEventCreatedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: CorrelatedEventCreatedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the enriched event ID\r\n   */\r\n  get enrichedEventId(): UniqueEntityId {\r\n    return this.eventData.enrichedEventId;\r\n  }\r\n\r\n  /**\r\n   * Get the type of the correlated event\r\n   */\r\n  get eventType(): EventType {\r\n    return this.eventData.eventType;\r\n  }\r\n\r\n  /**\r\n   * Get the severity of the correlated event\r\n   */\r\n  get severity(): EventSeverity {\r\n    return this.eventData.severity;\r\n  }\r\n\r\n  /**\r\n   * Get the correlation status\r\n   */\r\n  get correlationStatus(): CorrelationStatus {\r\n    return this.eventData.correlationStatus;\r\n  }\r\n\r\n  /**\r\n   * Get the correlation quality score\r\n   */\r\n  get correlationQualityScore(): number | undefined {\r\n    return this.eventData.correlationQualityScore;\r\n  }\r\n\r\n  /**\r\n   * Get the number of applied rules\r\n   */\r\n  get appliedRulesCount(): number {\r\n    return this.eventData.appliedRulesCount;\r\n  }\r\n\r\n  /**\r\n   * Get the number of correlation matches\r\n   */\r\n  get correlationMatchesCount(): number {\r\n    return this.eventData.correlationMatchesCount;\r\n  }\r\n\r\n  /**\r\n   * Get the number of related events\r\n   */\r\n  get relatedEventsCount(): number {\r\n    return this.eventData.relatedEventsCount;\r\n  }\r\n\r\n  /**\r\n   * Get the confidence level\r\n   */\r\n  get confidenceLevel(): ConfidenceLevel {\r\n    return this.eventData.confidenceLevel;\r\n  }\r\n\r\n  /**\r\n   * Check if the event has an attack chain\r\n   */\r\n  get hasAttackChain(): boolean {\r\n    return this.eventData.hasAttackChain;\r\n  }\r\n\r\n  /**\r\n   * Check if the event requires manual review\r\n   */\r\n  get requiresManualReview(): boolean {\r\n    return this.eventData.requiresManualReview;\r\n  }\r\n\r\n  /**\r\n   * Check if the correlated event is high severity\r\n   */\r\n  isHighSeverity(): boolean {\r\n    return [EventSeverity.HIGH, EventSeverity.CRITICAL].includes(this.severity);\r\n  }\r\n\r\n  /**\r\n   * Check if the correlated event is critical\r\n   */\r\n  isCritical(): boolean {\r\n    return this.severity === EventSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if the event has high correlation quality\r\n   */\r\n  hasHighCorrelationQuality(): boolean {\r\n    return (this.correlationQualityScore || 0) >= 70;\r\n  }\r\n\r\n  /**\r\n   * Check if correlation is completed\r\n   */\r\n  isCorrelationCompleted(): boolean {\r\n    return this.correlationStatus === CorrelationStatus.COMPLETED;\r\n  }\r\n\r\n  /**\r\n   * Check if the event has high confidence correlation\r\n   */\r\n  isHighConfidenceCorrelation(): boolean {\r\n    return [ConfidenceLevel.HIGH, ConfidenceLevel.VERY_HIGH, ConfidenceLevel.CONFIRMED].includes(this.confidenceLevel);\r\n  }\r\n\r\n  /**\r\n   * Check if the event has multiple related events\r\n   */\r\n  hasMultipleRelatedEvents(): boolean {\r\n    return this.relatedEventsCount > 1;\r\n  }\r\n\r\n  /**\r\n   * Check if the event has correlation matches\r\n   */\r\n  hasCorrelationMatches(): boolean {\r\n    return this.correlationMatchesCount > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if the event is part of a potential attack campaign\r\n   */\r\n  isPotentialAttackCampaign(): boolean {\r\n    return this.hasAttackChain && this.hasMultipleRelatedEvents() && this.isHighConfidenceCorrelation();\r\n  }\r\n\r\n  /**\r\n   * Get event priority for processing\r\n   */\r\n  getEventPriority(): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (this.isCritical() && this.isPotentialAttackCampaign()) {\r\n      return 'critical';\r\n    }\r\n    \r\n    if (this.isHighSeverity() && this.hasAttackChain) {\r\n      return 'high';\r\n    }\r\n    \r\n    if (this.hasHighCorrelationQuality() && this.hasCorrelationMatches()) {\r\n      return 'medium';\r\n    }\r\n    \r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get recommended actions based on event characteristics\r\n   */\r\n  getRecommendedActions(): string[] {\r\n    const actions: string[] = [];\r\n\r\n    if (this.isPotentialAttackCampaign()) {\r\n      actions.push('Initiate incident response procedures');\r\n      actions.push('Notify security operations center');\r\n      actions.push('Activate threat hunting team');\r\n    }\r\n\r\n    if (this.hasAttackChain) {\r\n      actions.push('Analyze attack chain progression');\r\n      actions.push('Identify attack vectors and techniques');\r\n      actions.push('Assess potential impact and scope');\r\n    }\r\n\r\n    if (this.isHighConfidenceCorrelation()) {\r\n      actions.push('Validate correlation findings');\r\n      actions.push('Gather additional evidence');\r\n      actions.push('Prepare containment measures');\r\n    }\r\n\r\n    if (this.requiresManualReview) {\r\n      actions.push('Schedule manual review');\r\n      actions.push('Assign to security analyst');\r\n      actions.push('Prioritize based on severity and confidence');\r\n    }\r\n\r\n    if (this.hasMultipleRelatedEvents()) {\r\n      actions.push('Investigate related events');\r\n      actions.push('Look for additional correlations');\r\n      actions.push('Map event relationships');\r\n    }\r\n\r\n    return actions;\r\n  }\r\n\r\n  /**\r\n   * Get alert level based on event characteristics\r\n   */\r\n  getAlertLevel(): 'info' | 'warning' | 'alert' | 'critical' {\r\n    if (this.isPotentialAttackCampaign()) {\r\n      return 'critical';\r\n    }\r\n    \r\n    if (this.hasAttackChain && this.isHighSeverity()) {\r\n      return 'alert';\r\n    }\r\n    \r\n    if (this.isHighConfidenceCorrelation() || this.hasMultipleRelatedEvents()) {\r\n      return 'warning';\r\n    }\r\n    \r\n    return 'info';\r\n  }\r\n\r\n  /**\r\n   * Get escalation requirements\r\n   */\r\n  getEscalationRequirements(): {\r\n    shouldEscalate: boolean;\r\n    escalationLevel: 'tier1' | 'tier2' | 'tier3' | 'management';\r\n    timeoutMinutes: number;\r\n    reason: string;\r\n  } {\r\n    if (this.isPotentialAttackCampaign()) {\r\n      return {\r\n        shouldEscalate: true,\r\n        escalationLevel: 'management',\r\n        timeoutMinutes: 15,\r\n        reason: 'Potential coordinated attack campaign detected',\r\n      };\r\n    }\r\n\r\n    if (this.isCritical() && this.hasAttackChain) {\r\n      return {\r\n        shouldEscalate: true,\r\n        escalationLevel: 'tier3',\r\n        timeoutMinutes: 30,\r\n        reason: 'Critical severity event with attack chain identified',\r\n      };\r\n    }\r\n\r\n    if (this.isHighSeverity() && this.isHighConfidenceCorrelation()) {\r\n      return {\r\n        shouldEscalate: true,\r\n        escalationLevel: 'tier2',\r\n        timeoutMinutes: 60,\r\n        reason: 'High severity event with high confidence correlation',\r\n      };\r\n    }\r\n\r\n    if (this.hasHighCorrelationQuality() && this.hasCorrelationMatches()) {\r\n      return {\r\n        shouldEscalate: true,\r\n        escalationLevel: 'tier1',\r\n        timeoutMinutes: 120,\r\n        reason: 'Quality correlation with multiple matches found',\r\n      };\r\n    }\r\n\r\n    return {\r\n      shouldEscalate: false,\r\n      escalationLevel: 'tier1',\r\n      timeoutMinutes: 240,\r\n      reason: 'Standard correlation processing',\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get event summary for handlers\r\n   */\r\n  getEventSummary(): {\r\n    correlatedEventId: string;\r\n    enrichedEventId: string;\r\n    eventType: EventType;\r\n    severity: EventSeverity;\r\n    correlationStatus: CorrelationStatus;\r\n    correlationQualityScore?: number;\r\n    appliedRulesCount: number;\r\n    correlationMatchesCount: number;\r\n    relatedEventsCount: number;\r\n    confidenceLevel: ConfidenceLevel;\r\n    hasAttackChain: boolean;\r\n    requiresManualReview: boolean;\r\n    isHighSeverity: boolean;\r\n    isCritical: boolean;\r\n    hasHighCorrelationQuality: boolean;\r\n    isCorrelationCompleted: boolean;\r\n    isHighConfidenceCorrelation: boolean;\r\n    hasMultipleRelatedEvents: boolean;\r\n    hasCorrelationMatches: boolean;\r\n    isPotentialAttackCampaign: boolean;\r\n    eventPriority: 'low' | 'medium' | 'high' | 'critical';\r\n    alertLevel: 'info' | 'warning' | 'alert' | 'critical';\r\n    recommendedActions: string[];\r\n    escalationRequirements: ReturnType<typeof this.getEscalationRequirements>;\r\n  } {\r\n    return {\r\n      correlatedEventId: this.aggregateId.toString(),\r\n      enrichedEventId: this.enrichedEventId.toString(),\r\n      eventType: this.eventType,\r\n      severity: this.severity,\r\n      correlationStatus: this.correlationStatus,\r\n      correlationQualityScore: this.correlationQualityScore,\r\n      appliedRulesCount: this.appliedRulesCount,\r\n      correlationMatchesCount: this.correlationMatchesCount,\r\n      relatedEventsCount: this.relatedEventsCount,\r\n      confidenceLevel: this.confidenceLevel,\r\n      hasAttackChain: this.hasAttackChain,\r\n      requiresManualReview: this.requiresManualReview,\r\n      isHighSeverity: this.isHighSeverity(),\r\n      isCritical: this.isCritical(),\r\n      hasHighCorrelationQuality: this.hasHighCorrelationQuality(),\r\n      isCorrelationCompleted: this.isCorrelationCompleted(),\r\n      isHighConfidenceCorrelation: this.isHighConfidenceCorrelation(),\r\n      hasMultipleRelatedEvents: this.hasMultipleRelatedEvents(),\r\n      hasCorrelationMatches: this.hasCorrelationMatches(),\r\n      isPotentialAttackCampaign: this.isPotentialAttackCampaign(),\r\n      eventPriority: this.getEventPriority(),\r\n      alertLevel: this.getAlertLevel(),\r\n      recommendedActions: this.getRecommendedActions(),\r\n      escalationRequirements: this.getEscalationRequirements(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventSummary: this.getEventSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}