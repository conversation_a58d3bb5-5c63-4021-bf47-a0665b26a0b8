629ea81588003d65b6319acd7492d459
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ConfigChangeDetectorService_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnConfigChange = exports.ConfigChangeDetectorService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const event_emitter_1 = require("@nestjs/event-emitter");
const chokidar_1 = require("chokidar");
const fs_1 = require("fs");
const path_1 = require("path");
const lodash_1 = require("lodash");
const dotenv = __importStar(require("dotenv"));
let ConfigChangeDetectorService = ConfigChangeDetectorService_1 = class ConfigChangeDetectorService {
    constructor(configService, eventEmitter) {
        this.configService = configService;
        this.eventEmitter = eventEmitter;
        this.logger = new common_1.Logger(ConfigChangeDetectorService_1.name);
        this.currentConfig = {};
        this.options = this.loadHotReloadOptions();
        this.debouncedHandleChange = (0, lodash_1.debounce)(this.handleConfigChange.bind(this), this.options.debounceMs);
        this.currentConfig = this.captureCurrentConfig();
    }
    async onModuleInit() {
        if (this.options.enabled) {
            await this.startWatching();
            this.logger.log('Configuration change detection started');
        }
        else {
            this.logger.debug('Configuration change detection disabled');
        }
    }
    async onModuleDestroy() {
        await this.stopWatching();
        this.logger.log('Configuration change detection stopped');
    }
    /**
     * Start watching configuration files for changes
     */
    async startWatching() {
        if (this.fileWatcher) {
            await this.stopWatching();
        }
        const watchPaths = this.getWatchPaths();
        if (watchPaths.length === 0) {
            this.logger.warn('No configuration files found to watch');
            return;
        }
        this.fileWatcher = (0, chokidar_1.watch)(watchPaths, {
            ignored: /(^|[\/\\])\../, // ignore dotfiles
            persistent: true,
            ignoreInitial: true,
            awaitWriteFinish: {
                stabilityThreshold: 100,
                pollInterval: 100,
            },
        });
        this.fileWatcher.on('change', (path) => {
            this.logger.debug(`Configuration file changed: ${path}`);
            this.debouncedHandleChange();
        });
        this.fileWatcher.on('add', (path) => {
            this.logger.debug(`Configuration file added: ${path}`);
            this.debouncedHandleChange();
        });
        this.fileWatcher.on('unlink', (path) => {
            this.logger.debug(`Configuration file removed: ${path}`);
            this.debouncedHandleChange();
        });
        this.fileWatcher.on('error', (error) => {
            this.logger.error('File watcher error:', error);
        });
        this.logger.log(`Watching ${watchPaths.length} configuration files for changes`);
    }
    /**
     * Stop watching configuration files
     */
    async stopWatching() {
        if (this.fileWatcher) {
            await this.fileWatcher.close();
            this.fileWatcher = undefined;
        }
    }
    /**
     * Manually trigger configuration reload
     */
    async reloadConfiguration() {
        this.logger.log('Manual configuration reload triggered');
        return this.handleConfigChange();
    }
    /**
     * Get current configuration snapshot
     */
    getCurrentConfig() {
        return { ...this.currentConfig };
    }
    /**
     * Check if a configuration key is critical (requires restart)
     */
    isCriticalKey(key) {
        return this.options.criticalKeys.includes(key) ||
            key.startsWith('DATABASE_') ||
            key.startsWith('REDIS_') ||
            key === 'PORT' ||
            key === 'NODE_ENV';
    }
    /**
     * Validate configuration changes
     */
    async validateChanges(changes) {
        if (!this.options.validateOnChange) {
            return { isValid: true, errors: [] };
        }
        const errors = [];
        for (const change of changes) {
            try {
                // Basic validation - you can extend this with more sophisticated validation
                if (change.key.includes('PORT') && change.newValue) {
                    const port = parseInt(change.newValue, 10);
                    if (isNaN(port) || port < 1 || port > 65535) {
                        errors.push(`Invalid port value for ${change.key}: ${change.newValue}`);
                    }
                }
                if (change.key.includes('URL') && change.newValue) {
                    try {
                        new URL(change.newValue);
                    }
                    catch {
                        errors.push(`Invalid URL value for ${change.key}: ${change.newValue}`);
                    }
                }
                if (change.key.includes('BOOLEAN') && change.newValue) {
                    if (!['true', 'false', '1', '0'].includes(change.newValue.toLowerCase())) {
                        errors.push(`Invalid boolean value for ${change.key}: ${change.newValue}`);
                    }
                }
            }
            catch (error) {
                errors.push(`Validation error for ${change.key}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * Apply configuration changes to the current process
     */
    async applyChanges(changes) {
        for (const change of changes) {
            switch (change.action) {
                case 'added':
                case 'modified':
                    process.env[change.key] = change.newValue;
                    this.currentConfig[change.key] = change.newValue;
                    break;
                case 'removed':
                    delete process.env[change.key];
                    delete this.currentConfig[change.key];
                    break;
            }
        }
        this.logger.debug(`Applied ${changes.length} configuration changes`);
    }
    /**
     * Handle configuration change event
     */
    async handleConfigChange() {
        try {
            const newConfig = this.loadCurrentEnvironmentConfig();
            const changes = this.detectChanges(this.currentConfig, newConfig);
            if (changes.length === 0) {
                this.logger.debug('No configuration changes detected');
                return null;
            }
            this.logger.log(`Detected ${changes.length} configuration changes`);
            // Validate changes
            const validation = await this.validateChanges(changes);
            if (!validation.isValid) {
                this.logger.error('Configuration validation failed:', validation.errors);
                return null;
            }
            // Check for critical changes
            const criticalChanges = changes.filter(change => this.isCriticalKey(change.key));
            const hasCriticalChanges = criticalChanges.length > 0;
            if (hasCriticalChanges && this.options.restartOnCriticalChange) {
                this.logger.warn(`Critical configuration changes detected: ${criticalChanges.map(c => c.key).join(', ')}. ` +
                    'Application restart may be required.');
            }
            // Apply non-critical changes
            const nonCriticalChanges = changes.filter(change => !this.isCriticalKey(change.key));
            if (nonCriticalChanges.length > 0) {
                await this.applyChanges(nonCriticalChanges);
            }
            // Create change event
            const changeEvent = {
                type: 'file',
                source: 'environment-files',
                changes,
                timestamp: new Date(),
            };
            // Emit event
            this.eventEmitter.emit('config.changed', changeEvent);
            // Log changes
            this.logChanges(changes);
            return changeEvent;
        }
        catch (error) {
            this.logger.error('Error handling configuration change:', error);
            return null;
        }
    }
    /**
     * Detect changes between old and new configuration
     */
    detectChanges(oldConfig, newConfig) {
        const changes = [];
        const allKeys = new Set([...Object.keys(oldConfig), ...Object.keys(newConfig)]);
        for (const key of allKeys) {
            // Skip excluded keys
            if (this.options.excludeKeys.includes(key)) {
                continue;
            }
            // Only include specified keys if includeKeys is not empty
            if (this.options.includeKeys.length > 0 && !this.options.includeKeys.includes(key)) {
                continue;
            }
            const oldValue = oldConfig[key];
            const newValue = newConfig[key];
            if (oldValue === undefined && newValue !== undefined) {
                changes.push({
                    key,
                    oldValue: undefined,
                    newValue,
                    action: 'added',
                });
            }
            else if (oldValue !== undefined && newValue === undefined) {
                changes.push({
                    key,
                    oldValue,
                    newValue: undefined,
                    action: 'removed',
                });
            }
            else if (oldValue !== newValue) {
                changes.push({
                    key,
                    oldValue,
                    newValue,
                    action: 'modified',
                });
            }
        }
        return changes;
    }
    /**
     * Load current environment configuration from files
     */
    loadCurrentEnvironmentConfig() {
        const config = {};
        const currentEnv = process.env['NODE_ENV'] || 'development';
        // Load environment files in order of precedence
        const envFiles = [
            '.env',
            '.env.local',
            `.env.${currentEnv}`,
            `.env.${currentEnv}.local`,
        ];
        for (const envFile of envFiles) {
            const filePath = (0, path_1.join)(process.cwd(), envFile);
            if ((0, fs_1.existsSync)(filePath)) {
                try {
                    const fileContent = (0, fs_1.readFileSync)(filePath, 'utf8');
                    const parsed = dotenv.parse(fileContent);
                    Object.assign(config, parsed);
                }
                catch (error) {
                    this.logger.warn(`Failed to load environment file ${envFile}:`, error);
                }
            }
        }
        return config;
    }
    /**
     * Capture current configuration state
     */
    captureCurrentConfig() {
        return { ...process.env };
    }
    /**
     * Get paths to watch for configuration changes
     */
    getWatchPaths() {
        const paths = [];
        const currentEnv = process.env['NODE_ENV'] || 'development';
        // Default environment files
        const defaultFiles = [
            '.env',
            '.env.local',
            `.env.${currentEnv}`,
            `.env.${currentEnv}.local`,
        ];
        // Add existing files to watch list
        for (const file of [...defaultFiles, ...this.options.watchFiles]) {
            const filePath = (0, path_1.join)(process.cwd(), file);
            if ((0, fs_1.existsSync)(filePath)) {
                paths.push(filePath);
            }
        }
        return paths;
    }
    /**
     * Load hot-reload options from configuration
     */
    loadHotReloadOptions() {
        const isDevelopment = (process.env['NODE_ENV'] || 'development') === 'development';
        return {
            enabled: process.env['CONFIG_HOT_RELOAD_ENABLED'] === 'true' || isDevelopment,
            watchFiles: (process.env['CONFIG_WATCH_FILES'] || '').split(',').filter(Boolean),
            debounceMs: parseInt(process.env['CONFIG_DEBOUNCE_MS'] || '1000', 10),
            excludeKeys: (process.env['CONFIG_EXCLUDE_KEYS'] || '').split(',').filter(Boolean),
            includeKeys: (process.env['CONFIG_INCLUDE_KEYS'] || '').split(',').filter(Boolean),
            validateOnChange: process.env['CONFIG_VALIDATE_ON_CHANGE'] !== 'false',
            restartOnCriticalChange: process.env['CONFIG_RESTART_ON_CRITICAL_CHANGE'] === 'true',
            criticalKeys: (process.env['CONFIG_CRITICAL_KEYS'] || 'DATABASE_HOST,DATABASE_PORT,REDIS_HOST,REDIS_PORT,PORT,NODE_ENV')
                .split(',')
                .filter(Boolean),
        };
    }
    /**
     * Log configuration changes
     */
    logChanges(changes) {
        for (const change of changes) {
            const logMessage = `Config ${change.action}: ${change.key}`;
            if (change.key.toLowerCase().includes('password') ||
                change.key.toLowerCase().includes('secret') ||
                change.key.toLowerCase().includes('key')) {
                this.logger.log(`${logMessage} = [REDACTED]`);
            }
            else {
                this.logger.log(`${logMessage} = ${change.oldValue} -> ${change.newValue}`);
            }
        }
    }
};
exports.ConfigChangeDetectorService = ConfigChangeDetectorService;
exports.ConfigChangeDetectorService = ConfigChangeDetectorService = ConfigChangeDetectorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof event_emitter_1.EventEmitter2 !== "undefined" && event_emitter_1.EventEmitter2) === "function" ? _b : Object])
], ConfigChangeDetectorService);
/**
 * Configuration change event listener decorator
 */
const OnConfigChange = (keys) => {
    return (target, propertyName, descriptor) => {
        const originalMethod = descriptor.value;
        descriptor.value = function (...args) {
            const event = args[0];
            if (keys && keys.length > 0) {
                const relevantChanges = event.changes.filter(change => keys.includes(change.key));
                if (relevantChanges.length === 0) {
                    return;
                }
            }
            return originalMethod.apply(this, args);
        };
        return descriptor;
    };
};
exports.OnConfigChange = OnConfigChange;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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