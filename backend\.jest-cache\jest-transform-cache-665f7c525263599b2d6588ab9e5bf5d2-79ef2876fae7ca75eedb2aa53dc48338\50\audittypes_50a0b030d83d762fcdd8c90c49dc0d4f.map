{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\types\\audit.types.ts", "mappings": ";AAAA;;;;;GAKG;;;AAEH;;GAEG;AACH,IAAY,WAuBX;AAvBD,WAAY,WAAW;IACrB,gCAAiB,CAAA;IACjB,4BAAa,CAAA;IACb,gCAAiB,CAAA;IACjB,gCAAiB,CAAA;IACjB,8BAAe,CAAA;IACf,gCAAiB,CAAA;IACjB,gCAAiB,CAAA;IACjB,gCAAiB,CAAA;IACjB,gCAAiB,CAAA;IACjB,kCAAmB,CAAA;IACnB,gCAAiB,CAAA;IACjB,kCAAmB,CAAA;IACnB,gCAAiB,CAAA;IACjB,kCAAmB,CAAA;IACnB,oCAAqB,CAAA;IACrB,wCAAyB,CAAA;IACzB,sCAAuB,CAAA;IACvB,gCAAiB,CAAA;IACjB,kCAAmB,CAAA;IACnB,8BAAe,CAAA;IACf,kCAAmB,CAAA;IACnB,sCAAuB,CAAA;AACzB,CAAC,EAvBW,WAAW,2BAAX,WAAW,QAuBtB;AAED;;GAEG;AACH,IAAY,WAMX;AAND,WAAY,WAAW;IACrB,kCAAmB,CAAA;IACnB,kCAAmB,CAAA;IACnB,kCAAmB,CAAA;IACnB,kCAAmB,CAAA;IACnB,sCAAuB,CAAA;AACzB,CAAC,EANW,WAAW,2BAAX,WAAW,QAMtB;AAED;;GAEG;AACH,IAAY,aAKX;AALD,WAAY,aAAa;IACvB,4BAAW,CAAA;IACX,kCAAiB,CAAA;IACjB,8BAAa,CAAA;IACb,sCAAqB,CAAA;AACvB,CAAC,EALW,aAAa,6BAAb,aAAa,QAKxB;AAED;;GAEG;AACH,IAAY,aAWX;AAXD,WAAY,aAAa;IACvB,kDAAiC,CAAA;IACjC,gDAA+B,CAAA;IAC/B,4CAA2B,CAAA;IAC3B,wDAAuC,CAAA;IACvC,8DAA6C,CAAA;IAC7C,kDAAiC,CAAA;IACjC,0CAAyB,CAAA;IACzB,4CAA2B,CAAA;IAC3B,gCAAe,CAAA;IACf,sDAAqC,CAAA;AACvC,CAAC,EAXW,aAAa,6BAAb,aAAa,QAWxB;AA0PD;;GAEG;AACH,MAAa,UAAU;IAsBrB;;OAEG;IACH,MAAM,CAAC,WAAW,CAChB,MAAmB,EACnB,MAAmB,EACnB,WAAmB,EACnB,UAA+B,EAAE;QAEjC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,OAAO;YACL,EAAE,EAAE,MAAM,CAAC,UAAU,EAAE;YACvB,SAAS,EAAE,GAAG;YACd,MAAM;YACN,MAAM;YACN,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC;YACtE,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC;YAC9D,WAAW;YACX,MAAM,EAAE;gBACN,WAAW,EAAE,UAAU;gBACvB,GAAG,OAAO,CAAC,MAAM;aAClB;YACD,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,MAAmB,EAAE,MAAmB;QAC3D,wBAAwB;QACxB,IAAI;YACF,WAAW,CAAC,MAAM;YAClB,WAAW,CAAC,KAAK;YACjB,WAAW,CAAC,OAAO;YACnB,WAAW,CAAC,SAAS;SACtB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACnB,OAAO,MAAM,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC;QACtF,CAAC;QAED,0BAA0B;QAC1B,IAAI;YACF,WAAW,CAAC,MAAM;YAClB,WAAW,CAAC,MAAM;YAClB,WAAW,CAAC,OAAO;YACnB,WAAW,CAAC,MAAM;YAClB,WAAW,CAAC,OAAO;YACnB,WAAW,CAAC,QAAQ;YACpB,WAAW,CAAC,UAAU;SACvB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACnB,OAAO,MAAM,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC;QACpF,CAAC;QAED,uBAAuB;QACvB,OAAO,MAAM,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,MAAmB;QACtC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,WAAW,CAAC,KAAK,CAAC;YACvB,KAAK,WAAW,CAAC,MAAM;gBACrB,OAAO,aAAa,CAAC,cAAc,CAAC;YAEtC,KAAK,WAAW,CAAC,MAAM;gBACrB,OAAO,aAAa,CAAC,aAAa,CAAC;YAErC,KAAK,WAAW,CAAC,IAAI,CAAC;YACtB,KAAK,WAAW,CAAC,MAAM;gBACrB,OAAO,aAAa,CAAC,WAAW,CAAC;YAEnC,KAAK,WAAW,CAAC,MAAM,CAAC;YACxB,KAAK,WAAW,CAAC,MAAM,CAAC;YACxB,KAAK,WAAW,CAAC,MAAM,CAAC;YACxB,KAAK,WAAW,CAAC,MAAM;gBACrB,OAAO,aAAa,CAAC,iBAAiB,CAAC;YAEzC,KAAK,WAAW,CAAC,SAAS,CAAC;YAC3B,KAAK,WAAW,CAAC,MAAM,CAAC;YACxB,KAAK,WAAW,CAAC,OAAO;gBACtB,OAAO,aAAa,CAAC,oBAAoB,CAAC;YAE5C;gBACE,OAAO,aAAa,CAAC,gBAAgB,CAAC;QAC1C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,KAA0B;QAClD,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,yBAAyB;QACzB,QAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;YACvB,KAAK,aAAa,CAAC,QAAQ;gBACzB,KAAK,IAAI,EAAE,CAAC;gBACZ,MAAM;YACR,KAAK,aAAa,CAAC,IAAI;gBACrB,KAAK,IAAI,EAAE,CAAC;gBACZ,MAAM;YACR,KAAK,aAAa,CAAC,MAAM;gBACvB,KAAK,IAAI,EAAE,CAAC;gBACZ,MAAM;YACR,KAAK,aAAa,CAAC,GAAG;gBACpB,KAAK,IAAI,EAAE,CAAC;gBACZ,MAAM;QACV,CAAC;QAED,cAAc;QACd,MAAM,eAAe,GAAG;YACtB,WAAW,CAAC,MAAM;YAClB,WAAW,CAAC,KAAK;YACjB,WAAW,CAAC,OAAO;YACnB,WAAW,CAAC,SAAS;SACtB,CAAC;QACF,IAAI,KAAK,CAAC,MAAM,IAAI,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,gBAAgB;QAChB,IAAI,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC,OAAO,EAAE,CAAC;YACzC,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,4BAA4B;QAC5B,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;YACf,kBAAkB;YAClB,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACnF,KAAK,IAAI,EAAE,CAAC;YACd,CAAC;YAED,sCAAsC;YACtC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBAChD,oDAAoD;gBACpD,mDAAmD;gBACnD,KAAK,IAAI,CAAC,CAAC;YACb,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YACxC,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;gBAC1B,KAAK,IAAI,EAAE,CAAC;YACd,CAAC;QACH,CAAC;QAED,gCAAgC;QAChC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,KAAiB,EAAE,MAAmB;QACzD,IAAI,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAChC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,SAAS,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC;QAE/B,oCAAoC;QACpC,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;YACnB,SAAS,CAAC,IAAI,GAAG;gBACf,GAAG,SAAS,CAAC,IAAI;gBACjB,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;gBACpF,SAAS,EAAE,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS;aACrG,CAAC;QACJ,CAAC;QAED,gCAAgC;QAChC,IAAI,SAAS,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;YACpD,OAAO,SAAS,CAAC,OAAO,CAAC;QAC3B,CAAC;aAAM,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YAC7B,SAAS,CAAC,OAAO,GAAG,UAAU,CAAC,mBAAmB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACxE,CAAC;QAED,4BAA4B;QAC5B,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACvB,SAAS,CAAC,QAAQ,GAAG,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,SAAS,CAAC,KAAa;QACpC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzC,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACtB,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,OAAO,MAAM,EAAE,CAAC;QACpC,CAAC;QACD,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,MAAM,EAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,aAAa,CAAC,EAAU;QACrC,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC;QAC5C,CAAC;QACD,uBAAuB;QACvB,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC;IACpC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,OAAyB;QAC1D,MAAM,eAAe,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;QAE7E,MAAM,cAAc,GAAG,CAAC,GAAwB,EAAuB,EAAE;YACvE,MAAM,SAAS,GAAwB,EAAE,CAAC;YAE1C,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;gBAC3C,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBACrE,SAAS,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC;gBACpC,CAAC;qBAAM,CAAC;oBACN,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;gBACzB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC;QAEF,OAAO;YACL,GAAG,OAAO;YACV,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;YACnE,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;SACjE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,QAA6B;QAC3D,MAAM,aAAa,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;QAC5F,MAAM,SAAS,GAAwB,EAAE,CAAC;QAE1C,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAChD,IAAI,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;gBAC3E,SAAS,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC;YACpC,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACzB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,KAA0B,EAAE,MAAmB;QAChE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YAClE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,KAAK,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,KAAK,CAAC,QAAQ,IAAI,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC;YACxH,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,QAAuB;QACrD,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,aAAa,CAAC,GAAG;gBACpB,OAAO,CAAC,CAAC;YACX,KAAK,aAAa,CAAC,MAAM;gBACvB,OAAO,CAAC,CAAC;YACX,KAAK,aAAa,CAAC,IAAI;gBACrB,OAAO,CAAC,CAAC;YACX,KAAK,aAAa,CAAC,QAAQ;gBACzB,OAAO,CAAC,CAAC;YACX;gBACE,OAAO,CAAC,CAAC;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,KAAiB;QAClC,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;QAChD,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,MAAM,IAAI,QAAQ,CAAC;QACpE,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,YAAY,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;QAExG,OAAO,IAAI,SAAS,KAAK,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,QAAQ,MAAM,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,QAAQ,GAAG,CAAC;IACpG,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,MAAwB;QACzC,MAAM,KAAK,GAAwB,EAAE,CAAC;QAEtC,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,KAAK,CAAC,SAAS,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,SAAS,EAAE,CAAC;QAC/C,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,KAAK,CAAC,SAAS,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;QACjE,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,KAAK,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QACvC,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;QACzC,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;QACzC,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtD,KAAK,CAAC,QAAQ,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC;QAC9C,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtD,KAAK,CAAC,QAAQ,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC;QAC9C,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5D,KAAK,CAAC,uBAAuB,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,aAAa,EAAE,CAAC;QACjE,CAAC;QAED,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,KAAK,CAAC,qBAAqB,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC;QAC7D,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,KAAK,CAAC,GAAG,GAAG;gBACV,EAAE,WAAW,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE;gBACzD,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE;aACtD,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YAC3E,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;YACrB,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;gBACtC,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC;YAC7C,CAAC;YACD,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;gBACtC,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC;YAC7C,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9D,KAAK,CAAC,cAAc,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,cAAc,EAAE,CAAC;QACxD,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACpB,KAAK,CAAC,iBAAiB,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;QAC7C,CAAC;QAED,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1D,KAAK,CAAC,oBAAoB,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC;QAC7D,CAAC;QAED,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1D,KAAK,CAAC,oBAAoB,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC;QAC7D,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;;AAvZH,gCAwZC;AAvZC;;GAEG;AACa,yBAAc,GAAgB;IAC5C,OAAO,EAAE,IAAI;IACb,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC;IAC1C,iBAAiB,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;IAC/C,eAAe,EAAE,aAAa,CAAC,GAAG;IAClC,oBAAoB,EAAE,KAAK;IAC3B,kBAAkB,EAAE,IAAI;IACxB,gBAAgB,EAAE,IAAI,EAAE,UAAU;IAClC,kBAAkB,EAAE,IAAI;IACxB,SAAS,EAAE,IAAI;IACf,YAAY,EAAE,IAAI;IAClB,eAAe,EAAE;QACf,WAAW,EAAE,EAAE;QACf,SAAS,EAAE,EAAE;QACb,UAAU,EAAE,EAAE;KACf;CACF,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\types\\audit.types.ts"], "sourcesContent": ["/**\r\n * Audit Types\r\n * \r\n * Common types and interfaces for audit logging across the application.\r\n * Provides consistent audit patterns and utilities.\r\n */\r\n\r\n/**\r\n * Audit Action Types\r\n */\r\nexport enum AuditAction {\r\n  CREATE = 'create',\r\n  READ = 'read',\r\n  UPDATE = 'update',\r\n  DELETE = 'delete',\r\n  LOGIN = 'login',\r\n  LOGOUT = 'logout',\r\n  ACCESS = 'access',\r\n  EXPORT = 'export',\r\n  IMPORT = 'import',\r\n  APPROVE = 'approve',\r\n  REJECT = 'reject',\r\n  EXECUTE = 'execute',\r\n  CANCEL = 'cancel',\r\n  SUSPEND = 'suspend',\r\n  ACTIVATE = 'activate',\r\n  DEACTIVATE = 'deactivate',\r\n  CONFIGURE = 'configure',\r\n  BACKUP = 'backup',\r\n  RESTORE = 'restore',\r\n  PURGE = 'purge',\r\n  ARCHIVE = 'archive',\r\n  UNARCHIVE = 'unarchive',\r\n}\r\n\r\n/**\r\n * Audit Result Status\r\n */\r\nexport enum AuditResult {\r\n  SUCCESS = 'success',\r\n  FAILURE = 'failure',\r\n  PARTIAL = 'partial',\r\n  PENDING = 'pending',\r\n  CANCELLED = 'cancelled',\r\n}\r\n\r\n/**\r\n * Audit Severity Levels\r\n */\r\nexport enum AuditSeverity {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CRITICAL = 'critical',\r\n}\r\n\r\n/**\r\n * Audit Categories\r\n */\r\nexport enum AuditCategory {\r\n  AUTHENTICATION = 'authentication',\r\n  AUTHORIZATION = 'authorization',\r\n  DATA_ACCESS = 'data_access',\r\n  DATA_MODIFICATION = 'data_modification',\r\n  SYSTEM_CONFIGURATION = 'system_configuration',\r\n  SECURITY_EVENT = 'security_event',\r\n  COMPLIANCE = 'compliance',\r\n  PERFORMANCE = 'performance',\r\n  ERROR = 'error',\r\n  BUSINESS_PROCESS = 'business_process',\r\n}\r\n\r\n/**\r\n * User Context for Audit\r\n */\r\nexport interface AuditUserContext {\r\n  /** User identifier */\r\n  userId: string;\r\n  /** Username */\r\n  username?: string;\r\n  /** User email */\r\n  email?: string;\r\n  /** User roles */\r\n  roles?: string[];\r\n  /** User permissions */\r\n  permissions?: string[];\r\n  /** Session identifier */\r\n  sessionId?: string;\r\n  /** User agent */\r\n  userAgent?: string;\r\n  /** IP address */\r\n  ipAddress?: string;\r\n  /** Geographic location */\r\n  location?: {\r\n    country?: string;\r\n    region?: string;\r\n    city?: string;\r\n    coordinates?: {\r\n      latitude: number;\r\n      longitude: number;\r\n    };\r\n  };\r\n}\r\n\r\n/**\r\n * System Context for Audit\r\n */\r\nexport interface AuditSystemContext {\r\n  /** Application name */\r\n  application: string;\r\n  /** Application version */\r\n  version?: string;\r\n  /** Environment (dev, staging, prod) */\r\n  environment?: string;\r\n  /** Service name */\r\n  service?: string;\r\n  /** Module name */\r\n  module?: string;\r\n  /** Server/instance identifier */\r\n  instanceId?: string;\r\n  /** Request ID for tracing */\r\n  requestId?: string;\r\n  /** Correlation ID */\r\n  correlationId?: string;\r\n  /** Tenant identifier (for multi-tenant systems) */\r\n  tenantId?: string;\r\n}\r\n\r\n/**\r\n * Resource Context for Audit\r\n */\r\nexport interface AuditResourceContext {\r\n  /** Resource type */\r\n  resourceType: string;\r\n  /** Resource identifier */\r\n  resourceId?: string;\r\n  /** Resource name */\r\n  resourceName?: string;\r\n  /** Parent resource information */\r\n  parentResource?: {\r\n    type: string;\r\n    id: string;\r\n    name?: string;\r\n  };\r\n  /** Resource attributes */\r\n  attributes?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Data Changes for Audit\r\n */\r\nexport interface AuditDataChanges {\r\n  /** Previous values */\r\n  before?: Record<string, any>;\r\n  /** New values */\r\n  after?: Record<string, any>;\r\n  /** Changed fields */\r\n  changedFields?: string[];\r\n  /** Change summary */\r\n  summary?: string;\r\n}\r\n\r\n/**\r\n * Audit Entry\r\n * Complete audit log entry\r\n */\r\nexport interface AuditEntry {\r\n  /** Unique audit entry identifier */\r\n  id: string;\r\n  /** Timestamp of the event */\r\n  timestamp: Date;\r\n  /** Action performed */\r\n  action: AuditAction;\r\n  /** Result of the action */\r\n  result: AuditResult;\r\n  /** Severity level */\r\n  severity: AuditSeverity;\r\n  /** Audit category */\r\n  category: AuditCategory;\r\n  /** Event description */\r\n  description: string;\r\n  /** Detailed message */\r\n  message?: string;\r\n  /** User context */\r\n  user?: AuditUserContext;\r\n  /** System context */\r\n  system: AuditSystemContext;\r\n  /** Resource context */\r\n  resource?: AuditResourceContext;\r\n  /** Data changes */\r\n  changes?: AuditDataChanges;\r\n  /** Additional metadata */\r\n  metadata?: Record<string, any>;\r\n  /** Duration of the operation (in milliseconds) */\r\n  duration?: number;\r\n  /** Error information (for failed operations) */\r\n  error?: {\r\n    code?: string;\r\n    message?: string;\r\n    stack?: string;\r\n  };\r\n  /** Risk score (0-100) */\r\n  riskScore?: number;\r\n  /** Compliance tags */\r\n  complianceTags?: string[];\r\n  /** Retention period (in days) */\r\n  retentionDays?: number;\r\n}\r\n\r\n/**\r\n * Audit Query Parameters\r\n */\r\nexport interface AuditQueryParams {\r\n  /** Start date for query */\r\n  startDate?: Date;\r\n  /** End date for query */\r\n  endDate?: Date;\r\n  /** User ID filter */\r\n  userId?: string;\r\n  /** Action filter */\r\n  actions?: AuditAction[];\r\n  /** Result filter */\r\n  results?: AuditResult[];\r\n  /** Severity filter */\r\n  severities?: AuditSeverity[];\r\n  /** Category filter */\r\n  categories?: AuditCategory[];\r\n  /** Resource type filter */\r\n  resourceTypes?: string[];\r\n  /** Resource ID filter */\r\n  resourceIds?: string[];\r\n  /** Text search in description/message */\r\n  search?: string;\r\n  /** Minimum risk score */\r\n  minRiskScore?: number;\r\n  /** Maximum risk score */\r\n  maxRiskScore?: number;\r\n  /** Compliance tags filter */\r\n  complianceTags?: string[];\r\n  /** Tenant ID filter */\r\n  tenantId?: string;\r\n  /** Application filter */\r\n  applications?: string[];\r\n  /** Environment filter */\r\n  environments?: string[];\r\n}\r\n\r\n/**\r\n * Audit Statistics\r\n */\r\nexport interface AuditStatistics {\r\n  /** Total number of audit entries */\r\n  totalEntries: number;\r\n  /** Entries by action */\r\n  byAction: Record<AuditAction, number>;\r\n  /** Entries by result */\r\n  byResult: Record<AuditResult, number>;\r\n  /** Entries by severity */\r\n  bySeverity: Record<AuditSeverity, number>;\r\n  /** Entries by category */\r\n  byCategory: Record<AuditCategory, number>;\r\n  /** Top users by activity */\r\n  topUsers: Array<{\r\n    userId: string;\r\n    username?: string;\r\n    count: number;\r\n  }>;\r\n  /** Top resources by access */\r\n  topResources: Array<{\r\n    resourceType: string;\r\n    resourceId: string;\r\n    resourceName?: string;\r\n    count: number;\r\n  }>;\r\n  /** Risk distribution */\r\n  riskDistribution: {\r\n    low: number;    // 0-25\r\n    medium: number; // 26-50\r\n    high: number;   // 51-75\r\n    critical: number; // 76-100\r\n  };\r\n  /** Time period */\r\n  period: {\r\n    start: Date;\r\n    end: Date;\r\n  };\r\n}\r\n\r\n/**\r\n * Audit Configuration\r\n */\r\nexport interface AuditConfig {\r\n  /** Whether audit logging is enabled */\r\n  enabled: boolean;\r\n  /** Actions to audit */\r\n  auditedActions: AuditAction[];\r\n  /** Categories to audit */\r\n  auditedCategories: AuditCategory[];\r\n  /** Minimum severity to log */\r\n  minimumSeverity: AuditSeverity;\r\n  /** Whether to include sensitive data */\r\n  includeSensitiveData: boolean;\r\n  /** Whether to include data changes */\r\n  includeDataChanges: boolean;\r\n  /** Maximum retention period (in days) */\r\n  maxRetentionDays: number;\r\n  /** Whether to compress old entries */\r\n  compressOldEntries: boolean;\r\n  /** Batch size for bulk operations */\r\n  batchSize: number;\r\n  /** Whether to enable real-time alerts */\r\n  enableAlerts: boolean;\r\n  /** Alert thresholds */\r\n  alertThresholds: {\r\n    failureRate: number; // Percentage\r\n    riskScore: number;   // 0-100\r\n    timeWindow: number;  // Minutes\r\n  };\r\n}\r\n\r\n/**\r\n * Audit Utilities\r\n */\r\nexport class AuditUtils {\r\n  /**\r\n   * Default audit configuration\r\n   */\r\n  static readonly DEFAULT_CONFIG: AuditConfig = {\r\n    enabled: true,\r\n    auditedActions: Object.values(AuditAction),\r\n    auditedCategories: Object.values(AuditCategory),\r\n    minimumSeverity: AuditSeverity.LOW,\r\n    includeSensitiveData: false,\r\n    includeDataChanges: true,\r\n    maxRetentionDays: 2555, // 7 years\r\n    compressOldEntries: true,\r\n    batchSize: 1000,\r\n    enableAlerts: true,\r\n    alertThresholds: {\r\n      failureRate: 10,\r\n      riskScore: 75,\r\n      timeWindow: 60,\r\n    },\r\n  };\r\n\r\n  /**\r\n   * Create an audit entry\r\n   */\r\n  static createEntry(\r\n    action: AuditAction,\r\n    result: AuditResult,\r\n    description: string,\r\n    options: Partial<AuditEntry> = {}\r\n  ): AuditEntry {\r\n    const now = new Date();\r\n    \r\n    return {\r\n      id: crypto.randomUUID(),\r\n      timestamp: now,\r\n      action,\r\n      result,\r\n      severity: options.severity || AuditUtils.inferSeverity(action, result),\r\n      category: options.category || AuditUtils.inferCategory(action),\r\n      description,\r\n      system: {\r\n        application: 'sentinel',\r\n        ...options.system,\r\n      },\r\n      ...options,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Infer severity from action and result\r\n   */\r\n  static inferSeverity(action: AuditAction, result: AuditResult): AuditSeverity {\r\n    // High severity actions\r\n    if ([\r\n      AuditAction.DELETE,\r\n      AuditAction.PURGE,\r\n      AuditAction.EXECUTE,\r\n      AuditAction.CONFIGURE,\r\n    ].includes(action)) {\r\n      return result === AuditResult.FAILURE ? AuditSeverity.CRITICAL : AuditSeverity.HIGH;\r\n    }\r\n\r\n    // Medium severity actions\r\n    if ([\r\n      AuditAction.CREATE,\r\n      AuditAction.UPDATE,\r\n      AuditAction.APPROVE,\r\n      AuditAction.REJECT,\r\n      AuditAction.SUSPEND,\r\n      AuditAction.ACTIVATE,\r\n      AuditAction.DEACTIVATE,\r\n    ].includes(action)) {\r\n      return result === AuditResult.FAILURE ? AuditSeverity.HIGH : AuditSeverity.MEDIUM;\r\n    }\r\n\r\n    // Low severity actions\r\n    return result === AuditResult.FAILURE ? AuditSeverity.MEDIUM : AuditSeverity.LOW;\r\n  }\r\n\r\n  /**\r\n   * Infer category from action\r\n   */\r\n  static inferCategory(action: AuditAction): AuditCategory {\r\n    switch (action) {\r\n      case AuditAction.LOGIN:\r\n      case AuditAction.LOGOUT:\r\n        return AuditCategory.AUTHENTICATION;\r\n      \r\n      case AuditAction.ACCESS:\r\n        return AuditCategory.AUTHORIZATION;\r\n      \r\n      case AuditAction.READ:\r\n      case AuditAction.EXPORT:\r\n        return AuditCategory.DATA_ACCESS;\r\n      \r\n      case AuditAction.CREATE:\r\n      case AuditAction.UPDATE:\r\n      case AuditAction.DELETE:\r\n      case AuditAction.IMPORT:\r\n        return AuditCategory.DATA_MODIFICATION;\r\n      \r\n      case AuditAction.CONFIGURE:\r\n      case AuditAction.BACKUP:\r\n      case AuditAction.RESTORE:\r\n        return AuditCategory.SYSTEM_CONFIGURATION;\r\n      \r\n      default:\r\n        return AuditCategory.BUSINESS_PROCESS;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate risk score based on various factors\r\n   */\r\n  static calculateRiskScore(entry: Partial<AuditEntry>): number {\r\n    let score = 0;\r\n\r\n    // Base score by severity\r\n    switch (entry.severity) {\r\n      case AuditSeverity.CRITICAL:\r\n        score += 40;\r\n        break;\r\n      case AuditSeverity.HIGH:\r\n        score += 30;\r\n        break;\r\n      case AuditSeverity.MEDIUM:\r\n        score += 20;\r\n        break;\r\n      case AuditSeverity.LOW:\r\n        score += 10;\r\n        break;\r\n    }\r\n\r\n    // Action risk\r\n    const highRiskActions = [\r\n      AuditAction.DELETE,\r\n      AuditAction.PURGE,\r\n      AuditAction.EXECUTE,\r\n      AuditAction.CONFIGURE,\r\n    ];\r\n    if (entry.action && highRiskActions.includes(entry.action)) {\r\n      score += 20;\r\n    }\r\n\r\n    // Result impact\r\n    if (entry.result === AuditResult.FAILURE) {\r\n      score += 15;\r\n    }\r\n\r\n    // User context risk factors\r\n    if (entry.user) {\r\n      // Privileged user\r\n      if (entry.user.roles?.includes('admin') || entry.user.roles?.includes('superuser')) {\r\n        score += 10;\r\n      }\r\n      \r\n      // Unusual location (simplified check)\r\n      if (entry.user.location && entry.user.ipAddress) {\r\n        // This would typically involve geolocation analysis\r\n        // For now, just add a small risk for remote access\r\n        score += 5;\r\n      }\r\n    }\r\n\r\n    // Time-based risk (off-hours access)\r\n    if (entry.timestamp) {\r\n      const hour = entry.timestamp.getHours();\r\n      if (hour < 6 || hour > 22) {\r\n        score += 10;\r\n      }\r\n    }\r\n\r\n    // Ensure score is within bounds\r\n    return Math.min(Math.max(score, 0), 100);\r\n  }\r\n\r\n  /**\r\n   * Sanitize sensitive data from audit entry\r\n   */\r\n  static sanitizeEntry(entry: AuditEntry, config: AuditConfig): AuditEntry {\r\n    if (config.includeSensitiveData) {\r\n      return entry;\r\n    }\r\n\r\n    const sanitized = { ...entry };\r\n\r\n    // Remove sensitive user information\r\n    if (sanitized.user) {\r\n      sanitized.user = {\r\n        ...sanitized.user,\r\n        email: sanitized.user.email ? AuditUtils.maskEmail(sanitized.user.email) : undefined,\r\n        ipAddress: sanitized.user.ipAddress ? AuditUtils.maskIpAddress(sanitized.user.ipAddress) : undefined,\r\n      };\r\n    }\r\n\r\n    // Remove sensitive data changes\r\n    if (sanitized.changes && !config.includeDataChanges) {\r\n      delete sanitized.changes;\r\n    } else if (sanitized.changes) {\r\n      sanitized.changes = AuditUtils.sanitizeDataChanges(sanitized.changes);\r\n    }\r\n\r\n    // Remove sensitive metadata\r\n    if (sanitized.metadata) {\r\n      sanitized.metadata = AuditUtils.sanitizeMetadata(sanitized.metadata);\r\n    }\r\n\r\n    return sanitized;\r\n  }\r\n\r\n  /**\r\n   * Mask email address\r\n   */\r\n  private static maskEmail(email: string): string {\r\n    const [local, domain] = email.split('@');\r\n    if (local.length <= 2) {\r\n      return `${local[0]}***@${domain}`;\r\n    }\r\n    return `${local.substring(0, 2)}***@${domain}`;\r\n  }\r\n\r\n  /**\r\n   * Mask IP address\r\n   */\r\n  private static maskIpAddress(ip: string): string {\r\n    const parts = ip.split('.');\r\n    if (parts.length === 4) {\r\n      return `${parts[0]}.${parts[1]}.***.***.`;\r\n    }\r\n    // IPv6 or other format\r\n    return ip.substring(0, 8) + '***';\r\n  }\r\n\r\n  /**\r\n   * Sanitize data changes\r\n   */\r\n  private static sanitizeDataChanges(changes: AuditDataChanges): AuditDataChanges {\r\n    const sensitiveFields = ['password', 'token', 'secret', 'key', 'credential'];\r\n    \r\n    const sanitizeObject = (obj: Record<string, any>): Record<string, any> => {\r\n      const sanitized: Record<string, any> = {};\r\n      \r\n      Object.entries(obj).forEach(([key, value]) => {\r\n        if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {\r\n          sanitized[key] = '***REDACTED***';\r\n        } else {\r\n          sanitized[key] = value;\r\n        }\r\n      });\r\n      \r\n      return sanitized;\r\n    };\r\n\r\n    return {\r\n      ...changes,\r\n      before: changes.before ? sanitizeObject(changes.before) : undefined,\r\n      after: changes.after ? sanitizeObject(changes.after) : undefined,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Sanitize metadata\r\n   */\r\n  private static sanitizeMetadata(metadata: Record<string, any>): Record<string, any> {\r\n    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'credential', 'authorization'];\r\n    const sanitized: Record<string, any> = {};\r\n    \r\n    Object.entries(metadata).forEach(([key, value]) => {\r\n      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {\r\n        sanitized[key] = '***REDACTED***';\r\n      } else {\r\n        sanitized[key] = value;\r\n      }\r\n    });\r\n    \r\n    return sanitized;\r\n  }\r\n\r\n  /**\r\n   * Check if entry should be audited based on configuration\r\n   */\r\n  static shouldAudit(entry: Partial<AuditEntry>, config: AuditConfig): boolean {\r\n    if (!config.enabled) {\r\n      return false;\r\n    }\r\n\r\n    if (entry.action && !config.auditedActions.includes(entry.action)) {\r\n      return false;\r\n    }\r\n\r\n    if (entry.category && !config.auditedCategories.includes(entry.category)) {\r\n      return false;\r\n    }\r\n\r\n    if (entry.severity && AuditUtils.getSeverityLevel(entry.severity) < AuditUtils.getSeverityLevel(config.minimumSeverity)) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Get numeric severity level for comparison\r\n   */\r\n  private static getSeverityLevel(severity: AuditSeverity): number {\r\n    switch (severity) {\r\n      case AuditSeverity.LOW:\r\n        return 1;\r\n      case AuditSeverity.MEDIUM:\r\n        return 2;\r\n      case AuditSeverity.HIGH:\r\n        return 3;\r\n      case AuditSeverity.CRITICAL:\r\n        return 4;\r\n      default:\r\n        return 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Format audit entry for display\r\n   */\r\n  static formatEntry(entry: AuditEntry): string {\r\n    const timestamp = entry.timestamp.toISOString();\r\n    const user = entry.user?.username || entry.user?.userId || 'system';\r\n    const resource = entry.resource ? `${entry.resource.resourceType}:${entry.resource.resourceId}` : 'N/A';\r\n    \r\n    return `[${timestamp}] ${user} ${entry.action} ${resource} - ${entry.result} (${entry.severity})`;\r\n  }\r\n\r\n  /**\r\n   * Create audit query from parameters\r\n   */\r\n  static createQuery(params: AuditQueryParams): Record<string, any> {\r\n    const query: Record<string, any> = {};\r\n\r\n    if (params.startDate) {\r\n      query.timestamp = { $gte: params.startDate };\r\n    }\r\n\r\n    if (params.endDate) {\r\n      query.timestamp = { ...query.timestamp, $lte: params.endDate };\r\n    }\r\n\r\n    if (params.userId) {\r\n      query['user.userId'] = params.userId;\r\n    }\r\n\r\n    if (params.actions && params.actions.length > 0) {\r\n      query.action = { $in: params.actions };\r\n    }\r\n\r\n    if (params.results && params.results.length > 0) {\r\n      query.result = { $in: params.results };\r\n    }\r\n\r\n    if (params.severities && params.severities.length > 0) {\r\n      query.severity = { $in: params.severities };\r\n    }\r\n\r\n    if (params.categories && params.categories.length > 0) {\r\n      query.category = { $in: params.categories };\r\n    }\r\n\r\n    if (params.resourceTypes && params.resourceTypes.length > 0) {\r\n      query['resource.resourceType'] = { $in: params.resourceTypes };\r\n    }\r\n\r\n    if (params.resourceIds && params.resourceIds.length > 0) {\r\n      query['resource.resourceId'] = { $in: params.resourceIds };\r\n    }\r\n\r\n    if (params.search) {\r\n      query.$or = [\r\n        { description: { $regex: params.search, $options: 'i' } },\r\n        { message: { $regex: params.search, $options: 'i' } },\r\n      ];\r\n    }\r\n\r\n    if (params.minRiskScore !== undefined || params.maxRiskScore !== undefined) {\r\n      query.riskScore = {};\r\n      if (params.minRiskScore !== undefined) {\r\n        query.riskScore.$gte = params.minRiskScore;\r\n      }\r\n      if (params.maxRiskScore !== undefined) {\r\n        query.riskScore.$lte = params.maxRiskScore;\r\n      }\r\n    }\r\n\r\n    if (params.complianceTags && params.complianceTags.length > 0) {\r\n      query.complianceTags = { $in: params.complianceTags };\r\n    }\r\n\r\n    if (params.tenantId) {\r\n      query['system.tenantId'] = params.tenantId;\r\n    }\r\n\r\n    if (params.applications && params.applications.length > 0) {\r\n      query['system.application'] = { $in: params.applications };\r\n    }\r\n\r\n    if (params.environments && params.environments.length > 0) {\r\n      query['system.environment'] = { $in: params.environments };\r\n    }\r\n\r\n    return query;\r\n  }\r\n}"], "version": 3}