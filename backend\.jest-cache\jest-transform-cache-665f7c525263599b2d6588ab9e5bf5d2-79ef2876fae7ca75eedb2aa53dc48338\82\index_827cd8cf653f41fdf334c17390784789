c1c5c7cbb4272795c477ee8c83b45652
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateConfig = exports.ConfigValidationService = exports.OnConfigChange = exports.ConfigChangeDetectorService = exports.validateEnvironmentOrThrow = exports.validateEnvironmentConfig = exports.EnvironmentValidator = exports.SentinelConfigurationService = exports.sentinelConfiguration = exports.monitoringConfig = exports.securityConfig = exports.loggingConfig = exports.redisConfig = exports.aiConfig = exports.OAuthConfigHelper = exports.OAuthConfig = exports.oauthConfig = exports.JwtConfigHelper = exports.JwtConfig = exports.jwtConfig = exports.authConfig = exports.DatabaseConfigurationService = exports.databaseConfig = void 0;
var database_config_1 = require("./database.config");
Object.defineProperty(exports, "databaseConfig", { enumerable: true, get: function () { return database_config_1.databaseConfig; } });
Object.defineProperty(exports, "DatabaseConfigurationService", { enumerable: true, get: function () { return database_config_1.DatabaseConfigurationService; } });
var auth_config_1 = require("./auth.config");
Object.defineProperty(exports, "authConfig", { enumerable: true, get: function () { return auth_config_1.authConfig; } });
var jwt_config_1 = require("./jwt.config");
Object.defineProperty(exports, "jwtConfig", { enumerable: true, get: function () { return jwt_config_1.jwtConfig; } });
Object.defineProperty(exports, "JwtConfig", { enumerable: true, get: function () { return jwt_config_1.JwtConfig; } });
Object.defineProperty(exports, "JwtConfigHelper", { enumerable: true, get: function () { return jwt_config_1.JwtConfigHelper; } });
var oauth_config_1 = require("./oauth.config");
Object.defineProperty(exports, "oauthConfig", { enumerable: true, get: function () { return oauth_config_1.oauthConfig; } });
Object.defineProperty(exports, "OAuthConfig", { enumerable: true, get: function () { return oauth_config_1.OAuthConfig; } });
Object.defineProperty(exports, "OAuthConfigHelper", { enumerable: true, get: function () { return oauth_config_1.OAuthConfigHelper; } });
var ai_config_1 = require("./ai.config");
Object.defineProperty(exports, "aiConfig", { enumerable: true, get: function () { return ai_config_1.aiConfig; } });
var redis_config_1 = require("./redis.config");
Object.defineProperty(exports, "redisConfig", { enumerable: true, get: function () { return redis_config_1.redisConfig; } });
var logging_config_1 = require("./logging.config");
Object.defineProperty(exports, "loggingConfig", { enumerable: true, get: function () { return logging_config_1.loggingConfig; } });
var security_config_1 = require("./security.config");
Object.defineProperty(exports, "securityConfig", { enumerable: true, get: function () { return security_config_1.securityConfig; } });
var monitoring_config_1 = require("./monitoring.config");
Object.defineProperty(exports, "monitoringConfig", { enumerable: true, get: function () { return monitoring_config_1.monitoringConfig; } });
// New configuration services and utilities
var configuration_1 = require("./configuration");
Object.defineProperty(exports, "sentinelConfiguration", { enumerable: true, get: function () { return __importDefault(configuration_1).default; } });
Object.defineProperty(exports, "SentinelConfigurationService", { enumerable: true, get: function () { return configuration_1.SentinelConfigurationService; } });
var environment_validator_1 = require("./environment.validator");
Object.defineProperty(exports, "EnvironmentValidator", { enumerable: true, get: function () { return environment_validator_1.EnvironmentValidator; } });
Object.defineProperty(exports, "validateEnvironmentConfig", { enumerable: true, get: function () { return environment_validator_1.validateEnvironmentConfig; } });
Object.defineProperty(exports, "validateEnvironmentOrThrow", { enumerable: true, get: function () { return environment_validator_1.validateEnvironmentOrThrow; } });
var config_change_detector_service_1 = require("./config-change-detector.service");
Object.defineProperty(exports, "ConfigChangeDetectorService", { enumerable: true, get: function () { return config_change_detector_service_1.ConfigChangeDetectorService; } });
Object.defineProperty(exports, "OnConfigChange", { enumerable: true, get: function () { return config_change_detector_service_1.OnConfigChange; } });
var config_validator_1 = require("./validators/config.validator");
Object.defineProperty(exports, "ConfigValidationService", { enumerable: true, get: function () { return config_validator_1.ConfigValidationService; } });
Object.defineProperty(exports, "validateConfig", { enumerable: true, get: function () { return config_validator_1.validateConfig; } });
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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