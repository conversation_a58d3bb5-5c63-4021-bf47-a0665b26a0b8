{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai-ml\\application\\services\\vulnerability-analysis.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAqC;AACrC,uCAA2C;AAC3C,+BAA6B;AAC7B,mFAAwE;AACxE,+EAAqE;AACrE,iGAAsF;AACtF,2GAA8F;AAC9F,sFAAkF;AAClF,0FAAsF;AAEtF;;;GAGG;AAEI,IAAM,4BAA4B,oCAAlC,MAAM,4BAA4B;IAGvC,YAEE,qBAA+D,EAE/D,oBAA6D,EAE7D,qBAAsE,EAEtE,aAAqC,EACpB,iBAAoC,EACpC,aAA4B,EAC5B,YAA0B;QAT1B,0BAAqB,GAArB,qBAAqB,CAAyB;QAE9C,yBAAoB,GAApB,oBAAoB,CAAwB;QAE5C,0BAAqB,GAArB,qBAAqB,CAAgC;QAErD,kBAAa,GAAb,aAAa,CAAO;QACpB,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;QAb5B,WAAM,GAAG,IAAI,eAAM,CAAC,8BAA4B,CAAC,IAAI,CAAC,CAAC;IAcrE,CAAC;IAEJ;;;;;OAKG;IACH,KAAK,CAAC,eAAe,CACnB,iBAQC,EACD,MAAc;QAEd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE;gBAC5D,KAAK,EAAE,iBAAiB,CAAC,KAAK;gBAC9B,MAAM;aACP,CAAC,CAAC;YAEH,+CAA+C;YAC/C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,CAAC;YAElE,sBAAsB;YACtB,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;gBAC5C,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,qCAAqC,iBAAiB,CAAC,KAAK,IAAI,SAAS,EAAE;gBAClF,WAAW,EAAE,8CAA8C;gBAC3D,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC;gBACnD,SAAS,EAAE,iBAAiB;gBAC5B,oBAAoB,EAAE,KAAK,CAAC,EAAE;gBAC9B,SAAS,EAAE,MAAM;gBACjB,aAAa,EAAE;oBACb,OAAO,EAAE,MAAM,EAAE,YAAY;oBAC7B,OAAO,EAAE,CAAC;oBACV,mBAAmB,EAAE,GAAG;iBACzB;aACF,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE5D,2BAA2B;YAC3B,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,gCAAgC,EAAE;gBAC7D,KAAK,EAAE,QAAQ,CAAC,EAAE;gBAClB,iBAAiB;gBACjB,OAAO,EAAE,KAAK,CAAC,EAAE;aAClB,EAAE;gBACD,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC;gBAC7C,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,aAAa;gBACtB,KAAK,EAAE,CAAC;aACT,CAAC,CAAC;YAEH,kBAAkB;YAClB,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,cAAc,EACd,QAAQ,CAAC,EAAE,EACX;gBACE,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,iBAAiB,CAAC,KAAK;gBAC9B,OAAO,EAAE,KAAK,CAAC,EAAE;aAClB,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6CAA6C,EAAE;gBAC7D,KAAK,EAAE,QAAQ,CAAC,EAAE;gBAClB,KAAK,EAAE,iBAAiB,CAAC,KAAK;gBAC9B,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sDAAsD,EAAE;gBACxE,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,iBAAiB,CAAC,KAAK;gBAC9B,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,yBAAyB,CAC7B,iBAWC,EACD,MAAc;QAEd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;gBACzD,KAAK,EAAE,iBAAiB,CAAC,KAAK;gBAC9B,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;YAE9D,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;gBAC5C,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,kCAAkC,iBAAiB,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC/E,WAAW,EAAE,2CAA2C;gBACxD,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC;gBACnD,SAAS,EAAE,iBAAiB;gBAC5B,oBAAoB,EAAE,KAAK,CAAC,EAAE;gBAC9B,SAAS,EAAE,MAAM;gBACjB,aAAa,EAAE;oBACb,OAAO,EAAE,MAAM,EAAE,YAAY;oBAC7B,OAAO,EAAE,CAAC;oBACV,mBAAmB,EAAE,GAAG;iBACzB;aACF,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE5D,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,6BAA6B,EAAE;gBAC1D,KAAK,EAAE,QAAQ,CAAC,EAAE;gBAClB,iBAAiB;gBACjB,OAAO,EAAE,KAAK,CAAC,EAAE;aAClB,EAAE;gBACD,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC;gBAC7C,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,aAAa;aACvB,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,cAAc,EACd,QAAQ,CAAC,EAAE,EACX;gBACE,IAAI,EAAE,8BAA8B;gBACpC,KAAK,EAAE,iBAAiB,CAAC,KAAK;gBAC9B,OAAO,EAAE,KAAK,CAAC,EAAE;aAClB,CACF,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAmD,EAAE;gBACrE,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,iBAAiB,CAAC,KAAK;gBAC9B,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,kCAAkC,CACtC,iBAQC,EACD,kBAMC,EACD,MAAc;QAEd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,EAAE;gBAClE,KAAK,EAAE,iBAAiB,CAAC,KAAK;gBAC9B,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAE1D,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;gBAC5C,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,iCAAiC,iBAAiB,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC9E,WAAW,EAAE,wDAAwD;gBACrE,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC;gBACnD,SAAS,EAAE;oBACT,aAAa,EAAE,iBAAiB;oBAChC,WAAW,EAAE,kBAAkB;iBAChC;gBACD,oBAAoB,EAAE,KAAK,CAAC,EAAE;gBAC9B,SAAS,EAAE,MAAM;gBACjB,aAAa,EAAE;oBACb,OAAO,EAAE,MAAM,EAAE,YAAY;oBAC7B,OAAO,EAAE,CAAC;oBACV,mBAAmB,EAAE,GAAG;iBACzB;aACF,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE5D,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,sCAAsC,EAAE;gBACnE,KAAK,EAAE,QAAQ,CAAC,EAAE;gBAClB,iBAAiB;gBACjB,kBAAkB;gBAClB,OAAO,EAAE,KAAK,CAAC,EAAE;aAClB,EAAE;gBACD,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC;gBAC7C,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,aAAa;aACvB,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,cAAc,EACd,QAAQ,CAAC,EAAE,EACX;gBACE,IAAI,EAAE,6BAA6B;gBACnC,KAAK,EAAE,iBAAiB,CAAC,KAAK;gBAC9B,OAAO,EAAE,KAAK,CAAC,EAAE;aAClB,CACF,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iDAAiD,EAAE;gBACnE,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,iBAAiB,CAAC,KAAK;gBAC9B,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,YAAY,CAChB,eAME,EACF,YAAgE,EAChE,MAAc;QAEd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;gBACzD,KAAK,EAAE,eAAe,CAAC,MAAM;gBAC7B,YAAY;gBACZ,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;YAC7D,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAEpD,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;gBAC5C,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,SAAS,YAAY,eAAe,eAAe,CAAC,MAAM,kBAAkB;gBACnF,WAAW,EAAE,oBAAoB,YAAY,WAAW;gBACxD,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE;oBACT,eAAe;oBACf,YAAY;oBACZ,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC;iBAChD;gBACD,oBAAoB,EAAE,KAAK,CAAC,EAAE;gBAC9B,SAAS,EAAE,MAAM;gBACjB,aAAa,EAAE;oBACb,OAAO,EAAE,MAAM,EAAE,aAAa;oBAC9B,OAAO,EAAE,CAAC;oBACV,SAAS,EAAE,EAAE;oBACb,mBAAmB,EAAE,GAAG;iBACzB;gBACD,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,EAAE,CAAC;aACpD,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE5D,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,8BAA8B,EAAE;gBAC3D,KAAK,EAAE,QAAQ,CAAC,EAAE;gBAClB,eAAe;gBACf,YAAY;gBACZ,OAAO,EAAE,KAAK,CAAC,EAAE;aAClB,EAAE;gBACD,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC;gBAC7C,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,aAAa;aACvB,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,cAAc,EACd,QAAQ,CAAC,EAAE,EACX;gBACE,IAAI,EAAE,8BAA8B;gBACpC,YAAY;gBACZ,KAAK,EAAE,eAAe,CAAC,MAAM;gBAC7B,OAAO,EAAE,KAAK,CAAC,EAAE;aAClB,CACF,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAmD,EAAE;gBACrE,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,eAAe,CAAC,MAAM;gBAC7B,YAAY;gBACZ,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,cAAc,CAAC,KAAa;QAChC,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE;YACpB,SAAS,EAAE,CAAC,oBAAoB,CAAC;SAClC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,iBAAiB,CAAC,KAAa;QACnC,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAC1C,KAAK,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE;YAC/B,SAAS,EAAE,CAAC,oBAAoB,CAAC;YACjC,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,iBAAiB,CAAC,KAAa,EAAE,MAAc;QACnD,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC7C,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAED,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACjD,CAAC;YAED,GAAG,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;YACzC,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE3C,qCAAqC;YACrC,IAAI,GAAG,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC5B,yDAAyD;YAC3D,CAAC;YAED,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,cAAc,EACd,KAAK,EACL,EAAE,MAAM,EAAE,cAAc,EAAE,CAC3B,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBACjD,KAAK;gBACL,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,eAAe,CAAC,SAAiB;QAC7C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI;aAChB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,oCAAoC,SAAS,EAAE,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACK,iBAAiB,CAAC,iBAAsB;QAC9C,IAAI,iBAAiB,CAAC,SAAS,IAAI,GAAG,IAAI,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;YAC7E,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,IAAI,iBAAiB,CAAC,SAAS,IAAI,GAAG,EAAE,CAAC;YACvC,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,IAAI,iBAAiB,CAAC,SAAS,IAAI,GAAG,EAAE,CAAC;YACvC,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACK,gBAAgB,CAAC,QAAgB;QACvC,MAAM,UAAU,GAAG;YACjB,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,CAAC;YACP,MAAM,EAAE,CAAC;YACT,GAAG,EAAE,CAAC;SACP,CAAC;QACF,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;;;OAIG;IACK,uBAAuB,CAAC,YAAoB;QAClD,MAAM,OAAO,GAAG;YACd,QAAQ,EAAE,uBAAuB;YACjC,mBAAmB,EAAE,mBAAmB;YACxC,WAAW,EAAE,eAAe;SAC7B,CAAC;QACF,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,uBAAuB,CAAC;IAC1D,CAAC;CACF,CAAA;AAjfY,oEAA4B;uCAA5B,4BAA4B;IADxC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,iCAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,8BAAU,CAAC,CAAA;IAE5B,WAAA,IAAA,0BAAgB,EAAC,+CAAkB,CAAC,CAAA;IAEpC,WAAA,IAAA,kBAAW,EAAC,aAAa,CAAC,CAAA;yDALa,oBAAU,oBAAV,oBAAU,oDAEX,oBAAU,oBAAV,oBAAU,oDAET,oBAAU,oBAAV,oBAAU,oDAElB,YAAK,oBAAL,YAAK,oDACD,+CAAiB,oBAAjB,+CAAiB,oDACrB,8BAAa,oBAAb,8BAAa,oDACd,4BAAY,oBAAZ,4BAAY;GAdlC,4BAA4B,CAifxC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai-ml\\application\\services\\vulnerability-analysis.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { InjectQueue } from '@nestjs/bull';\r\nimport { Queue } from 'bull';\r\nimport { AnalysisJob } from '../../domain/entities/analysis-job.entity';\r\nimport { Prediction } from '../../domain/entities/prediction.entity';\r\nimport { ModelConfiguration } from '../../domain/entities/model-configuration.entity';\r\nimport { AIServiceProvider } from '../../infrastructure/services/ai-service-provider.service';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\n\r\n/**\r\n * Vulnerability Analysis service\r\n * Provides AI-powered vulnerability analysis capabilities\r\n */\r\n@Injectable()\r\nexport class VulnerabilityAnalysisService {\r\n  private readonly logger = new Logger(VulnerabilityAnalysisService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(AnalysisJob)\r\n    private readonly analysisJobRepository: Repository<AnalysisJob>,\r\n    @InjectRepository(Prediction)\r\n    private readonly predictionRepository: Repository<Prediction>,\r\n    @InjectRepository(ModelConfiguration)\r\n    private readonly modelConfigRepository: Repository<ModelConfiguration>,\r\n    @InjectQueue('ai-analysis')\r\n    private readonly analysisQueue: Queue,\r\n    private readonly aiServiceProvider: AIServiceProvider,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly auditService: AuditService,\r\n  ) {}\r\n\r\n  /**\r\n   * Analyze vulnerability severity using AI\r\n   * @param vulnerabilityData Vulnerability data to analyze\r\n   * @param userId User requesting the analysis\r\n   * @returns Analysis job\r\n   */\r\n  async analyzeSeverity(\r\n    vulnerabilityData: {\r\n      cveId?: string;\r\n      description: string;\r\n      cvssScore?: number;\r\n      affectedSoftware?: string;\r\n      exploitAvailable?: boolean;\r\n      patchAvailable?: boolean;\r\n      context?: Record<string, any>;\r\n    },\r\n    userId: string,\r\n  ): Promise<AnalysisJob> {\r\n    try {\r\n      this.logger.debug('Starting vulnerability severity analysis', {\r\n        cveId: vulnerabilityData.cveId,\r\n        userId,\r\n      });\r\n\r\n      // Get default model for vulnerability analysis\r\n      const model = await this.getDefaultModel('vulnerability_scanner');\r\n\r\n      // Create analysis job\r\n      const job = this.analysisJobRepository.create({\r\n        type: 'vulnerability_analysis',\r\n        title: `Vulnerability Severity Analysis - ${vulnerabilityData.cveId || 'Unknown'}`,\r\n        description: 'AI-powered vulnerability severity assessment',\r\n        status: 'pending',\r\n        priority: this.determinePriority(vulnerabilityData),\r\n        inputData: vulnerabilityData,\r\n        modelConfigurationId: model.id,\r\n        createdBy: userId,\r\n        configuration: {\r\n          timeout: 300000, // 5 minutes\r\n          retries: 2,\r\n          confidenceThreshold: 0.7,\r\n        },\r\n      });\r\n\r\n      const savedJob = await this.analysisJobRepository.save(job);\r\n\r\n      // Queue job for processing\r\n      await this.analysisQueue.add('analyze-vulnerability-severity', {\r\n        jobId: savedJob.id,\r\n        vulnerabilityData,\r\n        modelId: model.id,\r\n      }, {\r\n        priority: this.getQueuePriority(job.priority),\r\n        attempts: 3,\r\n        backoff: 'exponential',\r\n        delay: 0,\r\n      });\r\n\r\n      // Log audit event\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'create',\r\n        'analysis_job',\r\n        savedJob.id,\r\n        {\r\n          type: 'vulnerability_analysis',\r\n          cveId: vulnerabilityData.cveId,\r\n          modelId: model.id,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Vulnerability severity analysis job created', {\r\n        jobId: savedJob.id,\r\n        cveId: vulnerabilityData.cveId,\r\n        userId,\r\n      });\r\n\r\n      return savedJob;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create vulnerability severity analysis job', {\r\n        error: error.message,\r\n        cveId: vulnerabilityData.cveId,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Analyze exploit probability using AI\r\n   * @param vulnerabilityData Vulnerability data\r\n   * @param userId User requesting the analysis\r\n   * @returns Analysis job\r\n   */\r\n  async analyzeExploitProbability(\r\n    vulnerabilityData: {\r\n      cveId?: string;\r\n      description: string;\r\n      cvssScore?: number;\r\n      attackVector?: string;\r\n      attackComplexity?: string;\r\n      privilegesRequired?: string;\r\n      userInteraction?: string;\r\n      publicExploits?: boolean;\r\n      ageInDays?: number;\r\n      context?: Record<string, any>;\r\n    },\r\n    userId: string,\r\n  ): Promise<AnalysisJob> {\r\n    try {\r\n      this.logger.debug('Starting exploit probability analysis', {\r\n        cveId: vulnerabilityData.cveId,\r\n        userId,\r\n      });\r\n\r\n      const model = await this.getDefaultModel('threat_classifier');\r\n\r\n      const job = this.analysisJobRepository.create({\r\n        type: 'vulnerability_analysis',\r\n        title: `Exploit Probability Analysis - ${vulnerabilityData.cveId || 'Unknown'}`,\r\n        description: 'AI-powered exploit probability assessment',\r\n        status: 'pending',\r\n        priority: this.determinePriority(vulnerabilityData),\r\n        inputData: vulnerabilityData,\r\n        modelConfigurationId: model.id,\r\n        createdBy: userId,\r\n        configuration: {\r\n          timeout: 180000, // 3 minutes\r\n          retries: 2,\r\n          confidenceThreshold: 0.6,\r\n        },\r\n      });\r\n\r\n      const savedJob = await this.analysisJobRepository.save(job);\r\n\r\n      await this.analysisQueue.add('analyze-exploit-probability', {\r\n        jobId: savedJob.id,\r\n        vulnerabilityData,\r\n        modelId: model.id,\r\n      }, {\r\n        priority: this.getQueuePriority(job.priority),\r\n        attempts: 3,\r\n        backoff: 'exponential',\r\n      });\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'create',\r\n        'analysis_job',\r\n        savedJob.id,\r\n        {\r\n          type: 'exploit_probability_analysis',\r\n          cveId: vulnerabilityData.cveId,\r\n          modelId: model.id,\r\n        },\r\n      );\r\n\r\n      return savedJob;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create exploit probability analysis job', {\r\n        error: error.message,\r\n        cveId: vulnerabilityData.cveId,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate remediation recommendations using AI\r\n   * @param vulnerabilityData Vulnerability data\r\n   * @param environmentContext Environment context\r\n   * @param userId User requesting the analysis\r\n   * @returns Analysis job\r\n   */\r\n  async generateRemediationRecommendations(\r\n    vulnerabilityData: {\r\n      cveId?: string;\r\n      description: string;\r\n      affectedSoftware: string;\r\n      version?: string;\r\n      severity: string;\r\n      exploitAvailable?: boolean;\r\n      patchAvailable?: boolean;\r\n    },\r\n    environmentContext: {\r\n      operatingSystem?: string;\r\n      architecture?: string;\r\n      businessCriticality?: string;\r\n      maintenanceWindow?: string;\r\n      complianceRequirements?: string[];\r\n    },\r\n    userId: string,\r\n  ): Promise<AnalysisJob> {\r\n    try {\r\n      this.logger.debug('Starting remediation recommendation generation', {\r\n        cveId: vulnerabilityData.cveId,\r\n        userId,\r\n      });\r\n\r\n      const model = await this.getDefaultModel('nlp_processor');\r\n\r\n      const job = this.analysisJobRepository.create({\r\n        type: 'vulnerability_analysis',\r\n        title: `Remediation Recommendations - ${vulnerabilityData.cveId || 'Unknown'}`,\r\n        description: 'AI-generated vulnerability remediation recommendations',\r\n        status: 'pending',\r\n        priority: this.determinePriority(vulnerabilityData),\r\n        inputData: {\r\n          vulnerability: vulnerabilityData,\r\n          environment: environmentContext,\r\n        },\r\n        modelConfigurationId: model.id,\r\n        createdBy: userId,\r\n        configuration: {\r\n          timeout: 240000, // 4 minutes\r\n          retries: 2,\r\n          confidenceThreshold: 0.5,\r\n        },\r\n      });\r\n\r\n      const savedJob = await this.analysisJobRepository.save(job);\r\n\r\n      await this.analysisQueue.add('generate-remediation-recommendations', {\r\n        jobId: savedJob.id,\r\n        vulnerabilityData,\r\n        environmentContext,\r\n        modelId: model.id,\r\n      }, {\r\n        priority: this.getQueuePriority(job.priority),\r\n        attempts: 3,\r\n        backoff: 'exponential',\r\n      });\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'create',\r\n        'analysis_job',\r\n        savedJob.id,\r\n        {\r\n          type: 'remediation_recommendations',\r\n          cveId: vulnerabilityData.cveId,\r\n          modelId: model.id,\r\n        },\r\n      );\r\n\r\n      return savedJob;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create remediation recommendation job', {\r\n        error: error.message,\r\n        cveId: vulnerabilityData.cveId,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Perform batch vulnerability analysis\r\n   * @param vulnerabilities Array of vulnerabilities to analyze\r\n   * @param analysisType Type of analysis to perform\r\n   * @param userId User requesting the analysis\r\n   * @returns Analysis job\r\n   */\r\n  async batchAnalyze(\r\n    vulnerabilities: Array<{\r\n      id: string;\r\n      cveId?: string;\r\n      description: string;\r\n      cvssScore?: number;\r\n      [key: string]: any;\r\n    }>,\r\n    analysisType: 'severity' | 'exploit_probability' | 'remediation',\r\n    userId: string,\r\n  ): Promise<AnalysisJob> {\r\n    try {\r\n      this.logger.debug('Starting batch vulnerability analysis', {\r\n        count: vulnerabilities.length,\r\n        analysisType,\r\n        userId,\r\n      });\r\n\r\n      const modelType = this.getModelTypeForAnalysis(analysisType);\r\n      const model = await this.getDefaultModel(modelType);\r\n\r\n      const job = this.analysisJobRepository.create({\r\n        type: 'vulnerability_analysis',\r\n        title: `Batch ${analysisType} Analysis - ${vulnerabilities.length} vulnerabilities`,\r\n        description: `AI-powered batch ${analysisType} analysis`,\r\n        status: 'pending',\r\n        priority: 'normal',\r\n        inputData: {\r\n          vulnerabilities,\r\n          analysisType,\r\n          batchSize: Math.min(vulnerabilities.length, 10),\r\n        },\r\n        modelConfigurationId: model.id,\r\n        createdBy: userId,\r\n        configuration: {\r\n          timeout: 600000, // 10 minutes\r\n          retries: 1,\r\n          batchSize: 10,\r\n          confidenceThreshold: 0.6,\r\n        },\r\n        totalStages: Math.ceil(vulnerabilities.length / 10),\r\n      });\r\n\r\n      const savedJob = await this.analysisJobRepository.save(job);\r\n\r\n      await this.analysisQueue.add('batch-vulnerability-analysis', {\r\n        jobId: savedJob.id,\r\n        vulnerabilities,\r\n        analysisType,\r\n        modelId: model.id,\r\n      }, {\r\n        priority: this.getQueuePriority(job.priority),\r\n        attempts: 2,\r\n        backoff: 'exponential',\r\n      });\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'create',\r\n        'analysis_job',\r\n        savedJob.id,\r\n        {\r\n          type: 'batch_vulnerability_analysis',\r\n          analysisType,\r\n          count: vulnerabilities.length,\r\n          modelId: model.id,\r\n        },\r\n      );\r\n\r\n      return savedJob;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create batch vulnerability analysis job', {\r\n        error: error.message,\r\n        count: vulnerabilities.length,\r\n        analysisType,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get analysis job by ID\r\n   * @param jobId Job ID\r\n   * @returns Analysis job\r\n   */\r\n  async getAnalysisJob(jobId: string): Promise<AnalysisJob | null> {\r\n    return await this.analysisJobRepository.findOne({\r\n      where: { id: jobId },\r\n      relations: ['modelConfiguration'],\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get predictions for an analysis job\r\n   * @param jobId Job ID\r\n   * @returns Array of predictions\r\n   */\r\n  async getJobPredictions(jobId: string): Promise<Prediction[]> {\r\n    return await this.predictionRepository.find({\r\n      where: { analysisJobId: jobId },\r\n      relations: ['modelConfiguration'],\r\n      order: { createdAt: 'DESC' },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Cancel analysis job\r\n   * @param jobId Job ID\r\n   * @param userId User cancelling the job\r\n   */\r\n  async cancelAnalysisJob(jobId: string, userId: string): Promise<void> {\r\n    try {\r\n      const job = await this.getAnalysisJob(jobId);\r\n      if (!job) {\r\n        throw new Error('Analysis job not found');\r\n      }\r\n\r\n      if (job.isTerminal) {\r\n        throw new Error('Cannot cancel completed job');\r\n      }\r\n\r\n      job.markAsCancelled('Cancelled by user');\r\n      await this.analysisJobRepository.save(job);\r\n\r\n      // Remove from queue if still pending\r\n      if (job.status === 'queued') {\r\n        // Implementation would depend on Bull queue job tracking\r\n      }\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'cancel',\r\n        'analysis_job',\r\n        jobId,\r\n        { reason: 'user_request' },\r\n      );\r\n\r\n      this.logger.log('Analysis job cancelled', { jobId, userId });\r\n    } catch (error) {\r\n      this.logger.error('Failed to cancel analysis job', {\r\n        jobId,\r\n        userId,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get default model configuration for analysis type\r\n   * @param modelType Model type\r\n   * @returns Model configuration\r\n   */\r\n  private async getDefaultModel(modelType: string): Promise<ModelConfiguration> {\r\n    const model = await this.modelConfigRepository.findOne({\r\n      where: {\r\n        type: modelType,\r\n        status: 'active',\r\n        isDefault: true,\r\n      },\r\n    });\r\n\r\n    if (!model) {\r\n      throw new Error(`No default model found for type: ${modelType}`);\r\n    }\r\n\r\n    return model;\r\n  }\r\n\r\n  /**\r\n   * Determine job priority based on vulnerability data\r\n   * @param vulnerabilityData Vulnerability data\r\n   * @returns Priority level\r\n   */\r\n  private determinePriority(vulnerabilityData: any): 'low' | 'normal' | 'high' | 'urgent' {\r\n    if (vulnerabilityData.cvssScore >= 9.0 || vulnerabilityData.exploitAvailable) {\r\n      return 'urgent';\r\n    }\r\n    if (vulnerabilityData.cvssScore >= 7.0) {\r\n      return 'high';\r\n    }\r\n    if (vulnerabilityData.cvssScore >= 4.0) {\r\n      return 'normal';\r\n    }\r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get queue priority from job priority\r\n   * @param priority Job priority\r\n   * @returns Queue priority\r\n   */\r\n  private getQueuePriority(priority: string): number {\r\n    const priorities = {\r\n      urgent: 1,\r\n      high: 2,\r\n      normal: 3,\r\n      low: 4,\r\n    };\r\n    return priorities[priority] || 3;\r\n  }\r\n\r\n  /**\r\n   * Get model type for analysis type\r\n   * @param analysisType Analysis type\r\n   * @returns Model type\r\n   */\r\n  private getModelTypeForAnalysis(analysisType: string): string {\r\n    const mapping = {\r\n      severity: 'vulnerability_scanner',\r\n      exploit_probability: 'threat_classifier',\r\n      remediation: 'nlp_processor',\r\n    };\r\n    return mapping[analysisType] || 'vulnerability_scanner';\r\n  }\r\n}\r\n"], "version": 3}