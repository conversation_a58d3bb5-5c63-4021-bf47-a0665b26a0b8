08d4c9dbbf86e9d408e3e6722900858f
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var JwtAuthGuard_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtAuthGuard = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const core_1 = require("@nestjs/core");
// Metadata keys as constants
const PUBLIC_KEY = 'isPublic';
const OPTIONAL_AUTH_KEY = 'isOptionalAuth';
/**
 * JWT authentication guard with enhanced type safety and optimization
 * Protects routes by validating JWT tokens with support for public and optional endpoints
 */
let JwtAuthGuard = JwtAuthGuard_1 = class JwtAuthGuard extends (0, passport_1.AuthGuard)('jwt') {
    constructor(reflector) {
        super();
        this.reflector = reflector;
        this.logger = new common_1.Logger(JwtAuthGuard_1.name);
    }
    canActivate(context) {
        const isPublic = this.reflector.getAllAndOverride(PUBLIC_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (isPublic)
            return true;
        const isOptional = this.reflector.getAllAndOverride(OPTIONAL_AUTH_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        return isOptional ? this.handleOptionalAuth(context) : super.canActivate(context);
    }
    handleRequest(err, user, info, context) {
        const request = context.switchToHttp().getRequest();
        const requestInfo = this.getRequestInfo(request);
        this.logAuthAttempt(requestInfo, !!user, user);
        if (err || !user) {
            const errorMessage = err?.message || info?.message || 'Unauthorized';
            this.logAuthFailure(requestInfo, errorMessage);
            throw err || new common_1.UnauthorizedException(errorMessage);
        }
        this.logAuthSuccess(user, requestInfo);
        return this.enrichUserWithMetadata(user, requestInfo);
    }
    async handleOptionalAuth(context) {
        try {
            return !!(await super.canActivate(context));
        }
        catch (error) {
            const request = context.switchToHttp().getRequest();
            this.logger.debug('Optional auth failed, proceeding without authentication', {
                ...this.getRequestInfo(request),
                error: error instanceof Error ? error.message : 'Unknown error',
            });
            // Set user to null to indicate no authentication
            request.user = null;
            return true;
        }
    }
    getRequestInfo(request) {
        return {
            url: request.url,
            method: request.method,
            ipAddress: request.ip || 'unknown',
            userAgent: request.get('User-Agent') || 'unknown',
            hasToken: !!request.headers.authorization,
        };
    }
    logAuthAttempt(requestInfo, hasUser, user) {
        this.logger.debug('JWT authentication attempt', {
            ...requestInfo,
            userId: user?.id,
        });
    }
    logAuthFailure(requestInfo, errorMessage) {
        this.logger.warn('JWT authentication failed', {
            url: requestInfo.url,
            method: requestInfo.method,
            ipAddress: requestInfo.ipAddress,
            error: errorMessage,
            tokenPresent: requestInfo.hasToken,
        });
    }
    logAuthSuccess(user, requestInfo) {
        this.logger.debug('JWT authentication successful', {
            userId: user.id,
            email: user.email,
            url: requestInfo.url,
            method: requestInfo.method,
            ipAddress: requestInfo.ipAddress,
        });
    }
    enrichUserWithMetadata(user, requestInfo) {
        return {
            ...user,
            requestMetadata: {
                ipAddress: requestInfo.ipAddress,
                userAgent: requestInfo.userAgent,
                timestamp: new Date(),
                url: requestInfo.url,
                method: requestInfo.method,
            },
        };
    }
};
exports.JwtAuthGuard = JwtAuthGuard;
exports.JwtAuthGuard = JwtAuthGuard = JwtAuthGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object])
], JwtAuthGuard);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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