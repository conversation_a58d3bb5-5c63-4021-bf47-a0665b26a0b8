8aec48b9b96dc74b5f24031dfcc96694
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateConfig = exports.configValidationSchema = void 0;
const config_1 = require("@nestjs/config");
const Joi = __importStar(require("joi"));
// Validation pattern factory with strong typing
const createPatterns = () => ({
    secret: (minLength = 32) => Joi.string().min(minLength).required(),
    port: (defaultValue = 3000) => Joi.number().port().default(defaultValue),
    hostname: (defaultValue) => Joi.string().hostname().default(defaultValue),
    positiveInt: (min = 1, max = Number.MAX_SAFE_INTEGER, defaultValue) => Joi.number().integer().min(min).max(max).default(defaultValue),
    boolean: (defaultValue) => Joi.boolean().default(defaultValue),
    enum: (values, defaultValue) => defaultValue !== undefined
        ? Joi.string().valid(...values).default(defaultValue)
        : Joi.string().valid(...values),
    uri: (required = false) => required ? Joi.string().uri().required() : Joi.string().uri(),
    stringWithDefault: (defaultValue) => Joi.string().default(defaultValue),
    optionalString: () => Joi.string().optional(),
});
const patterns = createPatterns();
// Environment-specific validation schemas
const createEnvironmentSchemas = () => ({
    production: Joi.object({
        NODE_ENV: Joi.string().valid('production').required(),
        JWT_SECRET: patterns.secret(64),
        JWT_REFRESH_SECRET: patterns.secret(64),
        SESSION_SECRET: patterns.secret(64),
        DATABASE_SSL: patterns.boolean(true).valid(true).required(),
        LOG_LEVEL: patterns.enum(['error', 'warn', 'info']).required(),
        SECURITY_HSTS_ENABLED: patterns.boolean(true).valid(true).required(),
        SECURITY_CSP_ENABLED: patterns.boolean(true).valid(true).required(),
    }),
    development: Joi.object({
        NODE_ENV: Joi.string().valid('development').required(),
        DEV_ENABLE_PLAYGROUND: patterns.boolean(true),
        DEV_ENABLE_INTROSPECTION: patterns.boolean(true),
        DEV_ENABLE_DEBUG_LOGGING: patterns.boolean(true),
    }),
    test: Joi.object({
        NODE_ENV: Joi.string().valid('test').required(),
        DATABASE_NAME: Joi.string().pattern(/_test$/).required(),
        LOG_LEVEL: patterns.enum(['error', 'warn'], 'error'),
    }),
    staging: Joi.object({
        NODE_ENV: Joi.string().valid('staging').required(),
    }),
});
const envSchemas = createEnvironmentSchemas();
// Configuration schema factory with grouped sections
const createConfigSchema = () => {
    const coreSchema = {
        // Core Application
        NODE_ENV: patterns.enum(['development', 'production', 'test', 'staging'], 'development'),
        PORT: patterns.port(3000),
        APP_NAME: patterns.stringWithDefault('Sentinel Backend'),
        APP_VERSION: patterns.stringWithDefault('1.0.0'),
        API_PREFIX: patterns.stringWithDefault('api'),
        API_VERSION: patterns.stringWithDefault('v1'),
    };
    const databaseSchema = {
        // Database Configuration
        DATABASE_TYPE: patterns.enum(['postgres', 'mysql', 'mariadb'], 'postgres'),
        DATABASE_HOST: Joi.string().hostname().required(),
        DATABASE_PORT: patterns.port(5432),
        DATABASE_USERNAME: Joi.string().required(),
        DATABASE_PASSWORD: Joi.string().required(),
        DATABASE_NAME: Joi.string().required(),
        DATABASE_SCHEMA: patterns.stringWithDefault('public'),
        DATABASE_SSL: patterns.boolean(false),
        DATABASE_LOGGING: patterns.boolean(true),
        DATABASE_SYNCHRONIZE: patterns.boolean(false),
        DATABASE_MIGRATIONS_RUN: patterns.boolean(true),
        DATABASE_MAX_CONNECTIONS: patterns.positiveInt(1, 1000, 100),
        DATABASE_CONNECTION_TIMEOUT: patterns.positiveInt(1000, undefined, 60000),
    };
    const redisSchema = {
        // Redis Configuration
        REDIS_HOST: patterns.hostname('localhost'),
        REDIS_PORT: patterns.port(6379),
        REDIS_PASSWORD: Joi.string().allow('').optional(),
        REDIS_DB: patterns.positiveInt(0, 15, 0),
        REDIS_TTL: patterns.positiveInt(1, undefined, 3600),
        REDIS_MAX_RETRIES: patterns.positiveInt(0, undefined, 3),
        REDIS_RETRY_DELAY: patterns.positiveInt(100, undefined, 1000),
    };
    const authSchema = {
        // Authentication
        JWT_SECRET: patterns.secret(),
        JWT_EXPIRES_IN: patterns.stringWithDefault('1h'),
        JWT_REFRESH_SECRET: patterns.secret(),
        JWT_REFRESH_EXPIRES_IN: patterns.stringWithDefault('7d'),
        BCRYPT_ROUNDS: patterns.positiveInt(8, 15, 12),
        SESSION_SECRET: patterns.secret(),
    };
    const securitySchema = {
        // Rate Limiting
        RATE_LIMIT_TTL: patterns.positiveInt(1, undefined, 60),
        RATE_LIMIT_LIMIT: patterns.positiveInt(1, undefined, 100),
        RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS: patterns.boolean(false),
        // CORS
        CORS_ORIGIN: patterns.stringWithDefault('http://localhost:3001'),
        CORS_METHODS: patterns.stringWithDefault('GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS'),
        CORS_CREDENTIALS: patterns.boolean(true),
        // Security Headers
        SECURITY_HSTS_ENABLED: patterns.boolean(false),
        SECURITY_HSTS_MAX_AGE: patterns.positiveInt(0, undefined, 31536000),
        SECURITY_CSP_ENABLED: patterns.boolean(false),
        SECURITY_FRAME_OPTIONS: patterns.enum(['DENY', 'SAMEORIGIN', 'ALLOW-FROM'], 'DENY'),
        SECURITY_CONTENT_TYPE_OPTIONS: patterns.boolean(true),
    };
    const loggingSchema = {
        // Logging Configuration
        LOG_LEVEL: patterns.enum(['error', 'warn', 'info', 'http', 'verbose', 'debug', 'silly'], 'info'),
        LOG_FORMAT: patterns.enum(['json', 'simple', 'combined'], 'json'),
        LOG_FILE_ENABLED: patterns.boolean(false),
        LOG_FILE_PATH: patterns.stringWithDefault('logs/app.log'),
        LOG_FILE_MAX_SIZE: patterns.stringWithDefault('10m'),
        LOG_FILE_MAX_FILES: patterns.positiveInt(1, undefined, 5),
        LOG_CONSOLE_ENABLED: patterns.boolean(true),
        LOG_AUDIT_ENABLED: patterns.boolean(false),
        LOG_AUDIT_PATH: patterns.stringWithDefault('logs/audit.log'),
    };
    const healthSchema = {
        // Health Check
        HEALTH_CHECK_ENABLED: patterns.boolean(true),
        HEALTH_CHECK_DATABASE_ENABLED: patterns.boolean(true),
        HEALTH_CHECK_REDIS_ENABLED: patterns.boolean(true),
        HEALTH_CHECK_MEMORY_HEAP_THRESHOLD: patterns.positiveInt(50, undefined, 150),
        HEALTH_CHECK_MEMORY_RSS_THRESHOLD: patterns.positiveInt(50, undefined, 150),
    };
    const aiServiceSchema = {
        // AI Service Configuration
        AI_SERVICE_ENABLED: patterns.boolean(false),
        AI_SERVICE_URL: patterns.uri().when('AI_SERVICE_ENABLED', {
            is: true, then: Joi.required(), otherwise: Joi.optional(),
        }),
        AI_SERVICE_API_KEY: Joi.string().when('AI_SERVICE_ENABLED', {
            is: true, then: Joi.required(), otherwise: Joi.optional(),
        }),
        AI_SERVICE_TIMEOUT: patterns.positiveInt(1000, undefined, 30000),
        AI_SERVICE_RETRY_ATTEMPTS: patterns.positiveInt(0, 10, 3),
        AI_SERVICE_RETRY_DELAY: patterns.positiveInt(100, undefined, 1000),
        AI_SERVICE_CIRCUIT_BREAKER_ENABLED: patterns.boolean(false),
        AI_SERVICE_CIRCUIT_BREAKER_THRESHOLD: patterns.positiveInt(1, undefined, 5),
        AI_SERVICE_CIRCUIT_BREAKER_TIMEOUT: patterns.positiveInt(1000, undefined, 60000),
    };
    const externalServicesSchema = {
        // External Services
        THREAT_INTEL_API_KEY: patterns.optionalString(),
        VULNERABILITY_SCANNER_API_KEY: patterns.optionalString(),
        NOTIFICATION_SERVICE_URL: patterns.uri(),
        NOTIFICATION_SERVICE_API_KEY: patterns.optionalString(),
    };
    const monitoringSchema = {
        // Monitoring & Metrics
        METRICS_ENABLED: patterns.boolean(false),
        METRICS_PORT: patterns.port(9090),
        METRICS_PATH: patterns.stringWithDefault('/metrics'),
        TRACING_ENABLED: patterns.boolean(false),
        TRACING_SERVICE_NAME: patterns.stringWithDefault('sentinel-backend'),
        TRACING_JAEGER_ENDPOINT: patterns.uri(),
    };
    const fileUploadSchema = {
        // File Upload Configuration
        UPLOAD_MAX_FILE_SIZE: patterns.positiveInt(1024, undefined, 10485760),
        UPLOAD_ALLOWED_TYPES: patterns.stringWithDefault('application/json,text/csv,application/xml'),
        UPLOAD_DESTINATION: patterns.stringWithDefault('./uploads'),
        UPLOAD_TEMP_DESTINATION: patterns.stringWithDefault('./temp'),
    };
    const cacheSchema = {
        // Cache Configuration
        CACHE_TTL_DEFAULT: patterns.positiveInt(1, undefined, 300),
        CACHE_TTL_VULNERABILITIES: patterns.positiveInt(1, undefined, 1800),
        CACHE_TTL_THREATS: patterns.positiveInt(1, undefined, 900),
        CACHE_TTL_AI_RESULTS: patterns.positiveInt(1, undefined, 3600),
        CACHE_MAX_ITEMS: patterns.positiveInt(100, undefined, 10000),
    };
    const paginationSchema = {
        // Pagination
        PAGINATION_DEFAULT_LIMIT: patterns.positiveInt(1, 100, 20),
        PAGINATION_MAX_LIMIT: patterns.positiveInt(10, 1000, 100),
    };
    const validationSchema = {
        // Validation Configuration
        VALIDATION_WHITELIST: patterns.boolean(true),
        VALIDATION_FORBID_NON_WHITELISTED: patterns.boolean(true),
        VALIDATION_SKIP_MISSING_PROPERTIES: patterns.boolean(false),
        VALIDATION_TRANSFORM: patterns.boolean(true),
    };
    const developmentSchema = {
        // Development Configuration
        DEV_ENABLE_PLAYGROUND: patterns.boolean(false),
        DEV_ENABLE_INTROSPECTION: patterns.boolean(false),
        DEV_ENABLE_DEBUG_LOGGING: patterns.boolean(false),
        DEV_MOCK_EXTERNAL_SERVICES: patterns.boolean(false),
    };
    return Joi.object({
        ...coreSchema,
        ...databaseSchema,
        ...redisSchema,
        ...authSchema,
        ...securitySchema,
        ...loggingSchema,
        ...healthSchema,
        ...aiServiceSchema,
        ...externalServicesSchema,
        ...monitoringSchema,
        ...fileUploadSchema,
        ...cacheSchema,
        ...paginationSchema,
        ...validationSchema,
        ...developmentSchema,
    });
};
exports.configValidationSchema = createConfigSchema();
// Type-safe validation function
const validateConfig = (config) => {
    const { error, value } = exports.configValidationSchema.validate(config, {
        allowUnknown: true,
        abortEarly: false,
    });
    if (error) {
        const errorMessages = error.details.map(detail => detail.message);
        throw new Error(`Config validation failed: ${errorMessages.join(', ')}`);
    }
    // Apply environment-specific validation with type safety
    const environment = value.NODE_ENV;
    const envSchema = envSchemas[environment];
    if (envSchema) {
        const { error: envError } = envSchema.validate(value);
        if (envError) {
            const envErrorMessages = envError.details.map(detail => detail.message);
            throw new Error(`Environment-specific validation failed for ${environment}: ${envErrorMessages.join(', ')}`);
        }
    }
    return value;
};
exports.validateConfig = validateConfig;
// NestJS configuration factory with type safety
exports.default = (0, config_1.registerAs)('app', () => (0, exports.validateConfig)(process.env));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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