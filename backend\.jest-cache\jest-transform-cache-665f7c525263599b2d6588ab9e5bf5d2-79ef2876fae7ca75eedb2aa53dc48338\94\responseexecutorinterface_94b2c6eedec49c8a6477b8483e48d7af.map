{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\services\\response-executor.interface.ts", "mappings": ";;;AAyCA;;GAEG;AACH,IAAY,gBAMX;AAND,WAAY,gBAAgB;IAC1B,+BAAW,CAAA;IACX,qCAAiB,CAAA;IACjB,iCAAa,CAAA;IACb,yCAAqB,CAAA;IACrB,2CAAuB,CAAA;AACzB,CAAC,EANW,gBAAgB,gCAAhB,gBAAgB,QAM3B;AAED;;GAEG;AACH,IAAY,YAMX;AAND,WAAY,YAAY;IACtB,uCAAuB,CAAA;IACvB,iDAAiC,CAAA;IACjC,iCAAiB,CAAA;IACjB,yCAAyB,CAAA;IACzB,mCAAmB,CAAA;AACrB,CAAC,EANW,YAAY,4BAAZ,YAAY,QAMvB;AAkID;;GAEG;AACH,IAAY,eAMX;AAND,WAAY,eAAe;IACzB,gCAAa,CAAA;IACb,sCAAmB,CAAA;IACnB,sCAAmB,CAAA;IACnB,8CAA2B,CAAA;IAC3B,wCAAqB,CAAA;AACvB,CAAC,EANW,eAAe,+BAAf,eAAe,QAM1B;AAED;;GAEG;AACH,IAAY,WAMX;AAND,WAAY,WAAW;IACrB,4BAAa,CAAA;IACb,0BAAW,CAAA;IACX,gCAAiB,CAAA;IACjB,4BAAa,CAAA;IACb,oCAAqB,CAAA;AACvB,CAAC,EANW,WAAW,2BAAX,WAAW,QAMtB;AAwBD;;GAEG;AACH,IAAY,qBAWX;AAXD,WAAY,qBAAqB;IAC/B,0DAAiC,CAAA;IACjC,wDAA+B,CAAA;IAC/B,wDAA+B,CAAA;IAC/B,4CAAmB,CAAA;IACnB,8CAAqB,CAAA;IACrB,4CAAmB,CAAA;IACnB,kDAAyB,CAAA;IACzB,gDAAuB,CAAA;IACvB,8CAAqB,CAAA;IACrB,0CAAiB,CAAA;AACnB,CAAC,EAXW,qBAAqB,qCAArB,qBAAqB,QAWhC;AAwCD;;GAEG;AACH,IAAY,gBASX;AATD,WAAY,gBAAgB;IAC1B,mEAA+C,CAAA;IAC/C,uDAAmC,CAAA;IACnC,iDAA6B,CAAA;IAC7B,mDAA+B,CAAA;IAC/B,uDAAmC,CAAA;IACnC,iDAA6B,CAAA;IAC7B,6DAAyC,CAAA;IACzC,qCAAiB,CAAA;AACnB,CAAC,EATW,gBAAgB,gCAAhB,gBAAgB,QAS3B;AAED;;GAEG;AACH,IAAY,kBAMX;AAND,WAAY,kBAAkB;IAC5B,uCAAiB,CAAA;IACjB,2CAAqB,CAAA;IACrB,yCAAmB,CAAA;IACnB,mDAA6B,CAAA;IAC7B,mDAA6B,CAAA;AAC/B,CAAC,EANW,kBAAkB,kCAAlB,kBAAkB,QAM7B;AAsCD;;GAEG;AACH,IAAY,cASX;AATD,WAAY,cAAc;IACxB,uCAAqB,CAAA;IACrB,yCAAuB,CAAA;IACvB,6CAA2B,CAAA;IAC3B,+CAA6B,CAAA;IAC7B,iDAA+B,CAAA;IAC/B,2CAAyB,CAAA;IACzB,uCAAqB,CAAA;IACrB,2CAAyB,CAAA;AAC3B,CAAC,EATW,cAAc,8BAAd,cAAc,QASzB;AAsBD;;GAEG;AACH,IAAY,aAWX;AAXD,WAAY,aAAa;IACvB,kCAAiB,CAAA;IACjB,kCAAiB,CAAA;IACjB,oCAAmB,CAAA;IACnB,gCAAe,CAAA;IACf,kCAAiB,CAAA;IACjB,0CAAyB,CAAA;IACzB,4BAAW,CAAA;IACX,gCAAe,CAAA;IACf,sCAAqB,CAAA;IACrB,8BAAa,CAAA;AACf,CAAC,EAXW,aAAa,6BAAb,aAAa,QAWxB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\services\\response-executor.interface.ts"], "sourcesContent": ["import { Event } from '../../entities/event.entity';\r\nimport { NormalizedEvent } from '../../entities/normalized-event.entity';\r\nimport { EnrichedEvent } from '../../entities/enriched-event.entity';\r\nimport { CorrelatedEvent } from '../../entities/correlated-event.entity';\r\nimport { ResponseAction } from '../../entities/response-action.entity';\r\nimport { ActionType } from '../../enums/action-type.enum';\r\nimport { ActionStatus } from '../../enums/action-status.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { DetectedThreat } from './threat-detector.interface';\r\nimport { DiscoveredVulnerability } from './vulnerability-scanner.interface';\r\n\r\n/**\r\n * Response Execution Context\r\n */\r\nexport interface ResponseContext {\r\n  /** Detected threats */\r\n  threats: DetectedThreat[];\r\n  /** Discovered vulnerabilities */\r\n  vulnerabilities: DiscoveredVulnerability[];\r\n  /** Source event */\r\n  event: Event | NormalizedEvent | EnrichedEvent | CorrelatedEvent;\r\n  /** Response priority */\r\n  priority: ResponsePriority;\r\n  /** Response mode */\r\n  mode: ResponseMode;\r\n  /** User context */\r\n  userContext?: {\r\n    userId: string;\r\n    tenantId: string;\r\n    permissions: string[];\r\n  };\r\n  /** Response constraints */\r\n  constraints: ResponseConstraints;\r\n  /** Response metadata */\r\n  metadata?: Record<string, any>;\r\n  /** Correlation ID for tracking */\r\n  correlationId?: string;\r\n  /** Response deadline */\r\n  deadline?: Date;\r\n}\r\n\r\n/**\r\n * Response Priority Enum\r\n */\r\nexport enum ResponsePriority {\r\n  LOW = 'LOW',\r\n  MEDIUM = 'MEDIUM',\r\n  HIGH = 'HIGH',\r\n  CRITICAL = 'CRITICAL',\r\n  EMERGENCY = 'EMERGENCY',\r\n}\r\n\r\n/**\r\n * Response Mode Enum\r\n */\r\nexport enum ResponseMode {\r\n  AUTOMATIC = 'AUTOMATIC',\r\n  SEMI_AUTOMATIC = 'SEMI_AUTOMATIC',\r\n  MANUAL = 'MANUAL',\r\n  SIMULATION = 'SIMULATION',\r\n  DRY_RUN = 'DRY_RUN',\r\n}\r\n\r\n/**\r\n * Response Constraints Interface\r\n */\r\nexport interface ResponseConstraints {\r\n  /** Maximum execution time in milliseconds */\r\n  maxExecutionTimeMs?: number;\r\n  /** Maximum number of concurrent actions */\r\n  maxConcurrentActions?: number;\r\n  /** Allowed action types */\r\n  allowedActionTypes?: ActionType[];\r\n  /** Forbidden action types */\r\n  forbiddenActionTypes?: ActionType[];\r\n  /** Resource limits */\r\n  resourceLimits?: {\r\n    maxCpuUsage?: number;\r\n    maxMemoryUsage?: number;\r\n    maxNetworkBandwidth?: number;\r\n  };\r\n  /** Business hours constraints */\r\n  businessHoursOnly?: boolean;\r\n  /** Approval requirements */\r\n  requiresApproval?: boolean;\r\n  /** Rollback requirements */\r\n  requiresRollback?: boolean;\r\n}\r\n\r\n/**\r\n * Response Execution Result\r\n */\r\nexport interface ResponseResult {\r\n  /** Whether response execution was successful */\r\n  success: boolean;\r\n  /** Response execution ID */\r\n  executionId: string;\r\n  /** Number of actions executed successfully */\r\n  actionsExecuted: number;\r\n  /** Number of actions that failed */\r\n  actionsFailed: number;\r\n  /** Number of actions skipped */\r\n  actionsSkipped: number;\r\n  /** Total execution time in milliseconds */\r\n  executionTime: number;\r\n  /** Executed actions details */\r\n  executedActions: ExecutedActionResult[];\r\n  /** Response effectiveness score (0-100) */\r\n  effectivenessScore: number;\r\n  /** Response impact assessment */\r\n  impact: ResponseImpact;\r\n  /** Execution errors */\r\n  errors: ResponseError[];\r\n  /** Execution warnings */\r\n  warnings: string[];\r\n  /** Response recommendations */\r\n  recommendations: string[];\r\n  /** Response metadata */\r\n  metadata?: Record<string, any>;\r\n  /** Rollback information if applicable */\r\n  rollbackInfo?: RollbackInfo;\r\n}\r\n\r\n/**\r\n * Executed Action Result Interface\r\n */\r\nexport interface ExecutedActionResult {\r\n  /** Action identifier */\r\n  actionId: string;\r\n  /** Action type */\r\n  actionType: ActionType;\r\n  /** Action name */\r\n  actionName: string;\r\n  /** Execution status */\r\n  status: ActionStatus;\r\n  /** Execution start time */\r\n  startTime: Date;\r\n  /** Execution end time */\r\n  endTime?: Date;\r\n  /** Execution duration in milliseconds */\r\n  duration: number;\r\n  /** Action parameters used */\r\n  parameters: Record<string, any>;\r\n  /** Action result data */\r\n  result?: any;\r\n  /** Action error if failed */\r\n  error?: string;\r\n  /** Action progress (0-100) */\r\n  progress: number;\r\n  /** Action impact */\r\n  impact: ActionImpact;\r\n  /** Action metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Response Impact Interface\r\n */\r\nexport interface ResponseImpact {\r\n  /** Threat mitigation level */\r\n  threatMitigation: MitigationLevel;\r\n  /** Vulnerability remediation level */\r\n  vulnerabilityRemediation: MitigationLevel;\r\n  /** System availability impact */\r\n  systemAvailability: ImpactLevel;\r\n  /** Performance impact */\r\n  performanceImpact: ImpactLevel;\r\n  /** Security posture improvement */\r\n  securityImprovement: ImpactLevel;\r\n  /** Business continuity impact */\r\n  businessContinuity: ImpactLevel;\r\n  /** Compliance impact */\r\n  complianceImpact: ImpactLevel;\r\n}\r\n\r\n/**\r\n * Action Impact Interface\r\n */\r\nexport interface ActionImpact {\r\n  /** Impact on system resources */\r\n  resourceImpact: ImpactLevel;\r\n  /** Impact on network */\r\n  networkImpact: ImpactLevel;\r\n  /** Impact on users */\r\n  userImpact: ImpactLevel;\r\n  /** Impact on business operations */\r\n  businessImpact: ImpactLevel;\r\n  /** Security effectiveness */\r\n  securityEffectiveness: ImpactLevel;\r\n}\r\n\r\n/**\r\n * Mitigation Level Enum\r\n */\r\nexport enum MitigationLevel {\r\n  NONE = 'NONE',\r\n  MINIMAL = 'MINIMAL',\r\n  PARTIAL = 'PARTIAL',\r\n  SUBSTANTIAL = 'SUBSTANTIAL',\r\n  COMPLETE = 'COMPLETE',\r\n}\r\n\r\n/**\r\n * Impact Level Enum\r\n */\r\nexport enum ImpactLevel {\r\n  NONE = 'NONE',\r\n  LOW = 'LOW',\r\n  MEDIUM = 'MEDIUM',\r\n  HIGH = 'HIGH',\r\n  CRITICAL = 'CRITICAL',\r\n}\r\n\r\n/**\r\n * Response Error Interface\r\n */\r\nexport interface ResponseError {\r\n  /** Error code */\r\n  code: string;\r\n  /** Error message */\r\n  message: string;\r\n  /** Error category */\r\n  category: ResponseErrorCategory;\r\n  /** Error severity */\r\n  severity: EventSeverity;\r\n  /** Error timestamp */\r\n  timestamp: Date;\r\n  /** Error details */\r\n  details?: Record<string, any>;\r\n  /** Whether error is recoverable */\r\n  recoverable: boolean;\r\n  /** Suggested remediation */\r\n  remediation?: string;\r\n}\r\n\r\n/**\r\n * Response Error Category Enum\r\n */\r\nexport enum ResponseErrorCategory {\r\n  AUTHENTICATION = 'AUTHENTICATION',\r\n  AUTHORIZATION = 'AUTHORIZATION',\r\n  CONFIGURATION = 'CONFIGURATION',\r\n  NETWORK = 'NETWORK',\r\n  RESOURCE = 'RESOURCE',\r\n  TIMEOUT = 'TIMEOUT',\r\n  VALIDATION = 'VALIDATION',\r\n  EXECUTION = 'EXECUTION',\r\n  ROLLBACK = 'ROLLBACK',\r\n  SYSTEM = 'SYSTEM',\r\n}\r\n\r\n/**\r\n * Rollback Information Interface\r\n */\r\nexport interface RollbackInfo {\r\n  /** Whether rollback is available */\r\n  available: boolean;\r\n  /** Rollback plan */\r\n  plan: RollbackStep[];\r\n  /** Rollback complexity */\r\n  complexity: RollbackComplexity;\r\n  /** Estimated rollback time */\r\n  estimatedTimeMs: number;\r\n  /** Rollback risks */\r\n  risks: string[];\r\n  /** Rollback requirements */\r\n  requirements: string[];\r\n}\r\n\r\n/**\r\n * Rollback Step Interface\r\n */\r\nexport interface RollbackStep {\r\n  /** Step identifier */\r\n  id: string;\r\n  /** Step description */\r\n  description: string;\r\n  /** Step order */\r\n  order: number;\r\n  /** Step type */\r\n  type: RollbackStepType;\r\n  /** Step parameters */\r\n  parameters: Record<string, any>;\r\n  /** Step dependencies */\r\n  dependencies: string[];\r\n  /** Estimated step time */\r\n  estimatedTimeMs: number;\r\n}\r\n\r\n/**\r\n * Rollback Step Type Enum\r\n */\r\nexport enum RollbackStepType {\r\n  CONFIGURATION_RESTORE = 'CONFIGURATION_RESTORE',\r\n  SERVICE_RESTART = 'SERVICE_RESTART',\r\n  RULE_REMOVAL = 'RULE_REMOVAL',\r\n  POLICY_REVERT = 'POLICY_REVERT',\r\n  NETWORK_RESTORE = 'NETWORK_RESTORE',\r\n  DATA_RESTORE = 'DATA_RESTORE',\r\n  PERMISSION_RESTORE = 'PERMISSION_RESTORE',\r\n  CUSTOM = 'CUSTOM',\r\n}\r\n\r\n/**\r\n * Rollback Complexity Enum\r\n */\r\nexport enum RollbackComplexity {\r\n  SIMPLE = 'SIMPLE',\r\n  MODERATE = 'MODERATE',\r\n  COMPLEX = 'COMPLEX',\r\n  VERY_COMPLEX = 'VERY_COMPLEX',\r\n  IRREVERSIBLE = 'IRREVERSIBLE',\r\n}\r\n\r\n/**\r\n * Action Definition Interface\r\n */\r\nexport interface ActionDefinition {\r\n  /** Action identifier */\r\n  id: string;\r\n  /** Action type */\r\n  type: ActionType;\r\n  /** Action name */\r\n  name: string;\r\n  /** Action description */\r\n  description: string;\r\n  /** Action category */\r\n  category: ActionCategory;\r\n  /** Action parameters schema */\r\n  parameters: ActionParameterSchema[];\r\n  /** Action timeout in milliseconds */\r\n  timeoutMs: number;\r\n  /** Action retry configuration */\r\n  retryConfig: RetryConfig;\r\n  /** Action priority */\r\n  priority: number;\r\n  /** Action prerequisites */\r\n  prerequisites: string[];\r\n  /** Action side effects */\r\n  sideEffects: string[];\r\n  /** Action rollback support */\r\n  rollbackSupported: boolean;\r\n  /** Action approval required */\r\n  approvalRequired: boolean;\r\n  /** Action enabled status */\r\n  enabled: boolean;\r\n  /** Action metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Action Category Enum\r\n */\r\nexport enum ActionCategory {\r\n  BLOCKING = 'BLOCKING',\r\n  ISOLATION = 'ISOLATION',\r\n  REMEDIATION = 'REMEDIATION',\r\n  NOTIFICATION = 'NOTIFICATION',\r\n  INVESTIGATION = 'INVESTIGATION',\r\n  MONITORING = 'MONITORING',\r\n  RECOVERY = 'RECOVERY',\r\n  PREVENTION = 'PREVENTION',\r\n}\r\n\r\n/**\r\n * Action Parameter Schema Interface\r\n */\r\nexport interface ActionParameterSchema {\r\n  /** Parameter name */\r\n  name: string;\r\n  /** Parameter type */\r\n  type: ParameterType;\r\n  /** Parameter description */\r\n  description: string;\r\n  /** Whether parameter is required */\r\n  required: boolean;\r\n  /** Default parameter value */\r\n  defaultValue?: any;\r\n  /** Parameter validation rules */\r\n  validation?: ParameterValidation;\r\n  /** Parameter metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Parameter Type Enum\r\n */\r\nexport enum ParameterType {\r\n  STRING = 'STRING',\r\n  NUMBER = 'NUMBER',\r\n  BOOLEAN = 'BOOLEAN',\r\n  ARRAY = 'ARRAY',\r\n  OBJECT = 'OBJECT',\r\n  IP_ADDRESS = 'IP_ADDRESS',\r\n  URL = 'URL',\r\n  EMAIL = 'EMAIL',\r\n  DURATION = 'DURATION',\r\n  ENUM = 'ENUM',\r\n}\r\n\r\n/**\r\n * Parameter Validation Interface\r\n */\r\nexport interface ParameterValidation {\r\n  /** Minimum value/length */\r\n  min?: number;\r\n  /** Maximum value/length */\r\n  max?: number;\r\n  /** Regular expression pattern */\r\n  pattern?: string;\r\n  /** Allowed enum values */\r\n  enumValues?: string[];\r\n  /** Custom validation function name */\r\n  customValidator?: string;\r\n}\r\n\r\n/**\r\n * Retry Configuration Interface\r\n */\r\nexport interface RetryConfig {\r\n  /** Maximum retry attempts */\r\n  maxAttempts: number;\r\n  /** Initial delay in milliseconds */\r\n  initialDelayMs: number;\r\n  /** Delay multiplier for exponential backoff */\r\n  delayMultiplier: number;\r\n  /** Maximum delay in milliseconds */\r\n  maxDelayMs: number;\r\n  /** Retry on specific error types */\r\n  retryOnErrors: string[];\r\n  /** Stop retry on specific error types */\r\n  stopOnErrors: string[];\r\n}\r\n\r\n/**\r\n * Action Execution Status\r\n */\r\nexport interface ActionExecutionStatus {\r\n  /** Action execution ID */\r\n  executionId: string;\r\n  /** Action ID */\r\n  actionId: string;\r\n  /** Current status */\r\n  status: ActionStatus;\r\n  /** Execution progress (0-100) */\r\n  progress: number;\r\n  /** Start time */\r\n  startTime: Date;\r\n  /** End time if completed */\r\n  endTime?: Date;\r\n  /** Current step description */\r\n  currentStep?: string;\r\n  /** Execution result if completed */\r\n  result?: any;\r\n  /** Execution error if failed */\r\n  error?: string;\r\n  /** Estimated completion time */\r\n  estimatedCompletionTime?: Date;\r\n  /** Execution metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Response Execution Configuration\r\n */\r\nexport interface ResponseExecutionConfig {\r\n  /** Default response mode */\r\n  defaultMode: ResponseMode;\r\n  /** Default response priority */\r\n  defaultPriority: ResponsePriority;\r\n  /** Maximum concurrent executions */\r\n  maxConcurrentExecutions: number;\r\n  /** Default execution timeout */\r\n  defaultTimeoutMs: number;\r\n  /** Enable automatic rollback on failure */\r\n  autoRollbackOnFailure: boolean;\r\n  /** Require approval for high-impact actions */\r\n  requireApprovalForHighImpact: boolean;\r\n  /** Enable execution simulation */\r\n  enableSimulation: boolean;\r\n  /** Default retry configuration */\r\n  defaultRetryConfig: RetryConfig;\r\n  /** Execution logging level */\r\n  loggingLevel: 'minimal' | 'standard' | 'detailed' | 'verbose';\r\n}\r\n\r\n/**\r\n * Response Executor Context\r\n */\r\nexport interface ResponseExecutorContext {\r\n  /** Execution request ID */\r\n  requestId: string;\r\n  /** Execution configuration */\r\n  config: ResponseExecutionConfig;\r\n  /** User context */\r\n  userContext?: {\r\n    userId: string;\r\n    tenantId: string;\r\n    permissions: string[];\r\n  };\r\n  /** Execution metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Response Executor Interface\r\n * \r\n * Defines the contract for executing automated response actions.\r\n * Supports multiple execution modes and provides comprehensive action management.\r\n */\r\nexport interface ResponseExecutor {\r\n  /**\r\n   * Execute response actions for an event\r\n   * @param event - Event to respond to\r\n   * @param context - Response context\r\n   * @param executorContext - Executor context\r\n   * @returns Response execution result\r\n   */\r\n  executeResponse(\r\n    event: Event, \r\n    context: ResponseContext, \r\n    executorContext?: ResponseExecutorContext\r\n  ): Promise<ResponseResult>;\r\n\r\n  /**\r\n   * Execute response actions for a normalized event\r\n   * @param normalizedEvent - Normalized event to respond to\r\n   * @param context - Response context\r\n   * @param executorContext - Executor context\r\n   * @returns Response execution result\r\n   */\r\n  executeResponseForNormalizedEvent(\r\n    normalizedEvent: NormalizedEvent, \r\n    context: ResponseContext, \r\n    executorContext?: ResponseExecutorContext\r\n  ): Promise<ResponseResult>;\r\n\r\n  /**\r\n   * Execute response actions for an enriched event\r\n   * @param enrichedEvent - Enriched event to respond to\r\n   * @param context - Response context\r\n   * @param executorContext - Executor context\r\n   * @returns Response execution result\r\n   */\r\n  executeResponseForEnrichedEvent(\r\n    enrichedEvent: EnrichedEvent, \r\n    context: ResponseContext, \r\n    executorContext?: ResponseExecutorContext\r\n  ): Promise<ResponseResult>;\r\n\r\n  /**\r\n   * Execute response actions for a correlated event\r\n   * @param correlatedEvent - Correlated event to respond to\r\n   * @param context - Response context\r\n   * @param executorContext - Executor context\r\n   * @returns Response execution result\r\n   */\r\n  executeResponseForCorrelatedEvent(\r\n    correlatedEvent: CorrelatedEvent, \r\n    context: ResponseContext, \r\n    executorContext?: ResponseExecutorContext\r\n  ): Promise<ResponseResult>;\r\n\r\n  /**\r\n   * Execute a specific action\r\n   * @param actionType - Type of action to execute\r\n   * @param parameters - Action parameters\r\n   * @param executorContext - Executor context\r\n   * @returns Action execution result\r\n   */\r\n  executeAction(\r\n    actionType: ActionType, \r\n    parameters: Record<string, any>, \r\n    executorContext?: ResponseExecutorContext\r\n  ): Promise<ExecutedActionResult>;\r\n\r\n  /**\r\n   * Execute multiple actions in sequence\r\n   * @param actions - Actions to execute\r\n   * @param executorContext - Executor context\r\n   * @returns Array of action execution results\r\n   */\r\n  executeActions(\r\n    actions: Array<{ type: ActionType; parameters: Record<string, any> }>, \r\n    executorContext?: ResponseExecutorContext\r\n  ): Promise<ExecutedActionResult[]>;\r\n\r\n  /**\r\n   * Execute multiple actions in parallel\r\n   * @param actions - Actions to execute\r\n   * @param executorContext - Executor context\r\n   * @returns Array of action execution results\r\n   */\r\n  executeActionsParallel(\r\n    actions: Array<{ type: ActionType; parameters: Record<string, any> }>, \r\n    executorContext?: ResponseExecutorContext\r\n  ): Promise<ExecutedActionResult[]>;\r\n\r\n  /**\r\n   * Validate action parameters\r\n   * @param actionType - Type of action to validate\r\n   * @param parameters - Action parameters to validate\r\n   * @param executorContext - Executor context\r\n   * @returns Validation result\r\n   */\r\n  validateAction(\r\n    actionType: ActionType, \r\n    parameters: Record<string, any>, \r\n    executorContext?: ResponseExecutorContext\r\n  ): Promise<{\r\n    isValid: boolean;\r\n    errors: string[];\r\n    warnings: string[];\r\n    suggestions: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Simulate action execution without actually executing\r\n   * @param actionType - Type of action to simulate\r\n   * @param parameters - Action parameters\r\n   * @param executorContext - Executor context\r\n   * @returns Simulation result\r\n   */\r\n  simulateAction(\r\n    actionType: ActionType, \r\n    parameters: Record<string, any>, \r\n    executorContext?: ResponseExecutorContext\r\n  ): Promise<{\r\n    wouldSucceed: boolean;\r\n    estimatedDuration: number;\r\n    expectedImpact: ActionImpact;\r\n    potentialRisks: string[];\r\n    prerequisites: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Get available action definitions\r\n   * @param category - Optional action category filter\r\n   * @param executorContext - Executor context\r\n   * @returns Array of available action definitions\r\n   */\r\n  getAvailableActions(\r\n    category?: ActionCategory, \r\n    executorContext?: ResponseExecutorContext\r\n  ): Promise<ActionDefinition[]>;\r\n\r\n  /**\r\n   * Get action execution status\r\n   * @param executionId - Action execution ID\r\n   * @returns Action execution status\r\n   */\r\n  getActionStatus(executionId: string): Promise<ActionExecutionStatus>;\r\n\r\n  /**\r\n   * Cancel action execution\r\n   * @param executionId - Action execution ID\r\n   * @param reason - Cancellation reason\r\n   * @returns Cancellation result\r\n   */\r\n  cancelAction(executionId: string, reason?: string): Promise<{\r\n    cancelled: boolean;\r\n    reason: string;\r\n    timestamp: Date;\r\n  }>;\r\n\r\n  /**\r\n   * Rollback executed action\r\n   * @param executionId - Action execution ID\r\n   * @param executorContext - Executor context\r\n   * @returns Rollback result\r\n   */\r\n  rollbackAction(\r\n    executionId: string, \r\n    executorContext?: ResponseExecutorContext\r\n  ): Promise<{\r\n    success: boolean;\r\n    rollbackSteps: RollbackStep[];\r\n    duration: number;\r\n    errors: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Get rollback plan for action\r\n   * @param executionId - Action execution ID\r\n   * @returns Rollback plan\r\n   */\r\n  getRollbackPlan(executionId: string): Promise<RollbackInfo>;\r\n\r\n  /**\r\n   * Get response execution history\r\n   * @param eventId - Event ID to get history for\r\n   * @param limit - Maximum number of results\r\n   * @returns Response execution history\r\n   */\r\n  getExecutionHistory(eventId: string, limit?: number): Promise<ResponseResult[]>;\r\n\r\n  /**\r\n   * Get current response executor configuration\r\n   * @returns Current executor configuration\r\n   */\r\n  getExecutorConfig(): Promise<ResponseExecutionConfig>;\r\n\r\n  /**\r\n   * Update response executor configuration\r\n   * @param config - New executor configuration\r\n   * @returns Configuration update result\r\n   */\r\n  updateExecutorConfig(config: Partial<ResponseExecutionConfig>): Promise<{\r\n    updated: boolean;\r\n    config: ResponseExecutionConfig;\r\n    timestamp: Date;\r\n  }>;\r\n\r\n  /**\r\n   * Get response executor health status\r\n   * @returns Executor system health\r\n   */\r\n  getExecutorHealth(): Promise<{\r\n    status: 'healthy' | 'degraded' | 'unhealthy';\r\n    actionEngines: Record<string, {\r\n      status: 'healthy' | 'degraded' | 'unhealthy';\r\n      latency: number;\r\n      successRate: number;\r\n      lastExecution: Date;\r\n    }>;\r\n    executionQueue: {\r\n      pending: number;\r\n      running: number;\r\n      completed: number;\r\n      failed: number;\r\n    };\r\n    lastHealthCheck: Date;\r\n  }>;\r\n\r\n  /**\r\n   * Pause response execution\r\n   * @param reason - Reason for pausing\r\n   * @returns Pause confirmation\r\n   */\r\n  pauseExecution(reason?: string): Promise<{\r\n    paused: boolean;\r\n    reason: string;\r\n    timestamp: Date;\r\n  }>;\r\n\r\n  /**\r\n   * Resume response execution\r\n   * @param reason - Reason for resuming\r\n   * @returns Resume confirmation\r\n   */\r\n  resumeExecution(reason?: string): Promise<{\r\n    resumed: boolean;\r\n    reason: string;\r\n    timestamp: Date;\r\n  }>;\r\n} "], "version": 3}