7ad66ae3bc918d0034769260ccf59197
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestDataFactory = exports.TestDatabaseUtil = void 0;
const typeorm_1 = require("typeorm");
const testing_1 = require("@nestjs/testing");
const typeorm_2 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
/**
 * Test Database Utility
 *
 * Comprehensive database testing utility providing:
 * - Isolated test database setup with transaction management
 * - Test data factory and fixture management
 * - Database cleanup and reset utilities
 * - Migration testing and schema validation
 * - Performance testing with query optimization
 * - Data consistency validation and integrity checks
 * - Mock data generation with realistic test scenarios
 * - Transaction isolation and rollback capabilities
 */
class TestDatabaseUtil {
    constructor() {
        this.repositories = new Map();
    }
    /**
     * Get singleton instance
     */
    static getInstance() {
        if (!TestDatabaseUtil.instance) {
            TestDatabaseUtil.instance = new TestDatabaseUtil();
        }
        return TestDatabaseUtil.instance;
    }
    /**
     * Initialize test database
     */
    async initializeTestDatabase(entities) {
        this.testingModule = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: ['.env.test', '.env'],
                }),
                typeorm_2.TypeOrmModule.forRootAsync({
                    imports: [config_1.ConfigModule],
                    useFactory: async (configService) => ({
                        type: 'postgres',
                        host: configService.get('TEST_DB_HOST', 'localhost'),
                        port: configService.get('TEST_DB_PORT', 5433),
                        username: configService.get('TEST_DB_USERNAME', 'test'),
                        password: configService.get('TEST_DB_PASSWORD', 'test'),
                        database: configService.get('TEST_DB_NAME', 'sentinel_test'),
                        entities,
                        synchronize: true,
                        dropSchema: true,
                        logging: configService.get('TEST_DB_LOGGING', 'false') === 'true',
                        maxQueryExecutionTime: 1000,
                        extra: {
                            connectionLimit: 10,
                            acquireTimeout: 30000,
                            timeout: 30000,
                        },
                    }),
                    inject: [config_1.ConfigService],
                }),
                typeorm_2.TypeOrmModule.forFeature(entities),
            ],
        }).compile();
        this.dataSource = this.testingModule.get(typeorm_1.DataSource);
        // Initialize repositories
        for (const entity of entities) {
            const repository = this.dataSource.getRepository(entity);
            this.repositories.set(entity.name, repository);
        }
        await this.dataSource.synchronize(true);
    }
    /**
     * Get repository for entity
     */
    getRepository(entity) {
        const repository = this.repositories.get(entity.name);
        if (!repository) {
            throw new Error(`Repository for entity ${entity.name} not found`);
        }
        return repository;
    }
    /**
     * Get data source
     */
    getDataSource() {
        return this.dataSource;
    }
    /**
     * Get testing module
     */
    getTestingModule() {
        return this.testingModule;
    }
    /**
     * Start transaction
     */
    async startTransaction() {
        await this.dataSource.query('BEGIN');
    }
    /**
     * Rollback transaction
     */
    async rollbackTransaction() {
        await this.dataSource.query('ROLLBACK');
    }
    /**
     * Commit transaction
     */
    async commitTransaction() {
        await this.dataSource.query('COMMIT');
    }
    /**
     * Clean all tables
     */
    async cleanDatabase() {
        const entities = this.dataSource.entityMetadatas;
        // Disable foreign key checks
        await this.dataSource.query('SET FOREIGN_KEY_CHECKS = 0');
        // Truncate all tables
        for (const entity of entities) {
            await this.dataSource.query(`TRUNCATE TABLE ${entity.tableName} RESTART IDENTITY CASCADE`);
        }
        // Re-enable foreign key checks
        await this.dataSource.query('SET FOREIGN_KEY_CHECKS = 1');
    }
    /**
     * Reset database to initial state
     */
    async resetDatabase() {
        await this.dataSource.dropDatabase();
        await this.dataSource.synchronize(true);
    }
    /**
     * Seed test data
     */
    async seedTestData(seedData) {
        for (const [entityName, data] of Object.entries(seedData)) {
            const repository = this.repositories.get(entityName);
            if (repository) {
                await repository.save(data);
            }
        }
    }
    /**
     * Create test data factory
     */
    createTestDataFactory(entity) {
        return new TestDataFactory(entity, this.getRepository(entity));
    }
    /**
     * Validate database schema
     */
    async validateSchema() {
        const issues = [];
        try {
            // Check if all entities have corresponding tables
            const entities = this.dataSource.entityMetadatas;
            for (const entity of entities) {
                const tableExists = await this.dataSource.query(`SELECT table_name FROM information_schema.tables WHERE table_name = '${entity.tableName}'`);
                if (tableExists.length === 0) {
                    issues.push(`Table ${entity.tableName} does not exist`);
                }
            }
            // Check foreign key constraints
            for (const entity of entities) {
                for (const relation of entity.relations) {
                    if (relation.isManyToOne || relation.isOneToOne) {
                        const constraintExists = await this.dataSource.query(`
              SELECT constraint_name 
              FROM information_schema.table_constraints 
              WHERE table_name = '${entity.tableName}' 
              AND constraint_type = 'FOREIGN KEY'
            `);
                        if (constraintExists.length === 0 && relation.foreignKeys.length > 0) {
                            issues.push(`Foreign key constraint missing for ${entity.tableName}.${relation.propertyName}`);
                        }
                    }
                }
            }
            // Check indexes
            for (const entity of entities) {
                for (const index of entity.indices) {
                    const indexExists = await this.dataSource.query(`
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename = '${entity.tableName}' 
            AND indexname = '${index.name}'
          `);
                    if (indexExists.length === 0) {
                        issues.push(`Index ${index.name} missing on table ${entity.tableName}`);
                    }
                }
            }
        }
        catch (error) {
            issues.push(`Schema validation error: ${error.message}`);
        }
        return {
            valid: issues.length === 0,
            issues,
        };
    }
    /**
     * Run performance tests
     */
    async runPerformanceTests() {
        const queryPerformance = {};
        const slowQueries = [];
        // Test basic CRUD operations
        const entities = this.dataSource.entityMetadatas;
        for (const entity of entities.slice(0, 3)) { // Test first 3 entities
            const repository = this.repositories.get(entity.name);
            if (repository) {
                // Test SELECT performance
                const selectStart = Date.now();
                await repository.find({ take: 100 });
                const selectDuration = Date.now() - selectStart;
                queryPerformance[`${entity.name}_select`] = selectDuration;
                if (selectDuration > 1000) {
                    slowQueries.push({
                        query: `SELECT * FROM ${entity.tableName} LIMIT 100`,
                        duration: selectDuration,
                    });
                }
                // Test COUNT performance
                const countStart = Date.now();
                await repository.count();
                const countDuration = Date.now() - countStart;
                queryPerformance[`${entity.name}_count`] = countDuration;
                if (countDuration > 500) {
                    slowQueries.push({
                        query: `SELECT COUNT(*) FROM ${entity.tableName}`,
                        duration: countDuration,
                    });
                }
            }
        }
        // Get connection pool stats
        const connectionPoolStats = {
            totalConnections: this.dataSource.driver.pool?.totalCount || 0,
            idleConnections: this.dataSource.driver.pool?.idleCount || 0,
            waitingConnections: this.dataSource.driver.pool?.waitingCount || 0,
        };
        return {
            queryPerformance,
            connectionPoolStats,
            slowQueries,
        };
    }
    /**
     * Test data consistency
     */
    async testDataConsistency() {
        const issues = [];
        try {
            // Check foreign key integrity
            const entities = this.dataSource.entityMetadatas;
            for (const entity of entities) {
                for (const relation of entity.relations) {
                    if (relation.isManyToOne || relation.isOneToOne) {
                        const orphanedRecords = await this.dataSource.query(`
              SELECT COUNT(*) as count
              FROM ${entity.tableName} t1
              LEFT JOIN ${relation.inverseEntityMetadata.tableName} t2 
              ON t1.${relation.joinColumns[0]?.databaseName} = t2.${relation.inverseEntityMetadata.primaryColumns[0]?.databaseName}
              WHERE t1.${relation.joinColumns[0]?.databaseName} IS NOT NULL 
              AND t2.${relation.inverseEntityMetadata.primaryColumns[0]?.databaseName} IS NULL
            `);
                        if (orphanedRecords[0]?.count > 0) {
                            issues.push(`Found ${orphanedRecords[0].count} orphaned records in ${entity.tableName}.${relation.propertyName}`);
                        }
                    }
                }
            }
            // Check unique constraints
            for (const entity of entities) {
                for (const unique of entity.uniques) {
                    const duplicates = await this.dataSource.query(`
            SELECT ${unique.columnNames.join(', ')}, COUNT(*) as count
            FROM ${entity.tableName}
            GROUP BY ${unique.columnNames.join(', ')}
            HAVING COUNT(*) > 1
          `);
                    if (duplicates.length > 0) {
                        issues.push(`Found duplicate values in unique constraint ${unique.name} on table ${entity.tableName}`);
                    }
                }
            }
        }
        catch (error) {
            issues.push(`Data consistency check error: ${error.message}`);
        }
        return {
            consistent: issues.length === 0,
            issues,
        };
    }
    /**
     * Close database connection
     */
    async closeConnection() {
        if (this.dataSource && this.dataSource.isInitialized) {
            await this.dataSource.destroy();
        }
        if (this.testingModule) {
            await this.testingModule.close();
        }
    }
}
exports.TestDatabaseUtil = TestDatabaseUtil;
/**
 * Test Data Factory
 *
 * Factory class for creating test data with realistic values
 */
class TestDataFactory {
    constructor(entity, repository) {
        this.entity = entity;
        this.repository = repository;
        this.defaultValues = new Map();
        this.sequences = new Map();
    }
    /**
     * Set default value for property
     */
    setDefault(property, value) {
        this.defaultValues.set(property, value);
        return this;
    }
    /**
     * Set sequence for property
     */
    setSequence(property, startValue = 1) {
        this.sequences.set(property, startValue);
        return this;
    }
    /**
     * Create single entity
     */
    async create(overrides = {}) {
        const entityData = new this.entity();
        // Apply default values
        for (const [property, value] of this.defaultValues.entries()) {
            if (!(property in overrides)) {
                entityData[property] = typeof value === 'function' ? value() : value;
            }
        }
        // Apply sequence values
        for (const [property, currentValue] of this.sequences.entries()) {
            if (!(property in overrides)) {
                entityData[property] = currentValue;
                this.sequences.set(property, currentValue + 1);
            }
        }
        // Apply overrides
        Object.assign(entityData, overrides);
        return await this.repository.save(entityData);
    }
    /**
     * Create multiple entities
     */
    async createMany(count, overrides = {}) {
        const entities = [];
        for (let i = 0; i < count; i++) {
            const entity = await this.create(overrides);
            entities.push(entity);
        }
        return entities;
    }
    /**
     * Build entity without saving
     */
    build(overrides = {}) {
        const entityData = new this.entity();
        // Apply default values
        for (const [property, value] of this.defaultValues.entries()) {
            if (!(property in overrides)) {
                entityData[property] = typeof value === 'function' ? value() : value;
            }
        }
        // Apply sequence values
        for (const [property, currentValue] of this.sequences.entries()) {
            if (!(property in overrides)) {
                entityData[property] = currentValue;
                this.sequences.set(property, currentValue + 1);
            }
        }
        // Apply overrides
        Object.assign(entityData, overrides);
        return entityData;
    }
    /**
     * Build multiple entities without saving
     */
    buildMany(count, overrides = {}) {
        const entities = [];
        for (let i = 0; i < count; i++) {
            const entity = this.build(overrides);
            entities.push(entity);
        }
        return entities;
    }
}
exports.TestDataFactory = TestDataFactory;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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