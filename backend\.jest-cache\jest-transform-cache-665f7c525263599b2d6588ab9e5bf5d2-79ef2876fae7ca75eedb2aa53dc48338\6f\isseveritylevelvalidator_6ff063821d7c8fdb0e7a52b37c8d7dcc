4e4d919f42a538afe1373796d7faffd7
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeverityLevelUtils = exports.IsSeverityLevelConstraint = exports.NumericSeverity = exports.SeverityLevel = void 0;
exports.IsSeverityLevel = IsSeverityLevel;
exports.IsStringSeverityLevel = IsStringSeverityLevel;
exports.IsNumericSeverityLevel = IsNumericSeverityLevel;
exports.IsCvssSeverityLevel = IsCvssSeverityLevel;
exports.IsSeverityLevelArray = IsSeverityLevelArray;
const class_validator_1 = require("class-validator");
/**
 * Severity level enumeration
 * Based on CVSS and industry standards
 */
var SeverityLevel;
(function (SeverityLevel) {
    SeverityLevel["CRITICAL"] = "critical";
    SeverityLevel["HIGH"] = "high";
    SeverityLevel["MEDIUM"] = "medium";
    SeverityLevel["LOW"] = "low";
    SeverityLevel["INFORMATIONAL"] = "informational";
    SeverityLevel["NONE"] = "none";
})(SeverityLevel || (exports.SeverityLevel = SeverityLevel = {}));
/**
 * Numeric severity scale (0-10)
 * Aligned with CVSS scoring
 */
var NumericSeverity;
(function (NumericSeverity) {
    NumericSeverity[NumericSeverity["NONE"] = 0] = "NONE";
    NumericSeverity[NumericSeverity["LOW_MIN"] = 0.1] = "LOW_MIN";
    NumericSeverity[NumericSeverity["LOW_MAX"] = 3.9] = "LOW_MAX";
    NumericSeverity[NumericSeverity["MEDIUM_MIN"] = 4] = "MEDIUM_MIN";
    NumericSeverity[NumericSeverity["MEDIUM_MAX"] = 6.9] = "MEDIUM_MAX";
    NumericSeverity[NumericSeverity["HIGH_MIN"] = 7] = "HIGH_MIN";
    NumericSeverity[NumericSeverity["HIGH_MAX"] = 8.9] = "HIGH_MAX";
    NumericSeverity[NumericSeverity["CRITICAL_MIN"] = 9] = "CRITICAL_MIN";
    NumericSeverity[NumericSeverity["CRITICAL_MAX"] = 10] = "CRITICAL_MAX";
})(NumericSeverity || (exports.NumericSeverity = NumericSeverity = {}));
/**
 * Severity level validator constraint
 * Validates severity levels in both string and numeric formats
 */
let IsSeverityLevelConstraint = class IsSeverityLevelConstraint {
    /**
     * Validate severity level
     * @param value Value to validate
     * @param args Validation arguments
     * @returns boolean indicating if value is valid severity level
     */
    validate(value, args) {
        const defaultOptions = {
            allowNumeric: true,
            allowString: true,
            caseSensitive: false,
        };
        const options = {
            ...defaultOptions,
            ...(args.constraints[0] || {}),
        };
        if (value === null || value === undefined) {
            return false;
        }
        // Handle numeric severity (0-10)
        if (typeof value === 'number') {
            if (options.allowNumeric === false) {
                return false;
            }
            return this.validateNumericSeverity(value);
        }
        // Handle string severity
        if (typeof value === 'string') {
            if (options.allowString === false) {
                return false;
            }
            return this.validateStringSeverity(value, options);
        }
        return false;
    }
    /**
     * Validate numeric severity (0-10 scale)
     * @param value Numeric value to validate
     * @returns boolean indicating validity
     */
    validateNumericSeverity(value) {
        return value >= 0 && value <= 10 && !isNaN(value) && isFinite(value);
    }
    /**
     * Validate string severity level
     * @param value String value to validate
     * @param options Validation options
     * @returns boolean indicating validity
     */
    validateStringSeverity(value, options) {
        const normalizedValue = options.caseSensitive ? value : value.toLowerCase();
        // Check custom levels first
        if (options.customLevels && options.customLevels.length > 0) {
            const customLevels = options.caseSensitive
                ? options.customLevels
                : options.customLevels.map(level => level.toLowerCase());
            return customLevels.includes(normalizedValue);
        }
        // Check standard severity levels
        const standardLevels = Object.values(SeverityLevel);
        return standardLevels.includes(normalizedValue);
    }
    /**
     * Default error message for severity level validation
     * @param args Validation arguments
     * @returns Error message
     */
    defaultMessage(args) {
        const options = args.constraints[0] || {};
        const allowedFormats = [];
        if (options.allowString !== false) {
            if (options.customLevels && options.customLevels.length > 0) {
                allowedFormats.push(`one of: ${options.customLevels.join(', ')}`);
            }
            else {
                allowedFormats.push(`one of: ${Object.values(SeverityLevel).join(', ')}`);
            }
        }
        if (options.allowNumeric !== false) {
            allowedFormats.push('a number between 0 and 10');
        }
        return `${args.property} must be ${allowedFormats.join(' or ')}`;
    }
};
exports.IsSeverityLevelConstraint = IsSeverityLevelConstraint;
exports.IsSeverityLevelConstraint = IsSeverityLevelConstraint = __decorate([
    (0, class_validator_1.ValidatorConstraint)({ name: 'isSeverityLevel', async: false })
], IsSeverityLevelConstraint);
/**
 * Severity level validation decorator
 * @param options Severity validation options
 * @param validationOptions Validation options
 * @returns Property decorator
 */
function IsSeverityLevel(options, validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [options],
            validator: IsSeverityLevelConstraint,
        });
    };
}
/**
 * String severity level validation decorator
 * @param customLevels Custom allowed severity levels
 * @param validationOptions Validation options
 * @returns Property decorator
 */
function IsStringSeverityLevel(customLevels, validationOptions) {
    return IsSeverityLevel({
        allowNumeric: false,
        allowString: true,
        customLevels,
        caseSensitive: false,
    }, validationOptions);
}
/**
 * Numeric severity level validation decorator
 * @param validationOptions Validation options
 * @returns Property decorator
 */
function IsNumericSeverityLevel(validationOptions) {
    return IsSeverityLevel({
        allowNumeric: true,
        allowString: false,
    }, validationOptions);
}
/**
 * CVSS severity level validation decorator
 * @param validationOptions Validation options
 * @returns Property decorator
 */
function IsCvssSeverityLevel(validationOptions) {
    return IsSeverityLevel({
        allowNumeric: true,
        allowString: true,
        caseSensitive: false,
    }, validationOptions);
}
/**
 * Severity level array validation decorator
 * @param options Severity validation options
 * @param validationOptions Validation options
 * @returns Property decorator
 */
function IsSeverityLevelArray(options, validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            name: 'isSeverityLevelArray',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [options],
            validator: {
                validate(value, args) {
                    if (!Array.isArray(value)) {
                        return false;
                    }
                    const constraint = new IsSeverityLevelConstraint();
                    return value.every(item => constraint.validate(item, args));
                },
                defaultMessage(args) {
                    return `${args.property} must be an array of valid severity levels`;
                },
            },
        });
    };
}
/**
 * Utility functions for severity level handling
 */
class SeverityLevelUtils {
    /**
     * Convert numeric severity to string level
     * @param numericSeverity Numeric severity (0-10)
     * @returns String severity level
     */
    static numericToString(numericSeverity) {
        if (numericSeverity === 0) {
            return SeverityLevel.NONE;
        }
        else if (numericSeverity >= NumericSeverity.LOW_MIN && numericSeverity <= NumericSeverity.LOW_MAX) {
            return SeverityLevel.LOW;
        }
        else if (numericSeverity >= NumericSeverity.MEDIUM_MIN && numericSeverity <= NumericSeverity.MEDIUM_MAX) {
            return SeverityLevel.MEDIUM;
        }
        else if (numericSeverity >= NumericSeverity.HIGH_MIN && numericSeverity <= NumericSeverity.HIGH_MAX) {
            return SeverityLevel.HIGH;
        }
        else if (numericSeverity >= NumericSeverity.CRITICAL_MIN && numericSeverity <= NumericSeverity.CRITICAL_MAX) {
            return SeverityLevel.CRITICAL;
        }
        else {
            return SeverityLevel.INFORMATIONAL;
        }
    }
    /**
     * Convert string severity to numeric range
     * @param stringSeverity String severity level
     * @returns Numeric severity range [min, max]
     */
    static stringToNumericRange(stringSeverity) {
        const normalizedLevel = stringSeverity.toLowerCase();
        switch (normalizedLevel) {
            case SeverityLevel.NONE:
                return [0, 0];
            case SeverityLevel.LOW:
                return [NumericSeverity.LOW_MIN, NumericSeverity.LOW_MAX];
            case SeverityLevel.MEDIUM:
                return [NumericSeverity.MEDIUM_MIN, NumericSeverity.MEDIUM_MAX];
            case SeverityLevel.HIGH:
                return [NumericSeverity.HIGH_MIN, NumericSeverity.HIGH_MAX];
            case SeverityLevel.CRITICAL:
                return [NumericSeverity.CRITICAL_MIN, NumericSeverity.CRITICAL_MAX];
            case SeverityLevel.INFORMATIONAL:
            default:
                return [0, 0];
        }
    }
    /**
     * Get severity level priority (higher number = higher priority)
     * @param severity Severity level
     * @returns Priority number
     */
    static getPriority(severity) {
        if (typeof severity === 'number') {
            return severity;
        }
        const normalizedLevel = severity.toLowerCase();
        switch (normalizedLevel) {
            case SeverityLevel.CRITICAL:
                return 10;
            case SeverityLevel.HIGH:
                return 8;
            case SeverityLevel.MEDIUM:
                return 5;
            case SeverityLevel.LOW:
                return 2;
            case SeverityLevel.INFORMATIONAL:
                return 1;
            case SeverityLevel.NONE:
            default:
                return 0;
        }
    }
    /**
     * Compare two severity levels
     * @param severity1 First severity level
     * @param severity2 Second severity level
     * @returns Comparison result (-1, 0, 1)
     */
    static compare(severity1, severity2) {
        const priority1 = this.getPriority(severity1);
        const priority2 = this.getPriority(severity2);
        if (priority1 < priority2)
            return -1;
        if (priority1 > priority2)
            return 1;
        return 0;
    }
    /**
     * Sort severity levels by priority (highest first)
     * @param severities Array of severity levels
     * @returns Sorted array
     */
    static sortByPriority(severities) {
        return [...severities].sort((a, b) => this.compare(b, a));
    }
    /**
     * Validate severity level without decorator
     * @param severity Severity level to validate
     * @param options Validation options
     * @returns boolean indicating validity
     */
    static isValid(severity, options) {
        const constraint = new IsSeverityLevelConstraint();
        return constraint.validate(severity, { constraints: [options] });
    }
    /**
     * Normalize severity level format
     * @param severity Severity level
     * @returns Normalized severity level or null if invalid
     */
    static normalize(severity) {
        if (!this.isValid(severity)) {
            return null;
        }
        if (typeof severity === 'string') {
            return severity.toLowerCase();
        }
        return severity;
    }
    /**
     * Get color code for severity level (for UI display)
     * @param severity Severity level
     * @returns Color code
     */
    static getColorCode(severity) {
        const priority = this.getPriority(severity);
        if (priority >= 9)
            return '#dc3545'; // Critical - Red
        if (priority >= 7)
            return '#fd7e14'; // High - Orange
        if (priority >= 4)
            return '#ffc107'; // Medium - Yellow
        if (priority >= 1)
            return '#28a745'; // Low - Green
        return '#6c757d'; // None/Info - Gray
    }
    /**
     * Get severity level description
     * @param severity Severity level
     * @returns Human-readable description
     */
    static getDescription(severity) {
        if (typeof severity === 'number') {
            severity = this.numericToString(severity);
        }
        const normalizedLevel = severity.toLowerCase();
        switch (normalizedLevel) {
            case SeverityLevel.CRITICAL:
                return 'Critical vulnerabilities that require immediate attention';
            case SeverityLevel.HIGH:
                return 'High-risk vulnerabilities that should be addressed promptly';
            case SeverityLevel.MEDIUM:
                return 'Medium-risk vulnerabilities that should be addressed in a timely manner';
            case SeverityLevel.LOW:
                return 'Low-risk vulnerabilities that can be addressed as time permits';
            case SeverityLevel.INFORMATIONAL:
                return 'Informational findings that may be of interest';
            case SeverityLevel.NONE:
            default:
                return 'No security impact identified';
        }
    }
}
exports.SeverityLevelUtils = SeverityLevelUtils;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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