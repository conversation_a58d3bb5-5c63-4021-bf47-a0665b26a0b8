{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\vulnerability.factory.spec.ts", "mappings": ";;AAAA,oEAAqK;AACrK,4FAAmJ;AACnJ,2EAAkE;AAClE,6EAAoE;AACpE,2GAAuG;AACvG,gEAA8D;AAE9D,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,IAAI,kBAAwC,CAAC;IAC7C,IAAI,aAAqC,CAAC;IAE1C,UAAU,CAAC,GAAG,EAAE;QACd,kBAAkB,GAAG,CAAC;gBACpB,OAAO,EAAE,SAAS;gBAClB,SAAS,EAAE,eAAe;gBAC1B,SAAS,EAAE,QAAQ;gBACnB,WAAW,EAAE,MAAM;gBACnB,kBAAkB,EAAE,CAAC;wBACnB,IAAI,EAAE,oBAAoB;wBAC1B,OAAO,EAAE,QAAQ;wBACjB,IAAI,EAAE,UAAU;qBACjB,CAAC;gBACF,QAAQ,EAAE,UAAU;gBACpB,cAAc,EAAE,CAAC,yBAAyB,CAAC;aAC5C,CAAC,CAAC;QAEH,aAAa,GAAG;YACd,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE,gBAAgB;YACxB,YAAY,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;YAC9C,YAAY,EAAE,eAAe;YAC7B,OAAO,EAAE,8CAA8C;SACxD,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,OAAO,GAA+B;gBAC1C,KAAK,EAAE,6BAA6B;gBACpC,WAAW,EAAE,2CAA2C;gBACxD,QAAQ,EAAE,qCAAc,CAAC,IAAI;gBAC7B,QAAQ,EAAE,WAAW;gBACrB,IAAI,EAAE,eAAe;gBACrB,cAAc,EAAE,kBAAkB;gBAClC,SAAS,EAAE,aAAa;aACzB,CAAC;YAEF,MAAM,aAAa,GAAG,4CAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE3D,MAAM,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,oCAAa,CAAC,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAChE,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YACpF,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,0CAAmB,CAAC,UAAU,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,SAAS,GAAG,mCAAS,CAAC,UAAU,CAAC,GAAG,EAAE,8CAA8C,CAAC,CAAC;YAE5F,MAAM,OAAO,GAA+B;gBAC1C,EAAE,EAAE,8BAAc,CAAC,QAAQ,EAAE;gBAC7B,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,uBAAuB;gBAC9B,WAAW,EAAE,4BAA4B;gBACzC,QAAQ,EAAE,qCAAc,CAAC,QAAQ;gBACjC,QAAQ,EAAE,uBAAuB;gBACjC,IAAI,EAAE,iBAAiB;gBACvB,UAAU,EAAE,uCAAe,CAAC,IAAI;gBAChC,UAAU,EAAE,CAAC,SAAS,CAAC;gBACvB,cAAc,EAAE,kBAAkB;gBAClC,SAAS,EAAE,aAAa;gBACxB,IAAI,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC;gBACzB,UAAU,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE;aAClC,CAAC;YAEF,MAAM,aAAa,GAAG,4CAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE3D,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,EAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,QAAQ,GAAqB;gBACjC,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,oCAAoC;gBAC3C,WAAW,EAAE,qDAAqD;gBAClE,MAAM,EAAE;oBACN,SAAS,EAAE,GAAG;oBACd,YAAY,EAAE,8CAA8C;oBAC5D,mBAAmB,EAAE,GAAG;oBACxB,WAAW,EAAE,GAAG;iBACjB;gBACD,aAAa,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;gBAC/C,gBAAgB,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;gBAClD,gBAAgB,EAAE,CAAC;wBACjB,MAAM,EAAE,QAAQ;wBAChB,OAAO,EAAE,aAAa;wBACtB,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;qBAC9B,CAAC;gBACF,UAAU,EAAE,CAAC;wBACX,GAAG,EAAE,2DAA2D;wBAChE,MAAM,EAAE,QAAQ;wBAChB,IAAI,EAAE,CAAC,iBAAiB,CAAC;qBAC1B,CAAC;gBACF,MAAM,EAAE,CAAC,SAAS,CAAC;aACpB,CAAC;YAEF,MAAM,aAAa,GAAG,4CAAoB,CAAC,eAAe,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;YAEzF,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YACvE,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;YAC7D,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACnE,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,QAAQ,GAAqB;gBACjC,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,oBAAoB;gBAC3B,WAAW,EAAE,8CAA8C;gBAC3D,MAAM,EAAE;oBACN,SAAS,EAAE,GAAG;oBACd,YAAY,EAAE,8BAA8B;iBAC7C;gBACD,MAAM,EAAE;oBACN,SAAS,EAAE,GAAG;oBACd,YAAY,EAAE,8CAA8C;iBAC7D;gBACD,aAAa,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;gBAC/C,gBAAgB,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;gBAClD,gBAAgB,EAAE,EAAE;gBACpB,UAAU,EAAE,EAAE;gBACd,MAAM,EAAE,EAAE;aACX,CAAC;YAEF,MAAM,aAAa,GAAG,4CAAoB,CAAC,eAAe,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;YAEzF,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,IAAI,CAAC,CAAC,CAAC,gCAAgC;QAC5F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,UAAU,GAA4B;gBAC1C,SAAS,EAAE,UAAU;gBACrB,KAAK,EAAE,eAAe;gBACtB,IAAI,EAAE,4BAA4B;gBAClC,WAAW,EAAE,iDAAiD;gBAC9D,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,GAAG;gBACd,UAAU,EAAE,8CAA8C;gBAC1D,KAAK,EAAE;oBACL,EAAE,EAAE,YAAY;oBAChB,IAAI,EAAE,sBAAsB;oBAC5B,IAAI,EAAE,iBAAiB;oBACvB,SAAS,EAAE,eAAe;oBAC1B,QAAQ,EAAE,oBAAoB;iBAC/B;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,GAAG;oBACT,QAAQ,EAAE,OAAO;oBACjB,OAAO,EAAE,OAAO;oBAChB,OAAO,EAAE,QAAQ;iBAClB;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,QAAQ;oBACjB,MAAM,EAAE,OAAO;oBACf,UAAU,EAAE,EAAE;iBACf;gBACD,aAAa,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;gBAC/C,QAAQ,EAAE;oBACR,GAAG,EAAE,qCAAqC;oBAC1C,SAAS,EAAE,GAAG;iBACf;aACF,CAAC;YAEF,MAAM,aAAa,GAAG,4CAAoB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAEtE,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC/D,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,MAAM,CAAC,CAAC;YAC3D,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,SAAS,CAAC,CAAC;YACjE,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACnE,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC/E,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC9D,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,UAAU,GAA4B;gBAC1C,SAAS,EAAE,UAAU;gBACrB,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,gCAAgC;gBAC7C,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE;oBACL,EAAE,EAAE,WAAW;oBACf,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,QAAQ;iBACf;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,QAAQ;oBACjB,MAAM,EAAE,OAAO;oBACf,UAAU,EAAE,EAAE;iBACf;gBACD,aAAa,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;aAChD,CAAC;YAEF,MAAM,aAAa,GAAG,4CAAoB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAEtE,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,GAAG,CAAC,CAAC;YACxD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,iBAAiB,GAAG,4CAAoB,CAAC,MAAM,CAAC;gBACpD,KAAK,EAAE,oBAAoB;gBAC3B,WAAW,EAAE,gCAAgC;gBAC7C,QAAQ,EAAE,qCAAc,CAAC,MAAM;gBAC/B,QAAQ,EAAE,SAAS;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE,kBAAkB;gBAClC,SAAS,EAAE,aAAa;gBACxB,IAAI,EAAE,CAAC,MAAM,CAAC;aACf,CAAC,CAAC;YAEH,MAAM,WAAW,GAAiC;gBAChD,KAAK,EAAE,eAAe;gBACtB,MAAM,EAAE,0BAA0B;gBAClC,kBAAkB,EAAE,qBAAqB;gBACzC,QAAQ,EAAE,CAAC;wBACT,IAAI,EAAE,oBAAoB;wBAC1B,IAAI,EAAE,QAAQ;wBACd,MAAM,EAAE,WAAW;wBACnB,WAAW,EAAE,EAAE;wBACf,aAAa,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;qBAChD,CAAC;gBACF,mBAAmB,EAAE,CAAC;wBACpB,KAAK,EAAE,OAAO;wBACd,QAAQ,EAAE,sBAAsB;wBAChC,aAAa,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;wBAC/C,YAAY,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;wBAC9C,UAAU,EAAE,EAAE;qBACf,CAAC;gBACF,cAAc,EAAE,CAAC;wBACf,WAAW,EAAE,OAAO;wBACpB,SAAS,EAAE,mCAAmC;wBAC9C,MAAM,EAAE,gBAAgB;qBACzB,CAAC;gBACF,SAAS,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;aAC5C,CAAC;YAEF,MAAM,qBAAqB,GAAG,4CAAoB,CAAC,sBAAsB,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;YAE1G,MAAM,CAAC,qBAAqB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC/E,MAAM,CAAC,qBAAqB,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAM,CAAC,qBAAqB,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACjG,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACpE,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YACnE,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;YACtE,MAAM,CAAC,qBAAqB,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1E,MAAM,CAAC,qBAAqB,CAAC,UAAU,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,aAAa,GAAG,4CAAoB,CAAC,mBAAmB,CAC5D,mCAAmC,EACnC,2FAA2F,EAC3F,eAAe,EACf,kBAAkB,CACnB,CAAC;YAEF,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACtE,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;YAC7D,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAC3D,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC/D,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC5D,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,aAAa,GAAG,4CAAoB,CAAC,aAAa,CACtD,yBAAyB,EACzB,sFAAsF,EACtF,kBAAkB,EAClB,mBAAmB,CACpB,CAAC;YAEF,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC5D,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;YAC7D,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;YAC5C,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YAC5D,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACjE,MAAM,CAAC,aAAa,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACvE,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,aAAa,GAAG,4CAAoB,CAAC,aAAa,CACtD,0BAA0B,EAC1B,kDAAkD,EAClD,kBAAkB,EAClB,gBAAgB,CACjB,CAAC;YAEF,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC9D,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAChC,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;gBAC7D,sDAAsD;gBACtD,MAAM,cAAc,GAAG,CAAC,SAAiB,EAAoB,EAAE,CAAC,CAAC;oBAC/D,KAAK,EAAE,UAAU;oBACjB,KAAK,EAAE,MAAM;oBACb,WAAW,EAAE,MAAM;oBACnB,MAAM,EAAE;wBACN,SAAS,EAAE,SAAS;wBACpB,YAAY,EAAE,8CAA8C;qBAC7D;oBACD,aAAa,EAAE,IAAI,IAAI,EAAE;oBACzB,gBAAgB,EAAE,IAAI,IAAI,EAAE;oBAC5B,gBAAgB,EAAE,EAAE;oBACpB,UAAU,EAAE,EAAE;oBACd,MAAM,EAAE,EAAE;iBACX,CAAC,CAAC;gBAEH,MAAM,CAAC,4CAAoB,CAAC,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC;qBAC3F,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;gBACjC,MAAM,CAAC,4CAAoB,CAAC,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC;qBAC3F,IAAI,CAAC,qCAAc,CAAC,IAAI,CAAC,CAAC;gBAC7B,MAAM,CAAC,4CAAoB,CAAC,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC;qBAC3F,IAAI,CAAC,qCAAc,CAAC,MAAM,CAAC,CAAC;gBAC/B,MAAM,CAAC,4CAAoB,CAAC,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC;qBAC3F,IAAI,CAAC,qCAAc,CAAC,GAAG,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;gBACvD,MAAM,gBAAgB,GAAG,CAAC,QAAgB,EAA2B,EAAE,CAAC,CAAC;oBACvE,SAAS,EAAE,MAAM;oBACjB,IAAI,EAAE,MAAM;oBACZ,WAAW,EAAE,MAAM;oBACnB,QAAQ;oBACR,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;oBACjD,OAAO,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE;oBACzE,aAAa,EAAE,IAAI,IAAI,EAAE;iBAC1B,CAAC,CAAC;gBAEH,MAAM,CAAC,4CAAoB,CAAC,cAAc,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC;qBAC/E,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;gBACjC,MAAM,CAAC,4CAAoB,CAAC,cAAc,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC;qBAC3E,IAAI,CAAC,qCAAc,CAAC,IAAI,CAAC,CAAC;gBAC7B,MAAM,CAAC,4CAAoB,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;qBAC7E,IAAI,CAAC,qCAAc,CAAC,MAAM,CAAC,CAAC;gBAC/B,MAAM,CAAC,4CAAoB,CAAC,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC;qBAC1E,IAAI,CAAC,qCAAc,CAAC,GAAG,CAAC,CAAC;gBAC5B,MAAM,CAAC,4CAAoB,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC;qBAC9E,IAAI,CAAC,qCAAc,CAAC,OAAO,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,gBAAgB,GAAG,CAAC,MAAgB,EAAoB,EAAE,CAAC,CAAC;oBAChE,KAAK,EAAE,UAAU;oBACjB,KAAK,EAAE,MAAM;oBACb,WAAW,EAAE,MAAM;oBACnB,aAAa,EAAE,IAAI,IAAI,EAAE;oBACzB,gBAAgB,EAAE,IAAI,IAAI,EAAE;oBAC5B,gBAAgB,EAAE,EAAE;oBACpB,UAAU,EAAE,EAAE;oBACd,MAAM;iBACP,CAAC,CAAC;gBAEH,MAAM,CAAC,4CAAoB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC;qBACpG,IAAI,CAAC,WAAW,CAAC,CAAC;gBACrB,MAAM,CAAC,4CAAoB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC;qBACpG,IAAI,CAAC,WAAW,CAAC,CAAC;gBACrB,MAAM,CAAC,4CAAoB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC;qBACrG,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC1B,MAAM,CAAC,4CAAoB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC;qBACrG,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAC7B,MAAM,CAAC,4CAAoB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC;qBACrG,IAAI,CAAC,SAAS,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;gBACtE,MAAM,wBAAwB,GAAG,CAAC,UAAkB,EAA2B,EAAE,CAAC,CAAC;oBACjF,SAAS,EAAE,MAAM;oBACjB,IAAI,EAAE,MAAM;oBACZ,WAAW,EAAE,MAAM;oBACnB,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;oBACjD,OAAO,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE;oBACrE,aAAa,EAAE,IAAI,IAAI,EAAE;iBAC1B,CAAC,CAAC;gBAEH,MAAM,CAAC,4CAAoB,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC;qBACjF,IAAI,CAAC,uCAAe,CAAC,SAAS,CAAC,CAAC;gBACnC,MAAM,CAAC,4CAAoB,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC;qBACjF,IAAI,CAAC,uCAAe,CAAC,SAAS,CAAC,CAAC;gBACnC,MAAM,CAAC,4CAAoB,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC;qBACjF,IAAI,CAAC,uCAAe,CAAC,IAAI,CAAC,CAAC;gBAC9B,MAAM,CAAC,4CAAoB,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC;qBACjF,IAAI,CAAC,uCAAe,CAAC,MAAM,CAAC,CAAC;gBAChC,MAAM,CAAC,4CAAoB,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC;qBACjF,IAAI,CAAC,uCAAe,CAAC,GAAG,CAAC,CAAC;gBAC7B,MAAM,CAAC,4CAAoB,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC;qBACjF,IAAI,CAAC,uCAAe,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,UAAU,GAA4B;gBAC1C,SAAS,EAAE,MAAM;gBACjB,IAAI,EAAE,MAAM;gBACZ,WAAW,EAAE,MAAM;gBACnB,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,GAAG;gBACd,UAAU,EAAE,gBAAgB;gBAC5B,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;gBACjD,OAAO,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE;gBACzE,aAAa,EAAE,IAAI,IAAI,EAAE;aAC1B,CAAC;YAEF,MAAM,aAAa,GAAG,4CAAoB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAEtE,6DAA6D;YAC7D,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,aAAa,GAAG,4CAAoB,CAAC,MAAM,CAAC;gBAChD,KAAK,EAAE,MAAM;gBACb,WAAW,EAAE,MAAM;gBACnB,QAAQ,EAAE,qCAAc,CAAC,MAAM;gBAC/B,QAAQ,EAAE,MAAM;gBAChB,IAAI,EAAE,MAAM;gBACZ,cAAc,EAAE,EAAE,EAAE,6CAA6C;gBACjE,SAAS,EAAE,aAAa;aACzB,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\vulnerability.factory.spec.ts"], "sourcesContent": ["import { VulnerabilityFactory, CreateVulnerabilityOptions, CVEDatabaseEntry, VulnerabilityScanResult, ThreatIntelVulnerabilityData } from '../vulnerability.factory';\r\nimport { Vulnerability, VulnerabilityAsset, VulnerabilityDiscovery, VulnerabilityStatus } from '../../entities/vulnerability/vulnerability.entity';\r\nimport { ThreatSeverity } from '../../enums/threat-severity.enum';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\nimport { CVSSScore, CVSSVersion } from '../../value-objects/threat-indicators/cvss-score.value-object';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\n\r\ndescribe('VulnerabilityFactory', () => {\r\n  let mockAffectedAssets: VulnerabilityAsset[];\r\n  let mockDiscovery: VulnerabilityDiscovery;\r\n\r\n  beforeEach(() => {\r\n    mockAffectedAssets = [{\r\n      assetId: 'asset-1',\r\n      assetName: 'Web Server 01',\r\n      assetType: 'server',\r\n      criticality: 'high',\r\n      affectedComponents: [{\r\n        name: 'Apache HTTP Server',\r\n        version: '2.4.41',\r\n        type: 'software',\r\n      }],\r\n      exposure: 'external',\r\n      businessImpact: ['customer_facing_service'],\r\n    }];\r\n\r\n    mockDiscovery = {\r\n      method: 'automated_scan',\r\n      source: 'Nessus Scanner',\r\n      discoveredAt: new Date('2024-01-15T10:00:00Z'),\r\n      discoveredBy: 'Security Team',\r\n      details: 'Discovered during routine vulnerability scan',\r\n    };\r\n  });\r\n\r\n  describe('create', () => {\r\n    it('should create a vulnerability with basic options', () => {\r\n      const options: CreateVulnerabilityOptions = {\r\n        title: 'SQL Injection Vulnerability',\r\n        description: 'SQL injection vulnerability in login form',\r\n        severity: ThreatSeverity.HIGH,\r\n        category: 'injection',\r\n        type: 'sql_injection',\r\n        affectedAssets: mockAffectedAssets,\r\n        discovery: mockDiscovery,\r\n      };\r\n\r\n      const vulnerability = VulnerabilityFactory.create(options);\r\n\r\n      expect(vulnerability).toBeInstanceOf(Vulnerability);\r\n      expect(vulnerability.title).toBe('SQL Injection Vulnerability');\r\n      expect(vulnerability.description).toBe('SQL injection vulnerability in login form');\r\n      expect(vulnerability.severity).toBe(ThreatSeverity.HIGH);\r\n      expect(vulnerability.category).toBe('injection');\r\n      expect(vulnerability.type).toBe('sql_injection');\r\n      expect(vulnerability.affectedAssets).toHaveLength(1);\r\n      expect(vulnerability.status).toBe(VulnerabilityStatus.DISCOVERED);\r\n    });\r\n\r\n    it('should create a vulnerability with all options', () => {\r\n      const cvssScore = CVSSScore.createV3_1(8.5, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N');\r\n      \r\n      const options: CreateVulnerabilityOptions = {\r\n        id: UniqueEntityId.generate(),\r\n        cveId: 'CVE-2024-1234',\r\n        title: 'Remote Code Execution',\r\n        description: 'Critical RCE vulnerability',\r\n        severity: ThreatSeverity.CRITICAL,\r\n        category: 'remote_code_execution',\r\n        type: 'buffer_overflow',\r\n        confidence: ConfidenceLevel.HIGH,\r\n        cvssScores: [cvssScore],\r\n        affectedAssets: mockAffectedAssets,\r\n        discovery: mockDiscovery,\r\n        tags: ['critical', 'rce'],\r\n        attributes: { scanner: 'custom' },\r\n      };\r\n\r\n      const vulnerability = VulnerabilityFactory.create(options);\r\n\r\n      expect(vulnerability.id.equals(options.id!)).toBe(true);\r\n      expect(vulnerability.cveId).toBe('CVE-2024-1234');\r\n      expect(vulnerability.confidence).toBe(ConfidenceLevel.HIGH);\r\n      expect(vulnerability.cvssScores).toHaveLength(1);\r\n      expect(vulnerability.tags).toContain('critical');\r\n      expect(vulnerability.tags).toContain('rce');\r\n      expect(vulnerability.attributes.scanner).toBe('custom');\r\n    });\r\n  });\r\n\r\n  describe('fromCVEDatabase', () => {\r\n    it('should create vulnerability from CVE database entry', () => {\r\n      const cveEntry: CVEDatabaseEntry = {\r\n        cveId: 'CVE-2024-5678',\r\n        title: 'Apache HTTP Server Buffer Overflow',\r\n        description: 'Buffer overflow vulnerability in Apache HTTP Server',\r\n        cvssV3: {\r\n          baseScore: 9.8,\r\n          vectorString: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',\r\n          exploitabilityScore: 3.9,\r\n          impactScore: 5.9,\r\n        },\r\n        publishedDate: new Date('2024-01-10T00:00:00Z'),\r\n        lastModifiedDate: new Date('2024-01-15T00:00:00Z'),\r\n        affectedProducts: [{\r\n          vendor: 'Apache',\r\n          product: 'HTTP Server',\r\n          versions: ['2.4.0', '2.4.41'],\r\n        }],\r\n        references: [{\r\n          url: 'https://httpd.apache.org/security/vulnerabilities_24.html',\r\n          source: 'Apache',\r\n          tags: ['Vendor Advisory'],\r\n        }],\r\n        cweIds: ['CWE-119'],\r\n      };\r\n\r\n      const vulnerability = VulnerabilityFactory.fromCVEDatabase(cveEntry, mockAffectedAssets);\r\n\r\n      expect(vulnerability.cveId).toBe('CVE-2024-5678');\r\n      expect(vulnerability.title).toBe('Apache HTTP Server Buffer Overflow');\r\n      expect(vulnerability.severity).toBe(ThreatSeverity.CRITICAL);\r\n      expect(vulnerability.category).toBe('memory_corruption');\r\n      expect(vulnerability.confidence).toBe(ConfidenceLevel.HIGH);\r\n      expect(vulnerability.cvssScores).toHaveLength(1);\r\n      expect(vulnerability.cvssScores[0].baseScore).toBe(9.8);\r\n      expect(vulnerability.tags).toContain('cve');\r\n      expect(vulnerability.tags).toContain('nvd');\r\n      expect(vulnerability.tags).toContain('cwe-CWE-119');\r\n      expect(vulnerability.discovery.method).toBe('threat_intelligence');\r\n      expect(vulnerability.discovery.source).toBe('CVE Database');\r\n    });\r\n\r\n    it('should handle CVE entry with both CVSS v2 and v3 scores', () => {\r\n      const cveEntry: CVEDatabaseEntry = {\r\n        cveId: 'CVE-2024-9999',\r\n        title: 'Test Vulnerability',\r\n        description: 'Test vulnerability with multiple CVSS scores',\r\n        cvssV2: {\r\n          baseScore: 7.5,\r\n          vectorString: '(AV:N/AC:L/Au:N/C:P/I:P/A:P)',\r\n        },\r\n        cvssV3: {\r\n          baseScore: 8.8,\r\n          vectorString: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H',\r\n        },\r\n        publishedDate: new Date('2024-01-01T00:00:00Z'),\r\n        lastModifiedDate: new Date('2024-01-01T00:00:00Z'),\r\n        affectedProducts: [],\r\n        references: [],\r\n        cweIds: [],\r\n      };\r\n\r\n      const vulnerability = VulnerabilityFactory.fromCVEDatabase(cveEntry, mockAffectedAssets);\r\n\r\n      expect(vulnerability.cvssScores).toHaveLength(2);\r\n      expect(vulnerability.severity).toBe(ThreatSeverity.HIGH); // Based on higher CVSS v3 score\r\n    });\r\n  });\r\n\r\n  describe('fromScanResult', () => {\r\n    it('should create vulnerability from scan result', () => {\r\n      const scanResult: VulnerabilityScanResult = {\r\n        scannerId: 'SCAN-001',\r\n        cveId: 'CVE-2024-1111',\r\n        name: 'Cross-Site Scripting (XSS)',\r\n        description: 'Reflected XSS vulnerability in search parameter',\r\n        severity: 'Medium',\r\n        cvssScore: 6.1,\r\n        cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N',\r\n        asset: {\r\n          id: 'web-app-01',\r\n          name: 'Main Web Application',\r\n          type: 'web_application',\r\n          ipAddress: '*************',\r\n          hostname: 'webapp.example.com',\r\n        },\r\n        service: {\r\n          port: 443,\r\n          protocol: 'HTTPS',\r\n          service: 'nginx',\r\n          version: '1.18.0',\r\n        },\r\n        scanner: {\r\n          name: 'OWASP ZAP',\r\n          version: '2.12.0',\r\n          ruleId: '40012',\r\n          confidence: 85,\r\n        },\r\n        scanTimestamp: new Date('2024-01-20T14:30:00Z'),\r\n        metadata: {\r\n          url: '/search?q=<script>alert(1)</script>',\r\n          parameter: 'q',\r\n        },\r\n      };\r\n\r\n      const vulnerability = VulnerabilityFactory.fromScanResult(scanResult);\r\n\r\n      expect(vulnerability.cveId).toBe('CVE-2024-1111');\r\n      expect(vulnerability.title).toBe('Cross-Site Scripting (XSS)');\r\n      expect(vulnerability.severity).toBe(ThreatSeverity.MEDIUM);\r\n      expect(vulnerability.category).toBe('injection');\r\n      expect(vulnerability.confidence).toBe(ConfidenceLevel.VERY_HIGH);\r\n      expect(vulnerability.affectedAssets).toHaveLength(1);\r\n      expect(vulnerability.affectedAssets[0].assetId).toBe('web-app-01');\r\n      expect(vulnerability.affectedAssets[0].assetName).toBe('Main Web Application');\r\n      expect(vulnerability.discovery.method).toBe('automated_scan');\r\n      expect(vulnerability.discovery.source).toBe('OWASP ZAP');\r\n      expect(vulnerability.tags).toContain('scan-result');\r\n      expect(vulnerability.tags).toContain('owasp zap');\r\n      expect(vulnerability.attributes.scannerId).toBe('SCAN-001');\r\n    });\r\n\r\n    it('should handle scan result without CVSS vector', () => {\r\n      const scanResult: VulnerabilityScanResult = {\r\n        scannerId: 'SCAN-002',\r\n        name: 'Weak SSL Configuration',\r\n        description: 'SSL/TLS configuration weakness',\r\n        severity: 'Low',\r\n        asset: {\r\n          id: 'server-01',\r\n          name: 'Web Server',\r\n          type: 'server',\r\n        },\r\n        scanner: {\r\n          name: 'Nessus',\r\n          version: '10.4.0',\r\n          ruleId: '51192',\r\n          confidence: 70,\r\n        },\r\n        scanTimestamp: new Date('2024-01-20T15:00:00Z'),\r\n      };\r\n\r\n      const vulnerability = VulnerabilityFactory.fromScanResult(scanResult);\r\n\r\n      expect(vulnerability.cvssScores).toHaveLength(0);\r\n      expect(vulnerability.severity).toBe(ThreatSeverity.LOW);\r\n      expect(vulnerability.confidence).toBe(ConfidenceLevel.HIGH);\r\n    });\r\n  });\r\n\r\n  describe('withThreatIntelligence', () => {\r\n    it('should enhance vulnerability with threat intelligence data', () => {\r\n      const baseVulnerability = VulnerabilityFactory.create({\r\n        title: 'Test Vulnerability',\r\n        description: 'Base vulnerability for testing',\r\n        severity: ThreatSeverity.MEDIUM,\r\n        category: 'general',\r\n        type: 'unknown',\r\n        affectedAssets: mockAffectedAssets,\r\n        discovery: mockDiscovery,\r\n        tags: ['base'],\r\n      });\r\n\r\n      const threatIntel: ThreatIntelVulnerabilityData = {\r\n        cveId: 'CVE-2024-2222',\r\n        source: 'Threat Intelligence Feed',\r\n        exploitationStatus: 'active_exploitation',\r\n        exploits: [{\r\n          name: 'Public Exploit Kit',\r\n          type: 'public',\r\n          source: 'ExploitDB',\r\n          reliability: 90,\r\n          publishedDate: new Date('2024-01-18T00:00:00Z'),\r\n        }],\r\n        threatActorActivity: [{\r\n          actor: 'APT29',\r\n          campaign: 'Operation SolarStorm',\r\n          firstObserved: new Date('2024-01-16T00:00:00Z'),\r\n          lastObserved: new Date('2024-01-19T00:00:00Z'),\r\n          confidence: 85,\r\n        }],\r\n        attackPatterns: [{\r\n          techniqueId: 'T1190',\r\n          technique: 'Exploit Public-Facing Application',\r\n          tactic: 'Initial Access',\r\n        }],\r\n        timestamp: new Date('2024-01-20T00:00:00Z'),\r\n      };\r\n\r\n      const enhancedVulnerability = VulnerabilityFactory.withThreatIntelligence(baseVulnerability, threatIntel);\r\n\r\n      expect(enhancedVulnerability.exploitation?.status).toBe('active_exploitation');\r\n      expect(enhancedVulnerability.exploitation?.availableExploits).toHaveLength(1);\r\n      expect(enhancedVulnerability.exploitation?.availableExploits[0].name).toBe('Public Exploit Kit');\r\n      expect(enhancedVulnerability.tags).toContain('threat-intelligence');\r\n      expect(enhancedVulnerability.tags).toContain('actively-exploited');\r\n      expect(enhancedVulnerability.tags).toContain('threat-actor-activity');\r\n      expect(enhancedVulnerability.attributes.threatIntelligence).toBeDefined();\r\n      expect(enhancedVulnerability.attributes.threatIntelligence.source).toBe('Threat Intelligence Feed');\r\n    });\r\n  });\r\n\r\n  describe('createCriticalAlert', () => {\r\n    it('should create a critical vulnerability alert', () => {\r\n      const vulnerability = VulnerabilityFactory.createCriticalAlert(\r\n        'Zero-Day RCE in Production System',\r\n        'Critical zero-day remote code execution vulnerability discovered in production web server',\r\n        'CVE-2024-0000',\r\n        mockAffectedAssets\r\n      );\r\n\r\n      expect(vulnerability.title).toBe('Zero-Day RCE in Production System');\r\n      expect(vulnerability.severity).toBe(ThreatSeverity.CRITICAL);\r\n      expect(vulnerability.category).toBe('critical-alert');\r\n      expect(vulnerability.type).toBe('security-alert');\r\n      expect(vulnerability.confidence).toBe(ConfidenceLevel.HIGH);\r\n      expect(vulnerability.cveId).toBe('CVE-2024-0000');\r\n      expect(vulnerability.tags).toContain('critical');\r\n      expect(vulnerability.tags).toContain('alert');\r\n      expect(vulnerability.tags).toContain('immediate-response');\r\n      expect(vulnerability.discovery.method).toBe('external_report');\r\n      expect(vulnerability.attributes.alertType).toBe('critical');\r\n      expect(vulnerability.attributes.requiresImmediateAttention).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('createZeroDay', () => {\r\n    it('should create a zero-day vulnerability', () => {\r\n      const vulnerability = VulnerabilityFactory.createZeroDay(\r\n        'Unknown Buffer Overflow',\r\n        'Previously unknown buffer overflow vulnerability discovered during incident response',\r\n        mockAffectedAssets,\r\n        'incident_response'\r\n      );\r\n\r\n      expect(vulnerability.title).toBe('Unknown Buffer Overflow');\r\n      expect(vulnerability.severity).toBe(ThreatSeverity.CRITICAL);\r\n      expect(vulnerability.category).toBe('zero-day');\r\n      expect(vulnerability.type).toBe('unknown-vulnerability');\r\n      expect(vulnerability.confidence).toBe(ConfidenceLevel.MEDIUM);\r\n      expect(vulnerability.cveId).toBeUndefined();\r\n      expect(vulnerability.tags).toContain('zero-day');\r\n      expect(vulnerability.tags).toContain('no-cve');\r\n      expect(vulnerability.tags).toContain('active-exploitation');\r\n      expect(vulnerability.discovery.method).toBe('incident_response');\r\n      expect(vulnerability.exploitation?.status).toBe('active_exploitation');\r\n      expect(vulnerability.attributes.zeroDay).toBe(true);\r\n      expect(vulnerability.attributes.noCVE).toBe(true);\r\n    });\r\n\r\n    it('should create zero-day with different discovery methods', () => {\r\n      const vulnerability = VulnerabilityFactory.createZeroDay(\r\n        'Manual Testing Discovery',\r\n        'Zero-day found during manual penetration testing',\r\n        mockAffectedAssets,\r\n        'manual_testing'\r\n      );\r\n\r\n      expect(vulnerability.discovery.method).toBe('manual_testing');\r\n      expect(vulnerability.attributes.discoveryMethod).toBe('manual_testing');\r\n    });\r\n  });\r\n\r\n  describe('helper methods', () => {\r\n    describe('severity mapping', () => {\r\n      it('should map CVSS scores to threat severity correctly', () => {\r\n        // Test through fromCVEDatabase which uses the mapping\r\n        const createCVEEntry = (cvssScore: number): CVEDatabaseEntry => ({\r\n          cveId: 'CVE-TEST',\r\n          title: 'Test',\r\n          description: 'Test',\r\n          cvssV3: {\r\n            baseScore: cvssScore,\r\n            vectorString: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N',\r\n          },\r\n          publishedDate: new Date(),\r\n          lastModifiedDate: new Date(),\r\n          affectedProducts: [],\r\n          references: [],\r\n          cweIds: [],\r\n        });\r\n\r\n        expect(VulnerabilityFactory.fromCVEDatabase(createCVEEntry(9.5), mockAffectedAssets).severity)\r\n          .toBe(ThreatSeverity.CRITICAL);\r\n        expect(VulnerabilityFactory.fromCVEDatabase(createCVEEntry(8.0), mockAffectedAssets).severity)\r\n          .toBe(ThreatSeverity.HIGH);\r\n        expect(VulnerabilityFactory.fromCVEDatabase(createCVEEntry(5.0), mockAffectedAssets).severity)\r\n          .toBe(ThreatSeverity.MEDIUM);\r\n        expect(VulnerabilityFactory.fromCVEDatabase(createCVEEntry(2.0), mockAffectedAssets).severity)\r\n          .toBe(ThreatSeverity.LOW);\r\n      });\r\n\r\n      it('should map scanner severity strings correctly', () => {\r\n        const createScanResult = (severity: string): VulnerabilityScanResult => ({\r\n          scannerId: 'test',\r\n          name: 'Test',\r\n          description: 'Test',\r\n          severity,\r\n          asset: { id: 'test', name: 'Test', type: 'test' },\r\n          scanner: { name: 'Test', version: '1.0', ruleId: 'test', confidence: 80 },\r\n          scanTimestamp: new Date(),\r\n        });\r\n\r\n        expect(VulnerabilityFactory.fromScanResult(createScanResult('Critical')).severity)\r\n          .toBe(ThreatSeverity.CRITICAL);\r\n        expect(VulnerabilityFactory.fromScanResult(createScanResult('High')).severity)\r\n          .toBe(ThreatSeverity.HIGH);\r\n        expect(VulnerabilityFactory.fromScanResult(createScanResult('Medium')).severity)\r\n          .toBe(ThreatSeverity.MEDIUM);\r\n        expect(VulnerabilityFactory.fromScanResult(createScanResult('Low')).severity)\r\n          .toBe(ThreatSeverity.LOW);\r\n        expect(VulnerabilityFactory.fromScanResult(createScanResult('Unknown')).severity)\r\n          .toBe(ThreatSeverity.UNKNOWN);\r\n      });\r\n    });\r\n\r\n    describe('category inference', () => {\r\n      it('should infer category from CWE IDs correctly', () => {\r\n        const createCVEWithCWE = (cweIds: string[]): CVEDatabaseEntry => ({\r\n          cveId: 'CVE-TEST',\r\n          title: 'Test',\r\n          description: 'Test',\r\n          publishedDate: new Date(),\r\n          lastModifiedDate: new Date(),\r\n          affectedProducts: [],\r\n          references: [],\r\n          cweIds,\r\n        });\r\n\r\n        expect(VulnerabilityFactory.fromCVEDatabase(createCVEWithCWE(['CWE-79']), mockAffectedAssets).category)\r\n          .toBe('injection');\r\n        expect(VulnerabilityFactory.fromCVEDatabase(createCVEWithCWE(['CWE-89']), mockAffectedAssets).category)\r\n          .toBe('injection');\r\n        expect(VulnerabilityFactory.fromCVEDatabase(createCVEWithCWE(['CWE-287']), mockAffectedAssets).category)\r\n          .toBe('authentication');\r\n        expect(VulnerabilityFactory.fromCVEDatabase(createCVEWithCWE(['CWE-119']), mockAffectedAssets).category)\r\n          .toBe('memory_corruption');\r\n        expect(VulnerabilityFactory.fromCVEDatabase(createCVEWithCWE(['CWE-999']), mockAffectedAssets).category)\r\n          .toBe('general');\r\n      });\r\n    });\r\n\r\n    describe('confidence mapping', () => {\r\n      it('should map scanner confidence to domain confidence correctly', () => {\r\n        const createScanWithConfidence = (confidence: number): VulnerabilityScanResult => ({\r\n          scannerId: 'test',\r\n          name: 'Test',\r\n          description: 'Test',\r\n          severity: 'Medium',\r\n          asset: { id: 'test', name: 'Test', type: 'test' },\r\n          scanner: { name: 'Test', version: '1.0', ruleId: 'test', confidence },\r\n          scanTimestamp: new Date(),\r\n        });\r\n\r\n        expect(VulnerabilityFactory.fromScanResult(createScanWithConfidence(98)).confidence)\r\n          .toBe(ConfidenceLevel.CONFIRMED);\r\n        expect(VulnerabilityFactory.fromScanResult(createScanWithConfidence(88)).confidence)\r\n          .toBe(ConfidenceLevel.VERY_HIGH);\r\n        expect(VulnerabilityFactory.fromScanResult(createScanWithConfidence(75)).confidence)\r\n          .toBe(ConfidenceLevel.HIGH);\r\n        expect(VulnerabilityFactory.fromScanResult(createScanWithConfidence(60)).confidence)\r\n          .toBe(ConfidenceLevel.MEDIUM);\r\n        expect(VulnerabilityFactory.fromScanResult(createScanWithConfidence(40)).confidence)\r\n          .toBe(ConfidenceLevel.LOW);\r\n        expect(VulnerabilityFactory.fromScanResult(createScanWithConfidence(20)).confidence)\r\n          .toBe(ConfidenceLevel.VERY_LOW);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle invalid CVSS vector gracefully', () => {\r\n      const scanResult: VulnerabilityScanResult = {\r\n        scannerId: 'test',\r\n        name: 'Test',\r\n        description: 'Test',\r\n        severity: 'Medium',\r\n        cvssScore: 6.0,\r\n        cvssVector: 'INVALID_VECTOR',\r\n        asset: { id: 'test', name: 'Test', type: 'test' },\r\n        scanner: { name: 'Test', version: '1.0', ruleId: 'test', confidence: 80 },\r\n        scanTimestamp: new Date(),\r\n      };\r\n\r\n      const vulnerability = VulnerabilityFactory.fromScanResult(scanResult);\r\n\r\n      // Should create a basic CVSS score when vector parsing fails\r\n      expect(vulnerability.cvssScores).toHaveLength(1);\r\n      expect(vulnerability.cvssScores[0].baseScore).toBe(6.0);\r\n    });\r\n\r\n    it('should handle empty affected assets array', () => {\r\n      const vulnerability = VulnerabilityFactory.create({\r\n        title: 'Test',\r\n        description: 'Test',\r\n        severity: ThreatSeverity.MEDIUM,\r\n        category: 'test',\r\n        type: 'test',\r\n        affectedAssets: [], // Empty array - should be handled gracefully\r\n        discovery: mockDiscovery,\r\n      });\r\n\r\n      expect(vulnerability.affectedAssets).toHaveLength(0);\r\n    });\r\n  });\r\n});"], "version": 3}