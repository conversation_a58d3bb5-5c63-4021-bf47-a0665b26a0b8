0f21aaa81f264c1569863539fbb8ab23
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityException = void 0;
const typeorm_1 = require("typeorm");
const vulnerability_entity_1 = require("./vulnerability.entity");
const asset_entity_1 = require("../../../asset-management/domain/entities/asset.entity");
/**
 * Vulnerability Exception entity
 * Represents approved exceptions for vulnerabilities that won't be remediated
 */
let VulnerabilityException = class VulnerabilityException {
    /**
     * Check if exception is active
     */
    get isActive() {
        return this.status === 'approved' && !this.isExpired;
    }
    /**
     * Check if exception is expired
     */
    get isExpired() {
        return new Date() > this.expirationDate;
    }
    /**
     * Check if exception is pending approval
     */
    get isPending() {
        return this.status === 'pending';
    }
    /**
     * Check if exception needs review
     */
    get needsReview() {
        if (!this.nextReviewDate)
            return false;
        return new Date() >= this.nextReviewDate;
    }
    /**
     * Get days until expiration
     */
    get daysUntilExpiration() {
        const now = new Date();
        const diffMs = this.expirationDate.getTime() - now.getTime();
        return Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Get exception age in days
     */
    get ageInDays() {
        const now = new Date();
        const diffMs = now.getTime() - this.requestedAt.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Check if exception is expiring soon (within 30 days)
     */
    get isExpiringSoon() {
        return this.daysUntilExpiration <= 30 && this.daysUntilExpiration > 0;
    }
    /**
     * Approve exception
     */
    approve(approvedBy, comments) {
        this.status = 'approved';
        this.approvedBy = approvedBy;
        this.approvedAt = new Date();
        if (this.approvalWorkflow) {
            const currentApprover = this.approvalWorkflow.requiredApprovers[this.approvalWorkflow.currentStep];
            if (currentApprover) {
                currentApprover.approved = true;
                currentApprover.approvedAt = new Date().toISOString();
                currentApprover.comments = comments;
            }
        }
        this.setNextReviewDate();
    }
    /**
     * Reject exception
     */
    reject(rejectedBy, reason) {
        this.status = 'rejected';
        this.reviewedBy = rejectedBy;
        this.lastReviewedAt = new Date();
        if (!this.customAttributes) {
            this.customAttributes = {};
        }
        this.customAttributes.rejectionReason = reason;
    }
    /**
     * Revoke exception
     */
    revoke(revokedBy, reason) {
        this.status = 'revoked';
        this.reviewedBy = revokedBy;
        this.lastReviewedAt = new Date();
        if (!this.customAttributes) {
            this.customAttributes = {};
        }
        this.customAttributes.revocationReason = reason;
    }
    /**
     * Extend exception expiration
     */
    extend(newExpirationDate, extendedBy, reason) {
        this.expirationDate = newExpirationDate;
        this.reviewedBy = extendedBy;
        this.lastReviewedAt = new Date();
        if (!this.customAttributes) {
            this.customAttributes = {};
        }
        if (!this.customAttributes.extensions) {
            this.customAttributes.extensions = [];
        }
        this.customAttributes.extensions.push({
            extendedBy,
            extendedAt: new Date().toISOString(),
            newExpirationDate: newExpirationDate.toISOString(),
            reason,
        });
        this.setNextReviewDate();
    }
    /**
     * Review exception
     */
    review(reviewedBy, notes) {
        this.reviewedBy = reviewedBy;
        this.lastReviewedAt = new Date();
        if (notes) {
            if (!this.customAttributes) {
                this.customAttributes = {};
            }
            if (!this.customAttributes.reviewNotes) {
                this.customAttributes.reviewNotes = [];
            }
            this.customAttributes.reviewNotes.push({
                reviewedBy,
                reviewedAt: new Date().toISOString(),
                notes,
            });
        }
        this.setNextReviewDate();
    }
    /**
     * Add tag to exception
     */
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }
    /**
     * Remove tag from exception
     */
    removeTag(tag) {
        this.tags = this.tags.filter(t => t !== tag);
    }
    /**
     * Set next review date based on conditions
     */
    setNextReviewDate() {
        if (!this.conditions?.reviewFrequency)
            return;
        const now = new Date();
        const frequency = this.conditions.reviewFrequency;
        switch (frequency) {
            case 'monthly':
                this.nextReviewDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
                break;
            case 'quarterly':
                this.nextReviewDate = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
                break;
            case 'semi_annually':
                this.nextReviewDate = new Date(now.getTime() + 180 * 24 * 60 * 60 * 1000);
                break;
            case 'annually':
                this.nextReviewDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
                break;
        }
    }
    /**
     * Get exception summary
     */
    getSummary() {
        return {
            id: this.id,
            type: this.type,
            status: this.status,
            title: this.title,
            riskLevel: this.riskLevel,
            vulnerabilityId: this.vulnerabilityId,
            assetId: this.assetId,
            isActive: this.isActive,
            isExpired: this.isExpired,
            isPending: this.isPending,
            needsReview: this.needsReview,
            isExpiringSoon: this.isExpiringSoon,
            daysUntilExpiration: this.daysUntilExpiration,
            ageInDays: this.ageInDays,
            requestedAt: this.requestedAt,
            approvedAt: this.approvedAt,
            expirationDate: this.expirationDate,
            nextReviewDate: this.nextReviewDate,
            tags: this.tags,
        };
    }
    /**
     * Export exception for reporting
     */
    exportForReporting() {
        return {
            exception: this.getSummary(),
            justification: this.justification,
            businessImpact: this.businessImpact,
            compensatingControls: this.compensatingControls,
            mitigations: this.mitigations,
            conditions: this.conditions,
            approvalWorkflow: this.approvalWorkflow,
            customAttributes: this.customAttributes,
            exportedAt: new Date().toISOString(),
        };
    }
};
exports.VulnerabilityException = VulnerabilityException;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], VulnerabilityException.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['risk_acceptance', 'false_positive', 'compensating_control', 'business_justification', 'technical_limitation'],
    }),
    __metadata("design:type", String)
], VulnerabilityException.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['pending', 'approved', 'rejected', 'expired', 'revoked'],
        default: 'pending',
    }),
    __metadata("design:type", String)
], VulnerabilityException.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], VulnerabilityException.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], VulnerabilityException.prototype, "justification", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'business_impact', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityException.prototype, "businessImpact", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'risk_level',
        type: 'enum',
        enum: ['low', 'medium', 'high', 'critical'],
    }),
    __metadata("design:type", String)
], VulnerabilityException.prototype, "riskLevel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'compensating_controls', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Array !== "undefined" && Array) === "function" ? _a : Object)
], VulnerabilityException.prototype, "compensatingControls", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Array !== "undefined" && Array) === "function" ? _b : Object)
], VulnerabilityException.prototype, "mitigations", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], VulnerabilityException.prototype, "conditions", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'approval_workflow', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], VulnerabilityException.prototype, "approvalWorkflow", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'expiration_date', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], VulnerabilityException.prototype, "expirationDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'requested_at', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], VulnerabilityException.prototype, "requestedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'approved_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], VulnerabilityException.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_reviewed_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], VulnerabilityException.prototype, "lastReviewedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'next_review_date', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_g = typeof Date !== "undefined" && Date) === "function" ? _g : Object)
], VulnerabilityException.prototype, "nextReviewDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'requested_by', type: 'uuid' }),
    __metadata("design:type", String)
], VulnerabilityException.prototype, "requestedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'approved_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityException.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'reviewed_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityException.prototype, "reviewedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], VulnerabilityException.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'custom_attributes', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_h = typeof Record !== "undefined" && Record) === "function" ? _h : Object)
], VulnerabilityException.prototype, "customAttributes", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_j = typeof Date !== "undefined" && Date) === "function" ? _j : Object)
], VulnerabilityException.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_k = typeof Date !== "undefined" && Date) === "function" ? _k : Object)
], VulnerabilityException.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => vulnerability_entity_1.Vulnerability, vulnerability => vulnerability.exceptions, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'vulnerability_id' }),
    __metadata("design:type", typeof (_l = typeof vulnerability_entity_1.Vulnerability !== "undefined" && vulnerability_entity_1.Vulnerability) === "function" ? _l : Object)
], VulnerabilityException.prototype, "vulnerability", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vulnerability_id', type: 'uuid' }),
    __metadata("design:type", String)
], VulnerabilityException.prototype, "vulnerabilityId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => asset_entity_1.Asset, { nullable: true, onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'asset_id' }),
    __metadata("design:type", typeof (_m = typeof asset_entity_1.Asset !== "undefined" && asset_entity_1.Asset) === "function" ? _m : Object)
], VulnerabilityException.prototype, "asset", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'asset_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityException.prototype, "assetId", void 0);
exports.VulnerabilityException = VulnerabilityException = __decorate([
    (0, typeorm_1.Entity)('vulnerability_exceptions'),
    (0, typeorm_1.Index)(['vulnerabilityId']),
    (0, typeorm_1.Index)(['assetId']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['expirationDate']),
    (0, typeorm_1.Index)(['riskLevel'])
], VulnerabilityException);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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