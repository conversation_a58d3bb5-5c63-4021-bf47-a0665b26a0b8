{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\event\\correlated-event.entity.ts", "mappings": ";;;AAAA,8FAAyF;AAIzF,6EAAoE;AACpE,kFAA6E;AA4C7E;;GAEG;AACH,IAAY,eAQX;AARD,WAAY,eAAe;IACzB,wCAAqB,CAAA;IACrB,sCAAmB,CAAA;IACnB,4CAAyB,CAAA;IACzB,0CAAuB,CAAA;IACvB,wCAAqB,CAAA;IACrB,gDAA6B,CAAA;IAC7B,oCAAiB,CAAA;AACnB,CAAC,EARW,eAAe,+BAAf,eAAe,QAQ1B;AA0KD;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAa,eAAgB,SAAQ,uCAAuC;IAM1E,YAAY,KAA2B,EAAE,EAAmB;QAC1D,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACnB,CAAC;IAES,QAAQ;QAChB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3E,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC;QAC1D,IAAI,WAAW,GAAG,eAAe,CAAC,0BAA0B,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,iCAAiC,eAAe,CAAC,0BAA0B,SAAS,CAAC,CAAC;QACxG,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,uCAAe,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,6BAA6B,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC,qBAAqB;YACxD,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC,qBAAqB,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,qCAAqC,eAAe,CAAC,qBAAqB,QAAQ,eAAe,CAAC,qBAAqB,EAAE,CAAC,CAAC;QAC7I,CAAC;QAED,4BAA4B;QAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;YAClF,MAAM,IAAI,KAAK,CAAC,6BAA6B,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CACX,YAA2B,EAC3B,aAA8B,EAC9B,mBAAwC,EACxC,mBAAwC,EACxC,OAEC;QAED,MAAM,UAAU,GAAG,eAAe,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;QAC5E,MAAM,KAAK,GAAG,eAAe,CAAC,cAAc,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;QAEvF,MAAM,KAAK,GAAyB;YAClC,cAAc,EAAE,YAAY,CAAC,EAAE;YAC/B,eAAe,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;YACrD,mBAAmB;YACnB,mBAAmB;YACnB,UAAU;YACV,KAAK;YACL,MAAM,EAAE,OAAO,EAAE,MAAM,IAAI,EAAE;SAC9B,CAAC;QAEF,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;QAEnD,uBAAuB;QACvB,eAAe,CAAC,cAAc,CAAC,IAAI,+CAAqB,CACtD,eAAe,CAAC,EAAE,EAClB;YACE,aAAa,EAAE,eAAe,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC5C,cAAc,EAAE,YAAY,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC1C,eAAe,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;YACxD,eAAe,EAAE,mBAAmB,CAAC,IAAI;YACzC,UAAU,EAAE,UAAU;YACtB,KAAK;YACL,UAAU,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM;YACpC,oBAAoB,EAAE,mBAAmB,CAAC,gBAAgB,CAAC,MAAM;YACjE,cAAc,EAAE,CAAC,CAAC,mBAAmB,CAAC,WAAW;YACjD,sBAAsB,EAAE,CAAC,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,UAAU;YAC7E,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CACF,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,QAA6B;QAC9D,IAAI,eAAe,GAAG,CAAC,CAAC;QAExB,gCAAgC;QAChC,MAAM,oBAAoB,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;YACvD,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM;YACxF,CAAC,CAAC,CAAC,CAAC;QACN,eAAe,IAAI,oBAAoB,GAAG,GAAG,CAAC;QAE9C,oCAAoC;QACpC,eAAe,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAEvE,mCAAmC;QACnC,MAAM,cAAc,GAAG;YACrB,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,EAAE;YAC/B,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE;YAC9B,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,EAAE;YAC7B,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,EAAE;YAChC,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE;YAC9B,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE,EAAE;YAClC,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE;SAC7B,CAAC;QACF,eAAe,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAEvD,iDAAiD;QACjD,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACzB,eAAe,IAAI,QAAQ,CAAC,WAAW,CAAC,UAAU,GAAG,GAAG,CAAC;QAC3D,CAAC;QACD,IAAI,QAAQ,CAAC,mBAAmB,EAAE,CAAC;YACjC,eAAe,IAAI,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,GAAG,GAAG,CAAC;QAC3E,CAAC;QAED,OAAO,uCAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC;IAC1E,CAAC;IAEO,MAAM,CAAC,cAAc,CAC3B,QAA6B,EAC7B,QAA6B;QAE7B,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,mCAAmC;QACnC,MAAM,kBAAkB,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;YACrD,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM;YACtF,CAAC,CAAC,CAAC,CAAC;QACN,KAAK,IAAI,kBAAkB,GAAG,EAAE,CAAC;QAEjC,+BAA+B;QAC/B,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAE5D,sCAAsC;QACtC,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACzB,KAAK,IAAI,QAAQ,CAAC,WAAW,CAAC,YAAY,GAAG,GAAG,CAAC;QACnD,CAAC;QAED,iCAAiC;QACjC,IAAI,QAAQ,CAAC,mBAAmB,EAAE,CAAC;YACjC,KAAK,IAAI,QAAQ,CAAC,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC;QAC1D,CAAC;QAED,+BAA+B;QAC/B,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YAChC,MAAM,YAAY,GAAG,CAAC,QAAQ,CAAC,kBAAkB,CAAC,gBAAgB;gBAC7C,QAAQ,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAC1E,KAAK,IAAI,YAAY,GAAG,GAAG,CAAC;QAC9B,CAAC;QAED,qBAAqB;QACrB,MAAM,YAAY,GAAG,QAAQ,CAAC,kBAAkB,CAAC,cAAc,GAAG,CAAC,CAAC;QACpE,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,YAAY,CAAC,CAAC;QAE1C,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,eAAe,CAAC,yBAAyB,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,WAAW,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,UAAU,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,OAAO,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,sBAAsB,EAAE;YAC7B,CAAC,IAAI,CAAC,eAAe,KAAK,eAAe,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;IACxF,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,IAAI,IAAI,CAAC,0BAA0B,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YACjE,OAAO,UAAU,CAAC;QACpB,CAAC;QACD,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAC3D,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC1D,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,+BAA+B;QAC7B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YAC1B,eAAe,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAClE,eAAe,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACvD,eAAe,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAClC,eAAe,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACnD,eAAe,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YACjE,eAAe,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,eAAe,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YACvE,eAAe,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,eAAe,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YACtD,eAAe,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,eAAe,CAAC,QAAQ,EAAE,CAAC;YACtD,eAAe,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YACtD,eAAe,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,qBAAqB;QAUnB,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,eAAe;YAC1B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YACrC,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACrD,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,MAAM;YAC5E,cAAc,EAAE,IAAI,CAAC,2BAA2B,EAAE;SACnD,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YACtB,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,EAAE;YACpD,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;YACpE,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;YACnD,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;YACnD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,QAAQ,EAAE;gBACR,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBACzC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;gBACrC,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,EAAE;gBACrD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAC/C,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAC/C,0BAA0B,EAAE,IAAI,CAAC,0BAA0B,EAAE;gBAC7D,wBAAwB,EAAE,IAAI,CAAC,2BAA2B,EAAE;gBAC5D,4BAA4B,EAAE,IAAI,CAAC,+BAA+B,EAAE;gBACpE,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;aACjD;YACD,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE;YACxC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE;SACzC,CAAC;IACJ,CAAC;;AAnYH,0CAoYC;AAnYyB,0CAA0B,GAAG,CAAC,CAAC;AAC/B,qCAAqB,GAAG,CAAC,CAAC;AAC1B,qCAAqB,GAAG,GAAG,CAAC;AAC5B,yCAAyB,GAAG,EAAE,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\event\\correlated-event.entity.ts"], "sourcesContent": ["import { BaseAggregateRoot } from '../../../../shared-kernel/domain/base-aggregate-root';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { EnrichedEvent } from './enriched-event.entity';\r\nimport { IOC } from '../../value-objects/threat-indicators/ioc.value-object';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\nimport { EventsCorrelatedEvent } from '../../events/events-correlated.event';\r\n\r\n/**\r\n * Correlated Event Properties\r\n */\r\nexport interface CorrelatedEventProps {\r\n  /** Reference to the primary enriched event */\r\n  primaryEventId: UniqueEntityId;\r\n  /** Related enriched event IDs */\r\n  relatedEventIds: UniqueEntityId[];\r\n  /** Correlation analysis results */\r\n  correlationAnalysis: CorrelationAnalysis;\r\n  /** Correlation metadata */\r\n  correlationMetadata: CorrelationMetadata;\r\n  /** Correlation confidence level */\r\n  confidence: ConfidenceLevel;\r\n  /** Correlation score (0-100) */\r\n  score: number;\r\n  /** Correlation errors if any */\r\n  errors: CorrelationError[];\r\n}\r\n\r\n/**\r\n * Correlation Analysis\r\n */\r\nexport interface CorrelationAnalysis {\r\n  /** Correlation type */\r\n  type: CorrelationType;\r\n  /** Correlation patterns found */\r\n  patterns: CorrelationPattern[];\r\n  /** Common indicators across events */\r\n  commonIndicators: IOC[];\r\n  /** Temporal correlation data */\r\n  temporalCorrelation?: TemporalCorrelation;\r\n  /** Spatial correlation data */\r\n  spatialCorrelation?: SpatialCorrelation;\r\n  /** Behavioral correlation data */\r\n  behavioralCorrelation?: BehavioralCorrelation;\r\n  /** Campaign correlation data */\r\n  campaignCorrelation?: CampaignCorrelation;\r\n  /** Attack chain analysis */\r\n  attackChain?: AttackChainAnalysis;\r\n}\r\n\r\n/**\r\n * Correlation Type\r\n */\r\nexport enum CorrelationType {\r\n  TEMPORAL = 'temporal',\r\n  SPATIAL = 'spatial',\r\n  BEHAVIORAL = 'behavioral',\r\n  INDICATOR = 'indicator',\r\n  CAMPAIGN = 'campaign',\r\n  ATTACK_CHAIN = 'attack_chain',\r\n  HYBRID = 'hybrid',\r\n}\r\n\r\n/**\r\n * Correlation Pattern\r\n */\r\nexport interface CorrelationPattern {\r\n  /** Pattern type */\r\n  type: string;\r\n  /** Pattern description */\r\n  description: string;\r\n  /** Pattern confidence (0-100) */\r\n  confidence: number;\r\n  /** Events matching this pattern */\r\n  matchingEvents: string[];\r\n  /** Pattern attributes */\r\n  attributes: Record<string, any>;\r\n  /** Pattern strength (0-1) */\r\n  strength: number;\r\n}\r\n\r\n/**\r\n * Temporal Correlation\r\n */\r\nexport interface TemporalCorrelation {\r\n  /** Time window in milliseconds */\r\n  timeWindow: number;\r\n  /** Event sequence */\r\n  sequence: Array<{\r\n    eventId: string;\r\n    timestamp: Date;\r\n    order: number;\r\n  }>;\r\n  /** Time gaps between events */\r\n  timeGaps: number[];\r\n  /** Temporal pattern type */\r\n  patternType: 'burst' | 'periodic' | 'sequential' | 'simultaneous';\r\n  /** Pattern regularity score (0-100) */\r\n  regularity: number;\r\n}\r\n\r\n/**\r\n * Spatial Correlation\r\n */\r\nexport interface SpatialCorrelation {\r\n  /** Common network segments */\r\n  commonSegments: string[];\r\n  /** Common geographic locations */\r\n  commonLocations: string[];\r\n  /** Network proximity score (0-100) */\r\n  networkProximity: number;\r\n  /** Geographic proximity score (0-100) */\r\n  geographicProximity: number;\r\n  /** Asset relationships */\r\n  assetRelationships: Array<{\r\n    type: 'same_subnet' | 'same_vlan' | 'same_location' | 'trust_relationship';\r\n    assets: string[];\r\n    strength: number;\r\n  }>;\r\n}\r\n\r\n/**\r\n * Behavioral Correlation\r\n */\r\nexport interface BehavioralCorrelation {\r\n  /** Common user behaviors */\r\n  commonBehaviors: string[];\r\n  /** Anomaly patterns */\r\n  anomalyPatterns: string[];\r\n  /** Behavioral similarity score (0-100) */\r\n  similarityScore: number;\r\n  /** Deviation from baseline */\r\n  baselineDeviation: number;\r\n  /** Behavioral clusters */\r\n  clusters: Array<{\r\n    name: string;\r\n    events: string[];\r\n    characteristics: string[];\r\n  }>;\r\n}\r\n\r\n/**\r\n * Campaign Correlation\r\n */\r\nexport interface CampaignCorrelation {\r\n  /** Campaign identifier */\r\n  campaignId?: string;\r\n  /** Campaign name */\r\n  campaignName?: string;\r\n  /** Threat actor attribution */\r\n  threatActor?: string;\r\n  /** Campaign confidence (0-100) */\r\n  campaignConfidence: number;\r\n  /** Common TTPs */\r\n  commonTTPs: string[];\r\n  /** Campaign timeline */\r\n  timeline: {\r\n    start: Date;\r\n    end?: Date;\r\n    phases: Array<{\r\n      name: string;\r\n      start: Date;\r\n      end?: Date;\r\n      events: string[];\r\n    }>;\r\n  };\r\n}\r\n\r\n/**\r\n * Attack Chain Analysis\r\n */\r\nexport interface AttackChainAnalysis {\r\n  /** Attack phases identified */\r\n  phases: Array<{\r\n    name: string;\r\n    order: number;\r\n    events: string[];\r\n    techniques: string[];\r\n    confidence: number;\r\n  }>;\r\n  /** Kill chain mapping */\r\n  killChainMapping: Record<string, string[]>;\r\n  /** Chain completeness (0-100) */\r\n  completeness: number;\r\n  /** Chain confidence (0-100) */\r\n  confidence: number;\r\n  /** Missing phases */\r\n  missingPhases: string[];\r\n}\r\n\r\n/**\r\n * Correlation Metadata\r\n */\r\nexport interface CorrelationMetadata {\r\n  /** Correlation engine version */\r\n  engineVersion: string;\r\n  /** Correlation algorithms used */\r\n  algorithmsUsed: string[];\r\n  /** Processing duration in milliseconds */\r\n  processingDuration: number;\r\n  /** Correlated at timestamp */\r\n  correlatedAt: Date;\r\n  /** Data sources used */\r\n  dataSources: string[];\r\n  /** Correlation rules applied */\r\n  rulesApplied: string[];\r\n  /** Performance metrics */\r\n  performanceMetrics: {\r\n    eventsProcessed: number;\r\n    correlationsFound: number;\r\n    falsePositives: number;\r\n    processingRate: number;\r\n  };\r\n}\r\n\r\n/**\r\n * Correlation Error\r\n */\r\nexport interface CorrelationError {\r\n  /** Error type */\r\n  type: 'timeout' | 'insufficient_data' | 'algorithm_failure' | 'resource_limit';\r\n  /** Error message */\r\n  message: string;\r\n  /** Affected events */\r\n  affectedEvents: string[];\r\n  /** Error severity */\r\n  severity: 'low' | 'medium' | 'high' | 'critical';\r\n  /** Recovery suggestions */\r\n  recoverySuggestions: string[];\r\n}\r\n\r\n/**\r\n * Correlated Event Entity\r\n * \r\n * Represents a group of enriched events that have been correlated based on\r\n * temporal, spatial, behavioral, or indicator-based relationships.\r\n * \r\n * Key responsibilities:\r\n * - Store correlation analysis results\r\n * - Track correlation confidence and scoring\r\n * - Provide correlation pattern insights\r\n * - Support attack chain reconstruction\r\n * - Enable campaign attribution\r\n * \r\n * Business Rules:\r\n * - Must reference at least two enriched events\r\n * - Correlation score must reflect analysis quality\r\n * - High-confidence correlations trigger threat escalation\r\n * - Attack chain analysis requires temporal ordering\r\n */\r\nexport class CorrelatedEvent extends BaseAggregateRoot<CorrelatedEventProps> {\r\n  private static readonly MIN_EVENTS_FOR_CORRELATION = 2;\r\n  private static readonly MIN_CORRELATION_SCORE = 0;\r\n  private static readonly MAX_CORRELATION_SCORE = 100;\r\n  private static readonly HIGH_CONFIDENCE_THRESHOLD = 80;\r\n\r\n  constructor(props: CorrelatedEventProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n  }\r\n\r\n  protected validate(): void {\r\n    if (!this.props.primaryEventId) {\r\n      throw new Error('Correlated event must have a primary event');\r\n    }\r\n\r\n    if (!this.props.relatedEventIds || this.props.relatedEventIds.length === 0) {\r\n      throw new Error('Correlated event must have related events');\r\n    }\r\n\r\n    const totalEvents = 1 + this.props.relatedEventIds.length;\r\n    if (totalEvents < CorrelatedEvent.MIN_EVENTS_FOR_CORRELATION) {\r\n      throw new Error(`Correlation requires at least ${CorrelatedEvent.MIN_EVENTS_FOR_CORRELATION} events`);\r\n    }\r\n\r\n    if (!this.props.correlationAnalysis) {\r\n      throw new Error('Correlated event must have correlation analysis');\r\n    }\r\n\r\n    if (!this.props.correlationMetadata) {\r\n      throw new Error('Correlated event must have correlation metadata');\r\n    }\r\n\r\n    if (!Object.values(ConfidenceLevel).includes(this.props.confidence)) {\r\n      throw new Error(`Invalid confidence level: ${this.props.confidence}`);\r\n    }\r\n\r\n    if (this.props.score < CorrelatedEvent.MIN_CORRELATION_SCORE || \r\n        this.props.score > CorrelatedEvent.MAX_CORRELATION_SCORE) {\r\n      throw new Error(`Correlation score must be between ${CorrelatedEvent.MIN_CORRELATION_SCORE} and ${CorrelatedEvent.MAX_CORRELATION_SCORE}`);\r\n    }\r\n\r\n    // Validate correlation type\r\n    if (!Object.values(CorrelationType).includes(this.props.correlationAnalysis.type)) {\r\n      throw new Error(`Invalid correlation type: ${this.props.correlationAnalysis.type}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a correlated event from enriched events\r\n   */\r\n  static create(\r\n    primaryEvent: EnrichedEvent,\r\n    relatedEvents: EnrichedEvent[],\r\n    correlationAnalysis: CorrelationAnalysis,\r\n    correlationMetadata: CorrelationMetadata,\r\n    options?: {\r\n      errors?: CorrelationError[];\r\n    }\r\n  ): CorrelatedEvent {\r\n    const confidence = CorrelatedEvent.calculateConfidence(correlationAnalysis);\r\n    const score = CorrelatedEvent.calculateScore(correlationAnalysis, correlationMetadata);\r\n\r\n    const props: CorrelatedEventProps = {\r\n      primaryEventId: primaryEvent.id,\r\n      relatedEventIds: relatedEvents.map(event => event.id),\r\n      correlationAnalysis,\r\n      correlationMetadata,\r\n      confidence,\r\n      score,\r\n      errors: options?.errors || [],\r\n    };\r\n\r\n    const correlatedEvent = new CorrelatedEvent(props);\r\n\r\n    // Publish domain event\r\n    correlatedEvent.addDomainEvent(new EventsCorrelatedEvent(\r\n      correlatedEvent.id,\r\n      {\r\n        correlationId: correlatedEvent.id.toString(),\r\n        primaryEventId: primaryEvent.id.toString(),\r\n        relatedEventIds: relatedEvents.map(e => e.id.toString()),\r\n        correlationType: correlationAnalysis.type,\r\n        confidence: confidence,\r\n        score,\r\n        eventCount: 1 + relatedEvents.length,\r\n        commonIndicatorCount: correlationAnalysis.commonIndicators.length,\r\n        hasAttackChain: !!correlationAnalysis.attackChain,\r\n        hasCampaignAttribution: !!correlationAnalysis.campaignCorrelation?.campaignId,\r\n        timestamp: new Date().toISOString(),\r\n      }\r\n    ));\r\n\r\n    return correlatedEvent;\r\n  }\r\n\r\n  private static calculateConfidence(analysis: CorrelationAnalysis): ConfidenceLevel {\r\n    let confidenceScore = 0;\r\n\r\n    // Base confidence from patterns\r\n    const avgPatternConfidence = analysis.patterns.length > 0\r\n      ? analysis.patterns.reduce((sum, p) => sum + p.confidence, 0) / analysis.patterns.length\r\n      : 0;\r\n    confidenceScore += avgPatternConfidence * 0.4;\r\n\r\n    // Confidence from common indicators\r\n    confidenceScore += Math.min(analysis.commonIndicators.length * 10, 30);\r\n\r\n    // Confidence from correlation type\r\n    const typeConfidence = {\r\n      [CorrelationType.INDICATOR]: 20,\r\n      [CorrelationType.TEMPORAL]: 15,\r\n      [CorrelationType.SPATIAL]: 15,\r\n      [CorrelationType.BEHAVIORAL]: 10,\r\n      [CorrelationType.CAMPAIGN]: 25,\r\n      [CorrelationType.ATTACK_CHAIN]: 30,\r\n      [CorrelationType.HYBRID]: 20,\r\n    };\r\n    confidenceScore += typeConfidence[analysis.type] || 10;\r\n\r\n    // Bonus for attack chain or campaign correlation\r\n    if (analysis.attackChain) {\r\n      confidenceScore += analysis.attackChain.confidence * 0.2;\r\n    }\r\n    if (analysis.campaignCorrelation) {\r\n      confidenceScore += analysis.campaignCorrelation.campaignConfidence * 0.2;\r\n    }\r\n\r\n    return ConfidenceLevel.fromNumericValue(Math.min(100, confidenceScore));\r\n  }\r\n\r\n  private static calculateScore(\r\n    analysis: CorrelationAnalysis,\r\n    metadata: CorrelationMetadata\r\n  ): number {\r\n    let score = 0;\r\n\r\n    // Base score from pattern strength\r\n    const avgPatternStrength = analysis.patterns.length > 0\r\n      ? analysis.patterns.reduce((sum, p) => sum + p.strength, 0) / analysis.patterns.length\r\n      : 0;\r\n    score += avgPatternStrength * 40;\r\n\r\n    // Score from common indicators\r\n    score += Math.min(analysis.commonIndicators.length * 5, 25);\r\n\r\n    // Score from correlation completeness\r\n    if (analysis.attackChain) {\r\n      score += analysis.attackChain.completeness * 0.2;\r\n    }\r\n\r\n    // Score from temporal regularity\r\n    if (analysis.temporalCorrelation) {\r\n      score += analysis.temporalCorrelation.regularity * 0.15;\r\n    }\r\n\r\n    // Score from spatial proximity\r\n    if (analysis.spatialCorrelation) {\r\n      const avgProximity = (analysis.spatialCorrelation.networkProximity + \r\n                           analysis.spatialCorrelation.geographicProximity) / 2;\r\n      score += avgProximity * 0.1;\r\n    }\r\n\r\n    // Penalty for errors\r\n    const errorPenalty = metadata.performanceMetrics.falsePositives * 2;\r\n    score = Math.max(0, score - errorPenalty);\r\n\r\n    return Math.min(100, Math.round(score));\r\n  }\r\n\r\n  /**\r\n   * Get primary event ID\r\n   */\r\n  get primaryEventId(): UniqueEntityId {\r\n    return this.props.primaryEventId;\r\n  }\r\n\r\n  /**\r\n   * Get related event IDs\r\n   */\r\n  get relatedEventIds(): UniqueEntityId[] {\r\n    return [...this.props.relatedEventIds];\r\n  }\r\n\r\n  /**\r\n   * Get all event IDs (primary + related)\r\n   */\r\n  get allEventIds(): UniqueEntityId[] {\r\n    return [this.props.primaryEventId, ...this.props.relatedEventIds];\r\n  }\r\n\r\n  /**\r\n   * Get correlation analysis\r\n   */\r\n  get correlationAnalysis(): CorrelationAnalysis {\r\n    return this.props.correlationAnalysis;\r\n  }\r\n\r\n  /**\r\n   * Get correlation metadata\r\n   */\r\n  get correlationMetadata(): CorrelationMetadata {\r\n    return this.props.correlationMetadata;\r\n  }\r\n\r\n  /**\r\n   * Get confidence level\r\n   */\r\n  get confidence(): ConfidenceLevel {\r\n    return this.props.confidence;\r\n  }\r\n\r\n  /**\r\n   * Get correlation score\r\n   */\r\n  get score(): number {\r\n    return this.props.score;\r\n  }\r\n\r\n  /**\r\n   * Get correlation errors\r\n   */\r\n  get errors(): CorrelationError[] {\r\n    return [...this.props.errors];\r\n  }\r\n\r\n  /**\r\n   * Get correlation type\r\n   */\r\n  get correlationType(): CorrelationType {\r\n    return this.props.correlationAnalysis.type;\r\n  }\r\n\r\n  /**\r\n   * Get event count\r\n   */\r\n  get eventCount(): number {\r\n    return 1 + this.props.relatedEventIds.length;\r\n  }\r\n\r\n  /**\r\n   * Check if correlation is high confidence\r\n   */\r\n  isHighConfidence(): boolean {\r\n    return this.props.score >= CorrelatedEvent.HIGH_CONFIDENCE_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Check if correlation has attack chain\r\n   */\r\n  hasAttackChain(): boolean {\r\n    return !!this.props.correlationAnalysis.attackChain;\r\n  }\r\n\r\n  /**\r\n   * Check if correlation has campaign attribution\r\n   */\r\n  hasCampaignAttribution(): boolean {\r\n    return !!this.props.correlationAnalysis.campaignCorrelation?.campaignId;\r\n  }\r\n\r\n  /**\r\n   * Check if correlation has common indicators\r\n   */\r\n  hasCommonIndicators(): boolean {\r\n    return this.props.correlationAnalysis.commonIndicators.length > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if correlation spans multiple assets\r\n   */\r\n  spansMultipleAssets(): boolean {\r\n    return this.props.correlationAnalysis.spatialCorrelation?.assetRelationships.length > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if correlation indicates coordinated attack\r\n   */\r\n  indicatesCoordinatedAttack(): boolean {\r\n    return this.hasAttackChain() || \r\n           this.hasCampaignAttribution() || \r\n           (this.correlationType === CorrelationType.TEMPORAL && this.isHighConfidence());\r\n  }\r\n\r\n  /**\r\n   * Get threat escalation priority\r\n   */\r\n  getThreatEscalationPriority(): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (this.indicatesCoordinatedAttack() && this.isHighConfidence()) {\r\n      return 'critical';\r\n    }\r\n    if (this.hasAttackChain() || this.hasCampaignAttribution()) {\r\n      return 'high';\r\n    }\r\n    if (this.isHighConfidence() && this.hasCommonIndicators()) {\r\n      return 'medium';\r\n    }\r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get investigation recommendations\r\n   */\r\n  getInvestigationRecommendations(): string[] {\r\n    const recommendations: string[] = [];\r\n\r\n    if (this.hasAttackChain()) {\r\n      recommendations.push('Analyze complete attack chain progression');\r\n      recommendations.push('Identify missing attack phases');\r\n      recommendations.push('Assess potential future attack steps');\r\n    }\r\n\r\n    if (this.hasCampaignAttribution()) {\r\n      recommendations.push('Research threat actor TTPs');\r\n      recommendations.push('Check for additional campaign indicators');\r\n      recommendations.push('Review historical campaign activities');\r\n    }\r\n\r\n    if (this.hasCommonIndicators()) {\r\n      recommendations.push('Investigate all systems with common indicators');\r\n      recommendations.push('Expand IOC hunting across environment');\r\n    }\r\n\r\n    if (this.spansMultipleAssets()) {\r\n      recommendations.push('Assess lateral movement paths');\r\n      recommendations.push('Review network segmentation effectiveness');\r\n    }\r\n\r\n    if (this.correlationType === CorrelationType.TEMPORAL) {\r\n      recommendations.push('Analyze event timing patterns');\r\n      recommendations.push('Look for additional events in time window');\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Get correlation summary\r\n   */\r\n  getCorrelationSummary(): {\r\n    type: string;\r\n    eventCount: number;\r\n    confidence: string;\r\n    score: number;\r\n    hasAttackChain: boolean;\r\n    hasCampaignAttribution: boolean;\r\n    commonIndicatorCount: number;\r\n    threatPriority: string;\r\n  } {\r\n    return {\r\n      type: this.correlationType,\r\n      eventCount: this.eventCount,\r\n      confidence: this.confidence,\r\n      score: this.score,\r\n      hasAttackChain: this.hasAttackChain(),\r\n      hasCampaignAttribution: this.hasCampaignAttribution(),\r\n      commonIndicatorCount: this.props.correlationAnalysis.commonIndicators.length,\r\n      threatPriority: this.getThreatEscalationPriority(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      id: this.id.toString(),\r\n      primaryEventId: this.props.primaryEventId.toString(),\r\n      relatedEventIds: this.props.relatedEventIds.map(id => id.toString()),\r\n      correlationAnalysis: this.props.correlationAnalysis,\r\n      correlationMetadata: this.props.correlationMetadata,\r\n      confidence: this.props.confidence,\r\n      score: this.props.score,\r\n      errors: this.props.errors,\r\n      analysis: {\r\n        isHighConfidence: this.isHighConfidence(),\r\n        hasAttackChain: this.hasAttackChain(),\r\n        hasCampaignAttribution: this.hasCampaignAttribution(),\r\n        hasCommonIndicators: this.hasCommonIndicators(),\r\n        spansMultipleAssets: this.spansMultipleAssets(),\r\n        indicatesCoordinatedAttack: this.indicatesCoordinatedAttack(),\r\n        threatEscalationPriority: this.getThreatEscalationPriority(),\r\n        investigationRecommendations: this.getInvestigationRecommendations(),\r\n        correlationSummary: this.getCorrelationSummary(),\r\n      },\r\n      createdAt: this.createdAt?.toISOString(),\r\n      updatedAt: this.updatedAt?.toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}