{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\event-processing-status-changed.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAC5E,wFAA0G;AAc1G;;;;;;;;;;GAUG;AACH,MAAa,uCAAwC,SAAQ,+BAAsD;IACjH,YACE,WAA2B,EAC3B,SAAgD,EAChD,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,yDAA0B,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,yDAA0B,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,yDAA0B,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,yDAA0B,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,yDAA0B,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,MAAM,gBAAgB,GAAG;YACvB,oDAAqB,CAAC,GAAG;YACzB,oDAAqB,CAAC,WAAW;YACjC,oDAAqB,CAAC,UAAU;YAChC,oDAAqB,CAAC,SAAS;YAC/B,oDAAqB,CAAC,QAAQ;YAC9B,oDAAqB,CAAC,WAAW;YACjC,oDAAqB,CAAC,UAAU;YAChC,oDAAqB,CAAC,SAAS;YAC/B,oDAAqB,CAAC,QAAQ;YAC9B,oDAAqB,CAAC,cAAc;YACpC,oDAAqB,CAAC,aAAa;YACnC,oDAAqB,CAAC,QAAQ;YAC9B,oDAAqB,CAAC,QAAQ;SAC/B,CAAC;QAEF,MAAM,QAAQ,GAAG,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1D,MAAM,QAAQ,GAAG,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE1D,OAAO,QAAQ,KAAK,CAAC,CAAC,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAI,QAAQ,GAAG,QAAQ,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,SAAS,KAAK,oDAAqB,CAAC,YAAY;YACrD,CAAC,IAAI,CAAC,SAAS,KAAK,oDAAqB,CAAC,MAAM;gBAC/C,CAAC,oDAAqB,CAAC,GAAG,EAAE,oDAAqB,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IACnG,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,eAAe,GAAG,CAAC,oDAAqB,CAAC,GAAG,CAAC,CAAC;QACpD,MAAM,gBAAgB,GAAG;YACvB,oDAAqB,CAAC,WAAW;YACjC,oDAAqB,CAAC,UAAU;YAChC,oDAAqB,CAAC,SAAS;YAC/B,oDAAqB,CAAC,QAAQ;YAC9B,oDAAqB,CAAC,WAAW;YACjC,oDAAqB,CAAC,UAAU;SACjC,CAAC;QACF,MAAM,cAAc,GAAG;YACrB,oDAAqB,CAAC,SAAS;YAC/B,oDAAqB,CAAC,QAAQ;SAC/B,CAAC;QACF,MAAM,gBAAgB,GAAG;YACvB,oDAAqB,CAAC,cAAc;YACpC,oDAAqB,CAAC,aAAa;YACnC,oDAAqB,CAAC,QAAQ;SAC/B,CAAC;QACF,MAAM,aAAa,GAAG,CAAC,oDAAqB,CAAC,QAAQ,CAAC,CAAC;QACvD,MAAM,WAAW,GAAG;YAClB,oDAAqB,CAAC,MAAM;YAC5B,oDAAqB,CAAC,OAAO;YAC7B,oDAAqB,CAAC,OAAO;SAC9B,CAAC;QAEF,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;YAAE,OAAO,WAAW,CAAC;QACjE,IAAI,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;YAAE,OAAO,YAAY,CAAC;QACnE,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;YAAE,OAAO,UAAU,CAAC;QAC/D,IAAI,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;YAAE,OAAO,YAAY,CAAC;QACnE,IAAI,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC;QAC7D,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;YAAE,OAAO,OAAO,CAAC;QACzD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,IAAI,IAAI,CAAC,SAAS,EAAE;YAAE,OAAO,MAAM,CAAC;QACpC,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAAE,OAAO,QAAQ,CAAC;QAC9C,IAAI,IAAI,CAAC,YAAY,EAAE;YAAE,OAAO,KAAK,CAAC;QACtC,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,kEAAkE;QAClE,OAAO,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,oBAAoB;QAUlB,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACjD,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7C,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAC3C,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,iCAAiC;SACjE,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gBAAgB;QAgBd,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YACpC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,oBAAoB,EAAE,yDAA0B,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC;YAC/E,oBAAoB,EAAE,yDAA0B,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC;YAC/E,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,QAAQ,EAAE,IAAI,CAAC,uBAAuB,EAAE;YACxC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACjD,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;YACjC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE;YAC3B,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;YACjC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAC3C,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;SACxC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE;SAC/C,CAAC;IACJ,CAAC;CACF;AA3OD,0FA2OC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\event-processing-status-changed.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventProcessingStatus, EventProcessingStatusUtils } from '../enums/event-processing-status.enum';\r\n\r\n/**\r\n * Event Processing Status Changed Domain Event Data\r\n */\r\nexport interface EventProcessingStatusChangedEventData {\r\n  /** Previous processing status of the event */\r\n  oldStatus: EventProcessingStatus;\r\n  /** New processing status of the event */\r\n  newStatus: EventProcessingStatus;\r\n  /** When the processing status was changed */\r\n  timestamp: Date;\r\n}\r\n\r\n/**\r\n * Event Processing Status Changed Domain Event\r\n * \r\n * Raised when a security event's processing status changes in the pipeline.\r\n * This event triggers various downstream processes including:\r\n * - Pipeline stage transitions\r\n * - Processing metrics updates\r\n * - Performance monitoring\r\n * - Error handling and retries\r\n * - Processing notifications\r\n */\r\nexport class EventProcessingStatusChangedDomainEvent extends BaseDomainEvent<EventProcessingStatusChangedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: EventProcessingStatusChangedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the previous processing status\r\n   */\r\n  get oldStatus(): EventProcessingStatus {\r\n    return this.eventData.oldStatus;\r\n  }\r\n\r\n  /**\r\n   * Get the new processing status\r\n   */\r\n  get newStatus(): EventProcessingStatus {\r\n    return this.eventData.newStatus;\r\n  }\r\n\r\n  /**\r\n   * Get when the processing status was changed\r\n   */\r\n  get timestamp(): Date {\r\n    return this.eventData.timestamp;\r\n  }\r\n\r\n  /**\r\n   * Check if processing completed successfully\r\n   */\r\n  wasCompleted(): boolean {\r\n    return EventProcessingStatusUtils.isSuccess(this.newStatus);\r\n  }\r\n\r\n  /**\r\n   * Check if processing failed\r\n   */\r\n  hasFailed(): boolean {\r\n    return EventProcessingStatusUtils.isFailure(this.newStatus);\r\n  }\r\n\r\n  /**\r\n   * Check if processing is in progress\r\n   */\r\n  isInProgress(): boolean {\r\n    return EventProcessingStatusUtils.isInProgress(this.newStatus);\r\n  }\r\n\r\n  /**\r\n   * Check if processing reached terminal state\r\n   */\r\n  isTerminal(): boolean {\r\n    return EventProcessingStatusUtils.isTerminal(this.newStatus);\r\n  }\r\n\r\n  /**\r\n   * Check if processing requires attention\r\n   */\r\n  requiresAttention(): boolean {\r\n    return EventProcessingStatusUtils.requiresAttention(this.newStatus);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a forward progression in the pipeline\r\n   */\r\n  isForwardProgression(): boolean {\r\n    const progressionOrder = [\r\n      EventProcessingStatus.RAW,\r\n      EventProcessingStatus.NORMALIZING,\r\n      EventProcessingStatus.NORMALIZED,\r\n      EventProcessingStatus.ENRICHING,\r\n      EventProcessingStatus.ENRICHED,\r\n      EventProcessingStatus.CORRELATING,\r\n      EventProcessingStatus.CORRELATED,\r\n      EventProcessingStatus.ANALYZING,\r\n      EventProcessingStatus.ANALYZED,\r\n      EventProcessingStatus.PENDING_REVIEW,\r\n      EventProcessingStatus.INVESTIGATING,\r\n      EventProcessingStatus.RESOLVED,\r\n      EventProcessingStatus.ARCHIVED,\r\n    ];\r\n\r\n    const oldIndex = progressionOrder.indexOf(this.oldStatus);\r\n    const newIndex = progressionOrder.indexOf(this.newStatus);\r\n\r\n    return oldIndex !== -1 && newIndex !== -1 && newIndex > oldIndex;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a backward movement (retry/reprocessing)\r\n   */\r\n  isBackwardMovement(): boolean {\r\n    return this.newStatus === EventProcessingStatus.REPROCESSING ||\r\n           (this.oldStatus === EventProcessingStatus.FAILED && \r\n            [EventProcessingStatus.RAW, EventProcessingStatus.NORMALIZING].includes(this.newStatus));\r\n  }\r\n\r\n  /**\r\n   * Get processing stage category\r\n   */\r\n  getStageCategory(): 'ingestion' | 'processing' | 'analysis' | 'resolution' | 'storage' | 'error' | 'other' {\r\n    const ingestionStages = [EventProcessingStatus.RAW];\r\n    const processingStages = [\r\n      EventProcessingStatus.NORMALIZING,\r\n      EventProcessingStatus.NORMALIZED,\r\n      EventProcessingStatus.ENRICHING,\r\n      EventProcessingStatus.ENRICHED,\r\n      EventProcessingStatus.CORRELATING,\r\n      EventProcessingStatus.CORRELATED,\r\n    ];\r\n    const analysisStages = [\r\n      EventProcessingStatus.ANALYZING,\r\n      EventProcessingStatus.ANALYZED,\r\n    ];\r\n    const resolutionStages = [\r\n      EventProcessingStatus.PENDING_REVIEW,\r\n      EventProcessingStatus.INVESTIGATING,\r\n      EventProcessingStatus.RESOLVED,\r\n    ];\r\n    const storageStages = [EventProcessingStatus.ARCHIVED];\r\n    const errorStages = [\r\n      EventProcessingStatus.FAILED,\r\n      EventProcessingStatus.TIMEOUT,\r\n      EventProcessingStatus.ON_HOLD,\r\n    ];\r\n\r\n    if (ingestionStages.includes(this.newStatus)) return 'ingestion';\r\n    if (processingStages.includes(this.newStatus)) return 'processing';\r\n    if (analysisStages.includes(this.newStatus)) return 'analysis';\r\n    if (resolutionStages.includes(this.newStatus)) return 'resolution';\r\n    if (storageStages.includes(this.newStatus)) return 'storage';\r\n    if (errorStages.includes(this.newStatus)) return 'error';\r\n    return 'other';\r\n  }\r\n\r\n  /**\r\n   * Get notification priority based on status change\r\n   */\r\n  getNotificationPriority(): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (this.hasFailed()) return 'high';\r\n    if (this.requiresAttention()) return 'medium';\r\n    if (this.wasCompleted()) return 'low';\r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Check if change requires notification\r\n   */\r\n  requiresNotification(): boolean {\r\n    // Notify on failures, completions, and attention-requiring states\r\n    return this.hasFailed() || this.wasCompleted() || this.requiresAttention();\r\n  }\r\n\r\n  /**\r\n   * Get processing metrics for this change\r\n   */\r\n  getProcessingMetrics(): {\r\n    oldStatus: EventProcessingStatus;\r\n    newStatus: EventProcessingStatus;\r\n    isForwardProgression: boolean;\r\n    isBackwardMovement: boolean;\r\n    stageCategory: string;\r\n    isTerminal: boolean;\r\n    requiresAttention: boolean;\r\n    processingTime?: number;\r\n  } {\r\n    return {\r\n      oldStatus: this.oldStatus,\r\n      newStatus: this.newStatus,\r\n      isForwardProgression: this.isForwardProgression(),\r\n      isBackwardMovement: this.isBackwardMovement(),\r\n      stageCategory: this.getStageCategory(),\r\n      isTerminal: this.isTerminal(),\r\n      requiresAttention: this.requiresAttention(),\r\n      processingTime: this.getAge(), // Time since this event occurred\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get change summary for handlers\r\n   */\r\n  getChangeSummary(): {\r\n    eventId: string;\r\n    oldStatus: EventProcessingStatus;\r\n    newStatus: EventProcessingStatus;\r\n    oldStatusDescription: string;\r\n    newStatusDescription: string;\r\n    stageCategory: string;\r\n    priority: string;\r\n    requiresNotification: boolean;\r\n    wasCompleted: boolean;\r\n    hasFailed: boolean;\r\n    isInProgress: boolean;\r\n    isTerminal: boolean;\r\n    requiresAttention: boolean;\r\n    timestamp: string;\r\n  } {\r\n    return {\r\n      eventId: this.aggregateId.toString(),\r\n      oldStatus: this.oldStatus,\r\n      newStatus: this.newStatus,\r\n      oldStatusDescription: EventProcessingStatusUtils.getDescription(this.oldStatus),\r\n      newStatusDescription: EventProcessingStatusUtils.getDescription(this.newStatus),\r\n      stageCategory: this.getStageCategory(),\r\n      priority: this.getNotificationPriority(),\r\n      requiresNotification: this.requiresNotification(),\r\n      wasCompleted: this.wasCompleted(),\r\n      hasFailed: this.hasFailed(),\r\n      isInProgress: this.isInProgress(),\r\n      isTerminal: this.isTerminal(),\r\n      requiresAttention: this.requiresAttention(),\r\n      timestamp: this.timestamp.toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      changeSummary: this.getChangeSummary(),\r\n      processingMetrics: this.getProcessingMetrics(),\r\n    };\r\n  }\r\n}"], "version": 3}