{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\versioning\\api-versioning.service.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAAyE;AACzE,2CAA+C;AAG/C,gFAA4E;AAC5E,oGAA+F;AA2C/F;;;GAGG;AAEI,IAAM,oBAAoB,4BAA1B,MAAM,oBAAoB;IAM/B,YACmB,aAA4B,EAC5B,aAA4B,EAC5B,YAAqC;QAFrC,kBAAa,GAAb,aAAa,CAAe;QAC5B,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAyB;QARvC,WAAM,GAAG,IAAI,eAAM,CAAC,sBAAoB,CAAC,IAAI,CAAC,CAAC;QAC/C,sBAAiB,GAAgC,IAAI,GAAG,EAAE,CAAC;QAC3D,oBAAe,GAAwB,IAAI,GAAG,EAAE,CAAC;QACjD,wBAAmB,GAAsC,IAAI,GAAG,EAAE,CAAC;QAOlF,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,OAAgB;QAC7B,wCAAwC;QACxC,MAAM,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAW,CAAC;QAC/D,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAC9C,CAAC;QAED,iBAAiB;QACjB,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;QACjE,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;QAED,wBAAwB;QACxB,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAW,CAAC;QAC5D,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAC7C,CAAC;QAED,kCAAkC;QAClC,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;QAC5C,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;YACxF,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,OAAe;QAC7B,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,OAAe;QAC5B,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,OAAe;QACjC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACjD,OAAO,WAAW,EAAE,MAAM,KAAK,YAAY,IAAI,WAAW,EAAE,MAAM,KAAK,QAAQ,CAAC;IAClF,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,OAAe,EAAE,QAAiB;QACvD,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAEvE,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAC/B,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,CACnD,CAAC;QACJ,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,aAAqB,EAAE,aAAqB;QAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAElD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,IAAI,4BAAmB,CAAC,2BAA2B,CAAC,CAAC;QAC7D,CAAC;QAED,MAAM,aAAa,GAAyB;YAC1C,aAAa;YACb,aAAa;YACb,UAAU,EAAE,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,aAAa,CAAC;YACpE,eAAe,EAAE,IAAI,CAAC,iCAAiC,CAAC,aAAa,EAAE,aAAa,CAAC;YACrF,iBAAiB,EAAE,KAAK;SACzB,CAAC;QAEF,aAAa,CAAC,iBAAiB,GAAG,aAAa,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAE3E,IAAI,aAAa,CAAC,iBAAiB,EAAE,CAAC;YACpC,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;QACtF,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;aAC/C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;aAC/D,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC;aAClC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QAE9D,OAAO,cAAc,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,KAAK,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,IAAS,EAAE,aAAqB,EAAE,aAAsB;QAC9E,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,aAAa,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChD,CAAC;QAED,IAAI,aAAa,KAAK,aAAa,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,QAAQ,GAAG,sBAAsB,aAAa,IAAI,aAAa,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;QAElH,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACrD,IAAI,MAAM,EAAE,CAAC;gBACX,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,yCAAyC;YACzC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;YAE/F,sBAAsB;YACtB,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAEjE,OAAO,WAAW,CAAC;QAErB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBACzD,aAAa;gBACb,aAAa;gBACb,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,IAAI,CAAC,CAAC,iDAAiD;QAChE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,WAAmB,EAAE,SAAiB;QAqB3D,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAE9C,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACzB,MAAM,IAAI,4BAAmB,CAAC,+CAA+C,CAAC,CAAC;QACjF,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,6BAA6B,WAAW,QAAQ,SAAS,EAAE;YACrE,eAAe,EAAE,IAAI,CAAC,0BAA0B,CAAC,WAAW,EAAE,SAAS,CAAC;YACxE,WAAW,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACxE,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,SAAS,CAAC;YACtE,cAAc,EAAE,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,SAAS,CAAC;SACvE,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,OAAe,EAAE,QAAgB,EAAE,MAAe;QACtE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG;gBACd,OAAO;gBACP,QAAQ;gBACR,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,SAAS,EAAE,kCAAkC;aACzD,CAAC;YAEF,oBAAoB;YACpB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;YAE9C,2CAA2C;YAC3C,MAAM,QAAQ,GAAG,iBAAiB,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACtF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAS,QAAQ,CAAC,IAAI,CAAC,CAAC;YACxE,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,WAAW;QAEtF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,YAAoB,IAAI;QAMjD,IAAI,CAAC;YACH,yDAAyD;YACzD,4BAA4B;YAC5B,OAAO;gBACL,aAAa,EAAE,KAAK;gBACpB,mBAAmB,EAAE;oBACnB,KAAK,EAAE,IAAI;oBACX,KAAK,EAAE,IAAI;oBACX,KAAK,EAAE,GAAG;iBACX;gBACD,YAAY,EAAE;oBACZ,EAAE,QAAQ,EAAE,gBAAgB,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE;oBAC3D,EAAE,QAAQ,EAAE,yBAAyB,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE;oBACpE,EAAE,QAAQ,EAAE,gBAAgB,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE;iBAC5D;gBACD,sBAAsB,EAAE;oBACtB,KAAK,EAAE,GAAG;oBACV,KAAK,EAAE,IAAI;iBACZ;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACxF,OAAO;gBACL,aAAa,EAAE,CAAC;gBAChB,mBAAmB,EAAE,EAAE;gBACvB,YAAY,EAAE,EAAE;gBAChB,sBAAsB,EAAE,EAAE;aAC3B,CAAC;QACJ,CAAC;IACH,CAAC;IAED,yBAAyB;IAEjB,kBAAkB;QACxB,MAAM,QAAQ,GAAqB;YACjC;gBACE,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,WAAW,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACnC,eAAe,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACvC,UAAU,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBAClC,QAAQ,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,uBAAuB,CAAC;gBACnE,eAAe,EAAE,EAAE;aACpB;YACD;gBACE,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,YAAY;gBACpB,WAAW,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACnC,eAAe,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACvC,UAAU,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBAClC,QAAQ,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,uBAAuB,EAAE,qBAAqB,EAAE,mBAAmB,CAAC;gBAC/G,eAAe,EAAE,CAAC,qBAAqB,EAAE,wBAAwB,CAAC;aACnE;YACD;gBACE,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,WAAW,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACnC,QAAQ,EAAE;oBACR,iBAAiB;oBACjB,iBAAiB;oBACjB,0BAA0B;oBAC1B,qBAAqB;oBACrB,sBAAsB;oBACtB,qBAAqB;oBACrB,oBAAoB;oBACpB,mBAAmB;oBACnB,mBAAmB;oBACnB,kBAAkB;iBACnB;gBACD,eAAe,EAAE;oBACf,uBAAuB;oBACvB,0BAA0B;oBAC1B,8BAA8B;oBAC9B,oBAAoB;iBACrB;aACF;SACF,CAAC;QAEF,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,8BAA8B;QAC9B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAEvC,kCAAkC;QAClC,IAAI,CAAC,6BAA6B,EAAE,CAAC;QAErC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,EAAE;YAC1C,iBAAiB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;YAC5D,aAAa,EAAE,IAAI,CAAC,sBAAsB,EAAE;SAC7C,CAAC,CAAC;IACL,CAAC;IAEO,6BAA6B;QACnC,uBAAuB;QACvB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE;YAClC;gBACE,OAAO,EAAE,KAAK;gBACd,eAAe,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACvC,UAAU,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBAClC,WAAW,EAAE,MAAM;gBACnB,cAAc,EAAE,0BAA0B;gBAC1C,QAAQ,EAAE,UAAU;aACrB;SACF,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE;YAClC;gBACE,OAAO,EAAE,KAAK;gBACd,eAAe,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACvC,UAAU,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBAClC,WAAW,EAAE,MAAM;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,QAAQ,EAAE,SAAS;aACpB;YACD;gBACE,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,yBAAyB;gBACnC,OAAO,EAAE,qBAAqB;gBAC9B,eAAe,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACvC,WAAW,EAAE,gBAAgB;gBAC7B,QAAQ,EAAE,SAAS;aACpB;SACF,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,OAAe;QACtC,+BAA+B;QAC/B,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAE/C,gCAAgC;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC;IAChE,CAAC;IAEO,eAAe,CAAC,CAAS,EAAE,CAAS;QAC1C,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACxC,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAChE,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC7B,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAE7B,IAAI,KAAK,GAAG,KAAK;gBAAE,OAAO,CAAC,CAAC;YAC5B,IAAI,KAAK,GAAG,KAAK;gBAAE,OAAO,CAAC,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,CAAC,CAAC;IACX,CAAC;IAEO,qBAAqB,CAAC,aAAqB,EAAE,aAAqB;QACxE,2CAA2C;QAC3C,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1D,OAAO,WAAW,KAAK,WAAW,CAAC;IACrC,CAAC;IAEO,iCAAiC,CAAC,WAAmB,EAAE,SAAiB;QAC9E,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAC9C,OAAO,MAAM,EAAE,eAAe,IAAI,EAAE,CAAC;IACvC,CAAC;IAEO,iBAAiB,CAAC,WAAmB,EAAE,SAAiB;QAC9D,0CAA0C;QAC1C,IAAI,WAAW,KAAK,KAAK,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;YACjD,OAAO;gBACL,yCAAyC;gBACzC,gCAAgC;gBAChC,uBAAuB;gBACvB,sBAAsB;gBACtB,wBAAwB;aACzB,CAAC;QACJ,CAAC;QAED,IAAI,WAAW,KAAK,KAAK,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;YACjD,OAAO;gBACL,+BAA+B;gBAC/B,gCAAgC;gBAChC,uBAAuB;aACxB,CAAC;QACJ,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,IAAS,EAAE,WAAmB,EAAE,SAAiB;QACzF,8CAA8C;QAC9C,IAAI,WAAW,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAE9B,0BAA0B;QAC1B,IAAI,WAAW,KAAK,KAAK,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;YACjD,gCAAgC;YAChC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;aAAM,IAAI,WAAW,KAAK,KAAK,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;YACxD,gCAAgC;YAChC,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,eAAe,CAAC,IAAS;QAC/B,2CAA2C;QAC3C,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;gBAC7C,GAAG,KAAK;gBACR,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE;gBAC1B,SAAS,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,SAAS;aACzC,CAAC,CAAC,CAAC;QACN,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,gBAAgB,CAAC,IAAS;QAChC,2CAA2C;QAC3C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,CAAC,UAAU,GAAG;gBAChB,GAAG,IAAI,CAAC,UAAU;gBAClB,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ;aAClC,CAAC;YACF,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QAClC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,0BAA0B,CAAC,WAAmB,EAAE,SAAiB;QAMvE,kDAAkD;QAClD,OAAO;YACL;gBACE,MAAM,EAAE,8BAA8B;gBACtC,MAAM,EAAE,8BAA8B;gBACtC,QAAQ,EAAE,qCAAqC;gBAC/C,OAAO,EAAE,mCAAmC;aAC7C;YACD;gBACE,MAAM,EAAE,8BAA8B;gBACtC,MAAM,EAAE,0BAA0B;gBAClC,QAAQ,EAAE,+BAA+B;gBACzC,OAAO,EAAE,uDAAuD;aACjE;SACF,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,WAAmB,EAAE,SAAiB;QAKlE,OAAO;YACL;gBACE,OAAO,EAAE,qBAAqB;gBAC9B,WAAW,EAAE,6BAA6B;gBAC1C,QAAQ,EAAE,qCAAqC;aAChD;SACF,CAAC;IACJ,CAAC;IAEO,yBAAyB,CAAC,WAAmB,EAAE,SAAiB;QAMtE,OAAO;YACL;gBACE,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,uBAAuB;gBAC9B,WAAW,EAAE,gDAAgD;gBAC7D,IAAI,EAAE;;;;;;;;IAQV;aACG;YACD;gBACE,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,0BAA0B;gBACjC,WAAW,EAAE,wCAAwC;gBACrD,IAAI,EAAE;;;;qCAIuB;aAC9B;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AA1iBY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;yDAQuB,sBAAa,oBAAb,sBAAa,oDACb,8BAAa,oBAAb,8BAAa,oDACd,mDAAuB,oBAAvB,mDAAuB;GAT7C,oBAAoB,CA0iBhC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\versioning\\api-versioning.service.ts"], "sourcesContent": ["import { Injectable, Logger, BadRequestException } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { Request } from 'express';\r\n\r\nimport { LoggerService } from '../../infrastructure/logging/logger.service';\r\nimport { DistributedCacheService } from '../../infrastructure/cache/distributed-cache.service';\r\n\r\n/**\r\n * API version information interface\r\n */\r\nexport interface ApiVersionInfo {\r\n  version: string;\r\n  status: 'active' | 'deprecated' | 'sunset';\r\n  releaseDate: Date;\r\n  deprecationDate?: Date;\r\n  sunsetDate?: Date;\r\n  supportedUntil?: Date;\r\n  features: string[];\r\n  breakingChanges: string[];\r\n  migrationGuide?: string;\r\n}\r\n\r\n/**\r\n * Version compatibility interface\r\n */\r\nexport interface VersionCompatibility {\r\n  sourceVersion: string;\r\n  targetVersion: string;\r\n  compatible: boolean;\r\n  breakingChanges: string[];\r\n  migrationRequired: boolean;\r\n  migrationSteps?: string[];\r\n}\r\n\r\n/**\r\n * API deprecation warning interface\r\n */\r\nexport interface DeprecationWarning {\r\n  version: string;\r\n  feature?: string;\r\n  endpoint?: string;\r\n  deprecationDate: Date;\r\n  sunsetDate?: Date;\r\n  replacement?: string;\r\n  migrationGuide?: string;\r\n  severity: 'info' | 'warning' | 'critical';\r\n}\r\n\r\n/**\r\n * API versioning service providing comprehensive version management\r\n * Handles version detection, compatibility checks, and deprecation warnings\r\n */\r\n@Injectable()\r\nexport class ApiVersioningService {\r\n  private readonly logger = new Logger(ApiVersioningService.name);\r\n  private readonly supportedVersions: Map<string, ApiVersionInfo> = new Map();\r\n  private readonly versionMappings: Map<string, string> = new Map();\r\n  private readonly deprecationWarnings: Map<string, DeprecationWarning[]> = new Map();\r\n\r\n  constructor(\r\n    private readonly configService: ConfigService,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly cacheService: DistributedCacheService,\r\n  ) {\r\n    this.initializeVersions();\r\n  }\r\n\r\n  /**\r\n   * Extract API version from request\r\n   */\r\n  extractVersion(request: Request): string {\r\n    // Check header first (preferred method)\r\n    const headerVersion = request.headers['api-version'] as string;\r\n    if (headerVersion) {\r\n      return this.normalizeVersion(headerVersion);\r\n    }\r\n\r\n    // Check URL path\r\n    const pathMatch = request.path.match(/^\\/api\\/v(\\d+(?:\\.\\d+)?)/);\r\n    if (pathMatch) {\r\n      return this.normalizeVersion(pathMatch[1]);\r\n    }\r\n\r\n    // Check query parameter\r\n    const queryVersion = request.query['api-version'] as string;\r\n    if (queryVersion) {\r\n      return this.normalizeVersion(queryVersion);\r\n    }\r\n\r\n    // Check Accept header for version\r\n    const acceptHeader = request.headers.accept;\r\n    if (acceptHeader) {\r\n      const versionMatch = acceptHeader.match(/application\\/vnd\\.sentinel\\.v(\\d+(?:\\.\\d+)?)/);\r\n      if (versionMatch) {\r\n        return this.normalizeVersion(versionMatch[1]);\r\n      }\r\n    }\r\n\r\n    // Default to latest stable version\r\n    return this.getLatestStableVersion();\r\n  }\r\n\r\n  /**\r\n   * Validate API version\r\n   */\r\n  validateVersion(version: string): boolean {\r\n    const normalizedVersion = this.normalizeVersion(version);\r\n    return this.supportedVersions.has(normalizedVersion);\r\n  }\r\n\r\n  /**\r\n   * Get version information\r\n   */\r\n  getVersionInfo(version: string): ApiVersionInfo | null {\r\n    const normalizedVersion = this.normalizeVersion(version);\r\n    return this.supportedVersions.get(normalizedVersion) || null;\r\n  }\r\n\r\n  /**\r\n   * Check if version is deprecated\r\n   */\r\n  isVersionDeprecated(version: string): boolean {\r\n    const versionInfo = this.getVersionInfo(version);\r\n    return versionInfo?.status === 'deprecated' || versionInfo?.status === 'sunset';\r\n  }\r\n\r\n  /**\r\n   * Get deprecation warnings for version\r\n   */\r\n  getDeprecationWarnings(version: string, endpoint?: string): DeprecationWarning[] {\r\n    const normalizedVersion = this.normalizeVersion(version);\r\n    const warnings = this.deprecationWarnings.get(normalizedVersion) || [];\r\n    \r\n    if (endpoint) {\r\n      return warnings.filter(warning => \r\n        !warning.endpoint || warning.endpoint === endpoint\r\n      );\r\n    }\r\n    \r\n    return warnings;\r\n  }\r\n\r\n  /**\r\n   * Check version compatibility\r\n   */\r\n  checkCompatibility(sourceVersion: string, targetVersion: string): VersionCompatibility {\r\n    const source = this.getVersionInfo(sourceVersion);\r\n    const target = this.getVersionInfo(targetVersion);\r\n\r\n    if (!source || !target) {\r\n      throw new BadRequestException('Invalid version specified');\r\n    }\r\n\r\n    const compatibility: VersionCompatibility = {\r\n      sourceVersion,\r\n      targetVersion,\r\n      compatible: this.areVersionsCompatible(sourceVersion, targetVersion),\r\n      breakingChanges: this.getBreakingChangesBetweenVersions(sourceVersion, targetVersion),\r\n      migrationRequired: false,\r\n    };\r\n\r\n    compatibility.migrationRequired = compatibility.breakingChanges.length > 0;\r\n    \r\n    if (compatibility.migrationRequired) {\r\n      compatibility.migrationSteps = this.getMigrationSteps(sourceVersion, targetVersion);\r\n    }\r\n\r\n    return compatibility;\r\n  }\r\n\r\n  /**\r\n   * Get supported versions\r\n   */\r\n  getSupportedVersions(): ApiVersionInfo[] {\r\n    return Array.from(this.supportedVersions.values())\r\n      .sort((a, b) => this.compareVersions(b.version, a.version));\r\n  }\r\n\r\n  /**\r\n   * Get latest stable version\r\n   */\r\n  getLatestStableVersion(): string {\r\n    const activeVersions = Array.from(this.supportedVersions.values())\r\n      .filter(v => v.status === 'active')\r\n      .sort((a, b) => this.compareVersions(b.version, a.version));\r\n    \r\n    return activeVersions[0]?.version || '2.0';\r\n  }\r\n\r\n  /**\r\n   * Transform response for version compatibility\r\n   */\r\n  async transformResponse(data: any, targetVersion: string, sourceVersion?: string): Promise<any> {\r\n    if (!sourceVersion) {\r\n      sourceVersion = this.getLatestStableVersion();\r\n    }\r\n\r\n    if (sourceVersion === targetVersion) {\r\n      return data;\r\n    }\r\n\r\n    const cacheKey = `response_transform_${sourceVersion}_${targetVersion}_${JSON.stringify(data).substring(0, 100)}`;\r\n    \r\n    try {\r\n      // Check cache first\r\n      const cached = await this.cacheService.get(cacheKey);\r\n      if (cached) {\r\n        return cached;\r\n      }\r\n\r\n      // Apply version-specific transformations\r\n      const transformed = await this.applyVersionTransformations(data, sourceVersion, targetVersion);\r\n\r\n      // Cache for 5 minutes\r\n      await this.cacheService.set(cacheKey, transformed, { ttl: 300 });\r\n\r\n      return transformed;\r\n\r\n    } catch (error) {\r\n      this.loggerService.error('Response transformation failed', {\r\n        sourceVersion,\r\n        targetVersion,\r\n        error: error.message,\r\n      });\r\n      return data; // Return original data on transformation failure\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate version migration guide\r\n   */\r\n  generateMigrationGuide(fromVersion: string, toVersion: string): {\r\n    overview: string;\r\n    breakingChanges: Array<{\r\n      change: string;\r\n      impact: string;\r\n      solution: string;\r\n      example?: string;\r\n    }>;\r\n    newFeatures: string[];\r\n    deprecatedFeatures: Array<{\r\n      feature: string;\r\n      replacement?: string;\r\n      timeline: string;\r\n    }>;\r\n    migrationSteps: Array<{\r\n      step: number;\r\n      title: string;\r\n      description: string;\r\n      code?: string;\r\n    }>;\r\n  } {\r\n    const fromInfo = this.getVersionInfo(fromVersion);\r\n    const toInfo = this.getVersionInfo(toVersion);\r\n\r\n    if (!fromInfo || !toInfo) {\r\n      throw new BadRequestException('Invalid version specified for migration guide');\r\n    }\r\n\r\n    return {\r\n      overview: `Migration guide from API v${fromVersion} to v${toVersion}`,\r\n      breakingChanges: this.getDetailedBreakingChanges(fromVersion, toVersion),\r\n      newFeatures: toInfo.features.filter(f => !fromInfo.features.includes(f)),\r\n      deprecatedFeatures: this.getDeprecatedFeatures(fromVersion, toVersion),\r\n      migrationSteps: this.getDetailedMigrationSteps(fromVersion, toVersion),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Log version usage for analytics\r\n   */\r\n  async logVersionUsage(version: string, endpoint: string, userId?: string): Promise<void> {\r\n    try {\r\n      const logData = {\r\n        version,\r\n        endpoint,\r\n        userId,\r\n        timestamp: new Date(),\r\n        userAgent: 'unknown', // Would be extracted from request\r\n      };\r\n\r\n      // Log for analytics\r\n      this.logger.log('API version usage', logData);\r\n\r\n      // Store in cache for analytics aggregation\r\n      const usageKey = `version_usage_${version}_${new Date().toISOString().split('T')[0]}`;\r\n      const currentUsage = await this.cacheService.get<number>(usageKey) || 0;\r\n      await this.cacheService.set(usageKey, currentUsage + 1, { ttl: 86400 }); // 24 hours\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to log version usage', { error: error.message });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get version usage statistics\r\n   */\r\n  async getVersionUsageStats(timeRange: string = '7d'): Promise<{\r\n    totalRequests: number;\r\n    versionDistribution: Record<string, number>;\r\n    topEndpoints: Array<{ endpoint: string; count: number; version: string }>;\r\n    deprecatedVersionUsage: Record<string, number>;\r\n  }> {\r\n    try {\r\n      // This would typically query a proper analytics database\r\n      // For now, return mock data\r\n      return {\r\n        totalRequests: 10000,\r\n        versionDistribution: {\r\n          '2.0': 7500,\r\n          '1.5': 2000,\r\n          '1.0': 500,\r\n        },\r\n        topEndpoints: [\r\n          { endpoint: '/api/v2/events', count: 2500, version: '2.0' },\r\n          { endpoint: '/api/v2/vulnerabilities', count: 2000, version: '2.0' },\r\n          { endpoint: '/api/v1/assets', count: 1500, version: '1.0' },\r\n        ],\r\n        deprecatedVersionUsage: {\r\n          '1.0': 500,\r\n          '1.5': 2000,\r\n        },\r\n      };\r\n\r\n    } catch (error) {\r\n      this.loggerService.error('Failed to get version usage stats', { error: error.message });\r\n      return {\r\n        totalRequests: 0,\r\n        versionDistribution: {},\r\n        topEndpoints: [],\r\n        deprecatedVersionUsage: {},\r\n      };\r\n    }\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private initializeVersions(): void {\r\n    const versions: ApiVersionInfo[] = [\r\n      {\r\n        version: '1.0',\r\n        status: 'sunset',\r\n        releaseDate: new Date('2023-01-01'),\r\n        deprecationDate: new Date('2024-01-01'),\r\n        sunsetDate: new Date('2024-06-01'),\r\n        features: ['basic-events', 'basic-assets', 'basic-vulnerabilities'],\r\n        breakingChanges: [],\r\n      },\r\n      {\r\n        version: '1.5',\r\n        status: 'deprecated',\r\n        releaseDate: new Date('2023-06-01'),\r\n        deprecationDate: new Date('2024-06-01'),\r\n        sunsetDate: new Date('2025-01-01'),\r\n        features: ['basic-events', 'basic-assets', 'basic-vulnerabilities', 'threat-intelligence', 'basic-correlation'],\r\n        breakingChanges: ['event-schema-change', 'asset-id-format-change'],\r\n      },\r\n      {\r\n        version: '2.0',\r\n        status: 'active',\r\n        releaseDate: new Date('2024-01-01'),\r\n        features: [\r\n          'advanced-events',\r\n          'advanced-assets',\r\n          'advanced-vulnerabilities',\r\n          'threat-intelligence',\r\n          'advanced-correlation',\r\n          'real-time-streaming',\r\n          'advanced-analytics',\r\n          'ai-ml-integration',\r\n          'incident-response',\r\n          'compliance-audit',\r\n        ],\r\n        breakingChanges: [\r\n          'complete-api-redesign',\r\n          'new-authentication-model',\r\n          'restructured-response-format',\r\n          'new-error-handling',\r\n        ],\r\n      },\r\n    ];\r\n\r\n    versions.forEach(version => {\r\n      this.supportedVersions.set(version.version, version);\r\n    });\r\n\r\n    // Initialize version mappings\r\n    this.versionMappings.set('1', '1.0');\r\n    this.versionMappings.set('1.0', '1.0');\r\n    this.versionMappings.set('1.5', '1.5');\r\n    this.versionMappings.set('2', '2.0');\r\n    this.versionMappings.set('2.0', '2.0');\r\n\r\n    // Initialize deprecation warnings\r\n    this.initializeDeprecationWarnings();\r\n\r\n    this.logger.log('API versions initialized', {\r\n      supportedVersions: Array.from(this.supportedVersions.keys()),\r\n      latestVersion: this.getLatestStableVersion(),\r\n    });\r\n  }\r\n\r\n  private initializeDeprecationWarnings(): void {\r\n    // Version 1.0 warnings\r\n    this.deprecationWarnings.set('1.0', [\r\n      {\r\n        version: '1.0',\r\n        deprecationDate: new Date('2024-01-01'),\r\n        sunsetDate: new Date('2024-06-01'),\r\n        replacement: 'v2.0',\r\n        migrationGuide: '/docs/migration/v1-to-v2',\r\n        severity: 'critical',\r\n      },\r\n    ]);\r\n\r\n    // Version 1.5 warnings\r\n    this.deprecationWarnings.set('1.5', [\r\n      {\r\n        version: '1.5',\r\n        deprecationDate: new Date('2024-06-01'),\r\n        sunsetDate: new Date('2025-01-01'),\r\n        replacement: 'v2.0',\r\n        migrationGuide: '/docs/migration/v1.5-to-v2',\r\n        severity: 'warning',\r\n      },\r\n      {\r\n        version: '1.5',\r\n        endpoint: '/api/v1.5/events/legacy',\r\n        feature: 'Legacy event format',\r\n        deprecationDate: new Date('2024-03-01'),\r\n        replacement: '/api/v2/events',\r\n        severity: 'warning',\r\n      },\r\n    ]);\r\n  }\r\n\r\n  private normalizeVersion(version: string): string {\r\n    // Remove 'v' prefix if present\r\n    const cleanVersion = version.replace(/^v/, '');\r\n    \r\n    // Map to full version if needed\r\n    return this.versionMappings.get(cleanVersion) || cleanVersion;\r\n  }\r\n\r\n  private compareVersions(a: string, b: string): number {\r\n    const aParts = a.split('.').map(Number);\r\n    const bParts = b.split('.').map(Number);\r\n    \r\n    for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {\r\n      const aPart = aParts[i] || 0;\r\n      const bPart = bParts[i] || 0;\r\n      \r\n      if (aPart > bPart) return 1;\r\n      if (aPart < bPart) return -1;\r\n    }\r\n    \r\n    return 0;\r\n  }\r\n\r\n  private areVersionsCompatible(sourceVersion: string, targetVersion: string): boolean {\r\n    // Major version changes are not compatible\r\n    const sourceMajor = parseInt(sourceVersion.split('.')[0]);\r\n    const targetMajor = parseInt(targetVersion.split('.')[0]);\r\n    \r\n    return sourceMajor === targetMajor;\r\n  }\r\n\r\n  private getBreakingChangesBetweenVersions(fromVersion: string, toVersion: string): string[] {\r\n    const toInfo = this.getVersionInfo(toVersion);\r\n    return toInfo?.breakingChanges || [];\r\n  }\r\n\r\n  private getMigrationSteps(fromVersion: string, toVersion: string): string[] {\r\n    // Return version-specific migration steps\r\n    if (fromVersion === '1.0' && toVersion === '2.0') {\r\n      return [\r\n        'Update authentication to use JWT tokens',\r\n        'Migrate to new response format',\r\n        'Update error handling',\r\n        'Migrate event schema',\r\n        'Update asset ID format',\r\n      ];\r\n    }\r\n    \r\n    if (fromVersion === '1.5' && toVersion === '2.0') {\r\n      return [\r\n        'Update authentication headers',\r\n        'Migrate to new response format',\r\n        'Update error handling',\r\n      ];\r\n    }\r\n    \r\n    return [];\r\n  }\r\n\r\n  private async applyVersionTransformations(data: any, fromVersion: string, toVersion: string): Promise<any> {\r\n    // Apply version-specific data transformations\r\n    let transformed = { ...data };\r\n\r\n    // Example transformations\r\n    if (fromVersion === '1.0' && toVersion === '2.0') {\r\n      // Transform v1.0 format to v2.0\r\n      transformed = this.transformV1ToV2(transformed);\r\n    } else if (fromVersion === '1.5' && toVersion === '2.0') {\r\n      // Transform v1.5 format to v2.0\r\n      transformed = this.transformV15ToV2(transformed);\r\n    }\r\n\r\n    return transformed;\r\n  }\r\n\r\n  private transformV1ToV2(data: any): any {\r\n    // Example transformation from v1.0 to v2.0\r\n    if (data.events) {\r\n      data.events = data.events.map((event: any) => ({\r\n        ...event,\r\n        metadata: event.meta || {},\r\n        timestamp: event.time || event.timestamp,\r\n      }));\r\n    }\r\n    \r\n    return data;\r\n  }\r\n\r\n  private transformV15ToV2(data: any): any {\r\n    // Example transformation from v1.5 to v2.0\r\n    if (data.pagination) {\r\n      data.pagination = {\r\n        ...data.pagination,\r\n        hasMore: data.pagination.has_more,\r\n      };\r\n      delete data.pagination.has_more;\r\n    }\r\n    \r\n    return data;\r\n  }\r\n\r\n  private getDetailedBreakingChanges(fromVersion: string, toVersion: string): Array<{\r\n    change: string;\r\n    impact: string;\r\n    solution: string;\r\n    example?: string;\r\n  }> {\r\n    // Return detailed breaking changes with solutions\r\n    return [\r\n      {\r\n        change: 'Authentication model changed',\r\n        impact: 'API keys no longer supported',\r\n        solution: 'Migrate to JWT token authentication',\r\n        example: 'Authorization: Bearer <jwt-token>',\r\n      },\r\n      {\r\n        change: 'Response format restructured',\r\n        impact: 'Response wrapper changed',\r\n        solution: 'Update response parsing logic',\r\n        example: '{ \"success\": true, \"data\": {...}, \"metadata\": {...} }',\r\n      },\r\n    ];\r\n  }\r\n\r\n  private getDeprecatedFeatures(fromVersion: string, toVersion: string): Array<{\r\n    feature: string;\r\n    replacement?: string;\r\n    timeline: string;\r\n  }> {\r\n    return [\r\n      {\r\n        feature: 'Legacy event format',\r\n        replacement: 'New structured event format',\r\n        timeline: 'Deprecated in v1.5, removed in v2.0',\r\n      },\r\n    ];\r\n  }\r\n\r\n  private getDetailedMigrationSteps(fromVersion: string, toVersion: string): Array<{\r\n    step: number;\r\n    title: string;\r\n    description: string;\r\n    code?: string;\r\n  }> {\r\n    return [\r\n      {\r\n        step: 1,\r\n        title: 'Update Authentication',\r\n        description: 'Replace API key authentication with JWT tokens',\r\n        code: `// Old (v1.x)\r\nfetch('/api/v1/events', {\r\n  headers: { 'X-API-Key': 'your-api-key' }\r\n});\r\n\r\n// New (v2.0)\r\nfetch('/api/v2/events', {\r\n  headers: { 'Authorization': 'Bearer your-jwt-token' }\r\n});`,\r\n      },\r\n      {\r\n        step: 2,\r\n        title: 'Update Response Handling',\r\n        description: 'Adapt to new response format structure',\r\n        code: `// Old (v1.x)\r\nconst events = response.events;\r\n\r\n// New (v2.0)\r\nconst events = response.data.events;`,\r\n      },\r\n    ];\r\n  }\r\n}\r\n"], "version": 3}