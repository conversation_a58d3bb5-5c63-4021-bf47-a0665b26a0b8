{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\correlated-event.entity.ts", "mappings": ";;;AAAA,6DAA8E;AAG9E,sEAA6D;AAG7D,8EAAqE;AAO5D,kGAPA,2CAAiB,OAOA;AAL1B,2GAAoG;AACpG,yHAAiH;AACjH,iIAAyH;AAiCzH;;GAEG;AACH,IAAY,mBASX;AATD,WAAY,mBAAmB;IAC7B,4CAAqB,CAAA;IACrB,0CAAmB,CAAA;IACnB,0CAAmB,CAAA;IACnB,gDAAyB,CAAA;IACzB,8CAAuB,CAAA;IACvB,kDAA2B,CAAA;IAC3B,4CAAqB,CAAA;IACrB,wCAAiB,CAAA;AACnB,CAAC,EATW,mBAAmB,mCAAnB,mBAAmB,QAS9B;AAwBD;;GAEG;AACH,IAAY,oBASX;AATD,WAAY,oBAAoB;IAC9B,uCAAe,CAAA;IACf,2CAAmB,CAAA;IACnB,uCAAe,CAAA;IACf,2CAAmB,CAAA;IACnB,6CAAqB,CAAA;IACrB,2CAAmB,CAAA;IACnB,6CAAqB,CAAA;IACrB,mDAA2B,CAAA;AAC7B,CAAC,EATW,oBAAoB,oCAApB,oBAAoB,QAS/B;AAuLD;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,MAAa,eAAgB,SAAQ,iCAAuC;IAQ1E,YAAY,KAA2B,EAAE,EAAmB;QAC1D,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACjB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,KAA2B,EAAE,EAAmB;QAC5D,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAEvD,iDAAiD;QACjD,eAAe,CAAC,cAAc,CAAC,IAAI,yEAAiC,CAClE,eAAe,CAAC,EAAE,EAClB;YACE,eAAe,EAAE,KAAK,CAAC,eAAe;YACtC,SAAS,EAAE,KAAK,CAAC,IAAI;YACrB,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,iBAAiB,EAAE,KAAK,CAAC,iBAAiB;YAC1C,uBAAuB,EAAE,KAAK,CAAC,uBAAuB;YACtD,iBAAiB,EAAE,KAAK,CAAC,YAAY,CAAC,MAAM;YAC5C,uBAAuB,EAAE,KAAK,CAAC,kBAAkB,CAAC,MAAM;YACxD,kBAAkB,EAAE,KAAK,CAAC,eAAe,CAAC,MAAM;YAChD,eAAe,EAAE,KAAK,CAAC,eAAe;YACtC,cAAc,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW;YACnC,oBAAoB,EAAE,KAAK,CAAC,oBAAoB,IAAI,KAAK;SAC1D,CACF,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC;IAES,kBAAkB;QAC1B,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,GAAG,eAAe,CAAC,uBAAuB,EAAE,CAAC;YACnF,MAAM,IAAI,KAAK,CAAC,yCAAyC,eAAe,CAAC,uBAAuB,sBAAsB,CAAC,CAAC;QAC1H,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,GAAG,eAAe,CAAC,kBAAkB,EAAE,CAAC;YAC3E,MAAM,IAAI,KAAK,CAAC,yCAAyC,eAAe,CAAC,kBAAkB,iBAAiB,CAAC,CAAC;QAChH,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,uBAAuB,KAAK,SAAS;YAChD,CAAC,IAAI,CAAC,KAAK,CAAC,uBAAuB,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,uBAAuB,GAAG,GAAG,CAAC,EAAE,CAAC;YACzF,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,SAAS;YAClC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;YACvF,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC3B,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,GAAG,eAAe,CAAC,qBAAqB,EAAE,CAAC;YAC/E,MAAM,IAAI,KAAK,CAAC,yBAAyB,eAAe,CAAC,qBAAqB,oBAAoB,CAAC,CAAC;QACtG,CAAC;QAED,0CAA0C;QAC1C,IAAI,CAAC,oCAAoC,EAAE,CAAC;QAE5C,sCAAsC;QACtC,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAExC,mCAAmC;QACnC,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAC3B,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,oCAAoC;QAC1C,mEAAmE;QACnE,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,KAAK,2CAAiB,CAAC,SAAS,EAAE,CAAC;YACjE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;YAC1E,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAED,0DAA0D;QAC1D,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,KAAK,2CAAiB,CAAC,MAAM,EAAE,CAAC;YAC9D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB;gBAChC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;gBACxF,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAED,kEAAkE;QAClE,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,KAAK,2CAAiB,CAAC,WAAW,EAAE,CAAC;YACnE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAC7D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;IACH,CAAC;IAEO,gCAAgC;QACtC,+BAA+B;QAC/B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAClD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;YAC7D,CAAC;YACD,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,IAAI,KAAK,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;gBACnD,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;YAC5E,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC3D,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;YAC7D,CAAC;YACD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAED,2CAA2C;QAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;YAChD,IAAI,QAAQ,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;gBAC7B,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;YACvE,CAAC;YACD,IAAI,QAAQ,CAAC,UAAU,GAAG,CAAC,IAAI,QAAQ,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;gBACzD,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC;QAED,0CAA0C;QAC1C,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAClC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;YAC9C,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC,IAAI,OAAO,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;gBACvD,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,6CAA6C;QAC7C,IAAI,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;YACrC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;YACpD,IAAI,UAAU,CAAC,YAAY,GAAG,CAAC,IAAI,UAAU,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC;gBACjE,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;YACpF,CAAC;YACD,IAAI,UAAU,CAAC,UAAU,GAAG,CAAC,IAAI,UAAU,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;gBAC7D,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;YACjF,CAAC;QACH,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAY,CAAC;QAE5C,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,WAAW,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1E,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,6CAA6C;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACnD,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC5C,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAE3C,IAAI,YAAY,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;gBAC1C,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;YACxE,CAAC;YAED,IAAI,YAAY,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;gBACjD,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAED,gCAAgC;QAChC,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QACtC,IAAI,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,MAAM,kBAAkB,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACrF,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,GAAG,kBAAkB,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,2BAA2B;YACxF,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED,UAAU;IACV,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IACpC,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;IACtC,CAAC;IAED,IAAI,YAAY;QACd,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;IACxC,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;IAC1C,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACrD,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IACpC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACnE,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,aAAa;QACf,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IACvC,CAAC;IAED,IAAI,YAAY;QACd,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IACtC,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IACxF,CAAC;IAED,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC;IACzC,CAAC;IAED,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC;IAC3C,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC;IACzC,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAC5E,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IACzC,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAC5F,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAC1F,CAAC;IAED,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAChG,CAAC;IAED,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,IAAI,KAAK,CAAC;IAClD,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC;IAC5C,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7E,CAAC;IAED,mBAAmB;IAEnB;;OAEG;IACH,gBAAgB;QACd,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,KAAK,2CAAiB,CAAC,OAAO,EAAE,CAAC;YAC/D,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,2CAAiB,CAAC,WAAW,CAAC;QAC7D,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7C,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAE3E,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,MAAyB;QAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,KAAK,2CAAiB,CAAC,WAAW,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,2CAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,2CAAiB,CAAC,OAAO,CAAC;QACxG,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,IAAI,IAAI,EAAE,CAAC;QAC/C,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC;QACtC,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,SAAS,CAAC;QAE5C,sDAAsD;QACtD,IAAI,CAAC,gCAAgC,CAAC,MAAM,CAAC,CAAC;QAE9C,yCAAyC;QACzC,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAExC,IAAI,CAAC,cAAc,CAAC,IAAI,sFAAuC,CAC7D,IAAI,CAAC,EAAE,EACP;YACE,SAAS,EAAE,2CAAiB,CAAC,WAAW;YACxC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB;YACvC,MAAM;YACN,uBAAuB,EAAE,IAAI,CAAC,KAAK,CAAC,uBAAuB;YAC3D,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,IAAI,KAAK;SAC/D,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,KAAa,EAAE,MAAmC;QAChE,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,KAAK,2CAAiB,CAAC,WAAW,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,2CAAiB,CAAC,MAAM,CAAC;QACxD,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAExC,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG;gBAC7B,OAAO,EAAE,KAAK;gBACd,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE;gBACvC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,EAAE;gBACrC,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE;gBAC/B,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC;gBAChC,oBAAoB,EAAE,MAAM,CAAC,oBAAoB,IAAI,CAAC;gBACtD,eAAe,EAAE,MAAM,CAAC,eAAe,IAAI,CAAC;gBAC5C,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,CAAC;gBAChC,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,CAAC;gBACtC,kBAAkB,EAAE,MAAM,CAAC,kBAAkB,IAAI,EAAE;aACpD,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,8FAA2C,CACjE,IAAI,CAAC,EAAE,EACP;YACE,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;YAC3C,KAAK;YACL,OAAO,EAAE,IAAI,CAAC,mBAAmB;YACjC,mBAAmB,EAAE,IAAI,CAAC,iCAAiC,EAAE;SAC9D,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAc;QAC5B,IAAI,CAAC,CAAC,2CAAiB,CAAC,OAAO,EAAE,2CAAiB,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAClG,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,2CAAiB,CAAC,OAAO,CAAC;QACzD,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,SAAS,CAAC;QAC5C,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;QAEhC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,IAAI,IAAI,CAAC,iCAAiC,EAAE,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,2CAAiB,CAAC,OAAO,CAAC;QACzD,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,SAAS,CAAC;QAC5C,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,SAAS,CAAC;QAC9C,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,SAAS,CAAC;QAC5C,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,SAAS,CAAC;QAEzC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,KAAuB;QACzC,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,IAAI,eAAe,CAAC,uBAAuB,EAAE,CAAC;YACpF,MAAM,IAAI,KAAK,CAAC,wBAAwB,eAAe,CAAC,uBAAuB,sBAAsB,CAAC,CAAC;QACzG,CAAC;QAED,4BAA4B;QAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CACtD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAClE,CAAC;QAEF,IAAI,aAAa,EAAE,CAAC;YAClB,+CAA+C;YAC/C,IAAI,KAAK,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,EAAE,CAAC;gBAChD,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,gBAAgB;YAChB,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,OAAuB;QACrC,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,IAAI,eAAe,CAAC,kBAAkB,EAAE,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,wBAAwB,eAAe,CAAC,kBAAkB,iBAAiB,CAAC,CAAC;QAC/F,CAAC;QAED,sBAAsB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,OAAuB;QACnC,sBAAsB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QACvE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAwB;QACrC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;QACrC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,OAAe;QACnC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACtD,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,QAAkE;QAC1F,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,QAAQ,CAAC;QAC1C,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,OAAgE;QACvF,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,OAAO,CAAC;QACxC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,2BAA2B,CAAC,UAAsE;QAChG,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,UAAU,CAAC;QAC9C,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,cAAmC;QACtD,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,GAAG,cAAc,EAAE,CAAC;IAClF,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAqB;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;QACzE,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,6BAA6B,CAAC,KAAa;QACzC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,uBAAuB,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,gCAAgC,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,MAAgB;QAClC,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,EAAE,CAAC;QACxD,MAAM,SAAS,GAAG,CAAC,GAAG,aAAa,EAAE,GAAG,MAAM,CAAC,CAAC;QAEhD,IAAI,SAAS,CAAC,MAAM,GAAG,eAAe,CAAC,qBAAqB,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,yBAAyB,eAAe,CAAC,qBAAqB,oBAAoB,CAAC,CAAC;QACtG,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,SAAS,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,MAAc;QAChC,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,UAAkB,EAAE,KAAc;QACrD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QACnC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;QACjC,CAAC;IACH,CAAC;IAED,gBAAgB;IAEhB;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,KAAK,2CAAiB,CAAC,SAAS,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,KAAK,2CAAiB,CAAC,MAAM,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,KAAK,2CAAiB,CAAC,WAAW,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,KAAK,2CAAiB,CAAC,OAAO,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,KAAK,2CAAiB,CAAC,OAAO,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,uBAAuB,IAAI,CAAC,CAAC,IAAI,eAAe,CAAC,6BAA6B,CAAC;IACpG,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,iCAAiC;QAC/B,OAAO,IAAI,CAAC,mBAAmB,IAAI,eAAe,CAAC,wBAAwB,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9D,IAAI,CAAC,yBAAyB,EAAE;YAChC,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,CAAC,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,eAAe,IAAI,CAAC,CAAC,IAAI,eAAe,CAAC,yBAAyB,CAAC;IAC3G,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,sBAAsB,IAAI,IAAI,IAAI,EAAE,CAAC;QAChE,OAAO,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAc;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,MAAc;QAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,SAA+B;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAE5D,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACxG,OAAO,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAE5D,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAC7D,KAAK,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CACxD,CAAC;IACJ,CAAC;IAED,yBAAyB;IAEjB,gCAAgC,CAAC,MAAyB;QAChE,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,gCAAgC;QAChC,MAAM,kBAAkB,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE,CAAC;QAC1D,KAAK,IAAI,kBAAkB,CAAC;QAE5B,4BAA4B;QAC5B,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACnD,KAAK,IAAI,eAAe,CAAC;QAEzB,0BAA0B;QAC1B,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;QAChD,KAAK,IAAI,aAAa,CAAC;QAEvB,kCAAkC;QAClC,IAAI,MAAM,CAAC,eAAe,GAAG,EAAE,EAAE,CAAC;YAChC,KAAK,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;QACzC,CAAC;QAED,sCAAsC;QACtC,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QACzE,KAAK,IAAI,aAAa,CAAC;QAEvB,gCAAgC;QAChC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3D,KAAK,IAAI,YAAY,CAAC;QAEtB,IAAI,CAAC,KAAK,CAAC,uBAAuB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IACzE,CAAC;IAEO,gCAAgC;QACtC,IAAI,cAAc,GAAG,KAAK,CAAC;QAE3B,iEAAiE;QACjE,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAE,CAAC;YAChE,cAAc,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACtC,cAAc,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,iCAAiC;QACjC,IAAI,IAAI,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,EAAE,CAAC;YAC7C,cAAc,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,4CAA4C;QAC5C,IAAI,IAAI,CAAC,mBAAmB,EAAE,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnE,cAAc,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,cAAc,CAAC;IACnD,CAAC;;AAz6BH,0CA06BC;AAz6ByB,wCAAwB,GAAG,CAAC,CAAC;AAC7B,6CAA6B,GAAG,EAAE,CAAC;AACnC,yCAAyB,GAAG,EAAE,CAAC;AAC/B,qCAAqB,GAAG,EAAE,CAAC;AAC3B,uCAAuB,GAAG,GAAG,CAAC;AAC9B,kCAAkB,GAAG,GAAG,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\correlated-event.entity.ts"], "sourcesContent": ["import { BaseAggregateRoot, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventMetadata } from '../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../enums/event-processing-status.enum';\r\nimport { CorrelationStatus } from '../enums/correlation-status.enum';\r\nimport { ConfidenceLevel } from '../enums/confidence-level.enum';\r\nimport { CorrelatedEventCreatedDomainEvent } from '../events/correlated-event-created.domain-event';\r\nimport { CorrelatedEventStatusChangedDomainEvent } from '../events/correlated-event-status-changed.domain-event';\r\nimport { CorrelatedEventCorrelationFailedDomainEvent } from '../events/correlated-event-correlation-failed.domain-event';\r\n\r\n// Export CorrelationStatus for use in other files\r\nexport { CorrelationStatus };\r\n\r\n/**\r\n * Correlation Rule Interface\r\n */\r\nexport interface CorrelationRule {\r\n  /** Rule identifier */\r\n  id: string;\r\n  /** Rule name */\r\n  name: string;\r\n  /** Rule description */\r\n  description: string;\r\n  /** Rule type (temporal, spatial, pattern, etc.) */\r\n  type: CorrelationRuleType;\r\n  /** Rule priority (higher number = higher priority) */\r\n  priority: number;\r\n  /** Whether the rule is required for successful correlation */\r\n  required: boolean;\r\n  /** Time window for correlation in milliseconds */\r\n  timeWindowMs: number;\r\n  /** Maximum events to correlate */\r\n  maxEvents?: number;\r\n  /** Minimum confidence threshold */\r\n  minConfidence: number;\r\n  /** Rule configuration */\r\n  config?: Record<string, any>;\r\n  /** Rule timeout in milliseconds */\r\n  timeoutMs?: number;\r\n}\r\n\r\n/**\r\n * Correlation Rule Type Enum\r\n */\r\nexport enum CorrelationRuleType {\r\n  TEMPORAL = 'TEMPORAL',           // Time-based correlation\r\n  SPATIAL = 'SPATIAL',             // Location/network-based correlation\r\n  PATTERN = 'PATTERN',             // Pattern matching correlation\r\n  BEHAVIORAL = 'BEHAVIORAL',       // Behavioral analysis correlation\r\n  SIGNATURE = 'SIGNATURE',         // Signature-based correlation\r\n  STATISTICAL = 'STATISTICAL',     // Statistical correlation\r\n  SEMANTIC = 'SEMANTIC',           // Semantic/content correlation\r\n  CAUSAL = 'CAUSAL',              // Causal relationship correlation\r\n}\r\n\r\n/**\r\n * Correlation Match Interface\r\n */\r\nexport interface CorrelationMatch {\r\n  /** Matched event ID */\r\n  eventId: UniqueEntityId;\r\n  /** Match confidence score (0-100) */\r\n  confidence: number;\r\n  /** Match type */\r\n  matchType: CorrelationMatchType;\r\n  /** Correlation rule that created this match */\r\n  ruleId: string;\r\n  /** Match details */\r\n  details: Record<string, any>;\r\n  /** When the match was found */\r\n  timestamp: Date;\r\n  /** Match weight in correlation */\r\n  weight: number;\r\n  /** Additional metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Correlation Match Type Enum\r\n */\r\nexport enum CorrelationMatchType {\r\n  EXACT = 'EXACT',                 // Exact match\r\n  PARTIAL = 'PARTIAL',             // Partial match\r\n  FUZZY = 'FUZZY',                // Fuzzy match\r\n  PATTERN = 'PATTERN',             // Pattern-based match\r\n  TEMPORAL = 'TEMPORAL',           // Time-based match\r\n  SPATIAL = 'SPATIAL',             // Location-based match\r\n  SEMANTIC = 'SEMANTIC',           // Semantic match\r\n  STATISTICAL = 'STATISTICAL',     // Statistical correlation\r\n}\r\n\r\n/**\r\n * Correlation Result Interface\r\n */\r\nexport interface CorrelationResult {\r\n  /** Whether correlation was successful */\r\n  success: boolean;\r\n  /** Applied correlation rules */\r\n  appliedRules: string[];\r\n  /** Failed correlation rules */\r\n  failedRules: string[];\r\n  /** Correlation warnings */\r\n  warnings: string[];\r\n  /** Correlation errors */\r\n  errors: string[];\r\n  /** Processing duration in milliseconds */\r\n  processingDurationMs: number;\r\n  /** Overall correlation confidence score (0-100) */\r\n  confidenceScore: number;\r\n  /** Number of correlation rules used */\r\n  rulesUsed: number;\r\n  /** Total matches found */\r\n  matchesFound: number;\r\n  /** Correlation patterns identified */\r\n  patternsIdentified: string[];\r\n  /** Attack chain indicators */\r\n  attackChainIndicators?: string[];\r\n}\r\n\r\n/**\r\n * Attack Chain Interface\r\n */\r\nexport interface AttackChain {\r\n  /** Chain identifier */\r\n  id: string;\r\n  /** Chain name/title */\r\n  name: string;\r\n  /** Chain description */\r\n  description: string;\r\n  /** Attack stages in chronological order */\r\n  stages: AttackStage[];\r\n  /** Overall chain confidence */\r\n  confidence: ConfidenceLevel;\r\n  /** Chain severity */\r\n  severity: EventSeverity;\r\n  /** Chain tactics (MITRE ATT&CK) */\r\n  tactics?: string[];\r\n  /** Chain techniques (MITRE ATT&CK) */\r\n  techniques?: string[];\r\n  /** Chain timeline */\r\n  timeline: {\r\n    startTime: Date;\r\n    endTime: Date;\r\n    duration: number;\r\n  };\r\n  /** Chain metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Attack Stage Interface\r\n */\r\nexport interface AttackStage {\r\n  /** Stage identifier */\r\n  id: string;\r\n  /** Stage name */\r\n  name: string;\r\n  /** Stage description */\r\n  description: string;\r\n  /** Events in this stage */\r\n  eventIds: UniqueEntityId[];\r\n  /** Stage order in attack chain */\r\n  order: number;\r\n  /** Stage confidence */\r\n  confidence: ConfidenceLevel;\r\n  /** Stage timestamp */\r\n  timestamp: Date;\r\n  /** MITRE ATT&CK tactic */\r\n  tactic?: string;\r\n  /** MITRE ATT&CK techniques */\r\n  techniques?: string[];\r\n  /** Stage metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * CorrelatedEvent Entity Properties\r\n */\r\nexport interface CorrelatedEventProps {\r\n  /** Original enriched event ID that was correlated */\r\n  enrichedEventId: UniqueEntityId;\r\n  /** Event metadata containing timestamp, source, and processing information */\r\n  metadata: EventMetadata;\r\n  /** Type of the security event */\r\n  type: EventType;\r\n  /** Severity level of the event */\r\n  severity: EventSeverity;\r\n  /** Current status of the event */\r\n  status: EventStatus;\r\n  /** Current processing status in the pipeline */\r\n  processingStatus: EventProcessingStatus;\r\n  /** Current correlation status */\r\n  correlationStatus: CorrelationStatus;\r\n  /** Original enriched event data */\r\n  enrichedData: Record<string, any>;\r\n  /** Correlated event data with correlation context */\r\n  correlatedData: Record<string, any>;\r\n  /** Event title/summary */\r\n  title: string;\r\n  /** Detailed description of the event */\r\n  description?: string;\r\n  /** Tags for categorization and filtering */\r\n  tags?: string[];\r\n  /** Risk score (0-100) */\r\n  riskScore?: number;\r\n  /** Confidence level */\r\n  confidenceLevel: ConfidenceLevel;\r\n  /** Additional custom attributes */\r\n  attributes?: Record<string, any>;\r\n  /** Event correlation ID for grouping related events */\r\n  correlationId: string;\r\n  /** Parent event ID if this is a child event */\r\n  parentEventId?: UniqueEntityId;\r\n  /** Child event IDs if this is a parent event */\r\n  childEventIds: UniqueEntityId[];\r\n  /** Applied correlation rules */\r\n  appliedRules: CorrelationRule[];\r\n  /** Correlation matches found */\r\n  correlationMatches: CorrelationMatch[];\r\n  /** Correlation result details */\r\n  correlationResult?: CorrelationResult;\r\n  /** When correlation started */\r\n  correlationStartedAt?: Date;\r\n  /** When correlation completed */\r\n  correlationCompletedAt?: Date;\r\n  /** Correlation processing attempts */\r\n  correlationAttempts?: number;\r\n  /** Last correlation error */\r\n  lastCorrelationError?: string;\r\n  /** Attack chain information */\r\n  attackChain?: AttackChain;\r\n  /** Related event IDs (correlated events) */\r\n  relatedEventIds: UniqueEntityId[];\r\n  /** Correlation patterns identified */\r\n  correlationPatterns: string[];\r\n  /** Temporal correlation data */\r\n  temporalCorrelation?: {\r\n    timeWindow: number;\r\n    eventSequence: UniqueEntityId[];\r\n    patternType: string;\r\n    confidence: number;\r\n  };\r\n  /** Spatial correlation data */\r\n  spatialCorrelation?: {\r\n    sourceIps: string[];\r\n    targetIps: string[];\r\n    networkSegments: string[];\r\n    geographicRegions: string[];\r\n    confidence: number;\r\n  };\r\n  /** Behavioral correlation data */\r\n  behavioralCorrelation?: {\r\n    userPatterns: string[];\r\n    systemPatterns: string[];\r\n    anomalyScore: number;\r\n    baselineDeviation: number;\r\n    confidence: number;\r\n  };\r\n  /** Whether the correlated event requires manual review */\r\n  requiresManualReview?: boolean;\r\n  /** Manual review notes */\r\n  reviewNotes?: string;\r\n  /** Who reviewed the event */\r\n  reviewedBy?: string;\r\n  /** When the event was reviewed */\r\n  reviewedAt?: Date;\r\n  /** Correlation quality score (0-100) */\r\n  correlationQualityScore?: number;\r\n  /** Validation errors found during correlation */\r\n  validationErrors?: string[];\r\n}\r\n\r\n/**\r\n * CorrelatedEvent Entity\r\n * \r\n * Represents a security event that has been processed through the correlation pipeline.\r\n * Correlated events have relationships with other events and provide context about\r\n * attack patterns, sequences, and coordinated activities.\r\n * \r\n * Key responsibilities:\r\n * - Maintain correlated event state and lifecycle\r\n * - Enforce correlation business rules and data quality standards\r\n * - Track correlation process and applied rules\r\n * - Generate domain events for correlation state changes\r\n * - Support attack chain analysis and pattern recognition\r\n * - Manage manual review workflow for complex correlations\r\n * - Maintain relationships with related events\r\n * \r\n * Business Rules:\r\n * - Correlated events must reference a valid enriched event\r\n * - Correlation matches must include confidence scores and rule attribution\r\n * - Attack chains must have chronological ordering and valid stages\r\n * - High-confidence correlations may trigger automated responses\r\n * - Correlation attempts are tracked and limited\r\n * - Failed correlation must preserve data integrity and provide fallback\r\n */\r\nexport class CorrelatedEvent extends BaseAggregateRoot<CorrelatedEventProps> {\r\n  private static readonly MAX_CORRELATION_ATTEMPTS = 3;\r\n  private static readonly MIN_CORRELATION_QUALITY_SCORE = 70;\r\n  private static readonly HIGH_CONFIDENCE_THRESHOLD = 85;\r\n  private static readonly MAX_VALIDATION_ERRORS = 10;\r\n  private static readonly MAX_CORRELATION_MATCHES = 100;\r\n  private static readonly MAX_RELATED_EVENTS = 500;\r\n\r\n  constructor(props: CorrelatedEventProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Create a new CorrelatedEvent\r\n   */\r\n  static create(props: CorrelatedEventProps, id?: UniqueEntityId): CorrelatedEvent {\r\n    const correlatedEvent = new CorrelatedEvent(props, id);\r\n    \r\n    // Add domain event for correlated event creation\r\n    correlatedEvent.addDomainEvent(new CorrelatedEventCreatedDomainEvent(\r\n      correlatedEvent.id,\r\n      {\r\n        enrichedEventId: props.enrichedEventId,\r\n        eventType: props.type,\r\n        severity: props.severity,\r\n        correlationStatus: props.correlationStatus,\r\n        correlationQualityScore: props.correlationQualityScore,\r\n        appliedRulesCount: props.appliedRules.length,\r\n        correlationMatchesCount: props.correlationMatches.length,\r\n        relatedEventsCount: props.relatedEventIds.length,\r\n        confidenceLevel: props.confidenceLevel,\r\n        hasAttackChain: !!props.attackChain,\r\n        requiresManualReview: props.requiresManualReview || false,\r\n      }\r\n    ));\r\n\r\n    return correlatedEvent;\r\n  }\r\n\r\n  protected validateInvariants(): void {\r\n    super.validateInvariants();\r\n\r\n    if (!this.props.enrichedEventId) {\r\n      throw new Error('CorrelatedEvent must reference an enriched event');\r\n    }\r\n\r\n    if (!this.props.metadata) {\r\n      throw new Error('CorrelatedEvent must have metadata');\r\n    }\r\n\r\n    if (!this.props.type) {\r\n      throw new Error('CorrelatedEvent must have a type');\r\n    }\r\n\r\n    if (!this.props.severity) {\r\n      throw new Error('CorrelatedEvent must have a severity');\r\n    }\r\n\r\n    if (!this.props.status) {\r\n      throw new Error('CorrelatedEvent must have a status');\r\n    }\r\n\r\n    if (!this.props.processingStatus) {\r\n      throw new Error('CorrelatedEvent must have a processing status');\r\n    }\r\n\r\n    if (!this.props.correlationStatus) {\r\n      throw new Error('CorrelatedEvent must have a correlation status');\r\n    }\r\n\r\n    if (!this.props.enrichedData) {\r\n      throw new Error('CorrelatedEvent must have enriched data');\r\n    }\r\n\r\n    if (!this.props.correlatedData) {\r\n      throw new Error('CorrelatedEvent must have correlated data');\r\n    }\r\n\r\n    if (!this.props.title || this.props.title.trim().length === 0) {\r\n      throw new Error('CorrelatedEvent must have a non-empty title');\r\n    }\r\n\r\n    if (!this.props.confidenceLevel) {\r\n      throw new Error('CorrelatedEvent must have a confidence level');\r\n    }\r\n\r\n    if (!this.props.correlationId || this.props.correlationId.trim().length === 0) {\r\n      throw new Error('CorrelatedEvent must have a correlation ID');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.childEventIds)) {\r\n      throw new Error('CorrelatedEvent must have child event IDs array');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.appliedRules)) {\r\n      throw new Error('CorrelatedEvent must have applied rules array');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.correlationMatches)) {\r\n      throw new Error('CorrelatedEvent must have correlation matches array');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.relatedEventIds)) {\r\n      throw new Error('CorrelatedEvent must have related event IDs array');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.correlationPatterns)) {\r\n      throw new Error('CorrelatedEvent must have correlation patterns array');\r\n    }\r\n\r\n    if (this.props.correlationMatches.length > CorrelatedEvent.MAX_CORRELATION_MATCHES) {\r\n      throw new Error(`CorrelatedEvent cannot have more than ${CorrelatedEvent.MAX_CORRELATION_MATCHES} correlation matches`);\r\n    }\r\n\r\n    if (this.props.relatedEventIds.length > CorrelatedEvent.MAX_RELATED_EVENTS) {\r\n      throw new Error(`CorrelatedEvent cannot have more than ${CorrelatedEvent.MAX_RELATED_EVENTS} related events`);\r\n    }\r\n\r\n    if (this.props.correlationQualityScore !== undefined && \r\n        (this.props.correlationQualityScore < 0 || this.props.correlationQualityScore > 100)) {\r\n      throw new Error('Correlation quality score must be between 0 and 100');\r\n    }\r\n\r\n    if (this.props.riskScore !== undefined && \r\n        (this.props.riskScore < 0 || this.props.riskScore > 100)) {\r\n      throw new Error('Risk score must be between 0 and 100');\r\n    }\r\n\r\n    if (this.props.correlationAttempts !== undefined && this.props.correlationAttempts < 0) {\r\n      throw new Error('Correlation attempts cannot be negative');\r\n    }\r\n\r\n    if (this.props.validationErrors && \r\n        this.props.validationErrors.length > CorrelatedEvent.MAX_VALIDATION_ERRORS) {\r\n      throw new Error(`Cannot have more than ${CorrelatedEvent.MAX_VALIDATION_ERRORS} validation errors`);\r\n    }\r\n\r\n    // Validate correlation status consistency\r\n    this.validateCorrelationStatusConsistency();\r\n\r\n    // Validate correlation data integrity\r\n    this.validateCorrelationDataIntegrity();\r\n\r\n    // Validate attack chain if present\r\n    if (this.props.attackChain) {\r\n      this.validateAttackChain();\r\n    }\r\n  }\r\n\r\n  private validateCorrelationStatusConsistency(): void {\r\n    // If correlation is completed, it should have completion timestamp\r\n    if (this.props.correlationStatus === CorrelationStatus.COMPLETED) {\r\n      if (!this.props.correlationCompletedAt) {\r\n        throw new Error('Completed correlation must have completion timestamp');\r\n      }\r\n      if (!this.props.correlationResult) {\r\n        throw new Error('Completed correlation must have result');\r\n      }\r\n    }\r\n\r\n    // If correlation failed, it should have error information\r\n    if (this.props.correlationStatus === CorrelationStatus.FAILED) {\r\n      if (!this.props.lastCorrelationError && \r\n          (!this.props.correlationResult || this.props.correlationResult.errors.length === 0)) {\r\n        throw new Error('Failed correlation must have error information');\r\n      }\r\n    }\r\n\r\n    // If correlation is in progress, it should have started timestamp\r\n    if (this.props.correlationStatus === CorrelationStatus.IN_PROGRESS) {\r\n      if (!this.props.correlationStartedAt) {\r\n        throw new Error('In-progress correlation must have start timestamp');\r\n      }\r\n    }\r\n\r\n    // Manual review consistency\r\n    if (this.props.requiresManualReview && this.props.reviewedAt) {\r\n      if (!this.props.reviewedBy) {\r\n        throw new Error('Reviewed events must have reviewer information');\r\n      }\r\n    }\r\n  }\r\n\r\n  private validateCorrelationDataIntegrity(): void {\r\n    // Validate correlation matches\r\n    for (const match of this.props.correlationMatches) {\r\n      if (!match.eventId) {\r\n        throw new Error('Correlation match must have an event ID');\r\n      }\r\n      if (match.confidence < 0 || match.confidence > 100) {\r\n        throw new Error('Correlation match confidence must be between 0 and 100');\r\n      }\r\n      if (!match.ruleId) {\r\n        throw new Error('Correlation match must have a rule ID');\r\n      }\r\n      if (!match.timestamp) {\r\n        throw new Error('Correlation match must have a timestamp');\r\n      }\r\n      if (match.weight < 0 || match.weight > 1) {\r\n        throw new Error('Correlation match weight must be between 0 and 1');\r\n      }\r\n    }\r\n\r\n    // Validate temporal correlation if present\r\n    if (this.props.temporalCorrelation) {\r\n      const temporal = this.props.temporalCorrelation;\r\n      if (temporal.timeWindow <= 0) {\r\n        throw new Error('Temporal correlation time window must be positive');\r\n      }\r\n      if (temporal.confidence < 0 || temporal.confidence > 100) {\r\n        throw new Error('Temporal correlation confidence must be between 0 and 100');\r\n      }\r\n    }\r\n\r\n    // Validate spatial correlation if present\r\n    if (this.props.spatialCorrelation) {\r\n      const spatial = this.props.spatialCorrelation;\r\n      if (spatial.confidence < 0 || spatial.confidence > 100) {\r\n        throw new Error('Spatial correlation confidence must be between 0 and 100');\r\n      }\r\n    }\r\n\r\n    // Validate behavioral correlation if present\r\n    if (this.props.behavioralCorrelation) {\r\n      const behavioral = this.props.behavioralCorrelation;\r\n      if (behavioral.anomalyScore < 0 || behavioral.anomalyScore > 100) {\r\n        throw new Error('Behavioral correlation anomaly score must be between 0 and 100');\r\n      }\r\n      if (behavioral.confidence < 0 || behavioral.confidence > 100) {\r\n        throw new Error('Behavioral correlation confidence must be between 0 and 100');\r\n      }\r\n    }\r\n  }\r\n\r\n  private validateAttackChain(): void {\r\n    const attackChain = this.props.attackChain!;\r\n    \r\n    if (!attackChain.id || attackChain.id.trim().length === 0) {\r\n      throw new Error('Attack chain must have an ID');\r\n    }\r\n\r\n    if (!attackChain.name || attackChain.name.trim().length === 0) {\r\n      throw new Error('Attack chain must have a name');\r\n    }\r\n\r\n    if (!Array.isArray(attackChain.stages) || attackChain.stages.length === 0) {\r\n      throw new Error('Attack chain must have at least one stage');\r\n    }\r\n\r\n    if (!attackChain.timeline) {\r\n      throw new Error('Attack chain must have timeline information');\r\n    }\r\n\r\n    // Validate stages are in chronological order\r\n    for (let i = 1; i < attackChain.stages.length; i++) {\r\n      const prevStage = attackChain.stages[i - 1];\r\n      const currentStage = attackChain.stages[i];\r\n      \r\n      if (currentStage.order <= prevStage.order) {\r\n        throw new Error('Attack chain stages must be in chronological order');\r\n      }\r\n      \r\n      if (currentStage.timestamp < prevStage.timestamp) {\r\n        throw new Error('Attack chain stage timestamps must be chronological');\r\n      }\r\n    }\r\n\r\n    // Validate timeline consistency\r\n    const timeline = attackChain.timeline;\r\n    if (timeline.endTime < timeline.startTime) {\r\n      throw new Error('Attack chain end time must be after start time');\r\n    }\r\n\r\n    const calculatedDuration = timeline.endTime.getTime() - timeline.startTime.getTime();\r\n    if (Math.abs(timeline.duration - calculatedDuration) > 1000) { // Allow 1 second tolerance\r\n      throw new Error('Attack chain duration must match start and end times');\r\n    }\r\n  }\r\n\r\n  // Getters\r\n  get enrichedEventId(): UniqueEntityId {\r\n    return this.props.enrichedEventId;\r\n  }\r\n\r\n  get metadata(): EventMetadata {\r\n    return this.props.metadata;\r\n  }\r\n\r\n  get type(): EventType {\r\n    return this.props.type;\r\n  }\r\n\r\n  get severity(): EventSeverity {\r\n    return this.props.severity;\r\n  }\r\n\r\n  get status(): EventStatus {\r\n    return this.props.status;\r\n  }\r\n\r\n  get processingStatus(): EventProcessingStatus {\r\n    return this.props.processingStatus;\r\n  }\r\n\r\n  get correlationStatus(): CorrelationStatus {\r\n    return this.props.correlationStatus;\r\n  }\r\n\r\n  get enrichedData(): Record<string, any> {\r\n    return { ...this.props.enrichedData };\r\n  }\r\n\r\n  get correlatedData(): Record<string, any> {\r\n    return { ...this.props.correlatedData };\r\n  }\r\n\r\n  get title(): string {\r\n    return this.props.title;\r\n  }\r\n\r\n  get description(): string | undefined {\r\n    return this.props.description;\r\n  }\r\n\r\n  get tags(): string[] {\r\n    return this.props.tags ? [...this.props.tags] : [];\r\n  }\r\n\r\n  get riskScore(): number | undefined {\r\n    return this.props.riskScore;\r\n  }\r\n\r\n  get confidenceLevel(): ConfidenceLevel {\r\n    return this.props.confidenceLevel;\r\n  }\r\n\r\n  get attributes(): Record<string, any> {\r\n    return this.props.attributes ? { ...this.props.attributes } : {};\r\n  }\r\n\r\n  get correlationId(): string {\r\n    return this.props.correlationId;\r\n  }\r\n\r\n  get parentEventId(): UniqueEntityId | undefined {\r\n    return this.props.parentEventId;\r\n  }\r\n\r\n  get childEventIds(): UniqueEntityId[] {\r\n    return [...this.props.childEventIds];\r\n  }\r\n\r\n  get appliedRules(): CorrelationRule[] {\r\n    return [...this.props.appliedRules];\r\n  }\r\n\r\n  get correlationMatches(): CorrelationMatch[] {\r\n    return [...this.props.correlationMatches];\r\n  }\r\n\r\n  get correlationResult(): CorrelationResult | undefined {\r\n    return this.props.correlationResult ? { ...this.props.correlationResult } : undefined;\r\n  }\r\n\r\n  get correlationStartedAt(): Date | undefined {\r\n    return this.props.correlationStartedAt;\r\n  }\r\n\r\n  get correlationCompletedAt(): Date | undefined {\r\n    return this.props.correlationCompletedAt;\r\n  }\r\n\r\n  get correlationAttempts(): number {\r\n    return this.props.correlationAttempts || 0;\r\n  }\r\n\r\n  get lastCorrelationError(): string | undefined {\r\n    return this.props.lastCorrelationError;\r\n  }\r\n\r\n  get attackChain(): AttackChain | undefined {\r\n    return this.props.attackChain ? { ...this.props.attackChain } : undefined;\r\n  }\r\n\r\n  get relatedEventIds(): UniqueEntityId[] {\r\n    return [...this.props.relatedEventIds];\r\n  }\r\n\r\n  get correlationPatterns(): string[] {\r\n    return [...this.props.correlationPatterns];\r\n  }\r\n\r\n  get temporalCorrelation(): typeof this.props.temporalCorrelation {\r\n    return this.props.temporalCorrelation ? { ...this.props.temporalCorrelation } : undefined;\r\n  }\r\n\r\n  get spatialCorrelation(): typeof this.props.spatialCorrelation {\r\n    return this.props.spatialCorrelation ? { ...this.props.spatialCorrelation } : undefined;\r\n  }\r\n\r\n  get behavioralCorrelation(): typeof this.props.behavioralCorrelation {\r\n    return this.props.behavioralCorrelation ? { ...this.props.behavioralCorrelation } : undefined;\r\n  }\r\n\r\n  get requiresManualReview(): boolean {\r\n    return this.props.requiresManualReview || false;\r\n  }\r\n\r\n  get reviewNotes(): string | undefined {\r\n    return this.props.reviewNotes;\r\n  }\r\n\r\n  get reviewedBy(): string | undefined {\r\n    return this.props.reviewedBy;\r\n  }\r\n\r\n  get reviewedAt(): Date | undefined {\r\n    return this.props.reviewedAt;\r\n  }\r\n\r\n  get correlationQualityScore(): number | undefined {\r\n    return this.props.correlationQualityScore;\r\n  }\r\n\r\n  get validationErrors(): string[] {\r\n    return this.props.validationErrors ? [...this.props.validationErrors] : [];\r\n  }\r\n\r\n  // Business methods\r\n\r\n  /**\r\n   * Start correlation process\r\n   */\r\n  startCorrelation(): void {\r\n    if (this.props.correlationStatus !== CorrelationStatus.PENDING) {\r\n      throw new Error('Can only start correlation for pending events');\r\n    }\r\n\r\n    this.props.correlationStatus = CorrelationStatus.IN_PROGRESS;\r\n    this.props.correlationStartedAt = new Date();\r\n    this.props.correlationAttempts = (this.props.correlationAttempts || 0) + 1;\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Complete correlation process\r\n   */\r\n  completeCorrelation(result: CorrelationResult): void {\r\n    if (this.props.correlationStatus !== CorrelationStatus.IN_PROGRESS) {\r\n      throw new Error('Can only complete correlation for in-progress events');\r\n    }\r\n\r\n    this.props.correlationStatus = result.success ? CorrelationStatus.COMPLETED : CorrelationStatus.PARTIAL;\r\n    this.props.correlationCompletedAt = new Date();\r\n    this.props.correlationResult = result;\r\n    this.props.lastCorrelationError = undefined;\r\n\r\n    // Calculate correlation quality score based on result\r\n    this.calculateCorrelationQualityScore(result);\r\n\r\n    // Determine if manual review is required\r\n    this.determineManualReviewRequirement();\r\n\r\n    this.addDomainEvent(new CorrelatedEventStatusChangedDomainEvent(\r\n      this.id,\r\n      {\r\n        oldStatus: CorrelationStatus.IN_PROGRESS,\r\n        newStatus: this.props.correlationStatus,\r\n        result,\r\n        correlationQualityScore: this.props.correlationQualityScore,\r\n        requiresManualReview: this.props.requiresManualReview || false,\r\n      }\r\n    ));\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Fail correlation process\r\n   */\r\n  failCorrelation(error: string, result?: Partial<CorrelationResult>): void {\r\n    if (this.props.correlationStatus !== CorrelationStatus.IN_PROGRESS) {\r\n      throw new Error('Can only fail correlation for in-progress events');\r\n    }\r\n\r\n    this.props.correlationStatus = CorrelationStatus.FAILED;\r\n    this.props.lastCorrelationError = error;\r\n    \r\n    if (result) {\r\n      this.props.correlationResult = {\r\n        success: false,\r\n        appliedRules: result.appliedRules || [],\r\n        failedRules: result.failedRules || [],\r\n        warnings: result.warnings || [],\r\n        errors: result.errors || [error],\r\n        processingDurationMs: result.processingDurationMs || 0,\r\n        confidenceScore: result.confidenceScore || 0,\r\n        rulesUsed: result.rulesUsed || 0,\r\n        matchesFound: result.matchesFound || 0,\r\n        patternsIdentified: result.patternsIdentified || [],\r\n      };\r\n    }\r\n\r\n    this.addDomainEvent(new CorrelatedEventCorrelationFailedDomainEvent(\r\n      this.id,\r\n      {\r\n        enrichedEventId: this.props.enrichedEventId,\r\n        error,\r\n        attempt: this.correlationAttempts,\r\n        maxAttemptsExceeded: this.hasExceededMaxCorrelationAttempts(),\r\n      }\r\n    ));\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Skip correlation process\r\n   */\r\n  skipCorrelation(reason: string): void {\r\n    if (![CorrelationStatus.PENDING, CorrelationStatus.FAILED].includes(this.props.correlationStatus)) {\r\n      throw new Error('Can only skip correlation for pending or failed events');\r\n    }\r\n\r\n    this.props.correlationStatus = CorrelationStatus.SKIPPED;\r\n    this.props.lastCorrelationError = undefined;\r\n    this.props.reviewNotes = reason;\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Reset correlation for retry\r\n   */\r\n  resetCorrelation(): void {\r\n    if (this.hasExceededMaxCorrelationAttempts()) {\r\n      throw new Error('Cannot reset correlation: maximum attempts exceeded');\r\n    }\r\n\r\n    this.props.correlationStatus = CorrelationStatus.PENDING;\r\n    this.props.correlationStartedAt = undefined;\r\n    this.props.correlationCompletedAt = undefined;\r\n    this.props.lastCorrelationError = undefined;\r\n    this.props.correlationResult = undefined;\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Add correlation match\r\n   */\r\n  addCorrelationMatch(match: CorrelationMatch): void {\r\n    if (this.props.correlationMatches.length >= CorrelatedEvent.MAX_CORRELATION_MATCHES) {\r\n      throw new Error(`Cannot add more than ${CorrelatedEvent.MAX_CORRELATION_MATCHES} correlation matches`);\r\n    }\r\n\r\n    // Check for duplicate match\r\n    const existingMatch = this.props.correlationMatches.find(\r\n      m => m.eventId.equals(match.eventId) && m.ruleId === match.ruleId\r\n    );\r\n\r\n    if (existingMatch) {\r\n      // Update existing match with higher confidence\r\n      if (match.confidence > existingMatch.confidence) {\r\n        Object.assign(existingMatch, match);\r\n      }\r\n    } else {\r\n      // Add new match\r\n      this.props.correlationMatches.push(match);\r\n    }\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Add related event\r\n   */\r\n  addRelatedEvent(eventId: UniqueEntityId): void {\r\n    if (this.props.relatedEventIds.length >= CorrelatedEvent.MAX_RELATED_EVENTS) {\r\n      throw new Error(`Cannot add more than ${CorrelatedEvent.MAX_RELATED_EVENTS} related events`);\r\n    }\r\n\r\n    // Check for duplicate\r\n    const exists = this.props.relatedEventIds.some(id => id.equals(eventId));\r\n    if (!exists) {\r\n      this.props.relatedEventIds.push(eventId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add child event\r\n   */\r\n  addChildEvent(eventId: UniqueEntityId): void {\r\n    // Check for duplicate\r\n    const exists = this.props.childEventIds.some(id => id.equals(eventId));\r\n    if (!exists) {\r\n      this.props.childEventIds.push(eventId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Set attack chain\r\n   */\r\n  setAttackChain(attackChain: AttackChain): void {\r\n    this.props.attackChain = attackChain;\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Add correlation pattern\r\n   */\r\n  addCorrelationPattern(pattern: string): void {\r\n    if (!this.props.correlationPatterns.includes(pattern)) {\r\n      this.props.correlationPatterns.push(pattern);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update temporal correlation\r\n   */\r\n  updateTemporalCorrelation(temporal: NonNullable<CorrelatedEventProps['temporalCorrelation']>): void {\r\n    this.props.temporalCorrelation = temporal;\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Update spatial correlation\r\n   */\r\n  updateSpatialCorrelation(spatial: NonNullable<CorrelatedEventProps['spatialCorrelation']>): void {\r\n    this.props.spatialCorrelation = spatial;\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Update behavioral correlation\r\n   */\r\n  updateBehavioralCorrelation(behavioral: NonNullable<CorrelatedEventProps['behavioralCorrelation']>): void {\r\n    this.props.behavioralCorrelation = behavioral;\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Update correlated data\r\n   */\r\n  updateCorrelatedData(correlatedData: Record<string, any>): void {\r\n    this.props.correlatedData = { ...this.props.correlatedData, ...correlatedData };\r\n  }\r\n\r\n  /**\r\n   * Add applied correlation rule\r\n   */\r\n  addAppliedRule(rule: CorrelationRule): void {\r\n    const existingRule = this.props.appliedRules.find(r => r.id === rule.id);\r\n    if (!existingRule) {\r\n      this.props.appliedRules.push(rule);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update correlation quality score\r\n   */\r\n  updateCorrelationQualityScore(score: number): void {\r\n    if (score < 0 || score > 100) {\r\n      throw new Error('Correlation quality score must be between 0 and 100');\r\n    }\r\n\r\n    this.props.correlationQualityScore = score;\r\n    this.determineManualReviewRequirement();\r\n  }\r\n\r\n  /**\r\n   * Add validation errors\r\n   */\r\n  addValidationErrors(errors: string[]): void {\r\n    const currentErrors = this.props.validationErrors || [];\r\n    const newErrors = [...currentErrors, ...errors];\r\n\r\n    if (newErrors.length > CorrelatedEvent.MAX_VALIDATION_ERRORS) {\r\n      throw new Error(`Cannot have more than ${CorrelatedEvent.MAX_VALIDATION_ERRORS} validation errors`);\r\n    }\r\n\r\n    this.props.validationErrors = newErrors;\r\n  }\r\n\r\n  /**\r\n   * Clear validation errors\r\n   */\r\n  clearValidationErrors(): void {\r\n    this.props.validationErrors = [];\r\n  }\r\n\r\n  /**\r\n   * Mark for manual review\r\n   */\r\n  markForManualReview(reason: string): void {\r\n    this.props.requiresManualReview = true;\r\n    this.props.reviewNotes = reason;\r\n  }\r\n\r\n  /**\r\n   * Complete manual review\r\n   */\r\n  completeManualReview(reviewedBy: string, notes?: string): void {\r\n    if (!this.props.requiresManualReview) {\r\n      throw new Error('Event is not marked for manual review');\r\n    }\r\n\r\n    this.props.reviewedBy = reviewedBy;\r\n    this.props.reviewedAt = new Date();\r\n    if (notes) {\r\n      this.props.reviewNotes = notes;\r\n    }\r\n  }\r\n\r\n  // Query methods\r\n\r\n  /**\r\n   * Check if correlation is completed\r\n   */\r\n  isCorrelationCompleted(): boolean {\r\n    return this.props.correlationStatus === CorrelationStatus.COMPLETED;\r\n  }\r\n\r\n  /**\r\n   * Check if correlation failed\r\n   */\r\n  isCorrelationFailed(): boolean {\r\n    return this.props.correlationStatus === CorrelationStatus.FAILED;\r\n  }\r\n\r\n  /**\r\n   * Check if correlation is in progress\r\n   */\r\n  isCorrelationInProgress(): boolean {\r\n    return this.props.correlationStatus === CorrelationStatus.IN_PROGRESS;\r\n  }\r\n\r\n  /**\r\n   * Check if correlation was skipped\r\n   */\r\n  isCorrelationSkipped(): boolean {\r\n    return this.props.correlationStatus === CorrelationStatus.SKIPPED;\r\n  }\r\n\r\n  /**\r\n   * Check if correlation is partial\r\n   */\r\n  isCorrelationPartial(): boolean {\r\n    return this.props.correlationStatus === CorrelationStatus.PARTIAL;\r\n  }\r\n\r\n  /**\r\n   * Check if event has high correlation quality\r\n   */\r\n  hasHighCorrelationQuality(): boolean {\r\n    return (this.props.correlationQualityScore || 0) >= CorrelatedEvent.MIN_CORRELATION_QUALITY_SCORE;\r\n  }\r\n\r\n  /**\r\n   * Check if event has validation errors\r\n   */\r\n  hasValidationErrors(): boolean {\r\n    return (this.props.validationErrors?.length || 0) > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if event has exceeded max correlation attempts\r\n   */\r\n  hasExceededMaxCorrelationAttempts(): boolean {\r\n    return this.correlationAttempts >= CorrelatedEvent.MAX_CORRELATION_ATTEMPTS;\r\n  }\r\n\r\n  /**\r\n   * Check if event is ready for next processing stage\r\n   */\r\n  isReadyForNextStage(): boolean {\r\n    return (this.isCorrelationCompleted() || this.isCorrelationPartial()) && \r\n           this.hasHighCorrelationQuality() && \r\n           !this.hasValidationErrors() &&\r\n           (!this.requiresManualReview || this.reviewedAt !== undefined);\r\n  }\r\n\r\n  /**\r\n   * Check if event has high confidence correlation\r\n   */\r\n  isHighConfidenceCorrelation(): boolean {\r\n    return (this.props.correlationResult?.confidenceScore || 0) >= CorrelatedEvent.HIGH_CONFIDENCE_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Check if event has attack chain\r\n   */\r\n  hasAttackChain(): boolean {\r\n    return !!this.props.attackChain;\r\n  }\r\n\r\n  /**\r\n   * Check if event has temporal correlation\r\n   */\r\n  hasTemporalCorrelation(): boolean {\r\n    return !!this.props.temporalCorrelation;\r\n  }\r\n\r\n  /**\r\n   * Check if event has spatial correlation\r\n   */\r\n  hasSpatialCorrelation(): boolean {\r\n    return !!this.props.spatialCorrelation;\r\n  }\r\n\r\n  /**\r\n   * Check if event has behavioral correlation\r\n   */\r\n  hasBehavioralCorrelation(): boolean {\r\n    return !!this.props.behavioralCorrelation;\r\n  }\r\n\r\n  /**\r\n   * Get correlation duration\r\n   */\r\n  getCorrelationDuration(): number | null {\r\n    if (!this.props.correlationStartedAt) {\r\n      return null;\r\n    }\r\n\r\n    const endTime = this.props.correlationCompletedAt || new Date();\r\n    return endTime.getTime() - this.props.correlationStartedAt.getTime();\r\n  }\r\n\r\n  /**\r\n   * Get applied rule names\r\n   */\r\n  getAppliedRuleNames(): string[] {\r\n    return this.props.appliedRules.map(rule => rule.name);\r\n  }\r\n\r\n  /**\r\n   * Check if specific rule was applied\r\n   */\r\n  hasAppliedRule(ruleId: string): boolean {\r\n    return this.props.appliedRules.some(rule => rule.id === ruleId);\r\n  }\r\n\r\n  /**\r\n   * Get matches by rule\r\n   */\r\n  getMatchesByRule(ruleId: string): CorrelationMatch[] {\r\n    return this.props.correlationMatches.filter(match => match.ruleId === ruleId);\r\n  }\r\n\r\n  /**\r\n   * Get matches by type\r\n   */\r\n  getMatchesByType(matchType: CorrelationMatchType): CorrelationMatch[] {\r\n    return this.props.correlationMatches.filter(match => match.matchType === matchType);\r\n  }\r\n\r\n  /**\r\n   * Get average match confidence\r\n   */\r\n  getAverageMatchConfidence(): number | null {\r\n    if (this.props.correlationMatches.length === 0) return null;\r\n    \r\n    const totalConfidence = this.props.correlationMatches.reduce((sum, match) => sum + match.confidence, 0);\r\n    return totalConfidence / this.props.correlationMatches.length;\r\n  }\r\n\r\n  /**\r\n   * Get highest confidence match\r\n   */\r\n  getHighestConfidenceMatch(): CorrelationMatch | null {\r\n    if (this.props.correlationMatches.length === 0) return null;\r\n    \r\n    return this.props.correlationMatches.reduce((highest, match) => \r\n      match.confidence > highest.confidence ? match : highest\r\n    );\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private calculateCorrelationQualityScore(result: CorrelationResult): void {\r\n    let score = 100;\r\n\r\n    // Reduce score for failed rules\r\n    const failedRulesPenalty = result.failedRules.length * 15;\r\n    score -= failedRulesPenalty;\r\n\r\n    // Reduce score for warnings\r\n    const warningsPenalty = result.warnings.length * 5;\r\n    score -= warningsPenalty;\r\n\r\n    // Reduce score for errors\r\n    const errorsPenalty = result.errors.length * 20;\r\n    score -= errorsPenalty;\r\n\r\n    // Reduce score for low confidence\r\n    if (result.confidenceScore < 70) {\r\n      score -= (70 - result.confidenceScore);\r\n    }\r\n\r\n    // Boost score for patterns identified\r\n    const patternsBonus = Math.min(result.patternsIdentified.length * 5, 20);\r\n    score += patternsBonus;\r\n\r\n    // Boost score for matches found\r\n    const matchesBonus = Math.min(result.matchesFound * 2, 15);\r\n    score += matchesBonus;\r\n\r\n    this.props.correlationQualityScore = Math.max(0, Math.min(100, score));\r\n  }\r\n\r\n  private determineManualReviewRequirement(): void {\r\n    let requiresReview = false;\r\n\r\n    // High confidence correlations with attack chains require review\r\n    if (this.hasAttackChain() && this.isHighConfidenceCorrelation()) {\r\n      requiresReview = true;\r\n    }\r\n\r\n    // Low correlation quality requires review\r\n    if (!this.hasHighCorrelationQuality()) {\r\n      requiresReview = true;\r\n    }\r\n\r\n    // Critical events require review\r\n    if (this.severity === EventSeverity.CRITICAL) {\r\n      requiresReview = true;\r\n    }\r\n\r\n    // Too many validation errors require review\r\n    if (this.hasValidationErrors() && this.validationErrors.length > 5) {\r\n      requiresReview = true;\r\n    }\r\n\r\n    this.props.requiresManualReview = requiresReview;\r\n  }\r\n}"], "version": 3}