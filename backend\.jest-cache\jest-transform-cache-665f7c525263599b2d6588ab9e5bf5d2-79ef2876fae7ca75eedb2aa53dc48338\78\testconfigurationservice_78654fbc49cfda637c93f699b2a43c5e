6e0e714837926960f661a25b36dfddb3
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TestConfigurationService_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestConfigurationService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("typeorm");
const testing_1 = require("@nestjs/testing");
/**
 * Test Configuration Service
 *
 * Centralized test configuration management providing:
 * - Environment-specific test configurations
 * - Database setup and teardown for testing
 * - Mock service configuration and management
 * - Test data generation and cleanup utilities
 * - Performance testing configuration
 * - Integration testing environment setup
 */
let TestConfigurationService = TestConfigurationService_1 = class TestConfigurationService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(TestConfigurationService_1.name);
    }
    /**
     * Get test database configuration
     */
    getTestDatabaseConfig() {
        const isCI = process.env.CI === 'true';
        const testDbName = `sentinel_test_${Date.now()}_${Math.random().toString(36).substring(7)}`;
        return {
            type: 'postgres',
            host: this.configService.get('TEST_DB_HOST', 'localhost'),
            port: this.configService.get('TEST_DB_PORT', 5433),
            username: this.configService.get('TEST_DB_USERNAME', 'test_user'),
            password: this.configService.get('TEST_DB_PASSWORD', 'test_password'),
            database: isCI ? testDbName : this.configService.get('TEST_DB_NAME', 'sentinel_test'),
            entities: [__dirname + '/../../**/*.entity{.ts,.js}'],
            synchronize: true,
            dropSchema: true,
            logging: this.configService.get('TEST_DB_LOGGING', false),
            maxQueryExecutionTime: 1000,
            extra: {
                connectionLimit: 10,
                acquireTimeout: 30000,
                timeout: 30000,
            },
        };
    }
    /**
     * Get test Redis configuration
     */
    getTestRedisConfig() {
        return {
            host: this.configService.get('TEST_REDIS_HOST', 'localhost'),
            port: this.configService.get('TEST_REDIS_PORT', 6380),
            db: this.configService.get('TEST_REDIS_DB', 1),
            password: this.configService.get('TEST_REDIS_PASSWORD'),
            keyPrefix: 'sentinel_test:',
            retryDelayOnFailover: 100,
            maxRetriesPerRequest: 3,
            lazyConnect: true,
        };
    }
    /**
     * Get test queue configuration
     */
    getTestQueueConfig() {
        return {
            redis: this.getTestRedisConfig(),
            defaultJobOptions: {
                removeOnComplete: 5,
                removeOnFail: 10,
                attempts: 1,
                backoff: {
                    type: 'exponential',
                    delay: 1000,
                },
            },
            settings: {
                stalledInterval: 30000,
                maxStalledCount: 1,
            },
        };
    }
    /**
     * Get test monitoring configuration
     */
    getTestMonitoringConfig() {
        return {
            metricsEnabled: true,
            metricsPrefix: 'sentinel_test',
            systemMetricsEnabled: false,
            businessMetricsEnabled: true,
            systemMetricsInterval: 60000,
            businessMetricsInterval: 30000,
            httpMetricsEnabled: true,
            httpMetricsUserTracking: false,
            httpMetricsSlowRequestThreshold: 1000,
            databaseMetricsEnabled: true,
            databaseMetricsQueryLogging: false,
            cacheMetricsEnabled: true,
            queueMetricsEnabled: true,
            metricsEndpointEnabled: false,
            metricsEndpointAuth: false,
            metricsCollectionTimeout: 5000,
            metricsMaxMemoryUsage: 50 * 1024 * 1024, // 50MB
        };
    }
    /**
     * Get test workflow execution configuration
     */
    getTestWorkflowConfig() {
        return {
            maxConcurrentExecutions: 5,
            defaultExecutionTimeout: 30000,
            maxParallelSteps: 3,
            enableStateCheckpoints: true,
            checkpointInterval: 10000,
            maxCheckpointsPerExecution: 5,
            stateCompressionEnabled: false,
            enablePerformanceMonitoring: true,
            metricsCollectionInterval: 5000,
            bottleneckDetectionEnabled: true,
            resourceMonitoringEnabled: false,
            enableAutoRetry: true,
            defaultMaxRetries: 2,
            defaultRetryDelay: 1000,
            enableCompensation: true,
            enableRollback: true,
            circuitBreakerEnabled: true,
            circuitBreakerThreshold: 3,
            circuitBreakerTimeout: 10000,
            externalServiceTimeout: 5000,
            externalServiceMaxRetries: 2,
            enableExecutionNotifications: false,
            enableExecutionAudit: true,
            enableAutoCleanup: false,
        };
    }
    /**
     * Get test external service configurations
     */
    getTestExternalServiceConfig() {
        return {
            mockServices: {
                enabled: true,
                baseUrl: 'http://localhost:3001',
                timeout: 5000,
                retries: 2,
            },
            threatIntelligence: {
                enabled: false,
                mockData: true,
                apiKey: 'test-api-key',
                baseUrl: 'http://localhost:3002',
            },
            notifications: {
                email: {
                    enabled: false,
                    mockSend: true,
                    provider: 'mock',
                },
                slack: {
                    enabled: false,
                    mockSend: true,
                    webhookUrl: 'http://localhost:3003/slack',
                },
                sms: {
                    enabled: false,
                    mockSend: true,
                    provider: 'mock',
                },
            },
            aiMl: {
                enabled: false,
                mockResponses: true,
                baseUrl: 'http://localhost:3004',
                timeout: 10000,
            },
        };
    }
    /**
     * Get test security configuration
     */
    getTestSecurityConfig() {
        return {
            jwt: {
                secret: 'test-jwt-secret-key-for-testing-only',
                expiresIn: '1h',
                issuer: 'sentinel-test',
                audience: 'sentinel-test-users',
            },
            encryption: {
                algorithm: 'aes-256-gcm',
                key: 'test-encryption-key-32-characters',
                ivLength: 16,
            },
            rateLimit: {
                enabled: false,
                windowMs: 60000,
                max: 1000,
            },
            cors: {
                origin: ['http://localhost:3000', 'http://localhost:3001'],
                credentials: true,
                methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
            },
        };
    }
    /**
     * Get test performance configuration
     */
    getTestPerformanceConfig() {
        return {
            loadTesting: {
                maxConcurrentUsers: 50,
                rampUpDuration: 10000, // 10 seconds
                testDuration: 60000, // 1 minute
                thinkTime: 1000, // 1 second between requests
                scenarios: [
                    {
                        name: 'workflow_execution',
                        weight: 40,
                        endpoints: ['/workflow-execution/start', '/workflow-execution/:id/status'],
                    },
                    {
                        name: 'monitoring_dashboard',
                        weight: 30,
                        endpoints: ['/monitoring/metrics', '/monitoring/health'],
                    },
                    {
                        name: 'template_management',
                        weight: 20,
                        endpoints: ['/templates', '/templates/:id'],
                    },
                    {
                        name: 'reporting',
                        weight: 10,
                        endpoints: ['/reports', '/reports/:id/generate'],
                    },
                ],
            },
            benchmarking: {
                warmupRequests: 100,
                measurementRequests: 1000,
                concurrency: 10,
                timeout: 30000,
                acceptableResponseTime: 500, // milliseconds
                acceptableErrorRate: 0.01, // 1%
                acceptableThroughput: 100, // requests per second
            },
        };
    }
    /**
     * Initialize test database
     */
    async initializeTestDatabase() {
        try {
            const config = this.getTestDatabaseConfig();
            this.testDataSource = new typeorm_1.DataSource(config);
            await this.testDataSource.initialize();
            this.logger.log('Test database initialized successfully');
            return this.testDataSource;
        }
        catch (error) {
            this.logger.error('Failed to initialize test database', error.stack);
            throw error;
        }
    }
    /**
     * Cleanup test database
     */
    async cleanupTestDatabase() {
        try {
            if (this.testDataSource && this.testDataSource.isInitialized) {
                await this.testDataSource.dropDatabase();
                await this.testDataSource.destroy();
                this.logger.log('Test database cleaned up successfully');
            }
        }
        catch (error) {
            this.logger.error('Failed to cleanup test database', error.stack);
            throw error;
        }
    }
    /**
     * Create test module with configuration
     */
    async createTestModule(moduleMetadata) {
        try {
            const testingModuleBuilder = testing_1.Test.createTestingModule(moduleMetadata);
            // Override configurations for testing
            testingModuleBuilder.overrideProvider(config_1.ConfigService).useValue({
                get: (key, defaultValue) => {
                    const testConfigs = {
                        ...this.getTestDatabaseConfig(),
                        ...this.getTestRedisConfig(),
                        ...this.getTestMonitoringConfig(),
                        ...this.getTestWorkflowConfig(),
                        ...this.getTestExternalServiceConfig(),
                        ...this.getTestSecurityConfig(),
                        ...this.getTestPerformanceConfig(),
                    };
                    return testConfigs[key] !== undefined ? testConfigs[key] : defaultValue;
                },
            });
            this.testingModule = await testingModuleBuilder.compile();
            // Enable logging for tests if needed
            if (this.configService.get('TEST_ENABLE_LOGGING', false)) {
                this.testingModule.useLogger(new common_1.Logger());
            }
            this.logger.log('Test module created successfully');
            return this.testingModule;
        }
        catch (error) {
            this.logger.error('Failed to create test module', error.stack);
            throw error;
        }
    }
    /**
     * Cleanup test module
     */
    async cleanupTestModule() {
        try {
            if (this.testingModule) {
                await this.testingModule.close();
                this.logger.log('Test module cleaned up successfully');
            }
        }
        catch (error) {
            this.logger.error('Failed to cleanup test module', error.stack);
            throw error;
        }
    }
    /**
     * Get test environment info
     */
    getTestEnvironmentInfo() {
        return {
            nodeEnv: process.env.NODE_ENV || 'test',
            isCI: process.env.CI === 'true',
            testTimeout: parseInt(process.env.TEST_TIMEOUT || '30000', 10),
            parallelTests: process.env.TEST_PARALLEL !== 'false',
            coverage: process.env.TEST_COVERAGE === 'true',
            verbose: process.env.TEST_VERBOSE === 'true',
            bail: process.env.TEST_BAIL === 'true',
        };
    }
    /**
     * Validate test environment
     */
    async validateTestEnvironment() {
        const issues = [];
        const warnings = [];
        try {
            // Check database connectivity
            const testDb = await this.initializeTestDatabase();
            await testDb.query('SELECT 1');
            await this.cleanupTestDatabase();
        }
        catch (error) {
            issues.push(`Database connectivity issue: ${error.message}`);
        }
        // Check required environment variables
        const requiredEnvVars = [
            'TEST_DB_HOST',
            'TEST_DB_PORT',
            'TEST_DB_USERNAME',
            'TEST_DB_PASSWORD',
        ];
        for (const envVar of requiredEnvVars) {
            if (!process.env[envVar]) {
                warnings.push(`Missing environment variable: ${envVar}`);
            }
        }
        // Check Node.js version
        const nodeVersion = process.version;
        const majorVersion = parseInt(nodeVersion.substring(1).split('.')[0], 10);
        if (majorVersion < 16) {
            issues.push(`Node.js version ${nodeVersion} is not supported. Minimum version: 16.x`);
        }
        // Check available memory
        const totalMemory = process.memoryUsage().heapTotal;
        const minMemory = 512 * 1024 * 1024; // 512MB
        if (totalMemory < minMemory) {
            warnings.push(`Low memory available: ${Math.round(totalMemory / 1024 / 1024)}MB. Recommended: 512MB+`);
        }
        return {
            valid: issues.length === 0,
            issues,
            warnings,
        };
    }
    /**
     * Generate test report configuration
     */
    getTestReportConfig() {
        return {
            reporters: [
                'default',
                ['jest-junit', {
                        outputDirectory: './test-results',
                        outputName: 'junit.xml',
                        classNameTemplate: '{classname}',
                        titleTemplate: '{title}',
                        ancestorSeparator: ' › ',
                        usePathForSuiteName: true,
                    }],
                ['jest-html-reporters', {
                        publicPath: './test-results',
                        filename: 'test-report.html',
                        expand: true,
                        hideIcon: false,
                        pageTitle: 'Sentinel Reporting Module Test Report',
                    }],
            ],
            coverageReporters: [
                'text',
                'lcov',
                'html',
                'json-summary',
            ],
            coverageDirectory: './coverage',
            collectCoverageFrom: [
                'src/**/*.{ts,js}',
                '!src/**/*.spec.{ts,js}',
                '!src/**/*.test.{ts,js}',
                '!src/**/*.d.ts',
                '!src/**/index.ts',
                '!src/**/*.interface.ts',
                '!src/**/*.enum.ts',
                '!src/**/*.constant.ts',
            ],
            coverageThreshold: {
                global: {
                    branches: 80,
                    functions: 80,
                    lines: 80,
                    statements: 80,
                },
                './src/modules/reporting/workflow-execution/': {
                    branches: 85,
                    functions: 85,
                    lines: 85,
                    statements: 85,
                },
                './src/modules/reporting/monitoring/': {
                    branches: 85,
                    functions: 85,
                    lines: 85,
                    statements: 85,
                },
            },
        };
    }
    /**
     * Get test data configuration
     */
    getTestDataConfig() {
        return {
            fixtures: {
                users: {
                    admin: {
                        id: 'test-admin-id',
                        email: '<EMAIL>',
                        role: 'admin',
                        permissions: ['*'],
                    },
                    user: {
                        id: 'test-user-id',
                        email: '<EMAIL>',
                        role: 'user',
                        permissions: ['workflow.view', 'workflow.execute'],
                    },
                    viewer: {
                        id: 'test-viewer-id',
                        email: '<EMAIL>',
                        role: 'viewer',
                        permissions: ['workflow.view'],
                    },
                },
                templates: {
                    simple: {
                        id: 'test-template-simple',
                        name: 'Simple Test Template',
                        definition: {
                            steps: [
                                { id: 'step1', type: 'notification', config: { message: 'Test' } },
                                { id: 'step2', type: 'validation', config: { rules: [] } },
                            ],
                        },
                    },
                    complex: {
                        id: 'test-template-complex',
                        name: 'Complex Test Template',
                        definition: {
                            steps: [
                                { id: 'step1', type: 'parallel', config: { steps: [] } },
                                { id: 'step2', type: 'conditional', config: { condition: true } },
                                { id: 'step3', type: 'loop', config: { type: 'for_each' } },
                            ],
                        },
                    },
                },
                executions: {
                    running: {
                        id: 'test-execution-running',
                        status: 'running',
                        startedAt: new Date(),
                        progress: 50,
                    },
                    completed: {
                        id: 'test-execution-completed',
                        status: 'completed',
                        startedAt: new Date(Date.now() - 60000),
                        completedAt: new Date(),
                        progress: 100,
                    },
                    failed: {
                        id: 'test-execution-failed',
                        status: 'failed',
                        startedAt: new Date(Date.now() - 30000),
                        completedAt: new Date(),
                        progress: 75,
                        errorDetails: { errorMessage: 'Test error' },
                    },
                },
            },
            generators: {
                execution: {
                    count: 100,
                    statusDistribution: {
                        completed: 0.7,
                        failed: 0.2,
                        running: 0.1,
                    },
                    durationRange: [1000, 300000], // 1 second to 5 minutes
                },
                metrics: {
                    count: 1000,
                    timeRange: 24 * 60 * 60 * 1000, // 24 hours
                    categories: ['business', 'technical', 'system'],
                },
            },
        };
    }
};
exports.TestConfigurationService = TestConfigurationService;
exports.TestConfigurationService = TestConfigurationService = TestConfigurationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], TestConfigurationService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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