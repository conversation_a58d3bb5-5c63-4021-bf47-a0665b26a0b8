eeb499a6d4bfd471833aa7c075b1a1b1
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const base_aggregate_root_1 = require("../../domain/base-aggregate-root");
const base_domain_event_1 = require("../../domain/base-domain-event");
const unique_entity_id_value_object_1 = require("../../value-objects/unique-entity-id.value-object");
// Test domain event
class TestDomainEvent extends base_domain_event_1.BaseDomainEvent {
    constructor(aggregateId, message) {
        super(aggregateId, { message });
    }
    get message() {
        return this.eventData.message;
    }
}
class AnotherTestEvent extends base_domain_event_1.BaseDomainEvent {
    constructor(aggregateId, value) {
        super(aggregateId, { value });
    }
    get value() {
        return this.eventData.value;
    }
}
class TestAggregateRoot extends base_aggregate_root_1.BaseAggregateRoot {
    constructor(props, id) {
        super(props, id);
    }
    get name() {
        return this.props.name;
    }
    get status() {
        return this.props.status;
    }
    // Business method that raises domain event
    changeName(newName) {
        const oldName = this.props.name;
        this.props.name = newName;
        this.addDomainEvent(new TestDomainEvent(this.id, `Name changed from ${oldName} to ${newName}`));
    }
    // Business method that raises multiple events
    activate() {
        this.props.status = 'active';
        this.addDomainEvent(new TestDomainEvent(this.id, 'Aggregate activated'));
        this.addDomainEvent(new AnotherTestEvent(this.id, 1));
    }
    // Method to test invariant validation
    validateTestInvariant() {
        this.validateInvariants();
    }
    // Override to add custom business rules
    validateInvariants() {
        super.validateInvariants();
        if (!this.props.name || this.props.name.trim().length === 0) {
            throw new Error('Name cannot be empty');
        }
    }
}
describe('BaseAggregateRoot', () => {
    describe('construction', () => {
        it('should create aggregate with provided props', () => {
            const props = {
                name: 'Test Aggregate',
                status: 'inactive'
            };
            const aggregate = new TestAggregateRoot(props);
            expect(aggregate.name).toBe('Test Aggregate');
            expect(aggregate.status).toBe('inactive');
            expect(aggregate.id).toBeInstanceOf(unique_entity_id_value_object_1.UniqueEntityId);
        });
        it('should create aggregate with provided ID', () => {
            const props = {
                name: 'Test Aggregate',
                status: 'inactive'
            };
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const aggregate = new TestAggregateRoot(props, id);
            expect(aggregate.id.equals(id)).toBe(true);
        });
        it('should start with no domain events', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            expect(aggregate.domainEvents).toHaveLength(0);
            expect(aggregate.hasUnpublishedEvents()).toBe(false);
        });
    });
    describe('domain event management', () => {
        it('should add domain events', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            aggregate.changeName('New Name');
            expect(aggregate.domainEvents).toHaveLength(1);
            expect(aggregate.domainEvents[0]).toBeInstanceOf(TestDomainEvent);
            expect(aggregate.domainEvents[0].message).toBe('Name changed from Test Aggregate to New Name');
        });
        it('should add multiple domain events', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            aggregate.activate();
            expect(aggregate.domainEvents).toHaveLength(2);
            expect(aggregate.domainEvents[0]).toBeInstanceOf(TestDomainEvent);
            expect(aggregate.domainEvents[1]).toBeInstanceOf(AnotherTestEvent);
        });
        it('should prevent duplicate events', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            const event = new TestDomainEvent(aggregate.id, 'Test message');
            // Add the same event twice
            aggregate.addDomainEvent(event);
            aggregate.addDomainEvent(event);
            expect(aggregate.domainEvents).toHaveLength(1);
        });
        it('should return copy of domain events', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            aggregate.changeName('New Name');
            const events1 = aggregate.domainEvents;
            const events2 = aggregate.domainEvents;
            expect(events1).not.toBe(events2); // Different array instances
            expect(events1).toEqual(events2); // Same content
        });
        it('should clear domain events', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            aggregate.changeName('New Name');
            expect(aggregate.domainEvents).toHaveLength(1);
            aggregate.clearEvents();
            expect(aggregate.domainEvents).toHaveLength(0);
        });
        it('should remove specific domain event', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            aggregate.activate(); // Adds 2 events
            expect(aggregate.domainEvents).toHaveLength(2);
            const eventToRemove = aggregate.domainEvents[0];
            aggregate.removeDomainEvent(eventToRemove);
            expect(aggregate.domainEvents).toHaveLength(1);
            expect(aggregate.domainEvents[0]).not.toBe(eventToRemove);
        });
    });
    describe('event querying', () => {
        it('should get events by type', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            aggregate.activate(); // Adds TestDomainEvent and AnotherTestEvent
            const testEvents = aggregate.getEventsByType(TestDomainEvent);
            const anotherEvents = aggregate.getEventsByType(AnotherTestEvent);
            expect(testEvents).toHaveLength(1);
            expect(anotherEvents).toHaveLength(1);
            expect(testEvents[0]).toBeInstanceOf(TestDomainEvent);
            expect(anotherEvents[0]).toBeInstanceOf(AnotherTestEvent);
        });
        it('should check if has events of specific type', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            expect(aggregate.hasEventsOfType(TestDomainEvent)).toBe(false);
            aggregate.changeName('New Name');
            expect(aggregate.hasEventsOfType(TestDomainEvent)).toBe(true);
            expect(aggregate.hasEventsOfType(AnotherTestEvent)).toBe(false);
        });
        it('should check for unpublished events', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            expect(aggregate.hasUnpublishedEvents()).toBe(false);
            aggregate.changeName('New Name');
            expect(aggregate.hasUnpublishedEvents()).toBe(true);
        });
        it('should get unpublished events', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            aggregate.activate(); // Adds 2 events
            const unpublishedEvents = aggregate.getUnpublishedEvents();
            expect(unpublishedEvents).toHaveLength(2);
            // Mark first event as dispatched
            aggregate.domainEvents[0].markAsDispatched();
            const stillUnpublished = aggregate.getUnpublishedEvents();
            expect(stillUnpublished).toHaveLength(1);
        });
        it('should mark events for dispatch', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            aggregate.activate();
            expect(aggregate.hasUnpublishedEvents()).toBe(true);
            aggregate.markEventsForDispatch();
            expect(aggregate.hasUnpublishedEvents()).toBe(false);
            expect(aggregate.domainEvents.every(e => e.isDispatched)).toBe(true);
        });
    });
    describe('versioning', () => {
        it('should track version based on event count', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            expect(aggregate.getVersion()).toBe(0);
            aggregate.changeName('New Name');
            expect(aggregate.getVersion()).toBe(1);
            aggregate.activate();
            expect(aggregate.getVersion()).toBe(3); // +2 events from activate
        });
        it('should create snapshot', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            aggregate.changeName('New Name');
            const snapshot = aggregate.createSnapshot();
            expect(snapshot.aggregateId).toBe(aggregate.id.toString());
            expect(snapshot.version).toBe(1);
            expect(snapshot.state).toEqual({ name: 'New Name', status: 'inactive' });
            expect(snapshot.timestamp).toBeInstanceOf(Date);
        });
    });
    describe('invariant validation', () => {
        it('should validate invariants', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            expect(() => aggregate.validateTestInvariant()).not.toThrow();
        });
        it('should throw error for invalid invariants', () => {
            const aggregate = new TestAggregateRoot({
                name: '',
                status: 'inactive'
            });
            expect(() => aggregate.validateTestInvariant()).toThrow('Name cannot be empty');
        });
        it('should validate base invariants', () => {
            // Create aggregate with null ID (bypassing constructor)
            const aggregate = Object.create(TestAggregateRoot.prototype);
            aggregate.props = { name: 'Test', status: 'active' };
            aggregate._id = null;
            expect(() => aggregate.validateTestInvariant()).toThrow('Aggregate must have a valid ID');
        });
    });
    describe('equality comparison', () => {
        it('should be equal to itself', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            expect(aggregate.equals(aggregate)).toBe(true);
        });
        it('should be equal to aggregate with same ID and version', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const props = {
                name: 'Test Aggregate',
                status: 'inactive'
            };
            const aggregate1 = new TestAggregateRoot(props, id);
            const aggregate2 = new TestAggregateRoot(props, id);
            expect(aggregate1.equals(aggregate2)).toBe(true);
        });
        it('should not be equal to aggregate with same ID but different version', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const props = {
                name: 'Test Aggregate',
                status: 'inactive'
            };
            const aggregate1 = new TestAggregateRoot(props, id);
            const aggregate2 = new TestAggregateRoot(props, id);
            aggregate1.changeName('New Name'); // Changes version
            expect(aggregate1.equals(aggregate2)).toBe(false);
        });
        it('should not be equal to aggregate with different ID', () => {
            const props = {
                name: 'Test Aggregate',
                status: 'inactive'
            };
            const aggregate1 = new TestAggregateRoot(props);
            const aggregate2 = new TestAggregateRoot(props);
            expect(aggregate1.equals(aggregate2)).toBe(false);
        });
    });
    describe('JSON serialization', () => {
        it('should serialize to JSON with events and version', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            aggregate.changeName('New Name');
            const json = aggregate.toJSON();
            expect(json).toHaveProperty('id');
            expect(json).toHaveProperty('name', 'New Name');
            expect(json).toHaveProperty('status', 'inactive');
            expect(json).toHaveProperty('domainEvents');
            expect(json).toHaveProperty('version', 1);
            expect(json.domainEvents).toHaveLength(1);
        });
        it('should include event metadata in JSON', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            aggregate.activate();
            const json = aggregate.toJSON();
            expect(json.domainEvents).toHaveLength(2);
            expect(json.domainEvents[0]).toHaveProperty('eventType', 'TestDomainEvent');
            expect(json.domainEvents[0]).toHaveProperty('eventId');
            expect(json.domainEvents[0]).toHaveProperty('occurredOn');
            expect(json.domainEvents[0]).toHaveProperty('isDispatched', false);
        });
    });
    describe('cloning', () => {
        it('should create a deep copy', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            aggregate.changeName('New Name');
            const clone = aggregate.clone();
            expect(clone).not.toBe(aggregate);
            expect(clone.id.equals(aggregate.id)).toBe(true);
            expect(clone.name).toBe(aggregate.name);
            expect(clone.status).toBe(aggregate.status);
            expect(clone.domainEvents).toHaveLength(aggregate.domainEvents.length);
        });
        it('should create independent clone', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            const clone = aggregate.clone();
            // Modify original
            aggregate.changeName('Modified Name');
            // Clone should not be affected
            expect(clone.name).toBe('Test Aggregate');
            expect(clone.domainEvents).toHaveLength(0);
        });
    });
    describe('inheritance', () => {
        class ExtendedAggregateRoot extends TestAggregateRoot {
            constructor(props, id) {
                super(props, id);
            }
            get category() {
                return this.props.category;
            }
            changeCategory(newCategory) {
                this.props.category = newCategory;
                this.addDomainEvent(new TestDomainEvent(this.id, `Category changed to ${newCategory}`));
            }
        }
        it('should support inheritance', () => {
            const props = {
                name: 'Extended Aggregate',
                status: 'active',
                category: 'Test Category'
            };
            const aggregate = new ExtendedAggregateRoot(props);
            expect(aggregate).toBeInstanceOf(ExtendedAggregateRoot);
            expect(aggregate).toBeInstanceOf(TestAggregateRoot);
            expect(aggregate).toBeInstanceOf(base_aggregate_root_1.BaseAggregateRoot);
            expect(aggregate.category).toBe('Test Category');
        });
        it('should maintain base functionality in derived classes', () => {
            const props = {
                name: 'Extended Aggregate',
                status: 'active',
                category: 'Test Category'
            };
            const aggregate = new ExtendedAggregateRoot(props);
            aggregate.changeCategory('New Category');
            expect(aggregate.domainEvents).toHaveLength(1);
            expect(aggregate.getVersion()).toBe(1);
        });
    });
    describe('edge cases', () => {
        it('should handle empty event list operations', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            expect(aggregate.getEventsByType(TestDomainEvent)).toHaveLength(0);
            expect(aggregate.hasEventsOfType(TestDomainEvent)).toBe(false);
            expect(aggregate.getUnpublishedEvents()).toHaveLength(0);
            aggregate.clearEvents(); // Should not throw
            aggregate.markEventsForDispatch(); // Should not throw
        });
        it('should handle removing non-existent event', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            const nonExistentEvent = new TestDomainEvent(aggregate.id, 'Non-existent');
            expect(() => aggregate.removeDomainEvent(nonExistentEvent)).not.toThrow();
            expect(aggregate.domainEvents).toHaveLength(0);
        });
        it('should handle snapshot with no events', () => {
            const aggregate = new TestAggregateRoot({
                name: 'Test Aggregate',
                status: 'inactive'
            });
            const snapshot = aggregate.createSnapshot();
            expect(snapshot.version).toBe(0);
            expect(snapshot.state).toEqual({ name: 'Test Aggregate', status: 'inactive' });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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