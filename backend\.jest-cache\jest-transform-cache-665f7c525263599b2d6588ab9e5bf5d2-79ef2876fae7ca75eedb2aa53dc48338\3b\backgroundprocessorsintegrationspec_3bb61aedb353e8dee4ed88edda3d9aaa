06fcc7f722c21b30649990d40aacf257
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const event_emitter_1 = require("@nestjs/event-emitter");
const bull_1 = require("@nestjs/bull");
const base_test_class_1 = require("../base/base-test.class");
const background_processors_module_1 = require("../../background-processors/background-processors.module");
const monitoring_module_1 = require("../../monitoring/monitoring.module");
const job_queue_manager_service_1 = require("../../background-processors/services/job-queue-manager.service");
const workflow_execution_processor_1 = require("../../background-processors/processors/workflow-execution.processor");
const asset_discovery_processor_1 = require("../../background-processors/processors/asset-discovery.processor");
const event_correlation_processor_1 = require("../../background-processors/processors/event-correlation.processor");
const metrics_collection_service_1 = require("../../monitoring/services/metrics-collection.service");
const job_execution_entity_1 = require("../../background-processors/entities/job-execution.entity");
const job_queue_config_entity_1 = require("../../background-processors/entities/job-queue-config.entity");
const metric_entity_1 = require("../../monitoring/entities/metric.entity");
/**
 * Background Processors Integration Tests
 *
 * Comprehensive integration tests for background processors providing:
 * - Job queue validation with Bull Redis integration testing
 * - Retry mechanism testing with exponential backoff validation
 * - Performance benchmarking with resource utilization monitoring
 * - Cross-processor communication and event handling validation
 * - Database transaction testing with job execution persistence
 * - Monitoring integration with metrics collection verification
 * - Error handling and circuit breaker functionality testing
 * - Queue health monitoring and alerting system validation
 */
describe('Background Processors Integration Tests', () => {
    let app;
    let testingModule;
    let jobQueueManager;
    let workflowProcessor;
    let assetDiscoveryProcessor;
    let eventCorrelationProcessor;
    let metricsService;
    let eventEmitter;
    let workflowQueue;
    let assetQueue;
    let eventQueue;
    let baseTest;
    // Test entities
    const testEntities = [job_execution_entity_1.JobExecution, job_queue_config_entity_1.JobQueueConfig, metric_entity_1.Metric];
    beforeAll(async () => {
        // Create base test instance
        baseTest = new (class extends base_test_class_1.BaseTestClass {
        })();
        // Setup test environment with mock queues
        await baseTest.setupTestEnvironment({
            imports: [
                background_processors_module_1.BackgroundProcessorsModule,
                monitoring_module_1.MonitoringModule,
            ],
            providers: [
                // Mock queue providers
                {
                    provide: (0, bull_1.getQueueToken)('workflow-execution'),
                    useValue: createMockQueue('workflow-execution'),
                },
                {
                    provide: (0, bull_1.getQueueToken)('asset-discovery'),
                    useValue: createMockQueue('asset-discovery'),
                },
                {
                    provide: (0, bull_1.getQueueToken)('event-correlation'),
                    useValue: createMockQueue('event-correlation'),
                },
            ],
        }, testEntities);
        app = baseTest.app;
        testingModule = baseTest.testingModule;
        // Get service instances
        jobQueueManager = baseTest.getService(job_queue_manager_service_1.JobQueueManagerService);
        workflowProcessor = baseTest.getService(workflow_execution_processor_1.WorkflowExecutionProcessor);
        assetDiscoveryProcessor = baseTest.getService(asset_discovery_processor_1.AssetDiscoveryProcessor);
        eventCorrelationProcessor = baseTest.getService(event_correlation_processor_1.EventCorrelationProcessor);
        metricsService = baseTest.getService(metrics_collection_service_1.MetricsCollectionService);
        eventEmitter = baseTest.getService(event_emitter_1.EventEmitter2);
        workflowQueue = baseTest.getService((0, bull_1.getQueueToken)('workflow-execution'));
        assetQueue = baseTest.getService((0, bull_1.getQueueToken)('asset-discovery'));
        eventQueue = baseTest.getService((0, bull_1.getQueueToken)('event-correlation'));
    });
    afterAll(async () => {
        await baseTest.cleanupTestEnvironment();
    });
    beforeEach(async () => {
        await baseTest.setupTestData();
        jest.clearAllMocks();
    });
    afterEach(async () => {
        await baseTest.cleanupTestData();
    });
    describe('Job Queue Management', () => {
        it('should add jobs to different queues successfully', async () => {
            // Arrange
            const workflowJobData = {
                workflowId: 'test-workflow',
                templateId: 'template-123',
                inputData: { param: 'value' },
            };
            const assetJobData = {
                scanId: 'scan-123',
                networkRanges: ['192.168.1.0/24'],
                scanConfiguration: {
                    scanType: 'full',
                    protocols: ['tcp'],
                    portRanges: ['1-1000'],
                    timeoutMs: 5000,
                    maxConcurrency: 10,
                    enableVulnerabilityScanning: true,
                    enableServiceDetection: true,
                    enableOSDetection: true,
                },
                filters: {
                    includePatterns: [],
                    excludePatterns: [],
                    assetTypes: [],
                },
            };
            // Act
            const workflowJob = await jobQueueManager.addJob('workflow-execution', 'execute-workflow', workflowJobData, { priority: 10 });
            const assetJob = await jobQueueManager.addJob('asset-discovery', 'discover-network', assetJobData, { priority: 8 });
            // Assert
            expect(workflowJob.queueName).toBe('workflow-execution');
            expect(workflowJob.jobType).toBe('execute-workflow');
            expect(workflowJob.priority).toBe(10);
            expect(assetJob.queueName).toBe('asset-discovery');
            expect(assetJob.jobType).toBe('discover-network');
            expect(assetJob.priority).toBe(8);
            // Verify queue add methods were called
            expect(workflowQueue.add).toHaveBeenCalledWith('execute-workflow', expect.objectContaining({
                jobExecutionId: workflowJob.id,
                ...workflowJobData,
            }), expect.objectContaining({ priority: 10 }));
            expect(assetQueue.add).toHaveBeenCalledWith('discover-network', expect.objectContaining({
                jobExecutionId: assetJob.id,
                ...assetJobData,
            }), expect.objectContaining({ priority: 8 }));
        });
        it('should get queue statistics for all queues', async () => {
            // Arrange
            const mockStats = {
                waiting: [{ id: '1' }, { id: '2' }],
                active: [{ id: '3' }],
                completed: [{ id: '4' }, { id: '5' }, { id: '6' }],
                failed: [{ id: '7' }],
                delayed: [],
            };
            // Mock queue methods
            Object.values({ workflowQueue, assetQueue, eventQueue }).forEach(queue => {
                queue.getWaiting.mockResolvedValue(mockStats.waiting);
                queue.getActive.mockResolvedValue(mockStats.active);
                queue.getCompleted.mockResolvedValue(mockStats.completed);
                queue.getFailed.mockResolvedValue(mockStats.failed);
                queue.getDelayed.mockResolvedValue(mockStats.delayed);
            });
            // Act
            const allStatistics = await jobQueueManager.getAllQueueStatistics();
            // Assert
            expect(allStatistics).toHaveLength(3);
            allStatistics.forEach(stats => {
                expect(stats.counts).toEqual({
                    waiting: 2,
                    active: 1,
                    completed: 3,
                    failed: 1,
                    delayed: 0,
                    total: 7,
                });
                expect(stats.health).toBeDefined();
                expect(['workflow-execution', 'asset-discovery', 'event-correlation'])
                    .toContain(stats.queueName);
            });
        });
        it('should pause and resume queues', async () => {
            // Act
            const paused = await jobQueueManager.pauseQueue('workflow-execution');
            const resumed = await jobQueueManager.resumeQueue('workflow-execution');
            // Assert
            expect(paused).toBe(true);
            expect(resumed).toBe(true);
            expect(workflowQueue.pause).toHaveBeenCalled();
            expect(workflowQueue.resume).toHaveBeenCalled();
        });
    });
    describe('Retry Mechanisms', () => {
        it('should retry failed jobs with exponential backoff', async () => {
            // Arrange
            const jobExecution = await baseTest.createTestJobExecution({
                jobType: 'workflow_execution',
                queueName: 'workflow-execution',
                status: 'failed',
                retryInfo: {
                    attempt: 1,
                    maxAttempts: 3,
                    backoffStrategy: 'exponential',
                    backoffDelay: 1000,
                    retryHistory: [],
                },
                error: {
                    message: 'Temporary failure',
                    retryable: true,
                    timestamp: new Date().toISOString(),
                },
            });
            // Act
            const retried = await jobQueueManager.retryJob(jobExecution.id);
            // Assert
            expect(retried).toBe(true);
            // Verify job execution was updated
            const updatedJob = await baseTest.getRepository(job_execution_entity_1.JobExecution)
                .findOne({ where: { id: jobExecution.id } });
            expect(updatedJob.retryInfo.attempt).toBe(2);
            expect(updatedJob.status).toBe('pending');
            expect(updatedJob.error).toBeNull();
            // Verify retry event was emitted
            await baseTest.assertEventEmitted('job.retried', {
                jobExecutionId: jobExecution.id,
                attempt: 2,
            });
        });
        it('should not retry jobs that exceed max attempts', async () => {
            // Arrange
            const jobExecution = await baseTest.createTestJobExecution({
                jobType: 'workflow_execution',
                queueName: 'workflow-execution',
                status: 'failed',
                retryInfo: {
                    attempt: 3,
                    maxAttempts: 3,
                    backoffStrategy: 'exponential',
                    backoffDelay: 1000,
                    retryHistory: [],
                },
                error: {
                    message: 'Max retries exceeded',
                    retryable: true,
                    timestamp: new Date().toISOString(),
                },
            });
            // Act
            const retried = await jobQueueManager.retryJob(jobExecution.id);
            // Assert
            expect(retried).toBe(false);
            // Verify job execution was not updated
            const updatedJob = await baseTest.getRepository(job_execution_entity_1.JobExecution)
                .findOne({ where: { id: jobExecution.id } });
            expect(updatedJob.retryInfo.attempt).toBe(3);
            expect(updatedJob.status).toBe('failed');
        });
        it('should not retry non-retryable errors', async () => {
            // Arrange
            const jobExecution = await baseTest.createTestJobExecution({
                jobType: 'workflow_execution',
                queueName: 'workflow-execution',
                status: 'failed',
                retryInfo: {
                    attempt: 1,
                    maxAttempts: 3,
                    backoffStrategy: 'exponential',
                    backoffDelay: 1000,
                    retryHistory: [],
                },
                error: {
                    message: 'Validation error',
                    retryable: false,
                    timestamp: new Date().toISOString(),
                },
            });
            // Act
            const retried = await jobQueueManager.retryJob(jobExecution.id);
            // Assert
            expect(retried).toBe(false);
        });
    });
    describe('Performance Testing', () => {
        it('should handle high job throughput', async () => {
            // Arrange
            const jobCount = 100;
            const jobData = {
                workflowId: 'perf-test',
                templateId: 'template-perf',
                inputData: { test: 'data' },
            };
            // Act
            const { result: jobs, executionTime } = await baseTest.measureExecutionTime(async () => {
                const promises = Array.from({ length: jobCount }, (_, i) => jobQueueManager.addJob('workflow-execution', 'execute-workflow', { ...jobData, workflowId: `perf-test-${i}` }, { priority: Math.floor(Math.random() * 10) }));
                return await Promise.all(promises);
            });
            // Assert
            expect(jobs).toHaveLength(jobCount);
            expect(executionTime).toBeLessThan(5000); // Should complete within 5 seconds
            expect(jobs.every(job => job.status === 'pending')).toBe(true);
            console.log(`High Throughput Test (${jobCount} jobs):`, {
                totalTime: `${executionTime}ms`,
                averageTimePerJob: `${executionTime / jobCount}ms`,
                jobsPerSecond: Math.round((jobCount / executionTime) * 1000),
            });
        });
        it('should monitor resource utilization during processing', async () => {
            // Arrange
            const initialMemory = process.memoryUsage();
            const jobExecution = await baseTest.createTestJobExecution({
                jobType: 'workflow_execution',
                queueName: 'workflow-execution',
                status: 'pending',
            });
            // Act
            const { result, executionTime } = await baseTest.measureExecutionTime(async () => {
                return await workflowProcessor.executeWorkflow({
                    id: 'resource-test-job',
                    name: 'execute-workflow',
                    data: {
                        jobExecutionId: jobExecution.id,
                        workflowId: 'resource-test',
                        templateId: 'template-resource',
                        inputData: {},
                        executionContext: { userId: baseTest.testUser.id },
                        configuration: {},
                    },
                    opts: {},
                    timestamp: Date.now(),
                    attemptsMade: 0,
                    delay: 0,
                    progress: jest.fn(),
                });
            });
            const finalMemory = process.memoryUsage();
            // Assert
            expect(result).toBeDefined();
            expect(executionTime).toBeLessThan(10000);
            const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
            expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // Less than 50MB increase
            console.log('Resource Utilization Test:', {
                executionTime: `${executionTime}ms`,
                memoryIncrease: `${Math.round(memoryIncrease / 1024 / 1024)}MB`,
                initialHeap: `${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`,
                finalHeap: `${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`,
            });
        });
    });
    describe('Event Handling', () => {
        it('should emit and handle job lifecycle events', async () => {
            // Arrange
            const eventPromises = [
                baseTest.waitForEvent('job.added'),
                baseTest.waitForEvent('workflow.execution.completed'),
            ];
            const jobData = {
                workflowId: 'event-test',
                templateId: 'template-event',
                inputData: {},
            };
            // Act
            const jobExecution = await jobQueueManager.addJob('workflow-execution', 'execute-workflow', jobData);
            await workflowProcessor.executeWorkflow({
                id: 'event-test-job',
                name: 'execute-workflow',
                data: {
                    jobExecutionId: jobExecution.id,
                    ...jobData,
                    executionContext: { userId: baseTest.testUser.id },
                    configuration: {},
                },
                opts: {},
                timestamp: Date.now(),
                attemptsMade: 0,
                delay: 0,
                progress: jest.fn(),
            });
            // Assert
            const [jobAddedEvent, workflowCompletedEvent] = await Promise.all(eventPromises);
            expect(jobAddedEvent).toMatchObject({
                jobExecutionId: jobExecution.id,
                queueName: 'workflow-execution',
                jobType: 'execute-workflow',
            });
            expect(workflowCompletedEvent).toMatchObject({
                workflowId: 'event-test',
                templateId: 'template-event',
                jobExecutionId: jobExecution.id,
            });
        });
        it('should handle cross-processor event communication', async () => {
            // Arrange
            const correlationEventPromise = baseTest.waitForEvent('event.correlation.completed');
            const jobExecution = await baseTest.createTestJobExecution({
                jobType: 'event_correlation',
                queueName: 'event-correlation',
                status: 'pending',
            });
            const correlationData = {
                correlationId: 'cross-processor-test',
                events: [
                    { id: '1', type: 'login', timestamp: new Date(), source: 'auth' },
                    { id: '2', type: 'access', timestamp: new Date(), source: 'api' },
                ],
                correlationRules: [
                    { id: 'rule1', name: 'Login followed by access', pattern: 'login->access' },
                ],
                timeWindow: {
                    windowSizeMs: 300000,
                    slidingIntervalMs: 60000,
                    maxEventsPerWindow: 1000,
                },
                analysisConfig: {
                    enableAnomalyDetection: true,
                    enablePatternMatching: true,
                    enableThreatIntelligence: false,
                    enableBehavioralAnalysis: false,
                    correlationThreshold: 0.8,
                    alertThreshold: 0.9,
                },
            };
            // Act
            await eventCorrelationProcessor.correlateEvents({
                id: 'cross-processor-job',
                name: 'correlate-events',
                data: {
                    jobExecutionId: jobExecution.id,
                    ...correlationData,
                },
                opts: {},
                timestamp: Date.now(),
                attemptsMade: 0,
                delay: 0,
                progress: jest.fn(),
            });
            // Assert
            const correlationEvent = await correlationEventPromise;
            expect(correlationEvent).toMatchObject({
                correlationId: 'cross-processor-test',
                jobExecutionId: jobExecution.id,
                eventsProcessed: 2,
            });
        });
    });
    describe('Monitoring Integration', () => {
        it('should record processor metrics', async () => {
            // Arrange
            const recordMetricSpy = jest.spyOn(metricsService, 'recordMetric');
            const jobExecution = await baseTest.createTestJobExecution({
                jobType: 'asset_discovery',
                queueName: 'asset-discovery',
                status: 'pending',
            });
            // Act
            await assetDiscoveryProcessor.discoverNetwork({
                id: 'metrics-test-job',
                name: 'discover-network',
                data: {
                    jobExecutionId: jobExecution.id,
                    scanId: 'metrics-scan',
                    networkRanges: ['192.168.1.0/24'],
                    scanConfiguration: {
                        scanType: 'quick',
                        protocols: ['tcp'],
                        portRanges: ['80', '443'],
                        timeoutMs: 5000,
                        maxConcurrency: 5,
                        enableVulnerabilityScanning: false,
                        enableServiceDetection: true,
                        enableOSDetection: false,
                    },
                    filters: {
                        includePatterns: [],
                        excludePatterns: [],
                        assetTypes: [],
                    },
                },
                opts: {},
                timestamp: Date.now(),
                attemptsMade: 0,
                delay: 0,
                progress: jest.fn(),
            });
            // Assert
            expect(recordMetricSpy).toHaveBeenCalledWith(expect.objectContaining({
                name: 'asset_discovery_total',
                value: 1,
                category: 'business',
                type: 'counter',
                labels: expect.objectContaining({
                    scan_id: 'metrics-scan',
                    status: 'success',
                }),
            }));
            expect(recordMetricSpy).toHaveBeenCalledWith(expect.objectContaining({
                name: 'asset_discovery_duration',
                category: 'performance',
                type: 'histogram',
                unit: 'ms',
            }));
        });
    });
    // Helper function to create mock queue
    function createMockQueue(name) {
        return {
            name,
            add: jest.fn().mockResolvedValue({ id: `${name}-job-${Date.now()}` }),
            getJob: jest.fn(),
            getWaiting: jest.fn().mockResolvedValue([]),
            getActive: jest.fn().mockResolvedValue([]),
            getCompleted: jest.fn().mockResolvedValue([]),
            getFailed: jest.fn().mockResolvedValue([]),
            getDelayed: jest.fn().mockResolvedValue([]),
            pause: jest.fn().mockResolvedValue(undefined),
            resume: jest.fn().mockResolvedValue(undefined),
            close: jest.fn().mockResolvedValue(undefined),
        };
    }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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