{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\index.spec.ts", "mappings": ";AAAA;;;;GAIG;;AAEH,6DAA6D;AAC7D,kCAAgC;AAChC,iCAA+B;AAC/B,iCAA+B;AAE/B,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;QACvD,+DAA+D;QAC/D,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\index.spec.ts"], "sourcesContent": ["/**\r\n * Pattern Tests Index\r\n * \r\n * This file ensures all pattern tests are properly exported and can be run together.\r\n */\r\n\r\n// Import all pattern test suites to ensure they are executed\r\nimport './circuit-breaker.spec';\r\nimport './retry-strategy.spec';\r\nimport './cache-strategy.spec';\r\n\r\ndescribe('Patterns Integration', () => {\r\n  it('should have all pattern test suites available', () => {\r\n    // This test ensures all pattern test files are properly loaded\r\n    expect(true).toBe(true);\r\n  });\r\n});"], "version": 3}