48d3f14bb8068e2e5ac434551e2b7aeb
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityDiscoveredEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const threat_severity_enum_1 = require("../enums/threat-severity.enum");
const confidence_level_enum_1 = require("../enums/confidence-level.enum");
/**
 * Vulnerability Discovered Domain Event
 *
 * Published when a new security vulnerability is discovered in the environment.
 * This event triggers vulnerability management and remediation workflows.
 *
 * Key use cases:
 * - Initiate vulnerability assessment procedures
 * - Trigger remediation planning workflows
 * - Send vulnerability notifications
 * - Update vulnerability databases
 * - Begin compliance impact analysis
 * - Escalate critical vulnerabilities
 */
class VulnerabilityDiscoveredEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, {
            eventVersion: 1,
            ...options,
            metadata: {
                eventType: 'VulnerabilityDiscovered',
                domain: 'Security',
                aggregateType: 'Vulnerability',
                ...options?.metadata,
            },
        });
    }
    /**
     * Get the vulnerability ID
     */
    get vulnerabilityId() {
        return this.eventData.vulnerabilityId;
    }
    /**
     * Get the CVE ID
     */
    get cveId() {
        return this.eventData.cveId;
    }
    /**
     * Get the vulnerability title
     */
    get title() {
        return this.eventData.title;
    }
    /**
     * Get the vulnerability severity
     */
    get severity() {
        return this.eventData.severity;
    }
    /**
     * Get the vulnerability category
     */
    get category() {
        return this.eventData.category;
    }
    /**
     * Get the vulnerability type
     */
    get vulnerabilityType() {
        return this.eventData.type;
    }
    /**
     * Get the discovery confidence
     */
    get confidence() {
        return this.eventData.confidence;
    }
    /**
     * Get the risk score
     */
    get riskScore() {
        return this.eventData.riskScore;
    }
    /**
     * Get the affected asset count
     */
    get affectedAssetCount() {
        return this.eventData.affectedAssetCount;
    }
    /**
     * Get the discovery method
     */
    get discoveryMethod() {
        return this.eventData.discoveryMethod;
    }
    /**
     * Get the discovery timestamp
     */
    get discoveryTimestamp() {
        return this.eventData.timestamp;
    }
    /**
     * Check if vulnerability is critical severity
     */
    isCriticalSeverity() {
        return this.eventData.severity === threat_severity_enum_1.ThreatSeverity.CRITICAL;
    }
    /**
     * Check if vulnerability is high severity or above
     */
    isHighSeverityOrAbove() {
        return this.eventData.severity === threat_severity_enum_1.ThreatSeverity.HIGH ||
            this.eventData.severity === threat_severity_enum_1.ThreatSeverity.CRITICAL;
    }
    /**
     * Check if vulnerability has high confidence
     */
    hasHighConfidence() {
        return this.eventData.confidence === confidence_level_enum_1.ConfidenceLevel.HIGH ||
            this.eventData.confidence === confidence_level_enum_1.ConfidenceLevel.VERY_HIGH ||
            this.eventData.confidence === confidence_level_enum_1.ConfidenceLevel.CONFIRMED;
    }
    /**
     * Check if vulnerability has high risk score
     */
    hasHighRiskScore() {
        return this.eventData.riskScore >= 70;
    }
    /**
     * Check if vulnerability affects multiple assets
     */
    affectsMultipleAssets() {
        return this.eventData.affectedAssetCount > 1;
    }
    /**
     * Check if vulnerability affects many assets
     */
    affectsManyAssets() {
        return this.eventData.affectedAssetCount >= 10;
    }
    /**
     * Check if vulnerability was discovered by automated scan
     */
    isAutomatedDiscovery() {
        return this.eventData.discoveryMethod === 'automated_scan';
    }
    /**
     * Check if vulnerability requires immediate attention
     */
    requiresImmediateAttention() {
        return this.isCriticalSeverity() ||
            (this.isHighSeverityOrAbove() && this.hasHighRiskScore()) ||
            (this.hasHighRiskScore() && this.affectsManyAssets());
    }
    /**
     * Check if vulnerability requires executive notification
     */
    requiresExecutiveNotification() {
        return this.isCriticalSeverity() &&
            (this.hasHighRiskScore() || this.affectsManyAssets());
    }
    /**
     * Check if vulnerability requires compliance reporting
     */
    requiresComplianceReporting() {
        return this.isCriticalSeverity() ||
            (this.isHighSeverityOrAbove() && this.hasHighRiskScore());
    }
    /**
     * Get remediation priority
     */
    getRemediationPriority() {
        if (this.requiresImmediateAttention()) {
            return 'critical';
        }
        if (this.isHighSeverityOrAbove()) {
            return 'high';
        }
        if (this.eventData.severity === threat_severity_enum_1.ThreatSeverity.MEDIUM) {
            return 'medium';
        }
        return 'low';
    }
    /**
     * Get remediation SLA (in days)
     */
    getRemediationSLA() {
        switch (this.eventData.severity) {
            case threat_severity_enum_1.ThreatSeverity.CRITICAL: return 1;
            case threat_severity_enum_1.ThreatSeverity.HIGH: return 7;
            case threat_severity_enum_1.ThreatSeverity.MEDIUM: return 30;
            case threat_severity_enum_1.ThreatSeverity.LOW: return 90;
            default: return 180;
        }
    }
    /**
     * Get notification urgency
     */
    getNotificationUrgency() {
        if (this.requiresImmediateAttention()) {
            return 'critical';
        }
        if (this.isHighSeverityOrAbove()) {
            return 'high';
        }
        if (this.hasHighRiskScore() || this.affectsMultipleAssets()) {
            return 'normal';
        }
        return 'low';
    }
    /**
     * Get assessment timeline (hours)
     */
    getAssessmentTimeline() {
        switch (this.eventData.severity) {
            case threat_severity_enum_1.ThreatSeverity.CRITICAL: return 2; // 2 hours
            case threat_severity_enum_1.ThreatSeverity.HIGH: return 8; // 8 hours
            case threat_severity_enum_1.ThreatSeverity.MEDIUM: return 24; // 24 hours
            case threat_severity_enum_1.ThreatSeverity.LOW: return 72; // 3 days
            default: return 168; // 7 days
        }
    }
    /**
     * Get notification channels
     */
    getNotificationChannels() {
        const channels = ['webhook'];
        if (this.requiresImmediateAttention()) {
            channels.push('email', 'slack', 'sms');
        }
        else if (this.isHighSeverityOrAbove()) {
            channels.push('email', 'slack');
        }
        else if (this.hasHighRiskScore()) {
            channels.push('email');
        }
        if (this.requiresExecutiveNotification()) {
            channels.push('executive_dashboard');
        }
        return channels;
    }
    /**
     * Get automated response actions
     */
    getAutomatedResponseActions() {
        const actions = [];
        if (this.requiresImmediateAttention()) {
            actions.push('create_vulnerability_ticket', 'assign_security_analyst', 'begin_assessment');
        }
        if (this.affectsManyAssets()) {
            actions.push('asset_inventory_check', 'impact_assessment');
        }
        if (this.hasHighRiskScore()) {
            actions.push('threat_intelligence_lookup', 'exploit_availability_check');
        }
        if (this.requiresComplianceReporting()) {
            actions.push('compliance_impact_assessment', 'regulatory_notification_prep');
        }
        if (this.eventData.cveId) {
            actions.push('cve_database_lookup', 'patch_availability_check');
        }
        return actions;
    }
    /**
     * Get metrics tags for this event
     */
    getMetricsTags() {
        return {
            event_type: 'vulnerability_discovered',
            severity: this.eventData.severity,
            category: this.eventData.category,
            type: this.eventData.type,
            confidence: this.eventData.confidence,
            discovery_method: this.eventData.discoveryMethod,
            has_cve: (!!this.eventData.cveId).toString(),
            risk_level: this.hasHighRiskScore() ? 'high' : 'low',
            remediation_priority: this.getRemediationPriority(),
            notification_urgency: this.getNotificationUrgency(),
            requires_immediate_attention: this.requiresImmediateAttention().toString(),
            requires_executive_notification: this.requiresExecutiveNotification().toString(),
            affected_asset_count: this.eventData.affectedAssetCount.toString(),
            affects_multiple_assets: this.affectsMultipleAssets().toString(),
        };
    }
    /**
     * Get audit log entry
     */
    getAuditLogEntry() {
        return {
            action: 'vulnerability_discovered',
            resource: 'Vulnerability',
            resourceId: this.eventData.vulnerabilityId,
            details: {
                cveId: this.eventData.cveId,
                title: this.eventData.title,
                severity: this.eventData.severity,
                category: this.eventData.category,
                type: this.eventData.type,
                confidence: this.eventData.confidence,
                riskScore: this.eventData.riskScore,
                affectedAssetCount: this.eventData.affectedAssetCount,
                discoveryMethod: this.eventData.discoveryMethod,
                requiresImmediateAttention: this.requiresImmediateAttention(),
                requiresExecutiveNotification: this.requiresExecutiveNotification(),
                remediationPriority: this.getRemediationPriority(),
                remediationSLA: this.getRemediationSLA(),
                assessmentTimeline: this.getAssessmentTimeline(),
            },
            timestamp: this.occurredOn.toISOString(),
        };
    }
    /**
     * Create integration event for external systems
     */
    toIntegrationEvent() {
        return {
            eventType: 'VulnerabilityDiscovered',
            version: '1.0',
            timestamp: this.occurredOn.toISOString(),
            data: {
                vulnerabilityId: this.eventData.vulnerabilityId,
                vulnerability: {
                    cveId: this.eventData.cveId,
                    title: this.eventData.title,
                    severity: this.eventData.severity,
                    category: this.eventData.category,
                    type: this.eventData.type,
                    confidence: this.eventData.confidence,
                    riskScore: this.eventData.riskScore,
                },
                discovery: {
                    method: this.eventData.discoveryMethod,
                    affectedAssetCount: this.eventData.affectedAssetCount,
                    timestamp: this.eventData.timestamp,
                },
                response: {
                    priority: this.getRemediationPriority(),
                    urgency: this.getNotificationUrgency(),
                    remediationSLA: this.getRemediationSLA(),
                    assessmentTimeline: this.getAssessmentTimeline(),
                    automatedActions: this.getAutomatedResponseActions(),
                },
                flags: {
                    requiresImmediateAttention: this.requiresImmediateAttention(),
                    requiresExecutiveNotification: this.requiresExecutiveNotification(),
                    requiresComplianceReporting: this.requiresComplianceReporting(),
                    hasHighRiskScore: this.hasHighRiskScore(),
                    affectsMultipleAssets: this.affectsMultipleAssets(),
                },
            },
            metadata: {
                correlationId: this.correlationId,
                causationId: this.causationId,
                domain: 'Security',
                aggregateType: 'Vulnerability',
            },
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventData: this.eventData,
            analysis: {
                isCriticalSeverity: this.isCriticalSeverity(),
                isHighSeverityOrAbove: this.isHighSeverityOrAbove(),
                hasHighConfidence: this.hasHighConfidence(),
                hasHighRiskScore: this.hasHighRiskScore(),
                affectsMultipleAssets: this.affectsMultipleAssets(),
                affectsManyAssets: this.affectsManyAssets(),
                requiresImmediateAttention: this.requiresImmediateAttention(),
                requiresExecutiveNotification: this.requiresExecutiveNotification(),
                requiresComplianceReporting: this.requiresComplianceReporting(),
                remediationPriority: this.getRemediationPriority(),
                remediationSLA: this.getRemediationSLA(),
                notificationUrgency: this.getNotificationUrgency(),
                assessmentTimeline: this.getAssessmentTimeline(),
                notificationChannels: this.getNotificationChannels(),
                automatedResponseActions: this.getAutomatedResponseActions(),
                metricsTags: this.getMetricsTags(),
            },
        };
    }
    /**
     * Create from JSON representation
     */
    static fromJSON(json) {
        return new VulnerabilityDiscoveredEvent(shared_kernel_1.UniqueEntityId.fromString(json.aggregateId), json.eventData, {
            eventId: shared_kernel_1.UniqueEntityId.fromString(json.eventId),
            occurredOn: new Date(json.occurredOn),
            correlationId: json.correlationId,
            causationId: json.causationId,
            metadata: json.metadata,
        });
    }
    /**
     * Get human-readable description
     */
    getDescription() {
        const severityText = this.eventData.severity.toUpperCase();
        const confidenceText = this.eventData.confidence.replace('_', ' ');
        const cveText = this.eventData.cveId ? ` (${this.eventData.cveId})` : '';
        const assetText = this.affectsMultipleAssets()
            ? ` affecting ${this.eventData.affectedAssetCount} assets`
            : ' affecting single asset';
        return `${severityText} severity vulnerability "${this.eventData.title}"${cveText} discovered with ${confidenceText} confidence${assetText}`;
    }
    /**
     * Get event summary for logging
     */
    getSummary() {
        return {
            eventType: 'VulnerabilityDiscovered',
            vulnerabilityId: this.eventData.vulnerabilityId,
            cveId: this.eventData.cveId,
            title: this.eventData.title,
            severity: this.eventData.severity,
            category: this.eventData.category,
            riskScore: this.eventData.riskScore,
            affectedAssetCount: this.eventData.affectedAssetCount,
            requiresImmediateAttention: this.requiresImmediateAttention(),
            remediationPriority: this.getRemediationPriority(),
            timestamp: this.occurredOn.toISOString(),
        };
    }
}
exports.VulnerabilityDiscoveredEvent = VulnerabilityDiscoveredEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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