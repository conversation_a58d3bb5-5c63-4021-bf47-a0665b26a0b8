e1279324771bd58f69b0cccc57e70e87
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportSchedule = void 0;
const typeorm_1 = require("typeorm");
const report_entity_1 = require("./report.entity");
/**
 * Report Schedule entity
 * Represents scheduled execution configurations for reports
 */
let ReportSchedule = class ReportSchedule {
    /**
     * Check if schedule is due for execution
     */
    get isDue() {
        if (!this.isActive || !this.nextRunAt)
            return false;
        return new Date() >= this.nextRunAt;
    }
    /**
     * Get success rate
     */
    get successRate() {
        if (this.runCount === 0)
            return 0;
        return (this.successCount / this.runCount) * 100;
    }
    /**
     * Update run statistics
     */
    updateRunStats(status) {
        this.runCount += 1;
        this.lastRunAt = new Date();
        this.lastRunStatus = status;
        if (status === 'success') {
            this.successCount += 1;
        }
        else if (status === 'failed') {
            this.failureCount += 1;
        }
    }
    /**
     * Calculate next run time based on cron expression
     */
    calculateNextRun() {
        // This would use a cron parser library like 'node-cron' or 'cron-parser'
        // For now, we'll implement a simple placeholder
        const now = new Date();
        // Parse basic cron expressions
        if (this.cronExpression === '0 0 * * *') { // Daily at midnight
            const nextRun = new Date(now);
            nextRun.setDate(nextRun.getDate() + 1);
            nextRun.setHours(0, 0, 0, 0);
            this.nextRunAt = nextRun;
        }
        else if (this.cronExpression === '0 0 * * 0') { // Weekly on Sunday
            const nextRun = new Date(now);
            const daysUntilSunday = (7 - nextRun.getDay()) % 7 || 7;
            nextRun.setDate(nextRun.getDate() + daysUntilSunday);
            nextRun.setHours(0, 0, 0, 0);
            this.nextRunAt = nextRun;
        }
        else if (this.cronExpression === '0 0 1 * *') { // Monthly on 1st
            const nextRun = new Date(now);
            nextRun.setMonth(nextRun.getMonth() + 1, 1);
            nextRun.setHours(0, 0, 0, 0);
            this.nextRunAt = nextRun;
        }
        else {
            // Default to daily if unknown pattern
            const nextRun = new Date(now);
            nextRun.setDate(nextRun.getDate() + 1);
            this.nextRunAt = nextRun;
        }
    }
    /**
     * Enable schedule
     */
    enable() {
        this.isActive = true;
        this.calculateNextRun();
    }
    /**
     * Disable schedule
     */
    disable() {
        this.isActive = false;
        this.nextRunAt = null;
    }
    /**
     * Validate cron expression
     */
    validateCronExpression() {
        // Basic validation - in a real implementation, use a cron parser library
        const parts = this.cronExpression.split(' ');
        if (parts.length !== 5) {
            return {
                isValid: false,
                error: 'Cron expression must have 5 parts (minute hour day month weekday)',
            };
        }
        // Check for common patterns
        const validPatterns = [
            '0 0 * * *', // Daily at midnight
            '0 0 * * 0', // Weekly on Sunday
            '0 0 1 * *', // Monthly on 1st
            '0 */6 * * *', // Every 6 hours
            '*/30 * * * *', // Every 30 minutes
        ];
        if (!validPatterns.includes(this.cronExpression)) {
            return {
                isValid: false,
                error: 'Unsupported cron expression pattern',
            };
        }
        return { isValid: true };
    }
};
exports.ReportSchedule = ReportSchedule;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ReportSchedule.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], ReportSchedule.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ReportSchedule.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_active', default: true }),
    __metadata("design:type", Boolean)
], ReportSchedule.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cron_expression' }),
    __metadata("design:type", String)
], ReportSchedule.prototype, "cronExpression", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 'UTC' }),
    __metadata("design:type", String)
], ReportSchedule.prototype, "timezone", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportSchedule.prototype, "parameters", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportSchedule.prototype, "notifications", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'retry_config', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportSchedule.prototype, "retryConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'next_run_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], ReportSchedule.prototype, "nextRunAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_run_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ReportSchedule.prototype, "lastRunAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_run_status', nullable: true }),
    __metadata("design:type", String)
], ReportSchedule.prototype, "lastRunStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'run_count', type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], ReportSchedule.prototype, "runCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'success_count', type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], ReportSchedule.prototype, "successCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'failure_count', type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], ReportSchedule.prototype, "failureCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], ReportSchedule.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ReportSchedule.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], ReportSchedule.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], ReportSchedule.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => report_entity_1.Report),
    (0, typeorm_1.JoinColumn)({ name: 'report_id' }),
    __metadata("design:type", typeof (_e = typeof report_entity_1.Report !== "undefined" && report_entity_1.Report) === "function" ? _e : Object)
], ReportSchedule.prototype, "report", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'report_id', type: 'uuid' }),
    __metadata("design:type", String)
], ReportSchedule.prototype, "reportId", void 0);
exports.ReportSchedule = ReportSchedule = __decorate([
    (0, typeorm_1.Entity)('report_schedules'),
    (0, typeorm_1.Index)(['reportId']),
    (0, typeorm_1.Index)(['isActive']),
    (0, typeorm_1.Index)(['nextRunAt'])
], ReportSchedule);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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