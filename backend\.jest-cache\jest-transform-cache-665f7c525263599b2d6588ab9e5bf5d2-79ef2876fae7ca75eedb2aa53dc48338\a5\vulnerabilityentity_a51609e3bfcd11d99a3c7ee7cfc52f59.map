{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\vulnerability\\vulnerability.entity.ts", "mappings": ";;;AAAA,gEAAiF;AAEjF,2EAAkE;AAClE,6EAAoE;AACpE,gGAA2F;AAC3F,wGAAkG;AA0ClG;;GAEG;AACH,IAAY,mBAUX;AAVD,WAAY,mBAAmB;IAC7B,gDAAyB,CAAA;IACzB,8CAAuB,CAAA;IACvB,0CAAmB,CAAA;IACnB,kDAA2B,CAAA;IAC3B,gDAAyB,CAAA;IACzB,4CAAqB,CAAA;IACrB,wCAAiB,CAAA;IACjB,wDAAiC,CAAA;IACjC,sDAA+B,CAAA;AACjC,CAAC,EAVW,mBAAmB,mCAAnB,mBAAmB,QAU9B;AA6LD;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAa,aAAc,SAAQ,iCAAqC;IAMtE,YAAY,KAAyB,EAAE,EAAmB;QACxD,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACnB,CAAC;IAES,QAAQ;QAChB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1E,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qCAAc,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,uCAAe,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,6BAA6B,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,iCAAiC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,GAAG,aAAa,CAAC,cAAc;YAClE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,GAAG,aAAa,CAAC,cAAc,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,8BAA8B,aAAa,CAAC,cAAc,QAAQ,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC;QACpH,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CACX,KAAa,EACb,WAAmB,EACnB,QAAwB,EACxB,QAAgB,EAChB,IAAY,EACZ,cAAoC,EACpC,SAAiC,EACjC,OAOC;QAED,MAAM,cAAc,GAAG,aAAa,CAAC,8BAA8B,CACjE,QAAQ,EACR,cAAc,EACd,OAAO,EAAE,UAAU,IAAI,EAAE,CAC1B,CAAC;QAEF,MAAM,KAAK,GAAuB;YAChC,KAAK,EAAE,OAAO,EAAE,KAAK;YACrB,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE;YACnB,WAAW,EAAE,WAAW,CAAC,IAAI,EAAE;YAC/B,QAAQ;YACR,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,EAAE;YACrC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE;YACzB,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;YACjB,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,uCAAe,CAAC,MAAM;YACzD,cAAc;YACd,MAAM,EAAE,mBAAmB,CAAC,UAAU;YACtC,SAAS;YACT,YAAY,EAAE,OAAO,EAAE,YAAY;YACnC,WAAW,EAAE;gBACX,MAAM,EAAE,aAAa;gBACrB,QAAQ,EAAE,aAAa,CAAC,4BAA4B,CAAC,QAAQ,EAAE,cAAc,CAAC,SAAS,CAAC;gBACxF,OAAO,EAAE,EAAE;gBACX,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,EAAE;aACb;YACD,cAAc;YACd,gBAAgB,EAAE,EAAE;YACpB,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,EAAE;YACzB,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,EAAE;SACtC,CAAC;QAEF,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;QAE/C,uBAAuB;QACvB,aAAa,CAAC,cAAc,CAAC,IAAI,6DAA4B,CAC3D,aAAa,CAAC,EAAE,EAChB;YACE,eAAe,EAAE,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC5C,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK;YAChC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK;YAChC,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,QAAQ;YACtC,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,QAAQ;YACtC,IAAI,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI;YAC9B,UAAU,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU;YAC1C,SAAS,EAAE,aAAa,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS;YACvD,kBAAkB,EAAE,aAAa,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM;YAC7D,eAAe,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM;YACrD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CACF,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,MAAM,CAAC,8BAA8B,CAC3C,QAAwB,EACxB,cAAoC,EACpC,UAAuB;QAEvB,0BAA0B;QAC1B,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,qCAAc,CAAC,QAAQ;gBAAE,SAAS,GAAG,EAAE,CAAC;gBAAC,MAAM;YACpD,KAAK,qCAAc,CAAC,IAAI;gBAAE,SAAS,GAAG,EAAE,CAAC;gBAAC,MAAM;YAChD,KAAK,qCAAc,CAAC,MAAM;gBAAE,SAAS,GAAG,EAAE,CAAC;gBAAC,MAAM;YAClD,KAAK,qCAAc,CAAC,GAAG;gBAAE,SAAS,GAAG,EAAE,CAAC;gBAAC,MAAM;YAC/C,KAAK,qCAAc,CAAC,OAAO;gBAAE,SAAS,GAAG,EAAE,CAAC;gBAAC,MAAM;QACrD,CAAC;QAED,yBAAyB;QACzB,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YACtE,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,GAAG,EAAE,CAAC,CAAC;QAChD,CAAC;QAED,+BAA+B;QAC/B,MAAM,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,KAAK,UAAU,CAAC,CAAC;QACxF,MAAM,qBAAqB,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,KAAK,MAAM,CAAC,CAAC;QAE3F,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,SAAS,IAAI,EAAE,CAAC;QAClB,CAAC;aAAM,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5C,SAAS,IAAI,EAAE,CAAC;QAClB,CAAC;QAED,sBAAsB;QACtB,MAAM,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC;QACrF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,SAAS,IAAI,EAAE,CAAC;QAClB,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAEhD,OAAO;YACL,SAAS,EAAE,cAAc;YACzB,mBAAmB,EAAE,EAAE,EAAE,gDAAgD;YACzE,WAAW,EAAE,cAAc;YAC3B,oBAAoB,EAAE;gBACpB,gBAAgB,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBACtD,eAAe,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBACrD,kBAAkB,EAAE,EAAE,EAAE,UAAU;gBAClC,cAAc,EAAE,EAAE,EAAE,UAAU;aAC/B;YACD,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,aAAa,CAAC,YAAY,CAAC,cAAc,CAAC;YACrD,YAAY,EAAE;gBACZ,eAAe,EAAE,CAAC;gBAClB,kBAAkB,EAAE,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;gBAC5E,iBAAiB,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;gBAChE,gBAAgB,EAAE,QAAQ;aAC3B;SACF,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,SAAiB;QAC3C,IAAI,SAAS,IAAI,aAAa,CAAC,uBAAuB;YAAE,OAAO,UAAU,CAAC;QAC1E,IAAI,SAAS,IAAI,aAAa,CAAC,mBAAmB;YAAE,OAAO,MAAM,CAAC;QAClE,IAAI,SAAS,IAAI,EAAE;YAAE,OAAO,QAAQ,CAAC;QACrC,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,MAAM,CAAC,4BAA4B,CACzC,QAAwB,EACxB,SAAiB;QAEjB,IAAI,QAAQ,KAAK,qCAAc,CAAC,QAAQ,IAAI,SAAS,IAAI,aAAa,CAAC,uBAAuB,EAAE,CAAC;YAC/F,OAAO,UAAU,CAAC;QACpB,CAAC;QACD,IAAI,QAAQ,KAAK,qCAAc,CAAC,IAAI,IAAI,SAAS,IAAI,aAAa,CAAC,mBAAmB,EAAE,CAAC;YACvF,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,IAAI,QAAQ,KAAK,qCAAc,CAAC,MAAM,EAAE,CAAC;YACvC,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAA8B,EAAE,MAAc;QACzD,IAAI,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACpC,OAAO,CAAC,mBAAmB;QAC7B,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;QAE9B,8CAA8C;QAC9C,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;QAE1C,uBAAuB;QACvB,IAAI,CAAC,cAAc,CAAC,IAAI,oEAA+B,CACrD,IAAI,CAAC,EAAE,EACP;YACE,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YACnC,SAAS;YACT,SAAS;YACT,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CACF,CAAC,CAAC;IACL,CAAC;IAEO,yBAAyB,CAAC,MAA2B;QAC3D,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,mBAAmB,CAAC,OAAO;gBAC9B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;oBAClD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAY,GAAG,GAAG,CAAC;gBACrD,CAAC;gBACD,MAAM;YACR,KAAK,mBAAmB,CAAC,WAAW;gBAClC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;oBACjD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,GAAG,GAAG,CAAC;gBACpD,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,aAAa,CAAC;gBAC9C,MAAM;YACR,KAAK,mBAAmB,CAAC,UAAU;gBACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;oBACtD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,gBAAgB,GAAG,GAAG,CAAC;gBACzD,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC;gBAC5C,MAAM;YACR,KAAK,mBAAmB,CAAC,QAAQ;gBAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;oBACtD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,gBAAgB,GAAG,GAAG,CAAC;gBACzD,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC;gBAC3C,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ;YAC/C,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,IAAI,aAAa,CAAC,uBAAuB,CAAC;IACtF,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,KAAK,MAAM;YAC9C,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,KAAK,UAAU,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,MAAM,KAAK,qBAAqB,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,KAAK,UAAU,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,OAAO,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,mBAAmB,EAAE;YAC1B,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,IAAI,CAAC,UAAU,EAAE;YAAE,OAAO,CAAC,CAAC;QAChC,IAAI,IAAI,CAAC,UAAU,EAAE;YAAE,OAAO,CAAC,CAAC;QAChC,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,KAAK,QAAQ;YAAE,OAAO,EAAE,CAAC;QAChE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAC5D,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAC9D,OAAO,IAAI,IAAI,EAAE,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO;YACL,mBAAmB,CAAC,UAAU;YAC9B,mBAAmB,CAAC,QAAQ;YAC5B,mBAAmB,CAAC,MAAM;SAC3B,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YACtB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAC9D,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;YACzC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;YAC/B,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;YACzC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;gBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;gBAC7B,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAC/C,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAC/C,0BAA0B,EAAE,IAAI,CAAC,0BAA0B,EAAE;gBAC7D,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBACxC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAAE;gBACjD,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;aAClC;YACD,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE;YACxC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE;SACzC,CAAC;IACJ,CAAC;;AApfH,sCAqfC;AApfyB,4BAAc,GAAG,CAAC,CAAC;AACnB,4BAAc,GAAG,GAAG,CAAC;AACrB,qCAAuB,GAAG,EAAE,CAAC;AAC7B,iCAAmB,GAAG,EAAE,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\vulnerability\\vulnerability.entity.ts"], "sourcesContent": ["import { BaseAggregateRoot, UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { CVSSScore } from '../../value-objects/threat-indicators/cvss-score.value-object';\r\nimport { ThreatSeverity } from '../../enums/threat-severity.enum';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\nimport { VulnerabilityDiscoveredEvent } from '../../events/vulnerability-discovered.event';\r\nimport { VulnerabilityStatusChangedEvent } from '../../events/vulnerability-status-changed.event';\r\n\r\n/**\r\n * Vulnerability Properties\r\n */\r\nexport interface VulnerabilityProps {\r\n  /** CVE identifier */\r\n  cveId?: string;\r\n  /** Vulnerability title */\r\n  title: string;\r\n  /** Vulnerability description */\r\n  description: string;\r\n  /** Vulnerability severity */\r\n  severity: ThreatSeverity;\r\n  /** CVSS scores */\r\n  cvssScores: CVSSScore[];\r\n  /** Vulnerability category */\r\n  category: string;\r\n  /** Vulnerability type */\r\n  type: string;\r\n  /** Discovery confidence */\r\n  confidence: ConfidenceLevel;\r\n  /** Affected assets */\r\n  affectedAssets: VulnerabilityAsset[];\r\n  /** Vulnerability status */\r\n  status: VulnerabilityStatus;\r\n  /** Discovery information */\r\n  discovery: VulnerabilityDiscovery;\r\n  /** Exploitation information */\r\n  exploitation?: VulnerabilityExploitation;\r\n  /** Remediation information */\r\n  remediation: VulnerabilityRemediation;\r\n  /** Risk assessment */\r\n  riskAssessment: VulnerabilityRiskAssessment;\r\n  /** Compliance impact */\r\n  complianceImpact: ComplianceImpact[];\r\n  /** Vulnerability tags */\r\n  tags: string[];\r\n  /** Custom attributes */\r\n  attributes: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Vulnerability Status\r\n */\r\nexport enum VulnerabilityStatus {\r\n  DISCOVERED = 'discovered',\r\n  CONFIRMED = 'confirmed',\r\n  TRIAGED = 'triaged',\r\n  IN_PROGRESS = 'in_progress',\r\n  REMEDIATED = 'remediated',\r\n  VERIFIED = 'verified',\r\n  CLOSED = 'closed',\r\n  FALSE_POSITIVE = 'false_positive',\r\n  ACCEPTED_RISK = 'accepted_risk',\r\n}\r\n\r\n/**\r\n * Vulnerability Asset\r\n */\r\nexport interface VulnerabilityAsset {\r\n  /** Asset identifier */\r\n  assetId: string;\r\n  /** Asset name */\r\n  assetName: string;\r\n  /** Asset type */\r\n  assetType: string;\r\n  /** Asset criticality */\r\n  criticality: 'low' | 'medium' | 'high' | 'critical';\r\n  /** Affected components */\r\n  affectedComponents: Array<{\r\n    name: string;\r\n    version: string;\r\n    type: 'software' | 'hardware' | 'firmware' | 'configuration';\r\n  }>;\r\n  /** Asset exposure */\r\n  exposure: 'internal' | 'dmz' | 'external' | 'cloud';\r\n  /** Business impact */\r\n  businessImpact: string[];\r\n}\r\n\r\n/**\r\n * Vulnerability Discovery\r\n */\r\nexport interface VulnerabilityDiscovery {\r\n  /** Discovery method */\r\n  method: 'automated_scan' | 'manual_testing' | 'threat_intelligence' | 'incident_response' | 'external_report';\r\n  /** Discovery source */\r\n  source: string;\r\n  /** Discovery date */\r\n  discoveredAt: Date;\r\n  /** Discovered by */\r\n  discoveredBy: string;\r\n  /** Discovery details */\r\n  details: string;\r\n  /** Scanner information */\r\n  scannerInfo?: {\r\n    name: string;\r\n    version: string;\r\n    ruleId: string;\r\n    confidence: number;\r\n  };\r\n}\r\n\r\n/**\r\n * Vulnerability Exploitation\r\n */\r\nexport interface VulnerabilityExploitation {\r\n  /** Exploitation status */\r\n  status: 'not_exploited' | 'proof_of_concept' | 'active_exploitation' | 'weaponized';\r\n  /** Exploitation difficulty */\r\n  difficulty: 'low' | 'medium' | 'high';\r\n  /** Available exploits */\r\n  availableExploits: Array<{\r\n    name: string;\r\n    type: 'public' | 'private' | 'commercial';\r\n    source: string;\r\n    reliability: number;\r\n    discovered: Date;\r\n  }>;\r\n  /** Exploitation timeline */\r\n  timeline?: {\r\n    firstExploitPublished?: Date;\r\n    firstExploitationObserved?: Date;\r\n    lastExploitationObserved?: Date;\r\n  };\r\n  /** Attack vectors */\r\n  attackVectors: string[];\r\n  /** Prerequisites */\r\n  prerequisites: string[];\r\n}\r\n\r\n/**\r\n * Vulnerability Remediation\r\n */\r\nexport interface VulnerabilityRemediation {\r\n  /** Remediation status */\r\n  status: 'not_started' | 'planned' | 'in_progress' | 'completed' | 'verified' | 'failed';\r\n  /** Remediation priority */\r\n  priority: 'low' | 'medium' | 'high' | 'critical';\r\n  /** Assigned to */\r\n  assignedTo?: string;\r\n  /** Due date */\r\n  dueDate?: Date;\r\n  /** Remediation actions */\r\n  actions: RemediationAction[];\r\n  /** Workarounds */\r\n  workarounds: Array<{\r\n    description: string;\r\n    effectiveness: number;\r\n    implementedAt?: Date;\r\n    implementedBy?: string;\r\n  }>;\r\n  /** Remediation timeline */\r\n  timeline: {\r\n    plannedStart?: Date;\r\n    actualStart?: Date;\r\n    plannedCompletion?: Date;\r\n    actualCompletion?: Date;\r\n    verificationDate?: Date;\r\n  };\r\n  /** Remediation cost */\r\n  cost?: {\r\n    estimated: number;\r\n    actual?: number;\r\n    currency: string;\r\n  };\r\n}\r\n\r\n/**\r\n * Remediation Action\r\n */\r\nexport interface RemediationAction {\r\n  /** Action type */\r\n  type: 'patch' | 'configuration_change' | 'access_control' | 'monitoring' | 'replacement' | 'mitigation';\r\n  /** Action description */\r\n  description: string;\r\n  /** Action status */\r\n  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';\r\n  /** Performed by */\r\n  performedBy?: string;\r\n  /** Performed at */\r\n  performedAt?: Date;\r\n  /** Action result */\r\n  result?: string;\r\n  /** Verification required */\r\n  verificationRequired: boolean;\r\n  /** Verification status */\r\n  verificationStatus?: 'pending' | 'passed' | 'failed';\r\n}\r\n\r\n/**\r\n * Vulnerability Risk Assessment\r\n */\r\nexport interface VulnerabilityRiskAssessment {\r\n  /** Overall risk score (0-100) */\r\n  riskScore: number;\r\n  /** Exploitability score */\r\n  exploitabilityScore: number;\r\n  /** Impact score */\r\n  impactScore: number;\r\n  /** Environmental factors */\r\n  environmentalFactors: {\r\n    assetCriticality: number;\r\n    networkExposure: number;\r\n    dataClassification: number;\r\n    businessImpact: number;\r\n  };\r\n  /** Risk factors */\r\n  riskFactors: Array<{\r\n    factor: string;\r\n    weight: number;\r\n    score: number;\r\n    description: string;\r\n  }>;\r\n  /** Risk level */\r\n  riskLevel: 'low' | 'medium' | 'high' | 'critical';\r\n  /** Business risk */\r\n  businessRisk: {\r\n    financialImpact: number;\r\n    reputationalImpact: 'low' | 'medium' | 'high';\r\n    operationalImpact: 'low' | 'medium' | 'high';\r\n    complianceImpact: 'low' | 'medium' | 'high';\r\n  };\r\n}\r\n\r\n/**\r\n * Compliance Impact\r\n */\r\nexport interface ComplianceImpact {\r\n  /** Regulation/standard */\r\n  regulation: string;\r\n  /** Impact level */\r\n  impact: 'low' | 'medium' | 'high' | 'critical';\r\n  /** Specific requirements affected */\r\n  requirements: string[];\r\n  /** Compliance status */\r\n  status: 'compliant' | 'non_compliant' | 'at_risk';\r\n  /** Remediation deadline */\r\n  deadline?: Date;\r\n  /** Potential penalties */\r\n  potentialPenalties?: string[];\r\n}\r\n\r\n/**\r\n * Vulnerability Entity\r\n * \r\n * Represents a security vulnerability with comprehensive tracking and management.\r\n * Handles the complete vulnerability lifecycle from discovery to remediation.\r\n * \r\n * Key responsibilities:\r\n * - Vulnerability lifecycle management\r\n * - Risk assessment and prioritization\r\n * - Remediation tracking and verification\r\n * - Compliance impact assessment\r\n * - Asset impact analysis\r\n * \r\n * Business Rules:\r\n * - Critical vulnerabilities require immediate triage\r\n * - Remediation timelines based on severity and exposure\r\n * - Compliance deadlines override standard timelines\r\n * - Risk scores must be recalculated when context changes\r\n */\r\nexport class Vulnerability extends BaseAggregateRoot<VulnerabilityProps> {\r\n  private static readonly MIN_RISK_SCORE = 0;\r\n  private static readonly MAX_RISK_SCORE = 100;\r\n  private static readonly CRITICAL_RISK_THRESHOLD = 80;\r\n  private static readonly HIGH_RISK_THRESHOLD = 60;\r\n\r\n  constructor(props: VulnerabilityProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n  }\r\n\r\n  protected validate(): void {\r\n    if (!this.props.title || this.props.title.trim().length === 0) {\r\n      throw new Error('Vulnerability must have a title');\r\n    }\r\n\r\n    if (!this.props.description || this.props.description.trim().length === 0) {\r\n      throw new Error('Vulnerability must have a description');\r\n    }\r\n\r\n    if (!Object.values(ThreatSeverity).includes(this.props.severity)) {\r\n      throw new Error(`Invalid vulnerability severity: ${this.props.severity}`);\r\n    }\r\n\r\n    if (!Object.values(ConfidenceLevel).includes(this.props.confidence)) {\r\n      throw new Error(`Invalid confidence level: ${this.props.confidence}`);\r\n    }\r\n\r\n    if (!Object.values(VulnerabilityStatus).includes(this.props.status)) {\r\n      throw new Error(`Invalid vulnerability status: ${this.props.status}`);\r\n    }\r\n\r\n    if (!this.props.category || this.props.category.trim().length === 0) {\r\n      throw new Error('Vulnerability must have a category');\r\n    }\r\n\r\n    if (!this.props.type || this.props.type.trim().length === 0) {\r\n      throw new Error('Vulnerability must have a type');\r\n    }\r\n\r\n    if (this.props.affectedAssets.length === 0) {\r\n      throw new Error('Vulnerability must affect at least one asset');\r\n    }\r\n\r\n    if (this.props.riskAssessment.riskScore < Vulnerability.MIN_RISK_SCORE || \r\n        this.props.riskAssessment.riskScore > Vulnerability.MAX_RISK_SCORE) {\r\n      throw new Error(`Risk score must be between ${Vulnerability.MIN_RISK_SCORE} and ${Vulnerability.MAX_RISK_SCORE}`);\r\n    }\r\n\r\n    if (!this.props.discovery.discoveredAt) {\r\n      throw new Error('Vulnerability must have a discovery date');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a new vulnerability\r\n   */\r\n  static create(\r\n    title: string,\r\n    description: string,\r\n    severity: ThreatSeverity,\r\n    category: string,\r\n    type: string,\r\n    affectedAssets: VulnerabilityAsset[],\r\n    discovery: VulnerabilityDiscovery,\r\n    options?: {\r\n      cveId?: string;\r\n      cvssScores?: CVSSScore[];\r\n      confidence?: ConfidenceLevel;\r\n      exploitation?: VulnerabilityExploitation;\r\n      tags?: string[];\r\n      attributes?: Record<string, any>;\r\n    }\r\n  ): Vulnerability {\r\n    const riskAssessment = Vulnerability.calculateInitialRiskAssessment(\r\n      severity,\r\n      affectedAssets,\r\n      options?.cvssScores || []\r\n    );\r\n\r\n    const props: VulnerabilityProps = {\r\n      cveId: options?.cveId,\r\n      title: title.trim(),\r\n      description: description.trim(),\r\n      severity,\r\n      cvssScores: options?.cvssScores || [],\r\n      category: category.trim(),\r\n      type: type.trim(),\r\n      confidence: options?.confidence || ConfidenceLevel.MEDIUM,\r\n      affectedAssets,\r\n      status: VulnerabilityStatus.DISCOVERED,\r\n      discovery,\r\n      exploitation: options?.exploitation,\r\n      remediation: {\r\n        status: 'not_started',\r\n        priority: Vulnerability.calculateRemediationPriority(severity, riskAssessment.riskScore),\r\n        actions: [],\r\n        workarounds: [],\r\n        timeline: {},\r\n      },\r\n      riskAssessment,\r\n      complianceImpact: [],\r\n      tags: options?.tags || [],\r\n      attributes: options?.attributes || {},\r\n    };\r\n\r\n    const vulnerability = new Vulnerability(props);\r\n\r\n    // Publish domain event\r\n    vulnerability.addDomainEvent(new VulnerabilityDiscoveredEvent(\r\n      vulnerability.id,\r\n      {\r\n        vulnerabilityId: vulnerability.id.toString(),\r\n        cveId: vulnerability.props.cveId,\r\n        title: vulnerability.props.title,\r\n        severity: vulnerability.props.severity,\r\n        category: vulnerability.props.category,\r\n        type: vulnerability.props.type,\r\n        confidence: vulnerability.props.confidence,\r\n        riskScore: vulnerability.props.riskAssessment.riskScore,\r\n        affectedAssetCount: vulnerability.props.affectedAssets.length,\r\n        discoveryMethod: vulnerability.props.discovery.method,\r\n        timestamp: new Date().toISOString(),\r\n      }\r\n    ));\r\n\r\n    return vulnerability;\r\n  }\r\n\r\n  private static calculateInitialRiskAssessment(\r\n    severity: ThreatSeverity,\r\n    affectedAssets: VulnerabilityAsset[],\r\n    cvssScores: CVSSScore[]\r\n  ): VulnerabilityRiskAssessment {\r\n    // Base risk from severity\r\n    let riskScore = 0;\r\n    switch (severity) {\r\n      case ThreatSeverity.CRITICAL: riskScore = 90; break;\r\n      case ThreatSeverity.HIGH: riskScore = 70; break;\r\n      case ThreatSeverity.MEDIUM: riskScore = 50; break;\r\n      case ThreatSeverity.LOW: riskScore = 30; break;\r\n      case ThreatSeverity.UNKNOWN: riskScore = 20; break;\r\n    }\r\n\r\n    // Adjust for CVSS scores\r\n    if (cvssScores.length > 0) {\r\n      const maxCVSS = Math.max(...cvssScores.map(score => score.baseScore));\r\n      riskScore = Math.max(riskScore, maxCVSS * 10);\r\n    }\r\n\r\n    // Adjust for asset criticality\r\n    const criticalAssets = affectedAssets.filter(asset => asset.criticality === 'critical');\r\n    const highCriticalityAssets = affectedAssets.filter(asset => asset.criticality === 'high');\r\n    \r\n    if (criticalAssets.length > 0) {\r\n      riskScore += 15;\r\n    } else if (highCriticalityAssets.length > 0) {\r\n      riskScore += 10;\r\n    }\r\n\r\n    // Adjust for exposure\r\n    const externalAssets = affectedAssets.filter(asset => asset.exposure === 'external');\r\n    if (externalAssets.length > 0) {\r\n      riskScore += 10;\r\n    }\r\n\r\n    const finalRiskScore = Math.min(100, riskScore);\r\n\r\n    return {\r\n      riskScore: finalRiskScore,\r\n      exploitabilityScore: 50, // Default, to be updated with exploitation data\r\n      impactScore: finalRiskScore,\r\n      environmentalFactors: {\r\n        assetCriticality: criticalAssets.length > 0 ? 100 : 50,\r\n        networkExposure: externalAssets.length > 0 ? 100 : 30,\r\n        dataClassification: 50, // Default\r\n        businessImpact: 50, // Default\r\n      },\r\n      riskFactors: [],\r\n      riskLevel: Vulnerability.getRiskLevel(finalRiskScore),\r\n      businessRisk: {\r\n        financialImpact: 0,\r\n        reputationalImpact: severity === ThreatSeverity.CRITICAL ? 'high' : 'medium',\r\n        operationalImpact: criticalAssets.length > 0 ? 'high' : 'medium',\r\n        complianceImpact: 'medium',\r\n      },\r\n    };\r\n  }\r\n\r\n  private static getRiskLevel(riskScore: number): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (riskScore >= Vulnerability.CRITICAL_RISK_THRESHOLD) return 'critical';\r\n    if (riskScore >= Vulnerability.HIGH_RISK_THRESHOLD) return 'high';\r\n    if (riskScore >= 30) return 'medium';\r\n    return 'low';\r\n  }\r\n\r\n  private static calculateRemediationPriority(\r\n    severity: ThreatSeverity,\r\n    riskScore: number\r\n  ): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (severity === ThreatSeverity.CRITICAL || riskScore >= Vulnerability.CRITICAL_RISK_THRESHOLD) {\r\n      return 'critical';\r\n    }\r\n    if (severity === ThreatSeverity.HIGH || riskScore >= Vulnerability.HIGH_RISK_THRESHOLD) {\r\n      return 'high';\r\n    }\r\n    if (severity === ThreatSeverity.MEDIUM) {\r\n      return 'medium';\r\n    }\r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability ID\r\n   */\r\n  get cveId(): string | undefined {\r\n    return this.props.cveId;\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability title\r\n   */\r\n  get title(): string {\r\n    return this.props.title;\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability description\r\n   */\r\n  get description(): string {\r\n    return this.props.description;\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability severity\r\n   */\r\n  get severity(): ThreatSeverity {\r\n    return this.props.severity;\r\n  }\r\n\r\n  /**\r\n   * Get CVSS scores\r\n   */\r\n  get cvssScores(): CVSSScore[] {\r\n    return [...this.props.cvssScores];\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability category\r\n   */\r\n  get category(): string {\r\n    return this.props.category;\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability type\r\n   */\r\n  get type(): string {\r\n    return this.props.type;\r\n  }\r\n\r\n  /**\r\n   * Get confidence level\r\n   */\r\n  get confidence(): ConfidenceLevel {\r\n    return this.props.confidence;\r\n  }\r\n\r\n  /**\r\n   * Get affected assets\r\n   */\r\n  get affectedAssets(): VulnerabilityAsset[] {\r\n    return [...this.props.affectedAssets];\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability status\r\n   */\r\n  get status(): VulnerabilityStatus {\r\n    return this.props.status;\r\n  }\r\n\r\n  /**\r\n   * Get discovery information\r\n   */\r\n  get discovery(): VulnerabilityDiscovery {\r\n    return this.props.discovery;\r\n  }\r\n\r\n  /**\r\n   * Get exploitation information\r\n   */\r\n  get exploitation(): VulnerabilityExploitation | undefined {\r\n    return this.props.exploitation;\r\n  }\r\n\r\n  /**\r\n   * Get remediation information\r\n   */\r\n  get remediation(): VulnerabilityRemediation {\r\n    return this.props.remediation;\r\n  }\r\n\r\n  /**\r\n   * Get risk assessment\r\n   */\r\n  get riskAssessment(): VulnerabilityRiskAssessment {\r\n    return this.props.riskAssessment;\r\n  }\r\n\r\n  /**\r\n   * Get compliance impact\r\n   */\r\n  get complianceImpact(): ComplianceImpact[] {\r\n    return [...this.props.complianceImpact];\r\n  }\r\n\r\n  /**\r\n   * Get tags\r\n   */\r\n  get tags(): string[] {\r\n    return [...this.props.tags];\r\n  }\r\n\r\n  /**\r\n   * Get attributes\r\n   */\r\n  get attributes(): Record<string, any> {\r\n    return { ...this.props.attributes };\r\n  }\r\n\r\n  /**\r\n   * Change vulnerability status\r\n   */\r\n  changeStatus(newStatus: VulnerabilityStatus, reason: string): void {\r\n    if (newStatus === this.props.status) {\r\n      return; // No change needed\r\n    }\r\n\r\n    const oldStatus = this.props.status;\r\n    this.props.status = newStatus;\r\n\r\n    // Update remediation timeline based on status\r\n    this.updateRemediationTimeline(newStatus);\r\n\r\n    // Publish domain event\r\n    this.addDomainEvent(new VulnerabilityStatusChangedEvent(\r\n      this.id,\r\n      {\r\n        vulnerabilityId: this.id.toString(),\r\n        oldStatus,\r\n        newStatus,\r\n        reason,\r\n        timestamp: new Date().toISOString(),\r\n      }\r\n    ));\r\n  }\r\n\r\n  private updateRemediationTimeline(status: VulnerabilityStatus): void {\r\n    const now = new Date();\r\n    \r\n    switch (status) {\r\n      case VulnerabilityStatus.TRIAGED:\r\n        if (!this.props.remediation.timeline.plannedStart) {\r\n          this.props.remediation.timeline.plannedStart = now;\r\n        }\r\n        break;\r\n      case VulnerabilityStatus.IN_PROGRESS:\r\n        if (!this.props.remediation.timeline.actualStart) {\r\n          this.props.remediation.timeline.actualStart = now;\r\n        }\r\n        this.props.remediation.status = 'in_progress';\r\n        break;\r\n      case VulnerabilityStatus.REMEDIATED:\r\n        if (!this.props.remediation.timeline.actualCompletion) {\r\n          this.props.remediation.timeline.actualCompletion = now;\r\n        }\r\n        this.props.remediation.status = 'completed';\r\n        break;\r\n      case VulnerabilityStatus.VERIFIED:\r\n        if (!this.props.remediation.timeline.verificationDate) {\r\n          this.props.remediation.timeline.verificationDate = now;\r\n        }\r\n        this.props.remediation.status = 'verified';\r\n        break;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is critical\r\n   */\r\n  isCritical(): boolean {\r\n    return this.props.severity === ThreatSeverity.CRITICAL ||\r\n           this.props.riskAssessment.riskScore >= Vulnerability.CRITICAL_RISK_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is high risk\r\n   */\r\n  isHighRisk(): boolean {\r\n    return this.props.riskAssessment.riskLevel === 'high' ||\r\n           this.props.riskAssessment.riskLevel === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is actively exploited\r\n   */\r\n  isActivelyExploited(): boolean {\r\n    return this.props.exploitation?.status === 'active_exploitation';\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability affects critical assets\r\n   */\r\n  affectsCriticalAssets(): boolean {\r\n    return this.props.affectedAssets.some(asset => asset.criticality === 'critical');\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is externally exposed\r\n   */\r\n  isExternallyExposed(): boolean {\r\n    return this.props.affectedAssets.some(asset => asset.exposure === 'external');\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability requires immediate attention\r\n   */\r\n  requiresImmediateAttention(): boolean {\r\n    return this.isCritical() || \r\n           this.isActivelyExploited() || \r\n           (this.isHighRisk() && this.affectsCriticalAssets());\r\n  }\r\n\r\n  /**\r\n   * Get remediation SLA (in days)\r\n   */\r\n  getRemediationSLA(): number {\r\n    if (this.isCritical()) return 1;\r\n    if (this.isHighRisk()) return 7;\r\n    if (this.props.riskAssessment.riskLevel === 'medium') return 30;\r\n    return 90;\r\n  }\r\n\r\n  /**\r\n   * Check if remediation is overdue\r\n   */\r\n  isRemediationOverdue(): boolean {\r\n    const slaDate = new Date(this.props.discovery.discoveredAt);\r\n    slaDate.setDate(slaDate.getDate() + this.getRemediationSLA());\r\n    return new Date() > slaDate && !this.isRemediated();\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is remediated\r\n   */\r\n  isRemediated(): boolean {\r\n    return [\r\n      VulnerabilityStatus.REMEDIATED,\r\n      VulnerabilityStatus.VERIFIED,\r\n      VulnerabilityStatus.CLOSED,\r\n    ].includes(this.props.status);\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      id: this.id.toString(),\r\n      cveId: this.props.cveId,\r\n      title: this.props.title,\r\n      description: this.props.description,\r\n      severity: this.props.severity,\r\n      cvssScores: this.props.cvssScores.map(score => score.toJSON()),\r\n      category: this.props.category,\r\n      type: this.props.type,\r\n      confidence: this.props.confidence,\r\n      affectedAssets: this.props.affectedAssets,\r\n      status: this.props.status,\r\n      discovery: this.props.discovery,\r\n      exploitation: this.props.exploitation,\r\n      remediation: this.props.remediation,\r\n      riskAssessment: this.props.riskAssessment,\r\n      complianceImpact: this.props.complianceImpact,\r\n      tags: this.props.tags,\r\n      attributes: this.props.attributes,\r\n      analysis: {\r\n        isCritical: this.isCritical(),\r\n        isHighRisk: this.isHighRisk(),\r\n        isActivelyExploited: this.isActivelyExploited(),\r\n        affectsCriticalAssets: this.affectsCriticalAssets(),\r\n        isExternallyExposed: this.isExternallyExposed(),\r\n        requiresImmediateAttention: this.requiresImmediateAttention(),\r\n        remediationSLA: this.getRemediationSLA(),\r\n        isRemediationOverdue: this.isRemediationOverdue(),\r\n        isRemediated: this.isRemediated(),\r\n      },\r\n      createdAt: this.createdAt?.toISOString(),\r\n      updatedAt: this.updatedAt?.toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}