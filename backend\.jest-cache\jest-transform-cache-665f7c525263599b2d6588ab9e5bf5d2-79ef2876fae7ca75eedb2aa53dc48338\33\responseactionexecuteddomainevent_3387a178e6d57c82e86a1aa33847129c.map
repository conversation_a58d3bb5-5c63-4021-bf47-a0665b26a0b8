{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\response-action-executed.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAC5E,gEAAuD;AAoBvD;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAa,iCAAkC,SAAQ,+BAAgD;IACrG,YACE,WAA2B,EAC3B,SAA0C,EAC1C,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,KAAK,IAAI,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,KAAK,KAAK,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,KAAK,SAAS;YAC/C,IAAI,CAAC,SAAS,CAAC,gBAAgB,KAAK,SAAS,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,yCAAyC;QACzC,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC5C,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC;YAChD,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,eAAe,GAAG;YACtB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,WAAW;YACtB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,QAAQ;YACnB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,eAAe;SAC3B,CAAC;QAEF,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,kBAAkB,GAAG;YACzB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,QAAQ;YACnB,6BAAU,CAAC,YAAY;YACvB,6BAAU,CAAC,SAAS;YACpB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,YAAY;YACvB,6BAAU,CAAC,oBAAoB;YAC/B,6BAAU,CAAC,eAAe;SAC3B,CAAC;QAEF,OAAO,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,eAAe,GAAG;YACtB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,sBAAsB;SAClC,CAAC;QAEF,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,qBAAqB,IAAI,CAAC,CAAC;QAE3D,IAAI,QAAQ,GAAG,CAAC;YAAE,OAAO,MAAM,CAAC;QAChC,IAAI,QAAQ,GAAG,EAAE;YAAE,OAAO,QAAQ,CAAC;QACnC,IAAI,QAAQ,GAAG,GAAG;YAAE,OAAO,MAAM,CAAC;QAClC,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC;QACnD,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;QACjD,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAE/C,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;gBAC/B,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;gBAC/C,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;gBAC5B,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;gBAC5C,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAE7C,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;gBAC9B,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACnD,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC3B,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAEjC,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;gBAC9B,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAE9B,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;gBAC9B,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBACvC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC3B,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,mBAAmB;QASjB,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;YACrC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,qBAAqB,IAAI,CAAC;YACxD,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YAClD,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;YACjC,WAAW,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACxC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;SAC9C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iBAAiB;QAKf,MAAM,qBAAqB,GAAG,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE3E,IAAI,UAAU,GAA2C,OAAO,CAAC;QACjE,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAAE,UAAU,GAAG,eAAe,CAAC;aACvD,IAAI,IAAI,CAAC,mBAAmB,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAAE,UAAU,GAAG,UAAU,CAAC;QAExF,IAAI,eAAe,GAA0C,UAAU,CAAC;QACxE,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAAE,eAAe,GAAG,WAAW,CAAC;aACxD,IAAI,IAAI,CAAC,QAAQ,EAAE;YAAE,eAAe,GAAG,UAAU,CAAC;QAEvD,OAAO;YACL,qBAAqB;YACrB,UAAU;YACV,eAAe;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;gBAC/B,YAAY,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;gBACjD,YAAY,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAC7C,CAAC;YAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;gBAC5B,YAAY,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;gBAC/C,YAAY,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YACrD,CAAC;YAED,IAAI,IAAI,CAAC,UAAU,KAAK,6BAAU,CAAC,mBAAmB,EAAE,CAAC;gBACvD,YAAY,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBAC9C,YAAY,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;gBAC/B,YAAY,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;gBAC7D,YAAY,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;gBAC5B,YAAY,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;gBACrE,YAAY,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;gBAC9B,YAAY,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;gBACrD,YAAY,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,kBAAkB;QAehB,OAAO;YACL,SAAS,EAAE,wBAAwB;YACnC,MAAM,EAAE,0BAA0B;YAClC,QAAQ,EAAE,gBAAgB;YAC1B,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YACvC,IAAI,EAAE,IAAI,CAAC,SAAS;YACpB,QAAQ,EAAE;gBACR,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;gBACjC,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;gBAClD,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;gBAClC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC7C,WAAW,EAAE,IAAI,CAAC,oBAAoB,EAAE;gBACxC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;aAC7C;SACF,CAAC;IACJ,CAAC;CACF;AAjYD,8EAiYC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\response-action-executed.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { ActionType } from '../enums/action-type.enum';\r\n\r\n/**\r\n * Response Action Executed Domain Event Data\r\n */\r\nexport interface ResponseActionExecutedEventData {\r\n  /** Type of action that was executed */\r\n  actionType: ActionType;\r\n  /** Who executed the action */\r\n  executedBy: string;\r\n  /** When the action was executed */\r\n  executedAt: Date;\r\n  /** Execution results */\r\n  executionResults?: Record<string, any>;\r\n  /** Whether success criteria were met */\r\n  successCriteriaMet?: boolean;\r\n  /** Actual execution duration in minutes */\r\n  actualDurationMinutes?: number;\r\n}\r\n\r\n/**\r\n * Response Action Executed Domain Event\r\n * \r\n * Raised when a response action has been successfully executed.\r\n * This event indicates that the action has completed its execution phase,\r\n * regardless of whether it was successful or not.\r\n * \r\n * Key information:\r\n * - Action type and execution details\r\n * - Who executed the action\r\n * - Execution timestamp and duration\r\n * - Execution results and success status\r\n * - Performance metrics\r\n * \r\n * Use cases:\r\n * - Track action execution completion\r\n * - Update monitoring dashboards\r\n * - Generate execution reports\r\n * - Trigger post-execution workflows\r\n * - Update related entities\r\n * - Log execution for audit trails\r\n */\r\nexport class ResponseActionExecutedDomainEvent extends BaseDomainEvent<ResponseActionExecutedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: ResponseActionExecutedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the action type\r\n   */\r\n  get actionType(): ActionType {\r\n    return this.eventData.actionType;\r\n  }\r\n\r\n  /**\r\n   * Get who executed the action\r\n   */\r\n  get executedBy(): string {\r\n    return this.eventData.executedBy;\r\n  }\r\n\r\n  /**\r\n   * Get when the action was executed\r\n   */\r\n  get executedAt(): Date {\r\n    return this.eventData.executedAt;\r\n  }\r\n\r\n  /**\r\n   * Get the execution results\r\n   */\r\n  get executionResults(): Record<string, any> | undefined {\r\n    return this.eventData.executionResults;\r\n  }\r\n\r\n  /**\r\n   * Check if success criteria were met\r\n   */\r\n  get successCriteriaMet(): boolean | undefined {\r\n    return this.eventData.successCriteriaMet;\r\n  }\r\n\r\n  /**\r\n   * Get actual execution duration in minutes\r\n   */\r\n  get actualDurationMinutes(): number | undefined {\r\n    return this.eventData.actualDurationMinutes;\r\n  }\r\n\r\n  /**\r\n   * Check if the execution was successful\r\n   */\r\n  isSuccessful(): boolean {\r\n    return this.eventData.successCriteriaMet === true;\r\n  }\r\n\r\n  /**\r\n   * Check if the execution failed\r\n   */\r\n  isFailed(): boolean {\r\n    return this.eventData.successCriteriaMet === false;\r\n  }\r\n\r\n  /**\r\n   * Check if the execution was partial\r\n   */\r\n  isPartial(): boolean {\r\n    return this.eventData.successCriteriaMet === undefined &&\r\n           this.eventData.executionResults !== undefined;\r\n  }\r\n\r\n  /**\r\n   * Check if this was an automated execution\r\n   */\r\n  isAutomatedExecution(): boolean {\r\n    // Check if executed by system/automation\r\n    return this.eventData.executedBy.includes('system') ||\r\n           this.eventData.executedBy.includes('automation') ||\r\n           this.eventData.executedBy.includes('bot');\r\n  }\r\n\r\n  /**\r\n   * Check if this was a manual execution\r\n   */\r\n  isManualExecution(): boolean {\r\n    return !this.isAutomatedExecution();\r\n  }\r\n\r\n  /**\r\n   * Check if this is a security-critical action\r\n   */\r\n  isSecurityCritical(): boolean {\r\n    const criticalActions = [\r\n      ActionType.ISOLATE_SYSTEM,\r\n      ActionType.SHUTDOWN_SYSTEM,\r\n      ActionType.DELETE_FILE,\r\n      ActionType.REMOVE_MALWARE,\r\n      ActionType.DISABLE_ACCOUNT,\r\n      ActionType.BLOCK_IP,\r\n      ActionType.QUARANTINE_FILE,\r\n      ActionType.UPDATE_FIREWALL,\r\n    ];\r\n    \r\n    return criticalActions.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a containment action\r\n   */\r\n  isContainmentAction(): boolean {\r\n    const containmentActions = [\r\n      ActionType.ISOLATE_SYSTEM,\r\n      ActionType.QUARANTINE_FILE,\r\n      ActionType.BLOCK_IP,\r\n      ActionType.BLOCK_DOMAIN,\r\n      ActionType.BLOCK_URL,\r\n      ActionType.DISABLE_ACCOUNT,\r\n      ActionType.REVOKE_TOKEN,\r\n      ActionType.TERMINATE_CONNECTION,\r\n      ActionType.SHUTDOWN_SYSTEM,\r\n    ];\r\n    \r\n    return containmentActions.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a recovery action\r\n   */\r\n  isRecoveryAction(): boolean {\r\n    const recoveryActions = [\r\n      ActionType.RESTORE_BACKUP,\r\n      ActionType.REBUILD_SYSTEM,\r\n      ActionType.RESTORE_NETWORK,\r\n      ActionType.ENABLE_SERVICE,\r\n      ActionType.RESET_PASSWORD,\r\n      ActionType.REGENERATE_CERTIFICATE,\r\n    ];\r\n    \r\n    return recoveryActions.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Check if execution was fast (under 5 minutes)\r\n   */\r\n  isFastExecution(): boolean {\r\n    return (this.eventData.actualDurationMinutes || 0) < 5;\r\n  }\r\n\r\n  /**\r\n   * Check if execution was slow (over 30 minutes)\r\n   */\r\n  isSlowExecution(): boolean {\r\n    return (this.eventData.actualDurationMinutes || 0) > 30;\r\n  }\r\n\r\n  /**\r\n   * Get execution performance category\r\n   */\r\n  getPerformanceCategory(): 'fast' | 'normal' | 'slow' | 'very_slow' {\r\n    const duration = this.eventData.actualDurationMinutes || 0;\r\n    \r\n    if (duration < 5) return 'fast';\r\n    if (duration < 30) return 'normal';\r\n    if (duration < 120) return 'slow';\r\n    return 'very_slow';\r\n  }\r\n\r\n  /**\r\n   * Get execution impact level\r\n   */\r\n  getImpactLevel(): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (this.isSecurityCritical()) {\r\n      return this.isSuccessful() ? 'high' : 'critical';\r\n    }\r\n    \r\n    if (this.isContainmentAction()) {\r\n      return this.isSuccessful() ? 'medium' : 'high';\r\n    }\r\n    \r\n    if (this.isRecoveryAction()) {\r\n      return this.isSuccessful() ? 'medium' : 'high';\r\n    }\r\n    \r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get recommended post-execution actions\r\n   */\r\n  getRecommendedPostActions(): string[] {\r\n    const actions: string[] = [];\r\n\r\n    if (this.isSuccessful()) {\r\n      actions.push('Validate action effectiveness');\r\n      actions.push('Update related security status');\r\n      \r\n      if (this.isContainmentAction()) {\r\n        actions.push('Monitor for containment bypass');\r\n        actions.push('Assess containment completeness');\r\n      }\r\n      \r\n      if (this.isRecoveryAction()) {\r\n        actions.push('Verify system functionality');\r\n        actions.push('Test recovered services');\r\n      }\r\n    } else {\r\n      actions.push('Analyze execution failure');\r\n      actions.push('Assess security impact of failure');\r\n      actions.push('Consider alternative actions');\r\n      \r\n      if (this.isSecurityCritical()) {\r\n        actions.push('Escalate to incident response team');\r\n        actions.push('Implement manual fallback procedures');\r\n      }\r\n    }\r\n\r\n    if (this.isSlowExecution()) {\r\n      actions.push('Investigate performance issues');\r\n      actions.push('Optimize action execution');\r\n    }\r\n\r\n    return actions;\r\n  }\r\n\r\n  /**\r\n   * Get stakeholders to notify\r\n   */\r\n  getNotificationTargets(): string[] {\r\n    const targets: string[] = [];\r\n\r\n    if (this.isSuccessful()) {\r\n      targets.push('action-requestor');\r\n      \r\n      if (this.isSecurityCritical()) {\r\n        targets.push('security-team');\r\n      }\r\n    } else {\r\n      targets.push('action-requestor');\r\n      targets.push('security-team');\r\n      \r\n      if (this.isSecurityCritical()) {\r\n        targets.push('incident-response-team');\r\n        targets.push('security-managers');\r\n      }\r\n    }\r\n\r\n    if (this.isSlowExecution()) {\r\n      targets.push('performance-team');\r\n    }\r\n\r\n    return targets;\r\n  }\r\n\r\n  /**\r\n   * Get execution metrics\r\n   */\r\n  getExecutionMetrics(): {\r\n    actionType: ActionType;\r\n    executionTime: number;\r\n    performanceCategory: string;\r\n    impactLevel: string;\r\n    isSuccessful: boolean;\r\n    isAutomated: boolean;\r\n    isSecurityCritical: boolean;\r\n  } {\r\n    return {\r\n      actionType: this.eventData.actionType,\r\n      executionTime: this.eventData.actualDurationMinutes || 0,\r\n      performanceCategory: this.getPerformanceCategory(),\r\n      impactLevel: this.getImpactLevel(),\r\n      isSuccessful: this.isSuccessful(),\r\n      isAutomated: this.isAutomatedExecution(),\r\n      isSecurityCritical: this.isSecurityCritical(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get compliance information\r\n   */\r\n  getComplianceInfo(): {\r\n    requiresDocumentation: boolean;\r\n    auditLevel: 'basic' | 'detailed' | 'comprehensive';\r\n    retentionPeriod: 'standard' | 'extended' | 'permanent';\r\n  } {\r\n    const requiresDocumentation = this.isSecurityCritical() || this.isFailed();\r\n    \r\n    let auditLevel: 'basic' | 'detailed' | 'comprehensive' = 'basic';\r\n    if (this.isSecurityCritical()) auditLevel = 'comprehensive';\r\n    else if (this.isContainmentAction() || this.isRecoveryAction()) auditLevel = 'detailed';\r\n    \r\n    let retentionPeriod: 'standard' | 'extended' | 'permanent' = 'standard';\r\n    if (this.isSecurityCritical()) retentionPeriod = 'permanent';\r\n    else if (this.isFailed()) retentionPeriod = 'extended';\r\n    \r\n    return {\r\n      requiresDocumentation,\r\n      auditLevel,\r\n      retentionPeriod,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get security implications\r\n   */\r\n  getSecurityImplications(): string[] {\r\n    const implications: string[] = [];\r\n\r\n    if (this.isSuccessful()) {\r\n      if (this.isContainmentAction()) {\r\n        implications.push('Threat containment achieved');\r\n        implications.push('Risk exposure reduced');\r\n      }\r\n      \r\n      if (this.isRecoveryAction()) {\r\n        implications.push('System recovery completed');\r\n        implications.push('Service availability restored');\r\n      }\r\n      \r\n      if (this.actionType === ActionType.PATCH_VULNERABILITY) {\r\n        implications.push('Vulnerability remediated');\r\n        implications.push('Attack surface reduced');\r\n      }\r\n    } else {\r\n      if (this.isContainmentAction()) {\r\n        implications.push('Containment failure - threat may spread');\r\n        implications.push('Increased risk exposure');\r\n      }\r\n      \r\n      if (this.isRecoveryAction()) {\r\n        implications.push('Recovery failure - service disruption continues');\r\n        implications.push('Business impact ongoing');\r\n      }\r\n      \r\n      if (this.isSecurityCritical()) {\r\n        implications.push('Critical security action failed');\r\n        implications.push('Manual intervention required');\r\n      }\r\n    }\r\n\r\n    return implications;\r\n  }\r\n\r\n  /**\r\n   * Convert to integration event format\r\n   */\r\n  toIntegrationEvent(): {\r\n    eventType: string;\r\n    action: string;\r\n    resource: string;\r\n    resourceId: string;\r\n    data: ResponseActionExecutedEventData;\r\n    metadata: {\r\n      isSuccessful: boolean;\r\n      performanceCategory: string;\r\n      impactLevel: string;\r\n      isSecurityCritical: boolean;\r\n      isAutomated: boolean;\r\n      executionMetrics: Record<string, any>;\r\n    };\r\n  } {\r\n    return {\r\n      eventType: 'ResponseActionExecuted',\r\n      action: 'response_action_executed',\r\n      resource: 'ResponseAction',\r\n      resourceId: this.aggregateId.toString(),\r\n      data: this.eventData,\r\n      metadata: {\r\n        isSuccessful: this.isSuccessful(),\r\n        performanceCategory: this.getPerformanceCategory(),\r\n        impactLevel: this.getImpactLevel(),\r\n        isSecurityCritical: this.isSecurityCritical(),\r\n        isAutomated: this.isAutomatedExecution(),\r\n        executionMetrics: this.getExecutionMetrics(),\r\n      },\r\n    };\r\n  }\r\n}"], "version": 3}