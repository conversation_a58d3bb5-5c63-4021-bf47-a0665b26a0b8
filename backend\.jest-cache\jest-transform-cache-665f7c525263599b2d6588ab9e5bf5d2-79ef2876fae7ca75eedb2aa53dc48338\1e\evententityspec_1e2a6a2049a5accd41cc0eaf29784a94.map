{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\event.entity.spec.ts", "mappings": ";;AAAA,kDAAoD;AACpD,iEAA6D;AAC7D,gHAA+F;AAC/F,kHAAiG;AACjG,4GAA2F;AAC3F,6DAA2D;AAC3D,iEAAwD;AACxD,yEAAgE;AAChE,qEAA4D;AAC5D,2FAAiF;AACjF,+EAAqE;AACrE,wFAAkF;AAClF,sGAA+F;AAC/F,4HAAoH;AAEpH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;IAC5B,IAAI,eAA2B,CAAC;IAChC,IAAI,aAA4B,CAAC;IAEjC,UAAU,CAAC,GAAG,EAAE;QACd,MAAM,SAAS,GAAG,6CAAc,CAAC,MAAM,EAAE,CAAC;QAC1C,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;QAC3E,aAAa,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAExD,eAAe,GAAG;YAChB,QAAQ,EAAE,aAAa;YACvB,IAAI,EAAE,2BAAS,CAAC,eAAe;YAC/B,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,MAAM,EAAE,+BAAW,CAAC,MAAM;YAC1B,gBAAgB,EAAE,oDAAqB,CAAC,GAAG;YAC3C,OAAO,EAAE,EAAE,OAAO,EAAE,sBAAsB,EAAE;YAC5C,KAAK,EAAE,qBAAqB;YAC5B,WAAW,EAAE,wCAAwC;YACrD,IAAI,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;YAC1B,SAAS,EAAE,EAAE;YACb,eAAe,EAAE,EAAE;YACnB,UAAU,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE;SAC3C,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,KAAK,GAAG,oBAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAE5C,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,oBAAK,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,8BAAc,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAW,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,GAAG,CAAC,CAAC;YAC/D,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACzE,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,KAAK,GAAG,oBAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAE5C,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;YACxC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,oDAAuB,CAAC,CAAC;YAEhE,MAAM,YAAY,GAAG,YAAY,CAAC,CAAC,CAA4B,CAAC;YAChE,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;YAC/D,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,QAAQ,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,KAAK,GAAG,oBAAK,CAAC,MAAM,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAEtD,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,YAAY,GAAG,EAAE,GAAG,eAAe,EAAE,CAAC;YAC5C,OAAQ,YAAoB,CAAC,QAAQ,CAAC;YAEtC,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,YAAY,GAAG,EAAE,GAAG,eAAe,EAAE,CAAC;YAC5C,OAAQ,YAAoB,CAAC,IAAI,CAAC;YAElC,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,YAAY,GAAG,EAAE,GAAG,eAAe,EAAE,CAAC;YAC5C,OAAQ,YAAoB,CAAC,QAAQ,CAAC;YAEtC,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,YAAY,GAAG,EAAE,GAAG,eAAe,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;YAEvD,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,YAAY,GAAG,EAAE,GAAG,eAAe,EAAE,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YAEpE,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,YAAY,GAAG,EAAE,GAAG,eAAe,EAAE,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAE3E,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,aAAa,GAAG,EAAE,GAAG,eAAe,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;YAC5D,MAAM,aAAa,GAAG,EAAE,GAAG,eAAe,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC;YAE7D,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;YAC1F,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,aAAa,GAAG,EAAE,GAAG,eAAe,EAAE,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC;YAClE,MAAM,aAAa,GAAG,EAAE,GAAG,eAAe,EAAE,eAAe,EAAE,GAAG,EAAE,CAAC;YAEnE,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;YAChG,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,YAAY,GAAG,EAAE,GAAG,eAAe,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAEzE,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,IAAI,KAAY,CAAC;QAEjB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,oBAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YACtC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,yCAAyC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,KAAK,CAAC,YAAY,CAAC,+BAAW,CAAC,aAAa,EAAE,UAAU,EAAE,wBAAwB,CAAC,CAAC;YAEpF,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAW,CAAC,aAAa,CAAC,CAAC;YAErD,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;YACxC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,iEAA6B,CAAC,CAAC;YAEtE,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAkC,CAAC;YACrE,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,+BAAW,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,+BAAW,CAAC,aAAa,CAAC,CAAC;YAC9D,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,KAAK,CAAC,YAAY,CAAC,+BAAW,CAAC,MAAM,CAAC,CAAC;YAEvC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAW,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;YACjC,KAAK,CAAC,YAAY,CAAC,+BAAW,CAAC,QAAQ,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;YACvE,MAAM,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAEhC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAW,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,UAAW,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;YACpF,MAAM,CAAC,KAAK,CAAC,UAAW,CAAC,OAAO,EAAE,CAAC,CAAC,mBAAmB,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,gBAAgB;YAChB,KAAK,CAAC,YAAY,CAAC,+BAAW,CAAC,QAAQ,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;YACvE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAEvC,cAAc;YACd,KAAK,CAAC,YAAY,CAAC,+BAAW,CAAC,MAAM,CAAC,CAAC;YAEvC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAW,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,aAAa,EAAE,CAAC;YACzC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,aAAa,EAAE,CAAC;YACzC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,aAAa,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,IAAI,KAAY,CAAC;QAEjB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,oBAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YACtC,KAAK,CAAC,WAAW,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAChC,KAAK,CAAC,sBAAsB,CAAC,oDAAqB,CAAC,UAAU,CAAC,CAAC;YAC/D,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YAE/B,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,UAAU,CAAC,CAAC;YACtE,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,eAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC;YACxF,MAAM,CAAC,KAAK,CAAC,eAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,mBAAmB,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;YAEpF,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;YACxC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,sFAAuC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,KAAK,CAAC,sBAAsB,CAAC,oDAAqB,CAAC,GAAG,CAAC,CAAC;YAExD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,GAAG,CAAC,CAAC;YAC/D,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,IAAI,KAAY,CAAC;QAEjB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,oBAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,cAAc,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;YAE9D,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YAE3C,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,UAAU,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAE1B,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;YACxF,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,KAAK,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;YAEhC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;YACpG,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;QACvG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iBAAiB,EAAE,GAAG,EAAE;YACzB,KAAK,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC;YAE1C,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,6BAA6B;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,wBAAwB;YAE5D,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACjE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAE7D,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YAE3B,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACzC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,0BAA0B;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,aAAa,GAAG,iBAAiB,CAAC;YAExC,KAAK,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAEtC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,QAAQ,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAE3C,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAE/B,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,eAAe,GAAG,KAAK,CAAC,kBAAkB,CAAC;YACjD,MAAM,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;YAEjC,KAAK,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAEhC,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,eAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;YACzF,MAAM,CAAC,KAAK,CAAC,eAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,mBAAmB,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,KAAK,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAErD,KAAK,CAAC,oBAAoB,EAAE,CAAC;YAE7B,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,aAAa,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,KAAK,CAAC,gBAAgB,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,CAAC,CAAC;YAE/E,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,KAAK,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAEvC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,aAAa,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAI,KAAY,CAAC;QAEjB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,oBAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpC,KAAK,CAAC,YAAY,CAAC,+BAAW,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAErC,KAAK,CAAC,YAAY,CAAC,+BAAW,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAErC,KAAK,CAAC,YAAY,CAAC,+BAAW,CAAC,cAAc,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEvC,KAAK,CAAC,YAAY,CAAC,+BAAW,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAE3D,MAAM,gBAAgB,GAAG,EAAE,GAAG,eAAe,EAAE,QAAQ,EAAE,mCAAa,CAAC,GAAG,EAAE,CAAC;YAC7E,MAAM,gBAAgB,GAAG,oBAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACxD,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtD,MAAM,qBAAqB,GAAG,EAAE,GAAG,eAAe,EAAE,QAAQ,EAAE,mCAAa,CAAC,QAAQ,EAAE,CAAC;YACvF,MAAM,qBAAqB,GAAG,oBAAK,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;YAClE,MAAM,CAAC,qBAAqB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,8BAA8B;YAEtE,MAAM,aAAa,GAAG,EAAE,GAAG,eAAe,EAAE,QAAQ,EAAE,mCAAa,CAAC,QAAQ,EAAE,CAAC;YAC/E,MAAM,aAAa,GAAG,oBAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB;YAE7D,MAAM,YAAY,GAAG,EAAE,GAAG,eAAe,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;YAC3D,MAAM,YAAY,GAAG,oBAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAChD,MAAM,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE9C,MAAM,WAAW,GAAG,EAAE,GAAG,eAAe,EAAE,CAAC;YAC3C,OAAO,WAAW,CAAC,SAAS,CAAC;YAC7B,MAAM,WAAW,GAAG,oBAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAC9C,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEhD,KAAK,CAAC,sBAAsB,CAAC,oDAAqB,CAAC,MAAM,CAAC,CAAC;YAC3D,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEnD,kCAAkC;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAClC,CAAC;YAED,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEhE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAE3B,MAAM,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC5D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,0BAA0B;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,0BAA0B;YACvE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,IAAI,KAAY,CAAC;QAEjB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,oBAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YAEnC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAW,CAAC,MAAM,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,GAAG,CAAC,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAE5B,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YAClE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,EAAE,aAAa,EAAE,WAAW,EAAE,CAAC,CAAC;YAChE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,mEAAmE,EAAE,GAAG,EAAE;YAC3E,MAAM,YAAY,GAAG;gBACnB,GAAG,eAAe;gBAClB,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,qBAAqB;aACtB,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;QACvG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,YAAY,GAAG;gBACnB,GAAG,eAAe;gBAClB,MAAM,EAAE,+BAAW,CAAC,MAAM;gBAC1B,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,kCAAkC;aAC3D,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,gEAAgE,CAAC,CAAC;QACrH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2EAA2E,EAAE,GAAG,EAAE;YACnF,MAAM,YAAY,GAAG;gBACnB,GAAG,eAAe;gBAClB,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,gBAAgB,EAAE,oDAAqB,CAAC,GAAG,EAAE,eAAe;gBAC5D,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,kEAAkE,CAAC,CAAC;QACvH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,IAAI,EAAE,2BAAS,CAAC,gBAAgB;gBAChC,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,KAAK,EAAE,eAAe;gBACtB,OAAO,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE;gBAClC,UAAU,EAAE,wCAAe,CAAC,SAAS;gBACrC,gBAAgB,EAAE,eAAe;aAClC,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,oBAAK,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,gBAAgB,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC1C,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,SAAS,CAAC,CAAC;YACnE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\event.entity.spec.ts"], "sourcesContent": ["import { Event, EventProps } from '../event.entity';\r\nimport { EventFactory } from '../../factories/event.factory';\r\nimport { EventMetadata } from '../../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../value-objects/event-metadata/event-source.value-object';\r\nimport { UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventStatus } from '../../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../../enums/event-source-type.enum';\r\nimport { EventCreatedDomainEvent } from '../../events/event-created.domain-event';\r\nimport { EventStatusChangedDomainEvent } from '../../events/event-status-changed.domain-event';\r\nimport { EventProcessingStatusChangedDomainEvent } from '../../events/event-processing-status-changed.domain-event';\r\n\r\ndescribe('Event Entity', () => {\r\n  let validEventProps: EventProps;\r\n  let eventMetadata: EventMetadata;\r\n\r\n  beforeEach(() => {\r\n    const timestamp = EventTimestamp.create();\r\n    const source = EventSource.create(EventSourceType.FIREWALL, 'firewall-01');\r\n    eventMetadata = EventMetadata.create(timestamp, source);\r\n\r\n    validEventProps = {\r\n      metadata: eventMetadata,\r\n      type: EventType.THREAT_DETECTED,\r\n      severity: EventSeverity.HIGH,\r\n      status: EventStatus.ACTIVE,\r\n      processingStatus: EventProcessingStatus.RAW,\r\n      rawData: { message: 'Test threat detected' },\r\n      title: 'Test Security Event',\r\n      description: 'A test security event for unit testing',\r\n      tags: ['test', 'security'],\r\n      riskScore: 75,\r\n      confidenceLevel: 85,\r\n      attributes: { testAttribute: 'testValue' },\r\n    };\r\n  });\r\n\r\n  describe('Creation', () => {\r\n    it('should create a valid event with required properties', () => {\r\n      const event = Event.create(validEventProps);\r\n\r\n      expect(event).toBeInstanceOf(Event);\r\n      expect(event.id).toBeInstanceOf(UniqueEntityId);\r\n      expect(event.type).toBe(EventType.THREAT_DETECTED);\r\n      expect(event.severity).toBe(EventSeverity.HIGH);\r\n      expect(event.status).toBe(EventStatus.ACTIVE);\r\n      expect(event.processingStatus).toBe(EventProcessingStatus.RAW);\r\n      expect(event.title).toBe('Test Security Event');\r\n      expect(event.description).toBe('A test security event for unit testing');\r\n      expect(event.riskScore).toBe(75);\r\n      expect(event.confidenceLevel).toBe(85);\r\n    });\r\n\r\n    it('should generate EventCreatedDomainEvent on creation', () => {\r\n      const event = Event.create(validEventProps);\r\n\r\n      const domainEvents = event.domainEvents;\r\n      expect(domainEvents).toHaveLength(1);\r\n      expect(domainEvents[0]).toBeInstanceOf(EventCreatedDomainEvent);\r\n      \r\n      const createdEvent = domainEvents[0] as EventCreatedDomainEvent;\r\n      expect(createdEvent.eventType).toBe(EventType.THREAT_DETECTED);\r\n      expect(createdEvent.severity).toBe(EventSeverity.HIGH);\r\n      expect(createdEvent.title).toBe('Test Security Event');\r\n    });\r\n\r\n    it('should create event with custom ID', () => {\r\n      const customId = UniqueEntityId.generate();\r\n      const event = Event.create(validEventProps, customId);\r\n\r\n      expect(event.id.equals(customId)).toBe(true);\r\n    });\r\n\r\n    it('should throw error when metadata is missing', () => {\r\n      const invalidProps = { ...validEventProps };\r\n      delete (invalidProps as any).metadata;\r\n\r\n      expect(() => Event.create(invalidProps)).toThrow('Event must have metadata');\r\n    });\r\n\r\n    it('should throw error when type is missing', () => {\r\n      const invalidProps = { ...validEventProps };\r\n      delete (invalidProps as any).type;\r\n\r\n      expect(() => Event.create(invalidProps)).toThrow('Event must have a type');\r\n    });\r\n\r\n    it('should throw error when severity is missing', () => {\r\n      const invalidProps = { ...validEventProps };\r\n      delete (invalidProps as any).severity;\r\n\r\n      expect(() => Event.create(invalidProps)).toThrow('Event must have a severity');\r\n    });\r\n\r\n    it('should throw error when title is empty', () => {\r\n      const invalidProps = { ...validEventProps, title: '' };\r\n\r\n      expect(() => Event.create(invalidProps)).toThrow('Event must have a non-empty title');\r\n    });\r\n\r\n    it('should throw error when title is too long', () => {\r\n      const invalidProps = { ...validEventProps, title: 'a'.repeat(201) };\r\n\r\n      expect(() => Event.create(invalidProps)).toThrow('Event title cannot exceed 200 characters');\r\n    });\r\n\r\n    it('should throw error when description is too long', () => {\r\n      const invalidProps = { ...validEventProps, description: 'a'.repeat(2001) };\r\n\r\n      expect(() => Event.create(invalidProps)).toThrow('Event description cannot exceed 2000 characters');\r\n    });\r\n\r\n    it('should throw error when risk score is invalid', () => {\r\n      const invalidProps1 = { ...validEventProps, riskScore: -1 };\r\n      const invalidProps2 = { ...validEventProps, riskScore: 101 };\r\n\r\n      expect(() => Event.create(invalidProps1)).toThrow('Risk score must be between 0 and 100');\r\n      expect(() => Event.create(invalidProps2)).toThrow('Risk score must be between 0 and 100');\r\n    });\r\n\r\n    it('should throw error when confidence level is invalid', () => {\r\n      const invalidProps1 = { ...validEventProps, confidenceLevel: -1 };\r\n      const invalidProps2 = { ...validEventProps, confidenceLevel: 101 };\r\n\r\n      expect(() => Event.create(invalidProps1)).toThrow('Confidence level must be between 0 and 100');\r\n      expect(() => Event.create(invalidProps2)).toThrow('Confidence level must be between 0 and 100');\r\n    });\r\n\r\n    it('should throw error when too many tags', () => {\r\n      const invalidProps = { ...validEventProps, tags: Array(21).fill('tag') };\r\n\r\n      expect(() => Event.create(invalidProps)).toThrow('Event cannot have more than 20 tags');\r\n    });\r\n  });\r\n\r\n  describe('Status Management', () => {\r\n    let event: Event;\r\n\r\n    beforeEach(() => {\r\n      event = Event.create(validEventProps);\r\n      event.clearEvents(); // Clear creation event for cleaner tests\r\n    });\r\n\r\n    it('should change status successfully', () => {\r\n      event.changeStatus(EventStatus.INVESTIGATING, 'analyst1', 'Starting investigation');\r\n\r\n      expect(event.status).toBe(EventStatus.INVESTIGATING);\r\n      \r\n      const domainEvents = event.domainEvents;\r\n      expect(domainEvents).toHaveLength(1);\r\n      expect(domainEvents[0]).toBeInstanceOf(EventStatusChangedDomainEvent);\r\n      \r\n      const statusEvent = domainEvents[0] as EventStatusChangedDomainEvent;\r\n      expect(statusEvent.oldStatus).toBe(EventStatus.ACTIVE);\r\n      expect(statusEvent.newStatus).toBe(EventStatus.INVESTIGATING);\r\n      expect(statusEvent.changedBy).toBe('analyst1');\r\n      expect(statusEvent.notes).toBe('Starting investigation');\r\n    });\r\n\r\n    it('should not generate event when status unchanged', () => {\r\n      event.changeStatus(EventStatus.ACTIVE);\r\n\r\n      expect(event.status).toBe(EventStatus.ACTIVE);\r\n      expect(event.domainEvents).toHaveLength(0);\r\n    });\r\n\r\n    it('should set resolution information when resolved', () => {\r\n      const beforeResolve = new Date();\r\n      event.changeStatus(EventStatus.RESOLVED, 'analyst1', 'Issue resolved');\r\n      const afterResolve = new Date();\r\n\r\n      expect(event.status).toBe(EventStatus.RESOLVED);\r\n      expect(event.resolvedBy).toBe('analyst1');\r\n      expect(event.resolutionNotes).toBe('Issue resolved');\r\n      expect(event.resolvedAt).toBeDefined();\r\n      expect(event.resolvedAt!.getTime()).toBeGreaterThanOrEqual(beforeResolve.getTime());\r\n      expect(event.resolvedAt!.getTime()).toBeLessThanOrEqual(afterResolve.getTime());\r\n    });\r\n\r\n    it('should clear resolution information when reopened', () => {\r\n      // First resolve\r\n      event.changeStatus(EventStatus.RESOLVED, 'analyst1', 'Issue resolved');\r\n      expect(event.resolvedAt).toBeDefined();\r\n      \r\n      // Then reopen\r\n      event.changeStatus(EventStatus.ACTIVE);\r\n      \r\n      expect(event.status).toBe(EventStatus.ACTIVE);\r\n      expect(event.resolvedAt).toBeUndefined();\r\n      expect(event.resolvedBy).toBeUndefined();\r\n      expect(event.resolutionNotes).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('Processing Status Management', () => {\r\n    let event: Event;\r\n\r\n    beforeEach(() => {\r\n      event = Event.create(validEventProps);\r\n      event.clearEvents();\r\n    });\r\n\r\n    it('should change processing status successfully', () => {\r\n      const beforeChange = new Date();\r\n      event.changeProcessingStatus(EventProcessingStatus.NORMALIZED);\r\n      const afterChange = new Date();\r\n\r\n      expect(event.processingStatus).toBe(EventProcessingStatus.NORMALIZED);\r\n      expect(event.lastProcessedAt).toBeDefined();\r\n      expect(event.lastProcessedAt!.getTime()).toBeGreaterThanOrEqual(beforeChange.getTime());\r\n      expect(event.lastProcessedAt!.getTime()).toBeLessThanOrEqual(afterChange.getTime());\r\n      \r\n      const domainEvents = event.domainEvents;\r\n      expect(domainEvents).toHaveLength(1);\r\n      expect(domainEvents[0]).toBeInstanceOf(EventProcessingStatusChangedDomainEvent);\r\n    });\r\n\r\n    it('should not generate event when processing status unchanged', () => {\r\n      event.changeProcessingStatus(EventProcessingStatus.RAW);\r\n\r\n      expect(event.processingStatus).toBe(EventProcessingStatus.RAW);\r\n      expect(event.domainEvents).toHaveLength(0);\r\n    });\r\n  });\r\n\r\n  describe('Data Management', () => {\r\n    let event: Event;\r\n\r\n    beforeEach(() => {\r\n      event = Event.create(validEventProps);\r\n    });\r\n\r\n    it('should update normalized data', () => {\r\n      const normalizedData = { normalized: true, field1: 'value1' };\r\n      \r\n      event.updateNormalizedData(normalizedData);\r\n\r\n      expect(event.normalizedData).toEqual(normalizedData);\r\n      expect(event.processingStatus).toBe(EventProcessingStatus.NORMALIZED);\r\n    });\r\n\r\n    it('should update risk score', () => {\r\n      event.updateRiskScore(90);\r\n\r\n      expect(event.riskScore).toBe(90);\r\n    });\r\n\r\n    it('should throw error for invalid risk score', () => {\r\n      expect(() => event.updateRiskScore(-1)).toThrow('Risk score must be between 0 and 100');\r\n      expect(() => event.updateRiskScore(101)).toThrow('Risk score must be between 0 and 100');\r\n    });\r\n\r\n    it('should update confidence level', () => {\r\n      event.updateConfidenceLevel(95);\r\n\r\n      expect(event.confidenceLevel).toBe(95);\r\n    });\r\n\r\n    it('should throw error for invalid confidence level', () => {\r\n      expect(() => event.updateConfidenceLevel(-1)).toThrow('Confidence level must be between 0 and 100');\r\n      expect(() => event.updateConfidenceLevel(101)).toThrow('Confidence level must be between 0 and 100');\r\n    });\r\n\r\n    it('should add tags', () => {\r\n      event.addTags(['new-tag', 'another-tag']);\r\n\r\n      expect(event.tags).toContain('new-tag');\r\n      expect(event.tags).toContain('another-tag');\r\n      expect(event.tags).toContain('test'); // Original tag should remain\r\n    });\r\n\r\n    it('should not add duplicate tags', () => {\r\n      event.addTags(['test', 'new-tag']); // 'test' already exists\r\n\r\n      expect(event.tags.filter(tag => tag === 'test')).toHaveLength(1);\r\n      expect(event.tags).toContain('new-tag');\r\n    });\r\n\r\n    it('should throw error when adding too many tags', () => {\r\n      const manyTags = Array(19).fill(0).map((_, i) => `tag-${i}`);\r\n      \r\n      expect(() => event.addTags(manyTags)).toThrow('Event cannot have more than 20 tags');\r\n    });\r\n\r\n    it('should remove tags', () => {\r\n      event.removeTags(['test']);\r\n\r\n      expect(event.tags).not.toContain('test');\r\n      expect(event.tags).toContain('security'); // Other tag should remain\r\n    });\r\n\r\n    it('should set correlation ID', () => {\r\n      const correlationId = 'correlation-123';\r\n      \r\n      event.setCorrelationId(correlationId);\r\n\r\n      expect(event.correlationId).toBe(correlationId);\r\n    });\r\n\r\n    it('should set parent event', () => {\r\n      const parentId = UniqueEntityId.generate();\r\n      \r\n      event.setParentEvent(parentId);\r\n\r\n      expect(event.parentEventId?.equals(parentId)).toBe(true);\r\n    });\r\n\r\n    it('should record processing attempt', () => {\r\n      const initialAttempts = event.processingAttempts;\r\n      const beforeAttempt = new Date();\r\n      \r\n      event.recordProcessingAttempt('Test error');\r\n      \r\n      const afterAttempt = new Date();\r\n\r\n      expect(event.processingAttempts).toBe(initialAttempts + 1);\r\n      expect(event.lastProcessingError).toBe('Test error');\r\n      expect(event.lastProcessedAt).toBeDefined();\r\n      expect(event.lastProcessedAt!.getTime()).toBeGreaterThanOrEqual(beforeAttempt.getTime());\r\n      expect(event.lastProcessedAt!.getTime()).toBeLessThanOrEqual(afterAttempt.getTime());\r\n    });\r\n\r\n    it('should clear processing error', () => {\r\n      event.recordProcessingAttempt('Test error');\r\n      expect(event.lastProcessingError).toBe('Test error');\r\n      \r\n      event.clearProcessingError();\r\n      \r\n      expect(event.lastProcessingError).toBeUndefined();\r\n    });\r\n\r\n    it('should update attributes', () => {\r\n      event.updateAttributes({ newAttr: 'newValue', testAttribute: 'updatedValue' });\r\n\r\n      expect(event.attributes.newAttr).toBe('newValue');\r\n      expect(event.attributes.testAttribute).toBe('updatedValue');\r\n    });\r\n\r\n    it('should remove attribute', () => {\r\n      event.removeAttribute('testAttribute');\r\n\r\n      expect(event.attributes.testAttribute).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('Query Methods', () => {\r\n    let event: Event;\r\n\r\n    beforeEach(() => {\r\n      event = Event.create(validEventProps);\r\n    });\r\n\r\n    it('should identify active events', () => {\r\n      expect(event.isActive()).toBe(true);\r\n      \r\n      event.changeStatus(EventStatus.RESOLVED);\r\n      expect(event.isActive()).toBe(false);\r\n      \r\n      event.changeStatus(EventStatus.CLOSED);\r\n      expect(event.isActive()).toBe(false);\r\n      \r\n      event.changeStatus(EventStatus.FALSE_POSITIVE);\r\n      expect(event.isActive()).toBe(false);\r\n    });\r\n\r\n    it('should identify resolved events', () => {\r\n      expect(event.isResolved()).toBe(false);\r\n      \r\n      event.changeStatus(EventStatus.RESOLVED);\r\n      expect(event.isResolved()).toBe(true);\r\n    });\r\n\r\n    it('should identify high severity events', () => {\r\n      expect(event.isHighSeverity()).toBe(true); // HIGH severity\r\n      \r\n      const lowSeverityProps = { ...validEventProps, severity: EventSeverity.LOW };\r\n      const lowSeverityEvent = Event.create(lowSeverityProps);\r\n      expect(lowSeverityEvent.isHighSeverity()).toBe(false);\r\n      \r\n      const criticalSeverityProps = { ...validEventProps, severity: EventSeverity.CRITICAL };\r\n      const criticalSeverityEvent = Event.create(criticalSeverityProps);\r\n      expect(criticalSeverityEvent.isHighSeverity()).toBe(true);\r\n    });\r\n\r\n    it('should identify critical events', () => {\r\n      expect(event.isCritical()).toBe(false); // HIGH severity, not CRITICAL\r\n      \r\n      const criticalProps = { ...validEventProps, severity: EventSeverity.CRITICAL };\r\n      const criticalEvent = Event.create(criticalProps);\r\n      expect(criticalEvent.isCritical()).toBe(true);\r\n    });\r\n\r\n    it('should identify high risk events', () => {\r\n      expect(event.isHighRisk()).toBe(true); // Risk score 75 >= 70\r\n      \r\n      const lowRiskProps = { ...validEventProps, riskScore: 50 };\r\n      const lowRiskEvent = Event.create(lowRiskProps);\r\n      expect(lowRiskEvent.isHighRisk()).toBe(false);\r\n      \r\n      const noRiskProps = { ...validEventProps };\r\n      delete noRiskProps.riskScore;\r\n      const noRiskEvent = Event.create(noRiskProps);\r\n      expect(noRiskEvent.isHighRisk()).toBe(false);\r\n    });\r\n\r\n    it('should identify processing failures', () => {\r\n      expect(event.hasProcessingFailed()).toBe(false);\r\n      \r\n      event.changeProcessingStatus(EventProcessingStatus.FAILED);\r\n      expect(event.hasProcessingFailed()).toBe(true);\r\n    });\r\n\r\n    it('should identify exceeded max attempts', () => {\r\n      expect(event.hasExceededMaxAttempts()).toBe(false);\r\n      \r\n      // Record 5 attempts (max allowed)\r\n      for (let i = 0; i < 5; i++) {\r\n        event.recordProcessingAttempt();\r\n      }\r\n      \r\n      expect(event.hasExceededMaxAttempts()).toBe(true);\r\n    });\r\n\r\n    it('should check for tags', () => {\r\n      expect(event.hasTag('test')).toBe(true);\r\n      expect(event.hasTag('nonexistent')).toBe(false);\r\n      \r\n      expect(event.hasAnyTag(['test', 'nonexistent'])).toBe(true);\r\n      expect(event.hasAnyTag(['nonexistent', 'another'])).toBe(false);\r\n      \r\n      expect(event.hasAllTags(['test', 'security'])).toBe(true);\r\n      expect(event.hasAllTags(['test', 'nonexistent'])).toBe(false);\r\n    });\r\n\r\n    it('should calculate age correctly', () => {\r\n      const age = event.getAge();\r\n      \r\n      expect(age).toBeGreaterThanOrEqual(0);\r\n      expect(age).toBeLessThan(1000); // Should be very recent\r\n    });\r\n\r\n    it('should identify recent events', () => {\r\n      expect(event.isRecent(3600000)).toBe(true); // Within 1 hour\r\n      expect(event.isRecent(1)).toBe(false); // Within 1ms (too strict)\r\n    });\r\n\r\n    it('should identify stale events', () => {\r\n      expect(event.isStale(86400000)).toBe(false); // Not older than 24 hours\r\n      expect(event.isStale(1)).toBe(true); // Older than 1ms\r\n    });\r\n  });\r\n\r\n  describe('Summary and Serialization', () => {\r\n    let event: Event;\r\n\r\n    beforeEach(() => {\r\n      event = Event.create(validEventProps);\r\n    });\r\n\r\n    it('should generate correct summary', () => {\r\n      const summary = event.getSummary();\r\n\r\n      expect(summary.id).toBe(event.id.toString());\r\n      expect(summary.title).toBe('Test Security Event');\r\n      expect(summary.type).toBe(EventType.THREAT_DETECTED);\r\n      expect(summary.severity).toBe(EventSeverity.HIGH);\r\n      expect(summary.status).toBe(EventStatus.ACTIVE);\r\n      expect(summary.processingStatus).toBe(EventProcessingStatus.RAW);\r\n      expect(summary.riskScore).toBe(75);\r\n      expect(summary.isActive).toBe(true);\r\n      expect(summary.isHighRisk).toBe(true);\r\n      expect(typeof summary.age).toBe('number');\r\n      expect(typeof summary.source).toBe('string');\r\n    });\r\n\r\n    it('should serialize to JSON correctly', () => {\r\n      const json = event.toJSON();\r\n\r\n      expect(json.id).toBe(event.id.toString());\r\n      expect(json.type).toBe(EventType.THREAT_DETECTED);\r\n      expect(json.severity).toBe(EventSeverity.HIGH);\r\n      expect(json.title).toBe('Test Security Event');\r\n      expect(json.metadata).toBeDefined();\r\n      expect(json.rawData).toEqual({ message: 'Test threat detected' });\r\n      expect(json.tags).toEqual(['test', 'security']);\r\n      expect(json.riskScore).toBe(75);\r\n      expect(json.confidenceLevel).toBe(85);\r\n      expect(json.attributes).toEqual({ testAttribute: 'testValue' });\r\n      expect(json.summary).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('Status Consistency Validation', () => {\r\n    it('should throw error when resolved event lacks resolution timestamp', () => {\r\n      const invalidProps = {\r\n        ...validEventProps,\r\n        status: EventStatus.RESOLVED,\r\n        // Missing resolvedAt\r\n      };\r\n\r\n      expect(() => Event.create(invalidProps)).toThrow('Resolved events must have a resolution timestamp');\r\n    });\r\n\r\n    it('should throw error when non-resolved event has resolution info', () => {\r\n      const invalidProps = {\r\n        ...validEventProps,\r\n        status: EventStatus.ACTIVE,\r\n        resolvedAt: new Date(), // Should not have resolution info\r\n      };\r\n\r\n      expect(() => Event.create(invalidProps)).toThrow('Only resolved or closed events can have resolution information');\r\n    });\r\n\r\n    it('should throw error when resolved event has inconsistent processing status', () => {\r\n      const invalidProps = {\r\n        ...validEventProps,\r\n        status: EventStatus.RESOLVED,\r\n        processingStatus: EventProcessingStatus.RAW, // Inconsistent\r\n        resolvedAt: new Date(),\r\n      };\r\n\r\n      expect(() => Event.create(invalidProps)).toThrow('Resolved events must have resolved or archived processing status');\r\n    });\r\n  });\r\n\r\n  describe('Factory Integration', () => {\r\n    it('should work with EventFactory', () => {\r\n      const event = EventFactory.create({\r\n        type: EventType.MALWARE_DETECTED,\r\n        severity: EventSeverity.CRITICAL,\r\n        title: 'Malware Alert',\r\n        rawData: { malware: 'trojan.exe' },\r\n        sourceType: EventSourceType.ANTIVIRUS,\r\n        sourceIdentifier: 'av-scanner-01',\r\n      });\r\n\r\n      expect(event).toBeInstanceOf(Event);\r\n      expect(event.type).toBe(EventType.MALWARE_DETECTED);\r\n      expect(event.severity).toBe(EventSeverity.CRITICAL);\r\n      expect(event.title).toBe('Malware Alert');\r\n      expect(event.metadata.source.type).toBe(EventSourceType.ANTIVIRUS);\r\n      expect(event.metadata.source.identifier).toBe('av-scanner-01');\r\n    });\r\n  });\r\n});"], "version": 3}