27a396a2dc0ed214cbd92a1da79fed22
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const correlated_event_specification_1 = require("../correlated-event.specification");
const correlated_event_entity_1 = require("../../entities/correlated-event.entity");
const correlation_status_enum_1 = require("../../enums/correlation-status.enum");
const shared_kernel_1 = require("../../../../../shared-kernel");
const event_metadata_value_object_1 = require("../../value-objects/event-metadata/event-metadata.value-object");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const event_status_enum_1 = require("../../enums/event-status.enum");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const confidence_level_enum_1 = require("../../enums/confidence-level.enum");
const event_timestamp_value_object_1 = require("../../value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../value-objects/event-metadata/event-source.value-object");
describe('CorrelatedEvent Specifications', () => {
    let mockEventMetadata;
    let baseProps;
    beforeEach(() => {
        // Create mock event metadata
        mockEventMetadata = event_metadata_value_object_1.EventMetadata.create({
            timestamp: event_timestamp_value_object_1.EventTimestamp.now(),
            source: event_source_value_object_1.EventSource.create({
                type: 'SIEM',
                identifier: 'test-siem-001',
                name: 'Test SIEM System'
            }),
            processingInfo: {
                receivedAt: new Date(),
                processedAt: new Date(),
                processingDuration: 100,
                version: '1.0.0'
            }
        });
        baseProps = {
            enrichedEventId: shared_kernel_1.UniqueEntityId.create(),
            metadata: mockEventMetadata,
            type: event_type_enum_1.EventType.SECURITY_ALERT,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            status: event_status_enum_1.EventStatus.ACTIVE,
            processingStatus: event_processing_status_enum_1.EventProcessingStatus.CORRELATED,
            correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
            enrichedData: {
                original_event: 'test_event',
                enriched_at: new Date().toISOString()
            },
            correlatedData: {
                correlation_id: 'corr_123',
                related_events: 5
            },
            title: 'Test Correlated Security Event',
            confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH,
            correlationId: 'corr_test_123',
            childEventIds: [],
            appliedRules: [],
            correlationMatches: [],
            relatedEventIds: [],
            correlationPatterns: ['temporal_sequence'],
            correlationQualityScore: 85
        };
    });
    describe('CorrelationCompletedSpecification', () => {
        it('should be satisfied by completed correlation events', () => {
            const spec = new correlated_event_specification_1.CorrelationCompletedSpecification();
            const completedEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED
            });
            expect(spec.isSatisfiedBy(completedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has completed correlation');
        });
        it('should not be satisfied by non-completed correlation events', () => {
            const spec = new correlated_event_specification_1.CorrelationCompletedSpecification();
            const pendingEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.PENDING
            });
            expect(spec.isSatisfiedBy(pendingEvent)).toBe(false);
        });
    });
    describe('CorrelationFailedSpecification', () => {
        it('should be satisfied by failed correlation events', () => {
            const spec = new correlated_event_specification_1.CorrelationFailedSpecification();
            const failedEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.FAILED
            });
            expect(spec.isSatisfiedBy(failedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has failed correlation');
        });
        it('should not be satisfied by non-failed correlation events', () => {
            const spec = new correlated_event_specification_1.CorrelationFailedSpecification();
            const completedEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED
            });
            expect(spec.isSatisfiedBy(completedEvent)).toBe(false);
        });
    });
    describe('CorrelationInProgressSpecification', () => {
        it('should be satisfied by in-progress correlation events', () => {
            const spec = new correlated_event_specification_1.CorrelationInProgressSpecification();
            const inProgressEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.IN_PROGRESS
            });
            expect(spec.isSatisfiedBy(inProgressEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event is currently being correlated');
        });
    });
    describe('CorrelationPartialSpecification', () => {
        it('should be satisfied by partial correlation events', () => {
            const spec = new correlated_event_specification_1.CorrelationPartialSpecification();
            const partialEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.PARTIAL
            });
            expect(spec.isSatisfiedBy(partialEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has partial correlation results');
        });
    });
    describe('HighCorrelationQualitySpecification', () => {
        it('should be satisfied by high quality correlation events', () => {
            const spec = new correlated_event_specification_1.HighCorrelationQualitySpecification();
            const highQualityEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationQualityScore: 85
            });
            expect(spec.isSatisfiedBy(highQualityEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has high correlation quality (>= 70)');
        });
        it('should not be satisfied by low quality correlation events', () => {
            const spec = new correlated_event_specification_1.HighCorrelationQualitySpecification();
            const lowQualityEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationQualityScore: 50
            });
            expect(spec.isSatisfiedBy(lowQualityEvent)).toBe(false);
        });
    });
    describe('HasValidationErrorsSpecification', () => {
        it('should be satisfied by events with validation errors', () => {
            const spec = new correlated_event_specification_1.HasValidationErrorsSpecification();
            const eventWithErrors = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                validationErrors: ['Error 1', 'Error 2']
            });
            expect(spec.isSatisfiedBy(eventWithErrors)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has validation errors');
        });
        it('should not be satisfied by events without validation errors', () => {
            const spec = new correlated_event_specification_1.HasValidationErrorsSpecification();
            const eventWithoutErrors = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                validationErrors: []
            });
            expect(spec.isSatisfiedBy(eventWithoutErrors)).toBe(false);
        });
    });
    describe('RequiresManualReviewSpecification', () => {
        it('should be satisfied by events requiring manual review', () => {
            const spec = new correlated_event_specification_1.RequiresManualReviewSpecification();
            const reviewEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                requiresManualReview: true
            });
            expect(spec.isSatisfiedBy(reviewEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event requires manual review');
        });
        it('should not be satisfied by events not requiring manual review', () => {
            const spec = new correlated_event_specification_1.RequiresManualReviewSpecification();
            const noReviewEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                requiresManualReview: false
            });
            expect(spec.isSatisfiedBy(noReviewEvent)).toBe(false);
        });
    });
    describe('ReadyForNextStageSpecification', () => {
        it('should be satisfied by events ready for next stage', () => {
            const spec = new correlated_event_specification_1.ReadyForNextStageSpecification();
            const readyEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                correlationQualityScore: 85,
                validationErrors: [],
                requiresManualReview: false
            });
            expect(spec.isSatisfiedBy(readyEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event is ready for next processing stage');
        });
        it('should not be satisfied by events not ready for next stage', () => {
            const spec = new correlated_event_specification_1.ReadyForNextStageSpecification();
            const notReadyEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.FAILED,
                correlationQualityScore: 50
            });
            expect(spec.isSatisfiedBy(notReadyEvent)).toBe(false);
        });
    });
    describe('HighConfidenceCorrelationSpecification', () => {
        it('should be satisfied by high confidence correlation events', () => {
            const spec = new correlated_event_specification_1.HighConfidenceCorrelationSpecification();
            const highConfidenceEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationResult: {
                    success: true,
                    appliedRules: [],
                    failedRules: [],
                    warnings: [],
                    errors: [],
                    processingDurationMs: 1000,
                    confidenceScore: 90,
                    rulesUsed: 2,
                    matchesFound: 5,
                    patternsIdentified: []
                }
            });
            expect(spec.isSatisfiedBy(highConfidenceEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has high confidence correlation');
        });
    });
    describe('HasAttackChainSpecification', () => {
        it('should be satisfied by events with attack chains', () => {
            const spec = new correlated_event_specification_1.HasAttackChainSpecification();
            const attackChain = {
                id: 'attack_chain_1',
                name: 'Multi-stage Attack',
                description: 'Coordinated attack with multiple stages',
                stages: [
                    {
                        id: 'stage_1',
                        name: 'Initial Access',
                        description: 'Initial compromise',
                        eventIds: [shared_kernel_1.UniqueEntityId.create()],
                        order: 1,
                        confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                        timestamp: new Date()
                    }
                ],
                confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                timeline: {
                    startTime: new Date(Date.now() - 3600000),
                    endTime: new Date(),
                    duration: 3600000
                }
            };
            const eventWithAttackChain = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                attackChain
            });
            expect(spec.isSatisfiedBy(eventWithAttackChain)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has an attack chain');
        });
        it('should not be satisfied by events without attack chains', () => {
            const spec = new correlated_event_specification_1.HasAttackChainSpecification();
            const eventWithoutAttackChain = correlated_event_entity_1.CorrelatedEvent.create(baseProps);
            expect(spec.isSatisfiedBy(eventWithoutAttackChain)).toBe(false);
        });
    });
    describe('HasTemporalCorrelationSpecification', () => {
        it('should be satisfied by events with temporal correlation', () => {
            const spec = new correlated_event_specification_1.HasTemporalCorrelationSpecification();
            const eventWithTemporal = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                temporalCorrelation: {
                    timeWindow: 3600000,
                    eventSequence: [shared_kernel_1.UniqueEntityId.create()],
                    patternType: 'sequence',
                    confidence: 80
                }
            });
            expect(spec.isSatisfiedBy(eventWithTemporal)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has temporal correlation data');
        });
    });
    describe('HasSpatialCorrelationSpecification', () => {
        it('should be satisfied by events with spatial correlation', () => {
            const spec = new correlated_event_specification_1.HasSpatialCorrelationSpecification();
            const eventWithSpatial = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                spatialCorrelation: {
                    sourceIps: ['*************'],
                    targetIps: ['*********'],
                    networkSegments: ['***********/24'],
                    geographicRegions: ['US-East'],
                    confidence: 75
                }
            });
            expect(spec.isSatisfiedBy(eventWithSpatial)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has spatial correlation data');
        });
    });
    describe('HasBehavioralCorrelationSpecification', () => {
        it('should be satisfied by events with behavioral correlation', () => {
            const spec = new correlated_event_specification_1.HasBehavioralCorrelationSpecification();
            const eventWithBehavioral = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                behavioralCorrelation: {
                    userPatterns: ['login_anomaly'],
                    systemPatterns: ['resource_spike'],
                    anomalyScore: 85,
                    baselineDeviation: 2.5,
                    confidence: 80
                }
            });
            expect(spec.isSatisfiedBy(eventWithBehavioral)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has behavioral correlation data');
        });
    });
    describe('CorrelationStatusSpecification', () => {
        it('should be satisfied by events with matching status', () => {
            const spec = new correlated_event_specification_1.CorrelationStatusSpecification([
                correlation_status_enum_1.CorrelationStatus.COMPLETED,
                correlation_status_enum_1.CorrelationStatus.PARTIAL
            ]);
            const completedEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED
            });
            expect(spec.isSatisfiedBy(completedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event status is one of: COMPLETED, PARTIAL');
        });
        it('should not be satisfied by events with non-matching status', () => {
            const spec = new correlated_event_specification_1.CorrelationStatusSpecification([correlation_status_enum_1.CorrelationStatus.COMPLETED]);
            const failedEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.FAILED
            });
            expect(spec.isSatisfiedBy(failedEvent)).toBe(false);
        });
    });
    describe('ConfidenceLevelSpecification', () => {
        it('should be satisfied by events with matching confidence level', () => {
            const spec = new correlated_event_specification_1.ConfidenceLevelSpecification([
                confidence_level_enum_1.ConfidenceLevel.HIGH,
                confidence_level_enum_1.ConfidenceLevel.VERY_HIGH
            ]);
            const highConfidenceEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH
            });
            expect(spec.isSatisfiedBy(highConfidenceEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event confidence level is one of: HIGH, VERY_HIGH');
        });
    });
    describe('CorrelationQualityScoreRangeSpecification', () => {
        it('should be satisfied by events within quality score range', () => {
            const spec = new correlated_event_specification_1.CorrelationQualityScoreRangeSpecification(70, 90);
            const qualityEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationQualityScore: 85
            });
            expect(spec.isSatisfiedBy(qualityEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event quality score is between 70 and 90');
        });
        it('should not be satisfied by events outside quality score range', () => {
            const spec = new correlated_event_specification_1.CorrelationQualityScoreRangeSpecification(70, 90);
            const lowQualityEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationQualityScore: 50
            });
            expect(spec.isSatisfiedBy(lowQualityEvent)).toBe(false);
        });
        it('should handle minimum score only', () => {
            const spec = new correlated_event_specification_1.CorrelationQualityScoreRangeSpecification(70);
            const qualityEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationQualityScore: 85
            });
            expect(spec.isSatisfiedBy(qualityEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event quality score is at least 70');
        });
        it('should handle maximum score only', () => {
            const spec = new correlated_event_specification_1.CorrelationQualityScoreRangeSpecification(undefined, 90);
            const qualityEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationQualityScore: 85
            });
            expect(spec.isSatisfiedBy(qualityEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event quality score is at most 90');
        });
    });
    describe('AppliedRuleSpecification', () => {
        it('should be satisfied by events with applied rule', () => {
            const rule = {
                id: 'test_rule_1',
                name: 'Test Rule',
                description: 'A test correlation rule',
                type: correlated_event_entity_1.CorrelationRuleType.TEMPORAL,
                priority: 100,
                required: false,
                timeWindowMs: 3600000,
                minConfidence: 70
            };
            const spec = new correlated_event_specification_1.AppliedRuleSpecification('test_rule_1');
            const eventWithRule = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                appliedRules: [rule]
            });
            expect(spec.isSatisfiedBy(eventWithRule)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has applied rule: test_rule_1');
        });
    });
    describe('EnrichedEventSpecification', () => {
        it('should be satisfied by events referencing specific enriched event', () => {
            const enrichedEventId = shared_kernel_1.UniqueEntityId.create();
            const spec = new correlated_event_specification_1.EnrichedEventSpecification(enrichedEventId);
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                enrichedEventId
            });
            expect(spec.isSatisfiedBy(event)).toBe(true);
            expect(spec.getDescription()).toBe(`Correlated event references enriched event: ${enrichedEventId.toString()}`);
        });
    });
    describe('CorrelationIdSpecification', () => {
        it('should be satisfied by events with matching correlation ID', () => {
            const correlationId = 'corr_test_123';
            const spec = new correlated_event_specification_1.CorrelationIdSpecification(correlationId);
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationId
            });
            expect(spec.isSatisfiedBy(event)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has correlation ID: corr_test_123');
        });
    });
    describe('HasRelatedEventsSpecification', () => {
        it('should be satisfied by events with sufficient related events', () => {
            const spec = new correlated_event_specification_1.HasRelatedEventsSpecification(2);
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                relatedEventIds: [shared_kernel_1.UniqueEntityId.create(), shared_kernel_1.UniqueEntityId.create(), shared_kernel_1.UniqueEntityId.create()]
            });
            expect(spec.isSatisfiedBy(event)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has at least 2 related events');
        });
        it('should not be satisfied by events with insufficient related events', () => {
            const spec = new correlated_event_specification_1.HasRelatedEventsSpecification(5);
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                relatedEventIds: [shared_kernel_1.UniqueEntityId.create()]
            });
            expect(spec.isSatisfiedBy(event)).toBe(false);
        });
    });
    describe('HasChildEventsSpecification', () => {
        it('should be satisfied by events with sufficient child events', () => {
            const spec = new correlated_event_specification_1.HasChildEventsSpecification(1);
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                childEventIds: [shared_kernel_1.UniqueEntityId.create()]
            });
            expect(spec.isSatisfiedBy(event)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has at least 1 child events');
        });
    });
    describe('CorrelationPatternSpecification', () => {
        it('should be satisfied by events with matching patterns', () => {
            const spec = new correlated_event_specification_1.CorrelationPatternSpecification(['temporal_sequence', 'ip_clustering']);
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationPatterns: ['temporal_sequence', 'behavioral_anomaly']
            });
            expect(spec.isSatisfiedBy(event)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has patterns: temporal_sequence, ip_clustering');
        });
    });
    describe('MatchTypeSpecification', () => {
        it('should be satisfied by events with matching match types', () => {
            const match = {
                eventId: shared_kernel_1.UniqueEntityId.create(),
                confidence: 80,
                matchType: correlated_event_entity_1.CorrelationMatchType.TEMPORAL,
                ruleId: 'rule1',
                details: {},
                timestamp: new Date(),
                weight: 0.8
            };
            const spec = new correlated_event_specification_1.MatchTypeSpecification([correlated_event_entity_1.CorrelationMatchType.TEMPORAL]);
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationMatches: [match]
            });
            expect(spec.isSatisfiedBy(event)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has matches of types: TEMPORAL');
        });
    });
    describe('ExceededMaxAttemptsSpecification', () => {
        it('should be satisfied by events that exceeded max attempts', () => {
            const spec = new correlated_event_specification_1.ExceededMaxAttemptsSpecification();
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationAttempts: 5 // Assuming max is 3
            });
            expect(spec.isSatisfiedBy(event)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has exceeded maximum correlation attempts');
        });
    });
    describe('ReviewedEventSpecification', () => {
        it('should be satisfied by reviewed events', () => {
            const spec = new correlated_event_specification_1.ReviewedEventSpecification();
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                reviewedAt: new Date(),
                reviewedBy: '<EMAIL>'
            });
            expect(spec.isSatisfiedBy(event)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event has been manually reviewed');
        });
        it('should not be satisfied by unreviewed events', () => {
            const spec = new correlated_event_specification_1.ReviewedEventSpecification();
            const event = correlated_event_entity_1.CorrelatedEvent.create(baseProps);
            expect(spec.isSatisfiedBy(event)).toBe(false);
        });
    });
    describe('PendingReviewSpecification', () => {
        it('should be satisfied by events pending review', () => {
            const spec = new correlated_event_specification_1.PendingReviewSpecification();
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                requiresManualReview: true
                // reviewedAt is undefined
            });
            expect(spec.isSatisfiedBy(event)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event is pending manual review');
        });
        it('should not be satisfied by reviewed events', () => {
            const spec = new correlated_event_specification_1.PendingReviewSpecification();
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                requiresManualReview: true,
                reviewedAt: new Date()
            });
            expect(spec.isSatisfiedBy(event)).toBe(false);
        });
    });
    describe('CorrelationDurationRangeSpecification', () => {
        it('should be satisfied by events within duration range', () => {
            const spec = new correlated_event_specification_1.CorrelationDurationRangeSpecification(1000, 5000);
            // Create event with correlation duration
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStartedAt: new Date(Date.now() - 3000),
                correlationCompletedAt: new Date()
            });
            expect(spec.isSatisfiedBy(event)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event duration is between 1000ms and 5000ms');
        });
        it('should not be satisfied by events without duration', () => {
            const spec = new correlated_event_specification_1.CorrelationDurationRangeSpecification(1000, 5000);
            const event = correlated_event_entity_1.CorrelatedEvent.create(baseProps);
            expect(spec.isSatisfiedBy(event)).toBe(false);
        });
    });
    describe('AverageMatchConfidenceRangeSpecification', () => {
        it('should be satisfied by events within confidence range', () => {
            const match1 = {
                eventId: shared_kernel_1.UniqueEntityId.create(),
                confidence: 80,
                matchType: correlated_event_entity_1.CorrelationMatchType.TEMPORAL,
                ruleId: 'rule1',
                details: {},
                timestamp: new Date(),
                weight: 0.8
            };
            const match2 = {
                eventId: shared_kernel_1.UniqueEntityId.create(),
                confidence: 90,
                matchType: correlated_event_entity_1.CorrelationMatchType.SPATIAL,
                ruleId: 'rule2',
                details: {},
                timestamp: new Date(),
                weight: 0.9
            };
            const spec = new correlated_event_specification_1.AverageMatchConfidenceRangeSpecification(70, 95);
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationMatches: [match1, match2]
            });
            expect(spec.isSatisfiedBy(event)).toBe(true);
            expect(spec.getDescription()).toBe('Correlated event average match confidence is between 70 and 95');
        });
    });
    describe('CorrelatedEventSpecificationBuilder', () => {
        it('should build complex specifications with AND logic', () => {
            const spec = correlated_event_specification_1.CorrelatedEventSpecificationBuilder.create()
                .correlationCompleted()
                .highCorrelationQuality()
                .withConfidenceLevel(confidence_level_enum_1.ConfidenceLevel.HIGH)
                .build();
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                correlationQualityScore: 85,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH
            });
            expect(spec.isSatisfiedBy(event)).toBe(true);
        });
        it('should build complex specifications with OR logic', () => {
            const spec = correlated_event_specification_1.CorrelatedEventSpecificationBuilder.create()
                .correlationCompleted()
                .correlationPartial()
                .buildWithOr();
            const completedEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED
            });
            const partialEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.PARTIAL
            });
            const failedEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.FAILED
            });
            expect(spec.isSatisfiedBy(completedEvent)).toBe(true);
            expect(spec.isSatisfiedBy(partialEvent)).toBe(true);
            expect(spec.isSatisfiedBy(failedEvent)).toBe(false);
        });
        it('should throw error when building without specifications', () => {
            const builder = correlated_event_specification_1.CorrelatedEventSpecificationBuilder.create();
            expect(() => builder.build()).toThrow('At least one specification must be added');
        });
        it('should return single specification when only one is added', () => {
            const spec = correlated_event_specification_1.CorrelatedEventSpecificationBuilder.create()
                .correlationCompleted()
                .build();
            expect(spec).toBeInstanceOf(correlated_event_specification_1.CorrelationCompletedSpecification);
        });
        it('should support fluent interface for all specification types', () => {
            const builder = correlated_event_specification_1.CorrelatedEventSpecificationBuilder.create()
                .correlationCompleted()
                .correlationFailed()
                .correlationInProgress()
                .correlationPartial()
                .highCorrelationQuality()
                .hasValidationErrors()
                .requiresManualReview()
                .readyForNextStage()
                .highConfidenceCorrelation()
                .hasAttackChain()
                .hasTemporalCorrelation()
                .hasSpatialCorrelation()
                .hasBehavioralCorrelation()
                .withCorrelationStatus(correlation_status_enum_1.CorrelationStatus.COMPLETED)
                .withConfidenceLevel(confidence_level_enum_1.ConfidenceLevel.HIGH)
                .correlationQualityScoreRange(70, 90)
                .withAppliedRule('test_rule')
                .fromEnrichedEvent(shared_kernel_1.UniqueEntityId.create())
                .withCorrelationId('corr_123')
                .hasRelatedEvents(2)
                .hasChildEvents(1)
                .withCorrelationPatterns('pattern1')
                .withMatchTypes(correlated_event_entity_1.CorrelationMatchType.TEMPORAL)
                .exceededMaxAttempts()
                .reviewed()
                .pendingReview()
                .correlationDurationRange(1000, 5000)
                .averageMatchConfidenceRange(70, 90);
            expect(builder).toBeInstanceOf(correlated_event_specification_1.CorrelatedEventSpecificationBuilder);
        });
    });
    describe('specification composition', () => {
        it('should support AND composition', () => {
            const spec1 = new correlated_event_specification_1.CorrelationCompletedSpecification();
            const spec2 = new correlated_event_specification_1.HighCorrelationQualitySpecification();
            const composedSpec = spec1.and(spec2);
            const event = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                correlationQualityScore: 85
            });
            expect(composedSpec.isSatisfiedBy(event)).toBe(true);
        });
        it('should support OR composition', () => {
            const spec1 = new correlated_event_specification_1.CorrelationCompletedSpecification();
            const spec2 = new correlated_event_specification_1.CorrelationPartialSpecification();
            const composedSpec = spec1.or(spec2);
            const completedEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED
            });
            const partialEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.PARTIAL
            });
            const failedEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.FAILED
            });
            expect(composedSpec.isSatisfiedBy(completedEvent)).toBe(true);
            expect(composedSpec.isSatisfiedBy(partialEvent)).toBe(true);
            expect(composedSpec.isSatisfiedBy(failedEvent)).toBe(false);
        });
        it('should support NOT composition', () => {
            const spec = new correlated_event_specification_1.CorrelationCompletedSpecification();
            const notSpec = spec.not();
            const completedEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED
            });
            const failedEvent = correlated_event_entity_1.CorrelatedEvent.create({
                ...baseProps,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.FAILED
            });
            expect(notSpec.isSatisfiedBy(completedEvent)).toBe(false);
            expect(notSpec.isSatisfiedBy(failedEvent)).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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