{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\security-orchestrator.service.ts", "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,gFAA2G;AAK3G,iFAA6E;AAC7E,mFAA+E;AAC/E,iGAA6F;AAC7F,qGAAgG;AAChG,0GAAiI;AACjI,0GAAoI;AACpI,sHAAwJ;AACxJ,8GAAiK;AACjK,gFAAuE;AACvE,wEAA+D;AAG/D,kFAAyE;AACzE,gGAAuF;AAEvF,8EAAqE;AACrE,yHAAuG;AAoDvG;;GAEG;AACH,IAAY,mBASX;AATD,WAAY,mBAAmB;IAC7B,0DAAmC,CAAA;IACnC,gDAAyB,CAAA;IACzB,4CAAqB,CAAA;IACrB,oDAA6B,CAAA;IAC7B,sDAA+B,CAAA;IAC/B,gDAAyB,CAAA;IACzB,sCAAe,CAAA;IACf,wCAAiB,CAAA;AACnB,CAAC,EATW,mBAAmB,mCAAnB,mBAAmB,QAS9B;AAwDD;;GAEG;AACH,IAAY,kBASX;AATD,WAAY,kBAAkB;IAC5B,uDAAiC,CAAA;IACjC,2DAAqC,CAAA;IACrC,2DAAqC,CAAA;IACrC,uEAAiD,CAAA;IACjD,iDAA2B,CAAA;IAC3B,+CAAyB,CAAA;IACzB,+DAAyC,CAAA;IACzC,mDAA6B,CAAA;AAC/B,CAAC,EATW,kBAAkB,kCAAlB,kBAAkB,QAS7B;AA0ED;;GAEG;AACH,IAAY,iBASX;AATD,WAAY,iBAAiB;IAC3B,4DAAuC,CAAA;IACvC,0DAAqC,CAAA;IACrC,8DAAyC,CAAA;IACzC,kDAA6B,CAAA;IAC7B,0DAAqC,CAAA;IACrC,4DAAuC,CAAA;IACvC,kEAA6C,CAAA;IAC7C,gDAA2B,CAAA;AAC7B,CAAC,EATW,iBAAiB,iCAAjB,iBAAiB,QAS5B;AAeD;;GAEG;AACH,IAAY,eAMX;AAND,WAAY,eAAe;IACzB,wCAAqB,CAAA;IACrB,sCAAmB,CAAA;IACnB,oCAAiB,CAAA;IACjB,4CAAyB,CAAA;IACzB,4CAAyB,CAAA;AAC3B,CAAC,EANW,eAAe,+BAAf,eAAe,QAM1B;AAED;;GAEG;AACH,IAAY,uBAKX;AALD,WAAY,uBAAuB;IACjC,sCAAW,CAAA;IACX,4CAAiB,CAAA;IACjB,wCAAa,CAAA;IACb,gDAAqB,CAAA;AACvB,CAAC,EALW,uBAAuB,uCAAvB,uBAAuB,QAKlC;AAwDD;;GAEG;AACH,IAAY,iBAOX;AAPD,WAAY,iBAAiB;IAC3B,kDAA6B,CAAA;IAC7B,sDAAiC,CAAA;IACjC,4DAAuC,CAAA;IACvC,0DAAqC,CAAA;IACrC,sDAAiC,CAAA;IACjC,sCAAiB,CAAA;AACnB,CAAC,EAPW,iBAAiB,iCAAjB,iBAAiB,QAO5B;AAYD;;GAEG;AACH,IAAY,oBAOX;AAPD,WAAY,oBAAoB;IAC9B,yCAAiB,CAAA;IACjB,uDAA+B,CAAA;IAC/B,qDAA6B,CAAA;IAC7B,uDAA+B,CAAA;IAC/B,mEAA2C,CAAA;IAC3C,yCAAiB,CAAA;AACnB,CAAC,EAPW,oBAAoB,oCAApB,oBAAoB,QAO/B;AAyDD;;GAEG;AACH,IAAY,WAOX;AAPD,WAAY,WAAW;IACrB,8BAAe,CAAA;IACf,8BAAe,CAAA;IACf,8BAAe,CAAA;IACf,0BAAW,CAAA;IACX,kCAAmB,CAAA;IACnB,sCAAuB,CAAA;AACzB,CAAC,EAPW,WAAW,2BAAX,WAAW,QAOtB;AAED;;GAEG;AACH,IAAY,sBAMX;AAND,WAAY,sBAAsB;IAChC,iDAAuB,CAAA;IACvB,2CAAiB,CAAA;IACjB,yCAAe,CAAA;IACf,iDAAuB,CAAA;IACvB,2CAAiB,CAAA;AACnB,CAAC,EANW,sBAAsB,sCAAtB,sBAAsB,QAMjC;AA0BD;;GAEG;AACH,IAAY,gBAQX;AARD,WAAY,gBAAgB;IAC1B,mEAA+C,CAAA;IAC/C,uDAAmC,CAAA;IACnC,iDAA6B,CAAA;IAC7B,mDAA+B,CAAA;IAC/B,uDAAmC,CAAA;IACnC,iDAA6B,CAAA;IAC7B,qCAAiB,CAAA;AACnB,CAAC,EARW,gBAAgB,gCAAhB,gBAAgB,QAQ3B;AAsBD;;;;;;;;;;;;;;;GAeG;AAEI,IAAM,2BAA2B,GAAjC,MAAM,2BAA4B,SAAQ,0BAAyC;IACxF,YACmB,eAAgC,EAChC,gBAAkC,EAClC,uBAAgD,EAChD,wBAAkD,EAClD,cAA8B,EAC9B,cAA8B,EAC9B,oBAA0C,EAC1C,gBAAkC;QAEnD,KAAK,CAAC,6BAA6B,CAAC,CAAC;QATpB,oBAAe,GAAf,eAAe,CAAiB;QAChC,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,4BAAuB,GAAvB,uBAAuB,CAAyB;QAChD,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,mBAAc,GAAd,cAAc,CAAgB;QAC9B,mBAAc,GAAd,cAAc,CAAgB;QAC9B,yBAAoB,GAApB,oBAAoB,CAAsB;QAC1C,qBAAgB,GAAhB,gBAAgB,CAAkB;IAGrD,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,2BAA2B,CAC/B,MAAe,EACf,OAA+C;QAE/C,MAAM,oBAAoB,GAAG,IAAI,CAAC,aAAa,CAAC;YAC9C,QAAQ,EAAE,8CAAgB,CAAC,MAAM;YACjC,IAAI,EAAE,0CAAY,CAAC,SAAS;YAC5B,SAAS,EAAE,MAAM,EAAE,YAAY;YAC/B,YAAY,EAAE,IAAI;YAClB,mBAAmB,EAAE,IAAI;YACzB,2BAA2B,EAAE,IAAI;YACjC,cAAc,EAAE,IAAI,CAAC,wBAAwB,EAAE;YAC/C,GAAG,OAAO;SACX,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,gBAAgB,CAC1B,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,4BAA4B,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC3E,oBAAoB,EACpB,6BAA6B,CAC9B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACzB,MAAe,EACf,OAA+C;QAE/C,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC;YAC3C,QAAQ,EAAE,8CAAgB,CAAC,IAAI;YAC/B,IAAI,EAAE,0CAAY,CAAC,SAAS;YAC5B,SAAS,EAAE,MAAM,EAAE,YAAY;YAC/B,cAAc,EAAE,IAAI,CAAC,wBAAwB,EAAE;YAC/C,GAAG,OAAO;SACX,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,gBAAgB,CAC1B,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,iBAAiB,CAAC,EAClE,iBAAiB,EACjB,uBAAuB,CACxB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,QAA+B,EAC/B,OAA+C;QAE/C,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;YACxC,QAAQ,EAAE,8CAAgB,CAAC,MAAM;YACjC,IAAI,EAAE,0CAAY,CAAC,MAAM;YACzB,SAAS,EAAE,MAAM,EAAE,aAAa;YAChC,mBAAmB,EAAE,IAAI;YACzB,GAAG,OAAO;SACX,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,gBAAgB,CAC1B,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,cAAc,CAAC,EAC/D,cAAc,EACd,sBAAsB,CACvB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAC3B,oBAA0C,EAC1C,OAA+C;QAE/C,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;YACzC,QAAQ,EAAE,8CAAgB,CAAC,QAAQ;YACnC,IAAI,EAAE,0CAAY,CAAC,cAAc;YACjC,SAAS,EAAE,OAAO,EAAE,aAAa;YACjC,YAAY,EAAE,KAAK;YACnB,GAAG,OAAO;SACX,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,gBAAgB,CAC1B,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,2BAA2B,CAAC,oBAAoB,EAAE,eAAe,CAAC,EACnF,eAAe,EACf,yBAAyB,CAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAC1B,SAAqC,EACrC,OAA+C;QAE/C,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;YACzC,QAAQ,EAAE,8CAAgB,CAAC,GAAG;YAC9B,IAAI,EAAE,0CAAY,CAAC,MAAM;YACzB,SAAS,EAAE,MAAM,EAAE,aAAa;YAChC,GAAG,OAAO;SACX,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,gBAAgB,CAC1B,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,8BAA8B,CAAC,SAAS,EAAE,eAAe,CAAC,EAC3E,eAAe,EACf,wBAAwB,CACzB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,4BAA4B,CACxC,MAAe,EACf,OAAqC;QAErC,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACrD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAgC;YAC1C,OAAO,EAAE,IAAI;YACb,eAAe;YACf,SAAS;YACT,OAAO,EAAE,IAAI,IAAI,EAAE;YACnB,QAAQ,EAAE,CAAC;YACX,eAAe,EAAE,CAAC;YAClB,eAAe,EAAE,CAAC;YAClB,oBAAoB,EAAE,CAAC;YACvB,eAAe,EAAE,CAAC;YAClB,mBAAmB,EAAE,CAAC;YACtB,oBAAoB,EAAE,CAAC;YACvB,iBAAiB,EAAE,EAAE;YACrB,cAAc,EAAE,EAAE;YAClB,kBAAkB,EAAE,EAAE;YACtB,eAAe,EAAE,EAAE;YACnB,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACjC,eAAe,EAAE,EAAE;SACpB,CAAC;QAEF,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,MAAM,CAAC,MAAM,SAAS,EAAE;gBAC7E,eAAe;gBACf,UAAU,EAAE,MAAM,CAAC,MAAM;gBACzB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;aACnB,CAAC,CAAC;YAEH,4BAA4B;YAC5B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAE3E,4BAA4B;YAC5B,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,EAAE,SAAS,IAAI,EAAE,CAAC;YAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;YAE9D,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAClD,CAAC;YAED,+BAA+B;YAC/B,IAAI,OAAO,CAAC,cAAc,EAAE,iBAAiB,EAAE,CAAC;gBAC9C,MAAM,IAAI,CAAC,0BAA0B,CAAC,cAAc,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YACzE,CAAC;YAED,2BAA2B;YAC3B,MAAM,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAE7E,MAAM,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC5B,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;YACjE,MAAM,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAE9D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,EAAE;gBAClD,eAAe;gBACf,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,eAAe,EAAE,MAAM,CAAC,eAAe;gBACvC,eAAe,EAAE,MAAM,CAAC,eAAe;gBACvC,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBACjD,eAAe;gBACf,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;YAEH,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;gBACjB,KAAK,EAAE,kBAAkB,CAAC,cAAc;gBACxC,SAAS,EAAE,sBAAsB;gBACjC,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,WAAW,EAAE,KAAK;aACnB,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CACxB,MAAe,EACf,OAAqC,EACrC,MAAmC;QAEnC,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC/C,IAAI,CAAC;gBACH,iCAAiC;gBACjC,IAAI,OAAO,CAAC,cAAc,EAAE,qBAAqB,EAAE,CAAC;oBAClD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBACjE,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBAChD,MAAM,CAAC,eAAe,EAAE,CAAC;gBAC3B,CAAC;gBAED,mBAAmB;gBACnB,IAAI,OAAO,CAAC,cAAc,EAAE,qBAAqB,EAAE,CAAC;oBAClD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBAChE,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;oBAC3C,MAAM,CAAC,eAAe,IAAI,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC1D,CAAC;gBAED,yBAAyB;gBACzB,IAAI,OAAO,CAAC,cAAc,EAAE,2BAA2B,IAAI,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,EAAE,CAAC;oBACpG,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBAC5E,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBAClD,MAAM,CAAC,oBAAoB,IAAI,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC;gBAC1E,CAAC;gBAED,qBAAqB;gBACrB,IAAI,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,cAAc,EAAE,uBAAuB,EAAE,CAAC;oBAC5E,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;oBACnF,IAAI,cAAc,EAAE,CAAC;wBACnB,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;wBAC5C,MAAM,CAAC,eAAe,IAAI,cAAc,CAAC,eAAe,CAAC;oBAC3D,CAAC;gBACH,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,KAAK,CAAC,EAAE,EAAE,EAAE;oBACtD,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;oBAC5B,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,CAAC,CAAC;gBAEH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;oBACjB,KAAK,EAAE,kBAAkB,CAAC,gBAAgB;oBAC1C,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;oBAC5B,SAAS,EAAE,yBAAyB;oBACpC,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,WAAW,EAAE,IAAI;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CACxB,KAAY,EACZ,OAAqC;QAErC,MAAM,iBAAiB,GAAsB;YAC3C,SAAS,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACvC,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE;gBACN,mBAAmB,EAAE,IAAI;gBACzB,gBAAgB,EAAE,IAAI;gBACtB,iBAAiB,EAAE,IAAI;gBACvB,oBAAoB,EAAE,IAAI;gBAC1B,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,KAAK;gBACrC,UAAU,EAAE,CAAC;gBACb,SAAS,EAAE,CAAC;aACb;YACD,WAAW,EAAE;gBACX,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,QAAQ;gBAClC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS;gBACvC,WAAW,EAAE,CAAC,kBAAkB,CAAC;aAClC;YACD,aAAa,EAAE,OAAO,CAAC,aAAa;SACrC,CAAC;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CACzB,KAAY,EACZ,OAAqC;QAErC,MAAM,gBAAgB,GAA2B;YAC/C,SAAS,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACvC,MAAM,EAAE;gBACN,uBAAuB,EAAE,IAAI;gBAC7B,WAAW,EAAE,EAAE;gBACf,sBAAsB,EAAE,EAAE;gBAC1B,iBAAiB,EAAE,KAAK;gBACxB,cAAc,EAAE,IAAI;gBACpB,wBAAwB,EAAE,IAAI;gBAC9B,iBAAiB,EAAE,IAAI;aACxB;YACD,WAAW,EAAE;gBACX,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,QAAQ;gBAClC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS;aACxC;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAClC,KAAY,EACZ,OAAqC;QAErC,MAAM,cAAc,GAAgC;YAClD,SAAS,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACvC,MAAM,EAAE;gBACN,QAAQ,EAAE,OAAc;gBACxB,kBAAkB,EAAE,KAAK;gBACzB,SAAS,EAAE,KAAK;gBAChB,kBAAkB,EAAE,CAAC;aACtB;YACD,WAAW,EAAE;gBACX,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,QAAQ;gBAClC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS;aACxC;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CACpC,KAAY,EACZ,OAAqC,EACrC,mBAAgD;QAEhD,kCAAkC;QAClC,MAAM,cAAc,GAAG,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAClE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CACnD,CAAC;QACF,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CACzE,EAAE,CAAC,MAAM,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,CACrC,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,cAAc,EAAE,iBAAiB,CAAC,EAAE,CAAC;YAC1E,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,eAAe,GAAoB;YACvC,OAAO,EAAE,cAAc,EAAE,OAAO,IAAI,EAAE;YACtC,eAAe,EAAE,iBAAiB,EAAE,eAAe,IAAI,EAAE;YACzD,KAAK;YACL,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,cAAc,EAAE,iBAAiB,CAAC;YAClF,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,0CAAY,CAAC,SAAS;YAC5C,WAAW,EAAE;gBACX,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,QAAQ;gBAClC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS;gBACvC,WAAW,EAAE,CAAC,kBAAkB,CAAC;aAClC;YACD,WAAW,EAAE;gBACX,kBAAkB,EAAE,MAAM;gBAC1B,oBAAoB,EAAE,CAAC;gBACvB,gBAAgB,EAAE,KAAK;gBACvB,gBAAgB,EAAE,IAAI;aACvB;YACD,aAAa,EAAE,OAAO,CAAC,aAAa;SACrC,CAAC;QAEF,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B,CACtC,MAAe,EACf,OAAqC,EACrC,MAAmC;QAEnC,IAAI,CAAC;YACH,uCAAuC;YACvC,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAC/E,OAAO,EAAE,gBAAgB;YACzB,SAAS,EAAE,kBAAkB;YAC7B,mCAAa,CAAC,MAAM,CAAC,mBAAmB;aACzC,CAAC;YAEF,4BAA4B;YAC5B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,CAAC;YAC/E,MAAM,CAAC,mBAAmB,GAAG,YAAY,CAAC,MAAM,CAAC;YAEjD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,YAAY,CAAC,MAAM,qBAAqB,CAAC,CAAC;QAEzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC7E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAChC,QAA+B,EAC/B,OAAqC;QAErC,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,oCAAoC;YACpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAEzD,6BAA6B;YAC7B,MAAM,OAAO,GAAG,EAAE,CAAC;YACnB,MAAM,cAAc,GAAG,MAAM,OAAO,CAAC,GAAG,CACtC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CACxD,CAAC;YAEF,KAAK,MAAM,QAAQ,IAAI,cAAc,EAAE,CAAC;gBACtC,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;YACpC,CAAC;YAED,kBAAkB;YAClB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAElE,iCAAiC;YACjC,MAAM,YAAY,GAAG,QAAQ,CAAC,kBAAkB;gBAC9C,CAAC,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;gBAC5C,CAAC,CAAC,EAAE,CAAC;YAEP,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,MAAM,GAAwB;gBAClC,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,OAAO;gBACP,QAAQ,EAAE,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE;gBACjD,cAAc,EAAE,MAAM,CAAC,MAAM;gBAC7B,YAAY,EAAE,OAAO,CAAC,MAAM;gBAC5B,gBAAgB,EAAE,QAAQ,CAAC,MAAM;gBACjC,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,YAAY;gBACZ,eAAe,EAAE,MAAM,IAAI,CAAC,oCAAoC,CAAC,OAAO,EAAE,QAAQ,CAAC;gBACnF,UAAU,EAAE,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,QAAQ,CAAC;aAC/D,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,EAAE;gBAC1C,MAAM;gBACN,cAAc,EAAE,MAAM,CAAC,MAAM;gBAC7B,YAAY,EAAE,OAAO,CAAC,MAAM;gBAC5B,gBAAgB,EAAE,QAAQ,CAAC,MAAM;aAClC,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,2BAA2B,CACvC,IAA0B,EAC1B,OAAqC;QAErC,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACjD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,EAAE;gBAClD,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,WAAW;gBACX,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;aACjC,CAAC,CAAC;YAEH,2BAA2B;YAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;YAE3E,kBAAkB;YAClB,MAAM,eAAe,GAAG,EAAE,CAAC;YAC3B,IAAI,eAAe,GAAG,CAAC,CAAC;YACxB,IAAI,aAAa,GAAG,CAAC,CAAC;YAEtB,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;gBACnC,IAAI,CAAC;oBACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAC5D,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,UAAU,CAClB,CAAC;oBAEF,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAEnC,IAAI,YAAY,CAAC,MAAM,KAAK,iCAAY,CAAC,SAAS,EAAE,CAAC;wBACnD,eAAe,EAAE,CAAC;oBACpB,CAAC;yBAAM,CAAC;wBACN,aAAa,EAAE,CAAC;oBAClB,CAAC;gBAEH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;wBAC3C,QAAQ,EAAE,MAAM,CAAC,EAAE;wBACnB,UAAU,EAAE,MAAM,CAAC,IAAI;wBACvB,KAAK,EAAE,KAAK,CAAC,OAAO;qBACrB,CAAC,CAAC;oBACH,aAAa,EAAE,CAAC;gBAClB,CAAC;YACH,CAAC;YAED,yBAAyB;YACzB,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACxC,IAAI,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;oBAC3D,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;oBACnD,WAAW,EAAE,CAAC;gBAChB,CAAC;YACH,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,MAAM,GAAmB;gBAC7B,OAAO,EAAE,aAAa,KAAK,CAAC;gBAC5B,WAAW;gBACX,eAAe;gBACf,aAAa;gBACb,cAAc,EAAE,CAAC;gBACjB,aAAa,EAAE,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE;gBACtD,eAAe;gBACf,kBAAkB,EAAE,IAAI,CAAC,2BAA2B,CAAC,eAAe,EAAE,aAAa,CAAC;gBACpF,MAAM,EAAE,MAAM,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC;gBACxD,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,eAAe,EAAE,EAAE;aACpB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,EAAE;gBAC7C,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,WAAW;gBACX,eAAe;gBACf,aAAa;gBACb,WAAW;aACZ,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACtD,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,WAAW;gBACX,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,8BAA8B,CAC1C,SAAqC,EACrC,OAAqC;QAErC,IAAI,CAAC;YACH,+BAA+B;YAC/B,MAAM,CAAC,UAAU,EAAE,WAAW,EAAE,kBAAkB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACtE,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC;gBACvE,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC;gBACzE,IAAI,CAAC,uBAAuB,CAAC,0BAA0B,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC;aACxF,CAAC,CAAC;YAEH,4BAA4B;YAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,WAAW,EAAE,kBAAkB,CAAC,CAAC;YAC/F,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;YACzD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YACnD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,+BAA+B,CAChE,UAAU,EACV,WAAW,EACX,kBAAkB,CACnB,CAAC;YAEF,OAAO;gBACL,SAAS;gBACT,aAAa;gBACb,SAAS;gBACT,eAAe,EAAE,UAAU;gBAC3B,gBAAgB,EAAE,WAAW;gBAC7B,uBAAuB,EAAE,kBAAkB;gBAC3C,MAAM;gBACN,eAAe;gBACf,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,iBAAiB;IAET,wBAAwB;QAC9B,OAAO;YACL,qBAAqB,EAAE,IAAI;YAC3B,qBAAqB,EAAE,IAAI;YAC3B,2BAA2B,EAAE,IAAI;YACjC,uBAAuB,EAAE,IAAI;YAC7B,iBAAiB,EAAE,IAAI;YACvB,gBAAgB,EAAE,IAAI;YACtB,SAAS,EAAE,EAAE;YACb,uBAAuB,EAAE,CAAC;YAC1B,aAAa,EAAE,CAAC;YAChB,SAAS,EAAE,MAAM;SAClB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,MAAe,EACf,OAAqC;QAErC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7D,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,MAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,aAAa,GAAG,IAAI,CAAC;YAEzB,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACvC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;oBAChE,IAAI,IAAI,CAAC,MAAM,KAAK,mBAAmB,CAAC,eAAe,EAAE,CAAC;wBACxD,aAAa,GAAG,KAAK,CAAC;wBACtB,MAAM;oBACR,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,aAAa,EAAE,CAAC;gBAClB,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAEO,yBAAyB,CAAC,IAAuB,EAAE,KAAY;QACrE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,oBAAoB;QACpB,IAAI,SAAS,CAAC,UAAU,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACvE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,wBAAwB;QACxB,IAAI,SAAS,CAAC,cAAc,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,6BAA6B;QAC7B,IAAI,SAAS,CAAC,kBAAkB,IAAI,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC,kBAAkB,EAAE,CAAC;YACnF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,aAAa,CAAI,KAAU,EAAE,SAAiB;QACpD,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YACjD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,4BAA4B,CAAC,KAAY;QAC/C,MAAM,uBAAuB,GAAG;YAC9B,2BAAS,CAAC,sBAAsB;YAChC,2BAAS,CAAC,cAAc;YACxB,2BAAS,CAAC,oBAAoB;YAC9B,2BAAS,CAAC,aAAa;SACxB,CAAC;QAEF,OAAO,uBAAuB,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAEO,qBAAqB,CAC3B,KAAY,EACZ,cAA+B,EAC/B,iBAAqC;QAErC,OAAO,CACL,KAAK,CAAC,QAAQ,IAAI,mCAAa,CAAC,IAAI;YACpC,CAAC,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;YACrD,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CACpE,CAAC;IACJ,CAAC;IAEO,yBAAyB,CAC/B,KAAY,EACZ,cAA+B,EAC/B,iBAAqC;QAErC,IAAI,KAAK,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,EAAE,CAAC;YAC9C,OAAO,8CAAgB,CAAC,QAAQ,CAAC;QACnC,CAAC;QAED,IAAI,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7E,OAAO,8CAAgB,CAAC,QAAQ,CAAC;QACnC,CAAC;QAED,IAAI,iBAAiB,EAAE,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,mDAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChG,OAAO,8CAAgB,CAAC,QAAQ,CAAC;QACnC,CAAC;QAED,IAAI,KAAK,CAAC,QAAQ,KAAK,mCAAa,CAAC,IAAI,EAAE,CAAC;YAC1C,OAAO,8CAAgB,CAAC,IAAI,CAAC;QAC/B,CAAC;QAED,OAAO,8CAAgB,CAAC,MAAM,CAAC;IACjC,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,QAA+B;QAChE,+EAA+E;QAC/E,OAAO,IAAI,CAAC,eAAe,CAAC,eAAe,CACzC,QAAQ,CAAC,SAAS,CAAC,KAAK,EACxB,QAAQ,CAAC,SAAS,CAAC,GAAG,EACtB,QAAQ,CAAC,UAAU,CACpB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,MAAe,EAAE,OAAc;QAChE,MAAM,QAAQ,GAAoB,EAAE,CAAC;QAErC,qBAAqB;QACrB,MAAM,eAAe,GAAG,IAAI,GAAG,EAAkB,CAAC;QAClD,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACrB,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YACvC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,KAAK,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,IAAI,eAAe,CAAC,OAAO,EAAE,EAAE,CAAC;YAC5D,IAAI,SAAS,GAAG,EAAE,EAAE,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC;oBACZ,EAAE,EAAE,8CAAc,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;oBACxC,IAAI,EAAE,iBAAiB,CAAC,iBAAiB;oBACzC,IAAI,EAAE,uBAAuB;oBAC7B,WAAW,EAAE,yCAAyC,MAAM,EAAE;oBAC9D,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;oBAC/C,QAAQ,EAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC,qCAAc,CAAC,IAAI,CAAC,CAAC,CAAC,qCAAc,CAAC,MAAM;oBACtE,UAAU,EAAE,CAAC,MAAM,CAAC;oBACpB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;oBACpF,QAAQ,EAAE;wBACR,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;wBAC1B,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,SAAS;qBACzC;oBACD,eAAe,EAAE,EAAE;oBACnB,eAAe,EAAE,CAAC,sBAAsB,MAAM,2BAA2B,CAAC;iBAC3E,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,MAAe;QACnD,MAAM,YAAY,GAAuB,EAAE,CAAC;QAE5C,8BAA8B;QAC9B,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,YAAY;QACvC,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAmB,CAAC;QAEpD,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACrB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC9E,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBACnC,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YACpC,CAAC;YACD,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,KAAK,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,YAAY,CAAC,IAAI,CAAC;oBAChB,EAAE,EAAE,8CAAc,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;oBACxC,IAAI,EAAE,eAAe,CAAC,QAAQ;oBAC9B,MAAM,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAC7C,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE,EAAE,GAAG,CAAC;oBAClD,UAAU;oBACV,WAAW,EAAE,GAAG,WAAW,CAAC,MAAM,2BAA2B,UAAU,GAAC,IAAI,UAAU;oBACtF,YAAY,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB,CAAC,MAAM;iBACrG,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,MAAmC,EACnC,OAAqC;QAErC,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,MAAM,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;YAC/B,eAAe,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,eAAe,4DAA4D,CAAC,CAAC;QAC9G,CAAC;QAED,IAAI,MAAM,CAAC,oBAAoB,GAAG,CAAC,EAAE,CAAC;YACpC,eAAe,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,oBAAoB,0DAA0D,CAAC,CAAC;QACjH,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,eAAe,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,sEAAsE,CAAC,CAAC;QACtH,CAAC;QAED,IAAI,MAAM,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;YACnC,eAAe,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,mBAAmB,wEAAwE,CAAC,CAAC;QAC9H,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,oCAAoC,CAAC,OAAc,EAAE,QAAyB;QAC1F,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,eAAe,CAAC,IAAI,CAAC,eAAe,OAAO,CAAC,MAAM,sDAAsD,CAAC,CAAC;QAC5G,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC,MAAM,mDAAmD,CAAC,CAAC;QACtG,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,0BAA0B,CAAC,OAAc,EAAE,QAAyB;QAC1E,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClD,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;QAClH,MAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC;QAEhH,OAAO,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAClC,IAAoB,EACpB,IAA0B,EAC1B,OAAqC;QAErC,4CAA4C;QAC5C,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YAC5B,KAAK,iBAAiB,CAAC,YAAY;gBACjC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBAC/D,OAAO,OAAO,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,OAAO,CAAC,CAAC,CAAC,iBAAiB;YAE5E,KAAK,iBAAiB,CAAC,iBAAiB;gBACtC,OAAO,IAAI,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,CAAC;YAElD;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,MAAwB,EACxB,OAAqC;QAErC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,MAAM,CAAC,IAAI,EAAE,EAAE;YAC7D,UAAU,EAAE,MAAM,CAAC,IAAI;YACvB,UAAU,EAAE,MAAM,CAAC,UAAU;SAC9B,CAAC,CAAC;QAEH,2DAA2D;QAC3D,mCAAmC;IACrC,CAAC;IAEO,2BAA2B,CAAC,eAAuB,EAAE,aAAqB;QAChF,MAAM,KAAK,GAAG,eAAe,GAAG,aAAa,CAAC;QAC9C,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAC1B,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC;IACzC,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,eAAsB;QACvD,6DAA6D;QAC7D,OAAO;YACL,gBAAgB,EAAE,SAAS;YAC3B,wBAAwB,EAAE,SAAS;YACnC,kBAAkB,EAAE,MAAM;YAC1B,iBAAiB,EAAE,KAAK;YACxB,mBAAmB,EAAE,QAAQ;YAC7B,kBAAkB,EAAE,MAAM;YAC1B,gBAAgB,EAAE,MAAM;SACzB,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAAC,UAAe,EAAE,WAAgB,EAAE,kBAAuB;QACvF,6CAA6C;QAC7C,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,yCAAyC;QACzC,KAAK,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;QAC3D,KAAK,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAEtD,mCAAmC;QACnC,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAE9C,6CAA6C;QAC7C,KAAK,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,EAAE,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;QAE5E,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC3C,CAAC;IAEO,kBAAkB,CAAC,aAAqB;QAC9C,IAAI,aAAa,IAAI,EAAE;YAAE,OAAO,iBAAiB,CAAC,GAAG,CAAC;QACtD,IAAI,aAAa,IAAI,EAAE;YAAE,OAAO,iBAAiB,CAAC,MAAM,CAAC;QACzD,IAAI,aAAa,IAAI,EAAE;YAAE,OAAO,iBAAiB,CAAC,IAAI,CAAC;QACvD,OAAO,iBAAiB,CAAC,QAAQ,CAAC;IACpC,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,SAAqC;QAC/D,gDAAgD;QAChD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,KAAK,CAAC,+BAA+B,CAC3C,UAAe,EACf,WAAgB,EAChB,kBAAuB;QAEvB,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,WAAW,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;YACxC,eAAe,CAAC,IAAI,CAAC,WAAW,WAAW,CAAC,mBAAmB,+BAA+B,CAAC,CAAC;QAClG,CAAC;QAED,IAAI,kBAAkB,CAAC,0BAA0B,GAAG,CAAC,EAAE,CAAC;YACtD,eAAe,CAAC,IAAI,CAAC,aAAa,kBAAkB,CAAC,0BAA0B,2BAA2B,CAAC,CAAC;QAC9G,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,iBAAiB;QACvB,OAAO;YACL,mBAAmB,EAAE,CAAC;YACtB,0BAA0B,EAAE,CAAC;YAC7B,mBAAmB,EAAE,CAAC;YACtB,yBAAyB,EAAE,CAAC;YAC5B,qBAAqB,EAAE,CAAC;YACxB,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,CAAC;gBACd,YAAY,EAAE,CAAC;aAChB;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC5B,MAAmC,EACnC,OAAqC;QAErC,OAAO;YACL,mBAAmB,EAAE,MAAM,CAAC,QAAQ;YACpC,0BAA0B,EAAE,MAAM,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACrG,mBAAmB,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,kBAAkB,EAAE,CAAC,CAAC;YAC9F,yBAAyB,EAAE,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC;YAClG,qBAAqB,EAAE,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC,CAAC;YAC5F,UAAU,EAAE,MAAM,CAAC,eAAe,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,oBAAoB;YACnF,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,GAAG,GAAG;YAC3E,WAAW,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;YACrF,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,CAAC,EAAE,wCAAwC;gBACrD,WAAW,EAAE,CAAC;gBACd,YAAY,EAAE,CAAC;aAChB;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AA5/BY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;yDAGyB,kCAAe,oBAAf,kCAAe,oDACd,oCAAgB,oBAAhB,oCAAgB,oDACT,kDAAuB,oBAAvB,kDAAuB,oDACtB,qDAAwB,oBAAxB,qDAAwB,oDAClC,0CAAc,oBAAd,0CAAc,oDACd,0CAAc,oBAAd,0CAAc,oDACR,sDAAoB,oBAApB,sDAAoB,oDACxB,8CAAgB,oBAAhB,8CAAgB;GAT1C,2BAA2B,CA4/BvC;AAeD,IAAY,iBAKX;AALD,WAAY,iBAAiB;IAC3B,gCAAW,CAAA;IACX,sCAAiB,CAAA;IACjB,kCAAa,CAAA;IACb,0CAAqB,CAAA;AACvB,CAAC,EALW,iBAAiB,iCAAjB,iBAAiB,QAK5B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\security-orchestrator.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { BaseService, ServiceContext, ServiceResult } from '../../../../shared-kernel/domain/base-service';\r\nimport { Event } from '../../domain/entities/event.entity';\r\nimport { NormalizedEvent } from '../../domain/entities/normalized-event.entity';\r\nimport { EnrichedEvent } from '../../domain/entities/enriched-event.entity';\r\nimport { CorrelatedEvent } from '../../domain/entities/correlated-event.entity';\r\nimport { EventRepository } from '../../domain/repositories/event.repository';\r\nimport { ThreatRepository } from '../../domain/repositories/threat.repository';\r\nimport { VulnerabilityRepository } from '../../domain/repositories/vulnerability.repository';\r\nimport { ResponseActionRepository } from '../../domain/repositories/response-action.repository';\r\nimport { EventProcessor, ProcessingContext, ProcessingResult } from '../../domain/interfaces/services/event-processor.interface';\r\nimport { ThreatDetector, ThreatAnalysis, ThreatDetectionContext } from '../../domain/interfaces/services/threat-detector.interface';\r\nimport { VulnerabilityScanner, VulnerabilityScan, VulnerabilityScannerContext } from '../../domain/interfaces/services/vulnerability-scanner.interface';\r\nimport { ResponseExecutor, ResponseContext, ResponseResult, ResponsePriority, ResponseMode } from '../../domain/interfaces/services/response-executor.interface';\r\nimport { EventSeverity } from '../../domain/enums/event-severity.enum';\r\nimport { EventType } from '../../domain/enums/event-type.enum';\r\nimport { EventStatus } from '../../domain/enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../domain/enums/event-processing-status.enum';\r\nimport { ThreatSeverity } from '../../domain/enums/threat-severity.enum';\r\nimport { VulnerabilitySeverity } from '../../domain/enums/vulnerability-severity.enum';\r\nimport { ActionType } from '../../domain/enums/action-type.enum';\r\nimport { ActionStatus } from '../../domain/enums/action-status.enum';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { CorrelationId } from '../../../../shared-kernel/value-objects/correlation-id.value-object';\r\n\r\n/**\r\n * Security Orchestration Context\r\n * Extended service context for security operations\r\n */\r\nexport interface SecurityOrchestrationContext extends ServiceContext {\r\n  /** Processing priority level */\r\n  priority?: ResponsePriority;\r\n  /** Processing mode */\r\n  mode?: ResponseMode;\r\n  /** Maximum processing time in milliseconds */\r\n  timeoutMs?: number;\r\n  /** Enable automatic response execution */\r\n  autoResponse?: boolean;\r\n  /** Enable threat hunting */\r\n  enableThreatHunting?: boolean;\r\n  /** Enable vulnerability scanning */\r\n  enableVulnerabilityScanning?: boolean;\r\n  /** Custom processing rules */\r\n  customRules?: OrchestrationRule[];\r\n  /** Workflow configuration */\r\n  workflowConfig?: WorkflowConfiguration;\r\n}\r\n\r\n/**\r\n * Orchestration Rule Interface\r\n */\r\nexport interface OrchestrationRule {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  condition: OrchestrationCondition;\r\n  action: OrchestrationAction;\r\n  priority: number;\r\n  enabled: boolean;\r\n}\r\n\r\n/**\r\n * Orchestration Condition Interface\r\n */\r\nexport interface OrchestrationCondition {\r\n  eventTypes?: EventType[];\r\n  severityLevels?: EventSeverity[];\r\n  threatSeverities?: ThreatSeverity[];\r\n  vulnerabilitySeverities?: VulnerabilitySeverity[];\r\n  riskScoreThreshold?: number;\r\n  timeWindow?: number;\r\n  customCriteria?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Orchestration Action Enum\r\n */\r\nexport enum OrchestrationAction {\r\n  SKIP_PROCESSING = 'SKIP_PROCESSING',\r\n  PRIORITIZE = 'PRIORITIZE',\r\n  ESCALATE = 'ESCALATE',\r\n  AUTO_RESPOND = 'AUTO_RESPOND',\r\n  MANUAL_REVIEW = 'MANUAL_REVIEW',\r\n  QUARANTINE = 'QUARANTINE',\r\n  ALERT = 'ALERT',\r\n  CUSTOM = 'CUSTOM',\r\n}\r\n\r\n/**\r\n * Workflow Configuration Interface\r\n */\r\nexport interface WorkflowConfiguration {\r\n  enableEventProcessing: boolean;\r\n  enableThreatDetection: boolean;\r\n  enableVulnerabilityScanning: boolean;\r\n  enableResponseExecution: boolean;\r\n  enableCorrelation: boolean;\r\n  enableEnrichment: boolean;\r\n  batchSize: number;\r\n  maxConcurrentOperations: number;\r\n  retryAttempts: number;\r\n  timeoutMs: number;\r\n}\r\n\r\n/**\r\n * Security Orchestration Result\r\n */\r\nexport interface SecurityOrchestrationResult {\r\n  success: boolean;\r\n  orchestrationId: string;\r\n  startTime: Date;\r\n  endTime: Date;\r\n  duration: number;\r\n  eventsProcessed: number;\r\n  threatsDetected: number;\r\n  vulnerabilitiesFound: number;\r\n  actionsExecuted: number;\r\n  correlationsCreated: number;\r\n  enrichmentsPerformed: number;\r\n  processingResults: ProcessingResult[];\r\n  threatAnalyses: ThreatAnalysis[];\r\n  vulnerabilityScans: VulnerabilityScan[];\r\n  responseResults: ResponseResult[];\r\n  errors: OrchestrationError[];\r\n  warnings: string[];\r\n  metrics: OrchestrationMetrics;\r\n  recommendations: string[];\r\n}\r\n\r\n/**\r\n * Orchestration Error Interface\r\n */\r\nexport interface OrchestrationError {\r\n  stage: OrchestrationStage;\r\n  eventId?: string;\r\n  errorCode: string;\r\n  message: string;\r\n  details?: Record<string, any>;\r\n  timestamp: Date;\r\n  recoverable: boolean;\r\n}\r\n\r\n/**\r\n * Orchestration Stage Enum\r\n */\r\nexport enum OrchestrationStage {\r\n  INITIALIZATION = 'INITIALIZATION',\r\n  EVENT_PROCESSING = 'EVENT_PROCESSING',\r\n  THREAT_DETECTION = 'THREAT_DETECTION',\r\n  VULNERABILITY_SCANNING = 'VULNERABILITY_SCANNING',\r\n  CORRELATION = 'CORRELATION',\r\n  ENRICHMENT = 'ENRICHMENT',\r\n  RESPONSE_EXECUTION = 'RESPONSE_EXECUTION',\r\n  FINALIZATION = 'FINALIZATION',\r\n}\r\n\r\n/**\r\n * Orchestration Metrics Interface\r\n */\r\nexport interface OrchestrationMetrics {\r\n  totalProcessingTime: number;\r\n  averageEventProcessingTime: number;\r\n  threatDetectionTime: number;\r\n  vulnerabilityScanningTime: number;\r\n  responseExecutionTime: number;\r\n  throughput: number;\r\n  errorRate: number;\r\n  successRate: number;\r\n  resourceUtilization: {\r\n    cpuUsage: number;\r\n    memoryUsage: number;\r\n    networkUsage: number;\r\n  };\r\n}\r\n\r\n/**\r\n * Threat Hunting Criteria Interface\r\n */\r\nexport interface ThreatHuntingCriteria {\r\n  timeRange: { start: Date; end: Date };\r\n  eventTypes?: EventType[];\r\n  severityLevels?: EventSeverity[];\r\n  sources?: string[];\r\n  indicators?: string[];\r\n  mitreTechniques?: string[];\r\n  customFilters?: Record<string, any>;\r\n  maxResults?: number;\r\n  includeCorrelation?: boolean;\r\n  includePatternAnalysis?: boolean;\r\n}\r\n\r\n/**\r\n * Threat Hunting Result Interface\r\n */\r\nexport interface ThreatHuntingResult {\r\n  huntId: string;\r\n  criteria: ThreatHuntingCriteria;\r\n  startTime: Date;\r\n  endTime: Date;\r\n  duration: number;\r\n  eventsAnalyzed: number;\r\n  threatsFound: number;\r\n  patternsDetected: number;\r\n  events: Event[];\r\n  threats: any[];\r\n  patterns: ThreatPattern[];\r\n  correlations: EventCorrelation[];\r\n  recommendations: string[];\r\n  confidence: number;\r\n}\r\n\r\n/**\r\n * Threat Pattern Interface\r\n */\r\nexport interface ThreatPattern {\r\n  id: string;\r\n  type: ThreatPatternType;\r\n  name: string;\r\n  description: string;\r\n  confidence: number;\r\n  severity: ThreatSeverity;\r\n  indicators: string[];\r\n  events: string[];\r\n  timeline: { start: Date; end: Date };\r\n  mitreTechniques: string[];\r\n  recommendations: string[];\r\n}\r\n\r\n/**\r\n * Threat Pattern Type Enum\r\n */\r\nexport enum ThreatPatternType {\r\n  FREQUENCY_ANOMALY = 'FREQUENCY_ANOMALY',\r\n  TEMPORAL_PATTERN = 'TEMPORAL_PATTERN',\r\n  SOURCE_CORRELATION = 'SOURCE_CORRELATION',\r\n  ATTACK_CHAIN = 'ATTACK_CHAIN',\r\n  LATERAL_MOVEMENT = 'LATERAL_MOVEMENT',\r\n  DATA_EXFILTRATION = 'DATA_EXFILTRATION',\r\n  PRIVILEGE_ESCALATION = 'PRIVILEGE_ESCALATION',\r\n  PERSISTENCE = 'PERSISTENCE',\r\n}\r\n\r\n/**\r\n * Event Correlation Interface\r\n */\r\nexport interface EventCorrelation {\r\n  id: string;\r\n  type: CorrelationType;\r\n  events: string[];\r\n  confidence: number;\r\n  timeWindow: number;\r\n  description: string;\r\n  significance: CorrelationSignificance;\r\n}\r\n\r\n/**\r\n * Correlation Type Enum\r\n */\r\nexport enum CorrelationType {\r\n  TEMPORAL = 'TEMPORAL',\r\n  SPATIAL = 'SPATIAL',\r\n  CAUSAL = 'CAUSAL',\r\n  BEHAVIORAL = 'BEHAVIORAL',\r\n  CONTEXTUAL = 'CONTEXTUAL',\r\n}\r\n\r\n/**\r\n * Correlation Significance Enum\r\n */\r\nexport enum CorrelationSignificance {\r\n  LOW = 'LOW',\r\n  MEDIUM = 'MEDIUM',\r\n  HIGH = 'HIGH',\r\n  CRITICAL = 'CRITICAL',\r\n}\r\n\r\n/**\r\n * Incident Response Plan Interface\r\n */\r\nexport interface IncidentResponsePlan {\r\n  incidentId: string;\r\n  severity: EventSeverity;\r\n  category: string;\r\n  actions: ResponseActionPlan[];\r\n  escalationRules: EscalationRule[];\r\n  timeline: ResponseTimeline;\r\n  stakeholders: Stakeholder[];\r\n  communicationPlan: CommunicationPlan;\r\n  rollbackPlan?: RollbackPlan;\r\n}\r\n\r\n/**\r\n * Response Action Plan Interface\r\n */\r\nexport interface ResponseActionPlan {\r\n  id: string;\r\n  type: ActionType;\r\n  name: string;\r\n  description: string;\r\n  parameters: Record<string, any>;\r\n  priority: number;\r\n  dependencies: string[];\r\n  timeout: number;\r\n  retryPolicy: RetryPolicy;\r\n  approvalRequired: boolean;\r\n  rollbackSupported: boolean;\r\n}\r\n\r\n/**\r\n * Escalation Rule Interface\r\n */\r\nexport interface EscalationRule {\r\n  id: string;\r\n  condition: EscalationCondition;\r\n  action: EscalationAction;\r\n  timeout: number;\r\n  priority: number;\r\n  enabled: boolean;\r\n}\r\n\r\n/**\r\n * Escalation Condition Interface\r\n */\r\nexport interface EscalationCondition {\r\n  type: EscalationTrigger;\r\n  threshold?: number;\r\n  timeWindow?: number;\r\n  criteria?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Escalation Trigger Enum\r\n */\r\nexport enum EscalationTrigger {\r\n  TIME_ELAPSED = 'TIME_ELAPSED',\r\n  FAILED_ACTIONS = 'FAILED_ACTIONS',\r\n  SEVERITY_INCREASE = 'SEVERITY_INCREASE',\r\n  IMPACT_THRESHOLD = 'IMPACT_THRESHOLD',\r\n  MANUAL_TRIGGER = 'MANUAL_TRIGGER',\r\n  CUSTOM = 'CUSTOM',\r\n}\r\n\r\n/**\r\n * Escalation Action Interface\r\n */\r\nexport interface EscalationAction {\r\n  type: EscalationActionType;\r\n  parameters: Record<string, any>;\r\n  recipients: string[];\r\n  template?: string;\r\n}\r\n\r\n/**\r\n * Escalation Action Type Enum\r\n */\r\nexport enum EscalationActionType {\r\n  NOTIFY = 'NOTIFY',\r\n  CREATE_TICKET = 'CREATE_TICKET',\r\n  ALERT_ONCALL = 'ALERT_ONCALL',\r\n  ACTIVATE_TEAM = 'ACTIVATE_TEAM',\r\n  EMERGENCY_PROCEDURE = 'EMERGENCY_PROCEDURE',\r\n  CUSTOM = 'CUSTOM',\r\n}\r\n\r\n/**\r\n * Response Timeline Interface\r\n */\r\nexport interface ResponseTimeline {\r\n  detection: Date;\r\n  acknowledgment?: Date;\r\n  containment?: Date;\r\n  eradication?: Date;\r\n  recovery?: Date;\r\n  postIncident?: Date;\r\n  estimatedResolution?: Date;\r\n}\r\n\r\n/**\r\n * Stakeholder Interface\r\n */\r\nexport interface Stakeholder {\r\n  id: string;\r\n  name: string;\r\n  role: string;\r\n  contactInfo: ContactInfo;\r\n  responsibilities: string[];\r\n  escalationLevel: number;\r\n}\r\n\r\n/**\r\n * Contact Info Interface\r\n */\r\nexport interface ContactInfo {\r\n  email: string;\r\n  phone?: string;\r\n  slack?: string;\r\n  teams?: string;\r\n}\r\n\r\n/**\r\n * Communication Plan Interface\r\n */\r\nexport interface CommunicationPlan {\r\n  channels: CommunicationChannel[];\r\n  frequency: CommunicationFrequency;\r\n  templates: Record<string, string>;\r\n  stakeholderMatrix: Record<string, string[]>;\r\n}\r\n\r\n/**\r\n * Communication Channel Interface\r\n */\r\nexport interface CommunicationChannel {\r\n  type: ChannelType;\r\n  configuration: Record<string, any>;\r\n  priority: number;\r\n  enabled: boolean;\r\n}\r\n\r\n/**\r\n * Channel Type Enum\r\n */\r\nexport enum ChannelType {\r\n  EMAIL = 'EMAIL',\r\n  SLACK = 'SLACK',\r\n  TEAMS = 'TEAMS',\r\n  SMS = 'SMS',\r\n  WEBHOOK = 'WEBHOOK',\r\n  DASHBOARD = 'DASHBOARD',\r\n}\r\n\r\n/**\r\n * Communication Frequency Enum\r\n */\r\nexport enum CommunicationFrequency {\r\n  IMMEDIATE = 'IMMEDIATE',\r\n  HOURLY = 'HOURLY',\r\n  DAILY = 'DAILY',\r\n  ON_CHANGE = 'ON_CHANGE',\r\n  CUSTOM = 'CUSTOM',\r\n}\r\n\r\n/**\r\n * Rollback Plan Interface\r\n */\r\nexport interface RollbackPlan {\r\n  steps: RollbackStep[];\r\n  triggers: RollbackTrigger[];\r\n  approvalRequired: boolean;\r\n  estimatedTime: number;\r\n  risks: string[];\r\n}\r\n\r\n/**\r\n * Rollback Step Interface\r\n */\r\nexport interface RollbackStep {\r\n  id: string;\r\n  description: string;\r\n  order: number;\r\n  type: RollbackStepType;\r\n  parameters: Record<string, any>;\r\n  dependencies: string[];\r\n  timeout: number;\r\n}\r\n\r\n/**\r\n * Rollback Step Type Enum\r\n */\r\nexport enum RollbackStepType {\r\n  CONFIGURATION_RESTORE = 'CONFIGURATION_RESTORE',\r\n  SERVICE_RESTART = 'SERVICE_RESTART',\r\n  RULE_REMOVAL = 'RULE_REMOVAL',\r\n  POLICY_REVERT = 'POLICY_REVERT',\r\n  NETWORK_RESTORE = 'NETWORK_RESTORE',\r\n  DATA_RESTORE = 'DATA_RESTORE',\r\n  CUSTOM = 'CUSTOM',\r\n}\r\n\r\n/**\r\n * Rollback Trigger Interface\r\n */\r\nexport interface RollbackTrigger {\r\n  condition: string;\r\n  automatic: boolean;\r\n  timeout?: number;\r\n}\r\n\r\n/**\r\n * Retry Policy Interface\r\n */\r\nexport interface RetryPolicy {\r\n  maxAttempts: number;\r\n  initialDelay: number;\r\n  backoffMultiplier: number;\r\n  maxDelay: number;\r\n  retryableErrors: string[];\r\n}\r\n\r\n/**\r\n * Security Orchestrator Service\r\n * \r\n * Comprehensive application service that coordinates complex security workflows.\r\n * Implements sophisticated orchestration logic for event processing, threat detection,\r\n * vulnerability management, and automated response execution.\r\n * \r\n * Key responsibilities:\r\n * - Coordinate security event processing pipeline\r\n * - Orchestrate threat detection and analysis\r\n * - Manage vulnerability scanning workflows\r\n * - Execute automated response actions\r\n * - Perform threat hunting operations\r\n * - Handle incident response coordination\r\n * - Manage cross-cutting concerns (transactions, logging, metrics)\r\n */\r\n@Injectable()\r\nexport class SecurityOrchestratorService extends BaseService<SecurityOrchestrationContext> {\r\n  constructor(\r\n    private readonly eventRepository: EventRepository,\r\n    private readonly threatRepository: ThreatRepository,\r\n    private readonly vulnerabilityRepository: VulnerabilityRepository,\r\n    private readonly responseActionRepository: ResponseActionRepository,\r\n    private readonly eventProcessor: EventProcessor,\r\n    private readonly threatDetector: ThreatDetector,\r\n    private readonly vulnerabilityScanner: VulnerabilityScanner,\r\n    private readonly responseExecutor: ResponseExecutor,\r\n  ) {\r\n    super('SecurityOrchestratorService');\r\n  }\r\n\r\n  /**\r\n   * Orchestrate comprehensive security event processing\r\n   * Main entry point for security workflow coordination\r\n   */\r\n  async orchestrateSecurityWorkflow(\r\n    events: Event[],\r\n    context?: Partial<SecurityOrchestrationContext>\r\n  ): Promise<ServiceResult<SecurityOrchestrationResult>> {\r\n    const orchestrationContext = this.createContext({\r\n      priority: ResponsePriority.MEDIUM,\r\n      mode: ResponseMode.AUTOMATIC,\r\n      timeoutMs: 300000, // 5 minutes\r\n      autoResponse: true,\r\n      enableThreatHunting: true,\r\n      enableVulnerabilityScanning: true,\r\n      workflowConfig: this.getDefaultWorkflowConfig(),\r\n      ...context,\r\n    });\r\n\r\n    return this.executeOperation(\r\n      async () => this.performSecurityOrchestration(events, orchestrationContext),\r\n      orchestrationContext,\r\n      'orchestrateSecurityWorkflow'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Process security events through the complete pipeline\r\n   */\r\n  async processSecurityEvents(\r\n    events: Event[],\r\n    context?: Partial<SecurityOrchestrationContext>\r\n  ): Promise<ServiceResult<SecurityOrchestrationResult>> {\r\n    const processingContext = this.createContext({\r\n      priority: ResponsePriority.HIGH,\r\n      mode: ResponseMode.AUTOMATIC,\r\n      timeoutMs: 180000, // 3 minutes\r\n      workflowConfig: this.getDefaultWorkflowConfig(),\r\n      ...context,\r\n    });\r\n\r\n    return this.executeOperation(\r\n      async () => this.executeEventProcessing(events, processingContext),\r\n      processingContext,\r\n      'processSecurityEvents'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Perform comprehensive threat hunting operation\r\n   */\r\n  async performThreatHunting(\r\n    criteria: ThreatHuntingCriteria,\r\n    context?: Partial<SecurityOrchestrationContext>\r\n  ): Promise<ServiceResult<ThreatHuntingResult>> {\r\n    const huntingContext = this.createContext({\r\n      priority: ResponsePriority.MEDIUM,\r\n      mode: ResponseMode.MANUAL,\r\n      timeoutMs: 600000, // 10 minutes\r\n      enableThreatHunting: true,\r\n      ...context,\r\n    });\r\n\r\n    return this.executeOperation(\r\n      async () => this.executeThreatHunting(criteria, huntingContext),\r\n      huntingContext,\r\n      'performThreatHunting'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Execute incident response plan\r\n   */\r\n  async executeIncidentResponse(\r\n    incidentResponsePlan: IncidentResponsePlan,\r\n    context?: Partial<SecurityOrchestrationContext>\r\n  ): Promise<ServiceResult<ResponseResult>> {\r\n    const responseContext = this.createContext({\r\n      priority: ResponsePriority.CRITICAL,\r\n      mode: ResponseMode.SEMI_AUTOMATIC,\r\n      timeoutMs: 1800000, // 30 minutes\r\n      autoResponse: false,\r\n      ...context,\r\n    });\r\n\r\n    return this.executeOperation(\r\n      async () => this.executeIncidentResponsePlan(incidentResponsePlan, responseContext),\r\n      responseContext,\r\n      'executeIncidentResponse'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Analyze security posture and generate recommendations\r\n   */\r\n  async analyzeSecurityPosture(\r\n    timeRange: { start: Date; end: Date },\r\n    context?: Partial<SecurityOrchestrationContext>\r\n  ): Promise<ServiceResult<SecurityPostureAnalysis>> {\r\n    const analysisContext = this.createContext({\r\n      priority: ResponsePriority.LOW,\r\n      mode: ResponseMode.MANUAL,\r\n      timeoutMs: 900000, // 15 minutes\r\n      ...context,\r\n    });\r\n\r\n    return this.executeOperation(\r\n      async () => this.performSecurityPostureAnalysis(timeRange, analysisContext),\r\n      analysisContext,\r\n      'analyzeSecurityPosture'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Main orchestration logic implementation\r\n   */\r\n  private async performSecurityOrchestration(\r\n    events: Event[],\r\n    context: SecurityOrchestrationContext\r\n  ): Promise<SecurityOrchestrationResult> {\r\n    const orchestrationId = this.generateCorrelationId();\r\n    const startTime = new Date();\r\n    const result: SecurityOrchestrationResult = {\r\n      success: true,\r\n      orchestrationId,\r\n      startTime,\r\n      endTime: new Date(),\r\n      duration: 0,\r\n      eventsProcessed: 0,\r\n      threatsDetected: 0,\r\n      vulnerabilitiesFound: 0,\r\n      actionsExecuted: 0,\r\n      correlationsCreated: 0,\r\n      enrichmentsPerformed: 0,\r\n      processingResults: [],\r\n      threatAnalyses: [],\r\n      vulnerabilityScans: [],\r\n      responseResults: [],\r\n      errors: [],\r\n      warnings: [],\r\n      metrics: this.initializeMetrics(),\r\n      recommendations: [],\r\n    };\r\n\r\n    try {\r\n      this.logger.log(`Starting security orchestration for ${events.length} events`, {\r\n        orchestrationId,\r\n        eventCount: events.length,\r\n        priority: context.priority,\r\n        mode: context.mode,\r\n      });\r\n\r\n      // Apply orchestration rules\r\n      const filteredEvents = await this.applyOrchestrationRules(events, context);\r\n      \r\n      // Process events in batches\r\n      const batchSize = context.workflowConfig?.batchSize || 10;\r\n      const batches = this.createBatches(filteredEvents, batchSize);\r\n\r\n      for (const batch of batches) {\r\n        await this.processBatch(batch, context, result);\r\n      }\r\n\r\n      // Perform correlation analysis\r\n      if (context.workflowConfig?.enableCorrelation) {\r\n        await this.performCorrelationAnalysis(filteredEvents, context, result);\r\n      }\r\n\r\n      // Generate recommendations\r\n      result.recommendations = await this.generateRecommendations(result, context);\r\n\r\n      result.endTime = new Date();\r\n      result.duration = result.endTime.getTime() - startTime.getTime();\r\n      result.metrics = await this.calculateMetrics(result, context);\r\n\r\n      this.logger.log(`Security orchestration completed`, {\r\n        orchestrationId,\r\n        duration: result.duration,\r\n        eventsProcessed: result.eventsProcessed,\r\n        threatsDetected: result.threatsDetected,\r\n        success: result.success,\r\n      });\r\n\r\n      return result;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Security orchestration failed`, {\r\n        orchestrationId,\r\n        error: error.message,\r\n        stack: error.stack,\r\n      });\r\n\r\n      result.success = false;\r\n      result.errors.push({\r\n        stage: OrchestrationStage.INITIALIZATION,\r\n        errorCode: 'ORCHESTRATION_FAILED',\r\n        message: error.message,\r\n        timestamp: new Date(),\r\n        recoverable: false,\r\n      });\r\n\r\n      return result;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Process a batch of events\r\n   */\r\n  private async processBatch(\r\n    events: Event[],\r\n    context: SecurityOrchestrationContext,\r\n    result: SecurityOrchestrationResult\r\n  ): Promise<void> {\r\n    const batchPromises = events.map(async (event) => {\r\n      try {\r\n        // Process event through pipeline\r\n        if (context.workflowConfig?.enableEventProcessing) {\r\n          const processingResult = await this.processEvent(event, context);\r\n          result.processingResults.push(processingResult);\r\n          result.eventsProcessed++;\r\n        }\r\n\r\n        // Threat detection\r\n        if (context.workflowConfig?.enableThreatDetection) {\r\n          const threatAnalysis = await this.detectThreats(event, context);\r\n          result.threatAnalyses.push(threatAnalysis);\r\n          result.threatsDetected += threatAnalysis.threats.length;\r\n        }\r\n\r\n        // Vulnerability scanning\r\n        if (context.workflowConfig?.enableVulnerabilityScanning && this.shouldScanForVulnerabilities(event)) {\r\n          const vulnerabilityScan = await this.scanForVulnerabilities(event, context);\r\n          result.vulnerabilityScans.push(vulnerabilityScan);\r\n          result.vulnerabilitiesFound += vulnerabilityScan.vulnerabilities.length;\r\n        }\r\n\r\n        // Automated response\r\n        if (context.autoResponse && context.workflowConfig?.enableResponseExecution) {\r\n          const responseResult = await this.executeAutomatedResponse(event, context, result);\r\n          if (responseResult) {\r\n            result.responseResults.push(responseResult);\r\n            result.actionsExecuted += responseResult.actionsExecuted;\r\n          }\r\n        }\r\n\r\n      } catch (error) {\r\n        this.logger.error(`Error processing event ${event.id}`, {\r\n          eventId: event.id.toString(),\r\n          error: error.message,\r\n        });\r\n\r\n        result.errors.push({\r\n          stage: OrchestrationStage.EVENT_PROCESSING,\r\n          eventId: event.id.toString(),\r\n          errorCode: 'EVENT_PROCESSING_FAILED',\r\n          message: error.message,\r\n          timestamp: new Date(),\r\n          recoverable: true,\r\n        });\r\n      }\r\n    });\r\n\r\n    await Promise.allSettled(batchPromises);\r\n  }\r\n\r\n  /**\r\n   * Process individual event through pipeline\r\n   */\r\n  private async processEvent(\r\n    event: Event,\r\n    context: SecurityOrchestrationContext\r\n  ): Promise<ProcessingResult> {\r\n    const processingContext: ProcessingContext = {\r\n      requestId: this.generateCorrelationId(),\r\n      startTime: new Date(),\r\n      config: {\r\n        enableNormalization: true,\r\n        enableEnrichment: true,\r\n        enableCorrelation: true,\r\n        enableThreatAnalysis: true,\r\n        timeoutMs: context.timeoutMs || 60000,\r\n        maxRetries: 3,\r\n        batchSize: 1,\r\n      },\r\n      userContext: {\r\n        userId: context.userId || 'system',\r\n        tenantId: context.tenantId || 'default',\r\n        permissions: ['security:process'],\r\n      },\r\n      correlationId: context.correlationId,\r\n    };\r\n\r\n    return this.eventProcessor.processEvent(event, processingContext);\r\n  }\r\n\r\n  /**\r\n   * Detect threats for an event\r\n   */\r\n  private async detectThreats(\r\n    event: Event,\r\n    context: SecurityOrchestrationContext\r\n  ): Promise<ThreatAnalysis> {\r\n    const detectionContext: ThreatDetectionContext = {\r\n      requestId: this.generateCorrelationId(),\r\n      config: {\r\n        enableRealTimeDetection: true,\r\n        sensitivity: 80,\r\n        minConfidenceThreshold: 70,\r\n        maxAnalysisTimeMs: 30000,\r\n        enableMLModels: true,\r\n        enableBehavioralAnalysis: true,\r\n        enableThreatIntel: true,\r\n      },\r\n      userContext: {\r\n        userId: context.userId || 'system',\r\n        tenantId: context.tenantId || 'default',\r\n      },\r\n    };\r\n\r\n    return this.threatDetector.analyzeEvent(event, detectionContext);\r\n  }\r\n\r\n  /**\r\n   * Scan for vulnerabilities\r\n   */\r\n  private async scanForVulnerabilities(\r\n    event: Event,\r\n    context: SecurityOrchestrationContext\r\n  ): Promise<VulnerabilityScan> {\r\n    const scannerContext: VulnerabilityScannerContext = {\r\n      requestId: this.generateCorrelationId(),\r\n      config: {\r\n        scanType: 'QUICK' as any,\r\n        includeLowSeverity: false,\r\n        timeoutMs: 60000,\r\n        maxConcurrentScans: 5,\r\n      },\r\n      userContext: {\r\n        userId: context.userId || 'system',\r\n        tenantId: context.tenantId || 'default',\r\n      },\r\n    };\r\n\r\n    return this.vulnerabilityScanner.scanEvent(event, scannerContext);\r\n  }\r\n\r\n  /**\r\n   * Execute automated response\r\n   */\r\n  private async executeAutomatedResponse(\r\n    event: Event,\r\n    context: SecurityOrchestrationContext,\r\n    orchestrationResult: SecurityOrchestrationResult\r\n  ): Promise<ResponseResult | null> {\r\n    // Determine if response is needed\r\n    const threatAnalysis = orchestrationResult.threatAnalyses.find(ta => \r\n      ta.threats.some(t => t.id === event.id.toString())\r\n    );\r\n    const vulnerabilityScan = orchestrationResult.vulnerabilityScans.find(vs => \r\n      vs.target.id === event.id.toString()\r\n    );\r\n\r\n    if (!this.shouldExecuteResponse(event, threatAnalysis, vulnerabilityScan)) {\r\n      return null;\r\n    }\r\n\r\n    const responseContext: ResponseContext = {\r\n      threats: threatAnalysis?.threats || [],\r\n      vulnerabilities: vulnerabilityScan?.vulnerabilities || [],\r\n      event,\r\n      priority: this.determineResponsePriority(event, threatAnalysis, vulnerabilityScan),\r\n      mode: context.mode || ResponseMode.AUTOMATIC,\r\n      userContext: {\r\n        userId: context.userId || 'system',\r\n        tenantId: context.tenantId || 'default',\r\n        permissions: ['security:respond'],\r\n      },\r\n      constraints: {\r\n        maxExecutionTimeMs: 120000,\r\n        maxConcurrentActions: 3,\r\n        requiresApproval: false,\r\n        requiresRollback: true,\r\n      },\r\n      correlationId: context.correlationId,\r\n    };\r\n\r\n    return this.responseExecutor.executeResponse(event, responseContext);\r\n  }\r\n\r\n  /**\r\n   * Perform correlation analysis\r\n   */\r\n  private async performCorrelationAnalysis(\r\n    events: Event[],\r\n    context: SecurityOrchestrationContext,\r\n    result: SecurityOrchestrationResult\r\n  ): Promise<void> {\r\n    try {\r\n      // Find events suitable for correlation\r\n      const correlationCandidates = await this.eventRepository.findEventsForCorrelation(\r\n        3600000, // 1 hour window\r\n        undefined, // all event types\r\n        EventSeverity.MEDIUM // minimum severity\r\n      );\r\n\r\n      // Perform correlation logic\r\n      const correlations = await this.detectEventCorrelations(correlationCandidates);\r\n      result.correlationsCreated = correlations.length;\r\n\r\n      this.logger.debug(`Created ${correlations.length} event correlations`);\r\n\r\n    } catch (error) {\r\n      this.logger.error('Error in correlation analysis', { error: error.message });\r\n      result.warnings.push(`Correlation analysis failed: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute threat hunting operation\r\n   */\r\n  private async executeThreatHunting(\r\n    criteria: ThreatHuntingCriteria,\r\n    context: SecurityOrchestrationContext\r\n  ): Promise<ThreatHuntingResult> {\r\n    const huntId = this.generateCorrelationId();\r\n    const startTime = new Date();\r\n\r\n    try {\r\n      // Retrieve events based on criteria\r\n      const events = await this.findEventsForHunting(criteria);\r\n      \r\n      // Analyze events for threats\r\n      const threats = [];\r\n      const threatAnalyses = await Promise.all(\r\n        events.map(event => this.detectThreats(event, context))\r\n      );\r\n\r\n      for (const analysis of threatAnalyses) {\r\n        threats.push(...analysis.threats);\r\n      }\r\n\r\n      // Detect patterns\r\n      const patterns = await this.detectThreatPatterns(events, threats);\r\n\r\n      // Create correlations if enabled\r\n      const correlations = criteria.includeCorrelation \r\n        ? await this.detectEventCorrelations(events)\r\n        : [];\r\n\r\n      const endTime = new Date();\r\n      const result: ThreatHuntingResult = {\r\n        huntId,\r\n        criteria,\r\n        startTime,\r\n        endTime,\r\n        duration: endTime.getTime() - startTime.getTime(),\r\n        eventsAnalyzed: events.length,\r\n        threatsFound: threats.length,\r\n        patternsDetected: patterns.length,\r\n        events,\r\n        threats,\r\n        patterns,\r\n        correlations,\r\n        recommendations: await this.generateThreatHuntingRecommendations(threats, patterns),\r\n        confidence: this.calculateHuntingConfidence(threats, patterns),\r\n      };\r\n\r\n      this.logger.log(`Threat hunting completed`, {\r\n        huntId,\r\n        eventsAnalyzed: events.length,\r\n        threatsFound: threats.length,\r\n        patternsDetected: patterns.length,\r\n      });\r\n\r\n      return result;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Threat hunting failed`, {\r\n        huntId,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute incident response plan\r\n   */\r\n  private async executeIncidentResponsePlan(\r\n    plan: IncidentResponsePlan,\r\n    context: SecurityOrchestrationContext\r\n  ): Promise<ResponseResult> {\r\n    const executionId = this.generateCorrelationId();\r\n    const startTime = new Date();\r\n\r\n    try {\r\n      this.logger.log(`Executing incident response plan`, {\r\n        incidentId: plan.incidentId,\r\n        executionId,\r\n        actionCount: plan.actions.length,\r\n      });\r\n\r\n      // Sort actions by priority\r\n      const sortedActions = plan.actions.sort((a, b) => b.priority - a.priority);\r\n\r\n      // Execute actions\r\n      const executedActions = [];\r\n      let actionsExecuted = 0;\r\n      let actionsFailed = 0;\r\n\r\n      for (const action of sortedActions) {\r\n        try {\r\n          const actionResult = await this.responseExecutor.executeAction(\r\n            action.type,\r\n            action.parameters\r\n          );\r\n\r\n          executedActions.push(actionResult);\r\n          \r\n          if (actionResult.status === ActionStatus.COMPLETED) {\r\n            actionsExecuted++;\r\n          } else {\r\n            actionsFailed++;\r\n          }\r\n\r\n        } catch (error) {\r\n          this.logger.error(`Action execution failed`, {\r\n            actionId: action.id,\r\n            actionType: action.type,\r\n            error: error.message,\r\n          });\r\n          actionsFailed++;\r\n        }\r\n      }\r\n\r\n      // Check escalation rules\r\n      let escalations = 0;\r\n      for (const rule of plan.escalationRules) {\r\n        if (await this.evaluateEscalationRule(rule, plan, context)) {\r\n          await this.executeEscalation(rule.action, context);\r\n          escalations++;\r\n        }\r\n      }\r\n\r\n      const endTime = new Date();\r\n      const result: ResponseResult = {\r\n        success: actionsFailed === 0,\r\n        executionId,\r\n        actionsExecuted,\r\n        actionsFailed,\r\n        actionsSkipped: 0,\r\n        executionTime: endTime.getTime() - startTime.getTime(),\r\n        executedActions,\r\n        effectivenessScore: this.calculateEffectivenessScore(actionsExecuted, actionsFailed),\r\n        impact: await this.assessResponseImpact(executedActions),\r\n        errors: [],\r\n        warnings: [],\r\n        recommendations: [],\r\n      };\r\n\r\n      this.logger.log(`Incident response completed`, {\r\n        incidentId: plan.incidentId,\r\n        executionId,\r\n        actionsExecuted,\r\n        actionsFailed,\r\n        escalations,\r\n      });\r\n\r\n      return result;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Incident response execution failed`, {\r\n        incidentId: plan.incidentId,\r\n        executionId,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Perform security posture analysis\r\n   */\r\n  private async performSecurityPostureAnalysis(\r\n    timeRange: { start: Date; end: Date },\r\n    context: SecurityOrchestrationContext\r\n  ): Promise<SecurityPostureAnalysis> {\r\n    try {\r\n      // Get comprehensive statistics\r\n      const [eventStats, threatStats, vulnerabilityStats] = await Promise.all([\r\n        this.eventRepository.getEventStatistics(timeRange.start, timeRange.end),\r\n        this.threatRepository.getThreatStatistics(timeRange.start, timeRange.end),\r\n        this.vulnerabilityRepository.getVulnerabilityStatistics(timeRange.start, timeRange.end),\r\n      ]);\r\n\r\n      // Calculate security scores\r\n      const securityScore = this.calculateSecurityScore(eventStats, threatStats, vulnerabilityStats);\r\n      const riskLevel = this.determineRiskLevel(securityScore);\r\n      const trends = await this.analyzeTrends(timeRange);\r\n      const recommendations = await this.generateSecurityRecommendations(\r\n        eventStats, \r\n        threatStats, \r\n        vulnerabilityStats\r\n      );\r\n\r\n      return {\r\n        timeRange,\r\n        securityScore,\r\n        riskLevel,\r\n        eventStatistics: eventStats,\r\n        threatStatistics: threatStats,\r\n        vulnerabilityStatistics: vulnerabilityStats,\r\n        trends,\r\n        recommendations,\r\n        generatedAt: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error('Security posture analysis failed', { error: error.message });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Helper methods\r\n\r\n  private getDefaultWorkflowConfig(): WorkflowConfiguration {\r\n    return {\r\n      enableEventProcessing: true,\r\n      enableThreatDetection: true,\r\n      enableVulnerabilityScanning: true,\r\n      enableResponseExecution: true,\r\n      enableCorrelation: true,\r\n      enableEnrichment: true,\r\n      batchSize: 10,\r\n      maxConcurrentOperations: 5,\r\n      retryAttempts: 3,\r\n      timeoutMs: 300000,\r\n    };\r\n  }\r\n\r\n  private async applyOrchestrationRules(\r\n    events: Event[],\r\n    context: SecurityOrchestrationContext\r\n  ): Promise<Event[]> {\r\n    if (!context.customRules || context.customRules.length === 0) {\r\n      return events;\r\n    }\r\n\r\n    const filteredEvents = [];\r\n    for (const event of events) {\r\n      let shouldProcess = true;\r\n      \r\n      for (const rule of context.customRules) {\r\n        if (rule.enabled && this.evaluateOrchestrationRule(rule, event)) {\r\n          if (rule.action === OrchestrationAction.SKIP_PROCESSING) {\r\n            shouldProcess = false;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      if (shouldProcess) {\r\n        filteredEvents.push(event);\r\n      }\r\n    }\r\n\r\n    return filteredEvents;\r\n  }\r\n\r\n  private evaluateOrchestrationRule(rule: OrchestrationRule, event: Event): boolean {\r\n    const condition = rule.condition;\r\n    \r\n    // Check event types\r\n    if (condition.eventTypes && !condition.eventTypes.includes(event.type)) {\r\n      return false;\r\n    }\r\n\r\n    // Check severity levels\r\n    if (condition.severityLevels && !condition.severityLevels.includes(event.severity)) {\r\n      return false;\r\n    }\r\n\r\n    // Check risk score threshold\r\n    if (condition.riskScoreThreshold && event.riskScore < condition.riskScoreThreshold) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  private createBatches<T>(items: T[], batchSize: number): T[][] {\r\n    const batches = [];\r\n    for (let i = 0; i < items.length; i += batchSize) {\r\n      batches.push(items.slice(i, i + batchSize));\r\n    }\r\n    return batches;\r\n  }\r\n\r\n  private shouldScanForVulnerabilities(event: Event): boolean {\r\n    const vulnerabilityEventTypes = [\r\n      EventType.VULNERABILITY_DETECTED,\r\n      EventType.SYSTEM_STARTUP,\r\n      EventType.CONFIGURATION_CHANGE,\r\n      EventType.PATCH_APPLIED,\r\n    ];\r\n\r\n    return vulnerabilityEventTypes.includes(event.type);\r\n  }\r\n\r\n  private shouldExecuteResponse(\r\n    event: Event,\r\n    threatAnalysis?: ThreatAnalysis,\r\n    vulnerabilityScan?: VulnerabilityScan\r\n  ): boolean {\r\n    return (\r\n      event.severity >= EventSeverity.HIGH ||\r\n      (threatAnalysis && threatAnalysis.threats.length > 0) ||\r\n      (vulnerabilityScan && vulnerabilityScan.vulnerabilities.length > 0)\r\n    );\r\n  }\r\n\r\n  private determineResponsePriority(\r\n    event: Event,\r\n    threatAnalysis?: ThreatAnalysis,\r\n    vulnerabilityScan?: VulnerabilityScan\r\n  ): ResponsePriority {\r\n    if (event.severity === EventSeverity.CRITICAL) {\r\n      return ResponsePriority.CRITICAL;\r\n    }\r\n\r\n    if (threatAnalysis?.threats.some(t => t.severity === EventSeverity.CRITICAL)) {\r\n      return ResponsePriority.CRITICAL;\r\n    }\r\n\r\n    if (vulnerabilityScan?.vulnerabilities.some(v => v.severity === VulnerabilitySeverity.CRITICAL)) {\r\n      return ResponsePriority.CRITICAL;\r\n    }\r\n\r\n    if (event.severity === EventSeverity.HIGH) {\r\n      return ResponsePriority.HIGH;\r\n    }\r\n\r\n    return ResponsePriority.MEDIUM;\r\n  }\r\n\r\n  private async findEventsForHunting(criteria: ThreatHuntingCriteria): Promise<Event[]> {\r\n    // Implementation would use repository methods to find events based on criteria\r\n    return this.eventRepository.findByTimeRange(\r\n      criteria.timeRange.start,\r\n      criteria.timeRange.end,\r\n      criteria.maxResults\r\n    );\r\n  }\r\n\r\n  private async detectThreatPatterns(events: Event[], threats: any[]): Promise<ThreatPattern[]> {\r\n    const patterns: ThreatPattern[] = [];\r\n\r\n    // Frequency analysis\r\n    const sourceFrequency = new Map<string, number>();\r\n    events.forEach(event => {\r\n      const source = event.source.toString();\r\n      sourceFrequency.set(source, (sourceFrequency.get(source) || 0) + 1);\r\n    });\r\n\r\n    for (const [source, frequency] of sourceFrequency.entries()) {\r\n      if (frequency > 10) {\r\n        patterns.push({\r\n          id: UniqueEntityId.generate().toString(),\r\n          type: ThreatPatternType.FREQUENCY_ANOMALY,\r\n          name: 'High Frequency Source',\r\n          description: `High frequency of events from source: ${source}`,\r\n          confidence: Math.min(frequency / 20 * 100, 100),\r\n          severity: frequency > 50 ? ThreatSeverity.HIGH : ThreatSeverity.MEDIUM,\r\n          indicators: [source],\r\n          events: events.filter(e => e.source.toString() === source).map(e => e.id.toString()),\r\n          timeline: {\r\n            start: events[0].timestamp,\r\n            end: events[events.length - 1].timestamp,\r\n          },\r\n          mitreTechniques: [],\r\n          recommendations: [`Investigate source ${source} for potential compromise`],\r\n        });\r\n      }\r\n    }\r\n\r\n    return patterns;\r\n  }\r\n\r\n  private async detectEventCorrelations(events: Event[]): Promise<EventCorrelation[]> {\r\n    const correlations: EventCorrelation[] = [];\r\n\r\n    // Simple temporal correlation\r\n    const timeWindow = 300000; // 5 minutes\r\n    const correlatedGroups = new Map<string, Event[]>();\r\n\r\n    events.forEach(event => {\r\n      const timeKey = Math.floor(event.timestamp.getTime() / timeWindow).toString();\r\n      if (!correlatedGroups.has(timeKey)) {\r\n        correlatedGroups.set(timeKey, []);\r\n      }\r\n      correlatedGroups.get(timeKey)!.push(event);\r\n    });\r\n\r\n    for (const [timeKey, groupEvents] of correlatedGroups.entries()) {\r\n      if (groupEvents.length > 1) {\r\n        correlations.push({\r\n          id: UniqueEntityId.generate().toString(),\r\n          type: CorrelationType.TEMPORAL,\r\n          events: groupEvents.map(e => e.id.toString()),\r\n          confidence: Math.min(groupEvents.length * 20, 100),\r\n          timeWindow,\r\n          description: `${groupEvents.length} events occurred within ${timeWindow/1000} seconds`,\r\n          significance: groupEvents.length > 5 ? CorrelationSignificance.HIGH : CorrelationSignificance.MEDIUM,\r\n        });\r\n      }\r\n    }\r\n\r\n    return correlations;\r\n  }\r\n\r\n  private async generateRecommendations(\r\n    result: SecurityOrchestrationResult,\r\n    context: SecurityOrchestrationContext\r\n  ): Promise<string[]> {\r\n    const recommendations: string[] = [];\r\n\r\n    if (result.threatsDetected > 0) {\r\n      recommendations.push(`${result.threatsDetected} threats detected - review and prioritize response actions`);\r\n    }\r\n\r\n    if (result.vulnerabilitiesFound > 0) {\r\n      recommendations.push(`${result.vulnerabilitiesFound} vulnerabilities found - schedule remediation activities`);\r\n    }\r\n\r\n    if (result.errors.length > 0) {\r\n      recommendations.push(`${result.errors.length} processing errors occurred - review system health and configuration`);\r\n    }\r\n\r\n    if (result.correlationsCreated > 0) {\r\n      recommendations.push(`${result.correlationsCreated} event correlations identified - investigate potential attack patterns`);\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  private async generateThreatHuntingRecommendations(threats: any[], patterns: ThreatPattern[]): Promise<string[]> {\r\n    const recommendations: string[] = [];\r\n\r\n    if (threats.length > 0) {\r\n      recommendations.push(`Investigate ${threats.length} identified threats for potential security incidents`);\r\n    }\r\n\r\n    if (patterns.length > 0) {\r\n      recommendations.push(`Analyze ${patterns.length} detected patterns for attack campaign indicators`);\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  private calculateHuntingConfidence(threats: any[], patterns: ThreatPattern[]): number {\r\n    if (threats.length === 0 && patterns.length === 0) {\r\n      return 0;\r\n    }\r\n\r\n    const threatConfidence = threats.reduce((sum, threat) => sum + (threat.confidence || 0), 0) / threats.length || 0;\r\n    const patternConfidence = patterns.reduce((sum, pattern) => sum + pattern.confidence, 0) / patterns.length || 0;\r\n\r\n    return (threatConfidence + patternConfidence) / 2;\r\n  }\r\n\r\n  private async evaluateEscalationRule(\r\n    rule: EscalationRule,\r\n    plan: IncidentResponsePlan,\r\n    context: SecurityOrchestrationContext\r\n  ): Promise<boolean> {\r\n    // Simple escalation logic - can be extended\r\n    switch (rule.condition.type) {\r\n      case EscalationTrigger.TIME_ELAPSED:\r\n        const elapsed = Date.now() - plan.timeline.detection.getTime();\r\n        return elapsed > (rule.condition.timeWindow || 3600000); // 1 hour default\r\n\r\n      case EscalationTrigger.SEVERITY_INCREASE:\r\n        return plan.severity === EventSeverity.CRITICAL;\r\n\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  private async executeEscalation(\r\n    action: EscalationAction,\r\n    context: SecurityOrchestrationContext\r\n  ): Promise<void> {\r\n    this.logger.log(`Executing escalation action: ${action.type}`, {\r\n      actionType: action.type,\r\n      recipients: action.recipients,\r\n    });\r\n\r\n    // Implementation would integrate with notification systems\r\n    // For now, just log the escalation\r\n  }\r\n\r\n  private calculateEffectivenessScore(actionsExecuted: number, actionsFailed: number): number {\r\n    const total = actionsExecuted + actionsFailed;\r\n    if (total === 0) return 0;\r\n    return (actionsExecuted / total) * 100;\r\n  }\r\n\r\n  private async assessResponseImpact(executedActions: any[]): Promise<any> {\r\n    // Implementation would assess the impact of executed actions\r\n    return {\r\n      threatMitigation: 'PARTIAL',\r\n      vulnerabilityRemediation: 'MINIMAL',\r\n      systemAvailability: 'NONE',\r\n      performanceImpact: 'LOW',\r\n      securityImprovement: 'MEDIUM',\r\n      businessContinuity: 'NONE',\r\n      complianceImpact: 'NONE',\r\n    };\r\n  }\r\n\r\n  private calculateSecurityScore(eventStats: any, threatStats: any, vulnerabilityStats: any): number {\r\n    // Simple scoring algorithm - can be enhanced\r\n    let score = 100;\r\n\r\n    // Deduct points for high-severity events\r\n    score -= (eventStats.eventsBySeverity?.CRITICAL || 0) * 10;\r\n    score -= (eventStats.eventsBySeverity?.HIGH || 0) * 5;\r\n\r\n    // Deduct points for active threats\r\n    score -= (threatStats.activeThreats || 0) * 8;\r\n\r\n    // Deduct points for critical vulnerabilities\r\n    score -= (vulnerabilityStats.vulnerabilitiesBySeverity?.CRITICAL || 0) * 12;\r\n\r\n    return Math.max(0, Math.min(100, score));\r\n  }\r\n\r\n  private determineRiskLevel(securityScore: number): SecurityRiskLevel {\r\n    if (securityScore >= 80) return SecurityRiskLevel.LOW;\r\n    if (securityScore >= 60) return SecurityRiskLevel.MEDIUM;\r\n    if (securityScore >= 40) return SecurityRiskLevel.HIGH;\r\n    return SecurityRiskLevel.CRITICAL;\r\n  }\r\n\r\n  private async analyzeTrends(timeRange: { start: Date; end: Date }): Promise<SecurityTrend[]> {\r\n    // Implementation would analyze trends over time\r\n    return [];\r\n  }\r\n\r\n  private async generateSecurityRecommendations(\r\n    eventStats: any,\r\n    threatStats: any,\r\n    vulnerabilityStats: any\r\n  ): Promise<string[]> {\r\n    const recommendations: string[] = [];\r\n\r\n    if (threatStats.criticalThreatCount > 0) {\r\n      recommendations.push(`Address ${threatStats.criticalThreatCount} critical threats immediately`);\r\n    }\r\n\r\n    if (vulnerabilityStats.criticalVulnerabilityCount > 0) {\r\n      recommendations.push(`Remediate ${vulnerabilityStats.criticalVulnerabilityCount} critical vulnerabilities`);\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  private initializeMetrics(): OrchestrationMetrics {\r\n    return {\r\n      totalProcessingTime: 0,\r\n      averageEventProcessingTime: 0,\r\n      threatDetectionTime: 0,\r\n      vulnerabilityScanningTime: 0,\r\n      responseExecutionTime: 0,\r\n      throughput: 0,\r\n      errorRate: 0,\r\n      successRate: 0,\r\n      resourceUtilization: {\r\n        cpuUsage: 0,\r\n        memoryUsage: 0,\r\n        networkUsage: 0,\r\n      },\r\n    };\r\n  }\r\n\r\n  private async calculateMetrics(\r\n    result: SecurityOrchestrationResult,\r\n    context: SecurityOrchestrationContext\r\n  ): Promise<OrchestrationMetrics> {\r\n    return {\r\n      totalProcessingTime: result.duration,\r\n      averageEventProcessingTime: result.eventsProcessed > 0 ? result.duration / result.eventsProcessed : 0,\r\n      threatDetectionTime: result.threatAnalyses.reduce((sum, ta) => sum + ta.analysisDurationMs, 0),\r\n      vulnerabilityScanningTime: result.vulnerabilityScans.reduce((sum, vs) => sum + vs.scanDuration, 0),\r\n      responseExecutionTime: result.responseResults.reduce((sum, rr) => sum + rr.executionTime, 0),\r\n      throughput: result.eventsProcessed / (result.duration / 1000), // events per second\r\n      errorRate: result.errors.length / Math.max(result.eventsProcessed, 1) * 100,\r\n      successRate: 100 - (result.errors.length / Math.max(result.eventsProcessed, 1) * 100),\r\n      resourceUtilization: {\r\n        cpuUsage: 0, // Would be measured from system metrics\r\n        memoryUsage: 0,\r\n        networkUsage: 0,\r\n      },\r\n    };\r\n  }\r\n}\r\n\r\n// Additional interfaces for security posture analysis\r\nexport interface SecurityPostureAnalysis {\r\n  timeRange: { start: Date; end: Date };\r\n  securityScore: number;\r\n  riskLevel: SecurityRiskLevel;\r\n  eventStatistics: any;\r\n  threatStatistics: any;\r\n  vulnerabilityStatistics: any;\r\n  trends: SecurityTrend[];\r\n  recommendations: string[];\r\n  generatedAt: Date;\r\n}\r\n\r\nexport enum SecurityRiskLevel {\r\n  LOW = 'LOW',\r\n  MEDIUM = 'MEDIUM',\r\n  HIGH = 'HIGH',\r\n  CRITICAL = 'CRITICAL',\r\n}\r\n\r\nexport interface SecurityTrend {\r\n  metric: string;\r\n  direction: 'INCREASING' | 'DECREASING' | 'STABLE';\r\n  change: number;\r\n  period: string;\r\n  significance: 'LOW' | 'MEDIUM' | 'HIGH';\r\n} "], "version": 3}