c4c3e056d5eae392a71af658d96c2983
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const common_1 = require("@nestjs/common");
const user_service_1 = require("./user.service");
const user_entity_1 = require("../../domain/entities/user.entity");
const role_entity_1 = require("../../domain/entities/role.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const password_service_1 = require("../../../../infrastructure/auth/services/password.service");
describe('UserService', () => {
    let service;
    let userRepository;
    let roleRepository;
    let loggerService;
    let auditService;
    let passwordService;
    const mockUserRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findOne: jest.fn(),
        find: jest.fn(),
        remove: jest.fn(),
        count: jest.fn(),
        createQueryBuilder: jest.fn(),
    };
    const mockRoleRepository = {
        findOne: jest.fn(),
        findByIds: jest.fn(),
    };
    const mockLoggerService = {
        debug: jest.fn(),
        log: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
    };
    const mockAuditService = {
        logUserAction: jest.fn(),
    };
    const mockPasswordService = {
        hashPassword: jest.fn(),
    };
    const mockUser = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        status: 'active',
        emailVerified: true,
        roles: [],
        roleNames: ['user'],
        permissions: ['users.read'],
        fullName: 'John Doe',
        canLogin: true,
        isLocked: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const mockRole = {
        id: 'role-123',
        name: 'user',
        displayName: 'User',
        isDefault: true,
        isActive: true,
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                user_service_1.UserService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(user_entity_1.User),
                    useValue: mockUserRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(role_entity_1.Role),
                    useValue: mockRoleRepository,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: mockLoggerService,
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: mockAuditService,
                },
                {
                    provide: password_service_1.PasswordService,
                    useValue: mockPasswordService,
                },
            ],
        }).compile();
        service = module.get(user_service_1.UserService);
        userRepository = module.get((0, typeorm_1.getRepositoryToken)(user_entity_1.User));
        roleRepository = module.get((0, typeorm_1.getRepositoryToken)(role_entity_1.Role));
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
        passwordService = module.get(password_service_1.PasswordService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('create', () => {
        it('should create a user successfully', async () => {
            const createData = {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                passwordHash: 'password123',
            };
            const createdBy = 'admin-123';
            mockUserRepository.findOne.mockResolvedValue(null); // No existing user
            mockPasswordService.hashPassword.mockResolvedValue('hashed-password');
            mockRoleRepository.findOne.mockResolvedValue(mockRole); // Default role
            mockUserRepository.create.mockReturnValue(mockUser);
            mockUserRepository.save.mockResolvedValue(mockUser);
            const result = await service.create(createData, createdBy);
            expect(mockUserRepository.findOne).toHaveBeenCalledWith({
                where: { email: createData.email.toLowerCase() },
            });
            expect(mockPasswordService.hashPassword).toHaveBeenCalledWith(createData.passwordHash);
            expect(mockUserRepository.create).toHaveBeenCalled();
            expect(mockUserRepository.save).toHaveBeenCalledWith(mockUser);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(createdBy, 'create', 'user', mockUser.id, expect.any(Object));
            expect(result).toEqual(mockUser);
        });
        it('should throw BadRequestException when required fields are missing', async () => {
            const createData = {
                email: '<EMAIL>',
                // Missing firstName and lastName
            };
            const createdBy = 'admin-123';
            await expect(service.create(createData, createdBy)).rejects.toThrow(common_1.BadRequestException);
        });
        it('should throw ConflictException when user already exists', async () => {
            const createData = {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                passwordHash: 'password123',
            };
            const createdBy = 'admin-123';
            mockUserRepository.findOne.mockResolvedValue(mockUser); // Existing user
            await expect(service.create(createData, createdBy)).rejects.toThrow(common_1.ConflictException);
        });
    });
    describe('findById', () => {
        it('should return user when found', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            mockUserRepository.findOne.mockResolvedValue(mockUser);
            const result = await service.findById(id);
            expect(mockUserRepository.findOne).toHaveBeenCalledWith({
                where: { id },
                relations: ['roles'],
            });
            expect(result).toEqual(mockUser);
        });
        it('should return null when user not found', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            mockUserRepository.findOne.mockResolvedValue(null);
            const result = await service.findById(id);
            expect(result).toBeNull();
            expect(mockLoggerService.warn).toHaveBeenCalledWith('User not found', { id });
        });
    });
    describe('findByEmail', () => {
        it('should return user when found by email', async () => {
            const email = '<EMAIL>';
            mockUserRepository.findOne.mockResolvedValue(mockUser);
            const result = await service.findByEmail(email);
            expect(mockUserRepository.findOne).toHaveBeenCalledWith({
                where: { email: email.toLowerCase() },
                relations: ['roles'],
            });
            expect(result).toEqual(mockUser);
        });
    });
    describe('update', () => {
        it('should update user successfully', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            const updateData = { firstName: 'Jane' };
            const updatedBy = 'admin-123';
            jest.spyOn(service, 'findById').mockResolvedValue(mockUser);
            mockUserRepository.save.mockResolvedValue({ ...mockUser, ...updateData });
            const result = await service.update(id, updateData, updatedBy);
            expect(service.findById).toHaveBeenCalledWith(id);
            expect(mockUserRepository.save).toHaveBeenCalled();
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(updatedBy, 'update', 'user', id, expect.any(Object));
            expect(result).toEqual(expect.objectContaining(updateData));
        });
        it('should throw NotFoundException when user not found', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            const updateData = { firstName: 'Jane' };
            const updatedBy = 'admin-123';
            jest.spyOn(service, 'findById').mockResolvedValue(null);
            await expect(service.update(id, updateData, updatedBy)).rejects.toThrow(common_1.NotFoundException);
        });
        it('should throw ConflictException when email already exists', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            const updateData = { email: '<EMAIL>' };
            const updatedBy = 'admin-123';
            const existingUser = { ...mockUser, id: 'different-id' };
            jest.spyOn(service, 'findById').mockResolvedValue(mockUser);
            jest.spyOn(service, 'findByEmail').mockResolvedValue(existingUser);
            await expect(service.update(id, updateData, updatedBy)).rejects.toThrow(common_1.ConflictException);
        });
    });
    describe('delete', () => {
        it('should delete user successfully', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            const deletedBy = 'admin-123';
            jest.spyOn(service, 'findById').mockResolvedValue(mockUser);
            mockUserRepository.remove.mockResolvedValue(mockUser);
            await service.delete(id, deletedBy);
            expect(service.findById).toHaveBeenCalledWith(id);
            expect(mockUserRepository.remove).toHaveBeenCalledWith(mockUser);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(deletedBy, 'delete', 'user', id, expect.any(Object));
        });
        it('should throw NotFoundException when user not found', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            const deletedBy = 'admin-123';
            jest.spyOn(service, 'findById').mockResolvedValue(null);
            await expect(service.delete(id, deletedBy)).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe('assignRoles', () => {
        it('should assign roles to user successfully', async () => {
            const userId = '123e4567-e89b-12d3-a456-426614174000';
            const roleIds = ['role-1', 'role-2'];
            const assignedBy = 'admin-123';
            const roles = [
                { id: 'role-1', name: 'admin' },
                { id: 'role-2', name: 'analyst' },
            ];
            jest.spyOn(service, 'findById').mockResolvedValue(mockUser);
            mockRoleRepository.findByIds.mockResolvedValue(roles);
            mockUserRepository.save.mockResolvedValue({
                ...mockUser,
                roles,
                roleNames: ['admin', 'analyst'],
            });
            const result = await service.assignRoles(userId, roleIds, assignedBy);
            expect(service.findById).toHaveBeenCalledWith(userId);
            expect(mockRoleRepository.findByIds).toHaveBeenCalledWith(roleIds);
            expect(mockUserRepository.save).toHaveBeenCalled();
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(assignedBy, 'assign_roles', 'user', userId, expect.any(Object));
            expect(result.roles).toEqual(roles);
        });
        it('should throw NotFoundException when user not found', async () => {
            const userId = '123e4567-e89b-12d3-a456-426614174000';
            const roleIds = ['role-1'];
            const assignedBy = 'admin-123';
            jest.spyOn(service, 'findById').mockResolvedValue(null);
            await expect(service.assignRoles(userId, roleIds, assignedBy)).rejects.toThrow(common_1.NotFoundException);
        });
        it('should throw BadRequestException when role not found', async () => {
            const userId = '123e4567-e89b-12d3-a456-426614174000';
            const roleIds = ['role-1', 'role-2'];
            const assignedBy = 'admin-123';
            jest.spyOn(service, 'findById').mockResolvedValue(mockUser);
            mockRoleRepository.findByIds.mockResolvedValue([{ id: 'role-1', name: 'admin' }]); // Only one role found
            await expect(service.assignRoles(userId, roleIds, assignedBy)).rejects.toThrow(common_1.BadRequestException);
        });
    });
    describe('getStatistics', () => {
        it('should return user statistics', async () => {
            mockUserRepository.count
                .mockResolvedValueOnce(100) // total
                .mockResolvedValueOnce(80) // active
                .mockResolvedValueOnce(10) // inactive
                .mockResolvedValueOnce(5) // suspended
                .mockResolvedValueOnce(5) // pending verification
                .mockResolvedValueOnce(90) // email verified
                .mockResolvedValueOnce(10); // email unverified
            const result = await service.getStatistics();
            expect(result).toEqual({
                total: 100,
                byStatus: {
                    active: 80,
                    inactive: 10,
                    suspended: 5,
                    pendingVerification: 5,
                },
                byEmailVerification: {
                    verified: 90,
                    unverified: 10,
                },
                timestamp: expect.any(String),
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFx1c2VyLW1hbmFnZW1lbnRcXGFwcGxpY2F0aW9uXFxzZXJ2aWNlc1xcdXNlci5zZXJ2aWNlLnNwZWMudHMiLCJtYXBwaW5ncyI6Ijs7QUFBQSw2Q0FBc0Q7QUFDdEQsNkNBQXFEO0FBRXJELDJDQUEyRjtBQUMzRixpREFBNkM7QUFDN0MsbUVBQXlEO0FBQ3pELG1FQUF5RDtBQUN6RCxzRkFBa0Y7QUFDbEYsMEZBQXNGO0FBQ3RGLGdHQUE0RjtBQUU1RixRQUFRLENBQUMsYUFBYSxFQUFFLEdBQUcsRUFBRTtJQUMzQixJQUFJLE9BQW9CLENBQUM7SUFDekIsSUFBSSxjQUFnQyxDQUFDO0lBQ3JDLElBQUksY0FBZ0MsQ0FBQztJQUNyQyxJQUFJLGFBQTRCLENBQUM7SUFDakMsSUFBSSxZQUEwQixDQUFDO0lBQy9CLElBQUksZUFBZ0MsQ0FBQztJQUVyQyxNQUFNLGtCQUFrQixHQUFHO1FBQ3pCLE1BQU0sRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2pCLElBQUksRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2YsT0FBTyxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDbEIsSUFBSSxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDZixNQUFNLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNqQixLQUFLLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNoQixrQkFBa0IsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO0tBQzlCLENBQUM7SUFFRixNQUFNLGtCQUFrQixHQUFHO1FBQ3pCLE9BQU8sRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2xCLFNBQVMsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO0tBQ3JCLENBQUM7SUFFRixNQUFNLGlCQUFpQixHQUFHO1FBQ3hCLEtBQUssRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2hCLEdBQUcsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ2QsSUFBSSxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7UUFDZixLQUFLLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtLQUNqQixDQUFDO0lBRUYsTUFBTSxnQkFBZ0IsR0FBRztRQUN2QixhQUFhLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtLQUN6QixDQUFDO0lBRUYsTUFBTSxtQkFBbUIsR0FBRztRQUMxQixZQUFZLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtLQUN4QixDQUFDO0lBRUYsTUFBTSxRQUFRLEdBQWtCO1FBQzlCLEVBQUUsRUFBRSxzQ0FBc0M7UUFDMUMsS0FBSyxFQUFFLGtCQUFrQjtRQUN6QixTQUFTLEVBQUUsTUFBTTtRQUNqQixRQUFRLEVBQUUsS0FBSztRQUNmLE1BQU0sRUFBRSxRQUFRO1FBQ2hCLGFBQWEsRUFBRSxJQUFJO1FBQ25CLEtBQUssRUFBRSxFQUFFO1FBQ1QsU0FBUyxFQUFFLENBQUMsTUFBTSxDQUFDO1FBQ25CLFdBQVcsRUFBRSxDQUFDLFlBQVksQ0FBQztRQUMzQixRQUFRLEVBQUUsVUFBVTtRQUNwQixRQUFRLEVBQUUsSUFBSTtRQUNkLFFBQVEsRUFBRSxLQUFLO1FBQ2YsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFO1FBQ3JCLFNBQVMsRUFBRSxJQUFJLElBQUksRUFBRTtLQUN0QixDQUFDO0lBRUYsTUFBTSxRQUFRLEdBQWtCO1FBQzlCLEVBQUUsRUFBRSxVQUFVO1FBQ2QsSUFBSSxFQUFFLE1BQU07UUFDWixXQUFXLEVBQUUsTUFBTTtRQUNuQixTQUFTLEVBQUUsSUFBSTtRQUNmLFFBQVEsRUFBRSxJQUFJO0tBQ2YsQ0FBQztJQUVGLFVBQVUsQ0FBQyxLQUFLLElBQUksRUFBRTtRQUNwQixNQUFNLE1BQU0sR0FBa0IsTUFBTSxjQUFJLENBQUMsbUJBQW1CLENBQUM7WUFDM0QsU0FBUyxFQUFFO2dCQUNULDBCQUFXO2dCQUNYO29CQUNFLE9BQU8sRUFBRSxJQUFBLDRCQUFrQixFQUFDLGtCQUFJLENBQUM7b0JBQ2pDLFFBQVEsRUFBRSxrQkFBa0I7aUJBQzdCO2dCQUNEO29CQUNFLE9BQU8sRUFBRSxJQUFBLDRCQUFrQixFQUFDLGtCQUFJLENBQUM7b0JBQ2pDLFFBQVEsRUFBRSxrQkFBa0I7aUJBQzdCO2dCQUNEO29CQUNFLE9BQU8sRUFBRSw4QkFBYTtvQkFDdEIsUUFBUSxFQUFFLGlCQUFpQjtpQkFDNUI7Z0JBQ0Q7b0JBQ0UsT0FBTyxFQUFFLDRCQUFZO29CQUNyQixRQUFRLEVBQUUsZ0JBQWdCO2lCQUMzQjtnQkFDRDtvQkFDRSxPQUFPLEVBQUUsa0NBQWU7b0JBQ3hCLFFBQVEsRUFBRSxtQkFBbUI7aUJBQzlCO2FBQ0Y7U0FDRixDQUFDLENBQUMsT0FBTyxFQUFFLENBQUM7UUFFYixPQUFPLEdBQUcsTUFBTSxDQUFDLEdBQUcsQ0FBYywwQkFBVyxDQUFDLENBQUM7UUFDL0MsY0FBYyxHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQW1CLElBQUEsNEJBQWtCLEVBQUMsa0JBQUksQ0FBQyxDQUFDLENBQUM7UUFDeEUsY0FBYyxHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQW1CLElBQUEsNEJBQWtCLEVBQUMsa0JBQUksQ0FBQyxDQUFDLENBQUM7UUFDeEUsYUFBYSxHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQWdCLDhCQUFhLENBQUMsQ0FBQztRQUN6RCxZQUFZLEdBQUcsTUFBTSxDQUFDLEdBQUcsQ0FBZSw0QkFBWSxDQUFDLENBQUM7UUFDdEQsZUFBZSxHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQWtCLGtDQUFlLENBQUMsQ0FBQztJQUNqRSxDQUFDLENBQUMsQ0FBQztJQUVILFNBQVMsQ0FBQyxHQUFHLEVBQUU7UUFDYixJQUFJLENBQUMsYUFBYSxFQUFFLENBQUM7SUFDdkIsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsUUFBUSxFQUFFLEdBQUcsRUFBRTtRQUN0QixFQUFFLENBQUMsbUNBQW1DLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDakQsTUFBTSxVQUFVLEdBQUc7Z0JBQ2pCLEtBQUssRUFBRSxrQkFBa0I7Z0JBQ3pCLFNBQVMsRUFBRSxNQUFNO2dCQUNqQixRQUFRLEVBQUUsS0FBSztnQkFDZixZQUFZLEVBQUUsYUFBYTthQUM1QixDQUFDO1lBQ0YsTUFBTSxTQUFTLEdBQUcsV0FBVyxDQUFDO1lBRTlCLGtCQUFrQixDQUFDLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLG1CQUFtQjtZQUN2RSxtQkFBbUIsQ0FBQyxZQUFZLENBQUMsaUJBQWlCLENBQUMsaUJBQWlCLENBQUMsQ0FBQztZQUN0RSxrQkFBa0IsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxlQUFlO1lBQ3ZFLGtCQUFrQixDQUFDLE1BQU0sQ0FBQyxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUM7WUFDcEQsa0JBQWtCLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBRXBELE1BQU0sTUFBTSxHQUFHLE1BQU0sT0FBTyxDQUFDLE1BQU0sQ0FBQyxVQUFVLEVBQUUsU0FBUyxDQUFDLENBQUM7WUFFM0QsTUFBTSxDQUFDLGtCQUFrQixDQUFDLE9BQU8sQ0FBQyxDQUFDLG9CQUFvQixDQUFDO2dCQUN0RCxLQUFLLEVBQUUsRUFBRSxLQUFLLEVBQUUsVUFBVSxDQUFDLEtBQUssQ0FBQyxXQUFXLEVBQUUsRUFBRTthQUNqRCxDQUFDLENBQUM7WUFDSCxNQUFNLENBQUMsbUJBQW1CLENBQUMsWUFBWSxDQUFDLENBQUMsb0JBQW9CLENBQUMsVUFBVSxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBQ3ZGLE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxNQUFNLENBQUMsQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDO1lBQ3JELE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsQ0FBQyxvQkFBb0IsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUMvRCxNQUFNLENBQUMsZ0JBQWdCLENBQUMsYUFBYSxDQUFDLENBQUMsb0JBQW9CLENBQ3pELFNBQVMsRUFDVCxRQUFRLEVBQ1IsTUFBTSxFQUNOLFFBQVEsQ0FBQyxFQUFFLEVBQ1gsTUFBTSxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FDbkIsQ0FBQztZQUNGLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLENBQUM7UUFDbkMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsbUVBQW1FLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDakYsTUFBTSxVQUFVLEdBQUc7Z0JBQ2pCLEtBQUssRUFBRSxrQkFBa0I7Z0JBQ3pCLGlDQUFpQzthQUNsQyxDQUFDO1lBQ0YsTUFBTSxTQUFTLEdBQUcsV0FBVyxDQUFDO1lBRTlCLE1BQU0sTUFBTSxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsVUFBVSxFQUFFLFNBQVMsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyw0QkFBbUIsQ0FBQyxDQUFDO1FBQzNGLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHlEQUF5RCxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3ZFLE1BQU0sVUFBVSxHQUFHO2dCQUNqQixLQUFLLEVBQUUsa0JBQWtCO2dCQUN6QixTQUFTLEVBQUUsTUFBTTtnQkFDakIsUUFBUSxFQUFFLEtBQUs7Z0JBQ2YsWUFBWSxFQUFFLGFBQWE7YUFDNUIsQ0FBQztZQUNGLE1BQU0sU0FBUyxHQUFHLFdBQVcsQ0FBQztZQUU5QixrQkFBa0IsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxnQkFBZ0I7WUFFeEUsTUFBTSxNQUFNLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxVQUFVLEVBQUUsU0FBUyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLDBCQUFpQixDQUFDLENBQUM7UUFDekYsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxVQUFVLEVBQUUsR0FBRyxFQUFFO1FBQ3hCLEVBQUUsQ0FBQywrQkFBK0IsRUFBRSxLQUFLLElBQUksRUFBRTtZQUM3QyxNQUFNLEVBQUUsR0FBRyxzQ0FBc0MsQ0FBQztZQUNsRCxrQkFBa0IsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsUUFBUSxDQUFDLENBQUM7WUFFdkQsTUFBTSxNQUFNLEdBQUcsTUFBTSxPQUFPLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDO1lBRTFDLE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxPQUFPLENBQUMsQ0FBQyxvQkFBb0IsQ0FBQztnQkFDdEQsS0FBSyxFQUFFLEVBQUUsRUFBRSxFQUFFO2dCQUNiLFNBQVMsRUFBRSxDQUFDLE9BQU8sQ0FBQzthQUNyQixDQUFDLENBQUM7WUFDSCxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ25DLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHdDQUF3QyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3RELE1BQU0sRUFBRSxHQUFHLHNDQUFzQyxDQUFDO1lBQ2xELGtCQUFrQixDQUFDLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUVuRCxNQUFNLE1BQU0sR0FBRyxNQUFNLE9BQU8sQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUM7WUFFMUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQzFCLE1BQU0sQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsQ0FBQyxvQkFBb0IsQ0FBQyxnQkFBZ0IsRUFBRSxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFDaEYsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxhQUFhLEVBQUUsR0FBRyxFQUFFO1FBQzNCLEVBQUUsQ0FBQyx3Q0FBd0MsRUFBRSxLQUFLLElBQUksRUFBRTtZQUN0RCxNQUFNLEtBQUssR0FBRyxrQkFBa0IsQ0FBQztZQUNqQyxrQkFBa0IsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsUUFBUSxDQUFDLENBQUM7WUFFdkQsTUFBTSxNQUFNLEdBQUcsTUFBTSxPQUFPLENBQUMsV0FBVyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBRWhELE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxPQUFPLENBQUMsQ0FBQyxvQkFBb0IsQ0FBQztnQkFDdEQsS0FBSyxFQUFFLEVBQUUsS0FBSyxFQUFFLEtBQUssQ0FBQyxXQUFXLEVBQUUsRUFBRTtnQkFDckMsU0FBUyxFQUFFLENBQUMsT0FBTyxDQUFDO2FBQ3JCLENBQUMsQ0FBQztZQUNILE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLENBQUM7UUFDbkMsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxRQUFRLEVBQUUsR0FBRyxFQUFFO1FBQ3RCLEVBQUUsQ0FBQyxpQ0FBaUMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUMvQyxNQUFNLEVBQUUsR0FBRyxzQ0FBc0MsQ0FBQztZQUNsRCxNQUFNLFVBQVUsR0FBRyxFQUFFLFNBQVMsRUFBRSxNQUFNLEVBQUUsQ0FBQztZQUN6QyxNQUFNLFNBQVMsR0FBRyxXQUFXLENBQUM7WUFFOUIsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsVUFBVSxDQUFDLENBQUMsaUJBQWlCLENBQUMsUUFBZ0IsQ0FBQyxDQUFDO1lBQ3BFLGtCQUFrQixDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxFQUFFLEdBQUcsUUFBUSxFQUFFLEdBQUcsVUFBVSxFQUFFLENBQUMsQ0FBQztZQUUxRSxNQUFNLE1BQU0sR0FBRyxNQUFNLE9BQU8sQ0FBQyxNQUFNLENBQUMsRUFBRSxFQUFFLFVBQVUsRUFBRSxTQUFTLENBQUMsQ0FBQztZQUUvRCxNQUFNLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxDQUFDLG9CQUFvQixDQUFDLEVBQUUsQ0FBQyxDQUFDO1lBQ2xELE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDO1lBQ25ELE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxhQUFhLENBQUMsQ0FBQyxvQkFBb0IsQ0FDekQsU0FBUyxFQUNULFFBQVEsRUFDUixNQUFNLEVBQ04sRUFBRSxFQUNGLE1BQU0sQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLENBQ25CLENBQUM7WUFDRixNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDO1FBQzlELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLG9EQUFvRCxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ2xFLE1BQU0sRUFBRSxHQUFHLHNDQUFzQyxDQUFDO1lBQ2xELE1BQU0sVUFBVSxHQUFHLEVBQUUsU0FBUyxFQUFFLE1BQU0sRUFBRSxDQUFDO1lBQ3pDLE1BQU0sU0FBUyxHQUFHLFdBQVcsQ0FBQztZQUU5QixJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sRUFBRSxVQUFVLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUV4RCxNQUFNLE1BQU0sQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLEVBQUUsRUFBRSxVQUFVLEVBQUUsU0FBUyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLDBCQUFpQixDQUFDLENBQUM7UUFDN0YsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsMERBQTBELEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDeEUsTUFBTSxFQUFFLEdBQUcsc0NBQXNDLENBQUM7WUFDbEQsTUFBTSxVQUFVLEdBQUcsRUFBRSxLQUFLLEVBQUUsc0JBQXNCLEVBQUUsQ0FBQztZQUNyRCxNQUFNLFNBQVMsR0FBRyxXQUFXLENBQUM7WUFFOUIsTUFBTSxZQUFZLEdBQUcsRUFBRSxHQUFHLFFBQVEsRUFBRSxFQUFFLEVBQUUsY0FBYyxFQUFFLENBQUM7WUFDekQsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsVUFBVSxDQUFDLENBQUMsaUJBQWlCLENBQUMsUUFBZ0IsQ0FBQyxDQUFDO1lBQ3BFLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxFQUFFLGFBQWEsQ0FBQyxDQUFDLGlCQUFpQixDQUFDLFlBQW9CLENBQUMsQ0FBQztZQUUzRSxNQUFNLE1BQU0sQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLEVBQUUsRUFBRSxVQUFVLEVBQUUsU0FBUyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLDBCQUFpQixDQUFDLENBQUM7UUFDN0YsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxRQUFRLEVBQUUsR0FBRyxFQUFFO1FBQ3RCLEVBQUUsQ0FBQyxpQ0FBaUMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUMvQyxNQUFNLEVBQUUsR0FBRyxzQ0FBc0MsQ0FBQztZQUNsRCxNQUFNLFNBQVMsR0FBRyxXQUFXLENBQUM7WUFFOUIsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsVUFBVSxDQUFDLENBQUMsaUJBQWlCLENBQUMsUUFBZ0IsQ0FBQyxDQUFDO1lBQ3BFLGtCQUFrQixDQUFDLE1BQU0sQ0FBQyxpQkFBaUIsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUV0RCxNQUFNLE9BQU8sQ0FBQyxNQUFNLENBQUMsRUFBRSxFQUFFLFNBQVMsQ0FBQyxDQUFDO1lBRXBDLE1BQU0sQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLENBQUMsb0JBQW9CLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDbEQsTUFBTSxDQUFDLGtCQUFrQixDQUFDLE1BQU0sQ0FBQyxDQUFDLG9CQUFvQixDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ2pFLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxhQUFhLENBQUMsQ0FBQyxvQkFBb0IsQ0FDekQsU0FBUyxFQUNULFFBQVEsRUFDUixNQUFNLEVBQ04sRUFBRSxFQUNGLE1BQU0sQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLENBQ25CLENBQUM7UUFDSixDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxvREFBb0QsRUFBRSxLQUFLLElBQUksRUFBRTtZQUNsRSxNQUFNLEVBQUUsR0FBRyxzQ0FBc0MsQ0FBQztZQUNsRCxNQUFNLFNBQVMsR0FBRyxXQUFXLENBQUM7WUFFOUIsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsVUFBVSxDQUFDLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLENBQUM7WUFFeEQsTUFBTSxNQUFNLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxFQUFFLEVBQUUsU0FBUyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLDBCQUFpQixDQUFDLENBQUM7UUFDakYsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxhQUFhLEVBQUUsR0FBRyxFQUFFO1FBQzNCLEVBQUUsQ0FBQywwQ0FBMEMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUN4RCxNQUFNLE1BQU0sR0FBRyxzQ0FBc0MsQ0FBQztZQUN0RCxNQUFNLE9BQU8sR0FBRyxDQUFDLFFBQVEsRUFBRSxRQUFRLENBQUMsQ0FBQztZQUNyQyxNQUFNLFVBQVUsR0FBRyxXQUFXLENBQUM7WUFFL0IsTUFBTSxLQUFLLEdBQUc7Z0JBQ1osRUFBRSxFQUFFLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxPQUFPLEVBQUU7Z0JBQy9CLEVBQUUsRUFBRSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsU0FBUyxFQUFFO2FBQ2xDLENBQUM7WUFFRixJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sRUFBRSxVQUFVLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxRQUFnQixDQUFDLENBQUM7WUFDcEUsa0JBQWtCLENBQUMsU0FBUyxDQUFDLGlCQUFpQixDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQ3RELGtCQUFrQixDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQztnQkFDeEMsR0FBRyxRQUFRO2dCQUNYLEtBQUs7Z0JBQ0wsU0FBUyxFQUFFLENBQUMsT0FBTyxFQUFFLFNBQVMsQ0FBQzthQUNoQyxDQUFDLENBQUM7WUFFSCxNQUFNLE1BQU0sR0FBRyxNQUFNLE9BQU8sQ0FBQyxXQUFXLENBQUMsTUFBTSxFQUFFLE9BQU8sRUFBRSxVQUFVLENBQUMsQ0FBQztZQUV0RSxNQUFNLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxDQUFDLG9CQUFvQixDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQ3RELE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxTQUFTLENBQUMsQ0FBQyxvQkFBb0IsQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUNuRSxNQUFNLENBQUMsa0JBQWtCLENBQUMsSUFBSSxDQUFDLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQztZQUNuRCxNQUFNLENBQUMsZ0JBQWdCLENBQUMsYUFBYSxDQUFDLENBQUMsb0JBQW9CLENBQ3pELFVBQVUsRUFDVixjQUFjLEVBQ2QsTUFBTSxFQUNOLE1BQU0sRUFDTixNQUFNLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUNuQixDQUFDO1lBQ0YsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDdEMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsb0RBQW9ELEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDbEUsTUFBTSxNQUFNLEdBQUcsc0NBQXNDLENBQUM7WUFDdEQsTUFBTSxPQUFPLEdBQUcsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUMzQixNQUFNLFVBQVUsR0FBRyxXQUFXLENBQUM7WUFFL0IsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsVUFBVSxDQUFDLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLENBQUM7WUFFeEQsTUFBTSxNQUFNLENBQUMsT0FBTyxDQUFDLFdBQVcsQ0FBQyxNQUFNLEVBQUUsT0FBTyxFQUFFLFVBQVUsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQywwQkFBaUIsQ0FBQyxDQUFDO1FBQ3BHLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHNEQUFzRCxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3BFLE1BQU0sTUFBTSxHQUFHLHNDQUFzQyxDQUFDO1lBQ3RELE1BQU0sT0FBTyxHQUFHLENBQUMsUUFBUSxFQUFFLFFBQVEsQ0FBQyxDQUFDO1lBQ3JDLE1BQU0sVUFBVSxHQUFHLFdBQVcsQ0FBQztZQUUvQixJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sRUFBRSxVQUFVLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxRQUFnQixDQUFDLENBQUM7WUFDcEUsa0JBQWtCLENBQUMsU0FBUyxDQUFDLGlCQUFpQixDQUFDLENBQUMsRUFBRSxFQUFFLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxzQkFBc0I7WUFFekcsTUFBTSxNQUFNLENBQUMsT0FBTyxDQUFDLFdBQVcsQ0FBQyxNQUFNLEVBQUUsT0FBTyxFQUFFLFVBQVUsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyw0QkFBbUIsQ0FBQyxDQUFDO1FBQ3RHLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsZUFBZSxFQUFFLEdBQUcsRUFBRTtRQUM3QixFQUFFLENBQUMsK0JBQStCLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDN0Msa0JBQWtCLENBQUMsS0FBSztpQkFDckIscUJBQXFCLENBQUMsR0FBRyxDQUFDLENBQUMsUUFBUTtpQkFDbkMscUJBQXFCLENBQUMsRUFBRSxDQUFDLENBQUUsU0FBUztpQkFDcEMscUJBQXFCLENBQUMsRUFBRSxDQUFDLENBQUUsV0FBVztpQkFDdEMscUJBQXFCLENBQUMsQ0FBQyxDQUFDLENBQUcsWUFBWTtpQkFDdkMscUJBQXFCLENBQUMsQ0FBQyxDQUFDLENBQUcsdUJBQXVCO2lCQUNsRCxxQkFBcUIsQ0FBQyxFQUFFLENBQUMsQ0FBRSxpQkFBaUI7aUJBQzVDLHFCQUFxQixDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsbUJBQW1CO1lBRWpELE1BQU0sTUFBTSxHQUFHLE1BQU0sT0FBTyxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBRTdDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxPQUFPLENBQUM7Z0JBQ3JCLEtBQUssRUFBRSxHQUFHO2dCQUNWLFFBQVEsRUFBRTtvQkFDUixNQUFNLEVBQUUsRUFBRTtvQkFDVixRQUFRLEVBQUUsRUFBRTtvQkFDWixTQUFTLEVBQUUsQ0FBQztvQkFDWixtQkFBbUIsRUFBRSxDQUFDO2lCQUN2QjtnQkFDRCxtQkFBbUIsRUFBRTtvQkFDbkIsUUFBUSxFQUFFLEVBQUU7b0JBQ1osVUFBVSxFQUFFLEVBQUU7aUJBQ2Y7Z0JBQ0QsU0FBUyxFQUFFLE1BQU0sQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDO2FBQzlCLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXG1vZHVsZXNcXHVzZXItbWFuYWdlbWVudFxcYXBwbGljYXRpb25cXHNlcnZpY2VzXFx1c2VyLnNlcnZpY2Uuc3BlYy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUZXN0LCBUZXN0aW5nTW9kdWxlIH0gZnJvbSAnQG5lc3Rqcy90ZXN0aW5nJztcclxuaW1wb3J0IHsgZ2V0UmVwb3NpdG9yeVRva2VuIH0gZnJvbSAnQG5lc3Rqcy90eXBlb3JtJztcclxuaW1wb3J0IHsgUmVwb3NpdG9yeSB9IGZyb20gJ3R5cGVvcm0nO1xyXG5pbXBvcnQgeyBOb3RGb3VuZEV4Y2VwdGlvbiwgQmFkUmVxdWVzdEV4Y2VwdGlvbiwgQ29uZmxpY3RFeGNlcHRpb24gfSBmcm9tICdAbmVzdGpzL2NvbW1vbic7XHJcbmltcG9ydCB7IFVzZXJTZXJ2aWNlIH0gZnJvbSAnLi91c2VyLnNlcnZpY2UnO1xyXG5pbXBvcnQgeyBVc2VyIH0gZnJvbSAnLi4vLi4vZG9tYWluL2VudGl0aWVzL3VzZXIuZW50aXR5JztcclxuaW1wb3J0IHsgUm9sZSB9IGZyb20gJy4uLy4uL2RvbWFpbi9lbnRpdGllcy9yb2xlLmVudGl0eSc7XHJcbmltcG9ydCB7IExvZ2dlclNlcnZpY2UgfSBmcm9tICcuLi8uLi8uLi8uLi9pbmZyYXN0cnVjdHVyZS9sb2dnaW5nL2xvZ2dlci5zZXJ2aWNlJztcclxuaW1wb3J0IHsgQXVkaXRTZXJ2aWNlIH0gZnJvbSAnLi4vLi4vLi4vLi4vaW5mcmFzdHJ1Y3R1cmUvbG9nZ2luZy9hdWRpdC9hdWRpdC5zZXJ2aWNlJztcclxuaW1wb3J0IHsgUGFzc3dvcmRTZXJ2aWNlIH0gZnJvbSAnLi4vLi4vLi4vLi4vaW5mcmFzdHJ1Y3R1cmUvYXV0aC9zZXJ2aWNlcy9wYXNzd29yZC5zZXJ2aWNlJztcclxuXHJcbmRlc2NyaWJlKCdVc2VyU2VydmljZScsICgpID0+IHtcclxuICBsZXQgc2VydmljZTogVXNlclNlcnZpY2U7XHJcbiAgbGV0IHVzZXJSZXBvc2l0b3J5OiBSZXBvc2l0b3J5PFVzZXI+O1xyXG4gIGxldCByb2xlUmVwb3NpdG9yeTogUmVwb3NpdG9yeTxSb2xlPjtcclxuICBsZXQgbG9nZ2VyU2VydmljZTogTG9nZ2VyU2VydmljZTtcclxuICBsZXQgYXVkaXRTZXJ2aWNlOiBBdWRpdFNlcnZpY2U7XHJcbiAgbGV0IHBhc3N3b3JkU2VydmljZTogUGFzc3dvcmRTZXJ2aWNlO1xyXG5cclxuICBjb25zdCBtb2NrVXNlclJlcG9zaXRvcnkgPSB7XHJcbiAgICBjcmVhdGU6IGplc3QuZm4oKSxcclxuICAgIHNhdmU6IGplc3QuZm4oKSxcclxuICAgIGZpbmRPbmU6IGplc3QuZm4oKSxcclxuICAgIGZpbmQ6IGplc3QuZm4oKSxcclxuICAgIHJlbW92ZTogamVzdC5mbigpLFxyXG4gICAgY291bnQ6IGplc3QuZm4oKSxcclxuICAgIGNyZWF0ZVF1ZXJ5QnVpbGRlcjogamVzdC5mbigpLFxyXG4gIH07XHJcblxyXG4gIGNvbnN0IG1vY2tSb2xlUmVwb3NpdG9yeSA9IHtcclxuICAgIGZpbmRPbmU6IGplc3QuZm4oKSxcclxuICAgIGZpbmRCeUlkczogamVzdC5mbigpLFxyXG4gIH07XHJcblxyXG4gIGNvbnN0IG1vY2tMb2dnZXJTZXJ2aWNlID0ge1xyXG4gICAgZGVidWc6IGplc3QuZm4oKSxcclxuICAgIGxvZzogamVzdC5mbigpLFxyXG4gICAgd2FybjogamVzdC5mbigpLFxyXG4gICAgZXJyb3I6IGplc3QuZm4oKSxcclxuICB9O1xyXG5cclxuICBjb25zdCBtb2NrQXVkaXRTZXJ2aWNlID0ge1xyXG4gICAgbG9nVXNlckFjdGlvbjogamVzdC5mbigpLFxyXG4gIH07XHJcblxyXG4gIGNvbnN0IG1vY2tQYXNzd29yZFNlcnZpY2UgPSB7XHJcbiAgICBoYXNoUGFzc3dvcmQ6IGplc3QuZm4oKSxcclxuICB9O1xyXG5cclxuICBjb25zdCBtb2NrVXNlcjogUGFydGlhbDxVc2VyPiA9IHtcclxuICAgIGlkOiAnMTIzZTQ1NjctZTg5Yi0xMmQzLWE0NTYtNDI2NjE0MTc0MDAwJyxcclxuICAgIGVtYWlsOiAndGVzdEBleGFtcGxlLmNvbScsXHJcbiAgICBmaXJzdE5hbWU6ICdKb2huJyxcclxuICAgIGxhc3ROYW1lOiAnRG9lJyxcclxuICAgIHN0YXR1czogJ2FjdGl2ZScsXHJcbiAgICBlbWFpbFZlcmlmaWVkOiB0cnVlLFxyXG4gICAgcm9sZXM6IFtdLFxyXG4gICAgcm9sZU5hbWVzOiBbJ3VzZXInXSxcclxuICAgIHBlcm1pc3Npb25zOiBbJ3VzZXJzLnJlYWQnXSxcclxuICAgIGZ1bGxOYW1lOiAnSm9obiBEb2UnLFxyXG4gICAgY2FuTG9naW46IHRydWUsXHJcbiAgICBpc0xvY2tlZDogZmFsc2UsXHJcbiAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCksXHJcbiAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCksXHJcbiAgfTtcclxuXHJcbiAgY29uc3QgbW9ja1JvbGU6IFBhcnRpYWw8Um9sZT4gPSB7XHJcbiAgICBpZDogJ3JvbGUtMTIzJyxcclxuICAgIG5hbWU6ICd1c2VyJyxcclxuICAgIGRpc3BsYXlOYW1lOiAnVXNlcicsXHJcbiAgICBpc0RlZmF1bHQ6IHRydWUsXHJcbiAgICBpc0FjdGl2ZTogdHJ1ZSxcclxuICB9O1xyXG5cclxuICBiZWZvcmVFYWNoKGFzeW5jICgpID0+IHtcclxuICAgIGNvbnN0IG1vZHVsZTogVGVzdGluZ01vZHVsZSA9IGF3YWl0IFRlc3QuY3JlYXRlVGVzdGluZ01vZHVsZSh7XHJcbiAgICAgIHByb3ZpZGVyczogW1xyXG4gICAgICAgIFVzZXJTZXJ2aWNlLFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIHByb3ZpZGU6IGdldFJlcG9zaXRvcnlUb2tlbihVc2VyKSxcclxuICAgICAgICAgIHVzZVZhbHVlOiBtb2NrVXNlclJlcG9zaXRvcnksXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBwcm92aWRlOiBnZXRSZXBvc2l0b3J5VG9rZW4oUm9sZSksXHJcbiAgICAgICAgICB1c2VWYWx1ZTogbW9ja1JvbGVSZXBvc2l0b3J5LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgcHJvdmlkZTogTG9nZ2VyU2VydmljZSxcclxuICAgICAgICAgIHVzZVZhbHVlOiBtb2NrTG9nZ2VyU2VydmljZSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIHByb3ZpZGU6IEF1ZGl0U2VydmljZSxcclxuICAgICAgICAgIHVzZVZhbHVlOiBtb2NrQXVkaXRTZXJ2aWNlLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgcHJvdmlkZTogUGFzc3dvcmRTZXJ2aWNlLFxyXG4gICAgICAgICAgdXNlVmFsdWU6IG1vY2tQYXNzd29yZFNlcnZpY2UsXHJcbiAgICAgICAgfSxcclxuICAgICAgXSxcclxuICAgIH0pLmNvbXBpbGUoKTtcclxuXHJcbiAgICBzZXJ2aWNlID0gbW9kdWxlLmdldDxVc2VyU2VydmljZT4oVXNlclNlcnZpY2UpO1xyXG4gICAgdXNlclJlcG9zaXRvcnkgPSBtb2R1bGUuZ2V0PFJlcG9zaXRvcnk8VXNlcj4+KGdldFJlcG9zaXRvcnlUb2tlbihVc2VyKSk7XHJcbiAgICByb2xlUmVwb3NpdG9yeSA9IG1vZHVsZS5nZXQ8UmVwb3NpdG9yeTxSb2xlPj4oZ2V0UmVwb3NpdG9yeVRva2VuKFJvbGUpKTtcclxuICAgIGxvZ2dlclNlcnZpY2UgPSBtb2R1bGUuZ2V0PExvZ2dlclNlcnZpY2U+KExvZ2dlclNlcnZpY2UpO1xyXG4gICAgYXVkaXRTZXJ2aWNlID0gbW9kdWxlLmdldDxBdWRpdFNlcnZpY2U+KEF1ZGl0U2VydmljZSk7XHJcbiAgICBwYXNzd29yZFNlcnZpY2UgPSBtb2R1bGUuZ2V0PFBhc3N3b3JkU2VydmljZT4oUGFzc3dvcmRTZXJ2aWNlKTtcclxuICB9KTtcclxuXHJcbiAgYWZ0ZXJFYWNoKCgpID0+IHtcclxuICAgIGplc3QuY2xlYXJBbGxNb2NrcygpO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnY3JlYXRlJywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCBjcmVhdGUgYSB1c2VyIHN1Y2Nlc3NmdWxseScsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgY3JlYXRlRGF0YSA9IHtcclxuICAgICAgICBlbWFpbDogJ3Rlc3RAZXhhbXBsZS5jb20nLFxyXG4gICAgICAgIGZpcnN0TmFtZTogJ0pvaG4nLFxyXG4gICAgICAgIGxhc3ROYW1lOiAnRG9lJyxcclxuICAgICAgICBwYXNzd29yZEhhc2g6ICdwYXNzd29yZDEyMycsXHJcbiAgICAgIH07XHJcbiAgICAgIGNvbnN0IGNyZWF0ZWRCeSA9ICdhZG1pbi0xMjMnO1xyXG5cclxuICAgICAgbW9ja1VzZXJSZXBvc2l0b3J5LmZpbmRPbmUubW9ja1Jlc29sdmVkVmFsdWUobnVsbCk7IC8vIE5vIGV4aXN0aW5nIHVzZXJcclxuICAgICAgbW9ja1Bhc3N3b3JkU2VydmljZS5oYXNoUGFzc3dvcmQubW9ja1Jlc29sdmVkVmFsdWUoJ2hhc2hlZC1wYXNzd29yZCcpO1xyXG4gICAgICBtb2NrUm9sZVJlcG9zaXRvcnkuZmluZE9uZS5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrUm9sZSk7IC8vIERlZmF1bHQgcm9sZVxyXG4gICAgICBtb2NrVXNlclJlcG9zaXRvcnkuY3JlYXRlLm1vY2tSZXR1cm5WYWx1ZShtb2NrVXNlcik7XHJcbiAgICAgIG1vY2tVc2VyUmVwb3NpdG9yeS5zYXZlLm1vY2tSZXNvbHZlZFZhbHVlKG1vY2tVc2VyKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHNlcnZpY2UuY3JlYXRlKGNyZWF0ZURhdGEsIGNyZWF0ZWRCeSk7XHJcblxyXG4gICAgICBleHBlY3QobW9ja1VzZXJSZXBvc2l0b3J5LmZpbmRPbmUpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKHtcclxuICAgICAgICB3aGVyZTogeyBlbWFpbDogY3JlYXRlRGF0YS5lbWFpbC50b0xvd2VyQ2FzZSgpIH0sXHJcbiAgICAgIH0pO1xyXG4gICAgICBleHBlY3QobW9ja1Bhc3N3b3JkU2VydmljZS5oYXNoUGFzc3dvcmQpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKGNyZWF0ZURhdGEucGFzc3dvcmRIYXNoKTtcclxuICAgICAgZXhwZWN0KG1vY2tVc2VyUmVwb3NpdG9yeS5jcmVhdGUpLnRvSGF2ZUJlZW5DYWxsZWQoKTtcclxuICAgICAgZXhwZWN0KG1vY2tVc2VyUmVwb3NpdG9yeS5zYXZlKS50b0hhdmVCZWVuQ2FsbGVkV2l0aChtb2NrVXNlcik7XHJcbiAgICAgIGV4cGVjdChtb2NrQXVkaXRTZXJ2aWNlLmxvZ1VzZXJBY3Rpb24pLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKFxyXG4gICAgICAgIGNyZWF0ZWRCeSxcclxuICAgICAgICAnY3JlYXRlJyxcclxuICAgICAgICAndXNlcicsXHJcbiAgICAgICAgbW9ja1VzZXIuaWQsXHJcbiAgICAgICAgZXhwZWN0LmFueShPYmplY3QpLFxyXG4gICAgICApO1xyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0VxdWFsKG1vY2tVc2VyKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgQmFkUmVxdWVzdEV4Y2VwdGlvbiB3aGVuIHJlcXVpcmVkIGZpZWxkcyBhcmUgbWlzc2luZycsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgY3JlYXRlRGF0YSA9IHtcclxuICAgICAgICBlbWFpbDogJ3Rlc3RAZXhhbXBsZS5jb20nLFxyXG4gICAgICAgIC8vIE1pc3NpbmcgZmlyc3ROYW1lIGFuZCBsYXN0TmFtZVxyXG4gICAgICB9O1xyXG4gICAgICBjb25zdCBjcmVhdGVkQnkgPSAnYWRtaW4tMTIzJztcclxuXHJcbiAgICAgIGF3YWl0IGV4cGVjdChzZXJ2aWNlLmNyZWF0ZShjcmVhdGVEYXRhLCBjcmVhdGVkQnkpKS5yZWplY3RzLnRvVGhyb3coQmFkUmVxdWVzdEV4Y2VwdGlvbik7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IENvbmZsaWN0RXhjZXB0aW9uIHdoZW4gdXNlciBhbHJlYWR5IGV4aXN0cycsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgY3JlYXRlRGF0YSA9IHtcclxuICAgICAgICBlbWFpbDogJ3Rlc3RAZXhhbXBsZS5jb20nLFxyXG4gICAgICAgIGZpcnN0TmFtZTogJ0pvaG4nLFxyXG4gICAgICAgIGxhc3ROYW1lOiAnRG9lJyxcclxuICAgICAgICBwYXNzd29yZEhhc2g6ICdwYXNzd29yZDEyMycsXHJcbiAgICAgIH07XHJcbiAgICAgIGNvbnN0IGNyZWF0ZWRCeSA9ICdhZG1pbi0xMjMnO1xyXG5cclxuICAgICAgbW9ja1VzZXJSZXBvc2l0b3J5LmZpbmRPbmUubW9ja1Jlc29sdmVkVmFsdWUobW9ja1VzZXIpOyAvLyBFeGlzdGluZyB1c2VyXHJcblxyXG4gICAgICBhd2FpdCBleHBlY3Qoc2VydmljZS5jcmVhdGUoY3JlYXRlRGF0YSwgY3JlYXRlZEJ5KSkucmVqZWN0cy50b1Rocm93KENvbmZsaWN0RXhjZXB0aW9uKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnZmluZEJ5SWQnLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIHJldHVybiB1c2VyIHdoZW4gZm91bmQnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGlkID0gJzEyM2U0NTY3LWU4OWItMTJkMy1hNDU2LTQyNjYxNDE3NDAwMCc7XHJcbiAgICAgIG1vY2tVc2VyUmVwb3NpdG9yeS5maW5kT25lLm1vY2tSZXNvbHZlZFZhbHVlKG1vY2tVc2VyKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHNlcnZpY2UuZmluZEJ5SWQoaWQpO1xyXG5cclxuICAgICAgZXhwZWN0KG1vY2tVc2VyUmVwb3NpdG9yeS5maW5kT25lKS50b0hhdmVCZWVuQ2FsbGVkV2l0aCh7XHJcbiAgICAgICAgd2hlcmU6IHsgaWQgfSxcclxuICAgICAgICByZWxhdGlvbnM6IFsncm9sZXMnXSxcclxuICAgICAgfSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvRXF1YWwobW9ja1VzZXIpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCByZXR1cm4gbnVsbCB3aGVuIHVzZXIgbm90IGZvdW5kJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpZCA9ICcxMjNlNDU2Ny1lODliLTEyZDMtYTQ1Ni00MjY2MTQxNzQwMDAnO1xyXG4gICAgICBtb2NrVXNlclJlcG9zaXRvcnkuZmluZE9uZS5tb2NrUmVzb2x2ZWRWYWx1ZShudWxsKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHNlcnZpY2UuZmluZEJ5SWQoaWQpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9CZU51bGwoKTtcclxuICAgICAgZXhwZWN0KG1vY2tMb2dnZXJTZXJ2aWNlLndhcm4pLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKCdVc2VyIG5vdCBmb3VuZCcsIHsgaWQgfSk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2ZpbmRCeUVtYWlsJywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCByZXR1cm4gdXNlciB3aGVuIGZvdW5kIGJ5IGVtYWlsJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCBlbWFpbCA9ICd0ZXN0QGV4YW1wbGUuY29tJztcclxuICAgICAgbW9ja1VzZXJSZXBvc2l0b3J5LmZpbmRPbmUubW9ja1Jlc29sdmVkVmFsdWUobW9ja1VzZXIpO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc2VydmljZS5maW5kQnlFbWFpbChlbWFpbCk7XHJcblxyXG4gICAgICBleHBlY3QobW9ja1VzZXJSZXBvc2l0b3J5LmZpbmRPbmUpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKHtcclxuICAgICAgICB3aGVyZTogeyBlbWFpbDogZW1haWwudG9Mb3dlckNhc2UoKSB9LFxyXG4gICAgICAgIHJlbGF0aW9uczogWydyb2xlcyddLFxyXG4gICAgICB9KTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9FcXVhbChtb2NrVXNlcik7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ3VwZGF0ZScsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgdXBkYXRlIHVzZXIgc3VjY2Vzc2Z1bGx5JywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpZCA9ICcxMjNlNDU2Ny1lODliLTEyZDMtYTQ1Ni00MjY2MTQxNzQwMDAnO1xyXG4gICAgICBjb25zdCB1cGRhdGVEYXRhID0geyBmaXJzdE5hbWU6ICdKYW5lJyB9O1xyXG4gICAgICBjb25zdCB1cGRhdGVkQnkgPSAnYWRtaW4tMTIzJztcclxuXHJcbiAgICAgIGplc3Quc3B5T24oc2VydmljZSwgJ2ZpbmRCeUlkJykubW9ja1Jlc29sdmVkVmFsdWUobW9ja1VzZXIgYXMgVXNlcik7XHJcbiAgICAgIG1vY2tVc2VyUmVwb3NpdG9yeS5zYXZlLm1vY2tSZXNvbHZlZFZhbHVlKHsgLi4ubW9ja1VzZXIsIC4uLnVwZGF0ZURhdGEgfSk7XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzZXJ2aWNlLnVwZGF0ZShpZCwgdXBkYXRlRGF0YSwgdXBkYXRlZEJ5KTtcclxuXHJcbiAgICAgIGV4cGVjdChzZXJ2aWNlLmZpbmRCeUlkKS50b0hhdmVCZWVuQ2FsbGVkV2l0aChpZCk7XHJcbiAgICAgIGV4cGVjdChtb2NrVXNlclJlcG9zaXRvcnkuc2F2ZSkudG9IYXZlQmVlbkNhbGxlZCgpO1xyXG4gICAgICBleHBlY3QobW9ja0F1ZGl0U2VydmljZS5sb2dVc2VyQWN0aW9uKS50b0hhdmVCZWVuQ2FsbGVkV2l0aChcclxuICAgICAgICB1cGRhdGVkQnksXHJcbiAgICAgICAgJ3VwZGF0ZScsXHJcbiAgICAgICAgJ3VzZXInLFxyXG4gICAgICAgIGlkLFxyXG4gICAgICAgIGV4cGVjdC5hbnkoT2JqZWN0KSxcclxuICAgICAgKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9FcXVhbChleHBlY3Qub2JqZWN0Q29udGFpbmluZyh1cGRhdGVEYXRhKSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IE5vdEZvdW5kRXhjZXB0aW9uIHdoZW4gdXNlciBub3QgZm91bmQnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGlkID0gJzEyM2U0NTY3LWU4OWItMTJkMy1hNDU2LTQyNjYxNDE3NDAwMCc7XHJcbiAgICAgIGNvbnN0IHVwZGF0ZURhdGEgPSB7IGZpcnN0TmFtZTogJ0phbmUnIH07XHJcbiAgICAgIGNvbnN0IHVwZGF0ZWRCeSA9ICdhZG1pbi0xMjMnO1xyXG5cclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlLCAnZmluZEJ5SWQnKS5tb2NrUmVzb2x2ZWRWYWx1ZShudWxsKTtcclxuXHJcbiAgICAgIGF3YWl0IGV4cGVjdChzZXJ2aWNlLnVwZGF0ZShpZCwgdXBkYXRlRGF0YSwgdXBkYXRlZEJ5KSkucmVqZWN0cy50b1Rocm93KE5vdEZvdW5kRXhjZXB0aW9uKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgQ29uZmxpY3RFeGNlcHRpb24gd2hlbiBlbWFpbCBhbHJlYWR5IGV4aXN0cycsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgaWQgPSAnMTIzZTQ1NjctZTg5Yi0xMmQzLWE0NTYtNDI2NjE0MTc0MDAwJztcclxuICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IHsgZW1haWw6ICdleGlzdGluZ0BleGFtcGxlLmNvbScgfTtcclxuICAgICAgY29uc3QgdXBkYXRlZEJ5ID0gJ2FkbWluLTEyMyc7XHJcblxyXG4gICAgICBjb25zdCBleGlzdGluZ1VzZXIgPSB7IC4uLm1vY2tVc2VyLCBpZDogJ2RpZmZlcmVudC1pZCcgfTtcclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlLCAnZmluZEJ5SWQnKS5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrVXNlciBhcyBVc2VyKTtcclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlLCAnZmluZEJ5RW1haWwnKS5tb2NrUmVzb2x2ZWRWYWx1ZShleGlzdGluZ1VzZXIgYXMgVXNlcik7XHJcblxyXG4gICAgICBhd2FpdCBleHBlY3Qoc2VydmljZS51cGRhdGUoaWQsIHVwZGF0ZURhdGEsIHVwZGF0ZWRCeSkpLnJlamVjdHMudG9UaHJvdyhDb25mbGljdEV4Y2VwdGlvbik7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2RlbGV0ZScsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgZGVsZXRlIHVzZXIgc3VjY2Vzc2Z1bGx5JywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpZCA9ICcxMjNlNDU2Ny1lODliLTEyZDMtYTQ1Ni00MjY2MTQxNzQwMDAnO1xyXG4gICAgICBjb25zdCBkZWxldGVkQnkgPSAnYWRtaW4tMTIzJztcclxuXHJcbiAgICAgIGplc3Quc3B5T24oc2VydmljZSwgJ2ZpbmRCeUlkJykubW9ja1Jlc29sdmVkVmFsdWUobW9ja1VzZXIgYXMgVXNlcik7XHJcbiAgICAgIG1vY2tVc2VyUmVwb3NpdG9yeS5yZW1vdmUubW9ja1Jlc29sdmVkVmFsdWUobW9ja1VzZXIpO1xyXG5cclxuICAgICAgYXdhaXQgc2VydmljZS5kZWxldGUoaWQsIGRlbGV0ZWRCeSk7XHJcblxyXG4gICAgICBleHBlY3Qoc2VydmljZS5maW5kQnlJZCkudG9IYXZlQmVlbkNhbGxlZFdpdGgoaWQpO1xyXG4gICAgICBleHBlY3QobW9ja1VzZXJSZXBvc2l0b3J5LnJlbW92ZSkudG9IYXZlQmVlbkNhbGxlZFdpdGgobW9ja1VzZXIpO1xyXG4gICAgICBleHBlY3QobW9ja0F1ZGl0U2VydmljZS5sb2dVc2VyQWN0aW9uKS50b0hhdmVCZWVuQ2FsbGVkV2l0aChcclxuICAgICAgICBkZWxldGVkQnksXHJcbiAgICAgICAgJ2RlbGV0ZScsXHJcbiAgICAgICAgJ3VzZXInLFxyXG4gICAgICAgIGlkLFxyXG4gICAgICAgIGV4cGVjdC5hbnkoT2JqZWN0KSxcclxuICAgICAgKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgTm90Rm91bmRFeGNlcHRpb24gd2hlbiB1c2VyIG5vdCBmb3VuZCcsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgaWQgPSAnMTIzZTQ1NjctZTg5Yi0xMmQzLWE0NTYtNDI2NjE0MTc0MDAwJztcclxuICAgICAgY29uc3QgZGVsZXRlZEJ5ID0gJ2FkbWluLTEyMyc7XHJcblxyXG4gICAgICBqZXN0LnNweU9uKHNlcnZpY2UsICdmaW5kQnlJZCcpLm1vY2tSZXNvbHZlZFZhbHVlKG51bGwpO1xyXG5cclxuICAgICAgYXdhaXQgZXhwZWN0KHNlcnZpY2UuZGVsZXRlKGlkLCBkZWxldGVkQnkpKS5yZWplY3RzLnRvVGhyb3coTm90Rm91bmRFeGNlcHRpb24pO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdhc3NpZ25Sb2xlcycsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgYXNzaWduIHJvbGVzIHRvIHVzZXIgc3VjY2Vzc2Z1bGx5JywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCB1c2VySWQgPSAnMTIzZTQ1NjctZTg5Yi0xMmQzLWE0NTYtNDI2NjE0MTc0MDAwJztcclxuICAgICAgY29uc3Qgcm9sZUlkcyA9IFsncm9sZS0xJywgJ3JvbGUtMiddO1xyXG4gICAgICBjb25zdCBhc3NpZ25lZEJ5ID0gJ2FkbWluLTEyMyc7XHJcblxyXG4gICAgICBjb25zdCByb2xlcyA9IFtcclxuICAgICAgICB7IGlkOiAncm9sZS0xJywgbmFtZTogJ2FkbWluJyB9LFxyXG4gICAgICAgIHsgaWQ6ICdyb2xlLTInLCBuYW1lOiAnYW5hbHlzdCcgfSxcclxuICAgICAgXTtcclxuXHJcbiAgICAgIGplc3Quc3B5T24oc2VydmljZSwgJ2ZpbmRCeUlkJykubW9ja1Jlc29sdmVkVmFsdWUobW9ja1VzZXIgYXMgVXNlcik7XHJcbiAgICAgIG1vY2tSb2xlUmVwb3NpdG9yeS5maW5kQnlJZHMubW9ja1Jlc29sdmVkVmFsdWUocm9sZXMpO1xyXG4gICAgICBtb2NrVXNlclJlcG9zaXRvcnkuc2F2ZS5tb2NrUmVzb2x2ZWRWYWx1ZSh7XHJcbiAgICAgICAgLi4ubW9ja1VzZXIsXHJcbiAgICAgICAgcm9sZXMsXHJcbiAgICAgICAgcm9sZU5hbWVzOiBbJ2FkbWluJywgJ2FuYWx5c3QnXSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzZXJ2aWNlLmFzc2lnblJvbGVzKHVzZXJJZCwgcm9sZUlkcywgYXNzaWduZWRCeSk7XHJcblxyXG4gICAgICBleHBlY3Qoc2VydmljZS5maW5kQnlJZCkudG9IYXZlQmVlbkNhbGxlZFdpdGgodXNlcklkKTtcclxuICAgICAgZXhwZWN0KG1vY2tSb2xlUmVwb3NpdG9yeS5maW5kQnlJZHMpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKHJvbGVJZHMpO1xyXG4gICAgICBleHBlY3QobW9ja1VzZXJSZXBvc2l0b3J5LnNhdmUpLnRvSGF2ZUJlZW5DYWxsZWQoKTtcclxuICAgICAgZXhwZWN0KG1vY2tBdWRpdFNlcnZpY2UubG9nVXNlckFjdGlvbikudG9IYXZlQmVlbkNhbGxlZFdpdGgoXHJcbiAgICAgICAgYXNzaWduZWRCeSxcclxuICAgICAgICAnYXNzaWduX3JvbGVzJyxcclxuICAgICAgICAndXNlcicsXHJcbiAgICAgICAgdXNlcklkLFxyXG4gICAgICAgIGV4cGVjdC5hbnkoT2JqZWN0KSxcclxuICAgICAgKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5yb2xlcykudG9FcXVhbChyb2xlcyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IE5vdEZvdW5kRXhjZXB0aW9uIHdoZW4gdXNlciBub3QgZm91bmQnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHVzZXJJZCA9ICcxMjNlNDU2Ny1lODliLTEyZDMtYTQ1Ni00MjY2MTQxNzQwMDAnO1xyXG4gICAgICBjb25zdCByb2xlSWRzID0gWydyb2xlLTEnXTtcclxuICAgICAgY29uc3QgYXNzaWduZWRCeSA9ICdhZG1pbi0xMjMnO1xyXG5cclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlLCAnZmluZEJ5SWQnKS5tb2NrUmVzb2x2ZWRWYWx1ZShudWxsKTtcclxuXHJcbiAgICAgIGF3YWl0IGV4cGVjdChzZXJ2aWNlLmFzc2lnblJvbGVzKHVzZXJJZCwgcm9sZUlkcywgYXNzaWduZWRCeSkpLnJlamVjdHMudG9UaHJvdyhOb3RGb3VuZEV4Y2VwdGlvbik7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IEJhZFJlcXVlc3RFeGNlcHRpb24gd2hlbiByb2xlIG5vdCBmb3VuZCcsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgdXNlcklkID0gJzEyM2U0NTY3LWU4OWItMTJkMy1hNDU2LTQyNjYxNDE3NDAwMCc7XHJcbiAgICAgIGNvbnN0IHJvbGVJZHMgPSBbJ3JvbGUtMScsICdyb2xlLTInXTtcclxuICAgICAgY29uc3QgYXNzaWduZWRCeSA9ICdhZG1pbi0xMjMnO1xyXG5cclxuICAgICAgamVzdC5zcHlPbihzZXJ2aWNlLCAnZmluZEJ5SWQnKS5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrVXNlciBhcyBVc2VyKTtcclxuICAgICAgbW9ja1JvbGVSZXBvc2l0b3J5LmZpbmRCeUlkcy5tb2NrUmVzb2x2ZWRWYWx1ZShbeyBpZDogJ3JvbGUtMScsIG5hbWU6ICdhZG1pbicgfV0pOyAvLyBPbmx5IG9uZSByb2xlIGZvdW5kXHJcblxyXG4gICAgICBhd2FpdCBleHBlY3Qoc2VydmljZS5hc3NpZ25Sb2xlcyh1c2VySWQsIHJvbGVJZHMsIGFzc2lnbmVkQnkpKS5yZWplY3RzLnRvVGhyb3coQmFkUmVxdWVzdEV4Y2VwdGlvbik7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2dldFN0YXRpc3RpY3MnLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIHJldHVybiB1c2VyIHN0YXRpc3RpY3MnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIG1vY2tVc2VyUmVwb3NpdG9yeS5jb3VudFxyXG4gICAgICAgIC5tb2NrUmVzb2x2ZWRWYWx1ZU9uY2UoMTAwKSAvLyB0b3RhbFxyXG4gICAgICAgIC5tb2NrUmVzb2x2ZWRWYWx1ZU9uY2UoODApICAvLyBhY3RpdmVcclxuICAgICAgICAubW9ja1Jlc29sdmVkVmFsdWVPbmNlKDEwKSAgLy8gaW5hY3RpdmVcclxuICAgICAgICAubW9ja1Jlc29sdmVkVmFsdWVPbmNlKDUpICAgLy8gc3VzcGVuZGVkXHJcbiAgICAgICAgLm1vY2tSZXNvbHZlZFZhbHVlT25jZSg1KSAgIC8vIHBlbmRpbmcgdmVyaWZpY2F0aW9uXHJcbiAgICAgICAgLm1vY2tSZXNvbHZlZFZhbHVlT25jZSg5MCkgIC8vIGVtYWlsIHZlcmlmaWVkXHJcbiAgICAgICAgLm1vY2tSZXNvbHZlZFZhbHVlT25jZSgxMCk7IC8vIGVtYWlsIHVudmVyaWZpZWRcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHNlcnZpY2UuZ2V0U3RhdGlzdGljcygpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9FcXVhbCh7XHJcbiAgICAgICAgdG90YWw6IDEwMCxcclxuICAgICAgICBieVN0YXR1czoge1xyXG4gICAgICAgICAgYWN0aXZlOiA4MCxcclxuICAgICAgICAgIGluYWN0aXZlOiAxMCxcclxuICAgICAgICAgIHN1c3BlbmRlZDogNSxcclxuICAgICAgICAgIHBlbmRpbmdWZXJpZmljYXRpb246IDUsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBieUVtYWlsVmVyaWZpY2F0aW9uOiB7XHJcbiAgICAgICAgICB2ZXJpZmllZDogOTAsXHJcbiAgICAgICAgICB1bnZlcmlmaWVkOiAxMCxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHRpbWVzdGFtcDogZXhwZWN0LmFueShTdHJpbmcpLFxyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG59KTtcclxuIl0sInZlcnNpb24iOjN9