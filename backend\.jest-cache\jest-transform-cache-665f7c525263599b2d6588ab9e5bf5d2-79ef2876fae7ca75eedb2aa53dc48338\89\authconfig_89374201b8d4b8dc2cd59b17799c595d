1c0ecbfdc64b78096e3efa87233a18db
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authConfig = exports.AuthConfigValidation = void 0;
const config_1 = require("@nestjs/config");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
// Consolidated utility functions with better type safety
const parseNumber = (value, fallback) => {
    if (!value)
        return fallback;
    const parsed = Number(value);
    return Number.isNaN(parsed) ? fallback : parsed;
};
const parseBoolean = (value, fallback = false) => value?.toLowerCase() === 'true' || fallback;
// Time utilities as a namespace for better organization
var Time;
(function (Time) {
    Time.HOUR_MS = 3600000;
    Time.DAY_MS = 86400000;
    Time.minutes = (n) => n * 60000;
})(Time || (Time = {}));
// Validation class with comprehensive rules and better decorators
class AuthConfigValidation {
}
exports.AuthConfigValidation = AuthConfigValidation;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => value || 'your-super-secret-jwt-key-change-this-in-production'),
    __metadata("design:type", String)
], AuthConfigValidation.prototype, "jwtSecret", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => value || 'your-super-secret-refresh-key-change-this-in-production'),
    __metadata("design:type", String)
], AuthConfigValidation.prototype, "jwtRefreshSecret", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => value || 'your-super-secret-session-key-change-this-in-production'),
    __metadata("design:type", String)
], AuthConfigValidation.prototype, "sessionSecret", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(4),
    (0, class_validator_1.Max)(15),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_transformer_1.Transform)(({ value }) => parseNumber(value, 12)),
    __metadata("design:type", Number)
], AuthConfigValidation.prototype, "bcryptRounds", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(20),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_transformer_1.Transform)(({ value }) => parseNumber(value, 5)),
    __metadata("design:type", Number)
], AuthConfigValidation.prototype, "maxLoginAttempts", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_transformer_1.Transform)(({ value }) => parseNumber(value, 15)),
    __metadata("design:type", Number)
], AuthConfigValidation.prototype, "lockoutMinutes", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(6),
    (0, class_validator_1.Max)(128),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_transformer_1.Transform)(({ value }) => parseNumber(value, 8)),
    __metadata("design:type", Number)
], AuthConfigValidation.prototype, "passwordMinLength", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => parseBoolean(value, false)),
    __metadata("design:type", Boolean)
], AuthConfigValidation.prototype, "enableTwoFactor", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => parseBoolean(value, false)),
    __metadata("design:type", Boolean)
], AuthConfigValidation.prototype, "enableRoleHierarchy", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => parseBoolean(value, false)),
    __metadata("design:type", Boolean)
], AuthConfigValidation.prototype, "apiKeyEnabled", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)(['user', 'admin', 'super_admin']),
    __metadata("design:type", String)
], AuthConfigValidation.prototype, "defaultRole", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)(['strict', 'lax', 'none']),
    __metadata("design:type", String)
], AuthConfigValidation.prototype, "sameSite", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AuthConfigValidation.prototype, "googleClientId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AuthConfigValidation.prototype, "googleClientSecret", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AuthConfigValidation.prototype, "microsoftClientId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AuthConfigValidation.prototype, "microsoftClientSecret", void 0);
// Configuration defaults with const assertions
const DEFAULTS = {
    JWT_SECRET: 'your-super-secret-jwt-key-change-this-in-production',
    JWT_REFRESH_SECRET: 'your-super-secret-refresh-key-change-this-in-production',
    JWT_ISSUER: 'sentinel-platform',
    JWT_AUDIENCE: 'sentinel-users',
    JWT_EXPIRES_IN: '1h',
    JWT_REFRESH_EXPIRES_IN: '7d',
    SESSION_SECRET: 'your-super-secret-session-key-change-this-in-production',
    DEFAULT_ROLE: 'user',
    ADMIN_ROLE: 'admin',
    SUPER_ADMIN_ROLE: 'super_admin',
    MAX_LOGIN_ATTEMPTS: 5,
    LOCKOUT_MINUTES: 15,
    PASSWORD_MIN_LENGTH: 8,
    SESSION_TIMEOUT_MINUTES: 30,
    API_KEY_HEADER: 'X-API-Key',
    API_KEY_EXPIRATION_DAYS: 365,
    BCRYPT_ROUNDS: 12,
};
// Optimized environment variable access with type safety
const env = {
    get: (key, fallback) => process.env[key] ?? fallback,
    getString: (key, fallback = '') => process.env[key] ?? fallback,
    getNumber: (key, fallback = 0) => parseNumber(process.env[key], fallback),
    getBoolean: (key, fallback = false) => parseBoolean(process.env[key], fallback),
    isProduction: () => process.env['NODE_ENV'] === 'production',
};
// Streamlined OAuth provider builder
const createOAuthProvider = (provider) => {
    const prefix = provider.toUpperCase();
    const config = {};
    const clientId = env.get(`${prefix}_CLIENT_ID`);
    const clientSecret = env.get(`${prefix}_CLIENT_SECRET`);
    const callbackUrl = env.get(`${prefix}_CALLBACK_URL`);
    if (clientId)
        config.clientId = clientId;
    if (clientSecret)
        config.clientSecret = clientSecret;
    if (callbackUrl)
        config.callbackUrl = callbackUrl;
    return config;
};
// Main configuration factory with enhanced structure
exports.authConfig = (0, config_1.registerAs)('auth', () => ({
    jwt: {
        secret: env.getString('JWT_SECRET', DEFAULTS.JWT_SECRET),
        expiresIn: env.getString('JWT_EXPIRES_IN', DEFAULTS.JWT_EXPIRES_IN),
        refreshSecret: env.getString('JWT_REFRESH_SECRET', DEFAULTS.JWT_REFRESH_SECRET),
        refreshExpiresIn: env.getString('JWT_REFRESH_EXPIRES_IN', DEFAULTS.JWT_REFRESH_EXPIRES_IN),
        issuer: env.getString('JWT_ISSUER', DEFAULTS.JWT_ISSUER),
        audience: env.getString('JWT_AUDIENCE', DEFAULTS.JWT_AUDIENCE),
    },
    bcrypt: {
        rounds: env.getNumber('BCRYPT_ROUNDS', DEFAULTS.BCRYPT_ROUNDS),
    },
    session: {
        secret: env.getString('SESSION_SECRET', DEFAULTS.SESSION_SECRET),
        resave: false,
        saveUninitialized: false,
        cookie: {
            secure: env.isProduction(),
            httpOnly: true,
            maxAge: Time.DAY_MS,
            sameSite: env.get('COOKIE_SAME_SITE') ?? 'strict',
        },
    },
    oauth: {
        google: createOAuthProvider('google'),
        microsoft: createOAuthProvider('microsoft'),
    },
    rbac: {
        defaultRole: env.get('DEFAULT_USER_ROLE') ?? DEFAULTS.DEFAULT_ROLE,
        adminRole: env.get('ADMIN_ROLE') ?? DEFAULTS.ADMIN_ROLE,
        superAdminRole: env.get('SUPER_ADMIN_ROLE') ?? DEFAULTS.SUPER_ADMIN_ROLE,
        enableRoleHierarchy: env.getBoolean('ENABLE_ROLE_HIERARCHY'),
    },
    security: {
        maxLoginAttempts: env.getNumber('MAX_LOGIN_ATTEMPTS', DEFAULTS.MAX_LOGIN_ATTEMPTS),
        lockoutDuration: Time.minutes(env.getNumber('LOCKOUT_MINUTES', DEFAULTS.LOCKOUT_MINUTES)),
        passwordMinLength: env.getNumber('PASSWORD_MIN_LENGTH', DEFAULTS.PASSWORD_MIN_LENGTH),
        passwordRequireUppercase: env.getBoolean('PASSWORD_REQUIRE_UPPERCASE'),
        passwordRequireLowercase: env.getBoolean('PASSWORD_REQUIRE_LOWERCASE'),
        passwordRequireNumbers: env.getBoolean('PASSWORD_REQUIRE_NUMBERS'),
        passwordRequireSymbols: env.getBoolean('PASSWORD_REQUIRE_SYMBOLS'),
        enableTwoFactor: env.getBoolean('ENABLE_TWO_FACTOR'),
        sessionTimeout: Time.minutes(env.getNumber('SESSION_TIMEOUT_MINUTES', DEFAULTS.SESSION_TIMEOUT_MINUTES)),
    },
    apiKey: {
        headerName: env.getString('API_KEY_HEADER', DEFAULTS.API_KEY_HEADER),
        enabled: env.getBoolean('API_KEY_ENABLED'),
        defaultExpiration: env.getNumber('API_KEY_EXPIRATION_DAYS', DEFAULTS.API_KEY_EXPIRATION_DAYS) * Time.DAY_MS,
    },
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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