{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\__tests__\\error-handling-integration.spec.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAAsD;AACtD,2CAA8L;AAC9L,2CAA8C;AAC9C,mDAAqC;AACrC,2CAAiG;AACjG,oHAA0G;AAC1G,uFAAkF;AAElF,sDAAsD;AAItD,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAEvB,UAAU;QACR,OAAO,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;IACzC,CAAC;IAGD,aAAa;QACX,MAAM,IAAI,4BAAmB,CAAC,4BAA4B,CAAC,CAAC;IAC9D,CAAC;IAGD,qBAAqB;QACnB,MAAM,IAAI,4BAAmB,CAAC;YAC5B,OAAO,EAAE,mBAAmB;YAC5B,MAAM,EAAE;gBACN,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,sBAAsB,EAAE;gBACnD,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,gCAAgC,EAAE;aAC5D;SACF,CAAC,CAAC;IACL,CAAC;IAGD,eAAe;QACb,MAAM,IAAI,8BAAqB,CAAC,yBAAyB,CAAC,CAAC;IAC7D,CAAC;IAGD,YAAY;QACV,MAAM,IAAI,2BAAkB,CAAC,0BAA0B,CAAC,CAAC;IAC3D,CAAC;IAGD,WAAW;QACT,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,CAAC,CAAC;IACpD,CAAC;IAGD,gBAAgB;QACd,MAAM,IAAI,qCAA4B,CAAC,uBAAuB,CAAC,CAAC;IAClE,CAAC;IAGD,cAAc;QACZ,MAAM,IAAI,sBAAa,CACrB;YACE,UAAU,EAAE,GAAG;YACf,OAAO,EAAE,sBAAsB;YAC/B,KAAK,EAAE,kCAAkC;YACzC,OAAO,EAAE;gBACP,IAAI,EAAE,yBAAyB;gBAC/B,KAAK,EAAE,QAAQ;gBACf,UAAU,EAAE,kCAAkC;aAC/C;SACF,EACD,mBAAU,CAAC,oBAAoB,CAChC,CAAC;IACJ,CAAC;IAGD,kBAAkB;QAChB,sCAAsC;QACtC,MAAM,GAAG,GAAQ,IAAI,CAAC;QACtB,OAAO,GAAG,CAAC,YAAY,CAAC,CAAC,4BAA4B;IACvD,CAAC;IAGK,AAAN,KAAK,CAAC,aAAa;QACjB,0BAA0B;QAC1B,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACpC,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC;IAGD,mBAAmB,CAAS,IAAS;QACnC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,IAAI,4BAAmB,CAAC;gBAC5B,0BAA0B;gBAC1B,uBAAuB;aACxB,CAAC,CAAC;QACL,CAAC;QACD,OAAO,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC;IAC1C,CAAC;IAGK,AAAN,KAAK,CAAC,eAAe;QACnB,8BAA8B;QAC9B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QACxD,OAAO,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;IAC5C,CAAC;IAGD,gBAAgB;QACd,uCAAuC;QACvC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACzD,KAAK,CAAC,IAAI,GAAG,yBAAyB,CAAC;QACtC,KAAa,CAAC,IAAI,GAAG,cAAc,CAAC;QACrC,MAAM,KAAK,CAAC;IACd,CAAC;IAGD,iBAAiB;QACf,MAAM,IAAI,sBAAa,CACrB;YACE,UAAU,EAAE,GAAG;YACf,OAAO,EAAE,mBAAmB;YAC5B,KAAK,EAAE,qBAAqB;YAC5B,UAAU,EAAE,EAAE;SACf,EACD,mBAAU,CAAC,iBAAiB,CAC7B,CAAC;IACJ,CAAC;IAGD,OAAO,CAAc,EAAU;QAC7B,IAAI,EAAE,KAAK,SAAS,EAAE,CAAC;YACrB,MAAM,IAAI,4BAAmB,CAAC,wBAAwB,CAAC,CAAC;QAC1D,CAAC;QACD,IAAI,EAAE,KAAK,UAAU,EAAE,CAAC;YACtB,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;IAClC,CAAC;CACF,CAAA;AA1HC;IADC,IAAA,YAAG,EAAC,SAAS,CAAC;;;;qDAGd;AAGD;IADC,IAAA,YAAG,EAAC,aAAa,CAAC;;;;wDAGlB;AAGD;IADC,IAAA,YAAG,EAAC,sBAAsB,CAAC;;;;gEAS3B;AAGD;IADC,IAAA,YAAG,EAAC,cAAc,CAAC;;;;0DAGnB;AAGD;IADC,IAAA,YAAG,EAAC,WAAW,CAAC;;;;uDAGhB;AAGD;IADC,IAAA,YAAG,EAAC,WAAW,CAAC;;;;sDAGhB;AAGD;IADC,IAAA,YAAG,EAAC,gBAAgB,CAAC;;;;2DAGrB;AAGD;IADC,IAAA,YAAG,EAAC,cAAc,CAAC;;;;yDAenB;AAGD;IADC,IAAA,YAAG,EAAC,kBAAkB,CAAC;;;;6DAKvB;AAGK;IADL,IAAA,YAAG,EAAC,aAAa,CAAC;;;;wDAMlB;AAGD;IADC,IAAA,aAAI,EAAC,kBAAkB,CAAC;IACJ,WAAA,IAAA,aAAI,GAAE,CAAA;;;;8DAQ1B;AAGK;IADL,IAAA,YAAG,EAAC,eAAe,CAAC;;;;0DAKpB;AAGD;IADC,IAAA,YAAG,EAAC,gBAAgB,CAAC;;;;2DAOrB;AAGD;IADC,IAAA,YAAG,EAAC,kBAAkB,CAAC;;;;4DAWvB;AAGD;IADC,IAAA,YAAG,EAAC,UAAU,CAAC;IACP,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;kDAQnB;AA3HG,mBAAmB;IAHxB,IAAA,mBAAU,EAAC,YAAY,CAAC;IACxB,IAAA,wBAAe,EAAC,kEAA4B,CAAC;IAC7C,IAAA,mBAAU,EAAC,+CAAqB,CAAC;GAC5B,mBAAmB,CA4HxB;AAED,+BAA+B;AAC/B,MAAM,yBAAyB;IAC7B,KAAK,CAAC,SAAc,EAAE,IAAS;QAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;QACnC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC;QAEjC,IAAI,MAAM,GAAG,mBAAU,CAAC,qBAAqB,CAAC;QAC9C,IAAI,aAAa,GAAQ;YACvB,UAAU,EAAE,MAAM;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,CAAC,GAAG;YACjB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,OAAO,EAAE,uBAAuB;YAChC,KAAK,EAAE,uBAAuB;SAC/B,CAAC;QAEF,IAAI,SAAS,YAAY,sBAAa,EAAE,CAAC;YACvC,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,iBAAiB,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YAElD,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE,CAAC;gBAC1C,aAAa,GAAG;oBACd,UAAU,EAAE,MAAM;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,IAAI,EAAE,OAAO,CAAC,GAAG;oBACjB,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,OAAO,EAAE,iBAAiB;oBAC1B,KAAK,EAAE,SAAS,CAAC,IAAI;iBACtB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,aAAa,GAAG;oBACd,UAAU,EAAE,MAAM;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,IAAI,EAAE,OAAO,CAAC,GAAG;oBACjB,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,GAAG,iBAAiB;iBACrB,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IAAI,SAAS,YAAY,KAAK,EAAE,CAAC;YACtC,2BAA2B;YAC3B,aAAa,GAAG;gBACd,UAAU,EAAE,mBAAU,CAAC,qBAAqB;gBAC5C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,IAAI,EAAE,OAAO,CAAC,GAAG;gBACjB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,KAAK,EAAE,SAAS,CAAC,IAAI;aACtB,CAAC;YAEF,8BAA8B;YAC9B,IAAI,SAAS,CAAC,IAAI,KAAK,yBAAyB,EAAE,CAAC;gBACjD,aAAa,CAAC,UAAU,GAAG,mBAAU,CAAC,mBAAmB,CAAC;gBAC1D,aAAa,CAAC,KAAK,GAAG,qBAAqB,CAAC;gBAC5C,aAAa,CAAC,OAAO,GAAG,6CAA6C,CAAC;YACxE,CAAC;QACH,CAAC;QAED,gCAAgC;QAChC,IAAI,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACxC,aAAa,CAAC,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACpE,CAAC;QAED,8BAA8B;QAC9B,aAAa,CAAC,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,SAAS,CAAC;QAEvE,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;CACF;AAED,sCAAsC;AACtC,MAAM,gCAAgC;IACpC,SAAS,CAAC,OAAY,EAAE,IAAS;QAC/B,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI;QACvB,8DAA8D;SAC/D,CAAC;IACJ,CAAC;CACF;AAED,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;IAChD,IAAI,GAAqB,CAAC;IAE1B,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;iBACf,CAAC;aACH;YACD,WAAW,EAAE,CAAC,mBAAmB,CAAC;YAClC,SAAS,EAAE;gBACT;oBACE,OAAO,EAAE,+CAAqB;oBAC9B,QAAQ,EAAE,yBAAyB;iBACpC;gBACD;oBACE,OAAO,EAAE,kEAA4B;oBACrC,QAAQ,EAAE,gCAAgC;iBAC3C;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAE5C,gCAAgC;QAChC,GAAG,CAAC,gBAAgB,CAAC,IAAI,yBAAyB,EAAE,CAAC,CAAC;QAEtD,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;IACnB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,OAAO,EAAE,kBAAkB;aAC5B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,yBAAyB,CAAC;iBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,yBAAyB;gBAC/B,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,4BAA4B;gBACrC,KAAK,EAAE,qBAAqB;gBAC5B,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,kCAAkC,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,kCAAkC;gBACxC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,mBAAmB;gBAC5B,MAAM,EAAE;oBACN,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,sBAAsB,EAAE;oBACnD,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,gCAAgC,EAAE;iBAC5D;gBACD,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,0BAA0B,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,0BAA0B;gBAChC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,yBAAyB;gBAClC,KAAK,EAAE,uBAAuB;gBAC9B,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,uBAAuB,CAAC;iBAC5B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,uBAAuB;gBAC7B,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,0BAA0B;gBACnC,KAAK,EAAE,oBAAoB;gBAC3B,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,uBAAuB,CAAC;iBAC5B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,uBAAuB;gBAC7B,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,oBAAoB;gBAC7B,KAAK,EAAE,mBAAmB;gBAC1B,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,4BAA4B,CAAC;iBACjC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,4BAA4B;gBAClC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,uBAAuB;gBAChC,KAAK,EAAE,8BAA8B;gBACrC,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,0BAA0B,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,0BAA0B;gBAChC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,sBAAsB;gBAC/B,KAAK,EAAE,kCAAkC;gBACzC,OAAO,EAAE;oBACP,IAAI,EAAE,yBAAyB;oBAC/B,KAAK,EAAE,QAAQ;oBACf,UAAU,EAAE,kCAAkC;iBAC/C;gBACD,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,8BAA8B,CAAC;iBACnC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,8BAA8B;gBACpC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC;gBAC/C,KAAK,EAAE,WAAW;gBAClB,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,yBAAyB,CAAC;iBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,yBAAyB;gBAC/B,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,wBAAwB;gBACjC,KAAK,EAAE,OAAO;gBACd,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,8BAA8B,CAAC;iBACpC,IAAI,CAAC,EAAE,CAAC;iBACR,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,8BAA8B;gBACpC,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,0BAA0B;oBAC1B,uBAAuB;iBACxB;gBACD,KAAK,EAAE,qBAAqB;gBAC5B,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,8BAA8B,CAAC;iBACpC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;iBAC1B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,OAAO,EAAE,mBAAmB;aAC7B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,4BAA4B,CAAC;iBACjC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,4BAA4B;gBAClC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,6CAA6C;gBACtD,KAAK,EAAE,qBAAqB;gBAC5B,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,8BAA8B,CAAC;iBACnC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,8BAA8B;gBACpC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,mBAAmB;gBAC5B,KAAK,EAAE,qBAAqB;gBAC5B,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,EAAE,EAAE,KAAK;gBACT,IAAI,EAAE,UAAU;aACjB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,0BAA0B,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,2BAA2B,CAAC;iBAChC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,yBAAyB,CAAC;iBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,8BAA8B,CAAC;iBACpC,IAAI,CAAC,EAAE,CAAC;iBACR,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAChE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,aAAa,GAAG,sBAAsB,CAAC;YAE7C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,yBAAyB,CAAC;iBAC9B,GAAG,CAAC,kBAAkB,EAAE,aAAa,CAAC;iBACtC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,SAAS,GAAG,kBAAkB,CAAC;YAErC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,yBAAyB,CAAC;iBAC9B,GAAG,CAAC,cAAc,EAAE,SAAS,CAAC;iBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,sEAAsE,EAAE,KAAK,IAAI,EAAE;YACpF,MAAM,SAAS,GAAG;gBAChB,yBAAyB;gBACzB,0BAA0B;gBAC1B,uBAAuB;gBACvB,uBAAuB;gBACvB,4BAA4B;aAC7B,CAAC;YAEF,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAElE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;gBACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;gBAClD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAC/C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;gBAChD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;YAC1E,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,8BAA8B,CAAC;iBACnC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,oDAAoD;YACpD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YACpE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,MAAM,WAAW,GAAG;YAClB,EAAE,QAAQ,EAAE,yBAAyB,EAAE,cAAc,EAAE,GAAG,EAAE;YAC5D,EAAE,QAAQ,EAAE,0BAA0B,EAAE,cAAc,EAAE,GAAG,EAAE;YAC7D,EAAE,QAAQ,EAAE,uBAAuB,EAAE,cAAc,EAAE,GAAG,EAAE;YAC1D,EAAE,QAAQ,EAAE,uBAAuB,EAAE,cAAc,EAAE,GAAG,EAAE;YAC1D,EAAE,QAAQ,EAAE,0BAA0B,EAAE,cAAc,EAAE,GAAG,EAAE;YAC7D,EAAE,QAAQ,EAAE,8BAA8B,EAAE,cAAc,EAAE,GAAG,EAAE;YACjE,EAAE,QAAQ,EAAE,4BAA4B,EAAE,cAAc,EAAE,GAAG,EAAE;YAC/D,EAAE,QAAQ,EAAE,4BAA4B,EAAE,cAAc,EAAE,GAAG,EAAE;SAChE,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,EAAE,EAAE;YACnD,EAAE,CAAC,qCAAqC,cAAc,QAAQ,QAAQ,EAAE,EAAE,KAAK,IAAI,EAAE;gBACnF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAElE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,yBAAyB,CAAC;iBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,mDAAmD;YACnD,MAAM,cAAc,GAAG;gBACrB,yBAAyB;gBACzB,8BAA8B;gBAC9B,yBAAyB;gBACzB,4BAA4B;aAC7B,CAAC;YAEF,KAAK,MAAM,QAAQ,IAAI,cAAc,EAAE,CAAC;gBACtC,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACnD,CAAC;YAED,qDAAqD;YACrD,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\__tests__\\error-handling-integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication, HttpException, HttpStatus, BadRequestException, UnauthorizedException, ForbiddenException, NotFoundException, InternalServerErrorException } from '@nestjs/common';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport * as request from 'supertest';\r\nimport { Controller, Get, Post, Body, Param, UseFilters, UseInterceptors } from '@nestjs/common';\r\nimport { ResponseTransformInterceptor } from '../common/interceptors/response-transformation.interceptor';\r\nimport { GlobalExceptionFilter } from '../common/filters/global-exception.filter';\r\n\r\n// Test controller that throws various types of errors\r\n@Controller('error-test')\r\n@UseInterceptors(ResponseTransformInterceptor)\r\n@UseFilters(GlobalExceptionFilter)\r\nclass ErrorTestController {\r\n  @Get('success')\r\n  getSuccess() {\r\n    return { message: 'Success response' };\r\n  }\r\n\r\n  @Get('bad-request')\r\n  getBadRequest() {\r\n    throw new BadRequestException('Invalid request parameters');\r\n  }\r\n\r\n  @Get('bad-request-detailed')\r\n  getBadRequestDetailed() {\r\n    throw new BadRequestException({\r\n      message: 'Validation failed',\r\n      errors: [\r\n        { field: 'email', message: 'Invalid email format' },\r\n        { field: 'age', message: 'Age must be between 18 and 120' },\r\n      ],\r\n    });\r\n  }\r\n\r\n  @Get('unauthorized')\r\n  getUnauthorized() {\r\n    throw new UnauthorizedException('Authentication required');\r\n  }\r\n\r\n  @Get('forbidden')\r\n  getForbidden() {\r\n    throw new ForbiddenException('Insufficient permissions');\r\n  }\r\n\r\n  @Get('not-found')\r\n  getNotFound() {\r\n    throw new NotFoundException('Resource not found');\r\n  }\r\n\r\n  @Get('internal-error')\r\n  getInternalError() {\r\n    throw new InternalServerErrorException('Internal server error');\r\n  }\r\n\r\n  @Get('custom-error')\r\n  getCustomError() {\r\n    throw new HttpException(\r\n      {\r\n        statusCode: 422,\r\n        message: 'Unprocessable Entity',\r\n        error: 'Business logic validation failed',\r\n        details: {\r\n          code: 'BUSINESS_RULE_VIOLATION',\r\n          field: 'amount',\r\n          constraint: 'Amount cannot exceed daily limit',\r\n        },\r\n      },\r\n      HttpStatus.UNPROCESSABLE_ENTITY,\r\n    );\r\n  }\r\n\r\n  @Get('javascript-error')\r\n  getJavaScriptError() {\r\n    // Simulate a JavaScript runtime error\r\n    const obj: any = null;\r\n    return obj.someProperty; // This will throw TypeError\r\n  }\r\n\r\n  @Get('async-error')\r\n  async getAsyncError() {\r\n    // Simulate an async error\r\n    await new Promise((resolve, reject) => {\r\n      setTimeout(() => reject(new Error('Async operation failed')), 10);\r\n    });\r\n  }\r\n\r\n  @Post('validation-error')\r\n  postValidationError(@Body() body: any) {\r\n    if (!body.name) {\r\n      throw new BadRequestException([\r\n        'name should not be empty',\r\n        'name must be a string',\r\n      ]);\r\n    }\r\n    return { message: 'Validation passed' };\r\n  }\r\n\r\n  @Get('timeout-error')\r\n  async getTimeoutError() {\r\n    // Simulate a timeout scenario\r\n    await new Promise(resolve => setTimeout(resolve, 5000));\r\n    return { message: 'This should timeout' };\r\n  }\r\n\r\n  @Get('database-error')\r\n  getDatabaseError() {\r\n    // Simulate a database connection error\r\n    const error = new Error('Connection to database failed');\r\n    error.name = 'DatabaseConnectionError';\r\n    (error as any).code = 'ECONNREFUSED';\r\n    throw error;\r\n  }\r\n\r\n  @Get('rate-limit-error')\r\n  getRateLimitError() {\r\n    throw new HttpException(\r\n      {\r\n        statusCode: 429,\r\n        message: 'Too Many Requests',\r\n        error: 'Rate limit exceeded',\r\n        retryAfter: 60,\r\n      },\r\n      HttpStatus.TOO_MANY_REQUESTS,\r\n    );\r\n  }\r\n\r\n  @Get('user/:id')\r\n  getUser(@Param('id') id: string) {\r\n    if (id === 'invalid') {\r\n      throw new BadRequestException('Invalid user ID format');\r\n    }\r\n    if (id === 'notfound') {\r\n      throw new NotFoundException(`User with ID ${id} not found`);\r\n    }\r\n    return { id, name: 'John Doe' };\r\n  }\r\n}\r\n\r\n// Mock Global Exception Filter\r\nclass MockGlobalExceptionFilter {\r\n  catch(exception: any, host: any) {\r\n    const ctx = host.switchToHttp();\r\n    const response = ctx.getResponse();\r\n    const request = ctx.getRequest();\r\n\r\n    let status = HttpStatus.INTERNAL_SERVER_ERROR;\r\n    let errorResponse: any = {\r\n      statusCode: status,\r\n      timestamp: new Date().toISOString(),\r\n      path: request.url,\r\n      method: request.method,\r\n      message: 'Internal server error',\r\n      error: 'Internal Server Error',\r\n    };\r\n\r\n    if (exception instanceof HttpException) {\r\n      status = exception.getStatus();\r\n      const exceptionResponse = exception.getResponse();\r\n      \r\n      if (typeof exceptionResponse === 'string') {\r\n        errorResponse = {\r\n          statusCode: status,\r\n          timestamp: new Date().toISOString(),\r\n          path: request.url,\r\n          method: request.method,\r\n          message: exceptionResponse,\r\n          error: exception.name,\r\n        };\r\n      } else {\r\n        errorResponse = {\r\n          statusCode: status,\r\n          timestamp: new Date().toISOString(),\r\n          path: request.url,\r\n          method: request.method,\r\n          ...exceptionResponse,\r\n        };\r\n      }\r\n    } else if (exception instanceof Error) {\r\n      // Handle JavaScript errors\r\n      errorResponse = {\r\n        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,\r\n        timestamp: new Date().toISOString(),\r\n        path: request.url,\r\n        method: request.method,\r\n        message: exception.message,\r\n        error: exception.name,\r\n      };\r\n\r\n      // Handle specific error types\r\n      if (exception.name === 'DatabaseConnectionError') {\r\n        errorResponse.statusCode = HttpStatus.SERVICE_UNAVAILABLE;\r\n        errorResponse.error = 'Service Unavailable';\r\n        errorResponse.message = 'Database service is temporarily unavailable';\r\n      }\r\n    }\r\n\r\n    // Add correlation ID if present\r\n    if (request.headers['x-correlation-id']) {\r\n      errorResponse.correlationId = request.headers['x-correlation-id'];\r\n    }\r\n\r\n    // Add request ID for tracking\r\n    errorResponse.requestId = request.headers['x-request-id'] || 'unknown';\r\n\r\n    response.status(status).json(errorResponse);\r\n  }\r\n}\r\n\r\n// Mock Response Transform Interceptor\r\nclass MockResponseTransformInterceptor {\r\n  intercept(context: any, next: any) {\r\n    return next.handle().pipe(\r\n      // In a real implementation, this would transform the response\r\n    );\r\n  }\r\n}\r\n\r\ndescribe('Error Handling Integration Tests', () => {\r\n  let app: INestApplication;\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n        }),\r\n      ],\r\n      controllers: [ErrorTestController],\r\n      providers: [\r\n        {\r\n          provide: GlobalExceptionFilter,\r\n          useClass: MockGlobalExceptionFilter,\r\n        },\r\n        {\r\n          provide: ResponseTransformInterceptor,\r\n          useClass: MockResponseTransformInterceptor,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    \r\n    // Apply global exception filter\r\n    app.useGlobalFilters(new MockGlobalExceptionFilter());\r\n\r\n    await app.init();\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  describe('Success Responses', () => {\r\n    it('should return successful response', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/success')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        message: 'Success response',\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('HTTP Exception Handling', () => {\r\n    it('should handle BadRequestException', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/bad-request')\r\n        .expect(400);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 400,\r\n        timestamp: expect.any(String),\r\n        path: '/error-test/bad-request',\r\n        method: 'GET',\r\n        message: 'Invalid request parameters',\r\n        error: 'BadRequestException',\r\n        requestId: 'unknown',\r\n      });\r\n    });\r\n\r\n    it('should handle detailed BadRequestException', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/bad-request-detailed')\r\n        .expect(400);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 400,\r\n        timestamp: expect.any(String),\r\n        path: '/error-test/bad-request-detailed',\r\n        method: 'GET',\r\n        message: 'Validation failed',\r\n        errors: [\r\n          { field: 'email', message: 'Invalid email format' },\r\n          { field: 'age', message: 'Age must be between 18 and 120' },\r\n        ],\r\n        requestId: 'unknown',\r\n      });\r\n    });\r\n\r\n    it('should handle UnauthorizedException', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/unauthorized')\r\n        .expect(401);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 401,\r\n        timestamp: expect.any(String),\r\n        path: '/error-test/unauthorized',\r\n        method: 'GET',\r\n        message: 'Authentication required',\r\n        error: 'UnauthorizedException',\r\n        requestId: 'unknown',\r\n      });\r\n    });\r\n\r\n    it('should handle ForbiddenException', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/forbidden')\r\n        .expect(403);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 403,\r\n        timestamp: expect.any(String),\r\n        path: '/error-test/forbidden',\r\n        method: 'GET',\r\n        message: 'Insufficient permissions',\r\n        error: 'ForbiddenException',\r\n        requestId: 'unknown',\r\n      });\r\n    });\r\n\r\n    it('should handle NotFoundException', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/not-found')\r\n        .expect(404);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 404,\r\n        timestamp: expect.any(String),\r\n        path: '/error-test/not-found',\r\n        method: 'GET',\r\n        message: 'Resource not found',\r\n        error: 'NotFoundException',\r\n        requestId: 'unknown',\r\n      });\r\n    });\r\n\r\n    it('should handle InternalServerErrorException', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/internal-error')\r\n        .expect(500);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 500,\r\n        timestamp: expect.any(String),\r\n        path: '/error-test/internal-error',\r\n        method: 'GET',\r\n        message: 'Internal server error',\r\n        error: 'InternalServerErrorException',\r\n        requestId: 'unknown',\r\n      });\r\n    });\r\n\r\n    it('should handle custom HTTP exceptions', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/custom-error')\r\n        .expect(422);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 422,\r\n        timestamp: expect.any(String),\r\n        path: '/error-test/custom-error',\r\n        method: 'GET',\r\n        message: 'Unprocessable Entity',\r\n        error: 'Business logic validation failed',\r\n        details: {\r\n          code: 'BUSINESS_RULE_VIOLATION',\r\n          field: 'amount',\r\n          constraint: 'Amount cannot exceed daily limit',\r\n        },\r\n        requestId: 'unknown',\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('JavaScript Error Handling', () => {\r\n    it('should handle JavaScript runtime errors', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/javascript-error')\r\n        .expect(500);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 500,\r\n        timestamp: expect.any(String),\r\n        path: '/error-test/javascript-error',\r\n        method: 'GET',\r\n        message: expect.stringContaining('Cannot read'),\r\n        error: 'TypeError',\r\n        requestId: 'unknown',\r\n      });\r\n    });\r\n\r\n    it('should handle async errors', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/async-error')\r\n        .expect(500);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 500,\r\n        timestamp: expect.any(String),\r\n        path: '/error-test/async-error',\r\n        method: 'GET',\r\n        message: 'Async operation failed',\r\n        error: 'Error',\r\n        requestId: 'unknown',\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Validation Error Handling', () => {\r\n    it('should handle validation errors with array messages', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/error-test/validation-error')\r\n        .send({})\r\n        .expect(400);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 400,\r\n        timestamp: expect.any(String),\r\n        path: '/error-test/validation-error',\r\n        method: 'POST',\r\n        message: [\r\n          'name should not be empty',\r\n          'name must be a string',\r\n        ],\r\n        error: 'BadRequestException',\r\n        requestId: 'unknown',\r\n      });\r\n    });\r\n\r\n    it('should pass validation with valid data', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/error-test/validation-error')\r\n        .send({ name: 'John Doe' })\r\n        .expect(201);\r\n\r\n      expect(response.body).toEqual({\r\n        message: 'Validation passed',\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Specific Error Types', () => {\r\n    it('should handle database connection errors', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/database-error')\r\n        .expect(503);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 503,\r\n        timestamp: expect.any(String),\r\n        path: '/error-test/database-error',\r\n        method: 'GET',\r\n        message: 'Database service is temporarily unavailable',\r\n        error: 'Service Unavailable',\r\n        requestId: 'unknown',\r\n      });\r\n    });\r\n\r\n    it('should handle rate limit errors', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/rate-limit-error')\r\n        .expect(429);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 429,\r\n        timestamp: expect.any(String),\r\n        path: '/error-test/rate-limit-error',\r\n        method: 'GET',\r\n        message: 'Too Many Requests',\r\n        error: 'Rate limit exceeded',\r\n        retryAfter: 60,\r\n        requestId: 'unknown',\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Parameterized Route Error Handling', () => {\r\n    it('should handle valid parameter', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/user/123')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        id: '123',\r\n        name: 'John Doe',\r\n      });\r\n    });\r\n\r\n    it('should handle invalid parameter', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/user/invalid')\r\n        .expect(400);\r\n\r\n      expect(response.body.message).toBe('Invalid user ID format');\r\n    });\r\n\r\n    it('should handle not found parameter', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/user/notfound')\r\n        .expect(404);\r\n\r\n      expect(response.body.message).toBe('User with ID notfound not found');\r\n    });\r\n  });\r\n\r\n  describe('Error Response Format', () => {\r\n    it('should include timestamp in all error responses', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/bad-request')\r\n        .expect(400);\r\n\r\n      expect(response.body.timestamp).toMatch(/^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}/);\r\n    });\r\n\r\n    it('should include request path and method', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/error-test/validation-error')\r\n        .send({})\r\n        .expect(400);\r\n\r\n      expect(response.body.path).toBe('/error-test/validation-error');\r\n      expect(response.body.method).toBe('POST');\r\n    });\r\n\r\n    it('should include correlation ID when provided', async () => {\r\n      const correlationId = 'test-correlation-123';\r\n      \r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/bad-request')\r\n        .set('X-Correlation-ID', correlationId)\r\n        .expect(400);\r\n\r\n      expect(response.body.correlationId).toBe(correlationId);\r\n    });\r\n\r\n    it('should include request ID for tracking', async () => {\r\n      const requestId = 'test-request-456';\r\n      \r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/bad-request')\r\n        .set('X-Request-ID', requestId)\r\n        .expect(400);\r\n\r\n      expect(response.body.requestId).toBe(requestId);\r\n    });\r\n  });\r\n\r\n  describe('Error Consistency', () => {\r\n    it('should maintain consistent error format across different error types', async () => {\r\n      const endpoints = [\r\n        '/error-test/bad-request',\r\n        '/error-test/unauthorized',\r\n        '/error-test/forbidden',\r\n        '/error-test/not-found',\r\n        '/error-test/internal-error',\r\n      ];\r\n\r\n      for (const endpoint of endpoints) {\r\n        const response = await request(app.getHttpServer()).get(endpoint);\r\n        \r\n        expect(response.body).toHaveProperty('statusCode');\r\n        expect(response.body).toHaveProperty('timestamp');\r\n        expect(response.body).toHaveProperty('path');\r\n        expect(response.body).toHaveProperty('method');\r\n        expect(response.body).toHaveProperty('message');\r\n        expect(response.body).toHaveProperty('error');\r\n        expect(response.body).toHaveProperty('requestId');\r\n      }\r\n    });\r\n\r\n    it('should not expose sensitive information in error responses', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/javascript-error')\r\n        .expect(500);\r\n\r\n      // Should not contain stack traces or internal paths\r\n      expect(JSON.stringify(response.body)).not.toContain('node_modules');\r\n      expect(JSON.stringify(response.body)).not.toContain('at Object.');\r\n    });\r\n  });\r\n\r\n  describe('HTTP Status Code Accuracy', () => {\r\n    const statusTests = [\r\n      { endpoint: '/error-test/bad-request', expectedStatus: 400 },\r\n      { endpoint: '/error-test/unauthorized', expectedStatus: 401 },\r\n      { endpoint: '/error-test/forbidden', expectedStatus: 403 },\r\n      { endpoint: '/error-test/not-found', expectedStatus: 404 },\r\n      { endpoint: '/error-test/custom-error', expectedStatus: 422 },\r\n      { endpoint: '/error-test/rate-limit-error', expectedStatus: 429 },\r\n      { endpoint: '/error-test/internal-error', expectedStatus: 500 },\r\n      { endpoint: '/error-test/database-error', expectedStatus: 503 },\r\n    ];\r\n\r\n    statusTests.forEach(({ endpoint, expectedStatus }) => {\r\n      it(`should return correct status code ${expectedStatus} for ${endpoint}`, async () => {\r\n        const response = await request(app.getHttpServer()).get(endpoint);\r\n        \r\n        expect(response.status).toBe(expectedStatus);\r\n        expect(response.body.statusCode).toBe(expectedStatus);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Content-Type Headers', () => {\r\n    it('should return JSON content type for error responses', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/error-test/bad-request')\r\n        .expect(400);\r\n\r\n      expect(response.headers['content-type']).toMatch(/application\\/json/);\r\n    });\r\n  });\r\n\r\n  describe('Error Logging Integration', () => {\r\n    it('should handle errors without breaking the application', async () => {\r\n      // Make multiple error requests to ensure stability\r\n      const errorEndpoints = [\r\n        '/error-test/bad-request',\r\n        '/error-test/javascript-error',\r\n        '/error-test/async-error',\r\n        '/error-test/database-error',\r\n      ];\r\n\r\n      for (const endpoint of errorEndpoints) {\r\n        await request(app.getHttpServer()).get(endpoint);\r\n      }\r\n\r\n      // Application should still respond to valid requests\r\n      await request(app.getHttpServer())\r\n        .get('/error-test/success')\r\n        .expect(200);\r\n    });\r\n  });\r\n});"], "version": 3}