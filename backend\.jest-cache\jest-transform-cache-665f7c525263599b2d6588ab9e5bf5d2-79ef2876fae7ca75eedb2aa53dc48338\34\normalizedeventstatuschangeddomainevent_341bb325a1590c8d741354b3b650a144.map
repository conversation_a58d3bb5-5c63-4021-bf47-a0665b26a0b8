{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\normalized-event-status-changed.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAC5E,iFAA+F;AAoB/F;;;;;;;;;;GAUG;AACH,MAAa,uCAAwC,SAAQ,+BAAsD;IACjH,YACE,WAA2B,EAC3B,SAAgD,EAChD,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,IAAI,CAAC,SAAS,KAAK,6CAAmB,CAAC,SAAS,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,SAAS,KAAK,6CAAmB,CAAC,MAAM,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,SAAS,KAAK,6CAAmB,CAAC,OAAO,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,SAAS,KAAK,6CAAmB,CAAC,OAAO;YAC9C,IAAI,CAAC,SAAS,KAAK,6CAAmB,CAAC,WAAW,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,MAAM,EAAE,OAAO,KAAK,IAAI,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,MAAM,IAAI,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,MAAM,EAAE,oBAAoB,IAAI,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,MAAM,EAAE,eAAe,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,eAAe;QAmBb,OAAO;YACL,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC9C,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,wBAAwB,EAAE,IAAI,CAAC,wBAAwB,EAAE;YACzD,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACnD,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACrD,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACrD,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7C,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7C,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE;YAC9C,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,oBAAoB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YAClD,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;SAC3C,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;SACrC,CAAC;IACJ,CAAC;CACF;AAnMD,0FAmMC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\normalized-event-status-changed.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { NormalizationStatus, NormalizationResult } from '../entities/normalized-event.entity';\r\n\r\n/**\r\n * Normalized Event Status Changed Domain Event Data\r\n */\r\nexport interface NormalizedEventStatusChangedEventData {\r\n  /** Previous normalization status */\r\n  oldStatus: NormalizationStatus;\r\n  /** New normalization status */\r\n  newStatus: NormalizationStatus;\r\n  /** Normalization result (if completed) */\r\n  result?: NormalizationResult;\r\n  /** Data quality score */\r\n  dataQualityScore?: number;\r\n  /** Whether the event requires manual review */\r\n  requiresManualReview: boolean;\r\n  /** Timestamp when the status changed */\r\n  timestamp?: Date;\r\n}\r\n\r\n/**\r\n * Normalized Event Status Changed Domain Event\r\n * \r\n * Raised when a normalized event's status changes during the normalization process.\r\n * This event triggers various downstream processes including:\r\n * - Status monitoring and alerting\r\n * - Workflow progression\r\n * - Quality assurance checks\r\n * - Performance metrics collection\r\n * - Error handling and retry logic\r\n */\r\nexport class NormalizedEventStatusChangedDomainEvent extends BaseDomainEvent<NormalizedEventStatusChangedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: NormalizedEventStatusChangedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the previous normalization status\r\n   */\r\n  get oldStatus(): NormalizationStatus {\r\n    return this.eventData.oldStatus;\r\n  }\r\n\r\n  /**\r\n   * Get the new normalization status\r\n   */\r\n  get newStatus(): NormalizationStatus {\r\n    return this.eventData.newStatus;\r\n  }\r\n\r\n  /**\r\n   * Get the normalization result\r\n   */\r\n  get result(): NormalizationResult | undefined {\r\n    return this.eventData.result;\r\n  }\r\n\r\n  /**\r\n   * Get the data quality score\r\n   */\r\n  get dataQualityScore(): number | undefined {\r\n    return this.eventData.dataQualityScore;\r\n  }\r\n\r\n  /**\r\n   * Check if the event requires manual review\r\n   */\r\n  get requiresManualReview(): boolean {\r\n    return this.eventData.requiresManualReview;\r\n  }\r\n\r\n  /**\r\n   * Get the timestamp when status changed\r\n   */\r\n  get timestamp(): Date {\r\n    return this.eventData.timestamp || this.occurredOn;\r\n  }\r\n\r\n  /**\r\n   * Check if normalization was completed\r\n   */\r\n  isNormalizationCompleted(): boolean {\r\n    return this.newStatus === NormalizationStatus.COMPLETED;\r\n  }\r\n\r\n  /**\r\n   * Check if normalization failed\r\n   */\r\n  isNormalizationFailed(): boolean {\r\n    return this.newStatus === NormalizationStatus.FAILED;\r\n  }\r\n\r\n  /**\r\n   * Check if normalization was skipped\r\n   */\r\n  isNormalizationSkipped(): boolean {\r\n    return this.newStatus === NormalizationStatus.SKIPPED;\r\n  }\r\n\r\n  /**\r\n   * Check if normalization started\r\n   */\r\n  isNormalizationStarted(): boolean {\r\n    return this.oldStatus === NormalizationStatus.PENDING && \r\n           this.newStatus === NormalizationStatus.IN_PROGRESS;\r\n  }\r\n\r\n  /**\r\n   * Check if the result indicates success\r\n   */\r\n  isResultSuccessful(): boolean {\r\n    return this.result?.success === true;\r\n  }\r\n\r\n  /**\r\n   * Check if the event has high data quality\r\n   */\r\n  hasHighDataQuality(): boolean {\r\n    return (this.dataQualityScore || 0) >= 60;\r\n  }\r\n\r\n  /**\r\n   * Get the number of applied rules\r\n   */\r\n  getAppliedRulesCount(): number {\r\n    return this.result?.appliedRules?.length || 0;\r\n  }\r\n\r\n  /**\r\n   * Get the number of failed rules\r\n   */\r\n  getFailedRulesCount(): number {\r\n    return this.result?.failedRules?.length || 0;\r\n  }\r\n\r\n  /**\r\n   * Get the number of errors\r\n   */\r\n  getErrorsCount(): number {\r\n    return this.result?.errors?.length || 0;\r\n  }\r\n\r\n  /**\r\n   * Get the number of warnings\r\n   */\r\n  getWarningsCount(): number {\r\n    return this.result?.warnings?.length || 0;\r\n  }\r\n\r\n  /**\r\n   * Get processing duration\r\n   */\r\n  getProcessingDuration(): number {\r\n    return this.result?.processingDurationMs || 0;\r\n  }\r\n\r\n  /**\r\n   * Get confidence score\r\n   */\r\n  getConfidenceScore(): number {\r\n    return this.result?.confidenceScore || 0;\r\n  }\r\n\r\n  /**\r\n   * Get event summary for handlers\r\n   */\r\n  getEventSummary(): {\r\n    normalizedEventId: string;\r\n    oldStatus: NormalizationStatus;\r\n    newStatus: NormalizationStatus;\r\n    dataQualityScore?: number;\r\n    requiresManualReview: boolean;\r\n    isNormalizationCompleted: boolean;\r\n    isNormalizationFailed: boolean;\r\n    isNormalizationSkipped: boolean;\r\n    isNormalizationStarted: boolean;\r\n    isResultSuccessful: boolean;\r\n    hasHighDataQuality: boolean;\r\n    appliedRulesCount: number;\r\n    failedRulesCount: number;\r\n    errorsCount: number;\r\n    warningsCount: number;\r\n    processingDurationMs: number;\r\n    confidenceScore: number;\r\n  } {\r\n    return {\r\n      normalizedEventId: this.aggregateId.toString(),\r\n      oldStatus: this.oldStatus,\r\n      newStatus: this.newStatus,\r\n      dataQualityScore: this.dataQualityScore,\r\n      requiresManualReview: this.requiresManualReview,\r\n      isNormalizationCompleted: this.isNormalizationCompleted(),\r\n      isNormalizationFailed: this.isNormalizationFailed(),\r\n      isNormalizationSkipped: this.isNormalizationSkipped(),\r\n      isNormalizationStarted: this.isNormalizationStarted(),\r\n      isResultSuccessful: this.isResultSuccessful(),\r\n      hasHighDataQuality: this.hasHighDataQuality(),\r\n      appliedRulesCount: this.getAppliedRulesCount(),\r\n      failedRulesCount: this.getFailedRulesCount(),\r\n      errorsCount: this.getErrorsCount(),\r\n      warningsCount: this.getWarningsCount(),\r\n      processingDurationMs: this.getProcessingDuration(),\r\n      confidenceScore: this.getConfidenceScore(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventSummary: this.getEventSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}