{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\vulnerability-scan.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,6CAAmD;AACnD,qCAAkD;AAClD,+CAAwD;AACxD,+FAAoF;AACpF,yFAA+E;AAC/E,sFAAkF;AAClF,0FAAsF;AACtF,uGAAmG;AAEnG;;;GAGG;AAEI,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAGnC,YAEE,cAA8D,EAE9D,eAAmD,EAClC,aAA4B,EAC5B,YAA0B,EAC1B,mBAAwC;QALxC,mBAAc,GAAd,cAAc,CAA+B;QAE7C,oBAAe,GAAf,eAAe,CAAmB;QAClC,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;QAC1B,wBAAmB,GAAnB,mBAAmB,CAAqB;QAT1C,WAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;IAUjE,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,QAShB,EAAE,MAAc;QACf,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;gBACtC,GAAG,QAAQ;gBACX,MAAM,EAAE,SAAS;gBACjB,WAAW,EAAE,MAAM;aACpB,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,oBAAoB,EACpB,SAAS,CAAC,EAAE,EACZ;gBACE,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,WAAW,EAAE,QAAQ,CAAC,WAAW;aAClC,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,EAAE;gBACzD,MAAM,EAAE,SAAS,CAAC,EAAE;gBACpB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,EAAU;QAC7B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC,aAAa,EAAE,eAAe,CAAC;aAC5C,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBAC9C,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAejB;QAMC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC;YAChC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;YACnD,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAElC,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc;iBACrC,kBAAkB,CAAC,MAAM,CAAC;iBAC1B,iBAAiB,CAAC,kBAAkB,EAAE,aAAa,CAAC;iBACpD,iBAAiB,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;YAErD,gBAAgB;YAChB,IAAI,QAAQ,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC;gBAC/B,YAAY,CAAC,QAAQ,CAAC,kCAAkC,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;YAC/F,CAAC;YAED,IAAI,QAAQ,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC;gBAClC,YAAY,CAAC,QAAQ,CAAC,wCAAwC,EAAE,EAAE,YAAY,EAAE,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;YAC3G,CAAC;YAED,IAAI,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;gBAC9B,YAAY,CAAC,QAAQ,CAAC,+BAA+B,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1F,CAAC;YAED,IAAI,QAAQ,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC;gBACjC,YAAY,CAAC,QAAQ,CAAC,uCAAuC,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;YACxG,CAAC;YAED,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;gBAC1B,YAAY,CAAC,QAAQ,CAAC,iCAAiC,EAAE,EAAE,YAAY,EAAE,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;YACpG,CAAC;YAED,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;gBAC3B,YAAY,CAAC,QAAQ,CAAC,kCAAkC,EAAE,EAAE,aAAa,EAAE,QAAQ,CAAC,aAAa,EAAE,CAAC,CAAC;YACvG,CAAC;YAED,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;gBAC5B,YAAY,CAAC,QAAQ,CAAC,qCAAqC,EAAE,EAAE,cAAc,EAAE,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC;YAC5G,CAAC;YAED,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;gBAC7B,YAAY,CAAC,QAAQ,CAAC,sCAAsC,EAAE,EAAE,eAAe,EAAE,QAAQ,CAAC,eAAe,EAAE,CAAC,CAAC;YAC/G,CAAC;YAED,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;gBAC1B,YAAY,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YACvE,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACxB,YAAY,CAAC,QAAQ,CACnB,qEAAqE,EACrE,EAAE,UAAU,EAAE,IAAI,QAAQ,CAAC,UAAU,GAAG,EAAE,CAC3C,CAAC;YACJ,CAAC;YAED,gBAAgB;YAChB,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,WAAW,CAAC;YAC9C,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,IAAI,MAAM,CAAC;YAC/C,YAAY,CAAC,OAAO,CAAC,QAAQ,MAAM,EAAE,EAAE,SAAS,CAAC,CAAC;YAElD,mBAAmB;YACnB,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;YAE5D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM;aAC5C,CAAC,CAAC;YAEH,OAAO;gBACL,KAAK;gBACL,KAAK;gBACL,IAAI;gBACJ,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACrC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,EAAU,EAAE,MAAc;QACxC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,CAAC,MAAM,SAAS,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;aACP,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,kCAAkC;YAClC,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC;gBAC3C,MAAM,IAAI,CAAC,mBAAmB,CAAC,2BAA2B,CAAC;oBACzD,MAAM,EAAE,EAAE;oBACV,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,WAAW,EAAE,MAAM;oBACnB,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU;iBACrD,CAAC,CAAC;YACL,CAAC;YAED,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,OAAO,EACP,oBAAoB,EACpB,EAAE,EACF;gBACE,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,EAAE;gBACzD,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACtD,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,OAAY,EAAE,MAAe;QAC1D,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBACjD,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,kBAAkB,EAAE,OAAO,CAAC,OAAO,EAAE,oBAAoB,IAAI,CAAC;aAC/D,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACvB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,kCAAkC;YAClC,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,UAAU,EAAE,CAAC;gBAC9C,MAAM,IAAI,CAAC,mBAAmB,CAAC,6BAA6B,CAAC;oBAC3D,MAAM,EAAE,EAAE;oBACV,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU;iBACrD,CAAC,CAAC;YACL,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,UAAU,EACV,oBAAoB,EACpB,EAAE,EACF;oBACE,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,kBAAkB,EAAE,OAAO,CAAC,OAAO,EAAE,oBAAoB,IAAI,CAAC;oBAC9D,QAAQ,EAAE,IAAI,CAAC,eAAe;iBAC/B,CACF,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2CAA2C,EAAE;gBAC3D,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,kBAAkB,EAAE,OAAO,CAAC,OAAO,EAAE,oBAAoB,IAAI,CAAC;gBAC9D,QAAQ,EAAE,IAAI,CAAC,iBAAiB;aACjC,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;gBACzD,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,EAAU,EAAE,KAAa,EAAE,MAAe;QACvD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBAC9C,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,KAAK;aACN,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,kCAAkC;YAClC,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC;gBAC3C,MAAM,IAAI,CAAC,mBAAmB,CAAC,0BAA0B,CAAC;oBACxD,MAAM,EAAE,EAAE;oBACV,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,KAAK;oBACL,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU;iBACrD,CAAC,CAAC;YACL,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,MAAM,EACN,oBAAoB,EACpB,EAAE,EACF;oBACE,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,KAAK;iBACN,CACF,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,EAAE;gBAC3C,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,KAAK;aACN,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,EAAU,EAAE,MAAc,EAAE,MAAc;QACzD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBACjD,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;gBACN,MAAM;aACP,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACpB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,oBAAoB,EACpB,EAAE,EACF;gBACE,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;aACP,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2CAA2C,EAAE;gBAC3D,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;gBACN,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,EAAU,EAAE,QAAa;QAChD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAC9B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,QAAQ,CAAC,eAAe;gBAClC,YAAY,EAAE,QAAQ,CAAC,YAAY;aACpC,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,EAAU,EAAE,MAAc;QACzC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAEvC,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,oBAAoB,EACpB,EAAE,EACF;gBACE,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,EAAE;gBACzD,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IAEG,AAAN,KAAK,CAAC,qBAAqB;QACzB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAElD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBACpD,KAAK,EAAE;oBACL,MAAM,EAAE,SAAS;oBACjB,WAAW,EAAE,IAAA,iBAAO,EAAC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,qBAAqB;iBAClF;aACF,CAAC,CAAC;YAEH,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE,CAAC;gBAClC,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;gBAClD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;wBAClD,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,KAAK,EAAE,KAAK,CAAC,OAAO;qBACrB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,EAAE;oBAC3C,KAAK,EAAE,cAAc,CAAC,MAAM;iBAC7B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF,CAAA;AA/jBY,4DAAwB;AA6hB7B;IADL,IAAA,eAAI,EAAC,yBAAc,CAAC,YAAY,CAAC;;;wDACH,OAAO,oBAAP,OAAO;qEAiCrC;mCA9jBU,wBAAwB;IADpC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,6CAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,oBAAK,CAAC,CAAA;yDADS,oBAAU,oBAAV,oBAAU,oDAET,oBAAU,oBAAV,oBAAU,oDACZ,8BAAa,oBAAb,8BAAa,oDACd,4BAAY,oBAAZ,4BAAY,oDACL,0CAAmB,oBAAnB,0CAAmB;GAVhD,wBAAwB,CA+jBpC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\vulnerability-scan.service.ts"], "sourcesContent": ["import { Injectable, Logger, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, Between, In } from 'typeorm';\r\nimport { <PERSON>ron, CronExpression } from '@nestjs/schedule';\r\nimport { VulnerabilityScan } from '../../domain/entities/vulnerability-scan.entity';\r\nimport { Asset } from '../../../asset-management/domain/entities/asset.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../infrastructure/notification/notification.service';\r\n\r\n/**\r\n * Vulnerability Scan service\r\n * Handles vulnerability scanning operations and management\r\n */\r\n@Injectable()\r\nexport class VulnerabilityScanService {\r\n  private readonly logger = new Logger(VulnerabilityScanService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(VulnerabilityScan)\r\n    private readonly scanRepository: Repository<VulnerabilityScan>,\r\n    @InjectRepository(Asset)\r\n    private readonly assetRepository: Repository<Asset>,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly auditService: AuditService,\r\n    private readonly notificationService: NotificationService,\r\n  ) {}\r\n\r\n  /**\r\n   * Create vulnerability scan\r\n   */\r\n  async createScan(scanData: {\r\n    name: string;\r\n    description?: string;\r\n    scanType: string;\r\n    scannerType: string;\r\n    scannerVersion?: string;\r\n    scanConfig: any;\r\n    tags?: string[];\r\n    scheduledAt?: Date;\r\n  }, userId: string): Promise<VulnerabilityScan> {\r\n    try {\r\n      this.logger.debug('Creating vulnerability scan', {\r\n        name: scanData.name,\r\n        scanType: scanData.scanType,\r\n        scannerType: scanData.scannerType,\r\n        userId,\r\n      });\r\n\r\n      const scan = this.scanRepository.create({\r\n        ...scanData,\r\n        status: 'pending',\r\n        initiatedBy: userId,\r\n      });\r\n\r\n      const savedScan = await this.scanRepository.save(scan);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'create',\r\n        'vulnerability_scan',\r\n        savedScan.id,\r\n        {\r\n          name: scanData.name,\r\n          scanType: scanData.scanType,\r\n          scannerType: scanData.scannerType,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Vulnerability scan created successfully', {\r\n        scanId: savedScan.id,\r\n        name: scanData.name,\r\n        userId,\r\n      });\r\n\r\n      return savedScan;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create vulnerability scan', {\r\n        error: error.message,\r\n        scanData,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get scan details\r\n   */\r\n  async getScanDetails(id: string): Promise<VulnerabilityScan> {\r\n    try {\r\n      const scan = await this.scanRepository.findOne({\r\n        where: { id },\r\n        relations: ['assessments', 'scannedAssets'],\r\n      });\r\n\r\n      if (!scan) {\r\n        throw new NotFoundException('Scan not found');\r\n      }\r\n\r\n      this.logger.debug('Scan details retrieved', {\r\n        scanId: id,\r\n        name: scan.name,\r\n        status: scan.status,\r\n      });\r\n\r\n      return scan;\r\n    } catch (error) {\r\n      this.logger.error('Failed to get scan details', {\r\n        scanId: id,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Search scans with filtering\r\n   */\r\n  async searchScans(criteria: {\r\n    page?: number;\r\n    limit?: number;\r\n    scanTypes?: string[];\r\n    scannerTypes?: string[];\r\n    statuses?: string[];\r\n    initiatedBy?: string[];\r\n    startedAfter?: Date;\r\n    startedBefore?: Date;\r\n    completedAfter?: Date;\r\n    completedBefore?: Date;\r\n    tags?: string[];\r\n    searchText?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n  }): Promise<{\r\n    scans: VulnerabilityScan[];\r\n    total: number;\r\n    page: number;\r\n    totalPages: number;\r\n  }> {\r\n    try {\r\n      const page = criteria.page || 1;\r\n      const limit = Math.min(criteria.limit || 50, 1000);\r\n      const offset = (page - 1) * limit;\r\n\r\n      const queryBuilder = this.scanRepository\r\n        .createQueryBuilder('scan')\r\n        .leftJoinAndSelect('scan.assessments', 'assessments')\r\n        .leftJoinAndSelect('scan.scannedAssets', 'assets');\r\n\r\n      // Apply filters\r\n      if (criteria.scanTypes?.length) {\r\n        queryBuilder.andWhere('scan.scanType IN (:...scanTypes)', { scanTypes: criteria.scanTypes });\r\n      }\r\n\r\n      if (criteria.scannerTypes?.length) {\r\n        queryBuilder.andWhere('scan.scannerType IN (:...scannerTypes)', { scannerTypes: criteria.scannerTypes });\r\n      }\r\n\r\n      if (criteria.statuses?.length) {\r\n        queryBuilder.andWhere('scan.status IN (:...statuses)', { statuses: criteria.statuses });\r\n      }\r\n\r\n      if (criteria.initiatedBy?.length) {\r\n        queryBuilder.andWhere('scan.initiatedBy IN (:...initiatedBy)', { initiatedBy: criteria.initiatedBy });\r\n      }\r\n\r\n      if (criteria.startedAfter) {\r\n        queryBuilder.andWhere('scan.startedAt >= :startedAfter', { startedAfter: criteria.startedAfter });\r\n      }\r\n\r\n      if (criteria.startedBefore) {\r\n        queryBuilder.andWhere('scan.startedAt <= :startedBefore', { startedBefore: criteria.startedBefore });\r\n      }\r\n\r\n      if (criteria.completedAfter) {\r\n        queryBuilder.andWhere('scan.completedAt >= :completedAfter', { completedAfter: criteria.completedAfter });\r\n      }\r\n\r\n      if (criteria.completedBefore) {\r\n        queryBuilder.andWhere('scan.completedAt <= :completedBefore', { completedBefore: criteria.completedBefore });\r\n      }\r\n\r\n      if (criteria.tags?.length) {\r\n        queryBuilder.andWhere('scan.tags && :tags', { tags: criteria.tags });\r\n      }\r\n\r\n      if (criteria.searchText) {\r\n        queryBuilder.andWhere(\r\n          '(scan.name ILIKE :searchText OR scan.description ILIKE :searchText)',\r\n          { searchText: `%${criteria.searchText}%` }\r\n        );\r\n      }\r\n\r\n      // Apply sorting\r\n      const sortBy = criteria.sortBy || 'createdAt';\r\n      const sortOrder = criteria.sortOrder || 'DESC';\r\n      queryBuilder.orderBy(`scan.${sortBy}`, sortOrder);\r\n\r\n      // Apply pagination\r\n      queryBuilder.skip(offset).take(limit);\r\n\r\n      const [scans, total] = await queryBuilder.getManyAndCount();\r\n\r\n      this.logger.debug('Scan search completed', {\r\n        total,\r\n        page,\r\n        limit,\r\n        criteriaCount: Object.keys(criteria).length,\r\n      });\r\n\r\n      return {\r\n        scans,\r\n        total,\r\n        page,\r\n        totalPages: Math.ceil(total / limit),\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to search scans', {\r\n        error: error.message,\r\n        criteria,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Start scan\r\n   */\r\n  async startScan(id: string, userId: string): Promise<VulnerabilityScan> {\r\n    try {\r\n      const scan = await this.scanRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!scan) {\r\n        throw new NotFoundException('Scan not found');\r\n      }\r\n\r\n      if (scan.status !== 'pending') {\r\n        throw new Error(`Cannot start scan in ${scan.status} status`);\r\n      }\r\n\r\n      this.logger.debug('Starting vulnerability scan', {\r\n        scanId: id,\r\n        name: scan.name,\r\n        userId,\r\n      });\r\n\r\n      scan.start();\r\n      const savedScan = await this.scanRepository.save(scan);\r\n\r\n      // Send notification if configured\r\n      if (scan.scanConfig.notifications?.onStart) {\r\n        await this.notificationService.sendScanStartedNotification({\r\n          scanId: id,\r\n          scanName: scan.name,\r\n          scanType: scan.scanType,\r\n          initiatedBy: userId,\r\n          recipients: scan.scanConfig.notifications.recipients,\r\n        });\r\n      }\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'start',\r\n        'vulnerability_scan',\r\n        id,\r\n        {\r\n          name: scan.name,\r\n          scanType: scan.scanType,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Vulnerability scan started successfully', {\r\n        scanId: id,\r\n        name: scan.name,\r\n        userId,\r\n      });\r\n\r\n      return savedScan;\r\n    } catch (error) {\r\n      this.logger.error('Failed to start vulnerability scan', {\r\n        scanId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Complete scan\r\n   */\r\n  async completeScan(id: string, results: any, userId?: string): Promise<VulnerabilityScan> {\r\n    try {\r\n      const scan = await this.scanRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!scan) {\r\n        throw new NotFoundException('Scan not found');\r\n      }\r\n\r\n      this.logger.debug('Completing vulnerability scan', {\r\n        scanId: id,\r\n        name: scan.name,\r\n        vulnerabilityCount: results.summary?.totalVulnerabilities || 0,\r\n      });\r\n\r\n      scan.complete(results);\r\n      const savedScan = await this.scanRepository.save(scan);\r\n\r\n      // Send notification if configured\r\n      if (scan.scanConfig.notifications?.onComplete) {\r\n        await this.notificationService.sendScanCompletedNotification({\r\n          scanId: id,\r\n          scanName: scan.name,\r\n          scanType: scan.scanType,\r\n          results: results.summary,\r\n          recipients: scan.scanConfig.notifications.recipients,\r\n        });\r\n      }\r\n\r\n      if (userId) {\r\n        await this.auditService.logUserAction(\r\n          userId,\r\n          'complete',\r\n          'vulnerability_scan',\r\n          id,\r\n          {\r\n            name: scan.name,\r\n            vulnerabilityCount: results.summary?.totalVulnerabilities || 0,\r\n            duration: scan.durationSeconds,\r\n          },\r\n        );\r\n      }\r\n\r\n      this.logger.log('Vulnerability scan completed successfully', {\r\n        scanId: id,\r\n        name: scan.name,\r\n        vulnerabilityCount: results.summary?.totalVulnerabilities || 0,\r\n        duration: scan.durationFormatted,\r\n      });\r\n\r\n      return savedScan;\r\n    } catch (error) {\r\n      this.logger.error('Failed to complete vulnerability scan', {\r\n        scanId: id,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fail scan\r\n   */\r\n  async failScan(id: string, error: string, userId?: string): Promise<VulnerabilityScan> {\r\n    try {\r\n      const scan = await this.scanRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!scan) {\r\n        throw new NotFoundException('Scan not found');\r\n      }\r\n\r\n      this.logger.debug('Failing vulnerability scan', {\r\n        scanId: id,\r\n        name: scan.name,\r\n        error,\r\n      });\r\n\r\n      scan.fail(error);\r\n      const savedScan = await this.scanRepository.save(scan);\r\n\r\n      // Send notification if configured\r\n      if (scan.scanConfig.notifications?.onError) {\r\n        await this.notificationService.sendScanFailedNotification({\r\n          scanId: id,\r\n          scanName: scan.name,\r\n          scanType: scan.scanType,\r\n          error,\r\n          recipients: scan.scanConfig.notifications.recipients,\r\n        });\r\n      }\r\n\r\n      if (userId) {\r\n        await this.auditService.logUserAction(\r\n          userId,\r\n          'fail',\r\n          'vulnerability_scan',\r\n          id,\r\n          {\r\n            name: scan.name,\r\n            error,\r\n          },\r\n        );\r\n      }\r\n\r\n      this.logger.log('Vulnerability scan failed', {\r\n        scanId: id,\r\n        name: scan.name,\r\n        error,\r\n      });\r\n\r\n      return savedScan;\r\n    } catch (error) {\r\n      this.logger.error('Failed to fail vulnerability scan', {\r\n        scanId: id,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cancel scan\r\n   */\r\n  async cancelScan(id: string, reason: string, userId: string): Promise<VulnerabilityScan> {\r\n    try {\r\n      const scan = await this.scanRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!scan) {\r\n        throw new NotFoundException('Scan not found');\r\n      }\r\n\r\n      this.logger.debug('Cancelling vulnerability scan', {\r\n        scanId: id,\r\n        name: scan.name,\r\n        reason,\r\n        userId,\r\n      });\r\n\r\n      scan.cancel(reason);\r\n      const savedScan = await this.scanRepository.save(scan);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'cancel',\r\n        'vulnerability_scan',\r\n        id,\r\n        {\r\n          name: scan.name,\r\n          reason,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Vulnerability scan cancelled successfully', {\r\n        scanId: id,\r\n        name: scan.name,\r\n        reason,\r\n        userId,\r\n      });\r\n\r\n      return savedScan;\r\n    } catch (error) {\r\n      this.logger.error('Failed to cancel vulnerability scan', {\r\n        scanId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update scan progress\r\n   */\r\n  async updateScanProgress(id: string, progress: any): Promise<VulnerabilityScan> {\r\n    try {\r\n      const scan = await this.scanRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!scan) {\r\n        throw new NotFoundException('Scan not found');\r\n      }\r\n\r\n      scan.updateProgress(progress);\r\n      const savedScan = await this.scanRepository.save(scan);\r\n\r\n      this.logger.debug('Scan progress updated', {\r\n        scanId: id,\r\n        progress: progress.overallProgress,\r\n        currentPhase: progress.currentPhase,\r\n      });\r\n\r\n      return savedScan;\r\n    } catch (error) {\r\n      this.logger.error('Failed to update scan progress', {\r\n        scanId: id,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete scan\r\n   */\r\n  async deleteScan(id: string, userId: string): Promise<void> {\r\n    try {\r\n      const scan = await this.scanRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!scan) {\r\n        throw new NotFoundException('Scan not found');\r\n      }\r\n\r\n      if (scan.isRunning) {\r\n        throw new Error('Cannot delete running scan');\r\n      }\r\n\r\n      this.logger.debug('Deleting vulnerability scan', {\r\n        scanId: id,\r\n        name: scan.name,\r\n        userId,\r\n      });\r\n\r\n      await this.scanRepository.remove(scan);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'delete',\r\n        'vulnerability_scan',\r\n        id,\r\n        {\r\n          name: scan.name,\r\n          scanType: scan.scanType,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Vulnerability scan deleted successfully', {\r\n        scanId: id,\r\n        name: scan.name,\r\n        userId,\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to delete vulnerability scan', {\r\n        scanId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Monitor scheduled scans\r\n   */\r\n  @Cron(CronExpression.EVERY_MINUTE)\r\n  async monitorScheduledScans(): Promise<void> {\r\n    try {\r\n      this.logger.debug('Checking for scheduled scans');\r\n\r\n      const now = new Date();\r\n      const scheduledScans = await this.scanRepository.find({\r\n        where: {\r\n          status: 'pending',\r\n          scheduledAt: Between(new Date(now.getTime() - 60000), now), // Within last minute\r\n        },\r\n      });\r\n\r\n      for (const scan of scheduledScans) {\r\n        try {\r\n          await this.startScan(scan.id, scan.initiatedBy);\r\n        } catch (error) {\r\n          this.logger.error('Failed to start scheduled scan', {\r\n            scanId: scan.id,\r\n            error: error.message,\r\n          });\r\n        }\r\n      }\r\n\r\n      if (scheduledScans.length > 0) {\r\n        this.logger.log('Processed scheduled scans', {\r\n          count: scheduledScans.length,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      this.logger.error('Failed to monitor scheduled scans', {\r\n        error: error.message,\r\n      });\r\n    }\r\n  }\r\n}\r\n"], "version": 3}