31806d0165520e5ae4f6619b5d376604
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CorrelationService_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorrelationService = void 0;
const common_1 = require("@nestjs/common");
const correlated_event_entity_1 = require("../../domain/entities/event/correlated-event.entity");
const domain_event_publisher_1 = require("../../../shared-kernel/domain/domain-event-publisher");
const logger_1 = require("../../../shared-kernel/infrastructure/logger");
/**
 * Correlation Application Service
 *
 * Analyzes enriched events to identify relationships and patterns
 * that indicate coordinated attacks or related security incidents.
 *
 * Key responsibilities:
 * - Multi-dimensional event correlation
 * - Attack chain reconstruction
 * - Campaign attribution
 * - Pattern recognition and analysis
 * - Threat escalation based on correlations
 */
let CorrelationService = CorrelationService_1 = class CorrelationService {
    constructor(eventPublisher) {
        this.eventPublisher = eventPublisher;
        this.logger = new logger_1.Logger(CorrelationService_1.name);
        this.defaultConfig = {
            temporalWindow: 60, // 60 minutes
            minEvents: 2,
            minConfidence: 50,
            maxEvents: 100,
            enabledTypes: Object.values(correlated_event_entity_1.CorrelationType),
            algorithms: ['temporal', 'spatial', 'indicator', 'behavioral'],
        };
    }
    /**
     * Correlate enriched events
     */
    async correlateEvents(request) {
        const config = { ...this.defaultConfig, ...request.config };
        const startTime = Date.now();
        this.logger.info('Starting event correlation', {
            eventCount: request.events.length,
            timeWindow: request.timeWindow || config.temporalWindow,
            enabledTypes: config.enabledTypes,
        });
        const result = {
            success: false,
            correlatedEvents: [],
            duration: 0,
            statistics: {
                eventsProcessed: request.events.length,
                correlationsFound: 0,
                averageConfidence: 0,
                typeDistribution: {},
            },
            errors: [],
        };
        try {
            // Filter and prepare events
            const eligibleEvents = this.filterEligibleEvents(request.events, config);
            if (eligibleEvents.length < config.minEvents) {
                this.logger.warn('Insufficient events for correlation', {
                    eligibleEvents: eligibleEvents.length,
                    minRequired: config.minEvents,
                });
                result.success = true; // Not an error, just no correlations
                return result;
            }
            // Perform different types of correlation
            const correlations = [];
            for (const correlationType of config.enabledTypes) {
                if (request.focusTypes && !request.focusTypes.includes(correlationType)) {
                    continue;
                }
                try {
                    const typeCorrelations = await this.performCorrelationType(correlationType, eligibleEvents, config);
                    correlations.push(...typeCorrelations);
                }
                catch (error) {
                    this.logger.error(`Correlation type ${correlationType} failed`, {
                        error: error.message,
                        eventCount: eligibleEvents.length,
                    });
                    result.errors.push({
                        type: correlationType,
                        message: error.message,
                        affectedEvents: eligibleEvents.map(e => e.id.toString()),
                    });
                }
            }
            // Remove duplicate correlations and merge similar ones
            const uniqueCorrelations = this.deduplicateCorrelations(correlations);
            // Publish domain events for correlations
            for (const correlation of uniqueCorrelations) {
                await this.eventPublisher.publishAll(correlation.getDomainEvents());
                correlation.clearDomainEvents();
            }
            result.correlatedEvents = uniqueCorrelations;
            result.statistics = this.calculateStatistics(uniqueCorrelations);
            result.success = true;
            this.logger.info('Event correlation completed', {
                eventsProcessed: eligibleEvents.length,
                correlationsFound: uniqueCorrelations.length,
                averageConfidence: result.statistics.averageConfidence,
                duration: result.duration,
            });
        }
        catch (error) {
            this.logger.error('Event correlation failed', {
                error: error.message,
                stack: error.stack,
                eventCount: request.events.length,
            });
            result.errors.push({
                type: 'general',
                message: error.message,
                affectedEvents: request.events.map(e => e.id.toString()),
            });
        }
        finally {
            result.duration = Date.now() - startTime;
        }
        return result;
    }
    /**
     * Perform real-time correlation for streaming events
     */
    async correlateStreamingEvents(newEvent, recentEvents, config) {
        const correlationConfig = { ...this.defaultConfig, ...config };
        // Filter recent events within time window
        const timeWindow = correlationConfig.temporalWindow * 60 * 1000; // Convert to milliseconds
        const cutoffTime = new Date(Date.now() - timeWindow);
        const eligibleEvents = recentEvents.filter(event => event.createdAt && event.createdAt >= cutoffTime);
        // Add the new event
        const allEvents = [newEvent, ...eligibleEvents];
        const correlationRequest = {
            events: allEvents,
            config: correlationConfig,
            focusTypes: [correlated_event_entity_1.CorrelationType.TEMPORAL, correlated_event_entity_1.CorrelationType.INDICATOR],
        };
        const result = await this.correlateEvents(correlationRequest);
        return result.correlatedEvents;
    }
    /**
     * Filter events eligible for correlation
     */
    filterEligibleEvents(events, config) {
        return events
            .filter(event => event.isReadyForCorrelation())
            .filter(event => event.enrichmentScore >= config.minConfidence)
            .slice(0, config.maxEvents)
            .sort((a, b) => b.enrichmentScore - a.enrichmentScore);
    }
    /**
     * Perform specific correlation type
     */
    async performCorrelationType(type, events, config) {
        switch (type) {
            case correlated_event_entity_1.CorrelationType.TEMPORAL:
                return this.performTemporalCorrelation(events, config);
            case correlated_event_entity_1.CorrelationType.SPATIAL:
                return this.performSpatialCorrelation(events, config);
            case correlated_event_entity_1.CorrelationType.INDICATOR:
                return this.performIndicatorCorrelation(events, config);
            case correlated_event_entity_1.CorrelationType.BEHAVIORAL:
                return this.performBehavioralCorrelation(events, config);
            case correlated_event_entity_1.CorrelationType.CAMPAIGN:
                return this.performCampaignCorrelation(events, config);
            case correlated_event_entity_1.CorrelationType.ATTACK_CHAIN:
                return this.performAttackChainCorrelation(events, config);
            default:
                return [];
        }
    }
    /**
     * Perform temporal correlation
     */
    async performTemporalCorrelation(events, config) {
        const correlations = [];
        const timeWindow = config.temporalWindow * 60 * 1000; // Convert to milliseconds
        // Group events by time windows
        const timeGroups = this.groupEventsByTime(events, timeWindow);
        for (const group of timeGroups) {
            if (group.length < config.minEvents)
                continue;
            const [primaryEvent, ...relatedEvents] = group;
            const analysis = {
                type: correlated_event_entity_1.CorrelationType.TEMPORAL,
                patterns: [{
                        type: 'temporal_burst',
                        description: `${group.length} events within ${config.temporalWindow} minutes`,
                        confidence: this.calculateTemporalConfidence(group),
                        matchingEvents: group.map(e => e.id.toString()),
                        attributes: { timeWindow: config.temporalWindow },
                        strength: Math.min(1.0, group.length / 10),
                    }],
                commonIndicators: this.findCommonIndicators(group),
                temporalCorrelation: {
                    timeWindow,
                    sequence: group.map((event, index) => ({
                        eventId: event.id.toString(),
                        timestamp: event.createdAt || new Date(),
                        order: index,
                    })),
                    timeGaps: this.calculateTimeGaps(group),
                    patternType: this.determineTemporalPattern(group),
                    regularity: this.calculateTemporalRegularity(group),
                },
            };
            const correlation = correlated_event_entity_1.CorrelatedEvent.create(primaryEvent, relatedEvents, analysis, {
                engineVersion: '1.0',
                algorithmsUsed: ['temporal_clustering'],
                processingDuration: 0,
                correlatedAt: new Date(),
                dataSources: ['enriched_events'],
                rulesApplied: ['temporal_window'],
                performanceMetrics: {
                    eventsProcessed: group.length,
                    correlationsFound: 1,
                    falsePositives: 0,
                    processingRate: 1000,
                },
            });
            correlations.push(correlation);
        }
        return correlations;
    }
    /**
     * Perform indicator-based correlation
     */
    async performIndicatorCorrelation(events, config) {
        const correlations = [];
        // Group events by common indicators
        const indicatorGroups = this.groupEventsByIndicators(events);
        for (const [indicators, group] of indicatorGroups) {
            if (group.length < config.minEvents)
                continue;
            const [primaryEvent, ...relatedEvents] = group;
            const analysis = {
                type: correlated_event_entity_1.CorrelationType.INDICATOR,
                patterns: [{
                        type: 'common_indicators',
                        description: `${indicators.length} common indicators across ${group.length} events`,
                        confidence: this.calculateIndicatorConfidence(indicators, group),
                        matchingEvents: group.map(e => e.id.toString()),
                        attributes: { indicatorCount: indicators.length },
                        strength: Math.min(1.0, indicators.length / 5),
                    }],
                commonIndicators: indicators,
            };
            const correlation = correlated_event_entity_1.CorrelatedEvent.create(primaryEvent, relatedEvents, analysis, {
                engineVersion: '1.0',
                algorithmsUsed: ['indicator_matching'],
                processingDuration: 0,
                correlatedAt: new Date(),
                dataSources: ['threat_intelligence'],
                rulesApplied: ['common_indicators'],
                performanceMetrics: {
                    eventsProcessed: group.length,
                    correlationsFound: 1,
                    falsePositives: 0,
                    processingRate: 1000,
                },
            });
            correlations.push(correlation);
        }
        return correlations;
    }
    /**
     * Perform spatial correlation
     */
    async performSpatialCorrelation(events, config) {
        // Implementation would analyze network proximity, geographic location, etc.
        return [];
    }
    /**
     * Perform behavioral correlation
     */
    async performBehavioralCorrelation(events, config) {
        // Implementation would analyze user behavior patterns, anomalies, etc.
        return [];
    }
    /**
     * Perform campaign correlation
     */
    async performCampaignCorrelation(events, config) {
        // Implementation would analyze threat actor attribution, TTPs, etc.
        return [];
    }
    /**
     * Perform attack chain correlation
     */
    async performAttackChainCorrelation(events, config) {
        // Implementation would analyze MITRE ATT&CK techniques, kill chain phases, etc.
        return [];
    }
    /**
     * Group events by time windows
     */
    groupEventsByTime(events, timeWindow) {
        const groups = [];
        const sortedEvents = events.sort((a, b) => (a.createdAt?.getTime() || 0) - (b.createdAt?.getTime() || 0));
        let currentGroup = [];
        let groupStartTime = null;
        for (const event of sortedEvents) {
            const eventTime = event.createdAt?.getTime() || 0;
            if (groupStartTime === null || eventTime - groupStartTime <= timeWindow) {
                currentGroup.push(event);
                if (groupStartTime === null) {
                    groupStartTime = eventTime;
                }
            }
            else {
                if (currentGroup.length >= 2) {
                    groups.push(currentGroup);
                }
                currentGroup = [event];
                groupStartTime = eventTime;
            }
        }
        if (currentGroup.length >= 2) {
            groups.push(currentGroup);
        }
        return groups;
    }
    /**
     * Group events by common indicators
     */
    groupEventsByIndicators(events) {
        const groups = new Map();
        for (const event of events) {
            const indicators = event.indicators;
            if (indicators.length === 0)
                continue;
            // Create a key from sorted indicator values
            const key = indicators
                .map(ioc => `${ioc.type}:${ioc.value}`)
                .sort()
                .join('|');
            if (!groups.has(key)) {
                groups.set(key, []);
            }
            groups.get(key).push(event);
        }
        // Convert back to IOC[] keys
        const result = new Map();
        for (const [key, eventGroup] of groups) {
            if (eventGroup.length >= 2) {
                const indicators = eventGroup[0].indicators;
                result.set(indicators, eventGroup);
            }
        }
        return result;
    }
    /**
     * Find common indicators across events
     */
    findCommonIndicators(events) {
        if (events.length === 0)
            return [];
        const indicatorSets = events.map(event => new Set(event.indicators.map(ioc => `${ioc.type}:${ioc.value}`)));
        const commonIndicatorKeys = indicatorSets.reduce((common, current) => new Set([...common].filter(x => current.has(x))));
        // Convert back to IOC objects
        const firstEvent = events[0];
        return firstEvent.indicators.filter(ioc => commonIndicatorKeys.has(`${ioc.type}:${ioc.value}`));
    }
    /**
     * Calculate temporal confidence
     */
    calculateTemporalConfidence(events) {
        const baseConfidence = Math.min(90, events.length * 15);
        const qualityBonus = events.reduce((sum, e) => sum + e.enrichmentScore, 0) / events.length * 0.1;
        return Math.min(100, baseConfidence + qualityBonus);
    }
    /**
     * Calculate indicator confidence
     */
    calculateIndicatorConfidence(indicators, events) {
        const baseConfidence = Math.min(95, indicators.length * 20);
        const eventCountBonus = Math.min(20, events.length * 5);
        return Math.min(100, baseConfidence + eventCountBonus);
    }
    /**
     * Calculate time gaps between events
     */
    calculateTimeGaps(events) {
        const sortedEvents = events.sort((a, b) => (a.createdAt?.getTime() || 0) - (b.createdAt?.getTime() || 0));
        const gaps = [];
        for (let i = 1; i < sortedEvents.length; i++) {
            const gap = (sortedEvents[i].createdAt?.getTime() || 0) -
                (sortedEvents[i - 1].createdAt?.getTime() || 0);
            gaps.push(gap);
        }
        return gaps;
    }
    /**
     * Determine temporal pattern type
     */
    determineTemporalPattern(events) {
        const timeGaps = this.calculateTimeGaps(events);
        if (timeGaps.every(gap => gap < 60000)) { // All within 1 minute
            return 'simultaneous';
        }
        if (timeGaps.length <= 2) {
            return 'sequential';
        }
        const avgGap = timeGaps.reduce((sum, gap) => sum + gap, 0) / timeGaps.length;
        const variance = timeGaps.reduce((sum, gap) => sum + Math.pow(gap - avgGap, 2), 0) / timeGaps.length;
        if (variance < avgGap * 0.1) {
            return 'periodic';
        }
        return 'burst';
    }
    /**
     * Calculate temporal regularity
     */
    calculateTemporalRegularity(events) {
        const timeGaps = this.calculateTimeGaps(events);
        if (timeGaps.length === 0)
            return 100;
        const avgGap = timeGaps.reduce((sum, gap) => sum + gap, 0) / timeGaps.length;
        const variance = timeGaps.reduce((sum, gap) => sum + Math.pow(gap - avgGap, 2), 0) / timeGaps.length;
        const stdDev = Math.sqrt(variance);
        const regularity = Math.max(0, 100 - (stdDev / avgGap) * 100);
        return Math.min(100, regularity);
    }
    /**
     * Remove duplicate correlations
     */
    deduplicateCorrelations(correlations) {
        const seen = new Set();
        const unique = [];
        for (const correlation of correlations) {
            const key = correlation.allEventIds
                .map(id => id.toString())
                .sort()
                .join('|');
            if (!seen.has(key)) {
                seen.add(key);
                unique.push(correlation);
            }
        }
        return unique;
    }
    /**
     * Calculate correlation statistics
     */
    calculateStatistics(correlations) {
        const typeDistribution = {};
        for (const type of Object.values(correlated_event_entity_1.CorrelationType)) {
            typeDistribution[type] = 0;
        }
        let totalConfidence = 0;
        for (const correlation of correlations) {
            typeDistribution[correlation.correlationType]++;
            totalConfidence += correlation.score;
        }
        return {
            eventsProcessed: correlations.reduce((sum, c) => sum + c.eventCount, 0),
            correlationsFound: correlations.length,
            averageConfidence: correlations.length > 0 ? totalConfidence / correlations.length : 0,
            typeDistribution,
        };
    }
};
exports.CorrelationService = CorrelationService;
exports.CorrelationService = CorrelationService = CorrelationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof domain_event_publisher_1.DomainEventPublisher !== "undefined" && domain_event_publisher_1.DomainEventPublisher) === "function" ? _a : Object])
], CorrelationService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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