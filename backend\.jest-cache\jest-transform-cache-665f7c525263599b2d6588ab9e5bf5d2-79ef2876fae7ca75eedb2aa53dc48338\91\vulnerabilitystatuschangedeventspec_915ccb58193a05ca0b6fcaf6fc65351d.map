{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\vulnerability-status-changed.event.spec.ts", "mappings": ";;AAAA,8FAA6H;AAC7H,4FAAwF;AACxF,gEAA8D;AAC9D,yCAA+B;AAC/B,yCAAqC;AAuDrC,yCAAuC;AAGvC,IAAA,oBAAQ,EAAC,iCAAiC,EAAE,GAAG,EAAE;IAC/C,IAAI,WAA2B,CAAC;IAChC,IAAI,aAAkD,CAAC;IAEvD,IAAA,sBAAU,EAAC,GAAG,EAAE;QACd,WAAW,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;QACxC,aAAa,GAAG;YACd,eAAe,EAAE,UAAU;YAC3B,SAAS,EAAE,0CAAmB,CAAC,UAAU;YACzC,SAAS,EAAE,0CAAmB,CAAC,SAAS;YACxC,MAAM,EAAE,4BAA4B;YACpC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,kBAAkB;YAC7B,OAAO,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE;SACpC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,IAAA,cAAE,EAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YAE9E,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,0CAAmB,CAAC,UAAU,CAAC,CAAC;YAC7D,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,0CAAmB,CAAC,SAAS,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,WAAW,GAAwC;gBACvD,eAAe,EAAE,UAAU;gBAC3B,SAAS,EAAE,0CAAmB,CAAC,UAAU;gBACzC,SAAS,EAAE,0CAAmB,CAAC,SAAS;gBACxC,MAAM,EAAE,eAAe;gBACvB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;YAEF,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAE5E,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,IAAA,oBAAQ,EAAC,qBAAqB,EAAE,GAAG,EAAE;YACnC,IAAA,cAAE,EAAC,qCAAqC,EAAE,GAAG,EAAE;gBAC7C,MAAM,gBAAgB,GAAG;oBACvB,EAAE,IAAI,EAAE,0CAAmB,CAAC,UAAU,EAAE,EAAE,EAAE,0CAAmB,CAAC,SAAS,EAAE;oBAC3E,EAAE,IAAI,EAAE,0CAAmB,CAAC,SAAS,EAAE,EAAE,EAAE,0CAAmB,CAAC,OAAO,EAAE;oBACxE,EAAE,IAAI,EAAE,0CAAmB,CAAC,OAAO,EAAE,EAAE,EAAE,0CAAmB,CAAC,WAAW,EAAE;oBAC1E,EAAE,IAAI,EAAE,0CAAmB,CAAC,WAAW,EAAE,EAAE,EAAE,0CAAmB,CAAC,UAAU,EAAE;oBAC7E,EAAE,IAAI,EAAE,0CAAmB,CAAC,UAAU,EAAE,EAAE,EAAE,0CAAmB,CAAC,QAAQ,EAAE;oBAC1E,EAAE,IAAI,EAAE,0CAAmB,CAAC,QAAQ,EAAE,EAAE,EAAE,0CAAmB,CAAC,MAAM,EAAE;iBACvE,CAAC;gBAEF,gBAAgB,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;oBACxC,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;wBAC7D,GAAG,aAAa;wBAChB,SAAS,EAAE,IAAI;wBACf,SAAS,EAAE,EAAE;qBACd,CAAC,CAAC;oBACH,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjD,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,+CAA+C,EAAE,GAAG,EAAE;gBACvD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,UAAU;oBACzC,SAAS,EAAE,0CAAmB,CAAC,OAAO;iBACvC,CAAC,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,UAAU;oBACzC,SAAS,EAAE,0CAAmB,CAAC,cAAc;iBAC9C,CAAC,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,IAAA,cAAE,EAAC,mCAAmC,EAAE,GAAG,EAAE;gBAC3C,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,UAAU;oBACzC,SAAS,EAAE,0CAAmB,CAAC,WAAW;iBAC3C,CAAC,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,+CAA+C,EAAE,GAAG,EAAE;gBACvD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,UAAU;oBACzC,SAAS,EAAE,0CAAmB,CAAC,SAAS;iBACzC,CAAC,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,UAAU;oBACzC,SAAS,EAAE,0CAAmB,CAAC,cAAc;iBAC9C,CAAC,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,wBAAwB,EAAE,GAAG,EAAE;YACtC,IAAA,cAAE,EAAC,kCAAkC,EAAE,GAAG,EAAE;gBAC1C,MAAM,eAAe,GAAG;oBACtB,0CAAmB,CAAC,UAAU;oBAC9B,0CAAmB,CAAC,QAAQ;oBAC5B,0CAAmB,CAAC,MAAM;iBAC3B,CAAC;gBAEF,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBAC/B,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;wBAC7D,GAAG,aAAa;wBAChB,SAAS,EAAE,MAAM;qBAClB,CAAC,CAAC;oBACH,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpD,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,0CAA0C,EAAE,GAAG,EAAE;gBAClD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,cAAc;iBAC9C,CAAC,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,2BAA2B,EAAE,GAAG,EAAE;YACzC,IAAA,cAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;gBAC5C,MAAM,iBAAiB,GAAG;oBACxB,0CAAmB,CAAC,cAAc;oBAClC,0CAAmB,CAAC,aAAa;iBAClC,CAAC;gBAEF,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBACjC,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;wBAC7D,GAAG,aAAa;wBAChB,SAAS,EAAE,MAAM;qBAClB,CAAC,CAAC;oBACH,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvD,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,UAAU;iBAC1C,CAAC,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,0BAA0B,EAAE,GAAG,EAAE;YACxC,IAAA,cAAE,EAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,MAAM;oBACrC,SAAS,EAAE,0CAAmB,CAAC,UAAU;iBAC1C,CAAC,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,cAAc;oBAC7C,SAAS,EAAE,0CAAmB,CAAC,SAAS;iBACzC,CAAC,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,qDAAqD,EAAE,GAAG,EAAE;gBAC7D,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,UAAU;oBACzC,SAAS,EAAE,0CAAmB,CAAC,SAAS;iBACzC,CAAC,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,IAAA,cAAE,EAAC,gDAAgD,EAAE,GAAG,EAAE;gBACxD,MAAM,SAAS,GAAG;oBAChB;wBACE,IAAI,EAAE,0CAAmB,CAAC,UAAU;wBACpC,EAAE,EAAE,0CAAmB,CAAC,UAAU;wBAClC,QAAQ,EAAE,SAAS;qBACpB;oBACD;wBACE,IAAI,EAAE,0CAAmB,CAAC,UAAU;wBACpC,EAAE,EAAE,0CAAmB,CAAC,cAAc;wBACtC,QAAQ,EAAE,WAAW;qBACtB;oBACD;wBACE,IAAI,EAAE,0CAAmB,CAAC,MAAM;wBAChC,EAAE,EAAE,0CAAmB,CAAC,UAAU;wBAClC,QAAQ,EAAE,WAAW;qBACtB;oBACD;wBACE,IAAI,EAAE,0CAAmB,CAAC,UAAU;wBACpC,EAAE,EAAE,0CAAmB,CAAC,SAAS;wBACjC,QAAQ,EAAE,aAAa;qBACxB;oBACD;wBACE,IAAI,EAAE,0CAAmB,CAAC,UAAU;wBACpC,EAAE,EAAE,0CAAmB,CAAC,WAAW;wBACnC,QAAQ,EAAE,WAAW;qBACtB;oBACD;wBACE,IAAI,EAAE,0CAAmB,CAAC,OAAO;wBACjC,EAAE,EAAE,0CAAmB,CAAC,OAAO;wBAC/B,QAAQ,EAAE,SAAS;qBACpB;iBACF,CAAC;gBAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE;oBAC3C,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;wBAC7D,GAAG,aAAa;wBAChB,SAAS,EAAE,IAAI;wBACf,SAAS,EAAE,EAAE;qBACd,CAAC,CAAC;oBACH,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACnD,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,IAAA,oBAAQ,EAAC,yBAAyB,EAAE,GAAG,EAAE;YACvC,IAAA,cAAE,EAAC,kCAAkC,EAAE,GAAG,EAAE;gBAC1C,MAAM,SAAS,GAAG;oBAChB,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE;oBACvC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE;oBACtC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE;oBACvC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE;oBACxC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE;iBACzC,CAAC;gBAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;oBACvC,IAAI,IAAyB,EAAE,EAAuB,CAAC;oBAEvD,QAAQ,IAAI,EAAE,CAAC;wBACb,KAAK,SAAS;4BACZ,IAAI,GAAG,0CAAmB,CAAC,UAAU,CAAC;4BACtC,EAAE,GAAG,0CAAmB,CAAC,UAAU,CAAC;4BACpC,MAAM;wBACR,KAAK,WAAW;4BACd,IAAI,GAAG,0CAAmB,CAAC,UAAU,CAAC;4BACtC,EAAE,GAAG,0CAAmB,CAAC,cAAc,CAAC;4BACxC,MAAM;wBACR,KAAK,WAAW;4BACd,IAAI,GAAG,0CAAmB,CAAC,MAAM,CAAC;4BAClC,EAAE,GAAG,0CAAmB,CAAC,UAAU,CAAC;4BACpC,MAAM;wBACR,KAAK,YAAY;4BACf,IAAI,GAAG,0CAAmB,CAAC,UAAU,CAAC;4BACtC,EAAE,GAAG,0CAAmB,CAAC,WAAW,CAAC;4BACrC,MAAM;wBACR,KAAK,aAAa;4BAChB,IAAI,GAAG,0CAAmB,CAAC,UAAU,CAAC;4BACtC,EAAE,GAAG,0CAAmB,CAAC,SAAS,CAAC;4BACnC,MAAM;wBACR;4BACE,IAAI,GAAG,0CAAmB,CAAC,UAAU,CAAC;4BACtC,EAAE,GAAG,0CAAmB,CAAC,SAAS,CAAC;oBACvC,CAAC;oBAED,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;wBAC7D,GAAG,aAAa;wBAChB,SAAS,EAAE,IAAI;wBACf,SAAS,EAAE,EAAE;qBACd,CAAC,CAAC;oBACH,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACzD,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,0BAA0B,EAAE,GAAG,EAAE;YACxC,IAAA,cAAE,EAAC,sDAAsD,EAAE,GAAG,EAAE;gBAC9D,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,UAAU;oBACzC,SAAS,EAAE,0CAAmB,CAAC,UAAU;iBAC1C,CAAC,CAAC;gBAEH,MAAM,aAAa,GAAG,KAAK,CAAC,wBAAwB,EAAE,CAAC;gBACvD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAClD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBACpD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBACtD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;gBACjE,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;gBAC3D,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,wDAAwD,EAAE,GAAG,EAAE;gBAChE,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,MAAM;oBACrC,SAAS,EAAE,0CAAmB,CAAC,UAAU;iBAC1C,CAAC,CAAC;gBAEH,MAAM,aAAa,GAAG,KAAK,CAAC,wBAAwB,EAAE,CAAC;gBACvD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAClD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;gBAC5D,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBACzD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,IAAA,oBAAQ,EAAC,6BAA6B,EAAE,GAAG,EAAE;YAC3C,IAAA,cAAE,EAAC,oDAAoD,EAAE,GAAG,EAAE;gBAC5D,MAAM,SAAS,GAAG;oBAChB;wBACE,MAAM,EAAE,0CAAmB,CAAC,SAAS;wBACrC,eAAe,EAAE,CAAC,yBAAyB,EAAE,wBAAwB,CAAC;qBACvE;oBACD;wBACE,MAAM,EAAE,0CAAmB,CAAC,OAAO;wBACnC,eAAe,EAAE,CAAC,0BAA0B,EAAE,0BAA0B,CAAC;qBAC1E;oBACD;wBACE,MAAM,EAAE,0CAAmB,CAAC,WAAW;wBACvC,eAAe,EAAE,CAAC,4BAA4B,EAAE,qBAAqB,CAAC;qBACvE;oBACD;wBACE,MAAM,EAAE,0CAAmB,CAAC,UAAU;wBACtC,eAAe,EAAE,CAAC,uBAAuB,EAAE,wBAAwB,CAAC;qBACrE;oBACD;wBACE,MAAM,EAAE,0CAAmB,CAAC,QAAQ;wBACpC,eAAe,EAAE,CAAC,qBAAqB,EAAE,gBAAgB,CAAC;qBAC3D;oBACD;wBACE,MAAM,EAAE,0CAAmB,CAAC,MAAM;wBAClC,eAAe,EAAE,CAAC,uBAAuB,EAAE,yBAAyB,CAAC;qBACtE;oBACD;wBACE,MAAM,EAAE,0CAAmB,CAAC,cAAc;wBAC1C,eAAe,EAAE,CAAC,sBAAsB,EAAE,yBAAyB,CAAC;qBACrE;oBACD;wBACE,MAAM,EAAE,0CAAmB,CAAC,aAAa;wBACzC,eAAe,EAAE,CAAC,0BAA0B,EAAE,sBAAsB,CAAC;qBACtE;iBACF,CAAC;gBAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,EAAE,EAAE;oBAChD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;wBAC7D,GAAG,aAAa;wBAChB,SAAS,EAAE,MAAM;qBAClB,CAAC,CAAC;oBAEH,MAAM,OAAO,GAAG,KAAK,CAAC,2BAA2B,EAAE,CAAC;oBACpD,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;wBAC/B,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBACpC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,iEAAiE,EAAE,GAAG,EAAE;gBACzE,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,MAAM;oBACrC,SAAS,EAAE,0CAAmB,CAAC,UAAU;iBAC1C,CAAC,CAAC;gBAEH,MAAM,OAAO,GAAG,KAAK,CAAC,2BAA2B,EAAE,CAAC;gBACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;gBACzD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,SAAS,EAAE,GAAG,EAAE;QACvB,IAAA,oBAAQ,EAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,IAAA,cAAE,EAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBAC9E,MAAM,OAAO,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;gBAE3C,MAAM,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,kCAAkC,CAAC,CAAC;gBAC5F,MAAM,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;gBACvC,MAAM,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACtD,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC;oBACrC,UAAU,EAAE,0CAAmB,CAAC,UAAU;oBAC1C,UAAU,EAAE,0CAAmB,CAAC,SAAS;oBACzC,eAAe,EAAE,aAAa;iBAC/B,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,UAAU;oBACzC,SAAS,EAAE,0CAAmB,CAAC,UAAU;iBAC1C,CAAC,CAAC;gBAEH,MAAM,OAAO,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;gBAC3C,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,wBAAwB,CAAC,CAAC;gBAC/E,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,4BAA4B,CAAC,CAAC;gBAElF,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACnD,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;gBACnC,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,kCAAkC,EAAE,GAAG,EAAE;gBAC1C,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,UAAU;oBACzC,SAAS,EAAE,0CAAmB,CAAC,cAAc;iBAC9C,CAAC,CAAC;gBAEH,MAAM,OAAO,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;gBAC3C,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,2BAA2B,CAAC,CAAC;gBAEpF,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;gBACtC,MAAM,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACrD,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,0CAAmB,CAAC,cAAc,EAAE,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,kCAAkC,EAAE,GAAG,EAAE;gBAC1C,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,MAAM;oBACrC,SAAS,EAAE,0CAAmB,CAAC,UAAU;iBAC1C,CAAC,CAAC;gBAEH,MAAM,OAAO,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;gBAC3C,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,0BAA0B,CAAC,CAAC;gBAEnF,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;gBACtC,MAAM,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACrD,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,0CAAmB,CAAC,MAAM,EAAE,CAAC,CAAC;YACrF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,IAAA,oBAAQ,EAAC,oCAAoC,EAAE,GAAG,EAAE;YAClD,IAAA,cAAE,EAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,UAAU;oBACzC,SAAS,EAAE,0CAAmB,CAAC,UAAU;iBAC1C,CAAC,CAAC;gBAEH,MAAM,YAAY,GAAG,KAAK,CAAC,kCAAkC,EAAE,CAAC;gBAChE,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACjD,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBACrD,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAClD,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,wCAAwC,EAAE,GAAG,EAAE;gBAChD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,UAAU;oBACzC,SAAS,EAAE,0CAAmB,CAAC,cAAc;iBAC9C,CAAC,CAAC;gBAEH,MAAM,YAAY,GAAG,KAAK,CAAC,kCAAkC,EAAE,CAAC;gBAChE,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,kDAAkD,EAAE,GAAG,EAAE;gBAC1D,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE;oBAC7D,GAAG,aAAa;oBAChB,SAAS,EAAE,0CAAmB,CAAC,MAAM;oBACrC,SAAS,EAAE,0CAAmB,CAAC,UAAU;iBAC1C,CAAC,CAAC;gBAEH,MAAM,YAAY,GAAG,KAAK,CAAC,kCAAkC,EAAE,CAAC;gBAChE,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACjD,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,qDAAqD,EAAE,GAAG,EAAE;gBAC7D,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBAE9E,MAAM,YAAY,GAAG,KAAK,CAAC,kCAAkC,EAAE,CAAC;gBAChE,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC1C,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAA,oBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;YACtB,IAAA,cAAE,EAAC,+CAA+C,EAAE,GAAG,EAAE;gBACvD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBAC9E,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gBAE5B,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAC1D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACnD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,0CAAmB,CAAC,UAAU,CAAC,CAAC;gBACjE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,0CAAmB,CAAC,SAAS,CAAC,CAAC;gBAChE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACzD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;YAC9E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,UAAU,EAAE,GAAG,EAAE;YACxB,IAAA,cAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;gBAC5C,MAAM,aAAa,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBACtF,MAAM,IAAI,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC;gBACpC,MAAM,iBAAiB,GAAG,oEAA+B,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAEzE,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;gBAC9E,MAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBAClE,MAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBAClE,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBAC5D,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC9F,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,IAAA,oBAAQ,EAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,IAAA,cAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBAC9E,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;gBAE3C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBAC5C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAC3C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,iBAAiB,EAAE,GAAG,EAAE;YAC/B,IAAA,cAAE,EAAC,mCAAmC,EAAE,GAAG,EAAE;gBAC3C,MAAM,KAAK,GAAG,IAAI,oEAA+B,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBAC9E,MAAM,OAAO,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;gBAExC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAC7D,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACjD,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;gBACjE,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAC1D,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\vulnerability-status-changed.event.spec.ts"], "sourcesContent": ["import { VulnerabilityStatusChangedEvent, VulnerabilityStatusChangedEventData } from '../vulnerability-status-changed.event';\r\nimport { VulnerabilityStatus } from '../../entities/vulnerability/vulnerability.entity';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\n\r\ndescribe('VulnerabilityStatusChangedEvent', () => {\r\n  let aggregateId: UniqueEntityId;\r\n  let baseEventData: VulnerabilityStatusChangedEventData;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.generate();\r\n    baseEventData = {\r\n      vulnerabilityId: 'vuln-123',\r\n      oldStatus: VulnerabilityStatus.DISCOVERED,\r\n      newStatus: VulnerabilityStatus.CONFIRMED,\r\n      reason: 'Confirmed by security team',\r\n      timestamp: new Date().toISOString(),\r\n      changedBy: 'security-analyst',\r\n      context: { reviewId: 'review-456' },\r\n    };\r\n  });\r\n\r\n  describe('constructor and getters', () => {\r\n    it('should create event with all properties', () => {\r\n      const event = new VulnerabilityStatusChangedEvent(aggregateId, baseEventData);\r\n\r\n      expect(event.vulnerabilityId).toBe('vuln-123');\r\n      expect(event.oldStatus).toBe(VulnerabilityStatus.DISCOVERED);\r\n      expect(event.newStatus).toBe(VulnerabilityStatus.CONFIRMED);\r\n      expect(event.reason).toBe('Confirmed by security team');\r\n      expect(event.changedBy).toBe('security-analyst');\r\n      expect(event.context).toEqual({ reviewId: 'review-456' });\r\n      expect(event.aggregateId).toBe(aggregateId);\r\n    });\r\n\r\n    it('should create event with minimal data', () => {\r\n      const minimalData: VulnerabilityStatusChangedEventData = {\r\n        vulnerabilityId: 'vuln-123',\r\n        oldStatus: VulnerabilityStatus.DISCOVERED,\r\n        newStatus: VulnerabilityStatus.CONFIRMED,\r\n        reason: 'Status change',\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n\r\n      const event = new VulnerabilityStatusChangedEvent(aggregateId, minimalData);\r\n\r\n      expect(event.changedBy).toBeUndefined();\r\n      expect(event.context).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('status transition analysis', () => {\r\n    describe('isStatusProgression', () => {\r\n      it('should identify forward progression', () => {\r\n        const progressionCases = [\r\n          { from: VulnerabilityStatus.DISCOVERED, to: VulnerabilityStatus.CONFIRMED },\r\n          { from: VulnerabilityStatus.CONFIRMED, to: VulnerabilityStatus.TRIAGED },\r\n          { from: VulnerabilityStatus.TRIAGED, to: VulnerabilityStatus.IN_PROGRESS },\r\n          { from: VulnerabilityStatus.IN_PROGRESS, to: VulnerabilityStatus.REMEDIATED },\r\n          { from: VulnerabilityStatus.REMEDIATED, to: VulnerabilityStatus.VERIFIED },\r\n          { from: VulnerabilityStatus.VERIFIED, to: VulnerabilityStatus.CLOSED },\r\n        ];\r\n\r\n        progressionCases.forEach(({ from, to }) => {\r\n          const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n            ...baseEventData,\r\n            oldStatus: from,\r\n            newStatus: to,\r\n          });\r\n          expect(event.isStatusProgression()).toBe(true);\r\n        });\r\n      });\r\n\r\n      it('should not identify regression as progression', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.REMEDIATED,\r\n          newStatus: VulnerabilityStatus.TRIAGED,\r\n        });\r\n        expect(event.isStatusProgression()).toBe(false);\r\n      });\r\n\r\n      it('should not identify dismissal as progression', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.DISCOVERED,\r\n          newStatus: VulnerabilityStatus.FALSE_POSITIVE,\r\n        });\r\n        expect(event.isStatusProgression()).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isStatusRegression', () => {\r\n      it('should identify backward movement', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.REMEDIATED,\r\n          newStatus: VulnerabilityStatus.IN_PROGRESS,\r\n        });\r\n        expect(event.isStatusRegression()).toBe(true);\r\n      });\r\n\r\n      it('should not identify progression as regression', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.DISCOVERED,\r\n          newStatus: VulnerabilityStatus.CONFIRMED,\r\n        });\r\n        expect(event.isStatusRegression()).toBe(false);\r\n      });\r\n\r\n      it('should not identify dismissal as regression', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.REMEDIATED,\r\n          newStatus: VulnerabilityStatus.FALSE_POSITIVE,\r\n        });\r\n        expect(event.isStatusRegression()).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('wasVulnerabilityClosed', () => {\r\n      it('should identify closure statuses', () => {\r\n        const closureStatuses = [\r\n          VulnerabilityStatus.REMEDIATED,\r\n          VulnerabilityStatus.VERIFIED,\r\n          VulnerabilityStatus.CLOSED,\r\n        ];\r\n\r\n        closureStatuses.forEach(status => {\r\n          const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n            ...baseEventData,\r\n            newStatus: status,\r\n          });\r\n          expect(event.wasVulnerabilityClosed()).toBe(true);\r\n        });\r\n      });\r\n\r\n      it('should not identify non-closure statuses', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          newStatus: VulnerabilityStatus.FALSE_POSITIVE,\r\n        });\r\n        expect(event.wasVulnerabilityClosed()).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('wasVulnerabilityDismissed', () => {\r\n      it('should identify dismissal statuses', () => {\r\n        const dismissalStatuses = [\r\n          VulnerabilityStatus.FALSE_POSITIVE,\r\n          VulnerabilityStatus.ACCEPTED_RISK,\r\n        ];\r\n\r\n        dismissalStatuses.forEach(status => {\r\n          const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n            ...baseEventData,\r\n            newStatus: status,\r\n          });\r\n          expect(event.wasVulnerabilityDismissed()).toBe(true);\r\n        });\r\n      });\r\n\r\n      it('should not identify non-dismissal statuses', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          newStatus: VulnerabilityStatus.REMEDIATED,\r\n        });\r\n        expect(event.wasVulnerabilityDismissed()).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('wasVulnerabilityReopened', () => {\r\n      it('should identify reopening from closed status', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.CLOSED,\r\n          newStatus: VulnerabilityStatus.DISCOVERED,\r\n        });\r\n        expect(event.wasVulnerabilityReopened()).toBe(true);\r\n      });\r\n\r\n      it('should identify reopening from dismissed status', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.FALSE_POSITIVE,\r\n          newStatus: VulnerabilityStatus.CONFIRMED,\r\n        });\r\n        expect(event.wasVulnerabilityReopened()).toBe(true);\r\n      });\r\n\r\n      it('should not identify normal progression as reopening', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.DISCOVERED,\r\n          newStatus: VulnerabilityStatus.CONFIRMED,\r\n        });\r\n        expect(event.wasVulnerabilityReopened()).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('getTransitionType', () => {\r\n      it('should identify all transition types correctly', () => {\r\n        const testCases = [\r\n          {\r\n            from: VulnerabilityStatus.DISCOVERED,\r\n            to: VulnerabilityStatus.REMEDIATED,\r\n            expected: 'closure',\r\n          },\r\n          {\r\n            from: VulnerabilityStatus.DISCOVERED,\r\n            to: VulnerabilityStatus.FALSE_POSITIVE,\r\n            expected: 'dismissal',\r\n          },\r\n          {\r\n            from: VulnerabilityStatus.CLOSED,\r\n            to: VulnerabilityStatus.DISCOVERED,\r\n            expected: 'reopening',\r\n          },\r\n          {\r\n            from: VulnerabilityStatus.DISCOVERED,\r\n            to: VulnerabilityStatus.CONFIRMED,\r\n            expected: 'progression',\r\n          },\r\n          {\r\n            from: VulnerabilityStatus.REMEDIATED,\r\n            to: VulnerabilityStatus.IN_PROGRESS,\r\n            expected: 'reopening',\r\n          },\r\n          {\r\n            from: VulnerabilityStatus.TRIAGED,\r\n            to: VulnerabilityStatus.TRIAGED,\r\n            expected: 'lateral',\r\n          },\r\n        ];\r\n\r\n        testCases.forEach(({ from, to, expected }) => {\r\n          const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n            ...baseEventData,\r\n            oldStatus: from,\r\n            newStatus: to,\r\n          });\r\n          expect(event.getTransitionType()).toBe(expected);\r\n        });\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('notification requirements', () => {\r\n    describe('getNotificationPriority', () => {\r\n      it('should assign correct priorities', () => {\r\n        const testCases = [\r\n          { type: 'closure', expected: 'medium' },\r\n          { type: 'dismissal', expected: 'low' },\r\n          { type: 'reopening', expected: 'high' },\r\n          { type: 'regression', expected: 'high' },\r\n          { type: 'progression', expected: 'low' },\r\n        ];\r\n\r\n        testCases.forEach(({ type, expected }) => {\r\n          let from: VulnerabilityStatus, to: VulnerabilityStatus;\r\n          \r\n          switch (type) {\r\n            case 'closure':\r\n              from = VulnerabilityStatus.DISCOVERED;\r\n              to = VulnerabilityStatus.REMEDIATED;\r\n              break;\r\n            case 'dismissal':\r\n              from = VulnerabilityStatus.DISCOVERED;\r\n              to = VulnerabilityStatus.FALSE_POSITIVE;\r\n              break;\r\n            case 'reopening':\r\n              from = VulnerabilityStatus.CLOSED;\r\n              to = VulnerabilityStatus.DISCOVERED;\r\n              break;\r\n            case 'regression':\r\n              from = VulnerabilityStatus.REMEDIATED;\r\n              to = VulnerabilityStatus.IN_PROGRESS;\r\n              break;\r\n            case 'progression':\r\n              from = VulnerabilityStatus.DISCOVERED;\r\n              to = VulnerabilityStatus.CONFIRMED;\r\n              break;\r\n            default:\r\n              from = VulnerabilityStatus.DISCOVERED;\r\n              to = VulnerabilityStatus.CONFIRMED;\r\n          }\r\n\r\n          const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n            ...baseEventData,\r\n            oldStatus: from,\r\n            newStatus: to,\r\n          });\r\n          expect(event.getNotificationPriority()).toBe(expected);\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('getRequiredNotifications', () => {\r\n      it('should provide appropriate notifications for closure', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.DISCOVERED,\r\n          newStatus: VulnerabilityStatus.REMEDIATED,\r\n        });\r\n\r\n        const notifications = event.getRequiredNotifications();\r\n        expect(notifications.channels).toContain('email');\r\n        expect(notifications.channels).toContain('webhook');\r\n        expect(notifications.channels).toContain('dashboard');\r\n        expect(notifications.recipients).toContain('vulnerability_team');\r\n        expect(notifications.recipients).toContain('stakeholders');\r\n        expect(notifications.urgency).toBe('within_day');\r\n      });\r\n\r\n      it('should provide appropriate notifications for reopening', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.CLOSED,\r\n          newStatus: VulnerabilityStatus.DISCOVERED,\r\n        });\r\n\r\n        const notifications = event.getRequiredNotifications();\r\n        expect(notifications.channels).toContain('slack');\r\n        expect(notifications.recipients).toContain('security_team');\r\n        expect(notifications.recipients).toContain('management');\r\n        expect(notifications.urgency).toBe('within_hour');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('workflow actions', () => {\r\n    describe('getTriggeredWorkflowActions', () => {\r\n      it('should trigger appropriate actions for each status', () => {\r\n        const testCases = [\r\n          {\r\n            status: VulnerabilityStatus.CONFIRMED,\r\n            expectedActions: ['assign_to_security_team', 'create_risk_assessment'],\r\n          },\r\n          {\r\n            status: VulnerabilityStatus.TRIAGED,\r\n            expectedActions: ['assign_remediation_owner', 'set_remediation_timeline'],\r\n          },\r\n          {\r\n            status: VulnerabilityStatus.IN_PROGRESS,\r\n            expectedActions: ['start_remediation_tracking', 'notify_stakeholders'],\r\n          },\r\n          {\r\n            status: VulnerabilityStatus.REMEDIATED,\r\n            expectedActions: ['schedule_verification', 'update_asset_inventory'],\r\n          },\r\n          {\r\n            status: VulnerabilityStatus.VERIFIED,\r\n            expectedActions: ['close_vulnerability', 'update_metrics'],\r\n          },\r\n          {\r\n            status: VulnerabilityStatus.CLOSED,\r\n            expectedActions: ['archive_vulnerability', 'generate_closure_report'],\r\n          },\r\n          {\r\n            status: VulnerabilityStatus.FALSE_POSITIVE,\r\n            expectedActions: ['update_scanner_rules', 'document_false_positive'],\r\n          },\r\n          {\r\n            status: VulnerabilityStatus.ACCEPTED_RISK,\r\n            expectedActions: ['document_risk_acceptance', 'schedule_risk_review'],\r\n          },\r\n        ];\r\n\r\n        testCases.forEach(({ status, expectedActions }) => {\r\n          const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n            ...baseEventData,\r\n            newStatus: status,\r\n          });\r\n\r\n          const actions = event.getTriggeredWorkflowActions();\r\n          expectedActions.forEach(action => {\r\n            expect(actions).toContain(action);\r\n          });\r\n        });\r\n      });\r\n\r\n      it('should include reopening actions when vulnerability is reopened', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.CLOSED,\r\n          newStatus: VulnerabilityStatus.DISCOVERED,\r\n        });\r\n\r\n        const actions = event.getTriggeredWorkflowActions();\r\n        expect(actions).toContain('investigate_reopening_cause');\r\n        expect(actions).toContain('reassess_vulnerability');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('metrics', () => {\r\n    describe('getMetricsToUpdate', () => {\r\n      it('should include basic transition metrics', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, baseEventData);\r\n        const metrics = event.getMetricsToUpdate();\r\n\r\n        const transitionMetric = metrics.find(m => m.metric === 'vulnerability_status_transitions');\r\n        expect(transitionMetric).toBeDefined();\r\n        expect(transitionMetric?.operation).toBe('increment');\r\n        expect(transitionMetric?.tags).toEqual({\r\n          old_status: VulnerabilityStatus.DISCOVERED,\r\n          new_status: VulnerabilityStatus.CONFIRMED,\r\n          transition_type: 'progression',\r\n        });\r\n      });\r\n\r\n      it('should include closure metrics', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.DISCOVERED,\r\n          newStatus: VulnerabilityStatus.REMEDIATED,\r\n        });\r\n\r\n        const metrics = event.getMetricsToUpdate();\r\n        const closureMetric = metrics.find(m => m.metric === 'vulnerabilities_closed');\r\n        const timingMetric = metrics.find(m => m.metric === 'vulnerability_closure_time');\r\n\r\n        expect(closureMetric).toBeDefined();\r\n        expect(closureMetric?.operation).toBe('increment');\r\n        expect(timingMetric).toBeDefined();\r\n        expect(timingMetric?.operation).toBe('timing');\r\n      });\r\n\r\n      it('should include dismissal metrics', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.DISCOVERED,\r\n          newStatus: VulnerabilityStatus.FALSE_POSITIVE,\r\n        });\r\n\r\n        const metrics = event.getMetricsToUpdate();\r\n        const dismissalMetric = metrics.find(m => m.metric === 'vulnerabilities_dismissed');\r\n\r\n        expect(dismissalMetric).toBeDefined();\r\n        expect(dismissalMetric?.operation).toBe('increment');\r\n        expect(dismissalMetric?.tags).toEqual({ reason: VulnerabilityStatus.FALSE_POSITIVE });\r\n      });\r\n\r\n      it('should include reopening metrics', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.CLOSED,\r\n          newStatus: VulnerabilityStatus.DISCOVERED,\r\n        });\r\n\r\n        const metrics = event.getMetricsToUpdate();\r\n        const reopeningMetric = metrics.find(m => m.metric === 'vulnerabilities_reopened');\r\n\r\n        expect(reopeningMetric).toBeDefined();\r\n        expect(reopeningMetric?.operation).toBe('increment');\r\n        expect(reopeningMetric?.tags).toEqual({ from_status: VulnerabilityStatus.CLOSED });\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('compliance reporting', () => {\r\n    describe('getComplianceReportingRequirements', () => {\r\n      it('should require reporting for closure', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.DISCOVERED,\r\n          newStatus: VulnerabilityStatus.REMEDIATED,\r\n        });\r\n\r\n        const requirements = event.getComplianceReportingRequirements();\r\n        expect(requirements.required).toBe(true);\r\n        expect(requirements.frameworks).toContain('SOX');\r\n        expect(requirements.frameworks).toContain('PCI_DSS');\r\n        expect(requirements.deadline).toBe('within_week');\r\n        expect(requirements.reportType).toBe('closure');\r\n      });\r\n\r\n      it('should require reporting for dismissal', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.DISCOVERED,\r\n          newStatus: VulnerabilityStatus.FALSE_POSITIVE,\r\n        });\r\n\r\n        const requirements = event.getComplianceReportingRequirements();\r\n        expect(requirements.required).toBe(true);\r\n        expect(requirements.reportType).toBe('exception');\r\n      });\r\n\r\n      it('should require immediate reporting for reopening', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, {\r\n          ...baseEventData,\r\n          oldStatus: VulnerabilityStatus.CLOSED,\r\n          newStatus: VulnerabilityStatus.DISCOVERED,\r\n        });\r\n\r\n        const requirements = event.getComplianceReportingRequirements();\r\n        expect(requirements.required).toBe(true);\r\n        expect(requirements.deadline).toBe('within_24h');\r\n        expect(requirements.reportType).toBe('exception');\r\n      });\r\n\r\n      it('should not require reporting for normal progression', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, baseEventData);\r\n\r\n        const requirements = event.getComplianceReportingRequirements();\r\n        expect(requirements.required).toBe(false);\r\n        expect(requirements.reportType).toBe('none');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    describe('toJSON', () => {\r\n      it('should serialize event with all analysis data', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, baseEventData);\r\n        const json = event.toJSON();\r\n\r\n        expect(json.eventType).toBe('VulnerabilityStatusChanged');\r\n        expect(json.data.vulnerabilityId).toBe('vuln-123');\r\n        expect(json.data.oldStatus).toBe(VulnerabilityStatus.DISCOVERED);\r\n        expect(json.data.newStatus).toBe(VulnerabilityStatus.CONFIRMED);\r\n        expect(json.analysis).toBeDefined();\r\n        expect(json.analysis.transitionType).toBe('progression');\r\n        expect(json.analysis.isProgression).toBe(true);\r\n        expect(json.analysis.notificationPriority).toBe('low');\r\n        expect(json.analysis.triggeredActions).toContain('assign_to_security_team');\r\n      });\r\n    });\r\n\r\n    describe('fromJSON', () => {\r\n      it('should deserialize event correctly', () => {\r\n        const originalEvent = new VulnerabilityStatusChangedEvent(aggregateId, baseEventData);\r\n        const json = originalEvent.toJSON();\r\n        const deserializedEvent = VulnerabilityStatusChangedEvent.fromJSON(json);\r\n\r\n        expect(deserializedEvent.vulnerabilityId).toBe(originalEvent.vulnerabilityId);\r\n        expect(deserializedEvent.oldStatus).toBe(originalEvent.oldStatus);\r\n        expect(deserializedEvent.newStatus).toBe(originalEvent.newStatus);\r\n        expect(deserializedEvent.reason).toBe(originalEvent.reason);\r\n        expect(deserializedEvent.aggregateId.toString()).toBe(originalEvent.aggregateId.toString());\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('descriptions and summaries', () => {\r\n    describe('getDescription', () => {\r\n      it('should provide human-readable description', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, baseEventData);\r\n        const description = event.getDescription();\r\n\r\n        expect(description).toContain('discovered');\r\n        expect(description).toContain('confirmed');\r\n        expect(description).toContain('Confirmed by security team');\r\n      });\r\n    });\r\n\r\n    describe('getEventSummary', () => {\r\n      it('should provide structured summary', () => {\r\n        const event = new VulnerabilityStatusChangedEvent(aggregateId, baseEventData);\r\n        const summary = event.getEventSummary();\r\n\r\n        expect(summary.eventType).toBe('VulnerabilityStatusChanged');\r\n        expect(summary.vulnerabilityId).toBe('vuln-123');\r\n        expect(summary.statusTransition).toBe('discovered -> confirmed');\r\n        expect(summary.transitionType).toBe('progression');\r\n        expect(summary.reason).toBe('Confirmed by security team');\r\n        expect(summary.timestamp).toBeDefined();\r\n      });\r\n    });\r\n  });\r\n});"], "version": 3}