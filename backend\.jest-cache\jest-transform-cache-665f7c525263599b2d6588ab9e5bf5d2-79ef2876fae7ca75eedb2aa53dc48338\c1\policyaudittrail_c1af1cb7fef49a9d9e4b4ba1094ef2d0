41a1f9c3d1b8fd781885b4f0f11a2e46
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PolicyAuditTrail = exports.AuditEntry = exports.AuditSource = exports.AuditSeverity = exports.AuditEventType = void 0;
const base_entity_1 = require("../../../../shared-kernel/domain/base-entity");
const timestamp_value_object_1 = require("../../../../shared-kernel/value-objects/timestamp.value-object");
const user_id_value_object_1 = require("../../../../shared-kernel/value-objects/user-id.value-object");
const validation_exception_1 = require("../../../../shared-kernel/exceptions/validation.exception");
const security_policy_1 = require("./security-policy");
var AuditEventType;
(function (AuditEventType) {
    AuditEventType["POLICY_CREATED"] = "POLICY_CREATED";
    AuditEventType["POLICY_UPDATED"] = "POLICY_UPDATED";
    AuditEventType["POLICY_DELETED"] = "POLICY_DELETED";
    AuditEventType["POLICY_ENABLED"] = "POLICY_ENABLED";
    AuditEventType["POLICY_DISABLED"] = "POLICY_DISABLED";
    AuditEventType["RULE_ADDED"] = "RULE_ADDED";
    AuditEventType["RULE_UPDATED"] = "RULE_UPDATED";
    AuditEventType["RULE_REMOVED"] = "RULE_REMOVED";
    AuditEventType["RULE_EVALUATED"] = "RULE_EVALUATED";
    AuditEventType["ACTION_EXECUTED"] = "ACTION_EXECUTED";
    AuditEventType["ACTION_FAILED"] = "ACTION_FAILED";
    AuditEventType["COMPLIANCE_ASSESSED"] = "COMPLIANCE_ASSESSED";
    AuditEventType["EVIDENCE_ADDED"] = "EVIDENCE_ADDED";
    AuditEventType["CONFIGURATION_CHANGED"] = "CONFIGURATION_CHANGED";
})(AuditEventType || (exports.AuditEventType = AuditEventType = {}));
var AuditSeverity;
(function (AuditSeverity) {
    AuditSeverity["LOW"] = "LOW";
    AuditSeverity["MEDIUM"] = "MEDIUM";
    AuditSeverity["HIGH"] = "HIGH";
    AuditSeverity["CRITICAL"] = "CRITICAL";
})(AuditSeverity || (exports.AuditSeverity = AuditSeverity = {}));
var AuditSource;
(function (AuditSource) {
    AuditSource["WEB_UI"] = "WEB_UI";
    AuditSource["API"] = "API";
    AuditSource["SYSTEM"] = "SYSTEM";
    AuditSource["SCHEDULED_TASK"] = "SCHEDULED_TASK";
    AuditSource["WEBHOOK"] = "WEBHOOK";
    AuditSource["CLI"] = "CLI";
})(AuditSource || (exports.AuditSource = AuditSource = {}));
class AuditEntry extends base_entity_1.BaseEntity {
    constructor(props) {
        super(props.id);
        this.validateProps(props);
        this._tenantId = props.tenantId;
        this._eventType = props.eventType;
        this._severity = props.severity;
        this._userId = props.userId;
        this._policyId = props.policyId;
        this._ruleId = props.ruleId;
        this._description = props.description;
        this._details = props.details;
        this._context = props.context;
        this._timestamp = props.timestamp;
        this._tags = props.tags || [];
    }
    static create(props) {
        return new AuditEntry({
            ...props,
            timestamp: timestamp_value_object_1.Timestamp.now()
        });
    }
    addTag(tag) {
        if (!this._tags.includes(tag)) {
            this._tags.push(tag);
        }
    }
    removeTag(tag) {
        const index = this._tags.indexOf(tag);
        if (index > -1) {
            this._tags.splice(index, 1);
        }
    }
    hasTag(tag) {
        return this._tags.includes(tag);
    }
    toSearchableText() {
        return [
            this._description,
            this._eventType,
            this._severity,
            ...this._tags,
            JSON.stringify(this._details)
        ].join(' ').toLowerCase();
    }
    validateProps(props) {
        const errors = [];
        if (!props.tenantId) {
            errors.push({
                field: 'tenantId',
                value: props.tenantId,
                constraint: 'required',
                message: 'Tenant ID is required'
            });
        }
        if (!Object.values(AuditEventType).includes(props.eventType)) {
            errors.push({
                field: 'eventType',
                value: props.eventType,
                constraint: 'enum',
                message: 'Valid audit event type is required'
            });
        }
        if (!Object.values(AuditSeverity).includes(props.severity)) {
            errors.push({
                field: 'severity',
                value: props.severity,
                constraint: 'enum',
                message: 'Valid audit severity is required'
            });
        }
        if (!props.description || props.description.trim().length === 0) {
            errors.push({
                field: 'description',
                value: props.description,
                constraint: 'required',
                message: 'Audit description is required'
            });
        }
        if (!props.details || typeof props.details !== 'object') {
            errors.push({
                field: 'details',
                value: props.details,
                constraint: 'type',
                message: 'Audit details must be an object'
            });
        }
        if (!props.context || typeof props.context !== 'object') {
            errors.push({
                field: 'context',
                value: props.context,
                constraint: 'required',
                message: 'Audit context is required'
            });
        }
        else if (!Object.values(AuditSource).includes(props.context.source)) {
            errors.push({
                field: 'context.source',
                value: props.context.source,
                constraint: 'enum',
                message: 'Valid audit source is required'
            });
        }
        if (errors.length > 0) {
            throw new validation_exception_1.ValidationException('Audit entry validation failed', errors);
        }
    }
    // Getters
    get tenantId() { return this._tenantId; }
    get eventType() { return this._eventType; }
    get severity() { return this._severity; }
    get userId() { return this._userId; }
    get policyId() { return this._policyId; }
    get ruleId() { return this._ruleId; }
    get description() { return this._description; }
    get details() { return { ...this._details }; }
    get context() { return { ...this._context }; }
    get timestamp() { return this._timestamp; }
    get tags() { return [...this._tags]; }
}
exports.AuditEntry = AuditEntry;
class PolicyAuditTrail extends base_entity_1.BaseEntity {
    constructor(props) {
        super(props.id);
        this.validateProps(props);
        this._tenantId = props.tenantId;
        this._entries = props.entries || [];
        this._retentionDays = props.retentionDays;
        this._createdBy = props.createdBy;
    }
    static create(props) {
        return new PolicyAuditTrail(props);
    }
    addEntry(entry) {
        if (!entry.tenantId.equals(this._tenantId)) {
            throw new validation_exception_1.ValidationException('Audit entry tenant ID must match audit trail tenant ID', [{
                    field: 'tenantId',
                    value: entry.tenantId.value,
                    constraint: 'match',
                    message: 'Audit entry tenant ID must match audit trail tenant ID'
                }]);
        }
        this._entries.push(entry);
        this.cleanupExpiredEntries();
        this.touch();
    }
    addPolicyCreatedEntry(policyId, policyName, userId, context) {
        const entry = AuditEntry.create({
            tenantId: this._tenantId,
            eventType: AuditEventType.POLICY_CREATED,
            severity: AuditSeverity.MEDIUM,
            userId,
            policyId,
            description: `Policy '${policyName}' was created`,
            details: { policyName },
            context,
            tags: ['policy', 'creation']
        });
        this.addEntry(entry);
    }
    addRuleEvaluatedEntry(policyId, ruleId, evaluationResult, context) {
        const severity = evaluationResult.matched ? AuditSeverity.HIGH : AuditSeverity.LOW;
        const entry = AuditEntry.create({
            tenantId: this._tenantId,
            eventType: AuditEventType.RULE_EVALUATED,
            severity,
            policyId,
            ruleId,
            description: `Rule '${ruleId}' was evaluated: ${evaluationResult.reason}`,
            details: {
                matched: evaluationResult.matched,
                confidence: evaluationResult.confidence,
                reason: evaluationResult.reason,
                action: evaluationResult.action
            },
            context,
            tags: ['rule', 'evaluation', evaluationResult.matched ? 'matched' : 'no-match']
        });
        this.addEntry(entry);
    }
    addActionExecutedEntry(policyId, ruleId, actionType, executionStatus, userId, context, details) {
        const severity = executionStatus === security_policy_1.PolicyExecutionStatus.FAILED ?
            AuditSeverity.HIGH : AuditSeverity.MEDIUM;
        const entry = AuditEntry.create({
            tenantId: this._tenantId,
            eventType: AuditEventType.ACTION_EXECUTED,
            severity,
            userId,
            policyId,
            ruleId,
            description: `Action '${actionType}' was ${executionStatus.toLowerCase()}`,
            details: {
                actionType,
                executionStatus,
                ...details
            },
            context,
            tags: ['action', 'execution', executionStatus.toLowerCase()]
        });
        this.addEntry(entry);
    }
    addComplianceAssessedEntry(policyId, controlId, status, userId, context, findings) {
        const entry = AuditEntry.create({
            tenantId: this._tenantId,
            eventType: AuditEventType.COMPLIANCE_ASSESSED,
            severity: AuditSeverity.MEDIUM,
            userId,
            policyId,
            ruleId: controlId,
            description: `Compliance control '${controlId}' was assessed with status: ${status}`,
            details: {
                controlId,
                status,
                findingsCount: findings?.length || 0,
                findings: findings?.slice(0, 5) // Limit findings in audit for storage efficiency
            },
            context,
            tags: ['compliance', 'assessment', status.toLowerCase()]
        });
        this.addEntry(entry);
    }
    searchEntries(criteria) {
        let filteredEntries = [...this._entries];
        if (criteria.userId) {
            filteredEntries = filteredEntries.filter(entry => entry.userId && entry.userId.equals(criteria.userId));
        }
        if (criteria.eventTypes && criteria.eventTypes.length > 0) {
            filteredEntries = filteredEntries.filter(entry => criteria.eventTypes.includes(entry.eventType));
        }
        if (criteria.severities && criteria.severities.length > 0) {
            filteredEntries = filteredEntries.filter(entry => criteria.severities.includes(entry.severity));
        }
        if (criteria.startDate) {
            filteredEntries = filteredEntries.filter(entry => entry.timestamp.value >= criteria.startDate.value);
        }
        if (criteria.endDate) {
            filteredEntries = filteredEntries.filter(entry => entry.timestamp.value <= criteria.endDate.value);
        }
        if (criteria.policyId) {
            filteredEntries = filteredEntries.filter(entry => entry.policyId && entry.policyId.equals(criteria.policyId));
        }
        if (criteria.ruleId) {
            filteredEntries = filteredEntries.filter(entry => entry.ruleId === criteria.ruleId);
        }
        if (criteria.source) {
            filteredEntries = filteredEntries.filter(entry => entry.context.source === criteria.source);
        }
        // Sort by timestamp (most recent first)
        filteredEntries.sort((a, b) => b.timestamp.value.getTime() - a.timestamp.value.getTime());
        // Apply pagination
        const offset = criteria.offset || 0;
        const limit = criteria.limit || 100;
        return filteredEntries.slice(offset, offset + limit);
    }
    generateSummary(days = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        const cutoffTimestamp = timestamp_value_object_1.Timestamp.create(cutoffDate);
        const recentEntries = this._entries.filter(entry => entry.timestamp.value >= cutoffTimestamp.value);
        const eventsByType = {};
        const eventsBySeverity = {};
        const eventsBySource = {};
        const userEventCounts = new Map();
        // Initialize counters
        Object.values(AuditEventType).forEach(type => eventsByType[type] = 0);
        Object.values(AuditSeverity).forEach(severity => eventsBySeverity[severity] = 0);
        Object.values(AuditSource).forEach(source => eventsBySource[source] = 0);
        // Count events
        recentEntries.forEach(entry => {
            eventsByType[entry.eventType]++;
            eventsBySeverity[entry.severity]++;
            eventsBySource[entry.context.source]++;
            if (entry.userId) {
                const userIdStr = entry.userId.value;
                userEventCounts.set(userIdStr, (userEventCounts.get(userIdStr) || 0) + 1);
            }
        });
        // Get top users
        const topUsers = Array.from(userEventCounts.entries())
            .map(([userIdStr, count]) => ({
            userId: user_id_value_object_1.UserId.fromString(userIdStr),
            eventCount: count
        }))
            .sort((a, b) => b.eventCount - a.eventCount)
            .slice(0, 10);
        // Get recent activity (last 10 entries)
        const recentActivity = recentEntries
            .sort((a, b) => b.timestamp.value.getTime() - a.timestamp.value.getTime())
            .slice(0, 10);
        return {
            totalEvents: recentEntries.length,
            eventsByType,
            eventsBySeverity,
            eventsBySource,
            topUsers,
            recentActivity
        };
    }
    exportEntries(criteria, format = 'json') {
        const entries = this.searchEntries(criteria);
        if (format === 'csv') {
            const headers = [
                'ID', 'Timestamp', 'Event Type', 'Severity', 'User ID', 'Policy ID',
                'Rule ID', 'Description', 'Source', 'Tags'
            ];
            const csvRows = [
                headers.join(','),
                ...entries.map(entry => [
                    entry.id.value,
                    entry.timestamp.value.toISOString(),
                    entry.eventType,
                    entry.severity,
                    entry.userId?.value || '',
                    entry.policyId?.value || '',
                    entry.ruleId || '',
                    `"${entry.description.replace(/"/g, '""')}"`,
                    entry.context.source,
                    `"${entry.tags.join(', ')}"`
                ].join(','))
            ];
            return csvRows.join('\n');
        }
        return JSON.stringify(entries.map(entry => ({
            id: entry.id.value,
            timestamp: entry.timestamp.value.toISOString(),
            eventType: entry.eventType,
            severity: entry.severity,
            userId: entry.userId?.value,
            policyId: entry.policyId?.value,
            ruleId: entry.ruleId,
            description: entry.description,
            details: entry.details,
            context: entry.context,
            tags: entry.tags
        })), null, 2);
    }
    cleanupExpiredEntries() {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - this._retentionDays);
        const cutoffTimestamp = timestamp_value_object_1.Timestamp.create(cutoffDate);
        this._entries = this._entries.filter(entry => entry.timestamp.value >= cutoffTimestamp.value);
    }
    validateProps(props) {
        const errors = [];
        if (!props.tenantId) {
            errors.push({
                field: 'tenantId',
                value: props.tenantId,
                constraint: 'required',
                message: 'Tenant ID is required'
            });
        }
        if (!props.createdBy) {
            errors.push({
                field: 'createdBy',
                value: props.createdBy,
                constraint: 'required',
                message: 'Created by user ID is required'
            });
        }
        if (typeof props.retentionDays !== 'number' || props.retentionDays <= 0) {
            errors.push({
                field: 'retentionDays',
                value: props.retentionDays,
                constraint: 'positive',
                message: 'Retention days must be a positive number'
            });
        }
        if (props.entries && !Array.isArray(props.entries)) {
            errors.push({
                field: 'entries',
                value: props.entries,
                constraint: 'array',
                message: 'Entries must be an array'
            });
        }
        if (errors.length > 0) {
            throw new validation_exception_1.ValidationException('Policy audit trail validation failed', errors);
        }
    }
    touch() {
        // Update the last modified timestamp
        // This would typically update an updatedAt field if we had one
    }
    // Getters
    get tenantId() { return this._tenantId; }
    get entries() { return [...this._entries]; }
    get retentionDays() { return this._retentionDays; }
    get createdBy() { return this._createdBy; }
    get entryCount() { return this._entries.length; }
}
exports.PolicyAuditTrail = PolicyAuditTrail;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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