{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\response-action-failed.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAC5E,gEAAuD;AAoBvD;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAa,+BAAgC,SAAQ,+BAA8C;IACjG,YACE,WAA2B,EAC3B,SAAwC,EACxC,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,QAAQ,CAAC;YAC7C,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,YAAY,CAAC;YACjD,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC;YAC1C,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC/E,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,MAAM,eAAe,GAAG;YACtB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,WAAW;YACtB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,QAAQ;YACnB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,mBAAmB;SAC/B,CAAC;QAEF,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,MAAM,kBAAkB,GAAG;YACzB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,QAAQ;YACnB,6BAAU,CAAC,YAAY;YACvB,6BAAU,CAAC,SAAS;YACpB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,YAAY;YACvB,6BAAU,CAAC,oBAAoB;YAC/B,6BAAU,CAAC,eAAe;SAC3B,CAAC;QAEF,OAAO,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,MAAM,eAAe,GAAG;YACtB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,sBAAsB;SAClC,CAAC;QAEF,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,mBAAmB,GAAG;YAC1B,6BAAU,CAAC,UAAU;YACrB,6BAAU,CAAC,QAAQ;YACnB,6BAAU,CAAC,UAAU;YACrB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,WAAW;YACtB,6BAAU,CAAC,aAAa;YACxB,6BAAU,CAAC,eAAe;SAC3B,CAAC;QAEF,OAAO,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;QACrD,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC;QACnD,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC;QACnD,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QAEtD,IAAI,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACjH,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACvE,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpG,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACxG,OAAO,eAAe,CAAC;QACzB,CAAC;QAED,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACtG,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtG,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YACnD,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC3D,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YACnD,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACpD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACzD,CAAC;QAED,4BAA4B;QAC5B,QAAQ,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAClC,KAAK,YAAY;gBACf,OAAO,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;gBAC1D,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;gBACjD,MAAM;YACR,KAAK,SAAS;gBACZ,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBAClD,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACnD,MAAM;YACR,KAAK,UAAU;gBACb,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACnD,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;gBAC9C,MAAM;YACR,KAAK,eAAe;gBAClB,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBACvD,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBACpD,MAAM;YACR,KAAK,SAAS;gBACZ,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAC3C,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAC7C,MAAM;QACV,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,qCAAqC;QACrC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAEjC,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAEvC,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC1B,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAClC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACjC,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,yBAAyB;QAKvB,IAAI,IAAI,CAAC,yBAAyB,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YAC9D,OAAO;gBACL,SAAS,EAAE,IAAI;gBACf,KAAK,EAAE,WAAW;gBAClB,MAAM,EAAE,uDAAuD;aAChE,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO;gBACL,SAAS,EAAE,IAAI;gBACf,KAAK,EAAE,YAAY;gBACnB,MAAM,EAAE,iCAAiC;aAC1C,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YACzD,OAAO;gBACL,SAAS,EAAE,IAAI;gBACf,KAAK,EAAE,YAAY;gBACnB,MAAM,EAAE,+CAA+C;aACxD,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,MAAM,EAAE,CAAC;YACrE,OAAO;gBACL,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,WAAW;gBAClB,MAAM,EAAE,uCAAuC;aAChD,CAAC;QACJ,CAAC;QAED,OAAO;YACL,SAAS,EAAE,KAAK;YAChB,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,mCAAmC;SAC5C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,uBAAuB;QAMrB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO;gBACL,WAAW,EAAE,KAAK;gBAClB,YAAY,EAAE,CAAC;gBACf,UAAU,EAAE,CAAC;gBACb,UAAU,EAAE,CAAC,0BAA0B,CAAC;aACzC,CAAC;QACJ,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,SAAS;gBACZ,YAAY,GAAG,EAAE,CAAC;gBAClB,UAAU,GAAG,CAAC,CAAC;gBACf,UAAU,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,UAAU;gBACb,YAAY,GAAG,EAAE,CAAC;gBAClB,UAAU,GAAG,CAAC,CAAC;gBACf,UAAU,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;gBACzD,MAAM;YACR,KAAK,SAAS;gBACZ,YAAY,GAAG,CAAC,CAAC;gBACjB,UAAU,GAAG,CAAC,CAAC;gBACf,UAAU,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;gBAC/C,MAAM;YACR,KAAK,YAAY;gBACf,YAAY,GAAG,CAAC,CAAC;gBACjB,UAAU,GAAG,CAAC,CAAC;gBACf,UAAU,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACtD,MAAM;YACR,KAAK,eAAe;gBAClB,YAAY,GAAG,CAAC,CAAC;gBACjB,UAAU,GAAG,CAAC,CAAC;gBACf,UAAU,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACtD,MAAM;YACR;gBACE,UAAU,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,UAAU,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO;YACL,WAAW,EAAE,IAAI;YACjB,YAAY;YACZ,UAAU;YACV,UAAU;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iBAAiB;QAUf,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;YACrC,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;YACrC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ;YACjC,kBAAkB,EAAE,IAAI,CAAC,yBAAyB,EAAE;YACpD,WAAW,EAAE,IAAI,CAAC,kBAAkB,EAAE;YACtC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAehB,MAAM,UAAU,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEpD,OAAO;YACL,SAAS,EAAE,sBAAsB;YACjC,MAAM,EAAE,wBAAwB;YAChC,QAAQ,EAAE,gBAAgB;YAC1B,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YACvC,IAAI,EAAE,IAAI,CAAC,SAAS;YACpB,QAAQ,EAAE;gBACR,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC1C,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC1C,kBAAkB,EAAE,IAAI,CAAC,yBAAyB,EAAE;gBACpD,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;gBACrC,kBAAkB,EAAE,UAAU,CAAC,SAAS;gBACxC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ;aAClC;SACF,CAAC;IACJ,CAAC;CACF;AAnfD,0EAmfC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\response-action-failed.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { ActionType } from '../enums/action-type.enum';\r\n\r\n/**\r\n * Response Action Failed Domain Event Data\r\n */\r\nexport interface ResponseActionFailedEventData {\r\n  /** Type of action that failed */\r\n  actionType: ActionType;\r\n  /** Error message or reason for failure */\r\n  error: string;\r\n  /** Who was executing the action when it failed */\r\n  executedBy?: string;\r\n  /** When the action failed */\r\n  failedAt: Date;\r\n  /** Current retry count */\r\n  retryCount: number;\r\n  /** Whether the action can be retried */\r\n  canRetry: boolean;\r\n}\r\n\r\n/**\r\n * Response Action Failed Domain Event\r\n * \r\n * Raised when a response action execution fails.\r\n * This event indicates that the action could not be completed successfully\r\n * and may require intervention, retry, or alternative approaches.\r\n * \r\n * Key information:\r\n * - Action type and failure details\r\n * - Error information and context\r\n * - Retry capabilities and count\r\n * - Failure timestamp and executor\r\n * \r\n * Use cases:\r\n * - Trigger failure handling workflows\r\n * - Notify stakeholders of action failures\r\n * - Initiate retry mechanisms\r\n * - Escalate critical action failures\r\n * - Update monitoring and alerting systems\r\n * - Generate failure reports and analytics\r\n */\r\nexport class ResponseActionFailedDomainEvent extends BaseDomainEvent<ResponseActionFailedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: ResponseActionFailedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the action type that failed\r\n   */\r\n  get actionType(): ActionType {\r\n    return this.eventData.actionType;\r\n  }\r\n\r\n  /**\r\n   * Get the failure error message\r\n   */\r\n  get error(): string {\r\n    return this.eventData.error;\r\n  }\r\n\r\n  /**\r\n   * Get who was executing the action\r\n   */\r\n  get executedBy(): string | undefined {\r\n    return this.eventData.executedBy;\r\n  }\r\n\r\n  /**\r\n   * Get when the action failed\r\n   */\r\n  get failedAt(): Date {\r\n    return this.eventData.failedAt;\r\n  }\r\n\r\n  /**\r\n   * Get the current retry count\r\n   */\r\n  get retryCount(): number {\r\n    return this.eventData.retryCount;\r\n  }\r\n\r\n  /**\r\n   * Check if the action can be retried\r\n   */\r\n  get canRetry(): boolean {\r\n    return this.eventData.canRetry;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a first-time failure\r\n   */\r\n  isFirstFailure(): boolean {\r\n    return this.eventData.retryCount === 0;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a repeated failure\r\n   */\r\n  isRepeatedFailure(): boolean {\r\n    return this.eventData.retryCount > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a final failure (no more retries)\r\n   */\r\n  isFinalFailure(): boolean {\r\n    return !this.eventData.canRetry;\r\n  }\r\n\r\n  /**\r\n   * Check if this was an automated execution failure\r\n   */\r\n  isAutomatedFailure(): boolean {\r\n    return this.eventData.executedBy?.includes('system') ||\r\n           this.eventData.executedBy?.includes('automation') ||\r\n           this.eventData.executedBy?.includes('bot') ||\r\n           false;\r\n  }\r\n\r\n  /**\r\n   * Check if this was a manual execution failure\r\n   */\r\n  isManualFailure(): boolean {\r\n    return this.eventData.executedBy !== undefined && !this.isAutomatedFailure();\r\n  }\r\n\r\n  /**\r\n   * Check if this is a security-critical action failure\r\n   */\r\n  isSecurityCriticalFailure(): boolean {\r\n    const criticalActions = [\r\n      ActionType.ISOLATE_SYSTEM,\r\n      ActionType.SHUTDOWN_SYSTEM,\r\n      ActionType.DELETE_FILE,\r\n      ActionType.REMOVE_MALWARE,\r\n      ActionType.DISABLE_ACCOUNT,\r\n      ActionType.BLOCK_IP,\r\n      ActionType.QUARANTINE_FILE,\r\n      ActionType.UPDATE_FIREWALL,\r\n      ActionType.PATCH_VULNERABILITY,\r\n    ];\r\n    \r\n    return criticalActions.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a containment action failure\r\n   */\r\n  isContainmentFailure(): boolean {\r\n    const containmentActions = [\r\n      ActionType.ISOLATE_SYSTEM,\r\n      ActionType.QUARANTINE_FILE,\r\n      ActionType.BLOCK_IP,\r\n      ActionType.BLOCK_DOMAIN,\r\n      ActionType.BLOCK_URL,\r\n      ActionType.DISABLE_ACCOUNT,\r\n      ActionType.REVOKE_TOKEN,\r\n      ActionType.TERMINATE_CONNECTION,\r\n      ActionType.SHUTDOWN_SYSTEM,\r\n    ];\r\n    \r\n    return containmentActions.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a recovery action failure\r\n   */\r\n  isRecoveryFailure(): boolean {\r\n    const recoveryActions = [\r\n      ActionType.RESTORE_BACKUP,\r\n      ActionType.REBUILD_SYSTEM,\r\n      ActionType.RESTORE_NETWORK,\r\n      ActionType.ENABLE_SERVICE,\r\n      ActionType.RESET_PASSWORD,\r\n      ActionType.REGENERATE_CERTIFICATE,\r\n    ];\r\n    \r\n    return recoveryActions.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a notification action failure\r\n   */\r\n  isNotificationFailure(): boolean {\r\n    const notificationActions = [\r\n      ActionType.SEND_EMAIL,\r\n      ActionType.SEND_SMS,\r\n      ActionType.SEND_SLACK,\r\n      ActionType.TRIGGER_WEBHOOK,\r\n      ActionType.PAGE_ONCALL,\r\n      ActionType.CREATE_TICKET,\r\n      ActionType.UPDATE_INCIDENT,\r\n    ];\r\n    \r\n    return notificationActions.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Get failure severity level\r\n   */\r\n  getFailureSeverity(): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (this.isSecurityCriticalFailure()) {\r\n      return this.isFinalFailure() ? 'critical' : 'high';\r\n    }\r\n    \r\n    if (this.isContainmentFailure()) {\r\n      return this.isFinalFailure() ? 'high' : 'medium';\r\n    }\r\n    \r\n    if (this.isRecoveryFailure()) {\r\n      return this.isFinalFailure() ? 'high' : 'medium';\r\n    }\r\n    \r\n    if (this.isNotificationFailure()) {\r\n      return 'medium';\r\n    }\r\n    \r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get failure category\r\n   */\r\n  getFailureCategory(): 'technical' | 'permission' | 'timeout' | 'resource' | 'configuration' | 'network' | 'unknown' {\r\n    const errorLower = this.eventData.error.toLowerCase();\r\n    \r\n    if (errorLower.includes('permission') || errorLower.includes('unauthorized') || errorLower.includes('forbidden')) {\r\n      return 'permission';\r\n    }\r\n    \r\n    if (errorLower.includes('timeout') || errorLower.includes('timed out')) {\r\n      return 'timeout';\r\n    }\r\n    \r\n    if (errorLower.includes('resource') || errorLower.includes('memory') || errorLower.includes('disk')) {\r\n      return 'resource';\r\n    }\r\n    \r\n    if (errorLower.includes('config') || errorLower.includes('setting') || errorLower.includes('parameter')) {\r\n      return 'configuration';\r\n    }\r\n    \r\n    if (errorLower.includes('network') || errorLower.includes('connection') || errorLower.includes('dns')) {\r\n      return 'network';\r\n    }\r\n    \r\n    if (errorLower.includes('exception') || errorLower.includes('error') || errorLower.includes('failed')) {\r\n      return 'technical';\r\n    }\r\n    \r\n    return 'unknown';\r\n  }\r\n\r\n  /**\r\n   * Get recommended immediate actions\r\n   */\r\n  getImmediateActions(): string[] {\r\n    const actions: string[] = [];\r\n\r\n    if (this.isSecurityCriticalFailure()) {\r\n      actions.push('Escalate to incident response team');\r\n      actions.push('Assess security impact of failure');\r\n      actions.push('Consider manual fallback procedures');\r\n    }\r\n\r\n    if (this.isContainmentFailure()) {\r\n      actions.push('Implement alternative containment measures');\r\n      actions.push('Monitor for threat spread');\r\n      actions.push('Assess containment gap impact');\r\n    }\r\n\r\n    if (this.isRecoveryFailure()) {\r\n      actions.push('Assess service impact');\r\n      actions.push('Consider alternative recovery methods');\r\n      actions.push('Notify affected stakeholders');\r\n    }\r\n\r\n    if (this.canRetry) {\r\n      actions.push('Analyze failure cause before retry');\r\n      actions.push('Adjust retry parameters if needed');\r\n    } else {\r\n      actions.push('Investigate root cause of failure');\r\n      actions.push('Consider alternative action approaches');\r\n    }\r\n\r\n    // Category-specific actions\r\n    switch (this.getFailureCategory()) {\r\n      case 'permission':\r\n        actions.push('Verify action permissions and credentials');\r\n        actions.push('Check role-based access controls');\r\n        break;\r\n      case 'timeout':\r\n        actions.push('Check system performance and load');\r\n        actions.push('Consider increasing timeout limits');\r\n        break;\r\n      case 'resource':\r\n        actions.push('Check system resource availability');\r\n        actions.push('Free up resources if possible');\r\n        break;\r\n      case 'configuration':\r\n        actions.push('Verify action configuration parameters');\r\n        actions.push('Check system configuration settings');\r\n        break;\r\n      case 'network':\r\n        actions.push('Check network connectivity');\r\n        actions.push('Verify network configuration');\r\n        break;\r\n    }\r\n\r\n    return actions;\r\n  }\r\n\r\n  /**\r\n   * Get stakeholders to notify\r\n   */\r\n  getNotificationTargets(): string[] {\r\n    const targets: string[] = [];\r\n\r\n    // Always notify the action requestor\r\n    targets.push('action-requestor');\r\n\r\n    if (this.isSecurityCriticalFailure()) {\r\n      targets.push('security-team');\r\n      targets.push('incident-response-team');\r\n      \r\n      if (this.isFinalFailure()) {\r\n        targets.push('security-managers');\r\n        targets.push('on-call-engineers');\r\n      }\r\n    }\r\n\r\n    if (this.isContainmentFailure()) {\r\n      targets.push('containment-specialists');\r\n      targets.push('security-analysts');\r\n    }\r\n\r\n    if (this.isRecoveryFailure()) {\r\n      targets.push('recovery-team');\r\n      targets.push('service-owners');\r\n    }\r\n\r\n    if (this.isNotificationFailure()) {\r\n      targets.push('communication-team');\r\n    }\r\n\r\n    if (this.isRepeatedFailure()) {\r\n      targets.push('technical-support');\r\n    }\r\n\r\n    if (this.isFinalFailure()) {\r\n      targets.push('escalation-team');\r\n    }\r\n\r\n    return targets;\r\n  }\r\n\r\n  /**\r\n   * Get escalation requirements\r\n   */\r\n  getEscalationRequirements(): {\r\n    immediate: boolean;\r\n    level: 'none' | 'technical' | 'management' | 'executive';\r\n    reason: string;\r\n  } {\r\n    if (this.isSecurityCriticalFailure() && this.isFinalFailure()) {\r\n      return {\r\n        immediate: true,\r\n        level: 'executive',\r\n        reason: 'Critical security action failed with no retry options'\r\n      };\r\n    }\r\n\r\n    if (this.isSecurityCriticalFailure()) {\r\n      return {\r\n        immediate: true,\r\n        level: 'management',\r\n        reason: 'Critical security action failed'\r\n      };\r\n    }\r\n\r\n    if (this.isContainmentFailure() && this.isFinalFailure()) {\r\n      return {\r\n        immediate: true,\r\n        level: 'management',\r\n        reason: 'Containment action failed - threat may spread'\r\n      };\r\n    }\r\n\r\n    if (this.isRepeatedFailure() && this.getFailureSeverity() === 'high') {\r\n      return {\r\n        immediate: false,\r\n        level: 'technical',\r\n        reason: 'Repeated high-severity action failure'\r\n      };\r\n    }\r\n\r\n    return {\r\n      immediate: false,\r\n      level: 'none',\r\n      reason: 'Standard failure handling applies'\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get retry recommendations\r\n   */\r\n  getRetryRecommendations(): {\r\n    shouldRetry: boolean;\r\n    delayMinutes: number;\r\n    maxRetries: number;\r\n    conditions: string[];\r\n  } {\r\n    if (!this.canRetry) {\r\n      return {\r\n        shouldRetry: false,\r\n        delayMinutes: 0,\r\n        maxRetries: 0,\r\n        conditions: ['Action cannot be retried']\r\n      };\r\n    }\r\n\r\n    const category = this.getFailureCategory();\r\n    const conditions: string[] = [];\r\n    let delayMinutes = 5;\r\n    let maxRetries = 3;\r\n\r\n    switch (category) {\r\n      case 'timeout':\r\n        delayMinutes = 10;\r\n        maxRetries = 2;\r\n        conditions.push('Verify system performance before retry');\r\n        break;\r\n      case 'resource':\r\n        delayMinutes = 15;\r\n        maxRetries = 2;\r\n        conditions.push('Ensure sufficient resources available');\r\n        break;\r\n      case 'network':\r\n        delayMinutes = 5;\r\n        maxRetries = 3;\r\n        conditions.push('Verify network connectivity');\r\n        break;\r\n      case 'permission':\r\n        delayMinutes = 0;\r\n        maxRetries = 1;\r\n        conditions.push('Fix permission issues before retry');\r\n        break;\r\n      case 'configuration':\r\n        delayMinutes = 0;\r\n        maxRetries = 1;\r\n        conditions.push('Correct configuration before retry');\r\n        break;\r\n      default:\r\n        conditions.push('Analyze failure cause before retry');\r\n    }\r\n\r\n    if (this.isSecurityCriticalFailure()) {\r\n      conditions.push('Get approval for critical action retry');\r\n    }\r\n\r\n    return {\r\n      shouldRetry: true,\r\n      delayMinutes,\r\n      maxRetries,\r\n      conditions\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get failure metrics\r\n   */\r\n  getFailureMetrics(): {\r\n    actionType: ActionType;\r\n    failureCategory: string;\r\n    failureSeverity: string;\r\n    retryCount: number;\r\n    canRetry: boolean;\r\n    isSecurityCritical: boolean;\r\n    isAutomated: boolean;\r\n    isFinalFailure: boolean;\r\n  } {\r\n    return {\r\n      actionType: this.eventData.actionType,\r\n      failureCategory: this.getFailureCategory(),\r\n      failureSeverity: this.getFailureSeverity(),\r\n      retryCount: this.eventData.retryCount,\r\n      canRetry: this.eventData.canRetry,\r\n      isSecurityCritical: this.isSecurityCriticalFailure(),\r\n      isAutomated: this.isAutomatedFailure(),\r\n      isFinalFailure: this.isFinalFailure(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to integration event format\r\n   */\r\n  toIntegrationEvent(): {\r\n    eventType: string;\r\n    action: string;\r\n    resource: string;\r\n    resourceId: string;\r\n    data: ResponseActionFailedEventData;\r\n    metadata: {\r\n      failureSeverity: string;\r\n      failureCategory: string;\r\n      isSecurityCritical: boolean;\r\n      isFinalFailure: boolean;\r\n      escalationRequired: boolean;\r\n      canRetry: boolean;\r\n    };\r\n  } {\r\n    const escalation = this.getEscalationRequirements();\r\n    \r\n    return {\r\n      eventType: 'ResponseActionFailed',\r\n      action: 'response_action_failed',\r\n      resource: 'ResponseAction',\r\n      resourceId: this.aggregateId.toString(),\r\n      data: this.eventData,\r\n      metadata: {\r\n        failureSeverity: this.getFailureSeverity(),\r\n        failureCategory: this.getFailureCategory(),\r\n        isSecurityCritical: this.isSecurityCriticalFailure(),\r\n        isFinalFailure: this.isFinalFailure(),\r\n        escalationRequired: escalation.immediate,\r\n        canRetry: this.eventData.canRetry,\r\n      },\r\n    };\r\n  }\r\n}"], "version": 3}