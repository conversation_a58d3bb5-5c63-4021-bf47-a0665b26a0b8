{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\config-change-detector.service.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAmF;AACnF,2CAA+C;AAC/C,yDAAsD;AACtD,uCAA4C;AAC5C,2BAA8C;AAC9C,+BAA4B;AAC5B,mCAAkC;AAClC,+CAAiC;AAiC1B,IAAM,2BAA2B,mCAAjC,MAAM,2BAA2B;IAOtC,YACmB,aAA4B,EAC5B,YAA2B;QAD3B,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAe;QAR7B,WAAM,GAAG,IAAI,eAAM,CAAC,6BAA2B,CAAC,IAAI,CAAC,CAAC;QAE/D,kBAAa,GAAwB,EAAE,CAAC;QAQ9C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC3C,IAAI,CAAC,qBAAqB,GAAG,IAAA,iBAAQ,EACnC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAClC,IAAI,CAAC,OAAO,CAAC,UAAU,CACxB,CAAC;QACF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC5B,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACxC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAA,gBAAK,EAAC,UAAU,EAAE;YACnC,OAAO,EAAE,eAAe,EAAE,kBAAkB;YAC5C,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,gBAAgB,EAAE;gBAChB,kBAAkB,EAAE,GAAG;gBACvB,YAAY,EAAE,GAAG;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,IAAI,EAAE,CAAC,CAAC;YACzD,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,IAAI,EAAE,CAAC,CAAC;YACvD,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,IAAI,EAAE,CAAC,CAAC;YACzD,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,UAAU,CAAC,MAAM,kCAAkC,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,GAAW;QACvB,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC;YACvC,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC;YAC3B,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC;YACxB,GAAG,KAAK,MAAM;YACd,GAAG,KAAK,UAAU,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,OAAuB;QAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACnC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;QACvC,CAAC;QAED,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC;gBACH,4EAA4E;gBAC5E,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACnD,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;oBAC3C,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,EAAE,CAAC;wBAC5C,MAAM,CAAC,IAAI,CAAC,0BAA0B,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC1E,CAAC;gBACH,CAAC;gBAED,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;oBAClD,IAAI,CAAC;wBACH,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAC3B,CAAC;oBAAC,MAAM,CAAC;wBACP,MAAM,CAAC,IAAI,CAAC,yBAAyB,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;oBACzE,CAAC;gBACH,CAAC;gBAED,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACtD,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;wBACzE,MAAM,CAAC,IAAI,CAAC,6BAA6B,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC7E,CAAC;gBACH,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,wBAAwB,MAAM,CAAC,GAAG,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YACjH,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,OAAuB;QAChD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;gBACtB,KAAK,OAAO,CAAC;gBACb,KAAK,UAAU;oBACb,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;oBAC1C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;oBACjD,MAAM;gBACR,KAAK,SAAS;oBACZ,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBACtC,MAAM;YACV,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,OAAO,CAAC,MAAM,wBAAwB,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACtD,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YAElE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;gBACvD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,OAAO,CAAC,MAAM,wBAAwB,CAAC,CAAC;YAEpE,mBAAmB;YACnB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACvD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;gBACzE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,6BAA6B;YAC7B,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YACjF,MAAM,kBAAkB,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;YAEtD,IAAI,kBAAkB,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC;gBAC/D,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,4CAA4C,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;oBAC1F,sCAAsC,CACvC,CAAC;YACJ,CAAC;YAED,6BAA6B;YAC7B,MAAM,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YACrF,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;YAC9C,CAAC;YAED,sBAAsB;YACtB,MAAM,WAAW,GAAsB;gBACrC,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,mBAAmB;gBAC3B,OAAO;gBACP,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,aAAa;YACb,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;YAEtD,cAAc;YACd,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAEzB,OAAO,WAAW,CAAC;QAErB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,SAA8B,EAAE,SAA8B;QAClF,MAAM,OAAO,GAAmB,EAAE,CAAC;QACnC,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAEhF,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;YAC1B,qBAAqB;YACrB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3C,SAAS;YACX,CAAC;YAED,0DAA0D;YAC1D,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACnF,SAAS;YACX,CAAC;YAED,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;YAChC,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;YAEhC,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACrD,OAAO,CAAC,IAAI,CAAC;oBACX,GAAG;oBACH,QAAQ,EAAE,SAAS;oBACnB,QAAQ;oBACR,MAAM,EAAE,OAAO;iBAChB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAC5D,OAAO,CAAC,IAAI,CAAC;oBACX,GAAG;oBACH,QAAQ;oBACR,QAAQ,EAAE,SAAS;oBACnB,MAAM,EAAE,SAAS;iBAClB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,OAAO,CAAC,IAAI,CAAC;oBACX,GAAG;oBACH,QAAQ;oBACR,QAAQ;oBACR,MAAM,EAAE,UAAU;iBACnB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,4BAA4B;QAClC,MAAM,MAAM,GAAwB,EAAE,CAAC;QACvC,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,aAAa,CAAC;QAE5D,gDAAgD;QAChD,MAAM,QAAQ,GAAG;YACf,MAAM;YACN,YAAY;YACZ,QAAQ,UAAU,EAAE;YACpB,QAAQ,UAAU,QAAQ;SAC3B,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;YAC9C,IAAI,IAAA,eAAU,EAAC,QAAQ,CAAC,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACH,MAAM,WAAW,GAAG,IAAA,iBAAY,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;oBACnD,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;oBACzC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAChC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;gBACzE,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,OAAO,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,aAAa,CAAC;QAE5D,4BAA4B;QAC5B,MAAM,YAAY,GAAG;YACnB,MAAM;YACN,YAAY;YACZ,QAAQ,UAAU,EAAE;YACpB,QAAQ,UAAU,QAAQ;SAC3B,CAAC;QAEF,mCAAmC;QACnC,KAAK,MAAM,IAAI,IAAI,CAAC,GAAG,YAAY,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACjE,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,CAAC;YAC3C,IAAI,IAAA,eAAU,EAAC,QAAQ,CAAC,EAAE,CAAC;gBACzB,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvB,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,MAAM,aAAa,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,aAAa,CAAC,KAAK,aAAa,CAAC;QAEnF,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,KAAK,MAAM,IAAI,aAAa;YAC7E,UAAU,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;YAChF,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,MAAM,EAAE,EAAE,CAAC;YACrE,WAAW,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;YAClF,WAAW,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;YAClF,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,KAAK,OAAO;YACtE,uBAAuB,EAAE,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,KAAK,MAAM;YACpF,YAAY,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,iEAAiE,CAAC;iBACrH,KAAK,CAAC,GAAG,CAAC;iBACV,MAAM,CAAC,OAAO,CAAC;SACnB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,OAAuB;QACxC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,UAAU,GAAG,UAAU,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,GAAG,EAAE,CAAC;YAE5D,IAAI,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAC7C,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC3C,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,UAAU,eAAe,CAAC,CAAC;YAChD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,UAAU,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AAtYY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;yDASuB,sBAAa,oBAAb,sBAAa,oDACd,6BAAa,oBAAb,6BAAa;GATnC,2BAA2B,CAsYvC;AAED;;GAEG;AACI,MAAM,cAAc,GAAG,CAAC,IAAe,EAAE,EAAE;IAChD,OAAO,CAAC,MAAW,EAAE,YAAoB,EAAE,UAA8B,EAAE,EAAE;QAC3E,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;QAExC,UAAU,CAAC,KAAK,GAAG,UAAU,GAAG,IAAW;YACzC,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAsB,CAAC;YAE3C,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;gBAClF,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACjC,OAAO;gBACT,CAAC;YACH,CAAC;YAED,OAAO,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC,CAAC;AAnBW,QAAA,cAAc,kBAmBzB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\config-change-detector.service.ts"], "sourcesContent": ["import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { EventEmitter2 } from '@nestjs/event-emitter';\r\nimport { watch, FSWatcher } from 'chokidar';\r\nimport { readFileSync, existsSync } from 'fs';\r\nimport { join } from 'path';\r\nimport { debounce } from 'lodash';\r\nimport * as dotenv from 'dotenv';\r\n\r\n/**\r\n * Configuration change detection and hot-reload service\r\n * Monitors environment files and configuration changes for hot-reloading\r\n */\r\n\r\nexport interface ConfigChangeEvent {\r\n  type: 'file' | 'environment' | 'manual';\r\n  source: string;\r\n  changes: ConfigChange[];\r\n  timestamp: Date;\r\n}\r\n\r\nexport interface ConfigChange {\r\n  key: string;\r\n  oldValue: any;\r\n  newValue: any;\r\n  action: 'added' | 'modified' | 'removed';\r\n}\r\n\r\nexport interface HotReloadOptions {\r\n  enabled: boolean;\r\n  watchFiles: string[];\r\n  debounceMs: number;\r\n  excludeKeys: string[];\r\n  includeKeys: string[];\r\n  validateOnChange: boolean;\r\n  restartOnCriticalChange: boolean;\r\n  criticalKeys: string[];\r\n}\r\n\r\n@Injectable()\r\nexport class ConfigChangeDetectorService implements OnModuleInit, OnModuleDestroy {\r\n  private readonly logger = new Logger(ConfigChangeDetectorService.name);\r\n  private fileWatcher?: FSWatcher;\r\n  private currentConfig: Record<string, any> = {};\r\n  private readonly debouncedHandleChange: () => void;\r\n  private readonly options: HotReloadOptions;\r\n\r\n  constructor(\r\n    private readonly configService: ConfigService,\r\n    private readonly eventEmitter: EventEmitter2,\r\n  ) {\r\n    this.options = this.loadHotReloadOptions();\r\n    this.debouncedHandleChange = debounce(\r\n      this.handleConfigChange.bind(this),\r\n      this.options.debounceMs,\r\n    );\r\n    this.currentConfig = this.captureCurrentConfig();\r\n  }\r\n\r\n  async onModuleInit(): Promise<void> {\r\n    if (this.options.enabled) {\r\n      await this.startWatching();\r\n      this.logger.log('Configuration change detection started');\r\n    } else {\r\n      this.logger.debug('Configuration change detection disabled');\r\n    }\r\n  }\r\n\r\n  async onModuleDestroy(): Promise<void> {\r\n    await this.stopWatching();\r\n    this.logger.log('Configuration change detection stopped');\r\n  }\r\n\r\n  /**\r\n   * Start watching configuration files for changes\r\n   */\r\n  async startWatching(): Promise<void> {\r\n    if (this.fileWatcher) {\r\n      await this.stopWatching();\r\n    }\r\n\r\n    const watchPaths = this.getWatchPaths();\r\n    if (watchPaths.length === 0) {\r\n      this.logger.warn('No configuration files found to watch');\r\n      return;\r\n    }\r\n\r\n    this.fileWatcher = watch(watchPaths, {\r\n      ignored: /(^|[\\/\\\\])\\../, // ignore dotfiles\r\n      persistent: true,\r\n      ignoreInitial: true,\r\n      awaitWriteFinish: {\r\n        stabilityThreshold: 100,\r\n        pollInterval: 100,\r\n      },\r\n    });\r\n\r\n    this.fileWatcher.on('change', (path) => {\r\n      this.logger.debug(`Configuration file changed: ${path}`);\r\n      this.debouncedHandleChange();\r\n    });\r\n\r\n    this.fileWatcher.on('add', (path) => {\r\n      this.logger.debug(`Configuration file added: ${path}`);\r\n      this.debouncedHandleChange();\r\n    });\r\n\r\n    this.fileWatcher.on('unlink', (path) => {\r\n      this.logger.debug(`Configuration file removed: ${path}`);\r\n      this.debouncedHandleChange();\r\n    });\r\n\r\n    this.fileWatcher.on('error', (error) => {\r\n      this.logger.error('File watcher error:', error);\r\n    });\r\n\r\n    this.logger.log(`Watching ${watchPaths.length} configuration files for changes`);\r\n  }\r\n\r\n  /**\r\n   * Stop watching configuration files\r\n   */\r\n  async stopWatching(): Promise<void> {\r\n    if (this.fileWatcher) {\r\n      await this.fileWatcher.close();\r\n      this.fileWatcher = undefined;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Manually trigger configuration reload\r\n   */\r\n  async reloadConfiguration(): Promise<ConfigChangeEvent | null> {\r\n    this.logger.log('Manual configuration reload triggered');\r\n    return this.handleConfigChange();\r\n  }\r\n\r\n  /**\r\n   * Get current configuration snapshot\r\n   */\r\n  getCurrentConfig(): Record<string, any> {\r\n    return { ...this.currentConfig };\r\n  }\r\n\r\n  /**\r\n   * Check if a configuration key is critical (requires restart)\r\n   */\r\n  isCriticalKey(key: string): boolean {\r\n    return this.options.criticalKeys.includes(key) ||\r\n           key.startsWith('DATABASE_') ||\r\n           key.startsWith('REDIS_') ||\r\n           key === 'PORT' ||\r\n           key === 'NODE_ENV';\r\n  }\r\n\r\n  /**\r\n   * Validate configuration changes\r\n   */\r\n  async validateChanges(changes: ConfigChange[]): Promise<{ isValid: boolean; errors: string[] }> {\r\n    if (!this.options.validateOnChange) {\r\n      return { isValid: true, errors: [] };\r\n    }\r\n\r\n    const errors: string[] = [];\r\n\r\n    for (const change of changes) {\r\n      try {\r\n        // Basic validation - you can extend this with more sophisticated validation\r\n        if (change.key.includes('PORT') && change.newValue) {\r\n          const port = parseInt(change.newValue, 10);\r\n          if (isNaN(port) || port < 1 || port > 65535) {\r\n            errors.push(`Invalid port value for ${change.key}: ${change.newValue}`);\r\n          }\r\n        }\r\n\r\n        if (change.key.includes('URL') && change.newValue) {\r\n          try {\r\n            new URL(change.newValue);\r\n          } catch {\r\n            errors.push(`Invalid URL value for ${change.key}: ${change.newValue}`);\r\n          }\r\n        }\r\n\r\n        if (change.key.includes('BOOLEAN') && change.newValue) {\r\n          if (!['true', 'false', '1', '0'].includes(change.newValue.toLowerCase())) {\r\n            errors.push(`Invalid boolean value for ${change.key}: ${change.newValue}`);\r\n          }\r\n        }\r\n\r\n      } catch (error) {\r\n        errors.push(`Validation error for ${change.key}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Apply configuration changes to the current process\r\n   */\r\n  private async applyChanges(changes: ConfigChange[]): Promise<void> {\r\n    for (const change of changes) {\r\n      switch (change.action) {\r\n        case 'added':\r\n        case 'modified':\r\n          process.env[change.key] = change.newValue;\r\n          this.currentConfig[change.key] = change.newValue;\r\n          break;\r\n        case 'removed':\r\n          delete process.env[change.key];\r\n          delete this.currentConfig[change.key];\r\n          break;\r\n      }\r\n    }\r\n\r\n    this.logger.debug(`Applied ${changes.length} configuration changes`);\r\n  }\r\n\r\n  /**\r\n   * Handle configuration change event\r\n   */\r\n  private async handleConfigChange(): Promise<ConfigChangeEvent | null> {\r\n    try {\r\n      const newConfig = this.loadCurrentEnvironmentConfig();\r\n      const changes = this.detectChanges(this.currentConfig, newConfig);\r\n\r\n      if (changes.length === 0) {\r\n        this.logger.debug('No configuration changes detected');\r\n        return null;\r\n      }\r\n\r\n      this.logger.log(`Detected ${changes.length} configuration changes`);\r\n\r\n      // Validate changes\r\n      const validation = await this.validateChanges(changes);\r\n      if (!validation.isValid) {\r\n        this.logger.error('Configuration validation failed:', validation.errors);\r\n        return null;\r\n      }\r\n\r\n      // Check for critical changes\r\n      const criticalChanges = changes.filter(change => this.isCriticalKey(change.key));\r\n      const hasCriticalChanges = criticalChanges.length > 0;\r\n\r\n      if (hasCriticalChanges && this.options.restartOnCriticalChange) {\r\n        this.logger.warn(\r\n          `Critical configuration changes detected: ${criticalChanges.map(c => c.key).join(', ')}. ` +\r\n          'Application restart may be required.'\r\n        );\r\n      }\r\n\r\n      // Apply non-critical changes\r\n      const nonCriticalChanges = changes.filter(change => !this.isCriticalKey(change.key));\r\n      if (nonCriticalChanges.length > 0) {\r\n        await this.applyChanges(nonCriticalChanges);\r\n      }\r\n\r\n      // Create change event\r\n      const changeEvent: ConfigChangeEvent = {\r\n        type: 'file',\r\n        source: 'environment-files',\r\n        changes,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      // Emit event\r\n      this.eventEmitter.emit('config.changed', changeEvent);\r\n\r\n      // Log changes\r\n      this.logChanges(changes);\r\n\r\n      return changeEvent;\r\n\r\n    } catch (error) {\r\n      this.logger.error('Error handling configuration change:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Detect changes between old and new configuration\r\n   */\r\n  private detectChanges(oldConfig: Record<string, any>, newConfig: Record<string, any>): ConfigChange[] {\r\n    const changes: ConfigChange[] = [];\r\n    const allKeys = new Set([...Object.keys(oldConfig), ...Object.keys(newConfig)]);\r\n\r\n    for (const key of allKeys) {\r\n      // Skip excluded keys\r\n      if (this.options.excludeKeys.includes(key)) {\r\n        continue;\r\n      }\r\n\r\n      // Only include specified keys if includeKeys is not empty\r\n      if (this.options.includeKeys.length > 0 && !this.options.includeKeys.includes(key)) {\r\n        continue;\r\n      }\r\n\r\n      const oldValue = oldConfig[key];\r\n      const newValue = newConfig[key];\r\n\r\n      if (oldValue === undefined && newValue !== undefined) {\r\n        changes.push({\r\n          key,\r\n          oldValue: undefined,\r\n          newValue,\r\n          action: 'added',\r\n        });\r\n      } else if (oldValue !== undefined && newValue === undefined) {\r\n        changes.push({\r\n          key,\r\n          oldValue,\r\n          newValue: undefined,\r\n          action: 'removed',\r\n        });\r\n      } else if (oldValue !== newValue) {\r\n        changes.push({\r\n          key,\r\n          oldValue,\r\n          newValue,\r\n          action: 'modified',\r\n        });\r\n      }\r\n    }\r\n\r\n    return changes;\r\n  }\r\n\r\n  /**\r\n   * Load current environment configuration from files\r\n   */\r\n  private loadCurrentEnvironmentConfig(): Record<string, any> {\r\n    const config: Record<string, any> = {};\r\n    const currentEnv = process.env['NODE_ENV'] || 'development';\r\n\r\n    // Load environment files in order of precedence\r\n    const envFiles = [\r\n      '.env',\r\n      '.env.local',\r\n      `.env.${currentEnv}`,\r\n      `.env.${currentEnv}.local`,\r\n    ];\r\n\r\n    for (const envFile of envFiles) {\r\n      const filePath = join(process.cwd(), envFile);\r\n      if (existsSync(filePath)) {\r\n        try {\r\n          const fileContent = readFileSync(filePath, 'utf8');\r\n          const parsed = dotenv.parse(fileContent);\r\n          Object.assign(config, parsed);\r\n        } catch (error) {\r\n          this.logger.warn(`Failed to load environment file ${envFile}:`, error);\r\n        }\r\n      }\r\n    }\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Capture current configuration state\r\n   */\r\n  private captureCurrentConfig(): Record<string, any> {\r\n    return { ...process.env };\r\n  }\r\n\r\n  /**\r\n   * Get paths to watch for configuration changes\r\n   */\r\n  private getWatchPaths(): string[] {\r\n    const paths: string[] = [];\r\n    const currentEnv = process.env['NODE_ENV'] || 'development';\r\n\r\n    // Default environment files\r\n    const defaultFiles = [\r\n      '.env',\r\n      '.env.local',\r\n      `.env.${currentEnv}`,\r\n      `.env.${currentEnv}.local`,\r\n    ];\r\n\r\n    // Add existing files to watch list\r\n    for (const file of [...defaultFiles, ...this.options.watchFiles]) {\r\n      const filePath = join(process.cwd(), file);\r\n      if (existsSync(filePath)) {\r\n        paths.push(filePath);\r\n      }\r\n    }\r\n\r\n    return paths;\r\n  }\r\n\r\n  /**\r\n   * Load hot-reload options from configuration\r\n   */\r\n  private loadHotReloadOptions(): HotReloadOptions {\r\n    const isDevelopment = (process.env['NODE_ENV'] || 'development') === 'development';\r\n    \r\n    return {\r\n      enabled: process.env['CONFIG_HOT_RELOAD_ENABLED'] === 'true' || isDevelopment,\r\n      watchFiles: (process.env['CONFIG_WATCH_FILES'] || '').split(',').filter(Boolean),\r\n      debounceMs: parseInt(process.env['CONFIG_DEBOUNCE_MS'] || '1000', 10),\r\n      excludeKeys: (process.env['CONFIG_EXCLUDE_KEYS'] || '').split(',').filter(Boolean),\r\n      includeKeys: (process.env['CONFIG_INCLUDE_KEYS'] || '').split(',').filter(Boolean),\r\n      validateOnChange: process.env['CONFIG_VALIDATE_ON_CHANGE'] !== 'false',\r\n      restartOnCriticalChange: process.env['CONFIG_RESTART_ON_CRITICAL_CHANGE'] === 'true',\r\n      criticalKeys: (process.env['CONFIG_CRITICAL_KEYS'] || 'DATABASE_HOST,DATABASE_PORT,REDIS_HOST,REDIS_PORT,PORT,NODE_ENV')\r\n        .split(',')\r\n        .filter(Boolean),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Log configuration changes\r\n   */\r\n  private logChanges(changes: ConfigChange[]): void {\r\n    for (const change of changes) {\r\n      const logMessage = `Config ${change.action}: ${change.key}`;\r\n      \r\n      if (change.key.toLowerCase().includes('password') || \r\n          change.key.toLowerCase().includes('secret') || \r\n          change.key.toLowerCase().includes('key')) {\r\n        this.logger.log(`${logMessage} = [REDACTED]`);\r\n      } else {\r\n        this.logger.log(`${logMessage} = ${change.oldValue} -> ${change.newValue}`);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Configuration change event listener decorator\r\n */\r\nexport const OnConfigChange = (keys?: string[]) => {\r\n  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {\r\n    const originalMethod = descriptor.value;\r\n    \r\n    descriptor.value = function (...args: any[]) {\r\n      const event = args[0] as ConfigChangeEvent;\r\n      \r\n      if (keys && keys.length > 0) {\r\n        const relevantChanges = event.changes.filter(change => keys.includes(change.key));\r\n        if (relevantChanges.length === 0) {\r\n          return;\r\n        }\r\n      }\r\n      \r\n      return originalMethod.apply(this, args);\r\n    };\r\n    \r\n    return descriptor;\r\n  };\r\n};"], "version": 3}