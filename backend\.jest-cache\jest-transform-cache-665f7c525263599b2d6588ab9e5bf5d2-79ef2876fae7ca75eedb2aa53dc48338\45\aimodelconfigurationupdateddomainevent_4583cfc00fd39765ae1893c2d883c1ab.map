{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\ai-model-configuration-updated.domain-event.ts", "mappings": ";;;AAAA,0FAAqF;AAIrF;;;;;;GAMG;AACH,MAAa,gCAAiC,SAAQ,mCAAe;IACnE,YACkB,OAAuB,EACvB,aAAiC,EACjD,OAAwB,EACxB,UAAiB;QAEjB,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QALX,YAAO,GAAP,OAAO,CAAgB;QACvB,kBAAa,GAAb,aAAa,CAAoB;IAKnD,CAAC;IAEM,YAAY;QACjB,OAAO,6BAA6B,CAAC;IACvC,CAAC;IAEM,eAAe;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,YAAY;QACjB,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAChC,aAAa,EAAE;gBACb,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ;gBACrC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO;gBACnC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO;gBACnC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS;gBACvC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc;gBACjD,sDAAsD;aACvD;SACF,CAAC;IACJ,CAAC;CACF;AA/BD,4EA+BC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\ai-model-configuration-updated.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent } from '../../../../shared-kernel/domain/base-domain-event';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { ModelConfiguration } from '../entities/ai-model.entity';\r\n\r\n/**\r\n * AI Model Configuration Updated Domain Event\r\n * \r\n * Published when an AI model's configuration is updated.\r\n * This event can trigger various downstream processes such as\r\n * model redeployment, configuration validation, and monitoring updates.\r\n */\r\nexport class AIModelConfigurationUpdatedEvent extends BaseDomainEvent {\r\n  constructor(\r\n    public readonly modelId: UniqueEntityId,\r\n    public readonly configuration: ModelConfiguration,\r\n    eventId?: UniqueEntityId,\r\n    occurredOn?: Date\r\n  ) {\r\n    super(eventId, occurredOn);\r\n  }\r\n\r\n  public getEventName(): string {\r\n    return 'AIModelConfigurationUpdated';\r\n  }\r\n\r\n  public getEventVersion(): string {\r\n    return '1.0';\r\n  }\r\n\r\n  public getEventData(): Record<string, any> {\r\n    return {\r\n      modelId: this.modelId.toString(),\r\n      configuration: {\r\n        endpoint: this.configuration.endpoint,\r\n        timeout: this.configuration.timeout,\r\n        retries: this.configuration.retries,\r\n        batchSize: this.configuration.batchSize,\r\n        customSettings: this.configuration.customSettings,\r\n        // Note: apiKey is intentionally excluded for security\r\n      },\r\n    };\r\n  }\r\n}"], "version": 3}