039872431cc083c9e4455ab8868893e4
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const unauthorized_exception_1 = require("../../exceptions/unauthorized.exception");
describe('UnauthorizedException', () => {
    describe('constructor', () => {
        it('should create exception with default message', () => {
            const exception = new unauthorized_exception_1.UnauthorizedException();
            expect(exception).toBeInstanceOf(unauthorized_exception_1.UnauthorizedException);
            expect(exception.message).toBe('Authentication is required to access this resource');
            expect(exception.code).toBe('UNAUTHORIZED');
            expect(exception.severity).toBe('medium');
            expect(exception.category).toBe('authentication');
        });
        it('should create exception with custom message and options', () => {
            const exception = new unauthorized_exception_1.UnauthorizedException('Custom auth message', {
                authenticationScheme: 'Bearer',
                credentialType: 'JWT',
                reason: 'invalid_credentials',
                correlationId: 'test-correlation-id',
            });
            expect(exception.message).toBe('Custom auth message');
            expect(exception.authenticationScheme).toBe('Bearer');
            expect(exception.credentialType).toBe('JWT');
            expect(exception.reason).toBe('invalid_credentials');
            expect(exception.correlationId).toBe('test-correlation-id');
        });
    });
    describe('static factory methods', () => {
        describe('missingAuthentication', () => {
            it('should create exception for missing authentication', () => {
                const exception = unauthorized_exception_1.UnauthorizedException.missingAuthentication({
                    authenticationScheme: 'Bearer',
                    correlationId: 'test-id',
                });
                expect(exception.message).toBe('Authentication credentials are required');
                expect(exception.reason).toBe('missing_credentials');
                expect(exception.authenticationScheme).toBe('Bearer');
                expect(exception.isMissingCredentials()).toBe(true);
            });
        });
        describe('invalidCredentials', () => {
            it('should create exception for invalid credentials', () => {
                const exception = unauthorized_exception_1.UnauthorizedException.invalidCredentials('token', {
                    authenticationScheme: 'Bearer',
                });
                expect(exception.message).toBe('Invalid token provided');
                expect(exception.reason).toBe('invalid_credentials');
                expect(exception.credentialType).toBe('token');
                expect(exception.isInvalidCredentials()).toBe(true);
            });
        });
        describe('expiredCredentials', () => {
            it('should create exception for expired credentials', () => {
                const expirationTime = new Date('2023-01-01T00:00:00Z');
                const exception = unauthorized_exception_1.UnauthorizedException.expiredCredentials('JWT', {
                    expirationTime,
                });
                expect(exception.message).toBe('JWT has expired');
                expect(exception.reason).toBe('expired_credentials');
                expect(exception.credentialType).toBe('JWT');
                expect(exception.isExpiredCredentials()).toBe(true);
                expect(exception.getExpirationTime()).toEqual(expirationTime);
            });
        });
        describe('revokedCredentials', () => {
            it('should create exception for revoked credentials', () => {
                const revocationTime = new Date('2023-01-01T00:00:00Z');
                const exception = unauthorized_exception_1.UnauthorizedException.revokedCredentials('API key', {
                    revocationTime,
                });
                expect(exception.message).toBe('API key has been revoked');
                expect(exception.reason).toBe('revoked_credentials');
                expect(exception.credentialType).toBe('API key');
                expect(exception.isRevokedCredentials()).toBe(true);
                expect(exception.getRevocationTime()).toEqual(revocationTime);
            });
        });
        describe('malformedCredentials', () => {
            it('should create exception for malformed credentials', () => {
                const exception = unauthorized_exception_1.UnauthorizedException.malformedCredentials('JWT');
                expect(exception.message).toBe('Malformed JWT provided');
                expect(exception.reason).toBe('malformed_credentials');
                expect(exception.credentialType).toBe('JWT');
                expect(exception.isMalformedCredentials()).toBe(true);
            });
        });
        describe('unsupportedScheme', () => {
            it('should create exception for unsupported scheme', () => {
                const supportedSchemes = ['Bearer', 'Basic'];
                const exception = unauthorized_exception_1.UnauthorizedException.unsupportedScheme('Digest', supportedSchemes);
                expect(exception.message).toBe("Authentication scheme 'Digest' is not supported. Supported schemes: Bearer, Basic");
                expect(exception.reason).toBe('unsupported_scheme');
                expect(exception.authenticationScheme).toBe('Digest');
                expect(exception.isUnsupportedScheme()).toBe(true);
                expect(exception.getSupportedSchemes()).toEqual(supportedSchemes);
            });
            it('should create exception without supported schemes list', () => {
                const exception = unauthorized_exception_1.UnauthorizedException.unsupportedScheme('Digest');
                expect(exception.message).toBe("Authentication scheme 'Digest' is not supported");
                expect(exception.getSupportedSchemes()).toEqual([]);
            });
        });
    });
    describe('type checking methods', () => {
        it('should correctly identify missing credentials', () => {
            const exception = unauthorized_exception_1.UnauthorizedException.missingAuthentication();
            expect(exception.isMissingCredentials()).toBe(true);
            expect(exception.isInvalidCredentials()).toBe(false);
            expect(exception.isExpiredCredentials()).toBe(false);
            expect(exception.isRevokedCredentials()).toBe(false);
            expect(exception.isMalformedCredentials()).toBe(false);
            expect(exception.isUnsupportedScheme()).toBe(false);
        });
        it('should correctly identify invalid credentials', () => {
            const exception = unauthorized_exception_1.UnauthorizedException.invalidCredentials('password');
            expect(exception.isMissingCredentials()).toBe(false);
            expect(exception.isInvalidCredentials()).toBe(true);
            expect(exception.isExpiredCredentials()).toBe(false);
            expect(exception.isRevokedCredentials()).toBe(false);
            expect(exception.isMalformedCredentials()).toBe(false);
            expect(exception.isUnsupportedScheme()).toBe(false);
        });
    });
    describe('getUserMessage', () => {
        it('should return user-friendly message for missing credentials', () => {
            const exception = unauthorized_exception_1.UnauthorizedException.missingAuthentication();
            expect(exception.getUserMessage()).toBe('Please provide valid authentication credentials');
        });
        it('should return user-friendly message for invalid credentials', () => {
            const exception = unauthorized_exception_1.UnauthorizedException.invalidCredentials('password');
            expect(exception.getUserMessage()).toBe('The provided credentials are invalid');
        });
        it('should return user-friendly message for expired credentials', () => {
            const exception = unauthorized_exception_1.UnauthorizedException.expiredCredentials('token');
            expect(exception.getUserMessage()).toBe('Your authentication has expired. Please sign in again');
        });
        it('should return user-friendly message for revoked credentials', () => {
            const exception = unauthorized_exception_1.UnauthorizedException.revokedCredentials('token');
            expect(exception.getUserMessage()).toBe('Your authentication has been revoked. Please sign in again');
        });
        it('should return user-friendly message for malformed credentials', () => {
            const exception = unauthorized_exception_1.UnauthorizedException.malformedCredentials('token');
            expect(exception.getUserMessage()).toBe('The authentication format is invalid');
        });
        it('should return user-friendly message for unsupported scheme', () => {
            const exception = unauthorized_exception_1.UnauthorizedException.unsupportedScheme('Digest');
            expect(exception.getUserMessage()).toBe('The authentication method is not supported');
        });
        it('should return default message for unknown reason', () => {
            const exception = new unauthorized_exception_1.UnauthorizedException('Test message', {
                reason: 'unknown_reason',
            });
            expect(exception.getUserMessage()).toBe('Authentication is required to access this resource');
        });
    });
    describe('toApiResponse', () => {
        it('should convert to API response format', () => {
            const exception = unauthorized_exception_1.UnauthorizedException.invalidCredentials('JWT', {
                authenticationScheme: 'Bearer',
            });
            const response = exception.toApiResponse();
            expect(response).toEqual({
                error: 'The provided credentials are invalid',
                code: 'UNAUTHORIZED',
                details: {
                    reason: 'invalid_credentials',
                    authenticationScheme: 'Bearer',
                    credentialType: 'JWT',
                    supportedSchemes: [],
                },
            });
        });
    });
    describe('toJSON', () => {
        it('should convert to JSON with detailed information', () => {
            const expirationTime = new Date('2023-01-01T00:00:00Z');
            const exception = unauthorized_exception_1.UnauthorizedException.expiredCredentials('JWT', {
                authenticationScheme: 'Bearer',
                expirationTime,
            });
            const json = exception.toJSON();
            expect(json).toMatchObject({
                name: 'UnauthorizedException',
                message: 'JWT has expired',
                code: 'UNAUTHORIZED',
                severity: 'medium',
                category: 'authentication',
                authenticationScheme: 'Bearer',
                credentialType: 'JWT',
                reason: 'expired_credentials',
                isMissingCredentials: false,
                isInvalidCredentials: false,
                isExpiredCredentials: true,
                isRevokedCredentials: false,
                isMalformedCredentials: false,
                isUnsupportedScheme: false,
                supportedSchemes: [],
                expirationTime: expirationTime.toISOString(),
            });
        });
    });
    describe('inheritance', () => {
        it('should be instance of Error and DomainException', () => {
            const exception = new unauthorized_exception_1.UnauthorizedException();
            expect(exception).toBeInstanceOf(Error);
            expect(exception.name).toBe('UnauthorizedException');
            expect(exception.code).toBe('UNAUTHORIZED');
        });
        it('should maintain proper prototype chain', () => {
            const exception = new unauthorized_exception_1.UnauthorizedException();
            expect(exception instanceof unauthorized_exception_1.UnauthorizedException).toBe(true);
            expect(exception instanceof Error).toBe(true);
        });
    });
    describe('context and correlation', () => {
        it('should preserve context and correlation ID', () => {
            const context = { userId: 'user123', sessionId: 'session456' };
            const correlationId = 'correlation-123';
            const exception = new unauthorized_exception_1.UnauthorizedException('Test message', {
                context,
                correlationId,
            });
            expect(exception.context).toMatchObject(context);
            expect(exception.correlationId).toBe(correlationId);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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