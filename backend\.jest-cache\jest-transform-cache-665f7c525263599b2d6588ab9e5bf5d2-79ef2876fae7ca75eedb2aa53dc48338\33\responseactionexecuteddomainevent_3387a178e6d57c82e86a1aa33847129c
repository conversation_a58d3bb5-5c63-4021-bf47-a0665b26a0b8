a1a794483253d3bb24bed9c7327bd0a3
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseActionExecutedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const action_type_enum_1 = require("../enums/action-type.enum");
/**
 * Response Action Executed Domain Event
 *
 * Raised when a response action has been successfully executed.
 * This event indicates that the action has completed its execution phase,
 * regardless of whether it was successful or not.
 *
 * Key information:
 * - Action type and execution details
 * - Who executed the action
 * - Execution timestamp and duration
 * - Execution results and success status
 * - Performance metrics
 *
 * Use cases:
 * - Track action execution completion
 * - Update monitoring dashboards
 * - Generate execution reports
 * - Trigger post-execution workflows
 * - Update related entities
 * - Log execution for audit trails
 */
class ResponseActionExecutedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the action type
     */
    get actionType() {
        return this.eventData.actionType;
    }
    /**
     * Get who executed the action
     */
    get executedBy() {
        return this.eventData.executedBy;
    }
    /**
     * Get when the action was executed
     */
    get executedAt() {
        return this.eventData.executedAt;
    }
    /**
     * Get the execution results
     */
    get executionResults() {
        return this.eventData.executionResults;
    }
    /**
     * Check if success criteria were met
     */
    get successCriteriaMet() {
        return this.eventData.successCriteriaMet;
    }
    /**
     * Get actual execution duration in minutes
     */
    get actualDurationMinutes() {
        return this.eventData.actualDurationMinutes;
    }
    /**
     * Check if the execution was successful
     */
    isSuccessful() {
        return this.eventData.successCriteriaMet === true;
    }
    /**
     * Check if the execution failed
     */
    isFailed() {
        return this.eventData.successCriteriaMet === false;
    }
    /**
     * Check if the execution was partial
     */
    isPartial() {
        return this.eventData.successCriteriaMet === undefined &&
            this.eventData.executionResults !== undefined;
    }
    /**
     * Check if this was an automated execution
     */
    isAutomatedExecution() {
        // Check if executed by system/automation
        return this.eventData.executedBy.includes('system') ||
            this.eventData.executedBy.includes('automation') ||
            this.eventData.executedBy.includes('bot');
    }
    /**
     * Check if this was a manual execution
     */
    isManualExecution() {
        return !this.isAutomatedExecution();
    }
    /**
     * Check if this is a security-critical action
     */
    isSecurityCritical() {
        const criticalActions = [
            action_type_enum_1.ActionType.ISOLATE_SYSTEM,
            action_type_enum_1.ActionType.SHUTDOWN_SYSTEM,
            action_type_enum_1.ActionType.DELETE_FILE,
            action_type_enum_1.ActionType.REMOVE_MALWARE,
            action_type_enum_1.ActionType.DISABLE_ACCOUNT,
            action_type_enum_1.ActionType.BLOCK_IP,
            action_type_enum_1.ActionType.QUARANTINE_FILE,
            action_type_enum_1.ActionType.UPDATE_FIREWALL,
        ];
        return criticalActions.includes(this.eventData.actionType);
    }
    /**
     * Check if this is a containment action
     */
    isContainmentAction() {
        const containmentActions = [
            action_type_enum_1.ActionType.ISOLATE_SYSTEM,
            action_type_enum_1.ActionType.QUARANTINE_FILE,
            action_type_enum_1.ActionType.BLOCK_IP,
            action_type_enum_1.ActionType.BLOCK_DOMAIN,
            action_type_enum_1.ActionType.BLOCK_URL,
            action_type_enum_1.ActionType.DISABLE_ACCOUNT,
            action_type_enum_1.ActionType.REVOKE_TOKEN,
            action_type_enum_1.ActionType.TERMINATE_CONNECTION,
            action_type_enum_1.ActionType.SHUTDOWN_SYSTEM,
        ];
        return containmentActions.includes(this.eventData.actionType);
    }
    /**
     * Check if this is a recovery action
     */
    isRecoveryAction() {
        const recoveryActions = [
            action_type_enum_1.ActionType.RESTORE_BACKUP,
            action_type_enum_1.ActionType.REBUILD_SYSTEM,
            action_type_enum_1.ActionType.RESTORE_NETWORK,
            action_type_enum_1.ActionType.ENABLE_SERVICE,
            action_type_enum_1.ActionType.RESET_PASSWORD,
            action_type_enum_1.ActionType.REGENERATE_CERTIFICATE,
        ];
        return recoveryActions.includes(this.eventData.actionType);
    }
    /**
     * Check if execution was fast (under 5 minutes)
     */
    isFastExecution() {
        return (this.eventData.actualDurationMinutes || 0) < 5;
    }
    /**
     * Check if execution was slow (over 30 minutes)
     */
    isSlowExecution() {
        return (this.eventData.actualDurationMinutes || 0) > 30;
    }
    /**
     * Get execution performance category
     */
    getPerformanceCategory() {
        const duration = this.eventData.actualDurationMinutes || 0;
        if (duration < 5)
            return 'fast';
        if (duration < 30)
            return 'normal';
        if (duration < 120)
            return 'slow';
        return 'very_slow';
    }
    /**
     * Get execution impact level
     */
    getImpactLevel() {
        if (this.isSecurityCritical()) {
            return this.isSuccessful() ? 'high' : 'critical';
        }
        if (this.isContainmentAction()) {
            return this.isSuccessful() ? 'medium' : 'high';
        }
        if (this.isRecoveryAction()) {
            return this.isSuccessful() ? 'medium' : 'high';
        }
        return 'low';
    }
    /**
     * Get recommended post-execution actions
     */
    getRecommendedPostActions() {
        const actions = [];
        if (this.isSuccessful()) {
            actions.push('Validate action effectiveness');
            actions.push('Update related security status');
            if (this.isContainmentAction()) {
                actions.push('Monitor for containment bypass');
                actions.push('Assess containment completeness');
            }
            if (this.isRecoveryAction()) {
                actions.push('Verify system functionality');
                actions.push('Test recovered services');
            }
        }
        else {
            actions.push('Analyze execution failure');
            actions.push('Assess security impact of failure');
            actions.push('Consider alternative actions');
            if (this.isSecurityCritical()) {
                actions.push('Escalate to incident response team');
                actions.push('Implement manual fallback procedures');
            }
        }
        if (this.isSlowExecution()) {
            actions.push('Investigate performance issues');
            actions.push('Optimize action execution');
        }
        return actions;
    }
    /**
     * Get stakeholders to notify
     */
    getNotificationTargets() {
        const targets = [];
        if (this.isSuccessful()) {
            targets.push('action-requestor');
            if (this.isSecurityCritical()) {
                targets.push('security-team');
            }
        }
        else {
            targets.push('action-requestor');
            targets.push('security-team');
            if (this.isSecurityCritical()) {
                targets.push('incident-response-team');
                targets.push('security-managers');
            }
        }
        if (this.isSlowExecution()) {
            targets.push('performance-team');
        }
        return targets;
    }
    /**
     * Get execution metrics
     */
    getExecutionMetrics() {
        return {
            actionType: this.eventData.actionType,
            executionTime: this.eventData.actualDurationMinutes || 0,
            performanceCategory: this.getPerformanceCategory(),
            impactLevel: this.getImpactLevel(),
            isSuccessful: this.isSuccessful(),
            isAutomated: this.isAutomatedExecution(),
            isSecurityCritical: this.isSecurityCritical(),
        };
    }
    /**
     * Get compliance information
     */
    getComplianceInfo() {
        const requiresDocumentation = this.isSecurityCritical() || this.isFailed();
        let auditLevel = 'basic';
        if (this.isSecurityCritical())
            auditLevel = 'comprehensive';
        else if (this.isContainmentAction() || this.isRecoveryAction())
            auditLevel = 'detailed';
        let retentionPeriod = 'standard';
        if (this.isSecurityCritical())
            retentionPeriod = 'permanent';
        else if (this.isFailed())
            retentionPeriod = 'extended';
        return {
            requiresDocumentation,
            auditLevel,
            retentionPeriod,
        };
    }
    /**
     * Get security implications
     */
    getSecurityImplications() {
        const implications = [];
        if (this.isSuccessful()) {
            if (this.isContainmentAction()) {
                implications.push('Threat containment achieved');
                implications.push('Risk exposure reduced');
            }
            if (this.isRecoveryAction()) {
                implications.push('System recovery completed');
                implications.push('Service availability restored');
            }
            if (this.actionType === action_type_enum_1.ActionType.PATCH_VULNERABILITY) {
                implications.push('Vulnerability remediated');
                implications.push('Attack surface reduced');
            }
        }
        else {
            if (this.isContainmentAction()) {
                implications.push('Containment failure - threat may spread');
                implications.push('Increased risk exposure');
            }
            if (this.isRecoveryAction()) {
                implications.push('Recovery failure - service disruption continues');
                implications.push('Business impact ongoing');
            }
            if (this.isSecurityCritical()) {
                implications.push('Critical security action failed');
                implications.push('Manual intervention required');
            }
        }
        return implications;
    }
    /**
     * Convert to integration event format
     */
    toIntegrationEvent() {
        return {
            eventType: 'ResponseActionExecuted',
            action: 'response_action_executed',
            resource: 'ResponseAction',
            resourceId: this.aggregateId.toString(),
            data: this.eventData,
            metadata: {
                isSuccessful: this.isSuccessful(),
                performanceCategory: this.getPerformanceCategory(),
                impactLevel: this.getImpactLevel(),
                isSecurityCritical: this.isSecurityCritical(),
                isAutomated: this.isAutomatedExecution(),
                executionMetrics: this.getExecutionMetrics(),
            },
        };
    }
}
exports.ResponseActionExecutedDomainEvent = ResponseActionExecutedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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