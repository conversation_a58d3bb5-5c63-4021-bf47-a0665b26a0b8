e05f4d28582679757aa11b444ad769a2
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const action_status_enum_1 = require("../action-status.enum");
describe('ActionStatus', () => {
    describe('enum values', () => {
        it('should have all expected status values', () => {
            expect(action_status_enum_1.ActionStatus.PENDING).toBe('pending');
            expect(action_status_enum_1.ActionStatus.APPROVED).toBe('approved');
            expect(action_status_enum_1.ActionStatus.QUEUED).toBe('queued');
            expect(action_status_enum_1.ActionStatus.EXECUTING).toBe('executing');
            expect(action_status_enum_1.ActionStatus.COMPLETED).toBe('completed');
            expect(action_status_enum_1.ActionStatus.FAILED).toBe('failed');
            expect(action_status_enum_1.ActionStatus.CANCELLED).toBe('cancelled');
            expect(action_status_enum_1.ActionStatus.TIMEOUT).toBe('timeout');
        });
    });
    describe('ActionStatusUtils', () => {
        describe('getAllStatuses', () => {
            it('should return all status values', () => {
                const statuses = action_status_enum_1.ActionStatusUtils.getAllStatuses();
                expect(statuses.length).toBeGreaterThan(10);
                expect(statuses).toContain(action_status_enum_1.ActionStatus.PENDING);
                expect(statuses).toContain(action_status_enum_1.ActionStatus.COMPLETED);
                expect(statuses).toContain(action_status_enum_1.ActionStatus.FAILED);
            });
        });
        describe('getActiveStatuses', () => {
            it('should return non-terminal statuses', () => {
                const activeStatuses = action_status_enum_1.ActionStatusUtils.getActiveStatuses();
                expect(activeStatuses).toContain(action_status_enum_1.ActionStatus.PENDING);
                expect(activeStatuses).toContain(action_status_enum_1.ActionStatus.EXECUTING);
                expect(activeStatuses).toContain(action_status_enum_1.ActionStatus.QUEUED);
                expect(activeStatuses).not.toContain(action_status_enum_1.ActionStatus.COMPLETED);
                expect(activeStatuses).not.toContain(action_status_enum_1.ActionStatus.FAILED);
            });
        });
        describe('getTerminalStatuses', () => {
            it('should return terminal statuses', () => {
                const terminalStatuses = action_status_enum_1.ActionStatusUtils.getTerminalStatuses();
                expect(terminalStatuses).toContain(action_status_enum_1.ActionStatus.COMPLETED);
                expect(terminalStatuses).toContain(action_status_enum_1.ActionStatus.FAILED);
                expect(terminalStatuses).toContain(action_status_enum_1.ActionStatus.CANCELLED);
                expect(terminalStatuses).not.toContain(action_status_enum_1.ActionStatus.PENDING);
                expect(terminalStatuses).not.toContain(action_status_enum_1.ActionStatus.EXECUTING);
            });
        });
        describe('getSuccessStatuses', () => {
            it('should return success statuses', () => {
                const successStatuses = action_status_enum_1.ActionStatusUtils.getSuccessStatuses();
                expect(successStatuses).toContain(action_status_enum_1.ActionStatus.COMPLETED);
                expect(successStatuses).toContain(action_status_enum_1.ActionStatus.PARTIAL);
                expect(successStatuses).not.toContain(action_status_enum_1.ActionStatus.FAILED);
                expect(successStatuses).not.toContain(action_status_enum_1.ActionStatus.CANCELLED);
            });
        });
        describe('getFailureStatuses', () => {
            it('should return failure statuses', () => {
                const failureStatuses = action_status_enum_1.ActionStatusUtils.getFailureStatuses();
                expect(failureStatuses).toContain(action_status_enum_1.ActionStatus.FAILED);
                expect(failureStatuses).toContain(action_status_enum_1.ActionStatus.TIMEOUT);
                expect(failureStatuses).toContain(action_status_enum_1.ActionStatus.CANCELLED);
                expect(failureStatuses).not.toContain(action_status_enum_1.ActionStatus.COMPLETED);
            });
        });
        describe('isTerminal', () => {
            it('should correctly identify terminal statuses', () => {
                expect(action_status_enum_1.ActionStatusUtils.isTerminal(action_status_enum_1.ActionStatus.COMPLETED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isTerminal(action_status_enum_1.ActionStatus.FAILED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isTerminal(action_status_enum_1.ActionStatus.CANCELLED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isTerminal(action_status_enum_1.ActionStatus.PENDING)).toBe(false);
                expect(action_status_enum_1.ActionStatusUtils.isTerminal(action_status_enum_1.ActionStatus.EXECUTING)).toBe(false);
            });
        });
        describe('isActive', () => {
            it('should correctly identify active statuses', () => {
                expect(action_status_enum_1.ActionStatusUtils.isActive(action_status_enum_1.ActionStatus.PENDING)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isActive(action_status_enum_1.ActionStatus.EXECUTING)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isActive(action_status_enum_1.ActionStatus.QUEUED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isActive(action_status_enum_1.ActionStatus.COMPLETED)).toBe(false);
                expect(action_status_enum_1.ActionStatusUtils.isActive(action_status_enum_1.ActionStatus.FAILED)).toBe(false);
            });
        });
        describe('isSuccess', () => {
            it('should correctly identify success statuses', () => {
                expect(action_status_enum_1.ActionStatusUtils.isSuccess(action_status_enum_1.ActionStatus.COMPLETED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isSuccess(action_status_enum_1.ActionStatus.PARTIAL)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isSuccess(action_status_enum_1.ActionStatus.FAILED)).toBe(false);
                expect(action_status_enum_1.ActionStatusUtils.isSuccess(action_status_enum_1.ActionStatus.CANCELLED)).toBe(false);
            });
        });
        describe('isFailure', () => {
            it('should correctly identify failure statuses', () => {
                expect(action_status_enum_1.ActionStatusUtils.isFailure(action_status_enum_1.ActionStatus.FAILED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isFailure(action_status_enum_1.ActionStatus.TIMEOUT)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isFailure(action_status_enum_1.ActionStatus.CANCELLED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isFailure(action_status_enum_1.ActionStatus.COMPLETED)).toBe(false);
                expect(action_status_enum_1.ActionStatusUtils.isFailure(action_status_enum_1.ActionStatus.PENDING)).toBe(false);
            });
        });
        describe('requiresAttention', () => {
            it('should identify statuses requiring attention', () => {
                expect(action_status_enum_1.ActionStatusUtils.requiresAttention(action_status_enum_1.ActionStatus.FAILED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.requiresAttention(action_status_enum_1.ActionStatus.MANUAL_INTERVENTION)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.requiresAttention(action_status_enum_1.ActionStatus.TIMEOUT)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.requiresAttention(action_status_enum_1.ActionStatus.COMPLETED)).toBe(false);
                expect(action_status_enum_1.ActionStatusUtils.requiresAttention(action_status_enum_1.ActionStatus.PENDING)).toBe(false);
            });
        });
        describe('canRetry', () => {
            it('should identify retriable statuses', () => {
                expect(action_status_enum_1.ActionStatusUtils.canRetry(action_status_enum_1.ActionStatus.FAILED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.canRetry(action_status_enum_1.ActionStatus.TIMEOUT)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.canRetry(action_status_enum_1.ActionStatus.PARTIAL)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.canRetry(action_status_enum_1.ActionStatus.COMPLETED)).toBe(false);
                expect(action_status_enum_1.ActionStatusUtils.canRetry(action_status_enum_1.ActionStatus.CANCELLED)).toBe(false);
            });
        });
        describe('canCancel', () => {
            it('should identify cancellable statuses', () => {
                expect(action_status_enum_1.ActionStatusUtils.canCancel(action_status_enum_1.ActionStatus.PENDING)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.canCancel(action_status_enum_1.ActionStatus.QUEUED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.canCancel(action_status_enum_1.ActionStatus.EXECUTING)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.canCancel(action_status_enum_1.ActionStatus.COMPLETED)).toBe(false);
                expect(action_status_enum_1.ActionStatusUtils.canCancel(action_status_enum_1.ActionStatus.FAILED)).toBe(false);
            });
        });
        describe('isExecuting', () => {
            it('should identify executing statuses', () => {
                expect(action_status_enum_1.ActionStatusUtils.isExecuting(action_status_enum_1.ActionStatus.EXECUTING)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isExecuting(action_status_enum_1.ActionStatus.RETRYING)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isExecuting(action_status_enum_1.ActionStatus.PENDING)).toBe(false);
                expect(action_status_enum_1.ActionStatusUtils.isExecuting(action_status_enum_1.ActionStatus.COMPLETED)).toBe(false);
            });
        });
        describe('isWaiting', () => {
            it('should identify waiting statuses', () => {
                expect(action_status_enum_1.ActionStatusUtils.isWaiting(action_status_enum_1.ActionStatus.PENDING)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isWaiting(action_status_enum_1.ActionStatus.QUEUED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isWaiting(action_status_enum_1.ActionStatus.SCHEDULED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isWaiting(action_status_enum_1.ActionStatus.EXECUTING)).toBe(false);
                expect(action_status_enum_1.ActionStatusUtils.isWaiting(action_status_enum_1.ActionStatus.COMPLETED)).toBe(false);
            });
        });
        describe('getNextStatus', () => {
            it('should return correct next status in flow', () => {
                expect(action_status_enum_1.ActionStatusUtils.getNextStatus(action_status_enum_1.ActionStatus.PENDING)).toBe(action_status_enum_1.ActionStatus.APPROVED);
                expect(action_status_enum_1.ActionStatusUtils.getNextStatus(action_status_enum_1.ActionStatus.APPROVED)).toBe(action_status_enum_1.ActionStatus.QUEUED);
                expect(action_status_enum_1.ActionStatusUtils.getNextStatus(action_status_enum_1.ActionStatus.QUEUED)).toBe(action_status_enum_1.ActionStatus.EXECUTING);
                expect(action_status_enum_1.ActionStatusUtils.getNextStatus(action_status_enum_1.ActionStatus.EXECUTING)).toBe(action_status_enum_1.ActionStatus.COMPLETED);
                expect(action_status_enum_1.ActionStatusUtils.getNextStatus(action_status_enum_1.ActionStatus.COMPLETED)).toBeNull();
            });
        });
        describe('getValidTransitions', () => {
            it('should return valid transitions for each status', () => {
                const pendingTransitions = action_status_enum_1.ActionStatusUtils.getValidTransitions(action_status_enum_1.ActionStatus.PENDING);
                expect(pendingTransitions).toContain(action_status_enum_1.ActionStatus.APPROVED);
                expect(pendingTransitions).toContain(action_status_enum_1.ActionStatus.REJECTED);
                expect(pendingTransitions).toContain(action_status_enum_1.ActionStatus.CANCELLED);
                const executingTransitions = action_status_enum_1.ActionStatusUtils.getValidTransitions(action_status_enum_1.ActionStatus.EXECUTING);
                expect(executingTransitions).toContain(action_status_enum_1.ActionStatus.COMPLETED);
                expect(executingTransitions).toContain(action_status_enum_1.ActionStatus.FAILED);
                expect(executingTransitions).toContain(action_status_enum_1.ActionStatus.PARTIAL);
                const completedTransitions = action_status_enum_1.ActionStatusUtils.getValidTransitions(action_status_enum_1.ActionStatus.COMPLETED);
                expect(completedTransitions).toHaveLength(0); // Terminal state
            });
        });
        describe('isValidTransition', () => {
            it('should validate status transitions', () => {
                expect(action_status_enum_1.ActionStatusUtils.isValidTransition(action_status_enum_1.ActionStatus.PENDING, action_status_enum_1.ActionStatus.APPROVED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isValidTransition(action_status_enum_1.ActionStatus.APPROVED, action_status_enum_1.ActionStatus.QUEUED)).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isValidTransition(action_status_enum_1.ActionStatus.EXECUTING, action_status_enum_1.ActionStatus.COMPLETED)).toBe(true);
                // Invalid transitions
                expect(action_status_enum_1.ActionStatusUtils.isValidTransition(action_status_enum_1.ActionStatus.COMPLETED, action_status_enum_1.ActionStatus.PENDING)).toBe(false);
                expect(action_status_enum_1.ActionStatusUtils.isValidTransition(action_status_enum_1.ActionStatus.PENDING, action_status_enum_1.ActionStatus.COMPLETED)).toBe(false);
            });
        });
        describe('getDescription', () => {
            it('should return meaningful descriptions', () => {
                const pendingDesc = action_status_enum_1.ActionStatusUtils.getDescription(action_status_enum_1.ActionStatus.PENDING);
                expect(pendingDesc).toContain('awaiting approval');
                const executingDesc = action_status_enum_1.ActionStatusUtils.getDescription(action_status_enum_1.ActionStatus.EXECUTING);
                expect(executingDesc).toContain('currently being executed');
                const completedDesc = action_status_enum_1.ActionStatusUtils.getDescription(action_status_enum_1.ActionStatus.COMPLETED);
                expect(completedDesc).toContain('completed successfully');
            });
        });
        describe('getPriority', () => {
            it('should return higher priority for more urgent statuses', () => {
                const failedPriority = action_status_enum_1.ActionStatusUtils.getPriority(action_status_enum_1.ActionStatus.FAILED);
                const completedPriority = action_status_enum_1.ActionStatusUtils.getPriority(action_status_enum_1.ActionStatus.COMPLETED);
                expect(failedPriority).toBeLessThan(completedPriority); // Lower number = higher priority
                const executingPriority = action_status_enum_1.ActionStatusUtils.getPriority(action_status_enum_1.ActionStatus.EXECUTING);
                const queuedPriority = action_status_enum_1.ActionStatusUtils.getPriority(action_status_enum_1.ActionStatus.QUEUED);
                expect(executingPriority).toBeLessThan(queuedPriority);
            });
        });
        describe('getColorCode', () => {
            it('should return valid color codes', () => {
                const colorCode = action_status_enum_1.ActionStatusUtils.getColorCode(action_status_enum_1.ActionStatus.COMPLETED);
                expect(colorCode).toMatch(/^#[0-9A-F]{6}$/i);
                const failedColor = action_status_enum_1.ActionStatusUtils.getColorCode(action_status_enum_1.ActionStatus.FAILED);
                expect(failedColor).toMatch(/^#[0-9A-F]{6}$/i);
            });
        });
        describe('getIconName', () => {
            it('should return appropriate icon names', () => {
                expect(action_status_enum_1.ActionStatusUtils.getIconName(action_status_enum_1.ActionStatus.COMPLETED)).toBe('check-circle');
                expect(action_status_enum_1.ActionStatusUtils.getIconName(action_status_enum_1.ActionStatus.EXECUTING)).toBe('play-circle');
                expect(action_status_enum_1.ActionStatusUtils.getIconName(action_status_enum_1.ActionStatus.FAILED)).toBe('x-circle');
                expect(action_status_enum_1.ActionStatusUtils.getIconName(action_status_enum_1.ActionStatus.PENDING)).toBe('clock');
            });
        });
        describe('getProgressPercentage', () => {
            it('should return appropriate progress percentages', () => {
                expect(action_status_enum_1.ActionStatusUtils.getProgressPercentage(action_status_enum_1.ActionStatus.PENDING)).toBe(0);
                expect(action_status_enum_1.ActionStatusUtils.getProgressPercentage(action_status_enum_1.ActionStatus.EXECUTING)).toBe(50);
                expect(action_status_enum_1.ActionStatusUtils.getProgressPercentage(action_status_enum_1.ActionStatus.COMPLETED)).toBe(100);
                expect(action_status_enum_1.ActionStatusUtils.getProgressPercentage(action_status_enum_1.ActionStatus.FAILED)).toBe(0);
            });
        });
        describe('getEstimatedCompletion', () => {
            it('should return estimated completion times', () => {
                const baseEstimate = 60; // 60 minutes
                expect(action_status_enum_1.ActionStatusUtils.getEstimatedCompletion(action_status_enum_1.ActionStatus.APPROVED, baseEstimate)).toBe(60);
                expect(action_status_enum_1.ActionStatusUtils.getEstimatedCompletion(action_status_enum_1.ActionStatus.EXECUTING, baseEstimate)).toBe(30);
                expect(action_status_enum_1.ActionStatusUtils.getEstimatedCompletion(action_status_enum_1.ActionStatus.RETRYING, baseEstimate)).toBe(90);
                expect(action_status_enum_1.ActionStatusUtils.getEstimatedCompletion(action_status_enum_1.ActionStatus.COMPLETED, baseEstimate)).toBe(0);
                expect(action_status_enum_1.ActionStatusUtils.getEstimatedCompletion(action_status_enum_1.ActionStatus.PENDING, baseEstimate)).toBeNull();
            });
        });
        describe('getRetryConfig', () => {
            it('should return retry configuration for retriable statuses', () => {
                const failedConfig = action_status_enum_1.ActionStatusUtils.getRetryConfig(action_status_enum_1.ActionStatus.FAILED);
                expect(failedConfig.canRetry).toBe(true);
                expect(failedConfig.maxRetries).toBeGreaterThan(0);
                expect(failedConfig.backoffMultiplier).toBeGreaterThan(1);
                const completedConfig = action_status_enum_1.ActionStatusUtils.getRetryConfig(action_status_enum_1.ActionStatus.COMPLETED);
                expect(completedConfig.canRetry).toBe(false);
                expect(completedConfig.maxRetries).toBe(0);
            });
        });
        describe('getNotificationRequirements', () => {
            it('should return notification requirements', () => {
                const failedNotification = action_status_enum_1.ActionStatusUtils.getNotificationRequirements(action_status_enum_1.ActionStatus.FAILED);
                expect(failedNotification.notify).toBe(true);
                expect(failedNotification.urgency).toBe('high');
                expect(failedNotification.channels).toContain('email');
                const completedNotification = action_status_enum_1.ActionStatusUtils.getNotificationRequirements(action_status_enum_1.ActionStatus.COMPLETED);
                expect(completedNotification.notify).toBe(true);
                expect(completedNotification.urgency).toBe('low');
            });
        });
        describe('isValid', () => {
            it('should validate status strings', () => {
                expect(action_status_enum_1.ActionStatusUtils.isValid('pending')).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isValid('completed')).toBe(true);
                expect(action_status_enum_1.ActionStatusUtils.isValid('invalid_status')).toBe(false);
                expect(action_status_enum_1.ActionStatusUtils.isValid('')).toBe(false);
            });
        });
        describe('fromString', () => {
            it('should convert string to status', () => {
                expect(action_status_enum_1.ActionStatusUtils.fromString('pending')).toBe(action_status_enum_1.ActionStatus.PENDING);
                expect(action_status_enum_1.ActionStatusUtils.fromString('COMPLETED')).toBe(action_status_enum_1.ActionStatus.COMPLETED);
                expect(action_status_enum_1.ActionStatusUtils.fromString('executing')).toBe(action_status_enum_1.ActionStatus.EXECUTING);
                expect(action_status_enum_1.ActionStatusUtils.fromString('invalid_status')).toBeNull();
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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