{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\types\\security.types.spec.ts", "mappings": ";AAAA;;GAEG;;AAEH,+DAOoC;AAEpC,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,MAAM,GAAG,8BAAa,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAEtD,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,OAAO,GAAG,8BAAa,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YACvD,MAAM,OAAO,GAAG,8BAAa,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAEvD,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,MAAM,GAAG,8BAAa,CAAC,oBAAoB,EAAE,CAAC;YAEpD,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,MAAM,GAAG,8BAAa,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAE/C,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,+BAA+B;YAChE,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,MAAM,GAAG,8BAAa,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAC/C,MAAM,MAAM,GAAG,8BAAa,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAE/C,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,CAAC,8BAAa,CAAC,yBAAyB,CAC5C,8BAAa,CAAC,YAAY,EAC1B,8BAAa,CAAC,YAAY,CAC3B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,CAAC,8BAAa,CAAC,yBAAyB,CAC5C,8BAAa,CAAC,QAAQ,EACtB,8BAAa,CAAC,YAAY,CAC3B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,CAAC,8BAAa,CAAC,yBAAyB,CAC5C,8BAAa,CAAC,YAAY,EAC1B,8BAAa,CAAC,QAAQ,CACvB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,8BAAa,CAAC,yBAAyB,CAC5C,8BAAa,CAAC,MAAM,EACpB,8BAAa,CAAC,MAAM,CACrB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEd,MAAM,CAAC,8BAAa,CAAC,yBAAyB,CAC5C,8BAAa,CAAC,QAAQ,EACtB,8BAAa,CAAC,MAAM,CACrB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,8BAAa,CAAC,yBAAyB,CAC5C,8BAAa,CAAC,UAAU,EACxB,8BAAa,CAAC,UAAU,CACzB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEd,MAAM,CAAC,8BAAa,CAAC,yBAAyB,CAC5C,8BAAa,CAAC,UAAU,EACxB,8BAAa,CAAC,UAAU,CACzB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,MAAM,GAAG,8BAAa,CAAC,wBAAwB,CAAC,mBAAmB,CAAC,CAAC;YAE3E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,MAAM,GAAG,8BAAa,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;YAE9D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,MAAM,GAAG,8BAAa,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;YAElE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,yCAAyC,CAAC,CAAC;YAC7E,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;YACnE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,0CAA0C,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,MAAM,GAAG,8BAAa,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;YAExE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;YACvE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,WAAW,GAAG,8BAAa,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;YAC1E,MAAM,UAAU,GAAG,8BAAa,CAAC,wBAAwB,CAAC,uBAAuB,CAAC,CAAC;YAEnF,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,KAAK,GAAG,+BAA+B,CAAC;YAC9C,MAAM,MAAM,GAAG,8BAAa,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAElD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,KAAK,GAAG,kCAAkC,CAAC;YACjD,MAAM,MAAM,GAAG,8BAAa,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAElD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,MAAM,GAAG,8BAAa,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAE/C,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,CAAC,8BAAa,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,CAAC,8BAAa,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,8BAAa,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,8BAAa,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChE,MAAM,CAAC,8BAAa,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChE,MAAM,CAAC,8BAAa,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,CAAC,8BAAa,CAAC,gBAAgB,CAAC,yCAAyC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,CAAC,8BAAa,CAAC,gBAAgB,CAAC,IAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChE,MAAM,CAAC,8BAAa,CAAC,gBAAgB,CAAC,GAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,8BAAa,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,MAAM,CAAC,8BAAa,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,CAAC,8BAAa,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClE,MAAM,CAAC,8BAAa,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,8BAAa,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChE,MAAM,CAAC,8BAAa,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,8BAAa,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,KAAK,GAAG,8BAAa,CAAC,iBAAiB,EAAE,CAAC;YAEhD,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,+BAA+B;YAC/D,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,MAAM,GAAG,8BAAa,CAAC,iBAAiB,EAAE,CAAC;YACjD,MAAM,MAAM,GAAG,8BAAa,CAAC,iBAAiB,EAAE,CAAC;YAEjD,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,KAAK,GAAG,cAAc,CAAC;YAC7B,MAAM,MAAM,GAAG,8BAAa,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAE7D,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,MAAM,GAAG,8BAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEnE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,CAAC,8BAAa,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjE,MAAM,CAAC,8BAAa,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjE,MAAM,CAAC,8BAAa,CAAC,iBAAiB,CAAC,IAAW,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,MAAM,GAAG,8BAAa,CAAC,iBAAiB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,OAAO,GAAG,8BAAa,CAAC,qBAAqB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YAE7E,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,OAAO,GAAG;gBACd,UAAU,EAAE,qCAAoB,CAAC,GAAG;gBACpC,KAAK,EAAE,CAAC,OAAO,CAAC;gBAChB,WAAW,EAAE,CAAC,+BAAc,CAAC,IAAI,EAAE,+BAAc,CAAC,KAAK,CAAC;gBACxD,cAAc,EAAE,8BAAa,CAAC,YAAY;gBAC1C,SAAS,EAAE,aAAa;aACzB,CAAC;YAEF,MAAM,OAAO,GAAG,8BAAa,CAAC,qBAAqB,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;YAEtF,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,qCAAoB,CAAC,GAAG,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,+BAAc,CAAC,IAAI,EAAE,+BAAc,CAAC,KAAK,CAAC,CAAC,CAAC;YACjF,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,8BAAa,CAAC,YAAY,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAoB;gBAC/B,MAAM,EAAE,SAAS;gBACjB,WAAW,EAAE,CAAC,+BAAc,CAAC,KAAK,CAAC;gBACnC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,aAAa,CAAC,OAAO,EAAE,+BAAc,CAAC,MAAM,CAAC,CAAC;YAE3E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAoB;gBAC/B,MAAM,EAAE,SAAS;gBACjB,WAAW,EAAE,CAAC,+BAAc,CAAC,KAAK,CAAC;gBACnC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC;YACpD,MAAM,MAAM,GAAG,8BAAa,CAAC,aAAa,CAAC,OAAO,EAAE,+BAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAErF,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,OAAO,GAAoB;gBAC/B,MAAM,EAAE,SAAS;gBACjB,WAAW,EAAE,CAAC,+BAAc,CAAC,IAAI,EAAE,+BAAc,CAAC,KAAK,CAAC;gBACxD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,CAAC,8BAAa,CAAC,aAAa,CAAC,OAAO,EAAE,+BAAc,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7E,MAAM,CAAC,8BAAa,CAAC,aAAa,CAAC,OAAO,EAAE,+BAAc,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9E,MAAM,CAAC,8BAAa,CAAC,aAAa,CAAC,OAAO,EAAE,+BAAc,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,OAAO,GAAoB;gBAC/B,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,aAAa,CAAC,OAAO,EAAE,+BAAc,CAAC,IAAI,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,MAAM,OAAO,GAAoB;YAC/B,MAAM,EAAE,SAAS;YACjB,KAAK,EAAE,CAAC,MAAM,CAAC;YACf,SAAS,EAAE,aAAa;YACxB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,MAAM,GAAG,8BAAa,CAAC,wBAAwB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;YAEnE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,UAAU,GAAsB;gBACpC;oBACE,IAAI,EAAE,WAAW;oBACjB,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,SAAS;iBACjB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAE3E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,UAAU,GAAsB;gBACpC;oBACE,IAAI,EAAE,WAAW;oBACjB,QAAQ,EAAE,IAAI;oBACd,KAAK,EAAE,OAAO;oBACd,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;iBACzB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAE3E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,UAAU,GAAsB;gBACpC;oBACE,IAAI,EAAE,WAAW;oBACjB,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,YAAY;oBACnB,MAAM,EAAE,IAAI;iBACb;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAE3E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,UAAU,GAAsB;gBACpC;oBACE,IAAI,EAAE,WAAW;oBACjB,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,YAAY;iBACpB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAE3E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,UAAU,GAAsB;gBACpC;oBACE,IAAI,EAAE,WAAW;oBACjB,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,SAAS;iBACjB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,YAAY;iBACpB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAE3E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,OAAO,GAAoB;gBAC/B,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,KAAK,GAAG,8BAAa,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAE5D,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,OAAO,GAAoB;gBAC/B,MAAM,EAAE,SAAS;gBACjB,UAAU,EAAE,qCAAoB,CAAC,GAAG;gBACpC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,KAAK,GAAG,8BAAa,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAE5D,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,OAAO,GAAoB;gBAC/B,MAAM,EAAE,SAAS;gBACjB,KAAK,EAAE,CAAC,OAAO,CAAC;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,KAAK,GAAG,8BAAa,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAE5D,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,uBAAuB;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,OAAO,GAAoB;gBAC/B,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,aAAa;gBACxB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,KAAK,GAAG,8BAAa,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAE5D,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,0BAA0B;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,OAAO,GAAoB;gBAC/B,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,SAAS;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,KAAK,GAAG,8BAAa,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAE5D,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,2BAA2B;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,OAAO,GAAoB;gBAC/B,MAAM,EAAE,SAAS;gBACjB,UAAU,EAAE,qCAAoB,CAAC,GAAG;gBACpC,SAAS,EAAE,aAAa;gBACxB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,KAAK,GAAG,8BAAa,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAE5D,MAAM,CAAC,KAAK,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,MAAM,OAAO,GAAoB;gBAC/B,MAAM,EAAE,SAAS;gBACjB,KAAK,EAAE,CAAC,OAAO,CAAC;gBAChB,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE;oBACV,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW;iBAC7C;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,KAAK,GAAG,8BAAa,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAE5D,MAAM,CAAC,KAAK,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\types\\security.types.spec.ts"], "sourcesContent": ["/**\r\n * Security Types Tests\r\n */\r\n\r\nimport {\r\n  SecurityLevel,\r\n  SecurityUtils,\r\n  SecurityContext,\r\n  PermissionType,\r\n  AccessCondition,\r\n  AuthenticationMethod,\r\n} from '../../types/security.types';\r\n\r\ndescribe('SecurityUtils', () => {\r\n  describe('generateSecureRandom', () => {\r\n    it('should generate random string of specified length', () => {\r\n      const result = SecurityUtils.generateSecureRandom(16);\r\n      \r\n      expect(result).toHaveLength(16);\r\n      expect(typeof result).toBe('string');\r\n    });\r\n\r\n    it('should generate different strings on multiple calls', () => {\r\n      const result1 = SecurityUtils.generateSecureRandom(32);\r\n      const result2 = SecurityUtils.generateSecureRandom(32);\r\n      \r\n      expect(result1).not.toBe(result2);\r\n    });\r\n\r\n    it('should use default length when not specified', () => {\r\n      const result = SecurityUtils.generateSecureRandom();\r\n      \r\n      expect(result).toHaveLength(32);\r\n    });\r\n  });\r\n\r\n  describe('generateToken', () => {\r\n    it('should generate hex token of specified length', () => {\r\n      const result = SecurityUtils.generateToken(16);\r\n      \r\n      expect(result).toHaveLength(32); // 16 bytes = 32 hex characters\r\n      expect(/^[0-9a-f]+$/.test(result)).toBe(true);\r\n    });\r\n\r\n    it('should generate different tokens on multiple calls', () => {\r\n      const token1 = SecurityUtils.generateToken(16);\r\n      const token2 = SecurityUtils.generateToken(16);\r\n      \r\n      expect(token1).not.toBe(token2);\r\n    });\r\n  });\r\n\r\n  describe('isSecurityLevelSufficient', () => {\r\n    it('should return true when actual level meets required level', () => {\r\n      expect(SecurityUtils.isSecurityLevelSufficient(\r\n        SecurityLevel.CONFIDENTIAL,\r\n        SecurityLevel.CONFIDENTIAL\r\n      )).toBe(true);\r\n    });\r\n\r\n    it('should return true when actual level exceeds required level', () => {\r\n      expect(SecurityUtils.isSecurityLevelSufficient(\r\n        SecurityLevel.INTERNAL,\r\n        SecurityLevel.CONFIDENTIAL\r\n      )).toBe(true);\r\n    });\r\n\r\n    it('should return false when actual level is below required level', () => {\r\n      expect(SecurityUtils.isSecurityLevelSufficient(\r\n        SecurityLevel.CONFIDENTIAL,\r\n        SecurityLevel.INTERNAL\r\n      )).toBe(false);\r\n    });\r\n\r\n    it('should handle public level correctly', () => {\r\n      expect(SecurityUtils.isSecurityLevelSufficient(\r\n        SecurityLevel.PUBLIC,\r\n        SecurityLevel.PUBLIC\r\n      )).toBe(true);\r\n\r\n      expect(SecurityUtils.isSecurityLevelSufficient(\r\n        SecurityLevel.INTERNAL,\r\n        SecurityLevel.PUBLIC\r\n      )).toBe(false);\r\n    });\r\n\r\n    it('should handle top secret level correctly', () => {\r\n      expect(SecurityUtils.isSecurityLevelSufficient(\r\n        SecurityLevel.TOP_SECRET,\r\n        SecurityLevel.TOP_SECRET\r\n      )).toBe(true);\r\n\r\n      expect(SecurityUtils.isSecurityLevelSufficient(\r\n        SecurityLevel.RESTRICTED,\r\n        SecurityLevel.TOP_SECRET\r\n      )).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('validatePasswordStrength', () => {\r\n    it('should validate strong password', () => {\r\n      const result = SecurityUtils.validatePasswordStrength('StrongP@ssw0rd123');\r\n      \r\n      expect(result.isValid).toBe(true);\r\n      expect(result.score).toBeGreaterThan(4);\r\n      expect(result.feedback).toHaveLength(0);\r\n    });\r\n\r\n    it('should reject weak password', () => {\r\n      const result = SecurityUtils.validatePasswordStrength('weak');\r\n      \r\n      expect(result.isValid).toBe(false);\r\n      expect(result.score).toBeLessThan(4);\r\n      expect(result.feedback.length).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should provide feedback for missing requirements', () => {\r\n      const result = SecurityUtils.validatePasswordStrength('password');\r\n      \r\n      expect(result.feedback).toContain('Password must contain uppercase letters');\r\n      expect(result.feedback).toContain('Password must contain numbers');\r\n      expect(result.feedback).toContain('Password must contain special characters');\r\n    });\r\n\r\n    it('should detect common patterns', () => {\r\n      const result = SecurityUtils.validatePasswordStrength('Password123456');\r\n      \r\n      expect(result.feedback).toContain('Password contains common patterns');\r\n      expect(result.score).toBeLessThan(4);\r\n    });\r\n\r\n    it('should give bonus for longer passwords', () => {\r\n      const shortResult = SecurityUtils.validatePasswordStrength('StrongP@ss1');\r\n      const longResult = SecurityUtils.validatePasswordStrength('VeryStrongP@ssw0rd123');\r\n      \r\n      expect(longResult.score).toBeGreaterThan(shortResult.score);\r\n    });\r\n  });\r\n\r\n  describe('sanitizeInput', () => {\r\n    it('should sanitize HTML characters', () => {\r\n      const input = '<script>alert(\"xss\")</script>';\r\n      const result = SecurityUtils.sanitizeInput(input);\r\n      \r\n      expect(result).toBe('&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;');\r\n    });\r\n\r\n    it('should sanitize special characters', () => {\r\n      const input = 'Test & \"quotes\" <tags> /slashes/';\r\n      const result = SecurityUtils.sanitizeInput(input);\r\n      \r\n      expect(result).toBe('Test &amp; &quot;quotes&quot; &lt;tags&gt; &#x2F;slashes&#x2F;');\r\n    });\r\n\r\n    it('should handle empty string', () => {\r\n      const result = SecurityUtils.sanitizeInput('');\r\n      \r\n      expect(result).toBe('');\r\n    });\r\n  });\r\n\r\n  describe('isValidIpAddress', () => {\r\n    it('should validate IPv4 addresses', () => {\r\n      expect(SecurityUtils.isValidIpAddress('***********')).toBe(true);\r\n      expect(SecurityUtils.isValidIpAddress('********')).toBe(true);\r\n      expect(SecurityUtils.isValidIpAddress('***************')).toBe(true);\r\n    });\r\n\r\n    it('should reject invalid IPv4 addresses', () => {\r\n      expect(SecurityUtils.isValidIpAddress('256.1.1.1')).toBe(false);\r\n      expect(SecurityUtils.isValidIpAddress('192.168.1')).toBe(false);\r\n      expect(SecurityUtils.isValidIpAddress('***********.1')).toBe(false);\r\n    });\r\n\r\n    it('should validate IPv6 addresses', () => {\r\n      expect(SecurityUtils.isValidIpAddress('2001:0db8:85a3:0000:0000:8a2e:0370:7334')).toBe(true);\r\n    });\r\n\r\n    it('should reject non-string input', () => {\r\n      expect(SecurityUtils.isValidIpAddress(null as any)).toBe(false);\r\n      expect(SecurityUtils.isValidIpAddress(123 as any)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('isPrivateIpAddress', () => {\r\n    it('should identify private IPv4 addresses', () => {\r\n      expect(SecurityUtils.isPrivateIpAddress('***********')).toBe(true);\r\n      expect(SecurityUtils.isPrivateIpAddress('********')).toBe(true);\r\n      expect(SecurityUtils.isPrivateIpAddress('**********')).toBe(true);\r\n      expect(SecurityUtils.isPrivateIpAddress('127.0.0.1')).toBe(true);\r\n    });\r\n\r\n    it('should identify public IPv4 addresses', () => {\r\n      expect(SecurityUtils.isPrivateIpAddress('*******')).toBe(false);\r\n      expect(SecurityUtils.isPrivateIpAddress('*******')).toBe(false);\r\n    });\r\n\r\n    it('should return false for invalid IP addresses', () => {\r\n      expect(SecurityUtils.isPrivateIpAddress('invalid')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('generateCsrfToken', () => {\r\n    it('should generate CSRF token', () => {\r\n      const token = SecurityUtils.generateCsrfToken();\r\n      \r\n      expect(token).toHaveLength(64); // 32 bytes = 64 hex characters\r\n      expect(/^[0-9a-f]+$/.test(token)).toBe(true);\r\n    });\r\n\r\n    it('should generate different tokens', () => {\r\n      const token1 = SecurityUtils.generateCsrfToken();\r\n      const token2 = SecurityUtils.generateCsrfToken();\r\n      \r\n      expect(token1).not.toBe(token2);\r\n    });\r\n  });\r\n\r\n  describe('validateCsrfToken', () => {\r\n    it('should validate matching tokens', () => {\r\n      const token = 'abc123def456';\r\n      const result = SecurityUtils.validateCsrfToken(token, token);\r\n      \r\n      expect(result).toBe(true);\r\n    });\r\n\r\n    it('should reject non-matching tokens', () => {\r\n      const result = SecurityUtils.validateCsrfToken('token1', 'token2');\r\n      \r\n      expect(result).toBe(false);\r\n    });\r\n\r\n    it('should reject empty tokens', () => {\r\n      expect(SecurityUtils.validateCsrfToken('', 'token')).toBe(false);\r\n      expect(SecurityUtils.validateCsrfToken('token', '')).toBe(false);\r\n      expect(SecurityUtils.validateCsrfToken(null as any, 'token')).toBe(false);\r\n    });\r\n\r\n    it('should reject tokens of different lengths', () => {\r\n      const result = SecurityUtils.validateCsrfToken('short', 'verylongtoken');\r\n      \r\n      expect(result).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('createSecurityContext', () => {\r\n    it('should create basic security context', () => {\r\n      const context = SecurityUtils.createSecurityContext('user123', 'session456');\r\n      \r\n      expect(context.userId).toBe('user123');\r\n      expect(context.sessionId).toBe('session456');\r\n      expect(context.timestamp).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should create security context with options', () => {\r\n      const options = {\r\n        authMethod: AuthenticationMethod.MFA,\r\n        roles: ['admin'],\r\n        permissions: [PermissionType.READ, PermissionType.WRITE],\r\n        clearanceLevel: SecurityLevel.CONFIDENTIAL,\r\n        ipAddress: '***********',\r\n      };\r\n\r\n      const context = SecurityUtils.createSecurityContext('user123', 'session456', options);\r\n      \r\n      expect(context.authMethod).toBe(AuthenticationMethod.MFA);\r\n      expect(context.roles).toEqual(['admin']);\r\n      expect(context.permissions).toEqual([PermissionType.READ, PermissionType.WRITE]);\r\n      expect(context.clearanceLevel).toBe(SecurityLevel.CONFIDENTIAL);\r\n      expect(context.ipAddress).toBe('***********');\r\n    });\r\n  });\r\n\r\n  describe('hasPermission', () => {\r\n    it('should grant access for admin permission', () => {\r\n      const context: SecurityContext = {\r\n        userId: 'user123',\r\n        permissions: [PermissionType.ADMIN],\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      const result = SecurityUtils.hasPermission(context, PermissionType.DELETE);\r\n      \r\n      expect(result).toBe(true);\r\n    });\r\n\r\n    it('should grant access for owner permission', () => {\r\n      const context: SecurityContext = {\r\n        userId: 'user123',\r\n        permissions: [PermissionType.OWNER],\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      const resource = { type: 'Document', id: 'doc123' };\r\n      const result = SecurityUtils.hasPermission(context, PermissionType.DELETE, resource);\r\n      \r\n      expect(result).toBe(true);\r\n    });\r\n\r\n    it('should grant access for specific permission', () => {\r\n      const context: SecurityContext = {\r\n        userId: 'user123',\r\n        permissions: [PermissionType.READ, PermissionType.WRITE],\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      expect(SecurityUtils.hasPermission(context, PermissionType.READ)).toBe(true);\r\n      expect(SecurityUtils.hasPermission(context, PermissionType.WRITE)).toBe(true);\r\n      expect(SecurityUtils.hasPermission(context, PermissionType.DELETE)).toBe(false);\r\n    });\r\n\r\n    it('should deny access when no permissions', () => {\r\n      const context: SecurityContext = {\r\n        userId: 'user123',\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      const result = SecurityUtils.hasPermission(context, PermissionType.READ);\r\n      \r\n      expect(result).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('evaluateAccessConditions', () => {\r\n    const context: SecurityContext = {\r\n      userId: 'user123',\r\n      roles: ['user'],\r\n      ipAddress: '***********',\r\n      timestamp: new Date(),\r\n    };\r\n\r\n    it('should return true for empty conditions', () => {\r\n      const result = SecurityUtils.evaluateAccessConditions([], context);\r\n      \r\n      expect(result).toBe(true);\r\n    });\r\n\r\n    it('should evaluate equals condition', () => {\r\n      const conditions: AccessCondition[] = [\r\n        {\r\n          type: 'attribute',\r\n          operator: 'equals',\r\n          field: 'userId',\r\n          value: 'user123',\r\n        },\r\n      ];\r\n\r\n      const result = SecurityUtils.evaluateAccessConditions(conditions, context);\r\n      \r\n      expect(result).toBe(true);\r\n    });\r\n\r\n    it('should evaluate in condition', () => {\r\n      const conditions: AccessCondition[] = [\r\n        {\r\n          type: 'attribute',\r\n          operator: 'in',\r\n          field: 'roles',\r\n          value: ['user', 'admin'],\r\n        },\r\n      ];\r\n\r\n      const result = SecurityUtils.evaluateAccessConditions(conditions, context);\r\n      \r\n      expect(result).toBe(true);\r\n    });\r\n\r\n    it('should evaluate negated condition', () => {\r\n      const conditions: AccessCondition[] = [\r\n        {\r\n          type: 'attribute',\r\n          operator: 'equals',\r\n          field: 'userId',\r\n          value: 'other-user',\r\n          negate: true,\r\n        },\r\n      ];\r\n\r\n      const result = SecurityUtils.evaluateAccessConditions(conditions, context);\r\n      \r\n      expect(result).toBe(true);\r\n    });\r\n\r\n    it('should return false when condition fails', () => {\r\n      const conditions: AccessCondition[] = [\r\n        {\r\n          type: 'attribute',\r\n          operator: 'equals',\r\n          field: 'userId',\r\n          value: 'other-user',\r\n        },\r\n      ];\r\n\r\n      const result = SecurityUtils.evaluateAccessConditions(conditions, context);\r\n      \r\n      expect(result).toBe(false);\r\n    });\r\n\r\n    it('should require all conditions to pass', () => {\r\n      const conditions: AccessCondition[] = [\r\n        {\r\n          type: 'attribute',\r\n          operator: 'equals',\r\n          field: 'userId',\r\n          value: 'user123',\r\n        },\r\n        {\r\n          type: 'attribute',\r\n          operator: 'equals',\r\n          field: 'userId',\r\n          value: 'other-user',\r\n        },\r\n      ];\r\n\r\n      const result = SecurityUtils.evaluateAccessConditions(conditions, context);\r\n      \r\n      expect(result).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('calculateSecurityScore', () => {\r\n    it('should calculate base score', () => {\r\n      const context: SecurityContext = {\r\n        userId: 'user123',\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      const score = SecurityUtils.calculateSecurityScore(context);\r\n      \r\n      expect(score).toBe(50); // Base score\r\n    });\r\n\r\n    it('should increase score for MFA', () => {\r\n      const context: SecurityContext = {\r\n        userId: 'user123',\r\n        authMethod: AuthenticationMethod.MFA,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      const score = SecurityUtils.calculateSecurityScore(context);\r\n      \r\n      expect(score).toBe(70); // Base + MFA bonus\r\n    });\r\n\r\n    it('should decrease score for admin role', () => {\r\n      const context: SecurityContext = {\r\n        userId: 'user123',\r\n        roles: ['admin'],\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      const score = SecurityUtils.calculateSecurityScore(context);\r\n      \r\n      expect(score).toBe(40); // Base - admin penalty\r\n    });\r\n\r\n    it('should increase score for private IP', () => {\r\n      const context: SecurityContext = {\r\n        userId: 'user123',\r\n        ipAddress: '***********',\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      const score = SecurityUtils.calculateSecurityScore(context);\r\n      \r\n      expect(score).toBe(60); // Base + private IP bonus\r\n    });\r\n\r\n    it('should decrease score for public IP', () => {\r\n      const context: SecurityContext = {\r\n        userId: 'user123',\r\n        ipAddress: '*******',\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      const score = SecurityUtils.calculateSecurityScore(context);\r\n      \r\n      expect(score).toBe(45); // Base - public IP penalty\r\n    });\r\n\r\n    it('should cap score at 100', () => {\r\n      const context: SecurityContext = {\r\n        userId: 'user123',\r\n        authMethod: AuthenticationMethod.MFA,\r\n        ipAddress: '***********',\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      const score = SecurityUtils.calculateSecurityScore(context);\r\n      \r\n      expect(score).toBeLessThanOrEqual(100);\r\n    });\r\n\r\n    it('should not go below 0', () => {\r\n      const context: SecurityContext = {\r\n        userId: 'user123',\r\n        roles: ['admin'],\r\n        ipAddress: '*******',\r\n        attributes: {\r\n          sessionAge: 25 * 60 * 60 * 1000, // 25 hours\r\n        },\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      const score = SecurityUtils.calculateSecurityScore(context);\r\n      \r\n      expect(score).toBeGreaterThanOrEqual(0);\r\n    });\r\n  });\r\n});"], "version": 3}