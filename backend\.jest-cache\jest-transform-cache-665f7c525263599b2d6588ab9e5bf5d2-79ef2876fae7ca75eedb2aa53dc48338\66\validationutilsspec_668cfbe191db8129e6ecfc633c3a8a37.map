{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\utils\\validation.utils.spec.ts", "mappings": ";AAAA;;GAEG;;AAEH,mEAMsC;AAEtC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,KAAK,GAAqB;gBAC9B,kCAAe,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAChC,kCAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;aACnC,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,kCAAe,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,KAAK,GAAqB;gBAC9B,kCAAe,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAChC,kCAAe,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;aACpC,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,kCAAe,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,KAAK,GAAqB;gBAC9B,kCAAe,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAChC,kCAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;aACnC,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,kCAAe,CAAC,aAAa,CAAC,EAAE,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;YAE3E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,MAAM,GAAqB;gBAC/B,MAAM,EAAE;oBACN;wBACE,KAAK,EAAE,MAAM;wBACb,QAAQ,EAAE,IAAI;wBACd,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,CAAC,kCAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;qBAC5C;oBACD;wBACE,KAAK,EAAE,KAAK;wBACZ,QAAQ,EAAE,IAAI;wBACd,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,CAAC,kCAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;qBACtC;iBACF;aACF,CAAC;YAEF,MAAM,GAAG,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;YACtC,MAAM,MAAM,GAAG,MAAM,kCAAe,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,MAAM,GAAqB;gBAC/B,MAAM,EAAE;oBACN;wBACE,KAAK,EAAE,MAAM;wBACb,QAAQ,EAAE,IAAI;wBACd,IAAI,EAAE,QAAQ;qBACf;iBACF;aACF,CAAC;YAEF,MAAM,GAAG,GAAG,EAAE,CAAC;YACf,MAAM,MAAM,GAAG,MAAM,kCAAe,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,MAAM,GAAqB;gBAC/B,MAAM,EAAE;oBACN;wBACE,KAAK,EAAE,KAAK;wBACZ,QAAQ,EAAE,IAAI;wBACd,IAAI,EAAE,QAAQ;qBACf;iBACF;aACF,CAAC;YAEF,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,cAAc,EAAE,CAAC;YACpC,MAAM,MAAM,GAAG,MAAM,kCAAe,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,MAAM,GAAqB;gBAC/B,MAAM,EAAE;oBACN;wBACE,KAAK,EAAE,QAAQ;wBACf,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE,QAAQ;wBACd,YAAY,EAAE,QAAQ;qBACvB;iBACF;aACF,CAAC;YAEF,MAAM,GAAG,GAAG,EAAE,CAAC;YACf,MAAM,MAAM,GAAG,MAAM,kCAAe,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,MAAM,GAAqB;gBAC/B,MAAM,EAAE;oBACN;wBACE,KAAK,EAAE,MAAM;wBACb,QAAQ,EAAE,IAAI;wBACd,IAAI,EAAE,QAAQ;qBACf;iBACF;gBACD,YAAY,EAAE,KAAK;aACpB,CAAC;YAEF,MAAM,GAAG,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,MAAM,kCAAe,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,MAAM,GAAqB;gBAC/B,MAAM,EAAE;oBACN;wBACE,KAAK,EAAE,MAAM;wBACb,QAAQ,EAAE,IAAI;wBACd,IAAI,EAAE,QAAQ;qBACf;iBACF;gBACD,YAAY,EAAE,KAAK;gBACnB,YAAY,EAAE,IAAI;aACnB,CAAC;YAEF,MAAM,GAAG,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,MAAM,kCAAe,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,MAAM,GAAG,kCAAe,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAC/D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElC,MAAM,OAAO,GAAG,kCAAe,CAAC,YAAY,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,MAAM,GAAG,kCAAe,CAAC,YAAY,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElC,MAAM,OAAO,GAAG,kCAAe,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEpC,MAAM,OAAO,GAAG,kCAAe,CAAC,YAAY,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,MAAM,GAAG,kCAAe,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElC,MAAM,OAAO,GAAG,kCAAe,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,MAAM,GAAG,kCAAe,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElC,MAAM,OAAO,GAAG,kCAAe,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YACnE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,MAAM,GAAG,kCAAe,CAAC,YAAY,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,QAAQ,CAAC,CAAC;YACxE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElC,MAAM,OAAO,GAAG,kCAAe,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;YAClE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEpC,MAAM,OAAO,GAAG,kCAAe,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC7D,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,MAAM,GAAG,kCAAe,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElC,MAAM,OAAO,GAAG,kCAAe,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACnE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEnC,MAAM,OAAO,GAAG,kCAAe,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YACrE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,MAAM,GAAG,kCAAe,CAAC,YAAY,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;YACzE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElC,MAAM,OAAO,GAAG,kCAAe,CAAC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YACvE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,MAAM,GAAG,kCAAe,CAAC,YAAY,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElC,MAAM,OAAO,GAAG,kCAAe,CAAC,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACnE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,MAAM,GAAG,kCAAe,CAAC,YAAY,CAAC,sCAAsC,EAAE,MAAM,CAAC,CAAC;YAC5F,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElC,MAAM,OAAO,GAAG,kCAAe,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YACrE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;YACxB,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,IAAI,GAAG,kCAAe,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;gBAE9C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACxC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;YACzB,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,IAAI,GAAG,kCAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAEhD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,IAAI,GAAG,kCAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAEhD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;YACzB,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,IAAI,GAAG,kCAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAEhD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE;YACnB,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;gBACvC,MAAM,IAAI,GAAG,kCAAe,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAE3C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE;YACnB,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;gBACvC,MAAM,IAAI,GAAG,kCAAe,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAE3C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;gBACvC,MAAM,IAAI,GAAG,kCAAe,CAAC,KAAK,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;gBAElE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE;YACrB,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,IAAI,GAAG,kCAAe,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;gBAE3C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/D,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE;YACnB,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;gBAC9B,MAAM,IAAI,GAAG,kCAAe,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;gBAEzC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1D,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5D,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE;YACpB,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;gBAC/B,MAAM,IAAI,GAAG,kCAAe,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;gBAE1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,sCAAsC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,sCAAsC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAClD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE;YAClB,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;gBAClD,MAAM,IAAI,GAAG,kCAAe,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;gBAEhE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE;YACrB,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;gBACxD,MAAM,IAAI,GAAG,kCAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;gBAE5D,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC3C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;YACtB,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,IAAI,GAAG,kCAAe,CAAC,KAAK,CAAC,MAAM,CACvC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,EAC1B,oBAAoB,EACpB,aAAa,CACd,CAAC;gBAEF,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;YAC5B,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,CAAC,kCAAe,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpE,MAAM,CAAC,kCAAe,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1E,MAAM,CAAC,kCAAe,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAClE,MAAM,CAAC,kCAAe,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjE,MAAM,CAAC,kCAAe,CAAC,YAAY,CAAC,GAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;gBAC9B,MAAM,CAAC,kCAAe,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrE,MAAM,CAAC,kCAAe,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvE,MAAM,CAAC,kCAAe,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC9D,MAAM,CAAC,kCAAe,CAAC,UAAU,CAAC,GAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;YAC3B,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;gBAC/B,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,sCAAsC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvF,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,sCAAsC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvF,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAChE,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,GAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;gBACtC,MAAM,CAAC,kCAAe,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnE,MAAM,CAAC,kCAAe,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC7E,MAAM,CAAC,kCAAe,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtE,MAAM,CAAC,kCAAe,CAAC,iBAAiB,CAAC,GAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAChC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;gBACtC,MAAM,CAAC,kCAAe,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnE,MAAM,CAAC,kCAAe,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChE,MAAM,CAAC,kCAAe,CAAC,gBAAgB,CAAC,yCAAyC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/F,MAAM,CAAC,kCAAe,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAClE,MAAM,CAAC,kCAAe,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnE,MAAM,CAAC,kCAAe,CAAC,gBAAgB,CAAC,GAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;gBACvC,MAAM,CAAC,kCAAe,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrE,MAAM,CAAC,kCAAe,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpE,MAAM,CAAC,kCAAe,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3E,MAAM,CAAC,kCAAe,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACxE,MAAM,CAAC,kCAAe,CAAC,kBAAkB,CAAC,GAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;gBAClE,MAAM,CAAC,kCAAe,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa;gBACvF,MAAM,CAAC,kCAAe,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc;gBAC3F,MAAM,CAAC,kCAAe,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU;gBACrF,MAAM,CAAC,kCAAe,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtE,MAAM,CAAC,kCAAe,CAAC,iBAAiB,CAAC,GAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;gBACtC,MAAM,CAAC,kCAAe,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAChF,MAAM,CAAC,kCAAe,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBACxG,MAAM,CAAC,kCAAe,CAAC,cAAc,CAAC,GAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;gBAC1C,MAAM,CAAC,kCAAe,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACxF,MAAM,CAAC,kCAAe,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBACtF,MAAM,CAAC,kCAAe,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAChF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;YACxC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;gBAC1C,MAAM,MAAM,GAAG,kCAAe,CAAC,wBAAwB,CAAC,mBAAmB,CAAC,CAAC;gBAE7E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBACxC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;gBACtC,MAAM,MAAM,GAAG,kCAAe,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;gBAEhE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,MAAM,MAAM,GAAG,kCAAe,CAAC,wBAAwB,CAAC,GAAU,CAAC,CAAC;gBAEpE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,MAAM,GAAG,kCAAe,CAAC,MAAM,EAAE;iBACpC,KAAK,CAAC,MAAM,CAAC;iBACX,QAAQ,EAAE;iBACV,IAAI,CAAC,QAAQ,CAAC;iBACd,IAAI,CAAC,kCAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;iBACxC,GAAG,EAAE;iBACP,KAAK,CAAC,KAAK,CAAC;iBACV,QAAQ,EAAE;iBACV,IAAI,CAAC,QAAQ,CAAC;iBACd,IAAI,CAAC,kCAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBAClC,GAAG,EAAE;iBACP,kBAAkB,CAAC,KAAK,CAAC;iBACzB,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,MAAM,GAAG,kCAAe,CAAC,MAAM,EAAE;iBACpC,KAAK,CAAC,QAAQ,CAAC;iBACb,IAAI,CAAC,QAAQ,CAAC;iBACd,OAAO,CAAC,QAAQ,CAAC;iBACjB,WAAW,CAAC,aAAa,CAAC;iBAC1B,GAAG,EAAE;iBACP,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,eAAe,GAAG,KAAK,EAAE,KAAU,EAAE,EAAE,CAAC,CAAC;gBAC7C,OAAO,EAAE,KAAK,KAAK,OAAO;gBAC1B,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;aACb,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,kCAAe,CAAC,MAAM,EAAE;iBACpC,KAAK,CAAC,QAAQ,CAAC;iBACb,SAAS,CAAC,eAAe,CAAC;iBAC1B,GAAG,EAAE;iBACP,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,UAAU,GAAG,kCAAe,CAAC,KAAK,CAAC,MAAM,CAC7C,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,eAAe,EAC7C,sBAAsB,EACtB,mBAAmB,CACpB,CAAC;YAEF,MAAM,MAAM,GAAG,kCAAe,CAAC,MAAM,EAAE;iBACpC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;iBACtC,KAAK,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;iBAC7C,IAAI,CAAC,UAAU,CAAC;iBAChB,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,KAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\utils\\validation.utils.spec.ts"], "sourcesContent": ["/**\r\n * Validation Utils Tests\r\n */\r\n\r\nimport {\r\n  ValidationUtils,\r\n  ValidationRule,\r\n  ValidationSchema,\r\n  FieldSchema,\r\n  SchemaBuilder,\r\n} from '../../utils/validation.utils';\r\n\r\ndescribe('ValidationUtils', () => {\r\n  describe('validateValue', () => {\r\n    it('should validate value against rules', async () => {\r\n      const rules: ValidationRule[] = [\r\n        ValidationUtils.Rules.required(),\r\n        ValidationUtils.Rules.minLength(3),\r\n      ];\r\n\r\n      const result = await ValidationUtils.validateValue('hello', rules, 'testField');\r\n\r\n      expect(result.isValid).toBe(true);\r\n      expect(result.errors).toHaveLength(0);\r\n    });\r\n\r\n    it('should fail validation when rules fail', async () => {\r\n      const rules: ValidationRule[] = [\r\n        ValidationUtils.Rules.required(),\r\n        ValidationUtils.Rules.minLength(10),\r\n      ];\r\n\r\n      const result = await ValidationUtils.validateValue('hello', rules, 'testField');\r\n\r\n      expect(result.isValid).toBe(false);\r\n      expect(result.errors).toHaveLength(1);\r\n      expect(result.errors[0].field).toBe('testField');\r\n      expect(result.errors[0].code).toBe('MIN_LENGTH');\r\n    });\r\n\r\n    it('should stop validation on required rule failure', async () => {\r\n      const rules: ValidationRule[] = [\r\n        ValidationUtils.Rules.required(),\r\n        ValidationUtils.Rules.minLength(3),\r\n      ];\r\n\r\n      const result = await ValidationUtils.validateValue('', rules, 'testField');\r\n\r\n      expect(result.isValid).toBe(false);\r\n      expect(result.errors).toHaveLength(1);\r\n      expect(result.errors[0].code).toBe('REQUIRED');\r\n    });\r\n  });\r\n\r\n  describe('validateObject', () => {\r\n    it('should validate object against schema', async () => {\r\n      const schema: ValidationSchema = {\r\n        fields: [\r\n          {\r\n            field: 'name',\r\n            required: true,\r\n            type: 'string',\r\n            rules: [ValidationUtils.Rules.minLength(2)],\r\n          },\r\n          {\r\n            field: 'age',\r\n            required: true,\r\n            type: 'number',\r\n            rules: [ValidationUtils.Rules.min(0)],\r\n          },\r\n        ],\r\n      };\r\n\r\n      const obj = { name: 'John', age: 25 };\r\n      const result = await ValidationUtils.validateObject(obj, schema);\r\n\r\n      expect(result.isValid).toBe(true);\r\n      expect(result.errors).toHaveLength(0);\r\n    });\r\n\r\n    it('should fail validation for missing required fields', async () => {\r\n      const schema: ValidationSchema = {\r\n        fields: [\r\n          {\r\n            field: 'name',\r\n            required: true,\r\n            type: 'string',\r\n          },\r\n        ],\r\n      };\r\n\r\n      const obj = {};\r\n      const result = await ValidationUtils.validateObject(obj, schema);\r\n\r\n      expect(result.isValid).toBe(false);\r\n      expect(result.errors).toHaveLength(1);\r\n      expect(result.errors[0].code).toBe('REQUIRED_FIELD');\r\n    });\r\n\r\n    it('should fail validation for wrong types', async () => {\r\n      const schema: ValidationSchema = {\r\n        fields: [\r\n          {\r\n            field: 'age',\r\n            required: true,\r\n            type: 'number',\r\n          },\r\n        ],\r\n      };\r\n\r\n      const obj = { age: 'not a number' };\r\n      const result = await ValidationUtils.validateObject(obj, schema);\r\n\r\n      expect(result.isValid).toBe(false);\r\n      expect(result.errors).toHaveLength(1);\r\n      expect(result.errors[0].code).toBe('INVALID_TYPE');\r\n    });\r\n\r\n    it('should apply default values', async () => {\r\n      const schema: ValidationSchema = {\r\n        fields: [\r\n          {\r\n            field: 'status',\r\n            required: false,\r\n            type: 'string',\r\n            defaultValue: 'active',\r\n          },\r\n        ],\r\n      };\r\n\r\n      const obj = {};\r\n      const result = await ValidationUtils.validateObject(obj, schema);\r\n\r\n      expect(result.isValid).toBe(true);\r\n    });\r\n\r\n    it('should handle unknown fields when not allowed', async () => {\r\n      const schema: ValidationSchema = {\r\n        fields: [\r\n          {\r\n            field: 'name',\r\n            required: true,\r\n            type: 'string',\r\n          },\r\n        ],\r\n        allowUnknown: false,\r\n      };\r\n\r\n      const obj = { name: 'John', extra: 'field' };\r\n      const result = await ValidationUtils.validateObject(obj, schema);\r\n\r\n      expect(result.isValid).toBe(false);\r\n      expect(result.errors).toHaveLength(1);\r\n      expect(result.errors[0].code).toBe('UNKNOWN_FIELD');\r\n    });\r\n\r\n    it('should strip unknown fields when configured', async () => {\r\n      const schema: ValidationSchema = {\r\n        fields: [\r\n          {\r\n            field: 'name',\r\n            required: true,\r\n            type: 'string',\r\n          },\r\n        ],\r\n        allowUnknown: false,\r\n        stripUnknown: true,\r\n      };\r\n\r\n      const obj = { name: 'John', extra: 'field' };\r\n      const result = await ValidationUtils.validateObject(obj, schema);\r\n\r\n      expect(result.isValid).toBe(true);\r\n      expect(result.warnings).toHaveLength(1);\r\n      expect(result.warnings[0].code).toBe('UNKNOWN_FIELD_STRIPPED');\r\n    });\r\n  });\r\n\r\n  describe('validateType', () => {\r\n    it('should validate string type', () => {\r\n      const result = ValidationUtils.validateType('hello', 'string');\r\n      expect(result.isValid).toBe(true);\r\n\r\n      const result2 = ValidationUtils.validateType(123, 'string');\r\n      expect(result2.isValid).toBe(false);\r\n    });\r\n\r\n    it('should validate number type', () => {\r\n      const result = ValidationUtils.validateType(123, 'number');\r\n      expect(result.isValid).toBe(true);\r\n\r\n      const result2 = ValidationUtils.validateType('123', 'number');\r\n      expect(result2.isValid).toBe(false);\r\n\r\n      const result3 = ValidationUtils.validateType(NaN, 'number');\r\n      expect(result3.isValid).toBe(false);\r\n    });\r\n\r\n    it('should validate boolean type', () => {\r\n      const result = ValidationUtils.validateType(true, 'boolean');\r\n      expect(result.isValid).toBe(true);\r\n\r\n      const result2 = ValidationUtils.validateType('true', 'boolean');\r\n      expect(result2.isValid).toBe(false);\r\n    });\r\n\r\n    it('should validate array type', () => {\r\n      const result = ValidationUtils.validateType([1, 2, 3], 'array');\r\n      expect(result.isValid).toBe(true);\r\n\r\n      const result2 = ValidationUtils.validateType('not array', 'array');\r\n      expect(result2.isValid).toBe(false);\r\n    });\r\n\r\n    it('should validate object type', () => {\r\n      const result = ValidationUtils.validateType({ key: 'value' }, 'object');\r\n      expect(result.isValid).toBe(true);\r\n\r\n      const result2 = ValidationUtils.validateType([1, 2, 3], 'object');\r\n      expect(result2.isValid).toBe(false);\r\n\r\n      const result3 = ValidationUtils.validateType(null, 'object');\r\n      expect(result3.isValid).toBe(false);\r\n    });\r\n\r\n    it('should validate date type', () => {\r\n      const result = ValidationUtils.validateType(new Date(), 'date');\r\n      expect(result.isValid).toBe(true);\r\n\r\n      const result2 = ValidationUtils.validateType('2023-01-01', 'date');\r\n      expect(result2.isValid).toBe(true);\r\n\r\n      const result3 = ValidationUtils.validateType('invalid date', 'date');\r\n      expect(result3.isValid).toBe(false);\r\n    });\r\n\r\n    it('should validate email type', () => {\r\n      const result = ValidationUtils.validateType('<EMAIL>', 'email');\r\n      expect(result.isValid).toBe(true);\r\n\r\n      const result2 = ValidationUtils.validateType('invalid-email', 'email');\r\n      expect(result2.isValid).toBe(false);\r\n    });\r\n\r\n    it('should validate URL type', () => {\r\n      const result = ValidationUtils.validateType('https://example.com', 'url');\r\n      expect(result.isValid).toBe(true);\r\n\r\n      const result2 = ValidationUtils.validateType('invalid-url', 'url');\r\n      expect(result2.isValid).toBe(false);\r\n    });\r\n\r\n    it('should validate UUID type', () => {\r\n      const result = ValidationUtils.validateType('123e4567-e89b-12d3-a456-************', 'uuid');\r\n      expect(result.isValid).toBe(true);\r\n\r\n      const result2 = ValidationUtils.validateType('invalid-uuid', 'uuid');\r\n      expect(result2.isValid).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('validation rules', () => {\r\n    describe('required', () => {\r\n      it('should validate required values', () => {\r\n        const rule = ValidationUtils.Rules.required();\r\n\r\n        expect(rule.validate('value')).toBe(true);\r\n        expect(rule.validate('')).toBe(false);\r\n        expect(rule.validate(null)).toBe(false);\r\n        expect(rule.validate(undefined)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('minLength', () => {\r\n      it('should validate minimum length for strings', () => {\r\n        const rule = ValidationUtils.Rules.minLength(5);\r\n\r\n        expect(rule.validate('hello')).toBe(true);\r\n        expect(rule.validate('hello world')).toBe(true);\r\n        expect(rule.validate('hi')).toBe(false);\r\n      });\r\n\r\n      it('should validate minimum length for arrays', () => {\r\n        const rule = ValidationUtils.Rules.minLength(3);\r\n\r\n        expect(rule.validate([1, 2, 3])).toBe(true);\r\n        expect(rule.validate([1, 2, 3, 4])).toBe(true);\r\n        expect(rule.validate([1, 2])).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('maxLength', () => {\r\n      it('should validate maximum length for strings', () => {\r\n        const rule = ValidationUtils.Rules.maxLength(5);\r\n\r\n        expect(rule.validate('hello')).toBe(true);\r\n        expect(rule.validate('hi')).toBe(true);\r\n        expect(rule.validate('hello world')).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('min', () => {\r\n      it('should validate minimum value', () => {\r\n        const rule = ValidationUtils.Rules.min(10);\r\n\r\n        expect(rule.validate(10)).toBe(true);\r\n        expect(rule.validate(15)).toBe(true);\r\n        expect(rule.validate(5)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('max', () => {\r\n      it('should validate maximum value', () => {\r\n        const rule = ValidationUtils.Rules.max(10);\r\n\r\n        expect(rule.validate(10)).toBe(true);\r\n        expect(rule.validate(5)).toBe(true);\r\n        expect(rule.validate(15)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('pattern', () => {\r\n      it('should validate regex pattern', () => {\r\n        const rule = ValidationUtils.Rules.pattern(/^\\d{3}-\\d{3}-\\d{4}$/);\r\n\r\n        expect(rule.validate('************')).toBe(true);\r\n        expect(rule.validate('invalid-phone')).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('email', () => {\r\n      it('should validate email addresses', () => {\r\n        const rule = ValidationUtils.Rules.email();\r\n\r\n        expect(rule.validate('<EMAIL>')).toBe(true);\r\n        expect(rule.validate('<EMAIL>')).toBe(true);\r\n        expect(rule.validate('invalid-email')).toBe(false);\r\n        expect(rule.validate('@example.com')).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('url', () => {\r\n      it('should validate URLs', () => {\r\n        const rule = ValidationUtils.Rules.url();\r\n\r\n        expect(rule.validate('https://example.com')).toBe(true);\r\n        expect(rule.validate('http://localhost:3000')).toBe(true);\r\n        expect(rule.validate('ftp://files.example.com')).toBe(true);\r\n        expect(rule.validate('invalid-url')).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('uuid', () => {\r\n      it('should validate UUIDs', () => {\r\n        const rule = ValidationUtils.Rules.uuid();\r\n\r\n        expect(rule.validate('123e4567-e89b-12d3-a456-************')).toBe(true);\r\n        expect(rule.validate('550e8400-e29b-41d4-a716-************')).toBe(true);\r\n        expect(rule.validate('invalid-uuid')).toBe(false);\r\n        expect(rule.validate('123e4567-e89b-12d3-a456')).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('in', () => {\r\n      it('should validate value is in allowed list', () => {\r\n        const rule = ValidationUtils.Rules.in(['red', 'green', 'blue']);\r\n\r\n        expect(rule.validate('red')).toBe(true);\r\n        expect(rule.validate('green')).toBe(true);\r\n        expect(rule.validate('yellow')).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('notIn', () => {\r\n      it('should validate value is not in forbidden list', () => {\r\n        const rule = ValidationUtils.Rules.notIn(['admin', 'root']);\r\n\r\n        expect(rule.validate('user')).toBe(true);\r\n        expect(rule.validate('guest')).toBe(true);\r\n        expect(rule.validate('admin')).toBe(false);\r\n        expect(rule.validate('root')).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('custom', () => {\r\n      it('should validate with custom function', () => {\r\n        const rule = ValidationUtils.Rules.custom(\r\n          (value) => value % 2 === 0,\r\n          'Value must be even',\r\n          'EVEN_NUMBER'\r\n        );\r\n\r\n        expect(rule.validate(4)).toBe(true);\r\n        expect(rule.validate(6)).toBe(true);\r\n        expect(rule.validate(3)).toBe(false);\r\n        expect(rule.validate(5)).toBe(false);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('utility functions', () => {\r\n    describe('isValidEmail', () => {\r\n      it('should validate email addresses', () => {\r\n        expect(ValidationUtils.isValidEmail('<EMAIL>')).toBe(true);\r\n        expect(ValidationUtils.isValidEmail('<EMAIL>')).toBe(true);\r\n        expect(ValidationUtils.isValidEmail('invalid-email')).toBe(false);\r\n        expect(ValidationUtils.isValidEmail('@example.com')).toBe(false);\r\n        expect(ValidationUtils.isValidEmail(123 as any)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isValidUrl', () => {\r\n      it('should validate URLs', () => {\r\n        expect(ValidationUtils.isValidUrl('https://example.com')).toBe(true);\r\n        expect(ValidationUtils.isValidUrl('http://localhost:3000')).toBe(true);\r\n        expect(ValidationUtils.isValidUrl('invalid-url')).toBe(false);\r\n        expect(ValidationUtils.isValidUrl(123 as any)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isValidUUID', () => {\r\n      it('should validate UUIDs', () => {\r\n        expect(ValidationUtils.isValidUUID('123e4567-e89b-12d3-a456-************')).toBe(true);\r\n        expect(ValidationUtils.isValidUUID('550e8400-e29b-41d4-a716-************')).toBe(true);\r\n        expect(ValidationUtils.isValidUUID('invalid-uuid')).toBe(false);\r\n        expect(ValidationUtils.isValidUUID(123 as any)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isValidDateString', () => {\r\n      it('should validate date strings', () => {\r\n        expect(ValidationUtils.isValidDateString('2023-01-01')).toBe(true);\r\n        expect(ValidationUtils.isValidDateString('2023-01-01T12:00:00Z')).toBe(true);\r\n        expect(ValidationUtils.isValidDateString('invalid-date')).toBe(false);\r\n        expect(ValidationUtils.isValidDateString(123 as any)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isValidIpAddress', () => {\r\n      it('should validate IP addresses', () => {\r\n        expect(ValidationUtils.isValidIpAddress('***********')).toBe(true);\r\n        expect(ValidationUtils.isValidIpAddress('********')).toBe(true);\r\n        expect(ValidationUtils.isValidIpAddress('2001:0db8:85a3:0000:0000:8a2e:0370:7334')).toBe(true);\r\n        expect(ValidationUtils.isValidIpAddress('256.1.1.1')).toBe(false);\r\n        expect(ValidationUtils.isValidIpAddress('invalid-ip')).toBe(false);\r\n        expect(ValidationUtils.isValidIpAddress(123 as any)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isValidPhoneNumber', () => {\r\n      it('should validate phone numbers', () => {\r\n        expect(ValidationUtils.isValidPhoneNumber('+1234567890')).toBe(true);\r\n        expect(ValidationUtils.isValidPhoneNumber('1234567890')).toBe(true);\r\n        expect(ValidationUtils.isValidPhoneNumber('+****************')).toBe(true);\r\n        expect(ValidationUtils.isValidPhoneNumber('invalid-phone')).toBe(false);\r\n        expect(ValidationUtils.isValidPhoneNumber(123 as any)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isValidCreditCard', () => {\r\n      it('should validate credit card numbers using Luhn algorithm', () => {\r\n        expect(ValidationUtils.isValidCreditCard('****************')).toBe(true); // Valid Visa\r\n        expect(ValidationUtils.isValidCreditCard('4532 0151 1283 0366')).toBe(true); // With spaces\r\n        expect(ValidationUtils.isValidCreditCard('1234567890123456')).toBe(false); // Invalid\r\n        expect(ValidationUtils.isValidCreditCard('invalid-card')).toBe(false);\r\n        expect(ValidationUtils.isValidCreditCard(123 as any)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('sanitizeString', () => {\r\n      it('should sanitize string input', () => {\r\n        expect(ValidationUtils.sanitizeString('  hello   world  ')).toBe('hello world');\r\n        expect(ValidationUtils.sanitizeString('test<script>alert()</script>')).toBe('testscriptalert()/script');\r\n        expect(ValidationUtils.sanitizeString(123 as any)).toBe('');\r\n      });\r\n    });\r\n\r\n    describe('normalizeEmail', () => {\r\n      it('should normalize email addresses', () => {\r\n        expect(ValidationUtils.normalizeEmail('  <EMAIL>  ')).toBe('<EMAIL>');\r\n        expect(ValidationUtils.normalizeEmail('<EMAIL>')).toBe('<EMAIL>');\r\n        expect(ValidationUtils.normalizeEmail('invalid-email')).toBe('invalid-email');\r\n      });\r\n    });\r\n\r\n    describe('validatePasswordStrength', () => {\r\n      it('should validate strong passwords', () => {\r\n        const result = ValidationUtils.validatePasswordStrength('StrongP@ssw0rd123');\r\n\r\n        expect(result.isValid).toBe(true);\r\n        expect(result.score).toBeGreaterThan(4);\r\n        expect(result.feedback).toHaveLength(0);\r\n      });\r\n\r\n      it('should reject weak passwords', () => {\r\n        const result = ValidationUtils.validatePasswordStrength('weak');\r\n\r\n        expect(result.isValid).toBe(false);\r\n        expect(result.score).toBeLessThan(4);\r\n        expect(result.feedback.length).toBeGreaterThan(0);\r\n      });\r\n\r\n      it('should handle non-string input', () => {\r\n        const result = ValidationUtils.validatePasswordStrength(123 as any);\r\n\r\n        expect(result.isValid).toBe(false);\r\n        expect(result.score).toBe(0);\r\n        expect(result.feedback).toContain('Password must be a string');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('SchemaBuilder', () => {\r\n    it('should build schema using fluent interface', () => {\r\n      const schema = ValidationUtils.schema()\r\n        .field('name')\r\n          .required()\r\n          .type('string')\r\n          .rule(ValidationUtils.Rules.minLength(2))\r\n          .end()\r\n        .field('age')\r\n          .required()\r\n          .type('number')\r\n          .rule(ValidationUtils.Rules.min(0))\r\n          .end()\r\n        .allowUnknownFields(false)\r\n        .build();\r\n\r\n      expect(schema.fields).toHaveLength(2);\r\n      expect(schema.fields[0].field).toBe('name');\r\n      expect(schema.fields[0].required).toBe(true);\r\n      expect(schema.fields[0].type).toBe('string');\r\n      expect(schema.fields[1].field).toBe('age');\r\n      expect(schema.allowUnknown).toBe(false);\r\n    });\r\n\r\n    it('should build schema with default values and descriptions', () => {\r\n      const schema = ValidationUtils.schema()\r\n        .field('status')\r\n          .type('string')\r\n          .default('active')\r\n          .description('User status')\r\n          .end()\r\n        .build();\r\n\r\n      expect(schema.fields[0].defaultValue).toBe('active');\r\n      expect(schema.fields[0].description).toBe('User status');\r\n    });\r\n\r\n    it('should build schema with custom validators', () => {\r\n      const customValidator = async (value: any) => ({\r\n        isValid: value === 'valid',\r\n        errors: [],\r\n        warnings: [],\r\n      });\r\n\r\n      const schema = ValidationUtils.schema()\r\n        .field('custom')\r\n          .validator(customValidator)\r\n          .end()\r\n        .build();\r\n\r\n      expect(schema.fields[0].validator).toBe(customValidator);\r\n    });\r\n\r\n    it('should build schema with schema-level rules', () => {\r\n      const schemaRule = ValidationUtils.Rules.custom(\r\n        (obj) => obj.password === obj.confirmPassword,\r\n        'Passwords must match',\r\n        'PASSWORD_MISMATCH'\r\n      );\r\n\r\n      const schema = ValidationUtils.schema()\r\n        .field('password').type('string').end()\r\n        .field('confirmPassword').type('string').end()\r\n        .rule(schemaRule)\r\n        .build();\r\n\r\n      expect(schema.rules).toHaveLength(1);\r\n      expect(schema.rules![0]).toBe(schemaRule);\r\n    });\r\n  });\r\n});"], "version": 3}