{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability-assessment.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,0FAAqF;AACrF,8GAAmG;AACnG,wFAA8E;AAC9E,4FAAkF;AAClF,kGAAuF;AACvF,yFAAqF;AACrF,6FAAyF;AACzF,0GAAsG;AAEtG,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;IAC9C,IAAI,OAAuC,CAAC;IAC5C,IAAI,oBAAsE,CAAC;IAC3E,IAAI,uBAA+D,CAAC;IACpE,IAAI,eAA+C,CAAC;IACpD,IAAI,cAA0D,CAAC;IAC/D,IAAI,aAAyC,CAAC;IAC9C,IAAI,YAAuC,CAAC;IAC5C,IAAI,mBAAqD,CAAC;IAE1D,MAAM,cAAc,GAAG;QACrB,EAAE,EAAE,sCAAsC;QAC1C,eAAe,EAAE,UAAU;QAC3B,OAAO,EAAE,WAAW;QACpB,cAAc,EAAE,QAAQ;QACxB,MAAM,EAAE,SAAS;QACjB,QAAQ,EAAE,eAAe;QACzB,gBAAgB,EAAE,MAAM;QACxB,iBAAiB,EAAE,GAAG;QACtB,eAAe,EAAE,EAAE;QACnB,eAAe,EAAE,KAAK;QACtB,cAAc,EAAE,KAAK;QACrB,UAAU,EAAE,UAAU;QACtB,UAAU,EAAE,IAAI,IAAI,EAAE;QACtB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC9B,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;KACtB,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,EAAE,EAAE,UAAU;QACd,UAAU,EAAE,eAAe;QAC3B,KAAK,EAAE,oBAAoB;QAC3B,QAAQ,EAAE,QAAQ;KACnB,CAAC;IAEF,MAAM,SAAS,GAAG;QAChB,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,QAAQ;KACf,CAAC;IAEF,MAAM,QAAQ,GAAG;QACf,EAAE,EAAE,UAAU;QACd,IAAI,EAAE,WAAW;QACjB,MAAM,EAAE,WAAW;KACpB,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,cAAc,GAAG;YACrB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;YAClB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;YACpB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;YAChB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;gBACjC,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAC7C,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACjC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACnC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACtC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;gBAC1B,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;aACnB,CAAC,CAAC;SACJ,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,iEAA8B;gBAC9B;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,yDAAuB,CAAC;oBACpD,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,oCAAa,CAAC;oBAC1C,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,oBAAK,CAAC;oBAClC,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,6CAAiB,CAAC;oBAC9C,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE;wBACR,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;wBAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;wBACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;wBACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;qBACjB;iBACF;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE;wBACR,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;qBACzB;iBACF;gBACD;oBACE,OAAO,EAAE,0CAAmB;oBAC5B,QAAQ,EAAE;wBACR,0BAA0B,EAAE,IAAI,CAAC,EAAE,EAAE;qBACtC;iBACF;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAiC,iEAA8B,CAAC,CAAC;QACrF,oBAAoB,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,yDAAuB,CAAC,CAAC,CAAC;QAC/E,uBAAuB,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,oCAAa,CAAC,CAAC,CAAC;QACxE,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,oBAAK,CAAC,CAAC,CAAC;QACxD,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,6CAAiB,CAAC,CAAC,CAAC;QACnE,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,8BAAa,CAAC,CAAC;QAC1C,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,4BAAY,CAAC,CAAC;QACxC,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,0CAAmB,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAC3B,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,cAAc,GAAG;gBACrB,eAAe,EAAE,UAAU;gBAC3B,OAAO,EAAE,WAAW;gBACpB,cAAc,EAAE,QAAiB;gBACjC,QAAQ,EAAE,eAAe;gBACzB,gBAAgB,EAAE,MAAe;gBACjC,eAAe,EAAE,EAAE;aACpB,CAAC;YAEF,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YACrE,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACrD,oBAAoB,CAAC,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YAC5D,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAE5D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;YAE1E,MAAM,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;aAC1B,CAAC,CAAC;YACH,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACnD,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;aAC3B,CAAC,CAAC;YACH,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CACtD,MAAM,CAAC,gBAAgB,CAAC;gBACtB,GAAG,cAAc;gBACjB,MAAM,EAAE,SAAS;gBACjB,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC7B,CAAC,CACH,CAAC;YACF,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,QAAQ,EACR,0BAA0B,EAC1B,cAAc,CAAC,EAAE,EACjB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;YAC3E,MAAM,cAAc,GAAG;gBACrB,eAAe,EAAE,mBAAmB;gBACpC,cAAc,EAAE,QAAiB;aAClC,CAAC;YAEF,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CACV,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,UAAU,CAAC,CACrD,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,cAAc,GAAG;gBACrB,eAAe,EAAE,UAAU;gBAC3B,OAAO,EAAE,oBAAoB;gBAC7B,cAAc,EAAE,QAAiB;aAClC,CAAC;YAEF,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YACrE,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEhD,MAAM,MAAM,CACV,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,UAAU,CAAC,CACrD,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAE/D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,sCAAsC,CAAC,CAAC;YAE1F,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACvC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACxD,KAAK,EAAE,EAAE,EAAE,EAAE,sCAAsC,EAAE;gBACrD,SAAS,EAAE,CAAC,eAAe,EAAE,OAAO,EAAE,MAAM,CAAC;aAC9C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAErD,MAAM,MAAM,CACV,OAAO,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAChD,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,kBAAkB,EAAE,CAAC;YACnE,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAE1E,MAAM,cAAc,GAAG;gBACrB,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,gBAAgB,EAAE,CAAC,UAAU,CAAC;gBAC9B,QAAQ,EAAE,CAAC,SAAS,CAAC;gBACrB,eAAe,EAAE,CAAC,QAAQ,CAAC;aAC5B,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAE/D,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,sDAAsD,EACtD,EAAE,gBAAgB,EAAE,CAAC,UAAU,CAAC,EAAE,CACnC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,kBAAkB,EAAE,CAAC;YACnE,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAE5D,MAAM,cAAc,GAAG;gBACrB,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;aACV,CAAC;YAEF,MAAM,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAEhD,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YACvD,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,OAAO,GAAG;gBACd,QAAQ,EAAE,kBAAkB;gBAC5B,gBAAgB,EAAE,UAAmB;gBACrC,eAAe,EAAE,EAAE;aACpB,CAAC;YAEF,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAC/D,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBAC1C,GAAG,cAAc;gBACjB,GAAG,OAAO;aACX,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,sCAAsC,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YAE3G,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACrD,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,QAAQ,EACR,0BAA0B,EAC1B,sCAAsC,EACtC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,eAAe,EAAE,cAAc,CAAC,eAAe;gBAC/C,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC5B,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAErD,MAAM,MAAM,CACV,OAAO,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,EAAE,EAAE,UAAU,CAAC,CAC5D,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAC/D,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBAC1C,GAAG,cAAc;gBACjB,MAAM,EAAE,WAAW;aACpB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,sCAAsC,EAAE,UAAU,CAAC,CAAC;YAEpG,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACnD,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACrD,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,UAAU,EACV,0BAA0B,EAC1B,sCAAsC,EACtC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,eAAe,EAAE,cAAc,CAAC,eAAe;aAChD,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAErD,MAAM,MAAM,CACV,OAAO,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAC1D,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,UAAU,GAAG;gBACjB,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,uBAAuB;aAClC,CAAC;YAEF,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAC/D,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBAC1C,GAAG,cAAc;gBACjB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,sCAAsC,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;YAElH,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,cAAc,EAAE,uBAAuB,CAAC,CAAC;YAC5F,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACrD,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,cAAc,EACd,QAAQ,EACR,0BAA0B,EAC1B,sCAAsC,EACtC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,uBAAuB;aAClC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,UAAU,GAAG;gBACjB,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,qBAAqB;aAChC,CAAC;YAEF,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAC/D,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBAC1C,GAAG,cAAc;gBACjB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,gBAAgB,CAAC,sCAAsC,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;YAEnG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,cAAc,EAAE,qBAAqB,CAAC,CAAC;YAC1F,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAC/D,oBAAoB,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAE9D,MAAM,OAAO,CAAC,gBAAgB,CAAC,sCAAsC,EAAE,UAAU,CAAC,CAAC;YAEnF,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YACzE,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,QAAQ,EACR,0BAA0B,EAC1B,sCAAsC,EACtC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,eAAe,EAAE,cAAc,CAAC,eAAe;gBAC/C,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAErD,MAAM,MAAM,CACV,OAAO,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,UAAU,CAAC,CACxD,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability-assessment.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { VulnerabilityAssessmentService } from '../vulnerability-assessment.service';\r\nimport { VulnerabilityAssessment } from '../../../domain/entities/vulnerability-assessment.entity';\r\nimport { Vulnerability } from '../../../domain/entities/vulnerability.entity';\r\nimport { Asset } from '../../../../asset-management/domain/entities/asset.entity';\r\nimport { VulnerabilityScan } from '../../../domain/entities/vulnerability-scan.entity';\r\nimport { LoggerService } from '../../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../../infrastructure/notification/notification.service';\r\n\r\ndescribe('VulnerabilityAssessmentService', () => {\r\n  let service: VulnerabilityAssessmentService;\r\n  let assessmentRepository: jest.Mocked<Repository<VulnerabilityAssessment>>;\r\n  let vulnerabilityRepository: jest.Mocked<Repository<Vulnerability>>;\r\n  let assetRepository: jest.Mocked<Repository<Asset>>;\r\n  let scanRepository: jest.Mocked<Repository<VulnerabilityScan>>;\r\n  let loggerService: jest.Mocked<LoggerService>;\r\n  let auditService: jest.Mocked<AuditService>;\r\n  let notificationService: jest.Mocked<NotificationService>;\r\n\r\n  const mockAssessment = {\r\n    id: '123e4567-e89b-12d3-a456-426614174000',\r\n    vulnerabilityId: 'vuln-123',\r\n    assetId: 'asset-123',\r\n    assessmentType: 'manual',\r\n    status: 'pending',\r\n    findings: 'Test findings',\r\n    assessedSeverity: 'high',\r\n    assessedCvssScore: 7.5,\r\n    confidenceLevel: 85,\r\n    isFalsePositive: false,\r\n    isAcceptedRisk: false,\r\n    assessedBy: 'user-123',\r\n    assessedAt: new Date(),\r\n    getSummary: jest.fn(),\r\n    exportForReporting: jest.fn(),\r\n    complete: jest.fn(),\r\n    review: jest.fn(),\r\n    reject: jest.fn(),\r\n    markAsFalsePositive: jest.fn(),\r\n    acceptRisk: jest.fn(),\r\n  };\r\n\r\n  const mockVulnerability = {\r\n    id: 'vuln-123',\r\n    identifier: 'CVE-2023-1234',\r\n    title: 'Test Vulnerability',\r\n    severity: 'medium',\r\n  };\r\n\r\n  const mockAsset = {\r\n    id: 'asset-123',\r\n    name: 'Test Asset',\r\n    type: 'server',\r\n  };\r\n\r\n  const mockScan = {\r\n    id: 'scan-123',\r\n    name: 'Test Scan',\r\n    status: 'completed',\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const mockRepository = {\r\n      create: jest.fn(),\r\n      save: jest.fn(),\r\n      find: jest.fn(),\r\n      findOne: jest.fn(),\r\n      findOneBy: jest.fn(),\r\n      count: jest.fn(),\r\n      remove: jest.fn(),\r\n      createQueryBuilder: jest.fn(() => ({\r\n        leftJoinAndSelect: jest.fn().mockReturnThis(),\r\n        where: jest.fn().mockReturnThis(),\r\n        andWhere: jest.fn().mockReturnThis(),\r\n        orderBy: jest.fn().mockReturnThis(),\r\n        addOrderBy: jest.fn().mockReturnThis(),\r\n        skip: jest.fn().mockReturnThis(),\r\n        take: jest.fn().mockReturnThis(),\r\n        getManyAndCount: jest.fn(),\r\n        getMany: jest.fn(),\r\n      })),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        VulnerabilityAssessmentService,\r\n        {\r\n          provide: getRepositoryToken(VulnerabilityAssessment),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(Vulnerability),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(Asset),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(VulnerabilityScan),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: {\r\n            debug: jest.fn(),\r\n            log: jest.fn(),\r\n            warn: jest.fn(),\r\n            error: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: {\r\n            logUserAction: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: NotificationService,\r\n          useValue: {\r\n            sendAssessmentNotification: jest.fn(),\r\n          },\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<VulnerabilityAssessmentService>(VulnerabilityAssessmentService);\r\n    assessmentRepository = module.get(getRepositoryToken(VulnerabilityAssessment));\r\n    vulnerabilityRepository = module.get(getRepositoryToken(Vulnerability));\r\n    assetRepository = module.get(getRepositoryToken(Asset));\r\n    scanRepository = module.get(getRepositoryToken(VulnerabilityScan));\r\n    loggerService = module.get(LoggerService);\r\n    auditService = module.get(AuditService);\r\n    notificationService = module.get(NotificationService);\r\n  });\r\n\r\n  it('should be defined', () => {\r\n    expect(service).toBeDefined();\r\n  });\r\n\r\n  describe('createAssessment', () => {\r\n    it('should create a new assessment', async () => {\r\n      const assessmentData = {\r\n        vulnerabilityId: 'vuln-123',\r\n        assetId: 'asset-123',\r\n        assessmentType: 'manual' as const,\r\n        findings: 'Test findings',\r\n        assessedSeverity: 'high' as const,\r\n        confidenceLevel: 85,\r\n      };\r\n\r\n      vulnerabilityRepository.findOne.mockResolvedValue(mockVulnerability);\r\n      assetRepository.findOne.mockResolvedValue(mockAsset);\r\n      assessmentRepository.create.mockReturnValue(mockAssessment);\r\n      assessmentRepository.save.mockResolvedValue(mockAssessment);\r\n\r\n      const result = await service.createAssessment(assessmentData, 'user-123');\r\n\r\n      expect(vulnerabilityRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: 'vuln-123' },\r\n      });\r\n      expect(assetRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: 'asset-123' },\r\n      });\r\n      expect(assessmentRepository.create).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          ...assessmentData,\r\n          status: 'pending',\r\n          assessedBy: 'user-123',\r\n          assessedAt: expect.any(Date),\r\n        })\r\n      );\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'create',\r\n        'vulnerability_assessment',\r\n        mockAssessment.id,\r\n        expect.any(Object)\r\n      );\r\n    });\r\n\r\n    it('should throw NotFoundException when vulnerability not found', async () => {\r\n      const assessmentData = {\r\n        vulnerabilityId: 'non-existent-vuln',\r\n        assessmentType: 'manual' as const,\r\n      };\r\n\r\n      vulnerabilityRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.createAssessment(assessmentData, 'user-123')\r\n      ).rejects.toThrow('Vulnerability not found');\r\n    });\r\n\r\n    it('should throw NotFoundException when asset not found', async () => {\r\n      const assessmentData = {\r\n        vulnerabilityId: 'vuln-123',\r\n        assetId: 'non-existent-asset',\r\n        assessmentType: 'manual' as const,\r\n      };\r\n\r\n      vulnerabilityRepository.findOne.mockResolvedValue(mockVulnerability);\r\n      assetRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.createAssessment(assessmentData, 'user-123')\r\n      ).rejects.toThrow('Asset not found');\r\n    });\r\n  });\r\n\r\n  describe('getAssessmentDetails', () => {\r\n    it('should return assessment details', async () => {\r\n      assessmentRepository.findOne.mockResolvedValue(mockAssessment);\r\n\r\n      const result = await service.getAssessmentDetails('123e4567-e89b-12d3-a456-426614174000');\r\n\r\n      expect(result).toEqual(mockAssessment);\r\n      expect(assessmentRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: '123e4567-e89b-12d3-a456-426614174000' },\r\n        relations: ['vulnerability', 'asset', 'scan'],\r\n      });\r\n    });\r\n\r\n    it('should throw NotFoundException when assessment not found', async () => {\r\n      assessmentRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.getAssessmentDetails('non-existent-id')\r\n      ).rejects.toThrow('Assessment not found');\r\n    });\r\n  });\r\n\r\n  describe('searchAssessments', () => {\r\n    it('should search assessments with filters', async () => {\r\n      const mockQueryBuilder = assessmentRepository.createQueryBuilder();\r\n      mockQueryBuilder.getManyAndCount.mockResolvedValue([[mockAssessment], 1]);\r\n\r\n      const searchCriteria = {\r\n        page: 1,\r\n        limit: 10,\r\n        vulnerabilityIds: ['vuln-123'],\r\n        statuses: ['pending'],\r\n        assessmentTypes: ['manual'],\r\n      };\r\n\r\n      const result = await service.searchAssessments(searchCriteria);\r\n\r\n      expect(result).toHaveProperty('assessments');\r\n      expect(result).toHaveProperty('total');\r\n      expect(result).toHaveProperty('page');\r\n      expect(result).toHaveProperty('totalPages');\r\n      expect(result.assessments).toHaveLength(1);\r\n      expect(result.total).toBe(1);\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        'assessment.vulnerabilityId IN (:...vulnerabilityIds)',\r\n        { vulnerabilityIds: ['vuln-123'] }\r\n      );\r\n    });\r\n\r\n    it('should handle pagination correctly', async () => {\r\n      const mockQueryBuilder = assessmentRepository.createQueryBuilder();\r\n      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);\r\n\r\n      const searchCriteria = {\r\n        page: 3,\r\n        limit: 20,\r\n      };\r\n\r\n      await service.searchAssessments(searchCriteria);\r\n\r\n      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(40);\r\n      expect(mockQueryBuilder.take).toHaveBeenCalledWith(20);\r\n    });\r\n  });\r\n\r\n  describe('updateAssessment', () => {\r\n    it('should update an existing assessment', async () => {\r\n      const updates = {\r\n        findings: 'Updated findings',\r\n        assessedSeverity: 'critical' as const,\r\n        confidenceLevel: 95,\r\n      };\r\n\r\n      assessmentRepository.findOne.mockResolvedValue(mockAssessment);\r\n      assessmentRepository.save.mockResolvedValue({\r\n        ...mockAssessment,\r\n        ...updates,\r\n      });\r\n\r\n      const result = await service.updateAssessment('123e4567-e89b-12d3-a456-426614174000', updates, 'user-123');\r\n\r\n      expect(assessmentRepository.save).toHaveBeenCalled();\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'update',\r\n        'vulnerability_assessment',\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        expect.objectContaining({\r\n          vulnerabilityId: mockAssessment.vulnerabilityId,\r\n          changes: expect.any(Object),\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should throw NotFoundException when assessment not found', async () => {\r\n      assessmentRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.updateAssessment('non-existent-id', {}, 'user-123')\r\n      ).rejects.toThrow('Assessment not found');\r\n    });\r\n  });\r\n\r\n  describe('completeAssessment', () => {\r\n    it('should complete an assessment', async () => {\r\n      assessmentRepository.findOne.mockResolvedValue(mockAssessment);\r\n      assessmentRepository.save.mockResolvedValue({\r\n        ...mockAssessment,\r\n        status: 'completed',\r\n      });\r\n\r\n      const result = await service.completeAssessment('123e4567-e89b-12d3-a456-426614174000', 'user-123');\r\n\r\n      expect(mockAssessment.complete).toHaveBeenCalled();\r\n      expect(assessmentRepository.save).toHaveBeenCalled();\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'complete',\r\n        'vulnerability_assessment',\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        expect.objectContaining({\r\n          vulnerabilityId: mockAssessment.vulnerabilityId,\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should throw NotFoundException when assessment not found', async () => {\r\n      assessmentRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.completeAssessment('non-existent-id', 'user-123')\r\n      ).rejects.toThrow('Assessment not found');\r\n    });\r\n  });\r\n\r\n  describe('reviewAssessment', () => {\r\n    it('should review and approve an assessment', async () => {\r\n      const reviewData = {\r\n        approved: true,\r\n        comments: 'Assessment looks good',\r\n      };\r\n\r\n      assessmentRepository.findOne.mockResolvedValue(mockAssessment);\r\n      assessmentRepository.save.mockResolvedValue({\r\n        ...mockAssessment,\r\n        status: 'reviewed',\r\n      });\r\n\r\n      const result = await service.reviewAssessment('123e4567-e89b-12d3-a456-426614174000', reviewData, 'reviewer-123');\r\n\r\n      expect(mockAssessment.review).toHaveBeenCalledWith('reviewer-123', 'Assessment looks good');\r\n      expect(assessmentRepository.save).toHaveBeenCalled();\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'reviewer-123',\r\n        'review',\r\n        'vulnerability_assessment',\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        expect.objectContaining({\r\n          approved: true,\r\n          comments: 'Assessment looks good',\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should review and reject an assessment', async () => {\r\n      const reviewData = {\r\n        approved: false,\r\n        comments: 'Needs more analysis',\r\n      };\r\n\r\n      assessmentRepository.findOne.mockResolvedValue(mockAssessment);\r\n      assessmentRepository.save.mockResolvedValue({\r\n        ...mockAssessment,\r\n        status: 'rejected',\r\n      });\r\n\r\n      await service.reviewAssessment('123e4567-e89b-12d3-a456-426614174000', reviewData, 'reviewer-123');\r\n\r\n      expect(mockAssessment.review).toHaveBeenCalledWith('reviewer-123', 'Needs more analysis');\r\n      expect(mockAssessment.reject).toHaveBeenCalledWith('Needs more analysis');\r\n    });\r\n  });\r\n\r\n  describe('deleteAssessment', () => {\r\n    it('should delete an existing assessment', async () => {\r\n      assessmentRepository.findOne.mockResolvedValue(mockAssessment);\r\n      assessmentRepository.remove.mockResolvedValue(mockAssessment);\r\n\r\n      await service.deleteAssessment('123e4567-e89b-12d3-a456-426614174000', 'user-123');\r\n\r\n      expect(assessmentRepository.remove).toHaveBeenCalledWith(mockAssessment);\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'delete',\r\n        'vulnerability_assessment',\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        expect.objectContaining({\r\n          vulnerabilityId: mockAssessment.vulnerabilityId,\r\n          assessmentType: mockAssessment.assessmentType,\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should throw NotFoundException when assessment not found', async () => {\r\n      assessmentRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.deleteAssessment('non-existent-id', 'user-123')\r\n      ).rejects.toThrow('Assessment not found');\r\n    });\r\n  });\r\n});\r\n"], "version": 3}