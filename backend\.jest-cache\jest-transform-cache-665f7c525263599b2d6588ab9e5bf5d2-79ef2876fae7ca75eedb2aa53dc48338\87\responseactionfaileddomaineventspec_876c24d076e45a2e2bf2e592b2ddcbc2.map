{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-failed.domain-event.spec.ts", "mappings": ";;AAAA,gGAAwH;AACxH,gEAA8D;AAC9D,mEAA0D;AAE1D,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;IAC/C,IAAI,WAA2B,CAAC;IAChC,IAAI,SAAwC,CAAC;IAC7C,IAAI,KAAsC,CAAC;IAE3C,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;QACxC,SAAS,GAAG;YACV,UAAU,EAAE,6BAAU,CAAC,QAAQ;YAC/B,KAAK,EAAE,8CAA8C;YACrD,UAAU,EAAE,mBAAmB;YAC/B,QAAQ,EAAE,IAAI,IAAI,EAAE;YACpB,UAAU,EAAE,CAAC;YACb,QAAQ,EAAE,IAAI;SACf,CAAC;QACF,KAAK,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,qEAA+B,CAAC,CAAC;YAC9D,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAE,8BAAc,CAAC,QAAQ,EAAE;gBAClC,UAAU,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBAClC,YAAY,EAAE,CAAC;gBACf,aAAa,EAAE,UAAU;gBACzB,WAAW,EAAE,WAAW;gBACxB,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;aAC7B,CAAC;YAEF,MAAM,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAE/F,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACxD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAC9D,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;YAClE,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YACpE,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YAChE,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YACzE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,iBAAiB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7C,MAAM,iBAAiB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,iBAAiB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE5C,MAAM,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,UAAU,EAAE,gBAAgB;aAC7B,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,QAAQ,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAChE,GAAG,SAAS;gBACZ,UAAU,EAAE,cAAc;aAC3B,CAAC,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,UAAU,EAAE,mBAAmB;aAChC,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,eAAe,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,SAAS;aACtB,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAErD,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE5D,MAAM,aAAa,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7D,MAAM,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,WAAW;aACnC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3D,MAAM,UAAU,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,mBAAmB;aAC3C,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1D,MAAM,UAAU,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEhD,MAAM,eAAe,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,eAAe,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1D,MAAM,gBAAgB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,YAAY;aACpC,CAAC,CAAC;YACH,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3D,MAAM,mBAAmB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE9D,MAAM,cAAc,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,oBAAoB;aAC5C,CAAC,CAAC;YACH,MAAM,CAAC,cAAc,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEzD,MAAM,UAAU,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,UAAU,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElD,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,UAAU,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtD,MAAM,QAAQ,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAChE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,QAAQ;aAChC,CAAC,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,UAAU,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtD,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,MAAM,aAAa,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yEAAyE,EAAE,GAAG,EAAE;YACjF,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,qBAAqB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YACH,MAAM,CAAC,qBAAqB,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAC7E,MAAM,gBAAgB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,kBAAkB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;gBACrC,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YACH,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,aAAa,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,iBAAiB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,UAAU,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,eAAe,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,KAAK,EAAE,wCAAwC;aAChD,CAAC,CAAC;YACH,MAAM,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAEhE,MAAM,cAAc,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,KAAK,EAAE,oCAAoC;aAC5C,CAAC,CAAC;YACH,MAAM,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,KAAK,EAAE,sCAAsC;aAC9C,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,aAAa,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,KAAK,EAAE,2CAA2C;aACnD,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE5D,MAAM,SAAS,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACjE,GAAG,SAAS;gBACZ,KAAK,EAAE,iBAAiB;aACzB,CAAC,CAAC;YACH,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,KAAK,EAAE,iCAAiC;aACzC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAE/D,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,KAAK,EAAE,0BAA0B;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,KAAK,EAAE,4BAA4B;aACpC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE1D,MAAM,QAAQ,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAChE,GAAG,SAAS;gBACZ,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,cAAc,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,KAAK,EAAE,8BAA8B;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE9D,MAAM,UAAU,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,KAAK,EAAE,gCAAgC;aACxC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,KAAK,EAAE,sBAAsB;aAC9B,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,mEAAmE,EAAE,GAAG,EAAE;YAC3E,MAAM,OAAO,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,OAAO,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4CAA4C,CAAC,CAAC;YACxE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,aAAa,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,aAAa,CAAC,mBAAmB,EAAE,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,uCAAuC,CAAC,CAAC;YACnE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,OAAO,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,UAAU,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,mBAAmB,EAAE,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,eAAe,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,KAAK,EAAE,mBAAmB;aAC3B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,eAAe,CAAC,mBAAmB,EAAE,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2CAA2C,CAAC,CAAC;YACvE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,KAAK,EAAE,qBAAqB;aAC7B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,mBAAmB,EAAE,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,aAAa,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,KAAK,EAAE,qBAAqB;aAC7B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,aAAa,CAAC,mBAAmB,EAAE,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uEAAuE,EAAE,GAAG,EAAE;YAC/E,MAAM,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,WAAW,CAAC,mBAAmB,EAAE,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;YACpE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,KAAK,EAAE,2BAA2B;aACnC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,mBAAmB,EAAE,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,UAAU,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,sBAAsB,EAAE,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,aAAa,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,aAAa,CAAC,sBAAsB,EAAE,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,iBAAiB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,iBAAiB,CAAC,sBAAsB,EAAE,CAAC;YAC3D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,UAAU,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,sBAAsB,EAAE,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,oFAAoF,EAAE,GAAG,EAAE;YAC5F,MAAM,kBAAkB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,kBAAkB,CAAC,yBAAyB,EAAE,CAAC;YAClE,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+EAA+E,EAAE,GAAG,EAAE;YACvF,MAAM,UAAU,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;YACrD,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+EAA+E,EAAE,GAAG,EAAE;YACvF,MAAM,qBAAqB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;gBACtC,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,qBAAqB,CAAC,yBAAyB,EAAE,CAAC;YACrE,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yEAAyE,EAAE,GAAG,EAAE;YACjF,MAAM,iBAAiB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;gBACrC,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,iBAAiB,CAAC,yBAAyB,EAAE,CAAC;YACjE,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,aAAa,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;gBACjC,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,aAAa,CAAC,yBAAyB,EAAE,CAAC;YAC7D,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,eAAe,GAAG,KAAK,CAAC,uBAAuB,EAAE,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,UAAU,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,UAAU,CAAC,uBAAuB,EAAE,CAAC;YAC7D,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,KAAK,EAAE,qBAAqB;aAC7B,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,YAAY,CAAC,uBAAuB,EAAE,CAAC;YAC/D,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,aAAa,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,KAAK,EAAE,qBAAqB;aAC7B,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,aAAa,CAAC,uBAAuB,EAAE,CAAC;YAChE,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,uCAAuC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,KAAK,EAAE,2BAA2B;aACnC,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,YAAY,CAAC,uBAAuB,EAAE,CAAC;YAC/D,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,eAAe,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,KAAK,EAAE,mBAAmB;aAC3B,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,eAAe,CAAC,uBAAuB,EAAE,CAAC;YAClE,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,WAAW,CAAC,uBAAuB,EAAE,CAAC;YAC9D,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,eAAe,GAAG,KAAK,CAAC,uBAAuB,EAAE,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,OAAO,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAE1C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,UAAU,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,mBAAmB;aAChC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,iBAAiB,EAAE,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAEpD,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAChE,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC/D,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACzD,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9C,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBACxC,eAAe,EAAE,MAAM;gBACvB,eAAe,EAAE,SAAS;gBAC1B,kBAAkB,EAAE,IAAI;gBACxB,cAAc,EAAE,KAAK;gBACrB,kBAAkB,EAAE,IAAI;gBACxB,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,eAAe,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,SAAS;aACtB,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,aAAa,EAAE,CAAC;YACnD,MAAM,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,UAAU,EAAE,gBAA8B;aAC3C,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7D,MAAM,CAAC,YAAY,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,eAAe,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,KAAK,EAAE,EAAE;aACV,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,cAAc,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,KAAK,EAAE,mBAAmB;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-failed.domain-event.spec.ts"], "sourcesContent": ["import { ResponseActionFailedDomainEvent, ResponseActionFailedEventData } from '../response-action-failed.domain-event';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { ActionType } from '../../enums/action-type.enum';\r\n\r\ndescribe('ResponseActionFailedDomainEvent', () => {\r\n  let aggregateId: UniqueEntityId;\r\n  let eventData: ResponseActionFailedEventData;\r\n  let event: ResponseActionFailedDomainEvent;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.generate();\r\n    eventData = {\r\n      actionType: ActionType.BLOCK_IP,\r\n      error: 'Network timeout while connecting to firewall',\r\n      executedBy: 'system@automation',\r\n      failedAt: new Date(),\r\n      retryCount: 1,\r\n      canRetry: true,\r\n    };\r\n    event = new ResponseActionFailedDomainEvent(aggregateId, eventData);\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create event with required data', () => {\r\n      expect(event).toBeInstanceOf(ResponseActionFailedDomainEvent);\r\n      expect(event.aggregateId).toBe(aggregateId);\r\n      expect(event.eventData).toBe(eventData);\r\n      expect(event.occurredOn).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should create event with custom options', () => {\r\n      const customOptions = {\r\n        eventId: UniqueEntityId.generate(),\r\n        occurredOn: new Date('2023-01-01'),\r\n        eventVersion: 2,\r\n        correlationId: 'corr-123',\r\n        causationId: 'cause-456',\r\n        metadata: { source: 'test' },\r\n      };\r\n\r\n      const customEvent = new ResponseActionFailedDomainEvent(aggregateId, eventData, customOptions);\r\n\r\n      expect(customEvent.eventId).toBe(customOptions.eventId);\r\n      expect(customEvent.occurredOn).toBe(customOptions.occurredOn);\r\n      expect(customEvent.eventVersion).toBe(customOptions.eventVersion);\r\n      expect(customEvent.correlationId).toBe(customOptions.correlationId);\r\n      expect(customEvent.causationId).toBe(customOptions.causationId);\r\n      expect(customEvent.metadata).toEqual(customOptions.metadata);\r\n    });\r\n  });\r\n\r\n  describe('getters', () => {\r\n    it('should provide access to event data properties', () => {\r\n      expect(event.actionType).toBe(ActionType.BLOCK_IP);\r\n      expect(event.error).toBe('Network timeout while connecting to firewall');\r\n      expect(event.executedBy).toBe('system@automation');\r\n      expect(event.failedAt).toBe(eventData.failedAt);\r\n      expect(event.retryCount).toBe(1);\r\n      expect(event.canRetry).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('failure analysis', () => {\r\n    it('should identify first-time failures', () => {\r\n      const firstFailureEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        retryCount: 0,\r\n      });\r\n      expect(firstFailureEvent.isFirstFailure()).toBe(true);\r\n      expect(event.isFirstFailure()).toBe(false);\r\n    });\r\n\r\n    it('should identify repeated failures', () => {\r\n      expect(event.isRepeatedFailure()).toBe(true);\r\n\r\n      const firstFailureEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        retryCount: 0,\r\n      });\r\n      expect(firstFailureEvent.isRepeatedFailure()).toBe(false);\r\n    });\r\n\r\n    it('should identify final failures', () => {\r\n      const finalFailureEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        canRetry: false,\r\n      });\r\n      expect(finalFailureEvent.isFinalFailure()).toBe(true);\r\n      expect(event.isFinalFailure()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('execution type analysis', () => {\r\n    it('should identify automated failures', () => {\r\n      expect(event.isAutomatedFailure()).toBe(true);\r\n      expect(event.isManualFailure()).toBe(false);\r\n\r\n      const systemEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        executedBy: 'automation-bot',\r\n      });\r\n      expect(systemEvent.isAutomatedFailure()).toBe(true);\r\n\r\n      const botEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        executedBy: 'security-bot',\r\n      });\r\n      expect(botEvent.isAutomatedFailure()).toBe(true);\r\n    });\r\n\r\n    it('should identify manual failures', () => {\r\n      const manualEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        executedBy: '<EMAIL>',\r\n      });\r\n\r\n      expect(manualEvent.isAutomatedFailure()).toBe(false);\r\n      expect(manualEvent.isManualFailure()).toBe(true);\r\n    });\r\n\r\n    it('should handle missing executedBy', () => {\r\n      const noExecutorEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        executedBy: undefined,\r\n      });\r\n\r\n      expect(noExecutorEvent.isAutomatedFailure()).toBe(false);\r\n      expect(noExecutorEvent.isManualFailure()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('action type classification', () => {\r\n    it('should identify security-critical failures', () => {\r\n      expect(event.isSecurityCriticalFailure()).toBe(true);\r\n\r\n      const isolateEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.ISOLATE_SYSTEM,\r\n      });\r\n      expect(isolateEvent.isSecurityCriticalFailure()).toBe(true);\r\n\r\n      const shutdownEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SHUTDOWN_SYSTEM,\r\n      });\r\n      expect(shutdownEvent.isSecurityCriticalFailure()).toBe(true);\r\n\r\n      const deleteEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.DELETE_FILE,\r\n      });\r\n      expect(deleteEvent.isSecurityCriticalFailure()).toBe(true);\r\n\r\n      const patchEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.PATCH_VULNERABILITY,\r\n      });\r\n      expect(patchEvent.isSecurityCriticalFailure()).toBe(true);\r\n\r\n      const emailEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(emailEvent.isSecurityCriticalFailure()).toBe(false);\r\n    });\r\n\r\n    it('should identify containment failures', () => {\r\n      expect(event.isContainmentFailure()).toBe(true);\r\n\r\n      const quarantineEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.QUARANTINE_FILE,\r\n      });\r\n      expect(quarantineEvent.isContainmentFailure()).toBe(true);\r\n\r\n      const blockDomainEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.BLOCK_DOMAIN,\r\n      });\r\n      expect(blockDomainEvent.isContainmentFailure()).toBe(true);\r\n\r\n      const disableAccountEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.DISABLE_ACCOUNT,\r\n      });\r\n      expect(disableAccountEvent.isContainmentFailure()).toBe(true);\r\n\r\n      const terminateEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.TERMINATE_CONNECTION,\r\n      });\r\n      expect(terminateEvent.isContainmentFailure()).toBe(true);\r\n\r\n      const emailEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(emailEvent.isContainmentFailure()).toBe(false);\r\n    });\r\n\r\n    it('should identify recovery failures', () => {\r\n      const backupEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n      expect(backupEvent.isRecoveryFailure()).toBe(true);\r\n\r\n      const rebuildEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.REBUILD_SYSTEM,\r\n      });\r\n      expect(rebuildEvent.isRecoveryFailure()).toBe(true);\r\n\r\n      const enableEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.ENABLE_SERVICE,\r\n      });\r\n      expect(enableEvent.isRecoveryFailure()).toBe(true);\r\n\r\n      const resetEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESET_PASSWORD,\r\n      });\r\n      expect(resetEvent.isRecoveryFailure()).toBe(true);\r\n\r\n      expect(event.isRecoveryFailure()).toBe(false);\r\n    });\r\n\r\n    it('should identify notification failures', () => {\r\n      const emailEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(emailEvent.isNotificationFailure()).toBe(true);\r\n\r\n      const smsEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_SMS,\r\n      });\r\n      expect(smsEvent.isNotificationFailure()).toBe(true);\r\n\r\n      const slackEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_SLACK,\r\n      });\r\n      expect(slackEvent.isNotificationFailure()).toBe(true);\r\n\r\n      const webhookEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.TRIGGER_WEBHOOK,\r\n      });\r\n      expect(webhookEvent.isNotificationFailure()).toBe(true);\r\n\r\n      expect(event.isNotificationFailure()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('failure severity assessment', () => {\r\n    it('should assess critical severity for final security-critical failures', () => {\r\n      const criticalEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        canRetry: false,\r\n      });\r\n      expect(criticalEvent.getFailureSeverity()).toBe('critical');\r\n    });\r\n\r\n    it('should assess high severity for security-critical failures with retries', () => {\r\n      expect(event.getFailureSeverity()).toBe('high');\r\n    });\r\n\r\n    it('should assess high severity for final containment failures', () => {\r\n      const finalContainmentEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        canRetry: false,\r\n      });\r\n      expect(finalContainmentEvent.getFailureSeverity()).toBe('critical');\r\n    });\r\n\r\n    it('should assess medium severity for containment failures with retries', () => {\r\n      const containmentEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.QUARANTINE_FILE,\r\n      });\r\n      expect(containmentEvent.getFailureSeverity()).toBe('high');\r\n    });\r\n\r\n    it('should assess high severity for final recovery failures', () => {\r\n      const finalRecoveryEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n        canRetry: false,\r\n      });\r\n      expect(finalRecoveryEvent.getFailureSeverity()).toBe('high');\r\n    });\r\n\r\n    it('should assess medium severity for recovery failures with retries', () => {\r\n      const recoveryEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n      expect(recoveryEvent.getFailureSeverity()).toBe('medium');\r\n    });\r\n\r\n    it('should assess medium severity for notification failures', () => {\r\n      const notificationEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(notificationEvent.getFailureSeverity()).toBe('medium');\r\n    });\r\n\r\n    it('should assess low severity for other failures', () => {\r\n      const otherEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.EXECUTE_SCRIPT,\r\n      });\r\n      expect(otherEvent.getFailureSeverity()).toBe('low');\r\n    });\r\n  });\r\n\r\n  describe('failure categorization', () => {\r\n    it('should categorize permission failures', () => {\r\n      const permissionEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Permission denied: unauthorized access',\r\n      });\r\n      expect(permissionEvent.getFailureCategory()).toBe('permission');\r\n\r\n      const forbiddenEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Forbidden: insufficient privileges',\r\n      });\r\n      expect(forbiddenEvent.getFailureCategory()).toBe('permission');\r\n    });\r\n\r\n    it('should categorize timeout failures', () => {\r\n      const timeoutEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Operation timed out after 30 seconds',\r\n      });\r\n      expect(timeoutEvent.getFailureCategory()).toBe('timeout');\r\n    });\r\n\r\n    it('should categorize resource failures', () => {\r\n      const resourceEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Insufficient memory to complete operation',\r\n      });\r\n      expect(resourceEvent.getFailureCategory()).toBe('resource');\r\n\r\n      const diskEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Disk space full',\r\n      });\r\n      expect(diskEvent.getFailureCategory()).toBe('resource');\r\n    });\r\n\r\n    it('should categorize configuration failures', () => {\r\n      const configEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Invalid configuration parameter',\r\n      });\r\n      expect(configEvent.getFailureCategory()).toBe('configuration');\r\n\r\n      const settingEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Missing required setting',\r\n      });\r\n      expect(settingEvent.getFailureCategory()).toBe('configuration');\r\n    });\r\n\r\n    it('should categorize network failures', () => {\r\n      const networkEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Network connection refused',\r\n      });\r\n      expect(networkEvent.getFailureCategory()).toBe('network');\r\n\r\n      const dnsEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'DNS resolution failed',\r\n      });\r\n      expect(dnsEvent.getFailureCategory()).toBe('network');\r\n    });\r\n\r\n    it('should categorize technical failures', () => {\r\n      const exceptionEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Unhandled exception occurred',\r\n      });\r\n      expect(exceptionEvent.getFailureCategory()).toBe('technical');\r\n\r\n      const errorEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'System error: operation failed',\r\n      });\r\n      expect(errorEvent.getFailureCategory()).toBe('technical');\r\n    });\r\n\r\n    it('should categorize unknown failures', () => {\r\n      const unknownEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Something went wrong',\r\n      });\r\n      expect(unknownEvent.getFailureCategory()).toBe('unknown');\r\n    });\r\n  });\r\n\r\n  describe('immediate action recommendations', () => {\r\n    it('should recommend immediate actions for security-critical failures', () => {\r\n      const actions = event.getImmediateActions();\r\n      expect(actions).toContain('Escalate to incident response team');\r\n      expect(actions).toContain('Assess security impact of failure');\r\n      expect(actions).toContain('Consider manual fallback procedures');\r\n    });\r\n\r\n    it('should recommend immediate actions for containment failures', () => {\r\n      const actions = event.getImmediateActions();\r\n      expect(actions).toContain('Implement alternative containment measures');\r\n      expect(actions).toContain('Monitor for threat spread');\r\n      expect(actions).toContain('Assess containment gap impact');\r\n    });\r\n\r\n    it('should recommend immediate actions for recovery failures', () => {\r\n      const recoveryEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n\r\n      const actions = recoveryEvent.getImmediateActions();\r\n      expect(actions).toContain('Assess service impact');\r\n      expect(actions).toContain('Consider alternative recovery methods');\r\n      expect(actions).toContain('Notify affected stakeholders');\r\n    });\r\n\r\n    it('should recommend retry analysis for retryable failures', () => {\r\n      const actions = event.getImmediateActions();\r\n      expect(actions).toContain('Analyze failure cause before retry');\r\n      expect(actions).toContain('Adjust retry parameters if needed');\r\n    });\r\n\r\n    it('should recommend investigation for non-retryable failures', () => {\r\n      const finalEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        canRetry: false,\r\n      });\r\n\r\n      const actions = finalEvent.getImmediateActions();\r\n      expect(actions).toContain('Investigate root cause of failure');\r\n      expect(actions).toContain('Consider alternative action approaches');\r\n    });\r\n\r\n    it('should recommend category-specific actions for permission failures', () => {\r\n      const permissionEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Permission denied',\r\n      });\r\n\r\n      const actions = permissionEvent.getImmediateActions();\r\n      expect(actions).toContain('Verify action permissions and credentials');\r\n      expect(actions).toContain('Check role-based access controls');\r\n    });\r\n\r\n    it('should recommend category-specific actions for timeout failures', () => {\r\n      const timeoutEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Operation timed out',\r\n      });\r\n\r\n      const actions = timeoutEvent.getImmediateActions();\r\n      expect(actions).toContain('Check system performance and load');\r\n      expect(actions).toContain('Consider increasing timeout limits');\r\n    });\r\n\r\n    it('should recommend category-specific actions for resource failures', () => {\r\n      const resourceEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Insufficient memory',\r\n      });\r\n\r\n      const actions = resourceEvent.getImmediateActions();\r\n      expect(actions).toContain('Check system resource availability');\r\n      expect(actions).toContain('Free up resources if possible');\r\n    });\r\n\r\n    it('should recommend category-specific actions for configuration failures', () => {\r\n      const configEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Invalid configuration',\r\n      });\r\n\r\n      const actions = configEvent.getImmediateActions();\r\n      expect(actions).toContain('Verify action configuration parameters');\r\n      expect(actions).toContain('Check system configuration settings');\r\n    });\r\n\r\n    it('should recommend category-specific actions for network failures', () => {\r\n      const networkEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Network connection failed',\r\n      });\r\n\r\n      const actions = networkEvent.getImmediateActions();\r\n      expect(actions).toContain('Check network connectivity');\r\n      expect(actions).toContain('Verify network configuration');\r\n    });\r\n  });\r\n\r\n  describe('notification targets', () => {\r\n    it('should identify targets for all failures', () => {\r\n      const targets = event.getNotificationTargets();\r\n      expect(targets).toContain('action-requestor');\r\n    });\r\n\r\n    it('should identify targets for security-critical failures', () => {\r\n      const targets = event.getNotificationTargets();\r\n      expect(targets).toContain('security-team');\r\n      expect(targets).toContain('incident-response-team');\r\n    });\r\n\r\n    it('should identify targets for final security-critical failures', () => {\r\n      const finalEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        canRetry: false,\r\n      });\r\n\r\n      const targets = finalEvent.getNotificationTargets();\r\n      expect(targets).toContain('security-managers');\r\n      expect(targets).toContain('on-call-engineers');\r\n    });\r\n\r\n    it('should identify targets for containment failures', () => {\r\n      const targets = event.getNotificationTargets();\r\n      expect(targets).toContain('containment-specialists');\r\n      expect(targets).toContain('security-analysts');\r\n    });\r\n\r\n    it('should identify targets for recovery failures', () => {\r\n      const recoveryEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n\r\n      const targets = recoveryEvent.getNotificationTargets();\r\n      expect(targets).toContain('recovery-team');\r\n      expect(targets).toContain('service-owners');\r\n    });\r\n\r\n    it('should identify targets for notification failures', () => {\r\n      const notificationEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n\r\n      const targets = notificationEvent.getNotificationTargets();\r\n      expect(targets).toContain('communication-team');\r\n    });\r\n\r\n    it('should identify targets for repeated failures', () => {\r\n      const targets = event.getNotificationTargets();\r\n      expect(targets).toContain('technical-support');\r\n    });\r\n\r\n    it('should identify targets for final failures', () => {\r\n      const finalEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        canRetry: false,\r\n      });\r\n\r\n      const targets = finalEvent.getNotificationTargets();\r\n      expect(targets).toContain('escalation-team');\r\n    });\r\n  });\r\n\r\n  describe('escalation requirements', () => {\r\n    it('should require immediate executive escalation for final security-critical failures', () => {\r\n      const finalCriticalEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        canRetry: false,\r\n      });\r\n\r\n      const escalation = finalCriticalEvent.getEscalationRequirements();\r\n      expect(escalation.immediate).toBe(true);\r\n      expect(escalation.level).toBe('executive');\r\n      expect(escalation.reason).toBe('Critical security action failed with no retry options');\r\n    });\r\n\r\n    it('should require immediate management escalation for security-critical failures', () => {\r\n      const escalation = event.getEscalationRequirements();\r\n      expect(escalation.immediate).toBe(true);\r\n      expect(escalation.level).toBe('management');\r\n      expect(escalation.reason).toBe('Critical security action failed');\r\n    });\r\n\r\n    it('should require immediate management escalation for final containment failures', () => {\r\n      const finalContainmentEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.QUARANTINE_FILE,\r\n        canRetry: false,\r\n      });\r\n\r\n      const escalation = finalContainmentEvent.getEscalationRequirements();\r\n      expect(escalation.immediate).toBe(true);\r\n      expect(escalation.level).toBe('executive');\r\n      expect(escalation.reason).toBe('Critical security action failed with no retry options');\r\n    });\r\n\r\n    it('should require technical escalation for repeated high-severity failures', () => {\r\n      const repeatedHighEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.ISOLATE_SYSTEM,\r\n        retryCount: 2,\r\n      });\r\n\r\n      const escalation = repeatedHighEvent.getEscalationRequirements();\r\n      expect(escalation.immediate).toBe(true);\r\n      expect(escalation.level).toBe('management');\r\n      expect(escalation.reason).toBe('Critical security action failed');\r\n    });\r\n\r\n    it('should require no escalation for standard failures', () => {\r\n      const standardEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n        retryCount: 0,\r\n      });\r\n\r\n      const escalation = standardEvent.getEscalationRequirements();\r\n      expect(escalation.immediate).toBe(false);\r\n      expect(escalation.level).toBe('none');\r\n      expect(escalation.reason).toBe('Standard failure handling applies');\r\n    });\r\n  });\r\n\r\n  describe('retry recommendations', () => {\r\n    it('should recommend retry for retryable failures', () => {\r\n      const recommendations = event.getRetryRecommendations();\r\n      expect(recommendations.shouldRetry).toBe(true);\r\n      expect(recommendations.delayMinutes).toBe(10);\r\n      expect(recommendations.maxRetries).toBe(2);\r\n      expect(recommendations.conditions).toContain('Verify system performance before retry');\r\n    });\r\n\r\n    it('should not recommend retry for non-retryable failures', () => {\r\n      const finalEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        canRetry: false,\r\n      });\r\n\r\n      const recommendations = finalEvent.getRetryRecommendations();\r\n      expect(recommendations.shouldRetry).toBe(false);\r\n      expect(recommendations.delayMinutes).toBe(0);\r\n      expect(recommendations.maxRetries).toBe(0);\r\n      expect(recommendations.conditions).toContain('Action cannot be retried');\r\n    });\r\n\r\n    it('should provide timeout-specific retry recommendations', () => {\r\n      const timeoutEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Operation timed out',\r\n      });\r\n\r\n      const recommendations = timeoutEvent.getRetryRecommendations();\r\n      expect(recommendations.delayMinutes).toBe(10);\r\n      expect(recommendations.maxRetries).toBe(2);\r\n      expect(recommendations.conditions).toContain('Verify system performance before retry');\r\n    });\r\n\r\n    it('should provide resource-specific retry recommendations', () => {\r\n      const resourceEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Insufficient memory',\r\n      });\r\n\r\n      const recommendations = resourceEvent.getRetryRecommendations();\r\n      expect(recommendations.delayMinutes).toBe(15);\r\n      expect(recommendations.maxRetries).toBe(2);\r\n      expect(recommendations.conditions).toContain('Ensure sufficient resources available');\r\n    });\r\n\r\n    it('should provide network-specific retry recommendations', () => {\r\n      const networkEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Network connection failed',\r\n      });\r\n\r\n      const recommendations = networkEvent.getRetryRecommendations();\r\n      expect(recommendations.delayMinutes).toBe(5);\r\n      expect(recommendations.maxRetries).toBe(3);\r\n      expect(recommendations.conditions).toContain('Verify network connectivity');\r\n    });\r\n\r\n    it('should provide permission-specific retry recommendations', () => {\r\n      const permissionEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Permission denied',\r\n      });\r\n\r\n      const recommendations = permissionEvent.getRetryRecommendations();\r\n      expect(recommendations.delayMinutes).toBe(0);\r\n      expect(recommendations.maxRetries).toBe(1);\r\n      expect(recommendations.conditions).toContain('Fix permission issues before retry');\r\n    });\r\n\r\n    it('should provide configuration-specific retry recommendations', () => {\r\n      const configEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'Invalid configuration',\r\n      });\r\n\r\n      const recommendations = configEvent.getRetryRecommendations();\r\n      expect(recommendations.delayMinutes).toBe(0);\r\n      expect(recommendations.maxRetries).toBe(1);\r\n      expect(recommendations.conditions).toContain('Correct configuration before retry');\r\n    });\r\n\r\n    it('should require approval for security-critical action retries', () => {\r\n      const recommendations = event.getRetryRecommendations();\r\n      expect(recommendations.conditions).toContain('Get approval for critical action retry');\r\n    });\r\n  });\r\n\r\n  describe('failure metrics', () => {\r\n    it('should generate comprehensive failure metrics', () => {\r\n      const metrics = event.getFailureMetrics();\r\n\r\n      expect(metrics.actionType).toBe(ActionType.BLOCK_IP);\r\n      expect(metrics.failureCategory).toBe('timeout');\r\n      expect(metrics.failureSeverity).toBe('high');\r\n      expect(metrics.retryCount).toBe(1);\r\n      expect(metrics.canRetry).toBe(true);\r\n      expect(metrics.isSecurityCritical).toBe(true);\r\n      expect(metrics.isAutomated).toBe(true);\r\n      expect(metrics.isFinalFailure).toBe(false);\r\n    });\r\n\r\n    it('should generate metrics for final failures', () => {\r\n      const finalEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        canRetry: false,\r\n        executedBy: '<EMAIL>',\r\n      });\r\n\r\n      const metrics = finalEvent.getFailureMetrics();\r\n\r\n      expect(metrics.isFinalFailure).toBe(true);\r\n      expect(metrics.isAutomated).toBe(false);\r\n      expect(metrics.failureSeverity).toBe('critical');\r\n    });\r\n  });\r\n\r\n  describe('integration event conversion', () => {\r\n    it('should convert to integration event format', () => {\r\n      const integrationEvent = event.toIntegrationEvent();\r\n\r\n      expect(integrationEvent.eventType).toBe('ResponseActionFailed');\r\n      expect(integrationEvent.action).toBe('response_action_failed');\r\n      expect(integrationEvent.resource).toBe('ResponseAction');\r\n      expect(integrationEvent.resourceId).toBe(aggregateId.toString());\r\n      expect(integrationEvent.data).toBe(eventData);\r\n      expect(integrationEvent.metadata).toEqual({\r\n        failureSeverity: 'high',\r\n        failureCategory: 'timeout',\r\n        isSecurityCritical: true,\r\n        isFinalFailure: false,\r\n        escalationRequired: true,\r\n        canRetry: true,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle events without executedBy', () => {\r\n      const noExecutorEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        executedBy: undefined,\r\n      });\r\n\r\n      expect(noExecutorEvent.executedBy).toBeUndefined();\r\n      expect(noExecutorEvent.isAutomatedFailure()).toBe(false);\r\n      expect(noExecutorEvent.isManualFailure()).toBe(false);\r\n    });\r\n\r\n    it('should handle unknown action types gracefully', () => {\r\n      const unknownEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: 'UNKNOWN_ACTION' as ActionType,\r\n      });\r\n\r\n      expect(unknownEvent.isSecurityCriticalFailure()).toBe(false);\r\n      expect(unknownEvent.isContainmentFailure()).toBe(false);\r\n      expect(unknownEvent.isRecoveryFailure()).toBe(false);\r\n      expect(unknownEvent.isNotificationFailure()).toBe(false);\r\n      expect(unknownEvent.getFailureSeverity()).toBe('low');\r\n    });\r\n\r\n    it('should handle empty error messages', () => {\r\n      const emptyErrorEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: '',\r\n      });\r\n\r\n      expect(emptyErrorEvent.getFailureCategory()).toBe('unknown');\r\n    });\r\n\r\n    it('should handle case-insensitive error categorization', () => {\r\n      const upperCaseEvent = new ResponseActionFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        error: 'PERMISSION DENIED',\r\n      });\r\n\r\n      expect(upperCaseEvent.getFailureCategory()).toBe('permission');\r\n    });\r\n  });\r\n});"], "version": 3}