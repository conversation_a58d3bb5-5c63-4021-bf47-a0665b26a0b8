4b7e6c04ec442ce4ecc1444c85d52f83
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PolicyConfiguration = exports.RuleCategory = exports.RuleParameterType = void 0;
const base_entity_1 = require("../../../../shared-kernel/domain/base-entity");
const validation_exception_1 = require("../../../../shared-kernel/exceptions/validation.exception");
var RuleParameterType;
(function (RuleParameterType) {
    RuleParameterType["STRING"] = "STRING";
    RuleParameterType["NUMBER"] = "NUMBER";
    RuleParameterType["BOOLEAN"] = "BOOLEAN";
    RuleParameterType["ARRAY"] = "ARRAY";
    RuleParameterType["OBJECT"] = "OBJECT";
    RuleParameterType["ENUM"] = "ENUM";
    RuleParameterType["IP_ADDRESS"] = "IP_ADDRESS";
    RuleParameterType["EMAIL"] = "EMAIL";
    RuleParameterType["URL"] = "URL";
})(RuleParameterType || (exports.RuleParameterType = RuleParameterType = {}));
var RuleCategory;
(function (RuleCategory) {
    RuleCategory["AUTHENTICATION"] = "AUTHENTICATION";
    RuleCategory["AUTHORIZATION"] = "AUTHORIZATION";
    RuleCategory["DATA_PROTECTION"] = "DATA_PROTECTION";
    RuleCategory["NETWORK_SECURITY"] = "NETWORK_SECURITY";
    RuleCategory["MALWARE_DETECTION"] = "MALWARE_DETECTION";
    RuleCategory["INTRUSION_DETECTION"] = "INTRUSION_DETECTION";
    RuleCategory["COMPLIANCE"] = "COMPLIANCE";
    RuleCategory["CUSTOM"] = "CUSTOM";
})(RuleCategory || (exports.RuleCategory = RuleCategory = {}));
class PolicyConfiguration extends base_entity_1.BaseEntity {
    constructor(props) {
        super(props, props.id);
        this.validateProps(props);
        this._tenantId = props.tenantId;
        this._settings = props.settings;
        this._ruleTemplates = props.ruleTemplates;
        this._createdBy = props.createdBy;
        this._lastModifiedBy = props.lastModifiedBy;
        this._version = props.version;
    }
    static create(props) {
        return new PolicyConfiguration({
            tenantId: props.tenantId,
            settings: PolicyConfiguration.getDefaultSettings(),
            ruleTemplates: PolicyConfiguration.getDefaultRuleTemplates(),
            createdBy: props.createdBy,
            lastModifiedBy: props.createdBy,
            version: '1.0.0'
        });
    }
    updateSettings(settings, modifiedBy) {
        const updatedSettings = { ...this._settings, ...settings };
        this.validateSettings(updatedSettings);
        this._settings = updatedSettings;
        this._lastModifiedBy = modifiedBy;
        this.touch();
    }
    addRuleTemplate(template, modifiedBy) {
        this.validateRuleTemplate(template);
        // Check for duplicate template IDs
        if (this._ruleTemplates.some(t => t.id === template.id)) {
            throw new validation_exception_1.ValidationException(`Rule template with ID '${template.id}' already exists`, []);
        }
        this._ruleTemplates.push(template);
        this._lastModifiedBy = modifiedBy;
        this.touch();
    }
    updateRuleTemplate(templateId, updates, modifiedBy) {
        const templateIndex = this._ruleTemplates.findIndex(t => t.id === templateId);
        if (templateIndex === -1) {
            throw new validation_exception_1.ValidationException(`Rule template with ID '${templateId}' not found`, []);
        }
        const updatedTemplate = { ...this._ruleTemplates[templateIndex], ...updates };
        this.validateRuleTemplate(updatedTemplate);
        this._ruleTemplates[templateIndex] = updatedTemplate;
        this._lastModifiedBy = modifiedBy;
        this.touch();
    }
    removeRuleTemplate(templateId, modifiedBy) {
        const templateIndex = this._ruleTemplates.findIndex(t => t.id === templateId);
        if (templateIndex === -1) {
            throw new validation_exception_1.ValidationException(`Rule template with ID '${templateId}' not found`, []);
        }
        this._ruleTemplates.splice(templateIndex, 1);
        this._lastModifiedBy = modifiedBy;
        this.touch();
    }
    getRuleTemplatesByCategory(category) {
        return this._ruleTemplates.filter(t => t.category === category);
    }
    generateRuleFromTemplate(templateId, parameters) {
        const template = this._ruleTemplates.find(t => t.id === templateId);
        if (!template) {
            throw new validation_exception_1.ValidationException(`Rule template with ID '${templateId}' not found`, []);
        }
        // Validate required parameters
        const requiredParams = template.parameters.filter(p => p.required);
        for (const param of requiredParams) {
            if (!(param.name in parameters)) {
                throw new validation_exception_1.ValidationException(`Required parameter '${param.name}' is missing`, []);
            }
        }
        // Validate parameter types and values
        for (const param of template.parameters) {
            if (param.name in parameters) {
                this.validateParameterValue(param, parameters[param.name]);
            }
        }
        // Replace template placeholders with actual values
        let ruleCondition = template.template;
        for (const [key, value] of Object.entries(parameters)) {
            const placeholder = `{{${key}}}`;
            ruleCondition = ruleCondition.replace(new RegExp(placeholder, 'g'), JSON.stringify(value));
        }
        return ruleCondition;
    }
    validateRuleCondition(condition) {
        try {
            const parsed = JSON.parse(condition);
            return this.validateConditionStructure(parsed);
        }
        catch (error) {
            return false;
        }
    }
    getMetrics() {
        // This would typically be implemented by querying actual policy data
        // For now, return placeholder metrics
        return {
            totalPolicies: 0,
            activePolicies: 0,
            totalRules: 0,
            activeRules: 0,
            evaluationsLast24h: 0,
            violationsLast24h: 0,
            averageEvaluationTime: 0,
            topViolatedRules: []
        };
    }
    validateConditionStructure(condition) {
        if (typeof condition !== 'object' || condition === null) {
            return false;
        }
        // Check for valid logical operators
        const validOperators = ['and', 'or', 'not', 'field', 'operator', 'value'];
        const keys = Object.keys(condition);
        if (keys.length === 0) {
            return false;
        }
        // Validate logical operators
        if ('and' in condition) {
            return Array.isArray(condition.and) &&
                condition.and.every((sub) => this.validateConditionStructure(sub));
        }
        if ('or' in condition) {
            return Array.isArray(condition.or) &&
                condition.or.every((sub) => this.validateConditionStructure(sub));
        }
        if ('not' in condition) {
            return this.validateConditionStructure(condition.not);
        }
        // Validate field conditions
        if ('field' in condition && 'operator' in condition && 'value' in condition) {
            const validFieldOperators = [
                'equals', 'not_equals', 'contains', 'starts_with', 'ends_with',
                'greater_than', 'less_than', 'in', 'regex'
            ];
            return typeof condition.field === 'string' &&
                validFieldOperators.includes(condition.operator);
        }
        return false;
    }
    validateParameterValue(parameter, value) {
        switch (parameter.type) {
            case RuleParameterType.STRING:
                if (typeof value !== 'string') {
                    throw new validation_exception_1.ValidationException(`Parameter '${parameter.name}' must be a string`, []);
                }
                break;
            case RuleParameterType.NUMBER:
                if (typeof value !== 'number') {
                    throw new validation_exception_1.ValidationException(`Parameter '${parameter.name}' must be a number`, []);
                }
                break;
            case RuleParameterType.BOOLEAN:
                if (typeof value !== 'boolean') {
                    throw new validation_exception_1.ValidationException(`Parameter '${parameter.name}' must be a boolean`, []);
                }
                break;
            case RuleParameterType.ARRAY:
                if (!Array.isArray(value)) {
                    throw new validation_exception_1.ValidationException(`Parameter '${parameter.name}' must be an array`, []);
                }
                break;
            case RuleParameterType.ENUM:
                if (parameter.options && !parameter.options.includes(value)) {
                    throw new validation_exception_1.ValidationException(`Parameter '${parameter.name}' must be one of: ${parameter.options.join(', ')}`, []);
                }
                break;
            case RuleParameterType.IP_ADDRESS:
                if (!this.isValidIpAddress(value)) {
                    throw new validation_exception_1.ValidationException(`Parameter '${parameter.name}' must be a valid IP address`, []);
                }
                break;
            case RuleParameterType.EMAIL:
                if (!this.isValidEmail(value)) {
                    throw new validation_exception_1.ValidationException(`Parameter '${parameter.name}' must be a valid email address`, []);
                }
                break;
            case RuleParameterType.URL:
                if (!this.isValidUrl(value)) {
                    throw new validation_exception_1.ValidationException(`Parameter '${parameter.name}' must be a valid URL`, []);
                }
                break;
        }
    }
    isValidIpAddress(value) {
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
        return ipv4Regex.test(value) || ipv6Regex.test(value);
    }
    isValidEmail(value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(value);
    }
    isValidUrl(value) {
        try {
            new URL(value);
            return true;
        }
        catch {
            return false;
        }
    }
    static getDefaultSettings() {
        return {
            enableAutomaticExecution: false,
            requireApprovalForCriticalActions: true,
            maxRulesPerPolicy: 100,
            defaultRulePriority: 50,
            auditRetentionDays: 365,
            notificationSettings: {
                enableEmailNotifications: true,
                enableSlackNotifications: false,
                enableWebhookNotifications: false,
                emailRecipients: [],
                slackChannels: [],
                webhookUrls: [],
                notificationThresholds: {
                    criticalFindings: 1,
                    highFindings: 5,
                    policyViolations: 10
                }
            },
            escalationSettings: {
                enableAutoEscalation: false,
                escalationLevels: [],
                maxEscalationLevel: 3,
                escalationTimeouts: {
                    level1Minutes: 30,
                    level2Minutes: 60,
                    level3Minutes: 120
                }
            },
            integrationSettings: {
                siemIntegration: {
                    enabled: false,
                    eventTypes: []
                },
                ticketingIntegration: {
                    enabled: false,
                    system: 'jira'
                },
                threatIntelligence: {
                    enabled: false,
                    providers: [],
                    apiKeys: {},
                    updateFrequencyHours: 24
                }
            }
        };
    }
    static getDefaultRuleTemplates() {
        return [
            {
                id: 'failed-login-attempts',
                name: 'Failed Login Attempts',
                description: 'Detect multiple failed login attempts from the same IP',
                category: RuleCategory.AUTHENTICATION,
                template: '{"and":[{"field":"eventData.eventType","operator":"equals","value":"login_failed"},{"field":"eventData.attempts","operator":"greater_than","value":{{maxAttempts}}}]}',
                parameters: [
                    {
                        name: 'maxAttempts',
                        type: RuleParameterType.NUMBER,
                        description: 'Maximum number of failed attempts before triggering',
                        required: true,
                        defaultValue: 5
                    }
                ],
                defaultPriority: 80,
                defaultSeverity: 'HIGH',
                tags: ['authentication', 'brute-force']
            },
            {
                id: 'suspicious-ip-access',
                name: 'Suspicious IP Access',
                description: 'Detect access from known malicious IP addresses',
                category: RuleCategory.NETWORK_SECURITY,
                template: '{"field":"systemContext.sourceIp","operator":"in","value":{{suspiciousIps}}}',
                parameters: [
                    {
                        name: 'suspiciousIps',
                        type: RuleParameterType.ARRAY,
                        description: 'List of suspicious IP addresses',
                        required: true,
                        defaultValue: []
                    }
                ],
                defaultPriority: 90,
                defaultSeverity: 'CRITICAL',
                tags: ['network', 'threat-intelligence']
            }
        ];
    }
    validateProps(props) {
        if (!props.tenantId) {
            throw new validation_exception_1.ValidationException('Tenant ID is required', []);
        }
        if (!props.createdBy) {
            throw new validation_exception_1.ValidationException('Created by user ID is required', []);
        }
        if (!props.settings) {
            throw new validation_exception_1.ValidationException('Settings are required', []);
        }
        this.validateSettings(props.settings);
        if (!Array.isArray(props.ruleTemplates)) {
            throw new validation_exception_1.ValidationException('Rule templates must be an array', []);
        }
        props.ruleTemplates.forEach((template) => this.validateRuleTemplate(template));
    }
    validateSettings(settings) {
        if (typeof settings.enableAutomaticExecution !== 'boolean') {
            throw new validation_exception_1.ValidationException('enableAutomaticExecution must be a boolean', []);
        }
        if (typeof settings.maxRulesPerPolicy !== 'number' || settings.maxRulesPerPolicy <= 0) {
            throw new validation_exception_1.ValidationException('maxRulesPerPolicy must be a positive number', []);
        }
        if (typeof settings.auditRetentionDays !== 'number' || settings.auditRetentionDays <= 0) {
            throw new validation_exception_1.ValidationException('auditRetentionDays must be a positive number', []);
        }
    }
    validateRuleTemplate(template) {
        if (!template.id || template.id.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Rule template ID is required', []);
        }
        if (!template.name || template.name.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Rule template name is required', []);
        }
        if (!Object.values(RuleCategory).includes(template.category)) {
            throw new validation_exception_1.ValidationException('Valid rule category is required', []);
        }
        if (!template.template || template.template.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Rule template condition is required', []);
        }
        if (!Array.isArray(template.parameters)) {
            throw new validation_exception_1.ValidationException('Rule template parameters must be an array', []);
        }
        // Basic validation that the template is not empty and looks like it could be JSON
        if (!template.template.trim().startsWith('{') && !template.template.trim().startsWith('[')) {
            // Only validate as JSON if it's clearly meant to be JSON (starts with { or [)
            // and doesn't contain placeholders
            if (!template.template.includes('{{')) {
                try {
                    JSON.parse(template.template);
                }
                catch (error) {
                    throw new validation_exception_1.ValidationException('Rule template condition must be valid JSON', []);
                }
            }
        }
        template.parameters.forEach(param => {
            if (!param.name || param.name.trim().length === 0) {
                throw new validation_exception_1.ValidationException('Parameter name is required', []);
            }
            if (!Object.values(RuleParameterType).includes(param.type)) {
                throw new validation_exception_1.ValidationException('Valid parameter type is required', []);
            }
        });
    }
    touch() {
        // Update the last modified timestamp
        // This would typically update an updatedAt field if we had one
    }
    // Getters
    get tenantId() { return this._tenantId; }
    get settings() { return { ...this._settings }; }
    get ruleTemplates() { return [...this._ruleTemplates]; }
    get createdBy() { return this._createdBy; }
    get lastModifiedBy() { return this._lastModifiedBy; }
    get version() { return this._version; }
}
exports.PolicyConfiguration = PolicyConfiguration;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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