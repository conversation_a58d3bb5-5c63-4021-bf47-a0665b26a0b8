{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\tests\\integration\\dashboard-analytics.integration.spec.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAAsD;AAEtD,6CAAgD;AAChD,2CAA8C;AAC9C,yDAA2D;AAC3D,uCAA0C;AAC1C,mDAAqC;AAErC,6CAAqD;AACrD,qCAAwC;AACxC,+CAAkD;AAClD,2GAAsG;AACtG,mGAA8F;AAC9F,kGAA6F;AAC7F,0FAAqF;AACrF,kGAA6F;AAC7F,0GAAoG;AACpG,8GAAwG;AACxG,4GAAgG;AAChG,gGAAoF;AACpF,kGAAsF;AAEtF;;;;;;;;;;GAUG;AACH,QAAQ,CAAC,+CAA+C,EAAE,GAAG,EAAE;IAC7D,IAAI,GAAqB,CAAC;IAC1B,IAAI,gBAA8C,CAAC;IACnD,IAAI,gBAA0C,CAAC;IAC/C,IAAI,4BAA0D,CAAC;IAC/D,IAAI,aAA8C,CAAC;IACnD,IAAI,WAA8C,CAAC;IACnD,IAAI,wBAAgE,CAAC;IACrE,IAAI,sBAAwD,CAAC;IAC7D,IAAI,mBAAsD,CAAC;IAE3D,MAAM,QAAQ,GAAG;QACf,EAAE,EAAE,eAAe;QACnB,KAAK,EAAE,kBAAkB;QACzB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,WAAW;KAClB,CAAC;IAEF,MAAM,SAAS,GAAG,gBAAgB,CAAC;IAEnC,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,uBAAa,CAAC,OAAO,CAAC;oBACpB,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,WAAW;oBAC7C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,eAAe;oBACrD,QAAQ,EAAE;wBACR,gEAA0B;wBAC1B,oDAAoB;wBACpB,sDAAqB;qBACtB;oBACD,WAAW,EAAE,IAAI;oBACjB,UAAU,EAAE,IAAI;iBACjB,CAAC;gBACF,uBAAa,CAAC,UAAU,CAAC;oBACvB,gEAA0B;oBAC1B,oDAAoB;oBACpB,sDAAqB;iBACtB,CAAC;gBACF,kCAAkB,CAAC,OAAO,EAAE;gBAC5B,iBAAU,CAAC,OAAO,CAAC;oBACjB,KAAK,EAAE;wBACL,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,WAAW;wBAChD,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,IAAI;wBACnD,EAAE,EAAE,CAAC;qBACN;iBACF,CAAC;gBACF,yBAAc,CAAC,QAAQ,CAAC,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC;gBACnD,eAAS,CAAC,QAAQ,CAAC;oBACjB,MAAM,EAAE,aAAa;oBACrB,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;iBACjC,CAAC;aACH;YACD,WAAW,EAAE;gBACX,mEAA+B;gBAC/B,2DAA2B;aAC5B;YACD,SAAS,EAAE;gBACT,6DAA4B;gBAC5B,qDAAwB;gBACxB,6DAA4B;gBAC5B,oEAA+B;gBAC/B,wEAAiC;gBACjC,4BAA4B;gBAC5B;oBACE,OAAO,EAAE,cAAc;oBACvB,QAAQ,EAAE;wBACR,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;qBAC7C;iBACF;gBACD;oBACE,OAAO,EAAE,YAAY;oBACrB,QAAQ,EAAE;wBACR,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;qBAC7C;iBACF;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,wBAAwB;QACxB,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAA+B,6DAA4B,CAAC,CAAC;QACjG,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAA2B,qDAAwB,CAAC,CAAC;QACzF,4BAA4B,GAAG,aAAa,CAAC,GAAG,CAA+B,6DAA4B,CAAC,CAAC;QAC7G,aAAa,GAAG,aAAa,CAAC,GAAG,CAAkC,oEAA+B,CAAC,CAAC;QACpG,WAAW,GAAG,aAAa,CAAC,GAAG,CAAoC,wEAAiC,CAAC,CAAC;QAEtG,2BAA2B;QAC3B,wBAAwB,GAAG,aAAa,CAAC,GAAG,CAC1C,IAAA,4BAAkB,EAAC,gEAA0B,CAAC,CAC/C,CAAC;QACF,sBAAsB,GAAG,aAAa,CAAC,GAAG,CACxC,IAAA,4BAAkB,EAAC,oDAAoB,CAAC,CACzC,CAAC;QACF,mBAAmB,GAAG,aAAa,CAAC,GAAG,CACrC,IAAA,4BAAkB,EAAC,sDAAqB,CAAC,CAC1C,CAAC;QAEF,iBAAiB;QACjB,MAAM,YAAY,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,+BAA+B;IACjC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACvC,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;gBAC9D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,qBAAqB,CAAC;qBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,KAAK,CAAC;oBACL,SAAS,EAAE,KAAK;oBAChB,iBAAiB,EAAE,IAAI;oBACvB,aAAa,EAAE,IAAI;iBACpB,CAAC;qBACD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC/D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;gBACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;gBAE5D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC9C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE1D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;gBACnD,MAAM,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;gBAEpD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;oBACnC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;yBAChD,GAAG,CAAC,qBAAqB,CAAC;yBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;yBAC3C,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC;yBACpB,MAAM,CAAC,GAAG,CAAC,CAAC;oBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAChD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC9C,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;gBACvD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,qBAAqB,CAAC;qBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,KAAK,CAAC;oBACL,SAAS,EAAE,aAAa;oBACxB,QAAQ,EAAE,aAAa;oBACvB,SAAS,EAAE,KAAK;iBACjB,CAAC;qBACD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;gBACpE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;YACxC,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;gBACzD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,sBAAsB,CAAC;qBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,KAAK,CAAC;oBACL,OAAO,EAAE,sBAAsB;oBAC/B,eAAe,EAAE,EAAE;iBACpB,CAAC;qBACD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC/C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;gBAEhD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;gBAClE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,sBAAsB,CAAC;qBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,KAAK,CAAC,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC;qBAChC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;gBACjD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;gBACvD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,mBAAmB,CAAC;qBACxB,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,KAAK,CAAC;oBACL,cAAc,EAAE,IAAI;oBACpB,SAAS,EAAE,KAAK;iBACjB,CAAC;qBACD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC3D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;gBAE5D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC9C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE1D,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;oBACpC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;oBACtC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;oBAC5C,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;gBACxC,CAAC,CAAC,CAAC;gBAEH,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;gBAClD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,mBAAmB,CAAC;qBACxB,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,KAAK,CAAC,EAAE,sBAAsB,EAAE,IAAI,EAAE,CAAC;qBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;gBACpD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEhE,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;oBACzC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;oBAClC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;oBAC/B,MAAM,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxE,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;YACnC,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;gBACtD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,iBAAiB,CAAC;qBACtB,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,KAAK,CAAC;oBACL,SAAS,EAAE,KAAK;oBAChB,mBAAmB,EAAE,IAAI;oBACzB,OAAO,EAAE,UAAU;iBACpB,CAAC;qBACD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;gBACtD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,WAAW,EAAE,CAAC;gBAEnE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC9C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE1D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;gBACjD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACnE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;gBAC9C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,iBAAiB,CAAC;qBACtB,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,KAAK,CAAC;oBACL,aAAa,EAAE,IAAI;oBACnB,YAAY,EAAE,SAAS;iBACxB,CAAC;qBACD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;gBACrD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;gBACjD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;gBACrD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,QAAQ,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACzD,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;gBAC1D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,uCAAuC,CAAC;qBAC5C,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,KAAK,CAAC;oBACL,UAAU,EAAE,yBAAyB;oBACrC,OAAO,EAAE,sCAAsC;oBAC/C,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,UAAU;iBACpB,CAAC;qBACD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;gBAC5E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,mBAAmB,EAAE,cAAc,CAAC,CAAC,CAAC;gBACtF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBACzC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAErD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;gBACjD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;gBACjD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;gBAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;oBAC7B,QAAQ,EAAE,OAAO;oBACjB,MAAM,EAAE,WAAW;oBACnB,gBAAgB,EAAE,UAAU;iBAC7B,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,uCAAuC,CAAC;qBAC5C,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,KAAK,CAAC;oBACL,UAAU,EAAE,kBAAkB;oBAC9B,OAAO,EAAE,oBAAoB;oBAC7B,OAAO;iBACR,CAAC;qBACD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBACzC,+BAA+B;gBAC/B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBAChC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACtC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;YACnD,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;gBACpD,MAAM,eAAe,GAAG;oBACtB,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,aAAa,CAAC;oBAClD,SAAS,EAAE,eAAe;oBAC1B,eAAe,EAAE,KAAK;oBACtB,cAAc,EAAE,KAAK;oBACrB,0BAA0B,EAAE,IAAI;oBAChC,aAAa,EAAE,IAAI;iBACpB,CAAC;gBAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,IAAI,CAAC,gCAAgC,CAAC;qBACtC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,IAAI,CAAC,eAAe,CAAC;qBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;gBACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;gBACxD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;gBAEtD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;gBACrD,MAAM,UAAU,GAAG,CAAC,mBAAmB,EAAE,eAAe,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;gBAErF,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;oBACnC,MAAM,KAAK,GAAG;wBACZ,MAAM,EAAE,MAAM;wBACd,SAAS;wBACT,eAAe,EAAE,KAAK;wBACtB,cAAc,EAAE,IAAI;qBACrB,CAAC;oBAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;yBAChD,IAAI,CAAC,gCAAgC,CAAC;yBACtC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;yBAC3C,IAAI,CAAC,KAAK,CAAC;yBACX,MAAM,CAAC,GAAG,CAAC,CAAC;oBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uCAAuC,EAAE,GAAG,EAAE;YACrD,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;gBACtD,MAAM,WAAW,GAAG;oBAClB,SAAS,EAAE,KAAK;oBAChB,KAAK,EAAE;wBACL,GAAG,EAAE,mHAAmH;qBACzH;oBACD,aAAa,EAAE;wBACb,IAAI,EAAE,WAAW;wBACjB,KAAK,EAAE,yCAAyC;wBAChD,KAAK,EAAE,UAAU;wBACjB,KAAK,EAAE,OAAO;qBACf;iBACF,CAAC;gBAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,IAAI,CAAC,kCAAkC,CAAC;qBACxC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,IAAI,CAAC,WAAW,CAAC;qBACjB,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC5C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;gBAClD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;gBACnD,MAAM,WAAW,GAAG;oBAClB,SAAS,EAAE,gBAAgB;oBAC3B,KAAK,EAAE;wBACL,UAAU,EAAE,eAAe;wBAC3B,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;wBACnC,OAAO,EAAE,CAAC,OAAO,EAAE,mBAAmB,CAAC;wBACvC,OAAO,EAAE;4BACP,EAAE,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;4BACrD,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE;yBACxD;qBACF;oBACD,aAAa,EAAE;wBACb,IAAI,EAAE,YAAY;wBAClB,KAAK,EAAE,6BAA6B;qBACrC;iBACF,CAAC;gBAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,IAAI,CAAC,kCAAkC,CAAC;qBACxC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,IAAI,CAAC,WAAW,CAAC;qBACjB,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;YACnD,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;gBAC3D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,iCAAiC,CAAC;qBACtC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,KAAK,CAAC;oBACL,SAAS,EAAE,+CAA+C;oBAC1D,SAAS,EAAE,KAAK;oBAChB,MAAM,EAAE,SAAS;iBAClB,CAAC;qBACD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;gBACtD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC5D,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;gBAC5D,MAAM,OAAO,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;gBAEnD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;oBAC7B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;yBAChD,GAAG,CAAC,iCAAiC,CAAC;yBACtC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;yBAC3C,KAAK,CAAC;wBACL,SAAS,EAAE,oBAAoB;wBAC/B,MAAM;qBACP,CAAC;yBACD,MAAM,CAAC,GAAG,CAAC,CAAC;oBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;gBACxD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;YACjD,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;gBAC/D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,+BAA+B,CAAC;qBACpC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,KAAK,CAAC;oBACL,OAAO,EAAE,+BAA+B;oBACxC,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,QAAQ;oBACrB,SAAS,EAAE,kBAAkB;iBAC9B,CAAC;qBACD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC9C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;gBAC1D,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAEhD,KAAK,MAAM,WAAW,IAAI,aAAa,EAAE,CAAC;oBACxC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;yBAChD,GAAG,CAAC,+BAA+B,CAAC;yBACpC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;yBAC3C,KAAK,CAAC;wBACL,OAAO,EAAE,eAAe;wBACxB,WAAW;qBACZ,CAAC;yBACD,MAAM,CAAC,GAAG,CAAC,CAAC;oBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;gBAChD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,wCAAwC,EAAE,GAAG,EAAE;YACtD,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;gBACjE,MAAM,OAAO,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;gBACzC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;oBACvC,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC;iBACpC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAEvB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;oBAC7B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;yBAChD,GAAG,CAAC,8BAA8B,MAAM,EAAE,CAAC;yBAC3C,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;yBAC3C,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC;yBAChB,MAAM,CAAC,GAAG,CAAC,CAAC;oBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBACtC,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,kBAAkB,GAAG,EAAE,CAAC;YAC9B,MAAM,QAAQ,GAAG,KAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAC7D,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzB,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAC/B,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE9C,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,sCAAsC;YACtC,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;iBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAEhD,oCAAoC;YACpC,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAClC,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;iBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC;YAEtD,yBAAyB;YACzB,MAAM,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,UAAU,GAAG;gBACjB,SAAS,EAAE,aAAa;gBACxB,KAAK,EAAE;oBACL,UAAU,EAAE,eAAe;oBAC3B,QAAQ,EAAE;wBACR,EAAE,MAAM,EAAE,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE;wBACrF,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE;wBACtG,EAAE,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE;qBAC5B;iBACF;gBACD,gBAAgB,EAAE;oBAChB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,KAAK;iBACf;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,kCAAkC,CAAC;iBACxC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,UAAU,CAAC;iBAChB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9D,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,iCAAiC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,oCAAoC;IACpC,KAAK,UAAU,YAAY;QACzB,iCAAiC;QACjC,MAAM,eAAe,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YAC3D,cAAc,EAAE,gBAAgB,CAAC,EAAE;YACnC,QAAQ,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC1C,OAAO,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACjD,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI;YACzC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,IAAI;YAChC,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YACzE,QAAQ,EAAE;gBACR,OAAO,EAAE,SAAS,CAAC,EAAE;gBACrB,QAAQ,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aACvD;SACF,CAAC,CAAC,CAAC;QAEJ,MAAM,wBAAwB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAErD,+BAA+B;QAC/B,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACxD,QAAQ,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC1C,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,GAAG,GAAG;YACxC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;YAC3B,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE;YAC7B,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YACrE,QAAQ,EAAE;gBACR,MAAM,EAAE,WAAW;gBACnB,WAAW,EAAE,MAAM;aACpB;SACF,CAAC,CAAC,CAAC;QAEJ,MAAM,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEjD,4BAA4B;QAC5B,MAAM,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACrD,cAAc,EAAE,gBAAgB,CAAC,EAAE;YACnC,QAAQ,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC1C,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,IAAI;YAChC,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAC1E,QAAQ,EAAE;gBACR,MAAM,EAAE,WAAW;gBACnB,IAAI,EAAE,UAAU;aACjB;SACF,CAAC,CAAC,CAAC;QAEJ,MAAM,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC7C,CAAC;AACH,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\tests\\integration\\dashboard-analytics.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport { EventEmitterModule } from '@nestjs/event-emitter';\r\nimport { BullModule } from '@nestjs/bull';\r\nimport * as request from 'supertest';\r\nimport { Repository } from 'typeorm';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { JwtModule } from '@nestjs/jwt';\r\nimport { PassportModule } from '@nestjs/passport';\r\nimport { NotificationDashboardController } from '../../controllers/notification-dashboard.controller';\r\nimport { AdvancedAnalyticsController } from '../../controllers/advanced-analytics.controller';\r\nimport { NotificationDashboardService } from '../../services/notification-dashboard.service';\r\nimport { AdvancedAnalyticsService } from '../../services/advanced-analytics.service';\r\nimport { NotificationAnalyticsService } from '../../services/notification-analytics.service';\r\nimport { ProviderHealthMonitoringService } from '../../services/provider-health-monitoring.service';\r\nimport { NotificationCostManagementService } from '../../services/notification-cost-management.service';\r\nimport { NotificationAnalyticsEvent } from '../../entities/notification-analytics-event.entity';\r\nimport { ProviderHealthMetric } from '../../entities/provider-health-metric.entity';\r\nimport { NotificationCostEvent } from '../../entities/notification-cost-event.entity';\r\n\r\n/**\r\n * Dashboard and Analytics API Integration Tests\r\n * \r\n * Comprehensive integration tests for dashboard and analytics APIs including:\r\n * - Real-time dashboard data flow validation with WebSocket integration\r\n * - Advanced analytics API testing with complex query scenarios\r\n * - Data aggregation and visualization endpoint validation\r\n * - Performance monitoring and optimization testing\r\n * - Cross-module data integration and consistency validation\r\n * - Authentication and authorization testing for analytics endpoints\r\n */\r\ndescribe('Dashboard and Analytics API Integration Tests', () => {\r\n  let app: INestApplication;\r\n  let dashboardService: NotificationDashboardService;\r\n  let analyticsService: AdvancedAnalyticsService;\r\n  let notificationAnalyticsService: NotificationAnalyticsService;\r\n  let healthService: ProviderHealthMonitoringService;\r\n  let costService: NotificationCostManagementService;\r\n  let analyticsEventRepository: Repository<NotificationAnalyticsEvent>;\r\n  let healthMetricRepository: Repository<ProviderHealthMetric>;\r\n  let costEventRepository: Repository<NotificationCostEvent>;\r\n\r\n  const testUser = {\r\n    id: 'test-user-123',\r\n    email: '<EMAIL>',\r\n    role: 'admin',\r\n    team: 'analytics',\r\n  };\r\n\r\n  const authToken = 'test-jwt-token';\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        TypeOrmModule.forRoot({\r\n          type: 'postgres',\r\n          host: process.env.TEST_DB_HOST || 'localhost',\r\n          port: parseInt(process.env.TEST_DB_PORT) || 5433,\r\n          username: process.env.TEST_DB_USERNAME || 'test',\r\n          password: process.env.TEST_DB_PASSWORD || 'test',\r\n          database: process.env.TEST_DB_NAME || 'sentinel_test',\r\n          entities: [\r\n            NotificationAnalyticsEvent,\r\n            ProviderHealthMetric,\r\n            NotificationCostEvent,\r\n          ],\r\n          synchronize: true,\r\n          dropSchema: true,\r\n        }),\r\n        TypeOrmModule.forFeature([\r\n          NotificationAnalyticsEvent,\r\n          ProviderHealthMetric,\r\n          NotificationCostEvent,\r\n        ]),\r\n        EventEmitterModule.forRoot(),\r\n        BullModule.forRoot({\r\n          redis: {\r\n            host: process.env.TEST_REDIS_HOST || 'localhost',\r\n            port: parseInt(process.env.TEST_REDIS_PORT) || 6380,\r\n            db: 1,\r\n          },\r\n        }),\r\n        PassportModule.register({ defaultStrategy: 'jwt' }),\r\n        JwtModule.register({\r\n          secret: 'test-secret',\r\n          signOptions: { expiresIn: '1h' },\r\n        }),\r\n      ],\r\n      controllers: [\r\n        NotificationDashboardController,\r\n        AdvancedAnalyticsController,\r\n      ],\r\n      providers: [\r\n        NotificationDashboardService,\r\n        AdvancedAnalyticsService,\r\n        NotificationAnalyticsService,\r\n        ProviderHealthMonitoringService,\r\n        NotificationCostManagementService,\r\n        // Mock authentication guard\r\n        {\r\n          provide: 'JwtAuthGuard',\r\n          useValue: {\r\n            canActivate: jest.fn().mockReturnValue(true),\r\n          },\r\n        },\r\n        {\r\n          provide: 'RolesGuard',\r\n          useValue: {\r\n            canActivate: jest.fn().mockReturnValue(true),\r\n          },\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    await app.init();\r\n\r\n    // Get service instances\r\n    dashboardService = moduleFixture.get<NotificationDashboardService>(NotificationDashboardService);\r\n    analyticsService = moduleFixture.get<AdvancedAnalyticsService>(AdvancedAnalyticsService);\r\n    notificationAnalyticsService = moduleFixture.get<NotificationAnalyticsService>(NotificationAnalyticsService);\r\n    healthService = moduleFixture.get<ProviderHealthMonitoringService>(ProviderHealthMonitoringService);\r\n    costService = moduleFixture.get<NotificationCostManagementService>(NotificationCostManagementService);\r\n\r\n    // Get repository instances\r\n    analyticsEventRepository = moduleFixture.get<Repository<NotificationAnalyticsEvent>>(\r\n      getRepositoryToken(NotificationAnalyticsEvent)\r\n    );\r\n    healthMetricRepository = moduleFixture.get<Repository<ProviderHealthMetric>>(\r\n      getRepositoryToken(ProviderHealthMetric)\r\n    );\r\n    costEventRepository = moduleFixture.get<Repository<NotificationCostEvent>>(\r\n      getRepositoryToken(NotificationCostEvent)\r\n    );\r\n\r\n    // Seed test data\r\n    await seedTestData();\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    // Reset any test-specific data\r\n  });\r\n\r\n  describe('Dashboard API Endpoints', () => {\r\n    describe('GET /dashboard/overview', () => {\r\n      it('should return comprehensive dashboard overview', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/dashboard/overview')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .query({\r\n            timeRange: '24h',\r\n            includeComparison: true,\r\n            includeTrends: true,\r\n          })\r\n          .expect(200);\r\n\r\n        expect(response.body).toBeDefined();\r\n        expect(response.body.summary).toBeDefined();\r\n        expect(response.body.summary.totalNotifications).toBeDefined();\r\n        expect(response.body.summary.deliveryRate).toBeDefined();\r\n        expect(response.body.summary.avgDeliveryTime).toBeDefined();\r\n\r\n        expect(response.body.providers).toBeDefined();\r\n        expect(Array.isArray(response.body.providers)).toBe(true);\r\n\r\n        expect(response.body.channels).toBeDefined();\r\n        expect(Array.isArray(response.body.channels)).toBe(true);\r\n\r\n        expect(response.body.trends).toBeDefined();\r\n        expect(response.body.comparison).toBeDefined();\r\n      });\r\n\r\n      it('should handle different time ranges', async () => {\r\n        const timeRanges = ['1h', '6h', '24h', '7d', '30d'];\r\n\r\n        for (const timeRange of timeRanges) {\r\n          const response = await request(app.getHttpServer())\r\n            .get('/dashboard/overview')\r\n            .set('Authorization', `Bearer ${authToken}`)\r\n            .query({ timeRange })\r\n            .expect(200);\r\n\r\n          expect(response.body.timeRange).toBe(timeRange);\r\n          expect(response.body.summary).toBeDefined();\r\n        }\r\n      });\r\n\r\n      it('should filter by providers and channels', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/dashboard/overview')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .query({\r\n            providers: 'email,slack',\r\n            channels: 'email,slack',\r\n            timeRange: '24h',\r\n          })\r\n          .expect(200);\r\n\r\n        expect(response.body.filters).toBeDefined();\r\n        expect(response.body.filters.providers).toEqual(['email', 'slack']);\r\n        expect(response.body.filters.channels).toEqual(['email', 'slack']);\r\n      });\r\n    });\r\n\r\n    describe('GET /dashboard/real-time', () => {\r\n      it('should return real-time dashboard metrics', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/dashboard/real-time')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .query({\r\n            metrics: 'delivery,cost,health',\r\n            refreshInterval: 30,\r\n          })\r\n          .expect(200);\r\n\r\n        expect(response.body).toBeDefined();\r\n        expect(response.body.metrics).toBeDefined();\r\n        expect(response.body.refreshInterval).toBe(30);\r\n        expect(response.body.lastUpdated).toBeDefined();\r\n\r\n        expect(response.body.delivery).toBeDefined();\r\n        expect(response.body.cost).toBeDefined();\r\n        expect(response.body.health).toBeDefined();\r\n      });\r\n\r\n      it('should provide WebSocket endpoint for live updates', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/dashboard/real-time')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .query({ enableWebSocket: true })\r\n          .expect(200);\r\n\r\n        expect(response.body.websocketUrl).toBeDefined();\r\n        expect(response.body.websocketUrl).toContain('ws://');\r\n      });\r\n    });\r\n\r\n    describe('GET /dashboard/health', () => {\r\n      it('should return provider health dashboard', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/dashboard/health')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .query({\r\n            includeHistory: true,\r\n            timeRange: '24h',\r\n          })\r\n          .expect(200);\r\n\r\n        expect(response.body).toBeDefined();\r\n        expect(response.body.summary).toBeDefined();\r\n        expect(response.body.summary.totalProviders).toBeDefined();\r\n        expect(response.body.summary.healthyProviders).toBeDefined();\r\n        expect(response.body.summary.avgResponseTime).toBeDefined();\r\n\r\n        expect(response.body.providers).toBeDefined();\r\n        expect(Array.isArray(response.body.providers)).toBe(true);\r\n\r\n        response.body.providers.forEach(provider => {\r\n          expect(provider.name).toBeDefined();\r\n          expect(provider.status).toBeDefined();\r\n          expect(provider.responseTime).toBeDefined();\r\n          expect(provider.uptime).toBeDefined();\r\n        });\r\n\r\n        expect(response.body.history).toBeDefined();\r\n      });\r\n\r\n      it('should show circuit breaker status', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/dashboard/health')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .query({ includeCircuitBreakers: true })\r\n          .expect(200);\r\n\r\n        expect(response.body.circuitBreakers).toBeDefined();\r\n        expect(Array.isArray(response.body.circuitBreakers)).toBe(true);\r\n\r\n        response.body.circuitBreakers.forEach(cb => {\r\n          expect(cb.provider).toBeDefined();\r\n          expect(cb.state).toBeDefined();\r\n          expect(['closed', 'open', 'half-open'].includes(cb.state)).toBe(true);\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('GET /dashboard/cost', () => {\r\n      it('should return cost analytics dashboard', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/dashboard/cost')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .query({\r\n            timeRange: '30d',\r\n            includeOptimization: true,\r\n            groupBy: 'provider',\r\n          })\r\n          .expect(200);\r\n\r\n        expect(response.body).toBeDefined();\r\n        expect(response.body.summary).toBeDefined();\r\n        expect(response.body.summary.totalCost).toBeDefined();\r\n        expect(response.body.summary.avgCostPerNotification).toBeDefined();\r\n\r\n        expect(response.body.breakdown).toBeDefined();\r\n        expect(Array.isArray(response.body.breakdown)).toBe(true);\r\n\r\n        expect(response.body.trends).toBeDefined();\r\n        expect(response.body.optimization).toBeDefined();\r\n        expect(response.body.optimization.recommendations).toBeDefined();\r\n      });\r\n\r\n      it('should provide budget tracking', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/dashboard/cost')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .query({\r\n            includeBudget: true,\r\n            budgetPeriod: 'monthly',\r\n          })\r\n          .expect(200);\r\n\r\n        expect(response.body.budget).toBeDefined();\r\n        expect(response.body.budget.allocated).toBeDefined();\r\n        expect(response.body.budget.spent).toBeDefined();\r\n        expect(response.body.budget.remaining).toBeDefined();\r\n        expect(response.body.budget.utilizationRate).toBeDefined();\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Advanced Analytics API Endpoints', () => {\r\n    describe('GET /advanced-analytics/multi-dimensional', () => {\r\n      it('should perform multi-dimensional analytics', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/advanced-analytics/multi-dimensional')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .query({\r\n            dimensions: 'provider,channel,status',\r\n            metrics: 'count,avg_delivery_time,success_rate',\r\n            timeRange: '7d',\r\n            groupBy: 'provider',\r\n          })\r\n          .expect(200);\r\n\r\n        expect(response.body).toBeDefined();\r\n        expect(response.body.dimensions).toEqual(['provider', 'channel', 'status']);\r\n        expect(response.body.metrics).toEqual(['count', 'avg_delivery_time', 'success_rate']);\r\n        expect(response.body.data).toBeDefined();\r\n        expect(Array.isArray(response.body.data)).toBe(true);\r\n\r\n        expect(response.body.aggregations).toBeDefined();\r\n        expect(response.body.correlations).toBeDefined();\r\n        expect(response.body.insights).toBeDefined();\r\n      });\r\n\r\n      it('should handle complex filtering', async () => {\r\n        const filters = JSON.stringify({\r\n          provider: 'email',\r\n          status: 'delivered',\r\n          'alert.severity': 'critical',\r\n        });\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .get('/advanced-analytics/multi-dimensional')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .query({\r\n            dimensions: 'provider,channel',\r\n            metrics: 'count,success_rate',\r\n            filters,\r\n          })\r\n          .expect(200);\r\n\r\n        expect(response.body.data).toBeDefined();\r\n        // Verify filtering was applied\r\n        response.body.data.forEach(item => {\r\n          expect(item.provider).toBe('email');\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('POST /advanced-analytics/predictive', () => {\r\n      it('should generate predictive analytics', async () => {\r\n        const predictiveQuery = {\r\n          target: 'delivery_rate',\r\n          predictors: ['provider', 'channel', 'time_of_day'],\r\n          algorithm: 'random_forest',\r\n          historicalRange: '90d',\r\n          forecastPeriod: '30d',\r\n          includeConfidenceIntervals: true,\r\n          includeTrends: true,\r\n        };\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .post('/advanced-analytics/predictive')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .send(predictiveQuery)\r\n          .expect(200);\r\n\r\n        expect(response.body).toBeDefined();\r\n        expect(response.body.forecast).toBeDefined();\r\n        expect(response.body.forecast.predictions).toBeDefined();\r\n        expect(response.body.forecast.confidence).toBeDefined();\r\n        expect(response.body.forecast.accuracy).toBeDefined();\r\n\r\n        expect(response.body.trends).toBeDefined();\r\n        expect(response.body.anomalies).toBeDefined();\r\n        expect(response.body.recommendations).toBeDefined();\r\n      });\r\n\r\n      it('should handle different ML algorithms', async () => {\r\n        const algorithms = ['linear_regression', 'random_forest', 'neural_network', 'arima'];\r\n\r\n        for (const algorithm of algorithms) {\r\n          const query = {\r\n            target: 'cost',\r\n            algorithm,\r\n            historicalRange: '30d',\r\n            forecastPeriod: '7d',\r\n          };\r\n\r\n          const response = await request(app.getHttpServer())\r\n            .post('/advanced-analytics/predictive')\r\n            .set('Authorization', `Bearer ${authToken}`)\r\n            .send(query)\r\n            .expect(200);\r\n\r\n          expect(response.body.forecast.model).toBe(algorithm);\r\n        }\r\n      });\r\n    });\r\n\r\n    describe('POST /advanced-analytics/custom-query', () => {\r\n      it('should execute custom SQL-like queries', async () => {\r\n        const customQuery = {\r\n          queryType: 'sql',\r\n          query: {\r\n            sql: 'SELECT provider, COUNT(*) as total FROM notifications WHERE created_at > NOW() - INTERVAL 7 DAY GROUP BY provider',\r\n          },\r\n          visualization: {\r\n            type: 'bar_chart',\r\n            title: 'Notifications by Provider (Last 7 Days)',\r\n            xAxis: 'provider',\r\n            yAxis: 'total',\r\n          },\r\n        };\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .post('/advanced-analytics/custom-query')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .send(customQuery)\r\n          .expect(200);\r\n\r\n        expect(response.body).toBeDefined();\r\n        expect(response.body.query).toBeDefined();\r\n        expect(response.body.results).toBeDefined();\r\n        expect(Array.isArray(response.body.results)).toBe(true);\r\n        expect(response.body.visualization).toBeDefined();\r\n        expect(response.body.performance).toBeDefined();\r\n      });\r\n\r\n      it('should support visual query builder', async () => {\r\n        const visualQuery = {\r\n          queryType: 'visual_builder',\r\n          query: {\r\n            dataSource: 'notifications',\r\n            dimensions: ['provider', 'channel'],\r\n            metrics: ['count', 'avg_delivery_time'],\r\n            filters: [\r\n              { field: 'created_at', operator: 'gte', value: '7d' },\r\n              { field: 'status', operator: 'eq', value: 'delivered' },\r\n            ],\r\n          },\r\n          visualization: {\r\n            type: 'line_chart',\r\n            title: 'Delivery Performance Trends',\r\n          },\r\n        };\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .post('/advanced-analytics/custom-query')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .send(visualQuery)\r\n          .expect(200);\r\n\r\n        expect(response.body.results).toBeDefined();\r\n        expect(response.body.visualization.type).toBe('line_chart');\r\n      });\r\n    });\r\n\r\n    describe('GET /advanced-analytics/correlation', () => {\r\n      it('should analyze correlations between metrics', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/advanced-analytics/correlation')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .query({\r\n            variables: 'delivery_time,cost,success_rate,response_time',\r\n            timeRange: '30d',\r\n            method: 'pearson',\r\n          })\r\n          .expect(200);\r\n\r\n        expect(response.body).toBeDefined();\r\n        expect(response.body.correlationMatrix).toBeDefined();\r\n        expect(response.body.significantCorrelations).toBeDefined();\r\n        expect(Array.isArray(response.body.significantCorrelations)).toBe(true);\r\n        expect(response.body.insights).toBeDefined();\r\n        expect(response.body.visualization).toBeDefined();\r\n      });\r\n\r\n      it('should support different correlation methods', async () => {\r\n        const methods = ['pearson', 'spearman', 'kendall'];\r\n\r\n        for (const method of methods) {\r\n          const response = await request(app.getHttpServer())\r\n            .get('/advanced-analytics/correlation')\r\n            .set('Authorization', `Bearer ${authToken}`)\r\n            .query({\r\n              variables: 'delivery_time,cost',\r\n              method,\r\n            })\r\n            .expect(200);\r\n\r\n          expect(response.body.correlationMatrix).toBeDefined();\r\n        }\r\n      });\r\n    });\r\n\r\n    describe('GET /advanced-analytics/anomalies', () => {\r\n      it('should detect anomalies in notification metrics', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/advanced-analytics/anomalies')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .query({\r\n            metrics: 'delivery_time,cost,error_rate',\r\n            timeRange: '7d',\r\n            sensitivity: 'medium',\r\n            algorithm: 'isolation_forest',\r\n          })\r\n          .expect(200);\r\n\r\n        expect(response.body).toBeDefined();\r\n        expect(response.body.anomalies).toBeDefined();\r\n        expect(Array.isArray(response.body.anomalies)).toBe(true);\r\n        expect(response.body.patterns).toBeDefined();\r\n        expect(response.body.severity).toBeDefined();\r\n        expect(response.body.recommendations).toBeDefined();\r\n      });\r\n\r\n      it('should handle different sensitivity levels', async () => {\r\n        const sensitivities = ['low', 'medium', 'high'];\r\n\r\n        for (const sensitivity of sensitivities) {\r\n          const response = await request(app.getHttpServer())\r\n            .get('/advanced-analytics/anomalies')\r\n            .set('Authorization', `Bearer ${authToken}`)\r\n            .query({\r\n              metrics: 'delivery_time',\r\n              sensitivity,\r\n            })\r\n            .expect(200);\r\n\r\n          expect(response.body.anomalies).toBeDefined();\r\n        }\r\n      });\r\n    });\r\n\r\n    describe('GET /advanced-analytics/export/:format', () => {\r\n      it('should export analytics data in different formats', async () => {\r\n        const formats = ['csv', 'json', 'excel'];\r\n        const query = Buffer.from(JSON.stringify({\r\n          type: 'summary',\r\n          timeRange: '7d',\r\n          metrics: ['count', 'delivery_rate'],\r\n        })).toString('base64');\r\n\r\n        for (const format of formats) {\r\n          const response = await request(app.getHttpServer())\r\n            .get(`/advanced-analytics/export/${format}`)\r\n            .set('Authorization', `Bearer ${authToken}`)\r\n            .query({ query })\r\n            .expect(200);\r\n\r\n          expect(response.body).toBeDefined();\r\n        }\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Performance and Load Testing', () => {\r\n    it('should handle concurrent dashboard requests', async () => {\r\n      const concurrentRequests = 20;\r\n      const promises = Array(concurrentRequests).fill(null).map(() =>\r\n        request(app.getHttpServer())\r\n          .get('/dashboard/overview')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .query({ timeRange: '24h' })\r\n      );\r\n\r\n      const responses = await Promise.all(promises);\r\n      \r\n      responses.forEach(response => {\r\n        expect(response.status).toBe(200);\r\n        expect(response.body.summary).toBeDefined();\r\n      });\r\n    });\r\n\r\n    it('should cache dashboard data for performance', async () => {\r\n      const startTime = Date.now();\r\n      \r\n      // First request (should hit database)\r\n      await request(app.getHttpServer())\r\n        .get('/dashboard/overview')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .query({ timeRange: '24h' })\r\n        .expect(200);\r\n\r\n      const firstRequestTime = Date.now() - startTime;\r\n\r\n      // Second request (should hit cache)\r\n      const cacheStartTime = Date.now();\r\n      await request(app.getHttpServer())\r\n        .get('/dashboard/overview')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .query({ timeRange: '24h' })\r\n        .expect(200);\r\n\r\n      const secondRequestTime = Date.now() - cacheStartTime;\r\n\r\n      // Cache should be faster\r\n      expect(secondRequestTime).toBeLessThan(firstRequestTime);\r\n    });\r\n\r\n    it('should handle large dataset analytics queries', async () => {\r\n      const largeQuery = {\r\n        queryType: 'aggregation',\r\n        query: {\r\n          collection: 'notifications',\r\n          pipeline: [\r\n            { $match: { created_at: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } } },\r\n            { $group: { _id: { provider: '$provider', day: { $dayOfYear: '$created_at' } }, count: { $sum: 1 } } },\r\n            { $sort: { '_id.day': 1 } },\r\n          ],\r\n        },\r\n        executionOptions: {\r\n          timeout: 60000,\r\n          maxRows: 10000,\r\n        },\r\n      };\r\n\r\n      const startTime = Date.now();\r\n      const response = await request(app.getHttpServer())\r\n        .post('/advanced-analytics/custom-query')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(largeQuery)\r\n        .expect(200);\r\n\r\n      const executionTime = Date.now() - startTime;\r\n\r\n      expect(response.body.results).toBeDefined();\r\n      expect(response.body.performance.executionTime).toBeDefined();\r\n      expect(executionTime).toBeLessThan(60000); // Should complete within timeout\r\n    });\r\n  });\r\n\r\n  // Helper function to seed test data\r\n  async function seedTestData() {\r\n    // Create sample analytics events\r\n    const analyticsEvents = Array(100).fill(null).map((_, i) => ({\r\n      notificationId: `notification-${i}`,\r\n      provider: ['email', 'sms', 'slack'][i % 3],\r\n      channel: ['email', 'sms', 'slack'][i % 3],\r\n      status: ['delivered', 'failed', 'pending'][i % 3],\r\n      deliveryTime: Math.random() * 5000 + 1000,\r\n      cost: Math.random() * 0.1 + 0.01,\r\n      timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),\r\n      metadata: {\r\n        alertId: `alert-${i}`,\r\n        severity: ['low', 'medium', 'high', 'critical'][i % 4],\r\n      },\r\n    }));\r\n\r\n    await analyticsEventRepository.save(analyticsEvents);\r\n\r\n    // Create sample health metrics\r\n    const healthMetrics = Array(50).fill(null).map((_, i) => ({\r\n      provider: ['email', 'sms', 'slack'][i % 3],\r\n      responseTime: Math.random() * 1000 + 100,\r\n      uptime: Math.random() * 100,\r\n      errorRate: Math.random() * 10,\r\n      timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),\r\n      metadata: {\r\n        region: 'us-east-1',\r\n        environment: 'test',\r\n      },\r\n    }));\r\n\r\n    await healthMetricRepository.save(healthMetrics);\r\n\r\n    // Create sample cost events\r\n    const costEvents = Array(75).fill(null).map((_, i) => ({\r\n      notificationId: `notification-${i}`,\r\n      provider: ['email', 'sms', 'slack'][i % 3],\r\n      cost: Math.random() * 0.1 + 0.01,\r\n      currency: 'USD',\r\n      timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),\r\n      metadata: {\r\n        region: 'us-east-1',\r\n        tier: 'standard',\r\n      },\r\n    }));\r\n\r\n    await costEventRepository.save(costEvents);\r\n  }\r\n});\r\n"], "version": 3}