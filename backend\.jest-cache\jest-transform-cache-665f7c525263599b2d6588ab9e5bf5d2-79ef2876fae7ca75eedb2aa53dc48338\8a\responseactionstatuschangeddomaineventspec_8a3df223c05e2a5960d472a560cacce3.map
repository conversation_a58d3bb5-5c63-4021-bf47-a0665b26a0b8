{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-status-changed.domain-event.spec.ts", "mappings": ";;AAAA,gHAA8<PERSON>;AAC9I,gEAA8D;AAC9D,uEAA8D;AAE9D,QAAQ,CAAC,wCAAwC,EAAE,GAAG,EAAE;IACtD,IAAI,WAA2B,CAAC;IAChC,IAAI,SAA+C,CAAC;IACpD,IAAI,KAA6C,CAAC;IAElD,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;QACxC,SAAS,GAAG;YACV,SAAS,EAAE,iCAAY,CAAC,OAAO;YAC/B,SAAS,EAAE,iCAAY,CAAC,QAAQ;YAChC,SAAS,EAAE,mBAAmB;YAC9B,KAAK,EAAE,wBAAwB;YAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QACF,KAAK,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,oFAAsC,CAAC,CAAC;YACrE,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAE,8BAAc,CAAC,QAAQ,EAAE;gBAClC,UAAU,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBAClC,YAAY,EAAE,CAAC;gBACf,aAAa,EAAE,UAAU;gBACzB,WAAW,EAAE,WAAW;gBACxB,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;aAC7B,CAAC;YAEF,MAAM,WAAW,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEtG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACxD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAC9D,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;YAClE,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YACpE,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YAChE,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEzC,MAAM,eAAe,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC9E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,MAAM;aAC/B,CAAC,CAAC;YACH,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,eAAe,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC9E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,MAAM;aAC/B,CAAC,CAAC;YACH,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,aAAa,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,OAAO;aAChC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,MAAM;aAC/B,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,OAAO;aAChC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtD,MAAM,cAAc,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,aAAa,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,OAAO;gBAC/B,SAAS,EAAE,iCAAY,CAAC,QAAQ;aACjC,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,WAAW,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,MAAM;aAC/B,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,iBAAiB,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,mBAAmB;aAC5C,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEzD,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,OAAO;aAChC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,OAAO;aAChC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,SAAS,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,MAAM;gBAC9B,SAAS,EAAE,iCAAY,CAAC,OAAO;aAChC,CAAC,CAAC;YACH,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjD,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,mBAAmB,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAClF,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,MAAM;gBAC9B,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,iBAAiB,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEhD,MAAM,gBAAgB,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,MAAM;gBAC9B,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,cAAc,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,OAAO;gBAC/B,SAAS,EAAE,iCAAY,CAAC,QAAQ;aACjC,CAAC,CAAC;YACH,MAAM,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,UAAU,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,MAAM;gBAC9B,SAAS,EAAE,iCAAY,CAAC,QAAQ;aACjC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,UAAU,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,MAAM;aAC/B,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,WAAW,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,MAAM;gBAC9B,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,mBAAmB,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAClF,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,MAAM;gBAC9B,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAElE,MAAM,iBAAiB,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,eAAe,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC9E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,MAAM;gBAC9B,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,MAAM;gBAC9B,SAAS,EAAE,iCAAY,CAAC,MAAM;aAC/B,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,iBAAiB,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,mBAAmB;aAC5C,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,UAAU,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,QAAQ;gBAChC,SAAS,EAAE,iCAAY,CAAC,MAAM;aAC/B,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,OAAO,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,cAAc,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,MAAM;gBAC9B,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,cAAc,CAAC,qBAAqB,EAAE,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,cAAc,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,cAAc,CAAC,qBAAqB,EAAE,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,WAAW,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,MAAM;aAC/B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,WAAW,CAAC,qBAAqB,EAAE,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,iBAAiB,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,mBAAmB;aAC5C,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,iBAAiB,CAAC,qBAAqB,EAAE,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;YAC3D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,OAAO;aAChC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,OAAO;aAChC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,aAAa,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,MAAM;gBAC9B,SAAS,EAAE,iCAAY,CAAC,QAAQ;aACjC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,WAAW,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,MAAM;aAC/B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,WAAW,CAAC,qBAAqB,EAAE,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,SAAS,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,MAAM;gBAC9B,SAAS,EAAE,iCAAY,CAAC,OAAO;aAChC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,SAAS,CAAC,qBAAqB,EAAE,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,cAAc,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,cAAc,CAAC,qBAAqB,EAAE,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,aAAa,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,OAAO;gBAC/B,SAAS,EAAE,iCAAY,CAAC,QAAQ;aACjC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,cAAc,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,OAAO;gBAC/B,SAAS,EAAE,iCAAY,CAAC,QAAQ;aACjC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,cAAc,CAAC,sBAAsB,EAAE,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,mBAAmB,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAClF,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,MAAM;gBAC9B,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,mBAAmB,CAAC,sBAAsB,EAAE,CAAC;YAC7D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,sBAAsB,EAAE,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,MAAM;aAC/B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,sBAAsB,EAAE,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,iBAAiB,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,mBAAmB;aAC5C,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,iBAAiB,CAAC,sBAAsB,EAAE,CAAC;YAC3D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,iBAAiB,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,mBAAmB;aAC5C,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,MAAM;aAC/B,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,OAAO;aAChC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,MAAM;aAC/B,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,iBAAiB,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,mBAAmB;aAC5C,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,aAAa,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,MAAM;gBAC9B,SAAS,EAAE,iCAAY,CAAC,SAAS;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YAEnC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC7D,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,SAAS;gBACjC,SAAS,EAAE,iCAAY,CAAC,MAAM;aAC/B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,UAAU,EAAE,CAAC;YAE1C,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC7D,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAEpD,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACvE,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACvE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACzD,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9C,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBACxC,gBAAgB,EAAE,qBAAqB;gBACvC,cAAc,EAAE,UAAU;gBAC1B,YAAY,EAAE,KAAK;gBACnB,MAAM,EAAE,KAAK;gBACb,aAAa,EAAE,IAAI;gBACnB,UAAU,EAAE,KAAK;gBACjB,iBAAiB,EAAE,KAAK;aACzB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,cAAc,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;YAEH,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,CAAC;YACjD,MAAM,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,KAAK,EAAE,SAAS;aACjB,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;YAC3C,MAAM,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,eAAe,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC9E,GAAG,SAAS;gBACZ,SAAS,EAAE,iCAAY,CAAC,OAAO;gBAC/B,SAAS,EAAE,iCAAY,CAAC,OAAO;aAChC,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,YAAY,GAAG,IAAI,oFAAsC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,aAA6B;gBACxC,SAAS,EAAE,aAA6B;aACzC,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-status-changed.domain-event.spec.ts"], "sourcesContent": ["import { ResponseActionStatusChangedDomainEvent, ResponseActionStatusChangedEventData } from '../response-action-status-changed.domain-event';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { ActionStatus } from '../../enums/action-status.enum';\r\n\r\ndescribe('ResponseActionStatusChangedDomainEvent', () => {\r\n  let aggregateId: UniqueEntityId;\r\n  let eventData: ResponseActionStatusChangedEventData;\r\n  let event: ResponseActionStatusChangedDomainEvent;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.generate();\r\n    eventData = {\r\n      oldStatus: ActionStatus.PENDING,\r\n      newStatus: ActionStatus.APPROVED,\r\n      changedBy: '<EMAIL>',\r\n      notes: 'Approved for execution',\r\n      timestamp: new Date(),\r\n    };\r\n    event = new ResponseActionStatusChangedDomainEvent(aggregateId, eventData);\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create event with required data', () => {\r\n      expect(event).toBeInstanceOf(ResponseActionStatusChangedDomainEvent);\r\n      expect(event.aggregateId).toBe(aggregateId);\r\n      expect(event.eventData).toBe(eventData);\r\n      expect(event.occurredOn).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should create event with custom options', () => {\r\n      const customOptions = {\r\n        eventId: UniqueEntityId.generate(),\r\n        occurredOn: new Date('2023-01-01'),\r\n        eventVersion: 2,\r\n        correlationId: 'corr-123',\r\n        causationId: 'cause-456',\r\n        metadata: { source: 'test' },\r\n      };\r\n\r\n      const customEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, eventData, customOptions);\r\n\r\n      expect(customEvent.eventId).toBe(customOptions.eventId);\r\n      expect(customEvent.occurredOn).toBe(customOptions.occurredOn);\r\n      expect(customEvent.eventVersion).toBe(customOptions.eventVersion);\r\n      expect(customEvent.correlationId).toBe(customOptions.correlationId);\r\n      expect(customEvent.causationId).toBe(customOptions.causationId);\r\n      expect(customEvent.metadata).toEqual(customOptions.metadata);\r\n    });\r\n  });\r\n\r\n  describe('getters', () => {\r\n    it('should provide access to event data properties', () => {\r\n      expect(event.oldStatus).toBe(ActionStatus.PENDING);\r\n      expect(event.newStatus).toBe(ActionStatus.APPROVED);\r\n      expect(event.changedBy).toBe('<EMAIL>');\r\n      expect(event.notes).toBe('Approved for execution');\r\n      expect(event.changeTimestamp).toBe(eventData.timestamp);\r\n    });\r\n  });\r\n\r\n  describe('status transition analysis', () => {\r\n    it('should identify progression transitions', () => {\r\n      expect(event.isProgression()).toBe(true);\r\n\r\n      const regressionEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.PAUSED,\r\n      });\r\n      expect(regressionEvent.isProgression()).toBe(false);\r\n    });\r\n\r\n    it('should identify regression transitions', () => {\r\n      const regressionEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.PAUSED,\r\n      });\r\n      expect(regressionEvent.isRegression()).toBe(true);\r\n      expect(event.isRegression()).toBe(false);\r\n    });\r\n\r\n    it('should identify terminal transitions', () => {\r\n      const terminalEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.COMPLETED,\r\n      });\r\n      expect(terminalEvent.isTerminalTransition()).toBe(true);\r\n      expect(event.isTerminalTransition()).toBe(false);\r\n    });\r\n\r\n    it('should identify success transitions', () => {\r\n      const successEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.COMPLETED,\r\n      });\r\n      expect(successEvent.isSuccessTransition()).toBe(true);\r\n      expect(event.isSuccessTransition()).toBe(false);\r\n\r\n      const partialEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.PARTIAL,\r\n      });\r\n      expect(partialEvent.isSuccessTransition()).toBe(true);\r\n    });\r\n\r\n    it('should identify failure transitions', () => {\r\n      const failureEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.FAILED,\r\n      });\r\n      expect(failureEvent.isFailureTransition()).toBe(true);\r\n      expect(event.isFailureTransition()).toBe(false);\r\n\r\n      const timeoutEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.TIMEOUT,\r\n      });\r\n      expect(timeoutEvent.isFailureTransition()).toBe(true);\r\n\r\n      const cancelledEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.CANCELLED,\r\n      });\r\n      expect(cancelledEvent.isFailureTransition()).toBe(true);\r\n\r\n      const rejectedEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.PENDING,\r\n        newStatus: ActionStatus.REJECTED,\r\n      });\r\n      expect(rejectedEvent.isFailureTransition()).toBe(true);\r\n    });\r\n\r\n    it('should identify actions requiring attention', () => {\r\n      const failedEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.FAILED,\r\n      });\r\n      expect(failedEvent.requiresAttention()).toBe(true);\r\n\r\n      const interventionEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.MANUAL_INTERVENTION,\r\n      });\r\n      expect(interventionEvent.requiresAttention()).toBe(true);\r\n\r\n      const partialEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.PARTIAL,\r\n      });\r\n      expect(partialEvent.requiresAttention()).toBe(true);\r\n\r\n      const timeoutEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.TIMEOUT,\r\n      });\r\n      expect(timeoutEvent.requiresAttention()).toBe(true);\r\n\r\n      const holdEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.QUEUED,\r\n        newStatus: ActionStatus.ON_HOLD,\r\n      });\r\n      expect(holdEvent.requiresAttention()).toBe(true);\r\n\r\n      expect(event.requiresAttention()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('specific transition types', () => {\r\n    it('should identify execution start', () => {\r\n      const executionStartEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.QUEUED,\r\n        newStatus: ActionStatus.EXECUTING,\r\n      });\r\n      expect(executionStartEvent.isExecutionStart()).toBe(true);\r\n      expect(event.isExecutionStart()).toBe(false);\r\n    });\r\n\r\n    it('should identify execution end', () => {\r\n      const executionEndEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.COMPLETED,\r\n      });\r\n      expect(executionEndEvent.isExecutionEnd()).toBe(true);\r\n      expect(event.isExecutionEnd()).toBe(false);\r\n    });\r\n\r\n    it('should identify approval transition', () => {\r\n      expect(event.isApprovalTransition()).toBe(true);\r\n\r\n      const nonApprovalEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.QUEUED,\r\n        newStatus: ActionStatus.EXECUTING,\r\n      });\r\n      expect(nonApprovalEvent.isApprovalTransition()).toBe(false);\r\n    });\r\n\r\n    it('should identify rejection transition', () => {\r\n      const rejectionEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.PENDING,\r\n        newStatus: ActionStatus.REJECTED,\r\n      });\r\n      expect(rejectionEvent.isRejectionTransition()).toBe(true);\r\n      expect(event.isRejectionTransition()).toBe(false);\r\n    });\r\n\r\n    it('should identify retry transition', () => {\r\n      const retryEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.FAILED,\r\n        newStatus: ActionStatus.RETRYING,\r\n      });\r\n      expect(retryEvent.isRetryTransition()).toBe(true);\r\n      expect(event.isRetryTransition()).toBe(false);\r\n    });\r\n\r\n    it('should identify pause transition', () => {\r\n      const pauseEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.PAUSED,\r\n      });\r\n      expect(pauseEvent.isPauseTransition()).toBe(true);\r\n      expect(event.isPauseTransition()).toBe(false);\r\n    });\r\n\r\n    it('should identify resume transition', () => {\r\n      const resumeEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.PAUSED,\r\n        newStatus: ActionStatus.EXECUTING,\r\n      });\r\n      expect(resumeEvent.isResumeTransition()).toBe(true);\r\n      expect(event.isResumeTransition()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('change categorization', () => {\r\n    it('should categorize approval changes', () => {\r\n      expect(event.getChangeCategory()).toBe('approval');\r\n    });\r\n\r\n    it('should categorize execution changes', () => {\r\n      const executionStartEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.QUEUED,\r\n        newStatus: ActionStatus.EXECUTING,\r\n      });\r\n      expect(executionStartEvent.getChangeCategory()).toBe('execution');\r\n\r\n      const executionEndEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.COMPLETED,\r\n      });\r\n      expect(executionEndEvent.getChangeCategory()).toBe('execution');\r\n    });\r\n\r\n    it('should categorize completion changes', () => {\r\n      const completionEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.QUEUED,\r\n        newStatus: ActionStatus.COMPLETED,\r\n      });\r\n      expect(completionEvent.getChangeCategory()).toBe('completion');\r\n    });\r\n\r\n    it('should categorize failure changes', () => {\r\n      const failureEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.QUEUED,\r\n        newStatus: ActionStatus.FAILED,\r\n      });\r\n      expect(failureEvent.getChangeCategory()).toBe('failure');\r\n    });\r\n\r\n    it('should categorize intervention changes', () => {\r\n      const interventionEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.MANUAL_INTERVENTION,\r\n      });\r\n      expect(interventionEvent.getChangeCategory()).toBe('intervention');\r\n    });\r\n\r\n    it('should categorize other changes', () => {\r\n      const queueEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.APPROVED,\r\n        newStatus: ActionStatus.QUEUED,\r\n      });\r\n      expect(queueEvent.getChangeCategory()).toBe('other');\r\n    });\r\n  });\r\n\r\n  describe('recommended actions', () => {\r\n    it('should recommend actions for approved status', () => {\r\n      const actions = event.getRecommendedActions();\r\n      expect(actions).toContain('Queue action for execution');\r\n      expect(actions).toContain('Validate execution prerequisites');\r\n    });\r\n\r\n    it('should recommend actions for executing status', () => {\r\n      const executingEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.QUEUED,\r\n        newStatus: ActionStatus.EXECUTING,\r\n      });\r\n\r\n      const actions = executingEvent.getRecommendedActions();\r\n      expect(actions).toContain('Monitor action progress');\r\n      expect(actions).toContain('Set up timeout monitoring');\r\n    });\r\n\r\n    it('should recommend actions for completed status', () => {\r\n      const completedEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.COMPLETED,\r\n      });\r\n\r\n      const actions = completedEvent.getRecommendedActions();\r\n      expect(actions).toContain('Validate success criteria');\r\n      expect(actions).toContain('Update related entities');\r\n      expect(actions).toContain('Generate completion report');\r\n    });\r\n\r\n    it('should recommend actions for failed status', () => {\r\n      const failedEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.FAILED,\r\n      });\r\n\r\n      const actions = failedEvent.getRecommendedActions();\r\n      expect(actions).toContain('Analyze failure cause');\r\n      expect(actions).toContain('Determine if retry is appropriate');\r\n      expect(actions).toContain('Notify failure to stakeholders');\r\n    });\r\n\r\n    it('should recommend actions for manual intervention status', () => {\r\n      const interventionEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.MANUAL_INTERVENTION,\r\n      });\r\n\r\n      const actions = interventionEvent.getRecommendedActions();\r\n      expect(actions).toContain('Notify human operators');\r\n      expect(actions).toContain('Provide intervention guidance');\r\n      expect(actions).toContain('Escalate to appropriate team');\r\n    });\r\n\r\n    it('should recommend actions for timeout status', () => {\r\n      const timeoutEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.TIMEOUT,\r\n      });\r\n\r\n      const actions = timeoutEvent.getRecommendedActions();\r\n      expect(actions).toContain('Investigate timeout cause');\r\n      expect(actions).toContain('Determine if retry is safe');\r\n      expect(actions).toContain('Check system resources');\r\n    });\r\n\r\n    it('should recommend actions for partial status', () => {\r\n      const partialEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.PARTIAL,\r\n      });\r\n\r\n      const actions = partialEvent.getRecommendedActions();\r\n      expect(actions).toContain('Assess partial completion impact');\r\n      expect(actions).toContain('Determine next steps');\r\n      expect(actions).toContain('Consider manual completion');\r\n    });\r\n\r\n    it('should recommend actions for retrying status', () => {\r\n      const retryingEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.FAILED,\r\n        newStatus: ActionStatus.RETRYING,\r\n      });\r\n\r\n      const actions = retryingEvent.getRecommendedActions();\r\n      expect(actions).toContain('Monitor retry attempt');\r\n      expect(actions).toContain('Prepare for potential failure');\r\n    });\r\n\r\n    it('should recommend actions for paused status', () => {\r\n      const pausedEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.PAUSED,\r\n      });\r\n\r\n      const actions = pausedEvent.getRecommendedActions();\r\n      expect(actions).toContain('Document pause reason');\r\n      expect(actions).toContain('Set resume conditions');\r\n    });\r\n\r\n    it('should recommend actions for on hold status', () => {\r\n      const holdEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.QUEUED,\r\n        newStatus: ActionStatus.ON_HOLD,\r\n      });\r\n\r\n      const actions = holdEvent.getRecommendedActions();\r\n      expect(actions).toContain('Identify blocking dependencies');\r\n      expect(actions).toContain('Set resolution timeline');\r\n    });\r\n\r\n    it('should recommend actions for cancelled status', () => {\r\n      const cancelledEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.CANCELLED,\r\n      });\r\n\r\n      const actions = cancelledEvent.getRecommendedActions();\r\n      expect(actions).toContain('Clean up partial execution');\r\n      expect(actions).toContain('Document cancellation reason');\r\n    });\r\n\r\n    it('should recommend actions for rejected status', () => {\r\n      const rejectedEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.PENDING,\r\n        newStatus: ActionStatus.REJECTED,\r\n      });\r\n\r\n      const actions = rejectedEvent.getRecommendedActions();\r\n      expect(actions).toContain('Document rejection reason');\r\n      expect(actions).toContain('Notify requester');\r\n    });\r\n  });\r\n\r\n  describe('notification targets', () => {\r\n    it('should identify targets for approval transitions', () => {\r\n      const targets = event.getNotificationTargets();\r\n      expect(targets).toContain('action-requestor');\r\n      expect(targets).toContain('execution-team');\r\n    });\r\n\r\n    it('should identify targets for rejection transitions', () => {\r\n      const rejectionEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.PENDING,\r\n        newStatus: ActionStatus.REJECTED,\r\n      });\r\n\r\n      const targets = rejectionEvent.getNotificationTargets();\r\n      expect(targets).toContain('action-requestor');\r\n      expect(targets).toContain('security-team');\r\n    });\r\n\r\n    it('should identify targets for execution start', () => {\r\n      const executionStartEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.QUEUED,\r\n        newStatus: ActionStatus.EXECUTING,\r\n      });\r\n\r\n      const targets = executionStartEvent.getNotificationTargets();\r\n      expect(targets).toContain('monitoring-team');\r\n      expect(targets).toContain('action-requestor');\r\n    });\r\n\r\n    it('should identify targets for success transitions', () => {\r\n      const successEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.COMPLETED,\r\n      });\r\n\r\n      const targets = successEvent.getNotificationTargets();\r\n      expect(targets).toContain('action-requestor');\r\n      expect(targets).toContain('security-team');\r\n    });\r\n\r\n    it('should identify targets for failure transitions', () => {\r\n      const failureEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.FAILED,\r\n      });\r\n\r\n      const targets = failureEvent.getNotificationTargets();\r\n      expect(targets).toContain('action-requestor');\r\n      expect(targets).toContain('security-team');\r\n      expect(targets).toContain('incident-response-team');\r\n    });\r\n\r\n    it('should identify targets for attention-requiring transitions', () => {\r\n      const interventionEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.MANUAL_INTERVENTION,\r\n      });\r\n\r\n      const targets = interventionEvent.getNotificationTargets();\r\n      expect(targets).toContain('on-call-engineers');\r\n      expect(targets).toContain('security-analysts');\r\n    });\r\n  });\r\n\r\n  describe('urgency and impact assessment', () => {\r\n    it('should assess critical urgency for manual intervention', () => {\r\n      const interventionEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.MANUAL_INTERVENTION,\r\n      });\r\n      expect(interventionEvent.getUrgencyLevel()).toBe('critical');\r\n    });\r\n\r\n    it('should assess high urgency for failure transitions', () => {\r\n      const failureEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.FAILED,\r\n      });\r\n      expect(failureEvent.getUrgencyLevel()).toBe('high');\r\n    });\r\n\r\n    it('should assess medium urgency for attention-requiring transitions', () => {\r\n      const partialEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.PARTIAL,\r\n      });\r\n      expect(partialEvent.getUrgencyLevel()).toBe('medium');\r\n    });\r\n\r\n    it('should assess low urgency for normal transitions', () => {\r\n      expect(event.getUrgencyLevel()).toBe('low');\r\n    });\r\n\r\n    it('should assess high impact for terminal failures', () => {\r\n      const failureEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.FAILED,\r\n      });\r\n      expect(failureEvent.getImpact()).toBe('high');\r\n    });\r\n\r\n    it('should assess low impact for terminal successes', () => {\r\n      const successEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.COMPLETED,\r\n      });\r\n      expect(successEvent.getImpact()).toBe('low');\r\n    });\r\n\r\n    it('should assess medium impact for attention-requiring transitions', () => {\r\n      const interventionEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.MANUAL_INTERVENTION,\r\n      });\r\n      expect(interventionEvent.getImpact()).toBe('medium');\r\n    });\r\n\r\n    it('should assess low impact for progression transitions', () => {\r\n      expect(event.getImpact()).toBe('low');\r\n    });\r\n\r\n    it('should assess no impact for no-change transitions', () => {\r\n      const noChangeEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.QUEUED,\r\n        newStatus: ActionStatus.SCHEDULED,\r\n      });\r\n      expect(noChangeEvent.getImpact()).toBe('none');\r\n    });\r\n  });\r\n\r\n  describe('metrics generation', () => {\r\n    it('should generate comprehensive metrics', () => {\r\n      const metrics = event.getMetrics();\r\n\r\n      expect(metrics.statusTransition).toBe('pending_to_approved');\r\n      expect(metrics.isProgression).toBe(true);\r\n      expect(metrics.isTerminal).toBe(false);\r\n      expect(metrics.requiresAttention).toBe(false);\r\n      expect(metrics.changeCategory).toBe('approval');\r\n      expect(metrics.urgencyLevel).toBe('low');\r\n      expect(metrics.impact).toBe('low');\r\n    });\r\n\r\n    it('should generate metrics for failure transitions', () => {\r\n      const failureEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.EXECUTING,\r\n        newStatus: ActionStatus.FAILED,\r\n      });\r\n\r\n      const metrics = failureEvent.getMetrics();\r\n\r\n      expect(metrics.statusTransition).toBe('executing_to_failed');\r\n      expect(metrics.isProgression).toBe(true);\r\n      expect(metrics.isTerminal).toBe(true);\r\n      expect(metrics.requiresAttention).toBe(true);\r\n      expect(metrics.changeCategory).toBe('execution');\r\n      expect(metrics.urgencyLevel).toBe('high');\r\n      expect(metrics.impact).toBe('high');\r\n    });\r\n  });\r\n\r\n  describe('integration event conversion', () => {\r\n    it('should convert to integration event format', () => {\r\n      const integrationEvent = event.toIntegrationEvent();\r\n\r\n      expect(integrationEvent.eventType).toBe('ResponseActionStatusChanged');\r\n      expect(integrationEvent.action).toBe('response_action_status_changed');\r\n      expect(integrationEvent.resource).toBe('ResponseAction');\r\n      expect(integrationEvent.resourceId).toBe(aggregateId.toString());\r\n      expect(integrationEvent.data).toBe(eventData);\r\n      expect(integrationEvent.metadata).toEqual({\r\n        statusTransition: 'pending_to_approved',\r\n        changeCategory: 'approval',\r\n        urgencyLevel: 'low',\r\n        impact: 'low',\r\n        isProgression: true,\r\n        isTerminal: false,\r\n        requiresAttention: false,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle events without changedBy', () => {\r\n      const noChangerEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        changedBy: undefined,\r\n      });\r\n\r\n      expect(noChangerEvent.changedBy).toBeUndefined();\r\n      expect(noChangerEvent.getNotificationTargets()).toContain('action-requestor');\r\n    });\r\n\r\n    it('should handle events without notes', () => {\r\n      const noNotesEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        notes: undefined,\r\n      });\r\n\r\n      expect(noNotesEvent.notes).toBeUndefined();\r\n      expect(noNotesEvent.getRecommendedActions()).toContain('Queue action for execution');\r\n    });\r\n\r\n    it('should handle same status transitions', () => {\r\n      const sameStatusEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: ActionStatus.PENDING,\r\n        newStatus: ActionStatus.PENDING,\r\n      });\r\n\r\n      expect(sameStatusEvent.isProgression()).toBe(false);\r\n      expect(sameStatusEvent.isRegression()).toBe(false);\r\n      expect(sameStatusEvent.getImpact()).toBe('none');\r\n    });\r\n\r\n    it('should handle unknown status transitions', () => {\r\n      const unknownEvent = new ResponseActionStatusChangedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        oldStatus: 'UNKNOWN_OLD' as ActionStatus,\r\n        newStatus: 'UNKNOWN_NEW' as ActionStatus,\r\n      });\r\n\r\n      expect(unknownEvent.getChangeCategory()).toBe('other');\r\n      expect(unknownEvent.getUrgencyLevel()).toBe('low');\r\n    });\r\n  });\r\n});"], "version": 3}