{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\api-endpoints.integration.spec.ts", "mappings": ";;AACA,2CAA8D;AAC9D,6DAAwD;AACxD,6DAAyD;AACzD,2GAAsG;AACtG,0EAAsE;AACtE,sFAA2E;AAC3E,oFAAyE;AACzE,sEAA4D;AAC5D,oGAAyF;AACzF,2EAAiE;AACjE,uFAA4E;AAC5E,yEAA+D;AAE/D;;;;;;;;;;;;GAYG;AACH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;IAC/C,IAAI,GAAqB,CAAC;IAC1B,IAAI,aAA4B,CAAC;IACjC,IAAI,QAAuB,CAAC;IAE5B,gBAAgB;IAChB,MAAM,YAAY,GAAG;QACnB,2CAAgB;QAChB,yCAAe;QACf,4BAAS;QACT,mCAAY;QACZ,sBAAM;QACN,iCAAW;QACX,oBAAK;KACN,CAAC;IAEF,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,4BAA4B;QAC5B,QAAQ,GAAG,IAAI,CAAC,KAAM,SAAQ,+BAAa;SAAG,CAAC,EAAE,CAAC;QAElD,yBAAyB;QACzB,MAAM,QAAQ,CAAC,oBAAoB,CACjC;YACE,OAAO,EAAE;gBACP,kCAAe;gBACf,yDAA0B;gBAC1B,oCAAgB;aACjB;SACF,EACD,YAAY,CACb,CAAC;QAEF,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;QACnB,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,QAAQ,CAAC,sBAAsB,EAAE,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,QAAQ,CAAC,aAAa,EAAE,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,QAAQ,CAAC,eAAe,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAClD,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;gBACrE,UAAU;gBACV,MAAM,UAAU,GAAG;oBACjB,IAAI,EAAE,sBAAsB;oBAC5B,WAAW,EAAE,0CAA0C;oBACvD,IAAI,EAAE,WAAW;oBACjB,QAAQ,EAAE,UAAU;oBACpB,aAAa,EAAE;wBACb,UAAU,EAAE,UAAU;wBACtB,eAAe,EAAE,IAAI;wBACrB,OAAO,EAAE;4BACP,SAAS,EAAE;gCACT,KAAK,EAAE,sBAAsB;gCAC7B,GAAG,EAAE,sBAAsB;6BAC5B;4BACD,UAAU,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;yBACvC;wBACD,aAAa,EAAE;4BACb,IAAI,EAAE,WAAW;4BACjB,MAAM,EAAE,MAAM;4BACd,KAAK,EAAE,SAAS;yBACjB;qBACF;oBACD,QAAQ,EAAE;wBACR,OAAO,EAAE,IAAI;wBACb,SAAS,EAAE,OAAO;wBAClB,IAAI,EAAE,OAAO;wBACb,QAAQ,EAAE,KAAK;wBACf,UAAU,EAAE,CAAC,mBAAmB,CAAC;qBAClC;iBACF,CAAC;gBAEF,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,MAAM,EACN,+BAA+B,EAC/B,UAAU,CACX,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,OAAO,CAAC,CAAC;gBACjD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;oBAClC,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACtB,IAAI,EAAE,UAAU,CAAC,IAAI;oBACrB,WAAW,EAAE,UAAU,CAAC,WAAW;oBACnC,IAAI,EAAE,UAAU,CAAC,IAAI;oBACrB,QAAQ,EAAE,UAAU,CAAC,QAAQ;oBAC7B,QAAQ,EAAE,IAAI;oBACd,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE;iBAChC,CAAC,CAAC;gBAEH,QAAQ,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,EAAE;oBAC9C,EAAE,EAAE,QAAQ;oBACZ,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,QAAQ;oBACrB,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,QAAQ;oBAClB,aAAa,EAAE,QAAQ;oBACvB,QAAQ,EAAE,QAAQ;oBAClB,QAAQ,EAAE,SAAS;oBACnB,SAAS,EAAE,QAAQ;oBACnB,SAAS,EAAE,QAAQ;oBACnB,SAAS,EAAE,QAAQ;iBACpB,CAAC,CAAC;gBAEH,8BAA8B;gBAC9B,MAAM,QAAQ,CAAC,mBAAmB,CAAC,2CAAgB,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;YACrF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;gBACpE,UAAU;gBACV,MAAM,WAAW,GAAG;oBAClB,IAAI,EAAE,EAAE,EAAE,sBAAsB;oBAChC,IAAI,EAAE,cAAc,EAAE,uBAAuB;oBAC7C,aAAa,EAAE,SAAS,EAAE,4BAA4B;iBACvD,CAAC;gBAEF,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,MAAM,EACN,+BAA+B,EAC/B,WAAW,CACZ,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC;gBACrD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;oBAClC,UAAU,EAAE,GAAG;oBACf,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,EAAE,aAAa;iBACrB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;gBAC9D,UAAU;gBACV,MAAM,UAAU,GAAG;oBACjB,IAAI,EAAE,qBAAqB;oBAC3B,IAAI,EAAE,WAAW;oBACjB,QAAQ,EAAE,UAAU;iBACrB,CAAC;gBAEF,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,0BAA0B,CACxD,MAAM,EACN,+BAA+B,EAC/B,UAAU,CACX,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,YAAY,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;YACjD,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;gBAClE,UAAU;gBACV,MAAM,iBAAiB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;oBAC1C,QAAQ,CAAC,0BAA0B,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;oBAC/E,QAAQ,CAAC,0BAA0B,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;oBACjF,QAAQ,CAAC,0BAA0B,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC;iBACnF,CAAC,CAAC;gBAEH,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,KAAK,EACL,wEAAwE,CACzE,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,EAAE,CAAC,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;oBAClC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;oBACvB,UAAU,EAAE;wBACV,IAAI,EAAE,CAAC;wBACP,KAAK,EAAE,CAAC;wBACR,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACzB,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC/B;iBACF,CAAC,CAAC;gBAEH,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACpD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;gBAC5D,UAAU;gBACV,MAAM,OAAO,CAAC,GAAG,CAAC;oBAChB,QAAQ,CAAC,0BAA0B,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;oBAC7D,QAAQ,CAAC,0BAA0B,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;oBAC7D,QAAQ,CAAC,0BAA0B,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;iBAChE,CAAC,CAAC;gBAEH,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,KAAK,EACL,iDAAiD,CAClD,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,EAAE,CAAC,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/F,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;gBACxD,UAAU;gBACV,MAAM,OAAO,CAAC,GAAG,CAAC;oBAChB,QAAQ,CAAC,0BAA0B,CAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;oBACzE,QAAQ,CAAC,0BAA0B,CAAC,EAAE,IAAI,EAAE,uBAAuB,EAAE,CAAC;oBACtE,QAAQ,CAAC,0BAA0B,CAAC,EAAE,IAAI,EAAE,4BAA4B,EAAE,CAAC;iBAC5E,CAAC,CAAC;gBAEH,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,KAAK,EACL,+CAA+C,CAChD,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,EAAE,CAAC,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAW,EAAE,EAAE,CAC9C,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAC/C,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uCAAuC,EAAE,GAAG,EAAE;YACrD,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;gBACxD,UAAU;gBACV,MAAM,gBAAgB,GAAG,MAAM,QAAQ,CAAC,0BAA0B,CAAC;oBACjE,IAAI,EAAE,iBAAiB;oBACvB,WAAW,EAAE,wBAAwB;iBACtC,CAAC,CAAC;gBAEH,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,KAAK,EACL,iCAAiC,gBAAgB,CAAC,EAAE,EAAE,CACvD,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,EAAE,CAAC,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;oBAClC,EAAE,EAAE,gBAAgB,CAAC,EAAE;oBACvB,IAAI,EAAE,iBAAiB;oBACvB,WAAW,EAAE,wBAAwB;iBACtC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;gBACpE,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,KAAK,EACL,+CAA+C,CAChD,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,SAAS,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAChD,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;gBACpD,UAAU;gBACV,MAAM,OAAO,GAAG;oBACd,SAAS,EAAE,oBAAoB;oBAC/B,OAAO,EAAE,kBAAkB;oBAC3B,OAAO,EAAE;wBACP,UAAU,EAAE,eAAe;wBAC3B,UAAU,EAAE,cAAc;wBAC1B,SAAS,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;qBAC9B;oBACD,QAAQ,EAAE,EAAE;iBACb,CAAC;gBAEF,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,MAAM,EACN,6BAA6B,EAC7B,OAAO,CACR,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,OAAO,CAAC,CAAC;gBACjD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;oBAClC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAClC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACzB,SAAS,EAAE,oBAAoB;oBAC/B,OAAO,EAAE,kBAAkB;oBAC3B,MAAM,EAAE,SAAS;oBACjB,QAAQ,EAAE,EAAE;iBACb,CAAC,CAAC;gBAEH,8BAA8B;gBAC9B,MAAM,QAAQ,CAAC,mBAAmB,CAAC,mCAAY,EAC7C,EAAE,OAAO,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;gBAClD,UAAU;gBACV,MAAM,cAAc,GAAG;oBACrB,SAAS,EAAE,eAAe;oBAC1B,OAAO,EAAE,EAAE,EAAE,iBAAiB;oBAC9B,kBAAkB;iBACnB,CAAC;gBAEF,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,MAAM,EACN,6BAA6B,EAC7B,cAAc,CACf,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;YACnD,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;gBAClD,UAAU;gBACV,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,sBAAsB,CAAC;oBACzD,MAAM,EAAE,QAAQ;oBAChB,QAAQ,EAAE;wBACR,WAAW,EAAE,CAAC;wBACd,UAAU,EAAE,CAAC;wBACb,UAAU,EAAE,EAAE;wBACd,OAAO,EAAE,wBAAwB;wBACjC,OAAO,EAAE,EAAE;wBACX,KAAK,EAAE,EAAE;qBACV;iBACF,CAAC,CAAC;gBAEH,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,KAAK,EACL,+BAA+B,YAAY,CAAC,EAAE,EAAE,CACjD,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,EAAE,CAAC,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;oBAClC,GAAG,EAAE;wBACH,EAAE,EAAE,YAAY,CAAC,EAAE;wBACnB,MAAM,EAAE,QAAQ;wBAChB,QAAQ,EAAE;4BACR,WAAW,EAAE,CAAC;4BACd,UAAU,EAAE,CAAC;4BACb,UAAU,EAAE,EAAE;4BACd,OAAO,EAAE,wBAAwB;yBAClC;qBACF;oBACD,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC5B,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;YACjD,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;gBAClD,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,KAAK,EACL,+BAA+B,CAChC,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,EAAE,CAAC,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;oBAClC,MAAM,EAAE;wBACN,MAAM,EAAE,MAAM,CAAC,cAAc,CAAC,gCAAgC,CAAC;wBAC/D,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACjC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAClC,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACnC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC7B,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC9B,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC9B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;qBAC1B;oBACD,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;oBACzB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;YACxC,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;gBACjD,UAAU;gBACV,MAAM,UAAU,GAAG;oBACjB,IAAI,EAAE,aAAa;oBACnB,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,aAAa;oBACvB,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE;wBACN,OAAO,EAAE,KAAK;wBACd,WAAW,EAAE,MAAM;qBACpB;oBACD,MAAM,EAAE,YAAY;iBACrB,CAAC;gBAEF,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,MAAM,EACN,qBAAqB,EACrB,UAAU,CACX,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,OAAO,CAAC,CAAC;gBACjD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;oBAClC,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACtB,IAAI,EAAE,aAAa;oBACnB,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,aAAa;oBACvB,IAAI,EAAE,OAAO;oBACb,QAAQ,EAAE,IAAI;iBACf,CAAC,CAAC;gBAEH,8BAA8B;gBAC9B,MAAM,QAAQ,CAAC,mBAAmB,CAAC,sBAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACvC,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;gBAC9C,UAAU;gBACV,MAAM,OAAO,CAAC,GAAG,CAAC;oBAChB,QAAQ,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;oBACpE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;oBACvE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC;iBAC9E,CAAC,CAAC;gBAEH,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,KAAK,EACL,8CAA8C,CAC/C,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,EAAE,CAAC,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;oBAClC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;oBACvB,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC/B,CAAC,CAAC;gBAEH,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7F,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;YACtC,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;gBACnD,MAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,KAAK,EACL,oBAAoB,CACrB,CAAC;gBAEF,SAAS;gBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,EAAE,CAAC,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;oBAClC,MAAM,EAAE,MAAM,CAAC,cAAc,CAAC,gCAAgC,CAAC;oBAC/D,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;oBACzB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC3B,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,UAAU;YACV,MAAM,kBAAkB,GAAG,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,wBAAwB;gBAC9B,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,aAAa;aACxB,CAAC;YAEF,MAAM;YACN,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ,CAAC,oBAAoB,CAAC,KAAK,IAAI,EAAE;gBAC1F,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACnE,QAAQ,CAAC,wBAAwB,CAC/B,MAAM,EACN,+BAA+B,EAC/B,EAAE,GAAG,UAAU,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE,CACnD,CACF,CAAC;gBACF,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;YACnD,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,KAAK,mBAAU,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvF,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,oCAAoC;YAE/E,OAAO,CAAC,GAAG,CAAC,4BAA4B,kBAAkB,aAAa,EAAE;gBACvE,SAAS,EAAE,GAAG,aAAa,IAAI;gBAC/B,qBAAqB,EAAE,GAAG,aAAa,GAAG,kBAAkB,IAAI;gBAChE,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,kBAAkB,GAAG,aAAa,CAAC,GAAG,IAAI,CAAC;aAC3E,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,UAAU;YACV,MAAM,gBAAgB,GAAG,MAAM,QAAQ,CAAC,0BAA0B,EAAE,CAAC;YAErE,MAAM;YACN,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,uBAAuB,CAAC,KAAK,IAAI,EAAE;gBAClE,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,KAAK,EACL,iCAAiC,gBAAgB,CAAC,EAAE,EAAE,CACvD,CAAC;gBACF,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,EAAE,CAAC,CAAC;gBAC5C,OAAO,QAAQ,CAAC;YAClB,CAAC,EAAE,EAAE,CAAC,CAAC;YAEP,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,sBAAsB;YACvE,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB;YACnE,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE7C,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE;gBAC1C,WAAW,EAAE,GAAG,SAAS,CAAC,WAAW,IAAI;gBACzC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,IAAI;gBACjC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,IAAI;gBACjC,UAAU,EAAE,SAAS,CAAC,UAAU;aACjC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM;YACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,MAAM,EACN,+BAA+B,EAC/B,SAAS,EACT,EAAE,cAAc,EAAE,kBAAkB,EAAE,CACvC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAE3B,SAAS;YACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;gBAClC,UAAU,EAAE,GAAG;gBACf,KAAK,EAAE,aAAa;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,UAAU;YACV,MAAM,SAAS,GAAG;gBAChB,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,aAAa;gBACvB,aAAa,EAAE;oBACb,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;wBACnD,EAAE,EAAE,CAAC;wBACL,IAAI,EAAE,mBAAmB,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC;qBACzC,CAAC,CAAC;iBACJ;aACF,CAAC;YAEF,MAAM;YACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,MAAM,EACN,+BAA+B,EAC/B,SAAS,CACV,CAAC;YAEF,SAAS;YACT,qEAAqE;YACrE,MAAM,CAAC,CAAC,mBAAU,CAAC,OAAO,EAAE,mBAAU,CAAC,wBAAwB,CAAC,CAAC;iBAC9D,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,gEAAgE;YAChE,+DAA+D;YAE/D,yDAAyD;YACzD,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,KAAK,EACL,+BAA+B,CAChC,CAAC;YAEF,MAAM,CAAC,CAAC,mBAAU,CAAC,EAAE,EAAE,mBAAU,CAAC,qBAAqB,CAAC,CAAC;iBACtD,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,0DAA0D;YAC1D,kCAAkC;YAElC,kDAAkD;YAClD,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,0BAA0B,CACxD,QAAQ,EACR,uCAAuC,CACxC,CAAC;YAEF,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAU,CAAC,YAAY,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,UAAU;YACV,MAAM,aAAa,GAAG;gBACpB,IAAI,EAAE,+BAA+B;gBACrC,WAAW,EAAE,sBAAsB;gBACnC,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,UAAU;aACrB,CAAC;YAEF,MAAM;YACN,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CACtD,MAAM,EACN,+BAA+B,EAC/B,aAAa,CACd,CAAC;YAEF,SAAS;YACT,IAAI,QAAQ,CAAC,MAAM,KAAK,mBAAU,CAAC,OAAO,EAAE,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;gBACrD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAChE,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\api-endpoints.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication, HttpStatus } from '@nestjs/common';\r\nimport { BaseTestClass } from '../base/base-test.class';\r\nimport { ReportingModule } from '../../reporting.module';\r\nimport { BackgroundProcessorsModule } from '../../background-processors/background-processors.module';\r\nimport { MonitoringModule } from '../../monitoring/monitoring.module';\r\nimport { ReportDefinition } from '../../entities/report-definition.entity';\r\nimport { GeneratedReport } from '../../entities/generated-report.entity';\r\nimport { Dashboard } from '../../entities/dashboard.entity';\r\nimport { JobExecution } from '../../background-processors/entities/job-execution.entity';\r\nimport { Metric } from '../../monitoring/entities/metric.entity';\r\nimport { HealthCheck } from '../../monitoring/entities/health-check.entity';\r\nimport { Alert } from '../../monitoring/entities/alert.entity';\r\n\r\n/**\r\n * API Endpoints Integration Tests\r\n * \r\n * Comprehensive API endpoint testing providing:\r\n * - Request/response validation with comprehensive schema testing\r\n * - Authentication and authorization testing with role-based access control\r\n * - Error handling verification with proper HTTP status codes\r\n * - Pagination, filtering, and sorting functionality validation\r\n * - Input validation and sanitization testing\r\n * - Rate limiting and security controls verification\r\n * - Performance testing with response time benchmarking\r\n * - Cross-endpoint integration and workflow validation\r\n */\r\ndescribe('API Endpoints Integration Tests', () => {\r\n  let app: INestApplication;\r\n  let testingModule: TestingModule;\r\n  let baseTest: BaseTestClass;\r\n\r\n  // Test entities\r\n  const testEntities = [\r\n    ReportDefinition,\r\n    GeneratedReport,\r\n    Dashboard,\r\n    JobExecution,\r\n    Metric,\r\n    HealthCheck,\r\n    Alert,\r\n  ];\r\n\r\n  beforeAll(async () => {\r\n    // Create base test instance\r\n    baseTest = new (class extends BaseTestClass {})();\r\n\r\n    // Setup test environment\r\n    await baseTest.setupTestEnvironment(\r\n      {\r\n        imports: [\r\n          ReportingModule,\r\n          BackgroundProcessorsModule,\r\n          MonitoringModule,\r\n        ],\r\n      },\r\n      testEntities\r\n    );\r\n\r\n    app = baseTest.app;\r\n    testingModule = baseTest.testingModule;\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await baseTest.cleanupTestEnvironment();\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    await baseTest.setupTestData();\r\n  });\r\n\r\n  afterEach(async () => {\r\n    await baseTest.cleanupTestData();\r\n  });\r\n\r\n  describe('Report Definition Endpoints', () => {\r\n    describe('POST /reporting/report-definitions', () => {\r\n      it('should create a new report definition with valid data', async () => {\r\n        // Arrange\r\n        const reportData = {\r\n          name: 'Test Security Report',\r\n          description: 'A comprehensive security analysis report',\r\n          type: 'dashboard',\r\n          category: 'security',\r\n          configuration: {\r\n            dataSource: 'database',\r\n            refreshInterval: 3600,\r\n            filters: {\r\n              dateRange: {\r\n                start: '2024-01-01T00:00:00Z',\r\n                end: '2024-12-31T23:59:59Z',\r\n              },\r\n              categories: ['security', 'compliance'],\r\n            },\r\n            visualization: {\r\n              type: 'dashboard',\r\n              layout: 'grid',\r\n              theme: 'default',\r\n            },\r\n          },\r\n          schedule: {\r\n            enabled: true,\r\n            frequency: 'daily',\r\n            time: '09:00',\r\n            timezone: 'UTC',\r\n            recipients: ['<EMAIL>'],\r\n          },\r\n        };\r\n\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'post',\r\n          '/reporting/report-definitions',\r\n          reportData\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.CREATED);\r\n        expect(response.body).toMatchObject({\r\n          id: expect.any(String),\r\n          name: reportData.name,\r\n          description: reportData.description,\r\n          type: reportData.type,\r\n          category: reportData.category,\r\n          isActive: true,\r\n          createdBy: baseTest.testUser.id,\r\n        });\r\n\r\n        baseTest.assertResponseStructure(response.body, {\r\n          id: 'string',\r\n          name: 'string',\r\n          description: 'string',\r\n          type: 'string',\r\n          category: 'string',\r\n          configuration: 'object',\r\n          schedule: 'object',\r\n          isActive: 'boolean',\r\n          createdBy: 'string',\r\n          createdAt: 'string',\r\n          updatedAt: 'string',\r\n        });\r\n\r\n        // Verify database persistence\r\n        await baseTest.assertDatabaseState(ReportDefinition, { name: reportData.name }, 1);\r\n      });\r\n\r\n      it('should return 400 for invalid report definition data', async () => {\r\n        // Arrange\r\n        const invalidData = {\r\n          name: '', // Invalid: empty name\r\n          type: 'invalid_type', // Invalid: not in enum\r\n          configuration: 'invalid', // Invalid: should be object\r\n        };\r\n\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'post',\r\n          '/reporting/report-definitions',\r\n          invalidData\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.BAD_REQUEST);\r\n        expect(response.body).toMatchObject({\r\n          statusCode: 400,\r\n          message: expect.any(Array),\r\n          error: 'Bad Request',\r\n        });\r\n      });\r\n\r\n      it('should return 401 for unauthenticated requests', async () => {\r\n        // Arrange\r\n        const reportData = {\r\n          name: 'Unauthorized Report',\r\n          type: 'dashboard',\r\n          category: 'security',\r\n        };\r\n\r\n        // Act\r\n        const response = await baseTest.makeUnauthenticatedRequest(\r\n          'post',\r\n          '/reporting/report-definitions',\r\n          reportData\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.UNAUTHORIZED);\r\n      });\r\n    });\r\n\r\n    describe('GET /reporting/report-definitions', () => {\r\n      it('should return paginated list of report definitions', async () => {\r\n        // Arrange\r\n        const reportDefinitions = await Promise.all([\r\n          baseTest.createTestReportDefinition({ name: 'Report 1', category: 'security' }),\r\n          baseTest.createTestReportDefinition({ name: 'Report 2', category: 'compliance' }),\r\n          baseTest.createTestReportDefinition({ name: 'Report 3', category: 'performance' }),\r\n        ]);\r\n\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'get',\r\n          '/reporting/report-definitions?page=1&limit=2&sortBy=name&sortOrder=asc'\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.OK);\r\n        expect(response.body).toMatchObject({\r\n          data: expect.any(Array),\r\n          pagination: {\r\n            page: 1,\r\n            limit: 2,\r\n            total: expect.any(Number),\r\n            totalPages: expect.any(Number),\r\n          },\r\n        });\r\n\r\n        expect(response.body.data).toHaveLength(2);\r\n        expect(response.body.data[0].name).toBe('Report 1');\r\n        expect(response.body.data[1].name).toBe('Report 2');\r\n      });\r\n\r\n      it('should filter report definitions by category', async () => {\r\n        // Arrange\r\n        await Promise.all([\r\n          baseTest.createTestReportDefinition({ category: 'security' }),\r\n          baseTest.createTestReportDefinition({ category: 'security' }),\r\n          baseTest.createTestReportDefinition({ category: 'compliance' }),\r\n        ]);\r\n\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'get',\r\n          '/reporting/report-definitions?category=security'\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.OK);\r\n        expect(response.body.data).toHaveLength(2);\r\n        expect(response.body.data.every((report: any) => report.category === 'security')).toBe(true);\r\n      });\r\n\r\n      it('should search report definitions by name', async () => {\r\n        // Arrange\r\n        await Promise.all([\r\n          baseTest.createTestReportDefinition({ name: 'Security Analysis Report' }),\r\n          baseTest.createTestReportDefinition({ name: 'Performance Dashboard' }),\r\n          baseTest.createTestReportDefinition({ name: 'Security Compliance Report' }),\r\n        ]);\r\n\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'get',\r\n          '/reporting/report-definitions?search=Security'\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.OK);\r\n        expect(response.body.data).toHaveLength(2);\r\n        expect(response.body.data.every((report: any) => \r\n          report.name.toLowerCase().includes('security')\r\n        )).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('GET /reporting/report-definitions/:id', () => {\r\n      it('should return specific report definition', async () => {\r\n        // Arrange\r\n        const reportDefinition = await baseTest.createTestReportDefinition({\r\n          name: 'Detailed Report',\r\n          description: 'A detailed test report',\r\n        });\r\n\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'get',\r\n          `/reporting/report-definitions/${reportDefinition.id}`\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.OK);\r\n        expect(response.body).toMatchObject({\r\n          id: reportDefinition.id,\r\n          name: 'Detailed Report',\r\n          description: 'A detailed test report',\r\n        });\r\n      });\r\n\r\n      it('should return 404 for non-existent report definition', async () => {\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'get',\r\n          '/reporting/report-definitions/non-existent-id'\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.NOT_FOUND);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Background Processors Endpoints', () => {\r\n    describe('POST /background-processors/jobs', () => {\r\n      it('should add job to queue successfully', async () => {\r\n        // Arrange\r\n        const jobData = {\r\n          queueName: 'workflow-execution',\r\n          jobType: 'execute-workflow',\r\n          jobData: {\r\n            workflowId: 'test-workflow',\r\n            templateId: 'template-123',\r\n            inputData: { param: 'value' },\r\n          },\r\n          priority: 10,\r\n        };\r\n\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'post',\r\n          '/background-processors/jobs',\r\n          jobData\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.CREATED);\r\n        expect(response.body).toMatchObject({\r\n          jobExecutionId: expect.any(String),\r\n          jobId: expect.any(String),\r\n          queueName: 'workflow-execution',\r\n          jobType: 'execute-workflow',\r\n          status: 'pending',\r\n          priority: 10,\r\n        });\r\n\r\n        // Verify database persistence\r\n        await baseTest.assertDatabaseState(JobExecution, \r\n          { jobType: 'execute-workflow' }, 1);\r\n      });\r\n\r\n      it('should validate job data structure', async () => {\r\n        // Arrange\r\n        const invalidJobData = {\r\n          queueName: 'invalid-queue',\r\n          jobType: '', // Invalid: empty\r\n          // Missing jobData\r\n        };\r\n\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'post',\r\n          '/background-processors/jobs',\r\n          invalidJobData\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.BAD_REQUEST);\r\n      });\r\n    });\r\n\r\n    describe('GET /background-processors/jobs/:id', () => {\r\n      it('should return job execution status', async () => {\r\n        // Arrange\r\n        const jobExecution = await baseTest.createTestJobExecution({\r\n          status: 'active',\r\n          progress: {\r\n            currentStep: 2,\r\n            totalSteps: 5,\r\n            percentage: 40,\r\n            message: 'Processing step 2 of 5',\r\n            details: {},\r\n            steps: [],\r\n          },\r\n        });\r\n\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'get',\r\n          `/background-processors/jobs/${jobExecution.id}`\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.OK);\r\n        expect(response.body).toMatchObject({\r\n          job: {\r\n            id: jobExecution.id,\r\n            status: 'active',\r\n            progress: {\r\n              currentStep: 2,\r\n              totalSteps: 5,\r\n              percentage: 40,\r\n              message: 'Processing step 2 of 5',\r\n            },\r\n          },\r\n          metrics: expect.any(Object),\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('GET /background-processors/health', () => {\r\n      it('should return system health status', async () => {\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'get',\r\n          '/background-processors/health'\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.OK);\r\n        expect(response.body).toMatchObject({\r\n          health: {\r\n            status: expect.stringMatching(/^(healthy|degraded|unhealthy)$/),\r\n            queues: expect.any(Number),\r\n            healthyQueues: expect.any(Number),\r\n            degradedQueues: expect.any(Number),\r\n            unhealthyQueues: expect.any(Number),\r\n            totalJobs: expect.any(Number),\r\n            activeJobs: expect.any(Number),\r\n            failedJobs: expect.any(Number),\r\n            issues: expect.any(Array),\r\n          },\r\n          queues: expect.any(Array),\r\n          timestamp: expect.any(String),\r\n        });\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Monitoring Endpoints', () => {\r\n    describe('POST /monitoring/metrics', () => {\r\n      it('should record metric successfully', async () => {\r\n        // Arrange\r\n        const metricData = {\r\n          name: 'test_metric',\r\n          value: 42.5,\r\n          category: 'performance',\r\n          type: 'gauge',\r\n          unit: 'seconds',\r\n          labels: {\r\n            service: 'api',\r\n            environment: 'test',\r\n          },\r\n          source: 'test_suite',\r\n        };\r\n\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'post',\r\n          '/monitoring/metrics',\r\n          metricData\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.CREATED);\r\n        expect(response.body).toMatchObject({\r\n          id: expect.any(String),\r\n          name: 'test_metric',\r\n          value: 42.5,\r\n          category: 'performance',\r\n          type: 'gauge',\r\n          recorded: true,\r\n        });\r\n\r\n        // Verify database persistence\r\n        await baseTest.assertDatabaseState(Metric, { name: 'test_metric' }, 1);\r\n      });\r\n    });\r\n\r\n    describe('GET /monitoring/metrics', () => {\r\n      it('should return filtered metrics', async () => {\r\n        // Arrange\r\n        await Promise.all([\r\n          baseTest.createTestMetric({ name: 'cpu_usage', category: 'system' }),\r\n          baseTest.createTestMetric({ name: 'memory_usage', category: 'system' }),\r\n          baseTest.createTestMetric({ name: 'response_time', category: 'performance' }),\r\n        ]);\r\n\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'get',\r\n          '/monitoring/metrics?category=system&limit=10'\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.OK);\r\n        expect(response.body).toMatchObject({\r\n          data: expect.any(Array),\r\n          pagination: expect.any(Object),\r\n        });\r\n\r\n        expect(response.body.data).toHaveLength(2);\r\n        expect(response.body.data.every((metric: any) => metric.category === 'system')).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('GET /monitoring/health', () => {\r\n      it('should return overall system health', async () => {\r\n        // Act\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'get',\r\n          '/monitoring/health'\r\n        );\r\n\r\n        // Assert\r\n        expect(response.status).toBe(HttpStatus.OK);\r\n        expect(response.body).toMatchObject({\r\n          status: expect.stringMatching(/^(healthy|degraded|unhealthy)$/),\r\n          checks: expect.any(Array),\r\n          timestamp: expect.any(String),\r\n          uptime: expect.any(Number),\r\n        });\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Performance Testing', () => {\r\n    it('should handle concurrent API requests', async () => {\r\n      // Arrange\r\n      const concurrentRequests = 20;\r\n      const reportData = {\r\n        name: 'Concurrent Test Report',\r\n        type: 'dashboard',\r\n        category: 'performance',\r\n      };\r\n\r\n      // Act\r\n      const { result: responses, executionTime } = await baseTest.measureExecutionTime(async () => {\r\n        const promises = Array.from({ length: concurrentRequests }, (_, i) =>\r\n          baseTest.makeAuthenticatedRequest(\r\n            'post',\r\n            '/reporting/report-definitions',\r\n            { ...reportData, name: `${reportData.name} ${i}` }\r\n          )\r\n        );\r\n        return await Promise.all(promises);\r\n      });\r\n\r\n      // Assert\r\n      expect(responses).toHaveLength(concurrentRequests);\r\n      expect(responses.every(response => response.status === HttpStatus.CREATED)).toBe(true);\r\n      expect(executionTime).toBeLessThan(10000); // Should complete within 10 seconds\r\n\r\n      console.log(`Concurrent API Requests (${concurrentRequests} requests):`, {\r\n        totalTime: `${executionTime}ms`,\r\n        averageTimePerRequest: `${executionTime / concurrentRequests}ms`,\r\n        requestsPerSecond: Math.round((concurrentRequests / executionTime) * 1000),\r\n      });\r\n    });\r\n\r\n    it('should meet response time requirements', async () => {\r\n      // Arrange\r\n      const reportDefinition = await baseTest.createTestReportDefinition();\r\n\r\n      // Act\r\n      const benchmark = await baseTest.runPerformanceBenchmark(async () => {\r\n        const response = await baseTest.makeAuthenticatedRequest(\r\n          'get',\r\n          `/reporting/report-definitions/${reportDefinition.id}`\r\n        );\r\n        expect(response.status).toBe(HttpStatus.OK);\r\n        return response;\r\n      }, 50);\r\n\r\n      // Assert\r\n      expect(benchmark.averageTime).toBeLessThan(500); // Average under 500ms\r\n      expect(benchmark.maxTime).toBeLessThan(1000); // Max under 1 second\r\n      expect(benchmark.minTime).toBeGreaterThan(0);\r\n\r\n      console.log('API Response Time Benchmark:', {\r\n        averageTime: `${benchmark.averageTime}ms`,\r\n        minTime: `${benchmark.minTime}ms`,\r\n        maxTime: `${benchmark.maxTime}ms`,\r\n        iterations: benchmark.iterations,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Error Handling', () => {\r\n    it('should handle malformed JSON requests', async () => {\r\n      // Act\r\n      const response = await baseTest.makeAuthenticatedRequest(\r\n        'post',\r\n        '/reporting/report-definitions',\r\n        undefined,\r\n        { 'Content-Type': 'application/json' }\r\n      ).send('{ invalid json }');\r\n\r\n      // Assert\r\n      expect(response.status).toBe(HttpStatus.BAD_REQUEST);\r\n      expect(response.body).toMatchObject({\r\n        statusCode: 400,\r\n        error: 'Bad Request',\r\n      });\r\n    });\r\n\r\n    it('should handle large request payloads', async () => {\r\n      // Arrange\r\n      const largeData = {\r\n        name: 'Large Report',\r\n        type: 'dashboard',\r\n        category: 'performance',\r\n        configuration: {\r\n          largeArray: Array.from({ length: 10000 }, (_, i) => ({\r\n            id: i,\r\n            data: `Large data item ${i}`.repeat(100),\r\n          })),\r\n        },\r\n      };\r\n\r\n      // Act\r\n      const response = await baseTest.makeAuthenticatedRequest(\r\n        'post',\r\n        '/reporting/report-definitions',\r\n        largeData\r\n      );\r\n\r\n      // Assert\r\n      // Should either succeed or return appropriate error for payload size\r\n      expect([HttpStatus.CREATED, HttpStatus.REQUEST_ENTITY_TOO_LARGE])\r\n        .toContain(response.status);\r\n    });\r\n\r\n    it('should handle database connection errors gracefully', async () => {\r\n      // Note: This would require mocking database connection failures\r\n      // Implementation depends on specific database mocking strategy\r\n      \r\n      // For now, verify that the endpoint structure is correct\r\n      const response = await baseTest.makeAuthenticatedRequest(\r\n        'get',\r\n        '/reporting/report-definitions'\r\n      );\r\n\r\n      expect([HttpStatus.OK, HttpStatus.INTERNAL_SERVER_ERROR])\r\n        .toContain(response.status);\r\n    });\r\n  });\r\n\r\n  describe('Security Testing', () => {\r\n    it('should enforce role-based access control', async () => {\r\n      // This test would require setting up different user roles\r\n      // and testing access restrictions\r\n      \r\n      // For now, verify that authentication is required\r\n      const response = await baseTest.makeUnauthenticatedRequest(\r\n        'delete',\r\n        '/reporting/report-definitions/test-id'\r\n      );\r\n\r\n      expect(response.status).toBe(HttpStatus.UNAUTHORIZED);\r\n    });\r\n\r\n    it('should sanitize input data', async () => {\r\n      // Arrange\r\n      const maliciousData = {\r\n        name: '<script>alert(\"xss\")</script>',\r\n        description: 'DROP TABLE users; --',\r\n        type: 'dashboard',\r\n        category: 'security',\r\n      };\r\n\r\n      // Act\r\n      const response = await baseTest.makeAuthenticatedRequest(\r\n        'post',\r\n        '/reporting/report-definitions',\r\n        maliciousData\r\n      );\r\n\r\n      // Assert\r\n      if (response.status === HttpStatus.CREATED) {\r\n        expect(response.body.name).not.toContain('<script>');\r\n        expect(response.body.description).not.toContain('DROP TABLE');\r\n      }\r\n    });\r\n  });\r\n});\r\n"], "version": 3}