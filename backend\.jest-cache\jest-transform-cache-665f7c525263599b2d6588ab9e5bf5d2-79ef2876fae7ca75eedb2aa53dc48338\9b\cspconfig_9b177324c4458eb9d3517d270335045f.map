{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\csp.config.ts", "mappings": ";;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAE/C;;;GAGG;AAEI,IAAM,SAAS,GAAf,MAAM,SAAS;IACpB,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAE7D;;OAEG;IACH,iBAAiB;QACf,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAEtE,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,YAAY;gBACf,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACjC,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9B,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3B;gBACE,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,MAAM,UAAU,GAAG;YACjB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,YAAY,EAAE;gBACZ,QAAQ;gBACR,iBAAiB,EAAE,kDAAkD;gBACrE,0BAA0B;gBAC1B,mBAAmB;aACpB;YACD,WAAW,EAAE;gBACX,QAAQ;gBACR,iBAAiB,EAAE,8BAA8B;gBACjD,8BAA8B;gBAC9B,0BAA0B;aAC3B;YACD,SAAS,EAAE;gBACT,QAAQ;gBACR,OAAO;gBACP,QAAQ;gBACR,OAAO;aACR;YACD,UAAU,EAAE;gBACV,QAAQ;gBACR,2BAA2B;gBAC3B,0BAA0B;gBAC1B,OAAO;aACR;YACD,aAAa,EAAE;gBACb,QAAQ;gBACR,0BAA0B;gBAC1B,wBAAwB;gBACxB,yBAAyB;gBACzB,qBAAqB;aACtB;YACD,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,YAAY,EAAE,CAAC,QAAQ,CAAC;YACxB,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,YAAY,EAAE,CAAC,QAAQ,CAAC;YACxB,cAAc,EAAE,CAAC,QAAQ,CAAC;YAC1B,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,iBAAiB,EAAE,CAAC,QAAQ,CAAC;YAC7B,2BAA2B,EAAE,EAAE;YAC/B,yBAAyB,EAAE,EAAE;SAC9B,CAAC;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,MAAM,UAAU,GAAG;YACjB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,YAAY,EAAE;gBACZ,QAAQ;gBACR,iBAAiB;gBACjB,eAAe,EAAE,2BAA2B;gBAC5C,0BAA0B;gBAC1B,mBAAmB;gBACnB,kCAAkC;aACnC;YACD,WAAW,EAAE;gBACX,QAAQ;gBACR,iBAAiB;gBACjB,8BAA8B;gBAC9B,0BAA0B;gBAC1B,kCAAkC;aACnC;YACD,SAAS,EAAE;gBACT,QAAQ;gBACR,OAAO;gBACP,QAAQ;gBACR,OAAO;gBACP,qCAAqC;aACtC;YACD,UAAU,EAAE;gBACV,QAAQ;gBACR,2BAA2B;gBAC3B,0BAA0B;gBAC1B,OAAO;aACR;YACD,aAAa,EAAE;gBACb,QAAQ;gBACR,kCAAkC;gBAClC,gCAAgC;gBAChC,yBAAyB;gBACzB,qBAAqB;gBACrB,sCAAsC;aACvC;YACD,WAAW,EAAE,CAAC,QAAQ,EAAE,oCAAoC,CAAC;YAC7D,YAAY,EAAE,CAAC,QAAQ,CAAC;YACxB,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,YAAY,EAAE,CAAC,QAAQ,CAAC;YACxB,cAAc,EAAE,CAAC,QAAQ,CAAC;YAC1B,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,iBAAiB,EAAE,CAAC,QAAQ,CAAC;YAC7B,YAAY,EAAE,CAAC,6BAA6B,CAAC;SAC9C,CAAC;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,MAAM,UAAU,GAAG;YACjB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,YAAY,EAAE;gBACZ,QAAQ;gBACR,iBAAiB;gBACjB,eAAe;gBACf,QAAQ;gBACR,OAAO;gBACP,aAAa;gBACb,aAAa;aACd;YACD,WAAW,EAAE;gBACX,QAAQ;gBACR,iBAAiB;gBACjB,QAAQ;gBACR,OAAO;gBACP,aAAa;gBACb,aAAa;aACd;YACD,SAAS,EAAE;gBACT,QAAQ;gBACR,OAAO;gBACP,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,aAAa;aACd;YACD,UAAU,EAAE;gBACV,QAAQ;gBACR,OAAO;gBACP,QAAQ;gBACR,OAAO;gBACP,aAAa;gBACb,aAAa;aACd;YACD,aAAa,EAAE;gBACb,QAAQ;gBACR,QAAQ;gBACR,OAAO;gBACP,KAAK;gBACL,MAAM;gBACN,aAAa;gBACb,aAAa;aACd;YACD,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,CAAC;YACxE,YAAY,EAAE,CAAC,QAAQ,CAAC;YACxB,WAAW,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,aAAa,CAAC;YACrD,WAAW,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,aAAa,CAAC;YACrD,YAAY,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;YACjC,cAAc,EAAE,CAAC,QAAQ,CAAC;YAC1B,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,YAAY,EAAE,CAAC,6BAA6B,CAAC;SAC9C,CAAC;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,MAAM,UAAU,GAAG;YACjB,aAAa,EAAE,CAAC,QAAQ,EAAE,iBAAiB,EAAE,eAAe,CAAC;YAC7D,YAAY,EAAE,CAAC,QAAQ,EAAE,iBAAiB,EAAE,eAAe,CAAC;YAC5D,WAAW,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;YAC1C,SAAS,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;YACvC,UAAU,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;YAC/B,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,YAAY,EAAE,CAAC,QAAQ,CAAC;YACxB,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,aAAa,EAAE,CAAC,QAAQ,CAAC;SAC1B,CAAC;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,UAAoC;QACzD,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,8DAA8D;gBAC9D,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,YAAoB;QACzC,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzC,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,KAAK;gBACR,gDAAgD;gBAChD,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9B,KAAK,SAAS;gBACZ,qCAAqC;gBACrC,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9B,KAAK,QAAQ;gBACX,0CAA0C;gBAC1C,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAClC;gBACE,OAAO,OAAO,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,MAAM,UAAU,GAAG;YACjB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,iBAAiB,EAAE,CAAC,QAAQ,CAAC;YAC7B,YAAY,EAAE,CAAC,QAAQ,CAAC;SACzB,CAAC;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,MAAM,UAAU,GAAG;YACjB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,iBAAiB,EAAE,CAAC,QAAQ,CAAC;YAC7B,YAAY,EAAE,CAAC,QAAQ,CAAC;SACzB,CAAC;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,MAAM,UAAU,GAAG;YACjB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,aAAa,EAAE,CAAC,QAAQ,CAAC;YACzB,iBAAiB,EAAE,CAAC,QAAQ,CAAC;YAC7B,YAAY,EAAE,CAAC,QAAQ,CAAC;SACzB,CAAC;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAErC,6CAA6C;YAC7C,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACzC,CAAC;YAED,+CAA+C;YAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;YACtE,IAAI,WAAW,KAAK,YAAY,EAAE,CAAC;gBACjC,IAAI,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;oBAClC,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;gBAC9D,CAAC;gBAED,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACjD,OAAO,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;gBAC3E,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACrE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QACtE,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAErC,OAAO;YACL,WAAW;YACX,cAAc,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM;YACrC,eAAe,EAAE,GAAG,CAAC,QAAQ,CAAC,iBAAiB,CAAC;YAChD,aAAa,EAAE,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC;YAC5C,YAAY,EAAE,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC;YACxC,MAAM,EAAE,GAAG,CAAC,MAAM;SACnB,CAAC;IACJ,CAAC;CACF,CAAA;AA3VY,8BAAS;oBAAT,SAAS;IADrB,IAAA,mBAAU,GAAE;yDAEiC,sBAAa,oBAAb,sBAAa;GAD9C,SAAS,CA2VrB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\csp.config.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\n\r\n/**\r\n * Content Security Policy configuration service\r\n * Implements comprehensive CSP directives for XSS protection\r\n */\r\n@Injectable()\r\nexport class CspConfig {\r\n  constructor(private readonly configService: ConfigService) {}\r\n\r\n  /**\r\n   * Generate Content Security Policy header value\r\n   */\r\n  generateCSPHeader(): string {\r\n    const environment = this.configService.get('NODE_ENV', 'development');\r\n    \r\n    switch (environment) {\r\n      case 'production':\r\n        return this.getProductionCSP();\r\n      case 'staging':\r\n        return this.getStagingCSP();\r\n      case 'test':\r\n        return this.getTestCSP();\r\n      default:\r\n        return this.getDevelopmentCSP();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Production CSP - Most restrictive\r\n   */\r\n  private getProductionCSP(): string {\r\n    const directives = {\r\n      'default-src': [\"'self'\"],\r\n      'script-src': [\r\n        \"'self'\",\r\n        \"'unsafe-inline'\", // Required for some frameworks, consider removing\r\n        'https://cdn.jsdelivr.net',\r\n        'https://unpkg.com',\r\n      ],\r\n      'style-src': [\r\n        \"'self'\",\r\n        \"'unsafe-inline'\", // Required for dynamic styles\r\n        'https://fonts.googleapis.com',\r\n        'https://cdn.jsdelivr.net',\r\n      ],\r\n      'img-src': [\r\n        \"'self'\",\r\n        'data:',\r\n        'https:',\r\n        'blob:',\r\n      ],\r\n      'font-src': [\r\n        \"'self'\",\r\n        'https://fonts.gstatic.com',\r\n        'https://cdn.jsdelivr.net',\r\n        'data:',\r\n      ],\r\n      'connect-src': [\r\n        \"'self'\",\r\n        'https://api.sentinel.com',\r\n        'wss://api.sentinel.com',\r\n        'https://*.amazonaws.com',\r\n        'https://*.azure.com',\r\n      ],\r\n      'media-src': [\"'self'\"],\r\n      'object-src': [\"'none'\"],\r\n      'child-src': [\"'none'\"],\r\n      'frame-src': [\"'none'\"],\r\n      'worker-src': [\"'self'\"],\r\n      'manifest-src': [\"'self'\"],\r\n      'base-uri': [\"'self'\"],\r\n      'form-action': [\"'self'\"],\r\n      'frame-ancestors': [\"'none'\"],\r\n      'upgrade-insecure-requests': [],\r\n      'block-all-mixed-content': [],\r\n    };\r\n\r\n    return this.buildCSPString(directives);\r\n  }\r\n\r\n  /**\r\n   * Staging CSP - Moderate restrictions with debugging support\r\n   */\r\n  private getStagingCSP(): string {\r\n    const directives = {\r\n      'default-src': [\"'self'\"],\r\n      'script-src': [\r\n        \"'self'\",\r\n        \"'unsafe-inline'\",\r\n        \"'unsafe-eval'\", // Allow eval for debugging\r\n        'https://cdn.jsdelivr.net',\r\n        'https://unpkg.com',\r\n        'https://staging-cdn.sentinel.com',\r\n      ],\r\n      'style-src': [\r\n        \"'self'\",\r\n        \"'unsafe-inline'\",\r\n        'https://fonts.googleapis.com',\r\n        'https://cdn.jsdelivr.net',\r\n        'https://staging-cdn.sentinel.com',\r\n      ],\r\n      'img-src': [\r\n        \"'self'\",\r\n        'data:',\r\n        'https:',\r\n        'blob:',\r\n        'https://staging-assets.sentinel.com',\r\n      ],\r\n      'font-src': [\r\n        \"'self'\",\r\n        'https://fonts.gstatic.com',\r\n        'https://cdn.jsdelivr.net',\r\n        'data:',\r\n      ],\r\n      'connect-src': [\r\n        \"'self'\",\r\n        'https://staging-api.sentinel.com',\r\n        'wss://staging-api.sentinel.com',\r\n        'https://*.amazonaws.com',\r\n        'https://*.azure.com',\r\n        'https://staging-metrics.sentinel.com',\r\n      ],\r\n      'media-src': [\"'self'\", 'https://staging-media.sentinel.com'],\r\n      'object-src': [\"'none'\"],\r\n      'child-src': [\"'self'\"],\r\n      'frame-src': [\"'self'\"],\r\n      'worker-src': [\"'self'\"],\r\n      'manifest-src': [\"'self'\"],\r\n      'base-uri': [\"'self'\"],\r\n      'form-action': [\"'self'\"],\r\n      'frame-ancestors': [\"'self'\"],\r\n      'report-uri': ['/api/v1/security/csp-report'],\r\n    };\r\n\r\n    return this.buildCSPString(directives);\r\n  }\r\n\r\n  /**\r\n   * Development CSP - Permissive for development workflow\r\n   */\r\n  private getDevelopmentCSP(): string {\r\n    const directives = {\r\n      'default-src': [\"'self'\"],\r\n      'script-src': [\r\n        \"'self'\",\r\n        \"'unsafe-inline'\",\r\n        \"'unsafe-eval'\",\r\n        'https:',\r\n        'http:',\r\n        'localhost:*',\r\n        '127.0.0.1:*',\r\n      ],\r\n      'style-src': [\r\n        \"'self'\",\r\n        \"'unsafe-inline'\",\r\n        'https:',\r\n        'http:',\r\n        'localhost:*',\r\n        '127.0.0.1:*',\r\n      ],\r\n      'img-src': [\r\n        \"'self'\",\r\n        'data:',\r\n        'https:',\r\n        'http:',\r\n        'blob:',\r\n        'localhost:*',\r\n        '127.0.0.1:*',\r\n      ],\r\n      'font-src': [\r\n        \"'self'\",\r\n        'data:',\r\n        'https:',\r\n        'http:',\r\n        'localhost:*',\r\n        '127.0.0.1:*',\r\n      ],\r\n      'connect-src': [\r\n        \"'self'\",\r\n        'https:',\r\n        'http:',\r\n        'ws:',\r\n        'wss:',\r\n        'localhost:*',\r\n        '127.0.0.1:*',\r\n      ],\r\n      'media-src': [\"'self'\", 'https:', 'http:', 'localhost:*', '127.0.0.1:*'],\r\n      'object-src': [\"'none'\"],\r\n      'child-src': [\"'self'\", 'localhost:*', '127.0.0.1:*'],\r\n      'frame-src': [\"'self'\", 'localhost:*', '127.0.0.1:*'],\r\n      'worker-src': [\"'self'\", 'blob:'],\r\n      'manifest-src': [\"'self'\"],\r\n      'base-uri': [\"'self'\"],\r\n      'form-action': [\"'self'\"],\r\n      'report-uri': ['/api/v1/security/csp-report'],\r\n    };\r\n\r\n    return this.buildCSPString(directives);\r\n  }\r\n\r\n  /**\r\n   * Test CSP - Minimal restrictions for testing\r\n   */\r\n  private getTestCSP(): string {\r\n    const directives = {\r\n      'default-src': [\"'self'\", \"'unsafe-inline'\", \"'unsafe-eval'\"],\r\n      'script-src': [\"'self'\", \"'unsafe-inline'\", \"'unsafe-eval'\"],\r\n      'style-src': [\"'self'\", \"'unsafe-inline'\"],\r\n      'img-src': [\"'self'\", 'data:', 'blob:'],\r\n      'font-src': [\"'self'\", 'data:'],\r\n      'connect-src': [\"'self'\"],\r\n      'media-src': [\"'self'\"],\r\n      'object-src': [\"'none'\"],\r\n      'base-uri': [\"'self'\"],\r\n      'form-action': [\"'self'\"],\r\n    };\r\n\r\n    return this.buildCSPString(directives);\r\n  }\r\n\r\n  /**\r\n   * Build CSP string from directives object\r\n   */\r\n  private buildCSPString(directives: Record<string, string[]>): string {\r\n    const cspParts: string[] = [];\r\n\r\n    for (const [directive, sources] of Object.entries(directives)) {\r\n      if (sources.length === 0) {\r\n        // Directives without sources (like upgrade-insecure-requests)\r\n        cspParts.push(directive);\r\n      } else {\r\n        cspParts.push(`${directive} ${sources.join(' ')}`);\r\n      }\r\n    }\r\n\r\n    return cspParts.join('; ');\r\n  }\r\n\r\n  /**\r\n   * Get CSP configuration for specific endpoint types\r\n   */\r\n  getEndpointSpecificCSP(endpointType: string): string {\r\n    const baseCSP = this.generateCSPHeader();\r\n\r\n    switch (endpointType) {\r\n      case 'api':\r\n        // API endpoints don't need script/style sources\r\n        return this.getAPIOnlyCSP();\r\n      case 'webhook':\r\n        // Webhook endpoints need minimal CSP\r\n        return this.getWebhookCSP();\r\n      case 'health':\r\n        // Health check endpoints need minimal CSP\r\n        return this.getHealthCheckCSP();\r\n      default:\r\n        return baseCSP;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * API-only CSP for JSON endpoints\r\n   */\r\n  private getAPIOnlyCSP(): string {\r\n    const directives = {\r\n      'default-src': [\"'none'\"],\r\n      'connect-src': [\"'self'\"],\r\n      'base-uri': [\"'none'\"],\r\n      'form-action': [\"'none'\"],\r\n      'frame-ancestors': [\"'none'\"],\r\n      'object-src': [\"'none'\"],\r\n    };\r\n\r\n    return this.buildCSPString(directives);\r\n  }\r\n\r\n  /**\r\n   * Webhook CSP for external integrations\r\n   */\r\n  private getWebhookCSP(): string {\r\n    const directives = {\r\n      'default-src': [\"'none'\"],\r\n      'connect-src': [\"'self'\"],\r\n      'base-uri': [\"'none'\"],\r\n      'form-action': [\"'self'\"],\r\n      'frame-ancestors': [\"'none'\"],\r\n      'object-src': [\"'none'\"],\r\n    };\r\n\r\n    return this.buildCSPString(directives);\r\n  }\r\n\r\n  /**\r\n   * Health check CSP\r\n   */\r\n  private getHealthCheckCSP(): string {\r\n    const directives = {\r\n      'default-src': [\"'none'\"],\r\n      'base-uri': [\"'none'\"],\r\n      'form-action': [\"'none'\"],\r\n      'frame-ancestors': [\"'none'\"],\r\n      'object-src': [\"'none'\"],\r\n    };\r\n\r\n    return this.buildCSPString(directives);\r\n  }\r\n\r\n  /**\r\n   * Validate CSP configuration\r\n   */\r\n  validateCSPConfig(): boolean {\r\n    try {\r\n      const csp = this.generateCSPHeader();\r\n      \r\n      // Basic validation - ensure CSP is not empty\r\n      if (!csp || csp.trim().length === 0) {\r\n        throw new Error('CSP header is empty');\r\n      }\r\n\r\n      // Check for dangerous directives in production\r\n      const environment = this.configService.get('NODE_ENV', 'development');\r\n      if (environment === 'production') {\r\n        if (csp.includes(\"'unsafe-eval'\")) {\r\n          console.warn('CSP: unsafe-eval detected in production CSP');\r\n        }\r\n        \r\n        if (csp.includes('*') && !csp.includes('https:')) {\r\n          console.warn('CSP: Wildcard sources detected without HTTPS restriction');\r\n        }\r\n      }\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('CSP configuration validation failed:', error.message);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get CSP configuration summary for logging\r\n   */\r\n  getCSPConfigSummary(): Record<string, any> {\r\n    const environment = this.configService.get('NODE_ENV', 'development');\r\n    const csp = this.generateCSPHeader();\r\n    \r\n    return {\r\n      environment,\r\n      directiveCount: csp.split(';').length,\r\n      hasUnsafeInline: csp.includes(\"'unsafe-inline'\"),\r\n      hasUnsafeEval: csp.includes(\"'unsafe-eval'\"),\r\n      hasReportUri: csp.includes('report-uri'),\r\n      length: csp.length,\r\n    };\r\n  }\r\n}"], "version": 3}