{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\api\\controllers\\__tests__\\vulnerability-assessment.controller.integration.spec.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAAsD;AAEtD,6CAAgD;AAChD,2CAA8C;AAC9C,mDAAqC;AAErC,6CAAqD;AAErD,gGAA2F;AAC3F,qHAAgH;AAChH,wFAA8E;AAC9E,8GAAmG;AACnG,kGAAuF;AACvF,4FAAkF;AAClF,yFAAqF;AACrF,6FAAyF;AACzF,0GAAsG;AACtG,6FAAwF;AACxF,uFAAmF;AAEnF,QAAQ,CAAC,iDAAiD,EAAE,GAAG,EAAE;IAC/D,IAAI,GAAqB,CAAC;IAC1B,IAAI,uBAAkD,CAAC;IACvD,IAAI,oBAAyD,CAAC;IAC9D,IAAI,cAA6C,CAAC;IAClD,IAAI,eAAkC,CAAC;IAEvC,MAAM,QAAQ,GAAG;QACf,EAAE,EAAE,UAAU;QACd,KAAK,EAAE,kBAAkB;QACzB,KAAK,EAAE,CAAC,kBAAkB,CAAC;KAC5B,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,UAAU,EAAE,eAAe;QAC3B,KAAK,EAAE,oBAAoB;QAC3B,WAAW,EAAE,gCAAgC;QAC7C,QAAQ,EAAE,MAAM;QAChB,SAAS,EAAE,GAAG;QACd,gBAAgB,EAAE;YAChB;gBACE,MAAM,EAAE,WAAW;gBACnB,OAAO,EAAE,iBAAiB;gBAC1B,OAAO,EAAE,OAAO;aACjB;SACF;QACD,aAAa,EAAE,sBAAsB;QACrC,UAAU,EAAE;YACV,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,UAAU;YAChB,UAAU,EAAE,MAAM;YAClB,WAAW,EAAE,sBAAsB;SACpC;KACF,CAAC;IAEF,MAAM,SAAS,GAAG;QAChB,IAAI,EAAE,aAAa;QACnB,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE,eAAe;QAC1B,eAAe,EAAE,cAAc;QAC/B,MAAM,EAAE,QAAQ;QAChB,WAAW,EAAE,MAAM;QACnB,KAAK,EAAE,eAAe;QACtB,QAAQ,EAAE,eAAe;KAC1B,CAAC;IAEF,MAAM,cAAc,GAAG;QACrB,cAAc,EAAE,QAAQ;QACxB,QAAQ,EAAE,gDAAgD;QAC1D,kBAAkB,EAAE,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;QACpE,cAAc,EAAE,uCAAuC;QACvD,eAAe,EAAE,6CAA6C;QAC9D,gBAAgB,EAAE,MAAM;QACxB,iBAAiB,EAAE,GAAG;QACtB,eAAe,EAAE,EAAE;KACpB,CAAC;IAEF,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,uBAAa,CAAC,OAAO,CAAC;oBACpB,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,UAAU;oBACpB,QAAQ,EAAE,CAAC,oCAAa,EAAE,yDAAuB,EAAE,6CAAiB,EAAE,oBAAK,CAAC;oBAC5E,WAAW,EAAE,IAAI;oBACjB,OAAO,EAAE,KAAK;iBACf,CAAC;gBACF,uBAAa,CAAC,UAAU,CAAC;oBACvB,oCAAa;oBACb,yDAAuB;oBACvB,6CAAiB;oBACjB,oBAAK;iBACN,CAAC;aACH;YACD,WAAW,EAAE,CAAC,uEAAiC,CAAC;YAChD,SAAS,EAAE;gBACT,iEAA8B;gBAC9B;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE;wBACR,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;wBAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;wBACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;wBACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;qBACjB;iBACF;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE;wBACR,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;qBACzB;iBACF;gBACD;oBACE,OAAO,EAAE,0CAAmB;oBAC5B,QAAQ,EAAE;wBACR,0BAA0B,EAAE,IAAI,CAAC,EAAE,EAAE;qBACtC;iBACF;aACF;SACF,CAAC;aACC,aAAa,CAAC,6BAAY,CAAC;aAC3B,QAAQ,CAAC;YACR,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE;gBACvB,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;gBACpD,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd,CAAC;SACF,CAAC;aACD,aAAa,CAAC,wBAAU,CAAC;aACzB,QAAQ,CAAC;YACR,WAAW,EAAE,GAAG,EAAE,CAAC,IAAI;SACxB,CAAC;aACD,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,uBAAuB,GAAG,aAAa,CAAC,GAAG,CACzC,IAAA,4BAAkB,EAAC,oCAAa,CAAC,CAClC,CAAC;QACF,oBAAoB,GAAG,aAAa,CAAC,GAAG,CACtC,IAAA,4BAAkB,EAAC,yDAAuB,CAAC,CAC5C,CAAC;QACF,cAAc,GAAG,aAAa,CAAC,GAAG,CAChC,IAAA,4BAAkB,EAAC,6CAAiB,CAAC,CACtC,CAAC;QACF,eAAe,GAAG,aAAa,CAAC,GAAG,CACjC,IAAA,4BAAkB,EAAC,oBAAK,CAAC,CAC1B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,qCAAqC;QACrC,MAAM,oBAAoB,CAAC,KAAK,EAAE,CAAC;QACnC,MAAM,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACtC,MAAM,cAAc,CAAC,KAAK,EAAE,CAAC;QAC7B,MAAM,eAAe,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,oCAAoC;YACpC,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,4BAA4B;YAC5B,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,cAAc,GAAG;gBACrB,GAAG,cAAc;gBACjB,eAAe,EAAE,SAAS,CAAC,EAAE;gBAC7B,OAAO,EAAE,UAAU,CAAC,EAAE;aACvB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,gCAAgC,CAAC;iBACtC,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAC7D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE7C,kCAAkC;YAClC,MAAM,eAAe,GAAG,MAAM,oBAAoB,CAAC,OAAO,CAAC;gBACzD,KAAK,EAAE,EAAE,eAAe,EAAE,SAAS,CAAC,EAAE,EAAE;aACzC,CAAC,CAAC;YACH,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,iBAAiB,GAAG;gBACxB,GAAG,cAAc;gBACjB,eAAe,EAAE,cAAc;gBAC/B,cAAc,EAAE,cAAc;gBAC9B,eAAe,EAAE,GAAG,EAAE,iBAAiB;aACxC,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,gCAAgC,CAAC;iBACtC,IAAI,CAAC,iBAAiB,CAAC;iBACvB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,cAAc,GAAG;gBACrB,GAAG,cAAc;gBACjB,eAAe,EAAE,sCAAsC;aACxD,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,gCAAgC,CAAC;iBACtC,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,mBAAmB;YACnB,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,WAAW,GAAG,EAAE,CAAC;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC7B,WAAW,CAAC,IAAI,CACd,oBAAoB,CAAC,MAAM,CAAC;oBAC1B,eAAe,EAAE,SAAS,CAAC,EAAE;oBAC7B,cAAc,EAAE,QAAQ;oBACxB,MAAM,EAAE,SAAS;oBACjB,QAAQ,EAAE,iBAAiB,CAAC,EAAE;oBAC9B,UAAU,EAAE,QAAQ,CAAC,EAAE;oBACvB,UAAU,EAAE,IAAI,IAAI,EAAE;iBACvB,CAAC,CACH,CAAC;YACJ,CAAC;YACD,MAAM,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE7C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gCAAgC,CAAC;iBACrC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;iBAC7B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YACpD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,WAAW,GAAG,oBAAoB,CAAC,MAAM,CAAC;gBAC9C,eAAe,EAAE,SAAS,CAAC,EAAE;gBAC7B,cAAc,EAAE,QAAQ;gBACxB,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,oBAAoB;gBAC9B,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,oBAAoB,CAAC,MAAM,CAAC;gBAC9C,eAAe,EAAE,SAAS,CAAC,EAAE;gBAC7B,cAAc,EAAE,QAAQ;gBACxB,MAAM,EAAE,WAAW;gBACnB,QAAQ,EAAE,sBAAsB;gBAChC,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YAEH,MAAM,oBAAoB,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC;YAE5D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gCAAgC,CAAC;iBACrC,KAAK,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;iBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wCAAwC,EAAE,GAAG,EAAE;QACtD,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,UAAU,GAAG,oBAAoB,CAAC,MAAM,CAAC;gBAC7C,eAAe,EAAE,SAAS,CAAC,EAAE;gBAC7B,cAAc,EAAE,QAAQ;gBACxB,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,eAAe;gBACzB,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEpE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,kCAAkC,eAAe,CAAC,EAAE,EAAE,CAAC;iBAC3D,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,qEAAqE,CAAC;iBAC1E,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wCAAwC,EAAE,GAAG,EAAE;QACtD,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,UAAU,GAAG,oBAAoB,CAAC,MAAM,CAAC;gBAC7C,eAAe,EAAE,SAAS,CAAC,EAAE;gBAC7B,cAAc,EAAE,QAAQ;gBACxB,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,mBAAmB;gBAC7B,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEpE,MAAM,OAAO,GAAG;gBACd,QAAQ,EAAE,yCAAyC;gBACnD,gBAAgB,EAAE,UAAU;gBAC5B,eAAe,EAAE,EAAE;aACpB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,kCAAkC,eAAe,CAAC,EAAE,EAAE,CAAC;iBAC3D,IAAI,CAAC,OAAO,CAAC;iBACb,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACtE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAEpE,8BAA8B;YAC9B,MAAM,iBAAiB,GAAG,MAAM,oBAAoB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kDAAkD,EAAE,GAAG,EAAE;QAChE,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,UAAU,GAAG,oBAAoB,CAAC,MAAM,CAAC;gBAC7C,eAAe,EAAE,SAAS,CAAC,EAAE;gBAC7B,cAAc,EAAE,QAAQ;gBACxB,MAAM,EAAE,aAAa;gBACrB,QAAQ,EAAE,wBAAwB;gBAClC,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEpE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,kCAAkC,eAAe,CAAC,EAAE,WAAW,CAAC;iBACrE,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE/C,8BAA8B;YAC9B,MAAM,mBAAmB,GAAG,MAAM,oBAAoB,CAAC,OAAO,CAAC;gBAC7D,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,8EAA8E,CAAC;iBACpF,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gDAAgD,EAAE,GAAG,EAAE;QAC9D,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,UAAU,GAAG,oBAAoB,CAAC,MAAM,CAAC;gBAC7C,eAAe,EAAE,SAAS,CAAC,EAAE;gBAC7B,cAAc,EAAE,QAAQ;gBACxB,MAAM,EAAE,WAAW;gBACnB,QAAQ,EAAE,sBAAsB;gBAChC,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEpE,MAAM,UAAU,GAAG;gBACjB,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,kCAAkC;aAC7C,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,kCAAkC,eAAe,CAAC,EAAE,SAAS,CAAC;iBACnE,IAAI,CAAC,UAAU,CAAC;iBAChB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC/D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEnD,8BAA8B;YAC9B,MAAM,kBAAkB,GAAG,MAAM,oBAAoB,CAAC,OAAO,CAAC;gBAC5D,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACpE,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,UAAU,GAAG,oBAAoB,CAAC,MAAM,CAAC;gBAC7C,eAAe,EAAE,SAAS,CAAC,EAAE;gBAC7B,cAAc,EAAE,QAAQ;gBACxB,MAAM,EAAE,WAAW;gBACnB,QAAQ,EAAE,sBAAsB;gBAChC,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEpE,MAAM,UAAU,GAAG;gBACjB,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,yCAAyC;aACpD,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,kCAAkC,eAAe,CAAC,EAAE,SAAS,CAAC;iBACnE,IAAI,CAAC,UAAU,CAAC;iBAChB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2CAA2C,EAAE,GAAG,EAAE;QACzD,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,UAAU,GAAG,oBAAoB,CAAC,MAAM,CAAC;gBAC7C,eAAe,EAAE,SAAS,CAAC,EAAE;gBAC7B,cAAc,EAAE,QAAQ;gBACxB,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,iBAAiB;gBAC3B,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEpE,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,MAAM,CAAC,kCAAkC,eAAe,CAAC,EAAE,EAAE,CAAC;iBAC9D,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,sCAAsC;YACtC,MAAM,iBAAiB,GAAG,MAAM,oBAAoB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,CAAC,QAAQ,EAAE,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,MAAM,CAAC,qEAAqE,CAAC;iBAC7E,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\api\\controllers\\__tests__\\vulnerability-assessment.controller.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport * as request from 'supertest';\r\nimport { Repository } from 'typeorm';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\n\r\nimport { VulnerabilityAssessmentController } from '../vulnerability-assessment.controller';\r\nimport { VulnerabilityAssessmentService } from '../../../application/services/vulnerability-assessment.service';\r\nimport { Vulnerability } from '../../../domain/entities/vulnerability.entity';\r\nimport { VulnerabilityAssessment } from '../../../domain/entities/vulnerability-assessment.entity';\r\nimport { VulnerabilityScan } from '../../../domain/entities/vulnerability-scan.entity';\r\nimport { Asset } from '../../../../asset-management/domain/entities/asset.entity';\r\nimport { LoggerService } from '../../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../../infrastructure/notification/notification.service';\r\nimport { JwtAuthGuard } from '../../../../../infrastructure/auth/guards/jwt-auth.guard';\r\nimport { RolesGuard } from '../../../../../infrastructure/auth/guards/roles.guard';\r\n\r\ndescribe('VulnerabilityAssessmentController (Integration)', () => {\r\n  let app: INestApplication;\r\n  let vulnerabilityRepository: Repository<Vulnerability>;\r\n  let assessmentRepository: Repository<VulnerabilityAssessment>;\r\n  let scanRepository: Repository<VulnerabilityScan>;\r\n  let assetRepository: Repository<Asset>;\r\n\r\n  const mockUser = {\r\n    id: 'user-123',\r\n    email: '<EMAIL>',\r\n    roles: ['security_analyst'],\r\n  };\r\n\r\n  const mockVulnerability = {\r\n    identifier: 'CVE-2023-1234',\r\n    title: 'Test Vulnerability',\r\n    description: 'Test vulnerability description',\r\n    severity: 'high',\r\n    cvssScore: 7.5,\r\n    affectedProducts: [\r\n      {\r\n        vendor: 'Test Corp',\r\n        product: 'Web Application',\r\n        version: '1.0.0',\r\n      },\r\n    ],\r\n    publishedDate: '2023-01-01T00:00:00Z',\r\n    dataSource: {\r\n      name: 'Test Source',\r\n      type: 'internal',\r\n      confidence: 'high',\r\n      lastUpdated: '2023-01-01T00:00:00Z',\r\n    },\r\n  };\r\n\r\n  const mockAsset = {\r\n    name: 'Test Server',\r\n    type: 'server',\r\n    ipAddress: '*************',\r\n    operatingSystem: 'Ubuntu 20.04',\r\n    status: 'active',\r\n    criticality: 'high',\r\n    owner: 'IT Department',\r\n    location: 'Data Center 1',\r\n  };\r\n\r\n  const mockAssessment = {\r\n    assessmentType: 'manual',\r\n    findings: 'Confirmed vulnerability through manual testing',\r\n    recommendedActions: ['Apply security patch', 'Update configuration'],\r\n    businessImpact: 'High impact on customer data security',\r\n    technicalImpact: 'Potential data breach through SQL injection',\r\n    assessedSeverity: 'high',\r\n    assessedCvssScore: 7.5,\r\n    confidenceLevel: 90,\r\n  };\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        TypeOrmModule.forRoot({\r\n          type: 'sqlite',\r\n          database: ':memory:',\r\n          entities: [Vulnerability, VulnerabilityAssessment, VulnerabilityScan, Asset],\r\n          synchronize: true,\r\n          logging: false,\r\n        }),\r\n        TypeOrmModule.forFeature([\r\n          Vulnerability,\r\n          VulnerabilityAssessment,\r\n          VulnerabilityScan,\r\n          Asset,\r\n        ]),\r\n      ],\r\n      controllers: [VulnerabilityAssessmentController],\r\n      providers: [\r\n        VulnerabilityAssessmentService,\r\n        {\r\n          provide: LoggerService,\r\n          useValue: {\r\n            debug: jest.fn(),\r\n            log: jest.fn(),\r\n            warn: jest.fn(),\r\n            error: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: {\r\n            logUserAction: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: NotificationService,\r\n          useValue: {\r\n            sendAssessmentNotification: jest.fn(),\r\n          },\r\n        },\r\n      ],\r\n    })\r\n      .overrideGuard(JwtAuthGuard)\r\n      .useValue({\r\n        canActivate: (context) => {\r\n          const request = context.switchToHttp().getRequest();\r\n          request.user = mockUser;\r\n          return true;\r\n        },\r\n      })\r\n      .overrideGuard(RolesGuard)\r\n      .useValue({\r\n        canActivate: () => true,\r\n      })\r\n      .compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    await app.init();\r\n\r\n    vulnerabilityRepository = moduleFixture.get<Repository<Vulnerability>>(\r\n      getRepositoryToken(Vulnerability),\r\n    );\r\n    assessmentRepository = moduleFixture.get<Repository<VulnerabilityAssessment>>(\r\n      getRepositoryToken(VulnerabilityAssessment),\r\n    );\r\n    scanRepository = moduleFixture.get<Repository<VulnerabilityScan>>(\r\n      getRepositoryToken(VulnerabilityScan),\r\n    );\r\n    assetRepository = moduleFixture.get<Repository<Asset>>(\r\n      getRepositoryToken(Asset),\r\n    );\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    // Clean up database before each test\r\n    await assessmentRepository.clear();\r\n    await vulnerabilityRepository.clear();\r\n    await scanRepository.clear();\r\n    await assetRepository.clear();\r\n  });\r\n\r\n  describe('POST /api/vulnerability-assessments', () => {\r\n    it('should create a new assessment', async () => {\r\n      // Create prerequisite vulnerability\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      // Create prerequisite asset\r\n      const asset = assetRepository.create(mockAsset);\r\n      const savedAsset = await assetRepository.save(asset);\r\n\r\n      const assessmentData = {\r\n        ...mockAssessment,\r\n        vulnerabilityId: savedVuln.id,\r\n        assetId: savedAsset.id,\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/vulnerability-assessments')\r\n        .send(assessmentData)\r\n        .expect(201);\r\n\r\n      expect(response.body).toHaveProperty('id');\r\n      expect(response.body.vulnerabilityId).toBe(savedVuln.id);\r\n      expect(response.body.assetId).toBe(savedAsset.id);\r\n      expect(response.body.findings).toBe(assessmentData.findings);\r\n      expect(response.body.status).toBe('pending');\r\n\r\n      // Verify it was saved to database\r\n      const savedAssessment = await assessmentRepository.findOne({\r\n        where: { vulnerabilityId: savedVuln.id },\r\n      });\r\n      expect(savedAssessment).toBeDefined();\r\n      expect(savedAssessment.findings).toBe(assessmentData.findings);\r\n    });\r\n\r\n    it('should return 400 for invalid assessment data', async () => {\r\n      const invalidAssessment = {\r\n        ...mockAssessment,\r\n        vulnerabilityId: 'invalid-uuid',\r\n        assessmentType: 'invalid-type',\r\n        confidenceLevel: 150, // Invalid: > 100\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/vulnerability-assessments')\r\n        .send(invalidAssessment)\r\n        .expect(400);\r\n\r\n      expect(response.body.message).toContain('validation failed');\r\n    });\r\n\r\n    it('should return 404 for non-existent vulnerability', async () => {\r\n      const assessmentData = {\r\n        ...mockAssessment,\r\n        vulnerabilityId: '123e4567-e89b-12d3-a456-************',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/vulnerability-assessments')\r\n        .send(assessmentData)\r\n        .expect(404);\r\n\r\n      expect(response.body.message).toContain('Vulnerability not found');\r\n    });\r\n  });\r\n\r\n  describe('GET /api/vulnerability-assessments', () => {\r\n    it('should return paginated assessments', async () => {\r\n      // Create test data\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      const assessments = [];\r\n      for (let i = 1; i <= 15; i++) {\r\n        assessments.push(\r\n          assessmentRepository.create({\r\n            vulnerabilityId: savedVuln.id,\r\n            assessmentType: 'manual',\r\n            status: 'pending',\r\n            findings: `Test findings ${i}`,\r\n            assessedBy: mockUser.id,\r\n            assessedAt: new Date(),\r\n          }),\r\n        );\r\n      }\r\n      await assessmentRepository.save(assessments);\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/vulnerability-assessments')\r\n        .query({ page: 1, limit: 10 })\r\n        .expect(200);\r\n\r\n      expect(response.body).toHaveProperty('assessments');\r\n      expect(response.body).toHaveProperty('total');\r\n      expect(response.body).toHaveProperty('page');\r\n      expect(response.body).toHaveProperty('totalPages');\r\n      expect(response.body.assessments).toHaveLength(10);\r\n      expect(response.body.total).toBe(15);\r\n      expect(response.body.totalPages).toBe(2);\r\n    });\r\n\r\n    it('should filter assessments by status', async () => {\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      const assessment1 = assessmentRepository.create({\r\n        vulnerabilityId: savedVuln.id,\r\n        assessmentType: 'manual',\r\n        status: 'pending',\r\n        findings: 'Pending assessment',\r\n        assessedBy: mockUser.id,\r\n        assessedAt: new Date(),\r\n      });\r\n\r\n      const assessment2 = assessmentRepository.create({\r\n        vulnerabilityId: savedVuln.id,\r\n        assessmentType: 'manual',\r\n        status: 'completed',\r\n        findings: 'Completed assessment',\r\n        assessedBy: mockUser.id,\r\n        assessedAt: new Date(),\r\n      });\r\n\r\n      await assessmentRepository.save([assessment1, assessment2]);\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/vulnerability-assessments')\r\n        .query({ statuses: 'pending' })\r\n        .expect(200);\r\n\r\n      expect(response.body.assessments).toHaveLength(1);\r\n      expect(response.body.assessments[0].status).toBe('pending');\r\n    });\r\n  });\r\n\r\n  describe('GET /api/vulnerability-assessments/:id', () => {\r\n    it('should return assessment details', async () => {\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      const assessment = assessmentRepository.create({\r\n        vulnerabilityId: savedVuln.id,\r\n        assessmentType: 'manual',\r\n        status: 'pending',\r\n        findings: 'Test findings',\r\n        assessedBy: mockUser.id,\r\n        assessedAt: new Date(),\r\n      });\r\n      const savedAssessment = await assessmentRepository.save(assessment);\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .get(`/api/vulnerability-assessments/${savedAssessment.id}`)\r\n        .expect(200);\r\n\r\n      expect(response.body).toHaveProperty('id');\r\n      expect(response.body.vulnerabilityId).toBe(savedVuln.id);\r\n      expect(response.body.findings).toBe('Test findings');\r\n    });\r\n\r\n    it('should return 404 for non-existent assessment', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/vulnerability-assessments/123e4567-e89b-12d3-a456-************')\r\n        .expect(404);\r\n\r\n      expect(response.body.message).toContain('Assessment not found');\r\n    });\r\n  });\r\n\r\n  describe('PUT /api/vulnerability-assessments/:id', () => {\r\n    it('should update an existing assessment', async () => {\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      const assessment = assessmentRepository.create({\r\n        vulnerabilityId: savedVuln.id,\r\n        assessmentType: 'manual',\r\n        status: 'pending',\r\n        findings: 'Original findings',\r\n        assessedBy: mockUser.id,\r\n        assessedAt: new Date(),\r\n      });\r\n      const savedAssessment = await assessmentRepository.save(assessment);\r\n\r\n      const updates = {\r\n        findings: 'Updated findings after further analysis',\r\n        assessedSeverity: 'critical',\r\n        confidenceLevel: 95,\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .put(`/api/vulnerability-assessments/${savedAssessment.id}`)\r\n        .send(updates)\r\n        .expect(200);\r\n\r\n      expect(response.body.findings).toBe(updates.findings);\r\n      expect(response.body.assessedSeverity).toBe(updates.assessedSeverity);\r\n      expect(response.body.confidenceLevel).toBe(updates.confidenceLevel);\r\n\r\n      // Verify database was updated\r\n      const updatedAssessment = await assessmentRepository.findOne({\r\n        where: { id: savedAssessment.id },\r\n      });\r\n      expect(updatedAssessment.findings).toBe(updates.findings);\r\n    });\r\n  });\r\n\r\n  describe('POST /api/vulnerability-assessments/:id/complete', () => {\r\n    it('should complete an assessment', async () => {\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      const assessment = assessmentRepository.create({\r\n        vulnerabilityId: savedVuln.id,\r\n        assessmentType: 'manual',\r\n        status: 'in_progress',\r\n        findings: 'Assessment in progress',\r\n        assessedBy: mockUser.id,\r\n        assessedAt: new Date(),\r\n      });\r\n      const savedAssessment = await assessmentRepository.save(assessment);\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post(`/api/vulnerability-assessments/${savedAssessment.id}/complete`)\r\n        .expect(200);\r\n\r\n      expect(response.body.status).toBe('completed');\r\n\r\n      // Verify database was updated\r\n      const completedAssessment = await assessmentRepository.findOne({\r\n        where: { id: savedAssessment.id },\r\n      });\r\n      expect(completedAssessment.status).toBe('completed');\r\n    });\r\n\r\n    it('should return 404 for non-existent assessment', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/vulnerability-assessments/123e4567-e89b-12d3-a456-************/complete')\r\n        .expect(404);\r\n\r\n      expect(response.body.message).toContain('Assessment not found');\r\n    });\r\n  });\r\n\r\n  describe('POST /api/vulnerability-assessments/:id/review', () => {\r\n    it('should review and approve an assessment', async () => {\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      const assessment = assessmentRepository.create({\r\n        vulnerabilityId: savedVuln.id,\r\n        assessmentType: 'manual',\r\n        status: 'completed',\r\n        findings: 'Completed assessment',\r\n        assessedBy: mockUser.id,\r\n        assessedAt: new Date(),\r\n      });\r\n      const savedAssessment = await assessmentRepository.save(assessment);\r\n\r\n      const reviewData = {\r\n        approved: true,\r\n        comments: 'Assessment approved after review',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post(`/api/vulnerability-assessments/${savedAssessment.id}/review`)\r\n        .send(reviewData)\r\n        .expect(200);\r\n\r\n      expect(response.body.reviewComments).toBe(reviewData.comments);\r\n      expect(response.body.reviewedBy).toBe(mockUser.id);\r\n\r\n      // Verify database was updated\r\n      const reviewedAssessment = await assessmentRepository.findOne({\r\n        where: { id: savedAssessment.id },\r\n      });\r\n      expect(reviewedAssessment.reviewComments).toBe(reviewData.comments);\r\n      expect(reviewedAssessment.reviewedBy).toBe(mockUser.id);\r\n    });\r\n\r\n    it('should review and reject an assessment', async () => {\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      const assessment = assessmentRepository.create({\r\n        vulnerabilityId: savedVuln.id,\r\n        assessmentType: 'manual',\r\n        status: 'completed',\r\n        findings: 'Completed assessment',\r\n        assessedBy: mockUser.id,\r\n        assessedAt: new Date(),\r\n      });\r\n      const savedAssessment = await assessmentRepository.save(assessment);\r\n\r\n      const reviewData = {\r\n        approved: false,\r\n        comments: 'Assessment needs more detailed analysis',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post(`/api/vulnerability-assessments/${savedAssessment.id}/review`)\r\n        .send(reviewData)\r\n        .expect(200);\r\n\r\n      expect(response.body.status).toBe('rejected');\r\n      expect(response.body.reviewComments).toBe(reviewData.comments);\r\n    });\r\n  });\r\n\r\n  describe('DELETE /api/vulnerability-assessments/:id', () => {\r\n    it('should delete an assessment', async () => {\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      const assessment = assessmentRepository.create({\r\n        vulnerabilityId: savedVuln.id,\r\n        assessmentType: 'manual',\r\n        status: 'pending',\r\n        findings: 'Test assessment',\r\n        assessedBy: mockUser.id,\r\n        assessedAt: new Date(),\r\n      });\r\n      const savedAssessment = await assessmentRepository.save(assessment);\r\n\r\n      await request(app.getHttpServer())\r\n        .delete(`/api/vulnerability-assessments/${savedAssessment.id}`)\r\n        .expect(204);\r\n\r\n      // Verify it was deleted from database\r\n      const deletedAssessment = await assessmentRepository.findOne({\r\n        where: { id: savedAssessment.id },\r\n      });\r\n      expect(deletedAssessment).toBeNull();\r\n    });\r\n\r\n    it('should return 404 for non-existent assessment', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .delete('/api/vulnerability-assessments/123e4567-e89b-12d3-a456-************')\r\n        .expect(404);\r\n\r\n      expect(response.body.message).toContain('Assessment not found');\r\n    });\r\n  });\r\n});\r\n"], "version": 3}