{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\auth.module.ts", "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,qCAAwC;AACxC,+CAAkD;AAClD,2CAA6D;AAC7D,6CAAgD;AAChD,qDAAkE;AAElE,iDAA6C;AAC7C,4DAAwD;AACxD,gEAA4D;AAC5D,8EAAyE;AACzE,oFAA+E;AAC/E,8DAA0D;AAC1D,4DAAuD;AACvD,gEAA2D;AAC3D,sDAAkD;AAClD,kEAA8D;AAC9D,0DAAsD;AACtD,sDAAkD;AAClD,wDAAoD;AACpD,kEAA8D;AAC9D,4DAAwD;AACxD,gEAA4D;AAC5D,4DAAwD;AACxD,4DAAwD;AACxD,8EAAyE;AACzE,sFAAiF;AAEjF,gDAAgD;AAChD,2FAAiF;AACjF,2FAAiF;AACjF,6GAAkG;AAElG;;;;;;;;;;;;;;GAcG;AAuEI,IAAM,UAAU,GAAhB,MAAM,UAAU;CAAG,CAAA;AAAb,gCAAU;qBAAV,UAAU;IAtEtB,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,qBAAY;YACZ,yBAAc,CAAC,QAAQ,CAAC,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC;YACnD,eAAS,CAAC,aAAa,CAAC;gBACtB,OAAO,EAAE,CAAC,qBAAY,CAAC;gBACvB,MAAM,EAAE,CAAC,sBAAa,CAAC;gBACvB,UAAU,EAAE,KAAK,EAAE,aAA4B,EAAE,EAAE;oBACjD,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAY,KAAK,CAAC,CAAC;oBACtD,MAAM,WAAW,GAAG,4BAAe,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;oBAC9D,MAAM,WAAW,GAAG,4BAAe,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;oBAEpE,OAAO;wBACL,MAAM,EAAE,WAAW;wBACnB,UAAU,EAAE,SAAS,CAAC,UAAU;wBAChC,SAAS,EAAE,SAAS,CAAC,SAAS;wBAC9B,WAAW;wBACX,aAAa,EAAE,4BAAe,CAAC,gBAAgB,CAAC,SAAS,CAAC;qBAC3D,CAAC;gBACJ,CAAC;aACF,CAAC;YACF,uBAAa,CAAC,UAAU,CAAC,CAAC,kBAAI,EAAE,kBAAI,EAAE,mCAAY,CAAC,CAAC;SACrD;QACD,SAAS,EAAE;YACT,+BAA+B;YAC/B,0BAAW;YACX,kCAAe;YACf,4BAAY;YACZ,gCAAc;YACd,4BAAY;YACZ,4BAAY;YACZ,6CAAoB;YACpB,qDAAwB;YACxB,0BAAW;YACX,4BAAY;YAEZ,sBAAsB;YACtB,0BAAW;YACX,8BAAa;YACb,2CAAmB;YACnB,iDAAsB;YACtB,4BAAY;YAEZ,SAAS;YACT,6BAAY;YACZ,iCAAc;YACd,wBAAU;YACV,oCAAgB;YAChB,4BAAY;SACb;QACD,OAAO,EAAE;YACP,0BAAW;YACX,kCAAe;YACf,4BAAY;YACZ,gCAAc;YACd,4BAAY;YACZ,4BAAY;YACZ,6CAAoB;YACpB,qDAAwB;YACxB,0BAAW;YACX,4BAAY;YACZ,6BAAY;YACZ,iCAAc;YACd,wBAAU;YACV,oCAAgB;YAChB,4BAAY;YACZ,yBAAc;YACd,eAAS;SACV;KACF,CAAC;GACW,UAAU,CAAG", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\auth.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { JwtModule } from '@nestjs/jwt';\r\nimport { PassportModule } from '@nestjs/passport';\r\nimport { ConfigModule, ConfigService } from '@nestjs/config';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { JwtConfig, JwtConfigHelper } from '../config/jwt.config';\r\n\r\nimport { AuthService } from './auth.service';\r\nimport { JwtStrategy } from './strategies/jwt.strategy';\r\nimport { LocalStrategy } from './strategies/local.strategy';\r\nimport { GoogleOAuthStrategy } from './strategies/oauth-google.strategy';\r\nimport { MicrosoftOAuthStrategy } from './strategies/oauth-microsoft.strategy';\r\nimport { SamlStrategy } from './strategies/saml.strategy';\r\nimport { JwtAuthGuard } from './guards/jwt-auth.guard';\r\nimport { LocalAuthGuard } from './guards/local-auth.guard';\r\nimport { RolesGuard } from './guards/roles.guard';\r\nimport { PermissionsGuard } from './guards/permissions.guard';\r\nimport { SessionGuard } from './guards/session.guard';\r\nimport { RbacService } from './rbac/rbac.service';\r\nimport { PolicyEngine } from './rbac/policy.engine';\r\nimport { PasswordService } from './services/password.service';\r\nimport { TokenService } from './services/token.service';\r\nimport { SessionService } from './services/session.service';\r\nimport { SessionStore } from './services/session.store';\r\nimport { OAuthService } from './services/oauth.service';\r\nimport { PasswordResetService } from './services/password-reset.service';\r\nimport { EmailVerificationService } from './services/email-verification.service';\r\n\r\n// Import entities (these will be created later)\r\nimport { User } from '../../modules/user-management/domain/entities/user.entity';\r\nimport { Role } from '../../modules/user-management/domain/entities/role.entity';\r\nimport { RefreshToken } from '../../modules/user-management/domain/entities/refresh-token.entity';\r\n\r\n/**\r\n * Authentication and authorization module\r\n * Provides JWT-based authentication with RBAC (Role-Based Access Control)\r\n * \r\n * Features:\r\n * - JWT token authentication\r\n * - Local username/password authentication\r\n * - Role-based access control\r\n * - Permission-based authorization\r\n * - Session management\r\n * - Password hashing and validation\r\n * - Refresh token support\r\n * \r\n * @module AuthModule\r\n */\r\n@Module({\r\n  imports: [\r\n    ConfigModule,\r\n    PassportModule.register({ defaultStrategy: 'jwt' }),\r\n    JwtModule.registerAsync({\r\n      imports: [ConfigModule],\r\n      inject: [ConfigService],\r\n      useFactory: async (configService: ConfigService) => {\r\n        const jwtConfig = configService.get<JwtConfig>('jwt');\r\n        const signOptions = JwtConfigHelper.getSignOptions(jwtConfig);\r\n        const secretOrKey = JwtConfigHelper.getSecretOrKey(jwtConfig, true);\r\n        \r\n        return {\r\n          secret: secretOrKey,\r\n          privateKey: jwtConfig.privateKey,\r\n          publicKey: jwtConfig.publicKey,\r\n          signOptions,\r\n          verifyOptions: JwtConfigHelper.getVerifyOptions(jwtConfig),\r\n        };\r\n      },\r\n    }),\r\n    TypeOrmModule.forFeature([User, Role, RefreshToken]),\r\n  ],\r\n  providers: [\r\n    // Core authentication services\r\n    AuthService,\r\n    PasswordService,\r\n    TokenService,\r\n    SessionService,\r\n    SessionStore,\r\n    OAuthService,\r\n    PasswordResetService,\r\n    EmailVerificationService,\r\n    RbacService,\r\n    PolicyEngine,\r\n\r\n    // Passport strategies\r\n    JwtStrategy,\r\n    LocalStrategy,\r\n    GoogleOAuthStrategy,\r\n    MicrosoftOAuthStrategy,\r\n    SamlStrategy,\r\n\r\n    // Guards\r\n    JwtAuthGuard,\r\n    LocalAuthGuard,\r\n    RolesGuard,\r\n    PermissionsGuard,\r\n    SessionGuard,\r\n  ],\r\n  exports: [\r\n    AuthService,\r\n    PasswordService,\r\n    TokenService,\r\n    SessionService,\r\n    SessionStore,\r\n    OAuthService,\r\n    PasswordResetService,\r\n    EmailVerificationService,\r\n    RbacService,\r\n    PolicyEngine,\r\n    JwtAuthGuard,\r\n    LocalAuthGuard,\r\n    RolesGuard,\r\n    PermissionsGuard,\r\n    SessionGuard,\r\n    PassportModule,\r\n    JwtModule,\r\n  ],\r\n})\r\nexport class AuthModule {}\r\n"], "version": 3}