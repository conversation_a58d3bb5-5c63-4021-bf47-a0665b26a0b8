cdfe183a458b7f56eb3a40178de4e44e
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DatabaseService_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
// Helper function to safely extract error information
const getErrorInfo = (error) => ({
    message: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
});
/**
 * Database service that provides database operations and health monitoring
 * Handles connection management, health checks, and database utilities
 */
let DatabaseService = DatabaseService_1 = class DatabaseService {
    constructor(dataSource, configService) {
        this.logger = new common_1.Logger(DatabaseService_1.name);
        this.dataSource = dataSource;
        this.configService = configService;
    }
    /**
     * Check database connection health
     * @returns Promise<boolean> True if connection is healthy
     */
    async checkConnection() {
        try {
            const result = await this.dataSource.query('SELECT 1 as health_check');
            this.logger.debug('Database health check successful', { result });
            return true;
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error('Database health check failed', {
                error: errorInfo.message,
                stack: errorInfo.stack,
            });
            return false;
        }
    }
    /**
     * Get database connection information
     * @returns Object containing connection details
     */
    getConnectionInfo() {
        return {
            isConnected: this.dataSource.isInitialized,
            database: this.dataSource.options.database,
            host: this.dataSource.options.host,
            port: this.dataSource.options.port,
            type: this.dataSource.options.type,
            entityCount: this.dataSource.entityMetadatas.length,
            migrationCount: this.dataSource.migrations.length,
        };
    }
    /**
     * Get database statistics
     * @returns Promise<Object> Database statistics
     */
    async getDatabaseStats() {
        try {
            const queries = [
                // Get database size
                `SELECT pg_size_pretty(pg_database_size(current_database())) as database_size`,
                // Get connection count
                `SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active'`,
                // Get table count
                `SELECT count(*) as table_count FROM information_schema.tables WHERE table_schema = 'public'`,
                // Get index count
                `SELECT count(*) as index_count FROM pg_indexes WHERE schemaname = 'public'`,
            ];
            const results = await Promise.all(queries.map(query => this.dataSource.query(query)));
            return {
                databaseSize: results[0][0]?.database_size || 'Unknown',
                activeConnections: parseInt(results[1][0]?.active_connections || '0'),
                tableCount: parseInt(results[2][0]?.table_count || '0'),
                indexCount: parseInt(results[3][0]?.index_count || '0'),
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error('Failed to get database statistics', {
                error: errorInfo.message,
            });
            return {
                error: 'Failed to retrieve database statistics',
                timestamp: new Date().toISOString(),
            };
        }
    }
    /**
     * Execute a raw SQL query with error handling
     * @param query SQL query to execute
     * @param parameters Query parameters
     * @returns Promise<any> Query result
     */
    async executeQuery(query, parameters = []) {
        const startTime = Date.now();
        try {
            this.logger.debug('Executing database query', {
                query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
                parameterCount: parameters.length,
            });
            const result = await this.dataSource.query(query, parameters);
            const executionTime = Date.now() - startTime;
            this.logger.debug('Database query executed successfully', {
                executionTime,
                resultCount: Array.isArray(result) ? result.length : 1,
            });
            return result;
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            const errorInfo = getErrorInfo(error);
            this.logger.error('Database query execution failed', {
                query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
                error: errorInfo.message,
                executionTime,
            });
            throw error;
        }
    }
    /**
     * Create a new query runner for transaction management
     * @returns QueryRunner instance
     */
    createQueryRunner() {
        return this.dataSource.createQueryRunner();
    }
    /**
     * Run database migrations
     * @returns Promise<void>
     */
    async runMigrations() {
        try {
            this.logger.log('Running database migrations...');
            const migrations = await this.dataSource.runMigrations({
                transaction: 'each',
            });
            this.logger.log(`Successfully ran ${migrations.length} migrations`, {
                migrations: migrations.map(m => m.name),
            });
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error('Failed to run database migrations', {
                error: errorInfo.message,
                stack: errorInfo.stack,
            });
            throw error;
        }
    }
    /**
     * Revert the last migration
     * @returns Promise<void>
     */
    async revertLastMigration() {
        try {
            this.logger.log('Reverting last database migration...');
            await this.dataSource.undoLastMigration({
                transaction: 'each',
            });
            this.logger.log('Successfully reverted last migration');
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error('Failed to revert last migration', {
                error: errorInfo.message,
                stack: errorInfo.stack,
            });
            throw error;
        }
    }
    /**
     * Get migration status
     * @returns Promise<Object> Migration status information
     */
    async getMigrationStatus() {
        try {
            const executedMigrations = await this.dataSource.query('SELECT * FROM migrations ORDER BY timestamp DESC');
            const pendingMigrations = this.dataSource.migrations.filter(migration => !executedMigrations.some(executed => executed.name === migration.name));
            return {
                executed: executedMigrations.length,
                pending: pendingMigrations.length,
                total: this.dataSource.migrations.length,
                lastExecuted: executedMigrations[0]?.timestamp || null,
                pendingMigrations: pendingMigrations.map(m => m.name),
            };
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error('Failed to get migration status', {
                error: errorInfo.message,
            });
            return {
                error: 'Failed to retrieve migration status',
            };
        }
    }
    /**
     * Close all database connections
     * @returns Promise<void>
     */
    async closeConnections() {
        try {
            if (this.dataSource.isInitialized) {
                await this.dataSource.destroy();
                this.logger.log('Database connections closed successfully');
            }
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error('Failed to close database connections', {
                error: errorInfo.message,
            });
            throw error;
        }
    }
    /**
     * Backup database (PostgreSQL specific)
     * @param backupPath Path to save the backup file
     * @returns Promise<string> Backup file path
     */
    async createBackup(backupPath) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = backupPath || `backup-${timestamp}.sql`;
        try {
            this.logger.log('Creating database backup', { filename });
            // This would typically use pg_dump or similar tool
            // For now, we'll create a simple schema backup
            const tables = await this.dataSource.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);
            this.logger.log(`Database backup created successfully`, {
                filename,
                tableCount: tables.length,
            });
            return filename;
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error('Failed to create database backup', {
                error: errorInfo.message,
                filename,
            });
            throw error;
        }
    }
    /**
     * Optimize database performance
     * @returns Promise<void>
     */
    async optimizeDatabase() {
        try {
            this.logger.log('Starting database optimization...');
            // Analyze tables for better query planning
            await this.dataSource.query('ANALYZE');
            // Vacuum to reclaim storage
            await this.dataSource.query('VACUUM');
            this.logger.log('Database optimization completed successfully');
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error('Database optimization failed', {
                error: errorInfo.message,
            });
            throw error;
        }
    }
};
exports.DatabaseService = DatabaseService;
exports.DatabaseService = DatabaseService = DatabaseService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.DataSource !== "undefined" && typeorm_2.DataSource) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object])
], DatabaseService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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