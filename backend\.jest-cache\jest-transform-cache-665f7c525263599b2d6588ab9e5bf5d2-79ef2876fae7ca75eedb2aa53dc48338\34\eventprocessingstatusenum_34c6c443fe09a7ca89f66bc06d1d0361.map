{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\event-processing-status.enum.ts", "mappings": ";;;AAAA;;;;;;;;GAQG;AACH,IAAY,qBAkHX;AAlHD,WAAY,qBAAqB;IAC/B;;;OAGG;IACH,oCAAW,CAAA;IAEX;;;OAGG;IACH,oDAA2B,CAAA;IAE3B;;;OAGG;IACH,kDAAyB,CAAA;IAEzB;;;OAGG;IACH,gDAAuB,CAAA;IAEvB;;;OAGG;IACH,8CAAqB,CAAA;IAErB;;;OAGG;IACH,oDAA2B,CAAA;IAE3B;;;OAGG;IACH,kDAAyB,CAAA;IAEzB;;;OAGG;IACH,gDAAuB,CAAA;IAEvB;;;OAGG;IACH,8CAAqB,CAAA;IAErB;;;OAGG;IACH,0DAAiC,CAAA;IAEjC;;;OAGG;IACH,wDAA+B,CAAA;IAE/B;;;OAGG;IACH,8CAAqB,CAAA;IAErB;;;OAGG;IACH,8CAAqB,CAAA;IAErB;;;OAGG;IACH,0CAAiB,CAAA;IAEjB;;;OAGG;IACH,gDAAuB,CAAA;IAEvB;;;OAGG;IACH,4CAAmB,CAAA;IAEnB;;;OAGG;IACH,4CAAmB,CAAA;IAEnB;;;OAGG;IACH,4CAAmB,CAAA;IAEnB;;;OAGG;IACH,sDAA6B,CAAA;AAC/B,CAAC,EAlHW,qBAAqB,qCAArB,qBAAqB,QAkHhC;AAED;;GAEG;AACH,MAAa,0BAA0B;IACrC;;OAEG;IACH,MAAM,CAAC,cAAc;QACnB,OAAO,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB;QACtB,OAAO;YACL,qBAAqB,CAAC,GAAG;YACzB,qBAAqB,CAAC,WAAW;YACjC,qBAAqB,CAAC,UAAU;YAChC,qBAAqB,CAAC,SAAS;YAC/B,qBAAqB,CAAC,QAAQ;YAC9B,qBAAqB,CAAC,WAAW;YACjC,qBAAqB,CAAC,UAAU;YAChC,qBAAqB,CAAC,SAAS;YAC/B,qBAAqB,CAAC,QAAQ;YAC9B,qBAAqB,CAAC,cAAc;YACpC,qBAAqB,CAAC,aAAa;YACnC,qBAAqB,CAAC,OAAO;YAC7B,qBAAqB,CAAC,YAAY;SACnC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB;QACxB,OAAO;YACL,qBAAqB,CAAC,QAAQ;YAC9B,qBAAqB,CAAC,QAAQ;YAC9B,qBAAqB,CAAC,MAAM;YAC5B,qBAAqB,CAAC,SAAS;YAC/B,qBAAqB,CAAC,OAAO;YAC7B,qBAAqB,CAAC,OAAO;SAC9B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB;QACvB,OAAO;YACL,qBAAqB,CAAC,UAAU;YAChC,qBAAqB,CAAC,QAAQ;YAC9B,qBAAqB,CAAC,UAAU;YAChC,qBAAqB,CAAC,QAAQ;YAC9B,qBAAqB,CAAC,QAAQ;YAC9B,qBAAqB,CAAC,QAAQ;SAC/B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB;QACvB,OAAO;YACL,qBAAqB,CAAC,MAAM;YAC5B,qBAAqB,CAAC,OAAO;SAC9B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB;QAC1B,OAAO;YACL,qBAAqB,CAAC,WAAW;YACjC,qBAAqB,CAAC,SAAS;YAC/B,qBAAqB,CAAC,WAAW;YACjC,qBAAqB,CAAC,SAAS;YAC/B,qBAAqB,CAAC,aAAa;YACnC,qBAAqB,CAAC,YAAY;SACnC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,MAA6B;QAC7C,OAAO,0BAA0B,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,MAA6B;QAC3C,OAAO,0BAA0B,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,MAA6B;QAC5C,OAAO,0BAA0B,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,MAA6B;QAC5C,OAAO,0BAA0B,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,MAA6B;QAC/C,OAAO,0BAA0B,CAAC,qBAAqB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,MAA6B;QACpD,OAAO;YACL,qBAAqB,CAAC,cAAc;YACpC,qBAAqB,CAAC,aAAa;YACnC,qBAAqB,CAAC,MAAM;YAC5B,qBAAqB,CAAC,OAAO;YAC7B,qBAAqB,CAAC,OAAO;SAC9B,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,aAAoC;QACvD,MAAM,UAAU,GAAgE;YAC9E,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,qBAAqB,CAAC,WAAW;YAC9D,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE,qBAAqB,CAAC,UAAU;YACrE,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAE,qBAAqB,CAAC,SAAS;YACnE,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,qBAAqB,CAAC,QAAQ;YACjE,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,qBAAqB,CAAC,WAAW;YACnE,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE,qBAAqB,CAAC,UAAU;YACrE,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAE,qBAAqB,CAAC,SAAS;YACnE,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,qBAAqB,CAAC,QAAQ;YACjE,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,qBAAqB,CAAC,QAAQ;YAChE,CAAC,qBAAqB,CAAC,cAAc,CAAC,EAAE,qBAAqB,CAAC,aAAa;YAC3E,CAAC,qBAAqB,CAAC,aAAa,CAAC,EAAE,qBAAqB,CAAC,QAAQ;YACrE,CAAC,qBAAqB,CAAC,YAAY,CAAC,EAAE,qBAAqB,CAAC,UAAU;YACtE,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,+BAA+B;YACtE,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,qBAAqB,CAAC,QAAQ;YAChE,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,WAAW;YACnD,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,qBAAqB,CAAC,YAAY;YAClE,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,WAAW;YACpD,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,WAAW;YAClD,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,qBAAqB,CAAC,YAAY;SACpE,CAAC;QAEF,OAAO,UAAU,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,aAAoC;QAC3D,MAAM,WAAW,GAAgE;YAC/E,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,gBAAgB;YACnD,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE,qBAAqB,CAAC,GAAG;YAC9D,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAE,qBAAqB,CAAC,WAAW;YACrE,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,qBAAqB,CAAC,UAAU;YACnE,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,qBAAqB,CAAC,SAAS;YACjE,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE,qBAAqB,CAAC,QAAQ;YACnE,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAE,qBAAqB,CAAC,WAAW;YACrE,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,qBAAqB,CAAC,UAAU;YACnE,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,qBAAqB,CAAC,SAAS;YACjE,CAAC,qBAAqB,CAAC,cAAc,CAAC,EAAE,qBAAqB,CAAC,QAAQ;YACtE,CAAC,qBAAqB,CAAC,aAAa,CAAC,EAAE,qBAAqB,CAAC,cAAc;YAC3E,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,qBAAqB,CAAC,aAAa;YACrE,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,qBAAqB,CAAC,QAAQ;YAChE,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,cAAc;YACpD,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,iBAAiB;YAC1D,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,eAAe;YACtD,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,eAAe;YACtD,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,cAAc;YACrD,CAAC,qBAAqB,CAAC,YAAY,CAAC,EAAE,qBAAqB,CAAC,MAAM;SACnE,CAAC;QAEF,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,aAAoC;QAC7D,MAAM,WAAW,GAA2D;YAC1E,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE;gBAC3B,qBAAqB,CAAC,WAAW;gBACjC,qBAAqB,CAAC,OAAO;gBAC7B,qBAAqB,CAAC,MAAM;aAC7B;YACD,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE;gBACnC,qBAAqB,CAAC,UAAU;gBAChC,qBAAqB,CAAC,MAAM;gBAC5B,qBAAqB,CAAC,OAAO;aAC9B;YACD,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAE;gBAClC,qBAAqB,CAAC,SAAS;gBAC/B,qBAAqB,CAAC,OAAO;gBAC7B,qBAAqB,CAAC,MAAM;aAC7B;YACD,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE;gBACjC,qBAAqB,CAAC,QAAQ;gBAC9B,qBAAqB,CAAC,MAAM;gBAC5B,qBAAqB,CAAC,OAAO;aAC9B;YACD,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE;gBAChC,qBAAqB,CAAC,WAAW;gBACjC,qBAAqB,CAAC,SAAS;gBAC/B,qBAAqB,CAAC,MAAM;aAC7B;YACD,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE;gBACnC,qBAAqB,CAAC,UAAU;gBAChC,qBAAqB,CAAC,MAAM;gBAC5B,qBAAqB,CAAC,OAAO;aAC9B;YACD,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAE;gBAClC,qBAAqB,CAAC,SAAS;gBAC/B,qBAAqB,CAAC,MAAM;aAC7B;YACD,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE;gBACjC,qBAAqB,CAAC,QAAQ;gBAC9B,qBAAqB,CAAC,MAAM;gBAC5B,qBAAqB,CAAC,OAAO;aAC9B;YACD,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE;gBAChC,qBAAqB,CAAC,cAAc;gBACpC,qBAAqB,CAAC,QAAQ;gBAC9B,qBAAqB,CAAC,SAAS;aAChC;YACD,CAAC,qBAAqB,CAAC,cAAc,CAAC,EAAE;gBACtC,qBAAqB,CAAC,aAAa;gBACnC,qBAAqB,CAAC,SAAS;gBAC/B,qBAAqB,CAAC,OAAO;aAC9B;YACD,CAAC,qBAAqB,CAAC,aAAa,CAAC,EAAE;gBACrC,qBAAqB,CAAC,QAAQ;gBAC9B,qBAAqB,CAAC,OAAO;gBAC7B,qBAAqB,CAAC,cAAc;aACrC;YACD,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE;gBAChC,qBAAqB,CAAC,QAAQ;gBAC9B,qBAAqB,CAAC,YAAY;aACnC;YACD,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,WAAW;YACjD,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE;gBAC9B,qBAAqB,CAAC,YAAY;gBAClC,qBAAqB,CAAC,SAAS;aAChC;YACD,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,WAAW;YAClD,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,WAAW;YAChD,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE;gBAC/B,qBAAqB,CAAC,aAAa;gBACnC,qBAAqB,CAAC,cAAc;gBACpC,qBAAqB,CAAC,SAAS;aAChC;YACD,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE;gBAC/B,qBAAqB,CAAC,YAAY;gBAClC,qBAAqB,CAAC,SAAS;aAChC;YACD,CAAC,qBAAqB,CAAC,YAAY,CAAC,EAAE;gBACpC,qBAAqB,CAAC,UAAU;gBAChC,qBAAqB,CAAC,MAAM;aAC7B;SACF,CAAC;QAEF,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,IAA2B,EAAE,EAAyB;QAC7E,OAAO,0BAA0B,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,MAA6B;QACjD,MAAM,YAAY,GAA0C;YAC1D,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,0CAA0C;YACvE,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE,qCAAqC;YAC1E,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAE,qCAAqC;YACzE,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,wCAAwC;YAC3E,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,wCAAwC;YAC1E,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE,2CAA2C;YAChF,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAE,sCAAsC;YAC1E,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,gDAAgD;YACnF,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,gDAAgD;YAClF,CAAC,qBAAqB,CAAC,cAAc,CAAC,EAAE,qCAAqC;YAC7E,CAAC,qBAAqB,CAAC,aAAa,CAAC,EAAE,4BAA4B;YACnE,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,2BAA2B;YAC7D,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,sCAAsC;YACxE,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,gCAAgC;YAChE,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,mCAAmC;YACtE,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,kCAAkC;YACnE,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,0CAA0C;YAC3E,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,sBAAsB;YACvD,CAAC,qBAAqB,CAAC,YAAY,CAAC,EAAE,uCAAuC;SAC9E,CAAC;QAEF,OAAO,YAAY,CAAC,MAAM,CAAC,IAAI,gBAAgB,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,MAA6B;QAC9C,MAAM,UAAU,GAA0C;YACxD,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,CAAC;YAClC,CAAC,qBAAqB,CAAC,cAAc,CAAC,EAAE,CAAC;YACzC,CAAC,qBAAqB,CAAC,aAAa,CAAC,EAAE,CAAC;YACxC,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,CAAC;YAClC,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,CAAC;YACpC,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE,CAAC;YACtC,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,CAAC;YACpC,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE,CAAC;YACtC,CAAC,qBAAqB,CAAC,YAAY,CAAC,EAAE,EAAE;YACxC,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,EAAE;YAC/B,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE;YACpC,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAE,EAAE;YACtC,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE;YACpC,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAE,EAAE;YACtC,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE;YACpC,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,EAAE;YACrC,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,EAAE;YACnC,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE;SACrC,CAAC;QAEF,OAAO,UAAU,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;IACnC,CAAC;CACF;AAnVD,gEAmVC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\event-processing-status.enum.ts"], "sourcesContent": ["/**\r\n * Event Processing Status Enum\r\n * \r\n * Represents the various stages of event processing in the security pipeline.\r\n * Used to track the lifecycle of security events from ingestion to resolution.\r\n * \r\n * Processing Flow:\r\n * RAW -> NORMALIZED -> ENRICHED -> CORRELATED -> ANALYZED -> RESOLVED/ARCHIVED\r\n */\r\nexport enum EventProcessingStatus {\r\n  /**\r\n   * Event has been received but not yet processed\r\n   * Initial state when event enters the system\r\n   */\r\n  RAW = 'raw',\r\n\r\n  /**\r\n   * Event is currently being normalized\r\n   * Transformation from raw format to standard schema\r\n   */\r\n  NORMALIZING = 'normalizing',\r\n\r\n  /**\r\n   * Event has been normalized to standard format\r\n   * Basic parsing and field extraction completed\r\n   */\r\n  NORMALIZED = 'normalized',\r\n\r\n  /**\r\n   * Event is being enriched with additional context\r\n   * Adding threat intelligence, asset information, etc.\r\n   */\r\n  ENRICHING = 'enriching',\r\n\r\n  /**\r\n   * Event has been enriched with contextual data\r\n   * Additional metadata and intelligence added\r\n   */\r\n  ENRICHED = 'enriched',\r\n\r\n  /**\r\n   * Event is being correlated with other events\r\n   * Pattern matching and relationship analysis\r\n   */\r\n  CORRELATING = 'correlating',\r\n\r\n  /**\r\n   * Event has been correlated with related events\r\n   * Relationships and patterns identified\r\n   */\r\n  CORRELATED = 'correlated',\r\n\r\n  /**\r\n   * Event is being analyzed for threats\r\n   * AI/ML analysis and risk assessment\r\n   */\r\n  ANALYZING = 'analyzing',\r\n\r\n  /**\r\n   * Event analysis is complete\r\n   * Threat assessment and scoring finished\r\n   */\r\n  ANALYZED = 'analyzed',\r\n\r\n  /**\r\n   * Event requires manual review\r\n   * Escalated for human analysis\r\n   */\r\n  PENDING_REVIEW = 'pending_review',\r\n\r\n  /**\r\n   * Event is under investigation\r\n   * Active incident response in progress\r\n   */\r\n  INVESTIGATING = 'investigating',\r\n\r\n  /**\r\n   * Event has been resolved\r\n   * Issue addressed and closed\r\n   */\r\n  RESOLVED = 'resolved',\r\n\r\n  /**\r\n   * Event has been archived\r\n   * Long-term storage for compliance\r\n   */\r\n  ARCHIVED = 'archived',\r\n\r\n  /**\r\n   * Event processing failed\r\n   * Error occurred during processing\r\n   */\r\n  FAILED = 'failed',\r\n\r\n  /**\r\n   * Event was discarded as false positive\r\n   * Determined to be benign or noise\r\n   */\r\n  DISCARDED = 'discarded',\r\n\r\n  /**\r\n   * Event processing was skipped\r\n   * Filtered out by rules or policies\r\n   */\r\n  SKIPPED = 'skipped',\r\n\r\n  /**\r\n   * Event is on hold\r\n   * Processing paused pending external input\r\n   */\r\n  ON_HOLD = 'on_hold',\r\n\r\n  /**\r\n   * Event processing timed out\r\n   * Exceeded maximum processing time\r\n   */\r\n  TIMEOUT = 'timeout',\r\n\r\n  /**\r\n   * Event is being reprocessed\r\n   * Retry after failure or update\r\n   */\r\n  REPROCESSING = 'reprocessing'\r\n}\r\n\r\n/**\r\n * Event Processing Status Utilities\r\n */\r\nexport class EventProcessingStatusUtils {\r\n  /**\r\n   * Get all processing statuses\r\n   */\r\n  static getAllStatuses(): EventProcessingStatus[] {\r\n    return Object.values(EventProcessingStatus);\r\n  }\r\n\r\n  /**\r\n   * Get active processing statuses (not terminal states)\r\n   */\r\n  static getActiveStatuses(): EventProcessingStatus[] {\r\n    return [\r\n      EventProcessingStatus.RAW,\r\n      EventProcessingStatus.NORMALIZING,\r\n      EventProcessingStatus.NORMALIZED,\r\n      EventProcessingStatus.ENRICHING,\r\n      EventProcessingStatus.ENRICHED,\r\n      EventProcessingStatus.CORRELATING,\r\n      EventProcessingStatus.CORRELATED,\r\n      EventProcessingStatus.ANALYZING,\r\n      EventProcessingStatus.ANALYZED,\r\n      EventProcessingStatus.PENDING_REVIEW,\r\n      EventProcessingStatus.INVESTIGATING,\r\n      EventProcessingStatus.ON_HOLD,\r\n      EventProcessingStatus.REPROCESSING,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get terminal processing statuses (final states)\r\n   */\r\n  static getTerminalStatuses(): EventProcessingStatus[] {\r\n    return [\r\n      EventProcessingStatus.RESOLVED,\r\n      EventProcessingStatus.ARCHIVED,\r\n      EventProcessingStatus.FAILED,\r\n      EventProcessingStatus.DISCARDED,\r\n      EventProcessingStatus.SKIPPED,\r\n      EventProcessingStatus.TIMEOUT,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get processing statuses that indicate success\r\n   */\r\n  static getSuccessStatuses(): EventProcessingStatus[] {\r\n    return [\r\n      EventProcessingStatus.NORMALIZED,\r\n      EventProcessingStatus.ENRICHED,\r\n      EventProcessingStatus.CORRELATED,\r\n      EventProcessingStatus.ANALYZED,\r\n      EventProcessingStatus.RESOLVED,\r\n      EventProcessingStatus.ARCHIVED,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get processing statuses that indicate failure or issues\r\n   */\r\n  static getFailureStatuses(): EventProcessingStatus[] {\r\n    return [\r\n      EventProcessingStatus.FAILED,\r\n      EventProcessingStatus.TIMEOUT,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get processing statuses that are in progress\r\n   */\r\n  static getInProgressStatuses(): EventProcessingStatus[] {\r\n    return [\r\n      EventProcessingStatus.NORMALIZING,\r\n      EventProcessingStatus.ENRICHING,\r\n      EventProcessingStatus.CORRELATING,\r\n      EventProcessingStatus.ANALYZING,\r\n      EventProcessingStatus.INVESTIGATING,\r\n      EventProcessingStatus.REPROCESSING,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Check if a status is terminal (final state)\r\n   */\r\n  static isTerminal(status: EventProcessingStatus): boolean {\r\n    return EventProcessingStatusUtils.getTerminalStatuses().includes(status);\r\n  }\r\n\r\n  /**\r\n   * Check if a status is active (not terminal)\r\n   */\r\n  static isActive(status: EventProcessingStatus): boolean {\r\n    return EventProcessingStatusUtils.getActiveStatuses().includes(status);\r\n  }\r\n\r\n  /**\r\n   * Check if a status indicates success\r\n   */\r\n  static isSuccess(status: EventProcessingStatus): boolean {\r\n    return EventProcessingStatusUtils.getSuccessStatuses().includes(status);\r\n  }\r\n\r\n  /**\r\n   * Check if a status indicates failure\r\n   */\r\n  static isFailure(status: EventProcessingStatus): boolean {\r\n    return EventProcessingStatusUtils.getFailureStatuses().includes(status);\r\n  }\r\n\r\n  /**\r\n   * Check if a status is in progress\r\n   */\r\n  static isInProgress(status: EventProcessingStatus): boolean {\r\n    return EventProcessingStatusUtils.getInProgressStatuses().includes(status);\r\n  }\r\n\r\n  /**\r\n   * Check if a status requires human attention\r\n   */\r\n  static requiresAttention(status: EventProcessingStatus): boolean {\r\n    return [\r\n      EventProcessingStatus.PENDING_REVIEW,\r\n      EventProcessingStatus.INVESTIGATING,\r\n      EventProcessingStatus.FAILED,\r\n      EventProcessingStatus.ON_HOLD,\r\n      EventProcessingStatus.TIMEOUT,\r\n    ].includes(status);\r\n  }\r\n\r\n  /**\r\n   * Get the next logical status in the processing pipeline\r\n   */\r\n  static getNextStatus(currentStatus: EventProcessingStatus): EventProcessingStatus | null {\r\n    const statusFlow: Record<EventProcessingStatus, EventProcessingStatus | null> = {\r\n      [EventProcessingStatus.RAW]: EventProcessingStatus.NORMALIZING,\r\n      [EventProcessingStatus.NORMALIZING]: EventProcessingStatus.NORMALIZED,\r\n      [EventProcessingStatus.NORMALIZED]: EventProcessingStatus.ENRICHING,\r\n      [EventProcessingStatus.ENRICHING]: EventProcessingStatus.ENRICHED,\r\n      [EventProcessingStatus.ENRICHED]: EventProcessingStatus.CORRELATING,\r\n      [EventProcessingStatus.CORRELATING]: EventProcessingStatus.CORRELATED,\r\n      [EventProcessingStatus.CORRELATED]: EventProcessingStatus.ANALYZING,\r\n      [EventProcessingStatus.ANALYZING]: EventProcessingStatus.ANALYZED,\r\n      [EventProcessingStatus.ANALYZED]: EventProcessingStatus.RESOLVED,\r\n      [EventProcessingStatus.PENDING_REVIEW]: EventProcessingStatus.INVESTIGATING,\r\n      [EventProcessingStatus.INVESTIGATING]: EventProcessingStatus.RESOLVED,\r\n      [EventProcessingStatus.REPROCESSING]: EventProcessingStatus.NORMALIZED,\r\n      [EventProcessingStatus.ON_HOLD]: null, // Requires manual intervention\r\n      [EventProcessingStatus.RESOLVED]: EventProcessingStatus.ARCHIVED,\r\n      [EventProcessingStatus.ARCHIVED]: null, // Terminal\r\n      [EventProcessingStatus.FAILED]: EventProcessingStatus.REPROCESSING,\r\n      [EventProcessingStatus.DISCARDED]: null, // Terminal\r\n      [EventProcessingStatus.SKIPPED]: null, // Terminal\r\n      [EventProcessingStatus.TIMEOUT]: EventProcessingStatus.REPROCESSING,\r\n    };\r\n\r\n    return statusFlow[currentStatus] || null;\r\n  }\r\n\r\n  /**\r\n   * Get the previous status in the processing pipeline\r\n   */\r\n  static getPreviousStatus(currentStatus: EventProcessingStatus): EventProcessingStatus | null {\r\n    const reverseFlow: Record<EventProcessingStatus, EventProcessingStatus | null> = {\r\n      [EventProcessingStatus.RAW]: null, // Initial state\r\n      [EventProcessingStatus.NORMALIZING]: EventProcessingStatus.RAW,\r\n      [EventProcessingStatus.NORMALIZED]: EventProcessingStatus.NORMALIZING,\r\n      [EventProcessingStatus.ENRICHING]: EventProcessingStatus.NORMALIZED,\r\n      [EventProcessingStatus.ENRICHED]: EventProcessingStatus.ENRICHING,\r\n      [EventProcessingStatus.CORRELATING]: EventProcessingStatus.ENRICHED,\r\n      [EventProcessingStatus.CORRELATED]: EventProcessingStatus.CORRELATING,\r\n      [EventProcessingStatus.ANALYZING]: EventProcessingStatus.CORRELATED,\r\n      [EventProcessingStatus.ANALYZED]: EventProcessingStatus.ANALYZING,\r\n      [EventProcessingStatus.PENDING_REVIEW]: EventProcessingStatus.ANALYZED,\r\n      [EventProcessingStatus.INVESTIGATING]: EventProcessingStatus.PENDING_REVIEW,\r\n      [EventProcessingStatus.RESOLVED]: EventProcessingStatus.INVESTIGATING,\r\n      [EventProcessingStatus.ARCHIVED]: EventProcessingStatus.RESOLVED,\r\n      [EventProcessingStatus.FAILED]: null, // Error state\r\n      [EventProcessingStatus.DISCARDED]: null, // Decision state\r\n      [EventProcessingStatus.SKIPPED]: null, // Filter state\r\n      [EventProcessingStatus.ON_HOLD]: null, // Manual state\r\n      [EventProcessingStatus.TIMEOUT]: null, // Error state\r\n      [EventProcessingStatus.REPROCESSING]: EventProcessingStatus.FAILED,\r\n    };\r\n\r\n    return reverseFlow[currentStatus] || null;\r\n  }\r\n\r\n  /**\r\n   * Get valid transition statuses from current status\r\n   */\r\n  static getValidTransitions(currentStatus: EventProcessingStatus): EventProcessingStatus[] {\r\n    const transitions: Record<EventProcessingStatus, EventProcessingStatus[]> = {\r\n      [EventProcessingStatus.RAW]: [\r\n        EventProcessingStatus.NORMALIZING,\r\n        EventProcessingStatus.SKIPPED,\r\n        EventProcessingStatus.FAILED,\r\n      ],\r\n      [EventProcessingStatus.NORMALIZING]: [\r\n        EventProcessingStatus.NORMALIZED,\r\n        EventProcessingStatus.FAILED,\r\n        EventProcessingStatus.TIMEOUT,\r\n      ],\r\n      [EventProcessingStatus.NORMALIZED]: [\r\n        EventProcessingStatus.ENRICHING,\r\n        EventProcessingStatus.SKIPPED,\r\n        EventProcessingStatus.FAILED,\r\n      ],\r\n      [EventProcessingStatus.ENRICHING]: [\r\n        EventProcessingStatus.ENRICHED,\r\n        EventProcessingStatus.FAILED,\r\n        EventProcessingStatus.TIMEOUT,\r\n      ],\r\n      [EventProcessingStatus.ENRICHED]: [\r\n        EventProcessingStatus.CORRELATING,\r\n        EventProcessingStatus.ANALYZING,\r\n        EventProcessingStatus.FAILED,\r\n      ],\r\n      [EventProcessingStatus.CORRELATING]: [\r\n        EventProcessingStatus.CORRELATED,\r\n        EventProcessingStatus.FAILED,\r\n        EventProcessingStatus.TIMEOUT,\r\n      ],\r\n      [EventProcessingStatus.CORRELATED]: [\r\n        EventProcessingStatus.ANALYZING,\r\n        EventProcessingStatus.FAILED,\r\n      ],\r\n      [EventProcessingStatus.ANALYZING]: [\r\n        EventProcessingStatus.ANALYZED,\r\n        EventProcessingStatus.FAILED,\r\n        EventProcessingStatus.TIMEOUT,\r\n      ],\r\n      [EventProcessingStatus.ANALYZED]: [\r\n        EventProcessingStatus.PENDING_REVIEW,\r\n        EventProcessingStatus.RESOLVED,\r\n        EventProcessingStatus.DISCARDED,\r\n      ],\r\n      [EventProcessingStatus.PENDING_REVIEW]: [\r\n        EventProcessingStatus.INVESTIGATING,\r\n        EventProcessingStatus.DISCARDED,\r\n        EventProcessingStatus.ON_HOLD,\r\n      ],\r\n      [EventProcessingStatus.INVESTIGATING]: [\r\n        EventProcessingStatus.RESOLVED,\r\n        EventProcessingStatus.ON_HOLD,\r\n        EventProcessingStatus.PENDING_REVIEW,\r\n      ],\r\n      [EventProcessingStatus.RESOLVED]: [\r\n        EventProcessingStatus.ARCHIVED,\r\n        EventProcessingStatus.REPROCESSING,\r\n      ],\r\n      [EventProcessingStatus.ARCHIVED]: [], // Terminal\r\n      [EventProcessingStatus.FAILED]: [\r\n        EventProcessingStatus.REPROCESSING,\r\n        EventProcessingStatus.DISCARDED,\r\n      ],\r\n      [EventProcessingStatus.DISCARDED]: [], // Terminal\r\n      [EventProcessingStatus.SKIPPED]: [], // Terminal\r\n      [EventProcessingStatus.ON_HOLD]: [\r\n        EventProcessingStatus.INVESTIGATING,\r\n        EventProcessingStatus.PENDING_REVIEW,\r\n        EventProcessingStatus.DISCARDED,\r\n      ],\r\n      [EventProcessingStatus.TIMEOUT]: [\r\n        EventProcessingStatus.REPROCESSING,\r\n        EventProcessingStatus.DISCARDED,\r\n      ],\r\n      [EventProcessingStatus.REPROCESSING]: [\r\n        EventProcessingStatus.NORMALIZED,\r\n        EventProcessingStatus.FAILED,\r\n      ],\r\n    };\r\n\r\n    return transitions[currentStatus] || [];\r\n  }\r\n\r\n  /**\r\n   * Check if a status transition is valid\r\n   */\r\n  static isValidTransition(from: EventProcessingStatus, to: EventProcessingStatus): boolean {\r\n    return EventProcessingStatusUtils.getValidTransitions(from).includes(to);\r\n  }\r\n\r\n  /**\r\n   * Get human-readable description of status\r\n   */\r\n  static getDescription(status: EventProcessingStatus): string {\r\n    const descriptions: Record<EventProcessingStatus, string> = {\r\n      [EventProcessingStatus.RAW]: 'Event received and queued for processing',\r\n      [EventProcessingStatus.NORMALIZING]: 'Converting event to standard format',\r\n      [EventProcessingStatus.NORMALIZED]: 'Event normalized to standard schema',\r\n      [EventProcessingStatus.ENRICHING]: 'Adding contextual information to event',\r\n      [EventProcessingStatus.ENRICHED]: 'Event enriched with additional context',\r\n      [EventProcessingStatus.CORRELATING]: 'Analyzing relationships with other events',\r\n      [EventProcessingStatus.CORRELATED]: 'Event correlated with related events',\r\n      [EventProcessingStatus.ANALYZING]: 'Performing threat analysis and risk assessment',\r\n      [EventProcessingStatus.ANALYZED]: 'Analysis complete, threat assessment available',\r\n      [EventProcessingStatus.PENDING_REVIEW]: 'Awaiting manual review and decision',\r\n      [EventProcessingStatus.INVESTIGATING]: 'Under active investigation',\r\n      [EventProcessingStatus.RESOLVED]: 'Issue resolved and closed',\r\n      [EventProcessingStatus.ARCHIVED]: 'Event archived for long-term storage',\r\n      [EventProcessingStatus.FAILED]: 'Processing failed due to error',\r\n      [EventProcessingStatus.DISCARDED]: 'Event discarded as false positive',\r\n      [EventProcessingStatus.SKIPPED]: 'Event skipped by filtering rules',\r\n      [EventProcessingStatus.ON_HOLD]: 'Processing paused pending external input',\r\n      [EventProcessingStatus.TIMEOUT]: 'Processing timed out',\r\n      [EventProcessingStatus.REPROCESSING]: 'Event being reprocessed after failure',\r\n    };\r\n\r\n    return descriptions[status] || 'Unknown status';\r\n  }\r\n\r\n  /**\r\n   * Get status priority for sorting (lower number = higher priority)\r\n   */\r\n  static getPriority(status: EventProcessingStatus): number {\r\n    const priorities: Record<EventProcessingStatus, number> = {\r\n      [EventProcessingStatus.FAILED]: 1,\r\n      [EventProcessingStatus.TIMEOUT]: 2,\r\n      [EventProcessingStatus.PENDING_REVIEW]: 3,\r\n      [EventProcessingStatus.INVESTIGATING]: 4,\r\n      [EventProcessingStatus.ON_HOLD]: 5,\r\n      [EventProcessingStatus.ANALYZING]: 6,\r\n      [EventProcessingStatus.CORRELATING]: 7,\r\n      [EventProcessingStatus.ENRICHING]: 8,\r\n      [EventProcessingStatus.NORMALIZING]: 9,\r\n      [EventProcessingStatus.REPROCESSING]: 10,\r\n      [EventProcessingStatus.RAW]: 11,\r\n      [EventProcessingStatus.ANALYZED]: 12,\r\n      [EventProcessingStatus.CORRELATED]: 13,\r\n      [EventProcessingStatus.ENRICHED]: 14,\r\n      [EventProcessingStatus.NORMALIZED]: 15,\r\n      [EventProcessingStatus.RESOLVED]: 16,\r\n      [EventProcessingStatus.DISCARDED]: 17,\r\n      [EventProcessingStatus.SKIPPED]: 18,\r\n      [EventProcessingStatus.ARCHIVED]: 19,\r\n    };\r\n\r\n    return priorities[status] || 999;\r\n  }\r\n}\r\n"], "version": 3}