c269d2f2be29eb0f90ebd37923fdefeb
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const base_specification_1 = require("../../domain/base-specification");
// Test specification implementations
class NameSpecification extends base_specification_1.BaseSpecification {
    constructor(expectedName) {
        super();
        this.expectedName = expectedName;
    }
    isSatisfiedBy(entity) {
        return entity.name === this.expectedName;
    }
    getDescription() {
        return `Name equals '${this.expectedName}'`;
    }
}
class AgeRangeSpecification extends base_specification_1.BaseSpecification {
    constructor(minAge, maxAge) {
        super();
        this.minAge = minAge;
        this.maxAge = maxAge;
    }
    isSatisfiedBy(entity) {
        return entity.age >= this.minAge && entity.age <= this.maxAge;
    }
    getDescription() {
        return `Age between ${this.minAge} and ${this.maxAge}`;
    }
}
class StatusSpecification extends base_specification_1.BaseSpecification {
    constructor(expectedStatus) {
        super();
        this.expectedStatus = expectedStatus;
    }
    isSatisfiedBy(entity) {
        return entity.status === this.expectedStatus;
    }
    getDescription() {
        return `Status is '${this.expectedStatus}'`;
    }
}
class HasTagSpecification extends base_specification_1.BaseSpecification {
    constructor(tag) {
        super();
        this.tag = tag;
    }
    isSatisfiedBy(entity) {
        return entity.tags.includes(this.tag);
    }
    getDescription() {
        return `Has tag '${this.tag}'`;
    }
}
describe('BaseSpecification', () => {
    let testEntity;
    beforeEach(() => {
        testEntity = {
            id: '1',
            name: 'John Doe',
            age: 30,
            status: 'active',
            tags: ['developer', 'senior']
        };
    });
    describe('basic specification functionality', () => {
        it('should satisfy specification when condition is met', () => {
            const spec = new NameSpecification('John Doe');
            expect(spec.isSatisfiedBy(testEntity)).toBe(true);
        });
        it('should not satisfy specification when condition is not met', () => {
            const spec = new NameSpecification('Jane Doe');
            expect(spec.isSatisfiedBy(testEntity)).toBe(false);
        });
        it('should provide description', () => {
            const spec = new NameSpecification('John Doe');
            expect(spec.getDescription()).toBe("Name equals 'John Doe'");
        });
        it('should provide string representation', () => {
            const spec = new NameSpecification('John Doe');
            expect(spec.toString()).toBe("Name equals 'John Doe'");
        });
    });
    describe('AND specification', () => {
        it('should satisfy when both specifications are satisfied', () => {
            const nameSpec = new NameSpecification('John Doe');
            const ageSpec = new AgeRangeSpecification(25, 35);
            const andSpec = nameSpec.and(ageSpec);
            expect(andSpec.isSatisfiedBy(testEntity)).toBe(true);
        });
        it('should not satisfy when first specification is not satisfied', () => {
            const nameSpec = new NameSpecification('Jane Doe');
            const ageSpec = new AgeRangeSpecification(25, 35);
            const andSpec = nameSpec.and(ageSpec);
            expect(andSpec.isSatisfiedBy(testEntity)).toBe(false);
        });
        it('should not satisfy when second specification is not satisfied', () => {
            const nameSpec = new NameSpecification('John Doe');
            const ageSpec = new AgeRangeSpecification(35, 40);
            const andSpec = nameSpec.and(ageSpec);
            expect(andSpec.isSatisfiedBy(testEntity)).toBe(false);
        });
        it('should not satisfy when both specifications are not satisfied', () => {
            const nameSpec = new NameSpecification('Jane Doe');
            const ageSpec = new AgeRangeSpecification(35, 40);
            const andSpec = nameSpec.and(ageSpec);
            expect(andSpec.isSatisfiedBy(testEntity)).toBe(false);
        });
        it('should provide correct description for AND specification', () => {
            const nameSpec = new NameSpecification('John Doe');
            const ageSpec = new AgeRangeSpecification(25, 35);
            const andSpec = nameSpec.and(ageSpec);
            expect(andSpec.getDescription()).toBe("(Name equals 'John Doe') AND (Age between 25 and 35)");
        });
    });
    describe('OR specification', () => {
        it('should satisfy when first specification is satisfied', () => {
            const nameSpec = new NameSpecification('John Doe');
            const ageSpec = new AgeRangeSpecification(35, 40);
            const orSpec = nameSpec.or(ageSpec);
            expect(orSpec.isSatisfiedBy(testEntity)).toBe(true);
        });
        it('should satisfy when second specification is satisfied', () => {
            const nameSpec = new NameSpecification('Jane Doe');
            const ageSpec = new AgeRangeSpecification(25, 35);
            const orSpec = nameSpec.or(ageSpec);
            expect(orSpec.isSatisfiedBy(testEntity)).toBe(true);
        });
        it('should satisfy when both specifications are satisfied', () => {
            const nameSpec = new NameSpecification('John Doe');
            const ageSpec = new AgeRangeSpecification(25, 35);
            const orSpec = nameSpec.or(ageSpec);
            expect(orSpec.isSatisfiedBy(testEntity)).toBe(true);
        });
        it('should not satisfy when both specifications are not satisfied', () => {
            const nameSpec = new NameSpecification('Jane Doe');
            const ageSpec = new AgeRangeSpecification(35, 40);
            const orSpec = nameSpec.or(ageSpec);
            expect(orSpec.isSatisfiedBy(testEntity)).toBe(false);
        });
        it('should provide correct description for OR specification', () => {
            const nameSpec = new NameSpecification('John Doe');
            const ageSpec = new AgeRangeSpecification(25, 35);
            const orSpec = nameSpec.or(ageSpec);
            expect(orSpec.getDescription()).toBe("(Name equals 'John Doe') OR (Age between 25 and 35)");
        });
    });
    describe('NOT specification', () => {
        it('should satisfy when original specification is not satisfied', () => {
            const nameSpec = new NameSpecification('Jane Doe');
            const notSpec = nameSpec.not();
            expect(notSpec.isSatisfiedBy(testEntity)).toBe(true);
        });
        it('should not satisfy when original specification is satisfied', () => {
            const nameSpec = new NameSpecification('John Doe');
            const notSpec = nameSpec.not();
            expect(notSpec.isSatisfiedBy(testEntity)).toBe(false);
        });
        it('should provide correct description for NOT specification', () => {
            const nameSpec = new NameSpecification('John Doe');
            const notSpec = nameSpec.not();
            expect(notSpec.getDescription()).toBe("NOT (Name equals 'John Doe')");
        });
    });
    describe('complex specification combinations', () => {
        it('should handle complex AND/OR combinations', () => {
            const nameSpec = new NameSpecification('John Doe');
            const ageSpec = new AgeRangeSpecification(25, 35);
            const statusSpec = new StatusSpecification('active');
            // (Name = 'John Doe' AND Age 25-35) OR Status = 'active'
            const complexSpec = nameSpec.and(ageSpec).or(statusSpec);
            expect(complexSpec.isSatisfiedBy(testEntity)).toBe(true);
        });
        it('should handle NOT with AND/OR combinations', () => {
            const nameSpec = new NameSpecification('Jane Doe');
            const statusSpec = new StatusSpecification('inactive');
            // NOT (Name = 'Jane Doe' OR Status = 'inactive')
            const complexSpec = nameSpec.or(statusSpec).not();
            expect(complexSpec.isSatisfiedBy(testEntity)).toBe(true);
        });
        it('should provide correct description for complex specifications', () => {
            const nameSpec = new NameSpecification('John Doe');
            const ageSpec = new AgeRangeSpecification(25, 35);
            const complexSpec = nameSpec.and(ageSpec).not();
            expect(complexSpec.getDescription()).toBe("NOT ((Name equals 'John Doe') AND (Age between 25 and 35))");
        });
    });
    describe('specification equality', () => {
        it('should be equal to specification of same type with same description', () => {
            const spec1 = new NameSpecification('John Doe');
            const spec2 = new NameSpecification('John Doe');
            expect(spec1.equals(spec2)).toBe(true);
        });
        it('should not be equal to specification of same type with different description', () => {
            const spec1 = new NameSpecification('John Doe');
            const spec2 = new NameSpecification('Jane Doe');
            expect(spec1.equals(spec2)).toBe(false);
        });
        it('should not be equal to specification of different type', () => {
            const nameSpec = new NameSpecification('John Doe');
            const ageSpec = new AgeRangeSpecification(25, 35);
            expect(nameSpec.equals(ageSpec)).toBe(false);
        });
    });
    describe('JSON serialization', () => {
        it('should serialize to JSON', () => {
            const spec = new NameSpecification('John Doe');
            const json = spec.toJSON();
            expect(json).toEqual({
                type: 'NameSpecification',
                description: "Name equals 'John Doe'"
            });
        });
        it('should serialize complex specifications to JSON', () => {
            const nameSpec = new NameSpecification('John Doe');
            const ageSpec = new AgeRangeSpecification(25, 35);
            const andSpec = nameSpec.and(ageSpec);
            const json = andSpec.toJSON();
            expect(json.type).toBe('AndSpecification');
            expect(json.left).toEqual({
                type: 'NameSpecification',
                description: "Name equals 'John Doe'"
            });
            expect(json.right).toEqual({
                type: 'AgeRangeSpecification',
                description: 'Age between 25 and 35'
            });
        });
    });
    describe('AlwaysTrueSpecification', () => {
        it('should always be satisfied', () => {
            const spec = new base_specification_1.AlwaysTrueSpecification();
            expect(spec.isSatisfiedBy(testEntity)).toBe(true);
            expect(spec.isSatisfiedBy({})).toBe(true);
        });
        it('should provide correct description', () => {
            const spec = new base_specification_1.AlwaysTrueSpecification();
            expect(spec.getDescription()).toBe('Always True');
        });
    });
    describe('AlwaysFalseSpecification', () => {
        it('should never be satisfied', () => {
            const spec = new base_specification_1.AlwaysFalseSpecification();
            expect(spec.isSatisfiedBy(testEntity)).toBe(false);
            expect(spec.isSatisfiedBy({})).toBe(false);
        });
        it('should provide correct description', () => {
            const spec = new base_specification_1.AlwaysFalseSpecification();
            expect(spec.getDescription()).toBe('Always False');
        });
    });
    describe('SpecificationBuilder', () => {
        it('should build specification starting with always true', () => {
            const spec = base_specification_1.SpecificationBuilder
                .alwaysTrue()
                .and(new NameSpecification('John Doe'))
                .build();
            expect(spec.isSatisfiedBy(testEntity)).toBe(true);
        });
        it('should build specification starting with always false', () => {
            const spec = base_specification_1.SpecificationBuilder
                .alwaysFalse()
                .or(new NameSpecification('John Doe'))
                .build();
            expect(spec.isSatisfiedBy(testEntity)).toBe(true);
        });
        it('should build complex specification with fluent interface', () => {
            const spec = base_specification_1.SpecificationBuilder
                .create(new NameSpecification('John Doe'))
                .and(new AgeRangeSpecification(25, 35))
                .or(new StatusSpecification('active'))
                .not()
                .build();
            expect(spec.isSatisfiedBy(testEntity)).toBe(false);
        });
        it('should chain multiple operations', () => {
            const spec = base_specification_1.SpecificationBuilder
                .create(new NameSpecification('Jane Doe'))
                .or(new NameSpecification('John Doe'))
                .and(new StatusSpecification('active'))
                .build();
            expect(spec.isSatisfiedBy(testEntity)).toBe(true);
        });
    });
    describe('SpecificationUtils', () => {
        let specifications;
        beforeEach(() => {
            specifications = [
                new NameSpecification('John Doe'),
                new AgeRangeSpecification(25, 35),
                new StatusSpecification('active'),
                new HasTagSpecification('developer')
            ];
        });
        it('should evaluate all specifications', () => {
            const results = base_specification_1.SpecificationUtils.evaluateAll(testEntity, specifications);
            expect(results).toEqual({
                NameSpecification: true,
                AgeRangeSpecification: true,
                StatusSpecification: true,
                HasTagSpecification: true
            });
        });
        it('should find satisfied specifications', () => {
            const satisfied = base_specification_1.SpecificationUtils.findSatisfied(testEntity, specifications);
            expect(satisfied).toHaveLength(4);
            expect(satisfied).toEqual(specifications);
        });
        it('should find unsatisfied specifications', () => {
            const unsatisfiedSpecs = [
                new NameSpecification('Jane Doe'),
                new AgeRangeSpecification(35, 40),
                new StatusSpecification('inactive')
            ];
            const unsatisfied = base_specification_1.SpecificationUtils.findUnsatisfied(testEntity, unsatisfiedSpecs);
            expect(unsatisfied).toHaveLength(3);
            expect(unsatisfied).toEqual(unsatisfiedSpecs);
        });
        it('should check if all specifications are satisfied', () => {
            const allSatisfied = base_specification_1.SpecificationUtils.allSatisfied(testEntity, specifications);
            expect(allSatisfied).toBe(true);
            const mixedSpecs = [
                ...specifications,
                new NameSpecification('Jane Doe')
            ];
            const notAllSatisfied = base_specification_1.SpecificationUtils.allSatisfied(testEntity, mixedSpecs);
            expect(notAllSatisfied).toBe(false);
        });
        it('should check if any specification is satisfied', () => {
            const anySatisfied = base_specification_1.SpecificationUtils.anySatisfied(testEntity, specifications);
            expect(anySatisfied).toBe(true);
            const noneSpecs = [
                new NameSpecification('Jane Doe'),
                new AgeRangeSpecification(35, 40)
            ];
            const noneSatisfied = base_specification_1.SpecificationUtils.anySatisfied(testEntity, noneSpecs);
            expect(noneSatisfied).toBe(false);
        });
        it('should handle empty specification arrays', () => {
            expect(base_specification_1.SpecificationUtils.evaluateAll(testEntity, [])).toEqual({});
            expect(base_specification_1.SpecificationUtils.findSatisfied(testEntity, [])).toEqual([]);
            expect(base_specification_1.SpecificationUtils.findUnsatisfied(testEntity, [])).toEqual([]);
            expect(base_specification_1.SpecificationUtils.allSatisfied(testEntity, [])).toBe(true);
            expect(base_specification_1.SpecificationUtils.anySatisfied(testEntity, [])).toBe(false);
        });
    });
    describe('edge cases', () => {
        it('should handle null entity gracefully', () => {
            // Create a specification that handles null entities properly
            class NullSafeNameSpecification extends base_specification_1.BaseSpecification {
                constructor(expectedName) {
                    super();
                    this.expectedName = expectedName;
                }
                isSatisfiedBy(entity) {
                    return entity?.name === this.expectedName;
                }
                getDescription() {
                    return `Name equals '${this.expectedName}'`;
                }
            }
            const spec = new NullSafeNameSpecification('John Doe');
            expect(() => spec.isSatisfiedBy(null)).not.toThrow();
            expect(spec.isSatisfiedBy(null)).toBe(false);
        });
        it('should handle undefined entity gracefully', () => {
            // Create a specification that handles undefined entities properly
            class NullSafeNameSpecification extends base_specification_1.BaseSpecification {
                constructor(expectedName) {
                    super();
                    this.expectedName = expectedName;
                }
                isSatisfiedBy(entity) {
                    return entity?.name === this.expectedName;
                }
                getDescription() {
                    return `Name equals '${this.expectedName}'`;
                }
            }
            const spec = new NullSafeNameSpecification('John Doe');
            expect(() => spec.isSatisfiedBy(undefined)).not.toThrow();
            expect(spec.isSatisfiedBy(undefined)).toBe(false);
        });
        it('should handle entity with missing properties', () => {
            const incompleteEntity = { id: '1', name: 'John' };
            const ageSpec = new AgeRangeSpecification(25, 35);
            expect(() => ageSpec.isSatisfiedBy(incompleteEntity)).not.toThrow();
            expect(ageSpec.isSatisfiedBy(incompleteEntity)).toBe(false);
        });
        it('should handle deeply nested specifications', () => {
            let spec = new NameSpecification('John Doe');
            // Create deeply nested specification
            for (let i = 0; i < 10; i++) {
                spec = spec.and(new StatusSpecification('active'));
            }
            expect(spec.isSatisfiedBy(testEntity)).toBe(true);
            expect(spec.getDescription()).toContain('AND');
        });
        it('should handle specifications with special characters in descriptions', () => {
            class SpecialCharSpec extends base_specification_1.BaseSpecification {
                isSatisfiedBy(entity) {
                    return true;
                }
                getDescription() {
                    return 'Special chars: ()[]{}|&*+?^$\\.';
                }
            }
            const spec = new SpecialCharSpec();
            expect(spec.getDescription()).toBe('Special chars: ()[]{}|&*+?^$\\.');
            expect(spec.toString()).toBe('Special chars: ()[]{}|&*+?^$\\.');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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