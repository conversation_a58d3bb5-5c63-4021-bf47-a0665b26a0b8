{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\config\\test-configuration.service.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,qCAAwD;AACxD,6CAAsD;AAEtD;;;;;;;;;;GAUG;AAEI,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAKnC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAJxC,WAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;IAIR,CAAC;IAE7D;;OAEG;IACH,qBAAqB;QACnB,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,KAAK,MAAM,CAAC;QACvC,MAAM,UAAU,GAAG,iBAAiB,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;QAE5F,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;YACzD,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;YAClD,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,kBAAkB,EAAE,WAAW,CAAC;YACjE,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,kBAAkB,EAAE,eAAe,CAAC;YACrE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;YACrF,QAAQ,EAAE,CAAC,SAAS,GAAG,6BAA6B,CAAC;YACrD,WAAW,EAAE,IAAI;YACjB,UAAU,EAAE,IAAI;YAChB,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,CAAC;YACzD,qBAAqB,EAAE,IAAI;YAC3B,KAAK,EAAE;gBACL,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE,KAAK;gBACrB,OAAO,EAAE,KAAK;aACf;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,iBAAiB,EAAE,WAAW,CAAC;YAC5D,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC;YACrD,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;YAC9C,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,qBAAqB,CAAC;YACvD,SAAS,EAAE,gBAAgB;YAC3B,oBAAoB,EAAE,GAAG;YACzB,oBAAoB,EAAE,CAAC;YACvB,WAAW,EAAE,IAAI;SAClB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAChC,iBAAiB,EAAE;gBACjB,gBAAgB,EAAE,CAAC;gBACnB,YAAY,EAAE,EAAE;gBAChB,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE;oBACP,IAAI,EAAE,aAAa;oBACnB,KAAK,EAAE,IAAI;iBACZ;aACF;YACD,QAAQ,EAAE;gBACR,eAAe,EAAE,KAAK;gBACtB,eAAe,EAAE,CAAC;aACnB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,OAAO;YACL,cAAc,EAAE,IAAI;YACpB,aAAa,EAAE,eAAe;YAC9B,oBAAoB,EAAE,KAAK;YAC3B,sBAAsB,EAAE,IAAI;YAC5B,qBAAqB,EAAE,KAAK;YAC5B,uBAAuB,EAAE,KAAK;YAC9B,kBAAkB,EAAE,IAAI;YACxB,uBAAuB,EAAE,KAAK;YAC9B,+BAA+B,EAAE,IAAI;YACrC,sBAAsB,EAAE,IAAI;YAC5B,2BAA2B,EAAE,KAAK;YAClC,mBAAmB,EAAE,IAAI;YACzB,mBAAmB,EAAE,IAAI;YACzB,sBAAsB,EAAE,KAAK;YAC7B,mBAAmB,EAAE,KAAK;YAC1B,wBAAwB,EAAE,IAAI;YAC9B,qBAAqB,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;SACjD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO;YACL,uBAAuB,EAAE,CAAC;YAC1B,uBAAuB,EAAE,KAAK;YAC9B,gBAAgB,EAAE,CAAC;YACnB,sBAAsB,EAAE,IAAI;YAC5B,kBAAkB,EAAE,KAAK;YACzB,0BAA0B,EAAE,CAAC;YAC7B,uBAAuB,EAAE,KAAK;YAC9B,2BAA2B,EAAE,IAAI;YACjC,yBAAyB,EAAE,IAAI;YAC/B,0BAA0B,EAAE,IAAI;YAChC,yBAAyB,EAAE,KAAK;YAChC,eAAe,EAAE,IAAI;YACrB,iBAAiB,EAAE,CAAC;YACpB,iBAAiB,EAAE,IAAI;YACvB,kBAAkB,EAAE,IAAI;YACxB,cAAc,EAAE,IAAI;YACpB,qBAAqB,EAAE,IAAI;YAC3B,uBAAuB,EAAE,CAAC;YAC1B,qBAAqB,EAAE,KAAK;YAC5B,sBAAsB,EAAE,IAAI;YAC5B,yBAAyB,EAAE,CAAC;YAC5B,4BAA4B,EAAE,KAAK;YACnC,oBAAoB,EAAE,IAAI;YAC1B,iBAAiB,EAAE,KAAK;SACzB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,4BAA4B;QAC1B,OAAO;YACL,YAAY,EAAE;gBACZ,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,uBAAuB;gBAChC,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,CAAC;aACX;YACD,kBAAkB,EAAE;gBAClB,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,cAAc;gBACtB,OAAO,EAAE,uBAAuB;aACjC;YACD,aAAa,EAAE;gBACb,KAAK,EAAE;oBACL,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,IAAI;oBACd,QAAQ,EAAE,MAAM;iBACjB;gBACD,KAAK,EAAE;oBACL,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,IAAI;oBACd,UAAU,EAAE,6BAA6B;iBAC1C;gBACD,GAAG,EAAE;oBACH,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,IAAI;oBACd,QAAQ,EAAE,MAAM;iBACjB;aACF;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,KAAK;gBACd,aAAa,EAAE,IAAI;gBACnB,OAAO,EAAE,uBAAuB;gBAChC,OAAO,EAAE,KAAK;aACf;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO;YACL,GAAG,EAAE;gBACH,MAAM,EAAE,sCAAsC;gBAC9C,SAAS,EAAE,IAAI;gBACf,MAAM,EAAE,eAAe;gBACvB,QAAQ,EAAE,qBAAqB;aAChC;YACD,UAAU,EAAE;gBACV,SAAS,EAAE,aAAa;gBACxB,GAAG,EAAE,mCAAmC;gBACxC,QAAQ,EAAE,EAAE;aACb;YACD,SAAS,EAAE;gBACT,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,KAAK;gBACf,GAAG,EAAE,IAAI;aACV;YACD,IAAI,EAAE;gBACJ,MAAM,EAAE,CAAC,uBAAuB,EAAE,uBAAuB,CAAC;gBAC1D,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;aACnD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO;YACL,WAAW,EAAE;gBACX,kBAAkB,EAAE,EAAE;gBACtB,cAAc,EAAE,KAAK,EAAE,aAAa;gBACpC,YAAY,EAAE,KAAK,EAAE,WAAW;gBAChC,SAAS,EAAE,IAAI,EAAE,4BAA4B;gBAC7C,SAAS,EAAE;oBACT;wBACE,IAAI,EAAE,oBAAoB;wBAC1B,MAAM,EAAE,EAAE;wBACV,SAAS,EAAE,CAAC,2BAA2B,EAAE,gCAAgC,CAAC;qBAC3E;oBACD;wBACE,IAAI,EAAE,sBAAsB;wBAC5B,MAAM,EAAE,EAAE;wBACV,SAAS,EAAE,CAAC,qBAAqB,EAAE,oBAAoB,CAAC;qBACzD;oBACD;wBACE,IAAI,EAAE,qBAAqB;wBAC3B,MAAM,EAAE,EAAE;wBACV,SAAS,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;qBAC5C;oBACD;wBACE,IAAI,EAAE,WAAW;wBACjB,MAAM,EAAE,EAAE;wBACV,SAAS,EAAE,CAAC,UAAU,EAAE,uBAAuB,CAAC;qBACjD;iBACF;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,GAAG;gBACnB,mBAAmB,EAAE,IAAI;gBACzB,WAAW,EAAE,EAAE;gBACf,OAAO,EAAE,KAAK;gBACd,sBAAsB,EAAE,GAAG,EAAE,eAAe;gBAC5C,mBAAmB,EAAE,IAAI,EAAE,KAAK;gBAChC,oBAAoB,EAAE,GAAG,EAAE,sBAAsB;aAClD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB;QAC1B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC5C,IAAI,CAAC,cAAc,GAAG,IAAI,oBAAU,CAAC,MAAM,CAAC,CAAC;YAE7C,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;YAE1D,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;gBAC7D,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;gBACzC,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;gBACpC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,cAAmB;QACxC,IAAI,CAAC;YACH,MAAM,oBAAoB,GAAG,cAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;YAEtE,sCAAsC;YACtC,oBAAoB,CAAC,gBAAgB,CAAC,sBAAa,CAAC,CAAC,QAAQ,CAAC;gBAC5D,GAAG,EAAE,CAAC,GAAW,EAAE,YAAkB,EAAE,EAAE;oBACvC,MAAM,WAAW,GAAG;wBAClB,GAAG,IAAI,CAAC,qBAAqB,EAAE;wBAC/B,GAAG,IAAI,CAAC,kBAAkB,EAAE;wBAC5B,GAAG,IAAI,CAAC,uBAAuB,EAAE;wBACjC,GAAG,IAAI,CAAC,qBAAqB,EAAE;wBAC/B,GAAG,IAAI,CAAC,4BAA4B,EAAE;wBACtC,GAAG,IAAI,CAAC,qBAAqB,EAAE;wBAC/B,GAAG,IAAI,CAAC,wBAAwB,EAAE;qBACnC,CAAC;oBAEF,OAAO,WAAW,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;gBAC1E,CAAC;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,GAAG,MAAM,oBAAoB,CAAC,OAAO,EAAE,CAAC;YAE1D,qCAAqC;YACrC,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,qBAAqB,EAAE,KAAK,CAAC,EAAE,CAAC;gBACzD,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,eAAM,EAAE,CAAC,CAAC;YAC7C,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;gBACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAChE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,sBAAsB;QASpB,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,MAAM;YACvC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,KAAK,MAAM;YAC/B,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,OAAO,EAAE,EAAE,CAAC;YAC9D,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,KAAK,OAAO;YACpD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,KAAK,MAAM;YAC9C,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,KAAK,MAAM;YAC5C,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,KAAK,MAAM;SACvC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB;QAK3B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACnD,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC/B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,uCAAuC;QACvC,MAAM,eAAe,GAAG;YACtB,cAAc;YACd,cAAc;YACd,kBAAkB;YAClB,kBAAkB;SACnB,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzB,QAAQ,CAAC,IAAI,CAAC,iCAAiC,MAAM,EAAE,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAED,wBAAwB;QACxB,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;QACpC,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1E,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,mBAAmB,WAAW,0CAA0C,CAAC,CAAC;QACxF,CAAC;QAED,yBAAyB;QACzB,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC;QACpD,MAAM,SAAS,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,QAAQ;QAC7C,IAAI,WAAW,GAAG,SAAS,EAAE,CAAC;YAC5B,QAAQ,CAAC,IAAI,CAAC,yBAAyB,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACzG,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO;YACL,SAAS,EAAE;gBACT,SAAS;gBACT,CAAC,YAAY,EAAE;wBACb,eAAe,EAAE,gBAAgB;wBACjC,UAAU,EAAE,WAAW;wBACvB,iBAAiB,EAAE,aAAa;wBAChC,aAAa,EAAE,SAAS;wBACxB,iBAAiB,EAAE,KAAK;wBACxB,mBAAmB,EAAE,IAAI;qBAC1B,CAAC;gBACF,CAAC,qBAAqB,EAAE;wBACtB,UAAU,EAAE,gBAAgB;wBAC5B,QAAQ,EAAE,kBAAkB;wBAC5B,MAAM,EAAE,IAAI;wBACZ,QAAQ,EAAE,KAAK;wBACf,SAAS,EAAE,uCAAuC;qBACnD,CAAC;aACH;YACD,iBAAiB,EAAE;gBACjB,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,cAAc;aACf;YACD,iBAAiB,EAAE,YAAY;YAC/B,mBAAmB,EAAE;gBACnB,kBAAkB;gBAClB,wBAAwB;gBACxB,wBAAwB;gBACxB,gBAAgB;gBAChB,kBAAkB;gBAClB,wBAAwB;gBACxB,mBAAmB;gBACnB,uBAAuB;aACxB;YACD,iBAAiB,EAAE;gBACjB,MAAM,EAAE;oBACN,QAAQ,EAAE,EAAE;oBACZ,SAAS,EAAE,EAAE;oBACb,KAAK,EAAE,EAAE;oBACT,UAAU,EAAE,EAAE;iBACf;gBACD,6CAA6C,EAAE;oBAC7C,QAAQ,EAAE,EAAE;oBACZ,SAAS,EAAE,EAAE;oBACb,KAAK,EAAE,EAAE;oBACT,UAAU,EAAE,EAAE;iBACf;gBACD,qCAAqC,EAAE;oBACrC,QAAQ,EAAE,EAAE;oBACZ,SAAS,EAAE,EAAE;oBACb,KAAK,EAAE,EAAE;oBACT,UAAU,EAAE,EAAE;iBACf;aACF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO;YACL,QAAQ,EAAE;gBACR,KAAK,EAAE;oBACL,KAAK,EAAE;wBACL,EAAE,EAAE,eAAe;wBACnB,KAAK,EAAE,gBAAgB;wBACvB,IAAI,EAAE,OAAO;wBACb,WAAW,EAAE,CAAC,GAAG,CAAC;qBACnB;oBACD,IAAI,EAAE;wBACJ,EAAE,EAAE,cAAc;wBAClB,KAAK,EAAE,eAAe;wBACtB,IAAI,EAAE,MAAM;wBACZ,WAAW,EAAE,CAAC,eAAe,EAAE,kBAAkB,CAAC;qBACnD;oBACD,MAAM,EAAE;wBACN,EAAE,EAAE,gBAAgB;wBACpB,KAAK,EAAE,iBAAiB;wBACxB,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,CAAC,eAAe,CAAC;qBAC/B;iBACF;gBACD,SAAS,EAAE;oBACT,MAAM,EAAE;wBACN,EAAE,EAAE,sBAAsB;wBAC1B,IAAI,EAAE,sBAAsB;wBAC5B,UAAU,EAAE;4BACV,KAAK,EAAE;gCACL,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;gCAClE,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE;6BAC3D;yBACF;qBACF;oBACD,OAAO,EAAE;wBACP,EAAE,EAAE,uBAAuB;wBAC3B,IAAI,EAAE,uBAAuB;wBAC7B,UAAU,EAAE;4BACV,KAAK,EAAE;gCACL,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE;gCACxD,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE;gCACjE,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE;6BAC5D;yBACF;qBACF;iBACF;gBACD,UAAU,EAAE;oBACV,OAAO,EAAE;wBACP,EAAE,EAAE,wBAAwB;wBAC5B,MAAM,EAAE,SAAS;wBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,QAAQ,EAAE,EAAE;qBACb;oBACD,SAAS,EAAE;wBACT,EAAE,EAAE,0BAA0B;wBAC9B,MAAM,EAAE,WAAW;wBACnB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;wBACvC,WAAW,EAAE,IAAI,IAAI,EAAE;wBACvB,QAAQ,EAAE,GAAG;qBACd;oBACD,MAAM,EAAE;wBACN,EAAE,EAAE,uBAAuB;wBAC3B,MAAM,EAAE,QAAQ;wBAChB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;wBACvC,WAAW,EAAE,IAAI,IAAI,EAAE;wBACvB,QAAQ,EAAE,EAAE;wBACZ,YAAY,EAAE,EAAE,YAAY,EAAE,YAAY,EAAE;qBAC7C;iBACF;aACF;YACD,UAAU,EAAE;gBACV,SAAS,EAAE;oBACT,KAAK,EAAE,GAAG;oBACV,kBAAkB,EAAE;wBAClB,SAAS,EAAE,GAAG;wBACd,MAAM,EAAE,GAAG;wBACX,OAAO,EAAE,GAAG;qBACb;oBACD,aAAa,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,wBAAwB;iBACxD;gBACD,OAAO,EAAE;oBACP,KAAK,EAAE,IAAI;oBACX,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW;oBAC3C,UAAU,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,QAAQ,CAAC;iBAChD;aACF;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AAtjBY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;yDAMiC,sBAAa,oBAAb,sBAAa;GAL9C,wBAAwB,CAsjBpC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\config\\test-configuration.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { DataSource, DataSourceOptions } from 'typeorm';\r\nimport { Test, TestingModule } from '@nestjs/testing';\r\n\r\n/**\r\n * Test Configuration Service\r\n * \r\n * Centralized test configuration management providing:\r\n * - Environment-specific test configurations\r\n * - Database setup and teardown for testing\r\n * - Mock service configuration and management\r\n * - Test data generation and cleanup utilities\r\n * - Performance testing configuration\r\n * - Integration testing environment setup\r\n */\r\n@Injectable()\r\nexport class TestConfigurationService {\r\n  private readonly logger = new Logger(TestConfigurationService.name);\r\n  private testDataSource: DataSource;\r\n  private testingModule: TestingModule;\r\n\r\n  constructor(private readonly configService: ConfigService) {}\r\n\r\n  /**\r\n   * Get test database configuration\r\n   */\r\n  getTestDatabaseConfig(): DataSourceOptions {\r\n    const isCI = process.env.CI === 'true';\r\n    const testDbName = `sentinel_test_${Date.now()}_${Math.random().toString(36).substring(7)}`;\r\n\r\n    return {\r\n      type: 'postgres',\r\n      host: this.configService.get('TEST_DB_HOST', 'localhost'),\r\n      port: this.configService.get('TEST_DB_PORT', 5433),\r\n      username: this.configService.get('TEST_DB_USERNAME', 'test_user'),\r\n      password: this.configService.get('TEST_DB_PASSWORD', 'test_password'),\r\n      database: isCI ? testDbName : this.configService.get('TEST_DB_NAME', 'sentinel_test'),\r\n      entities: [__dirname + '/../../**/*.entity{.ts,.js}'],\r\n      synchronize: true,\r\n      dropSchema: true,\r\n      logging: this.configService.get('TEST_DB_LOGGING', false),\r\n      maxQueryExecutionTime: 1000,\r\n      extra: {\r\n        connectionLimit: 10,\r\n        acquireTimeout: 30000,\r\n        timeout: 30000,\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get test Redis configuration\r\n   */\r\n  getTestRedisConfig(): any {\r\n    return {\r\n      host: this.configService.get('TEST_REDIS_HOST', 'localhost'),\r\n      port: this.configService.get('TEST_REDIS_PORT', 6380),\r\n      db: this.configService.get('TEST_REDIS_DB', 1),\r\n      password: this.configService.get('TEST_REDIS_PASSWORD'),\r\n      keyPrefix: 'sentinel_test:',\r\n      retryDelayOnFailover: 100,\r\n      maxRetriesPerRequest: 3,\r\n      lazyConnect: true,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get test queue configuration\r\n   */\r\n  getTestQueueConfig(): any {\r\n    return {\r\n      redis: this.getTestRedisConfig(),\r\n      defaultJobOptions: {\r\n        removeOnComplete: 5,\r\n        removeOnFail: 10,\r\n        attempts: 1,\r\n        backoff: {\r\n          type: 'exponential',\r\n          delay: 1000,\r\n        },\r\n      },\r\n      settings: {\r\n        stalledInterval: 30000,\r\n        maxStalledCount: 1,\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get test monitoring configuration\r\n   */\r\n  getTestMonitoringConfig(): any {\r\n    return {\r\n      metricsEnabled: true,\r\n      metricsPrefix: 'sentinel_test',\r\n      systemMetricsEnabled: false,\r\n      businessMetricsEnabled: true,\r\n      systemMetricsInterval: 60000,\r\n      businessMetricsInterval: 30000,\r\n      httpMetricsEnabled: true,\r\n      httpMetricsUserTracking: false,\r\n      httpMetricsSlowRequestThreshold: 1000,\r\n      databaseMetricsEnabled: true,\r\n      databaseMetricsQueryLogging: false,\r\n      cacheMetricsEnabled: true,\r\n      queueMetricsEnabled: true,\r\n      metricsEndpointEnabled: false,\r\n      metricsEndpointAuth: false,\r\n      metricsCollectionTimeout: 5000,\r\n      metricsMaxMemoryUsage: 50 * 1024 * 1024, // 50MB\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get test workflow execution configuration\r\n   */\r\n  getTestWorkflowConfig(): any {\r\n    return {\r\n      maxConcurrentExecutions: 5,\r\n      defaultExecutionTimeout: 30000,\r\n      maxParallelSteps: 3,\r\n      enableStateCheckpoints: true,\r\n      checkpointInterval: 10000,\r\n      maxCheckpointsPerExecution: 5,\r\n      stateCompressionEnabled: false,\r\n      enablePerformanceMonitoring: true,\r\n      metricsCollectionInterval: 5000,\r\n      bottleneckDetectionEnabled: true,\r\n      resourceMonitoringEnabled: false,\r\n      enableAutoRetry: true,\r\n      defaultMaxRetries: 2,\r\n      defaultRetryDelay: 1000,\r\n      enableCompensation: true,\r\n      enableRollback: true,\r\n      circuitBreakerEnabled: true,\r\n      circuitBreakerThreshold: 3,\r\n      circuitBreakerTimeout: 10000,\r\n      externalServiceTimeout: 5000,\r\n      externalServiceMaxRetries: 2,\r\n      enableExecutionNotifications: false,\r\n      enableExecutionAudit: true,\r\n      enableAutoCleanup: false,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get test external service configurations\r\n   */\r\n  getTestExternalServiceConfig(): any {\r\n    return {\r\n      mockServices: {\r\n        enabled: true,\r\n        baseUrl: 'http://localhost:3001',\r\n        timeout: 5000,\r\n        retries: 2,\r\n      },\r\n      threatIntelligence: {\r\n        enabled: false,\r\n        mockData: true,\r\n        apiKey: 'test-api-key',\r\n        baseUrl: 'http://localhost:3002',\r\n      },\r\n      notifications: {\r\n        email: {\r\n          enabled: false,\r\n          mockSend: true,\r\n          provider: 'mock',\r\n        },\r\n        slack: {\r\n          enabled: false,\r\n          mockSend: true,\r\n          webhookUrl: 'http://localhost:3003/slack',\r\n        },\r\n        sms: {\r\n          enabled: false,\r\n          mockSend: true,\r\n          provider: 'mock',\r\n        },\r\n      },\r\n      aiMl: {\r\n        enabled: false,\r\n        mockResponses: true,\r\n        baseUrl: 'http://localhost:3004',\r\n        timeout: 10000,\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get test security configuration\r\n   */\r\n  getTestSecurityConfig(): any {\r\n    return {\r\n      jwt: {\r\n        secret: 'test-jwt-secret-key-for-testing-only',\r\n        expiresIn: '1h',\r\n        issuer: 'sentinel-test',\r\n        audience: 'sentinel-test-users',\r\n      },\r\n      encryption: {\r\n        algorithm: 'aes-256-gcm',\r\n        key: 'test-encryption-key-32-characters',\r\n        ivLength: 16,\r\n      },\r\n      rateLimit: {\r\n        enabled: false,\r\n        windowMs: 60000,\r\n        max: 1000,\r\n      },\r\n      cors: {\r\n        origin: ['http://localhost:3000', 'http://localhost:3001'],\r\n        credentials: true,\r\n        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get test performance configuration\r\n   */\r\n  getTestPerformanceConfig(): any {\r\n    return {\r\n      loadTesting: {\r\n        maxConcurrentUsers: 50,\r\n        rampUpDuration: 10000, // 10 seconds\r\n        testDuration: 60000, // 1 minute\r\n        thinkTime: 1000, // 1 second between requests\r\n        scenarios: [\r\n          {\r\n            name: 'workflow_execution',\r\n            weight: 40,\r\n            endpoints: ['/workflow-execution/start', '/workflow-execution/:id/status'],\r\n          },\r\n          {\r\n            name: 'monitoring_dashboard',\r\n            weight: 30,\r\n            endpoints: ['/monitoring/metrics', '/monitoring/health'],\r\n          },\r\n          {\r\n            name: 'template_management',\r\n            weight: 20,\r\n            endpoints: ['/templates', '/templates/:id'],\r\n          },\r\n          {\r\n            name: 'reporting',\r\n            weight: 10,\r\n            endpoints: ['/reports', '/reports/:id/generate'],\r\n          },\r\n        ],\r\n      },\r\n      benchmarking: {\r\n        warmupRequests: 100,\r\n        measurementRequests: 1000,\r\n        concurrency: 10,\r\n        timeout: 30000,\r\n        acceptableResponseTime: 500, // milliseconds\r\n        acceptableErrorRate: 0.01, // 1%\r\n        acceptableThroughput: 100, // requests per second\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Initialize test database\r\n   */\r\n  async initializeTestDatabase(): Promise<DataSource> {\r\n    try {\r\n      const config = this.getTestDatabaseConfig();\r\n      this.testDataSource = new DataSource(config);\r\n      \r\n      await this.testDataSource.initialize();\r\n      this.logger.log('Test database initialized successfully');\r\n      \r\n      return this.testDataSource;\r\n    } catch (error) {\r\n      this.logger.error('Failed to initialize test database', error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleanup test database\r\n   */\r\n  async cleanupTestDatabase(): Promise<void> {\r\n    try {\r\n      if (this.testDataSource && this.testDataSource.isInitialized) {\r\n        await this.testDataSource.dropDatabase();\r\n        await this.testDataSource.destroy();\r\n        this.logger.log('Test database cleaned up successfully');\r\n      }\r\n    } catch (error) {\r\n      this.logger.error('Failed to cleanup test database', error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create test module with configuration\r\n   */\r\n  async createTestModule(moduleMetadata: any): Promise<TestingModule> {\r\n    try {\r\n      const testingModuleBuilder = Test.createTestingModule(moduleMetadata);\r\n      \r\n      // Override configurations for testing\r\n      testingModuleBuilder.overrideProvider(ConfigService).useValue({\r\n        get: (key: string, defaultValue?: any) => {\r\n          const testConfigs = {\r\n            ...this.getTestDatabaseConfig(),\r\n            ...this.getTestRedisConfig(),\r\n            ...this.getTestMonitoringConfig(),\r\n            ...this.getTestWorkflowConfig(),\r\n            ...this.getTestExternalServiceConfig(),\r\n            ...this.getTestSecurityConfig(),\r\n            ...this.getTestPerformanceConfig(),\r\n          };\r\n          \r\n          return testConfigs[key] !== undefined ? testConfigs[key] : defaultValue;\r\n        },\r\n      });\r\n\r\n      this.testingModule = await testingModuleBuilder.compile();\r\n      \r\n      // Enable logging for tests if needed\r\n      if (this.configService.get('TEST_ENABLE_LOGGING', false)) {\r\n        this.testingModule.useLogger(new Logger());\r\n      }\r\n\r\n      this.logger.log('Test module created successfully');\r\n      return this.testingModule;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create test module', error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleanup test module\r\n   */\r\n  async cleanupTestModule(): Promise<void> {\r\n    try {\r\n      if (this.testingModule) {\r\n        await this.testingModule.close();\r\n        this.logger.log('Test module cleaned up successfully');\r\n      }\r\n    } catch (error) {\r\n      this.logger.error('Failed to cleanup test module', error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get test environment info\r\n   */\r\n  getTestEnvironmentInfo(): {\r\n    nodeEnv: string;\r\n    isCI: boolean;\r\n    testTimeout: number;\r\n    parallelTests: boolean;\r\n    coverage: boolean;\r\n    verbose: boolean;\r\n    bail: boolean;\r\n  } {\r\n    return {\r\n      nodeEnv: process.env.NODE_ENV || 'test',\r\n      isCI: process.env.CI === 'true',\r\n      testTimeout: parseInt(process.env.TEST_TIMEOUT || '30000', 10),\r\n      parallelTests: process.env.TEST_PARALLEL !== 'false',\r\n      coverage: process.env.TEST_COVERAGE === 'true',\r\n      verbose: process.env.TEST_VERBOSE === 'true',\r\n      bail: process.env.TEST_BAIL === 'true',\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Validate test environment\r\n   */\r\n  async validateTestEnvironment(): Promise<{\r\n    valid: boolean;\r\n    issues: string[];\r\n    warnings: string[];\r\n  }> {\r\n    const issues: string[] = [];\r\n    const warnings: string[] = [];\r\n\r\n    try {\r\n      // Check database connectivity\r\n      const testDb = await this.initializeTestDatabase();\r\n      await testDb.query('SELECT 1');\r\n      await this.cleanupTestDatabase();\r\n    } catch (error) {\r\n      issues.push(`Database connectivity issue: ${error.message}`);\r\n    }\r\n\r\n    // Check required environment variables\r\n    const requiredEnvVars = [\r\n      'TEST_DB_HOST',\r\n      'TEST_DB_PORT',\r\n      'TEST_DB_USERNAME',\r\n      'TEST_DB_PASSWORD',\r\n    ];\r\n\r\n    for (const envVar of requiredEnvVars) {\r\n      if (!process.env[envVar]) {\r\n        warnings.push(`Missing environment variable: ${envVar}`);\r\n      }\r\n    }\r\n\r\n    // Check Node.js version\r\n    const nodeVersion = process.version;\r\n    const majorVersion = parseInt(nodeVersion.substring(1).split('.')[0], 10);\r\n    if (majorVersion < 16) {\r\n      issues.push(`Node.js version ${nodeVersion} is not supported. Minimum version: 16.x`);\r\n    }\r\n\r\n    // Check available memory\r\n    const totalMemory = process.memoryUsage().heapTotal;\r\n    const minMemory = 512 * 1024 * 1024; // 512MB\r\n    if (totalMemory < minMemory) {\r\n      warnings.push(`Low memory available: ${Math.round(totalMemory / 1024 / 1024)}MB. Recommended: 512MB+`);\r\n    }\r\n\r\n    return {\r\n      valid: issues.length === 0,\r\n      issues,\r\n      warnings,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate test report configuration\r\n   */\r\n  getTestReportConfig(): any {\r\n    return {\r\n      reporters: [\r\n        'default',\r\n        ['jest-junit', {\r\n          outputDirectory: './test-results',\r\n          outputName: 'junit.xml',\r\n          classNameTemplate: '{classname}',\r\n          titleTemplate: '{title}',\r\n          ancestorSeparator: ' › ',\r\n          usePathForSuiteName: true,\r\n        }],\r\n        ['jest-html-reporters', {\r\n          publicPath: './test-results',\r\n          filename: 'test-report.html',\r\n          expand: true,\r\n          hideIcon: false,\r\n          pageTitle: 'Sentinel Reporting Module Test Report',\r\n        }],\r\n      ],\r\n      coverageReporters: [\r\n        'text',\r\n        'lcov',\r\n        'html',\r\n        'json-summary',\r\n      ],\r\n      coverageDirectory: './coverage',\r\n      collectCoverageFrom: [\r\n        'src/**/*.{ts,js}',\r\n        '!src/**/*.spec.{ts,js}',\r\n        '!src/**/*.test.{ts,js}',\r\n        '!src/**/*.d.ts',\r\n        '!src/**/index.ts',\r\n        '!src/**/*.interface.ts',\r\n        '!src/**/*.enum.ts',\r\n        '!src/**/*.constant.ts',\r\n      ],\r\n      coverageThreshold: {\r\n        global: {\r\n          branches: 80,\r\n          functions: 80,\r\n          lines: 80,\r\n          statements: 80,\r\n        },\r\n        './src/modules/reporting/workflow-execution/': {\r\n          branches: 85,\r\n          functions: 85,\r\n          lines: 85,\r\n          statements: 85,\r\n        },\r\n        './src/modules/reporting/monitoring/': {\r\n          branches: 85,\r\n          functions: 85,\r\n          lines: 85,\r\n          statements: 85,\r\n        },\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get test data configuration\r\n   */\r\n  getTestDataConfig(): any {\r\n    return {\r\n      fixtures: {\r\n        users: {\r\n          admin: {\r\n            id: 'test-admin-id',\r\n            email: '<EMAIL>',\r\n            role: 'admin',\r\n            permissions: ['*'],\r\n          },\r\n          user: {\r\n            id: 'test-user-id',\r\n            email: '<EMAIL>',\r\n            role: 'user',\r\n            permissions: ['workflow.view', 'workflow.execute'],\r\n          },\r\n          viewer: {\r\n            id: 'test-viewer-id',\r\n            email: '<EMAIL>',\r\n            role: 'viewer',\r\n            permissions: ['workflow.view'],\r\n          },\r\n        },\r\n        templates: {\r\n          simple: {\r\n            id: 'test-template-simple',\r\n            name: 'Simple Test Template',\r\n            definition: {\r\n              steps: [\r\n                { id: 'step1', type: 'notification', config: { message: 'Test' } },\r\n                { id: 'step2', type: 'validation', config: { rules: [] } },\r\n              ],\r\n            },\r\n          },\r\n          complex: {\r\n            id: 'test-template-complex',\r\n            name: 'Complex Test Template',\r\n            definition: {\r\n              steps: [\r\n                { id: 'step1', type: 'parallel', config: { steps: [] } },\r\n                { id: 'step2', type: 'conditional', config: { condition: true } },\r\n                { id: 'step3', type: 'loop', config: { type: 'for_each' } },\r\n              ],\r\n            },\r\n          },\r\n        },\r\n        executions: {\r\n          running: {\r\n            id: 'test-execution-running',\r\n            status: 'running',\r\n            startedAt: new Date(),\r\n            progress: 50,\r\n          },\r\n          completed: {\r\n            id: 'test-execution-completed',\r\n            status: 'completed',\r\n            startedAt: new Date(Date.now() - 60000),\r\n            completedAt: new Date(),\r\n            progress: 100,\r\n          },\r\n          failed: {\r\n            id: 'test-execution-failed',\r\n            status: 'failed',\r\n            startedAt: new Date(Date.now() - 30000),\r\n            completedAt: new Date(),\r\n            progress: 75,\r\n            errorDetails: { errorMessage: 'Test error' },\r\n          },\r\n        },\r\n      },\r\n      generators: {\r\n        execution: {\r\n          count: 100,\r\n          statusDistribution: {\r\n            completed: 0.7,\r\n            failed: 0.2,\r\n            running: 0.1,\r\n          },\r\n          durationRange: [1000, 300000], // 1 second to 5 minutes\r\n        },\r\n        metrics: {\r\n          count: 1000,\r\n          timeRange: 24 * 60 * 60 * 1000, // 24 hours\r\n          categories: ['business', 'technical', 'system'],\r\n        },\r\n      },\r\n    };\r\n  }\r\n}\r\n"], "version": 3}