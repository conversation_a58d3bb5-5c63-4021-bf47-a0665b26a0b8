c9fcec141a2625c3d16920832d9d929a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vulnerability_data_interface_1 = require("../vulnerability-data.interface");
const vulnerability_severity_enum_1 = require("../../../enums/vulnerability-severity.enum");
const confidence_level_enum_1 = require("../../../enums/confidence-level.enum");
describe('VulnerabilityData Interface', () => {
    let mockVulnerabilityData;
    let mockAlternativeIds;
    let mockCVSSScores;
    let mockAffectedProducts;
    let mockReferences;
    let mockExploitation;
    let mockRemediation;
    let mockAssessment;
    let mockComplianceImpact;
    let mockBusinessImpact;
    let mockTechnicalDetails;
    let mockDetectionInfo;
    let mockThreatContext;
    let mockAssetContext;
    let mockSharingInfo;
    let mockQualityMetrics;
    beforeEach(() => {
        mockAlternativeIds = [
            {
                type: 'vendor',
                value: 'VENDOR-2024-001',
                source: 'Vendor Security Team',
            },
            {
                type: 'internal',
                value: 'INT-VULN-123',
                source: 'Internal Security',
            },
        ];
        mockCVSSScores = [
            {
                version: '3.1',
                baseScore: 8.5,
                temporalScore: 7.8,
                environmentalScore: 8.2,
                baseVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',
                temporalVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H/E:F/RL:O/RC:C',
                environmentalVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H/CR:H/IR:H/AR:H',
                baseSeverity: 'HIGH',
                temporalSeverity: 'HIGH',
                environmentalSeverity: 'HIGH',
                exploitabilityScore: 3.9,
                impactScore: 5.9,
                source: 'NVD',
                assessedAt: '2024-01-01T00:00:00Z',
            },
        ];
        mockAffectedProducts = [
            {
                vendor: 'Apache',
                product: 'HTTP Server',
                version: '2.4.41',
                versionRange: {
                    startVersion: '2.4.0',
                    endVersion: '2.4.42',
                    startInclusive: true,
                    endInclusive: false,
                    criteria: '>=2.4.0 <2.4.42',
                },
                platform: 'Linux',
                configuration: 'Default configuration',
                cpe: 'cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:*',
                packageInfo: {
                    type: 'rpm',
                    name: 'httpd',
                    version: '2.4.41-1',
                    ecosystem: 'RHEL',
                    purl: 'pkg:rpm/rhel/httpd@2.4.41-1',
                },
            },
        ];
        mockReferences = [
            {
                url: 'https://nvd.nist.gov/vuln/detail/CVE-2024-0001',
                name: 'NVD Entry',
                source: 'NVD',
                type: vulnerability_data_interface_1.VulnerabilityReferenceType.ADVISORY,
                tags: ['official', 'government'],
            },
            {
                url: 'https://apache.org/security/CVE-2024-0001.html',
                name: 'Apache Security Advisory',
                source: 'Apache',
                type: vulnerability_data_interface_1.VulnerabilityReferenceType.VENDOR,
                tags: ['vendor', 'official'],
            },
        ];
        mockExploitation = {
            status: vulnerability_data_interface_1.VulnerabilityExploitationStatus.PROOF_OF_CONCEPT,
            complexity: 'medium',
            knownExploits: [
                {
                    name: 'Apache HTTP Server RCE',
                    type: 'remote_code_execution',
                    source: 'ExploitDB',
                    url: 'https://exploit-db.com/exploits/12345',
                    date: '2024-01-15T00:00:00Z',
                    maturity: 'proof_of_concept',
                },
            ],
            timeline: {
                firstExploitSeen: '2024-01-15T00:00:00Z',
                lastExploitSeen: '2024-01-20T00:00:00Z',
                trend: 'increasing',
                frequency: 'occasional',
            },
            context: {
                attackVector: 'network',
                attackComplexity: 'low',
                privilegesRequired: 'none',
                userInteraction: 'none',
                scope: 'unchanged',
            },
            pocAvailable: true,
            weaponized: false,
            inTheWild: false,
        };
        mockRemediation = {
            status: vulnerability_data_interface_1.VulnerabilityRemediationStatus.AVAILABLE,
            fixes: [
                {
                    type: 'patch',
                    description: 'Security patch for Apache HTTP Server',
                    version: '2.4.42',
                    url: 'https://apache.org/dist/httpd/patches/apply_to_2.4.41/',
                    date: '2024-01-10T00:00:00Z',
                    effectiveness: 'complete',
                },
            ],
            workarounds: [
                {
                    description: 'Disable vulnerable module',
                    steps: ['Edit httpd.conf', 'Comment out LoadModule', 'Restart service'],
                    effectiveness: 'high',
                    functionalImpact: 'medium',
                    complexity: 'low',
                },
            ],
            mitigations: [
                {
                    type: 'preventive',
                    description: 'Configure web application firewall',
                    guidance: 'Block requests matching exploit patterns',
                    effectiveness: 'medium',
                    controls: ['WAF', 'IPS'],
                },
            ],
            timeline: {
                vendorAcknowledged: '2024-01-05T00:00:00Z',
                fixDevelopmentStarted: '2024-01-06T00:00:00Z',
                fixAvailable: '2024-01-10T00:00:00Z',
                expectedFixDate: '2024-01-10T00:00:00Z',
            },
            effort: {
                timeToFix: 2,
                complexity: 'low',
                resources: ['System Administrator'],
                dependencies: ['Maintenance window'],
                remediationRisk: 'low',
            },
        };
        mockAssessment = {
            riskScore: 85,
            impact: {
                confidentiality: 'complete',
                integrity: 'complete',
                availability: 'partial',
                business: 'high',
                financial: 'medium',
                reputational: 'high',
            },
            likelihood: {
                exploitation: 'medium',
                vectorAccessibility: 'high',
                attackerMotivation: 'high',
                targetAttractiveness: 'medium',
            },
            priority: vulnerability_data_interface_1.VulnerabilityPriority.HIGH,
            assessedAt: '2024-01-01T00:00:00Z',
            assessedBy: 'Security Team',
            methodology: 'CVSS v3.1 + Business Context',
            notes: 'High priority due to public-facing exposure',
        };
        mockComplianceImpact = {
            frameworks: ['PCI-DSS', 'SOX', 'GDPR'],
            requirements: ['Data Protection', 'System Security'],
            violationSeverity: 'high',
            notificationsRequired: true,
            deadline: '2024-01-30T00:00:00Z',
            penalties: ['Fines', 'Audit Requirements'],
        };
        mockBusinessImpact = {
            affectedProcesses: ['Web Services', 'Customer Portal'],
            serviceDisruption: 'moderate',
            revenueImpact: 'medium',
            customerImpact: 'high',
            operationalImpact: 'medium',
            strategicImpact: 'low',
        };
        mockTechnicalDetails = {
            rootCause: 'Buffer overflow in request parsing',
            attackVectors: ['HTTP Request Manipulation'],
            prerequisites: ['Network access to web server'],
            technicalImpact: ['Remote Code Execution', 'Data Disclosure'],
            affectedComponents: ['mod_rewrite', 'request_parser'],
            codeExamples: [
                {
                    language: 'c',
                    code: 'char buffer[256]; strcpy(buffer, user_input);',
                    type: 'vulnerable',
                    description: 'Vulnerable code pattern',
                },
            ],
        };
        mockDetectionInfo = {
            methods: [vulnerability_data_interface_1.VulnerabilityDetectionMethod.STATIC_ANALYSIS, vulnerability_data_interface_1.VulnerabilityDetectionMethod.AUTOMATED_SCANNING],
            scannerInfo: {
                name: 'Nessus',
                version: '10.4.0',
                vendor: 'Tenable',
                scanDate: '2024-01-01T00:00:00Z',
                configuration: { scan_type: 'credentialed' },
            },
            rules: [
                {
                    id: 'NESSUS-12345',
                    name: 'Apache HTTP Server Buffer Overflow',
                    type: 'signature',
                    content: 'HTTP/1.1 request parsing vulnerability',
                    confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                },
            ],
            falsePositiveRate: 'low',
            confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
        };
        mockThreatContext = {
            threats: ['threat-123', 'threat-456'],
            threatActors: ['APT29', 'Lazarus Group'],
            campaigns: ['Operation WebStrike'],
            intelligence: [
                {
                    source: 'Threat Intel Feed',
                    type: 'exploitation',
                    content: 'Active exploitation observed in the wild',
                    confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                    date: '2024-01-15T00:00:00Z',
                },
            ],
        };
        mockAssetContext = [
            {
                assetId: 'asset-123',
                assetName: 'Web Server 01',
                assetType: 'server',
                criticality: 'high',
                exposure: 'external',
                environment: 'production',
                instanceDetails: {
                    instanceId: 'instance-456',
                    discoveredAt: '2024-01-01T00:00:00Z',
                    lastVerified: '2024-01-02T00:00:00Z',
                    status: 'open',
                    notes: 'Requires immediate patching',
                },
            },
        ];
        mockSharingInfo = {
            tlp: 'amber',
            restrictions: ['Internal use only'],
            permittedActions: ['analyze', 'detect', 'block'],
            agreementRef: 'vuln-sharing-001',
            copyright: '© 2024 Security Corp',
            license: 'CC BY-SA 4.0',
        };
        mockQualityMetrics = {
            completeness: 90,
            accuracy: 95,
            timeliness: 85,
            relevance: 88,
            overallScore: 89,
            assessedAt: '2024-01-01T00:00:00Z',
            issues: ['Missing exploitation timeline'],
        };
        mockVulnerabilityData = {
            version: '1.0.0',
            externalId: 'vuln-123',
            cveId: 'CVE-2024-0001',
            alternativeIds: mockAlternativeIds,
            sourceId: 'nvd-source',
            sourceOrganization: 'NIST NVD',
            sourceReliability: vulnerability_data_interface_1.VulnerabilitySourceReliability.A,
            title: 'Apache HTTP Server Buffer Overflow',
            description: 'A buffer overflow vulnerability in Apache HTTP Server allows remote code execution',
            severity: vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH,
            category: vulnerability_data_interface_1.VulnerabilityCategory.BUFFER_OVERFLOW,
            type: vulnerability_data_interface_1.VulnerabilityType.IMPLEMENTATION_BUG,
            confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
            status: vulnerability_data_interface_1.VulnerabilityStatus.PUBLISHED,
            publishedAt: '2024-01-01T00:00:00Z',
            modifiedAt: '2024-01-02T00:00:00Z',
            discoveredAt: '2023-12-15T00:00:00Z',
            disclosedAt: '2024-01-01T00:00:00Z',
            cvssScores: mockCVSSScores,
            affectedProducts: mockAffectedProducts,
            references: mockReferences,
            exploitation: mockExploitation,
            remediation: mockRemediation,
            assessment: mockAssessment,
            complianceImpact: mockComplianceImpact,
            businessImpact: mockBusinessImpact,
            technicalDetails: mockTechnicalDetails,
            detectionInfo: mockDetectionInfo,
            threatContext: mockThreatContext,
            assetContext: mockAssetContext,
            sharingInfo: mockSharingInfo,
            attributes: { custom: 'attribute' },
            tags: ['apache', 'buffer-overflow', 'rce'],
            qualityMetrics: mockQualityMetrics,
        };
    });
    describe('VulnerabilityData Structure', () => {
        it('should have all required fields', () => {
            expect(mockVulnerabilityData.version).toBeDefined();
            expect(mockVulnerabilityData.externalId).toBeDefined();
            expect(mockVulnerabilityData.sourceId).toBeDefined();
            expect(mockVulnerabilityData.sourceOrganization).toBeDefined();
            expect(mockVulnerabilityData.sourceReliability).toBeDefined();
            expect(mockVulnerabilityData.title).toBeDefined();
            expect(mockVulnerabilityData.description).toBeDefined();
            expect(mockVulnerabilityData.severity).toBeDefined();
            expect(mockVulnerabilityData.category).toBeDefined();
            expect(mockVulnerabilityData.type).toBeDefined();
            expect(mockVulnerabilityData.confidence).toBeDefined();
            expect(mockVulnerabilityData.status).toBeDefined();
            expect(mockVulnerabilityData.publishedAt).toBeDefined();
            expect(mockVulnerabilityData.modifiedAt).toBeDefined();
            expect(mockVulnerabilityData.cvssScores).toBeDefined();
            expect(mockVulnerabilityData.affectedProducts).toBeDefined();
            expect(mockVulnerabilityData.references).toBeDefined();
            expect(mockVulnerabilityData.assessment).toBeDefined();
            expect(mockVulnerabilityData.sharingInfo).toBeDefined();
        });
        it('should support versioning', () => {
            expect(mockVulnerabilityData.version).toBe('1.0.0');
            expect(typeof mockVulnerabilityData.version).toBe('string');
        });
        it('should be serializable to JSON', () => {
            const jsonString = JSON.stringify(mockVulnerabilityData);
            expect(jsonString).toBeDefined();
            expect(jsonString.length).toBeGreaterThan(0);
            const parsed = JSON.parse(jsonString);
            expect(parsed.version).toBe(mockVulnerabilityData.version);
            expect(parsed.externalId).toBe(mockVulnerabilityData.externalId);
        });
        it('should maintain data integrity after serialization', () => {
            const jsonString = JSON.stringify(mockVulnerabilityData);
            const parsed = JSON.parse(jsonString);
            expect(parsed.version).toBe(mockVulnerabilityData.version);
            expect(parsed.externalId).toBe(mockVulnerabilityData.externalId);
            expect(parsed.cveId).toBe(mockVulnerabilityData.cveId);
            expect(parsed.title).toBe(mockVulnerabilityData.title);
            expect(parsed.severity).toBe(mockVulnerabilityData.severity);
            expect(parsed.cvssScores.length).toBe(mockVulnerabilityData.cvssScores.length);
        });
    });
    describe('VulnerabilityAlternativeId', () => {
        it('should contain alternative identifiers', () => {
            const altId = mockAlternativeIds[0];
            expect(altId.type).toBe('vendor');
            expect(altId.value).toBe('VENDOR-2024-001');
            expect(altId.source).toBe('Vendor Security Team');
        });
    });
    describe('VulnerabilitySourceReliability Enum', () => {
        it('should contain all reliability levels', () => {
            const reliabilityLevels = Object.values(vulnerability_data_interface_1.VulnerabilitySourceReliability);
            expect(reliabilityLevels).toContain('A');
            expect(reliabilityLevels).toContain('B');
            expect(reliabilityLevels).toContain('C');
            expect(reliabilityLevels).toContain('D');
            expect(reliabilityLevels).toContain('E');
            expect(reliabilityLevels).toContain('F');
        });
    });
    describe('VulnerabilityCategory Enum', () => {
        it('should contain comprehensive vulnerability categories', () => {
            const categories = Object.values(vulnerability_data_interface_1.VulnerabilityCategory);
            expect(categories).toContain(vulnerability_data_interface_1.VulnerabilityCategory.BUFFER_OVERFLOW);
            expect(categories).toContain(vulnerability_data_interface_1.VulnerabilityCategory.SQL_INJECTION);
            expect(categories).toContain(vulnerability_data_interface_1.VulnerabilityCategory.CROSS_SITE_SCRIPTING);
            expect(categories).toContain(vulnerability_data_interface_1.VulnerabilityCategory.REMOTE_CODE_EXECUTION);
        });
    });
    describe('VulnerabilityCVSSData', () => {
        it('should contain CVSS scoring information', () => {
            const cvss = mockCVSSScores[0];
            expect(cvss.version).toBe('3.1');
            expect(cvss.baseScore).toBe(8.5);
            expect(cvss.baseVector).toContain('CVSS:3.1');
            expect(cvss.baseSeverity).toBe('HIGH');
            expect(cvss.source).toBe('NVD');
        });
        it('should support temporal and environmental scores', () => {
            const cvss = mockCVSSScores[0];
            expect(cvss.temporalScore).toBe(7.8);
            expect(cvss.environmentalScore).toBe(8.2);
            expect(cvss.temporalVector).toBeDefined();
            expect(cvss.environmentalVector).toBeDefined();
        });
    });
    describe('VulnerabilityAffectedProduct', () => {
        it('should contain product information', () => {
            const product = mockAffectedProducts[0];
            expect(product.vendor).toBe('Apache');
            expect(product.product).toBe('HTTP Server');
            expect(product.version).toBe('2.4.41');
            expect(product.cpe).toContain('apache:http_server');
        });
        it('should support version ranges', () => {
            const product = mockAffectedProducts[0];
            expect(product.versionRange).toBeDefined();
            expect(product.versionRange.startVersion).toBe('2.4.0');
            expect(product.versionRange.endVersion).toBe('2.4.42');
            expect(product.versionRange.startInclusive).toBe(true);
        });
        it('should support package information', () => {
            const product = mockAffectedProducts[0];
            expect(product.packageInfo).toBeDefined();
            expect(product.packageInfo.type).toBe('rpm');
            expect(product.packageInfo.purl).toContain('pkg:rpm');
        });
    });
    describe('VulnerabilityReference', () => {
        it('should contain reference information', () => {
            const reference = mockReferences[0];
            expect(reference.url).toContain('nvd.nist.gov');
            expect(reference.type).toBe(vulnerability_data_interface_1.VulnerabilityReferenceType.ADVISORY);
            expect(Array.isArray(reference.tags)).toBe(true);
        });
    });
    describe('VulnerabilityExploitationData', () => {
        it('should contain exploitation information', () => {
            expect(mockExploitation.status).toBe(vulnerability_data_interface_1.VulnerabilityExploitationStatus.PROOF_OF_CONCEPT);
            expect(mockExploitation.complexity).toBe('medium');
            expect(mockExploitation.pocAvailable).toBe(true);
            expect(mockExploitation.inTheWild).toBe(false);
        });
        it('should support known exploits', () => {
            expect(Array.isArray(mockExploitation.knownExploits)).toBe(true);
            expect(mockExploitation.knownExploits[0].name).toBe('Apache HTTP Server RCE');
        });
        it('should support exploitation timeline', () => {
            expect(mockExploitation.timeline).toBeDefined();
            expect(mockExploitation.timeline.trend).toBe('increasing');
        });
    });
    describe('VulnerabilityRemediationData', () => {
        it('should contain remediation information', () => {
            expect(mockRemediation.status).toBe(vulnerability_data_interface_1.VulnerabilityRemediationStatus.AVAILABLE);
            expect(Array.isArray(mockRemediation.fixes)).toBe(true);
            expect(Array.isArray(mockRemediation.workarounds)).toBe(true);
        });
        it('should support fix information', () => {
            const fix = mockRemediation.fixes[0];
            expect(fix.type).toBe('patch');
            expect(fix.version).toBe('2.4.42');
            expect(fix.effectiveness).toBe('complete');
        });
        it('should support remediation timeline', () => {
            expect(mockRemediation.timeline).toBeDefined();
            expect(mockRemediation.timeline.vendorAcknowledged).toBeDefined();
            expect(mockRemediation.timeline.fixAvailable).toBeDefined();
        });
    });
    describe('VulnerabilityAssessmentData', () => {
        it('should contain risk assessment', () => {
            expect(mockAssessment.riskScore).toBe(85);
            expect(mockAssessment.priority).toBe(vulnerability_data_interface_1.VulnerabilityPriority.HIGH);
            expect(mockAssessment.assessedBy).toBe('Security Team');
        });
        it('should support impact assessment', () => {
            expect(mockAssessment.impact.confidentiality).toBe('complete');
            expect(mockAssessment.impact.business).toBe('high');
        });
        it('should support likelihood assessment', () => {
            expect(mockAssessment.likelihood.exploitation).toBe('medium');
            expect(mockAssessment.likelihood.vectorAccessibility).toBe('high');
        });
    });
    describe('VulnerabilityComplianceImpact', () => {
        it('should contain compliance information', () => {
            expect(Array.isArray(mockComplianceImpact.frameworks)).toBe(true);
            expect(mockComplianceImpact.frameworks).toContain('PCI-DSS');
            expect(mockComplianceImpact.violationSeverity).toBe('high');
            expect(mockComplianceImpact.notificationsRequired).toBe(true);
        });
    });
    describe('VulnerabilityBusinessImpact', () => {
        it('should contain business impact assessment', () => {
            expect(Array.isArray(mockBusinessImpact.affectedProcesses)).toBe(true);
            expect(mockBusinessImpact.serviceDisruption).toBe('moderate');
            expect(mockBusinessImpact.customerImpact).toBe('high');
        });
    });
    describe('VulnerabilityTechnicalDetails', () => {
        it('should contain technical information', () => {
            expect(mockTechnicalDetails.rootCause).toBe('Buffer overflow in request parsing');
            expect(Array.isArray(mockTechnicalDetails.attackVectors)).toBe(true);
            expect(Array.isArray(mockTechnicalDetails.codeExamples)).toBe(true);
        });
        it('should support code examples', () => {
            const codeExample = mockTechnicalDetails.codeExamples[0];
            expect(codeExample.language).toBe('c');
            expect(codeExample.type).toBe('vulnerable');
        });
    });
    describe('VulnerabilityDetectionInfo', () => {
        it('should contain detection information', () => {
            expect(Array.isArray(mockDetectionInfo.methods)).toBe(true);
            expect(mockDetectionInfo.methods).toContain(vulnerability_data_interface_1.VulnerabilityDetectionMethod.STATIC_ANALYSIS);
            expect(mockDetectionInfo.confidence).toBe(confidence_level_enum_1.ConfidenceLevel.HIGH);
        });
        it('should support scanner information', () => {
            expect(mockDetectionInfo.scannerInfo).toBeDefined();
            expect(mockDetectionInfo.scannerInfo.name).toBe('Nessus');
            expect(mockDetectionInfo.scannerInfo.vendor).toBe('Tenable');
        });
    });
    describe('VulnerabilityThreatContext', () => {
        it('should contain threat intelligence', () => {
            expect(Array.isArray(mockThreatContext.threats)).toBe(true);
            expect(Array.isArray(mockThreatContext.threatActors)).toBe(true);
            expect(mockThreatContext.threatActors).toContain('APT29');
        });
        it('should support threat intelligence', () => {
            expect(Array.isArray(mockThreatContext.intelligence)).toBe(true);
            const intel = mockThreatContext.intelligence[0];
            expect(intel.confidence).toBe(confidence_level_enum_1.ConfidenceLevel.MEDIUM);
        });
    });
    describe('VulnerabilityAssetContext', () => {
        it('should contain asset information', () => {
            const asset = mockAssetContext[0];
            expect(asset.assetId).toBe('asset-123');
            expect(asset.criticality).toBe('high');
            expect(asset.exposure).toBe('external');
            expect(asset.environment).toBe('production');
        });
        it('should support instance details', () => {
            const asset = mockAssetContext[0];
            expect(asset.instanceDetails).toBeDefined();
            expect(asset.instanceDetails.status).toBe('open');
        });
    });
    describe('VulnerabilitySharingInfo', () => {
        it('should contain sharing restrictions', () => {
            expect(mockSharingInfo.tlp).toBe('amber');
            expect(Array.isArray(mockSharingInfo.permittedActions)).toBe(true);
            expect(mockSharingInfo.permittedActions).toContain('analyze');
        });
    });
    describe('VulnerabilityDataQualityMetrics', () => {
        it('should contain quality assessment', () => {
            expect(mockQualityMetrics.completeness).toBe(90);
            expect(mockQualityMetrics.accuracy).toBe(95);
            expect(mockQualityMetrics.overallScore).toBe(89);
            expect(mockQualityMetrics.assessedAt).toBeDefined();
        });
        it('should support quality issues tracking', () => {
            expect(Array.isArray(mockQualityMetrics.issues)).toBe(true);
            expect(mockQualityMetrics.issues).toContain('Missing exploitation timeline');
        });
    });
    describe('Data Validation Interface', () => {
        it('should define validation methods', () => {
            const mockValidator = {
                validate: jest.fn().mockReturnValue({
                    isValid: true,
                    errors: [],
                    warnings: [],
                    sanitizedData: mockVulnerabilityData,
                    qualityAssessment: mockQualityMetrics,
                }),
                validateVersion: jest.fn().mockReturnValue(true),
                sanitize: jest.fn().mockReturnValue(mockVulnerabilityData),
                validateCVSS: jest.fn().mockReturnValue([]),
            };
            expect(mockValidator.validate).toBeDefined();
            expect(mockValidator.validateVersion).toBeDefined();
            expect(mockValidator.sanitize).toBeDefined();
            expect(mockValidator.validateCVSS).toBeDefined();
        });
    });
    describe('Data Serialization Interface', () => {
        it('should define serialization methods', () => {
            const mockSerializer = {
                toJson: jest.fn().mockReturnValue('{}'),
                fromJson: jest.fn().mockReturnValue(mockVulnerabilityData),
                toCveJson: jest.fn().mockReturnValue('{}'),
                fromCveJson: jest.fn().mockReturnValue(mockVulnerabilityData),
                toBinary: jest.fn().mockReturnValue(Buffer.from('test')),
                fromBinary: jest.fn().mockReturnValue(mockVulnerabilityData),
                getSupportedVersions: jest.fn().mockReturnValue(['1.0.0']),
            };
            expect(mockSerializer.toJson).toBeDefined();
            expect(mockSerializer.fromJson).toBeDefined();
            expect(mockSerializer.toCveJson).toBeDefined();
            expect(mockSerializer.fromCveJson).toBeDefined();
            expect(mockSerializer.toBinary).toBeDefined();
            expect(mockSerializer.fromBinary).toBeDefined();
            expect(mockSerializer.getSupportedVersions).toBeDefined();
        });
    });
    describe('Data Transformation Interface', () => {
        it('should define transformation methods', () => {
            const mockTransformer = {
                transform: jest.fn().mockReturnValue(mockVulnerabilityData),
                transformToExternal: jest.fn().mockReturnValue({}),
                getSupportedSourceFormats: jest.fn().mockReturnValue(['nvd', 'cve']),
                getSupportedTargetFormats: jest.fn().mockReturnValue(['json', 'xml']),
                enrich: jest.fn().mockResolvedValue(mockVulnerabilityData),
                normalize: jest.fn().mockReturnValue([mockVulnerabilityData]),
            };
            expect(mockTransformer.transform).toBeDefined();
            expect(mockTransformer.transformToExternal).toBeDefined();
            expect(mockTransformer.getSupportedSourceFormats).toBeDefined();
            expect(mockTransformer.getSupportedTargetFormats).toBeDefined();
            expect(mockTransformer.enrich).toBeDefined();
            expect(mockTransformer.normalize).toBeDefined();
        });
    });
    describe('Version Compatibility', () => {
        it('should support version checking', () => {
            const versions = ['1.0.0', '1.1.0', '2.0.0'];
            versions.forEach(version => {
                const versionedData = { ...mockVulnerabilityData, version };
                expect(versionedData.version).toBe(version);
            });
        });
        it('should maintain backward compatibility', () => {
            const legacyData = {
                ...mockVulnerabilityData,
                version: '0.9.0',
            };
            expect(legacyData.version).toBeDefined();
            expect(legacyData.externalId).toBeDefined();
            expect(legacyData.title).toBeDefined();
        });
    });
    describe('External Integration Support', () => {
        it('should support CVE format compatibility', () => {
            const cveCompatible = {
                ...mockVulnerabilityData,
                attributes: {
                    ...mockVulnerabilityData.attributes,
                    cve_format: '5.0',
                    cve_data_type: 'CVE',
                    cve_data_format: 'MITRE',
                },
            };
            expect(cveCompatible.attributes).toHaveProperty('cve_format');
            expect(cveCompatible.attributes).toHaveProperty('cve_data_type');
        });
        it('should support multiple vulnerability databases', () => {
            const multiSourceData = {
                ...mockVulnerabilityData,
                alternativeIds: [
                    ...mockVulnerabilityData.alternativeIds,
                    { type: 'osvdb', value: 'OSVDB-12345', source: 'OSVDB' },
                    { type: 'bugtraq', value: 'BID-67890', source: 'SecurityFocus' },
                ],
            };
            expect(multiSourceData.alternativeIds.length).toBeGreaterThan(2);
            expect(multiSourceData.alternativeIds.some(id => id.type === 'osvdb')).toBe(true);
        });
        it('should support enrichment from multiple sources', () => {
            const enrichedData = {
                ...mockVulnerabilityData,
                attributes: {
                    ...mockVulnerabilityData.attributes,
                    enrichment_sources: ['nvd', 'mitre', 'vendor'],
                    enrichment_timestamp: '2024-01-01T00:00:00Z',
                    enrichment_version: '1.0',
                },
            };
            expect(enrichedData.attributes).toHaveProperty('enrichment_sources');
            expect(Array.isArray(enrichedData.attributes.enrichment_sources)).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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