4b67b3a7e105ad5024e6b77ff82f4761
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const threat_detected_domain_event_1 = require("../threat-detected.domain-event");
const shared_kernel_1 = require("../../../../../shared-kernel");
const threat_severity_enum_1 = require("../../enums/threat-severity.enum");
const event_timestamp_value_object_1 = require("../../value-objects/event-metadata/event-timestamp.value-object");
const threat_signature_value_object_1 = require("../../value-objects/threat-indicators/threat-signature.value-object");
const cvss_score_value_object_1 = require("../../value-objects/threat-indicators/cvss-score.value-object");
const ip_address_value_object_1 = require("../../value-objects/network/ip-address.value-object");
describe('ThreatDetectedDomainEvent', () => {
    let aggregateId;
    let eventData;
    beforeEach(() => {
        aggregateId = shared_kernel_1.UniqueEntityId.generate();
        eventData = {
            threatId: 'threat-001',
            name: 'Advanced Persistent Threat Campaign',
            description: 'Sophisticated multi-stage attack targeting financial data',
            severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
            category: 'Advanced Persistent Threat',
            type: 'Data Exfiltration',
            signature: threat_signature_value_object_1.ThreatSignature.create({
                id: 'sig-001',
                pattern: 'APT-FINANCIAL-2024',
                algorithm: 'yara',
                confidence: 95
            }),
            cvssScore: cvss_score_value_object_1.CvssScore.create(8.5),
            confidence: 92,
            riskScore: 88,
            detectedAt: event_timestamp_value_object_1.EventTimestamp.now(),
            detectionMethod: 'behavioral_analysis',
            detectionSource: 'advanced-threat-detection-engine',
            sourceIp: ip_address_value_object_1.IpAddress.create('*************'),
            targetIp: ip_address_value_object_1.IpAddress.create('*********'),
            affectedAssets: [
                {
                    id: 'asset-001',
                    name: 'Financial Database Server',
                    type: 'database',
                    criticality: 'critical'
                },
                {
                    id: 'asset-002',
                    name: 'Web Application Server',
                    type: 'web_server',
                    criticality: 'high'
                }
            ],
            indicators: [
                {
                    type: 'ip',
                    value: '*************',
                    confidence: 90,
                    source: 'threat-intelligence'
                },
                {
                    type: 'file_hash',
                    value: 'a1b2c3d4e5f6789012345678901234567890abcd',
                    confidence: 95,
                    source: 'malware-analysis'
                }
            ],
            attackVectors: ['phishing', 'lateral_movement', 'privilege_escalation'],
            threatActor: {
                name: 'APT-29',
                group: 'Cozy Bear',
                motivation: 'espionage',
                sophistication: 'advanced'
            },
            killChainStage: 'actions_objectives',
            potentialImpact: {
                confidentiality: 'high',
                integrity: 'high',
                availability: 'low',
                scope: 'changed',
                businessImpact: 'critical'
            },
            relatedEvents: ['event-001', 'event-002'],
            threatIntelligence: {
                campaigns: ['Operation Ghost'],
                ttps: ['T1566.001', 'T1078.004'],
                mitreAttackIds: ['T1566', 'T1078'],
                geolocation: {
                    country: 'Russia',
                    region: 'Eastern Europe',
                    coordinates: { lat: 55.7558, lon: 37.6176 }
                }
            },
            processingMetadata: {
                processingDuration: 2500,
                analysisEngine: 'ThreatDetectionEngine',
                engineVersion: '3.2.1',
                correlationId: 'corr-001'
            }
        };
    });
    describe('constructor', () => {
        it('should create event with valid data', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.aggregateId).toEqual(aggregateId);
            expect(event.threatId).toBe('threat-001');
            expect(event.threatName).toBe('Advanced Persistent Threat Campaign');
            expect(event.severity).toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
            expect(event.confidence).toBe(92);
            expect(event.riskScore).toBe(88);
        });
        it('should set correct metadata', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.metadata.eventType).toBe('ThreatDetected');
            expect(event.metadata.domain).toBe('Security');
            expect(event.metadata.aggregateType).toBe('Threat');
            expect(event.metadata.processingStage).toBe('detection');
        });
        it('should accept custom options', () => {
            const correlationId = 'test-correlation-id';
            const customMetadata = { customField: 'customValue' };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData, {
                correlationId,
                metadata: customMetadata
            });
            expect(event.correlationId).toBe(correlationId);
            expect(event.metadata.customField).toBe('customValue');
        });
    });
    describe('severity checks', () => {
        it('should identify critical severity threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.isCriticalSeverity()).toBe(true);
            expect(event.isHighSeverityOrAbove()).toBe(true);
        });
        it('should identify high severity threats', () => {
            const highSeverityData = { ...eventData, severity: threat_severity_enum_1.ThreatSeverity.HIGH };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, highSeverityData);
            expect(event.isCriticalSeverity()).toBe(false);
            expect(event.isHighSeverityOrAbove()).toBe(true);
        });
        it('should identify medium severity threats', () => {
            const mediumSeverityData = { ...eventData, severity: threat_severity_enum_1.ThreatSeverity.MEDIUM };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, mediumSeverityData);
            expect(event.isCriticalSeverity()).toBe(false);
            expect(event.isHighSeverityOrAbove()).toBe(false);
        });
    });
    describe('confidence checks', () => {
        it('should identify high confidence threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.hasHighConfidence()).toBe(true);
            expect(event.hasLowConfidence()).toBe(false);
        });
        it('should identify low confidence threats', () => {
            const lowConfidenceData = { ...eventData, confidence: 45 };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, lowConfidenceData);
            expect(event.hasHighConfidence()).toBe(false);
            expect(event.hasLowConfidence()).toBe(true);
        });
        it('should identify medium confidence threats', () => {
            const mediumConfidenceData = { ...eventData, confidence: 65 };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, mediumConfidenceData);
            expect(event.hasHighConfidence()).toBe(false);
            expect(event.hasLowConfidence()).toBe(false);
        });
    });
    describe('risk score checks', () => {
        it('should identify high risk threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.hasHighRiskScore()).toBe(true);
        });
        it('should identify critical risk threats', () => {
            const criticalRiskData = { ...eventData, riskScore: 95 };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, criticalRiskData);
            expect(event.hasCriticalRiskScore()).toBe(true);
            expect(event.hasHighRiskScore()).toBe(true);
        });
        it('should identify low risk threats', () => {
            const lowRiskData = { ...eventData, riskScore: 35 };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, lowRiskData);
            expect(event.hasHighRiskScore()).toBe(false);
            expect(event.hasCriticalRiskScore()).toBe(false);
        });
    });
    describe('asset impact checks', () => {
        it('should identify threats affecting critical assets', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.affectsCriticalAssets()).toBe(true);
        });
        it('should identify threats affecting multiple assets', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.affectsMultipleAssets()).toBe(true);
        });
        it('should identify threats affecting single asset', () => {
            const singleAssetData = {
                ...eventData,
                affectedAssets: [eventData.affectedAssets[0]]
            };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, singleAssetData);
            expect(event.affectsMultipleAssets()).toBe(false);
        });
    });
    describe('indicator checks', () => {
        it('should identify threats with multiple indicators', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.hasMultipleIndicators()).toBe(true);
        });
        it('should identify threats with high-confidence indicators', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.hasHighConfidenceIndicators()).toBe(true);
        });
        it('should identify threats with single indicator', () => {
            const singleIndicatorData = {
                ...eventData,
                indicators: [eventData.indicators[0]]
            };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, singleIndicatorData);
            expect(event.hasMultipleIndicators()).toBe(false);
        });
    });
    describe('threat actor checks', () => {
        it('should identify threats with known threat actors', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.hasKnownThreatActor()).toBe(true);
        });
        it('should identify advanced threat actors', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.hasAdvancedThreatActor()).toBe(true);
        });
        it('should identify threats without known actors', () => {
            const noActorData = { ...eventData };
            delete noActorData.threatActor;
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, noActorData);
            expect(event.hasKnownThreatActor()).toBe(false);
            expect(event.hasAdvancedThreatActor()).toBe(false);
        });
    });
    describe('kill chain analysis', () => {
        it('should identify late kill chain stages', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.isLateKillChainStage()).toBe(true);
        });
        it('should identify early kill chain stages', () => {
            const earlyStageData = { ...eventData, killChainStage: 'reconnaissance' };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, earlyStageData);
            expect(event.isLateKillChainStage()).toBe(false);
        });
    });
    describe('business impact checks', () => {
        it('should identify high business impact threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.hasHighBusinessImpact()).toBe(true);
        });
        it('should identify critical business impact threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.hasCriticalBusinessImpact()).toBe(true);
        });
        it('should identify low business impact threats', () => {
            const lowImpactData = {
                ...eventData,
                potentialImpact: {
                    ...eventData.potentialImpact,
                    businessImpact: 'low'
                }
            };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, lowImpactData);
            expect(event.hasHighBusinessImpact()).toBe(false);
            expect(event.hasCriticalBusinessImpact()).toBe(false);
        });
    });
    describe('threat intelligence checks', () => {
        it('should identify threats with geolocation data', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.hasGeolocationData()).toBe(true);
        });
        it('should identify threats part of known campaigns', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.isPartOfKnownCampaign()).toBe(true);
        });
        it('should identify threats without intelligence context', () => {
            const noIntelData = { ...eventData };
            delete noIntelData.threatIntelligence;
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, noIntelData);
            expect(event.hasGeolocationData()).toBe(false);
            expect(event.isPartOfKnownCampaign()).toBe(false);
        });
    });
    describe('response requirements', () => {
        it('should require immediate response for critical threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.requiresImmediateResponse()).toBe(true);
        });
        it('should require executive notification for critical threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.requiresExecutiveNotification()).toBe(true);
        });
        it('should require SOC escalation for high severity threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.requiresSOCEscalation()).toBe(true);
        });
        it('should not require immediate response for low severity threats', () => {
            const lowSeverityData = {
                ...eventData,
                severity: threat_severity_enum_1.ThreatSeverity.LOW,
                confidence: 50,
                riskScore: 30,
                affectedAssets: [{
                        id: 'asset-001',
                        name: 'Test Server',
                        type: 'test',
                        criticality: 'low'
                    }]
            };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, lowSeverityData);
            expect(event.requiresImmediateResponse()).toBe(false);
            expect(event.requiresExecutiveNotification()).toBe(false);
        });
    });
    describe('response priority', () => {
        it('should assign critical priority to immediate response threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.getResponsePriority()).toBe('critical');
        });
        it('should assign high priority to high severity threats', () => {
            const highSeverityData = {
                ...eventData,
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                confidence: 60,
                riskScore: 60
            };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, highSeverityData);
            expect(event.getResponsePriority()).toBe('high');
        });
        it('should assign medium priority to medium severity threats', () => {
            const mediumSeverityData = {
                ...eventData,
                severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                riskScore: 50
            };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, mediumSeverityData);
            expect(event.getResponsePriority()).toBe('medium');
        });
        it('should assign low priority to low severity threats', () => {
            const lowSeverityData = {
                ...eventData,
                severity: threat_severity_enum_1.ThreatSeverity.LOW,
                riskScore: 30
            };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, lowSeverityData);
            expect(event.getResponsePriority()).toBe('low');
        });
    });
    describe('containment urgency', () => {
        it('should set short containment time for immediate response threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.getContainmentUrgency()).toBe(15); // 15 minutes
        });
        it('should set appropriate containment times by severity', () => {
            const highSeverityData = { ...eventData, severity: threat_severity_enum_1.ThreatSeverity.HIGH };
            const mediumSeverityData = { ...eventData, severity: threat_severity_enum_1.ThreatSeverity.MEDIUM };
            const lowSeverityData = { ...eventData, severity: threat_severity_enum_1.ThreatSeverity.LOW };
            const highEvent = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, highSeverityData);
            const mediumEvent = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, mediumSeverityData);
            const lowEvent = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, lowSeverityData);
            expect(highEvent.getContainmentUrgency()).toBe(120); // 2 hours
            expect(mediumEvent.getContainmentUrgency()).toBe(480); // 8 hours
            expect(lowEvent.getContainmentUrgency()).toBe(1440); // 24 hours
        });
    });
    describe('investigation timeline', () => {
        it('should set short investigation time for immediate response threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            expect(event.getInvestigationTimeline()).toBe(2); // 2 hours
        });
        it('should set appropriate investigation times by severity', () => {
            const highSeverityData = { ...eventData, severity: threat_severity_enum_1.ThreatSeverity.HIGH };
            const mediumSeverityData = { ...eventData, severity: threat_severity_enum_1.ThreatSeverity.MEDIUM };
            const lowSeverityData = { ...eventData, severity: threat_severity_enum_1.ThreatSeverity.LOW };
            const highEvent = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, highSeverityData);
            const mediumEvent = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, mediumSeverityData);
            const lowEvent = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, lowSeverityData);
            expect(highEvent.getInvestigationTimeline()).toBe(24); // 24 hours
            expect(mediumEvent.getInvestigationTimeline()).toBe(72); // 3 days
            expect(lowEvent.getInvestigationTimeline()).toBe(168); // 7 days
        });
    });
    describe('containment actions', () => {
        it('should recommend comprehensive actions for immediate response threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            const actions = event.getRecommendedContainmentActions();
            expect(actions).toContain('isolate_affected_systems');
            expect(actions).toContain('block_malicious_ips');
            expect(actions).toContain('disable_compromised_accounts');
            expect(actions).toContain('block_source_ip');
            expect(actions).toContain('emergency_asset_isolation');
            expect(actions).toContain('advanced_forensics');
            expect(actions).toContain('damage_assessment');
        });
        it('should recommend basic actions for low severity threats', () => {
            const lowSeverityData = {
                ...eventData,
                severity: threat_severity_enum_1.ThreatSeverity.LOW,
                confidence: 50,
                riskScore: 30,
                affectedAssets: [{
                        id: 'asset-001',
                        name: 'Test Server',
                        type: 'test',
                        criticality: 'low'
                    }],
                killChainStage: 'reconnaissance'
            };
            delete lowSeverityData.sourceIp;
            delete lowSeverityData.threatActor;
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, lowSeverityData);
            const actions = event.getRecommendedContainmentActions();
            expect(actions).toHaveLength(0); // No immediate actions for low severity
        });
    });
    describe('notification targets', () => {
        it('should include comprehensive targets for critical threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            const targets = event.getNotificationTargets();
            expect(targets).toContain('security_operations_center');
            expect(targets).toContain('security_leadership');
            expect(targets).toContain('executive_team');
            expect(targets).toContain('incident_response_team');
            expect(targets).toContain('asset_owners');
            expect(targets).toContain('threat_intelligence_team');
            expect(targets).toContain('business_continuity_team');
        });
        it('should include basic targets for low severity threats', () => {
            const lowSeverityData = {
                ...eventData,
                severity: threat_severity_enum_1.ThreatSeverity.LOW,
                confidence: 50,
                riskScore: 30,
                potentialImpact: {
                    ...eventData.potentialImpact,
                    businessImpact: 'low'
                },
                affectedAssets: [{
                        id: 'asset-001',
                        name: 'Test Server',
                        type: 'test',
                        criticality: 'low'
                    }]
            };
            delete lowSeverityData.threatActor;
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, lowSeverityData);
            const targets = event.getNotificationTargets();
            expect(targets).toContain('security_operations_center');
            expect(targets).not.toContain('executive_team');
            expect(targets).not.toContain('threat_intelligence_team');
        });
    });
    describe('threat detection metrics', () => {
        it('should generate comprehensive metrics', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            const metrics = event.getThreatDetectionMetrics();
            expect(metrics.threatId).toBe('threat-001');
            expect(metrics.name).toBe('Advanced Persistent Threat Campaign');
            expect(metrics.severity).toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
            expect(metrics.confidence).toBe(92);
            expect(metrics.riskScore).toBe(88);
            expect(metrics.affectedAssetsCount).toBe(2);
            expect(metrics.indicatorsCount).toBe(2);
            expect(metrics.requiresImmediateResponse).toBe(true);
            expect(metrics.affectsCriticalAssets).toBe(true);
            expect(metrics.hasAdvancedThreatActor).toBe(true);
            expect(metrics.processingDuration).toBe(2500);
        });
    });
    describe('integration event conversion', () => {
        it('should convert to integration event format', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            const integrationEvent = event.toIntegrationEvent();
            expect(integrationEvent.eventType).toBe('ThreatDetected');
            expect(integrationEvent.version).toBe('1.0');
            expect(integrationEvent.data.threatId).toBe('threat-001');
            expect(integrationEvent.data.threat.name).toBe('Advanced Persistent Threat Campaign');
            expect(integrationEvent.data.threat.severity).toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
            expect(integrationEvent.data.assets.criticalAssetsAffected).toBe(true);
            expect(integrationEvent.data.indicators.count).toBe(2);
            expect(integrationEvent.data.attack.killChainStage).toBe('actions_objectives');
            expect(integrationEvent.data.response.requiresImmediateResponse).toBe(true);
            expect(integrationEvent.data.intelligence?.campaigns).toContain('Operation Ghost');
        });
    });
    describe('JSON serialization', () => {
        it('should serialize to JSON correctly', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            const json = event.toJSON();
            expect(json.eventData).toEqual(eventData);
            expect(json.analysis.isCriticalSeverity).toBe(true);
            expect(json.analysis.hasHighConfidence).toBe(true);
            expect(json.analysis.requiresImmediateResponse).toBe(true);
            expect(json.analysis.responsePriority).toBe('critical');
        });
        it('should deserialize from JSON correctly', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            const json = event.toJSON();
            const deserializedEvent = threat_detected_domain_event_1.ThreatDetectedDomainEvent.fromJSON(json);
            expect(deserializedEvent.aggregateId.equals(event.aggregateId)).toBe(true);
            expect(deserializedEvent.threatId).toBe(event.threatId);
            expect(deserializedEvent.threatName).toBe(event.threatName);
            expect(deserializedEvent.severity).toBe(event.severity);
        });
    });
    describe('human-readable description', () => {
        it('should generate appropriate description for critical threats', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            const description = event.getDescription();
            expect(description).toContain('critical severity');
            expect(description).toContain('Advanced Persistent Threat Campaign');
            expect(description).toContain('high confidence');
            expect(description).toContain('affecting critical assets');
            expect(description).toContain('APT-29');
        });
        it('should generate description without threat actor for unknown actors', () => {
            const noActorData = { ...eventData };
            delete noActorData.threatActor;
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, noActorData);
            const description = event.getDescription();
            expect(description).not.toContain('by ');
            expect(description).toContain('critical severity');
        });
    });
    describe('event summary', () => {
        it('should generate comprehensive summary', () => {
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, eventData);
            const summary = event.getSummary();
            expect(summary.eventType).toBe('ThreatDetected');
            expect(summary.threatId).toBe('threat-001');
            expect(summary.name).toBe('Advanced Persistent Threat Campaign');
            expect(summary.severity).toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
            expect(summary.confidence).toBe(92);
            expect(summary.requiresImmediateResponse).toBe(true);
            expect(summary.responsePriority).toBe('critical');
            expect(summary.killChainStage).toBe('actions_objectives');
        });
    });
    describe('edge cases', () => {
        it('should handle threats without CVSS score', () => {
            const noCvssData = { ...eventData };
            delete noCvssData.cvssScore;
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, noCvssData);
            expect(event.cvssScore).toBeUndefined();
        });
        it('should handle threats without IP addresses', () => {
            const noIpData = { ...eventData };
            delete noIpData.sourceIp;
            delete noIpData.targetIp;
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, noIpData);
            const actions = event.getRecommendedContainmentActions();
            expect(event.sourceIp).toBeUndefined();
            expect(event.targetIp).toBeUndefined();
            expect(actions).not.toContain('block_source_ip');
        });
        it('should handle threats without related events', () => {
            const noRelatedData = { ...eventData };
            delete noRelatedData.relatedEvents;
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, noRelatedData);
            expect(event.relatedEvents).toBeUndefined();
        });
        it('should handle empty indicators array', () => {
            const noIndicatorsData = { ...eventData, indicators: [] };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, noIndicatorsData);
            expect(event.hasMultipleIndicators()).toBe(false);
            expect(event.hasHighConfidenceIndicators()).toBe(false);
        });
        it('should handle empty affected assets array', () => {
            const noAssetsData = { ...eventData, affectedAssets: [] };
            const event = new threat_detected_domain_event_1.ThreatDetectedDomainEvent(aggregateId, noAssetsData);
            expect(event.affectsMultipleAssets()).toBe(false);
            expect(event.affectsCriticalAssets()).toBe(false);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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