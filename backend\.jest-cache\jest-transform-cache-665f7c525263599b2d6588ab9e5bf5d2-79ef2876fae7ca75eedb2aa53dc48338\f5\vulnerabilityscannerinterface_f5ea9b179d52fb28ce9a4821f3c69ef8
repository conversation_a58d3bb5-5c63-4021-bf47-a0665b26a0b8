efc94484c1704ac261dde9a11bed5cc1
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScheduleType = exports.ScanDepth = exports.ExclusionType = exports.CredentialType = exports.VulnerabilityScanType = exports.ScanTargetType = exports.BusinessImpactLevel = exports.ReferenceType = exports.RemediationEffort = exports.RemediationPriority = exports.RemediationType = exports.ExploitReliability = exports.ExploitComplexity = exports.ExploitAvailability = exports.ExploitType = exports.ComponentCriticality = exports.ComponentType = exports.ExploitationLikelihood = exports.VulnerabilityDiscoveryMethod = exports.VulnerabilityType = exports.VulnerabilityCategory = exports.VulnerabilityScanMethod = exports.VulnerabilitySeverity = void 0;
const vulnerability_severity_enum_1 = require("../../enums/vulnerability-severity.enum");
Object.defineProperty(exports, "VulnerabilitySeverity", { enumerable: true, get: function () { return vulnerability_severity_enum_1.VulnerabilitySeverity; } });
/**
 * Vulnerability Scan Method Enum
 */
var VulnerabilityScanMethod;
(function (VulnerabilityScanMethod) {
    VulnerabilityScanMethod["AUTHENTICATED"] = "AUTHENTICATED";
    VulnerabilityScanMethod["UNAUTHENTICATED"] = "UNAUTHENTICATED";
    VulnerabilityScanMethod["NETWORK_SCAN"] = "NETWORK_SCAN";
    VulnerabilityScanMethod["WEB_APPLICATION"] = "WEB_APPLICATION";
    VulnerabilityScanMethod["DATABASE_SCAN"] = "DATABASE_SCAN";
    VulnerabilityScanMethod["CONTAINER_SCAN"] = "CONTAINER_SCAN";
    VulnerabilityScanMethod["CLOUD_SCAN"] = "CLOUD_SCAN";
    VulnerabilityScanMethod["MOBILE_SCAN"] = "MOBILE_SCAN";
    VulnerabilityScanMethod["API_SCAN"] = "API_SCAN";
    VulnerabilityScanMethod["INFRASTRUCTURE"] = "INFRASTRUCTURE";
    VulnerabilityScanMethod["COMPLIANCE_SCAN"] = "COMPLIANCE_SCAN";
    VulnerabilityScanMethod["HYBRID"] = "HYBRID";
})(VulnerabilityScanMethod || (exports.VulnerabilityScanMethod = VulnerabilityScanMethod = {}));
/**
 * Vulnerability Category Enum
 */
var VulnerabilityCategory;
(function (VulnerabilityCategory) {
    VulnerabilityCategory["INJECTION"] = "INJECTION";
    VulnerabilityCategory["AUTHENTICATION"] = "AUTHENTICATION";
    VulnerabilityCategory["AUTHORIZATION"] = "AUTHORIZATION";
    VulnerabilityCategory["CRYPTOGRAPHY"] = "CRYPTOGRAPHY";
    VulnerabilityCategory["CONFIGURATION"] = "CONFIGURATION";
    VulnerabilityCategory["INPUT_VALIDATION"] = "INPUT_VALIDATION";
    VulnerabilityCategory["SESSION_MANAGEMENT"] = "SESSION_MANAGEMENT";
    VulnerabilityCategory["CROSS_SITE_SCRIPTING"] = "CROSS_SITE_SCRIPTING";
    VulnerabilityCategory["CROSS_SITE_REQUEST_FORGERY"] = "CROSS_SITE_REQUEST_FORGERY";
    VulnerabilityCategory["BUFFER_OVERFLOW"] = "BUFFER_OVERFLOW";
    VulnerabilityCategory["PRIVILEGE_ESCALATION"] = "PRIVILEGE_ESCALATION";
    VulnerabilityCategory["INFORMATION_DISCLOSURE"] = "INFORMATION_DISCLOSURE";
    VulnerabilityCategory["DENIAL_OF_SERVICE"] = "DENIAL_OF_SERVICE";
    VulnerabilityCategory["REMOTE_CODE_EXECUTION"] = "REMOTE_CODE_EXECUTION";
    VulnerabilityCategory["PATH_TRAVERSAL"] = "PATH_TRAVERSAL";
    VulnerabilityCategory["INSECURE_DESERIALIZATION"] = "INSECURE_DESERIALIZATION";
    VulnerabilityCategory["MISSING_PATCHES"] = "MISSING_PATCHES";
    VulnerabilityCategory["WEAK_PASSWORDS"] = "WEAK_PASSWORDS";
    VulnerabilityCategory["INSECURE_PROTOCOLS"] = "INSECURE_PROTOCOLS";
    VulnerabilityCategory["MISCONFIGURATION"] = "MISCONFIGURATION";
})(VulnerabilityCategory || (exports.VulnerabilityCategory = VulnerabilityCategory = {}));
/**
 * Vulnerability Type Enum
 */
var VulnerabilityType;
(function (VulnerabilityType) {
    VulnerabilityType["KNOWN_VULNERABILITY"] = "KNOWN_VULNERABILITY";
    VulnerabilityType["ZERO_DAY"] = "ZERO_DAY";
    VulnerabilityType["CONFIGURATION_ISSUE"] = "CONFIGURATION_ISSUE";
    VulnerabilityType["WEAK_CREDENTIAL"] = "WEAK_CREDENTIAL";
    VulnerabilityType["MISSING_PATCH"] = "MISSING_PATCH";
    VulnerabilityType["DESIGN_FLAW"] = "DESIGN_FLAW";
    VulnerabilityType["IMPLEMENTATION_BUG"] = "IMPLEMENTATION_BUG";
    VulnerabilityType["POLICY_VIOLATION"] = "POLICY_VIOLATION";
    VulnerabilityType["COMPLIANCE_GAP"] = "COMPLIANCE_GAP";
    VulnerabilityType["CUSTOM"] = "CUSTOM";
})(VulnerabilityType || (exports.VulnerabilityType = VulnerabilityType = {}));
/**
 * Vulnerability Discovery Method Enum
 */
var VulnerabilityDiscoveryMethod;
(function (VulnerabilityDiscoveryMethod) {
    VulnerabilityDiscoveryMethod["AUTOMATED_SCAN"] = "AUTOMATED_SCAN";
    VulnerabilityDiscoveryMethod["MANUAL_TESTING"] = "MANUAL_TESTING";
    VulnerabilityDiscoveryMethod["PENETRATION_TEST"] = "PENETRATION_TEST";
    VulnerabilityDiscoveryMethod["CODE_REVIEW"] = "CODE_REVIEW";
    VulnerabilityDiscoveryMethod["THREAT_INTELLIGENCE"] = "THREAT_INTELLIGENCE";
    VulnerabilityDiscoveryMethod["BUG_BOUNTY"] = "BUG_BOUNTY";
    VulnerabilityDiscoveryMethod["SECURITY_AUDIT"] = "SECURITY_AUDIT";
    VulnerabilityDiscoveryMethod["COMPLIANCE_CHECK"] = "COMPLIANCE_CHECK";
    VulnerabilityDiscoveryMethod["INCIDENT_RESPONSE"] = "INCIDENT_RESPONSE";
    VulnerabilityDiscoveryMethod["VENDOR_DISCLOSURE"] = "VENDOR_DISCLOSURE";
})(VulnerabilityDiscoveryMethod || (exports.VulnerabilityDiscoveryMethod = VulnerabilityDiscoveryMethod = {}));
/**
 * Exploitation Likelihood Enum
 */
var ExploitationLikelihood;
(function (ExploitationLikelihood) {
    ExploitationLikelihood["VERY_LOW"] = "VERY_LOW";
    ExploitationLikelihood["LOW"] = "LOW";
    ExploitationLikelihood["MEDIUM"] = "MEDIUM";
    ExploitationLikelihood["HIGH"] = "HIGH";
    ExploitationLikelihood["VERY_HIGH"] = "VERY_HIGH";
    ExploitationLikelihood["CRITICAL"] = "CRITICAL";
})(ExploitationLikelihood || (exports.ExploitationLikelihood = ExploitationLikelihood = {}));
/**
 * Component Type Enum
 */
var ComponentType;
(function (ComponentType) {
    ComponentType["APPLICATION"] = "APPLICATION";
    ComponentType["OPERATING_SYSTEM"] = "OPERATING_SYSTEM";
    ComponentType["DATABASE"] = "DATABASE";
    ComponentType["WEB_SERVER"] = "WEB_SERVER";
    ComponentType["NETWORK_DEVICE"] = "NETWORK_DEVICE";
    ComponentType["CONTAINER"] = "CONTAINER";
    ComponentType["CLOUD_SERVICE"] = "CLOUD_SERVICE";
    ComponentType["API"] = "API";
    ComponentType["LIBRARY"] = "LIBRARY";
    ComponentType["FRAMEWORK"] = "FRAMEWORK";
    ComponentType["PLUGIN"] = "PLUGIN";
    ComponentType["SERVICE"] = "SERVICE";
})(ComponentType || (exports.ComponentType = ComponentType = {}));
/**
 * Component Criticality Enum
 */
var ComponentCriticality;
(function (ComponentCriticality) {
    ComponentCriticality["LOW"] = "LOW";
    ComponentCriticality["MEDIUM"] = "MEDIUM";
    ComponentCriticality["HIGH"] = "HIGH";
    ComponentCriticality["CRITICAL"] = "CRITICAL";
})(ComponentCriticality || (exports.ComponentCriticality = ComponentCriticality = {}));
/**
 * Exploit Type Enum
 */
var ExploitType;
(function (ExploitType) {
    ExploitType["REMOTE"] = "REMOTE";
    ExploitType["LOCAL"] = "LOCAL";
    ExploitType["WEB"] = "WEB";
    ExploitType["CLIENT_SIDE"] = "CLIENT_SIDE";
    ExploitType["DENIAL_OF_SERVICE"] = "DENIAL_OF_SERVICE";
    ExploitType["PRIVILEGE_ESCALATION"] = "PRIVILEGE_ESCALATION";
    ExploitType["CODE_EXECUTION"] = "CODE_EXECUTION";
    ExploitType["INFORMATION_DISCLOSURE"] = "INFORMATION_DISCLOSURE";
})(ExploitType || (exports.ExploitType = ExploitType = {}));
/**
 * Exploit Availability Enum
 */
var ExploitAvailability;
(function (ExploitAvailability) {
    ExploitAvailability["PUBLIC"] = "PUBLIC";
    ExploitAvailability["PRIVATE"] = "PRIVATE";
    ExploitAvailability["COMMERCIAL"] = "COMMERCIAL";
    ExploitAvailability["PROOF_OF_CONCEPT"] = "PROOF_OF_CONCEPT";
    ExploitAvailability["WEAPONIZED"] = "WEAPONIZED";
    ExploitAvailability["IN_THE_WILD"] = "IN_THE_WILD";
})(ExploitAvailability || (exports.ExploitAvailability = ExploitAvailability = {}));
/**
 * Exploit Complexity Enum
 */
var ExploitComplexity;
(function (ExploitComplexity) {
    ExploitComplexity["LOW"] = "LOW";
    ExploitComplexity["MEDIUM"] = "MEDIUM";
    ExploitComplexity["HIGH"] = "HIGH";
})(ExploitComplexity || (exports.ExploitComplexity = ExploitComplexity = {}));
/**
 * Exploit Reliability Enum
 */
var ExploitReliability;
(function (ExploitReliability) {
    ExploitReliability["UNRELIABLE"] = "UNRELIABLE";
    ExploitReliability["AVERAGE"] = "AVERAGE";
    ExploitReliability["GOOD"] = "GOOD";
    ExploitReliability["EXCELLENT"] = "EXCELLENT";
})(ExploitReliability || (exports.ExploitReliability = ExploitReliability = {}));
/**
 * Remediation Type Enum
 */
var RemediationType;
(function (RemediationType) {
    RemediationType["PATCH"] = "PATCH";
    RemediationType["CONFIGURATION_CHANGE"] = "CONFIGURATION_CHANGE";
    RemediationType["WORKAROUND"] = "WORKAROUND";
    RemediationType["UPGRADE"] = "UPGRADE";
    RemediationType["REPLACEMENT"] = "REPLACEMENT";
    RemediationType["MITIGATION"] = "MITIGATION";
    RemediationType["ACCEPTANCE"] = "ACCEPTANCE";
    RemediationType["TRANSFER"] = "TRANSFER";
})(RemediationType || (exports.RemediationType = RemediationType = {}));
/**
 * Remediation Priority Enum
 */
var RemediationPriority;
(function (RemediationPriority) {
    RemediationPriority["IMMEDIATE"] = "IMMEDIATE";
    RemediationPriority["HIGH"] = "HIGH";
    RemediationPriority["MEDIUM"] = "MEDIUM";
    RemediationPriority["LOW"] = "LOW";
    RemediationPriority["DEFERRED"] = "DEFERRED";
})(RemediationPriority || (exports.RemediationPriority = RemediationPriority = {}));
/**
 * Remediation Effort Enum
 */
var RemediationEffort;
(function (RemediationEffort) {
    RemediationEffort["MINIMAL"] = "MINIMAL";
    RemediationEffort["LOW"] = "LOW";
    RemediationEffort["MEDIUM"] = "MEDIUM";
    RemediationEffort["HIGH"] = "HIGH";
    RemediationEffort["EXTENSIVE"] = "EXTENSIVE";
})(RemediationEffort || (exports.RemediationEffort = RemediationEffort = {}));
/**
 * Reference Type Enum
 */
var ReferenceType;
(function (ReferenceType) {
    ReferenceType["CVE"] = "CVE";
    ReferenceType["CWE"] = "CWE";
    ReferenceType["VENDOR_ADVISORY"] = "VENDOR_ADVISORY";
    ReferenceType["SECURITY_BULLETIN"] = "SECURITY_BULLETIN";
    ReferenceType["EXPLOIT_DATABASE"] = "EXPLOIT_DATABASE";
    ReferenceType["RESEARCH_PAPER"] = "RESEARCH_PAPER";
    ReferenceType["BLOG_POST"] = "BLOG_POST";
    ReferenceType["CONFERENCE_TALK"] = "CONFERENCE_TALK";
    ReferenceType["PATCH_NOTES"] = "PATCH_NOTES";
    ReferenceType["DOCUMENTATION"] = "DOCUMENTATION";
})(ReferenceType || (exports.ReferenceType = ReferenceType = {}));
/**
 * Business Impact Level Enum
 */
var BusinessImpactLevel;
(function (BusinessImpactLevel) {
    BusinessImpactLevel["NEGLIGIBLE"] = "NEGLIGIBLE";
    BusinessImpactLevel["LOW"] = "LOW";
    BusinessImpactLevel["MEDIUM"] = "MEDIUM";
    BusinessImpactLevel["HIGH"] = "HIGH";
    BusinessImpactLevel["CRITICAL"] = "CRITICAL";
})(BusinessImpactLevel || (exports.BusinessImpactLevel = BusinessImpactLevel = {}));
/**
 * Scan Target Type Enum
 */
var ScanTargetType;
(function (ScanTargetType) {
    ScanTargetType["IP_ADDRESS"] = "IP_ADDRESS";
    ScanTargetType["HOSTNAME"] = "HOSTNAME";
    ScanTargetType["URL"] = "URL";
    ScanTargetType["NETWORK_RANGE"] = "NETWORK_RANGE";
    ScanTargetType["APPLICATION"] = "APPLICATION";
    ScanTargetType["DATABASE"] = "DATABASE";
    ScanTargetType["CONTAINER"] = "CONTAINER";
    ScanTargetType["CLOUD_RESOURCE"] = "CLOUD_RESOURCE";
    ScanTargetType["API_ENDPOINT"] = "API_ENDPOINT";
})(ScanTargetType || (exports.ScanTargetType = ScanTargetType = {}));
/**
 * Vulnerability Scan Type Enum
 */
var VulnerabilityScanType;
(function (VulnerabilityScanType) {
    VulnerabilityScanType["QUICK"] = "QUICK";
    VulnerabilityScanType["FULL"] = "FULL";
    VulnerabilityScanType["COMPREHENSIVE"] = "COMPREHENSIVE";
    VulnerabilityScanType["COMPLIANCE"] = "COMPLIANCE";
    VulnerabilityScanType["CUSTOM"] = "CUSTOM";
    VulnerabilityScanType["CONTINUOUS"] = "CONTINUOUS";
})(VulnerabilityScanType || (exports.VulnerabilityScanType = VulnerabilityScanType = {}));
/**
 * Credential Type Enum
 */
var CredentialType;
(function (CredentialType) {
    CredentialType["USERNAME_PASSWORD"] = "USERNAME_PASSWORD";
    CredentialType["API_KEY"] = "API_KEY";
    CredentialType["CERTIFICATE"] = "CERTIFICATE";
    CredentialType["SSH_KEY"] = "SSH_KEY";
    CredentialType["TOKEN"] = "TOKEN";
    CredentialType["OAUTH"] = "OAUTH";
    CredentialType["CUSTOM"] = "CUSTOM";
})(CredentialType || (exports.CredentialType = CredentialType = {}));
/**
 * Exclusion Type Enum
 */
var ExclusionType;
(function (ExclusionType) {
    ExclusionType["IP_ADDRESS"] = "IP_ADDRESS";
    ExclusionType["HOSTNAME"] = "HOSTNAME";
    ExclusionType["PORT"] = "PORT";
    ExclusionType["SERVICE"] = "SERVICE";
    ExclusionType["VULNERABILITY_ID"] = "VULNERABILITY_ID";
    ExclusionType["CATEGORY"] = "CATEGORY";
    ExclusionType["SEVERITY"] = "SEVERITY";
})(ExclusionType || (exports.ExclusionType = ExclusionType = {}));
/**
 * Scan Depth Enum
 */
var ScanDepth;
(function (ScanDepth) {
    ScanDepth["SURFACE"] = "SURFACE";
    ScanDepth["SHALLOW"] = "SHALLOW";
    ScanDepth["MEDIUM"] = "MEDIUM";
    ScanDepth["DEEP"] = "DEEP";
    ScanDepth["EXHAUSTIVE"] = "EXHAUSTIVE";
})(ScanDepth || (exports.ScanDepth = ScanDepth = {}));
/**
 * Schedule Type Enum
 */
var ScheduleType;
(function (ScheduleType) {
    ScheduleType["ONE_TIME"] = "ONE_TIME";
    ScheduleType["RECURRING"] = "RECURRING";
    ScheduleType["CONTINUOUS"] = "CONTINUOUS";
    ScheduleType["EVENT_DRIVEN"] = "EVENT_DRIVEN";
})(ScheduleType || (exports.ScheduleType = ScheduleType = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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