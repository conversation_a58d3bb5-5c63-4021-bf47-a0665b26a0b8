d1d7621241ce33148fc1aa4aded3bc6e
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var IncidentManagementService_1;
var _a, _b, _c, _d, _e, _f, _g, _h, _j;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncidentManagementService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const incident_entity_1 = require("../../domain/entities/incident.entity");
const incident_task_entity_1 = require("../../domain/entities/incident-task.entity");
const incident_evidence_entity_1 = require("../../domain/entities/incident-evidence.entity");
const incident_timeline_entity_1 = require("../../domain/entities/incident-timeline.entity");
const response_plan_entity_1 = require("../../domain/entities/response-plan.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("./notification.service");
const response_orchestration_service_1 = require("./response-orchestration.service");
/**
 * Incident Management service
 * Handles incident lifecycle management, escalation, and coordination
 */
let IncidentManagementService = IncidentManagementService_1 = class IncidentManagementService {
    constructor(incidentRepository, taskRepository, evidenceRepository, timelineRepository, responsePlanRepository, loggerService, auditService, notificationService, responseOrchestrationService) {
        this.incidentRepository = incidentRepository;
        this.taskRepository = taskRepository;
        this.evidenceRepository = evidenceRepository;
        this.timelineRepository = timelineRepository;
        this.responsePlanRepository = responsePlanRepository;
        this.loggerService = loggerService;
        this.auditService = auditService;
        this.notificationService = notificationService;
        this.responseOrchestrationService = responseOrchestrationService;
        this.logger = new common_1.Logger(IncidentManagementService_1.name);
    }
    /**
     * Create a new incident
     * @param incidentData Incident creation data
     * @param userId User creating the incident
     * @returns Created incident
     */
    async createIncident(incidentData, userId) {
        try {
            this.logger.debug('Creating new incident', {
                title: incidentData.title,
                severity: incidentData.severity,
                category: incidentData.category,
                userId,
            });
            const incident = this.incidentRepository.create({
                ...incidentData,
                detectedAt: incidentData.detectedAt || new Date(),
                priority: incidentData.priority || this.calculateDefaultPriority(incidentData.severity),
                source: incidentData.source || 'manual',
                confidentiality: incidentData.confidentiality || 'internal',
                tags: incidentData.tags || [],
                createdBy: userId,
            });
            const savedIncident = await this.incidentRepository.save(incident);
            // Create initial timeline entry
            await this.addTimelineEntry(savedIncident.id, 'incident_created', 'Incident created', userId, { severity: incidentData.severity, category: incidentData.category });
            // Find and apply appropriate response plan
            await this.applyResponsePlan(savedIncident);
            // Send notifications
            await this.notificationService.sendIncidentNotification(savedIncident, 'created');
            await this.auditService.logUserAction(userId, 'create', 'incident', savedIncident.id, {
                title: incidentData.title,
                severity: incidentData.severity,
                category: incidentData.category,
            });
            this.logger.log('Incident created successfully', {
                incidentId: savedIncident.id,
                title: incidentData.title,
                userId,
            });
            return savedIncident;
        }
        catch (error) {
            this.logger.error('Failed to create incident', {
                error: error.message,
                incidentData,
                userId,
            });
            throw error;
        }
    }
    /**
     * Find incident by ID
     * @param id Incident ID
     * @returns Incident or null
     */
    async findById(id) {
        try {
            return await this.incidentRepository.findOne({
                where: { id },
                relations: ['tasks', 'evidence', 'communications', 'timeline', 'responsePlan'],
            });
        }
        catch (error) {
            this.logger.error('Failed to find incident by ID', {
                id,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Find incidents with filtering and pagination
     * @param options Query options
     * @returns Paginated incidents
     */
    async findMany(options) {
        try {
            const { page = 1, limit = 20, status, severity, priority, category, assignedTo, search, tags, dateRange, sortBy = 'createdAt', sortOrder = 'DESC', } = options;
            const queryBuilder = this.incidentRepository.createQueryBuilder('incident');
            // Apply filters
            if (status && status.length > 0) {
                queryBuilder.andWhere('incident.status IN (:...status)', { status });
            }
            if (severity && severity.length > 0) {
                queryBuilder.andWhere('incident.severity IN (:...severity)', { severity });
            }
            if (priority && priority.length > 0) {
                queryBuilder.andWhere('incident.priority IN (:...priority)', { priority });
            }
            if (category && category.length > 0) {
                queryBuilder.andWhere('incident.category IN (:...category)', { category });
            }
            if (assignedTo) {
                queryBuilder.andWhere('incident.assignedTo = :assignedTo', { assignedTo });
            }
            if (search) {
                queryBuilder.andWhere('(incident.title ILIKE :search OR incident.description ILIKE :search)', { search: `%${search}%` });
            }
            if (tags && tags.length > 0) {
                queryBuilder.andWhere('incident.tags && :tags', { tags });
            }
            if (dateRange) {
                queryBuilder.andWhere('incident.detectedAt BETWEEN :start AND :end', {
                    start: dateRange.start,
                    end: dateRange.end,
                });
            }
            // Apply sorting
            queryBuilder.orderBy(`incident.${sortBy}`, sortOrder);
            // Apply pagination
            const offset = (page - 1) * limit;
            queryBuilder.skip(offset).take(limit);
            const [incidents, total] = await queryBuilder.getManyAndCount();
            const totalPages = Math.ceil(total / limit);
            this.logger.debug('Incidents retrieved', {
                total,
                page,
                limit,
                totalPages,
                filters: { status, severity, priority, category, assignedTo, search, tags },
            });
            return {
                incidents,
                total,
                page,
                totalPages,
            };
        }
        catch (error) {
            this.logger.error('Failed to find incidents', {
                error: error.message,
                options,
            });
            throw error;
        }
    }
    /**
     * Update incident
     * @param id Incident ID
     * @param updateData Update data
     * @param userId User updating the incident
     * @returns Updated incident
     */
    async updateIncident(id, updateData, userId) {
        try {
            const incident = await this.findById(id);
            if (!incident) {
                throw new common_1.NotFoundException('Incident not found');
            }
            this.logger.debug('Updating incident', {
                incidentId: id,
                updateData,
                userId,
            });
            const previousStatus = incident.status;
            const previousSeverity = incident.severity;
            const previousPriority = incident.priority;
            // Update incident properties
            Object.assign(incident, updateData, {
                updatedBy: userId,
            });
            const updatedIncident = await this.incidentRepository.save(incident);
            // Create timeline entries for significant changes
            if (updateData.status && updateData.status !== previousStatus) {
                await this.addTimelineEntry(id, 'status_changed', `Status changed from ${previousStatus} to ${updateData.status}`, userId, { previousStatus, newStatus: updateData.status });
            }
            if (updateData.severity && updateData.severity !== previousSeverity) {
                await this.addTimelineEntry(id, 'severity_changed', `Severity changed from ${previousSeverity} to ${updateData.severity}`, userId, { previousSeverity, newSeverity: updateData.severity });
            }
            if (updateData.priority && updateData.priority !== previousPriority) {
                await this.addTimelineEntry(id, 'priority_changed', `Priority changed from ${previousPriority} to ${updateData.priority}`, userId, { previousPriority, newPriority: updateData.priority });
            }
            // Send notifications for status changes
            if (updateData.status && updateData.status !== previousStatus) {
                await this.notificationService.sendIncidentNotification(updatedIncident, 'status_changed');
            }
            await this.auditService.logUserAction(userId, 'update', 'incident', id, {
                changes: updateData,
                previousValues: { status: previousStatus, severity: previousSeverity, priority: previousPriority },
            });
            this.logger.log('Incident updated successfully', {
                incidentId: id,
                userId,
                changes: Object.keys(updateData),
            });
            return updatedIncident;
        }
        catch (error) {
            this.logger.error('Failed to update incident', {
                incidentId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Acknowledge incident
     * @param id Incident ID
     * @param userId User acknowledging the incident
     * @returns Updated incident
     */
    async acknowledgeIncident(id, userId) {
        try {
            const incident = await this.findById(id);
            if (!incident) {
                throw new common_1.NotFoundException('Incident not found');
            }
            if (incident.status !== 'new') {
                throw new common_1.BadRequestException('Incident has already been acknowledged');
            }
            incident.acknowledge(userId);
            const updatedIncident = await this.incidentRepository.save(incident);
            await this.addTimelineEntry(id, 'acknowledged', 'Incident acknowledged and investigation started', userId, { timeToAcknowledge: incident.metrics?.timeToAcknowledge });
            await this.notificationService.sendIncidentNotification(updatedIncident, 'acknowledged');
            await this.auditService.logUserAction(userId, 'acknowledge', 'incident', id, { timeToAcknowledge: incident.metrics?.timeToAcknowledge });
            this.logger.log('Incident acknowledged', {
                incidentId: id,
                userId,
                timeToAcknowledge: incident.metrics?.timeToAcknowledge,
            });
            return updatedIncident;
        }
        catch (error) {
            this.logger.error('Failed to acknowledge incident', {
                incidentId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Escalate incident
     * @param id Incident ID
     * @param reason Escalation reason
     * @param userId User escalating the incident
     * @returns Updated incident
     */
    async escalateIncident(id, reason, userId) {
        try {
            const incident = await this.findById(id);
            if (!incident) {
                throw new common_1.NotFoundException('Incident not found');
            }
            incident.escalate(reason, userId);
            const updatedIncident = await this.incidentRepository.save(incident);
            await this.addTimelineEntry(id, 'escalated', `Incident escalated: ${reason}`, userId, { escalationLevel: incident.metrics?.escalationLevel, reason });
            await this.notificationService.sendIncidentNotification(updatedIncident, 'escalated');
            await this.auditService.logUserAction(userId, 'escalate', 'incident', id, { reason, escalationLevel: incident.metrics?.escalationLevel });
            this.logger.log('Incident escalated', {
                incidentId: id,
                userId,
                reason,
                escalationLevel: incident.metrics?.escalationLevel,
            });
            return updatedIncident;
        }
        catch (error) {
            this.logger.error('Failed to escalate incident', {
                incidentId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Resolve incident
     * @param id Incident ID
     * @param resolution Resolution details
     * @param userId User resolving the incident
     * @returns Updated incident
     */
    async resolveIncident(id, resolution, userId) {
        try {
            const incident = await this.findById(id);
            if (!incident) {
                throw new common_1.NotFoundException('Incident not found');
            }
            incident.resolve(userId, resolution);
            const updatedIncident = await this.incidentRepository.save(incident);
            await this.addTimelineEntry(id, 'resolved', `Incident resolved: ${resolution}`, userId, {
                timeToResolution: incident.metrics?.timeToResolution,
                slaCompliant: incident.metrics?.slaCompliant,
                resolution,
            });
            await this.notificationService.sendIncidentNotification(updatedIncident, 'resolved');
            await this.auditService.logUserAction(userId, 'resolve', 'incident', id, {
                resolution,
                timeToResolution: incident.metrics?.timeToResolution,
                slaCompliant: incident.metrics?.slaCompliant,
            });
            this.logger.log('Incident resolved', {
                incidentId: id,
                userId,
                timeToResolution: incident.metrics?.timeToResolution,
                slaCompliant: incident.metrics?.slaCompliant,
            });
            return updatedIncident;
        }
        catch (error) {
            this.logger.error('Failed to resolve incident', {
                incidentId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Close incident
     * @param id Incident ID
     * @param userId User closing the incident
     * @returns Updated incident
     */
    async closeIncident(id, userId) {
        try {
            const incident = await this.findById(id);
            if (!incident) {
                throw new common_1.NotFoundException('Incident not found');
            }
            if (incident.status !== 'post_incident') {
                throw new common_1.BadRequestException('Incident must be resolved before closing');
            }
            incident.close(userId);
            const updatedIncident = await this.incidentRepository.save(incident);
            await this.addTimelineEntry(id, 'closed', 'Incident closed', userId);
            await this.notificationService.sendIncidentNotification(updatedIncident, 'closed');
            await this.auditService.logUserAction(userId, 'close', 'incident', id);
            this.logger.log('Incident closed', {
                incidentId: id,
                userId,
            });
            return updatedIncident;
        }
        catch (error) {
            this.logger.error('Failed to close incident', {
                incidentId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Add team member to incident
     * @param id Incident ID
     * @param userId User to add
     * @param role Role of the user
     * @param permissions Permissions for the user
     * @param addedBy User adding the team member
     * @returns Updated incident
     */
    async addTeamMember(id, userId, role, permissions, addedBy) {
        try {
            const incident = await this.findById(id);
            if (!incident) {
                throw new common_1.NotFoundException('Incident not found');
            }
            incident.addTeamMember(userId, role, permissions);
            const updatedIncident = await this.incidentRepository.save(incident);
            await this.addTimelineEntry(id, 'team_member_added', `Team member added: ${role}`, addedBy, { userId, role, permissions });
            await this.auditService.logUserAction(addedBy, 'add_team_member', 'incident', id, { userId, role, permissions });
            this.logger.log('Team member added to incident', {
                incidentId: id,
                userId,
                role,
                addedBy,
            });
            return updatedIncident;
        }
        catch (error) {
            this.logger.error('Failed to add team member', {
                incidentId: id,
                error: error.message,
                userId,
                addedBy,
            });
            throw error;
        }
    }
    /**
     * Get incident statistics
     * @returns Incident statistics
     */
    async getStatistics() {
        try {
            const [totalIncidents, openIncidents, criticalIncidents, overdueIncidents, incidentsByStatus, incidentsBySeverity, averageResolutionTime, slaCompliance,] = await Promise.all([
                this.incidentRepository.count(),
                this.incidentRepository.count({ where: { status: (0, typeorm_2.In)(['new', 'investigating', 'containment', 'eradication', 'recovery']) } }),
                this.incidentRepository.count({ where: { severity: 'critical' } }),
                this.getOverdueIncidentsCount(),
                this.getIncidentsByStatus(),
                this.getIncidentsBySeverity(),
                this.getAverageResolutionTime(),
                this.getSlaCompliance(),
            ]);
            return {
                totalIncidents,
                openIncidents,
                criticalIncidents,
                overdueIncidents,
                byStatus: incidentsByStatus,
                bySeverity: incidentsBySeverity,
                performance: {
                    averageResolutionTime,
                    slaCompliance,
                },
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error('Failed to get incident statistics', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Apply response plan to incident
     * @param incident Incident to apply plan to
     */
    async applyResponsePlan(incident) {
        try {
            const responsePlans = await this.responsePlanRepository.find({
                where: { isActive: true },
            });
            const applicablePlan = responsePlans.find(plan => plan.isApplicableToIncident(incident));
            if (applicablePlan) {
                incident.responsePlanId = applicablePlan.id;
                await this.incidentRepository.save(incident);
                // Start response orchestration
                await this.responseOrchestrationService.executeResponsePlan(incident.id, applicablePlan.id);
                this.logger.log('Response plan applied to incident', {
                    incidentId: incident.id,
                    responsePlanId: applicablePlan.id,
                    planName: applicablePlan.name,
                });
            }
        }
        catch (error) {
            this.logger.warn('Failed to apply response plan', {
                incidentId: incident.id,
                error: error.message,
            });
        }
    }
    /**
     * Add timeline entry
     * @param incidentId Incident ID
     * @param eventType Event type
     * @param description Event description
     * @param userId User who performed the action
     * @param metadata Additional metadata
     */
    async addTimelineEntry(incidentId, eventType, description, userId, metadata) {
        try {
            const timelineEntry = this.timelineRepository.create({
                incidentId,
                eventType,
                description,
                metadata,
                createdBy: userId,
            });
            await this.timelineRepository.save(timelineEntry);
        }
        catch (error) {
            this.logger.warn('Failed to add timeline entry', {
                incidentId,
                eventType,
                error: error.message,
            });
        }
    }
    /**
     * Calculate default priority based on severity
     * @param severity Incident severity
     * @returns Default priority
     */
    calculateDefaultPriority(severity) {
        switch (severity) {
            case 'critical':
                return 'urgent';
            case 'high':
                return 'high';
            case 'medium':
                return 'medium';
            case 'low':
            default:
                return 'low';
        }
    }
    // Helper methods for statistics
    async getOverdueIncidentsCount() {
        // This would implement actual overdue calculation
        // For now, return a placeholder
        return 0;
    }
    async getIncidentsByStatus() {
        const result = await this.incidentRepository
            .createQueryBuilder('incident')
            .select('incident.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .groupBy('incident.status')
            .getRawMany();
        return result.reduce((acc, item) => {
            acc[item.status] = parseInt(item.count);
            return acc;
        }, {});
    }
    async getIncidentsBySeverity() {
        const result = await this.incidentRepository
            .createQueryBuilder('incident')
            .select('incident.severity', 'severity')
            .addSelect('COUNT(*)', 'count')
            .groupBy('incident.severity')
            .getRawMany();
        return result.reduce((acc, item) => {
            acc[item.severity] = parseInt(item.count);
            return acc;
        }, {});
    }
    async getAverageResolutionTime() {
        // This would implement actual calculation
        // For now, return a placeholder
        return 240; // 4 hours in minutes
    }
    async getSlaCompliance() {
        // This would implement actual SLA compliance calculation
        // For now, return a placeholder
        return 85.5; // 85.5% compliance
    }
};
exports.IncidentManagementService = IncidentManagementService;
exports.IncidentManagementService = IncidentManagementService = IncidentManagementService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(incident_entity_1.Incident)),
    __param(1, (0, typeorm_1.InjectRepository)(incident_task_entity_1.IncidentTask)),
    __param(2, (0, typeorm_1.InjectRepository)(incident_evidence_entity_1.IncidentEvidence)),
    __param(3, (0, typeorm_1.InjectRepository)(incident_timeline_entity_1.IncidentTimeline)),
    __param(4, (0, typeorm_1.InjectRepository)(response_plan_entity_1.ResponsePlan)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _d : Object, typeof (_e = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _e : Object, typeof (_f = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _f : Object, typeof (_g = typeof audit_service_1.AuditService !== "undefined" && audit_service_1.AuditService) === "function" ? _g : Object, typeof (_h = typeof notification_service_1.NotificationService !== "undefined" && notification_service_1.NotificationService) === "function" ? _h : Object, typeof (_j = typeof response_orchestration_service_1.ResponseOrchestrationService !== "undefined" && response_orchestration_service_1.ResponseOrchestrationService) === "function" ? _j : Object])
], IncidentManagementService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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