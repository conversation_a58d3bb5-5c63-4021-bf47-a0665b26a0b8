0e722d2d06ad23c7104a643286f84a3f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NormalizedEventStatusChangedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const normalized_event_entity_1 = require("../entities/normalized-event.entity");
/**
 * Normalized Event Status Changed Domain Event
 *
 * Raised when a normalized event's status changes during the normalization process.
 * This event triggers various downstream processes including:
 * - Status monitoring and alerting
 * - Workflow progression
 * - Quality assurance checks
 * - Performance metrics collection
 * - Error handling and retry logic
 */
class NormalizedEventStatusChangedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the previous normalization status
     */
    get oldStatus() {
        return this.eventData.oldStatus;
    }
    /**
     * Get the new normalization status
     */
    get newStatus() {
        return this.eventData.newStatus;
    }
    /**
     * Get the normalization result
     */
    get result() {
        return this.eventData.result;
    }
    /**
     * Get the data quality score
     */
    get dataQualityScore() {
        return this.eventData.dataQualityScore;
    }
    /**
     * Check if the event requires manual review
     */
    get requiresManualReview() {
        return this.eventData.requiresManualReview;
    }
    /**
     * Get the timestamp when status changed
     */
    get timestamp() {
        return this.eventData.timestamp || this.occurredOn;
    }
    /**
     * Check if normalization was completed
     */
    isNormalizationCompleted() {
        return this.newStatus === normalized_event_entity_1.NormalizationStatus.COMPLETED;
    }
    /**
     * Check if normalization failed
     */
    isNormalizationFailed() {
        return this.newStatus === normalized_event_entity_1.NormalizationStatus.FAILED;
    }
    /**
     * Check if normalization was skipped
     */
    isNormalizationSkipped() {
        return this.newStatus === normalized_event_entity_1.NormalizationStatus.SKIPPED;
    }
    /**
     * Check if normalization started
     */
    isNormalizationStarted() {
        return this.oldStatus === normalized_event_entity_1.NormalizationStatus.PENDING &&
            this.newStatus === normalized_event_entity_1.NormalizationStatus.IN_PROGRESS;
    }
    /**
     * Check if the result indicates success
     */
    isResultSuccessful() {
        return this.result?.success === true;
    }
    /**
     * Check if the event has high data quality
     */
    hasHighDataQuality() {
        return (this.dataQualityScore || 0) >= 60;
    }
    /**
     * Get the number of applied rules
     */
    getAppliedRulesCount() {
        return this.result?.appliedRules?.length || 0;
    }
    /**
     * Get the number of failed rules
     */
    getFailedRulesCount() {
        return this.result?.failedRules?.length || 0;
    }
    /**
     * Get the number of errors
     */
    getErrorsCount() {
        return this.result?.errors?.length || 0;
    }
    /**
     * Get the number of warnings
     */
    getWarningsCount() {
        return this.result?.warnings?.length || 0;
    }
    /**
     * Get processing duration
     */
    getProcessingDuration() {
        return this.result?.processingDurationMs || 0;
    }
    /**
     * Get confidence score
     */
    getConfidenceScore() {
        return this.result?.confidenceScore || 0;
    }
    /**
     * Get event summary for handlers
     */
    getEventSummary() {
        return {
            normalizedEventId: this.aggregateId.toString(),
            oldStatus: this.oldStatus,
            newStatus: this.newStatus,
            dataQualityScore: this.dataQualityScore,
            requiresManualReview: this.requiresManualReview,
            isNormalizationCompleted: this.isNormalizationCompleted(),
            isNormalizationFailed: this.isNormalizationFailed(),
            isNormalizationSkipped: this.isNormalizationSkipped(),
            isNormalizationStarted: this.isNormalizationStarted(),
            isResultSuccessful: this.isResultSuccessful(),
            hasHighDataQuality: this.hasHighDataQuality(),
            appliedRulesCount: this.getAppliedRulesCount(),
            failedRulesCount: this.getFailedRulesCount(),
            errorsCount: this.getErrorsCount(),
            warningsCount: this.getWarningsCount(),
            processingDurationMs: this.getProcessingDuration(),
            confidenceScore: this.getConfidenceScore(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventSummary: this.getEventSummary(),
        };
    }
}
exports.NormalizedEventStatusChangedDomainEvent = NormalizedEventStatusChangedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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