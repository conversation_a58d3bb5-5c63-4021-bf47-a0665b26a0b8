609964796306d597a652668fd8aae08b
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
const retry_decorator_1 = require("../../decorators/retry.decorator");
const retry_strategy_1 = require("../../patterns/retry-strategy");
describe('Retry Decorator', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });
    describe('@Retry decorator', () => {
        it('should retry failed operations', async () => {
            var _a;
            let attemptCount = 0;
            class TestService {
                async flakyMethod() {
                    attemptCount++;
                    if (attemptCount < 3) {
                        throw new Error(`Attempt ${attemptCount} failed`);
                    }
                    return 'success';
                }
            }
            __decorate([
                (0, retry_decorator_1.Retry)({ maxAttempts: 3, baseDelay: 10 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "flakyMethod", null);
            const service = new TestService();
            const result = await service.flakyMethod();
            expect(result).toBe('success');
            expect(attemptCount).toBe(3);
        });
        it('should throw error after max attempts exceeded', async () => {
            var _a;
            let attemptCount = 0;
            class TestService {
                async alwaysFailingMethod() {
                    attemptCount++;
                    throw new Error(`Attempt ${attemptCount} failed`);
                }
            }
            __decorate([
                (0, retry_decorator_1.Retry)({ maxAttempts: 2, baseDelay: 10 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "alwaysFailingMethod", null);
            const service = new TestService();
            await expect(service.alwaysFailingMethod()).rejects.toThrow('Attempt 2 failed');
            expect(attemptCount).toBe(2);
        });
        it('should succeed on first attempt when no error occurs', async () => {
            var _a;
            let attemptCount = 0;
            class TestService {
                async successfulMethod() {
                    attemptCount++;
                    return 'immediate success';
                }
            }
            __decorate([
                (0, retry_decorator_1.Retry)({ maxAttempts: 3 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "successfulMethod", null);
            const service = new TestService();
            const result = await service.successfulMethod();
            expect(result).toBe('immediate success');
            expect(attemptCount).toBe(1);
        });
        it('should use custom retry strategy', async () => {
            var _a;
            let attemptCount = 0;
            const mockStrategy = {
                execute: jest.fn().mockImplementation(async (fn) => {
                    attemptCount++;
                    return await fn();
                }),
            };
            class TestService {
                constructor() {
                    this.retryStrategy = mockStrategy;
                }
                async methodWithCustomStrategy() {
                    return 'custom strategy result';
                }
            }
            __decorate([
                (0, retry_decorator_1.Retry)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "methodWithCustomStrategy", null);
            const service = new TestService();
            const result = await service.methodWithCustomStrategy();
            expect(result).toBe('custom strategy result');
            expect(mockStrategy.execute).toHaveBeenCalledTimes(1);
        });
        it('should respect retryOn condition', async () => {
            var _a;
            let attemptCount = 0;
            class CustomError extends Error {
                constructor(message) {
                    super(message);
                    this.name = 'CustomError';
                }
            }
            class TestService {
                async selectiveRetryMethod(shouldRetry) {
                    attemptCount++;
                    if (shouldRetry) {
                        throw new CustomError('Retryable error');
                    }
                    else {
                        throw new Error('Non-retryable error');
                    }
                }
            }
            __decorate([
                (0, retry_decorator_1.Retry)({
                    maxAttempts: 3,
                    baseDelay: 10,
                    retryOn: (error) => error instanceof CustomError
                }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [Boolean]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "selectiveRetryMethod", null);
            const service = new TestService();
            // Reset attempt count
            attemptCount = 0;
            // Non-retryable error should fail immediately
            await expect(service.selectiveRetryMethod(false)).rejects.toThrow('Non-retryable error');
            expect(attemptCount).toBe(1);
            // Reset attempt count
            attemptCount = 0;
            // Retryable error should be retried
            await expect(service.selectiveRetryMethod(true)).rejects.toThrow('Retryable error');
            expect(attemptCount).toBe(3);
        });
        it('should apply exponential backoff delays', async () => {
            var _a;
            let attemptCount = 0;
            const delays = [];
            const originalSetTimeout = global.setTimeout;
            // Mock setTimeout to capture delays
            global.setTimeout = jest.fn().mockImplementation((callback, delay) => {
                delays.push(delay);
                return originalSetTimeout(callback, 0); // Execute immediately for test
            });
            class TestService {
                async delayTestMethod() {
                    attemptCount++;
                    if (attemptCount < 3) {
                        throw new Error('Retry needed');
                    }
                    return 'success';
                }
            }
            __decorate([
                (0, retry_decorator_1.Retry)({
                    maxAttempts: 3,
                    baseDelay: 100,
                    backoffMultiplier: 2,
                    jitter: false // Disable jitter for predictable delays
                }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "delayTestMethod", null);
            const service = new TestService();
            await service.delayTestMethod();
            expect(delays).toHaveLength(2); // Two retries
            expect(delays[0]).toBe(100); // First retry: baseDelay
            expect(delays[1]).toBe(200); // Second retry: baseDelay * 2
            // Restore original setTimeout
            global.setTimeout = originalSetTimeout;
        });
    });
    describe('@ExponentialBackoff decorator', () => {
        it('should apply exponential backoff with default settings', async () => {
            var _a;
            let attemptCount = 0;
            class TestService {
                async exponentialMethod() {
                    attemptCount++;
                    if (attemptCount < 3) {
                        throw new Error('Retry needed');
                    }
                    return 'exponential success';
                }
            }
            __decorate([
                (0, retry_decorator_1.ExponentialBackoff)(3, 50),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "exponentialMethod", null);
            const service = new TestService();
            const result = await service.exponentialMethod();
            expect(result).toBe('exponential success');
            expect(attemptCount).toBe(3);
        });
        it('should use default parameters when not specified', async () => {
            var _a;
            let attemptCount = 0;
            class TestService {
                async defaultExponentialMethod() {
                    attemptCount++;
                    if (attemptCount < 2) {
                        throw new Error('Retry needed');
                    }
                    return 'default exponential success';
                }
            }
            __decorate([
                (0, retry_decorator_1.ExponentialBackoff)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "defaultExponentialMethod", null);
            const service = new TestService();
            const result = await service.defaultExponentialMethod();
            expect(result).toBe('default exponential success');
            expect(attemptCount).toBe(2);
        });
    });
    describe('@LinearBackoff decorator', () => {
        it('should apply linear backoff', async () => {
            var _a;
            let attemptCount = 0;
            class TestService {
                async linearMethod() {
                    attemptCount++;
                    if (attemptCount < 3) {
                        throw new Error('Retry needed');
                    }
                    return 'linear success';
                }
            }
            __decorate([
                (0, retry_decorator_1.LinearBackoff)(3, 100),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "linearMethod", null);
            const service = new TestService();
            const result = await service.linearMethod();
            expect(result).toBe('linear success');
            expect(attemptCount).toBe(3);
        });
    });
    describe('@RetryOnError decorator', () => {
        it('should only retry on specified error types', async () => {
            var _a;
            let attemptCount = 0;
            class NetworkError extends Error {
                constructor(message) {
                    super(message);
                    this.name = 'NetworkError';
                }
            }
            class ValidationError extends Error {
                constructor(message) {
                    super(message);
                    this.name = 'ValidationError';
                }
            }
            class TestService {
                async errorSpecificMethod(errorType) {
                    attemptCount++;
                    if (errorType === 'network') {
                        throw new NetworkError('Network failed');
                    }
                    else {
                        throw new ValidationError('Validation failed');
                    }
                }
            }
            __decorate([
                (0, retry_decorator_1.RetryOnError)([NetworkError], { maxAttempts: 3, baseDelay: 10 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "errorSpecificMethod", null);
            const service = new TestService();
            // Reset attempt count
            attemptCount = 0;
            // NetworkError should be retried
            await expect(service.errorSpecificMethod('network')).rejects.toThrow('Network failed');
            expect(attemptCount).toBe(3);
            // Reset attempt count
            attemptCount = 0;
            // ValidationError should not be retried
            await expect(service.errorSpecificMethod('validation')).rejects.toThrow('Validation failed');
            expect(attemptCount).toBe(1);
        });
        it('should work with multiple error types', async () => {
            var _a;
            let attemptCount = 0;
            class NetworkError extends Error {
                constructor(message) {
                    super(message);
                    this.name = 'NetworkError';
                }
            }
            class TimeoutError extends Error {
                constructor(message) {
                    super(message);
                    this.name = 'TimeoutError';
                }
            }
            class TestService {
                async multiErrorMethod(errorType) {
                    attemptCount++;
                    switch (errorType) {
                        case 'network':
                            throw new NetworkError('Network error');
                        case 'timeout':
                            throw new TimeoutError('Timeout error');
                        default:
                            throw new Error('Other error');
                    }
                }
            }
            __decorate([
                (0, retry_decorator_1.RetryOnError)([NetworkError, TimeoutError], { maxAttempts: 2, baseDelay: 10 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "multiErrorMethod", null);
            const service = new TestService();
            // NetworkError should be retried
            attemptCount = 0;
            await expect(service.multiErrorMethod('network')).rejects.toThrow('Network error');
            expect(attemptCount).toBe(2);
            // TimeoutError should be retried
            attemptCount = 0;
            await expect(service.multiErrorMethod('timeout')).rejects.toThrow('Timeout error');
            expect(attemptCount).toBe(2);
            // Other error should not be retried
            attemptCount = 0;
            await expect(service.multiErrorMethod('other')).rejects.toThrow('Other error');
            expect(attemptCount).toBe(1);
        });
    });
    describe('integration with RetryStrategy', () => {
        it('should work with custom RetryStrategy instance', async () => {
            var _a;
            let attemptCount = 0;
            const customStrategy = new retry_strategy_1.RetryStrategy({
                maxAttempts: 4,
                baseDelay: 25,
                backoffMultiplier: 1.5,
                jitter: false,
            });
            class TestService {
                async customStrategyMethod() {
                    attemptCount++;
                    if (attemptCount < 4) {
                        throw new Error('Custom strategy retry');
                    }
                    return 'custom strategy success';
                }
            }
            __decorate([
                (0, retry_decorator_1.Retry)({ strategy: customStrategy }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "customStrategyMethod", null);
            const service = new TestService();
            const result = await service.customStrategyMethod();
            expect(result).toBe('custom strategy success');
            expect(attemptCount).toBe(4);
        });
    });
    describe('error handling edge cases', () => {
        it('should handle synchronous errors', async () => {
            var _a;
            let attemptCount = 0;
            class TestService {
                async syncErrorMethod() {
                    attemptCount++;
                    throw new Error('Synchronous error');
                }
            }
            __decorate([
                (0, retry_decorator_1.Retry)({ maxAttempts: 2, baseDelay: 10 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "syncErrorMethod", null);
            const service = new TestService();
            await expect(service.syncErrorMethod()).rejects.toThrow('Synchronous error');
            expect(attemptCount).toBe(2);
        });
        it('should preserve error stack traces', async () => {
            var _a;
            class TestService {
                async stackTraceMethod() {
                    const error = new Error('Stack trace test');
                    error.stack = 'Original stack trace';
                    throw error;
                }
            }
            __decorate([
                (0, retry_decorator_1.Retry)({ maxAttempts: 2, baseDelay: 10 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "stackTraceMethod", null);
            const service = new TestService();
            try {
                await service.stackTraceMethod();
                fail('Should have thrown error');
            }
            catch (error) {
                expect(error).toBeInstanceOf(Error);
                expect(error.message).toBe('Stack trace test');
                expect(error.stack).toBe('Original stack trace');
            }
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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