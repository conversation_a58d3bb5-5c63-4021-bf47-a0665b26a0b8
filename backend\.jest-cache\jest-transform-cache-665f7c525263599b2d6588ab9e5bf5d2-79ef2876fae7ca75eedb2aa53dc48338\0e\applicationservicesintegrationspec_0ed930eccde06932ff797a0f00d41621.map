{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\application-services.integration.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,4GAAuG;AACvG,oGAA+F;AAK/F,wEAAoE;AAIpE,iFAA6E;AAC7E,mFAA+E;AAC/E,iGAA6F;AAC7F,qGAAgG;AAChG,0GAA4F;AAC5F,0GAA4F;AAC5F,sHAAwG;AACxG,8GAAgG;AAGhG,uHAAsG;AACtG,yHAAwG;AACxG,mHAAkG;AAIlG,wEAA+D;AAC/D,gFAAuE;AACvE,4EAAmE;AACnE,sFAA4E;AAC5E,kFAAyE;AACzE,gGAAuF;AACvF,0EAAiE;AACjE,8EAAqE;AACrE,yHAAuG;AACvG,qHAAoG;AACpG,2GAA0F;AAC1F,uGAAsF;AACtF,sHAAiH;AAGjH;;GAEG;AACH,MAAM,mBAAmB;IAAzB;QACU,WAAM,GAAuB,IAAI,GAAG,EAAE,CAAC;QACvC,eAAU,GAAG,KAAK,CAAC;IA0D7B,CAAC;IAxDC,aAAa,CAAC,IAAa;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,KAAY;QACrB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,IAAI,2DAA2B,CAAC,sBAAsB,CAAC,CAAC;QAChE,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAkB;QAC/B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,IAAI,2DAA2B,CAAC,sBAAsB,CAAC,CAAC;QAChE,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAkB;QAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAW,EAAE,GAAS;QAC1C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACrD,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAC3C,OAAO,SAAS,IAAI,KAAK,IAAI,SAAS,IAAI,GAAG,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAmB;QACpC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACrD,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAC5B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAuB;QAC1C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACrD,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAC5B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,YAAoB,EAAE,UAAwB,EAAE,WAA2B;QACxG,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,CAAC;QACvD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACrD,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAC3C,OAAO,SAAS,IAAI,UAAU,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK;QACH,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;CACF;AAED,MAAM,oBAAoB;IAA1B;QACU,YAAO,GAAwB,IAAI,GAAG,EAAE,CAAC;IAiCnD,CAAC;IA/BC,KAAK,CAAC,IAAI,CAAC,MAAc;QACvB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAkB;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAkB;QAC7B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAwB;QAC3C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAC7B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAmB;QACtC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CACpD,CAAC;IACJ,CAAC;IAED,KAAK;QACH,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;CACF;AAED,MAAM,2BAA2B;IAAjC;QACU,oBAAe,GAA+B,IAAI,GAAG,EAAE,CAAC;IAiClE,CAAC;IA/BC,KAAK,CAAC,IAAI,CAAC,aAA4B;QACrC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,aAAa,CAAC,CAAC;IACvE,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAkB;QAC/B,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAkB;QAC7B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAA+B;QAClD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAC7D,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAC3B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAU;QACzB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAC7D,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CACpD,CAAC;IACJ,CAAC;IAED,KAAK;QACH,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;CACF;AAED,MAAM,4BAA4B;IAAlC;QACU,YAAO,GAAgC,IAAI,GAAG,EAAE,CAAC;IAiC3D,CAAC;IA/BC,KAAK,CAAC,IAAI,CAAC,MAAsB;QAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAkB;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAkB;QAC7B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAoB;QACrC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,MAAM,KAAK,MAAM,CACzB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAgB;QAC/B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,IAAI,KAAK,IAAI,CACrB,CAAC;IACJ,CAAC;IAED,KAAK;QACH,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;CACF;AAED,MAAM,kBAAkB;IAAxB;QACU,eAAU,GAAG,KAAK,CAAC;QACnB,oBAAe,GAAG,CAAC,CAAC;IA0C9B,CAAC;IAxCC,aAAa,CAAC,IAAa;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;IAED,kBAAkB,CAAC,KAAa;QAC9B,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAY,EAAE,OAAY;QAC3C,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,IAAI,2DAA2B,CAAC,sCAAsC,CAAC,CAAC;QAChF,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC5B,eAAe,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC;YACrD,QAAQ,EAAE,IAAI,CAAC,eAAe,IAAI,GAAG;YACrC,eAAe,EAAE,IAAI;YACrB,aAAa,EAAE,IAAI;YACnB,gBAAgB,EAAE,EAAE;YACpB,MAAM,EAAE,EAAE;SACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAe,EAAE,OAAY;QAC/C,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC/B,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CACvD,CAAC;QACF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,MAAM,CAAC,MAAM;YAC1B,eAAe,EAAE,OAAO,CAAC,MAAM;YAC/B,OAAO;SACR,CAAC;IACJ,CAAC;CACF;AAED,MAAM,kBAAkB;IAAxB;QACU,eAAU,GAAG,KAAK,CAAC;QACnB,mBAAc,GAAG,CAAC,CAAC;IAiD7B,CAAC;IA/CC,aAAa,CAAC,IAAa;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;IAED,iBAAiB,CAAC,KAAa;QAC7B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAY,EAAE,OAAY;QAC3C,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,IAAI,2DAA2B,CAAC,sCAAsC,CAAC,CAAC;QAChF,CAAC;QAED,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,KAAK,mCAAa,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ;YAChG,CAAC,CAAC,CAAC;oBACC,EAAE,EAAE,8CAAc,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;oBACtC,QAAQ,EAAE,qCAAc,CAAC,IAAI;oBAC7B,UAAU,EAAE,EAAE;oBACd,SAAS,EAAE,qBAAqB;oBAChC,UAAU,EAAE,CAAC,oBAAoB,CAAC;iBACnC,CAAC;YACJ,CAAC,CAAC,EAAE,CAAC;QAEP,OAAO;YACL,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC5B,OAAO;YACP,UAAU,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACxC,YAAY,EAAE,IAAI,CAAC,cAAc,IAAI,EAAE;YACvC,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC;YAC9C,eAAe,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,yBAAyB,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE;SAC1F,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAe,EAAE,OAAY;QAC/C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAChC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CACvD,CAAC;QACF,OAAO;YACL,WAAW,EAAE,MAAM,CAAC,MAAM;YAC1B,QAAQ;YACR,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;SACpD,CAAC;IACJ,CAAC;CACF;AAED,MAAM,wBAAwB;IAA9B;QACU,eAAU,GAAG,KAAK,CAAC;IA6B7B,CAAC;IA3BC,aAAa,CAAC,IAAa;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,KAAY,EAAE,OAAY;QACxC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,IAAI,2DAA2B,CAAC,mCAAmC,CAAC,CAAC;QAC7E,CAAC;QAED,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,KAAK,2BAAS,CAAC,iBAAiB;YAChE,CAAC,CAAC,CAAC;oBACC,EAAE,EAAE,8CAAc,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;oBACtC,QAAQ,EAAE,mDAAqB,CAAC,MAAM;oBACtC,KAAK,EAAE,eAAe;oBACtB,KAAK,EAAE,GAAG;oBACV,WAAW,EAAE,gCAAgC;iBAC9C,CAAC;YACJ,CAAC,CAAC,EAAE,CAAC;QAEP,OAAO;YACL,MAAM,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;YAClD,eAAe;YACf,YAAY,EAAE,EAAE;YAChB,QAAQ,EAAE,OAAO;YACjB,QAAQ,EAAE,eAAe,CAAC,MAAM;SACjC,CAAC;IACJ,CAAC;CACF;AAED,MAAM,oBAAoB;IAA1B;QACU,eAAU,GAAG,KAAK,CAAC;QACnB,mBAAc,GAAG,CAAC,CAAC;IAoC7B,CAAC;IAlCC,aAAa,CAAC,IAAa;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;IAED,iBAAiB,CAAC,KAAa;QAC7B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAY,EAAE,OAAY;QAC9C,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,IAAI,2DAA2B,CAAC,+BAA+B,CAAC,CAAC;QACzE,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,eAAe,EAAE,MAAM,GAAG,CAAC;YAChF,CAAC,CAAC,CAAC;oBACC,IAAI,EAAE,6BAAU,CAAC,QAAQ;oBACzB,MAAM,EAAE,iCAAY,CAAC,SAAS;oBAC9B,UAAU,EAAE,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE;iBAC3D,CAAC;YACJ,CAAC,CAAC,EAAE,CAAC;QAEP,OAAO;YACL,UAAU,EAAE,8CAAc,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;YAC9C,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC1B,eAAe,EAAE,OAAO,CAAC,MAAM;YAC/B,OAAO;YACP,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI,CAAC,cAAc,IAAI,GAAG;SACrC,CAAC;IACJ,CAAC;CACF;AAED,QAAQ,CAAC,wCAAwC,EAAE,GAAG,EAAE;IACtD,IAAI,MAAqB,CAAC;IAC1B,IAAI,mBAAgD,CAAC;IACrD,IAAI,uBAAgD,CAAC;IACrD,IAAI,eAAoC,CAAC;IACzC,IAAI,gBAAsC,CAAC;IAC3C,IAAI,uBAAoD,CAAC;IACzD,IAAI,wBAAsD,CAAC;IAC3D,IAAI,cAAkC,CAAC;IACvC,IAAI,cAAkC,CAAC;IACvC,IAAI,oBAA8C,CAAC;IACnD,IAAI,gBAAsC,CAAC;IAE3C,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,wBAAwB;QACxB,eAAe,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC5C,gBAAgB,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC9C,uBAAuB,GAAG,IAAI,2BAA2B,EAAE,CAAC;QAC5D,wBAAwB,GAAG,IAAI,4BAA4B,EAAE,CAAC;QAC9D,cAAc,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAC1C,cAAc,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAC1C,oBAAoB,GAAG,IAAI,wBAAwB,EAAE,CAAC;QACtD,gBAAgB,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAE9C,MAAM,GAAG,MAAM,cAAI,CAAC,mBAAmB,CAAC;YACtC,SAAS,EAAE;gBACT,2DAA2B;gBAC3B,mDAAuB;gBACvB,EAAE,OAAO,EAAE,kCAAe,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACvD,EAAE,OAAO,EAAE,oCAAgB,EAAE,QAAQ,EAAE,gBAAgB,EAAE;gBACzD,EAAE,OAAO,EAAE,kDAAuB,EAAE,QAAQ,EAAE,uBAAuB,EAAE;gBACvE,EAAE,OAAO,EAAE,qDAAwB,EAAE,QAAQ,EAAE,wBAAwB,EAAE;gBACzE,EAAE,OAAO,EAAE,0CAAc,EAAE,QAAQ,EAAE,cAAc,EAAE;gBACrD,EAAE,OAAO,EAAE,0CAAc,EAAE,QAAQ,EAAE,cAAc,EAAE;gBACrD,EAAE,OAAO,EAAE,sDAAoB,EAAE,QAAQ,EAAE,oBAAoB,EAAE;gBACjE,EAAE,OAAO,EAAE,8CAAgB,EAAE,QAAQ,EAAE,gBAAgB,EAAE;aAC1D;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAA8B,2DAA2B,CAAC,CAAC;QAC3F,uBAAuB,GAAG,MAAM,CAAC,GAAG,CAA0B,mDAAuB,CAAC,CAAC;IACzF,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,eAAe,CAAC,KAAK,EAAE,CAAC;QACxB,gBAAgB,CAAC,KAAK,EAAE,CAAC;QACzB,uBAAuB,CAAC,KAAK,EAAE,CAAC;QAChC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACjC,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iDAAiD,EAAE,GAAG,EAAE;QAC/D,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,UAAU;YACV,MAAM,QAAQ,GAAG,iCAAQ,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,6BAAM,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,2CAAa,CAAC,MAAM,EAAE,CAAC;YAE7C,MAAM,MAAM,GAAG;gBACb,4BAAY,CAAC,MAAM,CAAC;oBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;oBACtE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;oBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;wBACzB,IAAI,EAAE,wCAAe,CAAC,QAAQ;wBAC9B,UAAU,EAAE,QAAQ;wBACpB,IAAI,EAAE,UAAU;qBACjB,CAAC;oBACF,IAAI,EAAE,2BAAS,CAAC,iBAAiB;oBACjC,QAAQ,EAAE,mCAAa,CAAC,IAAI;oBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;oBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE;oBACtC,QAAQ;oBACR,MAAM;iBACP,CAAC;gBACF,4BAAY,CAAC,MAAM,CAAC;oBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;oBACjE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;oBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;wBACzB,IAAI,EAAE,wCAAe,CAAC,GAAG;wBACzB,UAAU,EAAE,SAAS;wBACrB,IAAI,EAAE,KAAK;qBACZ,CAAC;oBACF,IAAI,EAAE,2BAAS,CAAC,gBAAgB;oBAChC,QAAQ,EAAE,mCAAa,CAAC,QAAQ;oBAChC,MAAM,EAAE,+BAAW,CAAC,QAAQ;oBAC5B,OAAO,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE;oBAClC,QAAQ;oBACR,MAAM;iBACP,CAAC;aACH,CAAC;YAEF,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,MAAM,EAAE;gBAC3E,QAAQ,EAAE,QAAQ,CAAC,KAAK;gBACxB,MAAM,EAAE,MAAM,CAAC,KAAK;gBACpB,aAAa,EAAE,aAAa,CAAC,KAAK;gBAClC,QAAQ,EAAE,MAAa;gBACvB,YAAY,EAAE,IAAI;gBAClB,mBAAmB,EAAE,IAAI;gBACzB,2BAA2B,EAAE,IAAI;gBACjC,cAAc,EAAE;oBACd,qBAAqB,EAAE,IAAI;oBAC3B,qBAAqB,EAAE,IAAI;oBAC3B,2BAA2B,EAAE,IAAI;oBACjC,uBAAuB,EAAE,IAAI;oBAC7B,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;oBACtB,SAAS,EAAE,CAAC;oBACZ,uBAAuB,EAAE,CAAC;oBAC1B,aAAa,EAAE,CAAC;oBAChB,SAAS,EAAE,KAAK;iBACjB;aACF,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE5D,8BAA8B;YAC9B,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACxD,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yEAAyE,EAAE,KAAK,IAAI,EAAE;YACvF,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAClE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,cAAc;gBAC9B,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,SAAS,EAAE,qBAAqB,EAAE;gBAC7C,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,uDAAuD;YACvD,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAEpC,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,qBAAqB,CAAC,CAAC,KAAK,CAAC,EAAE;gBACtE,QAAQ,EAAE,MAAa;gBACvB,SAAS,EAAE,KAAK;aACjB,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oCAAoC;YAC3E,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE3C,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,gCAAgC;YAC9E,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,UAAU;YACV,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACjD,4BAAY,CAAC,MAAM,CAAC;gBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACzE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,OAAO,CAAC,EAAE;oBACtB,IAAI,EAAE,OAAO,CAAC,EAAE;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAa,CAAC,MAAM;gBACjE,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;gBACvB,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CACH,CAAC;YAEF,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,MAAM,EAAE;gBAC3E,QAAQ,EAAE,QAAe;gBACzB,cAAc,EAAE;oBACd,qBAAqB,EAAE,IAAI;oBAC3B,qBAAqB,EAAE,IAAI;oBAC3B,2BAA2B,EAAE,IAAI;oBACjC,uBAAuB,EAAE,IAAI;oBAC7B,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;oBACtB,SAAS,EAAE,CAAC;oBACZ,uBAAuB,EAAE,CAAC;oBAC1B,aAAa,EAAE,CAAC;oBAChB,SAAS,EAAE,KAAK;iBACjB;aACF,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrD,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAE/D,8CAA8C;YAC9C,mBAAmB,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBACjD,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;YAEH,+CAA+C;YAC/C,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,mCAAa,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;YACvF,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAClE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,cAAc;gBAC9B,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;gBAC9B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,iCAAiC;YACjC,cAAc,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YACvC,cAAc,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YACtC,gBAAgB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YAExC,wCAAwC;YACxC,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC5E,QAAQ,EAAE,MAAa;gBACvB,SAAS,EAAE,EAAE,EAAE,qBAAqB;aACrC,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,mEAAmE;YACnE,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAClE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,iBAAiB;gBACjC,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACtC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,oCAAoC;YACpC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAEnC,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC5E,QAAQ,EAAE,MAAa;gBACvB,YAAY,EAAE,IAAI;aACnB,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,6DAA6D;YAC7D,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpE,2CAA2C;YAC3C,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC7D,MAAM,oBAAoB,GAAG,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAC/D,CAAC,CAAC,KAAK,KAAK,kBAAkB,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CACzE,CAAC;YACF,MAAM,CAAC,oBAAoB,CAAC,CAAC,WAAW,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,UAAU;YACV,MAAM,MAAM,GAAG;gBACb,4BAAY,CAAC,MAAM,CAAC;oBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;oBACtE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;oBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;wBACzB,IAAI,EAAE,wCAAe,CAAC,QAAQ;wBAC9B,UAAU,EAAE,QAAQ;wBACpB,IAAI,EAAE,UAAU;qBACjB,CAAC;oBACF,IAAI,EAAE,2BAAS,CAAC,iBAAiB;oBACjC,QAAQ,EAAE,mCAAa,CAAC,QAAQ;oBAChC,MAAM,EAAE,+BAAW,CAAC,QAAQ;oBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE;oBACtC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC;aACH,CAAC;YAEF,sCAAsC;YACtC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACnC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACnC,oBAAoB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAEzC,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,MAAM,EAAE;gBAC3E,QAAQ,EAAE,UAAiB;gBAC3B,YAAY,EAAE,KAAK;aACpB,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,8CAA8C;YAC9C,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE7D,6CAA6C;YAC7C,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,sCAAsC;QAC7F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAClE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;gBACjC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,oBAAoB,GAAG,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9E,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;gBAClF,SAAS,EAAE,CAAC;gBACZ,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC;oBACnB,MAAM,IAAI,2DAA2B,CAAC,mBAAmB,CAAC,CAAC;gBAC7D,CAAC;gBACD,OAAO,oBAAoB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,qBAAqB,CAAC,CAAC,KAAK,CAAC,EAAE;gBACtE,QAAQ,EAAE,QAAe;gBACzB,cAAc,EAAE;oBACd,qBAAqB,EAAE,IAAI;oBAC3B,qBAAqB,EAAE,KAAK;oBAC5B,2BAA2B,EAAE,KAAK;oBAClC,uBAAuB,EAAE,KAAK;oBAC9B,iBAAiB,EAAE,KAAK;oBACxB,gBAAgB,EAAE,KAAK;oBACvB,SAAS,EAAE,CAAC;oBACZ,uBAAuB,EAAE,CAAC;oBAC1B,aAAa,EAAE,CAAC;oBAChB,SAAS,EAAE,KAAK;iBACjB;aACF,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE3C,+BAA+B;YAC/B,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,UAAU;YACV,MAAM,UAAU,GAAG,GAAG,CAAC;YACvB,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACzD,4BAAY,CAAC,MAAM,CAAC;gBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACzE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,OAAO,CAAC,EAAE;oBACtB,IAAI,EAAE,OAAO,CAAC,EAAE;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;gBACvB,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CACH,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,MAAM,EAAE;gBAC3E,QAAQ,EAAE,QAAe;gBACzB,cAAc,EAAE;oBACd,qBAAqB,EAAE,IAAI;oBAC3B,qBAAqB,EAAE,IAAI;oBAC3B,2BAA2B,EAAE,KAAK;oBAClC,uBAAuB,EAAE,KAAK;oBAC9B,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;oBACtB,SAAS,EAAE,EAAE;oBACb,uBAAuB,EAAE,CAAC;oBAC1B,aAAa,EAAE,CAAC;oBAChB,SAAS,EAAE,MAAM;iBAClB;aACF,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAG,OAAO,GAAG,SAAS,CAAC;YAEtC,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7D,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAEvE,yBAAyB;YACzB,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,oCAAoC;YAC3E,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAExD,oBAAoB;YACpB,MAAM,UAAU,GAAG,UAAU,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,oBAAoB;YACxE,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,gCAAgC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,UAAU;YACV,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACjD,4BAAY,CAAC,MAAM,CAAC;gBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACxE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,aAAa,CAAC,EAAE;oBAC5B,IAAI,EAAE,aAAa,CAAC,EAAE;iBACvB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,cAAc;gBAC9B,QAAQ,EAAE,mCAAa,CAAC,GAAG;gBAC3B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE;gBACxC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CACH,CAAC;YAEF,6BAA6B;YAC7B,MAAM,UAAU,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAC/B,MAAM,OAAO,GAAG,EAAE,CAAC;YAEnB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACnC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAE7B,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,MAAM,EAAE;oBAC3E,QAAQ,EAAE,KAAY;oBACtB,cAAc,EAAE;wBACd,qBAAqB,EAAE,IAAI;wBAC3B,qBAAqB,EAAE,KAAK;wBAC5B,2BAA2B,EAAE,KAAK;wBAClC,uBAAuB,EAAE,KAAK;wBAC9B,iBAAiB,EAAE,KAAK;wBACxB,gBAAgB,EAAE,KAAK;wBACvB,SAAS;wBACT,uBAAuB,EAAE,CAAC;wBAC1B,aAAa,EAAE,CAAC;wBAChB,SAAS,EAAE,KAAK;qBACjB;iBACF,CAAC,CAAC;gBAEH,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;gBAErC,OAAO,CAAC,IAAI,CAAC;oBACX,SAAS;oBACT,QAAQ;oBACR,OAAO,EAAE,MAAM,CAAC,SAAS,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,OAAO;oBACxD,eAAe,EAAE,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;iBAC5E,CAAC,CAAC;YACL,CAAC;YAED,SAAS;YACT,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACvB,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YAEH,8DAA8D;YAC9D,MAAM,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;YAC5E,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;YACzE,UAAU;YACV,MAAM,QAAQ,GAAG,iCAAQ,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,6BAAM,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,2CAAa,CAAC,MAAM,EAAE,CAAC;YAE7C,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACxE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,WAAW;oBACvB,IAAI,EAAE,gBAAgB;iBACvB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,cAAc;gBAC9B,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,SAAS,EAAE,YAAY,EAAE;gBACpC,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC5E,QAAQ,EAAE,QAAQ,CAAC,KAAK;gBACxB,MAAM,EAAE,MAAM,CAAC,KAAK;gBACpB,aAAa,EAAE,aAAa,CAAC,KAAK;gBAClC,QAAQ,EAAE,MAAa;gBACvB,YAAY,EAAE,IAAI;aACnB,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,yCAAyC;YACzC,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1D,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAExD,+BAA+B;YAC/B,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;YAC3E,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,iBAAiB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAC7E,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,gBAAgB;oBAC5B,IAAI,EAAE,qBAAqB;iBAC5B,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,WAAW;gBAC3B,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE;oBACP,QAAQ,EAAE,KAAK;oBACf,WAAW,EAAE,IAAI;oBACjB,aAAa,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;iBAClC;gBACD,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC5E,QAAQ,EAAE,UAAiB;gBAC3B,YAAY,EAAE,IAAI;gBAClB,WAAW,EAAE;oBACX;wBACE,EAAE,EAAE,kBAAkB;wBACtB,IAAI,EAAE,sBAAsB;wBAC5B,WAAW,EAAE,sCAAsC;wBACnD,SAAS,EAAE;4BACT,UAAU,EAAE,CAAC,2BAAS,CAAC,WAAW,CAAC;4BACnC,cAAc,EAAE,CAAC,mCAAa,CAAC,QAAQ,CAAC;yBACzC;wBACD,MAAM,EAAE,cAAqB;wBAC7B,QAAQ,EAAE,CAAC;wBACX,OAAO,EAAE,IAAI;qBACd;iBACF;aACF,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEpD,uEAAuE;YACvE,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE5D,6CAA6C;YAC7C,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1D,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\application-services.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { SecurityOrchestratorService } from '../../application/services/security-orchestrator.service';\r\nimport { ThreatAssessmentService } from '../../application/services/threat-assessment.service';\r\nimport { Event } from '../../domain/entities/event.entity';\r\nimport { Threat } from '../../domain/entities/threat.entity';\r\nimport { Vulnerability } from '../../domain/entities/vulnerability.entity';\r\nimport { ResponseAction } from '../../domain/entities/response-action.entity';\r\nimport { EventFactory } from '../../domain/factories/event.factory';\r\nimport { ThreatFactory } from '../../domain/factories/threat.factory';\r\nimport { VulnerabilityFactory } from '../../domain/factories/vulnerability.factory';\r\nimport { ResponseActionFactory } from '../../domain/factories/response-action.factory';\r\nimport { EventRepository } from '../../domain/repositories/event.repository';\r\nimport { ThreatRepository } from '../../domain/repositories/threat.repository';\r\nimport { VulnerabilityRepository } from '../../domain/repositories/vulnerability.repository';\r\nimport { ResponseActionRepository } from '../../domain/repositories/response-action.repository';\r\nimport { EventProcessor } from '../../domain/interfaces/services/event-processor.interface';\r\nimport { ThreatDetector } from '../../domain/interfaces/services/threat-detector.interface';\r\nimport { VulnerabilityScanner } from '../../domain/interfaces/services/vulnerability-scanner.interface';\r\nimport { ResponseExecutor } from '../../domain/interfaces/services/response-executor.interface';\r\nimport { SecurityPolicy } from '../../domain/policies/security-policy';\r\nimport { CompliancePolicy } from '../../domain/policies/compliance-policy';\r\nimport { EventMetadata } from '../../domain/value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../domain/value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../domain/value-objects/event-metadata/event-source.value-object';\r\nimport { IpAddress } from '../../domain/value-objects/network/ip-address.value-object';\r\nimport { Port } from '../../domain/value-objects/network/port.value-object';\r\nimport { CvssScore } from '../../domain/value-objects/threat-indicators/cvss-score.value-object';\r\nimport { EventType } from '../../domain/enums/event-type.enum';\r\nimport { EventSeverity } from '../../domain/enums/event-severity.enum';\r\nimport { EventStatus } from '../../domain/enums/event-status.enum';\r\nimport { EventSourceType } from '../../domain/enums/event-source-type.enum';\r\nimport { ThreatSeverity } from '../../domain/enums/threat-severity.enum';\r\nimport { VulnerabilitySeverity } from '../../domain/enums/vulnerability-severity.enum';\r\nimport { ActionType } from '../../domain/enums/action-type.enum';\r\nimport { ActionStatus } from '../../domain/enums/action-status.enum';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { CorrelationId } from '../../../../shared-kernel/value-objects/correlation-id.value-object';\r\nimport { TenantId } from '../../../../shared-kernel/value-objects/tenant-id.value-object';\r\nimport { UserId } from '../../../../shared-kernel/value-objects/user-id.value-object';\r\nimport { ServiceUnavailableException } from '../../../../shared-kernel/exceptions/service-unavailable.exception';\r\nimport { ConflictException } from '../../../../shared-kernel/exceptions/conflict.exception';\r\n\r\n/**\r\n * Mock implementations for application service testing\r\n */\r\nclass MockEventRepository implements EventRepository {\r\n  private events: Map<string, Event> = new Map();\r\n  private shouldFail = false;\r\n\r\n  setShouldFail(fail: boolean): void {\r\n    this.shouldFail = fail;\r\n  }\r\n\r\n  async save(event: Event): Promise<void> {\r\n    if (this.shouldFail) {\r\n      throw new ServiceUnavailableException('Database unavailable');\r\n    }\r\n    this.events.set(event.id.toString(), event);\r\n  }\r\n\r\n  async findById(id: UniqueEntityId): Promise<Event | null> {\r\n    if (this.shouldFail) {\r\n      throw new ServiceUnavailableException('Database unavailable');\r\n    }\r\n    return this.events.get(id.toString()) || null;\r\n  }\r\n\r\n  async findAll(): Promise<Event[]> {\r\n    return Array.from(this.events.values());\r\n  }\r\n\r\n  async delete(id: UniqueEntityId): Promise<void> {\r\n    this.events.delete(id.toString());\r\n  }\r\n\r\n  async findByTimeRange(start: Date, end: Date): Promise<Event[]> {\r\n    return Array.from(this.events.values()).filter(event => {\r\n      const eventTime = event.timestamp.toDate();\r\n      return eventTime >= start && eventTime <= end;\r\n    });\r\n  }\r\n\r\n  async findBySource(source: EventSource): Promise<Event[]> {\r\n    return Array.from(this.events.values()).filter(event => \r\n      event.source.equals(source)\r\n    );\r\n  }\r\n\r\n  async findBySeverity(severity: EventSeverity): Promise<Event[]> {\r\n    return Array.from(this.events.values()).filter(event => \r\n      event.severity === severity\r\n    );\r\n  }\r\n\r\n  async findEventsForCorrelation(timeWindowMs: number, eventTypes?: EventType[], minSeverity?: EventSeverity): Promise<Event[]> {\r\n    const cutoffTime = new Date(Date.now() - timeWindowMs);\r\n    return Array.from(this.events.values()).filter(event => {\r\n      const eventTime = event.timestamp.toDate();\r\n      return eventTime >= cutoffTime;\r\n    });\r\n  }\r\n\r\n  clear(): void {\r\n    this.events.clear();\r\n  }\r\n}\r\n\r\nclass MockThreatRepository implements ThreatRepository {\r\n  private threats: Map<string, Threat> = new Map();\r\n\r\n  async save(threat: Threat): Promise<void> {\r\n    this.threats.set(threat.id.toString(), threat);\r\n  }\r\n\r\n  async findById(id: UniqueEntityId): Promise<Threat | null> {\r\n    return this.threats.get(id.toString()) || null;\r\n  }\r\n\r\n  async findAll(): Promise<Threat[]> {\r\n    return Array.from(this.threats.values());\r\n  }\r\n\r\n  async delete(id: UniqueEntityId): Promise<void> {\r\n    this.threats.delete(id.toString());\r\n  }\r\n\r\n  async findBySeverity(severity: ThreatSeverity): Promise<Threat[]> {\r\n    return Array.from(this.threats.values()).filter(threat => \r\n      threat.severity === severity\r\n    );\r\n  }\r\n\r\n  async findBySourceIp(sourceIp: IpAddress): Promise<Threat[]> {\r\n    return Array.from(this.threats.values()).filter(threat => \r\n      threat.sourceIp && threat.sourceIp.equals(sourceIp)\r\n    );\r\n  }\r\n\r\n  clear(): void {\r\n    this.threats.clear();\r\n  }\r\n}\r\n\r\nclass MockVulnerabilityRepository implements VulnerabilityRepository {\r\n  private vulnerabilities: Map<string, Vulnerability> = new Map();\r\n\r\n  async save(vulnerability: Vulnerability): Promise<void> {\r\n    this.vulnerabilities.set(vulnerability.id.toString(), vulnerability);\r\n  }\r\n\r\n  async findById(id: UniqueEntityId): Promise<Vulnerability | null> {\r\n    return this.vulnerabilities.get(id.toString()) || null;\r\n  }\r\n\r\n  async findAll(): Promise<Vulnerability[]> {\r\n    return Array.from(this.vulnerabilities.values());\r\n  }\r\n\r\n  async delete(id: UniqueEntityId): Promise<void> {\r\n    this.vulnerabilities.delete(id.toString());\r\n  }\r\n\r\n  async findBySeverity(severity: VulnerabilitySeverity): Promise<Vulnerability[]> {\r\n    return Array.from(this.vulnerabilities.values()).filter(vuln => \r\n      vuln.severity === severity\r\n    );\r\n  }\r\n\r\n  async findByPort(port: Port): Promise<Vulnerability[]> {\r\n    return Array.from(this.vulnerabilities.values()).filter(vuln => \r\n      vuln.affectedPort && vuln.affectedPort.equals(port)\r\n    );\r\n  }\r\n\r\n  clear(): void {\r\n    this.vulnerabilities.clear();\r\n  }\r\n}\r\n\r\nclass MockResponseActionRepository implements ResponseActionRepository {\r\n  private actions: Map<string, ResponseAction> = new Map();\r\n\r\n  async save(action: ResponseAction): Promise<void> {\r\n    this.actions.set(action.id.toString(), action);\r\n  }\r\n\r\n  async findById(id: UniqueEntityId): Promise<ResponseAction | null> {\r\n    return this.actions.get(id.toString()) || null;\r\n  }\r\n\r\n  async findAll(): Promise<ResponseAction[]> {\r\n    return Array.from(this.actions.values());\r\n  }\r\n\r\n  async delete(id: UniqueEntityId): Promise<void> {\r\n    this.actions.delete(id.toString());\r\n  }\r\n\r\n  async findByStatus(status: ActionStatus): Promise<ResponseAction[]> {\r\n    return Array.from(this.actions.values()).filter(action => \r\n      action.status === status\r\n    );\r\n  }\r\n\r\n  async findByType(type: ActionType): Promise<ResponseAction[]> {\r\n    return Array.from(this.actions.values()).filter(action => \r\n      action.type === type\r\n    );\r\n  }\r\n\r\n  clear(): void {\r\n    this.actions.clear();\r\n  }\r\n}\r\n\r\nclass MockEventProcessor implements EventProcessor {\r\n  private shouldFail = false;\r\n  private processingDelay = 0;\r\n\r\n  setShouldFail(fail: boolean): void {\r\n    this.shouldFail = fail;\r\n  }\r\n\r\n  setProcessingDelay(delay: number): void {\r\n    this.processingDelay = delay;\r\n  }\r\n\r\n  async processEvent(event: Event, context: any): Promise<any> {\r\n    if (this.processingDelay > 0) {\r\n      await new Promise(resolve => setTimeout(resolve, this.processingDelay));\r\n    }\r\n\r\n    if (this.shouldFail) {\r\n      throw new ServiceUnavailableException('Event processing service unavailable');\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      eventId: event.id.toString(),\r\n      processingSteps: ['normalize', 'enrich', 'correlate'],\r\n      duration: this.processingDelay || 100,\r\n      normalizedEvent: null,\r\n      enrichedEvent: null,\r\n      correlatedEvents: [],\r\n      errors: [],\r\n    };\r\n  }\r\n\r\n  async processEvents(events: Event[], context: any): Promise<any> {\r\n    const results = await Promise.all(\r\n      events.map(event => this.processEvent(event, context))\r\n    );\r\n    return {\r\n      success: true,\r\n      totalEvents: events.length,\r\n      processedEvents: results.length,\r\n      results,\r\n    };\r\n  }\r\n}\r\n\r\nclass MockThreatDetector implements ThreatDetector {\r\n  private shouldFail = false;\r\n  private detectionDelay = 0;\r\n\r\n  setShouldFail(fail: boolean): void {\r\n    this.shouldFail = fail;\r\n  }\r\n\r\n  setDetectionDelay(delay: number): void {\r\n    this.detectionDelay = delay;\r\n  }\r\n\r\n  async analyzeEvent(event: Event, context: any): Promise<any> {\r\n    if (this.detectionDelay > 0) {\r\n      await new Promise(resolve => setTimeout(resolve, this.detectionDelay));\r\n    }\r\n\r\n    if (this.shouldFail) {\r\n      throw new ServiceUnavailableException('Threat detection service unavailable');\r\n    }\r\n\r\n    const threats = event.severity === EventSeverity.HIGH || event.severity === EventSeverity.CRITICAL\r\n      ? [{ \r\n          id: UniqueEntityId.create().toString(), \r\n          severity: ThreatSeverity.HIGH, \r\n          confidence: 85,\r\n          signature: 'detected-threat-001',\r\n          indicators: ['suspicious-pattern']\r\n        }]\r\n      : [];\r\n\r\n    return {\r\n      eventId: event.id.toString(),\r\n      threats,\r\n      confidence: threats.length > 0 ? 85 : 10,\r\n      analysisTime: this.detectionDelay || 50,\r\n      indicators: threats.flatMap(t => t.indicators),\r\n      recommendations: threats.length > 0 ? ['Investigate immediately', 'Block source IP'] : [],\r\n    };\r\n  }\r\n\r\n  async analyzeEvents(events: Event[], context: any): Promise<any> {\r\n    const analyses = await Promise.all(\r\n      events.map(event => this.analyzeEvent(event, context))\r\n    );\r\n    return {\r\n      totalEvents: events.length,\r\n      analyses,\r\n      aggregatedThreats: analyses.flatMap(a => a.threats),\r\n    };\r\n  }\r\n}\r\n\r\nclass MockVulnerabilityScanner implements VulnerabilityScanner {\r\n  private shouldFail = false;\r\n\r\n  setShouldFail(fail: boolean): void {\r\n    this.shouldFail = fail;\r\n  }\r\n\r\n  async scanEvent(event: Event, context: any): Promise<any> {\r\n    if (this.shouldFail) {\r\n      throw new ServiceUnavailableException('Vulnerability scanner unavailable');\r\n    }\r\n\r\n    const vulnerabilities = event.type === EventType.NETWORK_INTRUSION\r\n      ? [{ \r\n          id: UniqueEntityId.create().toString(), \r\n          severity: VulnerabilitySeverity.MEDIUM, \r\n          cveId: 'CVE-2023-1234',\r\n          score: 6.5,\r\n          description: 'Network vulnerability detected'\r\n        }]\r\n      : [];\r\n\r\n    return {\r\n      target: { id: event.id.toString(), type: 'event' },\r\n      vulnerabilities,\r\n      scanDuration: 75,\r\n      scanType: 'QUICK',\r\n      findings: vulnerabilities.length,\r\n    };\r\n  }\r\n}\r\n\r\nclass MockResponseExecutor implements ResponseExecutor {\r\n  private shouldFail = false;\r\n  private executionDelay = 0;\r\n\r\n  setShouldFail(fail: boolean): void {\r\n    this.shouldFail = fail;\r\n  }\r\n\r\n  setExecutionDelay(delay: number): void {\r\n    this.executionDelay = delay;\r\n  }\r\n\r\n  async executeResponse(event: Event, context: any): Promise<any> {\r\n    if (this.executionDelay > 0) {\r\n      await new Promise(resolve => setTimeout(resolve, this.executionDelay));\r\n    }\r\n\r\n    if (this.shouldFail) {\r\n      throw new ServiceUnavailableException('Response executor unavailable');\r\n    }\r\n\r\n    const actions = context.threats?.length > 0 || context.vulnerabilities?.length > 0\r\n      ? [{ \r\n          type: ActionType.BLOCK_IP, \r\n          status: ActionStatus.COMPLETED,\r\n          parameters: { ipAddress: '*************', duration: 3600 }\r\n        }]\r\n      : [];\r\n\r\n    return {\r\n      responseId: UniqueEntityId.create().toString(),\r\n      event: event.id.toString(),\r\n      actionsExecuted: actions.length,\r\n      actions,\r\n      success: true,\r\n      duration: this.executionDelay || 200,\r\n    };\r\n  }\r\n}\r\n\r\ndescribe('Application Services Integration Tests', () => {\r\n  let module: TestingModule;\r\n  let orchestratorService: SecurityOrchestratorService;\r\n  let threatAssessmentService: ThreatAssessmentService;\r\n  let eventRepository: MockEventRepository;\r\n  let threatRepository: MockThreatRepository;\r\n  let vulnerabilityRepository: MockVulnerabilityRepository;\r\n  let responseActionRepository: MockResponseActionRepository;\r\n  let eventProcessor: MockEventProcessor;\r\n  let threatDetector: MockThreatDetector;\r\n  let vulnerabilityScanner: MockVulnerabilityScanner;\r\n  let responseExecutor: MockResponseExecutor;\r\n\r\n  beforeEach(async () => {\r\n    // Create mock instances\r\n    eventRepository = new MockEventRepository();\r\n    threatRepository = new MockThreatRepository();\r\n    vulnerabilityRepository = new MockVulnerabilityRepository();\r\n    responseActionRepository = new MockResponseActionRepository();\r\n    eventProcessor = new MockEventProcessor();\r\n    threatDetector = new MockThreatDetector();\r\n    vulnerabilityScanner = new MockVulnerabilityScanner();\r\n    responseExecutor = new MockResponseExecutor();\r\n\r\n    module = await Test.createTestingModule({\r\n      providers: [\r\n        SecurityOrchestratorService,\r\n        ThreatAssessmentService,\r\n        { provide: EventRepository, useValue: eventRepository },\r\n        { provide: ThreatRepository, useValue: threatRepository },\r\n        { provide: VulnerabilityRepository, useValue: vulnerabilityRepository },\r\n        { provide: ResponseActionRepository, useValue: responseActionRepository },\r\n        { provide: EventProcessor, useValue: eventProcessor },\r\n        { provide: ThreatDetector, useValue: threatDetector },\r\n        { provide: VulnerabilityScanner, useValue: vulnerabilityScanner },\r\n        { provide: ResponseExecutor, useValue: responseExecutor },\r\n      ],\r\n    }).compile();\r\n\r\n    orchestratorService = module.get<SecurityOrchestratorService>(SecurityOrchestratorService);\r\n    threatAssessmentService = module.get<ThreatAssessmentService>(ThreatAssessmentService);\r\n  });\r\n\r\n  afterEach(async () => {\r\n    eventRepository.clear();\r\n    threatRepository.clear();\r\n    vulnerabilityRepository.clear();\r\n    responseActionRepository.clear();\r\n    await module.close();\r\n  });\r\n\r\n  describe('Service Coordination and Transaction Boundaries', () => {\r\n    it('should coordinate multiple services in security workflow', async () => {\r\n      // Arrange\r\n      const tenantId = TenantId.create();\r\n      const userId = UserId.create();\r\n      const correlationId = CorrelationId.create();\r\n\r\n      const events = [\r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: 'firewall', version: '1.0' }),\r\n          timestamp: EventTimestamp.create(new Date()),\r\n          source: EventSource.create({\r\n            type: EventSourceType.FIREWALL,\r\n            identifier: 'fw-001',\r\n            name: 'Firewall',\r\n          }),\r\n          type: EventType.NETWORK_INTRUSION,\r\n          severity: EventSeverity.HIGH,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { sourceIp: '*************' },\r\n          tenantId,\r\n          userId,\r\n        }),\r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: 'ids', version: '1.0' }),\r\n          timestamp: EventTimestamp.create(new Date()),\r\n          source: EventSource.create({\r\n            type: EventSourceType.IDS,\r\n            identifier: 'ids-001',\r\n            name: 'IDS',\r\n          }),\r\n          type: EventType.MALWARE_DETECTED,\r\n          severity: EventSeverity.CRITICAL,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { malwareType: 'trojan' },\r\n          tenantId,\r\n          userId,\r\n        }),\r\n      ];\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow(events, {\r\n        tenantId: tenantId.value,\r\n        userId: userId.value,\r\n        correlationId: correlationId.value,\r\n        priority: 'HIGH' as any,\r\n        autoResponse: true,\r\n        enableThreatHunting: true,\r\n        enableVulnerabilityScanning: true,\r\n        workflowConfig: {\r\n          enableEventProcessing: true,\r\n          enableThreatDetection: true,\r\n          enableVulnerabilityScanning: true,\r\n          enableResponseExecution: true,\r\n          enableCorrelation: true,\r\n          enableEnrichment: true,\r\n          batchSize: 5,\r\n          maxConcurrentOperations: 3,\r\n          retryAttempts: 2,\r\n          timeoutMs: 60000,\r\n        },\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      expect(orchestrationResult.success).toBe(true);\r\n      expect(orchestrationResult.eventsProcessed).toBe(2);\r\n      expect(orchestrationResult.processingResults).toHaveLength(2);\r\n      expect(orchestrationResult.threatAnalyses).toHaveLength(2);\r\n      expect(orchestrationResult.vulnerabilityScans).toHaveLength(2);\r\n      expect(orchestrationResult.responseResults).toHaveLength(2);\r\n      \r\n      // Verify service coordination\r\n      expect(orchestrationResult.threatsDetected).toBeGreaterThan(0);\r\n      expect(orchestrationResult.actionsExecuted).toBeGreaterThan(0);\r\n      expect(orchestrationResult.duration).toBeGreaterThan(0);\r\n      expect(orchestrationResult.errors).toHaveLength(0);\r\n    });\r\n\r\n    it('should handle transaction boundaries correctly with rollback on failure', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-001',\r\n          name: 'Test App',\r\n        }),\r\n        type: EventType.SECURITY_ALERT,\r\n        severity: EventSeverity.HIGH,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { alertType: 'suspicious-activity' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Configure repository to fail after processing starts\r\n      eventRepository.setShouldFail(true);\r\n\r\n      // Act\r\n      const result = await orchestratorService.processSecurityEvents([event], {\r\n        priority: 'HIGH' as any,\r\n        timeoutMs: 30000,\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true); // Service handles errors gracefully\r\n      const processingResult = result.getValue();\r\n      \r\n      expect(processingResult.success).toBe(false); // But marks operation as failed\r\n      expect(processingResult.errors.length).toBeGreaterThan(0);\r\n      expect(processingResult.errors[0].errorCode).toBe('ORCHESTRATION_FAILED');\r\n    });\r\n\r\n    it('should handle concurrent service operations', async () => {\r\n      // Arrange\r\n      const events = Array.from({ length: 10 }, (_, i) => \r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: `source-${i}`, version: '1.0' }),\r\n          timestamp: EventTimestamp.create(new Date()),\r\n          source: EventSource.create({\r\n            type: EventSourceType.APPLICATION,\r\n            identifier: `app-${i}`,\r\n            name: `App ${i}`,\r\n          }),\r\n          type: EventType.AUTHENTICATION_FAILURE,\r\n          severity: i % 2 === 0 ? EventSeverity.HIGH : EventSeverity.MEDIUM,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { attempt: i },\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        })\r\n      );\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow(events, {\r\n        priority: 'MEDIUM' as any,\r\n        workflowConfig: {\r\n          enableEventProcessing: true,\r\n          enableThreatDetection: true,\r\n          enableVulnerabilityScanning: true,\r\n          enableResponseExecution: true,\r\n          enableCorrelation: true,\r\n          enableEnrichment: true,\r\n          batchSize: 3,\r\n          maxConcurrentOperations: 5,\r\n          retryAttempts: 2,\r\n          timeoutMs: 60000,\r\n        },\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      expect(orchestrationResult.success).toBe(true);\r\n      expect(orchestrationResult.eventsProcessed).toBe(10);\r\n      expect(orchestrationResult.processingResults).toHaveLength(10);\r\n      \r\n      // All events should be processed successfully\r\n      orchestrationResult.processingResults.forEach(pr => {\r\n        expect(pr.success).toBe(true);\r\n      });\r\n\r\n      // High severity events should generate threats\r\n      const highSeverityCount = events.filter(e => e.severity === EventSeverity.HIGH).length;\r\n      expect(orchestrationResult.threatsDetected).toBe(highSeverityCount);\r\n    });\r\n\r\n    it('should handle service timeouts gracefully', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-001',\r\n          name: 'Test App',\r\n        }),\r\n        type: EventType.SECURITY_ALERT,\r\n        severity: EventSeverity.HIGH,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { alertType: 'test' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Configure services with delays\r\n      eventProcessor.setProcessingDelay(100);\r\n      threatDetector.setDetectionDelay(100);\r\n      responseExecutor.setExecutionDelay(100);\r\n\r\n      // Act - Process with very short timeout\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow([event], {\r\n        priority: 'HIGH' as any,\r\n        timeoutMs: 50, // Very short timeout\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      // Should complete successfully despite timeout (graceful handling)\r\n      expect(orchestrationResult.eventsProcessed).toBe(1);\r\n      expect(orchestrationResult.duration).toBeGreaterThan(0);\r\n    });\r\n  });\r\n\r\n  describe('Error Handling and Recovery', () => {\r\n    it('should handle individual service failures gracefully', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-001',\r\n          name: 'Test App',\r\n        }),\r\n        type: EventType.NETWORK_INTRUSION,\r\n        severity: EventSeverity.HIGH,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { sourceIp: '*************' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Configure threat detector to fail\r\n      threatDetector.setShouldFail(true);\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow([event], {\r\n        priority: 'HIGH' as any,\r\n        autoResponse: true,\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      // Should continue processing despite threat detector failure\r\n      expect(orchestrationResult.eventsProcessed).toBe(1);\r\n      expect(orchestrationResult.processingResults).toHaveLength(1);\r\n      expect(orchestrationResult.processingResults[0].success).toBe(true);\r\n      \r\n      // Should have errors from threat detection\r\n      expect(orchestrationResult.errors.length).toBeGreaterThan(0);\r\n      const threatDetectionError = orchestrationResult.errors.find(e => \r\n        e.stage === 'THREAT_DETECTION' || e.message.includes('Threat detection')\r\n      );\r\n      expect(threatDetectionError).toBeDefined();\r\n    });\r\n\r\n    it('should handle cascading service failures', async () => {\r\n      // Arrange\r\n      const events = [\r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: 'firewall', version: '1.0' }),\r\n          timestamp: EventTimestamp.create(new Date()),\r\n          source: EventSource.create({\r\n            type: EventSourceType.FIREWALL,\r\n            identifier: 'fw-001',\r\n            name: 'Firewall',\r\n          }),\r\n          type: EventType.NETWORK_INTRUSION,\r\n          severity: EventSeverity.CRITICAL,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { sourceIp: '*************' },\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        }),\r\n      ];\r\n\r\n      // Configure multiple services to fail\r\n      eventProcessor.setShouldFail(true);\r\n      threatDetector.setShouldFail(true);\r\n      vulnerabilityScanner.setShouldFail(true);\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow(events, {\r\n        priority: 'CRITICAL' as any,\r\n        autoResponse: false,\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      // Should fail gracefully with multiple errors\r\n      expect(orchestrationResult.success).toBe(false);\r\n      expect(orchestrationResult.errors.length).toBeGreaterThan(0);\r\n      \r\n      // Should have attempted to process the event\r\n      expect(orchestrationResult.eventsProcessed).toBe(0); // No events processed due to failures\r\n    });\r\n\r\n    it('should implement retry logic for transient failures', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-001',\r\n          name: 'Test App',\r\n        }),\r\n        type: EventType.AUTHENTICATION_FAILURE,\r\n        severity: EventSeverity.MEDIUM,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { username: 'testuser' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      let callCount = 0;\r\n      const originalProcessEvent = eventProcessor.processEvent.bind(eventProcessor);\r\n      eventProcessor.processEvent = jest.fn().mockImplementation(async (event, context) => {\r\n        callCount++;\r\n        if (callCount <= 2) {\r\n          throw new ServiceUnavailableException('Transient failure');\r\n        }\r\n        return originalProcessEvent(event, context);\r\n      });\r\n\r\n      // Act\r\n      const result = await orchestratorService.processSecurityEvents([event], {\r\n        priority: 'MEDIUM' as any,\r\n        workflowConfig: {\r\n          enableEventProcessing: true,\r\n          enableThreatDetection: false,\r\n          enableVulnerabilityScanning: false,\r\n          enableResponseExecution: false,\r\n          enableCorrelation: false,\r\n          enableEnrichment: false,\r\n          batchSize: 1,\r\n          maxConcurrentOperations: 1,\r\n          retryAttempts: 3,\r\n          timeoutMs: 60000,\r\n        },\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const processingResult = result.getValue();\r\n      \r\n      // Should succeed after retries\r\n      expect(processingResult.success).toBe(true);\r\n      expect(processingResult.eventsProcessed).toBe(1);\r\n      expect(callCount).toBe(3); // Initial call + 2 retries\r\n    });\r\n  });\r\n\r\n  describe('Performance and Scalability', () => {\r\n    it('should handle high-volume event processing efficiently', async () => {\r\n      // Arrange\r\n      const eventCount = 100;\r\n      const events = Array.from({ length: eventCount }, (_, i) => \r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: `source-${i}`, version: '1.0' }),\r\n          timestamp: EventTimestamp.create(new Date()),\r\n          source: EventSource.create({\r\n            type: EventSourceType.APPLICATION,\r\n            identifier: `app-${i}`,\r\n            name: `App ${i}`,\r\n          }),\r\n          type: EventType.AUTHENTICATION_FAILURE,\r\n          severity: EventSeverity.MEDIUM,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { attempt: i },\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        })\r\n      );\r\n\r\n      const startTime = Date.now();\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow(events, {\r\n        priority: 'MEDIUM' as any,\r\n        workflowConfig: {\r\n          enableEventProcessing: true,\r\n          enableThreatDetection: true,\r\n          enableVulnerabilityScanning: false,\r\n          enableResponseExecution: false,\r\n          enableCorrelation: true,\r\n          enableEnrichment: true,\r\n          batchSize: 10,\r\n          maxConcurrentOperations: 5,\r\n          retryAttempts: 1,\r\n          timeoutMs: 120000,\r\n        },\r\n      });\r\n\r\n      const endTime = Date.now();\r\n      const totalTime = endTime - startTime;\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      expect(orchestrationResult.success).toBe(true);\r\n      expect(orchestrationResult.eventsProcessed).toBe(eventCount);\r\n      expect(orchestrationResult.processingResults).toHaveLength(eventCount);\r\n      \r\n      // Performance assertions\r\n      expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds\r\n      expect(orchestrationResult.duration).toBeGreaterThan(0);\r\n      \r\n      // Verify throughput\r\n      const throughput = eventCount / (totalTime / 1000); // events per second\r\n      expect(throughput).toBeGreaterThan(10); // At least 10 events per second\r\n    });\r\n\r\n    it('should optimize batch processing for better performance', async () => {\r\n      // Arrange\r\n      const events = Array.from({ length: 50 }, (_, i) => \r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: `batch-${i}`, version: '1.0' }),\r\n          timestamp: EventTimestamp.create(new Date()),\r\n          source: EventSource.create({\r\n            type: EventSourceType.APPLICATION,\r\n            identifier: `batch-app-${i}`,\r\n            name: `Batch App ${i}`,\r\n          }),\r\n          type: EventType.SECURITY_ALERT,\r\n          severity: EventSeverity.LOW,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { batchId: Math.floor(i / 10) },\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        })\r\n      );\r\n\r\n      // Test different batch sizes\r\n      const batchSizes = [5, 10, 25];\r\n      const results = [];\r\n\r\n      for (const batchSize of batchSizes) {\r\n        const startTime = Date.now();\r\n        \r\n        const result = await orchestratorService.orchestrateSecurityWorkflow(events, {\r\n          priority: 'LOW' as any,\r\n          workflowConfig: {\r\n            enableEventProcessing: true,\r\n            enableThreatDetection: false,\r\n            enableVulnerabilityScanning: false,\r\n            enableResponseExecution: false,\r\n            enableCorrelation: false,\r\n            enableEnrichment: false,\r\n            batchSize,\r\n            maxConcurrentOperations: 3,\r\n            retryAttempts: 1,\r\n            timeoutMs: 60000,\r\n          },\r\n        });\r\n\r\n        const endTime = Date.now();\r\n        const duration = endTime - startTime;\r\n\r\n        results.push({\r\n          batchSize,\r\n          duration,\r\n          success: result.isSuccess() && result.getValue().success,\r\n          eventsProcessed: result.isSuccess() ? result.getValue().eventsProcessed : 0,\r\n        });\r\n      }\r\n\r\n      // Assert\r\n      results.forEach(result => {\r\n        expect(result.success).toBe(true);\r\n        expect(result.eventsProcessed).toBe(50);\r\n      });\r\n\r\n      // Verify that larger batch sizes are generally more efficient\r\n      const sortedByBatchSize = results.sort((a, b) => a.batchSize - b.batchSize);\r\n      expect(sortedByBatchSize[0].batchSize).toBe(5);\r\n      expect(sortedByBatchSize[2].batchSize).toBe(25);\r\n    });\r\n  });\r\n\r\n  describe('Cross-Cutting Concerns', () => {\r\n    it('should maintain audit trail throughout service operations', async () => {\r\n      // Arrange\r\n      const tenantId = TenantId.create();\r\n      const userId = UserId.create();\r\n      const correlationId = CorrelationId.create();\r\n\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'audit-test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'audit-app',\r\n          name: 'Audit Test App',\r\n        }),\r\n        type: EventType.SECURITY_ALERT,\r\n        severity: EventSeverity.HIGH,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { alertType: 'audit-test' },\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow([event], {\r\n        tenantId: tenantId.value,\r\n        userId: userId.value,\r\n        correlationId: correlationId.value,\r\n        priority: 'HIGH' as any,\r\n        autoResponse: true,\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      // Verify audit information is maintained\r\n      expect(orchestrationResult.orchestrationId).toBeDefined();\r\n      expect(orchestrationResult.startTime).toBeInstanceOf(Date);\r\n      expect(orchestrationResult.endTime).toBeInstanceOf(Date);\r\n      expect(orchestrationResult.duration).toBeGreaterThan(0);\r\n      \r\n      // Verify metrics are collected\r\n      expect(orchestrationResult.metrics).toBeDefined();\r\n      expect(orchestrationResult.metrics.totalProcessingTime).toBeGreaterThan(0);\r\n      expect(orchestrationResult.metrics.throughput).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should handle security policies and compliance requirements', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'compliance-test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'compliance-app',\r\n          name: 'Compliance Test App',\r\n        }),\r\n        type: EventType.DATA_BREACH,\r\n        severity: EventSeverity.CRITICAL,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { \r\n          dataType: 'PII',\r\n          recordCount: 1000,\r\n          affectedUsers: ['user1', 'user2']\r\n        },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow([event], {\r\n        priority: 'CRITICAL' as any,\r\n        autoResponse: true,\r\n        customRules: [\r\n          {\r\n            id: 'data-breach-rule',\r\n            name: 'Data Breach Response',\r\n            description: 'Immediate response for data breaches',\r\n            condition: {\r\n              eventTypes: [EventType.DATA_BREACH],\r\n              severityLevels: [EventSeverity.CRITICAL],\r\n            },\r\n            action: 'AUTO_RESPOND' as any,\r\n            priority: 1,\r\n            enabled: true,\r\n          },\r\n        ],\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      expect(orchestrationResult.success).toBe(true);\r\n      expect(orchestrationResult.eventsProcessed).toBe(1);\r\n      \r\n      // Should have triggered automated response due to critical data breach\r\n      expect(orchestrationResult.actionsExecuted).toBeGreaterThan(0);\r\n      expect(orchestrationResult.responseResults).toHaveLength(1);\r\n      \r\n      // Should have recommendations for compliance\r\n      expect(orchestrationResult.recommendations).toBeDefined();\r\n      expect(orchestrationResult.recommendations.length).toBeGreaterThan(0);\r\n    });\r\n  });\r\n});"], "version": 3}