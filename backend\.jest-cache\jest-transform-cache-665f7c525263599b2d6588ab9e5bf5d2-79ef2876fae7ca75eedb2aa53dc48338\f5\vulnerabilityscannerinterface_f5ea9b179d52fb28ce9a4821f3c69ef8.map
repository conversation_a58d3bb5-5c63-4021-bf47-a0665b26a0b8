{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\services\\vulnerability-scanner.interface.ts", "mappings": ";;;AAKA,yFAAgF;AAyFvE,sGAzFA,mDAAqB,OAyFA;AAE9B;;GAEG;AACH,IAAY,uBAaX;AAbD,WAAY,uBAAuB;IACjC,0DAA+B,CAAA;IAC/B,8DAAmC,CAAA;IACnC,wDAA6B,CAAA;IAC7B,8DAAmC,CAAA;IACnC,0DAA+B,CAAA;IAC/B,4DAAiC,CAAA;IACjC,oDAAyB,CAAA;IACzB,sDAA2B,CAAA;IAC3B,gDAAqB,CAAA;IACrB,4DAAiC,CAAA;IACjC,8DAAmC,CAAA;IACnC,4CAAiB,CAAA;AACnB,CAAC,EAbW,uBAAuB,uCAAvB,uBAAuB,QAalC;AAED;;GAEG;AACH,IAAY,qBAqBX;AArBD,WAAY,qBAAqB;IAC/B,gDAAuB,CAAA;IACvB,0DAAiC,CAAA;IACjC,wDAA+B,CAAA;IAC/B,sDAA6B,CAAA;IAC7B,wDAA+B,CAAA;IAC/B,8DAAqC,CAAA;IACrC,kEAAyC,CAAA;IACzC,sEAA6C,CAAA;IAC7C,kFAAyD,CAAA;IACzD,4DAAmC,CAAA;IACnC,sEAA6C,CAAA;IAC7C,0EAAiD,CAAA;IACjD,gEAAuC,CAAA;IACvC,wEAA+C,CAAA;IAC/C,0DAAiC,CAAA;IACjC,8EAAqD,CAAA;IACrD,4DAAmC,CAAA;IACnC,0DAAiC,CAAA;IACjC,kEAAyC,CAAA;IACzC,8DAAqC,CAAA;AACvC,CAAC,EArBW,qBAAqB,qCAArB,qBAAqB,QAqBhC;AAED;;GAEG;AACH,IAAY,iBAWX;AAXD,WAAY,iBAAiB;IAC3B,gEAA2C,CAAA;IAC3C,0CAAqB,CAAA;IACrB,gEAA2C,CAAA;IAC3C,wDAAmC,CAAA;IACnC,oDAA+B,CAAA;IAC/B,gDAA2B,CAAA;IAC3B,8DAAyC,CAAA;IACzC,0DAAqC,CAAA;IACrC,sDAAiC,CAAA;IACjC,sCAAiB,CAAA;AACnB,CAAC,EAXW,iBAAiB,iCAAjB,iBAAiB,QAW5B;AAED;;GAEG;AACH,IAAY,4BAWX;AAXD,WAAY,4BAA4B;IACtC,iEAAiC,CAAA;IACjC,iEAAiC,CAAA;IACjC,qEAAqC,CAAA;IACrC,2DAA2B,CAAA;IAC3B,2EAA2C,CAAA;IAC3C,yDAAyB,CAAA;IACzB,iEAAiC,CAAA;IACjC,qEAAqC,CAAA;IACrC,uEAAuC,CAAA;IACvC,uEAAuC,CAAA;AACzC,CAAC,EAXW,4BAA4B,4CAA5B,4BAA4B,QAWvC;AAED;;GAEG;AACH,IAAY,sBAOX;AAPD,WAAY,sBAAsB;IAChC,+CAAqB,CAAA;IACrB,qCAAW,CAAA;IACX,2CAAiB,CAAA;IACjB,uCAAa,CAAA;IACb,iDAAuB,CAAA;IACvB,+CAAqB,CAAA;AACvB,CAAC,EAPW,sBAAsB,sCAAtB,sBAAsB,QAOjC;AAwBD;;GAEG;AACH,IAAY,aAaX;AAbD,WAAY,aAAa;IACvB,4CAA2B,CAAA;IAC3B,sDAAqC,CAAA;IACrC,sCAAqB,CAAA;IACrB,0CAAyB,CAAA;IACzB,kDAAiC,CAAA;IACjC,wCAAuB,CAAA;IACvB,gDAA+B,CAAA;IAC/B,4BAAW,CAAA;IACX,oCAAmB,CAAA;IACnB,wCAAuB,CAAA;IACvB,kCAAiB,CAAA;IACjB,oCAAmB,CAAA;AACrB,CAAC,EAbW,aAAa,6BAAb,aAAa,QAaxB;AAED;;GAEG;AACH,IAAY,oBAKX;AALD,WAAY,oBAAoB;IAC9B,mCAAW,CAAA;IACX,yCAAiB,CAAA;IACjB,qCAAa,CAAA;IACb,6CAAqB,CAAA;AACvB,CAAC,EALW,oBAAoB,oCAApB,oBAAoB,QAK/B;AA0BD;;GAEG;AACH,IAAY,WASX;AATD,WAAY,WAAW;IACrB,gCAAiB,CAAA;IACjB,8BAAe,CAAA;IACf,0BAAW,CAAA;IACX,0CAA2B,CAAA;IAC3B,sDAAuC,CAAA;IACvC,4DAA6C,CAAA;IAC7C,gDAAiC,CAAA;IACjC,gEAAiD,CAAA;AACnD,CAAC,EATW,WAAW,2BAAX,WAAW,QAStB;AAED;;GAEG;AACH,IAAY,mBAOX;AAPD,WAAY,mBAAmB;IAC7B,wCAAiB,CAAA;IACjB,0CAAmB,CAAA;IACnB,gDAAyB,CAAA;IACzB,4DAAqC,CAAA;IACrC,gDAAyB,CAAA;IACzB,kDAA2B,CAAA;AAC7B,CAAC,EAPW,mBAAmB,mCAAnB,mBAAmB,QAO9B;AAED;;GAEG;AACH,IAAY,iBAIX;AAJD,WAAY,iBAAiB;IAC3B,gCAAW,CAAA;IACX,sCAAiB,CAAA;IACjB,kCAAa,CAAA;AACf,CAAC,EAJW,iBAAiB,iCAAjB,iBAAiB,QAI5B;AAED;;GAEG;AACH,IAAY,kBAKX;AALD,WAAY,kBAAkB;IAC5B,+CAAyB,CAAA;IACzB,yCAAmB,CAAA;IACnB,mCAAa,CAAA;IACb,6CAAuB,CAAA;AACzB,CAAC,EALW,kBAAkB,kCAAlB,kBAAkB,QAK7B;AA8BD;;GAEG;AACH,IAAY,eASX;AATD,WAAY,eAAe;IACzB,kCAAe,CAAA;IACf,gEAA6C,CAAA;IAC7C,4CAAyB,CAAA;IACzB,sCAAmB,CAAA;IACnB,8CAA2B,CAAA;IAC3B,4CAAyB,CAAA;IACzB,4CAAyB,CAAA;IACzB,wCAAqB,CAAA;AACvB,CAAC,EATW,eAAe,+BAAf,eAAe,QAS1B;AAED;;GAEG;AACH,IAAY,mBAMX;AAND,WAAY,mBAAmB;IAC7B,8CAAuB,CAAA;IACvB,oCAAa,CAAA;IACb,wCAAiB,CAAA;IACjB,kCAAW,CAAA;IACX,4CAAqB,CAAA;AACvB,CAAC,EANW,mBAAmB,mCAAnB,mBAAmB,QAM9B;AAED;;GAEG;AACH,IAAY,iBAMX;AAND,WAAY,iBAAiB;IAC3B,wCAAmB,CAAA;IACnB,gCAAW,CAAA;IACX,sCAAiB,CAAA;IACjB,kCAAa,CAAA;IACb,4CAAuB,CAAA;AACzB,CAAC,EANW,iBAAiB,iCAAjB,iBAAiB,QAM5B;AAoBD;;GAEG;AACH,IAAY,aAWX;AAXD,WAAY,aAAa;IACvB,4BAAW,CAAA;IACX,4BAAW,CAAA;IACX,oDAAmC,CAAA;IACnC,wDAAuC,CAAA;IACvC,sDAAqC,CAAA;IACrC,kDAAiC,CAAA;IACjC,wCAAuB,CAAA;IACvB,oDAAmC,CAAA;IACnC,4CAA2B,CAAA;IAC3B,gDAA+B,CAAA;AACjC,CAAC,EAXW,aAAa,6BAAb,aAAa,QAWxB;AAsBD;;GAEG;AACH,IAAY,mBAMX;AAND,WAAY,mBAAmB;IAC7B,gDAAyB,CAAA;IACzB,kCAAW,CAAA;IACX,wCAAiB,CAAA;IACjB,oCAAa,CAAA;IACb,4CAAqB,CAAA;AACvB,CAAC,EANW,mBAAmB,mCAAnB,mBAAmB,QAM9B;AAoBD;;GAEG;AACH,IAAY,cAUX;AAVD,WAAY,cAAc;IACxB,2CAAyB,CAAA;IACzB,uCAAqB,CAAA;IACrB,6BAAW,CAAA;IACX,iDAA+B,CAAA;IAC/B,6CAA2B,CAAA;IAC3B,uCAAqB,CAAA;IACrB,yCAAuB,CAAA;IACvB,mDAAiC,CAAA;IACjC,+CAA6B,CAAA;AAC/B,CAAC,EAVW,cAAc,8BAAd,cAAc,QAUzB;AAwDD;;GAEG;AACH,IAAY,qBAOX;AAPD,WAAY,qBAAqB;IAC/B,wCAAe,CAAA;IACf,sCAAa,CAAA;IACb,wDAA+B,CAAA;IAC/B,kDAAyB,CAAA;IACzB,0CAAiB,CAAA;IACjB,kDAAyB,CAAA;AAC3B,CAAC,EAPW,qBAAqB,qCAArB,qBAAqB,QAOhC;AAsBD;;GAEG;AACH,IAAY,cAQX;AARD,WAAY,cAAc;IACxB,yDAAuC,CAAA;IACvC,qCAAmB,CAAA;IACnB,6CAA2B,CAAA;IAC3B,qCAAmB,CAAA;IACnB,iCAAe,CAAA;IACf,iCAAe,CAAA;IACf,mCAAiB,CAAA;AACnB,CAAC,EARW,cAAc,8BAAd,cAAc,QAQzB;AAsCD;;GAEG;AACH,IAAY,aAQX;AARD,WAAY,aAAa;IACvB,0CAAyB,CAAA;IACzB,sCAAqB,CAAA;IACrB,8BAAa,CAAA;IACb,oCAAmB,CAAA;IACnB,sDAAqC,CAAA;IACrC,sCAAqB,CAAA;IACrB,sCAAqB,CAAA;AACvB,CAAC,EARW,aAAa,6BAAb,aAAa,QAQxB;AAED;;GAEG;AACH,IAAY,SAMX;AAND,WAAY,SAAS;IACnB,gCAAmB,CAAA;IACnB,gCAAmB,CAAA;IACnB,8BAAiB,CAAA;IACjB,0BAAa,CAAA;IACb,sCAAyB,CAAA;AAC3B,CAAC,EANW,SAAS,yBAAT,SAAS,QAMpB;AAkBD;;GAEG;AACH,IAAY,YAKX;AALD,WAAY,YAAY;IACtB,qCAAqB,CAAA;IACrB,uCAAuB,CAAA;IACvB,yCAAyB,CAAA;IACzB,6CAA6B,CAAA;AAC/B,CAAC,EALW,YAAY,4BAAZ,YAAY,QAKvB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\services\\vulnerability-scanner.interface.ts"], "sourcesContent": ["import { Event } from '../../entities/event.entity';\r\nimport { NormalizedEvent } from '../../entities/normalized-event.entity';\r\nimport { EnrichedEvent } from '../../entities/enriched-event.entity';\r\nimport { CorrelatedEvent } from '../../entities/correlated-event.entity';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { VulnerabilitySeverity } from '../../enums/vulnerability-severity.enum';\r\n\r\n/**\r\n * Vulnerability Scan Result\r\n */\r\nexport interface VulnerabilityScan {\r\n  /** Discovered vulnerabilities */\r\n  vulnerabilities: DiscoveredVulnerability[];\r\n  /** Scan timestamp */\r\n  scanTime: Date;\r\n  /** Scan duration in milliseconds */\r\n  scanDuration: number;\r\n  /** Scan method used */\r\n  scanMethod: VulnerabilityScanMethod;\r\n  /** Scan coverage percentage */\r\n  coverage: number;\r\n  /** Scan confidence (0-100) */\r\n  confidence: number;\r\n  /** Scan target information */\r\n  target: ScanTarget;\r\n  /** Scan statistics */\r\n  statistics: ScanStatistics;\r\n  /** Scan metadata */\r\n  metadata?: Record<string, any>;\r\n  /** Scan recommendations */\r\n  recommendations: string[];\r\n  /** Scan errors encountered */\r\n  errors: string[];\r\n  /** Scan warnings */\r\n  warnings: string[];\r\n}\r\n\r\n/**\r\n * Discovered Vulnerability Interface\r\n */\r\nexport interface DiscoveredVulnerability {\r\n  /** Vulnerability identifier */\r\n  id: string;\r\n  /** CVE identifier if available */\r\n  cveId?: string;\r\n  /** CWE identifier if available */\r\n  cweId?: string;\r\n  /** Vulnerability title */\r\n  title: string;\r\n  /** Vulnerability description */\r\n  description: string;\r\n  /** Vulnerability severity */\r\n  severity: VulnerabilitySeverity;\r\n  /** CVSS v3 score */\r\n  cvssScore?: number;\r\n  /** CVSS v3 vector */\r\n  cvssVector?: string;\r\n  /** CVSS v2 score for legacy support */\r\n  cvssV2Score?: number;\r\n  /** Affected components/assets */\r\n  affectedComponents: AffectedComponent[];\r\n  /** Vulnerability category */\r\n  category: VulnerabilityCategory;\r\n  /** Vulnerability type */\r\n  type: VulnerabilityType;\r\n  /** Discovery method */\r\n  discoveryMethod: VulnerabilityDiscoveryMethod;\r\n  /** Discovery timestamp */\r\n  discoveredAt: Date;\r\n  /** First seen timestamp */\r\n  firstSeen?: Date;\r\n  /** Last seen timestamp */\r\n  lastSeen?: Date;\r\n  /** Exploitation likelihood */\r\n  exploitationLikelihood: ExploitationLikelihood;\r\n  /** Available exploits */\r\n  exploits: ExploitInfo[];\r\n  /** Remediation information */\r\n  remediation: RemediationInfo;\r\n  /** References and sources */\r\n  references: VulnerabilityReference[];\r\n  /** Vulnerability tags */\r\n  tags: string[];\r\n  /** Risk score (0-100) */\r\n  riskScore: number;\r\n  /** Business impact assessment */\r\n  businessImpact: BusinessImpact;\r\n  /** Vulnerability metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Vulnerability Severity Enum (re-export for convenience)\r\n */\r\nexport { VulnerabilitySeverity };\r\n\r\n/**\r\n * Vulnerability Scan Method Enum\r\n */\r\nexport enum VulnerabilityScanMethod {\r\n  AUTHENTICATED = 'AUTHENTICATED',\r\n  UNAUTHENTICATED = 'UNAUTHENTICATED',\r\n  NETWORK_SCAN = 'NETWORK_SCAN',\r\n  WEB_APPLICATION = 'WEB_APPLICATION',\r\n  DATABASE_SCAN = 'DATABASE_SCAN',\r\n  CONTAINER_SCAN = 'CONTAINER_SCAN',\r\n  CLOUD_SCAN = 'CLOUD_SCAN',\r\n  MOBILE_SCAN = 'MOBILE_SCAN',\r\n  API_SCAN = 'API_SCAN',\r\n  INFRASTRUCTURE = 'INFRASTRUCTURE',\r\n  COMPLIANCE_SCAN = 'COMPLIANCE_SCAN',\r\n  HYBRID = 'HYBRID',\r\n}\r\n\r\n/**\r\n * Vulnerability Category Enum\r\n */\r\nexport enum VulnerabilityCategory {\r\n  INJECTION = 'INJECTION',\r\n  AUTHENTICATION = 'AUTHENTICATION',\r\n  AUTHORIZATION = 'AUTHORIZATION',\r\n  CRYPTOGRAPHY = 'CRYPTOGRAPHY',\r\n  CONFIGURATION = 'CONFIGURATION',\r\n  INPUT_VALIDATION = 'INPUT_VALIDATION',\r\n  SESSION_MANAGEMENT = 'SESSION_MANAGEMENT',\r\n  CROSS_SITE_SCRIPTING = 'CROSS_SITE_SCRIPTING',\r\n  CROSS_SITE_REQUEST_FORGERY = 'CROSS_SITE_REQUEST_FORGERY',\r\n  BUFFER_OVERFLOW = 'BUFFER_OVERFLOW',\r\n  PRIVILEGE_ESCALATION = 'PRIVILEGE_ESCALATION',\r\n  INFORMATION_DISCLOSURE = 'INFORMATION_DISCLOSURE',\r\n  DENIAL_OF_SERVICE = 'DENIAL_OF_SERVICE',\r\n  REMOTE_CODE_EXECUTION = 'REMOTE_CODE_EXECUTION',\r\n  PATH_TRAVERSAL = 'PATH_TRAVERSAL',\r\n  INSECURE_DESERIALIZATION = 'INSECURE_DESERIALIZATION',\r\n  MISSING_PATCHES = 'MISSING_PATCHES',\r\n  WEAK_PASSWORDS = 'WEAK_PASSWORDS',\r\n  INSECURE_PROTOCOLS = 'INSECURE_PROTOCOLS',\r\n  MISCONFIGURATION = 'MISCONFIGURATION',\r\n}\r\n\r\n/**\r\n * Vulnerability Type Enum\r\n */\r\nexport enum VulnerabilityType {\r\n  KNOWN_VULNERABILITY = 'KNOWN_VULNERABILITY',\r\n  ZERO_DAY = 'ZERO_DAY',\r\n  CONFIGURATION_ISSUE = 'CONFIGURATION_ISSUE',\r\n  WEAK_CREDENTIAL = 'WEAK_CREDENTIAL',\r\n  MISSING_PATCH = 'MISSING_PATCH',\r\n  DESIGN_FLAW = 'DESIGN_FLAW',\r\n  IMPLEMENTATION_BUG = 'IMPLEMENTATION_BUG',\r\n  POLICY_VIOLATION = 'POLICY_VIOLATION',\r\n  COMPLIANCE_GAP = 'COMPLIANCE_GAP',\r\n  CUSTOM = 'CUSTOM',\r\n}\r\n\r\n/**\r\n * Vulnerability Discovery Method Enum\r\n */\r\nexport enum VulnerabilityDiscoveryMethod {\r\n  AUTOMATED_SCAN = 'AUTOMATED_SCAN',\r\n  MANUAL_TESTING = 'MANUAL_TESTING',\r\n  PENETRATION_TEST = 'PENETRATION_TEST',\r\n  CODE_REVIEW = 'CODE_REVIEW',\r\n  THREAT_INTELLIGENCE = 'THREAT_INTELLIGENCE',\r\n  BUG_BOUNTY = 'BUG_BOUNTY',\r\n  SECURITY_AUDIT = 'SECURITY_AUDIT',\r\n  COMPLIANCE_CHECK = 'COMPLIANCE_CHECK',\r\n  INCIDENT_RESPONSE = 'INCIDENT_RESPONSE',\r\n  VENDOR_DISCLOSURE = 'VENDOR_DISCLOSURE',\r\n}\r\n\r\n/**\r\n * Exploitation Likelihood Enum\r\n */\r\nexport enum ExploitationLikelihood {\r\n  VERY_LOW = 'VERY_LOW',\r\n  LOW = 'LOW',\r\n  MEDIUM = 'MEDIUM',\r\n  HIGH = 'HIGH',\r\n  VERY_HIGH = 'VERY_HIGH',\r\n  CRITICAL = 'CRITICAL',\r\n}\r\n\r\n/**\r\n * Affected Component Interface\r\n */\r\nexport interface AffectedComponent {\r\n  /** Component identifier */\r\n  id: string;\r\n  /** Component name */\r\n  name: string;\r\n  /** Component type */\r\n  type: ComponentType;\r\n  /** Component version */\r\n  version?: string;\r\n  /** Component vendor */\r\n  vendor?: string;\r\n  /** Component location */\r\n  location?: string;\r\n  /** Component criticality */\r\n  criticality: ComponentCriticality;\r\n  /** Component metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Component Type Enum\r\n */\r\nexport enum ComponentType {\r\n  APPLICATION = 'APPLICATION',\r\n  OPERATING_SYSTEM = 'OPERATING_SYSTEM',\r\n  DATABASE = 'DATABASE',\r\n  WEB_SERVER = 'WEB_SERVER',\r\n  NETWORK_DEVICE = 'NETWORK_DEVICE',\r\n  CONTAINER = 'CONTAINER',\r\n  CLOUD_SERVICE = 'CLOUD_SERVICE',\r\n  API = 'API',\r\n  LIBRARY = 'LIBRARY',\r\n  FRAMEWORK = 'FRAMEWORK',\r\n  PLUGIN = 'PLUGIN',\r\n  SERVICE = 'SERVICE',\r\n}\r\n\r\n/**\r\n * Component Criticality Enum\r\n */\r\nexport enum ComponentCriticality {\r\n  LOW = 'LOW',\r\n  MEDIUM = 'MEDIUM',\r\n  HIGH = 'HIGH',\r\n  CRITICAL = 'CRITICAL',\r\n}\r\n\r\n/**\r\n * Exploit Information Interface\r\n */\r\nexport interface ExploitInfo {\r\n  /** Exploit identifier */\r\n  id: string;\r\n  /** Exploit name */\r\n  name: string;\r\n  /** Exploit type */\r\n  type: ExploitType;\r\n  /** Exploit availability */\r\n  availability: ExploitAvailability;\r\n  /** Exploit complexity */\r\n  complexity: ExploitComplexity;\r\n  /** Exploit reliability */\r\n  reliability: ExploitReliability;\r\n  /** Exploit source */\r\n  source: string;\r\n  /** Exploit references */\r\n  references: string[];\r\n  /** Exploit metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Exploit Type Enum\r\n */\r\nexport enum ExploitType {\r\n  REMOTE = 'REMOTE',\r\n  LOCAL = 'LOCAL',\r\n  WEB = 'WEB',\r\n  CLIENT_SIDE = 'CLIENT_SIDE',\r\n  DENIAL_OF_SERVICE = 'DENIAL_OF_SERVICE',\r\n  PRIVILEGE_ESCALATION = 'PRIVILEGE_ESCALATION',\r\n  CODE_EXECUTION = 'CODE_EXECUTION',\r\n  INFORMATION_DISCLOSURE = 'INFORMATION_DISCLOSURE',\r\n}\r\n\r\n/**\r\n * Exploit Availability Enum\r\n */\r\nexport enum ExploitAvailability {\r\n  PUBLIC = 'PUBLIC',\r\n  PRIVATE = 'PRIVATE',\r\n  COMMERCIAL = 'COMMERCIAL',\r\n  PROOF_OF_CONCEPT = 'PROOF_OF_CONCEPT',\r\n  WEAPONIZED = 'WEAPONIZED',\r\n  IN_THE_WILD = 'IN_THE_WILD',\r\n}\r\n\r\n/**\r\n * Exploit Complexity Enum\r\n */\r\nexport enum ExploitComplexity {\r\n  LOW = 'LOW',\r\n  MEDIUM = 'MEDIUM',\r\n  HIGH = 'HIGH',\r\n}\r\n\r\n/**\r\n * Exploit Reliability Enum\r\n */\r\nexport enum ExploitReliability {\r\n  UNRELIABLE = 'UNRELIABLE',\r\n  AVERAGE = 'AVERAGE',\r\n  GOOD = 'GOOD',\r\n  EXCELLENT = 'EXCELLENT',\r\n}\r\n\r\n/**\r\n * Remediation Information Interface\r\n */\r\nexport interface RemediationInfo {\r\n  /** Remediation type */\r\n  type: RemediationType;\r\n  /** Remediation priority */\r\n  priority: RemediationPriority;\r\n  /** Remediation effort estimate */\r\n  effort: RemediationEffort;\r\n  /** Remediation steps */\r\n  steps: string[];\r\n  /** Remediation timeline */\r\n  timeline: string;\r\n  /** Remediation cost estimate */\r\n  costEstimate?: string;\r\n  /** Remediation resources required */\r\n  resourcesRequired: string[];\r\n  /** Remediation risks */\r\n  risks: string[];\r\n  /** Remediation alternatives */\r\n  alternatives: string[];\r\n  /** Remediation references */\r\n  references: string[];\r\n  /** Remediation metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Remediation Type Enum\r\n */\r\nexport enum RemediationType {\r\n  PATCH = 'PATCH',\r\n  CONFIGURATION_CHANGE = 'CONFIGURATION_CHANGE',\r\n  WORKAROUND = 'WORKAROUND',\r\n  UPGRADE = 'UPGRADE',\r\n  REPLACEMENT = 'REPLACEMENT',\r\n  MITIGATION = 'MITIGATION',\r\n  ACCEPTANCE = 'ACCEPTANCE',\r\n  TRANSFER = 'TRANSFER',\r\n}\r\n\r\n/**\r\n * Remediation Priority Enum\r\n */\r\nexport enum RemediationPriority {\r\n  IMMEDIATE = 'IMMEDIATE',\r\n  HIGH = 'HIGH',\r\n  MEDIUM = 'MEDIUM',\r\n  LOW = 'LOW',\r\n  DEFERRED = 'DEFERRED',\r\n}\r\n\r\n/**\r\n * Remediation Effort Enum\r\n */\r\nexport enum RemediationEffort {\r\n  MINIMAL = 'MINIMAL',\r\n  LOW = 'LOW',\r\n  MEDIUM = 'MEDIUM',\r\n  HIGH = 'HIGH',\r\n  EXTENSIVE = 'EXTENSIVE',\r\n}\r\n\r\n/**\r\n * Vulnerability Reference Interface\r\n */\r\nexport interface VulnerabilityReference {\r\n  /** Reference type */\r\n  type: ReferenceType;\r\n  /** Reference URL */\r\n  url: string;\r\n  /** Reference title */\r\n  title?: string;\r\n  /** Reference source */\r\n  source: string;\r\n  /** Reference date */\r\n  date?: Date;\r\n  /** Reference metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Reference Type Enum\r\n */\r\nexport enum ReferenceType {\r\n  CVE = 'CVE',\r\n  CWE = 'CWE',\r\n  VENDOR_ADVISORY = 'VENDOR_ADVISORY',\r\n  SECURITY_BULLETIN = 'SECURITY_BULLETIN',\r\n  EXPLOIT_DATABASE = 'EXPLOIT_DATABASE',\r\n  RESEARCH_PAPER = 'RESEARCH_PAPER',\r\n  BLOG_POST = 'BLOG_POST',\r\n  CONFERENCE_TALK = 'CONFERENCE_TALK',\r\n  PATCH_NOTES = 'PATCH_NOTES',\r\n  DOCUMENTATION = 'DOCUMENTATION',\r\n}\r\n\r\n/**\r\n * Business Impact Interface\r\n */\r\nexport interface BusinessImpact {\r\n  /** Impact level */\r\n  level: BusinessImpactLevel;\r\n  /** Affected business processes */\r\n  affectedProcesses: string[];\r\n  /** Financial impact estimate */\r\n  financialImpact?: string;\r\n  /** Compliance impact */\r\n  complianceImpact: string[];\r\n  /** Reputation impact */\r\n  reputationImpact: BusinessImpactLevel;\r\n  /** Operational impact */\r\n  operationalImpact: BusinessImpactLevel;\r\n  /** Impact metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Business Impact Level Enum\r\n */\r\nexport enum BusinessImpactLevel {\r\n  NEGLIGIBLE = 'NEGLIGIBLE',\r\n  LOW = 'LOW',\r\n  MEDIUM = 'MEDIUM',\r\n  HIGH = 'HIGH',\r\n  CRITICAL = 'CRITICAL',\r\n}\r\n\r\n/**\r\n * Scan Target Interface\r\n */\r\nexport interface ScanTarget {\r\n  /** Target identifier */\r\n  id: string;\r\n  /** Target type */\r\n  type: ScanTargetType;\r\n  /** Target address/location */\r\n  address: string;\r\n  /** Target name */\r\n  name?: string;\r\n  /** Target description */\r\n  description?: string;\r\n  /** Target metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Scan Target Type Enum\r\n */\r\nexport enum ScanTargetType {\r\n  IP_ADDRESS = 'IP_ADDRESS',\r\n  HOSTNAME = 'HOSTNAME',\r\n  URL = 'URL',\r\n  NETWORK_RANGE = 'NETWORK_RANGE',\r\n  APPLICATION = 'APPLICATION',\r\n  DATABASE = 'DATABASE',\r\n  CONTAINER = 'CONTAINER',\r\n  CLOUD_RESOURCE = 'CLOUD_RESOURCE',\r\n  API_ENDPOINT = 'API_ENDPOINT',\r\n}\r\n\r\n/**\r\n * Scan Statistics Interface\r\n */\r\nexport interface ScanStatistics {\r\n  /** Total vulnerabilities found */\r\n  totalVulnerabilities: number;\r\n  /** Vulnerabilities by severity */\r\n  vulnerabilitiesBySeverity: Record<VulnerabilitySeverity, number>;\r\n  /** Vulnerabilities by category */\r\n  vulnerabilitiesByCategory: Record<VulnerabilityCategory, number>;\r\n  /** Vulnerabilities by type */\r\n  vulnerabilitiesByType: Record<VulnerabilityType, number>;\r\n  /** Scan coverage metrics */\r\n  coverage: {\r\n    portsScanned: number;\r\n    servicesIdentified: number;\r\n    componentsAnalyzed: number;\r\n  };\r\n  /** Performance metrics */\r\n  performance: {\r\n    scanRate: number; // vulnerabilities per second\r\n    throughput: number; // targets per hour\r\n    accuracy: number; // percentage\r\n  };\r\n}\r\n\r\n/**\r\n * Vulnerability Scan Configuration\r\n */\r\nexport interface VulnerabilityScanConfig {\r\n  /** Scan type */\r\n  scanType: VulnerabilityScanType;\r\n  /** Include low severity vulnerabilities */\r\n  includeLowSeverity?: boolean;\r\n  /** Scan timeout in milliseconds */\r\n  timeoutMs?: number;\r\n  /** Maximum concurrent scans */\r\n  maxConcurrentScans?: number;\r\n  /** Scan credentials */\r\n  credentials?: ScanCredentials;\r\n  /** Custom scan rules */\r\n  customRules?: VulnerabilityScanRule[];\r\n  /** Scan exclusions */\r\n  exclusions?: ScanExclusion[];\r\n  /** Scan plugins to use */\r\n  plugins?: string[];\r\n  /** Scan depth level */\r\n  depth?: ScanDepth;\r\n  /** Enable aggressive scanning */\r\n  aggressive?: boolean;\r\n  /** Scan scheduling */\r\n  schedule?: ScanSchedule;\r\n}\r\n\r\n/**\r\n * Vulnerability Scan Type Enum\r\n */\r\nexport enum VulnerabilityScanType {\r\n  QUICK = 'QUICK',\r\n  FULL = 'FULL',\r\n  COMPREHENSIVE = 'COMPREHENSIVE',\r\n  COMPLIANCE = 'COMPLIANCE',\r\n  CUSTOM = 'CUSTOM',\r\n  CONTINUOUS = 'CONTINUOUS',\r\n}\r\n\r\n/**\r\n * Scan Credentials Interface\r\n */\r\nexport interface ScanCredentials {\r\n  /** Credential type */\r\n  type: CredentialType;\r\n  /** Username */\r\n  username?: string;\r\n  /** Password */\r\n  password?: string;\r\n  /** API key */\r\n  apiKey?: string;\r\n  /** Certificate */\r\n  certificate?: string;\r\n  /** Private key */\r\n  privateKey?: string;\r\n  /** Additional credential data */\r\n  additionalData?: Record<string, string>;\r\n}\r\n\r\n/**\r\n * Credential Type Enum\r\n */\r\nexport enum CredentialType {\r\n  USERNAME_PASSWORD = 'USERNAME_PASSWORD',\r\n  API_KEY = 'API_KEY',\r\n  CERTIFICATE = 'CERTIFICATE',\r\n  SSH_KEY = 'SSH_KEY',\r\n  TOKEN = 'TOKEN',\r\n  OAUTH = 'OAUTH',\r\n  CUSTOM = 'CUSTOM',\r\n}\r\n\r\n/**\r\n * Vulnerability Scan Rule Interface\r\n */\r\nexport interface VulnerabilityScanRule {\r\n  /** Rule identifier */\r\n  id: string;\r\n  /** Rule name */\r\n  name: string;\r\n  /** Rule description */\r\n  description: string;\r\n  /** Rule pattern */\r\n  pattern: string;\r\n  /** Rule severity */\r\n  severity: VulnerabilitySeverity;\r\n  /** Rule category */\r\n  category: VulnerabilityCategory;\r\n  /** Rule enabled status */\r\n  enabled: boolean;\r\n  /** Rule metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Scan Exclusion Interface\r\n */\r\nexport interface ScanExclusion {\r\n  /** Exclusion type */\r\n  type: ExclusionType;\r\n  /** Exclusion pattern */\r\n  pattern: string;\r\n  /** Exclusion reason */\r\n  reason: string;\r\n  /** Exclusion metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Exclusion Type Enum\r\n */\r\nexport enum ExclusionType {\r\n  IP_ADDRESS = 'IP_ADDRESS',\r\n  HOSTNAME = 'HOSTNAME',\r\n  PORT = 'PORT',\r\n  SERVICE = 'SERVICE',\r\n  VULNERABILITY_ID = 'VULNERABILITY_ID',\r\n  CATEGORY = 'CATEGORY',\r\n  SEVERITY = 'SEVERITY',\r\n}\r\n\r\n/**\r\n * Scan Depth Enum\r\n */\r\nexport enum ScanDepth {\r\n  SURFACE = 'SURFACE',\r\n  SHALLOW = 'SHALLOW',\r\n  MEDIUM = 'MEDIUM',\r\n  DEEP = 'DEEP',\r\n  EXHAUSTIVE = 'EXHAUSTIVE',\r\n}\r\n\r\n/**\r\n * Scan Schedule Interface\r\n */\r\nexport interface ScanSchedule {\r\n  /** Schedule type */\r\n  type: ScheduleType;\r\n  /** Schedule frequency */\r\n  frequency: string;\r\n  /** Schedule start time */\r\n  startTime: Date;\r\n  /** Schedule end time */\r\n  endTime?: Date;\r\n  /** Schedule metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Schedule Type Enum\r\n */\r\nexport enum ScheduleType {\r\n  ONE_TIME = 'ONE_TIME',\r\n  RECURRING = 'RECURRING',\r\n  CONTINUOUS = 'CONTINUOUS',\r\n  EVENT_DRIVEN = 'EVENT_DRIVEN',\r\n}\r\n\r\n/**\r\n * Vulnerability Scanner Context\r\n */\r\nexport interface VulnerabilityScannerContext {\r\n  /** Scan request ID */\r\n  requestId: string;\r\n  /** Scan configuration */\r\n  config: VulnerabilityScanConfig;\r\n  /** User context */\r\n  userContext?: {\r\n    userId: string;\r\n    tenantId: string;\r\n  };\r\n  /** Scan metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Vulnerability Scanner Interface\r\n * \r\n * Defines the contract for vulnerability scanning and assessment capabilities.\r\n * Supports multiple scan types and provides comprehensive vulnerability intelligence.\r\n */\r\nexport interface VulnerabilityScanner {\r\n  /**\r\n   * Scan event for vulnerabilities\r\n   * @param event - Event to scan\r\n   * @param context - Scanner context\r\n   * @returns Vulnerability scan result\r\n   */\r\n  scanEvent(event: Event, context?: VulnerabilityScannerContext): Promise<VulnerabilityScan>;\r\n\r\n  /**\r\n   * Scan normalized event for vulnerabilities\r\n   * @param normalizedEvent - Normalized event to scan\r\n   * @param context - Scanner context\r\n   * @returns Vulnerability scan result\r\n   */\r\n  scanNormalizedEvent(normalizedEvent: NormalizedEvent, context?: VulnerabilityScannerContext): Promise<VulnerabilityScan>;\r\n\r\n  /**\r\n   * Scan enriched event for vulnerabilities\r\n   * @param enrichedEvent - Enriched event to scan\r\n   * @param context - Scanner context\r\n   * @returns Vulnerability scan result\r\n   */\r\n  scanEnrichedEvent(enrichedEvent: EnrichedEvent, context?: VulnerabilityScannerContext): Promise<VulnerabilityScan>;\r\n\r\n  /**\r\n   * Scan correlated event for vulnerabilities\r\n   * @param correlatedEvent - Correlated event to scan\r\n   * @param context - Scanner context\r\n   * @returns Vulnerability scan result\r\n   */\r\n  scanCorrelatedEvent(correlatedEvent: CorrelatedEvent, context?: VulnerabilityScannerContext): Promise<VulnerabilityScan>;\r\n\r\n  /**\r\n   * Scan specific target for vulnerabilities\r\n   * @param target - Target to scan\r\n   * @param context - Scanner context\r\n   * @returns Vulnerability scan result\r\n   */\r\n  scanTarget(target: ScanTarget, context?: VulnerabilityScannerContext): Promise<VulnerabilityScan>;\r\n\r\n  /**\r\n   * Scan multiple targets for vulnerabilities\r\n   * @param targets - Targets to scan\r\n   * @param context - Scanner context\r\n   * @returns Array of vulnerability scan results\r\n   */\r\n  scanTargets(targets: ScanTarget[], context?: VulnerabilityScannerContext): Promise<VulnerabilityScan[]>;\r\n\r\n  /**\r\n   * Validate a discovered vulnerability\r\n   * @param vulnerability - Vulnerability to validate\r\n   * @param context - Scanner context\r\n   * @returns Validation result\r\n   */\r\n  validateVulnerability(vulnerability: DiscoveredVulnerability, context?: VulnerabilityScannerContext): Promise<{\r\n    isValid: boolean;\r\n    confidence: number;\r\n    validationReasons: string[];\r\n    falsePositiveProbability: number;\r\n  }>;\r\n\r\n  /**\r\n   * Enrich vulnerability with additional information\r\n   * @param vulnerability - Vulnerability to enrich\r\n   * @param context - Scanner context\r\n   * @returns Enriched vulnerability\r\n   */\r\n  enrichVulnerability(vulnerability: DiscoveredVulnerability, context?: VulnerabilityScannerContext): Promise<DiscoveredVulnerability>;\r\n\r\n  /**\r\n   * Get remediation recommendations for vulnerability\r\n   * @param vulnerability - Vulnerability to get recommendations for\r\n   * @param context - Scanner context\r\n   * @returns Remediation recommendations\r\n   */\r\n  getRemediationRecommendations(vulnerability: DiscoveredVulnerability, context?: VulnerabilityScannerContext): Promise<RemediationInfo[]>;\r\n\r\n  /**\r\n   * Get vulnerability intelligence\r\n   * @param vulnerabilityId - Vulnerability ID to lookup\r\n   * @param context - Scanner context\r\n   * @returns Vulnerability intelligence data\r\n   */\r\n  getVulnerabilityIntelligence(vulnerabilityId: string, context?: VulnerabilityScannerContext): Promise<{\r\n    vulnerability: DiscoveredVulnerability;\r\n    exploits: ExploitInfo[];\r\n    patches: string[];\r\n    references: VulnerabilityReference[];\r\n    relatedVulnerabilities: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Update vulnerability scan rules\r\n   * @param rules - New or updated scan rules\r\n   * @param context - Scanner context\r\n   * @returns Update result\r\n   */\r\n  updateScanRules(rules: VulnerabilityScanRule[], context?: VulnerabilityScannerContext): Promise<{\r\n    updated: number;\r\n    failed: number;\r\n    errors: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Get current vulnerability scanner configuration\r\n   * @returns Current scanner configuration\r\n   */\r\n  getScannerConfig(): Promise<VulnerabilityScanConfig>;\r\n\r\n  /**\r\n   * Update vulnerability scanner configuration\r\n   * @param config - New scanner configuration\r\n   * @returns Configuration update result\r\n   */\r\n  updateScannerConfig(config: Partial<VulnerabilityScanConfig>): Promise<{\r\n    updated: boolean;\r\n    config: VulnerabilityScanConfig;\r\n    timestamp: Date;\r\n  }>;\r\n\r\n  /**\r\n   * Get vulnerability scanner health status\r\n   * @returns Scanner system health\r\n   */\r\n  getScannerHealth(): Promise<{\r\n    status: 'healthy' | 'degraded' | 'unhealthy';\r\n    scanEngines: Record<string, {\r\n      status: 'healthy' | 'degraded' | 'unhealthy';\r\n      latency: number;\r\n      accuracy: number;\r\n      lastUpdate: Date;\r\n    }>;\r\n    vulnerabilityDatabases: Record<string, {\r\n      status: 'healthy' | 'degraded' | 'unhealthy';\r\n      lastUpdate: Date;\r\n      recordCount: number;\r\n    }>;\r\n    lastHealthCheck: Date;\r\n  }>;\r\n\r\n  /**\r\n   * Schedule vulnerability scan\r\n   * @param target - Target to scan\r\n   * @param schedule - Scan schedule\r\n   * @param context - Scanner context\r\n   * @returns Schedule confirmation\r\n   */\r\n  scheduleScan(target: ScanTarget, schedule: ScanSchedule, context?: VulnerabilityScannerContext): Promise<{\r\n    scheduled: boolean;\r\n    scheduleId: string;\r\n    nextScanTime: Date;\r\n  }>;\r\n\r\n  /**\r\n   * Cancel scheduled vulnerability scan\r\n   * @param scheduleId - Schedule ID to cancel\r\n   * @param reason - Cancellation reason\r\n   * @returns Cancellation confirmation\r\n   */\r\n  cancelScheduledScan(scheduleId: string, reason?: string): Promise<{\r\n    cancelled: boolean;\r\n    reason: string;\r\n    timestamp: Date;\r\n  }>;\r\n} "], "version": 3}