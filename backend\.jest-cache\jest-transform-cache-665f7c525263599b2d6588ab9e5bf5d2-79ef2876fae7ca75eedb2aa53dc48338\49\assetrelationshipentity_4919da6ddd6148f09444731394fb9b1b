d34ae5606aa4214e92c2043c15e6bdbc
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetRelationship = void 0;
const typeorm_1 = require("typeorm");
const asset_entity_1 = require("./asset.entity");
/**
 * Asset Relationship entity
 * Represents relationships and dependencies between assets
 */
let AssetRelationship = class AssetRelationship {
    /**
     * Check if relationship is critical
     */
    get isCritical() {
        return this.criticality === 'critical';
    }
    /**
     * Check if relationship is bidirectional
     */
    get isBidirectional() {
        return this.direction === 'bidirectional';
    }
    /**
     * Check if relationship is a hard dependency
     */
    get isHardDependency() {
        return this.details?.dependency?.type === 'hard';
    }
    /**
     * Get relationship age in days
     */
    get ageInDays() {
        const now = new Date();
        const diffMs = now.getTime() - this.discoveredAt.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Get days since last verification
     */
    get daysSinceLastVerification() {
        if (!this.lastVerifiedAt)
            return Infinity;
        const now = new Date();
        const diffMs = now.getTime() - this.lastVerifiedAt.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Check if relationship needs verification
     */
    get needsVerification() {
        // Relationships should be verified based on criticality
        const verificationIntervals = {
            critical: 7, // 7 days
            high: 30, // 30 days
            medium: 90, // 90 days
            low: 180, // 180 days
        };
        const interval = verificationIntervals[this.criticality];
        return this.daysSinceLastVerification > interval;
    }
    /**
     * Activate relationship
     */
    activate(updatedBy) {
        this.isActive = true;
        this.updatedBy = updatedBy;
    }
    /**
     * Deactivate relationship
     */
    deactivate(updatedBy) {
        this.isActive = false;
        this.updatedBy = updatedBy;
    }
    /**
     * Update relationship criticality
     */
    setCriticality(criticality, updatedBy) {
        this.criticality = criticality;
        this.updatedBy = updatedBy;
    }
    /**
     * Verify relationship
     */
    verify(updatedBy) {
        this.lastVerifiedAt = new Date();
        this.updatedBy = updatedBy;
    }
    /**
     * Add tag to relationship
     */
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }
    /**
     * Remove tag from relationship
     */
    removeTag(tag) {
        this.tags = this.tags.filter(t => t !== tag);
    }
    /**
     * Update relationship details
     */
    updateDetails(updates, updatedBy) {
        this.details = {
            ...this.details,
            ...updates,
        };
        this.updatedBy = updatedBy;
    }
    /**
     * Set custom attribute
     */
    setCustomAttribute(key, value) {
        if (!this.customAttributes) {
            this.customAttributes = {};
        }
        this.customAttributes[key] = value;
    }
    /**
     * Get custom attribute
     */
    getCustomAttribute(key) {
        return this.customAttributes?.[key];
    }
    /**
     * Calculate relationship risk score
     */
    calculateRiskScore() {
        let score = 0;
        // Base score from criticality
        const criticalityScores = { low: 2, medium: 5, high: 8, critical: 10 };
        score += criticalityScores[this.criticality];
        // Dependency type factor
        if (this.isHardDependency) {
            score += 2;
        }
        // Verification status
        if (this.needsVerification) {
            score += 1;
        }
        // Confidence factor
        const confidenceFactors = { low: 1, medium: 0, high: -0.5 };
        score += confidenceFactors[this.confidence];
        // Active status
        if (!this.isActive) {
            score += 1;
        }
        return Math.max(0, Math.min(10, score));
    }
    /**
     * Get relationship summary
     */
    getSummary() {
        return {
            id: this.id,
            relationshipType: this.relationshipType,
            sourceAssetId: this.sourceAssetId,
            targetAssetId: this.targetAssetId,
            direction: this.direction,
            criticality: this.criticality,
            isActive: this.isActive,
            isCritical: this.isCritical,
            isBidirectional: this.isBidirectional,
            isHardDependency: this.isHardDependency,
            needsVerification: this.needsVerification,
            weight: this.weight,
            confidence: this.confidence,
            discoveryMethod: this.discoveryMethod,
            ageInDays: this.ageInDays,
            daysSinceLastVerification: this.daysSinceLastVerification,
            riskScore: this.calculateRiskScore(),
            discoveredAt: this.discoveredAt,
            lastVerifiedAt: this.lastVerifiedAt,
            tags: this.tags,
        };
    }
    /**
     * Export relationship for reporting
     */
    exportForReporting() {
        return {
            relationship: this.getSummary(),
            details: this.details,
            customAttributes: this.customAttributes,
            exportedAt: new Date().toISOString(),
        };
    }
    /**
     * Get reverse relationship type
     */
    getReverseRelationshipType() {
        const reverseMap = {
            depends_on: 'supports',
            hosts: 'hosted_by',
            connects_to: 'connected_from',
            communicates_with: 'communicates_with',
            manages: 'managed_by',
            monitors: 'monitored_by',
            backs_up: 'backed_up_by',
            replicates_to: 'replicated_from',
            load_balances: 'load_balanced_by',
            proxies_to: 'proxied_by',
            authenticates_with: 'authenticates',
            stores_data_on: 'stores_data_from',
            processes_data_from: 'processes_data_to',
            serves_content_to: 'receives_content_from',
            routes_traffic_to: 'receives_traffic_from',
            clusters_with: 'clusters_with',
            mirrors: 'mirrored_by',
            synchronizes_with: 'synchronizes_with',
            inherits_from: 'inherited_by',
            extends: 'extended_by',
            contains: 'contained_by',
            part_of: 'contains',
            similar_to: 'similar_to',
            replaces: 'replaced_by',
            custom: 'custom',
        };
        return reverseMap[this.relationshipType] || this.relationshipType;
    }
};
exports.AssetRelationship = AssetRelationship;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AssetRelationship.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'relationship_type',
        type: 'enum',
        enum: [
            'depends_on',
            'hosts',
            'connects_to',
            'communicates_with',
            'manages',
            'monitors',
            'backs_up',
            'replicates_to',
            'load_balances',
            'proxies_to',
            'authenticates_with',
            'stores_data_on',
            'processes_data_from',
            'serves_content_to',
            'routes_traffic_to',
            'clusters_with',
            'mirrors',
            'synchronizes_with',
            'inherits_from',
            'extends',
            'contains',
            'part_of',
            'similar_to',
            'replaces',
            'custom',
        ],
    }),
    __metadata("design:type", String)
], AssetRelationship.prototype, "relationshipType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AssetRelationship.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_active', default: true }),
    __metadata("design:type", Boolean)
], AssetRelationship.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium',
    }),
    __metadata("design:type", String)
], AssetRelationship.prototype, "criticality", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['unidirectional', 'bidirectional'],
        default: 'unidirectional',
    }),
    __metadata("design:type", String)
], AssetRelationship.prototype, "direction", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AssetRelationship.prototype, "details", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 2, default: 1.0 }),
    __metadata("design:type", Number)
], AssetRelationship.prototype, "weight", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['low', 'medium', 'high'],
        default: 'medium',
    }),
    __metadata("design:type", String)
], AssetRelationship.prototype, "confidence", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'discovery_method',
        type: 'enum',
        enum: ['network_scan', 'agent', 'configuration', 'manual', 'inference', 'monitoring'],
        default: 'manual',
    }),
    __metadata("design:type", String)
], AssetRelationship.prototype, "discoveryMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], AssetRelationship.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'custom_attributes', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], AssetRelationship.prototype, "customAttributes", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'discovered_at', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], AssetRelationship.prototype, "discoveredAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_verified_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], AssetRelationship.prototype, "lastVerifiedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], AssetRelationship.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], AssetRelationship.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], AssetRelationship.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], AssetRelationship.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => asset_entity_1.Asset, asset => asset.sourceRelationships, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'source_asset_id' }),
    __metadata("design:type", typeof (_f = typeof asset_entity_1.Asset !== "undefined" && asset_entity_1.Asset) === "function" ? _f : Object)
], AssetRelationship.prototype, "sourceAsset", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'source_asset_id', type: 'uuid' }),
    __metadata("design:type", String)
], AssetRelationship.prototype, "sourceAssetId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => asset_entity_1.Asset, asset => asset.targetRelationships, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'target_asset_id' }),
    __metadata("design:type", typeof (_g = typeof asset_entity_1.Asset !== "undefined" && asset_entity_1.Asset) === "function" ? _g : Object)
], AssetRelationship.prototype, "targetAsset", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'target_asset_id', type: 'uuid' }),
    __metadata("design:type", String)
], AssetRelationship.prototype, "targetAssetId", void 0);
exports.AssetRelationship = AssetRelationship = __decorate([
    (0, typeorm_1.Entity)('asset_relationships'),
    (0, typeorm_1.Index)(['sourceAssetId']),
    (0, typeorm_1.Index)(['targetAssetId']),
    (0, typeorm_1.Index)(['relationshipType']),
    (0, typeorm_1.Index)(['isActive']),
    (0, typeorm_1.Index)(['criticality'])
], AssetRelationship);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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