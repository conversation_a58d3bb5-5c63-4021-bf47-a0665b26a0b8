{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\monitoring-infrastructure.integration.spec.ts", "mappings": ";;AAEA,yDAAsD;AACtD,6DAAwD;AACxD,0EAAsE;AACtE,qGAAgG;AAChG,yFAAoF;AACpF,iFAA6E;AAC7E,yFAAqF;AACrF,2EAAiE;AACjE,uFAA4E;AAC5E,yEAA+D;AAE/D;;;;;;;;;;;;GAYG;AACH,QAAQ,CAAC,6CAA6C,EAAE,GAAG,EAAE;IAC3D,IAAI,GAAqB,CAAC;IAC1B,IAAI,aAA4B,CAAC;IACjC,IAAI,cAAwC,CAAC;IAC7C,IAAI,kBAAsC,CAAC;IAC3C,IAAI,eAAgC,CAAC;IACrC,IAAI,mBAAwC,CAAC;IAC7C,IAAI,YAA2B,CAAC;IAChC,IAAI,QAAuB,CAAC;IAE5B,gBAAgB;IAChB,MAAM,YAAY,GAAG,CAAC,sBAAM,EAAE,iCAAW,EAAE,oBAAK,CAAC,CAAC;IAElD,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,4BAA4B;QAC5B,QAAQ,GAAG,IAAI,CAAC,KAAM,SAAQ,+BAAa;SAAG,CAAC,EAAE,CAAC;QAElD,yBAAyB;QACzB,MAAM,QAAQ,CAAC,oBAAoB,CACjC;YACE,OAAO,EAAE,CAAC,oCAAgB,CAAC;SAC5B,EACD,YAAY,CACb,CAAC;QAEF,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;QACnB,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;QAEvC,wBAAwB;QACxB,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,qDAAwB,CAAC,CAAC;QAC/D,kBAAkB,GAAG,QAAQ,CAAC,UAAU,CAAC,yCAAkB,CAAC,CAAC;QAC7D,eAAe,GAAG,QAAQ,CAAC,UAAU,CAAC,kCAAe,CAAC,CAAC;QACvD,mBAAmB,GAAG,QAAQ,CAAC,UAAU,CAAC,0CAAmB,CAAC,CAAC;QAC/D,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,6BAAa,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,QAAQ,CAAC,sBAAsB,EAAE,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,QAAQ,CAAC,aAAa,EAAE,CAAC;QAC/B,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,QAAQ,CAAC,eAAe,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,UAAU;YACV,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,cAAc;gBACpB,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,UAAU;gBACpB,IAAI,EAAE,SAAkB;gBACxB,MAAM,EAAE;oBACN,OAAO,EAAE,cAAc;oBACvB,WAAW,EAAE,MAAM;iBACpB;gBACD,MAAM,EAAE,kBAAkB;aAC3B,CAAC;YAEF,MAAM;YACN,MAAM,cAAc,GAAG,MAAM,cAAc,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAErE,SAAS;YACT,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;YACrC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAEpD,8BAA8B;YAC9B,MAAM,QAAQ,CAAC,mBAAmB,CAAC,sBAAM,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;YAEzE,iCAAiC;YACjC,MAAM,gBAAgB,GAAG,MAAM,cAAc,CAAC,UAAU,CAAC;gBACvD,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,CAAC;aACV,CAAC,CAAC;YAEH,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;gBAC7C,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,QAAQ,EAAE,UAAU,CAAC,QAAQ;gBAC7B,IAAI,EAAE,UAAU,CAAC,IAAI;aACtB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,UAAU;YACV,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,eAAe;gBACrB,QAAQ,EAAE,WAAoB;gBAC9B,IAAI,EAAE,SAAkB;gBACxB,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;gBAC1B,MAAM,EAAE,MAAM;aACf,CAAC;YAEF,0BAA0B;YAC1B,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,cAAc,CAAC,YAAY,CAAC,EAAE,GAAG,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;gBACzD,cAAc,CAAC,YAAY,CAAC,EAAE,GAAG,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;gBACzD,cAAc,CAAC,YAAY,CAAC,EAAE,GAAG,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;aAC1D,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,iBAAiB,GAAG,MAAM,cAAc,CAAC,oBAAoB,CAAC;gBAClE,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,KAAK;gBAClB,OAAO,EAAE,CAAC,SAAS,CAAC;gBACpB,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,EAAE,aAAa;oBACpD,GAAG,EAAE,IAAI,IAAI,EAAE;iBAChB;aACF,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;gBAC9C,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;gBAC1B,KAAK,EAAE,EAAE,EAAE,eAAe;gBAC1B,WAAW,EAAE,KAAK;aACnB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,UAAU;YACV,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,cAAc,CAAC,YAAY,CAAC;oBAC1B,IAAI,EAAE,qBAAqB;oBAC3B,KAAK,EAAE,GAAG;oBACV,QAAQ,EAAE,WAAW;oBACrB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;oBACxC,MAAM,EAAE,MAAM;iBACf,CAAC;gBACF,cAAc,CAAC,YAAY,CAAC;oBAC1B,IAAI,EAAE,uBAAuB;oBAC7B,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,aAAa;oBACvB,IAAI,EAAE,WAAW;oBACjB,MAAM,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE;oBACjC,MAAM,EAAE,MAAM;iBACf,CAAC;aACH,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,gBAAgB,GAAG,MAAM,cAAc,CAAC,uBAAuB,EAAE,CAAC;YAExE,SAAS;YACT,MAAM,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;YACzE,MAAM,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,oDAAoD,CAAC,CAAC;YACzF,MAAM,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;YAC7E,MAAM,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,kDAAkD,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,UAAU;YACV,MAAM,WAAW,GAAG,IAAI,CAAC;YACzB,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,uBAAuB;gBAC7B,QAAQ,EAAE,aAAsB;gBAChC,IAAI,EAAE,OAAgB;gBACtB,MAAM,EAAE,kBAAkB;aAC3B,CAAC;YAEF,MAAM;YACN,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ,CAAC,oBAAoB,CAAC,KAAK,IAAI,EAAE;gBACxF,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC5D,cAAc,CAAC,YAAY,CAAC;oBAC1B,GAAG,UAAU;oBACb,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;oBAC1B,MAAM,EAAE,EAAE,QAAQ,EAAE,YAAY,CAAC,GAAG,EAAE,EAAE,EAAE;iBAC3C,CAAC,CACH,CAAC;gBACF,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAC1C,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,oCAAoC;YAE/E,8BAA8B;YAC9B,MAAM,QAAQ,CAAC,mBAAmB,CAAC,sBAAM,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,EAAE,WAAW,CAAC,CAAC;YAEnF,OAAO,CAAC,GAAG,CAAC,gCAAgC,WAAW,YAAY,EAAE;gBACnE,SAAS,EAAE,GAAG,aAAa,IAAI;gBAC/B,oBAAoB,EAAE,GAAG,aAAa,GAAG,WAAW,IAAI;gBACxD,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,IAAI,CAAC;aACnE,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,UAAU;YACV,MAAM,iBAAiB,GAAG;gBACxB,IAAI,EAAE,qBAAqB;gBAC3B,QAAQ,EAAE,aAAsB;gBAChC,WAAW,EAAE,2BAA2B;gBACxC,QAAQ,EAAE,8BAA8B;gBACxC,MAAM,EAAE,KAAc;gBACtB,aAAa,EAAE;oBACb,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,CAAC;oBACV,cAAc,EAAE,CAAC,GAAG,CAAC;iBACtB;gBACD,QAAQ,EAAE;oBACR,QAAQ,EAAE,EAAE;oBACZ,OAAO,EAAE,IAAI;iBACd;gBACD,UAAU,EAAE;oBACV,YAAY,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE;oBACjE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE;iBAC3D;aACF,CAAC;YAEF,MAAM;YACN,MAAM,WAAW,GAAG,MAAM,kBAAkB,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAElF,SAAS;YACT,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YACrC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEzC,8BAA8B;YAC9B,MAAM,QAAQ,CAAC,mBAAmB,CAAC,iCAAW,EAAE,EAAE,IAAI,EAAE,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;YAErF,uBAAuB;YACvB,MAAM,MAAM,GAAG,MAAM,kBAAkB,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAC3E,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,UAAU;YACV,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,qBAAqB,CAAC;gBACvD,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,mCAAmC,EAAE,iBAAiB;gBAChE,UAAU,EAAE;oBACV,YAAY,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE;oBAC9D,YAAY,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE;iBAC3D;gBACD,UAAU,EAAE,IAAI;aACjB,CAAC,CAAC;YAEH,0BAA0B;YAC1B,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;YAElE,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,kBAAkB,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAE3E,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE/C,6DAA6D;YAC7D,MAAM,CAAC,cAAc,CAAC,CAAC,oBAAoB,CACzC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,CAAC;gBACpD,QAAQ,EAAE,UAAU;gBACpB,QAAQ,EAAE,QAAQ;gBAClB,MAAM,EAAE,cAAc;gBACtB,QAAQ,EAAE,WAAW,CAAC,EAAE;aACzB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,UAAU;YACV,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACrC,QAAQ,CAAC,qBAAqB,CAAC;oBAC7B,IAAI,EAAE,WAAW;oBACjB,UAAU,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,EAAE;oBACpD,OAAO,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,mBAAmB,EAAE,GAAG,EAAE;oBACzD,UAAU,EAAE,IAAI;iBACjB,CAAC;gBACF,QAAQ,CAAC,qBAAqB,CAAC;oBAC7B,IAAI,EAAE,WAAW;oBACjB,UAAU,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE;oBACtD,OAAO,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE;oBAC1D,UAAU,EAAE,KAAK;iBAClB,CAAC;gBACF,QAAQ,CAAC,qBAAqB,CAAC;oBAC7B,IAAI,EAAE,WAAW;oBACjB,UAAU,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,IAAI,EAAE;oBACvD,OAAO,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE;oBAC1D,UAAU,EAAE,IAAI;iBACjB,CAAC;aACH,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,aAAa,GAAG,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;YAExE,SAAS;YACT,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;YACvE,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YACrD,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,sCAAsC;QACjG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,UAAU;YACV,MAAM,SAAS,GAAG;gBAChB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,8BAA8B;gBAC3C,QAAQ,EAAE,SAAkB;gBAC5B,QAAQ,EAAE,QAAiB;gBAC3B,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,gBAAgB;gBAC1B,UAAU,EAAE;oBACV,MAAM,EAAE,mBAAmB;oBAC3B,QAAQ,EAAE,GAAG;oBACb,SAAS,EAAE,EAAE;oBACb,QAAQ,EAAE,GAAG;iBACd;gBACD,OAAO,EAAE;oBACP,MAAM,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE;oBACpD,WAAW,EAAE;wBACX,OAAO,EAAE,4CAA4C;wBACrD,SAAS,EAAE,2CAA2C;qBACvD;iBACF;aACF,CAAC;YAEF,MAAM;YACN,MAAM,KAAK,GAAG,MAAM,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YAE3D,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5B,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/B,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEpC,8BAA8B;YAC9B,MAAM,QAAQ,CAAC,mBAAmB,CAAC,oBAAK,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;YAEvE,0CAA0C;YAC1C,MAAM,QAAQ,CAAC,kBAAkB,CAAC,eAAe,EAAE;gBACjD,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,QAAQ,EAAE,SAAS,CAAC,QAAQ;aAC7B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,UAAU;YACV,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,eAAe,CAAC;gBAC3C,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,UAAU;gBACpB,MAAM,EAAE,QAAQ;aACjB,CAAC,CAAC;YAEH,0BAA0B;YAC1B,MAAM,iBAAiB,GAAG,MAAM,eAAe,CAAC,gBAAgB,CAC9D,KAAK,CAAC,EAAE,EACR,QAAQ,CAAC,QAAQ,CAAC,EAAE,EACpB,yBAAyB,EACzB,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,gCAAgC;aAChE,CAAC;YAEF,wBAAwB;YACxB,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACtD,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACnF,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAE/E,sBAAsB;YACtB,MAAM,aAAa,GAAG,MAAM,eAAe,CAAC,YAAY,CACtD,KAAK,CAAC,EAAE,EACR,QAAQ,CAAC,QAAQ,CAAC,EAAE,EACpB,OAAO,EACP,sCAAsC,EACtC,+BAA+B,CAChC,CAAC;YAEF,oBAAoB;YACpB,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACvE,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE1D,sCAAsC;YACtC,MAAM,QAAQ,CAAC,kBAAkB,CAAC,gBAAgB,EAAE;gBAClD,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,UAAU,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE;aACjC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,UAAU;YACV,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,eAAe,CAAC;gBAC3C,IAAI,EAAE,kBAAkB;gBACxB,QAAQ,EAAE,SAAS;gBACnB,MAAM,EAAE,QAAQ;gBAChB,eAAe,EAAE,CAAC;gBAClB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,EAAE,aAAa;aACzD,CAAC,CAAC;YAEH,wBAAwB;YACxB,IAAI,CAAC,KAAK,CAAC,eAAsB,EAAE,oBAAoB,CAAC,CAAC,eAAe,CAAC;gBACvE;oBACE,KAAK,EAAE,CAAC;oBACR,YAAY,EAAE,EAAE;oBAChB,QAAQ,EAAE,UAAU;oBACpB,UAAU,EAAE,CAAC,oBAAoB,CAAC;iBACnC;gBACD;oBACE,KAAK,EAAE,CAAC;oBACR,YAAY,EAAE,EAAE;oBAChB,QAAQ,EAAE,WAAW;oBACrB,UAAU,EAAE,CAAC,qBAAqB,CAAC;iBACpC;aACF,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,cAAc,GAAG,MAAM,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAErE,SAAS;YACT,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEjD,sCAAsC;YACtC,MAAM,QAAQ,CAAC,kBAAkB,CAAC,iBAAiB,EAAE;gBACnD,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,eAAe,EAAE,CAAC;gBAClB,WAAW,EAAE,UAAU;aACxB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,UAAU;YACV,MAAM,aAAa,GAAG;gBACpB,QAAQ,EAAE,QAAiB;gBAC3B,MAAM,EAAE,SAAS;gBACjB,OAAO,EAAE;oBACP,MAAM,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE;iBAClC;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC/B,QAAQ,CAAC,eAAe,CAAC;oBACvB,GAAG,aAAa;oBAChB,IAAI,EAAE,gBAAgB;oBACtB,UAAU,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE;iBAClE,CAAC;gBACF,QAAQ,CAAC,eAAe,CAAC;oBACvB,GAAG,aAAa;oBAChB,IAAI,EAAE,mBAAmB;oBACzB,UAAU,EAAE,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE;iBACrE,CAAC;gBACF,QAAQ,CAAC,eAAe,CAAC;oBACvB,GAAG,aAAa;oBAChB,IAAI,EAAE,oBAAoB;oBAC1B,UAAU,EAAE,EAAE,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE;iBACxE,CAAC;aACH,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,gBAAgB,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAE7E,SAAS;YACT,MAAM,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACnD,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjF,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,UAAU;YACV,MAAM,gBAAgB,GAAG;gBACvB,IAAI,EAAE,OAAgB;gBACtB,QAAQ,EAAE,UAAmB;gBAC7B,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,4BAA4B;gBACrC,UAAU,EAAE;oBACV,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,mBAAmB,EAAE;oBAC/C,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE;oBACrC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,oCAAoC,EAAE;iBACnE;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE,WAAW;oBACpB,MAAM,EAAE,cAAc;oBACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC;YAEF,8BAA8B;YAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,mBAA0B,EAAE,uBAAuB,CAAC;iBAC7E,iBAAiB,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC,CAAC;YAChE,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,mBAA0B,EAAE,uBAAuB,CAAC;iBAC7E,iBAAiB,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC,CAAC;YAChE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,mBAA0B,EAAE,yBAAyB,CAAC;iBACjF,iBAAiB,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAC;YAElE,MAAM;YACN,MAAM,OAAO,GAAG,MAAM,mBAAmB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE7E,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3D,MAAM,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACnC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,EAAE,EAAE,mBAAmB;gBACvB,OAAO,EAAE,gBAAgB,CAAC,KAAK;gBAC/B,IAAI,EAAE,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,CAAC;aACxD,CAAC,CACH,CAAC;YAEF,MAAM,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACnC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,CAAC;aACxD,CAAC,CACH,CAAC;YAEF,MAAM,CAAC,UAAU,CAAC,CAAC,oBAAoB,CACrC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,GAAG,EAAE,oCAAoC;gBACzC,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,KAAK,EAAE,gBAAgB,CAAC,KAAK;oBAC7B,OAAO,EAAE,gBAAgB,CAAC,OAAO;iBAClC,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,UAAU;YACV,MAAM,gBAAgB,GAAG;gBACvB,IAAI,EAAE,OAAgB;gBACtB,QAAQ,EAAE,SAAkB;gBAC5B,KAAK,EAAE,mBAAmB;gBAC1B,OAAO,EAAE,cAAc;gBACvB,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;aAC7D,CAAC;YAEF,yCAAyC;YACzC,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,mBAA0B,EAAE,uBAAuB,CAAC;iBAC5D,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBAC7B,YAAY,EAAE,CAAC;gBACf,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;oBACrB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;gBACvC,CAAC;gBACD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,iBAAiB,EAAE,CAAC;YACzD,CAAC,CAAC,CAAC;YAEL,MAAM;YACN,MAAM,OAAO,GAAG,MAAM,mBAAmB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE7E,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,2CAA2C;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM;YACN,MAAM,eAAe,GAAG,MAAM,cAAc,CAAC,oBAAoB,EAAE,CAAC;YAEpE,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,CAAC,eAAe,CAAC,CAAC,aAAa,CAAC;gBACpC,MAAM,EAAE;oBACN,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACxB;gBACD,GAAG,EAAE;oBACH,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACxB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC3B;gBACD,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;aAC/B,CAAC,CAAC;YAEH,+BAA+B;YAC/B,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,UAAU,CAAC;gBACpD,IAAI,EAAE,qBAAqB;gBAC3B,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC;aACV,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,+BAA+B;YAC/B,MAAM,iBAAiB,GAAG,KAAK,IAAI,EAAE;gBACnC,mCAAmC;gBACnC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,GAAG,EAAE,CAAC;oBAChC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChC,CAAC;YACH,CAAC,CAAC;YAEF,MAAM;YACN,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ,CAAC,oBAAoB,CAAC,KAAK,IAAI,EAAE;gBACvE,MAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;YAC3E,CAAC,CAAC,CAAC;YAEH,4BAA4B;YAC5B,MAAM,cAAc,CAAC,YAAY,CAAC;gBAChC,IAAI,EAAE,oBAAoB;gBAC1B,KAAK,EAAE,aAAa;gBACpB,QAAQ,EAAE,aAAa;gBACvB,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,kBAAkB;aAC3B,CAAC,CAAC;YAEH,4BAA4B;YAC5B,MAAM,mBAAmB,GAAG,MAAM,cAAc,CAAC,kBAAkB,CAAC;gBAClE,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,EAAE,gBAAgB;oBACtD,GAAG,EAAE,IAAI,IAAI,EAAE;iBAChB;gBACD,OAAO,EAAE,CAAC,oBAAoB,CAAC;aAChC,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YACtD,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\monitoring-infrastructure.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { EventEmitter2 } from '@nestjs/event-emitter';\r\nimport { BaseTestClass } from '../base/base-test.class';\r\nimport { MonitoringModule } from '../../monitoring/monitoring.module';\r\nimport { MetricsCollectionService } from '../../monitoring/services/metrics-collection.service';\r\nimport { HealthCheckService } from '../../monitoring/services/health-check.service';\r\nimport { AlertingService } from '../../monitoring/services/alerting.service';\r\nimport { NotificationService } from '../../monitoring/services/notification.service';\r\nimport { Metric } from '../../monitoring/entities/metric.entity';\r\nimport { HealthCheck } from '../../monitoring/entities/health-check.entity';\r\nimport { Alert } from '../../monitoring/entities/alert.entity';\r\n\r\n/**\r\n * Monitoring Infrastructure Integration Tests\r\n * \r\n * Comprehensive monitoring infrastructure testing providing:\r\n * - Metrics collection validation with Prometheus compatibility\r\n * - Health check verification with automated execution and alerting\r\n * - Alerting system testing with multi-channel notification validation\r\n * - Performance monitoring with resource utilization tracking\r\n * - Circuit breaker functionality testing with failure simulation\r\n * - Real-time dashboard API validation with streaming data\r\n * - Observability infrastructure testing with distributed tracing\r\n * - Integration with external monitoring systems validation\r\n */\r\ndescribe('Monitoring Infrastructure Integration Tests', () => {\r\n  let app: INestApplication;\r\n  let testingModule: TestingModule;\r\n  let metricsService: MetricsCollectionService;\r\n  let healthCheckService: HealthCheckService;\r\n  let alertingService: AlertingService;\r\n  let notificationService: NotificationService;\r\n  let eventEmitter: EventEmitter2;\r\n  let baseTest: BaseTestClass;\r\n\r\n  // Test entities\r\n  const testEntities = [Metric, HealthCheck, Alert];\r\n\r\n  beforeAll(async () => {\r\n    // Create base test instance\r\n    baseTest = new (class extends BaseTestClass {})();\r\n\r\n    // Setup test environment\r\n    await baseTest.setupTestEnvironment(\r\n      {\r\n        imports: [MonitoringModule],\r\n      },\r\n      testEntities\r\n    );\r\n\r\n    app = baseTest.app;\r\n    testingModule = baseTest.testingModule;\r\n\r\n    // Get service instances\r\n    metricsService = baseTest.getService(MetricsCollectionService);\r\n    healthCheckService = baseTest.getService(HealthCheckService);\r\n    alertingService = baseTest.getService(AlertingService);\r\n    notificationService = baseTest.getService(NotificationService);\r\n    eventEmitter = baseTest.getService(EventEmitter2);\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await baseTest.cleanupTestEnvironment();\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    await baseTest.setupTestData();\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  afterEach(async () => {\r\n    await baseTest.cleanupTestData();\r\n  });\r\n\r\n  describe('Metrics Collection', () => {\r\n    it('should record and retrieve metrics successfully', async () => {\r\n      // Arrange\r\n      const metricData = {\r\n        name: 'test_counter',\r\n        value: 1,\r\n        category: 'business',\r\n        type: 'counter' as const,\r\n        labels: {\r\n          service: 'test-service',\r\n          environment: 'test',\r\n        },\r\n        source: 'integration-test',\r\n      };\r\n\r\n      // Act\r\n      const recordedMetric = await metricsService.recordMetric(metricData);\r\n\r\n      // Assert\r\n      expect(recordedMetric).toBeDefined();\r\n      expect(recordedMetric.id).toBeDefined();\r\n      expect(recordedMetric.name).toBe(metricData.name);\r\n      expect(recordedMetric.value).toBe(metricData.value);\r\n\r\n      // Verify database persistence\r\n      await baseTest.assertDatabaseState(Metric, { name: metricData.name }, 1);\r\n\r\n      // Verify metric can be retrieved\r\n      const retrievedMetrics = await metricsService.getMetrics({\r\n        name: metricData.name,\r\n        limit: 10,\r\n        offset: 0,\r\n      });\r\n\r\n      expect(retrievedMetrics.data).toHaveLength(1);\r\n      expect(retrievedMetrics.data[0]).toMatchObject({\r\n        name: metricData.name,\r\n        value: metricData.value,\r\n        category: metricData.category,\r\n        type: metricData.type,\r\n      });\r\n    });\r\n\r\n    it('should aggregate metrics correctly', async () => {\r\n      // Arrange\r\n      const baseMetric = {\r\n        name: 'request_count',\r\n        category: 'technical' as const,\r\n        type: 'counter' as const,\r\n        labels: { service: 'api' },\r\n        source: 'test',\r\n      };\r\n\r\n      // Record multiple metrics\r\n      await Promise.all([\r\n        metricsService.recordMetric({ ...baseMetric, value: 10 }),\r\n        metricsService.recordMetric({ ...baseMetric, value: 15 }),\r\n        metricsService.recordMetric({ ...baseMetric, value: 20 }),\r\n      ]);\r\n\r\n      // Act\r\n      const aggregatedMetrics = await metricsService.getAggregatedMetrics({\r\n        name: 'request_count',\r\n        aggregation: 'sum',\r\n        groupBy: ['service'],\r\n        timeRange: {\r\n          start: new Date(Date.now() - 3600000), // 1 hour ago\r\n          end: new Date(),\r\n        },\r\n      });\r\n\r\n      // Assert\r\n      expect(aggregatedMetrics).toBeDefined();\r\n      expect(aggregatedMetrics.data).toHaveLength(1);\r\n      expect(aggregatedMetrics.data[0]).toMatchObject({\r\n        labels: { service: 'api' },\r\n        value: 45, // 10 + 15 + 20\r\n        aggregation: 'sum',\r\n      });\r\n    });\r\n\r\n    it('should export metrics in Prometheus format', async () => {\r\n      // Arrange\r\n      await Promise.all([\r\n        metricsService.recordMetric({\r\n          name: 'http_requests_total',\r\n          value: 100,\r\n          category: 'technical',\r\n          type: 'counter',\r\n          labels: { method: 'GET', status: '200' },\r\n          source: 'test',\r\n        }),\r\n        metricsService.recordMetric({\r\n          name: 'response_time_seconds',\r\n          value: 0.25,\r\n          category: 'performance',\r\n          type: 'histogram',\r\n          labels: { endpoint: '/api/test' },\r\n          source: 'test',\r\n        }),\r\n      ]);\r\n\r\n      // Act\r\n      const prometheusExport = await metricsService.exportPrometheusMetrics();\r\n\r\n      // Assert\r\n      expect(prometheusExport).toContain('# TYPE http_requests_total counter');\r\n      expect(prometheusExport).toContain('http_requests_total{method=\"GET\",status=\"200\"} 100');\r\n      expect(prometheusExport).toContain('# TYPE response_time_seconds histogram');\r\n      expect(prometheusExport).toContain('response_time_seconds{endpoint=\"/api/test\"} 0.25');\r\n    });\r\n\r\n    it('should handle high-frequency metric recording', async () => {\r\n      // Arrange\r\n      const metricCount = 1000;\r\n      const baseMetric = {\r\n        name: 'high_frequency_metric',\r\n        category: 'performance' as const,\r\n        type: 'gauge' as const,\r\n        source: 'performance-test',\r\n      };\r\n\r\n      // Act\r\n      const { result: metrics, executionTime } = await baseTest.measureExecutionTime(async () => {\r\n        const promises = Array.from({ length: metricCount }, (_, i) =>\r\n          metricsService.recordMetric({\r\n            ...baseMetric,\r\n            value: Math.random() * 100,\r\n            labels: { instance: `instance-${i % 10}` },\r\n          })\r\n        );\r\n        return await Promise.all(promises);\r\n      });\r\n\r\n      // Assert\r\n      expect(metrics).toHaveLength(metricCount);\r\n      expect(executionTime).toBeLessThan(10000); // Should complete within 10 seconds\r\n\r\n      // Verify database persistence\r\n      await baseTest.assertDatabaseState(Metric, { name: baseMetric.name }, metricCount);\r\n\r\n      console.log(`High-Frequency Metrics Test (${metricCount} metrics):`, {\r\n        totalTime: `${executionTime}ms`,\r\n        averageTimePerMetric: `${executionTime / metricCount}ms`,\r\n        metricsPerSecond: Math.round((metricCount / executionTime) * 1000),\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Health Checks', () => {\r\n    it('should register and execute health checks', async () => {\r\n      // Arrange\r\n      const healthCheckConfig = {\r\n        name: 'test-service-health',\r\n        category: 'application' as const,\r\n        description: 'Test service health check',\r\n        endpoint: 'http://localhost:3000/health',\r\n        method: 'GET' as const,\r\n        configuration: {\r\n          timeout: 5000,\r\n          retries: 3,\r\n          expectedStatus: [200],\r\n        },\r\n        schedule: {\r\n          interval: 60,\r\n          enabled: true,\r\n        },\r\n        thresholds: {\r\n          responseTime: { warning: 1000, critical: 3000, emergency: 10000 },\r\n          availability: { warning: 95, critical: 90, emergency: 80 },\r\n        },\r\n      };\r\n\r\n      // Act\r\n      const healthCheck = await healthCheckService.createHealthCheck(healthCheckConfig);\r\n\r\n      // Assert\r\n      expect(healthCheck).toBeDefined();\r\n      expect(healthCheck.id).toBeDefined();\r\n      expect(healthCheck.name).toBe(healthCheckConfig.name);\r\n      expect(healthCheck.isEnabled).toBe(true);\r\n\r\n      // Verify database persistence\r\n      await baseTest.assertDatabaseState(HealthCheck, { name: healthCheckConfig.name }, 1);\r\n\r\n      // Execute health check\r\n      const result = await healthCheckService.executeHealthCheck(healthCheck.id);\r\n      expect(result).toBeDefined();\r\n      expect(result.status).toMatch(/^(healthy|degraded|unhealthy|unknown)$/);\r\n    });\r\n\r\n    it('should handle health check failures and alerting', async () => {\r\n      // Arrange\r\n      const healthCheck = await baseTest.createTestHealthCheck({\r\n        name: 'failing-service',\r\n        endpoint: 'http://localhost:9999/nonexistent', // This will fail\r\n        thresholds: {\r\n          responseTime: { warning: 100, critical: 500, emergency: 1000 },\r\n          availability: { warning: 95, critical: 90, emergency: 80 },\r\n        },\r\n        isCritical: true,\r\n      });\r\n\r\n      // Spy on alerting service\r\n      const createAlertSpy = jest.spyOn(alertingService, 'createAlert');\r\n\r\n      // Act\r\n      const result = await healthCheckService.executeHealthCheck(healthCheck.id);\r\n\r\n      // Assert\r\n      expect(result.status).toBe('unhealthy');\r\n      expect(result.responseTime).toBeGreaterThan(0);\r\n\r\n      // Verify alert was created for critical health check failure\r\n      expect(createAlertSpy).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          name: expect.stringContaining('Health Check Failed'),\r\n          severity: 'critical',\r\n          category: 'system',\r\n          source: 'health_check',\r\n          sourceId: healthCheck.id,\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should calculate health scores correctly', async () => {\r\n      // Arrange\r\n      const healthChecks = await Promise.all([\r\n        baseTest.createTestHealthCheck({\r\n          name: 'service-a',\r\n          lastResult: { status: 'healthy', responseTime: 100 },\r\n          metrics: { availability: 99.5, averageResponseTime: 150 },\r\n          isCritical: true,\r\n        }),\r\n        baseTest.createTestHealthCheck({\r\n          name: 'service-b',\r\n          lastResult: { status: 'degraded', responseTime: 2000 },\r\n          metrics: { availability: 95.0, averageResponseTime: 1800 },\r\n          isCritical: false,\r\n        }),\r\n        baseTest.createTestHealthCheck({\r\n          name: 'service-c',\r\n          lastResult: { status: 'unhealthy', responseTime: 5000 },\r\n          metrics: { availability: 85.0, averageResponseTime: 4500 },\r\n          isCritical: true,\r\n        }),\r\n      ]);\r\n\r\n      // Act\r\n      const overallHealth = await healthCheckService.getOverallHealthStatus();\r\n\r\n      // Assert\r\n      expect(overallHealth).toBeDefined();\r\n      expect(overallHealth.status).toMatch(/^(healthy|degraded|unhealthy)$/);\r\n      expect(overallHealth.score).toBeGreaterThanOrEqual(0);\r\n      expect(overallHealth.score).toBeLessThanOrEqual(100);\r\n      expect(overallHealth.checks).toHaveLength(3);\r\n      expect(overallHealth.criticalIssues).toBeGreaterThan(0); // service-c is critical and unhealthy\r\n    });\r\n  });\r\n\r\n  describe('Alerting System', () => {\r\n    it('should create and manage alerts', async () => {\r\n      // Arrange\r\n      const alertData = {\r\n        name: 'High CPU Usage',\r\n        description: 'CPU usage exceeded threshold',\r\n        severity: 'warning' as const,\r\n        category: 'system' as const,\r\n        source: 'metrics',\r\n        sourceId: 'cpu-metric-123',\r\n        conditions: {\r\n          metric: 'cpu_usage_percent',\r\n          operator: '>',\r\n          threshold: 80,\r\n          duration: 300,\r\n        },\r\n        context: {\r\n          labels: { service: 'api-server', instance: 'api-1' },\r\n          annotations: {\r\n            runbook: 'https://wiki.example.com/runbooks/high-cpu',\r\n            dashboard: 'https://grafana.example.com/cpu-dashboard',\r\n          },\r\n        },\r\n      };\r\n\r\n      // Act\r\n      const alert = await alertingService.createAlert(alertData);\r\n\r\n      // Assert\r\n      expect(alert).toBeDefined();\r\n      expect(alert.id).toBeDefined();\r\n      expect(alert.name).toBe(alertData.name);\r\n      expect(alert.severity).toBe(alertData.severity);\r\n      expect(alert.status).toBe('active');\r\n\r\n      // Verify database persistence\r\n      await baseTest.assertDatabaseState(Alert, { name: alertData.name }, 1);\r\n\r\n      // Verify alert creation event was emitted\r\n      await baseTest.assertEventEmitted('alert.created', {\r\n        alertId: alert.id,\r\n        severity: alertData.severity,\r\n      });\r\n    });\r\n\r\n    it('should handle alert acknowledgment and resolution', async () => {\r\n      // Arrange\r\n      const alert = await baseTest.createTestAlert({\r\n        name: 'Test Alert',\r\n        severity: 'critical',\r\n        status: 'active',\r\n      });\r\n\r\n      // Act - Acknowledge alert\r\n      const acknowledgedAlert = await alertingService.acknowledgeAlert(\r\n        alert.id,\r\n        baseTest.testUser.id,\r\n        'Investigating the issue',\r\n        new Date(Date.now() + 3600000) // Expected resolution in 1 hour\r\n      );\r\n\r\n      // Assert acknowledgment\r\n      expect(acknowledgedAlert.status).toBe('acknowledged');\r\n      expect(acknowledgedAlert.acknowledgment).toBeDefined();\r\n      expect(acknowledgedAlert.acknowledgment.acknowledgedBy).toBe(baseTest.testUser.id);\r\n      expect(acknowledgedAlert.acknowledgment.notes).toBe('Investigating the issue');\r\n\r\n      // Act - Resolve alert\r\n      const resolvedAlert = await alertingService.resolveAlert(\r\n        alert.id,\r\n        baseTest.testUser.id,\r\n        'fixed',\r\n        'Issue resolved by restarting service',\r\n        'Service was stuck in deadlock'\r\n      );\r\n\r\n      // Assert resolution\r\n      expect(resolvedAlert.status).toBe('resolved');\r\n      expect(resolvedAlert.resolution).toBeDefined();\r\n      expect(resolvedAlert.resolution.resolvedBy).toBe(baseTest.testUser.id);\r\n      expect(resolvedAlert.resolution.resolution).toBe('fixed');\r\n\r\n      // Verify resolution event was emitted\r\n      await baseTest.assertEventEmitted('alert.resolved', {\r\n        alertId: alert.id,\r\n        resolvedBy: baseTest.testUser.id,\r\n      });\r\n    });\r\n\r\n    it('should handle alert escalation', async () => {\r\n      // Arrange\r\n      const alert = await baseTest.createTestAlert({\r\n        name: 'Escalating Alert',\r\n        severity: 'warning',\r\n        status: 'active',\r\n        escalationLevel: 0,\r\n        createdAt: new Date(Date.now() - 3600000), // 1 hour ago\r\n      });\r\n\r\n      // Mock escalation rules\r\n      jest.spyOn(alertingService as any, 'getEscalationRules').mockReturnValue([\r\n        {\r\n          level: 1,\r\n          delayMinutes: 30,\r\n          severity: 'critical',\r\n          recipients: ['<EMAIL>'],\r\n        },\r\n        {\r\n          level: 2,\r\n          delayMinutes: 60,\r\n          severity: 'emergency',\r\n          recipients: ['<EMAIL>'],\r\n        },\r\n      ]);\r\n\r\n      // Act\r\n      const escalatedAlert = await alertingService.escalateAlert(alert.id);\r\n\r\n      // Assert\r\n      expect(escalatedAlert.escalationLevel).toBe(1);\r\n      expect(escalatedAlert.severity).toBe('critical');\r\n\r\n      // Verify escalation event was emitted\r\n      await baseTest.assertEventEmitted('alert.escalated', {\r\n        alertId: alert.id,\r\n        escalationLevel: 1,\r\n        newSeverity: 'critical',\r\n      });\r\n    });\r\n\r\n    it('should correlate related alerts', async () => {\r\n      // Arrange\r\n      const baseAlertData = {\r\n        category: 'system' as const,\r\n        source: 'metrics',\r\n        context: {\r\n          labels: { service: 'api-server' },\r\n        },\r\n      };\r\n\r\n      const alerts = await Promise.all([\r\n        baseTest.createTestAlert({\r\n          ...baseAlertData,\r\n          name: 'High CPU Usage',\r\n          conditions: { metric: 'cpu_usage', operator: '>', threshold: 80 },\r\n        }),\r\n        baseTest.createTestAlert({\r\n          ...baseAlertData,\r\n          name: 'High Memory Usage',\r\n          conditions: { metric: 'memory_usage', operator: '>', threshold: 90 },\r\n        }),\r\n        baseTest.createTestAlert({\r\n          ...baseAlertData,\r\n          name: 'High Response Time',\r\n          conditions: { metric: 'response_time', operator: '>', threshold: 2000 },\r\n        }),\r\n      ]);\r\n\r\n      // Act\r\n      const correlatedAlerts = await alertingService.correlateAlerts(alerts[0].id);\r\n\r\n      // Assert\r\n      expect(correlatedAlerts).toBeDefined();\r\n      expect(correlatedAlerts.length).toBeGreaterThan(0);\r\n      expect(correlatedAlerts.some(alert => alert.name.includes('Memory'))).toBe(true);\r\n      expect(correlatedAlerts.some(alert => alert.name.includes('Response Time'))).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('Notification System', () => {\r\n    it('should send notifications through multiple channels', async () => {\r\n      // Arrange\r\n      const notificationData = {\r\n        type: 'alert' as const,\r\n        severity: 'critical' as const,\r\n        title: 'Critical System Alert',\r\n        message: 'Database connection failed',\r\n        recipients: [\r\n          { type: 'email', address: '<EMAIL>' },\r\n          { type: 'slack', address: '#alerts' },\r\n          { type: 'webhook', address: 'https://webhook.example.com/alerts' },\r\n        ],\r\n        metadata: {\r\n          alertId: 'alert-123',\r\n          source: 'health-check',\r\n          timestamp: new Date().toISOString(),\r\n        },\r\n      };\r\n\r\n      // Mock notification providers\r\n      const emailSpy = jest.spyOn(notificationService as any, 'sendEmailNotification')\r\n        .mockResolvedValue({ success: true, messageId: 'email-123' });\r\n      const slackSpy = jest.spyOn(notificationService as any, 'sendSlackNotification')\r\n        .mockResolvedValue({ success: true, messageId: 'slack-123' });\r\n      const webhookSpy = jest.spyOn(notificationService as any, 'sendWebhookNotification')\r\n        .mockResolvedValue({ success: true, messageId: 'webhook-123' });\r\n\r\n      // Act\r\n      const results = await notificationService.sendNotification(notificationData);\r\n\r\n      // Assert\r\n      expect(results).toHaveLength(3);\r\n      expect(results.every(result => result.success)).toBe(true);\r\n\r\n      expect(emailSpy).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          to: '<EMAIL>',\r\n          subject: notificationData.title,\r\n          body: expect.stringContaining(notificationData.message),\r\n        })\r\n      );\r\n\r\n      expect(slackSpy).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          channel: '#alerts',\r\n          text: expect.stringContaining(notificationData.message),\r\n        })\r\n      );\r\n\r\n      expect(webhookSpy).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          url: 'https://webhook.example.com/alerts',\r\n          payload: expect.objectContaining({\r\n            title: notificationData.title,\r\n            message: notificationData.message,\r\n          }),\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle notification failures with retry mechanism', async () => {\r\n      // Arrange\r\n      const notificationData = {\r\n        type: 'alert' as const,\r\n        severity: 'warning' as const,\r\n        title: 'Test Notification',\r\n        message: 'Test message',\r\n        recipients: [{ type: 'email', address: '<EMAIL>' }],\r\n      };\r\n\r\n      // Mock notification failure then success\r\n      let attemptCount = 0;\r\n      jest.spyOn(notificationService as any, 'sendEmailNotification')\r\n        .mockImplementation(async () => {\r\n          attemptCount++;\r\n          if (attemptCount < 3) {\r\n            throw new Error('Temporary failure');\r\n          }\r\n          return { success: true, messageId: 'email-retry-123' };\r\n        });\r\n\r\n      // Act\r\n      const results = await notificationService.sendNotification(notificationData);\r\n\r\n      // Assert\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].success).toBe(true);\r\n      expect(attemptCount).toBe(3); // Should have retried twice before success\r\n    });\r\n  });\r\n\r\n  describe('Performance Monitoring', () => {\r\n    it('should track system resource utilization', async () => {\r\n      // Act\r\n      const resourceMetrics = await metricsService.collectSystemMetrics();\r\n\r\n      // Assert\r\n      expect(resourceMetrics).toBeDefined();\r\n      expect(resourceMetrics).toMatchObject({\r\n        memory: {\r\n          heapUsed: expect.any(Number),\r\n          heapTotal: expect.any(Number),\r\n          external: expect.any(Number),\r\n          rss: expect.any(Number),\r\n        },\r\n        cpu: {\r\n          user: expect.any(Number),\r\n          system: expect.any(Number),\r\n        },\r\n        uptime: expect.any(Number),\r\n        loadAverage: expect.any(Array),\r\n      });\r\n\r\n      // Verify metrics were recorded\r\n      const memoryMetrics = await metricsService.getMetrics({\r\n        name: 'system_memory_usage',\r\n        limit: 1,\r\n        offset: 0,\r\n      });\r\n\r\n      expect(memoryMetrics.data.length).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should detect performance bottlenecks', async () => {\r\n      // Arrange - Simulate high load\r\n      const highLoadOperation = async () => {\r\n        // Simulate CPU-intensive operation\r\n        const start = Date.now();\r\n        while (Date.now() - start < 100) {\r\n          Math.random() * Math.random();\r\n        }\r\n      };\r\n\r\n      // Act\r\n      const { executionTime } = await baseTest.measureExecutionTime(async () => {\r\n        await Promise.all(Array.from({ length: 10 }, () => highLoadOperation()));\r\n      });\r\n\r\n      // Record performance metric\r\n      await metricsService.recordMetric({\r\n        name: 'operation_duration',\r\n        value: executionTime,\r\n        category: 'performance',\r\n        type: 'histogram',\r\n        unit: 'ms',\r\n        source: 'performance-test',\r\n      });\r\n\r\n      // Act - Analyze performance\r\n      const performanceAnalysis = await metricsService.analyzePerformance({\r\n        timeRange: {\r\n          start: new Date(Date.now() - 300000), // 5 minutes ago\r\n          end: new Date(),\r\n        },\r\n        metrics: ['operation_duration'],\r\n      });\r\n\r\n      // Assert\r\n      expect(performanceAnalysis).toBeDefined();\r\n      expect(performanceAnalysis.bottlenecks).toBeDefined();\r\n      expect(performanceAnalysis.recommendations).toBeDefined();\r\n    });\r\n  });\r\n});\r\n"], "version": 3}