{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\enriched-event-status-changed.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAC5E,6EAAuF;AAsBvF;;;;;;;;;;GAUG;AACH,MAAa,qCAAsC,SAAQ,+BAAoD;IAC7G,YACE,WAA2B,EAC3B,SAA8C,EAC9C,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,SAAS,KAAK,wCAAgB,CAAC,SAAS,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,SAAS,KAAK,wCAAgB,CAAC,MAAM,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,SAAS,KAAK,wCAAgB,CAAC,OAAO;YAC3C,IAAI,CAAC,SAAS,KAAK,wCAAgB,CAAC,WAAW,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,SAAS,KAAK,wCAAgB,CAAC,OAAO,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,SAAS,KAAK,wCAAgB,CAAC,OAAO,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,CAAC,wCAAgB,CAAC,SAAS,EAAE,wCAAgB,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,SAAS,KAAK,wCAAgB,CAAC,MAAM,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO,CAAC,CAAC;QAE3B,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;QACpF,IAAI,UAAU,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE/B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,MAAM,EAAE,oBAAoB,IAAI,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,MAAM,EAAE,eAAe,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,MAAM,EAAE,WAAW,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,MAAM,EAAE,kBAAkB,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,8BAA8B;QAC5B,MAAM,WAAW,GAA2B;YAC1C,CAAC,GAAG,wCAAgB,CAAC,OAAO,KAAK,wCAAgB,CAAC,WAAW,EAAE,CAAC,EAAE,oBAAoB;YACtF,CAAC,GAAG,wCAAgB,CAAC,WAAW,KAAK,wCAAgB,CAAC,SAAS,EAAE,CAAC,EAAE,mCAAmC;YACvG,CAAC,GAAG,wCAAgB,CAAC,WAAW,KAAK,wCAAgB,CAAC,OAAO,EAAE,CAAC,EAAE,2CAA2C;YAC7G,CAAC,GAAG,wCAAgB,CAAC,WAAW,KAAK,wCAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,mBAAmB;YACpF,CAAC,GAAG,wCAAgB,CAAC,OAAO,KAAK,wCAAgB,CAAC,OAAO,EAAE,CAAC,EAAE,oBAAoB;YAClF,CAAC,GAAG,wCAAgB,CAAC,MAAM,KAAK,wCAAgB,CAAC,OAAO,EAAE,CAAC,EAAE,4BAA4B;YACzF,CAAC,GAAG,wCAAgB,CAAC,MAAM,KAAK,wCAAgB,CAAC,OAAO,EAAE,CAAC,EAAE,2BAA2B;SACzF,CAAC;QAEF,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,EAAE,CAAC;QACnD,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,uBAAuB,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC1F,CAAC;IAED;;OAEG;IACH,eAAe;QAqBb,OAAO;YACL,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC5C,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,gBAAgB,EAAE,IAAI,CAAC,8BAA8B,EAAE;YACvD,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;YACnD,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACnD,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7C,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC/C,wBAAwB,EAAE,IAAI,CAAC,wBAAwB,EAAE;YACzD,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACnD,wBAAwB,EAAE,IAAI,CAAC,wBAAwB,EAAE;YACzD,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,EAAE;YACtD,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YAChD,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YAChD,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;YAC/B,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;SACrC,CAAC;IACJ,CAAC;CACF;AA1PD,sFA0PC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\enriched-event-status-changed.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EnrichmentStatus, EnrichmentResult } from '../entities/enriched-event.entity';\r\n\r\n/**\r\n * Enriched Event Status Changed Domain Event Data\r\n */\r\nexport interface EnrichedEventStatusChangedEventData {\r\n  /** Previous enrichment status */\r\n  oldStatus: EnrichmentStatus;\r\n  /** New enrichment status */\r\n  newStatus: EnrichmentStatus;\r\n  /** Enrichment result details */\r\n  result?: EnrichmentResult;\r\n  /** Updated enrichment quality score */\r\n  enrichmentQualityScore?: number;\r\n  /** Whether the event requires manual review */\r\n  requiresManualReview: boolean;\r\n  /** Timestamp of the status change */\r\n  timestamp?: Date;\r\n  /** Additional context about the status change */\r\n  context?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Enriched Event Status Changed Domain Event\r\n * \r\n * Raised when an enriched security event's enrichment status changes.\r\n * This event triggers various downstream processes including:\r\n * - Status-specific workflow routing\r\n * - Quality monitoring and alerting\r\n * - Manual review queue updates\r\n * - Performance metrics collection\r\n * - Audit trail maintenance\r\n */\r\nexport class EnrichedEventStatusChangedDomainEvent extends BaseDomainEvent<EnrichedEventStatusChangedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: EnrichedEventStatusChangedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the previous enrichment status\r\n   */\r\n  get oldStatus(): EnrichmentStatus {\r\n    return this.eventData.oldStatus;\r\n  }\r\n\r\n  /**\r\n   * Get the new enrichment status\r\n   */\r\n  get newStatus(): EnrichmentStatus {\r\n    return this.eventData.newStatus;\r\n  }\r\n\r\n  /**\r\n   * Get the enrichment result\r\n   */\r\n  get result(): EnrichmentResult | undefined {\r\n    return this.eventData.result;\r\n  }\r\n\r\n  /**\r\n   * Get the enrichment quality score\r\n   */\r\n  get enrichmentQualityScore(): number | undefined {\r\n    return this.eventData.enrichmentQualityScore;\r\n  }\r\n\r\n  /**\r\n   * Check if the event requires manual review\r\n   */\r\n  get requiresManualReview(): boolean {\r\n    return this.eventData.requiresManualReview;\r\n  }\r\n\r\n  /**\r\n   * Get the timestamp of the status change\r\n   */\r\n  get timestamp(): Date {\r\n    return this.eventData.timestamp || this.occurredOn;\r\n  }\r\n\r\n  /**\r\n   * Get additional context about the status change\r\n   */\r\n  get context(): Record<string, any> {\r\n    return this.eventData.context || {};\r\n  }\r\n\r\n  /**\r\n   * Check if enrichment was completed successfully\r\n   */\r\n  isEnrichmentCompleted(): boolean {\r\n    return this.newStatus === EnrichmentStatus.COMPLETED;\r\n  }\r\n\r\n  /**\r\n   * Check if enrichment failed\r\n   */\r\n  isEnrichmentFailed(): boolean {\r\n    return this.newStatus === EnrichmentStatus.FAILED;\r\n  }\r\n\r\n  /**\r\n   * Check if enrichment is now in progress\r\n   */\r\n  isEnrichmentStarted(): boolean {\r\n    return this.oldStatus === EnrichmentStatus.PENDING && \r\n           this.newStatus === EnrichmentStatus.IN_PROGRESS;\r\n  }\r\n\r\n  /**\r\n   * Check if enrichment was skipped\r\n   */\r\n  isEnrichmentSkipped(): boolean {\r\n    return this.newStatus === EnrichmentStatus.SKIPPED;\r\n  }\r\n\r\n  /**\r\n   * Check if enrichment is partial\r\n   */\r\n  isEnrichmentPartial(): boolean {\r\n    return this.newStatus === EnrichmentStatus.PARTIAL;\r\n  }\r\n\r\n  /**\r\n   * Check if the status change indicates success\r\n   */\r\n  isSuccessfulStatusChange(): boolean {\r\n    return [EnrichmentStatus.COMPLETED, EnrichmentStatus.PARTIAL].includes(this.newStatus);\r\n  }\r\n\r\n  /**\r\n   * Check if the status change indicates failure\r\n   */\r\n  isFailureStatusChange(): boolean {\r\n    return this.newStatus === EnrichmentStatus.FAILED;\r\n  }\r\n\r\n  /**\r\n   * Check if enrichment quality is high\r\n   */\r\n  hasHighEnrichmentQuality(): boolean {\r\n    return (this.enrichmentQualityScore || 0) >= 70;\r\n  }\r\n\r\n  /**\r\n   * Get enrichment success rate from result\r\n   */\r\n  getEnrichmentSuccessRate(): number {\r\n    if (!this.result) return 0;\r\n    \r\n    const totalRules = this.result.appliedRules.length + this.result.failedRules.length;\r\n    if (totalRules === 0) return 0;\r\n    \r\n    return (this.result.appliedRules.length / totalRules) * 100;\r\n  }\r\n\r\n  /**\r\n   * Get processing duration from result\r\n   */\r\n  getProcessingDuration(): number {\r\n    return this.result?.processingDurationMs || 0;\r\n  }\r\n\r\n  /**\r\n   * Get confidence score from result\r\n   */\r\n  getConfidenceScore(): number {\r\n    return this.result?.confidenceScore || 0;\r\n  }\r\n\r\n  /**\r\n   * Get number of sources used from result\r\n   */\r\n  getSourcesUsed(): number {\r\n    return this.result?.sourcesUsed || 0;\r\n  }\r\n\r\n  /**\r\n   * Get data points enriched from result\r\n   */\r\n  getDataPointsEnriched(): number {\r\n    return this.result?.dataPointsEnriched || 0;\r\n  }\r\n\r\n  /**\r\n   * Check if there are enrichment warnings\r\n   */\r\n  hasWarnings(): boolean {\r\n    return (this.result?.warnings?.length || 0) > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if there are enrichment errors\r\n   */\r\n  hasErrors(): boolean {\r\n    return (this.result?.errors?.length || 0) > 0;\r\n  }\r\n\r\n  /**\r\n   * Get status transition description\r\n   */\r\n  getStatusTransitionDescription(): string {\r\n    const transitions: Record<string, string> = {\r\n      [`${EnrichmentStatus.PENDING}->${EnrichmentStatus.IN_PROGRESS}`]: 'Enrichment started',\r\n      [`${EnrichmentStatus.IN_PROGRESS}->${EnrichmentStatus.COMPLETED}`]: 'Enrichment completed successfully',\r\n      [`${EnrichmentStatus.IN_PROGRESS}->${EnrichmentStatus.PARTIAL}`]: 'Enrichment completed with partial results',\r\n      [`${EnrichmentStatus.IN_PROGRESS}->${EnrichmentStatus.FAILED}`]: 'Enrichment failed',\r\n      [`${EnrichmentStatus.PENDING}->${EnrichmentStatus.SKIPPED}`]: 'Enrichment skipped',\r\n      [`${EnrichmentStatus.FAILED}->${EnrichmentStatus.PENDING}`]: 'Enrichment reset for retry',\r\n      [`${EnrichmentStatus.FAILED}->${EnrichmentStatus.SKIPPED}`]: 'Failed enrichment skipped',\r\n    };\r\n\r\n    const key = `${this.oldStatus}->${this.newStatus}`;\r\n    return transitions[key] || `Status changed from ${this.oldStatus} to ${this.newStatus}`;\r\n  }\r\n\r\n  /**\r\n   * Get event summary for handlers\r\n   */\r\n  getEventSummary(): {\r\n    enrichedEventId: string;\r\n    oldStatus: EnrichmentStatus;\r\n    newStatus: EnrichmentStatus;\r\n    statusTransition: string;\r\n    enrichmentQualityScore?: number;\r\n    requiresManualReview: boolean;\r\n    isEnrichmentCompleted: boolean;\r\n    isEnrichmentFailed: boolean;\r\n    isEnrichmentStarted: boolean;\r\n    isSuccessfulStatusChange: boolean;\r\n    isFailureStatusChange: boolean;\r\n    hasHighEnrichmentQuality: boolean;\r\n    enrichmentSuccessRate: number;\r\n    processingDuration: number;\r\n    confidenceScore: number;\r\n    sourcesUsed: number;\r\n    dataPointsEnriched: number;\r\n    hasWarnings: boolean;\r\n    hasErrors: boolean;\r\n  } {\r\n    return {\r\n      enrichedEventId: this.aggregateId.toString(),\r\n      oldStatus: this.oldStatus,\r\n      newStatus: this.newStatus,\r\n      statusTransition: this.getStatusTransitionDescription(),\r\n      enrichmentQualityScore: this.enrichmentQualityScore,\r\n      requiresManualReview: this.requiresManualReview,\r\n      isEnrichmentCompleted: this.isEnrichmentCompleted(),\r\n      isEnrichmentFailed: this.isEnrichmentFailed(),\r\n      isEnrichmentStarted: this.isEnrichmentStarted(),\r\n      isSuccessfulStatusChange: this.isSuccessfulStatusChange(),\r\n      isFailureStatusChange: this.isFailureStatusChange(),\r\n      hasHighEnrichmentQuality: this.hasHighEnrichmentQuality(),\r\n      enrichmentSuccessRate: this.getEnrichmentSuccessRate(),\r\n      processingDuration: this.getProcessingDuration(),\r\n      confidenceScore: this.getConfidenceScore(),\r\n      sourcesUsed: this.getSourcesUsed(),\r\n      dataPointsEnriched: this.getDataPointsEnriched(),\r\n      hasWarnings: this.hasWarnings(),\r\n      hasErrors: this.hasErrors(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventSummary: this.getEventSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}