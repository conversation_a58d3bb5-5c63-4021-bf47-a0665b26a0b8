import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { SecurityEvent } from '../../../modules/event-processing/domain/entities/security-event.entity';
import { Vulnerability } from '../../../modules/vulnerability-management/domain/entities/vulnerability.entity';
import { ThreatIndicator } from '../../../modules/threat-intelligence/domain/entities/threat-indicator.entity';
import { Incident } from '../../../modules/incident-response/domain/entities/incident.entity';
import { DistributedCacheService } from '../../../infrastructure/cache/distributed-cache.service';
import { LoggerService } from '../../../infrastructure/logging/logger.service';
import { PrometheusMetricsService } from '../../../infrastructure/monitoring/prometheus-metrics.service';
import { StreamingGateway, StreamSubscription, StreamMessage } from './streaming.gateway';

/**
 * Stream configuration interface
 */
export interface StreamConfig {
  name: string;
  description: string;
  dataSource: string;
  updateFrequency: number;
  retentionPeriod: number;
  maxSubscribers: number;
  requiresAuth: boolean;
  allowedRoles: string[];
}

/**
 * Subscription request interface
 */
export interface SubscriptionRequest {
  userId: string;
  socketId: string;
  streamType: string;
  filters: Record<string, any>;
  options?: Record<string, any>;
}

/**
 * Real-time streaming service
 * Manages WebSocket subscriptions and real-time data distribution
 */
@Injectable()
export class StreamingService implements OnModuleInit {
  private readonly logger = new Logger(StreamingService.name);
  private gateway: StreamingGateway;
  private activeSubscriptions = new Map<string, StreamSubscription>();
  private streamConfigs = new Map<string, StreamConfig>();
  private dataBuffers = new Map<string, StreamMessage[]>();

  constructor(
    @InjectRepository(SecurityEvent)
    private readonly eventRepository: Repository<SecurityEvent>,
    @InjectRepository(Vulnerability)
    private readonly vulnerabilityRepository: Repository<Vulnerability>,
    @InjectRepository(ThreatIndicator)
    private readonly threatRepository: Repository<ThreatIndicator>,
    @InjectRepository(Incident)
    private readonly incidentRepository: Repository<Incident>,
    private readonly configService: ConfigService,
    private readonly cacheService: DistributedCacheService,
    private readonly loggerService: LoggerService,
    private readonly metricsService: PrometheusMetricsService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async onModuleInit() {
    this.initializeStreamConfigs();
    this.setupEventListeners();
    this.startDataProcessing();
    this.logger.log('Streaming service initialized');
  }

  /**
   * Set gateway reference
   */
  setGateway(gateway: StreamingGateway): void {
    this.gateway = gateway;
  }

  /**
   * Get available streams for user
   */
  async getAvailableStreams(userId: string): Promise<StreamConfig[]> {
    // Get user roles (would integrate with user service)
    const userRoles = await this.getUserRoles(userId);
    
    return Array.from(this.streamConfigs.values()).filter(config => {
      if (!config.requiresAuth) {
        return true;
      }
      return config.allowedRoles.some(role => userRoles.includes(role));
    });
  }

  /**
   * Create subscription
   */
  async createSubscription(request: SubscriptionRequest): Promise<string> {
    const subscriptionId = this.generateSubscriptionId();
    
    const subscription: StreamSubscription = {
      id: subscriptionId,
      userId: request.userId,
      socketId: request.socketId,
      streamType: request.streamType,
      filters: request.filters,
      createdAt: new Date(),
      lastActivity: new Date(),
    };

    this.activeSubscriptions.set(subscriptionId, subscription);

    // Start data streaming for this subscription
    await this.startStreamingForSubscription(subscription);

    this.logger.log('Subscription created', {
      subscriptionId,
      userId: request.userId,
      streamType: request.streamType,
    });

    return subscriptionId;
  }

  /**
   * Remove subscription
   */
  async removeSubscription(subscriptionId: string, userId: string): Promise<void> {
    const subscription = this.activeSubscriptions.get(subscriptionId);
    
    if (subscription && subscription.userId === userId) {
      this.activeSubscriptions.delete(subscriptionId);
      
      this.logger.log('Subscription removed', {
        subscriptionId,
        userId,
        streamType: subscription.streamType,
      });
    }
  }

  /**
   * Broadcast security event
   */
  async broadcastSecurityEvent(event: SecurityEvent): Promise<void> {
    const message: StreamMessage = {
      id: this.generateMessageId(),
      streamType: 'security-events',
      eventType: 'event.created',
      data: {
        id: event.id,
        eventType: event.eventType,
        severity: event.severity,
        sourceIp: event.sourceIp,
        sourceHostname: event.sourceHostname,
        timestamp: event.timestamp,
        description: event.description,
        riskScore: event.riskScore,
        metadata: event.metadata,
      },
      timestamp: new Date(),
      metadata: {
        module: 'event-processing',
        version: '2.0',
      },
    };

    this.gateway?.broadcastToStream('security-events', message);
    await this.bufferMessage('security-events', message);
  }

  /**
   * Broadcast vulnerability update
   */
  async broadcastVulnerabilityUpdate(vulnerability: Vulnerability): Promise<void> {
    const message: StreamMessage = {
      id: this.generateMessageId(),
      streamType: 'vulnerability-updates',
      eventType: 'vulnerability.updated',
      data: {
        id: vulnerability.id,
        cveId: vulnerability.cveId,
        severity: vulnerability.severity,
        status: vulnerability.status,
        cvssScore: vulnerability.cvssScore,
        title: vulnerability.title,
        description: vulnerability.description,
        discoveredAt: vulnerability.discoveredAt,
        resolvedAt: vulnerability.resolvedAt,
        assetId: vulnerability.asset?.id,
      },
      timestamp: new Date(),
      metadata: {
        module: 'vulnerability-management',
        version: '2.0',
      },
    };

    this.gateway?.broadcastToStream('vulnerability-updates', message);
    await this.bufferMessage('vulnerability-updates', message);
  }

  /**
   * Broadcast threat intelligence update
   */
  async broadcastThreatIntelligence(threat: ThreatIndicator): Promise<void> {
    const message: StreamMessage = {
      id: this.generateMessageId(),
      streamType: 'threat-intelligence',
      eventType: 'threat.detected',
      data: {
        id: threat.id,
        type: threat.type,
        value: threat.value,
        threatType: threat.threatType,
        confidence: threat.confidence,
        severity: threat.severity,
        source: threat.source,
        firstSeen: threat.firstSeen,
        lastSeen: threat.lastSeen,
        tags: threat.tags,
      },
      timestamp: new Date(),
      metadata: {
        module: 'threat-intelligence',
        version: '2.0',
      },
    };

    this.gateway?.broadcastToStream('threat-intelligence', message);
    await this.bufferMessage('threat-intelligence', message);
  }

  /**
   * Broadcast incident update
   */
  async broadcastIncidentUpdate(incident: Incident): Promise<void> {
    const message: StreamMessage = {
      id: this.generateMessageId(),
      streamType: 'incident-updates',
      eventType: 'incident.updated',
      data: {
        id: incident.id,
        title: incident.title,
        severity: incident.severity,
        status: incident.status,
        category: incident.category,
        assignedTo: incident.assignedTo,
        createdAt: incident.createdAt,
        detectedAt: incident.detectedAt,
        firstResponseAt: incident.firstResponseAt,
        resolvedAt: incident.resolvedAt,
      },
      timestamp: new Date(),
      metadata: {
        module: 'incident-response',
        version: '2.0',
      },
    };

    this.gateway?.broadcastToStream('incident-updates', message);
    await this.bufferMessage('incident-updates', message);
  }

  /**
   * Broadcast system metrics
   */
  async broadcastSystemMetrics(metrics: Record<string, any>): Promise<void> {
    const message: StreamMessage = {
      id: this.generateMessageId(),
      streamType: 'system-metrics',
      eventType: 'metrics.updated',
      data: {
        timestamp: new Date(),
        metrics: {
          cpu: metrics.cpu || 0,
          memory: metrics.memory || 0,
          network: metrics.network || 0,
          events: metrics.events || 0,
          activeUsers: metrics.activeUsers || 0,
          systemLoad: metrics.systemLoad || 0,
        },
      },
      timestamp: new Date(),
      metadata: {
        module: 'infrastructure',
        version: '2.0',
      },
    };

    this.gateway?.broadcastToStream('system-metrics', message);
    await this.bufferMessage('system-metrics', message);
  }

  /**
   * Get stream statistics
   */
  async getStreamStatistics(): Promise<{
    activeSubscriptions: number;
    streamTypes: Record<string, number>;
    messagesSent: Record<string, number>;
    connectedUsers: number;
  }> {
    const subscriptions = Array.from(this.activeSubscriptions.values());
    
    const streamTypes: Record<string, number> = {};
    subscriptions.forEach(sub => {
      streamTypes[sub.streamType] = (streamTypes[sub.streamType] || 0) + 1;
    });

    const uniqueUsers = new Set(subscriptions.map(sub => sub.userId));

    return {
      activeSubscriptions: subscriptions.length,
      streamTypes,
      messagesSent: await this.getMessageCounts(),
      connectedUsers: uniqueUsers.size,
    };
  }

  // Private helper methods

  private initializeStreamConfigs(): void {
    const configs: StreamConfig[] = [
      {
        name: 'security-events',
        description: 'Real-time security events stream',
        dataSource: 'event-processing',
        updateFrequency: 1000, // 1 second
        retentionPeriod: 3600, // 1 hour
        maxSubscribers: 100,
        requiresAuth: true,
        allowedRoles: ['security_analyst', 'security_manager', 'admin'],
      },
      {
        name: 'vulnerability-updates',
        description: 'Vulnerability status updates stream',
        dataSource: 'vulnerability-management',
        updateFrequency: 5000, // 5 seconds
        retentionPeriod: 7200, // 2 hours
        maxSubscribers: 50,
        requiresAuth: true,
        allowedRoles: ['vulnerability_analyst', 'security_analyst', 'admin'],
      },
      {
        name: 'threat-intelligence',
        description: 'Threat intelligence updates stream',
        dataSource: 'threat-intelligence',
        updateFrequency: 10000, // 10 seconds
        retentionPeriod: 3600, // 1 hour
        maxSubscribers: 30,
        requiresAuth: true,
        allowedRoles: ['threat_analyst', 'security_analyst', 'admin'],
      },
      {
        name: 'system-metrics',
        description: 'System performance metrics stream',
        dataSource: 'infrastructure',
        updateFrequency: 5000, // 5 seconds
        retentionPeriod: 1800, // 30 minutes
        maxSubscribers: 20,
        requiresAuth: true,
        allowedRoles: ['system_admin', 'admin'],
      },
      {
        name: 'incident-updates',
        description: 'Incident response updates stream',
        dataSource: 'incident-response',
        updateFrequency: 2000, // 2 seconds
        retentionPeriod: 7200, // 2 hours
        maxSubscribers: 40,
        requiresAuth: true,
        allowedRoles: ['incident_responder', 'security_manager', 'admin'],
      },
    ];

    configs.forEach(config => {
      this.streamConfigs.set(config.name, config);
    });

    this.logger.log('Stream configurations initialized', {
      streamCount: configs.length,
      streams: configs.map(c => c.name),
    });
  }

  private setupEventListeners(): void {
    // Listen for entity events from other modules
    this.eventEmitter.on('security-event.created', (event: SecurityEvent) => {
      this.broadcastSecurityEvent(event);
    });

    this.eventEmitter.on('vulnerability.updated', (vulnerability: Vulnerability) => {
      this.broadcastVulnerabilityUpdate(vulnerability);
    });

    this.eventEmitter.on('threat.detected', (threat: ThreatIndicator) => {
      this.broadcastThreatIntelligence(threat);
    });

    this.eventEmitter.on('incident.updated', (incident: Incident) => {
      this.broadcastIncidentUpdate(incident);
    });

    this.logger.log('Event listeners configured');
  }

  private startDataProcessing(): void {
    // Start system metrics broadcasting
    setInterval(async () => {
      try {
        const metrics = await this.collectSystemMetrics();
        await this.broadcastSystemMetrics(metrics);
      } catch (error) {
        this.logger.error('Failed to broadcast system metrics', { error: error.message });
      }
    }, 5000);

    // Start buffer cleanup
    setInterval(() => {
      this.cleanupBuffers();
    }, 60000); // Every minute

    this.logger.log('Data processing started');
  }

  private async startStreamingForSubscription(subscription: StreamSubscription): Promise<void> {
    // Send initial data based on subscription type
    try {
      const initialData = await this.getInitialStreamData(subscription.streamType, subscription.filters);
      
      if (initialData.length > 0) {
        for (const data of initialData) {
          const message: StreamMessage = {
            id: this.generateMessageId(),
            streamType: subscription.streamType,
            eventType: 'initial.data',
            data,
            timestamp: new Date(),
            metadata: {
              initial: true,
            },
          };

          this.gateway?.broadcastToStream(subscription.streamType, message, subscription.filters);
        }
      }
    } catch (error) {
      this.logger.error('Failed to send initial stream data', {
        subscriptionId: subscription.id,
        streamType: subscription.streamType,
        error: error.message,
      });
    }
  }

  private async getInitialStreamData(streamType: string, filters: Record<string, any>): Promise<any[]> {
    const limit = 10; // Limit initial data

    switch (streamType) {
      case 'security-events':
        return await this.eventRepository.find({
          order: { timestamp: 'DESC' },
          take: limit,
        });

      case 'vulnerability-updates':
        return await this.vulnerabilityRepository.find({
          order: { discoveredAt: 'DESC' },
          take: limit,
        });

      case 'threat-intelligence':
        return await this.threatRepository.find({
          order: { lastSeen: 'DESC' },
          take: limit,
        });

      case 'incident-updates':
        return await this.incidentRepository.find({
          order: { createdAt: 'DESC' },
          take: limit,
        });

      default:
        return [];
    }
  }

  private async collectSystemMetrics(): Promise<Record<string, any>> {
    // Collect system metrics (would integrate with actual monitoring)
    return {
      cpu: Math.random() * 100,
      memory: Math.random() * 100,
      network: Math.random() * 1000,
      events: await this.eventRepository.count(),
      activeUsers: this.activeSubscriptions.size,
      systemLoad: Math.random() * 5,
    };
  }

  private async bufferMessage(streamType: string, message: StreamMessage): Promise<void> {
    const buffer = this.dataBuffers.get(streamType) || [];
    buffer.push(message);

    // Keep only recent messages
    const config = this.streamConfigs.get(streamType);
    if (config) {
      const cutoff = Date.now() - (config.retentionPeriod * 1000);
      const filtered = buffer.filter(msg => msg.timestamp.getTime() > cutoff);
      this.dataBuffers.set(streamType, filtered);
    }
  }

  private cleanupBuffers(): void {
    const now = Date.now();
    
    for (const [streamType, buffer] of this.dataBuffers.entries()) {
      const config = this.streamConfigs.get(streamType);
      if (config) {
        const cutoff = now - (config.retentionPeriod * 1000);
        const filtered = buffer.filter(msg => msg.timestamp.getTime() > cutoff);
        this.dataBuffers.set(streamType, filtered);
      }
    }
  }

  private async getUserRoles(userId: string): Promise<string[]> {
    // Mock user roles - would integrate with user service
    return ['security_analyst', 'admin'];
  }

  private async getMessageCounts(): Promise<Record<string, number>> {
    const counts: Record<string, number> = {};
    
    for (const [streamType, buffer] of this.dataBuffers.entries()) {
      counts[streamType] = buffer.length;
    }
    
    return counts;
  }

  private generateSubscriptionId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
