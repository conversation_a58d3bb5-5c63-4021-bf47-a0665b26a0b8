{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\ai-model-status-changed.domain-event.ts", "mappings": ";;;AAAA,0FAAqF;AAIrF;;;;;;GAMG;AACH,MAAa,yBAA0B,SAAQ,mCAAe;IAC5D,YACkB,OAAuB,EACvB,SAAiB,EACjB,cAA2B,EAC3B,SAAsB,EACtC,OAAwB,EACxB,UAAiB;QAEjB,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAPX,YAAO,GAAP,OAAO,CAAgB;QACvB,cAAS,GAAT,SAAS,CAAQ;QACjB,mBAAc,GAAd,cAAc,CAAa;QAC3B,cAAS,GAAT,SAAS,CAAa;IAKxC,CAAC;IAEM,YAAY;QACjB,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAEM,eAAe;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,YAAY;QACjB,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAChC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;CACF;AA5BD,8DA4BC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\ai-model-status-changed.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent } from '../../../../shared-kernel/domain/base-domain-event';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { ModelStatus } from '../entities/ai-model.entity';\r\n\r\n/**\r\n * AI Model Status Changed Domain Event\r\n * \r\n * Published when an AI model's status changes (e.g., active to inactive).\r\n * This event can trigger various downstream processes such as\r\n * load balancer updates, monitoring adjustments, and notification systems.\r\n */\r\nexport class AIModelStatusChangedEvent extends BaseDomainEvent {\r\n  constructor(\r\n    public readonly modelId: UniqueEntityId,\r\n    public readonly modelName: string,\r\n    public readonly previousStatus: ModelStatus,\r\n    public readonly newStatus: ModelStatus,\r\n    eventId?: UniqueEntityId,\r\n    occurredOn?: Date\r\n  ) {\r\n    super(eventId, occurredOn);\r\n  }\r\n\r\n  public getEventName(): string {\r\n    return 'AIModelStatusChanged';\r\n  }\r\n\r\n  public getEventVersion(): string {\r\n    return '1.0';\r\n  }\r\n\r\n  public getEventData(): Record<string, any> {\r\n    return {\r\n      modelId: this.modelId.toString(),\r\n      modelName: this.modelName,\r\n      previousStatus: this.previousStatus,\r\n      newStatus: this.newStatus,\r\n    };\r\n  }\r\n}"], "version": 3}