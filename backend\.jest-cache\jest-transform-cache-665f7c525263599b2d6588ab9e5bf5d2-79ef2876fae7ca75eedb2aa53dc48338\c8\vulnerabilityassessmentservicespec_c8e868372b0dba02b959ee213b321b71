4b80774c4adf0bb17360373d397d643d
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const vulnerability_assessment_service_1 = require("../vulnerability-assessment.service");
const vulnerability_assessment_entity_1 = require("../../../domain/entities/vulnerability-assessment.entity");
const vulnerability_entity_1 = require("../../../domain/entities/vulnerability.entity");
const asset_entity_1 = require("../../../../asset-management/domain/entities/asset.entity");
const vulnerability_scan_entity_1 = require("../../../domain/entities/vulnerability-scan.entity");
const logger_service_1 = require("../../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../../infrastructure/notification/notification.service");
describe('VulnerabilityAssessmentService', () => {
    let service;
    let assessmentRepository;
    let vulnerabilityRepository;
    let assetRepository;
    let scanRepository;
    let loggerService;
    let auditService;
    let notificationService;
    const mockAssessment = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        vulnerabilityId: 'vuln-123',
        assetId: 'asset-123',
        assessmentType: 'manual',
        status: 'pending',
        findings: 'Test findings',
        assessedSeverity: 'high',
        assessedCvssScore: 7.5,
        confidenceLevel: 85,
        isFalsePositive: false,
        isAcceptedRisk: false,
        assessedBy: 'user-123',
        assessedAt: new Date(),
        getSummary: jest.fn(),
        exportForReporting: jest.fn(),
        complete: jest.fn(),
        review: jest.fn(),
        reject: jest.fn(),
        markAsFalsePositive: jest.fn(),
        acceptRisk: jest.fn(),
    };
    const mockVulnerability = {
        id: 'vuln-123',
        identifier: 'CVE-2023-1234',
        title: 'Test Vulnerability',
        severity: 'medium',
    };
    const mockAsset = {
        id: 'asset-123',
        name: 'Test Asset',
        type: 'server',
    };
    const mockScan = {
        id: 'scan-123',
        name: 'Test Scan',
        status: 'completed',
    };
    beforeEach(async () => {
        const mockRepository = {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            findOneBy: jest.fn(),
            count: jest.fn(),
            remove: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
                leftJoinAndSelect: jest.fn().mockReturnThis(),
                where: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                addOrderBy: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getManyAndCount: jest.fn(),
                getMany: jest.fn(),
            })),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                vulnerability_assessment_service_1.VulnerabilityAssessmentService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_assessment_entity_1.VulnerabilityAssessment),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_entity_1.Vulnerability),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(asset_entity_1.Asset),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_scan_entity_1.VulnerabilityScan),
                    useValue: mockRepository,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: {
                        debug: jest.fn(),
                        log: jest.fn(),
                        warn: jest.fn(),
                        error: jest.fn(),
                    },
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: {
                        logUserAction: jest.fn(),
                    },
                },
                {
                    provide: notification_service_1.NotificationService,
                    useValue: {
                        sendAssessmentNotification: jest.fn(),
                    },
                },
            ],
        }).compile();
        service = module.get(vulnerability_assessment_service_1.VulnerabilityAssessmentService);
        assessmentRepository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_assessment_entity_1.VulnerabilityAssessment));
        vulnerabilityRepository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_entity_1.Vulnerability));
        assetRepository = module.get((0, typeorm_1.getRepositoryToken)(asset_entity_1.Asset));
        scanRepository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_scan_entity_1.VulnerabilityScan));
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
        notificationService = module.get(notification_service_1.NotificationService);
    });
    it('should be defined', () => {
        expect(service).toBeDefined();
    });
    describe('createAssessment', () => {
        it('should create a new assessment', async () => {
            const assessmentData = {
                vulnerabilityId: 'vuln-123',
                assetId: 'asset-123',
                assessmentType: 'manual',
                findings: 'Test findings',
                assessedSeverity: 'high',
                confidenceLevel: 85,
            };
            vulnerabilityRepository.findOne.mockResolvedValue(mockVulnerability);
            assetRepository.findOne.mockResolvedValue(mockAsset);
            assessmentRepository.create.mockReturnValue(mockAssessment);
            assessmentRepository.save.mockResolvedValue(mockAssessment);
            const result = await service.createAssessment(assessmentData, 'user-123');
            expect(vulnerabilityRepository.findOne).toHaveBeenCalledWith({
                where: { id: 'vuln-123' },
            });
            expect(assetRepository.findOne).toHaveBeenCalledWith({
                where: { id: 'asset-123' },
            });
            expect(assessmentRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                ...assessmentData,
                status: 'pending',
                assessedBy: 'user-123',
                assessedAt: expect.any(Date),
            }));
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'create', 'vulnerability_assessment', mockAssessment.id, expect.any(Object));
        });
        it('should throw NotFoundException when vulnerability not found', async () => {
            const assessmentData = {
                vulnerabilityId: 'non-existent-vuln',
                assessmentType: 'manual',
            };
            vulnerabilityRepository.findOne.mockResolvedValue(null);
            await expect(service.createAssessment(assessmentData, 'user-123')).rejects.toThrow('Vulnerability not found');
        });
        it('should throw NotFoundException when asset not found', async () => {
            const assessmentData = {
                vulnerabilityId: 'vuln-123',
                assetId: 'non-existent-asset',
                assessmentType: 'manual',
            };
            vulnerabilityRepository.findOne.mockResolvedValue(mockVulnerability);
            assetRepository.findOne.mockResolvedValue(null);
            await expect(service.createAssessment(assessmentData, 'user-123')).rejects.toThrow('Asset not found');
        });
    });
    describe('getAssessmentDetails', () => {
        it('should return assessment details', async () => {
            assessmentRepository.findOne.mockResolvedValue(mockAssessment);
            const result = await service.getAssessmentDetails('123e4567-e89b-12d3-a456-426614174000');
            expect(result).toEqual(mockAssessment);
            expect(assessmentRepository.findOne).toHaveBeenCalledWith({
                where: { id: '123e4567-e89b-12d3-a456-426614174000' },
                relations: ['vulnerability', 'asset', 'scan'],
            });
        });
        it('should throw NotFoundException when assessment not found', async () => {
            assessmentRepository.findOne.mockResolvedValue(null);
            await expect(service.getAssessmentDetails('non-existent-id')).rejects.toThrow('Assessment not found');
        });
    });
    describe('searchAssessments', () => {
        it('should search assessments with filters', async () => {
            const mockQueryBuilder = assessmentRepository.createQueryBuilder();
            mockQueryBuilder.getManyAndCount.mockResolvedValue([[mockAssessment], 1]);
            const searchCriteria = {
                page: 1,
                limit: 10,
                vulnerabilityIds: ['vuln-123'],
                statuses: ['pending'],
                assessmentTypes: ['manual'],
            };
            const result = await service.searchAssessments(searchCriteria);
            expect(result).toHaveProperty('assessments');
            expect(result).toHaveProperty('total');
            expect(result).toHaveProperty('page');
            expect(result).toHaveProperty('totalPages');
            expect(result.assessments).toHaveLength(1);
            expect(result.total).toBe(1);
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('assessment.vulnerabilityId IN (:...vulnerabilityIds)', { vulnerabilityIds: ['vuln-123'] });
        });
        it('should handle pagination correctly', async () => {
            const mockQueryBuilder = assessmentRepository.createQueryBuilder();
            mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);
            const searchCriteria = {
                page: 3,
                limit: 20,
            };
            await service.searchAssessments(searchCriteria);
            expect(mockQueryBuilder.skip).toHaveBeenCalledWith(40);
            expect(mockQueryBuilder.take).toHaveBeenCalledWith(20);
        });
    });
    describe('updateAssessment', () => {
        it('should update an existing assessment', async () => {
            const updates = {
                findings: 'Updated findings',
                assessedSeverity: 'critical',
                confidenceLevel: 95,
            };
            assessmentRepository.findOne.mockResolvedValue(mockAssessment);
            assessmentRepository.save.mockResolvedValue({
                ...mockAssessment,
                ...updates,
            });
            const result = await service.updateAssessment('123e4567-e89b-12d3-a456-426614174000', updates, 'user-123');
            expect(assessmentRepository.save).toHaveBeenCalled();
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'update', 'vulnerability_assessment', '123e4567-e89b-12d3-a456-426614174000', expect.objectContaining({
                vulnerabilityId: mockAssessment.vulnerabilityId,
                changes: expect.any(Object),
            }));
        });
        it('should throw NotFoundException when assessment not found', async () => {
            assessmentRepository.findOne.mockResolvedValue(null);
            await expect(service.updateAssessment('non-existent-id', {}, 'user-123')).rejects.toThrow('Assessment not found');
        });
    });
    describe('completeAssessment', () => {
        it('should complete an assessment', async () => {
            assessmentRepository.findOne.mockResolvedValue(mockAssessment);
            assessmentRepository.save.mockResolvedValue({
                ...mockAssessment,
                status: 'completed',
            });
            const result = await service.completeAssessment('123e4567-e89b-12d3-a456-426614174000', 'user-123');
            expect(mockAssessment.complete).toHaveBeenCalled();
            expect(assessmentRepository.save).toHaveBeenCalled();
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'complete', 'vulnerability_assessment', '123e4567-e89b-12d3-a456-426614174000', expect.objectContaining({
                vulnerabilityId: mockAssessment.vulnerabilityId,
            }));
        });
        it('should throw NotFoundException when assessment not found', async () => {
            assessmentRepository.findOne.mockResolvedValue(null);
            await expect(service.completeAssessment('non-existent-id', 'user-123')).rejects.toThrow('Assessment not found');
        });
    });
    describe('reviewAssessment', () => {
        it('should review and approve an assessment', async () => {
            const reviewData = {
                approved: true,
                comments: 'Assessment looks good',
            };
            assessmentRepository.findOne.mockResolvedValue(mockAssessment);
            assessmentRepository.save.mockResolvedValue({
                ...mockAssessment,
                status: 'reviewed',
            });
            const result = await service.reviewAssessment('123e4567-e89b-12d3-a456-426614174000', reviewData, 'reviewer-123');
            expect(mockAssessment.review).toHaveBeenCalledWith('reviewer-123', 'Assessment looks good');
            expect(assessmentRepository.save).toHaveBeenCalled();
            expect(auditService.logUserAction).toHaveBeenCalledWith('reviewer-123', 'review', 'vulnerability_assessment', '123e4567-e89b-12d3-a456-426614174000', expect.objectContaining({
                approved: true,
                comments: 'Assessment looks good',
            }));
        });
        it('should review and reject an assessment', async () => {
            const reviewData = {
                approved: false,
                comments: 'Needs more analysis',
            };
            assessmentRepository.findOne.mockResolvedValue(mockAssessment);
            assessmentRepository.save.mockResolvedValue({
                ...mockAssessment,
                status: 'rejected',
            });
            await service.reviewAssessment('123e4567-e89b-12d3-a456-426614174000', reviewData, 'reviewer-123');
            expect(mockAssessment.review).toHaveBeenCalledWith('reviewer-123', 'Needs more analysis');
            expect(mockAssessment.reject).toHaveBeenCalledWith('Needs more analysis');
        });
    });
    describe('deleteAssessment', () => {
        it('should delete an existing assessment', async () => {
            assessmentRepository.findOne.mockResolvedValue(mockAssessment);
            assessmentRepository.remove.mockResolvedValue(mockAssessment);
            await service.deleteAssessment('123e4567-e89b-12d3-a456-426614174000', 'user-123');
            expect(assessmentRepository.remove).toHaveBeenCalledWith(mockAssessment);
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'delete', 'vulnerability_assessment', '123e4567-e89b-12d3-a456-426614174000', expect.objectContaining({
                vulnerabilityId: mockAssessment.vulnerabilityId,
                assessmentType: mockAssessment.assessmentType,
            }));
        });
        it('should throw NotFoundException when assessment not found', async () => {
            assessmentRepository.findOne.mockResolvedValue(null);
            await expect(service.deleteAssessment('non-existent-id', 'user-123')).rejects.toThrow('Assessment not found');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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