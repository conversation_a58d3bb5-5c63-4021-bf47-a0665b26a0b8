{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\security-policy.ts", "mappings": ";;;AAAA,8EAA0E;AAC1E,yHAAuG;AACvG,2GAA2F;AAG3F,oGAAgG;AAoBhG,IAAY,kBAQX;AARD,WAAY,kBAAkB;IAC5B,2CAAqB,CAAA;IACrB,yDAAmC,CAAA;IACnC,yDAAmC,CAAA;IACnC,iDAA2B,CAAA;IAC3B,6CAAuB,CAAA;IACvB,2CAAqB,CAAA;IACrB,uDAAiC,CAAA;AACnC,CAAC,EARW,kBAAkB,kCAAlB,kBAAkB,QAQ7B;AAED,IAAY,oBAKX;AALD,WAAY,oBAAoB;IAC9B,mCAAW,CAAA;IACX,yCAAiB,CAAA;IACjB,qCAAa,CAAA;IACb,6CAAqB,CAAA;AACvB,CAAC,EALW,oBAAoB,oCAApB,oBAAoB,QAK/B;AA6CD,IAAY,qBAMX;AAND,WAAY,qBAAqB;IAC/B,4CAAmB,CAAA;IACnB,8CAAqB,CAAA;IACrB,0CAAiB,CAAA;IACjB,4CAAmB,CAAA;IACnB,gEAAuC,CAAA;AACzC,CAAC,EANW,qBAAqB,qCAArB,qBAAqB,QAMhC;AAED,MAAa,cAAe,SAAQ,wBAAe;IAWjD,YAAY,KAWX;QACC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;QAEvB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAE1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC;QACtC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;QAClC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,cAAc,CAAC;QAC5C,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC;IAC5C,CAAC;IAEM,MAAM,CAAC,MAAM,CAAC,KAQpB;QACC,OAAO,IAAI,cAAc,CAAC;YACxB,GAAG,KAAK;YACR,cAAc,EAAE,KAAK,CAAC,SAAS;YAC/B,UAAU,EAAE,EAAE;SACf,CAAC,CAAC;IACL,CAAC;IAEM,QAAQ,CAAC,OAAgC;QAC9C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,OAAO,GAA6B,EAAE,CAAC;QAE7C,iDAAiD;QACjD,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;aACjC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;aAC5B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QAE3C,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAEjD,MAAM,MAAM,GAA2B;oBACrC,QAAQ,EAAE,IAAI,CAAC,EAAE;oBACjB,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,OAAO;oBACP,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;oBACzC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,iBAAiB;oBACrF,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjE,WAAW,EAAE,kCAAS,CAAC,GAAG,EAAE;oBAC5B,OAAO;iBACR,CAAC;gBAEF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAErB,qBAAqB;gBACrB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBAE3B,0EAA0E;gBAC1E,IAAI,OAAO,IAAI,IAAI,CAAC,QAAQ,KAAK,oBAAoB,CAAC,QAAQ,EAAE,CAAC;oBAC/D,MAAM;gBACR,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,qDAAqD;gBACrD,MAAM,WAAW,GAA2B;oBAC1C,QAAQ,EAAE,IAAI,CAAC,EAAE;oBACjB,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,2BAA2B,KAAK,CAAC,OAAO,EAAE;oBAClD,UAAU,EAAE,CAAC;oBACb,WAAW,EAAE,kCAAS,CAAC,GAAG,EAAE;oBAC5B,OAAO;iBACR,CAAC;gBACF,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,OAAO,CAAC,IAAkB,EAAE,UAAkB;QACnD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAExB,+BAA+B;QAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,0CAAmB,CAAC,iBAAiB,IAAI,CAAC,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEM,UAAU,CAAC,MAAc,EAAE,OAA8B,EAAE,UAAkB;QAClF,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;QAC9D,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;YACrB,MAAM,IAAI,0CAAmB,CAAC,iBAAiB,MAAM,aAAa,EAAE,EAAE,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,WAAW,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG,OAAO,EAAE,CAAC;QAC9D,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAE/B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEM,UAAU,CAAC,MAAc,EAAE,UAAkB;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;QAC9D,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;YACrB,MAAM,IAAI,0CAAmB,CAAC,iBAAiB,MAAM,aAAa,EAAE,EAAE,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QACjC,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEM,MAAM,CAAC,UAAkB;QAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEM,OAAO,CAAC,UAAkB;QAC/B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEO,KAAK;QACX,qCAAqC;QACrC,+DAA+D;IACjE,CAAC;IAEO,YAAY,CAAC,IAAkB,EAAE,OAAgC;QACvE,IAAI,CAAC;YACH,+DAA+D;YAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,SAAc,EAAE,OAAgC;QACxE,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,2BAA2B;QAC3B,IAAI,SAAS,CAAC,GAAG,EAAE,CAAC;YAClB,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,YAAiB,EAAE,EAAE,CAC/C,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,OAAO,CAAC,CAC9C,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,CAAC,EAAE,EAAE,CAAC;YACjB,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,YAAiB,EAAE,EAAE,CAC7C,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,OAAO,CAAC,CAC9C,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,CAAC,GAAG,EAAE,CAAC;YAClB,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;QAED,2BAA2B;QAC3B,IAAI,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YAC/E,OAAO,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,sBAAsB,CAAC,SAAc,EAAE,OAAgC;QAC7E,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAChE,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC;QACtC,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;QAEpC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,QAAQ;gBACX,OAAO,UAAU,KAAK,aAAa,CAAC;YACtC,KAAK,YAAY;gBACf,OAAO,UAAU,KAAK,aAAa,CAAC;YACtC,KAAK,UAAU;gBACb,OAAO,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC9E,KAAK,aAAa;gBAChB,OAAO,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAChF,KAAK,WAAW;gBACd,OAAO,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC9E,KAAK,cAAc;gBACjB,OAAO,MAAM,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;YACpD,KAAK,WAAW;gBACd,OAAO,MAAM,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;YACpD,KAAK,IAAI;gBACP,OAAO,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAC5E,KAAK,OAAO;gBACV,OAAO,OAAO,UAAU,KAAK,QAAQ,IAAI,IAAI,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtF;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,SAAiB,EAAE,OAAgC;QACvE,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,OAAO,GAAQ,OAAO,CAAC;QAE3B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,IAAI,IAAI,OAAO,EAAE,CAAC;gBAC9D,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,mBAAmB,CAAC,IAAkB,EAAE,OAAgC;QAC9E,yCAAyC;QACzC,IAAI,UAAU,GAAG,GAAG,CAAC;QAErB,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtB,KAAK,oBAAoB,CAAC,QAAQ;gBAChC,UAAU,GAAG,GAAG,CAAC;gBACjB,MAAM;YACR,KAAK,oBAAoB,CAAC,IAAI;gBAC5B,UAAU,GAAG,GAAG,CAAC;gBACjB,MAAM;YACR,KAAK,oBAAoB,CAAC,MAAM;gBAC9B,UAAU,GAAG,GAAG,CAAC;gBACjB,MAAM;YACR,KAAK,oBAAoB,CAAC,GAAG;gBAC3B,UAAU,GAAG,GAAG,CAAC;gBACjB,MAAM;QACV,CAAC;QAED,8CAA8C;QAC9C,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC;QACpF,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC5C,CAAC;IAEO,aAAa,CAAC,gBAAwC;QAC5D,MAAM,UAAU,GAAqB;YACnC,EAAE,EAAE,8CAAc,CAAC,QAAQ,EAAE;YAC7B,QAAQ,EAAE,IAAI,CAAC,EAAE;YACjB,MAAM,EAAE,gBAAgB,CAAC,MAAM;YAC/B,gBAAgB;YAChB,eAAe,EAAE,qBAAqB,CAAC,OAAO;YAC9C,UAAU,EAAE,kCAAS,CAAC,GAAG,EAAE;SAC5B,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAEO,aAAa,CAAC,KAAU;QAC9B,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,0CAAmB,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,0CAAmB,CAAC,gCAAgC,EAAE,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxD,MAAM,IAAI,0CAAmB,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACpB,MAAM,IAAI,0CAAmB,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACrB,MAAM,IAAI,0CAAmB,CAAC,gCAAgC,EAAE,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,0CAAmB,CAAC,wBAAwB,EAAE,EAAE,CAAC,CAAC;QAC9D,CAAC;QAED,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IACvE,CAAC;IAEO,YAAY,CAAC,IAAkB;QACrC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,0CAAmB,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChD,MAAM,IAAI,0CAAmB,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1D,MAAM,IAAI,0CAAmB,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACtC,MAAM,IAAI,0CAAmB,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YAC3D,MAAM,IAAI,0CAAmB,CAAC,6CAA6C,EAAE,EAAE,CAAC,CAAC;QACnF,CAAC;QAED,mCAAmC;QACnC,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,0CAAmB,CAAC,mCAAmC,EAAE,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED,UAAU;IACV,IAAW,IAAI,KAAa,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAChD,IAAW,WAAW,KAAa,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IAC9D,IAAW,OAAO,KAAa,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACtD,IAAW,QAAQ,KAAe,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC1D,IAAW,KAAK,KAAqB,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/D,IAAW,OAAO,KAAc,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACvD,IAAW,SAAS,KAAa,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IAC1D,IAAW,cAAc,KAAa,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IACpE,IAAW,UAAU,KAAyB,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CAC9E;AAlWD,wCAkWC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\security-policy.ts"], "sourcesContent": ["import { BaseEntity } from '../../../../shared-kernel/domain/base-entity';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { Timestamp } from '../../../../shared-kernel/value-objects/timestamp.value-object';\r\nimport { UserId } from '../../../../shared-kernel/value-objects/user-id.value-object';\r\nimport { TenantId } from '../../../../shared-kernel/value-objects/tenant-id.value-object';\r\nimport { ValidationException } from '../../../../shared-kernel/exceptions/validation.exception';\r\n\r\nexport interface SecurityRule {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  condition: string; // JSON-based rule condition\r\n  action: SecurityAction;\r\n  severity: SecurityRuleSeverity;\r\n  enabled: boolean;\r\n  priority: number;\r\n}\r\n\r\nexport interface SecurityAction {\r\n  type: SecurityActionType;\r\n  parameters: Record<string, any>;\r\n  autoExecute: boolean;\r\n  requiresApproval: boolean;\r\n}\r\n\r\nexport enum SecurityActionType {\r\n  BLOCK_IP = 'BLOCK_IP',\r\n  QUARANTINE_USER = 'QUARANTINE_USER',\r\n  DISABLE_ACCOUNT = 'DISABLE_ACCOUNT',\r\n  ALERT_ADMIN = 'ALERT_ADMIN',\r\n  LOG_EVENT = 'LOG_EVENT',\r\n  ESCALATE = 'ESCALATE',\r\n  ISOLATE_SYSTEM = 'ISOLATE_SYSTEM'\r\n}\r\n\r\nexport enum SecurityRuleSeverity {\r\n  LOW = 'LOW',\r\n  MEDIUM = 'MEDIUM',\r\n  HIGH = 'HIGH',\r\n  CRITICAL = 'CRITICAL'\r\n}\r\n\r\nexport interface PolicyEvaluationContext {\r\n  eventData: Record<string, any>;\r\n  userContext?: {\r\n    userId: UserId;\r\n    tenantId: TenantId;\r\n    roles: string[];\r\n    permissions: string[];\r\n  };\r\n  systemContext?: {\r\n    timestamp: Timestamp;\r\n    sourceIp?: string;\r\n    userAgent?: string;\r\n  };\r\n  threatContext?: {\r\n    threatLevel: string;\r\n    indicators: string[];\r\n    confidence: number;\r\n  };\r\n}\r\n\r\nexport interface PolicyEvaluationResult {\r\n  policyId: UniqueEntityId;\r\n  ruleId: string;\r\n  matched: boolean;\r\n  action?: SecurityAction;\r\n  reason: string;\r\n  confidence: number;\r\n  evaluatedAt: Timestamp;\r\n  context: PolicyEvaluationContext;\r\n}\r\n\r\nexport interface PolicyAuditEntry {\r\n  id: UniqueEntityId;\r\n  policyId: UniqueEntityId;\r\n  ruleId: string;\r\n  evaluationResult: PolicyEvaluationResult;\r\n  executedAction?: SecurityAction;\r\n  executionStatus: PolicyExecutionStatus;\r\n  executedBy?: UserId;\r\n  executedAt: Timestamp;\r\n  notes?: string;\r\n}\r\n\r\nexport enum PolicyExecutionStatus {\r\n  PENDING = 'PENDING',\r\n  EXECUTED = 'EXECUTED',\r\n  FAILED = 'FAILED',\r\n  SKIPPED = 'SKIPPED',\r\n  REQUIRES_APPROVAL = 'REQUIRES_APPROVAL'\r\n}\r\n\r\nexport class SecurityPolicy extends BaseEntity<any> {\r\n  private readonly _name: string;\r\n  private readonly _description: string;\r\n  private readonly _version: string;\r\n  private readonly _tenantId: TenantId;\r\n  private _rules: SecurityRule[];\r\n  private _enabled: boolean;\r\n  private readonly _createdBy: UserId;\r\n  private _lastModifiedBy: UserId;\r\n  private _auditTrail: PolicyAuditEntry[];\r\n\r\n  constructor(props: {\r\n    id?: UniqueEntityId;\r\n    name: string;\r\n    description: string;\r\n    version: string;\r\n    tenantId: TenantId;\r\n    rules: SecurityRule[];\r\n    enabled: boolean;\r\n    createdBy: UserId;\r\n    lastModifiedBy: UserId;\r\n    auditTrail?: PolicyAuditEntry[];\r\n  }) {\r\n    super(props, props.id);\r\n    \r\n    this.validateProps(props);\r\n    \r\n    this._name = props.name;\r\n    this._description = props.description;\r\n    this._version = props.version;\r\n    this._tenantId = props.tenantId;\r\n    this._rules = props.rules;\r\n    this._enabled = props.enabled;\r\n    this._createdBy = props.createdBy;\r\n    this._lastModifiedBy = props.lastModifiedBy;\r\n    this._auditTrail = props.auditTrail || [];\r\n  }\r\n\r\n  public static create(props: {\r\n    name: string;\r\n    description: string;\r\n    version: string;\r\n    tenantId: TenantId;\r\n    rules: SecurityRule[];\r\n    enabled: boolean;\r\n    createdBy: UserId;\r\n  }): SecurityPolicy {\r\n    return new SecurityPolicy({\r\n      ...props,\r\n      lastModifiedBy: props.createdBy,\r\n      auditTrail: []\r\n    });\r\n  }\r\n\r\n  public evaluate(context: PolicyEvaluationContext): PolicyEvaluationResult[] {\r\n    if (!this._enabled) {\r\n      return [];\r\n    }\r\n\r\n    const results: PolicyEvaluationResult[] = [];\r\n    \r\n    // Sort rules by priority (higher priority first)\r\n    const sortedRules = [...this._rules]\r\n      .filter(rule => rule.enabled)\r\n      .sort((a, b) => b.priority - a.priority);\r\n\r\n    for (const rule of sortedRules) {\r\n      try {\r\n        const matched = this.evaluateRule(rule, context);\r\n        \r\n        const result: PolicyEvaluationResult = {\r\n          policyId: this.id,\r\n          ruleId: rule.id,\r\n          matched,\r\n          action: matched ? rule.action : undefined,\r\n          reason: matched ? `Rule '${rule.name}' matched` : `Rule '${rule.name}' did not match`,\r\n          confidence: matched ? this.calculateConfidence(rule, context) : 0,\r\n          evaluatedAt: Timestamp.now(),\r\n          context\r\n        };\r\n\r\n        results.push(result);\r\n\r\n        // Create audit entry\r\n        this.addAuditEntry(result);\r\n\r\n        // If rule matches and has high priority, we might want to stop evaluation\r\n        if (matched && rule.severity === SecurityRuleSeverity.CRITICAL) {\r\n          break;\r\n        }\r\n      } catch (error) {\r\n        // Log evaluation error but continue with other rules\r\n        const errorResult: PolicyEvaluationResult = {\r\n          policyId: this.id,\r\n          ruleId: rule.id,\r\n          matched: false,\r\n          reason: `Rule evaluation failed: ${error.message}`,\r\n          confidence: 0,\r\n          evaluatedAt: Timestamp.now(),\r\n          context\r\n        };\r\n        results.push(errorResult);\r\n      }\r\n    }\r\n\r\n    return results;\r\n  }\r\n\r\n  public addRule(rule: SecurityRule, modifiedBy: UserId): void {\r\n    this.validateRule(rule);\r\n    \r\n    // Check for duplicate rule IDs\r\n    if (this._rules.some(r => r.id === rule.id)) {\r\n      throw new ValidationException(`Rule with ID '${rule.id}' already exists`, []);\r\n    }\r\n\r\n    this._rules.push(rule);\r\n    this._lastModifiedBy = modifiedBy;\r\n    this.touch();\r\n  }\r\n\r\n  public updateRule(ruleId: string, updates: Partial<SecurityRule>, modifiedBy: UserId): void {\r\n    const ruleIndex = this._rules.findIndex(r => r.id === ruleId);\r\n    if (ruleIndex === -1) {\r\n      throw new ValidationException(`Rule with ID '${ruleId}' not found`, []);\r\n    }\r\n\r\n    const updatedRule = { ...this._rules[ruleIndex], ...updates };\r\n    this.validateRule(updatedRule);\r\n\r\n    this._rules[ruleIndex] = updatedRule;\r\n    this._lastModifiedBy = modifiedBy;\r\n    this.touch();\r\n  }\r\n\r\n  public removeRule(ruleId: string, modifiedBy: UserId): void {\r\n    const ruleIndex = this._rules.findIndex(r => r.id === ruleId);\r\n    if (ruleIndex === -1) {\r\n      throw new ValidationException(`Rule with ID '${ruleId}' not found`, []);\r\n    }\r\n\r\n    this._rules.splice(ruleIndex, 1);\r\n    this._lastModifiedBy = modifiedBy;\r\n    this.touch();\r\n  }\r\n\r\n  public enable(modifiedBy: UserId): void {\r\n    this._enabled = true;\r\n    this._lastModifiedBy = modifiedBy;\r\n    this.touch();\r\n  }\r\n\r\n  public disable(modifiedBy: UserId): void {\r\n    this._enabled = false;\r\n    this._lastModifiedBy = modifiedBy;\r\n    this.touch();\r\n  }\r\n\r\n  private touch(): void {\r\n    // Update the last modified timestamp\r\n    // This would typically update an updatedAt field if we had one\r\n  }\r\n\r\n  private evaluateRule(rule: SecurityRule, context: PolicyEvaluationContext): boolean {\r\n    try {\r\n      // Parse the rule condition (simplified JSON-based rule engine)\r\n      const condition = JSON.parse(rule.condition);\r\n      return this.evaluateCondition(condition, context);\r\n    } catch (error) {\r\n      throw new Error(`Invalid rule condition: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  private evaluateCondition(condition: any, context: PolicyEvaluationContext): boolean {\r\n    if (typeof condition !== 'object' || condition === null) {\r\n      return false;\r\n    }\r\n\r\n    // Handle logical operators\r\n    if (condition.and) {\r\n      return condition.and.every((subCondition: any) => \r\n        this.evaluateCondition(subCondition, context)\r\n      );\r\n    }\r\n\r\n    if (condition.or) {\r\n      return condition.or.some((subCondition: any) => \r\n        this.evaluateCondition(subCondition, context)\r\n      );\r\n    }\r\n\r\n    if (condition.not) {\r\n      return !this.evaluateCondition(condition.not, context);\r\n    }\r\n\r\n    // Handle field comparisons\r\n    if (condition.field && condition.operator && condition.hasOwnProperty('value')) {\r\n      return this.evaluateFieldCondition(condition, context);\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  private evaluateFieldCondition(condition: any, context: PolicyEvaluationContext): boolean {\r\n    const fieldValue = this.getFieldValue(condition.field, context);\r\n    const expectedValue = condition.value;\r\n    const operator = condition.operator;\r\n\r\n    switch (operator) {\r\n      case 'equals':\r\n        return fieldValue === expectedValue;\r\n      case 'not_equals':\r\n        return fieldValue !== expectedValue;\r\n      case 'contains':\r\n        return typeof fieldValue === 'string' && fieldValue.includes(expectedValue);\r\n      case 'starts_with':\r\n        return typeof fieldValue === 'string' && fieldValue.startsWith(expectedValue);\r\n      case 'ends_with':\r\n        return typeof fieldValue === 'string' && fieldValue.endsWith(expectedValue);\r\n      case 'greater_than':\r\n        return Number(fieldValue) > Number(expectedValue);\r\n      case 'less_than':\r\n        return Number(fieldValue) < Number(expectedValue);\r\n      case 'in':\r\n        return Array.isArray(expectedValue) && expectedValue.includes(fieldValue);\r\n      case 'regex':\r\n        return typeof fieldValue === 'string' && new RegExp(expectedValue).test(fieldValue);\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  private getFieldValue(fieldPath: string, context: PolicyEvaluationContext): any {\r\n    const parts = fieldPath.split('.');\r\n    let current: any = context;\r\n\r\n    for (const part of parts) {\r\n      if (current && typeof current === 'object' && part in current) {\r\n        current = current[part];\r\n      } else {\r\n        return undefined;\r\n      }\r\n    }\r\n\r\n    return current;\r\n  }\r\n\r\n  private calculateConfidence(rule: SecurityRule, context: PolicyEvaluationContext): number {\r\n    // Base confidence based on rule severity\r\n    let confidence = 0.5;\r\n\r\n    switch (rule.severity) {\r\n      case SecurityRuleSeverity.CRITICAL:\r\n        confidence = 0.9;\r\n        break;\r\n      case SecurityRuleSeverity.HIGH:\r\n        confidence = 0.8;\r\n        break;\r\n      case SecurityRuleSeverity.MEDIUM:\r\n        confidence = 0.6;\r\n        break;\r\n      case SecurityRuleSeverity.LOW:\r\n        confidence = 0.4;\r\n        break;\r\n    }\r\n\r\n    // Adjust based on threat context if available\r\n    if (context.threatContext) {\r\n      confidence = Math.min(1.0, confidence + (context.threatContext.confidence * 0.2));\r\n    }\r\n\r\n    return Math.round(confidence * 100) / 100;\r\n  }\r\n\r\n  private addAuditEntry(evaluationResult: PolicyEvaluationResult): void {\r\n    const auditEntry: PolicyAuditEntry = {\r\n      id: UniqueEntityId.generate(),\r\n      policyId: this.id,\r\n      ruleId: evaluationResult.ruleId,\r\n      evaluationResult,\r\n      executionStatus: PolicyExecutionStatus.PENDING,\r\n      executedAt: Timestamp.now()\r\n    };\r\n\r\n    this._auditTrail.push(auditEntry);\r\n  }\r\n\r\n  private validateProps(props: any): void {\r\n    if (!props.name || props.name.trim().length === 0) {\r\n      throw new ValidationException('Policy name is required', []);\r\n    }\r\n\r\n    if (!props.description || props.description.trim().length === 0) {\r\n      throw new ValidationException('Policy description is required', []);\r\n    }\r\n\r\n    if (!props.version || props.version.trim().length === 0) {\r\n      throw new ValidationException('Policy version is required', []);\r\n    }\r\n\r\n    if (!props.tenantId) {\r\n      throw new ValidationException('Tenant ID is required', []);\r\n    }\r\n\r\n    if (!props.createdBy) {\r\n      throw new ValidationException('Created by user ID is required', []);\r\n    }\r\n\r\n    if (!Array.isArray(props.rules)) {\r\n      throw new ValidationException('Rules must be an array', []);\r\n    }\r\n\r\n    props.rules.forEach((rule: SecurityRule) => this.validateRule(rule));\r\n  }\r\n\r\n  private validateRule(rule: SecurityRule): void {\r\n    if (!rule.id || rule.id.trim().length === 0) {\r\n      throw new ValidationException('Rule ID is required', []);\r\n    }\r\n\r\n    if (!rule.name || rule.name.trim().length === 0) {\r\n      throw new ValidationException('Rule name is required', []);\r\n    }\r\n\r\n    if (!rule.condition || rule.condition.trim().length === 0) {\r\n      throw new ValidationException('Rule condition is required', []);\r\n    }\r\n\r\n    if (!rule.action || !rule.action.type) {\r\n      throw new ValidationException('Rule action is required', []);\r\n    }\r\n\r\n    if (typeof rule.priority !== 'number' || rule.priority < 0) {\r\n      throw new ValidationException('Rule priority must be a non-negative number', []);\r\n    }\r\n\r\n    // Validate condition is valid JSON\r\n    try {\r\n      JSON.parse(rule.condition);\r\n    } catch (error) {\r\n      throw new ValidationException('Rule condition must be valid JSON', []);\r\n    }\r\n  }\r\n\r\n  // Getters\r\n  public get name(): string { return this._name; }\r\n  public get description(): string { return this._description; }\r\n  public get version(): string { return this._version; }\r\n  public get tenantId(): TenantId { return this._tenantId; }\r\n  public get rules(): SecurityRule[] { return [...this._rules]; }\r\n  public get enabled(): boolean { return this._enabled; }\r\n  public get createdBy(): UserId { return this._createdBy; }\r\n  public get lastModifiedBy(): UserId { return this._lastModifiedBy; }\r\n  public get auditTrail(): PolicyAuditEntry[] { return [...this._auditTrail]; }\r\n}"], "version": 3}