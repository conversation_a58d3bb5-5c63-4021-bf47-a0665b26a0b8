b4fbdf06c49b1234f69edf2edfa45e5c
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventSpecificationBuilder = exports.SecurityAlertSpecification = exports.RequiresAttentionSpecification = exports.MaxAttemptsExceededSpecification = exports.FailedProcessingEventSpecification = exports.EventCorrelationSpecification = exports.EventAgeRangeSpecification = exports.EventRiskScoreRangeSpecification = exports.EventTagSpecification = exports.EventSourceTypeSpecification = exports.EventProcessingStatusSpecification = exports.EventStatusSpecification = exports.EventSeveritySpecification = exports.EventTypeSpecification = exports.StaleEventSpecification = exports.RecentEventSpecification = exports.HighRiskEventSpecification = exports.ActiveEventSpecification = exports.CriticalEventSpecification = exports.HighSeverityEventSpecification = exports.EventSpecification = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const event_type_enum_1 = require("../enums/event-type.enum");
const event_status_enum_1 = require("../enums/event-status.enum");
/**
 * Event Specification Base Class
 *
 * Base class for all event-related specifications.
 * Provides common functionality for event filtering and validation.
 */
class EventSpecification extends shared_kernel_1.BaseSpecification {
    /**
     * Helper method to check if event matches any of the provided types
     */
    matchesAnyType(event, types) {
        return types.includes(event.type);
    }
    /**
     * Helper method to check if event matches any of the provided severities
     */
    matchesAnySeverity(event, severities) {
        return severities.includes(event.severity);
    }
    /**
     * Helper method to check if event matches any of the provided statuses
     */
    matchesAnyStatus(event, statuses) {
        return statuses.includes(event.status);
    }
    /**
     * Helper method to check if event matches any of the provided processing statuses
     */
    matchesAnyProcessingStatus(event, statuses) {
        return statuses.includes(event.processingStatus);
    }
    /**
     * Helper method to check if event source matches any of the provided types
     */
    matchesAnySourceType(event, sourceTypes) {
        return sourceTypes.includes(event.metadata.source.type);
    }
    /**
     * Helper method to check if event has any of the provided tags
     */
    hasAnyTag(event, tags) {
        return event.hasAnyTag(tags);
    }
    /**
     * Helper method to check if event has all of the provided tags
     */
    hasAllTags(event, tags) {
        return event.hasAllTags(tags);
    }
    /**
     * Helper method to check if event age is within range
     */
    isAgeWithinRange(event, minAgeMs, maxAgeMs) {
        const age = event.getAge();
        if (minAgeMs !== undefined && age < minAgeMs) {
            return false;
        }
        if (maxAgeMs !== undefined && age > maxAgeMs) {
            return false;
        }
        return true;
    }
    /**
     * Helper method to check if risk score is within range
     */
    isRiskScoreWithinRange(event, minScore, maxScore) {
        const riskScore = event.riskScore;
        if (riskScore === undefined) {
            return minScore === undefined; // If no min score required, undefined is acceptable
        }
        if (minScore !== undefined && riskScore < minScore) {
            return false;
        }
        if (maxScore !== undefined && riskScore > maxScore) {
            return false;
        }
        return true;
    }
}
exports.EventSpecification = EventSpecification;
/**
 * High Severity Event Specification
 *
 * Specification for events with high or critical severity.
 */
class HighSeverityEventSpecification extends EventSpecification {
    isSatisfiedBy(event) {
        return event.isHighSeverity();
    }
    getDescription() {
        return 'Event has high or critical severity';
    }
}
exports.HighSeverityEventSpecification = HighSeverityEventSpecification;
/**
 * Critical Event Specification
 *
 * Specification for events with critical severity only.
 */
class CriticalEventSpecification extends EventSpecification {
    isSatisfiedBy(event) {
        return event.isCritical();
    }
    getDescription() {
        return 'Event has critical severity';
    }
}
exports.CriticalEventSpecification = CriticalEventSpecification;
/**
 * Active Event Specification
 *
 * Specification for events that are currently active (not resolved or closed).
 */
class ActiveEventSpecification extends EventSpecification {
    isSatisfiedBy(event) {
        return event.isActive();
    }
    getDescription() {
        return 'Event is active (not resolved or closed)';
    }
}
exports.ActiveEventSpecification = ActiveEventSpecification;
/**
 * High Risk Event Specification
 *
 * Specification for events with high risk scores.
 */
class HighRiskEventSpecification extends EventSpecification {
    isSatisfiedBy(event) {
        return event.isHighRisk();
    }
    getDescription() {
        return 'Event has high risk score (>= 70)';
    }
}
exports.HighRiskEventSpecification = HighRiskEventSpecification;
/**
 * Recent Event Specification
 *
 * Specification for events that occurred recently.
 */
class RecentEventSpecification extends EventSpecification {
    constructor(withinMs = 3600000) {
        super();
        this.withinMs = withinMs;
    }
    isSatisfiedBy(event) {
        return event.isRecent(this.withinMs);
    }
    getDescription() {
        const hours = Math.floor(this.withinMs / 3600000);
        const minutes = Math.floor((this.withinMs % 3600000) / 60000);
        return `Event occurred within ${hours}h ${minutes}m`;
    }
}
exports.RecentEventSpecification = RecentEventSpecification;
/**
 * Stale Event Specification
 *
 * Specification for events that are considered stale.
 */
class StaleEventSpecification extends EventSpecification {
    constructor(olderThanMs = 86400000) {
        super();
        this.olderThanMs = olderThanMs;
    }
    isSatisfiedBy(event) {
        return event.isStale(this.olderThanMs);
    }
    getDescription() {
        const hours = Math.floor(this.olderThanMs / 3600000);
        return `Event is older than ${hours} hours`;
    }
}
exports.StaleEventSpecification = StaleEventSpecification;
/**
 * Event Type Specification
 *
 * Specification for events of specific types.
 */
class EventTypeSpecification extends EventSpecification {
    constructor(types) {
        super();
        this.types = types;
    }
    isSatisfiedBy(event) {
        return this.matchesAnyType(event, this.types);
    }
    getDescription() {
        return `Event type is one of: ${this.types.join(', ')}`;
    }
}
exports.EventTypeSpecification = EventTypeSpecification;
/**
 * Event Severity Specification
 *
 * Specification for events of specific severities.
 */
class EventSeveritySpecification extends EventSpecification {
    constructor(severities) {
        super();
        this.severities = severities;
    }
    isSatisfiedBy(event) {
        return this.matchesAnySeverity(event, this.severities);
    }
    getDescription() {
        return `Event severity is one of: ${this.severities.join(', ')}`;
    }
}
exports.EventSeveritySpecification = EventSeveritySpecification;
/**
 * Event Status Specification
 *
 * Specification for events with specific statuses.
 */
class EventStatusSpecification extends EventSpecification {
    constructor(statuses) {
        super();
        this.statuses = statuses;
    }
    isSatisfiedBy(event) {
        return this.matchesAnyStatus(event, this.statuses);
    }
    getDescription() {
        return `Event status is one of: ${this.statuses.join(', ')}`;
    }
}
exports.EventStatusSpecification = EventStatusSpecification;
/**
 * Event Processing Status Specification
 *
 * Specification for events with specific processing statuses.
 */
class EventProcessingStatusSpecification extends EventSpecification {
    constructor(statuses) {
        super();
        this.statuses = statuses;
    }
    isSatisfiedBy(event) {
        return this.matchesAnyProcessingStatus(event, this.statuses);
    }
    getDescription() {
        return `Event processing status is one of: ${this.statuses.join(', ')}`;
    }
}
exports.EventProcessingStatusSpecification = EventProcessingStatusSpecification;
/**
 * Event Source Type Specification
 *
 * Specification for events from specific source types.
 */
class EventSourceTypeSpecification extends EventSpecification {
    constructor(sourceTypes) {
        super();
        this.sourceTypes = sourceTypes;
    }
    isSatisfiedBy(event) {
        return this.matchesAnySourceType(event, this.sourceTypes);
    }
    getDescription() {
        return `Event source type is one of: ${this.sourceTypes.join(', ')}`;
    }
}
exports.EventSourceTypeSpecification = EventSourceTypeSpecification;
/**
 * Event Tag Specification
 *
 * Specification for events with specific tags.
 */
class EventTagSpecification extends EventSpecification {
    constructor(tags, requireAll = false) {
        super();
        this.tags = tags;
        this.requireAll = requireAll;
    }
    isSatisfiedBy(event) {
        return this.requireAll
            ? this.hasAllTags(event, this.tags)
            : this.hasAnyTag(event, this.tags);
    }
    getDescription() {
        const operator = this.requireAll ? 'all' : 'any';
        return `Event has ${operator} of these tags: ${this.tags.join(', ')}`;
    }
}
exports.EventTagSpecification = EventTagSpecification;
/**
 * Event Risk Score Range Specification
 *
 * Specification for events within a specific risk score range.
 */
class EventRiskScoreRangeSpecification extends EventSpecification {
    constructor(minScore, maxScore) {
        super();
        this.minScore = minScore;
        this.maxScore = maxScore;
    }
    isSatisfiedBy(event) {
        return this.isRiskScoreWithinRange(event, this.minScore, this.maxScore);
    }
    getDescription() {
        if (this.minScore !== undefined && this.maxScore !== undefined) {
            return `Event risk score is between ${this.minScore} and ${this.maxScore}`;
        }
        else if (this.minScore !== undefined) {
            return `Event risk score is at least ${this.minScore}`;
        }
        else if (this.maxScore !== undefined) {
            return `Event risk score is at most ${this.maxScore}`;
        }
        return 'Event has any risk score';
    }
}
exports.EventRiskScoreRangeSpecification = EventRiskScoreRangeSpecification;
/**
 * Event Age Range Specification
 *
 * Specification for events within a specific age range.
 */
class EventAgeRangeSpecification extends EventSpecification {
    constructor(minAgeMs, maxAgeMs) {
        super();
        this.minAgeMs = minAgeMs;
        this.maxAgeMs = maxAgeMs;
    }
    isSatisfiedBy(event) {
        return this.isAgeWithinRange(event, this.minAgeMs, this.maxAgeMs);
    }
    getDescription() {
        const formatTime = (ms) => {
            const hours = Math.floor(ms / 3600000);
            const minutes = Math.floor((ms % 3600000) / 60000);
            return `${hours}h ${minutes}m`;
        };
        if (this.minAgeMs !== undefined && this.maxAgeMs !== undefined) {
            return `Event age is between ${formatTime(this.minAgeMs)} and ${formatTime(this.maxAgeMs)}`;
        }
        else if (this.minAgeMs !== undefined) {
            return `Event is at least ${formatTime(this.minAgeMs)} old`;
        }
        else if (this.maxAgeMs !== undefined) {
            return `Event is at most ${formatTime(this.maxAgeMs)} old`;
        }
        return 'Event has any age';
    }
}
exports.EventAgeRangeSpecification = EventAgeRangeSpecification;
/**
 * Event Correlation Specification
 *
 * Specification for events with specific correlation properties.
 */
class EventCorrelationSpecification extends EventSpecification {
    constructor(correlationId, parentEventId, hasCorrelation = true) {
        super();
        this.correlationId = correlationId;
        this.parentEventId = parentEventId;
        this.hasCorrelation = hasCorrelation;
    }
    isSatisfiedBy(event) {
        if (this.correlationId) {
            return event.correlationId === this.correlationId;
        }
        if (this.parentEventId) {
            return event.parentEventId?.equals(this.parentEventId) || false;
        }
        if (this.hasCorrelation) {
            return event.correlationId !== undefined || event.parentEventId !== undefined;
        }
        else {
            return event.correlationId === undefined && event.parentEventId === undefined;
        }
    }
    getDescription() {
        if (this.correlationId) {
            return `Event has correlation ID: ${this.correlationId}`;
        }
        if (this.parentEventId) {
            return `Event has parent ID: ${this.parentEventId.toString()}`;
        }
        return this.hasCorrelation
            ? 'Event has correlation information'
            : 'Event has no correlation information';
    }
}
exports.EventCorrelationSpecification = EventCorrelationSpecification;
/**
 * Failed Processing Event Specification
 *
 * Specification for events that have failed processing.
 */
class FailedProcessingEventSpecification extends EventSpecification {
    isSatisfiedBy(event) {
        return event.hasProcessingFailed();
    }
    getDescription() {
        return 'Event processing has failed';
    }
}
exports.FailedProcessingEventSpecification = FailedProcessingEventSpecification;
/**
 * Max Attempts Exceeded Specification
 *
 * Specification for events that have exceeded maximum processing attempts.
 */
class MaxAttemptsExceededSpecification extends EventSpecification {
    isSatisfiedBy(event) {
        return event.hasExceededMaxAttempts();
    }
    getDescription() {
        return 'Event has exceeded maximum processing attempts';
    }
}
exports.MaxAttemptsExceededSpecification = MaxAttemptsExceededSpecification;
/**
 * Requires Attention Specification
 *
 * Specification for events that require human attention.
 */
class RequiresAttentionSpecification extends EventSpecification {
    isSatisfiedBy(event) {
        return event.isHighSeverity() ||
            event.isHighRisk() ||
            event.hasProcessingFailed() ||
            event.hasExceededMaxAttempts() ||
            event.status === event_status_enum_1.EventStatus.INVESTIGATING;
    }
    getDescription() {
        return 'Event requires human attention (high severity, high risk, failed processing, or under investigation)';
    }
}
exports.RequiresAttentionSpecification = RequiresAttentionSpecification;
/**
 * Security Alert Specification
 *
 * Specification for events that are security alerts.
 */
class SecurityAlertSpecification extends EventSpecification {
    isSatisfiedBy(event) {
        return this.matchesAnyType(event, SecurityAlertSpecification.SECURITY_EVENT_TYPES) ||
            event.isHighSeverity() ||
            event.hasTag('security-alert');
    }
    getDescription() {
        return 'Event is a security alert (threat, malware, vulnerability, or high severity)';
    }
}
exports.SecurityAlertSpecification = SecurityAlertSpecification;
SecurityAlertSpecification.SECURITY_EVENT_TYPES = [
    event_type_enum_1.EventType.THREAT_DETECTED,
    event_type_enum_1.EventType.MALWARE_DETECTED,
    event_type_enum_1.EventType.VULNERABILITY_DETECTED,
    event_type_enum_1.EventType.SUSPICIOUS_CONNECTION,
    event_type_enum_1.EventType.PORT_SCAN_DETECTED,
    event_type_enum_1.EventType.DDOS_ATTACK,
    event_type_enum_1.EventType.DATA_BREACH,
    event_type_enum_1.EventType.IOC_DETECTED,
];
/**
 * Composite Event Specification Builder
 *
 * Builder for creating complex event specifications using fluent interface.
 */
class EventSpecificationBuilder {
    constructor() {
        this.specifications = [];
    }
    /**
     * Add high severity filter
     */
    highSeverity() {
        this.specifications.push(new HighSeverityEventSpecification());
        return this;
    }
    /**
     * Add critical severity filter
     */
    critical() {
        this.specifications.push(new CriticalEventSpecification());
        return this;
    }
    /**
     * Add active status filter
     */
    active() {
        this.specifications.push(new ActiveEventSpecification());
        return this;
    }
    /**
     * Add high risk filter
     */
    highRisk() {
        this.specifications.push(new HighRiskEventSpecification());
        return this;
    }
    /**
     * Add recent events filter
     */
    recent(withinMs = 3600000) {
        this.specifications.push(new RecentEventSpecification(withinMs));
        return this;
    }
    /**
     * Add event type filter
     */
    ofTypes(...types) {
        this.specifications.push(new EventTypeSpecification(types));
        return this;
    }
    /**
     * Add severity filter
     */
    withSeverities(...severities) {
        this.specifications.push(new EventSeveritySpecification(severities));
        return this;
    }
    /**
     * Add source type filter
     */
    fromSources(...sourceTypes) {
        this.specifications.push(new EventSourceTypeSpecification(sourceTypes));
        return this;
    }
    /**
     * Add tag filter
     */
    withTags(tags, requireAll = false) {
        this.specifications.push(new EventTagSpecification(tags, requireAll));
        return this;
    }
    /**
     * Add risk score range filter
     */
    riskScoreRange(minScore, maxScore) {
        this.specifications.push(new EventRiskScoreRangeSpecification(minScore, maxScore));
        return this;
    }
    /**
     * Add requires attention filter
     */
    requiresAttention() {
        this.specifications.push(new RequiresAttentionSpecification());
        return this;
    }
    /**
     * Add security alert filter
     */
    securityAlerts() {
        this.specifications.push(new SecurityAlertSpecification());
        return this;
    }
    /**
     * Build the final specification using AND logic
     */
    build() {
        if (this.specifications.length === 0) {
            throw new Error('At least one specification must be added');
        }
        if (this.specifications.length === 1) {
            return this.specifications[0];
        }
        // Combine all specifications with AND logic
        let combined = this.specifications[0];
        for (let i = 1; i < this.specifications.length; i++) {
            combined = combined.and(this.specifications[i]);
        }
        return combined;
    }
    /**
     * Build the final specification using OR logic
     */
    buildWithOr() {
        if (this.specifications.length === 0) {
            throw new Error('At least one specification must be added');
        }
        if (this.specifications.length === 1) {
            return this.specifications[0];
        }
        // Combine all specifications with OR logic
        let combined = this.specifications[0];
        for (let i = 1; i < this.specifications.length; i++) {
            combined = combined.or(this.specifications[i]);
        }
        return combined;
    }
    /**
     * Create a new builder instance
     */
    static create() {
        return new EventSpecificationBuilder();
    }
}
exports.EventSpecificationBuilder = EventSpecificationBuilder;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFxzcGVjaWZpY2F0aW9uc1xcZXZlbnQuc3BlY2lmaWNhdGlvbi50cyIsIm1hcHBpbmdzIjoiOzs7QUFBQSw2REFBOEU7QUFFOUUsOERBQXFEO0FBRXJELGtFQUF5RDtBQUl6RDs7Ozs7R0FLRztBQUNILE1BQXNCLGtCQUFtQixTQUFRLGlDQUF3QjtJQUN2RTs7T0FFRztJQUNPLGNBQWMsQ0FBQyxLQUFZLEVBQUUsS0FBa0I7UUFDdkQsT0FBTyxLQUFLLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQztJQUNwQyxDQUFDO0lBRUQ7O09BRUc7SUFDTyxrQkFBa0IsQ0FBQyxLQUFZLEVBQUUsVUFBMkI7UUFDcEUsT0FBTyxVQUFVLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQztJQUM3QyxDQUFDO0lBRUQ7O09BRUc7SUFDTyxnQkFBZ0IsQ0FBQyxLQUFZLEVBQUUsUUFBdUI7UUFDOUQsT0FBTyxRQUFRLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQztJQUN6QyxDQUFDO0lBRUQ7O09BRUc7SUFDTywwQkFBMEIsQ0FBQyxLQUFZLEVBQUUsUUFBaUM7UUFDbEYsT0FBTyxRQUFRLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO0lBQ25ELENBQUM7SUFFRDs7T0FFRztJQUNPLG9CQUFvQixDQUFDLEtBQVksRUFBRSxXQUE4QjtRQUN6RSxPQUFPLFdBQVcsQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUM7SUFDMUQsQ0FBQztJQUVEOztPQUVHO0lBQ08sU0FBUyxDQUFDLEtBQVksRUFBRSxJQUFjO1FBQzlDLE9BQU8sS0FBSyxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsQ0FBQztJQUMvQixDQUFDO0lBRUQ7O09BRUc7SUFDTyxVQUFVLENBQUMsS0FBWSxFQUFFLElBQWM7UUFDL0MsT0FBTyxLQUFLLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxDQUFDO0lBQ2hDLENBQUM7SUFFRDs7T0FFRztJQUNPLGdCQUFnQixDQUFDLEtBQVksRUFBRSxRQUFpQixFQUFFLFFBQWlCO1FBQzNFLE1BQU0sR0FBRyxHQUFHLEtBQUssQ0FBQyxNQUFNLEVBQUUsQ0FBQztRQUUzQixJQUFJLFFBQVEsS0FBSyxTQUFTLElBQUksR0FBRyxHQUFHLFFBQVEsRUFBRSxDQUFDO1lBQzdDLE9BQU8sS0FBSyxDQUFDO1FBQ2YsQ0FBQztRQUVELElBQUksUUFBUSxLQUFLLFNBQVMsSUFBSSxHQUFHLEdBQUcsUUFBUSxFQUFFLENBQUM7WUFDN0MsT0FBTyxLQUFLLENBQUM7UUFDZixDQUFDO1FBRUQsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQ7O09BRUc7SUFDTyxzQkFBc0IsQ0FBQyxLQUFZLEVBQUUsUUFBaUIsRUFBRSxRQUFpQjtRQUNqRixNQUFNLFNBQVMsR0FBRyxLQUFLLENBQUMsU0FBUyxDQUFDO1FBRWxDLElBQUksU0FBUyxLQUFLLFNBQVMsRUFBRSxDQUFDO1lBQzVCLE9BQU8sUUFBUSxLQUFLLFNBQVMsQ0FBQyxDQUFDLG9EQUFvRDtRQUNyRixDQUFDO1FBRUQsSUFBSSxRQUFRLEtBQUssU0FBUyxJQUFJLFNBQVMsR0FBRyxRQUFRLEVBQUUsQ0FBQztZQUNuRCxPQUFPLEtBQUssQ0FBQztRQUNmLENBQUM7UUFFRCxJQUFJLFFBQVEsS0FBSyxTQUFTLElBQUksU0FBUyxHQUFHLFFBQVEsRUFBRSxDQUFDO1lBQ25ELE9BQU8sS0FBSyxDQUFDO1FBQ2YsQ0FBQztRQUVELE9BQU8sSUFBSSxDQUFDO0lBQ2QsQ0FBQztDQUNGO0FBdkZELGdEQXVGQztBQUVEOzs7O0dBSUc7QUFDSCxNQUFhLDhCQUErQixTQUFRLGtCQUFrQjtJQUNwRSxhQUFhLENBQUMsS0FBWTtRQUN4QixPQUFPLEtBQUssQ0FBQyxjQUFjLEVBQUUsQ0FBQztJQUNoQyxDQUFDO0lBRUQsY0FBYztRQUNaLE9BQU8scUNBQXFDLENBQUM7SUFDL0MsQ0FBQztDQUNGO0FBUkQsd0VBUUM7QUFFRDs7OztHQUlHO0FBQ0gsTUFBYSwwQkFBMkIsU0FBUSxrQkFBa0I7SUFDaEUsYUFBYSxDQUFDLEtBQVk7UUFDeEIsT0FBTyxLQUFLLENBQUMsVUFBVSxFQUFFLENBQUM7SUFDNUIsQ0FBQztJQUVELGNBQWM7UUFDWixPQUFPLDZCQUE2QixDQUFDO0lBQ3ZDLENBQUM7Q0FDRjtBQVJELGdFQVFDO0FBRUQ7Ozs7R0FJRztBQUNILE1BQWEsd0JBQXlCLFNBQVEsa0JBQWtCO0lBQzlELGFBQWEsQ0FBQyxLQUFZO1FBQ3hCLE9BQU8sS0FBSyxDQUFDLFFBQVEsRUFBRSxDQUFDO0lBQzFCLENBQUM7SUFFRCxjQUFjO1FBQ1osT0FBTywwQ0FBMEMsQ0FBQztJQUNwRCxDQUFDO0NBQ0Y7QUFSRCw0REFRQztBQUVEOzs7O0dBSUc7QUFDSCxNQUFhLDBCQUEyQixTQUFRLGtCQUFrQjtJQUNoRSxhQUFhLENBQUMsS0FBWTtRQUN4QixPQUFPLEtBQUssQ0FBQyxVQUFVLEVBQUUsQ0FBQztJQUM1QixDQUFDO0lBRUQsY0FBYztRQUNaLE9BQU8sbUNBQW1DLENBQUM7SUFDN0MsQ0FBQztDQUNGO0FBUkQsZ0VBUUM7QUFFRDs7OztHQUlHO0FBQ0gsTUFBYSx3QkFBeUIsU0FBUSxrQkFBa0I7SUFDOUQsWUFBNkIsV0FBbUIsT0FBTztRQUNyRCxLQUFLLEVBQUUsQ0FBQztRQURtQixhQUFRLEdBQVIsUUFBUSxDQUFrQjtJQUV2RCxDQUFDO0lBRUQsYUFBYSxDQUFDLEtBQVk7UUFDeEIsT0FBTyxLQUFLLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQztJQUN2QyxDQUFDO0lBRUQsY0FBYztRQUNaLE1BQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLFFBQVEsR0FBRyxPQUFPLENBQUMsQ0FBQztRQUNsRCxNQUFNLE9BQU8sR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsR0FBRyxPQUFPLENBQUMsR0FBRyxLQUFLLENBQUMsQ0FBQztRQUM5RCxPQUFPLHlCQUF5QixLQUFLLEtBQUssT0FBTyxHQUFHLENBQUM7SUFDdkQsQ0FBQztDQUNGO0FBZEQsNERBY0M7QUFFRDs7OztHQUlHO0FBQ0gsTUFBYSx1QkFBd0IsU0FBUSxrQkFBa0I7SUFDN0QsWUFBNkIsY0FBc0IsUUFBUTtRQUN6RCxLQUFLLEVBQUUsQ0FBQztRQURtQixnQkFBVyxHQUFYLFdBQVcsQ0FBbUI7SUFFM0QsQ0FBQztJQUVELGFBQWEsQ0FBQyxLQUFZO1FBQ3hCLE9BQU8sS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUM7SUFDekMsQ0FBQztJQUVELGNBQWM7UUFDWixNQUFNLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxXQUFXLEdBQUcsT0FBTyxDQUFDLENBQUM7UUFDckQsT0FBTyx1QkFBdUIsS0FBSyxRQUFRLENBQUM7SUFDOUMsQ0FBQztDQUNGO0FBYkQsMERBYUM7QUFFRDs7OztHQUlHO0FBQ0gsTUFBYSxzQkFBdUIsU0FBUSxrQkFBa0I7SUFDNUQsWUFBNkIsS0FBa0I7UUFDN0MsS0FBSyxFQUFFLENBQUM7UUFEbUIsVUFBSyxHQUFMLEtBQUssQ0FBYTtJQUUvQyxDQUFDO0lBRUQsYUFBYSxDQUFDLEtBQVk7UUFDeEIsT0FBTyxJQUFJLENBQUMsY0FBYyxDQUFDLEtBQUssRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7SUFDaEQsQ0FBQztJQUVELGNBQWM7UUFDWixPQUFPLHlCQUF5QixJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDO0lBQzFELENBQUM7Q0FDRjtBQVpELHdEQVlDO0FBRUQ7Ozs7R0FJRztBQUNILE1BQWEsMEJBQTJCLFNBQVEsa0JBQWtCO0lBQ2hFLFlBQTZCLFVBQTJCO1FBQ3RELEtBQUssRUFBRSxDQUFDO1FBRG1CLGVBQVUsR0FBVixVQUFVLENBQWlCO0lBRXhELENBQUM7SUFFRCxhQUFhLENBQUMsS0FBWTtRQUN4QixPQUFPLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDO0lBQ3pELENBQUM7SUFFRCxjQUFjO1FBQ1osT0FBTyw2QkFBNkIsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQztJQUNuRSxDQUFDO0NBQ0Y7QUFaRCxnRUFZQztBQUVEOzs7O0dBSUc7QUFDSCxNQUFhLHdCQUF5QixTQUFRLGtCQUFrQjtJQUM5RCxZQUE2QixRQUF1QjtRQUNsRCxLQUFLLEVBQUUsQ0FBQztRQURtQixhQUFRLEdBQVIsUUFBUSxDQUFlO0lBRXBELENBQUM7SUFFRCxhQUFhLENBQUMsS0FBWTtRQUN4QixPQUFPLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ3JELENBQUM7SUFFRCxjQUFjO1FBQ1osT0FBTywyQkFBMkIsSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQztJQUMvRCxDQUFDO0NBQ0Y7QUFaRCw0REFZQztBQUVEOzs7O0dBSUc7QUFDSCxNQUFhLGtDQUFtQyxTQUFRLGtCQUFrQjtJQUN4RSxZQUE2QixRQUFpQztRQUM1RCxLQUFLLEVBQUUsQ0FBQztRQURtQixhQUFRLEdBQVIsUUFBUSxDQUF5QjtJQUU5RCxDQUFDO0lBRUQsYUFBYSxDQUFDLEtBQVk7UUFDeEIsT0FBTyxJQUFJLENBQUMsMEJBQTBCLENBQUMsS0FBSyxFQUFFLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQztJQUMvRCxDQUFDO0lBRUQsY0FBYztRQUNaLE9BQU8sc0NBQXNDLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUM7SUFDMUUsQ0FBQztDQUNGO0FBWkQsZ0ZBWUM7QUFFRDs7OztHQUlHO0FBQ0gsTUFBYSw0QkFBNkIsU0FBUSxrQkFBa0I7SUFDbEUsWUFBNkIsV0FBOEI7UUFDekQsS0FBSyxFQUFFLENBQUM7UUFEbUIsZ0JBQVcsR0FBWCxXQUFXLENBQW1CO0lBRTNELENBQUM7SUFFRCxhQUFhLENBQUMsS0FBWTtRQUN4QixPQUFPLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDO0lBQzVELENBQUM7SUFFRCxjQUFjO1FBQ1osT0FBTyxnQ0FBZ0MsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQztJQUN2RSxDQUFDO0NBQ0Y7QUFaRCxvRUFZQztBQUVEOzs7O0dBSUc7QUFDSCxNQUFhLHFCQUFzQixTQUFRLGtCQUFrQjtJQUMzRCxZQUNtQixJQUFjLEVBQ2QsYUFBc0IsS0FBSztRQUU1QyxLQUFLLEVBQUUsQ0FBQztRQUhTLFNBQUksR0FBSixJQUFJLENBQVU7UUFDZCxlQUFVLEdBQVYsVUFBVSxDQUFpQjtJQUc5QyxDQUFDO0lBRUQsYUFBYSxDQUFDLEtBQVk7UUFDeEIsT0FBTyxJQUFJLENBQUMsVUFBVTtZQUNwQixDQUFDLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLElBQUksQ0FBQztZQUNuQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO0lBQ3ZDLENBQUM7SUFFRCxjQUFjO1FBQ1osTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUM7UUFDakQsT0FBTyxhQUFhLFFBQVEsbUJBQW1CLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUM7SUFDeEUsQ0FBQztDQUNGO0FBbEJELHNEQWtCQztBQUVEOzs7O0dBSUc7QUFDSCxNQUFhLGdDQUFpQyxTQUFRLGtCQUFrQjtJQUN0RSxZQUNtQixRQUFpQixFQUNqQixRQUFpQjtRQUVsQyxLQUFLLEVBQUUsQ0FBQztRQUhTLGFBQVEsR0FBUixRQUFRLENBQVM7UUFDakIsYUFBUSxHQUFSLFFBQVEsQ0FBUztJQUdwQyxDQUFDO0lBRUQsYUFBYSxDQUFDLEtBQVk7UUFDeEIsT0FBTyxJQUFJLENBQUMsc0JBQXNCLENBQUMsS0FBSyxFQUFFLElBQUksQ0FBQyxRQUFRLEVBQUUsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQzFFLENBQUM7SUFFRCxjQUFjO1FBQ1osSUFBSSxJQUFJLENBQUMsUUFBUSxLQUFLLFNBQVMsSUFBSSxJQUFJLENBQUMsUUFBUSxLQUFLLFNBQVMsRUFBRSxDQUFDO1lBQy9ELE9BQU8sK0JBQStCLElBQUksQ0FBQyxRQUFRLFFBQVEsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDO1FBQzdFLENBQUM7YUFBTSxJQUFJLElBQUksQ0FBQyxRQUFRLEtBQUssU0FBUyxFQUFFLENBQUM7WUFDdkMsT0FBTyxnQ0FBZ0MsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDO1FBQ3pELENBQUM7YUFBTSxJQUFJLElBQUksQ0FBQyxRQUFRLEtBQUssU0FBUyxFQUFFLENBQUM7WUFDdkMsT0FBTywrQkFBK0IsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDO1FBQ3hELENBQUM7UUFDRCxPQUFPLDBCQUEwQixDQUFDO0lBQ3BDLENBQUM7Q0FDRjtBQXRCRCw0RUFzQkM7QUFFRDs7OztHQUlHO0FBQ0gsTUFBYSwwQkFBMkIsU0FBUSxrQkFBa0I7SUFDaEUsWUFDbUIsUUFBaUIsRUFDakIsUUFBaUI7UUFFbEMsS0FBSyxFQUFFLENBQUM7UUFIUyxhQUFRLEdBQVIsUUFBUSxDQUFTO1FBQ2pCLGFBQVEsR0FBUixRQUFRLENBQVM7SUFHcEMsQ0FBQztJQUVELGFBQWEsQ0FBQyxLQUFZO1FBQ3hCLE9BQU8sSUFBSSxDQUFDLGdCQUFnQixDQUFDLEtBQUssRUFBRSxJQUFJLENBQUMsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQztJQUNwRSxDQUFDO0lBRUQsY0FBYztRQUNaLE1BQU0sVUFBVSxHQUFHLENBQUMsRUFBVSxFQUFFLEVBQUU7WUFDaEMsTUFBTSxLQUFLLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFLEdBQUcsT0FBTyxDQUFDLENBQUM7WUFDdkMsTUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsR0FBRyxPQUFPLENBQUMsR0FBRyxLQUFLLENBQUMsQ0FBQztZQUNuRCxPQUFPLEdBQUcsS0FBSyxLQUFLLE9BQU8sR0FBRyxDQUFDO1FBQ2pDLENBQUMsQ0FBQztRQUVGLElBQUksSUFBSSxDQUFDLFFBQVEsS0FBSyxTQUFTLElBQUksSUFBSSxDQUFDLFFBQVEsS0FBSyxTQUFTLEVBQUUsQ0FBQztZQUMvRCxPQUFPLHdCQUF3QixVQUFVLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxRQUFRLFVBQVUsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQztRQUM5RixDQUFDO2FBQU0sSUFBSSxJQUFJLENBQUMsUUFBUSxLQUFLLFNBQVMsRUFBRSxDQUFDO1lBQ3ZDLE9BQU8scUJBQXFCLFVBQVUsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQztRQUM5RCxDQUFDO2FBQU0sSUFBSSxJQUFJLENBQUMsUUFBUSxLQUFLLFNBQVMsRUFBRSxDQUFDO1lBQ3ZDLE9BQU8sb0JBQW9CLFVBQVUsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQztRQUM3RCxDQUFDO1FBQ0QsT0FBTyxtQkFBbUIsQ0FBQztJQUM3QixDQUFDO0NBQ0Y7QUE1QkQsZ0VBNEJDO0FBRUQ7Ozs7R0FJRztBQUNILE1BQWEsNkJBQThCLFNBQVEsa0JBQWtCO0lBQ25FLFlBQ21CLGFBQXNCLEVBQ3RCLGFBQThCLEVBQzlCLGlCQUEwQixJQUFJO1FBRS9DLEtBQUssRUFBRSxDQUFDO1FBSlMsa0JBQWEsR0FBYixhQUFhLENBQVM7UUFDdEIsa0JBQWEsR0FBYixhQUFhLENBQWlCO1FBQzlCLG1CQUFjLEdBQWQsY0FBYyxDQUFnQjtJQUdqRCxDQUFDO0lBRUQsYUFBYSxDQUFDLEtBQVk7UUFDeEIsSUFBSSxJQUFJLENBQUMsYUFBYSxFQUFFLENBQUM7WUFDdkIsT0FBTyxLQUFLLENBQUMsYUFBYSxLQUFLLElBQUksQ0FBQyxhQUFhLENBQUM7UUFDcEQsQ0FBQztRQUVELElBQUksSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBQ3ZCLE9BQU8sS0FBSyxDQUFDLGFBQWEsRUFBRSxNQUFNLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLEtBQUssQ0FBQztRQUNsRSxDQUFDO1FBRUQsSUFBSSxJQUFJLENBQUMsY0FBYyxFQUFFLENBQUM7WUFDeEIsT0FBTyxLQUFLLENBQUMsYUFBYSxLQUFLLFNBQVMsSUFBSSxLQUFLLENBQUMsYUFBYSxLQUFLLFNBQVMsQ0FBQztRQUNoRixDQUFDO2FBQU0sQ0FBQztZQUNOLE9BQU8sS0FBSyxDQUFDLGFBQWEsS0FBSyxTQUFTLElBQUksS0FBSyxDQUFDLGFBQWEsS0FBSyxTQUFTLENBQUM7UUFDaEYsQ0FBQztJQUNILENBQUM7SUFFRCxjQUFjO1FBQ1osSUFBSSxJQUFJLENBQUMsYUFBYSxFQUFFLENBQUM7WUFDdkIsT0FBTyw2QkFBNkIsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO1FBQzNELENBQUM7UUFFRCxJQUFJLElBQUksQ0FBQyxhQUFhLEVBQUUsQ0FBQztZQUN2QixPQUFPLHdCQUF3QixJQUFJLENBQUMsYUFBYSxDQUFDLFFBQVEsRUFBRSxFQUFFLENBQUM7UUFDakUsQ0FBQztRQUVELE9BQU8sSUFBSSxDQUFDLGNBQWM7WUFDeEIsQ0FBQyxDQUFDLG1DQUFtQztZQUNyQyxDQUFDLENBQUMsc0NBQXNDLENBQUM7SUFDN0MsQ0FBQztDQUNGO0FBdENELHNFQXNDQztBQUVEOzs7O0dBSUc7QUFDSCxNQUFhLGtDQUFtQyxTQUFRLGtCQUFrQjtJQUN4RSxhQUFhLENBQUMsS0FBWTtRQUN4QixPQUFPLEtBQUssQ0FBQyxtQkFBbUIsRUFBRSxDQUFDO0lBQ3JDLENBQUM7SUFFRCxjQUFjO1FBQ1osT0FBTyw2QkFBNkIsQ0FBQztJQUN2QyxDQUFDO0NBQ0Y7QUFSRCxnRkFRQztBQUVEOzs7O0dBSUc7QUFDSCxNQUFhLGdDQUFpQyxTQUFRLGtCQUFrQjtJQUN0RSxhQUFhLENBQUMsS0FBWTtRQUN4QixPQUFPLEtBQUssQ0FBQyxzQkFBc0IsRUFBRSxDQUFDO0lBQ3hDLENBQUM7SUFFRCxjQUFjO1FBQ1osT0FBTyxnREFBZ0QsQ0FBQztJQUMxRCxDQUFDO0NBQ0Y7QUFSRCw0RUFRQztBQUVEOzs7O0dBSUc7QUFDSCxNQUFhLDhCQUErQixTQUFRLGtCQUFrQjtJQUNwRSxhQUFhLENBQUMsS0FBWTtRQUN4QixPQUFPLEtBQUssQ0FBQyxjQUFjLEVBQUU7WUFDdEIsS0FBSyxDQUFDLFVBQVUsRUFBRTtZQUNsQixLQUFLLENBQUMsbUJBQW1CLEVBQUU7WUFDM0IsS0FBSyxDQUFDLHNCQUFzQixFQUFFO1lBQzlCLEtBQUssQ0FBQyxNQUFNLEtBQUssK0JBQVcsQ0FBQyxhQUFhLENBQUM7SUFDcEQsQ0FBQztJQUVELGNBQWM7UUFDWixPQUFPLHNHQUFzRyxDQUFDO0lBQ2hILENBQUM7Q0FDRjtBQVpELHdFQVlDO0FBRUQ7Ozs7R0FJRztBQUNILE1BQWEsMEJBQTJCLFNBQVEsa0JBQWtCO0lBWWhFLGFBQWEsQ0FBQyxLQUFZO1FBQ3hCLE9BQU8sSUFBSSxDQUFDLGNBQWMsQ0FBQyxLQUFLLEVBQUUsMEJBQTBCLENBQUMsb0JBQW9CLENBQUM7WUFDM0UsS0FBSyxDQUFDLGNBQWMsRUFBRTtZQUN0QixLQUFLLENBQUMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLENBQUM7SUFDeEMsQ0FBQztJQUVELGNBQWM7UUFDWixPQUFPLDhFQUE4RSxDQUFDO0lBQ3hGLENBQUM7O0FBcEJILGdFQXFCQztBQXBCeUIsK0NBQW9CLEdBQUc7SUFDN0MsMkJBQVMsQ0FBQyxlQUFlO0lBQ3pCLDJCQUFTLENBQUMsZ0JBQWdCO0lBQzFCLDJCQUFTLENBQUMsc0JBQXNCO0lBQ2hDLDJCQUFTLENBQUMscUJBQXFCO0lBQy9CLDJCQUFTLENBQUMsa0JBQWtCO0lBQzVCLDJCQUFTLENBQUMsV0FBVztJQUNyQiwyQkFBUyxDQUFDLFdBQVc7SUFDckIsMkJBQVMsQ0FBQyxZQUFZO0NBQ3ZCLENBQUM7QUFhSjs7OztHQUlHO0FBQ0gsTUFBYSx5QkFBeUI7SUFBdEM7UUFDVSxtQkFBYyxHQUF5QixFQUFFLENBQUM7SUFrSnBELENBQUM7SUFoSkM7O09BRUc7SUFDSCxZQUFZO1FBQ1YsSUFBSSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsSUFBSSw4QkFBOEIsRUFBRSxDQUFDLENBQUM7UUFDL0QsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxRQUFRO1FBQ04sSUFBSSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsSUFBSSwwQkFBMEIsRUFBRSxDQUFDLENBQUM7UUFDM0QsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxNQUFNO1FBQ0osSUFBSSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsSUFBSSx3QkFBd0IsRUFBRSxDQUFDLENBQUM7UUFDekQsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxRQUFRO1FBQ04sSUFBSSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsSUFBSSwwQkFBMEIsRUFBRSxDQUFDLENBQUM7UUFDM0QsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxNQUFNLENBQUMsV0FBbUIsT0FBTztRQUMvQixJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxJQUFJLHdCQUF3QixDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7UUFDakUsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxPQUFPLENBQUMsR0FBRyxLQUFrQjtRQUMzQixJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxJQUFJLHNCQUFzQixDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7UUFDNUQsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxjQUFjLENBQUMsR0FBRyxVQUEyQjtRQUMzQyxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxJQUFJLDBCQUEwQixDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUM7UUFDckUsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxXQUFXLENBQUMsR0FBRyxXQUE4QjtRQUMzQyxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxJQUFJLDRCQUE0QixDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUM7UUFDeEUsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxRQUFRLENBQUMsSUFBYyxFQUFFLGFBQXNCLEtBQUs7UUFDbEQsSUFBSSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsSUFBSSxxQkFBcUIsQ0FBQyxJQUFJLEVBQUUsVUFBVSxDQUFDLENBQUMsQ0FBQztRQUN0RSxPQUFPLElBQUksQ0FBQztJQUNkLENBQUM7SUFFRDs7T0FFRztJQUNILGNBQWMsQ0FBQyxRQUFpQixFQUFFLFFBQWlCO1FBQ2pELElBQUksQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLElBQUksZ0NBQWdDLENBQUMsUUFBUSxFQUFFLFFBQVEsQ0FBQyxDQUFDLENBQUM7UUFDbkYsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxpQkFBaUI7UUFDZixJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxJQUFJLDhCQUE4QixFQUFFLENBQUMsQ0FBQztRQUMvRCxPQUFPLElBQUksQ0FBQztJQUNkLENBQUM7SUFFRDs7T0FFRztJQUNILGNBQWM7UUFDWixJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxJQUFJLDBCQUEwQixFQUFFLENBQUMsQ0FBQztRQUMzRCxPQUFPLElBQUksQ0FBQztJQUNkLENBQUM7SUFFRDs7T0FFRztJQUNILEtBQUs7UUFDSCxJQUFJLElBQUksQ0FBQyxjQUFjLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRSxDQUFDO1lBQ3JDLE1BQU0sSUFBSSxLQUFLLENBQUMsMENBQTBDLENBQUMsQ0FBQztRQUM5RCxDQUFDO1FBRUQsSUFBSSxJQUFJLENBQUMsY0FBYyxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUUsQ0FBQztZQUNyQyxPQUFPLElBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDaEMsQ0FBQztRQUVELDRDQUE0QztRQUM1QyxJQUFJLFFBQVEsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3RDLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ3BELFFBQVEsR0FBRyxRQUFRLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQyxDQUFDLENBQXVCLENBQUM7UUFDeEUsQ0FBQztRQUVELE9BQU8sUUFBUSxDQUFDO0lBQ2xCLENBQUM7SUFFRDs7T0FFRztJQUNILFdBQVc7UUFDVCxJQUFJLElBQUksQ0FBQyxjQUFjLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRSxDQUFDO1lBQ3JDLE1BQU0sSUFBSSxLQUFLLENBQUMsMENBQTBDLENBQUMsQ0FBQztRQUM5RCxDQUFDO1FBRUQsSUFBSSxJQUFJLENBQUMsY0FBYyxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUUsQ0FBQztZQUNyQyxPQUFPLElBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDaEMsQ0FBQztRQUVELDJDQUEyQztRQUMzQyxJQUFJLFFBQVEsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3RDLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ3BELFFBQVEsR0FBRyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQyxDQUFDLENBQXVCLENBQUM7UUFDdkUsQ0FBQztRQUVELE9BQU8sUUFBUSxDQUFDO0lBQ2xCLENBQUM7SUFFRDs7T0FFRztJQUNILE1BQU0sQ0FBQyxNQUFNO1FBQ1gsT0FBTyxJQUFJLHlCQUF5QixFQUFFLENBQUM7SUFDekMsQ0FBQztDQUNGO0FBbkpELDhEQW1KQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXGNvcmVcXHNlY3VyaXR5XFxkb21haW5cXHNwZWNpZmljYXRpb25zXFxldmVudC5zcGVjaWZpY2F0aW9uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJhc2VTcGVjaWZpY2F0aW9uLCBVbmlxdWVFbnRpdHlJZCB9IGZyb20gJy4uLy4uLy4uLy4uL3NoYXJlZC1rZXJuZWwnO1xyXG5pbXBvcnQgeyBFdmVudCB9IGZyb20gJy4uL2VudGl0aWVzL2V2ZW50LmVudGl0eSc7XHJcbmltcG9ydCB7IEV2ZW50VHlwZSB9IGZyb20gJy4uL2VudW1zL2V2ZW50LXR5cGUuZW51bSc7XHJcbmltcG9ydCB7IEV2ZW50U2V2ZXJpdHkgfSBmcm9tICcuLi9lbnVtcy9ldmVudC1zZXZlcml0eS5lbnVtJztcclxuaW1wb3J0IHsgRXZlbnRTdGF0dXMgfSBmcm9tICcuLi9lbnVtcy9ldmVudC1zdGF0dXMuZW51bSc7XHJcbmltcG9ydCB7IEV2ZW50UHJvY2Vzc2luZ1N0YXR1cyB9IGZyb20gJy4uL2VudW1zL2V2ZW50LXByb2Nlc3Npbmctc3RhdHVzLmVudW0nO1xyXG5pbXBvcnQgeyBFdmVudFNvdXJjZVR5cGUgfSBmcm9tICcuLi9lbnVtcy9ldmVudC1zb3VyY2UtdHlwZS5lbnVtJztcclxuXHJcbi8qKlxyXG4gKiBFdmVudCBTcGVjaWZpY2F0aW9uIEJhc2UgQ2xhc3NcclxuICogXHJcbiAqIEJhc2UgY2xhc3MgZm9yIGFsbCBldmVudC1yZWxhdGVkIHNwZWNpZmljYXRpb25zLlxyXG4gKiBQcm92aWRlcyBjb21tb24gZnVuY3Rpb25hbGl0eSBmb3IgZXZlbnQgZmlsdGVyaW5nIGFuZCB2YWxpZGF0aW9uLlxyXG4gKi9cclxuZXhwb3J0IGFic3RyYWN0IGNsYXNzIEV2ZW50U3BlY2lmaWNhdGlvbiBleHRlbmRzIEJhc2VTcGVjaWZpY2F0aW9uPEV2ZW50PiB7XHJcbiAgLyoqXHJcbiAgICogSGVscGVyIG1ldGhvZCB0byBjaGVjayBpZiBldmVudCBtYXRjaGVzIGFueSBvZiB0aGUgcHJvdmlkZWQgdHlwZXNcclxuICAgKi9cclxuICBwcm90ZWN0ZWQgbWF0Y2hlc0FueVR5cGUoZXZlbnQ6IEV2ZW50LCB0eXBlczogRXZlbnRUeXBlW10pOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0eXBlcy5pbmNsdWRlcyhldmVudC50eXBlKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEhlbHBlciBtZXRob2QgdG8gY2hlY2sgaWYgZXZlbnQgbWF0Y2hlcyBhbnkgb2YgdGhlIHByb3ZpZGVkIHNldmVyaXRpZXNcclxuICAgKi9cclxuICBwcm90ZWN0ZWQgbWF0Y2hlc0FueVNldmVyaXR5KGV2ZW50OiBFdmVudCwgc2V2ZXJpdGllczogRXZlbnRTZXZlcml0eVtdKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gc2V2ZXJpdGllcy5pbmNsdWRlcyhldmVudC5zZXZlcml0eSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBIZWxwZXIgbWV0aG9kIHRvIGNoZWNrIGlmIGV2ZW50IG1hdGNoZXMgYW55IG9mIHRoZSBwcm92aWRlZCBzdGF0dXNlc1xyXG4gICAqL1xyXG4gIHByb3RlY3RlZCBtYXRjaGVzQW55U3RhdHVzKGV2ZW50OiBFdmVudCwgc3RhdHVzZXM6IEV2ZW50U3RhdHVzW10pOiBib29sZWFuIHtcclxuICAgIHJldHVybiBzdGF0dXNlcy5pbmNsdWRlcyhldmVudC5zdGF0dXMpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogSGVscGVyIG1ldGhvZCB0byBjaGVjayBpZiBldmVudCBtYXRjaGVzIGFueSBvZiB0aGUgcHJvdmlkZWQgcHJvY2Vzc2luZyBzdGF0dXNlc1xyXG4gICAqL1xyXG4gIHByb3RlY3RlZCBtYXRjaGVzQW55UHJvY2Vzc2luZ1N0YXR1cyhldmVudDogRXZlbnQsIHN0YXR1c2VzOiBFdmVudFByb2Nlc3NpbmdTdGF0dXNbXSk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHN0YXR1c2VzLmluY2x1ZGVzKGV2ZW50LnByb2Nlc3NpbmdTdGF0dXMpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogSGVscGVyIG1ldGhvZCB0byBjaGVjayBpZiBldmVudCBzb3VyY2UgbWF0Y2hlcyBhbnkgb2YgdGhlIHByb3ZpZGVkIHR5cGVzXHJcbiAgICovXHJcbiAgcHJvdGVjdGVkIG1hdGNoZXNBbnlTb3VyY2VUeXBlKGV2ZW50OiBFdmVudCwgc291cmNlVHlwZXM6IEV2ZW50U291cmNlVHlwZVtdKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gc291cmNlVHlwZXMuaW5jbHVkZXMoZXZlbnQubWV0YWRhdGEuc291cmNlLnR5cGUpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogSGVscGVyIG1ldGhvZCB0byBjaGVjayBpZiBldmVudCBoYXMgYW55IG9mIHRoZSBwcm92aWRlZCB0YWdzXHJcbiAgICovXHJcbiAgcHJvdGVjdGVkIGhhc0FueVRhZyhldmVudDogRXZlbnQsIHRhZ3M6IHN0cmluZ1tdKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gZXZlbnQuaGFzQW55VGFnKHRhZ3MpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogSGVscGVyIG1ldGhvZCB0byBjaGVjayBpZiBldmVudCBoYXMgYWxsIG9mIHRoZSBwcm92aWRlZCB0YWdzXHJcbiAgICovXHJcbiAgcHJvdGVjdGVkIGhhc0FsbFRhZ3MoZXZlbnQ6IEV2ZW50LCB0YWdzOiBzdHJpbmdbXSk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIGV2ZW50Lmhhc0FsbFRhZ3ModGFncyk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBIZWxwZXIgbWV0aG9kIHRvIGNoZWNrIGlmIGV2ZW50IGFnZSBpcyB3aXRoaW4gcmFuZ2VcclxuICAgKi9cclxuICBwcm90ZWN0ZWQgaXNBZ2VXaXRoaW5SYW5nZShldmVudDogRXZlbnQsIG1pbkFnZU1zPzogbnVtYmVyLCBtYXhBZ2VNcz86IG51bWJlcik6IGJvb2xlYW4ge1xyXG4gICAgY29uc3QgYWdlID0gZXZlbnQuZ2V0QWdlKCk7XHJcbiAgICBcclxuICAgIGlmIChtaW5BZ2VNcyAhPT0gdW5kZWZpbmVkICYmIGFnZSA8IG1pbkFnZU1zKSB7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgaWYgKG1heEFnZU1zICE9PSB1bmRlZmluZWQgJiYgYWdlID4gbWF4QWdlTXMpIHtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEhlbHBlciBtZXRob2QgdG8gY2hlY2sgaWYgcmlzayBzY29yZSBpcyB3aXRoaW4gcmFuZ2VcclxuICAgKi9cclxuICBwcm90ZWN0ZWQgaXNSaXNrU2NvcmVXaXRoaW5SYW5nZShldmVudDogRXZlbnQsIG1pblNjb3JlPzogbnVtYmVyLCBtYXhTY29yZT86IG51bWJlcik6IGJvb2xlYW4ge1xyXG4gICAgY29uc3Qgcmlza1Njb3JlID0gZXZlbnQucmlza1Njb3JlO1xyXG4gICAgXHJcbiAgICBpZiAocmlza1Njb3JlID09PSB1bmRlZmluZWQpIHtcclxuICAgICAgcmV0dXJuIG1pblNjb3JlID09PSB1bmRlZmluZWQ7IC8vIElmIG5vIG1pbiBzY29yZSByZXF1aXJlZCwgdW5kZWZpbmVkIGlzIGFjY2VwdGFibGVcclxuICAgIH1cclxuICAgIFxyXG4gICAgaWYgKG1pblNjb3JlICE9PSB1bmRlZmluZWQgJiYgcmlza1Njb3JlIDwgbWluU2NvcmUpIHtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAobWF4U2NvcmUgIT09IHVuZGVmaW5lZCAmJiByaXNrU2NvcmUgPiBtYXhTY29yZSkge1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiB0cnVlO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEhpZ2ggU2V2ZXJpdHkgRXZlbnQgU3BlY2lmaWNhdGlvblxyXG4gKiBcclxuICogU3BlY2lmaWNhdGlvbiBmb3IgZXZlbnRzIHdpdGggaGlnaCBvciBjcml0aWNhbCBzZXZlcml0eS5cclxuICovXHJcbmV4cG9ydCBjbGFzcyBIaWdoU2V2ZXJpdHlFdmVudFNwZWNpZmljYXRpb24gZXh0ZW5kcyBFdmVudFNwZWNpZmljYXRpb24ge1xyXG4gIGlzU2F0aXNmaWVkQnkoZXZlbnQ6IEV2ZW50KTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gZXZlbnQuaXNIaWdoU2V2ZXJpdHkoKTtcclxuICB9XHJcblxyXG4gIGdldERlc2NyaXB0aW9uKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gJ0V2ZW50IGhhcyBoaWdoIG9yIGNyaXRpY2FsIHNldmVyaXR5JztcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBDcml0aWNhbCBFdmVudCBTcGVjaWZpY2F0aW9uXHJcbiAqIFxyXG4gKiBTcGVjaWZpY2F0aW9uIGZvciBldmVudHMgd2l0aCBjcml0aWNhbCBzZXZlcml0eSBvbmx5LlxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIENyaXRpY2FsRXZlbnRTcGVjaWZpY2F0aW9uIGV4dGVuZHMgRXZlbnRTcGVjaWZpY2F0aW9uIHtcclxuICBpc1NhdGlzZmllZEJ5KGV2ZW50OiBFdmVudCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIGV2ZW50LmlzQ3JpdGljYWwoKTtcclxuICB9XHJcblxyXG4gIGdldERlc2NyaXB0aW9uKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gJ0V2ZW50IGhhcyBjcml0aWNhbCBzZXZlcml0eSc7XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogQWN0aXZlIEV2ZW50IFNwZWNpZmljYXRpb25cclxuICogXHJcbiAqIFNwZWNpZmljYXRpb24gZm9yIGV2ZW50cyB0aGF0IGFyZSBjdXJyZW50bHkgYWN0aXZlIChub3QgcmVzb2x2ZWQgb3IgY2xvc2VkKS5cclxuICovXHJcbmV4cG9ydCBjbGFzcyBBY3RpdmVFdmVudFNwZWNpZmljYXRpb24gZXh0ZW5kcyBFdmVudFNwZWNpZmljYXRpb24ge1xyXG4gIGlzU2F0aXNmaWVkQnkoZXZlbnQ6IEV2ZW50KTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gZXZlbnQuaXNBY3RpdmUoKTtcclxuICB9XHJcblxyXG4gIGdldERlc2NyaXB0aW9uKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gJ0V2ZW50IGlzIGFjdGl2ZSAobm90IHJlc29sdmVkIG9yIGNsb3NlZCknO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEhpZ2ggUmlzayBFdmVudCBTcGVjaWZpY2F0aW9uXHJcbiAqIFxyXG4gKiBTcGVjaWZpY2F0aW9uIGZvciBldmVudHMgd2l0aCBoaWdoIHJpc2sgc2NvcmVzLlxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIEhpZ2hSaXNrRXZlbnRTcGVjaWZpY2F0aW9uIGV4dGVuZHMgRXZlbnRTcGVjaWZpY2F0aW9uIHtcclxuICBpc1NhdGlzZmllZEJ5KGV2ZW50OiBFdmVudCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIGV2ZW50LmlzSGlnaFJpc2soKTtcclxuICB9XHJcblxyXG4gIGdldERlc2NyaXB0aW9uKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gJ0V2ZW50IGhhcyBoaWdoIHJpc2sgc2NvcmUgKD49IDcwKSc7XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogUmVjZW50IEV2ZW50IFNwZWNpZmljYXRpb25cclxuICogXHJcbiAqIFNwZWNpZmljYXRpb24gZm9yIGV2ZW50cyB0aGF0IG9jY3VycmVkIHJlY2VudGx5LlxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIFJlY2VudEV2ZW50U3BlY2lmaWNhdGlvbiBleHRlbmRzIEV2ZW50U3BlY2lmaWNhdGlvbiB7XHJcbiAgY29uc3RydWN0b3IocHJpdmF0ZSByZWFkb25seSB3aXRoaW5NczogbnVtYmVyID0gMzYwMDAwMCkgeyAvLyBEZWZhdWx0IDEgaG91clxyXG4gICAgc3VwZXIoKTtcclxuICB9XHJcblxyXG4gIGlzU2F0aXNmaWVkQnkoZXZlbnQ6IEV2ZW50KTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gZXZlbnQuaXNSZWNlbnQodGhpcy53aXRoaW5Ncyk7XHJcbiAgfVxyXG5cclxuICBnZXREZXNjcmlwdGlvbigpOiBzdHJpbmcge1xyXG4gICAgY29uc3QgaG91cnMgPSBNYXRoLmZsb29yKHRoaXMud2l0aGluTXMgLyAzNjAwMDAwKTtcclxuICAgIGNvbnN0IG1pbnV0ZXMgPSBNYXRoLmZsb29yKCh0aGlzLndpdGhpbk1zICUgMzYwMDAwMCkgLyA2MDAwMCk7XHJcbiAgICByZXR1cm4gYEV2ZW50IG9jY3VycmVkIHdpdGhpbiAke2hvdXJzfWggJHttaW51dGVzfW1gO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIFN0YWxlIEV2ZW50IFNwZWNpZmljYXRpb25cclxuICogXHJcbiAqIFNwZWNpZmljYXRpb24gZm9yIGV2ZW50cyB0aGF0IGFyZSBjb25zaWRlcmVkIHN0YWxlLlxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIFN0YWxlRXZlbnRTcGVjaWZpY2F0aW9uIGV4dGVuZHMgRXZlbnRTcGVjaWZpY2F0aW9uIHtcclxuICBjb25zdHJ1Y3Rvcihwcml2YXRlIHJlYWRvbmx5IG9sZGVyVGhhbk1zOiBudW1iZXIgPSA4NjQwMDAwMCkgeyAvLyBEZWZhdWx0IDI0IGhvdXJzXHJcbiAgICBzdXBlcigpO1xyXG4gIH1cclxuXHJcbiAgaXNTYXRpc2ZpZWRCeShldmVudDogRXZlbnQpOiBib29sZWFuIHtcclxuICAgIHJldHVybiBldmVudC5pc1N0YWxlKHRoaXMub2xkZXJUaGFuTXMpO1xyXG4gIH1cclxuXHJcbiAgZ2V0RGVzY3JpcHRpb24oKTogc3RyaW5nIHtcclxuICAgIGNvbnN0IGhvdXJzID0gTWF0aC5mbG9vcih0aGlzLm9sZGVyVGhhbk1zIC8gMzYwMDAwMCk7XHJcbiAgICByZXR1cm4gYEV2ZW50IGlzIG9sZGVyIHRoYW4gJHtob3Vyc30gaG91cnNgO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEV2ZW50IFR5cGUgU3BlY2lmaWNhdGlvblxyXG4gKiBcclxuICogU3BlY2lmaWNhdGlvbiBmb3IgZXZlbnRzIG9mIHNwZWNpZmljIHR5cGVzLlxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIEV2ZW50VHlwZVNwZWNpZmljYXRpb24gZXh0ZW5kcyBFdmVudFNwZWNpZmljYXRpb24ge1xyXG4gIGNvbnN0cnVjdG9yKHByaXZhdGUgcmVhZG9ubHkgdHlwZXM6IEV2ZW50VHlwZVtdKSB7XHJcbiAgICBzdXBlcigpO1xyXG4gIH1cclxuXHJcbiAgaXNTYXRpc2ZpZWRCeShldmVudDogRXZlbnQpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLm1hdGNoZXNBbnlUeXBlKGV2ZW50LCB0aGlzLnR5cGVzKTtcclxuICB9XHJcblxyXG4gIGdldERlc2NyaXB0aW9uKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gYEV2ZW50IHR5cGUgaXMgb25lIG9mOiAke3RoaXMudHlwZXMuam9pbignLCAnKX1gO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEV2ZW50IFNldmVyaXR5IFNwZWNpZmljYXRpb25cclxuICogXHJcbiAqIFNwZWNpZmljYXRpb24gZm9yIGV2ZW50cyBvZiBzcGVjaWZpYyBzZXZlcml0aWVzLlxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIEV2ZW50U2V2ZXJpdHlTcGVjaWZpY2F0aW9uIGV4dGVuZHMgRXZlbnRTcGVjaWZpY2F0aW9uIHtcclxuICBjb25zdHJ1Y3Rvcihwcml2YXRlIHJlYWRvbmx5IHNldmVyaXRpZXM6IEV2ZW50U2V2ZXJpdHlbXSkge1xyXG4gICAgc3VwZXIoKTtcclxuICB9XHJcblxyXG4gIGlzU2F0aXNmaWVkQnkoZXZlbnQ6IEV2ZW50KTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5tYXRjaGVzQW55U2V2ZXJpdHkoZXZlbnQsIHRoaXMuc2V2ZXJpdGllcyk7XHJcbiAgfVxyXG5cclxuICBnZXREZXNjcmlwdGlvbigpOiBzdHJpbmcge1xyXG4gICAgcmV0dXJuIGBFdmVudCBzZXZlcml0eSBpcyBvbmUgb2Y6ICR7dGhpcy5zZXZlcml0aWVzLmpvaW4oJywgJyl9YDtcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBFdmVudCBTdGF0dXMgU3BlY2lmaWNhdGlvblxyXG4gKiBcclxuICogU3BlY2lmaWNhdGlvbiBmb3IgZXZlbnRzIHdpdGggc3BlY2lmaWMgc3RhdHVzZXMuXHJcbiAqL1xyXG5leHBvcnQgY2xhc3MgRXZlbnRTdGF0dXNTcGVjaWZpY2F0aW9uIGV4dGVuZHMgRXZlbnRTcGVjaWZpY2F0aW9uIHtcclxuICBjb25zdHJ1Y3Rvcihwcml2YXRlIHJlYWRvbmx5IHN0YXR1c2VzOiBFdmVudFN0YXR1c1tdKSB7XHJcbiAgICBzdXBlcigpO1xyXG4gIH1cclxuXHJcbiAgaXNTYXRpc2ZpZWRCeShldmVudDogRXZlbnQpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLm1hdGNoZXNBbnlTdGF0dXMoZXZlbnQsIHRoaXMuc3RhdHVzZXMpO1xyXG4gIH1cclxuXHJcbiAgZ2V0RGVzY3JpcHRpb24oKTogc3RyaW5nIHtcclxuICAgIHJldHVybiBgRXZlbnQgc3RhdHVzIGlzIG9uZSBvZjogJHt0aGlzLnN0YXR1c2VzLmpvaW4oJywgJyl9YDtcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBFdmVudCBQcm9jZXNzaW5nIFN0YXR1cyBTcGVjaWZpY2F0aW9uXHJcbiAqIFxyXG4gKiBTcGVjaWZpY2F0aW9uIGZvciBldmVudHMgd2l0aCBzcGVjaWZpYyBwcm9jZXNzaW5nIHN0YXR1c2VzLlxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIEV2ZW50UHJvY2Vzc2luZ1N0YXR1c1NwZWNpZmljYXRpb24gZXh0ZW5kcyBFdmVudFNwZWNpZmljYXRpb24ge1xyXG4gIGNvbnN0cnVjdG9yKHByaXZhdGUgcmVhZG9ubHkgc3RhdHVzZXM6IEV2ZW50UHJvY2Vzc2luZ1N0YXR1c1tdKSB7XHJcbiAgICBzdXBlcigpO1xyXG4gIH1cclxuXHJcbiAgaXNTYXRpc2ZpZWRCeShldmVudDogRXZlbnQpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLm1hdGNoZXNBbnlQcm9jZXNzaW5nU3RhdHVzKGV2ZW50LCB0aGlzLnN0YXR1c2VzKTtcclxuICB9XHJcblxyXG4gIGdldERlc2NyaXB0aW9uKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gYEV2ZW50IHByb2Nlc3Npbmcgc3RhdHVzIGlzIG9uZSBvZjogJHt0aGlzLnN0YXR1c2VzLmpvaW4oJywgJyl9YDtcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBFdmVudCBTb3VyY2UgVHlwZSBTcGVjaWZpY2F0aW9uXHJcbiAqIFxyXG4gKiBTcGVjaWZpY2F0aW9uIGZvciBldmVudHMgZnJvbSBzcGVjaWZpYyBzb3VyY2UgdHlwZXMuXHJcbiAqL1xyXG5leHBvcnQgY2xhc3MgRXZlbnRTb3VyY2VUeXBlU3BlY2lmaWNhdGlvbiBleHRlbmRzIEV2ZW50U3BlY2lmaWNhdGlvbiB7XHJcbiAgY29uc3RydWN0b3IocHJpdmF0ZSByZWFkb25seSBzb3VyY2VUeXBlczogRXZlbnRTb3VyY2VUeXBlW10pIHtcclxuICAgIHN1cGVyKCk7XHJcbiAgfVxyXG5cclxuICBpc1NhdGlzZmllZEJ5KGV2ZW50OiBFdmVudCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMubWF0Y2hlc0FueVNvdXJjZVR5cGUoZXZlbnQsIHRoaXMuc291cmNlVHlwZXMpO1xyXG4gIH1cclxuXHJcbiAgZ2V0RGVzY3JpcHRpb24oKTogc3RyaW5nIHtcclxuICAgIHJldHVybiBgRXZlbnQgc291cmNlIHR5cGUgaXMgb25lIG9mOiAke3RoaXMuc291cmNlVHlwZXMuam9pbignLCAnKX1gO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEV2ZW50IFRhZyBTcGVjaWZpY2F0aW9uXHJcbiAqIFxyXG4gKiBTcGVjaWZpY2F0aW9uIGZvciBldmVudHMgd2l0aCBzcGVjaWZpYyB0YWdzLlxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIEV2ZW50VGFnU3BlY2lmaWNhdGlvbiBleHRlbmRzIEV2ZW50U3BlY2lmaWNhdGlvbiB7XHJcbiAgY29uc3RydWN0b3IoXHJcbiAgICBwcml2YXRlIHJlYWRvbmx5IHRhZ3M6IHN0cmluZ1tdLFxyXG4gICAgcHJpdmF0ZSByZWFkb25seSByZXF1aXJlQWxsOiBib29sZWFuID0gZmFsc2VcclxuICApIHtcclxuICAgIHN1cGVyKCk7XHJcbiAgfVxyXG5cclxuICBpc1NhdGlzZmllZEJ5KGV2ZW50OiBFdmVudCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWlyZUFsbCBcclxuICAgICAgPyB0aGlzLmhhc0FsbFRhZ3MoZXZlbnQsIHRoaXMudGFncylcclxuICAgICAgOiB0aGlzLmhhc0FueVRhZyhldmVudCwgdGhpcy50YWdzKTtcclxuICB9XHJcblxyXG4gIGdldERlc2NyaXB0aW9uKCk6IHN0cmluZyB7XHJcbiAgICBjb25zdCBvcGVyYXRvciA9IHRoaXMucmVxdWlyZUFsbCA/ICdhbGwnIDogJ2FueSc7XHJcbiAgICByZXR1cm4gYEV2ZW50IGhhcyAke29wZXJhdG9yfSBvZiB0aGVzZSB0YWdzOiAke3RoaXMudGFncy5qb2luKCcsICcpfWA7XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogRXZlbnQgUmlzayBTY29yZSBSYW5nZSBTcGVjaWZpY2F0aW9uXHJcbiAqIFxyXG4gKiBTcGVjaWZpY2F0aW9uIGZvciBldmVudHMgd2l0aGluIGEgc3BlY2lmaWMgcmlzayBzY29yZSByYW5nZS5cclxuICovXHJcbmV4cG9ydCBjbGFzcyBFdmVudFJpc2tTY29yZVJhbmdlU3BlY2lmaWNhdGlvbiBleHRlbmRzIEV2ZW50U3BlY2lmaWNhdGlvbiB7XHJcbiAgY29uc3RydWN0b3IoXHJcbiAgICBwcml2YXRlIHJlYWRvbmx5IG1pblNjb3JlPzogbnVtYmVyLFxyXG4gICAgcHJpdmF0ZSByZWFkb25seSBtYXhTY29yZT86IG51bWJlclxyXG4gICkge1xyXG4gICAgc3VwZXIoKTtcclxuICB9XHJcblxyXG4gIGlzU2F0aXNmaWVkQnkoZXZlbnQ6IEV2ZW50KTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5pc1Jpc2tTY29yZVdpdGhpblJhbmdlKGV2ZW50LCB0aGlzLm1pblNjb3JlLCB0aGlzLm1heFNjb3JlKTtcclxuICB9XHJcblxyXG4gIGdldERlc2NyaXB0aW9uKCk6IHN0cmluZyB7XHJcbiAgICBpZiAodGhpcy5taW5TY29yZSAhPT0gdW5kZWZpbmVkICYmIHRoaXMubWF4U2NvcmUgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICByZXR1cm4gYEV2ZW50IHJpc2sgc2NvcmUgaXMgYmV0d2VlbiAke3RoaXMubWluU2NvcmV9IGFuZCAke3RoaXMubWF4U2NvcmV9YDtcclxuICAgIH0gZWxzZSBpZiAodGhpcy5taW5TY29yZSAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgIHJldHVybiBgRXZlbnQgcmlzayBzY29yZSBpcyBhdCBsZWFzdCAke3RoaXMubWluU2NvcmV9YDtcclxuICAgIH0gZWxzZSBpZiAodGhpcy5tYXhTY29yZSAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgIHJldHVybiBgRXZlbnQgcmlzayBzY29yZSBpcyBhdCBtb3N0ICR7dGhpcy5tYXhTY29yZX1gO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuICdFdmVudCBoYXMgYW55IHJpc2sgc2NvcmUnO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEV2ZW50IEFnZSBSYW5nZSBTcGVjaWZpY2F0aW9uXHJcbiAqIFxyXG4gKiBTcGVjaWZpY2F0aW9uIGZvciBldmVudHMgd2l0aGluIGEgc3BlY2lmaWMgYWdlIHJhbmdlLlxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIEV2ZW50QWdlUmFuZ2VTcGVjaWZpY2F0aW9uIGV4dGVuZHMgRXZlbnRTcGVjaWZpY2F0aW9uIHtcclxuICBjb25zdHJ1Y3RvcihcclxuICAgIHByaXZhdGUgcmVhZG9ubHkgbWluQWdlTXM/OiBudW1iZXIsXHJcbiAgICBwcml2YXRlIHJlYWRvbmx5IG1heEFnZU1zPzogbnVtYmVyXHJcbiAgKSB7XHJcbiAgICBzdXBlcigpO1xyXG4gIH1cclxuXHJcbiAgaXNTYXRpc2ZpZWRCeShldmVudDogRXZlbnQpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmlzQWdlV2l0aGluUmFuZ2UoZXZlbnQsIHRoaXMubWluQWdlTXMsIHRoaXMubWF4QWdlTXMpO1xyXG4gIH1cclxuXHJcbiAgZ2V0RGVzY3JpcHRpb24oKTogc3RyaW5nIHtcclxuICAgIGNvbnN0IGZvcm1hdFRpbWUgPSAobXM6IG51bWJlcikgPT4ge1xyXG4gICAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3IobXMgLyAzNjAwMDAwKTtcclxuICAgICAgY29uc3QgbWludXRlcyA9IE1hdGguZmxvb3IoKG1zICUgMzYwMDAwMCkgLyA2MDAwMCk7XHJcbiAgICAgIHJldHVybiBgJHtob3Vyc31oICR7bWludXRlc31tYDtcclxuICAgIH07XHJcblxyXG4gICAgaWYgKHRoaXMubWluQWdlTXMgIT09IHVuZGVmaW5lZCAmJiB0aGlzLm1heEFnZU1zICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgcmV0dXJuIGBFdmVudCBhZ2UgaXMgYmV0d2VlbiAke2Zvcm1hdFRpbWUodGhpcy5taW5BZ2VNcyl9IGFuZCAke2Zvcm1hdFRpbWUodGhpcy5tYXhBZ2VNcyl9YDtcclxuICAgIH0gZWxzZSBpZiAodGhpcy5taW5BZ2VNcyAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgIHJldHVybiBgRXZlbnQgaXMgYXQgbGVhc3QgJHtmb3JtYXRUaW1lKHRoaXMubWluQWdlTXMpfSBvbGRgO1xyXG4gICAgfSBlbHNlIGlmICh0aGlzLm1heEFnZU1zICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgcmV0dXJuIGBFdmVudCBpcyBhdCBtb3N0ICR7Zm9ybWF0VGltZSh0aGlzLm1heEFnZU1zKX0gb2xkYDtcclxuICAgIH1cclxuICAgIHJldHVybiAnRXZlbnQgaGFzIGFueSBhZ2UnO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEV2ZW50IENvcnJlbGF0aW9uIFNwZWNpZmljYXRpb25cclxuICogXHJcbiAqIFNwZWNpZmljYXRpb24gZm9yIGV2ZW50cyB3aXRoIHNwZWNpZmljIGNvcnJlbGF0aW9uIHByb3BlcnRpZXMuXHJcbiAqL1xyXG5leHBvcnQgY2xhc3MgRXZlbnRDb3JyZWxhdGlvblNwZWNpZmljYXRpb24gZXh0ZW5kcyBFdmVudFNwZWNpZmljYXRpb24ge1xyXG4gIGNvbnN0cnVjdG9yKFxyXG4gICAgcHJpdmF0ZSByZWFkb25seSBjb3JyZWxhdGlvbklkPzogc3RyaW5nLFxyXG4gICAgcHJpdmF0ZSByZWFkb25seSBwYXJlbnRFdmVudElkPzogVW5pcXVlRW50aXR5SWQsXHJcbiAgICBwcml2YXRlIHJlYWRvbmx5IGhhc0NvcnJlbGF0aW9uOiBib29sZWFuID0gdHJ1ZVxyXG4gICkge1xyXG4gICAgc3VwZXIoKTtcclxuICB9XHJcblxyXG4gIGlzU2F0aXNmaWVkQnkoZXZlbnQ6IEV2ZW50KTogYm9vbGVhbiB7XHJcbiAgICBpZiAodGhpcy5jb3JyZWxhdGlvbklkKSB7XHJcbiAgICAgIHJldHVybiBldmVudC5jb3JyZWxhdGlvbklkID09PSB0aGlzLmNvcnJlbGF0aW9uSWQ7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmICh0aGlzLnBhcmVudEV2ZW50SWQpIHtcclxuICAgICAgcmV0dXJuIGV2ZW50LnBhcmVudEV2ZW50SWQ/LmVxdWFscyh0aGlzLnBhcmVudEV2ZW50SWQpIHx8IGZhbHNlO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAodGhpcy5oYXNDb3JyZWxhdGlvbikge1xyXG4gICAgICByZXR1cm4gZXZlbnQuY29ycmVsYXRpb25JZCAhPT0gdW5kZWZpbmVkIHx8IGV2ZW50LnBhcmVudEV2ZW50SWQgIT09IHVuZGVmaW5lZDtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHJldHVybiBldmVudC5jb3JyZWxhdGlvbklkID09PSB1bmRlZmluZWQgJiYgZXZlbnQucGFyZW50RXZlbnRJZCA9PT0gdW5kZWZpbmVkO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgZ2V0RGVzY3JpcHRpb24oKTogc3RyaW5nIHtcclxuICAgIGlmICh0aGlzLmNvcnJlbGF0aW9uSWQpIHtcclxuICAgICAgcmV0dXJuIGBFdmVudCBoYXMgY29ycmVsYXRpb24gSUQ6ICR7dGhpcy5jb3JyZWxhdGlvbklkfWA7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmICh0aGlzLnBhcmVudEV2ZW50SWQpIHtcclxuICAgICAgcmV0dXJuIGBFdmVudCBoYXMgcGFyZW50IElEOiAke3RoaXMucGFyZW50RXZlbnRJZC50b1N0cmluZygpfWA7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiB0aGlzLmhhc0NvcnJlbGF0aW9uIFxyXG4gICAgICA/ICdFdmVudCBoYXMgY29ycmVsYXRpb24gaW5mb3JtYXRpb24nXHJcbiAgICAgIDogJ0V2ZW50IGhhcyBubyBjb3JyZWxhdGlvbiBpbmZvcm1hdGlvbic7XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogRmFpbGVkIFByb2Nlc3NpbmcgRXZlbnQgU3BlY2lmaWNhdGlvblxyXG4gKiBcclxuICogU3BlY2lmaWNhdGlvbiBmb3IgZXZlbnRzIHRoYXQgaGF2ZSBmYWlsZWQgcHJvY2Vzc2luZy5cclxuICovXHJcbmV4cG9ydCBjbGFzcyBGYWlsZWRQcm9jZXNzaW5nRXZlbnRTcGVjaWZpY2F0aW9uIGV4dGVuZHMgRXZlbnRTcGVjaWZpY2F0aW9uIHtcclxuICBpc1NhdGlzZmllZEJ5KGV2ZW50OiBFdmVudCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIGV2ZW50Lmhhc1Byb2Nlc3NpbmdGYWlsZWQoKTtcclxuICB9XHJcblxyXG4gIGdldERlc2NyaXB0aW9uKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gJ0V2ZW50IHByb2Nlc3NpbmcgaGFzIGZhaWxlZCc7XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogTWF4IEF0dGVtcHRzIEV4Y2VlZGVkIFNwZWNpZmljYXRpb25cclxuICogXHJcbiAqIFNwZWNpZmljYXRpb24gZm9yIGV2ZW50cyB0aGF0IGhhdmUgZXhjZWVkZWQgbWF4aW11bSBwcm9jZXNzaW5nIGF0dGVtcHRzLlxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIE1heEF0dGVtcHRzRXhjZWVkZWRTcGVjaWZpY2F0aW9uIGV4dGVuZHMgRXZlbnRTcGVjaWZpY2F0aW9uIHtcclxuICBpc1NhdGlzZmllZEJ5KGV2ZW50OiBFdmVudCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIGV2ZW50Lmhhc0V4Y2VlZGVkTWF4QXR0ZW1wdHMoKTtcclxuICB9XHJcblxyXG4gIGdldERlc2NyaXB0aW9uKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gJ0V2ZW50IGhhcyBleGNlZWRlZCBtYXhpbXVtIHByb2Nlc3NpbmcgYXR0ZW1wdHMnO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIFJlcXVpcmVzIEF0dGVudGlvbiBTcGVjaWZpY2F0aW9uXHJcbiAqIFxyXG4gKiBTcGVjaWZpY2F0aW9uIGZvciBldmVudHMgdGhhdCByZXF1aXJlIGh1bWFuIGF0dGVudGlvbi5cclxuICovXHJcbmV4cG9ydCBjbGFzcyBSZXF1aXJlc0F0dGVudGlvblNwZWNpZmljYXRpb24gZXh0ZW5kcyBFdmVudFNwZWNpZmljYXRpb24ge1xyXG4gIGlzU2F0aXNmaWVkQnkoZXZlbnQ6IEV2ZW50KTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gZXZlbnQuaXNIaWdoU2V2ZXJpdHkoKSB8fCBcclxuICAgICAgICAgICBldmVudC5pc0hpZ2hSaXNrKCkgfHwgXHJcbiAgICAgICAgICAgZXZlbnQuaGFzUHJvY2Vzc2luZ0ZhaWxlZCgpIHx8XHJcbiAgICAgICAgICAgZXZlbnQuaGFzRXhjZWVkZWRNYXhBdHRlbXB0cygpIHx8XHJcbiAgICAgICAgICAgZXZlbnQuc3RhdHVzID09PSBFdmVudFN0YXR1cy5JTlZFU1RJR0FUSU5HO1xyXG4gIH1cclxuXHJcbiAgZ2V0RGVzY3JpcHRpb24oKTogc3RyaW5nIHtcclxuICAgIHJldHVybiAnRXZlbnQgcmVxdWlyZXMgaHVtYW4gYXR0ZW50aW9uIChoaWdoIHNldmVyaXR5LCBoaWdoIHJpc2ssIGZhaWxlZCBwcm9jZXNzaW5nLCBvciB1bmRlciBpbnZlc3RpZ2F0aW9uKSc7XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogU2VjdXJpdHkgQWxlcnQgU3BlY2lmaWNhdGlvblxyXG4gKiBcclxuICogU3BlY2lmaWNhdGlvbiBmb3IgZXZlbnRzIHRoYXQgYXJlIHNlY3VyaXR5IGFsZXJ0cy5cclxuICovXHJcbmV4cG9ydCBjbGFzcyBTZWN1cml0eUFsZXJ0U3BlY2lmaWNhdGlvbiBleHRlbmRzIEV2ZW50U3BlY2lmaWNhdGlvbiB7XHJcbiAgcHJpdmF0ZSBzdGF0aWMgcmVhZG9ubHkgU0VDVVJJVFlfRVZFTlRfVFlQRVMgPSBbXHJcbiAgICBFdmVudFR5cGUuVEhSRUFUX0RFVEVDVEVELFxyXG4gICAgRXZlbnRUeXBlLk1BTFdBUkVfREVURUNURUQsXHJcbiAgICBFdmVudFR5cGUuVlVMTkVSQUJJTElUWV9ERVRFQ1RFRCxcclxuICAgIEV2ZW50VHlwZS5TVVNQSUNJT1VTX0NPTk5FQ1RJT04sXHJcbiAgICBFdmVudFR5cGUuUE9SVF9TQ0FOX0RFVEVDVEVELFxyXG4gICAgRXZlbnRUeXBlLkRET1NfQVRUQUNLLFxyXG4gICAgRXZlbnRUeXBlLkRBVEFfQlJFQUNILFxyXG4gICAgRXZlbnRUeXBlLklPQ19ERVRFQ1RFRCxcclxuICBdO1xyXG5cclxuICBpc1NhdGlzZmllZEJ5KGV2ZW50OiBFdmVudCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMubWF0Y2hlc0FueVR5cGUoZXZlbnQsIFNlY3VyaXR5QWxlcnRTcGVjaWZpY2F0aW9uLlNFQ1VSSVRZX0VWRU5UX1RZUEVTKSB8fFxyXG4gICAgICAgICAgIGV2ZW50LmlzSGlnaFNldmVyaXR5KCkgfHxcclxuICAgICAgICAgICBldmVudC5oYXNUYWcoJ3NlY3VyaXR5LWFsZXJ0Jyk7XHJcbiAgfVxyXG5cclxuICBnZXREZXNjcmlwdGlvbigpOiBzdHJpbmcge1xyXG4gICAgcmV0dXJuICdFdmVudCBpcyBhIHNlY3VyaXR5IGFsZXJ0ICh0aHJlYXQsIG1hbHdhcmUsIHZ1bG5lcmFiaWxpdHksIG9yIGhpZ2ggc2V2ZXJpdHkpJztcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBDb21wb3NpdGUgRXZlbnQgU3BlY2lmaWNhdGlvbiBCdWlsZGVyXHJcbiAqIFxyXG4gKiBCdWlsZGVyIGZvciBjcmVhdGluZyBjb21wbGV4IGV2ZW50IHNwZWNpZmljYXRpb25zIHVzaW5nIGZsdWVudCBpbnRlcmZhY2UuXHJcbiAqL1xyXG5leHBvcnQgY2xhc3MgRXZlbnRTcGVjaWZpY2F0aW9uQnVpbGRlciB7XHJcbiAgcHJpdmF0ZSBzcGVjaWZpY2F0aW9uczogRXZlbnRTcGVjaWZpY2F0aW9uW10gPSBbXTtcclxuXHJcbiAgLyoqXHJcbiAgICogQWRkIGhpZ2ggc2V2ZXJpdHkgZmlsdGVyXHJcbiAgICovXHJcbiAgaGlnaFNldmVyaXR5KCk6IEV2ZW50U3BlY2lmaWNhdGlvbkJ1aWxkZXIge1xyXG4gICAgdGhpcy5zcGVjaWZpY2F0aW9ucy5wdXNoKG5ldyBIaWdoU2V2ZXJpdHlFdmVudFNwZWNpZmljYXRpb24oKSk7XHJcbiAgICByZXR1cm4gdGhpcztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEFkZCBjcml0aWNhbCBzZXZlcml0eSBmaWx0ZXJcclxuICAgKi9cclxuICBjcml0aWNhbCgpOiBFdmVudFNwZWNpZmljYXRpb25CdWlsZGVyIHtcclxuICAgIHRoaXMuc3BlY2lmaWNhdGlvbnMucHVzaChuZXcgQ3JpdGljYWxFdmVudFNwZWNpZmljYXRpb24oKSk7XHJcbiAgICByZXR1cm4gdGhpcztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEFkZCBhY3RpdmUgc3RhdHVzIGZpbHRlclxyXG4gICAqL1xyXG4gIGFjdGl2ZSgpOiBFdmVudFNwZWNpZmljYXRpb25CdWlsZGVyIHtcclxuICAgIHRoaXMuc3BlY2lmaWNhdGlvbnMucHVzaChuZXcgQWN0aXZlRXZlbnRTcGVjaWZpY2F0aW9uKCkpO1xyXG4gICAgcmV0dXJuIHRoaXM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBBZGQgaGlnaCByaXNrIGZpbHRlclxyXG4gICAqL1xyXG4gIGhpZ2hSaXNrKCk6IEV2ZW50U3BlY2lmaWNhdGlvbkJ1aWxkZXIge1xyXG4gICAgdGhpcy5zcGVjaWZpY2F0aW9ucy5wdXNoKG5ldyBIaWdoUmlza0V2ZW50U3BlY2lmaWNhdGlvbigpKTtcclxuICAgIHJldHVybiB0aGlzO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQWRkIHJlY2VudCBldmVudHMgZmlsdGVyXHJcbiAgICovXHJcbiAgcmVjZW50KHdpdGhpbk1zOiBudW1iZXIgPSAzNjAwMDAwKTogRXZlbnRTcGVjaWZpY2F0aW9uQnVpbGRlciB7XHJcbiAgICB0aGlzLnNwZWNpZmljYXRpb25zLnB1c2gobmV3IFJlY2VudEV2ZW50U3BlY2lmaWNhdGlvbih3aXRoaW5NcykpO1xyXG4gICAgcmV0dXJuIHRoaXM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBBZGQgZXZlbnQgdHlwZSBmaWx0ZXJcclxuICAgKi9cclxuICBvZlR5cGVzKC4uLnR5cGVzOiBFdmVudFR5cGVbXSk6IEV2ZW50U3BlY2lmaWNhdGlvbkJ1aWxkZXIge1xyXG4gICAgdGhpcy5zcGVjaWZpY2F0aW9ucy5wdXNoKG5ldyBFdmVudFR5cGVTcGVjaWZpY2F0aW9uKHR5cGVzKSk7XHJcbiAgICByZXR1cm4gdGhpcztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEFkZCBzZXZlcml0eSBmaWx0ZXJcclxuICAgKi9cclxuICB3aXRoU2V2ZXJpdGllcyguLi5zZXZlcml0aWVzOiBFdmVudFNldmVyaXR5W10pOiBFdmVudFNwZWNpZmljYXRpb25CdWlsZGVyIHtcclxuICAgIHRoaXMuc3BlY2lmaWNhdGlvbnMucHVzaChuZXcgRXZlbnRTZXZlcml0eVNwZWNpZmljYXRpb24oc2V2ZXJpdGllcykpO1xyXG4gICAgcmV0dXJuIHRoaXM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBBZGQgc291cmNlIHR5cGUgZmlsdGVyXHJcbiAgICovXHJcbiAgZnJvbVNvdXJjZXMoLi4uc291cmNlVHlwZXM6IEV2ZW50U291cmNlVHlwZVtdKTogRXZlbnRTcGVjaWZpY2F0aW9uQnVpbGRlciB7XHJcbiAgICB0aGlzLnNwZWNpZmljYXRpb25zLnB1c2gobmV3IEV2ZW50U291cmNlVHlwZVNwZWNpZmljYXRpb24oc291cmNlVHlwZXMpKTtcclxuICAgIHJldHVybiB0aGlzO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQWRkIHRhZyBmaWx0ZXJcclxuICAgKi9cclxuICB3aXRoVGFncyh0YWdzOiBzdHJpbmdbXSwgcmVxdWlyZUFsbDogYm9vbGVhbiA9IGZhbHNlKTogRXZlbnRTcGVjaWZpY2F0aW9uQnVpbGRlciB7XHJcbiAgICB0aGlzLnNwZWNpZmljYXRpb25zLnB1c2gobmV3IEV2ZW50VGFnU3BlY2lmaWNhdGlvbih0YWdzLCByZXF1aXJlQWxsKSk7XHJcbiAgICByZXR1cm4gdGhpcztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEFkZCByaXNrIHNjb3JlIHJhbmdlIGZpbHRlclxyXG4gICAqL1xyXG4gIHJpc2tTY29yZVJhbmdlKG1pblNjb3JlPzogbnVtYmVyLCBtYXhTY29yZT86IG51bWJlcik6IEV2ZW50U3BlY2lmaWNhdGlvbkJ1aWxkZXIge1xyXG4gICAgdGhpcy5zcGVjaWZpY2F0aW9ucy5wdXNoKG5ldyBFdmVudFJpc2tTY29yZVJhbmdlU3BlY2lmaWNhdGlvbihtaW5TY29yZSwgbWF4U2NvcmUpKTtcclxuICAgIHJldHVybiB0aGlzO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQWRkIHJlcXVpcmVzIGF0dGVudGlvbiBmaWx0ZXJcclxuICAgKi9cclxuICByZXF1aXJlc0F0dGVudGlvbigpOiBFdmVudFNwZWNpZmljYXRpb25CdWlsZGVyIHtcclxuICAgIHRoaXMuc3BlY2lmaWNhdGlvbnMucHVzaChuZXcgUmVxdWlyZXNBdHRlbnRpb25TcGVjaWZpY2F0aW9uKCkpO1xyXG4gICAgcmV0dXJuIHRoaXM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBBZGQgc2VjdXJpdHkgYWxlcnQgZmlsdGVyXHJcbiAgICovXHJcbiAgc2VjdXJpdHlBbGVydHMoKTogRXZlbnRTcGVjaWZpY2F0aW9uQnVpbGRlciB7XHJcbiAgICB0aGlzLnNwZWNpZmljYXRpb25zLnB1c2gobmV3IFNlY3VyaXR5QWxlcnRTcGVjaWZpY2F0aW9uKCkpO1xyXG4gICAgcmV0dXJuIHRoaXM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBCdWlsZCB0aGUgZmluYWwgc3BlY2lmaWNhdGlvbiB1c2luZyBBTkQgbG9naWNcclxuICAgKi9cclxuICBidWlsZCgpOiBFdmVudFNwZWNpZmljYXRpb24ge1xyXG4gICAgaWYgKHRoaXMuc3BlY2lmaWNhdGlvbnMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignQXQgbGVhc3Qgb25lIHNwZWNpZmljYXRpb24gbXVzdCBiZSBhZGRlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIGlmICh0aGlzLnNwZWNpZmljYXRpb25zLmxlbmd0aCA9PT0gMSkge1xyXG4gICAgICByZXR1cm4gdGhpcy5zcGVjaWZpY2F0aW9uc1swXTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDb21iaW5lIGFsbCBzcGVjaWZpY2F0aW9ucyB3aXRoIEFORCBsb2dpY1xyXG4gICAgbGV0IGNvbWJpbmVkID0gdGhpcy5zcGVjaWZpY2F0aW9uc1swXTtcclxuICAgIGZvciAobGV0IGkgPSAxOyBpIDwgdGhpcy5zcGVjaWZpY2F0aW9ucy5sZW5ndGg7IGkrKykge1xyXG4gICAgICBjb21iaW5lZCA9IGNvbWJpbmVkLmFuZCh0aGlzLnNwZWNpZmljYXRpb25zW2ldKSBhcyBFdmVudFNwZWNpZmljYXRpb247XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIGNvbWJpbmVkO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQnVpbGQgdGhlIGZpbmFsIHNwZWNpZmljYXRpb24gdXNpbmcgT1IgbG9naWNcclxuICAgKi9cclxuICBidWlsZFdpdGhPcigpOiBFdmVudFNwZWNpZmljYXRpb24ge1xyXG4gICAgaWYgKHRoaXMuc3BlY2lmaWNhdGlvbnMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignQXQgbGVhc3Qgb25lIHNwZWNpZmljYXRpb24gbXVzdCBiZSBhZGRlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIGlmICh0aGlzLnNwZWNpZmljYXRpb25zLmxlbmd0aCA9PT0gMSkge1xyXG4gICAgICByZXR1cm4gdGhpcy5zcGVjaWZpY2F0aW9uc1swXTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDb21iaW5lIGFsbCBzcGVjaWZpY2F0aW9ucyB3aXRoIE9SIGxvZ2ljXHJcbiAgICBsZXQgY29tYmluZWQgPSB0aGlzLnNwZWNpZmljYXRpb25zWzBdO1xyXG4gICAgZm9yIChsZXQgaSA9IDE7IGkgPCB0aGlzLnNwZWNpZmljYXRpb25zLmxlbmd0aDsgaSsrKSB7XHJcbiAgICAgIGNvbWJpbmVkID0gY29tYmluZWQub3IodGhpcy5zcGVjaWZpY2F0aW9uc1tpXSkgYXMgRXZlbnRTcGVjaWZpY2F0aW9uO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBjb21iaW5lZDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENyZWF0ZSBhIG5ldyBidWlsZGVyIGluc3RhbmNlXHJcbiAgICovXHJcbiAgc3RhdGljIGNyZWF0ZSgpOiBFdmVudFNwZWNpZmljYXRpb25CdWlsZGVyIHtcclxuICAgIHJldHVybiBuZXcgRXZlbnRTcGVjaWZpY2F0aW9uQnVpbGRlcigpO1xyXG4gIH1cclxufSJdLCJ2ZXJzaW9uIjozfQ==