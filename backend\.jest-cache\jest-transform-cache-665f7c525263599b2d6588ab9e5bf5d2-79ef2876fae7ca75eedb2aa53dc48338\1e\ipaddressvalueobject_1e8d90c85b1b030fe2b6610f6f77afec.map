{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\ip-address.value-object.ts", "mappings": ";;;AAAA,oGAA+F;AAE/F;;GAEG;AACH,IAAY,aAGX;AAHD,WAAY,aAAa;IACvB,8BAAa,CAAA;IACb,8BAAa,CAAA;AACf,CAAC,EAHW,aAAa,6BAAb,aAAa,QAGxB;AAED;;GAEG;AACH,IAAY,cAQX;AARD,WAAY,cAAc;IACxB,mCAAiB,CAAA;IACjB,qCAAmB,CAAA;IACnB,uCAAqB,CAAA;IACrB,yCAAuB,CAAA;IACvB,yCAAuB,CAAA;IACvB,2CAAyB,CAAA;IACzB,uCAAqB,CAAA;AACvB,CAAC,EARW,cAAc,8BAAd,cAAc,QAQzB;AAED;;;;;;;;;;;;GAYG;AACH,MAAa,SAAU,SAAQ,mCAAuB;IASpD,YAAY,KAAa;QACvB,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IAES,oBAAoB;QAC3B,IAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAE3C,IAAK,IAAY,CAAC,KAAK,KAAK,aAAa,CAAC,IAAI,EAAE,CAAC;YAC9C,IAAY,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACjD,CAAC;aAAM,CAAC;YACL,IAAY,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACrD,CAAC;IACH,CAAC;IAES,QAAQ;QAChB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAEnC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,8BAA8B,OAAO,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEO,aAAa;QACnB,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,OAAO,aAAa,CAAC,IAAI,CAAC;QAC5B,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACzC,OAAO,aAAa,CAAC,IAAI,CAAC;QAC5B,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;IACzD,CAAC;IAEO,WAAW,CAAC,OAAe;QACjC,OAAO,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC;IAEO,WAAW,CAAC,OAAe;QACjC,6BAA6B;QAC7B,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,8CAA8C;QAC9C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,6BAA6B;QAC7B,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,mCAAmC;QACnC,IAAI,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,iDAAiD;QACjD,MAAM,WAAW,GAAG,0LAA0L,CAAC;QAE/M,OAAO,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAEO,eAAe;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAEO,iBAAiB;QACvB,mCAAmC;QACnC,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACtD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACvD,MAAM,YAAY,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YAC9D,MAAM,WAAW,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrD,QAAQ,GAAG,CAAC,GAAG,SAAS,EAAE,GAAG,WAAW,EAAE,GAAG,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrE,CAAC;QACD,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,OAAe;QAC/B,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,MAAc,EAAE,MAAc,EAAE,MAAc,EAAE,MAAc;QAClF,MAAM,OAAO,GAAG,GAAG,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,EAAE,CAAC;QAC1D,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS;QACd,OAAO,IAAI,SAAS,CAAC,WAAW,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa;QAClB,OAAO,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC,IAAI,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC,IAAI,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,SAAS;QACP,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QACD,OAAO,CAAC,GAAG,IAAI,CAAC,OAAQ,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,CAAC,GAAG,IAAI,CAAC,SAAU,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,YAAY;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAQ,CAAC;QAC7B,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC;QAE9C,yBAAyB;QACzB,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAClB,OAAO,cAAc,CAAC,QAAQ,CAAC;QACjC,CAAC;QAED,0BAA0B;QAC1B,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC;YACjC,OAAO,cAAc,CAAC,SAAS,CAAC;QAClC,CAAC;QAED,YAAY;QACZ,IAAI,KAAK,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;YACvE,OAAO,cAAc,CAAC,SAAS,CAAC;QAClC,CAAC;QAED,8BAA8B;QAC9B,IAAI,KAAK,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;YACpC,OAAO,cAAc,CAAC,UAAU,CAAC;QACnC,CAAC;QAED,iBAAiB;QACjB,IACE,KAAK,KAAK,EAAE,IAAI,aAAa;YAC7B,CAAC,KAAK,KAAK,GAAG,IAAI,MAAM,IAAI,EAAE,IAAI,MAAM,IAAI,EAAE,CAAC,IAAI,gBAAgB;YACnE,CAAC,KAAK,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,iBAAiB;UACnD,CAAC;YACD,OAAO,cAAc,CAAC,OAAO,CAAC;QAChC,CAAC;QAED,kBAAkB;QAClB,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC;YAChC,OAAO,cAAc,CAAC,QAAQ,CAAC;QACjC,CAAC;QAED,OAAO,cAAc,CAAC,MAAM,CAAC;IAC/B,CAAC;IAEO,YAAY;QAClB,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAE1C,iBAAiB;QACjB,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;YACtB,OAAO,cAAc,CAAC,QAAQ,CAAC;QACjC,CAAC;QAED,yBAAyB;QACzB,IAAI,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC;YACtD,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3D,OAAO,cAAc,CAAC,UAAU,CAAC;QACnC,CAAC;QAED,uBAAuB;QACvB,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7B,OAAO,cAAc,CAAC,SAAS,CAAC;QAClC,CAAC;QAED,kCAAkC;QAClC,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACzD,OAAO,cAAc,CAAC,OAAO,CAAC;QAChC,CAAC;QAED,+CAA+C;QAC/C,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/D,OAAO,cAAc,CAAC,QAAQ,CAAC;QACjC,CAAC;QAED,kCAAkC;QAClC,IAAI,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YACtE,OAAO,cAAc,CAAC,QAAQ,CAAC;QACjC,CAAC;QAED,OAAO,cAAc,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,QAAQ,EAAE,KAAK,cAAc,CAAC,OAAO,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,QAAQ,EAAE,KAAK,cAAc,CAAC,MAAM,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,QAAQ,EAAE,KAAK,cAAc,CAAC,QAAQ,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,QAAQ,EAAE,KAAK,cAAc,CAAC,SAAS,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,cAAsB,EAAE,YAAoB;QACtD,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,cAAsB,EAAE,YAAoB;QAClE,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,cAAc,CAAC,CAAC;QAChD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACrC,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;QAC7C,MAAM,IAAI,GAAG,CAAC,UAAU,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC;QAEvD,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC;IAClD,CAAC;IAEO,eAAe,CAAC,cAAsB,EAAE,YAAoB;QAClE,mCAAmC;QACnC,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,cAAc,CAAC,CAAC;QAChD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACxC,MAAM,eAAe,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;QAEhD,MAAM,WAAW,GAAG,YAAY,CAAC;QACjC,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,WAAW,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;YACxD,MAAM,cAAc,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACrD,MAAM,iBAAiB,GAAG,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAE3D,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,WAAW,GAAG,WAAW,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE,GAAG,aAAa,CAAC,CAAC,GAAG,MAAM,CAAC;YAEvD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,EAAE,CAAC;gBAC3D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,WAAW,IAAI,EAAE,CAAC;QACpB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAQ,CAAC;QAC7B,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAQ,CAAC;YAC7B,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC;QAC5E,CAAC;aAAM,CAAC;YACN,8DAA8D;YAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;YACpD,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,YAAoB;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClB,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACrC,MAAM,IAAI,GAAG,CAAC,UAAU,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,UAAU,GAAG,OAAO,GAAG,IAAI,CAAC;YAElC,MAAM,MAAM,GAAG,CAAC,UAAU,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;YAC1C,MAAM,MAAM,GAAG,CAAC,UAAU,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;YAC1C,MAAM,MAAM,GAAG,CAAC,UAAU,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC;YACzC,MAAM,MAAM,GAAG,UAAU,GAAG,IAAI,CAAC;YAEjC,OAAO,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAClE,CAAC;aAAM,CAAC;YACN,8CAA8C;YAC9C,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY;QACV,+CAA+C;QAC/C,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YAC1C,OAAO,KAAK,CAAC,CAAC,kDAAkD;QAClE,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAQ,CAAC;YAE7B,uCAAuC;YACvC,mEAAmE;YACnE,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,oBAAoB;YACpB,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9F,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,sCAAsC;QACtC,8CAA8C;QAC9C,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClB,MAAM,UAAU,GAAG,IAAI,CAAC,OAAQ,CAAC,CAAC,CAAC,CAAC;YAEpC,iDAAiD;YACjD,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,IAAI,GAAG;gBAAE,OAAO,eAAe,CAAC;YACjE,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,IAAI,GAAG;gBAAE,OAAO,aAAa,CAAC;YACjE,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,IAAI,GAAG;gBAAE,OAAO,cAAc,CAAC;QACpE,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAiB;QAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,0CAA0C;QAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;IAClE,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,MAAM;YACpB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,cAAc,EAAE,IAAI,CAAC,QAAQ,EAAE;YAC/B,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;YAC/B,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;YACjC,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;YAChC,UAAU,EAAE,IAAI,CAAC,mBAAmB,EAAE;SACvC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,OAAe;QAC5B,IAAI,CAAC;YACH,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;;AAxeH,8BAyeC;AAxeyB,oBAAU,GAAG,6FAA6F,CAAC;AAC3G,oBAAU,GAAG,4CAA4C,CAAC;AAC1D,+BAAqB,GAAG,4EAA4E,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\ip-address.value-object.ts"], "sourcesContent": ["import { BaseValueObject } from '../../../../../shared-kernel/value-objects/base-value-object';\r\n\r\n/**\r\n * IP Address Type\r\n */\r\nexport enum IPAddressType {\r\n  IPv4 = 'ipv4',\r\n  IPv6 = 'ipv6',\r\n}\r\n\r\n/**\r\n * IP Address Classification\r\n */\r\nexport enum IPAddressClass {\r\n  PUBLIC = 'public',\r\n  PRIVATE = 'private',\r\n  LOOPBACK = 'loopback',\r\n  MULTICAST = 'multicast',\r\n  BROADCAST = 'broadcast',\r\n  LINK_LOCAL = 'link_local',\r\n  RESERVED = 'reserved',\r\n}\r\n\r\n/**\r\n * IP Address Value Object\r\n * \r\n * Represents an IP address (IPv4 or IPv6) with validation and utility methods.\r\n * Provides classification, network analysis, and security-related functionality.\r\n * \r\n * Key features:\r\n * - IPv4 and IPv6 support\r\n * - Address classification (public, private, etc.)\r\n * - Network range checking\r\n * - Geolocation support preparation\r\n * - Security analysis utilities\r\n */\r\nexport class IPAddress extends BaseValueObject<string> {\r\n  private static readonly IPv4_REGEX = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\r\n  private static readonly IPv6_REGEX = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;\r\n  private static readonly IPv6_COMPRESSED_REGEX = /^(([0-9a-fA-F]{1,4}:)*)?::([0-9a-fA-F]{1,4}:)*[0-9a-fA-F]{1,4}$|^::$|^::1$/;\r\n\r\n  private readonly _type: IPAddressType;\r\n  private readonly _octets?: number[]; // For IPv4\r\n  private readonly _segments?: string[]; // For IPv6\r\n\r\n  constructor(value: string) {\r\n    super(value);\r\n  }\r\n\r\n  protected initializeProperties(): void {\r\n    (this as any)._type = this.determineType();\r\n    \r\n    if ((this as any)._type === IPAddressType.IPv4) {\r\n      (this as any)._octets = this.parseIPv4Octets();\r\n    } else {\r\n      (this as any)._segments = this.parseIPv6Segments();\r\n    }\r\n  }\r\n\r\n  protected validate(): void {\r\n    if (!this._value || this._value.trim().length === 0) {\r\n      throw new Error('IP address cannot be empty');\r\n    }\r\n\r\n    const trimmed = this._value.trim();\r\n    \r\n    if (!this.isValidIPv4(trimmed) && !this.isValidIPv6(trimmed)) {\r\n      throw new Error(`Invalid IP address format: ${trimmed}`);\r\n    }\r\n  }\r\n\r\n  private determineType(): IPAddressType {\r\n    if (this.isValidIPv4(this._value)) {\r\n      return IPAddressType.IPv4;\r\n    } else if (this.isValidIPv6(this._value)) {\r\n      return IPAddressType.IPv6;\r\n    }\r\n    throw new Error('Unable to determine IP address type');\r\n  }\r\n\r\n  private isValidIPv4(address: string): boolean {\r\n    return IPAddress.IPv4_REGEX.test(address);\r\n  }\r\n\r\n  private isValidIPv6(address: string): boolean {\r\n    // Handle special cases first\r\n    if (address === '::' || address === '::1') {\r\n      return true;\r\n    }\r\n    \r\n    // Reject addresses with multiple :: sequences\r\n    if ((address.match(/::/g) || []).length > 1) {\r\n      return false;\r\n    }\r\n    \r\n    // Check for full IPv6 format\r\n    if (IPAddress.IPv6_REGEX.test(address)) {\r\n      return true;\r\n    }\r\n    \r\n    // Check for compressed IPv6 format\r\n    if (IPAddress.IPv6_COMPRESSED_REGEX.test(address)) {\r\n      return true;\r\n    }\r\n    \r\n    // Additional validation for common IPv6 patterns\r\n    const ipv6Pattern = /^([0-9a-fA-F]{0,4}:){1,7}[0-9a-fA-F]{0,4}$|^::([0-9a-fA-F]{0,4}:){0,6}[0-9a-fA-F]{0,4}$|^([0-9a-fA-F]{0,4}:){1,6}::$|^([0-9a-fA-F]{0,4}:){1,6}:([0-9a-fA-F]{0,4}:){0,5}[0-9a-fA-F]{0,4}$/;\r\n    \r\n    return ipv6Pattern.test(address);\r\n  }\r\n\r\n  private parseIPv4Octets(): number[] {\r\n    return this._value.split('.').map(octet => parseInt(octet, 10));\r\n  }\r\n\r\n  private parseIPv6Segments(): string[] {\r\n    // Expand compressed IPv6 addresses\r\n    let expanded = this._value;\r\n    if (expanded.includes('::')) {\r\n      const parts = expanded.split('::');\r\n      const leftParts = parts[0] ? parts[0].split(':') : [];\r\n      const rightParts = parts[1] ? parts[1].split(':') : [];\r\n      const missingParts = 8 - leftParts.length - rightParts.length;\r\n      const middleParts = Array(missingParts).fill('0000');\r\n      expanded = [...leftParts, ...middleParts, ...rightParts].join(':');\r\n    }\r\n    return expanded.split(':').map(segment => segment.padStart(4, '0'));\r\n  }\r\n\r\n  /**\r\n   * Create an IP address from string\r\n   */\r\n  static fromString(address: string): IPAddress {\r\n    return new IPAddress(address);\r\n  }\r\n\r\n  /**\r\n   * Create an IPv4 address from octets\r\n   */\r\n  static fromIPv4Octets(octet1: number, octet2: number, octet3: number, octet4: number): IPAddress {\r\n    const address = `${octet1}.${octet2}.${octet3}.${octet4}`;\r\n    return new IPAddress(address);\r\n  }\r\n\r\n  /**\r\n   * Create localhost IPv4 address\r\n   */\r\n  static localhost(): IPAddress {\r\n    return new IPAddress('127.0.0.1');\r\n  }\r\n\r\n  /**\r\n   * Create localhost IPv6 address\r\n   */\r\n  static localhostIPv6(): IPAddress {\r\n    return new IPAddress('::1');\r\n  }\r\n\r\n  /**\r\n   * Get IP address type\r\n   */\r\n  get type(): IPAddressType {\r\n    return this._type;\r\n  }\r\n\r\n  /**\r\n   * Check if this is an IPv4 address\r\n   */\r\n  isIPv4(): boolean {\r\n    return this._type === IPAddressType.IPv4;\r\n  }\r\n\r\n  /**\r\n   * Check if this is an IPv6 address\r\n   */\r\n  isIPv6(): boolean {\r\n    return this._type === IPAddressType.IPv6;\r\n  }\r\n\r\n  /**\r\n   * Get IPv4 octets (only for IPv4 addresses)\r\n   */\r\n  getOctets(): number[] {\r\n    if (!this.isIPv4()) {\r\n      throw new Error('Cannot get octets for non-IPv4 address');\r\n    }\r\n    return [...this._octets!];\r\n  }\r\n\r\n  /**\r\n   * Get IPv6 segments (only for IPv6 addresses)\r\n   */\r\n  getSegments(): string[] {\r\n    if (!this.isIPv6()) {\r\n      throw new Error('Cannot get segments for non-IPv6 address');\r\n    }\r\n    return [...this._segments!];\r\n  }\r\n\r\n  /**\r\n   * Classify the IP address\r\n   */\r\n  classify(): IPAddressClass {\r\n    if (this.isIPv4()) {\r\n      return this.classifyIPv4();\r\n    } else {\r\n      return this.classifyIPv6();\r\n    }\r\n  }\r\n\r\n  private classifyIPv4(): IPAddressClass {\r\n    const octets = this._octets!;\r\n    const [first, second, third, fourth] = octets;\r\n\r\n    // Loopback (*********/8)\r\n    if (first === 127) {\r\n      return IPAddressClass.LOOPBACK;\r\n    }\r\n\r\n    // Multicast (*********/4)\r\n    if (first >= 224 && first <= 239) {\r\n      return IPAddressClass.MULTICAST;\r\n    }\r\n\r\n    // Broadcast\r\n    if (first === 255 && second === 255 && third === 255 && fourth === 255) {\r\n      return IPAddressClass.BROADCAST;\r\n    }\r\n\r\n    // Link-local (***********/16)\r\n    if (first === 169 && second === 254) {\r\n      return IPAddressClass.LINK_LOCAL;\r\n    }\r\n\r\n    // Private ranges\r\n    if (\r\n      first === 10 || // 10.0.0.0/8\r\n      (first === 172 && second >= 16 && second <= 31) || // **********/12\r\n      (first === 192 && second === 168) // ***********/16\r\n    ) {\r\n      return IPAddressClass.PRIVATE;\r\n    }\r\n\r\n    // Reserved ranges\r\n    if (first === 0 || first >= 240) {\r\n      return IPAddressClass.RESERVED;\r\n    }\r\n\r\n    return IPAddressClass.PUBLIC;\r\n  }\r\n\r\n  private classifyIPv6(): IPAddressClass {\r\n    const address = this._value.toLowerCase();\r\n\r\n    // Loopback (::1)\r\n    if (address === '::1') {\r\n      return IPAddressClass.LOOPBACK;\r\n    }\r\n\r\n    // Link-local (fe80::/10)\r\n    if (address.startsWith('fe8') || address.startsWith('fe9') || \r\n        address.startsWith('fea') || address.startsWith('feb')) {\r\n      return IPAddressClass.LINK_LOCAL;\r\n    }\r\n\r\n    // Multicast (ff00::/8)\r\n    if (address.startsWith('ff')) {\r\n      return IPAddressClass.MULTICAST;\r\n    }\r\n\r\n    // Private/Unique Local (fc00::/7)\r\n    if (address.startsWith('fc') || address.startsWith('fd')) {\r\n      return IPAddressClass.PRIVATE;\r\n    }\r\n\r\n    // Reserved (including documentation addresses)\r\n    if (address.startsWith('::') || address.startsWith('2001:db8')) {\r\n      return IPAddressClass.RESERVED;\r\n    }\r\n\r\n    // Check for other reserved ranges\r\n    if (address.startsWith('2001:0db8') || address.startsWith('2001:db8')) {\r\n      return IPAddressClass.RESERVED;\r\n    }\r\n\r\n    return IPAddressClass.PUBLIC;\r\n  }\r\n\r\n  /**\r\n   * Check if address is private\r\n   */\r\n  isPrivate(): boolean {\r\n    return this.classify() === IPAddressClass.PRIVATE;\r\n  }\r\n\r\n  /**\r\n   * Check if address is public\r\n   */\r\n  isPublic(): boolean {\r\n    return this.classify() === IPAddressClass.PUBLIC;\r\n  }\r\n\r\n  /**\r\n   * Check if address is loopback\r\n   */\r\n  isLoopback(): boolean {\r\n    return this.classify() === IPAddressClass.LOOPBACK;\r\n  }\r\n\r\n  /**\r\n   * Check if address is multicast\r\n   */\r\n  isMulticast(): boolean {\r\n    return this.classify() === IPAddressClass.MULTICAST;\r\n  }\r\n\r\n  /**\r\n   * Check if address is in a specific network range\r\n   */\r\n  isInNetwork(networkAddress: string, prefixLength: number): boolean {\r\n    if (this.isIPv4()) {\r\n      return this.isInIPv4Network(networkAddress, prefixLength);\r\n    } else {\r\n      return this.isInIPv6Network(networkAddress, prefixLength);\r\n    }\r\n  }\r\n\r\n  private isInIPv4Network(networkAddress: string, prefixLength: number): boolean {\r\n    const networkIP = new IPAddress(networkAddress);\r\n    if (!networkIP.isIPv4()) {\r\n      return false;\r\n    }\r\n\r\n    const thisInt = this.toIPv4Integer();\r\n    const networkInt = networkIP.toIPv4Integer();\r\n    const mask = (0xFFFFFFFF << (32 - prefixLength)) >>> 0;\r\n\r\n    return (thisInt & mask) === (networkInt & mask);\r\n  }\r\n\r\n  private isInIPv6Network(networkAddress: string, prefixLength: number): boolean {\r\n    // Simplified IPv6 network checking\r\n    const networkIP = new IPAddress(networkAddress);\r\n    if (!networkIP.isIPv6()) {\r\n      return false;\r\n    }\r\n\r\n    const thisSegments = this.getSegments();\r\n    const networkSegments = networkIP.getSegments();\r\n    \r\n    const bitsToCheck = prefixLength;\r\n    let checkedBits = 0;\r\n\r\n    for (let i = 0; i < 8 && checkedBits < bitsToCheck; i++) {\r\n      const thisSegmentInt = parseInt(thisSegments[i], 16);\r\n      const networkSegmentInt = parseInt(networkSegments[i], 16);\r\n      \r\n      const remainingBits = Math.min(16, bitsToCheck - checkedBits);\r\n      const mask = (0xFFFF << (16 - remainingBits)) & 0xFFFF;\r\n      \r\n      if ((thisSegmentInt & mask) !== (networkSegmentInt & mask)) {\r\n        return false;\r\n      }\r\n      \r\n      checkedBits += 16;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Convert IPv4 to 32-bit integer\r\n   */\r\n  toIPv4Integer(): number {\r\n    if (!this.isIPv4()) {\r\n      throw new Error('Cannot convert non-IPv4 address to integer');\r\n    }\r\n    const octets = this._octets!;\r\n    return (octets[0] << 24) + (octets[1] << 16) + (octets[2] << 8) + octets[3];\r\n  }\r\n\r\n  /**\r\n   * Get reverse DNS lookup format\r\n   */\r\n  getReverseDNSFormat(): string {\r\n    if (this.isIPv4()) {\r\n      const octets = this._octets!;\r\n      return `${octets[3]}.${octets[2]}.${octets[1]}.${octets[0]}.in-addr.arpa`;\r\n    } else {\r\n      // IPv6 reverse DNS is more complex, simplified implementation\r\n      const segments = this.getSegments();\r\n      const chars = segments.join('').split('').reverse();\r\n      return chars.join('.') + '.ip6.arpa';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get network address for given prefix length\r\n   */\r\n  getNetworkAddress(prefixLength: number): IPAddress {\r\n    if (this.isIPv4()) {\r\n      const thisInt = this.toIPv4Integer();\r\n      const mask = (0xFFFFFFFF << (32 - prefixLength)) >>> 0;\r\n      const networkInt = thisInt & mask;\r\n      \r\n      const octet1 = (networkInt >>> 24) & 0xFF;\r\n      const octet2 = (networkInt >>> 16) & 0xFF;\r\n      const octet3 = (networkInt >>> 8) & 0xFF;\r\n      const octet4 = networkInt & 0xFF;\r\n      \r\n      return IPAddress.fromIPv4Octets(octet1, octet2, octet3, octet4);\r\n    } else {\r\n      // Simplified IPv6 network address calculation\r\n      throw new Error('IPv6 network address calculation not implemented');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if this IP address could be suspicious\r\n   */\r\n  isSuspicious(): boolean {\r\n    // Basic heuristics for suspicious IP addresses\r\n    if (this.isPrivate() || this.isLoopback()) {\r\n      return false; // Internal addresses are generally not suspicious\r\n    }\r\n\r\n    if (this.isIPv4()) {\r\n      const octets = this._octets!;\r\n      \r\n      // Check for common suspicious patterns\r\n      // All octets the same (but exclude common public DNS like *******)\r\n      if (octets.every(octet => octet === octets[0]) && octets[0] !== 8) {\r\n        return true;\r\n      }\r\n      \r\n      // Sequential octets\r\n      if (octets[1] === octets[0] + 1 && octets[2] === octets[1] + 1 && octets[3] === octets[2] + 1) {\r\n        return true;\r\n      }\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Get geographic region hint based on IP ranges\r\n   */\r\n  getRegionHint(): string | null {\r\n    if (!this.isPublic()) {\r\n      return null;\r\n    }\r\n\r\n    // This is a simplified implementation\r\n    // In practice, you would use a GeoIP database\r\n    if (this.isIPv4()) {\r\n      const firstOctet = this._octets![0];\r\n      \r\n      // Very basic regional hints based on first octet\r\n      if (firstOctet >= 1 && firstOctet <= 126) return 'North America';\r\n      if (firstOctet >= 128 && firstOctet <= 191) return 'Europe/Asia';\r\n      if (firstOctet >= 192 && firstOctet <= 223) return 'Asia Pacific';\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Compare IP addresses for equality\r\n   */\r\n  public equals(other?: IPAddress): boolean {\r\n    if (!other) {\r\n      return false;\r\n    }\r\n\r\n    if (this === other) {\r\n      return true;\r\n    }\r\n\r\n    // Normalize both addresses for comparison\r\n    return this._value.toLowerCase() === other._value.toLowerCase();\r\n  }\r\n\r\n  /**\r\n   * Get string representation\r\n   */\r\n  public toString(): string {\r\n    return this._value;\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      address: this._value,\r\n      type: this._type,\r\n      classification: this.classify(),\r\n      isPrivate: this.isPrivate(),\r\n      isPublic: this.isPublic(),\r\n      isLoopback: this.isLoopback(),\r\n      isMulticast: this.isMulticast(),\r\n      isSuspicious: this.isSuspicious(),\r\n      regionHint: this.getRegionHint(),\r\n      reverseDNS: this.getReverseDNSFormat(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create IPAddress from JSON\r\n   */\r\n  static fromJSON(json: Record<string, any>): IPAddress {\r\n    return new IPAddress(json.address);\r\n  }\r\n\r\n  /**\r\n   * Validate IP address format without creating instance\r\n   */\r\n  static isValid(address: string): boolean {\r\n    try {\r\n      new IPAddress(address);\r\n      return true;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}