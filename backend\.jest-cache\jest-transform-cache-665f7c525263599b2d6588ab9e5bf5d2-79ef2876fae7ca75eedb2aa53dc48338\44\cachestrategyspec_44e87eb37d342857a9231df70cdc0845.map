{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\cache-strategy.spec.ts", "mappings": ";;AAAA,kEAIuC;AAEvC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,IAAI,KAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,IAAI,sCAAqB,CAAC;gBAChC,UAAU,EAAE,KAAK,EAAE,WAAW;gBAC9B,OAAO,EAAE,GAAG;gBACZ,eAAe,EAAE,IAAI;aACtB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,SAAS,CAAC,GAAG,EAAE;YACb,KAAK,CAAC,OAAO,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAChC,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;gBAChD,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAClC,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACvC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;gBACxD,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBAC/C,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;gBAC1C,MAAM,KAAK,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;gBAEzC,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnD,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;gBAClC,MAAM,KAAK,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;gBAC1C,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEpD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;gBACpD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3B,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;gBAClE,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;gBACnD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;gBACxC,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAClC,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAClC,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAElC,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC;gBAEpB,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5C,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5C,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;gBACrD,MAAM,KAAK,CAAC,GAAG,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;gBAC9C,MAAM,KAAK,CAAC,GAAG,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;gBAC9C,MAAM,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;gBAChD,MAAM,KAAK,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;gBAEzC,MAAM,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAE9B,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtD,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvD,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrD,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;gBACzC,MAAM,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW;gBAExD,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAErD,sBAAsB;gBACtB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;gBAEtD,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;gBACzD,MAAM,aAAa,GAAG,IAAI,sCAAqB,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;gBAEpE,MAAM,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;gBAChD,MAAM,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAE7D,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;gBACtD,MAAM,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAE1D,aAAa,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;gBAC7D,MAAM,KAAK,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;gBAE7C,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEnD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;gBAEtD,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;gBAC7D,MAAM,iBAAiB,GAAG,IAAI,sCAAqB,CAAC;oBAClD,UAAU,EAAE,EAAE;oBACd,eAAe,EAAE,EAAE;iBACpB,CAAC,CAAC;gBAEH,MAAM,iBAAiB,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;gBAErD,kCAAkC;gBAClC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;gBAEtD,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,UAAU,EAAE,CAAC;gBACrD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAE7B,iBAAiB,CAAC,OAAO,EAAE,CAAC;YAC9B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;YACxC,EAAE,CAAC,kEAAkE,EAAE,KAAK,IAAI,EAAE;gBAChF,MAAM,UAAU,GAAG,IAAI,sCAAqB,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;gBAE7D,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACvC,6CAA6C;gBAC7C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrD,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAEvC,4BAA4B;gBAC5B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrD,4CAA4C;gBAC5C,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAE7B,oCAAoC;gBACpC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrD,sDAAsD;gBACtD,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAEvC,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChD,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjD,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEhD,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;gBAClE,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC1B,MAAM,UAAU,GAAG,IAAI,sCAAqB,CAAC;oBAC3C,OAAO,EAAE,CAAC;oBACV,OAAO;iBACR,CAAC,CAAC;gBAEH,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACvC,6CAA6C;gBAC7C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrD,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,oBAAoB;gBAE5D,MAAM,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBACnE,KAAK,EAAE,QAAQ;iBAChB,CAAC,CAAC,CAAC;gBAEJ,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;gBAC3D,MAAM,UAAU,GAAG,IAAI,sCAAqB,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;gBAE7D,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACvC,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAEvC,oDAAoD;gBACpD,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;gBAE/C,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChD,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChD,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAE5D,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;gBAClD,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAElC,MAAM;gBACN,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAExB,OAAO;gBACP,MAAM,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBAEhC,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC;gBACzC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC/B,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;gBAC7C,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAClC,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAClC,MAAM,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAE3B,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC;gBACzC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAChC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;gBACtC,MAAM,UAAU,GAAG,IAAI,sCAAqB,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;gBAE7D,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACvC,6CAA6C;gBAC7C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrD,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,oBAAoB;gBAE5D,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC9C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAElC,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;gBACnD,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAElC,gCAAgC;gBAChC,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACxB,MAAM,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACxB,MAAM,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBAEhC,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC;gBACzC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,GAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;YAC/B,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;gBACjE,MAAM,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;gBAExC,gCAAgC;gBAChC,MAAM,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAC/B,MAAM,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAC/B,MAAM,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAE/B,6DAA6D;gBAC7D,4DAA4D;gBAC5D,MAAM,UAAU,GAAG,IAAI,sCAAqB,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;gBAE7D,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACvC,6CAA6C;gBAC7C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrD,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAEvC,4BAA4B;gBAC5B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrD,6BAA6B;gBAC7B,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAC7B,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAE7B,oCAAoC;gBACpC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrD,oDAAoD;gBACpD,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAEvC,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChD,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjD,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEhD,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;gBACxD,MAAM,KAAK,CAAC,GAAG,CAAC,WAAW,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;gBAC3C,MAAM,KAAK,CAAC,GAAG,CAAC,WAAW,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;gBAC3C,MAAM,KAAK,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAE/C,kCAAkC;gBAClC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;gBAEtD,iBAAiB;gBACjB,KAAK,CAAC,OAAO,EAAE,CAAC;gBAEhB,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjD,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjD,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;gBAClD,MAAM,QAAQ,GAAG;oBACf,MAAM,EAAE,aAAa;oBACrB,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE;oBAC3B,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBAChB,IAAI,EAAE,IAAI;oBACV,SAAS,EAAE,SAAS;iBACrB,CAAC;gBAEF,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACpD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;oBAC5B,MAAM,SAAS,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBACvC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,IAAI,OAA8B,CAAC;QACnC,IAAI,OAA8B,CAAC;QACnC,IAAI,UAAmC,CAAC;QAExC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,GAAG,IAAI,sCAAqB,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;YAC3D,OAAO,GAAG,IAAI,sCAAqB,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;YAC3D,UAAU,GAAG,IAAI,wCAAuB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,SAAS,CAAC,GAAG,EAAE;YACb,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAExC,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAExC,gCAAgC;YAChC,MAAM,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAE/C,6BAA6B;YAC7B,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEhC,sBAAsB;YACtB,MAAM,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;YACzC,MAAM,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YAEjD,MAAM,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YACzC,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YAEzC,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3B,MAAM,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAC1C,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAE1C,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC;YAEzB,MAAM,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAEtC,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACvC,MAAM,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY;YAC1C,MAAM,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe;YAErD,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC;YAEjD,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE1D,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,mBAAoB,SAAQ,8BAAa;gBAA/C;;oBACU,UAAK,GAAG,IAAI,GAAG,EAAe,CAAC;gBA0CzC,CAAC;gBAxCC,KAAK,CAAC,GAAG,CAAI,GAAW;oBACtB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;gBACrC,CAAC;gBAED,KAAK,CAAC,GAAG,CAAI,GAAW,EAAE,KAAQ,EAAE,GAAY;oBAC9C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC7B,CAAC;gBAED,KAAK,CAAC,MAAM,CAAC,GAAW;oBACtB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAChC,CAAC;gBAED,KAAK,CAAC,KAAK,CAAC,OAAgB;oBAC1B,IAAI,OAAO,EAAE,CAAC;wBACZ,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;wBACvD,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;4BACpC,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gCACpB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;4BACzB,CAAC;wBACH,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;oBACrB,CAAC;gBACH,CAAC;gBAED,KAAK,CAAC,GAAG,CAAC,GAAW;oBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC7B,CAAC;gBAED,KAAK,CAAC,UAAU;oBACd,OAAO;wBACL,IAAI,EAAE,CAAC;wBACP,MAAM,EAAE,CAAC;wBACT,IAAI,EAAE,CAAC;wBACP,OAAO,EAAE,CAAC;wBACV,SAAS,EAAE,CAAC;wBACZ,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;wBACrB,OAAO,EAAE,CAAC;qBACX,CAAC;gBACJ,CAAC;aACF;YAED,MAAM,WAAW,GAAG,IAAI,mBAAmB,EAAE,CAAC;YAE9C,MAAM,WAAW,CAAC,GAAG,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,SAAS,GAAG,IAAI,sCAAqB,EAAE,CAAC;YAC9C,MAAM,QAAQ,GAAG,EAAE,CAAC;YAEpB,kBAAkB;YAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5B,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;YAChE,CAAC;YAED,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE5B,6BAA6B;YAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5B,MAAM,CAAC,MAAM,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACpE,CAAC;YAED,SAAS,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,SAAS,GAAG,IAAI,sCAAqB,EAAE,CAAC;YAC9C,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAErC,MAAM,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAC7C,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAEnD,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEnC,SAAS,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,SAAS,GAAG,IAAI,sCAAqB,EAAE,CAAC;YAC9C,MAAM,WAAW,GAAG;gBAClB,iBAAiB;gBACjB,iBAAiB;gBACjB,kBAAkB;gBAClB,eAAe;gBACf,iBAAiB;gBACjB,sBAAsB;gBACtB,kBAAkB;aACnB,CAAC;YAEF,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;gBAC9B,MAAM,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,GAAG,EAAE,CAAC,CAAC;gBAC7C,MAAM,CAAC,MAAM,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,CAAC;YAC5D,CAAC;YAED,SAAS,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\cache-strategy.spec.ts"], "sourcesContent": ["import { \r\n  InMemoryCacheStrategy, \r\n  MultiLevelCacheStrategy,\r\n  CacheStrategy \r\n} from '../../patterns/cache-strategy';\r\n\r\ndescribe('Cache Strategy', () => {\r\n  describe('InMemoryCacheStrategy', () => {\r\n    let cache: InMemoryCacheStrategy;\r\n\r\n    beforeEach(() => {\r\n      cache = new InMemoryCacheStrategy({\r\n        defaultTtl: 60000, // 1 minute\r\n        maxSize: 100,\r\n        cleanupInterval: 1000,\r\n      });\r\n    });\r\n\r\n    afterEach(() => {\r\n      cache.destroy();\r\n    });\r\n\r\n    describe('basic operations', () => {\r\n      it('should store and retrieve values', async () => {\r\n        await cache.set('key1', 'value1');\r\n        const result = await cache.get('key1');\r\n        expect(result).toBe('value1');\r\n      });\r\n\r\n      it('should return null for non-existent keys', async () => {\r\n        const result = await cache.get('non-existent');\r\n        expect(result).toBeNull();\r\n      });\r\n\r\n      it('should check if key exists', async () => {\r\n        await cache.set('existing-key', 'value');\r\n        \r\n        expect(await cache.has('existing-key')).toBe(true);\r\n        expect(await cache.has('non-existent')).toBe(false);\r\n      });\r\n\r\n      it('should delete keys', async () => {\r\n        await cache.set('key-to-delete', 'value');\r\n        expect(await cache.has('key-to-delete')).toBe(true);\r\n        \r\n        const deleted = await cache.delete('key-to-delete');\r\n        expect(deleted).toBe(true);\r\n        expect(await cache.has('key-to-delete')).toBe(false);\r\n      });\r\n\r\n      it('should return false when deleting non-existent key', async () => {\r\n        const deleted = await cache.delete('non-existent');\r\n        expect(deleted).toBe(false);\r\n      });\r\n\r\n      it('should clear all entries', async () => {\r\n        await cache.set('key1', 'value1');\r\n        await cache.set('key2', 'value2');\r\n        await cache.set('key3', 'value3');\r\n        \r\n        await cache.clear();\r\n        \r\n        expect(await cache.has('key1')).toBe(false);\r\n        expect(await cache.has('key2')).toBe(false);\r\n        expect(await cache.has('key3')).toBe(false);\r\n      });\r\n\r\n      it('should clear entries matching pattern', async () => {\r\n        await cache.set('user:1:profile', 'profile1');\r\n        await cache.set('user:2:profile', 'profile2');\r\n        await cache.set('user:1:settings', 'settings1');\r\n        await cache.set('product:1', 'product1');\r\n        \r\n        await cache.clear('user:1:*');\r\n        \r\n        expect(await cache.has('user:1:profile')).toBe(false);\r\n        expect(await cache.has('user:1:settings')).toBe(false);\r\n        expect(await cache.has('user:2:profile')).toBe(true);\r\n        expect(await cache.has('product:1')).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('TTL functionality', () => {\r\n      it('should respect custom TTL', async () => {\r\n        await cache.set('short-lived', 'value', 50); // 50ms TTL\r\n        \r\n        expect(await cache.get('short-lived')).toBe('value');\r\n        \r\n        // Wait for expiration\r\n        await new Promise(resolve => setTimeout(resolve, 60));\r\n        \r\n        expect(await cache.get('short-lived')).toBeNull();\r\n      });\r\n\r\n      it('should use default TTL when not specified', async () => {\r\n        const shortTtlCache = new InMemoryCacheStrategy({ defaultTtl: 50 });\r\n        \r\n        await shortTtlCache.set('default-ttl', 'value');\r\n        expect(await shortTtlCache.get('default-ttl')).toBe('value');\r\n        \r\n        await new Promise(resolve => setTimeout(resolve, 60));\r\n        expect(await shortTtlCache.get('default-ttl')).toBeNull();\r\n        \r\n        shortTtlCache.destroy();\r\n      });\r\n\r\n      it('should handle expired entries in has() method', async () => {\r\n        await cache.set('expiring-key', 'value', 50);\r\n        \r\n        expect(await cache.has('expiring-key')).toBe(true);\r\n        \r\n        await new Promise(resolve => setTimeout(resolve, 60));\r\n        \r\n        expect(await cache.has('expiring-key')).toBe(false);\r\n      });\r\n\r\n      it('should clean up expired entries automatically', async () => {\r\n        const quickCleanupCache = new InMemoryCacheStrategy({\r\n          defaultTtl: 50,\r\n          cleanupInterval: 25,\r\n        });\r\n        \r\n        await quickCleanupCache.set('auto-cleanup', 'value');\r\n        \r\n        // Wait for expiration and cleanup\r\n        await new Promise(resolve => setTimeout(resolve, 80));\r\n        \r\n        const metrics = await quickCleanupCache.getMetrics();\r\n        expect(metrics.size).toBe(0);\r\n        \r\n        quickCleanupCache.destroy();\r\n      });\r\n    });\r\n\r\n    describe('size limits and eviction', () => {\r\n      it('should evict least recently used entries when size limit reached', async () => {\r\n        const smallCache = new InMemoryCacheStrategy({ maxSize: 2 });\r\n        \r\n        await smallCache.set('key1', 'value1');\r\n        // Small delay to ensure different timestamps\r\n        await new Promise(resolve => setTimeout(resolve, 1));\r\n        await smallCache.set('key2', 'value2');\r\n        \r\n        // Small delay before access\r\n        await new Promise(resolve => setTimeout(resolve, 1));\r\n        // Access key1 to make it more recently used\r\n        await smallCache.get('key1');\r\n        \r\n        // Small delay before adding new key\r\n        await new Promise(resolve => setTimeout(resolve, 1));\r\n        // Adding key3 should evict key2 (least recently used)\r\n        await smallCache.set('key3', 'value3');\r\n        \r\n        expect(await smallCache.has('key1')).toBe(true);\r\n        expect(await smallCache.has('key2')).toBe(false);\r\n        expect(await smallCache.has('key3')).toBe(true);\r\n        \r\n        smallCache.destroy();\r\n      });\r\n\r\n      it('should call onEvict callback when evicting entries', async () => {\r\n        const onEvict = jest.fn();\r\n        const smallCache = new InMemoryCacheStrategy({ \r\n          maxSize: 1,\r\n          onEvict,\r\n        });\r\n        \r\n        await smallCache.set('key1', 'value1');\r\n        // Small delay to ensure different timestamps\r\n        await new Promise(resolve => setTimeout(resolve, 1));\r\n        await smallCache.set('key2', 'value2'); // Should evict key1\r\n        \r\n        expect(onEvict).toHaveBeenCalledWith('key1', expect.objectContaining({\r\n          value: 'value1',\r\n        }));\r\n        \r\n        smallCache.destroy();\r\n      });\r\n\r\n      it('should not evict when updating existing key', async () => {\r\n        const smallCache = new InMemoryCacheStrategy({ maxSize: 2 });\r\n        \r\n        await smallCache.set('key1', 'value1');\r\n        await smallCache.set('key2', 'value2');\r\n        \r\n        // Update existing key - should not trigger eviction\r\n        await smallCache.set('key1', 'updated-value1');\r\n        \r\n        expect(await smallCache.has('key1')).toBe(true);\r\n        expect(await smallCache.has('key2')).toBe(true);\r\n        expect(await smallCache.get('key1')).toBe('updated-value1');\r\n        \r\n        smallCache.destroy();\r\n      });\r\n    });\r\n\r\n    describe('metrics', () => {\r\n      it('should track cache hits and misses', async () => {\r\n        await cache.set('key1', 'value1');\r\n        \r\n        // Hit\r\n        await cache.get('key1');\r\n        \r\n        // Miss\r\n        await cache.get('non-existent');\r\n        \r\n        const metrics = await cache.getMetrics();\r\n        expect(metrics.hits).toBe(1);\r\n        expect(metrics.misses).toBe(1);\r\n        expect(metrics.hitRate).toBe(0.5);\r\n      });\r\n\r\n      it('should track sets and deletes', async () => {\r\n        await cache.set('key1', 'value1');\r\n        await cache.set('key2', 'value2');\r\n        await cache.delete('key1');\r\n        \r\n        const metrics = await cache.getMetrics();\r\n        expect(metrics.sets).toBe(2);\r\n        expect(metrics.deletes).toBe(1);\r\n        expect(metrics.size).toBe(1);\r\n      });\r\n\r\n      it('should track evictions', async () => {\r\n        const smallCache = new InMemoryCacheStrategy({ maxSize: 1 });\r\n        \r\n        await smallCache.set('key1', 'value1');\r\n        // Small delay to ensure different timestamps\r\n        await new Promise(resolve => setTimeout(resolve, 1));\r\n        await smallCache.set('key2', 'value2'); // Should evict key1\r\n        \r\n        const metrics = await smallCache.getMetrics();\r\n        expect(metrics.evictions).toBe(1);\r\n        \r\n        smallCache.destroy();\r\n      });\r\n\r\n      it('should calculate hit rate correctly', async () => {\r\n        await cache.set('key1', 'value1');\r\n        \r\n        // 2 hits, 1 miss = 2/3 hit rate\r\n        await cache.get('key1');\r\n        await cache.get('key1');\r\n        await cache.get('non-existent');\r\n        \r\n        const metrics = await cache.getMetrics();\r\n        expect(metrics.hitRate).toBeCloseTo(2/3, 2);\r\n      });\r\n    });\r\n\r\n    describe('access tracking', () => {\r\n      it('should update access count and last accessed time', async () => {\r\n        await cache.set('tracked-key', 'value');\r\n        \r\n        // Access the key multiple times\r\n        await cache.get('tracked-key');\r\n        await cache.get('tracked-key');\r\n        await cache.get('tracked-key');\r\n        \r\n        // Access count should be tracked internally for LRU eviction\r\n        // We can't directly test this, but we can test LRU behavior\r\n        const smallCache = new InMemoryCacheStrategy({ maxSize: 2 });\r\n        \r\n        await smallCache.set('key1', 'value1');\r\n        // Small delay to ensure different timestamps\r\n        await new Promise(resolve => setTimeout(resolve, 1));\r\n        await smallCache.set('key2', 'value2');\r\n        \r\n        // Small delay before access\r\n        await new Promise(resolve => setTimeout(resolve, 1));\r\n        // Access key1 multiple times\r\n        await smallCache.get('key1');\r\n        await smallCache.get('key1');\r\n        \r\n        // Small delay before adding new key\r\n        await new Promise(resolve => setTimeout(resolve, 1));\r\n        // Add key3 - should evict key2 (less recently used)\r\n        await smallCache.set('key3', 'value3');\r\n        \r\n        expect(await smallCache.has('key1')).toBe(true);\r\n        expect(await smallCache.has('key2')).toBe(false);\r\n        expect(await smallCache.has('key3')).toBe(true);\r\n        \r\n        smallCache.destroy();\r\n      });\r\n    });\r\n\r\n    describe('manual cleanup', () => {\r\n      it('should manually clean up expired entries', async () => {\r\n        await cache.set('expiring1', 'value1', 50);\r\n        await cache.set('expiring2', 'value2', 50);\r\n        await cache.set('persistent', 'value3', 60000);\r\n        \r\n        // Wait for some entries to expire\r\n        await new Promise(resolve => setTimeout(resolve, 60));\r\n        \r\n        // Manual cleanup\r\n        cache.cleanup();\r\n        \r\n        expect(await cache.has('expiring1')).toBe(false);\r\n        expect(await cache.has('expiring2')).toBe(false);\r\n        expect(await cache.has('persistent')).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('data types', () => {\r\n      it('should handle different data types', async () => {\r\n        const testData = {\r\n          string: 'test string',\r\n          number: 42,\r\n          boolean: true,\r\n          object: { nested: 'value' },\r\n          array: [1, 2, 3],\r\n          null: null,\r\n          undefined: undefined,\r\n        };\r\n        \r\n        for (const [key, value] of Object.entries(testData)) {\r\n          await cache.set(key, value);\r\n          const retrieved = await cache.get(key);\r\n          expect(retrieved).toEqual(value);\r\n        }\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('MultiLevelCacheStrategy', () => {\r\n    let l1Cache: InMemoryCacheStrategy;\r\n    let l2Cache: InMemoryCacheStrategy;\r\n    let multiCache: MultiLevelCacheStrategy;\r\n\r\n    beforeEach(() => {\r\n      l1Cache = new InMemoryCacheStrategy({ defaultTtl: 60000 });\r\n      l2Cache = new InMemoryCacheStrategy({ defaultTtl: 60000 });\r\n      multiCache = new MultiLevelCacheStrategy(l1Cache, l2Cache);\r\n    });\r\n\r\n    afterEach(() => {\r\n      l1Cache.destroy();\r\n      l2Cache.destroy();\r\n    });\r\n\r\n    it('should check L1 cache first', async () => {\r\n      await l1Cache.set('l1-key', 'l1-value');\r\n      \r\n      const result = await multiCache.get('l1-key');\r\n      expect(result).toBe('l1-value');\r\n    });\r\n\r\n    it('should fall back to L2 cache and populate L1', async () => {\r\n      await l2Cache.set('l2-key', 'l2-value');\r\n      \r\n      // Should not be in L1 initially\r\n      expect(await l1Cache.get('l2-key')).toBeNull();\r\n      \r\n      // Get from multi-level cache\r\n      const result = await multiCache.get('l2-key');\r\n      expect(result).toBe('l2-value');\r\n      \r\n      // Should now be in L1\r\n      expect(await l1Cache.get('l2-key')).toBe('l2-value');\r\n    });\r\n\r\n    it('should return null when key not found in either cache', async () => {\r\n      const result = await multiCache.get('non-existent');\r\n      expect(result).toBeNull();\r\n    });\r\n\r\n    it('should set in both caches', async () => {\r\n      await multiCache.set('multi-key', 'multi-value');\r\n      \r\n      expect(await l1Cache.get('multi-key')).toBe('multi-value');\r\n      expect(await l2Cache.get('multi-key')).toBe('multi-value');\r\n    });\r\n\r\n    it('should delete from both caches', async () => {\r\n      await l1Cache.set('delete-key', 'value');\r\n      await l2Cache.set('delete-key', 'value');\r\n      \r\n      const deleted = await multiCache.delete('delete-key');\r\n      expect(deleted).toBe(true);\r\n      \r\n      expect(await l1Cache.has('delete-key')).toBe(false);\r\n      expect(await l2Cache.has('delete-key')).toBe(false);\r\n    });\r\n\r\n    it('should clear both caches', async () => {\r\n      await l1Cache.set('clear-key1', 'value1');\r\n      await l2Cache.set('clear-key2', 'value2');\r\n      \r\n      await multiCache.clear();\r\n      \r\n      expect(await l1Cache.has('clear-key1')).toBe(false);\r\n      expect(await l2Cache.has('clear-key2')).toBe(false);\r\n    });\r\n\r\n    it('should check L1 first, then L2 for has() method', async () => {\r\n      await l2Cache.set('l2-only', 'value');\r\n      \r\n      expect(await multiCache.has('l2-only')).toBe(true);\r\n    });\r\n\r\n    it('should combine metrics from both levels', async () => {\r\n      await multiCache.set('key1', 'value1');\r\n      await multiCache.get('key1'); // Hit in L1\r\n      await multiCache.get('non-existent'); // Miss in both\r\n      \r\n      const metrics = await multiCache.getMetrics();\r\n      expect(metrics.hits).toBeGreaterThan(0);\r\n      expect(metrics.misses).toBeGreaterThan(0);\r\n      expect(metrics.sets).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should handle TTL in both levels', async () => {\r\n      await multiCache.set('ttl-key', 'ttl-value', 50);\r\n      \r\n      expect(await multiCache.get('ttl-key')).toBe('ttl-value');\r\n      \r\n      await new Promise(resolve => setTimeout(resolve, 60));\r\n      \r\n      expect(await multiCache.get('ttl-key')).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('abstract CacheStrategy', () => {\r\n    it('should be extendable for custom implementations', async () => {\r\n      class CustomCacheStrategy extends CacheStrategy {\r\n        private store = new Map<string, any>();\r\n\r\n        async get<T>(key: string): Promise<T | null> {\r\n          return this.store.get(key) || null;\r\n        }\r\n\r\n        async set<T>(key: string, value: T, ttl?: number): Promise<void> {\r\n          this.store.set(key, value);\r\n        }\r\n\r\n        async delete(key: string): Promise<boolean> {\r\n          return this.store.delete(key);\r\n        }\r\n\r\n        async clear(pattern?: string): Promise<void> {\r\n          if (pattern) {\r\n            const regex = new RegExp(pattern.replace(/\\*/g, '.*'));\r\n            for (const key of this.store.keys()) {\r\n              if (regex.test(key)) {\r\n                this.store.delete(key);\r\n              }\r\n            }\r\n          } else {\r\n            this.store.clear();\r\n          }\r\n        }\r\n\r\n        async has(key: string): Promise<boolean> {\r\n          return this.store.has(key);\r\n        }\r\n\r\n        async getMetrics(): Promise<any> {\r\n          return {\r\n            hits: 0,\r\n            misses: 0,\r\n            sets: 0,\r\n            deletes: 0,\r\n            evictions: 0,\r\n            size: this.store.size,\r\n            hitRate: 0,\r\n          };\r\n        }\r\n      }\r\n\r\n      const customCache = new CustomCacheStrategy();\r\n      \r\n      await customCache.set('custom-key', 'custom-value');\r\n      expect(await customCache.get('custom-key')).toBe('custom-value');\r\n      expect(await customCache.has('custom-key')).toBe(true);\r\n      \r\n      await customCache.delete('custom-key');\r\n      expect(await customCache.has('custom-key')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle concurrent access gracefully', async () => {\r\n      const testCache = new InMemoryCacheStrategy();\r\n      const promises = [];\r\n      \r\n      // Concurrent sets\r\n      for (let i = 0; i < 10; i++) {\r\n        promises.push(testCache.set(`concurrent-${i}`, `value-${i}`));\r\n      }\r\n      \r\n      await Promise.all(promises);\r\n      \r\n      // Verify all values were set\r\n      for (let i = 0; i < 10; i++) {\r\n        expect(await testCache.get(`concurrent-${i}`)).toBe(`value-${i}`);\r\n      }\r\n      \r\n      testCache.destroy();\r\n    });\r\n\r\n    it('should handle large values', async () => {\r\n      const testCache = new InMemoryCacheStrategy();\r\n      const largeValue = 'x'.repeat(10000);\r\n      \r\n      await testCache.set('large-key', largeValue);\r\n      const retrieved = await testCache.get('large-key');\r\n      \r\n      expect(retrieved).toBe(largeValue);\r\n      \r\n      testCache.destroy();\r\n    });\r\n\r\n    it('should handle special characters in keys', async () => {\r\n      const testCache = new InMemoryCacheStrategy();\r\n      const specialKeys = [\r\n        'key with spaces',\r\n        'key:with:colons',\r\n        'key/with/slashes',\r\n        'key.with.dots',\r\n        'key-with-dashes',\r\n        'key_with_underscores',\r\n        'key@with@symbols',\r\n      ];\r\n      \r\n      for (const key of specialKeys) {\r\n        await testCache.set(key, `value-for-${key}`);\r\n        expect(await testCache.get(key)).toBe(`value-for-${key}`);\r\n      }\r\n      \r\n      testCache.destroy();\r\n    });\r\n  });\r\n});"], "version": 3}