{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\vulnerability-assessment.service.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAA4C;AAG5C,oFAA2E;AAE3E,2HAAsH;AACtH,iGAA4F;AAC5F,yEAAsE;AAsJtE;;;;;;;;;;;;;GAaG;AAEI,IAAM,8BAA8B,sCAApC,MAAM,8BAA8B;IAIzC,YACmB,cAAoC;QAApC,mBAAc,GAAd,cAAc,CAAsB;QAJtC,WAAM,GAAG,IAAI,eAAM,CAAC,gCAA8B,CAAC,IAAI,CAAC,CAAC;QACzD,qBAAgB,GAAG,IAAI,yEAAkC,EAAE,CAAC;IAI1E,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACvB,OAAuC;QAEvC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAE5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;YACpD,eAAe,EAAE,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC5C,KAAK,EAAE,aAAa,CAAC,KAAK;YAC1B,QAAQ,EAAE,aAAa,CAAC,QAAQ;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,wBAAwB;YACxB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YAEpF,wCAAwC;YACxC,IAAI,eAAe,CAAC;YACpB,IAAI,OAAO,CAAC,OAAO,EAAE,sBAAsB,KAAK,KAAK,EAAE,CAAC;gBACtD,eAAe,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;YACrE,CAAC;YAED,0CAA0C;YAC1C,IAAI,kBAAkB,CAAC;YACvB,IAAI,OAAO,CAAC,OAAO,EAAE,yBAAyB,EAAE,CAAC;gBAC/C,kBAAkB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;YAC1E,CAAC;YAED,sCAAsC;YACtC,IAAI,cAAc,CAAC;YACnB,IAAI,OAAO,CAAC,OAAO,EAAE,qBAAqB,EAAE,CAAC;gBAC3C,cAAc,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YACnF,CAAC;YAED,wCAAwC;YACxC,IAAI,gBAAgB,CAAC;YACrB,IAAI,OAAO,CAAC,OAAO,EAAE,uBAAuB,EAAE,CAAC;gBAC7C,gBAAgB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YACvF,CAAC;YAED,2BAA2B;YAC3B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CACxD,aAAa,EACb,YAAY,EACZ,eAAe,EACf,OAAO,CAAC,OAAO,CAChB,CAAC;YAEF,uCAAuC;YACvC,MAAM,OAAO,GAAG,IAAI,CAAC,0BAA0B,CAC7C,aAAa,EACb,YAAY,EACZ,eAAe,EACf,cAAc,CACf,CAAC;YAEF,MAAM,MAAM,GAAkC;gBAC5C,OAAO,EAAE,IAAI;gBACb,eAAe,EAAE,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE;gBAC5C,OAAO;gBACP,YAAY;gBACZ,eAAe;gBACf,kBAAkB;gBAClB,cAAc;gBACd,gBAAgB;gBAChB,eAAe;gBACf,QAAQ,EAAE;oBACR,cAAc,EAAE,IAAI,IAAI,EAAE;oBAC1B,kBAAkB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBAC1C,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC;oBACzD,iBAAiB,EAAE,KAAK;oBACxB,QAAQ,EAAE,gCAAgC;iBAC3C;aACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE;gBACrD,eAAe,EAAE,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE;gBAC5C,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,kBAAkB;aAC7C,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBACnD,eAAe,EAAE,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE;gBAC5C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,eAAe,EAAE,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE;gBAC5C,OAAO,EAAE;oBACP,WAAW,EAAE,QAAQ;oBACrB,QAAQ,EAAE,QAAQ;oBAClB,OAAO,EAAE,QAAQ;oBACjB,UAAU,EAAE,uCAAe,CAAC,GAAG;iBAChC;gBACD,YAAY,EAAE;oBACZ,aAAa,EAAE,CAAC;oBAChB,YAAY,EAAE,CAAC;oBACf,cAAc,EAAE,CAAC;oBACjB,gBAAgB,EAAE,CAAC;oBACnB,YAAY,EAAE,CAAC;iBAChB;gBACD,eAAe,EAAE;oBACf,SAAS,EAAE,CAAC,kBAAkB,EAAE,wBAAwB,CAAC;oBACzD,SAAS,EAAE,EAAE;oBACb,QAAQ,EAAE,EAAE;oBACZ,UAAU,EAAE,EAAE;iBACf;gBACD,QAAQ,EAAE;oBACR,cAAc,EAAE,IAAI,IAAI,EAAE;oBAC1B,kBAAkB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBAC1C,eAAe,EAAE,EAAE;oBACnB,iBAAiB,EAAE,KAAK;oBACxB,QAAQ,EAAE,gCAAgC;iBAC3C;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC7B,OAA8B;QAE9B,MAAM,MAAM,GAAG;YACb,kBAAkB,EAAE,IAAI;YACxB,uBAAuB,EAAE,IAAI;YAC7B,SAAS,EAAE,EAAE;YACb,SAAS,EAAE,MAAM,EAAE,YAAY;YAC/B,GAAG,OAAO,CAAC,MAAM;SAClB,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wCAAwC,EAAE;YACzD,kBAAkB,EAAE,OAAO,CAAC,eAAe,CAAC,MAAM;YAClD,SAAS,EAAE,MAAM,CAAC,SAAS;SAC5B,CAAC,CAAC;QAEH,MAAM,OAAO,GAAoC,EAAE,CAAC;QACpD,MAAM,MAAM,GAA0E,EAAE,CAAC;QAEzF,gDAAgD;QAChD,IAAI,eAAe,GAAG,CAAC,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;QACnD,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;YAC9B,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,CAAC;QACpE,CAAC;QAED,qBAAqB;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YAClE,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;YAE7D,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE;gBACtD,IAAI,CAAC;oBACH,MAAM,iBAAiB,GAAmC;wBACxD,aAAa;wBACb,OAAO,EAAE,OAAO,CAAC,OAAO;wBACxB,OAAO,EAAE;4BACP,sBAAsB,EAAE,MAAM,CAAC,uBAAuB;4BACtD,yBAAyB,EAAE,MAAM,CAAC,uBAAuB;4BACzD,qBAAqB,EAAE,MAAM,CAAC,uBAAuB;4BACrD,uBAAuB,EAAE,MAAM,CAAC,uBAAuB;yBACxD;qBACF,CAAC;oBAEF,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;gBAC3D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC;wBACV,eAAe,EAAE,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE;wBAC5C,KAAK,EAAE,KAAK,CAAC,OAAO;wBACpB,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;qBACxC,CAAC,CAAC;oBACH,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAE7D,KAAK,MAAM,MAAM,IAAI,YAAY,EAAE,CAAC;gBAClC,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;oBAClD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE;YAC1D,oBAAoB,EAAE,OAAO,CAAC,eAAe,CAAC,MAAM;YACpD,qBAAqB,EAAE,OAAO,CAAC,MAAM;YACrC,iBAAiB,EAAE,MAAM,CAAC,MAAM;YAChC,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;SACzD,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,IAAI;YACb,aAAa,EAAE,OAAO,CAAC,eAAe,CAAC,MAAM;YAC7C,qBAAqB,EAAE,OAAO,CAAC,MAAM;YACrC,iBAAiB,EAAE,MAAM,CAAC,MAAM;YAChC,OAAO;YACP,OAAO;YACP,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAC/B,aAA4B,EAC5B,OAAa;QAQb,yDAAyD;QACzD,IAAI,aAAa,GAAG,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC;QAE3D,yBAAyB;QACzB,IAAI,aAAa,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YACpF,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,GAAG,EAAE,CAAC,CAAC;QACxD,CAAC;QAED,wDAAwD;QACxD,IAAI,YAAY,GAAG,EAAE,CAAC,CAAC,qBAAqB;QAC5C,IAAI,aAAa,CAAC,qBAAqB,EAAE,EAAE,CAAC;YAC1C,YAAY,IAAI,EAAE,CAAC;QACrB,CAAC;QACD,IAAI,aAAa,CAAC,mBAAmB,EAAE,EAAE,CAAC;YACxC,YAAY,IAAI,EAAE,CAAC;QACrB,CAAC;QAED,wDAAwD;QACxD,IAAI,cAAc,GAAG,EAAE,CAAC,CAAC,uBAAuB;QAChD,IAAI,OAAO,EAAE,sBAAsB,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,cAAc,IAAI,OAAO,CAAC,sBAAsB,CAAC,MAAM,GAAG,EAAE,CAAC;QAC/D,CAAC;QAED,mDAAmD;QACnD,IAAI,gBAAgB,GAAG,EAAE,CAAC,CAAC,yBAAyB;QACpD,IAAI,aAAa,CAAC,UAAU,EAAE,EAAE,CAAC;YAC/B,gBAAgB,IAAI,EAAE,CAAC;QACzB,CAAC;QACD,IAAI,aAAa,CAAC,mBAAmB,EAAE,EAAE,CAAC;YACxC,gBAAgB,IAAI,EAAE,CAAC;QACzB,CAAC;QAED,4BAA4B;QAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAC/B,CAAC,aAAa,GAAG,GAAG,CAAC;YACrB,CAAC,YAAY,GAAG,GAAG,CAAC;YACpB,CAAC,cAAc,GAAG,GAAG,CAAC;YACtB,CAAC,gBAAgB,GAAG,GAAG,CAAC,CACzB,CAAC;QAEF,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC;YAC3C,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC;YACzC,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc,CAAC;YAC7C,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC;YACjD,YAAY;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,aAA4B;QAC/D,MAAM,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;QAEhD,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO;gBACL,cAAc,EAAE,KAAc;gBAC9B,iBAAiB,EAAE,CAAC;gBACpB,cAAc,EAAE,CAAC;gBACjB,kBAAkB,EAAE,CAAC;gBACrB,iBAAiB,EAAE,MAAe;gBAClC,aAAa,EAAE,EAAE;aAClB,CAAC;QACJ,CAAC;QAED,MAAM,cAAc,GAAG,YAAY,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;QAC9F,MAAM,kBAAkB,GAAG,YAAY,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QAElG,IAAI,cAAc,GAA2C,KAAK,CAAC;QACnE,IAAI,YAAY,CAAC,MAAM,KAAK,qBAAqB,EAAE,CAAC;YAClD,cAAc,GAAG,UAAU,CAAC;QAC9B,CAAC;aAAM,IAAI,YAAY,CAAC,MAAM,KAAK,YAAY,EAAE,CAAC;YAChD,cAAc,GAAG,MAAM,CAAC;QAC1B,CAAC;aAAM,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;YAC9B,cAAc,GAAG,QAAQ,CAAC;QAC5B,CAAC;QAED,OAAO;YACL,cAAc;YACd,iBAAiB,EAAE,YAAY,CAAC,iBAAiB,CAAC,MAAM;YACxD,cAAc;YACd,kBAAkB;YAClB,iBAAiB,EAAE,YAAY,CAAC,UAAU;YAC1C,aAAa,EAAE,YAAY,CAAC,aAAa;SAC1C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,aAA4B;QACjE,gEAAgE;QAChE,OAAO;YACL,mBAAmB,EAAE,QAAiB;YACtC,iBAAiB,EAAE,KAAc;YACjC,iBAAiB,EAAE,KAAK;YACxB,qBAAqB,EAAE,KAAK;YAC5B,MAAM,EAAE,EAAE;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,aAA4B,EAAE,OAAa;QAC5E,IAAI,iBAAiB,GAA2C,KAAK,CAAC;QACtE,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,mBAAmB,GAAG,GAAG,CAAC;QAE9B,IAAI,aAAa,CAAC,qBAAqB,EAAE,EAAE,CAAC;YAC1C,iBAAiB,GAAG,MAAM,CAAC;YAC3B,eAAe,GAAG,MAAM,CAAC,CAAC,mBAAmB;YAC7C,mBAAmB,GAAG,EAAE,CAAC;QAC3B,CAAC;QAED,OAAO;YACL,iBAAiB;YACjB,eAAe;YACf,cAAc,EAAE,iBAAiB;YACjC,kBAAkB,EAAE,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,MAAe,CAAC,CAAC,CAAC,QAAiB;YACpF,mBAAmB;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,aAA4B,EAAE,OAAa;QAC9E,OAAO;YACL,mBAAmB,EAAE,OAAO,EAAE,sBAAsB,IAAI,EAAE;YAC1D,iBAAiB,EAAE,aAAa,CAAC,UAAU,EAAE;YAC7C,cAAc,EAAE,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACtD,iBAAiB,EAAE,EAAE;YACrB,iBAAiB,EAAE,aAAa,CAAC,qBAAqB,EAAE;SACzD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CACnC,aAA4B,EAC5B,YAAiB,EACjB,eAAqB,EACrB,OAAa;QAEb,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,CAAC;YACvD,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAC;YACpF,SAAS,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;YACxC,SAAS,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;YACxC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAED,8BAA8B;QAC9B,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;YACxB,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACzC,CAAC;QAED,UAAU,CAAC,IAAI,CAAC,kCAAkC,EAAE,6BAA6B,CAAC,CAAC;QAEnF,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,0BAA0B,CAChC,aAA4B,EAC5B,YAAiB,EACjB,eAAqB,EACrB,cAAoB;QAEpB,IAAI,WAAW,GAA2C,KAAK,CAAC;QAChE,IAAI,QAAQ,GAA2C,KAAK,CAAC;QAC7D,IAAI,OAAO,GAA4C,KAAK,CAAC;QAE7D,IAAI,YAAY,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACpC,WAAW,GAAG,UAAU,CAAC;YACzB,QAAQ,GAAG,UAAU,CAAC;YACtB,OAAO,GAAG,WAAW,CAAC;QACxB,CAAC;aAAM,IAAI,YAAY,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YAC3C,WAAW,GAAG,MAAM,CAAC;YACrB,QAAQ,GAAG,MAAM,CAAC;YAClB,OAAO,GAAG,MAAM,CAAC;QACnB,CAAC;aAAM,IAAI,YAAY,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YAC3C,WAAW,GAAG,QAAQ,CAAC;YACvB,QAAQ,GAAG,QAAQ,CAAC;YACpB,OAAO,GAAG,QAAQ,CAAC;QACrB,CAAC;QAED,OAAO;YACL,WAAW;YACX,QAAQ;YACR,OAAO;YACP,UAAU,EAAE,aAAa,CAAC,UAAU;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,eAAgC;QAChE,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACnC,iCAAiC;YACjC,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtF,OAAO,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtF,OAAO,CAAC,CAAC;YACX,CAAC;YAED,qBAAqB;YACrB,OAAO,CAAC,CAAC,cAAc,CAAC,SAAS,GAAG,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAwC;QACnE,MAAM,uBAAuB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;QACjG,MAAM,uBAAuB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QAC7F,MAAM,yBAAyB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;QACjG,MAAM,sBAAsB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,CAAC,MAAM,CAAC;QAE3F,MAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC;YACzC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM;YACnF,CAAC,CAAC,CAAC,CAAC;QAEN,8BAA8B;QAC9B,MAAM,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAC7E,MAAM,oBAAoB,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAClE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/B,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,MAAM,kBAAkB,GAAG,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC;aAC5D,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;aAC3B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;aACX,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;QAEvB,OAAO;YACL,uBAAuB;YACvB,uBAAuB;YACvB,yBAAyB;YACzB,sBAAsB;YACtB,gBAAgB;YAChB,kBAAkB;SACnB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,OAAa;QACtC,MAAM,OAAO,GAAG,CAAC,wBAAwB,EAAE,aAAa,CAAC,CAAC;QAE1D,IAAI,OAAO,EAAE,sBAAsB,EAAE,CAAC;YACpC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,OAAO,EAAE,yBAAyB,EAAE,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC5C,CAAC;QACD,IAAI,OAAO,EAAE,qBAAqB,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,OAAO,EAAE,uBAAuB,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAU;QACjC,MAAM,eAAe,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,qBAAqB,EAAE,YAAY,CAAC,CAAC;QACpF,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;QACxD,OAAO,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;IACvF,CAAC;CACF,CAAA;AAzgBY,wEAA8B;yCAA9B,8BAA8B;IAD1C,IAAA,mBAAU,GAAE;yDAMwB,6CAAoB,oBAApB,6CAAoB;GAL5C,8BAA8B,CAygB1C", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\vulnerability-assessment.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { Vulnerability, VulnerabilityStatus } from '../../domain/entities/vulnerability/vulnerability.entity';\r\nimport { ThreatSeverity } from '../../domain/enums/threat-severity.enum';\r\nimport { ConfidenceLevel } from '../../domain/enums/confidence-level.enum';\r\nimport { CVSSScore } from '../../domain/value-objects/threat-indicators/cvss-score.value-object';\r\nimport { CriticalVulnerabilitySpecification } from '../../domain/specifications/critical-vulnerability.specification';\r\nimport { DomainEventPublisher } from '../../../shared-kernel/domain/domain-event-publisher';\r\nimport { Logger } from '../../../shared-kernel/infrastructure/logger';\r\n\r\n/**\r\n * Vulnerability Assessment Request\r\n */\r\nexport interface VulnerabilityAssessmentRequest {\r\n  /** Vulnerability to assess */\r\n  vulnerability: Vulnerability;\r\n  /** Assessment context */\r\n  context?: {\r\n    threatLandscape: string[];\r\n    businessContext: string[];\r\n    complianceRequirements: string[];\r\n    riskTolerance: 'low' | 'medium' | 'high';\r\n  };\r\n  /** Assessment options */\r\n  options?: {\r\n    includeExploitAnalysis: boolean;\r\n    includeThreatIntelligence: boolean;\r\n    includeBusinessImpact: boolean;\r\n    includeComplianceImpact: boolean;\r\n  };\r\n}\r\n\r\n/**\r\n * Vulnerability Assessment Result\r\n */\r\nexport interface VulnerabilityAssessmentResult {\r\n  /** Assessment success */\r\n  success: boolean;\r\n  /** Vulnerability ID */\r\n  vulnerabilityId: string;\r\n  /** Assessment summary */\r\n  summary: {\r\n    overallRisk: 'low' | 'medium' | 'high' | 'critical';\r\n    priority: 'low' | 'medium' | 'high' | 'critical';\r\n    urgency: 'low' | 'medium' | 'high' | 'immediate';\r\n    confidence: ConfidenceLevel;\r\n  };\r\n  /** Risk analysis */\r\n  riskAnalysis: {\r\n    technicalRisk: number;\r\n    businessRisk: number;\r\n    complianceRisk: number;\r\n    reputationalRisk: number;\r\n    combinedRisk: number;\r\n  };\r\n  /** Exploit analysis */\r\n  exploitAnalysis?: {\r\n    exploitability: 'low' | 'medium' | 'high' | 'critical';\r\n    availableExploits: number;\r\n    publicExploits: number;\r\n    weaponizedExploits: number;\r\n    exploitComplexity: 'low' | 'medium' | 'high';\r\n    attackVectors: string[];\r\n  };\r\n  /** Threat intelligence */\r\n  threatIntelligence?: {\r\n    threatActorInterest: 'low' | 'medium' | 'high';\r\n    campaignRelevance: 'low' | 'medium' | 'high';\r\n    industryTargeting: boolean;\r\n    geopoliticalRelevance: boolean;\r\n    trends: string[];\r\n  };\r\n  /** Business impact */\r\n  businessImpact?: {\r\n    operationalImpact: 'low' | 'medium' | 'high' | 'critical';\r\n    financialImpact: number;\r\n    customerImpact: 'low' | 'medium' | 'high';\r\n    reputationalImpact: 'low' | 'medium' | 'high';\r\n    serviceAvailability: number;\r\n  };\r\n  /** Compliance impact */\r\n  complianceImpact?: {\r\n    regulationsAffected: string[];\r\n    reportingRequired: boolean;\r\n    potentialFines: number;\r\n    auditImplications: string[];\r\n    certificationRisk: boolean;\r\n  };\r\n  /** Recommendations */\r\n  recommendations: {\r\n    immediate: string[];\r\n    shortTerm: string[];\r\n    longTerm: string[];\r\n    preventive: string[];\r\n  };\r\n  /** Assessment metadata */\r\n  metadata: {\r\n    assessmentDate: Date;\r\n    assessmentDuration: number;\r\n    dataSourcesUsed: string[];\r\n    assessmentVersion: string;\r\n    assessor: string;\r\n  };\r\n}\r\n\r\n/**\r\n * Bulk Assessment Request\r\n */\r\nexport interface BulkAssessmentRequest {\r\n  /** Vulnerabilities to assess */\r\n  vulnerabilities: Vulnerability[];\r\n  /** Assessment configuration */\r\n  config?: {\r\n    prioritizeCritical: boolean;\r\n    includeDetailedAnalysis: boolean;\r\n    batchSize: number;\r\n    timeoutMs: number;\r\n  };\r\n  /** Assessment context */\r\n  context?: {\r\n    threatLandscape: string[];\r\n    businessContext: string[];\r\n    complianceRequirements: string[];\r\n    riskTolerance: 'low' | 'medium' | 'high';\r\n  };\r\n}\r\n\r\n/**\r\n * Bulk Assessment Result\r\n */\r\nexport interface BulkAssessmentResult {\r\n  /** Assessment success */\r\n  success: boolean;\r\n  /** Total vulnerabilities assessed */\r\n  totalAssessed: number;\r\n  /** Successful assessments */\r\n  successfulAssessments: number;\r\n  /** Failed assessments */\r\n  failedAssessments: number;\r\n  /** Individual results */\r\n  results: VulnerabilityAssessmentResult[];\r\n  /** Summary statistics */\r\n  summary: {\r\n    criticalVulnerabilities: number;\r\n    highRiskVulnerabilities: number;\r\n    mediumRiskVulnerabilities: number;\r\n    lowRiskVulnerabilities: number;\r\n    averageRiskScore: number;\r\n    topRecommendations: string[];\r\n  };\r\n  /** Assessment errors */\r\n  errors: Array<{\r\n    vulnerabilityId: string;\r\n    error: string;\r\n    retryable: boolean;\r\n  }>;\r\n}\r\n\r\n/**\r\n * Vulnerability Assessment Application Service\r\n * \r\n * Provides comprehensive vulnerability assessment capabilities including\r\n * risk analysis, exploit assessment, threat intelligence integration,\r\n * and business impact evaluation.\r\n * \r\n * Key responsibilities:\r\n * - Comprehensive vulnerability risk assessment\r\n * - Exploit analysis and threat intelligence integration\r\n * - Business and compliance impact evaluation\r\n * - Prioritization and recommendation generation\r\n * - Bulk assessment processing\r\n */\r\n@Injectable()\r\nexport class VulnerabilityAssessmentService {\r\n  private readonly logger = new Logger(VulnerabilityAssessmentService.name);\r\n  private readonly criticalVulnSpec = new CriticalVulnerabilitySpecification();\r\n\r\n  constructor(\r\n    private readonly eventPublisher: DomainEventPublisher\r\n  ) {}\r\n\r\n  /**\r\n   * Assess a single vulnerability\r\n   */\r\n  async assessVulnerability(\r\n    request: VulnerabilityAssessmentRequest\r\n  ): Promise<VulnerabilityAssessmentResult> {\r\n    const startTime = Date.now();\r\n    const vulnerability = request.vulnerability;\r\n\r\n    this.logger.info('Starting vulnerability assessment', {\r\n      vulnerabilityId: vulnerability.id.toString(),\r\n      cveId: vulnerability.cveId,\r\n      severity: vulnerability.severity,\r\n    });\r\n\r\n    try {\r\n      // Perform risk analysis\r\n      const riskAnalysis = await this.performRiskAnalysis(vulnerability, request.context);\r\n\r\n      // Perform exploit analysis if requested\r\n      let exploitAnalysis;\r\n      if (request.options?.includeExploitAnalysis !== false) {\r\n        exploitAnalysis = await this.performExploitAnalysis(vulnerability);\r\n      }\r\n\r\n      // Gather threat intelligence if requested\r\n      let threatIntelligence;\r\n      if (request.options?.includeThreatIntelligence) {\r\n        threatIntelligence = await this.gatherThreatIntelligence(vulnerability);\r\n      }\r\n\r\n      // Assess business impact if requested\r\n      let businessImpact;\r\n      if (request.options?.includeBusinessImpact) {\r\n        businessImpact = await this.assessBusinessImpact(vulnerability, request.context);\r\n      }\r\n\r\n      // Assess compliance impact if requested\r\n      let complianceImpact;\r\n      if (request.options?.includeComplianceImpact) {\r\n        complianceImpact = await this.assessComplianceImpact(vulnerability, request.context);\r\n      }\r\n\r\n      // Generate recommendations\r\n      const recommendations = await this.generateRecommendations(\r\n        vulnerability,\r\n        riskAnalysis,\r\n        exploitAnalysis,\r\n        request.context\r\n      );\r\n\r\n      // Calculate overall assessment summary\r\n      const summary = this.calculateAssessmentSummary(\r\n        vulnerability,\r\n        riskAnalysis,\r\n        exploitAnalysis,\r\n        businessImpact\r\n      );\r\n\r\n      const result: VulnerabilityAssessmentResult = {\r\n        success: true,\r\n        vulnerabilityId: vulnerability.id.toString(),\r\n        summary,\r\n        riskAnalysis,\r\n        exploitAnalysis,\r\n        threatIntelligence,\r\n        businessImpact,\r\n        complianceImpact,\r\n        recommendations,\r\n        metadata: {\r\n          assessmentDate: new Date(),\r\n          assessmentDuration: Date.now() - startTime,\r\n          dataSourcesUsed: this.getDataSourcesUsed(request.options),\r\n          assessmentVersion: '1.0',\r\n          assessor: 'VulnerabilityAssessmentService',\r\n        },\r\n      };\r\n\r\n      this.logger.info('Vulnerability assessment completed', {\r\n        vulnerabilityId: vulnerability.id.toString(),\r\n        overallRisk: summary.overallRisk,\r\n        priority: summary.priority,\r\n        duration: result.metadata.assessmentDuration,\r\n      });\r\n\r\n      return result;\r\n\r\n    } catch (error) {\r\n      this.logger.error('Vulnerability assessment failed', {\r\n        vulnerabilityId: vulnerability.id.toString(),\r\n        error: error.message,\r\n        stack: error.stack,\r\n      });\r\n\r\n      return {\r\n        success: false,\r\n        vulnerabilityId: vulnerability.id.toString(),\r\n        summary: {\r\n          overallRisk: 'medium',\r\n          priority: 'medium',\r\n          urgency: 'medium',\r\n          confidence: ConfidenceLevel.LOW,\r\n        },\r\n        riskAnalysis: {\r\n          technicalRisk: 0,\r\n          businessRisk: 0,\r\n          complianceRisk: 0,\r\n          reputationalRisk: 0,\r\n          combinedRisk: 0,\r\n        },\r\n        recommendations: {\r\n          immediate: ['Retry assessment', 'Manual review required'],\r\n          shortTerm: [],\r\n          longTerm: [],\r\n          preventive: [],\r\n        },\r\n        metadata: {\r\n          assessmentDate: new Date(),\r\n          assessmentDuration: Date.now() - startTime,\r\n          dataSourcesUsed: [],\r\n          assessmentVersion: '1.0',\r\n          assessor: 'VulnerabilityAssessmentService',\r\n        },\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Assess multiple vulnerabilities in bulk\r\n   */\r\n  async assessVulnerabilitiesBulk(\r\n    request: BulkAssessmentRequest\r\n  ): Promise<BulkAssessmentResult> {\r\n    const config = {\r\n      prioritizeCritical: true,\r\n      includeDetailedAnalysis: true,\r\n      batchSize: 10,\r\n      timeoutMs: 300000, // 5 minutes\r\n      ...request.config,\r\n    };\r\n\r\n    this.logger.info('Starting bulk vulnerability assessment', {\r\n      vulnerabilityCount: request.vulnerabilities.length,\r\n      batchSize: config.batchSize,\r\n    });\r\n\r\n    const results: VulnerabilityAssessmentResult[] = [];\r\n    const errors: Array<{ vulnerabilityId: string; error: string; retryable: boolean }> = [];\r\n\r\n    // Sort vulnerabilities by priority if requested\r\n    let vulnerabilities = [...request.vulnerabilities];\r\n    if (config.prioritizeCritical) {\r\n      vulnerabilities = this.prioritizeVulnerabilities(vulnerabilities);\r\n    }\r\n\r\n    // Process in batches\r\n    for (let i = 0; i < vulnerabilities.length; i += config.batchSize) {\r\n      const batch = vulnerabilities.slice(i, i + config.batchSize);\r\n      \r\n      const batchPromises = batch.map(async (vulnerability) => {\r\n        try {\r\n          const assessmentRequest: VulnerabilityAssessmentRequest = {\r\n            vulnerability,\r\n            context: request.context,\r\n            options: {\r\n              includeExploitAnalysis: config.includeDetailedAnalysis,\r\n              includeThreatIntelligence: config.includeDetailedAnalysis,\r\n              includeBusinessImpact: config.includeDetailedAnalysis,\r\n              includeComplianceImpact: config.includeDetailedAnalysis,\r\n            },\r\n          };\r\n\r\n          return await this.assessVulnerability(assessmentRequest);\r\n        } catch (error) {\r\n          errors.push({\r\n            vulnerabilityId: vulnerability.id.toString(),\r\n            error: error.message,\r\n            retryable: this.isRetryableError(error),\r\n          });\r\n          return null;\r\n        }\r\n      });\r\n\r\n      const batchResults = await Promise.allSettled(batchPromises);\r\n      \r\n      for (const result of batchResults) {\r\n        if (result.status === 'fulfilled' && result.value) {\r\n          results.push(result.value);\r\n        }\r\n      }\r\n    }\r\n\r\n    const summary = this.calculateBulkSummary(results);\r\n\r\n    this.logger.info('Bulk vulnerability assessment completed', {\r\n      totalVulnerabilities: request.vulnerabilities.length,\r\n      successfulAssessments: results.length,\r\n      failedAssessments: errors.length,\r\n      criticalVulnerabilities: summary.criticalVulnerabilities,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      totalAssessed: request.vulnerabilities.length,\r\n      successfulAssessments: results.length,\r\n      failedAssessments: errors.length,\r\n      results,\r\n      summary,\r\n      errors,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Perform risk analysis\r\n   */\r\n  private async performRiskAnalysis(\r\n    vulnerability: Vulnerability,\r\n    context?: any\r\n  ): Promise<{\r\n    technicalRisk: number;\r\n    businessRisk: number;\r\n    complianceRisk: number;\r\n    reputationalRisk: number;\r\n    combinedRisk: number;\r\n  }> {\r\n    // Technical risk based on CVSS, severity, exploitability\r\n    let technicalRisk = vulnerability.riskAssessment.riskScore;\r\n\r\n    // Adjust for CVSS scores\r\n    if (vulnerability.cvssScores.length > 0) {\r\n      const maxCVSS = Math.max(...vulnerability.cvssScores.map(score => score.baseScore));\r\n      technicalRisk = Math.max(technicalRisk, maxCVSS * 10);\r\n    }\r\n\r\n    // Business risk based on asset criticality and exposure\r\n    let businessRisk = 50; // Base business risk\r\n    if (vulnerability.affectsCriticalAssets()) {\r\n      businessRisk += 30;\r\n    }\r\n    if (vulnerability.isExternallyExposed()) {\r\n      businessRisk += 20;\r\n    }\r\n\r\n    // Compliance risk based on regulations and requirements\r\n    let complianceRisk = 30; // Base compliance risk\r\n    if (context?.complianceRequirements?.length > 0) {\r\n      complianceRisk += context.complianceRequirements.length * 10;\r\n    }\r\n\r\n    // Reputational risk based on severity and exposure\r\n    let reputationalRisk = 20; // Base reputational risk\r\n    if (vulnerability.isCritical()) {\r\n      reputationalRisk += 40;\r\n    }\r\n    if (vulnerability.isExternallyExposed()) {\r\n      reputationalRisk += 30;\r\n    }\r\n\r\n    // Combined risk calculation\r\n    const combinedRisk = Math.min(100, \r\n      (technicalRisk * 0.4) + \r\n      (businessRisk * 0.3) + \r\n      (complianceRisk * 0.2) + \r\n      (reputationalRisk * 0.1)\r\n    );\r\n\r\n    return {\r\n      technicalRisk: Math.min(100, technicalRisk),\r\n      businessRisk: Math.min(100, businessRisk),\r\n      complianceRisk: Math.min(100, complianceRisk),\r\n      reputationalRisk: Math.min(100, reputationalRisk),\r\n      combinedRisk,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Perform exploit analysis\r\n   */\r\n  private async performExploitAnalysis(vulnerability: Vulnerability) {\r\n    const exploitation = vulnerability.exploitation;\r\n    \r\n    if (!exploitation) {\r\n      return {\r\n        exploitability: 'low' as const,\r\n        availableExploits: 0,\r\n        publicExploits: 0,\r\n        weaponizedExploits: 0,\r\n        exploitComplexity: 'high' as const,\r\n        attackVectors: [],\r\n      };\r\n    }\r\n\r\n    const publicExploits = exploitation.availableExploits.filter(e => e.type === 'public').length;\r\n    const weaponizedExploits = exploitation.availableExploits.filter(e => e.reliability >= 80).length;\r\n\r\n    let exploitability: 'low' | 'medium' | 'high' | 'critical' = 'low';\r\n    if (exploitation.status === 'active_exploitation') {\r\n      exploitability = 'critical';\r\n    } else if (exploitation.status === 'weaponized') {\r\n      exploitability = 'high';\r\n    } else if (publicExploits > 0) {\r\n      exploitability = 'medium';\r\n    }\r\n\r\n    return {\r\n      exploitability,\r\n      availableExploits: exploitation.availableExploits.length,\r\n      publicExploits,\r\n      weaponizedExploits,\r\n      exploitComplexity: exploitation.difficulty,\r\n      attackVectors: exploitation.attackVectors,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Gather threat intelligence\r\n   */\r\n  private async gatherThreatIntelligence(vulnerability: Vulnerability) {\r\n    // This would typically integrate with threat intelligence feeds\r\n    return {\r\n      threatActorInterest: 'medium' as const,\r\n      campaignRelevance: 'low' as const,\r\n      industryTargeting: false,\r\n      geopoliticalRelevance: false,\r\n      trends: [],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Assess business impact\r\n   */\r\n  private async assessBusinessImpact(vulnerability: Vulnerability, context?: any) {\r\n    let operationalImpact: 'low' | 'medium' | 'high' | 'critical' = 'low';\r\n    let financialImpact = 0;\r\n    let serviceAvailability = 100;\r\n\r\n    if (vulnerability.affectsCriticalAssets()) {\r\n      operationalImpact = 'high';\r\n      financialImpact = 100000; // Estimated impact\r\n      serviceAvailability = 80;\r\n    }\r\n\r\n    return {\r\n      operationalImpact,\r\n      financialImpact,\r\n      customerImpact: operationalImpact,\r\n      reputationalImpact: vulnerability.isCritical() ? 'high' as const : 'medium' as const,\r\n      serviceAvailability,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Assess compliance impact\r\n   */\r\n  private async assessComplianceImpact(vulnerability: Vulnerability, context?: any) {\r\n    return {\r\n      regulationsAffected: context?.complianceRequirements || [],\r\n      reportingRequired: vulnerability.isCritical(),\r\n      potentialFines: vulnerability.isCritical() ? 50000 : 0,\r\n      auditImplications: [],\r\n      certificationRisk: vulnerability.affectsCriticalAssets(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate recommendations\r\n   */\r\n  private async generateRecommendations(\r\n    vulnerability: Vulnerability,\r\n    riskAnalysis: any,\r\n    exploitAnalysis?: any,\r\n    context?: any\r\n  ) {\r\n    const immediate: string[] = [];\r\n    const shortTerm: string[] = [];\r\n    const longTerm: string[] = [];\r\n    const preventive: string[] = [];\r\n\r\n    if (this.criticalVulnSpec.isSatisfiedBy(vulnerability)) {\r\n      const strategies = this.criticalVulnSpec.getRiskMitigationStrategies(vulnerability);\r\n      immediate.push(...strategies.immediate);\r\n      shortTerm.push(...strategies.shortTerm);\r\n      longTerm.push(...strategies.longTerm);\r\n    }\r\n\r\n    // Add general recommendations\r\n    if (vulnerability.cveId) {\r\n      shortTerm.push('Apply vendor patches');\r\n    }\r\n    \r\n    preventive.push('Implement vulnerability scanning', 'Security awareness training');\r\n\r\n    return { immediate, shortTerm, longTerm, preventive };\r\n  }\r\n\r\n  /**\r\n   * Calculate assessment summary\r\n   */\r\n  private calculateAssessmentSummary(\r\n    vulnerability: Vulnerability,\r\n    riskAnalysis: any,\r\n    exploitAnalysis?: any,\r\n    businessImpact?: any\r\n  ) {\r\n    let overallRisk: 'low' | 'medium' | 'high' | 'critical' = 'low';\r\n    let priority: 'low' | 'medium' | 'high' | 'critical' = 'low';\r\n    let urgency: 'low' | 'medium' | 'high' | 'immediate' = 'low';\r\n\r\n    if (riskAnalysis.combinedRisk >= 80) {\r\n      overallRisk = 'critical';\r\n      priority = 'critical';\r\n      urgency = 'immediate';\r\n    } else if (riskAnalysis.combinedRisk >= 60) {\r\n      overallRisk = 'high';\r\n      priority = 'high';\r\n      urgency = 'high';\r\n    } else if (riskAnalysis.combinedRisk >= 40) {\r\n      overallRisk = 'medium';\r\n      priority = 'medium';\r\n      urgency = 'medium';\r\n    }\r\n\r\n    return {\r\n      overallRisk,\r\n      priority,\r\n      urgency,\r\n      confidence: vulnerability.confidence,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Prioritize vulnerabilities for assessment\r\n   */\r\n  private prioritizeVulnerabilities(vulnerabilities: Vulnerability[]): Vulnerability[] {\r\n    return vulnerabilities.sort((a, b) => {\r\n      // Critical vulnerabilities first\r\n      if (this.criticalVulnSpec.isSatisfiedBy(a) && !this.criticalVulnSpec.isSatisfiedBy(b)) {\r\n        return -1;\r\n      }\r\n      if (!this.criticalVulnSpec.isSatisfiedBy(a) && this.criticalVulnSpec.isSatisfiedBy(b)) {\r\n        return 1;\r\n      }\r\n\r\n      // Then by risk score\r\n      return b.riskAssessment.riskScore - a.riskAssessment.riskScore;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Calculate bulk assessment summary\r\n   */\r\n  private calculateBulkSummary(results: VulnerabilityAssessmentResult[]) {\r\n    const criticalVulnerabilities = results.filter(r => r.summary.overallRisk === 'critical').length;\r\n    const highRiskVulnerabilities = results.filter(r => r.summary.overallRisk === 'high').length;\r\n    const mediumRiskVulnerabilities = results.filter(r => r.summary.overallRisk === 'medium').length;\r\n    const lowRiskVulnerabilities = results.filter(r => r.summary.overallRisk === 'low').length;\r\n\r\n    const averageRiskScore = results.length > 0 \r\n      ? results.reduce((sum, r) => sum + r.riskAnalysis.combinedRisk, 0) / results.length \r\n      : 0;\r\n\r\n    // Collect top recommendations\r\n    const allRecommendations = results.flatMap(r => r.recommendations.immediate);\r\n    const recommendationCounts = allRecommendations.reduce((acc, rec) => {\r\n      acc[rec] = (acc[rec] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<string, number>);\r\n\r\n    const topRecommendations = Object.entries(recommendationCounts)\r\n      .sort(([,a], [,b]) => b - a)\r\n      .slice(0, 5)\r\n      .map(([rec]) => rec);\r\n\r\n    return {\r\n      criticalVulnerabilities,\r\n      highRiskVulnerabilities,\r\n      mediumRiskVulnerabilities,\r\n      lowRiskVulnerabilities,\r\n      averageRiskScore,\r\n      topRecommendations,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get data sources used for assessment\r\n   */\r\n  private getDataSourcesUsed(options?: any): string[] {\r\n    const sources = ['vulnerability_database', 'cvss_scores'];\r\n    \r\n    if (options?.includeExploitAnalysis) {\r\n      sources.push('exploit_database');\r\n    }\r\n    if (options?.includeThreatIntelligence) {\r\n      sources.push('threat_intelligence_feeds');\r\n    }\r\n    if (options?.includeBusinessImpact) {\r\n      sources.push('asset_management');\r\n    }\r\n    if (options?.includeComplianceImpact) {\r\n      sources.push('compliance_framework');\r\n    }\r\n\r\n    return sources;\r\n  }\r\n\r\n  /**\r\n   * Check if error is retryable\r\n   */\r\n  private isRetryableError(error: any): boolean {\r\n    const retryableErrors = ['timeout', 'network', 'service_unavailable', 'rate_limit'];\r\n    const errorMessage = error.message?.toLowerCase() || '';\r\n    return retryableErrors.some(retryableError => errorMessage.includes(retryableError));\r\n  }\r\n}\r\n"], "version": 3}