{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\response-action.factory.ts", "mappings": ";;;AAAA,+EAAyF;AAEzF,gEAAwE;AACxE,oEAA2D;AAuE3D;;;;;;;;;;;;;GAaG;AACH,MAAa,qBAAqB;IAChC;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,OAAoC;QAChD,oCAAoC;QACpC,qBAAqB,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;QAE3D,sCAAsC;QACtC,MAAM,QAAQ,GAAG,qBAAqB,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAE7E,0BAA0B;QAC1B,MAAM,WAAW,GAAwB;YACvC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,iCAAY,CAAC,OAAO;YAC9C,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ;YAC/C,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,QAAQ,CAAC,WAAW;YACxD,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,QAAQ,CAAC,YAAY;YAC3D,wBAAwB,EAAE,OAAO,CAAC,wBAAwB,IAAI,QAAQ,CAAC,wBAAwB;YAC/F,qBAAqB,EAAE,SAAS;YAChC,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,IAAI,QAAQ,CAAC,mBAAmB;YAChF,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,QAAQ,CAAC,gBAAgB;YACvE,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa;YAC9D,UAAU,EAAE,SAAS;YACrB,UAAU,EAAE,SAAS;YACrB,WAAW,EAAE,SAAS;YACtB,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,UAAU,EAAE,SAAS;YACrB,UAAU,EAAE,SAAS;YACrB,gBAAgB,EAAE,SAAS;YAC3B,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,QAAQ,CAAC,eAAe;YACpE,kBAAkB,EAAE,SAAS;YAC7B,cAAc,EAAE,SAAS;YACzB,UAAU,EAAE,CAAC;YACb,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU;YACrD,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,IAAI,QAAQ,CAAC,iBAAiB;YAC1E,WAAW,EAAE,SAAS;YACtB,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,QAAQ,CAAC,YAAY;YAC3D,UAAU,EAAE,KAAK;YACjB,eAAe,EAAE,SAAS;YAC1B,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI;YACnC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;YAChC,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,eAAe,EAAE,OAAO,CAAC,eAAe;YACxC,sBAAsB,EAAE,OAAO,CAAC,sBAAsB;YACtD,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,cAAc,EAAE,EAAE;YAClB,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,QAAQ,CAAC,cAAc;YACjE,QAAQ,EAAE,KAAK;YACf,UAAU,EAAE,SAAS;SACtB,CAAC;QAEF,OAAO,uCAAc,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAC5B,UAAsB,EACtB,KAAa,EACb,MAA6C,EAC7C,UAA+B,EAC/B,OAA8C;QAE9C,IAAI,CAAC,kCAAe,CAAC,yBAAyB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACtE,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,mCAAmC,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,qBAAqB,CAAC,MAAM,CAAC;YAClC,UAAU;YACV,KAAK;YACL,MAAM;YACN,UAAU;YACV,QAAQ,EAAE,MAAM;YAChB,WAAW,EAAE,kCAAe,CAAC,WAAW,CAAC,UAAU,CAAC;YACpD,gBAAgB,EAAE,kCAAe,CAAC,UAAU,CAAC,UAAU,CAAC;YACxD,IAAI,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC;YACjC,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAC5B,UAAsB,EACtB,KAAa,EACb,MAA6C,EAC7C,UAA+B,EAC/B,OAA8C;QAE9C,IAAI,CAAC,kCAAe,CAAC,yBAAyB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACtE,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,oCAAoC,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,qBAAqB,CAAC,MAAM,CAAC;YAClC,UAAU;YACV,KAAK;YACL,MAAM;YACN,UAAU;YACV,QAAQ,EAAE,MAAM;YAChB,WAAW,EAAE,kCAAe,CAAC,WAAW,CAAC,UAAU,CAAC;YACpD,gBAAgB,EAAE,IAAI,EAAE,iDAAiD;YACzE,IAAI,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC;YACjC,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CACzB,UAAsB,EACtB,KAAa,EACb,MAA6C,EAC7C,UAA+B,EAC/B,OAA8C;QAE9C,IAAI,CAAC,kCAAe,CAAC,sBAAsB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,gCAAgC,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,qBAAqB,CAAC,MAAM,CAAC;YAClC,UAAU;YACV,KAAK;YACL,MAAM;YACN,UAAU;YACV,QAAQ,EAAE,QAAQ;YAClB,WAAW,EAAE,kCAAe,CAAC,WAAW,CAAC,UAAU,CAAC;YACpD,gBAAgB,EAAE,kCAAe,CAAC,UAAU,CAAC,UAAU,CAAC;YACxD,IAAI,EAAE,CAAC,UAAU,EAAE,aAAa,CAAC;YACjC,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAC7B,UAAsB,EACtB,KAAa,EACb,UAA+B,EAC/B,OAA8C;QAE9C,IAAI,CAAC,kCAAe,CAAC,0BAA0B,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,oCAAoC,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,qBAAqB,CAAC,MAAM,CAAC;YAClC,UAAU;YACV,KAAK;YACL,UAAU;YACV,QAAQ,EAAE,QAAQ;YAClB,WAAW,EAAE,IAAI;YACjB,gBAAgB,EAAE,KAAK;YACvB,IAAI,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC;YACvC,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAC1B,UAAsB,EACtB,KAAa,EACb,UAA+B,EAC/B,OAA8C;QAE9C,IAAI,CAAC,kCAAe,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,kCAAkC,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,qBAAqB,CAAC,MAAM,CAAC;YAClC,UAAU;YACV,KAAK;YACL,UAAU;YACV,WAAW,EAAE,IAAI;YACjB,gBAAgB,EAAE,kCAAe,CAAC,UAAU,CAAC,UAAU,CAAC;YACxD,MAAM,EAAE,iCAAY,CAAC,QAAQ,EAAE,uCAAuC;YACtE,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CACvB,UAAsB,EACtB,KAAa,EACb,UAA+B,EAC/B,OAA8C;QAE9C,IAAI,CAAC,kCAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,8BAA8B,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,qBAAqB,CAAC,MAAM,CAAC;YAClC,UAAU;YACV,KAAK;YACL,UAAU;YACV,WAAW,EAAE,KAAK;YAClB,gBAAgB,EAAE,IAAI;YACtB,IAAI,EAAE,CAAC,QAAQ,EAAE,gBAAgB,CAAC;YAClC,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAC7B,UAAsB,EACtB,KAAa,EACb,UAA+B,EAC/B,OAA8C;QAE9C,OAAO,qBAAqB,CAAC,MAAM,CAAC;YAClC,UAAU;YACV,KAAK;YACL,UAAU;YACV,QAAQ,EAAE,MAAM;YAChB,IAAI,EAAE,CAAC,eAAe,EAAE,QAAQ,CAAC;YACjC,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CACzB,UAAsB,EACtB,KAAa,EACb,UAA+B,EAC/B,OAA8C;QAE9C,OAAO,qBAAqB,CAAC,MAAM,CAAC;YAClC,UAAU;YACV,KAAK;YACL,UAAU;YACV,QAAQ,EAAE,UAAU;YACpB,gBAAgB,EAAE,IAAI;YACtB,aAAa,EAAE,SAAS;YACxB,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;YAC/B,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CACxB,YAA4B,EAC5B,UAAsB,EACtB,KAAa,EACb,UAA+B,EAC/B,OAA8C;QAE9C,MAAM,WAAW,GAAG,qBAAqB,CAAC,MAAM,CAAC;YAC/C,UAAU;YACV,KAAK;YACL,UAAU;YACV,cAAc,EAAE,YAAY,CAAC,EAAE;YAC/B,aAAa,EAAE,YAAY,CAAC,aAAa,IAAI,YAAY,CAAC,EAAE,CAAC,QAAQ,EAAE;YACvE,QAAQ,EAAE,YAAY,CAAC,QAAQ,EAAE,+BAA+B;YAChE,IAAI,EAAE,CAAC,GAAG,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC;YACvC,GAAG,OAAO;SACX,CAAC,CAAC;QAEH,sBAAsB;QACtB,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAE5C,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CACtB,OAAuB,EACvB,UAAsB,EACtB,KAAa,EACb,UAA+B,EAC/B,OAA8C;QAE9C,OAAO,qBAAqB,CAAC,MAAM,CAAC;YAClC,UAAU;YACV,KAAK;YACL,UAAU;YACV,cAAc,EAAE,OAAO;YACvB,MAAM,EAAE;gBACN,IAAI,EAAE,OAAO;gBACb,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAE;aACvB;YACD,IAAI,EAAE,CAAC,gBAAgB,EAAE,oBAAoB,CAAC;YAC9C,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CACvB,QAAwB,EACxB,UAAsB,EACtB,KAAa,EACb,UAA+B,EAC/B,OAA8C;QAE9C,OAAO,qBAAqB,CAAC,MAAM,CAAC;YAClC,UAAU;YACV,KAAK;YACL,UAAU;YACV,eAAe,EAAE,QAAQ;YACzB,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,EAAE,EAAE,QAAQ,CAAC,QAAQ,EAAE;aACxB;YACD,QAAQ,EAAE,MAAM,EAAE,+CAA+C;YACjE,IAAI,EAAE,CAAC,iBAAiB,EAAE,UAAU,CAAC;YACrC,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,yBAAyB,CAC9B,eAA+B,EAC/B,UAAsB,EACtB,KAAa,EACb,UAA+B,EAC/B,OAA8C;QAE9C,OAAO,qBAAqB,CAAC,MAAM,CAAC;YAClC,UAAU;YACV,KAAK;YACL,UAAU;YACV,sBAAsB,EAAE,eAAe;YACvC,MAAM,EAAE;gBACN,IAAI,EAAE,eAAe;gBACrB,EAAE,EAAE,eAAe,CAAC,QAAQ,EAAE;aAC/B;YACD,IAAI,EAAE,CAAC,wBAAwB,EAAE,aAAa,CAAC;YAC/C,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAAC,UAAsB;QAmBrD,MAAM,WAAW,GAAG,kCAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC5D,MAAM,UAAU,GAAG,kCAAe,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAC1D,MAAM,YAAY,GAAG,kCAAe,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAC9D,MAAM,aAAa,GAAG,kCAAe,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;QAC3E,MAAM,mBAAmB,GAAG,kCAAe,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAC/E,MAAM,YAAY,GAAG,kCAAe,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;QACzE,MAAM,eAAe,GAAG,kCAAe,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAEvE,qDAAqD;QACrD,IAAI,QAAQ,GAA2C,QAAQ,CAAC;QAChE,IAAI,kCAAe,CAAC,yBAAyB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACrE,QAAQ,GAAG,MAAM,CAAC;QACpB,CAAC;aAAM,IAAI,kCAAe,CAAC,yBAAyB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5E,QAAQ,GAAG,MAAM,CAAC;QACpB,CAAC;aAAM,IAAI,UAAU,EAAE,CAAC;YACtB,QAAQ,GAAG,MAAM,CAAC;QACpB,CAAC;QAED,8CAA8C;QAC9C,IAAI,YAAY,GAAwB,SAAS,CAAC;QAClD,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,GAAG;gBACb,WAAW,EAAE,IAAI;gBACjB,aAAa,EAAE,qBAAqB,CAAC,qBAAqB,CAAC,UAAU,CAAC;aACvE,CAAC;QACJ,CAAC;QAED,gDAAgD;QAChD,MAAM,IAAI,GAAa,EAAE,CAAC;QAC1B,IAAI,WAAW;YAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxC,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,UAAU;YAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACvC,IAAI,YAAY;YAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1C,IAAI,kCAAe,CAAC,yBAAyB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC/F,IAAI,kCAAe,CAAC,yBAAyB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC/F,IAAI,kCAAe,CAAC,sBAAsB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACzF,IAAI,kCAAe,CAAC,0BAA0B,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEjG,OAAO;YACL,QAAQ;YACR,WAAW;YACX,YAAY;YACZ,wBAAwB,EAAE,aAAa;YACvC,mBAAmB;YACnB,gBAAgB,EAAE,YAAY,CAAC,QAAQ;YACvC,aAAa,EAAE,YAAY,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;YAC7E,eAAe;YACf,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,iBAAiB,EAAE,CAAC;YACpB,YAAY;YACZ,IAAI;YACJ,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,mDAAmD;SACrG,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAAC,UAAsB;QACzD,MAAM,aAAa,GAAiC;YAClD,CAAC,6BAAU,CAAC,QAAQ,CAAC,EAAE;gBACrB,2BAA2B;gBAC3B,uBAAuB;gBACvB,8BAA8B;aAC/B;YACD,CAAC,6BAAU,CAAC,YAAY,CAAC,EAAE;gBACzB,+BAA+B;gBAC/B,4BAA4B;gBAC5B,kCAAkC;aACnC;YACD,CAAC,6BAAU,CAAC,eAAe,CAAC,EAAE;gBAC5B,wBAAwB;gBACxB,6BAA6B;gBAC7B,oCAAoC;aACrC;YACD,CAAC,6BAAU,CAAC,eAAe,CAAC,EAAE;gBAC5B,6BAA6B;gBAC7B,mCAAmC;gBACnC,gCAAgC;aACjC;YACD,CAAC,6BAAU,CAAC,cAAc,CAAC,EAAE;gBAC3B,gCAAgC;gBAChC,qCAAqC;gBACrC,6BAA6B;aAC9B;YACD,CAAC,6BAAU,CAAC,eAAe,CAAC,EAAE;gBAC5B,yCAAyC;gBACzC,sBAAsB;gBACtB,6BAA6B;aAC9B;YACD,CAAC,6BAAU,CAAC,kBAAkB,CAAC,EAAE;gBAC/B,sCAAsC;gBACtC,6BAA6B;gBAC7B,2BAA2B;aAC5B;SACF,CAAC;QAEF,OAAO,aAAa,CAAC,UAAU,CAAC,IAAI;YAClC,0CAA0C;YAC1C,oCAAoC;YACpC,4BAA4B;SAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,2BAA2B,CAAC,OAAoC;QACrE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxD,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,6CAA6C;QAC7C,qBAAqB,CAAC,8BAA8B,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;IAC/F,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,8BAA8B,CAAC,UAAsB,EAAE,UAA+B;QACnG,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,6BAAU,CAAC,QAAQ;gBACtB,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;oBAC1B,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;gBAC1E,CAAC;gBACD,MAAM;YACR,KAAK,6BAAU,CAAC,YAAY;gBAC1B,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;oBACvB,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;gBAC1E,CAAC;gBACD,MAAM;YACR,KAAK,6BAAU,CAAC,eAAe;gBAC7B,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;oBAClD,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;gBAC7F,CAAC;gBACD,MAAM;YACR,KAAK,6BAAU,CAAC,eAAe;gBAC7B,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACjD,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;gBACxF,CAAC;gBACD,MAAM;YACR,KAAK,6BAAU,CAAC,UAAU;gBACxB,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBAClD,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;gBAC1F,CAAC;gBACD,MAAM;YACR,iCAAiC;QACnC,CAAC;IACH,CAAC;CACF;AArhBD,sDAqhBC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\response-action.factory.ts"], "sourcesContent": ["import { ResponseAction, ResponseActionProps } from '../entities/response-action.entity';\r\nimport { UniqueEntityId } from '../../../../shared-kernel';\r\nimport { ActionType, ActionTypeUtils } from '../enums/action-type.enum';\r\nimport { ActionStatus } from '../enums/action-status.enum';\r\n\r\n/**\r\n * Response Action Creation Options\r\n */\r\nexport interface CreateResponseActionOptions {\r\n  /** Action ID (optional, will be generated if not provided) */\r\n  id?: UniqueEntityId;\r\n  /** Type of action to create */\r\n  actionType: ActionType;\r\n  /** Action title */\r\n  title: string;\r\n  /** Action description */\r\n  description?: string;\r\n  /** Action parameters */\r\n  parameters: Record<string, any>;\r\n  /** Target information */\r\n  target?: {\r\n    type: 'event' | 'threat' | 'vulnerability' | 'system' | 'user' | 'network' | 'custom';\r\n    id: string;\r\n    name?: string;\r\n    metadata?: Record<string, any>;\r\n  };\r\n  /** Priority level */\r\n  priority?: 'low' | 'normal' | 'high' | 'critical';\r\n  /** Whether the action is automated */\r\n  isAutomated?: boolean;\r\n  /** Whether the action is reversible */\r\n  isReversible?: boolean;\r\n  /** Estimated duration in minutes */\r\n  estimatedDurationMinutes?: number;\r\n  /** Required permissions */\r\n  requiredPermissions?: string[];\r\n  /** Whether approval is required */\r\n  approvalRequired?: boolean;\r\n  /** Approval level required */\r\n  approvalLevel?: 'analyst' | 'senior' | 'manager' | 'director';\r\n  /** Success criteria */\r\n  successCriteria?: string[];\r\n  /** Maximum retry attempts */\r\n  maxRetries?: number;\r\n  /** Retry delay in minutes */\r\n  retryDelayMinutes?: number;\r\n  /** Rollback information */\r\n  rollbackInfo?: {\r\n    canRollback: boolean;\r\n    rollbackSteps: string[];\r\n    rollbackData?: Record<string, any>;\r\n  };\r\n  /** Initial tags */\r\n  tags?: string[];\r\n  /** Initial metadata */\r\n  metadata?: Record<string, any>;\r\n  /** Related event ID */\r\n  relatedEventId?: UniqueEntityId;\r\n  /** Related threat ID */\r\n  relatedThreatId?: UniqueEntityId;\r\n  /** Related vulnerability ID */\r\n  relatedVulnerabilityId?: UniqueEntityId;\r\n  /** Parent action ID */\r\n  parentActionId?: UniqueEntityId;\r\n  /** Correlation ID */\r\n  correlationId?: string;\r\n  /** Timeout in minutes */\r\n  timeoutMinutes?: number;\r\n  /** Initial status */\r\n  status?: ActionStatus;\r\n  /** Scheduled execution time */\r\n  scheduledAt?: Date;\r\n}\r\n\r\n/**\r\n * Response Action Factory\r\n * \r\n * Factory class for creating ResponseAction entities with proper validation and defaults.\r\n * Handles complex action creation scenarios and ensures all business rules are applied.\r\n * \r\n * Key responsibilities:\r\n * - Create actions from various input formats\r\n * - Apply default values based on action type\r\n * - Validate action configuration\r\n * - Set up proper permissions and approval requirements\r\n * - Configure retry and rollback settings\r\n * - Generate appropriate success criteria\r\n */\r\nexport class ResponseActionFactory {\r\n  /**\r\n   * Create a new ResponseAction with the provided options\r\n   */\r\n  static create(options: CreateResponseActionOptions): ResponseAction {\r\n    // Validate the action configuration\r\n    ResponseActionFactory.validateActionConfiguration(options);\r\n    \r\n    // Apply defaults based on action type\r\n    const defaults = ResponseActionFactory.getActionDefaults(options.actionType);\r\n    \r\n    // Build action properties\r\n    const actionProps: ResponseActionProps = {\r\n      actionType: options.actionType,\r\n      status: options.status || ActionStatus.PENDING,\r\n      title: options.title,\r\n      description: options.description,\r\n      parameters: options.parameters,\r\n      target: options.target,\r\n      priority: options.priority || defaults.priority,\r\n      isAutomated: options.isAutomated ?? defaults.isAutomated,\r\n      isReversible: options.isReversible ?? defaults.isReversible,\r\n      estimatedDurationMinutes: options.estimatedDurationMinutes || defaults.estimatedDurationMinutes,\r\n      actualDurationMinutes: undefined,\r\n      requiredPermissions: options.requiredPermissions || defaults.requiredPermissions,\r\n      approvalRequired: options.approvalRequired ?? defaults.approvalRequired,\r\n      approvalLevel: options.approvalLevel || defaults.approvalLevel,\r\n      approvedBy: undefined,\r\n      approvedAt: undefined,\r\n      scheduledBy: undefined,\r\n      scheduledAt: options.scheduledAt,\r\n      executedBy: undefined,\r\n      executedAt: undefined,\r\n      executionResults: undefined,\r\n      successCriteria: options.successCriteria || defaults.successCriteria,\r\n      successCriteriaMet: undefined,\r\n      executionError: undefined,\r\n      retryCount: 0,\r\n      maxRetries: options.maxRetries || defaults.maxRetries,\r\n      retryDelayMinutes: options.retryDelayMinutes || defaults.retryDelayMinutes,\r\n      nextRetryAt: undefined,\r\n      rollbackInfo: options.rollbackInfo || defaults.rollbackInfo,\r\n      rolledBack: false,\r\n      rollbackDetails: undefined,\r\n      tags: options.tags || defaults.tags,\r\n      metadata: options.metadata || {},\r\n      relatedEventId: options.relatedEventId,\r\n      relatedThreatId: options.relatedThreatId,\r\n      relatedVulnerabilityId: options.relatedVulnerabilityId,\r\n      parentActionId: options.parentActionId,\r\n      childActionIds: [],\r\n      correlationId: options.correlationId,\r\n      timeoutMinutes: options.timeoutMinutes || defaults.timeoutMinutes,\r\n      timedOut: false,\r\n      timedOutAt: undefined,\r\n    };\r\n\r\n    return ResponseAction.create(actionProps, options.id);\r\n  }\r\n\r\n  /**\r\n   * Create a containment action\r\n   */\r\n  static createContainmentAction(\r\n    actionType: ActionType,\r\n    title: string,\r\n    target: CreateResponseActionOptions['target'],\r\n    parameters: Record<string, any>,\r\n    options?: Partial<CreateResponseActionOptions>\r\n  ): ResponseAction {\r\n    if (!ActionTypeUtils.getContainmentActionTypes().includes(actionType)) {\r\n      throw new Error(`${actionType} is not a containment action type`);\r\n    }\r\n\r\n    return ResponseActionFactory.create({\r\n      actionType,\r\n      title,\r\n      target,\r\n      parameters,\r\n      priority: 'high',\r\n      isAutomated: ActionTypeUtils.isAutomated(actionType),\r\n      approvalRequired: ActionTypeUtils.isHighRisk(actionType),\r\n      tags: ['containment', 'security'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an eradication action\r\n   */\r\n  static createEradicationAction(\r\n    actionType: ActionType,\r\n    title: string,\r\n    target: CreateResponseActionOptions['target'],\r\n    parameters: Record<string, any>,\r\n    options?: Partial<CreateResponseActionOptions>\r\n  ): ResponseAction {\r\n    if (!ActionTypeUtils.getEradicationActionTypes().includes(actionType)) {\r\n      throw new Error(`${actionType} is not an eradication action type`);\r\n    }\r\n\r\n    return ResponseActionFactory.create({\r\n      actionType,\r\n      title,\r\n      target,\r\n      parameters,\r\n      priority: 'high',\r\n      isAutomated: ActionTypeUtils.isAutomated(actionType),\r\n      approvalRequired: true, // Eradication actions typically require approval\r\n      tags: ['eradication', 'security'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a recovery action\r\n   */\r\n  static createRecoveryAction(\r\n    actionType: ActionType,\r\n    title: string,\r\n    target: CreateResponseActionOptions['target'],\r\n    parameters: Record<string, any>,\r\n    options?: Partial<CreateResponseActionOptions>\r\n  ): ResponseAction {\r\n    if (!ActionTypeUtils.getRecoveryActionTypes().includes(actionType)) {\r\n      throw new Error(`${actionType} is not a recovery action type`);\r\n    }\r\n\r\n    return ResponseActionFactory.create({\r\n      actionType,\r\n      title,\r\n      target,\r\n      parameters,\r\n      priority: 'normal',\r\n      isAutomated: ActionTypeUtils.isAutomated(actionType),\r\n      approvalRequired: ActionTypeUtils.isHighRisk(actionType),\r\n      tags: ['recovery', 'restoration'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a notification action\r\n   */\r\n  static createNotificationAction(\r\n    actionType: ActionType,\r\n    title: string,\r\n    parameters: Record<string, any>,\r\n    options?: Partial<CreateResponseActionOptions>\r\n  ): ResponseAction {\r\n    if (!ActionTypeUtils.getNotificationActionTypes().includes(actionType)) {\r\n      throw new Error(`${actionType} is not a notification action type`);\r\n    }\r\n\r\n    return ResponseActionFactory.create({\r\n      actionType,\r\n      title,\r\n      parameters,\r\n      priority: 'normal',\r\n      isAutomated: true,\r\n      approvalRequired: false,\r\n      tags: ['notification', 'communication'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an automated action\r\n   */\r\n  static createAutomatedAction(\r\n    actionType: ActionType,\r\n    title: string,\r\n    parameters: Record<string, any>,\r\n    options?: Partial<CreateResponseActionOptions>\r\n  ): ResponseAction {\r\n    if (!ActionTypeUtils.isAutomated(actionType)) {\r\n      throw new Error(`${actionType} is not an automated action type`);\r\n    }\r\n\r\n    return ResponseActionFactory.create({\r\n      actionType,\r\n      title,\r\n      parameters,\r\n      isAutomated: true,\r\n      approvalRequired: ActionTypeUtils.isHighRisk(actionType),\r\n      status: ActionStatus.APPROVED, // Auto-approve if no approval required\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a manual action\r\n   */\r\n  static createManualAction(\r\n    actionType: ActionType,\r\n    title: string,\r\n    parameters: Record<string, any>,\r\n    options?: Partial<CreateResponseActionOptions>\r\n  ): ResponseAction {\r\n    if (!ActionTypeUtils.isManual(actionType)) {\r\n      throw new Error(`${actionType} is not a manual action type`);\r\n    }\r\n\r\n    return ResponseActionFactory.create({\r\n      actionType,\r\n      title,\r\n      parameters,\r\n      isAutomated: false,\r\n      approvalRequired: true,\r\n      tags: ['manual', 'human-required'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a high-priority action\r\n   */\r\n  static createHighPriorityAction(\r\n    actionType: ActionType,\r\n    title: string,\r\n    parameters: Record<string, any>,\r\n    options?: Partial<CreateResponseActionOptions>\r\n  ): ResponseAction {\r\n    return ResponseActionFactory.create({\r\n      actionType,\r\n      title,\r\n      parameters,\r\n      priority: 'high',\r\n      tags: ['high-priority', 'urgent'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a critical action\r\n   */\r\n  static createCriticalAction(\r\n    actionType: ActionType,\r\n    title: string,\r\n    parameters: Record<string, any>,\r\n    options?: Partial<CreateResponseActionOptions>\r\n  ): ResponseAction {\r\n    return ResponseActionFactory.create({\r\n      actionType,\r\n      title,\r\n      parameters,\r\n      priority: 'critical',\r\n      approvalRequired: true,\r\n      approvalLevel: 'manager',\r\n      tags: ['critical', 'emergency'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a chained action (child of another action)\r\n   */\r\n  static createChainedAction(\r\n    parentAction: ResponseAction,\r\n    actionType: ActionType,\r\n    title: string,\r\n    parameters: Record<string, any>,\r\n    options?: Partial<CreateResponseActionOptions>\r\n  ): ResponseAction {\r\n    const childAction = ResponseActionFactory.create({\r\n      actionType,\r\n      title,\r\n      parameters,\r\n      parentActionId: parentAction.id,\r\n      correlationId: parentAction.correlationId || parentAction.id.toString(),\r\n      priority: parentAction.priority, // Inherit priority from parent\r\n      tags: [...parentAction.tags, 'chained'],\r\n      ...options,\r\n    });\r\n\r\n    // Add child to parent\r\n    parentAction.addChildAction(childAction.id);\r\n\r\n    return childAction;\r\n  }\r\n\r\n  /**\r\n   * Create an action from event response\r\n   */\r\n  static fromEventResponse(\r\n    eventId: UniqueEntityId,\r\n    actionType: ActionType,\r\n    title: string,\r\n    parameters: Record<string, any>,\r\n    options?: Partial<CreateResponseActionOptions>\r\n  ): ResponseAction {\r\n    return ResponseActionFactory.create({\r\n      actionType,\r\n      title,\r\n      parameters,\r\n      relatedEventId: eventId,\r\n      target: {\r\n        type: 'event',\r\n        id: eventId.toString(),\r\n      },\r\n      tags: ['event-response', 'automated-response'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an action from threat response\r\n   */\r\n  static fromThreatResponse(\r\n    threatId: UniqueEntityId,\r\n    actionType: ActionType,\r\n    title: string,\r\n    parameters: Record<string, any>,\r\n    options?: Partial<CreateResponseActionOptions>\r\n  ): ResponseAction {\r\n    return ResponseActionFactory.create({\r\n      actionType,\r\n      title,\r\n      parameters,\r\n      relatedThreatId: threatId,\r\n      target: {\r\n        type: 'threat',\r\n        id: threatId.toString(),\r\n      },\r\n      priority: 'high', // Threat responses are typically high priority\r\n      tags: ['threat-response', 'security'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an action from vulnerability response\r\n   */\r\n  static fromVulnerabilityResponse(\r\n    vulnerabilityId: UniqueEntityId,\r\n    actionType: ActionType,\r\n    title: string,\r\n    parameters: Record<string, any>,\r\n    options?: Partial<CreateResponseActionOptions>\r\n  ): ResponseAction {\r\n    return ResponseActionFactory.create({\r\n      actionType,\r\n      title,\r\n      parameters,\r\n      relatedVulnerabilityId: vulnerabilityId,\r\n      target: {\r\n        type: 'vulnerability',\r\n        id: vulnerabilityId.toString(),\r\n      },\r\n      tags: ['vulnerability-response', 'remediation'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get default values for an action type\r\n   */\r\n  private static getActionDefaults(actionType: ActionType): {\r\n    priority: 'low' | 'normal' | 'high' | 'critical';\r\n    isAutomated: boolean;\r\n    isReversible: boolean;\r\n    estimatedDurationMinutes: number;\r\n    requiredPermissions: string[];\r\n    approvalRequired: boolean;\r\n    approvalLevel?: 'analyst' | 'senior' | 'manager' | 'director';\r\n    successCriteria: string[];\r\n    maxRetries: number;\r\n    retryDelayMinutes: number;\r\n    rollbackInfo?: {\r\n      canRollback: boolean;\r\n      rollbackSteps: string[];\r\n      rollbackData?: Record<string, any>;\r\n    };\r\n    tags: string[];\r\n    timeoutMinutes: number;\r\n  } {\r\n    const isAutomated = ActionTypeUtils.isAutomated(actionType);\r\n    const isHighRisk = ActionTypeUtils.isHighRisk(actionType);\r\n    const isReversible = ActionTypeUtils.isReversible(actionType);\r\n    const estimatedTime = ActionTypeUtils.getExecutionTimeEstimate(actionType);\r\n    const requiredPermissions = ActionTypeUtils.getRequiredPermissions(actionType);\r\n    const approvalReqs = ActionTypeUtils.getApprovalRequirements(actionType);\r\n    const successCriteria = ActionTypeUtils.getSuccessCriteria(actionType);\r\n\r\n    // Determine priority based on action characteristics\r\n    let priority: 'low' | 'normal' | 'high' | 'critical' = 'normal';\r\n    if (ActionTypeUtils.getContainmentActionTypes().includes(actionType)) {\r\n      priority = 'high';\r\n    } else if (ActionTypeUtils.getEradicationActionTypes().includes(actionType)) {\r\n      priority = 'high';\r\n    } else if (isHighRisk) {\r\n      priority = 'high';\r\n    }\r\n\r\n    // Set up rollback info for reversible actions\r\n    let rollbackInfo: typeof rollbackInfo = undefined;\r\n    if (isReversible) {\r\n      rollbackInfo = {\r\n        canRollback: true,\r\n        rollbackSteps: ResponseActionFactory.generateRollbackSteps(actionType),\r\n      };\r\n    }\r\n\r\n    // Generate tags based on action characteristics\r\n    const tags: string[] = [];\r\n    if (isAutomated) tags.push('automated');\r\n    if (!isAutomated) tags.push('manual');\r\n    if (isHighRisk) tags.push('high-risk');\r\n    if (isReversible) tags.push('reversible');\r\n    if (ActionTypeUtils.getContainmentActionTypes().includes(actionType)) tags.push('containment');\r\n    if (ActionTypeUtils.getEradicationActionTypes().includes(actionType)) tags.push('eradication');\r\n    if (ActionTypeUtils.getRecoveryActionTypes().includes(actionType)) tags.push('recovery');\r\n    if (ActionTypeUtils.getNotificationActionTypes().includes(actionType)) tags.push('notification');\r\n\r\n    return {\r\n      priority,\r\n      isAutomated,\r\n      isReversible,\r\n      estimatedDurationMinutes: estimatedTime,\r\n      requiredPermissions,\r\n      approvalRequired: approvalReqs.required,\r\n      approvalLevel: approvalReqs.level !== 'none' ? approvalReqs.level : undefined,\r\n      successCriteria,\r\n      maxRetries: isAutomated ? 3 : 1,\r\n      retryDelayMinutes: 5,\r\n      rollbackInfo,\r\n      tags,\r\n      timeoutMinutes: Math.max(estimatedTime * 2, 30), // Timeout is 2x estimated time, minimum 30 minutes\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate rollback steps for an action type\r\n   */\r\n  private static generateRollbackSteps(actionType: ActionType): string[] {\r\n    const rollbackSteps: Record<ActionType, string[]> = {\r\n      [ActionType.BLOCK_IP]: [\r\n        'Remove IP from block list',\r\n        'Update firewall rules',\r\n        'Verify IP traffic is allowed',\r\n      ],\r\n      [ActionType.BLOCK_DOMAIN]: [\r\n        'Remove domain from block list',\r\n        'Update DNS filtering rules',\r\n        'Verify domain access is restored',\r\n      ],\r\n      [ActionType.DISABLE_ACCOUNT]: [\r\n        'Re-enable user account',\r\n        'Restore account permissions',\r\n        'Notify user of account restoration',\r\n      ],\r\n      [ActionType.QUARANTINE_FILE]: [\r\n        'Remove file from quarantine',\r\n        'Restore file to original location',\r\n        'Update file access permissions',\r\n      ],\r\n      [ActionType.ISOLATE_SYSTEM]: [\r\n        'Remove network isolation rules',\r\n        'Restore system network connectivity',\r\n        'Verify system communication',\r\n      ],\r\n      [ActionType.UPDATE_FIREWALL]: [\r\n        'Restore previous firewall configuration',\r\n        'Apply rollback rules',\r\n        'Verify network connectivity',\r\n      ],\r\n      [ActionType.CHANGE_PERMISSIONS]: [\r\n        'Restore previous permission settings',\r\n        'Update access control lists',\r\n        'Verify permission changes',\r\n      ],\r\n    };\r\n\r\n    return rollbackSteps[actionType] || [\r\n      'Identify changes made by original action',\r\n      'Reverse the changes systematically',\r\n      'Verify rollback completion',\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Validate action configuration\r\n   */\r\n  static validateActionConfiguration(options: CreateResponseActionOptions): void {\r\n    if (!options.actionType) {\r\n      throw new Error('Action type is required');\r\n    }\r\n\r\n    if (!options.title || options.title.trim().length === 0) {\r\n      throw new Error('Action title is required');\r\n    }\r\n\r\n    if (!options.parameters) {\r\n      throw new Error('Action parameters are required');\r\n    }\r\n\r\n    // Validate action type specific requirements\r\n    ResponseActionFactory.validateActionTypeRequirements(options.actionType, options.parameters);\r\n  }\r\n\r\n  /**\r\n   * Validate action type specific requirements\r\n   */\r\n  private static validateActionTypeRequirements(actionType: ActionType, parameters: Record<string, any>): void {\r\n    switch (actionType) {\r\n      case ActionType.BLOCK_IP:\r\n        if (!parameters.ipAddress) {\r\n          throw new Error('IP address parameter is required for BLOCK_IP action');\r\n        }\r\n        break;\r\n      case ActionType.BLOCK_DOMAIN:\r\n        if (!parameters.domain) {\r\n          throw new Error('Domain parameter is required for BLOCK_DOMAIN action');\r\n        }\r\n        break;\r\n      case ActionType.DISABLE_ACCOUNT:\r\n        if (!parameters.accountId && !parameters.username) {\r\n          throw new Error('Account ID or username parameter is required for DISABLE_ACCOUNT action');\r\n        }\r\n        break;\r\n      case ActionType.QUARANTINE_FILE:\r\n        if (!parameters.filePath && !parameters.fileHash) {\r\n          throw new Error('File path or hash parameter is required for QUARANTINE_FILE action');\r\n        }\r\n        break;\r\n      case ActionType.SEND_EMAIL:\r\n        if (!parameters.recipients || !parameters.subject) {\r\n          throw new Error('Recipients and subject parameters are required for SEND_EMAIL action');\r\n        }\r\n        break;\r\n      // Add more validations as needed\r\n    }\r\n  }\r\n}"], "version": 3}