{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\incident-response\\domain\\entities\\incident-evidence.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,uDAA6C;AAE7C;;;GAGG;AAOI,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IA8P3B;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,cAAc,EAAE,iBAAiB,IAAI,KAAK,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,SAAS,EAAE,SAAS,IAAI,KAAK,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;QACpC,IAAI,CAAC,IAAI;YAAE,OAAO,SAAS,CAAC;QAE5B,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,QAAQ,GAAG,IAAI,CAAC;QAEpB,OAAO,QAAQ,IAAI,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,QAAQ,IAAI,IAAI,CAAC;YACjB,SAAS,EAAE,CAAC;QACd,CAAC;QAED,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC1D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,sBAAsB,CACpB,MAAsF,EACtF,MAAc,EACd,UAMI,EAAE;QAEN,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACvB,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,MAAM;YACN,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,SAAiB,iBAAiB;QAChD,qDAAqD;QACrD,sCAAsC;QAEtC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,GAAG;gBACpB,iBAAiB,EAAE,KAAK;gBACxB,YAAY,EAAE,CAAC;gBACf,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,CAAC;aACf,CAAC;QACJ,CAAC;QAED,2BAA2B;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,KAAK,SAAS,CAAC;QAE3D,IAAI,CAAC,cAAc,CAAC,iBAAiB,GAAG,OAAO,CAAC;QAChD,IAAI,CAAC,cAAc,CAAC,eAAe,GAAG,MAAM,CAAC;QAC7C,IAAI,CAAC,cAAc,CAAC,kBAAkB,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAElE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;YAC3C,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC;gBAC9B,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,0CAA0C;gBACvD,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,4CAA4C;aACrD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,YAAkD,QAAQ;QACtE,+CAA+C;QAC/C,yCAAyC;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QACxC,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,SAAS,EAAE,CAAC;QAEpD,mCAAmC;QACnC,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,KAAK;gBACR,OAAO,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;YACpC,KAAK,MAAM;gBACT,OAAO,QAAQ,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;YACrC,KAAK,QAAQ;gBACX,OAAO,UAAU,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;YACvC,KAAK,QAAQ;gBACX,OAAO,UAAU,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;YACxC;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY;QACV,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACvD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC3D,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,YAAY,CACV,MAAc,EACd,MAA+C,EAC/C,OAAe,EACf,WAAoB,IAAI,EACxB,UAAmB;QAEnB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG;gBACf,SAAS,EAAE,KAAK;gBAChB,qBAAqB,EAAE,UAAU;aAClC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YAC9B,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC;YAC5B,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAAc,EAAE,KAAa;QACxC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG;gBACf,SAAS,EAAE,KAAK;gBAChB,qBAAqB,EAAE,UAAU;aAClC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,MAAM,CAAC;QACxC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;QACnC,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAExD,6BAA6B;QAC7B,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,KAAK,EAAE;YAChD,OAAO,EAAE,oBAAoB;YAC7B,KAAK,EAAE,MAAM;SACd,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,UAAkB,EAAE,MAAe;QAClD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,KAAK,CAAC;YAEjC,6BAA6B;YAC7B,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,UAAU,EAAE;gBACrD,OAAO,EAAE,qBAAqB;gBAC9B,KAAK,EAAE,MAAM,IAAI,+BAA+B;aACjD,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,UAAkB,EAAE,QAAiB;QAC3C,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QAEzB,sBAAsB;QACtB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,IAAI,CAAC,WAAW,GAAG;gBACjB,WAAW,EAAE,OAAO;gBACpB,WAAW,EAAE,UAAU;gBACvB,SAAS,EAAE,IAAI;aAChB,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;QAC1C,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,UAAU,EAAE;YAClD,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW;YACtC,OAAO,EAAE,mBAAmB;SAC7B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,0BAA0B;QAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACxC,CAAC;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC3C,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACrD,CAAC;QAED,OAAO;YACL,UAAU,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC/B,MAAM;SACP,CAAC;IACJ,CAAC;CACF,CAAA;AAxgBY,4CAAgB;AAE3B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;4CACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;8CACX;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;qDACL;AAuBpB;IAlBC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,UAAU;YACV,aAAa;YACb,YAAY;YACZ,iBAAiB;YACjB,YAAY;YACZ,UAAU;YACV,OAAO;YACP,iBAAiB;YACjB,oBAAoB;YACpB,iBAAiB;YACjB,gBAAgB;YAChB,mBAAmB;YACnB,OAAO;SACR;KACF,CAAC;;8CACW;AAUb;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC;QACpF,OAAO,EAAE,YAAY;KACtB,CAAC;;gDACwF;AAM1F;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;kDA2ExB;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;kDACpC,KAAK,oBAAL,KAAK;wDASlB;AAMH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAU9D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAyB5D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wDAsBjE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;qDAAC;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;qDAC3B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;8CACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CAC1B;AAGf;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;mDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;mDAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,0BAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;IACjF,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;kDAC1B,0BAAQ,oBAAR,0BAAQ;kDAAC;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;oDAC3B;2BA5PR,gBAAgB;IAN5B,IAAA,gBAAM,EAAC,mBAAmB,CAAC;IAC3B,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;GACV,gBAAgB,CAwgB5B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\incident-response\\domain\\entities\\incident-evidence.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { Incident } from './incident.entity';\r\n\r\n/**\r\n * Incident Evidence entity\r\n * Represents digital evidence collected during incident response\r\n */\r\n@Entity('incident_evidence')\r\n@Index(['incidentId'])\r\n@Index(['type'])\r\n@Index(['status'])\r\n@Index(['collectedBy'])\r\n@Index(['collectedAt'])\r\nexport class IncidentEvidence {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Evidence name/title\r\n   */\r\n  @Column({ length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Evidence description\r\n   */\r\n  @Column({ type: 'text' })\r\n  description: string;\r\n\r\n  /**\r\n   * Evidence type\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'log_file',\r\n      'memory_dump',\r\n      'disk_image',\r\n      'network_capture',\r\n      'screenshot',\r\n      'document',\r\n      'email',\r\n      'database_export',\r\n      'configuration_file',\r\n      'registry_export',\r\n      'malware_sample',\r\n      'forensic_artifact',\r\n      'other',\r\n    ],\r\n  })\r\n  type: string;\r\n\r\n  /**\r\n   * Evidence status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['collecting', 'collected', 'processing', 'analyzed', 'archived', 'corrupted'],\r\n    default: 'collecting',\r\n  })\r\n  status: 'collecting' | 'collected' | 'processing' | 'analyzed' | 'archived' | 'corrupted';\r\n\r\n  /**\r\n   * Evidence metadata and properties\r\n   */\r\n  @Column({ type: 'jsonb' })\r\n  metadata: {\r\n    // File information\r\n    fileName?: string;\r\n    fileSize?: number; // bytes\r\n    filePath?: string;\r\n    mimeType?: string;\r\n    \r\n    // Hash values for integrity\r\n    hashes?: {\r\n      md5?: string;\r\n      sha1?: string;\r\n      sha256?: string;\r\n      sha512?: string;\r\n    };\r\n    \r\n    // Source information\r\n    sourceSystem?: {\r\n      hostname?: string;\r\n      ipAddress?: string;\r\n      operatingSystem?: string;\r\n      assetId?: string;\r\n    };\r\n    \r\n    // Collection details\r\n    collectionMethod?: string;\r\n    collectionTool?: string;\r\n    collectionVersion?: string;\r\n    collectionParameters?: Record<string, any>;\r\n    \r\n    // Timestamps\r\n    originalTimestamp?: string;\r\n    modifiedTimestamp?: string;\r\n    accessedTimestamp?: string;\r\n    \r\n    // Forensic information\r\n    forensicImage?: {\r\n      imageType: 'dd' | 'e01' | 'aff' | 'vmdk' | 'other';\r\n      compressionUsed: boolean;\r\n      encryptionUsed: boolean;\r\n      verificationHash: string;\r\n    };\r\n    \r\n    // Network capture details\r\n    networkCapture?: {\r\n      interface: string;\r\n      captureFilter?: string;\r\n      packetCount?: number;\r\n      duration?: number; // seconds\r\n      protocols?: string[];\r\n    };\r\n    \r\n    // Log file details\r\n    logFile?: {\r\n      logType: string;\r\n      timeRange: {\r\n        start: string;\r\n        end: string;\r\n      };\r\n      entryCount?: number;\r\n      format?: string;\r\n    };\r\n    \r\n    // Analysis results\r\n    analysis?: {\r\n      malwareDetected?: boolean;\r\n      suspiciousActivity?: boolean;\r\n      iocs?: Array<{\r\n        type: string;\r\n        value: string;\r\n        confidence: 'low' | 'medium' | 'high';\r\n      }>;\r\n      findings?: string[];\r\n      recommendations?: string[];\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Chain of custody tracking\r\n   */\r\n  @Column({ name: 'chain_of_custody', type: 'jsonb' })\r\n  chainOfCustody: Array<{\r\n    action: 'collected' | 'transferred' | 'analyzed' | 'copied' | 'archived' | 'destroyed';\r\n    timestamp: string;\r\n    userId: string;\r\n    location?: string;\r\n    purpose?: string;\r\n    notes?: string;\r\n    signature?: string;\r\n    witness?: string;\r\n  }>;\r\n\r\n  /**\r\n   * Storage information\r\n   */\r\n  @Column({ name: 'storage_info', type: 'jsonb', nullable: true })\r\n  storageInfo?: {\r\n    storageType: 'local' | 'cloud' | 'network' | 'removable';\r\n    storagePath: string;\r\n    encrypted: boolean;\r\n    encryptionMethod?: string;\r\n    compressionUsed?: boolean;\r\n    backupLocation?: string;\r\n    retentionPeriod?: number; // days\r\n    accessRestrictions?: string[];\r\n  };\r\n\r\n  /**\r\n   * Legal and compliance information\r\n   */\r\n  @Column({ name: 'legal_info', type: 'jsonb', nullable: true })\r\n  legalInfo?: {\r\n    legalHold: boolean;\r\n    legalHoldReason?: string;\r\n    legalHoldBy?: string;\r\n    legalHoldDate?: string;\r\n    \r\n    privacyClassification?: 'public' | 'internal' | 'confidential' | 'restricted';\r\n    dataClassification?: string[];\r\n    \r\n    complianceRequirements?: string[];\r\n    retentionRequirements?: {\r\n      minimumDays: number;\r\n      maximumDays?: number;\r\n      reason: string;\r\n    };\r\n    \r\n    accessLog?: Array<{\r\n      userId: string;\r\n      timestamp: string;\r\n      action: 'view' | 'download' | 'copy' | 'modify';\r\n      purpose: string;\r\n      approved: boolean;\r\n      approvedBy?: string;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Quality and integrity metrics\r\n   */\r\n  @Column({ name: 'quality_metrics', type: 'jsonb', nullable: true })\r\n  qualityMetrics?: {\r\n    integrityVerified: boolean;\r\n    integrityMethod?: string;\r\n    integrityTimestamp?: string;\r\n    \r\n    completeness: number; // 0-1 scale\r\n    accuracy: number; // 0-1 scale\r\n    reliability: number; // 0-1 scale\r\n    \r\n    issues?: Array<{\r\n      type: 'corruption' | 'incomplete' | 'timestamp_mismatch' | 'hash_mismatch' | 'other';\r\n      description: string;\r\n      severity: 'low' | 'medium' | 'high';\r\n      impact: string;\r\n    }>;\r\n    \r\n    validationResults?: Array<{\r\n      test: string;\r\n      result: 'pass' | 'fail' | 'warning';\r\n      details?: string;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * When evidence was collected\r\n   */\r\n  @Column({ name: 'collected_at', type: 'timestamp with time zone' })\r\n  collectedAt: Date;\r\n\r\n  /**\r\n   * User who collected the evidence\r\n   */\r\n  @Column({ name: 'collected_by', type: 'uuid' })\r\n  collectedBy: string;\r\n\r\n  /**\r\n   * Evidence tags for categorization\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * Evidence notes and comments\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  notes?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => Incident, incident => incident.evidence, { onDelete: 'CASCADE' })\r\n  @JoinColumn({ name: 'incident_id' })\r\n  incident: Incident;\r\n\r\n  @Column({ name: 'incident_id', type: 'uuid' })\r\n  incidentId: string;\r\n\r\n  /**\r\n   * Check if evidence integrity is verified\r\n   */\r\n  get isIntegrityVerified(): boolean {\r\n    return this.qualityMetrics?.integrityVerified || false;\r\n  }\r\n\r\n  /**\r\n   * Check if evidence is under legal hold\r\n   */\r\n  get isUnderLegalHold(): boolean {\r\n    return this.legalInfo?.legalHold || false;\r\n  }\r\n\r\n  /**\r\n   * Get evidence size in human-readable format\r\n   */\r\n  get fileSizeFormatted(): string {\r\n    const size = this.metadata.fileSize;\r\n    if (!size) return 'Unknown';\r\n    \r\n    const units = ['B', 'KB', 'MB', 'GB', 'TB'];\r\n    let unitIndex = 0;\r\n    let fileSize = size;\r\n    \r\n    while (fileSize >= 1024 && unitIndex < units.length - 1) {\r\n      fileSize /= 1024;\r\n      unitIndex++;\r\n    }\r\n    \r\n    return `${fileSize.toFixed(1)} ${units[unitIndex]}`;\r\n  }\r\n\r\n  /**\r\n   * Get evidence age in days\r\n   */\r\n  get ageInDays(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.collectedAt.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Add chain of custody entry\r\n   */\r\n  addChainOfCustodyEntry(\r\n    action: 'collected' | 'transferred' | 'analyzed' | 'copied' | 'archived' | 'destroyed',\r\n    userId: string,\r\n    options: {\r\n      location?: string;\r\n      purpose?: string;\r\n      notes?: string;\r\n      signature?: string;\r\n      witness?: string;\r\n    } = {},\r\n  ): void {\r\n    this.chainOfCustody.push({\r\n      action,\r\n      timestamp: new Date().toISOString(),\r\n      userId,\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Verify evidence integrity\r\n   */\r\n  verifyIntegrity(method: string = 'hash_comparison'): boolean {\r\n    // This would implement actual integrity verification\r\n    // For now, we'll simulate the process\r\n    \r\n    if (!this.qualityMetrics) {\r\n      this.qualityMetrics = {\r\n        integrityVerified: false,\r\n        completeness: 1,\r\n        accuracy: 1,\r\n        reliability: 1,\r\n      };\r\n    }\r\n    \r\n    // Simulate integrity check\r\n    const isValid = this.metadata.hashes?.sha256 !== undefined;\r\n    \r\n    this.qualityMetrics.integrityVerified = isValid;\r\n    this.qualityMetrics.integrityMethod = method;\r\n    this.qualityMetrics.integrityTimestamp = new Date().toISOString();\r\n    \r\n    if (!isValid && this.qualityMetrics.issues) {\r\n      this.qualityMetrics.issues.push({\r\n        type: 'hash_mismatch',\r\n        description: 'Evidence integrity could not be verified',\r\n        severity: 'high',\r\n        impact: 'Evidence may be corrupted or tampered with',\r\n      });\r\n    }\r\n    \r\n    return isValid;\r\n  }\r\n\r\n  /**\r\n   * Calculate evidence hash\r\n   */\r\n  calculateHash(algorithm: 'md5' | 'sha1' | 'sha256' | 'sha512' = 'sha256'): string {\r\n    // This would implement actual hash calculation\r\n    // For now, we'll return a simulated hash\r\n    const timestamp = Date.now().toString();\r\n    const data = `${this.id}-${this.name}-${timestamp}`;\r\n    \r\n    // Simulate hash based on algorithm\r\n    switch (algorithm) {\r\n      case 'md5':\r\n        return `md5-${data.slice(0, 32)}`;\r\n      case 'sha1':\r\n        return `sha1-${data.slice(0, 40)}`;\r\n      case 'sha256':\r\n        return `sha256-${data.slice(0, 64)}`;\r\n      case 'sha512':\r\n        return `sha512-${data.slice(0, 128)}`;\r\n      default:\r\n        return data;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update evidence hashes\r\n   */\r\n  updateHashes(): void {\r\n    if (!this.metadata.hashes) {\r\n      this.metadata.hashes = {};\r\n    }\r\n    \r\n    this.metadata.hashes.md5 = this.calculateHash('md5');\r\n    this.metadata.hashes.sha1 = this.calculateHash('sha1');\r\n    this.metadata.hashes.sha256 = this.calculateHash('sha256');\r\n    this.metadata.hashes.sha512 = this.calculateHash('sha512');\r\n  }\r\n\r\n  /**\r\n   * Add access log entry\r\n   */\r\n  addAccessLog(\r\n    userId: string,\r\n    action: 'view' | 'download' | 'copy' | 'modify',\r\n    purpose: string,\r\n    approved: boolean = true,\r\n    approvedBy?: string,\r\n  ): void {\r\n    if (!this.legalInfo) {\r\n      this.legalInfo = {\r\n        legalHold: false,\r\n        privacyClassification: 'internal',\r\n      };\r\n    }\r\n    \r\n    if (!this.legalInfo.accessLog) {\r\n      this.legalInfo.accessLog = [];\r\n    }\r\n    \r\n    this.legalInfo.accessLog.push({\r\n      userId,\r\n      timestamp: new Date().toISOString(),\r\n      action,\r\n      purpose,\r\n      approved,\r\n      approvedBy,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Set legal hold\r\n   */\r\n  setLegalHold(reason: string, setBy: string): void {\r\n    if (!this.legalInfo) {\r\n      this.legalInfo = {\r\n        legalHold: false,\r\n        privacyClassification: 'internal',\r\n      };\r\n    }\r\n    \r\n    this.legalInfo.legalHold = true;\r\n    this.legalInfo.legalHoldReason = reason;\r\n    this.legalInfo.legalHoldBy = setBy;\r\n    this.legalInfo.legalHoldDate = new Date().toISOString();\r\n    \r\n    // Add chain of custody entry\r\n    this.addChainOfCustodyEntry('transferred', setBy, {\r\n      purpose: 'Legal hold applied',\r\n      notes: reason,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Release legal hold\r\n   */\r\n  releaseLegalHold(releasedBy: string, reason?: string): void {\r\n    if (this.legalInfo) {\r\n      this.legalInfo.legalHold = false;\r\n      \r\n      // Add chain of custody entry\r\n      this.addChainOfCustodyEntry('transferred', releasedBy, {\r\n        purpose: 'Legal hold released',\r\n        notes: reason || 'Legal hold no longer required',\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Archive evidence\r\n   */\r\n  archive(archivedBy: string, location?: string): void {\r\n    this.status = 'archived';\r\n    \r\n    // Update storage info\r\n    if (!this.storageInfo) {\r\n      this.storageInfo = {\r\n        storageType: 'local',\r\n        storagePath: '/archive',\r\n        encrypted: true,\r\n      };\r\n    }\r\n    \r\n    if (location) {\r\n      this.storageInfo.storagePath = location;\r\n    }\r\n    \r\n    // Add chain of custody entry\r\n    this.addChainOfCustodyEntry('archived', archivedBy, {\r\n      location: this.storageInfo.storagePath,\r\n      purpose: 'Long-term storage',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Validate evidence completeness\r\n   */\r\n  validateCompleteness(): { isComplete: boolean; issues: string[] } {\r\n    const issues: string[] = [];\r\n    \r\n    // Check required metadata\r\n    if (!this.metadata.fileName) {\r\n      issues.push('File name is missing');\r\n    }\r\n    \r\n    if (!this.metadata.fileSize) {\r\n      issues.push('File size is missing');\r\n    }\r\n    \r\n    if (!this.metadata.hashes?.sha256) {\r\n      issues.push('SHA256 hash is missing');\r\n    }\r\n    \r\n    // Check chain of custody\r\n    if (this.chainOfCustody.length === 0) {\r\n      issues.push('Chain of custody is empty');\r\n    }\r\n    \r\n    // Check collection details\r\n    if (!this.metadata.collectionMethod) {\r\n      issues.push('Collection method is not documented');\r\n    }\r\n    \r\n    return {\r\n      isComplete: issues.length === 0,\r\n      issues,\r\n    };\r\n  }\r\n}\r\n"], "version": 3}