b331576b4fd29d85c3fa1581ba0c336e
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIModel = exports.HealthStatus = exports.ModelStatus = exports.ModelType = exports.AIProvider = void 0;
const base_aggregate_root_1 = require("../../../../shared-kernel/domain/base-aggregate-root");
const ai_model_created_domain_event_1 = require("../events/ai-model-created.domain-event");
const ai_model_status_changed_domain_event_1 = require("../events/ai-model-status-changed.domain-event");
const ai_model_performance_updated_domain_event_1 = require("../events/ai-model-performance-updated.domain-event");
const ai_model_configuration_updated_domain_event_1 = require("../events/ai-model-configuration-updated.domain-event");
var AIProvider;
(function (AIProvider) {
    AIProvider["OPENAI"] = "openai";
    AIProvider["BEDROCK"] = "bedrock";
    AIProvider["TENSORFLOW"] = "tensorflow";
    AIProvider["PYTHON_AI"] = "python_ai";
    AIProvider["HUGGING_FACE"] = "hugging_face";
    AIProvider["AZURE_OPENAI"] = "azure_openai";
    AIProvider["GOOGLE_AI"] = "google_ai";
    AIProvider["ANTHROPIC"] = "anthropic";
})(AIProvider || (exports.AIProvider = AIProvider = {}));
var ModelType;
(function (ModelType) {
    ModelType["LANGUAGE_MODEL"] = "language_model";
    ModelType["CLASSIFICATION"] = "classification";
    ModelType["REGRESSION"] = "regression";
    ModelType["CLUSTERING"] = "clustering";
    ModelType["ANOMALY_DETECTION"] = "anomaly_detection";
    ModelType["PATTERN_RECOGNITION"] = "pattern_recognition";
    ModelType["NLP"] = "nlp";
    ModelType["COMPUTER_VISION"] = "computer_vision";
    ModelType["REINFORCEMENT_LEARNING"] = "reinforcement_learning";
    ModelType["ENSEMBLE"] = "ensemble";
})(ModelType || (exports.ModelType = ModelType = {}));
var ModelStatus;
(function (ModelStatus) {
    ModelStatus["ACTIVE"] = "active";
    ModelStatus["INACTIVE"] = "inactive";
    ModelStatus["TRAINING"] = "training";
    ModelStatus["DEPLOYING"] = "deploying";
    ModelStatus["MAINTENANCE"] = "maintenance";
    ModelStatus["ARCHIVED"] = "archived";
    ModelStatus["FAILED"] = "failed";
})(ModelStatus || (exports.ModelStatus = ModelStatus = {}));
var HealthStatus;
(function (HealthStatus) {
    HealthStatus["HEALTHY"] = "healthy";
    HealthStatus["UNHEALTHY"] = "unhealthy";
    HealthStatus["DEGRADED"] = "degraded";
    HealthStatus["UNKNOWN"] = "unknown";
})(HealthStatus || (exports.HealthStatus = HealthStatus = {}));
class AIModel extends base_aggregate_root_1.BaseAggregateRoot {
    constructor(props, id) {
        super(props, id);
        this.validateInvariants();
    }
    static create(props, id) {
        const now = new Date();
        const aiModel = new AIModel({
            ...props,
            currentLoad: 0,
            createdAt: now,
            updatedAt: now
        }, id);
        aiModel.addDomainEvent(new ai_model_created_domain_event_1.AIModelCreatedEvent(aiModel.id, props.name, props.provider, props.modelType));
        return aiModel;
    }
    static reconstitute(props, id) {
        return new AIModel(props, id);
    }
    // Getters
    get name() {
        return this.props.name;
    }
    get version() {
        return this.props.version;
    }
    get provider() {
        return this.props.provider;
    }
    get modelType() {
        return this.props.modelType;
    }
    get status() {
        return this.props.status;
    }
    get configuration() {
        return this.props.configuration;
    }
    get performance() {
        return this.props.performance;
    }
    get capabilities() {
        return this.props.capabilities;
    }
    get resourceRequirements() {
        return this.props.resourceRequirements;
    }
    get supportedTaskTypes() {
        return [...this.props.supportedTaskTypes];
    }
    get tags() {
        return [...this.props.tags];
    }
    get priority() {
        return this.props.priority;
    }
    get weight() {
        return this.props.weight;
    }
    get maxConcurrentRequests() {
        return this.props.maxConcurrentRequests;
    }
    get currentLoad() {
        return this.props.currentLoad;
    }
    get lastHealthCheck() {
        return this.props.lastHealthCheck;
    }
    get lastUsed() {
        return this.props.lastUsed;
    }
    get deployedAt() {
        return this.props.deployedAt;
    }
    get metadata() {
        return { ...this.props.metadata };
    }
    get createdAt() {
        return this.props.createdAt;
    }
    get updatedAt() {
        return this.props.updatedAt;
    }
    // Domain methods
    /**
     * Activates the AI model
     */
    activate() {
        if (this.props.status === ModelStatus.ARCHIVED) {
            throw new Error('Cannot activate archived model');
        }
        const previousStatus = this.props.status;
        this.props.status = ModelStatus.ACTIVE;
        this.props.updatedAt = new Date();
        this.addDomainEvent(new ai_model_status_changed_domain_event_1.AIModelStatusChangedEvent(this.id, this.props.name, previousStatus, ModelStatus.ACTIVE));
    }
    /**
     * Deactivates the AI model
     */
    deactivate() {
        const previousStatus = this.props.status;
        this.props.status = ModelStatus.INACTIVE;
        this.props.updatedAt = new Date();
        this.addDomainEvent(new ai_model_status_changed_domain_event_1.AIModelStatusChangedEvent(this.id, this.props.name, previousStatus, ModelStatus.INACTIVE));
    }
    /**
     * Archives the AI model
     */
    archive() {
        const previousStatus = this.props.status;
        this.props.status = ModelStatus.ARCHIVED;
        this.props.updatedAt = new Date();
        this.addDomainEvent(new ai_model_status_changed_domain_event_1.AIModelStatusChangedEvent(this.id, this.props.name, previousStatus, ModelStatus.ARCHIVED));
    }
    /**
     * Updates performance metrics
     */
    updatePerformanceMetrics(metrics) {
        this.props.performance = {
            ...this.props.performance,
            ...metrics,
            lastUpdated: new Date(),
        };
        this.props.updatedAt = new Date();
        this.addDomainEvent(new ai_model_performance_updated_domain_event_1.AIModelPerformanceUpdatedEvent(this.id, this.props.performance));
    }
    /**
     * Updates current load
     */
    updateLoad(delta) {
        const newLoad = Math.max(0, this.props.currentLoad + delta);
        if (newLoad > this.props.maxConcurrentRequests) {
            throw new Error(`Load exceeds maximum concurrent requests: ${this.props.maxConcurrentRequests}`);
        }
        this.props.currentLoad = newLoad;
        this.props.lastUsed = new Date();
        this.props.updatedAt = new Date();
    }
    /**
     * Checks if model can handle additional requests
     */
    canHandleRequest() {
        return (this.props.status === ModelStatus.ACTIVE &&
            this.props.currentLoad < this.props.maxConcurrentRequests);
    }
    /**
     * Checks if model supports a specific task type
     */
    supportsTaskType(taskType) {
        return this.props.supportedTaskTypes.includes(taskType);
    }
    /**
     * Gets model availability percentage
     */
    getAvailability() {
        if (this.props.performance.totalRequests === 0) {
            return 1.0;
        }
        return this.props.performance.successfulRequests / this.props.performance.totalRequests;
    }
    /**
     * Gets model success rate
     */
    getSuccessRate() {
        if (this.props.performance.totalRequests === 0) {
            return 1.0;
        }
        return this.props.performance.successfulRequests / this.props.performance.totalRequests;
    }
    /**
     * Calculates model performance score
     */
    calculatePerformanceScore() {
        const metrics = this.props.performance;
        const accuracyScore = metrics.accuracy;
        const latencyScore = Math.max(0, 1 - metrics.averageLatency / 10000); // Normalize to 10s max
        const availabilityScore = this.getAvailability();
        const successRateScore = this.getSuccessRate();
        return (accuracyScore * 0.3 + latencyScore * 0.2 + availabilityScore * 0.25 + successRateScore * 0.25);
    }
    /**
     * Updates model configuration
     */
    updateConfiguration(config) {
        this.props.configuration = {
            ...this.props.configuration,
            ...config,
        };
        this.props.updatedAt = new Date();
        this.addDomainEvent(new ai_model_configuration_updated_domain_event_1.AIModelConfigurationUpdatedEvent(this.id, this.props.configuration));
    }
    /**
     * Adds a tag to the model
     */
    addTag(tag) {
        if (!this.props.tags.includes(tag)) {
            this.props.tags.push(tag);
            this.props.updatedAt = new Date();
        }
    }
    /**
     * Removes a tag from the model
     */
    removeTag(tag) {
        this.props.tags = this.props.tags.filter(t => t !== tag);
        this.props.updatedAt = new Date();
    }
    /**
     * Checks if model is overloaded
     */
    isOverloaded() {
        const loadPercentage = this.props.currentLoad / this.props.maxConcurrentRequests;
        return loadPercentage > 0.8; // 80% threshold
    }
    /**
     * Gets model utilization percentage
     */
    getUtilization() {
        return this.props.currentLoad / this.props.maxConcurrentRequests;
    }
    /**
     * Updates health check timestamp
     */
    recordHealthCheck() {
        this.props.lastHealthCheck = new Date();
        this.props.updatedAt = new Date();
    }
    /**
     * Updates deployment timestamp
     */
    markAsDeployed() {
        this.props.deployedAt = new Date();
        this.props.status = ModelStatus.ACTIVE;
        this.props.updatedAt = new Date();
    }
    /**
     * Updates metadata
     */
    updateMetadata(metadata) {
        this.props.metadata = {
            ...this.props.metadata,
            ...metadata,
        };
        this.props.updatedAt = new Date();
    }
    validateInvariants() {
        super.validateInvariants();
        if (!this.props.name || this.props.name.trim().length === 0) {
            throw new Error('AI Model name is required');
        }
        if (!this.props.version || this.props.version.trim().length === 0) {
            throw new Error('AI Model version is required');
        }
        if (!Object.values(AIProvider).includes(this.props.provider)) {
            throw new Error('Invalid AI provider');
        }
        if (!Object.values(ModelType).includes(this.props.modelType)) {
            throw new Error('Invalid model type');
        }
        if (!Object.values(ModelStatus).includes(this.props.status)) {
            throw new Error('Invalid model status');
        }
        if (this.props.priority < 0 || this.props.priority > 10) {
            throw new Error('Priority must be between 0 and 10');
        }
        if (this.props.weight < 0 || this.props.weight > 10) {
            throw new Error('Weight must be between 0 and 10');
        }
        if (this.props.maxConcurrentRequests <= 0) {
            throw new Error('Max concurrent requests must be greater than 0');
        }
        if (this.props.currentLoad < 0) {
            throw new Error('Current load cannot be negative');
        }
        if (this.props.supportedTaskTypes.length === 0) {
            throw new Error('Model must support at least one task type');
        }
        if (this.props.configuration.timeout <= 0) {
            throw new Error('Configuration timeout must be greater than 0');
        }
        if (this.props.configuration.retries < 0) {
            throw new Error('Configuration retries cannot be negative');
        }
        if (this.props.configuration.batchSize <= 0) {
            throw new Error('Configuration batch size must be greater than 0');
        }
    }
}
exports.AIModel = AIModel;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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