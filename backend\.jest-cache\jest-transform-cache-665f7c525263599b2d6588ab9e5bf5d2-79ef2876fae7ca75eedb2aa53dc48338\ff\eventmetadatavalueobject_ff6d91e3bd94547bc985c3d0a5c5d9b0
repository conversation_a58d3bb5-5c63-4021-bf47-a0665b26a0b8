6e8149e7ca462afb04bd1f732d1d502a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventMetadata = void 0;
const shared_kernel_1 = require("../../../../../shared-kernel");
const event_timestamp_value_object_1 = require("./event-timestamp.value-object");
const event_source_value_object_1 = require("./event-source.value-object");
/**
 * Event Metadata Value Object
 *
 * Encapsulates metadata information about security events.
 * Provides context about the event's origin, timing, and processing requirements.
 *
 * Key features:
 * - Immutable metadata container
 * - Validation of metadata consistency
 * - Processing hints for pipeline optimization
 * - Extensible custom fields support
 * - Event integrity verification
 */
class EventMetadata extends shared_kernel_1.BaseValueObject {
    constructor(props) {
        super(props);
    }
    validate() {
        if (!this._value.timestamp) {
            throw new Error('Event metadata must have a timestamp');
        }
        if (!this._value.source) {
            throw new Error('Event metadata must have a source');
        }
        // Validate schema version format if provided
        if (this._value.schemaVersion && !/^\d+\.\d+(\.\d+)?$/.test(this._value.schemaVersion)) {
            throw new Error('Schema version must be in semantic version format (e.g., 1.0.0)');
        }
        // Validate event size if provided
        if (this._value.eventSize !== undefined && this._value.eventSize < 0) {
            throw new Error('Event size cannot be negative');
        }
        // Validate checksum format if provided
        if (this._value.checksum && !/^[a-fA-F0-9]+$/.test(this._value.checksum)) {
            throw new Error('Checksum must be a valid hexadecimal string');
        }
        // Validate retention days if provided
        if (this._value.processingHints?.retentionDays !== undefined) {
            const retentionDays = this._value.processingHints.retentionDays;
            if (retentionDays < 1 || retentionDays > 3650) { // Max 10 years
                throw new Error('Retention days must be between 1 and 3650');
            }
        }
    }
    /**
     * Create event metadata with minimal required information
     */
    static create(timestamp, source, options) {
        return new EventMetadata({
            timestamp,
            source,
            ...options,
        });
    }
    /**
     * Create event metadata for current time
     */
    static createNow(source, options) {
        return EventMetadata.create(event_timestamp_value_object_1.EventTimestamp.create(), source, options);
    }
    /**
     * Create event metadata from source system information
     */
    static fromSource(sourceType, sourceIdentifier, timestamp, options) {
        const eventTimestamp = timestamp ? event_timestamp_value_object_1.EventTimestamp.fromDate(timestamp) : event_timestamp_value_object_1.EventTimestamp.create();
        const eventSource = event_source_value_object_1.EventSource.create(sourceType, sourceIdentifier);
        return EventMetadata.create(eventTimestamp, eventSource, options);
    }
    /**
     * Get the event timestamp
     */
    get timestamp() {
        return this._value.timestamp;
    }
    /**
     * Get the event source
     */
    get source() {
        return this._value.source;
    }
    /**
     * Get the original event ID from source system
     */
    get originalEventId() {
        return this._value.originalEventId;
    }
    /**
     * Get the schema version
     */
    get schemaVersion() {
        return this._value.schemaVersion;
    }
    /**
     * Get the event size in bytes
     */
    get eventSize() {
        return this._value.eventSize;
    }
    /**
     * Get the event checksum
     */
    get checksum() {
        return this._value.checksum;
    }
    /**
     * Get custom fields
     */
    get customFields() {
        return this._value.customFields || {};
    }
    /**
     * Get collection method
     */
    get collectionMethod() {
        return this._value.collectionMethod;
    }
    /**
     * Get processing hints
     */
    get processingHints() {
        return this._value.processingHints;
    }
    /**
     * Check if event has a specific custom field
     */
    hasCustomField(key) {
        return key in this.customFields;
    }
    /**
     * Get a custom field value
     */
    getCustomField(key) {
        return this.customFields[key];
    }
    /**
     * Get processing priority
     */
    getPriority() {
        return this._value.processingHints?.priority || 'normal';
    }
    /**
     * Check if normalization should be skipped
     */
    shouldSkipNormalization() {
        return this._value.processingHints?.skipNormalization || false;
    }
    /**
     * Check if enrichment should be skipped
     */
    shouldSkipEnrichment() {
        return this._value.processingHints?.skipEnrichment || false;
    }
    /**
     * Check if correlation should be skipped
     */
    shouldSkipCorrelation() {
        return this._value.processingHints?.skipCorrelation || false;
    }
    /**
     * Get retention period in days
     */
    getRetentionDays() {
        return this._value.processingHints?.retentionDays || 365; // Default 1 year
    }
    /**
     * Check if event is high priority
     */
    isHighPriority() {
        const priority = this.getPriority();
        return priority === 'high' || priority === 'critical';
    }
    /**
     * Check if event is critical priority
     */
    isCriticalPriority() {
        return this.getPriority() === 'critical';
    }
    /**
     * Get event age in milliseconds
     */
    getAge() {
        return this.timestamp.getAge();
    }
    /**
     * Check if event is recent (within specified time)
     */
    isRecent(withinMs = 300000) {
        return this.getAge() <= withinMs;
    }
    /**
     * Check if event is stale (older than specified time)
     */
    isStale(olderThanMs = 86400000) {
        return this.getAge() > olderThanMs;
    }
    /**
     * Create a new metadata with updated custom fields
     */
    withCustomFields(customFields) {
        return new EventMetadata({
            ...this._value,
            customFields: { ...this.customFields, ...customFields },
        });
    }
    /**
     * Create a new metadata with updated processing hints
     */
    withProcessingHints(hints) {
        return new EventMetadata({
            ...this._value,
            processingHints: { ...this._value.processingHints, ...hints },
        });
    }
    /**
     * Create a new metadata with updated priority
     */
    withPriority(priority) {
        return this.withProcessingHints({ priority });
    }
    /**
     * Create a new metadata with checksum
     */
    withChecksum(checksum) {
        return new EventMetadata({
            ...this._value,
            checksum,
        });
    }
    /**
     * Create a new metadata with event size
     */
    withEventSize(eventSize) {
        return new EventMetadata({
            ...this._value,
            eventSize,
        });
    }
    /**
     * Verify event integrity using checksum
     */
    verifyIntegrity(actualChecksum) {
        if (!this._value.checksum) {
            return false; // No checksum to verify against
        }
        return this._value.checksum.toLowerCase() === actualChecksum.toLowerCase();
    }
    /**
     * Get metadata summary for logging
     */
    getSummary() {
        return {
            timestamp: this.timestamp.toISOString(),
            source: this.source.identifier,
            sourceType: this.source.type,
            priority: this.getPriority(),
            age: this.getAge(),
            hasChecksum: !!this._value.checksum,
            customFieldCount: Object.keys(this.customFields).length,
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            timestamp: this.timestamp.toJSON(),
            source: this.source.toJSON(),
            originalEventId: this._value.originalEventId,
            schemaVersion: this._value.schemaVersion,
            eventSize: this._value.eventSize,
            checksum: this._value.checksum,
            customFields: this._value.customFields,
            collectionMethod: this._value.collectionMethod,
            processingHints: this._value.processingHints,
            summary: this.getSummary(),
        };
    }
    /**
     * Create EventMetadata from JSON
     */
    static fromJSON(json) {
        return new EventMetadata({
            timestamp: event_timestamp_value_object_1.EventTimestamp.fromJSON(json.timestamp),
            source: event_source_value_object_1.EventSource.fromJSON(json.source),
            originalEventId: json.originalEventId,
            schemaVersion: json.schemaVersion,
            eventSize: json.eventSize,
            checksum: json.checksum,
            customFields: json.customFields,
            collectionMethod: json.collectionMethod,
            processingHints: json.processingHints,
        });
    }
    /**
     * Compare metadata for equality
     */
    equals(other) {
        if (!other) {
            return false;
        }
        if (this === other) {
            return true;
        }
        return (this.timestamp.equals(other.timestamp) &&
            this.source.equals(other.source) &&
            this._value.originalEventId === other._value.originalEventId &&
            this._value.schemaVersion === other._value.schemaVersion &&
            this._value.checksum === other._value.checksum);
    }
    /**
     * Get string representation
     */
    toString() {
        return `EventMetadata(${this.source.toString()}, ${this.timestamp.toString()})`;
    }
}
exports.EventMetadata = EventMetadata;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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