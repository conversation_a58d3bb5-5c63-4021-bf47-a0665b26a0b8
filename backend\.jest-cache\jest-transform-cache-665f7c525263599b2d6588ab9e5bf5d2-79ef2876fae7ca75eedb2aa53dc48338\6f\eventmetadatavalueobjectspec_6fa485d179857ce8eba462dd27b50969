0fe418ae8caa2dc82e1fd163822fa82d
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const event_metadata_value_object_1 = require("../event-metadata.value-object");
const event_timestamp_value_object_1 = require("../event-timestamp.value-object");
const event_source_value_object_1 = require("../event-source.value-object");
const event_source_type_enum_1 = require("../../../enums/event-source-type.enum");
describe('EventMetadata Value Object', () => {
    let timestamp;
    let source;
    beforeEach(() => {
        timestamp = event_timestamp_value_object_1.EventTimestamp.create();
        source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
    });
    describe('creation', () => {
        it('should create valid event metadata with minimal required information', () => {
            // Act
            const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
            // Assert
            expect(metadata).toBeDefined();
            expect(metadata.timestamp).toBe(timestamp);
            expect(metadata.source).toBe(source);
            expect(metadata.originalEventId).toBeUndefined();
            expect(metadata.schemaVersion).toBeUndefined();
        });
        it('should create valid event metadata with all optional properties', () => {
            // Arrange
            const options = {
                originalEventId: 'orig-123',
                schemaVersion: '1.2.0',
                eventSize: 1024,
                checksum: 'abc123def456',
                customFields: { priority: 'high', department: 'security' },
                collectionMethod: 'streaming',
                processingHints: {
                    priority: 'critical',
                    skipNormalization: false,
                    skipEnrichment: false,
                    skipCorrelation: false,
                    retentionDays: 90,
                },
            };
            // Act
            const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source, options);
            // Assert
            expect(metadata.originalEventId).toBe('orig-123');
            expect(metadata.schemaVersion).toBe('1.2.0');
            expect(metadata.eventSize).toBe(1024);
            expect(metadata.checksum).toBe('abc123def456');
            expect(metadata.customFields).toEqual({ priority: 'high', department: 'security' });
            expect(metadata.collectionMethod).toBe('streaming');
            expect(metadata.processingHints?.priority).toBe('critical');
            expect(metadata.processingHints?.retentionDays).toBe(90);
        });
        it('should create event metadata for current time', () => {
            // Act
            const metadata = event_metadata_value_object_1.EventMetadata.createNow(source);
            // Assert
            expect(metadata.timestamp).toBeDefined();
            expect(metadata.source).toBe(source);
            expect(metadata.timestamp.isRecent()).toBe(true);
        });
        it('should create event metadata from source system information', () => {
            // Arrange
            const specificDate = new Date('2024-01-15T10:30:00.000Z');
            // Act
            const metadata = event_metadata_value_object_1.EventMetadata.fromSource(event_source_type_enum_1.EventSourceType.EDR, 'edr-001', specificDate, { originalEventId: 'edr-event-123' });
            // Assert
            expect(metadata.source.type).toBe(event_source_type_enum_1.EventSourceType.EDR);
            expect(metadata.source.identifier).toBe('edr-001');
            expect(metadata.timestamp.occurredAt).toEqual(specificDate);
            expect(metadata.originalEventId).toBe('edr-event-123');
        });
        it('should create event metadata from source without timestamp', () => {
            // Act
            const metadata = event_metadata_value_object_1.EventMetadata.fromSource(event_source_type_enum_1.EventSourceType.SIEM, 'splunk-001');
            // Assert
            expect(metadata.source.type).toBe(event_source_type_enum_1.EventSourceType.SIEM);
            expect(metadata.source.identifier).toBe('splunk-001');
            expect(metadata.timestamp.isRecent()).toBe(true);
        });
    });
    describe('validation', () => {
        it('should throw error when timestamp is not provided', () => {
            // Act & Assert
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({ timestamp: null, source });
            }).toThrow('Event metadata must have a timestamp');
        });
        it('should throw error when source is not provided', () => {
            // Act & Assert
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({ timestamp, source: null });
            }).toThrow('Event metadata must have a source');
        });
        it('should throw error when schema version format is invalid', () => {
            // Act & Assert
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({
                    timestamp,
                    source,
                    schemaVersion: 'invalid-version',
                });
            }).toThrow('Schema version must be in semantic version format (e.g., 1.0.0)');
        });
        it('should accept valid schema version formats', () => {
            // Act & Assert
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({ timestamp, source, schemaVersion: '1.0.0' });
            }).not.toThrow();
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({ timestamp, source, schemaVersion: '2.1' });
            }).not.toThrow();
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({ timestamp, source, schemaVersion: '1.0.0-beta' });
            }).not.toThrow();
        });
        it('should throw error when event size is negative', () => {
            // Act & Assert
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({
                    timestamp,
                    source,
                    eventSize: -100,
                });
            }).toThrow('Event size cannot be negative');
        });
        it('should throw error when checksum format is invalid', () => {
            // Act & Assert
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({
                    timestamp,
                    source,
                    checksum: 'invalid-checksum!',
                });
            }).toThrow('Checksum must be a valid hexadecimal string');
        });
        it('should accept valid checksum formats', () => {
            // Act & Assert
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({ timestamp, source, checksum: 'abc123' });
            }).not.toThrow();
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({ timestamp, source, checksum: 'ABC123DEF456' });
            }).not.toThrow();
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({ timestamp, source, checksum: '0123456789abcdef' });
            }).not.toThrow();
        });
        it('should throw error when retention days is out of range', () => {
            // Act & Assert
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({
                    timestamp,
                    source,
                    processingHints: { retentionDays: 0 },
                });
            }).toThrow('Retention days must be between 1 and 3650');
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({
                    timestamp,
                    source,
                    processingHints: { retentionDays: 4000 },
                });
            }).toThrow('Retention days must be between 1 and 3650');
        });
        it('should accept valid retention days', () => {
            // Act & Assert
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({
                    timestamp,
                    source,
                    processingHints: { retentionDays: 1 },
                });
            }).not.toThrow();
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({
                    timestamp,
                    source,
                    processingHints: { retentionDays: 365 },
                });
            }).not.toThrow();
            expect(() => {
                new event_metadata_value_object_1.EventMetadata({
                    timestamp,
                    source,
                    processingHints: { retentionDays: 3650 },
                });
            }).not.toThrow();
        });
    });
    describe('custom fields handling', () => {
        it('should handle custom fields correctly', () => {
            // Arrange
            const customFields = { priority: 'high', department: 'security', tags: ['critical', 'network'] };
            const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source, { customFields });
            // Act & Assert
            expect(metadata.hasCustomField('priority')).toBe(true);
            expect(metadata.hasCustomField('nonexistent')).toBe(false);
            expect(metadata.getCustomField('priority')).toBe('high');
            expect(metadata.getCustomField('department')).toBe('security');
            expect(metadata.getCustomField('tags')).toEqual(['critical', 'network']);
            expect(metadata.getCustomField('nonexistent')).toBeUndefined();
        });
        it('should handle empty custom fields gracefully', () => {
            // Arrange
            const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
            // Act & Assert
            expect(metadata.customFields).toEqual({});
            expect(metadata.hasCustomField('anything')).toBe(false);
            expect(metadata.getCustomField('anything')).toBeUndefined();
        });
        it('should create new instance with updated custom fields', () => {
            // Arrange
            const original = event_metadata_value_object_1.EventMetadata.create(timestamp, source, {
                customFields: { existing: 'value' },
            });
            const newFields = { priority: 'high', department: 'security' };
            // Act
            const updated = original.withCustomFields(newFields);
            // Assert
            expect(updated).not.toBe(original);
            expect(updated.hasCustomField('existing')).toBe(true);
            expect(updated.hasCustomField('priority')).toBe(true);
            expect(updated.hasCustomField('department')).toBe(true);
            expect(original.hasCustomField('priority')).toBe(false);
        });
    });
    describe('processing hints', () => {
        it('should handle processing hints correctly', () => {
            // Arrange
            const processingHints = {
                priority: 'critical',
                skipNormalization: true,
                skipEnrichment: false,
                skipCorrelation: true,
                retentionDays: 180,
            };
            const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source, { processingHints });
            // Act & Assert
            expect(metadata.getPriority()).toBe('critical');
            expect(metadata.shouldSkipNormalization()).toBe(true);
            expect(metadata.shouldSkipEnrichment()).toBe(false);
            expect(metadata.shouldSkipCorrelation()).toBe(true);
            expect(metadata.getRetentionDays()).toBe(180);
        });
        it('should use default values when processing hints are not provided', () => {
            // Arrange
            const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
            // Act & Assert
            expect(metadata.getPriority()).toBe('normal');
            expect(metadata.shouldSkipNormalization()).toBe(false);
            expect(metadata.shouldSkipEnrichment()).toBe(false);
            expect(metadata.shouldSkipCorrelation()).toBe(false);
            expect(metadata.getRetentionDays()).toBe(365);
        });
        it('should identify priority levels correctly', () => {
            // Arrange
            const highPriority = event_metadata_value_object_1.EventMetadata.create(timestamp, source, {
                processingHints: { priority: 'high' },
            });
            const criticalPriority = event_metadata_value_object_1.EventMetadata.create(timestamp, source, {
                processingHints: { priority: 'critical' },
            });
            const normalPriority = event_metadata_value_object_1.EventMetadata.create(timestamp, source, {
                processingHints: { priority: 'normal' },
            });
            // Act & Assert
            expect(highPriority.isHighPriority()).toBe(true);
            expect(highPriority.isCriticalPriority()).toBe(false);
            expect(criticalPriority.isHighPriority()).toBe(true);
            expect(criticalPriority.isCriticalPriority()).toBe(true);
            expect(normalPriority.isHighPriority()).toBe(false);
            expect(normalPriority.isCriticalPriority()).toBe(false);
        });
        it('should create new instance with updated processing hints', () => {
            // Arrange
            const original = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
            const newHints = { priority: 'high', skipNormalization: true };
            // Act
            const updated = original.withProcessingHints(newHints);
            // Assert
            expect(updated).not.toBe(original);
            expect(updated.getPriority()).toBe('high');
            expect(updated.shouldSkipNormalization()).toBe(true);
            expect(original.getPriority()).toBe('normal');
            expect(original.shouldSkipNormalization()).toBe(false);
        });
        it('should create new instance with updated priority', () => {
            // Arrange
            const original = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
            // Act
            const updated = original.withPriority('critical');
            // Assert
            expect(updated).not.toBe(original);
            expect(updated.getPriority()).toBe('critical');
            expect(original.getPriority()).toBe('normal');
        });
    });
    describe('event properties', () => {
        it('should create new instance with checksum', () => {
            // Arrange
            const original = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
            // Act
            const updated = original.withChecksum('abc123def456');
            // Assert
            expect(updated).not.toBe(original);
            expect(updated.checksum).toBe('abc123def456');
            expect(original.checksum).toBeUndefined();
        });
        it('should create new instance with event size', () => {
            // Arrange
            const original = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
            // Act
            const updated = original.withEventSize(2048);
            // Assert
            expect(updated).not.toBe(original);
            expect(updated.eventSize).toBe(2048);
            expect(original.eventSize).toBeUndefined();
        });
        it('should verify integrity correctly', () => {
            // Arrange
            const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source, {
                checksum: 'abc123def456',
            });
            // Act & Assert
            expect(metadata.verifyIntegrity('abc123def456')).toBe(true);
            expect(metadata.verifyIntegrity('ABC123DEF456')).toBe(true); // Case insensitive
            expect(metadata.verifyIntegrity('different')).toBe(false);
        });
        it('should return false for integrity verification when no checksum is set', () => {
            // Arrange
            const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
            // Act & Assert
            expect(metadata.verifyIntegrity('any-checksum')).toBe(false);
        });
    });
    describe('age and freshness', () => {
        it('should delegate age calculations to timestamp', () => {
            // Arrange
            const pastDate = new Date(Date.now() - 5000); // 5 seconds ago
            const pastTimestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(pastDate);
            const metadata = event_metadata_value_object_1.EventMetadata.create(pastTimestamp, source);
            // Act
            const age = metadata.getAge();
            // Assert
            expect(age).toBeGreaterThanOrEqual(4900);
            expect(age).toBeLessThanOrEqual(5100);
        });
        it('should delegate freshness checks to timestamp', () => {
            // Arrange
            const recentDate = new Date(Date.now() - 2 * 60 * 1000); // 2 minutes ago
            const recentTimestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(recentDate);
            const metadata = event_metadata_value_object_1.EventMetadata.create(recentTimestamp, source);
            // Act & Assert
            expect(metadata.isRecent()).toBe(true);
            expect(metadata.isRecent(60000)).toBe(false); // Within 1 minute
        });
        it('should delegate stale checks to timestamp', () => {
            // Arrange
            const staleDate = new Date(Date.now() - 25 * 60 * 60 * 1000); // 25 hours ago
            const staleTimestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(staleDate);
            const metadata = event_metadata_value_object_1.EventMetadata.create(staleTimestamp, source);
            // Act & Assert
            expect(metadata.isStale()).toBe(true);
            expect(metadata.isStale(48 * 60 * 60 * 1000)).toBe(false); // Within 48 hours
        });
    });
    describe('summary and logging', () => {
        it('should provide comprehensive summary', () => {
            // Arrange
            const pastDate = new Date(Date.now() - 5000); // 5 seconds ago
            const pastTimestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(pastDate);
            const metadata = event_metadata_value_object_1.EventMetadata.create(pastTimestamp, source, {
                checksum: 'abc123',
                customFields: { priority: 'high', department: 'security' },
                processingHints: { priority: 'critical' },
            });
            // Act
            const summary = metadata.getSummary();
            // Assert
            expect(summary.timestamp).toBe(pastTimestamp.toISOString());
            expect(summary.source).toBe(source.identifier);
            expect(summary.sourceType).toBe(source.type);
            expect(summary.priority).toBe('critical');
            expect(summary.hasChecksum).toBe(true);
            expect(summary.customFieldCount).toBe(2);
            expect(typeof summary.age).toBe('number');
        });
    });
    describe('serialization', () => {
        it('should convert to JSON correctly', () => {
            // Arrange
            const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source, {
                originalEventId: 'orig-123',
                schemaVersion: '1.2.0',
                eventSize: 1024,
                checksum: 'abc123',
                customFields: { priority: 'high' },
                collectionMethod: 'streaming',
                processingHints: { priority: 'critical', retentionDays: 90 },
            });
            // Act
            const json = metadata.toJSON();
            // Assert
            expect(json.timestamp).toBeDefined();
            expect(json.source).toBeDefined();
            expect(json.originalEventId).toBe('orig-123');
            expect(json.schemaVersion).toBe('1.2.0');
            expect(json.eventSize).toBe(1024);
            expect(json.checksum).toBe('abc123');
            expect(json.customFields).toEqual({ priority: 'high' });
            expect(json.collectionMethod).toBe('streaming');
            expect(json.processingHints).toEqual({ priority: 'critical', retentionDays: 90 });
            expect(json.summary).toBeDefined();
        });
        it('should create from JSON correctly', () => {
            // Arrange
            const json = {
                timestamp: timestamp.toJSON(),
                source: source.toJSON(),
                originalEventId: 'orig-123',
                schemaVersion: '1.2.0',
                eventSize: 1024,
                checksum: 'abc123',
                customFields: { priority: 'high' },
                collectionMethod: 'streaming',
                processingHints: { priority: 'critical', retentionDays: 90 },
            };
            // Act
            const metadata = event_metadata_value_object_1.EventMetadata.fromJSON(json);
            // Assert
            expect(metadata.originalEventId).toBe('orig-123');
            expect(metadata.schemaVersion).toBe('1.2.0');
            expect(metadata.eventSize).toBe(1024);
            expect(metadata.checksum).toBe('abc123');
            expect(metadata.customFields).toEqual({ priority: 'high' });
            expect(metadata.collectionMethod).toBe('streaming');
            expect(metadata.processingHints?.priority).toBe('critical');
            expect(metadata.processingHints?.retentionDays).toBe(90);
        });
    });
    describe('equality and comparison', () => {
        it('should compare metadata for equality correctly', () => {
            // Arrange
            const metadata1 = event_metadata_value_object_1.EventMetadata.create(timestamp, source, {
                originalEventId: 'orig-123',
                schemaVersion: '1.0.0',
                checksum: 'abc123',
            });
            const metadata2 = event_metadata_value_object_1.EventMetadata.create(timestamp, source, {
                originalEventId: 'orig-123',
                schemaVersion: '1.0.0',
                checksum: 'abc123',
            });
            const metadata3 = event_metadata_value_object_1.EventMetadata.create(timestamp, source, {
                originalEventId: 'orig-456',
                schemaVersion: '1.0.0',
                checksum: 'abc123',
            });
            // Act & Assert
            expect(metadata1.equals(metadata2)).toBe(true);
            expect(metadata1.equals(metadata3)).toBe(false);
            expect(metadata1.equals(undefined)).toBe(false);
            expect(metadata1.equals(metadata1)).toBe(true);
        });
        it('should convert to string correctly', () => {
            // Arrange
            const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
            // Act
            const str = metadata.toString();
            // Assert
            expect(str).toContain('EventMetadata');
            expect(str).toContain(source.toString());
            expect(str).toContain(timestamp.toString());
        });
    });
    describe('edge cases', () => {
        it('should handle missing optional properties gracefully', () => {
            // Arrange
            const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
            // Act & Assert
            expect(metadata.originalEventId).toBeUndefined();
            expect(metadata.schemaVersion).toBeUndefined();
            expect(metadata.eventSize).toBeUndefined();
            expect(metadata.checksum).toBeUndefined();
            expect(metadata.customFields).toEqual({});
            expect(metadata.collectionMethod).toBeUndefined();
            expect(metadata.processingHints).toBeUndefined();
        });
        it('should handle partial processing hints gracefully', () => {
            // Arrange
            const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source, {
                processingHints: { priority: 'high' }, // Only priority set
            });
            // Act & Assert
            expect(metadata.getPriority()).toBe('high');
            expect(metadata.shouldSkipNormalization()).toBe(false); // Default
            expect(metadata.shouldSkipEnrichment()).toBe(false); // Default
            expect(metadata.shouldSkipCorrelation()).toBe(false); // Default
            expect(metadata.getRetentionDays()).toBe(365); // Default
        });
        it('should handle zero event size correctly', () => {
            // Arrange & Act & Assert
            expect(() => {
                event_metadata_value_object_1.EventMetadata.create(timestamp, source, { eventSize: 0 });
            }).not.toThrow();
            const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source, { eventSize: 0 });
            expect(metadata.eventSize).toBe(0);
        });
        it('should handle empty checksum string', () => {
            // Arrange & Act & Assert
            expect(() => {
                event_metadata_value_object_1.EventMetadata.create(timestamp, source, { checksum: '' });
            }).toThrow('Checksum must be a valid hexadecimal string');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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