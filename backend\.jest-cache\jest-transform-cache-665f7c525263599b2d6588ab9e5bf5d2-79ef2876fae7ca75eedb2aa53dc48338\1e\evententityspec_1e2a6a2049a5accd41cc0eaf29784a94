1de7b06a0fce85ec5052b8611fd193fd
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const event_entity_1 = require("../event.entity");
const event_factory_1 = require("../../factories/event.factory");
const event_metadata_value_object_1 = require("../../value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../value-objects/event-metadata/event-source.value-object");
const shared_kernel_1 = require("../../../../shared-kernel");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const event_status_enum_1 = require("../../enums/event-status.enum");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const event_source_type_enum_1 = require("../../enums/event-source-type.enum");
const event_created_domain_event_1 = require("../../events/event-created.domain-event");
const event_status_changed_domain_event_1 = require("../../events/event-status-changed.domain-event");
const event_processing_status_changed_domain_event_1 = require("../../events/event-processing-status-changed.domain-event");
describe('Event Entity', () => {
    let validEventProps;
    let eventMetadata;
    beforeEach(() => {
        const timestamp = event_timestamp_value_object_1.EventTimestamp.create();
        const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'firewall-01');
        eventMetadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
        validEventProps = {
            metadata: eventMetadata,
            type: event_type_enum_1.EventType.THREAT_DETECTED,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            status: event_status_enum_1.EventStatus.ACTIVE,
            processingStatus: event_processing_status_enum_1.EventProcessingStatus.RAW,
            rawData: { message: 'Test threat detected' },
            title: 'Test Security Event',
            description: 'A test security event for unit testing',
            tags: ['test', 'security'],
            riskScore: 75,
            confidenceLevel: 85,
            attributes: { testAttribute: 'testValue' },
        };
    });
    describe('Creation', () => {
        it('should create a valid event with required properties', () => {
            const event = event_entity_1.Event.create(validEventProps);
            expect(event).toBeInstanceOf(event_entity_1.Event);
            expect(event.id).toBeInstanceOf(shared_kernel_1.UniqueEntityId);
            expect(event.type).toBe(event_type_enum_1.EventType.THREAT_DETECTED);
            expect(event.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
            expect(event.status).toBe(event_status_enum_1.EventStatus.ACTIVE);
            expect(event.processingStatus).toBe(event_processing_status_enum_1.EventProcessingStatus.RAW);
            expect(event.title).toBe('Test Security Event');
            expect(event.description).toBe('A test security event for unit testing');
            expect(event.riskScore).toBe(75);
            expect(event.confidenceLevel).toBe(85);
        });
        it('should generate EventCreatedDomainEvent on creation', () => {
            const event = event_entity_1.Event.create(validEventProps);
            const domainEvents = event.domainEvents;
            expect(domainEvents).toHaveLength(1);
            expect(domainEvents[0]).toBeInstanceOf(event_created_domain_event_1.EventCreatedDomainEvent);
            const createdEvent = domainEvents[0];
            expect(createdEvent.eventType).toBe(event_type_enum_1.EventType.THREAT_DETECTED);
            expect(createdEvent.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
            expect(createdEvent.title).toBe('Test Security Event');
        });
        it('should create event with custom ID', () => {
            const customId = shared_kernel_1.UniqueEntityId.generate();
            const event = event_entity_1.Event.create(validEventProps, customId);
            expect(event.id.equals(customId)).toBe(true);
        });
        it('should throw error when metadata is missing', () => {
            const invalidProps = { ...validEventProps };
            delete invalidProps.metadata;
            expect(() => event_entity_1.Event.create(invalidProps)).toThrow('Event must have metadata');
        });
        it('should throw error when type is missing', () => {
            const invalidProps = { ...validEventProps };
            delete invalidProps.type;
            expect(() => event_entity_1.Event.create(invalidProps)).toThrow('Event must have a type');
        });
        it('should throw error when severity is missing', () => {
            const invalidProps = { ...validEventProps };
            delete invalidProps.severity;
            expect(() => event_entity_1.Event.create(invalidProps)).toThrow('Event must have a severity');
        });
        it('should throw error when title is empty', () => {
            const invalidProps = { ...validEventProps, title: '' };
            expect(() => event_entity_1.Event.create(invalidProps)).toThrow('Event must have a non-empty title');
        });
        it('should throw error when title is too long', () => {
            const invalidProps = { ...validEventProps, title: 'a'.repeat(201) };
            expect(() => event_entity_1.Event.create(invalidProps)).toThrow('Event title cannot exceed 200 characters');
        });
        it('should throw error when description is too long', () => {
            const invalidProps = { ...validEventProps, description: 'a'.repeat(2001) };
            expect(() => event_entity_1.Event.create(invalidProps)).toThrow('Event description cannot exceed 2000 characters');
        });
        it('should throw error when risk score is invalid', () => {
            const invalidProps1 = { ...validEventProps, riskScore: -1 };
            const invalidProps2 = { ...validEventProps, riskScore: 101 };
            expect(() => event_entity_1.Event.create(invalidProps1)).toThrow('Risk score must be between 0 and 100');
            expect(() => event_entity_1.Event.create(invalidProps2)).toThrow('Risk score must be between 0 and 100');
        });
        it('should throw error when confidence level is invalid', () => {
            const invalidProps1 = { ...validEventProps, confidenceLevel: -1 };
            const invalidProps2 = { ...validEventProps, confidenceLevel: 101 };
            expect(() => event_entity_1.Event.create(invalidProps1)).toThrow('Confidence level must be between 0 and 100');
            expect(() => event_entity_1.Event.create(invalidProps2)).toThrow('Confidence level must be between 0 and 100');
        });
        it('should throw error when too many tags', () => {
            const invalidProps = { ...validEventProps, tags: Array(21).fill('tag') };
            expect(() => event_entity_1.Event.create(invalidProps)).toThrow('Event cannot have more than 20 tags');
        });
    });
    describe('Status Management', () => {
        let event;
        beforeEach(() => {
            event = event_entity_1.Event.create(validEventProps);
            event.clearEvents(); // Clear creation event for cleaner tests
        });
        it('should change status successfully', () => {
            event.changeStatus(event_status_enum_1.EventStatus.INVESTIGATING, 'analyst1', 'Starting investigation');
            expect(event.status).toBe(event_status_enum_1.EventStatus.INVESTIGATING);
            const domainEvents = event.domainEvents;
            expect(domainEvents).toHaveLength(1);
            expect(domainEvents[0]).toBeInstanceOf(event_status_changed_domain_event_1.EventStatusChangedDomainEvent);
            const statusEvent = domainEvents[0];
            expect(statusEvent.oldStatus).toBe(event_status_enum_1.EventStatus.ACTIVE);
            expect(statusEvent.newStatus).toBe(event_status_enum_1.EventStatus.INVESTIGATING);
            expect(statusEvent.changedBy).toBe('analyst1');
            expect(statusEvent.notes).toBe('Starting investigation');
        });
        it('should not generate event when status unchanged', () => {
            event.changeStatus(event_status_enum_1.EventStatus.ACTIVE);
            expect(event.status).toBe(event_status_enum_1.EventStatus.ACTIVE);
            expect(event.domainEvents).toHaveLength(0);
        });
        it('should set resolution information when resolved', () => {
            const beforeResolve = new Date();
            event.changeStatus(event_status_enum_1.EventStatus.RESOLVED, 'analyst1', 'Issue resolved');
            const afterResolve = new Date();
            expect(event.status).toBe(event_status_enum_1.EventStatus.RESOLVED);
            expect(event.resolvedBy).toBe('analyst1');
            expect(event.resolutionNotes).toBe('Issue resolved');
            expect(event.resolvedAt).toBeDefined();
            expect(event.resolvedAt.getTime()).toBeGreaterThanOrEqual(beforeResolve.getTime());
            expect(event.resolvedAt.getTime()).toBeLessThanOrEqual(afterResolve.getTime());
        });
        it('should clear resolution information when reopened', () => {
            // First resolve
            event.changeStatus(event_status_enum_1.EventStatus.RESOLVED, 'analyst1', 'Issue resolved');
            expect(event.resolvedAt).toBeDefined();
            // Then reopen
            event.changeStatus(event_status_enum_1.EventStatus.ACTIVE);
            expect(event.status).toBe(event_status_enum_1.EventStatus.ACTIVE);
            expect(event.resolvedAt).toBeUndefined();
            expect(event.resolvedBy).toBeUndefined();
            expect(event.resolutionNotes).toBeUndefined();
        });
    });
    describe('Processing Status Management', () => {
        let event;
        beforeEach(() => {
            event = event_entity_1.Event.create(validEventProps);
            event.clearEvents();
        });
        it('should change processing status successfully', () => {
            const beforeChange = new Date();
            event.changeProcessingStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZED);
            const afterChange = new Date();
            expect(event.processingStatus).toBe(event_processing_status_enum_1.EventProcessingStatus.NORMALIZED);
            expect(event.lastProcessedAt).toBeDefined();
            expect(event.lastProcessedAt.getTime()).toBeGreaterThanOrEqual(beforeChange.getTime());
            expect(event.lastProcessedAt.getTime()).toBeLessThanOrEqual(afterChange.getTime());
            const domainEvents = event.domainEvents;
            expect(domainEvents).toHaveLength(1);
            expect(domainEvents[0]).toBeInstanceOf(event_processing_status_changed_domain_event_1.EventProcessingStatusChangedDomainEvent);
        });
        it('should not generate event when processing status unchanged', () => {
            event.changeProcessingStatus(event_processing_status_enum_1.EventProcessingStatus.RAW);
            expect(event.processingStatus).toBe(event_processing_status_enum_1.EventProcessingStatus.RAW);
            expect(event.domainEvents).toHaveLength(0);
        });
    });
    describe('Data Management', () => {
        let event;
        beforeEach(() => {
            event = event_entity_1.Event.create(validEventProps);
        });
        it('should update normalized data', () => {
            const normalizedData = { normalized: true, field1: 'value1' };
            event.updateNormalizedData(normalizedData);
            expect(event.normalizedData).toEqual(normalizedData);
            expect(event.processingStatus).toBe(event_processing_status_enum_1.EventProcessingStatus.NORMALIZED);
        });
        it('should update risk score', () => {
            event.updateRiskScore(90);
            expect(event.riskScore).toBe(90);
        });
        it('should throw error for invalid risk score', () => {
            expect(() => event.updateRiskScore(-1)).toThrow('Risk score must be between 0 and 100');
            expect(() => event.updateRiskScore(101)).toThrow('Risk score must be between 0 and 100');
        });
        it('should update confidence level', () => {
            event.updateConfidenceLevel(95);
            expect(event.confidenceLevel).toBe(95);
        });
        it('should throw error for invalid confidence level', () => {
            expect(() => event.updateConfidenceLevel(-1)).toThrow('Confidence level must be between 0 and 100');
            expect(() => event.updateConfidenceLevel(101)).toThrow('Confidence level must be between 0 and 100');
        });
        it('should add tags', () => {
            event.addTags(['new-tag', 'another-tag']);
            expect(event.tags).toContain('new-tag');
            expect(event.tags).toContain('another-tag');
            expect(event.tags).toContain('test'); // Original tag should remain
        });
        it('should not add duplicate tags', () => {
            event.addTags(['test', 'new-tag']); // 'test' already exists
            expect(event.tags.filter(tag => tag === 'test')).toHaveLength(1);
            expect(event.tags).toContain('new-tag');
        });
        it('should throw error when adding too many tags', () => {
            const manyTags = Array(19).fill(0).map((_, i) => `tag-${i}`);
            expect(() => event.addTags(manyTags)).toThrow('Event cannot have more than 20 tags');
        });
        it('should remove tags', () => {
            event.removeTags(['test']);
            expect(event.tags).not.toContain('test');
            expect(event.tags).toContain('security'); // Other tag should remain
        });
        it('should set correlation ID', () => {
            const correlationId = 'correlation-123';
            event.setCorrelationId(correlationId);
            expect(event.correlationId).toBe(correlationId);
        });
        it('should set parent event', () => {
            const parentId = shared_kernel_1.UniqueEntityId.generate();
            event.setParentEvent(parentId);
            expect(event.parentEventId?.equals(parentId)).toBe(true);
        });
        it('should record processing attempt', () => {
            const initialAttempts = event.processingAttempts;
            const beforeAttempt = new Date();
            event.recordProcessingAttempt('Test error');
            const afterAttempt = new Date();
            expect(event.processingAttempts).toBe(initialAttempts + 1);
            expect(event.lastProcessingError).toBe('Test error');
            expect(event.lastProcessedAt).toBeDefined();
            expect(event.lastProcessedAt.getTime()).toBeGreaterThanOrEqual(beforeAttempt.getTime());
            expect(event.lastProcessedAt.getTime()).toBeLessThanOrEqual(afterAttempt.getTime());
        });
        it('should clear processing error', () => {
            event.recordProcessingAttempt('Test error');
            expect(event.lastProcessingError).toBe('Test error');
            event.clearProcessingError();
            expect(event.lastProcessingError).toBeUndefined();
        });
        it('should update attributes', () => {
            event.updateAttributes({ newAttr: 'newValue', testAttribute: 'updatedValue' });
            expect(event.attributes.newAttr).toBe('newValue');
            expect(event.attributes.testAttribute).toBe('updatedValue');
        });
        it('should remove attribute', () => {
            event.removeAttribute('testAttribute');
            expect(event.attributes.testAttribute).toBeUndefined();
        });
    });
    describe('Query Methods', () => {
        let event;
        beforeEach(() => {
            event = event_entity_1.Event.create(validEventProps);
        });
        it('should identify active events', () => {
            expect(event.isActive()).toBe(true);
            event.changeStatus(event_status_enum_1.EventStatus.RESOLVED);
            expect(event.isActive()).toBe(false);
            event.changeStatus(event_status_enum_1.EventStatus.CLOSED);
            expect(event.isActive()).toBe(false);
            event.changeStatus(event_status_enum_1.EventStatus.FALSE_POSITIVE);
            expect(event.isActive()).toBe(false);
        });
        it('should identify resolved events', () => {
            expect(event.isResolved()).toBe(false);
            event.changeStatus(event_status_enum_1.EventStatus.RESOLVED);
            expect(event.isResolved()).toBe(true);
        });
        it('should identify high severity events', () => {
            expect(event.isHighSeverity()).toBe(true); // HIGH severity
            const lowSeverityProps = { ...validEventProps, severity: event_severity_enum_1.EventSeverity.LOW };
            const lowSeverityEvent = event_entity_1.Event.create(lowSeverityProps);
            expect(lowSeverityEvent.isHighSeverity()).toBe(false);
            const criticalSeverityProps = { ...validEventProps, severity: event_severity_enum_1.EventSeverity.CRITICAL };
            const criticalSeverityEvent = event_entity_1.Event.create(criticalSeverityProps);
            expect(criticalSeverityEvent.isHighSeverity()).toBe(true);
        });
        it('should identify critical events', () => {
            expect(event.isCritical()).toBe(false); // HIGH severity, not CRITICAL
            const criticalProps = { ...validEventProps, severity: event_severity_enum_1.EventSeverity.CRITICAL };
            const criticalEvent = event_entity_1.Event.create(criticalProps);
            expect(criticalEvent.isCritical()).toBe(true);
        });
        it('should identify high risk events', () => {
            expect(event.isHighRisk()).toBe(true); // Risk score 75 >= 70
            const lowRiskProps = { ...validEventProps, riskScore: 50 };
            const lowRiskEvent = event_entity_1.Event.create(lowRiskProps);
            expect(lowRiskEvent.isHighRisk()).toBe(false);
            const noRiskProps = { ...validEventProps };
            delete noRiskProps.riskScore;
            const noRiskEvent = event_entity_1.Event.create(noRiskProps);
            expect(noRiskEvent.isHighRisk()).toBe(false);
        });
        it('should identify processing failures', () => {
            expect(event.hasProcessingFailed()).toBe(false);
            event.changeProcessingStatus(event_processing_status_enum_1.EventProcessingStatus.FAILED);
            expect(event.hasProcessingFailed()).toBe(true);
        });
        it('should identify exceeded max attempts', () => {
            expect(event.hasExceededMaxAttempts()).toBe(false);
            // Record 5 attempts (max allowed)
            for (let i = 0; i < 5; i++) {
                event.recordProcessingAttempt();
            }
            expect(event.hasExceededMaxAttempts()).toBe(true);
        });
        it('should check for tags', () => {
            expect(event.hasTag('test')).toBe(true);
            expect(event.hasTag('nonexistent')).toBe(false);
            expect(event.hasAnyTag(['test', 'nonexistent'])).toBe(true);
            expect(event.hasAnyTag(['nonexistent', 'another'])).toBe(false);
            expect(event.hasAllTags(['test', 'security'])).toBe(true);
            expect(event.hasAllTags(['test', 'nonexistent'])).toBe(false);
        });
        it('should calculate age correctly', () => {
            const age = event.getAge();
            expect(age).toBeGreaterThanOrEqual(0);
            expect(age).toBeLessThan(1000); // Should be very recent
        });
        it('should identify recent events', () => {
            expect(event.isRecent(3600000)).toBe(true); // Within 1 hour
            expect(event.isRecent(1)).toBe(false); // Within 1ms (too strict)
        });
        it('should identify stale events', () => {
            expect(event.isStale(86400000)).toBe(false); // Not older than 24 hours
            expect(event.isStale(1)).toBe(true); // Older than 1ms
        });
    });
    describe('Summary and Serialization', () => {
        let event;
        beforeEach(() => {
            event = event_entity_1.Event.create(validEventProps);
        });
        it('should generate correct summary', () => {
            const summary = event.getSummary();
            expect(summary.id).toBe(event.id.toString());
            expect(summary.title).toBe('Test Security Event');
            expect(summary.type).toBe(event_type_enum_1.EventType.THREAT_DETECTED);
            expect(summary.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
            expect(summary.status).toBe(event_status_enum_1.EventStatus.ACTIVE);
            expect(summary.processingStatus).toBe(event_processing_status_enum_1.EventProcessingStatus.RAW);
            expect(summary.riskScore).toBe(75);
            expect(summary.isActive).toBe(true);
            expect(summary.isHighRisk).toBe(true);
            expect(typeof summary.age).toBe('number');
            expect(typeof summary.source).toBe('string');
        });
        it('should serialize to JSON correctly', () => {
            const json = event.toJSON();
            expect(json.id).toBe(event.id.toString());
            expect(json.type).toBe(event_type_enum_1.EventType.THREAT_DETECTED);
            expect(json.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
            expect(json.title).toBe('Test Security Event');
            expect(json.metadata).toBeDefined();
            expect(json.rawData).toEqual({ message: 'Test threat detected' });
            expect(json.tags).toEqual(['test', 'security']);
            expect(json.riskScore).toBe(75);
            expect(json.confidenceLevel).toBe(85);
            expect(json.attributes).toEqual({ testAttribute: 'testValue' });
            expect(json.summary).toBeDefined();
        });
    });
    describe('Status Consistency Validation', () => {
        it('should throw error when resolved event lacks resolution timestamp', () => {
            const invalidProps = {
                ...validEventProps,
                status: event_status_enum_1.EventStatus.RESOLVED,
                // Missing resolvedAt
            };
            expect(() => event_entity_1.Event.create(invalidProps)).toThrow('Resolved events must have a resolution timestamp');
        });
        it('should throw error when non-resolved event has resolution info', () => {
            const invalidProps = {
                ...validEventProps,
                status: event_status_enum_1.EventStatus.ACTIVE,
                resolvedAt: new Date(), // Should not have resolution info
            };
            expect(() => event_entity_1.Event.create(invalidProps)).toThrow('Only resolved or closed events can have resolution information');
        });
        it('should throw error when resolved event has inconsistent processing status', () => {
            const invalidProps = {
                ...validEventProps,
                status: event_status_enum_1.EventStatus.RESOLVED,
                processingStatus: event_processing_status_enum_1.EventProcessingStatus.RAW, // Inconsistent
                resolvedAt: new Date(),
            };
            expect(() => event_entity_1.Event.create(invalidProps)).toThrow('Resolved events must have resolved or archived processing status');
        });
    });
    describe('Factory Integration', () => {
        it('should work with EventFactory', () => {
            const event = event_factory_1.EventFactory.create({
                type: event_type_enum_1.EventType.MALWARE_DETECTED,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                title: 'Malware Alert',
                rawData: { malware: 'trojan.exe' },
                sourceType: event_source_type_enum_1.EventSourceType.ANTIVIRUS,
                sourceIdentifier: 'av-scanner-01',
            });
            expect(event).toBeInstanceOf(event_entity_1.Event);
            expect(event.type).toBe(event_type_enum_1.EventType.MALWARE_DETECTED);
            expect(event.severity).toBe(event_severity_enum_1.EventSeverity.CRITICAL);
            expect(event.title).toBe('Malware Alert');
            expect(event.metadata.source.type).toBe(event_source_type_enum_1.EventSourceType.ANTIVIRUS);
            expect(event.metadata.source.identifier).toBe('av-scanner-01');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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