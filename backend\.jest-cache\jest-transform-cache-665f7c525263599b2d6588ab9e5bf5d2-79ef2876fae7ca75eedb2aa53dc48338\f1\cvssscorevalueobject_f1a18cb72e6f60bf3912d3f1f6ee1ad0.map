{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\cvss-score.value-object.ts", "mappings": ";;;AAAA,oGAA+F;AAE/F;;GAEG;AACH,IAAY,WAKX;AALD,WAAY,WAAW;IACrB,yBAAU,CAAA;IACV,2BAAY,CAAA;IACZ,2BAAY,CAAA;IACZ,2BAAY,CAAA;AACd,CAAC,EALW,WAAW,2BAAX,WAAW,QAKtB;AAED;;GAEG;AACH,IAAY,YAMX;AAND,WAAY,YAAY;IACtB,6BAAa,CAAA;IACb,2BAAW,CAAA;IACX,iCAAiB,CAAA;IACjB,6BAAa,CAAA;IACb,qCAAqB,CAAA;AACvB,CAAC,EANW,YAAY,4BAAZ,YAAY,QAMvB;AA0BD;;;;;;;;;;;;;GAaG;AACH,MAAa,SAAU,SAAQ,mCAA+B;IAI5D,YAAY,KAAqB;QAC/B,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IAES,QAAQ;QAChB,sBAAsB;QACtB,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC/F,MAAM,IAAI,KAAK,CAAC,mCAAmC,SAAS,CAAC,SAAS,QAAQ,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC;QACvG,CAAC;QAED,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;gBACvG,MAAM,IAAI,KAAK,CAAC,uCAAuC,SAAS,CAAC,SAAS,QAAQ,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC;YAC3G,CAAC;QACH,CAAC;QAED,2CAA2C;QAC3C,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;YACjD,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;gBACjH,MAAM,IAAI,KAAK,CAAC,4CAA4C,SAAS,CAAC,SAAS,QAAQ,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC;YAChH,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,yBAAyB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;QAClE,CAAC;QAED,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,iCAAiC;QACjC,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;YAClD,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,GAAG,EAAE,EAAE,CAAC;gBAChF,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YAC1C,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC;gBAChE,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;IACH,CAAC;IAEO,oBAAoB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAE/C,2CAA2C;QAC3C,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAC5B,KAAK,WAAW,CAAC,EAAE;gBACjB,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;gBACpC,MAAM;YACR,KAAK,WAAW,CAAC,IAAI,CAAC;YACtB,KAAK,WAAW,CAAC,IAAI;gBACnB,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;gBACpC,MAAM;YACR,KAAK,WAAW,CAAC,IAAI;gBACnB,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;gBACpC,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,6BAA6B,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,MAAc;QAC3C,+CAA+C;QAC/C,MAAM,OAAO,GAAG,+DAA+D,CAAC;QAChF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,MAAc;QAC3C,+DAA+D;QAC/D,MAAM,OAAO,GAAG,yFAAyF,CAAC;QAC1G,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,MAAc;QAC3C,kFAAkF;QAClF,MAAM,OAAO,GAAG,yHAAyH,CAAC;QAC1I,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CACX,SAAiB,EACjB,OAAoB,EACpB,YAAoB,EACpB,OAAiF;QAEjF,OAAO,IAAI,SAAS,CAAC;YACnB,SAAS;YACT,OAAO;YACP,YAAY;YACZ,YAAY,EAAE,IAAI,IAAI,EAAE;YACxB,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CACf,SAAiB,EACjB,YAAoB,EACpB,OAAiF;QAEjF,OAAO,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CACf,SAAiB,EACjB,YAAoB,EACpB,OAAiF;QAEjF,OAAO,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,YAAoB,EAAE,MAAe;QAC3D,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACtD,MAAM,SAAS,GAAG,SAAS,CAAC,4BAA4B,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAEhF,OAAO,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;IACxE,CAAC;IAEO,MAAM,CAAC,aAAa,CAAC,YAAoB;QAC/C,IAAI,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC;YAAE,OAAO,WAAW,CAAC,IAAI,CAAC;QACjE,IAAI,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC;YAAE,OAAO,WAAW,CAAC,IAAI,CAAC;QACjE,IAAI,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC;YAAE,OAAO,WAAW,CAAC,IAAI,CAAC;QACjE,IAAI,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC;YAAE,OAAO,WAAW,CAAC,EAAE,CAAC;QAE3D,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;IACtE,CAAC;IAEO,MAAM,CAAC,4BAA4B,CAAC,YAAoB,EAAE,OAAoB;QACpF,sCAAsC;QACtC,uEAAuE;QAEvE,6EAA6E;QAC7E,6DAA6D;QAC7D,OAAO,GAAG,CAAC,CAAC,cAAc;IAC5B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,IAAI,CAAC,MAAM,CAAC,aAAa;YACzB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,WAAW;QACT,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEvC,IAAI,KAAK,KAAK,GAAG;YAAE,OAAO,YAAY,CAAC,IAAI,CAAC;QAC5C,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG;YAAE,OAAO,YAAY,CAAC,GAAG,CAAC;QAC1D,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG;YAAE,OAAO,YAAY,CAAC,MAAM,CAAC;QAC7D,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG;YAAE,OAAO,YAAY,CAAC,IAAI,CAAC;QAC3D,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;YAAE,OAAO,YAAY,CAAC,QAAQ,CAAC;QAEhE,OAAO,YAAY,CAAC,IAAI,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,WAAW,EAAE,KAAK,YAAY,CAAC,QAAQ,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,OAAO,QAAQ,KAAK,YAAY,CAAC,IAAI,IAAI,QAAQ,KAAK,YAAY,CAAC,QAAQ,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,OAAO,IAAI,CAAC,iBAAiB,EAAE,IAAI,GAAG,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,YAAY;QACV,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,YAAY,CAAC,QAAQ,CAAC,CAAC,OAAO,UAAU,CAAC;YAC9C,KAAK,YAAY,CAAC,IAAI,CAAC,CAAC,OAAO,MAAM,CAAC;YACtC,KAAK,YAAY,CAAC,MAAM,CAAC,CAAC,OAAO,QAAQ,CAAC;YAC1C,KAAK,YAAY,CAAC,GAAG,CAAC;YACtB,KAAK,YAAY,CAAC,IAAI,CAAC;YACvB;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,sCAAsC;IACjE,CAAC;IAED;;OAEG;IACH,sBAAsB;QAKpB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEpC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,YAAY,CAAC,QAAQ;gBACxB,OAAO;oBACL,SAAS,EAAE,IAAI;oBACf,IAAI,EAAE,CAAC;oBACP,WAAW,EAAE,gDAAgD;iBAC9D,CAAC;YACJ,KAAK,YAAY,CAAC,IAAI;gBACpB,OAAO;oBACL,SAAS,EAAE,KAAK;oBAChB,IAAI,EAAE,CAAC;oBACP,WAAW,EAAE,yCAAyC;iBACvD,CAAC;YACJ,KAAK,YAAY,CAAC,MAAM;gBACtB,OAAO;oBACL,SAAS,EAAE,KAAK;oBAChB,IAAI,EAAE,EAAE;oBACR,WAAW,EAAE,4CAA4C;iBAC1D,CAAC;YACJ,KAAK,YAAY,CAAC,GAAG;gBACnB,OAAO;oBACL,SAAS,EAAE,KAAK;oBAChB,IAAI,EAAE,EAAE;oBACR,WAAW,EAAE,yCAAyC;iBACvD,CAAC;YACJ;gBACE,OAAO;oBACL,SAAS,EAAE,KAAK;oBAChB,IAAI,EAAE,GAAG;oBACT,WAAW,EAAE,mCAAmC;iBACjD,CAAC;QACN,CAAC;IACH,CAAC;IAED;;OAEG;IACH,yBAAyB;QAOvB,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE3C,OAAO;YACL,MAAM,EAAE,UAAU;YAClB,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE;YACtB,KAAK,EAAE,UAAU;YACjB,IAAI,EAAE,UAAU;YAChB,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,GAAG;SAC3C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,aAAqB;QACrC,OAAO,IAAI,SAAS,CAAC;YACnB,GAAG,IAAI,CAAC,MAAM;YACd,aAAa;SACd,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,kBAA0B;QAC/C,OAAO,IAAI,SAAS,CAAC;YACnB,GAAG,IAAI,CAAC,MAAM;YACd,kBAAkB;SACnB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,mBAA2B,EAAE,WAAmB;QAC5D,OAAO,IAAI,SAAS,CAAC;YACnB,GAAG,IAAI,CAAC,MAAM;YACd,mBAAmB;YACnB,WAAW;SACZ,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,eAAe;QAWb,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAElD,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;YAChC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;YACxC,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAClD,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACxC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YAC9B,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,0BAA0B,EAAE,IAAI,CAAC,0BAA0B,EAAE;YAC7D,eAAe,EAAE,WAAW,CAAC,IAAI;SAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAiB;QAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,KAAK,CAAC,MAAM,CAAC,SAAS;YAChD,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,OAAO;YAC5C,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC;IAChE,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC;IACzF,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;YAChC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;YACxC,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAClD,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;YACtC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB;YACpD,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,WAAW,EAAE;YACrD,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YAC1B,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;YAC/B,UAAU,EAAE,IAAI,CAAC,yBAAyB,EAAE;YAC5C,WAAW,EAAE,IAAI,CAAC,sBAAsB,EAAE;SAC3C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,OAAO,IAAI,SAAS,CAAC;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,OAAO,EAAE,IAAI,CAAC,OAAsB;YACpC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS;YACzE,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,SAAiB,EAAE,OAAoB,EAAE,YAAoB;QAC1E,IAAI,CAAC;YACH,IAAI,SAAS,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;;AA/eH,8BAgfC;AA/eyB,mBAAS,GAAG,GAAG,CAAC;AAChB,mBAAS,GAAG,IAAI,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\cvss-score.value-object.ts"], "sourcesContent": ["import { BaseValueObject } from '../../../../../shared-kernel/value-objects/base-value-object';\r\n\r\n/**\r\n * CVSS Version\r\n */\r\nexport enum CVSSVersion {\r\n  V2 = '2.0',\r\n  V3_0 = '3.0',\r\n  V3_1 = '3.1',\r\n  V4_0 = '4.0',\r\n}\r\n\r\n/**\r\n * CVSS Severity Rating\r\n */\r\nexport enum CVSSSeverity {\r\n  NONE = 'none',\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CRITICAL = 'critical',\r\n}\r\n\r\n/**\r\n * CVSS Score Properties\r\n */\r\nexport interface CVSSScoreProps {\r\n  /** Base score (0.0 - 10.0) */\r\n  baseScore: number;\r\n  /** Temporal score (0.0 - 10.0) */\r\n  temporalScore?: number;\r\n  /** Environmental score (0.0 - 10.0) */\r\n  environmentalScore?: number;\r\n  /** CVSS version */\r\n  version: CVSSVersion;\r\n  /** CVSS vector string */\r\n  vectorString: string;\r\n  /** Exploitability subscore */\r\n  exploitabilityScore?: number;\r\n  /** Impact subscore */\r\n  impactScore?: number;\r\n  /** When the score was calculated */\r\n  calculatedAt?: Date;\r\n  /** Source of the CVSS score */\r\n  source?: string;\r\n}\r\n\r\n/**\r\n * CVSS Score Value Object\r\n * \r\n * Represents a Common Vulnerability Scoring System (CVSS) score with validation\r\n * and utility methods for vulnerability assessment and risk management.\r\n * \r\n * Key features:\r\n * - CVSS v2, v3.0, v3.1, and v4.0 support\r\n * - Vector string parsing and validation\r\n * - Severity classification\r\n * - Risk assessment utilities\r\n * - Temporal and environmental scoring\r\n * - Compliance and reporting support\r\n */\r\nexport class CVSSScore extends BaseValueObject<CVSSScoreProps> {\r\n  private static readonly MIN_SCORE = 0.0;\r\n  private static readonly MAX_SCORE = 10.0;\r\n\r\n  constructor(props: CVSSScoreProps) {\r\n    super(props);\r\n  }\r\n\r\n  protected validate(): void {\r\n    // Validate base score\r\n    if (this._value.baseScore < CVSSScore.MIN_SCORE || this._value.baseScore > CVSSScore.MAX_SCORE) {\r\n      throw new Error(`CVSS base score must be between ${CVSSScore.MIN_SCORE} and ${CVSSScore.MAX_SCORE}`);\r\n    }\r\n\r\n    // Validate temporal score if provided\r\n    if (this._value.temporalScore !== undefined) {\r\n      if (this._value.temporalScore < CVSSScore.MIN_SCORE || this._value.temporalScore > CVSSScore.MAX_SCORE) {\r\n        throw new Error(`CVSS temporal score must be between ${CVSSScore.MIN_SCORE} and ${CVSSScore.MAX_SCORE}`);\r\n      }\r\n    }\r\n\r\n    // Validate environmental score if provided\r\n    if (this._value.environmentalScore !== undefined) {\r\n      if (this._value.environmentalScore < CVSSScore.MIN_SCORE || this._value.environmentalScore > CVSSScore.MAX_SCORE) {\r\n        throw new Error(`CVSS environmental score must be between ${CVSSScore.MIN_SCORE} and ${CVSSScore.MAX_SCORE}`);\r\n      }\r\n    }\r\n\r\n    // Validate version\r\n    if (!Object.values(CVSSVersion).includes(this._value.version)) {\r\n      throw new Error(`Invalid CVSS version: ${this._value.version}`);\r\n    }\r\n\r\n    // Validate vector string format\r\n    if (!this._value.vectorString || this._value.vectorString.trim().length === 0) {\r\n      throw new Error('CVSS vector string is required');\r\n    }\r\n\r\n    this.validateVectorString();\r\n\r\n    // Validate subscores if provided\r\n    if (this._value.exploitabilityScore !== undefined) {\r\n      if (this._value.exploitabilityScore < 0 || this._value.exploitabilityScore > 10) {\r\n        throw new Error('CVSS exploitability score must be between 0 and 10');\r\n      }\r\n    }\r\n\r\n    if (this._value.impactScore !== undefined) {\r\n      if (this._value.impactScore < 0 || this._value.impactScore > 10) {\r\n        throw new Error('CVSS impact score must be between 0 and 10');\r\n      }\r\n    }\r\n  }\r\n\r\n  private validateVectorString(): void {\r\n    const vector = this._value.vectorString.trim();\r\n    \r\n    // Basic format validation based on version\r\n    switch (this._value.version) {\r\n      case CVSSVersion.V2:\r\n        this.validateV2VectorString(vector);\r\n        break;\r\n      case CVSSVersion.V3_0:\r\n      case CVSSVersion.V3_1:\r\n        this.validateV3VectorString(vector);\r\n        break;\r\n      case CVSSVersion.V4_0:\r\n        this.validateV4VectorString(vector);\r\n        break;\r\n      default:\r\n        throw new Error(`Unsupported CVSS version: ${this._value.version}`);\r\n    }\r\n  }\r\n\r\n  private validateV2VectorString(vector: string): void {\r\n    // CVSS v2 format: (AV:L/AC:M/Au:N/C:P/I:P/A:P)\r\n    const v2Regex = /^\\(AV:[LAN]\\/AC:[HML]\\/Au:[MSN]\\/C:[NPC]\\/I:[NPC]\\/A:[NPC]\\)$/;\r\n    if (!v2Regex.test(vector)) {\r\n      throw new Error('Invalid CVSS v2 vector string format');\r\n    }\r\n  }\r\n\r\n  private validateV3VectorString(vector: string): void {\r\n    // CVSS v3 format: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H\r\n    const v3Regex = /^CVSS:3\\.[01]\\/AV:[NALP]\\/AC:[LH]\\/PR:[NLH]\\/UI:[NR]\\/S:[UC]\\/C:[NLH]\\/I:[NLH]\\/A:[NLH]/;\r\n    if (!v3Regex.test(vector)) {\r\n      throw new Error('Invalid CVSS v3 vector string format');\r\n    }\r\n  }\r\n\r\n  private validateV4VectorString(vector: string): void {\r\n    // CVSS v4 format: CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N\r\n    const v4Regex = /^CVSS:4\\.0\\/AV:[NALP]\\/AC:[LH]\\/AT:[NP]\\/PR:[NLH]\\/UI:[NPA]\\/VC:[HLN]\\/VI:[HLN]\\/VA:[HLN]\\/SC:[HLN]\\/SI:[HLN]\\/SA:[HLN]/;\r\n    if (!v4Regex.test(vector)) {\r\n      throw new Error('Invalid CVSS v4 vector string format');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a CVSS score with base score and vector\r\n   */\r\n  static create(\r\n    baseScore: number,\r\n    version: CVSSVersion,\r\n    vectorString: string,\r\n    options?: Partial<Omit<CVSSScoreProps, 'baseScore' | 'version' | 'vectorString'>>\r\n  ): CVSSScore {\r\n    return new CVSSScore({\r\n      baseScore,\r\n      version,\r\n      vectorString,\r\n      calculatedAt: new Date(),\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create CVSS v3.1 score\r\n   */\r\n  static createV3_1(\r\n    baseScore: number,\r\n    vectorString: string,\r\n    options?: Partial<Omit<CVSSScoreProps, 'baseScore' | 'version' | 'vectorString'>>\r\n  ): CVSSScore {\r\n    return CVSSScore.create(baseScore, CVSSVersion.V3_1, vectorString, options);\r\n  }\r\n\r\n  /**\r\n   * Create CVSS v4.0 score\r\n   */\r\n  static createV4_0(\r\n    baseScore: number,\r\n    vectorString: string,\r\n    options?: Partial<Omit<CVSSScoreProps, 'baseScore' | 'version' | 'vectorString'>>\r\n  ): CVSSScore {\r\n    return CVSSScore.create(baseScore, CVSSVersion.V4_0, vectorString, options);\r\n  }\r\n\r\n  /**\r\n   * Parse CVSS score from vector string\r\n   */\r\n  static fromVectorString(vectorString: string, source?: string): CVSSScore {\r\n    const version = CVSSScore.detectVersion(vectorString);\r\n    const baseScore = CVSSScore.calculateBaseScoreFromVector(vectorString, version);\r\n    \r\n    return CVSSScore.create(baseScore, version, vectorString, { source });\r\n  }\r\n\r\n  private static detectVersion(vectorString: string): CVSSVersion {\r\n    if (vectorString.startsWith('CVSS:4.0')) return CVSSVersion.V4_0;\r\n    if (vectorString.startsWith('CVSS:3.1')) return CVSSVersion.V3_1;\r\n    if (vectorString.startsWith('CVSS:3.0')) return CVSSVersion.V3_0;\r\n    if (vectorString.startsWith('(AV:')) return CVSSVersion.V2;\r\n    \r\n    throw new Error('Unable to detect CVSS version from vector string');\r\n  }\r\n\r\n  private static calculateBaseScoreFromVector(vectorString: string, version: CVSSVersion): number {\r\n    // This is a simplified implementation\r\n    // In practice, you would implement the full CVSS calculation algorithm\r\n    \r\n    // For now, return a placeholder that would be calculated based on the vector\r\n    // This should be replaced with actual CVSS calculation logic\r\n    return 7.5; // Placeholder\r\n  }\r\n\r\n  /**\r\n   * Get base score\r\n   */\r\n  get baseScore(): number {\r\n    return this._value.baseScore;\r\n  }\r\n\r\n  /**\r\n   * Get temporal score\r\n   */\r\n  get temporalScore(): number | undefined {\r\n    return this._value.temporalScore;\r\n  }\r\n\r\n  /**\r\n   * Get environmental score\r\n   */\r\n  get environmentalScore(): number | undefined {\r\n    return this._value.environmentalScore;\r\n  }\r\n\r\n  /**\r\n   * Get CVSS version\r\n   */\r\n  get version(): CVSSVersion {\r\n    return this._value.version;\r\n  }\r\n\r\n  /**\r\n   * Get vector string\r\n   */\r\n  get vectorString(): string {\r\n    return this._value.vectorString;\r\n  }\r\n\r\n  /**\r\n   * Get exploitability score\r\n   */\r\n  get exploitabilityScore(): number | undefined {\r\n    return this._value.exploitabilityScore;\r\n  }\r\n\r\n  /**\r\n   * Get impact score\r\n   */\r\n  get impactScore(): number | undefined {\r\n    return this._value.impactScore;\r\n  }\r\n\r\n  /**\r\n   * Get calculation date\r\n   */\r\n  get calculatedAt(): Date | undefined {\r\n    return this._value.calculatedAt;\r\n  }\r\n\r\n  /**\r\n   * Get source\r\n   */\r\n  get source(): string | undefined {\r\n    return this._value.source;\r\n  }\r\n\r\n  /**\r\n   * Get the effective score (environmental > temporal > base)\r\n   */\r\n  getEffectiveScore(): number {\r\n    return this._value.environmentalScore || \r\n           this._value.temporalScore || \r\n           this._value.baseScore;\r\n  }\r\n\r\n  /**\r\n   * Get severity rating based on score\r\n   */\r\n  getSeverity(): CVSSSeverity {\r\n    const score = this.getEffectiveScore();\r\n    \r\n    if (score === 0.0) return CVSSSeverity.NONE;\r\n    if (score >= 0.1 && score <= 3.9) return CVSSSeverity.LOW;\r\n    if (score >= 4.0 && score <= 6.9) return CVSSSeverity.MEDIUM;\r\n    if (score >= 7.0 && score <= 8.9) return CVSSSeverity.HIGH;\r\n    if (score >= 9.0 && score <= 10.0) return CVSSSeverity.CRITICAL;\r\n    \r\n    return CVSSSeverity.NONE;\r\n  }\r\n\r\n  /**\r\n   * Check if score is critical\r\n   */\r\n  isCritical(): boolean {\r\n    return this.getSeverity() === CVSSSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if score is high or critical\r\n   */\r\n  isHighOrCritical(): boolean {\r\n    const severity = this.getSeverity();\r\n    return severity === CVSSSeverity.HIGH || severity === CVSSSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if score requires immediate attention\r\n   */\r\n  requiresImmediateAttention(): boolean {\r\n    return this.getEffectiveScore() >= 7.0;\r\n  }\r\n\r\n  /**\r\n   * Get risk level for business impact\r\n   */\r\n  getRiskLevel(): 'low' | 'medium' | 'high' | 'critical' {\r\n    const severity = this.getSeverity();\r\n    switch (severity) {\r\n      case CVSSSeverity.CRITICAL: return 'critical';\r\n      case CVSSSeverity.HIGH: return 'high';\r\n      case CVSSSeverity.MEDIUM: return 'medium';\r\n      case CVSSSeverity.LOW:\r\n      case CVSSSeverity.NONE:\r\n      default:\r\n        return 'low';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get priority score (1-10) for remediation\r\n   */\r\n  getPriorityScore(): number {\r\n    const score = this.getEffectiveScore();\r\n    return Math.ceil(score); // Convert 0-10 scale to 1-10 priority\r\n  }\r\n\r\n  /**\r\n   * Get remediation timeline based on score\r\n   */\r\n  getRemediationTimeline(): {\r\n    immediate: boolean;\r\n    days: number;\r\n    description: string;\r\n  } {\r\n    const severity = this.getSeverity();\r\n    \r\n    switch (severity) {\r\n      case CVSSSeverity.CRITICAL:\r\n        return {\r\n          immediate: true,\r\n          days: 1,\r\n          description: 'Immediate remediation required within 24 hours',\r\n        };\r\n      case CVSSSeverity.HIGH:\r\n        return {\r\n          immediate: false,\r\n          days: 7,\r\n          description: 'High priority remediation within 7 days',\r\n        };\r\n      case CVSSSeverity.MEDIUM:\r\n        return {\r\n          immediate: false,\r\n          days: 30,\r\n          description: 'Medium priority remediation within 30 days',\r\n        };\r\n      case CVSSSeverity.LOW:\r\n        return {\r\n          immediate: false,\r\n          days: 90,\r\n          description: 'Low priority remediation within 90 days',\r\n        };\r\n      default:\r\n        return {\r\n          immediate: false,\r\n          days: 365,\r\n          description: 'No immediate remediation required',\r\n        };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get compliance requirements based on score\r\n   */\r\n  getComplianceRequirements(): {\r\n    pciDss: boolean;\r\n    sox: boolean;\r\n    hipaa: boolean;\r\n    gdpr: boolean;\r\n    reporting: boolean;\r\n  } {\r\n    const isHighRisk = this.isHighOrCritical();\r\n    \r\n    return {\r\n      pciDss: isHighRisk,\r\n      sox: this.isCritical(),\r\n      hipaa: isHighRisk,\r\n      gdpr: isHighRisk,\r\n      reporting: this.getEffectiveScore() >= 4.0,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a new score with temporal metrics\r\n   */\r\n  withTemporalScore(temporalScore: number): CVSSScore {\r\n    return new CVSSScore({\r\n      ...this._value,\r\n      temporalScore,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new score with environmental metrics\r\n   */\r\n  withEnvironmentalScore(environmentalScore: number): CVSSScore {\r\n    return new CVSSScore({\r\n      ...this._value,\r\n      environmentalScore,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new score with subscores\r\n   */\r\n  withSubscores(exploitabilityScore: number, impactScore: number): CVSSScore {\r\n    return new CVSSScore({\r\n      ...this._value,\r\n      exploitabilityScore,\r\n      impactScore,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get score summary for reporting\r\n   */\r\n  getScoreSummary(): {\r\n    baseScore: number;\r\n    temporalScore?: number;\r\n    environmentalScore?: number;\r\n    effectiveScore: number;\r\n    severity: string;\r\n    riskLevel: string;\r\n    priorityScore: number;\r\n    requiresImmediateAttention: boolean;\r\n    remediationDays: number;\r\n  } {\r\n    const remediation = this.getRemediationTimeline();\r\n    \r\n    return {\r\n      baseScore: this._value.baseScore,\r\n      temporalScore: this._value.temporalScore,\r\n      environmentalScore: this._value.environmentalScore,\r\n      effectiveScore: this.getEffectiveScore(),\r\n      severity: this.getSeverity(),\r\n      riskLevel: this.getRiskLevel(),\r\n      priorityScore: this.getPriorityScore(),\r\n      requiresImmediateAttention: this.requiresImmediateAttention(),\r\n      remediationDays: remediation.days,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Compare CVSS scores\r\n   */\r\n  public equals(other?: CVSSScore): boolean {\r\n    if (!other) {\r\n      return false;\r\n    }\r\n\r\n    if (this === other) {\r\n      return true;\r\n    }\r\n\r\n    return this._value.baseScore === other._value.baseScore &&\r\n           this._value.version === other._value.version &&\r\n           this._value.vectorString === other._value.vectorString;\r\n  }\r\n\r\n  /**\r\n   * Get string representation\r\n   */\r\n  public toString(): string {\r\n    return `CVSS ${this._value.version}: ${this._value.baseScore} (${this.getSeverity()})`;\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      baseScore: this._value.baseScore,\r\n      temporalScore: this._value.temporalScore,\r\n      environmentalScore: this._value.environmentalScore,\r\n      version: this._value.version,\r\n      vectorString: this._value.vectorString,\r\n      exploitabilityScore: this._value.exploitabilityScore,\r\n      impactScore: this._value.impactScore,\r\n      calculatedAt: this._value.calculatedAt?.toISOString(),\r\n      source: this._value.source,\r\n      summary: this.getScoreSummary(),\r\n      compliance: this.getComplianceRequirements(),\r\n      remediation: this.getRemediationTimeline(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create CVSSScore from JSON\r\n   */\r\n  static fromJSON(json: Record<string, any>): CVSSScore {\r\n    return new CVSSScore({\r\n      baseScore: json.baseScore,\r\n      temporalScore: json.temporalScore,\r\n      environmentalScore: json.environmentalScore,\r\n      version: json.version as CVSSVersion,\r\n      vectorString: json.vectorString,\r\n      exploitabilityScore: json.exploitabilityScore,\r\n      impactScore: json.impactScore,\r\n      calculatedAt: json.calculatedAt ? new Date(json.calculatedAt) : undefined,\r\n      source: json.source,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Validate CVSS score format without creating instance\r\n   */\r\n  static isValid(baseScore: number, version: CVSSVersion, vectorString: string): boolean {\r\n    try {\r\n      new CVSSScore({ baseScore, version, vectorString });\r\n      return true;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}