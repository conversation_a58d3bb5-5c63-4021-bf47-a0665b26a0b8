{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\policy-configuration.ts", "mappings": ";;;AAAA,8EAA0E;AAK1E,oGAAgG;AA0FhG,IAAY,iBAUX;AAVD,WAAY,iBAAiB;IAC3B,sCAAiB,CAAA;IACjB,sCAAiB,CAAA;IACjB,wCAAmB,CAAA;IACnB,oCAAe,CAAA;IACf,sCAAiB,CAAA;IACjB,kCAAa,CAAA;IACb,8CAAyB,CAAA;IACzB,oCAAe,CAAA;IACf,gCAAW,CAAA;AACb,CAAC,EAVW,iBAAiB,iCAAjB,iBAAiB,QAU5B;AAED,IAAY,YASX;AATD,WAAY,YAAY;IACtB,iDAAiC,CAAA;IACjC,+CAA+B,CAAA;IAC/B,mDAAmC,CAAA;IACnC,qDAAqC,CAAA;IACrC,uDAAuC,CAAA;IACvC,2DAA2C,CAAA;IAC3C,yCAAyB,CAAA;IACzB,iCAAiB,CAAA;AACnB,CAAC,EATW,YAAY,4BAAZ,YAAY,QASvB;AAiBD,MAAa,mBAAoB,SAAQ,wBAAe;IAQtD,YAAY,KAQX;QACC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;QAEvB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAE1B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,aAAa,CAAC;QAC1C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;QAClC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,cAAc,CAAC;QAC5C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;IAChC,CAAC;IAEM,MAAM,CAAC,MAAM,CAAC,KAGpB;QACC,OAAO,IAAI,mBAAmB,CAAC;YAC7B,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,QAAQ,EAAE,mBAAmB,CAAC,kBAAkB,EAAE;YAClD,aAAa,EAAE,mBAAmB,CAAC,uBAAuB,EAAE;YAC5D,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,cAAc,EAAE,KAAK,CAAC,SAAS;YAC/B,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;IACL,CAAC;IAEM,cAAc,CAAC,QAA8C,EAAE,UAAkB;QACtF,MAAM,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,EAAE,CAAC;QAC3D,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;QACjC,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEM,eAAe,CAAC,QAAsB,EAAE,UAAkB;QAC/D,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAEpC,mCAAmC;QACnC,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;YACxD,MAAM,IAAI,0CAAmB,CAAC,0BAA0B,QAAQ,CAAC,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC;QAC7F,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnC,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEM,kBAAkB,CAAC,UAAkB,EAAE,OAA8B,EAAE,UAAkB;QAC9F,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,CAAC;QAC9E,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC;YACzB,MAAM,IAAI,0CAAmB,CAAC,0BAA0B,UAAU,aAAa,EAAE,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,MAAM,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,CAAC;QAC9E,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAE3C,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,eAAe,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEM,kBAAkB,CAAC,UAAkB,EAAE,UAAkB;QAC9D,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,CAAC;QAC9E,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC;YACzB,MAAM,IAAI,0CAAmB,CAAC,0BAA0B,UAAU,aAAa,EAAE,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEM,0BAA0B,CAAC,QAAsB;QACtD,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IAClE,CAAC;IAEM,wBAAwB,CAAC,UAAkB,EAAE,UAA+B;QACjF,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,CAAC;QACpE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0CAAmB,CAAC,0BAA0B,UAAU,aAAa,EAAE,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,+BAA+B;QAC/B,MAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACnE,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,UAAU,CAAC,EAAE,CAAC;gBAChC,MAAM,IAAI,0CAAmB,CAAC,uBAAuB,KAAK,CAAC,IAAI,cAAc,EAAE,EAAE,CAAC,CAAC;YACrF,CAAC;QACH,CAAC;QAED,sCAAsC;QACtC,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YACxC,IAAI,KAAK,CAAC,IAAI,IAAI,UAAU,EAAE,CAAC;gBAC7B,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,mDAAmD;QACnD,IAAI,aAAa,GAAG,QAAQ,CAAC,QAAQ,CAAC;QACtC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACtD,MAAM,WAAW,GAAG,KAAK,GAAG,IAAI,CAAC;YACjC,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7F,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAEM,qBAAqB,CAAC,SAAiB;QAC5C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACrC,OAAO,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,UAAU;QACf,qEAAqE;QACrE,sCAAsC;QACtC,OAAO;YACL,aAAa,EAAE,CAAC;YAChB,cAAc,EAAE,CAAC;YACjB,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,CAAC;YACd,kBAAkB,EAAE,CAAC;YACrB,iBAAiB,EAAE,CAAC;YACpB,qBAAqB,EAAE,CAAC;YACxB,gBAAgB,EAAE,EAAE;SACrB,CAAC;IACJ,CAAC;IAEO,0BAA0B,CAAC,SAAc;QAC/C,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oCAAoC;QACpC,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAC1E,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEpC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,6BAA6B;QAC7B,IAAI,KAAK,IAAI,SAAS,EAAE,CAAC;YACvB,OAAO,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC;gBAC5B,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,IAAI,IAAI,SAAS,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC3B,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,CAAC;QAChF,CAAC;QAED,IAAI,KAAK,IAAI,SAAS,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACxD,CAAC;QAED,4BAA4B;QAC5B,IAAI,OAAO,IAAI,SAAS,IAAI,UAAU,IAAI,SAAS,IAAI,OAAO,IAAI,SAAS,EAAE,CAAC;YAC5E,MAAM,mBAAmB,GAAG;gBAC1B,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,EAAE,WAAW;gBAC9D,cAAc,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO;aAC3C,CAAC;YACF,OAAO,OAAO,SAAS,CAAC,KAAK,KAAK,QAAQ;gBACnC,mBAAmB,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,sBAAsB,CAAC,SAAwB,EAAE,KAAU;QACjE,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,iBAAiB,CAAC,MAAM;gBAC3B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;oBAC9B,MAAM,IAAI,0CAAmB,CAAC,cAAc,SAAS,CAAC,IAAI,oBAAoB,EAAE,EAAE,CAAC,CAAC;gBACtF,CAAC;gBACD,MAAM;YACR,KAAK,iBAAiB,CAAC,MAAM;gBAC3B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;oBAC9B,MAAM,IAAI,0CAAmB,CAAC,cAAc,SAAS,CAAC,IAAI,oBAAoB,EAAE,EAAE,CAAC,CAAC;gBACtF,CAAC;gBACD,MAAM;YACR,KAAK,iBAAiB,CAAC,OAAO;gBAC5B,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;oBAC/B,MAAM,IAAI,0CAAmB,CAAC,cAAc,SAAS,CAAC,IAAI,qBAAqB,EAAE,EAAE,CAAC,CAAC;gBACvF,CAAC;gBACD,MAAM;YACR,KAAK,iBAAiB,CAAC,KAAK;gBAC1B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC1B,MAAM,IAAI,0CAAmB,CAAC,cAAc,SAAS,CAAC,IAAI,oBAAoB,EAAE,EAAE,CAAC,CAAC;gBACtF,CAAC;gBACD,MAAM;YACR,KAAK,iBAAiB,CAAC,IAAI;gBACzB,IAAI,SAAS,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC5D,MAAM,IAAI,0CAAmB,CAAC,cAAc,SAAS,CAAC,IAAI,qBAAqB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gBACrH,CAAC;gBACD,MAAM;YACR,KAAK,iBAAiB,CAAC,UAAU;gBAC/B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;oBAClC,MAAM,IAAI,0CAAmB,CAAC,cAAc,SAAS,CAAC,IAAI,8BAA8B,EAAE,EAAE,CAAC,CAAC;gBAChG,CAAC;gBACD,MAAM;YACR,KAAK,iBAAiB,CAAC,KAAK;gBAC1B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC9B,MAAM,IAAI,0CAAmB,CAAC,cAAc,SAAS,CAAC,IAAI,iCAAiC,EAAE,EAAE,CAAC,CAAC;gBACnG,CAAC;gBACD,MAAM;YACR,KAAK,iBAAiB,CAAC,GAAG;gBACxB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC5B,MAAM,IAAI,0CAAmB,CAAC,cAAc,SAAS,CAAC,IAAI,uBAAuB,EAAE,EAAE,CAAC,CAAC;gBACzF,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,KAAa;QACpC,MAAM,SAAS,GAAG,6FAA6F,CAAC;QAChH,MAAM,SAAS,GAAG,4CAA4C,CAAC;QAC/D,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACxD,CAAC;IAEO,YAAY,CAAC,KAAa;QAChC,MAAM,UAAU,GAAG,4BAA4B,CAAC;QAChD,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAEO,UAAU,CAAC,KAAa;QAC9B,IAAI,CAAC;YACH,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,kBAAkB;QAC/B,OAAO;YACL,wBAAwB,EAAE,KAAK;YAC/B,iCAAiC,EAAE,IAAI;YACvC,iBAAiB,EAAE,GAAG;YACtB,mBAAmB,EAAE,EAAE;YACvB,kBAAkB,EAAE,GAAG;YACvB,oBAAoB,EAAE;gBACpB,wBAAwB,EAAE,IAAI;gBAC9B,wBAAwB,EAAE,KAAK;gBAC/B,0BAA0B,EAAE,KAAK;gBACjC,eAAe,EAAE,EAAE;gBACnB,aAAa,EAAE,EAAE;gBACjB,WAAW,EAAE,EAAE;gBACf,sBAAsB,EAAE;oBACtB,gBAAgB,EAAE,CAAC;oBACnB,YAAY,EAAE,CAAC;oBACf,gBAAgB,EAAE,EAAE;iBACrB;aACF;YACD,kBAAkB,EAAE;gBAClB,oBAAoB,EAAE,KAAK;gBAC3B,gBAAgB,EAAE,EAAE;gBACpB,kBAAkB,EAAE,CAAC;gBACrB,kBAAkB,EAAE;oBAClB,aAAa,EAAE,EAAE;oBACjB,aAAa,EAAE,EAAE;oBACjB,aAAa,EAAE,GAAG;iBACnB;aACF;YACD,mBAAmB,EAAE;gBACnB,eAAe,EAAE;oBACf,OAAO,EAAE,KAAK;oBACd,UAAU,EAAE,EAAE;iBACf;gBACD,oBAAoB,EAAE;oBACpB,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,MAAM;iBACf;gBACD,kBAAkB,EAAE;oBAClB,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE,EAAE;oBACb,OAAO,EAAE,EAAE;oBACX,oBAAoB,EAAE,EAAE;iBACzB;aACF;SACF,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,uBAAuB;QACpC,OAAO;YACL;gBACE,EAAE,EAAE,uBAAuB;gBAC3B,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,wDAAwD;gBACrE,QAAQ,EAAE,YAAY,CAAC,cAAc;gBACrC,QAAQ,EAAE,uKAAuK;gBACjL,UAAU,EAAE;oBACV;wBACE,IAAI,EAAE,aAAa;wBACnB,IAAI,EAAE,iBAAiB,CAAC,MAAM;wBAC9B,WAAW,EAAE,qDAAqD;wBAClE,QAAQ,EAAE,IAAI;wBACd,YAAY,EAAE,CAAC;qBAChB;iBACF;gBACD,eAAe,EAAE,EAAE;gBACnB,eAAe,EAAE,MAAM;gBACvB,IAAI,EAAE,CAAC,gBAAgB,EAAE,aAAa,CAAC;aACxC;YACD;gBACE,EAAE,EAAE,sBAAsB;gBAC1B,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,iDAAiD;gBAC9D,QAAQ,EAAE,YAAY,CAAC,gBAAgB;gBACvC,QAAQ,EAAE,8EAA8E;gBACxF,UAAU,EAAE;oBACV;wBACE,IAAI,EAAE,eAAe;wBACrB,IAAI,EAAE,iBAAiB,CAAC,KAAK;wBAC7B,WAAW,EAAE,iCAAiC;wBAC9C,QAAQ,EAAE,IAAI;wBACd,YAAY,EAAE,EAAE;qBACjB;iBACF;gBACD,eAAe,EAAE,EAAE;gBACnB,eAAe,EAAE,UAAU;gBAC3B,IAAI,EAAE,CAAC,SAAS,EAAE,qBAAqB,CAAC;aACzC;SACF,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,KAAU;QAC9B,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACpB,MAAM,IAAI,0CAAmB,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACrB,MAAM,IAAI,0CAAmB,CAAC,gCAAgC,EAAE,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACpB,MAAM,IAAI,0CAAmB,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEtC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;YACxC,MAAM,IAAI,0CAAmB,CAAC,iCAAiC,EAAE,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,QAAsB,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC/F,CAAC;IAEO,gBAAgB,CAAC,QAAqC;QAC5D,IAAI,OAAO,QAAQ,CAAC,wBAAwB,KAAK,SAAS,EAAE,CAAC;YAC3D,MAAM,IAAI,0CAAmB,CAAC,4CAA4C,EAAE,EAAE,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,OAAO,QAAQ,CAAC,iBAAiB,KAAK,QAAQ,IAAI,QAAQ,CAAC,iBAAiB,IAAI,CAAC,EAAE,CAAC;YACtF,MAAM,IAAI,0CAAmB,CAAC,6CAA6C,EAAE,EAAE,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,OAAO,QAAQ,CAAC,kBAAkB,KAAK,QAAQ,IAAI,QAAQ,CAAC,kBAAkB,IAAI,CAAC,EAAE,CAAC;YACxF,MAAM,IAAI,0CAAmB,CAAC,8CAA8C,EAAE,EAAE,CAAC,CAAC;QACpF,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,QAAsB;QACjD,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,0CAAmB,CAAC,8BAA8B,EAAE,EAAE,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxD,MAAM,IAAI,0CAAmB,CAAC,gCAAgC,EAAE,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7D,MAAM,IAAI,0CAAmB,CAAC,iCAAiC,EAAE,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,0CAAmB,CAAC,qCAAqC,EAAE,EAAE,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACxC,MAAM,IAAI,0CAAmB,CAAC,2CAA2C,EAAE,EAAE,CAAC,CAAC;QACjF,CAAC;QAED,kFAAkF;QAClF,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3F,8EAA8E;YAC9E,mCAAmC;YACnC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtC,IAAI,CAAC;oBACH,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAChC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,IAAI,0CAAmB,CAAC,4CAA4C,EAAE,EAAE,CAAC,CAAC;gBAClF,CAAC;YACH,CAAC;QACH,CAAC;QAED,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAClC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClD,MAAM,IAAI,0CAAmB,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;YAClE,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3D,MAAM,IAAI,0CAAmB,CAAC,kCAAkC,EAAE,EAAE,CAAC,CAAC;YACxE,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK;QACX,qCAAqC;QACrC,+DAA+D;IACjE,CAAC;IAED,UAAU;IACV,IAAW,QAAQ,KAAe,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC1D,IAAW,QAAQ,KAAkC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IACpF,IAAW,aAAa,KAAqB,OAAO,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;IAC/E,IAAW,SAAS,KAAa,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IAC1D,IAAW,cAAc,KAAa,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IACpE,IAAW,OAAO,KAAa,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;CACvD;AAxbD,kDAwbC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\policy-configuration.ts"], "sourcesContent": ["import { BaseEntity } from '../../../../shared-kernel/domain/base-entity';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { Timestamp } from '../../../../shared-kernel/value-objects/timestamp.value-object';\r\nimport { UserId } from '../../../../shared-kernel/value-objects/user-id.value-object';\r\nimport { TenantId } from '../../../../shared-kernel/value-objects/tenant-id.value-object';\r\nimport { ValidationException } from '../../../../shared-kernel/exceptions/validation.exception';\r\n\r\nexport interface PolicyConfigurationSettings {\r\n  enableAutomaticExecution: boolean;\r\n  requireApprovalForCriticalActions: boolean;\r\n  maxRulesPerPolicy: number;\r\n  defaultRulePriority: number;\r\n  auditRetentionDays: number;\r\n  notificationSettings: NotificationSettings;\r\n  escalationSettings: EscalationSettings;\r\n  integrationSettings: IntegrationSettings;\r\n}\r\n\r\nexport interface NotificationSettings {\r\n  enableEmailNotifications: boolean;\r\n  enableSlackNotifications: boolean;\r\n  enableWebhookNotifications: boolean;\r\n  emailRecipients: string[];\r\n  slackChannels: string[];\r\n  webhookUrls: string[];\r\n  notificationThresholds: {\r\n    criticalFindings: number;\r\n    highFindings: number;\r\n    policyViolations: number;\r\n  };\r\n}\r\n\r\nexport interface EscalationSettings {\r\n  enableAutoEscalation: boolean;\r\n  escalationLevels: EscalationLevel[];\r\n  maxEscalationLevel: number;\r\n  escalationTimeouts: {\r\n    level1Minutes: number;\r\n    level2Minutes: number;\r\n    level3Minutes: number;\r\n  };\r\n}\r\n\r\nexport interface EscalationLevel {\r\n  level: number;\r\n  name: string;\r\n  recipients: UserId[];\r\n  actions: string[];\r\n  timeoutMinutes: number;\r\n}\r\n\r\nexport interface IntegrationSettings {\r\n  siemIntegration: {\r\n    enabled: boolean;\r\n    endpoint?: string;\r\n    apiKey?: string;\r\n    eventTypes: string[];\r\n  };\r\n  ticketingIntegration: {\r\n    enabled: boolean;\r\n    system: string; // 'jira', 'servicenow', etc.\r\n    endpoint?: string;\r\n    credentials?: Record<string, string>;\r\n    defaultProject?: string;\r\n  };\r\n  threatIntelligence: {\r\n    enabled: boolean;\r\n    providers: string[];\r\n    apiKeys: Record<string, string>;\r\n    updateFrequencyHours: number;\r\n  };\r\n}\r\n\r\nexport interface RuleTemplate {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  category: RuleCategory;\r\n  template: string; // JSON template for rule condition\r\n  parameters: RuleParameter[];\r\n  defaultPriority: number;\r\n  defaultSeverity: string;\r\n  tags: string[];\r\n}\r\n\r\nexport interface RuleParameter {\r\n  name: string;\r\n  type: RuleParameterType;\r\n  description: string;\r\n  required: boolean;\r\n  defaultValue?: any;\r\n  validationRules?: string[];\r\n  options?: string[]; // For enum-type parameters\r\n}\r\n\r\nexport enum RuleParameterType {\r\n  STRING = 'STRING',\r\n  NUMBER = 'NUMBER',\r\n  BOOLEAN = 'BOOLEAN',\r\n  ARRAY = 'ARRAY',\r\n  OBJECT = 'OBJECT',\r\n  ENUM = 'ENUM',\r\n  IP_ADDRESS = 'IP_ADDRESS',\r\n  EMAIL = 'EMAIL',\r\n  URL = 'URL'\r\n}\r\n\r\nexport enum RuleCategory {\r\n  AUTHENTICATION = 'AUTHENTICATION',\r\n  AUTHORIZATION = 'AUTHORIZATION',\r\n  DATA_PROTECTION = 'DATA_PROTECTION',\r\n  NETWORK_SECURITY = 'NETWORK_SECURITY',\r\n  MALWARE_DETECTION = 'MALWARE_DETECTION',\r\n  INTRUSION_DETECTION = 'INTRUSION_DETECTION',\r\n  COMPLIANCE = 'COMPLIANCE',\r\n  CUSTOM = 'CUSTOM'\r\n}\r\n\r\nexport interface PolicyMetrics {\r\n  totalPolicies: number;\r\n  activePolicies: number;\r\n  totalRules: number;\r\n  activeRules: number;\r\n  evaluationsLast24h: number;\r\n  violationsLast24h: number;\r\n  averageEvaluationTime: number;\r\n  topViolatedRules: Array<{\r\n    ruleId: string;\r\n    ruleName: string;\r\n    violationCount: number;\r\n  }>;\r\n}\r\n\r\nexport class PolicyConfiguration extends BaseEntity<any> {\r\n  private readonly _tenantId: TenantId;\r\n  private _settings: PolicyConfigurationSettings;\r\n  private _ruleTemplates: RuleTemplate[];\r\n  private readonly _createdBy: UserId;\r\n  private _lastModifiedBy: UserId;\r\n  private _version: string;\r\n\r\n  constructor(props: {\r\n    id?: UniqueEntityId;\r\n    tenantId: TenantId;\r\n    settings: PolicyConfigurationSettings;\r\n    ruleTemplates: RuleTemplate[];\r\n    createdBy: UserId;\r\n    lastModifiedBy: UserId;\r\n    version: string;\r\n  }) {\r\n    super(props, props.id);\r\n    \r\n    this.validateProps(props);\r\n    \r\n    this._tenantId = props.tenantId;\r\n    this._settings = props.settings;\r\n    this._ruleTemplates = props.ruleTemplates;\r\n    this._createdBy = props.createdBy;\r\n    this._lastModifiedBy = props.lastModifiedBy;\r\n    this._version = props.version;\r\n  }\r\n\r\n  public static create(props: {\r\n    tenantId: TenantId;\r\n    createdBy: UserId;\r\n  }): PolicyConfiguration {\r\n    return new PolicyConfiguration({\r\n      tenantId: props.tenantId,\r\n      settings: PolicyConfiguration.getDefaultSettings(),\r\n      ruleTemplates: PolicyConfiguration.getDefaultRuleTemplates(),\r\n      createdBy: props.createdBy,\r\n      lastModifiedBy: props.createdBy,\r\n      version: '1.0.0'\r\n    });\r\n  }\r\n\r\n  public updateSettings(settings: Partial<PolicyConfigurationSettings>, modifiedBy: UserId): void {\r\n    const updatedSettings = { ...this._settings, ...settings };\r\n    this.validateSettings(updatedSettings);\r\n    \r\n    this._settings = updatedSettings;\r\n    this._lastModifiedBy = modifiedBy;\r\n    this.touch();\r\n  }\r\n\r\n  public addRuleTemplate(template: RuleTemplate, modifiedBy: UserId): void {\r\n    this.validateRuleTemplate(template);\r\n    \r\n    // Check for duplicate template IDs\r\n    if (this._ruleTemplates.some(t => t.id === template.id)) {\r\n      throw new ValidationException(`Rule template with ID '${template.id}' already exists`, []);\r\n    }\r\n\r\n    this._ruleTemplates.push(template);\r\n    this._lastModifiedBy = modifiedBy;\r\n    this.touch();\r\n  }\r\n\r\n  public updateRuleTemplate(templateId: string, updates: Partial<RuleTemplate>, modifiedBy: UserId): void {\r\n    const templateIndex = this._ruleTemplates.findIndex(t => t.id === templateId);\r\n    if (templateIndex === -1) {\r\n      throw new ValidationException(`Rule template with ID '${templateId}' not found`, []);\r\n    }\r\n\r\n    const updatedTemplate = { ...this._ruleTemplates[templateIndex], ...updates };\r\n    this.validateRuleTemplate(updatedTemplate);\r\n\r\n    this._ruleTemplates[templateIndex] = updatedTemplate;\r\n    this._lastModifiedBy = modifiedBy;\r\n    this.touch();\r\n  }\r\n\r\n  public removeRuleTemplate(templateId: string, modifiedBy: UserId): void {\r\n    const templateIndex = this._ruleTemplates.findIndex(t => t.id === templateId);\r\n    if (templateIndex === -1) {\r\n      throw new ValidationException(`Rule template with ID '${templateId}' not found`, []);\r\n    }\r\n\r\n    this._ruleTemplates.splice(templateIndex, 1);\r\n    this._lastModifiedBy = modifiedBy;\r\n    this.touch();\r\n  }\r\n\r\n  public getRuleTemplatesByCategory(category: RuleCategory): RuleTemplate[] {\r\n    return this._ruleTemplates.filter(t => t.category === category);\r\n  }\r\n\r\n  public generateRuleFromTemplate(templateId: string, parameters: Record<string, any>): string {\r\n    const template = this._ruleTemplates.find(t => t.id === templateId);\r\n    if (!template) {\r\n      throw new ValidationException(`Rule template with ID '${templateId}' not found`, []);\r\n    }\r\n\r\n    // Validate required parameters\r\n    const requiredParams = template.parameters.filter(p => p.required);\r\n    for (const param of requiredParams) {\r\n      if (!(param.name in parameters)) {\r\n        throw new ValidationException(`Required parameter '${param.name}' is missing`, []);\r\n      }\r\n    }\r\n\r\n    // Validate parameter types and values\r\n    for (const param of template.parameters) {\r\n      if (param.name in parameters) {\r\n        this.validateParameterValue(param, parameters[param.name]);\r\n      }\r\n    }\r\n\r\n    // Replace template placeholders with actual values\r\n    let ruleCondition = template.template;\r\n    for (const [key, value] of Object.entries(parameters)) {\r\n      const placeholder = `{{${key}}}`;\r\n      ruleCondition = ruleCondition.replace(new RegExp(placeholder, 'g'), JSON.stringify(value));\r\n    }\r\n\r\n    return ruleCondition;\r\n  }\r\n\r\n  public validateRuleCondition(condition: string): boolean {\r\n    try {\r\n      const parsed = JSON.parse(condition);\r\n      return this.validateConditionStructure(parsed);\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  public getMetrics(): PolicyMetrics {\r\n    // This would typically be implemented by querying actual policy data\r\n    // For now, return placeholder metrics\r\n    return {\r\n      totalPolicies: 0,\r\n      activePolicies: 0,\r\n      totalRules: 0,\r\n      activeRules: 0,\r\n      evaluationsLast24h: 0,\r\n      violationsLast24h: 0,\r\n      averageEvaluationTime: 0,\r\n      topViolatedRules: []\r\n    };\r\n  }\r\n\r\n  private validateConditionStructure(condition: any): boolean {\r\n    if (typeof condition !== 'object' || condition === null) {\r\n      return false;\r\n    }\r\n\r\n    // Check for valid logical operators\r\n    const validOperators = ['and', 'or', 'not', 'field', 'operator', 'value'];\r\n    const keys = Object.keys(condition);\r\n    \r\n    if (keys.length === 0) {\r\n      return false;\r\n    }\r\n\r\n    // Validate logical operators\r\n    if ('and' in condition) {\r\n      return Array.isArray(condition.and) && \r\n             condition.and.every((sub: any) => this.validateConditionStructure(sub));\r\n    }\r\n\r\n    if ('or' in condition) {\r\n      return Array.isArray(condition.or) && \r\n             condition.or.every((sub: any) => this.validateConditionStructure(sub));\r\n    }\r\n\r\n    if ('not' in condition) {\r\n      return this.validateConditionStructure(condition.not);\r\n    }\r\n\r\n    // Validate field conditions\r\n    if ('field' in condition && 'operator' in condition && 'value' in condition) {\r\n      const validFieldOperators = [\r\n        'equals', 'not_equals', 'contains', 'starts_with', 'ends_with',\r\n        'greater_than', 'less_than', 'in', 'regex'\r\n      ];\r\n      return typeof condition.field === 'string' &&\r\n             validFieldOperators.includes(condition.operator);\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  private validateParameterValue(parameter: RuleParameter, value: any): void {\r\n    switch (parameter.type) {\r\n      case RuleParameterType.STRING:\r\n        if (typeof value !== 'string') {\r\n          throw new ValidationException(`Parameter '${parameter.name}' must be a string`, []);\r\n        }\r\n        break;\r\n      case RuleParameterType.NUMBER:\r\n        if (typeof value !== 'number') {\r\n          throw new ValidationException(`Parameter '${parameter.name}' must be a number`, []);\r\n        }\r\n        break;\r\n      case RuleParameterType.BOOLEAN:\r\n        if (typeof value !== 'boolean') {\r\n          throw new ValidationException(`Parameter '${parameter.name}' must be a boolean`, []);\r\n        }\r\n        break;\r\n      case RuleParameterType.ARRAY:\r\n        if (!Array.isArray(value)) {\r\n          throw new ValidationException(`Parameter '${parameter.name}' must be an array`, []);\r\n        }\r\n        break;\r\n      case RuleParameterType.ENUM:\r\n        if (parameter.options && !parameter.options.includes(value)) {\r\n          throw new ValidationException(`Parameter '${parameter.name}' must be one of: ${parameter.options.join(', ')}`, []);\r\n        }\r\n        break;\r\n      case RuleParameterType.IP_ADDRESS:\r\n        if (!this.isValidIpAddress(value)) {\r\n          throw new ValidationException(`Parameter '${parameter.name}' must be a valid IP address`, []);\r\n        }\r\n        break;\r\n      case RuleParameterType.EMAIL:\r\n        if (!this.isValidEmail(value)) {\r\n          throw new ValidationException(`Parameter '${parameter.name}' must be a valid email address`, []);\r\n        }\r\n        break;\r\n      case RuleParameterType.URL:\r\n        if (!this.isValidUrl(value)) {\r\n          throw new ValidationException(`Parameter '${parameter.name}' must be a valid URL`, []);\r\n        }\r\n        break;\r\n    }\r\n  }\r\n\r\n  private isValidIpAddress(value: string): boolean {\r\n    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\r\n    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;\r\n    return ipv4Regex.test(value) || ipv6Regex.test(value);\r\n  }\r\n\r\n  private isValidEmail(value: string): boolean {\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return emailRegex.test(value);\r\n  }\r\n\r\n  private isValidUrl(value: string): boolean {\r\n    try {\r\n      new URL(value);\r\n      return true;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private static getDefaultSettings(): PolicyConfigurationSettings {\r\n    return {\r\n      enableAutomaticExecution: false,\r\n      requireApprovalForCriticalActions: true,\r\n      maxRulesPerPolicy: 100,\r\n      defaultRulePriority: 50,\r\n      auditRetentionDays: 365,\r\n      notificationSettings: {\r\n        enableEmailNotifications: true,\r\n        enableSlackNotifications: false,\r\n        enableWebhookNotifications: false,\r\n        emailRecipients: [],\r\n        slackChannels: [],\r\n        webhookUrls: [],\r\n        notificationThresholds: {\r\n          criticalFindings: 1,\r\n          highFindings: 5,\r\n          policyViolations: 10\r\n        }\r\n      },\r\n      escalationSettings: {\r\n        enableAutoEscalation: false,\r\n        escalationLevels: [],\r\n        maxEscalationLevel: 3,\r\n        escalationTimeouts: {\r\n          level1Minutes: 30,\r\n          level2Minutes: 60,\r\n          level3Minutes: 120\r\n        }\r\n      },\r\n      integrationSettings: {\r\n        siemIntegration: {\r\n          enabled: false,\r\n          eventTypes: []\r\n        },\r\n        ticketingIntegration: {\r\n          enabled: false,\r\n          system: 'jira'\r\n        },\r\n        threatIntelligence: {\r\n          enabled: false,\r\n          providers: [],\r\n          apiKeys: {},\r\n          updateFrequencyHours: 24\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  private static getDefaultRuleTemplates(): RuleTemplate[] {\r\n    return [\r\n      {\r\n        id: 'failed-login-attempts',\r\n        name: 'Failed Login Attempts',\r\n        description: 'Detect multiple failed login attempts from the same IP',\r\n        category: RuleCategory.AUTHENTICATION,\r\n        template: '{\"and\":[{\"field\":\"eventData.eventType\",\"operator\":\"equals\",\"value\":\"login_failed\"},{\"field\":\"eventData.attempts\",\"operator\":\"greater_than\",\"value\":{{maxAttempts}}}]}',\r\n        parameters: [\r\n          {\r\n            name: 'maxAttempts',\r\n            type: RuleParameterType.NUMBER,\r\n            description: 'Maximum number of failed attempts before triggering',\r\n            required: true,\r\n            defaultValue: 5\r\n          }\r\n        ],\r\n        defaultPriority: 80,\r\n        defaultSeverity: 'HIGH',\r\n        tags: ['authentication', 'brute-force']\r\n      },\r\n      {\r\n        id: 'suspicious-ip-access',\r\n        name: 'Suspicious IP Access',\r\n        description: 'Detect access from known malicious IP addresses',\r\n        category: RuleCategory.NETWORK_SECURITY,\r\n        template: '{\"field\":\"systemContext.sourceIp\",\"operator\":\"in\",\"value\":{{suspiciousIps}}}',\r\n        parameters: [\r\n          {\r\n            name: 'suspiciousIps',\r\n            type: RuleParameterType.ARRAY,\r\n            description: 'List of suspicious IP addresses',\r\n            required: true,\r\n            defaultValue: []\r\n          }\r\n        ],\r\n        defaultPriority: 90,\r\n        defaultSeverity: 'CRITICAL',\r\n        tags: ['network', 'threat-intelligence']\r\n      }\r\n    ];\r\n  }\r\n\r\n  private validateProps(props: any): void {\r\n    if (!props.tenantId) {\r\n      throw new ValidationException('Tenant ID is required', []);\r\n    }\r\n\r\n    if (!props.createdBy) {\r\n      throw new ValidationException('Created by user ID is required', []);\r\n    }\r\n\r\n    if (!props.settings) {\r\n      throw new ValidationException('Settings are required', []);\r\n    }\r\n\r\n    this.validateSettings(props.settings);\r\n\r\n    if (!Array.isArray(props.ruleTemplates)) {\r\n      throw new ValidationException('Rule templates must be an array', []);\r\n    }\r\n\r\n    props.ruleTemplates.forEach((template: RuleTemplate) => this.validateRuleTemplate(template));\r\n  }\r\n\r\n  private validateSettings(settings: PolicyConfigurationSettings): void {\r\n    if (typeof settings.enableAutomaticExecution !== 'boolean') {\r\n      throw new ValidationException('enableAutomaticExecution must be a boolean', []);\r\n    }\r\n\r\n    if (typeof settings.maxRulesPerPolicy !== 'number' || settings.maxRulesPerPolicy <= 0) {\r\n      throw new ValidationException('maxRulesPerPolicy must be a positive number', []);\r\n    }\r\n\r\n    if (typeof settings.auditRetentionDays !== 'number' || settings.auditRetentionDays <= 0) {\r\n      throw new ValidationException('auditRetentionDays must be a positive number', []);\r\n    }\r\n  }\r\n\r\n  private validateRuleTemplate(template: RuleTemplate): void {\r\n    if (!template.id || template.id.trim().length === 0) {\r\n      throw new ValidationException('Rule template ID is required', []);\r\n    }\r\n\r\n    if (!template.name || template.name.trim().length === 0) {\r\n      throw new ValidationException('Rule template name is required', []);\r\n    }\r\n\r\n    if (!Object.values(RuleCategory).includes(template.category)) {\r\n      throw new ValidationException('Valid rule category is required', []);\r\n    }\r\n\r\n    if (!template.template || template.template.trim().length === 0) {\r\n      throw new ValidationException('Rule template condition is required', []);\r\n    }\r\n\r\n    if (!Array.isArray(template.parameters)) {\r\n      throw new ValidationException('Rule template parameters must be an array', []);\r\n    }\r\n\r\n    // Basic validation that the template is not empty and looks like it could be JSON\r\n    if (!template.template.trim().startsWith('{') && !template.template.trim().startsWith('[')) {\r\n      // Only validate as JSON if it's clearly meant to be JSON (starts with { or [)\r\n      // and doesn't contain placeholders\r\n      if (!template.template.includes('{{')) {\r\n        try {\r\n          JSON.parse(template.template);\r\n        } catch (error) {\r\n          throw new ValidationException('Rule template condition must be valid JSON', []);\r\n        }\r\n      }\r\n    }\r\n\r\n    template.parameters.forEach(param => {\r\n      if (!param.name || param.name.trim().length === 0) {\r\n        throw new ValidationException('Parameter name is required', []);\r\n      }\r\n\r\n      if (!Object.values(RuleParameterType).includes(param.type)) {\r\n        throw new ValidationException('Valid parameter type is required', []);\r\n      }\r\n    });\r\n  }\r\n\r\n  private touch(): void {\r\n    // Update the last modified timestamp\r\n    // This would typically update an updatedAt field if we had one\r\n  }\r\n\r\n  // Getters\r\n  public get tenantId(): TenantId { return this._tenantId; }\r\n  public get settings(): PolicyConfigurationSettings { return { ...this._settings }; }\r\n  public get ruleTemplates(): RuleTemplate[] { return [...this._ruleTemplates]; }\r\n  public get createdBy(): UserId { return this._createdBy; }\r\n  public get lastModifiedBy(): UserId { return this._lastModifiedBy; }\r\n  public get version(): string { return this._version; }\r\n}"], "version": 3}