40f506ed5adb35da448e014d3a270a06
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const cache_strategy_1 = require("../../patterns/cache-strategy");
describe('Cache Strategy', () => {
    describe('InMemoryCacheStrategy', () => {
        let cache;
        beforeEach(() => {
            cache = new cache_strategy_1.InMemoryCacheStrategy({
                defaultTtl: 60000, // 1 minute
                maxSize: 100,
                cleanupInterval: 1000,
            });
        });
        afterEach(() => {
            cache.destroy();
        });
        describe('basic operations', () => {
            it('should store and retrieve values', async () => {
                await cache.set('key1', 'value1');
                const result = await cache.get('key1');
                expect(result).toBe('value1');
            });
            it('should return null for non-existent keys', async () => {
                const result = await cache.get('non-existent');
                expect(result).toBeNull();
            });
            it('should check if key exists', async () => {
                await cache.set('existing-key', 'value');
                expect(await cache.has('existing-key')).toBe(true);
                expect(await cache.has('non-existent')).toBe(false);
            });
            it('should delete keys', async () => {
                await cache.set('key-to-delete', 'value');
                expect(await cache.has('key-to-delete')).toBe(true);
                const deleted = await cache.delete('key-to-delete');
                expect(deleted).toBe(true);
                expect(await cache.has('key-to-delete')).toBe(false);
            });
            it('should return false when deleting non-existent key', async () => {
                const deleted = await cache.delete('non-existent');
                expect(deleted).toBe(false);
            });
            it('should clear all entries', async () => {
                await cache.set('key1', 'value1');
                await cache.set('key2', 'value2');
                await cache.set('key3', 'value3');
                await cache.clear();
                expect(await cache.has('key1')).toBe(false);
                expect(await cache.has('key2')).toBe(false);
                expect(await cache.has('key3')).toBe(false);
            });
            it('should clear entries matching pattern', async () => {
                await cache.set('user:1:profile', 'profile1');
                await cache.set('user:2:profile', 'profile2');
                await cache.set('user:1:settings', 'settings1');
                await cache.set('product:1', 'product1');
                await cache.clear('user:1:*');
                expect(await cache.has('user:1:profile')).toBe(false);
                expect(await cache.has('user:1:settings')).toBe(false);
                expect(await cache.has('user:2:profile')).toBe(true);
                expect(await cache.has('product:1')).toBe(true);
            });
        });
        describe('TTL functionality', () => {
            it('should respect custom TTL', async () => {
                await cache.set('short-lived', 'value', 50); // 50ms TTL
                expect(await cache.get('short-lived')).toBe('value');
                // Wait for expiration
                await new Promise(resolve => setTimeout(resolve, 60));
                expect(await cache.get('short-lived')).toBeNull();
            });
            it('should use default TTL when not specified', async () => {
                const shortTtlCache = new cache_strategy_1.InMemoryCacheStrategy({ defaultTtl: 50 });
                await shortTtlCache.set('default-ttl', 'value');
                expect(await shortTtlCache.get('default-ttl')).toBe('value');
                await new Promise(resolve => setTimeout(resolve, 60));
                expect(await shortTtlCache.get('default-ttl')).toBeNull();
                shortTtlCache.destroy();
            });
            it('should handle expired entries in has() method', async () => {
                await cache.set('expiring-key', 'value', 50);
                expect(await cache.has('expiring-key')).toBe(true);
                await new Promise(resolve => setTimeout(resolve, 60));
                expect(await cache.has('expiring-key')).toBe(false);
            });
            it('should clean up expired entries automatically', async () => {
                const quickCleanupCache = new cache_strategy_1.InMemoryCacheStrategy({
                    defaultTtl: 50,
                    cleanupInterval: 25,
                });
                await quickCleanupCache.set('auto-cleanup', 'value');
                // Wait for expiration and cleanup
                await new Promise(resolve => setTimeout(resolve, 80));
                const metrics = await quickCleanupCache.getMetrics();
                expect(metrics.size).toBe(0);
                quickCleanupCache.destroy();
            });
        });
        describe('size limits and eviction', () => {
            it('should evict least recently used entries when size limit reached', async () => {
                const smallCache = new cache_strategy_1.InMemoryCacheStrategy({ maxSize: 2 });
                await smallCache.set('key1', 'value1');
                // Small delay to ensure different timestamps
                await new Promise(resolve => setTimeout(resolve, 1));
                await smallCache.set('key2', 'value2');
                // Small delay before access
                await new Promise(resolve => setTimeout(resolve, 1));
                // Access key1 to make it more recently used
                await smallCache.get('key1');
                // Small delay before adding new key
                await new Promise(resolve => setTimeout(resolve, 1));
                // Adding key3 should evict key2 (least recently used)
                await smallCache.set('key3', 'value3');
                expect(await smallCache.has('key1')).toBe(true);
                expect(await smallCache.has('key2')).toBe(false);
                expect(await smallCache.has('key3')).toBe(true);
                smallCache.destroy();
            });
            it('should call onEvict callback when evicting entries', async () => {
                const onEvict = jest.fn();
                const smallCache = new cache_strategy_1.InMemoryCacheStrategy({
                    maxSize: 1,
                    onEvict,
                });
                await smallCache.set('key1', 'value1');
                // Small delay to ensure different timestamps
                await new Promise(resolve => setTimeout(resolve, 1));
                await smallCache.set('key2', 'value2'); // Should evict key1
                expect(onEvict).toHaveBeenCalledWith('key1', expect.objectContaining({
                    value: 'value1',
                }));
                smallCache.destroy();
            });
            it('should not evict when updating existing key', async () => {
                const smallCache = new cache_strategy_1.InMemoryCacheStrategy({ maxSize: 2 });
                await smallCache.set('key1', 'value1');
                await smallCache.set('key2', 'value2');
                // Update existing key - should not trigger eviction
                await smallCache.set('key1', 'updated-value1');
                expect(await smallCache.has('key1')).toBe(true);
                expect(await smallCache.has('key2')).toBe(true);
                expect(await smallCache.get('key1')).toBe('updated-value1');
                smallCache.destroy();
            });
        });
        describe('metrics', () => {
            it('should track cache hits and misses', async () => {
                await cache.set('key1', 'value1');
                // Hit
                await cache.get('key1');
                // Miss
                await cache.get('non-existent');
                const metrics = await cache.getMetrics();
                expect(metrics.hits).toBe(1);
                expect(metrics.misses).toBe(1);
                expect(metrics.hitRate).toBe(0.5);
            });
            it('should track sets and deletes', async () => {
                await cache.set('key1', 'value1');
                await cache.set('key2', 'value2');
                await cache.delete('key1');
                const metrics = await cache.getMetrics();
                expect(metrics.sets).toBe(2);
                expect(metrics.deletes).toBe(1);
                expect(metrics.size).toBe(1);
            });
            it('should track evictions', async () => {
                const smallCache = new cache_strategy_1.InMemoryCacheStrategy({ maxSize: 1 });
                await smallCache.set('key1', 'value1');
                // Small delay to ensure different timestamps
                await new Promise(resolve => setTimeout(resolve, 1));
                await smallCache.set('key2', 'value2'); // Should evict key1
                const metrics = await smallCache.getMetrics();
                expect(metrics.evictions).toBe(1);
                smallCache.destroy();
            });
            it('should calculate hit rate correctly', async () => {
                await cache.set('key1', 'value1');
                // 2 hits, 1 miss = 2/3 hit rate
                await cache.get('key1');
                await cache.get('key1');
                await cache.get('non-existent');
                const metrics = await cache.getMetrics();
                expect(metrics.hitRate).toBeCloseTo(2 / 3, 2);
            });
        });
        describe('access tracking', () => {
            it('should update access count and last accessed time', async () => {
                await cache.set('tracked-key', 'value');
                // Access the key multiple times
                await cache.get('tracked-key');
                await cache.get('tracked-key');
                await cache.get('tracked-key');
                // Access count should be tracked internally for LRU eviction
                // We can't directly test this, but we can test LRU behavior
                const smallCache = new cache_strategy_1.InMemoryCacheStrategy({ maxSize: 2 });
                await smallCache.set('key1', 'value1');
                // Small delay to ensure different timestamps
                await new Promise(resolve => setTimeout(resolve, 1));
                await smallCache.set('key2', 'value2');
                // Small delay before access
                await new Promise(resolve => setTimeout(resolve, 1));
                // Access key1 multiple times
                await smallCache.get('key1');
                await smallCache.get('key1');
                // Small delay before adding new key
                await new Promise(resolve => setTimeout(resolve, 1));
                // Add key3 - should evict key2 (less recently used)
                await smallCache.set('key3', 'value3');
                expect(await smallCache.has('key1')).toBe(true);
                expect(await smallCache.has('key2')).toBe(false);
                expect(await smallCache.has('key3')).toBe(true);
                smallCache.destroy();
            });
        });
        describe('manual cleanup', () => {
            it('should manually clean up expired entries', async () => {
                await cache.set('expiring1', 'value1', 50);
                await cache.set('expiring2', 'value2', 50);
                await cache.set('persistent', 'value3', 60000);
                // Wait for some entries to expire
                await new Promise(resolve => setTimeout(resolve, 60));
                // Manual cleanup
                cache.cleanup();
                expect(await cache.has('expiring1')).toBe(false);
                expect(await cache.has('expiring2')).toBe(false);
                expect(await cache.has('persistent')).toBe(true);
            });
        });
        describe('data types', () => {
            it('should handle different data types', async () => {
                const testData = {
                    string: 'test string',
                    number: 42,
                    boolean: true,
                    object: { nested: 'value' },
                    array: [1, 2, 3],
                    null: null,
                    undefined: undefined,
                };
                for (const [key, value] of Object.entries(testData)) {
                    await cache.set(key, value);
                    const retrieved = await cache.get(key);
                    expect(retrieved).toEqual(value);
                }
            });
        });
    });
    describe('MultiLevelCacheStrategy', () => {
        let l1Cache;
        let l2Cache;
        let multiCache;
        beforeEach(() => {
            l1Cache = new cache_strategy_1.InMemoryCacheStrategy({ defaultTtl: 60000 });
            l2Cache = new cache_strategy_1.InMemoryCacheStrategy({ defaultTtl: 60000 });
            multiCache = new cache_strategy_1.MultiLevelCacheStrategy(l1Cache, l2Cache);
        });
        afterEach(() => {
            l1Cache.destroy();
            l2Cache.destroy();
        });
        it('should check L1 cache first', async () => {
            await l1Cache.set('l1-key', 'l1-value');
            const result = await multiCache.get('l1-key');
            expect(result).toBe('l1-value');
        });
        it('should fall back to L2 cache and populate L1', async () => {
            await l2Cache.set('l2-key', 'l2-value');
            // Should not be in L1 initially
            expect(await l1Cache.get('l2-key')).toBeNull();
            // Get from multi-level cache
            const result = await multiCache.get('l2-key');
            expect(result).toBe('l2-value');
            // Should now be in L1
            expect(await l1Cache.get('l2-key')).toBe('l2-value');
        });
        it('should return null when key not found in either cache', async () => {
            const result = await multiCache.get('non-existent');
            expect(result).toBeNull();
        });
        it('should set in both caches', async () => {
            await multiCache.set('multi-key', 'multi-value');
            expect(await l1Cache.get('multi-key')).toBe('multi-value');
            expect(await l2Cache.get('multi-key')).toBe('multi-value');
        });
        it('should delete from both caches', async () => {
            await l1Cache.set('delete-key', 'value');
            await l2Cache.set('delete-key', 'value');
            const deleted = await multiCache.delete('delete-key');
            expect(deleted).toBe(true);
            expect(await l1Cache.has('delete-key')).toBe(false);
            expect(await l2Cache.has('delete-key')).toBe(false);
        });
        it('should clear both caches', async () => {
            await l1Cache.set('clear-key1', 'value1');
            await l2Cache.set('clear-key2', 'value2');
            await multiCache.clear();
            expect(await l1Cache.has('clear-key1')).toBe(false);
            expect(await l2Cache.has('clear-key2')).toBe(false);
        });
        it('should check L1 first, then L2 for has() method', async () => {
            await l2Cache.set('l2-only', 'value');
            expect(await multiCache.has('l2-only')).toBe(true);
        });
        it('should combine metrics from both levels', async () => {
            await multiCache.set('key1', 'value1');
            await multiCache.get('key1'); // Hit in L1
            await multiCache.get('non-existent'); // Miss in both
            const metrics = await multiCache.getMetrics();
            expect(metrics.hits).toBeGreaterThan(0);
            expect(metrics.misses).toBeGreaterThan(0);
            expect(metrics.sets).toBeGreaterThan(0);
        });
        it('should handle TTL in both levels', async () => {
            await multiCache.set('ttl-key', 'ttl-value', 50);
            expect(await multiCache.get('ttl-key')).toBe('ttl-value');
            await new Promise(resolve => setTimeout(resolve, 60));
            expect(await multiCache.get('ttl-key')).toBeNull();
        });
    });
    describe('abstract CacheStrategy', () => {
        it('should be extendable for custom implementations', async () => {
            class CustomCacheStrategy extends cache_strategy_1.CacheStrategy {
                constructor() {
                    super(...arguments);
                    this.store = new Map();
                }
                async get(key) {
                    return this.store.get(key) || null;
                }
                async set(key, value, ttl) {
                    this.store.set(key, value);
                }
                async delete(key) {
                    return this.store.delete(key);
                }
                async clear(pattern) {
                    if (pattern) {
                        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
                        for (const key of this.store.keys()) {
                            if (regex.test(key)) {
                                this.store.delete(key);
                            }
                        }
                    }
                    else {
                        this.store.clear();
                    }
                }
                async has(key) {
                    return this.store.has(key);
                }
                async getMetrics() {
                    return {
                        hits: 0,
                        misses: 0,
                        sets: 0,
                        deletes: 0,
                        evictions: 0,
                        size: this.store.size,
                        hitRate: 0,
                    };
                }
            }
            const customCache = new CustomCacheStrategy();
            await customCache.set('custom-key', 'custom-value');
            expect(await customCache.get('custom-key')).toBe('custom-value');
            expect(await customCache.has('custom-key')).toBe(true);
            await customCache.delete('custom-key');
            expect(await customCache.has('custom-key')).toBe(false);
        });
    });
    describe('error handling', () => {
        it('should handle concurrent access gracefully', async () => {
            const testCache = new cache_strategy_1.InMemoryCacheStrategy();
            const promises = [];
            // Concurrent sets
            for (let i = 0; i < 10; i++) {
                promises.push(testCache.set(`concurrent-${i}`, `value-${i}`));
            }
            await Promise.all(promises);
            // Verify all values were set
            for (let i = 0; i < 10; i++) {
                expect(await testCache.get(`concurrent-${i}`)).toBe(`value-${i}`);
            }
            testCache.destroy();
        });
        it('should handle large values', async () => {
            const testCache = new cache_strategy_1.InMemoryCacheStrategy();
            const largeValue = 'x'.repeat(10000);
            await testCache.set('large-key', largeValue);
            const retrieved = await testCache.get('large-key');
            expect(retrieved).toBe(largeValue);
            testCache.destroy();
        });
        it('should handle special characters in keys', async () => {
            const testCache = new cache_strategy_1.InMemoryCacheStrategy();
            const specialKeys = [
                'key with spaces',
                'key:with:colons',
                'key/with/slashes',
                'key.with.dots',
                'key-with-dashes',
                'key_with_underscores',
                'key@with@symbols',
            ];
            for (const key of specialKeys) {
                await testCache.set(key, `value-for-${key}`);
                expect(await testCache.get(key)).toBe(`value-for-${key}`);
            }
            testCache.destroy();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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