{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\vulnerability-assessment.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,6CAAmD;AACnD,qCAAyC;AACzC,2GAAgG;AAChG,qFAA2E;AAC3E,yFAA+E;AAC/E,+FAAoF;AACpF,sFAAkF;AAClF,0FAAsF;AACtF,uGAAmG;AAEnG;;;GAGG;AAEI,IAAM,8BAA8B,sCAApC,MAAM,8BAA8B;IAGzC,YAEE,oBAA0E,EAE1E,uBAAmE,EAEnE,eAAmD,EAEnD,cAA8D,EAC7C,aAA4B,EAC5B,YAA0B,EAC1B,mBAAwC;QATxC,yBAAoB,GAApB,oBAAoB,CAAqC;QAEzD,4BAAuB,GAAvB,uBAAuB,CAA2B;QAElD,oBAAe,GAAf,eAAe,CAAmB;QAElC,mBAAc,GAAd,cAAc,CAA+B;QAC7C,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;QAC1B,wBAAmB,GAAnB,mBAAmB,CAAqB;QAb1C,WAAM,GAAG,IAAI,eAAM,CAAC,gCAA8B,CAAC,IAAI,CAAC,CAAC;IAcvE,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,cAwBtB,EAAE,MAAc;QACf,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,eAAe,EAAE,cAAc,CAAC,eAAe;gBAC/C,OAAO,EAAE,cAAc,CAAC,OAAO;gBAC/B,cAAc,EAAE,cAAc,CAAC,cAAc;gBAC7C,MAAM;aACP,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;gBAC/D,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,CAAC,eAAe,EAAE;aAC9C,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;YACzD,CAAC;YAED,6BAA6B;YAC7B,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC3B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;oBAC/C,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,CAAC,OAAO,EAAE;iBACtC,CAAC,CAAC;gBAEH,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,MAAM,IAAI,0BAAiB,CAAC,iBAAiB,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;YAED,4BAA4B;YAC5B,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;gBAC1B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;oBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,CAAC,MAAM,EAAE;iBACrC,CAAC,CAAC;gBAEH,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;gBAClD,GAAG,cAAc;gBACjB,MAAM,EAAE,SAAS;gBACjB,UAAU,EAAE,MAAM;gBAClB,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEzE,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,0BAA0B,EAC1B,eAAe,CAAC,EAAE,EAClB;gBACE,eAAe,EAAE,cAAc,CAAC,eAAe;gBAC/C,OAAO,EAAE,cAAc,CAAC,OAAO;gBAC/B,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+CAA+C,EAAE;gBAC/D,YAAY,EAAE,eAAe,CAAC,EAAE;gBAChC,eAAe,EAAE,cAAc,CAAC,eAAe;gBAC/C,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE;gBAC7D,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,cAAc;gBACd,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,EAAU;QACnC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;gBACzD,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC,eAAe,EAAE,OAAO,EAAE,MAAM,CAAC;aAC9C,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,CAAC,CAAC;YACtD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,YAAY,EAAE,EAAE;gBAChB,eAAe,EAAE,UAAU,CAAC,eAAe;gBAC3C,MAAM,EAAE,UAAU,CAAC,MAAM;aAC1B,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACpD,YAAY,EAAE,EAAE;gBAChB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,QAgBvB;QAMC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC;YAChC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;YACnD,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAElC,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB;iBAC3C,kBAAkB,CAAC,YAAY,CAAC;iBAChC,iBAAiB,CAAC,0BAA0B,EAAE,eAAe,CAAC;iBAC9D,iBAAiB,CAAC,kBAAkB,EAAE,OAAO,CAAC;iBAC9C,iBAAiB,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAEhD,gBAAgB;YAChB,IAAI,QAAQ,CAAC,gBAAgB,EAAE,MAAM,EAAE,CAAC;gBACtC,YAAY,CAAC,QAAQ,CAAC,sDAAsD,EAAE;oBAC5E,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;iBAC5C,CAAC,CAAC;YACL,CAAC;YAED,IAAI,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;gBAC9B,YAAY,CAAC,QAAQ,CAAC,sCAAsC,EAAE;oBAC5D,QAAQ,EAAE,QAAQ,CAAC,QAAQ;iBAC5B,CAAC,CAAC;YACL,CAAC;YAED,IAAI,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;gBAC7B,YAAY,CAAC,QAAQ,CAAC,oCAAoC,EAAE;oBAC1D,OAAO,EAAE,QAAQ,CAAC,OAAO;iBAC1B,CAAC,CAAC;YACL,CAAC;YAED,IAAI,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;gBAC9B,YAAY,CAAC,QAAQ,CAAC,qCAAqC,EAAE;oBAC3D,QAAQ,EAAE,QAAQ,CAAC,QAAQ;iBAC5B,CAAC,CAAC;YACL,CAAC;YAED,IAAI,QAAQ,CAAC,eAAe,EAAE,MAAM,EAAE,CAAC;gBACrC,YAAY,CAAC,QAAQ,CAAC,oDAAoD,EAAE;oBAC1E,eAAe,EAAE,QAAQ,CAAC,eAAe;iBAC1C,CAAC,CAAC;YACL,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;gBAChC,YAAY,CAAC,QAAQ,CAAC,2CAA2C,EAAE;oBACjE,UAAU,EAAE,QAAQ,CAAC,UAAU;iBAChC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;gBAC3B,YAAY,CAAC,QAAQ,CAAC,yCAAyC,EAAE;oBAC/D,aAAa,EAAE,QAAQ,CAAC,aAAa;iBACtC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;gBAC5B,YAAY,CAAC,QAAQ,CAAC,0CAA0C,EAAE;oBAChE,cAAc,EAAE,QAAQ,CAAC,cAAc;iBACxC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;gBAChC,YAAY,CAAC,QAAQ,CAAC,iDAAiD,EAAE;oBACvE,UAAU,EAAE,QAAQ,CAAC,UAAU;iBAChC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,QAAQ,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;gBAC3C,YAAY,CAAC,QAAQ,CAAC,+CAA+C,EAAE;oBACrE,eAAe,EAAE,QAAQ,CAAC,eAAe;iBAC1C,CAAC,CAAC;YACL,CAAC;YAED,IAAI,QAAQ,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;gBAC1C,YAAY,CAAC,QAAQ,CAAC,6CAA6C,EAAE;oBACnE,cAAc,EAAE,QAAQ,CAAC,cAAc;iBACxC,CAAC,CAAC;YACL,CAAC;YAED,gBAAgB;YAChB,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,YAAY,CAAC;YAC/C,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,IAAI,MAAM,CAAC;YAC/C,YAAY,CAAC,OAAO,CAAC,cAAc,MAAM,EAAE,EAAE,SAAS,CAAC,CAAC;YAExD,mBAAmB;YACnB,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtC,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;YAElE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM;aAC5C,CAAC,CAAC;YAEH,OAAO;gBACL,WAAW;gBACX,KAAK;gBACL,IAAI;gBACJ,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACrC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,EAAU,EAAE,OAAY,EAAE,MAAc;QAC7D,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;gBACzD,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,CAAC,CAAC;YACtD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;gBACvC,YAAY,EAAE,EAAE;gBAChB,MAAM;aACP,CAAC,CAAC;YAEH,0BAA0B;YAC1B,MAAM,OAAO,GAAG,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACjC,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;oBACrC,OAAO,CAAC,GAAG,CAAC,GAAG;wBACb,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC;wBACrB,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC;qBACjB,CAAC;gBACJ,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAEnC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEzE,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,0BAA0B,EAC1B,EAAE,EACF;gBACE,eAAe,EAAE,UAAU,CAAC,eAAe;gBAC3C,OAAO;aACR,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,EAAE;gBACjD,YAAY,EAAE,EAAE;gBAChB,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM;gBACzC,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,YAAY,EAAE,EAAE;gBAChB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,EAAU,EAAE,MAAc;QACjD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;gBACzD,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC,eAAe,CAAC;aAC7B,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,CAAC,CAAC;YACtD,CAAC;YAED,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEzE,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,UAAU,EACV,0BAA0B,EAC1B,EAAE,EACF;gBACE,eAAe,EAAE,UAAU,CAAC,eAAe;aAC5C,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,EAAE;gBACnD,YAAY,EAAE,EAAE;gBAChB,eAAe,EAAE,UAAU,CAAC,eAAe;gBAC3C,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBACjD,YAAY,EAAE,EAAE;gBAChB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,EAAU,EAAE,UAGlC,EAAE,MAAc;QACf,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;gBACzD,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,CAAC,CAAC;YACtD,CAAC;YAED,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;YAE/C,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACzB,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACzC,CAAC;YAED,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEzE,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,0BAA0B,EAC1B,EAAE,EACF;gBACE,eAAe,EAAE,UAAU,CAAC,eAAe;gBAC3C,QAAQ,EAAE,UAAU,CAAC,QAAQ;gBAC7B,QAAQ,EAAE,UAAU,CAAC,QAAQ;aAC9B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,EAAE;gBAClD,YAAY,EAAE,EAAE;gBAChB,QAAQ,EAAE,UAAU,CAAC,QAAQ;gBAC7B,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,YAAY,EAAE,EAAE;gBAChB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,EAAU,EAAE,MAAc;QAC/C,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;gBACzD,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,CAAC,CAAC;YACtD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;gBACvC,YAAY,EAAE,EAAE;gBAChB,eAAe,EAAE,UAAU,CAAC,eAAe;gBAC3C,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAEnD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,0BAA0B,EAC1B,EAAE,EACF;gBACE,eAAe,EAAE,UAAU,CAAC,eAAe;gBAC3C,cAAc,EAAE,UAAU,CAAC,cAAc;aAC1C,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,EAAE;gBACjD,YAAY,EAAE,EAAE;gBAChB,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,YAAY,EAAE,EAAE;gBAChB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AA1eY,wEAA8B;yCAA9B,8BAA8B;IAD1C,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,yDAAuB,CAAC,CAAA;IAEzC,WAAA,IAAA,0BAAgB,EAAC,oCAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,oBAAK,CAAC,CAAA;IAEvB,WAAA,IAAA,0BAAgB,EAAC,6CAAiB,CAAC,CAAA;yDALG,oBAAU,oBAAV,oBAAU,oDAEP,oBAAU,oBAAV,oBAAU,oDAElB,oBAAU,oBAAV,oBAAU,oDAEX,oBAAU,oBAAV,oBAAU,oDACX,8BAAa,oBAAb,8BAAa,oDACd,4BAAY,oBAAZ,4BAAY,oDACL,0CAAmB,oBAAnB,0CAAmB;GAdhD,8BAA8B,CA0e1C", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\vulnerability-assessment.service.ts"], "sourcesContent": ["import { Injectable, Logger, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, In } from 'typeorm';\r\nimport { VulnerabilityAssessment } from '../../domain/entities/vulnerability-assessment.entity';\r\nimport { Vulnerability } from '../../domain/entities/vulnerability.entity';\r\nimport { Asset } from '../../../asset-management/domain/entities/asset.entity';\r\nimport { VulnerabilityScan } from '../../domain/entities/vulnerability-scan.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../infrastructure/notification/notification.service';\r\n\r\n/**\r\n * Vulnerability Assessment service\r\n * Handles vulnerability assessment operations and workflows\r\n */\r\n@Injectable()\r\nexport class VulnerabilityAssessmentService {\r\n  private readonly logger = new Logger(VulnerabilityAssessmentService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(VulnerabilityAssessment)\r\n    private readonly assessmentRepository: Repository<VulnerabilityAssessment>,\r\n    @InjectRepository(Vulnerability)\r\n    private readonly vulnerabilityRepository: Repository<Vulnerability>,\r\n    @InjectRepository(Asset)\r\n    private readonly assetRepository: Repository<Asset>,\r\n    @InjectRepository(VulnerabilityScan)\r\n    private readonly scanRepository: Repository<VulnerabilityScan>,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly auditService: AuditService,\r\n    private readonly notificationService: NotificationService,\r\n  ) {}\r\n\r\n  /**\r\n   * Create vulnerability assessment\r\n   */\r\n  async createAssessment(assessmentData: {\r\n    vulnerabilityId: string;\r\n    assetId?: string;\r\n    scanId?: string;\r\n    assessmentType: 'manual' | 'automated' | 'hybrid';\r\n    findings?: string;\r\n    recommendedActions?: string[];\r\n    businessImpact?: string;\r\n    technicalImpact?: string;\r\n    exploitabilityAssessment?: any;\r\n    mitigationStrategies?: any[];\r\n    compensatingControls?: any[];\r\n    methodology?: string;\r\n    toolsUsed?: string[];\r\n    assessmentCriteria?: any;\r\n    assessedSeverity?: string;\r\n    assessedCvssScore?: number;\r\n    assessedRiskScore?: number;\r\n    assessedPriority?: string;\r\n    confidenceLevel?: number;\r\n    isFalsePositive?: boolean;\r\n    falsePositiveJustification?: string;\r\n    isAcceptedRisk?: boolean;\r\n    riskAcceptanceJustification?: string;\r\n  }, userId: string): Promise<VulnerabilityAssessment> {\r\n    try {\r\n      this.logger.debug('Creating vulnerability assessment', {\r\n        vulnerabilityId: assessmentData.vulnerabilityId,\r\n        assetId: assessmentData.assetId,\r\n        assessmentType: assessmentData.assessmentType,\r\n        userId,\r\n      });\r\n\r\n      // Validate vulnerability exists\r\n      const vulnerability = await this.vulnerabilityRepository.findOne({\r\n        where: { id: assessmentData.vulnerabilityId },\r\n      });\r\n\r\n      if (!vulnerability) {\r\n        throw new NotFoundException('Vulnerability not found');\r\n      }\r\n\r\n      // Validate asset if provided\r\n      if (assessmentData.assetId) {\r\n        const asset = await this.assetRepository.findOne({\r\n          where: { id: assessmentData.assetId },\r\n        });\r\n\r\n        if (!asset) {\r\n          throw new NotFoundException('Asset not found');\r\n        }\r\n      }\r\n\r\n      // Validate scan if provided\r\n      if (assessmentData.scanId) {\r\n        const scan = await this.scanRepository.findOne({\r\n          where: { id: assessmentData.scanId },\r\n        });\r\n\r\n        if (!scan) {\r\n          throw new NotFoundException('Scan not found');\r\n        }\r\n      }\r\n\r\n      const assessment = this.assessmentRepository.create({\r\n        ...assessmentData,\r\n        status: 'pending',\r\n        assessedBy: userId,\r\n        assessedAt: new Date(),\r\n      });\r\n\r\n      const savedAssessment = await this.assessmentRepository.save(assessment);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'create',\r\n        'vulnerability_assessment',\r\n        savedAssessment.id,\r\n        {\r\n          vulnerabilityId: assessmentData.vulnerabilityId,\r\n          assetId: assessmentData.assetId,\r\n          assessmentType: assessmentData.assessmentType,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Vulnerability assessment created successfully', {\r\n        assessmentId: savedAssessment.id,\r\n        vulnerabilityId: assessmentData.vulnerabilityId,\r\n        userId,\r\n      });\r\n\r\n      return savedAssessment;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create vulnerability assessment', {\r\n        error: error.message,\r\n        assessmentData,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get assessment details\r\n   */\r\n  async getAssessmentDetails(id: string): Promise<VulnerabilityAssessment> {\r\n    try {\r\n      const assessment = await this.assessmentRepository.findOne({\r\n        where: { id },\r\n        relations: ['vulnerability', 'asset', 'scan'],\r\n      });\r\n\r\n      if (!assessment) {\r\n        throw new NotFoundException('Assessment not found');\r\n      }\r\n\r\n      this.logger.debug('Assessment details retrieved', {\r\n        assessmentId: id,\r\n        vulnerabilityId: assessment.vulnerabilityId,\r\n        status: assessment.status,\r\n      });\r\n\r\n      return assessment;\r\n    } catch (error) {\r\n      this.logger.error('Failed to get assessment details', {\r\n        assessmentId: id,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Search assessments with filtering\r\n   */\r\n  async searchAssessments(criteria: {\r\n    page?: number;\r\n    limit?: number;\r\n    vulnerabilityIds?: string[];\r\n    assetIds?: string[];\r\n    scanIds?: string[];\r\n    statuses?: string[];\r\n    assessmentTypes?: string[];\r\n    assessedBy?: string[];\r\n    assessedAfter?: Date;\r\n    assessedBefore?: Date;\r\n    severities?: string[];\r\n    isFalsePositive?: boolean;\r\n    isAcceptedRisk?: boolean;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n  }): Promise<{\r\n    assessments: VulnerabilityAssessment[];\r\n    total: number;\r\n    page: number;\r\n    totalPages: number;\r\n  }> {\r\n    try {\r\n      const page = criteria.page || 1;\r\n      const limit = Math.min(criteria.limit || 50, 1000);\r\n      const offset = (page - 1) * limit;\r\n\r\n      const queryBuilder = this.assessmentRepository\r\n        .createQueryBuilder('assessment')\r\n        .leftJoinAndSelect('assessment.vulnerability', 'vulnerability')\r\n        .leftJoinAndSelect('assessment.asset', 'asset')\r\n        .leftJoinAndSelect('assessment.scan', 'scan');\r\n\r\n      // Apply filters\r\n      if (criteria.vulnerabilityIds?.length) {\r\n        queryBuilder.andWhere('assessment.vulnerabilityId IN (:...vulnerabilityIds)', { \r\n          vulnerabilityIds: criteria.vulnerabilityIds \r\n        });\r\n      }\r\n\r\n      if (criteria.assetIds?.length) {\r\n        queryBuilder.andWhere('assessment.assetId IN (:...assetIds)', { \r\n          assetIds: criteria.assetIds \r\n        });\r\n      }\r\n\r\n      if (criteria.scanIds?.length) {\r\n        queryBuilder.andWhere('assessment.scanId IN (:...scanIds)', { \r\n          scanIds: criteria.scanIds \r\n        });\r\n      }\r\n\r\n      if (criteria.statuses?.length) {\r\n        queryBuilder.andWhere('assessment.status IN (:...statuses)', { \r\n          statuses: criteria.statuses \r\n        });\r\n      }\r\n\r\n      if (criteria.assessmentTypes?.length) {\r\n        queryBuilder.andWhere('assessment.assessmentType IN (:...assessmentTypes)', { \r\n          assessmentTypes: criteria.assessmentTypes \r\n        });\r\n      }\r\n\r\n      if (criteria.assessedBy?.length) {\r\n        queryBuilder.andWhere('assessment.assessedBy IN (:...assessedBy)', { \r\n          assessedBy: criteria.assessedBy \r\n        });\r\n      }\r\n\r\n      if (criteria.assessedAfter) {\r\n        queryBuilder.andWhere('assessment.assessedAt >= :assessedAfter', { \r\n          assessedAfter: criteria.assessedAfter \r\n        });\r\n      }\r\n\r\n      if (criteria.assessedBefore) {\r\n        queryBuilder.andWhere('assessment.assessedAt <= :assessedBefore', { \r\n          assessedBefore: criteria.assessedBefore \r\n        });\r\n      }\r\n\r\n      if (criteria.severities?.length) {\r\n        queryBuilder.andWhere('assessment.assessedSeverity IN (:...severities)', { \r\n          severities: criteria.severities \r\n        });\r\n      }\r\n\r\n      if (criteria.isFalsePositive !== undefined) {\r\n        queryBuilder.andWhere('assessment.isFalsePositive = :isFalsePositive', { \r\n          isFalsePositive: criteria.isFalsePositive \r\n        });\r\n      }\r\n\r\n      if (criteria.isAcceptedRisk !== undefined) {\r\n        queryBuilder.andWhere('assessment.isAcceptedRisk = :isAcceptedRisk', { \r\n          isAcceptedRisk: criteria.isAcceptedRisk \r\n        });\r\n      }\r\n\r\n      // Apply sorting\r\n      const sortBy = criteria.sortBy || 'assessedAt';\r\n      const sortOrder = criteria.sortOrder || 'DESC';\r\n      queryBuilder.orderBy(`assessment.${sortBy}`, sortOrder);\r\n\r\n      // Apply pagination\r\n      queryBuilder.skip(offset).take(limit);\r\n\r\n      const [assessments, total] = await queryBuilder.getManyAndCount();\r\n\r\n      this.logger.debug('Assessment search completed', {\r\n        total,\r\n        page,\r\n        limit,\r\n        criteriaCount: Object.keys(criteria).length,\r\n      });\r\n\r\n      return {\r\n        assessments,\r\n        total,\r\n        page,\r\n        totalPages: Math.ceil(total / limit),\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to search assessments', {\r\n        error: error.message,\r\n        criteria,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update assessment\r\n   */\r\n  async updateAssessment(id: string, updates: any, userId: string): Promise<VulnerabilityAssessment> {\r\n    try {\r\n      const assessment = await this.assessmentRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!assessment) {\r\n        throw new NotFoundException('Assessment not found');\r\n      }\r\n\r\n      this.logger.debug('Updating assessment', {\r\n        assessmentId: id,\r\n        userId,\r\n      });\r\n\r\n      // Track changes for audit\r\n      const changes = {};\r\n      Object.keys(updates).forEach(key => {\r\n        if (assessment[key] !== updates[key]) {\r\n          changes[key] = {\r\n            from: assessment[key],\r\n            to: updates[key],\r\n          };\r\n        }\r\n      });\r\n\r\n      Object.assign(assessment, updates);\r\n\r\n      const savedAssessment = await this.assessmentRepository.save(assessment);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'update',\r\n        'vulnerability_assessment',\r\n        id,\r\n        {\r\n          vulnerabilityId: assessment.vulnerabilityId,\r\n          changes,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Assessment updated successfully', {\r\n        assessmentId: id,\r\n        changesCount: Object.keys(changes).length,\r\n        userId,\r\n      });\r\n\r\n      return savedAssessment;\r\n    } catch (error) {\r\n      this.logger.error('Failed to update assessment', {\r\n        assessmentId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Complete assessment\r\n   */\r\n  async completeAssessment(id: string, userId: string): Promise<VulnerabilityAssessment> {\r\n    try {\r\n      const assessment = await this.assessmentRepository.findOne({\r\n        where: { id },\r\n        relations: ['vulnerability'],\r\n      });\r\n\r\n      if (!assessment) {\r\n        throw new NotFoundException('Assessment not found');\r\n      }\r\n\r\n      assessment.complete();\r\n      const savedAssessment = await this.assessmentRepository.save(assessment);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'complete',\r\n        'vulnerability_assessment',\r\n        id,\r\n        {\r\n          vulnerabilityId: assessment.vulnerabilityId,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Assessment completed successfully', {\r\n        assessmentId: id,\r\n        vulnerabilityId: assessment.vulnerabilityId,\r\n        userId,\r\n      });\r\n\r\n      return savedAssessment;\r\n    } catch (error) {\r\n      this.logger.error('Failed to complete assessment', {\r\n        assessmentId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Review assessment\r\n   */\r\n  async reviewAssessment(id: string, reviewData: {\r\n    comments?: string;\r\n    approved: boolean;\r\n  }, userId: string): Promise<VulnerabilityAssessment> {\r\n    try {\r\n      const assessment = await this.assessmentRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!assessment) {\r\n        throw new NotFoundException('Assessment not found');\r\n      }\r\n\r\n      assessment.review(userId, reviewData.comments);\r\n      \r\n      if (!reviewData.approved) {\r\n        assessment.reject(reviewData.comments);\r\n      }\r\n\r\n      const savedAssessment = await this.assessmentRepository.save(assessment);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'review',\r\n        'vulnerability_assessment',\r\n        id,\r\n        {\r\n          vulnerabilityId: assessment.vulnerabilityId,\r\n          approved: reviewData.approved,\r\n          comments: reviewData.comments,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Assessment reviewed successfully', {\r\n        assessmentId: id,\r\n        approved: reviewData.approved,\r\n        userId,\r\n      });\r\n\r\n      return savedAssessment;\r\n    } catch (error) {\r\n      this.logger.error('Failed to review assessment', {\r\n        assessmentId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete assessment\r\n   */\r\n  async deleteAssessment(id: string, userId: string): Promise<void> {\r\n    try {\r\n      const assessment = await this.assessmentRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!assessment) {\r\n        throw new NotFoundException('Assessment not found');\r\n      }\r\n\r\n      this.logger.debug('Deleting assessment', {\r\n        assessmentId: id,\r\n        vulnerabilityId: assessment.vulnerabilityId,\r\n        userId,\r\n      });\r\n\r\n      await this.assessmentRepository.remove(assessment);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'delete',\r\n        'vulnerability_assessment',\r\n        id,\r\n        {\r\n          vulnerabilityId: assessment.vulnerabilityId,\r\n          assessmentType: assessment.assessmentType,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Assessment deleted successfully', {\r\n        assessmentId: id,\r\n        userId,\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to delete assessment', {\r\n        assessmentId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}