{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\repositories\\response-action.repository.ts", "mappings": "", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\repositories\\response-action.repository.ts"], "sourcesContent": ["import { AggregateRepository, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { ResponseAction } from '../entities/response-action.entity';\r\nimport { ActionType } from '../enums/action-type.enum';\r\nimport { ActionStatus } from '../enums/action-status.enum';\r\n\r\n/**\r\n * Response Action Repository Interface\r\n * \r\n * Provides specialized data access methods for ResponseAction aggregates.\r\n * Focuses on automated response management, execution tracking, and audit trails.\r\n * \r\n * Key responsibilities:\r\n * - Response action persistence and retrieval\r\n * - Action execution status tracking\r\n * - Performance metrics for response actions\r\n * - Audit trail for compliance and analysis\r\n * - Rollback and recovery operations\r\n */\r\nexport interface ResponseActionRepository extends AggregateRepository<ResponseAction, UniqueEntityId> {\r\n  /**\r\n   * Find response actions by action type\r\n   * Type-specific action analysis and management\r\n   * \r\n   * @param actionType The action type to filter by\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to actions of the specified type\r\n   */\r\n  findByActionType(actionType: ActionType, limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find response actions by multiple action types\r\n   * Multi-type action filtering\r\n   * \r\n   * @param actionTypes Array of action types to include\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to actions of any of the specified types\r\n   */\r\n  findByActionTypes(actionTypes: ActionType[], limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find response actions by status\r\n   * Status-based action management and monitoring\r\n   * \r\n   * @param status The action status to filter by\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to actions with the specified status\r\n   */\r\n  findByStatus(status: ActionStatus, limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find pending response actions\r\n   * Actions waiting for execution or approval\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to pending actions\r\n   */\r\n  findPendingActions(limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find running response actions\r\n   * Currently executing actions for monitoring\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to running actions\r\n   */\r\n  findRunningActions(limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find completed response actions\r\n   * Successfully executed actions for analysis\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to completed actions\r\n   */\r\n  findCompletedActions(limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find failed response actions\r\n   * Failed actions for error analysis and retry\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to failed actions\r\n   */\r\n  findFailedActions(limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find response actions by priority\r\n   * Priority-based action management\r\n   * \r\n   * @param priority The priority level to filter by\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to actions with the specified priority\r\n   */\r\n  findByPriority(priority: 'low' | 'normal' | 'high' | 'critical', limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find high priority response actions\r\n   * Critical and high priority actions requiring immediate attention\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to high priority actions\r\n   */\r\n  findHighPriorityActions(limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find automated response actions\r\n   * Actions that can be executed automatically\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to automated actions\r\n   */\r\n  findAutomatedActions(limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find manual response actions\r\n   * Actions requiring human intervention\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to manual actions\r\n   */\r\n  findManualActions(limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find response actions requiring approval\r\n   * Actions waiting for approval before execution\r\n   * \r\n   * @param approvalLevel Optional specific approval level\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to actions requiring approval\r\n   */\r\n  findRequiringApproval(\r\n    approvalLevel?: 'analyst' | 'senior' | 'manager' | 'director',\r\n    limit?: number\r\n  ): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find approved response actions\r\n   * Actions that have been approved for execution\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to approved actions\r\n   */\r\n  findApprovedActions(limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find response actions by target type\r\n   * Target-specific action analysis\r\n   * \r\n   * @param targetType The target type to filter by\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to actions targeting the specified type\r\n   */\r\n  findByTargetType(\r\n    targetType: 'event' | 'threat' | 'vulnerability' | 'system' | 'user' | 'network' | 'custom',\r\n    limit?: number\r\n  ): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find response actions by target ID\r\n   * Target-specific action tracking\r\n   * \r\n   * @param targetId The target ID to search for\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to actions targeting the specified ID\r\n   */\r\n  findByTargetId(targetId: string, limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find reversible response actions\r\n   * Actions that can be rolled back\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to reversible actions\r\n   */\r\n  findReversibleActions(limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find rolled back response actions\r\n   * Actions that have been rolled back\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to rolled back actions\r\n   */\r\n  findRolledBackActions(limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find response actions by execution time range\r\n   * Performance analysis for action execution\r\n   * \r\n   * @param minDurationMinutes Minimum execution duration in minutes\r\n   * @param maxDurationMinutes Maximum execution duration in minutes\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to actions within the execution time range\r\n   */\r\n  findByExecutionTimeRange(\r\n    minDurationMinutes?: number,\r\n    maxDurationMinutes?: number,\r\n    limit?: number\r\n  ): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find long-running response actions\r\n   * Actions that have been running longer than expected\r\n   * \r\n   * @param thresholdMinutes Duration threshold in minutes (default: 60)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to long-running actions\r\n   */\r\n  findLongRunningActions(thresholdMinutes?: number, limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find response actions by executor\r\n   * Track actions by who executed them\r\n   * \r\n   * @param executorId The executor ID\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to actions executed by the specified executor\r\n   */\r\n  findByExecutor(executorId: string, limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find response actions by approver\r\n   * Track actions by who approved them\r\n   * \r\n   * @param approverId The approver ID\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to actions approved by the specified approver\r\n   */\r\n  findByApprover(approverId: string, limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find recent response actions within a time window\r\n   * Recent action monitoring and analysis\r\n   * \r\n   * @param withinHours Time window in hours (default: 24)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to recent actions\r\n   */\r\n  findRecentActions(withinHours?: number, limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find scheduled response actions\r\n   * Actions scheduled for future execution\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to scheduled actions\r\n   */\r\n  findScheduledActions(limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find overdue response actions\r\n   * Actions that should have been executed by now\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to overdue actions\r\n   */\r\n  findOverdueActions(limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Count response actions by status\r\n   * Status distribution analysis\r\n   * \r\n   * @returns Promise resolving to status counts\r\n   */\r\n  countByStatus(): Promise<Record<ActionStatus, number>>;\r\n\r\n  /**\r\n   * Count response actions by type\r\n   * Type distribution analysis\r\n   * \r\n   * @returns Promise resolving to type counts\r\n   */\r\n  countByActionType(): Promise<Record<ActionType, number>>;\r\n\r\n  /**\r\n   * Count response actions by priority\r\n   * Priority distribution analysis\r\n   * \r\n   * @returns Promise resolving to priority counts\r\n   */\r\n  countByPriority(): Promise<Record<'low' | 'normal' | 'high' | 'critical', number>>;\r\n\r\n  /**\r\n   * Get response action statistics for a time period\r\n   * Comprehensive action metrics and analysis\r\n   * \r\n   * @param startTime Start of the time period\r\n   * @param endTime End of the time period\r\n   * @returns Promise resolving to response action statistics\r\n   */\r\n  getActionStatistics(startTime: Date, endTime: Date): Promise<{\r\n    totalActions: number;\r\n    actionsByStatus: Record<ActionStatus, number>;\r\n    actionsByType: Record<ActionType, number>;\r\n    actionsByPriority: Record<'low' | 'normal' | 'high' | 'critical', number>;\r\n    automatedActions: number;\r\n    manualActions: number;\r\n    successRate: number;\r\n    averageExecutionTime: number;\r\n    approvalMetrics: {\r\n      actionsRequiringApproval: number;\r\n      averageApprovalTime: number;\r\n      approvalsByLevel: Record<string, number>;\r\n    };\r\n    performanceMetrics: {\r\n      fastestExecution: number;\r\n      slowestExecution: number;\r\n      timeoutCount: number;\r\n      rollbackCount: number;\r\n    };\r\n    targetDistribution: Record<string, number>;\r\n    executorMetrics: Record<string, {\r\n      actionsExecuted: number;\r\n      successRate: number;\r\n      averageExecutionTime: number;\r\n    }>;\r\n  }>;\r\n\r\n  /**\r\n   * Find response actions for audit\r\n   * Compliance and audit trail queries\r\n   * \r\n   * @param criteria Audit criteria\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to actions for audit\r\n   */\r\n  findForAudit(criteria?: {\r\n    timeRange?: { start: Date; end: Date };\r\n    actionTypes?: ActionType[];\r\n    statuses?: ActionStatus[];\r\n    priorities?: Array<'low' | 'normal' | 'high' | 'critical'>;\r\n    includeRollbacks?: boolean;\r\n    includeFailures?: boolean;\r\n  }, limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Find response actions by effectiveness\r\n   * Analyze action effectiveness and success rates\r\n   * \r\n   * @param minSuccessRate Minimum success rate threshold\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to effective actions\r\n   */\r\n  findByEffectiveness(minSuccessRate?: number, limit?: number): Promise<ResponseAction[]>;\r\n\r\n  /**\r\n   * Bulk update action status\r\n   * Efficient batch operation for action management\r\n   * \r\n   * @param actionIds Array of action IDs to update\r\n   * @param newStatus New action status\r\n   * @param updatedBy Who is performing the update\r\n   * @returns Promise resolving to the number of updated actions\r\n   */\r\n  bulkUpdateStatus(\r\n    actionIds: UniqueEntityId[],\r\n    newStatus: ActionStatus,\r\n    updatedBy?: string\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Archive old completed actions\r\n   * Moves old completed actions to archive storage\r\n   * \r\n   * @param olderThanDays Age threshold in days\r\n   * @param batchSize Number of actions to archive per batch\r\n   * @returns Promise resolving to the number of archived actions\r\n   */\r\n  archiveOldActions(olderThanDays: number, batchSize?: number): Promise<number>;\r\n\r\n  /**\r\n   * Find response action patterns\r\n   * Analyze patterns in response actions for optimization\r\n   * \r\n   * @param timeWindow Time window for pattern analysis in days\r\n   * @returns Promise resolving to action pattern data\r\n   */\r\n  findActionPatterns(timeWindow?: number): Promise<Array<{\r\n    pattern: string;\r\n    frequency: number;\r\n    successRate: number;\r\n    averageExecutionTime: number;\r\n    commonTargets: string[];\r\n    recommendations: string[];\r\n  }>>;\r\n}"], "version": 3}