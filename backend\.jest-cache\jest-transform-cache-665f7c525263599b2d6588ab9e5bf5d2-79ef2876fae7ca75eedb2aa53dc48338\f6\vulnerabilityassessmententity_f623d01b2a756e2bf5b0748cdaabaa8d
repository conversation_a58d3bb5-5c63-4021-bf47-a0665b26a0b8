18742b3310bbe61488b16632f0383a5e
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityAssessment = void 0;
const typeorm_1 = require("typeorm");
const vulnerability_entity_1 = require("./vulnerability.entity");
const vulnerability_scan_entity_1 = require("./vulnerability-scan.entity");
const asset_entity_1 = require("../../../asset-management/domain/entities/asset.entity");
/**
 * Vulnerability Assessment entity
 * Represents an assessment or analysis of a vulnerability
 */
let VulnerabilityAssessment = class VulnerabilityAssessment {
    /**
     * Check if assessment is completed
     */
    get isCompleted() {
        return this.status === 'completed';
    }
    /**
     * Check if assessment is pending
     */
    get isPending() {
        return this.status === 'pending';
    }
    /**
     * Check if assessment is in progress
     */
    get isInProgress() {
        return this.status === 'in_progress';
    }
    /**
     * Check if assessment was rejected
     */
    get isRejected() {
        return this.status === 'rejected';
    }
    /**
     * Check if assessment has been reviewed
     */
    get isReviewed() {
        return this.reviewedBy !== null && this.reviewedAt !== null;
    }
    /**
     * Get assessment age in days
     */
    get ageInDays() {
        const now = new Date();
        const diffMs = now.getTime() - this.assessedAt.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Check if assessment is high confidence
     */
    get isHighConfidence() {
        return this.confidenceLevel >= 80;
    }
    /**
     * Check if assessment changed the original severity
     */
    get severityChanged() {
        return this.assessedSeverity !== null;
    }
    /**
     * Start assessment
     */
    start() {
        this.status = 'in_progress';
    }
    /**
     * Complete assessment
     */
    complete() {
        this.status = 'completed';
    }
    /**
     * Reject assessment
     */
    reject(reason) {
        this.status = 'rejected';
        if (reason && this.metadata) {
            this.metadata.rejectionReason = reason;
        }
    }
    /**
     * Review assessment
     */
    review(reviewedBy, comments) {
        this.reviewedBy = reviewedBy;
        this.reviewedAt = new Date();
        if (comments) {
            this.reviewComments = comments;
        }
    }
    /**
     * Mark as false positive
     */
    markAsFalsePositive(justification) {
        this.isFalsePositive = true;
        this.falsePositiveJustification = justification;
        this.assessedSeverity = 'info';
    }
    /**
     * Accept risk
     */
    acceptRisk(justification) {
        this.isAcceptedRisk = true;
        this.riskAcceptanceJustification = justification;
    }
    /**
     * Get assessment summary
     */
    getSummary() {
        return {
            id: this.id,
            vulnerabilityId: this.vulnerabilityId,
            assetId: this.assetId,
            scanId: this.scanId,
            assessmentType: this.assessmentType,
            status: this.status,
            assessedSeverity: this.assessedSeverity,
            assessedCvssScore: this.assessedCvssScore,
            assessedRiskScore: this.assessedRiskScore,
            assessedPriority: this.assessedPriority,
            confidenceLevel: this.confidenceLevel,
            isFalsePositive: this.isFalsePositive,
            isAcceptedRisk: this.isAcceptedRisk,
            isCompleted: this.isCompleted,
            isPending: this.isPending,
            isInProgress: this.isInProgress,
            isRejected: this.isRejected,
            isReviewed: this.isReviewed,
            isHighConfidence: this.isHighConfidence,
            severityChanged: this.severityChanged,
            ageInDays: this.ageInDays,
            assessedBy: this.assessedBy,
            assessedAt: this.assessedAt,
            reviewedBy: this.reviewedBy,
            reviewedAt: this.reviewedAt,
        };
    }
    /**
     * Export assessment for reporting
     */
    exportForReporting() {
        return {
            assessment: this.getSummary(),
            findings: this.findings,
            recommendedActions: this.recommendedActions,
            businessImpact: this.businessImpact,
            technicalImpact: this.technicalImpact,
            exploitabilityAssessment: this.exploitabilityAssessment,
            mitigationStrategies: this.mitigationStrategies,
            compensatingControls: this.compensatingControls,
            methodology: this.methodology,
            toolsUsed: this.toolsUsed,
            assessmentCriteria: this.assessmentCriteria,
            reviewComments: this.reviewComments,
            metadata: this.metadata,
            exportedAt: new Date().toISOString(),
        };
    }
};
exports.VulnerabilityAssessment = VulnerabilityAssessment;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vulnerability_id', type: 'uuid' }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "vulnerabilityId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'asset_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "assetId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'scan_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "scanId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['manual', 'automated', 'ai_assisted', 'peer_review'],
        default: 'manual',
    }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "assessmentType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['pending', 'in_progress', 'completed', 'rejected'],
        default: 'pending',
    }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['critical', 'high', 'medium', 'low', 'info'],
        nullable: true,
    }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "assessedSeverity", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assessed_cvss_score', type: 'decimal', precision: 3, scale: 1, nullable: true }),
    __metadata("design:type", Number)
], VulnerabilityAssessment.prototype, "assessedCvssScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assessed_risk_score', type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], VulnerabilityAssessment.prototype, "assessedRiskScore", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['urgent', 'high', 'medium', 'low'],
        nullable: true,
    }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "assessedPriority", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "findings", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'recommended_actions', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "recommendedActions", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'business_impact', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "businessImpact", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'technical_impact', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "technicalImpact", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'exploitability_assessment', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "exploitabilityAssessment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mitigation_strategies', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], VulnerabilityAssessment.prototype, "mitigationStrategies", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'compensating_controls', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], VulnerabilityAssessment.prototype, "compensatingControls", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'confidence_level', type: 'integer', default: 50 }),
    __metadata("design:type", Number)
], VulnerabilityAssessment.prototype, "confidenceLevel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_false_positive', default: false }),
    __metadata("design:type", Boolean)
], VulnerabilityAssessment.prototype, "isFalsePositive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'false_positive_justification', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "falsePositiveJustification", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_accepted_risk', default: false }),
    __metadata("design:type", Boolean)
], VulnerabilityAssessment.prototype, "isAcceptedRisk", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'risk_acceptance_justification', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "riskAcceptanceJustification", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "methodology", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], VulnerabilityAssessment.prototype, "toolsUsed", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assessment_criteria', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], VulnerabilityAssessment.prototype, "assessmentCriteria", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Record !== "undefined" && Record) === "function" ? _b : Object)
], VulnerabilityAssessment.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assessed_by', type: 'uuid' }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "assessedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assessed_at', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], VulnerabilityAssessment.prototype, "assessedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'reviewed_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "reviewedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'reviewed_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], VulnerabilityAssessment.prototype, "reviewedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'review_comments', type: 'text', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityAssessment.prototype, "reviewComments", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], VulnerabilityAssessment.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], VulnerabilityAssessment.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => vulnerability_entity_1.Vulnerability, vulnerability => vulnerability.assessments),
    (0, typeorm_1.JoinColumn)({ name: 'vulnerability_id' }),
    __metadata("design:type", typeof (_g = typeof vulnerability_entity_1.Vulnerability !== "undefined" && vulnerability_entity_1.Vulnerability) === "function" ? _g : Object)
], VulnerabilityAssessment.prototype, "vulnerability", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => asset_entity_1.Asset, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'asset_id' }),
    __metadata("design:type", typeof (_h = typeof asset_entity_1.Asset !== "undefined" && asset_entity_1.Asset) === "function" ? _h : Object)
], VulnerabilityAssessment.prototype, "asset", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => vulnerability_scan_entity_1.VulnerabilityScan, scan => scan.assessments, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'scan_id' }),
    __metadata("design:type", typeof (_j = typeof vulnerability_scan_entity_1.VulnerabilityScan !== "undefined" && vulnerability_scan_entity_1.VulnerabilityScan) === "function" ? _j : Object)
], VulnerabilityAssessment.prototype, "scan", void 0);
exports.VulnerabilityAssessment = VulnerabilityAssessment = __decorate([
    (0, typeorm_1.Entity)('vulnerability_assessments'),
    (0, typeorm_1.Index)(['vulnerabilityId']),
    (0, typeorm_1.Index)(['assetId']),
    (0, typeorm_1.Index)(['scanId']),
    (0, typeorm_1.Index)(['assessedBy']),
    (0, typeorm_1.Index)(['assessedAt']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['assessedSeverity'])
], VulnerabilityAssessment);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFx2dWxuZXJhYmlsaXR5LW1hbmFnZW1lbnRcXGRvbWFpblxcZW50aXRpZXNcXHZ1bG5lcmFiaWxpdHktYXNzZXNzbWVudC5lbnRpdHkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFBLHFDQVNpQjtBQUNqQixpRUFBdUQ7QUFDdkQsMkVBQWdFO0FBQ2hFLHlGQUErRTtBQUUvRTs7O0dBR0c7QUFTSSxJQUFNLHVCQUF1QixHQUE3QixNQUFNLHVCQUF1QjtJQTJObEM7O09BRUc7SUFDSCxJQUFJLFdBQVc7UUFDYixPQUFPLElBQUksQ0FBQyxNQUFNLEtBQUssV0FBVyxDQUFDO0lBQ3JDLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksU0FBUztRQUNYLE9BQU8sSUFBSSxDQUFDLE1BQU0sS0FBSyxTQUFTLENBQUM7SUFDbkMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxZQUFZO1FBQ2QsT0FBTyxJQUFJLENBQUMsTUFBTSxLQUFLLGFBQWEsQ0FBQztJQUN2QyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLFVBQVU7UUFDWixPQUFPLElBQUksQ0FBQyxNQUFNLEtBQUssVUFBVSxDQUFDO0lBQ3BDLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksVUFBVTtRQUNaLE9BQU8sSUFBSSxDQUFDLFVBQVUsS0FBSyxJQUFJLElBQUksSUFBSSxDQUFDLFVBQVUsS0FBSyxJQUFJLENBQUM7SUFDOUQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxTQUFTO1FBQ1gsTUFBTSxHQUFHLEdBQUcsSUFBSSxJQUFJLEVBQUUsQ0FBQztRQUN2QixNQUFNLE1BQU0sR0FBRyxHQUFHLENBQUMsT0FBTyxFQUFFLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLEVBQUUsQ0FBQztRQUN6RCxPQUFPLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxHQUFHLENBQUMsSUFBSSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQztJQUNwRCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLGdCQUFnQjtRQUNsQixPQUFPLElBQUksQ0FBQyxlQUFlLElBQUksRUFBRSxDQUFDO0lBQ3BDLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksZUFBZTtRQUNqQixPQUFPLElBQUksQ0FBQyxnQkFBZ0IsS0FBSyxJQUFJLENBQUM7SUFDeEMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsS0FBSztRQUNILElBQUksQ0FBQyxNQUFNLEdBQUcsYUFBYSxDQUFDO0lBQzlCLENBQUM7SUFFRDs7T0FFRztJQUNILFFBQVE7UUFDTixJQUFJLENBQUMsTUFBTSxHQUFHLFdBQVcsQ0FBQztJQUM1QixDQUFDO0lBRUQ7O09BRUc7SUFDSCxNQUFNLENBQUMsTUFBZTtRQUNwQixJQUFJLENBQUMsTUFBTSxHQUFHLFVBQVUsQ0FBQztRQUN6QixJQUFJLE1BQU0sSUFBSSxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUM7WUFDNUIsSUFBSSxDQUFDLFFBQVEsQ0FBQyxlQUFlLEdBQUcsTUFBTSxDQUFDO1FBQ3pDLENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxNQUFNLENBQUMsVUFBa0IsRUFBRSxRQUFpQjtRQUMxQyxJQUFJLENBQUMsVUFBVSxHQUFHLFVBQVUsQ0FBQztRQUM3QixJQUFJLENBQUMsVUFBVSxHQUFHLElBQUksSUFBSSxFQUFFLENBQUM7UUFDN0IsSUFBSSxRQUFRLEVBQUUsQ0FBQztZQUNiLElBQUksQ0FBQyxjQUFjLEdBQUcsUUFBUSxDQUFDO1FBQ2pDLENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxtQkFBbUIsQ0FBQyxhQUFxQjtRQUN2QyxJQUFJLENBQUMsZUFBZSxHQUFHLElBQUksQ0FBQztRQUM1QixJQUFJLENBQUMsMEJBQTBCLEdBQUcsYUFBYSxDQUFDO1FBQ2hELElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxNQUFNLENBQUM7SUFDakMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsVUFBVSxDQUFDLGFBQXFCO1FBQzlCLElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFDO1FBQzNCLElBQUksQ0FBQywyQkFBMkIsR0FBRyxhQUFhLENBQUM7SUFDbkQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsVUFBVTtRQUNSLE9BQU87WUFDTCxFQUFFLEVBQUUsSUFBSSxDQUFDLEVBQUU7WUFDWCxlQUFlLEVBQUUsSUFBSSxDQUFDLGVBQWU7WUFDckMsT0FBTyxFQUFFLElBQUksQ0FBQyxPQUFPO1lBQ3JCLE1BQU0sRUFBRSxJQUFJLENBQUMsTUFBTTtZQUNuQixjQUFjLEVBQUUsSUFBSSxDQUFDLGNBQWM7WUFDbkMsTUFBTSxFQUFFLElBQUksQ0FBQyxNQUFNO1lBQ25CLGdCQUFnQixFQUFFLElBQUksQ0FBQyxnQkFBZ0I7WUFDdkMsaUJBQWlCLEVBQUUsSUFBSSxDQUFDLGlCQUFpQjtZQUN6QyxpQkFBaUIsRUFBRSxJQUFJLENBQUMsaUJBQWlCO1lBQ3pDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxnQkFBZ0I7WUFDdkMsZUFBZSxFQUFFLElBQUksQ0FBQyxlQUFlO1lBQ3JDLGVBQWUsRUFBRSxJQUFJLENBQUMsZUFBZTtZQUNyQyxjQUFjLEVBQUUsSUFBSSxDQUFDLGNBQWM7WUFDbkMsV0FBVyxFQUFFLElBQUksQ0FBQyxXQUFXO1lBQzdCLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUztZQUN6QixZQUFZLEVBQUUsSUFBSSxDQUFDLFlBQVk7WUFDL0IsVUFBVSxFQUFFLElBQUksQ0FBQyxVQUFVO1lBQzNCLFVBQVUsRUFBRSxJQUFJLENBQUMsVUFBVTtZQUMzQixnQkFBZ0IsRUFBRSxJQUFJLENBQUMsZ0JBQWdCO1lBQ3ZDLGVBQWUsRUFBRSxJQUFJLENBQUMsZUFBZTtZQUNyQyxTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVM7WUFDekIsVUFBVSxFQUFFLElBQUksQ0FBQyxVQUFVO1lBQzNCLFVBQVUsRUFBRSxJQUFJLENBQUMsVUFBVTtZQUMzQixVQUFVLEVBQUUsSUFBSSxDQUFDLFVBQVU7WUFDM0IsVUFBVSxFQUFFLElBQUksQ0FBQyxVQUFVO1NBQzVCLENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSCxrQkFBa0I7UUFDaEIsT0FBTztZQUNMLFVBQVUsRUFBRSxJQUFJLENBQUMsVUFBVSxFQUFFO1lBQzdCLFFBQVEsRUFBRSxJQUFJLENBQUMsUUFBUTtZQUN2QixrQkFBa0IsRUFBRSxJQUFJLENBQUMsa0JBQWtCO1lBQzNDLGNBQWMsRUFBRSxJQUFJLENBQUMsY0FBYztZQUNuQyxlQUFlLEVBQUUsSUFBSSxDQUFDLGVBQWU7WUFDckMsd0JBQXdCLEVBQUUsSUFBSSxDQUFDLHdCQUF3QjtZQUN2RCxvQkFBb0IsRUFBRSxJQUFJLENBQUMsb0JBQW9CO1lBQy9DLG9CQUFvQixFQUFFLElBQUksQ0FBQyxvQkFBb0I7WUFDL0MsV0FBVyxFQUFFLElBQUksQ0FBQyxXQUFXO1lBQzdCLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUztZQUN6QixrQkFBa0IsRUFBRSxJQUFJLENBQUMsa0JBQWtCO1lBQzNDLGNBQWMsRUFBRSxJQUFJLENBQUMsY0FBYztZQUNuQyxRQUFRLEVBQUUsSUFBSSxDQUFDLFFBQVE7WUFDdkIsVUFBVSxFQUFFLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFO1NBQ3JDLENBQUM7SUFDSixDQUFDO0NBQ0YsQ0FBQTtBQS9YWSwwREFBdUI7QUFFbEM7SUFEQyxJQUFBLGdDQUFzQixFQUFDLE1BQU0sQ0FBQzs7bURBQ3BCO0FBTVg7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsa0JBQWtCLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxDQUFDOztnRUFDM0I7QUFNeEI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsVUFBVSxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOzt3REFDMUM7QUFNakI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsU0FBUyxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOzt1REFDMUM7QUFVaEI7SUFMQyxJQUFBLGdCQUFNLEVBQUM7UUFDTixJQUFJLEVBQUUsTUFBTTtRQUNaLElBQUksRUFBRSxDQUFDLFFBQVEsRUFBRSxXQUFXLEVBQUUsYUFBYSxFQUFFLGFBQWEsQ0FBQztRQUMzRCxPQUFPLEVBQUUsUUFBUTtLQUNsQixDQUFDOzsrREFDcUU7QUFVdkU7SUFMQyxJQUFBLGdCQUFNLEVBQUM7UUFDTixJQUFJLEVBQUUsTUFBTTtRQUNaLElBQUksRUFBRSxDQUFDLFNBQVMsRUFBRSxhQUFhLEVBQUUsV0FBVyxFQUFFLFVBQVUsQ0FBQztRQUN6RCxPQUFPLEVBQUUsU0FBUztLQUNuQixDQUFDOzt1REFDMkQ7QUFVN0Q7SUFMQyxJQUFBLGdCQUFNLEVBQUM7UUFDTixJQUFJLEVBQUUsTUFBTTtRQUNaLElBQUksRUFBRSxDQUFDLFVBQVUsRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLEtBQUssRUFBRSxNQUFNLENBQUM7UUFDbkQsUUFBUSxFQUFFLElBQUk7S0FDZixDQUFDOztpRUFDaUU7QUFNbkU7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUscUJBQXFCLEVBQUUsSUFBSSxFQUFFLFNBQVMsRUFBRSxTQUFTLEVBQUUsQ0FBQyxFQUFFLEtBQUssRUFBRSxDQUFDLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOztrRUFDdEU7QUFNM0I7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUscUJBQXFCLEVBQUUsSUFBSSxFQUFFLFNBQVMsRUFBRSxTQUFTLEVBQUUsQ0FBQyxFQUFFLEtBQUssRUFBRSxDQUFDLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOztrRUFDdEU7QUFVM0I7SUFMQyxJQUFBLGdCQUFNLEVBQUM7UUFDTixJQUFJLEVBQUUsTUFBTTtRQUNaLElBQUksRUFBRSxDQUFDLFFBQVEsRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLEtBQUssQ0FBQztRQUN6QyxRQUFRLEVBQUUsSUFBSTtLQUNmLENBQUM7O2lFQUNzRDtBQU14RDtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOzt5REFDdkI7QUFNbEI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUscUJBQXFCLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7O21FQUMxQztBQU01QjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxpQkFBaUIsRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQzs7K0RBQzFDO0FBTXhCO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGtCQUFrQixFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOztnRUFDMUM7QUFNekI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsMkJBQTJCLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7O3lFQUMxQztBQU1sQztJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSx1QkFBdUIsRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQzs7cUVBQ3pDO0FBTWhDO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLHVCQUF1QixFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOztxRUFDekM7QUFNaEM7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsa0JBQWtCLEVBQUUsSUFBSSxFQUFFLFNBQVMsRUFBRSxPQUFPLEVBQUUsRUFBRSxFQUFFLENBQUM7O2dFQUMzQztBQU14QjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxtQkFBbUIsRUFBRSxPQUFPLEVBQUUsS0FBSyxFQUFFLENBQUM7O2dFQUM3QjtBQU16QjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSw4QkFBOEIsRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQzs7MkVBQzNDO0FBTXBDO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGtCQUFrQixFQUFFLE9BQU8sRUFBRSxLQUFLLEVBQUUsQ0FBQzs7K0RBQzdCO0FBTXhCO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLCtCQUErQixFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOzs0RUFDM0M7QUFNckM7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7OzREQUNOO0FBTXJCO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7OzBEQUNyQjtBQU1yQjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxxQkFBcUIsRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztrREFDbEQsTUFBTSxvQkFBTixNQUFNO21FQUFjO0FBTXpDO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7a0RBQy9CLE1BQU0sb0JBQU4sTUFBTTt5REFBYztBQU0vQjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxhQUFhLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxDQUFDOzsyREFDM0I7QUFNbkI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsYUFBYSxFQUFFLElBQUksRUFBRSwwQkFBMEIsRUFBRSxDQUFDO2tEQUN0RCxJQUFJLG9CQUFKLElBQUk7MkRBQUM7QUFNakI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsYUFBYSxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOzsyREFDMUM7QUFNcEI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsYUFBYSxFQUFFLElBQUksRUFBRSwwQkFBMEIsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7a0RBQ3JFLElBQUksb0JBQUosSUFBSTsyREFBQztBQU1sQjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxpQkFBaUIsRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQzs7K0RBQzFDO0FBR3hCO0lBREMsSUFBQSwwQkFBZ0IsRUFBQyxFQUFFLElBQUksRUFBRSxZQUFZLEVBQUUsQ0FBQztrREFDOUIsSUFBSSxvQkFBSixJQUFJOzBEQUFDO0FBR2hCO0lBREMsSUFBQSwwQkFBZ0IsRUFBQyxFQUFFLElBQUksRUFBRSxZQUFZLEVBQUUsQ0FBQztrREFDOUIsSUFBSSxvQkFBSixJQUFJOzBEQUFDO0FBS2hCO0lBRkMsSUFBQSxtQkFBUyxFQUFDLEdBQUcsRUFBRSxDQUFDLG9DQUFhLEVBQUUsYUFBYSxDQUFDLEVBQUUsQ0FBQyxhQUFhLENBQUMsV0FBVyxDQUFDO0lBQzFFLElBQUEsb0JBQVUsRUFBQyxFQUFFLElBQUksRUFBRSxrQkFBa0IsRUFBRSxDQUFDO2tEQUMxQixvQ0FBYSxvQkFBYixvQ0FBYTs4REFBQztBQUk3QjtJQUZDLElBQUEsbUJBQVMsRUFBQyxHQUFHLEVBQUUsQ0FBQyxvQkFBSyxFQUFFLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDO0lBQzFDLElBQUEsb0JBQVUsRUFBQyxFQUFFLElBQUksRUFBRSxVQUFVLEVBQUUsQ0FBQztrREFDekIsb0JBQUssb0JBQUwsb0JBQUs7c0RBQUM7QUFJZDtJQUZDLElBQUEsbUJBQVMsRUFBQyxHQUFHLEVBQUUsQ0FBQyw2Q0FBaUIsRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxXQUFXLEVBQUUsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7SUFDaEYsSUFBQSxvQkFBVSxFQUFDLEVBQUUsSUFBSSxFQUFFLFNBQVMsRUFBRSxDQUFDO2tEQUN6Qiw2Q0FBaUIsb0JBQWpCLDZDQUFpQjtxREFBQztrQ0F6TmQsdUJBQXVCO0lBUm5DLElBQUEsZ0JBQU0sRUFBQywyQkFBMkIsQ0FBQztJQUNuQyxJQUFBLGVBQUssRUFBQyxDQUFDLGlCQUFpQixDQUFDLENBQUM7SUFDMUIsSUFBQSxlQUFLLEVBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQztJQUNsQixJQUFBLGVBQUssRUFBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ2pCLElBQUEsZUFBSyxFQUFDLENBQUMsWUFBWSxDQUFDLENBQUM7SUFDckIsSUFBQSxlQUFLLEVBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQztJQUNyQixJQUFBLGVBQUssRUFBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ2pCLElBQUEsZUFBSyxFQUFDLENBQUMsa0JBQWtCLENBQUMsQ0FBQztHQUNmLHVCQUF1QixDQStYbkMiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFx2dWxuZXJhYmlsaXR5LW1hbmFnZW1lbnRcXGRvbWFpblxcZW50aXRpZXNcXHZ1bG5lcmFiaWxpdHktYXNzZXNzbWVudC5lbnRpdHkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcclxuICBFbnRpdHksXHJcbiAgUHJpbWFyeUdlbmVyYXRlZENvbHVtbixcclxuICBDb2x1bW4sXHJcbiAgQ3JlYXRlRGF0ZUNvbHVtbixcclxuICBVcGRhdGVEYXRlQ29sdW1uLFxyXG4gIE1hbnlUb09uZSxcclxuICBKb2luQ29sdW1uLFxyXG4gIEluZGV4LFxyXG59IGZyb20gJ3R5cGVvcm0nO1xyXG5pbXBvcnQgeyBWdWxuZXJhYmlsaXR5IH0gZnJvbSAnLi92dWxuZXJhYmlsaXR5LmVudGl0eSc7XHJcbmltcG9ydCB7IFZ1bG5lcmFiaWxpdHlTY2FuIH0gZnJvbSAnLi92dWxuZXJhYmlsaXR5LXNjYW4uZW50aXR5JztcclxuaW1wb3J0IHsgQXNzZXQgfSBmcm9tICcuLi8uLi8uLi9hc3NldC1tYW5hZ2VtZW50L2RvbWFpbi9lbnRpdGllcy9hc3NldC5lbnRpdHknO1xyXG5cclxuLyoqXHJcbiAqIFZ1bG5lcmFiaWxpdHkgQXNzZXNzbWVudCBlbnRpdHlcclxuICogUmVwcmVzZW50cyBhbiBhc3Nlc3NtZW50IG9yIGFuYWx5c2lzIG9mIGEgdnVsbmVyYWJpbGl0eVxyXG4gKi9cclxuQEVudGl0eSgndnVsbmVyYWJpbGl0eV9hc3Nlc3NtZW50cycpXHJcbkBJbmRleChbJ3Z1bG5lcmFiaWxpdHlJZCddKVxyXG5ASW5kZXgoWydhc3NldElkJ10pXHJcbkBJbmRleChbJ3NjYW5JZCddKVxyXG5ASW5kZXgoWydhc3Nlc3NlZEJ5J10pXHJcbkBJbmRleChbJ2Fzc2Vzc2VkQXQnXSlcclxuQEluZGV4KFsnc3RhdHVzJ10pXHJcbkBJbmRleChbJ2Fzc2Vzc2VkU2V2ZXJpdHknXSlcclxuZXhwb3J0IGNsYXNzIFZ1bG5lcmFiaWxpdHlBc3Nlc3NtZW50IHtcclxuICBAUHJpbWFyeUdlbmVyYXRlZENvbHVtbigndXVpZCcpXHJcbiAgaWQ6IHN0cmluZztcclxuXHJcbiAgLyoqXHJcbiAgICogQXNzb2NpYXRlZCB2dWxuZXJhYmlsaXR5XHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICd2dWxuZXJhYmlsaXR5X2lkJywgdHlwZTogJ3V1aWQnIH0pXHJcbiAgdnVsbmVyYWJpbGl0eUlkOiBzdHJpbmc7XHJcblxyXG4gIC8qKlxyXG4gICAqIEFzc29jaWF0ZWQgYXNzZXQgKGlmIGFzc2Vzc21lbnQgaXMgYXNzZXQtc3BlY2lmaWMpXHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICdhc3NldF9pZCcsIHR5cGU6ICd1dWlkJywgbnVsbGFibGU6IHRydWUgfSlcclxuICBhc3NldElkPzogc3RyaW5nO1xyXG5cclxuICAvKipcclxuICAgKiBBc3NvY2lhdGVkIHNjYW4gKGlmIGFzc2Vzc21lbnQgY2FtZSBmcm9tIGEgc2NhbilcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ3NjYW5faWQnLCB0eXBlOiAndXVpZCcsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgc2NhbklkPzogc3RyaW5nO1xyXG5cclxuICAvKipcclxuICAgKiBBc3Nlc3NtZW50IHR5cGVcclxuICAgKi9cclxuICBAQ29sdW1uKHtcclxuICAgIHR5cGU6ICdlbnVtJyxcclxuICAgIGVudW06IFsnbWFudWFsJywgJ2F1dG9tYXRlZCcsICdhaV9hc3Npc3RlZCcsICdwZWVyX3JldmlldyddLFxyXG4gICAgZGVmYXVsdDogJ21hbnVhbCcsXHJcbiAgfSlcclxuICBhc3Nlc3NtZW50VHlwZTogJ21hbnVhbCcgfCAnYXV0b21hdGVkJyB8ICdhaV9hc3Npc3RlZCcgfCAncGVlcl9yZXZpZXcnO1xyXG5cclxuICAvKipcclxuICAgKiBBc3Nlc3NtZW50IHN0YXR1c1xyXG4gICAqL1xyXG4gIEBDb2x1bW4oe1xyXG4gICAgdHlwZTogJ2VudW0nLFxyXG4gICAgZW51bTogWydwZW5kaW5nJywgJ2luX3Byb2dyZXNzJywgJ2NvbXBsZXRlZCcsICdyZWplY3RlZCddLFxyXG4gICAgZGVmYXVsdDogJ3BlbmRpbmcnLFxyXG4gIH0pXHJcbiAgc3RhdHVzOiAncGVuZGluZycgfCAnaW5fcHJvZ3Jlc3MnIHwgJ2NvbXBsZXRlZCcgfCAncmVqZWN0ZWQnO1xyXG5cclxuICAvKipcclxuICAgKiBBc3Nlc3NlZCBzZXZlcml0eSAobWF5IGRpZmZlciBmcm9tIG9yaWdpbmFsKVxyXG4gICAqL1xyXG4gIEBDb2x1bW4oe1xyXG4gICAgdHlwZTogJ2VudW0nLFxyXG4gICAgZW51bTogWydjcml0aWNhbCcsICdoaWdoJywgJ21lZGl1bScsICdsb3cnLCAnaW5mbyddLFxyXG4gICAgbnVsbGFibGU6IHRydWUsXHJcbiAgfSlcclxuICBhc3Nlc3NlZFNldmVyaXR5PzogJ2NyaXRpY2FsJyB8ICdoaWdoJyB8ICdtZWRpdW0nIHwgJ2xvdycgfCAnaW5mbyc7XHJcblxyXG4gIC8qKlxyXG4gICAqIEFzc2Vzc2VkIENWU1Mgc2NvcmVcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ2Fzc2Vzc2VkX2N2c3Nfc2NvcmUnLCB0eXBlOiAnZGVjaW1hbCcsIHByZWNpc2lvbjogMywgc2NhbGU6IDEsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgYXNzZXNzZWRDdnNzU2NvcmU/OiBudW1iZXI7XHJcblxyXG4gIC8qKlxyXG4gICAqIEFzc2Vzc2VkIHJpc2sgc2NvcmVcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ2Fzc2Vzc2VkX3Jpc2tfc2NvcmUnLCB0eXBlOiAnZGVjaW1hbCcsIHByZWNpc2lvbjogNSwgc2NhbGU6IDIsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgYXNzZXNzZWRSaXNrU2NvcmU/OiBudW1iZXI7XHJcblxyXG4gIC8qKlxyXG4gICAqIEFzc2Vzc2VkIHByaW9yaXR5XHJcbiAgICovXHJcbiAgQENvbHVtbih7XHJcbiAgICB0eXBlOiAnZW51bScsXHJcbiAgICBlbnVtOiBbJ3VyZ2VudCcsICdoaWdoJywgJ21lZGl1bScsICdsb3cnXSxcclxuICAgIG51bGxhYmxlOiB0cnVlLFxyXG4gIH0pXHJcbiAgYXNzZXNzZWRQcmlvcml0eT86ICd1cmdlbnQnIHwgJ2hpZ2gnIHwgJ21lZGl1bScgfCAnbG93JztcclxuXHJcbiAgLyoqXHJcbiAgICogQXNzZXNzbWVudCBmaW5kaW5ncyBhbmQgbm90ZXNcclxuICAgKi9cclxuICBAQ29sdW1uKHsgdHlwZTogJ3RleHQnLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIGZpbmRpbmdzPzogc3RyaW5nO1xyXG5cclxuICAvKipcclxuICAgKiBSZWNvbW1lbmRlZCBhY3Rpb25zXHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICdyZWNvbW1lbmRlZF9hY3Rpb25zJywgdHlwZTogJ3RleHQnLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIHJlY29tbWVuZGVkQWN0aW9ucz86IHN0cmluZztcclxuXHJcbiAgLyoqXHJcbiAgICogQnVzaW5lc3MgaW1wYWN0IGFzc2Vzc21lbnRcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ2J1c2luZXNzX2ltcGFjdCcsIHR5cGU6ICd0ZXh0JywgbnVsbGFibGU6IHRydWUgfSlcclxuICBidXNpbmVzc0ltcGFjdD86IHN0cmluZztcclxuXHJcbiAgLyoqXHJcbiAgICogVGVjaG5pY2FsIGltcGFjdCBhc3Nlc3NtZW50XHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICd0ZWNobmljYWxfaW1wYWN0JywgdHlwZTogJ3RleHQnLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIHRlY2huaWNhbEltcGFjdD86IHN0cmluZztcclxuXHJcbiAgLyoqXHJcbiAgICogRXhwbG9pdGFiaWxpdHkgYXNzZXNzbWVudFxyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBuYW1lOiAnZXhwbG9pdGFiaWxpdHlfYXNzZXNzbWVudCcsIHR5cGU6ICd0ZXh0JywgbnVsbGFibGU6IHRydWUgfSlcclxuICBleHBsb2l0YWJpbGl0eUFzc2Vzc21lbnQ/OiBzdHJpbmc7XHJcblxyXG4gIC8qKlxyXG4gICAqIE1pdGlnYXRpb24gc3RyYXRlZ2llc1xyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBuYW1lOiAnbWl0aWdhdGlvbl9zdHJhdGVnaWVzJywgdHlwZTogJ2pzb25iJywgbnVsbGFibGU6IHRydWUgfSlcclxuICBtaXRpZ2F0aW9uU3RyYXRlZ2llcz86IHN0cmluZ1tdO1xyXG5cclxuICAvKipcclxuICAgKiBDb21wZW5zYXRpbmcgY29udHJvbHNcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ2NvbXBlbnNhdGluZ19jb250cm9scycsIHR5cGU6ICdqc29uYicsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgY29tcGVuc2F0aW5nQ29udHJvbHM/OiBzdHJpbmdbXTtcclxuXHJcbiAgLyoqXHJcbiAgICogQXNzZXNzbWVudCBjb25maWRlbmNlIGxldmVsICgwLTEwMClcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ2NvbmZpZGVuY2VfbGV2ZWwnLCB0eXBlOiAnaW50ZWdlcicsIGRlZmF1bHQ6IDUwIH0pXHJcbiAgY29uZmlkZW5jZUxldmVsOiBudW1iZXI7XHJcblxyXG4gIC8qKlxyXG4gICAqIFdoZXRoZXIgZmFsc2UgcG9zaXRpdmVcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ2lzX2ZhbHNlX3Bvc2l0aXZlJywgZGVmYXVsdDogZmFsc2UgfSlcclxuICBpc0ZhbHNlUG9zaXRpdmU6IGJvb2xlYW47XHJcblxyXG4gIC8qKlxyXG4gICAqIEZhbHNlIHBvc2l0aXZlIGp1c3RpZmljYXRpb25cclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ2ZhbHNlX3Bvc2l0aXZlX2p1c3RpZmljYXRpb24nLCB0eXBlOiAndGV4dCcsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgZmFsc2VQb3NpdGl2ZUp1c3RpZmljYXRpb24/OiBzdHJpbmc7XHJcblxyXG4gIC8qKlxyXG4gICAqIFdoZXRoZXIgYWNjZXB0ZWQgcmlza1xyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBuYW1lOiAnaXNfYWNjZXB0ZWRfcmlzaycsIGRlZmF1bHQ6IGZhbHNlIH0pXHJcbiAgaXNBY2NlcHRlZFJpc2s6IGJvb2xlYW47XHJcblxyXG4gIC8qKlxyXG4gICAqIFJpc2sgYWNjZXB0YW5jZSBqdXN0aWZpY2F0aW9uXHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICdyaXNrX2FjY2VwdGFuY2VfanVzdGlmaWNhdGlvbicsIHR5cGU6ICd0ZXh0JywgbnVsbGFibGU6IHRydWUgfSlcclxuICByaXNrQWNjZXB0YW5jZUp1c3RpZmljYXRpb24/OiBzdHJpbmc7XHJcblxyXG4gIC8qKlxyXG4gICAqIEFzc2Vzc21lbnQgbWV0aG9kb2xvZ3kgdXNlZFxyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIG1ldGhvZG9sb2d5Pzogc3RyaW5nO1xyXG5cclxuICAvKipcclxuICAgKiBUb29scyB1c2VkIGZvciBhc3Nlc3NtZW50XHJcbiAgICovXHJcbiAgQENvbHVtbih7IHR5cGU6ICdqc29uYicsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgdG9vbHNVc2VkPzogc3RyaW5nW107XHJcblxyXG4gIC8qKlxyXG4gICAqIEFzc2Vzc21lbnQgY3JpdGVyaWFcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ2Fzc2Vzc21lbnRfY3JpdGVyaWEnLCB0eXBlOiAnanNvbmInLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIGFzc2Vzc21lbnRDcml0ZXJpYT86IFJlY29yZDxzdHJpbmcsIGFueT47XHJcblxyXG4gIC8qKlxyXG4gICAqIEFkZGl0aW9uYWwgbWV0YWRhdGFcclxuICAgKi9cclxuICBAQ29sdW1uKHsgdHlwZTogJ2pzb25iJywgbnVsbGFibGU6IHRydWUgfSlcclxuICBtZXRhZGF0YT86IFJlY29yZDxzdHJpbmcsIGFueT47XHJcblxyXG4gIC8qKlxyXG4gICAqIFVzZXIgd2hvIHBlcmZvcm1lZCB0aGUgYXNzZXNzbWVudFxyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBuYW1lOiAnYXNzZXNzZWRfYnknLCB0eXBlOiAndXVpZCcgfSlcclxuICBhc3Nlc3NlZEJ5OiBzdHJpbmc7XHJcblxyXG4gIC8qKlxyXG4gICAqIFdoZW4gdGhlIGFzc2Vzc21lbnQgd2FzIHBlcmZvcm1lZFxyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBuYW1lOiAnYXNzZXNzZWRfYXQnLCB0eXBlOiAndGltZXN0YW1wIHdpdGggdGltZSB6b25lJyB9KVxyXG4gIGFzc2Vzc2VkQXQ6IERhdGU7XHJcblxyXG4gIC8qKlxyXG4gICAqIFVzZXIgd2hvIHJldmlld2VkIHRoZSBhc3Nlc3NtZW50XHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICdyZXZpZXdlZF9ieScsIHR5cGU6ICd1dWlkJywgbnVsbGFibGU6IHRydWUgfSlcclxuICByZXZpZXdlZEJ5Pzogc3RyaW5nO1xyXG5cclxuICAvKipcclxuICAgKiBXaGVuIHRoZSBhc3Nlc3NtZW50IHdhcyByZXZpZXdlZFxyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBuYW1lOiAncmV2aWV3ZWRfYXQnLCB0eXBlOiAndGltZXN0YW1wIHdpdGggdGltZSB6b25lJywgbnVsbGFibGU6IHRydWUgfSlcclxuICByZXZpZXdlZEF0PzogRGF0ZTtcclxuXHJcbiAgLyoqXHJcbiAgICogUmV2aWV3IGNvbW1lbnRzXHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICdyZXZpZXdfY29tbWVudHMnLCB0eXBlOiAndGV4dCcsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgcmV2aWV3Q29tbWVudHM/OiBzdHJpbmc7XHJcblxyXG4gIEBDcmVhdGVEYXRlQ29sdW1uKHsgbmFtZTogJ2NyZWF0ZWRfYXQnIH0pXHJcbiAgY3JlYXRlZEF0OiBEYXRlO1xyXG5cclxuICBAVXBkYXRlRGF0ZUNvbHVtbih7IG5hbWU6ICd1cGRhdGVkX2F0JyB9KVxyXG4gIHVwZGF0ZWRBdDogRGF0ZTtcclxuXHJcbiAgLy8gUmVsYXRpb25zaGlwc1xyXG4gIEBNYW55VG9PbmUoKCkgPT4gVnVsbmVyYWJpbGl0eSwgdnVsbmVyYWJpbGl0eSA9PiB2dWxuZXJhYmlsaXR5LmFzc2Vzc21lbnRzKVxyXG4gIEBKb2luQ29sdW1uKHsgbmFtZTogJ3Z1bG5lcmFiaWxpdHlfaWQnIH0pXHJcbiAgdnVsbmVyYWJpbGl0eTogVnVsbmVyYWJpbGl0eTtcclxuXHJcbiAgQE1hbnlUb09uZSgoKSA9PiBBc3NldCwgeyBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIEBKb2luQ29sdW1uKHsgbmFtZTogJ2Fzc2V0X2lkJyB9KVxyXG4gIGFzc2V0PzogQXNzZXQ7XHJcblxyXG4gIEBNYW55VG9PbmUoKCkgPT4gVnVsbmVyYWJpbGl0eVNjYW4sIHNjYW4gPT4gc2Nhbi5hc3Nlc3NtZW50cywgeyBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIEBKb2luQ29sdW1uKHsgbmFtZTogJ3NjYW5faWQnIH0pXHJcbiAgc2Nhbj86IFZ1bG5lcmFiaWxpdHlTY2FuO1xyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBhc3Nlc3NtZW50IGlzIGNvbXBsZXRlZFxyXG4gICAqL1xyXG4gIGdldCBpc0NvbXBsZXRlZCgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCc7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBhc3Nlc3NtZW50IGlzIHBlbmRpbmdcclxuICAgKi9cclxuICBnZXQgaXNQZW5kaW5nKCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMuc3RhdHVzID09PSAncGVuZGluZyc7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBhc3Nlc3NtZW50IGlzIGluIHByb2dyZXNzXHJcbiAgICovXHJcbiAgZ2V0IGlzSW5Qcm9ncmVzcygpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLnN0YXR1cyA9PT0gJ2luX3Byb2dyZXNzJztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIGFzc2Vzc21lbnQgd2FzIHJlamVjdGVkXHJcbiAgICovXHJcbiAgZ2V0IGlzUmVqZWN0ZWQoKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5zdGF0dXMgPT09ICdyZWplY3RlZCc7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBhc3Nlc3NtZW50IGhhcyBiZWVuIHJldmlld2VkXHJcbiAgICovXHJcbiAgZ2V0IGlzUmV2aWV3ZWQoKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXZpZXdlZEJ5ICE9PSBudWxsICYmIHRoaXMucmV2aWV3ZWRBdCAhPT0gbnVsbDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBhc3Nlc3NtZW50IGFnZSBpbiBkYXlzXHJcbiAgICovXHJcbiAgZ2V0IGFnZUluRGF5cygpOiBudW1iZXIge1xyXG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcclxuICAgIGNvbnN0IGRpZmZNcyA9IG5vdy5nZXRUaW1lKCkgLSB0aGlzLmFzc2Vzc2VkQXQuZ2V0VGltZSgpO1xyXG4gICAgcmV0dXJuIE1hdGguZmxvb3IoZGlmZk1zIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIGFzc2Vzc21lbnQgaXMgaGlnaCBjb25maWRlbmNlXHJcbiAgICovXHJcbiAgZ2V0IGlzSGlnaENvbmZpZGVuY2UoKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5jb25maWRlbmNlTGV2ZWwgPj0gODA7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBhc3Nlc3NtZW50IGNoYW5nZWQgdGhlIG9yaWdpbmFsIHNldmVyaXR5XHJcbiAgICovXHJcbiAgZ2V0IHNldmVyaXR5Q2hhbmdlZCgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmFzc2Vzc2VkU2V2ZXJpdHkgIT09IG51bGw7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBTdGFydCBhc3Nlc3NtZW50XHJcbiAgICovXHJcbiAgc3RhcnQoKTogdm9pZCB7XHJcbiAgICB0aGlzLnN0YXR1cyA9ICdpbl9wcm9ncmVzcyc7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDb21wbGV0ZSBhc3Nlc3NtZW50XHJcbiAgICovXHJcbiAgY29tcGxldGUoKTogdm9pZCB7XHJcbiAgICB0aGlzLnN0YXR1cyA9ICdjb21wbGV0ZWQnO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogUmVqZWN0IGFzc2Vzc21lbnRcclxuICAgKi9cclxuICByZWplY3QocmVhc29uPzogc3RyaW5nKTogdm9pZCB7XHJcbiAgICB0aGlzLnN0YXR1cyA9ICdyZWplY3RlZCc7XHJcbiAgICBpZiAocmVhc29uICYmIHRoaXMubWV0YWRhdGEpIHtcclxuICAgICAgdGhpcy5tZXRhZGF0YS5yZWplY3Rpb25SZWFzb24gPSByZWFzb247XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBSZXZpZXcgYXNzZXNzbWVudFxyXG4gICAqL1xyXG4gIHJldmlldyhyZXZpZXdlZEJ5OiBzdHJpbmcsIGNvbW1lbnRzPzogc3RyaW5nKTogdm9pZCB7XHJcbiAgICB0aGlzLnJldmlld2VkQnkgPSByZXZpZXdlZEJ5O1xyXG4gICAgdGhpcy5yZXZpZXdlZEF0ID0gbmV3IERhdGUoKTtcclxuICAgIGlmIChjb21tZW50cykge1xyXG4gICAgICB0aGlzLnJldmlld0NvbW1lbnRzID0gY29tbWVudHM7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBNYXJrIGFzIGZhbHNlIHBvc2l0aXZlXHJcbiAgICovXHJcbiAgbWFya0FzRmFsc2VQb3NpdGl2ZShqdXN0aWZpY2F0aW9uOiBzdHJpbmcpOiB2b2lkIHtcclxuICAgIHRoaXMuaXNGYWxzZVBvc2l0aXZlID0gdHJ1ZTtcclxuICAgIHRoaXMuZmFsc2VQb3NpdGl2ZUp1c3RpZmljYXRpb24gPSBqdXN0aWZpY2F0aW9uO1xyXG4gICAgdGhpcy5hc3Nlc3NlZFNldmVyaXR5ID0gJ2luZm8nO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQWNjZXB0IHJpc2tcclxuICAgKi9cclxuICBhY2NlcHRSaXNrKGp1c3RpZmljYXRpb246IHN0cmluZyk6IHZvaWQge1xyXG4gICAgdGhpcy5pc0FjY2VwdGVkUmlzayA9IHRydWU7XHJcbiAgICB0aGlzLnJpc2tBY2NlcHRhbmNlSnVzdGlmaWNhdGlvbiA9IGp1c3RpZmljYXRpb247XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgYXNzZXNzbWVudCBzdW1tYXJ5XHJcbiAgICovXHJcbiAgZ2V0U3VtbWFyeSgpOiBhbnkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgaWQ6IHRoaXMuaWQsXHJcbiAgICAgIHZ1bG5lcmFiaWxpdHlJZDogdGhpcy52dWxuZXJhYmlsaXR5SWQsXHJcbiAgICAgIGFzc2V0SWQ6IHRoaXMuYXNzZXRJZCxcclxuICAgICAgc2NhbklkOiB0aGlzLnNjYW5JZCxcclxuICAgICAgYXNzZXNzbWVudFR5cGU6IHRoaXMuYXNzZXNzbWVudFR5cGUsXHJcbiAgICAgIHN0YXR1czogdGhpcy5zdGF0dXMsXHJcbiAgICAgIGFzc2Vzc2VkU2V2ZXJpdHk6IHRoaXMuYXNzZXNzZWRTZXZlcml0eSxcclxuICAgICAgYXNzZXNzZWRDdnNzU2NvcmU6IHRoaXMuYXNzZXNzZWRDdnNzU2NvcmUsXHJcbiAgICAgIGFzc2Vzc2VkUmlza1Njb3JlOiB0aGlzLmFzc2Vzc2VkUmlza1Njb3JlLFxyXG4gICAgICBhc3Nlc3NlZFByaW9yaXR5OiB0aGlzLmFzc2Vzc2VkUHJpb3JpdHksXHJcbiAgICAgIGNvbmZpZGVuY2VMZXZlbDogdGhpcy5jb25maWRlbmNlTGV2ZWwsXHJcbiAgICAgIGlzRmFsc2VQb3NpdGl2ZTogdGhpcy5pc0ZhbHNlUG9zaXRpdmUsXHJcbiAgICAgIGlzQWNjZXB0ZWRSaXNrOiB0aGlzLmlzQWNjZXB0ZWRSaXNrLFxyXG4gICAgICBpc0NvbXBsZXRlZDogdGhpcy5pc0NvbXBsZXRlZCxcclxuICAgICAgaXNQZW5kaW5nOiB0aGlzLmlzUGVuZGluZyxcclxuICAgICAgaXNJblByb2dyZXNzOiB0aGlzLmlzSW5Qcm9ncmVzcyxcclxuICAgICAgaXNSZWplY3RlZDogdGhpcy5pc1JlamVjdGVkLFxyXG4gICAgICBpc1Jldmlld2VkOiB0aGlzLmlzUmV2aWV3ZWQsXHJcbiAgICAgIGlzSGlnaENvbmZpZGVuY2U6IHRoaXMuaXNIaWdoQ29uZmlkZW5jZSxcclxuICAgICAgc2V2ZXJpdHlDaGFuZ2VkOiB0aGlzLnNldmVyaXR5Q2hhbmdlZCxcclxuICAgICAgYWdlSW5EYXlzOiB0aGlzLmFnZUluRGF5cyxcclxuICAgICAgYXNzZXNzZWRCeTogdGhpcy5hc3Nlc3NlZEJ5LFxyXG4gICAgICBhc3Nlc3NlZEF0OiB0aGlzLmFzc2Vzc2VkQXQsXHJcbiAgICAgIHJldmlld2VkQnk6IHRoaXMucmV2aWV3ZWRCeSxcclxuICAgICAgcmV2aWV3ZWRBdDogdGhpcy5yZXZpZXdlZEF0LFxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEV4cG9ydCBhc3Nlc3NtZW50IGZvciByZXBvcnRpbmdcclxuICAgKi9cclxuICBleHBvcnRGb3JSZXBvcnRpbmcoKTogYW55IHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGFzc2Vzc21lbnQ6IHRoaXMuZ2V0U3VtbWFyeSgpLFxyXG4gICAgICBmaW5kaW5nczogdGhpcy5maW5kaW5ncyxcclxuICAgICAgcmVjb21tZW5kZWRBY3Rpb25zOiB0aGlzLnJlY29tbWVuZGVkQWN0aW9ucyxcclxuICAgICAgYnVzaW5lc3NJbXBhY3Q6IHRoaXMuYnVzaW5lc3NJbXBhY3QsXHJcbiAgICAgIHRlY2huaWNhbEltcGFjdDogdGhpcy50ZWNobmljYWxJbXBhY3QsXHJcbiAgICAgIGV4cGxvaXRhYmlsaXR5QXNzZXNzbWVudDogdGhpcy5leHBsb2l0YWJpbGl0eUFzc2Vzc21lbnQsXHJcbiAgICAgIG1pdGlnYXRpb25TdHJhdGVnaWVzOiB0aGlzLm1pdGlnYXRpb25TdHJhdGVnaWVzLFxyXG4gICAgICBjb21wZW5zYXRpbmdDb250cm9sczogdGhpcy5jb21wZW5zYXRpbmdDb250cm9scyxcclxuICAgICAgbWV0aG9kb2xvZ3k6IHRoaXMubWV0aG9kb2xvZ3ksXHJcbiAgICAgIHRvb2xzVXNlZDogdGhpcy50b29sc1VzZWQsXHJcbiAgICAgIGFzc2Vzc21lbnRDcml0ZXJpYTogdGhpcy5hc3Nlc3NtZW50Q3JpdGVyaWEsXHJcbiAgICAgIHJldmlld0NvbW1lbnRzOiB0aGlzLnJldmlld0NvbW1lbnRzLFxyXG4gICAgICBtZXRhZGF0YTogdGhpcy5tZXRhZGF0YSxcclxuICAgICAgZXhwb3J0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgfTtcclxuICB9XHJcbn1cclxuIl0sInZlcnNpb24iOjN9