36ea142392acd57e18cf1bf175b503a7
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const threat_factory_1 = require("../threat.factory");
const threat_severity_enum_1 = require("../../enums/threat-severity.enum");
const ioc_value_object_1 = require("../../value-objects/threat-indicators/ioc.value-object");
const cvss_score_value_object_1 = require("../../value-objects/threat-indicators/cvss-score.value-object");
const threat_detected_event_1 = require("../../events/threat-detected.event");
describe('ThreatFactory', () => {
    describe('create', () => {
        it('should create a basic threat with required fields', () => {
            const options = {
                name: 'Test Threat',
                description: 'Test threat description',
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                category: 'malware',
                type: 'trojan',
                confidence: 85,
            };
            const threat = threat_factory_1.ThreatFactory.create(options);
            expect(threat.name).toBe('Test Threat');
            expect(threat.description).toBe('Test threat description');
            expect(threat.severity).toBe(threat_severity_enum_1.ThreatSeverity.HIGH);
            expect(threat.category).toBe('malware');
            expect(threat.type).toBe('trojan');
            expect(threat.confidence).toBe(85);
            expect(threat.isActive()).toBe(true);
            expect(threat.riskAssessment.riskScore).toBeGreaterThan(0);
        });
        it('should create threat with optional fields', () => {
            const ioc = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********00', 'high', 'confirmed');
            const cvssScore = cvss_score_value_object_1.CVSSScore.createV3_1(8.5, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            const options = {
                name: 'Advanced Threat',
                description: 'Advanced persistent threat',
                severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
                category: 'apt',
                subcategory: 'nation-state',
                type: 'advanced-persistent-threat',
                confidence: 95,
                indicators: [ioc],
                cvssScores: [cvssScore],
                attribution: {
                    actor: 'APT29',
                    confidence: 90,
                    aliases: ['Cozy Bear'],
                    motivation: ['espionage'],
                    capabilities: ['advanced_evasion'],
                    campaigns: ['Operation Ghost'],
                },
                techniques: [{
                        id: 'T1055',
                        name: 'Process Injection',
                        tactic: 'Defense Evasion',
                        description: 'Adversaries may inject code into processes',
                        confidence: 85,
                        evidence: ['process_hollowing_detected'],
                    }],
                affectedAssets: ['server-001', 'workstation-042'],
                tags: ['apt', 'critical', 'nation-state'],
                attributes: { campaign: 'Operation Ghost' },
            };
            const threat = threat_factory_1.ThreatFactory.create(options);
            expect(threat.subcategory).toBe('nation-state');
            expect(threat.indicators).toHaveLength(1);
            expect(threat.cvssScores).toHaveLength(1);
            expect(threat.attribution?.actor).toBe('APT29');
            expect(threat.techniques).toHaveLength(1);
            expect(threat.affectedAssets).toHaveLength(2);
            expect(threat.tags).toContain('apt');
            expect(threat.attributes.campaign).toBe('Operation Ghost');
        });
        it('should publish ThreatDetectedEvent on creation', () => {
            const options = {
                name: 'Test Threat',
                description: 'Test description',
                severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                category: 'test',
                type: 'test_type',
                confidence: 70,
            };
            const threat = threat_factory_1.ThreatFactory.create(options);
            const domainEvents = threat.domainEvents;
            expect(domainEvents).toHaveLength(1);
            expect(domainEvents[0]).toBeInstanceOf(threat_detected_event_1.ThreatDetectedEvent);
            const detectedEvent = domainEvents[0];
            expect(detectedEvent.threatName).toBe('Test Threat');
            expect(detectedEvent.severity).toBe(threat_severity_enum_1.ThreatSeverity.MEDIUM);
        });
    });
    describe('fromThreatIntelligence', () => {
        it('should create threat from threat intelligence data', () => {
            const threatData = {
                indicator: '***********00',
                indicatorType: 'ip',
                severity: 'high',
                description: 'Malicious IP address',
                confidence: 85,
                source: 'threat-intel-feed',
                attribution: {
                    actor: 'APT28',
                    campaign: 'Operation Stealth',
                    malwareFamily: 'Sofacy',
                },
                metadata: { feed_id: '12345' },
            };
            const threat = threat_factory_1.ThreatFactory.fromThreatIntelligence(threatData);
            expect(threat.name).toBe('Threat Intelligence: ***********00');
            expect(threat.description).toBe('Malicious IP address');
            expect(threat.severity).toBe(threat_severity_enum_1.ThreatSeverity.HIGH);
            expect(threat.category).toBe('threat-intelligence');
            expect(threat.type).toBe('ip');
            expect(threat.confidence).toBe(85);
            expect(threat.indicators).toHaveLength(1);
            expect(threat.indicators[0].value).toBe('***********00');
            expect(threat.attribution?.actor).toBe('APT28');
            expect(threat.malwareFamily?.name).toBe('Sofacy');
            expect(threat.tags).toContain('threat-intelligence');
            expect(threat.attributes.source).toBe('threat-intel-feed');
        });
        it('should infer confidence when not provided', () => {
            const threatData = {
                indicator: 'malicious.example.com',
                indicatorType: 'domain',
                severity: 'medium',
                description: 'Command and control domain',
                source: 'internal-analysis',
                attribution: {
                    actor: 'Unknown Actor',
                    campaign: 'Campaign X',
                },
            };
            const threat = threat_factory_1.ThreatFactory.fromThreatIntelligence(threatData);
            expect(threat.confidence).toBeGreaterThan(50);
            expect(threat.confidence).toBeLessThanOrEqual(100);
        });
        it('should handle minimal threat intelligence data', () => {
            const threatData = {
                indicator: 'suspicious-file.exe',
                indicatorType: 'filename',
                severity: 'low',
            };
            const threat = threat_factory_1.ThreatFactory.fromThreatIntelligence(threatData);
            expect(threat.name).toBe('Threat Intelligence: suspicious-file.exe');
            expect(threat.severity).toBe(threat_severity_enum_1.ThreatSeverity.LOW);
            expect(threat.indicators).toHaveLength(1);
            expect(threat.attribution).toBeUndefined();
            expect(threat.malwareFamily).toBeUndefined();
        });
    });
    describe('fromMalwareAnalysis', () => {
        it('should create threat from malware analysis data', () => {
            const malwareData = {
                hash: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456',
                hashType: 'SHA256',
                family: 'Emotet',
                variant: 'v4',
                confidence: 90,
                capabilities: ['credential_theft', 'lateral_movement'],
                protocols: ['HTTP', 'HTTPS'],
                persistence: ['registry_modification', 'scheduled_task'],
                affectedFiles: ['/tmp/malware.exe', '/var/log/system.log'],
                networkIndicators: ['malicious.example.com', '192.168.1.200'],
            };
            const threat = threat_factory_1.ThreatFactory.fromMalwareAnalysis(malwareData);
            expect(threat.name).toBe('Malware: Emotet');
            expect(threat.category).toBe('malware');
            expect(threat.type).toBe('Emotet');
            expect(threat.confidence).toBe(90);
            expect(threat.indicators.length).toBeGreaterThan(1); // Hash + network indicators
            expect(threat.malwareFamily?.name).toBe('Emotet');
            expect(threat.malwareFamily?.variant).toBe('v4');
            expect(threat.affectedAssets).toEqual(['/tmp/malware.exe', '/var/log/system.log']);
            expect(threat.tags).toContain('malware');
            expect(threat.attributes.hash).toBe(malwareData.hash);
        });
        it('should handle malware data without family', () => {
            const malwareData = {
                hash: 'abcdef1234567890',
                hashType: 'MD5',
                confidence: 75,
                capabilities: ['file_encryption'],
                protocols: [],
                persistence: [],
            };
            const threat = threat_factory_1.ThreatFactory.fromMalwareAnalysis(malwareData);
            expect(threat.name).toBe('Malware: abcdef12');
            expect(threat.type).toBe('unknown-malware');
            expect(threat.malwareFamily).toBeUndefined();
            expect(threat.indicators).toHaveLength(1);
        });
        it('should infer severity based on capabilities', () => {
            const highCapabilityMalware = {
                hash: 'test123',
                hashType: 'SHA1',
                confidence: 80,
                capabilities: ['data_exfiltration', 'credential_theft', 'lateral_movement', 'persistence', 'evasion'],
                protocols: ['HTTPS', 'DNS'],
                persistence: ['registry', 'service'],
                networkIndicators: ['c2.example.com'],
            };
            const threat = threat_factory_1.ThreatFactory.fromMalwareAnalysis(highCapabilityMalware);
            expect(threat.severity).toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
        });
    });
    describe('fromAttackPattern', () => {
        it('should create threat from attack pattern data', () => {
            const patternData = {
                name: 'Credential Dumping',
                techniqueId: 'T1003',
                tactic: 'Credential Access',
                description: 'Adversaries may attempt to dump credentials',
                confidence: 85,
                evidence: ['lsass_access_detected', 'mimikatz_signature'],
                affectedAssets: ['DC-01', 'WS-042'],
            };
            const threat = threat_factory_1.ThreatFactory.fromAttackPattern(patternData);
            expect(threat.name).toBe('Attack Pattern: Credential Dumping');
            expect(threat.description).toBe('Adversaries may attempt to dump credentials');
            expect(threat.category).toBe('attack-pattern');
            expect(threat.type).toBe('credential-access');
            expect(threat.confidence).toBe(85);
            expect(threat.techniques).toHaveLength(1);
            expect(threat.techniques[0].id).toBe('T1003');
            expect(threat.affectedAssets).toEqual(['DC-01', 'WS-042']);
            expect(threat.tags).toContain('attack-pattern');
            expect(threat.attributes.techniqueId).toBe('T1003');
        });
        it('should infer severity from tactic', () => {
            const impactPattern = {
                name: 'Data Destruction',
                tactic: 'Impact',
                description: 'Adversaries may destroy data',
                confidence: 90,
                evidence: ['file_deletion_detected'],
            };
            const threat = threat_factory_1.ThreatFactory.fromAttackPattern(impactPattern);
            expect(threat.severity).toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
            const reconPattern = {
                name: 'Network Discovery',
                tactic: 'Discovery',
                description: 'Adversaries may discover network resources',
                confidence: 70,
                evidence: ['network_scan_detected'],
            };
            const reconThreat = threat_factory_1.ThreatFactory.fromAttackPattern(reconPattern);
            expect(reconThreat.severity).toBe(threat_severity_enum_1.ThreatSeverity.LOW);
        });
    });
    describe('createHighSeverityAlert', () => {
        it('should create high severity alert with defaults', () => {
            const threat = threat_factory_1.ThreatFactory.createHighSeverityAlert('Security Alert', 'High severity security incident', 'security-alert', 'incident');
            expect(threat.name).toBe('Security Alert');
            expect(threat.severity).toBe(threat_severity_enum_1.ThreatSeverity.HIGH);
            expect(threat.confidence).toBe(85);
            expect(threat.tags).toContain('high-severity');
            expect(threat.tags).toContain('alert');
        });
        it('should allow overriding defaults', () => {
            const threat = threat_factory_1.ThreatFactory.createHighSeverityAlert('Custom Alert', 'Custom description', 'custom', 'custom-type', {
                confidence: 95,
                tags: ['custom-tag'],
                attributes: { priority: 'urgent' },
            });
            expect(threat.confidence).toBe(95);
            expect(threat.tags).toContain('custom-tag');
            expect(threat.attributes.priority).toBe('urgent');
        });
    });
    describe('createCriticalAlert', () => {
        it('should create critical alert with appropriate defaults', () => {
            const threat = threat_factory_1.ThreatFactory.createCriticalAlert('Critical Security Incident', 'Critical threat requiring immediate response', 'incident', 'critical-incident');
            expect(threat.severity).toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
            expect(threat.confidence).toBe(95);
            expect(threat.tags).toContain('critical');
            expect(threat.tags).toContain('alert');
            expect(threat.tags).toContain('immediate-response');
            expect(threat.requiresImmediateAttention()).toBe(true);
        });
    });
    describe('fromEventCorrelation', () => {
        it('should create threat from correlated events', () => {
            const mockEvents = [
                { id: '1', severity: 'high' },
                { id: '2', severity: 'medium' },
                { id: '3', severity: 'high' },
            ];
            const threat = threat_factory_1.ThreatFactory.fromEventCorrelation(mockEvents, 'Multi-stage Attack', 'Correlated attack involving multiple events');
            expect(threat.name).toBe('Multi-stage Attack');
            expect(threat.category).toBe('correlated-threat');
            expect(threat.type).toBe('multi-stage-attack');
            expect(threat.tags).toContain('correlated');
            expect(threat.tags).toContain('multi-stage');
            expect(threat.techniques).toHaveLength(1);
            expect(threat.attributes.correlatedEventCount).toBe(3);
        });
        it('should infer severity from event count', () => {
            const manyEvents = Array.from({ length: 12 }, (_, i) => ({ id: i.toString() }));
            const threat = threat_factory_1.ThreatFactory.fromEventCorrelation(manyEvents, 'Large Scale Attack', 'Attack with many correlated events');
            expect(threat.severity).toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
        });
    });
    describe('createManualThreat', () => {
        it('should create manual threat with analyst information', () => {
            const threat = threat_factory_1.ThreatFactory.createManualThreat('Manual Analysis Result', 'Threat identified through manual analysis', threat_severity_enum_1.ThreatSeverity.MEDIUM, 'manual-analysis', 'analyst-finding', 80, '<EMAIL>');
            expect(threat.name).toBe('Manual Analysis Result');
            expect(threat.severity).toBe(threat_severity_enum_1.ThreatSeverity.MEDIUM);
            expect(threat.confidence).toBe(80);
            expect(threat.tags).toContain('manual');
            expect(threat.tags).toContain('analyst-created');
            expect(threat.attributes.createdBy).toBe('<EMAIL>');
            expect(threat.attributes.source).toBe('manual-analysis');
        });
    });
    describe('helper methods', () => {
        it('should map threat severity correctly', () => {
            // Test string mapping
            const criticalThreat = threat_factory_1.ThreatFactory.fromThreatIntelligence({
                indicator: 'test',
                indicatorType: 'ip',
                severity: 'critical',
            });
            expect(criticalThreat.severity).toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
            // Test numeric mapping
            const highThreat = threat_factory_1.ThreatFactory.fromThreatIntelligence({
                indicator: 'test',
                indicatorType: 'ip',
                severity: 8,
            });
            expect(highThreat.severity).toBe(threat_severity_enum_1.ThreatSeverity.HIGH);
            // Test unknown mapping
            const unknownThreat = threat_factory_1.ThreatFactory.fromThreatIntelligence({
                indicator: 'test',
                indicatorType: 'ip',
                severity: 'invalid',
            });
            expect(unknownThreat.severity).toBe(threat_severity_enum_1.ThreatSeverity.UNKNOWN);
        });
        it('should infer IOC types correctly', () => {
            const ipThreat = threat_factory_1.ThreatFactory.fromThreatIntelligence({
                indicator: '***********',
                indicatorType: 'ip_address',
                severity: 'medium',
            });
            expect(ipThreat.indicators[0].type).toBe(ioc_value_object_1.IOCType.IP_ADDRESS);
            const domainThreat = threat_factory_1.ThreatFactory.fromThreatIntelligence({
                indicator: 'malicious.example.com',
                indicatorType: 'domain',
                severity: 'medium',
            });
            expect(domainThreat.indicators[0].type).toBe(ioc_value_object_1.IOCType.DOMAIN);
            const hashThreat = threat_factory_1.ThreatFactory.fromThreatIntelligence({
                indicator: 'abcdef1234567890abcdef1234567890',
                indicatorType: 'md5',
                severity: 'medium',
            });
            expect(hashThreat.indicators[0].type).toBe(ioc_value_object_1.IOCType.MD5_HASH);
        });
        it('should calculate confidence correctly', () => {
            const highQualityData = {
                indicator: 'test.com',
                indicatorType: 'domain',
                severity: 'high',
                source: 'premium-feed',
                description: 'Detailed analysis of this malicious domain with comprehensive attribution data',
                attribution: {
                    actor: 'APT29',
                    campaign: 'Operation Stealth',
                },
            };
            const threat = threat_factory_1.ThreatFactory.fromThreatIntelligence(highQualityData);
            expect(threat.confidence).toBeGreaterThan(80);
            const lowQualityData = {
                indicator: 'test2.com',
                indicatorType: 'domain',
                severity: 'low',
            };
            const lowQualityThreat = threat_factory_1.ThreatFactory.fromThreatIntelligence(lowQualityData);
            expect(lowQualityThreat.confidence).toBeLessThan(70);
        });
    });
    describe('error handling', () => {
        it('should handle invalid data gracefully', () => {
            expect(() => {
                threat_factory_1.ThreatFactory.create({
                    name: '',
                    description: 'Test',
                    severity: threat_severity_enum_1.ThreatSeverity.LOW,
                    category: 'test',
                    type: 'test',
                    confidence: 50,
                });
            }).toThrow('Threat must have a name');
            expect(() => {
                threat_factory_1.ThreatFactory.create({
                    name: 'Test',
                    description: '',
                    severity: threat_severity_enum_1.ThreatSeverity.LOW,
                    category: 'test',
                    type: 'test',
                    confidence: 50,
                });
            }).toThrow('Threat must have a description');
            expect(() => {
                threat_factory_1.ThreatFactory.create({
                    name: 'Test',
                    description: 'Test',
                    severity: threat_severity_enum_1.ThreatSeverity.LOW,
                    category: 'test',
                    type: 'test',
                    confidence: 150,
                });
            }).toThrow('Threat confidence must be between 0 and 100');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************