{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\app.controller.ts", "mappings": ";;;;;;;;;;;;;AAAA,2CAAiD;AACjD,6CAAqE;AACrE,+CAA2C;AAE3C;;;GAGG;AAGI,IAAM,aAAa,GAAnB,MAAM,aAAa;IACxB,YAA6B,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAEvD;;OAEG;IAqBH,kBAAkB;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IA4BH,SAAS;QACP,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IAoBH,UAAU;QACR,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;IACtC,CAAC;CACF,CAAA;AAzFY,sCAAa;AA0BxB;IApBC,IAAA,YAAG,GAAE;IACL,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,6BAA6B;QACtC,WAAW,EAAE,uDAAuD;KACrE,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,gDAAgD;QAC7D,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,4CAA4C,EAAE;gBAC/E,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE;gBAC7C,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAC/B,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,EAAE;gBACvD,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE;gBAClD,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,+BAA+B,EAAE;aACzE;SACF;KACF,CAAC;;;;uDAGD;AAgCD;IA3BC,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,wBAAwB;QACjC,WAAW,EAAE,sDAAsD;KACpE,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,2CAA2C;QACxD,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE;gBAC9C,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE;gBAClD,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAC1B,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACxB,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACzB,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBAC/B;iBACF;gBACD,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAC3B,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAChC;SACF;KACF,CAAC;;;;8CAGD;AAwBD;IAnBC,IAAA,YAAG,EAAC,SAAS,CAAC;IACd,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,yBAAyB;QAClC,WAAW,EAAE,gDAAgD;KAC9D,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,4CAA4C;QACzD,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE;gBAC7C,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE;gBAClD,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAC7B,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAC/B,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC7B;SACF;KACF,CAAC;;;;+CAGD;wBAxFU,aAAa;IAFzB,IAAA,iBAAO,EAAC,aAAa,CAAC;IACtB,IAAA,mBAAU,GAAE;yDAE8B,wBAAU,oBAAV,wBAAU;GADxC,aAAa,CAyFzB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\app.controller.ts"], "sourcesContent": ["import { Controller, Get } from '@nestjs/common';\r\nimport { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';\r\nimport { AppService } from './app.service';\r\n\r\n/**\r\n * Root application controller\r\n * Provides basic application information and status endpoints\r\n */\r\n@ApiTags('Application')\r\n@Controller()\r\nexport class AppController {\r\n  constructor(private readonly appService: AppService) {}\r\n\r\n  /**\r\n   * Root endpoint - returns application information\r\n   */\r\n  @Get()\r\n  @ApiOperation({ \r\n    summary: 'Get application information',\r\n    description: 'Returns basic information about the Sentinel platform'\r\n  })\r\n  @ApiResponse({ \r\n    status: 200, \r\n    description: 'Application information retrieved successfully',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        name: { type: 'string', example: 'Sentinel Vulnerability Assessment Platform' },\r\n        version: { type: 'string', example: '1.0.0' },\r\n        description: { type: 'string' },\r\n        environment: { type: 'string', example: 'development' },\r\n        timestamp: { type: 'string', format: 'date-time' },\r\n        uptime: { type: 'number', description: 'Application uptime in seconds' }\r\n      }\r\n    }\r\n  })\r\n  getApplicationInfo() {\r\n    return this.appService.getApplicationInfo();\r\n  }\r\n\r\n  /**\r\n   * Status endpoint - returns application status\r\n   */\r\n  @Get('status')\r\n  @ApiOperation({ \r\n    summary: 'Get application status',\r\n    description: 'Returns current application status and basic metrics'\r\n  })\r\n  @ApiResponse({ \r\n    status: 200, \r\n    description: 'Application status retrieved successfully',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        status: { type: 'string', example: 'healthy' },\r\n        timestamp: { type: 'string', format: 'date-time' },\r\n        uptime: { type: 'number' },\r\n        memory: { \r\n          type: 'object',\r\n          properties: {\r\n            used: { type: 'number' },\r\n            total: { type: 'number' },\r\n            percentage: { type: 'number' }\r\n          }\r\n        },\r\n        version: { type: 'string' },\r\n        environment: { type: 'string' }\r\n      }\r\n    }\r\n  })\r\n  getStatus() {\r\n    return this.appService.getStatus();\r\n  }\r\n\r\n  /**\r\n   * Version endpoint - returns application version information\r\n   */\r\n  @Get('version')\r\n  @ApiOperation({ \r\n    summary: 'Get version information',\r\n    description: 'Returns detailed version and build information'\r\n  })\r\n  @ApiResponse({ \r\n    status: 200, \r\n    description: 'Version information retrieved successfully',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        version: { type: 'string', example: '1.0.0' },\r\n        buildDate: { type: 'string', format: 'date-time' },\r\n        gitCommit: { type: 'string' },\r\n        nodeVersion: { type: 'string' },\r\n        platform: { type: 'string' }\r\n      }\r\n    }\r\n  })\r\n  getVersion() {\r\n    return this.appService.getVersion();\r\n  }\r\n}\r\n"], "version": 3}