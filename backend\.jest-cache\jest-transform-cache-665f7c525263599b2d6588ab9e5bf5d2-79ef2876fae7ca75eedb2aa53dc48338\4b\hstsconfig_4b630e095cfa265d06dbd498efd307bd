86f30036daf465f1b42c840a3ddb0dbc
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.HstsConfig = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
/**
 * HTTP Strict Transport Security (HSTS) configuration service
 * Implements HSTS policy for enforcing HTTPS connections
 */
let HstsConfig = class HstsConfig {
    constructor(configService) {
        this.configService = configService;
    }
    /**
     * Generate HSTS header value based on environment
     */
    generateHSTSHeader() {
        const environment = this.configService.get('NODE_ENV', 'development');
        switch (environment) {
            case 'production':
                return this.getProductionHSTS();
            case 'staging':
                return this.getStagingHSTS();
            case 'test':
                return this.getTestHSTS();
            default:
                return this.getDevelopmentHSTS();
        }
    }
    /**
     * Production HSTS - Maximum security with preload
     */
    getProductionHSTS() {
        const maxAge = this.configService.get('security.hsts.maxAge', 31536000); // 1 year
        const includeSubDomains = this.configService.get('security.hsts.includeSubDomains', true);
        const preload = this.configService.get('security.hsts.preload', true);
        let hstsValue = `max-age=${maxAge}`;
        if (includeSubDomains) {
            hstsValue += '; includeSubDomains';
        }
        if (preload) {
            hstsValue += '; preload';
        }
        return hstsValue;
    }
    /**
     * Staging HSTS - Moderate security without preload
     */
    getStagingHSTS() {
        const maxAge = this.configService.get('security.hsts.maxAge', 7776000); // 90 days
        const includeSubDomains = this.configService.get('security.hsts.includeSubDomains', true);
        let hstsValue = `max-age=${maxAge}`;
        if (includeSubDomains) {
            hstsValue += '; includeSubDomains';
        }
        return hstsValue;
    }
    /**
     * Development HSTS - Short duration for development
     */
    getDevelopmentHSTS() {
        const maxAge = this.configService.get('security.hsts.maxAge', 3600); // 1 hour
        return `max-age=${maxAge}`;
    }
    /**
     * Test HSTS - Minimal duration for testing
     */
    getTestHSTS() {
        return 'max-age=0'; // Disable HSTS in tests
    }
    /**
     * Check if HSTS should be enabled for the current environment
     */
    isHSTSEnabled() {
        const environment = this.configService.get('NODE_ENV', 'development');
        const forceDisable = this.configService.get('security.hsts.disabled', false);
        if (forceDisable) {
            return false;
        }
        // HSTS should be enabled in production and staging
        return ['production', 'staging'].includes(environment);
    }
    /**
     * Get HSTS configuration for specific domain types
     */
    getDomainSpecificHSTS(domainType) {
        const baseHSTS = this.generateHSTSHeader();
        switch (domainType) {
            case 'api':
                // API domains should have strict HSTS
                return this.getAPIHSTS();
            case 'admin':
                // Admin domains should have maximum security
                return this.getAdminHSTS();
            case 'public':
                // Public domains can have standard HSTS
                return baseHSTS;
            default:
                return baseHSTS;
        }
    }
    /**
     * API-specific HSTS configuration
     */
    getAPIHSTS() {
        const environment = this.configService.get('NODE_ENV', 'development');
        if (environment === 'production') {
            return 'max-age=63072000; includeSubDomains; preload'; // 2 years
        }
        else if (environment === 'staging') {
            return 'max-age=15552000; includeSubDomains'; // 180 days
        }
        else {
            return 'max-age=3600'; // 1 hour for development
        }
    }
    /**
     * Admin-specific HSTS configuration
     */
    getAdminHSTS() {
        const environment = this.configService.get('NODE_ENV', 'development');
        if (environment === 'production') {
            return 'max-age=63072000; includeSubDomains; preload'; // 2 years
        }
        else if (environment === 'staging') {
            return 'max-age=31536000; includeSubDomains'; // 1 year
        }
        else {
            return 'max-age=7200'; // 2 hours for development
        }
    }
    /**
     * Validate HSTS configuration
     */
    validateHSTSConfig() {
        try {
            const hstsHeader = this.generateHSTSHeader();
            const environment = this.configService.get('NODE_ENV', 'development');
            // Basic validation
            if (!hstsHeader || hstsHeader.trim().length === 0) {
                throw new Error('HSTS header is empty');
            }
            // Extract max-age value
            const maxAgeMatch = hstsHeader.match(/max-age=(\d+)/);
            if (!maxAgeMatch) {
                throw new Error('HSTS header missing max-age directive');
            }
            const maxAge = parseInt(maxAgeMatch[1], 10);
            // Validate max-age values
            if (environment === 'production' && maxAge < 31536000) {
                console.warn('HSTS: Production max-age should be at least 1 year (31536000 seconds)');
            }
            if (maxAge < 0) {
                throw new Error('HSTS max-age cannot be negative');
            }
            // Check for preload in production
            if (environment === 'production' && !hstsHeader.includes('preload')) {
                console.warn('HSTS: Consider enabling preload for production domains');
            }
            return true;
        }
        catch (error) {
            console.error('HSTS configuration validation failed:', error.message);
            return false;
        }
    }
    /**
     * Get HSTS preload eligibility status
     */
    getPreloadEligibility() {
        const hstsHeader = this.generateHSTSHeader();
        const environment = this.configService.get('NODE_ENV', 'development');
        const requirements = [
            'Serve a valid certificate',
            'Redirect all HTTP traffic to HTTPS',
            'Serve all subdomains over HTTPS',
            'Serve HSTS header with max-age >= 31536000 (1 year)',
            'Include includeSubDomains directive',
            'Include preload directive',
        ];
        const issues = [];
        let eligible = true;
        // Check max-age
        const maxAgeMatch = hstsHeader.match(/max-age=(\d+)/);
        if (!maxAgeMatch || parseInt(maxAgeMatch[1], 10) < 31536000) {
            issues.push('max-age must be at least 31536000 seconds (1 year)');
            eligible = false;
        }
        // Check includeSubDomains
        if (!hstsHeader.includes('includeSubDomains')) {
            issues.push('includeSubDomains directive is required');
            eligible = false;
        }
        // Check preload
        if (!hstsHeader.includes('preload')) {
            issues.push('preload directive is required');
            eligible = false;
        }
        // Environment check
        if (environment !== 'production') {
            issues.push('Preload is only recommended for production environments');
            eligible = false;
        }
        return {
            eligible,
            requirements,
            issues,
        };
    }
    /**
     * Get HSTS configuration summary for logging
     */
    getHSTSConfigSummary() {
        const hstsHeader = this.generateHSTSHeader();
        const environment = this.configService.get('NODE_ENV', 'development');
        const maxAgeMatch = hstsHeader.match(/max-age=(\d+)/);
        return {
            environment,
            enabled: this.isHSTSEnabled(),
            maxAge: maxAgeMatch ? parseInt(maxAgeMatch[1], 10) : 0,
            maxAgeDays: maxAgeMatch ? Math.floor(parseInt(maxAgeMatch[1], 10) / 86400) : 0,
            includeSubDomains: hstsHeader.includes('includeSubDomains'),
            preload: hstsHeader.includes('preload'),
            headerLength: hstsHeader.length,
            preloadEligible: this.getPreloadEligibility().eligible,
        };
    }
    /**
     * Generate HSTS removal header (for emergency use)
     */
    generateRemovalHeader() {
        return 'max-age=0';
    }
    /**
     * Check if domain is in HSTS preload list (simulation)
     */
    isInPreloadList(domain) {
        // In a real implementation, this would check against the actual preload list
        // For now, we'll simulate based on configuration
        const preloadDomains = this.configService.get('security.hsts.preloadDomains', []);
        return preloadDomains.includes(domain);
    }
};
exports.HstsConfig = HstsConfig;
exports.HstsConfig = HstsConfig = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], HstsConfig);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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