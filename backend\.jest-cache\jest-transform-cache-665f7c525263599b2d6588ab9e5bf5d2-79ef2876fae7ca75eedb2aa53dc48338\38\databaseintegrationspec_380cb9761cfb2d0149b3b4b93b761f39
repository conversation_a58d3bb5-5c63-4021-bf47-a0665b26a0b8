adbb89ac410eb7d88c1d36fecea92dfd
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const report_definition_entity_1 = require("../../entities/report-definition.entity");
const report_definition_service_1 = require("../../services/report-definition.service");
const cache_service_1 = require("../../../infrastructure/cache/cache.service");
const metrics_service_1 = require("../../../infrastructure/metrics/metrics.service");
/**
 * Database Integration Tests
 *
 * Tests database operations including:
 * - Entity persistence and retrieval
 * - Transaction management and rollback
 * - Data consistency and integrity
 * - Relationship handling and cascading
 * - Query performance and optimization
 */
describe('Database Integration Tests', () => {
    let module;
    let dataSource;
    let repository;
    let service;
    let cacheService;
    let metricsService;
    beforeAll(async () => {
        const mockCacheService = {
            get: jest.fn(),
            set: jest.fn(),
            del: jest.fn(),
            delPattern: jest.fn(),
        };
        const mockMetricsService = {
            recordCounter: jest.fn(),
            recordHistogram: jest.fn(),
            recordGauge: jest.fn(),
        };
        module = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                typeorm_1.TypeOrmModule.forRoot({
                    type: 'postgres',
                    host: process.env.TEST_DB_HOST || 'localhost',
                    port: parseInt(process.env.TEST_DB_PORT || '5433'),
                    username: process.env.TEST_DB_USERNAME || 'test',
                    password: process.env.TEST_DB_PASSWORD || 'test',
                    database: process.env.TEST_DB_NAME || 'sentinel_test',
                    entities: [report_definition_entity_1.ReportDefinition],
                    synchronize: true,
                    dropSchema: true,
                    logging: false,
                }),
                typeorm_1.TypeOrmModule.forFeature([report_definition_entity_1.ReportDefinition]),
            ],
            providers: [
                report_definition_service_1.ReportDefinitionService,
                {
                    provide: cache_service_1.CacheService,
                    useValue: mockCacheService,
                },
                {
                    provide: metrics_service_1.MetricsService,
                    useValue: mockMetricsService,
                },
            ],
        }).compile();
        dataSource = module.get(typeorm_2.DataSource);
        repository = module.get((0, typeorm_1.getRepositoryToken)(report_definition_entity_1.ReportDefinition));
        service = module.get(report_definition_service_1.ReportDefinitionService);
        cacheService = module.get(cache_service_1.CacheService);
        metricsService = module.get(metrics_service_1.MetricsService);
    });
    afterAll(async () => {
        await dataSource.destroy();
        await module.close();
    });
    beforeEach(async () => {
        // Clean up database before each test
        await repository.clear();
        jest.clearAllMocks();
    });
    describe('Entity Persistence', () => {
        it('should create and persist report definition', async () => {
            const reportData = {
                name: 'Integration Test Report',
                description: 'Test report for integration testing',
                category: 'compliance',
                type: 'tabular',
                dataSource: 'compliance',
                query: 'SELECT * FROM compliance_data WHERE date >= :startDate',
                parameters: [
                    {
                        name: 'startDate',
                        type: 'date',
                        required: true,
                        defaultValue: null,
                    },
                ],
                outputFormats: ['pdf', 'excel'],
                schedule: {
                    enabled: true,
                    cronExpression: '0 9 * * 1',
                    timezone: 'UTC',
                },
            };
            const createdReport = await service.createReportDefinition(reportData, 'test-user');
            expect(createdReport).toBeDefined();
            expect(createdReport.id).toBeDefined();
            expect(createdReport.name).toBe(reportData.name);
            expect(createdReport.createdBy).toBe('test-user');
            expect(createdReport.isActive).toBe(true);
            // Verify persistence
            const retrievedReport = await repository.findOneBy({ id: createdReport.id });
            expect(retrievedReport).toBeDefined();
            expect(retrievedReport.name).toBe(reportData.name);
        });
        it('should handle complex parameter structures', async () => {
            const reportData = {
                name: 'Complex Parameters Report',
                description: 'Report with complex parameter structures',
                category: 'audit',
                type: 'chart',
                dataSource: 'audit',
                query: 'SELECT * FROM audit_events WHERE risk_level = :riskLevel AND date BETWEEN :startDate AND :endDate',
                parameters: [
                    {
                        name: 'riskLevel',
                        type: 'select',
                        required: true,
                        options: ['low', 'medium', 'high', 'critical'],
                        defaultValue: 'medium',
                    },
                    {
                        name: 'startDate',
                        type: 'date',
                        required: true,
                        defaultValue: null,
                    },
                    {
                        name: 'endDate',
                        type: 'date',
                        required: true,
                        defaultValue: null,
                    },
                ],
                outputFormats: ['pdf', 'csv'],
            };
            const createdReport = await service.createReportDefinition(reportData, 'test-user');
            expect(createdReport.parameters).toHaveLength(3);
            expect(createdReport.parameters[0].options).toEqual(['low', 'medium', 'high', 'critical']);
            // Verify JSON serialization/deserialization
            const retrievedReport = await repository.findOneBy({ id: createdReport.id });
            expect(retrievedReport.parameters[0].options).toEqual(['low', 'medium', 'high', 'critical']);
        });
    });
    describe('Transaction Management', () => {
        it('should rollback transaction on error', async () => {
            const reportData = {
                name: 'Transaction Test Report',
                description: 'Test report for transaction testing',
                category: 'compliance',
                type: 'tabular',
                dataSource: 'compliance',
                query: 'SELECT * FROM compliance_data',
                parameters: [],
                outputFormats: ['pdf'],
            };
            // Create initial report
            const createdReport = await service.createReportDefinition(reportData, 'test-user');
            expect(createdReport).toBeDefined();
            // Attempt to create duplicate (should fail)
            await expect(service.createReportDefinition(reportData, 'test-user'))
                .rejects.toThrow('Report definition with this name already exists');
            // Verify original report still exists
            const existingReport = await repository.findOneBy({ id: createdReport.id });
            expect(existingReport).toBeDefined();
            // Verify only one report exists
            const allReports = await repository.find();
            expect(allReports).toHaveLength(1);
        });
        it('should handle concurrent updates correctly', async () => {
            const reportData = {
                name: 'Concurrent Update Test',
                description: 'Test report for concurrent updates',
                category: 'compliance',
                type: 'tabular',
                dataSource: 'compliance',
                query: 'SELECT * FROM compliance_data',
                parameters: [],
                outputFormats: ['pdf'],
            };
            const createdReport = await service.createReportDefinition(reportData, 'test-user');
            // Simulate concurrent updates
            const update1Promise = service.updateReportDefinition(createdReport.id, { description: 'Updated by user 1' }, 'user-1');
            const update2Promise = service.updateReportDefinition(createdReport.id, { description: 'Updated by user 2' }, 'user-2');
            // Both updates should succeed (last one wins)
            const [result1, result2] = await Promise.all([update1Promise, update2Promise]);
            expect(result1).toBeDefined();
            expect(result2).toBeDefined();
            // Verify final state
            const finalReport = await repository.findOneBy({ id: createdReport.id });
            expect(finalReport.description).toMatch(/Updated by user [12]/);
        });
    });
    describe('Data Consistency', () => {
        it('should maintain referential integrity', async () => {
            const reportData = {
                name: 'Referential Integrity Test',
                description: 'Test report for referential integrity',
                category: 'compliance',
                type: 'tabular',
                dataSource: 'compliance',
                query: 'SELECT * FROM compliance_data',
                parameters: [],
                outputFormats: ['pdf'],
            };
            const createdReport = await service.createReportDefinition(reportData, 'test-user');
            // Verify all required fields are set
            expect(createdReport.createdAt).toBeDefined();
            expect(createdReport.updatedAt).toBeDefined();
            expect(createdReport.createdBy).toBe('test-user');
            expect(createdReport.updatedBy).toBe('test-user');
            expect(createdReport.isActive).toBe(true);
            // Update and verify timestamps
            const originalUpdatedAt = createdReport.updatedAt;
            await new Promise(resolve => setTimeout(resolve, 10)); // Small delay
            const updatedReport = await service.updateReportDefinition(createdReport.id, { description: 'Updated description' }, 'another-user');
            expect(updatedReport.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
            expect(updatedReport.updatedBy).toBe('another-user');
            expect(updatedReport.createdBy).toBe('test-user'); // Should remain unchanged
        });
        it('should enforce unique constraints', async () => {
            const reportData1 = {
                name: 'Unique Test Report',
                description: 'First report',
                category: 'compliance',
                type: 'tabular',
                dataSource: 'compliance',
                query: 'SELECT * FROM compliance_data',
                parameters: [],
                outputFormats: ['pdf'],
            };
            const reportData2 = {
                ...reportData1,
                description: 'Second report with same name',
            };
            await service.createReportDefinition(reportData1, 'test-user');
            await expect(service.createReportDefinition(reportData2, 'test-user'))
                .rejects.toThrow('Report definition with this name already exists');
        });
    });
    describe('Query Performance', () => {
        beforeEach(async () => {
            // Create test data
            const reports = Array.from({ length: 50 }, (_, i) => ({
                name: `Performance Test Report ${i}`,
                description: `Test report ${i} for performance testing`,
                category: i % 2 === 0 ? 'compliance' : 'audit',
                type: i % 3 === 0 ? 'tabular' : 'chart',
                dataSource: i % 2 === 0 ? 'compliance' : 'audit',
                query: `SELECT * FROM ${i % 2 === 0 ? 'compliance' : 'audit'}_data`,
                parameters: [],
                outputFormats: ['pdf'],
                isActive: true,
                createdBy: 'test-user',
                updatedBy: 'test-user',
                createdAt: new Date(),
                updatedAt: new Date(),
            }));
            await repository.save(reports);
        });
        it('should perform efficient pagination queries', async () => {
            const startTime = Date.now();
            const result = await service.getReportDefinitions({
                page: 1,
                limit: 10,
                category: 'compliance',
            });
            const queryTime = Date.now() - startTime;
            expect(result.reportDefinitions).toHaveLength(10);
            expect(result.total).toBeGreaterThan(0);
            expect(queryTime).toBeLessThan(1000); // Should complete within 1 second
        });
        it('should perform efficient search queries', async () => {
            const startTime = Date.now();
            const result = await service.getReportDefinitions({
                search: 'Performance Test',
                page: 1,
                limit: 20,
            });
            const queryTime = Date.now() - startTime;
            expect(result.reportDefinitions.length).toBeGreaterThan(0);
            expect(queryTime).toBeLessThan(1000); // Should complete within 1 second
            // Verify search results contain the search term
            result.reportDefinitions.forEach(report => {
                expect(report.name.toLowerCase()).toContain('performance test');
            });
        });
        it('should handle large result sets efficiently', async () => {
            const startTime = Date.now();
            const result = await service.getReportDefinitions({
                page: 1,
                limit: 100,
            });
            const queryTime = Date.now() - startTime;
            expect(result.reportDefinitions.length).toBeGreaterThan(0);
            expect(queryTime).toBeLessThan(2000); // Should complete within 2 seconds
        });
    });
    describe('Soft Delete Functionality', () => {
        it('should soft delete report definition', async () => {
            const reportData = {
                name: 'Soft Delete Test',
                description: 'Test report for soft delete',
                category: 'compliance',
                type: 'tabular',
                dataSource: 'compliance',
                query: 'SELECT * FROM compliance_data',
                parameters: [],
                outputFormats: ['pdf'],
            };
            const createdReport = await service.createReportDefinition(reportData, 'test-user');
            // Delete the report
            await service.deleteReportDefinition(createdReport.id, 'delete-user');
            // Verify it's not returned in active queries
            const activeReports = await service.getReportDefinitions({});
            expect(activeReports.reportDefinitions.find(r => r.id === createdReport.id)).toBeUndefined();
            // Verify it still exists in database but marked as inactive
            const deletedReport = await repository.findOne({
                where: { id: createdReport.id },
                withDeleted: true,
            });
            expect(deletedReport).toBeDefined();
            expect(deletedReport.isActive).toBe(false);
            expect(deletedReport.updatedBy).toBe('delete-user');
        });
        it('should not return soft deleted reports in queries', async () => {
            const reportData = {
                name: 'Query Exclusion Test',
                description: 'Test report for query exclusion',
                category: 'compliance',
                type: 'tabular',
                dataSource: 'compliance',
                query: 'SELECT * FROM compliance_data',
                parameters: [],
                outputFormats: ['pdf'],
            };
            const createdReport = await service.createReportDefinition(reportData, 'test-user');
            await service.deleteReportDefinition(createdReport.id, 'delete-user');
            // Verify getReportDefinitionById returns null
            const retrievedReport = await service.getReportDefinitionById(createdReport.id);
            expect(retrievedReport).toBeNull();
            // Verify it's not in the list
            const allReports = await service.getReportDefinitions({});
            expect(allReports.reportDefinitions.find(r => r.id === createdReport.id)).toBeUndefined();
        });
    });
    describe('Cache Integration', () => {
        it('should integrate with cache service', async () => {
            const reportData = {
                name: 'Cache Integration Test',
                description: 'Test report for cache integration',
                category: 'compliance',
                type: 'tabular',
                dataSource: 'compliance',
                query: 'SELECT * FROM compliance_data',
                parameters: [],
                outputFormats: ['pdf'],
            };
            const createdReport = await service.createReportDefinition(reportData, 'test-user');
            // Verify cache operations were called
            expect(cacheService.delPattern).toHaveBeenCalledWith('report_definitions_*');
            // Reset mocks
            jest.clearAllMocks();
            // Get report by ID (should attempt cache first)
            cacheService.get.mockResolvedValue(null);
            await service.getReportDefinitionById(createdReport.id);
            expect(cacheService.get).toHaveBeenCalledWith(`report_definition_${createdReport.id}`);
            expect(cacheService.set).toHaveBeenCalledWith(`report_definition_${createdReport.id}`, expect.any(Object), 3600);
        });
    });
    describe('Metrics Integration', () => {
        it('should record metrics for database operations', async () => {
            const reportData = {
                name: 'Metrics Integration Test',
                description: 'Test report for metrics integration',
                category: 'compliance',
                type: 'tabular',
                dataSource: 'compliance',
                query: 'SELECT * FROM compliance_data',
                parameters: [],
                outputFormats: ['pdf'],
            };
            await service.createReportDefinition(reportData, 'test-user');
            expect(metricsService.recordCounter).toHaveBeenCalledWith('report_definition_created', {
                category: 'compliance',
                type: 'tabular',
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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