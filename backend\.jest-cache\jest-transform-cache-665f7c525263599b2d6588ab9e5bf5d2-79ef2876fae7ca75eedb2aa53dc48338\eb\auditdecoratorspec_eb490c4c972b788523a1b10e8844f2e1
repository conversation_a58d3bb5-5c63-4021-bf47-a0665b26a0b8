272041797f6cdf7cc2270d558de889d5
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
const audit_decorator_1 = require("../../decorators/audit.decorator");
const correlation_id_value_object_1 = require("../../value-objects/correlation-id.value-object");
const user_id_value_object_1 = require("../../value-objects/user-id.value-object");
const tenant_id_value_object_1 = require("../../value-objects/tenant-id.value-object");
describe('Audit Decorator', () => {
    let mockAuditLogger;
    let auditLogs;
    beforeEach(() => {
        auditLogs = [];
        mockAuditLogger = {
            log: jest.fn().mockImplementation((log) => {
                auditLogs.push(log);
                return Promise.resolve();
            }),
        };
    });
    describe('@Audit decorator', () => {
        it('should log successful method execution', async () => {
            var _a;
            class TestService {
                constructor() {
                    this.auditLogger = mockAuditLogger;
                }
                async testMethod(param) {
                    return `result-${param}`;
                }
            }
            __decorate([
                (0, audit_decorator_1.Audit)({ operation: 'test-operation', resource: 'test-resource' }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "testMethod", null);
            const service = new TestService();
            const result = await service.testMethod('input');
            expect(result).toBe('result-input');
            expect(mockAuditLogger.log).toHaveBeenCalledTimes(1);
            const auditLog = auditLogs[0];
            expect(auditLog.operation).toBe('test-operation');
            expect(auditLog.resource).toBe('test-resource');
            expect(auditLog.status).toBe('SUCCESS');
            expect(auditLog.duration).toBeGreaterThanOrEqual(0);
            expect(auditLog.metadata?.args).toEqual(['input']);
        });
        it('should log failed method execution', async () => {
            var _a;
            class TestService {
                constructor() {
                    this.auditLogger = mockAuditLogger;
                }
                async failingMethod() {
                    throw new Error('Test error');
                }
            }
            __decorate([
                (0, audit_decorator_1.Audit)({ operation: 'failing-operation' }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "failingMethod", null);
            const service = new TestService();
            await expect(service.failingMethod()).rejects.toThrow('Test error');
            expect(mockAuditLogger.log).toHaveBeenCalledTimes(1);
            const auditLog = auditLogs[0];
            expect(auditLog.operation).toBe('failing-operation');
            expect(auditLog.status).toBe('FAILURE');
            expect(auditLog.error).toBe('Test error');
        });
        it('should include user and tenant context', async () => {
            var _a;
            const userId = user_id_value_object_1.UserId.create();
            const tenantId = tenant_id_value_object_1.TenantId.create();
            const correlationId = correlation_id_value_object_1.CorrelationId.create();
            class TestService {
                constructor() {
                    this.auditLogger = mockAuditLogger;
                }
                async contextMethod() {
                    // Method implementation
                }
            }
            __decorate([
                (0, audit_decorator_1.Audit)({
                    userId,
                    tenantId,
                    correlationId,
                    operation: 'context-operation'
                }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "contextMethod", null);
            const service = new TestService();
            await service.contextMethod();
            const auditLog = auditLogs[0];
            expect(auditLog.userId).toBe(userId);
            expect(auditLog.tenantId).toBe(tenantId);
            expect(auditLog.correlationId).toBe(correlationId);
        });
        it('should handle missing audit logger gracefully', async () => {
            var _a;
            class TestService {
                // No audit logger
                async methodWithoutLogger() {
                    return 'success';
                }
            }
            __decorate([
                (0, audit_decorator_1.Audit)({ operation: 'no-logger-operation' }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "methodWithoutLogger", null);
            const service = new TestService();
            const result = await service.methodWithoutLogger();
            expect(result).toBe('success');
            // Should not throw error even without logger
        });
        it('should generate default operation name from class and method', async () => {
            var _a;
            class TestService {
                constructor() {
                    this.auditLogger = mockAuditLogger;
                }
                async defaultNameMethod() {
                    // Method implementation
                }
            }
            __decorate([
                (0, audit_decorator_1.Audit)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "defaultNameMethod", null);
            const service = new TestService();
            await service.defaultNameMethod();
            const auditLog = auditLogs[0];
            expect(auditLog.operation).toBe('TestService.defaultNameMethod');
            expect(auditLog.resource).toBe('TestService');
        });
        it('should include custom metadata', async () => {
            var _a;
            const customMetadata = { customField: 'customValue', number: 42 };
            class TestService {
                constructor() {
                    this.auditLogger = mockAuditLogger;
                }
                async metadataMethod() {
                    // Method implementation
                }
            }
            __decorate([
                (0, audit_decorator_1.Audit)({ metadata: customMetadata }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "metadataMethod", null);
            const service = new TestService();
            await service.metadataMethod();
            const auditLog = auditLogs[0];
            expect(auditLog.metadata).toMatchObject(customMetadata);
        });
        it('should handle complex arguments properly', async () => {
            var _a;
            class TestService {
                constructor() {
                    this.auditLogger = mockAuditLogger;
                }
                async complexArgsMethod(str, num, obj, arr) {
                    // Method implementation
                }
            }
            __decorate([
                (0, audit_decorator_1.Audit)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String, Number, Object, Array]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "complexArgsMethod", null);
            const service = new TestService();
            await service.complexArgsMethod('test', 123, { prop: 'value' }, [1, 2, 3]);
            const auditLog = auditLogs[0];
            expect(auditLog.metadata?.args).toEqual(['test', 123, '[Object]', '[Object]']);
        });
    });
    describe('@SimpleAudit decorator', () => {
        it('should work with custom operation name', async () => {
            var _a;
            class TestService {
                constructor() {
                    this.auditLogger = mockAuditLogger;
                }
                async simpleMethod() {
                    return 'simple-result';
                }
            }
            __decorate([
                (0, audit_decorator_1.SimpleAudit)('simple-operation'),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "simpleMethod", null);
            const service = new TestService();
            const result = await service.simpleMethod();
            expect(result).toBe('simple-result');
            expect(mockAuditLogger.log).toHaveBeenCalledTimes(1);
            const auditLog = auditLogs[0];
            expect(auditLog.operation).toBe('simple-operation');
            expect(auditLog.status).toBe('SUCCESS');
        });
        it('should use default operation name when not provided', async () => {
            var _a;
            class TestService {
                constructor() {
                    this.auditLogger = mockAuditLogger;
                }
                async defaultSimpleMethod() {
                    // Method implementation
                }
            }
            __decorate([
                (0, audit_decorator_1.SimpleAudit)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "defaultSimpleMethod", null);
            const service = new TestService();
            await service.defaultSimpleMethod();
            const auditLog = auditLogs[0];
            expect(auditLog.operation).toBe('TestService.defaultSimpleMethod');
        });
    });
    describe('error handling', () => {
        it('should continue execution even if audit logging fails', async () => {
            var _a;
            const failingLogger = {
                log: jest.fn().mockRejectedValue(new Error('Logging failed')),
            };
            class TestService {
                constructor() {
                    this.auditLogger = failingLogger;
                }
                async methodWithFailingLogger() {
                    return 'method-result';
                }
            }
            __decorate([
                (0, audit_decorator_1.Audit)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "methodWithFailingLogger", null);
            const service = new TestService();
            const result = await service.methodWithFailingLogger();
            expect(result).toBe('method-result');
            expect(failingLogger.log).toHaveBeenCalledTimes(1);
        });
        it('should preserve original error when method fails', async () => {
            var _a;
            class TestService {
                constructor() {
                    this.auditLogger = mockAuditLogger;
                }
                async methodThatThrows() {
                    throw new Error('Original error');
                }
            }
            __decorate([
                (0, audit_decorator_1.Audit)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "methodThatThrows", null);
            const service = new TestService();
            await expect(service.methodThatThrows()).rejects.toThrow('Original error');
            const auditLog = auditLogs[0];
            expect(auditLog.status).toBe('FAILURE');
            expect(auditLog.error).toBe('Original error');
        });
    });
    describe('performance', () => {
        it('should measure execution duration accurately', async () => {
            var _a;
            class TestService {
                constructor() {
                    this.auditLogger = mockAuditLogger;
                }
                async slowMethod() {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }
            __decorate([
                (0, audit_decorator_1.Audit)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "slowMethod", null);
            const service = new TestService();
            await service.slowMethod();
            const auditLog = auditLogs[0];
            expect(auditLog.duration).toBeGreaterThanOrEqual(90); // Allow some variance
            expect(auditLog.duration).toBeLessThan(200);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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