{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\__tests__\\api-integration.spec.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAAsD;AAEtD,2CAA8C;AAC9C,mDAAqC;AACrC,uEAAkE;AAClE,uEAAkE;AAClE,iFAA4E;AAC5E,uCAAyC;AACzC,2CAAsF;AACtF,+EAAiE;AAEjE,8CAA8C;AAC9C,IAEM,cAAc,GAFpB,MAEM,cAAe,SAAQ,uCAAiB;IAC5C;QACE,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAC1B,CAAC;IAGD,UAAU;QACR,OAAO,IAAI,CAAC,qBAAqB,CAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC,CAAC;IACpE,CAAC;IAGD,QAAQ;QACN,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;IAC9D,CAAC;IAGD,YAAY,CAAU,KAAU;QAC9B,MAAM,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;QACpE,MAAM,UAAU,GAAG;YACjB,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,CAAC;YACR,UAAU,EAAE,CAAC;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,CAAC;SACV,CAAC;QACF,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IACxD,CAAC;IAGD,YAAY,CAAS,IAAS;QAC5B,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC,qBAAqB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IACzD,CAAC;IAGD,WAAW,CAAiB,KAAa;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5C,OAAO,IAAI,CAAC,qBAAqB,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;IACpE,CAAC;CACF,CAAA;AAnCC;IADC,IAAA,YAAG,EAAC,SAAS,CAAC;;;;gDAGd;AAGD;IADC,IAAA,YAAG,EAAC,OAAO,CAAC;;;;8CAGZ;AAGD;IADC,IAAA,YAAG,EAAC,WAAW,CAAC;IACH,WAAA,IAAA,cAAK,GAAE,CAAA;;;;kDAYpB;AAGD;IADC,IAAA,aAAI,EAAC,UAAU,CAAC;IACH,WAAA,IAAA,aAAI,GAAE,CAAA;;;;kDAGnB;AAGD;IADC,IAAA,YAAG,EAAC,iBAAiB,CAAC;IACV,WAAA,IAAA,cAAK,EAAC,OAAO,CAAC,CAAA;;;;iDAG1B;AAxCG,cAAc;IAFnB,IAAA,mBAAU,EAAC,MAAM,CAAC;IAClB,IAAA,kCAAU,EAAC,KAAK,CAAC;;GACZ,cAAc,CAyCnB;AAED,iCAAiC;AAGjC,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAGvB,KAAK;QACH,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAC7D,CAAC;IAID,KAAK;QACH,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAC7D,CAAC;IAID,aAAa;QACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;IAC5D,CAAC;CACF,CAAA;AAfC;IAFC,IAAA,YAAG,EAAC,IAAI,CAAC;IACT,IAAA,kCAAU,EAAC,KAAK,CAAC;;;;gDAGjB;AAID;IAFC,IAAA,YAAG,EAAC,IAAI,CAAC;IACT,IAAA,kCAAU,EAAC,KAAK,CAAC;;;;gDAGjB;AAID;IAFC,IAAA,YAAG,EAAC,YAAY,CAAC;IACjB,IAAA,kCAAU,EAAC,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;;;;wDAG1D;AAjBG,mBAAmB;IAFxB,IAAA,mBAAU,EAAC,WAAW,CAAC;IACvB,IAAA,kBAAS,EAAC,mCAAe,CAAC;GACrB,mBAAmB,CAkBxB;AAED,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,IAAI,GAAqB,CAAC;IAC1B,IAAI,iBAAoD,CAAC;IAEzD,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,4BAA4B;QAC5B,iBAAiB,GAAG;YAClB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;YACzB,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;YAC1B,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;YACzB,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;YAC/B,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;YAC7B,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;YAC1B,sBAAsB,EAAE,IAAI,CAAC,EAAE,EAAE;SAC3B,CAAC;QAET,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;iBACf,CAAC;aACH;YACD,WAAW,EAAE,CAAC,cAAc,EAAE,mBAAmB,CAAC;YAClD,SAAS,EAAE;gBACT;oBACE,OAAO,EAAE,6CAAoB;oBAC7B,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD,gBAAS;gBACT,mCAAe;aAChB;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;IACnB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,GAAG,EAAE;QACd,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAChC,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;gBAC3D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,eAAe,CAAC;qBACpB,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;oBAC5B,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE;oBACpC,OAAO,EAAE,kCAAkC;oBAC3C,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,QAAQ,EAAE,EAAE;iBACb,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;gBACzD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,aAAa,CAAC;qBAClB,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;oBAC5B,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE;wBACL,IAAI,EAAE,YAAY;wBAClB,OAAO,EAAE,YAAY;wBACrB,OAAO,EAAE,EAAE;wBACX,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC9B;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;gBAC7D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,iBAAiB,CAAC;qBACtB,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;oBAC5B,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE;wBACJ,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACzB,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;qBAC1B;oBACD,OAAO,EAAE,6BAA6B;oBACtC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,UAAU,EAAE;wBACV,IAAI,EAAE,CAAC;wBACP,KAAK,EAAE,EAAE;wBACT,KAAK,EAAE,CAAC;wBACR,UAAU,EAAE,CAAC;wBACb,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,CAAC;qBACV;oBACD,QAAQ,EAAE;wBACR,UAAU,EAAE,CAAC;wBACb,YAAY,EAAE,EAAE;wBAChB,WAAW,EAAE,CAAC;wBACd,UAAU,EAAE,CAAC;qBACd;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;gBAChE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,IAAI,CAAC,gBAAgB,CAAC;qBACtB,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC;qBACjD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;oBAC5B,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;oBACzB,OAAO,EAAE,kCAAkC;oBAC3C,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,QAAQ,EAAE,EAAE;iBACb,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;gBACtE,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,IAAI,CAAC,gBAAgB,CAAC;qBACtB,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,gBAAgB;qBACvC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,wDAAwD;YAC1E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;gBAC5C,MAAM,cAAc,GAAG,oCAAoC,CAAC;gBAC5D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,kBAAkB,kBAAkB,CAAC,cAAc,CAAC,EAAE,CAAC;qBAC3D,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;gBAC1C,MAAM,SAAS,GAAG,aAAa,CAAC;gBAChC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,kBAAkB,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC;qBACtD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACrD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,UAAU,CAAC,GAAG,EAAE;YACd,6CAA6C;YAC7C,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACxD,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACxD,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC;gBAC/C,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC,CAAC;YACH,iBAAiB,CAAC,oBAAoB,CAAC,eAAe,CAAC;gBACrD,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,IAAI,EAAE,EAAE;gBAC7D,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,IAAI,EAAE,EAAE;aAC9D,CAAC,CAAC;YACH,iBAAiB,CAAC,kBAAkB,CAAC,eAAe,CAAC;gBACnD,UAAU,EAAE,IAAI;gBAChB,iBAAiB,EAAE,KAAK;gBACxB,eAAe,EAAE,EAAE;aACpB,CAAC,CAAC;YACH,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAC/D,iBAAiB,CAAC,sBAAsB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;gBACzD,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,eAAe,CAAC;qBACpB,GAAG,CAAC,QAAQ,EAAE,sCAAsC,CAAC;qBACrD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC9D,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;gBACzD,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,eAAe,CAAC;qBACpB,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;qBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC9D,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;gBAC3D,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,2BAA2B,CAAC;qBAChC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC9D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;gBAC1C,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACxD,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAExD,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,eAAe,CAAC;qBACpB,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;qBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;gBAC7C,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;gBAC1D,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACzD,iBAAiB,CAAC,oBAAoB,CAAC,eAAe,CAAC;oBACrD,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,IAAI,EAAE,EAAE;oBAC7D,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,IAAI,EAAE,EAAE;iBAC9D,CAAC,CAAC;gBAEH,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,eAAe,CAAC;qBACpB,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC;qBAC7B,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;gBAC5C,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACxD,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACxD,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC;oBAC/C,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,QAAQ;oBAChB,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,UAAU,EAAE,IAAI,IAAI,EAAE;iBACvB,CAAC,CAAC;gBAEH,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,eAAe,CAAC;qBACpB,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;qBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO;YACzB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;gBACvE,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACxD,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACxD,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC;oBAC/C,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,YAAY;oBACpB,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,eAAe,EAAE,IAAI,IAAI,EAAE;iBAC5B,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,uBAAuB,CAAC;qBAC5B,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;qBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC1D,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,+DAA+D,EAAE,KAAK,IAAI,EAAE;gBAC7E,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACxD,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACxD,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC;oBAC/C,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,YAAY;oBACpB,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,eAAe,EAAE,IAAI,IAAI,EAAE;iBAC5B,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,uBAAuB,CAAC;qBAC5B,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;qBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;gBACjD,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACxD,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACxD,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC;oBAC/C,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,QAAQ;oBAChB,WAAW,EAAE,IAAI,IAAI,EAAE;iBACxB,CAAC,CAAC;gBACH,iBAAiB,CAAC,kBAAkB,CAAC,eAAe,CAAC;oBACnD,UAAU,EAAE,IAAI;oBAChB,iBAAiB,EAAE,KAAK;oBACxB,eAAe,EAAE,EAAE;iBACpB,CAAC,CAAC;gBAEH,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,eAAe,CAAC;qBACpB,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;qBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;gBACxD,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACxD,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACxD,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC;oBAC/C,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,QAAQ;oBAChB,WAAW,EAAE,IAAI,IAAI,EAAE;iBACxB,CAAC,CAAC;gBACH,iBAAiB,CAAC,kBAAkB,CAAC,eAAe,CAAC;oBACnD,UAAU,EAAE,IAAI;oBAChB,iBAAiB,EAAE,IAAI;oBACvB,eAAe,EAAE,CAAC,qCAAqC,CAAC;iBACzD,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,eAAe,CAAC;qBACpB,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;qBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;gBACxF,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAChF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;YAC/B,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;gBACxC,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACxD,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACxD,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC;oBAC/C,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,QAAQ;oBAChB,WAAW,EAAE,IAAI,IAAI,EAAE;iBACxB,CAAC,CAAC;gBAEH,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,eAAe,CAAC;qBACpB,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;qBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAC5D,KAAK,EACL,eAAe,EACf,SAAS,CACV,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,iBAAiB,CAAC,cAAc,CAAC,kBAAkB,CAAC,GAAG,EAAE;gBACvD,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,eAAe,CAAC;iBACpB,GAAG,CAAC,eAAe,EAAE,SAAS,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAC1D,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACzD,iBAAiB,CAAC,oBAAoB,CAAC,eAAe,CAAC;gBACrD,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,IAAI,EAAE,EAAE;gBAC7D,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,IAAI,EAAE,EAAE;aAC9D,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,eAAe,CAAC;iBACpB,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC;iBAC7B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YAC1E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,eAAe,CAAC;iBACpB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,MAAM,aAAa,GAAG,sBAAsB,CAAC;YAE7C,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,eAAe,CAAC;iBACpB,GAAG,CAAC,kBAAkB,EAAE,aAAa,CAAC;iBACtC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,iFAAiF;YACjF,6CAA6C;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,4DAA4D;QAC5D,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,0CAA0C;YAC1C,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAC5C,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,CAClD,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE9C,8CAA8C;YAC9C,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,eAAe,CAAC;iBACpB,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC;iBACjC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACxD,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACxD,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC;gBAC/C,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,eAAe,CAAC;iBACpB,GAAG,CAAC,QAAQ,EAAE,sCAAsC,CAAC;iBACrD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,eAAe,CAAC;iBACpB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,0EAA0E;YAC1E,yDAAyD;YACzD,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,iCAAiC,CAAC;iBACtC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC;gBACvC,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,KAAK,EAAE,CAAC;gBACR,UAAU,EAAE,CAAC;gBACb,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,CAAC;aACV,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oCAAoC,CAAC;iBACzC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wDAAwD;YACxD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\__tests__\\api-integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport * as request from 'supertest';\r\nimport { BaseApiController } from '../common/base-api.controller';\r\nimport { ApiVersionGuard } from '../versioning/api-version.guard';\r\nimport { ApiVersioningService } from '../versioning/api-versioning.service';\r\nimport { Reflector } from '@nestjs/core';\r\nimport { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';\r\nimport { ApiVersion } from '../versioning/api-version.decorator';\r\n\r\n// Test controller extending BaseApiController\r\n@Controller('test')\r\n@ApiVersion('1.0')\r\nclass TestController extends BaseApiController {\r\n  constructor() {\r\n    super('TestController');\r\n  }\r\n\r\n  @Get('success')\r\n  getSuccess() {\r\n    return this.createSuccessResponse({ message: 'Test successful' });\r\n  }\r\n\r\n  @Get('error')\r\n  getError() {\r\n    return this.createErrorResponse('Test error', 'TEST_ERROR');\r\n  }\r\n\r\n  @Get('paginated')\r\n  getPaginated(@Query() query: any) {\r\n    const data = [{ id: 1, name: 'Item 1' }, { id: 2, name: 'Item 2' }];\r\n    const pagination = {\r\n      page: 1,\r\n      limit: 10,\r\n      total: 2,\r\n      totalPages: 1,\r\n      hasNext: false,\r\n      hasPrev: false,\r\n      offset: 0,\r\n    };\r\n    return this.createPaginatedResponse(data, pagination);\r\n  }\r\n\r\n  @Post('validate')\r\n  postValidate(@Body() body: any) {\r\n    this.validateRequiredParams(body, ['name', 'email']);\r\n    return this.createSuccessResponse({ validated: true });\r\n  }\r\n\r\n  @Get('sanitize/:input')\r\n  getSanitize(@Param('input') input: string) {\r\n    const sanitized = this.sanitizeInput(input);\r\n    return this.createSuccessResponse({ original: input, sanitized });\r\n  }\r\n}\r\n\r\n// Test controller for versioning\r\n@Controller('versioned')\r\n@UseGuards(ApiVersionGuard)\r\nclass VersionedController {\r\n  @Get('v1')\r\n  @ApiVersion('1.0')\r\n  getV1() {\r\n    return { version: '1.0', message: 'Version 1.0 endpoint' };\r\n  }\r\n\r\n  @Get('v2')\r\n  @ApiVersion('2.0')\r\n  getV2() {\r\n    return { version: '2.0', message: 'Version 2.0 endpoint' };\r\n  }\r\n\r\n  @Get('deprecated')\r\n  @ApiVersion('1.0', { deprecated: true, replacement: 'v2' })\r\n  getDeprecated() {\r\n    return { version: '1.0', message: 'Deprecated endpoint' };\r\n  }\r\n}\r\n\r\ndescribe('API Integration Tests', () => {\r\n  let app: INestApplication;\r\n  let versioningService: jest.Mocked<ApiVersioningService>;\r\n\r\n  beforeAll(async () => {\r\n    // Mock ApiVersioningService\r\n    versioningService = {\r\n      extractVersion: jest.fn(),\r\n      validateVersion: jest.fn(),\r\n      getVersionInfo: jest.fn(),\r\n      getSupportedVersions: jest.fn(),\r\n      checkCompatibility: jest.fn(),\r\n      logVersionUsage: jest.fn(),\r\n      getDeprecationWarnings: jest.fn(),\r\n    } as any;\r\n\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n        }),\r\n      ],\r\n      controllers: [TestController, VersionedController],\r\n      providers: [\r\n        {\r\n          provide: ApiVersioningService,\r\n          useValue: versioningService,\r\n        },\r\n        Reflector,\r\n        ApiVersionGuard,\r\n      ],\r\n    }).compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    await app.init();\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  beforeEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('BaseApiController', () => {\r\n    describe('Success Response', () => {\r\n      it('should return standardized success response', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/test/success')\r\n          .expect(200);\r\n\r\n        expect(response.body).toEqual({\r\n          success: true,\r\n          data: { message: 'Test successful' },\r\n          message: 'Operation completed successfully',\r\n          timestamp: expect.any(String),\r\n          metadata: {},\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('Error Response', () => {\r\n      it('should return standardized error response', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/test/error')\r\n          .expect(200);\r\n\r\n        expect(response.body).toEqual({\r\n          success: false,\r\n          error: {\r\n            code: 'TEST_ERROR',\r\n            message: 'Test error',\r\n            details: {},\r\n            timestamp: expect.any(String),\r\n          },\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('Paginated Response', () => {\r\n      it('should return standardized paginated response', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/test/paginated')\r\n          .expect(200);\r\n\r\n        expect(response.body).toEqual({\r\n          success: true,\r\n          data: [\r\n            { id: 1, name: 'Item 1' },\r\n            { id: 2, name: 'Item 2' },\r\n          ],\r\n          message: 'Data retrieved successfully',\r\n          timestamp: expect.any(String),\r\n          pagination: {\r\n            page: 1,\r\n            limit: 10,\r\n            total: 2,\r\n            totalPages: 1,\r\n            hasNext: false,\r\n            hasPrev: false,\r\n            offset: 0,\r\n          },\r\n          metadata: {\r\n            totalItems: 2,\r\n            itemsPerPage: 10,\r\n            currentPage: 1,\r\n            totalPages: 1,\r\n          },\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('Validation', () => {\r\n      it('should validate required parameters successfully', async () => {\r\n        const response = await request(app.getHttpServer())\r\n          .post('/test/validate')\r\n          .send({ name: 'John', email: '<EMAIL>' })\r\n          .expect(201);\r\n\r\n        expect(response.body).toEqual({\r\n          success: true,\r\n          data: { validated: true },\r\n          message: 'Operation completed successfully',\r\n          timestamp: expect.any(String),\r\n          metadata: {},\r\n        });\r\n      });\r\n\r\n      it('should fail validation for missing required parameters', async () => {\r\n        await request(app.getHttpServer())\r\n          .post('/test/validate')\r\n          .send({ name: 'John' }) // Missing email\r\n          .expect(500); // This would be handled by exception filter in real app\r\n      });\r\n    });\r\n\r\n    describe('Input Sanitization', () => {\r\n      it('should sanitize XSS attempts', async () => {\r\n        const maliciousInput = '<script>alert(\"xss\")</script>Hello';\r\n        const response = await request(app.getHttpServer())\r\n          .get(`/test/sanitize/${encodeURIComponent(maliciousInput)}`)\r\n          .expect(200);\r\n\r\n        expect(response.body.data.sanitized).toBe('Hello');\r\n        expect(response.body.data.original).toBe(maliciousInput);\r\n      });\r\n\r\n      it('should preserve safe input', async () => {\r\n        const safeInput = 'Hello World';\r\n        const response = await request(app.getHttpServer())\r\n          .get(`/test/sanitize/${encodeURIComponent(safeInput)}`)\r\n          .expect(200);\r\n\r\n        expect(response.body.data.sanitized).toBe(safeInput);\r\n        expect(response.body.data.original).toBe(safeInput);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('API Versioning', () => {\r\n    beforeEach(() => {\r\n      // Setup default mocks for versioning service\r\n      versioningService.extractVersion.mockReturnValue('1.0');\r\n      versioningService.validateVersion.mockReturnValue(true);\r\n      versioningService.getVersionInfo.mockReturnValue({\r\n        version: '1.0',\r\n        status: 'active',\r\n        releaseDate: new Date(),\r\n      });\r\n      versioningService.getSupportedVersions.mockReturnValue([\r\n        { version: '1.0', status: 'active', releaseDate: new Date() },\r\n        { version: '2.0', status: 'active', releaseDate: new Date() },\r\n      ]);\r\n      versioningService.checkCompatibility.mockReturnValue({\r\n        compatible: true,\r\n        migrationRequired: false,\r\n        breakingChanges: [],\r\n      });\r\n      versioningService.logVersionUsage.mockResolvedValue(undefined);\r\n      versioningService.getDeprecationWarnings.mockReturnValue([]);\r\n    });\r\n\r\n    describe('Version Extraction', () => {\r\n      it('should extract version from Accept header', async () => {\r\n        await request(app.getHttpServer())\r\n          .get('/versioned/v1')\r\n          .set('Accept', 'application/vnd.api+json;version=1.0')\r\n          .expect(200);\r\n\r\n        expect(versioningService.extractVersion).toHaveBeenCalled();\r\n      });\r\n\r\n      it('should extract version from custom header', async () => {\r\n        await request(app.getHttpServer())\r\n          .get('/versioned/v1')\r\n          .set('X-API-Version', '1.0')\r\n          .expect(200);\r\n\r\n        expect(versioningService.extractVersion).toHaveBeenCalled();\r\n      });\r\n\r\n      it('should extract version from query parameter', async () => {\r\n        await request(app.getHttpServer())\r\n          .get('/versioned/v1?version=1.0')\r\n          .expect(200);\r\n\r\n        expect(versioningService.extractVersion).toHaveBeenCalled();\r\n      });\r\n    });\r\n\r\n    describe('Version Validation', () => {\r\n      it('should allow valid version', async () => {\r\n        versioningService.extractVersion.mockReturnValue('1.0');\r\n        versioningService.validateVersion.mockReturnValue(true);\r\n\r\n        await request(app.getHttpServer())\r\n          .get('/versioned/v1')\r\n          .set('X-API-Version', '1.0')\r\n          .expect(200);\r\n\r\n        expect(versioningService.validateVersion).toHaveBeenCalledWith('1.0');\r\n      });\r\n\r\n      it('should reject invalid version', async () => {\r\n        versioningService.extractVersion.mockReturnValue('999.0');\r\n        versioningService.validateVersion.mockReturnValue(false);\r\n        versioningService.getSupportedVersions.mockReturnValue([\r\n          { version: '1.0', status: 'active', releaseDate: new Date() },\r\n          { version: '2.0', status: 'active', releaseDate: new Date() },\r\n        ]);\r\n\r\n        await request(app.getHttpServer())\r\n          .get('/versioned/v1')\r\n          .set('X-API-Version', '999.0')\r\n          .expect(400);\r\n      });\r\n\r\n      it('should reject sunset version', async () => {\r\n        versioningService.extractVersion.mockReturnValue('0.5');\r\n        versioningService.validateVersion.mockReturnValue(true);\r\n        versioningService.getVersionInfo.mockReturnValue({\r\n          version: '0.5',\r\n          status: 'sunset',\r\n          releaseDate: new Date(),\r\n          sunsetDate: new Date(),\r\n        });\r\n\r\n        await request(app.getHttpServer())\r\n          .get('/versioned/v1')\r\n          .set('X-API-Version', '0.5')\r\n          .expect(410); // Gone\r\n      });\r\n    });\r\n\r\n    describe('Deprecation Handling', () => {\r\n      it('should add deprecation headers for deprecated endpoints', async () => {\r\n        versioningService.extractVersion.mockReturnValue('1.0');\r\n        versioningService.validateVersion.mockReturnValue(true);\r\n        versioningService.getVersionInfo.mockReturnValue({\r\n          version: '1.0',\r\n          status: 'deprecated',\r\n          releaseDate: new Date(),\r\n          deprecationDate: new Date(),\r\n        });\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .get('/versioned/deprecated')\r\n          .set('X-API-Version', '1.0')\r\n          .expect(200);\r\n\r\n        expect(response.headers['x-api-deprecated']).toBe('true');\r\n        expect(response.headers['x-api-deprecated-version']).toBe('1.0');\r\n      });\r\n\r\n      it('should include replacement information in deprecation headers', async () => {\r\n        versioningService.extractVersion.mockReturnValue('1.0');\r\n        versioningService.validateVersion.mockReturnValue(true);\r\n        versioningService.getVersionInfo.mockReturnValue({\r\n          version: '1.0',\r\n          status: 'deprecated',\r\n          releaseDate: new Date(),\r\n          deprecationDate: new Date(),\r\n        });\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .get('/versioned/deprecated')\r\n          .set('X-API-Version', '1.0')\r\n          .expect(200);\r\n\r\n        expect(response.headers['x-api-replacement']).toBe('v2');\r\n      });\r\n    });\r\n\r\n    describe('Version Compatibility', () => {\r\n      it('should handle compatible versions', async () => {\r\n        versioningService.extractVersion.mockReturnValue('1.1');\r\n        versioningService.validateVersion.mockReturnValue(true);\r\n        versioningService.getVersionInfo.mockReturnValue({\r\n          version: '1.1',\r\n          status: 'active',\r\n          releaseDate: new Date(),\r\n        });\r\n        versioningService.checkCompatibility.mockReturnValue({\r\n          compatible: true,\r\n          migrationRequired: false,\r\n          breakingChanges: [],\r\n        });\r\n\r\n        await request(app.getHttpServer())\r\n          .get('/versioned/v1')\r\n          .set('X-API-Version', '1.1')\r\n          .expect(200);\r\n      });\r\n\r\n      it('should warn about migration requirements', async () => {\r\n        versioningService.extractVersion.mockReturnValue('1.5');\r\n        versioningService.validateVersion.mockReturnValue(true);\r\n        versioningService.getVersionInfo.mockReturnValue({\r\n          version: '1.5',\r\n          status: 'active',\r\n          releaseDate: new Date(),\r\n        });\r\n        versioningService.checkCompatibility.mockReturnValue({\r\n          compatible: true,\r\n          migrationRequired: true,\r\n          breakingChanges: ['Field renamed: oldField -> newField'],\r\n        });\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .get('/versioned/v1')\r\n          .set('X-API-Version', '1.5')\r\n          .expect(200);\r\n\r\n        expect(response.headers['x-api-compatibility-warning']).toContain('Migration required');\r\n        expect(response.headers['x-api-breaking-changes']).toContain('Field renamed');\r\n      });\r\n    });\r\n\r\n    describe('Version Logging', () => {\r\n      it('should log version usage', async () => {\r\n        versioningService.extractVersion.mockReturnValue('1.0');\r\n        versioningService.validateVersion.mockReturnValue(true);\r\n        versioningService.getVersionInfo.mockReturnValue({\r\n          version: '1.0',\r\n          status: 'active',\r\n          releaseDate: new Date(),\r\n        });\r\n\r\n        await request(app.getHttpServer())\r\n          .get('/versioned/v1')\r\n          .set('X-API-Version', '1.0')\r\n          .expect(200);\r\n\r\n        expect(versioningService.logVersionUsage).toHaveBeenCalledWith(\r\n          '1.0',\r\n          '/versioned/v1',\r\n          undefined, // No user in test\r\n        );\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Error Handling', () => {\r\n    it('should handle version guard errors gracefully', async () => {\r\n      versioningService.extractVersion.mockImplementation(() => {\r\n        throw new Error('Version extraction failed');\r\n      });\r\n\r\n      await request(app.getHttpServer())\r\n        .get('/versioned/v1')\r\n        .set('X-API-Version', 'invalid')\r\n        .expect(400);\r\n    });\r\n\r\n    it('should provide meaningful error messages', async () => {\r\n      versioningService.extractVersion.mockReturnValue('999.0');\r\n      versioningService.validateVersion.mockReturnValue(false);\r\n      versioningService.getSupportedVersions.mockReturnValue([\r\n        { version: '1.0', status: 'active', releaseDate: new Date() },\r\n        { version: '2.0', status: 'active', releaseDate: new Date() },\r\n      ]);\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .get('/versioned/v1')\r\n        .set('X-API-Version', '999.0')\r\n        .expect(400);\r\n\r\n      expect(response.body.message).toContain('Unsupported API version: 999.0');\r\n      expect(response.body.message).toContain('Supported versions: 1.0, 2.0');\r\n    });\r\n  });\r\n\r\n  describe('Request/Response Transformation', () => {\r\n    it('should handle request transformation', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/test/success')\r\n        .expect(200);\r\n\r\n      expect(response.body.timestamp).toMatch(/^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}/);\r\n    });\r\n\r\n    it('should include correlation ID in responses when provided', async () => {\r\n      const correlationId = 'test-correlation-123';\r\n      \r\n      await request(app.getHttpServer())\r\n        .get('/test/success')\r\n        .set('X-Correlation-ID', correlationId)\r\n        .expect(200);\r\n\r\n      // In a real implementation, the correlation ID would be included in the response\r\n      // This test verifies the header is processed\r\n    });\r\n  });\r\n\r\n  describe('Rate Limiting Integration', () => {\r\n    // These tests would require actual rate limiting middleware\r\n    it('should respect rate limits', async () => {\r\n      // Multiple requests to test rate limiting\r\n      const requests = Array(5).fill(null).map(() =>\r\n        request(app.getHttpServer()).get('/test/success')\r\n      );\r\n\r\n      const responses = await Promise.all(requests);\r\n      \r\n      // All should succeed in this test environment\r\n      responses.forEach(response => {\r\n        expect(response.status).toBe(200);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Content Negotiation', () => {\r\n    it('should handle JSON content type', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/test/success')\r\n        .set('Accept', 'application/json')\r\n        .expect(200);\r\n\r\n      expect(response.headers['content-type']).toMatch(/application\\/json/);\r\n    });\r\n\r\n    it('should handle different API versions in Accept header', async () => {\r\n      versioningService.extractVersion.mockReturnValue('2.0');\r\n      versioningService.validateVersion.mockReturnValue(true);\r\n      versioningService.getVersionInfo.mockReturnValue({\r\n        version: '2.0',\r\n        status: 'active',\r\n        releaseDate: new Date(),\r\n      });\r\n\r\n      await request(app.getHttpServer())\r\n        .get('/versioned/v2')\r\n        .set('Accept', 'application/vnd.api+json;version=2.0')\r\n        .expect(200);\r\n\r\n      expect(versioningService.extractVersion).toHaveBeenCalled();\r\n    });\r\n  });\r\n\r\n  describe('Security Headers', () => {\r\n    it('should include security headers in responses', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/test/success')\r\n        .expect(200);\r\n\r\n      // In a real implementation, security headers would be added by middleware\r\n      // This test structure shows how to verify their presence\r\n      expect(response.headers).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('Pagination Integration', () => {\r\n    it('should handle pagination parameters correctly', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/test/paginated?page=1&limit=10')\r\n        .expect(200);\r\n\r\n      expect(response.body.pagination).toEqual({\r\n        page: 1,\r\n        limit: 10,\r\n        total: 2,\r\n        totalPages: 1,\r\n        hasNext: false,\r\n        hasPrev: false,\r\n        offset: 0,\r\n      });\r\n    });\r\n\r\n    it('should handle invalid pagination parameters', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/test/paginated?page=-1&limit=1000')\r\n        .expect(200);\r\n\r\n      // BaseApiController should normalize invalid parameters\r\n      expect(response.body.pagination.page).toBeGreaterThan(0);\r\n      expect(response.body.pagination.limit).toBeLessThanOrEqual(100);\r\n    });\r\n  });\r\n});"], "version": 3}