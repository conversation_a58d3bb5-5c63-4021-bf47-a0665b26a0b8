0f7e6cadb6dbbfb87060dcce04d3b03f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const threat_hunting_service_1 = require("./threat-hunting.service");
const security_event_repository_interface_1 = require("../../domain/interfaces/repositories/security-event.repository.interface");
const vulnerability_repository_interface_1 = require("../../domain/interfaces/repositories/vulnerability.repository.interface");
const incident_repository_interface_1 = require("../../domain/interfaces/repositories/incident.repository.interface");
const correlation_repository_interface_1 = require("../../domain/interfaces/repositories/correlation.repository.interface");
const domain_event_publisher_1 = require("../../../shared-kernel/domain/domain-event-publisher");
const confidence_level_enum_1 = require("../../domain/enums/confidence-level.enum");
const threat_severity_enum_1 = require("../../domain/enums/threat-severity.enum");
describe('ThreatHuntingService', () => {
    let service;
    let mockSecurityEventRepository;
    let mockVulnerabilityRepository;
    let mockIncidentRepository;
    let mockCorrelationRepository;
    let mockEventPublisher;
    beforeEach(async () => {
        mockSecurityEventRepository = {
            findByIOC: jest.fn(),
            findByCriteria: jest.fn(),
            getStatistics: jest.fn(),
        };
        mockVulnerabilityRepository = {
            getStatistics: jest.fn(),
        };
        mockIncidentRepository = {
            getStatistics: jest.fn(),
        };
        mockCorrelationRepository = {
            getStatistics: jest.fn(),
        };
        mockEventPublisher = {
            publishAll: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                threat_hunting_service_1.ThreatHuntingService,
                {
                    provide: security_event_repository_interface_1.SecurityEventRepository,
                    useValue: mockSecurityEventRepository,
                },
                {
                    provide: vulnerability_repository_interface_1.VulnerabilityRepository,
                    useValue: mockVulnerabilityRepository,
                },
                {
                    provide: incident_repository_interface_1.IncidentRepository,
                    useValue: mockIncidentRepository,
                },
                {
                    provide: correlation_repository_interface_1.CorrelationRepository,
                    useValue: mockCorrelationRepository,
                },
                {
                    provide: domain_event_publisher_1.DomainEventPublisher,
                    useValue: mockEventPublisher,
                },
            ],
        }).compile();
        service = module.get(threat_hunting_service_1.ThreatHuntingService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('executeHunt', () => {
        it('should execute IOC-based hunt successfully', async () => {
            const query = {
                name: 'Test IOC Hunt',
                description: 'Test IOC hunting query',
                type: 'ioc_search',
                parameters: {
                    timeRange: {
                        from: new Date('2023-01-01'),
                        to: new Date('2023-01-02'),
                    },
                    criteria: {},
                    iocs: [
                        {
                            type: 'ip',
                            value: '*************',
                            confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                        },
                        {
                            type: 'domain',
                            value: 'malicious.example.com',
                            confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                        },
                    ],
                },
                priority: 'high',
                expectedResults: {
                    estimatedMatches: 10,
                    confidenceThreshold: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                    falsePositiveRate: 0.1,
                },
            };
            const mockEvents = [
                createMockSecurityEvent({
                    id: 'event-1',
                    sourceIP: '*************',
                    severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                }),
                createMockSecurityEvent({
                    id: 'event-2',
                    domain: 'malicious.example.com',
                    severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                }),
            ];
            mockSecurityEventRepository.findByIOC.mockResolvedValueOnce([mockEvents[0]]);
            mockSecurityEventRepository.findByIOC.mockResolvedValueOnce([mockEvents[1]]);
            const result = await service.executeHunt(query);
            expect(result.execution.status).toBe('completed');
            expect(result.results.totalMatches).toBe(2);
            expect(result.findings).toHaveLength(2);
            expect(result.findings[0].type).toBe('ioc_match');
            expect(result.findings[0].confidence).toBe(confidence_level_enum_1.ConfidenceLevel.HIGH);
            expect(result.analysis.threatAssessment.overallThreatLevel).toBe('high');
            expect(mockSecurityEventRepository.findByIOC).toHaveBeenCalledTimes(2);
        });
        it('should execute behavioral analysis hunt', async () => {
            const query = {
                name: 'Behavioral Analysis Hunt',
                description: 'Test behavioral analysis',
                type: 'behavioral_analysis',
                parameters: {
                    timeRange: {
                        from: new Date('2023-01-01'),
                        to: new Date('2023-01-02'),
                    },
                    criteria: {},
                    patterns: [
                        {
                            pattern: 'unusual_login_times',
                            threshold: 3,
                            timeWindow: 3600,
                        },
                    ],
                },
                priority: 'medium',
                expectedResults: {
                    estimatedMatches: 5,
                    confidenceThreshold: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                    falsePositiveRate: 0.2,
                },
            };
            const result = await service.executeHunt(query);
            expect(result.execution.status).toBe('completed');
            expect(result.query.type).toBe('behavioral_analysis');
            expect(result.analysis.threatAssessment).toBeDefined();
            expect(result.quality.dataCoverage).toBeGreaterThanOrEqual(0);
        });
        it('should execute anomaly detection hunt', async () => {
            const query = {
                name: 'Anomaly Detection Hunt',
                description: 'Test anomaly detection',
                type: 'anomaly_detection',
                parameters: {
                    timeRange: {
                        from: new Date('2023-01-01'),
                        to: new Date('2023-01-02'),
                    },
                    criteria: {
                        anomalyThreshold: 2.5,
                        baselineWindow: 7,
                    },
                },
                priority: 'low',
                expectedResults: {
                    estimatedMatches: 3,
                    confidenceThreshold: confidence_level_enum_1.ConfidenceLevel.LOW,
                    falsePositiveRate: 0.3,
                },
            };
            const result = await service.executeHunt(query);
            expect(result.execution.status).toBe('completed');
            expect(result.query.type).toBe('anomaly_detection');
            expect(result.execution.duration).toBeGreaterThan(0);
        });
        it('should execute pattern matching hunt', async () => {
            const query = {
                name: 'Pattern Matching Hunt',
                description: 'Test pattern matching',
                type: 'pattern_matching',
                parameters: {
                    timeRange: {
                        from: new Date('2023-01-01'),
                        to: new Date('2023-01-02'),
                    },
                    criteria: {
                        patterns: ['powershell.*encoded', 'cmd.*bypass'],
                    },
                },
                priority: 'high',
                expectedResults: {
                    estimatedMatches: 8,
                    confidenceThreshold: confidence_level_enum_1.ConfidenceLevel.HIGH,
                    falsePositiveRate: 0.15,
                },
            };
            const result = await service.executeHunt(query);
            expect(result.execution.status).toBe('completed');
            expect(result.query.type).toBe('pattern_matching');
            expect(result.execution.progress).toBe(100);
        });
        it('should execute timeline analysis hunt', async () => {
            const query = {
                name: 'Timeline Analysis Hunt',
                description: 'Test timeline analysis',
                type: 'timeline_analysis',
                parameters: {
                    timeRange: {
                        from: new Date('2023-01-01'),
                        to: new Date('2023-01-02'),
                    },
                    criteria: {
                        timelinePatterns: ['rapid_succession', 'coordinated_activity'],
                    },
                },
                priority: 'critical',
                expectedResults: {
                    estimatedMatches: 2,
                    confidenceThreshold: confidence_level_enum_1.ConfidenceLevel.HIGH,
                    falsePositiveRate: 0.05,
                },
            };
            const result = await service.executeHunt(query);
            expect(result.execution.status).toBe('completed');
            expect(result.query.type).toBe('timeline_analysis');
            expect(result.analysis.attackTimeline).toBeDefined();
        });
        it('should handle hunt execution errors gracefully', async () => {
            const query = {
                name: 'Failing Hunt',
                description: 'Test error handling',
                type: 'ioc_search',
                parameters: {
                    timeRange: {
                        from: new Date('2023-01-01'),
                        to: new Date('2023-01-02'),
                    },
                    criteria: {},
                    iocs: [
                        {
                            type: 'ip',
                            value: '*************',
                            confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                        },
                    ],
                },
                priority: 'high',
                expectedResults: {
                    estimatedMatches: 10,
                    confidenceThreshold: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                    falsePositiveRate: 0.1,
                },
            };
            mockSecurityEventRepository.findByIOC.mockRejectedValue(new Error('Database error'));
            const result = await service.executeHunt(query);
            expect(result.execution.status).toBe('failed');
            expect(result.execution.progress).toBe(0);
            expect(result.execution.duration).toBeGreaterThan(0);
        });
        it('should handle unsupported hunt types', async () => {
            const query = {
                name: 'Unsupported Hunt',
                description: 'Test unsupported type',
                type: 'unsupported_type',
                parameters: {
                    timeRange: {
                        from: new Date('2023-01-01'),
                        to: new Date('2023-01-02'),
                    },
                    criteria: {},
                },
                priority: 'low',
                expectedResults: {
                    estimatedMatches: 0,
                    confidenceThreshold: confidence_level_enum_1.ConfidenceLevel.LOW,
                    falsePositiveRate: 0,
                },
            };
            const result = await service.executeHunt(query);
            expect(result.execution.status).toBe('failed');
        });
        it('should calculate quality metrics correctly', async () => {
            const query = {
                name: 'Quality Test Hunt',
                description: 'Test quality metrics calculation',
                type: 'ioc_search',
                parameters: {
                    timeRange: {
                        from: new Date('2023-01-01'),
                        to: new Date('2023-01-02'),
                    },
                    criteria: {},
                    iocs: [
                        {
                            type: 'ip',
                            value: '*************',
                            confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                        },
                    ],
                },
                priority: 'medium',
                expectedResults: {
                    estimatedMatches: 5,
                    confidenceThreshold: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                    falsePositiveRate: 0.1,
                },
            };
            const mockEvent = createMockSecurityEvent({
                id: 'event-1',
                sourceIP: '*************',
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
            });
            mockSecurityEventRepository.findByIOC.mockResolvedValue([mockEvent]);
            const result = await service.executeHunt(query);
            expect(result.quality.dataCoverage).toBeGreaterThan(0);
            expect(result.quality.searchCompleteness).toBe(95);
            expect(result.quality.resultConfidence).toBeGreaterThan(0);
            expect(result.quality.falsePositiveLikelihood).toBeGreaterThanOrEqual(0);
        });
        it('should build attack timeline correctly', async () => {
            const query = {
                name: 'Timeline Test Hunt',
                description: 'Test attack timeline building',
                type: 'ioc_search',
                parameters: {
                    timeRange: {
                        from: new Date('2023-01-01'),
                        to: new Date('2023-01-02'),
                    },
                    criteria: {},
                    iocs: [
                        {
                            type: 'ip',
                            value: '*************',
                            confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                        },
                    ],
                },
                priority: 'high',
                expectedResults: {
                    estimatedMatches: 1,
                    confidenceThreshold: confidence_level_enum_1.ConfidenceLevel.HIGH,
                    falsePositiveRate: 0.05,
                },
            };
            const mockEvent = createMockSecurityEvent({
                id: 'event-1',
                sourceIP: '*************',
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                timestamp: new Date('2023-01-01T10:00:00Z'),
            });
            mockSecurityEventRepository.findByIOC.mockResolvedValue([mockEvent]);
            const result = await service.executeHunt(query);
            expect(result.analysis.attackTimeline).toBeDefined();
            expect(result.analysis.attackTimeline.length).toBeGreaterThan(0);
            expect(result.analysis.recommendations.immediate).toBeDefined();
            expect(result.analysis.recommendations.shortTerm).toBeDefined();
            expect(result.analysis.recommendations.longTerm).toBeDefined();
            expect(result.analysis.recommendations.preventive).toBeDefined();
        });
    });
    describe('getPredefinedQueries', () => {
        it('should return predefined hunting queries', async () => {
            const queries = await service.getPredefinedQueries();
            expect(queries).toHaveLength(2);
            expect(queries[0].name).toBe('Suspicious PowerShell Activity');
            expect(queries[0].type).toBe('pattern_matching');
            expect(queries[0].priority).toBe('high');
            expect(queries[1].name).toBe('Lateral Movement Detection');
            expect(queries[1].type).toBe('behavioral_analysis');
            expect(queries[1].priority).toBe('critical');
        });
        it('should return queries with valid time ranges', async () => {
            const queries = await service.getPredefinedQueries();
            queries.forEach(query => {
                expect(query.parameters.timeRange.from).toBeInstanceOf(Date);
                expect(query.parameters.timeRange.to).toBeInstanceOf(Date);
                expect(query.parameters.timeRange.from.getTime()).toBeLessThan(query.parameters.timeRange.to.getTime());
            });
        });
        it('should return queries with expected results', async () => {
            const queries = await service.getPredefinedQueries();
            queries.forEach(query => {
                expect(query.expectedResults.estimatedMatches).toBeGreaterThan(0);
                expect(Object.values(confidence_level_enum_1.ConfidenceLevel)).toContain(query.expectedResults.confidenceThreshold);
                expect(query.expectedResults.falsePositiveRate).toBeGreaterThanOrEqual(0);
                expect(query.expectedResults.falsePositiveRate).toBeLessThanOrEqual(1);
            });
        });
    });
    describe('performance', () => {
        it('should complete hunt within reasonable time', async () => {
            const query = {
                name: 'Performance Test Hunt',
                description: 'Test hunt performance',
                type: 'ioc_search',
                parameters: {
                    timeRange: {
                        from: new Date('2023-01-01'),
                        to: new Date('2023-01-02'),
                    },
                    criteria: {},
                    iocs: [
                        {
                            type: 'ip',
                            value: '*************',
                            confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                        },
                    ],
                },
                priority: 'medium',
                expectedResults: {
                    estimatedMatches: 1,
                    confidenceThreshold: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                    falsePositiveRate: 0.1,
                },
            };
            mockSecurityEventRepository.findByIOC.mockResolvedValue([]);
            const startTime = Date.now();
            const result = await service.executeHunt(query);
            const duration = Date.now() - startTime;
            expect(result.execution.status).toBe('completed');
            expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
            expect(result.execution.duration).toBeLessThan(5000);
        });
    });
    // Helper function to create mock security events
    function createMockSecurityEvent(options) {
        return {
            id: { toString: () => options.id },
            source: { name: 'test-source' },
            timestamp: { value: options.timestamp || new Date() },
            severity: options.severity,
            rawData: {
                sourceIP: options.sourceIP,
                domain: options.domain,
            },
            affectedAssets: [
                {
                    assetId: 'asset-1',
                    assetName: 'Test Asset',
                },
            ],
        };
    }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************