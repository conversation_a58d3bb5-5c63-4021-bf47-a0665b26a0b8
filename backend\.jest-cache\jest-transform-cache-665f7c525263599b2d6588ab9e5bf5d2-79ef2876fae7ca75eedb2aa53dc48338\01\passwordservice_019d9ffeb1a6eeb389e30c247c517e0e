e60d49f5e1fc26bc55158099354301a2
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PasswordService_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PasswordService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const bcrypt = __importStar(require("bcrypt"));
/**
 * Password service for hashing and validating passwords
 * Provides secure password operations using bcrypt
 */
let PasswordService = PasswordService_1 = class PasswordService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(PasswordService_1.name);
        const authConfig = this.configService.get('auth');
        this.saltRounds = authConfig.bcrypt.rounds;
    }
    /**
     * Hash a plain text password
     * @param password Plain text password
     * @returns Promise<string> Hashed password
     */
    async hashPassword(password) {
        try {
            this.logger.debug('Hashing password', {
                passwordLength: password.length,
                saltRounds: this.saltRounds,
            });
            const hash = await bcrypt.hash(password, this.saltRounds);
            this.logger.debug('Password hashed successfully', {
                hashLength: hash.length,
            });
            return hash;
        }
        catch (error) {
            this.logger.error('Error hashing password', {
                error: error.message,
                passwordLength: password.length,
            });
            throw new Error('Failed to hash password');
        }
    }
    /**
     * Validate a password against its hash
     * @param password Plain text password
     * @param hash Hashed password
     * @returns Promise<boolean> True if password is valid
     */
    async validatePassword(password, hash) {
        try {
            this.logger.debug('Validating password', {
                passwordLength: password.length,
                hashLength: hash.length,
            });
            const isValid = await bcrypt.compare(password, hash);
            this.logger.debug('Password validation result', {
                isValid,
            });
            return isValid;
        }
        catch (error) {
            this.logger.error('Error validating password', {
                error: error.message,
                passwordLength: password.length,
                hashLength: hash.length,
            });
            return false;
        }
    }
    /**
     * Validate password strength
     * @param password Plain text password
     * @returns Object containing validation result and details
     */
    validatePasswordStrength(password) {
        const errors = [];
        let score = 0;
        const authConfig = this.configService.get('auth');
        const requirements = authConfig.security;
        // Check minimum length
        if (password.length < requirements.passwordMinLength) {
            errors.push(`Password must be at least ${requirements.passwordMinLength} characters long`);
        }
        else {
            score += 1;
        }
        // Check for uppercase letters
        if (requirements.passwordRequireUppercase && !/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        else if (/[A-Z]/.test(password)) {
            score += 1;
        }
        // Check for lowercase letters
        if (requirements.passwordRequireLowercase && !/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        else if (/[a-z]/.test(password)) {
            score += 1;
        }
        // Check for numbers
        if (requirements.passwordRequireNumbers && !/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }
        else if (/\d/.test(password)) {
            score += 1;
        }
        // Check for symbols
        if (requirements.passwordRequireSymbols && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }
        else if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            score += 1;
        }
        // Additional strength checks
        if (password.length >= 12) {
            score += 1;
        }
        if (password.length >= 16) {
            score += 1;
        }
        // Check for common patterns
        const commonPatterns = [
            /123456/,
            /password/i,
            /qwerty/i,
            /admin/i,
            /letmein/i,
        ];
        const hasCommonPattern = commonPatterns.some(pattern => pattern.test(password));
        if (hasCommonPattern) {
            errors.push('Password contains common patterns and is not secure');
            score = Math.max(0, score - 2);
        }
        // Check for repeated characters
        if (/(.)\1{2,}/.test(password)) {
            errors.push('Password should not contain repeated characters');
            score = Math.max(0, score - 1);
        }
        const isValid = errors.length === 0;
        this.logger.debug('Password strength validation', {
            passwordLength: password.length,
            isValid,
            score,
            errorCount: errors.length,
        });
        return {
            isValid,
            errors,
            score: Math.min(score, 5), // Cap score at 5
        };
    }
    /**
     * Generate a secure random password
     * @param length Password length (default: 16)
     * @param includeSymbols Include special characters (default: true)
     * @returns string Generated password
     */
    generateSecurePassword(length = 16, includeSymbols = true) {
        const lowercase = 'abcdefghijklmnopqrstuvwxyz';
        const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const numbers = '0123456789';
        const symbols = '!@#$%^&*(),.?":{}|<>';
        let charset = lowercase + uppercase + numbers;
        if (includeSymbols) {
            charset += symbols;
        }
        let password = '';
        // Ensure at least one character from each required set
        password += lowercase[Math.floor(Math.random() * lowercase.length)];
        password += uppercase[Math.floor(Math.random() * uppercase.length)];
        password += numbers[Math.floor(Math.random() * numbers.length)];
        if (includeSymbols) {
            password += symbols[Math.floor(Math.random() * symbols.length)];
        }
        // Fill the rest randomly
        for (let i = password.length; i < length; i++) {
            password += charset[Math.floor(Math.random() * charset.length)];
        }
        // Shuffle the password
        const passwordArray = password.split('');
        for (let i = passwordArray.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [passwordArray[i], passwordArray[j]] = [passwordArray[j], passwordArray[i]];
        }
        const generatedPassword = passwordArray.join('');
        this.logger.debug('Secure password generated', {
            length: generatedPassword.length,
            includeSymbols,
        });
        return generatedPassword;
    }
    /**
     * Check if password needs to be rehashed (due to changed salt rounds)
     * @param hash Existing password hash
     * @returns boolean True if rehashing is needed
     */
    needsRehash(hash) {
        try {
            const currentRounds = bcrypt.getRounds(hash);
            const needsRehash = currentRounds !== this.saltRounds;
            this.logger.debug('Password rehash check', {
                currentRounds,
                configuredRounds: this.saltRounds,
                needsRehash,
            });
            return needsRehash;
        }
        catch (error) {
            this.logger.error('Error checking if password needs rehash', {
                error: error.message,
            });
            return false;
        }
    }
};
exports.PasswordService = PasswordService;
exports.PasswordService = PasswordService = PasswordService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], PasswordService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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