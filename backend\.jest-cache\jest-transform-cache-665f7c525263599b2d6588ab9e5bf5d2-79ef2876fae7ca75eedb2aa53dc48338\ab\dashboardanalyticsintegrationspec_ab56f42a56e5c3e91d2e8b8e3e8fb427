75c1ac0284b34bde574b086404e9eb26
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const event_emitter_1 = require("@nestjs/event-emitter");
const bull_1 = require("@nestjs/bull");
const request = __importStar(require("supertest"));
const typeorm_2 = require("@nestjs/typeorm");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const notification_dashboard_controller_1 = require("../../controllers/notification-dashboard.controller");
const advanced_analytics_controller_1 = require("../../controllers/advanced-analytics.controller");
const notification_dashboard_service_1 = require("../../services/notification-dashboard.service");
const advanced_analytics_service_1 = require("../../services/advanced-analytics.service");
const notification_analytics_service_1 = require("../../services/notification-analytics.service");
const provider_health_monitoring_service_1 = require("../../services/provider-health-monitoring.service");
const notification_cost_management_service_1 = require("../../services/notification-cost-management.service");
const notification_analytics_event_entity_1 = require("../../entities/notification-analytics-event.entity");
const provider_health_metric_entity_1 = require("../../entities/provider-health-metric.entity");
const notification_cost_event_entity_1 = require("../../entities/notification-cost-event.entity");
/**
 * Dashboard and Analytics API Integration Tests
 *
 * Comprehensive integration tests for dashboard and analytics APIs including:
 * - Real-time dashboard data flow validation with WebSocket integration
 * - Advanced analytics API testing with complex query scenarios
 * - Data aggregation and visualization endpoint validation
 * - Performance monitoring and optimization testing
 * - Cross-module data integration and consistency validation
 * - Authentication and authorization testing for analytics endpoints
 */
describe('Dashboard and Analytics API Integration Tests', () => {
    let app;
    let dashboardService;
    let analyticsService;
    let notificationAnalyticsService;
    let healthService;
    let costService;
    let analyticsEventRepository;
    let healthMetricRepository;
    let costEventRepository;
    const testUser = {
        id: 'test-user-123',
        email: '<EMAIL>',
        role: 'admin',
        team: 'analytics',
    };
    const authToken = 'test-jwt-token';
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                typeorm_1.TypeOrmModule.forRoot({
                    type: 'postgres',
                    host: process.env.TEST_DB_HOST || 'localhost',
                    port: parseInt(process.env.TEST_DB_PORT) || 5433,
                    username: process.env.TEST_DB_USERNAME || 'test',
                    password: process.env.TEST_DB_PASSWORD || 'test',
                    database: process.env.TEST_DB_NAME || 'sentinel_test',
                    entities: [
                        notification_analytics_event_entity_1.NotificationAnalyticsEvent,
                        provider_health_metric_entity_1.ProviderHealthMetric,
                        notification_cost_event_entity_1.NotificationCostEvent,
                    ],
                    synchronize: true,
                    dropSchema: true,
                }),
                typeorm_1.TypeOrmModule.forFeature([
                    notification_analytics_event_entity_1.NotificationAnalyticsEvent,
                    provider_health_metric_entity_1.ProviderHealthMetric,
                    notification_cost_event_entity_1.NotificationCostEvent,
                ]),
                event_emitter_1.EventEmitterModule.forRoot(),
                bull_1.BullModule.forRoot({
                    redis: {
                        host: process.env.TEST_REDIS_HOST || 'localhost',
                        port: parseInt(process.env.TEST_REDIS_PORT) || 6380,
                        db: 1,
                    },
                }),
                passport_1.PassportModule.register({ defaultStrategy: 'jwt' }),
                jwt_1.JwtModule.register({
                    secret: 'test-secret',
                    signOptions: { expiresIn: '1h' },
                }),
            ],
            controllers: [
                notification_dashboard_controller_1.NotificationDashboardController,
                advanced_analytics_controller_1.AdvancedAnalyticsController,
            ],
            providers: [
                notification_dashboard_service_1.NotificationDashboardService,
                advanced_analytics_service_1.AdvancedAnalyticsService,
                notification_analytics_service_1.NotificationAnalyticsService,
                provider_health_monitoring_service_1.ProviderHealthMonitoringService,
                notification_cost_management_service_1.NotificationCostManagementService,
                // Mock authentication guard
                {
                    provide: 'JwtAuthGuard',
                    useValue: {
                        canActivate: jest.fn().mockReturnValue(true),
                    },
                },
                {
                    provide: 'RolesGuard',
                    useValue: {
                        canActivate: jest.fn().mockReturnValue(true),
                    },
                },
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
        // Get service instances
        dashboardService = moduleFixture.get(notification_dashboard_service_1.NotificationDashboardService);
        analyticsService = moduleFixture.get(advanced_analytics_service_1.AdvancedAnalyticsService);
        notificationAnalyticsService = moduleFixture.get(notification_analytics_service_1.NotificationAnalyticsService);
        healthService = moduleFixture.get(provider_health_monitoring_service_1.ProviderHealthMonitoringService);
        costService = moduleFixture.get(notification_cost_management_service_1.NotificationCostManagementService);
        // Get repository instances
        analyticsEventRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(notification_analytics_event_entity_1.NotificationAnalyticsEvent));
        healthMetricRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(provider_health_metric_entity_1.ProviderHealthMetric));
        costEventRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(notification_cost_event_entity_1.NotificationCostEvent));
        // Seed test data
        await seedTestData();
    });
    afterAll(async () => {
        await app.close();
    });
    beforeEach(async () => {
        // Reset any test-specific data
    });
    describe('Dashboard API Endpoints', () => {
        describe('GET /dashboard/overview', () => {
            it('should return comprehensive dashboard overview', async () => {
                const response = await request(app.getHttpServer())
                    .get('/dashboard/overview')
                    .set('Authorization', `Bearer ${authToken}`)
                    .query({
                    timeRange: '24h',
                    includeComparison: true,
                    includeTrends: true,
                })
                    .expect(200);
                expect(response.body).toBeDefined();
                expect(response.body.summary).toBeDefined();
                expect(response.body.summary.totalNotifications).toBeDefined();
                expect(response.body.summary.deliveryRate).toBeDefined();
                expect(response.body.summary.avgDeliveryTime).toBeDefined();
                expect(response.body.providers).toBeDefined();
                expect(Array.isArray(response.body.providers)).toBe(true);
                expect(response.body.channels).toBeDefined();
                expect(Array.isArray(response.body.channels)).toBe(true);
                expect(response.body.trends).toBeDefined();
                expect(response.body.comparison).toBeDefined();
            });
            it('should handle different time ranges', async () => {
                const timeRanges = ['1h', '6h', '24h', '7d', '30d'];
                for (const timeRange of timeRanges) {
                    const response = await request(app.getHttpServer())
                        .get('/dashboard/overview')
                        .set('Authorization', `Bearer ${authToken}`)
                        .query({ timeRange })
                        .expect(200);
                    expect(response.body.timeRange).toBe(timeRange);
                    expect(response.body.summary).toBeDefined();
                }
            });
            it('should filter by providers and channels', async () => {
                const response = await request(app.getHttpServer())
                    .get('/dashboard/overview')
                    .set('Authorization', `Bearer ${authToken}`)
                    .query({
                    providers: 'email,slack',
                    channels: 'email,slack',
                    timeRange: '24h',
                })
                    .expect(200);
                expect(response.body.filters).toBeDefined();
                expect(response.body.filters.providers).toEqual(['email', 'slack']);
                expect(response.body.filters.channels).toEqual(['email', 'slack']);
            });
        });
        describe('GET /dashboard/real-time', () => {
            it('should return real-time dashboard metrics', async () => {
                const response = await request(app.getHttpServer())
                    .get('/dashboard/real-time')
                    .set('Authorization', `Bearer ${authToken}`)
                    .query({
                    metrics: 'delivery,cost,health',
                    refreshInterval: 30,
                })
                    .expect(200);
                expect(response.body).toBeDefined();
                expect(response.body.metrics).toBeDefined();
                expect(response.body.refreshInterval).toBe(30);
                expect(response.body.lastUpdated).toBeDefined();
                expect(response.body.delivery).toBeDefined();
                expect(response.body.cost).toBeDefined();
                expect(response.body.health).toBeDefined();
            });
            it('should provide WebSocket endpoint for live updates', async () => {
                const response = await request(app.getHttpServer())
                    .get('/dashboard/real-time')
                    .set('Authorization', `Bearer ${authToken}`)
                    .query({ enableWebSocket: true })
                    .expect(200);
                expect(response.body.websocketUrl).toBeDefined();
                expect(response.body.websocketUrl).toContain('ws://');
            });
        });
        describe('GET /dashboard/health', () => {
            it('should return provider health dashboard', async () => {
                const response = await request(app.getHttpServer())
                    .get('/dashboard/health')
                    .set('Authorization', `Bearer ${authToken}`)
                    .query({
                    includeHistory: true,
                    timeRange: '24h',
                })
                    .expect(200);
                expect(response.body).toBeDefined();
                expect(response.body.summary).toBeDefined();
                expect(response.body.summary.totalProviders).toBeDefined();
                expect(response.body.summary.healthyProviders).toBeDefined();
                expect(response.body.summary.avgResponseTime).toBeDefined();
                expect(response.body.providers).toBeDefined();
                expect(Array.isArray(response.body.providers)).toBe(true);
                response.body.providers.forEach(provider => {
                    expect(provider.name).toBeDefined();
                    expect(provider.status).toBeDefined();
                    expect(provider.responseTime).toBeDefined();
                    expect(provider.uptime).toBeDefined();
                });
                expect(response.body.history).toBeDefined();
            });
            it('should show circuit breaker status', async () => {
                const response = await request(app.getHttpServer())
                    .get('/dashboard/health')
                    .set('Authorization', `Bearer ${authToken}`)
                    .query({ includeCircuitBreakers: true })
                    .expect(200);
                expect(response.body.circuitBreakers).toBeDefined();
                expect(Array.isArray(response.body.circuitBreakers)).toBe(true);
                response.body.circuitBreakers.forEach(cb => {
                    expect(cb.provider).toBeDefined();
                    expect(cb.state).toBeDefined();
                    expect(['closed', 'open', 'half-open'].includes(cb.state)).toBe(true);
                });
            });
        });
        describe('GET /dashboard/cost', () => {
            it('should return cost analytics dashboard', async () => {
                const response = await request(app.getHttpServer())
                    .get('/dashboard/cost')
                    .set('Authorization', `Bearer ${authToken}`)
                    .query({
                    timeRange: '30d',
                    includeOptimization: true,
                    groupBy: 'provider',
                })
                    .expect(200);
                expect(response.body).toBeDefined();
                expect(response.body.summary).toBeDefined();
                expect(response.body.summary.totalCost).toBeDefined();
                expect(response.body.summary.avgCostPerNotification).toBeDefined();
                expect(response.body.breakdown).toBeDefined();
                expect(Array.isArray(response.body.breakdown)).toBe(true);
                expect(response.body.trends).toBeDefined();
                expect(response.body.optimization).toBeDefined();
                expect(response.body.optimization.recommendations).toBeDefined();
            });
            it('should provide budget tracking', async () => {
                const response = await request(app.getHttpServer())
                    .get('/dashboard/cost')
                    .set('Authorization', `Bearer ${authToken}`)
                    .query({
                    includeBudget: true,
                    budgetPeriod: 'monthly',
                })
                    .expect(200);
                expect(response.body.budget).toBeDefined();
                expect(response.body.budget.allocated).toBeDefined();
                expect(response.body.budget.spent).toBeDefined();
                expect(response.body.budget.remaining).toBeDefined();
                expect(response.body.budget.utilizationRate).toBeDefined();
            });
        });
    });
    describe('Advanced Analytics API Endpoints', () => {
        describe('GET /advanced-analytics/multi-dimensional', () => {
            it('should perform multi-dimensional analytics', async () => {
                const response = await request(app.getHttpServer())
                    .get('/advanced-analytics/multi-dimensional')
                    .set('Authorization', `Bearer ${authToken}`)
                    .query({
                    dimensions: 'provider,channel,status',
                    metrics: 'count,avg_delivery_time,success_rate',
                    timeRange: '7d',
                    groupBy: 'provider',
                })
                    .expect(200);
                expect(response.body).toBeDefined();
                expect(response.body.dimensions).toEqual(['provider', 'channel', 'status']);
                expect(response.body.metrics).toEqual(['count', 'avg_delivery_time', 'success_rate']);
                expect(response.body.data).toBeDefined();
                expect(Array.isArray(response.body.data)).toBe(true);
                expect(response.body.aggregations).toBeDefined();
                expect(response.body.correlations).toBeDefined();
                expect(response.body.insights).toBeDefined();
            });
            it('should handle complex filtering', async () => {
                const filters = JSON.stringify({
                    provider: 'email',
                    status: 'delivered',
                    'alert.severity': 'critical',
                });
                const response = await request(app.getHttpServer())
                    .get('/advanced-analytics/multi-dimensional')
                    .set('Authorization', `Bearer ${authToken}`)
                    .query({
                    dimensions: 'provider,channel',
                    metrics: 'count,success_rate',
                    filters,
                })
                    .expect(200);
                expect(response.body.data).toBeDefined();
                // Verify filtering was applied
                response.body.data.forEach(item => {
                    expect(item.provider).toBe('email');
                });
            });
        });
        describe('POST /advanced-analytics/predictive', () => {
            it('should generate predictive analytics', async () => {
                const predictiveQuery = {
                    target: 'delivery_rate',
                    predictors: ['provider', 'channel', 'time_of_day'],
                    algorithm: 'random_forest',
                    historicalRange: '90d',
                    forecastPeriod: '30d',
                    includeConfidenceIntervals: true,
                    includeTrends: true,
                };
                const response = await request(app.getHttpServer())
                    .post('/advanced-analytics/predictive')
                    .set('Authorization', `Bearer ${authToken}`)
                    .send(predictiveQuery)
                    .expect(200);
                expect(response.body).toBeDefined();
                expect(response.body.forecast).toBeDefined();
                expect(response.body.forecast.predictions).toBeDefined();
                expect(response.body.forecast.confidence).toBeDefined();
                expect(response.body.forecast.accuracy).toBeDefined();
                expect(response.body.trends).toBeDefined();
                expect(response.body.anomalies).toBeDefined();
                expect(response.body.recommendations).toBeDefined();
            });
            it('should handle different ML algorithms', async () => {
                const algorithms = ['linear_regression', 'random_forest', 'neural_network', 'arima'];
                for (const algorithm of algorithms) {
                    const query = {
                        target: 'cost',
                        algorithm,
                        historicalRange: '30d',
                        forecastPeriod: '7d',
                    };
                    const response = await request(app.getHttpServer())
                        .post('/advanced-analytics/predictive')
                        .set('Authorization', `Bearer ${authToken}`)
                        .send(query)
                        .expect(200);
                    expect(response.body.forecast.model).toBe(algorithm);
                }
            });
        });
        describe('POST /advanced-analytics/custom-query', () => {
            it('should execute custom SQL-like queries', async () => {
                const customQuery = {
                    queryType: 'sql',
                    query: {
                        sql: 'SELECT provider, COUNT(*) as total FROM notifications WHERE created_at > NOW() - INTERVAL 7 DAY GROUP BY provider',
                    },
                    visualization: {
                        type: 'bar_chart',
                        title: 'Notifications by Provider (Last 7 Days)',
                        xAxis: 'provider',
                        yAxis: 'total',
                    },
                };
                const response = await request(app.getHttpServer())
                    .post('/advanced-analytics/custom-query')
                    .set('Authorization', `Bearer ${authToken}`)
                    .send(customQuery)
                    .expect(200);
                expect(response.body).toBeDefined();
                expect(response.body.query).toBeDefined();
                expect(response.body.results).toBeDefined();
                expect(Array.isArray(response.body.results)).toBe(true);
                expect(response.body.visualization).toBeDefined();
                expect(response.body.performance).toBeDefined();
            });
            it('should support visual query builder', async () => {
                const visualQuery = {
                    queryType: 'visual_builder',
                    query: {
                        dataSource: 'notifications',
                        dimensions: ['provider', 'channel'],
                        metrics: ['count', 'avg_delivery_time'],
                        filters: [
                            { field: 'created_at', operator: 'gte', value: '7d' },
                            { field: 'status', operator: 'eq', value: 'delivered' },
                        ],
                    },
                    visualization: {
                        type: 'line_chart',
                        title: 'Delivery Performance Trends',
                    },
                };
                const response = await request(app.getHttpServer())
                    .post('/advanced-analytics/custom-query')
                    .set('Authorization', `Bearer ${authToken}`)
                    .send(visualQuery)
                    .expect(200);
                expect(response.body.results).toBeDefined();
                expect(response.body.visualization.type).toBe('line_chart');
            });
        });
        describe('GET /advanced-analytics/correlation', () => {
            it('should analyze correlations between metrics', async () => {
                const response = await request(app.getHttpServer())
                    .get('/advanced-analytics/correlation')
                    .set('Authorization', `Bearer ${authToken}`)
                    .query({
                    variables: 'delivery_time,cost,success_rate,response_time',
                    timeRange: '30d',
                    method: 'pearson',
                })
                    .expect(200);
                expect(response.body).toBeDefined();
                expect(response.body.correlationMatrix).toBeDefined();
                expect(response.body.significantCorrelations).toBeDefined();
                expect(Array.isArray(response.body.significantCorrelations)).toBe(true);
                expect(response.body.insights).toBeDefined();
                expect(response.body.visualization).toBeDefined();
            });
            it('should support different correlation methods', async () => {
                const methods = ['pearson', 'spearman', 'kendall'];
                for (const method of methods) {
                    const response = await request(app.getHttpServer())
                        .get('/advanced-analytics/correlation')
                        .set('Authorization', `Bearer ${authToken}`)
                        .query({
                        variables: 'delivery_time,cost',
                        method,
                    })
                        .expect(200);
                    expect(response.body.correlationMatrix).toBeDefined();
                }
            });
        });
        describe('GET /advanced-analytics/anomalies', () => {
            it('should detect anomalies in notification metrics', async () => {
                const response = await request(app.getHttpServer())
                    .get('/advanced-analytics/anomalies')
                    .set('Authorization', `Bearer ${authToken}`)
                    .query({
                    metrics: 'delivery_time,cost,error_rate',
                    timeRange: '7d',
                    sensitivity: 'medium',
                    algorithm: 'isolation_forest',
                })
                    .expect(200);
                expect(response.body).toBeDefined();
                expect(response.body.anomalies).toBeDefined();
                expect(Array.isArray(response.body.anomalies)).toBe(true);
                expect(response.body.patterns).toBeDefined();
                expect(response.body.severity).toBeDefined();
                expect(response.body.recommendations).toBeDefined();
            });
            it('should handle different sensitivity levels', async () => {
                const sensitivities = ['low', 'medium', 'high'];
                for (const sensitivity of sensitivities) {
                    const response = await request(app.getHttpServer())
                        .get('/advanced-analytics/anomalies')
                        .set('Authorization', `Bearer ${authToken}`)
                        .query({
                        metrics: 'delivery_time',
                        sensitivity,
                    })
                        .expect(200);
                    expect(response.body.anomalies).toBeDefined();
                }
            });
        });
        describe('GET /advanced-analytics/export/:format', () => {
            it('should export analytics data in different formats', async () => {
                const formats = ['csv', 'json', 'excel'];
                const query = Buffer.from(JSON.stringify({
                    type: 'summary',
                    timeRange: '7d',
                    metrics: ['count', 'delivery_rate'],
                })).toString('base64');
                for (const format of formats) {
                    const response = await request(app.getHttpServer())
                        .get(`/advanced-analytics/export/${format}`)
                        .set('Authorization', `Bearer ${authToken}`)
                        .query({ query })
                        .expect(200);
                    expect(response.body).toBeDefined();
                }
            });
        });
    });
    describe('Performance and Load Testing', () => {
        it('should handle concurrent dashboard requests', async () => {
            const concurrentRequests = 20;
            const promises = Array(concurrentRequests).fill(null).map(() => request(app.getHttpServer())
                .get('/dashboard/overview')
                .set('Authorization', `Bearer ${authToken}`)
                .query({ timeRange: '24h' }));
            const responses = await Promise.all(promises);
            responses.forEach(response => {
                expect(response.status).toBe(200);
                expect(response.body.summary).toBeDefined();
            });
        });
        it('should cache dashboard data for performance', async () => {
            const startTime = Date.now();
            // First request (should hit database)
            await request(app.getHttpServer())
                .get('/dashboard/overview')
                .set('Authorization', `Bearer ${authToken}`)
                .query({ timeRange: '24h' })
                .expect(200);
            const firstRequestTime = Date.now() - startTime;
            // Second request (should hit cache)
            const cacheStartTime = Date.now();
            await request(app.getHttpServer())
                .get('/dashboard/overview')
                .set('Authorization', `Bearer ${authToken}`)
                .query({ timeRange: '24h' })
                .expect(200);
            const secondRequestTime = Date.now() - cacheStartTime;
            // Cache should be faster
            expect(secondRequestTime).toBeLessThan(firstRequestTime);
        });
        it('should handle large dataset analytics queries', async () => {
            const largeQuery = {
                queryType: 'aggregation',
                query: {
                    collection: 'notifications',
                    pipeline: [
                        { $match: { created_at: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } } },
                        { $group: { _id: { provider: '$provider', day: { $dayOfYear: '$created_at' } }, count: { $sum: 1 } } },
                        { $sort: { '_id.day': 1 } },
                    ],
                },
                executionOptions: {
                    timeout: 60000,
                    maxRows: 10000,
                },
            };
            const startTime = Date.now();
            const response = await request(app.getHttpServer())
                .post('/advanced-analytics/custom-query')
                .set('Authorization', `Bearer ${authToken}`)
                .send(largeQuery)
                .expect(200);
            const executionTime = Date.now() - startTime;
            expect(response.body.results).toBeDefined();
            expect(response.body.performance.executionTime).toBeDefined();
            expect(executionTime).toBeLessThan(60000); // Should complete within timeout
        });
    });
    // Helper function to seed test data
    async function seedTestData() {
        // Create sample analytics events
        const analyticsEvents = Array(100).fill(null).map((_, i) => ({
            notificationId: `notification-${i}`,
            provider: ['email', 'sms', 'slack'][i % 3],
            channel: ['email', 'sms', 'slack'][i % 3],
            status: ['delivered', 'failed', 'pending'][i % 3],
            deliveryTime: Math.random() * 5000 + 1000,
            cost: Math.random() * 0.1 + 0.01,
            timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
            metadata: {
                alertId: `alert-${i}`,
                severity: ['low', 'medium', 'high', 'critical'][i % 4],
            },
        }));
        await analyticsEventRepository.save(analyticsEvents);
        // Create sample health metrics
        const healthMetrics = Array(50).fill(null).map((_, i) => ({
            provider: ['email', 'sms', 'slack'][i % 3],
            responseTime: Math.random() * 1000 + 100,
            uptime: Math.random() * 100,
            errorRate: Math.random() * 10,
            timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
            metadata: {
                region: 'us-east-1',
                environment: 'test',
            },
        }));
        await healthMetricRepository.save(healthMetrics);
        // Create sample cost events
        const costEvents = Array(75).fill(null).map((_, i) => ({
            notificationId: `notification-${i}`,
            provider: ['email', 'sms', 'slack'][i % 3],
            cost: Math.random() * 0.1 + 0.01,
            currency: 'USD',
            timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
            metadata: {
                region: 'us-east-1',
                tier: 'standard',
            },
        }));
        await costEventRepository.save(costEvents);
    }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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