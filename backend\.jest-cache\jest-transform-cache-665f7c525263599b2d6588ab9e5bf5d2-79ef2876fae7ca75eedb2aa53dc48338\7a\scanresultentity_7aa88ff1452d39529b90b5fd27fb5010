7612e08dd267f59028e6e88170a2487a
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScanResult = void 0;
const typeorm_1 = require("typeorm");
const asset_entity_1 = require("./asset.entity");
const vulnerability_entity_1 = require("./vulnerability.entity");
const vulnerability_scan_entity_1 = require("./vulnerability-scan.entity");
/**
 * Scan Result entity
 * Represents individual findings from vulnerability scans
 */
let ScanResult = class ScanResult {
    /**
     * Check if finding is critical
     */
    get isCritical() {
        return this.severity === 'critical';
    }
    /**
     * Check if finding is high severity
     */
    get isHighSeverity() {
        return ['critical', 'high'].includes(this.severity);
    }
    /**
     * Check if finding is new
     */
    get isNew() {
        return this.status === 'new';
    }
    /**
     * Check if finding is resolved
     */
    get isResolved() {
        return ['fixed', 'false_positive', 'risk_accepted'].includes(this.status);
    }
    /**
     * Get finding age in days
     */
    get ageInDays() {
        const now = new Date();
        const diffMs = now.getTime() - this.firstDetected.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Get days since last detection
     */
    get daysSinceLastDetection() {
        const now = new Date();
        const diffMs = now.getTime() - this.lastDetected.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Update last detected timestamp
     */
    updateLastDetected() {
        this.lastDetected = new Date();
        this.detectionCount += 1;
    }
    /**
     * Calculate risk score based on severity, exploitability, and other factors
     */
    calculateRiskScore() {
        const severityScores = {
            info: 0.1,
            low: 2.5,
            medium: 5.0,
            high: 7.5,
            critical: 9.5,
        };
        let baseScore = this.cvssScore || severityScores[this.severity];
        // Apply exploit availability multiplier
        if (this.exploitAvailable) {
            baseScore *= 1.3;
        }
        // Apply confidence factor
        if (this.confidence) {
            baseScore *= (this.confidence / 100);
        }
        // Apply false positive likelihood reduction
        if (this.falsePositiveLikelihood) {
            baseScore *= (1 - this.falsePositiveLikelihood);
        }
        this.riskScore = Math.min(baseScore, 10);
        this.riskScore = Math.round(this.riskScore * 10) / 10; // Round to 1 decimal
    }
    /**
     * Mark finding as false positive
     */
    markAsFalsePositive() {
        this.status = 'false_positive';
    }
    /**
     * Mark finding as fixed
     */
    markAsFixed() {
        this.status = 'fixed';
    }
    /**
     * Mark finding as risk accepted
     */
    markAsRiskAccepted() {
        this.status = 'risk_accepted';
    }
    /**
     * Link to vulnerability
     */
    linkToVulnerability(vulnerabilityId) {
        this.vulnerabilityId = vulnerabilityId;
        this.status = 'existing';
    }
};
exports.ScanResult = ScanResult;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ScanResult.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500 }),
    __metadata("design:type", String)
], ScanResult.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], ScanResult.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['info', 'low', 'medium', 'high', 'critical'],
    }),
    __metadata("design:type", String)
], ScanResult.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['new', 'existing', 'fixed', 'false_positive', 'risk_accepted'],
        default: 'new',
    }),
    __metadata("design:type", String)
], ScanResult.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cvss_score', type: 'decimal', precision: 3, scale: 1, nullable: true }),
    __metadata("design:type", Number)
], ScanResult.prototype, "cvssScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'risk_score', type: 'decimal', precision: 3, scale: 1, default: 0 }),
    __metadata("design:type", Number)
], ScanResult.prototype, "riskScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'plugin_id', nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "pluginId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'plugin_name', nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "pluginName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'plugin_family', nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "pluginFamily", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], ScanResult.prototype, "port", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "protocol", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'service_name', nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "serviceName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'service_version', nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "serviceVersion", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "url", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'http_method', nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "httpMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'request_response', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ScanResult.prototype, "requestResponse", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "evidence", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "solution", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'see_also', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ScanResult.prototype, "seeAlso", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cve_references', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ScanResult.prototype, "cveReferences", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'owasp_category', nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "owaspCategory", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cwe_id', nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "cweId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'exploit_available', default: false }),
    __metadata("design:type", Boolean)
], ScanResult.prototype, "exploitAvailable", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'exploit_ease', nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "exploitEase", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'patch_publication_date', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], ScanResult.prototype, "patchPublicationDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vuln_publication_date', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ScanResult.prototype, "vulnPublicationDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'first_detected', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], ScanResult.prototype, "firstDetected", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_detected', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], ScanResult.prototype, "lastDetected", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'detection_count', type: 'integer', default: 1 }),
    __metadata("design:type", Number)
], ScanResult.prototype, "detectionCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], ScanResult.prototype, "confidence", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'false_positive_likelihood', type: 'decimal', precision: 3, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], ScanResult.prototype, "falsePositiveLikelihood", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'raw_output', type: 'text', nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "rawOutput", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_e = typeof Record !== "undefined" && Record) === "function" ? _e : Object)
], ScanResult.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'detected_at', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], ScanResult.prototype, "detectedAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_g = typeof Date !== "undefined" && Date) === "function" ? _g : Object)
], ScanResult.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_h = typeof Date !== "undefined" && Date) === "function" ? _h : Object)
], ScanResult.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => vulnerability_scan_entity_1.VulnerabilityScan),
    (0, typeorm_1.JoinColumn)({ name: 'scan_id' }),
    __metadata("design:type", typeof (_j = typeof vulnerability_scan_entity_1.VulnerabilityScan !== "undefined" && vulnerability_scan_entity_1.VulnerabilityScan) === "function" ? _j : Object)
], ScanResult.prototype, "scan", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'scan_id', type: 'uuid' }),
    __metadata("design:type", String)
], ScanResult.prototype, "scanId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => asset_entity_1.Asset),
    (0, typeorm_1.JoinColumn)({ name: 'asset_id' }),
    __metadata("design:type", typeof (_k = typeof asset_entity_1.Asset !== "undefined" && asset_entity_1.Asset) === "function" ? _k : Object)
], ScanResult.prototype, "asset", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'asset_id', type: 'uuid' }),
    __metadata("design:type", String)
], ScanResult.prototype, "assetId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => vulnerability_entity_1.Vulnerability, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'vulnerability_id' }),
    __metadata("design:type", typeof (_l = typeof vulnerability_entity_1.Vulnerability !== "undefined" && vulnerability_entity_1.Vulnerability) === "function" ? _l : Object)
], ScanResult.prototype, "vulnerability", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vulnerability_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ScanResult.prototype, "vulnerabilityId", void 0);
exports.ScanResult = ScanResult = __decorate([
    (0, typeorm_1.Entity)('scan_results'),
    (0, typeorm_1.Index)(['scanId']),
    (0, typeorm_1.Index)(['assetId']),
    (0, typeorm_1.Index)(['vulnerabilityId']),
    (0, typeorm_1.Index)(['severity']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['detectedAt'])
], ScanResult);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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