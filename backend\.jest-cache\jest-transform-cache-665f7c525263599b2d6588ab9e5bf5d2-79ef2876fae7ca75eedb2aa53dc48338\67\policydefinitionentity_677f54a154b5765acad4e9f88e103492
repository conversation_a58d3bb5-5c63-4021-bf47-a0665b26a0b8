0378416f96b89861ea333ec931cc2a93
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PolicyDefinition = void 0;
const typeorm_1 = require("typeorm");
const compliance_framework_entity_1 = require("./compliance-framework.entity");
const policy_violation_entity_1 = require("./policy-violation.entity");
/**
 * Policy Definition entity
 * Represents organizational policies and their enforcement rules
 */
let PolicyDefinition = class PolicyDefinition {
    /**
     * Check if policy is expired
     */
    get isExpired() {
        if (!this.expirationDate)
            return false;
        return new Date() > this.expirationDate;
    }
    /**
     * Check if policy is due for review
     */
    get isDueForReview() {
        const nextReviewDate = this.metadata?.nextReviewDate;
        if (!nextReviewDate)
            return false;
        return new Date() > new Date(nextReviewDate);
    }
    /**
     * Check if policy is critical
     */
    get isCritical() {
        return this.severity === 'critical' || this.enforcementLevel === 'critical';
    }
    /**
     * Get total number of rules
     */
    get totalRules() {
        return this.configuration.rules.length;
    }
    /**
     * Get mandatory rules
     */
    get mandatoryRules() {
        return this.configuration.rules.filter(rule => rule.type === 'mandatory');
    }
    /**
     * Get automatable rules
     */
    get automatableRules() {
        return this.configuration.rules.filter(rule => rule.automatable);
    }
    /**
     * Activate policy
     */
    activate(userId) {
        this.isActive = true;
        this.updatedBy = userId;
        if (!this.metadata) {
            this.metadata = {};
        }
        this.metadata.implementationStatus = 'implemented';
        this.metadata.implementationDate = new Date().toISOString();
    }
    /**
     * Deactivate policy
     */
    deactivate(userId, reason) {
        this.isActive = false;
        this.updatedBy = userId;
        if (!this.metadata) {
            this.metadata = {};
        }
        this.metadata.deactivationReason = reason;
        this.metadata.deactivatedBy = userId;
        this.metadata.deactivatedAt = new Date().toISOString();
    }
    /**
     * Update version
     */
    updateVersion(newVersion, userId, changes) {
        const oldVersion = this.version;
        this.version = newVersion;
        this.updatedBy = userId;
        if (!this.metadata) {
            this.metadata = {};
        }
        if (!this.metadata.changeHistory) {
            this.metadata.changeHistory = [];
        }
        this.metadata.changeHistory.push({
            version: newVersion,
            date: new Date().toISOString(),
            author: userId,
            changes,
            reason: `Version update from ${oldVersion} to ${newVersion}`,
        });
    }
    /**
     * Add rule to policy
     */
    addRule(rule) {
        if (!this.configuration.rules) {
            this.configuration.rules = [];
        }
        this.configuration.rules.push({
            id: `rule-${Date.now()}`,
            ...rule,
        });
    }
    /**
     * Remove rule from policy
     */
    removeRule(ruleId) {
        if (this.configuration.rules) {
            this.configuration.rules = this.configuration.rules.filter(rule => rule.id !== ruleId);
        }
    }
    /**
     * Update rule
     */
    updateRule(ruleId, updates) {
        if (this.configuration.rules) {
            const ruleIndex = this.configuration.rules.findIndex(rule => rule.id === ruleId);
            if (ruleIndex >= 0) {
                this.configuration.rules[ruleIndex] = {
                    ...this.configuration.rules[ruleIndex],
                    ...updates,
                };
            }
        }
    }
    /**
     * Get rule by ID
     */
    getRuleById(ruleId) {
        return this.configuration.rules.find(rule => rule.id === ruleId) || null;
    }
    /**
     * Validate policy configuration
     */
    validateConfiguration() {
        const errors = [];
        // Check rules
        if (!this.configuration.rules || this.configuration.rules.length === 0) {
            errors.push('Policy must have at least one rule');
        }
        else {
            // Check rule IDs are unique
            const ruleIds = this.configuration.rules.map(r => r.id);
            const uniqueRuleIds = new Set(ruleIds);
            if (ruleIds.length !== uniqueRuleIds.size) {
                errors.push('Rule IDs must be unique');
            }
            // Check required rule fields
            this.configuration.rules.forEach((rule, index) => {
                if (!rule.name || !rule.description || !rule.requirement) {
                    errors.push(`Rule ${index + 1} is missing required fields`);
                }
            });
        }
        // Check effective date
        if (this.effectiveDate > new Date()) {
            errors.push('Effective date cannot be in the future for active policies');
        }
        // Check expiration date
        if (this.expirationDate && this.expirationDate <= this.effectiveDate) {
            errors.push('Expiration date must be after effective date');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * Calculate policy complexity score
     */
    calculateComplexityScore() {
        const totalRules = this.totalRules;
        const mandatoryRules = this.mandatoryRules.length;
        const automatableRules = this.automatableRules.length;
        // Weighted complexity calculation
        const ruleComplexity = totalRules * 2;
        const mandatoryComplexity = mandatoryRules * 3;
        const automationReduction = automatableRules * 1;
        const severityMultiplier = this.severity === 'critical' ? 2 : 1;
        return Math.max(1, (ruleComplexity + mandatoryComplexity - automationReduction) * severityMultiplier);
    }
    /**
     * Get compliance frameworks
     */
    getComplianceFrameworks() {
        return this.configuration.compliance?.frameworks || [];
    }
    /**
     * Get compliance controls
     */
    getComplianceControls() {
        return this.configuration.compliance?.controls || [];
    }
    /**
     * Check if policy applies to system
     */
    appliesToSystem(systemId) {
        const applicableSystems = this.configuration.scope?.applicableSystems;
        if (!applicableSystems)
            return true; // No restrictions means applies to all
        return applicableSystems.includes(systemId);
    }
    /**
     * Check if policy applies to role
     */
    appliesToRole(role) {
        const applicableRoles = this.configuration.scope?.applicableRoles;
        if (!applicableRoles)
            return true; // No restrictions means applies to all
        return applicableRoles.includes(role);
    }
    /**
     * Generate policy summary
     */
    generateSummary() {
        return {
            id: this.id,
            name: this.name,
            type: this.type,
            severity: this.severity,
            enforcementLevel: this.enforcementLevel,
            isActive: this.isActive,
            version: this.version,
            effectiveDate: this.effectiveDate,
            expirationDate: this.expirationDate,
            isExpired: this.isExpired,
            isDueForReview: this.isDueForReview,
            isCritical: this.isCritical,
            totalRules: this.totalRules,
            mandatoryRules: this.mandatoryRules.length,
            automatableRules: this.automatableRules.length,
            complexityScore: this.calculateComplexityScore(),
            complianceFrameworks: this.getComplianceFrameworks(),
            tags: this.tags,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
    /**
     * Export policy for sharing
     */
    exportPolicy() {
        return {
            name: this.name,
            description: this.description,
            type: this.type,
            severity: this.severity,
            enforcementLevel: this.enforcementLevel,
            version: this.version,
            configuration: this.configuration,
            metadata: this.metadata,
            tags: this.tags,
            exportedAt: new Date().toISOString(),
            exportedBy: 'Sentinel Platform',
        };
    }
};
exports.PolicyDefinition = PolicyDefinition;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PolicyDefinition.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], PolicyDefinition.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], PolicyDefinition.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'access_control',
            'data_protection',
            'authentication',
            'authorization',
            'encryption',
            'audit_logging',
            'backup_recovery',
            'incident_response',
            'change_management',
            'vulnerability_management',
            'network_security',
            'physical_security',
            'personnel_security',
            'business_continuity',
            'risk_management',
            'compliance_monitoring',
            'information_classification',
            'asset_management',
            'vendor_management',
            'training_awareness',
        ],
    }),
    __metadata("design:type", String)
], PolicyDefinition.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium',
    }),
    __metadata("design:type", String)
], PolicyDefinition.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'enforcement_level',
        type: 'enum',
        enum: ['advisory', 'warning', 'blocking', 'critical'],
        default: 'warning',
    }),
    __metadata("design:type", String)
], PolicyDefinition.prototype, "enforcementLevel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_active', default: true }),
    __metadata("design:type", Boolean)
], PolicyDefinition.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: '1.0' }),
    __metadata("design:type", String)
], PolicyDefinition.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'effective_date', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], PolicyDefinition.prototype, "effectiveDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'expiration_date', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], PolicyDefinition.prototype, "expirationDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], PolicyDefinition.prototype, "configuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], PolicyDefinition.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], PolicyDefinition.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], PolicyDefinition.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], PolicyDefinition.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], PolicyDefinition.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], PolicyDefinition.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => compliance_framework_entity_1.ComplianceFramework, framework => framework.policies, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'framework_id' }),
    __metadata("design:type", typeof (_e = typeof compliance_framework_entity_1.ComplianceFramework !== "undefined" && compliance_framework_entity_1.ComplianceFramework) === "function" ? _e : Object)
], PolicyDefinition.prototype, "framework", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'framework_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], PolicyDefinition.prototype, "frameworkId", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => policy_violation_entity_1.PolicyViolation, violation => violation.policy),
    __metadata("design:type", Array)
], PolicyDefinition.prototype, "violations", void 0);
exports.PolicyDefinition = PolicyDefinition = __decorate([
    (0, typeorm_1.Entity)('policy_definitions'),
    (0, typeorm_1.Index)(['frameworkId']),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['isActive']),
    (0, typeorm_1.Index)(['severity']),
    (0, typeorm_1.Index)(['enforcementLevel'])
], PolicyDefinition);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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