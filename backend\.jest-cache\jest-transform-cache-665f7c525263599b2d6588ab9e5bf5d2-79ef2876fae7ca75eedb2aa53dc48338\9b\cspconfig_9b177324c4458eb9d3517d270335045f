893c0bd3dc4d7b5845e4716022e8ddfa
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CspConfig = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
/**
 * Content Security Policy configuration service
 * Implements comprehensive CSP directives for XSS protection
 */
let CspConfig = class CspConfig {
    constructor(configService) {
        this.configService = configService;
    }
    /**
     * Generate Content Security Policy header value
     */
    generateCSPHeader() {
        const environment = this.configService.get('NODE_ENV', 'development');
        switch (environment) {
            case 'production':
                return this.getProductionCSP();
            case 'staging':
                return this.getStagingCSP();
            case 'test':
                return this.getTestCSP();
            default:
                return this.getDevelopmentCSP();
        }
    }
    /**
     * Production CSP - Most restrictive
     */
    getProductionCSP() {
        const directives = {
            'default-src': ["'self'"],
            'script-src': [
                "'self'",
                "'unsafe-inline'", // Required for some frameworks, consider removing
                'https://cdn.jsdelivr.net',
                'https://unpkg.com',
            ],
            'style-src': [
                "'self'",
                "'unsafe-inline'", // Required for dynamic styles
                'https://fonts.googleapis.com',
                'https://cdn.jsdelivr.net',
            ],
            'img-src': [
                "'self'",
                'data:',
                'https:',
                'blob:',
            ],
            'font-src': [
                "'self'",
                'https://fonts.gstatic.com',
                'https://cdn.jsdelivr.net',
                'data:',
            ],
            'connect-src': [
                "'self'",
                'https://api.sentinel.com',
                'wss://api.sentinel.com',
                'https://*.amazonaws.com',
                'https://*.azure.com',
            ],
            'media-src': ["'self'"],
            'object-src': ["'none'"],
            'child-src': ["'none'"],
            'frame-src': ["'none'"],
            'worker-src': ["'self'"],
            'manifest-src': ["'self'"],
            'base-uri': ["'self'"],
            'form-action': ["'self'"],
            'frame-ancestors': ["'none'"],
            'upgrade-insecure-requests': [],
            'block-all-mixed-content': [],
        };
        return this.buildCSPString(directives);
    }
    /**
     * Staging CSP - Moderate restrictions with debugging support
     */
    getStagingCSP() {
        const directives = {
            'default-src': ["'self'"],
            'script-src': [
                "'self'",
                "'unsafe-inline'",
                "'unsafe-eval'", // Allow eval for debugging
                'https://cdn.jsdelivr.net',
                'https://unpkg.com',
                'https://staging-cdn.sentinel.com',
            ],
            'style-src': [
                "'self'",
                "'unsafe-inline'",
                'https://fonts.googleapis.com',
                'https://cdn.jsdelivr.net',
                'https://staging-cdn.sentinel.com',
            ],
            'img-src': [
                "'self'",
                'data:',
                'https:',
                'blob:',
                'https://staging-assets.sentinel.com',
            ],
            'font-src': [
                "'self'",
                'https://fonts.gstatic.com',
                'https://cdn.jsdelivr.net',
                'data:',
            ],
            'connect-src': [
                "'self'",
                'https://staging-api.sentinel.com',
                'wss://staging-api.sentinel.com',
                'https://*.amazonaws.com',
                'https://*.azure.com',
                'https://staging-metrics.sentinel.com',
            ],
            'media-src': ["'self'", 'https://staging-media.sentinel.com'],
            'object-src': ["'none'"],
            'child-src': ["'self'"],
            'frame-src': ["'self'"],
            'worker-src': ["'self'"],
            'manifest-src': ["'self'"],
            'base-uri': ["'self'"],
            'form-action': ["'self'"],
            'frame-ancestors': ["'self'"],
            'report-uri': ['/api/v1/security/csp-report'],
        };
        return this.buildCSPString(directives);
    }
    /**
     * Development CSP - Permissive for development workflow
     */
    getDevelopmentCSP() {
        const directives = {
            'default-src': ["'self'"],
            'script-src': [
                "'self'",
                "'unsafe-inline'",
                "'unsafe-eval'",
                'https:',
                'http:',
                'localhost:*',
                '127.0.0.1:*',
            ],
            'style-src': [
                "'self'",
                "'unsafe-inline'",
                'https:',
                'http:',
                'localhost:*',
                '127.0.0.1:*',
            ],
            'img-src': [
                "'self'",
                'data:',
                'https:',
                'http:',
                'blob:',
                'localhost:*',
                '127.0.0.1:*',
            ],
            'font-src': [
                "'self'",
                'data:',
                'https:',
                'http:',
                'localhost:*',
                '127.0.0.1:*',
            ],
            'connect-src': [
                "'self'",
                'https:',
                'http:',
                'ws:',
                'wss:',
                'localhost:*',
                '127.0.0.1:*',
            ],
            'media-src': ["'self'", 'https:', 'http:', 'localhost:*', '127.0.0.1:*'],
            'object-src': ["'none'"],
            'child-src': ["'self'", 'localhost:*', '127.0.0.1:*'],
            'frame-src': ["'self'", 'localhost:*', '127.0.0.1:*'],
            'worker-src': ["'self'", 'blob:'],
            'manifest-src': ["'self'"],
            'base-uri': ["'self'"],
            'form-action': ["'self'"],
            'report-uri': ['/api/v1/security/csp-report'],
        };
        return this.buildCSPString(directives);
    }
    /**
     * Test CSP - Minimal restrictions for testing
     */
    getTestCSP() {
        const directives = {
            'default-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
            'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
            'style-src': ["'self'", "'unsafe-inline'"],
            'img-src': ["'self'", 'data:', 'blob:'],
            'font-src': ["'self'", 'data:'],
            'connect-src': ["'self'"],
            'media-src': ["'self'"],
            'object-src': ["'none'"],
            'base-uri': ["'self'"],
            'form-action': ["'self'"],
        };
        return this.buildCSPString(directives);
    }
    /**
     * Build CSP string from directives object
     */
    buildCSPString(directives) {
        const cspParts = [];
        for (const [directive, sources] of Object.entries(directives)) {
            if (sources.length === 0) {
                // Directives without sources (like upgrade-insecure-requests)
                cspParts.push(directive);
            }
            else {
                cspParts.push(`${directive} ${sources.join(' ')}`);
            }
        }
        return cspParts.join('; ');
    }
    /**
     * Get CSP configuration for specific endpoint types
     */
    getEndpointSpecificCSP(endpointType) {
        const baseCSP = this.generateCSPHeader();
        switch (endpointType) {
            case 'api':
                // API endpoints don't need script/style sources
                return this.getAPIOnlyCSP();
            case 'webhook':
                // Webhook endpoints need minimal CSP
                return this.getWebhookCSP();
            case 'health':
                // Health check endpoints need minimal CSP
                return this.getHealthCheckCSP();
            default:
                return baseCSP;
        }
    }
    /**
     * API-only CSP for JSON endpoints
     */
    getAPIOnlyCSP() {
        const directives = {
            'default-src': ["'none'"],
            'connect-src': ["'self'"],
            'base-uri': ["'none'"],
            'form-action': ["'none'"],
            'frame-ancestors': ["'none'"],
            'object-src': ["'none'"],
        };
        return this.buildCSPString(directives);
    }
    /**
     * Webhook CSP for external integrations
     */
    getWebhookCSP() {
        const directives = {
            'default-src': ["'none'"],
            'connect-src': ["'self'"],
            'base-uri': ["'none'"],
            'form-action': ["'self'"],
            'frame-ancestors': ["'none'"],
            'object-src': ["'none'"],
        };
        return this.buildCSPString(directives);
    }
    /**
     * Health check CSP
     */
    getHealthCheckCSP() {
        const directives = {
            'default-src': ["'none'"],
            'base-uri': ["'none'"],
            'form-action': ["'none'"],
            'frame-ancestors': ["'none'"],
            'object-src': ["'none'"],
        };
        return this.buildCSPString(directives);
    }
    /**
     * Validate CSP configuration
     */
    validateCSPConfig() {
        try {
            const csp = this.generateCSPHeader();
            // Basic validation - ensure CSP is not empty
            if (!csp || csp.trim().length === 0) {
                throw new Error('CSP header is empty');
            }
            // Check for dangerous directives in production
            const environment = this.configService.get('NODE_ENV', 'development');
            if (environment === 'production') {
                if (csp.includes("'unsafe-eval'")) {
                    console.warn('CSP: unsafe-eval detected in production CSP');
                }
                if (csp.includes('*') && !csp.includes('https:')) {
                    console.warn('CSP: Wildcard sources detected without HTTPS restriction');
                }
            }
            return true;
        }
        catch (error) {
            console.error('CSP configuration validation failed:', error.message);
            return false;
        }
    }
    /**
     * Get CSP configuration summary for logging
     */
    getCSPConfigSummary() {
        const environment = this.configService.get('NODE_ENV', 'development');
        const csp = this.generateCSPHeader();
        return {
            environment,
            directiveCount: csp.split(';').length,
            hasUnsafeInline: csp.includes("'unsafe-inline'"),
            hasUnsafeEval: csp.includes("'unsafe-eval'"),
            hasReportUri: csp.includes('report-uri'),
            length: csp.length,
        };
    }
};
exports.CspConfig = CspConfig;
exports.CspConfig = CspConfig = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], CspConfig);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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