{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\types\\response.types.ts", "mappings": ";AAAA;;;;;GAKG;;;AAIH;;GAEG;AACH,IAAY,UA4BX;AA5BD,WAAY,UAAU;IACpB,UAAU;IACV,yCAAQ,CAAA;IACR,mDAAa,CAAA;IACb,qDAAc,CAAA;IACd,yDAAgB,CAAA;IAEhB,cAAc;IACd,uEAAuB,CAAA;IACvB,+CAAW,CAAA;IACX,6DAAkB,CAAA;IAElB,eAAe;IACf,2DAAiB,CAAA;IACjB,6DAAkB,CAAA;IAClB,uDAAe,CAAA;IACf,uDAAe,CAAA;IACf,yEAAwB,CAAA;IACxB,qDAAc,CAAA;IACd,6EAA0B,CAAA;IAC1B,uEAAuB,CAAA;IAEvB,eAAe;IACf,+EAA2B,CAAA;IAC3B,mEAAqB,CAAA;IACrB,2DAAiB,CAAA;IACjB,2EAAyB,CAAA;IACzB,mEAAqB,CAAA;AACvB,CAAC,EA5BW,UAAU,0BAAV,UAAU,QA4BrB;AAED;;GAEG;AACH,IAAY,cAKX;AALD,WAAY,cAAc;IACxB,qCAAmB,CAAA;IACnB,iCAAe,CAAA;IACf,qCAAmB,CAAA;IACnB,+BAAa,CAAA;AACf,CAAC,EALW,cAAc,8BAAd,cAAc,QAKzB;AAoLD;;GAEG;AACH,MAAa,aAAa;IACxB;;OAEG;IACH,MAAM,CAAC,OAAO,CACZ,IAAO,EACP,UAAkB,kCAAkC,EACpD,aAAyB,UAAU,CAAC,EAAE,EACtC,IAA0B;QAE1B,OAAO;YACL,MAAM,EAAE,cAAc,CAAC,OAAO;YAC9B,UAAU;YACV,OAAO;YACP,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI;SACL,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CACV,KAAkB,EAClB,UAAkB,kBAAkB,EACpC,aAAyB,UAAU,CAAC,qBAAqB,EACzD,gBAAgC;QAEhC,OAAO;YACL,MAAM,EAAE,cAAc,CAAC,KAAK;YAC5B,UAAU;YACV,OAAO;YACP,KAAK;YACL,MAAM,EAAE,gBAAgB;YACxB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CACpB,MAAyB,EACzB,UAAkB,mBAAmB;QAErC,MAAM,YAAY,GAAgB;YAChC,IAAI,EAAE,kBAAkB;YACxB,OAAO,EAAE,wCAAwC;SAClD,CAAC;QAEF,OAAO,aAAa,CAAC,KAAK,CACxB,YAAY,EACZ,OAAO,EACP,UAAU,CAAC,oBAAoB,EAC/B,MAAM,CACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CACb,QAAgB,EAChB,UAA4B;QAE5B,MAAM,OAAO,GAAG,UAAU;YACxB,CAAC,CAAC,GAAG,QAAQ,qBAAqB,UAAU,aAAa;YACzD,CAAC,CAAC,GAAG,QAAQ,YAAY,CAAC;QAE5B,MAAM,KAAK,GAAgB;YACzB,IAAI,EAAE,WAAW;YACjB,OAAO;YACP,OAAO,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;SAClC,CAAC;QAEF,OAAO,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CACjB,UAAkB,yBAAyB;QAE3C,MAAM,KAAK,GAAgB;YACzB,IAAI,EAAE,cAAc;YACpB,OAAO;SACR,CAAC;QAEF,OAAO,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CACd,UAAkB,eAAe;QAEjC,MAAM,KAAK,GAAgB;YACzB,IAAI,EAAE,WAAW;YACjB,OAAO;SACR,CAAC;QAEF,OAAO,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CACb,UAAkB,mBAAmB,EACrC,OAA6B;QAE7B,MAAM,KAAK,GAAgB;YACzB,IAAI,EAAE,UAAU;YAChB,OAAO;YACP,OAAO;SACR,CAAC;QAEF,OAAO,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAChB,UAAmB,EACnB,UAAkB,qBAAqB;QAEvC,MAAM,KAAK,GAAgB;YACzB,IAAI,EAAE,cAAc;YACpB,OAAO;YACP,OAAO,EAAE,EAAE,UAAU,EAAE;SACxB,CAAC;QAEF,OAAO,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CACd,KAAU,EACV,UAA0B,EAC1B,OAAe,EACf,UAAkB,6BAA6B;QAE/C,MAAM,KAAK,GAAG;YACZ,KAAK,EAAE,GAAG,OAAO,iBAAiB,UAAU,CAAC,YAAY,EAAE;YAC3D,IAAI,EAAE,GAAG,OAAO,SAAS,UAAU,CAAC,UAAU,UAAU,UAAU,CAAC,YAAY,EAAE;YACjF,IAAI,EAAE,GAAG,OAAO,SAAS,UAAU,CAAC,WAAW,UAAU,UAAU,CAAC,YAAY,EAAE;YAClF,GAAG,CAAC,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,QAAQ,IAAI;gBACnD,IAAI,EAAE,GAAG,OAAO,SAAS,UAAU,CAAC,QAAQ,UAAU,UAAU,CAAC,YAAY,EAAE;aAChF,CAAC;YACF,GAAG,CAAC,UAAU,CAAC,eAAe,IAAI,UAAU,CAAC,YAAY,IAAI;gBAC3D,QAAQ,EAAE,GAAG,OAAO,SAAS,UAAU,CAAC,YAAY,UAAU,UAAU,CAAC,YAAY,EAAE;aACxF,CAAC;SACH,CAAC;QAEF,OAAO;YACL,MAAM,EAAE,cAAc,CAAC,OAAO;YAC9B,UAAU,EAAE,UAAU,CAAC,EAAE;YACzB,OAAO;YACP,IAAI,EAAE,KAAK;YACX,UAAU;YACV,KAAK;YACL,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAClB,OAKE,EACF,OAAgB;QAEhB,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;QACtE,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC,MAAM,CAAC;QAChE,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC;QAE7B,uBAAuB;QACvB,MAAM,YAAY,GAA2B,EAAE,CAAC;QAChD,OAAO;aACJ,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,OAAO,IAAI,CAAC,CAAC,KAAK,CAAC;aAC5C,OAAO,CAAC,CAAC,CAAC,EAAE;YACX,MAAM,IAAI,GAAG,CAAC,CAAC,KAAM,CAAC,IAAI,CAAC;YAC3B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEL,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC;YACjC,CAAC,CAAC,OAAO,KAAK,oCAAoC;YAClD,CAAC,CAAC,GAAG,UAAU,OAAO,KAAK,uCAAuC,MAAM,SAAS,CAAC;QAEpF,OAAO;YACL,MAAM,EAAE,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO;YACtE,UAAU,EAAE,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ;YAC9D,OAAO,EAAE,OAAO,IAAI,cAAc;YAClC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK;YACL,UAAU;YACV,MAAM;YACN,OAAO;YACP,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;SAC9E,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAChB,MAKE,EACF,UAIC;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;QACjF,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAErC,0CAA0C;QAC1C,MAAM,mBAAmB,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC9E,GAAG,CAAC,GAAG,CAAC,GAAG;gBACT,GAAG,KAAK;gBACR,WAAW,EAAE,GAAG;aACjB,CAAC;YACF,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAyB,CAAC,CAAC;QAE9B,OAAO;YACL,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK;YAC/D,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,mBAAmB;YACpE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,0BAA0B;YACnE,SAAS,EAAE,GAAG;YACd,OAAO;YACP,MAAM,EAAE,mBAAmB;YAC3B,MAAM,EAAE;gBACN,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,MAAM,EAAE;oBACN,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,IAAI;oBAC5B,KAAK,EAAE,UAAU,CAAC,MAAM,CAAC,KAAK;oBAC9B,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;iBACjF;gBACD,GAAG,EAAE;oBACH,KAAK,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK;iBAC5B;aACF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CACf,QAOC,EACD,QAA8B,EAC9B,UAAkB,4BAA4B;QAE9C,OAAO;YACL,MAAM,EAAE,cAAc,CAAC,OAAO;YAC9B,UAAU,EAAE,UAAU,CAAC,OAAO;YAC9B,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,QAAQ;aACT;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,QAAyB;QACxC,OAAO,QAAQ,CAAC,MAAM,KAAK,cAAc,CAAC,OAAO,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,QAAyB;QACtC,OAAO,QAAQ,CAAC,MAAM,KAAK,cAAc,CAAC,KAAK,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,QAAyB;QAC9C,IAAI,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpC,OAAO,QAAQ,CAAC,KAAK,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC;QACpD,CAAC;QACD,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,QAAyB;QAClD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,QAAQ,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAE1C,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAClB,QAAW,EACX,SAAiB;QAEjB,OAAO;YACL,GAAG,QAAQ;YACX,SAAS;SACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAChB,QAAW,EACX,OAAe;QAEf,OAAO;YACL,GAAG,QAAQ;YACX,OAAO;SACR,CAAC;IACJ,CAAC;CACF;AAnWD,sCAmWC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\types\\response.types.ts"], "sourcesContent": ["/**\r\n * Response Types\r\n * \r\n * Common types and interfaces for API responses across the application.\r\n * Provides consistent response patterns and utilities.\r\n */\r\n\r\nimport { PaginationMeta } from './pagination.types';\r\n\r\n/**\r\n * HTTP Status Codes\r\n */\r\nexport enum HttpStatus {\r\n  // Success\r\n  OK = 200,\r\n  CREATED = 201,\r\n  ACCEPTED = 202,\r\n  NO_CONTENT = 204,\r\n\r\n  // Redirection\r\n  MOVED_PERMANENTLY = 301,\r\n  FOUND = 302,\r\n  NOT_MODIFIED = 304,\r\n\r\n  // Client Error\r\n  BAD_REQUEST = 400,\r\n  UNAUTHORIZED = 401,\r\n  FORBIDDEN = 403,\r\n  NOT_FOUND = 404,\r\n  METHOD_NOT_ALLOWED = 405,\r\n  CONFLICT = 409,\r\n  UNPROCESSABLE_ENTITY = 422,\r\n  TOO_MANY_REQUESTS = 429,\r\n\r\n  // Server Error\r\n  INTERNAL_SERVER_ERROR = 500,\r\n  NOT_IMPLEMENTED = 501,\r\n  BAD_GATEWAY = 502,\r\n  SERVICE_UNAVAILABLE = 503,\r\n  GATEWAY_TIMEOUT = 504,\r\n}\r\n\r\n/**\r\n * Response Status\r\n */\r\nexport enum ResponseStatus {\r\n  SUCCESS = 'success',\r\n  ERROR = 'error',\r\n  WARNING = 'warning',\r\n  INFO = 'info',\r\n}\r\n\r\n/**\r\n * Error Detail\r\n * Detailed information about an error\r\n */\r\nexport interface ErrorDetail {\r\n  /** Error code */\r\n  code: string;\r\n  /** Human-readable error message */\r\n  message: string;\r\n  /** Field that caused the error (for validation errors) */\r\n  field?: string;\r\n  /** Additional context about the error */\r\n  context?: Record<string, any>;\r\n  /** Stack trace (only in development) */\r\n  stack?: string;\r\n}\r\n\r\n/**\r\n * Validation Error\r\n * Specific error for validation failures\r\n */\r\nexport interface ValidationError extends ErrorDetail {\r\n  /** Field that failed validation */\r\n  field: string;\r\n  /** Value that failed validation */\r\n  value?: any;\r\n  /** Validation rule that failed */\r\n  rule?: string;\r\n}\r\n\r\n/**\r\n * Base API Response\r\n * Standard structure for all API responses\r\n */\r\nexport interface BaseApiResponse {\r\n  /** Response status */\r\n  status: ResponseStatus;\r\n  /** HTTP status code */\r\n  statusCode: HttpStatus;\r\n  /** Response message */\r\n  message: string;\r\n  /** Timestamp of the response */\r\n  timestamp: string;\r\n  /** Request ID for tracing */\r\n  requestId?: string;\r\n  /** API version */\r\n  version?: string;\r\n  /** Response metadata */\r\n  meta?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Success Response\r\n * Response for successful operations\r\n */\r\nexport interface SuccessResponse<T = any> extends BaseApiResponse {\r\n  status: ResponseStatus.SUCCESS;\r\n  /** Response data */\r\n  data: T;\r\n  /** Links for HATEOAS */\r\n  links?: Record<string, string>;\r\n}\r\n\r\n/**\r\n * Error Response\r\n * Response for failed operations\r\n */\r\nexport interface ErrorResponse extends BaseApiResponse {\r\n  status: ResponseStatus.ERROR;\r\n  /** Error details */\r\n  error: ErrorDetail;\r\n  /** Additional errors (for validation) */\r\n  errors?: ErrorDetail[];\r\n  /** Debug information (only in development) */\r\n  debug?: {\r\n    stack?: string;\r\n    context?: Record<string, any>;\r\n  };\r\n}\r\n\r\n/**\r\n * Paginated Response\r\n * Response for paginated data\r\n */\r\nexport interface PaginatedResponse<T = any> extends SuccessResponse<T[]> {\r\n  /** Pagination metadata */\r\n  pagination: PaginationMeta;\r\n  /** Pagination links */\r\n  links: {\r\n    first: string;\r\n    last: string;\r\n    next?: string;\r\n    previous?: string;\r\n    self: string;\r\n  };\r\n}\r\n\r\n/**\r\n * Bulk Operation Response\r\n * Response for bulk operations\r\n */\r\nexport interface BulkOperationResponse<T = any> extends BaseApiResponse {\r\n  /** Total number of items processed */\r\n  total: number;\r\n  /** Number of successful operations */\r\n  successful: number;\r\n  /** Number of failed operations */\r\n  failed: number;\r\n  /** Results for each item */\r\n  results: Array<{\r\n    /** Item identifier */\r\n    id: string | number;\r\n    /** Operation status */\r\n    status: 'success' | 'error';\r\n    /** Result data (for successful operations) */\r\n    data?: T;\r\n    /** Error details (for failed operations) */\r\n    error?: ErrorDetail;\r\n  }>;\r\n  /** Summary of errors by type */\r\n  errorSummary?: Record<string, number>;\r\n}\r\n\r\n/**\r\n * Health Check Response\r\n * Response for health check endpoints\r\n */\r\nexport interface HealthCheckResponse extends BaseApiResponse {\r\n  /** Overall health status */\r\n  healthy: boolean;\r\n  /** Individual service checks */\r\n  checks: Record<string, {\r\n    status: 'healthy' | 'unhealthy' | 'degraded';\r\n    message?: string;\r\n    responseTime?: number;\r\n    lastChecked: string;\r\n    details?: Record<string, any>;\r\n  }>;\r\n  /** System information */\r\n  system: {\r\n    uptime: number;\r\n    memory: {\r\n      used: number;\r\n      total: number;\r\n      percentage: number;\r\n    };\r\n    cpu: {\r\n      usage: number;\r\n    };\r\n  };\r\n}\r\n\r\n/**\r\n * File Upload Response\r\n * Response for file upload operations\r\n */\r\nexport interface FileUploadResponse extends SuccessResponse {\r\n  data: {\r\n    /** Uploaded file information */\r\n    file: {\r\n      /** Original filename */\r\n      originalName: string;\r\n      /** Stored filename */\r\n      filename: string;\r\n      /** File size in bytes */\r\n      size: number;\r\n      /** MIME type */\r\n      mimeType: string;\r\n      /** File URL */\r\n      url: string;\r\n      /** File hash/checksum */\r\n      hash?: string;\r\n    };\r\n    /** Upload metadata */\r\n    metadata?: Record<string, any>;\r\n  };\r\n}\r\n\r\n/**\r\n * Response Utilities\r\n */\r\nexport class ResponseUtils {\r\n  /**\r\n   * Create a success response\r\n   */\r\n  static success<T>(\r\n    data: T,\r\n    message: string = 'Operation completed successfully',\r\n    statusCode: HttpStatus = HttpStatus.OK,\r\n    meta?: Record<string, any>\r\n  ): SuccessResponse<T> {\r\n    return {\r\n      status: ResponseStatus.SUCCESS,\r\n      statusCode,\r\n      message,\r\n      data,\r\n      timestamp: new Date().toISOString(),\r\n      meta,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create an error response\r\n   */\r\n  static error(\r\n    error: ErrorDetail,\r\n    message: string = 'Operation failed',\r\n    statusCode: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR,\r\n    additionalErrors?: ErrorDetail[]\r\n  ): ErrorResponse {\r\n    return {\r\n      status: ResponseStatus.ERROR,\r\n      statusCode,\r\n      message,\r\n      error,\r\n      errors: additionalErrors,\r\n      timestamp: new Date().toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a validation error response\r\n   */\r\n  static validationError(\r\n    errors: ValidationError[],\r\n    message: string = 'Validation failed'\r\n  ): ErrorResponse {\r\n    const primaryError: ErrorDetail = {\r\n      code: 'VALIDATION_ERROR',\r\n      message: 'One or more validation errors occurred',\r\n    };\r\n\r\n    return ResponseUtils.error(\r\n      primaryError,\r\n      message,\r\n      HttpStatus.UNPROCESSABLE_ENTITY,\r\n      errors\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Create a not found response\r\n   */\r\n  static notFound(\r\n    resource: string,\r\n    identifier?: string | number\r\n  ): ErrorResponse {\r\n    const message = identifier \r\n      ? `${resource} with identifier '${identifier}' not found`\r\n      : `${resource} not found`;\r\n\r\n    const error: ErrorDetail = {\r\n      code: 'NOT_FOUND',\r\n      message,\r\n      context: { resource, identifier },\r\n    };\r\n\r\n    return ResponseUtils.error(error, message, HttpStatus.NOT_FOUND);\r\n  }\r\n\r\n  /**\r\n   * Create an unauthorized response\r\n   */\r\n  static unauthorized(\r\n    message: string = 'Authentication required'\r\n  ): ErrorResponse {\r\n    const error: ErrorDetail = {\r\n      code: 'UNAUTHORIZED',\r\n      message,\r\n    };\r\n\r\n    return ResponseUtils.error(error, message, HttpStatus.UNAUTHORIZED);\r\n  }\r\n\r\n  /**\r\n   * Create a forbidden response\r\n   */\r\n  static forbidden(\r\n    message: string = 'Access denied'\r\n  ): ErrorResponse {\r\n    const error: ErrorDetail = {\r\n      code: 'FORBIDDEN',\r\n      message,\r\n    };\r\n\r\n    return ResponseUtils.error(error, message, HttpStatus.FORBIDDEN);\r\n  }\r\n\r\n  /**\r\n   * Create a conflict response\r\n   */\r\n  static conflict(\r\n    message: string = 'Resource conflict',\r\n    context?: Record<string, any>\r\n  ): ErrorResponse {\r\n    const error: ErrorDetail = {\r\n      code: 'CONFLICT',\r\n      message,\r\n      context,\r\n    };\r\n\r\n    return ResponseUtils.error(error, message, HttpStatus.CONFLICT);\r\n  }\r\n\r\n  /**\r\n   * Create a rate limit response\r\n   */\r\n  static rateLimited(\r\n    retryAfter?: number,\r\n    message: string = 'Rate limit exceeded'\r\n  ): ErrorResponse {\r\n    const error: ErrorDetail = {\r\n      code: 'RATE_LIMITED',\r\n      message,\r\n      context: { retryAfter },\r\n    };\r\n\r\n    return ResponseUtils.error(error, message, HttpStatus.TOO_MANY_REQUESTS);\r\n  }\r\n\r\n  /**\r\n   * Create a paginated response\r\n   */\r\n  static paginated<T>(\r\n    items: T[],\r\n    pagination: PaginationMeta,\r\n    baseUrl: string,\r\n    message: string = 'Data retrieved successfully'\r\n  ): PaginatedResponse<T> {\r\n    const links = {\r\n      first: `${baseUrl}?page=1&limit=${pagination.itemsPerPage}`,\r\n      last: `${baseUrl}?page=${pagination.totalPages}&limit=${pagination.itemsPerPage}`,\r\n      self: `${baseUrl}?page=${pagination.currentPage}&limit=${pagination.itemsPerPage}`,\r\n      ...(pagination.hasNextPage && pagination.nextPage && {\r\n        next: `${baseUrl}?page=${pagination.nextPage}&limit=${pagination.itemsPerPage}`,\r\n      }),\r\n      ...(pagination.hasPreviousPage && pagination.previousPage && {\r\n        previous: `${baseUrl}?page=${pagination.previousPage}&limit=${pagination.itemsPerPage}`,\r\n      }),\r\n    };\r\n\r\n    return {\r\n      status: ResponseStatus.SUCCESS,\r\n      statusCode: HttpStatus.OK,\r\n      message,\r\n      data: items,\r\n      pagination,\r\n      links,\r\n      timestamp: new Date().toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a bulk operation response\r\n   */\r\n  static bulkOperation<T>(\r\n    results: Array<{\r\n      id: string | number;\r\n      status: 'success' | 'error';\r\n      data?: T;\r\n      error?: ErrorDetail;\r\n    }>,\r\n    message?: string\r\n  ): BulkOperationResponse<T> {\r\n    const successful = results.filter(r => r.status === 'success').length;\r\n    const failed = results.filter(r => r.status === 'error').length;\r\n    const total = results.length;\r\n\r\n    // Create error summary\r\n    const errorSummary: Record<string, number> = {};\r\n    results\r\n      .filter(r => r.status === 'error' && r.error)\r\n      .forEach(r => {\r\n        const code = r.error!.code;\r\n        errorSummary[code] = (errorSummary[code] || 0) + 1;\r\n      });\r\n\r\n    const defaultMessage = failed === 0 \r\n      ? `All ${total} operations completed successfully`\r\n      : `${successful} of ${total} operations completed successfully, ${failed} failed`;\r\n\r\n    return {\r\n      status: failed === 0 ? ResponseStatus.SUCCESS : ResponseStatus.WARNING,\r\n      statusCode: failed === 0 ? HttpStatus.OK : HttpStatus.ACCEPTED,\r\n      message: message || defaultMessage,\r\n      timestamp: new Date().toISOString(),\r\n      total,\r\n      successful,\r\n      failed,\r\n      results,\r\n      errorSummary: Object.keys(errorSummary).length > 0 ? errorSummary : undefined,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a health check response\r\n   */\r\n  static healthCheck(\r\n    checks: Record<string, {\r\n      status: 'healthy' | 'unhealthy' | 'degraded';\r\n      message?: string;\r\n      responseTime?: number;\r\n      details?: Record<string, any>;\r\n    }>,\r\n    systemInfo: {\r\n      uptime: number;\r\n      memory: { used: number; total: number };\r\n      cpu: { usage: number };\r\n    }\r\n  ): HealthCheckResponse {\r\n    const healthy = Object.values(checks).every(check => check.status === 'healthy');\r\n    const now = new Date().toISOString();\r\n\r\n    // Add lastChecked timestamp to all checks\r\n    const checksWithTimestamp = Object.entries(checks).reduce((acc, [key, check]) => {\r\n      acc[key] = {\r\n        ...check,\r\n        lastChecked: now,\r\n      };\r\n      return acc;\r\n    }, {} as Record<string, any>);\r\n\r\n    return {\r\n      status: healthy ? ResponseStatus.SUCCESS : ResponseStatus.ERROR,\r\n      statusCode: healthy ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE,\r\n      message: healthy ? 'System is healthy' : 'System has health issues',\r\n      timestamp: now,\r\n      healthy,\r\n      checks: checksWithTimestamp,\r\n      system: {\r\n        uptime: systemInfo.uptime,\r\n        memory: {\r\n          used: systemInfo.memory.used,\r\n          total: systemInfo.memory.total,\r\n          percentage: Math.round((systemInfo.memory.used / systemInfo.memory.total) * 100),\r\n        },\r\n        cpu: {\r\n          usage: systemInfo.cpu.usage,\r\n        },\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a file upload response\r\n   */\r\n  static fileUpload(\r\n    fileInfo: {\r\n      originalName: string;\r\n      filename: string;\r\n      size: number;\r\n      mimeType: string;\r\n      url: string;\r\n      hash?: string;\r\n    },\r\n    metadata?: Record<string, any>,\r\n    message: string = 'File uploaded successfully'\r\n  ): FileUploadResponse {\r\n    return {\r\n      status: ResponseStatus.SUCCESS,\r\n      statusCode: HttpStatus.CREATED,\r\n      message,\r\n      timestamp: new Date().toISOString(),\r\n      data: {\r\n        file: fileInfo,\r\n        metadata,\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check if response is successful\r\n   */\r\n  static isSuccess(response: BaseApiResponse): response is SuccessResponse {\r\n    return response.status === ResponseStatus.SUCCESS;\r\n  }\r\n\r\n  /**\r\n   * Check if response is an error\r\n   */\r\n  static isError(response: BaseApiResponse): response is ErrorResponse {\r\n    return response.status === ResponseStatus.ERROR;\r\n  }\r\n\r\n  /**\r\n   * Extract error message from response\r\n   */\r\n  static getErrorMessage(response: BaseApiResponse): string {\r\n    if (ResponseUtils.isError(response)) {\r\n      return response.error.message || response.message;\r\n    }\r\n    return response.message;\r\n  }\r\n\r\n  /**\r\n   * Extract all error messages from response\r\n   */\r\n  static getAllErrorMessages(response: BaseApiResponse): string[] {\r\n    if (!ResponseUtils.isError(response)) {\r\n      return [];\r\n    }\r\n\r\n    const messages = [response.error.message];\r\n    \r\n    if (response.errors) {\r\n      messages.push(...response.errors.map(error => error.message));\r\n    }\r\n\r\n    return messages.filter(Boolean);\r\n  }\r\n\r\n  /**\r\n   * Create response with request ID\r\n   */\r\n  static withRequestId<T extends BaseApiResponse>(\r\n    response: T,\r\n    requestId: string\r\n  ): T {\r\n    return {\r\n      ...response,\r\n      requestId,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create response with version\r\n   */\r\n  static withVersion<T extends BaseApiResponse>(\r\n    response: T,\r\n    version: string\r\n  ): T {\r\n    return {\r\n      ...response,\r\n      version,\r\n    };\r\n  }\r\n}"], "version": 3}