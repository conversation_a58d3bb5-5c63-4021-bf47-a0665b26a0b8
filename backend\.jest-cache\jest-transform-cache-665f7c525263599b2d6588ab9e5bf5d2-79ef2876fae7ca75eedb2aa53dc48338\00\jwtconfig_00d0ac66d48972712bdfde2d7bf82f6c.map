{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\jwt.config.ts", "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,qDAAiF;AACjF,yDAAoD;AA6BpD,yCAAyC;AACzC,MAAa,mBAAmB;CAmD/B;AAnDD,kDAmDC;AAhDC;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,IAAI,qDAAqD,CAAC;;mDACzE;AAIhB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACQ;AAInB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACS;AAIpB;IAFC,IAAA,sBAAI,EAAC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,IAAI,OAAO,CAAC;;sDAClB;AAIzB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,IAAI,KAAK,CAAC;;sDACtB;AAInB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,IAAI,yDAAyD,CAAC;;0DACtE;AAIvB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC;;6DACd;AAI1B;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,IAAI,mBAAmB,CAAC;;mDACvC;AAIhB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,IAAI,gBAAgB,CAAC;;qDAClC;AAOlB;IALC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,GAAG,CAAC;IACR,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;;2DACrB;AAIxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACM;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;kDACI;AAGjB,oBAAoB;AACpB,MAAM,YAAY,GAAG,CAAC,KAAyB,EAAE,QAAQ,GAAG,KAAK,EAAW,EAAE,CAC5E,KAAK,EAAE,WAAW,EAAE,KAAK,MAAM,IAAI,QAAQ,CAAC;AAE9C,MAAM,GAAG,GAAG;IACV,SAAS,EAAE,CAAC,GAAW,EAAE,QAAQ,GAAG,EAAE,EAAU,EAAE,CAChD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,QAAQ;IAE9B,SAAS,EAAE,CAAC,GAAW,EAAE,QAAQ,GAAG,CAAC,EAAU,EAAE;QAC/C,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,KAAK;YAAE,OAAO,QAAQ,CAAC;QAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAC7B,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;IAClD,CAAC;IAED,UAAU,EAAE,CAAC,GAAW,EAAE,QAAQ,GAAG,KAAK,EAAW,EAAE,CACrD,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC;IAE1C,YAAY,EAAE,GAAY,EAAE,CAC1B,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,YAAY;CAClC,CAAC;AAEX,yBAAyB;AACzB,MAAM,QAAQ,GAAG;IACf,UAAU,EAAE,qDAAqD;IACjE,kBAAkB,EAAE,yDAAyD;IAC7E,aAAa,EAAE,OAAuB;IACtC,cAAc,EAAE,KAAK;IACrB,sBAAsB,EAAE,IAAI;IAC5B,UAAU,EAAE,mBAAmB;IAC/B,YAAY,EAAE,gBAAgB;IAC9B,mBAAmB,EAAE,CAAC;IACtB,WAAW,EAAE,IAAI;CACT,CAAC;AAEX;;;GAGG;AACU,QAAA,SAAS,GAAG,IAAA,mBAAU,EAAC,KAAK,EAAE,GAAc,EAAE;IACzD,MAAM,SAAS,GAAI,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,aAAa,CAAkB,CAAC;IAC3F,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAEzC,gDAAgD;IAChD,IAAI,GAAG,CAAC,YAAY,EAAE,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAC3C,MAAM,aAAa,GAAG,GAAG,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QAE1D,IAAI,MAAM,KAAK,QAAQ,CAAC,UAAU,IAAI,aAAa,KAAK,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YACpF,OAAO,CAAC,IAAI,CAAC,2HAA2H,CAAC,CAAC;QAC5I,CAAC;IACH,CAAC;IAED,kDAAkD;IAClD,MAAM,UAAU,GAAG;QACjB,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC,YAAY,EAAE,QAAQ,CAAC,UAAU,CAAC;QACxD,SAAS;QACT,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,cAAc,CAAC;QACnE,aAAa,EAAE,GAAG,CAAC,SAAS,CAAC,oBAAoB,EAAE,QAAQ,CAAC,kBAAkB,CAAC;QAC/E,gBAAgB,EAAE,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,QAAQ,CAAC,sBAAsB,CAAC;QAC1F,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC,YAAY,EAAE,QAAQ,CAAC,UAAU,CAAC;QACxD,QAAQ,EAAE,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,QAAQ,CAAC,YAAY,CAAC;QAC9D,cAAc,EAAE,GAAG,CAAC,SAAS,CAAC,qBAAqB,EAAE,QAAQ,CAAC,mBAAmB,CAAC;QAClF,gBAAgB,EAAE,GAAG,CAAC,UAAU,CAAC,uBAAuB,EAAE,KAAK,CAAC;QAChE,eAAe,EAAE,GAAG,CAAC,UAAU,CAAC,uBAAuB,EAAE,KAAK,CAAC;QAC/D,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,QAAQ,CAAC,WAAW,CAAC;KAC3D,CAAC;IAEF,+BAA+B;IAC/B,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACtE,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACxE,MAAM,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;IAC7C,MAAM,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAEtC,4CAA4C;IAC5C,IAAI,KAAK,IAAI,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;QAC/D,OAAO,CAAC,IAAI,CAAC,qIAAqI,CAAC,CAAC;IACtJ,CAAC;IAED,MAAM,MAAM,GAAc;QACxB,GAAG,UAAU;QACb,GAAG,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE,CAAC;QAC/B,GAAG,CAAC,UAAU,IAAI,EAAE,UAAU,EAAE,CAAC;QACjC,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,CAAC;QAC3B,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE,CAAC;KACxB,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,eAAe,GAAG;IAC7B;;OAEG;IACH,cAAc,EAAE,CAAC,MAAiB,EAAE,EAAE,CAAC,CAAC;QACtC,SAAS,EAAE,MAAM,CAAC,SAAS;QAC3B,SAAS,EAAE,MAAM,CAAC,SAAS;QAC3B,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,KAAK,EAAE,MAAM,CAAC,KAAK;KACpB,CAAC;IAEF;;OAEG;IACH,gBAAgB,EAAE,CAAC,MAAiB,EAAE,EAAE,CAAC,CAAC;QACxC,UAAU,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC;QAC9B,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,cAAc,EAAE,MAAM,CAAC,cAAc;QACrC,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;QACzC,eAAe,EAAE,MAAM,CAAC,eAAe;QACvC,MAAM,EAAE,MAAM,CAAC,MAAM;KACtB,CAAC;IAEF;;OAEG;IACH,cAAc,EAAE,CAAC,MAAiB,EAAE,SAAS,GAAG,KAAK,EAAU,EAAE;QAC/D,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC;QAChG,CAAC;QACD,OAAO,MAAM,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,QAAQ,EAAE,CAAC,MAAiB,EAAY,EAAE;QACxC,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACtB,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAC3D,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBACvB,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,MAAM,CAAC,cAAc,GAAG,CAAC,IAAI,MAAM,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,QAAQ,EAAE,CAAC,MAAiB,EAAW,EAAE;QACvC,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,CAAC;QAC9D,MAAM,sBAAsB,GAAG,MAAM,CAAC,aAAa,KAAK,QAAQ,CAAC,kBAAkB,CAAC;QACpF,MAAM,iBAAiB,GAAG,MAAM,CAAC,SAAS,KAAK,KAAK,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC;QAClF,MAAM,kBAAkB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAE3F,OAAO,CAAC,eAAe,IAAI,CAAC,sBAAsB,IAAI,iBAAiB,IAAI,kBAAkB,CAAC;IAChG,CAAC;CACF,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\jwt.config.ts"], "sourcesContent": ["import { registerAs } from '@nestjs/config';\r\nimport { IsString, IsN<PERSON>ber, IsOptional, IsIn, Min, Max } from 'class-validator';\r\nimport { Transform, Type } from 'class-transformer';\r\n\r\n/**\r\n * JWT-specific configuration module\r\n * Provides secure JWT token configuration with validation\r\n */\r\n\r\n// JWT Algorithm types\r\nexport type JwtAlgorithm = 'HS256' | 'HS384' | 'HS512' | 'RS256' | 'RS384' | 'RS512';\r\n\r\n// JWT Configuration interface\r\nexport interface JwtConfig {\r\n  readonly secret: string;\r\n  readonly publicKey?: string;\r\n  readonly privateKey?: string;\r\n  readonly algorithm: JwtAlgorithm;\r\n  readonly expiresIn: string;\r\n  readonly refreshSecret: string;\r\n  readonly refreshExpiresIn: string;\r\n  readonly issuer: string;\r\n  readonly audience: string;\r\n  readonly clockTolerance: number;\r\n  readonly ignoreExpiration: boolean;\r\n  readonly ignoreNotBefore: boolean;\r\n  readonly maxAge: string;\r\n  readonly subject?: string;\r\n  readonly jwtid?: string;\r\n}\r\n\r\n// Validation class for JWT configuration\r\nexport class JwtConfigValidation {\r\n  @IsString()\r\n  @Transform(({ value }) => value || 'your-super-secret-jwt-key-change-this-in-production')\r\n  secret!: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  publicKey?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  privateKey?: string;\r\n\r\n  @IsIn(['HS256', 'HS384', 'HS512', 'RS256', 'RS384', 'RS512'])\r\n  @Transform(({ value }) => value || 'HS256')\r\n  algorithm!: JwtAlgorithm;\r\n\r\n  @IsString()\r\n  @Transform(({ value }) => value || '15m')\r\n  expiresIn!: string;\r\n\r\n  @IsString()\r\n  @Transform(({ value }) => value || 'your-super-secret-refresh-key-change-this-in-production')\r\n  refreshSecret!: string;\r\n\r\n  @IsString()\r\n  @Transform(({ value }) => value || '7d')\r\n  refreshExpiresIn!: string;\r\n\r\n  @IsString()\r\n  @Transform(({ value }) => value || 'sentinel-platform')\r\n  issuer!: string;\r\n\r\n  @IsString()\r\n  @Transform(({ value }) => value || 'sentinel-users')\r\n  audience!: string;\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  @Max(300)\r\n  @Type(() => Number)\r\n  @Transform(({ value }) => Number(value) || 0)\r\n  clockTolerance!: number;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  subject?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  jwtid?: string;\r\n}\r\n\r\n// Utility functions\r\nconst parseBoolean = (value: string | undefined, fallback = false): boolean => \r\n  value?.toLowerCase() === 'true' || fallback;\r\n\r\nconst env = {\r\n  getString: (key: string, fallback = ''): string => \r\n    process.env[key] ?? fallback,\r\n  \r\n  getNumber: (key: string, fallback = 0): number => {\r\n    const value = process.env[key];\r\n    if (!value) return fallback;\r\n    const parsed = Number(value);\r\n    return Number.isNaN(parsed) ? fallback : parsed;\r\n  },\r\n  \r\n  getBoolean: (key: string, fallback = false): boolean => \r\n    parseBoolean(process.env[key], fallback),\r\n  \r\n  isProduction: (): boolean => \r\n    process.env['NODE_ENV'] === 'production',\r\n} as const;\r\n\r\n// Configuration defaults\r\nconst DEFAULTS = {\r\n  JWT_SECRET: 'your-super-secret-jwt-key-change-this-in-production',\r\n  JWT_REFRESH_SECRET: 'your-super-secret-refresh-key-change-this-in-production',\r\n  JWT_ALGORITHM: 'HS256' as JwtAlgorithm,\r\n  JWT_EXPIRES_IN: '15m',\r\n  JWT_REFRESH_EXPIRES_IN: '7d',\r\n  JWT_ISSUER: 'sentinel-platform',\r\n  JWT_AUDIENCE: 'sentinel-users',\r\n  JWT_CLOCK_TOLERANCE: 0,\r\n  JWT_MAX_AGE: '1h',\r\n} as const;\r\n\r\n/**\r\n * JWT configuration factory\r\n * Creates JWT configuration with environment variable support and validation\r\n */\r\nexport const jwtConfig = registerAs('jwt', (): JwtConfig => {\r\n  const algorithm = (env.getString('JWT_ALGORITHM', DEFAULTS.JWT_ALGORITHM) as JwtAlgorithm);\r\n  const isRSA = algorithm.startsWith('RS');\r\n  \r\n  // For production, warn if using default secrets\r\n  if (env.isProduction()) {\r\n    const secret = env.getString('JWT_SECRET');\r\n    const refreshSecret = env.getString('JWT_REFRESH_SECRET');\r\n    \r\n    if (secret === DEFAULTS.JWT_SECRET || refreshSecret === DEFAULTS.JWT_REFRESH_SECRET) {\r\n      console.warn('⚠️  WARNING: Using default JWT secrets in production! Please set JWT_SECRET and JWT_REFRESH_SECRET environment variables.');\r\n    }\r\n  }\r\n\r\n  // Build config object with conditional properties\r\n  const baseConfig = {\r\n    secret: env.getString('JWT_SECRET', DEFAULTS.JWT_SECRET),\r\n    algorithm,\r\n    expiresIn: env.getString('JWT_EXPIRES_IN', DEFAULTS.JWT_EXPIRES_IN),\r\n    refreshSecret: env.getString('JWT_REFRESH_SECRET', DEFAULTS.JWT_REFRESH_SECRET),\r\n    refreshExpiresIn: env.getString('JWT_REFRESH_EXPIRES_IN', DEFAULTS.JWT_REFRESH_EXPIRES_IN),\r\n    issuer: env.getString('JWT_ISSUER', DEFAULTS.JWT_ISSUER),\r\n    audience: env.getString('JWT_AUDIENCE', DEFAULTS.JWT_AUDIENCE),\r\n    clockTolerance: env.getNumber('JWT_CLOCK_TOLERANCE', DEFAULTS.JWT_CLOCK_TOLERANCE),\r\n    ignoreExpiration: env.getBoolean('JWT_IGNORE_EXPIRATION', false),\r\n    ignoreNotBefore: env.getBoolean('JWT_IGNORE_NOT_BEFORE', false),\r\n    maxAge: env.getString('JWT_MAX_AGE', DEFAULTS.JWT_MAX_AGE),\r\n  };\r\n\r\n  // Build complete config object\r\n  const publicKey = isRSA ? env.getString('JWT_PUBLIC_KEY') : undefined;\r\n  const privateKey = isRSA ? env.getString('JWT_PRIVATE_KEY') : undefined;\r\n  const subject = env.getString('JWT_SUBJECT');\r\n  const jwtid = env.getString('JWT_ID');\r\n\r\n  // Warn about missing RSA keys in production\r\n  if (isRSA && env.isProduction() && (!publicKey || !privateKey)) {\r\n    console.warn('⚠️  WARNING: RSA algorithm selected but RSA keys not provided! Please set JWT_PUBLIC_KEY and JWT_PRIVATE_KEY environment variables.');\r\n  }\r\n\r\n  const config: JwtConfig = {\r\n    ...baseConfig,\r\n    ...(publicKey && { publicKey }),\r\n    ...(privateKey && { privateKey }),\r\n    ...(subject && { subject }),\r\n    ...(jwtid && { jwtid }),\r\n  };\r\n\r\n  return config;\r\n});\r\n\r\n/**\r\n * JWT configuration helper functions\r\n */\r\nexport const JwtConfigHelper = {\r\n  /**\r\n   * Get JWT sign options for the JWT service\r\n   */\r\n  getSignOptions: (config: JwtConfig) => ({\r\n    algorithm: config.algorithm,\r\n    expiresIn: config.expiresIn,\r\n    issuer: config.issuer,\r\n    audience: config.audience,\r\n    subject: config.subject,\r\n    jwtid: config.jwtid,\r\n  }),\r\n\r\n  /**\r\n   * Get JWT verify options for the JWT service\r\n   */\r\n  getVerifyOptions: (config: JwtConfig) => ({\r\n    algorithms: [config.algorithm],\r\n    issuer: config.issuer,\r\n    audience: config.audience,\r\n    clockTolerance: config.clockTolerance,\r\n    ignoreExpiration: config.ignoreExpiration,\r\n    ignoreNotBefore: config.ignoreNotBefore,\r\n    maxAge: config.maxAge,\r\n  }),\r\n\r\n  /**\r\n   * Get secret or key for signing/verifying\r\n   */\r\n  getSecretOrKey: (config: JwtConfig, isPrivate = false): string => {\r\n    if (config.algorithm.startsWith('RS')) {\r\n      return isPrivate ? (config.privateKey || config.secret) : (config.publicKey || config.secret);\r\n    }\r\n    return config.secret;\r\n  },\r\n\r\n  /**\r\n   * Validate JWT configuration\r\n   */\r\n  validate: (config: JwtConfig): string[] => {\r\n    const errors: string[] = [];\r\n\r\n    if (!config.secret) {\r\n      errors.push('JWT secret is required');\r\n    }\r\n\r\n    if (config.algorithm.startsWith('RS')) {\r\n      if (!config.publicKey) {\r\n        errors.push('Public key is required for RSA algorithms');\r\n      }\r\n      if (!config.privateKey) {\r\n        errors.push('Private key is required for RSA algorithms');\r\n      }\r\n    }\r\n\r\n    if (!config.issuer) {\r\n      errors.push('JWT issuer is required');\r\n    }\r\n\r\n    if (!config.audience) {\r\n      errors.push('JWT audience is required');\r\n    }\r\n\r\n    if (config.clockTolerance < 0 || config.clockTolerance > 300) {\r\n      errors.push('Clock tolerance must be between 0 and 300 seconds');\r\n    }\r\n\r\n    return errors;\r\n  },\r\n\r\n  /**\r\n   * Check if configuration is secure for production\r\n   */\r\n  isSecure: (config: JwtConfig): boolean => {\r\n    const isDefaultSecret = config.secret === DEFAULTS.JWT_SECRET;\r\n    const isDefaultRefreshSecret = config.refreshSecret === DEFAULTS.JWT_REFRESH_SECRET;\r\n    const isShortExpiration = config.expiresIn === '15m' || config.expiresIn === '1h';\r\n    const hasStrongAlgorithm = ['HS256', 'RS256', 'RS384', 'RS512'].includes(config.algorithm);\r\n\r\n    return !isDefaultSecret && !isDefaultRefreshSecret && isShortExpiration && hasStrongAlgorithm;\r\n  },\r\n};"], "version": 3}