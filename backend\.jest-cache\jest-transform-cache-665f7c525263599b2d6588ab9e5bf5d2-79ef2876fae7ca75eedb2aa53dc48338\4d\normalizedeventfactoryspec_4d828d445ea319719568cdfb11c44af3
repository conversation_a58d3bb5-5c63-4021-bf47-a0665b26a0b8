6ecb3a0351c4f6057ce5583e9fe1ac35
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const normalized_event_factory_1 = require("../normalized-event.factory");
const normalized_event_entity_1 = require("../../entities/normalized-event.entity");
const shared_kernel_1 = require("../../../../../shared-kernel");
const event_metadata_value_object_1 = require("../../value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../value-objects/event-metadata/event-source.value-object");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const event_status_enum_1 = require("../../enums/event-status.enum");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const event_source_type_enum_1 = require("../../enums/event-source-type.enum");
// Mock Event class for testing
class MockEvent {
    constructor(props = {}) {
        const timestamp = event_timestamp_value_object_1.EventTimestamp.create();
        const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.SIEM, 'test-siem');
        const metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
        this.id = props.id || shared_kernel_1.UniqueEntityId.create();
        this.metadata = props.metadata || metadata;
        this.type = props.type || event_type_enum_1.EventType.THREAT_DETECTED;
        this.severity = props.severity || event_severity_enum_1.EventSeverity.HIGH;
        this.status = props.status || event_status_enum_1.EventStatus.ACTIVE;
        this.processingStatus = props.processingStatus || event_processing_status_enum_1.EventProcessingStatus.RAW;
        this.rawData = props.rawData || { test: 'data' };
        this.title = props.title || 'Test Event';
        this.description = props.description;
        this.tags = props.tags || [];
        this.riskScore = props.riskScore;
        this.confidenceLevel = props.confidenceLevel;
        this.attributes = props.attributes || {};
        this.correlationId = props.correlationId;
        this.parentEventId = props.parentEventId;
    }
    isHighRisk() {
        return (this.riskScore || 0) >= 80;
    }
    isCritical() {
        return this.severity === event_severity_enum_1.EventSeverity.CRITICAL;
    }
}
describe('NormalizedEventFactory', () => {
    let mockEvent;
    let mockRule;
    beforeEach(() => {
        mockEvent = new MockEvent();
        mockRule = {
            id: 'test-rule',
            name: 'Test Rule',
            description: 'Test normalization rule',
            priority: 100,
            required: false,
        };
    });
    describe('create', () => {
        it('should create a normalized event with required properties', () => {
            // Arrange
            const options = {
                originalEvent: mockEvent,
                normalizedData: { normalized: 'data' },
                schemaVersion: '1.0.0',
            };
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.create(options);
            // Assert
            expect(normalizedEvent).toBeInstanceOf(normalized_event_entity_1.NormalizedEvent);
            expect(normalizedEvent.originalEventId).toEqual(mockEvent.id);
            expect(normalizedEvent.type).toBe(mockEvent.type);
            expect(normalizedEvent.severity).toBe(mockEvent.severity);
            expect(normalizedEvent.schemaVersion).toBe('1.0.0');
            expect(normalizedEvent.normalizedData).toEqual({ normalized: 'data' });
        });
        it('should use default values when not provided', () => {
            // Arrange
            const options = {
                originalEvent: mockEvent,
                normalizedData: { normalized: 'data' },
                schemaVersion: '1.0.0',
            };
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.create(options);
            // Assert
            expect(normalizedEvent.status).toBe(event_status_enum_1.EventStatus.ACTIVE);
            expect(normalizedEvent.processingStatus).toBe(event_processing_status_enum_1.EventProcessingStatus.NORMALIZED);
            expect(normalizedEvent.normalizationStatus).toBe(normalized_event_entity_1.NormalizationStatus.PENDING);
            expect(normalizedEvent.appliedRules).toEqual([]);
        });
        it('should override properties when provided', () => {
            // Arrange
            const options = {
                originalEvent: mockEvent,
                normalizedData: { normalized: 'data' },
                schemaVersion: '1.0.0',
                type: event_type_enum_1.EventType.MALWARE_DETECTED,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                title: 'Custom Title',
                appliedRules: [mockRule],
                normalizationStatus: normalized_event_entity_1.NormalizationStatus.COMPLETED,
            };
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.create(options);
            // Assert
            expect(normalizedEvent.type).toBe(event_type_enum_1.EventType.MALWARE_DETECTED);
            expect(normalizedEvent.severity).toBe(event_severity_enum_1.EventSeverity.CRITICAL);
            expect(normalizedEvent.title).toBe('Custom Title');
            expect(normalizedEvent.appliedRules).toEqual([mockRule]);
            expect(normalizedEvent.normalizationStatus).toBe(normalized_event_entity_1.NormalizationStatus.COMPLETED);
        });
        it('should merge attributes from original event and options', () => {
            // Arrange
            mockEvent.attributes = { original: 'value' };
            const options = {
                originalEvent: mockEvent,
                normalizedData: { normalized: 'data' },
                schemaVersion: '1.0.0',
                attributes: { additional: 'value' },
            };
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.create(options);
            // Assert
            expect(normalizedEvent.attributes).toEqual({
                original: 'value',
                additional: 'value',
            });
        });
    });
    describe('createWithNormalization', () => {
        it('should create normalized event with automatic normalization', () => {
            // Arrange
            const config = {
                defaultSchemaVersion: '2.0.0',
                availableRules: [mockRule],
                minDataQualityThreshold: 70,
            };
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.createWithNormalization(mockEvent, config);
            // Assert
            expect(normalizedEvent).toBeInstanceOf(normalized_event_entity_1.NormalizedEvent);
            expect(normalizedEvent.schemaVersion).toBe('2.0.0');
            expect(normalizedEvent.dataQualityScore).toBeDefined();
        });
        it('should determine manual review requirement for high-risk events', () => {
            // Arrange
            mockEvent.riskScore = 85;
            const config = {
                requireManualReviewForHighRisk: true,
            };
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.createWithNormalization(mockEvent, config);
            // Assert
            expect(normalizedEvent.requiresManualReview).toBe(true);
        });
        it('should determine manual review requirement for critical events', () => {
            // Arrange
            mockEvent.severity = event_severity_enum_1.EventSeverity.CRITICAL;
            const config = {
                requireManualReviewForCritical: true,
            };
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.createWithNormalization(mockEvent, config);
            // Assert
            expect(normalizedEvent.requiresManualReview).toBe(true);
        });
    });
    describe('fromRawData', () => {
        it('should create normalized event from raw data', () => {
            // Arrange
            const rawData = {
                event_type: 'threat_detected',
                severity: 'high',
                title: 'Raw Event Title',
                timestamp: new Date().toISOString(),
            };
            const originalEventId = shared_kernel_1.UniqueEntityId.create();
            const schemaVersion = '1.0.0';
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.fromRawData(rawData, originalEventId, schemaVersion);
            // Assert
            expect(normalizedEvent).toBeInstanceOf(normalized_event_entity_1.NormalizedEvent);
            expect(normalizedEvent.originalEventId).toEqual(originalEventId);
            expect(normalizedEvent.schemaVersion).toBe(schemaVersion);
            expect(normalizedEvent.originalData).toEqual(rawData);
        });
        it('should infer event properties from raw data', () => {
            // Arrange
            const rawData = {
                event_type: 'malware_detected',
                severity: 'critical',
                title: 'Malware Alert',
                description: 'Malware detected on system',
                tags: ['malware', 'alert'],
                risk_score: 90,
            };
            const originalEventId = shared_kernel_1.UniqueEntityId.create();
            const schemaVersion = '1.0.0';
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.fromRawData(rawData, originalEventId, schemaVersion);
            // Assert
            expect(normalizedEvent.type).toBe(event_type_enum_1.EventType.MALWARE_DETECTED);
            expect(normalizedEvent.severity).toBe(event_severity_enum_1.EventSeverity.CRITICAL);
            expect(normalizedEvent.title).toBe('Malware Alert');
            expect(normalizedEvent.description).toBe('Malware detected on system');
            expect(normalizedEvent.tags).toEqual(['malware', 'alert']);
            expect(normalizedEvent.riskScore).toBe(90);
        });
    });
    describe('createBatch', () => {
        it('should create multiple normalized events successfully', () => {
            // Arrange
            const events = [
                new MockEvent({ title: 'Event 1' }),
                new MockEvent({ title: 'Event 2' }),
                new MockEvent({ title: 'Event 3' }),
            ];
            const options = {
                events: events,
                schemaVersion: '1.0.0',
                rules: [mockRule],
            };
            // Act
            const result = normalized_event_factory_1.NormalizedEventFactory.createBatch(options);
            // Assert
            expect(result.successful).toHaveLength(3);
            expect(result.failed).toHaveLength(0);
            expect(result.summary.total).toBe(3);
            expect(result.summary.successful).toBe(3);
            expect(result.summary.failed).toBe(0);
            expect(result.summary.processingTimeMs).toBeGreaterThan(0);
        });
        it('should handle failures and continue processing', () => {
            // Arrange
            const events = [
                new MockEvent({ title: 'Event 1' }),
                null, // This will cause an error
                new MockEvent({ title: 'Event 3' }),
            ];
            const options = {
                events: events,
                schemaVersion: '1.0.0',
                rules: [mockRule],
                stopOnFailure: false,
            };
            // Act
            const result = normalized_event_factory_1.NormalizedEventFactory.createBatch(options);
            // Assert
            expect(result.successful).toHaveLength(2);
            expect(result.failed).toHaveLength(1);
            expect(result.summary.total).toBe(3);
            expect(result.summary.successful).toBe(2);
            expect(result.summary.failed).toBe(1);
        });
        it('should stop on first failure when configured', () => {
            // Arrange
            const events = [
                new MockEvent({ title: 'Event 1' }),
                null, // This will cause an error
                new MockEvent({ title: 'Event 3' }),
            ];
            const options = {
                events: events,
                schemaVersion: '1.0.0',
                rules: [mockRule],
                stopOnFailure: true,
            };
            // Act
            const result = normalized_event_factory_1.NormalizedEventFactory.createBatch(options);
            // Assert
            expect(result.successful).toHaveLength(1);
            expect(result.failed).toHaveLength(1);
            expect(result.summary.successful).toBe(1);
            expect(result.summary.failed).toBe(1);
        });
    });
    describe('createForTesting', () => {
        it('should create normalized event for testing with defaults', () => {
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.createForTesting();
            // Assert
            expect(normalizedEvent).toBeInstanceOf(normalized_event_entity_1.NormalizedEvent);
            expect(normalizedEvent.normalizationStatus).toBe(normalized_event_entity_1.NormalizationStatus.COMPLETED);
            expect(normalizedEvent.dataQualityScore).toBe(85);
            expect(normalizedEvent.normalizedData).toHaveProperty('normalized', true);
        });
        it('should create normalized event for testing with overrides', () => {
            // Arrange
            const overrides = {
                originalEvent: mockEvent,
                normalizationStatus: normalized_event_entity_1.NormalizationStatus.FAILED,
                dataQualityScore: 45,
            };
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.createForTesting(overrides);
            // Assert
            expect(normalizedEvent.originalEventId).toEqual(mockEvent.id);
            expect(normalizedEvent.normalizationStatus).toBe(normalized_event_entity_1.NormalizationStatus.FAILED);
            expect(normalizedEvent.dataQualityScore).toBe(45);
        });
    });
    describe('normalization rules application', () => {
        it('should apply timestamp normalization rule', () => {
            // This test would verify the private method behavior
            // In a real implementation, you might expose this for testing
            // or test it through the public interface
            expect(true).toBe(true); // Placeholder
        });
        it('should apply severity normalization rule', () => {
            // This test would verify the private method behavior
            expect(true).toBe(true); // Placeholder
        });
        it('should apply field mapping rule', () => {
            // This test would verify the private method behavior
            expect(true).toBe(true); // Placeholder
        });
    });
    describe('data quality calculation', () => {
        it('should calculate high data quality score for successful normalization', () => {
            // Arrange
            const config = {
                availableRules: [mockRule],
            };
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.createWithNormalization(mockEvent, config);
            // Assert
            expect(normalizedEvent.dataQualityScore).toBeGreaterThan(60);
        });
        it('should calculate lower data quality score for failed normalization', () => {
            // This would test the private method behavior
            // In practice, you might create a scenario that causes validation errors
            expect(true).toBe(true); // Placeholder
        });
    });
    describe('event type mapping', () => {
        it('should map threat event types correctly', () => {
            // Arrange
            const rawData = { event_type: 'threat_detected' };
            const originalEventId = shared_kernel_1.UniqueEntityId.create();
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.fromRawData(rawData, originalEventId, '1.0.0');
            // Assert
            expect(normalizedEvent.type).toBe(event_type_enum_1.EventType.THREAT_DETECTED);
        });
        it('should map malware event types correctly', () => {
            // Arrange
            const rawData = { event_type: 'malware_found' };
            const originalEventId = shared_kernel_1.UniqueEntityId.create();
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.fromRawData(rawData, originalEventId, '1.0.0');
            // Assert
            expect(normalizedEvent.type).toBe(event_type_enum_1.EventType.MALWARE_DETECTED);
        });
        it('should default to custom event type for unknown types', () => {
            // Arrange
            const rawData = { event_type: 'unknown_type' };
            const originalEventId = shared_kernel_1.UniqueEntityId.create();
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.fromRawData(rawData, originalEventId, '1.0.0');
            // Assert
            expect(normalizedEvent.type).toBe(event_type_enum_1.EventType.CUSTOM);
        });
    });
    describe('severity mapping', () => {
        it('should map critical severity correctly', () => {
            // Arrange
            const rawData = { severity: 'critical' };
            const originalEventId = shared_kernel_1.UniqueEntityId.create();
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.fromRawData(rawData, originalEventId, '1.0.0');
            // Assert
            expect(normalizedEvent.severity).toBe(event_severity_enum_1.EventSeverity.CRITICAL);
        });
        it('should map high severity correctly', () => {
            // Arrange
            const rawData = { severity: 'high' };
            const originalEventId = shared_kernel_1.UniqueEntityId.create();
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.fromRawData(rawData, originalEventId, '1.0.0');
            // Assert
            expect(normalizedEvent.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
        });
        it('should default to medium severity for unknown severities', () => {
            // Arrange
            const rawData = { severity: 'unknown' };
            const originalEventId = shared_kernel_1.UniqueEntityId.create();
            // Act
            const normalizedEvent = normalized_event_factory_1.NormalizedEventFactory.fromRawData(rawData, originalEventId, '1.0.0');
            // Assert
            expect(normalizedEvent.severity).toBe(event_severity_enum_1.EventSeverity.MEDIUM);
        });
    });
    describe('error handling', () => {
        it('should handle missing required properties gracefully', () => {
            // Arrange
            const invalidOptions = {
                // Missing originalEvent
                normalizedData: { normalized: 'data' },
                schemaVersion: '1.0.0',
            };
            // Act & Assert
            expect(() => normalized_event_factory_1.NormalizedEventFactory.create(invalidOptions)).toThrow();
        });
        it('should handle invalid schema version', () => {
            // Arrange
            const options = {
                originalEvent: mockEvent,
                normalizedData: { normalized: 'data' },
                schemaVersion: '', // Empty schema version
            };
            // Act & Assert
            expect(() => normalized_event_factory_1.NormalizedEventFactory.create(options)).toThrow();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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