{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\event.entity.ts", "mappings": ";;;AAAA,6DAA8E;AAG9E,sEAA6D;AAC7D,kEAAyD;AACzD,wFAA8E;AAC9E,qFAA+E;AAC/E,mGAA4F;AAC5F,yHAAiH;AAkDjH;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAa,KAAM,SAAQ,iCAA6B;IAMtD,YAAY,KAAiB,EAAE,EAAmB;QAChD,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACjB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,KAAiB,EAAE,EAAmB;QAClD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAEnC,sCAAsC;QACtC,KAAK,CAAC,cAAc,CAAC,IAAI,oDAAuB,CAC9C,KAAK,CAAC,EAAE,EACR;YACE,SAAS,EAAE,KAAK,CAAC,IAAI;YACrB,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM;YAC7B,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,SAAS;YACnC,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,SAAS,EAAE,KAAK,CAAC,SAAS;SAC3B,CACF,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAES,kBAAkB;QAC1B,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,CAAC,gBAAgB,aAAa,CAAC,CAAC;QACpF,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC3F,MAAM,IAAI,KAAK,CAAC,mCAAmC,KAAK,CAAC,sBAAsB,aAAa,CAAC,CAAC;QAChG,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC;YACnG,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,EAAE,CAAC;YACrH,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;YAC/D,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,CAAC,QAAQ,OAAO,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;YACrF,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,8BAA8B;QAC9B,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAEO,yBAAyB;QAC/B,8DAA8D;QAC9D,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,+BAAW,CAAC,QAAQ,EAAE,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAED,gEAAgE;QAChE,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,+BAAW,CAAC,QAAQ,EAAE,+BAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YACrG,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;QACpF,CAAC;QAED,2DAA2D;QAC3D,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,+BAAW,CAAC,QAAQ;YAC1C,CAAC,CAAC,oDAAqB,CAAC,QAAQ,EAAE,oDAAqB,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC5G,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAED,UAAU;IACV,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,OAAO;QACT,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IACnC,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAClF,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACrD,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IACpC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACnE,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;IACxC,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IACpC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IACpC,CAAC;IAED,mBAAmB;IAEnB;;OAEG;IACH,YAAY,CAAC,SAAsB,EAAE,SAAkB,EAAE,KAAc;QACrE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACpC,OAAO,CAAC,mBAAmB;QAC7B,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;QAE9B,oBAAoB;QACpB,IAAI,SAAS,KAAK,+BAAW,CAAC,QAAQ,EAAE,CAAC;YACvC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YACnC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC;YAClC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;QACrC,CAAC;aAAM,IAAI,SAAS,KAAK,+BAAW,CAAC,QAAQ,EAAE,CAAC;YAC9C,2BAA2B;YAC3B,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC;YAClC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC;YAClC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,iEAA6B,CACnD,IAAI,CAAC,EAAE,EACP;YACE,SAAS;YACT,SAAS;YACT,SAAS;YACT,KAAK;YACL,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,SAAgC;QACrD,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC9C,OAAO,CAAC,mBAAmB;QAC7B,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,SAAS,CAAC;QACxC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;QAExC,IAAI,CAAC,cAAc,CAAC,IAAI,sFAAuC,CAC7D,IAAI,CAAC,EAAE,EACP;YACE,SAAS;YACT,SAAS;YACT,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,cAAmC;QACtD,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,EAAE,GAAG,cAAc,EAAE,CAAC;QAElD,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,oDAAqB,CAAC,GAAG,EAAE,CAAC;YAC9D,IAAI,CAAC,sBAAsB,CAAC,oDAAqB,CAAC,UAAU,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,SAAiB;QAC/B,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,eAAuB;QAC3C,IAAI,eAAe,GAAG,CAAC,IAAI,eAAe,GAAG,GAAG,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAAc;QACpB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;QAC1C,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,WAAW,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB;QAE7E,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,CAAC,QAAQ,OAAO,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAc;QACvB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,aAAqB;QACpC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,aAA6B;QAC1C,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,KAAc;QACpC,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACzE,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;QAExC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACzC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,SAAS,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,UAA+B;QAC9C,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,GAAG,UAAU,EAAE,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,GAAW;QACzB,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED,gBAAgB;IAEhB;;OAEG;IACH,QAAQ;QACN,OAAO,CAAC,CAAC,+BAAW,CAAC,QAAQ,EAAE,+BAAW,CAAC,MAAM,EAAE,+BAAW,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC7G,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,+BAAW,CAAC,QAAQ,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,CAAC,mCAAa,CAAC,IAAI,EAAE,mCAAa,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,oDAAqB,CAAC,MAAM,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,kBAAkB,IAAI,KAAK,CAAC,uBAAuB,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,WAAmB,OAAO;QACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,QAAQ,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,cAAsB,QAAQ;QACpC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,GAAG,WAAW,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,IAAc;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAc;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,UAAU;QAaR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YACtB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;YAC/B,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE;YAClB,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE;YACvC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;SAC9B,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE;YACtC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;YACzC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;YAC/B,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;YAC3C,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;YACvC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,QAAQ,EAAE;YACnD,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;YACjD,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;YACnD,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,WAAW,EAAE;YAC1D,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,EAAE;YAChD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;YAC3C,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;SAC3B,CAAC;IACJ,CAAC;;AA/gBH,sBAghBC;AA/gByB,6BAAuB,GAAG,CAAC,CAAC;AAC5B,sBAAgB,GAAG,GAAG,CAAC;AACvB,4BAAsB,GAAG,IAAI,CAAC;AAC9B,cAAQ,GAAG,EAAE,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\event.entity.ts"], "sourcesContent": ["import { BaseAggregateRoot, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventMetadata } from '../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../enums/event-processing-status.enum';\r\nimport { EventCreatedDomainEvent } from '../events/event-created.domain-event';\r\nimport { EventStatusChangedDomainEvent } from '../events/event-status-changed.domain-event';\r\nimport { EventProcessingStatusChangedDomainEvent } from '../events/event-processing-status-changed.domain-event';\r\n\r\n/**\r\n * Event Entity Properties\r\n */\r\nexport interface EventProps {\r\n  /** Event metadata containing timestamp, source, and processing information */\r\n  metadata: EventMetadata;\r\n  /** Type of the security event */\r\n  type: EventType;\r\n  /** Severity level of the event */\r\n  severity: EventSeverity;\r\n  /** Current status of the event */\r\n  status: EventStatus;\r\n  /** Current processing status in the pipeline */\r\n  processingStatus: EventProcessingStatus;\r\n  /** Raw event data as received from source */\r\n  rawData: Record<string, any>;\r\n  /** Normalized event data in standard format */\r\n  normalizedData?: Record<string, any>;\r\n  /** Event title/summary */\r\n  title: string;\r\n  /** Detailed description of the event */\r\n  description?: string;\r\n  /** Tags for categorization and filtering */\r\n  tags?: string[];\r\n  /** Risk score (0-100) */\r\n  riskScore?: number;\r\n  /** Confidence level (0-100) */\r\n  confidenceLevel?: number;\r\n  /** Additional custom attributes */\r\n  attributes?: Record<string, any>;\r\n  /** Event correlation ID for grouping related events */\r\n  correlationId?: string;\r\n  /** Parent event ID if this is a child event */\r\n  parentEventId?: UniqueEntityId;\r\n  /** Number of times this event has been processed */\r\n  processingAttempts?: number;\r\n  /** Last processing error if any */\r\n  lastProcessingError?: string;\r\n  /** When the event was last processed */\r\n  lastProcessedAt?: Date;\r\n  /** When the event was resolved */\r\n  resolvedAt?: Date;\r\n  /** Who resolved the event */\r\n  resolvedBy?: string;\r\n  /** Resolution notes */\r\n  resolutionNotes?: string;\r\n}\r\n\r\n/**\r\n * Event Entity\r\n * \r\n * Represents a security event in the system. Events are the core domain objects\r\n * that capture security-related activities, incidents, and observations.\r\n * \r\n * Key responsibilities:\r\n * - Maintain event state and lifecycle\r\n * - Enforce business rules and invariants\r\n * - Generate domain events for state changes\r\n * - Provide event analysis and scoring capabilities\r\n * - Support event correlation and relationships\r\n * \r\n * Business Rules:\r\n * - Events must have valid metadata and type\r\n * - Status transitions must follow defined workflows\r\n * - Risk scores must be between 0-100\r\n * - Processing attempts are tracked and limited\r\n * - Resolution requires proper authorization\r\n */\r\nexport class Event extends BaseAggregateRoot<EventProps> {\r\n  private static readonly MAX_PROCESSING_ATTEMPTS = 5;\r\n  private static readonly MAX_TITLE_LENGTH = 200;\r\n  private static readonly MAX_DESCRIPTION_LENGTH = 2000;\r\n  private static readonly MAX_TAGS = 20;\r\n\r\n  constructor(props: EventProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Create a new Event\r\n   */\r\n  static create(props: EventProps, id?: UniqueEntityId): Event {\r\n    const event = new Event(props, id);\r\n    \r\n    // Add domain event for event creation\r\n    event.addDomainEvent(new EventCreatedDomainEvent(\r\n      event.id,\r\n      {\r\n        eventType: props.type,\r\n        severity: props.severity,\r\n        source: props.metadata.source,\r\n        timestamp: props.metadata.timestamp,\r\n        title: props.title,\r\n        riskScore: props.riskScore,\r\n      }\r\n    ));\r\n\r\n    return event;\r\n  }\r\n\r\n  protected validateInvariants(): void {\r\n    super.validateInvariants();\r\n\r\n    if (!this.props.metadata) {\r\n      throw new Error('Event must have metadata');\r\n    }\r\n\r\n    if (!this.props.type) {\r\n      throw new Error('Event must have a type');\r\n    }\r\n\r\n    if (!this.props.severity) {\r\n      throw new Error('Event must have a severity');\r\n    }\r\n\r\n    if (!this.props.status) {\r\n      throw new Error('Event must have a status');\r\n    }\r\n\r\n    if (!this.props.processingStatus) {\r\n      throw new Error('Event must have a processing status');\r\n    }\r\n\r\n    if (!this.props.rawData) {\r\n      throw new Error('Event must have raw data');\r\n    }\r\n\r\n    if (!this.props.title || this.props.title.trim().length === 0) {\r\n      throw new Error('Event must have a non-empty title');\r\n    }\r\n\r\n    if (this.props.title.length > Event.MAX_TITLE_LENGTH) {\r\n      throw new Error(`Event title cannot exceed ${Event.MAX_TITLE_LENGTH} characters`);\r\n    }\r\n\r\n    if (this.props.description && this.props.description.length > Event.MAX_DESCRIPTION_LENGTH) {\r\n      throw new Error(`Event description cannot exceed ${Event.MAX_DESCRIPTION_LENGTH} characters`);\r\n    }\r\n\r\n    if (this.props.riskScore !== undefined && (this.props.riskScore < 0 || this.props.riskScore > 100)) {\r\n      throw new Error('Risk score must be between 0 and 100');\r\n    }\r\n\r\n    if (this.props.confidenceLevel !== undefined && (this.props.confidenceLevel < 0 || this.props.confidenceLevel > 100)) {\r\n      throw new Error('Confidence level must be between 0 and 100');\r\n    }\r\n\r\n    if (this.props.tags && this.props.tags.length > Event.MAX_TAGS) {\r\n      throw new Error(`Event cannot have more than ${Event.MAX_TAGS} tags`);\r\n    }\r\n\r\n    if (this.props.processingAttempts !== undefined && this.props.processingAttempts < 0) {\r\n      throw new Error('Processing attempts cannot be negative');\r\n    }\r\n\r\n    // Validate status consistency\r\n    this.validateStatusConsistency();\r\n  }\r\n\r\n  private validateStatusConsistency(): void {\r\n    // If event is resolved, it should have resolution information\r\n    if (this.props.status === EventStatus.RESOLVED) {\r\n      if (!this.props.resolvedAt) {\r\n        throw new Error('Resolved events must have a resolution timestamp');\r\n      }\r\n    }\r\n\r\n    // If event has resolution info, it should be resolved or closed\r\n    if (this.props.resolvedAt && ![EventStatus.RESOLVED, EventStatus.CLOSED].includes(this.props.status)) {\r\n      throw new Error('Only resolved or closed events can have resolution information');\r\n    }\r\n\r\n    // Processing status should be consistent with event status\r\n    if (this.props.status === EventStatus.RESOLVED && \r\n        ![EventProcessingStatus.RESOLVED, EventProcessingStatus.ARCHIVED].includes(this.props.processingStatus)) {\r\n      throw new Error('Resolved events must have resolved or archived processing status');\r\n    }\r\n  }\r\n\r\n  // Getters\r\n  get metadata(): EventMetadata {\r\n    return this.props.metadata;\r\n  }\r\n\r\n  get type(): EventType {\r\n    return this.props.type;\r\n  }\r\n\r\n  get severity(): EventSeverity {\r\n    return this.props.severity;\r\n  }\r\n\r\n  get status(): EventStatus {\r\n    return this.props.status;\r\n  }\r\n\r\n  get processingStatus(): EventProcessingStatus {\r\n    return this.props.processingStatus;\r\n  }\r\n\r\n  get rawData(): Record<string, any> {\r\n    return { ...this.props.rawData };\r\n  }\r\n\r\n  get normalizedData(): Record<string, any> | undefined {\r\n    return this.props.normalizedData ? { ...this.props.normalizedData } : undefined;\r\n  }\r\n\r\n  get title(): string {\r\n    return this.props.title;\r\n  }\r\n\r\n  get description(): string | undefined {\r\n    return this.props.description;\r\n  }\r\n\r\n  get tags(): string[] {\r\n    return this.props.tags ? [...this.props.tags] : [];\r\n  }\r\n\r\n  get riskScore(): number | undefined {\r\n    return this.props.riskScore;\r\n  }\r\n\r\n  get confidenceLevel(): number | undefined {\r\n    return this.props.confidenceLevel;\r\n  }\r\n\r\n  get attributes(): Record<string, any> {\r\n    return this.props.attributes ? { ...this.props.attributes } : {};\r\n  }\r\n\r\n  get correlationId(): string | undefined {\r\n    return this.props.correlationId;\r\n  }\r\n\r\n  get parentEventId(): UniqueEntityId | undefined {\r\n    return this.props.parentEventId;\r\n  }\r\n\r\n  get processingAttempts(): number {\r\n    return this.props.processingAttempts || 0;\r\n  }\r\n\r\n  get lastProcessingError(): string | undefined {\r\n    return this.props.lastProcessingError;\r\n  }\r\n\r\n  get lastProcessedAt(): Date | undefined {\r\n    return this.props.lastProcessedAt;\r\n  }\r\n\r\n  get resolvedAt(): Date | undefined {\r\n    return this.props.resolvedAt;\r\n  }\r\n\r\n  get resolvedBy(): string | undefined {\r\n    return this.props.resolvedBy;\r\n  }\r\n\r\n  get resolutionNotes(): string | undefined {\r\n    return this.props.resolutionNotes;\r\n  }\r\n\r\n  // Business methods\r\n\r\n  /**\r\n   * Change event status\r\n   */\r\n  changeStatus(newStatus: EventStatus, changedBy?: string, notes?: string): void {\r\n    if (this.props.status === newStatus) {\r\n      return; // No change needed\r\n    }\r\n\r\n    const oldStatus = this.props.status;\r\n    this.props.status = newStatus;\r\n\r\n    // Handle resolution\r\n    if (newStatus === EventStatus.RESOLVED) {\r\n      this.props.resolvedAt = new Date();\r\n      this.props.resolvedBy = changedBy;\r\n      this.props.resolutionNotes = notes;\r\n    } else if (oldStatus === EventStatus.RESOLVED) {\r\n      // Reopening resolved event\r\n      this.props.resolvedAt = undefined;\r\n      this.props.resolvedBy = undefined;\r\n      this.props.resolutionNotes = undefined;\r\n    }\r\n\r\n    this.addDomainEvent(new EventStatusChangedDomainEvent(\r\n      this.id,\r\n      {\r\n        oldStatus,\r\n        newStatus,\r\n        changedBy,\r\n        notes,\r\n        timestamp: new Date(),\r\n      }\r\n    ));\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Change processing status\r\n   */\r\n  changeProcessingStatus(newStatus: EventProcessingStatus): void {\r\n    if (this.props.processingStatus === newStatus) {\r\n      return; // No change needed\r\n    }\r\n\r\n    const oldStatus = this.props.processingStatus;\r\n    this.props.processingStatus = newStatus;\r\n    this.props.lastProcessedAt = new Date();\r\n\r\n    this.addDomainEvent(new EventProcessingStatusChangedDomainEvent(\r\n      this.id,\r\n      {\r\n        oldStatus,\r\n        newStatus,\r\n        timestamp: new Date(),\r\n      }\r\n    ));\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Update normalized data\r\n   */\r\n  updateNormalizedData(normalizedData: Record<string, any>): void {\r\n    this.props.normalizedData = { ...normalizedData };\r\n    \r\n    if (this.props.processingStatus === EventProcessingStatus.RAW) {\r\n      this.changeProcessingStatus(EventProcessingStatus.NORMALIZED);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update risk score\r\n   */\r\n  updateRiskScore(riskScore: number): void {\r\n    if (riskScore < 0 || riskScore > 100) {\r\n      throw new Error('Risk score must be between 0 and 100');\r\n    }\r\n\r\n    this.props.riskScore = riskScore;\r\n  }\r\n\r\n  /**\r\n   * Update confidence level\r\n   */\r\n  updateConfidenceLevel(confidenceLevel: number): void {\r\n    if (confidenceLevel < 0 || confidenceLevel > 100) {\r\n      throw new Error('Confidence level must be between 0 and 100');\r\n    }\r\n\r\n    this.props.confidenceLevel = confidenceLevel;\r\n  }\r\n\r\n  /**\r\n   * Add tags\r\n   */\r\n  addTags(tags: string[]): void {\r\n    const currentTags = this.props.tags || [];\r\n    const newTags = [...new Set([...currentTags, ...tags])]; // Remove duplicates\r\n\r\n    if (newTags.length > Event.MAX_TAGS) {\r\n      throw new Error(`Event cannot have more than ${Event.MAX_TAGS} tags`);\r\n    }\r\n\r\n    this.props.tags = newTags;\r\n  }\r\n\r\n  /**\r\n   * Remove tags\r\n   */\r\n  removeTags(tags: string[]): void {\r\n    if (!this.props.tags) {\r\n      return;\r\n    }\r\n\r\n    this.props.tags = this.props.tags.filter(tag => !tags.includes(tag));\r\n  }\r\n\r\n  /**\r\n   * Set correlation ID\r\n   */\r\n  setCorrelationId(correlationId: string): void {\r\n    this.props.correlationId = correlationId;\r\n  }\r\n\r\n  /**\r\n   * Set parent event\r\n   */\r\n  setParentEvent(parentEventId: UniqueEntityId): void {\r\n    this.props.parentEventId = parentEventId;\r\n  }\r\n\r\n  /**\r\n   * Record processing attempt\r\n   */\r\n  recordProcessingAttempt(error?: string): void {\r\n    this.props.processingAttempts = (this.props.processingAttempts || 0) + 1;\r\n    this.props.lastProcessedAt = new Date();\r\n    \r\n    if (error) {\r\n      this.props.lastProcessingError = error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear processing error\r\n   */\r\n  clearProcessingError(): void {\r\n    this.props.lastProcessingError = undefined;\r\n  }\r\n\r\n  /**\r\n   * Update attributes\r\n   */\r\n  updateAttributes(attributes: Record<string, any>): void {\r\n    this.props.attributes = { ...this.props.attributes, ...attributes };\r\n  }\r\n\r\n  /**\r\n   * Remove attribute\r\n   */\r\n  removeAttribute(key: string): void {\r\n    if (this.props.attributes) {\r\n      delete this.props.attributes[key];\r\n    }\r\n  }\r\n\r\n  // Query methods\r\n\r\n  /**\r\n   * Check if event is active (not resolved or closed)\r\n   */\r\n  isActive(): boolean {\r\n    return ![EventStatus.RESOLVED, EventStatus.CLOSED, EventStatus.FALSE_POSITIVE].includes(this.props.status);\r\n  }\r\n\r\n  /**\r\n   * Check if event is resolved\r\n   */\r\n  isResolved(): boolean {\r\n    return this.props.status === EventStatus.RESOLVED;\r\n  }\r\n\r\n  /**\r\n   * Check if event is high severity\r\n   */\r\n  isHighSeverity(): boolean {\r\n    return [EventSeverity.HIGH, EventSeverity.CRITICAL].includes(this.props.severity);\r\n  }\r\n\r\n  /**\r\n   * Check if event is critical\r\n   */\r\n  isCritical(): boolean {\r\n    return this.props.severity === EventSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if event has high risk score\r\n   */\r\n  isHighRisk(): boolean {\r\n    return (this.props.riskScore || 0) >= 70;\r\n  }\r\n\r\n  /**\r\n   * Check if event processing has failed\r\n   */\r\n  hasProcessingFailed(): boolean {\r\n    return this.props.processingStatus === EventProcessingStatus.FAILED;\r\n  }\r\n\r\n  /**\r\n   * Check if event has exceeded max processing attempts\r\n   */\r\n  hasExceededMaxAttempts(): boolean {\r\n    return this.processingAttempts >= Event.MAX_PROCESSING_ATTEMPTS;\r\n  }\r\n\r\n  /**\r\n   * Check if event is recent\r\n   */\r\n  isRecent(withinMs: number = 3600000): boolean { // Default 1 hour\r\n    return this.metadata.timestamp.getAge() <= withinMs;\r\n  }\r\n\r\n  /**\r\n   * Check if event is stale\r\n   */\r\n  isStale(olderThanMs: number = 86400000): boolean { // Default 24 hours\r\n    return this.metadata.timestamp.getAge() > olderThanMs;\r\n  }\r\n\r\n  /**\r\n   * Check if event has specific tag\r\n   */\r\n  hasTag(tag: string): boolean {\r\n    return this.tags.includes(tag);\r\n  }\r\n\r\n  /**\r\n   * Check if event has any of the specified tags\r\n   */\r\n  hasAnyTag(tags: string[]): boolean {\r\n    return tags.some(tag => this.hasTag(tag));\r\n  }\r\n\r\n  /**\r\n   * Check if event has all of the specified tags\r\n   */\r\n  hasAllTags(tags: string[]): boolean {\r\n    return tags.every(tag => this.hasTag(tag));\r\n  }\r\n\r\n  /**\r\n   * Get event age in milliseconds\r\n   */\r\n  getAge(): number {\r\n    return this.metadata.timestamp.getAge();\r\n  }\r\n\r\n  /**\r\n   * Get processing delay\r\n   */\r\n  getProcessingDelay(): number | null {\r\n    return this.metadata.timestamp.getProcessingDelay();\r\n  }\r\n\r\n  /**\r\n   * Get event summary for display\r\n   */\r\n  getSummary(): {\r\n    id: string;\r\n    title: string;\r\n    type: EventType;\r\n    severity: EventSeverity;\r\n    status: EventStatus;\r\n    processingStatus: EventProcessingStatus;\r\n    riskScore?: number;\r\n    age: number;\r\n    source: string;\r\n    isActive: boolean;\r\n    isHighRisk: boolean;\r\n  } {\r\n    return {\r\n      id: this.id.toString(),\r\n      title: this.props.title,\r\n      type: this.props.type,\r\n      severity: this.props.severity,\r\n      status: this.props.status,\r\n      processingStatus: this.props.processingStatus,\r\n      riskScore: this.props.riskScore,\r\n      age: this.getAge(),\r\n      source: this.metadata.source.toString(),\r\n      isActive: this.isActive(),\r\n      isHighRisk: this.isHighRisk(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      metadata: this.props.metadata.toJSON(),\r\n      type: this.props.type,\r\n      severity: this.props.severity,\r\n      status: this.props.status,\r\n      processingStatus: this.props.processingStatus,\r\n      rawData: this.props.rawData,\r\n      normalizedData: this.props.normalizedData,\r\n      title: this.props.title,\r\n      description: this.props.description,\r\n      tags: this.props.tags,\r\n      riskScore: this.props.riskScore,\r\n      confidenceLevel: this.props.confidenceLevel,\r\n      attributes: this.props.attributes,\r\n      correlationId: this.props.correlationId,\r\n      parentEventId: this.props.parentEventId?.toString(),\r\n      processingAttempts: this.props.processingAttempts,\r\n      lastProcessingError: this.props.lastProcessingError,\r\n      lastProcessedAt: this.props.lastProcessedAt?.toISOString(),\r\n      resolvedAt: this.props.resolvedAt?.toISOString(),\r\n      resolvedBy: this.props.resolvedBy,\r\n      resolutionNotes: this.props.resolutionNotes,\r\n      summary: this.getSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}