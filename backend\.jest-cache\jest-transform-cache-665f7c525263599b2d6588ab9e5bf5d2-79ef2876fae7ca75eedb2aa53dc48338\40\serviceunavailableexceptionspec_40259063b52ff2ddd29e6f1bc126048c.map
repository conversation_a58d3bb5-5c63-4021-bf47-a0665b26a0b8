{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\service-unavailable.exception.spec.ts", "mappings": ";;AAAA,kGAA6F;AAE7F,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;IAC3C,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,SAAS,GAAG,IAAI,2DAA2B,CAC/C,iBAAiB,EACjB,aAAa,EACb,aAAa,CACd,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,2DAA2B,CAAC,CAAC;YAC9D,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACnD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,qBAAqB,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC/D,MAAM,iBAAiB,GAAG;gBACxB,KAAK,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;gBACvC,GAAG,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;gBACrC,WAAW,EAAE,uBAAuB;aACrC,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,2DAA2B,CAC/C,iBAAiB,EACjB,aAAa,EACb,aAAa,EACb;gBACE,qBAAqB;gBACrB,UAAU,EAAE,GAAG;gBACf,iBAAiB;gBACjB,aAAa,EAAE,qBAAqB;aACrC,CACF,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YACvE,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC/D,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;YAC3B,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC7C,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC/C,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAE7C,wCAAwC;gBACxC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC;gBAC7B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;gBAExC,MAAM,SAAS,GAAG,2DAA2B,CAAC,WAAW,CACvD,aAAa,EACb,KAAK,EACL,GAAG,EACH;oBACE,WAAW,EAAE,kBAAkB;iBAChC,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;gBAClF,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC3D,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;oBAC1C,KAAK;oBACL,GAAG;oBACH,WAAW,EAAE,kBAAkB;iBAChC,CAAC,CAAC;gBACH,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;gBAC5D,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE7C,4BAA4B;gBAC5B,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC;YACzB,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;gBAC3D,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC7C,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC/C,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAE7C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC;gBAC7B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;gBAExC,MAAM,SAAS,GAAG,2DAA2B,CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;gBAErF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;gBAClF,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB;gBAE9D,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC;YACzB,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;gBAC3D,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC7C,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC/C,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAE7C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC;gBAC7B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;gBAExC,MAAM,SAAS,GAAG,2DAA2B,CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;gBAErF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,2EAA2E,CAAC,CAAC;gBAC5G,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,oBAAoB;gBAE5D,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC;YACzB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;YACxB,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,qBAAqB,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC/D,MAAM,SAAS,GAAG,2DAA2B,CAAC,QAAQ,CAAC,aAAa,EAAE;oBACpE,WAAW,EAAE,GAAG;oBAChB,WAAW,EAAE,GAAG;oBAChB,qBAAqB;oBACrB,UAAU,EAAE,GAAG;iBAChB,CAAC,CAAC;gBAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;gBAClG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACxD,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;gBACvE,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACvC,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE1C,MAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;gBACzC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;oBACvB,WAAW,EAAE,GAAG;oBAChB,WAAW,EAAE,GAAG;oBAChB,cAAc,EAAE,GAAG;iBACpB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,SAAS,GAAG,2DAA2B,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAEtE,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB;YAC5D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;gBAC1D,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACzD,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAEvD,MAAM,SAAS,GAAG,2DAA2B,CAAC,kBAAkB,CAAC,aAAa,EAAE;oBAC9E,YAAY,EAAE,EAAE;oBAChB,gBAAgB,EAAE,CAAC;oBACnB,eAAe;oBACf,aAAa;iBACd,CAAC,CAAC;gBAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,wFAAwF,CACzF,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACpE,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEpD,MAAM,WAAW,GAAG,SAAS,CAAC,qBAAqB,EAAE,CAAC;gBACtD,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC;oBAC1B,YAAY,EAAE,EAAE;oBAChB,gBAAgB,EAAE,CAAC;oBACnB,eAAe;oBACf,aAAa;iBACd,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;gBACxD,MAAM,SAAS,GAAG,2DAA2B,CAAC,iBAAiB,CAC7D,aAAa,EACb,iBAAiB,EACjB;oBACE,eAAe,EAAE,oBAAoB;oBACrC,UAAU,EAAE,GAAG;iBAChB,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;gBAC5F,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAClE,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACvC,MAAM,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEnD,MAAM,OAAO,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;gBAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;oBACtB,cAAc,EAAE,iBAAiB;oBACjC,eAAe,EAAE,oBAAoB;iBACtC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,SAAS,GAAG,2DAA2B,CAAC,kBAAkB,CAC9D,aAAa,EACb,QAAQ,EACR;oBACE,YAAY,EAAE,EAAE;oBAChB,WAAW,EAAE,GAAG;oBAChB,UAAU,EAAE,GAAG;iBAChB,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;gBACtF,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACnE,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACvC,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEpD,MAAM,YAAY,GAAG,SAAS,CAAC,eAAe,EAAE,CAAC;gBACjD,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC;oBAC3B,YAAY,EAAE,QAAQ;oBACtB,YAAY,EAAE,EAAE;oBAChB,WAAW,EAAE,GAAG;oBAChB,eAAe,EAAE,EAAE;iBACpB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,SAAS,GAAG,2DAA2B,CAAC,kBAAkB,CAC9D,aAAa,EACb,oCAAoC,CACrC,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,2FAA2F,CAC5F,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACnE,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,qBAAqB;gBAC7D,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;YAC5B,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;gBAC5D,MAAM,SAAS,GAAG,2DAA2B,CAAC,YAAY,CAAC,aAAa,EAAE;oBACxE,mBAAmB,EAAE,uBAAuB;oBAC5C,UAAU,EAAE,EAAE;iBACf,CAAC,CAAC;gBAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,+FAA+F,CAChG,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC5D,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACtC,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;gBAC9D,MAAM,SAAS,GAAG,2DAA2B,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;gBAE1E,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,uEAAuE,CACxE,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,qBAAqB;YAC9D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,oBAAoB,GAAG,2DAA2B,CAAC,WAAW,CAClE,SAAS,EACT,IAAI,IAAI,EAAE,EACV,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAC/B,CAAC;YACF,MAAM,iBAAiB,GAAG,2DAA2B,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC1E,MAAM,gBAAgB,GAAG,2DAA2B,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YACnF,MAAM,mBAAmB,GAAG,2DAA2B,CAAC,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAC3F,MAAM,iBAAiB,GAAG,2DAA2B,CAAC,kBAAkB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC9F,MAAM,eAAe,GAAG,2DAA2B,CAAC,kBAAkB,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;YACpG,MAAM,aAAa,GAAG,2DAA2B,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAE1E,MAAM,CAAC,oBAAoB,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtD,MAAM,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE7D,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE3D,MAAM,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE/D,MAAM,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE7D,MAAM,CAAC,eAAe,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,eAAe,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,kBAAkB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,qBAAqB;gBAC/E,MAAM,SAAS,GAAG,IAAI,2DAA2B,CAC/C,cAAc,EACd,aAAa,EACb,aAAa,EACb;oBACE,qBAAqB,EAAE,kBAAkB;iBAC1C,CACF,CAAC;gBAEF,MAAM,iBAAiB,GAAG,SAAS,CAAC,oBAAoB,EAAE,CAAC;gBAC3D,MAAM,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBAC/C,MAAM,CAAC,iBAAiB,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,SAAS,GAAG,IAAI,2DAA2B,CAAC,cAAc,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;gBAE7F,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;gBAChD,MAAM,gBAAgB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,gBAAgB;gBACxE,MAAM,SAAS,GAAG,IAAI,2DAA2B,CAC/C,cAAc,EACd,aAAa,EACb,aAAa,EACb;oBACE,qBAAqB,EAAE,gBAAgB;iBACxC,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;YAC7C,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;gBACtC,MAAM,kBAAkB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,qBAAqB;gBAC/E,MAAM,SAAS,GAAG,IAAI,2DAA2B,CAC/C,cAAc,EACd,aAAa,EACb,aAAa,EACb;oBACE,qBAAqB,EAAE,kBAAkB;iBAC1C,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,6BAA6B,EAAE,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC/E,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,SAAS,GAAG,IAAI,2DAA2B,CAAC,cAAc,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;gBAE7F,MAAM,CAAC,SAAS,CAAC,6BAA6B,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC/D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;gBACvD,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC7C,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC/C,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAE7C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC;gBAC7B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;gBAExC,MAAM,SAAS,GAAG,2DAA2B,CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;gBAErF,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAErD,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC;YACzB,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;gBAC5D,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC7C,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC/C,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAE7C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC;gBAC7B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;gBAExC,MAAM,SAAS,GAAG,2DAA2B,CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;gBAErF,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEtD,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC;YACzB,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;gBAC/D,MAAM,SAAS,GAAG,2DAA2B,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAEtE,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC7C,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC/C,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAE7C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC;gBAC7B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;gBAExC,MAAM,SAAS,GAAG,2DAA2B,CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,GAAG,EAAE;oBACnF,WAAW,EAAE,kBAAkB;iBAChC,CAAC,CAAC;gBAEH,MAAM,eAAe,GAAG,SAAS,CAAC,kBAAkB,EAAE,CAAC;gBACvD,MAAM,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC;oBAC9B,WAAW,EAAE,KAAK;oBAClB,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,IAAI,EAAE,qBAAqB;oBACrC,WAAW,EAAE,kBAAkB;iBAChC,CAAC,CAAC;gBAEH,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC;YACzB,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;gBAC9D,MAAM,SAAS,GAAG,2DAA2B,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAEtE,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACvC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;gBACxD,MAAM,SAAS,GAAG,2DAA2B,CAAC,WAAW,CACvD,aAAa,EACb,IAAI,IAAI,EAAE,EACV,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAC/B,CAAC;gBAEF,MAAM,eAAe,GAAG,SAAS,CAAC,uBAAuB,EAAE,CAAC;gBAE5D,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACtD,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3C,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,2BAA2B;YAC9E,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,SAAS,GAAG,2DAA2B,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAEtE,MAAM,eAAe,GAAG,SAAS,CAAC,uBAAuB,EAAE,CAAC;gBAE5D,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC5D,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3C,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;gBAC5D,MAAM,SAAS,GAAG,2DAA2B,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;gBAEhF,MAAM,eAAe,GAAG,SAAS,CAAC,uBAAuB,EAAE,CAAC;gBAE5D,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACtD,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3C,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;gBAChE,MAAM,SAAS,GAAG,2DAA2B,CAAC,kBAAkB,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;gBAElG,MAAM,eAAe,GAAG,SAAS,CAAC,uBAAuB,EAAE,CAAC;gBAE5D,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACtD,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3C,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC7C,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC/C,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAE7C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC;YAC7B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YAExC,MAAM,SAAS,GAAG,2DAA2B,CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;YAErF,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,OAAO,CACxC,yFAAyF,CAC1F,CAAC;YAEF,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,SAAS,GAAG,2DAA2B,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;YAE1F,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,OAAO,CACxC,yHAAyH,CAC1H,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,SAAS,GAAG,2DAA2B,CAAC,kBAAkB,CAAC,aAAa,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;YAErG,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,OAAO,CACxC,uGAAuG,CACxG,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,SAAS,GAAG,2DAA2B,CAAC,kBAAkB,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;YAElG,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CACrC,kFAAkF,CACnF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,SAAS,GAAG,2DAA2B,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;YAE3F,MAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;YAE3C,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;YAClF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;gBACrC,WAAW,EAAE,aAAa;gBAC1B,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE,GAAG;aAChB,CAAC,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;gBACrC,aAAa,EAAE,KAAK;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,SAAS,GAAG,IAAI,2DAA2B,CAAC,cAAc,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;YAE7F,MAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;YAE3C,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,qBAAqB,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC/D,MAAM,SAAS,GAAG,2DAA2B,CAAC,QAAQ,CAAC,aAAa,EAAE;gBACpE,WAAW,EAAE,GAAG;gBAChB,WAAW,EAAE,GAAG;gBAChB,qBAAqB;gBACrB,UAAU,EAAE,GAAG;aAChB,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;gBACzB,IAAI,EAAE,6BAA6B;gBACnC,IAAI,EAAE,qBAAqB;gBAC3B,QAAQ,EAAE,MAAM;gBAChB,QAAQ,EAAE,cAAc;gBACxB,WAAW,EAAE,aAAa;gBAC1B,oBAAoB,EAAE,UAAU;gBAChC,qBAAqB,EAAE,qBAAqB,CAAC,WAAW,EAAE;gBAC1D,UAAU,EAAE,GAAG;gBACf,aAAa,EAAE,KAAK;gBACpB,UAAU,EAAE,IAAI;gBAChB,oBAAoB,EAAE,KAAK;gBAC3B,mBAAmB,EAAE,KAAK;gBAC1B,oBAAoB,EAAE,KAAK;gBAC3B,oBAAoB,EAAE,KAAK;gBAC3B,cAAc,EAAE,KAAK;gBACrB,qBAAqB,EAAE,KAAK;gBAC5B,QAAQ,EAAE;oBACR,WAAW,EAAE,GAAG;oBAChB,WAAW,EAAE,GAAG;oBAChB,cAAc,EAAE,GAAG;iBACpB;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,SAAS,GAAG,IAAI,2DAA2B,CAAC,cAAc,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;YAE7F,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,SAAS,GAAG,IAAI,2DAA2B,CAAC,cAAc,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;YAE7F,MAAM,CAAC,SAAS,YAAY,2DAA2B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpE,MAAM,CAAC,SAAS,YAAY,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\service-unavailable.exception.spec.ts"], "sourcesContent": ["import { ServiceUnavailableException } from '../../exceptions/service-unavailable.exception';\r\n\r\ndescribe('ServiceUnavailableException', () => {\r\n  describe('constructor', () => {\r\n    it('should create exception with required parameters', () => {\r\n      const exception = new ServiceUnavailableException(\r\n        'Service is down',\r\n        'UserService',\r\n        'maintenance'\r\n      );\r\n\r\n      expect(exception).toBeInstanceOf(ServiceUnavailableException);\r\n      expect(exception.message).toBe('Service is down');\r\n      expect(exception.code).toBe('SERVICE_UNAVAILABLE');\r\n      expect(exception.severity).toBe('high');\r\n      expect(exception.category).toBe('availability');\r\n      expect(exception.serviceName).toBe('UserService');\r\n      expect(exception.unavailabilityReason).toBe('maintenance');\r\n    });\r\n\r\n    it('should create exception with all options', () => {\r\n      const estimatedRecoveryTime = new Date('2023-01-01T15:00:00Z');\r\n      const maintenanceWindow = {\r\n        start: new Date('2023-01-01T12:00:00Z'),\r\n        end: new Date('2023-01-01T14:00:00Z'),\r\n        description: 'Scheduled maintenance',\r\n      };\r\n\r\n      const exception = new ServiceUnavailableException(\r\n        'Service is down',\r\n        'UserService',\r\n        'maintenance',\r\n        {\r\n          estimatedRecoveryTime,\r\n          retryAfter: 300,\r\n          maintenanceWindow,\r\n          correlationId: 'test-correlation-id',\r\n        }\r\n      );\r\n\r\n      expect(exception.estimatedRecoveryTime).toEqual(estimatedRecoveryTime);\r\n      expect(exception.retryAfter).toBe(300);\r\n      expect(exception.maintenanceWindow).toEqual(maintenanceWindow);\r\n      expect(exception.correlationId).toBe('test-correlation-id');\r\n    });\r\n  });\r\n\r\n  describe('static factory methods', () => {\r\n    describe('maintenance', () => {\r\n      it('should create exception for ongoing maintenance', () => {\r\n        const now = new Date('2023-01-01T13:00:00Z');\r\n        const start = new Date('2023-01-01T12:00:00Z');\r\n        const end = new Date('2023-01-01T14:00:00Z');\r\n\r\n        // Mock Date.now to return our test time\r\n        const originalNow = Date.now;\r\n        Date.now = jest.fn(() => now.getTime());\r\n\r\n        const exception = ServiceUnavailableException.maintenance(\r\n          'UserService',\r\n          start,\r\n          end,\r\n          {\r\n            description: 'Database upgrade',\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe('UserService is currently undergoing maintenance');\r\n        expect(exception.serviceName).toBe('UserService');\r\n        expect(exception.unavailabilityReason).toBe('maintenance');\r\n        expect(exception.maintenanceWindow).toEqual({\r\n          start,\r\n          end,\r\n          description: 'Database upgrade',\r\n        });\r\n        expect(exception.retryAfter).toBe(3600); // 1 hour until end\r\n        expect(exception.isMaintenance()).toBe(true);\r\n\r\n        // Restore original Date.now\r\n        Date.now = originalNow;\r\n      });\r\n\r\n      it('should create exception for scheduled maintenance', () => {\r\n        const now = new Date('2023-01-01T11:00:00Z');\r\n        const start = new Date('2023-01-01T12:00:00Z');\r\n        const end = new Date('2023-01-01T14:00:00Z');\r\n\r\n        const originalNow = Date.now;\r\n        Date.now = jest.fn(() => now.getTime());\r\n\r\n        const exception = ServiceUnavailableException.maintenance('UserService', start, end);\r\n\r\n        expect(exception.message).toBe('UserService will be unavailable for maintenance');\r\n        expect(exception.retryAfter).toBe(3600); // 1 hour until start\r\n\r\n        Date.now = originalNow;\r\n      });\r\n\r\n      it('should create exception for completed maintenance', () => {\r\n        const now = new Date('2023-01-01T15:00:00Z');\r\n        const start = new Date('2023-01-01T12:00:00Z');\r\n        const end = new Date('2023-01-01T14:00:00Z');\r\n\r\n        const originalNow = Date.now;\r\n        Date.now = jest.fn(() => now.getTime());\r\n\r\n        const exception = ServiceUnavailableException.maintenance('UserService', start, end);\r\n\r\n        expect(exception.message).toBe('UserService maintenance has completed but service may still be recovering');\r\n        expect(exception.retryAfter).toBe(300); // Default 5 minutes\r\n\r\n        Date.now = originalNow;\r\n      });\r\n    });\r\n\r\n    describe('overload', () => {\r\n      it('should create exception for service overload', () => {\r\n        const estimatedRecoveryTime = new Date('2023-01-01T13:00:00Z');\r\n        const exception = ServiceUnavailableException.overload('UserService', {\r\n          currentLoad: 150,\r\n          maxCapacity: 100,\r\n          estimatedRecoveryTime,\r\n          retryAfter: 120,\r\n        });\r\n\r\n        expect(exception.message).toBe('UserService is currently overloaded and cannot process requests');\r\n        expect(exception.serviceName).toBe('UserService');\r\n        expect(exception.unavailabilityReason).toBe('overload');\r\n        expect(exception.estimatedRecoveryTime).toEqual(estimatedRecoveryTime);\r\n        expect(exception.retryAfter).toBe(120);\r\n        expect(exception.isOverload()).toBe(true);\r\n\r\n        const loadInfo = exception.getLoadInfo();\r\n        expect(loadInfo).toEqual({\r\n          currentLoad: 150,\r\n          maxCapacity: 100,\r\n          loadPercentage: 150,\r\n        });\r\n      });\r\n\r\n      it('should use default retry time for overload', () => {\r\n        const exception = ServiceUnavailableException.overload('UserService');\r\n\r\n        expect(exception.retryAfter).toBe(60); // Default 1 minute\r\n      });\r\n    });\r\n\r\n    describe('circuitBreakerOpen', () => {\r\n      it('should create exception for circuit breaker open', () => {\r\n        const circuitOpenTime = new Date('2023-01-01T12:00:00Z');\r\n        const nextRetryTime = new Date('2023-01-01T12:05:00Z');\r\n\r\n        const exception = ServiceUnavailableException.circuitBreakerOpen('UserService', {\r\n          failureCount: 10,\r\n          failureThreshold: 5,\r\n          circuitOpenTime,\r\n          nextRetryTime,\r\n        });\r\n\r\n        expect(exception.message).toBe(\r\n          'UserService is temporarily unavailable due to repeated failures (circuit breaker open)'\r\n        );\r\n        expect(exception.serviceName).toBe('UserService');\r\n        expect(exception.unavailabilityReason).toBe('circuit_breaker_open');\r\n        expect(exception.isCircuitBreakerOpen()).toBe(true);\r\n\r\n        const circuitInfo = exception.getCircuitBreakerInfo();\r\n        expect(circuitInfo).toEqual({\r\n          failureCount: 10,\r\n          failureThreshold: 5,\r\n          circuitOpenTime,\r\n          nextRetryTime,\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('dependencyFailure', () => {\r\n      it('should create exception for dependency failure', () => {\r\n        const exception = ServiceUnavailableException.dependencyFailure(\r\n          'UserService',\r\n          'DatabaseService',\r\n          {\r\n            dependencyError: 'Connection timeout',\r\n            retryAfter: 180,\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe('UserService is unavailable due to DatabaseService failure');\r\n        expect(exception.serviceName).toBe('UserService');\r\n        expect(exception.unavailabilityReason).toBe('dependency_failure');\r\n        expect(exception.retryAfter).toBe(180);\r\n        expect(exception.isDependencyFailure()).toBe(true);\r\n\r\n        const depInfo = exception.getDependencyInfo();\r\n        expect(depInfo).toEqual({\r\n          dependencyName: 'DatabaseService',\r\n          dependencyError: 'Connection timeout',\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('resourceExhaustion', () => {\r\n      it('should create exception for resource exhaustion', () => {\r\n        const exception = ServiceUnavailableException.resourceExhaustion(\r\n          'UserService',\r\n          'memory',\r\n          {\r\n            currentUsage: 95,\r\n            maxCapacity: 100,\r\n            retryAfter: 240,\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe('UserService is unavailable due to memory exhaustion');\r\n        expect(exception.serviceName).toBe('UserService');\r\n        expect(exception.unavailabilityReason).toBe('resource_exhaustion');\r\n        expect(exception.retryAfter).toBe(240);\r\n        expect(exception.isResourceExhaustion()).toBe(true);\r\n\r\n        const resourceInfo = exception.getResourceInfo();\r\n        expect(resourceInfo).toEqual({\r\n          resourceType: 'memory',\r\n          currentUsage: 95,\r\n          maxCapacity: 100,\r\n          usagePercentage: 95,\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('configurationError', () => {\r\n      it('should create exception for configuration error', () => {\r\n        const exception = ServiceUnavailableException.configurationError(\r\n          'UserService',\r\n          'Invalid database connection string'\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          'UserService is unavailable due to configuration error: Invalid database connection string'\r\n        );\r\n        expect(exception.serviceName).toBe('UserService');\r\n        expect(exception.unavailabilityReason).toBe('configuration_error');\r\n        expect(exception.retryAfter).toBe(600); // Default 10 minutes\r\n        expect(exception.isConfigurationError()).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('initializing', () => {\r\n      it('should create exception for service initialization', () => {\r\n        const exception = ServiceUnavailableException.initializing('UserService', {\r\n          initializationStage: 'Loading configuration',\r\n          retryAfter: 45,\r\n        });\r\n\r\n        expect(exception.message).toBe(\r\n          'UserService is currently initializing (Loading configuration) and not ready to serve requests'\r\n        );\r\n        expect(exception.serviceName).toBe('UserService');\r\n        expect(exception.unavailabilityReason).toBe('initializing');\r\n        expect(exception.retryAfter).toBe(45);\r\n        expect(exception.isInitializing()).toBe(true);\r\n      });\r\n\r\n      it('should create exception without initialization stage', () => {\r\n        const exception = ServiceUnavailableException.initializing('UserService');\r\n\r\n        expect(exception.message).toBe(\r\n          'UserService is currently initializing and not ready to serve requests'\r\n        );\r\n        expect(exception.retryAfter).toBe(30); // Default 30 seconds\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('type checking methods', () => {\r\n    it('should correctly identify unavailability reasons', () => {\r\n      const maintenanceException = ServiceUnavailableException.maintenance(\r\n        'Service',\r\n        new Date(),\r\n        new Date(Date.now() + 3600000)\r\n      );\r\n      const overloadException = ServiceUnavailableException.overload('Service');\r\n      const circuitException = ServiceUnavailableException.circuitBreakerOpen('Service');\r\n      const dependencyException = ServiceUnavailableException.dependencyFailure('Service', 'DB');\r\n      const resourceException = ServiceUnavailableException.resourceExhaustion('Service', 'memory');\r\n      const configException = ServiceUnavailableException.configurationError('Service', 'Invalid config');\r\n      const initException = ServiceUnavailableException.initializing('Service');\r\n\r\n      expect(maintenanceException.isMaintenance()).toBe(true);\r\n      expect(maintenanceException.isOverload()).toBe(false);\r\n\r\n      expect(overloadException.isOverload()).toBe(true);\r\n      expect(overloadException.isCircuitBreakerOpen()).toBe(false);\r\n\r\n      expect(circuitException.isCircuitBreakerOpen()).toBe(true);\r\n      expect(circuitException.isDependencyFailure()).toBe(false);\r\n\r\n      expect(dependencyException.isDependencyFailure()).toBe(true);\r\n      expect(dependencyException.isResourceExhaustion()).toBe(false);\r\n\r\n      expect(resourceException.isResourceExhaustion()).toBe(true);\r\n      expect(resourceException.isConfigurationError()).toBe(false);\r\n\r\n      expect(configException.isConfigurationError()).toBe(true);\r\n      expect(configException.isInitializing()).toBe(false);\r\n\r\n      expect(initException.isInitializing()).toBe(true);\r\n      expect(initException.isMaintenance()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('utility methods', () => {\r\n    describe('getTimeUntilRecovery', () => {\r\n      it('should return time until recovery in seconds', () => {\r\n        const futureRecoveryTime = new Date(Date.now() + 300000); // 5 minutes from now\r\n        const exception = new ServiceUnavailableException(\r\n          'Service down',\r\n          'UserService',\r\n          'maintenance',\r\n          {\r\n            estimatedRecoveryTime: futureRecoveryTime,\r\n          }\r\n        );\r\n\r\n        const timeUntilRecovery = exception.getTimeUntilRecovery();\r\n        expect(timeUntilRecovery).toBeGreaterThan(290);\r\n        expect(timeUntilRecovery).toBeLessThanOrEqual(300);\r\n      });\r\n\r\n      it('should return null when no recovery time is set', () => {\r\n        const exception = new ServiceUnavailableException('Service down', 'UserService', 'overload');\r\n\r\n        expect(exception.getTimeUntilRecovery()).toBeNull();\r\n      });\r\n\r\n      it('should return 0 for past recovery time', () => {\r\n        const pastRecoveryTime = new Date(Date.now() - 300000); // 5 minutes ago\r\n        const exception = new ServiceUnavailableException(\r\n          'Service down',\r\n          'UserService',\r\n          'maintenance',\r\n          {\r\n            estimatedRecoveryTime: pastRecoveryTime,\r\n          }\r\n        );\r\n\r\n        expect(exception.getTimeUntilRecovery()).toBe(0);\r\n      });\r\n    });\r\n\r\n    describe('getTimeUntilRecoveryFormatted', () => {\r\n      it('should format time correctly', () => {\r\n        const futureRecoveryTime = new Date(Date.now() + 300000); // 5 minutes from now\r\n        const exception = new ServiceUnavailableException(\r\n          'Service down',\r\n          'UserService',\r\n          'maintenance',\r\n          {\r\n            estimatedRecoveryTime: futureRecoveryTime,\r\n          }\r\n        );\r\n\r\n        expect(exception.getTimeUntilRecoveryFormatted()).toMatch(/\\d+ minute\\(s\\)/);\r\n      });\r\n\r\n      it('should return null when no recovery time is set', () => {\r\n        const exception = new ServiceUnavailableException('Service down', 'UserService', 'overload');\r\n\r\n        expect(exception.getTimeUntilRecoveryFormatted()).toBeNull();\r\n      });\r\n    });\r\n\r\n    describe('isInMaintenanceWindow', () => {\r\n      it('should return true when in maintenance window', () => {\r\n        const now = new Date('2023-01-01T13:00:00Z');\r\n        const start = new Date('2023-01-01T12:00:00Z');\r\n        const end = new Date('2023-01-01T14:00:00Z');\r\n\r\n        const originalNow = Date.now;\r\n        Date.now = jest.fn(() => now.getTime());\r\n\r\n        const exception = ServiceUnavailableException.maintenance('UserService', start, end);\r\n\r\n        expect(exception.isInMaintenanceWindow()).toBe(true);\r\n\r\n        Date.now = originalNow;\r\n      });\r\n\r\n      it('should return false when not in maintenance window', () => {\r\n        const now = new Date('2023-01-01T11:00:00Z');\r\n        const start = new Date('2023-01-01T12:00:00Z');\r\n        const end = new Date('2023-01-01T14:00:00Z');\r\n\r\n        const originalNow = Date.now;\r\n        Date.now = jest.fn(() => now.getTime());\r\n\r\n        const exception = ServiceUnavailableException.maintenance('UserService', start, end);\r\n\r\n        expect(exception.isInMaintenanceWindow()).toBe(false);\r\n\r\n        Date.now = originalNow;\r\n      });\r\n\r\n      it('should return false when no maintenance window is set', () => {\r\n        const exception = ServiceUnavailableException.overload('UserService');\r\n\r\n        expect(exception.isInMaintenanceWindow()).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('getMaintenanceInfo', () => {\r\n      it('should return maintenance information', () => {\r\n        const now = new Date('2023-01-01T13:00:00Z');\r\n        const start = new Date('2023-01-01T12:00:00Z');\r\n        const end = new Date('2023-01-01T14:00:00Z');\r\n\r\n        const originalNow = Date.now;\r\n        Date.now = jest.fn(() => now.getTime());\r\n\r\n        const exception = ServiceUnavailableException.maintenance('UserService', start, end, {\r\n          description: 'Database upgrade',\r\n        });\r\n\r\n        const maintenanceInfo = exception.getMaintenanceInfo();\r\n        expect(maintenanceInfo).toEqual({\r\n          isScheduled: false,\r\n          isOngoing: true,\r\n          isCompleted: false,\r\n          duration: 7200, // 2 hours in seconds\r\n          description: 'Database upgrade',\r\n        });\r\n\r\n        Date.now = originalNow;\r\n      });\r\n\r\n      it('should return null when no maintenance window is set', () => {\r\n        const exception = ServiceUnavailableException.overload('UserService');\r\n\r\n        expect(exception.getMaintenanceInfo()).toBeNull();\r\n      });\r\n    });\r\n\r\n    describe('getRetryRecommendations', () => {\r\n      it('should provide recommendations for maintenance', () => {\r\n        const exception = ServiceUnavailableException.maintenance(\r\n          'UserService',\r\n          new Date(),\r\n          new Date(Date.now() + 3600000)\r\n        );\r\n\r\n        const recommendations = exception.getRetryRecommendations();\r\n\r\n        expect(recommendations.backoffStrategy).toBe('fixed');\r\n        expect(recommendations.maxRetries).toBe(1);\r\n        expect(recommendations.shouldRetry).toBe(false); // Currently in maintenance\r\n      });\r\n\r\n      it('should provide recommendations for overload', () => {\r\n        const exception = ServiceUnavailableException.overload('UserService');\r\n\r\n        const recommendations = exception.getRetryRecommendations();\r\n\r\n        expect(recommendations.backoffStrategy).toBe('exponential');\r\n        expect(recommendations.maxRetries).toBe(5);\r\n        expect(recommendations.shouldRetry).toBe(true);\r\n      });\r\n\r\n      it('should provide recommendations for circuit breaker', () => {\r\n        const exception = ServiceUnavailableException.circuitBreakerOpen('UserService');\r\n\r\n        const recommendations = exception.getRetryRecommendations();\r\n\r\n        expect(recommendations.backoffStrategy).toBe('fixed');\r\n        expect(recommendations.maxRetries).toBe(3);\r\n        expect(recommendations.shouldRetry).toBe(true);\r\n      });\r\n\r\n      it('should provide recommendations for configuration error', () => {\r\n        const exception = ServiceUnavailableException.configurationError('UserService', 'Invalid config');\r\n\r\n        const recommendations = exception.getRetryRecommendations();\r\n\r\n        expect(recommendations.backoffStrategy).toBe('fixed');\r\n        expect(recommendations.maxRetries).toBe(1);\r\n        expect(recommendations.shouldRetry).toBe(false);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('getUserMessage', () => {\r\n    it('should return user-friendly message for ongoing maintenance', () => {\r\n      const now = new Date('2023-01-01T13:00:00Z');\r\n      const start = new Date('2023-01-01T12:00:00Z');\r\n      const end = new Date('2023-01-01T14:00:00Z');\r\n\r\n      const originalNow = Date.now;\r\n      Date.now = jest.fn(() => now.getTime());\r\n\r\n      const exception = ServiceUnavailableException.maintenance('UserService', start, end);\r\n\r\n      expect(exception.getUserMessage()).toMatch(\r\n        /UserService is currently undergoing maintenance\\. Please try again in \\d+ minute\\(s\\)\\./\r\n      );\r\n\r\n      Date.now = originalNow;\r\n    });\r\n\r\n    it('should return user-friendly message for overload', () => {\r\n      const exception = ServiceUnavailableException.overload('UserService', { retryAfter: 60 });\r\n\r\n      expect(exception.getUserMessage()).toMatch(\r\n        /UserService is currently experiencing high load and cannot process your request\\. Please try again in \\d+ second\\(s\\)\\./\r\n      );\r\n    });\r\n\r\n    it('should return user-friendly message for circuit breaker', () => {\r\n      const exception = ServiceUnavailableException.circuitBreakerOpen('UserService', { retryAfter: 300 });\r\n\r\n      expect(exception.getUserMessage()).toMatch(\r\n        /UserService is temporarily unavailable due to recent failures\\. Please try again in \\d+ minute\\(s\\)\\./\r\n      );\r\n    });\r\n\r\n    it('should return user-friendly message for configuration error', () => {\r\n      const exception = ServiceUnavailableException.configurationError('UserService', 'Invalid config');\r\n\r\n      expect(exception.getUserMessage()).toBe(\r\n        'UserService is unavailable due to a configuration issue. Please contact support.'\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('toApiResponse', () => {\r\n    it('should convert to API response format with headers', () => {\r\n      const exception = ServiceUnavailableException.overload('UserService', { retryAfter: 120 });\r\n\r\n      const response = exception.toApiResponse();\r\n\r\n      expect(response.error).toMatch(/UserService is currently experiencing high load/);\r\n      expect(response.code).toBe('SERVICE_UNAVAILABLE');\r\n      expect(response.details).toMatchObject({\r\n        serviceName: 'UserService',\r\n        reason: 'overload',\r\n        retryAfter: 120,\r\n      });\r\n      expect(response.headers).toMatchObject({\r\n        'Retry-After': '120',\r\n      });\r\n    });\r\n\r\n    it('should not include Retry-After header when not set', () => {\r\n      const exception = new ServiceUnavailableException('Service down', 'UserService', 'overload');\r\n\r\n      const response = exception.toApiResponse();\r\n\r\n      expect(response.headers).toEqual({});\r\n    });\r\n  });\r\n\r\n  describe('toJSON', () => {\r\n    it('should convert to JSON with detailed information', () => {\r\n      const estimatedRecoveryTime = new Date('2023-01-01T15:00:00Z');\r\n      const exception = ServiceUnavailableException.overload('UserService', {\r\n        currentLoad: 150,\r\n        maxCapacity: 100,\r\n        estimatedRecoveryTime,\r\n        retryAfter: 120,\r\n      });\r\n\r\n      const json = exception.toJSON();\r\n\r\n      expect(json).toMatchObject({\r\n        name: 'ServiceUnavailableException',\r\n        code: 'SERVICE_UNAVAILABLE',\r\n        severity: 'high',\r\n        category: 'availability',\r\n        serviceName: 'UserService',\r\n        unavailabilityReason: 'overload',\r\n        estimatedRecoveryTime: estimatedRecoveryTime.toISOString(),\r\n        retryAfter: 120,\r\n        isMaintenance: false,\r\n        isOverload: true,\r\n        isCircuitBreakerOpen: false,\r\n        isDependencyFailure: false,\r\n        isResourceExhaustion: false,\r\n        isConfigurationError: false,\r\n        isInitializing: false,\r\n        isInMaintenanceWindow: false,\r\n        loadInfo: {\r\n          currentLoad: 150,\r\n          maxCapacity: 100,\r\n          loadPercentage: 150,\r\n        },\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('inheritance', () => {\r\n    it('should be instance of Error and DomainException', () => {\r\n      const exception = new ServiceUnavailableException('Service down', 'UserService', 'overload');\r\n\r\n      expect(exception).toBeInstanceOf(Error);\r\n      expect(exception.name).toBe('ServiceUnavailableException');\r\n      expect(exception.code).toBe('SERVICE_UNAVAILABLE');\r\n    });\r\n\r\n    it('should maintain proper prototype chain', () => {\r\n      const exception = new ServiceUnavailableException('Service down', 'UserService', 'overload');\r\n\r\n      expect(exception instanceof ServiceUnavailableException).toBe(true);\r\n      expect(exception instanceof Error).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}