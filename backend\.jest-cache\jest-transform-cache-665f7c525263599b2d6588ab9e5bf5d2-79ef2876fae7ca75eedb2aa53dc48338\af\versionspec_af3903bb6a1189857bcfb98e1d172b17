00507daeb824d95f52790e872327d6c2
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const version_value_object_1 = require("../../value-objects/version.value-object");
describe('Version', () => {
    describe('creation', () => {
        it('should create version from string', () => {
            const version = version_value_object_1.Version.fromString('1.2.3');
            expect(version.value).toBe('1.2.3');
            expect(version.major).toBe(1);
            expect(version.minor).toBe(2);
            expect(version.patch).toBe(3);
        });
        it('should create version from components', () => {
            const version = version_value_object_1.Version.create(1, 2, 3);
            expect(version.value).toBe('1.2.3');
            expect(version.major).toBe(1);
            expect(version.minor).toBe(2);
            expect(version.patch).toBe(3);
        });
        it('should create version with prerelease', () => {
            const version = version_value_object_1.Version.create(1, 2, 3, 'alpha.1');
            expect(version.value).toBe('1.2.3-alpha.1');
            expect(version.prerelease).toBe('alpha.1');
            expect(version.isPrerelease()).toBe(true);
        });
        it('should create version with build metadata', () => {
            const version = version_value_object_1.Version.create(1, 2, 3, undefined, 'build.123');
            expect(version.value).toBe('1.2.3+build.123');
            expect(version.buildMetadata).toBe('build.123');
        });
        it('should create version with prerelease and build metadata', () => {
            const version = version_value_object_1.Version.create(1, 2, 3, 'beta.2', 'build.456');
            expect(version.value).toBe('1.2.3-beta.2+build.456');
            expect(version.prerelease).toBe('beta.2');
            expect(version.buildMetadata).toBe('build.456');
        });
        it('should create initial version', () => {
            const version = version_value_object_1.Version.initial();
            expect(version.value).toBe('0.0.0');
            expect(version.isInitial()).toBe(true);
        });
        it('should create first stable version', () => {
            const version = version_value_object_1.Version.firstStable();
            expect(version.value).toBe('1.0.0');
            expect(version.isStable()).toBe(true);
        });
    });
    describe('validation', () => {
        it('should throw error for null value', () => {
            expect(() => new version_value_object_1.Version(null)).toThrow('Version cannot be null or undefined');
        });
        it('should throw error for undefined value', () => {
            expect(() => new version_value_object_1.Version(undefined)).toThrow('Version cannot be null or undefined');
        });
        it('should throw error for empty string', () => {
            expect(() => new version_value_object_1.Version('')).toThrow('Version cannot be empty');
        });
        it('should throw error for non-string value', () => {
            expect(() => new version_value_object_1.Version(123)).toThrow('Version must be a string');
        });
        it('should throw error for invalid semantic version format', () => {
            expect(() => new version_value_object_1.Version('1.2')).toThrow('Invalid semantic version format');
            expect(() => new version_value_object_1.Version('1.2.3.4')).toThrow('Invalid semantic version format');
            expect(() => new version_value_object_1.Version('v1.2.3')).toThrow('Invalid semantic version format');
            expect(() => new version_value_object_1.Version('1.2.3-')).toThrow('Invalid semantic version format');
            expect(() => new version_value_object_1.Version('1.2.3+')).toThrow('Invalid semantic version format');
        });
        it('should throw error for negative version components', () => {
            expect(() => version_value_object_1.Version.create(-1, 0, 0)).toThrow('Version components must be non-negative integers');
            expect(() => version_value_object_1.Version.create(0, -1, 0)).toThrow('Version components must be non-negative integers');
            expect(() => version_value_object_1.Version.create(0, 0, -1)).toThrow('Version components must be non-negative integers');
        });
        it('should throw error for non-integer version components', () => {
            expect(() => version_value_object_1.Version.create(1.5, 0, 0)).toThrow('Version components must be integers');
            expect(() => version_value_object_1.Version.create(0, 2.5, 0)).toThrow('Version components must be integers');
            expect(() => version_value_object_1.Version.create(0, 0, 3.5)).toThrow('Version components must be integers');
        });
    });
    describe('static validation methods', () => {
        it('should validate correct semantic version strings', () => {
            expect(version_value_object_1.Version.isValid('1.2.3')).toBe(true);
            expect(version_value_object_1.Version.isValid('0.0.0')).toBe(true);
            expect(version_value_object_1.Version.isValid('10.20.30')).toBe(true);
            expect(version_value_object_1.Version.isValid('1.2.3-alpha')).toBe(true);
            expect(version_value_object_1.Version.isValid('1.2.3-alpha.1')).toBe(true);
            expect(version_value_object_1.Version.isValid('1.2.3+build.1')).toBe(true);
            expect(version_value_object_1.Version.isValid('1.2.3-alpha.1+build.1')).toBe(true);
        });
        it('should reject invalid semantic version strings', () => {
            expect(version_value_object_1.Version.isValid('1.2')).toBe(false);
            expect(version_value_object_1.Version.isValid('1.2.3.4')).toBe(false);
            expect(version_value_object_1.Version.isValid('v1.2.3')).toBe(false);
            expect(version_value_object_1.Version.isValid('1.2.3-')).toBe(false);
            expect(version_value_object_1.Version.isValid('1.2.3+')).toBe(false);
            expect(version_value_object_1.Version.isValid('')).toBe(false);
            expect(version_value_object_1.Version.isValid(null)).toBe(false);
            expect(version_value_object_1.Version.isValid(undefined)).toBe(false);
        });
        it('should try parse valid version', () => {
            const result = version_value_object_1.Version.tryParse('1.2.3');
            expect(result).not.toBeNull();
            expect(result.value).toBe('1.2.3');
        });
        it('should return null for invalid version in tryParse', () => {
            const result = version_value_object_1.Version.tryParse('invalid-version');
            expect(result).toBeNull();
        });
    });
    describe('version properties', () => {
        it('should get core version', () => {
            const version = new version_value_object_1.Version('1.2.3-alpha.1+build.1');
            expect(version.getCoreVersion()).toBe('1.2.3');
        });
        it('should identify prerelease versions', () => {
            const stable = new version_value_object_1.Version('1.2.3');
            const prerelease = new version_value_object_1.Version('1.2.3-alpha.1');
            expect(stable.isPrerelease()).toBe(false);
            expect(stable.isStable()).toBe(true);
            expect(prerelease.isPrerelease()).toBe(true);
            expect(prerelease.isStable()).toBe(false);
        });
        it('should identify initial version', () => {
            const initial = new version_value_object_1.Version('0.0.0');
            const notInitial = new version_value_object_1.Version('0.0.1');
            const prereleaseInitial = new version_value_object_1.Version('0.0.0-alpha');
            expect(initial.isInitial()).toBe(true);
            expect(notInitial.isInitial()).toBe(false);
            expect(prereleaseInitial.isInitial()).toBe(false);
        });
    });
    describe('version increment operations', () => {
        let baseVersion;
        beforeEach(() => {
            baseVersion = new version_value_object_1.Version('1.2.3');
        });
        it('should increment major version', () => {
            const incremented = baseVersion.incrementMajor();
            expect(incremented.value).toBe('2.0.0');
            expect(incremented.major).toBe(2);
            expect(incremented.minor).toBe(0);
            expect(incremented.patch).toBe(0);
        });
        it('should increment minor version', () => {
            const incremented = baseVersion.incrementMinor();
            expect(incremented.value).toBe('1.3.0');
            expect(incremented.major).toBe(1);
            expect(incremented.minor).toBe(3);
            expect(incremented.patch).toBe(0);
        });
        it('should increment patch version', () => {
            const incremented = baseVersion.incrementPatch();
            expect(incremented.value).toBe('1.2.4');
            expect(incremented.major).toBe(1);
            expect(incremented.minor).toBe(2);
            expect(incremented.patch).toBe(4);
        });
        it('should increment with prerelease', () => {
            const incremented = baseVersion.incrementMajor('alpha.1');
            expect(incremented.value).toBe('2.0.0-alpha.1');
            expect(incremented.prerelease).toBe('alpha.1');
        });
        it('should increment with build metadata', () => {
            const incremented = baseVersion.incrementMinor(undefined, 'build.123');
            expect(incremented.value).toBe('1.3.0+build.123');
            expect(incremented.buildMetadata).toBe('build.123');
        });
        it('should increment with prerelease and build metadata', () => {
            const incremented = baseVersion.incrementPatch('beta.2', 'build.456');
            expect(incremented.value).toBe('1.2.4-beta.2+build.456');
            expect(incremented.prerelease).toBe('beta.2');
            expect(incremented.buildMetadata).toBe('build.456');
        });
    });
    describe('version modification operations', () => {
        let baseVersion;
        beforeEach(() => {
            baseVersion = new version_value_object_1.Version('1.2.3');
        });
        it('should add prerelease', () => {
            const withPrerelease = baseVersion.withPrerelease('alpha.1');
            expect(withPrerelease.value).toBe('1.2.3-alpha.1');
            expect(withPrerelease.prerelease).toBe('alpha.1');
        });
        it('should throw error for empty prerelease', () => {
            expect(() => baseVersion.withPrerelease('')).toThrow('Prerelease identifier cannot be empty');
            expect(() => baseVersion.withPrerelease('   ')).toThrow('Prerelease identifier cannot be empty');
        });
        it('should add build metadata', () => {
            const withBuild = baseVersion.withBuildMetadata('build.123');
            expect(withBuild.value).toBe('1.2.3+build.123');
            expect(withBuild.buildMetadata).toBe('build.123');
        });
        it('should throw error for empty build metadata', () => {
            expect(() => baseVersion.withBuildMetadata('')).toThrow('Build metadata cannot be empty');
            expect(() => baseVersion.withBuildMetadata('   ')).toThrow('Build metadata cannot be empty');
        });
        it('should convert to stable version', () => {
            const prerelease = new version_value_object_1.Version('1.2.3-alpha.1+build.123');
            const stable = prerelease.toStable();
            expect(stable.value).toBe('1.2.3');
            expect(stable.prerelease).toBeNull();
            expect(stable.buildMetadata).toBeNull();
        });
    });
    describe('version comparison', () => {
        it('should compare major versions', () => {
            const v1 = new version_value_object_1.Version('1.0.0');
            const v2 = new version_value_object_1.Version('2.0.0');
            expect(v1.compareTo(v2)).toBe(-1);
            expect(v2.compareTo(v1)).toBe(1);
            expect(v1.compareTo(v1)).toBe(0);
        });
        it('should compare minor versions', () => {
            const v1 = new version_value_object_1.Version('1.1.0');
            const v2 = new version_value_object_1.Version('1.2.0');
            expect(v1.compareTo(v2)).toBe(-1);
            expect(v2.compareTo(v1)).toBe(1);
        });
        it('should compare patch versions', () => {
            const v1 = new version_value_object_1.Version('1.0.1');
            const v2 = new version_value_object_1.Version('1.0.2');
            expect(v1.compareTo(v2)).toBe(-1);
            expect(v2.compareTo(v1)).toBe(1);
        });
        it('should compare prerelease versions', () => {
            const stable = new version_value_object_1.Version('1.0.0');
            const prerelease = new version_value_object_1.Version('1.0.0-alpha');
            expect(prerelease.compareTo(stable)).toBe(-1);
            expect(stable.compareTo(prerelease)).toBe(1);
        });
        it('should compare different prerelease versions', () => {
            const alpha = new version_value_object_1.Version('1.0.0-alpha');
            const beta = new version_value_object_1.Version('1.0.0-beta');
            expect(alpha.compareTo(beta)).toBe(-1);
            expect(beta.compareTo(alpha)).toBe(1);
        });
        it('should compare numeric prerelease identifiers', () => {
            const v1 = new version_value_object_1.Version('1.0.0-alpha.1');
            const v2 = new version_value_object_1.Version('1.0.0-alpha.2');
            expect(v1.compareTo(v2)).toBe(-1);
            expect(v2.compareTo(v1)).toBe(1);
        });
        it('should compare mixed prerelease identifiers', () => {
            const numeric = new version_value_object_1.Version('1.0.0-alpha.1');
            const text = new version_value_object_1.Version('1.0.0-alpha.beta');
            expect(numeric.compareTo(text)).toBe(-1);
            expect(text.compareTo(numeric)).toBe(1);
        });
    });
    describe('version comparison methods', () => {
        let v1;
        let v2;
        let v3;
        beforeEach(() => {
            v1 = new version_value_object_1.Version('1.0.0');
            v2 = new version_value_object_1.Version('2.0.0');
            v3 = new version_value_object_1.Version('1.0.0');
        });
        it('should check if less than', () => {
            expect(v1.isLessThan(v2)).toBe(true);
            expect(v2.isLessThan(v1)).toBe(false);
            expect(v1.isLessThan(v3)).toBe(false);
        });
        it('should check if less than or equal', () => {
            expect(v1.isLessThanOrEqual(v2)).toBe(true);
            expect(v1.isLessThanOrEqual(v3)).toBe(true);
            expect(v2.isLessThanOrEqual(v1)).toBe(false);
        });
        it('should check if greater than', () => {
            expect(v2.isGreaterThan(v1)).toBe(true);
            expect(v1.isGreaterThan(v2)).toBe(false);
            expect(v1.isGreaterThan(v3)).toBe(false);
        });
        it('should check if greater than or equal', () => {
            expect(v2.isGreaterThanOrEqual(v1)).toBe(true);
            expect(v1.isGreaterThanOrEqual(v3)).toBe(true);
            expect(v1.isGreaterThanOrEqual(v2)).toBe(false);
        });
        it('should check compatibility (same major version)', () => {
            const v1_1 = new version_value_object_1.Version('1.1.0');
            const v1_2 = new version_value_object_1.Version('1.2.0');
            const v2_0 = new version_value_object_1.Version('2.0.0');
            const v0_1 = new version_value_object_1.Version('0.1.0');
            expect(v1_1.isCompatibleWith(v1_2)).toBe(true);
            expect(v1_1.isCompatibleWith(v2_0)).toBe(false);
            expect(v1_1.isCompatibleWith(v0_1)).toBe(false); // 0.x.x versions are not compatible
        });
    });
    describe('version range satisfaction', () => {
        let version;
        beforeEach(() => {
            version = new version_value_object_1.Version('1.2.3');
        });
        it('should satisfy exact match', () => {
            expect(version.satisfies('1.2.3')).toBe(true);
            expect(version.satisfies('1.2.4')).toBe(false);
        });
        it('should satisfy caret range (compatible within major)', () => {
            expect(version.satisfies('^1.2.0')).toBe(true);
            expect(version.satisfies('^1.0.0')).toBe(true);
            expect(version.satisfies('^1.3.0')).toBe(false);
            expect(version.satisfies('^2.0.0')).toBe(false);
        });
        it('should satisfy tilde range (compatible within minor)', () => {
            expect(version.satisfies('~1.2.0')).toBe(true);
            expect(version.satisfies('~1.2.3')).toBe(true);
            expect(version.satisfies('~1.1.0')).toBe(false);
            expect(version.satisfies('~1.3.0')).toBe(false);
        });
        it('should satisfy comparison operators', () => {
            expect(version.satisfies('>=1.2.0')).toBe(true);
            expect(version.satisfies('>=1.2.3')).toBe(true);
            expect(version.satisfies('>=1.2.4')).toBe(false);
            expect(version.satisfies('<=1.2.3')).toBe(true);
            expect(version.satisfies('<=1.2.4')).toBe(true);
            expect(version.satisfies('<=1.2.2')).toBe(false);
            expect(version.satisfies('>1.2.2')).toBe(true);
            expect(version.satisfies('>1.2.3')).toBe(false);
            expect(version.satisfies('<1.2.4')).toBe(true);
            expect(version.satisfies('<1.2.3')).toBe(false);
            expect(version.satisfies('=1.2.3')).toBe(true);
            expect(version.satisfies('=1.2.4')).toBe(false);
        });
        it('should not satisfy invalid ranges', () => {
            expect(version.satisfies('invalid-range')).toBe(false);
            expect(version.satisfies('^invalid')).toBe(false);
            expect(version.satisfies('>=invalid')).toBe(false);
        });
    });
    describe('equality and comparison', () => {
        it('should be equal to itself', () => {
            const version = new version_value_object_1.Version('1.2.3');
            expect(version.equals(version)).toBe(true);
        });
        it('should be equal to version with same value', () => {
            const version1 = new version_value_object_1.Version('1.2.3');
            const version2 = new version_value_object_1.Version('1.2.3');
            expect(version1.equals(version2)).toBe(true);
        });
        it('should not be equal to version with different value', () => {
            const version1 = new version_value_object_1.Version('1.2.3');
            const version2 = new version_value_object_1.Version('1.2.4');
            expect(version1.equals(version2)).toBe(false);
        });
        it('should ignore build metadata in equality', () => {
            const version1 = new version_value_object_1.Version('1.2.3+build.1');
            const version2 = new version_value_object_1.Version('1.2.3+build.2');
            expect(version1.equals(version2)).toBe(true);
        });
        it('should consider prerelease in equality', () => {
            const stable = new version_value_object_1.Version('1.2.3');
            const prerelease = new version_value_object_1.Version('1.2.3-alpha');
            expect(stable.equals(prerelease)).toBe(false);
        });
        it('should not be equal to null or undefined', () => {
            const version = new version_value_object_1.Version('1.2.3');
            expect(version.equals(null)).toBe(false);
            expect(version.equals(undefined)).toBe(false);
        });
        it('should not be equal to non-Version object', () => {
            const version = new version_value_object_1.Version('1.2.3');
            expect(version.equals({})).toBe(false);
        });
    });
    describe('serialization', () => {
        it('should convert to string', () => {
            const version = new version_value_object_1.Version('1.2.3-alpha.1+build.123');
            expect(version.toString()).toBe('1.2.3-alpha.1+build.123');
        });
        it('should convert to JSON', () => {
            const version = new version_value_object_1.Version('1.2.3-alpha.1+build.123');
            const json = version.toJSON();
            expect(json.value).toBe('1.2.3-alpha.1+build.123');
            expect(json.major).toBe(1);
            expect(json.minor).toBe(2);
            expect(json.patch).toBe(3);
            expect(json.prerelease).toBe('alpha.1');
            expect(json.buildMetadata).toBe('build.123');
            expect(json.coreVersion).toBe('1.2.3');
            expect(json.isPrerelease).toBe(true);
            expect(json.isStable).toBe(false);
            expect(json.type).toBe('Version');
        });
        it('should create from JSON', () => {
            const json = { value: '1.2.3-alpha.1+build.123' };
            const version = version_value_object_1.Version.fromJSON(json);
            expect(version.value).toBe('1.2.3-alpha.1+build.123');
            expect(version.major).toBe(1);
            expect(version.minor).toBe(2);
            expect(version.patch).toBe(3);
        });
    });
    describe('immutability', () => {
        it('should be immutable after creation', () => {
            const version = new version_value_object_1.Version('1.2.3');
            const originalValue = version.value;
            // Attempt to modify (should not work due to readonly)
            expect(() => {
                version._value = 'modified';
            }).not.toThrow(); // TypeScript prevents this, but runtime doesn't throw
            // Value should remain unchanged
            expect(version.value).toBe(originalValue);
        });
        it('should be frozen', () => {
            const version = new version_value_object_1.Version('1.2.3');
            expect(Object.isFrozen(version)).toBe(true);
        });
        it('should return new instances for modification operations', () => {
            const original = new version_value_object_1.Version('1.2.3');
            const incremented = original.incrementMajor();
            expect(incremented).not.toBe(original);
            expect(original.value).toBe('1.2.3');
            expect(incremented.value).toBe('2.0.0');
        });
    });
    describe('edge cases', () => {
        it('should handle large version numbers', () => {
            const version = new version_value_object_1.Version('999999.999999.999999');
            expect(version.major).toBe(999999);
            expect(version.minor).toBe(999999);
            expect(version.patch).toBe(999999);
        });
        it('should handle complex prerelease identifiers', () => {
            const version = new version_value_object_1.Version('1.0.0-alpha.1.2.3.beta.4.5.6');
            expect(version.prerelease).toBe('alpha.1.2.3.beta.4.5.6');
        });
        it('should handle complex build metadata', () => {
            const version = new version_value_object_1.Version('1.0.0+build.1.2.3.sha.abc123');
            expect(version.buildMetadata).toBe('build.1.2.3.sha.abc123');
        });
        it('should compare prerelease versions with different lengths', () => {
            const v1 = new version_value_object_1.Version('1.0.0-alpha');
            const v2 = new version_value_object_1.Version('1.0.0-alpha.1');
            expect(v1.compareTo(v2)).toBe(-1);
            expect(v2.compareTo(v1)).toBe(1);
        });
        it('should handle zero versions', () => {
            const version = new version_value_object_1.Version('0.0.0');
            expect(version.isInitial()).toBe(true);
            expect(version.isStable()).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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