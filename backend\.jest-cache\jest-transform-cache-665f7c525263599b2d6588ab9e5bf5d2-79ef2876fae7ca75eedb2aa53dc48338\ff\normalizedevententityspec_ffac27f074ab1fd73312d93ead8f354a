79313e12dae12d22f5c2c68add6bb68f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const normalized_event_entity_1 = require("../normalized-event.entity");
const shared_kernel_1 = require("../../../../../shared-kernel");
const event_metadata_value_object_1 = require("../../value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../value-objects/event-metadata/event-source.value-object");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const event_status_enum_1 = require("../../enums/event-status.enum");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const event_source_type_enum_1 = require("../../enums/event-source-type.enum");
const node_test_1 = require("node:test");
const node_test_2 = require("node:test");
const node_test_3 = require("node:test");
(0, node_test_2.describe)('NormalizedEvent Entity', () => {
    let validProps;
    let mockMetadata;
    let mockRule;
    (0, node_test_3.beforeEach)(() => {
        // Create mock metadata
        const timestamp = event_timestamp_value_object_1.EventTimestamp.create();
        const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.SIEM, 'test-siem');
        mockMetadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
        // Create mock normalization rule
        mockRule = {
            id: 'test-rule',
            name: 'Test Rule',
            description: 'Test normalization rule',
            priority: 100,
            required: false,
        };
        // Create valid props
        validProps = {
            originalEventId: shared_kernel_1.UniqueEntityId.create(),
            metadata: mockMetadata,
            type: event_type_enum_1.EventType.THREAT_DETECTED,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            status: event_status_enum_1.EventStatus.ACTIVE,
            processingStatus: event_processing_status_enum_1.EventProcessingStatus.NORMALIZED,
            normalizationStatus: normalized_event_entity_1.NormalizationStatus.COMPLETED,
            originalData: { raw: 'data' },
            normalizedData: { normalized: 'data' },
            title: 'Test Normalized Event',
            appliedRules: [mockRule],
            schemaVersion: '1.0.0',
            dataQualityScore: 85,
            normalizationAttempts: 1,
        };
    });
    (0, node_test_2.describe)('creation', () => {
        (0, node_test_1.it)('should create a valid normalized event with required properties', () => {
            // Act
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(validProps);
            // Assert
            expect(normalizedEvent).toBeInstanceOf(normalized_event_entity_1.NormalizedEvent);
            expect(normalizedEvent.originalEventId).toEqual(validProps.originalEventId);
            expect(normalizedEvent.type).toBe(event_type_enum_1.EventType.THREAT_DETECTED);
            expect(normalizedEvent.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
            expect(normalizedEvent.normalizationStatus).toBe(normalized_event_entity_1.NormalizationStatus.COMPLETED);
            expect(normalizedEvent.schemaVersion).toBe('1.0.0');
            expect(normalizedEvent.dataQualityScore).toBe(85);
        });
        (0, node_test_1.it)('should generate domain event when created', () => {
            // Act
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(validProps);
            // Assert
            const domainEvents = normalizedEvent.getUncommittedEvents();
            expect(domainEvents).toHaveLength(1);
            expect(domainEvents[0].constructor.name).toBe('NormalizedEventCreatedDomainEvent');
        });
        (0, node_test_1.it)('should throw error when original event ID is missing', () => {
            // Arrange
            const invalidProps = { ...validProps };
            delete invalidProps.originalEventId;
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('NormalizedEvent must reference an original event');
        });
        (0, node_test_1.it)('should throw error when metadata is missing', () => {
            // Arrange
            const invalidProps = { ...validProps };
            delete invalidProps.metadata;
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('NormalizedEvent must have metadata');
        });
        (0, node_test_1.it)('should throw error when normalized data is missing', () => {
            // Arrange
            const invalidProps = { ...validProps };
            delete invalidProps.normalizedData;
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('NormalizedEvent must have normalized data');
        });
        (0, node_test_1.it)('should throw error when schema version is missing', () => {
            // Arrange
            const invalidProps = { ...validProps };
            delete invalidProps.schemaVersion;
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('NormalizedEvent must have a schema version');
        });
        (0, node_test_1.it)('should throw error when applied rules is not an array', () => {
            // Arrange
            const invalidProps = { ...validProps, appliedRules: 'not-an-array' };
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('NormalizedEvent must have applied rules array');
        });
    });
    (0, node_test_2.describe)('validation', () => {
        (0, node_test_1.it)('should validate data quality score range', () => {
            // Arrange
            const invalidProps = { ...validProps, dataQualityScore: 150 };
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('Data quality score must be between 0 and 100');
        });
        (0, node_test_1.it)('should validate risk score range', () => {
            // Arrange
            const invalidProps = { ...validProps, riskScore: -10 };
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('Risk score must be between 0 and 100');
        });
        (0, node_test_1.it)('should validate confidence level range', () => {
            // Arrange
            const invalidProps = { ...validProps, confidenceLevel: 110 };
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('Confidence level must be between 0 and 100');
        });
        (0, node_test_1.it)('should validate normalization attempts cannot be negative', () => {
            // Arrange
            const invalidProps = { ...validProps, normalizationAttempts: -1 };
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('Normalization attempts cannot be negative');
        });
        (0, node_test_1.it)('should validate maximum validation errors', () => {
            // Arrange
            const tooManyErrors = Array(15).fill('error');
            const invalidProps = { ...validProps, validationErrors: tooManyErrors };
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('Cannot have more than 10 validation errors');
        });
    });
    (0, node_test_2.describe)('normalization status consistency', () => {
        (0, node_test_1.it)('should require completion timestamp for completed normalization', () => {
            // Arrange
            const invalidProps = {
                ...validProps,
                normalizationStatus: normalized_event_entity_1.NormalizationStatus.COMPLETED,
                normalizationCompletedAt: undefined,
            };
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('Completed normalization must have completion timestamp');
        });
        (0, node_test_1.it)('should require normalization result for completed normalization', () => {
            // Arrange
            const invalidProps = {
                ...validProps,
                normalizationStatus: normalized_event_entity_1.NormalizationStatus.COMPLETED,
                normalizationCompletedAt: new Date(),
                normalizationResult: undefined,
            };
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('Completed normalization must have result');
        });
        (0, node_test_1.it)('should require error information for failed normalization', () => {
            // Arrange
            const invalidProps = {
                ...validProps,
                normalizationStatus: normalized_event_entity_1.NormalizationStatus.FAILED,
                lastNormalizationError: undefined,
                normalizationResult: undefined,
            };
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('Failed normalization must have error information');
        });
        (0, node_test_1.it)('should require start timestamp for in-progress normalization', () => {
            // Arrange
            const invalidProps = {
                ...validProps,
                normalizationStatus: normalized_event_entity_1.NormalizationStatus.IN_PROGRESS,
                normalizationStartedAt: undefined,
            };
            // Act & Assert
            expect(() => normalized_event_entity_1.NormalizedEvent.create(invalidProps)).toThrow('In-progress normalization must have start timestamp');
        });
    });
    (0, node_test_2.describe)('business methods', () => {
        let normalizedEvent;
        (0, node_test_3.beforeEach)(() => {
            const pendingProps = {
                ...validProps,
                normalizationStatus: normalized_event_entity_1.NormalizationStatus.PENDING,
                normalizationCompletedAt: undefined,
                normalizationResult: undefined,
            };
            normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(pendingProps);
        });
        (0, node_test_2.describe)('startNormalization', () => {
            (0, node_test_1.it)('should start normalization for pending events', () => {
                // Act
                normalizedEvent.startNormalization();
                // Assert
                expect(normalizedEvent.normalizationStatus).toBe(normalized_event_entity_1.NormalizationStatus.IN_PROGRESS);
                expect(normalizedEvent.normalizationStartedAt).toBeDefined();
                expect(normalizedEvent.normalizationAttempts).toBe(1);
            });
            (0, node_test_1.it)('should throw error when starting normalization for non-pending events', () => {
                // Arrange
                normalizedEvent.startNormalization();
                // Act & Assert
                expect(() => normalizedEvent.startNormalization()).toThrow('Can only start normalization for pending events');
            });
        });
        (0, node_test_2.describe)('completeNormalization', () => {
            let mockResult;
            (0, node_test_3.beforeEach)(() => {
                mockResult = {
                    success: true,
                    appliedRules: ['rule1', 'rule2'],
                    failedRules: [],
                    warnings: [],
                    errors: [],
                    processingDurationMs: 1000,
                    confidenceScore: 90,
                };
                normalizedEvent.startNormalization();
            });
            (0, node_test_1.it)('should complete normalization for in-progress events', () => {
                // Act
                normalizedEvent.completeNormalization(mockResult);
                // Assert
                expect(normalizedEvent.normalizationStatus).toBe(normalized_event_entity_1.NormalizationStatus.COMPLETED);
                expect(normalizedEvent.normalizationCompletedAt).toBeDefined();
                expect(normalizedEvent.normalizationResult).toEqual(mockResult);
                expect(normalizedEvent.lastNormalizationError).toBeUndefined();
            });
            (0, node_test_1.it)('should generate domain event when normalization completes', () => {
                // Act
                normalizedEvent.completeNormalization(mockResult);
                // Assert
                const domainEvents = normalizedEvent.getUncommittedEvents();
                const statusChangedEvent = domainEvents.find(e => e.constructor.name === 'NormalizedEventStatusChangedDomainEvent');
                expect(statusChangedEvent).toBeDefined();
            });
            (0, node_test_1.it)('should throw error when completing normalization for non-in-progress events', () => {
                // Arrange
                normalizedEvent.completeNormalization(mockResult);
                // Act & Assert
                expect(() => normalizedEvent.completeNormalization(mockResult)).toThrow('Can only complete normalization for in-progress events');
            });
        });
        (0, node_test_2.describe)('failNormalization', () => {
            (0, node_test_3.beforeEach)(() => {
                normalizedEvent.startNormalization();
            });
            (0, node_test_1.it)('should fail normalization for in-progress events', () => {
                // Act
                normalizedEvent.failNormalization('Test error');
                // Assert
                expect(normalizedEvent.normalizationStatus).toBe(normalized_event_entity_1.NormalizationStatus.FAILED);
                expect(normalizedEvent.lastNormalizationError).toBe('Test error');
            });
            (0, node_test_1.it)('should generate domain event when normalization fails', () => {
                // Act
                normalizedEvent.failNormalization('Test error');
                // Assert
                const domainEvents = normalizedEvent.getUncommittedEvents();
                const failedEvent = domainEvents.find(e => e.constructor.name === 'NormalizedEventValidationFailedDomainEvent');
                expect(failedEvent).toBeDefined();
            });
            (0, node_test_1.it)('should throw error when failing normalization for non-in-progress events', () => {
                // Arrange
                normalizedEvent.failNormalization('Test error');
                // Act & Assert
                expect(() => normalizedEvent.failNormalization('Another error')).toThrow('Can only fail normalization for in-progress events');
            });
        });
        (0, node_test_2.describe)('skipNormalization', () => {
            (0, node_test_1.it)('should skip normalization for pending events', () => {
                // Act
                normalizedEvent.skipNormalization('Test reason');
                // Assert
                expect(normalizedEvent.normalizationStatus).toBe(normalized_event_entity_1.NormalizationStatus.SKIPPED);
                expect(normalizedEvent.reviewNotes).toBe('Test reason');
            });
            (0, node_test_1.it)('should skip normalization for failed events', () => {
                // Arrange
                normalizedEvent.startNormalization();
                normalizedEvent.failNormalization('Test error');
                // Act
                normalizedEvent.skipNormalization('Skip reason');
                // Assert
                expect(normalizedEvent.normalizationStatus).toBe(normalized_event_entity_1.NormalizationStatus.SKIPPED);
            });
            (0, node_test_1.it)('should throw error when skipping normalization for invalid statuses', () => {
                // Arrange
                normalizedEvent.startNormalization();
                // Act & Assert
                expect(() => normalizedEvent.skipNormalization('Test reason')).toThrow('Can only skip normalization for pending or failed events');
            });
        });
        (0, node_test_2.describe)('resetNormalization', () => {
            (0, node_test_1.it)('should reset normalization for failed events', () => {
                // Arrange
                normalizedEvent.startNormalization();
                normalizedEvent.failNormalization('Test error');
                // Act
                normalizedEvent.resetNormalization();
                // Assert
                expect(normalizedEvent.normalizationStatus).toBe(normalized_event_entity_1.NormalizationStatus.PENDING);
                expect(normalizedEvent.normalizationStartedAt).toBeUndefined();
                expect(normalizedEvent.lastNormalizationError).toBeUndefined();
            });
            (0, node_test_1.it)('should throw error when resetting after max attempts exceeded', () => {
                // Arrange
                const propsWithMaxAttempts = {
                    ...validProps,
                    normalizationStatus: normalized_event_entity_1.NormalizationStatus.FAILED,
                    normalizationAttempts: 3,
                };
                const eventWithMaxAttempts = normalized_event_entity_1.NormalizedEvent.create(propsWithMaxAttempts);
                // Act & Assert
                expect(() => eventWithMaxAttempts.resetNormalization()).toThrow('Cannot reset normalization: maximum attempts exceeded');
            });
        });
    });
    (0, node_test_2.describe)('data management', () => {
        let normalizedEvent;
        (0, node_test_3.beforeEach)(() => {
            normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(validProps);
        });
        (0, node_test_2.describe)('updateNormalizedData', () => {
            (0, node_test_1.it)('should update normalized data for non-completed events', () => {
                // Arrange
                const pendingProps = {
                    ...validProps,
                    normalizationStatus: normalized_event_entity_1.NormalizationStatus.PENDING,
                };
                const pendingEvent = normalized_event_entity_1.NormalizedEvent.create(pendingProps);
                const newData = { updated: 'data' };
                // Act
                pendingEvent.updateNormalizedData(newData);
                // Assert
                expect(pendingEvent.normalizedData).toEqual(newData);
            });
            (0, node_test_1.it)('should throw error when updating normalized data for completed events', () => {
                // Arrange
                const newData = { updated: 'data' };
                // Act & Assert
                expect(() => normalizedEvent.updateNormalizedData(newData)).toThrow('Cannot update normalized data for completed normalization');
            });
        });
        (0, node_test_2.describe)('addAppliedRule', () => {
            (0, node_test_1.it)('should add new applied rule', () => {
                // Arrange
                const newRule = {
                    id: 'new-rule',
                    name: 'New Rule',
                    description: 'New normalization rule',
                    priority: 50,
                    required: true,
                };
                // Act
                normalizedEvent.addAppliedRule(newRule);
                // Assert
                expect(normalizedEvent.appliedRules).toContain(newRule);
            });
            (0, node_test_1.it)('should not add duplicate rules', () => {
                // Arrange
                const initialCount = normalizedEvent.appliedRules.length;
                // Act
                normalizedEvent.addAppliedRule(mockRule);
                // Assert
                expect(normalizedEvent.appliedRules).toHaveLength(initialCount);
            });
        });
        (0, node_test_2.describe)('updateDataQualityScore', () => {
            (0, node_test_1.it)('should update data quality score within valid range', () => {
                // Act
                normalizedEvent.updateDataQualityScore(75);
                // Assert
                expect(normalizedEvent.dataQualityScore).toBe(75);
            });
            (0, node_test_1.it)('should throw error for invalid data quality score', () => {
                // Act & Assert
                expect(() => normalizedEvent.updateDataQualityScore(150)).toThrow('Data quality score must be between 0 and 100');
            });
        });
        (0, node_test_2.describe)('validation errors management', () => {
            (0, node_test_1.it)('should add validation errors', () => {
                // Arrange
                const errors = ['Error 1', 'Error 2'];
                // Act
                normalizedEvent.addValidationErrors(errors);
                // Assert
                expect(normalizedEvent.validationErrors).toEqual(errors);
            });
            (0, node_test_1.it)('should clear validation errors', () => {
                // Arrange
                normalizedEvent.addValidationErrors(['Error 1']);
                // Act
                normalizedEvent.clearValidationErrors();
                // Assert
                expect(normalizedEvent.validationErrors).toEqual([]);
            });
            (0, node_test_1.it)('should throw error when adding too many validation errors', () => {
                // Arrange
                const tooManyErrors = Array(15).fill('error');
                // Act & Assert
                expect(() => normalizedEvent.addValidationErrors(tooManyErrors)).toThrow('Cannot have more than 10 validation errors');
            });
        });
    });
    (0, node_test_2.describe)('manual review', () => {
        let normalizedEvent;
        (0, node_test_3.beforeEach)(() => {
            normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(validProps);
        });
        (0, node_test_2.describe)('markForManualReview', () => {
            (0, node_test_1.it)('should mark event for manual review', () => {
                // Act
                normalizedEvent.markForManualReview('High risk event');
                // Assert
                expect(normalizedEvent.requiresManualReview).toBe(true);
                expect(normalizedEvent.reviewNotes).toBe('High risk event');
            });
        });
        (0, node_test_2.describe)('completeManualReview', () => {
            (0, node_test_1.it)('should complete manual review for events marked for review', () => {
                // Arrange
                normalizedEvent.markForManualReview('Test reason');
                // Act
                normalizedEvent.completeManualReview('<EMAIL>', 'Review completed');
                // Assert
                expect(normalizedEvent.reviewedBy).toBe('<EMAIL>');
                expect(normalizedEvent.reviewedAt).toBeDefined();
                expect(normalizedEvent.reviewNotes).toBe('Review completed');
            });
            (0, node_test_1.it)('should throw error when completing review for events not marked for review', () => {
                // Act & Assert
                expect(() => normalizedEvent.completeManualReview('<EMAIL>')).toThrow('Event is not marked for manual review');
            });
        });
    });
    (0, node_test_2.describe)('query methods', () => {
        let normalizedEvent;
        (0, node_test_3.beforeEach)(() => {
            normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(validProps);
        });
        (0, node_test_1.it)('should check if normalization is completed', () => {
            expect(normalizedEvent.isNormalizationCompleted()).toBe(true);
        });
        (0, node_test_1.it)('should check if normalization failed', () => {
            expect(normalizedEvent.isNormalizationFailed()).toBe(false);
        });
        (0, node_test_1.it)('should check if normalization is in progress', () => {
            expect(normalizedEvent.isNormalizationInProgress()).toBe(false);
        });
        (0, node_test_1.it)('should check if normalization was skipped', () => {
            expect(normalizedEvent.isNormalizationSkipped()).toBe(false);
        });
        (0, node_test_1.it)('should check if event has high data quality', () => {
            expect(normalizedEvent.hasHighDataQuality()).toBe(true);
        });
        (0, node_test_1.it)('should check if event has validation errors', () => {
            expect(normalizedEvent.hasValidationErrors()).toBe(false);
        });
        (0, node_test_1.it)('should check if event has exceeded max normalization attempts', () => {
            expect(normalizedEvent.hasExceededMaxNormalizationAttempts()).toBe(false);
        });
        (0, node_test_1.it)('should check if event is ready for next stage', () => {
            expect(normalizedEvent.isReadyForNextStage()).toBe(true);
        });
        (0, node_test_1.it)('should check if event is high risk', () => {
            // Arrange
            const highRiskProps = { ...validProps, riskScore: 85 };
            const highRiskEvent = normalized_event_entity_1.NormalizedEvent.create(highRiskProps);
            // Assert
            expect(highRiskEvent.isHighRisk()).toBe(true);
        });
        (0, node_test_1.it)('should get normalization duration', () => {
            // Arrange
            const startTime = new Date();
            const endTime = new Date(startTime.getTime() + 5000);
            const propsWithDuration = {
                ...validProps,
                normalizationStartedAt: startTime,
                normalizationCompletedAt: endTime,
            };
            const eventWithDuration = normalized_event_entity_1.NormalizedEvent.create(propsWithDuration);
            // Act
            const duration = eventWithDuration.getNormalizationDuration();
            // Assert
            expect(duration).toBe(5000);
        });
        (0, node_test_1.it)('should get applied rule names', () => {
            // Act
            const ruleNames = normalizedEvent.getAppliedRuleNames();
            // Assert
            expect(ruleNames).toEqual(['Test Rule']);
        });
        (0, node_test_1.it)('should check if specific rule was applied', () => {
            // Act & Assert
            expect(normalizedEvent.hasAppliedRule('test-rule')).toBe(true);
            expect(normalizedEvent.hasAppliedRule('non-existent-rule')).toBe(false);
        });
    });
    (0, node_test_2.describe)('serialization', () => {
        (0, node_test_1.it)('should convert to JSON representation', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(validProps);
            // Act
            const json = normalizedEvent.toJSON();
            // Assert
            expect(json).toHaveProperty('id');
            expect(json).toHaveProperty('originalEventId');
            expect(json).toHaveProperty('type', event_type_enum_1.EventType.THREAT_DETECTED);
            expect(json).toHaveProperty('severity', event_severity_enum_1.EventSeverity.HIGH);
            expect(json).toHaveProperty('normalizationStatus', normalized_event_entity_1.NormalizationStatus.COMPLETED);
            expect(json).toHaveProperty('schemaVersion', '1.0.0');
            expect(json).toHaveProperty('summary');
            expect(json.summary).toHaveProperty('isReadyForNextStage');
        });
        (0, node_test_1.it)('should get event summary', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(validProps);
            // Act
            const summary = normalizedEvent.getSummary();
            // Assert
            expect(summary).toHaveProperty('id');
            expect(summary).toHaveProperty('originalEventId');
            expect(summary).toHaveProperty('title', 'Test Normalized Event');
            expect(summary).toHaveProperty('type', event_type_enum_1.EventType.THREAT_DETECTED);
            expect(summary).toHaveProperty('severity', event_severity_enum_1.EventSeverity.HIGH);
            expect(summary).toHaveProperty('normalizationStatus', normalized_event_entity_1.NormalizationStatus.COMPLETED);
            expect(summary).toHaveProperty('dataQualityScore', 85);
            expect(summary).toHaveProperty('appliedRulesCount', 1);
            expect(summary).toHaveProperty('hasValidationErrors', false);
            expect(summary).toHaveProperty('requiresManualReview', false);
            expect(summary).toHaveProperty('isReadyForNextStage', true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************