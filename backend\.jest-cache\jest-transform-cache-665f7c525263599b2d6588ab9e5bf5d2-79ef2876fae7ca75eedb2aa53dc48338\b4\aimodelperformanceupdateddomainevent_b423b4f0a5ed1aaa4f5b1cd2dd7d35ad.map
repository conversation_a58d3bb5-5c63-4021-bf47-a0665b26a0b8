{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\ai-model-performance-updated.domain-event.ts", "mappings": ";;;AAAA,0FAAqF;AAIrF;;;;;;GAMG;AACH,MAAa,8BAA+B,SAAQ,mCAAe;IACjE,YACkB,OAAuB,EACvB,WAA6B,EAC7C,OAAwB,EACxB,UAAiB;QAEjB,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QALX,YAAO,GAAP,OAAO,CAAgB;QACvB,gBAAW,GAAX,WAAW,CAAkB;IAK/C,CAAC;IAEM,YAAY;QACjB,OAAO,2BAA2B,CAAC;IACrC,CAAC;IAEM,eAAe;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,YAAY;QACjB,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAChC,WAAW,EAAE;gBACX,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa;gBAC7C,kBAAkB,EAAE,IAAI,CAAC,WAAW,CAAC,kBAAkB;gBACvD,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc;gBAC/C,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc;gBAC/C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;gBACvC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;gBACvC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ;gBACnC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS;gBACrC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM;gBAC/B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;gBACjC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;gBACvC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,EAAE;aACxD;SACF,CAAC;IACJ,CAAC;CACF;AArCD,wEAqCC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\ai-model-performance-updated.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent } from '../../../../shared-kernel/domain/base-domain-event';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { ModelPerformance } from '../entities/ai-model.entity';\r\n\r\n/**\r\n * AI Model Performance Updated Domain Event\r\n * \r\n * Published when an AI model's performance metrics are updated.\r\n * This event can trigger various downstream processes such as\r\n * model ranking updates, performance alerts, and optimization workflows.\r\n */\r\nexport class AIModelPerformanceUpdatedEvent extends BaseDomainEvent {\r\n  constructor(\r\n    public readonly modelId: UniqueEntityId,\r\n    public readonly performance: ModelPerformance,\r\n    eventId?: UniqueEntityId,\r\n    occurredOn?: Date\r\n  ) {\r\n    super(eventId, occurredOn);\r\n  }\r\n\r\n  public getEventName(): string {\r\n    return 'AIModelPerformanceUpdated';\r\n  }\r\n\r\n  public getEventVersion(): string {\r\n    return '1.0';\r\n  }\r\n\r\n  public getEventData(): Record<string, any> {\r\n    return {\r\n      modelId: this.modelId.toString(),\r\n      performance: {\r\n        totalRequests: this.performance.totalRequests,\r\n        successfulRequests: this.performance.successfulRequests,\r\n        failedRequests: this.performance.failedRequests,\r\n        averageLatency: this.performance.averageLatency,\r\n        p95Latency: this.performance.p95Latency,\r\n        p99Latency: this.performance.p99Latency,\r\n        accuracy: this.performance.accuracy,\r\n        precision: this.performance.precision,\r\n        recall: this.performance.recall,\r\n        f1Score: this.performance.f1Score,\r\n        throughput: this.performance.throughput,\r\n        lastUpdated: this.performance.lastUpdated.toISOString(),\r\n      },\r\n    };\r\n  }\r\n}"], "version": 3}