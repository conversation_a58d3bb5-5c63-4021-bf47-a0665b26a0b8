{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\domain\\entities\\policy-violation.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,iFAAsE;AACtE,yEAA8D;AAE9D;;;GAGG;AAQI,IAAM,eAAe,GAArB,MAAM,eAAe;IAyO1B;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,CAAC,CAAC,UAAU,EAAE,eAAe,EAAE,gBAAgB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAE/B,kCAAkC;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAEjF,OAAO,IAAI,IAAI,EAAE,GAAG,QAAQ,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QACzD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,qBAAqB;QACvB,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QACrE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,SAAS,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAc,EAAE,UAAkB;QACvC,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;QAE5B,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC;QAChC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,MAAc;QAC7B,IAAI,CAAC,MAAM,GAAG,yBAAyB,CAAC;QACxC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,MAAc,EAAE,UAAkB;QACxC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,GAAG,UAAU,CAAC;QACjD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,GAAG,MAAM,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,MAAc,EAAE,aAAqB;QAC9C,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YACjC,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG;gBAC5B,UAAU,EAAE,QAAQ;gBACpB,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,QAAQ;aACpB,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,uBAAuB,GAAG,aAAa,CAAC;QACpE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,UAAU,GAAG,MAAM,CAAC;QAChD,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,MAAc,EAAE,MAAc;QAChD,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,GAAG,MAAM,CAAC;QACnD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,GAAG,MAAM,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAAa;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;YACzB,GAAG,QAAQ;YACX,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,cAAmB;QACtC,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG;YAC5B,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc;YAC9B,GAAG,cAAc;SAClB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,MAAc,EAAE,IAA6D;QAChG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,EAAE,CAAC;QAChC,CAAC;QAED,MAAM,SAAS,GAAG,GAAG,IAAI,SAAS,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACK,WAAW;QACjB,MAAM,SAAS,GAAG;YAChB,QAAQ,EAAE,CAAC,EAAI,UAAU;YACzB,IAAI,EAAE,EAAE,EAAO,QAAQ;YACvB,MAAM,EAAE,EAAE,EAAK,SAAS;YACxB,GAAG,EAAE,GAAG,EAAO,SAAS;SACzB,CAAC;QAEF,OAAO,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC/B,MAAM,cAAc,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QACpE,OAAO,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YACjD,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,oBAAoB,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,IAAI,CAAC;YAC/D,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,IAAI,CAAC;YAC3D,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC;YACjD,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE;YACjC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;gBACpB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;gBAClB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;gBACtB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;aACvB,CAAC,CAAC,CAAC,IAAI;YACR,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC5B,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;gBACtB,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI;gBAC1B,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW;aACzC,CAAC,CAAC,CAAC,IAAI;YACR,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,OAAO,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,SAAS;YACd,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,iBAAiB;YAC1C,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,eAAe,GAAG,MAAM,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,IAAI,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC;IAC7C,CAAC;CACF,CAAA;AA/eY,0CAAe;AAE1B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;2CACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;8CACV;AAMd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;oDACL;AA2BpB;IAtBC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,gBAAgB;YAChB,iBAAiB;YACjB,gBAAgB;YAChB,eAAe;YACf,YAAY;YACZ,eAAe;YACf,iBAAiB;YACjB,mBAAmB;YACnB,mBAAmB;YACnB,0BAA0B;YAC1B,kBAAkB;YAClB,mBAAmB;YACnB,oBAAoB;YACpB,qBAAqB;YACrB,iBAAiB;YACjB,uBAAuB;YACvB,OAAO;SACR;KACF,CAAC;;sDACoB;AAStB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;KAC5C,CAAC;;iDAC+C;AAUjD;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,MAAM,EAAE,eAAe,EAAE,qBAAqB,EAAE,yBAAyB,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,CAAC;QAChI,OAAO,EAAE,MAAM;KAChB,CAAC;;+CACa;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;mDAAC;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;mDAAC;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;gDA6GxB;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;6CACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;mDAC3B;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAC1C;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;kDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;kDAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,2CAAgB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;kDACzB,2CAAgB,oBAAhB,2CAAgB;+CAAC;AAG1B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAC1C;AAIlB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,mDAAoB,EAAE,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC9F,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;kDACzB,mDAAoB,oBAApB,mDAAoB;mDAAC;AAGlC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAC1C;0BAvOX,eAAe;IAP3B,IAAA,gBAAM,EAAC,mBAAmB,CAAC;IAC3B,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,cAAc,CAAC,CAAC;IACvB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,eAAe,CAAC,CAAC;GACZ,eAAe,CA+e3B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\domain\\entities\\policy-violation.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { ComplianceAssessment } from './compliance-assessment.entity';\r\nimport { PolicyDefinition } from './policy-definition.entity';\r\n\r\n/**\r\n * Policy Violation entity\r\n * Represents violations of compliance policies and controls\r\n */\r\n@Entity('policy_violations')\r\n@Index(['policyId'])\r\n@Index(['assessmentId'])\r\n@Index(['severity'])\r\n@Index(['status'])\r\n@Index(['detectedAt'])\r\n@Index(['violationType'])\r\nexport class PolicyViolation {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Violation title\r\n   */\r\n  @Column({ length: 255 })\r\n  title: string;\r\n\r\n  /**\r\n   * Violation description\r\n   */\r\n  @Column({ type: 'text' })\r\n  description: string;\r\n\r\n  /**\r\n   * Type of violation\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'access_control',\r\n      'data_protection',\r\n      'authentication',\r\n      'authorization',\r\n      'encryption',\r\n      'audit_logging',\r\n      'backup_recovery',\r\n      'incident_response',\r\n      'change_management',\r\n      'vulnerability_management',\r\n      'network_security',\r\n      'physical_security',\r\n      'personnel_security',\r\n      'business_continuity',\r\n      'risk_management',\r\n      'compliance_monitoring',\r\n      'other',\r\n    ],\r\n  })\r\n  violationType: string;\r\n\r\n  /**\r\n   * Violation severity\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high', 'critical'],\r\n  })\r\n  severity: 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  /**\r\n   * Violation status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['open', 'investigating', 'remediation_planned', 'remediation_in_progress', 'resolved', 'accepted_risk', 'false_positive'],\r\n    default: 'open',\r\n  })\r\n  status: string;\r\n\r\n  /**\r\n   * When the violation was detected\r\n   */\r\n  @Column({ name: 'detected_at', type: 'timestamp with time zone' })\r\n  detectedAt: Date;\r\n\r\n  /**\r\n   * When the violation was resolved\r\n   */\r\n  @Column({ name: 'resolved_at', type: 'timestamp with time zone', nullable: true })\r\n  resolvedAt?: Date;\r\n\r\n  /**\r\n   * Violation details and context\r\n   */\r\n  @Column({ type: 'jsonb' })\r\n  details: {\r\n    // Violation specifics\r\n    violatedControls?: string[];\r\n    violatedPolicies?: string[];\r\n    violatedRequirements?: string[];\r\n    \r\n    // Evidence and detection\r\n    evidence?: Array<{\r\n      type: 'log' | 'screenshot' | 'document' | 'system_output' | 'witness_statement';\r\n      description: string;\r\n      source: string;\r\n      timestamp: string;\r\n      location?: string;\r\n      hash?: string;\r\n    }>;\r\n    \r\n    detectionMethod?: 'automated_scan' | 'manual_review' | 'audit' | 'incident_investigation' | 'user_report';\r\n    detectionSource?: string;\r\n    \r\n    // Affected entities\r\n    affectedSystems?: Array<{\r\n      systemId?: string;\r\n      systemName: string;\r\n      systemType: string;\r\n      impact: 'none' | 'low' | 'medium' | 'high';\r\n    }>;\r\n    \r\n    affectedUsers?: Array<{\r\n      userId?: string;\r\n      username?: string;\r\n      role?: string;\r\n      impact: string;\r\n    }>;\r\n    \r\n    affectedData?: Array<{\r\n      dataType: string;\r\n      classification: 'public' | 'internal' | 'confidential' | 'restricted';\r\n      volume?: string;\r\n      location?: string;\r\n    }>;\r\n    \r\n    // Business impact\r\n    businessImpact?: {\r\n      description: string;\r\n      financialImpact?: number;\r\n      operationalImpact?: string;\r\n      reputationalImpact?: string;\r\n      legalImpact?: string;\r\n      customerImpact?: string;\r\n    };\r\n    \r\n    // Risk assessment\r\n    riskAssessment?: {\r\n      likelihood: 'very_low' | 'low' | 'medium' | 'high' | 'very_high';\r\n      impact: 'very_low' | 'low' | 'medium' | 'high' | 'very_high';\r\n      riskScore: number;\r\n      riskLevel: 'low' | 'medium' | 'high' | 'critical';\r\n      mitigatingFactors?: string[];\r\n      aggravatingFactors?: string[];\r\n    };\r\n    \r\n    // Root cause analysis\r\n    rootCause?: {\r\n      primaryCause: string;\r\n      contributingFactors: string[];\r\n      systemicIssues?: string[];\r\n      humanFactors?: string[];\r\n      processFailures?: string[];\r\n      technologyFailures?: string[];\r\n    };\r\n    \r\n    // Remediation information\r\n    remediation?: {\r\n      immediateActions?: string[];\r\n      shortTermActions?: string[];\r\n      longTermActions?: string[];\r\n      preventiveActions?: string[];\r\n      estimatedCost?: number;\r\n      estimatedEffort?: string;\r\n      targetDate?: string;\r\n      assignedTo?: string;\r\n      dependencies?: string[];\r\n    };\r\n    \r\n    // Compliance context\r\n    compliance?: {\r\n      frameworks: string[];\r\n      controls: string[];\r\n      requirements: string[];\r\n      regulatoryImplications?: string[];\r\n      reportingRequired?: boolean;\r\n      notificationRequired?: boolean;\r\n      deadlines?: Array<{\r\n        type: string;\r\n        date: string;\r\n        description: string;\r\n      }>;\r\n    };\r\n    \r\n    // Additional metadata\r\n    metadata?: {\r\n      correlationId?: string;\r\n      relatedViolations?: string[];\r\n      relatedIncidents?: string[];\r\n      externalReferences?: string[];\r\n      tags?: string[];\r\n      customFields?: Record<string, any>;\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Violation tags\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * User who detected/reported the violation\r\n   */\r\n  @Column({ name: 'detected_by', type: 'uuid' })\r\n  detectedBy: string;\r\n\r\n  /**\r\n   * User assigned to handle the violation\r\n   */\r\n  @Column({ name: 'assigned_to', type: 'uuid', nullable: true })\r\n  assignedTo?: string;\r\n\r\n  /**\r\n   * User who last updated the violation\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => PolicyDefinition, { nullable: true })\r\n  @JoinColumn({ name: 'policy_id' })\r\n  policy?: PolicyDefinition;\r\n\r\n  @Column({ name: 'policy_id', type: 'uuid', nullable: true })\r\n  policyId?: string;\r\n\r\n  @ManyToOne(() => ComplianceAssessment, assessment => assessment.violations, { nullable: true })\r\n  @JoinColumn({ name: 'assessment_id' })\r\n  assessment?: ComplianceAssessment;\r\n\r\n  @Column({ name: 'assessment_id', type: 'uuid', nullable: true })\r\n  assessmentId?: string;\r\n\r\n  /**\r\n   * Check if violation is open\r\n   */\r\n  get isOpen(): boolean {\r\n    return !['resolved', 'accepted_risk', 'false_positive'].includes(this.status);\r\n  }\r\n\r\n  /**\r\n   * Check if violation is critical\r\n   */\r\n  get isCritical(): boolean {\r\n    return this.severity === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if violation is overdue\r\n   */\r\n  get isOverdue(): boolean {\r\n    if (!this.isOpen) return false;\r\n    \r\n    // Calculate SLA based on severity\r\n    const slaHours = this.getSlaHours();\r\n    const deadline = new Date(this.detectedAt.getTime() + slaHours * 60 * 60 * 1000);\r\n    \r\n    return new Date() > deadline;\r\n  }\r\n\r\n  /**\r\n   * Get violation age in hours\r\n   */\r\n  get ageInHours(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.detectedAt.getTime();\r\n    return Math.round(diffMs / (1000 * 60 * 60));\r\n  }\r\n\r\n  /**\r\n   * Get time to resolution in hours\r\n   */\r\n  get timeToResolutionHours(): number | null {\r\n    if (!this.resolvedAt) return null;\r\n    const diffMs = this.resolvedAt.getTime() - this.detectedAt.getTime();\r\n    return Math.round(diffMs / (1000 * 60 * 60));\r\n  }\r\n\r\n  /**\r\n   * Get risk score\r\n   */\r\n  get riskScore(): number {\r\n    return this.details.riskAssessment?.riskScore || this.calculateDefaultRiskScore();\r\n  }\r\n\r\n  /**\r\n   * Assign violation to user\r\n   */\r\n  assign(userId: string, assignedBy: string): void {\r\n    this.assignedTo = userId;\r\n    this.updatedBy = assignedBy;\r\n    \r\n    if (this.status === 'open') {\r\n      this.status = 'investigating';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Start remediation\r\n   */\r\n  startRemediation(userId: string): void {\r\n    this.status = 'remediation_in_progress';\r\n    this.updatedBy = userId;\r\n  }\r\n\r\n  /**\r\n   * Resolve violation\r\n   */\r\n  resolve(userId: string, resolution: string): void {\r\n    this.status = 'resolved';\r\n    this.resolvedAt = new Date();\r\n    this.updatedBy = userId;\r\n    \r\n    if (!this.details.remediation) {\r\n      this.details.remediation = {};\r\n    }\r\n    \r\n    this.details.remediation.resolution = resolution;\r\n    this.details.remediation.resolvedBy = userId;\r\n    this.details.remediation.resolvedAt = this.resolvedAt.toISOString();\r\n  }\r\n\r\n  /**\r\n   * Accept risk\r\n   */\r\n  acceptRisk(userId: string, justification: string): void {\r\n    this.status = 'accepted_risk';\r\n    this.resolvedAt = new Date();\r\n    this.updatedBy = userId;\r\n    \r\n    if (!this.details.riskAssessment) {\r\n      this.details.riskAssessment = {\r\n        likelihood: 'medium',\r\n        impact: 'medium',\r\n        riskScore: 5,\r\n        riskLevel: 'medium',\r\n      };\r\n    }\r\n    \r\n    this.details.riskAssessment.acceptanceJustification = justification;\r\n    this.details.riskAssessment.acceptedBy = userId;\r\n    this.details.riskAssessment.acceptedAt = this.resolvedAt.toISOString();\r\n  }\r\n\r\n  /**\r\n   * Mark as false positive\r\n   */\r\n  markAsFalsePositive(userId: string, reason: string): void {\r\n    this.status = 'false_positive';\r\n    this.resolvedAt = new Date();\r\n    this.updatedBy = userId;\r\n    \r\n    if (!this.details.metadata) {\r\n      this.details.metadata = {};\r\n    }\r\n    \r\n    this.details.metadata.falsePositiveReason = reason;\r\n    this.details.metadata.markedBy = userId;\r\n    this.details.metadata.markedAt = this.resolvedAt.toISOString();\r\n  }\r\n\r\n  /**\r\n   * Add evidence\r\n   */\r\n  addEvidence(evidence: any): void {\r\n    if (!this.details.evidence) {\r\n      this.details.evidence = [];\r\n    }\r\n    \r\n    this.details.evidence.push({\r\n      ...evidence,\r\n      timestamp: new Date().toISOString(),\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Update risk assessment\r\n   */\r\n  updateRiskAssessment(riskAssessment: any): void {\r\n    this.details.riskAssessment = {\r\n      ...this.details.riskAssessment,\r\n      ...riskAssessment,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Add remediation action\r\n   */\r\n  addRemediationAction(action: string, type: 'immediate' | 'short_term' | 'long_term' | 'preventive'): void {\r\n    if (!this.details.remediation) {\r\n      this.details.remediation = {};\r\n    }\r\n    \r\n    const actionKey = `${type}Actions`;\r\n    if (!this.details.remediation[actionKey]) {\r\n      this.details.remediation[actionKey] = [];\r\n    }\r\n    \r\n    this.details.remediation[actionKey].push(action);\r\n  }\r\n\r\n  /**\r\n   * Get SLA hours based on severity\r\n   */\r\n  private getSlaHours(): number {\r\n    const slaMatrix = {\r\n      critical: 4,   // 4 hours\r\n      high: 24,      // 1 day\r\n      medium: 72,    // 3 days\r\n      low: 168,      // 1 week\r\n    };\r\n    \r\n    return slaMatrix[this.severity] || 168;\r\n  }\r\n\r\n  /**\r\n   * Calculate default risk score\r\n   */\r\n  private calculateDefaultRiskScore(): number {\r\n    const severityScores = { low: 2, medium: 5, high: 8, critical: 10 };\r\n    return severityScores[this.severity] || 5;\r\n  }\r\n\r\n  /**\r\n   * Generate violation summary\r\n   */\r\n  generateSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      title: this.title,\r\n      violationType: this.violationType,\r\n      severity: this.severity,\r\n      status: this.status,\r\n      detectedAt: this.detectedAt,\r\n      resolvedAt: this.resolvedAt,\r\n      isOpen: this.isOpen,\r\n      isCritical: this.isCritical,\r\n      isOverdue: this.isOverdue,\r\n      ageInHours: this.ageInHours,\r\n      timeToResolutionHours: this.timeToResolutionHours,\r\n      riskScore: this.riskScore,\r\n      assignedTo: this.assignedTo,\r\n      affectedSystemsCount: this.details.affectedSystems?.length || 0,\r\n      affectedUsersCount: this.details.affectedUsers?.length || 0,\r\n      evidenceCount: this.details.evidence?.length || 0,\r\n      tags: this.tags,\r\n      createdAt: this.createdAt,\r\n      updatedAt: this.updatedAt,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export violation for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      violation: this.generateSummary(),\r\n      details: this.details,\r\n      policy: this.policy ? {\r\n        id: this.policy.id,\r\n        name: this.policy.name,\r\n        type: this.policy.type,\r\n      } : null,\r\n      assessment: this.assessment ? {\r\n        id: this.assessment.id,\r\n        name: this.assessment.name,\r\n        frameworkId: this.assessment.frameworkId,\r\n      } : null,\r\n      exportedAt: new Date().toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check if violation requires immediate attention\r\n   */\r\n  requiresImmediateAttention(): boolean {\r\n    return this.isCritical || \r\n           this.isOverdue || \r\n           this.details.compliance?.reportingRequired ||\r\n           this.details.businessImpact?.financialImpact > 100000;\r\n  }\r\n\r\n  /**\r\n   * Get compliance frameworks affected\r\n   */\r\n  getAffectedFrameworks(): string[] {\r\n    return this.details.compliance?.frameworks || [];\r\n  }\r\n\r\n  /**\r\n   * Get affected controls\r\n   */\r\n  getAffectedControls(): string[] {\r\n    return this.details.violatedControls || [];\r\n  }\r\n}\r\n"], "version": 3}