eaaed5c453afc7c18eee64cab5f56ab4
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GoogleOAuthStrategy_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoogleOAuthStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_google_oauth20_1 = require("passport-google-oauth20");
const config_1 = require("@nestjs/config");
const auth_service_1 = require("../auth.service");
/**
 * Google OAuth 2.0 authentication strategy
 * Handles Google OAuth authentication flow
 */
let GoogleOAuthStrategy = GoogleOAuthStrategy_1 = class GoogleOAuthStrategy extends (0, passport_1.PassportStrategy)(passport_google_oauth20_1.Strategy, 'google') {
    constructor(configService, authService) {
        const authConfig = configService.get('auth');
        super({
            clientID: authConfig.oauth.google.clientId,
            clientSecret: authConfig.oauth.google.clientSecret,
            callbackURL: authConfig.oauth.google.callbackUrl,
            scope: ['email', 'profile'],
            passReqToCallback: true,
        });
        this.configService = configService;
        this.authService = authService;
        this.logger = new common_1.Logger(GoogleOAuthStrategy_1.name);
    }
    /**
     * Validate Google OAuth user
     * @param request Express request object
     * @param accessToken OAuth access token
     * @param refreshToken OAuth refresh token
     * @param profile Google user profile
     * @param done Passport callback
     */
    async validate(request, accessToken, refreshToken, profile, done) {
        try {
            this.logger.debug('Validating Google OAuth user', {
                profileId: profile.id,
                email: profile.emails?.[0]?.value,
                ipAddress: request.ip,
            });
            // Extract user information from Google profile
            const email = profile.emails?.[0]?.value;
            const firstName = profile.name?.givenName || '';
            const lastName = profile.name?.familyName || '';
            const avatarUrl = profile.photos?.[0]?.value;
            if (!email) {
                this.logger.warn('Google OAuth profile missing email', {
                    profileId: profile.id,
                });
                return done(new Error('Email not provided by Google'), null);
            }
            // Find or create user
            const user = await this.authService.findOrCreateOAuthUser({
                email,
                firstName,
                lastName,
                avatarUrl,
                provider: 'google',
                providerId: profile.id,
                providerData: {
                    accessToken,
                    refreshToken,
                    profile: {
                        id: profile.id,
                        displayName: profile.displayName,
                        emails: profile.emails,
                        photos: profile.photos,
                    },
                },
            });
            if (!user) {
                this.logger.warn('Failed to create or find OAuth user', {
                    email,
                    provider: 'google',
                });
                return done(new Error('Failed to authenticate user'), null);
            }
            // Check if user account is active
            if (!user.canLogin) {
                this.logger.warn('OAuth user cannot login', {
                    userId: user.id,
                    status: user.status,
                    emailVerified: user.emailVerified,
                });
                return done(new Error('Account is not active'), null);
            }
            // Add OAuth metadata to user object
            const userWithMetadata = {
                ...user,
                authMetadata: {
                    provider: 'google',
                    providerId: profile.id,
                    ipAddress: request.ip,
                    userAgent: request.get('User-Agent'),
                    timestamp: new Date(),
                    method: 'oauth',
                },
            };
            this.logger.log('Google OAuth authentication successful', {
                userId: user.id,
                email: user.email,
                ipAddress: request.ip,
            });
            return done(null, userWithMetadata);
        }
        catch (error) {
            this.logger.error('Google OAuth authentication error', {
                error: error.message,
                profileId: profile?.id,
                email: profile?.emails?.[0]?.value,
                ipAddress: request.ip,
            });
            return done(error, null);
        }
    }
};
exports.GoogleOAuthStrategy = GoogleOAuthStrategy;
exports.GoogleOAuthStrategy = GoogleOAuthStrategy = GoogleOAuthStrategy_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof auth_service_1.AuthService !== "undefined" && auth_service_1.AuthService) === "function" ? _b : Object])
], GoogleOAuthStrategy);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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