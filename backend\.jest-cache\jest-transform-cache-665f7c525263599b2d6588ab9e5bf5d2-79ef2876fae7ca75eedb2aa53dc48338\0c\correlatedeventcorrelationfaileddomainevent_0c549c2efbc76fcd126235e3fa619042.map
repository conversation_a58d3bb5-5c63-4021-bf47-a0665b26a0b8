{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\correlated-event-correlation-failed.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AA8B5E;;;;;;;;;;;;GAYG;AACH,MAAa,2CAA4C,SAAQ,+BAA0D;IACzH,YACE,WAA2B,EAC3B,SAAoD,EACpD,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,IAAI,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,KAAK,KAAK,CAAC,CAAC,0CAA0C;IACzF,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,OAAO,KAAK,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,MAAM,kBAAkB,GAAG;YACzB,yBAAyB;YACzB,4BAA4B;YAC5B,kBAAkB;YAClB,oBAAoB;SACrB,CAAC;QAEF,OAAO,IAAI,CAAC,mBAAmB;YACxB,CAAC,IAAI,CAAC,SAAS,IAAI,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/D,CAAC,IAAI,CAAC,WAAW,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,WAAW,GAAkF;YACjG,mBAAmB;YACnB,SAAS,EAAE,WAAW;YACtB,eAAe,EAAE,WAAW;YAC5B,qBAAqB,EAAE,WAAW;YAClC,qBAAqB,EAAE,WAAW;YAClC,mBAAmB,EAAE,WAAW;YAEhC,uBAAuB;YACvB,qBAAqB,EAAE,eAAe;YACtC,uBAAuB,EAAE,eAAe;YACxC,oBAAoB,EAAE,eAAe;YACrC,wBAAwB,EAAE,eAAe;YAEzC,cAAc;YACd,oBAAoB,EAAE,MAAM;YAC5B,yBAAyB,EAAE,MAAM;YACjC,wBAAwB,EAAE,MAAM;YAChC,gBAAgB,EAAE,MAAM;YAExB,gBAAgB;YAChB,yBAAyB,EAAE,QAAQ;YACnC,4BAA4B,EAAE,QAAQ;YACtC,kBAAkB,EAAE,QAAQ;YAC5B,oBAAoB,EAAE,QAAQ;SAC/B,CAAC;QAEF,OAAO,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC7B,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC7B,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,qBAAqB,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;QAC3D,CAAC;QAED,QAAQ,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAClC,KAAK,WAAW;gBACd,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBACzC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAC3C,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;gBAC5C,MAAM;YAER,KAAK,eAAe;gBAClB,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;gBACtD,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBACzC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAC3C,MAAM;YAER,KAAK,MAAM;gBACT,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;gBAC1C,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAC7C,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;gBACjD,MAAM;YAER,KAAK,QAAQ;gBACX,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBAClD,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAC7C,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACnC,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;gBAC5C,MAAM;YAER,KAAK,SAAS;gBACZ,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;gBAC1C,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;gBAC/C,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBACzC,MAAM;QACV,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAC5C,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,yBAAyB;QAMvB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE3C,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;YAC5B,OAAO;gBACL,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,YAAY;gBAC7B,OAAO,EAAE,UAAU;gBACnB,cAAc,EAAE,EAAE;aACnB,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACpD,OAAO;gBACL,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,KAAK;gBACtB,OAAO,EAAE,MAAM;gBACf,cAAc,EAAE,EAAE;aACnB,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,KAAK,QAAQ,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,QAAQ,EAAE,CAAC;YACpE,OAAO;gBACL,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,KAAK;gBACtB,OAAO,EAAE,QAAQ;gBACjB,cAAc,EAAE,EAAE;aACnB,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,eAAe,EAAE,CAAC;YAClD,OAAO;gBACL,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,KAAK;gBACtB,OAAO,EAAE,QAAQ;gBACjB,cAAc,EAAE,GAAG;aACpB,CAAC;QACJ,CAAC;QAED,OAAO;YACL,cAAc,EAAE,KAAK;YACrB,eAAe,EAAE,KAAK;YACtB,OAAO,EAAE,KAAK;YACd,cAAc,EAAE,GAAG;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAKhB,MAAM,OAAO,GAIR,EAAE,CAAC;QAER,kBAAkB;QAClB,OAAO,CAAC,IAAI,CAAC;YACX,MAAM,EAAE,sBAAsB;YAC9B,KAAK,EAAE,CAAC;YACR,IAAI,EAAE;gBACJ,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;gBAChC,QAAQ,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBACnC,QAAQ,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBACnC,UAAU,EAAE,IAAI,CAAC,SAAS,IAAI,SAAS;aACxC;SACF,CAAC,CAAC;QAEH,gCAAgC;QAChC,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,mCAAmC;gBAC3C,KAAK,EAAE,CAAC;aACT,CAAC,CAAC;QACL,CAAC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,+BAA+B;gBACvC,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE;oBACJ,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;oBAChC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;iBACvC;aACF,CAAC,CAAC;QACL,CAAC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,4BAA4B;gBACpC,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE;oBACJ,QAAQ,EAAE,IAAI,CAAC,kBAAkB,EAAE;iBACpC;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,+DAA+D;QAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,WAAW;QACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,YAAY;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;QAE5E,wCAAwC;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,KAAK,CAAC;QAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,eAAe;QAsBb,OAAO;YACL,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC9C,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;YAChD,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YACrC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAC3C,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7C,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAC3C,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,sBAAsB,EAAE,IAAI,CAAC,yBAAyB,EAAE;YACxD,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;SAC3C,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;SACrC,CAAC;IACJ,CAAC;CACF;AA/aD,kGA+aC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\correlated-event-correlation-failed.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\n\r\n/**\r\n * Correlated Event Correlation Failed Domain Event Data\r\n */\r\nexport interface CorrelatedEventCorrelationFailedEventData {\r\n  /** Enriched event ID that failed correlation */\r\n  enrichedEventId: UniqueEntityId;\r\n  /** Error message describing the failure */\r\n  error: string;\r\n  /** Current correlation attempt number */\r\n  attempt: number;\r\n  /** Whether maximum attempts have been exceeded */\r\n  maxAttemptsExceeded: boolean;\r\n  /** Failure timestamp */\r\n  failedAt?: Date;\r\n  /** Error code for categorization */\r\n  errorCode?: string;\r\n  /** Stack trace or detailed error information */\r\n  errorDetails?: string;\r\n  /** Correlation rules that were being processed */\r\n  failedRules?: string[];\r\n  /** Processing context at time of failure */\r\n  processingContext?: Record<string, any>;\r\n  /** Whether this failure is retryable */\r\n  isRetryable?: boolean;\r\n  /** Suggested retry delay in milliseconds */\r\n  retryDelayMs?: number;\r\n}\r\n\r\n/**\r\n * Correlated Event Correlation Failed Domain Event\r\n * \r\n * Raised when correlation processing fails for a correlated event.\r\n * This event triggers various error handling and recovery processes including:\r\n * - Error logging and monitoring\r\n * - Retry scheduling (if applicable)\r\n * - Alert generation for persistent failures\r\n * - Metrics collection for failure analysis\r\n * - Dead letter queue management\r\n * - Escalation to operations team\r\n * - Fallback processing workflows\r\n */\r\nexport class CorrelatedEventCorrelationFailedDomainEvent extends BaseDomainEvent<CorrelatedEventCorrelationFailedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: CorrelatedEventCorrelationFailedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the enriched event ID\r\n   */\r\n  get enrichedEventId(): UniqueEntityId {\r\n    return this.eventData.enrichedEventId;\r\n  }\r\n\r\n  /**\r\n   * Get the error message\r\n   */\r\n  get error(): string {\r\n    return this.eventData.error;\r\n  }\r\n\r\n  /**\r\n   * Get the current attempt number\r\n   */\r\n  get attempt(): number {\r\n    return this.eventData.attempt;\r\n  }\r\n\r\n  /**\r\n   * Check if maximum attempts have been exceeded\r\n   */\r\n  get maxAttemptsExceeded(): boolean {\r\n    return this.eventData.maxAttemptsExceeded;\r\n  }\r\n\r\n  /**\r\n   * Get the failure timestamp\r\n   */\r\n  get failedAt(): Date {\r\n    return this.eventData.failedAt || this.occurredOn;\r\n  }\r\n\r\n  /**\r\n   * Get the error code\r\n   */\r\n  get errorCode(): string | undefined {\r\n    return this.eventData.errorCode;\r\n  }\r\n\r\n  /**\r\n   * Get detailed error information\r\n   */\r\n  get errorDetails(): string | undefined {\r\n    return this.eventData.errorDetails;\r\n  }\r\n\r\n  /**\r\n   * Get the failed rules\r\n   */\r\n  get failedRules(): string[] {\r\n    return this.eventData.failedRules || [];\r\n  }\r\n\r\n  /**\r\n   * Get the processing context\r\n   */\r\n  get processingContext(): Record<string, any> {\r\n    return this.eventData.processingContext || {};\r\n  }\r\n\r\n  /**\r\n   * Check if this failure is retryable\r\n   */\r\n  get isRetryable(): boolean {\r\n    return this.eventData.isRetryable !== false; // Default to true unless explicitly false\r\n  }\r\n\r\n  /**\r\n   * Get suggested retry delay\r\n   */\r\n  get retryDelayMs(): number {\r\n    return this.eventData.retryDelayMs || this.calculateRetryDelay();\r\n  }\r\n\r\n  /**\r\n   * Check if this is the first failure\r\n   */\r\n  isFirstFailure(): boolean {\r\n    return this.attempt === 1;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a repeated failure\r\n   */\r\n  isRepeatedFailure(): boolean {\r\n    return this.attempt > 1;\r\n  }\r\n\r\n  /**\r\n   * Check if this failure should trigger an alert\r\n   */\r\n  shouldTriggerAlert(): boolean {\r\n    return this.maxAttemptsExceeded || this.attempt >= 2 || !this.isRetryable;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a critical failure\r\n   */\r\n  isCriticalFailure(): boolean {\r\n    const criticalErrorCodes = [\r\n      'CORRELATION_ENGINE_DOWN',\r\n      'DATABASE_CONNECTION_FAILED',\r\n      'MEMORY_EXHAUSTED',\r\n      'SECURITY_VIOLATION',\r\n    ];\r\n\r\n    return this.maxAttemptsExceeded || \r\n           (this.errorCode && criticalErrorCodes.includes(this.errorCode)) ||\r\n           !this.isRetryable;\r\n  }\r\n\r\n  /**\r\n   * Get failure category\r\n   */\r\n  getFailureCategory(): 'transient' | 'configuration' | 'data' | 'system' | 'unknown' {\r\n    if (!this.errorCode) {\r\n      return 'unknown';\r\n    }\r\n\r\n    const categoryMap: Record<string, 'transient' | 'configuration' | 'data' | 'system' | 'unknown'> = {\r\n      // Transient errors\r\n      'TIMEOUT': 'transient',\r\n      'NETWORK_ERROR': 'transient',\r\n      'SERVICE_UNAVAILABLE': 'transient',\r\n      'RATE_LIMIT_EXCEEDED': 'transient',\r\n      'TEMPORARY_FAILURE': 'transient',\r\n\r\n      // Configuration errors\r\n      'INVALID_RULE_CONFIG': 'configuration',\r\n      'MISSING_CONFIGURATION': 'configuration',\r\n      'INVALID_PARAMETERS': 'configuration',\r\n      'RULE_VALIDATION_FAILED': 'configuration',\r\n\r\n      // Data errors\r\n      'INVALID_EVENT_DATA': 'data',\r\n      'MISSING_REQUIRED_FIELDS': 'data',\r\n      'DATA_VALIDATION_FAILED': 'data',\r\n      'CORRUPTED_DATA': 'data',\r\n\r\n      // System errors\r\n      'CORRELATION_ENGINE_DOWN': 'system',\r\n      'DATABASE_CONNECTION_FAILED': 'system',\r\n      'MEMORY_EXHAUSTED': 'system',\r\n      'SECURITY_VIOLATION': 'system',\r\n    };\r\n\r\n    return categoryMap[this.errorCode] || 'unknown';\r\n  }\r\n\r\n  /**\r\n   * Get failure severity\r\n   */\r\n  getFailureSeverity(): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (this.isCriticalFailure()) {\r\n      return 'critical';\r\n    }\r\n\r\n    if (this.maxAttemptsExceeded) {\r\n      return 'high';\r\n    }\r\n\r\n    if (this.isRepeatedFailure()) {\r\n      return 'medium';\r\n    }\r\n\r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get recommended recovery actions\r\n   */\r\n  getRecoveryActions(): string[] {\r\n    const actions: string[] = [];\r\n\r\n    if (this.isRetryable && !this.maxAttemptsExceeded) {\r\n      actions.push(`Schedule retry in ${this.retryDelayMs}ms`);\r\n    }\r\n\r\n    switch (this.getFailureCategory()) {\r\n      case 'transient':\r\n        actions.push('Monitor system resources');\r\n        actions.push('Check network connectivity');\r\n        actions.push('Verify service availability');\r\n        break;\r\n\r\n      case 'configuration':\r\n        actions.push('Review correlation rule configuration');\r\n        actions.push('Validate rule parameters');\r\n        actions.push('Check configuration syntax');\r\n        break;\r\n\r\n      case 'data':\r\n        actions.push('Validate input event data');\r\n        actions.push('Check data format and schema');\r\n        actions.push('Review data transformation logic');\r\n        break;\r\n\r\n      case 'system':\r\n        actions.push('Check system health and resources');\r\n        actions.push('Verify database connectivity');\r\n        actions.push('Review system logs');\r\n        actions.push('Escalate to operations team');\r\n        break;\r\n\r\n      case 'unknown':\r\n        actions.push('Investigate error details');\r\n        actions.push('Review correlation engine logs');\r\n        actions.push('Contact development team');\r\n        break;\r\n    }\r\n\r\n    if (this.maxAttemptsExceeded) {\r\n      actions.push('Move to dead letter queue');\r\n      actions.push('Generate manual review task');\r\n      actions.push('Notify operations team');\r\n    }\r\n\r\n    if (this.shouldTriggerAlert()) {\r\n      actions.push('Generate failure alert');\r\n      actions.push('Update monitoring dashboards');\r\n    }\r\n\r\n    return actions;\r\n  }\r\n\r\n  /**\r\n   * Get escalation requirements\r\n   */\r\n  getEscalationRequirements(): {\r\n    shouldEscalate: boolean;\r\n    escalationLevel: 'ops' | 'dev' | 'management';\r\n    urgency: 'low' | 'medium' | 'high' | 'critical';\r\n    timeoutMinutes: number;\r\n  } {\r\n    const severity = this.getFailureSeverity();\r\n\r\n    if (severity === 'critical') {\r\n      return {\r\n        shouldEscalate: true,\r\n        escalationLevel: 'management',\r\n        urgency: 'critical',\r\n        timeoutMinutes: 15,\r\n      };\r\n    }\r\n\r\n    if (severity === 'high' || this.maxAttemptsExceeded) {\r\n      return {\r\n        shouldEscalate: true,\r\n        escalationLevel: 'ops',\r\n        urgency: 'high',\r\n        timeoutMinutes: 30,\r\n      };\r\n    }\r\n\r\n    if (severity === 'medium' && this.getFailureCategory() === 'system') {\r\n      return {\r\n        shouldEscalate: true,\r\n        escalationLevel: 'ops',\r\n        urgency: 'medium',\r\n        timeoutMinutes: 60,\r\n      };\r\n    }\r\n\r\n    if (this.getFailureCategory() === 'configuration') {\r\n      return {\r\n        shouldEscalate: true,\r\n        escalationLevel: 'dev',\r\n        urgency: 'medium',\r\n        timeoutMinutes: 120,\r\n      };\r\n    }\r\n\r\n    return {\r\n      shouldEscalate: false,\r\n      escalationLevel: 'ops',\r\n      urgency: 'low',\r\n      timeoutMinutes: 240,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get metrics to update based on failure\r\n   */\r\n  getMetricsToUpdate(): Array<{\r\n    metric: string;\r\n    value: number;\r\n    tags?: Record<string, string>;\r\n  }> {\r\n    const metrics: Array<{\r\n      metric: string;\r\n      value: number;\r\n      tags?: Record<string, string>;\r\n    }> = [];\r\n\r\n    // Failure counter\r\n    metrics.push({\r\n      metric: 'correlation_failures',\r\n      value: 1,\r\n      tags: {\r\n        attempt: this.attempt.toString(),\r\n        category: this.getFailureCategory(),\r\n        severity: this.getFailureSeverity(),\r\n        error_code: this.errorCode || 'unknown',\r\n      },\r\n    });\r\n\r\n    // Max attempts exceeded counter\r\n    if (this.maxAttemptsExceeded) {\r\n      metrics.push({\r\n        metric: 'correlation_max_attempts_exceeded',\r\n        value: 1,\r\n      });\r\n    }\r\n\r\n    // Retry counter\r\n    if (this.isRetryable && !this.maxAttemptsExceeded) {\r\n      metrics.push({\r\n        metric: 'correlation_retries_scheduled',\r\n        value: 1,\r\n        tags: {\r\n          attempt: this.attempt.toString(),\r\n          delay_ms: this.retryDelayMs.toString(),\r\n        },\r\n      });\r\n    }\r\n\r\n    // Alert counter\r\n    if (this.shouldTriggerAlert()) {\r\n      metrics.push({\r\n        metric: 'correlation_failure_alerts',\r\n        value: 1,\r\n        tags: {\r\n          severity: this.getFailureSeverity(),\r\n        },\r\n      });\r\n    }\r\n\r\n    return metrics;\r\n  }\r\n\r\n  /**\r\n   * Calculate retry delay based on attempt number\r\n   */\r\n  private calculateRetryDelay(): number {\r\n    // Exponential backoff: 2^attempt * 1000ms, capped at 5 minutes\r\n    const baseDelay = 1000; // 1 second\r\n    const maxDelay = 300000; // 5 minutes\r\n    const delay = Math.min(baseDelay * Math.pow(2, this.attempt - 1), maxDelay);\r\n    \r\n    // Add jitter to prevent thundering herd\r\n    const jitter = Math.random() * 0.1 * delay;\r\n    return Math.floor(delay + jitter);\r\n  }\r\n\r\n  /**\r\n   * Get event summary for handlers\r\n   */\r\n  getEventSummary(): {\r\n    correlatedEventId: string;\r\n    enrichedEventId: string;\r\n    error: string;\r\n    attempt: number;\r\n    maxAttemptsExceeded: boolean;\r\n    failedAt: Date;\r\n    errorCode?: string;\r\n    errorDetails?: string;\r\n    failedRules: string[];\r\n    isRetryable: boolean;\r\n    retryDelayMs: number;\r\n    isFirstFailure: boolean;\r\n    isRepeatedFailure: boolean;\r\n    shouldTriggerAlert: boolean;\r\n    isCriticalFailure: boolean;\r\n    failureCategory: 'transient' | 'configuration' | 'data' | 'system' | 'unknown';\r\n    failureSeverity: 'low' | 'medium' | 'high' | 'critical';\r\n    recoveryActions: string[];\r\n    escalationRequirements: ReturnType<typeof this.getEscalationRequirements>;\r\n    metricsToUpdate: ReturnType<typeof this.getMetricsToUpdate>;\r\n  } {\r\n    return {\r\n      correlatedEventId: this.aggregateId.toString(),\r\n      enrichedEventId: this.enrichedEventId.toString(),\r\n      error: this.error,\r\n      attempt: this.attempt,\r\n      maxAttemptsExceeded: this.maxAttemptsExceeded,\r\n      failedAt: this.failedAt,\r\n      errorCode: this.errorCode,\r\n      errorDetails: this.errorDetails,\r\n      failedRules: this.failedRules,\r\n      isRetryable: this.isRetryable,\r\n      retryDelayMs: this.retryDelayMs,\r\n      isFirstFailure: this.isFirstFailure(),\r\n      isRepeatedFailure: this.isRepeatedFailure(),\r\n      shouldTriggerAlert: this.shouldTriggerAlert(),\r\n      isCriticalFailure: this.isCriticalFailure(),\r\n      failureCategory: this.getFailureCategory(),\r\n      failureSeverity: this.getFailureSeverity(),\r\n      recoveryActions: this.getRecoveryActions(),\r\n      escalationRequirements: this.getEscalationRequirements(),\r\n      metricsToUpdate: this.getMetricsToUpdate(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventSummary: this.getEventSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}