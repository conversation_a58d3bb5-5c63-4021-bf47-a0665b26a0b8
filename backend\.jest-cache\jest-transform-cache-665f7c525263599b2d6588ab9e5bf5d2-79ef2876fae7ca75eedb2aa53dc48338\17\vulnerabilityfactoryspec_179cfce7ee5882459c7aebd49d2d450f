60da91e48686ce8d07a1b6334e8bdf24
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vulnerability_factory_1 = require("../vulnerability.factory");
const vulnerability_entity_1 = require("../../entities/vulnerability/vulnerability.entity");
const threat_severity_enum_1 = require("../../enums/threat-severity.enum");
const confidence_level_enum_1 = require("../../enums/confidence-level.enum");
const cvss_score_value_object_1 = require("../../value-objects/threat-indicators/cvss-score.value-object");
const shared_kernel_1 = require("../../../../../shared-kernel");
describe('VulnerabilityFactory', () => {
    let mockAffectedAssets;
    let mockDiscovery;
    beforeEach(() => {
        mockAffectedAssets = [{
                assetId: 'asset-1',
                assetName: 'Web Server 01',
                assetType: 'server',
                criticality: 'high',
                affectedComponents: [{
                        name: 'Apache HTTP Server',
                        version: '2.4.41',
                        type: 'software',
                    }],
                exposure: 'external',
                businessImpact: ['customer_facing_service'],
            }];
        mockDiscovery = {
            method: 'automated_scan',
            source: 'Nessus Scanner',
            discoveredAt: new Date('2024-01-15T10:00:00Z'),
            discoveredBy: 'Security Team',
            details: 'Discovered during routine vulnerability scan',
        };
    });
    describe('create', () => {
        it('should create a vulnerability with basic options', () => {
            const options = {
                title: 'SQL Injection Vulnerability',
                description: 'SQL injection vulnerability in login form',
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                category: 'injection',
                type: 'sql_injection',
                affectedAssets: mockAffectedAssets,
                discovery: mockDiscovery,
            };
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.create(options);
            expect(vulnerability).toBeInstanceOf(vulnerability_entity_1.Vulnerability);
            expect(vulnerability.title).toBe('SQL Injection Vulnerability');
            expect(vulnerability.description).toBe('SQL injection vulnerability in login form');
            expect(vulnerability.severity).toBe(threat_severity_enum_1.ThreatSeverity.HIGH);
            expect(vulnerability.category).toBe('injection');
            expect(vulnerability.type).toBe('sql_injection');
            expect(vulnerability.affectedAssets).toHaveLength(1);
            expect(vulnerability.status).toBe(vulnerability_entity_1.VulnerabilityStatus.DISCOVERED);
        });
        it('should create a vulnerability with all options', () => {
            const cvssScore = cvss_score_value_object_1.CVSSScore.createV3_1(8.5, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N');
            const options = {
                id: shared_kernel_1.UniqueEntityId.generate(),
                cveId: 'CVE-2024-1234',
                title: 'Remote Code Execution',
                description: 'Critical RCE vulnerability',
                severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
                category: 'remote_code_execution',
                type: 'buffer_overflow',
                confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                cvssScores: [cvssScore],
                affectedAssets: mockAffectedAssets,
                discovery: mockDiscovery,
                tags: ['critical', 'rce'],
                attributes: { scanner: 'custom' },
            };
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.create(options);
            expect(vulnerability.id.equals(options.id)).toBe(true);
            expect(vulnerability.cveId).toBe('CVE-2024-1234');
            expect(vulnerability.confidence).toBe(confidence_level_enum_1.ConfidenceLevel.HIGH);
            expect(vulnerability.cvssScores).toHaveLength(1);
            expect(vulnerability.tags).toContain('critical');
            expect(vulnerability.tags).toContain('rce');
            expect(vulnerability.attributes.scanner).toBe('custom');
        });
    });
    describe('fromCVEDatabase', () => {
        it('should create vulnerability from CVE database entry', () => {
            const cveEntry = {
                cveId: 'CVE-2024-5678',
                title: 'Apache HTTP Server Buffer Overflow',
                description: 'Buffer overflow vulnerability in Apache HTTP Server',
                cvssV3: {
                    baseScore: 9.8,
                    vectorString: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',
                    exploitabilityScore: 3.9,
                    impactScore: 5.9,
                },
                publishedDate: new Date('2024-01-10T00:00:00Z'),
                lastModifiedDate: new Date('2024-01-15T00:00:00Z'),
                affectedProducts: [{
                        vendor: 'Apache',
                        product: 'HTTP Server',
                        versions: ['2.4.0', '2.4.41'],
                    }],
                references: [{
                        url: 'https://httpd.apache.org/security/vulnerabilities_24.html',
                        source: 'Apache',
                        tags: ['Vendor Advisory'],
                    }],
                cweIds: ['CWE-119'],
            };
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.fromCVEDatabase(cveEntry, mockAffectedAssets);
            expect(vulnerability.cveId).toBe('CVE-2024-5678');
            expect(vulnerability.title).toBe('Apache HTTP Server Buffer Overflow');
            expect(vulnerability.severity).toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
            expect(vulnerability.category).toBe('memory_corruption');
            expect(vulnerability.confidence).toBe(confidence_level_enum_1.ConfidenceLevel.HIGH);
            expect(vulnerability.cvssScores).toHaveLength(1);
            expect(vulnerability.cvssScores[0].baseScore).toBe(9.8);
            expect(vulnerability.tags).toContain('cve');
            expect(vulnerability.tags).toContain('nvd');
            expect(vulnerability.tags).toContain('cwe-CWE-119');
            expect(vulnerability.discovery.method).toBe('threat_intelligence');
            expect(vulnerability.discovery.source).toBe('CVE Database');
        });
        it('should handle CVE entry with both CVSS v2 and v3 scores', () => {
            const cveEntry = {
                cveId: 'CVE-2024-9999',
                title: 'Test Vulnerability',
                description: 'Test vulnerability with multiple CVSS scores',
                cvssV2: {
                    baseScore: 7.5,
                    vectorString: '(AV:N/AC:L/Au:N/C:P/I:P/A:P)',
                },
                cvssV3: {
                    baseScore: 8.8,
                    vectorString: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H',
                },
                publishedDate: new Date('2024-01-01T00:00:00Z'),
                lastModifiedDate: new Date('2024-01-01T00:00:00Z'),
                affectedProducts: [],
                references: [],
                cweIds: [],
            };
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.fromCVEDatabase(cveEntry, mockAffectedAssets);
            expect(vulnerability.cvssScores).toHaveLength(2);
            expect(vulnerability.severity).toBe(threat_severity_enum_1.ThreatSeverity.HIGH); // Based on higher CVSS v3 score
        });
    });
    describe('fromScanResult', () => {
        it('should create vulnerability from scan result', () => {
            const scanResult = {
                scannerId: 'SCAN-001',
                cveId: 'CVE-2024-1111',
                name: 'Cross-Site Scripting (XSS)',
                description: 'Reflected XSS vulnerability in search parameter',
                severity: 'Medium',
                cvssScore: 6.1,
                cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N',
                asset: {
                    id: 'web-app-01',
                    name: 'Main Web Application',
                    type: 'web_application',
                    ipAddress: '*************',
                    hostname: 'webapp.example.com',
                },
                service: {
                    port: 443,
                    protocol: 'HTTPS',
                    service: 'nginx',
                    version: '1.18.0',
                },
                scanner: {
                    name: 'OWASP ZAP',
                    version: '2.12.0',
                    ruleId: '40012',
                    confidence: 85,
                },
                scanTimestamp: new Date('2024-01-20T14:30:00Z'),
                metadata: {
                    url: '/search?q=<script>alert(1)</script>',
                    parameter: 'q',
                },
            };
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.fromScanResult(scanResult);
            expect(vulnerability.cveId).toBe('CVE-2024-1111');
            expect(vulnerability.title).toBe('Cross-Site Scripting (XSS)');
            expect(vulnerability.severity).toBe(threat_severity_enum_1.ThreatSeverity.MEDIUM);
            expect(vulnerability.category).toBe('injection');
            expect(vulnerability.confidence).toBe(confidence_level_enum_1.ConfidenceLevel.VERY_HIGH);
            expect(vulnerability.affectedAssets).toHaveLength(1);
            expect(vulnerability.affectedAssets[0].assetId).toBe('web-app-01');
            expect(vulnerability.affectedAssets[0].assetName).toBe('Main Web Application');
            expect(vulnerability.discovery.method).toBe('automated_scan');
            expect(vulnerability.discovery.source).toBe('OWASP ZAP');
            expect(vulnerability.tags).toContain('scan-result');
            expect(vulnerability.tags).toContain('owasp zap');
            expect(vulnerability.attributes.scannerId).toBe('SCAN-001');
        });
        it('should handle scan result without CVSS vector', () => {
            const scanResult = {
                scannerId: 'SCAN-002',
                name: 'Weak SSL Configuration',
                description: 'SSL/TLS configuration weakness',
                severity: 'Low',
                asset: {
                    id: 'server-01',
                    name: 'Web Server',
                    type: 'server',
                },
                scanner: {
                    name: 'Nessus',
                    version: '10.4.0',
                    ruleId: '51192',
                    confidence: 70,
                },
                scanTimestamp: new Date('2024-01-20T15:00:00Z'),
            };
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.fromScanResult(scanResult);
            expect(vulnerability.cvssScores).toHaveLength(0);
            expect(vulnerability.severity).toBe(threat_severity_enum_1.ThreatSeverity.LOW);
            expect(vulnerability.confidence).toBe(confidence_level_enum_1.ConfidenceLevel.HIGH);
        });
    });
    describe('withThreatIntelligence', () => {
        it('should enhance vulnerability with threat intelligence data', () => {
            const baseVulnerability = vulnerability_factory_1.VulnerabilityFactory.create({
                title: 'Test Vulnerability',
                description: 'Base vulnerability for testing',
                severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                category: 'general',
                type: 'unknown',
                affectedAssets: mockAffectedAssets,
                discovery: mockDiscovery,
                tags: ['base'],
            });
            const threatIntel = {
                cveId: 'CVE-2024-2222',
                source: 'Threat Intelligence Feed',
                exploitationStatus: 'active_exploitation',
                exploits: [{
                        name: 'Public Exploit Kit',
                        type: 'public',
                        source: 'ExploitDB',
                        reliability: 90,
                        publishedDate: new Date('2024-01-18T00:00:00Z'),
                    }],
                threatActorActivity: [{
                        actor: 'APT29',
                        campaign: 'Operation SolarStorm',
                        firstObserved: new Date('2024-01-16T00:00:00Z'),
                        lastObserved: new Date('2024-01-19T00:00:00Z'),
                        confidence: 85,
                    }],
                attackPatterns: [{
                        techniqueId: 'T1190',
                        technique: 'Exploit Public-Facing Application',
                        tactic: 'Initial Access',
                    }],
                timestamp: new Date('2024-01-20T00:00:00Z'),
            };
            const enhancedVulnerability = vulnerability_factory_1.VulnerabilityFactory.withThreatIntelligence(baseVulnerability, threatIntel);
            expect(enhancedVulnerability.exploitation?.status).toBe('active_exploitation');
            expect(enhancedVulnerability.exploitation?.availableExploits).toHaveLength(1);
            expect(enhancedVulnerability.exploitation?.availableExploits[0].name).toBe('Public Exploit Kit');
            expect(enhancedVulnerability.tags).toContain('threat-intelligence');
            expect(enhancedVulnerability.tags).toContain('actively-exploited');
            expect(enhancedVulnerability.tags).toContain('threat-actor-activity');
            expect(enhancedVulnerability.attributes.threatIntelligence).toBeDefined();
            expect(enhancedVulnerability.attributes.threatIntelligence.source).toBe('Threat Intelligence Feed');
        });
    });
    describe('createCriticalAlert', () => {
        it('should create a critical vulnerability alert', () => {
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.createCriticalAlert('Zero-Day RCE in Production System', 'Critical zero-day remote code execution vulnerability discovered in production web server', 'CVE-2024-0000', mockAffectedAssets);
            expect(vulnerability.title).toBe('Zero-Day RCE in Production System');
            expect(vulnerability.severity).toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
            expect(vulnerability.category).toBe('critical-alert');
            expect(vulnerability.type).toBe('security-alert');
            expect(vulnerability.confidence).toBe(confidence_level_enum_1.ConfidenceLevel.HIGH);
            expect(vulnerability.cveId).toBe('CVE-2024-0000');
            expect(vulnerability.tags).toContain('critical');
            expect(vulnerability.tags).toContain('alert');
            expect(vulnerability.tags).toContain('immediate-response');
            expect(vulnerability.discovery.method).toBe('external_report');
            expect(vulnerability.attributes.alertType).toBe('critical');
            expect(vulnerability.attributes.requiresImmediateAttention).toBe(true);
        });
    });
    describe('createZeroDay', () => {
        it('should create a zero-day vulnerability', () => {
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.createZeroDay('Unknown Buffer Overflow', 'Previously unknown buffer overflow vulnerability discovered during incident response', mockAffectedAssets, 'incident_response');
            expect(vulnerability.title).toBe('Unknown Buffer Overflow');
            expect(vulnerability.severity).toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
            expect(vulnerability.category).toBe('zero-day');
            expect(vulnerability.type).toBe('unknown-vulnerability');
            expect(vulnerability.confidence).toBe(confidence_level_enum_1.ConfidenceLevel.MEDIUM);
            expect(vulnerability.cveId).toBeUndefined();
            expect(vulnerability.tags).toContain('zero-day');
            expect(vulnerability.tags).toContain('no-cve');
            expect(vulnerability.tags).toContain('active-exploitation');
            expect(vulnerability.discovery.method).toBe('incident_response');
            expect(vulnerability.exploitation?.status).toBe('active_exploitation');
            expect(vulnerability.attributes.zeroDay).toBe(true);
            expect(vulnerability.attributes.noCVE).toBe(true);
        });
        it('should create zero-day with different discovery methods', () => {
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.createZeroDay('Manual Testing Discovery', 'Zero-day found during manual penetration testing', mockAffectedAssets, 'manual_testing');
            expect(vulnerability.discovery.method).toBe('manual_testing');
            expect(vulnerability.attributes.discoveryMethod).toBe('manual_testing');
        });
    });
    describe('helper methods', () => {
        describe('severity mapping', () => {
            it('should map CVSS scores to threat severity correctly', () => {
                // Test through fromCVEDatabase which uses the mapping
                const createCVEEntry = (cvssScore) => ({
                    cveId: 'CVE-TEST',
                    title: 'Test',
                    description: 'Test',
                    cvssV3: {
                        baseScore: cvssScore,
                        vectorString: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N',
                    },
                    publishedDate: new Date(),
                    lastModifiedDate: new Date(),
                    affectedProducts: [],
                    references: [],
                    cweIds: [],
                });
                expect(vulnerability_factory_1.VulnerabilityFactory.fromCVEDatabase(createCVEEntry(9.5), mockAffectedAssets).severity)
                    .toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
                expect(vulnerability_factory_1.VulnerabilityFactory.fromCVEDatabase(createCVEEntry(8.0), mockAffectedAssets).severity)
                    .toBe(threat_severity_enum_1.ThreatSeverity.HIGH);
                expect(vulnerability_factory_1.VulnerabilityFactory.fromCVEDatabase(createCVEEntry(5.0), mockAffectedAssets).severity)
                    .toBe(threat_severity_enum_1.ThreatSeverity.MEDIUM);
                expect(vulnerability_factory_1.VulnerabilityFactory.fromCVEDatabase(createCVEEntry(2.0), mockAffectedAssets).severity)
                    .toBe(threat_severity_enum_1.ThreatSeverity.LOW);
            });
            it('should map scanner severity strings correctly', () => {
                const createScanResult = (severity) => ({
                    scannerId: 'test',
                    name: 'Test',
                    description: 'Test',
                    severity,
                    asset: { id: 'test', name: 'Test', type: 'test' },
                    scanner: { name: 'Test', version: '1.0', ruleId: 'test', confidence: 80 },
                    scanTimestamp: new Date(),
                });
                expect(vulnerability_factory_1.VulnerabilityFactory.fromScanResult(createScanResult('Critical')).severity)
                    .toBe(threat_severity_enum_1.ThreatSeverity.CRITICAL);
                expect(vulnerability_factory_1.VulnerabilityFactory.fromScanResult(createScanResult('High')).severity)
                    .toBe(threat_severity_enum_1.ThreatSeverity.HIGH);
                expect(vulnerability_factory_1.VulnerabilityFactory.fromScanResult(createScanResult('Medium')).severity)
                    .toBe(threat_severity_enum_1.ThreatSeverity.MEDIUM);
                expect(vulnerability_factory_1.VulnerabilityFactory.fromScanResult(createScanResult('Low')).severity)
                    .toBe(threat_severity_enum_1.ThreatSeverity.LOW);
                expect(vulnerability_factory_1.VulnerabilityFactory.fromScanResult(createScanResult('Unknown')).severity)
                    .toBe(threat_severity_enum_1.ThreatSeverity.UNKNOWN);
            });
        });
        describe('category inference', () => {
            it('should infer category from CWE IDs correctly', () => {
                const createCVEWithCWE = (cweIds) => ({
                    cveId: 'CVE-TEST',
                    title: 'Test',
                    description: 'Test',
                    publishedDate: new Date(),
                    lastModifiedDate: new Date(),
                    affectedProducts: [],
                    references: [],
                    cweIds,
                });
                expect(vulnerability_factory_1.VulnerabilityFactory.fromCVEDatabase(createCVEWithCWE(['CWE-79']), mockAffectedAssets).category)
                    .toBe('injection');
                expect(vulnerability_factory_1.VulnerabilityFactory.fromCVEDatabase(createCVEWithCWE(['CWE-89']), mockAffectedAssets).category)
                    .toBe('injection');
                expect(vulnerability_factory_1.VulnerabilityFactory.fromCVEDatabase(createCVEWithCWE(['CWE-287']), mockAffectedAssets).category)
                    .toBe('authentication');
                expect(vulnerability_factory_1.VulnerabilityFactory.fromCVEDatabase(createCVEWithCWE(['CWE-119']), mockAffectedAssets).category)
                    .toBe('memory_corruption');
                expect(vulnerability_factory_1.VulnerabilityFactory.fromCVEDatabase(createCVEWithCWE(['CWE-999']), mockAffectedAssets).category)
                    .toBe('general');
            });
        });
        describe('confidence mapping', () => {
            it('should map scanner confidence to domain confidence correctly', () => {
                const createScanWithConfidence = (confidence) => ({
                    scannerId: 'test',
                    name: 'Test',
                    description: 'Test',
                    severity: 'Medium',
                    asset: { id: 'test', name: 'Test', type: 'test' },
                    scanner: { name: 'Test', version: '1.0', ruleId: 'test', confidence },
                    scanTimestamp: new Date(),
                });
                expect(vulnerability_factory_1.VulnerabilityFactory.fromScanResult(createScanWithConfidence(98)).confidence)
                    .toBe(confidence_level_enum_1.ConfidenceLevel.CONFIRMED);
                expect(vulnerability_factory_1.VulnerabilityFactory.fromScanResult(createScanWithConfidence(88)).confidence)
                    .toBe(confidence_level_enum_1.ConfidenceLevel.VERY_HIGH);
                expect(vulnerability_factory_1.VulnerabilityFactory.fromScanResult(createScanWithConfidence(75)).confidence)
                    .toBe(confidence_level_enum_1.ConfidenceLevel.HIGH);
                expect(vulnerability_factory_1.VulnerabilityFactory.fromScanResult(createScanWithConfidence(60)).confidence)
                    .toBe(confidence_level_enum_1.ConfidenceLevel.MEDIUM);
                expect(vulnerability_factory_1.VulnerabilityFactory.fromScanResult(createScanWithConfidence(40)).confidence)
                    .toBe(confidence_level_enum_1.ConfidenceLevel.LOW);
                expect(vulnerability_factory_1.VulnerabilityFactory.fromScanResult(createScanWithConfidence(20)).confidence)
                    .toBe(confidence_level_enum_1.ConfidenceLevel.VERY_LOW);
            });
        });
    });
    describe('error handling', () => {
        it('should handle invalid CVSS vector gracefully', () => {
            const scanResult = {
                scannerId: 'test',
                name: 'Test',
                description: 'Test',
                severity: 'Medium',
                cvssScore: 6.0,
                cvssVector: 'INVALID_VECTOR',
                asset: { id: 'test', name: 'Test', type: 'test' },
                scanner: { name: 'Test', version: '1.0', ruleId: 'test', confidence: 80 },
                scanTimestamp: new Date(),
            };
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.fromScanResult(scanResult);
            // Should create a basic CVSS score when vector parsing fails
            expect(vulnerability.cvssScores).toHaveLength(1);
            expect(vulnerability.cvssScores[0].baseScore).toBe(6.0);
        });
        it('should handle empty affected assets array', () => {
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.create({
                title: 'Test',
                description: 'Test',
                severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                category: 'test',
                type: 'test',
                affectedAssets: [], // Empty array - should be handled gracefully
                discovery: mockDiscovery,
            });
            expect(vulnerability.affectedAssets).toHaveLength(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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