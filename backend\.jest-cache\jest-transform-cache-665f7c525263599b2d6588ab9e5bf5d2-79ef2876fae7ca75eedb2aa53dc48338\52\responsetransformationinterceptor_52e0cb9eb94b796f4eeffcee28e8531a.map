{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\common\\interceptors\\response-transformation.interceptor.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAMwB;AAExB,8CAA0C;AAE1C,2CAA+C;AAE/C;;;GAGG;AAEI,IAAM,iCAAiC,yCAAvC,MAAM,iCAAiC;IAG5C,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAFxC,WAAM,GAAG,IAAI,eAAM,CAAC,mCAAiC,CAAC,IAAI,CAAC,CAAC;IAEjB,CAAC;IAE7D,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAW,CAAC;QAC7D,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAY,CAAC;QAChE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,EACvE,IAAA,eAAG,EAAC,eAAe,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC,CAC9E,CAAC;IACJ,CAAC;IAEO,iBAAiB,CACvB,IAAS,EACT,OAAgB,EAChB,QAAkB,EAClB,SAAiB;QAEjB,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAE9C,sDAAsD;QACtD,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YAC1D,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;QACrE,CAAC;QAED,0CAA0C;QAC1C,MAAM,gBAAgB,GAAG;YACvB,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAC7C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,cAAc,CAAC;SACvD,CAAC;QAEF,4BAA4B;QAC5B,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,gBAAwB,CAAC,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACnF,CAAC;QAED,wBAAwB;QACxB,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;YACpC,gBAAwB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEO,uBAAuB,CAC7B,IAAS,EACT,OAAgB,EAChB,cAAsB;QAEtB,MAAM,QAAQ,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAE7B,yCAAyC;QACzC,QAAQ,CAAC,QAAQ,GAAG;YAClB,GAAG,QAAQ,CAAC,QAAQ;YACpB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,cAAc,CAAC;SAChD,CAAC;QAEF,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;YACxD,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,cAAc,CAAC,OAAgB,EAAE,cAAsB;QAC7D,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YACpC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;YACrC,cAAc,EAAE,GAAG,cAAc,IAAI;YACrC,QAAQ,EAAE,OAAO,CAAC,WAAW;YAC7B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;YACxC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC;YAClD,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC;YAClD,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC;SACxD,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAAC,OAAgB;QAC7C,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAExD,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,KAAK;gBACR,OAAO,GAAG,QAAQ,yBAAyB,CAAC;YAC9C,KAAK,MAAM;gBACT,OAAO,GAAG,QAAQ,uBAAuB,CAAC;YAC5C,KAAK,KAAK,CAAC;YACX,KAAK,OAAO;gBACV,OAAO,GAAG,QAAQ,uBAAuB,CAAC;YAC5C,KAAK,QAAQ;gBACX,OAAO,GAAG,QAAQ,uBAAuB,CAAC;YAC5C;gBACE,OAAO,kCAAkC,CAAC;QAC9C,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,IAAY;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACjD,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAC9C,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC;YAC1B,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC;YACxB,CAAC,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,aAAa;SACjD,CAAC;QAEF,OAAO,eAAe,CAAC,CAAC;YACtB,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,UAAU,CAAC;IACf,CAAC;IAEO,aAAa,CAAC,OAAgB;QACpC,gCAAgC;QAChC,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QACtD,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QAChC,CAAC;QAED,uBAAuB;QACvB,MAAM,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,eAAe,CAAW,CAAC;QACjE,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,aAAa,CAAC;QACvB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,YAAY,CAAC,OAAgB;QACnC,OAAO,CACL,OAAO,CAAC,OAAO,CAAC,cAAc,CAAW;YACzC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAW;YAC7C,IAAI,CAAC,iBAAiB,EAAE,CACzB,CAAC;IACJ,CAAC;IAEO,iBAAiB;QACvB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;IAEO,qBAAqB,CAAC,cAAsB;QAClD,IAAI,cAAc,GAAG,GAAG;YAAE,OAAO,WAAW,CAAC;QAC7C,IAAI,cAAc,GAAG,GAAG;YAAE,OAAO,MAAM,CAAC;QACxC,IAAI,cAAc,GAAG,IAAI;YAAE,OAAO,YAAY,CAAC;QAC/C,IAAI,cAAc,GAAG,IAAI;YAAE,OAAO,MAAM,CAAC;QACzC,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,iBAAiB,CAAC,IAAS;QACjC,OAAO,CACL,IAAI;YACJ,OAAO,IAAI,KAAK,QAAQ;YACxB,CAAC,IAAI,CAAC,UAAU;gBACf,CAAC,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC;gBACrD,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAC3C,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,IAAS,EAAE,OAAgB;QACvD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;QAED,gCAAgC;QAChC,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,IAAc,CAAC,IAAI,CAAC,CAAC;QACzD,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAe,CAAC,IAAI,EAAE,CAAC;QAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAE5C,OAAO;YACL,IAAI;YACJ,KAAK;YACL,KAAK;YACL,UAAU;YACV,OAAO,EAAE,IAAI,GAAG,UAAU;YAC1B,OAAO,EAAE,IAAI,GAAG,CAAC;YACjB,MAAM,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK;SAC3B,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,OAAgB;QACzC,wDAAwD;QACxD,OAAO,CACL,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC7B,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,MAAM;YAC7C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAU,qBAAqB,EAAE,KAAK,CAAC,CAC9D,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,OAAgB,EAAE,IAAS;QAC/C,MAAM,OAAO,GAAG,GAAG,OAAO,CAAC,QAAQ,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;QAC/D,MAAM,KAAK,GAAG;YACZ,IAAI,EAAE,GAAG,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE;SACzC,CAAC;QAEF,8BAA8B;QAC9B,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACvC,KAAa,CAAC,MAAM,GAAG,GAAG,OAAO,0BAA0B,CAAC;YAC5D,KAAa,CAAC,QAAQ,GAAG,GAAG,OAAO,4BAA4B,CAAC;QACnE,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC7C,KAAa,CAAC,IAAI,GAAG,GAAG,OAAO,8BAA8B,CAAC;YAC9D,KAAa,CAAC,WAAW,GAAG,GAAG,OAAO,qCAAqC,CAAC;QAC/E,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACrC,KAAa,CAAC,YAAY,GAAG,GAAG,OAAO,8BAA8B,CAAC;YACtE,KAAa,CAAC,UAAU,GAAG,GAAG,OAAO,4BAA4B,CAAC;QACrE,CAAC;QAED,qCAAqC;QACrC,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;YAEpD,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;gBACZ,KAAa,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;gBACvE,KAAa,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;YACpE,CAAC;YAED,IAAI,IAAI,GAAG,UAAU,EAAE,CAAC;gBACrB,KAAa,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;gBACvE,KAAa,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,kBAAkB,CAAC,OAAgB,EAAE,IAAY,EAAE,KAAa;QACtE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QAC1F,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC9C,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QAChD,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;IACxB,CAAC;IAEO,WAAW,CAAC,OAAgB,EAAE,IAAS,EAAE,SAAiB;QAChE,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAE9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE;YACtC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,GAAG,EAAE,OAAO,CAAC,WAAW;YACxB,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;YACpC,cAAc,EAAE,GAAG,cAAc,IAAI;YACrC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM;YACzC,QAAQ,EAAE,CAAC,IAAI,CAAC,OAAO;YACvB,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU;YAChC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;YACtB,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,SAAS;YACnC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC;SACnD,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAnQY,8EAAiC;4CAAjC,iCAAiC;IAD7C,IAAA,mBAAU,GAAE;yDAIiC,sBAAa,oBAAb,sBAAa;GAH9C,iCAAiC,CAmQ7C", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\common\\interceptors\\response-transformation.interceptor.ts"], "sourcesContent": ["import {\r\n  Injectable,\r\n  NestInterceptor,\r\n  Execution<PERSON>ontext,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  Logger,\r\n} from '@nestjs/common';\r\nimport { Observable } from 'rxjs';\r\nimport { map, tap } from 'rxjs/operators';\r\nimport { Request, Response } from 'express';\r\nimport { ConfigService } from '@nestjs/config';\r\n\r\n/**\r\n * Response Transformation Interceptor\r\n * Standardizes API responses across all endpoints\r\n */\r\n@Injectable()\r\nexport class ResponseTransformationInterceptor implements NestInterceptor {\r\n  private readonly logger = new Logger(ResponseTransformationInterceptor.name);\r\n\r\n  constructor(private readonly configService: ConfigService) {}\r\n\r\n  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {\r\n    const request = context.switchToHttp().getRequest<Request>();\r\n    const response = context.switchToHttp().getResponse<Response>();\r\n    const startTime = Date.now();\r\n\r\n    return next.handle().pipe(\r\n      map(data => this.transformResponse(data, request, response, startTime)),\r\n      tap(transformedData => this.logResponse(request, transformedData, startTime)),\r\n    );\r\n  }\r\n\r\n  private transformResponse(\r\n    data: any,\r\n    request: Request,\r\n    response: Response,\r\n    startTime: number,\r\n  ): any {\r\n    const processingTime = Date.now() - startTime;\r\n\r\n    // If data is already in standard format, return as-is\r\n    if (data && typeof data === 'object' && 'success' in data) {\r\n      return this.enhanceStandardResponse(data, request, processingTime);\r\n    }\r\n\r\n    // Transform raw data into standard format\r\n    const standardResponse = {\r\n      success: true,\r\n      data: data,\r\n      message: this.generateSuccessMessage(request),\r\n      timestamp: new Date().toISOString(),\r\n      metadata: this.createMetadata(request, processingTime),\r\n    };\r\n\r\n    // Add pagination if present\r\n    if (this.hasPaginationData(data)) {\r\n      (standardResponse as any).pagination = this.extractPaginationInfo(data, request);\r\n    }\r\n\r\n    // Add links for HATEOAS\r\n    if (this.shouldIncludeLinks(request)) {\r\n      (standardResponse as any).links = this.generateLinks(request, data);\r\n    }\r\n\r\n    return standardResponse;\r\n  }\r\n\r\n  private enhanceStandardResponse(\r\n    data: any,\r\n    request: Request,\r\n    processingTime: number,\r\n  ): any {\r\n    const enhanced = { ...data };\r\n\r\n    // Ensure metadata exists and is enhanced\r\n    enhanced.metadata = {\r\n      ...enhanced.metadata,\r\n      ...this.createMetadata(request, processingTime),\r\n    };\r\n\r\n    // Add links if not present\r\n    if (!enhanced.links && this.shouldIncludeLinks(request)) {\r\n      enhanced.links = this.generateLinks(request, enhanced.data);\r\n    }\r\n\r\n    return enhanced;\r\n  }\r\n\r\n  private createMetadata(request: Request, processingTime: number): any {\r\n    return {\r\n      version: this.getApiVersion(request),\r\n      timestamp: new Date().toISOString(),\r\n      requestId: this.getRequestId(request),\r\n      processingTime: `${processingTime}ms`,\r\n      endpoint: request.originalUrl,\r\n      method: request.method,\r\n      userAgent: request.headers['user-agent'],\r\n      clientVersion: request.headers['x-client-version'],\r\n      correlationId: request.headers['x-correlation-id'],\r\n      performance: this.categorizePerformance(processingTime),\r\n    };\r\n  }\r\n\r\n  private generateSuccessMessage(request: Request): string {\r\n    const method = request.method.toLowerCase();\r\n    const resource = this.extractResourceName(request.path);\r\n\r\n    switch (method) {\r\n      case 'get':\r\n        return `${resource} retrieved successfully`;\r\n      case 'post':\r\n        return `${resource} created successfully`;\r\n      case 'put':\r\n      case 'patch':\r\n        return `${resource} updated successfully`;\r\n      case 'delete':\r\n        return `${resource} deleted successfully`;\r\n      default:\r\n        return 'Operation completed successfully';\r\n    }\r\n  }\r\n\r\n  private extractResourceName(path: string): string {\r\n    const segments = path.split('/').filter(Boolean);\r\n    const resourceSegment = segments.find(segment => \r\n      !segment.startsWith('api') && \r\n      !segment.startsWith('v') && \r\n      !segment.match(/^[0-9a-f-]{36}$/i) // Not a UUID\r\n    );\r\n    \r\n    return resourceSegment ? \r\n      resourceSegment.charAt(0).toUpperCase() + resourceSegment.slice(1) : \r\n      'Resource';\r\n  }\r\n\r\n  private getApiVersion(request: Request): string {\r\n    // Extract version from URL path\r\n    const versionMatch = request.path.match(/\\/v(\\d+)\\//);\r\n    if (versionMatch) {\r\n      return versionMatch[1] + '.0';\r\n    }\r\n\r\n    // Check version header\r\n    const headerVersion = request.headers['x-api-version'] as string;\r\n    if (headerVersion) {\r\n      return headerVersion;\r\n    }\r\n\r\n    return '1.0';\r\n  }\r\n\r\n  private getRequestId(request: Request): string {\r\n    return (\r\n      request.headers['x-request-id'] as string ||\r\n      request.headers['x-correlation-id'] as string ||\r\n      this.generateRequestId()\r\n    );\r\n  }\r\n\r\n  private generateRequestId(): string {\r\n    return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  private categorizePerformance(processingTime: number): string {\r\n    if (processingTime < 100) return 'excellent';\r\n    if (processingTime < 300) return 'good';\r\n    if (processingTime < 1000) return 'acceptable';\r\n    if (processingTime < 3000) return 'slow';\r\n    return 'very_slow';\r\n  }\r\n\r\n  private hasPaginationData(data: any): boolean {\r\n    return (\r\n      data &&\r\n      typeof data === 'object' &&\r\n      (data.pagination || \r\n       (data.total !== undefined && data.page !== undefined) ||\r\n       (Array.isArray(data) && data.length > 0))\r\n    );\r\n  }\r\n\r\n  private extractPaginationInfo(data: any, request: Request): any {\r\n    if (data.pagination) {\r\n      return data.pagination;\r\n    }\r\n\r\n    // Extract from query parameters\r\n    const page = parseInt(request.query.page as string) || 1;\r\n    const limit = parseInt(request.query.limit as string) || 10;\r\n    const total = data.total || (Array.isArray(data) ? data.length : 0);\r\n    const totalPages = Math.ceil(total / limit);\r\n\r\n    return {\r\n      page,\r\n      limit,\r\n      total,\r\n      totalPages,\r\n      hasNext: page < totalPages,\r\n      hasPrev: page > 1,\r\n      offset: (page - 1) * limit,\r\n    };\r\n  }\r\n\r\n  private shouldIncludeLinks(request: Request): boolean {\r\n    // Include links for API v2 or when explicitly requested\r\n    return (\r\n      request.path.includes('/v2/') ||\r\n      request.headers['x-include-links'] === 'true' ||\r\n      this.configService.get<boolean>('API_INCLUDE_HATEOAS', false)\r\n    );\r\n  }\r\n\r\n  private generateLinks(request: Request, data: any): any {\r\n    const baseUrl = `${request.protocol}://${request.get('host')}`;\r\n    const links = {\r\n      self: `${baseUrl}${request.originalUrl}`,\r\n    };\r\n\r\n    // Add resource-specific links\r\n    if (request.path.includes('/analytics')) {\r\n      (links as any).export = `${baseUrl}/api/v1/analytics/export`;\r\n      (links as any).insights = `${baseUrl}/api/v1/analytics/insights`;\r\n    }\r\n\r\n    if (request.path.includes('/vulnerabilities')) {\r\n      (links as any).scan = `${baseUrl}/api/v1/vulnerabilities/scan`;\r\n      (links as any).remediation = `${baseUrl}/api/v1/vulnerabilities/remediation`;\r\n    }\r\n\r\n    if (request.path.includes('/threats')) {\r\n      (links as any).intelligence = `${baseUrl}/api/v1/threats/intelligence`;\r\n      (links as any).indicators = `${baseUrl}/api/v1/threats/indicators`;\r\n    }\r\n\r\n    // Add pagination links if applicable\r\n    if (data && data.pagination) {\r\n      const { page, limit, totalPages } = data.pagination;\r\n      \r\n      if (page > 1) {\r\n        (links as any).prev = this.buildPaginationUrl(request, page - 1, limit);\r\n        (links as any).first = this.buildPaginationUrl(request, 1, limit);\r\n      }\r\n      \r\n      if (page < totalPages) {\r\n        (links as any).next = this.buildPaginationUrl(request, page + 1, limit);\r\n        (links as any).last = this.buildPaginationUrl(request, totalPages, limit);\r\n      }\r\n    }\r\n\r\n    return links;\r\n  }\r\n\r\n  private buildPaginationUrl(request: Request, page: number, limit: number): string {\r\n    const url = new URL(`${request.protocol}://${request.get('host')}${request.originalUrl}`);\r\n    url.searchParams.set('page', page.toString());\r\n    url.searchParams.set('limit', limit.toString());\r\n    return url.toString();\r\n  }\r\n\r\n  private logResponse(request: Request, data: any, startTime: number): void {\r\n    const processingTime = Date.now() - startTime;\r\n    \r\n    this.logger.log('Response transformed', {\r\n      method: request.method,\r\n      url: request.originalUrl,\r\n      statusCode: data.success ? 200 : 400,\r\n      processingTime: `${processingTime}ms`,\r\n      responseSize: JSON.stringify(data).length,\r\n      hasError: !data.success,\r\n      hasPagination: !!data.pagination,\r\n      hasLinks: !!data.links,\r\n      requestId: data.metadata?.requestId,\r\n      correlationId: request.headers['x-correlation-id'],\r\n    });\r\n  }\r\n}"], "version": 3}