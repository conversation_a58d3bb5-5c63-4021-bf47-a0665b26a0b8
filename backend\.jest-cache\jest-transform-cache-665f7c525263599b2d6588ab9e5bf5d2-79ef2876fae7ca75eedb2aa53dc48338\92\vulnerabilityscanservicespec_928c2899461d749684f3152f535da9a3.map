{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability-scan.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,8EAAyE;AACzE,kGAAuF;AACvF,4FAAkF;AAClF,yFAAqF;AACrF,6FAAyF;AACzF,kHAA6G;AAE7G,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;IACxC,IAAI,OAAiC,CAAC;IACtC,IAAI,cAA0D,CAAC;IAC/D,IAAI,eAA+C,CAAC;IACpD,IAAI,aAAyC,CAAC;IAC9C,IAAI,YAAuC,CAAC;IAC5C,IAAI,cAAwD,CAAC;IAE7D,MAAM,QAAQ,GAAG;QACf,EAAE,EAAE,sCAAsC;QAC1C,IAAI,EAAE,WAAW;QACjB,WAAW,EAAE,uBAAuB;QACpC,QAAQ,EAAE,SAAS;QACnB,WAAW,EAAE,QAAQ;QACrB,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,gBAAgB;aACxB;SACF;QACD,UAAU,EAAE;YACV,SAAS,EAAE,QAAQ;YACnB,cAAc,EAAE,KAAK;SACtB;QACD,QAAQ,EAAE,QAAQ;QAClB,WAAW,EAAE,UAAU;QACvB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;KAC1B,CAAC;IAEF,MAAM,SAAS,GAAG;QAChB,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE,eAAe;KAC3B,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,cAAc,GAAG;YACrB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;YAClB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;YACpB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;YAChB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;gBACjC,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAC7C,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACjC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACnC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACtC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;gBAC1B,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;aACnB,CAAC,CAAC;SACJ,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,qDAAwB;gBACxB;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,6CAAiB,CAAC;oBAC9C,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,oBAAK,CAAC;oBAClC,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE;wBACR,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;wBAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;wBACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;wBACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;qBACjB;iBACF;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE;wBACR,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;qBACzB;iBACF;gBACD;oBACE,OAAO,EAAE,2DAA2B;oBACpC,QAAQ,EAAE;wBACR,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;wBAC1B,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;wBAC3B,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;wBACvB,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;wBAC9B,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;wBAC/B,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;qBACtB;iBACF;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAA2B,qDAAwB,CAAC,CAAC;QACzE,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,6CAAiB,CAAC,CAAC,CAAC;QACnE,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,oBAAK,CAAC,CAAC,CAAC;QACxD,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,8BAAa,CAAC,CAAC;QAC1C,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,4BAAY,CAAC,CAAC;QACxC,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,2DAA2B,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAC3B,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,MAAM,QAAQ,GAAG;gBACf,IAAI,EAAE,UAAU;gBAChB,WAAW,EAAE,sBAAsB;gBACnC,QAAQ,EAAE,SAAkB;gBAC5B,WAAW,EAAE,QAAiB;gBAC9B,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,SAAkB;wBACxB,KAAK,EAAE,gBAAgB;qBACxB;iBACF;gBACD,UAAU,EAAE;oBACV,SAAS,EAAE,QAAQ;oBACnB,cAAc,EAAE,KAAK;iBACtB;aACF,CAAC;YAEF,cAAc,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAChD,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEhD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAE9D,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAChD,MAAM,CAAC,gBAAgB,CAAC;gBACtB,GAAG,QAAQ;gBACX,MAAM,EAAE,SAAS;gBACjB,WAAW,EAAE,UAAU;aACxB,CAAC,CACH,CAAC;YACF,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,QAAQ,EACR,oBAAoB,EACpB,QAAQ,CAAC,EAAE,EACX,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,QAAQ,GAAG;gBACf,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,SAAkB;gBAC5B,WAAW,EAAE,QAAiB;gBAC9B,OAAO,EAAE,EAAE;gBACX,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,CAAC,WAAW,CAAC;aACxB,CAAC;YAEF,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACpD,cAAc,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAChD,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEhD,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAE/C,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAChD,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;aAClC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,sCAAsC,CAAC,CAAC;YAEpF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACjC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAClD,KAAK,EAAE,EAAE,EAAE,EAAE,sCAAsC,EAAE;gBACrD,SAAS,EAAE,CAAC,eAAe,CAAC;aAC7B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE/C,MAAM,MAAM,CACV,OAAO,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAC1C,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,gBAAgB,GAAG,cAAc,CAAC,kBAAkB,EAAE,CAAC;YAC7D,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAEpE,MAAM,cAAc,GAAG;gBACrB,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,SAAS,EAAE,CAAC,SAAS,CAAC;gBACtB,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;gBAChC,YAAY,EAAE,CAAC,QAAQ,CAAC;aACzB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,kCAAkC,EAClC,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC,EAAE,CAC3B,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,gBAAgB,GAAG,cAAc,CAAC,kBAAkB,EAAE,CAAC;YAC7D,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAE5D,MAAM,cAAc,GAAG;gBACrB,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACpC,aAAa,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;aACtC,CAAC;YAEF,MAAM,OAAO,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;YAE1C,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,iCAAiC,EACjC,EAAE,YAAY,EAAE,cAAc,CAAC,YAAY,EAAE,CAC9C,CAAC;YACF,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,kCAAkC,EAClC,EAAE,aAAa,EAAE,cAAc,CAAC,aAAa,EAAE,CAChD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,aAAa,GAAG;gBACpB,WAAW,EAAE,QAAQ;gBACrB,cAAc,EAAE,YAAY;gBAC5B,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACnD,cAAc,CAAC,eAAe,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAChE,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACpC,GAAG,QAAQ;gBACX,MAAM,EAAE,SAAS;gBACjB,cAAc,EAAE,YAAY;aAC7B,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,sCAAsC,EAAE,UAAU,CAAC,CAAC;YAE3F,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,oBAAoB,CACzD,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,OAAO,EAAE,QAAQ,CAAC,OAAO;aAC1B,CAAC,CACH,CAAC;YACF,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAC1D,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,OAAO,EACP,oBAAoB,EACpB,QAAQ,CAAC,EAAE,EACX,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE/C,MAAM,MAAM,CACV,OAAO,CAAC,SAAS,CAAC,iBAAiB,EAAE,UAAU,CAAC,CACjD,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,eAAe,GAAG,EAAE,GAAG,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;YAC3D,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAE1D,MAAM,MAAM,CACV,OAAO,CAAC,SAAS,CAAC,sCAAsC,EAAE,UAAU,CAAC,CACtE,CAAC,OAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,eAAe,GAAG;gBACtB,GAAG,QAAQ;gBACX,MAAM,EAAE,SAAS;gBACjB,cAAc,EAAE,YAAY;aAC7B,CAAC;YAEF,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC1D,cAAc,CAAC,UAAU,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACvD,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACpC,GAAG,eAAe;gBAClB,MAAM,EAAE,WAAW;aACpB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CACrC,sCAAsC,EACtC,6BAA6B,EAC7B,UAAU,CACX,CAAC;YAEF,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAC/E,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,6BAA6B,CAAC,CAAC;YACnF,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,QAAQ,EACR,oBAAoB,EACpB,eAAe,CAAC,EAAE,EAClB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,iBAAiB,GAAG,EAAE,GAAG,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;YAC/D,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAE5D,MAAM,MAAM,CACV,OAAO,CAAC,UAAU,CAAC,sCAAsC,EAAE,QAAQ,EAAE,UAAU,CAAC,CACjF,CAAC,OAAO,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,eAAe,GAAG,EAAE,GAAG,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;YAC3D,MAAM,YAAY,GAAG;gBACnB,eAAe,EAAE,EAAE;gBACnB,YAAY,EAAE,WAAW;gBACzB,oBAAoB,EAAE,CAAC;aACxB,CAAC;YAEF,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC1D,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAEvD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAC7C,sCAAsC,EACtC,YAAY,CACb,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAC1E,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE/C,MAAM,MAAM,CACV,OAAO,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAClD,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,eAAe,GAAG,EAAE,GAAG,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;YAC3D,MAAM,OAAO,GAAG;gBACd,OAAO,EAAE;oBACP,oBAAoB,EAAE,EAAE;oBACxB,aAAa,EAAE,CAAC;oBAChB,SAAS,EAAE,CAAC;oBACZ,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,CAAC;iBACZ;gBACD,eAAe,EAAE,EAAE;aACpB,CAAC;YAEF,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC1D,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACpC,GAAG,eAAe;gBAClB,MAAM,EAAE,WAAW;aACpB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,CACvC,sCAAsC,EACtC,OAAO,EACP,UAAU,CACX,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAC/D,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,UAAU,EACV,oBAAoB,EACpB,eAAe,CAAC,EAAE,EAClB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,iBAAiB,GAAG,EAAE,GAAG,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;YAC/D,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAC5D,cAAc,CAAC,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAE3D,MAAM,OAAO,CAAC,UAAU,CAAC,sCAAsC,EAAE,UAAU,CAAC,CAAC;YAE7E,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;YACtE,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,QAAQ,EACR,oBAAoB,EACpB,iBAAiB,CAAC,EAAE,EACpB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,MAAM,eAAe,GAAG,EAAE,GAAG,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;YAC3D,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAE1D,MAAM,MAAM,CACV,OAAO,CAAC,UAAU,CAAC,sCAAsC,EAAE,UAAU,CAAC,CACvE,CAAC,OAAO,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability-scan.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { VulnerabilityScanService } from '../vulnerability-scan.service';\r\nimport { VulnerabilityScan } from '../../../domain/entities/vulnerability-scan.entity';\r\nimport { Asset } from '../../../../asset-management/domain/entities/asset.entity';\r\nimport { LoggerService } from '../../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../../infrastructure/logging/audit/audit.service';\r\nimport { VulnerabilityScannerService } from '../../../infrastructure/services/vulnerability-scanner.service';\r\n\r\ndescribe('VulnerabilityScanService', () => {\r\n  let service: VulnerabilityScanService;\r\n  let scanRepository: jest.Mocked<Repository<VulnerabilityScan>>;\r\n  let assetRepository: jest.Mocked<Repository<Asset>>;\r\n  let loggerService: jest.Mocked<LoggerService>;\r\n  let auditService: jest.Mocked<AuditService>;\r\n  let scannerService: jest.Mocked<VulnerabilityScannerService>;\r\n\r\n  const mockScan = {\r\n    id: '123e4567-e89b-12d3-a456-426614174000',\r\n    name: 'Test Scan',\r\n    description: 'Test scan description',\r\n    scanType: 'network',\r\n    scannerType: 'nessus',\r\n    status: 'pending',\r\n    targets: [\r\n      {\r\n        type: 'network',\r\n        value: '***********/24',\r\n      },\r\n    ],\r\n    scanConfig: {\r\n      intensity: 'normal',\r\n      useCredentials: false,\r\n    },\r\n    priority: 'medium',\r\n    initiatedBy: 'user-123',\r\n    getSummary: jest.fn(),\r\n    exportForReporting: jest.fn(),\r\n    start: jest.fn(),\r\n    complete: jest.fn(),\r\n    fail: jest.fn(),\r\n    cancel: jest.fn(),\r\n    updateProgress: jest.fn(),\r\n  };\r\n\r\n  const mockAsset = {\r\n    id: 'asset-123',\r\n    name: 'Test Asset',\r\n    type: 'server',\r\n    ipAddress: '*************',\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const mockRepository = {\r\n      create: jest.fn(),\r\n      save: jest.fn(),\r\n      find: jest.fn(),\r\n      findOne: jest.fn(),\r\n      findOneBy: jest.fn(),\r\n      count: jest.fn(),\r\n      remove: jest.fn(),\r\n      createQueryBuilder: jest.fn(() => ({\r\n        leftJoinAndSelect: jest.fn().mockReturnThis(),\r\n        where: jest.fn().mockReturnThis(),\r\n        andWhere: jest.fn().mockReturnThis(),\r\n        orderBy: jest.fn().mockReturnThis(),\r\n        addOrderBy: jest.fn().mockReturnThis(),\r\n        skip: jest.fn().mockReturnThis(),\r\n        take: jest.fn().mockReturnThis(),\r\n        getManyAndCount: jest.fn(),\r\n        getMany: jest.fn(),\r\n      })),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        VulnerabilityScanService,\r\n        {\r\n          provide: getRepositoryToken(VulnerabilityScan),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(Asset),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: {\r\n            debug: jest.fn(),\r\n            log: jest.fn(),\r\n            warn: jest.fn(),\r\n            error: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: {\r\n            logUserAction: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: VulnerabilityScannerService,\r\n          useValue: {\r\n            startNessusScan: jest.fn(),\r\n            startOpenVASScan: jest.fn(),\r\n            startZAPScan: jest.fn(),\r\n            getNessusScanStatus: jest.fn(),\r\n            getNessusScanResults: jest.fn(),\r\n            cancelScan: jest.fn(),\r\n          },\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<VulnerabilityScanService>(VulnerabilityScanService);\r\n    scanRepository = module.get(getRepositoryToken(VulnerabilityScan));\r\n    assetRepository = module.get(getRepositoryToken(Asset));\r\n    loggerService = module.get(LoggerService);\r\n    auditService = module.get(AuditService);\r\n    scannerService = module.get(VulnerabilityScannerService);\r\n  });\r\n\r\n  it('should be defined', () => {\r\n    expect(service).toBeDefined();\r\n  });\r\n\r\n  describe('createScan', () => {\r\n    it('should create a new scan', async () => {\r\n      const scanData = {\r\n        name: 'New Scan',\r\n        description: 'New scan description',\r\n        scanType: 'network' as const,\r\n        scannerType: 'nessus' as const,\r\n        targets: [\r\n          {\r\n            type: 'network' as const,\r\n            value: '***********/24',\r\n          },\r\n        ],\r\n        scanConfig: {\r\n          intensity: 'normal',\r\n          useCredentials: false,\r\n        },\r\n      };\r\n\r\n      scanRepository.create.mockReturnValue(mockScan);\r\n      scanRepository.save.mockResolvedValue(mockScan);\r\n\r\n      const result = await service.createScan(scanData, 'user-123');\r\n\r\n      expect(scanRepository.create).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          ...scanData,\r\n          status: 'pending',\r\n          initiatedBy: 'user-123',\r\n        })\r\n      );\r\n      expect(scanRepository.save).toHaveBeenCalledWith(mockScan);\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'create',\r\n        'vulnerability_scan',\r\n        mockScan.id,\r\n        expect.any(Object)\r\n      );\r\n    });\r\n\r\n    it('should resolve asset IDs when provided', async () => {\r\n      const scanData = {\r\n        name: 'Asset Scan',\r\n        scanType: 'network' as const,\r\n        scannerType: 'nessus' as const,\r\n        targets: [],\r\n        scanConfig: {},\r\n        assetIds: ['asset-123'],\r\n      };\r\n\r\n      assetRepository.find.mockResolvedValue([mockAsset]);\r\n      scanRepository.create.mockReturnValue(mockScan);\r\n      scanRepository.save.mockResolvedValue(mockScan);\r\n\r\n      await service.createScan(scanData, 'user-123');\r\n\r\n      expect(assetRepository.find).toHaveBeenCalledWith({\r\n        where: { id: expect.any(Object) },\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('getScanDetails', () => {\r\n    it('should return scan details', async () => {\r\n      scanRepository.findOne.mockResolvedValue(mockScan);\r\n\r\n      const result = await service.getScanDetails('123e4567-e89b-12d3-a456-426614174000');\r\n\r\n      expect(result).toEqual(mockScan);\r\n      expect(scanRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: '123e4567-e89b-12d3-a456-426614174000' },\r\n        relations: ['scannedAssets'],\r\n      });\r\n    });\r\n\r\n    it('should throw NotFoundException when scan not found', async () => {\r\n      scanRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.getScanDetails('non-existent-id')\r\n      ).rejects.toThrow('Scan not found');\r\n    });\r\n  });\r\n\r\n  describe('searchScans', () => {\r\n    it('should search scans with filters', async () => {\r\n      const mockQueryBuilder = scanRepository.createQueryBuilder();\r\n      mockQueryBuilder.getManyAndCount.mockResolvedValue([[mockScan], 1]);\r\n\r\n      const searchCriteria = {\r\n        page: 1,\r\n        limit: 10,\r\n        scanTypes: ['network'],\r\n        statuses: ['pending', 'running'],\r\n        scannerTypes: ['nessus'],\r\n      };\r\n\r\n      const result = await service.searchScans(searchCriteria);\r\n\r\n      expect(result).toHaveProperty('scans');\r\n      expect(result).toHaveProperty('total');\r\n      expect(result).toHaveProperty('page');\r\n      expect(result).toHaveProperty('totalPages');\r\n      expect(result.scans).toHaveLength(1);\r\n      expect(result.total).toBe(1);\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        'scan.scanType IN (:...scanTypes)',\r\n        { scanTypes: ['network'] }\r\n      );\r\n    });\r\n\r\n    it('should handle date range filters', async () => {\r\n      const mockQueryBuilder = scanRepository.createQueryBuilder();\r\n      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);\r\n\r\n      const searchCriteria = {\r\n        page: 1,\r\n        limit: 10,\r\n        createdAfter: new Date('2023-01-01'),\r\n        createdBefore: new Date('2023-12-31'),\r\n      };\r\n\r\n      await service.searchScans(searchCriteria);\r\n\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        'scan.createdAt >= :createdAfter',\r\n        { createdAfter: searchCriteria.createdAfter }\r\n      );\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        'scan.createdAt <= :createdBefore',\r\n        { createdBefore: searchCriteria.createdBefore }\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('startScan', () => {\r\n    it('should start a Nessus scan', async () => {\r\n      const scannerResult = {\r\n        scannerType: 'nessus',\r\n        externalScanId: 'nessus-123',\r\n        status: 'running',\r\n        startedAt: new Date(),\r\n      };\r\n\r\n      scanRepository.findOne.mockResolvedValue(mockScan);\r\n      scannerService.startNessusScan.mockResolvedValue(scannerResult);\r\n      scanRepository.save.mockResolvedValue({\r\n        ...mockScan,\r\n        status: 'running',\r\n        externalScanId: 'nessus-123',\r\n      });\r\n\r\n      const result = await service.startScan('123e4567-e89b-12d3-a456-426614174000', 'user-123');\r\n\r\n      expect(scannerService.startNessusScan).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          name: mockScan.name,\r\n          targets: mockScan.targets,\r\n        })\r\n      );\r\n      expect(mockScan.start).toHaveBeenCalledWith('nessus-123');\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'start',\r\n        'vulnerability_scan',\r\n        mockScan.id,\r\n        expect.any(Object)\r\n      );\r\n    });\r\n\r\n    it('should throw error when scan not found', async () => {\r\n      scanRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.startScan('non-existent-id', 'user-123')\r\n      ).rejects.toThrow('Scan not found');\r\n    });\r\n\r\n    it('should throw error when scan cannot be started', async () => {\r\n      const runningMockScan = { ...mockScan, status: 'running' };\r\n      scanRepository.findOne.mockResolvedValue(runningMockScan);\r\n\r\n      await expect(\r\n        service.startScan('123e4567-e89b-12d3-a456-426614174000', 'user-123')\r\n      ).rejects.toThrow('Scan cannot be started');\r\n    });\r\n  });\r\n\r\n  describe('cancelScan', () => {\r\n    it('should cancel a running scan', async () => {\r\n      const runningMockScan = {\r\n        ...mockScan,\r\n        status: 'running',\r\n        externalScanId: 'nessus-123',\r\n      };\r\n\r\n      scanRepository.findOne.mockResolvedValue(runningMockScan);\r\n      scannerService.cancelScan.mockResolvedValue(undefined);\r\n      scanRepository.save.mockResolvedValue({\r\n        ...runningMockScan,\r\n        status: 'cancelled',\r\n      });\r\n\r\n      const result = await service.cancelScan(\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        'User requested cancellation',\r\n        'user-123'\r\n      );\r\n\r\n      expect(scannerService.cancelScan).toHaveBeenCalledWith('nessus', 'nessus-123');\r\n      expect(runningMockScan.cancel).toHaveBeenCalledWith('User requested cancellation');\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'cancel',\r\n        'vulnerability_scan',\r\n        runningMockScan.id,\r\n        expect.any(Object)\r\n      );\r\n    });\r\n\r\n    it('should throw error when scan cannot be cancelled', async () => {\r\n      const completedMockScan = { ...mockScan, status: 'completed' };\r\n      scanRepository.findOne.mockResolvedValue(completedMockScan);\r\n\r\n      await expect(\r\n        service.cancelScan('123e4567-e89b-12d3-a456-426614174000', 'reason', 'user-123')\r\n      ).rejects.toThrow('Scan cannot be cancelled');\r\n    });\r\n  });\r\n\r\n  describe('updateScanProgress', () => {\r\n    it('should update scan progress', async () => {\r\n      const runningMockScan = { ...mockScan, status: 'running' };\r\n      const progressData = {\r\n        overallProgress: 50,\r\n        currentPhase: 'port_scan',\r\n        vulnerabilitiesFound: 5,\r\n      };\r\n\r\n      scanRepository.findOne.mockResolvedValue(runningMockScan);\r\n      scanRepository.save.mockResolvedValue(runningMockScan);\r\n\r\n      const result = await service.updateScanProgress(\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        progressData\r\n      );\r\n\r\n      expect(runningMockScan.updateProgress).toHaveBeenCalledWith(progressData);\r\n      expect(scanRepository.save).toHaveBeenCalledWith(runningMockScan);\r\n    });\r\n\r\n    it('should throw error when scan not found', async () => {\r\n      scanRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.updateScanProgress('non-existent-id', {})\r\n      ).rejects.toThrow('Scan not found');\r\n    });\r\n  });\r\n\r\n  describe('completeScan', () => {\r\n    it('should complete a scan with results', async () => {\r\n      const runningMockScan = { ...mockScan, status: 'running' };\r\n      const results = {\r\n        summary: {\r\n          totalVulnerabilities: 10,\r\n          criticalCount: 2,\r\n          highCount: 3,\r\n          mediumCount: 3,\r\n          lowCount: 2,\r\n        },\r\n        vulnerabilities: [],\r\n      };\r\n\r\n      scanRepository.findOne.mockResolvedValue(runningMockScan);\r\n      scanRepository.save.mockResolvedValue({\r\n        ...runningMockScan,\r\n        status: 'completed',\r\n      });\r\n\r\n      const result = await service.completeScan(\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        results,\r\n        'user-123'\r\n      );\r\n\r\n      expect(runningMockScan.complete).toHaveBeenCalledWith(results);\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'complete',\r\n        'vulnerability_scan',\r\n        runningMockScan.id,\r\n        expect.any(Object)\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('deleteScan', () => {\r\n    it('should delete a scan that is not running', async () => {\r\n      const completedMockScan = { ...mockScan, status: 'completed' };\r\n      scanRepository.findOne.mockResolvedValue(completedMockScan);\r\n      scanRepository.remove.mockResolvedValue(completedMockScan);\r\n\r\n      await service.deleteScan('123e4567-e89b-12d3-a456-426614174000', 'user-123');\r\n\r\n      expect(scanRepository.remove).toHaveBeenCalledWith(completedMockScan);\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'delete',\r\n        'vulnerability_scan',\r\n        completedMockScan.id,\r\n        expect.any(Object)\r\n      );\r\n    });\r\n\r\n    it('should throw error when trying to delete a running scan', async () => {\r\n      const runningMockScan = { ...mockScan, status: 'running' };\r\n      scanRepository.findOne.mockResolvedValue(runningMockScan);\r\n\r\n      await expect(\r\n        service.deleteScan('123e4567-e89b-12d3-a456-426614174000', 'user-123')\r\n      ).rejects.toThrow('Cannot delete running scan');\r\n    });\r\n  });\r\n});\r\n"], "version": 3}