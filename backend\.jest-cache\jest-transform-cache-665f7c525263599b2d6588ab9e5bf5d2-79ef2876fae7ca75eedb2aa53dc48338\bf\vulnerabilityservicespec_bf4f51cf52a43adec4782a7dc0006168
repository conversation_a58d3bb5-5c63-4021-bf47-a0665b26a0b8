5d751138c9537ec5222283b458bc5cd0
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const common_1 = require("@nestjs/common");
const vulnerability_service_1 = require("./vulnerability.service");
const vulnerability_entity_1 = require("../../domain/entities/vulnerability.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
describe('VulnerabilityService', () => {
    let service;
    let repository;
    let loggerService;
    let auditService;
    const mockRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findOne: jest.fn(),
        find: jest.fn(),
        remove: jest.fn(),
        count: jest.fn(),
        createQueryBuilder: jest.fn(),
    };
    const mockLoggerService = {
        debug: jest.fn(),
        log: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
    };
    const mockAuditService = {
        logUserAction: jest.fn(),
    };
    const mockVulnerability = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        title: 'Test Vulnerability',
        description: 'Test vulnerability description',
        severity: 'high',
        status: 'open',
        priority: 'high',
        source: 'manual',
        discoveredAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                vulnerability_service_1.VulnerabilityService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_entity_1.Vulnerability),
                    useValue: mockRepository,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: mockLoggerService,
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: mockAuditService,
                },
            ],
        }).compile();
        service = module.get(vulnerability_service_1.VulnerabilityService);
        repository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_entity_1.Vulnerability));
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('create', () => {
        it('should create a vulnerability successfully', async () => {
            const createData = {
                title: 'Test Vulnerability',
                description: 'Test description',
                severity: 'high',
            };
            const userId = 'user-123';
            mockRepository.create.mockReturnValue(mockVulnerability);
            mockRepository.save.mockResolvedValue(mockVulnerability);
            const result = await service.create(createData, userId);
            expect(mockRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                title: createData.title,
                description: createData.description,
                severity: createData.severity,
            }));
            expect(mockRepository.save).toHaveBeenCalledWith(mockVulnerability);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'create', 'vulnerability', mockVulnerability.id, expect.any(Object));
            expect(result).toEqual(mockVulnerability);
        });
        it('should throw BadRequestException when title is missing', async () => {
            const createData = {
                description: 'Test description',
            };
            const userId = 'user-123';
            await expect(service.create(createData, userId)).rejects.toThrow(common_1.BadRequestException);
        });
        it('should throw BadRequestException when description is missing', async () => {
            const createData = {
                title: 'Test Vulnerability',
            };
            const userId = 'user-123';
            await expect(service.create(createData, userId)).rejects.toThrow(common_1.BadRequestException);
        });
    });
    describe('findById', () => {
        it('should return vulnerability when found', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            mockRepository.findOne.mockResolvedValue(mockVulnerability);
            const result = await service.findById(id);
            expect(mockRepository.findOne).toHaveBeenCalledWith({
                where: { id },
                relations: ['assessments'],
            });
            expect(result).toEqual(mockVulnerability);
        });
        it('should return null when vulnerability not found', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            mockRepository.findOne.mockResolvedValue(null);
            const result = await service.findById(id);
            expect(result).toBeNull();
            expect(mockLoggerService.warn).toHaveBeenCalledWith('Vulnerability not found', { id });
        });
    });
    describe('findByCveId', () => {
        it('should return vulnerability when found by CVE ID', async () => {
            const cveId = 'CVE-2023-1234';
            mockRepository.findOne.mockResolvedValue(mockVulnerability);
            const result = await service.findByCveId(cveId);
            expect(mockRepository.findOne).toHaveBeenCalledWith({
                where: { cveId },
                relations: ['assessments'],
            });
            expect(result).toEqual(mockVulnerability);
        });
    });
    describe('update', () => {
        it('should update vulnerability successfully', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            const updateData = { severity: 'critical' };
            const userId = 'user-123';
            jest.spyOn(service, 'findById').mockResolvedValue(mockVulnerability);
            mockRepository.save.mockResolvedValue({ ...mockVulnerability, ...updateData });
            const result = await service.update(id, updateData, userId);
            expect(service.findById).toHaveBeenCalledWith(id);
            expect(mockRepository.save).toHaveBeenCalled();
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'update', 'vulnerability', id, expect.any(Object));
            expect(result).toEqual(expect.objectContaining(updateData));
        });
        it('should throw NotFoundException when vulnerability not found', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            const updateData = { severity: 'critical' };
            const userId = 'user-123';
            jest.spyOn(service, 'findById').mockResolvedValue(null);
            await expect(service.update(id, updateData, userId)).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe('delete', () => {
        it('should delete vulnerability successfully', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            const userId = 'user-123';
            jest.spyOn(service, 'findById').mockResolvedValue(mockVulnerability);
            mockRepository.remove.mockResolvedValue(mockVulnerability);
            await service.delete(id, userId);
            expect(service.findById).toHaveBeenCalledWith(id);
            expect(mockRepository.remove).toHaveBeenCalledWith(mockVulnerability);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'delete', 'vulnerability', id, expect.any(Object));
        });
        it('should throw NotFoundException when vulnerability not found', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            const userId = 'user-123';
            jest.spyOn(service, 'findById').mockResolvedValue(null);
            await expect(service.delete(id, userId)).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe('getStatistics', () => {
        it('should return vulnerability statistics', async () => {
            mockRepository.count
                .mockResolvedValueOnce(100) // total
                .mockResolvedValueOnce(10) // critical
                .mockResolvedValueOnce(20) // high
                .mockResolvedValueOnce(30) // medium
                .mockResolvedValueOnce(40) // low
                .mockResolvedValueOnce(50) // open
                .mockResolvedValueOnce(25) // in_progress
                .mockResolvedValueOnce(25); // resolved
            const result = await service.getStatistics();
            expect(result).toEqual({
                total: 100,
                bySeverity: {
                    critical: 10,
                    high: 20,
                    medium: 30,
                    low: 40,
                },
                byStatus: {
                    open: 50,
                    inProgress: 25,
                    resolved: 25,
                },
                timestamp: expect.any(String),
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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