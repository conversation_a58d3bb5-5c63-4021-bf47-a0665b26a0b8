{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\__tests__\\is-cve-id.validator.spec.ts", "mappings": ";;;;;;;;;;;AAAA,qDAA2C;AAC3C,yDAAiD;AACjD,gEAKgC;AAEhC,MAAM,UAAU;CAMf;AAJC;IADC,IAAA,6BAAO,GAAE;;yCACI;AAGd;IADC,IAAA,kCAAY,GAAE;;0CACE;AAGnB,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,IAAI,UAA6B,CAAC;IAElC,UAAU,CAAC,GAAG,EAAE;QACd,UAAU,GAAG,IAAI,uCAAiB,EAAE,CAAC;IACvC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;YACxB,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,WAAW,GAAG;oBAClB,eAAe;oBACf,gBAAgB;oBAChB,iBAAiB;oBACjB,eAAe;oBACf,eAAe;iBAChB,CAAC;gBAEF,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBAC1B,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3D,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,aAAa,GAAG;oBACpB,aAAa,EAAE,sBAAsB;oBACrC,cAAc,EAAE,6BAA6B;oBAC7C,eAAe,EAAE,YAAY;oBAC7B,WAAW,EAAE,mBAAmB;oBAChC,eAAe,EAAE,uBAAuB;oBACxC,WAAW,EAAE,qBAAqB;oBAClC,eAAe,EAAE,iBAAiB;oBAClC,eAAe,EAAE,yBAAyB;oBAC1C,EAAE,EAAE,eAAe;oBACnB,IAAI,EAAE,aAAa;oBACnB,SAAS,EAAE,kBAAkB;oBAC7B,GAAG,EAAE,2BAA2B;iBACjC,CAAC;gBAEF,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBAC5B,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5D,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;gBAE7C,cAAc;gBACd,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,EAAE,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnE,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,WAAW,OAAO,EAAE,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC7E,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,WAAW,GAAG,CAAC,OAAO,EAAE,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEjF,gBAAgB;gBAChB,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,EAAE,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpE,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,WAAW,GAAG,CAAC,OAAO,EAAE,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,OAAO,GAAG,UAAU,CAAC,cAAc,CAAC;oBACxC,QAAQ,EAAE,OAAO;iBACX,CAAC,CAAC;gBAEV,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACnC,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;YAC3B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,CAAC,gCAAU,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3D,MAAM,CAAC,gCAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,CAAC,gCAAU,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACrD,MAAM,CAAC,gCAAU,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC3D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;gBAC1D,MAAM,CAAC,gCAAU,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrE,MAAM,CAAC,gCAAU,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,CAAC,gCAAU,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAC/D,MAAM,CAAC,gCAAU,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACrE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;YAC7B,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,CAAC,gCAAU,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBACnE,MAAM,CAAC,gCAAU,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;gBAC1C,MAAM,CAAC,gCAAU,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvD,MAAM,CAAC,gCAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;YACzB,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;gBACvC,MAAM,CAAC,gCAAU,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBACpE,MAAM,CAAC,gCAAU,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,CAAC,gCAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,GAAG,GAAG,IAAA,gCAAY,EAAC,UAAU,EAAE;gBACnC,KAAK,EAAE,eAAe;gBACtB,MAAM,EAAE,CAAC,eAAe,EAAE,eAAe,CAAC;aAC3C,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,GAAG,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,GAAG,GAAG,IAAA,gCAAY,EAAC,UAAU,EAAE;gBACnC,KAAK,EAAE,aAAa;gBACpB,MAAM,EAAE,CAAC,eAAe,EAAE,aAAa,CAAC;aACzC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,GAAG,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAEzC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC;YACpE,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;YAEtE,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\__tests__\\is-cve-id.validator.spec.ts"], "sourcesContent": ["import { validate } from 'class-validator';\r\nimport { plainToClass } from 'class-transformer';\r\nimport {\r\n  IsCveId,\r\n  IsCveIdArray,\r\n  CveIdUtils,\r\n  IsCveIdConstraint,\r\n} from '../is-cve-id.validator';\r\n\r\nclass TestCveDto {\r\n  @IsCveId()\r\n  cveId: string;\r\n\r\n  @IsCveIdArray()\r\n  cveIds: string[];\r\n}\r\n\r\ndescribe('CVE ID Validator', () => {\r\n  let constraint: IsCveIdConstraint;\r\n\r\n  beforeEach(() => {\r\n    constraint = new IsCveIdConstraint();\r\n  });\r\n\r\n  describe('IsCveIdConstraint', () => {\r\n    describe('validate', () => {\r\n      it('should validate correct CVE ID format', () => {\r\n        const validCveIds = [\r\n          'CVE-2021-1234',\r\n          'CVE-2023-12345',\r\n          'CVE-2024-123456',\r\n          'CVE-2020-0001',\r\n          'CVE-1999-9999',\r\n        ];\r\n\r\n        validCveIds.forEach(cveId => {\r\n          expect(constraint.validate(cveId, {} as any)).toBe(true);\r\n        });\r\n      });\r\n\r\n      it('should reject invalid CVE ID formats', () => {\r\n        const invalidCveIds = [\r\n          'CVE-21-1234', // Invalid year format\r\n          'CVE-2021-123', // Too few digits in sequence\r\n          'cve-2021-1234', // Lowercase\r\n          'CVE-2021-', // Missing sequence\r\n          'CVE-2021-abcd', // Non-numeric sequence\r\n          '2021-1234', // Missing CVE prefix\r\n          'CVE-1998-1234', // Year too early\r\n          'CVE-2030-1234', // Year too far in future\r\n          '', // Empty string\r\n          null, // Null value\r\n          undefined, // Undefined value\r\n          123, // Number instead of string\r\n        ];\r\n\r\n        invalidCveIds.forEach(cveId => {\r\n          expect(constraint.validate(cveId, {} as any)).toBe(false);\r\n        });\r\n      });\r\n\r\n      it('should validate year range correctly', () => {\r\n        const currentYear = new Date().getFullYear();\r\n        \r\n        // Valid years\r\n        expect(constraint.validate('CVE-1999-1234', {} as any)).toBe(true);\r\n        expect(constraint.validate(`CVE-${currentYear}-1234`, {} as any)).toBe(true);\r\n        expect(constraint.validate(`CVE-${currentYear + 1}-1234`, {} as any)).toBe(true);\r\n        \r\n        // Invalid years\r\n        expect(constraint.validate('CVE-1998-1234', {} as any)).toBe(false);\r\n        expect(constraint.validate(`CVE-${currentYear + 2}-1234`, {} as any)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('defaultMessage', () => {\r\n      it('should return appropriate error message', () => {\r\n        const message = constraint.defaultMessage({\r\n          property: 'cveId',\r\n        } as any);\r\n        \r\n        expect(message).toContain('cveId');\r\n        expect(message).toContain('CVE identifier');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('CveIdUtils', () => {\r\n    describe('extractYear', () => {\r\n      it('should extract year from valid CVE ID', () => {\r\n        expect(CveIdUtils.extractYear('CVE-2021-1234')).toBe(2021);\r\n        expect(CveIdUtils.extractYear('CVE-2023-56789')).toBe(2023);\r\n      });\r\n\r\n      it('should return null for invalid CVE ID', () => {\r\n        expect(CveIdUtils.extractYear('invalid')).toBeNull();\r\n        expect(CveIdUtils.extractYear('CVE-21-1234')).toBeNull();\r\n      });\r\n    });\r\n\r\n    describe('extractSequenceNumber', () => {\r\n      it('should extract sequence number from valid CVE ID', () => {\r\n        expect(CveIdUtils.extractSequenceNumber('CVE-2021-1234')).toBe(1234);\r\n        expect(CveIdUtils.extractSequenceNumber('CVE-2023-56789')).toBe(56789);\r\n      });\r\n\r\n      it('should return null for invalid CVE ID', () => {\r\n        expect(CveIdUtils.extractSequenceNumber('invalid')).toBeNull();\r\n        expect(CveIdUtils.extractSequenceNumber('CVE-21-1234')).toBeNull();\r\n      });\r\n    });\r\n\r\n    describe('generateCveId', () => {\r\n      it('should generate valid CVE ID from components', () => {\r\n        expect(CveIdUtils.generateCveId(2021, 1234)).toBe('CVE-2021-1234');\r\n        expect(CveIdUtils.generateCveId(2023, 123)).toBe('CVE-2023-0123');\r\n      });\r\n    });\r\n\r\n    describe('isValid', () => {\r\n      it('should validate CVE ID correctly', () => {\r\n        expect(CveIdUtils.isValid('CVE-2021-1234')).toBe(true);\r\n        expect(CveIdUtils.isValid('invalid')).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('normalize', () => {\r\n      it('should normalize valid CVE ID', () => {\r\n        expect(CveIdUtils.normalize('cve-2021-1234')).toBe('CVE-2021-1234');\r\n        expect(CveIdUtils.normalize('CVE-2021-1234')).toBe('CVE-2021-1234');\r\n      });\r\n\r\n      it('should return null for invalid CVE ID', () => {\r\n        expect(CveIdUtils.normalize('invalid')).toBeNull();\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('DTO Validation', () => {\r\n    it('should validate DTO with valid CVE ID', async () => {\r\n      const dto = plainToClass(TestCveDto, {\r\n        cveId: 'CVE-2021-1234',\r\n        cveIds: ['CVE-2021-1234', 'CVE-2022-5678'],\r\n      });\r\n\r\n      const errors = await validate(dto);\r\n      expect(errors).toHaveLength(0);\r\n    });\r\n\r\n    it('should reject DTO with invalid CVE ID', async () => {\r\n      const dto = plainToClass(TestCveDto, {\r\n        cveId: 'invalid-cve',\r\n        cveIds: ['CVE-2021-1234', 'invalid-cve'],\r\n      });\r\n\r\n      const errors = await validate(dto);\r\n      expect(errors.length).toBeGreaterThan(0);\r\n      \r\n      const cveIdError = errors.find(error => error.property === 'cveId');\r\n      const cveIdsError = errors.find(error => error.property === 'cveIds');\r\n      \r\n      expect(cveIdError).toBeDefined();\r\n      expect(cveIdsError).toBeDefined();\r\n    });\r\n  });\r\n});"], "version": 3}