c59b9d9a6560cc17fc076750a8f5e912
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IOC = void 0;
const typeorm_1 = require("typeorm");
const threat_feed_entity_1 = require("./threat-feed.entity");
const threat_actor_entity_1 = require("./threat-actor.entity");
/**
 * Indicator of Compromise (IOC) entity
 * Represents security indicators used for threat detection and analysis
 */
let IOC = class IOC {
    /**
     * Check if IOC is expired
     */
    get isExpired() {
        return this.expiresAt ? new Date() > this.expiresAt : false;
    }
    /**
     * Check if IOC is active and valid
     */
    get isActive() {
        return this.status === 'active' && !this.isExpired;
    }
    /**
     * Get age of IOC in days
     */
    get ageInDays() {
        const now = new Date();
        const diffMs = now.getTime() - this.firstSeen.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Get risk score based on confidence and severity
     */
    get riskScore() {
        const severityWeights = {
            low: 1,
            medium: 2,
            high: 3,
            critical: 4,
        };
        const severityWeight = severityWeights[this.severity];
        const confidenceWeight = this.confidence / 100;
        return Math.round(severityWeight * confidenceWeight * 2.5 * 10) / 10;
    }
    /**
     * Check if IOC type is a hash
     */
    get isHash() {
        return this.type.startsWith('file_hash_');
    }
    /**
     * Check if IOC type is network-related
     */
    get isNetworkIndicator() {
        return ['ip_address', 'domain', 'url', 'cidr'].includes(this.type);
    }
    /**
     * Get normalized IOC value for comparison
     */
    get normalizedValue() {
        switch (this.type) {
            case 'domain':
            case 'email':
                return this.value.toLowerCase();
            case 'file_hash_md5':
            case 'file_hash_sha1':
            case 'file_hash_sha256':
                return this.value.toLowerCase();
            case 'ip_address':
                return this.value.trim();
            default:
                return this.value;
        }
    }
    /**
     * Update last seen timestamp
     */
    updateLastSeen() {
        this.lastSeen = new Date();
        this.observationCount += 1;
    }
    /**
     * Mark IOC as validated
     */
    markAsValidated(notes) {
        this.isValidated = true;
        this.validationNotes = notes;
    }
    /**
     * Mark IOC as false positive
     */
    markAsFalsePositive(reason) {
        this.status = 'false_positive';
        this.validationNotes = reason;
        this.isMonitored = false;
    }
    /**
     * Add enrichment data
     */
    addEnrichmentData(source, data) {
        if (!this.enrichmentData) {
            this.enrichmentData = {};
        }
        this.enrichmentData[source] = {
            ...data,
            enrichedAt: new Date().toISOString(),
        };
    }
};
exports.IOC = IOC;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], IOC.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'ip_address',
            'domain',
            'url',
            'file_hash_md5',
            'file_hash_sha1',
            'file_hash_sha256',
            'email',
            'registry_key',
            'file_path',
            'mutex',
            'user_agent',
            'certificate',
            'asn',
            'cidr',
            'other',
        ],
    }),
    __metadata("design:type", String)
], IOC.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 2048 }),
    __metadata("design:type", String)
], IOC.prototype, "value", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], IOC.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', default: 50 }),
    __metadata("design:type", Number)
], IOC.prototype, "confidence", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium',
    }),
    __metadata("design:type", String)
], IOC.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['active', 'inactive', 'expired', 'false_positive', 'whitelisted'],
        default: 'active',
    }),
    __metadata("design:type", String)
], IOC.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['white', 'green', 'amber', 'red'],
        default: 'white',
    }),
    __metadata("design:type", String)
], IOC.prototype, "tlp", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], IOC.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mitre_techniques', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], IOC.prototype, "mitreTechniques", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'kill_chain_phases', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], IOC.prototype, "killChainPhases", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'first_seen', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], IOC.prototype, "firstSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_seen', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], IOC.prototype, "lastSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'expires_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], IOC.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], IOC.prototype, "source", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], IOC.prototype, "references", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'observation_count', type: 'integer', default: 1 }),
    __metadata("design:type", Number)
], IOC.prototype, "observationCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_monitored', default: true }),
    __metadata("design:type", Boolean)
], IOC.prototype, "isMonitored", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_validated', default: false }),
    __metadata("design:type", Boolean)
], IOC.prototype, "isValidated", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'validation_notes', type: 'text', nullable: true }),
    __metadata("design:type", String)
], IOC.prototype, "validationNotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], IOC.prototype, "geolocation", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], IOC.prototype, "context", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'detection_rules', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], IOC.prototype, "detectionRules", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'enrichment_data', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_d = typeof Record !== "undefined" && Record) === "function" ? _d : Object)
], IOC.prototype, "enrichmentData", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_e = typeof Record !== "undefined" && Record) === "function" ? _e : Object)
], IOC.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], IOC.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], IOC.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], IOC.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_g = typeof Date !== "undefined" && Date) === "function" ? _g : Object)
], IOC.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => threat_feed_entity_1.ThreatFeed, threatFeed => threatFeed.iocs, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'threat_feed_id' }),
    __metadata("design:type", typeof (_h = typeof threat_feed_entity_1.ThreatFeed !== "undefined" && threat_feed_entity_1.ThreatFeed) === "function" ? _h : Object)
], IOC.prototype, "threatFeed", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'threat_feed_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], IOC.prototype, "threatFeedId", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => threat_actor_entity_1.ThreatActor, threatActor => threatActor.iocs),
    (0, typeorm_1.JoinTable)({
        name: 'ioc_threat_actors',
        joinColumn: { name: 'ioc_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'threat_actor_id', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], IOC.prototype, "threatActors", void 0);
exports.IOC = IOC = __decorate([
    (0, typeorm_1.Entity)('iocs'),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['value']),
    (0, typeorm_1.Index)(['confidence']),
    (0, typeorm_1.Index)(['severity']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['firstSeen']),
    (0, typeorm_1.Index)(['lastSeen'])
], IOC);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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