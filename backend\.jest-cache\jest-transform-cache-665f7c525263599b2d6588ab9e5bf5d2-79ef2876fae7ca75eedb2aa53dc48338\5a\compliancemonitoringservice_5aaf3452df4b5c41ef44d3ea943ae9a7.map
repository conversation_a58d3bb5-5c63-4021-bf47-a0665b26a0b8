{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\application\\services\\compliance-monitoring.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,6CAAmD;AACnD,qCAAmE;AACnE,+CAAwD;AACxD,mGAAwF;AACxF,qGAA0F;AAC1F,6FAAkF;AAClF,2FAAgF;AAChF,6EAAkE;AAClE,sFAAkF;AAClF,0FAAsF;AACtF,uGAAmG;AACnG,6EAAwE;AACxE,+EAA0E;AAE1E;;;GAGG;AAEI,IAAM,2BAA2B,mCAAjC,MAAM,2BAA2B;IAGtC,YAEE,mBAAqE,EAErE,oBAAuE,EAEvE,gBAA+D,EAE/D,mBAAiE,EAEjE,kBAAyD,EACxC,aAA4B,EAC5B,YAA0B,EAC1B,mBAAwC,EACxC,wBAAkD,EAClD,yBAAoD;QAbpD,wBAAmB,GAAnB,mBAAmB,CAAiC;QAEpD,yBAAoB,GAApB,oBAAoB,CAAkC;QAEtD,qBAAgB,GAAhB,gBAAgB,CAA8B;QAE9C,wBAAmB,GAAnB,mBAAmB,CAA6B;QAEhD,uBAAkB,GAAlB,kBAAkB,CAAsB;QACxC,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;QAC1B,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,8BAAyB,GAAzB,yBAAyB,CAA2B;QAjBtD,WAAM,GAAG,IAAI,eAAM,CAAC,6BAA2B,CAAC,IAAI,CAAC,CAAC;IAkBpE,CAAC;IAEJ;;;OAGG;IACH,KAAK,CAAC,sBAAsB;QAC1B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YAErD,MAAM,CACJ,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,kBAAkB,EAClB,qBAAqB,EACrB,gBAAgB,EAChB,mBAAmB,EACnB,gBAAgB,EACjB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,0BAA0B,EAAE;gBACjC,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,cAAc;gBAC3C,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,EAAE,eAAe;gBAChD,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,EAAE,eAAe;aAC9C,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG;gBAChB,OAAO,EAAE;oBACP,gBAAgB;oBAChB,gBAAgB;oBAChB,cAAc;oBACd,kBAAkB;oBAClB,sBAAsB,EAAE,MAAM,IAAI,CAAC,+BAA+B,EAAE;iBACrE;gBACD,qBAAqB;gBACrB,gBAAgB;gBAChB,mBAAmB;gBACnB,MAAM,EAAE,gBAAgB;gBACxB,WAAW,EAAE,MAAM,IAAI,CAAC,oBAAoB,EAAE;gBAC9C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6CAA6C,EAAE;gBAC7D,gBAAgB;gBAChB,cAAc;gBACd,kBAAkB;aACnB,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE;gBAC3D,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IAEG,AAAN,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAE1D,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;gBAC3D,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,KAAK,MAAM,SAAS,IAAI,gBAAgB,EAAE,CAAC;gBACzC,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBACtD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE;wBAC1D,WAAW,EAAE,SAAS,CAAC,EAAE;wBACzB,aAAa,EAAE,SAAS,CAAC,IAAI;wBAC7B,KAAK,EAAE,KAAK,CAAC,OAAO;qBACrB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,gCAAgC;YAChC,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAErC,8BAA8B;YAC9B,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAEnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,EAAE;gBACvD,mBAAmB,EAAE,gBAAgB,CAAC,MAAM;aAC7C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,EAAE;gBAClE,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,0BAA0B,CAAC,WAAmB;QAClD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;gBACvD,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;gBAC1B,SAAS,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC;aACvC,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,0BAAiB,CAAC,qBAAqB,CAAC,CAAC;YACrD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBACnD,WAAW;gBACX,aAAa,EAAE,SAAS,CAAC,IAAI;aAC9B,CAAC,CAAC;YAEH,wBAAwB;YACxB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAErE,oCAAoC;YACpC,IAAI,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,SAAS,CAAC,EAAE,CAAC;gBACtD,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YAC3C,CAAC;YAED,8BAA8B;YAC9B,MAAM,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;YAEhD,0BAA0B;YAC1B,MAAM,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;YAE5C,4BAA4B;YAC5B,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YAE9C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE;gBAC7D,WAAW;aACZ,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE;gBAC1D,WAAW;gBACX,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,gBAAgB,CACpB,WAAmB,EACnB,cAMC,EACD,MAAc;QAEd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,WAAW;gBACX,cAAc,EAAE,cAAc,CAAC,cAAc;gBAC7C,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;gBACvD,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;aAC3B,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,0BAAiB,CAAC,qBAAqB,CAAC,CAAC;YACrD,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;gBAClD,GAAG,cAAc;gBACjB,WAAW;gBACX,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEzE,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,uBAAuB,EACvB,eAAe,CAAC,EAAE,EAClB;gBACE,WAAW;gBACX,cAAc,EAAE,cAAc,CAAC,cAAc;gBAC7C,cAAc,EAAE,cAAc,CAAC,IAAI;aACpC,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4CAA4C,EAAE;gBAC5D,YAAY,EAAE,eAAe,CAAC,EAAE;gBAChC,WAAW;gBACX,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE;gBAC1D,WAAW;gBACX,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,eAAe,CAAC,YAAoB,EAAE,MAAc;QACxD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;gBACzD,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;gBAC3B,SAAS,EAAE,CAAC,WAAW,CAAC;aACzB,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,CAAC,CAAC;YACtD,CAAC;YAED,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACzB,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE3E,gDAAgD;YAChD,MAAM,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAAC;YAEpD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,OAAO,EACP,uBAAuB,EACvB,YAAY,EACZ;gBACE,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,cAAc,EAAE,UAAU,CAAC,cAAc;aAC1C,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,EAAE;gBAC/C,YAAY;gBACZ,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;gBACzD,YAAY;gBACZ,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,kBAAkB,CACtB,YAAoB,EACpB,OAAY,EACZ,MAAc;QAEd,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;gBACzD,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;gBAC3B,SAAS,EAAE,CAAC,WAAW,CAAC;aACzB,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,CAAC,CAAC;YACtD,CAAC;YAED,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACrC,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE3E,iDAAiD;YACjD,MAAM,IAAI,CAAC,gCAAgC,CAAC,UAAU,CAAC,CAAC;YAExD,qBAAqB;YACrB,MAAM,IAAI,CAAC,mBAAmB,CAAC,0BAA0B,CACvD,UAAU,EACV,sBAAsB,CACvB,CAAC;YAEF,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,UAAU,EACV,uBAAuB,EACvB,YAAY,EACZ;gBACE,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,eAAe,EAAE,OAAO,CAAC,YAAY;gBACrC,gBAAgB,EAAE,OAAO,CAAC,aAAa;aACxC,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,EAAE;gBACjD,YAAY;gBACZ,eAAe,EAAE,OAAO,CAAC,YAAY;gBACrC,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE;gBAC5D,YAAY;gBACZ,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,4BAA4B,CAAC,WAAmB;QACpD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;gBACvD,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;gBAC1B,SAAS,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC;aACvC,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,0BAAiB,CAAC,qBAAqB,CAAC,CAAC;YACrD,CAAC;YAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YACrE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAC5E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAE9D,MAAM,MAAM,GAAG;gBACb,SAAS,EAAE;oBACT,EAAE,EAAE,SAAS,CAAC,EAAE;oBAChB,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;oBAC1B,aAAa,EAAE,SAAS,CAAC,aAAa;iBACvC;gBACD,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,CAAC;oBACnC,EAAE,EAAE,gBAAgB,CAAC,EAAE;oBACvB,cAAc,EAAE,gBAAgB,CAAC,cAAc;oBAC/C,MAAM,EAAE,gBAAgB,CAAC,MAAM;oBAC/B,oBAAoB,EAAE,gBAAgB,CAAC,oBAAoB;oBAC3D,aAAa,EAAE,gBAAgB,CAAC,OAAO,EAAE,aAAa;oBACtD,gBAAgB,EAAE,gBAAgB,CAAC,qBAAqB;oBACxD,YAAY,EAAE,gBAAgB,CAAC,iBAAiB;iBACjD,CAAC,CAAC,CAAC,IAAI;gBACR,UAAU,EAAE;oBACV,KAAK,EAAE,cAAc,CAAC,MAAM;oBAC5B,QAAQ,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM;oBACtE,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM;oBAC9D,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,MAAM;oBAClE,GAAG,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM;iBAC7D;gBACD,QAAQ,EAAE;oBACR,KAAK,EAAE,QAAQ,CAAC,MAAM;oBACtB,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM;oBAC/C,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM;oBACjD,YAAY,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,MAAM;iBAC5D;gBACD,eAAe,EAAE,MAAM,IAAI,CAAC,iCAAiC,CAAC,WAAW,CAAC;gBAC1E,SAAS,EAAE,MAAM,IAAI,CAAC,2BAA2B,CAAC,WAAW,CAAC;gBAC9D,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB;gBACxD,SAAS,EAAE,gBAAgB,EAAE,SAAS,IAAI,KAAK;aAChD,CAAC;YAEF,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE;gBAC7D,WAAW;gBACX,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,mBAAmB,CAAC,OAAe,EAAE;QACzC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YAE9C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE;oBACL,cAAc,EAAE,IAAA,iBAAO,EAAC,SAAS,EAAE,OAAO,CAAC;oBAC3C,MAAM,EAAE,WAAW;iBACpB;gBACD,SAAS,EAAE,CAAC,WAAW,CAAC;gBACxB,KAAK,EAAE,EAAE,cAAc,EAAE,KAAK,EAAE;aACjC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;gBACrD,KAAK,EAAE;oBACL,UAAU,EAAE,IAAA,iBAAO,EAAC,SAAS,EAAE,OAAO,CAAC;iBACxC;gBACD,KAAK,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE;aAC7B,CAAC,CAAC;YAEH,qBAAqB;YACrB,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;YAEnF,OAAO;gBACL,MAAM,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE;gBACpC,MAAM;gBACN,OAAO,EAAE;oBACP,gBAAgB,EAAE,WAAW,CAAC,MAAM;oBACpC,eAAe,EAAE,UAAU,CAAC,MAAM;oBAClC,sBAAsB,EAAE,IAAI,CAAC,+BAA+B,CAAC,WAAW,CAAC;oBACzE,cAAc,EAAE,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;iBACrD;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBACnD,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,yBAAyB;IAEjB,KAAK,CAAC,wBAAwB;QACpC,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IAC7E,CAAC;IAEO,KAAK,CAAC,wBAAwB;QACpC,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;IACjD,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAClC,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;YAC1C,KAAK,EAAE,EAAE,MAAM,EAAE,IAAA,YAAE,EAAC,CAAC,MAAM,EAAE,eAAe,EAAE,qBAAqB,EAAE,yBAAyB,CAAC,CAAC,EAAE;SACnG,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,0BAA0B;QACtC,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;YAC1C,KAAK,EAAE;gBACL,QAAQ,EAAE,UAAU;gBACpB,MAAM,EAAE,IAAA,YAAE,EAAC,CAAC,MAAM,EAAE,eAAe,EAAE,qBAAqB,EAAE,yBAAyB,CAAC,CAAC;aACxF;SACF,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,wBAAwB;QACpC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YACrD,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;YACzB,SAAS,EAAE,CAAC,aAAa,CAAC;SAC3B,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,EAAE,CAAC;QAE1B,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACtE,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,iCAAiC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAEnF,cAAc,CAAC,IAAI,CAAC;gBAClB,WAAW,EAAE,SAAS,CAAC,EAAE;gBACzB,aAAa,EAAE,SAAS,CAAC,IAAI;gBAC7B,aAAa,EAAE,SAAS,CAAC,IAAI;gBAC7B,eAAe;gBACf,kBAAkB,EAAE,gBAAgB,EAAE,cAAc;gBACpD,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,aAAa,IAAI,SAAS;aAC9D,CAAC,CAAC;QACL,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,IAAY;QAC5C,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QAE9C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YACrD,KAAK,EAAE,EAAE,UAAU,EAAE,IAAA,iBAAO,EAAC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,EAAE;YACrD,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;YAC7B,IAAI,EAAE,EAAE;SACT,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;IAClD,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,IAAY;QAC/C,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3B,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QAE1C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YACvD,KAAK,EAAE;gBACL,kBAAkB,EAAE,IAAA,iBAAO,EAAC,IAAI,IAAI,EAAE,EAAE,OAAO,CAAC;gBAChD,MAAM,EAAE,IAAA,YAAE,EAAC,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;aACvC;YACD,SAAS,EAAE,CAAC,WAAW,CAAC;YACxB,KAAK,EAAE,EAAE,kBAAkB,EAAE,KAAK,EAAE;SACrC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;IACnD,CAAC;IAEO,KAAK,CAAC,+BAA+B;QAC3C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QAEtF,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEtC,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,iCAAiC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACzE,UAAU,IAAI,KAAK,CAAC;QACtB,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;IACpD,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACnE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;YAC1D,KAAK,EAAE;gBACL,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,IAAA,YAAE,EAAC,CAAC,MAAM,EAAE,eAAe,EAAE,qBAAqB,EAAE,yBAAyB,CAAC,CAAC;aACxF;SACF,CAAC,CAAC;QAEH,MAAM,gBAAgB,GAAG,CAAC,kBAAkB,GAAG,EAAE,CAAC,GAAG,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;QAE1E,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,gBAAgB,IAAI,EAAE;YAAE,SAAS,GAAG,UAAU,CAAC;aAC9C,IAAI,gBAAgB,IAAI,EAAE;YAAE,SAAS,GAAG,MAAM,CAAC;aAC/C,IAAI,gBAAgB,IAAI,EAAE;YAAE,SAAS,GAAG,QAAQ,CAAC;QAEtD,OAAO;YACL,gBAAgB;YAChB,SAAS;YACT,kBAAkB;YAClB,cAAc;SACf,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,WAAmB;QACnD,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,EAAE,WAAW,EAAE;YACtB,KAAK,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE;SAClC,CAAC,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,UAAuC,EAAE,SAA8B;QAC7F,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAC7B,IAAI,CAAC,UAAU,CAAC,kBAAkB;YAAE,OAAO,KAAK,CAAC;QACjD,OAAO,IAAI,IAAI,EAAE,IAAI,UAAU,CAAC,kBAAkB,CAAC;IACrD,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,SAA8B;QAC7D,+CAA+C;QAC/C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,EAAE;YAC9C,WAAW,EAAE,SAAS,CAAC,EAAE;YACzB,aAAa,EAAE,SAAS,CAAC,IAAI;SAC9B,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,SAA8B;QACpE,oDAAoD;QACpD,MAAM,kBAAkB,GAAG,SAAS,CAAC,mBAAmB,CAAC;QAEzD,KAAK,MAAM,OAAO,IAAI,kBAAkB,EAAE,CAAC;YACzC,MAAM,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,SAA8B;QAChE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAChD,KAAK,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;SACrD,CAAC,CAAC;QAEH,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,yBAAyB,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,SAA8B;QAClE,iDAAiD;QACjD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;YAC/C,WAAW,EAAE,SAAS,CAAC,EAAE;SAC1B,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAC9D,KAAK,EAAE;gBACL,kBAAkB,EAAE,IAAA,iBAAO,EAAC,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;gBAC/D,MAAM,EAAE,IAAA,YAAE,EAAC,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;aACvC;YACD,SAAS,EAAE,CAAC,WAAW,CAAC;SACzB,CAAC,CAAC;QAEH,KAAK,MAAM,UAAU,IAAI,kBAAkB,EAAE,CAAC;YAC5C,MAAM,IAAI,CAAC,mBAAmB,CAAC,iCAAiC,CAAC,UAAU,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,MAAM,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,EAAE,CAAC;IAC3D,CAAC;IAEO,KAAK,CAAC,4BAA4B,CAAC,UAAgC;QACzE,gDAAgD;QAChD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACxB,UAAU,CAAC,OAAO,GAAG,EAAE,aAAa,EAAE,EAAE,EAAE,CAAC;QAC7C,CAAC;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;QACvC,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YACrD,MAAM,YAAY,GAAG;gBACnB,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,UAAU,EAAE,MAAM,CAAC,IAAI;gBACvB,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,eAAwB;gBAChC,cAAc,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;oBAC9C,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,WAAW,EAAE,OAAO,CAAC,IAAI;oBACzB,MAAM,EAAE,YAAqB;iBAC9B,CAAC,CAAC;aACJ,CAAC;YAEF,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACnD,CAAC;IAEO,KAAK,CAAC,gCAAgC,CAAC,UAAgC;QAC7E,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa;YAAE,OAAO;QAE/C,KAAK,MAAM,MAAM,IAAI,UAAU,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YACtD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC5C,IAAI,OAAO,CAAC,MAAM,KAAK,eAAe,EAAE,CAAC;oBACvC,MAAM,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,UAAgC,EAAE,MAAW,EAAE,OAAY;QAClG,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAChD,KAAK,EAAE,uBAAuB,OAAO,CAAC,WAAW,EAAE;YACnD,WAAW,EAAE,WAAW,OAAO,CAAC,SAAS,cAAc,MAAM,CAAC,UAAU,mBAAmB;YAC3F,aAAa,EAAE,uBAAuB;YACtC,QAAQ,EAAE,OAAO,CAAC,SAAS,IAAI,QAAQ;YACvC,UAAU,EAAE,IAAI,IAAI,EAAE;YACtB,YAAY,EAAE,UAAU,CAAC,EAAE;YAC3B,OAAO,EAAE;gBACP,gBAAgB,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC;gBACrC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;gBAChC,eAAe,EAAE,uBAAuB;gBACxC,UAAU,EAAE;oBACV,UAAU,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;oBACpC,QAAQ,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC;oBAC7B,YAAY,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;iBACrC;aACF;YACD,UAAU,EAAE,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS;YACxD,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,UAAU,CAAC,SAAS,EAAE,IAAI,IAAI,SAAS,CAAC;SAC5E,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACjD,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,WAAmB,EAAE,WAAoB,KAAK;QACjF,MAAM,eAAe,GAAQ,EAAE,CAAC;QAEhC,IAAI,QAAQ,EAAE,CAAC;YACb,eAAe,CAAC,MAAM,GAAG,IAAA,YAAE,EAAC,CAAC,MAAM,EAAE,eAAe,EAAE,qBAAqB,EAAE,yBAAyB,CAAC,CAAC,CAAC;QAC3G,CAAC;QAED,qFAAqF;QACrF,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;IACzE,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,WAAmB;QACpD,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC;IACtE,CAAC;IAEO,KAAK,CAAC,iCAAiC,CAAC,WAAmB;QACjE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACrE,OAAO,gBAAgB,EAAE,oBAAoB,IAAI,CAAC,CAAC;IACrD,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,WAAmB;QAC3D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACxE,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;QAC/E,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QAEvE,IAAI,aAAa,GAAG,CAAC;YAAE,OAAO,UAAU,CAAC;QACzC,IAAI,SAAS,GAAG,CAAC;YAAE,OAAO,MAAM,CAAC;QACjC,IAAI,SAAS,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,QAAQ,CAAC;QAC5D,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,iBAAiB,CAAC,WAAmC,EAAE,UAA6B,EAAE,SAAe,EAAE,OAAa;QAC1H,6CAA6C;QAC7C,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QAExC,OAAO,WAAW,IAAI,OAAO,EAAE,CAAC;YAC9B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC;YACxC,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC;YACtC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAEvC,MAAM,eAAe,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC7C,CAAC,CAAC,cAAc,IAAI,SAAS,IAAI,CAAC,CAAC,cAAc,IAAI,OAAO,CAC7D,CAAC;YAEF,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC3C,CAAC,CAAC,UAAU,IAAI,SAAS,IAAI,CAAC,CAAC,UAAU,IAAI,OAAO,CACrD,CAAC;YAEF,KAAK,CAAC,IAAI,CAAC;gBACT,SAAS;gBACT,OAAO;gBACP,WAAW,EAAE,eAAe,CAAC,MAAM;gBACnC,UAAU,EAAE,cAAc,CAAC,MAAM;gBACjC,sBAAsB,EAAE,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC;aAC9E,CAAC,CAAC;YAEH,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,+BAA+B,CAAC,WAAmC;QACzE,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEvC,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE,CACxD,GAAG,GAAG,CAAC,UAAU,CAAC,oBAAoB,IAAI,CAAC,CAAC,EAAE,CAAC,CAChD,CAAC;QAEF,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAEO,uBAAuB,CAAC,MAAa;QAC3C,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,QAAQ,CAAC;QAEvC,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACjE,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAE/D,MAAM,QAAQ,GAAG,IAAI,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;QACjE,MAAM,SAAS,GAAG,IAAI,CAAC,+BAA+B,CAAC,UAAU,CAAC,CAAC;QAEnE,IAAI,SAAS,GAAG,QAAQ,GAAG,CAAC;YAAE,OAAO,WAAW,CAAC;QACjD,IAAI,SAAS,GAAG,QAAQ,GAAG,CAAC;YAAE,OAAO,WAAW,CAAC;QACjD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AA/xBY,kEAA2B;AAoFhC;IADL,IAAA,eAAI,EAAC,yBAAc,CAAC,UAAU,CAAC;;;wDACL,OAAO,oBAAP,OAAO;oEAkCjC;sCAtHU,2BAA2B;IADvC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,iDAAmB,CAAC,CAAA;IAErC,WAAA,IAAA,0BAAgB,EAAC,mDAAoB,CAAC,CAAA;IAEtC,WAAA,IAAA,0BAAgB,EAAC,2CAAgB,CAAC,CAAA;IAElC,WAAA,IAAA,0BAAgB,EAAC,yCAAe,CAAC,CAAA;IAEjC,WAAA,IAAA,0BAAgB,EAAC,2BAAQ,CAAC,CAAA;yDAPW,oBAAU,oBAAV,oBAAU,oDAET,oBAAU,oBAAV,oBAAU,oDAEd,oBAAU,oBAAV,oBAAU,oDAEP,oBAAU,oBAAV,oBAAU,oDAEX,oBAAU,oBAAV,oBAAU,oDACf,8BAAa,oBAAb,8BAAa,oDACd,4BAAY,oBAAZ,4BAAY,oDACL,0CAAmB,oBAAnB,0CAAmB,oDACd,qDAAwB,oBAAxB,qDAAwB,oDACvB,uDAAyB,oBAAzB,uDAAyB;GAlB5D,2BAA2B,CA+xBvC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\application\\services\\compliance-monitoring.service.ts"], "sourcesContent": ["import { Injectable, Logger, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, FindManyOptions, In, Between } from 'typeorm';\r\nimport { <PERSON>ron, CronExpression } from '@nestjs/schedule';\r\nimport { ComplianceFramework } from '../../domain/entities/compliance-framework.entity';\r\nimport { ComplianceAssessment } from '../../domain/entities/compliance-assessment.entity';\r\nimport { PolicyDefinition } from '../../domain/entities/policy-definition.entity';\r\nimport { PolicyViolation } from '../../domain/entities/policy-violation.entity';\r\nimport { AuditLog } from '../../domain/entities/audit-log.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../infrastructure/notification/notification.service';\r\nimport { PolicyEnforcementService } from './policy-enforcement.service';\r\nimport { ViolationDetectionService } from './violation-detection.service';\r\n\r\n/**\r\n * Compliance Monitoring service\r\n * Handles continuous compliance monitoring, assessment scheduling, and violation tracking\r\n */\r\n@Injectable()\r\nexport class ComplianceMonitoringService {\r\n  private readonly logger = new Logger(ComplianceMonitoringService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(ComplianceFramework)\r\n    private readonly frameworkRepository: Repository<ComplianceFramework>,\r\n    @InjectRepository(ComplianceAssessment)\r\n    private readonly assessmentRepository: Repository<ComplianceAssessment>,\r\n    @InjectRepository(PolicyDefinition)\r\n    private readonly policyRepository: Repository<PolicyDefinition>,\r\n    @InjectRepository(PolicyViolation)\r\n    private readonly violationRepository: Repository<PolicyViolation>,\r\n    @InjectRepository(AuditLog)\r\n    private readonly auditLogRepository: Repository<AuditLog>,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly auditService: AuditService,\r\n    private readonly notificationService: NotificationService,\r\n    private readonly policyEnforcementService: PolicyEnforcementService,\r\n    private readonly violationDetectionService: ViolationDetectionService,\r\n  ) {}\r\n\r\n  /**\r\n   * Get compliance dashboard data\r\n   * @returns Compliance dashboard metrics\r\n   */\r\n  async getComplianceDashboard(): Promise<any> {\r\n    try {\r\n      this.logger.debug('Generating compliance dashboard');\r\n\r\n      const [\r\n        activeFrameworks,\r\n        totalAssessments,\r\n        openViolations,\r\n        criticalViolations,\r\n        complianceByFramework,\r\n        recentViolations,\r\n        upcomingAssessments,\r\n        complianceTrends,\r\n      ] = await Promise.all([\r\n        this.getActiveFrameworksCount(),\r\n        this.getTotalAssessmentsCount(),\r\n        this.getOpenViolationsCount(),\r\n        this.getCriticalViolationsCount(),\r\n        this.getComplianceByFramework(),\r\n        this.getRecentViolations(7), // Last 7 days\r\n        this.getUpcomingAssessments(30), // Next 30 days\r\n        this.getComplianceTrends(90), // Last 90 days\r\n      ]);\r\n\r\n      const dashboard = {\r\n        summary: {\r\n          activeFrameworks,\r\n          totalAssessments,\r\n          openViolations,\r\n          criticalViolations,\r\n          overallComplianceScore: await this.calculateOverallComplianceScore(),\r\n        },\r\n        complianceByFramework,\r\n        recentViolations,\r\n        upcomingAssessments,\r\n        trends: complianceTrends,\r\n        riskMetrics: await this.calculateRiskMetrics(),\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n\r\n      this.logger.log('Compliance dashboard generated successfully', {\r\n        activeFrameworks,\r\n        openViolations,\r\n        criticalViolations,\r\n      });\r\n\r\n      return dashboard;\r\n    } catch (error) {\r\n      this.logger.error('Failed to generate compliance dashboard', {\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Monitor compliance status for all active frameworks\r\n   */\r\n  @Cron(CronExpression.EVERY_HOUR)\r\n  async monitorCompliance(): Promise<void> {\r\n    try {\r\n      this.logger.debug('Starting compliance monitoring cycle');\r\n\r\n      const activeFrameworks = await this.frameworkRepository.find({\r\n        where: { isActive: true },\r\n      });\r\n\r\n      for (const framework of activeFrameworks) {\r\n        try {\r\n          await this.monitorFrameworkCompliance(framework.id);\r\n        } catch (error) {\r\n          this.logger.error('Failed to monitor framework compliance', {\r\n            frameworkId: framework.id,\r\n            frameworkName: framework.name,\r\n            error: error.message,\r\n          });\r\n        }\r\n      }\r\n\r\n      // Check for overdue assessments\r\n      await this.checkOverdueAssessments();\r\n\r\n      // Check for policy violations\r\n      await this.checkPolicyViolations();\r\n\r\n      this.logger.log('Compliance monitoring cycle completed', {\r\n        frameworksMonitored: activeFrameworks.length,\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to complete compliance monitoring cycle', {\r\n        error: error.message,\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Monitor compliance for a specific framework\r\n   * @param frameworkId Framework ID\r\n   */\r\n  async monitorFrameworkCompliance(frameworkId: string): Promise<void> {\r\n    try {\r\n      const framework = await this.frameworkRepository.findOne({\r\n        where: { id: frameworkId },\r\n        relations: ['assessments', 'policies'],\r\n      });\r\n\r\n      if (!framework) {\r\n        throw new NotFoundException('Framework not found');\r\n      }\r\n\r\n      this.logger.debug('Monitoring framework compliance', {\r\n        frameworkId,\r\n        frameworkName: framework.name,\r\n      });\r\n\r\n      // Get latest assessment\r\n      const latestAssessment = await this.getLatestAssessment(frameworkId);\r\n\r\n      // Check if new assessment is needed\r\n      if (this.isAssessmentDue(latestAssessment, framework)) {\r\n        await this.scheduleAssessment(framework);\r\n      }\r\n\r\n      // Monitor continuous controls\r\n      await this.monitorContinuousControls(framework);\r\n\r\n      // Check policy compliance\r\n      await this.checkPolicyCompliance(framework);\r\n\r\n      // Update compliance metrics\r\n      await this.updateComplianceMetrics(framework);\r\n\r\n      this.logger.debug('Framework compliance monitoring completed', {\r\n        frameworkId,\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to monitor framework compliance', {\r\n        frameworkId,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create compliance assessment\r\n   * @param frameworkId Framework ID\r\n   * @param assessmentData Assessment data\r\n   * @param userId User creating the assessment\r\n   * @returns Created assessment\r\n   */\r\n  async createAssessment(\r\n    frameworkId: string,\r\n    assessmentData: {\r\n      name: string;\r\n      description: string;\r\n      assessmentType: 'self_assessment' | 'internal_audit' | 'external_audit' | 'certification' | 'continuous_monitoring';\r\n      assessmentDate: Date;\r\n      scope: any;\r\n    },\r\n    userId: string,\r\n  ): Promise<ComplianceAssessment> {\r\n    try {\r\n      this.logger.debug('Creating compliance assessment', {\r\n        frameworkId,\r\n        assessmentType: assessmentData.assessmentType,\r\n        userId,\r\n      });\r\n\r\n      const framework = await this.frameworkRepository.findOne({\r\n        where: { id: frameworkId },\r\n      });\r\n\r\n      if (!framework) {\r\n        throw new NotFoundException('Framework not found');\r\n      }\r\n\r\n      const assessment = this.assessmentRepository.create({\r\n        ...assessmentData,\r\n        frameworkId,\r\n        status: 'planned',\r\n        createdBy: userId,\r\n      });\r\n\r\n      const savedAssessment = await this.assessmentRepository.save(assessment);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'create',\r\n        'compliance_assessment',\r\n        savedAssessment.id,\r\n        {\r\n          frameworkId,\r\n          assessmentType: assessmentData.assessmentType,\r\n          assessmentName: assessmentData.name,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Compliance assessment created successfully', {\r\n        assessmentId: savedAssessment.id,\r\n        frameworkId,\r\n        userId,\r\n      });\r\n\r\n      return savedAssessment;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create compliance assessment', {\r\n        frameworkId,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Start compliance assessment\r\n   * @param assessmentId Assessment ID\r\n   * @param userId User starting the assessment\r\n   * @returns Updated assessment\r\n   */\r\n  async startAssessment(assessmentId: string, userId: string): Promise<ComplianceAssessment> {\r\n    try {\r\n      const assessment = await this.assessmentRepository.findOne({\r\n        where: { id: assessmentId },\r\n        relations: ['framework'],\r\n      });\r\n\r\n      if (!assessment) {\r\n        throw new NotFoundException('Assessment not found');\r\n      }\r\n\r\n      assessment.start(userId);\r\n      const updatedAssessment = await this.assessmentRepository.save(assessment);\r\n\r\n      // Initialize assessment with framework controls\r\n      await this.initializeAssessmentControls(assessment);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'start',\r\n        'compliance_assessment',\r\n        assessmentId,\r\n        {\r\n          frameworkId: assessment.frameworkId,\r\n          assessmentType: assessment.assessmentType,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Compliance assessment started', {\r\n        assessmentId,\r\n        userId,\r\n      });\r\n\r\n      return updatedAssessment;\r\n    } catch (error) {\r\n      this.logger.error('Failed to start compliance assessment', {\r\n        assessmentId,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Complete compliance assessment\r\n   * @param assessmentId Assessment ID\r\n   * @param results Assessment results\r\n   * @param userId User completing the assessment\r\n   * @returns Updated assessment\r\n   */\r\n  async completeAssessment(\r\n    assessmentId: string,\r\n    results: any,\r\n    userId: string,\r\n  ): Promise<ComplianceAssessment> {\r\n    try {\r\n      const assessment = await this.assessmentRepository.findOne({\r\n        where: { id: assessmentId },\r\n        relations: ['framework'],\r\n      });\r\n\r\n      if (!assessment) {\r\n        throw new NotFoundException('Assessment not found');\r\n      }\r\n\r\n      assessment.complete(userId, results);\r\n      const updatedAssessment = await this.assessmentRepository.save(assessment);\r\n\r\n      // Generate violations for non-compliant controls\r\n      await this.generateViolationsFromAssessment(assessment);\r\n\r\n      // Send notifications\r\n      await this.notificationService.sendComplianceNotification(\r\n        assessment,\r\n        'assessment_completed',\r\n      );\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'complete',\r\n        'compliance_assessment',\r\n        assessmentId,\r\n        {\r\n          frameworkId: assessment.frameworkId,\r\n          complianceScore: results.overallScore,\r\n          complianceStatus: results.overallStatus,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Compliance assessment completed', {\r\n        assessmentId,\r\n        complianceScore: results.overallScore,\r\n        userId,\r\n      });\r\n\r\n      return updatedAssessment;\r\n    } catch (error) {\r\n      this.logger.error('Failed to complete compliance assessment', {\r\n        assessmentId,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get compliance status for framework\r\n   * @param frameworkId Framework ID\r\n   * @returns Compliance status\r\n   */\r\n  async getFrameworkComplianceStatus(frameworkId: string): Promise<any> {\r\n    try {\r\n      const framework = await this.frameworkRepository.findOne({\r\n        where: { id: frameworkId },\r\n        relations: ['assessments', 'policies'],\r\n      });\r\n\r\n      if (!framework) {\r\n        throw new NotFoundException('Framework not found');\r\n      }\r\n\r\n      const latestAssessment = await this.getLatestAssessment(frameworkId);\r\n      const openViolations = await this.getFrameworkViolations(frameworkId, true);\r\n      const policies = await this.getFrameworkPolicies(frameworkId);\r\n\r\n      const status = {\r\n        framework: {\r\n          id: framework.id,\r\n          name: framework.name,\r\n          type: framework.type,\r\n          version: framework.version,\r\n          totalControls: framework.totalControls,\r\n        },\r\n        latestAssessment: latestAssessment ? {\r\n          id: latestAssessment.id,\r\n          assessmentDate: latestAssessment.assessmentDate,\r\n          status: latestAssessment.status,\r\n          compliancePercentage: latestAssessment.compliancePercentage,\r\n          overallStatus: latestAssessment.results?.overallStatus,\r\n          criticalFindings: latestAssessment.criticalFindingsCount,\r\n          highFindings: latestAssessment.highFindingsCount,\r\n        } : null,\r\n        violations: {\r\n          total: openViolations.length,\r\n          critical: openViolations.filter(v => v.severity === 'critical').length,\r\n          high: openViolations.filter(v => v.severity === 'high').length,\r\n          medium: openViolations.filter(v => v.severity === 'medium').length,\r\n          low: openViolations.filter(v => v.severity === 'low').length,\r\n        },\r\n        policies: {\r\n          total: policies.length,\r\n          active: policies.filter(p => p.isActive).length,\r\n          expired: policies.filter(p => p.isExpired).length,\r\n          dueForReview: policies.filter(p => p.isDueForReview).length,\r\n        },\r\n        complianceScore: await this.calculateFrameworkComplianceScore(frameworkId),\r\n        riskLevel: await this.calculateFrameworkRiskLevel(frameworkId),\r\n        nextAssessmentDate: latestAssessment?.nextAssessmentDate,\r\n        isOverdue: latestAssessment?.isOverdue || false,\r\n      };\r\n\r\n      return status;\r\n    } catch (error) {\r\n      this.logger.error('Failed to get framework compliance status', {\r\n        frameworkId,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get compliance trends\r\n   * @param days Number of days to look back\r\n   * @returns Compliance trends data\r\n   */\r\n  async getComplianceTrends(days: number = 90): Promise<any> {\r\n    try {\r\n      const endDate = new Date();\r\n      const startDate = new Date();\r\n      startDate.setDate(startDate.getDate() - days);\r\n\r\n      const assessments = await this.assessmentRepository.find({\r\n        where: {\r\n          assessmentDate: Between(startDate, endDate),\r\n          status: 'completed',\r\n        },\r\n        relations: ['framework'],\r\n        order: { assessmentDate: 'ASC' },\r\n      });\r\n\r\n      const violations = await this.violationRepository.find({\r\n        where: {\r\n          detectedAt: Between(startDate, endDate),\r\n        },\r\n        order: { detectedAt: 'ASC' },\r\n      });\r\n\r\n      // Group data by week\r\n      const trends = this.groupTrendsByWeek(assessments, violations, startDate, endDate);\r\n\r\n      return {\r\n        period: { startDate, endDate, days },\r\n        trends,\r\n        summary: {\r\n          totalAssessments: assessments.length,\r\n          totalViolations: violations.length,\r\n          averageComplianceScore: this.calculateAverageComplianceScore(assessments),\r\n          trendDirection: this.calculateTrendDirection(trends),\r\n        },\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to get compliance trends', {\r\n        days,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private async getActiveFrameworksCount(): Promise<number> {\r\n    return await this.frameworkRepository.count({ where: { isActive: true } });\r\n  }\r\n\r\n  private async getTotalAssessmentsCount(): Promise<number> {\r\n    return await this.assessmentRepository.count();\r\n  }\r\n\r\n  private async getOpenViolationsCount(): Promise<number> {\r\n    return await this.violationRepository.count({\r\n      where: { status: In(['open', 'investigating', 'remediation_planned', 'remediation_in_progress']) },\r\n    });\r\n  }\r\n\r\n  private async getCriticalViolationsCount(): Promise<number> {\r\n    return await this.violationRepository.count({\r\n      where: { \r\n        severity: 'critical',\r\n        status: In(['open', 'investigating', 'remediation_planned', 'remediation_in_progress']),\r\n      },\r\n    });\r\n  }\r\n\r\n  private async getComplianceByFramework(): Promise<any[]> {\r\n    const frameworks = await this.frameworkRepository.find({\r\n      where: { isActive: true },\r\n      relations: ['assessments'],\r\n    });\r\n\r\n    const complianceData = [];\r\n\r\n    for (const framework of frameworks) {\r\n      const latestAssessment = await this.getLatestAssessment(framework.id);\r\n      const complianceScore = await this.calculateFrameworkComplianceScore(framework.id);\r\n\r\n      complianceData.push({\r\n        frameworkId: framework.id,\r\n        frameworkName: framework.name,\r\n        frameworkType: framework.type,\r\n        complianceScore,\r\n        lastAssessmentDate: latestAssessment?.assessmentDate,\r\n        status: latestAssessment?.results?.overallStatus || 'unknown',\r\n      });\r\n    }\r\n\r\n    return complianceData;\r\n  }\r\n\r\n  private async getRecentViolations(days: number): Promise<any[]> {\r\n    const startDate = new Date();\r\n    startDate.setDate(startDate.getDate() - days);\r\n\r\n    const violations = await this.violationRepository.find({\r\n      where: { detectedAt: Between(startDate, new Date()) },\r\n      order: { detectedAt: 'DESC' },\r\n      take: 10,\r\n    });\r\n\r\n    return violations.map(v => v.generateSummary());\r\n  }\r\n\r\n  private async getUpcomingAssessments(days: number): Promise<any[]> {\r\n    const endDate = new Date();\r\n    endDate.setDate(endDate.getDate() + days);\r\n\r\n    const assessments = await this.assessmentRepository.find({\r\n      where: { \r\n        nextAssessmentDate: Between(new Date(), endDate),\r\n        status: In(['planned', 'in_progress']),\r\n      },\r\n      relations: ['framework'],\r\n      order: { nextAssessmentDate: 'ASC' },\r\n    });\r\n\r\n    return assessments.map(a => a.generateSummary());\r\n  }\r\n\r\n  private async calculateOverallComplianceScore(): Promise<number> {\r\n    const frameworks = await this.frameworkRepository.find({ where: { isActive: true } });\r\n    \r\n    if (frameworks.length === 0) return 0;\r\n\r\n    let totalScore = 0;\r\n    for (const framework of frameworks) {\r\n      const score = await this.calculateFrameworkComplianceScore(framework.id);\r\n      totalScore += score;\r\n    }\r\n\r\n    return Math.round(totalScore / frameworks.length);\r\n  }\r\n\r\n  private async calculateRiskMetrics(): Promise<any> {\r\n    const criticalViolations = await this.getCriticalViolationsCount();\r\n    const highViolations = await this.violationRepository.count({\r\n      where: { \r\n        severity: 'high',\r\n        status: In(['open', 'investigating', 'remediation_planned', 'remediation_in_progress']),\r\n      },\r\n    });\r\n\r\n    const overallRiskScore = (criticalViolations * 10) + (highViolations * 5);\r\n    \r\n    let riskLevel = 'low';\r\n    if (overallRiskScore >= 50) riskLevel = 'critical';\r\n    else if (overallRiskScore >= 25) riskLevel = 'high';\r\n    else if (overallRiskScore >= 10) riskLevel = 'medium';\r\n\r\n    return {\r\n      overallRiskScore,\r\n      riskLevel,\r\n      criticalViolations,\r\n      highViolations,\r\n    };\r\n  }\r\n\r\n  private async getLatestAssessment(frameworkId: string): Promise<ComplianceAssessment | null> {\r\n    return await this.assessmentRepository.findOne({\r\n      where: { frameworkId },\r\n      order: { assessmentDate: 'DESC' },\r\n    });\r\n  }\r\n\r\n  private isAssessmentDue(assessment: ComplianceAssessment | null, framework: ComplianceFramework): boolean {\r\n    if (!assessment) return true;\r\n    if (!assessment.nextAssessmentDate) return false;\r\n    return new Date() >= assessment.nextAssessmentDate;\r\n  }\r\n\r\n  private async scheduleAssessment(framework: ComplianceFramework): Promise<void> {\r\n    // Implementation for scheduling new assessment\r\n    this.logger.log('Assessment due for framework', {\r\n      frameworkId: framework.id,\r\n      frameworkName: framework.name,\r\n    });\r\n  }\r\n\r\n  private async monitorContinuousControls(framework: ComplianceFramework): Promise<void> {\r\n    // Implementation for monitoring continuous controls\r\n    const continuousControls = framework.automatableControls;\r\n    \r\n    for (const control of continuousControls) {\r\n      await this.policyEnforcementService.evaluateControl(framework.id, control.id);\r\n    }\r\n  }\r\n\r\n  private async checkPolicyCompliance(framework: ComplianceFramework): Promise<void> {\r\n    const policies = await this.policyRepository.find({\r\n      where: { frameworkId: framework.id, isActive: true },\r\n    });\r\n\r\n    for (const policy of policies) {\r\n      await this.violationDetectionService.checkPolicyCompliance(policy.id);\r\n    }\r\n  }\r\n\r\n  private async updateComplianceMetrics(framework: ComplianceFramework): Promise<void> {\r\n    // Implementation for updating compliance metrics\r\n    this.logger.debug('Updating compliance metrics', {\r\n      frameworkId: framework.id,\r\n    });\r\n  }\r\n\r\n  private async checkOverdueAssessments(): Promise<void> {\r\n    const overdueAssessments = await this.assessmentRepository.find({\r\n      where: { \r\n        nextAssessmentDate: Between(new Date('1900-01-01'), new Date()),\r\n        status: In(['planned', 'in_progress']),\r\n      },\r\n      relations: ['framework'],\r\n    });\r\n\r\n    for (const assessment of overdueAssessments) {\r\n      await this.notificationService.sendAssessmentOverdueNotification(assessment);\r\n    }\r\n  }\r\n\r\n  private async checkPolicyViolations(): Promise<void> {\r\n    await this.violationDetectionService.scanForViolations();\r\n  }\r\n\r\n  private async initializeAssessmentControls(assessment: ComplianceAssessment): Promise<void> {\r\n    // Initialize assessment with framework controls\r\n    if (!assessment.results) {\r\n      assessment.results = { domainResults: [] };\r\n    }\r\n\r\n    const framework = assessment.framework;\r\n    for (const domain of framework.configuration.domains) {\r\n      const domainResult = {\r\n        domainId: domain.id,\r\n        domainName: domain.name,\r\n        score: 0,\r\n        status: 'non_compliant' as const,\r\n        controlResults: domain.controls.map(control => ({\r\n          controlId: control.id,\r\n          controlName: control.name,\r\n          status: 'not_tested' as const,\r\n        })),\r\n      };\r\n      \r\n      assessment.results.domainResults.push(domainResult);\r\n    }\r\n\r\n    await this.assessmentRepository.save(assessment);\r\n  }\r\n\r\n  private async generateViolationsFromAssessment(assessment: ComplianceAssessment): Promise<void> {\r\n    if (!assessment.results?.domainResults) return;\r\n\r\n    for (const domain of assessment.results.domainResults) {\r\n      for (const control of domain.controlResults) {\r\n        if (control.status === 'non_compliant') {\r\n          await this.createViolationFromControl(assessment, domain, control);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  private async createViolationFromControl(assessment: ComplianceAssessment, domain: any, control: any): Promise<void> {\r\n    const violation = this.violationRepository.create({\r\n      title: `Non-compliance with ${control.controlName}`,\r\n      description: `Control ${control.controlId} in domain ${domain.domainName} is non-compliant`,\r\n      violationType: 'compliance_monitoring',\r\n      severity: control.riskLevel || 'medium',\r\n      detectedAt: new Date(),\r\n      assessmentId: assessment.id,\r\n      details: {\r\n        violatedControls: [control.controlId],\r\n        evidence: control.evidence || [],\r\n        detectionMethod: 'compliance_assessment',\r\n        compliance: {\r\n          frameworks: [assessment.frameworkId],\r\n          controls: [control.controlId],\r\n          requirements: control.findings || [],\r\n        },\r\n      },\r\n      detectedBy: assessment.updatedBy || assessment.createdBy,\r\n      tags: ['compliance', 'assessment', assessment.framework?.type || 'unknown'],\r\n    });\r\n\r\n    await this.violationRepository.save(violation);\r\n  }\r\n\r\n  private async getFrameworkViolations(frameworkId: string, openOnly: boolean = false): Promise<PolicyViolation[]> {\r\n    const whereConditions: any = {};\r\n    \r\n    if (openOnly) {\r\n      whereConditions.status = In(['open', 'investigating', 'remediation_planned', 'remediation_in_progress']);\r\n    }\r\n\r\n    // This would need to be implemented based on how violations are linked to frameworks\r\n    return await this.violationRepository.find({ where: whereConditions });\r\n  }\r\n\r\n  private async getFrameworkPolicies(frameworkId: string): Promise<PolicyDefinition[]> {\r\n    return await this.policyRepository.find({ where: { frameworkId } });\r\n  }\r\n\r\n  private async calculateFrameworkComplianceScore(frameworkId: string): Promise<number> {\r\n    const latestAssessment = await this.getLatestAssessment(frameworkId);\r\n    return latestAssessment?.compliancePercentage || 0;\r\n  }\r\n\r\n  private async calculateFrameworkRiskLevel(frameworkId: string): Promise<string> {\r\n    const violations = await this.getFrameworkViolations(frameworkId, true);\r\n    const criticalCount = violations.filter(v => v.severity === 'critical').length;\r\n    const highCount = violations.filter(v => v.severity === 'high').length;\r\n\r\n    if (criticalCount > 0) return 'critical';\r\n    if (highCount > 2) return 'high';\r\n    if (highCount > 0 || violations.length > 5) return 'medium';\r\n    return 'low';\r\n  }\r\n\r\n  private groupTrendsByWeek(assessments: ComplianceAssessment[], violations: PolicyViolation[], startDate: Date, endDate: Date): any[] {\r\n    // Implementation for grouping trends by week\r\n    const weeks = [];\r\n    const currentDate = new Date(startDate);\r\n    \r\n    while (currentDate <= endDate) {\r\n      const weekStart = new Date(currentDate);\r\n      const weekEnd = new Date(currentDate);\r\n      weekEnd.setDate(weekEnd.getDate() + 6);\r\n      \r\n      const weekAssessments = assessments.filter(a => \r\n        a.assessmentDate >= weekStart && a.assessmentDate <= weekEnd\r\n      );\r\n      \r\n      const weekViolations = violations.filter(v => \r\n        v.detectedAt >= weekStart && v.detectedAt <= weekEnd\r\n      );\r\n      \r\n      weeks.push({\r\n        weekStart,\r\n        weekEnd,\r\n        assessments: weekAssessments.length,\r\n        violations: weekViolations.length,\r\n        averageComplianceScore: this.calculateAverageComplianceScore(weekAssessments),\r\n      });\r\n      \r\n      currentDate.setDate(currentDate.getDate() + 7);\r\n    }\r\n    \r\n    return weeks;\r\n  }\r\n\r\n  private calculateAverageComplianceScore(assessments: ComplianceAssessment[]): number {\r\n    if (assessments.length === 0) return 0;\r\n    \r\n    const totalScore = assessments.reduce((sum, assessment) => \r\n      sum + (assessment.compliancePercentage || 0), 0\r\n    );\r\n    \r\n    return Math.round(totalScore / assessments.length);\r\n  }\r\n\r\n  private calculateTrendDirection(trends: any[]): 'improving' | 'stable' | 'declining' {\r\n    if (trends.length < 2) return 'stable';\r\n    \r\n    const firstHalf = trends.slice(0, Math.floor(trends.length / 2));\r\n    const secondHalf = trends.slice(Math.floor(trends.length / 2));\r\n    \r\n    const firstAvg = this.calculateAverageComplianceScore(firstHalf);\r\n    const secondAvg = this.calculateAverageComplianceScore(secondHalf);\r\n    \r\n    if (secondAvg > firstAvg + 5) return 'improving';\r\n    if (secondAvg < firstAvg - 5) return 'declining';\r\n    return 'stable';\r\n  }\r\n}\r\n"], "version": 3}