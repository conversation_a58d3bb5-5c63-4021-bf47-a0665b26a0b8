{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\forbidden.exception.spec.ts", "mappings": ";;AAAA,8EAA0E;AAE1E,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,SAAS,GAAG,IAAI,wCAAkB,EAAE,CAAC;YAE3C,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,wCAAkB,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACvE,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,SAAS,GAAG,IAAI,wCAAkB,CAAC,0BAA0B,EAAE;gBACnE,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,QAAQ;gBAChB,mBAAmB,EAAE,CAAC,aAAa,CAAC;gBACpC,eAAe,EAAE,CAAC,WAAW,CAAC;gBAC9B,aAAa,EAAE,qBAAqB;aACrC,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YAC/D,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YACzD,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACvC,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;gBAC9D,MAAM,mBAAmB,GAAG,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;gBAC1D,MAAM,eAAe,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBAErD,MAAM,SAAS,GAAG,wCAAkB,CAAC,uBAAuB,CAC1D,MAAM,EACN,QAAQ,EACR,mBAAmB,EACnB,eAAe,CAChB,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,2EAA2E,CAC5E,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACxC,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;gBACnE,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;gBAC3D,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC,CAAC;gBACjF,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,SAAS,GAAG,wCAAkB,CAAC,uBAAuB,CAC1D,UAAU,EACV,MAAM,EACN,CAAC,UAAU,CAAC,CACb,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;gBACxD,MAAM,aAAa,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;gBAC7C,MAAM,SAAS,GAAG,CAAC,MAAM,CAAC,CAAC;gBAE3B,MAAM,SAAS,GAAG,wCAAkB,CAAC,iBAAiB,CACpD,QAAQ,EACR,WAAW,EACX,aAAa,EACb,SAAS,CACV,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,sEAAsE,CACvE,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC1C,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC3C,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;gBACvD,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAC/C,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;gBACvD,MAAM,CAAC,GAAG,EAAE;oBACV,wCAAkB,CAAC,iBAAiB,CAClC,QAAQ,EACR,WAAW,EACX,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,EAAE,MAAM,CAAC,CAClB,CAAC;gBACJ,CAAC,CAAC,CAAC,OAAO,CAAC,uEAAuE,CAAC,CAAC;YACtF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;gBACpE,MAAM,SAAS,GAAG,wCAAkB,CAAC,oBAAoB,CAAC,UAAU,EAAE,SAAS,EAAE;oBAC/E,MAAM,EAAE,kBAAkB;iBAC3B,CAAC,CAAC;gBAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBACtE,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC5C,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACrD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;gBACvE,MAAM,SAAS,GAAG,wCAAkB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;gBAEvE,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAC7D,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAChC,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;gBACtE,MAAM,SAAS,GAAG,wCAAkB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE;oBACtE,MAAM,EAAE,aAAa;oBACrB,cAAc,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;iBACnC,CAAC,CAAC;gBAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBACzE,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACxC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACrD,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;gBACzE,MAAM,SAAS,GAAG,wCAAkB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAEhE,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;gBACjE,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;gBAC3D,MAAM,SAAS,GAAG,wCAAkB,CAAC,iBAAiB,CACpD,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,CACX,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;gBAC/E,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC5C,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACrD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACnD,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEhD,MAAM,aAAa,GAAG,SAAS,CAAC,gBAAgB,EAAE,CAAC;gBACnD,MAAM,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC;oBAC5B,MAAM,EAAE,UAAU;oBAClB,OAAO,EAAE,UAAU;iBACpB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;YACxC,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;gBAChE,MAAM,SAAS,GAAG,wCAAkB,CAAC,wBAAwB,CAC3D,MAAM,EACN,UAAU,EACV,YAAY,EACZ,YAAY,CACb,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;gBAC/F,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACtD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC1D,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC9D,MAAM,CAAC,SAAS,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE1D,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;gBAC7C,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC;oBACzB,YAAY,EAAE,YAAY;oBAC1B,gBAAgB,EAAE,YAAY;iBAC/B,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;gBACnE,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACrD,MAAM,SAAS,GAAG,wCAAkB,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE;oBACtE,gBAAgB,EAAE,OAAO;oBACzB,cAAc,EAAE,OAAO;oBACvB,WAAW;iBACZ,CAAC,CAAC;gBAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;gBACzF,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC1C,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACxC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEhD,MAAM,QAAQ,GAAG,SAAS,CAAC,sBAAsB,EAAE,CAAC;gBACpD,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;oBACvB,gBAAgB,EAAE,OAAO;oBACzB,cAAc,EAAE,OAAO;oBACvB,WAAW;iBACZ,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,SAAS,GAAG,wCAAkB,CAAC,uBAAuB,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;YAEhG,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,SAAS,GAAG,wCAAkB,CAAC,iBAAiB,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;YAEnG,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,SAAS,GAAG,wCAAkB,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAE3F,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,SAAS,GAAG,wCAAkB,CAAC,uBAAuB,CAC1D,MAAM,EACN,QAAQ,EACR,CAAC,aAAa,EAAE,YAAY,CAAC,EAC7B,CAAC,WAAW,EAAE,aAAa,CAAC,CAC7B,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,SAAS,GAAG,IAAI,wCAAkB,EAAE,CAAC;YAC3C,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,SAAS,GAAG,wCAAkB,CAAC,iBAAiB,CACpD,QAAQ,EACR,WAAW,EACX,CAAC,OAAO,EAAE,WAAW,CAAC,EACtB,CAAC,MAAM,CAAC,CACT,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,SAAS,GAAG,IAAI,wCAAkB,CAAC,MAAM,EAAE;gBAC/C,aAAa,EAAE,CAAC,OAAO,CAAC;aACzB,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,SAAS,GAAG,wCAAkB,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAC3F,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,SAAS,GAAG,wCAAkB,CAAC,wBAAwB,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YACxG,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,SAAS,GAAG,wCAAkB,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACxE,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACjG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,MAAM,SAAS,GAAG,wCAAkB,CAAC,uBAAuB,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;YAChG,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;QAC7G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,SAAS,GAAG,wCAAkB,CAAC,iBAAiB,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;YACnG,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,SAAS,GAAG,IAAI,wCAAkB,EAAE,CAAC;YAC3C,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,SAAS,GAAG,wCAAkB,CAAC,uBAAuB,CAC1D,MAAM,EACN,QAAQ,EACR,CAAC,aAAa,CAAC,EACf,CAAC,WAAW,CAAC,CACd,CAAC;YAEF,MAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;YAE3C,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBACvB,KAAK,EAAE,iEAAiE;gBACxE,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE;oBACP,QAAQ,EAAE,MAAM;oBAChB,MAAM,EAAE,QAAQ;oBAChB,MAAM,EAAE,yBAAyB;oBACjC,kBAAkB,EAAE,CAAC,aAAa,CAAC;oBACnC,YAAY,EAAE,EAAE;oBAChB,cAAc,EAAE,EAAE;iBACnB;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,SAAS,GAAG,wCAAkB,CAAC,uBAAuB,CAC1D,MAAM,EACN,QAAQ,EACR,CAAC,aAAa,EAAE,YAAY,CAAC,EAC7B,CAAC,WAAW,CAAC,CACd,CAAC;YAEF,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;gBACzB,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,eAAe;gBACzB,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,QAAQ;gBAChB,mBAAmB,EAAE,CAAC,aAAa,EAAE,YAAY,CAAC;gBAClD,eAAe,EAAE,CAAC,WAAW,CAAC;gBAC9B,kBAAkB,EAAE,CAAC,aAAa,EAAE,YAAY,CAAC;gBACjD,YAAY,EAAE,EAAE;gBAChB,MAAM,EAAE,yBAAyB;gBACjC,iBAAiB,EAAE,IAAI;gBACvB,WAAW,EAAE,KAAK;gBAClB,gBAAgB,EAAE,KAAK;gBACvB,0BAA0B,EAAE,KAAK;gBACjC,gBAAgB,EAAE,KAAK;gBACvB,aAAa,EAAE,IAAI;gBACnB,UAAU,EAAE,IAAI;gBAChB,mBAAmB,EAAE,IAAI;gBACzB,cAAc,EAAE,EAAE;aACnB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,SAAS,GAAG,IAAI,wCAAkB,EAAE,CAAC;YAE3C,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,SAAS,GAAG,IAAI,wCAAkB,EAAE,CAAC;YAE3C,MAAM,CAAC,SAAS,YAAY,wCAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,YAAY,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,OAAO,GAAG,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC;YACtE,MAAM,aAAa,GAAG,iBAAiB,CAAC;YAExC,MAAM,SAAS,GAAG,IAAI,wCAAkB,CAAC,cAAc,EAAE;gBACvD,OAAO;gBACP,aAAa;aACd,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\forbidden.exception.spec.ts"], "sourcesContent": ["import { ForbiddenException } from '../../exceptions/forbidden.exception';\r\n\r\ndescribe('ForbiddenException', () => {\r\n  describe('constructor', () => {\r\n    it('should create exception with default message', () => {\r\n      const exception = new ForbiddenException();\r\n\r\n      expect(exception).toBeInstanceOf(ForbiddenException);\r\n      expect(exception.message).toBe('Access to this resource is forbidden');\r\n      expect(exception.code).toBe('FORBIDDEN');\r\n      expect(exception.severity).toBe('medium');\r\n      expect(exception.category).toBe('authorization');\r\n    });\r\n\r\n    it('should create exception with custom message and options', () => {\r\n      const exception = new ForbiddenException('Custom forbidden message', {\r\n        resource: 'User',\r\n        action: 'delete',\r\n        requiredPermissions: ['user:delete'],\r\n        userPermissions: ['user:read'],\r\n        correlationId: 'test-correlation-id',\r\n      });\r\n\r\n      expect(exception.message).toBe('Custom forbidden message');\r\n      expect(exception.resource).toBe('User');\r\n      expect(exception.action).toBe('delete');\r\n      expect(exception.requiredPermissions).toEqual(['user:delete']);\r\n      expect(exception.userPermissions).toEqual(['user:read']);\r\n      expect(exception.correlationId).toBe('test-correlation-id');\r\n    });\r\n  });\r\n\r\n  describe('static factory methods', () => {\r\n    describe('insufficientPermissions', () => {\r\n      it('should create exception for insufficient permissions', () => {\r\n        const requiredPermissions = ['user:delete', 'user:admin'];\r\n        const userPermissions = ['user:read', 'user:update'];\r\n\r\n        const exception = ForbiddenException.insufficientPermissions(\r\n          'User',\r\n          'delete',\r\n          requiredPermissions,\r\n          userPermissions\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          'Insufficient permissions to delete User. Missing: user:delete, user:admin'\r\n        );\r\n        expect(exception.resource).toBe('User');\r\n        expect(exception.action).toBe('delete');\r\n        expect(exception.requiredPermissions).toEqual(requiredPermissions);\r\n        expect(exception.userPermissions).toEqual(userPermissions);\r\n        expect(exception.getMissingPermissions()).toEqual(['user:delete', 'user:admin']);\r\n        expect(exception.isPermissionBased()).toBe(true);\r\n      });\r\n\r\n      it('should handle empty user permissions', () => {\r\n        const exception = ForbiddenException.insufficientPermissions(\r\n          'Document',\r\n          'read',\r\n          ['doc:read']\r\n        );\r\n\r\n        expect(exception.getMissingPermissions()).toEqual(['doc:read']);\r\n      });\r\n    });\r\n\r\n    describe('insufficientRoles', () => {\r\n      it('should create exception for insufficient roles', () => {\r\n        const requiredRoles = ['admin', 'moderator'];\r\n        const userRoles = ['user'];\r\n\r\n        const exception = ForbiddenException.insufficientRoles(\r\n          'System',\r\n          'configure',\r\n          requiredRoles,\r\n          userRoles\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          'Insufficient roles to configure System. Required: admin or moderator'\r\n        );\r\n        expect(exception.resource).toBe('System');\r\n        expect(exception.action).toBe('configure');\r\n        expect(exception.requiredRoles).toEqual(requiredRoles);\r\n        expect(exception.userRoles).toEqual(userRoles);\r\n        expect(exception.isRoleBased()).toBe(true);\r\n      });\r\n\r\n      it('should throw error if user has required roles', () => {\r\n        expect(() => {\r\n          ForbiddenException.insufficientRoles(\r\n            'System',\r\n            'configure',\r\n            ['admin'],\r\n            ['admin', 'user']\r\n          );\r\n        }).toThrow('User has required roles - this should not create a ForbiddenException');\r\n      });\r\n    });\r\n\r\n    describe('resourceAccessDenied', () => {\r\n      it('should create exception for resource access denial with ID', () => {\r\n        const exception = ForbiddenException.resourceAccessDenied('Document', 'doc-123', {\r\n          reason: 'private_document',\r\n        });\r\n\r\n        expect(exception.message).toBe(\"Access denied to Document 'doc-123'\");\r\n        expect(exception.resource).toBe('Document');\r\n        expect(exception.context.resourceId).toBe('doc-123');\r\n        expect(exception.context.reason).toBe('private_document');\r\n      });\r\n\r\n      it('should create exception for resource access denial without ID', () => {\r\n        const exception = ForbiddenException.resourceAccessDenied('Documents');\r\n\r\n        expect(exception.message).toBe('Access denied to Documents');\r\n        expect(exception.resource).toBe('Documents');\r\n      });\r\n    });\r\n\r\n    describe('actionNotAllowed', () => {\r\n      it('should create exception for action not allowed with resource', () => {\r\n        const exception = ForbiddenException.actionNotAllowed('delete', 'User', {\r\n          reason: 'system_user',\r\n          allowedActions: ['read', 'update'],\r\n        });\r\n\r\n        expect(exception.message).toBe(\"Action 'delete' is not allowed on User\");\r\n        expect(exception.action).toBe('delete');\r\n        expect(exception.resource).toBe('User');\r\n        expect(exception.context.reason).toBe('system_user');\r\n        expect(exception.getAllowedActions()).toEqual(['read', 'update']);\r\n      });\r\n\r\n      it('should create exception for action not allowed without resource', () => {\r\n        const exception = ForbiddenException.actionNotAllowed('export');\r\n\r\n        expect(exception.message).toBe(\"Action 'export' is not allowed\");\r\n        expect(exception.action).toBe('export');\r\n      });\r\n    });\r\n\r\n    describe('ownershipRequired', () => {\r\n      it('should create exception for ownership requirement', () => {\r\n        const exception = ForbiddenException.ownershipRequired(\r\n          'Document',\r\n          'doc-123',\r\n          'user-456',\r\n          'user-789'\r\n        );\r\n\r\n        expect(exception.message).toBe(\"Only the owner can access Document 'doc-123'\");\r\n        expect(exception.resource).toBe('Document');\r\n        expect(exception.context.resourceId).toBe('doc-123');\r\n        expect(exception.context.userId).toBe('user-456');\r\n        expect(exception.context.ownerId).toBe('user-789');\r\n        expect(exception.isOwnershipBased()).toBe(true);\r\n\r\n        const ownershipInfo = exception.getOwnershipInfo();\r\n        expect(ownershipInfo).toEqual({\r\n          userId: 'user-456',\r\n          ownerId: 'user-789',\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('tenantIsolationViolation', () => {\r\n      it('should create exception for tenant isolation violation', () => {\r\n        const exception = ForbiddenException.tenantIsolationViolation(\r\n          'Data',\r\n          'data-123',\r\n          'tenant-456',\r\n          'tenant-789'\r\n        );\r\n\r\n        expect(exception.message).toBe(\"Access denied: Data 'data-123' belongs to a different tenant\");\r\n        expect(exception.resource).toBe('Data');\r\n        expect(exception.context.resourceId).toBe('data-123');\r\n        expect(exception.context.userTenantId).toBe('tenant-456');\r\n        expect(exception.context.resourceTenantId).toBe('tenant-789');\r\n        expect(exception.isTenantIsolationViolation()).toBe(true);\r\n\r\n        const tenantInfo = exception.getTenantInfo();\r\n        expect(tenantInfo).toEqual({\r\n          userTenantId: 'tenant-456',\r\n          resourceTenantId: 'tenant-789',\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('timeRestricted', () => {\r\n      it('should create exception for time-based access restriction', () => {\r\n        const currentTime = new Date('2023-01-01T22:00:00Z');\r\n        const exception = ForbiddenException.timeRestricted('System', 'backup', {\r\n          allowedTimeStart: '09:00',\r\n          allowedTimeEnd: '17:00',\r\n          currentTime,\r\n        });\r\n\r\n        expect(exception.message).toBe('Access to System for backup is restricted at this time');\r\n        expect(exception.resource).toBe('System');\r\n        expect(exception.action).toBe('backup');\r\n        expect(exception.isTimeRestricted()).toBe(true);\r\n\r\n        const timeInfo = exception.getTimeRestrictionInfo();\r\n        expect(timeInfo).toEqual({\r\n          allowedTimeStart: '09:00',\r\n          allowedTimeEnd: '17:00',\r\n          currentTime,\r\n        });\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('type checking methods', () => {\r\n    it('should correctly identify permission-based restrictions', () => {\r\n      const exception = ForbiddenException.insufficientPermissions('User', 'delete', ['user:delete']);\r\n      \r\n      expect(exception.isPermissionBased()).toBe(true);\r\n      expect(exception.isRoleBased()).toBe(false);\r\n      expect(exception.isOwnershipBased()).toBe(false);\r\n      expect(exception.isTenantIsolationViolation()).toBe(false);\r\n      expect(exception.isTimeRestricted()).toBe(false);\r\n    });\r\n\r\n    it('should correctly identify role-based restrictions', () => {\r\n      const exception = ForbiddenException.insufficientRoles('System', 'configure', ['admin'], ['user']);\r\n      \r\n      expect(exception.isPermissionBased()).toBe(false);\r\n      expect(exception.isRoleBased()).toBe(true);\r\n      expect(exception.isOwnershipBased()).toBe(false);\r\n      expect(exception.isTenantIsolationViolation()).toBe(false);\r\n      expect(exception.isTimeRestricted()).toBe(false);\r\n    });\r\n\r\n    it('should correctly identify ownership-based restrictions', () => {\r\n      const exception = ForbiddenException.ownershipRequired('Doc', 'doc-1', 'user-1', 'user-2');\r\n      \r\n      expect(exception.isPermissionBased()).toBe(false);\r\n      expect(exception.isRoleBased()).toBe(false);\r\n      expect(exception.isOwnershipBased()).toBe(true);\r\n      expect(exception.isTenantIsolationViolation()).toBe(false);\r\n      expect(exception.isTimeRestricted()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('getMissingPermissions', () => {\r\n    it('should return missing permissions', () => {\r\n      const exception = ForbiddenException.insufficientPermissions(\r\n        'User',\r\n        'delete',\r\n        ['user:delete', 'user:admin'],\r\n        ['user:read', 'user:update']\r\n      );\r\n\r\n      expect(exception.getMissingPermissions()).toEqual(['user:delete', 'user:admin']);\r\n    });\r\n\r\n    it('should return empty array when no permissions specified', () => {\r\n      const exception = new ForbiddenException();\r\n      expect(exception.getMissingPermissions()).toEqual([]);\r\n    });\r\n  });\r\n\r\n  describe('getMissingRoles', () => {\r\n    it('should return missing roles', () => {\r\n      const exception = ForbiddenException.insufficientRoles(\r\n        'System',\r\n        'configure',\r\n        ['admin', 'moderator'],\r\n        ['user']\r\n      );\r\n\r\n      expect(exception.getMissingRoles()).toEqual(['admin', 'moderator']);\r\n    });\r\n\r\n    it('should return required roles when no user roles specified', () => {\r\n      const exception = new ForbiddenException('Test', {\r\n        requiredRoles: ['admin'],\r\n      });\r\n\r\n      expect(exception.getMissingRoles()).toEqual(['admin']);\r\n    });\r\n  });\r\n\r\n  describe('getUserMessage', () => {\r\n    it('should return user-friendly message for ownership requirement', () => {\r\n      const exception = ForbiddenException.ownershipRequired('Doc', 'doc-1', 'user-1', 'user-2');\r\n      expect(exception.getUserMessage()).toBe('You can only access resources that you own');\r\n    });\r\n\r\n    it('should return user-friendly message for tenant isolation', () => {\r\n      const exception = ForbiddenException.tenantIsolationViolation('Data', 'data-1', 'tenant-1', 'tenant-2');\r\n      expect(exception.getUserMessage()).toBe('You can only access resources within your organization');\r\n    });\r\n\r\n    it('should return user-friendly message for time restriction', () => {\r\n      const exception = ForbiddenException.timeRestricted('System', 'backup');\r\n      expect(exception.getUserMessage()).toBe('Access to this resource is not allowed at this time');\r\n    });\r\n\r\n    it('should return user-friendly message for permission-based restriction', () => {\r\n      const exception = ForbiddenException.insufficientPermissions('User', 'delete', ['user:delete']);\r\n      expect(exception.getUserMessage()).toBe('You do not have the required permissions to perform this action');\r\n    });\r\n\r\n    it('should return user-friendly message for role-based restriction', () => {\r\n      const exception = ForbiddenException.insufficientRoles('System', 'configure', ['admin'], ['user']);\r\n      expect(exception.getUserMessage()).toBe('You do not have the required role to perform this action');\r\n    });\r\n\r\n    it('should return default message for general restriction', () => {\r\n      const exception = new ForbiddenException();\r\n      expect(exception.getUserMessage()).toBe('You do not have permission to access this resource');\r\n    });\r\n  });\r\n\r\n  describe('toApiResponse', () => {\r\n    it('should convert to API response format', () => {\r\n      const exception = ForbiddenException.insufficientPermissions(\r\n        'User',\r\n        'delete',\r\n        ['user:delete'],\r\n        ['user:read']\r\n      );\r\n\r\n      const response = exception.toApiResponse();\r\n\r\n      expect(response).toEqual({\r\n        error: 'You do not have the required permissions to perform this action',\r\n        code: 'FORBIDDEN',\r\n        details: {\r\n          resource: 'User',\r\n          action: 'delete',\r\n          reason: 'insufficient_privileges',\r\n          missingPermissions: ['user:delete'],\r\n          missingRoles: [],\r\n          allowedActions: [],\r\n        },\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('toJSON', () => {\r\n    it('should convert to JSON with detailed information', () => {\r\n      const exception = ForbiddenException.insufficientPermissions(\r\n        'User',\r\n        'delete',\r\n        ['user:delete', 'user:admin'],\r\n        ['user:read']\r\n      );\r\n\r\n      const json = exception.toJSON();\r\n\r\n      expect(json).toMatchObject({\r\n        name: 'ForbiddenException',\r\n        code: 'FORBIDDEN',\r\n        severity: 'medium',\r\n        category: 'authorization',\r\n        resource: 'User',\r\n        action: 'delete',\r\n        requiredPermissions: ['user:delete', 'user:admin'],\r\n        userPermissions: ['user:read'],\r\n        missingPermissions: ['user:delete', 'user:admin'],\r\n        missingRoles: [],\r\n        reason: 'insufficient_privileges',\r\n        isPermissionBased: true,\r\n        isRoleBased: false,\r\n        isOwnershipBased: false,\r\n        isTenantIsolationViolation: false,\r\n        isTimeRestricted: false,\r\n        ownershipInfo: null,\r\n        tenantInfo: null,\r\n        timeRestrictionInfo: null,\r\n        allowedActions: [],\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('inheritance', () => {\r\n    it('should be instance of Error and DomainException', () => {\r\n      const exception = new ForbiddenException();\r\n\r\n      expect(exception).toBeInstanceOf(Error);\r\n      expect(exception.name).toBe('ForbiddenException');\r\n      expect(exception.code).toBe('FORBIDDEN');\r\n    });\r\n\r\n    it('should maintain proper prototype chain', () => {\r\n      const exception = new ForbiddenException();\r\n\r\n      expect(exception instanceof ForbiddenException).toBe(true);\r\n      expect(exception instanceof Error).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('context and correlation', () => {\r\n    it('should preserve context and correlation ID', () => {\r\n      const context = { sessionId: 'session123', ipAddress: '***********' };\r\n      const correlationId = 'correlation-456';\r\n\r\n      const exception = new ForbiddenException('Test message', {\r\n        context,\r\n        correlationId,\r\n      });\r\n\r\n      expect(exception.context).toMatchObject(context);\r\n      expect(exception.correlationId).toBe(correlationId);\r\n    });\r\n  });\r\n});"], "version": 3}