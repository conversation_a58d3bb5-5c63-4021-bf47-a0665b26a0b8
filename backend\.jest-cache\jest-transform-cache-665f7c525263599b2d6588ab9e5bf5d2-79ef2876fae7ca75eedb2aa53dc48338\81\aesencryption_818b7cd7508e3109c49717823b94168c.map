{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\encryption\\aes.encryption.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAC/C,+CAAiC;AAiB1B,IAAM,aAAa,GAAnB,MAAM,aAAa;IAKxB,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAJxC,cAAS,GAAG,aAAa,CAAC;QAC1B,cAAS,GAAG,EAAE,CAAC;QACf,aAAQ,GAAG,EAAE,CAAC;IAE6B,CAAC;IAE7D;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,IAAY,EAAE,GAAY;QACtC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QACjD,MAAM,EAAE,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE7C,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAClE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC;QAEnD,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACnD,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAEjC,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QAEhC,OAAO;YACL,aAAa,EAAE,SAAS;YACxB,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;YACtB,GAAG,EAAE,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;YACxB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,OAA6B;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACzD,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAChD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAElD,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QACtE,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC;QACrD,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAE/B,IAAI,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACtE,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEpC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,QAAgB,EAAE,IAAY,EAAE,aAAqB,MAAM;QAC/E,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACjG,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAEO,gBAAgB,CAAC,GAAY;QACnC,MAAM,aAAa,GAAG,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,oBAAoB,CAAC,CAAC;QAClF,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,GAAW;QACrB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1C,OAAO,SAAS,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC;QAC7C,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF,CAAA;AAxFY,sCAAa;wBAAb,aAAa;IADzB,IAAA,mBAAU,GAAE;yDAMiC,sBAAa,oBAAb,sBAAa;GAL9C,aAAa,CAwFzB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\encryption\\aes.encryption.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport * as crypto from 'crypto';\r\n\r\nexport interface AESEncryptionResult {\r\n  encryptedData: string;\r\n  iv: string;\r\n  tag: string;\r\n  algorithm: string;\r\n}\r\n\r\nexport interface AESDecryptionOptions {\r\n  encryptedData: string;\r\n  iv: string;\r\n  tag: string;\r\n  key?: string;\r\n}\r\n\r\n@Injectable()\r\nexport class AESEncryption {\r\n  private readonly algorithm = 'aes-256-gcm';\r\n  private readonly keyLength = 32;\r\n  private readonly ivLength = 16;\r\n\r\n  constructor(private readonly configService: ConfigService) {}\r\n\r\n  /**\r\n   * Encrypt data using AES-256-GCM\r\n   */\r\n  async encrypt(data: string, key?: string): Promise<AESEncryptionResult> {\r\n    const encryptionKey = this.getEncryptionKey(key);\r\n    const iv = crypto.randomBytes(this.ivLength);\r\n    \r\n    const cipher = crypto.createCipher(this.algorithm, encryptionKey);\r\n    cipher.setAAD(Buffer.from('sentinel-aes', 'utf8'));\r\n\r\n    let encrypted = cipher.update(data, 'utf8', 'hex');\r\n    encrypted += cipher.final('hex');\r\n\r\n    const tag = cipher.getAuthTag();\r\n\r\n    return {\r\n      encryptedData: encrypted,\r\n      iv: iv.toString('hex'),\r\n      tag: tag.toString('hex'),\r\n      algorithm: this.algorithm,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Decrypt data using AES-256-GCM\r\n   */\r\n  async decrypt(options: AESDecryptionOptions): Promise<string> {\r\n    const encryptionKey = this.getEncryptionKey(options.key);\r\n    const ivBuffer = Buffer.from(options.iv, 'hex');\r\n    const tagBuffer = Buffer.from(options.tag, 'hex');\r\n\r\n    const decipher = crypto.createDecipher(this.algorithm, encryptionKey);\r\n    decipher.setAAD(Buffer.from('sentinel-aes', 'utf8'));\r\n    decipher.setAuthTag(tagBuffer);\r\n\r\n    let decrypted = decipher.update(options.encryptedData, 'hex', 'utf8');\r\n    decrypted += decipher.final('utf8');\r\n\r\n    return decrypted;\r\n  }\r\n\r\n  /**\r\n   * Generate a new AES key\r\n   */\r\n  generateKey(): string {\r\n    return crypto.randomBytes(this.keyLength).toString('hex');\r\n  }\r\n\r\n  /**\r\n   * Derive key from password using PBKDF2\r\n   */\r\n  deriveKeyFromPassword(password: string, salt: string, iterations: number = 100000): string {\r\n    return crypto.pbkdf2Sync(password, salt, iterations, this.keyLength, 'sha256').toString('hex');\r\n  }\r\n\r\n  /**\r\n   * Generate salt for key derivation\r\n   */\r\n  generateSalt(): string {\r\n    return crypto.randomBytes(16).toString('hex');\r\n  }\r\n\r\n  private getEncryptionKey(key?: string): string {\r\n    const encryptionKey = key || this.configService.get<string>('AES_ENCRYPTION_KEY');\r\n    if (!encryptionKey) {\r\n      throw new Error('AES encryption key not found in configuration');\r\n    }\r\n    return encryptionKey;\r\n  }\r\n\r\n  /**\r\n   * Validate encryption key format\r\n   */\r\n  validateKey(key: string): boolean {\r\n    try {\r\n      const keyBuffer = Buffer.from(key, 'hex');\r\n      return keyBuffer.length === this.keyLength;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n}"], "version": 3}