{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\decorators\\index.spec.ts", "mappings": ";AAAA;;;;GAIG;;AAEH,+DAA+D;AAC/D,kCAAgC;AAChC,kCAAgC;AAChC,kCAAgC;AAChC,uCAAqC;AAErC,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;QACzD,iEAAiE;QACjE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\decorators\\index.spec.ts"], "sourcesContent": ["/**\r\n * Decorator Tests Index\r\n * \r\n * This file ensures all decorator tests are properly exported and can be run together.\r\n */\r\n\r\n// Import all decorator test suites to ensure they are executed\r\nimport './audit.decorator.spec';\r\nimport './cache.decorator.spec';\r\nimport './retry.decorator.spec';\r\nimport './rate-limit.decorator.spec';\r\n\r\ndescribe('Decorators Integration', () => {\r\n  it('should have all decorator test suites available', () => {\r\n    // This test ensures all decorator test files are properly loaded\r\n    expect(true).toBe(true);\r\n  });\r\n});"], "version": 3}