{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\services\\password.service.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,+CAAiC;AAEjC;;;GAGG;AAEI,IAAM,eAAe,uBAArB,MAAM,eAAe;IAI1B,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAHxC,WAAM,GAAG,IAAI,eAAM,CAAC,iBAAe,CAAC,IAAI,CAAC,CAAC;QAIzD,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE;gBACpC,cAAc,EAAE,QAAQ,CAAC,MAAM;gBAC/B,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAE1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,UAAU,EAAE,IAAI,CAAC,MAAM;aACxB,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,cAAc,EAAE,QAAQ,CAAC,MAAM;aAChC,CAAC,CAAC;YACH,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,IAAY;QACnD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;gBACvC,cAAc,EAAE,QAAQ,CAAC,MAAM;gBAC/B,UAAU,EAAE,IAAI,CAAC,MAAM;aACxB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAErD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBAC9C,OAAO;aACR,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,cAAc,EAAE,QAAQ,CAAC,MAAM;gBAC/B,UAAU,EAAE,IAAI,CAAC,MAAM;aACxB,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,wBAAwB,CAAC,QAAgB;QAKvC,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,YAAY,GAAG,UAAU,CAAC,QAAQ,CAAC;QAEzC,uBAAuB;QACvB,IAAI,QAAQ,CAAC,MAAM,GAAG,YAAY,CAAC,iBAAiB,EAAE,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,6BAA6B,YAAY,CAAC,iBAAiB,kBAAkB,CAAC,CAAC;QAC7F,CAAC;aAAM,CAAC;YACN,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,8BAA8B;QAC9B,IAAI,YAAY,CAAC,wBAAwB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrE,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,8BAA8B;QAC9B,IAAI,YAAY,CAAC,wBAAwB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrE,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,oBAAoB;QACpB,IAAI,YAAY,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChE,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAC3D,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,oBAAoB;QACpB,IAAI,YAAY,CAAC,sBAAsB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpF,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACtE,CAAC;aAAM,IAAI,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnD,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;YAC1B,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;YAC1B,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,4BAA4B;QAC5B,MAAM,cAAc,GAAG;YACrB,QAAQ;YACR,WAAW;YACX,SAAS;YACT,QAAQ;YACR,UAAU;SACX,CAAC;QAEF,MAAM,gBAAgB,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAChF,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;YACnE,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;QACjC,CAAC;QAED,gCAAgC;QAChC,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAC/D,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;QACjC,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;QAEpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;YAChD,cAAc,EAAE,QAAQ,CAAC,MAAM;YAC/B,OAAO;YACP,KAAK;YACL,UAAU,EAAE,MAAM,CAAC,MAAM;SAC1B,CAAC,CAAC;QAEH,OAAO;YACL,OAAO;YACP,MAAM;YACN,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,iBAAiB;SAC7C,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,sBAAsB,CAAC,SAAiB,EAAE,EAAE,iBAA0B,IAAI;QACxE,MAAM,SAAS,GAAG,4BAA4B,CAAC;QAC/C,MAAM,SAAS,GAAG,4BAA4B,CAAC;QAC/C,MAAM,OAAO,GAAG,YAAY,CAAC;QAC7B,MAAM,OAAO,GAAG,sBAAsB,CAAC;QAEvC,IAAI,OAAO,GAAG,SAAS,GAAG,SAAS,GAAG,OAAO,CAAC;QAC9C,IAAI,cAAc,EAAE,CAAC;YACnB,OAAO,IAAI,OAAO,CAAC;QACrB,CAAC;QAED,IAAI,QAAQ,GAAG,EAAE,CAAC;QAElB,uDAAuD;QACvD,QAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QACpE,QAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QACpE,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAEhE,IAAI,cAAc,EAAE,CAAC;YACnB,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,yBAAyB;QACzB,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,uBAAuB;QACvB,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACzC,KAAK,IAAI,CAAC,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9E,CAAC;QAED,MAAM,iBAAiB,GAAG,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEjD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;YAC7C,MAAM,EAAE,iBAAiB,CAAC,MAAM;YAChC,cAAc;SACf,CAAC,CAAC;QAEH,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACH,WAAW,CAAC,IAAY;QACtB,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,WAAW,GAAG,aAAa,KAAK,IAAI,CAAC,UAAU,CAAC;YAEtD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,aAAa;gBACb,gBAAgB,EAAE,IAAI,CAAC,UAAU;gBACjC,WAAW;aACZ,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE;gBAC3D,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF,CAAA;AA9OY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;yDAKiC,sBAAa,oBAAb,sBAAa;GAJ9C,eAAe,CA8O3B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\services\\password.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport * as bcrypt from 'bcrypt';\r\n\r\n/**\r\n * Password service for hashing and validating passwords\r\n * Provides secure password operations using bcrypt\r\n */\r\n@Injectable()\r\nexport class PasswordService {\r\n  private readonly logger = new Logger(PasswordService.name);\r\n  private readonly saltRounds: number;\r\n\r\n  constructor(private readonly configService: ConfigService) {\r\n    const authConfig = this.configService.get('auth');\r\n    this.saltRounds = authConfig.bcrypt.rounds;\r\n  }\r\n\r\n  /**\r\n   * Hash a plain text password\r\n   * @param password Plain text password\r\n   * @returns Promise<string> Hashed password\r\n   */\r\n  async hashPassword(password: string): Promise<string> {\r\n    try {\r\n      this.logger.debug('Hashing password', {\r\n        passwordLength: password.length,\r\n        saltRounds: this.saltRounds,\r\n      });\r\n\r\n      const hash = await bcrypt.hash(password, this.saltRounds);\r\n\r\n      this.logger.debug('Password hashed successfully', {\r\n        hashLength: hash.length,\r\n      });\r\n\r\n      return hash;\r\n    } catch (error) {\r\n      this.logger.error('Error hashing password', {\r\n        error: error.message,\r\n        passwordLength: password.length,\r\n      });\r\n      throw new Error('Failed to hash password');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate a password against its hash\r\n   * @param password Plain text password\r\n   * @param hash Hashed password\r\n   * @returns Promise<boolean> True if password is valid\r\n   */\r\n  async validatePassword(password: string, hash: string): Promise<boolean> {\r\n    try {\r\n      this.logger.debug('Validating password', {\r\n        passwordLength: password.length,\r\n        hashLength: hash.length,\r\n      });\r\n\r\n      const isValid = await bcrypt.compare(password, hash);\r\n\r\n      this.logger.debug('Password validation result', {\r\n        isValid,\r\n      });\r\n\r\n      return isValid;\r\n    } catch (error) {\r\n      this.logger.error('Error validating password', {\r\n        error: error.message,\r\n        passwordLength: password.length,\r\n        hashLength: hash.length,\r\n      });\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate password strength\r\n   * @param password Plain text password\r\n   * @returns Object containing validation result and details\r\n   */\r\n  validatePasswordStrength(password: string): {\r\n    isValid: boolean;\r\n    errors: string[];\r\n    score: number;\r\n  } {\r\n    const errors: string[] = [];\r\n    let score = 0;\r\n\r\n    const authConfig = this.configService.get('auth');\r\n    const requirements = authConfig.security;\r\n\r\n    // Check minimum length\r\n    if (password.length < requirements.passwordMinLength) {\r\n      errors.push(`Password must be at least ${requirements.passwordMinLength} characters long`);\r\n    } else {\r\n      score += 1;\r\n    }\r\n\r\n    // Check for uppercase letters\r\n    if (requirements.passwordRequireUppercase && !/[A-Z]/.test(password)) {\r\n      errors.push('Password must contain at least one uppercase letter');\r\n    } else if (/[A-Z]/.test(password)) {\r\n      score += 1;\r\n    }\r\n\r\n    // Check for lowercase letters\r\n    if (requirements.passwordRequireLowercase && !/[a-z]/.test(password)) {\r\n      errors.push('Password must contain at least one lowercase letter');\r\n    } else if (/[a-z]/.test(password)) {\r\n      score += 1;\r\n    }\r\n\r\n    // Check for numbers\r\n    if (requirements.passwordRequireNumbers && !/\\d/.test(password)) {\r\n      errors.push('Password must contain at least one number');\r\n    } else if (/\\d/.test(password)) {\r\n      score += 1;\r\n    }\r\n\r\n    // Check for symbols\r\n    if (requirements.passwordRequireSymbols && !/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\r\n      errors.push('Password must contain at least one special character');\r\n    } else if (/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\r\n      score += 1;\r\n    }\r\n\r\n    // Additional strength checks\r\n    if (password.length >= 12) {\r\n      score += 1;\r\n    }\r\n\r\n    if (password.length >= 16) {\r\n      score += 1;\r\n    }\r\n\r\n    // Check for common patterns\r\n    const commonPatterns = [\r\n      /123456/,\r\n      /password/i,\r\n      /qwerty/i,\r\n      /admin/i,\r\n      /letmein/i,\r\n    ];\r\n\r\n    const hasCommonPattern = commonPatterns.some(pattern => pattern.test(password));\r\n    if (hasCommonPattern) {\r\n      errors.push('Password contains common patterns and is not secure');\r\n      score = Math.max(0, score - 2);\r\n    }\r\n\r\n    // Check for repeated characters\r\n    if (/(.)\\1{2,}/.test(password)) {\r\n      errors.push('Password should not contain repeated characters');\r\n      score = Math.max(0, score - 1);\r\n    }\r\n\r\n    const isValid = errors.length === 0;\r\n\r\n    this.logger.debug('Password strength validation', {\r\n      passwordLength: password.length,\r\n      isValid,\r\n      score,\r\n      errorCount: errors.length,\r\n    });\r\n\r\n    return {\r\n      isValid,\r\n      errors,\r\n      score: Math.min(score, 5), // Cap score at 5\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate a secure random password\r\n   * @param length Password length (default: 16)\r\n   * @param includeSymbols Include special characters (default: true)\r\n   * @returns string Generated password\r\n   */\r\n  generateSecurePassword(length: number = 16, includeSymbols: boolean = true): string {\r\n    const lowercase = 'abcdefghijklmnopqrstuvwxyz';\r\n    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\r\n    const numbers = '0123456789';\r\n    const symbols = '!@#$%^&*(),.?\":{}|<>';\r\n\r\n    let charset = lowercase + uppercase + numbers;\r\n    if (includeSymbols) {\r\n      charset += symbols;\r\n    }\r\n\r\n    let password = '';\r\n    \r\n    // Ensure at least one character from each required set\r\n    password += lowercase[Math.floor(Math.random() * lowercase.length)];\r\n    password += uppercase[Math.floor(Math.random() * uppercase.length)];\r\n    password += numbers[Math.floor(Math.random() * numbers.length)];\r\n    \r\n    if (includeSymbols) {\r\n      password += symbols[Math.floor(Math.random() * symbols.length)];\r\n    }\r\n\r\n    // Fill the rest randomly\r\n    for (let i = password.length; i < length; i++) {\r\n      password += charset[Math.floor(Math.random() * charset.length)];\r\n    }\r\n\r\n    // Shuffle the password\r\n    const passwordArray = password.split('');\r\n    for (let i = passwordArray.length - 1; i > 0; i--) {\r\n      const j = Math.floor(Math.random() * (i + 1));\r\n      [passwordArray[i], passwordArray[j]] = [passwordArray[j], passwordArray[i]];\r\n    }\r\n\r\n    const generatedPassword = passwordArray.join('');\r\n\r\n    this.logger.debug('Secure password generated', {\r\n      length: generatedPassword.length,\r\n      includeSymbols,\r\n    });\r\n\r\n    return generatedPassword;\r\n  }\r\n\r\n  /**\r\n   * Check if password needs to be rehashed (due to changed salt rounds)\r\n   * @param hash Existing password hash\r\n   * @returns boolean True if rehashing is needed\r\n   */\r\n  needsRehash(hash: string): boolean {\r\n    try {\r\n      const currentRounds = bcrypt.getRounds(hash);\r\n      const needsRehash = currentRounds !== this.saltRounds;\r\n\r\n      this.logger.debug('Password rehash check', {\r\n        currentRounds,\r\n        configuredRounds: this.saltRounds,\r\n        needsRehash,\r\n      });\r\n\r\n      return needsRehash;\r\n    } catch (error) {\r\n      this.logger.error('Error checking if password needs rehash', {\r\n        error: error.message,\r\n      });\r\n      return false;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}