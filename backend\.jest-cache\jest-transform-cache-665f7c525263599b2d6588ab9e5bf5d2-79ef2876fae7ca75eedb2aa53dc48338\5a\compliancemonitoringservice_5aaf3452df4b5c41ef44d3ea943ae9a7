2f2492a0c31f1689a305c73b0102b06b
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ComplianceMonitoringService_1;
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComplianceMonitoringService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_1 = require("@nestjs/schedule");
const compliance_framework_entity_1 = require("../../domain/entities/compliance-framework.entity");
const compliance_assessment_entity_1 = require("../../domain/entities/compliance-assessment.entity");
const policy_definition_entity_1 = require("../../domain/entities/policy-definition.entity");
const policy_violation_entity_1 = require("../../domain/entities/policy-violation.entity");
const audit_log_entity_1 = require("../../domain/entities/audit-log.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../infrastructure/notification/notification.service");
const policy_enforcement_service_1 = require("./policy-enforcement.service");
const violation_detection_service_1 = require("./violation-detection.service");
/**
 * Compliance Monitoring service
 * Handles continuous compliance monitoring, assessment scheduling, and violation tracking
 */
let ComplianceMonitoringService = ComplianceMonitoringService_1 = class ComplianceMonitoringService {
    constructor(frameworkRepository, assessmentRepository, policyRepository, violationRepository, auditLogRepository, loggerService, auditService, notificationService, policyEnforcementService, violationDetectionService) {
        this.frameworkRepository = frameworkRepository;
        this.assessmentRepository = assessmentRepository;
        this.policyRepository = policyRepository;
        this.violationRepository = violationRepository;
        this.auditLogRepository = auditLogRepository;
        this.loggerService = loggerService;
        this.auditService = auditService;
        this.notificationService = notificationService;
        this.policyEnforcementService = policyEnforcementService;
        this.violationDetectionService = violationDetectionService;
        this.logger = new common_1.Logger(ComplianceMonitoringService_1.name);
    }
    /**
     * Get compliance dashboard data
     * @returns Compliance dashboard metrics
     */
    async getComplianceDashboard() {
        try {
            this.logger.debug('Generating compliance dashboard');
            const [activeFrameworks, totalAssessments, openViolations, criticalViolations, complianceByFramework, recentViolations, upcomingAssessments, complianceTrends,] = await Promise.all([
                this.getActiveFrameworksCount(),
                this.getTotalAssessmentsCount(),
                this.getOpenViolationsCount(),
                this.getCriticalViolationsCount(),
                this.getComplianceByFramework(),
                this.getRecentViolations(7), // Last 7 days
                this.getUpcomingAssessments(30), // Next 30 days
                this.getComplianceTrends(90), // Last 90 days
            ]);
            const dashboard = {
                summary: {
                    activeFrameworks,
                    totalAssessments,
                    openViolations,
                    criticalViolations,
                    overallComplianceScore: await this.calculateOverallComplianceScore(),
                },
                complianceByFramework,
                recentViolations,
                upcomingAssessments,
                trends: complianceTrends,
                riskMetrics: await this.calculateRiskMetrics(),
                timestamp: new Date().toISOString(),
            };
            this.logger.log('Compliance dashboard generated successfully', {
                activeFrameworks,
                openViolations,
                criticalViolations,
            });
            return dashboard;
        }
        catch (error) {
            this.logger.error('Failed to generate compliance dashboard', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Monitor compliance status for all active frameworks
     */
    async monitorCompliance() {
        try {
            this.logger.debug('Starting compliance monitoring cycle');
            const activeFrameworks = await this.frameworkRepository.find({
                where: { isActive: true },
            });
            for (const framework of activeFrameworks) {
                try {
                    await this.monitorFrameworkCompliance(framework.id);
                }
                catch (error) {
                    this.logger.error('Failed to monitor framework compliance', {
                        frameworkId: framework.id,
                        frameworkName: framework.name,
                        error: error.message,
                    });
                }
            }
            // Check for overdue assessments
            await this.checkOverdueAssessments();
            // Check for policy violations
            await this.checkPolicyViolations();
            this.logger.log('Compliance monitoring cycle completed', {
                frameworksMonitored: activeFrameworks.length,
            });
        }
        catch (error) {
            this.logger.error('Failed to complete compliance monitoring cycle', {
                error: error.message,
            });
        }
    }
    /**
     * Monitor compliance for a specific framework
     * @param frameworkId Framework ID
     */
    async monitorFrameworkCompliance(frameworkId) {
        try {
            const framework = await this.frameworkRepository.findOne({
                where: { id: frameworkId },
                relations: ['assessments', 'policies'],
            });
            if (!framework) {
                throw new common_1.NotFoundException('Framework not found');
            }
            this.logger.debug('Monitoring framework compliance', {
                frameworkId,
                frameworkName: framework.name,
            });
            // Get latest assessment
            const latestAssessment = await this.getLatestAssessment(frameworkId);
            // Check if new assessment is needed
            if (this.isAssessmentDue(latestAssessment, framework)) {
                await this.scheduleAssessment(framework);
            }
            // Monitor continuous controls
            await this.monitorContinuousControls(framework);
            // Check policy compliance
            await this.checkPolicyCompliance(framework);
            // Update compliance metrics
            await this.updateComplianceMetrics(framework);
            this.logger.debug('Framework compliance monitoring completed', {
                frameworkId,
            });
        }
        catch (error) {
            this.logger.error('Failed to monitor framework compliance', {
                frameworkId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Create compliance assessment
     * @param frameworkId Framework ID
     * @param assessmentData Assessment data
     * @param userId User creating the assessment
     * @returns Created assessment
     */
    async createAssessment(frameworkId, assessmentData, userId) {
        try {
            this.logger.debug('Creating compliance assessment', {
                frameworkId,
                assessmentType: assessmentData.assessmentType,
                userId,
            });
            const framework = await this.frameworkRepository.findOne({
                where: { id: frameworkId },
            });
            if (!framework) {
                throw new common_1.NotFoundException('Framework not found');
            }
            const assessment = this.assessmentRepository.create({
                ...assessmentData,
                frameworkId,
                status: 'planned',
                createdBy: userId,
            });
            const savedAssessment = await this.assessmentRepository.save(assessment);
            await this.auditService.logUserAction(userId, 'create', 'compliance_assessment', savedAssessment.id, {
                frameworkId,
                assessmentType: assessmentData.assessmentType,
                assessmentName: assessmentData.name,
            });
            this.logger.log('Compliance assessment created successfully', {
                assessmentId: savedAssessment.id,
                frameworkId,
                userId,
            });
            return savedAssessment;
        }
        catch (error) {
            this.logger.error('Failed to create compliance assessment', {
                frameworkId,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Start compliance assessment
     * @param assessmentId Assessment ID
     * @param userId User starting the assessment
     * @returns Updated assessment
     */
    async startAssessment(assessmentId, userId) {
        try {
            const assessment = await this.assessmentRepository.findOne({
                where: { id: assessmentId },
                relations: ['framework'],
            });
            if (!assessment) {
                throw new common_1.NotFoundException('Assessment not found');
            }
            assessment.start(userId);
            const updatedAssessment = await this.assessmentRepository.save(assessment);
            // Initialize assessment with framework controls
            await this.initializeAssessmentControls(assessment);
            await this.auditService.logUserAction(userId, 'start', 'compliance_assessment', assessmentId, {
                frameworkId: assessment.frameworkId,
                assessmentType: assessment.assessmentType,
            });
            this.logger.log('Compliance assessment started', {
                assessmentId,
                userId,
            });
            return updatedAssessment;
        }
        catch (error) {
            this.logger.error('Failed to start compliance assessment', {
                assessmentId,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Complete compliance assessment
     * @param assessmentId Assessment ID
     * @param results Assessment results
     * @param userId User completing the assessment
     * @returns Updated assessment
     */
    async completeAssessment(assessmentId, results, userId) {
        try {
            const assessment = await this.assessmentRepository.findOne({
                where: { id: assessmentId },
                relations: ['framework'],
            });
            if (!assessment) {
                throw new common_1.NotFoundException('Assessment not found');
            }
            assessment.complete(userId, results);
            const updatedAssessment = await this.assessmentRepository.save(assessment);
            // Generate violations for non-compliant controls
            await this.generateViolationsFromAssessment(assessment);
            // Send notifications
            await this.notificationService.sendComplianceNotification(assessment, 'assessment_completed');
            await this.auditService.logUserAction(userId, 'complete', 'compliance_assessment', assessmentId, {
                frameworkId: assessment.frameworkId,
                complianceScore: results.overallScore,
                complianceStatus: results.overallStatus,
            });
            this.logger.log('Compliance assessment completed', {
                assessmentId,
                complianceScore: results.overallScore,
                userId,
            });
            return updatedAssessment;
        }
        catch (error) {
            this.logger.error('Failed to complete compliance assessment', {
                assessmentId,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Get compliance status for framework
     * @param frameworkId Framework ID
     * @returns Compliance status
     */
    async getFrameworkComplianceStatus(frameworkId) {
        try {
            const framework = await this.frameworkRepository.findOne({
                where: { id: frameworkId },
                relations: ['assessments', 'policies'],
            });
            if (!framework) {
                throw new common_1.NotFoundException('Framework not found');
            }
            const latestAssessment = await this.getLatestAssessment(frameworkId);
            const openViolations = await this.getFrameworkViolations(frameworkId, true);
            const policies = await this.getFrameworkPolicies(frameworkId);
            const status = {
                framework: {
                    id: framework.id,
                    name: framework.name,
                    type: framework.type,
                    version: framework.version,
                    totalControls: framework.totalControls,
                },
                latestAssessment: latestAssessment ? {
                    id: latestAssessment.id,
                    assessmentDate: latestAssessment.assessmentDate,
                    status: latestAssessment.status,
                    compliancePercentage: latestAssessment.compliancePercentage,
                    overallStatus: latestAssessment.results?.overallStatus,
                    criticalFindings: latestAssessment.criticalFindingsCount,
                    highFindings: latestAssessment.highFindingsCount,
                } : null,
                violations: {
                    total: openViolations.length,
                    critical: openViolations.filter(v => v.severity === 'critical').length,
                    high: openViolations.filter(v => v.severity === 'high').length,
                    medium: openViolations.filter(v => v.severity === 'medium').length,
                    low: openViolations.filter(v => v.severity === 'low').length,
                },
                policies: {
                    total: policies.length,
                    active: policies.filter(p => p.isActive).length,
                    expired: policies.filter(p => p.isExpired).length,
                    dueForReview: policies.filter(p => p.isDueForReview).length,
                },
                complianceScore: await this.calculateFrameworkComplianceScore(frameworkId),
                riskLevel: await this.calculateFrameworkRiskLevel(frameworkId),
                nextAssessmentDate: latestAssessment?.nextAssessmentDate,
                isOverdue: latestAssessment?.isOverdue || false,
            };
            return status;
        }
        catch (error) {
            this.logger.error('Failed to get framework compliance status', {
                frameworkId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Get compliance trends
     * @param days Number of days to look back
     * @returns Compliance trends data
     */
    async getComplianceTrends(days = 90) {
        try {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            const assessments = await this.assessmentRepository.find({
                where: {
                    assessmentDate: (0, typeorm_2.Between)(startDate, endDate),
                    status: 'completed',
                },
                relations: ['framework'],
                order: { assessmentDate: 'ASC' },
            });
            const violations = await this.violationRepository.find({
                where: {
                    detectedAt: (0, typeorm_2.Between)(startDate, endDate),
                },
                order: { detectedAt: 'ASC' },
            });
            // Group data by week
            const trends = this.groupTrendsByWeek(assessments, violations, startDate, endDate);
            return {
                period: { startDate, endDate, days },
                trends,
                summary: {
                    totalAssessments: assessments.length,
                    totalViolations: violations.length,
                    averageComplianceScore: this.calculateAverageComplianceScore(assessments),
                    trendDirection: this.calculateTrendDirection(trends),
                },
            };
        }
        catch (error) {
            this.logger.error('Failed to get compliance trends', {
                days,
                error: error.message,
            });
            throw error;
        }
    }
    // Private helper methods
    async getActiveFrameworksCount() {
        return await this.frameworkRepository.count({ where: { isActive: true } });
    }
    async getTotalAssessmentsCount() {
        return await this.assessmentRepository.count();
    }
    async getOpenViolationsCount() {
        return await this.violationRepository.count({
            where: { status: (0, typeorm_2.In)(['open', 'investigating', 'remediation_planned', 'remediation_in_progress']) },
        });
    }
    async getCriticalViolationsCount() {
        return await this.violationRepository.count({
            where: {
                severity: 'critical',
                status: (0, typeorm_2.In)(['open', 'investigating', 'remediation_planned', 'remediation_in_progress']),
            },
        });
    }
    async getComplianceByFramework() {
        const frameworks = await this.frameworkRepository.find({
            where: { isActive: true },
            relations: ['assessments'],
        });
        const complianceData = [];
        for (const framework of frameworks) {
            const latestAssessment = await this.getLatestAssessment(framework.id);
            const complianceScore = await this.calculateFrameworkComplianceScore(framework.id);
            complianceData.push({
                frameworkId: framework.id,
                frameworkName: framework.name,
                frameworkType: framework.type,
                complianceScore,
                lastAssessmentDate: latestAssessment?.assessmentDate,
                status: latestAssessment?.results?.overallStatus || 'unknown',
            });
        }
        return complianceData;
    }
    async getRecentViolations(days) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const violations = await this.violationRepository.find({
            where: { detectedAt: (0, typeorm_2.Between)(startDate, new Date()) },
            order: { detectedAt: 'DESC' },
            take: 10,
        });
        return violations.map(v => v.generateSummary());
    }
    async getUpcomingAssessments(days) {
        const endDate = new Date();
        endDate.setDate(endDate.getDate() + days);
        const assessments = await this.assessmentRepository.find({
            where: {
                nextAssessmentDate: (0, typeorm_2.Between)(new Date(), endDate),
                status: (0, typeorm_2.In)(['planned', 'in_progress']),
            },
            relations: ['framework'],
            order: { nextAssessmentDate: 'ASC' },
        });
        return assessments.map(a => a.generateSummary());
    }
    async calculateOverallComplianceScore() {
        const frameworks = await this.frameworkRepository.find({ where: { isActive: true } });
        if (frameworks.length === 0)
            return 0;
        let totalScore = 0;
        for (const framework of frameworks) {
            const score = await this.calculateFrameworkComplianceScore(framework.id);
            totalScore += score;
        }
        return Math.round(totalScore / frameworks.length);
    }
    async calculateRiskMetrics() {
        const criticalViolations = await this.getCriticalViolationsCount();
        const highViolations = await this.violationRepository.count({
            where: {
                severity: 'high',
                status: (0, typeorm_2.In)(['open', 'investigating', 'remediation_planned', 'remediation_in_progress']),
            },
        });
        const overallRiskScore = (criticalViolations * 10) + (highViolations * 5);
        let riskLevel = 'low';
        if (overallRiskScore >= 50)
            riskLevel = 'critical';
        else if (overallRiskScore >= 25)
            riskLevel = 'high';
        else if (overallRiskScore >= 10)
            riskLevel = 'medium';
        return {
            overallRiskScore,
            riskLevel,
            criticalViolations,
            highViolations,
        };
    }
    async getLatestAssessment(frameworkId) {
        return await this.assessmentRepository.findOne({
            where: { frameworkId },
            order: { assessmentDate: 'DESC' },
        });
    }
    isAssessmentDue(assessment, framework) {
        if (!assessment)
            return true;
        if (!assessment.nextAssessmentDate)
            return false;
        return new Date() >= assessment.nextAssessmentDate;
    }
    async scheduleAssessment(framework) {
        // Implementation for scheduling new assessment
        this.logger.log('Assessment due for framework', {
            frameworkId: framework.id,
            frameworkName: framework.name,
        });
    }
    async monitorContinuousControls(framework) {
        // Implementation for monitoring continuous controls
        const continuousControls = framework.automatableControls;
        for (const control of continuousControls) {
            await this.policyEnforcementService.evaluateControl(framework.id, control.id);
        }
    }
    async checkPolicyCompliance(framework) {
        const policies = await this.policyRepository.find({
            where: { frameworkId: framework.id, isActive: true },
        });
        for (const policy of policies) {
            await this.violationDetectionService.checkPolicyCompliance(policy.id);
        }
    }
    async updateComplianceMetrics(framework) {
        // Implementation for updating compliance metrics
        this.logger.debug('Updating compliance metrics', {
            frameworkId: framework.id,
        });
    }
    async checkOverdueAssessments() {
        const overdueAssessments = await this.assessmentRepository.find({
            where: {
                nextAssessmentDate: (0, typeorm_2.Between)(new Date('1900-01-01'), new Date()),
                status: (0, typeorm_2.In)(['planned', 'in_progress']),
            },
            relations: ['framework'],
        });
        for (const assessment of overdueAssessments) {
            await this.notificationService.sendAssessmentOverdueNotification(assessment);
        }
    }
    async checkPolicyViolations() {
        await this.violationDetectionService.scanForViolations();
    }
    async initializeAssessmentControls(assessment) {
        // Initialize assessment with framework controls
        if (!assessment.results) {
            assessment.results = { domainResults: [] };
        }
        const framework = assessment.framework;
        for (const domain of framework.configuration.domains) {
            const domainResult = {
                domainId: domain.id,
                domainName: domain.name,
                score: 0,
                status: 'non_compliant',
                controlResults: domain.controls.map(control => ({
                    controlId: control.id,
                    controlName: control.name,
                    status: 'not_tested',
                })),
            };
            assessment.results.domainResults.push(domainResult);
        }
        await this.assessmentRepository.save(assessment);
    }
    async generateViolationsFromAssessment(assessment) {
        if (!assessment.results?.domainResults)
            return;
        for (const domain of assessment.results.domainResults) {
            for (const control of domain.controlResults) {
                if (control.status === 'non_compliant') {
                    await this.createViolationFromControl(assessment, domain, control);
                }
            }
        }
    }
    async createViolationFromControl(assessment, domain, control) {
        const violation = this.violationRepository.create({
            title: `Non-compliance with ${control.controlName}`,
            description: `Control ${control.controlId} in domain ${domain.domainName} is non-compliant`,
            violationType: 'compliance_monitoring',
            severity: control.riskLevel || 'medium',
            detectedAt: new Date(),
            assessmentId: assessment.id,
            details: {
                violatedControls: [control.controlId],
                evidence: control.evidence || [],
                detectionMethod: 'compliance_assessment',
                compliance: {
                    frameworks: [assessment.frameworkId],
                    controls: [control.controlId],
                    requirements: control.findings || [],
                },
            },
            detectedBy: assessment.updatedBy || assessment.createdBy,
            tags: ['compliance', 'assessment', assessment.framework?.type || 'unknown'],
        });
        await this.violationRepository.save(violation);
    }
    async getFrameworkViolations(frameworkId, openOnly = false) {
        const whereConditions = {};
        if (openOnly) {
            whereConditions.status = (0, typeorm_2.In)(['open', 'investigating', 'remediation_planned', 'remediation_in_progress']);
        }
        // This would need to be implemented based on how violations are linked to frameworks
        return await this.violationRepository.find({ where: whereConditions });
    }
    async getFrameworkPolicies(frameworkId) {
        return await this.policyRepository.find({ where: { frameworkId } });
    }
    async calculateFrameworkComplianceScore(frameworkId) {
        const latestAssessment = await this.getLatestAssessment(frameworkId);
        return latestAssessment?.compliancePercentage || 0;
    }
    async calculateFrameworkRiskLevel(frameworkId) {
        const violations = await this.getFrameworkViolations(frameworkId, true);
        const criticalCount = violations.filter(v => v.severity === 'critical').length;
        const highCount = violations.filter(v => v.severity === 'high').length;
        if (criticalCount > 0)
            return 'critical';
        if (highCount > 2)
            return 'high';
        if (highCount > 0 || violations.length > 5)
            return 'medium';
        return 'low';
    }
    groupTrendsByWeek(assessments, violations, startDate, endDate) {
        // Implementation for grouping trends by week
        const weeks = [];
        const currentDate = new Date(startDate);
        while (currentDate <= endDate) {
            const weekStart = new Date(currentDate);
            const weekEnd = new Date(currentDate);
            weekEnd.setDate(weekEnd.getDate() + 6);
            const weekAssessments = assessments.filter(a => a.assessmentDate >= weekStart && a.assessmentDate <= weekEnd);
            const weekViolations = violations.filter(v => v.detectedAt >= weekStart && v.detectedAt <= weekEnd);
            weeks.push({
                weekStart,
                weekEnd,
                assessments: weekAssessments.length,
                violations: weekViolations.length,
                averageComplianceScore: this.calculateAverageComplianceScore(weekAssessments),
            });
            currentDate.setDate(currentDate.getDate() + 7);
        }
        return weeks;
    }
    calculateAverageComplianceScore(assessments) {
        if (assessments.length === 0)
            return 0;
        const totalScore = assessments.reduce((sum, assessment) => sum + (assessment.compliancePercentage || 0), 0);
        return Math.round(totalScore / assessments.length);
    }
    calculateTrendDirection(trends) {
        if (trends.length < 2)
            return 'stable';
        const firstHalf = trends.slice(0, Math.floor(trends.length / 2));
        const secondHalf = trends.slice(Math.floor(trends.length / 2));
        const firstAvg = this.calculateAverageComplianceScore(firstHalf);
        const secondAvg = this.calculateAverageComplianceScore(secondHalf);
        if (secondAvg > firstAvg + 5)
            return 'improving';
        if (secondAvg < firstAvg - 5)
            return 'declining';
        return 'stable';
    }
};
exports.ComplianceMonitoringService = ComplianceMonitoringService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_HOUR),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_l = typeof Promise !== "undefined" && Promise) === "function" ? _l : Object)
], ComplianceMonitoringService.prototype, "monitorCompliance", null);
exports.ComplianceMonitoringService = ComplianceMonitoringService = ComplianceMonitoringService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(compliance_framework_entity_1.ComplianceFramework)),
    __param(1, (0, typeorm_1.InjectRepository)(compliance_assessment_entity_1.ComplianceAssessment)),
    __param(2, (0, typeorm_1.InjectRepository)(policy_definition_entity_1.PolicyDefinition)),
    __param(3, (0, typeorm_1.InjectRepository)(policy_violation_entity_1.PolicyViolation)),
    __param(4, (0, typeorm_1.InjectRepository)(audit_log_entity_1.AuditLog)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _d : Object, typeof (_e = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _e : Object, typeof (_f = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _f : Object, typeof (_g = typeof audit_service_1.AuditService !== "undefined" && audit_service_1.AuditService) === "function" ? _g : Object, typeof (_h = typeof notification_service_1.NotificationService !== "undefined" && notification_service_1.NotificationService) === "function" ? _h : Object, typeof (_j = typeof policy_enforcement_service_1.PolicyEnforcementService !== "undefined" && policy_enforcement_service_1.PolicyEnforcementService) === "function" ? _j : Object, typeof (_k = typeof violation_detection_service_1.ViolationDetectionService !== "undefined" && violation_detection_service_1.ViolationDetectionService) === "function" ? _k : Object])
], ComplianceMonitoringService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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