{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\transaction.manager.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAiE;AAEjE,sDAAsD;AACtD,MAAM,YAAY,GAAG,CAAC,KAAc,EAAE,EAAE,CAAC,CAAC;IACxC,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;IAC/D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;CACxD,CAAC,CAAC;AAEH;;;GAGG;AAEI,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAI7B,YACsB,UAAsB;QAJ3B,WAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;QAM5D,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,oBAAoB,CACxB,SAAiD,EACjD,cAA2F;QAE3F,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;QAExD,IAAI,CAAC;YACH,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;YAE5B,IAAI,cAAc,EAAE,CAAC;gBACnB,MAAM,WAAW,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YACrD,CAAC;iBAAM,CAAC;gBACN,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAC;YACvC,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;gBACvC,cAAc,EAAE,cAAc,IAAI,SAAS;gBAC3C,YAAY,EAAE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ;aACtD,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEpD,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAC;YAEtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACxD,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACpD,KAAK,EAAE,SAAS,CAAC,OAAO;gBACxB,KAAK,EAAE,SAAS,CAAC,KAAK;aACvB,CAAC,CAAC;YAEH,IAAI,CAAC;gBACH,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAC;gBACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAC5D,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;oBAClD,KAAK,EAAE,YAAY,CAAC,aAAa,CAAC,CAAC,OAAO;iBAC3C,CAAC,CAAC;YACL,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC;gBACH,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC7C,CAAC;YAAC,OAAO,YAAY,EAAE,CAAC;gBACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;oBAClD,KAAK,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC,OAAO;iBAC1C,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,4BAA4B,CAChC,UAAyD,EACzD,cAA2F;QAE3F,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACjD,MAAM,OAAO,GAAQ,EAAE,CAAC;YAExB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3C,IAAI,CAAC;oBACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;oBACvE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;oBAC5C,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACvB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;oBACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE;wBAC7C,KAAK,EAAE,SAAS,CAAC,OAAO;wBACxB,cAAc,EAAE,CAAC;qBAClB,CAAC,CAAC;oBACH,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC,EAAE,cAAc,CAAC,CAAC;IACrB,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,qBAAqB,CACzB,UAGE;QAEF,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;QACxD,MAAM,OAAO,GAAQ,EAAE,CAAC;QAExB,IAAI,CAAC;YACH,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;YAC5B,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAC;YAErC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,cAAc,EAAE,UAAU,CAAC,MAAM;aAClC,CAAC,CAAC;YAEH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3C,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBAE1C,IAAI,CAAC;oBACH,mBAAmB;oBACnB,MAAM,WAAW,CAAC,KAAK,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;oBAC7C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,IAAI,WAAW,CAAC,CAAC;oBAEjD,oBAAoB;oBACpB,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBACpD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAErB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,IAAI,0BAA0B,CAAC,CAAC;gBAElE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;oBACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,IAAI,qCAAqC,EAAE;wBACzE,KAAK,EAAE,SAAS,CAAC,OAAO;wBACxB,cAAc,EAAE,CAAC;qBAClB,CAAC,CAAC;oBAEH,IAAI,CAAC;wBACH,MAAM,WAAW,CAAC,KAAK,CAAC,yBAAyB,IAAI,EAAE,CAAC,CAAC;wBACzD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,IAAI,GAAG,CAAC,CAAC;oBAC1D,CAAC;oBAAC,OAAO,aAAa,EAAE,CAAC;wBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,IAAI,GAAG,EAAE;4BAC7D,KAAK,EAAE,YAAY,CAAC,aAAa,CAAC,CAAC,OAAO;yBAC3C,CAAC,CAAC;wBACH,MAAM,aAAa,CAAC;oBACtB,CAAC;oBAED,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;YAED,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oDAAoD,CAAC,CAAC;YAExE,OAAO,OAAO,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACtD,KAAK,EAAE,SAAS,CAAC,OAAO;aACzB,CAAC,CAAC;YAEH,IAAI,CAAC;gBACH,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAC;gBACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC/C,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;oBAClD,KAAK,EAAE,YAAY,CAAC,aAAa,CAAC,CAAC,OAAO;iBAC3C,CAAC,CAAC;YACL,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC;gBACH,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;YAC9B,CAAC;YAAC,OAAO,YAAY,EAAE,CAAC;gBACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;oBAClD,KAAK,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC,OAAO;iBAC1C,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,gBAAgB,CACpB,SAAiD,EACjD,aAAqB,CAAC,EACtB,aAAqB,IAAI;QAEzB,IAAI,SAAgB,CAAC;QAErB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,OAAO,IAAI,UAAU,EAAE,CAAC,CAAC;gBAE5E,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YAEpD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBAEtE,sEAAsE;gBACtE,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBAEjD,IAAI,CAAC,WAAW,IAAI,OAAO,KAAK,UAAU,EAAE,CAAC;oBAC3C,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;oBACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,OAAO,WAAW,EAAE;wBAChE,KAAK,EAAE,SAAS,CAAC,OAAO;wBACxB,WAAW;wBACX,YAAY,EAAE,OAAO,KAAK,UAAU;qBACrC,CAAC,CAAC;oBACH,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;gBACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,UAAU,IAAI,EAAE;oBAClE,KAAK,EAAE,SAAS,CAAC,OAAO;oBACxB,OAAO;oBACP,UAAU;iBACX,CAAC,CAAC;gBAEH,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;QAED,MAAM,SAAS,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACK,gBAAgB,CAAC,KAAU;QACjC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,4CAA4C;QAC5C,MAAM,mBAAmB,GAAG;YAC1B,OAAO,EAAE,wBAAwB;YACjC,OAAO,EAAE,oBAAoB;YAC7B,OAAO,EAAE,uBAAuB;YAChC,OAAO,EAAE,qBAAqB;YAC9B,OAAO,EAAE,8CAA8C;SACxD,CAAC;QAEF,OAAO,mBAAmB,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;OASzC,CAAC,CAAC;YAEH,OAAO;gBACL,GAAG,KAAK,CAAC,CAAC,CAAC;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACxD,KAAK,EAAE,SAAS,CAAC,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;gBACL,KAAK,EAAE,2CAA2C;gBAClD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,qBAAqB;QACzB,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;;;;OAclC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,KAAK,EAAE,SAAS,CAAC,OAAO;aACzB,CAAC,CAAC;YACH,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF,CAAA;AAjVY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IAMR,WAAA,IAAA,0BAAgB,GAAE,CAAA;yDAAa,oBAAU,oBAAV,oBAAU;GALjC,kBAAkB,CAiV9B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\transaction.manager.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { InjectDataSource } from '@nestjs/typeorm';\r\nimport { DataSource, QueryRunner, EntityManager } from 'typeorm';\r\n\r\n// Helper function to safely extract error information\r\nconst getErrorInfo = (error: unknown) => ({\r\n  message: error instanceof Error ? error.message : String(error),\r\n  stack: error instanceof Error ? error.stack : undefined,\r\n});\r\n\r\n/**\r\n * Transaction manager for handling database transactions\r\n * Provides utilities for transaction management, rollback, and error handling\r\n */\r\n@Injectable()\r\nexport class TransactionManager {\r\n  private readonly logger = new Logger(TransactionManager.name);\r\n  private readonly dataSource: DataSource;\r\n\r\n  constructor(\r\n    @InjectDataSource() dataSource: DataSource,\r\n  ) {\r\n    this.dataSource = dataSource;\r\n  }\r\n\r\n  /**\r\n   * Execute a function within a database transaction\r\n   * Automatically handles commit/rollback based on success/failure\r\n   * \r\n   * @param operation Function to execute within transaction\r\n   * @param isolationLevel Transaction isolation level\r\n   * @returns Promise<T> Result of the operation\r\n   */\r\n  async executeInTransaction<T>(\r\n    operation: (manager: EntityManager) => Promise<T>,\r\n    isolationLevel?: 'READ UNCOMMITTED' | 'READ COMMITTED' | 'REPEATABLE READ' | 'SERIALIZABLE'\r\n  ): Promise<T> {\r\n    const queryRunner = this.dataSource.createQueryRunner();\r\n    \r\n    try {\r\n      await queryRunner.connect();\r\n      \r\n      if (isolationLevel) {\r\n        await queryRunner.startTransaction(isolationLevel);\r\n      } else {\r\n        await queryRunner.startTransaction();\r\n      }\r\n\r\n      this.logger.debug('Transaction started', {\r\n        isolationLevel: isolationLevel || 'default',\r\n        connectionId: queryRunner.connection.options.database,\r\n      });\r\n\r\n      const result = await operation(queryRunner.manager);\r\n\r\n      await queryRunner.commitTransaction();\r\n      \r\n      this.logger.debug('Transaction committed successfully');\r\n      return result;\r\n\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error('Transaction failed, rolling back', {\r\n        error: errorInfo.message,\r\n        stack: errorInfo.stack,\r\n      });\r\n\r\n      try {\r\n        await queryRunner.rollbackTransaction();\r\n        this.logger.debug('Transaction rolled back successfully');\r\n      } catch (rollbackError) {\r\n        this.logger.error('Failed to rollback transaction', {\r\n          error: getErrorInfo(rollbackError).message,\r\n        });\r\n      }\r\n\r\n      throw error;\r\n    } finally {\r\n      try {\r\n        await queryRunner.release();\r\n        this.logger.debug('Query runner released');\r\n      } catch (releaseError) {\r\n        this.logger.error('Failed to release query runner', {\r\n          error: getErrorInfo(releaseError).message,\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute multiple operations in a single transaction\r\n   * All operations must succeed or all will be rolled back\r\n   * \r\n   * @param operations Array of operations to execute\r\n   * @param isolationLevel Transaction isolation level\r\n   * @returns Promise<T[]> Array of operation results\r\n   */\r\n  async executeMultipleInTransaction<T>(\r\n    operations: Array<(manager: EntityManager) => Promise<T>>,\r\n    isolationLevel?: 'READ UNCOMMITTED' | 'READ COMMITTED' | 'REPEATABLE READ' | 'SERIALIZABLE'\r\n  ): Promise<T[]> {\r\n    return this.executeInTransaction(async (manager) => {\r\n      const results: T[] = [];\r\n      \r\n      for (let i = 0; i < operations.length; i++) {\r\n        try {\r\n          this.logger.debug(`Executing operation ${i + 1}/${operations.length}`);\r\n          const result = await operations[i](manager);\r\n          results.push(result);\r\n        } catch (error) {\r\n          const errorInfo = getErrorInfo(error);\r\n          this.logger.error(`Operation ${i + 1} failed`, {\r\n            error: errorInfo.message,\r\n            operationIndex: i,\r\n          });\r\n          throw error;\r\n        }\r\n      }\r\n      \r\n      return results;\r\n    }, isolationLevel);\r\n  }\r\n\r\n  /**\r\n   * Execute a batch of operations with savepoints\r\n   * Allows partial rollback to specific savepoints\r\n   * \r\n   * @param operations Array of operations with savepoint names\r\n   * @returns Promise<T[]> Array of operation results\r\n   */\r\n  async executeWithSavepoints<T>(\r\n    operations: Array<{\r\n      name: string;\r\n      operation: (manager: EntityManager) => Promise<T>;\r\n    }>\r\n  ): Promise<T[]> {\r\n    const queryRunner = this.dataSource.createQueryRunner();\r\n    const results: T[] = [];\r\n    \r\n    try {\r\n      await queryRunner.connect();\r\n      await queryRunner.startTransaction();\r\n\r\n      this.logger.debug('Transaction with savepoints started', {\r\n        operationCount: operations.length,\r\n      });\r\n\r\n      for (let i = 0; i < operations.length; i++) {\r\n        const { name, operation } = operations[i];\r\n        \r\n        try {\r\n          // Create savepoint\r\n          await queryRunner.query(`SAVEPOINT ${name}`);\r\n          this.logger.debug(`Savepoint '${name}' created`);\r\n\r\n          // Execute operation\r\n          const result = await operation(queryRunner.manager);\r\n          results.push(result);\r\n\r\n          this.logger.debug(`Operation '${name}' completed successfully`);\r\n\r\n        } catch (error) {\r\n          const errorInfo = getErrorInfo(error);\r\n          this.logger.error(`Operation '${name}' failed, rolling back to savepoint`, {\r\n            error: errorInfo.message,\r\n            operationIndex: i,\r\n          });\r\n\r\n          try {\r\n            await queryRunner.query(`ROLLBACK TO SAVEPOINT ${name}`);\r\n            this.logger.debug(`Rolled back to savepoint '${name}'`);\r\n          } catch (rollbackError) {\r\n            this.logger.error(`Failed to rollback to savepoint '${name}'`, {\r\n              error: getErrorInfo(rollbackError).message,\r\n            });\r\n            throw rollbackError;\r\n          }\r\n\r\n          throw error;\r\n        }\r\n      }\r\n\r\n      await queryRunner.commitTransaction();\r\n      this.logger.debug('Transaction with savepoints committed successfully');\r\n      \r\n      return results;\r\n\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error('Transaction with savepoints failed', {\r\n        error: errorInfo.message,\r\n      });\r\n\r\n      try {\r\n        await queryRunner.rollbackTransaction();\r\n        this.logger.debug('Transaction rolled back');\r\n      } catch (rollbackError) {\r\n        this.logger.error('Failed to rollback transaction', {\r\n          error: getErrorInfo(rollbackError).message,\r\n        });\r\n      }\r\n\r\n      throw error;\r\n    } finally {\r\n      try {\r\n        await queryRunner.release();\r\n      } catch (releaseError) {\r\n        this.logger.error('Failed to release query runner', {\r\n          error: getErrorInfo(releaseError).message,\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute operation with retry logic in case of deadlocks\r\n   * \r\n   * @param operation Function to execute\r\n   * @param maxRetries Maximum number of retry attempts\r\n   * @param retryDelay Delay between retries in milliseconds\r\n   * @returns Promise<T> Result of the operation\r\n   */\r\n  async executeWithRetry<T>(\r\n    operation: (manager: EntityManager) => Promise<T>,\r\n    maxRetries: number = 3,\r\n    retryDelay: number = 1000\r\n  ): Promise<T> {\r\n    let lastError: Error;\r\n\r\n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\r\n      try {\r\n        this.logger.debug(`Executing transaction attempt ${attempt}/${maxRetries}`);\r\n        \r\n        return await this.executeInTransaction(operation);\r\n\r\n      } catch (error) {\r\n        lastError = error instanceof Error ? error : new Error(String(error));\r\n        \r\n        // Check if error is retryable (deadlock, serialization failure, etc.)\r\n        const isRetryable = this.isRetryableError(error);\r\n        \r\n        if (!isRetryable || attempt === maxRetries) {\r\n          const errorInfo = getErrorInfo(error);\r\n          this.logger.error(`Transaction failed after ${attempt} attempts`, {\r\n            error: errorInfo.message,\r\n            isRetryable,\r\n            finalAttempt: attempt === maxRetries,\r\n          });\r\n          throw error;\r\n        }\r\n\r\n        const errorInfo = getErrorInfo(error);\r\n        this.logger.warn(`Transaction failed, retrying in ${retryDelay}ms`, {\r\n          error: errorInfo.message,\r\n          attempt,\r\n          maxRetries,\r\n        });\r\n\r\n        await new Promise(resolve => setTimeout(resolve, retryDelay));\r\n      }\r\n    }\r\n\r\n    throw lastError;\r\n  }\r\n\r\n  /**\r\n   * Check if an error is retryable (deadlock, serialization failure, etc.)\r\n   * \r\n   * @param error Error to check\r\n   * @returns boolean True if error is retryable\r\n   */\r\n  private isRetryableError(error: any): boolean {\r\n    if (!error.code) {\r\n      return false;\r\n    }\r\n\r\n    // PostgreSQL error codes that are retryable\r\n    const retryableErrorCodes = [\r\n      '40001', // serialization_failure\r\n      '40P01', // deadlock_detected\r\n      '53300', // too_many_connections\r\n      '08006', // connection_failure\r\n      '08001', // sqlclient_unable_to_establish_sqlconnection\r\n    ];\r\n\r\n    return retryableErrorCodes.includes(error.code);\r\n  }\r\n\r\n  /**\r\n   * Get transaction statistics\r\n   * \r\n   * @returns Promise<Record<string, any>> Transaction statistics\r\n   */\r\n  async getTransactionStats(): Promise<Record<string, any>> {\r\n    try {\r\n      const stats = await this.dataSource.query(`\r\n        SELECT \r\n          xact_commit as committed_transactions,\r\n          xact_rollback as rolled_back_transactions,\r\n          deadlocks,\r\n          temp_files,\r\n          temp_bytes\r\n        FROM pg_stat_database \r\n        WHERE datname = current_database()\r\n      `);\r\n\r\n      return {\r\n        ...stats[0],\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error('Failed to get transaction statistics', {\r\n        error: errorInfo.message,\r\n      });\r\n      return {\r\n        error: 'Failed to retrieve transaction statistics',\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get current active transactions\r\n   * \r\n   * @returns Promise<any[]> List of active transactions\r\n   */\r\n  async getActiveTransactions(): Promise<any[]> {\r\n    try {\r\n      return await this.dataSource.query(`\r\n        SELECT \r\n          pid,\r\n          usename,\r\n          application_name,\r\n          client_addr,\r\n          state,\r\n          query_start,\r\n          xact_start,\r\n          query\r\n        FROM pg_stat_activity \r\n        WHERE state IN ('active', 'idle in transaction')\r\n        AND pid != pg_backend_pid()\r\n        ORDER BY xact_start\r\n      `);\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error('Failed to get active transactions', {\r\n        error: errorInfo.message,\r\n      });\r\n      return [];\r\n    }\r\n  }\r\n}\r\n"], "version": 3}