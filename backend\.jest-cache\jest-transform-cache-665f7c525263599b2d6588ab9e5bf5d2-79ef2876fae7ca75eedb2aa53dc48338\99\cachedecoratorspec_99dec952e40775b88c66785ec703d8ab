95b89b370de45edef569c9afc7671cf9
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
const cache_decorator_1 = require("../../decorators/cache.decorator");
const cache_strategy_1 = require("../../patterns/cache-strategy");
describe('Cache Decorator', () => {
    let cacheStrategy;
    beforeEach(() => {
        cacheStrategy = new cache_strategy_1.InMemoryCacheStrategy({
            defaultTtl: 60000, // 1 minute
            maxSize: 100,
        });
    });
    afterEach(() => {
        cacheStrategy.destroy();
    });
    describe('@Cache decorator', () => {
        it('should cache method results', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                constructor() {
                    this.cacheStrategy = cacheStrategy;
                }
                async expensiveOperation(input) {
                    callCount++;
                    return `result-${input}-${callCount}`;
                }
            }
            __decorate([
                (0, cache_decorator_1.Cache)({ ttl: 30000 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "expensiveOperation", null);
            const service = new TestService();
            // First call should execute method
            const result1 = await service.expensiveOperation('test');
            expect(result1).toBe('result-test-1');
            expect(callCount).toBe(1);
            // Second call should return cached result
            const result2 = await service.expensiveOperation('test');
            expect(result2).toBe('result-test-1'); // Same result as first call
            expect(callCount).toBe(1); // Method not called again
        });
        it('should use different cache keys for different arguments', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                constructor() {
                    this.cacheStrategy = cacheStrategy;
                }
                async methodWithArgs(arg1, arg2) {
                    callCount++;
                    return `${arg1}-${arg2}-${callCount}`;
                }
            }
            __decorate([
                (0, cache_decorator_1.Cache)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String, Number]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "methodWithArgs", null);
            const service = new TestService();
            const result1 = await service.methodWithArgs('a', 1);
            const result2 = await service.methodWithArgs('a', 2);
            const result3 = await service.methodWithArgs('a', 1); // Same as first call
            expect(result1).toBe('a-1-1');
            expect(result2).toBe('a-2-2');
            expect(result3).toBe('a-1-1'); // Cached result
            expect(callCount).toBe(2); // Only two unique calls
        });
        it('should use custom key generator', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                constructor() {
                    this.cacheStrategy = cacheStrategy;
                }
                async customKeyMethod(id, data) {
                    callCount++;
                    return `result-${id}-${callCount}`;
                }
            }
            __decorate([
                (0, cache_decorator_1.Cache)({
                    key: (args) => `custom-${args[0]}`,
                    ttl: 30000
                }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String, Object]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "customKeyMethod", null);
            const service = new TestService();
            const result1 = await service.customKeyMethod('123', { prop: 'value1' });
            const result2 = await service.customKeyMethod('123', { prop: 'value2' }); // Different data, same ID
            expect(result1).toBe('result-123-1');
            expect(result2).toBe('result-123-1'); // Cached result despite different data
            expect(callCount).toBe(1);
        });
        it('should execute method when no cache strategy available', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                // No cache strategy
                async uncachedMethod(input) {
                    callCount++;
                    return `result-${input}-${callCount}`;
                }
            }
            __decorate([
                (0, cache_decorator_1.Cache)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "uncachedMethod", null);
            const service = new TestService();
            const result1 = await service.uncachedMethod('test');
            const result2 = await service.uncachedMethod('test');
            expect(result1).toBe('result-test-1');
            expect(result2).toBe('result-test-2'); // Not cached
            expect(callCount).toBe(2);
        });
        it('should handle cache errors gracefully', async () => {
            var _a;
            let callCount = 0;
            const failingCache = {
                get: jest.fn().mockRejectedValue(new Error('Cache get failed')),
                set: jest.fn().mockRejectedValue(new Error('Cache set failed')),
            };
            class TestService {
                constructor() {
                    this.cacheStrategy = failingCache;
                }
                async methodWithFailingCache(input) {
                    callCount++;
                    return `result-${input}-${callCount}`;
                }
            }
            __decorate([
                (0, cache_decorator_1.Cache)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "methodWithFailingCache", null);
            const service = new TestService();
            const result1 = await service.methodWithFailingCache('test');
            const result2 = await service.methodWithFailingCache('test');
            expect(result1).toBe('result-test-1');
            expect(result2).toBe('result-test-2'); // Not cached due to errors
            expect(callCount).toBe(2);
        });
        it('should respect TTL expiration', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                constructor() {
                    this.cacheStrategy = cacheStrategy;
                }
                async shortTtlMethod(input) {
                    callCount++;
                    return `result-${input}-${callCount}`;
                }
            }
            __decorate([
                (0, cache_decorator_1.Cache)({ ttl: 50 }) // 50ms TTL
                ,
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "shortTtlMethod", null);
            const service = new TestService();
            // First call
            const result1 = await service.shortTtlMethod('test');
            expect(result1).toBe('result-test-1');
            expect(callCount).toBe(1);
            // Second call immediately - should be cached
            const result2 = await service.shortTtlMethod('test');
            expect(result2).toBe('result-test-1');
            expect(callCount).toBe(1);
            // Wait for TTL to expire
            await new Promise(resolve => setTimeout(resolve, 60));
            // Third call after expiration - should execute method again
            const result3 = await service.shortTtlMethod('test');
            expect(result3).toBe('result-test-2');
            expect(callCount).toBe(2);
        });
    });
    describe('@CacheInvalidate decorator', () => {
        it('should invalidate specific cache keys', async () => {
            var _a, _b;
            let callCount = 0;
            class TestService {
                constructor() {
                    this.cacheStrategy = cacheStrategy;
                }
                async getData(id) {
                    callCount++;
                    return `data-${id}-${callCount}`;
                }
                async updateData(id) {
                    // Update operation
                }
            }
            __decorate([
                (0, cache_decorator_1.Cache)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "getData", null);
            __decorate([
                (0, cache_decorator_1.CacheInvalidate)('TestService.getData:["test"]'),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
            ], TestService.prototype, "updateData", null);
            const service = new TestService();
            // Cache some data
            const result1 = await service.getData('test');
            expect(result1).toBe('data-test-1');
            expect(callCount).toBe(1);
            // Verify it's cached
            const result2 = await service.getData('test');
            expect(result2).toBe('data-test-1');
            expect(callCount).toBe(1);
            // Invalidate cache
            await service.updateData('test');
            // Should fetch fresh data
            const result3 = await service.getData('test');
            expect(result3).toBe('data-test-2');
            expect(callCount).toBe(2);
        });
        it('should invalidate multiple cache keys', async () => {
            var _a;
            class TestService {
                constructor() {
                    this.cacheStrategy = cacheStrategy;
                }
                async invalidateMultiple() {
                    // Operation that invalidates multiple keys
                }
            }
            __decorate([
                (0, cache_decorator_1.CacheInvalidate)(['key1', 'key2', 'key3']),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "invalidateMultiple", null);
            // Pre-populate cache
            await cacheStrategy.set('key1', 'value1');
            await cacheStrategy.set('key2', 'value2');
            await cacheStrategy.set('key3', 'value3');
            expect(await cacheStrategy.has('key1')).toBe(true);
            expect(await cacheStrategy.has('key2')).toBe(true);
            expect(await cacheStrategy.has('key3')).toBe(true);
            const service = new TestService();
            await service.invalidateMultiple();
            expect(await cacheStrategy.has('key1')).toBe(false);
            expect(await cacheStrategy.has('key2')).toBe(false);
            expect(await cacheStrategy.has('key3')).toBe(false);
        });
        it('should use dynamic key generator', async () => {
            var _a;
            class TestService {
                constructor() {
                    this.cacheStrategy = cacheStrategy;
                }
                async invalidateDynamic(id) {
                    // Operation
                }
            }
            __decorate([
                (0, cache_decorator_1.CacheInvalidate)((args) => `dynamic-key-${args[0]}`),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "invalidateDynamic", null);
            // Pre-populate cache
            await cacheStrategy.set('dynamic-key-123', 'value');
            expect(await cacheStrategy.has('dynamic-key-123')).toBe(true);
            const service = new TestService();
            await service.invalidateDynamic('123');
            expect(await cacheStrategy.has('dynamic-key-123')).toBe(false);
        });
        it('should handle cache invalidation errors gracefully', async () => {
            var _a;
            const failingCache = {
                delete: jest.fn().mockRejectedValue(new Error('Delete failed')),
            };
            class TestService {
                constructor() {
                    this.cacheStrategy = failingCache;
                }
                async methodWithFailingInvalidation() {
                    return 'success';
                }
            }
            __decorate([
                (0, cache_decorator_1.CacheInvalidate)('some-key'),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "methodWithFailingInvalidation", null);
            const service = new TestService();
            // Should not throw error even if invalidation fails
            const result = await service.methodWithFailingInvalidation();
            expect(result).toBe('success');
            expect(failingCache.delete).toHaveBeenCalledWith('some-key');
        });
    });
    describe('@CacheEvictAll decorator', () => {
        it('should clear all cache entries for the class', async () => {
            var _a;
            class TestService {
                constructor() {
                    this.cacheStrategy = cacheStrategy;
                }
                async clearAllCache() {
                    // Operation that clears all cache
                }
            }
            __decorate([
                (0, cache_decorator_1.CacheEvictAll)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "clearAllCache", null);
            // Pre-populate cache with various keys
            await cacheStrategy.set('TestService.method1', 'value1');
            await cacheStrategy.set('TestService.method2', 'value2');
            await cacheStrategy.set('OtherService.method', 'value3');
            const service = new TestService();
            await service.clearAllCache();
            // TestService cache should be cleared, but OtherService should remain
            expect(await cacheStrategy.has('TestService.method1')).toBe(false);
            expect(await cacheStrategy.has('TestService.method2')).toBe(false);
            expect(await cacheStrategy.has('OtherService.method')).toBe(true);
        });
        it('should handle cache eviction errors gracefully', async () => {
            var _a;
            const failingCache = {
                clear: jest.fn().mockRejectedValue(new Error('Clear failed')),
            };
            class TestService {
                constructor() {
                    this.cacheStrategy = failingCache;
                }
                async methodWithFailingEviction() {
                    return 'success';
                }
            }
            __decorate([
                (0, cache_decorator_1.CacheEvictAll)(),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "methodWithFailingEviction", null);
            const service = new TestService();
            // Should not throw error even if eviction fails
            const result = await service.methodWithFailingEviction();
            expect(result).toBe('success');
            expect(failingCache.clear).toHaveBeenCalledWith('TestService.*');
        });
    });
    describe('integration scenarios', () => {
        it('should work with multiple decorators', async () => {
            var _a, _b;
            let callCount = 0;
            class TestService {
                constructor() {
                    this.cacheStrategy = cacheStrategy;
                }
                async cachedMethod(id) {
                    callCount++;
                    return `cached-${id}-${callCount}`;
                }
                async invalidateMethod(id) {
                    // Invalidation operation
                }
            }
            __decorate([
                (0, cache_decorator_1.Cache)({ ttl: 30000 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "cachedMethod", null);
            __decorate([
                (0, cache_decorator_1.CacheInvalidate)((args) => `TestService.cachedMethod:["${args[0]}"]`),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
            ], TestService.prototype, "invalidateMethod", null);
            const service = new TestService();
            // Cache data
            const result1 = await service.cachedMethod('test');
            expect(result1).toBe('cached-test-1');
            expect(callCount).toBe(1);
            // Verify cached
            const result2 = await service.cachedMethod('test');
            expect(result2).toBe('cached-test-1');
            expect(callCount).toBe(1);
            // Invalidate
            await service.invalidateMethod('test');
            // Should fetch fresh data
            const result3 = await service.cachedMethod('test');
            expect(result3).toBe('cached-test-2');
            expect(callCount).toBe(2);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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