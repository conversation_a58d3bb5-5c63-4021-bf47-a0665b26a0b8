53179d5f7ddcec86deb26774ef53fd41
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventTimestamp = void 0;
const base_value_object_1 = require("../../../../shared-kernel/value-objects/base-value-object");
/**
 * Event Timestamp Value Object
 *
 * Represents timing information for security events with high precision and forensic capabilities.
 * Tracks event occurrence, receipt, and processing times for accurate timeline analysis.
 *
 * Key features:
 * - High-precision timestamp tracking
 * - Multiple timestamp types (occurred, received, processed)
 * - Timezone awareness and conversion
 * - Original timestamp preservation for forensics
 * - Time drift detection and analysis
 * - Event aging and freshness calculations
 */
class EventTimestamp extends base_value_object_1.BaseValueObject {
    constructor(props) {
        super(props);
    }
    validate() {
        if (!this._value.occurredAt) {
            throw new Error('Event timestamp must have an occurredAt date');
        }
        if (!(this._value.occurredAt instanceof Date)) {
            throw new Error('occurredAt must be a valid Date object');
        }
        if (isNaN(this._value.occurredAt.getTime())) {
            throw new Error('occurredAt must be a valid date');
        }
        // Validate receivedAt if provided
        if (this._value.receivedAt) {
            if (!(this._value.receivedAt instanceof Date) || isNaN(this._value.receivedAt.getTime())) {
                throw new Error('receivedAt must be a valid Date object');
            }
        }
        // Validate processedAt if provided
        if (this._value.processedAt) {
            if (!(this._value.processedAt instanceof Date) || isNaN(this._value.processedAt.getTime())) {
                throw new Error('processedAt must be a valid Date object');
            }
        }
        // Validate timezone offset
        if (this._value.timezoneOffset !== undefined) {
            if (!Number.isInteger(this._value.timezoneOffset) ||
                this._value.timezoneOffset < -720 ||
                this._value.timezoneOffset > 720) {
                throw new Error('Timezone offset must be an integer between -720 and 720 minutes');
            }
        }
        // Check for reasonable timestamp bounds
        const now = new Date();
        const maxFuture = new Date(now.getTime() + EventTimestamp.MAX_FUTURE_TOLERANCE_MS);
        const maxPast = new Date(now.getTime() - EventTimestamp.MAX_PAST_TOLERANCE_MS);
        if (this._value.occurredAt > maxFuture) {
            throw new Error('Event timestamp cannot be more than 5 minutes in the future');
        }
        if (this._value.occurredAt < maxPast) {
            throw new Error('Event timestamp cannot be more than 1 year in the past');
        }
        // Validate timestamp ordering
        if (this._value.receivedAt && this._value.occurredAt > this._value.receivedAt) {
            // Allow small tolerance for clock skew
            const skew = this._value.occurredAt.getTime() - this._value.receivedAt.getTime();
            if (skew > 60000) { // 1 minute tolerance
                throw new Error('Event occurredAt cannot be significantly after receivedAt');
            }
        }
        if (this._value.processedAt && this._value.receivedAt &&
            this._value.receivedAt > this._value.processedAt) {
            throw new Error('Event receivedAt cannot be after processedAt');
        }
    }
    /**
     * Create an event timestamp for the current time
     */
    static create(options) {
        const now = new Date();
        return new EventTimestamp({
            occurredAt: now,
            receivedAt: now,
            timezoneOffset: now.getTimezoneOffset(),
            precision: 'millisecond',
            ...options,
        });
    }
    /**
     * Create an event timestamp from a specific date
     */
    static fromDate(occurredAt, options) {
        return new EventTimestamp({
            occurredAt,
            receivedAt: new Date(),
            timezoneOffset: occurredAt.getTimezoneOffset(),
            precision: 'millisecond',
            ...options,
        });
    }
    /**
     * Create an event timestamp from ISO string
     */
    static fromISOString(isoString, options) {
        const occurredAt = new Date(isoString);
        return EventTimestamp.fromDate(occurredAt, {
            originalTimestamp: isoString,
            ...options,
        });
    }
    /**
     * Create an event timestamp from Unix timestamp
     */
    static fromUnixTimestamp(timestamp, options) {
        const occurredAt = new Date(timestamp * 1000);
        return EventTimestamp.fromDate(occurredAt, {
            originalTimestamp: timestamp.toString(),
            ...options,
        });
    }
    /**
     * Create an event timestamp with original string preservation
     */
    static fromOriginalString(originalTimestamp, options) {
        const occurredAt = new Date(originalTimestamp);
        return new EventTimestamp({
            occurredAt,
            originalTimestamp,
            receivedAt: new Date(),
            timezoneOffset: occurredAt.getTimezoneOffset(),
            precision: 'millisecond',
            ...options,
        });
    }
    /**
     * Get when the event occurred
     */
    get occurredAt() {
        return new Date(this._value.occurredAt.getTime());
    }
    /**
     * Get when the event was received
     */
    get receivedAt() {
        return this._value.receivedAt ? new Date(this._value.receivedAt.getTime()) : undefined;
    }
    /**
     * Get when the event was processed
     */
    get processedAt() {
        return this._value.processedAt ? new Date(this._value.processedAt.getTime()) : undefined;
    }
    /**
     * Get timezone offset in minutes
     */
    get timezoneOffset() {
        return this._value.timezoneOffset;
    }
    /**
     * Get original timestamp string
     */
    get originalTimestamp() {
        return this._value.originalTimestamp;
    }
    /**
     * Get timestamp precision
     */
    get precision() {
        return this._value.precision || 'millisecond';
    }
    /**
     * Get event age in milliseconds
     */
    getAge() {
        return Date.now() - this._value.occurredAt.getTime();
    }
    /**
     * Get event age in seconds
     */
    getAgeInSeconds() {
        return Math.floor(this.getAge() / 1000);
    }
    /**
     * Get event age in minutes
     */
    getAgeInMinutes() {
        return Math.floor(this.getAge() / (1000 * 60));
    }
    /**
     * Get event age in hours
     */
    getAgeInHours() {
        return Math.floor(this.getAge() / (1000 * 60 * 60));
    }
    /**
     * Check if event is recent (within specified time)
     */
    isRecent(withinMs = 300000) {
        return this.getAge() <= withinMs;
    }
    /**
     * Check if event is stale (older than specified time)
     */
    isStale(olderThanMs = 86400000) {
        return this.getAge() > olderThanMs;
    }
    /**
     * Check if event occurred in the future
     */
    isInFuture() {
        return this._value.occurredAt.getTime() > Date.now();
    }
    /**
     * Get processing delay (time between occurrence and receipt)
     */
    getProcessingDelay() {
        if (!this._value.receivedAt) {
            return null;
        }
        return this._value.receivedAt.getTime() - this._value.occurredAt.getTime();
    }
    /**
     * Get ingestion delay (time between receipt and processing)
     */
    getIngestionDelay() {
        if (!this._value.receivedAt || !this._value.processedAt) {
            return null;
        }
        return this._value.processedAt.getTime() - this._value.receivedAt.getTime();
    }
    /**
     * Get total delay (time between occurrence and processing)
     */
    getTotalDelay() {
        if (!this._value.processedAt) {
            return null;
        }
        return this._value.processedAt.getTime() - this._value.occurredAt.getTime();
    }
    /**
     * Check if there's significant clock skew
     */
    hasClockSkew(toleranceMs = 60000) {
        const delay = this.getProcessingDelay();
        return delay !== null && Math.abs(delay) > toleranceMs;
    }
    /**
     * Convert to UTC
     */
    toUTC() {
        if (this._value.timezoneOffset === undefined) {
            return this.occurredAt;
        }
        const utcTime = this._value.occurredAt.getTime() + (this._value.timezoneOffset * 60000);
        return new Date(utcTime);
    }
    /**
     * Convert to specific timezone
     */
    toTimezone(offsetMinutes) {
        const utc = this.toUTC();
        return new Date(utc.getTime() - (offsetMinutes * 60000));
    }
    /**
     * Get ISO string representation
     */
    toISOString() {
        return this._value.occurredAt.toISOString();
    }
    /**
     * Get Unix timestamp
     */
    toUnixTimestamp() {
        return Math.floor(this._value.occurredAt.getTime() / 1000);
    }
    /**
     * Mark as received now
     */
    markAsReceived() {
        return new EventTimestamp({
            ...this._value,
            receivedAt: new Date(),
        });
    }
    /**
     * Mark as processed now
     */
    markAsProcessed() {
        return new EventTimestamp({
            ...this._value,
            processedAt: new Date(),
        });
    }
    /**
     * Update with processing timestamps
     */
    withProcessingTimestamps(receivedAt, processedAt) {
        return new EventTimestamp({
            ...this._value,
            receivedAt: receivedAt || this._value.receivedAt,
            processedAt: processedAt || this._value.processedAt,
        });
    }
    /**
     * Get timestamp summary for analysis
     */
    getTimingSummary() {
        return {
            occurredAt: this._value.occurredAt.toISOString(),
            receivedAt: this._value.receivedAt?.toISOString(),
            processedAt: this._value.processedAt?.toISOString(),
            age: this.getAge(),
            processingDelay: this.getProcessingDelay() || undefined,
            ingestionDelay: this.getIngestionDelay() || undefined,
            totalDelay: this.getTotalDelay() || undefined,
            hasClockSkew: this.hasClockSkew(),
            isRecent: this.isRecent(),
            isStale: this.isStale(),
        };
    }
    /**
     * Compare timestamps for equality
     */
    equals(other) {
        if (!other) {
            return false;
        }
        if (this === other) {
            return true;
        }
        return this._value.occurredAt.getTime() === other._value.occurredAt.getTime();
    }
    /**
     * Get string representation
     */
    toString() {
        return this._value.occurredAt.toISOString();
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            occurredAt: this._value.occurredAt.toISOString(),
            receivedAt: this._value.receivedAt?.toISOString(),
            processedAt: this._value.processedAt?.toISOString(),
            timezoneOffset: this._value.timezoneOffset,
            originalTimestamp: this._value.originalTimestamp,
            precision: this._value.precision,
            summary: this.getTimingSummary(),
        };
    }
    /**
     * Create EventTimestamp from JSON
     */
    static fromJSON(json) {
        return new EventTimestamp({
            occurredAt: new Date(json.occurredAt),
            receivedAt: json.receivedAt ? new Date(json.receivedAt) : undefined,
            processedAt: json.processedAt ? new Date(json.processedAt) : undefined,
            timezoneOffset: json.timezoneOffset,
            originalTimestamp: json.originalTimestamp,
            precision: json.precision,
        });
    }
    /**
     * Validate timestamp format without creating instance
     */
    static isValid(occurredAt) {
        try {
            new EventTimestamp({ occurredAt });
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.EventTimestamp = EventTimestamp;
EventTimestamp.MAX_FUTURE_TOLERANCE_MS = 5 * 60 * 1000; // 5 minutes
EventTimestamp.MAX_PAST_TOLERANCE_MS = 365 * 24 * 60 * 60 * 1000; // 1 year
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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