{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\threat-campaign.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAUiB;AACjB,qDAA+G;AAE/G,6EAAkE;AAClE,+DAAoD;AAEpD;;GAEG;AACH,IAAY,cAMX;AAND,WAAY,cAAc;IACxB,mCAAiB,CAAA;IACjB,uCAAqB,CAAA;IACrB,yCAAuB,CAAA;IACvB,yCAAuB,CAAA;IACvB,6DAA2C,CAAA;AAC7C,CAAC,EANW,cAAc,8BAAd,cAAc,QAMzB;AAED;;GAEG;AACH,IAAY,sBAKX;AALD,WAAY,sBAAsB;IAChC,qCAAW,CAAA;IACX,2CAAiB,CAAA;IACjB,uCAAa,CAAA;IACb,+CAAqB,CAAA;AACvB,CAAC,EALW,sBAAsB,sCAAtB,sBAAsB,QAKjC;AAED;;GAEG;AACH,IAAY,iBAUX;AAVD,WAAY,iBAAiB;IAC3B,8CAAyB,CAAA;IACzB,sDAAiC,CAAA;IACjC,4CAAuB,CAAA;IACvB,0CAAqB,CAAA;IACrB,8CAAyB,CAAA;IACzB,sDAAiC,CAAA;IACjC,oEAA+C,CAAA;IAC/C,8CAAyB,CAAA;IACzB,wCAAmB,CAAA;AACrB,CAAC,EAVW,iBAAiB,iCAAjB,iBAAiB,QAU5B;AAqCD;;;GAGG;AAMI,IAAM,cAAc,GAApB,MAAM,cAAc;IA2JzB;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM;YACrC,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,SAAS;YACxC,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,mBAAmB,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;QAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC5F,CAAC;IAED;;OAEG;IACH,IAAI,qBAAqB;QACvB,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,CAAC;QACzD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YACjD,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI;YACnC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,MAAkB;QAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QACpB,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAoB;QAClC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,wCAAwC;QACxC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;QAChF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,KAAuB;QACtC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,qBAAqB;QACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAC/B,IAAI,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,+BAA+B;QAC/B,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;YAC3B,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC;YACjC,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC;YACjC,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;YAChC,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,0BAA0B;QAC1B,MAAM,uBAAuB,GAAG;YAC9B,CAAC,sBAAsB,CAAC,GAAG,CAAC,EAAE,GAAG;YACjC,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAAE,GAAG;YACpC,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,GAAG;YAClC,CAAC,sBAAsB,CAAC,QAAQ,CAAC,EAAE,GAAG;SACvC,CAAC;QACF,KAAK,IAAI,uBAAuB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEtD,qBAAqB;QACrB,MAAM,oBAAoB,GAAG;YAC3B,iBAAiB,CAAC,QAAQ;YAC1B,iBAAiB,CAAC,UAAU;YAC5B,iBAAiB,CAAC,UAAU;YAC5B,iBAAiB,CAAC,SAAS;SAC5B,CAAC;QAEF,MAAM,sBAAsB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CACxD,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CACnC,CAAC;QAEF,IAAI,sBAAsB,EAAE,CAAC;YAC3B,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,4BAA4B;QAC5B,MAAM,eAAe,GAAG;YACtB,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,YAAY;YACZ,SAAS;YACT,oBAAoB;SACrB,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAChE,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAC/C,CAAC;QAEF,IAAI,qBAAqB,EAAE,CAAC;YAC1B,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,kBAAkB;QAClB,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;YAC9B,KAAK,IAAI,GAAG,CAAC,CAAC,wBAAwB;QACxC,CAAC;aAAM,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC;YACpC,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,kBAAkB;QAClB,IAAI,IAAI,CAAC,qBAAqB,IAAI,EAAE,EAAE,CAAC;YACrC,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,IAAI,KAAK,GAAG,GAAG,CAAC,CAAC,kBAAkB;QAEnC,qBAAqB;QACrB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,oBAAoB;QACpB,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC;YAC7B,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;aAAM,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC;YACpC,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;aAAM,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;YACnC,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,sBAAsB;QACtB,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QAC1E,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,oBAAoB;QACpB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClD,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,wBAAwB;QACxB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9C,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAAc;QAC1B,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YAC1E,KAAK,CAAC;IACV,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,OAAe;QAC5B,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC7B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YAC7E,KAAK,CAAC;IACV,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO,EAAE,CAAC;QAE7B,MAAM,YAAY,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE/C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC5B,MAAM,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACnD,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;aACtC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;aAC7C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YACjD,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI;YACnC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YAClD,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,SAAS,CAAC;QACvC,IAAI,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,QAAQ,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,mBAAmB,CAAC;IACnD,CAAC;CACF,CAAA;AAleY,wCAAc;AAEzB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;0CACpB;AAIX;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;IACxC,IAAA,0BAAQ,GAAE;;4CACE;AAKb;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;+CACS;AAInB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACxB,IAAA,0BAAQ,GAAE;;mDACS;AAQpB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,cAAc;QACpB,OAAO,EAAE,cAAc,CAAC,SAAS;KAClC,CAAC;IACD,IAAA,wBAAM,EAAC,cAAc,CAAC;;8CACA;AAQvB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,sBAAsB;QAC5B,OAAO,EAAE,sBAAsB,CAAC,MAAM;KACvC,CAAC;IACD,IAAA,wBAAM,EAAC,sBAAsB,CAAC;;sDACQ;AAQvC;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,iBAAiB;QACvB,KAAK,EAAE,IAAI;KACZ,CAAC;IACD,IAAA,yBAAO,GAAE;;kDACsB;AAIhC;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;IAC5C,IAAA,wBAAM,GAAE;kDACE,IAAI,oBAAJ,IAAI;iDAAC;AAKhB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;kDACC,IAAI,oBAAJ,IAAI;+CAAC;AAKf;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;kDACM,IAAI,oBAAJ,IAAI;oDAAC;AAKpB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;uDACiB;AAK3B;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;yDACmB;AAK7B;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;4DACsB;AAKhC;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACtD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;+CACa;AAKvB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACtD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;qDACqB;AAK/B;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACtD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;gDACoB;AAK9B;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;uDACiB;AAK3B;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;iDACW;AAKrB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDAMT;AAKF;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;kDACY;AAKtB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;4CACM;AAIhB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACvC,IAAA,0BAAQ,GAAE;;mDACS;AAIpB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACvC,IAAA,0BAAQ,GAAE;;sDACY;AAKvB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACnE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACU;AAKrB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACnE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACc;AAKzB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;kDACQ,MAAM,oBAAN,MAAM;wDAAc;AAKvC;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,iCAAW,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC5E,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC;kDAC1B,iCAAW,oBAAX,iCAAW;mDAAC;AAG1B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAClB;AAGvB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+CAAkB,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC;;0DAC9B;AAGzC;IADC,IAAA,0BAAgB,GAAE;kDACR,IAAI,oBAAJ,IAAI;iDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;kDACR,IAAI,oBAAJ,IAAI;iDAAC;yBAzJL,cAAc;IAL1B,IAAA,gBAAM,EAAC,kBAAkB,CAAC;IAC1B,IAAA,eAAK,EAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IAC9B,IAAA,eAAK,EAAC,CAAC,eAAe,CAAC,CAAC;IACxB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,wBAAwB,EAAE,CAAC;IAC1D,IAAA,eAAK,EAAC,CAAC,iBAAiB,CAAC,EAAE,EAAE,KAAK,EAAE,8BAA8B,EAAE,CAAC;GACzD,cAAc,CAke1B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\threat-campaign.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { IsEnum, IsOptional, IsString, IsNumber, IsBoolean, IsArray, IsObject, IsDate } from 'class-validator';\r\n\r\nimport { ThreatIntelligence } from './threat-intelligence.entity';\r\nimport { ThreatActor } from './threat-actor.entity';\r\n\r\n/**\r\n * Campaign status\r\n */\r\nexport enum CampaignStatus {\r\n  ACTIVE = 'active',\r\n  INACTIVE = 'inactive',\r\n  COMPLETED = 'completed',\r\n  SUSPECTED = 'suspected',\r\n  UNDER_INVESTIGATION = 'under_investigation',\r\n}\r\n\r\n/**\r\n * Campaign sophistication levels\r\n */\r\nexport enum CampaignSophistication {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  ADVANCED = 'advanced',\r\n}\r\n\r\n/**\r\n * Campaign objectives\r\n */\r\nexport enum CampaignObjective {\r\n  DATA_THEFT = 'data_theft',\r\n  FINANCIAL_GAIN = 'financial_gain',\r\n  ESPIONAGE = 'espionage',\r\n  SABOTAGE = 'sabotage',\r\n  DISRUPTION = 'disruption',\r\n  RECONNAISSANCE = 'reconnaissance',\r\n  CREDENTIAL_HARVESTING = 'credential_harvesting',\r\n  RANSOMWARE = 'ransomware',\r\n  UNKNOWN = 'unknown',\r\n}\r\n\r\n/**\r\n * Timeline information for campaign phases\r\n */\r\nexport interface CampaignTimeline {\r\n  phase: string;\r\n  startDate: Date;\r\n  endDate?: Date;\r\n  description: string;\r\n  indicators?: string[];\r\n}\r\n\r\n/**\r\n * Victim information\r\n */\r\nexport interface VictimInfo {\r\n  organizationName?: string;\r\n  sector: string;\r\n  country: string;\r\n  size?: 'small' | 'medium' | 'large' | 'enterprise';\r\n  impactLevel: 'low' | 'medium' | 'high' | 'critical';\r\n  discoveredDate: Date;\r\n  confirmedDate?: Date;\r\n}\r\n\r\n/**\r\n * Attack vector information\r\n */\r\nexport interface AttackVector {\r\n  vector: string;\r\n  description: string;\r\n  mitreId?: string;\r\n  frequency: 'high' | 'medium' | 'low';\r\n  successRate?: number;\r\n}\r\n\r\n/**\r\n * Threat Campaign entity\r\n * Represents coordinated threat activities attributed to specific actors\r\n */\r\n@Entity('threat_campaigns')\r\n@Index(['status', 'startDate'])\r\n@Index(['threatActorId'])\r\n@Index(['objectives'], { where: 'objectives IS NOT NULL' })\r\n@Index(['targetedSectors'], { where: 'targeted_sectors IS NOT NULL' })\r\nexport class ThreatCampaign {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  @Column({ type: 'varchar', length: 255 })\r\n  @IsString()\r\n  name: string;\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  aliases?: string[];\r\n\r\n  @Column({ type: 'text' })\r\n  @IsString()\r\n  description: string;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: CampaignStatus,\r\n    default: CampaignStatus.SUSPECTED,\r\n  })\r\n  @IsEnum(CampaignStatus)\r\n  status: CampaignStatus;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: CampaignSophistication,\r\n    default: CampaignSophistication.MEDIUM,\r\n  })\r\n  @IsEnum(CampaignSophistication)\r\n  sophistication: CampaignSophistication;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: CampaignObjective,\r\n    array: true,\r\n  })\r\n  @IsArray()\r\n  objectives: CampaignObjective[];\r\n\r\n  @Column({ type: 'timestamp with time zone' })\r\n  @IsDate()\r\n  startDate: Date;\r\n\r\n  @Column({ type: 'timestamp with time zone', nullable: true })\r\n  @IsOptional()\r\n  @IsDate()\r\n  endDate?: Date;\r\n\r\n  @Column({ type: 'timestamp with time zone', nullable: true })\r\n  @IsOptional()\r\n  @IsDate()\r\n  lastActivity?: Date;\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  targetedSectors?: string[];\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  targetedCountries?: string[];\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  targetedTechnologies?: string[];\r\n\r\n  @Column({ type: 'jsonb', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  victims?: VictimInfo[];\r\n\r\n  @Column({ type: 'jsonb', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  attackVectors?: AttackVector[];\r\n\r\n  @Column({ type: 'jsonb', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  timeline?: CampaignTimeline[];\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  malwareFamilies?: string[];\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  toolsUsed?: string[];\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  @IsOptional()\r\n  @IsObject()\r\n  infrastructure?: {\r\n    domains?: string[];\r\n    ipAddresses?: string[];\r\n    certificates?: string[];\r\n    emails?: string[];\r\n  };\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  references?: string[];\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  tags?: string[];\r\n\r\n  @Column({ type: 'integer', default: 0 })\r\n  @IsNumber()\r\n  victimCount: number;\r\n\r\n  @Column({ type: 'integer', default: 0 })\r\n  @IsNumber()\r\n  indicatorCount: number;\r\n\r\n  @Column({ type: 'decimal', precision: 3, scale: 1, nullable: true })\r\n  @IsOptional()\r\n  @IsNumber()\r\n  impactScore?: number;\r\n\r\n  @Column({ type: 'decimal', precision: 3, scale: 1, nullable: true })\r\n  @IsOptional()\r\n  @IsNumber()\r\n  confidenceScore?: number;\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  @IsOptional()\r\n  @IsObject()\r\n  customAttributes?: Record<string, any>;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => ThreatActor, (actor) => actor.campaigns, { nullable: true })\r\n  @JoinColumn({ name: 'threat_actor_id' })\r\n  threatActor?: ThreatActor;\r\n\r\n  @Column({ type: 'uuid', nullable: true })\r\n  threatActorId?: string;\r\n\r\n  @OneToMany(() => ThreatIntelligence, (threat) => threat.threatCampaign)\r\n  threatIntelligence: ThreatIntelligence[];\r\n\r\n  @CreateDateColumn()\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn()\r\n  updatedAt: Date;\r\n\r\n  /**\r\n   * Check if campaign is currently active\r\n   */\r\n  get isActive(): boolean {\r\n    return this.status === CampaignStatus.ACTIVE;\r\n  }\r\n\r\n  /**\r\n   * Check if campaign is ongoing (not completed)\r\n   */\r\n  get isOngoing(): boolean {\r\n    return this.status === CampaignStatus.ACTIVE || \r\n           this.status === CampaignStatus.SUSPECTED ||\r\n           this.status === CampaignStatus.UNDER_INVESTIGATION;\r\n  }\r\n\r\n  /**\r\n   * Get campaign duration in days\r\n   */\r\n  get durationInDays(): number {\r\n    const endDate = this.endDate || new Date();\r\n    return Math.floor((endDate.getTime() - this.startDate.getTime()) / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get days since last activity\r\n   */\r\n  get daysSinceLastActivity(): number {\r\n    const lastActivity = this.lastActivity || this.startDate;\r\n    return Math.floor((Date.now() - lastActivity.getTime()) / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get campaign summary\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      name: this.name,\r\n      aliases: this.aliases,\r\n      status: this.status,\r\n      sophistication: this.sophistication,\r\n      objectives: this.objectives,\r\n      isActive: this.isActive,\r\n      isOngoing: this.isOngoing,\r\n      durationInDays: this.durationInDays,\r\n      daysSinceLastActivity: this.daysSinceLastActivity,\r\n      victimCount: this.victimCount,\r\n      indicatorCount: this.indicatorCount,\r\n      impactScore: this.impactScore,\r\n      confidenceScore: this.confidenceScore,\r\n      threatActor: this.threatActor?.name,\r\n      targetedSectors: this.targetedSectors,\r\n      targetedCountries: this.targetedCountries,\r\n      tags: this.tags,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Add victim to campaign\r\n   */\r\n  addVictim(victim: VictimInfo): void {\r\n    if (!this.victims) {\r\n      this.victims = [];\r\n    }\r\n\r\n    this.victims.push(victim);\r\n    this.victimCount = this.victims.length;\r\n    this.lastActivity = new Date();\r\n  }\r\n\r\n  /**\r\n   * Add attack vector\r\n   */\r\n  addAttackVector(vector: AttackVector): void {\r\n    if (!this.attackVectors) {\r\n      this.attackVectors = [];\r\n    }\r\n\r\n    // Remove existing vector with same name\r\n    this.attackVectors = this.attackVectors.filter(v => v.vector !== vector.vector);\r\n    this.attackVectors.push(vector);\r\n  }\r\n\r\n  /**\r\n   * Add timeline phase\r\n   */\r\n  addTimelinePhase(phase: CampaignTimeline): void {\r\n    if (!this.timeline) {\r\n      this.timeline = [];\r\n    }\r\n\r\n    this.timeline.push(phase);\r\n    // Sort by start date\r\n    this.timeline.sort((a, b) => a.startDate.getTime() - b.startDate.getTime());\r\n  }\r\n\r\n  /**\r\n   * Update campaign activity\r\n   */\r\n  recordActivity(): void {\r\n    this.lastActivity = new Date();\r\n    if (this.status === CampaignStatus.INACTIVE) {\r\n      this.status = CampaignStatus.ACTIVE;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate impact score based on various factors\r\n   */\r\n  calculateImpactScore(): number {\r\n    let score = 0;\r\n\r\n    // Base score from victim count\r\n    if (this.victimCount > 100) {\r\n      score += 9.0;\r\n    } else if (this.victimCount > 50) {\r\n      score += 7.0;\r\n    } else if (this.victimCount > 10) {\r\n      score += 5.0;\r\n    } else if (this.victimCount > 0) {\r\n      score += 3.0;\r\n    }\r\n\r\n    // Sophistication modifier\r\n    const sophisticationModifiers = {\r\n      [CampaignSophistication.LOW]: 0.7,\r\n      [CampaignSophistication.MEDIUM]: 1.0,\r\n      [CampaignSophistication.HIGH]: 1.3,\r\n      [CampaignSophistication.ADVANCED]: 1.5,\r\n    };\r\n    score *= sophisticationModifiers[this.sophistication];\r\n\r\n    // Objective severity\r\n    const highImpactObjectives = [\r\n      CampaignObjective.SABOTAGE,\r\n      CampaignObjective.RANSOMWARE,\r\n      CampaignObjective.DATA_THEFT,\r\n      CampaignObjective.ESPIONAGE,\r\n    ];\r\n    \r\n    const hasHighImpactObjective = this.objectives.some(obj => \r\n      highImpactObjectives.includes(obj)\r\n    );\r\n    \r\n    if (hasHighImpactObjective) {\r\n      score += 1.0;\r\n    }\r\n\r\n    // Critical sector targeting\r\n    const criticalSectors = [\r\n      'financial',\r\n      'healthcare',\r\n      'energy',\r\n      'government',\r\n      'defense',\r\n      'telecommunications',\r\n    ];\r\n    \r\n    const targetsCriticalSector = this.targetedSectors?.some(sector => \r\n      criticalSectors.includes(sector.toLowerCase())\r\n    );\r\n    \r\n    if (targetsCriticalSector) {\r\n      score += 1.0;\r\n    }\r\n\r\n    // Duration factor\r\n    if (this.durationInDays > 365) {\r\n      score += 1.0; // Long-running campaign\r\n    } else if (this.durationInDays > 90) {\r\n      score += 0.5;\r\n    }\r\n\r\n    // Recent activity\r\n    if (this.daysSinceLastActivity <= 30) {\r\n      score += 0.5;\r\n    }\r\n\r\n    this.impactScore = Math.min(score, 10.0);\r\n    return this.impactScore;\r\n  }\r\n\r\n  /**\r\n   * Calculate confidence score based on attribution and evidence\r\n   */\r\n  calculateConfidenceScore(): number {\r\n    let score = 5.0; // Base confidence\r\n\r\n    // Attribution factor\r\n    if (this.threatActor) {\r\n      score += 2.0;\r\n    }\r\n\r\n    // Evidence quantity\r\n    if (this.indicatorCount > 50) {\r\n      score += 2.0;\r\n    } else if (this.indicatorCount > 20) {\r\n      score += 1.0;\r\n    } else if (this.indicatorCount > 5) {\r\n      score += 0.5;\r\n    }\r\n\r\n    // Victim confirmation\r\n    const confirmedVictims = this.victims?.filter(v => v.confirmedDate) || [];\r\n    if (confirmedVictims.length > 0) {\r\n      score += 1.0;\r\n    }\r\n\r\n    // Reference quality\r\n    if (this.references && this.references.length > 3) {\r\n      score += 0.5;\r\n    }\r\n\r\n    // Timeline completeness\r\n    if (this.timeline && this.timeline.length > 3) {\r\n      score += 0.5;\r\n    }\r\n\r\n    this.confidenceScore = Math.min(score, 10.0);\r\n    return this.confidenceScore;\r\n  }\r\n\r\n  /**\r\n   * Check if campaign targets specific sector\r\n   */\r\n  targetsSector(sector: string): boolean {\r\n    return this.targetedSectors ? \r\n      this.targetedSectors.some(s => s.toLowerCase() === sector.toLowerCase()) : \r\n      false;\r\n  }\r\n\r\n  /**\r\n   * Check if campaign targets specific country\r\n   */\r\n  targetsCountry(country: string): boolean {\r\n    return this.targetedCountries ? \r\n      this.targetedCountries.some(c => c.toLowerCase() === country.toLowerCase()) : \r\n      false;\r\n  }\r\n\r\n  /**\r\n   * Get most impacted sectors\r\n   */\r\n  getMostImpactedSectors(): Array<{ sector: string; count: number }> {\r\n    if (!this.victims) return [];\r\n\r\n    const sectorCounts = new Map<string, number>();\r\n    \r\n    this.victims.forEach(victim => {\r\n      const count = sectorCounts.get(victim.sector) || 0;\r\n      sectorCounts.set(victim.sector, count + 1);\r\n    });\r\n\r\n    return Array.from(sectorCounts.entries())\r\n      .map(([sector, count]) => ({ sector, count }))\r\n      .sort((a, b) => b.count - a.count);\r\n  }\r\n\r\n  /**\r\n   * Export campaign for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      id: this.id,\r\n      name: this.name,\r\n      aliases: this.aliases,\r\n      description: this.description,\r\n      status: this.status,\r\n      sophistication: this.sophistication,\r\n      objectives: this.objectives,\r\n      startDate: this.startDate,\r\n      endDate: this.endDate,\r\n      lastActivity: this.lastActivity,\r\n      durationInDays: this.durationInDays,\r\n      daysSinceLastActivity: this.daysSinceLastActivity,\r\n      targetedSectors: this.targetedSectors,\r\n      targetedCountries: this.targetedCountries,\r\n      targetedTechnologies: this.targetedTechnologies,\r\n      victimCount: this.victimCount,\r\n      indicatorCount: this.indicatorCount,\r\n      impactScore: this.impactScore,\r\n      confidenceScore: this.confidenceScore,\r\n      threatActor: this.threatActor?.name,\r\n      threatActorId: this.threatActorId,\r\n      malwareFamilies: this.malwareFamilies,\r\n      toolsUsed: this.toolsUsed,\r\n      mostImpactedSectors: this.getMostImpactedSectors(),\r\n      attackVectors: this.attackVectors,\r\n      timeline: this.timeline,\r\n      infrastructure: this.infrastructure,\r\n      tags: this.tags,\r\n      references: this.references,\r\n      createdAt: this.createdAt,\r\n      updatedAt: this.updatedAt,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Complete campaign\r\n   */\r\n  complete(): void {\r\n    this.status = CampaignStatus.COMPLETED;\r\n    this.endDate = new Date();\r\n  }\r\n\r\n  /**\r\n   * Activate campaign\r\n   */\r\n  activate(): void {\r\n    this.status = CampaignStatus.ACTIVE;\r\n    this.lastActivity = new Date();\r\n  }\r\n\r\n  /**\r\n   * Deactivate campaign\r\n   */\r\n  deactivate(): void {\r\n    this.status = CampaignStatus.INACTIVE;\r\n  }\r\n\r\n  /**\r\n   * Mark as under investigation\r\n   */\r\n  markUnderInvestigation(): void {\r\n    this.status = CampaignStatus.UNDER_INVESTIGATION;\r\n  }\r\n}\r\n"], "version": 3}