{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\logging\\logging.module.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAwC;AACxC,2CAA6D;AAC7D,+CAA6C;AAC7C,iDAAmC;AACnC,qDAAiD;AACjD,yDAAqD;AACrD,2EAAsE;AACtE,qEAAgE;AAChE,2EAAsE;AACtE,6DAA+D;AAE/D;;;;;;;;;;;;;GAaG;AAoEI,IAAM,aAAa,GAAnB,MAAM,aAAa;CAAG,CAAA;AAAhB,sCAAa;wBAAb,aAAa;IAnEzB,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,qBAAY;YACZ,4BAAa,CAAC,YAAY,CAAC;gBACzB,OAAO,EAAE,CAAC,qBAAY,CAAC;gBACvB,MAAM,EAAE,CAAC,sBAAa,CAAC;gBACvB,UAAU,EAAE,KAAK,EAAE,aAA4B,EAAE,EAAE;oBACjD,MAAM,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBAEnD,sDAAsD;oBACtD,MAAM,gBAAgB,GAAG,IAAI,mDAAuB,CAAC,aAAa,CAAC,CAAC;oBACpE,MAAM,gBAAgB,CAAC,YAAY,EAAE,CAAC;oBAEtC,iFAAiF;oBACjF,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;oBAElE,sDAAsD;oBACtD,MAAM,iBAAiB,GAAwB,EAAE,CAAC;oBAClD,IAAI,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;wBAClC,iBAAiB,CAAC,IAAI,CACpB,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;4BAC7B,KAAK,EAAE,aAAa,CAAC,OAAO,CAAC,KAAK;4BAClC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAC5B,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,EAC1B,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,EAChE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,IAAI,EAAE,EAAE,EAAE;gCACvF,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gCAClD,MAAM,cAAc,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gCAClE,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gCAC3E,OAAO,GAAG,SAAS,IAAI,KAAK,KAAK,cAAc,GAAG,UAAU,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC;4BACrF,CAAC,CAAC,CACH;yBACF,CAAC,CACH,CAAC;oBACJ,CAAC;oBAED,oDAAoD;oBACpD,MAAM,aAAa,GAAG,CAAC,GAAG,iBAAiB,EAAE,GAAG,iBAAiB,CAAC,CAAC;oBAEnE,OAAO;wBACL,KAAK,EAAE,aAAa,CAAC,KAAK;wBAC1B,MAAM,EAAE,IAAA,oCAAmB,EAAC,aAAa,CAAC;wBAC1C,UAAU,EAAE,aAAa;wBACzB,MAAM,EAAE,aAAa,CAAC,MAAM;wBAC5B,WAAW,EAAE,KAAK;wBAClB,gBAAgB,EAAE,IAAI;wBACtB,gBAAgB,EAAE,IAAI;qBACvB,CAAC;gBACJ,CAAC;aACF,CAAC;SACH;QACD,SAAS,EAAE;YACT,mDAAuB;YACvB,6CAAoB;YACpB,8BAAa;YACb,4BAAY;YACZ,6CAAoB;SACrB;QACD,OAAO,EAAE;YACP,mDAAuB;YACvB,6CAAoB;YACpB,8BAAa;YACb,4BAAY;YACZ,6CAAoB;YACpB,4BAAa;SACd;KACF,CAAC;GACW,aAAa,CAAG", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\logging\\logging.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { ConfigModule, ConfigService } from '@nestjs/config';\r\nimport { WinstonModule } from 'nest-winston';\r\nimport * as winston from 'winston';\r\nimport { LoggerService } from './logger.service';\r\nimport { AuditService } from './audit/audit.service';\r\nimport { SecurityAuditService } from './audit/security-audit.service';\r\nimport { CorrelationIdService } from './correlation-id.service';\r\nimport { TransportManagerService } from './transport-manager.service';\r\nimport { createWinstonFormat } from '../config/logging.config';\r\n\r\n/**\r\n * Logging module that provides structured logging with Winston\r\n * Supports multiple transports, audit logging, and correlation tracking\r\n * \r\n * Features:\r\n * - Structured JSON logging\r\n * - Multiple log levels and transports\r\n * - Audit logging for security events\r\n * - Correlation ID tracking\r\n * - Log rotation and archiving\r\n * - Environment-specific configuration\r\n * \r\n * @module LoggingModule\r\n */\r\n@Module({\r\n  imports: [\r\n    ConfigModule,\r\n    WinstonModule.forRootAsync({\r\n      imports: [ConfigModule],\r\n      inject: [ConfigService],\r\n      useFactory: async (configService: ConfigService) => {\r\n        const loggingConfig = configService.get('logging');\r\n        \r\n        // Create transport manager instance for configuration\r\n        const transportManager = new TransportManagerService(configService);\r\n        await transportManager.onModuleInit();\r\n        \r\n        // Get transports from transport manager (includes new formatters and transports)\r\n        const managedTransports = transportManager.getWinstonTransports();\r\n        \r\n        // Legacy console transport for backward compatibility\r\n        const consoleTransports: winston.transport[] = [];\r\n        if (loggingConfig.console.enabled) {\r\n          consoleTransports.push(\r\n            new winston.transports.Console({\r\n              level: loggingConfig.console.level,\r\n              format: winston.format.combine(\r\n                winston.format.timestamp(),\r\n                winston.format.colorize({ all: loggingConfig.console.colorize }),\r\n                winston.format.printf(({ timestamp, level, message, context, correlationId, ...meta }) => {\r\n                  const contextStr = context ? `[${context}] ` : '';\r\n                  const correlationStr = correlationId ? `[${correlationId}] ` : '';\r\n                  const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';\r\n                  return `${timestamp} ${level}: ${correlationStr}${contextStr}${message}${metaStr}`;\r\n                }),\r\n              ),\r\n            }),\r\n          );\r\n        }\r\n\r\n        // Combine managed transports with console transport\r\n        const allTransports = [...consoleTransports, ...managedTransports];\r\n\r\n        return {\r\n          level: loggingConfig.level,\r\n          format: createWinstonFormat(loggingConfig),\r\n          transports: allTransports,\r\n          silent: loggingConfig.silent,\r\n          exitOnError: false,\r\n          handleExceptions: true,\r\n          handleRejections: true,\r\n        };\r\n      },\r\n    }),\r\n  ],\r\n  providers: [\r\n    TransportManagerService,\r\n    CorrelationIdService,\r\n    LoggerService,\r\n    AuditService,\r\n    SecurityAuditService,\r\n  ],\r\n  exports: [\r\n    TransportManagerService,\r\n    CorrelationIdService,\r\n    LoggerService,\r\n    AuditService,\r\n    SecurityAuditService,\r\n    WinstonModule,\r\n  ],\r\n})\r\nexport class LoggingModule {}\r\n"], "version": 3}