{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\tests\\unit\\controllers\\report-definition.controller.spec.ts", "mappings": ";;AAAA,6CAAsD;AAEtD,oGAA+F;AAC/F,2FAAsF;AAGtF,gGAA0F;AAE1F;;;;;;;;;GASG;AACH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;IAC1C,IAAI,UAAsC,CAAC;IAC3C,IAAI,OAA6C,CAAC;IAElD,MAAM,QAAQ,GAAG;QACf,EAAE,EAAE,UAAU;QACd,KAAK,EAAE,kBAAkB;QACzB,KAAK,EAAE,CAAC,kBAAkB,CAAC;KAC5B,CAAC;IAEF,MAAM,oBAAoB,GAAG;QAC3B,EAAE,EAAE,YAAY;QAChB,IAAI,EAAE,aAAa;QACnB,WAAW,EAAE,yBAAyB;QACtC,QAAQ,EAAE,YAAY;QACtB,IAAI,EAAE,SAAS;QACf,UAAU,EAAE,YAAY;QACxB,KAAK,EAAE,+BAA+B;QACtC,UAAU,EAAE,EAAE;QACd,aAAa,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC;QAC/B,QAAQ,EAAE;YACR,OAAO,EAAE,IAAI;YACb,cAAc,EAAE,WAAW;YAC3B,QAAQ,EAAE,KAAK;SAChB;QACD,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,SAAS,EAAE,UAAU;QACrB,SAAS,EAAE,UAAU;KACtB,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,WAAW,GAAG;YAClB,sBAAsB,EAAE,IAAI,CAAC,EAAE,EAAE;YACjC,uBAAuB,EAAE,IAAI,CAAC,EAAE,EAAE;YAClC,sBAAsB,EAAE,IAAI,CAAC,EAAE,EAAE;YACjC,sBAAsB,EAAE,IAAI,CAAC,EAAE,EAAE;YACjC,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;YAC/B,wBAAwB,EAAE,IAAI,CAAC,EAAE,EAAE;YACnC,yBAAyB,EAAE,IAAI,CAAC,EAAE,EAAE;YACpC,2BAA2B,EAAE,IAAI,CAAC,EAAE,EAAE;SACvC,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,WAAW,EAAE,CAAC,yDAA0B,CAAC;YACzC,SAAS,EAAE;gBACT;oBACE,OAAO,EAAE,mDAAuB;oBAChC,QAAQ,EAAE,WAAW;iBACtB;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,UAAU,GAAG,MAAM,CAAC,GAAG,CAA6B,yDAA0B,CAAC,CAAC;QAChF,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,mDAAuB,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,MAAM,SAAS,GAA8B;YAC3C,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,wBAAwB;YACrC,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,YAAY;YACxB,KAAK,EAAE,+BAA+B;YACtC,UAAU,EAAE,EAAE;YACd,aAAa,EAAE,CAAC,KAAK,CAAC;SACvB,CAAC;QAEF,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,OAAO,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAEvE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,sBAAsB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAE5E,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,oBAAoB,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YACpF,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,4DAA2B,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,UAAU,GAAG,EAAE,GAAG,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YAC9C,OAAO,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;YAEjF,MAAM,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;iBAClE,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAExC,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,oBAAoB,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,OAAO,CAAC,sBAAsB,CAAC,iBAAiB,CAC9C,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAC7D,CAAC;YAEF,MAAM,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;iBACjE,OAAO,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,aAAa,GAAG,EAAE,IAAI,EAAE,MAAM,EAA+B,CAAC;YAEpE,MAAM,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;iBACrE,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,OAAO,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAExE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,uBAAuB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAC3E,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,4DAA2B,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,OAAO,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,UAAU,CAAC,uBAAuB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;iBACvE,OAAO,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,MAAM,CAAC,UAAU,CAAC,uBAAuB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;iBACvE,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,MAAM,SAAS,GAA8B;YAC3C,IAAI,EAAE,gBAAgB;YACtB,WAAW,EAAE,qBAAqB;SACnC,CAAC;QAEF,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,aAAa,GAAG,EAAE,GAAG,oBAAoB,EAAE,GAAG,SAAS,EAAE,CAAC;YAChE,OAAO,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAEhE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,sBAAsB,CAAC,YAAY,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YAE1F,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,oBAAoB,CAAC,YAAY,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YAClG,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,4DAA2B,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,aAAa,GAAG,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;YAC9D,MAAM,aAAa,GAAG,EAAE,GAAG,oBAAoB,EAAE,GAAG,aAAa,EAAE,CAAC;YACpE,OAAO,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAEhE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,sBAAsB,CAAC,YAAY,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YAE9F,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,0BAA0B;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,OAAO,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;YAE3F,MAAM,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC,cAAc,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;iBACjF,OAAO,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,OAAO,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAE5D,MAAM,UAAU,CAAC,sBAAsB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAEhE,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,oBAAoB,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,OAAO,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;YAE3F,MAAM,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;iBACtE,OAAO,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,MAAM,mBAAmB,GAAG;YAC1B,iBAAiB,EAAE,CAAC,oBAAoB,CAAC;YACzC,KAAK,EAAE,CAAC;YACR,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,EAAE;YACT,UAAU,EAAE,CAAC;SACd,CAAC;QAEF,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,OAAO,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;YAEpE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,oBAAoB,CAClD,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,CACjD,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAAC;gBACxD,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,OAAO,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;YAEpE,MAAM,UAAU,CAAC,oBAAoB,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YAEvG,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAAC;gBACxD,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,SAAS;gBACnB,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,OAAO,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;YAEpE,MAAM,UAAU,CAAC,oBAAoB,CAAC,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YAEzF,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAAC;gBACxD,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,GAAG,EAAE,0BAA0B;gBACtC,QAAQ,EAAE,SAAS;gBACnB,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,MAAM,aAAa,GAAG;YACpB,IAAI,EAAE,aAAa;YACnB,UAAU,EAAE,YAAY;YACxB,KAAK,EAAE,+BAA+B;YACtC,UAAU,EAAE,EAAE;SACf,CAAC;QAEF,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,gBAAgB,GAAG;gBACvB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;aACb,CAAC;YACF,OAAO,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAErE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,wBAAwB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAElF,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;YAC7E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,gBAAgB,GAAG;gBACvB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,CAAC,sBAAsB,EAAE,4BAA4B,CAAC;gBAC9D,QAAQ,EAAE,CAAC,6BAA6B,CAAC;aAC1C,CAAC;YACF,OAAO,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAErE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,wBAAwB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAElF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,gBAAgB,GAAG;gBACvB,GAAG,oBAAoB;gBACvB,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,qBAAqB;aAC5B,CAAC;YACF,OAAO,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAEtE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,yBAAyB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAElF,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,oBAAoB,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC1F,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,4DAA2B,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,OAAO,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC,CAAC;YAErG,MAAM,MAAM,CAAC,UAAU,CAAC,yBAAyB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;iBACzE,OAAO,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,MAAM,YAAY,GAAG;YACnB,EAAE,GAAG,oBAAoB,EAAE,OAAO,EAAE,CAAC,EAAE;YACvC,EAAE,GAAG,oBAAoB,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE;SACrE,CAAC;QAEF,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,OAAO,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAEpE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,2BAA2B,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAEpF,MAAM,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAC/E,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,OAAO,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;YAEhG,MAAM,MAAM,CAAC,UAAU,CAAC,2BAA2B,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;iBAC3E,OAAO,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,OAAO,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC;YAE3F,MAAM,MAAM,CAAC,UAAU,CAAC,uBAAuB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;iBACrE,OAAO,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,UAAU,GAAG,EAAE,IAAI,EAAE,GAAG,EAAS,CAAC,CAAC,eAAe;YAExD,MAAM,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;iBAClE,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,OAAO,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAExE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,uBAAuB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,gBAAgB,GAAG,EAAE,GAAG,QAAQ,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;YAE5D,2DAA2D;YAC3D,+CAA+C;YAC/C,OAAO,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAExE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,uBAAuB,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;YAExF,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,OAAO,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAExE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,uBAAuB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,4DAA2B,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,OAAO,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAExE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,uBAAuB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\tests\\unit\\controllers\\report-definition.controller.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { HttpStatus } from '@nestjs/common';\r\nimport { ReportDefinitionController } from '../../../controllers/report-definition.controller';\r\nimport { ReportDefinitionService } from '../../../services/report-definition.service';\r\nimport { CreateReportDefinitionDto } from '../../../dto/create-report-definition.dto';\r\nimport { UpdateReportDefinitionDto } from '../../../dto/update-report-definition.dto';\r\nimport { ReportDefinitionResponseDto } from '../../../dto/report-definition-response.dto';\r\n\r\n/**\r\n * Unit Tests for Report Definition Controller\r\n * \r\n * Tests all controller endpoints including:\r\n * - Request/response validation and transformation\r\n * - Error handling and HTTP status codes\r\n * - Authentication and authorization\r\n * - Input validation and sanitization\r\n * - Response formatting and serialization\r\n */\r\ndescribe('ReportDefinitionController', () => {\r\n  let controller: ReportDefinitionController;\r\n  let service: jest.Mocked<ReportDefinitionService>;\r\n\r\n  const mockUser = {\r\n    id: 'user-123',\r\n    email: '<EMAIL>',\r\n    roles: ['compliance_admin'],\r\n  };\r\n\r\n  const mockReportDefinition = {\r\n    id: 'report-123',\r\n    name: 'Test Report',\r\n    description: 'Test report description',\r\n    category: 'compliance',\r\n    type: 'tabular',\r\n    dataSource: 'compliance',\r\n    query: 'SELECT * FROM compliance_data',\r\n    parameters: [],\r\n    outputFormats: ['pdf', 'excel'],\r\n    schedule: {\r\n      enabled: true,\r\n      cronExpression: '0 9 * * 1',\r\n      timezone: 'UTC',\r\n    },\r\n    isActive: true,\r\n    createdAt: new Date('2024-01-01'),\r\n    updatedAt: new Date('2024-01-01'),\r\n    createdBy: 'user-123',\r\n    updatedBy: 'user-123',\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const mockService = {\r\n      createReportDefinition: jest.fn(),\r\n      getReportDefinitionById: jest.fn(),\r\n      updateReportDefinition: jest.fn(),\r\n      deleteReportDefinition: jest.fn(),\r\n      getReportDefinitions: jest.fn(),\r\n      validateReportDefinition: jest.fn(),\r\n      duplicateReportDefinition: jest.fn(),\r\n      getReportDefinitionVersions: jest.fn(),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      controllers: [ReportDefinitionController],\r\n      providers: [\r\n        {\r\n          provide: ReportDefinitionService,\r\n          useValue: mockService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    controller = module.get<ReportDefinitionController>(ReportDefinitionController);\r\n    service = module.get(ReportDefinitionService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('createReportDefinition', () => {\r\n    const createDto: CreateReportDefinitionDto = {\r\n      name: 'New Report',\r\n      description: 'New report description',\r\n      category: 'compliance',\r\n      type: 'tabular',\r\n      dataSource: 'compliance',\r\n      query: 'SELECT * FROM compliance_data',\r\n      parameters: [],\r\n      outputFormats: ['pdf'],\r\n    };\r\n\r\n    it('should create a new report definition successfully', async () => {\r\n      service.createReportDefinition.mockResolvedValue(mockReportDefinition);\r\n\r\n      const result = await controller.createReportDefinition(createDto, mockUser);\r\n\r\n      expect(service.createReportDefinition).toHaveBeenCalledWith(createDto, mockUser.id);\r\n      expect(result).toBeInstanceOf(ReportDefinitionResponseDto);\r\n      expect(result.id).toBe(mockReportDefinition.id);\r\n      expect(result.name).toBe(mockReportDefinition.name);\r\n    });\r\n\r\n    it('should handle validation errors', async () => {\r\n      const invalidDto = { ...createDto, name: '' };\r\n      service.createReportDefinition.mockRejectedValue(new Error('Validation failed'));\r\n\r\n      await expect(controller.createReportDefinition(invalidDto, mockUser))\r\n        .rejects.toThrow('Validation failed');\r\n\r\n      expect(service.createReportDefinition).toHaveBeenCalledWith(invalidDto, mockUser.id);\r\n    });\r\n\r\n    it('should handle duplicate name errors', async () => {\r\n      service.createReportDefinition.mockRejectedValue(\r\n        new Error('Report definition with this name already exists')\r\n      );\r\n\r\n      await expect(controller.createReportDefinition(createDto, mockUser))\r\n        .rejects.toThrow('Report definition with this name already exists');\r\n    });\r\n\r\n    it('should validate required fields', async () => {\r\n      const incompleteDto = { name: 'Test' } as CreateReportDefinitionDto;\r\n\r\n      await expect(controller.createReportDefinition(incompleteDto, mockUser))\r\n        .rejects.toThrow();\r\n    });\r\n  });\r\n\r\n  describe('getReportDefinitionById', () => {\r\n    it('should return report definition by ID', async () => {\r\n      service.getReportDefinitionById.mockResolvedValue(mockReportDefinition);\r\n\r\n      const result = await controller.getReportDefinitionById('report-123', mockUser);\r\n\r\n      expect(service.getReportDefinitionById).toHaveBeenCalledWith('report-123');\r\n      expect(result).toBeInstanceOf(ReportDefinitionResponseDto);\r\n      expect(result.id).toBe('report-123');\r\n    });\r\n\r\n    it('should throw 404 for non-existent report definition', async () => {\r\n      service.getReportDefinitionById.mockResolvedValue(null);\r\n\r\n      await expect(controller.getReportDefinitionById('non-existent', mockUser))\r\n        .rejects.toThrow('Report definition not found');\r\n    });\r\n\r\n    it('should validate UUID format', async () => {\r\n      await expect(controller.getReportDefinitionById('invalid-uuid', mockUser))\r\n        .rejects.toThrow();\r\n    });\r\n  });\r\n\r\n  describe('updateReportDefinition', () => {\r\n    const updateDto: UpdateReportDefinitionDto = {\r\n      name: 'Updated Report',\r\n      description: 'Updated description',\r\n    };\r\n\r\n    it('should update report definition successfully', async () => {\r\n      const updatedReport = { ...mockReportDefinition, ...updateDto };\r\n      service.updateReportDefinition.mockResolvedValue(updatedReport);\r\n\r\n      const result = await controller.updateReportDefinition('report-123', updateDto, mockUser);\r\n\r\n      expect(service.updateReportDefinition).toHaveBeenCalledWith('report-123', updateDto, mockUser.id);\r\n      expect(result).toBeInstanceOf(ReportDefinitionResponseDto);\r\n      expect(result.name).toBe(updateDto.name);\r\n    });\r\n\r\n    it('should handle partial updates', async () => {\r\n      const partialUpdate = { description: 'New description only' };\r\n      const updatedReport = { ...mockReportDefinition, ...partialUpdate };\r\n      service.updateReportDefinition.mockResolvedValue(updatedReport);\r\n\r\n      const result = await controller.updateReportDefinition('report-123', partialUpdate, mockUser);\r\n\r\n      expect(result.description).toBe(partialUpdate.description);\r\n      expect(result.name).toBe(mockReportDefinition.name); // Should remain unchanged\r\n    });\r\n\r\n    it('should throw 404 for non-existent report definition', async () => {\r\n      service.updateReportDefinition.mockRejectedValue(new Error('Report definition not found'));\r\n\r\n      await expect(controller.updateReportDefinition('non-existent', updateDto, mockUser))\r\n        .rejects.toThrow('Report definition not found');\r\n    });\r\n  });\r\n\r\n  describe('deleteReportDefinition', () => {\r\n    it('should delete report definition successfully', async () => {\r\n      service.deleteReportDefinition.mockResolvedValue(undefined);\r\n\r\n      await controller.deleteReportDefinition('report-123', mockUser);\r\n\r\n      expect(service.deleteReportDefinition).toHaveBeenCalledWith('report-123', mockUser.id);\r\n    });\r\n\r\n    it('should throw 404 for non-existent report definition', async () => {\r\n      service.deleteReportDefinition.mockRejectedValue(new Error('Report definition not found'));\r\n\r\n      await expect(controller.deleteReportDefinition('non-existent', mockUser))\r\n        .rejects.toThrow('Report definition not found');\r\n    });\r\n  });\r\n\r\n  describe('getReportDefinitions', () => {\r\n    const mockPaginatedResult = {\r\n      reportDefinitions: [mockReportDefinition],\r\n      total: 1,\r\n      page: 1,\r\n      limit: 20,\r\n      totalPages: 1,\r\n    };\r\n\r\n    it('should return paginated report definitions', async () => {\r\n      service.getReportDefinitions.mockResolvedValue(mockPaginatedResult);\r\n\r\n      const result = await controller.getReportDefinitions(\r\n        1, 20, 'compliance', 'tabular', 'test', mockUser\r\n      );\r\n\r\n      expect(service.getReportDefinitions).toHaveBeenCalledWith({\r\n        page: 1,\r\n        limit: 20,\r\n        category: 'compliance',\r\n        type: 'tabular',\r\n        search: 'test',\r\n      });\r\n      expect(result.reportDefinitions).toHaveLength(1);\r\n      expect(result.total).toBe(1);\r\n      expect(result.page).toBe(1);\r\n    });\r\n\r\n    it('should handle default pagination parameters', async () => {\r\n      service.getReportDefinitions.mockResolvedValue(mockPaginatedResult);\r\n\r\n      await controller.getReportDefinitions(undefined, undefined, undefined, undefined, undefined, mockUser);\r\n\r\n      expect(service.getReportDefinitions).toHaveBeenCalledWith({\r\n        page: 1,\r\n        limit: 20,\r\n        category: undefined,\r\n        type: undefined,\r\n        search: undefined,\r\n      });\r\n    });\r\n\r\n    it('should validate pagination limits', async () => {\r\n      service.getReportDefinitions.mockResolvedValue(mockPaginatedResult);\r\n\r\n      await controller.getReportDefinitions(1, 150, undefined, undefined, undefined, mockUser);\r\n\r\n      expect(service.getReportDefinitions).toHaveBeenCalledWith({\r\n        page: 1,\r\n        limit: 100, // Should be capped at 100\r\n        category: undefined,\r\n        type: undefined,\r\n        search: undefined,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('validateReportDefinition', () => {\r\n    const validationDto = {\r\n      name: 'Test Report',\r\n      dataSource: 'compliance',\r\n      query: 'SELECT * FROM compliance_data',\r\n      parameters: [],\r\n    };\r\n\r\n    it('should validate report definition successfully', async () => {\r\n      const validationResult = {\r\n        isValid: true,\r\n        errors: [],\r\n        warnings: [],\r\n      };\r\n      service.validateReportDefinition.mockResolvedValue(validationResult);\r\n\r\n      const result = await controller.validateReportDefinition(validationDto, mockUser);\r\n\r\n      expect(service.validateReportDefinition).toHaveBeenCalledWith(validationDto);\r\n      expect(result.isValid).toBe(true);\r\n      expect(result.errors).toEqual([]);\r\n    });\r\n\r\n    it('should return validation errors', async () => {\r\n      const validationResult = {\r\n        isValid: false,\r\n        errors: ['Invalid query syntax', 'Missing required parameter'],\r\n        warnings: ['Performance may be impacted'],\r\n      };\r\n      service.validateReportDefinition.mockResolvedValue(validationResult);\r\n\r\n      const result = await controller.validateReportDefinition(validationDto, mockUser);\r\n\r\n      expect(result.isValid).toBe(false);\r\n      expect(result.errors).toHaveLength(2);\r\n      expect(result.warnings).toHaveLength(1);\r\n    });\r\n  });\r\n\r\n  describe('duplicateReportDefinition', () => {\r\n    it('should duplicate report definition successfully', async () => {\r\n      const duplicatedReport = {\r\n        ...mockReportDefinition,\r\n        id: 'report-456',\r\n        name: 'Copy of Test Report',\r\n      };\r\n      service.duplicateReportDefinition.mockResolvedValue(duplicatedReport);\r\n\r\n      const result = await controller.duplicateReportDefinition('report-123', mockUser);\r\n\r\n      expect(service.duplicateReportDefinition).toHaveBeenCalledWith('report-123', mockUser.id);\r\n      expect(result).toBeInstanceOf(ReportDefinitionResponseDto);\r\n      expect(result.id).toBe('report-456');\r\n      expect(result.name).toBe('Copy of Test Report');\r\n    });\r\n\r\n    it('should throw 404 for non-existent source report', async () => {\r\n      service.duplicateReportDefinition.mockRejectedValue(new Error('Source report definition not found'));\r\n\r\n      await expect(controller.duplicateReportDefinition('non-existent', mockUser))\r\n        .rejects.toThrow('Source report definition not found');\r\n    });\r\n  });\r\n\r\n  describe('getReportDefinitionVersions', () => {\r\n    const mockVersions = [\r\n      { ...mockReportDefinition, version: 1 },\r\n      { ...mockReportDefinition, version: 2, name: 'Updated Test Report' },\r\n    ];\r\n\r\n    it('should return report definition versions', async () => {\r\n      service.getReportDefinitionVersions.mockResolvedValue(mockVersions);\r\n\r\n      const result = await controller.getReportDefinitionVersions('report-123', mockUser);\r\n\r\n      expect(service.getReportDefinitionVersions).toHaveBeenCalledWith('report-123');\r\n      expect(result).toHaveLength(2);\r\n      expect(result[0].version).toBe(1);\r\n      expect(result[1].version).toBe(2);\r\n    });\r\n\r\n    it('should throw 404 for non-existent report definition', async () => {\r\n      service.getReportDefinitionVersions.mockRejectedValue(new Error('Report definition not found'));\r\n\r\n      await expect(controller.getReportDefinitionVersions('non-existent', mockUser))\r\n        .rejects.toThrow('Report definition not found');\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle service errors gracefully', async () => {\r\n      service.getReportDefinitionById.mockRejectedValue(new Error('Database connection failed'));\r\n\r\n      await expect(controller.getReportDefinitionById('report-123', mockUser))\r\n        .rejects.toThrow('Database connection failed');\r\n    });\r\n\r\n    it('should handle validation pipe errors', async () => {\r\n      const invalidDto = { name: 123 } as any; // Invalid type\r\n\r\n      await expect(controller.createReportDefinition(invalidDto, mockUser))\r\n        .rejects.toThrow();\r\n    });\r\n  });\r\n\r\n  describe('authorization', () => {\r\n    it('should allow access for compliance_admin role', async () => {\r\n      service.getReportDefinitionById.mockResolvedValue(mockReportDefinition);\r\n\r\n      const result = await controller.getReportDefinitionById('report-123', mockUser);\r\n\r\n      expect(result).toBeDefined();\r\n    });\r\n\r\n    it('should handle unauthorized access', async () => {\r\n      const unauthorizedUser = { ...mockUser, roles: ['viewer'] };\r\n\r\n      // This would be handled by guards in actual implementation\r\n      // Here we're just testing the controller logic\r\n      service.getReportDefinitionById.mockResolvedValue(mockReportDefinition);\r\n\r\n      const result = await controller.getReportDefinitionById('report-123', unauthorizedUser);\r\n\r\n      expect(result).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('response transformation', () => {\r\n    it('should transform entity to response DTO', async () => {\r\n      service.getReportDefinitionById.mockResolvedValue(mockReportDefinition);\r\n\r\n      const result = await controller.getReportDefinitionById('report-123', mockUser);\r\n\r\n      expect(result).toBeInstanceOf(ReportDefinitionResponseDto);\r\n      expect(result).toHaveProperty('id');\r\n      expect(result).toHaveProperty('name');\r\n      expect(result).toHaveProperty('description');\r\n      expect(result).toHaveProperty('metadata');\r\n    });\r\n\r\n    it('should include metadata in response', async () => {\r\n      service.getReportDefinitionById.mockResolvedValue(mockReportDefinition);\r\n\r\n      const result = await controller.getReportDefinitionById('report-123', mockUser);\r\n\r\n      expect(result.metadata).toBeDefined();\r\n      expect(result.metadata.createdAt).toBeDefined();\r\n      expect(result.metadata.updatedAt).toBeDefined();\r\n      expect(result.metadata.createdBy).toBe('user-123');\r\n    });\r\n  });\r\n});\r\n"], "version": 3}