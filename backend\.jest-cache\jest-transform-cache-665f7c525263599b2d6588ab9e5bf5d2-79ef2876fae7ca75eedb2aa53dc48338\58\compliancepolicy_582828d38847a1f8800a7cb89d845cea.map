{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\compliance-policy.ts", "mappings": ";;;AAAA,8EAA0E;AAC1E,yHAAuG;AACvG,2GAA2F;AAG3F,oGAAgG;AAEhG,IAAY,mBASX;AATD,WAAY,mBAAmB;IAC7B,oCAAa,CAAA;IACb,oCAAa,CAAA;IACb,sCAAe,CAAA;IACf,0CAAmB,CAAA;IACnB,8CAAuB,CAAA;IACvB,oCAAa,CAAA;IACb,kCAAW,CAAA;IACX,sCAAe,CAAA;AACjB,CAAC,EATW,mBAAmB,mCAAnB,mBAAmB,QAS9B;AAED,IAAY,gBAMX;AAND,WAAY,gBAAgB;IAC1B,2CAAuB,CAAA;IACvB,mDAA+B,CAAA;IAC/B,+DAA2C,CAAA;IAC3C,iDAA6B,CAAA;IAC7B,iEAA6C,CAAA;AAC/C,CAAC,EANW,gBAAgB,gCAAhB,gBAAgB,QAM3B;AAED,IAAY,qBAKX;AALD,WAAY,qBAAqB;IAC/B,kDAAyB,CAAA;IACzB,gDAAuB,CAAA;IACvB,kDAAyB,CAAA;IACzB,sDAA6B,CAAA;AAC/B,CAAC,EALW,qBAAqB,qCAArB,qBAAqB,QAKhC;AAgCD,IAAY,sBASX;AATD,WAAY,sBAAsB;IAChC,+CAAqB,CAAA;IACrB,mDAAyB,CAAA;IACzB,iDAAuB,CAAA;IACvB,yDAA+B,CAAA;IAC/B,uDAA6B,CAAA;IAC7B,qDAA2B,CAAA;IAC3B,2CAAiB,CAAA;IACjB,iDAAuB,CAAA;AACzB,CAAC,EATW,sBAAsB,sCAAtB,sBAAsB,QASjC;AAED,IAAY,kBAKX;AALD,WAAY,kBAAkB;IAC5B,2CAAqB,CAAA;IACrB,mCAAa,CAAA;IACb,uCAAiB,CAAA;IACjB,iCAAW,CAAA;AACb,CAAC,EALW,kBAAkB,kCAAlB,kBAAkB,QAK7B;AAED,IAAY,wBAOX;AAPD,WAAY,wBAAwB;IAClC,qDAAyB,CAAA;IACzB,2CAAe,CAAA;IACf,6CAAiB,CAAA;IACjB,+CAAmB,CAAA;IACnB,mDAAuB,CAAA;IACvB,iDAAqB,CAAA;AACvB,CAAC,EAPW,wBAAwB,wCAAxB,wBAAwB,QAOnC;AA0BD,IAAY,yBAMX;AAND,WAAY,yBAAyB;IACnC,kDAAqB,CAAA;IACrB,0CAAa,CAAA;IACb,8CAAiB,CAAA;IACjB,wCAAW,CAAA;IACX,4DAA+B,CAAA;AACjC,CAAC,EANW,yBAAyB,yCAAzB,yBAAyB,QAMpC;AAED,IAAY,uBAMX;AAND,WAAY,uBAAuB;IACjC,wCAAa,CAAA;IACb,sDAA2B,CAAA;IAC3B,gDAAqB,CAAA;IACrB,0DAA+B,CAAA;IAC/B,4DAAiC,CAAA;AACnC,CAAC,EANW,uBAAuB,uCAAvB,uBAAuB,QAMlC;AAyBD,MAAa,gBAAiB,SAAQ,wBAAe;IAanD,YAAY,KAaX;QACC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;QAEvB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAE1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC;QACtC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;QAClC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,cAAc,CAAC;QAC5C,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,WAAW,IAAI,EAAE,CAAC;QAC5C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;IACtC,CAAC;IAEM,MAAM,CAAC,MAAM,CAAC,KASpB;QACC,OAAO,IAAI,gBAAgB,CAAC;YAC1B,GAAG,KAAK;YACR,cAAc,EAAE,KAAK,CAAC,SAAS;YAC/B,WAAW,EAAE,EAAE;YACf,OAAO,EAAE,EAAE;SACZ,CAAC,CAAC;IACL,CAAC;IAEM,gBAAgB,CAAC,SAAiB,EAAE,UAAkB;QAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;QAC7D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0CAAmB,CAAC,oBAAoB,SAAS,aAAa,EAAE,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,wCAAwC;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAEtD,6CAA6C;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QAExD,MAAM,UAAU,GAAyB;YACvC,EAAE,EAAE,8CAAc,CAAC,QAAQ,EAAE;YAC7B,QAAQ,EAAE,IAAI,CAAC,EAAE;YACjB,SAAS;YACT,UAAU;YACV,UAAU,EAAE,kCAAS,CAAC,GAAG,EAAE;YAC3B,MAAM;YACN,QAAQ;YACR,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC;YACvD,iBAAiB,EAAE,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YAC5D,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,CAAC;QAEF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEnC,wBAAwB;QACxB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC;QAC7C,OAAO,CAAC,cAAc,GAAG,UAAU,CAAC,iBAAiB,CAAC;QAEtD,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,OAAO,UAAU,CAAC;IACpB,CAAC;IAEM,wBAAwB,CAC7B,SAA8B,EAC9B,YAA0D,EAC1D,WAAmB;QAEnB,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;QAChF,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACrD,CAAC,CAAC,UAAU,CAAC,KAAK,IAAI,YAAY,CAAC,SAAS,CAAC,KAAK;YAClD,CAAC,CAAC,UAAU,CAAC,KAAK,IAAI,YAAY,CAAC,OAAO,CAAC,KAAK,CACjD,CAAC;QAEF,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QACzE,MAAM,WAAW,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC/D,MAAM,eAAe,GAAG,IAAI,CAAC,gCAAgC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAEtF,MAAM,MAAM,GAAqB;YAC/B,EAAE,EAAE,8CAAc,CAAC,QAAQ,EAAE;YAC7B,QAAQ,EAAE,IAAI,CAAC,EAAE;YACjB,SAAS;YACT,WAAW,EAAE,kCAAS,CAAC,GAAG,EAAE;YAC5B,WAAW;YACX,YAAY;YACZ,aAAa,EAAE,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC;YAC3D,eAAe;YACf,QAAQ,EAAE,WAAW;YACrB,eAAe;YACf,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,eAAe,EAAE,WAAW,CAAC;SACzF,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,UAAU,CAAC,OAA0B,EAAE,UAAkB;QAC9D,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAE9B,kCAAkC;QAClC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,0CAAmB,CAAC,oBAAoB,OAAO,CAAC,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEM,aAAa,CAAC,SAAiB,EAAE,OAAmC,EAAE,UAAkB;QAC7F,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;QACvE,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,0CAAmB,CAAC,oBAAoB,SAAS,aAAa,EAAE,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,MAAM,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,GAAG,OAAO,EAAE,CAAC;QACvE,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;QAErC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC;QAC9C,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEM,aAAa,CAAC,SAAiB,EAAE,UAAkB;QACxD,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;QACvE,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,0CAAmB,CAAC,oBAAoB,SAAS,aAAa,EAAE,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEM,WAAW,CAAC,SAAiB,EAAE,QAA4B,EAAE,UAAkB;QACpF,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;QAC7D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0CAAmB,CAAC,oBAAoB,SAAS,aAAa,EAAE,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChC,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEM,8BAA8B;QACnC,MAAM,GAAG,GAAG,kCAAS,CAAC,GAAG,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,cAAc;gBAAE,OAAO,IAAI,CAAC;YACzC,OAAO,OAAO,CAAC,cAAc,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,0BAA0B;QAC/B,MAAM,OAAO,GAAkD,EAAS,CAAC;QAEzE,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC3D,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;YAChF,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnC,OAAO,CAAC,SAAS,CAAC,GAAG,gBAAgB,CAAC,YAAY,CAAC;gBACnD,SAAS;YACX,CAAC;YAED,MAAM,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YACtD,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,sBAAsB,CAAC,OAA0B;QACvD,MAAM,QAAQ,GAAwB,EAAE,CAAC;QAEzC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAC5B,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,6DAA6D;QAC7D,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,qBAAqB,CAAC,UAAU;gBACnC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC;gBACxD,MAAM;YACR,KAAK,qBAAqB,CAAC,SAAS;gBAClC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC;gBACvD,MAAM;YACR,KAAK,qBAAqB,CAAC,UAAU;gBACnC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC;gBACxD,MAAM;QACV,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,uBAAuB,CAAC,OAA0B;QACxD,4CAA4C;QAC5C,mEAAmE;QACnE,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,sBAAsB,CAAC,OAA0B;QACvD,2CAA2C;QAC3C,8DAA8D;QAC9D,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,uBAAuB,CAAC,OAA0B;QACxD,4CAA4C;QAC5C,kEAAkE;QAClE,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,yBAAyB,CAAC,QAA6B;QAC7D,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,gBAAgB,CAAC,SAAS,CAAC;QACpC,CAAC;QAED,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QACjG,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,yBAAyB,CAAC,IAAI,CAAC,CAAC;QAEzF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,OAAO,gBAAgB,CAAC,aAAa,CAAC;QACxC,CAAC;QAED,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,OAAO,gBAAgB,CAAC,mBAAmB,CAAC;QAC9C,CAAC;QAED,OAAO,gBAAgB,CAAC,mBAAmB,CAAC;IAC9C,CAAC;IAEO,uBAAuB,CAAC,QAA6B;QAC3D,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACnF,CAAC;IAEO,2BAA2B,CAAC,OAA0B;QAC5D,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,QAAQ,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;QAE7B,QAAQ,OAAO,CAAC,cAAc,EAAE,CAAC;YAC/B,KAAK,wBAAwB,CAAC,KAAK;gBACjC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACpC,MAAM;YACR,KAAK,wBAAwB,CAAC,MAAM;gBAClC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACpC,MAAM;YACR,KAAK,wBAAwB,CAAC,OAAO;gBACnC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;gBACtC,MAAM;YACR,KAAK,wBAAwB,CAAC,SAAS;gBACrC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;gBACtC,MAAM;YACR,KAAK,wBAAwB,CAAC,QAAQ;gBACpC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC5C,MAAM;YACR;gBACE,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,uBAAuB;QAClE,CAAC;QAED,OAAO,kCAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAEO,wBAAwB,CAAC,QAA6B;QAC5D,MAAM,OAAO,GAAG;YACd,KAAK,EAAE,QAAQ,CAAC,MAAM;YACtB,SAAS,EAAE,CAAC;YACZ,YAAY,EAAE,CAAC;YACf,kBAAkB,EAAE,CAAC;YACrB,WAAW,EAAE,CAAC;SACf,CAAC;QAEF,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;gBACvB,KAAK,gBAAgB,CAAC,SAAS;oBAC7B,OAAO,CAAC,SAAS,EAAE,CAAC;oBACpB,MAAM;gBACR,KAAK,gBAAgB,CAAC,aAAa;oBACjC,OAAO,CAAC,YAAY,EAAE,CAAC;oBACvB,MAAM;gBACR,KAAK,gBAAgB,CAAC,mBAAmB;oBACvC,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC7B,MAAM;gBACR,KAAK,gBAAgB,CAAC,YAAY;oBAChC,OAAO,CAAC,WAAW,EAAE,CAAC;oBACtB,MAAM;YACV,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,sBAAsB,CAAC,OAAY;QACzC,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC;YAAE,OAAO,gBAAgB,CAAC,YAAY,CAAC;QAC9D,IAAI,OAAO,CAAC,YAAY,GAAG,CAAC;YAAE,OAAO,gBAAgB,CAAC,aAAa,CAAC;QACpE,IAAI,OAAO,CAAC,kBAAkB,GAAG,CAAC;YAAE,OAAO,gBAAgB,CAAC,mBAAmB,CAAC;QAChF,IAAI,OAAO,CAAC,WAAW,GAAG,CAAC;YAAE,OAAO,gBAAgB,CAAC,YAAY,CAAC;QAClE,OAAO,gBAAgB,CAAC,SAAS,CAAC;IACpC,CAAC;IAEO,gCAAgC,CAAC,SAA8B,EAAE,QAA6B;QACpG,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;QAE1C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC3B,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,yCAAyC;QACzC,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,mBAAmB,CAAC,IAAI;gBAC3B,eAAe,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;gBACzE,eAAe,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;gBACzD,MAAM;YACR,KAAK,mBAAmB,CAAC,IAAI;gBAC3B,eAAe,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;gBAC1E,eAAe,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;gBACjE,MAAM;YACR,wDAAwD;QAC1D,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACrC,CAAC;IAEO,wBAAwB,CAC9B,SAA8B,EAC9B,OAAY,EACZ,QAA6B;QAE7B,MAAM,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;QACnF,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,yBAAyB,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;QAExG,OAAO,GAAG,SAAS,2BAA2B,oBAAoB,eAAe;YAC1E,GAAG,OAAO,CAAC,KAAK,uBAAuB,OAAO,CAAC,SAAS,cAAc;YACtE,GAAG,OAAO,CAAC,YAAY,mBAAmB,gBAAgB,iDAAiD,CAAC;IACrH,CAAC;IAEO,yBAAyB,CAAC,QAA4B;QAC5D,IAAI,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC;YACtD,OAAO,gBAAgB,CAAC,aAAa,CAAC;QACxC,CAAC;QACD,IAAI,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC5D,OAAO,gBAAgB,CAAC,mBAAmB,CAAC;QAC9C,CAAC;QACD,IAAI,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC;YACrD,OAAO,gBAAgB,CAAC,YAAY,CAAC;QACvC,CAAC;QACD,OAAO,gBAAgB,CAAC,SAAS,CAAC;IACpC,CAAC;IAEO,KAAK;QACX,qCAAqC;QACrC,+DAA+D;IACjE,CAAC;IAEO,aAAa,CAAC,KAAU;QAC9B,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,0CAAmB,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,0CAAmB,CAAC,gCAAgC,EAAE,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;YAClE,MAAM,IAAI,0CAAmB,CAAC,wCAAwC,EAAE,EAAE,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxD,MAAM,IAAI,0CAAmB,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACpB,MAAM,IAAI,0CAAmB,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACrB,MAAM,IAAI,0CAAmB,CAAC,gCAAgC,EAAE,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,0CAAmB,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC;QACjE,CAAC;QAED,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAA0B,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC;IACxF,CAAC;IAEO,eAAe,CAAC,OAA0B;QAChD,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,0CAAmB,CAAC,wBAAwB,EAAE,EAAE,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,0CAAmB,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,0CAAmB,CAAC,kCAAkC,EAAE,EAAE,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,0CAAmB,CAAC,oDAAoD,EAAE,EAAE,CAAC,CAAC;QAC1F,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,0CAAmB,CAAC,gCAAgC,EAAE,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACzB,MAAM,IAAI,0CAAmB,CAAC,sCAAsC,EAAE,EAAE,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,0CAAmB,CAAC,uCAAuC,EAAE,EAAE,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,0CAAmB,CAAC,mCAAmC,EAAE,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED,UAAU;IACV,IAAW,IAAI,KAAa,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAChD,IAAW,WAAW,KAAa,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IAC9D,IAAW,SAAS,KAA0B,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IACvE,IAAW,OAAO,KAAa,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACtD,IAAW,QAAQ,KAAe,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC1D,IAAW,QAAQ,KAA0B,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC1E,IAAW,OAAO,KAAc,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACvD,IAAW,SAAS,KAAa,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IAC1D,IAAW,cAAc,KAAa,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IACpE,IAAW,WAAW,KAA6B,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IACnF,IAAW,OAAO,KAAyB,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;CACxE;AArdD,4CAqdC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\compliance-policy.ts"], "sourcesContent": ["import { BaseEntity } from '../../../../shared-kernel/domain/base-entity';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { Timestamp } from '../../../../shared-kernel/value-objects/timestamp.value-object';\r\nimport { UserId } from '../../../../shared-kernel/value-objects/user-id.value-object';\r\nimport { TenantId } from '../../../../shared-kernel/value-objects/tenant-id.value-object';\r\nimport { ValidationException } from '../../../../shared-kernel/exceptions/validation.exception';\r\n\r\nexport enum ComplianceFramework {\r\n  SOC2 = 'SOC2',\r\n  GDPR = 'GDPR',\r\n  HIPAA = 'HIPAA',\r\n  PCI_DSS = 'PCI_DSS',\r\n  ISO_27001 = 'ISO_27001',\r\n  NIST = 'NIST',\r\n  CIS = 'CIS',\r\n  COBIT = 'COBIT'\r\n}\r\n\r\nexport enum ComplianceStatus {\r\n  COMPLIANT = 'COMPLIANT',\r\n  NON_COMPLIANT = 'NON_COMPLIANT',\r\n  PARTIALLY_COMPLIANT = 'PARTIALLY_COMPLIANT',\r\n  NOT_ASSESSED = 'NOT_ASSESSED',\r\n  REMEDIATION_REQUIRED = 'REMEDIATION_REQUIRED'\r\n}\r\n\r\nexport enum ComplianceControlType {\r\n  PREVENTIVE = 'PREVENTIVE',\r\n  DETECTIVE = 'DETECTIVE',\r\n  CORRECTIVE = 'CORRECTIVE',\r\n  COMPENSATING = 'COMPENSATING'\r\n}\r\n\r\nexport interface ComplianceControl {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  framework: ComplianceFramework;\r\n  controlId: string; // Framework-specific control ID (e.g., SOC2-CC6.1)\r\n  type: ComplianceControlType;\r\n  requirements: string[];\r\n  testProcedures: string[];\r\n  evidence: ComplianceEvidence[];\r\n  status: ComplianceStatus;\r\n  lastAssessed?: Timestamp;\r\n  nextAssessment?: Timestamp;\r\n  responsible: UserId;\r\n  priority: CompliancePriority;\r\n  automatedCheck: boolean;\r\n  checkFrequency?: ComplianceCheckFrequency;\r\n}\r\n\r\nexport interface ComplianceEvidence {\r\n  id: string;\r\n  type: ComplianceEvidenceType;\r\n  description: string;\r\n  source: string;\r\n  collectedAt: Timestamp;\r\n  collectedBy: UserId;\r\n  validUntil?: Timestamp;\r\n  metadata: Record<string, any>;\r\n}\r\n\r\nexport enum ComplianceEvidenceType {\r\n  DOCUMENT = 'DOCUMENT',\r\n  SCREENSHOT = 'SCREENSHOT',\r\n  LOG_ENTRY = 'LOG_ENTRY',\r\n  CONFIGURATION = 'CONFIGURATION',\r\n  AUDIT_REPORT = 'AUDIT_REPORT',\r\n  CERTIFICATE = 'CERTIFICATE',\r\n  POLICY = 'POLICY',\r\n  PROCEDURE = 'PROCEDURE'\r\n}\r\n\r\nexport enum CompliancePriority {\r\n  CRITICAL = 'CRITICAL',\r\n  HIGH = 'HIGH',\r\n  MEDIUM = 'MEDIUM',\r\n  LOW = 'LOW'\r\n}\r\n\r\nexport enum ComplianceCheckFrequency {\r\n  CONTINUOUS = 'CONTINUOUS',\r\n  DAILY = 'DAILY',\r\n  WEEKLY = 'WEEKLY',\r\n  MONTHLY = 'MONTHLY',\r\n  QUARTERLY = 'QUARTERLY',\r\n  ANNUALLY = 'ANNUALLY'\r\n}\r\n\r\nexport interface ComplianceAssessment {\r\n  id: UniqueEntityId;\r\n  policyId: UniqueEntityId;\r\n  controlId: string;\r\n  assessedBy: UserId;\r\n  assessedAt: Timestamp;\r\n  status: ComplianceStatus;\r\n  findings: ComplianceFinding[];\r\n  recommendations: string[];\r\n  nextAssessmentDue: Timestamp;\r\n  evidence: ComplianceEvidence[];\r\n}\r\n\r\nexport interface ComplianceFinding {\r\n  id: string;\r\n  severity: ComplianceFindingSeverity;\r\n  description: string;\r\n  impact: string;\r\n  recommendation: string;\r\n  status: ComplianceFindingStatus;\r\n  dueDate?: Timestamp;\r\n  assignedTo?: UserId;\r\n}\r\n\r\nexport enum ComplianceFindingSeverity {\r\n  CRITICAL = 'CRITICAL',\r\n  HIGH = 'HIGH',\r\n  MEDIUM = 'MEDIUM',\r\n  LOW = 'LOW',\r\n  INFORMATIONAL = 'INFORMATIONAL'\r\n}\r\n\r\nexport enum ComplianceFindingStatus {\r\n  OPEN = 'OPEN',\r\n  IN_PROGRESS = 'IN_PROGRESS',\r\n  RESOLVED = 'RESOLVED',\r\n  ACCEPTED_RISK = 'ACCEPTED_RISK',\r\n  FALSE_POSITIVE = 'FALSE_POSITIVE'\r\n}\r\n\r\nexport interface ComplianceReport {\r\n  id: UniqueEntityId;\r\n  policyId: UniqueEntityId;\r\n  framework: ComplianceFramework;\r\n  generatedAt: Timestamp;\r\n  generatedBy: UserId;\r\n  reportPeriod: {\r\n    startDate: Timestamp;\r\n    endDate: Timestamp;\r\n  };\r\n  overallStatus: ComplianceStatus;\r\n  controlsSummary: {\r\n    total: number;\r\n    compliant: number;\r\n    nonCompliant: number;\r\n    partiallyCompliant: number;\r\n    notAssessed: number;\r\n  };\r\n  findings: ComplianceFinding[];\r\n  recommendations: string[];\r\n  executiveSummary: string;\r\n}\r\n\r\nexport class CompliancePolicy extends BaseEntity<any> {\r\n  private readonly _name: string;\r\n  private readonly _description: string;\r\n  private readonly _framework: ComplianceFramework;\r\n  private readonly _version: string;\r\n  private readonly _tenantId: TenantId;\r\n  private _controls: ComplianceControl[];\r\n  private _enabled: boolean;\r\n  private readonly _createdBy: UserId;\r\n  private _lastModifiedBy: UserId;\r\n  private _assessments: ComplianceAssessment[];\r\n  private _reports: ComplianceReport[];\r\n\r\n  constructor(props: {\r\n    id?: UniqueEntityId;\r\n    name: string;\r\n    description: string;\r\n    framework: ComplianceFramework;\r\n    version: string;\r\n    tenantId: TenantId;\r\n    controls: ComplianceControl[];\r\n    enabled: boolean;\r\n    createdBy: UserId;\r\n    lastModifiedBy: UserId;\r\n    assessments?: ComplianceAssessment[];\r\n    reports?: ComplianceReport[];\r\n  }) {\r\n    super(props, props.id);\r\n    \r\n    this.validateProps(props);\r\n    \r\n    this._name = props.name;\r\n    this._description = props.description;\r\n    this._framework = props.framework;\r\n    this._version = props.version;\r\n    this._tenantId = props.tenantId;\r\n    this._controls = props.controls;\r\n    this._enabled = props.enabled;\r\n    this._createdBy = props.createdBy;\r\n    this._lastModifiedBy = props.lastModifiedBy;\r\n    this._assessments = props.assessments || [];\r\n    this._reports = props.reports || [];\r\n  }\r\n\r\n  public static create(props: {\r\n    name: string;\r\n    description: string;\r\n    framework: ComplianceFramework;\r\n    version: string;\r\n    tenantId: TenantId;\r\n    controls: ComplianceControl[];\r\n    enabled: boolean;\r\n    createdBy: UserId;\r\n  }): CompliancePolicy {\r\n    return new CompliancePolicy({\r\n      ...props,\r\n      lastModifiedBy: props.createdBy,\r\n      assessments: [],\r\n      reports: []\r\n    });\r\n  }\r\n\r\n  public assessCompliance(controlId: string, assessedBy: UserId): ComplianceAssessment {\r\n    const control = this._controls.find(c => c.id === controlId);\r\n    if (!control) {\r\n      throw new ValidationException(`Control with ID '${controlId}' not found`, []);\r\n    }\r\n\r\n    // Perform automated checks if available\r\n    const findings = this.performAutomatedChecks(control);\r\n    \r\n    // Determine overall status based on findings\r\n    const status = this.determineComplianceStatus(findings);\r\n\r\n    const assessment: ComplianceAssessment = {\r\n      id: UniqueEntityId.generate(),\r\n      policyId: this.id,\r\n      controlId,\r\n      assessedBy,\r\n      assessedAt: Timestamp.now(),\r\n      status,\r\n      findings,\r\n      recommendations: this.generateRecommendations(findings),\r\n      nextAssessmentDue: this.calculateNextAssessmentDate(control),\r\n      evidence: control.evidence\r\n    };\r\n\r\n    this._assessments.push(assessment);\r\n    \r\n    // Update control status\r\n    control.status = status;\r\n    control.lastAssessed = assessment.assessedAt;\r\n    control.nextAssessment = assessment.nextAssessmentDue;\r\n\r\n    this.touch();\r\n    return assessment;\r\n  }\r\n\r\n  public generateComplianceReport(\r\n    framework: ComplianceFramework,\r\n    reportPeriod: { startDate: Timestamp; endDate: Timestamp },\r\n    generatedBy: UserId\r\n  ): ComplianceReport {\r\n    const frameworkControls = this._controls.filter(c => c.framework === framework);\r\n    const recentAssessments = this._assessments.filter(a => \r\n      a.assessedAt.value >= reportPeriod.startDate.value &&\r\n      a.assessedAt.value <= reportPeriod.endDate.value\r\n    );\r\n\r\n    const controlsSummary = this.calculateControlsSummary(frameworkControls);\r\n    const allFindings = recentAssessments.flatMap(a => a.findings);\r\n    const recommendations = this.generateFrameworkRecommendations(framework, allFindings);\r\n\r\n    const report: ComplianceReport = {\r\n      id: UniqueEntityId.generate(),\r\n      policyId: this.id,\r\n      framework,\r\n      generatedAt: Timestamp.now(),\r\n      generatedBy,\r\n      reportPeriod,\r\n      overallStatus: this.calculateOverallStatus(controlsSummary),\r\n      controlsSummary,\r\n      findings: allFindings,\r\n      recommendations,\r\n      executiveSummary: this.generateExecutiveSummary(framework, controlsSummary, allFindings)\r\n    };\r\n\r\n    this._reports.push(report);\r\n    this.touch();\r\n    \r\n    return report;\r\n  }\r\n\r\n  public addControl(control: ComplianceControl, modifiedBy: UserId): void {\r\n    this.validateControl(control);\r\n    \r\n    // Check for duplicate control IDs\r\n    if (this._controls.some(c => c.id === control.id)) {\r\n      throw new ValidationException(`Control with ID '${control.id}' already exists`, []);\r\n    }\r\n\r\n    this._controls.push(control);\r\n    this._lastModifiedBy = modifiedBy;\r\n    this.touch();\r\n  }\r\n\r\n  public updateControl(controlId: string, updates: Partial<ComplianceControl>, modifiedBy: UserId): void {\r\n    const controlIndex = this._controls.findIndex(c => c.id === controlId);\r\n    if (controlIndex === -1) {\r\n      throw new ValidationException(`Control with ID '${controlId}' not found`, []);\r\n    }\r\n\r\n    const updatedControl = { ...this._controls[controlIndex], ...updates };\r\n    this.validateControl(updatedControl);\r\n\r\n    this._controls[controlIndex] = updatedControl;\r\n    this._lastModifiedBy = modifiedBy;\r\n    this.touch();\r\n  }\r\n\r\n  public removeControl(controlId: string, modifiedBy: UserId): void {\r\n    const controlIndex = this._controls.findIndex(c => c.id === controlId);\r\n    if (controlIndex === -1) {\r\n      throw new ValidationException(`Control with ID '${controlId}' not found`, []);\r\n    }\r\n\r\n    this._controls.splice(controlIndex, 1);\r\n    this._lastModifiedBy = modifiedBy;\r\n    this.touch();\r\n  }\r\n\r\n  public addEvidence(controlId: string, evidence: ComplianceEvidence, modifiedBy: UserId): void {\r\n    const control = this._controls.find(c => c.id === controlId);\r\n    if (!control) {\r\n      throw new ValidationException(`Control with ID '${controlId}' not found`, []);\r\n    }\r\n\r\n    control.evidence.push(evidence);\r\n    this._lastModifiedBy = modifiedBy;\r\n    this.touch();\r\n  }\r\n\r\n  public getControlsRequiringAssessment(): ComplianceControl[] {\r\n    const now = Timestamp.now();\r\n    return this._controls.filter(control => {\r\n      if (!control.nextAssessment) return true;\r\n      return control.nextAssessment.value <= now.value;\r\n    });\r\n  }\r\n\r\n  public getComplianceStatusSummary(): Record<ComplianceFramework, ComplianceStatus> {\r\n    const summary: Record<ComplianceFramework, ComplianceStatus> = {} as any;\r\n    \r\n    for (const framework of Object.values(ComplianceFramework)) {\r\n      const frameworkControls = this._controls.filter(c => c.framework === framework);\r\n      if (frameworkControls.length === 0) {\r\n        summary[framework] = ComplianceStatus.NOT_ASSESSED;\r\n        continue;\r\n      }\r\n\r\n      const statuses = frameworkControls.map(c => c.status);\r\n      summary[framework] = this.aggregateComplianceStatus(statuses);\r\n    }\r\n\r\n    return summary;\r\n  }\r\n\r\n  private performAutomatedChecks(control: ComplianceControl): ComplianceFinding[] {\r\n    const findings: ComplianceFinding[] = [];\r\n\r\n    if (!control.automatedCheck) {\r\n      return findings;\r\n    }\r\n\r\n    // Simulate automated compliance checks based on control type\r\n    switch (control.type) {\r\n      case ComplianceControlType.PREVENTIVE:\r\n        findings.push(...this.checkPreventiveControls(control));\r\n        break;\r\n      case ComplianceControlType.DETECTIVE:\r\n        findings.push(...this.checkDetectiveControls(control));\r\n        break;\r\n      case ComplianceControlType.CORRECTIVE:\r\n        findings.push(...this.checkCorrectiveControls(control));\r\n        break;\r\n    }\r\n\r\n    return findings;\r\n  }\r\n\r\n  private checkPreventiveControls(control: ComplianceControl): ComplianceFinding[] {\r\n    // Placeholder for preventive control checks\r\n    // In real implementation, this would integrate with security tools\r\n    return [];\r\n  }\r\n\r\n  private checkDetectiveControls(control: ComplianceControl): ComplianceFinding[] {\r\n    // Placeholder for detective control checks\r\n    // In real implementation, this would check monitoring systems\r\n    return [];\r\n  }\r\n\r\n  private checkCorrectiveControls(control: ComplianceControl): ComplianceFinding[] {\r\n    // Placeholder for corrective control checks\r\n    // In real implementation, this would verify remediation processes\r\n    return [];\r\n  }\r\n\r\n  private determineComplianceStatus(findings: ComplianceFinding[]): ComplianceStatus {\r\n    if (findings.length === 0) {\r\n      return ComplianceStatus.COMPLIANT;\r\n    }\r\n\r\n    const criticalFindings = findings.filter(f => f.severity === ComplianceFindingSeverity.CRITICAL);\r\n    const highFindings = findings.filter(f => f.severity === ComplianceFindingSeverity.HIGH);\r\n\r\n    if (criticalFindings.length > 0) {\r\n      return ComplianceStatus.NON_COMPLIANT;\r\n    }\r\n\r\n    if (highFindings.length > 0) {\r\n      return ComplianceStatus.PARTIALLY_COMPLIANT;\r\n    }\r\n\r\n    return ComplianceStatus.PARTIALLY_COMPLIANT;\r\n  }\r\n\r\n  private generateRecommendations(findings: ComplianceFinding[]): string[] {\r\n    return findings.map(f => f.recommendation).filter(r => r && r.trim().length > 0);\r\n  }\r\n\r\n  private calculateNextAssessmentDate(control: ComplianceControl): Timestamp {\r\n    const now = new Date();\r\n    let nextDate = new Date(now);\r\n\r\n    switch (control.checkFrequency) {\r\n      case ComplianceCheckFrequency.DAILY:\r\n        nextDate.setDate(now.getDate() + 1);\r\n        break;\r\n      case ComplianceCheckFrequency.WEEKLY:\r\n        nextDate.setDate(now.getDate() + 7);\r\n        break;\r\n      case ComplianceCheckFrequency.MONTHLY:\r\n        nextDate.setMonth(now.getMonth() + 1);\r\n        break;\r\n      case ComplianceCheckFrequency.QUARTERLY:\r\n        nextDate.setMonth(now.getMonth() + 3);\r\n        break;\r\n      case ComplianceCheckFrequency.ANNUALLY:\r\n        nextDate.setFullYear(now.getFullYear() + 1);\r\n        break;\r\n      default:\r\n        nextDate.setMonth(now.getMonth() + 3); // Default to quarterly\r\n    }\r\n\r\n    return Timestamp.create(nextDate);\r\n  }\r\n\r\n  private calculateControlsSummary(controls: ComplianceControl[]) {\r\n    const summary = {\r\n      total: controls.length,\r\n      compliant: 0,\r\n      nonCompliant: 0,\r\n      partiallyCompliant: 0,\r\n      notAssessed: 0\r\n    };\r\n\r\n    controls.forEach(control => {\r\n      switch (control.status) {\r\n        case ComplianceStatus.COMPLIANT:\r\n          summary.compliant++;\r\n          break;\r\n        case ComplianceStatus.NON_COMPLIANT:\r\n          summary.nonCompliant++;\r\n          break;\r\n        case ComplianceStatus.PARTIALLY_COMPLIANT:\r\n          summary.partiallyCompliant++;\r\n          break;\r\n        case ComplianceStatus.NOT_ASSESSED:\r\n          summary.notAssessed++;\r\n          break;\r\n      }\r\n    });\r\n\r\n    return summary;\r\n  }\r\n\r\n  private calculateOverallStatus(summary: any): ComplianceStatus {\r\n    if (summary.total === 0) return ComplianceStatus.NOT_ASSESSED;\r\n    if (summary.nonCompliant > 0) return ComplianceStatus.NON_COMPLIANT;\r\n    if (summary.partiallyCompliant > 0) return ComplianceStatus.PARTIALLY_COMPLIANT;\r\n    if (summary.notAssessed > 0) return ComplianceStatus.NOT_ASSESSED;\r\n    return ComplianceStatus.COMPLIANT;\r\n  }\r\n\r\n  private generateFrameworkRecommendations(framework: ComplianceFramework, findings: ComplianceFinding[]): string[] {\r\n    const recommendations = new Set<string>();\r\n    \r\n    findings.forEach(finding => {\r\n      if (finding.recommendation) {\r\n        recommendations.add(finding.recommendation);\r\n      }\r\n    });\r\n\r\n    // Add framework-specific recommendations\r\n    switch (framework) {\r\n      case ComplianceFramework.SOC2:\r\n        recommendations.add('Ensure continuous monitoring of security controls');\r\n        recommendations.add('Maintain comprehensive audit logs');\r\n        break;\r\n      case ComplianceFramework.GDPR:\r\n        recommendations.add('Implement data protection by design and by default');\r\n        recommendations.add('Maintain records of processing activities');\r\n        break;\r\n      // Add more framework-specific recommendations as needed\r\n    }\r\n\r\n    return Array.from(recommendations);\r\n  }\r\n\r\n  private generateExecutiveSummary(\r\n    framework: ComplianceFramework,\r\n    summary: any,\r\n    findings: ComplianceFinding[]\r\n  ): string {\r\n    const compliancePercentage = Math.round((summary.compliant / summary.total) * 100);\r\n    const criticalFindings = findings.filter(f => f.severity === ComplianceFindingSeverity.CRITICAL).length;\r\n    \r\n    return `${framework} Compliance Assessment: ${compliancePercentage}% compliant. ` +\r\n           `${summary.total} controls assessed, ${summary.compliant} compliant, ` +\r\n           `${summary.nonCompliant} non-compliant. ${criticalFindings} critical findings require immediate attention.`;\r\n  }\r\n\r\n  private aggregateComplianceStatus(statuses: ComplianceStatus[]): ComplianceStatus {\r\n    if (statuses.includes(ComplianceStatus.NON_COMPLIANT)) {\r\n      return ComplianceStatus.NON_COMPLIANT;\r\n    }\r\n    if (statuses.includes(ComplianceStatus.PARTIALLY_COMPLIANT)) {\r\n      return ComplianceStatus.PARTIALLY_COMPLIANT;\r\n    }\r\n    if (statuses.includes(ComplianceStatus.NOT_ASSESSED)) {\r\n      return ComplianceStatus.NOT_ASSESSED;\r\n    }\r\n    return ComplianceStatus.COMPLIANT;\r\n  }\r\n\r\n  private touch(): void {\r\n    // Update the last modified timestamp\r\n    // This would typically update an updatedAt field if we had one\r\n  }\r\n\r\n  private validateProps(props: any): void {\r\n    if (!props.name || props.name.trim().length === 0) {\r\n      throw new ValidationException('Policy name is required', []);\r\n    }\r\n\r\n    if (!props.description || props.description.trim().length === 0) {\r\n      throw new ValidationException('Policy description is required', []);\r\n    }\r\n\r\n    if (!Object.values(ComplianceFramework).includes(props.framework)) {\r\n      throw new ValidationException('Valid compliance framework is required', []);\r\n    }\r\n\r\n    if (!props.version || props.version.trim().length === 0) {\r\n      throw new ValidationException('Policy version is required', []);\r\n    }\r\n\r\n    if (!props.tenantId) {\r\n      throw new ValidationException('Tenant ID is required', []);\r\n    }\r\n\r\n    if (!props.createdBy) {\r\n      throw new ValidationException('Created by user ID is required', []);\r\n    }\r\n\r\n    if (!Array.isArray(props.controls)) {\r\n      throw new ValidationException('Controls must be an array', []);\r\n    }\r\n\r\n    props.controls.forEach((control: ComplianceControl) => this.validateControl(control));\r\n  }\r\n\r\n  private validateControl(control: ComplianceControl): void {\r\n    if (!control.id || control.id.trim().length === 0) {\r\n      throw new ValidationException('Control ID is required', []);\r\n    }\r\n\r\n    if (!control.name || control.name.trim().length === 0) {\r\n      throw new ValidationException('Control name is required', []);\r\n    }\r\n\r\n    if (!control.controlId || control.controlId.trim().length === 0) {\r\n      throw new ValidationException('Control framework ID is required', []);\r\n    }\r\n\r\n    if (!Object.values(ComplianceFramework).includes(control.framework)) {\r\n      throw new ValidationException('Valid compliance framework is required for control', []);\r\n    }\r\n\r\n    if (!Object.values(ComplianceControlType).includes(control.type)) {\r\n      throw new ValidationException('Valid control type is required', []);\r\n    }\r\n\r\n    if (!control.responsible) {\r\n      throw new ValidationException('Control responsible user is required', []);\r\n    }\r\n\r\n    if (!Array.isArray(control.requirements)) {\r\n      throw new ValidationException('Control requirements must be an array', []);\r\n    }\r\n\r\n    if (!Array.isArray(control.evidence)) {\r\n      throw new ValidationException('Control evidence must be an array', []);\r\n    }\r\n  }\r\n\r\n  // Getters\r\n  public get name(): string { return this._name; }\r\n  public get description(): string { return this._description; }\r\n  public get framework(): ComplianceFramework { return this._framework; }\r\n  public get version(): string { return this._version; }\r\n  public get tenantId(): TenantId { return this._tenantId; }\r\n  public get controls(): ComplianceControl[] { return [...this._controls]; }\r\n  public get enabled(): boolean { return this._enabled; }\r\n  public get createdBy(): UserId { return this._createdBy; }\r\n  public get lastModifiedBy(): UserId { return this._lastModifiedBy; }\r\n  public get assessments(): ComplianceAssessment[] { return [...this._assessments]; }\r\n  public get reports(): ComplianceReport[] { return [...this._reports]; }\r\n}"], "version": 3}