{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\decorators\\retry.decorator.spec.ts", "mappings": ";;;;;;;;;;;AAAA,sEAK0C;AAC1C,kEAA8D;AAE9D,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,UAAU,CAAC,GAAG,EAAE;QACd,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;;YAC9C,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,MAAM,WAAW;gBAET,AAAN,KAAK,CAAC,WAAW;oBACf,YAAY,EAAE,CAAC;oBACf,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;wBACrB,MAAM,IAAI,KAAK,CAAC,WAAW,YAAY,SAAS,CAAC,CAAC;oBACpD,CAAC;oBACD,OAAO,SAAS,CAAC;gBACnB,CAAC;aACF;YAPO;gBADL,IAAA,uBAAK,EAAC,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;;;oEACpB,OAAO,oBAAP,OAAO;0DAM3B;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,EAAE,CAAC;YAE3C,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;;YAC9D,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,MAAM,WAAW;gBAET,AAAN,KAAK,CAAC,mBAAmB;oBACvB,YAAY,EAAE,CAAC;oBACf,MAAM,IAAI,KAAK,CAAC,WAAW,YAAY,SAAS,CAAC,CAAC;gBACpD,CAAC;aACF;YAJO;gBADL,IAAA,uBAAK,EAAC,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;;;oEACZ,OAAO,oBAAP,OAAO;kEAGnC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,MAAM,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAChF,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;;YACpE,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,MAAM,WAAW;gBAET,AAAN,KAAK,CAAC,gBAAgB;oBACpB,YAAY,EAAE,CAAC;oBACf,OAAO,mBAAmB,CAAC;gBAC7B,CAAC;aACF;YAJO;gBADL,IAAA,uBAAK,EAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC;;;oEACA,OAAO,oBAAP,OAAO;+DAGhC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACzC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;;YAChD,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,MAAM,YAAY,GAAG;gBACnB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;oBACjD,YAAY,EAAE,CAAC;oBACf,OAAO,MAAM,EAAE,EAAE,CAAC;gBACpB,CAAC,CAAC;aACH,CAAC;YAEF,MAAM,WAAW;gBAAjB;oBACE,kBAAa,GAAG,YAAY,CAAC;gBAM/B,CAAC;gBAHO,AAAN,KAAK,CAAC,wBAAwB;oBAC5B,OAAO,wBAAwB,CAAC;gBAClC,CAAC;aACF;YAHO;gBADL,IAAA,uBAAK,GAAE;;;oEAC0B,OAAO,oBAAP,OAAO;uEAExC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,EAAE,CAAC;YAExD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC9C,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;;YAChD,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,MAAM,WAAY,SAAQ,KAAK;gBAC7B,YAAY,OAAe;oBACzB,KAAK,CAAC,OAAO,CAAC,CAAC;oBACf,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;gBAC5B,CAAC;aACF;YAED,MAAM,WAAW;gBAMT,AAAN,KAAK,CAAC,oBAAoB,CAAC,WAAoB;oBAC7C,YAAY,EAAE,CAAC;oBACf,IAAI,WAAW,EAAE,CAAC;wBAChB,MAAM,IAAI,WAAW,CAAC,iBAAiB,CAAC,CAAC;oBAC3C,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;oBACzC,CAAC;gBACH,CAAC;aACF;YARO;gBALL,IAAA,uBAAK,EAAC;oBACL,WAAW,EAAE,CAAC;oBACd,SAAS,EAAE,EAAE;oBACb,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,YAAY,WAAW;iBACjD,CAAC;;;oEACgD,OAAO,oBAAP,OAAO;mEAOxD;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,sBAAsB;YACtB,YAAY,GAAG,CAAC,CAAC;YAEjB,8CAA8C;YAC9C,MAAM,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YACzF,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE7B,sBAAsB;YACtB,YAAY,GAAG,CAAC,CAAC;YAEjB,oCAAoC;YACpC,MAAM,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YACpF,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;;YACvD,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,kBAAkB,GAAG,MAAM,CAAC,UAAU,CAAC;YAE7C,oCAAoC;YACpC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACnE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnB,OAAO,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,+BAA+B;YACzE,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW;gBAOT,AAAN,KAAK,CAAC,eAAe;oBACnB,YAAY,EAAE,CAAC;oBACf,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;wBACrB,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;oBAClC,CAAC;oBACD,OAAO,SAAS,CAAC;gBACnB,CAAC;aACF;YAPO;gBANL,IAAA,uBAAK,EAAC;oBACL,WAAW,EAAE,CAAC;oBACd,SAAS,EAAE,GAAG;oBACd,iBAAiB,EAAE,CAAC;oBACpB,MAAM,EAAE,KAAK,CAAC,wCAAwC;iBACvD,CAAC;;;oEACuB,OAAO,oBAAP,OAAO;8DAM/B;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,OAAO,CAAC,eAAe,EAAE,CAAC;YAEhC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc;YAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,yBAAyB;YACtD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,8BAA8B;YAE3D,8BAA8B;YAC9B,MAAM,CAAC,UAAU,GAAG,kBAAkB,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;;YACtE,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,MAAM,WAAW;gBAET,AAAN,KAAK,CAAC,iBAAiB;oBACrB,YAAY,EAAE,CAAC;oBACf,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;wBACrB,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;oBAClC,CAAC;oBACD,OAAO,qBAAqB,CAAC;gBAC/B,CAAC;aACF;YAPO;gBADL,IAAA,oCAAkB,EAAC,CAAC,EAAE,EAAE,CAAC;;;oEACC,OAAO,oBAAP,OAAO;gEAMjC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAEjD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC3C,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;;YAChE,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,MAAM,WAAW;gBAET,AAAN,KAAK,CAAC,wBAAwB;oBAC5B,YAAY,EAAE,CAAC;oBACf,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;wBACrB,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;oBAClC,CAAC;oBACD,OAAO,6BAA6B,CAAC;gBACvC,CAAC;aACF;YAPO;gBADL,IAAA,oCAAkB,GAAE;;;oEACa,OAAO,oBAAP,OAAO;uEAMxC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,EAAE,CAAC;YAExD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACnD,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;;YAC3C,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,MAAM,WAAW;gBAET,AAAN,KAAK,CAAC,YAAY;oBAChB,YAAY,EAAE,CAAC;oBACf,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;wBACrB,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;oBAClC,CAAC;oBACD,OAAO,gBAAgB,CAAC;gBAC1B,CAAC;aACF;YAPO;gBADL,IAAA,+BAAa,EAAC,CAAC,EAAE,GAAG,CAAC;;;oEACA,OAAO,oBAAP,OAAO;2DAM5B;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,EAAE,CAAC;YAE5C,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACtC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;;YAC1D,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,MAAM,YAAa,SAAQ,KAAK;gBAC9B,YAAY,OAAe;oBACzB,KAAK,CAAC,OAAO,CAAC,CAAC;oBACf,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;gBAC7B,CAAC;aACF;YAED,MAAM,eAAgB,SAAQ,KAAK;gBACjC,YAAY,OAAe;oBACzB,KAAK,CAAC,OAAO,CAAC,CAAC;oBACf,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;gBAChC,CAAC;aACF;YAED,MAAM,WAAW;gBAET,AAAN,KAAK,CAAC,mBAAmB,CAAC,SAAmC;oBAC3D,YAAY,EAAE,CAAC;oBACf,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;wBAC5B,MAAM,IAAI,YAAY,CAAC,gBAAgB,CAAC,CAAC;oBAC3C,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,eAAe,CAAC,mBAAmB,CAAC,CAAC;oBACjD,CAAC;gBACH,CAAC;aACF;YARO;gBADL,IAAA,8BAAY,EAAC,CAAC,YAAY,CAAC,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;;;oEACA,OAAO,oBAAP,OAAO;kEAOtE;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,sBAAsB;YACtB,YAAY,GAAG,CAAC,CAAC;YAEjB,iCAAiC;YACjC,MAAM,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACvF,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE7B,sBAAsB;YACtB,YAAY,GAAG,CAAC,CAAC;YAEjB,wCAAwC;YACxC,MAAM,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAC7F,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;;YACrD,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,MAAM,YAAa,SAAQ,KAAK;gBAC9B,YAAY,OAAe;oBACzB,KAAK,CAAC,OAAO,CAAC,CAAC;oBACf,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;gBAC7B,CAAC;aACF;YAED,MAAM,YAAa,SAAQ,KAAK;gBAC9B,YAAY,OAAe;oBACzB,KAAK,CAAC,OAAO,CAAC,CAAC;oBACf,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;gBAC7B,CAAC;aACF;YAED,MAAM,WAAW;gBAET,AAAN,KAAK,CAAC,gBAAgB,CAAC,SAA0C;oBAC/D,YAAY,EAAE,CAAC;oBACf,QAAQ,SAAS,EAAE,CAAC;wBAClB,KAAK,SAAS;4BACZ,MAAM,IAAI,YAAY,CAAC,eAAe,CAAC,CAAC;wBAC1C,KAAK,SAAS;4BACZ,MAAM,IAAI,YAAY,CAAC,eAAe,CAAC,CAAC;wBAC1C;4BACE,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC;aACF;YAXO;gBADL,IAAA,8BAAY,EAAC,CAAC,YAAY,EAAE,YAAY,CAAC,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;;;oEACV,OAAO,oBAAP,OAAO;+DAU1E;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,iCAAiC;YACjC,YAAY,GAAG,CAAC,CAAC;YACjB,MAAM,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YACnF,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE7B,iCAAiC;YACjC,YAAY,GAAG,CAAC,CAAC;YACjB,MAAM,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YACnF,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE7B,oCAAoC;YACpC,YAAY,GAAG,CAAC,CAAC;YACjB,MAAM,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAC/E,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;;YAC9D,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,MAAM,cAAc,GAAG,IAAI,8BAAa,CAAC;gBACvC,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,EAAE;gBACb,iBAAiB,EAAE,GAAG;gBACtB,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,MAAM,WAAW;gBAET,AAAN,KAAK,CAAC,oBAAoB;oBACxB,YAAY,EAAE,CAAC;oBACf,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;wBACrB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;oBAC3C,CAAC;oBACD,OAAO,yBAAyB,CAAC;gBACnC,CAAC;aACF;YAPO;gBADL,IAAA,uBAAK,EAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC;;;oEACN,OAAO,oBAAP,OAAO;mEAMpC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,EAAE,CAAC;YAEpD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC/C,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;;YAChD,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,MAAM,WAAW;gBAET,AAAN,KAAK,CAAC,eAAe;oBACnB,YAAY,EAAE,CAAC;oBACf,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;gBACvC,CAAC;aACF;YAJO;gBADL,IAAA,uBAAK,EAAC,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;;;oEAChB,OAAO,oBAAP,OAAO;8DAG/B;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAC7E,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;;YAClD,MAAM,WAAW;gBAET,AAAN,KAAK,CAAC,gBAAgB;oBACpB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;oBAC5C,KAAK,CAAC,KAAK,GAAG,sBAAsB,CAAC;oBACrC,MAAM,KAAK,CAAC;gBACd,CAAC;aACF;YALO;gBADL,IAAA,uBAAK,EAAC,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;;;oEACf,OAAO,oBAAP,OAAO;+DAIhC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,IAAI,CAAC;gBACH,MAAM,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBACjC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACnC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACpC,MAAM,CAAE,KAAe,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAC1D,MAAM,CAAE,KAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\decorators\\retry.decorator.spec.ts"], "sourcesContent": ["import { \r\n  Retry, \r\n  ExponentialBackoff, \r\n  LinearBackoff, \r\n  RetryOnError \r\n} from '../../decorators/retry.decorator';\r\nimport { RetryStrategy } from '../../patterns/retry-strategy';\r\n\r\ndescribe('Retry Decorator', () => {\r\n  beforeEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('@Retry decorator', () => {\r\n    it('should retry failed operations', async () => {\r\n      let attemptCount = 0;\r\n\r\n      class TestService {\r\n        @Retry({ maxAttempts: 3, baseDelay: 10 })\r\n        async flakyMethod(): Promise<string> {\r\n          attemptCount++;\r\n          if (attemptCount < 3) {\r\n            throw new Error(`Attempt ${attemptCount} failed`);\r\n          }\r\n          return 'success';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      const result = await service.flakyMethod();\r\n\r\n      expect(result).toBe('success');\r\n      expect(attemptCount).toBe(3);\r\n    });\r\n\r\n    it('should throw error after max attempts exceeded', async () => {\r\n      let attemptCount = 0;\r\n\r\n      class TestService {\r\n        @Retry({ maxAttempts: 2, baseDelay: 10 })\r\n        async alwaysFailingMethod(): Promise<string> {\r\n          attemptCount++;\r\n          throw new Error(`Attempt ${attemptCount} failed`);\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      await expect(service.alwaysFailingMethod()).rejects.toThrow('Attempt 2 failed');\r\n      expect(attemptCount).toBe(2);\r\n    });\r\n\r\n    it('should succeed on first attempt when no error occurs', async () => {\r\n      let attemptCount = 0;\r\n\r\n      class TestService {\r\n        @Retry({ maxAttempts: 3 })\r\n        async successfulMethod(): Promise<string> {\r\n          attemptCount++;\r\n          return 'immediate success';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      const result = await service.successfulMethod();\r\n\r\n      expect(result).toBe('immediate success');\r\n      expect(attemptCount).toBe(1);\r\n    });\r\n\r\n    it('should use custom retry strategy', async () => {\r\n      let attemptCount = 0;\r\n      const mockStrategy = {\r\n        execute: jest.fn().mockImplementation(async (fn) => {\r\n          attemptCount++;\r\n          return await fn();\r\n        }),\r\n      };\r\n\r\n      class TestService {\r\n        retryStrategy = mockStrategy;\r\n\r\n        @Retry()\r\n        async methodWithCustomStrategy(): Promise<string> {\r\n          return 'custom strategy result';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      const result = await service.methodWithCustomStrategy();\r\n\r\n      expect(result).toBe('custom strategy result');\r\n      expect(mockStrategy.execute).toHaveBeenCalledTimes(1);\r\n    });\r\n\r\n    it('should respect retryOn condition', async () => {\r\n      let attemptCount = 0;\r\n\r\n      class CustomError extends Error {\r\n        constructor(message: string) {\r\n          super(message);\r\n          this.name = 'CustomError';\r\n        }\r\n      }\r\n\r\n      class TestService {\r\n        @Retry({ \r\n          maxAttempts: 3, \r\n          baseDelay: 10,\r\n          retryOn: (error) => error instanceof CustomError \r\n        })\r\n        async selectiveRetryMethod(shouldRetry: boolean): Promise<string> {\r\n          attemptCount++;\r\n          if (shouldRetry) {\r\n            throw new CustomError('Retryable error');\r\n          } else {\r\n            throw new Error('Non-retryable error');\r\n          }\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      // Reset attempt count\r\n      attemptCount = 0;\r\n      \r\n      // Non-retryable error should fail immediately\r\n      await expect(service.selectiveRetryMethod(false)).rejects.toThrow('Non-retryable error');\r\n      expect(attemptCount).toBe(1);\r\n\r\n      // Reset attempt count\r\n      attemptCount = 0;\r\n      \r\n      // Retryable error should be retried\r\n      await expect(service.selectiveRetryMethod(true)).rejects.toThrow('Retryable error');\r\n      expect(attemptCount).toBe(3);\r\n    });\r\n\r\n    it('should apply exponential backoff delays', async () => {\r\n      let attemptCount = 0;\r\n      const delays: number[] = [];\r\n      const originalSetTimeout = global.setTimeout;\r\n\r\n      // Mock setTimeout to capture delays\r\n      global.setTimeout = jest.fn().mockImplementation((callback, delay) => {\r\n        delays.push(delay);\r\n        return originalSetTimeout(callback, 0); // Execute immediately for test\r\n      });\r\n\r\n      class TestService {\r\n        @Retry({ \r\n          maxAttempts: 3, \r\n          baseDelay: 100, \r\n          backoffMultiplier: 2,\r\n          jitter: false // Disable jitter for predictable delays\r\n        })\r\n        async delayTestMethod(): Promise<string> {\r\n          attemptCount++;\r\n          if (attemptCount < 3) {\r\n            throw new Error('Retry needed');\r\n          }\r\n          return 'success';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      await service.delayTestMethod();\r\n\r\n      expect(delays).toHaveLength(2); // Two retries\r\n      expect(delays[0]).toBe(100); // First retry: baseDelay\r\n      expect(delays[1]).toBe(200); // Second retry: baseDelay * 2\r\n\r\n      // Restore original setTimeout\r\n      global.setTimeout = originalSetTimeout;\r\n    });\r\n  });\r\n\r\n  describe('@ExponentialBackoff decorator', () => {\r\n    it('should apply exponential backoff with default settings', async () => {\r\n      let attemptCount = 0;\r\n\r\n      class TestService {\r\n        @ExponentialBackoff(3, 50)\r\n        async exponentialMethod(): Promise<string> {\r\n          attemptCount++;\r\n          if (attemptCount < 3) {\r\n            throw new Error('Retry needed');\r\n          }\r\n          return 'exponential success';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      const result = await service.exponentialMethod();\r\n\r\n      expect(result).toBe('exponential success');\r\n      expect(attemptCount).toBe(3);\r\n    });\r\n\r\n    it('should use default parameters when not specified', async () => {\r\n      let attemptCount = 0;\r\n\r\n      class TestService {\r\n        @ExponentialBackoff()\r\n        async defaultExponentialMethod(): Promise<string> {\r\n          attemptCount++;\r\n          if (attemptCount < 2) {\r\n            throw new Error('Retry needed');\r\n          }\r\n          return 'default exponential success';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      const result = await service.defaultExponentialMethod();\r\n\r\n      expect(result).toBe('default exponential success');\r\n      expect(attemptCount).toBe(2);\r\n    });\r\n  });\r\n\r\n  describe('@LinearBackoff decorator', () => {\r\n    it('should apply linear backoff', async () => {\r\n      let attemptCount = 0;\r\n\r\n      class TestService {\r\n        @LinearBackoff(3, 100)\r\n        async linearMethod(): Promise<string> {\r\n          attemptCount++;\r\n          if (attemptCount < 3) {\r\n            throw new Error('Retry needed');\r\n          }\r\n          return 'linear success';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      const result = await service.linearMethod();\r\n\r\n      expect(result).toBe('linear success');\r\n      expect(attemptCount).toBe(3);\r\n    });\r\n  });\r\n\r\n  describe('@RetryOnError decorator', () => {\r\n    it('should only retry on specified error types', async () => {\r\n      let attemptCount = 0;\r\n\r\n      class NetworkError extends Error {\r\n        constructor(message: string) {\r\n          super(message);\r\n          this.name = 'NetworkError';\r\n        }\r\n      }\r\n\r\n      class ValidationError extends Error {\r\n        constructor(message: string) {\r\n          super(message);\r\n          this.name = 'ValidationError';\r\n        }\r\n      }\r\n\r\n      class TestService {\r\n        @RetryOnError([NetworkError], { maxAttempts: 3, baseDelay: 10 })\r\n        async errorSpecificMethod(errorType: 'network' | 'validation'): Promise<string> {\r\n          attemptCount++;\r\n          if (errorType === 'network') {\r\n            throw new NetworkError('Network failed');\r\n          } else {\r\n            throw new ValidationError('Validation failed');\r\n          }\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      // Reset attempt count\r\n      attemptCount = 0;\r\n      \r\n      // NetworkError should be retried\r\n      await expect(service.errorSpecificMethod('network')).rejects.toThrow('Network failed');\r\n      expect(attemptCount).toBe(3);\r\n\r\n      // Reset attempt count\r\n      attemptCount = 0;\r\n      \r\n      // ValidationError should not be retried\r\n      await expect(service.errorSpecificMethod('validation')).rejects.toThrow('Validation failed');\r\n      expect(attemptCount).toBe(1);\r\n    });\r\n\r\n    it('should work with multiple error types', async () => {\r\n      let attemptCount = 0;\r\n\r\n      class NetworkError extends Error {\r\n        constructor(message: string) {\r\n          super(message);\r\n          this.name = 'NetworkError';\r\n        }\r\n      }\r\n\r\n      class TimeoutError extends Error {\r\n        constructor(message: string) {\r\n          super(message);\r\n          this.name = 'TimeoutError';\r\n        }\r\n      }\r\n\r\n      class TestService {\r\n        @RetryOnError([NetworkError, TimeoutError], { maxAttempts: 2, baseDelay: 10 })\r\n        async multiErrorMethod(errorType: 'network' | 'timeout' | 'other'): Promise<string> {\r\n          attemptCount++;\r\n          switch (errorType) {\r\n            case 'network':\r\n              throw new NetworkError('Network error');\r\n            case 'timeout':\r\n              throw new TimeoutError('Timeout error');\r\n            default:\r\n              throw new Error('Other error');\r\n          }\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      // NetworkError should be retried\r\n      attemptCount = 0;\r\n      await expect(service.multiErrorMethod('network')).rejects.toThrow('Network error');\r\n      expect(attemptCount).toBe(2);\r\n\r\n      // TimeoutError should be retried\r\n      attemptCount = 0;\r\n      await expect(service.multiErrorMethod('timeout')).rejects.toThrow('Timeout error');\r\n      expect(attemptCount).toBe(2);\r\n\r\n      // Other error should not be retried\r\n      attemptCount = 0;\r\n      await expect(service.multiErrorMethod('other')).rejects.toThrow('Other error');\r\n      expect(attemptCount).toBe(1);\r\n    });\r\n  });\r\n\r\n  describe('integration with RetryStrategy', () => {\r\n    it('should work with custom RetryStrategy instance', async () => {\r\n      let attemptCount = 0;\r\n      const customStrategy = new RetryStrategy({\r\n        maxAttempts: 4,\r\n        baseDelay: 25,\r\n        backoffMultiplier: 1.5,\r\n        jitter: false,\r\n      });\r\n\r\n      class TestService {\r\n        @Retry({ strategy: customStrategy })\r\n        async customStrategyMethod(): Promise<string> {\r\n          attemptCount++;\r\n          if (attemptCount < 4) {\r\n            throw new Error('Custom strategy retry');\r\n          }\r\n          return 'custom strategy success';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      const result = await service.customStrategyMethod();\r\n\r\n      expect(result).toBe('custom strategy success');\r\n      expect(attemptCount).toBe(4);\r\n    });\r\n  });\r\n\r\n  describe('error handling edge cases', () => {\r\n    it('should handle synchronous errors', async () => {\r\n      let attemptCount = 0;\r\n\r\n      class TestService {\r\n        @Retry({ maxAttempts: 2, baseDelay: 10 })\r\n        async syncErrorMethod(): Promise<string> {\r\n          attemptCount++;\r\n          throw new Error('Synchronous error');\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      await expect(service.syncErrorMethod()).rejects.toThrow('Synchronous error');\r\n      expect(attemptCount).toBe(2);\r\n    });\r\n\r\n    it('should preserve error stack traces', async () => {\r\n      class TestService {\r\n        @Retry({ maxAttempts: 2, baseDelay: 10 })\r\n        async stackTraceMethod(): Promise<string> {\r\n          const error = new Error('Stack trace test');\r\n          error.stack = 'Original stack trace';\r\n          throw error;\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      try {\r\n        await service.stackTraceMethod();\r\n        fail('Should have thrown error');\r\n      } catch (error) {\r\n        expect(error).toBeInstanceOf(Error);\r\n        expect((error as Error).message).toBe('Stack trace test');\r\n        expect((error as Error).stack).toBe('Original stack trace');\r\n      }\r\n    });\r\n  });\r\n});"], "version": 3}