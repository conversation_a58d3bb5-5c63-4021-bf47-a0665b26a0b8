69013e5a87e5e03fb5b8f8462f8712bf
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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