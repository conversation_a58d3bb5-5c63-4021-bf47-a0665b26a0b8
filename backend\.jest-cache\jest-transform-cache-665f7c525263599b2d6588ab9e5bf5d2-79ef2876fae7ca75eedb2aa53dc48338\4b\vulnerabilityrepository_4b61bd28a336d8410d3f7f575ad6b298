f3e57aa4bd793809ed58a47cace1b424
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityStatus = void 0;
/**
 * Vulnerability Status Enum (placeholder until actual enum is implemented)
 */
var VulnerabilityStatus;
(function (VulnerabilityStatus) {
    VulnerabilityStatus["DISCOVERED"] = "DISCOVERED";
    VulnerabilityStatus["CONFIRMED"] = "CONFIRMED";
    VulnerabilityStatus["TRIAGED"] = "TRIAGED";
    VulnerabilityStatus["IN_PROGRESS"] = "IN_PROGRESS";
    VulnerabilityStatus["REMEDIATED"] = "REMEDIATED";
    VulnerabilityStatus["VERIFIED"] = "VERIFIED";
    VulnerabilityStatus["CLOSED"] = "CLOSED";
    VulnerabilityStatus["FALSE_POSITIVE"] = "FALSE_POSITIVE";
    VulnerabilityStatus["ACCEPTED_RISK"] = "ACCEPTED_RISK";
    VulnerabilityStatus["DEFERRED"] = "DEFERRED";
})(VulnerabilityStatus || (exports.VulnerabilityStatus = VulnerabilityStatus = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFxyZXBvc2l0b3JpZXNcXHZ1bG5lcmFiaWxpdHkucmVwb3NpdG9yeS50cyIsIm1hcHBpbmdzIjoiOzs7QUFJQTs7R0FFRztBQUNILElBQVksbUJBV1g7QUFYRCxXQUFZLG1CQUFtQjtJQUM3QixnREFBeUIsQ0FBQTtJQUN6Qiw4Q0FBdUIsQ0FBQTtJQUN2QiwwQ0FBbUIsQ0FBQTtJQUNuQixrREFBMkIsQ0FBQTtJQUMzQixnREFBeUIsQ0FBQTtJQUN6Qiw0Q0FBcUIsQ0FBQTtJQUNyQix3Q0FBaUIsQ0FBQTtJQUNqQix3REFBaUMsQ0FBQTtJQUNqQyxzREFBK0IsQ0FBQTtJQUMvQiw0Q0FBcUIsQ0FBQTtBQUN2QixDQUFDLEVBWFcsbUJBQW1CLG1DQUFuQixtQkFBbUIsUUFXOUIiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFxyZXBvc2l0b3JpZXNcXHZ1bG5lcmFiaWxpdHkucmVwb3NpdG9yeS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBZ2dyZWdhdGVSZXBvc2l0b3J5LCBVbmlxdWVFbnRpdHlJZCB9IGZyb20gJy4uLy4uLy4uLy4uL3NoYXJlZC1rZXJuZWwnO1xyXG5pbXBvcnQgeyBUaHJlYXRTZXZlcml0eSB9IGZyb20gJy4uL2VudW1zL3RocmVhdC1zZXZlcml0eS5lbnVtJztcclxuaW1wb3J0IHsgVnVsbmVyYWJpbGl0eVNldmVyaXR5IH0gZnJvbSAnLi4vZW51bXMvdnVsbmVyYWJpbGl0eS1zZXZlcml0eS5lbnVtJztcclxuXHJcbi8qKlxyXG4gKiBWdWxuZXJhYmlsaXR5IFN0YXR1cyBFbnVtIChwbGFjZWhvbGRlciB1bnRpbCBhY3R1YWwgZW51bSBpcyBpbXBsZW1lbnRlZClcclxuICovXHJcbmV4cG9ydCBlbnVtIFZ1bG5lcmFiaWxpdHlTdGF0dXMge1xyXG4gIERJU0NPVkVSRUQgPSAnRElTQ09WRVJFRCcsXHJcbiAgQ09ORklSTUVEID0gJ0NPTkZJUk1FRCcsXHJcbiAgVFJJQUdFRCA9ICdUUklBR0VEJyxcclxuICBJTl9QUk9HUkVTUyA9ICdJTl9QUk9HUkVTUycsXHJcbiAgUkVNRURJQVRFRCA9ICdSRU1FRElBVEVEJyxcclxuICBWRVJJRklFRCA9ICdWRVJJRklFRCcsXHJcbiAgQ0xPU0VEID0gJ0NMT1NFRCcsXHJcbiAgRkFMU0VfUE9TSVRJVkUgPSAnRkFMU0VfUE9TSVRJVkUnLFxyXG4gIEFDQ0VQVEVEX1JJU0sgPSAnQUNDRVBURURfUklTSycsXHJcbiAgREVGRVJSRUQgPSAnREVGRVJSRUQnLFxyXG59XHJcblxyXG4vKipcclxuICogVnVsbmVyYWJpbGl0eSBFbnRpdHkgSW50ZXJmYWNlIChwbGFjZWhvbGRlciB1bnRpbCBhY3R1YWwgZW50aXR5IGlzIGltcGxlbWVudGVkKVxyXG4gKiBUaGlzIGludGVyZmFjZSBkZWZpbmVzIHRoZSBleHBlY3RlZCBzdHJ1Y3R1cmUgb2YgdGhlIFZ1bG5lcmFiaWxpdHkgZW50aXR5XHJcbiAqL1xyXG5leHBvcnQgaW50ZXJmYWNlIFZ1bG5lcmFiaWxpdHkge1xyXG4gIGlkOiBVbmlxdWVFbnRpdHlJZDtcclxuICBjdmVJZD86IHN0cmluZztcclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIHNldmVyaXR5OiBWdWxuZXJhYmlsaXR5U2V2ZXJpdHk7XHJcbiAgc3RhdHVzOiBWdWxuZXJhYmlsaXR5U3RhdHVzO1xyXG4gIGN2c3NTY29yZT86IG51bWJlcjtcclxuICByaXNrU2NvcmU6IG51bWJlcjtcclxuICBjYXRlZ29yeTogc3RyaW5nO1xyXG4gIHR5cGU6IHN0cmluZztcclxuICB0YWdzOiBzdHJpbmdbXTtcclxuICBhZmZlY3RlZEFzc2V0czogQXJyYXk8e1xyXG4gICAgaWQ6IHN0cmluZztcclxuICAgIG5hbWU6IHN0cmluZztcclxuICAgIHR5cGU6IHN0cmluZztcclxuICAgIGNyaXRpY2FsaXR5OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnIHwgJ2NyaXRpY2FsJztcclxuICAgIGV4cG9zdXJlOiAnaW50ZXJuYWwnIHwgJ2V4dGVybmFsJyB8ICdkbXonO1xyXG4gIH0+O1xyXG4gIGV4cGxvaXRhdGlvbj86IHtcclxuICAgIHN0YXR1czogJ25vX2V4cGxvaXRhdGlvbicgfCAncHJvb2Zfb2ZfY29uY2VwdCcgfCAnYWN0aXZlX2V4cGxvaXRhdGlvbic7XHJcbiAgICBkaWZmaWN1bHR5OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnO1xyXG4gICAgcmVxdWlyZW1lbnRzOiBzdHJpbmdbXTtcclxuICB9O1xyXG4gIHJlbWVkaWF0aW9uPzoge1xyXG4gICAgZWZmb3J0OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnO1xyXG4gICAgdGltZWxpbmU6IHN0cmluZztcclxuICAgIHN0ZXBzOiBzdHJpbmdbXTtcclxuICAgIHByaW9yaXR5OiBudW1iZXI7XHJcbiAgfTtcclxuICBkaXNjb3ZlcmVkQXQ6IERhdGU7XHJcbiAgY3JlYXRlZEF0OiBEYXRlO1xyXG4gIHVwZGF0ZWRBdDogRGF0ZTtcclxuICByZW1lZGlhdGVkQXQ/OiBEYXRlO1xyXG4gIHZlcmlmaWVkQXQ/OiBEYXRlO1xyXG59XHJcblxyXG4vKipcclxuICogVnVsbmVyYWJpbGl0eSBSZXBvc2l0b3J5IEludGVyZmFjZVxyXG4gKiBcclxuICogUHJvdmlkZXMgc3BlY2lhbGl6ZWQgZGF0YSBhY2Nlc3MgbWV0aG9kcyBmb3IgVnVsbmVyYWJpbGl0eSBhZ2dyZWdhdGVzLlxyXG4gKiBGb2N1c2VzIG9uIHZ1bG5lcmFiaWxpdHkgbWFuYWdlbWVudCwgYXNzZXNzbWVudCwgYW5kIHJlbWVkaWF0aW9uIHRyYWNraW5nLlxyXG4gKiBcclxuICogS2V5IHJlc3BvbnNpYmlsaXRpZXM6XHJcbiAqIC0gVnVsbmVyYWJpbGl0eSBwZXJzaXN0ZW5jZSBhbmQgcmV0cmlldmFsXHJcbiAqIC0gUmlzay1iYXNlZCB2dWxuZXJhYmlsaXR5IHByaW9yaXRpemF0aW9uXHJcbiAqIC0gQXNzZXQtYmFzZWQgdnVsbmVyYWJpbGl0eSBhbmFseXNpc1xyXG4gKiAtIFJlbWVkaWF0aW9uIHRyYWNraW5nIGFuZCBtZXRyaWNzXHJcbiAqIC0gQ29tcGxpYW5jZSBhbmQgcmVwb3J0aW5nIHN1cHBvcnRcclxuICovXHJcbmV4cG9ydCBpbnRlcmZhY2UgVnVsbmVyYWJpbGl0eVJlcG9zaXRvcnkgZXh0ZW5kcyBBZ2dyZWdhdGVSZXBvc2l0b3J5PFZ1bG5lcmFiaWxpdHksIFVuaXF1ZUVudGl0eUlkPiB7XHJcbiAgLyoqXHJcbiAgICogRmluZCB2dWxuZXJhYmlsaXRpZXMgYnkgc2V2ZXJpdHkgbGV2ZWxcclxuICAgKiBDcml0aWNhbCBmb3IgcHJpb3JpdHktYmFzZWQgdnVsbmVyYWJpbGl0eSBtYW5hZ2VtZW50XHJcbiAgICogXHJcbiAgICogQHBhcmFtIHNldmVyaXR5IFRoZSBzZXZlcml0eSBsZXZlbCB0byBmaWx0ZXIgYnlcclxuICAgKiBAcGFyYW0gbGltaXQgT3B0aW9uYWwgbGltaXQgZm9yIHJlc3VsdHNcclxuICAgKiBAcmV0dXJucyBQcm9taXNlIHJlc29sdmluZyB0byB2dWxuZXJhYmlsaXRpZXMgd2l0aCB0aGUgc3BlY2lmaWVkIHNldmVyaXR5XHJcbiAgICovXHJcbiAgZmluZEJ5U2V2ZXJpdHkoc2V2ZXJpdHk6IFZ1bG5lcmFiaWxpdHlTZXZlcml0eSwgbGltaXQ/OiBudW1iZXIpOiBQcm9taXNlPFZ1bG5lcmFiaWxpdHlbXT47XHJcblxyXG4gIC8qKlxyXG4gICAqIEZpbmQgdnVsbmVyYWJpbGl0aWVzIGJ5IG11bHRpcGxlIHNldmVyaXR5IGxldmVsc1xyXG4gICAqIFVzZWZ1bCBmb3IgZmlsdGVyaW5nIGhpZ2gtcHJpb3JpdHkgdnVsbmVyYWJpbGl0aWVzXHJcbiAgICogXHJcbiAgICogQHBhcmFtIHNldmVyaXRpZXMgQXJyYXkgb2Ygc2V2ZXJpdHkgbGV2ZWxzIHRvIGluY2x1ZGVcclxuICAgKiBAcGFyYW0gbGltaXQgT3B0aW9uYWwgbGltaXQgZm9yIHJlc3VsdHNcclxuICAgKiBAcmV0dXJucyBQcm9taXNlIHJlc29sdmluZyB0byB2dWxuZXJhYmlsaXRpZXMgd2l0aCBhbnkgb2YgdGhlIHNwZWNpZmllZCBzZXZlcml0aWVzXHJcbiAgICovXHJcbiAgZmluZEJ5U2V2ZXJpdGllcyhzZXZlcml0aWVzOiBWdWxuZXJhYmlsaXR5U2V2ZXJpdHlbXSwgbGltaXQ/OiBudW1iZXIpOiBQcm9taXNlPFZ1bG5lcmFiaWxpdHlbXT47XHJcblxyXG4gIC8qKlxyXG4gICAqIEZpbmQgY3JpdGljYWwgdnVsbmVyYWJpbGl0aWVzXHJcbiAgICogTW9zdCB1cmdlbnQgdnVsbmVyYWJpbGl0aWVzIHJlcXVpcmluZyBpbW1lZGlhdGUgYXR0ZW50aW9uXHJcbiAgICogXHJcbiAgICogQHBhcmFtIGxpbWl0IE9wdGlvbmFsIGxpbWl0IGZvciByZXN1bHRzXHJcbiAgICogQHJldHVybnMgUHJvbWlzZSByZXNvbHZpbmcgdG8gY3JpdGljYWwgdnVsbmVyYWJpbGl0aWVzXHJcbiAgICovXHJcbiAgZmluZENyaXRpY2FsVnVsbmVyYWJpbGl0aWVzKGxpbWl0PzogbnVtYmVyKTogUHJvbWlzZTxWdWxuZXJhYmlsaXR5W10+O1xyXG5cclxuICAvKipcclxuICAgKiBGaW5kIGhpZ2ggc2V2ZXJpdHkgdnVsbmVyYWJpbGl0aWVzIChISUdIIGFuZCBDUklUSUNBTClcclxuICAgKiBIaWdoLXByaW9yaXR5IHZ1bG5lcmFiaWxpdGllcyBmb3IgZm9jdXNlZCByZW1lZGlhdGlvblxyXG4gICAqIFxyXG4gICAqIEBwYXJhbSBsaW1pdCBPcHRpb25hbCBsaW1pdCBmb3IgcmVzdWx0c1xyXG4gICAqIEByZXR1cm5zIFByb21pc2UgcmVzb2x2aW5nIHRvIGhpZ2ggc2V2ZXJpdHkgdnVsbmVyYWJpbGl0aWVzXHJcbiAgICovXHJcbiAgZmluZEhpZ2hTZXZlcml0eVZ1bG5lcmFiaWxpdGllcyhsaW1pdD86IG51bWJlcik6IFByb21pc2U8VnVsbmVyYWJpbGl0eVtdPjtcclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCB2dWxuZXJhYmlsaXRpZXMgYnkgc3RhdHVzXHJcbiAgICogRXNzZW50aWFsIGZvciB3b3JrZmxvdyBtYW5hZ2VtZW50IGFuZCByZW1lZGlhdGlvbiB0cmFja2luZ1xyXG4gICAqIFxyXG4gICAqIEBwYXJhbSBzdGF0dXMgVGhlIHZ1bG5lcmFiaWxpdHkgc3RhdHVzIHRvIGZpbHRlciBieVxyXG4gICAqIEBwYXJhbSBsaW1pdCBPcHRpb25hbCBsaW1pdCBmb3IgcmVzdWx0c1xyXG4gICAqIEByZXR1cm5zIFByb21pc2UgcmVzb2x2aW5nIHRvIHZ1bG5lcmFiaWxpdGllcyB3aXRoIHRoZSBzcGVjaWZpZWQgc3RhdHVzXHJcbiAgICovXHJcbiAgZmluZEJ5U3RhdHVzKHN0YXR1czogVnVsbmVyYWJpbGl0eVN0YXR1cywgbGltaXQ/OiBudW1iZXIpOiBQcm9taXNlPFZ1bG5lcmFiaWxpdHlbXT47XHJcblxyXG4gIC8qKlxyXG4gICAqIEZpbmQgYWN0aXZlIHZ1bG5lcmFiaWxpdGllcyAobm90IHJlbWVkaWF0ZWQgb3IgY2xvc2VkKVxyXG4gICAqIEN1cnJlbnQgdnVsbmVyYWJpbGl0aWVzIHJlcXVpcmluZyBhdHRlbnRpb25cclxuICAgKiBcclxuICAgKiBAcGFyYW0gbGltaXQgT3B0aW9uYWwgbGltaXQgZm9yIHJlc3VsdHNcclxuICAgKiBAcmV0dXJucyBQcm9taXNlIHJlc29sdmluZyB0byBhY3RpdmUgdnVsbmVyYWJpbGl0aWVzXHJcbiAgICovXHJcbiAgZmluZEFjdGl2ZVZ1bG5lcmFiaWxpdGllcyhsaW1pdD86IG51bWJlcik6IFByb21pc2U8VnVsbmVyYWJpbGl0eVtdPjtcclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCByZW1lZGlhdGVkIHZ1bG5lcmFiaWxpdGllc1xyXG4gICAqIEhpc3RvcmljYWwgdnVsbmVyYWJpbGl0aWVzIGZvciBhbmFseXNpcyBhbmQgcmVwb3J0aW5nXHJcbiAgICogXHJcbiAgICogQHBhcmFtIGxpbWl0IE9wdGlvbmFsIGxpbWl0IGZvciByZXN1bHRzXHJcbiAgICogQHJldHVybnMgUHJvbWlzZSByZXNvbHZpbmcgdG8gcmVtZWRpYXRlZCB2dWxuZXJhYmlsaXRpZXNcclxuICAgKi9cclxuICBmaW5kUmVtZWRpYXRlZFZ1bG5lcmFiaWxpdGllcyhsaW1pdD86IG51bWJlcik6IFByb21pc2U8VnVsbmVyYWJpbGl0eVtdPjtcclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCB2dWxuZXJhYmlsaXRpZXMgd2l0aCBoaWdoIHJpc2sgc2NvcmVzXHJcbiAgICogUmlzay1iYXNlZCB2dWxuZXJhYmlsaXR5IHByaW9yaXRpemF0aW9uXHJcbiAgICogXHJcbiAgICogQHBhcmFtIG1pblJpc2tTY29yZSBNaW5pbXVtIHJpc2sgc2NvcmUgdGhyZXNob2xkIChkZWZhdWx0OiA3MClcclxuICAgKiBAcGFyYW0gbGltaXQgT3B0aW9uYWwgbGltaXQgZm9yIHJlc3VsdHNcclxuICAgKiBAcmV0dXJucyBQcm9taXNlIHJlc29sdmluZyB0byBoaWdoLXJpc2sgdnVsbmVyYWJpbGl0aWVzXHJcbiAgICovXHJcbiAgZmluZEhpZ2hSaXNrVnVsbmVyYWJpbGl0aWVzKG1pblJpc2tTY29yZT86IG51bWJlciwgbGltaXQ/OiBudW1iZXIpOiBQcm9taXNlPFZ1bG5lcmFiaWxpdHlbXT47XHJcblxyXG4gIC8qKlxyXG4gICAqIEZpbmQgdnVsbmVyYWJpbGl0aWVzIGJ5IENWRSBJRFxyXG4gICAqIENWRS1zcGVjaWZpYyB2dWxuZXJhYmlsaXR5IGxvb2t1cFxyXG4gICAqIFxyXG4gICAqIEBwYXJhbSBjdmVJZCBUaGUgQ1ZFIGlkZW50aWZpZXJcclxuICAgKiBAcmV0dXJucyBQcm9taXNlIHJlc29sdmluZyB0byB2dWxuZXJhYmlsaXR5IHdpdGggdGhlIHNwZWNpZmllZCBDVkUgSURcclxuICAgKi9cclxuICBmaW5kQnlDdmVJZChjdmVJZDogc3RyaW5nKTogUHJvbWlzZTxWdWxuZXJhYmlsaXR5IHwgbnVsbD47XHJcblxyXG4gIC8qKlxyXG4gICAqIEZpbmQgdnVsbmVyYWJpbGl0aWVzIHdpdGggQ1ZFIGlkZW50aWZpZXJzXHJcbiAgICogS25vd24gdnVsbmVyYWJpbGl0aWVzIHdpdGggb2ZmaWNpYWwgQ1ZFIGFzc2lnbm1lbnRzXHJcbiAgICogXHJcbiAgICogQHBhcmFtIGxpbWl0IE9wdGlvbmFsIGxpbWl0IGZvciByZXN1bHRzXHJcbiAgICogQHJldHVybnMgUHJvbWlzZSByZXNvbHZpbmcgdG8gdnVsbmVyYWJpbGl0aWVzIHdpdGggQ1ZFIElEc1xyXG4gICAqL1xyXG4gIGZpbmRXaXRoQ3ZlSWRzKGxpbWl0PzogbnVtYmVyKTogUHJvbWlzZTxWdWxuZXJhYmlsaXR5W10+O1xyXG5cclxuICAvKipcclxuICAgKiBGaW5kIHplcm8tZGF5IHZ1bG5lcmFiaWxpdGllcyAobm8gQ1ZFKVxyXG4gICAqIFVua25vd24gb3IgbmV3bHkgZGlzY292ZXJlZCB2dWxuZXJhYmlsaXRpZXNcclxuICAgKiBcclxuICAgKiBAcGFyYW0gbGltaXQgT3B0aW9uYWwgbGltaXQgZm9yIHJlc3VsdHNcclxuICAgKiBAcmV0dXJucyBQcm9taXNlIHJlc29sdmluZyB0byB6ZXJvLWRheSB2dWxuZXJhYmlsaXRpZXNcclxuICAgKi9cclxuICBmaW5kWmVyb0RheVZ1bG5lcmFiaWxpdGllcyhsaW1pdD86IG51bWJlcik6IFByb21pc2U8VnVsbmVyYWJpbGl0eVtdPjtcclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCB2dWxuZXJhYmlsaXRpZXMgYnkgQ1ZTUyBzY29yZSByYW5nZVxyXG4gICAqIENWU1MtYmFzZWQgdnVsbmVyYWJpbGl0eSBmaWx0ZXJpbmdcclxuICAgKiBcclxuICAgKiBAcGFyYW0gbWluU2NvcmUgTWluaW11bSBDVlNTIHNjb3JlXHJcbiAgICogQHBhcmFtIG1heFNjb3JlIE1heGltdW0gQ1ZTUyBzY29yZVxyXG4gICAqIEBwYXJhbSBsaW1pdCBPcHRpb25hbCBsaW1pdCBmb3IgcmVzdWx0c1xyXG4gICAqIEByZXR1cm5zIFByb21pc2UgcmVzb2x2aW5nIHRvIHZ1bG5lcmFiaWxpdGllcyB3aXRoaW4gdGhlIENWU1Mgc2NvcmUgcmFuZ2VcclxuICAgKi9cclxuICBmaW5kQnlDdnNzU2NvcmVSYW5nZShtaW5TY29yZT86IG51bWJlciwgbWF4U2NvcmU/OiBudW1iZXIsIGxpbWl0PzogbnVtYmVyKTogUHJvbWlzZTxWdWxuZXJhYmlsaXR5W10+O1xyXG5cclxuICAvKipcclxuICAgKiBGaW5kIHZ1bG5lcmFiaWxpdGllcyB3aXRoIGhpZ2ggQ1ZTUyBzY29yZXNcclxuICAgKiBDVlNTLWJhc2VkIGhpZ2gtcHJpb3JpdHkgdnVsbmVyYWJpbGl0eSBpZGVudGlmaWNhdGlvblxyXG4gICAqIFxyXG4gICAqIEBwYXJhbSBtaW5TY29yZSBNaW5pbXVtIENWU1Mgc2NvcmUgKGRlZmF1bHQ6IDcuMClcclxuICAgKiBAcGFyYW0gbGltaXQgT3B0aW9uYWwgbGltaXQgZm9yIHJlc3VsdHNcclxuICAgKiBAcmV0dXJucyBQcm9taXNlIHJlc29sdmluZyB0byBoaWdoIENWU1Mgc2NvcmUgdnVsbmVyYWJpbGl0aWVzXHJcbiAgICovXHJcbiAgZmluZEhpZ2hDdnNzVnVsbmVyYWJpbGl0aWVzKG1pblNjb3JlPzogbnVtYmVyLCBsaW1pdD86IG51bWJlcik6IFByb21pc2U8VnVsbmVyYWJpbGl0eVtdPjtcclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCB2dWxuZXJhYmlsaXRpZXMgYnkgY2F0ZWdvcnlcclxuICAgKiBDYXRlZ29yaWNhbCB2dWxuZXJhYmlsaXR5IGFuYWx5c2lzXHJcbiAgICogXHJcbiAgICogQHBhcmFtIGNhdGVnb3JpZXMgQXJyYXkgb2YgdnVsbmVyYWJpbGl0eSBjYXRlZ29yaWVzXHJcbiAgICogQHBhcmFtIGxpbWl0IE9wdGlvbmFsIGxpbWl0IGZvciByZXN1bHRzXHJcbiAgICogQHJldHVybnMgUHJvbWlzZSByZXNvbHZpbmcgdG8gdnVsbmVyYWJpbGl0aWVzIGluIHRoZSBzcGVjaWZpZWQgY2F0ZWdvcmllc1xyXG4gICAqL1xyXG4gIGZpbmRCeUNhdGVnb3JpZXMoY2F0ZWdvcmllczogc3RyaW5nW10sIGxpbWl0PzogbnVtYmVyKTogUHJvbWlzZTxWdWxuZXJhYmlsaXR5W10+O1xyXG5cclxuICAvKipcclxuICAgKiBGaW5kIHZ1bG5lcmFiaWxpdGllcyBieSB0eXBlXHJcbiAgICogVHlwZS1zcGVjaWZpYyB2dWxuZXJhYmlsaXR5IGFuYWx5c2lzXHJcbiAgICogXHJcbiAgICogQHBhcmFtIHR5cGVzIEFycmF5IG9mIHZ1bG5lcmFiaWxpdHkgdHlwZXNcclxuICAgKiBAcGFyYW0gbGltaXQgT3B0aW9uYWwgbGltaXQgZm9yIHJlc3VsdHNcclxuICAgKiBAcmV0dXJucyBQcm9taXNlIHJlc29sdmluZyB0byB2dWxuZXJhYmlsaXRpZXMgb2YgdGhlIHNwZWNpZmllZCB0eXBlc1xyXG4gICAqL1xyXG4gIGZpbmRCeVR5cGVzKHR5cGVzOiBzdHJpbmdbXSwgbGltaXQ/OiBudW1iZXIpOiBQcm9taXNlPFZ1bG5lcmFiaWxpdHlbXT47XHJcblxyXG4gIC8qKlxyXG4gICAqIEZpbmQgdnVsbmVyYWJpbGl0aWVzIGJ5IHRhZ3NcclxuICAgKiBUYWctYmFzZWQgdnVsbmVyYWJpbGl0eSBmaWx0ZXJpbmcgYW5kIGNhdGVnb3JpemF0aW9uXHJcbiAgICogXHJcbiAgICogQHBhcmFtIHRhZ3MgQXJyYXkgb2YgdGFncyB0byBzZWFyY2ggZm9yXHJcbiAgICogQHBhcmFtIG1hdGNoQWxsIFdoZXRoZXIgdG8gbWF0Y2ggYWxsIHRhZ3MgKEFORCkgb3IgYW55IHRhZyAoT1IpXHJcbiAgICogQHBhcmFtIGxpbWl0IE9wdGlvbmFsIGxpbWl0IGZvciByZXN1bHRzXHJcbiAgICogQHJldHVybnMgUHJvbWlzZSByZXNvbHZpbmcgdG8gdnVsbmVyYWJpbGl0aWVzIHdpdGggdGhlIHNwZWNpZmllZCB0YWdzXHJcbiAgICovXHJcbiAgZmluZEJ5VGFncyh0YWdzOiBzdHJpbmdbXSwgbWF0Y2hBbGw/OiBib29sZWFuLCBsaW1pdD86IG51bWJlcik6IFByb21pc2U8VnVsbmVyYWJpbGl0eVtdPjtcclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCB2dWxuZXJhYmlsaXRpZXMgYWZmZWN0aW5nIHNwZWNpZmljIGFzc2V0c1xyXG4gICAqIEFzc2V0LWJhc2VkIHZ1bG5lcmFiaWxpdHkgYW5hbHlzaXNcclxuICAgKiBcclxuICAgKiBAcGFyYW0gYXNzZXRJZHMgQXJyYXkgb2YgYXNzZXQgSURzXHJcbiAgICogQHBhcmFtIGxpbWl0IE9wdGlvbmFsIGxpbWl0IGZvciByZXN1bHRzXHJcbiAgICogQHJldHVybnMgUHJvbWlzZSByZXNvbHZpbmcgdG8gdnVsbmVyYWJpbGl0aWVzIGFmZmVjdGluZyB0aGUgc3BlY2lmaWVkIGFzc2V0c1xyXG4gICAqL1xyXG4gIGZpbmRCeUFmZmVjdGVkQXNzZXRzKGFzc2V0SWRzOiBzdHJpbmdbXSwgbGltaXQ/OiBudW1iZXIpOiBQcm9taXNlPFZ1bG5lcmFiaWxpdHlbXT47XHJcblxyXG4gIC8qKlxyXG4gICAqIEZpbmQgdnVsbmVyYWJpbGl0aWVzIGFmZmVjdGluZyBjcml0aWNhbCBhc3NldHNcclxuICAgKiBDcml0aWNhbCBhc3NldCB2dWxuZXJhYmlsaXR5IHByaW9yaXRpemF0aW9uXHJcbiAgICogXHJcbiAgICogQHBhcmFtIGxpbWl0IE9wdGlvbmFsIGxpbWl0IGZvciByZXN1bHRzXHJcbiAgICogQHJldHVybnMgUHJvbWlzZSByZXNvbHZpbmcgdG8gdnVsbmVyYWJpbGl0aWVzIGFmZmVjdGluZyBjcml0aWNhbCBhc3NldHNcclxuICAgKi9cclxuICBmaW5kQWZmZWN0aW5nQ3JpdGljYWxBc3NldHMobGltaXQ/OiBudW1iZXIpOiBQcm9taXNlPFZ1bG5lcmFiaWxpdHlbXT47XHJcblxyXG4gIC8qKlxyXG4gICAqIEZpbmQgdnVsbmVyYWJpbGl0aWVzIGFmZmVjdGluZyBleHRlcm5hbGx5IGV4cG9zZWQgYXNzZXRzXHJcbiAgICogRXh0ZXJuYWwgZXhwb3N1cmUgdnVsbmVyYWJpbGl0eSBwcmlvcml0aXphdGlvblxyXG4gICAqIFxyXG4gICAqIEBwYXJhbSBsaW1pdCBPcHRpb25hbCBsaW1pdCBmb3IgcmVzdWx0c1xyXG4gICAqIEByZXR1cm5zIFByb21pc2UgcmVzb2x2aW5nIHRvIHZ1bG5lcmFiaWxpdGllcyBhZmZlY3RpbmcgZXh0ZXJuYWxseSBleHBvc2VkIGFzc2V0c1xyXG4gICAqL1xyXG4gIGZpbmRBZmZlY3RpbmdFeHRlcm5hbEFzc2V0cyhsaW1pdD86IG51bWJlcik6IFByb21pc2U8VnVsbmVyYWJpbGl0eVtdPjtcclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCBhY3RpdmVseSBleHBsb2l0ZWQgdnVsbmVyYWJpbGl0aWVzXHJcbiAgICogSW1tZWRpYXRlIHRocmVhdCB2dWxuZXJhYmlsaXR5IGlkZW50aWZpY2F0aW9uXHJcbiAgICogXHJcbiAgICogQHBhcmFtIGxpbWl0IE9wdGlvbmFsIGxpbWl0IGZvciByZXN1bHRzXHJcbiAgICogQHJldHVybnMgUHJvbWlzZSByZXNvbHZpbmcgdG8gYWN0aXZlbHkgZXhwbG9pdGVkIHZ1bG5lcmFiaWxpdGllc1xyXG4gICAqL1xyXG4gIGZpbmRBY3RpdmVseUV4cGxvaXRlZChsaW1pdD86IG51bWJlcik6IFByb21pc2U8VnVsbmVyYWJpbGl0eVtdPjtcclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCByZWNlbnQgdnVsbmVyYWJpbGl0aWVzIHdpdGhpbiBhIHRpbWUgd2luZG93XHJcbiAgICogUmVjZW50bHkgZGlzY292ZXJlZCB2dWxuZXJhYmlsaXR5IG1vbml0b3JpbmdcclxuICAgKiBcclxuICAgKiBAcGFyYW0gd2l0aGluRGF5cyBUaW1lIHdpbmRvdyBpbiBkYXlzIChkZWZhdWx0OiA3KVxyXG4gICAqIEBwYXJhbSBsaW1pdCBPcHRpb25hbCBsaW1pdCBmb3IgcmVzdWx0c1xyXG4gICAqIEByZXR1cm5zIFByb21pc2UgcmVzb2x2aW5nIHRvIHJlY2VudCB2dWxuZXJhYmlsaXRpZXNcclxuICAgKi9cclxuICBmaW5kUmVjZW50VnVsbmVyYWJpbGl0aWVzKHdpdGhpbkRheXM/OiBudW1iZXIsIGxpbWl0PzogbnVtYmVyKTogUHJvbWlzZTxWdWxuZXJhYmlsaXR5W10+O1xyXG5cclxuICAvKipcclxuICAgKiBGaW5kIHN0YWxlIHZ1bG5lcmFiaWxpdGllcyB0aGF0IGhhdmVuJ3QgYmVlbiB1cGRhdGVkXHJcbiAgICogSWRlbnRpZmllcyB2dWxuZXJhYmlsaXRpZXMgdGhhdCBtYXkgbmVlZCBhdHRlbnRpb25cclxuICAgKiBcclxuICAgKiBAcGFyYW0gb2xkZXJUaGFuRGF5cyBBZ2UgdGhyZXNob2xkIGluIGRheXMgKGRlZmF1bHQ6IDkwKVxyXG4gICAqIEBwYXJhbSBsaW1pdCBPcHRpb25hbCBsaW1pdCBmb3IgcmVzdWx0c1xyXG4gICAqIEByZXR1cm5zIFByb21pc2UgcmVzb2x2aW5nIHRvIHN0YWxlIHZ1bG5lcmFiaWxpdGllc1xyXG4gICAqL1xyXG4gIGZpbmRTdGFsZVZ1bG5lcmFiaWxpdGllcyhvbGRlclRoYW5EYXlzPzogbnVtYmVyLCBsaW1pdD86IG51bWJlcik6IFByb21pc2U8VnVsbmVyYWJpbGl0eVtdPjtcclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCB2dWxuZXJhYmlsaXRpZXMgYnkgYWdlIHJhbmdlXHJcbiAgICogQWdlLWJhc2VkIHZ1bG5lcmFiaWxpdHkgYW5hbHlzaXNcclxuICAgKiBcclxuICAgKiBAcGFyYW0gbWluQWdlRGF5cyBNaW5pbXVtIGFnZSBpbiBkYXlzXHJcbiAgICogQHBhcmFtIG1heEFnZURheXMgTWF4aW11bSBhZ2UgaW4gZGF5c1xyXG4gICAqIEBwYXJhbSBsaW1pdCBPcHRpb25hbCBsaW1pdCBmb3IgcmVzdWx0c1xyXG4gICAqIEByZXR1cm5zIFByb21pc2UgcmVzb2x2aW5nIHRvIHZ1bG5lcmFiaWxpdGllcyB3aXRoaW4gdGhlIGFnZSByYW5nZVxyXG4gICAqL1xyXG4gIGZpbmRCeUFnZVJhbmdlKG1pbkFnZURheXM/OiBudW1iZXIsIG1heEFnZURheXM/OiBudW1iZXIsIGxpbWl0PzogbnVtYmVyKTogUHJvbWlzZTxWdWxuZXJhYmlsaXR5W10+O1xyXG5cclxuICAvKipcclxuICAgKiBDb3VudCB2dWxuZXJhYmlsaXRpZXMgYnkgc2V2ZXJpdHlcclxuICAgKiBTZXZlcml0eSBkaXN0cmlidXRpb24gYW5hbHlzaXNcclxuICAgKiBcclxuICAgKiBAcmV0dXJucyBQcm9taXNlIHJlc29sdmluZyB0byBzZXZlcml0eSBjb3VudHNcclxuICAgKi9cclxuICBjb3VudEJ5U2V2ZXJpdHkoKTogUHJvbWlzZTxSZWNvcmQ8VnVsbmVyYWJpbGl0eVNldmVyaXR5LCBudW1iZXI+PjtcclxuXHJcbiAgLyoqXHJcbiAgICogQ291bnQgdnVsbmVyYWJpbGl0aWVzIGJ5IHN0YXR1c1xyXG4gICAqIFN0YXR1cyBkaXN0cmlidXRpb24gYW5hbHlzaXNcclxuICAgKiBcclxuICAgKiBAcmV0dXJucyBQcm9taXNlIHJlc29sdmluZyB0byBzdGF0dXMgY291bnRzXHJcbiAgICovXHJcbiAgY291bnRCeVN0YXR1cygpOiBQcm9taXNlPFJlY29yZDxWdWxuZXJhYmlsaXR5U3RhdHVzLCBudW1iZXI+PjtcclxuXHJcbiAgLyoqXHJcbiAgICogQ291bnQgdnVsbmVyYWJpbGl0aWVzIGJ5IGNhdGVnb3J5XHJcbiAgICogQ2F0ZWdvcnkgZGlzdHJpYnV0aW9uIGFuYWx5c2lzXHJcbiAgICogXHJcbiAgICogQHJldHVybnMgUHJvbWlzZSByZXNvbHZpbmcgdG8gY2F0ZWdvcnkgY291bnRzXHJcbiAgICovXHJcbiAgY291bnRCeUNhdGVnb3J5KCk6IFByb21pc2U8UmVjb3JkPHN0cmluZywgbnVtYmVyPj47XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB2dWxuZXJhYmlsaXR5IHN0YXRpc3RpY3MgZm9yIGEgdGltZSBwZXJpb2RcclxuICAgKiBDb21wcmVoZW5zaXZlIHZ1bG5lcmFiaWxpdHkgbWV0cmljcyBhbmQgYW5hbHlzaXNcclxuICAgKiBcclxuICAgKiBAcGFyYW0gc3RhcnRUaW1lIFN0YXJ0IG9mIHRoZSB0aW1lIHBlcmlvZFxyXG4gICAqIEBwYXJhbSBlbmRUaW1lIEVuZCBvZiB0aGUgdGltZSBwZXJpb2RcclxuICAgKiBAcmV0dXJucyBQcm9taXNlIHJlc29sdmluZyB0byB2dWxuZXJhYmlsaXR5IHN0YXRpc3RpY3NcclxuICAgKi9cclxuICBnZXRWdWxuZXJhYmlsaXR5U3RhdGlzdGljcyhzdGFydFRpbWU6IERhdGUsIGVuZFRpbWU6IERhdGUpOiBQcm9taXNlPHtcclxuICAgIHRvdGFsVnVsbmVyYWJpbGl0aWVzOiBudW1iZXI7XHJcbiAgICBhY3RpdmVWdWxuZXJhYmlsaXRpZXM6IG51bWJlcjtcclxuICAgIHJlbWVkaWF0ZWRWdWxuZXJhYmlsaXRpZXM6IG51bWJlcjtcclxuICAgIHZ1bG5lcmFiaWxpdGllc0J5U2V2ZXJpdHk6IFJlY29yZDxWdWxuZXJhYmlsaXR5U2V2ZXJpdHksIG51bWJlcj47XHJcbiAgICB2dWxuZXJhYmlsaXRpZXNCeVN0YXR1czogUmVjb3JkPFZ1bG5lcmFiaWxpdHlTdGF0dXMsIG51bWJlcj47XHJcbiAgICB2dWxuZXJhYmlsaXRpZXNCeUNhdGVnb3J5OiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+O1xyXG4gICAgYXZlcmFnZVJpc2tTY29yZTogbnVtYmVyO1xyXG4gICAgYXZlcmFnZUN2c3NTY29yZTogbnVtYmVyO1xyXG4gICAgaGlnaFJpc2tWdWxuZXJhYmlsaXR5Q291bnQ6IG51bWJlcjtcclxuICAgIGNyaXRpY2FsVnVsbmVyYWJpbGl0eUNvdW50OiBudW1iZXI7XHJcbiAgICB6ZXJvRGF5Q291bnQ6IG51bWJlcjtcclxuICAgIGFjdGl2ZWx5RXhwbG9pdGVkQ291bnQ6IG51bWJlcjtcclxuICAgIGF2ZXJhZ2VSZW1lZGlhdGlvblRpbWU6IG51bWJlcjtcclxuICAgIHJlbWVkaWF0aW9uTWV0cmljczoge1xyXG4gICAgICB0b3RhbFJlbWVkaWF0ZWQ6IG51bWJlcjtcclxuICAgICAgYXZlcmFnZVRpbWVUb1JlbWVkaWF0aW9uOiBudW1iZXI7XHJcbiAgICAgIHJlbWVkaWF0aW9uQnlQcmlvcml0eTogUmVjb3JkPG51bWJlciwgbnVtYmVyPjtcclxuICAgIH07XHJcbiAgICBhc3NldEltcGFjdDoge1xyXG4gICAgICBjcml0aWNhbEFzc2V0c0FmZmVjdGVkOiBudW1iZXI7XHJcbiAgICAgIGV4dGVybmFsQXNzZXRzQWZmZWN0ZWQ6IG51bWJlcjtcclxuICAgICAgdG90YWxBc3NldHNBZmZlY3RlZDogbnVtYmVyO1xyXG4gICAgfTtcclxuICB9PjtcclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCB2dWxuZXJhYmlsaXRpZXMgZm9yIHByaW9yaXRpemF0aW9uXHJcbiAgICogUmlzay1iYXNlZCB2dWxuZXJhYmlsaXR5IHByaW9yaXRpemF0aW9uIGFsZ29yaXRobVxyXG4gICAqIFxyXG4gICAqIEBwYXJhbSBjcml0ZXJpYSBQcmlvcml0aXphdGlvbiBjcml0ZXJpYVxyXG4gICAqIEBwYXJhbSBsaW1pdCBPcHRpb25hbCBsaW1pdCBmb3IgcmVzdWx0c1xyXG4gICAqIEByZXR1cm5zIFByb21pc2UgcmVzb2x2aW5nIHRvIHByaW9yaXRpemVkIHZ1bG5lcmFiaWxpdGllc1xyXG4gICAqL1xyXG4gIGZpbmRGb3JQcmlvcml0aXphdGlvbihjcml0ZXJpYT86IHtcclxuICAgIG1pblNldmVyaXR5PzogVnVsbmVyYWJpbGl0eVNldmVyaXR5O1xyXG4gICAgbWluUmlza1Njb3JlPzogbnVtYmVyO1xyXG4gICAgbWluQ3Zzc1Njb3JlPzogbnVtYmVyO1xyXG4gICAgaW5jbHVkZUFjdGl2ZWx5RXhwbG9pdGVkPzogYm9vbGVhbjtcclxuICAgIGluY2x1ZGVDcml0aWNhbEFzc2V0cz86IGJvb2xlYW47XHJcbiAgICBpbmNsdWRlRXh0ZXJuYWxBc3NldHM/OiBib29sZWFuO1xyXG4gICAgbWF4QWdlPzogbnVtYmVyO1xyXG4gIH0sIGxpbWl0PzogbnVtYmVyKTogUHJvbWlzZTxWdWxuZXJhYmlsaXR5W10+O1xyXG5cclxuICAvKipcclxuICAgKiBGaW5kIHZ1bG5lcmFiaWxpdGllcyBieSByZW1lZGlhdGlvbiBlZmZvcnRcclxuICAgKiBFZmZvcnQtYmFzZWQgcmVtZWRpYXRpb24gcGxhbm5pbmdcclxuICAgKiBcclxuICAgKiBAcGFyYW0gZWZmb3J0IFJlbWVkaWF0aW9uIGVmZm9ydCBsZXZlbFxyXG4gICAqIEBwYXJhbSBsaW1pdCBPcHRpb25hbCBsaW1pdCBmb3IgcmVzdWx0c1xyXG4gICAqIEByZXR1cm5zIFByb21pc2UgcmVzb2x2aW5nIHRvIHZ1bG5lcmFiaWxpdGllcyB3aXRoIHRoZSBzcGVjaWZpZWQgcmVtZWRpYXRpb24gZWZmb3J0XHJcbiAgICovXHJcbiAgZmluZEJ5UmVtZWRpYXRpb25FZmZvcnQoZWZmb3J0OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnLCBsaW1pdD86IG51bWJlcik6IFByb21pc2U8VnVsbmVyYWJpbGl0eVtdPjtcclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCB2dWxuZXJhYmlsaXRpZXMgYnkgcmVtZWRpYXRpb24gcHJpb3JpdHlcclxuICAgKiBQcmlvcml0eS1iYXNlZCByZW1lZGlhdGlvbiBwbGFubmluZ1xyXG4gICAqIFxyXG4gICAqIEBwYXJhbSBtaW5Qcmlvcml0eSBNaW5pbXVtIHJlbWVkaWF0aW9uIHByaW9yaXR5XHJcbiAgICogQHBhcmFtIGxpbWl0IE9wdGlvbmFsIGxpbWl0IGZvciByZXN1bHRzXHJcbiAgICogQHJldHVybnMgUHJvbWlzZSByZXNvbHZpbmcgdG8gdnVsbmVyYWJpbGl0aWVzIHdpdGggdGhlIHNwZWNpZmllZCBwcmlvcml0eSBvciBoaWdoZXJcclxuICAgKi9cclxuICBmaW5kQnlSZW1lZGlhdGlvblByaW9yaXR5KG1pblByaW9yaXR5OiBudW1iZXIsIGxpbWl0PzogbnVtYmVyKTogUHJvbWlzZTxWdWxuZXJhYmlsaXR5W10+O1xyXG5cclxuICAvKipcclxuICAgKiBGaW5kIG92ZXJkdWUgdnVsbmVyYWJpbGl0aWVzXHJcbiAgICogSWRlbnRpZmllcyB2dWxuZXJhYmlsaXRpZXMgcGFzdCB0aGVpciByZW1lZGlhdGlvbiB0aW1lbGluZVxyXG4gICAqIFxyXG4gICAqIEBwYXJhbSBsaW1pdCBPcHRpb25hbCBsaW1pdCBmb3IgcmVzdWx0c1xyXG4gICAqIEByZXR1cm5zIFByb21pc2UgcmVzb2x2aW5nIHRvIG92ZXJkdWUgdnVsbmVyYWJpbGl0aWVzXHJcbiAgICovXHJcbiAgZmluZE92ZXJkdWVWdWxuZXJhYmlsaXRpZXMobGltaXQ/OiBudW1iZXIpOiBQcm9taXNlPFZ1bG5lcmFiaWxpdHlbXT47XHJcblxyXG4gIC8qKlxyXG4gICAqIEFyY2hpdmUgb2xkIHJlbWVkaWF0ZWQgdnVsbmVyYWJpbGl0aWVzXHJcbiAgICogTW92ZXMgb2xkIHJlbWVkaWF0ZWQgdnVsbmVyYWJpbGl0aWVzIHRvIGFyY2hpdmUgc3RvcmFnZVxyXG4gICAqIFxyXG4gICAqIEBwYXJhbSBvbGRlclRoYW5EYXlzIEFnZSB0aHJlc2hvbGQgaW4gZGF5c1xyXG4gICAqIEBwYXJhbSBiYXRjaFNpemUgTnVtYmVyIG9mIHZ1bG5lcmFiaWxpdGllcyB0byBhcmNoaXZlIHBlciBiYXRjaFxyXG4gICAqIEByZXR1cm5zIFByb21pc2UgcmVzb2x2aW5nIHRvIHRoZSBudW1iZXIgb2YgYXJjaGl2ZWQgdnVsbmVyYWJpbGl0aWVzXHJcbiAgICovXHJcbiAgYXJjaGl2ZU9sZFZ1bG5lcmFiaWxpdGllcyhvbGRlclRoYW5EYXlzOiBudW1iZXIsIGJhdGNoU2l6ZT86IG51bWJlcik6IFByb21pc2U8bnVtYmVyPjtcclxuXHJcbiAgLyoqXHJcbiAgICogQnVsayB1cGRhdGUgdnVsbmVyYWJpbGl0eSBzdGF0dXNcclxuICAgKiBFZmZpY2llbnQgYmF0Y2ggb3BlcmF0aW9uIGZvciB2dWxuZXJhYmlsaXR5IG1hbmFnZW1lbnRcclxuICAgKiBcclxuICAgKiBAcGFyYW0gdnVsbmVyYWJpbGl0eUlkcyBBcnJheSBvZiB2dWxuZXJhYmlsaXR5IElEcyB0byB1cGRhdGVcclxuICAgKiBAcGFyYW0gbmV3U3RhdHVzIE5ldyB2dWxuZXJhYmlsaXR5IHN0YXR1c1xyXG4gICAqIEBwYXJhbSB1cGRhdGVkQnkgV2hvIGlzIHBlcmZvcm1pbmcgdGhlIHVwZGF0ZVxyXG4gICAqIEByZXR1cm5zIFByb21pc2UgcmVzb2x2aW5nIHRvIHRoZSBudW1iZXIgb2YgdXBkYXRlZCB2dWxuZXJhYmlsaXRpZXNcclxuICAgKi9cclxuICBidWxrVXBkYXRlU3RhdHVzKFxyXG4gICAgdnVsbmVyYWJpbGl0eUlkczogVW5pcXVlRW50aXR5SWRbXSxcclxuICAgIG5ld1N0YXR1czogVnVsbmVyYWJpbGl0eVN0YXR1cyxcclxuICAgIHVwZGF0ZWRCeT86IHN0cmluZ1xyXG4gICk6IFByb21pc2U8bnVtYmVyPjtcclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCB2dWxuZXJhYmlsaXR5IHRyZW5kc1xyXG4gICAqIEFuYWx5emVzIHZ1bG5lcmFiaWxpdHkgcGF0dGVybnMgb3ZlciB0aW1lXHJcbiAgICogXHJcbiAgICogQHBhcmFtIHRpbWVXaW5kb3cgVGltZSB3aW5kb3cgZm9yIHRyZW5kIGFuYWx5c2lzIGluIGRheXNcclxuICAgKiBAcmV0dXJucyBQcm9taXNlIHJlc29sdmluZyB0byB2dWxuZXJhYmlsaXR5IHRyZW5kIGRhdGFcclxuICAgKi9cclxuICBmaW5kVnVsbmVyYWJpbGl0eVRyZW5kcyh0aW1lV2luZG93PzogbnVtYmVyKTogUHJvbWlzZTxBcnJheTx7XHJcbiAgICBkYXRlOiBEYXRlO1xyXG4gICAgdnVsbmVyYWJpbGl0eUNvdW50OiBudW1iZXI7XHJcbiAgICBzZXZlcml0eURpc3RyaWJ1dGlvbjogUmVjb3JkPFZ1bG5lcmFiaWxpdHlTZXZlcml0eSwgbnVtYmVyPjtcclxuICAgIHRvcENhdGVnb3JpZXM6IEFycmF5PHsgY2F0ZWdvcnk6IHN0cmluZzsgY291bnQ6IG51bWJlciB9PjtcclxuICAgIHJlbWVkaWF0aW9uUmF0ZTogbnVtYmVyO1xyXG4gICAgYXZlcmFnZVJpc2tTY29yZTogbnVtYmVyO1xyXG4gIH0+PjtcclxufSJdLCJ2ZXJzaW9uIjozfQ==