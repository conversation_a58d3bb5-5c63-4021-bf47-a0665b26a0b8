9aacdb8b7cf4bf0e260ff946cbfdadce
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const common_1 = require("@nestjs/common");
const ioc_service_1 = require("./ioc.service");
const ioc_entity_1 = require("../../domain/entities/ioc.entity");
const threat_feed_entity_1 = require("../../domain/entities/threat-feed.entity");
const threat_actor_entity_1 = require("../../domain/entities/threat-actor.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
describe('IOCService', () => {
    let service;
    let iocRepository;
    let threatFeedRepository;
    let threatActorRepository;
    let loggerService;
    let auditService;
    const mockIOCRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findOne: jest.fn(),
        find: jest.fn(),
        remove: jest.fn(),
        count: jest.fn(),
        createQueryBuilder: jest.fn(),
    };
    const mockThreatFeedRepository = {
        findOne: jest.fn(),
    };
    const mockThreatActorRepository = {
        findBy: jest.fn(),
    };
    const mockLoggerService = {
        debug: jest.fn(),
        log: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
    };
    const mockAuditService = {
        logUserAction: jest.fn(),
    };
    const mockIOC = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        type: 'ip_address',
        value: '*************',
        description: 'Malicious IP address',
        confidence: 85,
        severity: 'high',
        status: 'active',
        tlp: 'amber',
        firstSeen: new Date('2023-12-01T10:00:00.000Z'),
        lastSeen: new Date('2023-12-01T15:30:00.000Z'),
        observationCount: 1,
        isMonitored: true,
        isValidated: false,
        isExpired: false,
        isActive: true,
        ageInDays: 15,
        riskScore: 8.5,
        isHash: false,
        isNetworkIndicator: true,
        normalizedValue: '*************',
        createdAt: new Date(),
        updatedAt: new Date(),
        updateLastSeen: jest.fn(),
        markAsValidated: jest.fn(),
        markAsFalsePositive: jest.fn(),
        addEnrichmentData: jest.fn(),
    };
    const mockThreatActor = {
        id: 'actor-123',
        name: 'APT28',
        type: 'apt',
        sophistication: 'advanced',
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                ioc_service_1.IOCService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(ioc_entity_1.IOC),
                    useValue: mockIOCRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(threat_feed_entity_1.ThreatFeed),
                    useValue: mockThreatFeedRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(threat_actor_entity_1.ThreatActor),
                    useValue: mockThreatActorRepository,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: mockLoggerService,
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: mockAuditService,
                },
            ],
        }).compile();
        service = module.get(ioc_service_1.IOCService);
        iocRepository = module.get((0, typeorm_1.getRepositoryToken)(ioc_entity_1.IOC));
        threatFeedRepository = module.get((0, typeorm_1.getRepositoryToken)(threat_feed_entity_1.ThreatFeed));
        threatActorRepository = module.get((0, typeorm_1.getRepositoryToken)(threat_actor_entity_1.ThreatActor));
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('create', () => {
        it('should create an IOC successfully', async () => {
            const createData = {
                type: 'ip_address',
                value: '*************',
                description: 'Malicious IP address',
                confidence: 85,
                severity: 'high',
            };
            const createdBy = 'analyst-123';
            jest.spyOn(service, 'findByTypeAndValue').mockResolvedValue(null);
            mockIOCRepository.create.mockReturnValue(mockIOC);
            mockIOCRepository.save.mockResolvedValue(mockIOC);
            const result = await service.create(createData, createdBy);
            expect(service.findByTypeAndValue).toHaveBeenCalledWith(createData.type, createData.value);
            expect(mockIOCRepository.create).toHaveBeenCalled();
            expect(mockIOCRepository.save).toHaveBeenCalledWith(mockIOC);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(createdBy, 'create', 'ioc', mockIOC.id, expect.any(Object));
            expect(result).toEqual(mockIOC);
        });
        it('should update existing IOC when duplicate is found', async () => {
            const createData = {
                type: 'ip_address',
                value: '*************',
                description: 'Malicious IP address',
            };
            const createdBy = 'analyst-123';
            const existingIOC = { ...mockIOC, observationCount: 2 };
            jest.spyOn(service, 'findByTypeAndValue').mockResolvedValue(existingIOC);
            mockIOCRepository.save.mockResolvedValue(existingIOC);
            const result = await service.create(createData, createdBy);
            expect(service.findByTypeAndValue).toHaveBeenCalledWith(createData.type, createData.value);
            expect(existingIOC.updateLastSeen).toHaveBeenCalled();
            expect(mockIOCRepository.save).toHaveBeenCalledWith(existingIOC);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(createdBy, 'update_existing', 'ioc', existingIOC.id, expect.any(Object));
            expect(result).toEqual(existingIOC);
        });
        it('should throw BadRequestException when required fields are missing', async () => {
            const createData = {
                type: 'ip_address',
                // Missing value
            };
            const createdBy = 'analyst-123';
            await expect(service.create(createData, createdBy)).rejects.toThrow(common_1.BadRequestException);
        });
    });
    describe('findById', () => {
        it('should return IOC when found', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            mockIOCRepository.findOne.mockResolvedValue(mockIOC);
            const result = await service.findById(id);
            expect(mockIOCRepository.findOne).toHaveBeenCalledWith({
                where: { id },
                relations: ['threatFeed', 'threatActors'],
            });
            expect(result).toEqual(mockIOC);
        });
        it('should return null when IOC not found', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            mockIOCRepository.findOne.mockResolvedValue(null);
            const result = await service.findById(id);
            expect(result).toBeNull();
            expect(mockLoggerService.warn).toHaveBeenCalledWith('IOC not found', { id });
        });
    });
    describe('findByTypeAndValue', () => {
        it('should return IOC when found by type and value', async () => {
            const type = 'ip_address';
            const value = '*************';
            mockIOCRepository.findOne.mockResolvedValue(mockIOC);
            const result = await service.findByTypeAndValue(type, value);
            expect(mockIOCRepository.findOne).toHaveBeenCalledWith({
                where: { type, value },
                relations: ['threatFeed', 'threatActors'],
            });
            expect(result).toEqual(mockIOC);
        });
    });
    describe('update', () => {
        it('should update IOC successfully', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            const updateData = { confidence: 90, severity: 'critical' };
            const updatedBy = 'analyst-123';
            jest.spyOn(service, 'findById').mockResolvedValue(mockIOC);
            const updatedIOC = { ...mockIOC, ...updateData };
            mockIOCRepository.save.mockResolvedValue(updatedIOC);
            const result = await service.update(id, updateData, updatedBy);
            expect(service.findById).toHaveBeenCalledWith(id);
            expect(mockIOCRepository.save).toHaveBeenCalled();
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(updatedBy, 'update', 'ioc', id, expect.any(Object));
            expect(result).toEqual(updatedIOC);
        });
        it('should throw NotFoundException when IOC not found', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            const updateData = { confidence: 90 };
            const updatedBy = 'analyst-123';
            jest.spyOn(service, 'findById').mockResolvedValue(null);
            await expect(service.update(id, updateData, updatedBy)).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe('delete', () => {
        it('should delete IOC successfully', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            const deletedBy = 'admin-123';
            jest.spyOn(service, 'findById').mockResolvedValue(mockIOC);
            mockIOCRepository.remove.mockResolvedValue(mockIOC);
            await service.delete(id, deletedBy);
            expect(service.findById).toHaveBeenCalledWith(id);
            expect(mockIOCRepository.remove).toHaveBeenCalledWith(mockIOC);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(deletedBy, 'delete', 'ioc', id, expect.any(Object));
        });
        it('should throw NotFoundException when IOC not found', async () => {
            const id = '123e4567-e89b-12d3-a456-426614174000';
            const deletedBy = 'admin-123';
            jest.spyOn(service, 'findById').mockResolvedValue(null);
            await expect(service.delete(id, deletedBy)).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe('bulkCreate', () => {
        it('should bulk create IOCs successfully', async () => {
            const iocsData = [
                { type: 'ip_address', value: '*************' },
                { type: 'domain', value: 'malicious.com' },
            ];
            const createdBy = 'analyst-123';
            jest.spyOn(service, 'create')
                .mockResolvedValueOnce({ ...mockIOC, observationCount: 1 })
                .mockResolvedValueOnce({ ...mockIOC, id: 'ioc-2', observationCount: 1 });
            const result = await service.bulkCreate(iocsData, createdBy);
            expect(service.create).toHaveBeenCalledTimes(2);
            expect(result.created).toHaveLength(2);
            expect(result.updated).toHaveLength(0);
            expect(result.errors).toHaveLength(0);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(createdBy, 'bulk_create', 'ioc', null, expect.any(Object));
        });
        it('should handle errors during bulk creation', async () => {
            const iocsData = [
                { type: 'ip_address', value: '*************' },
                { type: 'invalid', value: 'invalid-value' },
            ];
            const createdBy = 'analyst-123';
            jest.spyOn(service, 'create')
                .mockResolvedValueOnce({ ...mockIOC, observationCount: 1 })
                .mockRejectedValueOnce(new common_1.BadRequestException('Invalid IOC type'));
            const result = await service.bulkCreate(iocsData, createdBy);
            expect(result.created).toHaveLength(1);
            expect(result.updated).toHaveLength(0);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].error).toBe('Invalid IOC type');
        });
    });
    describe('associateWithThreatActors', () => {
        it('should associate IOC with threat actors successfully', async () => {
            const iocId = '123e4567-e89b-12d3-a456-426614174000';
            const threatActorIds = ['actor-123'];
            const updatedBy = 'analyst-123';
            jest.spyOn(service, 'findById').mockResolvedValue(mockIOC);
            mockThreatActorRepository.findBy.mockResolvedValue([mockThreatActor]);
            const updatedIOC = { ...mockIOC, threatActors: [mockThreatActor] };
            mockIOCRepository.save.mockResolvedValue(updatedIOC);
            const result = await service.associateWithThreatActors(iocId, threatActorIds, updatedBy);
            expect(service.findById).toHaveBeenCalledWith(iocId);
            expect(mockThreatActorRepository.findBy).toHaveBeenCalledWith({
                id: expect.any(Object),
            });
            expect(mockIOCRepository.save).toHaveBeenCalled();
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(updatedBy, 'associate_threat_actors', 'ioc', iocId, expect.any(Object));
            expect(result).toEqual(updatedIOC);
        });
        it('should throw NotFoundException when IOC not found', async () => {
            const iocId = '123e4567-e89b-12d3-a456-426614174000';
            const threatActorIds = ['actor-123'];
            const updatedBy = 'analyst-123';
            jest.spyOn(service, 'findById').mockResolvedValue(null);
            await expect(service.associateWithThreatActors(iocId, threatActorIds, updatedBy)).rejects.toThrow(common_1.NotFoundException);
        });
        it('should throw BadRequestException when threat actor not found', async () => {
            const iocId = '123e4567-e89b-12d3-a456-426614174000';
            const threatActorIds = ['actor-123', 'actor-456'];
            const updatedBy = 'analyst-123';
            jest.spyOn(service, 'findById').mockResolvedValue(mockIOC);
            mockThreatActorRepository.findBy.mockResolvedValue([mockThreatActor]); // Only one found
            await expect(service.associateWithThreatActors(iocId, threatActorIds, updatedBy)).rejects.toThrow(common_1.BadRequestException);
        });
    });
    describe('getStatistics', () => {
        it('should return IOC statistics', async () => {
            mockIOCRepository.count
                .mockResolvedValueOnce(100) // total
                .mockResolvedValueOnce(80) // active
                .mockResolvedValueOnce(10) // inactive
                .mockResolvedValueOnce(10) // expired
                .mockResolvedValueOnce(5) // critical
                .mockResolvedValueOnce(15) // high
                .mockResolvedValueOnce(50) // medium
                .mockResolvedValueOnce(30) // low
                .mockResolvedValueOnce(25) // ip addresses
                .mockResolvedValueOnce(30) // domains
                .mockResolvedValueOnce(20) // hashes
                .mockResolvedValueOnce(15); // urls
            const result = await service.getStatistics();
            expect(result).toEqual({
                total: 100,
                byStatus: {
                    active: 80,
                    inactive: 10,
                    expired: 10,
                },
                bySeverity: {
                    critical: 5,
                    high: 15,
                    medium: 50,
                    low: 30,
                },
                byType: {
                    ipAddresses: 25,
                    domains: 30,
                    hashes: 20,
                    urls: 15,
                    other: 10,
                },
                timestamp: expect.any(String),
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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