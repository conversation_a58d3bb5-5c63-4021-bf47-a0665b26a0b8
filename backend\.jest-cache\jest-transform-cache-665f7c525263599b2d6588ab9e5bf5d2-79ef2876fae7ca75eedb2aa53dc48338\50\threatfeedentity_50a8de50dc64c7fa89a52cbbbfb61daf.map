{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\threat-feed.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAQiB;AACjB,6CAAmC;AAEnC;;;GAGG;AAMI,IAAM,UAAU,GAAhB,MAAM,UAAU;IAiPrB;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YAAE,OAAO,KAAK,CAAC;QAE3C,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,KAAK,CAAC;QAEjC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,0BAA0B;QACjE,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;QAExE,OAAO,UAAU,IAAI,MAAM,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YAAE,OAAO,KAAK,CAAC;QAE7D,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC;QAEhC,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;QACxE,OAAO,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,CAAC,CAAC;QAE9B,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;QACxF,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE1B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe;YAAE,OAAO,CAAC,CAAC;QAEjE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,eAAe,CACb,OAAgB,EAChB,QAAgB,EAChB,UAAkB,CAAC,EACnB,cAAsB,CAAC,EACvB,cAAsB,CAAC,EACvB,KAAc;QAEd,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG;gBACf,eAAe,EAAE,CAAC;gBAClB,WAAW,EAAE,CAAC;gBACd,eAAe,EAAE,CAAC;gBAClB,mBAAmB,EAAE,CAAC;gBACtB,mBAAmB,EAAE,CAAC;aACvB,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;YAChC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3E,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,OAAO,CAAC;YACzC,IAAI,CAAC,SAAS,CAAC,mBAAmB,GAAG,WAAW,CAAC;YACjD,IAAI,CAAC,SAAS,CAAC,mBAAmB,GAAG,WAAW,CAAC;QACnD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;YAC9B,IAAI,CAAC,aAAa,GAAG,KAAK,IAAI,eAAe,CAAC;YAC9C,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG,QAAQ,CAAC;QAE3C,6BAA6B;QAC7B,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;QAC7F,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YACnB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,IAAI,QAAQ,CAAC;YAClE,IAAI,CAAC,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAC7C,CAAC,UAAU,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,UAAU,CACxD,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,CAAC,mBAAmB,GAAG,QAAQ,CAAC;QAChD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,KAAc;QAC5B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC;QAElD,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;QAEhE,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AA7WY,gCAAU;AAErB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;sCACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;wCACzB;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;+CAC1B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CACpB;AAUrB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,CAAC;QAC1E,OAAO,EAAE,aAAa;KACvB,CAAC;;wCAC2E;AAU7E;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;QAC3D,OAAO,EAAE,MAAM;KAChB,CAAC;;0CACgE;AAMlE;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uCACd;AAUb;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,CAAC;QACpD,OAAO,EAAE,QAAQ;KAClB,CAAC;;0CACsD;AAMxD;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;oDAC3C;AAMzB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;mDAC3C;AAUxB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC;QACxC,OAAO,EAAE,OAAO;KACjB,CAAC;;uCACuC;AAMzC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;iDAC3C;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;4CAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CAQ7D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAQhE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAC1C;AAM7B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCAC1B;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;4CAAC;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;mDAAC;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAC7B;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAC3C;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;6CAC1C;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;8CAC1C;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CAS5D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDAO/D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CAM5D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAKlE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAC/B,MAAM,oBAAN,MAAM;4CAAc;AAM/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CAC1C;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;6CAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;6CAAC;AAIhB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,gBAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC;;wCAChC;qBA/OD,UAAU;IALtB,IAAA,gBAAM,EAAC,cAAc,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;IACjC,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;GACP,UAAU,CA6WtB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\threat-feed.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n} from 'typeorm';\r\nimport { IOC } from './ioc.entity';\r\n\r\n/**\r\n * Threat Feed entity\r\n * Represents external threat intelligence feeds and their configuration\r\n */\r\n@Entity('threat_feeds')\r\n@Index(['name'], { unique: true })\r\n@Index(['type'])\r\n@Index(['status'])\r\n@Index(['lastSync'])\r\nexport class ThreatFeed {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Feed name\r\n   */\r\n  @Column({ unique: true, length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Feed display name\r\n   */\r\n  @Column({ name: 'display_name', length: 255 })\r\n  displayName: string;\r\n\r\n  /**\r\n   * Feed description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Feed type\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['commercial', 'open_source', 'government', 'community', 'internal'],\r\n    default: 'open_source',\r\n  })\r\n  type: 'commercial' | 'open_source' | 'government' | 'community' | 'internal';\r\n\r\n  /**\r\n   * Feed format\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['stix', 'taxii', 'json', 'csv', 'xml', 'txt', 'api'],\r\n    default: 'json',\r\n  })\r\n  format: 'stix' | 'taxii' | 'json' | 'csv' | 'xml' | 'txt' | 'api';\r\n\r\n  /**\r\n   * Feed URL or endpoint\r\n   */\r\n  @Column({ nullable: true })\r\n  url?: string;\r\n\r\n  /**\r\n   * Feed status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['active', 'inactive', 'error', 'maintenance'],\r\n    default: 'active',\r\n  })\r\n  status: 'active' | 'inactive' | 'error' | 'maintenance';\r\n\r\n  /**\r\n   * Feed reliability score (0-100)\r\n   */\r\n  @Column({ name: 'reliability_score', type: 'integer', default: 50 })\r\n  reliabilityScore: number;\r\n\r\n  /**\r\n   * Feed confidence level (0-100)\r\n   */\r\n  @Column({ name: 'confidence_level', type: 'integer', default: 50 })\r\n  confidenceLevel: number;\r\n\r\n  /**\r\n   * TLP (Traffic Light Protocol) classification\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['white', 'green', 'amber', 'red'],\r\n    default: 'white',\r\n  })\r\n  tlp: 'white' | 'green' | 'amber' | 'red';\r\n\r\n  /**\r\n   * Sync frequency in minutes\r\n   */\r\n  @Column({ name: 'sync_frequency', type: 'integer', default: 60 })\r\n  syncFrequency: number;\r\n\r\n  /**\r\n   * Whether auto-sync is enabled\r\n   */\r\n  @Column({ name: 'auto_sync', default: true })\r\n  autoSync: boolean;\r\n\r\n  /**\r\n   * Authentication configuration\r\n   */\r\n  @Column({ name: 'auth_config', type: 'jsonb', nullable: true })\r\n  authConfig?: {\r\n    type: 'none' | 'basic' | 'bearer' | 'api_key' | 'oauth';\r\n    username?: string;\r\n    password?: string;\r\n    token?: string;\r\n    apiKey?: string;\r\n    headers?: Record<string, string>;\r\n  };\r\n\r\n  /**\r\n   * Feed parsing configuration\r\n   */\r\n  @Column({ name: 'parsing_config', type: 'jsonb', nullable: true })\r\n  parsingConfig?: {\r\n    delimiter?: string;\r\n    skipLines?: number;\r\n    columns?: Record<string, string>;\r\n    jsonPath?: string;\r\n    xmlPath?: string;\r\n    filters?: Record<string, any>;\r\n  };\r\n\r\n  /**\r\n   * IOC types supported by this feed\r\n   */\r\n  @Column({ name: 'supported_ioc_types', type: 'jsonb', nullable: true })\r\n  supportedIocTypes?: string[];\r\n\r\n  /**\r\n   * Feed tags\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  tags?: string[];\r\n\r\n  /**\r\n   * Last successful sync timestamp\r\n   */\r\n  @Column({ name: 'last_sync', type: 'timestamp with time zone', nullable: true })\r\n  lastSync?: Date;\r\n\r\n  /**\r\n   * Last sync attempt timestamp\r\n   */\r\n  @Column({ name: 'last_sync_attempt', type: 'timestamp with time zone', nullable: true })\r\n  lastSyncAttempt?: Date;\r\n\r\n  /**\r\n   * Last sync status\r\n   */\r\n  @Column({ name: 'last_sync_status', nullable: true })\r\n  lastSyncStatus?: string;\r\n\r\n  /**\r\n   * Last sync error message\r\n   */\r\n  @Column({ name: 'last_sync_error', type: 'text', nullable: true })\r\n  lastSyncError?: string;\r\n\r\n  /**\r\n   * Total IOCs imported from this feed\r\n   */\r\n  @Column({ name: 'total_iocs', type: 'integer', default: 0 })\r\n  totalIocs: number;\r\n\r\n  /**\r\n   * Active IOCs from this feed\r\n   */\r\n  @Column({ name: 'active_iocs', type: 'integer', default: 0 })\r\n  activeIocs: number;\r\n\r\n  /**\r\n   * Sync statistics\r\n   */\r\n  @Column({ name: 'sync_stats', type: 'jsonb', nullable: true })\r\n  syncStats?: {\r\n    lastSyncDuration?: number;\r\n    averageSyncDuration?: number;\r\n    successfulSyncs?: number;\r\n    failedSyncs?: number;\r\n    newIocsLastSync?: number;\r\n    updatedIocsLastSync?: number;\r\n    expiredIocsLastSync?: number;\r\n  };\r\n\r\n  /**\r\n   * Feed provider information\r\n   */\r\n  @Column({ name: 'provider_info', type: 'jsonb', nullable: true })\r\n  providerInfo?: {\r\n    name?: string;\r\n    website?: string;\r\n    contact?: string;\r\n    license?: string;\r\n    terms?: string;\r\n  };\r\n\r\n  /**\r\n   * Rate limiting configuration\r\n   */\r\n  @Column({ name: 'rate_limit', type: 'jsonb', nullable: true })\r\n  rateLimit?: {\r\n    requestsPerMinute?: number;\r\n    requestsPerHour?: number;\r\n    requestsPerDay?: number;\r\n    backoffStrategy?: 'linear' | 'exponential';\r\n  };\r\n\r\n  /**\r\n   * Data retention policy\r\n   */\r\n  @Column({ name: 'retention_policy', type: 'jsonb', nullable: true })\r\n  retentionPolicy?: {\r\n    maxAge?: number; // days\r\n    maxRecords?: number;\r\n    autoCleanup?: boolean;\r\n  };\r\n\r\n  /**\r\n   * Additional metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: Record<string, any>;\r\n\r\n  /**\r\n   * User who created this feed\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid', nullable: true })\r\n  createdBy?: string;\r\n\r\n  /**\r\n   * User who last updated this feed\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @OneToMany(() => IOC, ioc => ioc.threatFeed)\r\n  iocs: IOC[];\r\n\r\n  /**\r\n   * Check if feed is healthy\r\n   */\r\n  get isHealthy(): boolean {\r\n    if (this.status !== 'active') return false;\r\n    \r\n    if (!this.lastSync) return false;\r\n    \r\n    const maxAge = this.syncFrequency * 2; // Allow 2x sync frequency\r\n    const ageMinutes = (Date.now() - this.lastSync.getTime()) / (1000 * 60);\r\n    \r\n    return ageMinutes <= maxAge;\r\n  }\r\n\r\n  /**\r\n   * Check if feed is due for sync\r\n   */\r\n  get isDueForSync(): boolean {\r\n    if (!this.autoSync || this.status !== 'active') return false;\r\n    \r\n    if (!this.lastSync) return true;\r\n    \r\n    const ageMinutes = (Date.now() - this.lastSync.getTime()) / (1000 * 60);\r\n    return ageMinutes >= this.syncFrequency;\r\n  }\r\n\r\n  /**\r\n   * Get sync success rate\r\n   */\r\n  get syncSuccessRate(): number {\r\n    if (!this.syncStats) return 0;\r\n    \r\n    const total = (this.syncStats.successfulSyncs || 0) + (this.syncStats.failedSyncs || 0);\r\n    if (total === 0) return 0;\r\n    \r\n    return Math.round(((this.syncStats.successfulSyncs || 0) / total) * 100);\r\n  }\r\n\r\n  /**\r\n   * Get average IOCs per sync\r\n   */\r\n  get averageIocsPerSync(): number {\r\n    if (!this.syncStats || !this.syncStats.successfulSyncs) return 0;\r\n    \r\n    return Math.round(this.totalIocs / this.syncStats.successfulSyncs);\r\n  }\r\n\r\n  /**\r\n   * Update sync statistics\r\n   */\r\n  updateSyncStats(\r\n    success: boolean,\r\n    duration: number,\r\n    newIocs: number = 0,\r\n    updatedIocs: number = 0,\r\n    expiredIocs: number = 0,\r\n    error?: string,\r\n  ): void {\r\n    if (!this.syncStats) {\r\n      this.syncStats = {\r\n        successfulSyncs: 0,\r\n        failedSyncs: 0,\r\n        newIocsLastSync: 0,\r\n        updatedIocsLastSync: 0,\r\n        expiredIocsLastSync: 0,\r\n      };\r\n    }\r\n\r\n    this.lastSyncAttempt = new Date();\r\n\r\n    if (success) {\r\n      this.lastSync = new Date();\r\n      this.lastSyncStatus = 'success';\r\n      this.lastSyncError = null;\r\n      this.syncStats.successfulSyncs = (this.syncStats.successfulSyncs || 0) + 1;\r\n      this.syncStats.newIocsLastSync = newIocs;\r\n      this.syncStats.updatedIocsLastSync = updatedIocs;\r\n      this.syncStats.expiredIocsLastSync = expiredIocs;\r\n    } else {\r\n      this.lastSyncStatus = 'error';\r\n      this.lastSyncError = error || 'Unknown error';\r\n      this.syncStats.failedSyncs = (this.syncStats.failedSyncs || 0) + 1;\r\n    }\r\n\r\n    this.syncStats.lastSyncDuration = duration;\r\n    \r\n    // Calculate average duration\r\n    const totalSyncs = (this.syncStats.successfulSyncs || 0) + (this.syncStats.failedSyncs || 0);\r\n    if (totalSyncs > 1) {\r\n      const currentAvg = this.syncStats.averageSyncDuration || duration;\r\n      this.syncStats.averageSyncDuration = Math.round(\r\n        (currentAvg * (totalSyncs - 1) + duration) / totalSyncs\r\n      );\r\n    } else {\r\n      this.syncStats.averageSyncDuration = duration;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Mark feed as healthy\r\n   */\r\n  markAsHealthy(): void {\r\n    this.status = 'active';\r\n  }\r\n\r\n  /**\r\n   * Mark feed as unhealthy\r\n   */\r\n  markAsUnhealthy(error?: string): void {\r\n    this.status = 'error';\r\n    this.lastSyncError = error;\r\n  }\r\n\r\n  /**\r\n   * Get next sync time\r\n   */\r\n  get nextSyncTime(): Date | null {\r\n    if (!this.autoSync || !this.lastSync) return null;\r\n    \r\n    const nextSync = new Date(this.lastSync);\r\n    nextSync.setMinutes(nextSync.getMinutes() + this.syncFrequency);\r\n    \r\n    return nextSync;\r\n  }\r\n}\r\n"], "version": 3}