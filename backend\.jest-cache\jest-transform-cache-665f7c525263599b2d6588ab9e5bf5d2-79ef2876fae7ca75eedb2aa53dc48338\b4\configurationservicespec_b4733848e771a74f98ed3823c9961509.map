{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\__tests__\\configuration.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAA+C;AAC/C,oDAU0B;AAE1B,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;IAC5C,IAAI,OAAqC,CAAC;IAC1C,IAAI,aAAyC,CAAC;IAC9C,IAAI,UAAiC,CAAC;IAEtC,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,4BAA4B;QAC5B,UAAU,GAAG,IAAA,2CAA2B,GAAE,CAAC;QAE3C,qBAAqB;QACrB,aAAa,GAAG;YACd,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,UAAU,CAAC;SACpC,CAAC;QAET,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,4CAA4B;gBAC5B;oBACE,OAAO,EAAE,sBAAa;oBACtB,QAAQ,EAAE,aAAa;iBACxB;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAA+B,4CAA4B,CAAC,CAAC;IACnF,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,8BAA8B;QAC9B,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/B,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3B,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;YAC3B,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,UAAU,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC;YAC5C,UAAU,CAAC,WAAW,CAAC,YAAY,GAAG,KAAK,CAAC;YAC5C,UAAU,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC;YACtC,UAAU,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC;YAEzC,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,UAAU,CAAC,WAAW,CAAC,aAAa,GAAG,KAAK,CAAC;YAC7C,UAAU,CAAC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC;YAC3C,UAAU,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC;YACtC,UAAU,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC;YAEzC,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,UAAU,CAAC,WAAW,CAAC,aAAa,GAAG,KAAK,CAAC;YAC7C,UAAU,CAAC,WAAW,CAAC,YAAY,GAAG,KAAK,CAAC;YAC5C,UAAU,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC;YACrC,UAAU,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC;YAEzC,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,UAAU,CAAC,WAAW,CAAC,aAAa,GAAG,KAAK,CAAC;YAC7C,UAAU,CAAC,WAAW,CAAC,YAAY,GAAG,KAAK,CAAC;YAC5C,UAAU,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC;YACtC,UAAU,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC;YAExC,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;IAC/C,UAAU,CAAC,GAAG,EAAE;QACd,+CAA+C;QAC/C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACrC,IAAI,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC;gBACnF,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjF,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,MAAM,GAAG,IAAA,uCAAuB,GAAE,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;YAEvC,MAAM,MAAM,GAAG,IAAA,uCAAuB,GAAE,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC;YAEjC,MAAM,MAAM,GAAG,IAAA,uCAAuB,GAAE,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;YAEpC,MAAM,MAAM,GAAG,IAAA,uCAAuB,GAAE,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,MAAM,GAAG,IAAA,uCAAuB,GAAE,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YACtF,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,GAAG,kBAAkB,CAAC;YACpD,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,6CAA6C,CAAC;YAC3E,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,MAAM,CAAC;YAEzC,MAAM,MAAM,GAAG,IAAA,uCAAuB,GAAE,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,uBAAuB,EAAE,uBAAuB,CAAC,CAAC,CAAC;YACtF,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC;YAEhC,MAAM,MAAM,GAAG,IAAA,uCAAuB,GAAE,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,6BAA6B;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,MAAM,GAAG,IAAA,oCAAoB,GAAE,CAAC;YAEtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,OAAO,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,gBAAgB,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,GAAG,UAAU,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,GAAG,UAAU,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,QAAQ,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,GAAG,YAAY,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,MAAM,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,IAAI,CAAC;YAE/C,MAAM,MAAM,GAAG,IAAA,oCAAoB,GAAE,CAAC;YAEtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,SAAS,CAAC;YAEzC,MAAM,MAAM,GAAG,IAAA,oCAAoB,GAAE,CAAC;YAEtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,6BAA6B;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,MAAM,GAAG,IAAA,iCAAiB,GAAE,CAAC;YAEnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,mBAAmB,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,WAAW,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,GAAG,GAAG,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC;YAElC,MAAM,MAAM,GAAG,IAAA,iCAAiB,GAAE,CAAC;YAEnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,MAAM,GAAG,IAAA,oCAAoB,GAAE,CAAC;YAEtC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YAChF,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAC3F,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YACxF,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,mBAAmB,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,uBAAuB,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,GAAG,KAAK,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,uBAAuB,CAAC;YACxD,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,KAAK,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,GAAG,YAAY,CAAC;YAErD,MAAM,MAAM,GAAG,IAAA,oCAAoB,GAAE,CAAC;YAEtC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAC9D,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,MAAM,GAAG,IAAA,2CAA2B,GAAE,CAAC;YAE7C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;YAEvC,MAAM,MAAM,GAAG,IAAA,2CAA2B,GAAE,CAAC;YAE7C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\__tests__\\configuration.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { \r\n  SentinelConfigurationService,\r\n  createSentinelConfiguration,\r\n  createEnvironmentConfig,\r\n  createApplicationConfig,\r\n  createDatabaseConfig,\r\n  createRedisConfig,\r\n  createSecurityConfig,\r\n  Environment,\r\n  SentinelConfiguration\r\n} from '../configuration';\r\n\r\ndescribe('SentinelConfigurationService', () => {\r\n  let service: SentinelConfigurationService;\r\n  let configService: jest.Mocked<ConfigService>;\r\n  let mockConfig: SentinelConfiguration;\r\n\r\n  beforeEach(async () => {\r\n    // Create mock configuration\r\n    mockConfig = createSentinelConfiguration();\r\n    \r\n    // Mock ConfigService\r\n    configService = {\r\n      get: jest.fn().mockReturnValue(mockConfig),\r\n    } as any;\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        SentinelConfigurationService,\r\n        {\r\n          provide: ConfigService,\r\n          useValue: configService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<SentinelConfigurationService>(SentinelConfigurationService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n    // Reset environment variables\r\n    delete process.env['NODE_ENV'];\r\n    delete process.env['PORT'];\r\n    delete process.env['APP_NAME'];\r\n  });\r\n\r\n  describe('constructor', () => {\r\n    it('should be defined', () => {\r\n      expect(service).toBeDefined();\r\n    });\r\n\r\n    it('should load configuration from ConfigService', () => {\r\n      expect(configService.get).toHaveBeenCalledWith('sentinel');\r\n    });\r\n  });\r\n\r\n  describe('environment getter', () => {\r\n    it('should return environment configuration', () => {\r\n      const result = service.environment;\r\n      expect(result).toEqual(mockConfig.environment);\r\n      expect(result).toHaveProperty('environment');\r\n      expect(result).toHaveProperty('isDevelopment');\r\n      expect(result).toHaveProperty('isProduction');\r\n      expect(result).toHaveProperty('isTest');\r\n      expect(result).toHaveProperty('isStaging');\r\n    });\r\n  });\r\n\r\n  describe('application getter', () => {\r\n    it('should return application configuration', () => {\r\n      const result = service.application;\r\n      expect(result).toEqual(mockConfig.application);\r\n      expect(result).toHaveProperty('name');\r\n      expect(result).toHaveProperty('version');\r\n      expect(result).toHaveProperty('port');\r\n      expect(result).toHaveProperty('globalPrefix');\r\n    });\r\n  });\r\n\r\n  describe('database getter', () => {\r\n    it('should return database configuration', () => {\r\n      const result = service.database;\r\n      expect(result).toEqual(mockConfig.database);\r\n      expect(result).toHaveProperty('type');\r\n      expect(result).toHaveProperty('host');\r\n      expect(result).toHaveProperty('port');\r\n      expect(result).toHaveProperty('username');\r\n      expect(result).toHaveProperty('password');\r\n      expect(result).toHaveProperty('database');\r\n    });\r\n  });\r\n\r\n  describe('redis getter', () => {\r\n    it('should return redis configuration', () => {\r\n      const result = service.redis;\r\n      expect(result).toEqual(mockConfig.redis);\r\n      expect(result).toHaveProperty('host');\r\n      expect(result).toHaveProperty('port');\r\n      expect(result).toHaveProperty('db');\r\n    });\r\n  });\r\n\r\n  describe('security getter', () => {\r\n    it('should return security configuration', () => {\r\n      const result = service.security;\r\n      expect(result).toEqual(mockConfig.security);\r\n      expect(result).toHaveProperty('jwtSecret');\r\n      expect(result).toHaveProperty('jwtExpiresIn');\r\n      expect(result).toHaveProperty('bcryptRounds');\r\n    });\r\n  });\r\n\r\n  describe('getAll', () => {\r\n    it('should return complete configuration', () => {\r\n      const result = service.getAll();\r\n      expect(result).toEqual(mockConfig);\r\n      expect(result).toHaveProperty('environment');\r\n      expect(result).toHaveProperty('application');\r\n      expect(result).toHaveProperty('database');\r\n      expect(result).toHaveProperty('redis');\r\n      expect(result).toHaveProperty('security');\r\n    });\r\n  });\r\n\r\n  describe('environment check methods', () => {\r\n    it('should correctly identify development environment', () => {\r\n      mockConfig.environment.isDevelopment = true;\r\n      mockConfig.environment.isProduction = false;\r\n      mockConfig.environment.isTest = false;\r\n      mockConfig.environment.isStaging = false;\r\n\r\n      expect(service.isDevelopment()).toBe(true);\r\n      expect(service.isProduction()).toBe(false);\r\n      expect(service.isTest()).toBe(false);\r\n      expect(service.isStaging()).toBe(false);\r\n    });\r\n\r\n    it('should correctly identify production environment', () => {\r\n      mockConfig.environment.isDevelopment = false;\r\n      mockConfig.environment.isProduction = true;\r\n      mockConfig.environment.isTest = false;\r\n      mockConfig.environment.isStaging = false;\r\n\r\n      expect(service.isDevelopment()).toBe(false);\r\n      expect(service.isProduction()).toBe(true);\r\n      expect(service.isTest()).toBe(false);\r\n      expect(service.isStaging()).toBe(false);\r\n    });\r\n\r\n    it('should correctly identify test environment', () => {\r\n      mockConfig.environment.isDevelopment = false;\r\n      mockConfig.environment.isProduction = false;\r\n      mockConfig.environment.isTest = true;\r\n      mockConfig.environment.isStaging = false;\r\n\r\n      expect(service.isDevelopment()).toBe(false);\r\n      expect(service.isProduction()).toBe(false);\r\n      expect(service.isTest()).toBe(true);\r\n      expect(service.isStaging()).toBe(false);\r\n    });\r\n\r\n    it('should correctly identify staging environment', () => {\r\n      mockConfig.environment.isDevelopment = false;\r\n      mockConfig.environment.isProduction = false;\r\n      mockConfig.environment.isTest = false;\r\n      mockConfig.environment.isStaging = true;\r\n\r\n      expect(service.isDevelopment()).toBe(false);\r\n      expect(service.isProduction()).toBe(false);\r\n      expect(service.isTest()).toBe(false);\r\n      expect(service.isStaging()).toBe(true);\r\n    });\r\n  });\r\n});\r\n\r\ndescribe('Configuration Factory Functions', () => {\r\n  beforeEach(() => {\r\n    // Reset environment variables before each test\r\n    Object.keys(process.env).forEach(key => {\r\n      if (key.startsWith('NODE_ENV') || key.startsWith('APP_') || key.startsWith('DATABASE_') || \r\n          key.startsWith('REDIS_') || key.startsWith('JWT_') || key.startsWith('PORT')) {\r\n        delete process.env[key];\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('createEnvironmentConfig', () => {\r\n    it('should create development environment config by default', () => {\r\n      const config = createEnvironmentConfig();\r\n      \r\n      expect(config.environment).toBe('development');\r\n      expect(config.isDevelopment).toBe(true);\r\n      expect(config.isProduction).toBe(false);\r\n      expect(config.isTest).toBe(false);\r\n      expect(config.isStaging).toBe(false);\r\n      expect(config.debug).toBe(true);\r\n      expect(config.hotReload).toBe(true);\r\n    });\r\n\r\n    it('should create production environment config', () => {\r\n      process.env['NODE_ENV'] = 'production';\r\n      \r\n      const config = createEnvironmentConfig();\r\n      \r\n      expect(config.environment).toBe('production');\r\n      expect(config.isDevelopment).toBe(false);\r\n      expect(config.isProduction).toBe(true);\r\n      expect(config.isTest).toBe(false);\r\n      expect(config.isStaging).toBe(false);\r\n      expect(config.debug).toBe(false);\r\n      expect(config.hotReload).toBe(false);\r\n    });\r\n\r\n    it('should create test environment config', () => {\r\n      process.env['NODE_ENV'] = 'test';\r\n      \r\n      const config = createEnvironmentConfig();\r\n      \r\n      expect(config.environment).toBe('test');\r\n      expect(config.isDevelopment).toBe(false);\r\n      expect(config.isProduction).toBe(false);\r\n      expect(config.isTest).toBe(true);\r\n      expect(config.isStaging).toBe(false);\r\n      expect(config.debug).toBe(true);\r\n      expect(config.hotReload).toBe(false);\r\n    });\r\n\r\n    it('should create staging environment config', () => {\r\n      process.env['NODE_ENV'] = 'staging';\r\n      \r\n      const config = createEnvironmentConfig();\r\n      \r\n      expect(config.environment).toBe('staging');\r\n      expect(config.isDevelopment).toBe(false);\r\n      expect(config.isProduction).toBe(false);\r\n      expect(config.isTest).toBe(false);\r\n      expect(config.isStaging).toBe(true);\r\n      expect(config.debug).toBe(false);\r\n      expect(config.hotReload).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('createApplicationConfig', () => {\r\n    it('should create application config with defaults', () => {\r\n      const config = createApplicationConfig();\r\n      \r\n      expect(config.name).toBe('Sentinel Backend');\r\n      expect(config.version).toBe('1.0.0');\r\n      expect(config.description).toBe('Sentinel Vulnerability Assessment Platform Backend');\r\n      expect(config.port).toBe(3000);\r\n      expect(config.globalPrefix).toBe('api');\r\n      expect(config.corsOrigin).toEqual(['http://localhost:3001']);\r\n      expect(config.trustProxy).toBe(false);\r\n      expect(config.shutdownTimeout).toBe(10000);\r\n    });\r\n\r\n    it('should create application config from environment variables', () => {\r\n      process.env['APP_NAME'] = 'Test App';\r\n      process.env['APP_VERSION'] = '2.0.0';\r\n      process.env['APP_DESCRIPTION'] = 'Test Description';\r\n      process.env['PORT'] = '4000';\r\n      process.env['API_PREFIX'] = 'v1';\r\n      process.env['CORS_ORIGIN'] = 'http://localhost:3000,http://localhost:3001';\r\n      process.env['TRUST_PROXY'] = 'true';\r\n      process.env['SHUTDOWN_TIMEOUT'] = '5000';\r\n      \r\n      const config = createApplicationConfig();\r\n      \r\n      expect(config.name).toBe('Test App');\r\n      expect(config.version).toBe('2.0.0');\r\n      expect(config.description).toBe('Test Description');\r\n      expect(config.port).toBe(4000);\r\n      expect(config.globalPrefix).toBe('v1');\r\n      expect(config.corsOrigin).toEqual(['http://localhost:3000', 'http://localhost:3001']);\r\n      expect(config.trustProxy).toBe(true);\r\n      expect(config.shutdownTimeout).toBe(5000);\r\n    });\r\n\r\n    it('should handle invalid port gracefully', () => {\r\n      process.env['PORT'] = 'invalid';\r\n      \r\n      const config = createApplicationConfig();\r\n      \r\n      expect(config.port).toBe(3000); // Should fallback to default\r\n    });\r\n  });\r\n\r\n  describe('createDatabaseConfig', () => {\r\n    it('should create database config with defaults', () => {\r\n      const config = createDatabaseConfig();\r\n      \r\n      expect(config.type).toBe('postgres');\r\n      expect(config.host).toBe('localhost');\r\n      expect(config.port).toBe(5432);\r\n      expect(config.username).toBe('sentinel');\r\n      expect(config.password).toBe('password');\r\n      expect(config.database).toBe('sentinel');\r\n      expect(config.schema).toBe('public');\r\n      expect(config.ssl).toBe(false);\r\n      expect(config.maxConnections).toBe(100);\r\n    });\r\n\r\n    it('should create database config from environment variables', () => {\r\n      process.env['DATABASE_TYPE'] = 'mysql';\r\n      process.env['DATABASE_HOST'] = 'db.example.com';\r\n      process.env['DATABASE_PORT'] = '3306';\r\n      process.env['DATABASE_USERNAME'] = 'testuser';\r\n      process.env['DATABASE_PASSWORD'] = 'testpass';\r\n      process.env['DATABASE_NAME'] = 'testdb';\r\n      process.env['DATABASE_SCHEMA'] = 'testschema';\r\n      process.env['DATABASE_SSL'] = 'true';\r\n      process.env['DATABASE_MAX_CONNECTIONS'] = '50';\r\n      \r\n      const config = createDatabaseConfig();\r\n      \r\n      expect(config.type).toBe('mysql');\r\n      expect(config.host).toBe('db.example.com');\r\n      expect(config.port).toBe(3306);\r\n      expect(config.username).toBe('testuser');\r\n      expect(config.password).toBe('testpass');\r\n      expect(config.database).toBe('testdb');\r\n      expect(config.schema).toBe('testschema');\r\n      expect(config.ssl).toBe(true);\r\n      expect(config.maxConnections).toBe(50);\r\n    });\r\n\r\n    it('should handle invalid port gracefully', () => {\r\n      process.env['DATABASE_PORT'] = 'invalid';\r\n      \r\n      const config = createDatabaseConfig();\r\n      \r\n      expect(config.port).toBe(5432); // Should fallback to default\r\n    });\r\n  });\r\n\r\n  describe('createRedisConfig', () => {\r\n    it('should create redis config with defaults', () => {\r\n      const config = createRedisConfig();\r\n      \r\n      expect(config.host).toBe('localhost');\r\n      expect(config.port).toBe(6379);\r\n      expect(config.password).toBeUndefined();\r\n      expect(config.db).toBe(0);\r\n      expect(config.ttl).toBe(3600);\r\n      expect(config.maxRetries).toBe(3);\r\n      expect(config.family).toBe(4);\r\n    });\r\n\r\n    it('should create redis config from environment variables', () => {\r\n      process.env['REDIS_HOST'] = 'redis.example.com';\r\n      process.env['REDIS_PORT'] = '6380';\r\n      process.env['REDIS_PASSWORD'] = 'redispass';\r\n      process.env['REDIS_DB'] = '1';\r\n      process.env['REDIS_TTL'] = '7200';\r\n      process.env['REDIS_MAX_RETRIES'] = '5';\r\n      process.env['REDIS_FAMILY'] = '6';\r\n      \r\n      const config = createRedisConfig();\r\n      \r\n      expect(config.host).toBe('redis.example.com');\r\n      expect(config.port).toBe(6380);\r\n      expect(config.password).toBe('redispass');\r\n      expect(config.db).toBe(1);\r\n      expect(config.ttl).toBe(7200);\r\n      expect(config.maxRetries).toBe(5);\r\n      expect(config.family).toBe(6);\r\n    });\r\n  });\r\n\r\n  describe('createSecurityConfig', () => {\r\n    it('should create security config with defaults', () => {\r\n      const config = createSecurityConfig();\r\n      \r\n      expect(config.jwtSecret).toBe('your-super-secret-jwt-key-change-in-production');\r\n      expect(config.jwtExpiresIn).toBe('1h');\r\n      expect(config.jwtRefreshSecret).toBe('your-super-secret-refresh-key-change-in-production');\r\n      expect(config.jwtRefreshExpiresIn).toBe('7d');\r\n      expect(config.bcryptRounds).toBe(12);\r\n      expect(config.sessionSecret).toBe('your-super-secret-session-key-change-in-production');\r\n      expect(config.rateLimitTtl).toBe(60);\r\n      expect(config.rateLimitLimit).toBe(100);\r\n      expect(config.corsEnabled).toBe(true);\r\n      expect(config.frameOptions).toBe('DENY');\r\n    });\r\n\r\n    it('should create security config from environment variables', () => {\r\n      process.env['JWT_SECRET'] = 'custom-jwt-secret';\r\n      process.env['JWT_EXPIRES_IN'] = '2h';\r\n      process.env['JWT_REFRESH_SECRET'] = 'custom-refresh-secret';\r\n      process.env['JWT_REFRESH_EXPIRES_IN'] = '14d';\r\n      process.env['BCRYPT_ROUNDS'] = '10';\r\n      process.env['SESSION_SECRET'] = 'custom-session-secret';\r\n      process.env['RATE_LIMIT_TTL'] = '120';\r\n      process.env['RATE_LIMIT_LIMIT'] = '200';\r\n      process.env['CORS_ENABLED'] = 'false';\r\n      process.env['SECURITY_FRAME_OPTIONS'] = 'SAMEORIGIN';\r\n      \r\n      const config = createSecurityConfig();\r\n      \r\n      expect(config.jwtSecret).toBe('custom-jwt-secret');\r\n      expect(config.jwtExpiresIn).toBe('2h');\r\n      expect(config.jwtRefreshSecret).toBe('custom-refresh-secret');\r\n      expect(config.jwtRefreshExpiresIn).toBe('14d');\r\n      expect(config.bcryptRounds).toBe(10);\r\n      expect(config.sessionSecret).toBe('custom-session-secret');\r\n      expect(config.rateLimitTtl).toBe(120);\r\n      expect(config.rateLimitLimit).toBe(200);\r\n      expect(config.corsEnabled).toBe(false);\r\n      expect(config.frameOptions).toBe('SAMEORIGIN');\r\n    });\r\n  });\r\n\r\n  describe('createSentinelConfiguration', () => {\r\n    it('should create complete configuration', () => {\r\n      const config = createSentinelConfiguration();\r\n      \r\n      expect(config).toHaveProperty('environment');\r\n      expect(config).toHaveProperty('application');\r\n      expect(config).toHaveProperty('database');\r\n      expect(config).toHaveProperty('redis');\r\n      expect(config).toHaveProperty('security');\r\n      expect(config).toHaveProperty('logging');\r\n      expect(config).toHaveProperty('monitoring');\r\n      expect(config).toHaveProperty('aiService');\r\n      expect(config).toHaveProperty('externalServices');\r\n      expect(config).toHaveProperty('cache');\r\n      expect(config).toHaveProperty('validation');\r\n      expect(config).toHaveProperty('fileUpload');\r\n      expect(config).toHaveProperty('pagination');\r\n      expect(config).toHaveProperty('development');\r\n    });\r\n\r\n    it('should have consistent environment across all sub-configs', () => {\r\n      process.env['NODE_ENV'] = 'production';\r\n      \r\n      const config = createSentinelConfiguration();\r\n      \r\n      expect(config.environment.environment).toBe('production');\r\n      expect(config.environment.isProduction).toBe(true);\r\n      expect(config.development.enableHotReload).toBe(false);\r\n    });\r\n  });\r\n});"], "version": 3}