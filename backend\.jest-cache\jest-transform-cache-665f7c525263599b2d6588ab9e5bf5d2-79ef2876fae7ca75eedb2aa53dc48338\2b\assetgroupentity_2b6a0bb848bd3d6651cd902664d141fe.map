{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\domain\\entities\\asset-group.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAaiB;AACjB,iDAAuC;AAEvC;;;GAGG;AAMI,IAAM,UAAU,GAAhB,MAAM,UAAU;IAkNrB;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,IAAI,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,OAAO,OAAO,EAAE,CAAC;YACf,KAAK,EAAE,CAAC;YACR,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QAC3B,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,OAAO;QACL,MAAM,IAAI,GAAa,EAAE,CAAC;QAC1B,IAAI,OAAO,GAAe,IAAI,CAAC;QAE/B,OAAO,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC3B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QAC3B,CAAC;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,OAAY,EAAE,SAAiB;QACjD,IAAI,CAAC,aAAa,GAAG;YACnB,GAAG,IAAI,CAAC,aAAa;YACrB,GAAG,OAAO;SACX,CAAC;QACF,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,GAAW,EAAE,KAAU;QACxC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,GAAW;QAC5B,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,0BAA0B,CAAC,KAAU;QACnC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,EAAE,OAAO,EAAE,CAAC;YACjD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE,CAAC;QAE5D,iDAAiD;QACjD,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QAElE,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC;gBAC7C,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,SAAiB,EAAE,KAAU;QAChD,IAAI,CAAC;YACH,4EAA4E;YAC5E,sCAAsC;YACtC,OAAO,IAAI,CAAC,CAAC,cAAc;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,MAAM,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,CAAC;QAErD,IAAI,QAAQ,EAAE,iBAAiB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAC1D,OAAO;gBACL,GAAG,cAAc;gBACjB,GAAG,QAAQ;aACZ,CAAC;QACJ,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,UAAe;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;YACzB,GAAG,UAAU;YACb,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,SAAiB;QACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,SAAiB;QAC1B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAAc,EAAE,UAAmC;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC;QAC1C,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAE1B,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,KAAK;gBACR,OAAO,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;YAClD,KAAK,MAAM;gBACT,OAAO,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;YACtF,KAAK,MAAM;gBACT,OAAO,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;oBAC/B,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC;oBAChC,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;YACnD;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE;YACpB,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;YACtB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE;YACxB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,iCAAiC;QACjC,IAAI,IAAI,CAAC,aAAa,EAAE,cAAc,EAAE,OAAO,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE,CAAC;YAE5D,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;YACrE,CAAC;YAED,+BAA+B;YAC/B,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACrC,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;YACvC,IAAI,OAAO,CAAC,MAAM,KAAK,aAAa,CAAC,IAAI,EAAE,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,IAAI,IAAI,CAAC,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;YAC/C,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1D,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,CAAC;YAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YACzC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjD,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;IACxD,CAAC;CACF,CAAA;AAjfY,gCAAU;AAErB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;sCACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;wCACX;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CACpB;AAwBrB;IAnBC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,gBAAgB;YAChB,cAAc;YACd,YAAY;YACZ,WAAW;YACX,aAAa;YACb,aAAa;YACb,YAAY;YACZ,SAAS;YACT,YAAY;YACZ,UAAU;YACV,iBAAiB;YACjB,kBAAkB;YAClB,QAAQ;SACT;QACD,OAAO,EAAE,gBAAgB;KAC1B,CAAC;;wCACW;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;4CAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAqExC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CAyCxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;wCACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;oDAAc;AAMvC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;6CAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;6CAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;6CAAC;AAIhB;IADC,IAAA,oBAAU,GAAE;8BACJ,UAAU;0CAAC;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CAC1C;AAGlB;IADC,IAAA,sBAAY,GAAE;;4CACQ;AAIvB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oBAAK,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;;0CAC7B;qBAhNL,UAAU;IALtB,IAAA,gBAAM,EAAC,cAAc,CAAC;IACtB,IAAA,cAAI,EAAC,eAAe,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;GACP,UAAU,CAiftB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\domain\\entities\\asset-group.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n  ManyToOne,\r\n  JoinColumn,\r\n  Tree,\r\n  TreeParent,\r\n  TreeChildren,\r\n} from 'typeorm';\r\nimport { Asset } from './asset.entity';\r\n\r\n/**\r\n * Asset Group entity\r\n * Represents hierarchical grouping of assets for organization and management\r\n */\r\n@Entity('asset_groups')\r\n@Tree('closure-table')\r\n@Index(['type'])\r\n@Index(['isActive'])\r\n@Index(['parentId'])\r\nexport class AssetGroup {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Group name\r\n   */\r\n  @Column({ length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Group description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Group type for categorization\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'organizational',\r\n      'geographical',\r\n      'functional',\r\n      'technical',\r\n      'environment',\r\n      'criticality',\r\n      'compliance',\r\n      'project',\r\n      'department',\r\n      'location',\r\n      'network_segment',\r\n      'application_tier',\r\n      'custom',\r\n    ],\r\n    default: 'organizational',\r\n  })\r\n  type: string;\r\n\r\n  /**\r\n   * Whether group is active\r\n   */\r\n  @Column({ name: 'is_active', default: true })\r\n  isActive: boolean;\r\n\r\n  /**\r\n   * Group configuration and rules\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  configuration?: {\r\n    // Auto-assignment rules\r\n    autoAssignment?: {\r\n      enabled: boolean;\r\n      rules: Array<{\r\n        id: string;\r\n        name: string;\r\n        condition: string; // JSON logic expression\r\n        priority: number;\r\n        description?: string;\r\n      }>;\r\n    };\r\n    \r\n    // Group policies\r\n    policies?: {\r\n      inheritFromParent?: boolean;\r\n      overrideChildPolicies?: boolean;\r\n      defaultCriticality?: 'low' | 'medium' | 'high' | 'critical';\r\n      defaultEnvironment?: 'production' | 'staging' | 'development' | 'testing' | 'sandbox';\r\n      requiredTags?: string[];\r\n      forbiddenTags?: string[];\r\n    };\r\n    \r\n    // Monitoring settings\r\n    monitoring?: {\r\n      enabled: boolean;\r\n      scanFrequency?: 'continuous' | 'hourly' | 'daily' | 'weekly';\r\n      alertThresholds?: {\r\n        offlineAssets?: number; // percentage\r\n        vulnerableAssets?: number; // percentage\r\n        nonCompliantAssets?: number; // percentage\r\n      };\r\n      notifications?: {\r\n        enabled: boolean;\r\n        recipients: string[]; // User IDs\r\n        events: string[]; // Event types to notify about\r\n      };\r\n    };\r\n    \r\n    // Compliance settings\r\n    compliance?: {\r\n      frameworks: string[]; // Framework IDs\r\n      inheritFromParent?: boolean;\r\n      assessmentFrequency?: 'monthly' | 'quarterly' | 'semi_annually' | 'annually';\r\n      autoAssessment?: boolean;\r\n    };\r\n    \r\n    // Access control\r\n    access?: {\r\n      owners: string[]; // User IDs\r\n      viewers: string[]; // User IDs\r\n      editors: string[]; // User IDs\r\n      inheritPermissions?: boolean;\r\n    };\r\n    \r\n    // Custom attributes schema\r\n    customAttributesSchema?: Array<{\r\n      name: string;\r\n      type: 'string' | 'number' | 'boolean' | 'date' | 'enum';\r\n      required?: boolean;\r\n      defaultValue?: any;\r\n      enumValues?: string[];\r\n      validation?: {\r\n        min?: number;\r\n        max?: number;\r\n        pattern?: string;\r\n      };\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Group metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: {\r\n    // Business information\r\n    businessOwner?: string; // User ID\r\n    technicalOwner?: string; // User ID\r\n    department?: string;\r\n    costCenter?: string;\r\n    project?: string;\r\n    \r\n    // Geographical information\r\n    region?: string;\r\n    country?: string;\r\n    city?: string;\r\n    building?: string;\r\n    floor?: string;\r\n    room?: string;\r\n    \r\n    // Network information\r\n    networkSegment?: string;\r\n    vlan?: number;\r\n    subnet?: string;\r\n    \r\n    // Compliance information\r\n    dataClassification?: 'public' | 'internal' | 'confidential' | 'restricted';\r\n    regulatoryRequirements?: string[];\r\n    \r\n    // Lifecycle information\r\n    creationReason?: string;\r\n    expectedLifespan?: number; // months\r\n    reviewFrequency?: 'monthly' | 'quarterly' | 'annually';\r\n    lastReviewDate?: string;\r\n    nextReviewDate?: string;\r\n    \r\n    // Statistics cache\r\n    statistics?: {\r\n      totalAssets?: number;\r\n      assetsByType?: Record<string, number>;\r\n      assetsByStatus?: Record<string, number>;\r\n      assetsByCriticality?: Record<string, number>;\r\n      lastUpdated?: string;\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Group tags for categorization\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * Custom attributes\r\n   */\r\n  @Column({ name: 'custom_attributes', type: 'jsonb', nullable: true })\r\n  customAttributes?: Record<string, any>;\r\n\r\n  /**\r\n   * User who created the group\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  /**\r\n   * User who last updated the group\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Tree relationships\r\n  @TreeParent()\r\n  parent?: AssetGroup;\r\n\r\n  @Column({ name: 'parent_id', type: 'uuid', nullable: true })\r\n  parentId?: string;\r\n\r\n  @TreeChildren()\r\n  children: AssetGroup[];\r\n\r\n  // Asset relationships\r\n  @OneToMany(() => Asset, asset => asset.group)\r\n  assets: Asset[];\r\n\r\n  /**\r\n   * Get total asset count including child groups\r\n   */\r\n  get totalAssetCount(): number {\r\n    return this.metadata?.statistics?.totalAssets || 0;\r\n  }\r\n\r\n  /**\r\n   * Check if group is root level\r\n   */\r\n  get isRootGroup(): boolean {\r\n    return !this.parentId;\r\n  }\r\n\r\n  /**\r\n   * Check if group has children\r\n   */\r\n  get hasChildren(): boolean {\r\n    return this.children && this.children.length > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if group has assets\r\n   */\r\n  get hasAssets(): boolean {\r\n    return this.assets && this.assets.length > 0;\r\n  }\r\n\r\n  /**\r\n   * Get group depth in hierarchy\r\n   */\r\n  getDepth(): number {\r\n    let depth = 0;\r\n    let current = this.parent;\r\n    while (current) {\r\n      depth++;\r\n      current = current.parent;\r\n    }\r\n    return depth;\r\n  }\r\n\r\n  /**\r\n   * Get full path from root to this group\r\n   */\r\n  getPath(): string {\r\n    const path: string[] = [];\r\n    let current: AssetGroup = this;\r\n    \r\n    while (current) {\r\n      path.unshift(current.name);\r\n      current = current.parent;\r\n    }\r\n    \r\n    return path.join(' > ');\r\n  }\r\n\r\n  /**\r\n   * Add tag to group\r\n   */\r\n  addTag(tag: string): void {\r\n    if (!this.tags.includes(tag)) {\r\n      this.tags.push(tag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove tag from group\r\n   */\r\n  removeTag(tag: string): void {\r\n    this.tags = this.tags.filter(t => t !== tag);\r\n  }\r\n\r\n  /**\r\n   * Update group configuration\r\n   */\r\n  updateConfiguration(updates: any, updatedBy: string): void {\r\n    this.configuration = {\r\n      ...this.configuration,\r\n      ...updates,\r\n    };\r\n    this.updatedBy = updatedBy;\r\n  }\r\n\r\n  /**\r\n   * Set custom attribute\r\n   */\r\n  setCustomAttribute(key: string, value: any): void {\r\n    if (!this.customAttributes) {\r\n      this.customAttributes = {};\r\n    }\r\n    this.customAttributes[key] = value;\r\n  }\r\n\r\n  /**\r\n   * Get custom attribute\r\n   */\r\n  getCustomAttribute(key: string): any {\r\n    return this.customAttributes?.[key];\r\n  }\r\n\r\n  /**\r\n   * Check if asset matches auto-assignment rules\r\n   */\r\n  matchesAutoAssignmentRules(asset: any): boolean {\r\n    if (!this.configuration?.autoAssignment?.enabled) {\r\n      return false;\r\n    }\r\n\r\n    const rules = this.configuration.autoAssignment.rules || [];\r\n    \r\n    // Sort rules by priority (higher priority first)\r\n    const sortedRules = rules.sort((a, b) => b.priority - a.priority);\r\n    \r\n    for (const rule of sortedRules) {\r\n      if (this.evaluateRule(rule.condition, asset)) {\r\n        return true;\r\n      }\r\n    }\r\n    \r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Evaluate rule condition against asset\r\n   */\r\n  private evaluateRule(condition: string, asset: any): boolean {\r\n    try {\r\n      // Simple rule evaluation - in production, use a proper expression evaluator\r\n      // This is a simplified implementation\r\n      return true; // Placeholder\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get inherited policies from parent groups\r\n   */\r\n  getInheritedPolicies(): any {\r\n    const policies = { ...this.configuration?.policies };\r\n    \r\n    if (policies?.inheritFromParent && this.parent) {\r\n      const parentPolicies = this.parent.getInheritedPolicies();\r\n      return {\r\n        ...parentPolicies,\r\n        ...policies,\r\n      };\r\n    }\r\n    \r\n    return policies;\r\n  }\r\n\r\n  /**\r\n   * Update statistics cache\r\n   */\r\n  updateStatistics(statistics: any): void {\r\n    if (!this.metadata) {\r\n      this.metadata = {};\r\n    }\r\n    \r\n    this.metadata.statistics = {\r\n      ...statistics,\r\n      lastUpdated: new Date().toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Activate group\r\n   */\r\n  activate(updatedBy: string): void {\r\n    this.isActive = true;\r\n    this.updatedBy = updatedBy;\r\n  }\r\n\r\n  /**\r\n   * Deactivate group\r\n   */\r\n  deactivate(updatedBy: string): void {\r\n    this.isActive = false;\r\n    this.updatedBy = updatedBy;\r\n  }\r\n\r\n  /**\r\n   * Check if user has access to group\r\n   */\r\n  hasUserAccess(userId: string, accessType: 'view' | 'edit' | 'own'): boolean {\r\n    const access = this.configuration?.access;\r\n    if (!access) return false;\r\n\r\n    switch (accessType) {\r\n      case 'own':\r\n        return access.owners?.includes(userId) || false;\r\n      case 'edit':\r\n        return access.owners?.includes(userId) || access.editors?.includes(userId) || false;\r\n      case 'view':\r\n        return access.owners?.includes(userId) || \r\n               access.editors?.includes(userId) || \r\n               access.viewers?.includes(userId) || false;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get group summary\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      name: this.name,\r\n      type: this.type,\r\n      isActive: this.isActive,\r\n      parentId: this.parentId,\r\n      path: this.getPath(),\r\n      depth: this.getDepth(),\r\n      isRootGroup: this.isRootGroup,\r\n      hasChildren: this.hasChildren,\r\n      hasAssets: this.hasAssets,\r\n      totalAssetCount: this.totalAssetCount,\r\n      tags: this.tags,\r\n      createdAt: this.createdAt,\r\n      updatedAt: this.updatedAt,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export group for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      group: this.getSummary(),\r\n      configuration: this.configuration,\r\n      metadata: this.metadata,\r\n      customAttributes: this.customAttributes,\r\n      exportedAt: new Date().toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Validate group configuration\r\n   */\r\n  validateConfiguration(): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    // Validate auto-assignment rules\r\n    if (this.configuration?.autoAssignment?.enabled) {\r\n      const rules = this.configuration.autoAssignment.rules || [];\r\n      \r\n      if (rules.length === 0) {\r\n        errors.push('Auto-assignment is enabled but no rules are defined');\r\n      }\r\n      \r\n      // Check for duplicate rule IDs\r\n      const ruleIds = rules.map(r => r.id);\r\n      const uniqueRuleIds = new Set(ruleIds);\r\n      if (ruleIds.length !== uniqueRuleIds.size) {\r\n        errors.push('Duplicate rule IDs found in auto-assignment rules');\r\n      }\r\n    }\r\n\r\n    // Validate compliance settings\r\n    if (this.configuration?.compliance?.frameworks) {\r\n      if (this.configuration.compliance.frameworks.length === 0) {\r\n        errors.push('Compliance frameworks list is empty');\r\n      }\r\n    }\r\n\r\n    // Validate access control\r\n    if (this.configuration?.access) {\r\n      const access = this.configuration.access;\r\n      if (!access.owners || access.owners.length === 0) {\r\n        errors.push('At least one owner must be specified');\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clone group configuration\r\n   */\r\n  cloneConfiguration(): any {\r\n    return JSON.parse(JSON.stringify(this.configuration));\r\n  }\r\n}\r\n"], "version": 3}