{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\oauth.config.ts", "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,qDAAyE;AACzE,yDAA8C;AAsC9C,2CAA2C;AAC3C,MAAa,qBAAqB;CA8DjC;AA9DD,sDA8DC;AA3DC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6DACa;AAIxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iEACiB;AAI5B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;;gEACmB;AAI3B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gEACgB;AAI3B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oEACoB;AAI/B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;;mEACsB;AAI9B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8DACc;AAIzB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6DACa;AAIxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iEACiB;AAI5B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;;gEACmB;AAI3B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+DACe;AAI1B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mEACmB;AAI9B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;;kEACqB;AAK7B;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,KAAK,MAAM,CAAC;;yDACtB;AAKrB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,KAAK,MAAM,CAAC;;0DACrB;AAGxB,oBAAoB;AACpB,MAAM,YAAY,GAAG,CAAC,KAAyB,EAAE,QAAQ,GAAG,KAAK,EAAW,EAAE,CAC5E,KAAK,EAAE,WAAW,EAAE,KAAK,MAAM,IAAI,QAAQ,CAAC;AAE9C,MAAM,WAAW,GAAG,CAAC,KAAyB,EAAE,QAAgB,EAAU,EAAE;IAC1E,IAAI,CAAC,KAAK;QAAE,OAAO,QAAQ,CAAC;IAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7B,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;AAClD,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,KAAyB,EAAE,WAAqB,EAAE,EAAY,EAAE;IAClF,IAAI,CAAC,KAAK;QAAE,OAAO,QAAQ,CAAC;IAC5B,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC7D,CAAC,CAAC;AAEF,MAAM,GAAG,GAAG;IACV,SAAS,EAAE,CAAC,GAAW,EAAE,QAAQ,GAAG,EAAE,EAAU,EAAE,CAChD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,QAAQ;IAE9B,SAAS,EAAE,CAAC,GAAW,EAAE,QAAQ,GAAG,CAAC,EAAU,EAAE,CAC/C,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC;IAEzC,UAAU,EAAE,CAAC,GAAW,EAAE,QAAQ,GAAG,KAAK,EAAW,EAAE,CACrD,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC;IAE1C,QAAQ,EAAE,CAAC,GAAW,EAAE,WAAqB,EAAE,EAAY,EAAE,CAC3D,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC;IAExC,YAAY,EAAE,GAAY,EAAE,CAC1B,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,YAAY;CAClC,CAAC;AAEX,yBAAyB;AACzB,MAAM,QAAQ,GAAG;IACf,gBAAgB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;IAC/C,gBAAgB,EAAE,EAAE,GAAG,IAAI,EAAE,WAAW;IACxC,WAAW,EAAE,CAAC;IACd,WAAW,EAAE,IAAI;IACjB,YAAY,EAAE,IAAI;IAClB,cAAc,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;IAC9C,aAAa,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;IAC7C,gBAAgB,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;IAChD,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC;IAC1C,eAAe,EAAE,CAAC,eAAe,EAAE,gBAAgB,CAAC;IACpD,eAAe,EAAE,CAAC,OAAO,EAAE,gBAAgB,CAAC;IAC5C,cAAc,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;CACpC,CAAC;AAEX,yDAAyD;AACzD,MAAM,oBAAoB,GAAG,CAC3B,QAAgB,EAChB,gBAAmC,QAAQ,CAAC,cAAc,EACrC,EAAE;IACvB,MAAM,MAAM,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IACtC,MAAM,QAAQ,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,MAAM,YAAY,CAAC,CAAC;IACtD,MAAM,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,MAAM,gBAAgB,CAAC,CAAC;IAC9D,MAAM,WAAW,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,MAAM,eAAe,CAAC,CAAC;IAC5D,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,MAAM,SAAS,CAAC,CAAC,CAAC,gBAAgB;IAClE,MAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,MAAM,QAAQ,EAAE,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;IAClE,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,IAAI,YAAY,CAAC,CAAC;IAEpF,OAAO;QACL,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,MAAM;QACN,KAAK;QACL,OAAO;KACR,CAAC;AACJ,CAAC,CAAC;AAEF;;;GAGG;AACU,QAAA,WAAW,GAAG,IAAA,mBAAU,EAAC,OAAO,EAAE,GAAgB,EAAE;IAC/D,iDAAiD;IACjD,IAAI,GAAG,CAAC,YAAY,EAAE,EAAE,CAAC;QACvB,MAAM,iBAAiB,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAClD,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAC3D,MAAM,QAAQ,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,QAAQ,YAAY,CAAC,CAAC;YACxD,MAAM,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,QAAQ,gBAAgB,CAAC,CAAC;YAChE,OAAO,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,2DAA2D,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzG,CAAC;IACH,CAAC;IAED,OAAO;QACL,MAAM,EAAE,oBAAoB,CAAC,QAAQ,EAAE,QAAQ,CAAC,aAAa,CAAC;QAC9D,SAAS,EAAE,oBAAoB,CAAC,WAAW,EAAE,QAAQ,CAAC,gBAAgB,CAAC;QACvE,MAAM,EAAE,oBAAoB,CAAC,QAAQ,EAAE,QAAQ,CAAC,aAAa,CAAC;QAC9D,QAAQ,EAAE,oBAAoB,CAAC,UAAU,EAAE,QAAQ,CAAC,eAAe,CAAC;QACpE,QAAQ,EAAE,oBAAoB,CAAC,UAAU,EAAE,QAAQ,CAAC,eAAe,CAAC;QACpE,OAAO,EAAE,oBAAoB,CAAC,SAAS,EAAE,QAAQ,CAAC,cAAc,CAAC;QACjE,KAAK,EAAE,oBAAoB,CAAC,OAAO,CAAC;QACpC,IAAI,EAAE,oBAAoB,CAAC,MAAM,CAAC;QAClC,KAAK,EAAE,oBAAoB,CAAC,OAAO,CAAC;QACpC,OAAO,EAAE,oBAAoB,CAAC,SAAS,CAAC;QAExC,QAAQ,EAAE;YACR,eAAe,EAAE,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,QAAQ,CAAC,gBAAgB,CAAC;YACnF,eAAe,EAAE,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,QAAQ,CAAC,gBAAgB,CAAC;YACnF,UAAU,EAAE,GAAG,CAAC,SAAS,CAAC,mBAAmB,EAAE,QAAQ,CAAC,WAAW,CAAC;YACpE,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,QAAQ,CAAC,WAAW,CAAC;YACrE,WAAW,EAAE,GAAG,CAAC,UAAU,CAAC,oBAAoB,EAAE,QAAQ,CAAC,YAAY,CAAC;YACxE,aAAa,EAAE,GAAG,CAAC,QAAQ,CAAC,sBAAsB,EAAE,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC;SAClF;KACF,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,iBAAiB,GAAG;IAC/B;;OAEG;IACH,mBAAmB,EAAE,CAAC,MAAmB,EAAY,EAAE;QACrD,OAAO,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;aAC1B,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC;aAC7D,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,iBAAiB,EAAE,CAAC,MAAmB,EAAE,QAAgB,EAA8B,EAAE;QACvF,MAAM,cAAc,GAAG,MAAM,CAAC,QAA6B,CAAC,CAAC;QAC7D,OAAO,CAAC,cAAc,IAAI,OAAO,cAAc,KAAK,QAAQ,IAAI,SAAS,IAAI,cAAc,CAAC;YAC1F,CAAC,CAAC,cAAqC;YACvC,CAAC,CAAC,IAAI,CAAC;IACX,CAAC;IAED;;OAEG;IACH,iBAAiB,EAAE,CAAC,MAAmB,EAAE,QAAgB,EAAW,EAAE;QACpE,MAAM,cAAc,GAAG,yBAAiB,CAAC,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC7E,OAAO,cAAc,EAAE,OAAO,IAAI,KAAK,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,cAAc,EAAE,CAAC,MAAmB,EAAE,QAAgB,EAAE,OAAgB,EAAU,EAAE;QAClF,MAAM,cAAc,GAAG,yBAAiB,CAAC,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAE7E,IAAI,cAAc,EAAE,WAAW,EAAE,CAAC;YAChC,OAAO,cAAc,CAAC,WAAW,CAAC;QACpC,CAAC;QAED,gCAAgC;QAChC,MAAM,IAAI,GAAG,OAAO,IAAI,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAAC;QAC1E,OAAO,GAAG,IAAI,SAAS,QAAQ,WAAW,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,QAAQ,EAAE,CAAC,MAAmB,EAAY,EAAE;QAC1C,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,iBAAiB;QACjB,IAAI,MAAM,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,EAAE,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,EAAE,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;QAED,0BAA0B;QAC1B,MAAM,gBAAgB,GAAG,yBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAEvE,KAAK,MAAM,QAAQ,IAAI,gBAAgB,EAAE,CAAC;YACxC,MAAM,cAAc,GAAG,yBAAiB,CAAC,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAE7E,IAAI,CAAC,cAAc;gBAAE,SAAS;YAE9B,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;gBAC7B,MAAM,CAAC,IAAI,CAAC,mBAAmB,QAAQ,qBAAqB,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;gBACjC,MAAM,CAAC,IAAI,CAAC,mBAAmB,QAAQ,yBAAyB,CAAC,CAAC;YACpE,CAAC;YAED,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/D,MAAM,CAAC,IAAI,CAAC,mBAAmB,QAAQ,+BAA+B,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,QAAQ,EAAE,CAAC,MAAmB,EAAW,EAAE;QACzC,MAAM,gBAAgB,GAAG,yBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAEvE,0CAA0C;QAC1C,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,6DAA6D;QAC7D,KAAK,MAAM,QAAQ,IAAI,gBAAgB,EAAE,CAAC;YACxC,MAAM,cAAc,GAAG,yBAAiB,CAAC,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAE7E,IAAI,CAAC,cAAc,EAAE,QAAQ,IAAI,CAAC,cAAc,EAAE,YAAY,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,mCAAmC;YACnC,IAAI,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAC3C,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBACpD,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,sCAAsC;QACtC,OAAO,MAAM,CAAC,QAAQ,CAAC,UAAU,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,sBAAsB,EAAE,CAAC,QAAgB,EAAU,EAAE;QACnD,MAAM,YAAY,GAA2B;YAC3C,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,WAAW;YACtB,MAAM,EAAE,QAAQ;YAChB,QAAQ,EAAE,UAAU;YACpB,QAAQ,EAAE,UAAU;YACpB,OAAO,EAAE,SAAS;YAClB,KAAK,EAAE,OAAO;YACd,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,OAAO;YACd,OAAO,EAAE,eAAe;SACzB,CAAC;QAEF,OAAO,YAAY,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC;CACF,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\oauth.config.ts"], "sourcesContent": ["import { registerAs } from '@nestjs/config';\r\nimport { IsString, IsOptional, IsUrl, IsBoolean } from 'class-validator';\r\nimport { Transform } from 'class-transformer';\r\n\r\n/**\r\n * OAuth configuration module\r\n * Provides configuration for OAuth providers (Google, Microsoft, etc.)\r\n */\r\n\r\n// OAuth Provider Configuration interfaces\r\nexport interface OAuthProviderConfig {\r\n  readonly clientId?: string;\r\n  readonly clientSecret?: string;\r\n  readonly callbackUrl?: string;\r\n  readonly tenant?: string; // For Microsoft\r\n  readonly scope?: string[];\r\n  readonly enabled: boolean;\r\n}\r\n\r\nexport interface OAuthConfig {\r\n  readonly google: OAuthProviderConfig;\r\n  readonly microsoft: OAuthProviderConfig;\r\n  readonly github: OAuthProviderConfig;\r\n  readonly linkedin: OAuthProviderConfig;\r\n  readonly facebook: OAuthProviderConfig;\r\n  readonly twitter: OAuthProviderConfig;\r\n  readonly apple: OAuthProviderConfig;\r\n  readonly okta: OAuthProviderConfig;\r\n  readonly auth0: OAuthProviderConfig;\r\n  readonly generic: OAuthProviderConfig;\r\n  readonly settings: {\r\n    readonly stateExpiration: number;\r\n    readonly callbackTimeout: number;\r\n    readonly maxRetries: number;\r\n    readonly enablePKCE: boolean;\r\n    readonly enableNonce: boolean;\r\n    readonly defaultScopes: string[];\r\n  };\r\n}\r\n\r\n// Validation class for OAuth configuration\r\nexport class OAuthConfigValidation {\r\n  @IsOptional()\r\n  @IsString()\r\n  googleClientId?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  googleClientSecret?: string;\r\n\r\n  @IsOptional()\r\n  @IsUrl()\r\n  googleCallbackUrl?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  microsoftClientId?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  microsoftClientSecret?: string;\r\n\r\n  @IsOptional()\r\n  @IsUrl()\r\n  microsoftCallbackUrl?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  microsoftTenant?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  githubClientId?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  githubClientSecret?: string;\r\n\r\n  @IsOptional()\r\n  @IsUrl()\r\n  githubCallbackUrl?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  linkedinClientId?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  linkedinClientSecret?: string;\r\n\r\n  @IsOptional()\r\n  @IsUrl()\r\n  linkedinCallbackUrl?: string;\r\n\r\n  @IsOptional()\r\n  @IsBoolean()\r\n  @Transform(({ value }) => value === 'true')\r\n  enablePKCE?: boolean;\r\n\r\n  @IsOptional()\r\n  @IsBoolean()\r\n  @Transform(({ value }) => value === 'true')\r\n  enableNonce?: boolean;\r\n}\r\n\r\n// Utility functions\r\nconst parseBoolean = (value: string | undefined, fallback = false): boolean => \r\n  value?.toLowerCase() === 'true' || fallback;\r\n\r\nconst parseNumber = (value: string | undefined, fallback: number): number => {\r\n  if (!value) return fallback;\r\n  const parsed = Number(value);\r\n  return Number.isNaN(parsed) ? fallback : parsed;\r\n};\r\n\r\nconst parseArray = (value: string | undefined, fallback: string[] = []): string[] => {\r\n  if (!value) return fallback;\r\n  return value.split(',').map(s => s.trim()).filter(Boolean);\r\n};\r\n\r\nconst env = {\r\n  getString: (key: string, fallback = ''): string => \r\n    process.env[key] ?? fallback,\r\n  \r\n  getNumber: (key: string, fallback = 0): number => \r\n    parseNumber(process.env[key], fallback),\r\n  \r\n  getBoolean: (key: string, fallback = false): boolean => \r\n    parseBoolean(process.env[key], fallback),\r\n  \r\n  getArray: (key: string, fallback: string[] = []): string[] =>\r\n    parseArray(process.env[key], fallback),\r\n  \r\n  isProduction: (): boolean => \r\n    process.env['NODE_ENV'] === 'production',\r\n} as const;\r\n\r\n// Configuration defaults\r\nconst DEFAULTS = {\r\n  STATE_EXPIRATION: 30 * 60 * 1000, // 30 minutes\r\n  CALLBACK_TIMEOUT: 60 * 1000, // 1 minute\r\n  MAX_RETRIES: 3,\r\n  ENABLE_PKCE: true,\r\n  ENABLE_NONCE: true,\r\n  DEFAULT_SCOPES: ['openid', 'email', 'profile'],\r\n  GOOGLE_SCOPES: ['openid', 'email', 'profile'],\r\n  MICROSOFT_SCOPES: ['openid', 'email', 'profile'],\r\n  GITHUB_SCOPES: ['user:email', 'read:user'],\r\n  LINKEDIN_SCOPES: ['r_liteprofile', 'r_emailaddress'],\r\n  FACEBOOK_SCOPES: ['email', 'public_profile'],\r\n  TWITTER_SCOPES: ['tweet.read', 'users.read'],\r\n} as const;\r\n\r\n// Helper function to create OAuth provider configuration\r\nconst createProviderConfig = (\r\n  provider: string,\r\n  defaultScopes: readonly string[] = DEFAULTS.DEFAULT_SCOPES,\r\n): OAuthProviderConfig => {\r\n  const prefix = provider.toUpperCase();\r\n  const clientId = env.getString(`${prefix}_CLIENT_ID`);\r\n  const clientSecret = env.getString(`${prefix}_CLIENT_SECRET`);\r\n  const callbackUrl = env.getString(`${prefix}_CALLBACK_URL`);\r\n  const tenant = env.getString(`${prefix}_TENANT`); // For Microsoft\r\n  const scope = env.getArray(`${prefix}_SCOPE`, [...defaultScopes]);\r\n  const enabled = env.getBoolean(`${prefix}_ENABLED`) && !!(clientId && clientSecret);\r\n\r\n  return {\r\n    clientId,\r\n    clientSecret,\r\n    callbackUrl,\r\n    tenant,\r\n    scope,\r\n    enabled,\r\n  };\r\n};\r\n\r\n/**\r\n * OAuth configuration factory\r\n * Creates OAuth configuration with environment variable support and validation\r\n */\r\nexport const oauthConfig = registerAs('oauth', (): OAuthConfig => {\r\n  // Warn about missing configuration in production\r\n  if (env.isProduction()) {\r\n    const requiredProviders = ['GOOGLE', 'MICROSOFT'];\r\n    const missingProviders = requiredProviders.filter(provider => {\r\n      const clientId = env.getString(`${provider}_CLIENT_ID`);\r\n      const clientSecret = env.getString(`${provider}_CLIENT_SECRET`);\r\n      return !clientId || !clientSecret;\r\n    });\r\n\r\n    if (missingProviders.length > 0) {\r\n      console.warn(`⚠️  WARNING: Missing OAuth configuration for providers: ${missingProviders.join(', ')}`);\r\n    }\r\n  }\r\n\r\n  return {\r\n    google: createProviderConfig('google', DEFAULTS.GOOGLE_SCOPES),\r\n    microsoft: createProviderConfig('microsoft', DEFAULTS.MICROSOFT_SCOPES),\r\n    github: createProviderConfig('github', DEFAULTS.GITHUB_SCOPES),\r\n    linkedin: createProviderConfig('linkedin', DEFAULTS.LINKEDIN_SCOPES),\r\n    facebook: createProviderConfig('facebook', DEFAULTS.FACEBOOK_SCOPES),\r\n    twitter: createProviderConfig('twitter', DEFAULTS.TWITTER_SCOPES),\r\n    apple: createProviderConfig('apple'),\r\n    okta: createProviderConfig('okta'),\r\n    auth0: createProviderConfig('auth0'),\r\n    generic: createProviderConfig('generic'),\r\n\r\n    settings: {\r\n      stateExpiration: env.getNumber('OAUTH_STATE_EXPIRATION', DEFAULTS.STATE_EXPIRATION),\r\n      callbackTimeout: env.getNumber('OAUTH_CALLBACK_TIMEOUT', DEFAULTS.CALLBACK_TIMEOUT),\r\n      maxRetries: env.getNumber('OAUTH_MAX_RETRIES', DEFAULTS.MAX_RETRIES),\r\n      enablePKCE: env.getBoolean('OAUTH_ENABLE_PKCE', DEFAULTS.ENABLE_PKCE),\r\n      enableNonce: env.getBoolean('OAUTH_ENABLE_NONCE', DEFAULTS.ENABLE_NONCE),\r\n      defaultScopes: env.getArray('OAUTH_DEFAULT_SCOPES', [...DEFAULTS.DEFAULT_SCOPES]),\r\n    },\r\n  };\r\n});\r\n\r\n/**\r\n * OAuth configuration helper functions\r\n */\r\nexport const OAuthConfigHelper = {\r\n  /**\r\n   * Get enabled OAuth providers\r\n   */\r\n  getEnabledProviders: (config: OAuthConfig): string[] => {\r\n    return Object.entries(config)\r\n      .filter(([key, value]) => key !== 'settings' && value.enabled)\r\n      .map(([key]) => key);\r\n  },\r\n\r\n  /**\r\n   * Get provider configuration\r\n   */\r\n  getProviderConfig: (config: OAuthConfig, provider: string): OAuthProviderConfig | null => {\r\n    const providerConfig = config[provider as keyof OAuthConfig];\r\n    return (providerConfig && typeof providerConfig === 'object' && 'enabled' in providerConfig) \r\n      ? providerConfig as OAuthProviderConfig \r\n      : null;\r\n  },\r\n\r\n  /**\r\n   * Check if provider is enabled\r\n   */\r\n  isProviderEnabled: (config: OAuthConfig, provider: string): boolean => {\r\n    const providerConfig = OAuthConfigHelper.getProviderConfig(config, provider);\r\n    return providerConfig?.enabled || false;\r\n  },\r\n\r\n  /**\r\n   * Get callback URL for provider\r\n   */\r\n  getCallbackUrl: (config: OAuthConfig, provider: string, baseUrl?: string): string => {\r\n    const providerConfig = OAuthConfigHelper.getProviderConfig(config, provider);\r\n    \r\n    if (providerConfig?.callbackUrl) {\r\n      return providerConfig.callbackUrl;\r\n    }\r\n\r\n    // Generate default callback URL\r\n    const base = baseUrl || env.getString('APP_URL', 'http://localhost:3000');\r\n    return `${base}/auth/${provider}/callback`;\r\n  },\r\n\r\n  /**\r\n   * Validate OAuth configuration\r\n   */\r\n  validate: (config: OAuthConfig): string[] => {\r\n    const errors: string[] = [];\r\n\r\n    // Check settings\r\n    if (config.settings.stateExpiration <= 0) {\r\n      errors.push('OAuth state expiration must be positive');\r\n    }\r\n\r\n    if (config.settings.callbackTimeout <= 0) {\r\n      errors.push('OAuth callback timeout must be positive');\r\n    }\r\n\r\n    if (config.settings.maxRetries < 0) {\r\n      errors.push('OAuth max retries cannot be negative');\r\n    }\r\n\r\n    // Check enabled providers\r\n    const enabledProviders = OAuthConfigHelper.getEnabledProviders(config);\r\n    \r\n    for (const provider of enabledProviders) {\r\n      const providerConfig = OAuthConfigHelper.getProviderConfig(config, provider);\r\n      \r\n      if (!providerConfig) continue;\r\n\r\n      if (!providerConfig.clientId) {\r\n        errors.push(`OAuth provider '${provider}' missing client ID`);\r\n      }\r\n\r\n      if (!providerConfig.clientSecret) {\r\n        errors.push(`OAuth provider '${provider}' missing client secret`);\r\n      }\r\n\r\n      if (!providerConfig.scope || providerConfig.scope.length === 0) {\r\n        errors.push(`OAuth provider '${provider}' missing scope configuration`);\r\n      }\r\n    }\r\n\r\n    return errors;\r\n  },\r\n\r\n  /**\r\n   * Check if configuration is secure for production\r\n   */\r\n  isSecure: (config: OAuthConfig): boolean => {\r\n    const enabledProviders = OAuthConfigHelper.getEnabledProviders(config);\r\n    \r\n    // Must have at least one enabled provider\r\n    if (enabledProviders.length === 0) {\r\n      return false;\r\n    }\r\n\r\n    // Check that all enabled providers have proper configuration\r\n    for (const provider of enabledProviders) {\r\n      const providerConfig = OAuthConfigHelper.getProviderConfig(config, provider);\r\n      \r\n      if (!providerConfig?.clientId || !providerConfig?.clientSecret) {\r\n        return false;\r\n      }\r\n\r\n      // Check for default/example values\r\n      if (providerConfig.clientId.includes('example') || \r\n          providerConfig.clientSecret.includes('example')) {\r\n        return false;\r\n      }\r\n    }\r\n\r\n    // Security settings should be enabled\r\n    return config.settings.enablePKCE && config.settings.enableNonce;\r\n  },\r\n\r\n  /**\r\n   * Get provider display name\r\n   */\r\n  getProviderDisplayName: (provider: string): string => {\r\n    const displayNames: Record<string, string> = {\r\n      google: 'Google',\r\n      microsoft: 'Microsoft',\r\n      github: 'GitHub',\r\n      linkedin: 'LinkedIn',\r\n      facebook: 'Facebook',\r\n      twitter: 'Twitter',\r\n      apple: 'Apple',\r\n      okta: 'Okta',\r\n      auth0: 'Auth0',\r\n      generic: 'Generic OAuth',\r\n    };\r\n\r\n    return displayNames[provider] || provider.charAt(0).toUpperCase() + provider.slice(1);\r\n  },\r\n};"], "version": 3}