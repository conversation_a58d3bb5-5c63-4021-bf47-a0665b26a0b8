03699c92ba3c5d0f08bbaacd9381a49d
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseConfigurationService = exports.databaseConfig = void 0;
const config_1 = require("@nestjs/config");
const common_1 = require("@nestjs/common");
const config_2 = require("@nestjs/config");
/**
 * Environment variable utilities with strict type safety
 */
class EnvParser {
    static string(key, defaultValue) {
        return process.env[key] ?? defaultValue;
    }
    static number(key, defaultValue) {
        const value = process.env[key];
        const parsed = value ? Number(value) : NaN;
        return Number.isInteger(parsed) ? parsed : defaultValue;
    }
    static boolean(key, defaultValue = false) {
        const value = process.env[key];
        return value === 'true' ? true : value === 'false' ? false : defaultValue;
    }
    static databaseType(key, defaultValue) {
        const value = process.env[key];
        const validTypes = new Set(['postgres', 'mysql', 'mariadb']);
        return validTypes.has(value) ? value : defaultValue;
    }
    static optionalString(key) {
        return process.env[key] || undefined;
    }
}
/**
 * Create database pool configuration
 */
const createPoolConfig = () => ({
    min: EnvParser.number('DATABASE_POOL_MIN', 2),
    max: EnvParser.number('DATABASE_POOL_MAX', 100),
    acquireTimeoutMillis: EnvParser.number('DATABASE_POOL_ACQUIRE_TIMEOUT', 60000),
    createTimeoutMillis: EnvParser.number('DATABASE_POOL_CREATE_TIMEOUT', 30000),
    destroyTimeoutMillis: EnvParser.number('DATABASE_POOL_DESTROY_TIMEOUT', 5000),
    idleTimeoutMillis: EnvParser.number('DATABASE_POOL_IDLE_TIMEOUT', 30000),
    reapIntervalMillis: EnvParser.number('DATABASE_POOL_REAP_INTERVAL', 1000),
    createRetryIntervalMillis: EnvParser.number('DATABASE_POOL_CREATE_RETRY_INTERVAL', 200),
    propagateCreateError: EnvParser.boolean('DATABASE_POOL_PROPAGATE_CREATE_ERROR', false),
});
/**
 * Create database SSL configuration
 */
const createSSLConfig = () => ({
    enabled: EnvParser.boolean('DATABASE_SSL', false),
    rejectUnauthorized: EnvParser.boolean('DATABASE_SSL_REJECT_UNAUTHORIZED', false),
    ca: EnvParser.optionalString('DATABASE_SSL_CA'),
    cert: EnvParser.optionalString('DATABASE_SSL_CERT'),
    key: EnvParser.optionalString('DATABASE_SSL_KEY'),
});
/**
 * Create database migration configuration
 */
const createMigrationConfig = () => ({
    run: EnvParser.boolean('DATABASE_MIGRATIONS_RUN', true),
    tableName: EnvParser.string('DATABASE_MIGRATIONS_TABLE', 'migrations'),
    directory: EnvParser.string('DATABASE_MIGRATIONS_DIR', 'src/database/migrations'),
    transactionPerMigration: EnvParser.boolean('DATABASE_MIGRATIONS_TRANSACTION_PER_MIGRATION', true),
    disableTransactions: EnvParser.boolean('DATABASE_MIGRATIONS_DISABLE_TRANSACTIONS', false),
});
/**
 * Create database logging configuration
 */
const createLoggingConfig = () => {
    const isDevelopment = process.env['NODE_ENV'] === 'development';
    return {
        enabled: EnvParser.boolean('DATABASE_LOGGING_ENABLED', isDevelopment),
        logQueries: EnvParser.boolean('DATABASE_LOG_QUERIES', isDevelopment),
        logErrors: EnvParser.boolean('DATABASE_LOG_ERRORS', true),
        logSlowQueries: EnvParser.boolean('DATABASE_LOG_SLOW_QUERIES', false),
        slowQueryThreshold: EnvParser.number('DATABASE_SLOW_QUERY_THRESHOLD', 1000),
        logOnlyFailedQueries: EnvParser.boolean('DATABASE_LOG_ONLY_FAILED_QUERIES', false),
        maxQueryExecutionTime: EnvParser.number('DATABASE_MAX_QUERY_EXECUTION_TIME', 30000),
    };
};
/**
 * Create complete database configuration
 */
const createDatabaseConfig = () => {
    const isDevelopment = process.env['NODE_ENV'] === 'development';
    const isTest = process.env['NODE_ENV'] === 'test';
    return {
        type: EnvParser.databaseType('DATABASE_TYPE', 'postgres'),
        host: EnvParser.string('DATABASE_HOST', 'localhost'),
        port: EnvParser.number('DATABASE_PORT', 5432),
        username: EnvParser.string('DATABASE_USERNAME', 'sentinel_user'),
        password: EnvParser.string('DATABASE_PASSWORD', 'sentinel_password'),
        database: EnvParser.string('DATABASE_NAME', isTest ? 'sentinel_test' : 'sentinel_db'),
        schema: EnvParser.string('DATABASE_SCHEMA', 'public'),
        ssl: createSSLConfig(),
        pool: createPoolConfig(),
        migration: createMigrationConfig(),
        logging: createLoggingConfig(),
        synchronize: EnvParser.boolean('DATABASE_SYNCHRONIZE', isDevelopment),
        dropSchema: EnvParser.boolean('DATABASE_DROP_SCHEMA', false),
        entities: [
            `${__dirname}/../../**/*.entity{.ts,.js}`,
            `${__dirname}/../../modules/**/domain/entities/**/*.entity{.ts,.js}`,
        ],
        migrations: [`${__dirname}/../../database/migrations/*{.ts,.js}`],
        subscribers: [`${__dirname}/../../database/subscribers/*{.ts,.js}`],
        timezone: EnvParser.string('DATABASE_TIMEZONE', 'UTC'),
        charset: EnvParser.string('DATABASE_CHARSET', 'utf8mb4'),
        collation: EnvParser.string('DATABASE_COLLATION', 'utf8mb4_unicode_ci'),
    };
};
/**
 * Database configuration factory for TypeORM
 * Provides environment-specific database connection settings with strict type safety
 */
exports.databaseConfig = (0, config_1.registerAs)('database', () => {
    const isDevelopment = process.env['NODE_ENV'] === 'development';
    const isProduction = process.env['NODE_ENV'] === 'production';
    const dbConfig = createDatabaseConfig();
    const config = {
        type: dbConfig.type,
        host: dbConfig.host,
        port: dbConfig.port,
        username: dbConfig.username,
        password: dbConfig.password,
        database: dbConfig.database,
        schema: dbConfig.schema,
        ssl: dbConfig.ssl.enabled ? {
            rejectUnauthorized: dbConfig.ssl.rejectUnauthorized,
            ...(dbConfig.ssl.ca && { ca: dbConfig.ssl.ca }),
            ...(dbConfig.ssl.cert && { cert: dbConfig.ssl.cert }),
            ...(dbConfig.ssl.key && { key: dbConfig.ssl.key }),
        } : false,
        entities: dbConfig.entities,
        migrations: dbConfig.migrations,
        subscribers: dbConfig.subscribers,
        extra: {
            min: dbConfig.pool.min,
            max: dbConfig.pool.max,
            acquireTimeoutMillis: dbConfig.pool.acquireTimeoutMillis,
            createTimeoutMillis: dbConfig.pool.createTimeoutMillis,
            destroyTimeoutMillis: dbConfig.pool.destroyTimeoutMillis,
            idleTimeoutMillis: dbConfig.pool.idleTimeoutMillis,
            reapIntervalMillis: dbConfig.pool.reapIntervalMillis,
            createRetryIntervalMillis: dbConfig.pool.createRetryIntervalMillis,
            propagateCreateError: dbConfig.pool.propagateCreateError,
            statement_timeout: dbConfig.logging.maxQueryExecutionTime,
            query_timeout: dbConfig.logging.maxQueryExecutionTime,
        },
        synchronize: dbConfig.synchronize,
        dropSchema: dbConfig.dropSchema,
        migrationsRun: dbConfig.migration.run,
        migrationsTableName: dbConfig.migration.tableName,
        migrationsTransactionMode: dbConfig.migration.transactionPerMigration ? 'each' : 'all',
        logging: dbConfig.logging.enabled ?
            (isDevelopment ?
                (dbConfig.logging.logQueries ? ['query', 'error', 'warn', 'info'] : ['error', 'warn']) :
                (isProduction ? ['error'] : ['error', 'warn'])) : false,
        logger: 'advanced-console',
        maxQueryExecutionTime: dbConfig.logging.slowQueryThreshold,
    };
    // Add Redis cache configuration if enabled
    if (EnvParser.boolean('REDIS_ENABLED', true)) {
        const redisPassword = process.env['REDIS_PASSWORD'];
        config.cache = {
            type: 'redis',
            options: {
                host: EnvParser.string('REDIS_HOST', 'localhost'),
                port: EnvParser.number('REDIS_PORT', 6379),
                ...(redisPassword && { password: redisPassword }),
                db: EnvParser.number('REDIS_CACHE_DB', 1),
            },
            duration: EnvParser.number('DATABASE_CACHE_DURATION', 30000),
        };
    }
    return config;
});
/**
 * Database configuration service for dependency injection
 */
let DatabaseConfigurationService = class DatabaseConfigurationService {
    constructor(configService) {
        this.configService = configService;
        this.config = createDatabaseConfig();
    }
    get type() {
        return this.config.type;
    }
    get host() {
        return this.config.host;
    }
    get port() {
        return this.config.port;
    }
    get username() {
        return this.config.username;
    }
    get password() {
        return this.config.password;
    }
    get database() {
        return this.config.database;
    }
    get schema() {
        return this.config.schema;
    }
    get ssl() {
        return this.config.ssl;
    }
    get pool() {
        return this.config.pool;
    }
    get migration() {
        return this.config.migration;
    }
    get logging() {
        return this.config.logging;
    }
    get synchronize() {
        return this.config.synchronize;
    }
    get dropSchema() {
        return this.config.dropSchema;
    }
    get timezone() {
        return this.config.timezone;
    }
    get charset() {
        return this.config.charset;
    }
    get collation() {
        return this.config.collation;
    }
    /**
     * Get complete database configuration
     */
    getAll() {
        return this.config;
    }
    /**
     * Get TypeORM configuration options
     */
    getTypeOrmOptions() {
        return this.configService.get('database');
    }
    /**
     * Get connection string for the database
     */
    getConnectionString() {
        const { type, username, password, host, port, database } = this.config;
        return `${type}://${username}:${password}@${host}:${port}/${database}`;
    }
    /**
     * Check if SSL is enabled
     */
    isSSLEnabled() {
        return this.config.ssl.enabled;
    }
    /**
     * Check if migrations should run automatically
     */
    shouldRunMigrations() {
        return this.config.migration.run;
    }
    /**
     * Check if query logging is enabled
     */
    isQueryLoggingEnabled() {
        return this.config.logging.enabled && this.config.logging.logQueries;
    }
};
exports.DatabaseConfigurationService = DatabaseConfigurationService;
exports.DatabaseConfigurationService = DatabaseConfigurationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_2.ConfigService !== "undefined" && config_2.ConfigService) === "function" ? _a : Object])
], DatabaseConfigurationService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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