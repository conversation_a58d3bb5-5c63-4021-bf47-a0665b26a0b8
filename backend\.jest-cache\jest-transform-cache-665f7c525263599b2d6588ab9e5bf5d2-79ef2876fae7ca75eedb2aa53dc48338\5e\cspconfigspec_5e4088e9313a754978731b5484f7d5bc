39892a6f31c65e5a7591e4ea5c7f594b
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const csp_config_1 = require("../csp.config");
describe('CspConfig', () => {
    let cspConfig;
    let configService;
    beforeEach(async () => {
        const mockConfigService = {
            get: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                csp_config_1.CspConfig,
                {
                    provide: config_1.ConfigService,
                    useValue: mockConfigService,
                },
            ],
        }).compile();
        cspConfig = module.get(csp_config_1.CspConfig);
        configService = module.get(config_1.ConfigService);
    });
    describe('generateCSPHeader', () => {
        it('should return production CSP for production environment', () => {
            configService.get.mockReturnValue('production');
            const csp = cspConfig.generateCSPHeader();
            expect(csp).toContain("default-src 'self'");
            expect(csp).toContain("object-src 'none'");
            expect(csp).toContain("frame-ancestors 'none'");
            expect(csp).toContain('upgrade-insecure-requests');
            expect(csp).toContain('block-all-mixed-content');
        });
        it('should return staging CSP for staging environment', () => {
            configService.get.mockReturnValue('staging');
            const csp = cspConfig.generateCSPHeader();
            expect(csp).toContain("default-src 'self'");
            expect(csp).toContain("script-src 'self' 'unsafe-inline' 'unsafe-eval'");
            expect(csp).toContain('report-uri /api/v1/security/csp-report');
            expect(csp).toContain('https://staging-api.sentinel.com');
        });
        it('should return development CSP for development environment', () => {
            configService.get.mockReturnValue('development');
            const csp = cspConfig.generateCSPHeader();
            expect(csp).toContain("script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http: localhost:* 127.0.0.1:*");
            expect(csp).toContain('localhost:*');
            expect(csp).toContain('127.0.0.1:*');
            expect(csp).toContain('report-uri /api/v1/security/csp-report');
        });
        it('should return test CSP for test environment', () => {
            configService.get.mockReturnValue('test');
            const csp = cspConfig.generateCSPHeader();
            expect(csp).toContain("default-src 'self' 'unsafe-inline' 'unsafe-eval'");
            expect(csp).toContain("script-src 'self' 'unsafe-inline' 'unsafe-eval'");
            expect(csp).not.toContain('report-uri');
        });
        it('should default to development CSP for unknown environment', () => {
            configService.get.mockReturnValue('unknown');
            const csp = cspConfig.generateCSPHeader();
            expect(csp).toContain('localhost:*');
            expect(csp).toContain("'unsafe-eval'");
        });
    });
    describe('getEndpointSpecificCSP', () => {
        beforeEach(() => {
            configService.get.mockReturnValue('production');
        });
        it('should return API-only CSP for API endpoints', () => {
            const csp = cspConfig.getEndpointSpecificCSP('api');
            expect(csp).toContain("default-src 'none'");
            expect(csp).toContain("connect-src 'self'");
            expect(csp).toContain("object-src 'none'");
            expect(csp).not.toContain('script-src');
            expect(csp).not.toContain('style-src');
        });
        it('should return webhook CSP for webhook endpoints', () => {
            const csp = cspConfig.getEndpointSpecificCSP('webhook');
            expect(csp).toContain("default-src 'none'");
            expect(csp).toContain("connect-src 'self'");
            expect(csp).toContain("form-action 'self'");
        });
        it('should return health check CSP for health endpoints', () => {
            const csp = cspConfig.getEndpointSpecificCSP('health');
            expect(csp).toContain("default-src 'none'");
            expect(csp).not.toContain('connect-src');
            expect(csp).not.toContain('script-src');
        });
        it('should return default CSP for unknown endpoint types', () => {
            const defaultCsp = cspConfig.generateCSPHeader();
            const endpointCsp = cspConfig.getEndpointSpecificCSP('unknown');
            expect(endpointCsp).toBe(defaultCsp);
        });
    });
    describe('validateCSPConfig', () => {
        it('should validate valid CSP configuration', () => {
            configService.get.mockReturnValue('development');
            const isValid = cspConfig.validateCSPConfig();
            expect(isValid).toBe(true);
        });
        it('should fail validation for empty CSP', () => {
            configService.get.mockReturnValue('production');
            jest.spyOn(cspConfig, 'getProductionCSP').mockReturnValue('');
            const isValid = cspConfig.validateCSPConfig();
            expect(isValid).toBe(false);
        });
        it('should warn about unsafe-eval in production', () => {
            configService.get.mockReturnValue('production');
            jest.spyOn(cspConfig, 'getProductionCSP').mockReturnValue("default-src 'self'; script-src 'unsafe-eval'");
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            const isValid = cspConfig.validateCSPConfig();
            expect(isValid).toBe(true);
            expect(consoleSpy).toHaveBeenCalledWith('CSP: unsafe-eval detected in production CSP');
            consoleSpy.mockRestore();
        });
        it('should warn about wildcard sources without HTTPS in production', () => {
            configService.get.mockReturnValue('production');
            jest.spyOn(cspConfig, 'getProductionCSP').mockReturnValue("default-src 'self'; script-src *");
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            const isValid = cspConfig.validateCSPConfig();
            expect(isValid).toBe(true);
            expect(consoleSpy).toHaveBeenCalledWith('CSP: Wildcard sources detected without HTTPS restriction');
            consoleSpy.mockRestore();
        });
        it('should handle validation errors gracefully', () => {
            configService.get.mockImplementation(() => {
                throw new Error('Configuration error');
            });
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
            const isValid = cspConfig.validateCSPConfig();
            expect(isValid).toBe(false);
            expect(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });
    describe('getCSPConfigSummary', () => {
        it('should return configuration summary', () => {
            configService.get.mockReturnValue('production');
            const summary = cspConfig.getCSPConfigSummary();
            expect(summary.environment).toBe('production');
            expect(typeof summary.directiveCount).toBe('number');
            expect(typeof summary.hasUnsafeInline).toBe('boolean');
            expect(typeof summary.hasUnsafeEval).toBe('boolean');
            expect(typeof summary.hasReportUri).toBe('boolean');
            expect(typeof summary.length).toBe('number');
        });
        it('should detect unsafe-inline in CSP', () => {
            configService.get.mockReturnValue('development');
            const summary = cspConfig.getCSPConfigSummary();
            expect(summary.hasUnsafeInline).toBe(true);
        });
        it('should detect unsafe-eval in CSP', () => {
            configService.get.mockReturnValue('development');
            const summary = cspConfig.getCSPConfigSummary();
            expect(summary.hasUnsafeEval).toBe(true);
        });
        it('should detect report-uri in CSP', () => {
            configService.get.mockReturnValue('staging');
            const summary = cspConfig.getCSPConfigSummary();
            expect(summary.hasReportUri).toBe(true);
        });
    });
    describe('buildCSPString', () => {
        it('should build CSP string from directives object', () => {
            const directives = {
                'default-src': ["'self'"],
                'script-src': ["'self'", "'unsafe-inline'"],
                'upgrade-insecure-requests': [],
            };
            const cspString = cspConfig.buildCSPString(directives);
            expect(cspString).toBe("default-src 'self'; script-src 'self' 'unsafe-inline'; upgrade-insecure-requests");
        });
        it('should handle empty sources array', () => {
            const directives = {
                'block-all-mixed-content': [],
                'upgrade-insecure-requests': [],
            };
            const cspString = cspConfig.buildCSPString(directives);
            expect(cspString).toBe('block-all-mixed-content; upgrade-insecure-requests');
        });
        it('should handle single source', () => {
            const directives = {
                'default-src': ["'none'"],
            };
            const cspString = cspConfig.buildCSPString(directives);
            expect(cspString).toBe("default-src 'none'");
        });
    });
    describe('environment-specific configurations', () => {
        it('should have strict production configuration', () => {
            configService.get.mockReturnValue('production');
            const csp = cspConfig.generateCSPHeader();
            expect(csp).not.toContain("'unsafe-eval'");
            expect(csp).toContain('upgrade-insecure-requests');
            expect(csp).toContain('block-all-mixed-content');
            expect(csp).toContain("frame-ancestors 'none'");
        });
        it('should allow debugging in staging', () => {
            configService.get.mockReturnValue('staging');
            const csp = cspConfig.generateCSPHeader();
            expect(csp).toContain("'unsafe-eval'");
            expect(csp).toContain('staging-api.sentinel.com');
            expect(csp).toContain('report-uri');
        });
        it('should be permissive in development', () => {
            configService.get.mockReturnValue('development');
            const csp = cspConfig.generateCSPHeader();
            expect(csp).toContain('localhost:*');
            expect(csp).toContain('127.0.0.1:*');
            expect(csp).toContain('https: http:');
            expect(csp).toContain("'unsafe-eval'");
        });
        it('should be minimal in test environment', () => {
            configService.get.mockReturnValue('test');
            const csp = cspConfig.generateCSPHeader();
            expect(csp).toContain("'unsafe-inline'");
            expect(csp).toContain("'unsafe-eval'");
            expect(csp).not.toContain('localhost:*');
            expect(csp).not.toContain('report-uri');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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