{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\hsts.config.ts", "mappings": ";;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAE/C;;;GAGG;AAEI,IAAM,UAAU,GAAhB,MAAM,UAAU;IACrB,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAE7D;;OAEG;IACH,kBAAkB;QAChB,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAEtE,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,YAAY;gBACf,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAClC,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/B,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5B;gBACE,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC,CAAC,SAAS;QAClF,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAAC,CAAC;QAC1F,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;QAEtE,IAAI,SAAS,GAAG,WAAW,MAAM,EAAE,CAAC;QAEpC,IAAI,iBAAiB,EAAE,CAAC;YACtB,SAAS,IAAI,qBAAqB,CAAC;QACrC,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACZ,SAAS,IAAI,WAAW,CAAC;QAC3B,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC,CAAC,UAAU;QAClF,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAAC,CAAC;QAE1F,IAAI,SAAS,GAAG,WAAW,MAAM,EAAE,CAAC;QAEpC,IAAI,iBAAiB,EAAE,CAAC;YACtB,SAAS,IAAI,qBAAqB,CAAC;QACrC,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC,CAAC,SAAS;QAE9E,OAAO,WAAW,MAAM,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,WAAW;QACjB,OAAO,WAAW,CAAC,CAAC,wBAAwB;IAC9C,CAAC;IAED;;OAEG;IACH,aAAa;QACX,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QACtE,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAE7E,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,mDAAmD;QACnD,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,UAAkB;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE3C,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,KAAK;gBACR,sCAAsC;gBACtC,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3B,KAAK,OAAO;gBACV,6CAA6C;gBAC7C,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;YAC7B,KAAK,QAAQ;gBACX,wCAAwC;gBACxC,OAAO,QAAQ,CAAC;YAClB;gBACE,OAAO,QAAQ,CAAC;QACpB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAEtE,IAAI,WAAW,KAAK,YAAY,EAAE,CAAC;YACjC,OAAO,8CAA8C,CAAC,CAAC,UAAU;QACnE,CAAC;aAAM,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YACrC,OAAO,qCAAqC,CAAC,CAAC,WAAW;QAC3D,CAAC;aAAM,CAAC;YACN,OAAO,cAAc,CAAC,CAAC,yBAAyB;QAClD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAEtE,IAAI,WAAW,KAAK,YAAY,EAAE,CAAC;YACjC,OAAO,8CAA8C,CAAC,CAAC,UAAU;QACnE,CAAC;aAAM,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YACrC,OAAO,qCAAqC,CAAC,CAAC,SAAS;QACzD,CAAC;aAAM,CAAC;YACN,OAAO,cAAc,CAAC,CAAC,0BAA0B;QACnD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;YAEtE,mBAAmB;YACnB,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClD,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC1C,CAAC;YAED,wBAAwB;YACxB,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YACtD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC3D,CAAC;YAED,MAAM,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAE5C,0BAA0B;YAC1B,IAAI,WAAW,KAAK,YAAY,IAAI,MAAM,GAAG,QAAQ,EAAE,CAAC;gBACtD,OAAO,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;YACxF,CAAC;YAED,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACrD,CAAC;YAED,kCAAkC;YAClC,IAAI,WAAW,KAAK,YAAY,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBACpE,OAAO,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;YACzE,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACtE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,qBAAqB;QAKnB,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAEtE,MAAM,YAAY,GAAG;YACnB,2BAA2B;YAC3B,oCAAoC;YACpC,iCAAiC;YACjC,qDAAqD;YACrD,qCAAqC;YACrC,2BAA2B;SAC5B,CAAC;QAEF,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,QAAQ,GAAG,IAAI,CAAC;QAEpB,gBAAgB;QAChB,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QACtD,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAClE,QAAQ,GAAG,KAAK,CAAC;QACnB,CAAC;QAED,0BAA0B;QAC1B,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YACvD,QAAQ,GAAG,KAAK,CAAC;QACnB,CAAC;QAED,gBAAgB;QAChB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC7C,QAAQ,GAAG,KAAK,CAAC;QACnB,CAAC;QAED,oBAAoB;QACpB,IAAI,WAAW,KAAK,YAAY,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;YACvE,QAAQ,GAAG,KAAK,CAAC;QACnB,CAAC;QAED,OAAO;YACL,QAAQ;YACR,YAAY;YACZ,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QACtE,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAEtD,OAAO;YACL,WAAW;YACX,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE;YAC7B,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9E,iBAAiB,EAAE,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC;YAC3D,OAAO,EAAE,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC;YACvC,YAAY,EAAE,UAAU,CAAC,MAAM;YAC/B,eAAe,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC,QAAQ;SACvD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAc;QAC5B,6EAA6E;QAC7E,iDAAiD;QACjD,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAW,8BAA8B,EAAE,EAAE,CAAC,CAAC;QAC5F,OAAO,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;CACF,CAAA;AAjRY,gCAAU;qBAAV,UAAU;IADtB,IAAA,mBAAU,GAAE;yDAEiC,sBAAa,oBAAb,sBAAa;GAD9C,UAAU,CAiRtB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\hsts.config.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\n\r\n/**\r\n * HTTP Strict Transport Security (HSTS) configuration service\r\n * Implements HSTS policy for enforcing HTTPS connections\r\n */\r\n@Injectable()\r\nexport class HstsConfig {\r\n  constructor(private readonly configService: ConfigService) {}\r\n\r\n  /**\r\n   * Generate HSTS header value based on environment\r\n   */\r\n  generateHSTSHeader(): string {\r\n    const environment = this.configService.get('NODE_ENV', 'development');\r\n    \r\n    switch (environment) {\r\n      case 'production':\r\n        return this.getProductionHSTS();\r\n      case 'staging':\r\n        return this.getStagingHSTS();\r\n      case 'test':\r\n        return this.getTestHSTS();\r\n      default:\r\n        return this.getDevelopmentHSTS();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Production HSTS - Maximum security with preload\r\n   */\r\n  private getProductionHSTS(): string {\r\n    const maxAge = this.configService.get('security.hsts.maxAge', 31536000); // 1 year\r\n    const includeSubDomains = this.configService.get('security.hsts.includeSubDomains', true);\r\n    const preload = this.configService.get('security.hsts.preload', true);\r\n\r\n    let hstsValue = `max-age=${maxAge}`;\r\n\r\n    if (includeSubDomains) {\r\n      hstsValue += '; includeSubDomains';\r\n    }\r\n\r\n    if (preload) {\r\n      hstsValue += '; preload';\r\n    }\r\n\r\n    return hstsValue;\r\n  }\r\n\r\n  /**\r\n   * Staging HSTS - Moderate security without preload\r\n   */\r\n  private getStagingHSTS(): string {\r\n    const maxAge = this.configService.get('security.hsts.maxAge', 7776000); // 90 days\r\n    const includeSubDomains = this.configService.get('security.hsts.includeSubDomains', true);\r\n\r\n    let hstsValue = `max-age=${maxAge}`;\r\n\r\n    if (includeSubDomains) {\r\n      hstsValue += '; includeSubDomains';\r\n    }\r\n\r\n    return hstsValue;\r\n  }\r\n\r\n  /**\r\n   * Development HSTS - Short duration for development\r\n   */\r\n  private getDevelopmentHSTS(): string {\r\n    const maxAge = this.configService.get('security.hsts.maxAge', 3600); // 1 hour\r\n    \r\n    return `max-age=${maxAge}`;\r\n  }\r\n\r\n  /**\r\n   * Test HSTS - Minimal duration for testing\r\n   */\r\n  private getTestHSTS(): string {\r\n    return 'max-age=0'; // Disable HSTS in tests\r\n  }\r\n\r\n  /**\r\n   * Check if HSTS should be enabled for the current environment\r\n   */\r\n  isHSTSEnabled(): boolean {\r\n    const environment = this.configService.get('NODE_ENV', 'development');\r\n    const forceDisable = this.configService.get('security.hsts.disabled', false);\r\n\r\n    if (forceDisable) {\r\n      return false;\r\n    }\r\n\r\n    // HSTS should be enabled in production and staging\r\n    return ['production', 'staging'].includes(environment);\r\n  }\r\n\r\n  /**\r\n   * Get HSTS configuration for specific domain types\r\n   */\r\n  getDomainSpecificHSTS(domainType: string): string {\r\n    const baseHSTS = this.generateHSTSHeader();\r\n\r\n    switch (domainType) {\r\n      case 'api':\r\n        // API domains should have strict HSTS\r\n        return this.getAPIHSTS();\r\n      case 'admin':\r\n        // Admin domains should have maximum security\r\n        return this.getAdminHSTS();\r\n      case 'public':\r\n        // Public domains can have standard HSTS\r\n        return baseHSTS;\r\n      default:\r\n        return baseHSTS;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * API-specific HSTS configuration\r\n   */\r\n  private getAPIHSTS(): string {\r\n    const environment = this.configService.get('NODE_ENV', 'development');\r\n    \r\n    if (environment === 'production') {\r\n      return 'max-age=63072000; includeSubDomains; preload'; // 2 years\r\n    } else if (environment === 'staging') {\r\n      return 'max-age=15552000; includeSubDomains'; // 180 days\r\n    } else {\r\n      return 'max-age=3600'; // 1 hour for development\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Admin-specific HSTS configuration\r\n   */\r\n  private getAdminHSTS(): string {\r\n    const environment = this.configService.get('NODE_ENV', 'development');\r\n    \r\n    if (environment === 'production') {\r\n      return 'max-age=63072000; includeSubDomains; preload'; // 2 years\r\n    } else if (environment === 'staging') {\r\n      return 'max-age=31536000; includeSubDomains'; // 1 year\r\n    } else {\r\n      return 'max-age=7200'; // 2 hours for development\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate HSTS configuration\r\n   */\r\n  validateHSTSConfig(): boolean {\r\n    try {\r\n      const hstsHeader = this.generateHSTSHeader();\r\n      const environment = this.configService.get('NODE_ENV', 'development');\r\n\r\n      // Basic validation\r\n      if (!hstsHeader || hstsHeader.trim().length === 0) {\r\n        throw new Error('HSTS header is empty');\r\n      }\r\n\r\n      // Extract max-age value\r\n      const maxAgeMatch = hstsHeader.match(/max-age=(\\d+)/);\r\n      if (!maxAgeMatch) {\r\n        throw new Error('HSTS header missing max-age directive');\r\n      }\r\n\r\n      const maxAge = parseInt(maxAgeMatch[1], 10);\r\n\r\n      // Validate max-age values\r\n      if (environment === 'production' && maxAge < 31536000) {\r\n        console.warn('HSTS: Production max-age should be at least 1 year (31536000 seconds)');\r\n      }\r\n\r\n      if (maxAge < 0) {\r\n        throw new Error('HSTS max-age cannot be negative');\r\n      }\r\n\r\n      // Check for preload in production\r\n      if (environment === 'production' && !hstsHeader.includes('preload')) {\r\n        console.warn('HSTS: Consider enabling preload for production domains');\r\n      }\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('HSTS configuration validation failed:', error.message);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get HSTS preload eligibility status\r\n   */\r\n  getPreloadEligibility(): {\r\n    eligible: boolean;\r\n    requirements: string[];\r\n    issues: string[];\r\n  } {\r\n    const hstsHeader = this.generateHSTSHeader();\r\n    const environment = this.configService.get('NODE_ENV', 'development');\r\n    \r\n    const requirements = [\r\n      'Serve a valid certificate',\r\n      'Redirect all HTTP traffic to HTTPS',\r\n      'Serve all subdomains over HTTPS',\r\n      'Serve HSTS header with max-age >= 31536000 (1 year)',\r\n      'Include includeSubDomains directive',\r\n      'Include preload directive',\r\n    ];\r\n\r\n    const issues: string[] = [];\r\n    let eligible = true;\r\n\r\n    // Check max-age\r\n    const maxAgeMatch = hstsHeader.match(/max-age=(\\d+)/);\r\n    if (!maxAgeMatch || parseInt(maxAgeMatch[1], 10) < 31536000) {\r\n      issues.push('max-age must be at least 31536000 seconds (1 year)');\r\n      eligible = false;\r\n    }\r\n\r\n    // Check includeSubDomains\r\n    if (!hstsHeader.includes('includeSubDomains')) {\r\n      issues.push('includeSubDomains directive is required');\r\n      eligible = false;\r\n    }\r\n\r\n    // Check preload\r\n    if (!hstsHeader.includes('preload')) {\r\n      issues.push('preload directive is required');\r\n      eligible = false;\r\n    }\r\n\r\n    // Environment check\r\n    if (environment !== 'production') {\r\n      issues.push('Preload is only recommended for production environments');\r\n      eligible = false;\r\n    }\r\n\r\n    return {\r\n      eligible,\r\n      requirements,\r\n      issues,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get HSTS configuration summary for logging\r\n   */\r\n  getHSTSConfigSummary(): Record<string, any> {\r\n    const hstsHeader = this.generateHSTSHeader();\r\n    const environment = this.configService.get('NODE_ENV', 'development');\r\n    const maxAgeMatch = hstsHeader.match(/max-age=(\\d+)/);\r\n    \r\n    return {\r\n      environment,\r\n      enabled: this.isHSTSEnabled(),\r\n      maxAge: maxAgeMatch ? parseInt(maxAgeMatch[1], 10) : 0,\r\n      maxAgeDays: maxAgeMatch ? Math.floor(parseInt(maxAgeMatch[1], 10) / 86400) : 0,\r\n      includeSubDomains: hstsHeader.includes('includeSubDomains'),\r\n      preload: hstsHeader.includes('preload'),\r\n      headerLength: hstsHeader.length,\r\n      preloadEligible: this.getPreloadEligibility().eligible,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate HSTS removal header (for emergency use)\r\n   */\r\n  generateRemovalHeader(): string {\r\n    return 'max-age=0';\r\n  }\r\n\r\n  /**\r\n   * Check if domain is in HSTS preload list (simulation)\r\n   */\r\n  isInPreloadList(domain: string): boolean {\r\n    // In a real implementation, this would check against the actual preload list\r\n    // For now, we'll simulate based on configuration\r\n    const preloadDomains = this.configService.get<string[]>('security.hsts.preloadDomains', []);\r\n    return preloadDomains.includes(domain);\r\n  }\r\n}"], "version": 3}