{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\application\\services\\ioc.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAA4F;AAC5F,6CAAmD;AACnD,qCAA0D;AAC1D,iEAAuD;AACvD,iFAAsE;AACtE,mFAAwE;AACxE,sFAAkF;AAClF,0FAAsF;AAEtF;;;GAGG;AAEI,IAAM,UAAU,kBAAhB,MAAM,UAAU;IAGrB,YAEE,aAA+C,EAE/C,oBAA6D,EAE7D,qBAA+D,EAC9C,aAA4B,EAC5B,YAA0B;QAN1B,kBAAa,GAAb,aAAa,CAAiB;QAE9B,yBAAoB,GAApB,oBAAoB,CAAwB;QAE5C,0BAAqB,GAArB,qBAAqB,CAAyB;QAC9C,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;QAV5B,WAAM,GAAG,IAAI,eAAM,CAAC,YAAU,CAAC,IAAI,CAAC,CAAC;IAWnD,CAAC;IAEJ;;;;;OAKG;IACH,KAAK,CAAC,MAAM,CAAC,OAAqB,EAAE,SAAiB;QACnD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE;gBACpC,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC;gBACxC,SAAS;aACV,CAAC,CAAC;YAEH,2BAA2B;YAC3B,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;gBACpC,MAAM,IAAI,4BAAmB,CAAC,6BAA6B,CAAC,CAAC;YAC/D,CAAC;YAED,0BAA0B;YAC1B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAC/E,IAAI,WAAW,EAAE,CAAC;gBAChB,oDAAoD;gBACpD,WAAW,CAAC,cAAc,EAAE,CAAC;gBAC7B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAE9D,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,SAAS,EACT,iBAAiB,EACjB,KAAK,EACL,UAAU,CAAC,EAAE,EACb,EAAE,MAAM,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,UAAU,CAAC,gBAAgB,EAAE,CAClF,CAAC;gBAEF,OAAO,UAAU,CAAC;YACpB,CAAC;YAED,qBAAqB;YACrB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;gBACpC,GAAG,OAAO;gBACV,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC;gBACvD,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,GAAG;gBACnC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,GAAG;gBACjC,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,EAAE;gBACpC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,QAAQ;gBACtC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,QAAQ;gBAClC,GAAG,EAAE,OAAO,CAAC,GAAG,IAAI,OAAO;gBAC3B,WAAW,EAAE,OAAO,CAAC,WAAW,KAAK,KAAK;gBAC1C,gBAAgB,EAAE,CAAC;gBACnB,SAAS;aACV,CAAC,CAAC;YAEH,kCAAkC;YAClC,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAE1B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEpD,kBAAkB;YAClB,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,SAAS,EACT,QAAQ,EACR,KAAK,EACL,QAAQ,CAAC,EAAE,EACX;gBACE,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACzC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,UAAU,EAAE,QAAQ,CAAC,UAAU;aAChC,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,EAAE;gBAC1C,KAAK,EAAE,QAAQ,CAAC,EAAE;gBAClB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,SAAS;aACV,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;gBACxC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,SAAS;aACV,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,EAAU;QACvB,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;gBAC3C,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC,YAAY,EAAE,cAAc,CAAC;aAC1C,CAAC,CAAC;YAEH,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC1C,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBAC5C,EAAE;gBACF,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,kBAAkB,CAAC,IAAY,EAAE,KAAa;QAClD,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;gBACtC,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,eAAe,EAAE;gBACvC,SAAS,EAAE,CAAC,YAAY,EAAE,cAAc,CAAC;aAC1C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACxD,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,OAWd;QACC,IAAI,CAAC;YACH,MAAM,EACJ,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,IAAI,EACJ,QAAQ,EACR,MAAM,EACN,UAAU,EACV,YAAY,EACZ,MAAM,EACN,MAAM,GAAG,UAAU,EACnB,SAAS,GAAG,MAAM,GACnB,GAAG,OAAO,CAAC;YAEZ,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,KAAK,CAAC;iBAC9D,iBAAiB,CAAC,gBAAgB,EAAE,YAAY,CAAC;iBACjD,iBAAiB,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;YAEzD,gBAAgB;YAChB,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,YAAY,CAAC,QAAQ,CAAC,wBAAwB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;YAC5D,CAAC;YAED,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,YAAY,CAAC,QAAQ,CAAC,gCAAgC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YACxE,CAAC;YAED,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,YAAY,CAAC,QAAQ,CAAC,4BAA4B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAClE,CAAC;YAED,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,UAAU,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;oBACjC,YAAY,CAAC,QAAQ,CAAC,kCAAkC,EAAE,EAAE,aAAa,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC/F,CAAC;gBACD,IAAI,UAAU,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;oBACjC,YAAY,CAAC,QAAQ,CAAC,kCAAkC,EAAE,EAAE,aAAa,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC/F,CAAC;YACH,CAAC;YAED,IAAI,YAAY,EAAE,CAAC;gBACjB,YAAY,CAAC,QAAQ,CAAC,kCAAkC,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;YAC9E,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACX,YAAY,CAAC,QAAQ,CACnB,4DAA4D,EAC5D,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,EAAE,CAC1B,CAAC;YACJ,CAAC;YAED,gBAAgB;YAChB,YAAY,CAAC,OAAO,CAAC,OAAO,MAAM,EAAE,EAAE,SAAS,CAAC,CAAC;YAEjD,mBAAmB;YACnB,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAClC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;YAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;YAE5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE;gBAClC,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,UAAU;gBACV,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE;aACtE,CAAC,CAAC;YAEH,OAAO;gBACL,IAAI;gBACJ,KAAK;gBACL,IAAI;gBACJ,UAAU;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;gBACvC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,OAAO;aACR,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,UAAwB,EAAE,SAAiB;QAClE,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACpC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,MAAM,IAAI,0BAAiB,CAAC,eAAe,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE;gBAChC,KAAK,EAAE,EAAE;gBACT,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;gBAC/C,SAAS;aACV,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,YAAY,GAAG;gBACnB,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,QAAQ,EAAE,GAAG,CAAC,QAAQ;gBACtB,UAAU,EAAE,GAAG,CAAC,UAAU;gBAC1B,WAAW,EAAE,GAAG,CAAC,WAAW;aAC7B,CAAC;YAEF,gBAAgB;YAChB,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;YAC/B,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;YAE1B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEtD,kBAAkB;YAClB,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,SAAS,EACT,QAAQ,EACR,KAAK,EACL,EAAE,EACF;gBACE,YAAY;gBACZ,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;aAChD,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,EAAE;gBAC1C,KAAK,EAAE,EAAE;gBACT,SAAS;aACV,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;gBACxC,KAAK,EAAE,EAAE;gBACT,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS;aACV,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,SAAiB;QACxC,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACpC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,MAAM,IAAI,0BAAiB,CAAC,eAAe,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE;gBAChC,KAAK,EAAE,EAAE;gBACT,SAAS;aACV,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAErC,kBAAkB;YAClB,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,SAAS,EACT,QAAQ,EACR,KAAK,EACL,EAAE,EACF;gBACE,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC;gBACpC,QAAQ,EAAE,GAAG,CAAC,QAAQ;aACvB,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,EAAE;gBAC1C,KAAK,EAAE,EAAE;gBACT,SAAS;aACV,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;gBACxC,KAAK,EAAE,EAAE;gBACT,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS;aACV,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,UAAU,CACd,QAAwB,EACxB,SAAiB;QAEjB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBACtC,KAAK,EAAE,QAAQ,CAAC,MAAM;gBACtB,SAAS;aACV,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG;gBACd,OAAO,EAAE,EAAW;gBACpB,OAAO,EAAE,EAAW;gBACpB,MAAM,EAAE,EAAW;aACpB,CAAC;YAEF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,IAAI,CAAC;oBACH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;oBAClD,IAAI,GAAG,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;wBAC7B,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC5B,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC5B,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;wBAClB,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;wBACtC,KAAK,EAAE,KAAK,CAAC,OAAO;qBACrB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,qBAAqB;YACrB,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,SAAS,EACT,aAAa,EACb,KAAK,EACL,IAAI,EACJ;gBACE,cAAc,EAAE,QAAQ,CAAC,MAAM;gBAC/B,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM;gBAC/B,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM;gBAC/B,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM;aAC9B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,EAAE;gBAC7C,cAAc,EAAE,QAAQ,CAAC,MAAM;gBAC/B,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM;gBAC/B,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM;gBAC/B,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM;gBAC7B,SAAS;aACV,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBAC9C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,QAAQ,CAAC,MAAM;gBACtB,SAAS;aACV,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,yBAAyB,CAC7B,KAAa,EACb,cAAwB,EACxB,SAAiB;QAEjB,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACvC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,MAAM,IAAI,0BAAiB,CAAC,eAAe,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;gBAC3D,EAAE,EAAE,IAAA,YAAE,EAAC,cAAc,CAAC;aACvB,CAAC,CAAC;YAEH,IAAI,YAAY,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,EAAE,CAAC;gBAClD,MAAM,IAAI,4BAAmB,CAAC,qCAAqC,CAAC,CAAC;YACvE,CAAC;YAED,GAAG,CAAC,YAAY,GAAG,YAAY,CAAC;YAChC,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;YAE1B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEtD,kBAAkB;YAClB,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,SAAS,EACT,yBAAyB,EACzB,KAAK,EACL,KAAK,EACL;gBACE,cAAc;gBACd,gBAAgB,EAAE,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;aAClD,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,EAAE;gBACnD,KAAK;gBACL,gBAAgB,EAAE,YAAY,CAAC,MAAM;gBACrC,SAAS;aACV,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE;gBAC9D,KAAK;gBACL,cAAc;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS;aACV,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,MAAM,CACJ,KAAK,EACL,MAAM,EACN,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,GAAG,EACH,WAAW,EACX,OAAO,EACP,MAAM,EACN,IAAI,EACL,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;gBAC1B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC;gBACzD,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC;gBAC3D,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC;gBAC1D,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,CAAC;gBAC7D,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC;gBACzD,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,CAAC;gBAC3D,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC;gBACxD,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,CAAC;gBAC3D,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,CAAC;gBACvD,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,IAAA,YAAE,EAAC,CAAC,eAAe,EAAE,gBAAgB,EAAE,kBAAkB,CAAC,CAAC,EAAE,EAAE,CAAC;gBAC1G,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;aACrD,CAAC,CAAC;YAEH,OAAO;gBACL,KAAK;gBACL,QAAQ,EAAE;oBACR,MAAM;oBACN,QAAQ;oBACR,OAAO;iBACR;gBACD,UAAU,EAAE;oBACV,QAAQ;oBACR,IAAI;oBACJ,MAAM;oBACN,GAAG;iBACJ;gBACD,MAAM,EAAE;oBACN,WAAW;oBACX,OAAO;oBACP,MAAM;oBACN,IAAI;oBACJ,KAAK,EAAE,KAAK,GAAG,WAAW,GAAG,OAAO,GAAG,MAAM,GAAG,IAAI;iBACrD;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,SAAS,CAAC,GAAQ;QAC9B,IAAI,CAAC;YACH,mCAAmC;YACnC,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBAC9B,4DAA4D;gBAC5D,iCAAiC;gBACjC,GAAG,CAAC,WAAW,GAAG;oBAChB,OAAO,EAAE,SAAS;oBAClB,MAAM,EAAE,SAAS;oBACjB,IAAI,EAAE,SAAS;iBAChB,CAAC;YACJ,CAAC;YAED,gCAAgC;YAChC,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC1B,+CAA+C;gBAC/C,iCAAiC;YACnC,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBACvC,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,cAAc,CAAC,IAAY,EAAE,KAAa;QAChD,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,QAAQ,CAAC;YACd,KAAK,OAAO;gBACV,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;YACpC,KAAK,eAAe,CAAC;YACrB,KAAK,gBAAgB,CAAC;YACtB,KAAK,kBAAkB;gBACrB,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;YACpC,KAAK,YAAY;gBACf,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC;YACtB,KAAK,KAAK;gBACR,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC;YACtB;gBACE,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,aAAa,CAAC,KAAc;QAClC,IAAI,CAAC,KAAK;YAAE,OAAO,SAAS,CAAC;QAC7B,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;QACxC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACK,kBAAkB,CAAC,IAAS;QAClC,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAC9B,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;YACpB,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACK,eAAe,CAAC,IAAS;QAC/B,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAC9B,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;YACpB,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC;QACD,OAAO,SAAS,CAAC,cAAc,CAAC;QAChC,OAAO,SAAS,CAAC,QAAQ,CAAC;QAC1B,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AA3oBY,gCAAU;qBAAV,UAAU;IADtB,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,gBAAG,CAAC,CAAA;IAErB,WAAA,IAAA,0BAAgB,EAAC,+BAAU,CAAC,CAAA;IAE5B,WAAA,IAAA,0BAAgB,EAAC,iCAAW,CAAC,CAAA;yDAHE,oBAAU,oBAAV,oBAAU,oDAEH,oBAAU,oBAAV,oBAAU,oDAET,oBAAU,oBAAV,oBAAU,oDAClB,8BAAa,oBAAb,8BAAa,oDACd,4BAAY,oBAAZ,4BAAY;GAXlC,UAAU,CA2oBtB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\application\\services\\ioc.service.ts"], "sourcesContent": ["import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, FindManyOptions, In } from 'typeorm';\r\nimport { IOC } from '../../domain/entities/ioc.entity';\r\nimport { ThreatFeed } from '../../domain/entities/threat-feed.entity';\r\nimport { ThreatActor } from '../../domain/entities/threat-actor.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\n\r\n/**\r\n * IOC service that handles Indicator of Compromise management operations\r\n * Provides CRUD operations and business logic for IOCs\r\n */\r\n@Injectable()\r\nexport class IOCService {\r\n  private readonly logger = new Logger(IOCService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(IOC)\r\n    private readonly iocRepository: Repository<IOC>,\r\n    @InjectRepository(ThreatFeed)\r\n    private readonly threatFeedRepository: Repository<ThreatFeed>,\r\n    @InjectRepository(ThreatActor)\r\n    private readonly threatActorRepository: Repository<ThreatActor>,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly auditService: AuditService,\r\n  ) {}\r\n\r\n  /**\r\n   * Create a new IOC\r\n   * @param iocData IOC data\r\n   * @param createdBy User creating the IOC\r\n   * @returns Created IOC\r\n   */\r\n  async create(iocData: Partial<IOC>, createdBy: string): Promise<IOC> {\r\n    try {\r\n      this.logger.debug('Creating new IOC', {\r\n        type: iocData.type,\r\n        value: this.sanitizeValue(iocData.value),\r\n        createdBy,\r\n      });\r\n\r\n      // Validate required fields\r\n      if (!iocData.type || !iocData.value) {\r\n        throw new BadRequestException('Type and value are required');\r\n      }\r\n\r\n      // Check for duplicate IOC\r\n      const existingIOC = await this.findByTypeAndValue(iocData.type, iocData.value);\r\n      if (existingIOC) {\r\n        // Update existing IOC instead of creating duplicate\r\n        existingIOC.updateLastSeen();\r\n        const updatedIOC = await this.iocRepository.save(existingIOC);\r\n        \r\n        await this.auditService.logUserAction(\r\n          createdBy,\r\n          'update_existing',\r\n          'ioc',\r\n          updatedIOC.id,\r\n          { reason: 'duplicate_submission', observationCount: updatedIOC.observationCount },\r\n        );\r\n\r\n        return updatedIOC;\r\n      }\r\n\r\n      // Set default values\r\n      const now = new Date();\r\n      const ioc = this.iocRepository.create({\r\n        ...iocData,\r\n        value: this.normalizeValue(iocData.type, iocData.value),\r\n        firstSeen: iocData.firstSeen || now,\r\n        lastSeen: iocData.lastSeen || now,\r\n        confidence: iocData.confidence || 50,\r\n        severity: iocData.severity || 'medium',\r\n        status: iocData.status || 'active',\r\n        tlp: iocData.tlp || 'white',\r\n        isMonitored: iocData.isMonitored !== false,\r\n        observationCount: 1,\r\n        createdBy,\r\n      });\r\n\r\n      // Enrich IOC with additional data\r\n      await this.enrichIOC(ioc);\r\n\r\n      const savedIOC = await this.iocRepository.save(ioc);\r\n\r\n      // Log audit event\r\n      await this.auditService.logUserAction(\r\n        createdBy,\r\n        'create',\r\n        'ioc',\r\n        savedIOC.id,\r\n        {\r\n          type: savedIOC.type,\r\n          value: this.sanitizeValue(savedIOC.value),\r\n          severity: savedIOC.severity,\r\n          confidence: savedIOC.confidence,\r\n        },\r\n      );\r\n\r\n      this.logger.log('IOC created successfully', {\r\n        iocId: savedIOC.id,\r\n        type: savedIOC.type,\r\n        createdBy,\r\n      });\r\n\r\n      return savedIOC;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create IOC', {\r\n        error: error.message,\r\n        type: iocData.type,\r\n        createdBy,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find IOC by ID\r\n   * @param id IOC ID\r\n   * @returns IOC or null\r\n   */\r\n  async findById(id: string): Promise<IOC | null> {\r\n    try {\r\n      const ioc = await this.iocRepository.findOne({\r\n        where: { id },\r\n        relations: ['threatFeed', 'threatActors'],\r\n      });\r\n\r\n      if (!ioc) {\r\n        this.logger.warn('IOC not found', { id });\r\n        return null;\r\n      }\r\n\r\n      return ioc;\r\n    } catch (error) {\r\n      this.logger.error('Failed to find IOC by ID', {\r\n        id,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find IOC by type and value\r\n   * @param type IOC type\r\n   * @param value IOC value\r\n   * @returns IOC or null\r\n   */\r\n  async findByTypeAndValue(type: string, value: string): Promise<IOC | null> {\r\n    try {\r\n      const normalizedValue = this.normalizeValue(type, value);\r\n      return await this.iocRepository.findOne({\r\n        where: { type, value: normalizedValue },\r\n        relations: ['threatFeed', 'threatActors'],\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to find IOC by type and value', {\r\n        type,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find IOCs with pagination and filtering\r\n   * @param options Query options\r\n   * @returns Paginated IOCs\r\n   */\r\n  async findMany(options: {\r\n    page?: number;\r\n    limit?: number;\r\n    type?: string[];\r\n    severity?: string[];\r\n    status?: string[];\r\n    confidence?: { min?: number; max?: number };\r\n    threatFeedId?: string;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n  }): Promise<{ iocs: IOC[]; total: number; page: number; totalPages: number }> {\r\n    try {\r\n      const {\r\n        page = 1,\r\n        limit = 20,\r\n        type,\r\n        severity,\r\n        status,\r\n        confidence,\r\n        threatFeedId,\r\n        search,\r\n        sortBy = 'lastSeen',\r\n        sortOrder = 'DESC',\r\n      } = options;\r\n\r\n      const queryBuilder = this.iocRepository.createQueryBuilder('ioc')\r\n        .leftJoinAndSelect('ioc.threatFeed', 'threatFeed')\r\n        .leftJoinAndSelect('ioc.threatActors', 'threatActors');\r\n\r\n      // Apply filters\r\n      if (type && type.length > 0) {\r\n        queryBuilder.andWhere('ioc.type IN (:...type)', { type });\r\n      }\r\n\r\n      if (severity && severity.length > 0) {\r\n        queryBuilder.andWhere('ioc.severity IN (:...severity)', { severity });\r\n      }\r\n\r\n      if (status && status.length > 0) {\r\n        queryBuilder.andWhere('ioc.status IN (:...status)', { status });\r\n      }\r\n\r\n      if (confidence) {\r\n        if (confidence.min !== undefined) {\r\n          queryBuilder.andWhere('ioc.confidence >= :minConfidence', { minConfidence: confidence.min });\r\n        }\r\n        if (confidence.max !== undefined) {\r\n          queryBuilder.andWhere('ioc.confidence <= :maxConfidence', { maxConfidence: confidence.max });\r\n        }\r\n      }\r\n\r\n      if (threatFeedId) {\r\n        queryBuilder.andWhere('ioc.threatFeedId = :threatFeedId', { threatFeedId });\r\n      }\r\n\r\n      if (search) {\r\n        queryBuilder.andWhere(\r\n          '(ioc.value ILIKE :search OR ioc.description ILIKE :search)',\r\n          { search: `%${search}%` },\r\n        );\r\n      }\r\n\r\n      // Apply sorting\r\n      queryBuilder.orderBy(`ioc.${sortBy}`, sortOrder);\r\n\r\n      // Apply pagination\r\n      const offset = (page - 1) * limit;\r\n      queryBuilder.skip(offset).take(limit);\r\n\r\n      const [iocs, total] = await queryBuilder.getManyAndCount();\r\n      const totalPages = Math.ceil(total / limit);\r\n\r\n      this.logger.debug('IOCs retrieved', {\r\n        total,\r\n        page,\r\n        limit,\r\n        totalPages,\r\n        filters: { type, severity, status, confidence, threatFeedId, search },\r\n      });\r\n\r\n      return {\r\n        iocs,\r\n        total,\r\n        page,\r\n        totalPages,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to find IOCs', {\r\n        error: error.message,\r\n        options,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update IOC\r\n   * @param id IOC ID\r\n   * @param updateData Update data\r\n   * @param updatedBy User performing the update\r\n   * @returns Updated IOC\r\n   */\r\n  async update(id: string, updateData: Partial<IOC>, updatedBy: string): Promise<IOC> {\r\n    try {\r\n      const ioc = await this.findById(id);\r\n      if (!ioc) {\r\n        throw new NotFoundException('IOC not found');\r\n      }\r\n\r\n      this.logger.debug('Updating IOC', {\r\n        iocId: id,\r\n        updateData: this.sanitizeUpdateData(updateData),\r\n        updatedBy,\r\n      });\r\n\r\n      // Store original data for audit\r\n      const originalData = {\r\n        status: ioc.status,\r\n        severity: ioc.severity,\r\n        confidence: ioc.confidence,\r\n        isMonitored: ioc.isMonitored,\r\n      };\r\n\r\n      // Update fields\r\n      Object.assign(ioc, updateData);\r\n      ioc.updatedBy = updatedBy;\r\n\r\n      const updatedIOC = await this.iocRepository.save(ioc);\r\n\r\n      // Log audit event\r\n      await this.auditService.logUserAction(\r\n        updatedBy,\r\n        'update',\r\n        'ioc',\r\n        id,\r\n        {\r\n          originalData,\r\n          updateData: this.sanitizeUpdateData(updateData),\r\n        },\r\n      );\r\n\r\n      this.logger.log('IOC updated successfully', {\r\n        iocId: id,\r\n        updatedBy,\r\n      });\r\n\r\n      return updatedIOC;\r\n    } catch (error) {\r\n      this.logger.error('Failed to update IOC', {\r\n        iocId: id,\r\n        error: error.message,\r\n        updatedBy,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete IOC\r\n   * @param id IOC ID\r\n   * @param deletedBy User performing the deletion\r\n   */\r\n  async delete(id: string, deletedBy: string): Promise<void> {\r\n    try {\r\n      const ioc = await this.findById(id);\r\n      if (!ioc) {\r\n        throw new NotFoundException('IOC not found');\r\n      }\r\n\r\n      this.logger.debug('Deleting IOC', {\r\n        iocId: id,\r\n        deletedBy,\r\n      });\r\n\r\n      await this.iocRepository.remove(ioc);\r\n\r\n      // Log audit event\r\n      await this.auditService.logUserAction(\r\n        deletedBy,\r\n        'delete',\r\n        'ioc',\r\n        id,\r\n        {\r\n          type: ioc.type,\r\n          value: this.sanitizeValue(ioc.value),\r\n          severity: ioc.severity,\r\n        },\r\n      );\r\n\r\n      this.logger.log('IOC deleted successfully', {\r\n        iocId: id,\r\n        deletedBy,\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to delete IOC', {\r\n        iocId: id,\r\n        error: error.message,\r\n        deletedBy,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Bulk create IOCs\r\n   * @param iocsData Array of IOC data\r\n   * @param createdBy User creating the IOCs\r\n   * @returns Creation results\r\n   */\r\n  async bulkCreate(\r\n    iocsData: Partial<IOC>[],\r\n    createdBy: string,\r\n  ): Promise<{ created: IOC[]; updated: IOC[]; errors: any[] }> {\r\n    try {\r\n      this.logger.debug('Bulk creating IOCs', {\r\n        count: iocsData.length,\r\n        createdBy,\r\n      });\r\n\r\n      const results = {\r\n        created: [] as IOC[],\r\n        updated: [] as IOC[],\r\n        errors: [] as any[],\r\n      };\r\n\r\n      for (const iocData of iocsData) {\r\n        try {\r\n          const ioc = await this.create(iocData, createdBy);\r\n          if (ioc.observationCount > 1) {\r\n            results.updated.push(ioc);\r\n          } else {\r\n            results.created.push(ioc);\r\n          }\r\n        } catch (error) {\r\n          results.errors.push({\r\n            iocData: this.sanitizeIOCData(iocData),\r\n            error: error.message,\r\n          });\r\n        }\r\n      }\r\n\r\n      // Log bulk operation\r\n      await this.auditService.logUserAction(\r\n        createdBy,\r\n        'bulk_create',\r\n        'ioc',\r\n        null,\r\n        {\r\n          totalRequested: iocsData.length,\r\n          created: results.created.length,\r\n          updated: results.updated.length,\r\n          errors: results.errors.length,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Bulk IOC creation completed', {\r\n        totalRequested: iocsData.length,\r\n        created: results.created.length,\r\n        updated: results.updated.length,\r\n        errors: results.errors.length,\r\n        createdBy,\r\n      });\r\n\r\n      return results;\r\n    } catch (error) {\r\n      this.logger.error('Failed to bulk create IOCs', {\r\n        error: error.message,\r\n        count: iocsData.length,\r\n        createdBy,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Associate IOC with threat actors\r\n   * @param iocId IOC ID\r\n   * @param threatActorIds Array of threat actor IDs\r\n   * @param updatedBy User performing the association\r\n   * @returns Updated IOC\r\n   */\r\n  async associateWithThreatActors(\r\n    iocId: string,\r\n    threatActorIds: string[],\r\n    updatedBy: string,\r\n  ): Promise<IOC> {\r\n    try {\r\n      const ioc = await this.findById(iocId);\r\n      if (!ioc) {\r\n        throw new NotFoundException('IOC not found');\r\n      }\r\n\r\n      const threatActors = await this.threatActorRepository.findBy({\r\n        id: In(threatActorIds),\r\n      });\r\n\r\n      if (threatActors.length !== threatActorIds.length) {\r\n        throw new BadRequestException('One or more threat actors not found');\r\n      }\r\n\r\n      ioc.threatActors = threatActors;\r\n      ioc.updatedBy = updatedBy;\r\n\r\n      const updatedIOC = await this.iocRepository.save(ioc);\r\n\r\n      // Log audit event\r\n      await this.auditService.logUserAction(\r\n        updatedBy,\r\n        'associate_threat_actors',\r\n        'ioc',\r\n        iocId,\r\n        {\r\n          threatActorIds,\r\n          threatActorNames: threatActors.map(ta => ta.name),\r\n        },\r\n      );\r\n\r\n      this.logger.log('IOC associated with threat actors', {\r\n        iocId,\r\n        threatActorCount: threatActors.length,\r\n        updatedBy,\r\n      });\r\n\r\n      return updatedIOC;\r\n    } catch (error) {\r\n      this.logger.error('Failed to associate IOC with threat actors', {\r\n        iocId,\r\n        threatActorIds,\r\n        error: error.message,\r\n        updatedBy,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get IOC statistics\r\n   * @returns IOC statistics\r\n   */\r\n  async getStatistics(): Promise<any> {\r\n    try {\r\n      const [\r\n        total,\r\n        active,\r\n        inactive,\r\n        expired,\r\n        critical,\r\n        high,\r\n        medium,\r\n        low,\r\n        ipAddresses,\r\n        domains,\r\n        hashes,\r\n        urls,\r\n      ] = await Promise.all([\r\n        this.iocRepository.count(),\r\n        this.iocRepository.count({ where: { status: 'active' } }),\r\n        this.iocRepository.count({ where: { status: 'inactive' } }),\r\n        this.iocRepository.count({ where: { status: 'expired' } }),\r\n        this.iocRepository.count({ where: { severity: 'critical' } }),\r\n        this.iocRepository.count({ where: { severity: 'high' } }),\r\n        this.iocRepository.count({ where: { severity: 'medium' } }),\r\n        this.iocRepository.count({ where: { severity: 'low' } }),\r\n        this.iocRepository.count({ where: { type: 'ip_address' } }),\r\n        this.iocRepository.count({ where: { type: 'domain' } }),\r\n        this.iocRepository.count({ where: { type: In(['file_hash_md5', 'file_hash_sha1', 'file_hash_sha256']) } }),\r\n        this.iocRepository.count({ where: { type: 'url' } }),\r\n      ]);\r\n\r\n      return {\r\n        total,\r\n        byStatus: {\r\n          active,\r\n          inactive,\r\n          expired,\r\n        },\r\n        bySeverity: {\r\n          critical,\r\n          high,\r\n          medium,\r\n          low,\r\n        },\r\n        byType: {\r\n          ipAddresses,\r\n          domains,\r\n          hashes,\r\n          urls,\r\n          other: total - ipAddresses - domains - hashes - urls,\r\n        },\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to get IOC statistics', {\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Enrich IOC with additional data\r\n   * @param ioc IOC to enrich\r\n   */\r\n  private async enrichIOC(ioc: IOC): Promise<void> {\r\n    try {\r\n      // Add geolocation for IP addresses\r\n      if (ioc.type === 'ip_address') {\r\n        // This would typically call an external geolocation service\r\n        // For now, this is a placeholder\r\n        ioc.geolocation = {\r\n          country: 'Unknown',\r\n          region: 'Unknown',\r\n          city: 'Unknown',\r\n        };\r\n      }\r\n\r\n      // Add context based on IOC type\r\n      if (ioc.type === 'domain') {\r\n        // This would typically perform domain analysis\r\n        // For now, this is a placeholder\r\n      }\r\n    } catch (error) {\r\n      this.logger.warn('Failed to enrich IOC', {\r\n        iocId: ioc.id,\r\n        error: error.message,\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Normalize IOC value based on type\r\n   * @param type IOC type\r\n   * @param value IOC value\r\n   * @returns Normalized value\r\n   */\r\n  private normalizeValue(type: string, value: string): string {\r\n    switch (type) {\r\n      case 'domain':\r\n      case 'email':\r\n        return value.toLowerCase().trim();\r\n      case 'file_hash_md5':\r\n      case 'file_hash_sha1':\r\n      case 'file_hash_sha256':\r\n        return value.toLowerCase().trim();\r\n      case 'ip_address':\r\n        return value.trim();\r\n      case 'url':\r\n        return value.trim();\r\n      default:\r\n        return value.trim();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sanitize IOC value for logging\r\n   * @param value IOC value\r\n   * @returns Sanitized value\r\n   */\r\n  private sanitizeValue(value?: string): string {\r\n    if (!value) return '[EMPTY]';\r\n    if (value.length > 50) {\r\n      return value.substring(0, 47) + '...';\r\n    }\r\n    return value;\r\n  }\r\n\r\n  /**\r\n   * Sanitize update data for logging\r\n   * @param data Update data\r\n   * @returns Sanitized data\r\n   */\r\n  private sanitizeUpdateData(data: any): any {\r\n    const sanitized = { ...data };\r\n    if (sanitized.value) {\r\n      sanitized.value = this.sanitizeValue(sanitized.value);\r\n    }\r\n    return sanitized;\r\n  }\r\n\r\n  /**\r\n   * Sanitize IOC data for logging\r\n   * @param data IOC data\r\n   * @returns Sanitized data\r\n   */\r\n  private sanitizeIOCData(data: any): any {\r\n    const sanitized = { ...data };\r\n    if (sanitized.value) {\r\n      sanitized.value = this.sanitizeValue(sanitized.value);\r\n    }\r\n    delete sanitized.enrichmentData;\r\n    delete sanitized.metadata;\r\n    return sanitized;\r\n  }\r\n}\r\n"], "version": 3}