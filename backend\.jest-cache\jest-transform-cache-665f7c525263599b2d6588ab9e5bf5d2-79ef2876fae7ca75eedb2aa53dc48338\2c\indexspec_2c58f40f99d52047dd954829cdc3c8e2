418437fe0a4959e878754d0c1c49333a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const exceptions_1 = require("../../exceptions");
describe('Exception Index', () => {
    describe('exports', () => {
        it('should export all exception classes', () => {
            expect(exceptions_1.DomainException).toBeDefined();
            expect(exceptions_1.ValidationException).toBeDefined();
            expect(exceptions_1.NotFoundException).toBeDefined();
            expect(exceptions_1.UnauthorizedException).toBeDefined();
            expect(exceptions_1.ForbiddenException).toBeDefined();
            expect(exceptions_1.ConflictException).toBeDefined();
            expect(exceptions_1.RateLimitException).toBeDefined();
            expect(exceptions_1.ServiceUnavailableException).toBeDefined();
        });
        it('should export exception categories', () => {
            expect(exceptions_1.ExceptionCategories).toBeDefined();
            expect(exceptions_1.ExceptionCategories.DOMAIN).toContain('BusinessRuleViolationException');
            expect(exceptions_1.ExceptionCategories.RESOURCE).toContain('NotFoundException');
            expect(exceptions_1.ExceptionCategories.VALIDATION).toContain('ValidationException');
            expect(exceptions_1.ExceptionCategories.SECURITY).toContain('UnauthorizedException');
            expect(exceptions_1.ExceptionCategories.CONFLICT).toContain('ConflictException');
            expect(exceptions_1.ExceptionCategories.AVAILABILITY).toContain('RateLimitException');
        });
        it('should export exception severity levels', () => {
            expect(exceptions_1.ExceptionSeverity).toBeDefined();
            expect(exceptions_1.ExceptionSeverity.LOW).toBe('low');
            expect(exceptions_1.ExceptionSeverity.MEDIUM).toBe('medium');
            expect(exceptions_1.ExceptionSeverity.HIGH).toBe('high');
            expect(exceptions_1.ExceptionSeverity.CRITICAL).toBe('critical');
        });
    });
    describe('helper functions', () => {
        describe('isDomainException', () => {
            it('should return true for domain exceptions', () => {
                const exception = new exceptions_1.ValidationException('Test error', []);
                expect((0, exceptions_1.isDomainException)(exception)).toBe(true);
            });
            it('should return false for regular errors', () => {
                const error = new Error('Regular error');
                expect((0, exceptions_1.isDomainException)(error)).toBe(false);
            });
            it('should return false for non-error values', () => {
                expect((0, exceptions_1.isDomainException)('string')).toBe(false);
                expect((0, exceptions_1.isDomainException)(null)).toBe(false);
                expect((0, exceptions_1.isDomainException)(undefined)).toBe(false);
                expect((0, exceptions_1.isDomainException)({})).toBe(false);
            });
        });
        describe('isValidationException', () => {
            it('should return true for validation exceptions', () => {
                const exception = exceptions_1.ValidationException.single('field', 'value', 'required', 'Field is required');
                expect((0, exceptions_1.isValidationException)(exception)).toBe(true);
            });
            it('should return false for other domain exceptions', () => {
                const exception = exceptions_1.NotFoundException.entity('User', 'user-123');
                expect((0, exceptions_1.isValidationException)(exception)).toBe(false);
            });
            it('should return false for non-exceptions', () => {
                expect((0, exceptions_1.isValidationException)('string')).toBe(false);
                expect((0, exceptions_1.isValidationException)(new Error())).toBe(false);
            });
        });
        describe('isNotFoundException', () => {
            it('should return true for not found exceptions', () => {
                const exception = exceptions_1.NotFoundException.entity('User', 'user-123');
                expect((0, exceptions_1.isNotFoundException)(exception)).toBe(true);
            });
            it('should return false for other domain exceptions', () => {
                const exception = exceptions_1.ValidationException.single('field', 'value', 'required', 'Field is required');
                expect((0, exceptions_1.isNotFoundException)(exception)).toBe(false);
            });
        });
        describe('isAuthorizationException', () => {
            it('should return true for unauthorized exceptions', () => {
                const exception = exceptions_1.UnauthorizedException.missingAuthentication();
                expect((0, exceptions_1.isAuthorizationException)(exception)).toBe(true);
            });
            it('should return true for forbidden exceptions', () => {
                const exception = exceptions_1.ForbiddenException.insufficientPermissions('User', 'delete', ['user:delete']);
                expect((0, exceptions_1.isAuthorizationException)(exception)).toBe(true);
            });
            it('should return false for other domain exceptions', () => {
                const exception = exceptions_1.NotFoundException.entity('User', 'user-123');
                expect((0, exceptions_1.isAuthorizationException)(exception)).toBe(false);
            });
        });
        describe('isConflictException', () => {
            it('should return true for conflict exceptions', () => {
                const exception = exceptions_1.ConflictException.duplicateResource('User', 'email', '<EMAIL>');
                expect((0, exceptions_1.isConflictException)(exception)).toBe(true);
            });
            it('should return false for other domain exceptions', () => {
                const exception = exceptions_1.NotFoundException.entity('User', 'user-123');
                expect((0, exceptions_1.isConflictException)(exception)).toBe(false);
            });
        });
        describe('isRateLimitException', () => {
            it('should return true for rate limit exceptions', () => {
                const exception = exceptions_1.RateLimitException.apiRequestLimit(100, 105, new Date());
                expect((0, exceptions_1.isRateLimitException)(exception)).toBe(true);
            });
            it('should return false for other domain exceptions', () => {
                const exception = exceptions_1.NotFoundException.entity('User', 'user-123');
                expect((0, exceptions_1.isRateLimitException)(exception)).toBe(false);
            });
        });
        describe('isServiceUnavailableException', () => {
            it('should return true for service unavailable exceptions', () => {
                const exception = exceptions_1.ServiceUnavailableException.overload('UserService');
                expect((0, exceptions_1.isServiceUnavailableException)(exception)).toBe(true);
            });
            it('should return false for other domain exceptions', () => {
                const exception = exceptions_1.NotFoundException.entity('User', 'user-123');
                expect((0, exceptions_1.isServiceUnavailableException)(exception)).toBe(false);
            });
        });
        describe('getHttpStatusCode', () => {
            it('should return correct status codes for different exception types', () => {
                const validationException = exceptions_1.ValidationException.single('field', 'value', 'required', 'Required');
                const unauthorizedException = exceptions_1.UnauthorizedException.missingAuthentication();
                const forbiddenException = exceptions_1.ForbiddenException.insufficientPermissions('User', 'delete', ['user:delete']);
                const notFoundException = exceptions_1.NotFoundException.entity('User', 'user-123');
                const conflictException = exceptions_1.ConflictException.duplicateResource('User', 'email', '<EMAIL>');
                const rateLimitException = exceptions_1.RateLimitException.apiRequestLimit(100, 105, new Date());
                const serviceUnavailableException = exceptions_1.ServiceUnavailableException.overload('UserService');
                expect((0, exceptions_1.getHttpStatusCode)(validationException)).toBe(400);
                expect((0, exceptions_1.getHttpStatusCode)(unauthorizedException)).toBe(401);
                expect((0, exceptions_1.getHttpStatusCode)(forbiddenException)).toBe(403);
                expect((0, exceptions_1.getHttpStatusCode)(notFoundException)).toBe(404);
                expect((0, exceptions_1.getHttpStatusCode)(conflictException)).toBe(409);
                expect((0, exceptions_1.getHttpStatusCode)(rateLimitException)).toBe(429);
                expect((0, exceptions_1.getHttpStatusCode)(serviceUnavailableException)).toBe(503);
            });
            it('should return 400 for unknown domain exceptions', () => {
                // Create a custom domain exception that doesn't match specific types
                class CustomDomainException extends exceptions_1.DomainException {
                    constructor() {
                        super('Custom error', 'CUSTOM_ERROR');
                    }
                }
                const customException = new CustomDomainException();
                expect((0, exceptions_1.getHttpStatusCode)(customException)).toBe(400);
            });
        });
        describe('toApiErrorResponse', () => {
            it('should use exception toApiResponse method when available', () => {
                const exception = exceptions_1.ValidationException.single('email', 'invalid', 'format', 'Invalid email format');
                const response = (0, exceptions_1.toApiErrorResponse)(exception);
                expect(response).toHaveProperty('error');
                expect(response).toHaveProperty('code', 'VALIDATION_ERROR');
                expect(response).toHaveProperty('details');
                expect(response.details).toHaveProperty('field', 'email');
            });
            it('should provide fallback response for exceptions without toApiResponse', () => {
                // Create a basic domain exception without toApiResponse method
                class BasicDomainException extends exceptions_1.DomainException {
                    constructor() {
                        super('Basic error', 'BASIC_ERROR', {
                            severity: 'low',
                            category: 'test',
                        });
                    }
                }
                const exception = new BasicDomainException();
                const response = (0, exceptions_1.toApiErrorResponse)(exception);
                expect(response).toEqual({
                    error: exception.getUserMessage(),
                    code: 'BASIC_ERROR',
                    details: {
                        category: 'test',
                        severity: 'low',
                        timestamp: exception.timestamp.toISOString(),
                        correlationId: exception.correlationId,
                    },
                });
            });
        });
        describe('sanitizeExceptionForLogging', () => {
            it('should sanitize sensitive information from exception context', () => {
                const exception = new exceptions_1.ValidationException('Test error', [], {
                    context: {
                        password: 'secret123',
                        token: 'jwt-token',
                        email: '<EMAIL>',
                        userId: 'user-123',
                    },
                });
                const sanitized = (0, exceptions_1.sanitizeExceptionForLogging)(exception);
                expect(sanitized.context.password).toBe('[REDACTED]');
                expect(sanitized.context.token).toBe('[REDACTED]');
                expect(sanitized.context.email).toBe('<EMAIL>');
                expect(sanitized.context.userId).toBe('user-123');
            });
            it('should use custom sensitive keys', () => {
                const exception = new exceptions_1.ValidationException('Test error', [], {
                    context: {
                        apiKey: 'api-key-123',
                        email: '<EMAIL>',
                        customSecret: 'secret-value',
                    },
                });
                const sanitized = (0, exceptions_1.sanitizeExceptionForLogging)(exception, ['apiKey', 'customSecret']);
                expect(sanitized.context.apiKey).toBe('[REDACTED]');
                expect(sanitized.context.customSecret).toBe('[REDACTED]');
                expect(sanitized.context.email).toBe('<EMAIL>');
            });
            it('should handle exceptions without sensitive data', () => {
                const exception = new exceptions_1.ValidationException('Test error', [], {
                    context: {
                        userId: 'user-123',
                        operation: 'create',
                    },
                });
                const sanitized = (0, exceptions_1.sanitizeExceptionForLogging)(exception);
                expect(sanitized.context.userId).toBe('user-123');
                expect(sanitized.context.operation).toBe('create');
            });
        });
    });
    describe('integration', () => {
        it('should work with all exception types in a unified way', () => {
            const exceptions = [
                exceptions_1.ValidationException.single('field', 'value', 'required', 'Required'),
                exceptions_1.NotFoundException.entity('User', 'user-123'),
                exceptions_1.UnauthorizedException.missingAuthentication(),
                exceptions_1.ForbiddenException.insufficientPermissions('User', 'delete', ['user:delete']),
                exceptions_1.ConflictException.duplicateResource('User', 'email', '<EMAIL>'),
                exceptions_1.RateLimitException.apiRequestLimit(100, 105, new Date()),
                exceptions_1.ServiceUnavailableException.overload('UserService'),
            ];
            exceptions.forEach(exception => {
                expect((0, exceptions_1.isDomainException)(exception)).toBe(true);
                expect((0, exceptions_1.getHttpStatusCode)(exception)).toBeGreaterThanOrEqual(400);
                expect((0, exceptions_1.getHttpStatusCode)(exception)).toBeLessThan(600);
                const apiResponse = (0, exceptions_1.toApiErrorResponse)(exception);
                expect(apiResponse).toHaveProperty('error');
                expect(apiResponse).toHaveProperty('code');
                const sanitized = (0, exceptions_1.sanitizeExceptionForLogging)(exception);
                expect(sanitized).toHaveProperty('name');
                expect(sanitized).toHaveProperty('message');
                expect(sanitized).toHaveProperty('code');
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxzaGFyZWQta2VybmVsXFxfX3Rlc3RzX19cXGV4Y2VwdGlvbnNcXGluZGV4LnNwZWMudHMiLCJtYXBwaW5ncyI6Ijs7QUFBQSxpREFxQjBCO0FBRTFCLFFBQVEsQ0FBQyxpQkFBaUIsRUFBRSxHQUFHLEVBQUU7SUFDL0IsUUFBUSxDQUFDLFNBQVMsRUFBRSxHQUFHLEVBQUU7UUFDdkIsRUFBRSxDQUFDLHFDQUFxQyxFQUFFLEdBQUcsRUFBRTtZQUM3QyxNQUFNLENBQUMsNEJBQWUsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3RDLE1BQU0sQ0FBQyxnQ0FBbUIsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQzFDLE1BQU0sQ0FBQyw4QkFBaUIsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3hDLE1BQU0sQ0FBQyxrQ0FBcUIsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQzVDLE1BQU0sQ0FBQywrQkFBa0IsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3pDLE1BQU0sQ0FBQyw4QkFBaUIsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3hDLE1BQU0sQ0FBQywrQkFBa0IsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3pDLE1BQU0sQ0FBQyx3Q0FBMkIsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1FBQ3BELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLG9DQUFvQyxFQUFFLEdBQUcsRUFBRTtZQUM1QyxNQUFNLENBQUMsZ0NBQW1CLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUMxQyxNQUFNLENBQUMsZ0NBQW1CLENBQUMsTUFBTSxDQUFDLENBQUMsU0FBUyxDQUFDLGdDQUFnQyxDQUFDLENBQUM7WUFDL0UsTUFBTSxDQUFDLGdDQUFtQixDQUFDLFFBQVEsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDO1lBQ3BFLE1BQU0sQ0FBQyxnQ0FBbUIsQ0FBQyxVQUFVLENBQUMsQ0FBQyxTQUFTLENBQUMscUJBQXFCLENBQUMsQ0FBQztZQUN4RSxNQUFNLENBQUMsZ0NBQW1CLENBQUMsUUFBUSxDQUFDLENBQUMsU0FBUyxDQUFDLHVCQUF1QixDQUFDLENBQUM7WUFDeEUsTUFBTSxDQUFDLGdDQUFtQixDQUFDLFFBQVEsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDO1lBQ3BFLE1BQU0sQ0FBQyxnQ0FBbUIsQ0FBQyxZQUFZLENBQUMsQ0FBQyxTQUFTLENBQUMsb0JBQW9CLENBQUMsQ0FBQztRQUMzRSxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx5Q0FBeUMsRUFBRSxHQUFHLEVBQUU7WUFDakQsTUFBTSxDQUFDLDhCQUFpQixDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7WUFDeEMsTUFBTSxDQUFDLDhCQUFpQixDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUMxQyxNQUFNLENBQUMsOEJBQWlCLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ2hELE1BQU0sQ0FBQyw4QkFBaUIsQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDNUMsTUFBTSxDQUFDLDhCQUFpQixDQUFDLFFBQVEsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUN0RCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGtCQUFrQixFQUFFLEdBQUcsRUFBRTtRQUNoQyxRQUFRLENBQUMsbUJBQW1CLEVBQUUsR0FBRyxFQUFFO1lBQ2pDLEVBQUUsQ0FBQywwQ0FBMEMsRUFBRSxHQUFHLEVBQUU7Z0JBQ2xELE1BQU0sU0FBUyxHQUFHLElBQUksZ0NBQW1CLENBQUMsWUFBWSxFQUFFLEVBQUUsQ0FBQyxDQUFDO2dCQUM1RCxNQUFNLENBQUMsSUFBQSw4QkFBaUIsRUFBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUNsRCxDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyx3Q0FBd0MsRUFBRSxHQUFHLEVBQUU7Z0JBQ2hELE1BQU0sS0FBSyxHQUFHLElBQUksS0FBSyxDQUFDLGVBQWUsQ0FBQyxDQUFDO2dCQUN6QyxNQUFNLENBQUMsSUFBQSw4QkFBaUIsRUFBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUMvQyxDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQywwQ0FBMEMsRUFBRSxHQUFHLEVBQUU7Z0JBQ2xELE1BQU0sQ0FBQyxJQUFBLDhCQUFpQixFQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO2dCQUNoRCxNQUFNLENBQUMsSUFBQSw4QkFBaUIsRUFBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztnQkFDNUMsTUFBTSxDQUFDLElBQUEsOEJBQWlCLEVBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7Z0JBQ2pELE1BQU0sQ0FBQyxJQUFBLDhCQUFpQixFQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzVDLENBQUMsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxRQUFRLENBQUMsdUJBQXVCLEVBQUUsR0FBRyxFQUFFO1lBQ3JDLEVBQUUsQ0FBQyw4Q0FBOEMsRUFBRSxHQUFHLEVBQUU7Z0JBQ3RELE1BQU0sU0FBUyxHQUFHLGdDQUFtQixDQUFDLE1BQU0sQ0FBQyxPQUFPLEVBQUUsT0FBTyxFQUFFLFVBQVUsRUFBRSxtQkFBbUIsQ0FBQyxDQUFDO2dCQUNoRyxNQUFNLENBQUMsSUFBQSxrQ0FBcUIsRUFBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUN0RCxDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyxpREFBaUQsRUFBRSxHQUFHLEVBQUU7Z0JBQ3pELE1BQU0sU0FBUyxHQUFHLDhCQUFpQixDQUFDLE1BQU0sQ0FBQyxNQUFNLEVBQUUsVUFBVSxDQUFDLENBQUM7Z0JBQy9ELE1BQU0sQ0FBQyxJQUFBLGtDQUFxQixFQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQ3ZELENBQUMsQ0FBQyxDQUFDO1lBRUgsRUFBRSxDQUFDLHdDQUF3QyxFQUFFLEdBQUcsRUFBRTtnQkFDaEQsTUFBTSxDQUFDLElBQUEsa0NBQXFCLEVBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7Z0JBQ3BELE1BQU0sQ0FBQyxJQUFBLGtDQUFxQixFQUFDLElBQUksS0FBSyxFQUFFLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUN6RCxDQUFDLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO1FBRUgsUUFBUSxDQUFDLHFCQUFxQixFQUFFLEdBQUcsRUFBRTtZQUNuQyxFQUFFLENBQUMsNkNBQTZDLEVBQUUsR0FBRyxFQUFFO2dCQUNyRCxNQUFNLFNBQVMsR0FBRyw4QkFBaUIsQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLFVBQVUsQ0FBQyxDQUFDO2dCQUMvRCxNQUFNLENBQUMsSUFBQSxnQ0FBbUIsRUFBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUNwRCxDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyxpREFBaUQsRUFBRSxHQUFHLEVBQUU7Z0JBQ3pELE1BQU0sU0FBUyxHQUFHLGdDQUFtQixDQUFDLE1BQU0sQ0FBQyxPQUFPLEVBQUUsT0FBTyxFQUFFLFVBQVUsRUFBRSxtQkFBbUIsQ0FBQyxDQUFDO2dCQUNoRyxNQUFNLENBQUMsSUFBQSxnQ0FBbUIsRUFBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUNyRCxDQUFDLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO1FBRUgsUUFBUSxDQUFDLDBCQUEwQixFQUFFLEdBQUcsRUFBRTtZQUN4QyxFQUFFLENBQUMsZ0RBQWdELEVBQUUsR0FBRyxFQUFFO2dCQUN4RCxNQUFNLFNBQVMsR0FBRyxrQ0FBcUIsQ0FBQyxxQkFBcUIsRUFBRSxDQUFDO2dCQUNoRSxNQUFNLENBQUMsSUFBQSxxQ0FBd0IsRUFBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUN6RCxDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyw2Q0FBNkMsRUFBRSxHQUFHLEVBQUU7Z0JBQ3JELE1BQU0sU0FBUyxHQUFHLCtCQUFrQixDQUFDLHVCQUF1QixDQUFDLE1BQU0sRUFBRSxRQUFRLEVBQUUsQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDO2dCQUNoRyxNQUFNLENBQUMsSUFBQSxxQ0FBd0IsRUFBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUN6RCxDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyxpREFBaUQsRUFBRSxHQUFHLEVBQUU7Z0JBQ3pELE1BQU0sU0FBUyxHQUFHLDhCQUFpQixDQUFDLE1BQU0sQ0FBQyxNQUFNLEVBQUUsVUFBVSxDQUFDLENBQUM7Z0JBQy9ELE1BQU0sQ0FBQyxJQUFBLHFDQUF3QixFQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzFELENBQUMsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxRQUFRLENBQUMscUJBQXFCLEVBQUUsR0FBRyxFQUFFO1lBQ25DLEVBQUUsQ0FBQyw0Q0FBNEMsRUFBRSxHQUFHLEVBQUU7Z0JBQ3BELE1BQU0sU0FBUyxHQUFHLDhCQUFpQixDQUFDLGlCQUFpQixDQUFDLE1BQU0sRUFBRSxPQUFPLEVBQUUsa0JBQWtCLENBQUMsQ0FBQztnQkFDM0YsTUFBTSxDQUFDLElBQUEsZ0NBQW1CLEVBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDcEQsQ0FBQyxDQUFDLENBQUM7WUFFSCxFQUFFLENBQUMsaURBQWlELEVBQUUsR0FBRyxFQUFFO2dCQUN6RCxNQUFNLFNBQVMsR0FBRyw4QkFBaUIsQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLFVBQVUsQ0FBQyxDQUFDO2dCQUMvRCxNQUFNLENBQUMsSUFBQSxnQ0FBbUIsRUFBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUNyRCxDQUFDLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO1FBRUgsUUFBUSxDQUFDLHNCQUFzQixFQUFFLEdBQUcsRUFBRTtZQUNwQyxFQUFFLENBQUMsOENBQThDLEVBQUUsR0FBRyxFQUFFO2dCQUN0RCxNQUFNLFNBQVMsR0FBRywrQkFBa0IsQ0FBQyxlQUFlLENBQUMsR0FBRyxFQUFFLEdBQUcsRUFBRSxJQUFJLElBQUksRUFBRSxDQUFDLENBQUM7Z0JBQzNFLE1BQU0sQ0FBQyxJQUFBLGlDQUFvQixFQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3JELENBQUMsQ0FBQyxDQUFDO1lBRUgsRUFBRSxDQUFDLGlEQUFpRCxFQUFFLEdBQUcsRUFBRTtnQkFDekQsTUFBTSxTQUFTLEdBQUcsOEJBQWlCLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxVQUFVLENBQUMsQ0FBQztnQkFDL0QsTUFBTSxDQUFDLElBQUEsaUNBQW9CLEVBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDdEQsQ0FBQyxDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILFFBQVEsQ0FBQywrQkFBK0IsRUFBRSxHQUFHLEVBQUU7WUFDN0MsRUFBRSxDQUFDLHVEQUF1RCxFQUFFLEdBQUcsRUFBRTtnQkFDL0QsTUFBTSxTQUFTLEdBQUcsd0NBQTJCLENBQUMsUUFBUSxDQUFDLGFBQWEsQ0FBQyxDQUFDO2dCQUN0RSxNQUFNLENBQUMsSUFBQSwwQ0FBNkIsRUFBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUM5RCxDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyxpREFBaUQsRUFBRSxHQUFHLEVBQUU7Z0JBQ3pELE1BQU0sU0FBUyxHQUFHLDhCQUFpQixDQUFDLE1BQU0sQ0FBQyxNQUFNLEVBQUUsVUFBVSxDQUFDLENBQUM7Z0JBQy9ELE1BQU0sQ0FBQyxJQUFBLDBDQUE2QixFQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQy9ELENBQUMsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxRQUFRLENBQUMsbUJBQW1CLEVBQUUsR0FBRyxFQUFFO1lBQ2pDLEVBQUUsQ0FBQyxrRUFBa0UsRUFBRSxHQUFHLEVBQUU7Z0JBQzFFLE1BQU0sbUJBQW1CLEdBQUcsZ0NBQW1CLENBQUMsTUFBTSxDQUFDLE9BQU8sRUFBRSxPQUFPLEVBQUUsVUFBVSxFQUFFLFVBQVUsQ0FBQyxDQUFDO2dCQUNqRyxNQUFNLHFCQUFxQixHQUFHLGtDQUFxQixDQUFDLHFCQUFxQixFQUFFLENBQUM7Z0JBQzVFLE1BQU0sa0JBQWtCLEdBQUcsK0JBQWtCLENBQUMsdUJBQXVCLENBQUMsTUFBTSxFQUFFLFFBQVEsRUFBRSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUM7Z0JBQ3pHLE1BQU0saUJBQWlCLEdBQUcsOEJBQWlCLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxVQUFVLENBQUMsQ0FBQztnQkFDdkUsTUFBTSxpQkFBaUIsR0FBRyw4QkFBaUIsQ0FBQyxpQkFBaUIsQ0FBQyxNQUFNLEVBQUUsT0FBTyxFQUFFLGtCQUFrQixDQUFDLENBQUM7Z0JBQ25HLE1BQU0sa0JBQWtCLEdBQUcsK0JBQWtCLENBQUMsZUFBZSxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxDQUFDO2dCQUNwRixNQUFNLDJCQUEyQixHQUFHLHdDQUEyQixDQUFDLFFBQVEsQ0FBQyxhQUFhLENBQUMsQ0FBQztnQkFFeEYsTUFBTSxDQUFDLElBQUEsOEJBQWlCLEVBQUMsbUJBQW1CLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztnQkFDekQsTUFBTSxDQUFDLElBQUEsOEJBQWlCLEVBQUMscUJBQXFCLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztnQkFDM0QsTUFBTSxDQUFDLElBQUEsOEJBQWlCLEVBQUMsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztnQkFDeEQsTUFBTSxDQUFDLElBQUEsOEJBQWlCLEVBQUMsaUJBQWlCLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztnQkFDdkQsTUFBTSxDQUFDLElBQUEsOEJBQWlCLEVBQUMsaUJBQWlCLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztnQkFDdkQsTUFBTSxDQUFDLElBQUEsOEJBQWlCLEVBQUMsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztnQkFDeEQsTUFBTSxDQUFDLElBQUEsOEJBQWlCLEVBQUMsMkJBQTJCLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztZQUNuRSxDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyxpREFBaUQsRUFBRSxHQUFHLEVBQUU7Z0JBQ3pELHFFQUFxRTtnQkFDckUsTUFBTSxxQkFBc0IsU0FBUSw0QkFBZTtvQkFDakQ7d0JBQ0UsS0FBSyxDQUFDLGNBQWMsRUFBRSxjQUFjLENBQUMsQ0FBQztvQkFDeEMsQ0FBQztpQkFDRjtnQkFFRCxNQUFNLGVBQWUsR0FBRyxJQUFJLHFCQUFxQixFQUFFLENBQUM7Z0JBQ3BELE1BQU0sQ0FBQyxJQUFBLDhCQUFpQixFQUFDLGVBQWUsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBQ3ZELENBQUMsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxRQUFRLENBQUMsb0JBQW9CLEVBQUUsR0FBRyxFQUFFO1lBQ2xDLEVBQUUsQ0FBQywwREFBMEQsRUFBRSxHQUFHLEVBQUU7Z0JBQ2xFLE1BQU0sU0FBUyxHQUFHLGdDQUFtQixDQUFDLE1BQU0sQ0FBQyxPQUFPLEVBQUUsU0FBUyxFQUFFLFFBQVEsRUFBRSxzQkFBc0IsQ0FBQyxDQUFDO2dCQUNuRyxNQUFNLFFBQVEsR0FBRyxJQUFBLCtCQUFrQixFQUFDLFNBQVMsQ0FBQyxDQUFDO2dCQUUvQyxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsY0FBYyxDQUFDLE9BQU8sQ0FBQyxDQUFDO2dCQUN6QyxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsY0FBYyxDQUFDLE1BQU0sRUFBRSxrQkFBa0IsQ0FBQyxDQUFDO2dCQUM1RCxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsY0FBYyxDQUFDLFNBQVMsQ0FBQyxDQUFDO2dCQUMzQyxNQUFNLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxPQUFPLEVBQUUsT0FBTyxDQUFDLENBQUM7WUFDNUQsQ0FBQyxDQUFDLENBQUM7WUFFSCxFQUFFLENBQUMsdUVBQXVFLEVBQUUsR0FBRyxFQUFFO2dCQUMvRSwrREFBK0Q7Z0JBQy9ELE1BQU0sb0JBQXFCLFNBQVEsNEJBQWU7b0JBQ2hEO3dCQUNFLEtBQUssQ0FBQyxhQUFhLEVBQUUsYUFBYSxFQUFFOzRCQUNsQyxRQUFRLEVBQUUsS0FBSzs0QkFDZixRQUFRLEVBQUUsTUFBTTt5QkFDakIsQ0FBQyxDQUFDO29CQUNMLENBQUM7aUJBQ0Y7Z0JBRUQsTUFBTSxTQUFTLEdBQUcsSUFBSSxvQkFBb0IsRUFBRSxDQUFDO2dCQUM3QyxNQUFNLFFBQVEsR0FBRyxJQUFBLCtCQUFrQixFQUFDLFNBQVMsQ0FBQyxDQUFDO2dCQUUvQyxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsT0FBTyxDQUFDO29CQUN2QixLQUFLLEVBQUUsU0FBUyxDQUFDLGNBQWMsRUFBRTtvQkFDakMsSUFBSSxFQUFFLGFBQWE7b0JBQ25CLE9BQU8sRUFBRTt3QkFDUCxRQUFRLEVBQUUsTUFBTTt3QkFDaEIsUUFBUSxFQUFFLEtBQUs7d0JBQ2YsU0FBUyxFQUFFLFNBQVMsQ0FBQyxTQUFTLENBQUMsV0FBVyxFQUFFO3dCQUM1QyxhQUFhLEVBQUUsU0FBUyxDQUFDLGFBQWE7cUJBQ3ZDO2lCQUNGLENBQUMsQ0FBQztZQUNMLENBQUMsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxRQUFRLENBQUMsNkJBQTZCLEVBQUUsR0FBRyxFQUFFO1lBQzNDLEVBQUUsQ0FBQyw4REFBOEQsRUFBRSxHQUFHLEVBQUU7Z0JBQ3RFLE1BQU0sU0FBUyxHQUFHLElBQUksZ0NBQW1CLENBQUMsWUFBWSxFQUFFLEVBQUUsRUFBRTtvQkFDMUQsT0FBTyxFQUFFO3dCQUNQLFFBQVEsRUFBRSxXQUFXO3dCQUNyQixLQUFLLEVBQUUsV0FBVzt3QkFDbEIsS0FBSyxFQUFFLGtCQUFrQjt3QkFDekIsTUFBTSxFQUFFLFVBQVU7cUJBQ25CO2lCQUNGLENBQUMsQ0FBQztnQkFFSCxNQUFNLFNBQVMsR0FBRyxJQUFBLHdDQUEyQixFQUFDLFNBQVMsQ0FBQyxDQUFDO2dCQUV6RCxNQUFNLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUM7Z0JBQ3RELE1BQU0sQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztnQkFDbkQsTUFBTSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLGtCQUFrQixDQUFDLENBQUM7Z0JBQ3pELE1BQU0sQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUNwRCxDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyxrQ0FBa0MsRUFBRSxHQUFHLEVBQUU7Z0JBQzFDLE1BQU0sU0FBUyxHQUFHLElBQUksZ0NBQW1CLENBQUMsWUFBWSxFQUFFLEVBQUUsRUFBRTtvQkFDMUQsT0FBTyxFQUFFO3dCQUNQLE1BQU0sRUFBRSxhQUFhO3dCQUNyQixLQUFLLEVBQUUsa0JBQWtCO3dCQUN6QixZQUFZLEVBQUUsY0FBYztxQkFDN0I7aUJBQ0YsQ0FBQyxDQUFDO2dCQUVILE1BQU0sU0FBUyxHQUFHLElBQUEsd0NBQTJCLEVBQUMsU0FBUyxFQUFFLENBQUMsUUFBUSxFQUFFLGNBQWMsQ0FBQyxDQUFDLENBQUM7Z0JBRXJGLE1BQU0sQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztnQkFDcEQsTUFBTSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsWUFBWSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDO2dCQUMxRCxNQUFNLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsa0JBQWtCLENBQUMsQ0FBQztZQUMzRCxDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyxpREFBaUQsRUFBRSxHQUFHLEVBQUU7Z0JBQ3pELE1BQU0sU0FBUyxHQUFHLElBQUksZ0NBQW1CLENBQUMsWUFBWSxFQUFFLEVBQUUsRUFBRTtvQkFDMUQsT0FBTyxFQUFFO3dCQUNQLE1BQU0sRUFBRSxVQUFVO3dCQUNsQixTQUFTLEVBQUUsUUFBUTtxQkFDcEI7aUJBQ0YsQ0FBQyxDQUFDO2dCQUVILE1BQU0sU0FBUyxHQUFHLElBQUEsd0NBQTJCLEVBQUMsU0FBUyxDQUFDLENBQUM7Z0JBRXpELE1BQU0sQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQztnQkFDbEQsTUFBTSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ3JELENBQUMsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxhQUFhLEVBQUUsR0FBRyxFQUFFO1FBQzNCLEVBQUUsQ0FBQyx1REFBdUQsRUFBRSxHQUFHLEVBQUU7WUFDL0QsTUFBTSxVQUFVLEdBQUc7Z0JBQ2pCLGdDQUFtQixDQUFDLE1BQU0sQ0FBQyxPQUFPLEVBQUUsT0FBTyxFQUFFLFVBQVUsRUFBRSxVQUFVLENBQUM7Z0JBQ3BFLDhCQUFpQixDQUFDLE1BQU0sQ0FBQyxNQUFNLEVBQUUsVUFBVSxDQUFDO2dCQUM1QyxrQ0FBcUIsQ0FBQyxxQkFBcUIsRUFBRTtnQkFDN0MsK0JBQWtCLENBQUMsdUJBQXVCLENBQUMsTUFBTSxFQUFFLFFBQVEsRUFBRSxDQUFDLGFBQWEsQ0FBQyxDQUFDO2dCQUM3RSw4QkFBaUIsQ0FBQyxpQkFBaUIsQ0FBQyxNQUFNLEVBQUUsT0FBTyxFQUFFLGtCQUFrQixDQUFDO2dCQUN4RSwrQkFBa0IsQ0FBQyxlQUFlLENBQUMsR0FBRyxFQUFFLEdBQUcsRUFBRSxJQUFJLElBQUksRUFBRSxDQUFDO2dCQUN4RCx3Q0FBMkIsQ0FBQyxRQUFRLENBQUMsYUFBYSxDQUFDO2FBQ3BELENBQUM7WUFFRixVQUFVLENBQUMsT0FBTyxDQUFDLFNBQVMsQ0FBQyxFQUFFO2dCQUM3QixNQUFNLENBQUMsSUFBQSw4QkFBaUIsRUFBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFDaEQsTUFBTSxDQUFDLElBQUEsOEJBQWlCLEVBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxzQkFBc0IsQ0FBQyxHQUFHLENBQUMsQ0FBQztnQkFDakUsTUFBTSxDQUFDLElBQUEsOEJBQWlCLEVBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxZQUFZLENBQUMsR0FBRyxDQUFDLENBQUM7Z0JBRXZELE1BQU0sV0FBVyxHQUFHLElBQUEsK0JBQWtCLEVBQUMsU0FBUyxDQUFDLENBQUM7Z0JBQ2xELE1BQU0sQ0FBQyxXQUFXLENBQUMsQ0FBQyxjQUFjLENBQUMsT0FBTyxDQUFDLENBQUM7Z0JBQzVDLE1BQU0sQ0FBQyxXQUFXLENBQUMsQ0FBQyxjQUFjLENBQUMsTUFBTSxDQUFDLENBQUM7Z0JBRTNDLE1BQU0sU0FBUyxHQUFHLElBQUEsd0NBQTJCLEVBQUMsU0FBUyxDQUFDLENBQUM7Z0JBQ3pELE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxjQUFjLENBQUMsTUFBTSxDQUFDLENBQUM7Z0JBQ3pDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxjQUFjLENBQUMsU0FBUyxDQUFDLENBQUM7Z0JBQzVDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxjQUFjLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDM0MsQ0FBQyxDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0FBQ0wsQ0FBQyxDQUFDLENBQUMiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxzaGFyZWQta2VybmVsXFxfX3Rlc3RzX19cXGV4Y2VwdGlvbnNcXGluZGV4LnNwZWMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcclxuICBEb21haW5FeGNlcHRpb24sXHJcbiAgVmFsaWRhdGlvbkV4Y2VwdGlvbixcclxuICBOb3RGb3VuZEV4Y2VwdGlvbixcclxuICBVbmF1dGhvcml6ZWRFeGNlcHRpb24sXHJcbiAgRm9yYmlkZGVuRXhjZXB0aW9uLFxyXG4gIENvbmZsaWN0RXhjZXB0aW9uLFxyXG4gIFJhdGVMaW1pdEV4Y2VwdGlvbixcclxuICBTZXJ2aWNlVW5hdmFpbGFibGVFeGNlcHRpb24sXHJcbiAgRXhjZXB0aW9uQ2F0ZWdvcmllcyxcclxuICBFeGNlcHRpb25TZXZlcml0eSxcclxuICBpc0RvbWFpbkV4Y2VwdGlvbixcclxuICBpc1ZhbGlkYXRpb25FeGNlcHRpb24sXHJcbiAgaXNOb3RGb3VuZEV4Y2VwdGlvbixcclxuICBpc0F1dGhvcml6YXRpb25FeGNlcHRpb24sXHJcbiAgaXNDb25mbGljdEV4Y2VwdGlvbixcclxuICBpc1JhdGVMaW1pdEV4Y2VwdGlvbixcclxuICBpc1NlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbixcclxuICBnZXRIdHRwU3RhdHVzQ29kZSxcclxuICB0b0FwaUVycm9yUmVzcG9uc2UsXHJcbiAgc2FuaXRpemVFeGNlcHRpb25Gb3JMb2dnaW5nLFxyXG59IGZyb20gJy4uLy4uL2V4Y2VwdGlvbnMnO1xyXG5cclxuZGVzY3JpYmUoJ0V4Y2VwdGlvbiBJbmRleCcsICgpID0+IHtcclxuICBkZXNjcmliZSgnZXhwb3J0cycsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgZXhwb3J0IGFsbCBleGNlcHRpb24gY2xhc3NlcycsICgpID0+IHtcclxuICAgICAgZXhwZWN0KERvbWFpbkV4Y2VwdGlvbikudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KFZhbGlkYXRpb25FeGNlcHRpb24pLnRvQmVEZWZpbmVkKCk7XHJcbiAgICAgIGV4cGVjdChOb3RGb3VuZEV4Y2VwdGlvbikudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KFVuYXV0aG9yaXplZEV4Y2VwdGlvbikudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KEZvcmJpZGRlbkV4Y2VwdGlvbikudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KENvbmZsaWN0RXhjZXB0aW9uKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgICBleHBlY3QoUmF0ZUxpbWl0RXhjZXB0aW9uKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgICBleHBlY3QoU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBleHBvcnQgZXhjZXB0aW9uIGNhdGVnb3JpZXMnLCAoKSA9PiB7XHJcbiAgICAgIGV4cGVjdChFeGNlcHRpb25DYXRlZ29yaWVzKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgICBleHBlY3QoRXhjZXB0aW9uQ2F0ZWdvcmllcy5ET01BSU4pLnRvQ29udGFpbignQnVzaW5lc3NSdWxlVmlvbGF0aW9uRXhjZXB0aW9uJyk7XHJcbiAgICAgIGV4cGVjdChFeGNlcHRpb25DYXRlZ29yaWVzLlJFU09VUkNFKS50b0NvbnRhaW4oJ05vdEZvdW5kRXhjZXB0aW9uJyk7XHJcbiAgICAgIGV4cGVjdChFeGNlcHRpb25DYXRlZ29yaWVzLlZBTElEQVRJT04pLnRvQ29udGFpbignVmFsaWRhdGlvbkV4Y2VwdGlvbicpO1xyXG4gICAgICBleHBlY3QoRXhjZXB0aW9uQ2F0ZWdvcmllcy5TRUNVUklUWSkudG9Db250YWluKCdVbmF1dGhvcml6ZWRFeGNlcHRpb24nKTtcclxuICAgICAgZXhwZWN0KEV4Y2VwdGlvbkNhdGVnb3JpZXMuQ09ORkxJQ1QpLnRvQ29udGFpbignQ29uZmxpY3RFeGNlcHRpb24nKTtcclxuICAgICAgZXhwZWN0KEV4Y2VwdGlvbkNhdGVnb3JpZXMuQVZBSUxBQklMSVRZKS50b0NvbnRhaW4oJ1JhdGVMaW1pdEV4Y2VwdGlvbicpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBleHBvcnQgZXhjZXB0aW9uIHNldmVyaXR5IGxldmVscycsICgpID0+IHtcclxuICAgICAgZXhwZWN0KEV4Y2VwdGlvblNldmVyaXR5KS50b0JlRGVmaW5lZCgpO1xyXG4gICAgICBleHBlY3QoRXhjZXB0aW9uU2V2ZXJpdHkuTE9XKS50b0JlKCdsb3cnKTtcclxuICAgICAgZXhwZWN0KEV4Y2VwdGlvblNldmVyaXR5Lk1FRElVTSkudG9CZSgnbWVkaXVtJyk7XHJcbiAgICAgIGV4cGVjdChFeGNlcHRpb25TZXZlcml0eS5ISUdIKS50b0JlKCdoaWdoJyk7XHJcbiAgICAgIGV4cGVjdChFeGNlcHRpb25TZXZlcml0eS5DUklUSUNBTCkudG9CZSgnY3JpdGljYWwnKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnaGVscGVyIGZ1bmN0aW9ucycsICgpID0+IHtcclxuICAgIGRlc2NyaWJlKCdpc0RvbWFpbkV4Y2VwdGlvbicsICgpID0+IHtcclxuICAgICAgaXQoJ3Nob3VsZCByZXR1cm4gdHJ1ZSBmb3IgZG9tYWluIGV4Y2VwdGlvbnMnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gbmV3IFZhbGlkYXRpb25FeGNlcHRpb24oJ1Rlc3QgZXJyb3InLCBbXSk7XHJcbiAgICAgICAgZXhwZWN0KGlzRG9tYWluRXhjZXB0aW9uKGV4Y2VwdGlvbikpLnRvQmUodHJ1ZSk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaXQoJ3Nob3VsZCByZXR1cm4gZmFsc2UgZm9yIHJlZ3VsYXIgZXJyb3JzJywgKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGVycm9yID0gbmV3IEVycm9yKCdSZWd1bGFyIGVycm9yJyk7XHJcbiAgICAgICAgZXhwZWN0KGlzRG9tYWluRXhjZXB0aW9uKGVycm9yKSkudG9CZShmYWxzZSk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaXQoJ3Nob3VsZCByZXR1cm4gZmFsc2UgZm9yIG5vbi1lcnJvciB2YWx1ZXMnLCAoKSA9PiB7XHJcbiAgICAgICAgZXhwZWN0KGlzRG9tYWluRXhjZXB0aW9uKCdzdHJpbmcnKSkudG9CZShmYWxzZSk7XHJcbiAgICAgICAgZXhwZWN0KGlzRG9tYWluRXhjZXB0aW9uKG51bGwpKS50b0JlKGZhbHNlKTtcclxuICAgICAgICBleHBlY3QoaXNEb21haW5FeGNlcHRpb24odW5kZWZpbmVkKSkudG9CZShmYWxzZSk7XHJcbiAgICAgICAgZXhwZWN0KGlzRG9tYWluRXhjZXB0aW9uKHt9KSkudG9CZShmYWxzZSk7XHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgZGVzY3JpYmUoJ2lzVmFsaWRhdGlvbkV4Y2VwdGlvbicsICgpID0+IHtcclxuICAgICAgaXQoJ3Nob3VsZCByZXR1cm4gdHJ1ZSBmb3IgdmFsaWRhdGlvbiBleGNlcHRpb25zJywgKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IFZhbGlkYXRpb25FeGNlcHRpb24uc2luZ2xlKCdmaWVsZCcsICd2YWx1ZScsICdyZXF1aXJlZCcsICdGaWVsZCBpcyByZXF1aXJlZCcpO1xyXG4gICAgICAgIGV4cGVjdChpc1ZhbGlkYXRpb25FeGNlcHRpb24oZXhjZXB0aW9uKSkudG9CZSh0cnVlKTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpdCgnc2hvdWxkIHJldHVybiBmYWxzZSBmb3Igb3RoZXIgZG9tYWluIGV4Y2VwdGlvbnMnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gTm90Rm91bmRFeGNlcHRpb24uZW50aXR5KCdVc2VyJywgJ3VzZXItMTIzJyk7XHJcbiAgICAgICAgZXhwZWN0KGlzVmFsaWRhdGlvbkV4Y2VwdGlvbihleGNlcHRpb24pKS50b0JlKGZhbHNlKTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpdCgnc2hvdWxkIHJldHVybiBmYWxzZSBmb3Igbm9uLWV4Y2VwdGlvbnMnLCAoKSA9PiB7XHJcbiAgICAgICAgZXhwZWN0KGlzVmFsaWRhdGlvbkV4Y2VwdGlvbignc3RyaW5nJykpLnRvQmUoZmFsc2UpO1xyXG4gICAgICAgIGV4cGVjdChpc1ZhbGlkYXRpb25FeGNlcHRpb24obmV3IEVycm9yKCkpKS50b0JlKGZhbHNlKTtcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBkZXNjcmliZSgnaXNOb3RGb3VuZEV4Y2VwdGlvbicsICgpID0+IHtcclxuICAgICAgaXQoJ3Nob3VsZCByZXR1cm4gdHJ1ZSBmb3Igbm90IGZvdW5kIGV4Y2VwdGlvbnMnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gTm90Rm91bmRFeGNlcHRpb24uZW50aXR5KCdVc2VyJywgJ3VzZXItMTIzJyk7XHJcbiAgICAgICAgZXhwZWN0KGlzTm90Rm91bmRFeGNlcHRpb24oZXhjZXB0aW9uKSkudG9CZSh0cnVlKTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpdCgnc2hvdWxkIHJldHVybiBmYWxzZSBmb3Igb3RoZXIgZG9tYWluIGV4Y2VwdGlvbnMnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gVmFsaWRhdGlvbkV4Y2VwdGlvbi5zaW5nbGUoJ2ZpZWxkJywgJ3ZhbHVlJywgJ3JlcXVpcmVkJywgJ0ZpZWxkIGlzIHJlcXVpcmVkJyk7XHJcbiAgICAgICAgZXhwZWN0KGlzTm90Rm91bmRFeGNlcHRpb24oZXhjZXB0aW9uKSkudG9CZShmYWxzZSk7XHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgZGVzY3JpYmUoJ2lzQXV0aG9yaXphdGlvbkV4Y2VwdGlvbicsICgpID0+IHtcclxuICAgICAgaXQoJ3Nob3VsZCByZXR1cm4gdHJ1ZSBmb3IgdW5hdXRob3JpemVkIGV4Y2VwdGlvbnMnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gVW5hdXRob3JpemVkRXhjZXB0aW9uLm1pc3NpbmdBdXRoZW50aWNhdGlvbigpO1xyXG4gICAgICAgIGV4cGVjdChpc0F1dGhvcml6YXRpb25FeGNlcHRpb24oZXhjZXB0aW9uKSkudG9CZSh0cnVlKTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpdCgnc2hvdWxkIHJldHVybiB0cnVlIGZvciBmb3JiaWRkZW4gZXhjZXB0aW9ucycsICgpID0+IHtcclxuICAgICAgICBjb25zdCBleGNlcHRpb24gPSBGb3JiaWRkZW5FeGNlcHRpb24uaW5zdWZmaWNpZW50UGVybWlzc2lvbnMoJ1VzZXInLCAnZGVsZXRlJywgWyd1c2VyOmRlbGV0ZSddKTtcclxuICAgICAgICBleHBlY3QoaXNBdXRob3JpemF0aW9uRXhjZXB0aW9uKGV4Y2VwdGlvbikpLnRvQmUodHJ1ZSk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaXQoJ3Nob3VsZCByZXR1cm4gZmFsc2UgZm9yIG90aGVyIGRvbWFpbiBleGNlcHRpb25zJywgKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IE5vdEZvdW5kRXhjZXB0aW9uLmVudGl0eSgnVXNlcicsICd1c2VyLTEyMycpO1xyXG4gICAgICAgIGV4cGVjdChpc0F1dGhvcml6YXRpb25FeGNlcHRpb24oZXhjZXB0aW9uKSkudG9CZShmYWxzZSk7XHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgZGVzY3JpYmUoJ2lzQ29uZmxpY3RFeGNlcHRpb24nLCAoKSA9PiB7XHJcbiAgICAgIGl0KCdzaG91bGQgcmV0dXJuIHRydWUgZm9yIGNvbmZsaWN0IGV4Y2VwdGlvbnMnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gQ29uZmxpY3RFeGNlcHRpb24uZHVwbGljYXRlUmVzb3VyY2UoJ1VzZXInLCAnZW1haWwnLCAndGVzdEBleGFtcGxlLmNvbScpO1xyXG4gICAgICAgIGV4cGVjdChpc0NvbmZsaWN0RXhjZXB0aW9uKGV4Y2VwdGlvbikpLnRvQmUodHJ1ZSk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaXQoJ3Nob3VsZCByZXR1cm4gZmFsc2UgZm9yIG90aGVyIGRvbWFpbiBleGNlcHRpb25zJywgKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IE5vdEZvdW5kRXhjZXB0aW9uLmVudGl0eSgnVXNlcicsICd1c2VyLTEyMycpO1xyXG4gICAgICAgIGV4cGVjdChpc0NvbmZsaWN0RXhjZXB0aW9uKGV4Y2VwdGlvbikpLnRvQmUoZmFsc2UpO1xyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGRlc2NyaWJlKCdpc1JhdGVMaW1pdEV4Y2VwdGlvbicsICgpID0+IHtcclxuICAgICAgaXQoJ3Nob3VsZCByZXR1cm4gdHJ1ZSBmb3IgcmF0ZSBsaW1pdCBleGNlcHRpb25zJywgKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IFJhdGVMaW1pdEV4Y2VwdGlvbi5hcGlSZXF1ZXN0TGltaXQoMTAwLCAxMDUsIG5ldyBEYXRlKCkpO1xyXG4gICAgICAgIGV4cGVjdChpc1JhdGVMaW1pdEV4Y2VwdGlvbihleGNlcHRpb24pKS50b0JlKHRydWUpO1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGl0KCdzaG91bGQgcmV0dXJuIGZhbHNlIGZvciBvdGhlciBkb21haW4gZXhjZXB0aW9ucycsICgpID0+IHtcclxuICAgICAgICBjb25zdCBleGNlcHRpb24gPSBOb3RGb3VuZEV4Y2VwdGlvbi5lbnRpdHkoJ1VzZXInLCAndXNlci0xMjMnKTtcclxuICAgICAgICBleHBlY3QoaXNSYXRlTGltaXRFeGNlcHRpb24oZXhjZXB0aW9uKSkudG9CZShmYWxzZSk7XHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgZGVzY3JpYmUoJ2lzU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uJywgKCkgPT4ge1xyXG4gICAgICBpdCgnc2hvdWxkIHJldHVybiB0cnVlIGZvciBzZXJ2aWNlIHVuYXZhaWxhYmxlIGV4Y2VwdGlvbnMnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLm92ZXJsb2FkKCdVc2VyU2VydmljZScpO1xyXG4gICAgICAgIGV4cGVjdChpc1NlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbihleGNlcHRpb24pKS50b0JlKHRydWUpO1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGl0KCdzaG91bGQgcmV0dXJuIGZhbHNlIGZvciBvdGhlciBkb21haW4gZXhjZXB0aW9ucycsICgpID0+IHtcclxuICAgICAgICBjb25zdCBleGNlcHRpb24gPSBOb3RGb3VuZEV4Y2VwdGlvbi5lbnRpdHkoJ1VzZXInLCAndXNlci0xMjMnKTtcclxuICAgICAgICBleHBlY3QoaXNTZXJ2aWNlVW5hdmFpbGFibGVFeGNlcHRpb24oZXhjZXB0aW9uKSkudG9CZShmYWxzZSk7XHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgZGVzY3JpYmUoJ2dldEh0dHBTdGF0dXNDb2RlJywgKCkgPT4ge1xyXG4gICAgICBpdCgnc2hvdWxkIHJldHVybiBjb3JyZWN0IHN0YXR1cyBjb2RlcyBmb3IgZGlmZmVyZW50IGV4Y2VwdGlvbiB0eXBlcycsICgpID0+IHtcclxuICAgICAgICBjb25zdCB2YWxpZGF0aW9uRXhjZXB0aW9uID0gVmFsaWRhdGlvbkV4Y2VwdGlvbi5zaW5nbGUoJ2ZpZWxkJywgJ3ZhbHVlJywgJ3JlcXVpcmVkJywgJ1JlcXVpcmVkJyk7XHJcbiAgICAgICAgY29uc3QgdW5hdXRob3JpemVkRXhjZXB0aW9uID0gVW5hdXRob3JpemVkRXhjZXB0aW9uLm1pc3NpbmdBdXRoZW50aWNhdGlvbigpO1xyXG4gICAgICAgIGNvbnN0IGZvcmJpZGRlbkV4Y2VwdGlvbiA9IEZvcmJpZGRlbkV4Y2VwdGlvbi5pbnN1ZmZpY2llbnRQZXJtaXNzaW9ucygnVXNlcicsICdkZWxldGUnLCBbJ3VzZXI6ZGVsZXRlJ10pO1xyXG4gICAgICAgIGNvbnN0IG5vdEZvdW5kRXhjZXB0aW9uID0gTm90Rm91bmRFeGNlcHRpb24uZW50aXR5KCdVc2VyJywgJ3VzZXItMTIzJyk7XHJcbiAgICAgICAgY29uc3QgY29uZmxpY3RFeGNlcHRpb24gPSBDb25mbGljdEV4Y2VwdGlvbi5kdXBsaWNhdGVSZXNvdXJjZSgnVXNlcicsICdlbWFpbCcsICd0ZXN0QGV4YW1wbGUuY29tJyk7XHJcbiAgICAgICAgY29uc3QgcmF0ZUxpbWl0RXhjZXB0aW9uID0gUmF0ZUxpbWl0RXhjZXB0aW9uLmFwaVJlcXVlc3RMaW1pdCgxMDAsIDEwNSwgbmV3IERhdGUoKSk7XHJcbiAgICAgICAgY29uc3Qgc2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLm92ZXJsb2FkKCdVc2VyU2VydmljZScpO1xyXG5cclxuICAgICAgICBleHBlY3QoZ2V0SHR0cFN0YXR1c0NvZGUodmFsaWRhdGlvbkV4Y2VwdGlvbikpLnRvQmUoNDAwKTtcclxuICAgICAgICBleHBlY3QoZ2V0SHR0cFN0YXR1c0NvZGUodW5hdXRob3JpemVkRXhjZXB0aW9uKSkudG9CZSg0MDEpO1xyXG4gICAgICAgIGV4cGVjdChnZXRIdHRwU3RhdHVzQ29kZShmb3JiaWRkZW5FeGNlcHRpb24pKS50b0JlKDQwMyk7XHJcbiAgICAgICAgZXhwZWN0KGdldEh0dHBTdGF0dXNDb2RlKG5vdEZvdW5kRXhjZXB0aW9uKSkudG9CZSg0MDQpO1xyXG4gICAgICAgIGV4cGVjdChnZXRIdHRwU3RhdHVzQ29kZShjb25mbGljdEV4Y2VwdGlvbikpLnRvQmUoNDA5KTtcclxuICAgICAgICBleHBlY3QoZ2V0SHR0cFN0YXR1c0NvZGUocmF0ZUxpbWl0RXhjZXB0aW9uKSkudG9CZSg0MjkpO1xyXG4gICAgICAgIGV4cGVjdChnZXRIdHRwU3RhdHVzQ29kZShzZXJ2aWNlVW5hdmFpbGFibGVFeGNlcHRpb24pKS50b0JlKDUwMyk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaXQoJ3Nob3VsZCByZXR1cm4gNDAwIGZvciB1bmtub3duIGRvbWFpbiBleGNlcHRpb25zJywgKCkgPT4ge1xyXG4gICAgICAgIC8vIENyZWF0ZSBhIGN1c3RvbSBkb21haW4gZXhjZXB0aW9uIHRoYXQgZG9lc24ndCBtYXRjaCBzcGVjaWZpYyB0eXBlc1xyXG4gICAgICAgIGNsYXNzIEN1c3RvbURvbWFpbkV4Y2VwdGlvbiBleHRlbmRzIERvbWFpbkV4Y2VwdGlvbiB7XHJcbiAgICAgICAgICBjb25zdHJ1Y3RvcigpIHtcclxuICAgICAgICAgICAgc3VwZXIoJ0N1c3RvbSBlcnJvcicsICdDVVNUT01fRVJST1InKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGN1c3RvbUV4Y2VwdGlvbiA9IG5ldyBDdXN0b21Eb21haW5FeGNlcHRpb24oKTtcclxuICAgICAgICBleHBlY3QoZ2V0SHR0cFN0YXR1c0NvZGUoY3VzdG9tRXhjZXB0aW9uKSkudG9CZSg0MDApO1xyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGRlc2NyaWJlKCd0b0FwaUVycm9yUmVzcG9uc2UnLCAoKSA9PiB7XHJcbiAgICAgIGl0KCdzaG91bGQgdXNlIGV4Y2VwdGlvbiB0b0FwaVJlc3BvbnNlIG1ldGhvZCB3aGVuIGF2YWlsYWJsZScsICgpID0+IHtcclxuICAgICAgICBjb25zdCBleGNlcHRpb24gPSBWYWxpZGF0aW9uRXhjZXB0aW9uLnNpbmdsZSgnZW1haWwnLCAnaW52YWxpZCcsICdmb3JtYXQnLCAnSW52YWxpZCBlbWFpbCBmb3JtYXQnKTtcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IHRvQXBpRXJyb3JSZXNwb25zZShleGNlcHRpb24pO1xyXG5cclxuICAgICAgICBleHBlY3QocmVzcG9uc2UpLnRvSGF2ZVByb3BlcnR5KCdlcnJvcicpO1xyXG4gICAgICAgIGV4cGVjdChyZXNwb25zZSkudG9IYXZlUHJvcGVydHkoJ2NvZGUnLCAnVkFMSURBVElPTl9FUlJPUicpO1xyXG4gICAgICAgIGV4cGVjdChyZXNwb25zZSkudG9IYXZlUHJvcGVydHkoJ2RldGFpbHMnKTtcclxuICAgICAgICBleHBlY3QocmVzcG9uc2UuZGV0YWlscykudG9IYXZlUHJvcGVydHkoJ2ZpZWxkJywgJ2VtYWlsJyk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaXQoJ3Nob3VsZCBwcm92aWRlIGZhbGxiYWNrIHJlc3BvbnNlIGZvciBleGNlcHRpb25zIHdpdGhvdXQgdG9BcGlSZXNwb25zZScsICgpID0+IHtcclxuICAgICAgICAvLyBDcmVhdGUgYSBiYXNpYyBkb21haW4gZXhjZXB0aW9uIHdpdGhvdXQgdG9BcGlSZXNwb25zZSBtZXRob2RcclxuICAgICAgICBjbGFzcyBCYXNpY0RvbWFpbkV4Y2VwdGlvbiBleHRlbmRzIERvbWFpbkV4Y2VwdGlvbiB7XHJcbiAgICAgICAgICBjb25zdHJ1Y3RvcigpIHtcclxuICAgICAgICAgICAgc3VwZXIoJ0Jhc2ljIGVycm9yJywgJ0JBU0lDX0VSUk9SJywge1xyXG4gICAgICAgICAgICAgIHNldmVyaXR5OiAnbG93JyxcclxuICAgICAgICAgICAgICBjYXRlZ29yeTogJ3Rlc3QnLFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IG5ldyBCYXNpY0RvbWFpbkV4Y2VwdGlvbigpO1xyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gdG9BcGlFcnJvclJlc3BvbnNlKGV4Y2VwdGlvbik7XHJcblxyXG4gICAgICAgIGV4cGVjdChyZXNwb25zZSkudG9FcXVhbCh7XHJcbiAgICAgICAgICBlcnJvcjogZXhjZXB0aW9uLmdldFVzZXJNZXNzYWdlKCksXHJcbiAgICAgICAgICBjb2RlOiAnQkFTSUNfRVJST1InLFxyXG4gICAgICAgICAgZGV0YWlsczoge1xyXG4gICAgICAgICAgICBjYXRlZ29yeTogJ3Rlc3QnLFxyXG4gICAgICAgICAgICBzZXZlcml0eTogJ2xvdycsXHJcbiAgICAgICAgICAgIHRpbWVzdGFtcDogZXhjZXB0aW9uLnRpbWVzdGFtcC50b0lTT1N0cmluZygpLFxyXG4gICAgICAgICAgICBjb3JyZWxhdGlvbklkOiBleGNlcHRpb24uY29ycmVsYXRpb25JZCxcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgZGVzY3JpYmUoJ3Nhbml0aXplRXhjZXB0aW9uRm9yTG9nZ2luZycsICgpID0+IHtcclxuICAgICAgaXQoJ3Nob3VsZCBzYW5pdGl6ZSBzZW5zaXRpdmUgaW5mb3JtYXRpb24gZnJvbSBleGNlcHRpb24gY29udGV4dCcsICgpID0+IHtcclxuICAgICAgICBjb25zdCBleGNlcHRpb24gPSBuZXcgVmFsaWRhdGlvbkV4Y2VwdGlvbignVGVzdCBlcnJvcicsIFtdLCB7XHJcbiAgICAgICAgICBjb250ZXh0OiB7XHJcbiAgICAgICAgICAgIHBhc3N3b3JkOiAnc2VjcmV0MTIzJyxcclxuICAgICAgICAgICAgdG9rZW46ICdqd3QtdG9rZW4nLFxyXG4gICAgICAgICAgICBlbWFpbDogJ3VzZXJAZXhhbXBsZS5jb20nLFxyXG4gICAgICAgICAgICB1c2VySWQ6ICd1c2VyLTEyMycsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBjb25zdCBzYW5pdGl6ZWQgPSBzYW5pdGl6ZUV4Y2VwdGlvbkZvckxvZ2dpbmcoZXhjZXB0aW9uKTtcclxuXHJcbiAgICAgICAgZXhwZWN0KHNhbml0aXplZC5jb250ZXh0LnBhc3N3b3JkKS50b0JlKCdbUkVEQUNURURdJyk7XHJcbiAgICAgICAgZXhwZWN0KHNhbml0aXplZC5jb250ZXh0LnRva2VuKS50b0JlKCdbUkVEQUNURURdJyk7XHJcbiAgICAgICAgZXhwZWN0KHNhbml0aXplZC5jb250ZXh0LmVtYWlsKS50b0JlKCd1c2VyQGV4YW1wbGUuY29tJyk7XHJcbiAgICAgICAgZXhwZWN0KHNhbml0aXplZC5jb250ZXh0LnVzZXJJZCkudG9CZSgndXNlci0xMjMnKTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpdCgnc2hvdWxkIHVzZSBjdXN0b20gc2Vuc2l0aXZlIGtleXMnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gbmV3IFZhbGlkYXRpb25FeGNlcHRpb24oJ1Rlc3QgZXJyb3InLCBbXSwge1xyXG4gICAgICAgICAgY29udGV4dDoge1xyXG4gICAgICAgICAgICBhcGlLZXk6ICdhcGkta2V5LTEyMycsXHJcbiAgICAgICAgICAgIGVtYWlsOiAndXNlckBleGFtcGxlLmNvbScsXHJcbiAgICAgICAgICAgIGN1c3RvbVNlY3JldDogJ3NlY3JldC12YWx1ZScsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBjb25zdCBzYW5pdGl6ZWQgPSBzYW5pdGl6ZUV4Y2VwdGlvbkZvckxvZ2dpbmcoZXhjZXB0aW9uLCBbJ2FwaUtleScsICdjdXN0b21TZWNyZXQnXSk7XHJcblxyXG4gICAgICAgIGV4cGVjdChzYW5pdGl6ZWQuY29udGV4dC5hcGlLZXkpLnRvQmUoJ1tSRURBQ1RFRF0nKTtcclxuICAgICAgICBleHBlY3Qoc2FuaXRpemVkLmNvbnRleHQuY3VzdG9tU2VjcmV0KS50b0JlKCdbUkVEQUNURURdJyk7XHJcbiAgICAgICAgZXhwZWN0KHNhbml0aXplZC5jb250ZXh0LmVtYWlsKS50b0JlKCd1c2VyQGV4YW1wbGUuY29tJyk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaXQoJ3Nob3VsZCBoYW5kbGUgZXhjZXB0aW9ucyB3aXRob3V0IHNlbnNpdGl2ZSBkYXRhJywgKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IG5ldyBWYWxpZGF0aW9uRXhjZXB0aW9uKCdUZXN0IGVycm9yJywgW10sIHtcclxuICAgICAgICAgIGNvbnRleHQ6IHtcclxuICAgICAgICAgICAgdXNlcklkOiAndXNlci0xMjMnLFxyXG4gICAgICAgICAgICBvcGVyYXRpb246ICdjcmVhdGUnLFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgY29uc3Qgc2FuaXRpemVkID0gc2FuaXRpemVFeGNlcHRpb25Gb3JMb2dnaW5nKGV4Y2VwdGlvbik7XHJcblxyXG4gICAgICAgIGV4cGVjdChzYW5pdGl6ZWQuY29udGV4dC51c2VySWQpLnRvQmUoJ3VzZXItMTIzJyk7XHJcbiAgICAgICAgZXhwZWN0KHNhbml0aXplZC5jb250ZXh0Lm9wZXJhdGlvbikudG9CZSgnY3JlYXRlJyk7XHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdpbnRlZ3JhdGlvbicsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgd29yayB3aXRoIGFsbCBleGNlcHRpb24gdHlwZXMgaW4gYSB1bmlmaWVkIHdheScsICgpID0+IHtcclxuICAgICAgY29uc3QgZXhjZXB0aW9ucyA9IFtcclxuICAgICAgICBWYWxpZGF0aW9uRXhjZXB0aW9uLnNpbmdsZSgnZmllbGQnLCAndmFsdWUnLCAncmVxdWlyZWQnLCAnUmVxdWlyZWQnKSxcclxuICAgICAgICBOb3RGb3VuZEV4Y2VwdGlvbi5lbnRpdHkoJ1VzZXInLCAndXNlci0xMjMnKSxcclxuICAgICAgICBVbmF1dGhvcml6ZWRFeGNlcHRpb24ubWlzc2luZ0F1dGhlbnRpY2F0aW9uKCksXHJcbiAgICAgICAgRm9yYmlkZGVuRXhjZXB0aW9uLmluc3VmZmljaWVudFBlcm1pc3Npb25zKCdVc2VyJywgJ2RlbGV0ZScsIFsndXNlcjpkZWxldGUnXSksXHJcbiAgICAgICAgQ29uZmxpY3RFeGNlcHRpb24uZHVwbGljYXRlUmVzb3VyY2UoJ1VzZXInLCAnZW1haWwnLCAndGVzdEBleGFtcGxlLmNvbScpLFxyXG4gICAgICAgIFJhdGVMaW1pdEV4Y2VwdGlvbi5hcGlSZXF1ZXN0TGltaXQoMTAwLCAxMDUsIG5ldyBEYXRlKCkpLFxyXG4gICAgICAgIFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbi5vdmVybG9hZCgnVXNlclNlcnZpY2UnKSxcclxuICAgICAgXTtcclxuXHJcbiAgICAgIGV4Y2VwdGlvbnMuZm9yRWFjaChleGNlcHRpb24gPT4ge1xyXG4gICAgICAgIGV4cGVjdChpc0RvbWFpbkV4Y2VwdGlvbihleGNlcHRpb24pKS50b0JlKHRydWUpO1xyXG4gICAgICAgIGV4cGVjdChnZXRIdHRwU3RhdHVzQ29kZShleGNlcHRpb24pKS50b0JlR3JlYXRlclRoYW5PckVxdWFsKDQwMCk7XHJcbiAgICAgICAgZXhwZWN0KGdldEh0dHBTdGF0dXNDb2RlKGV4Y2VwdGlvbikpLnRvQmVMZXNzVGhhbig2MDApO1xyXG5cclxuICAgICAgICBjb25zdCBhcGlSZXNwb25zZSA9IHRvQXBpRXJyb3JSZXNwb25zZShleGNlcHRpb24pO1xyXG4gICAgICAgIGV4cGVjdChhcGlSZXNwb25zZSkudG9IYXZlUHJvcGVydHkoJ2Vycm9yJyk7XHJcbiAgICAgICAgZXhwZWN0KGFwaVJlc3BvbnNlKS50b0hhdmVQcm9wZXJ0eSgnY29kZScpO1xyXG5cclxuICAgICAgICBjb25zdCBzYW5pdGl6ZWQgPSBzYW5pdGl6ZUV4Y2VwdGlvbkZvckxvZ2dpbmcoZXhjZXB0aW9uKTtcclxuICAgICAgICBleHBlY3Qoc2FuaXRpemVkKS50b0hhdmVQcm9wZXJ0eSgnbmFtZScpO1xyXG4gICAgICAgIGV4cGVjdChzYW5pdGl6ZWQpLnRvSGF2ZVByb3BlcnR5KCdtZXNzYWdlJyk7XHJcbiAgICAgICAgZXhwZWN0KHNhbml0aXplZCkudG9IYXZlUHJvcGVydHkoJ2NvZGUnKTtcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuICB9KTtcclxufSk7Il0sInZlcnNpb24iOjN9