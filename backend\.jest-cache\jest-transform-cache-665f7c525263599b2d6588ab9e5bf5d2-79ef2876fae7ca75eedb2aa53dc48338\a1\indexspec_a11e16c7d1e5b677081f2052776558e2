9ef1cf45eaafd846fdcef36b0944eb94
"use strict";
/**
 * Decorator Tests Index
 *
 * This file ensures all decorator tests are properly exported and can be run together.
 */
Object.defineProperty(exports, "__esModule", { value: true });
// Import all decorator test suites to ensure they are executed
require("./audit.decorator.spec");
require("./cache.decorator.spec");
require("./retry.decorator.spec");
require("./rate-limit.decorator.spec");
describe('Decorators Integration', () => {
    it('should have all decorator test suites available', () => {
        // This test ensures all decorator test files are properly loaded
        expect(true).toBe(true);
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxzaGFyZWQta2VybmVsXFxfX3Rlc3RzX19cXGRlY29yYXRvcnNcXGluZGV4LnNwZWMudHMiLCJtYXBwaW5ncyI6IjtBQUFBOzs7O0dBSUc7O0FBRUgsK0RBQStEO0FBQy9ELGtDQUFnQztBQUNoQyxrQ0FBZ0M7QUFDaEMsa0NBQWdDO0FBQ2hDLHVDQUFxQztBQUVyQyxRQUFRLENBQUMsd0JBQXdCLEVBQUUsR0FBRyxFQUFFO0lBQ3RDLEVBQUUsQ0FBQyxpREFBaUQsRUFBRSxHQUFHLEVBQUU7UUFDekQsaUVBQWlFO1FBQ2pFLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7SUFDMUIsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXHNoYXJlZC1rZXJuZWxcXF9fdGVzdHNfX1xcZGVjb3JhdG9yc1xcaW5kZXguc3BlYy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcclxuICogRGVjb3JhdG9yIFRlc3RzIEluZGV4XHJcbiAqIFxyXG4gKiBUaGlzIGZpbGUgZW5zdXJlcyBhbGwgZGVjb3JhdG9yIHRlc3RzIGFyZSBwcm9wZXJseSBleHBvcnRlZCBhbmQgY2FuIGJlIHJ1biB0b2dldGhlci5cclxuICovXHJcblxyXG4vLyBJbXBvcnQgYWxsIGRlY29yYXRvciB0ZXN0IHN1aXRlcyB0byBlbnN1cmUgdGhleSBhcmUgZXhlY3V0ZWRcclxuaW1wb3J0ICcuL2F1ZGl0LmRlY29yYXRvci5zcGVjJztcclxuaW1wb3J0ICcuL2NhY2hlLmRlY29yYXRvci5zcGVjJztcclxuaW1wb3J0ICcuL3JldHJ5LmRlY29yYXRvci5zcGVjJztcclxuaW1wb3J0ICcuL3JhdGUtbGltaXQuZGVjb3JhdG9yLnNwZWMnO1xyXG5cclxuZGVzY3JpYmUoJ0RlY29yYXRvcnMgSW50ZWdyYXRpb24nLCAoKSA9PiB7XHJcbiAgaXQoJ3Nob3VsZCBoYXZlIGFsbCBkZWNvcmF0b3IgdGVzdCBzdWl0ZXMgYXZhaWxhYmxlJywgKCkgPT4ge1xyXG4gICAgLy8gVGhpcyB0ZXN0IGVuc3VyZXMgYWxsIGRlY29yYXRvciB0ZXN0IGZpbGVzIGFyZSBwcm9wZXJseSBsb2FkZWRcclxuICAgIGV4cGVjdCh0cnVlKS50b0JlKHRydWUpO1xyXG4gIH0pO1xyXG59KTsiXSwidmVyc2lvbiI6M30=