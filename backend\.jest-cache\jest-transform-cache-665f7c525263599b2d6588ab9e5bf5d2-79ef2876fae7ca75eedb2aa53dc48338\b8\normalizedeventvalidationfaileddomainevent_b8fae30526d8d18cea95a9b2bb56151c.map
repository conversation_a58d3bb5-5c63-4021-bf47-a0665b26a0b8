{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\normalized-event-validation-failed.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAwB5E;;;;;;;;;;GAUG;AACH,MAAa,0CAA2C,SAAQ,+BAAyD;IACvH,YACE,WAA2B,EAC3B,SAAmD,EACnD,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,IAAI,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,OAAO,KAAK,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,OAAO,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC3C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;IACrF,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC5C,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC5C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACjC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACvC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CACxC,CAAC;IACX,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC5C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACjC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACxC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CACxC,CAAC;IACX,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAChC,OAAO,cAAc,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAClC,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QACD,IAAI,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC,EAAE,CAAC;YACnC,OAAO,cAAc,CAAC;QACxB,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,8CAA8C;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,WAAW;QACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,YAAY;QACrC,MAAM,KAAK,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;QACxD,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,eAAe;QAgBb,OAAO;YACL,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC9C,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;YAChD,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,EAAE;YACtD,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YACrC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,0BAA0B,EAAE,IAAI,CAAC,0BAA0B,EAAE;YAC7D,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAC3C,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,EAAE;SACvD,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;SACrC,CAAC;IACJ,CAAC;CACF;AAhOD,gGAgOC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\normalized-event-validation-failed.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\n\r\n/**\r\n * Normalized Event Validation Failed Domain Event Data\r\n */\r\nexport interface NormalizedEventValidationFailedEventData {\r\n  /** Original event ID that failed normalization */\r\n  originalEventId: UniqueEntityId;\r\n  /** Error message describing the validation failure */\r\n  error: string;\r\n  /** Current normalization attempt number */\r\n  attempt: number;\r\n  /** Whether maximum attempts have been exceeded */\r\n  maxAttemptsExceeded: boolean;\r\n  /** Validation errors encountered */\r\n  validationErrors?: string[];\r\n  /** Failed normalization rules */\r\n  failedRules?: string[];\r\n  /** Processing duration before failure */\r\n  processingDurationMs?: number;\r\n  /** Timestamp when the failure occurred */\r\n  timestamp?: Date;\r\n}\r\n\r\n/**\r\n * Normalized Event Validation Failed Domain Event\r\n * \r\n * Raised when a normalized event fails validation during the normalization process.\r\n * This event triggers various downstream processes including:\r\n * - Error handling and retry logic\r\n * - Alert generation for failed normalizations\r\n * - Dead letter queue management\r\n * - Performance monitoring and analysis\r\n * - Manual intervention workflows\r\n */\r\nexport class NormalizedEventValidationFailedDomainEvent extends BaseDomainEvent<NormalizedEventValidationFailedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: NormalizedEventValidationFailedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the original event ID\r\n   */\r\n  get originalEventId(): UniqueEntityId {\r\n    return this.eventData.originalEventId;\r\n  }\r\n\r\n  /**\r\n   * Get the error message\r\n   */\r\n  get error(): string {\r\n    return this.eventData.error;\r\n  }\r\n\r\n  /**\r\n   * Get the current attempt number\r\n   */\r\n  get attempt(): number {\r\n    return this.eventData.attempt;\r\n  }\r\n\r\n  /**\r\n   * Check if maximum attempts have been exceeded\r\n   */\r\n  get maxAttemptsExceeded(): boolean {\r\n    return this.eventData.maxAttemptsExceeded;\r\n  }\r\n\r\n  /**\r\n   * Get validation errors\r\n   */\r\n  get validationErrors(): string[] {\r\n    return this.eventData.validationErrors || [];\r\n  }\r\n\r\n  /**\r\n   * Get failed rules\r\n   */\r\n  get failedRules(): string[] {\r\n    return this.eventData.failedRules || [];\r\n  }\r\n\r\n  /**\r\n   * Get processing duration before failure\r\n   */\r\n  get processingDurationMs(): number {\r\n    return this.eventData.processingDurationMs || 0;\r\n  }\r\n\r\n  /**\r\n   * Get the timestamp when failure occurred\r\n   */\r\n  get timestamp(): Date {\r\n    return this.eventData.timestamp || this.occurredOn;\r\n  }\r\n\r\n  /**\r\n   * Check if this is the first attempt\r\n   */\r\n  isFirstAttempt(): boolean {\r\n    return this.attempt === 1;\r\n  }\r\n\r\n  /**\r\n   * Check if retry is possible\r\n   */\r\n  canRetry(): boolean {\r\n    return !this.maxAttemptsExceeded;\r\n  }\r\n\r\n  /**\r\n   * Check if manual intervention is required\r\n   */\r\n  requiresManualIntervention(): boolean {\r\n    return this.maxAttemptsExceeded || this.validationErrors.length > 5;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a critical failure\r\n   */\r\n  isCriticalFailure(): boolean {\r\n    return this.maxAttemptsExceeded || \r\n           this.error.toLowerCase().includes('critical') ||\r\n           this.error.toLowerCase().includes('fatal');\r\n  }\r\n\r\n  /**\r\n   * Get the number of validation errors\r\n   */\r\n  getValidationErrorsCount(): number {\r\n    return this.validationErrors.length;\r\n  }\r\n\r\n  /**\r\n   * Get the number of failed rules\r\n   */\r\n  getFailedRulesCount(): number {\r\n    return this.failedRules.length;\r\n  }\r\n\r\n  /**\r\n   * Check if failure is due to schema validation\r\n   */\r\n  isSchemaValidationFailure(): boolean {\r\n    return this.error.toLowerCase().includes('schema') ||\r\n           this.validationErrors.some(error => error.toLowerCase().includes('schema'));\r\n  }\r\n\r\n  /**\r\n   * Check if failure is due to data quality issues\r\n   */\r\n  isDataQualityFailure(): boolean {\r\n    return this.error.toLowerCase().includes('quality') ||\r\n           this.error.toLowerCase().includes('invalid') ||\r\n           this.validationErrors.some(error => \r\n             error.toLowerCase().includes('quality') || \r\n             error.toLowerCase().includes('invalid')\r\n           );\r\n  }\r\n\r\n  /**\r\n   * Check if failure is due to missing required fields\r\n   */\r\n  isMissingFieldsFailure(): boolean {\r\n    return this.error.toLowerCase().includes('required') ||\r\n           this.error.toLowerCase().includes('missing') ||\r\n           this.validationErrors.some(error => \r\n             error.toLowerCase().includes('required') || \r\n             error.toLowerCase().includes('missing')\r\n           );\r\n  }\r\n\r\n  /**\r\n   * Get failure category\r\n   */\r\n  getFailureCategory(): 'schema' | 'data_quality' | 'missing_fields' | 'rule_failure' | 'unknown' {\r\n    if (this.isSchemaValidationFailure()) {\r\n      return 'schema';\r\n    }\r\n    if (this.isDataQualityFailure()) {\r\n      return 'data_quality';\r\n    }\r\n    if (this.isMissingFieldsFailure()) {\r\n      return 'missing_fields';\r\n    }\r\n    if (this.getFailedRulesCount() > 0) {\r\n      return 'rule_failure';\r\n    }\r\n    return 'unknown';\r\n  }\r\n\r\n  /**\r\n   * Get retry delay suggestion in milliseconds\r\n   */\r\n  getSuggestedRetryDelayMs(): number {\r\n    // Exponential backoff based on attempt number\r\n    const baseDelay = 1000; // 1 second\r\n    const maxDelay = 300000; // 5 minutes\r\n    const delay = baseDelay * Math.pow(2, this.attempt - 1);\r\n    return Math.min(delay, maxDelay);\r\n  }\r\n\r\n  /**\r\n   * Get event summary for handlers\r\n   */\r\n  getEventSummary(): {\r\n    normalizedEventId: string;\r\n    originalEventId: string;\r\n    error: string;\r\n    attempt: number;\r\n    maxAttemptsExceeded: boolean;\r\n    validationErrorsCount: number;\r\n    failedRulesCount: number;\r\n    processingDurationMs: number;\r\n    isFirstAttempt: boolean;\r\n    canRetry: boolean;\r\n    requiresManualIntervention: boolean;\r\n    isCriticalFailure: boolean;\r\n    failureCategory: string;\r\n    suggestedRetryDelayMs: number;\r\n  } {\r\n    return {\r\n      normalizedEventId: this.aggregateId.toString(),\r\n      originalEventId: this.originalEventId.toString(),\r\n      error: this.error,\r\n      attempt: this.attempt,\r\n      maxAttemptsExceeded: this.maxAttemptsExceeded,\r\n      validationErrorsCount: this.getValidationErrorsCount(),\r\n      failedRulesCount: this.getFailedRulesCount(),\r\n      processingDurationMs: this.processingDurationMs,\r\n      isFirstAttempt: this.isFirstAttempt(),\r\n      canRetry: this.canRetry(),\r\n      requiresManualIntervention: this.requiresManualIntervention(),\r\n      isCriticalFailure: this.isCriticalFailure(),\r\n      failureCategory: this.getFailureCategory(),\r\n      suggestedRetryDelayMs: this.getSuggestedRetryDelayMs(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventSummary: this.getEventSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}