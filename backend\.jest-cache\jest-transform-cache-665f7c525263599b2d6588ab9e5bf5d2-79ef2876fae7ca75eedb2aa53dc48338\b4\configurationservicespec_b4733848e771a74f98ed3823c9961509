e411ecfe654745d52e3d25e5add73cf5
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const configuration_1 = require("../configuration");
describe('SentinelConfigurationService', () => {
    let service;
    let configService;
    let mockConfig;
    beforeEach(async () => {
        // Create mock configuration
        mockConfig = (0, configuration_1.createSentinelConfiguration)();
        // Mock ConfigService
        configService = {
            get: jest.fn().mockReturnValue(mockConfig),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                configuration_1.SentinelConfigurationService,
                {
                    provide: config_1.ConfigService,
                    useValue: configService,
                },
            ],
        }).compile();
        service = module.get(configuration_1.SentinelConfigurationService);
    });
    afterEach(() => {
        jest.clearAllMocks();
        // Reset environment variables
        delete process.env['NODE_ENV'];
        delete process.env['PORT'];
        delete process.env['APP_NAME'];
    });
    describe('constructor', () => {
        it('should be defined', () => {
            expect(service).toBeDefined();
        });
        it('should load configuration from ConfigService', () => {
            expect(configService.get).toHaveBeenCalledWith('sentinel');
        });
    });
    describe('environment getter', () => {
        it('should return environment configuration', () => {
            const result = service.environment;
            expect(result).toEqual(mockConfig.environment);
            expect(result).toHaveProperty('environment');
            expect(result).toHaveProperty('isDevelopment');
            expect(result).toHaveProperty('isProduction');
            expect(result).toHaveProperty('isTest');
            expect(result).toHaveProperty('isStaging');
        });
    });
    describe('application getter', () => {
        it('should return application configuration', () => {
            const result = service.application;
            expect(result).toEqual(mockConfig.application);
            expect(result).toHaveProperty('name');
            expect(result).toHaveProperty('version');
            expect(result).toHaveProperty('port');
            expect(result).toHaveProperty('globalPrefix');
        });
    });
    describe('database getter', () => {
        it('should return database configuration', () => {
            const result = service.database;
            expect(result).toEqual(mockConfig.database);
            expect(result).toHaveProperty('type');
            expect(result).toHaveProperty('host');
            expect(result).toHaveProperty('port');
            expect(result).toHaveProperty('username');
            expect(result).toHaveProperty('password');
            expect(result).toHaveProperty('database');
        });
    });
    describe('redis getter', () => {
        it('should return redis configuration', () => {
            const result = service.redis;
            expect(result).toEqual(mockConfig.redis);
            expect(result).toHaveProperty('host');
            expect(result).toHaveProperty('port');
            expect(result).toHaveProperty('db');
        });
    });
    describe('security getter', () => {
        it('should return security configuration', () => {
            const result = service.security;
            expect(result).toEqual(mockConfig.security);
            expect(result).toHaveProperty('jwtSecret');
            expect(result).toHaveProperty('jwtExpiresIn');
            expect(result).toHaveProperty('bcryptRounds');
        });
    });
    describe('getAll', () => {
        it('should return complete configuration', () => {
            const result = service.getAll();
            expect(result).toEqual(mockConfig);
            expect(result).toHaveProperty('environment');
            expect(result).toHaveProperty('application');
            expect(result).toHaveProperty('database');
            expect(result).toHaveProperty('redis');
            expect(result).toHaveProperty('security');
        });
    });
    describe('environment check methods', () => {
        it('should correctly identify development environment', () => {
            mockConfig.environment.isDevelopment = true;
            mockConfig.environment.isProduction = false;
            mockConfig.environment.isTest = false;
            mockConfig.environment.isStaging = false;
            expect(service.isDevelopment()).toBe(true);
            expect(service.isProduction()).toBe(false);
            expect(service.isTest()).toBe(false);
            expect(service.isStaging()).toBe(false);
        });
        it('should correctly identify production environment', () => {
            mockConfig.environment.isDevelopment = false;
            mockConfig.environment.isProduction = true;
            mockConfig.environment.isTest = false;
            mockConfig.environment.isStaging = false;
            expect(service.isDevelopment()).toBe(false);
            expect(service.isProduction()).toBe(true);
            expect(service.isTest()).toBe(false);
            expect(service.isStaging()).toBe(false);
        });
        it('should correctly identify test environment', () => {
            mockConfig.environment.isDevelopment = false;
            mockConfig.environment.isProduction = false;
            mockConfig.environment.isTest = true;
            mockConfig.environment.isStaging = false;
            expect(service.isDevelopment()).toBe(false);
            expect(service.isProduction()).toBe(false);
            expect(service.isTest()).toBe(true);
            expect(service.isStaging()).toBe(false);
        });
        it('should correctly identify staging environment', () => {
            mockConfig.environment.isDevelopment = false;
            mockConfig.environment.isProduction = false;
            mockConfig.environment.isTest = false;
            mockConfig.environment.isStaging = true;
            expect(service.isDevelopment()).toBe(false);
            expect(service.isProduction()).toBe(false);
            expect(service.isTest()).toBe(false);
            expect(service.isStaging()).toBe(true);
        });
    });
});
describe('Configuration Factory Functions', () => {
    beforeEach(() => {
        // Reset environment variables before each test
        Object.keys(process.env).forEach(key => {
            if (key.startsWith('NODE_ENV') || key.startsWith('APP_') || key.startsWith('DATABASE_') ||
                key.startsWith('REDIS_') || key.startsWith('JWT_') || key.startsWith('PORT')) {
                delete process.env[key];
            }
        });
    });
    describe('createEnvironmentConfig', () => {
        it('should create development environment config by default', () => {
            const config = (0, configuration_1.createEnvironmentConfig)();
            expect(config.environment).toBe('development');
            expect(config.isDevelopment).toBe(true);
            expect(config.isProduction).toBe(false);
            expect(config.isTest).toBe(false);
            expect(config.isStaging).toBe(false);
            expect(config.debug).toBe(true);
            expect(config.hotReload).toBe(true);
        });
        it('should create production environment config', () => {
            process.env['NODE_ENV'] = 'production';
            const config = (0, configuration_1.createEnvironmentConfig)();
            expect(config.environment).toBe('production');
            expect(config.isDevelopment).toBe(false);
            expect(config.isProduction).toBe(true);
            expect(config.isTest).toBe(false);
            expect(config.isStaging).toBe(false);
            expect(config.debug).toBe(false);
            expect(config.hotReload).toBe(false);
        });
        it('should create test environment config', () => {
            process.env['NODE_ENV'] = 'test';
            const config = (0, configuration_1.createEnvironmentConfig)();
            expect(config.environment).toBe('test');
            expect(config.isDevelopment).toBe(false);
            expect(config.isProduction).toBe(false);
            expect(config.isTest).toBe(true);
            expect(config.isStaging).toBe(false);
            expect(config.debug).toBe(true);
            expect(config.hotReload).toBe(false);
        });
        it('should create staging environment config', () => {
            process.env['NODE_ENV'] = 'staging';
            const config = (0, configuration_1.createEnvironmentConfig)();
            expect(config.environment).toBe('staging');
            expect(config.isDevelopment).toBe(false);
            expect(config.isProduction).toBe(false);
            expect(config.isTest).toBe(false);
            expect(config.isStaging).toBe(true);
            expect(config.debug).toBe(false);
            expect(config.hotReload).toBe(false);
        });
    });
    describe('createApplicationConfig', () => {
        it('should create application config with defaults', () => {
            const config = (0, configuration_1.createApplicationConfig)();
            expect(config.name).toBe('Sentinel Backend');
            expect(config.version).toBe('1.0.0');
            expect(config.description).toBe('Sentinel Vulnerability Assessment Platform Backend');
            expect(config.port).toBe(3000);
            expect(config.globalPrefix).toBe('api');
            expect(config.corsOrigin).toEqual(['http://localhost:3001']);
            expect(config.trustProxy).toBe(false);
            expect(config.shutdownTimeout).toBe(10000);
        });
        it('should create application config from environment variables', () => {
            process.env['APP_NAME'] = 'Test App';
            process.env['APP_VERSION'] = '2.0.0';
            process.env['APP_DESCRIPTION'] = 'Test Description';
            process.env['PORT'] = '4000';
            process.env['API_PREFIX'] = 'v1';
            process.env['CORS_ORIGIN'] = 'http://localhost:3000,http://localhost:3001';
            process.env['TRUST_PROXY'] = 'true';
            process.env['SHUTDOWN_TIMEOUT'] = '5000';
            const config = (0, configuration_1.createApplicationConfig)();
            expect(config.name).toBe('Test App');
            expect(config.version).toBe('2.0.0');
            expect(config.description).toBe('Test Description');
            expect(config.port).toBe(4000);
            expect(config.globalPrefix).toBe('v1');
            expect(config.corsOrigin).toEqual(['http://localhost:3000', 'http://localhost:3001']);
            expect(config.trustProxy).toBe(true);
            expect(config.shutdownTimeout).toBe(5000);
        });
        it('should handle invalid port gracefully', () => {
            process.env['PORT'] = 'invalid';
            const config = (0, configuration_1.createApplicationConfig)();
            expect(config.port).toBe(3000); // Should fallback to default
        });
    });
    describe('createDatabaseConfig', () => {
        it('should create database config with defaults', () => {
            const config = (0, configuration_1.createDatabaseConfig)();
            expect(config.type).toBe('postgres');
            expect(config.host).toBe('localhost');
            expect(config.port).toBe(5432);
            expect(config.username).toBe('sentinel');
            expect(config.password).toBe('password');
            expect(config.database).toBe('sentinel');
            expect(config.schema).toBe('public');
            expect(config.ssl).toBe(false);
            expect(config.maxConnections).toBe(100);
        });
        it('should create database config from environment variables', () => {
            process.env['DATABASE_TYPE'] = 'mysql';
            process.env['DATABASE_HOST'] = 'db.example.com';
            process.env['DATABASE_PORT'] = '3306';
            process.env['DATABASE_USERNAME'] = 'testuser';
            process.env['DATABASE_PASSWORD'] = 'testpass';
            process.env['DATABASE_NAME'] = 'testdb';
            process.env['DATABASE_SCHEMA'] = 'testschema';
            process.env['DATABASE_SSL'] = 'true';
            process.env['DATABASE_MAX_CONNECTIONS'] = '50';
            const config = (0, configuration_1.createDatabaseConfig)();
            expect(config.type).toBe('mysql');
            expect(config.host).toBe('db.example.com');
            expect(config.port).toBe(3306);
            expect(config.username).toBe('testuser');
            expect(config.password).toBe('testpass');
            expect(config.database).toBe('testdb');
            expect(config.schema).toBe('testschema');
            expect(config.ssl).toBe(true);
            expect(config.maxConnections).toBe(50);
        });
        it('should handle invalid port gracefully', () => {
            process.env['DATABASE_PORT'] = 'invalid';
            const config = (0, configuration_1.createDatabaseConfig)();
            expect(config.port).toBe(5432); // Should fallback to default
        });
    });
    describe('createRedisConfig', () => {
        it('should create redis config with defaults', () => {
            const config = (0, configuration_1.createRedisConfig)();
            expect(config.host).toBe('localhost');
            expect(config.port).toBe(6379);
            expect(config.password).toBeUndefined();
            expect(config.db).toBe(0);
            expect(config.ttl).toBe(3600);
            expect(config.maxRetries).toBe(3);
            expect(config.family).toBe(4);
        });
        it('should create redis config from environment variables', () => {
            process.env['REDIS_HOST'] = 'redis.example.com';
            process.env['REDIS_PORT'] = '6380';
            process.env['REDIS_PASSWORD'] = 'redispass';
            process.env['REDIS_DB'] = '1';
            process.env['REDIS_TTL'] = '7200';
            process.env['REDIS_MAX_RETRIES'] = '5';
            process.env['REDIS_FAMILY'] = '6';
            const config = (0, configuration_1.createRedisConfig)();
            expect(config.host).toBe('redis.example.com');
            expect(config.port).toBe(6380);
            expect(config.password).toBe('redispass');
            expect(config.db).toBe(1);
            expect(config.ttl).toBe(7200);
            expect(config.maxRetries).toBe(5);
            expect(config.family).toBe(6);
        });
    });
    describe('createSecurityConfig', () => {
        it('should create security config with defaults', () => {
            const config = (0, configuration_1.createSecurityConfig)();
            expect(config.jwtSecret).toBe('your-super-secret-jwt-key-change-in-production');
            expect(config.jwtExpiresIn).toBe('1h');
            expect(config.jwtRefreshSecret).toBe('your-super-secret-refresh-key-change-in-production');
            expect(config.jwtRefreshExpiresIn).toBe('7d');
            expect(config.bcryptRounds).toBe(12);
            expect(config.sessionSecret).toBe('your-super-secret-session-key-change-in-production');
            expect(config.rateLimitTtl).toBe(60);
            expect(config.rateLimitLimit).toBe(100);
            expect(config.corsEnabled).toBe(true);
            expect(config.frameOptions).toBe('DENY');
        });
        it('should create security config from environment variables', () => {
            process.env['JWT_SECRET'] = 'custom-jwt-secret';
            process.env['JWT_EXPIRES_IN'] = '2h';
            process.env['JWT_REFRESH_SECRET'] = 'custom-refresh-secret';
            process.env['JWT_REFRESH_EXPIRES_IN'] = '14d';
            process.env['BCRYPT_ROUNDS'] = '10';
            process.env['SESSION_SECRET'] = 'custom-session-secret';
            process.env['RATE_LIMIT_TTL'] = '120';
            process.env['RATE_LIMIT_LIMIT'] = '200';
            process.env['CORS_ENABLED'] = 'false';
            process.env['SECURITY_FRAME_OPTIONS'] = 'SAMEORIGIN';
            const config = (0, configuration_1.createSecurityConfig)();
            expect(config.jwtSecret).toBe('custom-jwt-secret');
            expect(config.jwtExpiresIn).toBe('2h');
            expect(config.jwtRefreshSecret).toBe('custom-refresh-secret');
            expect(config.jwtRefreshExpiresIn).toBe('14d');
            expect(config.bcryptRounds).toBe(10);
            expect(config.sessionSecret).toBe('custom-session-secret');
            expect(config.rateLimitTtl).toBe(120);
            expect(config.rateLimitLimit).toBe(200);
            expect(config.corsEnabled).toBe(false);
            expect(config.frameOptions).toBe('SAMEORIGIN');
        });
    });
    describe('createSentinelConfiguration', () => {
        it('should create complete configuration', () => {
            const config = (0, configuration_1.createSentinelConfiguration)();
            expect(config).toHaveProperty('environment');
            expect(config).toHaveProperty('application');
            expect(config).toHaveProperty('database');
            expect(config).toHaveProperty('redis');
            expect(config).toHaveProperty('security');
            expect(config).toHaveProperty('logging');
            expect(config).toHaveProperty('monitoring');
            expect(config).toHaveProperty('aiService');
            expect(config).toHaveProperty('externalServices');
            expect(config).toHaveProperty('cache');
            expect(config).toHaveProperty('validation');
            expect(config).toHaveProperty('fileUpload');
            expect(config).toHaveProperty('pagination');
            expect(config).toHaveProperty('development');
        });
        it('should have consistent environment across all sub-configs', () => {
            process.env['NODE_ENV'] = 'production';
            const config = (0, configuration_1.createSentinelConfiguration)();
            expect(config.environment.environment).toBe('production');
            expect(config.environment.isProduction).toBe(true);
            expect(config.development.enableHotReload).toBe(false);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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