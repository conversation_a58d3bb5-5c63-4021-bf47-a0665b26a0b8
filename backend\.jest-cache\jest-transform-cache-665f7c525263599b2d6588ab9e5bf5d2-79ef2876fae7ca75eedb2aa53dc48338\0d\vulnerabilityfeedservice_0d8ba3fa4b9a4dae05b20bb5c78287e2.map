{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\vulnerability-feed.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,6CAAmD;AACnD,qCAA+C;AAC/C,+CAAwD;AACxD,+FAAoF;AACpF,qFAA2E;AAC3E,sFAAkF;AAClF,0FAAsF;AACtF,uGAAmG;AAEnG;;;GAGG;AAEI,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAGnC,YAEE,cAA8D,EAE9D,uBAAmE,EAClD,aAA4B,EAC5B,YAA0B,EAC1B,mBAAwC;QALxC,mBAAc,GAAd,cAAc,CAA+B;QAE7C,4BAAuB,GAAvB,uBAAuB,CAA2B;QAClD,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;QAC1B,wBAAmB,GAAnB,mBAAmB,CAAqB;QAT1C,WAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;IAUjE,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,QAWhB,EAAE,MAAc;QACf,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;gBACtC,GAAG,QAAQ;gBACX,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,oBAAoB,EACpB,SAAS,CAAC,EAAE,EACZ;gBACE,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;aAC5B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,EAAE;gBACzD,MAAM,EAAE,SAAS,CAAC,EAAE;gBACpB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,EAAU;QAC7B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBAC9C,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAYjB;QAMC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC;YAChC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;YACnD,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAElC,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAEpE,gBAAgB;YAChB,IAAI,QAAQ,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC;gBAC/B,YAAY,CAAC,QAAQ,CAAC,kCAAkC,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;YAC/F,CAAC;YAED,IAAI,QAAQ,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC;gBAC/B,YAAY,CAAC,QAAQ,CAAC,kCAAkC,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;YAC/F,CAAC;YAED,IAAI,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;gBAC9B,YAAY,CAAC,QAAQ,CAAC,+BAA+B,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1F,CAAC;YAED,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACpC,YAAY,CAAC,QAAQ,CAAC,2BAA2B,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtF,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;gBAChC,YAAY,CAAC,QAAQ,CAAC,mCAAmC,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YAClG,CAAC;YAED,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;gBAC1B,YAAY,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YACvE,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACxB,YAAY,CAAC,QAAQ,CACnB,wGAAwG,EACxG,EAAE,UAAU,EAAE,IAAI,QAAQ,CAAC,UAAU,GAAG,EAAE,CAC3C,CAAC;YACJ,CAAC;YAED,gBAAgB;YAChB,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,WAAW,CAAC;YAC9C,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,IAAI,MAAM,CAAC;YAC/C,YAAY,CAAC,OAAO,CAAC,QAAQ,MAAM,EAAE,EAAE,SAAS,CAAC,CAAC;YAElD,mBAAmB;YACnB,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;YAE5D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM;aAC5C,CAAC,CAAC;YAEH,OAAO;gBACL,KAAK;gBACL,KAAK;gBACL,IAAI;gBACJ,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACrC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,EAAU,EAAE,OAAY,EAAE,MAAc;QACvD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE;gBACjC,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;aACP,CAAC,CAAC;YAEH,0BAA0B;YAC1B,MAAM,OAAO,GAAG,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACjC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC/B,OAAO,CAAC,GAAG,CAAC,GAAG;wBACb,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC;wBACf,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC;qBACjB,CAAC;gBACJ,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAE7B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,oBAAoB,EACpB,EAAE,EACF;gBACE,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,OAAO;aACR,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,EAAE;gBAC3C,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM;gBACzC,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,MAAc;QAC3C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE;gBACnC,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;aACP,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,UAAU,EACV,oBAAoB,EACpB,EAAE,EACF;gBACE,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,EAAE;gBAC7C,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBAC3C,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,EAAU,EAAE,MAAc;QAC7C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE;gBACrC,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;aACP,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,YAAY,EACZ,oBAAoB,EACpB,EAAE,EACF;gBACE,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,EAAE;gBAC/C,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,EAAU,EAAE,MAAc;QACvC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,MAAM,EACN,oBAAoB,EACpB,EAAE,EACF;gBACE,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,gBAAgB,EAAE,UAAU,CAAC,gBAAgB;gBAC7C,YAAY,EAAE,UAAU,CAAC,YAAY;gBACrC,cAAc,EAAE,UAAU,CAAC,cAAc;aAC1C,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,EAAE;gBAClD,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,gBAAgB,EAAE,UAAU,CAAC,gBAAgB;gBAC7C,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;gBACvC,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,EAAU,EAAE,MAAc;QACzC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE;gBACjC,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAEvC,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,oBAAoB,EACpB,EAAE,EACF;gBACE,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,EAAE;gBAC3C,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IAEG,AAAN,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAExD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBACrD,KAAK,EAAE;oBACL,QAAQ,EAAE,IAAI;oBACd,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,IAAA,kBAAQ,EAAC,IAAI,IAAI,EAAE,CAAC;iBACjC;aACF,CAAC,CAAC;YAEH,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;gBACnC,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACnC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;wBACzD,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,QAAQ,EAAE,IAAI,CAAC,IAAI;wBACnB,KAAK,EAAE,KAAK,CAAC,OAAO;qBACrB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,EAAE;oBAChD,KAAK,EAAE,eAAe,CAAC,MAAM;iBAC9B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBAC3C,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,IAAuB;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAErC,iEAAiE;YACjE,kCAAkC;YAClC,iDAAiD;YACjD,qCAAqC;YACrC,sCAAsC;YACtC,sBAAsB;YAEtB,MAAM,WAAW,GAAG;gBAClB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;gBACrD,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE;gBACtD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;gBAChD,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;gBAClD,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC;aAC/C,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAC/B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAErC,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC7B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AArhBY,4DAAwB;AA8c7B;IADL,IAAA,eAAI,EAAC,yBAAc,CAAC,eAAe,CAAC;;;wDACf,OAAO,oBAAP,OAAO;4DAkC5B;mCAhfU,wBAAwB;IADpC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,6CAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,oCAAa,CAAC,CAAA;yDADC,oBAAU,oBAAV,oBAAU,oDAED,oBAAU,oBAAV,oBAAU,oDACpB,8BAAa,oBAAb,8BAAa,oDACd,4BAAY,oBAAZ,4BAAY,oDACL,0CAAmB,oBAAnB,0CAAmB;GAVhD,wBAAwB,CAqhBpC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\vulnerability-feed.service.ts"], "sourcesContent": ["import { Injectable, Logger, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, LessThan } from 'typeorm';\r\nimport { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';\r\nimport { VulnerabilityFeed } from '../../domain/entities/vulnerability-feed.entity';\r\nimport { Vulnerability } from '../../domain/entities/vulnerability.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../infrastructure/notification/notification.service';\r\n\r\n/**\r\n * Vulnerability Feed service\r\n * Handles vulnerability feed management and synchronization\r\n */\r\n@Injectable()\r\nexport class VulnerabilityFeedService {\r\n  private readonly logger = new Logger(VulnerabilityFeedService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(VulnerabilityFeed)\r\n    private readonly feedRepository: Repository<VulnerabilityFeed>,\r\n    @InjectRepository(Vulnerability)\r\n    private readonly vulnerabilityRepository: Repository<Vulnerability>,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly auditService: AuditService,\r\n    private readonly notificationService: NotificationService,\r\n  ) {}\r\n\r\n  /**\r\n   * Create vulnerability feed\r\n   */\r\n  async createFeed(feedData: {\r\n    name: string;\r\n    description?: string;\r\n    feedType: string;\r\n    provider?: string;\r\n    url: string;\r\n    format: string;\r\n    authConfig?: any;\r\n    feedConfig: any;\r\n    priority?: string;\r\n    tags?: string[];\r\n  }, userId: string): Promise<VulnerabilityFeed> {\r\n    try {\r\n      this.logger.debug('Creating vulnerability feed', {\r\n        name: feedData.name,\r\n        feedType: feedData.feedType,\r\n        provider: feedData.provider,\r\n        userId,\r\n      });\r\n\r\n      const feed = this.feedRepository.create({\r\n        ...feedData,\r\n        status: 'inactive',\r\n        isActive: false,\r\n      });\r\n\r\n      const savedFeed = await this.feedRepository.save(feed);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'create',\r\n        'vulnerability_feed',\r\n        savedFeed.id,\r\n        {\r\n          name: feedData.name,\r\n          feedType: feedData.feedType,\r\n          provider: feedData.provider,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Vulnerability feed created successfully', {\r\n        feedId: savedFeed.id,\r\n        name: feedData.name,\r\n        userId,\r\n      });\r\n\r\n      return savedFeed;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create vulnerability feed', {\r\n        error: error.message,\r\n        feedData,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get feed details\r\n   */\r\n  async getFeedDetails(id: string): Promise<VulnerabilityFeed> {\r\n    try {\r\n      const feed = await this.feedRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!feed) {\r\n        throw new NotFoundException('Feed not found');\r\n      }\r\n\r\n      this.logger.debug('Feed details retrieved', {\r\n        feedId: id,\r\n        name: feed.name,\r\n        status: feed.status,\r\n      });\r\n\r\n      return feed;\r\n    } catch (error) {\r\n      this.logger.error('Failed to get feed details', {\r\n        feedId: id,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Search feeds with filtering\r\n   */\r\n  async searchFeeds(criteria: {\r\n    page?: number;\r\n    limit?: number;\r\n    feedTypes?: string[];\r\n    providers?: string[];\r\n    statuses?: string[];\r\n    isActive?: boolean;\r\n    priorities?: string[];\r\n    tags?: string[];\r\n    searchText?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n  }): Promise<{\r\n    feeds: VulnerabilityFeed[];\r\n    total: number;\r\n    page: number;\r\n    totalPages: number;\r\n  }> {\r\n    try {\r\n      const page = criteria.page || 1;\r\n      const limit = Math.min(criteria.limit || 50, 1000);\r\n      const offset = (page - 1) * limit;\r\n\r\n      const queryBuilder = this.feedRepository.createQueryBuilder('feed');\r\n\r\n      // Apply filters\r\n      if (criteria.feedTypes?.length) {\r\n        queryBuilder.andWhere('feed.feedType IN (:...feedTypes)', { feedTypes: criteria.feedTypes });\r\n      }\r\n\r\n      if (criteria.providers?.length) {\r\n        queryBuilder.andWhere('feed.provider IN (:...providers)', { providers: criteria.providers });\r\n      }\r\n\r\n      if (criteria.statuses?.length) {\r\n        queryBuilder.andWhere('feed.status IN (:...statuses)', { statuses: criteria.statuses });\r\n      }\r\n\r\n      if (criteria.isActive !== undefined) {\r\n        queryBuilder.andWhere('feed.isActive = :isActive', { isActive: criteria.isActive });\r\n      }\r\n\r\n      if (criteria.priorities?.length) {\r\n        queryBuilder.andWhere('feed.priority IN (:...priorities)', { priorities: criteria.priorities });\r\n      }\r\n\r\n      if (criteria.tags?.length) {\r\n        queryBuilder.andWhere('feed.tags && :tags', { tags: criteria.tags });\r\n      }\r\n\r\n      if (criteria.searchText) {\r\n        queryBuilder.andWhere(\r\n          '(feed.name ILIKE :searchText OR feed.description ILIKE :searchText OR feed.provider ILIKE :searchText)',\r\n          { searchText: `%${criteria.searchText}%` }\r\n        );\r\n      }\r\n\r\n      // Apply sorting\r\n      const sortBy = criteria.sortBy || 'createdAt';\r\n      const sortOrder = criteria.sortOrder || 'DESC';\r\n      queryBuilder.orderBy(`feed.${sortBy}`, sortOrder);\r\n\r\n      // Apply pagination\r\n      queryBuilder.skip(offset).take(limit);\r\n\r\n      const [feeds, total] = await queryBuilder.getManyAndCount();\r\n\r\n      this.logger.debug('Feed search completed', {\r\n        total,\r\n        page,\r\n        limit,\r\n        criteriaCount: Object.keys(criteria).length,\r\n      });\r\n\r\n      return {\r\n        feeds,\r\n        total,\r\n        page,\r\n        totalPages: Math.ceil(total / limit),\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to search feeds', {\r\n        error: error.message,\r\n        criteria,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update feed\r\n   */\r\n  async updateFeed(id: string, updates: any, userId: string): Promise<VulnerabilityFeed> {\r\n    try {\r\n      const feed = await this.feedRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!feed) {\r\n        throw new NotFoundException('Feed not found');\r\n      }\r\n\r\n      this.logger.debug('Updating feed', {\r\n        feedId: id,\r\n        name: feed.name,\r\n        userId,\r\n      });\r\n\r\n      // Track changes for audit\r\n      const changes = {};\r\n      Object.keys(updates).forEach(key => {\r\n        if (feed[key] !== updates[key]) {\r\n          changes[key] = {\r\n            from: feed[key],\r\n            to: updates[key],\r\n          };\r\n        }\r\n      });\r\n\r\n      Object.assign(feed, updates);\r\n\r\n      const savedFeed = await this.feedRepository.save(feed);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'update',\r\n        'vulnerability_feed',\r\n        id,\r\n        {\r\n          name: feed.name,\r\n          changes,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Feed updated successfully', {\r\n        feedId: id,\r\n        name: feed.name,\r\n        changesCount: Object.keys(changes).length,\r\n        userId,\r\n      });\r\n\r\n      return savedFeed;\r\n    } catch (error) {\r\n      this.logger.error('Failed to update feed', {\r\n        feedId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Activate feed\r\n   */\r\n  async activateFeed(id: string, userId: string): Promise<VulnerabilityFeed> {\r\n    try {\r\n      const feed = await this.feedRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!feed) {\r\n        throw new NotFoundException('Feed not found');\r\n      }\r\n\r\n      this.logger.debug('Activating feed', {\r\n        feedId: id,\r\n        name: feed.name,\r\n        userId,\r\n      });\r\n\r\n      feed.activate();\r\n      const savedFeed = await this.feedRepository.save(feed);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'activate',\r\n        'vulnerability_feed',\r\n        id,\r\n        {\r\n          name: feed.name,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Feed activated successfully', {\r\n        feedId: id,\r\n        name: feed.name,\r\n        userId,\r\n      });\r\n\r\n      return savedFeed;\r\n    } catch (error) {\r\n      this.logger.error('Failed to activate feed', {\r\n        feedId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deactivate feed\r\n   */\r\n  async deactivateFeed(id: string, userId: string): Promise<VulnerabilityFeed> {\r\n    try {\r\n      const feed = await this.feedRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!feed) {\r\n        throw new NotFoundException('Feed not found');\r\n      }\r\n\r\n      this.logger.debug('Deactivating feed', {\r\n        feedId: id,\r\n        name: feed.name,\r\n        userId,\r\n      });\r\n\r\n      feed.deactivate();\r\n      const savedFeed = await this.feedRepository.save(feed);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'deactivate',\r\n        'vulnerability_feed',\r\n        id,\r\n        {\r\n          name: feed.name,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Feed deactivated successfully', {\r\n        feedId: id,\r\n        name: feed.name,\r\n        userId,\r\n      });\r\n\r\n      return savedFeed;\r\n    } catch (error) {\r\n      this.logger.error('Failed to deactivate feed', {\r\n        feedId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sync feed manually\r\n   */\r\n  async syncFeed(id: string, userId: string): Promise<any> {\r\n    try {\r\n      const feed = await this.feedRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!feed) {\r\n        throw new NotFoundException('Feed not found');\r\n      }\r\n\r\n      if (!feed.isActive) {\r\n        throw new Error('Cannot sync inactive feed');\r\n      }\r\n\r\n      this.logger.debug('Starting manual feed sync', {\r\n        feedId: id,\r\n        name: feed.name,\r\n        userId,\r\n      });\r\n\r\n      const syncResult = await this.performFeedSync(feed);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'sync',\r\n        'vulnerability_feed',\r\n        id,\r\n        {\r\n          name: feed.name,\r\n          recordsProcessed: syncResult.recordsProcessed,\r\n          recordsAdded: syncResult.recordsAdded,\r\n          recordsUpdated: syncResult.recordsUpdated,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Feed sync completed successfully', {\r\n        feedId: id,\r\n        name: feed.name,\r\n        recordsProcessed: syncResult.recordsProcessed,\r\n        userId,\r\n      });\r\n\r\n      return syncResult;\r\n    } catch (error) {\r\n      this.logger.error('Failed to sync feed', {\r\n        feedId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete feed\r\n   */\r\n  async deleteFeed(id: string, userId: string): Promise<void> {\r\n    try {\r\n      const feed = await this.feedRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!feed) {\r\n        throw new NotFoundException('Feed not found');\r\n      }\r\n\r\n      this.logger.debug('Deleting feed', {\r\n        feedId: id,\r\n        name: feed.name,\r\n        userId,\r\n      });\r\n\r\n      await this.feedRepository.remove(feed);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'delete',\r\n        'vulnerability_feed',\r\n        id,\r\n        {\r\n          name: feed.name,\r\n          feedType: feed.feedType,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Feed deleted successfully', {\r\n        feedId: id,\r\n        name: feed.name,\r\n        userId,\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to delete feed', {\r\n        feedId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Monitor feeds for scheduled synchronization\r\n   */\r\n  @Cron(CronExpression.EVERY_5_MINUTES)\r\n  async monitorFeeds(): Promise<void> {\r\n    try {\r\n      this.logger.debug('Checking feeds for synchronization');\r\n\r\n      const feedsDueForSync = await this.feedRepository.find({\r\n        where: {\r\n          isActive: true,\r\n          status: 'active',\r\n          nextSyncAt: LessThan(new Date()),\r\n        },\r\n      });\r\n\r\n      for (const feed of feedsDueForSync) {\r\n        try {\r\n          await this.performFeedSync(feed);\r\n        } catch (error) {\r\n          this.logger.error('Failed to sync feed during monitoring', {\r\n            feedId: feed.id,\r\n            feedName: feed.name,\r\n            error: error.message,\r\n          });\r\n        }\r\n      }\r\n\r\n      if (feedsDueForSync.length > 0) {\r\n        this.logger.log('Processed scheduled feed syncs', {\r\n          count: feedsDueForSync.length,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      this.logger.error('Failed to monitor feeds', {\r\n        error: error.message,\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Perform feed synchronization\r\n   */\r\n  private async performFeedSync(feed: VulnerabilityFeed): Promise<any> {\r\n    const startTime = Date.now();\r\n    \r\n    try {\r\n      feed.startSync();\r\n      await this.feedRepository.save(feed);\r\n\r\n      // Simulate feed processing - in real implementation, this would:\r\n      // 1. Fetch data from the feed URL\r\n      // 2. Parse the data according to the feed format\r\n      // 3. Validate and transform the data\r\n      // 4. Create or update vulnerabilities\r\n      // 5. Track statistics\r\n\r\n      const mockResults = {\r\n        duration: Math.floor((Date.now() - startTime) / 1000),\r\n        recordsProcessed: Math.floor(Math.random() * 100) + 10,\r\n        recordsAdded: Math.floor(Math.random() * 20) + 1,\r\n        recordsUpdated: Math.floor(Math.random() * 30) + 5,\r\n        recordsSkipped: Math.floor(Math.random() * 10),\r\n      };\r\n\r\n      feed.completeSync(mockResults);\r\n      await this.feedRepository.save(feed);\r\n\r\n      return mockResults;\r\n    } catch (error) {\r\n      feed.failSync(error.message);\r\n      await this.feedRepository.save(feed);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}