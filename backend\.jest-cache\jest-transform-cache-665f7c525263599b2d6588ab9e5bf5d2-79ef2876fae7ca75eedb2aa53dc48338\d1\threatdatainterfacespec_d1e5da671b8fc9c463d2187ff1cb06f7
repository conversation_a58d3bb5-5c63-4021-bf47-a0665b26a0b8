52711b2741331af832aa3cef85d63359
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const threat_data_interface_1 = require("../threat-data.interface");
const threat_severity_enum_1 = require("../../../enums/threat-severity.enum");
const confidence_level_enum_1 = require("../../../enums/confidence-level.enum");
describe('ThreatData Interface', () => {
    let mockThreatData;
    let mockIndicators;
    let mockCVSSScores;
    let mockAttribution;
    let mockMalwareFamily;
    let mockAttackTechniques;
    let mockTimeline;
    let mockMitigations;
    let mockDetectionRules;
    let mockSharingInfo;
    let mockQualityMetrics;
    beforeEach(() => {
        mockIndicators = [
            {
                type: threat_data_interface_1.ThreatIndicatorType.IP_ADDRESS,
                value: '*************',
                description: 'Malicious IP address',
                confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                firstSeen: '2024-01-01T00:00:00Z',
                lastSeen: '2024-01-02T00:00:00Z',
                status: threat_data_interface_1.ThreatIndicatorStatus.ACTIVE,
                tags: ['malware', 'c2'],
                context: { source: 'honeypot' },
            },
            {
                type: threat_data_interface_1.ThreatIndicatorType.DOMAIN,
                value: 'malicious.com',
                description: 'Command and control domain',
                confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                firstSeen: '2024-01-01T00:00:00Z',
                lastSeen: '2024-01-02T00:00:00Z',
                status: threat_data_interface_1.ThreatIndicatorStatus.ACTIVE,
                tags: ['c2', 'apt'],
            },
        ];
        mockCVSSScores = [
            {
                version: '3.1',
                score: 8.5,
                vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',
                severity: 'HIGH',
                exploitabilityScore: 3.9,
                impactScore: 5.9,
            },
        ];
        mockAttribution = {
            actorName: 'APT29',
            aliases: ['Cozy Bear', 'The Dukes'],
            actorType: threat_data_interface_1.ThreatActorType.NATION_STATE,
            country: 'RU',
            motivation: [threat_data_interface_1.ThreatActorMotivation.ESPIONAGE],
            sophistication: threat_data_interface_1.ThreatActorSophistication.ADVANCED,
            confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
            evidence: ['TTPs match known APT29 campaigns'],
        };
        mockMalwareFamily = {
            name: 'Cobalt Strike',
            aliases: ['CS', 'CobaltStrike'],
            type: threat_data_interface_1.MalwareType.RAT,
            platforms: ['Windows', 'Linux'],
            capabilities: ['Remote Access', 'Lateral Movement'],
            variants: ['v4.0', 'v4.1'],
        };
        mockAttackTechniques = [
            {
                techniqueId: 'T1055',
                name: 'Process Injection',
                tactic: 'Defense Evasion',
                subTechniques: ['T1055.001', 'T1055.002'],
                platforms: ['Windows'],
                dataSources: ['Process monitoring'],
                mitigations: ['M1040'],
                detections: ['Monitor for process injection'],
            },
        ];
        mockTimeline = {
            campaignStart: '2024-01-01T00:00:00Z',
            campaignEnd: '2024-01-31T23:59:59Z',
            events: [
                {
                    timestamp: '2024-01-01T00:00:00Z',
                    type: 'initial_compromise',
                    description: 'Initial compromise detected',
                    severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                    indicators: ['*************'],
                },
            ],
            duration: 31,
            patterns: [
                {
                    type: 'temporal',
                    description: 'Activity peaks during business hours',
                    confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                    data: { peak_hours: '09:00-17:00' },
                },
            ],
        };
        mockMitigations = [
            {
                type: threat_data_interface_1.ThreatMitigationType.PREVENTIVE,
                description: 'Block malicious IP addresses',
                difficulty: 'low',
                effectiveness: 'high',
                cost: 'low',
                technologies: ['Firewall', 'IPS'],
                steps: ['Add IP to blocklist', 'Monitor for bypass attempts'],
            },
        ];
        mockDetectionRules = [
            {
                type: threat_data_interface_1.ThreatDetectionRuleType.YARA,
                name: 'Cobalt Strike Beacon',
                content: 'rule CobaltStrike_Beacon { ... }',
                format: 'yara',
                confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                falsePositiveRate: 'low',
                tags: ['malware', 'cobalt-strike'],
                references: ['https://example.com/analysis'],
            },
        ];
        mockSharingInfo = {
            tlp: threat_data_interface_1.TLPMarking.AMBER,
            restrictions: ['No public sharing'],
            permittedActions: [threat_data_interface_1.ThreatSharingAction.ANALYZE, threat_data_interface_1.ThreatSharingAction.DETECT],
            agreementRef: 'sharing-agreement-001',
            copyright: '© 2024 Threat Intel Corp',
            license: 'CC BY-SA 4.0',
        };
        mockQualityMetrics = {
            completeness: 85,
            accuracy: 90,
            timeliness: 95,
            relevance: 80,
            overallScore: 87,
            assessedAt: '2024-01-01T00:00:00Z',
            issues: ['Missing attribution details'],
        };
        mockThreatData = {
            version: '1.0.0',
            externalId: 'threat-123',
            sourceId: 'intel-source-001',
            sourceOrganization: 'Threat Intel Corp',
            sourceReliability: threat_data_interface_1.ThreatSourceReliability.A,
            name: 'APT29 Campaign 2024',
            description: 'Advanced persistent threat campaign targeting government entities',
            severity: threat_severity_enum_1.ThreatSeverity.HIGH,
            category: threat_data_interface_1.ThreatCategory.APT,
            subcategory: 'Government Targeting',
            type: threat_data_interface_1.ThreatType.CAMPAIGN,
            confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
            status: threat_data_interface_1.ThreatStatus.ACTIVE,
            firstSeen: '2024-01-01T00:00:00Z',
            lastSeen: '2024-01-31T23:59:59Z',
            expiresAt: '2024-12-31T23:59:59Z',
            indicators: mockIndicators,
            cvssScores: mockCVSSScores,
            attribution: mockAttribution,
            malwareFamily: mockMalwareFamily,
            attackTechniques: mockAttackTechniques,
            targetedSectors: ['Government', 'Defense'],
            targetedRegions: ['US', 'EU'],
            affectedPlatforms: ['Windows', 'Linux'],
            killChainPhases: [threat_data_interface_1.ThreatKillChainPhase.RECONNAISSANCE, threat_data_interface_1.ThreatKillChainPhase.WEAPONIZATION],
            timeline: mockTimeline,
            mitigations: mockMitigations,
            detectionRules: mockDetectionRules,
            relatedThreats: ['threat-456', 'threat-789'],
            sharingInfo: mockSharingInfo,
            attributes: { custom: 'attribute' },
            tags: ['apt', 'nation-state', 'espionage'],
            qualityMetrics: mockQualityMetrics,
        };
    });
    describe('ThreatData Structure', () => {
        it('should have all required fields', () => {
            expect(mockThreatData.version).toBeDefined();
            expect(mockThreatData.externalId).toBeDefined();
            expect(mockThreatData.sourceId).toBeDefined();
            expect(mockThreatData.sourceOrganization).toBeDefined();
            expect(mockThreatData.sourceReliability).toBeDefined();
            expect(mockThreatData.name).toBeDefined();
            expect(mockThreatData.description).toBeDefined();
            expect(mockThreatData.severity).toBeDefined();
            expect(mockThreatData.category).toBeDefined();
            expect(mockThreatData.type).toBeDefined();
            expect(mockThreatData.confidence).toBeDefined();
            expect(mockThreatData.status).toBeDefined();
            expect(mockThreatData.firstSeen).toBeDefined();
            expect(mockThreatData.lastSeen).toBeDefined();
            expect(mockThreatData.indicators).toBeDefined();
            expect(mockThreatData.attackTechniques).toBeDefined();
            expect(mockThreatData.sharingInfo).toBeDefined();
        });
        it('should support versioning', () => {
            expect(mockThreatData.version).toBe('1.0.0');
            expect(typeof mockThreatData.version).toBe('string');
        });
        it('should be serializable to JSON', () => {
            const jsonString = JSON.stringify(mockThreatData);
            expect(jsonString).toBeDefined();
            expect(jsonString.length).toBeGreaterThan(0);
            const parsed = JSON.parse(jsonString);
            expect(parsed.version).toBe(mockThreatData.version);
            expect(parsed.externalId).toBe(mockThreatData.externalId);
        });
        it('should maintain data integrity after serialization', () => {
            const jsonString = JSON.stringify(mockThreatData);
            const parsed = JSON.parse(jsonString);
            expect(parsed.version).toBe(mockThreatData.version);
            expect(parsed.externalId).toBe(mockThreatData.externalId);
            expect(parsed.name).toBe(mockThreatData.name);
            expect(parsed.severity).toBe(mockThreatData.severity);
            expect(parsed.category).toBe(mockThreatData.category);
            expect(parsed.indicators.length).toBe(mockThreatData.indicators.length);
        });
    });
    describe('ThreatSourceReliability Enum', () => {
        it('should contain all reliability levels', () => {
            const reliabilityLevels = Object.values(threat_data_interface_1.ThreatSourceReliability);
            expect(reliabilityLevels).toContain('A');
            expect(reliabilityLevels).toContain('B');
            expect(reliabilityLevels).toContain('C');
            expect(reliabilityLevels).toContain('D');
            expect(reliabilityLevels).toContain('E');
            expect(reliabilityLevels).toContain('F');
        });
    });
    describe('ThreatCategory Enum', () => {
        it('should contain comprehensive threat categories', () => {
            const categories = Object.values(threat_data_interface_1.ThreatCategory);
            expect(categories).toContain(threat_data_interface_1.ThreatCategory.MALWARE);
            expect(categories).toContain(threat_data_interface_1.ThreatCategory.PHISHING);
            expect(categories).toContain(threat_data_interface_1.ThreatCategory.RANSOMWARE);
            expect(categories).toContain(threat_data_interface_1.ThreatCategory.APT);
            expect(categories).toContain(threat_data_interface_1.ThreatCategory.NATION_STATE);
        });
    });
    describe('ThreatIndicatorData', () => {
        it('should contain indicator information', () => {
            const indicator = mockIndicators[0];
            expect(indicator.type).toBe(threat_data_interface_1.ThreatIndicatorType.IP_ADDRESS);
            expect(indicator.value).toBe('*************');
            expect(indicator.confidence).toBe(confidence_level_enum_1.ConfidenceLevel.HIGH);
            expect(indicator.status).toBe(threat_data_interface_1.ThreatIndicatorStatus.ACTIVE);
        });
        it('should support multiple indicator types', () => {
            const types = Object.values(threat_data_interface_1.ThreatIndicatorType);
            expect(types).toContain(threat_data_interface_1.ThreatIndicatorType.IP_ADDRESS);
            expect(types).toContain(threat_data_interface_1.ThreatIndicatorType.DOMAIN);
            expect(types).toContain(threat_data_interface_1.ThreatIndicatorType.FILE_HASH);
            expect(types).toContain(threat_data_interface_1.ThreatIndicatorType.URL);
        });
    });
    describe('ThreatCVSSData', () => {
        it('should contain CVSS scoring information', () => {
            const cvss = mockCVSSScores[0];
            expect(cvss.version).toBe('3.1');
            expect(cvss.score).toBe(8.5);
            expect(cvss.vector).toContain('CVSS:3.1');
            expect(cvss.severity).toBe('HIGH');
        });
        it('should support different CVSS versions', () => {
            const cvssV2 = {
                version: '2.0',
                score: 7.5,
                vector: 'AV:N/AC:L/Au:N/C:P/I:P/A:P',
                severity: 'HIGH',
            };
            expect(cvssV2.version).toBe('2.0');
            expect(cvssV2.score).toBeGreaterThan(0);
        });
    });
    describe('ThreatAttributionData', () => {
        it('should contain attribution information', () => {
            expect(mockAttribution.actorName).toBe('APT29');
            expect(mockAttribution.actorType).toBe(threat_data_interface_1.ThreatActorType.NATION_STATE);
            expect(mockAttribution.sophistication).toBe(threat_data_interface_1.ThreatActorSophistication.ADVANCED);
            expect(mockAttribution.confidence).toBe(confidence_level_enum_1.ConfidenceLevel.HIGH);
        });
        it('should support multiple motivations', () => {
            expect(Array.isArray(mockAttribution.motivation)).toBe(true);
            expect(mockAttribution.motivation).toContain(threat_data_interface_1.ThreatActorMotivation.ESPIONAGE);
        });
    });
    describe('ThreatMalwareFamilyData', () => {
        it('should contain malware family information', () => {
            expect(mockMalwareFamily.name).toBe('Cobalt Strike');
            expect(mockMalwareFamily.type).toBe(threat_data_interface_1.MalwareType.RAT);
            expect(Array.isArray(mockMalwareFamily.platforms)).toBe(true);
            expect(Array.isArray(mockMalwareFamily.capabilities)).toBe(true);
        });
    });
    describe('ThreatAttackTechniqueData', () => {
        it('should contain MITRE ATT&CK information', () => {
            const technique = mockAttackTechniques[0];
            expect(technique.techniqueId).toBe('T1055');
            expect(technique.name).toBe('Process Injection');
            expect(technique.tactic).toBe('Defense Evasion');
            expect(Array.isArray(technique.subTechniques)).toBe(true);
        });
    });
    describe('ThreatTimelineData', () => {
        it('should contain timeline information', () => {
            expect(mockTimeline.campaignStart).toBeDefined();
            expect(mockTimeline.campaignEnd).toBeDefined();
            expect(Array.isArray(mockTimeline.events)).toBe(true);
            expect(mockTimeline.duration).toBe(31);
        });
        it('should support activity patterns', () => {
            expect(Array.isArray(mockTimeline.patterns)).toBe(true);
            expect(mockTimeline.patterns[0].type).toBe('temporal');
        });
    });
    describe('ThreatMitigationData', () => {
        it('should contain mitigation information', () => {
            const mitigation = mockMitigations[0];
            expect(mitigation.type).toBe(threat_data_interface_1.ThreatMitigationType.PREVENTIVE);
            expect(mitigation.effectiveness).toBe('high');
            expect(Array.isArray(mitigation.technologies)).toBe(true);
        });
    });
    describe('ThreatDetectionRuleData', () => {
        it('should contain detection rule information', () => {
            const rule = mockDetectionRules[0];
            expect(rule.type).toBe(threat_data_interface_1.ThreatDetectionRuleType.YARA);
            expect(rule.name).toBe('Cobalt Strike Beacon');
            expect(rule.confidence).toBe(confidence_level_enum_1.ConfidenceLevel.HIGH);
        });
    });
    describe('ThreatSharingInfo', () => {
        it('should contain sharing restrictions', () => {
            expect(mockSharingInfo.tlp).toBe(threat_data_interface_1.TLPMarking.AMBER);
            expect(Array.isArray(mockSharingInfo.permittedActions)).toBe(true);
            expect(mockSharingInfo.permittedActions).toContain(threat_data_interface_1.ThreatSharingAction.ANALYZE);
        });
        it('should support TLP markings', () => {
            const tlpLevels = Object.values(threat_data_interface_1.TLPMarking);
            expect(tlpLevels).toContain(threat_data_interface_1.TLPMarking.RED);
            expect(tlpLevels).toContain(threat_data_interface_1.TLPMarking.AMBER);
            expect(tlpLevels).toContain(threat_data_interface_1.TLPMarking.GREEN);
            expect(tlpLevels).toContain(threat_data_interface_1.TLPMarking.WHITE);
        });
    });
    describe('ThreatDataQualityMetrics', () => {
        it('should contain quality assessment', () => {
            expect(mockQualityMetrics.completeness).toBe(85);
            expect(mockQualityMetrics.accuracy).toBe(90);
            expect(mockQualityMetrics.overallScore).toBe(87);
            expect(mockQualityMetrics.assessedAt).toBeDefined();
        });
        it('should support quality issues tracking', () => {
            expect(Array.isArray(mockQualityMetrics.issues)).toBe(true);
            expect(mockQualityMetrics.issues).toContain('Missing attribution details');
        });
    });
    describe('Data Validation Interface', () => {
        it('should define validation methods', () => {
            const mockValidator = {
                validate: jest.fn().mockReturnValue({
                    isValid: true,
                    errors: [],
                    warnings: [],
                    sanitizedData: mockThreatData,
                    qualityAssessment: mockQualityMetrics,
                }),
                validateVersion: jest.fn().mockReturnValue(true),
                sanitize: jest.fn().mockReturnValue(mockThreatData),
                validateIndicators: jest.fn().mockReturnValue([]),
            };
            expect(mockValidator.validate).toBeDefined();
            expect(mockValidator.validateVersion).toBeDefined();
            expect(mockValidator.sanitize).toBeDefined();
            expect(mockValidator.validateIndicators).toBeDefined();
        });
    });
    describe('Data Serialization Interface', () => {
        it('should define serialization methods', () => {
            const mockSerializer = {
                toJson: jest.fn().mockReturnValue('{}'),
                fromJson: jest.fn().mockReturnValue(mockThreatData),
                toStix: jest.fn().mockReturnValue('{}'),
                fromStix: jest.fn().mockReturnValue(mockThreatData),
                toBinary: jest.fn().mockReturnValue(Buffer.from('test')),
                fromBinary: jest.fn().mockReturnValue(mockThreatData),
                getSupportedVersions: jest.fn().mockReturnValue(['1.0.0']),
            };
            expect(mockSerializer.toJson).toBeDefined();
            expect(mockSerializer.fromJson).toBeDefined();
            expect(mockSerializer.toStix).toBeDefined();
            expect(mockSerializer.fromStix).toBeDefined();
            expect(mockSerializer.toBinary).toBeDefined();
            expect(mockSerializer.fromBinary).toBeDefined();
            expect(mockSerializer.getSupportedVersions).toBeDefined();
        });
    });
    describe('Data Transformation Interface', () => {
        it('should define transformation methods', () => {
            const mockTransformer = {
                transform: jest.fn().mockReturnValue(mockThreatData),
                transformToExternal: jest.fn().mockReturnValue({}),
                getSupportedSourceFormats: jest.fn().mockReturnValue(['stix', 'json']),
                getSupportedTargetFormats: jest.fn().mockReturnValue(['json', 'xml']),
                enrich: jest.fn().mockResolvedValue(mockThreatData),
            };
            expect(mockTransformer.transform).toBeDefined();
            expect(mockTransformer.transformToExternal).toBeDefined();
            expect(mockTransformer.getSupportedSourceFormats).toBeDefined();
            expect(mockTransformer.getSupportedTargetFormats).toBeDefined();
            expect(mockTransformer.enrich).toBeDefined();
        });
    });
    describe('Version Compatibility', () => {
        it('should support version checking', () => {
            const versions = ['1.0.0', '1.1.0', '2.0.0'];
            versions.forEach(version => {
                const versionedData = { ...mockThreatData, version };
                expect(versionedData.version).toBe(version);
            });
        });
        it('should maintain backward compatibility', () => {
            const legacyData = {
                ...mockThreatData,
                version: '0.9.0',
            };
            expect(legacyData.version).toBeDefined();
            expect(legacyData.externalId).toBeDefined();
            expect(legacyData.name).toBeDefined();
        });
    });
    describe('External Integration Support', () => {
        it('should support STIX format compatibility', () => {
            // Mock STIX transformation
            const stixCompatible = {
                ...mockThreatData,
                attributes: {
                    ...mockThreatData.attributes,
                    stix_id: 'malware--12345',
                    stix_version: '2.1',
                },
            };
            expect(stixCompatible.attributes).toHaveProperty('stix_id');
            expect(stixCompatible.attributes).toHaveProperty('stix_version');
        });
        it('should support custom threat intelligence formats', () => {
            const customFormat = {
                ...mockThreatData,
                attributes: {
                    ...mockThreatData.attributes,
                    vendor_specific: {
                        format: 'custom',
                        version: '1.0',
                        extensions: ['attribution', 'timeline'],
                    },
                },
            };
            expect(customFormat.attributes).toHaveProperty('vendor_specific');
        });
        it('should support enrichment from multiple sources', () => {
            const enrichedData = {
                ...mockThreatData,
                attributes: {
                    ...mockThreatData.attributes,
                    enrichment_sources: ['source1', 'source2', 'source3'],
                    enrichment_timestamp: '2024-01-01T00:00:00Z',
                },
            };
            expect(enrichedData.attributes).toHaveProperty('enrichment_sources');
            expect(Array.isArray(enrichedData.attributes.enrichment_sources)).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************