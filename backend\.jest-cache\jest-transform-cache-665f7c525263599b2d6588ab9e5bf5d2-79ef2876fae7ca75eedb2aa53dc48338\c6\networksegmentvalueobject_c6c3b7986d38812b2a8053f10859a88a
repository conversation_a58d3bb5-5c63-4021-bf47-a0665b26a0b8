de6a0088308ab4011de1718f5ce6510c
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkSegment = exports.NetworkSegmentClass = exports.NetworkSegmentType = void 0;
const base_value_object_1 = require("../../../../../shared-kernel/value-objects/base-value-object");
const ip_address_value_object_1 = require("./ip-address.value-object");
/**
 * Network Segment Type
 */
var NetworkSegmentType;
(function (NetworkSegmentType) {
    NetworkSegmentType["IPv4"] = "ipv4";
    NetworkSegmentType["IPv6"] = "ipv6";
})(NetworkSegmentType || (exports.NetworkSegmentType = NetworkSegmentType = {}));
/**
 * Network Segment Classification
 */
var NetworkSegmentClass;
(function (NetworkSegmentClass) {
    NetworkSegmentClass["PUBLIC"] = "public";
    NetworkSegmentClass["PRIVATE"] = "private";
    NetworkSegmentClass["RESERVED"] = "reserved";
    NetworkSegmentClass["MULTICAST"] = "multicast";
    NetworkSegmentClass["LINK_LOCAL"] = "link_local";
    NetworkSegmentClass["LOOPBACK"] = "loopback";
})(NetworkSegmentClass || (exports.NetworkSegmentClass = NetworkSegmentClass = {}));
/**
 * Network Segment Value Object
 *
 * Represents a network segment using CIDR notation with comprehensive
 * network analysis and subnet calculation capabilities.
 *
 * Key features:
 * - CIDR notation validation and parsing
 * - IPv4 and IPv6 support
 * - Subnet calculations and network analysis
 * - IP address membership testing
 * - Network classification and security analysis
 * - Subnet splitting and aggregation utilities
 */
class NetworkSegment extends base_value_object_1.BaseValueObject {
    constructor(props) {
        super(props);
    }
    initializeProperties() {
        this._networkIP = ip_address_value_object_1.IPAddress.fromString(this._value.networkAddress);
        this._type = this._networkIP.isIPv4() ? NetworkSegmentType.IPv4 : NetworkSegmentType.IPv6;
        this.validateNetworkAddress();
    }
    validate() {
        // Validate network address
        if (!this._value.networkAddress || this._value.networkAddress.trim().length === 0) {
            throw new Error('Network address cannot be empty');
        }
        // Validate prefix length
        if (!Number.isInteger(this._value.prefixLength)) {
            throw new Error('Prefix length must be an integer');
        }
        // Determine IP type for proper validation
        const isIPv4 = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(this._value.networkAddress);
        const maxPrefixLength = isIPv4 ? 32 : 128;
        if (this._value.prefixLength < 0 || this._value.prefixLength > maxPrefixLength) {
            throw new Error(`Prefix length must be between 0 and ${maxPrefixLength}`);
        }
    }
    validateNetworkAddress() {
        // This is called after initializeProperties
        const maxPrefixLength = this._networkIP.isIPv4() ? 32 : 128;
        if (this._value.prefixLength > maxPrefixLength) {
            throw new Error(`Prefix length must be between 0 and ${maxPrefixLength}`);
        }
        // Validate that the network address is actually a network address
        if (this._networkIP.isIPv4()) {
            this.validateIPv4NetworkAddress();
        }
        else {
            this.validateIPv6NetworkAddress();
        }
    }
    validateIPv4NetworkAddress() {
        const networkInt = this._networkIP.toIPv4Integer();
        const mask = (0xFFFFFFFF << (32 - this._value.prefixLength)) >>> 0;
        const expectedNetworkInt = networkInt & mask;
        if (networkInt !== expectedNetworkInt) {
            const expectedOctets = [
                (expectedNetworkInt >>> 24) & 0xFF,
                (expectedNetworkInt >>> 16) & 0xFF,
                (expectedNetworkInt >>> 8) & 0xFF,
                expectedNetworkInt & 0xFF,
            ];
            const expectedAddress = expectedOctets.join('.');
            throw new Error(`Invalid network address. Expected ${expectedAddress}/${this._value.prefixLength}`);
        }
    }
    validateIPv6NetworkAddress() {
        // Simplified IPv6 network address validation
        // In a full implementation, this would perform proper IPv6 network address validation
        if (this._value.prefixLength % 4 !== 0) {
            // For simplicity, we'll only validate on nibble boundaries
            return;
        }
    }
    /**
     * Create network segment from CIDR string
     */
    static fromCIDR(cidr) {
        const parts = cidr.trim().split('/');
        if (parts.length !== 2) {
            throw new Error('CIDR notation must be in format "address/prefix"');
        }
        const networkAddress = parts[0];
        const prefixLength = parseInt(parts[1], 10);
        if (isNaN(prefixLength)) {
            throw new Error('Invalid prefix length in CIDR notation');
        }
        return new NetworkSegment({ networkAddress, prefixLength });
    }
    /**
     * Create network segment from network address and prefix
     */
    static create(networkAddress, prefixLength) {
        return new NetworkSegment({ networkAddress, prefixLength });
    }
    /**
     * Create common private network segments
     */
    static privateClassA() {
        return NetworkSegment.fromCIDR('10.0.0.0/8');
    }
    static privateClassB() {
        return NetworkSegment.fromCIDR('**********/12');
    }
    static privateClassC() {
        return NetworkSegment.fromCIDR('***********/16');
    }
    static localhost() {
        return NetworkSegment.fromCIDR('*********/8');
    }
    /**
     * Get network address
     */
    get networkAddress() {
        return this._value.networkAddress;
    }
    /**
     * Get prefix length
     */
    get prefixLength() {
        return this._value.prefixLength;
    }
    /**
     * Get network type
     */
    get type() {
        return this._type;
    }
    /**
     * Check if this is an IPv4 network
     */
    isIPv4() {
        return this._type === NetworkSegmentType.IPv4;
    }
    /**
     * Check if this is an IPv6 network
     */
    isIPv6() {
        return this._type === NetworkSegmentType.IPv6;
    }
    /**
     * Get CIDR notation string
     */
    toCIDR() {
        return `${this._value.networkAddress}/${this._value.prefixLength}`;
    }
    /**
     * Check if an IP address belongs to this network segment
     */
    contains(ipAddress) {
        const ip = typeof ipAddress === 'string' ? ip_address_value_object_1.IPAddress.fromString(ipAddress) : ipAddress;
        // Type compatibility check
        if ((this.isIPv4() && !ip.isIPv4()) || (this.isIPv6() && !ip.isIPv6())) {
            return false;
        }
        return ip.isInNetwork(this._value.networkAddress, this._value.prefixLength);
    }
    /**
     * Get network classification
     */
    classify() {
        const networkClass = this._networkIP.classify();
        switch (networkClass) {
            case 'public':
                return NetworkSegmentClass.PUBLIC;
            case 'private':
                return NetworkSegmentClass.PRIVATE;
            case 'reserved':
                return NetworkSegmentClass.RESERVED;
            case 'multicast':
                return NetworkSegmentClass.MULTICAST;
            case 'link_local':
                return NetworkSegmentClass.LINK_LOCAL;
            case 'loopback':
                return NetworkSegmentClass.LOOPBACK;
            default:
                return NetworkSegmentClass.RESERVED;
        }
    }
    /**
     * Check if network is private
     */
    isPrivate() {
        return this.classify() === NetworkSegmentClass.PRIVATE;
    }
    /**
     * Check if network is public
     */
    isPublic() {
        return this.classify() === NetworkSegmentClass.PUBLIC;
    }
    /**
     * Get total number of addresses in this network
     */
    getTotalAddresses() {
        if (this.isIPv4()) {
            const hostBits = 32 - this._value.prefixLength;
            return BigInt(Math.pow(2, hostBits));
        }
        else {
            const hostBits = 128 - this._value.prefixLength;
            return BigInt(2) ** BigInt(hostBits);
        }
    }
    /**
     * Get number of usable host addresses (excluding network and broadcast for IPv4)
     */
    getUsableAddresses() {
        const total = this.getTotalAddresses();
        if (this.isIPv4() && this._value.prefixLength < 31) {
            // Subtract network and broadcast addresses for IPv4
            return total - BigInt(2);
        }
        return total;
    }
    /**
     * Get subnet mask for IPv4 networks
     */
    getSubnetMask() {
        if (!this.isIPv4()) {
            throw new Error('Subnet mask is only applicable to IPv4 networks');
        }
        // Handle special case for /0 (default route)
        if (this._value.prefixLength === 0) {
            return '0.0.0.0';
        }
        const mask = (0xFFFFFFFF << (32 - this._value.prefixLength)) >>> 0;
        const octets = [
            (mask >>> 24) & 0xFF,
            (mask >>> 16) & 0xFF,
            (mask >>> 8) & 0xFF,
            mask & 0xFF,
        ];
        return octets.join('.');
    }
    /**
     * Get wildcard mask for IPv4 networks
     */
    getWildcardMask() {
        if (!this.isIPv4()) {
            throw new Error('Wildcard mask is only applicable to IPv4 networks');
        }
        const wildcardMask = (0xFFFFFFFF >>> this._value.prefixLength);
        const octets = [
            (wildcardMask >>> 24) & 0xFF,
            (wildcardMask >>> 16) & 0xFF,
            (wildcardMask >>> 8) & 0xFF,
            wildcardMask & 0xFF,
        ];
        return octets.join('.');
    }
    /**
     * Get broadcast address for IPv4 networks
     */
    getBroadcastAddress() {
        if (!this.isIPv4()) {
            throw new Error('Broadcast address is only applicable to IPv4 networks');
        }
        const networkInt = this._networkIP.toIPv4Integer();
        const hostMask = (0xFFFFFFFF >>> this._value.prefixLength);
        const broadcastInt = networkInt | hostMask;
        const octets = [
            (broadcastInt >>> 24) & 0xFF,
            (broadcastInt >>> 16) & 0xFF,
            (broadcastInt >>> 8) & 0xFF,
            broadcastInt & 0xFF,
        ];
        return ip_address_value_object_1.IPAddress.fromIPv4Octets(octets[0], octets[1], octets[2], octets[3]);
    }
    /**
     * Get first usable host address
     */
    getFirstHostAddress() {
        if (this.isIPv4()) {
            if (this._value.prefixLength >= 31) {
                // For /31 and /32 networks, the network address is usable
                return this._networkIP;
            }
            const networkInt = this._networkIP.toIPv4Integer();
            const firstHostInt = networkInt + 1;
            const octets = [
                (firstHostInt >>> 24) & 0xFF,
                (firstHostInt >>> 16) & 0xFF,
                (firstHostInt >>> 8) & 0xFF,
                firstHostInt & 0xFF,
            ];
            return ip_address_value_object_1.IPAddress.fromIPv4Octets(octets[0], octets[1], octets[2], octets[3]);
        }
        else {
            // For IPv6, the network address is typically not used for hosts
            // This is a simplified implementation
            return this._networkIP;
        }
    }
    /**
     * Get last usable host address
     */
    getLastHostAddress() {
        if (this.isIPv4()) {
            if (this._value.prefixLength >= 31) {
                // For /31 networks, calculate the second address
                if (this._value.prefixLength === 31) {
                    const networkInt = this._networkIP.toIPv4Integer();
                    const lastHostInt = networkInt + 1;
                    const octets = [
                        (lastHostInt >>> 24) & 0xFF,
                        (lastHostInt >>> 16) & 0xFF,
                        (lastHostInt >>> 8) & 0xFF,
                        lastHostInt & 0xFF,
                    ];
                    return ip_address_value_object_1.IPAddress.fromIPv4Octets(octets[0], octets[1], octets[2], octets[3]);
                }
                // For /32, only one address
                return this._networkIP;
            }
            const broadcastInt = this.getBroadcastAddress().toIPv4Integer();
            const lastHostInt = broadcastInt - 1;
            const octets = [
                (lastHostInt >>> 24) & 0xFF,
                (lastHostInt >>> 16) & 0xFF,
                (lastHostInt >>> 8) & 0xFF,
                lastHostInt & 0xFF,
            ];
            return ip_address_value_object_1.IPAddress.fromIPv4Octets(octets[0], octets[1], octets[2], octets[3]);
        }
        else {
            // Simplified IPv6 implementation
            throw new Error('IPv6 last host address calculation not implemented');
        }
    }
    /**
     * Split network into smaller subnets
     */
    split(newPrefixLength) {
        if (newPrefixLength <= this._value.prefixLength) {
            throw new Error('New prefix length must be greater than current prefix length');
        }
        const maxPrefixLength = this.isIPv4() ? 32 : 128;
        if (newPrefixLength > maxPrefixLength) {
            throw new Error(`New prefix length cannot exceed ${maxPrefixLength}`);
        }
        if (!this.isIPv4()) {
            throw new Error('IPv6 subnet splitting not implemented');
        }
        const subnets = [];
        const bitsToAdd = newPrefixLength - this._value.prefixLength;
        const numberOfSubnets = Math.pow(2, bitsToAdd);
        const subnetSize = Math.pow(2, 32 - newPrefixLength);
        const baseNetworkInt = this._networkIP.toIPv4Integer();
        for (let i = 0; i < numberOfSubnets; i++) {
            const subnetNetworkInt = baseNetworkInt + (i * subnetSize);
            const octets = [
                (subnetNetworkInt >>> 24) & 0xFF,
                (subnetNetworkInt >>> 16) & 0xFF,
                (subnetNetworkInt >>> 8) & 0xFF,
                subnetNetworkInt & 0xFF,
            ];
            const subnetAddress = octets.join('.');
            subnets.push(NetworkSegment.create(subnetAddress, newPrefixLength));
        }
        return subnets;
    }
    /**
     * Check if this network overlaps with another network
     */
    overlaps(other) {
        // Type compatibility check
        if (this._type !== other._type) {
            return false;
        }
        // Check if either network contains the other's network address
        return this.contains(other._networkIP) || other.contains(this._networkIP);
    }
    /**
     * Check if this network is a subnet of another network
     */
    isSubnetOf(other) {
        // Type compatibility check
        if (this._type !== other._type) {
            return false;
        }
        // This network is a subnet if:
        // 1. It has a longer prefix (more specific)
        // 2. Its network address is contained in the other network
        return this._value.prefixLength > other._value.prefixLength &&
            other.contains(this._networkIP);
    }
    /**
     * Check if this network is a supernet of another network
     */
    isSupernetOf(other) {
        return other.isSubnetOf(this);
    }
    /**
     * Get network security risk assessment
     */
    getSecurityRisk() {
        const reasons = [];
        const recommendations = [];
        let riskLevel = 'low';
        // Assess based on network classification
        const classification = this.classify();
        if (classification === NetworkSegmentClass.PUBLIC) {
            riskLevel = 'high';
            reasons.push('Public network segment exposed to internet');
            recommendations.push('Implement strict firewall rules');
            recommendations.push('Monitor for unauthorized access attempts');
        }
        // Assess based on network size
        const totalAddresses = this.getTotalAddresses();
        if (totalAddresses > BigInt(1000000)) {
            if (riskLevel === 'low')
                riskLevel = 'medium';
            reasons.push('Very large network segment');
            recommendations.push('Consider network segmentation');
        }
        // Assess based on prefix length
        if (this.isIPv4() && this._value.prefixLength < 16) {
            if (riskLevel === 'low')
                riskLevel = 'medium';
            reasons.push('Very broad network range');
            recommendations.push('Implement network segmentation');
        }
        // Check for common vulnerable ranges
        if (this.isIPv4()) {
            const networkStr = this.toCIDR();
            const vulnerableRanges = [
                '0.0.0.0/0', // Default route
                '10.0.0.0/8', // Large private range
                '**********/12', // Private range
                '***********/16' // Common private range
            ];
            if (vulnerableRanges.includes(networkStr)) {
                reasons.push('Common network range that may be targeted');
                recommendations.push('Implement additional monitoring');
            }
        }
        if (reasons.length === 0) {
            reasons.push('Standard network segment');
            recommendations.push('Maintain regular security monitoring');
        }
        return { riskLevel, reasons, recommendations };
    }
    /**
     * Get network information summary
     */
    getNetworkInfo() {
        const info = {
            cidr: this.toCIDR(),
            type: this._type,
            classification: this.classify(),
            networkAddress: this._value.networkAddress,
            prefixLength: this._value.prefixLength,
            totalAddresses: this.getTotalAddresses().toString(),
            usableAddresses: this.getUsableAddresses().toString(),
            isPrivate: this.isPrivate(),
            isPublic: this.isPublic(),
        };
        if (this.isIPv4()) {
            info.subnetMask = this.getSubnetMask();
            info.wildcardMask = this.getWildcardMask();
            if (this._value.prefixLength < 32) {
                info.broadcastAddress = this.getBroadcastAddress().toString();
            }
            if (this._value.prefixLength < 31) {
                info.firstHost = this.getFirstHostAddress().toString();
                info.lastHost = this.getLastHostAddress().toString();
            }
        }
        return info;
    }
    /**
     * Compare network segments for equality
     */
    equals(other) {
        if (!other) {
            return false;
        }
        if (this === other) {
            return true;
        }
        return this._value.networkAddress === other._value.networkAddress &&
            this._value.prefixLength === other._value.prefixLength;
    }
    /**
     * Convert to string representation
     */
    toString() {
        return this.toCIDR();
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...this.getNetworkInfo(),
            securityRisk: this.getSecurityRisk(),
        };
    }
    /**
     * Create NetworkSegment from JSON
     */
    static fromJSON(json) {
        return NetworkSegment.create(json.networkAddress, json.prefixLength);
    }
    /**
     * Validate CIDR format without creating instance
     */
    static isValidCIDR(cidr) {
        try {
            NetworkSegment.fromCIDR(cidr);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Parse network segment from various formats
     */
    static parse(input) {
        try {
            // Try CIDR format first
            if (input.includes('/')) {
                return NetworkSegment.fromCIDR(input);
            }
            // Try to parse as single IP (assume /32 for IPv4, /128 for IPv6)
            const ip = ip_address_value_object_1.IPAddress.fromString(input);
            const prefixLength = ip.isIPv4() ? 32 : 128;
            return NetworkSegment.create(input, prefixLength);
        }
        catch {
            return null;
        }
    }
}
exports.NetworkSegment = NetworkSegment;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************