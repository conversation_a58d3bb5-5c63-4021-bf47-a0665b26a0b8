{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\correlated-event-correlation-failed.domain-event.spec.ts", "mappings": ";;AAAA,0HAG6D;AAC7D,gEAA8D;AAE9D,QAAQ,CAAC,6CAA6C,EAAE,GAAG,EAAE;IAC3D,IAAI,SAAoD,CAAC;IACzD,IAAI,WAA2B,CAAC;IAEhC,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;QACtC,SAAS,GAAG;YACV,eAAe,EAAE,8BAAc,CAAC,MAAM,EAAE;YACxC,KAAK,EAAE,4BAA4B;YACnC,OAAO,EAAE,CAAC;YACV,mBAAmB,EAAE,KAAK;YAC1B,QAAQ,EAAE,IAAI,IAAI,EAAE;YACpB,SAAS,EAAE,SAAS;YACpB,YAAY,EAAE,qDAAqD;YACnE,WAAW,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;YAClD,iBAAiB,EAAE;gBACjB,aAAa,EAAE,UAAU;gBACzB,UAAU,EAAE,IAAI;gBAChB,SAAS,EAAE,CAAC;aACb;YACD,WAAW,EAAE,IAAI;YACjB,YAAY,EAAE,IAAI;SACnB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE5F,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAClF,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,aAAa,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAC9C,MAAM,gBAAgB,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1D,MAAM,aAAa,GAAG,UAAU,CAAC;YAEjC,MAAM,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE,SAAS,EAAE;gBAC1F,OAAO,EAAE,aAAa;gBACtB,UAAU,EAAE,gBAAgB;gBAC5B,aAAa;gBACb,YAAY,EAAE,CAAC;gBACf,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;aAC7B,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACzD,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtD,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,IAAI,WAAwD,CAAC;QAE7D,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,oBAAoB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACxF,GAAG,SAAS;gBACZ,QAAQ,EAAE,SAAS;aACpB,CAAC,CAAC;YAEH,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,uBAAuB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC3F,GAAG,SAAS;gBACZ,WAAW,EAAE,SAAS;aACvB,CAAC,CAAC;YAEH,MAAM,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAC7E,MAAM,mBAAmB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACvF,GAAG,SAAS;gBACZ,iBAAiB,EAAE,SAAS;aAC7B,CAAC,CAAC;YAEH,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,qBAAqB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACzF,GAAG,SAAS;gBACZ,WAAW,EAAE,SAAS;aACvB,CAAC,CAAC;YAEH,MAAM,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,sBAAsB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC1F,GAAG,SAAS;gBACZ,YAAY,EAAE,SAAS;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,IAAI,WAAwD,CAAC;QAE7D,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,iBAAiB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACrF,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;aACX,CAAC,CAAC;YAEH,MAAM,oBAAoB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACxF,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;aACX,CAAC,CAAC;YAEH,MAAM,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,oBAAoB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,iBAAiB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACrF,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;aACX,CAAC,CAAC;YAEH,MAAM,oBAAoB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACxF,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;aACX,CAAC,CAAC;YAEH,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,CAAC,oBAAoB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,gBAAgB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACpF,GAAG,SAAS;gBACZ,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC;YAEH,MAAM,oBAAoB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACxF,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;aACX,CAAC,CAAC;YAEH,MAAM,iBAAiB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACrF,GAAG,SAAS;gBACZ,WAAW,EAAE,KAAK;aACnB,CAAC,CAAC;YAEH,MAAM,iBAAiB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACrF,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;aACX,CAAC,CAAC;YAEH,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,gBAAgB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACpF,GAAG,SAAS;gBACZ,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC;YAEH,MAAM,kBAAkB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACtF,GAAG,SAAS;gBACZ,SAAS,EAAE,yBAAyB;aACrC,CAAC,CAAC;YAEH,MAAM,iBAAiB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACrF,GAAG,SAAS;gBACZ,WAAW,EAAE,KAAK;aACnB,CAAC,CAAC;YAEH,MAAM,kBAAkB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACtF,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;gBACV,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;YAEH,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,YAAY,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,SAAS,EAAE,eAAe;aAC3B,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,SAAS,EAAE,qBAAqB;aACjC,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5D,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5D,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,qBAAqB;aACjC,CAAC,CAAC;YAEH,MAAM,kBAAkB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACtF,GAAG,SAAS;gBACZ,SAAS,EAAE,uBAAuB;aACnC,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC/D,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,SAAS,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,SAAS,EAAE,oBAAoB;aAChC,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACnF,GAAG,SAAS;gBACZ,SAAS,EAAE,wBAAwB;aACpC,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpD,MAAM,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,yBAAyB;aACrC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,SAAS,EAAE,4BAA4B;aACxC,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,YAAY,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,SAAS,EAAE,eAAe;aAC3B,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,aAAa,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACjF,GAAG,SAAS;gBACZ,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,iBAAiB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACrF,GAAG,SAAS;gBACZ,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC;YAEH,MAAM,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,oCAAoC;QACvG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,mBAAmB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACvF,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;gBACV,mBAAmB,EAAE,KAAK;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,gBAAgB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACpF,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;gBACV,mBAAmB,EAAE,KAAK;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,cAAc,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAClF,GAAG,SAAS;gBACZ,WAAW,EAAE,IAAI;gBACjB,mBAAmB,EAAE,KAAK;gBAC1B,YAAY,EAAE,IAAI;aACnB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,cAAc,CAAC,kBAAkB,EAAE,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,cAAc,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAClF,GAAG,SAAS;gBACZ,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,cAAc,CAAC,kBAAkB,EAAE,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,qBAAqB;aACjC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,WAAW,CAAC,kBAAkB,EAAE,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,uCAAuC,CAAC,CAAC;YACnE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,SAAS,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,SAAS,EAAE,oBAAoB;aAChC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,SAAS,CAAC,kBAAkB,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,yBAAyB;aACrC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,WAAW,CAAC,kBAAkB,EAAE,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,gBAAgB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACpF,GAAG,SAAS;gBACZ,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;YACzD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,UAAU,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC9E,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC,CAAC,uBAAuB;aACnC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,aAAa,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACjF,GAAG,SAAS;gBACZ,SAAS,EAAE,yBAAyB;aACrC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,aAAa,CAAC,yBAAyB,EAAE,CAAC;YAC7D,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtD,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC5C,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,iBAAiB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACrF,GAAG,SAAS;gBACZ,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,iBAAiB,CAAC,yBAAyB,EAAE,CAAC;YACjE,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACxC,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;gBACV,SAAS,EAAE,4BAA4B;aACxC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,WAAW,CAAC,yBAAyB,EAAE,CAAC;YAC3D,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,SAAS,EAAE,qBAAqB;aACjC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,WAAW,CAAC,yBAAyB,EAAE,CAAC;YAC3D,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,gBAAgB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACpF,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;gBACV,SAAS,EAAE,SAAS;aACrB,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,gBAAgB,CAAC,yBAAyB,EAAE,CAAC;YAChE,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC5F,MAAM,OAAO,GAAG,WAAW,CAAC,kBAAkB,EAAE,CAAC;YAEjD,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,sBAAsB,CAAC,CAAC;YAC7E,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,aAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,aAAc,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAClC,OAAO,EAAE,GAAG;gBACZ,QAAQ,EAAE,WAAW;gBACrB,QAAQ,EAAE,QAAQ;gBAClB,UAAU,EAAE,SAAS;aACtB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,gBAAgB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACpF,GAAG,SAAS;gBACZ,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;YACtD,MAAM,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,mCAAmC,CAAC,CAAC;YAC9F,MAAM,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,iBAAkB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,cAAc,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAClF,GAAG,SAAS;gBACZ,WAAW,EAAE,IAAI;gBACjB,mBAAmB,EAAE,KAAK;aAC3B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,cAAc,CAAC,kBAAkB,EAAE,CAAC;YACpD,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,+BAA+B,CAAC,CAAC;YACpF,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,WAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,WAAY,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAChC,OAAO,EAAE,GAAG;gBACZ,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,UAAU,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBAC9E,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC,CAAC,uBAAuB;aACnC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAChD,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,4BAA4B,CAAC,CAAC;YACjF,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,WAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,WAAY,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAChC,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,aAAa,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACjF,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;gBACV,YAAY,EAAE,SAAS;aACxB,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACjF,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;gBACV,YAAY,EAAE,SAAS;aACxB,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACjF,GAAG,SAAS;gBACZ,OAAO,EAAE,CAAC;gBACV,YAAY,EAAE,SAAS;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc;YAErE,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc;YAErE,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,gBAAgB,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE;gBACpF,GAAG,SAAS;gBACZ,OAAO,EAAE,EAAE;gBACX,YAAY,EAAE,SAAS;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,sBAAsB;QAC3F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC5F,MAAM,OAAO,GAAG,WAAW,CAAC,eAAe,EAAE,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3E,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YACxE,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC3D,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,WAAW,EAAE,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,WAAW,GAAG,IAAI,8FAA2C,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC5F,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;YAElC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC3E,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\correlated-event-correlation-failed.domain-event.spec.ts"], "sourcesContent": ["import { \r\n  CorrelatedEventCorrelationFailedDomainEvent, \r\n  CorrelatedEventCorrelationFailedEventData \r\n} from '../correlated-event-correlation-failed.domain-event';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\n\r\ndescribe('CorrelatedEventCorrelationFailedDomainEvent', () => {\r\n  let eventData: CorrelatedEventCorrelationFailedEventData;\r\n  let aggregateId: UniqueEntityId;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.create();\r\n    eventData = {\r\n      enrichedEventId: UniqueEntityId.create(),\r\n      error: 'Correlation engine timeout',\r\n      attempt: 2,\r\n      maxAttemptsExceeded: false,\r\n      failedAt: new Date(),\r\n      errorCode: 'TIMEOUT',\r\n      errorDetails: 'Correlation processing exceeded 120 seconds timeout',\r\n      failedRules: ['temporal_rule_1', 'spatial_rule_2'],\r\n      processingContext: {\r\n        correlationId: 'corr_123',\r\n        eventCount: 1000,\r\n        ruleCount: 5\r\n      },\r\n      isRetryable: true,\r\n      retryDelayMs: 5000\r\n    };\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create domain event with required data', () => {\r\n      const domainEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData);\r\n\r\n      expect(domainEvent.aggregateId).toEqual(aggregateId);\r\n      expect(domainEvent.eventData).toEqual(eventData);\r\n      expect(domainEvent.eventName).toBe('CorrelatedEventCorrelationFailedDomainEvent');\r\n      expect(domainEvent.occurredOn).toBeInstanceOf(Date);\r\n      expect(domainEvent.eventId).toBeDefined();\r\n    });\r\n\r\n    it('should create domain event with custom options', () => {\r\n      const customEventId = UniqueEntityId.create();\r\n      const customOccurredOn = new Date('2023-01-01T00:00:00Z');\r\n      const correlationId = 'corr_123';\r\n\r\n      const domainEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData, {\r\n        eventId: customEventId,\r\n        occurredOn: customOccurredOn,\r\n        correlationId,\r\n        eventVersion: 2,\r\n        metadata: { custom: 'data' }\r\n      });\r\n\r\n      expect(domainEvent.eventId).toEqual(customEventId);\r\n      expect(domainEvent.occurredOn).toEqual(customOccurredOn);\r\n      expect(domainEvent.correlationId).toBe(correlationId);\r\n      expect(domainEvent.eventVersion).toBe(2);\r\n      expect(domainEvent.metadata).toEqual({ custom: 'data' });\r\n    });\r\n  });\r\n\r\n  describe('property getters', () => {\r\n    let domainEvent: CorrelatedEventCorrelationFailedDomainEvent;\r\n\r\n    beforeEach(() => {\r\n      domainEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData);\r\n    });\r\n\r\n    it('should return enriched event ID', () => {\r\n      expect(domainEvent.enrichedEventId).toEqual(eventData.enrichedEventId);\r\n    });\r\n\r\n    it('should return error message', () => {\r\n      expect(domainEvent.error).toBe(eventData.error);\r\n    });\r\n\r\n    it('should return attempt number', () => {\r\n      expect(domainEvent.attempt).toBe(eventData.attempt);\r\n    });\r\n\r\n    it('should return max attempts exceeded flag', () => {\r\n      expect(domainEvent.maxAttemptsExceeded).toBe(eventData.maxAttemptsExceeded);\r\n    });\r\n\r\n    it('should return failed at timestamp', () => {\r\n      expect(domainEvent.failedAt).toEqual(eventData.failedAt);\r\n    });\r\n\r\n    it('should return error code', () => {\r\n      expect(domainEvent.errorCode).toBe(eventData.errorCode);\r\n    });\r\n\r\n    it('should return error details', () => {\r\n      expect(domainEvent.errorDetails).toBe(eventData.errorDetails);\r\n    });\r\n\r\n    it('should return failed rules', () => {\r\n      expect(domainEvent.failedRules).toEqual(eventData.failedRules);\r\n    });\r\n\r\n    it('should return processing context', () => {\r\n      expect(domainEvent.processingContext).toEqual(eventData.processingContext);\r\n    });\r\n\r\n    it('should return is retryable flag', () => {\r\n      expect(domainEvent.isRetryable).toBe(eventData.isRetryable);\r\n    });\r\n\r\n    it('should return retry delay', () => {\r\n      expect(domainEvent.retryDelayMs).toBe(eventData.retryDelayMs);\r\n    });\r\n\r\n    it('should use occurred on when failed at is not provided', () => {\r\n      const eventWithoutFailedAt = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        failedAt: undefined\r\n      });\r\n\r\n      expect(eventWithoutFailedAt.failedAt).toEqual(eventWithoutFailedAt.occurredOn);\r\n    });\r\n\r\n    it('should return empty array for failed rules when not provided', () => {\r\n      const eventWithoutFailedRules = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        failedRules: undefined\r\n      });\r\n\r\n      expect(eventWithoutFailedRules.failedRules).toEqual([]);\r\n    });\r\n\r\n    it('should return empty object for processing context when not provided', () => {\r\n      const eventWithoutContext = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        processingContext: undefined\r\n      });\r\n\r\n      expect(eventWithoutContext.processingContext).toEqual({});\r\n    });\r\n\r\n    it('should default is retryable to true when not provided', () => {\r\n      const eventWithoutRetryable = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        isRetryable: undefined\r\n      });\r\n\r\n      expect(eventWithoutRetryable.isRetryable).toBe(true);\r\n    });\r\n\r\n    it('should calculate retry delay when not provided', () => {\r\n      const eventWithoutRetryDelay = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        retryDelayMs: undefined\r\n      });\r\n\r\n      expect(eventWithoutRetryDelay.retryDelayMs).toBeGreaterThan(0);\r\n    });\r\n  });\r\n\r\n  describe('failure analysis methods', () => {\r\n    let domainEvent: CorrelatedEventCorrelationFailedDomainEvent;\r\n\r\n    beforeEach(() => {\r\n      domainEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData);\r\n    });\r\n\r\n    it('should identify first failure', () => {\r\n      const firstFailureEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 1\r\n      });\r\n\r\n      const repeatedFailureEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 3\r\n      });\r\n\r\n      expect(firstFailureEvent.isFirstFailure()).toBe(true);\r\n      expect(repeatedFailureEvent.isFirstFailure()).toBe(false);\r\n    });\r\n\r\n    it('should identify repeated failure', () => {\r\n      const firstFailureEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 1\r\n      });\r\n\r\n      const repeatedFailureEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 3\r\n      });\r\n\r\n      expect(firstFailureEvent.isRepeatedFailure()).toBe(false);\r\n      expect(repeatedFailureEvent.isRepeatedFailure()).toBe(true);\r\n    });\r\n\r\n    it('should determine when to trigger alerts', () => {\r\n      const maxAttemptsEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        maxAttemptsExceeded: true\r\n      });\r\n\r\n      const repeatedFailureEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 2\r\n      });\r\n\r\n      const nonRetryableEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        isRetryable: false\r\n      });\r\n\r\n      const firstFailureEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 1\r\n      });\r\n\r\n      expect(maxAttemptsEvent.shouldTriggerAlert()).toBe(true);\r\n      expect(repeatedFailureEvent.shouldTriggerAlert()).toBe(true);\r\n      expect(nonRetryableEvent.shouldTriggerAlert()).toBe(true);\r\n      expect(firstFailureEvent.shouldTriggerAlert()).toBe(false);\r\n    });\r\n\r\n    it('should identify critical failures', () => {\r\n      const maxAttemptsEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        maxAttemptsExceeded: true\r\n      });\r\n\r\n      const criticalErrorEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'CORRELATION_ENGINE_DOWN'\r\n      });\r\n\r\n      const nonRetryableEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        isRetryable: false\r\n      });\r\n\r\n      const normalFailureEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 1,\r\n        errorCode: 'TIMEOUT'\r\n      });\r\n\r\n      expect(maxAttemptsEvent.isCriticalFailure()).toBe(true);\r\n      expect(criticalErrorEvent.isCriticalFailure()).toBe(true);\r\n      expect(nonRetryableEvent.isCriticalFailure()).toBe(true);\r\n      expect(normalFailureEvent.isCriticalFailure()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('failure categorization', () => {\r\n    it('should categorize transient errors', () => {\r\n      const timeoutEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'TIMEOUT'\r\n      });\r\n\r\n      const networkEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'NETWORK_ERROR'\r\n      });\r\n\r\n      const serviceEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'SERVICE_UNAVAILABLE'\r\n      });\r\n\r\n      expect(timeoutEvent.getFailureCategory()).toBe('transient');\r\n      expect(networkEvent.getFailureCategory()).toBe('transient');\r\n      expect(serviceEvent.getFailureCategory()).toBe('transient');\r\n    });\r\n\r\n    it('should categorize configuration errors', () => {\r\n      const configEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'INVALID_RULE_CONFIG'\r\n      });\r\n\r\n      const missingConfigEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'MISSING_CONFIGURATION'\r\n      });\r\n\r\n      expect(configEvent.getFailureCategory()).toBe('configuration');\r\n      expect(missingConfigEvent.getFailureCategory()).toBe('configuration');\r\n    });\r\n\r\n    it('should categorize data errors', () => {\r\n      const dataEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'INVALID_EVENT_DATA'\r\n      });\r\n\r\n      const validationEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'DATA_VALIDATION_FAILED'\r\n      });\r\n\r\n      expect(dataEvent.getFailureCategory()).toBe('data');\r\n      expect(validationEvent.getFailureCategory()).toBe('data');\r\n    });\r\n\r\n    it('should categorize system errors', () => {\r\n      const engineEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'CORRELATION_ENGINE_DOWN'\r\n      });\r\n\r\n      const dbEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'DATABASE_CONNECTION_FAILED'\r\n      });\r\n\r\n      expect(engineEvent.getFailureCategory()).toBe('system');\r\n      expect(dbEvent.getFailureCategory()).toBe('system');\r\n    });\r\n\r\n    it('should categorize unknown errors', () => {\r\n      const unknownEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'UNKNOWN_ERROR'\r\n      });\r\n\r\n      const noCodeEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: undefined\r\n      });\r\n\r\n      expect(unknownEvent.getFailureCategory()).toBe('unknown');\r\n      expect(noCodeEvent.getFailureCategory()).toBe('unknown');\r\n    });\r\n  });\r\n\r\n  describe('failure severity assessment', () => {\r\n    it('should assess critical severity', () => {\r\n      const criticalEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        maxAttemptsExceeded: true\r\n      });\r\n\r\n      expect(criticalEvent.getFailureSeverity()).toBe('critical');\r\n    });\r\n\r\n    it('should assess high severity', () => {\r\n      const highSeverityEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        maxAttemptsExceeded: true\r\n      });\r\n\r\n      expect(highSeverityEvent.getFailureSeverity()).toBe('critical'); // max attempts exceeded is critical\r\n    });\r\n\r\n    it('should assess medium severity', () => {\r\n      const mediumSeverityEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 2,\r\n        maxAttemptsExceeded: false\r\n      });\r\n\r\n      expect(mediumSeverityEvent.getFailureSeverity()).toBe('medium');\r\n    });\r\n\r\n    it('should assess low severity', () => {\r\n      const lowSeverityEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 1,\r\n        maxAttemptsExceeded: false\r\n      });\r\n\r\n      expect(lowSeverityEvent.getFailureSeverity()).toBe('low');\r\n    });\r\n  });\r\n\r\n  describe('recovery actions', () => {\r\n    it('should recommend retry for retryable failures', () => {\r\n      const retryableEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        isRetryable: true,\r\n        maxAttemptsExceeded: false,\r\n        retryDelayMs: 5000\r\n      });\r\n\r\n      const actions = retryableEvent.getRecoveryActions();\r\n      expect(actions).toContain('Schedule retry in 5000ms');\r\n    });\r\n\r\n    it('should recommend transient error actions', () => {\r\n      const transientEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'TIMEOUT'\r\n      });\r\n\r\n      const actions = transientEvent.getRecoveryActions();\r\n      expect(actions).toContain('Monitor system resources');\r\n      expect(actions).toContain('Check network connectivity');\r\n      expect(actions).toContain('Verify service availability');\r\n    });\r\n\r\n    it('should recommend configuration error actions', () => {\r\n      const configEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'INVALID_RULE_CONFIG'\r\n      });\r\n\r\n      const actions = configEvent.getRecoveryActions();\r\n      expect(actions).toContain('Review correlation rule configuration');\r\n      expect(actions).toContain('Validate rule parameters');\r\n      expect(actions).toContain('Check configuration syntax');\r\n    });\r\n\r\n    it('should recommend data error actions', () => {\r\n      const dataEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'INVALID_EVENT_DATA'\r\n      });\r\n\r\n      const actions = dataEvent.getRecoveryActions();\r\n      expect(actions).toContain('Validate input event data');\r\n      expect(actions).toContain('Check data format and schema');\r\n      expect(actions).toContain('Review data transformation logic');\r\n    });\r\n\r\n    it('should recommend system error actions', () => {\r\n      const systemEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'CORRELATION_ENGINE_DOWN'\r\n      });\r\n\r\n      const actions = systemEvent.getRecoveryActions();\r\n      expect(actions).toContain('Check system health and resources');\r\n      expect(actions).toContain('Verify database connectivity');\r\n      expect(actions).toContain('Review system logs');\r\n      expect(actions).toContain('Escalate to operations team');\r\n    });\r\n\r\n    it('should recommend max attempts exceeded actions', () => {\r\n      const maxAttemptsEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        maxAttemptsExceeded: true\r\n      });\r\n\r\n      const actions = maxAttemptsEvent.getRecoveryActions();\r\n      expect(actions).toContain('Move to dead letter queue');\r\n      expect(actions).toContain('Generate manual review task');\r\n      expect(actions).toContain('Notify operations team');\r\n    });\r\n\r\n    it('should recommend alert actions when needed', () => {\r\n      const alertEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 2 // Should trigger alert\r\n      });\r\n\r\n      const actions = alertEvent.getRecoveryActions();\r\n      expect(actions).toContain('Generate failure alert');\r\n      expect(actions).toContain('Update monitoring dashboards');\r\n    });\r\n  });\r\n\r\n  describe('escalation requirements', () => {\r\n    it('should require management escalation for critical failures', () => {\r\n      const criticalEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'CORRELATION_ENGINE_DOWN'\r\n      });\r\n\r\n      const escalation = criticalEvent.getEscalationRequirements();\r\n      expect(escalation.shouldEscalate).toBe(true);\r\n      expect(escalation.escalationLevel).toBe('management');\r\n      expect(escalation.urgency).toBe('critical');\r\n      expect(escalation.timeoutMinutes).toBe(15);\r\n    });\r\n\r\n    it('should require ops escalation for high severity failures', () => {\r\n      const highSeverityEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        maxAttemptsExceeded: true\r\n      });\r\n\r\n      const escalation = highSeverityEvent.getEscalationRequirements();\r\n      expect(escalation.shouldEscalate).toBe(true);\r\n      expect(escalation.escalationLevel).toBe('ops');\r\n      expect(escalation.urgency).toBe('high');\r\n      expect(escalation.timeoutMinutes).toBe(30);\r\n    });\r\n\r\n    it('should require ops escalation for system errors', () => {\r\n      const systemEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 2,\r\n        errorCode: 'DATABASE_CONNECTION_FAILED'\r\n      });\r\n\r\n      const escalation = systemEvent.getEscalationRequirements();\r\n      expect(escalation.shouldEscalate).toBe(true);\r\n      expect(escalation.escalationLevel).toBe('ops');\r\n      expect(escalation.urgency).toBe('medium');\r\n      expect(escalation.timeoutMinutes).toBe(60);\r\n    });\r\n\r\n    it('should require dev escalation for configuration errors', () => {\r\n      const configEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        errorCode: 'INVALID_RULE_CONFIG'\r\n      });\r\n\r\n      const escalation = configEvent.getEscalationRequirements();\r\n      expect(escalation.shouldEscalate).toBe(true);\r\n      expect(escalation.escalationLevel).toBe('dev');\r\n      expect(escalation.urgency).toBe('medium');\r\n      expect(escalation.timeoutMinutes).toBe(120);\r\n    });\r\n\r\n    it('should not require escalation for low severity transient errors', () => {\r\n      const lowSeverityEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 1,\r\n        errorCode: 'TIMEOUT'\r\n      });\r\n\r\n      const escalation = lowSeverityEvent.getEscalationRequirements();\r\n      expect(escalation.shouldEscalate).toBe(false);\r\n      expect(escalation.escalationLevel).toBe('ops');\r\n      expect(escalation.urgency).toBe('low');\r\n      expect(escalation.timeoutMinutes).toBe(240);\r\n    });\r\n  });\r\n\r\n  describe('metrics calculation', () => {\r\n    it('should calculate failure metrics', () => {\r\n      const domainEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData);\r\n      const metrics = domainEvent.getMetricsToUpdate();\r\n\r\n      const failureMetric = metrics.find(m => m.metric === 'correlation_failures');\r\n      expect(failureMetric).toBeDefined();\r\n      expect(failureMetric!.value).toBe(1);\r\n      expect(failureMetric!.tags).toEqual({\r\n        attempt: '2',\r\n        category: 'transient',\r\n        severity: 'medium',\r\n        error_code: 'TIMEOUT'\r\n      });\r\n    });\r\n\r\n    it('should calculate max attempts exceeded metrics', () => {\r\n      const maxAttemptsEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        maxAttemptsExceeded: true\r\n      });\r\n\r\n      const metrics = maxAttemptsEvent.getMetricsToUpdate();\r\n      const maxAttemptsMetric = metrics.find(m => m.metric === 'correlation_max_attempts_exceeded');\r\n      expect(maxAttemptsMetric).toBeDefined();\r\n      expect(maxAttemptsMetric!.value).toBe(1);\r\n    });\r\n\r\n    it('should calculate retry metrics for retryable failures', () => {\r\n      const retryableEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        isRetryable: true,\r\n        maxAttemptsExceeded: false\r\n      });\r\n\r\n      const metrics = retryableEvent.getMetricsToUpdate();\r\n      const retryMetric = metrics.find(m => m.metric === 'correlation_retries_scheduled');\r\n      expect(retryMetric).toBeDefined();\r\n      expect(retryMetric!.value).toBe(1);\r\n      expect(retryMetric!.tags).toEqual({\r\n        attempt: '2',\r\n        delay_ms: '5000'\r\n      });\r\n    });\r\n\r\n    it('should calculate alert metrics when alerts are triggered', () => {\r\n      const alertEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 2 // Should trigger alert\r\n      });\r\n\r\n      const metrics = alertEvent.getMetricsToUpdate();\r\n      const alertMetric = metrics.find(m => m.metric === 'correlation_failure_alerts');\r\n      expect(alertMetric).toBeDefined();\r\n      expect(alertMetric!.value).toBe(1);\r\n      expect(alertMetric!.tags).toEqual({\r\n        severity: 'medium'\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('retry delay calculation', () => {\r\n    it('should calculate exponential backoff delay', () => {\r\n      const attempt1Event = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 1,\r\n        retryDelayMs: undefined\r\n      });\r\n\r\n      const attempt2Event = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 2,\r\n        retryDelayMs: undefined\r\n      });\r\n\r\n      const attempt3Event = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 3,\r\n        retryDelayMs: undefined\r\n      });\r\n\r\n      expect(attempt1Event.retryDelayMs).toBeGreaterThanOrEqual(1000);\r\n      expect(attempt1Event.retryDelayMs).toBeLessThan(1500); // With jitter\r\n\r\n      expect(attempt2Event.retryDelayMs).toBeGreaterThanOrEqual(2000);\r\n      expect(attempt2Event.retryDelayMs).toBeLessThan(3000); // With jitter\r\n\r\n      expect(attempt3Event.retryDelayMs).toBeGreaterThanOrEqual(4000);\r\n      expect(attempt3Event.retryDelayMs).toBeLessThan(6000); // With jitter\r\n    });\r\n\r\n    it('should cap retry delay at maximum', () => {\r\n      const highAttemptEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        attempt: 10,\r\n        retryDelayMs: undefined\r\n      });\r\n\r\n      expect(highAttemptEvent.retryDelayMs).toBeLessThanOrEqual(330000); // 300000 + 10% jitter\r\n    });\r\n  });\r\n\r\n  describe('event summary', () => {\r\n    it('should provide comprehensive event summary', () => {\r\n      const domainEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData);\r\n      const summary = domainEvent.getEventSummary();\r\n\r\n      expect(summary.correlatedEventId).toBe(aggregateId.toString());\r\n      expect(summary.enrichedEventId).toBe(eventData.enrichedEventId.toString());\r\n      expect(summary.error).toBe(eventData.error);\r\n      expect(summary.attempt).toBe(eventData.attempt);\r\n      expect(summary.maxAttemptsExceeded).toBe(eventData.maxAttemptsExceeded);\r\n      expect(summary.failedAt).toEqual(eventData.failedAt);\r\n      expect(summary.errorCode).toBe(eventData.errorCode);\r\n      expect(summary.errorDetails).toBe(eventData.errorDetails);\r\n      expect(summary.failedRules).toEqual(eventData.failedRules);\r\n      expect(summary.isRetryable).toBe(eventData.isRetryable);\r\n      expect(summary.retryDelayMs).toBe(eventData.retryDelayMs);\r\n      expect(summary.isFirstFailure).toBe(false);\r\n      expect(summary.isRepeatedFailure).toBe(true);\r\n      expect(summary.shouldTriggerAlert).toBe(true);\r\n      expect(summary.isCriticalFailure).toBe(false);\r\n      expect(summary.failureCategory).toBe('transient');\r\n      expect(summary.failureSeverity).toBe('medium');\r\n      expect(summary.recoveryActions).toBeInstanceOf(Array);\r\n      expect(summary.escalationRequirements).toBeDefined();\r\n      expect(summary.metricsToUpdate).toBeInstanceOf(Array);\r\n    });\r\n  });\r\n\r\n  describe('JSON serialization', () => {\r\n    it('should serialize to JSON with event summary', () => {\r\n      const domainEvent = new CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData);\r\n      const json = domainEvent.toJSON();\r\n\r\n      expect(json.eventName).toBe('CorrelatedEventCorrelationFailedDomainEvent');\r\n      expect(json.aggregateId).toBe(aggregateId.toString());\r\n      expect(json.eventData).toEqual(eventData);\r\n      expect(json.eventSummary).toBeDefined();\r\n      expect(json.occurredOn).toBeInstanceOf(Date);\r\n    });\r\n  });\r\n});"], "version": 3}