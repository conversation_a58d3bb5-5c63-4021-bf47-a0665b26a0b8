dbed617d52d5d4293576d0cf45d3aba9
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const normalized_event_specification_1 = require("../normalized-event.specification");
const normalized_event_entity_1 = require("../../entities/normalized-event.entity");
const shared_kernel_1 = require("../../../../../shared-kernel");
const event_metadata_value_object_1 = require("../../value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../value-objects/event-metadata/event-source.value-object");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const event_status_enum_1 = require("../../enums/event-status.enum");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const event_source_type_enum_1 = require("../../enums/event-source-type.enum");
describe('NormalizedEvent Specifications', () => {
    let baseProps;
    let mockMetadata;
    let mockRule;
    beforeEach(() => {
        // Create mock metadata
        const timestamp = event_timestamp_value_object_1.EventTimestamp.create();
        const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.SIEM, 'test-siem');
        mockMetadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
        // Create mock normalization rule
        mockRule = {
            id: 'test-rule',
            name: 'Test Rule',
            description: 'Test normalization rule',
            priority: 100,
            required: false,
        };
        // Create base props
        baseProps = {
            originalEventId: shared_kernel_1.UniqueEntityId.create(),
            metadata: mockMetadata,
            type: event_type_enum_1.EventType.THREAT_DETECTED,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            status: event_status_enum_1.EventStatus.ACTIVE,
            processingStatus: event_processing_status_enum_1.EventProcessingStatus.NORMALIZED,
            normalizationStatus: normalized_event_entity_1.NormalizationStatus.COMPLETED,
            originalData: { raw: 'data' },
            normalizedData: { normalized: 'data' },
            title: 'Test Normalized Event',
            appliedRules: [mockRule],
            schemaVersion: '1.0.0',
            dataQualityScore: 85,
            normalizationAttempts: 1,
            normalizationCompletedAt: new Date(),
            normalizationResult: {
                success: true,
                appliedRules: ['test-rule'],
                failedRules: [],
                warnings: [],
                errors: [],
                processingDurationMs: 1000,
                confidenceScore: 90,
            },
        };
    });
    describe('NormalizationCompletedSpecification', () => {
        it('should be satisfied by completed normalized events', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.NormalizationCompletedSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event has completed normalization');
        });
        it('should not be satisfied by non-completed normalized events', () => {
            // Arrange
            const pendingProps = { ...baseProps, normalizationStatus: normalized_event_entity_1.NormalizationStatus.PENDING };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(pendingProps);
            const spec = new normalized_event_specification_1.NormalizationCompletedSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('NormalizationFailedSpecification', () => {
        it('should be satisfied by failed normalized events', () => {
            // Arrange
            const failedProps = {
                ...baseProps,
                normalizationStatus: normalized_event_entity_1.NormalizationStatus.FAILED,
                lastNormalizationError: 'Test error',
                normalizationCompletedAt: undefined,
                normalizationResult: undefined,
            };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(failedProps);
            const spec = new normalized_event_specification_1.NormalizationFailedSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event has failed normalization');
        });
        it('should not be satisfied by non-failed normalized events', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.NormalizationFailedSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('NormalizationInProgressSpecification', () => {
        it('should be satisfied by in-progress normalized events', () => {
            // Arrange
            const inProgressProps = {
                ...baseProps,
                normalizationStatus: normalized_event_entity_1.NormalizationStatus.IN_PROGRESS,
                normalizationStartedAt: new Date(),
                normalizationCompletedAt: undefined,
                normalizationResult: undefined,
            };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(inProgressProps);
            const spec = new normalized_event_specification_1.NormalizationInProgressSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event is currently being normalized');
        });
        it('should not be satisfied by non-in-progress normalized events', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.NormalizationInProgressSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('HighDataQualitySpecification', () => {
        it('should be satisfied by events with high data quality', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.HighDataQualitySpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event has high data quality (>= 60)');
        });
        it('should not be satisfied by events with low data quality', () => {
            // Arrange
            const lowQualityProps = { ...baseProps, dataQualityScore: 45 };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(lowQualityProps);
            const spec = new normalized_event_specification_1.HighDataQualitySpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('HasValidationErrorsSpecification', () => {
        it('should be satisfied by events with validation errors', () => {
            // Arrange
            const propsWithErrors = { ...baseProps, validationErrors: ['Error 1', 'Error 2'] };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(propsWithErrors);
            const spec = new normalized_event_specification_1.HasValidationErrorsSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event has validation errors');
        });
        it('should not be satisfied by events without validation errors', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.HasValidationErrorsSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('RequiresManualReviewSpecification', () => {
        it('should be satisfied by events requiring manual review', () => {
            // Arrange
            const reviewProps = { ...baseProps, requiresManualReview: true };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(reviewProps);
            const spec = new normalized_event_specification_1.RequiresManualReviewSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event requires manual review');
        });
        it('should not be satisfied by events not requiring manual review', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.RequiresManualReviewSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('ReadyForNextStageSpecification', () => {
        it('should be satisfied by events ready for next stage', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.ReadyForNextStageSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event is ready for next processing stage');
        });
        it('should not be satisfied by events not ready for next stage', () => {
            // Arrange
            const notReadyProps = {
                ...baseProps,
                normalizationStatus: normalized_event_entity_1.NormalizationStatus.PENDING,
                normalizationCompletedAt: undefined,
                normalizationResult: undefined,
            };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(notReadyProps);
            const spec = new normalized_event_specification_1.ReadyForNextStageSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('NormalizationStatusSpecification', () => {
        it('should be satisfied by events with matching status', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.NormalizationStatusSpecification([normalized_event_entity_1.NormalizationStatus.COMPLETED, normalized_event_entity_1.NormalizationStatus.FAILED]);
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event status is one of: COMPLETED, FAILED');
        });
        it('should not be satisfied by events with non-matching status', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.NormalizationStatusSpecification([normalized_event_entity_1.NormalizationStatus.PENDING, normalized_event_entity_1.NormalizationStatus.IN_PROGRESS]);
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('SchemaVersionSpecification', () => {
        it('should be satisfied by events with matching schema version', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.SchemaVersionSpecification('1.0.0');
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event uses schema version: 1.0.0');
        });
        it('should not be satisfied by events with non-matching schema version', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.SchemaVersionSpecification('2.0.0');
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('DataQualityScoreRangeSpecification', () => {
        it('should be satisfied by events within score range', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.DataQualityScoreRangeSpecification(80, 90);
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event data quality score is between 80 and 90');
        });
        it('should not be satisfied by events outside score range', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.DataQualityScoreRangeSpecification(90, 100);
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
        it('should handle minimum score only', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.DataQualityScoreRangeSpecification(80);
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event data quality score is at least 80');
        });
        it('should handle maximum score only', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.DataQualityScoreRangeSpecification(undefined, 90);
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event data quality score is at most 90');
        });
    });
    describe('AppliedRuleSpecification', () => {
        it('should be satisfied by events with applied rule', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.AppliedRuleSpecification('test-rule');
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event has applied rule: test-rule');
        });
        it('should not be satisfied by events without applied rule', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.AppliedRuleSpecification('non-existent-rule');
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('OriginalEventSpecification', () => {
        it('should be satisfied by events with matching original event ID', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.OriginalEventSpecification(baseProps.originalEventId);
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe(`Normalized event references original event: ${baseProps.originalEventId.toString()}`);
        });
        it('should not be satisfied by events with non-matching original event ID', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const differentId = shared_kernel_1.UniqueEntityId.create();
            const spec = new normalized_event_specification_1.OriginalEventSpecification(differentId);
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('ExceededMaxAttemptsSpecification', () => {
        it('should be satisfied by events that exceeded max attempts', () => {
            // Arrange
            const maxAttemptsProps = { ...baseProps, normalizationAttempts: 5 };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(maxAttemptsProps);
            const spec = new normalized_event_specification_1.ExceededMaxAttemptsSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event has exceeded maximum normalization attempts');
        });
        it('should not be satisfied by events that have not exceeded max attempts', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.ExceededMaxAttemptsSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('HighRiskNormalizedEventSpecification', () => {
        it('should be satisfied by high-risk events', () => {
            // Arrange
            const highRiskProps = { ...baseProps, riskScore: 85 };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(highRiskProps);
            const spec = new normalized_event_specification_1.HighRiskNormalizedEventSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event has high risk score (>= 80)');
        });
        it('should not be satisfied by low-risk events', () => {
            // Arrange
            const lowRiskProps = { ...baseProps, riskScore: 50 };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(lowRiskProps);
            const spec = new normalized_event_specification_1.HighRiskNormalizedEventSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('ReviewedEventSpecification', () => {
        it('should be satisfied by reviewed events', () => {
            // Arrange
            const reviewedProps = {
                ...baseProps,
                requiresManualReview: true,
                reviewedAt: new Date(),
                reviewedBy: '<EMAIL>',
            };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(reviewedProps);
            const spec = new normalized_event_specification_1.ReviewedEventSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event has been manually reviewed');
        });
        it('should not be satisfied by non-reviewed events', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.ReviewedEventSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('PendingReviewSpecification', () => {
        it('should be satisfied by events pending review', () => {
            // Arrange
            const pendingReviewProps = {
                ...baseProps,
                requiresManualReview: true,
                reviewedAt: undefined,
            };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(pendingReviewProps);
            const spec = new normalized_event_specification_1.PendingReviewSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event is pending manual review');
        });
        it('should not be satisfied by events not requiring review', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.PendingReviewSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
        it('should not be satisfied by already reviewed events', () => {
            // Arrange
            const reviewedProps = {
                ...baseProps,
                requiresManualReview: true,
                reviewedAt: new Date(),
                reviewedBy: '<EMAIL>',
            };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(reviewedProps);
            const spec = new normalized_event_specification_1.PendingReviewSpecification();
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('NormalizationDurationRangeSpecification', () => {
        it('should be satisfied by events within duration range', () => {
            // Arrange
            const startTime = new Date();
            const endTime = new Date(startTime.getTime() + 5000);
            const durationProps = {
                ...baseProps,
                normalizationStartedAt: startTime,
                normalizationCompletedAt: endTime,
            };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(durationProps);
            const spec = new normalized_event_specification_1.NormalizationDurationRangeSpecification(1000, 10000);
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
            expect(spec.getDescription()).toBe('Normalized event duration is between 1000ms and 10000ms');
        });
        it('should not be satisfied by events outside duration range', () => {
            // Arrange
            const startTime = new Date();
            const endTime = new Date(startTime.getTime() + 15000);
            const durationProps = {
                ...baseProps,
                normalizationStartedAt: startTime,
                normalizationCompletedAt: endTime,
            };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(durationProps);
            const spec = new normalized_event_specification_1.NormalizationDurationRangeSpecification(1000, 10000);
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
        it('should not be satisfied by events without duration information', () => {
            // Arrange
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            const spec = new normalized_event_specification_1.NormalizationDurationRangeSpecification(1000, 10000);
            // Act & Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);
        });
    });
    describe('NormalizedEventSpecificationBuilder', () => {
        it('should build specification with single condition', () => {
            // Arrange & Act
            const spec = normalized_event_specification_1.NormalizedEventSpecificationBuilder
                .create()
                .normalizationCompleted()
                .build();
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            // Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
        });
        it('should build specification with multiple AND conditions', () => {
            // Arrange & Act
            const spec = normalized_event_specification_1.NormalizedEventSpecificationBuilder
                .create()
                .normalizationCompleted()
                .highDataQuality()
                .withSchemaVersion('1.0.0')
                .build();
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            // Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
        });
        it('should build specification with multiple OR conditions', () => {
            // Arrange & Act
            const spec = normalized_event_specification_1.NormalizedEventSpecificationBuilder
                .create()
                .normalizationCompleted()
                .normalizationFailed()
                .buildWithOr();
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            // Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
        });
        it('should fail when building without conditions', () => {
            // Arrange & Act & Assert
            expect(() => {
                normalized_event_specification_1.NormalizedEventSpecificationBuilder
                    .create()
                    .build();
            }).toThrow('At least one specification must be added');
        });
        it('should build complex specification with all conditions', () => {
            // Arrange & Act
            const spec = normalized_event_specification_1.NormalizedEventSpecificationBuilder
                .create()
                .normalizationCompleted()
                .highDataQuality()
                .readyForNextStage()
                .withSchemaVersion('1.0.0')
                .dataQualityScoreRange(80, 100)
                .withAppliedRule('test-rule')
                .build();
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            // Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
        });
        it('should build specification for events requiring attention', () => {
            // Arrange & Act
            const spec = normalized_event_specification_1.NormalizedEventSpecificationBuilder
                .create()
                .requiresManualReview()
                .pendingReview()
                .buildWithOr();
            const pendingReviewProps = {
                ...baseProps,
                requiresManualReview: true,
                reviewedAt: undefined,
            };
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(pendingReviewProps);
            // Assert
            expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);
        });
    });
    describe('specification combinations', () => {
        it('should combine specifications with AND logic', () => {
            // Arrange
            const spec1 = new normalized_event_specification_1.NormalizationCompletedSpecification();
            const spec2 = new normalized_event_specification_1.HighDataQualitySpecification();
            const combinedSpec = spec1.and(spec2);
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            // Act & Assert
            expect(combinedSpec.isSatisfiedBy(normalizedEvent)).toBe(true);
        });
        it('should combine specifications with OR logic', () => {
            // Arrange
            const spec1 = new normalized_event_specification_1.NormalizationCompletedSpecification();
            const spec2 = new normalized_event_specification_1.NormalizationFailedSpecification();
            const combinedSpec = spec1.or(spec2);
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            // Act & Assert
            expect(combinedSpec.isSatisfiedBy(normalizedEvent)).toBe(true);
        });
        it('should negate specifications', () => {
            // Arrange
            const spec = new normalized_event_specification_1.NormalizationFailedSpecification();
            const negatedSpec = spec.not();
            const normalizedEvent = normalized_event_entity_1.NormalizedEvent.create(baseProps);
            // Act & Assert
            expect(negatedSpec.isSatisfiedBy(normalizedEvent)).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************