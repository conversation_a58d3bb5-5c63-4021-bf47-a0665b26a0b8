193567ed114a6e00a4a11f5206c1beba
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KillChainStage = exports.SophisticationLevel = exports.ThreatActorType = exports.IndicatorType = exports.ThreatDetectionSource = exports.ThreatAnalysisMethod = exports.ThreatCategory = exports.ThreatType = void 0;
/**
 * Threat Type Enum
 */
var ThreatType;
(function (ThreatType) {
    ThreatType["MALWARE"] = "MALWARE";
    ThreatType["PHISHING"] = "PHISHING";
    ThreatType["RANSOMWARE"] = "RANSOMWARE";
    ThreatType["APT"] = "APT";
    ThreatType["INSIDER_THREAT"] = "INSIDER_THREAT";
    ThreatType["DDoS"] = "DDOS";
    ThreatType["BRUTE_FORCE"] = "BRUTE_FORCE";
    ThreatType["SQL_INJECTION"] = "SQL_INJECTION";
    ThreatType["XSS"] = "XSS";
    ThreatType["PRIVILEGE_ESCALATION"] = "PRIVILEGE_ESCALATION";
    ThreatType["DATA_EXFILTRATION"] = "DATA_EXFILTRATION";
    ThreatType["LATERAL_MOVEMENT"] = "LATERAL_MOVEMENT";
    ThreatType["PERSISTENCE"] = "PERSISTENCE";
    ThreatType["RECONNAISSANCE"] = "RECONNAISSANCE";
    ThreatType["COMMAND_AND_CONTROL"] = "COMMAND_AND_CONTROL";
    ThreatType["UNKNOWN"] = "UNKNOWN";
})(ThreatType || (exports.ThreatType = ThreatType = {}));
/**
 * Threat Category Enum
 */
var ThreatCategory;
(function (ThreatCategory) {
    ThreatCategory["NETWORK"] = "NETWORK";
    ThreatCategory["ENDPOINT"] = "ENDPOINT";
    ThreatCategory["APPLICATION"] = "APPLICATION";
    ThreatCategory["DATA"] = "DATA";
    ThreatCategory["IDENTITY"] = "IDENTITY";
    ThreatCategory["INFRASTRUCTURE"] = "INFRASTRUCTURE";
    ThreatCategory["CLOUD"] = "CLOUD";
    ThreatCategory["IOT"] = "IOT";
    ThreatCategory["MOBILE"] = "MOBILE";
    ThreatCategory["EMAIL"] = "EMAIL";
    ThreatCategory["WEB"] = "WEB";
    ThreatCategory["PHYSICAL"] = "PHYSICAL";
})(ThreatCategory || (exports.ThreatCategory = ThreatCategory = {}));
/**
 * Threat Analysis Method Enum
 */
var ThreatAnalysisMethod;
(function (ThreatAnalysisMethod) {
    ThreatAnalysisMethod["SIGNATURE_BASED"] = "SIGNATURE_BASED";
    ThreatAnalysisMethod["BEHAVIORAL_ANALYSIS"] = "BEHAVIORAL_ANALYSIS";
    ThreatAnalysisMethod["MACHINE_LEARNING"] = "MACHINE_LEARNING";
    ThreatAnalysisMethod["HEURISTIC_ANALYSIS"] = "HEURISTIC_ANALYSIS";
    ThreatAnalysisMethod["THREAT_INTELLIGENCE"] = "THREAT_INTELLIGENCE";
    ThreatAnalysisMethod["ANOMALY_DETECTION"] = "ANOMALY_DETECTION";
    ThreatAnalysisMethod["PATTERN_MATCHING"] = "PATTERN_MATCHING";
    ThreatAnalysisMethod["STATISTICAL_ANALYSIS"] = "STATISTICAL_ANALYSIS";
    ThreatAnalysisMethod["HYBRID"] = "HYBRID";
})(ThreatAnalysisMethod || (exports.ThreatAnalysisMethod = ThreatAnalysisMethod = {}));
/**
 * Threat Detection Source Enum
 */
var ThreatDetectionSource;
(function (ThreatDetectionSource) {
    ThreatDetectionSource["INTERNAL_ANALYSIS"] = "INTERNAL_ANALYSIS";
    ThreatDetectionSource["THREAT_INTELLIGENCE"] = "THREAT_INTELLIGENCE";
    ThreatDetectionSource["MACHINE_LEARNING"] = "MACHINE_LEARNING";
    ThreatDetectionSource["SIGNATURE_DATABASE"] = "SIGNATURE_DATABASE";
    ThreatDetectionSource["BEHAVIORAL_ANALYSIS"] = "BEHAVIORAL_ANALYSIS";
    ThreatDetectionSource["COMMUNITY_FEEDS"] = "COMMUNITY_FEEDS";
    ThreatDetectionSource["COMMERCIAL_FEEDS"] = "COMMERCIAL_FEEDS";
    ThreatDetectionSource["GOVERNMENT_FEEDS"] = "GOVERNMENT_FEEDS";
    ThreatDetectionSource["OSINT"] = "OSINT";
    ThreatDetectionSource["SANDBOX_ANALYSIS"] = "SANDBOX_ANALYSIS";
})(ThreatDetectionSource || (exports.ThreatDetectionSource = ThreatDetectionSource = {}));
/**
 * Indicator Type Enum
 */
var IndicatorType;
(function (IndicatorType) {
    IndicatorType["IP_ADDRESS"] = "IP_ADDRESS";
    IndicatorType["DOMAIN"] = "DOMAIN";
    IndicatorType["URL"] = "URL";
    IndicatorType["FILE_HASH"] = "FILE_HASH";
    IndicatorType["EMAIL"] = "EMAIL";
    IndicatorType["USER_AGENT"] = "USER_AGENT";
    IndicatorType["REGISTRY_KEY"] = "REGISTRY_KEY";
    IndicatorType["FILE_PATH"] = "FILE_PATH";
    IndicatorType["PROCESS_NAME"] = "PROCESS_NAME";
    IndicatorType["CERTIFICATE"] = "CERTIFICATE";
    IndicatorType["MUTEX"] = "MUTEX";
    IndicatorType["YARA_RULE"] = "YARA_RULE";
    IndicatorType["CUSTOM"] = "CUSTOM";
})(IndicatorType || (exports.IndicatorType = IndicatorType = {}));
/**
 * Threat Actor Type Enum
 */
var ThreatActorType;
(function (ThreatActorType) {
    ThreatActorType["NATION_STATE"] = "NATION_STATE";
    ThreatActorType["CYBERCRIMINAL"] = "CYBERCRIMINAL";
    ThreatActorType["HACKTIVIST"] = "HACKTIVIST";
    ThreatActorType["INSIDER"] = "INSIDER";
    ThreatActorType["TERRORIST"] = "TERRORIST";
    ThreatActorType["UNKNOWN"] = "UNKNOWN";
})(ThreatActorType || (exports.ThreatActorType = ThreatActorType = {}));
/**
 * Sophistication Level Enum
 */
var SophisticationLevel;
(function (SophisticationLevel) {
    SophisticationLevel["MINIMAL"] = "MINIMAL";
    SophisticationLevel["INTERMEDIATE"] = "INTERMEDIATE";
    SophisticationLevel["ADVANCED"] = "ADVANCED";
    SophisticationLevel["EXPERT"] = "EXPERT";
    SophisticationLevel["INNOVATOR"] = "INNOVATOR";
    SophisticationLevel["STRATEGIC"] = "STRATEGIC";
})(SophisticationLevel || (exports.SophisticationLevel = SophisticationLevel = {}));
/**
 * Kill Chain Stage Enum
 */
var KillChainStage;
(function (KillChainStage) {
    KillChainStage["RECONNAISSANCE"] = "RECONNAISSANCE";
    KillChainStage["WEAPONIZATION"] = "WEAPONIZATION";
    KillChainStage["DELIVERY"] = "DELIVERY";
    KillChainStage["EXPLOITATION"] = "EXPLOITATION";
    KillChainStage["INSTALLATION"] = "INSTALLATION";
    KillChainStage["COMMAND_AND_CONTROL"] = "COMMAND_AND_CONTROL";
    KillChainStage["ACTIONS_ON_OBJECTIVES"] = "ACTIONS_ON_OBJECTIVES";
})(KillChainStage || (exports.KillChainStage = KillChainStage = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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