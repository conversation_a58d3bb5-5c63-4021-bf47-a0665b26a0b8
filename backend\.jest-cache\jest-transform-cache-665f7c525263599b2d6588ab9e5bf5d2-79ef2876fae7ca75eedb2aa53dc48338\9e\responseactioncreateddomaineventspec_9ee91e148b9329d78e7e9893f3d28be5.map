{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-created.domain-event.spec.ts", "mappings": ";;AAAA,kGAA2H;AAC3H,gEAA8D;AAC9D,mEAA0D;AAC1D,uEAA8D;AAE9D,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;IAChD,IAAI,WAA2B,CAAC;IAChC,IAAI,SAAyC,CAAC;IAC9C,IAAI,KAAuC,CAAC;IAE5C,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;QACxC,SAAS,GAAG;YACV,UAAU,EAAE,6BAAU,CAAC,QAAQ;YAC/B,MAAM,EAAE,iCAAY,CAAC,OAAO;YAC5B,KAAK,EAAE,4BAA4B;YACnC,QAAQ,EAAE,MAAM;YAChB,WAAW,EAAE,IAAI;YACjB,gBAAgB,EAAE,KAAK;YACvB,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;aACtB;YACD,cAAc,EAAE,8BAAc,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;YACpD,eAAe,EAAE,8BAAc,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;SACtD,CAAC;QACF,KAAK,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IACvE,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,uEAAgC,CAAC,CAAC;YAC/D,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAE,8BAAc,CAAC,QAAQ,EAAE;gBAClC,UAAU,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBAClC,YAAY,EAAE,CAAC;gBACf,aAAa,EAAE,UAAU;gBACzB,WAAW,EAAE,WAAW;gBACxB,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;aAC7B,CAAC;YAEF,MAAM,WAAW,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEhG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACxD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAC9D,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;YAClE,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YACpE,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YAChE,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC9D,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,aAAa,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1C,MAAM,WAAW,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,aAAa,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,QAAQ,EAAE,UAAU;aACrB,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE5C,MAAM,iBAAiB,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE/C,MAAM,YAAY,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtD,MAAM,UAAU,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,UAAU,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEnD,MAAM,cAAc,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,cAAc,EAAE,SAAS;gBACzB,eAAe,EAAE,SAAS;aAC3B,CAAC,CAAC;YACH,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,0EAA0E,EAAE,GAAG,EAAE;YAClF,MAAM,aAAa,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,gBAAgB,EAAE,IAAI;gBACtB,WAAW,EAAE,KAAK;aACnB,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,aAAa,CAAC,yBAAyB,EAAE,CAAC;YAClE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,6CAA6C,CAAC,CAAC;YACjF,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,cAAc,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,WAAW,EAAE,IAAI;gBACjB,gBAAgB,EAAE,KAAK;aACxB,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,cAAc,CAAC,yBAAyB,EAAE,CAAC;YACnE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,sCAAsC,CAAC,CAAC;YAC1E,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,MAAM,eAAe,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;YAC1D,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;YACnE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,sCAAsC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,aAAa,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,QAAQ,EAAE,UAAU;aACrB,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,aAAa,CAAC,yBAAyB,EAAE,CAAC;YAClE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;YACxE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,eAAe,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;YAC1D,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YACpE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,uCAAuC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAC7E,MAAM,iBAAiB,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,iBAAiB,CAAC,yBAAyB,EAAE,CAAC;YACtE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YACpE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,aAAa,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,gBAAgB,EAAE,IAAI;aACvB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,aAAa,CAAC,sBAAsB,EAAE,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,aAAa,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,QAAQ,EAAE,UAAU;aACrB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,aAAa,CAAC,sBAAsB,EAAE,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,UAAU,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,SAAS,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,kBAAkB;aAC1C,CAAC,CAAC;YACH,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,YAAY,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,WAAW,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,WAAW,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,aAAa,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,QAAQ,EAAE,UAAU;aACrB,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,YAAY,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;gBACrC,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,WAAW,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,QAAQ,EAAE,QAAQ;gBAClB,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,QAAQ,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACjE,GAAG,SAAS;gBACZ,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW;YAE9D,MAAM,SAAS,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,kBAAkB;aAC1C,CAAC,CAAC;YACH,MAAM,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEvD,MAAM,YAAY,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU;QACvE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,aAAa,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,UAAU,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,mBAAmB;aAC3C,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,cAAc,GAAG,KAAK,CAAC,2BAA2B,EAAE,CAAC;YAC3D,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC;YAClF,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,8CAA8C,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,WAAW,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,WAAW;aACnC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,WAAW,CAAC,2BAA2B,EAAE,CAAC;YACjE,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,6CAA6C,CAAC,CAAC;YAChF,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,YAAY,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,YAAY,CAAC,2BAA2B,EAAE,CAAC;YAClE,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,0CAA0C,CAAC,CAAC;YAC7E,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,sCAAsC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,aAAa,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,aAAa,CAAC,2BAA2B,EAAE,CAAC;YACnE,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,oDAAoD,CAAC,CAAC;YACvF,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,yCAAyC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAEpD,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACjE,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAChE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACzD,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9C,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBACxC,QAAQ,EAAE,MAAM;gBAChB,QAAQ,EAAE,aAAa;gBACvB,OAAO,EAAE,QAAQ;gBACjB,SAAS,EAAE,KAAK;gBAChB,sBAAsB,EAAE,CAAC;gBACzB,WAAW,EAAE,IAAI;gBACjB,gBAAgB,EAAE,KAAK;aACxB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,aAAa,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;YAC7C,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,cAAc,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,cAAc,EAAE,SAAS;gBACzB,eAAe,EAAE,SAAS;gBAC1B,sBAAsB,EAAE,SAAS;aAClC,CAAC,CAAC;YAEH,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,YAAY,GAAG,IAAI,uEAAgC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,gBAA8B;aAC3C,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,CAAC,YAAY,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1D,MAAM,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-created.domain-event.spec.ts"], "sourcesContent": ["import { ResponseActionCreatedDomainEvent, ResponseActionCreatedEventData } from '../response-action-created.domain-event';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { ActionType } from '../../enums/action-type.enum';\r\nimport { ActionStatus } from '../../enums/action-status.enum';\r\n\r\ndescribe('ResponseActionCreatedDomainEvent', () => {\r\n  let aggregateId: UniqueEntityId;\r\n  let eventData: ResponseActionCreatedEventData;\r\n  let event: ResponseActionCreatedDomainEvent;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.generate();\r\n    eventData = {\r\n      actionType: ActionType.BLOCK_IP,\r\n      status: ActionStatus.PENDING,\r\n      title: 'Block malicious IP address',\r\n      priority: 'high',\r\n      isAutomated: true,\r\n      approvalRequired: false,\r\n      target: {\r\n        type: 'system',\r\n        id: 'firewall-001',\r\n        name: 'Main Firewall',\r\n      },\r\n      relatedEventId: UniqueEntityId.generate().toString(),\r\n      relatedThreatId: UniqueEntityId.generate().toString(),\r\n    };\r\n    event = new ResponseActionCreatedDomainEvent(aggregateId, eventData);\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create event with required data', () => {\r\n      expect(event).toBeInstanceOf(ResponseActionCreatedDomainEvent);\r\n      expect(event.aggregateId).toBe(aggregateId);\r\n      expect(event.eventData).toBe(eventData);\r\n      expect(event.occurredOn).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should create event with custom options', () => {\r\n      const customOptions = {\r\n        eventId: UniqueEntityId.generate(),\r\n        occurredOn: new Date('2023-01-01'),\r\n        eventVersion: 2,\r\n        correlationId: 'corr-123',\r\n        causationId: 'cause-456',\r\n        metadata: { source: 'test' },\r\n      };\r\n\r\n      const customEvent = new ResponseActionCreatedDomainEvent(aggregateId, eventData, customOptions);\r\n\r\n      expect(customEvent.eventId).toBe(customOptions.eventId);\r\n      expect(customEvent.occurredOn).toBe(customOptions.occurredOn);\r\n      expect(customEvent.eventVersion).toBe(customOptions.eventVersion);\r\n      expect(customEvent.correlationId).toBe(customOptions.correlationId);\r\n      expect(customEvent.causationId).toBe(customOptions.causationId);\r\n      expect(customEvent.metadata).toEqual(customOptions.metadata);\r\n    });\r\n  });\r\n\r\n  describe('getters', () => {\r\n    it('should provide access to event data properties', () => {\r\n      expect(event.actionType).toBe(ActionType.BLOCK_IP);\r\n      expect(event.status).toBe(ActionStatus.PENDING);\r\n      expect(event.title).toBe('Block malicious IP address');\r\n      expect(event.priority).toBe('high');\r\n      expect(event.isAutomated).toBe(true);\r\n      expect(event.approvalRequired).toBe(false);\r\n      expect(event.target).toEqual(eventData.target);\r\n      expect(event.relatedEventId).toBe(eventData.relatedEventId);\r\n      expect(event.relatedThreatId).toBe(eventData.relatedThreatId);\r\n      expect(event.relatedVulnerabilityId).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('classification methods', () => {\r\n    it('should identify high priority actions', () => {\r\n      expect(event.isHighPriority()).toBe(true);\r\n\r\n      const normalEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        priority: 'normal',\r\n      });\r\n      expect(normalEvent.isHighPriority()).toBe(false);\r\n    });\r\n\r\n    it('should identify critical actions', () => {\r\n      const criticalEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        priority: 'critical',\r\n      });\r\n      expect(criticalEvent.isCritical()).toBe(true);\r\n      expect(event.isCritical()).toBe(false);\r\n    });\r\n\r\n    it('should identify security actions', () => {\r\n      expect(event.isSecurityAction()).toBe(true);\r\n\r\n      const notificationEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(notificationEvent.isSecurityAction()).toBe(false);\r\n    });\r\n\r\n    it('should identify containment actions', () => {\r\n      expect(event.isContainmentAction()).toBe(true);\r\n\r\n      const isolateEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.ISOLATE_SYSTEM,\r\n      });\r\n      expect(isolateEvent.isContainmentAction()).toBe(true);\r\n\r\n      const emailEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(emailEvent.isContainmentAction()).toBe(false);\r\n    });\r\n\r\n    it('should identify notification actions', () => {\r\n      const emailEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(emailEvent.isNotificationAction()).toBe(true);\r\n      expect(event.isNotificationAction()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('target and relationship methods', () => {\r\n    it('should check target entity type', () => {\r\n      expect(event.targetsEntityType('system')).toBe(true);\r\n      expect(event.targetsEntityType('user')).toBe(false);\r\n    });\r\n\r\n    it('should check related entities', () => {\r\n      expect(event.isEventRelated()).toBe(true);\r\n      expect(event.isThreatRelated()).toBe(true);\r\n      expect(event.isVulnerabilityRelated()).toBe(false);\r\n\r\n      const unrelatedEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        relatedEventId: undefined,\r\n        relatedThreatId: undefined,\r\n      });\r\n      expect(unrelatedEvent.isEventRelated()).toBe(false);\r\n      expect(unrelatedEvent.isThreatRelated()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('recommendation methods', () => {\r\n    it('should provide next action recommendations for approval required actions', () => {\r\n      const approvalEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        approvalRequired: true,\r\n        isAutomated: false,\r\n      });\r\n\r\n      const recommendations = approvalEvent.getRecommendedNextActions();\r\n      expect(recommendations).toContain('Request approval from appropriate authority');\r\n      expect(recommendations).toContain('Notify approval workflow system');\r\n    });\r\n\r\n    it('should provide next action recommendations for automated actions', () => {\r\n      const automatedEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        isAutomated: true,\r\n        approvalRequired: false,\r\n      });\r\n\r\n      const recommendations = automatedEvent.getRecommendedNextActions();\r\n      expect(recommendations).toContain('Queue action for automated execution');\r\n      expect(recommendations).toContain('Validate execution prerequisites');\r\n    });\r\n\r\n    it('should provide next action recommendations for high priority actions', () => {\r\n      const recommendations = event.getRecommendedNextActions();\r\n      expect(recommendations).toContain('Prioritize in execution queue');\r\n      expect(recommendations).toContain('Notify high-priority action handlers');\r\n    });\r\n\r\n    it('should provide next action recommendations for critical actions', () => {\r\n      const criticalEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        priority: 'critical',\r\n      });\r\n\r\n      const recommendations = criticalEvent.getRecommendedNextActions();\r\n      expect(recommendations).toContain('Escalate to incident response team');\r\n      expect(recommendations).toContain('Activate emergency response procedures');\r\n    });\r\n\r\n    it('should provide next action recommendations for containment actions', () => {\r\n      const recommendations = event.getRecommendedNextActions();\r\n      expect(recommendations).toContain('Prepare containment procedures');\r\n      expect(recommendations).toContain('Validate containment scope and impact');\r\n    });\r\n\r\n    it('should provide next action recommendations for notification actions', () => {\r\n      const notificationEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n\r\n      const recommendations = notificationEvent.getRecommendedNextActions();\r\n      expect(recommendations).toContain('Prepare notification templates');\r\n      expect(recommendations).toContain('Validate recipient lists');\r\n    });\r\n  });\r\n\r\n  describe('notification targets', () => {\r\n    it('should identify notification targets for approval required actions', () => {\r\n      const approvalEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        approvalRequired: true,\r\n      });\r\n\r\n      const targets = approvalEvent.getNotificationTargets();\r\n      expect(targets).toContain('approval-managers');\r\n    });\r\n\r\n    it('should identify notification targets for high priority actions', () => {\r\n      const targets = event.getNotificationTargets();\r\n      expect(targets).toContain('security-team');\r\n      expect(targets).toContain('incident-response-team');\r\n    });\r\n\r\n    it('should identify notification targets for critical actions', () => {\r\n      const criticalEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        priority: 'critical',\r\n      });\r\n\r\n      const targets = criticalEvent.getNotificationTargets();\r\n      expect(targets).toContain('security-managers');\r\n      expect(targets).toContain('on-call-engineers');\r\n    });\r\n\r\n    it('should identify notification targets for security actions', () => {\r\n      const targets = event.getNotificationTargets();\r\n      expect(targets).toContain('security-analysts');\r\n    });\r\n\r\n    it('should identify notification targets for containment actions', () => {\r\n      const targets = event.getNotificationTargets();\r\n      expect(targets).toContain('containment-specialists');\r\n    });\r\n  });\r\n\r\n  describe('categorization methods', () => {\r\n    it('should categorize containment actions', () => {\r\n      expect(event.getActionCategory()).toBe('Containment');\r\n    });\r\n\r\n    it('should categorize notification actions', () => {\r\n      const emailEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(emailEvent.getActionCategory()).toBe('Notification');\r\n    });\r\n\r\n    it('should categorize detection actions', () => {\r\n      const scanEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.VULNERABILITY_SCAN,\r\n      });\r\n      expect(scanEvent.getActionCategory()).toBe('Detection');\r\n    });\r\n\r\n    it('should categorize eradication actions', () => {\r\n      const malwareEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.REMOVE_MALWARE,\r\n      });\r\n      expect(malwareEvent.getActionCategory()).toBe('Eradication');\r\n    });\r\n\r\n    it('should categorize recovery actions', () => {\r\n      const backupEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n      expect(backupEvent.getActionCategory()).toBe('Recovery');\r\n    });\r\n\r\n    it('should categorize other actions', () => {\r\n      const customEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.EXECUTE_SCRIPT,\r\n      });\r\n      expect(customEvent.getActionCategory()).toBe('Other');\r\n    });\r\n  });\r\n\r\n  describe('urgency and timing methods', () => {\r\n    it('should determine execution urgency for critical actions', () => {\r\n      const criticalEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        priority: 'critical',\r\n      });\r\n      expect(criticalEvent.getExecutionUrgency()).toBe('immediate');\r\n    });\r\n\r\n    it('should determine execution urgency for high priority actions', () => {\r\n      expect(event.getExecutionUrgency()).toBe('urgent');\r\n    });\r\n\r\n    it('should determine execution urgency for containment actions', () => {\r\n      const isolateEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.ISOLATE_SYSTEM,\r\n        priority: 'normal',\r\n      });\r\n      expect(isolateEvent.getExecutionUrgency()).toBe('urgent');\r\n    });\r\n\r\n    it('should determine execution urgency for normal actions', () => {\r\n      const normalEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        priority: 'normal',\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(normalEvent.getExecutionUrgency()).toBe('normal');\r\n    });\r\n\r\n    it('should determine execution urgency for low priority actions', () => {\r\n      const lowEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        priority: 'low',\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(lowEvent.getExecutionUrgency()).toBe('low');\r\n    });\r\n\r\n    it('should estimate execution time for different action types', () => {\r\n      expect(event.getEstimatedExecutionTime()).toBe(1); // BLOCK_IP\r\n\r\n      const scanEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.VULNERABILITY_SCAN,\r\n      });\r\n      expect(scanEvent.getEstimatedExecutionTime()).toBe(15);\r\n\r\n      const unknownEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.EXECUTE_SCRIPT,\r\n      });\r\n      expect(unknownEvent.getEstimatedExecutionTime()).toBe(30); // Default\r\n    });\r\n  });\r\n\r\n  describe('risk assessment methods', () => {\r\n    it('should assess risk level for high-risk actions', () => {\r\n      const shutdownEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SHUTDOWN_SYSTEM,\r\n      });\r\n      expect(shutdownEvent.getRiskLevel()).toBe('high');\r\n    });\r\n\r\n    it('should assess risk level for medium-risk actions', () => {\r\n      const patchEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.PATCH_VULNERABILITY,\r\n      });\r\n      expect(patchEvent.getRiskLevel()).toBe('medium');\r\n    });\r\n\r\n    it('should assess risk level for low-risk actions', () => {\r\n      expect(event.getRiskLevel()).toBe('low');\r\n    });\r\n  });\r\n\r\n  describe('compliance methods', () => {\r\n    it('should provide compliance considerations for containment actions', () => {\r\n      const considerations = event.getComplianceConsiderations();\r\n      expect(considerations).toContain('Document containment rationale for compliance');\r\n      expect(considerations).toContain('Ensure containment actions are proportionate');\r\n    });\r\n\r\n    it('should provide compliance considerations for file deletion', () => {\r\n      const deleteEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.DELETE_FILE,\r\n      });\r\n\r\n      const considerations = deleteEvent.getComplianceConsiderations();\r\n      expect(considerations).toContain('Ensure data retention policies are followed');\r\n      expect(considerations).toContain('Create forensic copies before deletion');\r\n    });\r\n\r\n    it('should provide compliance considerations for account disabling', () => {\r\n      const disableEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.DISABLE_ACCOUNT,\r\n      });\r\n\r\n      const considerations = disableEvent.getComplianceConsiderations();\r\n      expect(considerations).toContain('Follow user access management procedures');\r\n      expect(considerations).toContain('Document account disabling rationale');\r\n    });\r\n\r\n    it('should provide compliance considerations for high-risk actions', () => {\r\n      const shutdownEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SHUTDOWN_SYSTEM,\r\n      });\r\n\r\n      const considerations = shutdownEvent.getComplianceConsiderations();\r\n      expect(considerations).toContain('Obtain appropriate approvals for high-risk actions');\r\n      expect(considerations).toContain('Document risk assessment and mitigation');\r\n    });\r\n  });\r\n\r\n  describe('integration event conversion', () => {\r\n    it('should convert to integration event format', () => {\r\n      const integrationEvent = event.toIntegrationEvent();\r\n\r\n      expect(integrationEvent.eventType).toBe('ResponseActionCreated');\r\n      expect(integrationEvent.action).toBe('response_action_created');\r\n      expect(integrationEvent.resource).toBe('ResponseAction');\r\n      expect(integrationEvent.resourceId).toBe(aggregateId.toString());\r\n      expect(integrationEvent.data).toBe(eventData);\r\n      expect(integrationEvent.metadata).toEqual({\r\n        priority: 'high',\r\n        category: 'Containment',\r\n        urgency: 'urgent',\r\n        riskLevel: 'low',\r\n        estimatedExecutionTime: 1,\r\n        isAutomated: true,\r\n        approvalRequired: false,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle events without target', () => {\r\n      const noTargetEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        target: undefined,\r\n      });\r\n\r\n      expect(noTargetEvent.target).toBeUndefined();\r\n      expect(noTargetEvent.targetsEntityType('system')).toBe(false);\r\n    });\r\n\r\n    it('should handle events without related entities', () => {\r\n      const unrelatedEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        relatedEventId: undefined,\r\n        relatedThreatId: undefined,\r\n        relatedVulnerabilityId: undefined,\r\n      });\r\n\r\n      expect(unrelatedEvent.isEventRelated()).toBe(false);\r\n      expect(unrelatedEvent.isThreatRelated()).toBe(false);\r\n      expect(unrelatedEvent.isVulnerabilityRelated()).toBe(false);\r\n    });\r\n\r\n    it('should handle unknown action types gracefully', () => {\r\n      const unknownEvent = new ResponseActionCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: 'UNKNOWN_ACTION' as ActionType,\r\n      });\r\n\r\n      expect(unknownEvent.getActionCategory()).toBe('Other');\r\n      expect(unknownEvent.getEstimatedExecutionTime()).toBe(30);\r\n      expect(unknownEvent.getRiskLevel()).toBe('low');\r\n    });\r\n  });\r\n});"], "version": 3}