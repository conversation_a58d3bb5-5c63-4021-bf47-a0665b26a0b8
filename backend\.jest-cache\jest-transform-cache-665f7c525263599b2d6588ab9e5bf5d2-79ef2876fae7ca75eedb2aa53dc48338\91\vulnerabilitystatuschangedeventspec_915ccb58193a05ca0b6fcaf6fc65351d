8a8d568696dc3d47584248ae3fd6023b
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vulnerability_status_changed_event_1 = require("../vulnerability-status-changed.event");
const vulnerability_entity_1 = require("../../entities/vulnerability/vulnerability.entity");
const shared_kernel_1 = require("../../../../../shared-kernel");
const node_test_1 = require("node:test");
const node_test_2 = require("node:test");
const node_test_3 = require("node:test");
(0, node_test_2.describe)('VulnerabilityStatusChangedEvent', () => {
    let aggregateId;
    let baseEventData;
    (0, node_test_3.beforeEach)(() => {
        aggregateId = shared_kernel_1.UniqueEntityId.generate();
        baseEventData = {
            vulnerabilityId: 'vuln-123',
            oldStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
            newStatus: vulnerability_entity_1.VulnerabilityStatus.CONFIRMED,
            reason: 'Confirmed by security team',
            timestamp: new Date().toISOString(),
            changedBy: 'security-analyst',
            context: { reviewId: 'review-456' },
        };
    });
    (0, node_test_2.describe)('constructor and getters', () => {
        (0, node_test_1.it)('should create event with all properties', () => {
            const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, baseEventData);
            expect(event.vulnerabilityId).toBe('vuln-123');
            expect(event.oldStatus).toBe(vulnerability_entity_1.VulnerabilityStatus.DISCOVERED);
            expect(event.newStatus).toBe(vulnerability_entity_1.VulnerabilityStatus.CONFIRMED);
            expect(event.reason).toBe('Confirmed by security team');
            expect(event.changedBy).toBe('security-analyst');
            expect(event.context).toEqual({ reviewId: 'review-456' });
            expect(event.aggregateId).toBe(aggregateId);
        });
        (0, node_test_1.it)('should create event with minimal data', () => {
            const minimalData = {
                vulnerabilityId: 'vuln-123',
                oldStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                newStatus: vulnerability_entity_1.VulnerabilityStatus.CONFIRMED,
                reason: 'Status change',
                timestamp: new Date().toISOString(),
            };
            const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, minimalData);
            expect(event.changedBy).toBeUndefined();
            expect(event.context).toBeUndefined();
        });
    });
    (0, node_test_2.describe)('status transition analysis', () => {
        (0, node_test_2.describe)('isStatusProgression', () => {
            (0, node_test_1.it)('should identify forward progression', () => {
                const progressionCases = [
                    { from: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED, to: vulnerability_entity_1.VulnerabilityStatus.CONFIRMED },
                    { from: vulnerability_entity_1.VulnerabilityStatus.CONFIRMED, to: vulnerability_entity_1.VulnerabilityStatus.TRIAGED },
                    { from: vulnerability_entity_1.VulnerabilityStatus.TRIAGED, to: vulnerability_entity_1.VulnerabilityStatus.IN_PROGRESS },
                    { from: vulnerability_entity_1.VulnerabilityStatus.IN_PROGRESS, to: vulnerability_entity_1.VulnerabilityStatus.REMEDIATED },
                    { from: vulnerability_entity_1.VulnerabilityStatus.REMEDIATED, to: vulnerability_entity_1.VulnerabilityStatus.VERIFIED },
                    { from: vulnerability_entity_1.VulnerabilityStatus.VERIFIED, to: vulnerability_entity_1.VulnerabilityStatus.CLOSED },
                ];
                progressionCases.forEach(({ from, to }) => {
                    const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                        ...baseEventData,
                        oldStatus: from,
                        newStatus: to,
                    });
                    expect(event.isStatusProgression()).toBe(true);
                });
            });
            (0, node_test_1.it)('should not identify regression as progression', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.TRIAGED,
                });
                expect(event.isStatusProgression()).toBe(false);
            });
            (0, node_test_1.it)('should not identify dismissal as progression', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE,
                });
                expect(event.isStatusProgression()).toBe(false);
            });
        });
        (0, node_test_2.describe)('isStatusRegression', () => {
            (0, node_test_1.it)('should identify backward movement', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.IN_PROGRESS,
                });
                expect(event.isStatusRegression()).toBe(true);
            });
            (0, node_test_1.it)('should not identify progression as regression', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.CONFIRMED,
                });
                expect(event.isStatusRegression()).toBe(false);
            });
            (0, node_test_1.it)('should not identify dismissal as regression', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE,
                });
                expect(event.isStatusRegression()).toBe(false);
            });
        });
        (0, node_test_2.describe)('wasVulnerabilityClosed', () => {
            (0, node_test_1.it)('should identify closure statuses', () => {
                const closureStatuses = [
                    vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
                    vulnerability_entity_1.VulnerabilityStatus.VERIFIED,
                    vulnerability_entity_1.VulnerabilityStatus.CLOSED,
                ];
                closureStatuses.forEach(status => {
                    const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                        ...baseEventData,
                        newStatus: status,
                    });
                    expect(event.wasVulnerabilityClosed()).toBe(true);
                });
            });
            (0, node_test_1.it)('should not identify non-closure statuses', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE,
                });
                expect(event.wasVulnerabilityClosed()).toBe(false);
            });
        });
        (0, node_test_2.describe)('wasVulnerabilityDismissed', () => {
            (0, node_test_1.it)('should identify dismissal statuses', () => {
                const dismissalStatuses = [
                    vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE,
                    vulnerability_entity_1.VulnerabilityStatus.ACCEPTED_RISK,
                ];
                dismissalStatuses.forEach(status => {
                    const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                        ...baseEventData,
                        newStatus: status,
                    });
                    expect(event.wasVulnerabilityDismissed()).toBe(true);
                });
            });
            (0, node_test_1.it)('should not identify non-dismissal statuses', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
                });
                expect(event.wasVulnerabilityDismissed()).toBe(false);
            });
        });
        (0, node_test_2.describe)('wasVulnerabilityReopened', () => {
            (0, node_test_1.it)('should identify reopening from closed status', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.CLOSED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                });
                expect(event.wasVulnerabilityReopened()).toBe(true);
            });
            (0, node_test_1.it)('should identify reopening from dismissed status', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.CONFIRMED,
                });
                expect(event.wasVulnerabilityReopened()).toBe(true);
            });
            (0, node_test_1.it)('should not identify normal progression as reopening', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.CONFIRMED,
                });
                expect(event.wasVulnerabilityReopened()).toBe(false);
            });
        });
        (0, node_test_2.describe)('getTransitionType', () => {
            (0, node_test_1.it)('should identify all transition types correctly', () => {
                const testCases = [
                    {
                        from: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                        to: vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
                        expected: 'closure',
                    },
                    {
                        from: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                        to: vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE,
                        expected: 'dismissal',
                    },
                    {
                        from: vulnerability_entity_1.VulnerabilityStatus.CLOSED,
                        to: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                        expected: 'reopening',
                    },
                    {
                        from: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                        to: vulnerability_entity_1.VulnerabilityStatus.CONFIRMED,
                        expected: 'progression',
                    },
                    {
                        from: vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
                        to: vulnerability_entity_1.VulnerabilityStatus.IN_PROGRESS,
                        expected: 'reopening',
                    },
                    {
                        from: vulnerability_entity_1.VulnerabilityStatus.TRIAGED,
                        to: vulnerability_entity_1.VulnerabilityStatus.TRIAGED,
                        expected: 'lateral',
                    },
                ];
                testCases.forEach(({ from, to, expected }) => {
                    const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                        ...baseEventData,
                        oldStatus: from,
                        newStatus: to,
                    });
                    expect(event.getTransitionType()).toBe(expected);
                });
            });
        });
    });
    (0, node_test_2.describe)('notification requirements', () => {
        (0, node_test_2.describe)('getNotificationPriority', () => {
            (0, node_test_1.it)('should assign correct priorities', () => {
                const testCases = [
                    { type: 'closure', expected: 'medium' },
                    { type: 'dismissal', expected: 'low' },
                    { type: 'reopening', expected: 'high' },
                    { type: 'regression', expected: 'high' },
                    { type: 'progression', expected: 'low' },
                ];
                testCases.forEach(({ type, expected }) => {
                    let from, to;
                    switch (type) {
                        case 'closure':
                            from = vulnerability_entity_1.VulnerabilityStatus.DISCOVERED;
                            to = vulnerability_entity_1.VulnerabilityStatus.REMEDIATED;
                            break;
                        case 'dismissal':
                            from = vulnerability_entity_1.VulnerabilityStatus.DISCOVERED;
                            to = vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE;
                            break;
                        case 'reopening':
                            from = vulnerability_entity_1.VulnerabilityStatus.CLOSED;
                            to = vulnerability_entity_1.VulnerabilityStatus.DISCOVERED;
                            break;
                        case 'regression':
                            from = vulnerability_entity_1.VulnerabilityStatus.REMEDIATED;
                            to = vulnerability_entity_1.VulnerabilityStatus.IN_PROGRESS;
                            break;
                        case 'progression':
                            from = vulnerability_entity_1.VulnerabilityStatus.DISCOVERED;
                            to = vulnerability_entity_1.VulnerabilityStatus.CONFIRMED;
                            break;
                        default:
                            from = vulnerability_entity_1.VulnerabilityStatus.DISCOVERED;
                            to = vulnerability_entity_1.VulnerabilityStatus.CONFIRMED;
                    }
                    const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                        ...baseEventData,
                        oldStatus: from,
                        newStatus: to,
                    });
                    expect(event.getNotificationPriority()).toBe(expected);
                });
            });
        });
        (0, node_test_2.describe)('getRequiredNotifications', () => {
            (0, node_test_1.it)('should provide appropriate notifications for closure', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
                });
                const notifications = event.getRequiredNotifications();
                expect(notifications.channels).toContain('email');
                expect(notifications.channels).toContain('webhook');
                expect(notifications.channels).toContain('dashboard');
                expect(notifications.recipients).toContain('vulnerability_team');
                expect(notifications.recipients).toContain('stakeholders');
                expect(notifications.urgency).toBe('within_day');
            });
            (0, node_test_1.it)('should provide appropriate notifications for reopening', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.CLOSED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                });
                const notifications = event.getRequiredNotifications();
                expect(notifications.channels).toContain('slack');
                expect(notifications.recipients).toContain('security_team');
                expect(notifications.recipients).toContain('management');
                expect(notifications.urgency).toBe('within_hour');
            });
        });
    });
    (0, node_test_2.describe)('workflow actions', () => {
        (0, node_test_2.describe)('getTriggeredWorkflowActions', () => {
            (0, node_test_1.it)('should trigger appropriate actions for each status', () => {
                const testCases = [
                    {
                        status: vulnerability_entity_1.VulnerabilityStatus.CONFIRMED,
                        expectedActions: ['assign_to_security_team', 'create_risk_assessment'],
                    },
                    {
                        status: vulnerability_entity_1.VulnerabilityStatus.TRIAGED,
                        expectedActions: ['assign_remediation_owner', 'set_remediation_timeline'],
                    },
                    {
                        status: vulnerability_entity_1.VulnerabilityStatus.IN_PROGRESS,
                        expectedActions: ['start_remediation_tracking', 'notify_stakeholders'],
                    },
                    {
                        status: vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
                        expectedActions: ['schedule_verification', 'update_asset_inventory'],
                    },
                    {
                        status: vulnerability_entity_1.VulnerabilityStatus.VERIFIED,
                        expectedActions: ['close_vulnerability', 'update_metrics'],
                    },
                    {
                        status: vulnerability_entity_1.VulnerabilityStatus.CLOSED,
                        expectedActions: ['archive_vulnerability', 'generate_closure_report'],
                    },
                    {
                        status: vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE,
                        expectedActions: ['update_scanner_rules', 'document_false_positive'],
                    },
                    {
                        status: vulnerability_entity_1.VulnerabilityStatus.ACCEPTED_RISK,
                        expectedActions: ['document_risk_acceptance', 'schedule_risk_review'],
                    },
                ];
                testCases.forEach(({ status, expectedActions }) => {
                    const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                        ...baseEventData,
                        newStatus: status,
                    });
                    const actions = event.getTriggeredWorkflowActions();
                    expectedActions.forEach(action => {
                        expect(actions).toContain(action);
                    });
                });
            });
            (0, node_test_1.it)('should include reopening actions when vulnerability is reopened', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.CLOSED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                });
                const actions = event.getTriggeredWorkflowActions();
                expect(actions).toContain('investigate_reopening_cause');
                expect(actions).toContain('reassess_vulnerability');
            });
        });
    });
    (0, node_test_2.describe)('metrics', () => {
        (0, node_test_2.describe)('getMetricsToUpdate', () => {
            (0, node_test_1.it)('should include basic transition metrics', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, baseEventData);
                const metrics = event.getMetricsToUpdate();
                const transitionMetric = metrics.find(m => m.metric === 'vulnerability_status_transitions');
                expect(transitionMetric).toBeDefined();
                expect(transitionMetric?.operation).toBe('increment');
                expect(transitionMetric?.tags).toEqual({
                    old_status: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                    new_status: vulnerability_entity_1.VulnerabilityStatus.CONFIRMED,
                    transition_type: 'progression',
                });
            });
            (0, node_test_1.it)('should include closure metrics', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
                });
                const metrics = event.getMetricsToUpdate();
                const closureMetric = metrics.find(m => m.metric === 'vulnerabilities_closed');
                const timingMetric = metrics.find(m => m.metric === 'vulnerability_closure_time');
                expect(closureMetric).toBeDefined();
                expect(closureMetric?.operation).toBe('increment');
                expect(timingMetric).toBeDefined();
                expect(timingMetric?.operation).toBe('timing');
            });
            (0, node_test_1.it)('should include dismissal metrics', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE,
                });
                const metrics = event.getMetricsToUpdate();
                const dismissalMetric = metrics.find(m => m.metric === 'vulnerabilities_dismissed');
                expect(dismissalMetric).toBeDefined();
                expect(dismissalMetric?.operation).toBe('increment');
                expect(dismissalMetric?.tags).toEqual({ reason: vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE });
            });
            (0, node_test_1.it)('should include reopening metrics', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.CLOSED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                });
                const metrics = event.getMetricsToUpdate();
                const reopeningMetric = metrics.find(m => m.metric === 'vulnerabilities_reopened');
                expect(reopeningMetric).toBeDefined();
                expect(reopeningMetric?.operation).toBe('increment');
                expect(reopeningMetric?.tags).toEqual({ from_status: vulnerability_entity_1.VulnerabilityStatus.CLOSED });
            });
        });
    });
    (0, node_test_2.describe)('compliance reporting', () => {
        (0, node_test_2.describe)('getComplianceReportingRequirements', () => {
            (0, node_test_1.it)('should require reporting for closure', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
                });
                const requirements = event.getComplianceReportingRequirements();
                expect(requirements.required).toBe(true);
                expect(requirements.frameworks).toContain('SOX');
                expect(requirements.frameworks).toContain('PCI_DSS');
                expect(requirements.deadline).toBe('within_week');
                expect(requirements.reportType).toBe('closure');
            });
            (0, node_test_1.it)('should require reporting for dismissal', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE,
                });
                const requirements = event.getComplianceReportingRequirements();
                expect(requirements.required).toBe(true);
                expect(requirements.reportType).toBe('exception');
            });
            (0, node_test_1.it)('should require immediate reporting for reopening', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, {
                    ...baseEventData,
                    oldStatus: vulnerability_entity_1.VulnerabilityStatus.CLOSED,
                    newStatus: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
                });
                const requirements = event.getComplianceReportingRequirements();
                expect(requirements.required).toBe(true);
                expect(requirements.deadline).toBe('within_24h');
                expect(requirements.reportType).toBe('exception');
            });
            (0, node_test_1.it)('should not require reporting for normal progression', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, baseEventData);
                const requirements = event.getComplianceReportingRequirements();
                expect(requirements.required).toBe(false);
                expect(requirements.reportType).toBe('none');
            });
        });
    });
    (0, node_test_2.describe)('serialization', () => {
        (0, node_test_2.describe)('toJSON', () => {
            (0, node_test_1.it)('should serialize event with all analysis data', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, baseEventData);
                const json = event.toJSON();
                expect(json.eventType).toBe('VulnerabilityStatusChanged');
                expect(json.data.vulnerabilityId).toBe('vuln-123');
                expect(json.data.oldStatus).toBe(vulnerability_entity_1.VulnerabilityStatus.DISCOVERED);
                expect(json.data.newStatus).toBe(vulnerability_entity_1.VulnerabilityStatus.CONFIRMED);
                expect(json.analysis).toBeDefined();
                expect(json.analysis.transitionType).toBe('progression');
                expect(json.analysis.isProgression).toBe(true);
                expect(json.analysis.notificationPriority).toBe('low');
                expect(json.analysis.triggeredActions).toContain('assign_to_security_team');
            });
        });
        (0, node_test_2.describe)('fromJSON', () => {
            (0, node_test_1.it)('should deserialize event correctly', () => {
                const originalEvent = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, baseEventData);
                const json = originalEvent.toJSON();
                const deserializedEvent = vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent.fromJSON(json);
                expect(deserializedEvent.vulnerabilityId).toBe(originalEvent.vulnerabilityId);
                expect(deserializedEvent.oldStatus).toBe(originalEvent.oldStatus);
                expect(deserializedEvent.newStatus).toBe(originalEvent.newStatus);
                expect(deserializedEvent.reason).toBe(originalEvent.reason);
                expect(deserializedEvent.aggregateId.toString()).toBe(originalEvent.aggregateId.toString());
            });
        });
    });
    (0, node_test_2.describe)('descriptions and summaries', () => {
        (0, node_test_2.describe)('getDescription', () => {
            (0, node_test_1.it)('should provide human-readable description', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, baseEventData);
                const description = event.getDescription();
                expect(description).toContain('discovered');
                expect(description).toContain('confirmed');
                expect(description).toContain('Confirmed by security team');
            });
        });
        (0, node_test_2.describe)('getEventSummary', () => {
            (0, node_test_1.it)('should provide structured summary', () => {
                const event = new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(aggregateId, baseEventData);
                const summary = event.getEventSummary();
                expect(summary.eventType).toBe('VulnerabilityStatusChanged');
                expect(summary.vulnerabilityId).toBe('vuln-123');
                expect(summary.statusTransition).toBe('discovered -> confirmed');
                expect(summary.transitionType).toBe('progression');
                expect(summary.reason).toBe('Confirmed by security team');
                expect(summary.timestamp).toBeDefined();
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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