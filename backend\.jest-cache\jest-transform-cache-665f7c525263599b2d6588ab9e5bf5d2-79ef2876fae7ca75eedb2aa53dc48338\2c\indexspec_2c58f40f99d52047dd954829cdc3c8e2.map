{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\index.spec.ts", "mappings": ";;AAAA,iDAqB0B;AAE1B,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,4BAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,CAAC,gCAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,8BAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,kCAAqB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,+BAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,8BAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,+BAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,wCAA2B,CAAC,CAAC,WAAW,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,gCAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,gCAAmB,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YAC/E,MAAM,CAAC,gCAAmB,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YACpE,MAAM,CAAC,gCAAmB,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACxE,MAAM,CAAC,gCAAmB,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;YACxE,MAAM,CAAC,gCAAmB,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YACpE,MAAM,CAAC,gCAAmB,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,8BAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,8BAAiB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,8BAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,8BAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,CAAC,8BAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;gBAClD,MAAM,SAAS,GAAG,IAAI,gCAAmB,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;gBAC5D,MAAM,CAAC,IAAA,8BAAiB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;gBAChD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;gBACzC,MAAM,CAAC,IAAA,8BAAiB,EAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;gBAClD,MAAM,CAAC,IAAA,8BAAiB,EAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAChD,MAAM,CAAC,IAAA,8BAAiB,EAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5C,MAAM,CAAC,IAAA,8BAAiB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjD,MAAM,CAAC,IAAA,8BAAiB,EAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,SAAS,GAAG,gCAAmB,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC;gBAChG,MAAM,CAAC,IAAA,kCAAqB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,SAAS,GAAG,8BAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBAC/D,MAAM,CAAC,IAAA,kCAAqB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;gBAChD,MAAM,CAAC,IAAA,kCAAqB,EAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpD,MAAM,CAAC,IAAA,kCAAqB,EAAC,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;YACnC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,SAAS,GAAG,8BAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBAC/D,MAAM,CAAC,IAAA,gCAAmB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,SAAS,GAAG,gCAAmB,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC;gBAChG,MAAM,CAAC,IAAA,gCAAmB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;YACxC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;gBACxD,MAAM,SAAS,GAAG,kCAAqB,CAAC,qBAAqB,EAAE,CAAC;gBAChE,MAAM,CAAC,IAAA,qCAAwB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,SAAS,GAAG,+BAAkB,CAAC,uBAAuB,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;gBAChG,MAAM,CAAC,IAAA,qCAAwB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,SAAS,GAAG,8BAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBAC/D,MAAM,CAAC,IAAA,qCAAwB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;YACnC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,SAAS,GAAG,8BAAiB,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;gBAC3F,MAAM,CAAC,IAAA,gCAAmB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,SAAS,GAAG,8BAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBAC/D,MAAM,CAAC,IAAA,gCAAmB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,SAAS,GAAG,+BAAkB,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;gBAC3E,MAAM,CAAC,IAAA,iCAAoB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,SAAS,GAAG,8BAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBAC/D,MAAM,CAAC,IAAA,iCAAoB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;YAC7C,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;gBAC/D,MAAM,SAAS,GAAG,wCAA2B,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACtE,MAAM,CAAC,IAAA,0CAA6B,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,SAAS,GAAG,8BAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBAC/D,MAAM,CAAC,IAAA,0CAA6B,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;gBAC1E,MAAM,mBAAmB,GAAG,gCAAmB,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;gBACjG,MAAM,qBAAqB,GAAG,kCAAqB,CAAC,qBAAqB,EAAE,CAAC;gBAC5E,MAAM,kBAAkB,GAAG,+BAAkB,CAAC,uBAAuB,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;gBACzG,MAAM,iBAAiB,GAAG,8BAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBACvE,MAAM,iBAAiB,GAAG,8BAAiB,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;gBACnG,MAAM,kBAAkB,GAAG,+BAAkB,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;gBACpF,MAAM,2BAA2B,GAAG,wCAA2B,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAExF,MAAM,CAAC,IAAA,8BAAiB,EAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACzD,MAAM,CAAC,IAAA,8BAAiB,EAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC3D,MAAM,CAAC,IAAA,8BAAiB,EAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACxD,MAAM,CAAC,IAAA,8BAAiB,EAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACvD,MAAM,CAAC,IAAA,8BAAiB,EAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACvD,MAAM,CAAC,IAAA,8BAAiB,EAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACxD,MAAM,CAAC,IAAA,8BAAiB,EAAC,2BAA2B,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,qEAAqE;gBACrE,MAAM,qBAAsB,SAAQ,4BAAe;oBACjD;wBACE,KAAK,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;oBACxC,CAAC;iBACF;gBAED,MAAM,eAAe,GAAG,IAAI,qBAAqB,EAAE,CAAC;gBACpD,MAAM,CAAC,IAAA,8BAAiB,EAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;gBAClE,MAAM,SAAS,GAAG,gCAAmB,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,sBAAsB,CAAC,CAAC;gBACnG,MAAM,QAAQ,GAAG,IAAA,+BAAkB,EAAC,SAAS,CAAC,CAAC;gBAE/C,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACzC,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;gBAC5D,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uEAAuE,EAAE,GAAG,EAAE;gBAC/E,+DAA+D;gBAC/D,MAAM,oBAAqB,SAAQ,4BAAe;oBAChD;wBACE,KAAK,CAAC,aAAa,EAAE,aAAa,EAAE;4BAClC,QAAQ,EAAE,KAAK;4BACf,QAAQ,EAAE,MAAM;yBACjB,CAAC,CAAC;oBACL,CAAC;iBACF;gBAED,MAAM,SAAS,GAAG,IAAI,oBAAoB,EAAE,CAAC;gBAC7C,MAAM,QAAQ,GAAG,IAAA,+BAAkB,EAAC,SAAS,CAAC,CAAC;gBAE/C,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;oBACvB,KAAK,EAAE,SAAS,CAAC,cAAc,EAAE;oBACjC,IAAI,EAAE,aAAa;oBACnB,OAAO,EAAE;wBACP,QAAQ,EAAE,MAAM;wBAChB,QAAQ,EAAE,KAAK;wBACf,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE;wBAC5C,aAAa,EAAE,SAAS,CAAC,aAAa;qBACvC;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;YAC3C,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;gBACtE,MAAM,SAAS,GAAG,IAAI,gCAAmB,CAAC,YAAY,EAAE,EAAE,EAAE;oBAC1D,OAAO,EAAE;wBACP,QAAQ,EAAE,WAAW;wBACrB,KAAK,EAAE,WAAW;wBAClB,KAAK,EAAE,kBAAkB;wBACzB,MAAM,EAAE,UAAU;qBACnB;iBACF,CAAC,CAAC;gBAEH,MAAM,SAAS,GAAG,IAAA,wCAA2B,EAAC,SAAS,CAAC,CAAC;gBAEzD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACtD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACnD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACzD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;gBAC1C,MAAM,SAAS,GAAG,IAAI,gCAAmB,CAAC,YAAY,EAAE,EAAE,EAAE;oBAC1D,OAAO,EAAE;wBACP,MAAM,EAAE,aAAa;wBACrB,KAAK,EAAE,kBAAkB;wBACzB,YAAY,EAAE,cAAc;qBAC7B;iBACF,CAAC,CAAC;gBAEH,MAAM,SAAS,GAAG,IAAA,wCAA2B,EAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC;gBAErF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACpD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC1D,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,SAAS,GAAG,IAAI,gCAAmB,CAAC,YAAY,EAAE,EAAE,EAAE;oBAC1D,OAAO,EAAE;wBACP,MAAM,EAAE,UAAU;wBAClB,SAAS,EAAE,QAAQ;qBACpB;iBACF,CAAC,CAAC;gBAEH,MAAM,SAAS,GAAG,IAAA,wCAA2B,EAAC,SAAS,CAAC,CAAC;gBAEzD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,UAAU,GAAG;gBACjB,gCAAmB,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;gBACpE,8BAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC;gBAC5C,kCAAqB,CAAC,qBAAqB,EAAE;gBAC7C,+BAAkB,CAAC,uBAAuB,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,aAAa,CAAC,CAAC;gBAC7E,8BAAiB,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,kBAAkB,CAAC;gBACxE,+BAAkB,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI,EAAE,CAAC;gBACxD,wCAA2B,CAAC,QAAQ,CAAC,aAAa,CAAC;aACpD,CAAC;YAEF,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBAC7B,MAAM,CAAC,IAAA,8BAAiB,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChD,MAAM,CAAC,IAAA,8BAAiB,EAAC,SAAS,CAAC,CAAC,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;gBACjE,MAAM,CAAC,IAAA,8BAAiB,EAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;gBAEvD,MAAM,WAAW,GAAG,IAAA,+BAAkB,EAAC,SAAS,CAAC,CAAC;gBAClD,MAAM,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBAC5C,MAAM,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAE3C,MAAM,SAAS,GAAG,IAAA,wCAA2B,EAAC,SAAS,CAAC,CAAC;gBACzD,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACzC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;gBAC5C,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\index.spec.ts"], "sourcesContent": ["import {\r\n  DomainException,\r\n  ValidationException,\r\n  NotFoundException,\r\n  UnauthorizedException,\r\n  ForbiddenException,\r\n  ConflictException,\r\n  RateLimitException,\r\n  ServiceUnavailableException,\r\n  ExceptionCategories,\r\n  ExceptionSeverity,\r\n  isDomainException,\r\n  isValidationException,\r\n  isNotFoundException,\r\n  isAuthorizationException,\r\n  isConflictException,\r\n  isRateLimitException,\r\n  isServiceUnavailableException,\r\n  getHttpStatusCode,\r\n  toApiErrorResponse,\r\n  sanitizeExceptionForLogging,\r\n} from '../../exceptions';\r\n\r\ndescribe('Exception Index', () => {\r\n  describe('exports', () => {\r\n    it('should export all exception classes', () => {\r\n      expect(DomainException).toBeDefined();\r\n      expect(ValidationException).toBeDefined();\r\n      expect(NotFoundException).toBeDefined();\r\n      expect(UnauthorizedException).toBeDefined();\r\n      expect(ForbiddenException).toBeDefined();\r\n      expect(ConflictException).toBeDefined();\r\n      expect(RateLimitException).toBeDefined();\r\n      expect(ServiceUnavailableException).toBeDefined();\r\n    });\r\n\r\n    it('should export exception categories', () => {\r\n      expect(ExceptionCategories).toBeDefined();\r\n      expect(ExceptionCategories.DOMAIN).toContain('BusinessRuleViolationException');\r\n      expect(ExceptionCategories.RESOURCE).toContain('NotFoundException');\r\n      expect(ExceptionCategories.VALIDATION).toContain('ValidationException');\r\n      expect(ExceptionCategories.SECURITY).toContain('UnauthorizedException');\r\n      expect(ExceptionCategories.CONFLICT).toContain('ConflictException');\r\n      expect(ExceptionCategories.AVAILABILITY).toContain('RateLimitException');\r\n    });\r\n\r\n    it('should export exception severity levels', () => {\r\n      expect(ExceptionSeverity).toBeDefined();\r\n      expect(ExceptionSeverity.LOW).toBe('low');\r\n      expect(ExceptionSeverity.MEDIUM).toBe('medium');\r\n      expect(ExceptionSeverity.HIGH).toBe('high');\r\n      expect(ExceptionSeverity.CRITICAL).toBe('critical');\r\n    });\r\n  });\r\n\r\n  describe('helper functions', () => {\r\n    describe('isDomainException', () => {\r\n      it('should return true for domain exceptions', () => {\r\n        const exception = new ValidationException('Test error', []);\r\n        expect(isDomainException(exception)).toBe(true);\r\n      });\r\n\r\n      it('should return false for regular errors', () => {\r\n        const error = new Error('Regular error');\r\n        expect(isDomainException(error)).toBe(false);\r\n      });\r\n\r\n      it('should return false for non-error values', () => {\r\n        expect(isDomainException('string')).toBe(false);\r\n        expect(isDomainException(null)).toBe(false);\r\n        expect(isDomainException(undefined)).toBe(false);\r\n        expect(isDomainException({})).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isValidationException', () => {\r\n      it('should return true for validation exceptions', () => {\r\n        const exception = ValidationException.single('field', 'value', 'required', 'Field is required');\r\n        expect(isValidationException(exception)).toBe(true);\r\n      });\r\n\r\n      it('should return false for other domain exceptions', () => {\r\n        const exception = NotFoundException.entity('User', 'user-123');\r\n        expect(isValidationException(exception)).toBe(false);\r\n      });\r\n\r\n      it('should return false for non-exceptions', () => {\r\n        expect(isValidationException('string')).toBe(false);\r\n        expect(isValidationException(new Error())).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isNotFoundException', () => {\r\n      it('should return true for not found exceptions', () => {\r\n        const exception = NotFoundException.entity('User', 'user-123');\r\n        expect(isNotFoundException(exception)).toBe(true);\r\n      });\r\n\r\n      it('should return false for other domain exceptions', () => {\r\n        const exception = ValidationException.single('field', 'value', 'required', 'Field is required');\r\n        expect(isNotFoundException(exception)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isAuthorizationException', () => {\r\n      it('should return true for unauthorized exceptions', () => {\r\n        const exception = UnauthorizedException.missingAuthentication();\r\n        expect(isAuthorizationException(exception)).toBe(true);\r\n      });\r\n\r\n      it('should return true for forbidden exceptions', () => {\r\n        const exception = ForbiddenException.insufficientPermissions('User', 'delete', ['user:delete']);\r\n        expect(isAuthorizationException(exception)).toBe(true);\r\n      });\r\n\r\n      it('should return false for other domain exceptions', () => {\r\n        const exception = NotFoundException.entity('User', 'user-123');\r\n        expect(isAuthorizationException(exception)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isConflictException', () => {\r\n      it('should return true for conflict exceptions', () => {\r\n        const exception = ConflictException.duplicateResource('User', 'email', '<EMAIL>');\r\n        expect(isConflictException(exception)).toBe(true);\r\n      });\r\n\r\n      it('should return false for other domain exceptions', () => {\r\n        const exception = NotFoundException.entity('User', 'user-123');\r\n        expect(isConflictException(exception)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isRateLimitException', () => {\r\n      it('should return true for rate limit exceptions', () => {\r\n        const exception = RateLimitException.apiRequestLimit(100, 105, new Date());\r\n        expect(isRateLimitException(exception)).toBe(true);\r\n      });\r\n\r\n      it('should return false for other domain exceptions', () => {\r\n        const exception = NotFoundException.entity('User', 'user-123');\r\n        expect(isRateLimitException(exception)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isServiceUnavailableException', () => {\r\n      it('should return true for service unavailable exceptions', () => {\r\n        const exception = ServiceUnavailableException.overload('UserService');\r\n        expect(isServiceUnavailableException(exception)).toBe(true);\r\n      });\r\n\r\n      it('should return false for other domain exceptions', () => {\r\n        const exception = NotFoundException.entity('User', 'user-123');\r\n        expect(isServiceUnavailableException(exception)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('getHttpStatusCode', () => {\r\n      it('should return correct status codes for different exception types', () => {\r\n        const validationException = ValidationException.single('field', 'value', 'required', 'Required');\r\n        const unauthorizedException = UnauthorizedException.missingAuthentication();\r\n        const forbiddenException = ForbiddenException.insufficientPermissions('User', 'delete', ['user:delete']);\r\n        const notFoundException = NotFoundException.entity('User', 'user-123');\r\n        const conflictException = ConflictException.duplicateResource('User', 'email', '<EMAIL>');\r\n        const rateLimitException = RateLimitException.apiRequestLimit(100, 105, new Date());\r\n        const serviceUnavailableException = ServiceUnavailableException.overload('UserService');\r\n\r\n        expect(getHttpStatusCode(validationException)).toBe(400);\r\n        expect(getHttpStatusCode(unauthorizedException)).toBe(401);\r\n        expect(getHttpStatusCode(forbiddenException)).toBe(403);\r\n        expect(getHttpStatusCode(notFoundException)).toBe(404);\r\n        expect(getHttpStatusCode(conflictException)).toBe(409);\r\n        expect(getHttpStatusCode(rateLimitException)).toBe(429);\r\n        expect(getHttpStatusCode(serviceUnavailableException)).toBe(503);\r\n      });\r\n\r\n      it('should return 400 for unknown domain exceptions', () => {\r\n        // Create a custom domain exception that doesn't match specific types\r\n        class CustomDomainException extends DomainException {\r\n          constructor() {\r\n            super('Custom error', 'CUSTOM_ERROR');\r\n          }\r\n        }\r\n\r\n        const customException = new CustomDomainException();\r\n        expect(getHttpStatusCode(customException)).toBe(400);\r\n      });\r\n    });\r\n\r\n    describe('toApiErrorResponse', () => {\r\n      it('should use exception toApiResponse method when available', () => {\r\n        const exception = ValidationException.single('email', 'invalid', 'format', 'Invalid email format');\r\n        const response = toApiErrorResponse(exception);\r\n\r\n        expect(response).toHaveProperty('error');\r\n        expect(response).toHaveProperty('code', 'VALIDATION_ERROR');\r\n        expect(response).toHaveProperty('details');\r\n        expect(response.details).toHaveProperty('field', 'email');\r\n      });\r\n\r\n      it('should provide fallback response for exceptions without toApiResponse', () => {\r\n        // Create a basic domain exception without toApiResponse method\r\n        class BasicDomainException extends DomainException {\r\n          constructor() {\r\n            super('Basic error', 'BASIC_ERROR', {\r\n              severity: 'low',\r\n              category: 'test',\r\n            });\r\n          }\r\n        }\r\n\r\n        const exception = new BasicDomainException();\r\n        const response = toApiErrorResponse(exception);\r\n\r\n        expect(response).toEqual({\r\n          error: exception.getUserMessage(),\r\n          code: 'BASIC_ERROR',\r\n          details: {\r\n            category: 'test',\r\n            severity: 'low',\r\n            timestamp: exception.timestamp.toISOString(),\r\n            correlationId: exception.correlationId,\r\n          },\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('sanitizeExceptionForLogging', () => {\r\n      it('should sanitize sensitive information from exception context', () => {\r\n        const exception = new ValidationException('Test error', [], {\r\n          context: {\r\n            password: 'secret123',\r\n            token: 'jwt-token',\r\n            email: '<EMAIL>',\r\n            userId: 'user-123',\r\n          },\r\n        });\r\n\r\n        const sanitized = sanitizeExceptionForLogging(exception);\r\n\r\n        expect(sanitized.context.password).toBe('[REDACTED]');\r\n        expect(sanitized.context.token).toBe('[REDACTED]');\r\n        expect(sanitized.context.email).toBe('<EMAIL>');\r\n        expect(sanitized.context.userId).toBe('user-123');\r\n      });\r\n\r\n      it('should use custom sensitive keys', () => {\r\n        const exception = new ValidationException('Test error', [], {\r\n          context: {\r\n            apiKey: 'api-key-123',\r\n            email: '<EMAIL>',\r\n            customSecret: 'secret-value',\r\n          },\r\n        });\r\n\r\n        const sanitized = sanitizeExceptionForLogging(exception, ['apiKey', 'customSecret']);\r\n\r\n        expect(sanitized.context.apiKey).toBe('[REDACTED]');\r\n        expect(sanitized.context.customSecret).toBe('[REDACTED]');\r\n        expect(sanitized.context.email).toBe('<EMAIL>');\r\n      });\r\n\r\n      it('should handle exceptions without sensitive data', () => {\r\n        const exception = new ValidationException('Test error', [], {\r\n          context: {\r\n            userId: 'user-123',\r\n            operation: 'create',\r\n          },\r\n        });\r\n\r\n        const sanitized = sanitizeExceptionForLogging(exception);\r\n\r\n        expect(sanitized.context.userId).toBe('user-123');\r\n        expect(sanitized.context.operation).toBe('create');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('integration', () => {\r\n    it('should work with all exception types in a unified way', () => {\r\n      const exceptions = [\r\n        ValidationException.single('field', 'value', 'required', 'Required'),\r\n        NotFoundException.entity('User', 'user-123'),\r\n        UnauthorizedException.missingAuthentication(),\r\n        ForbiddenException.insufficientPermissions('User', 'delete', ['user:delete']),\r\n        ConflictException.duplicateResource('User', 'email', '<EMAIL>'),\r\n        RateLimitException.apiRequestLimit(100, 105, new Date()),\r\n        ServiceUnavailableException.overload('UserService'),\r\n      ];\r\n\r\n      exceptions.forEach(exception => {\r\n        expect(isDomainException(exception)).toBe(true);\r\n        expect(getHttpStatusCode(exception)).toBeGreaterThanOrEqual(400);\r\n        expect(getHttpStatusCode(exception)).toBeLessThan(600);\r\n\r\n        const apiResponse = toApiErrorResponse(exception);\r\n        expect(apiResponse).toHaveProperty('error');\r\n        expect(apiResponse).toHaveProperty('code');\r\n\r\n        const sanitized = sanitizeExceptionForLogging(exception);\r\n        expect(sanitized).toHaveProperty('name');\r\n        expect(sanitized).toHaveProperty('message');\r\n        expect(sanitized).toHaveProperty('code');\r\n      });\r\n    });\r\n  });\r\n});"], "version": 3}