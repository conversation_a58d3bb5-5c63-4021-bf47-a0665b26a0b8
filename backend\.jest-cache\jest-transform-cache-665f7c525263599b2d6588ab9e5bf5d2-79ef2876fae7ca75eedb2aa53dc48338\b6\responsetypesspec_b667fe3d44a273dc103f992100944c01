9fb6dd9bbda99e54ccf385732e26478b
"use strict";
/**
 * Response Types Tests
 */
Object.defineProperty(exports, "__esModule", { value: true });
const response_types_1 = require("../../types/response.types");
describe('ResponseUtils', () => {
    describe('success', () => {
        it('should create a success response', () => {
            const data = { id: 1, name: 'Test' };
            const response = response_types_1.ResponseUtils.success(data, 'Operation successful');
            expect(response.status).toBe(response_types_1.ResponseStatus.SUCCESS);
            expect(response.statusCode).toBe(response_types_1.HttpStatus.OK);
            expect(response.message).toBe('Operation successful');
            expect(response.data).toEqual(data);
            expect(response.timestamp).toBeDefined();
        });
        it('should create a success response with custom status code', () => {
            const data = { id: 1 };
            const response = response_types_1.ResponseUtils.success(data, 'Created', response_types_1.HttpStatus.CREATED);
            expect(response.statusCode).toBe(response_types_1.HttpStatus.CREATED);
        });
        it('should create a success response with metadata', () => {
            const data = { id: 1 };
            const meta = { version: '1.0' };
            const response = response_types_1.ResponseUtils.success(data, 'Success', response_types_1.HttpStatus.OK, meta);
            expect(response.meta).toEqual(meta);
        });
    });
    describe('error', () => {
        it('should create an error response', () => {
            const error = {
                code: 'TEST_ERROR',
                message: 'Test error occurred',
            };
            const response = response_types_1.ResponseUtils.error(error, 'Operation failed');
            expect(response.status).toBe(response_types_1.ResponseStatus.ERROR);
            expect(response.statusCode).toBe(response_types_1.HttpStatus.INTERNAL_SERVER_ERROR);
            expect(response.message).toBe('Operation failed');
            expect(response.error).toEqual(error);
            expect(response.timestamp).toBeDefined();
        });
        it('should create an error response with additional errors', () => {
            const primaryError = {
                code: 'PRIMARY_ERROR',
                message: 'Primary error',
            };
            const additionalErrors = [
                { code: 'SECONDARY_ERROR', message: 'Secondary error' },
            ];
            const response = response_types_1.ResponseUtils.error(primaryError, 'Multiple errors', response_types_1.HttpStatus.BAD_REQUEST, additionalErrors);
            expect(response.errors).toEqual(additionalErrors);
        });
    });
    describe('validationError', () => {
        it('should create a validation error response', () => {
            const validationErrors = [
                {
                    code: 'REQUIRED_FIELD',
                    message: 'Name is required',
                    field: 'name',
                },
                {
                    code: 'INVALID_EMAIL',
                    message: 'Invalid email format',
                    field: 'email',
                    value: 'invalid-email',
                },
            ];
            const response = response_types_1.ResponseUtils.validationError(validationErrors);
            expect(response.status).toBe(response_types_1.ResponseStatus.ERROR);
            expect(response.statusCode).toBe(response_types_1.HttpStatus.UNPROCESSABLE_ENTITY);
            expect(response.error.code).toBe('VALIDATION_ERROR');
            expect(response.errors).toEqual(validationErrors);
        });
    });
    describe('notFound', () => {
        it('should create a not found response', () => {
            const response = response_types_1.ResponseUtils.notFound('User', 123);
            expect(response.status).toBe(response_types_1.ResponseStatus.ERROR);
            expect(response.statusCode).toBe(response_types_1.HttpStatus.NOT_FOUND);
            expect(response.error.code).toBe('NOT_FOUND');
            expect(response.error.message).toBe("User with identifier '123' not found");
            expect(response.error.context).toEqual({ resource: 'User', identifier: 123 });
        });
        it('should create a not found response without identifier', () => {
            const response = response_types_1.ResponseUtils.notFound('User');
            expect(response.error.message).toBe('User not found');
        });
    });
    describe('unauthorized', () => {
        it('should create an unauthorized response', () => {
            const response = response_types_1.ResponseUtils.unauthorized();
            expect(response.status).toBe(response_types_1.ResponseStatus.ERROR);
            expect(response.statusCode).toBe(response_types_1.HttpStatus.UNAUTHORIZED);
            expect(response.error.code).toBe('UNAUTHORIZED');
            expect(response.error.message).toBe('Authentication required');
        });
        it('should create an unauthorized response with custom message', () => {
            const response = response_types_1.ResponseUtils.unauthorized('Invalid token');
            expect(response.error.message).toBe('Invalid token');
        });
    });
    describe('forbidden', () => {
        it('should create a forbidden response', () => {
            const response = response_types_1.ResponseUtils.forbidden();
            expect(response.status).toBe(response_types_1.ResponseStatus.ERROR);
            expect(response.statusCode).toBe(response_types_1.HttpStatus.FORBIDDEN);
            expect(response.error.code).toBe('FORBIDDEN');
            expect(response.error.message).toBe('Access denied');
        });
    });
    describe('conflict', () => {
        it('should create a conflict response', () => {
            const context = { existingId: 123 };
            const response = response_types_1.ResponseUtils.conflict('Resource already exists', context);
            expect(response.status).toBe(response_types_1.ResponseStatus.ERROR);
            expect(response.statusCode).toBe(response_types_1.HttpStatus.CONFLICT);
            expect(response.error.code).toBe('CONFLICT');
            expect(response.error.context).toEqual(context);
        });
    });
    describe('rateLimited', () => {
        it('should create a rate limited response', () => {
            const response = response_types_1.ResponseUtils.rateLimited(60);
            expect(response.status).toBe(response_types_1.ResponseStatus.ERROR);
            expect(response.statusCode).toBe(response_types_1.HttpStatus.TOO_MANY_REQUESTS);
            expect(response.error.code).toBe('RATE_LIMITED');
            expect(response.error.context).toEqual({ retryAfter: 60 });
        });
    });
    describe('paginated', () => {
        it('should create a paginated response', () => {
            const items = [{ id: 1 }, { id: 2 }];
            const pagination = {
                currentPage: 1,
                itemsPerPage: 10,
                totalItems: 25,
                totalPages: 3,
                hasNextPage: true,
                hasPreviousPage: false,
                nextPage: 2,
                previousPage: null,
                firstItemIndex: 1,
                lastItemIndex: 10,
            };
            const response = response_types_1.ResponseUtils.paginated(items, pagination, '/api/users');
            expect(response.status).toBe(response_types_1.ResponseStatus.SUCCESS);
            expect(response.data).toEqual(items);
            expect(response.pagination).toEqual(pagination);
            expect(response.links.first).toBe('/api/users?page=1&limit=10');
            expect(response.links.next).toBe('/api/users?page=2&limit=10');
            expect(response.links.self).toBe('/api/users?page=1&limit=10');
        });
    });
    describe('bulkOperation', () => {
        it('should create a bulk operation response', () => {
            const results = [
                { id: 1, status: 'success', data: { name: 'User 1' } },
                { id: 2, status: 'error', error: { code: 'VALIDATION_ERROR', message: 'Invalid data' } },
                { id: 3, status: 'success', data: { name: 'User 3' } },
            ];
            const response = response_types_1.ResponseUtils.bulkOperation(results);
            expect(response.total).toBe(3);
            expect(response.successful).toBe(2);
            expect(response.failed).toBe(1);
            expect(response.results).toEqual(results);
            expect(response.errorSummary).toEqual({ VALIDATION_ERROR: 1 });
            expect(response.status).toBe(response_types_1.ResponseStatus.WARNING);
        });
        it('should create a successful bulk operation response', () => {
            const results = [
                { id: 1, status: 'success', data: { name: 'User 1' } },
                { id: 2, status: 'success', data: { name: 'User 2' } },
            ];
            const response = response_types_1.ResponseUtils.bulkOperation(results);
            expect(response.status).toBe(response_types_1.ResponseStatus.SUCCESS);
            expect(response.statusCode).toBe(response_types_1.HttpStatus.OK);
            expect(response.failed).toBe(0);
        });
    });
    describe('healthCheck', () => {
        it('should create a healthy system response', () => {
            const checks = {
                database: {
                    status: 'healthy',
                    responseTime: 50,
                },
                redis: {
                    status: 'healthy',
                    responseTime: 10,
                },
            };
            const systemInfo = {
                uptime: 3600,
                memory: { used: 512, total: 1024 },
                cpu: { usage: 25 },
            };
            const response = response_types_1.ResponseUtils.healthCheck(checks, systemInfo);
            expect(response.healthy).toBe(true);
            expect(response.status).toBe(response_types_1.ResponseStatus.SUCCESS);
            expect(response.statusCode).toBe(response_types_1.HttpStatus.OK);
            expect(response.checks.database.lastChecked).toBeDefined();
            expect(response.system.memory.percentage).toBe(50);
        });
        it('should create an unhealthy system response', () => {
            const checks = {
                database: {
                    status: 'unhealthy',
                    message: 'Connection failed',
                },
            };
            const systemInfo = {
                uptime: 3600,
                memory: { used: 512, total: 1024 },
                cpu: { usage: 25 },
            };
            const response = response_types_1.ResponseUtils.healthCheck(checks, systemInfo);
            expect(response.healthy).toBe(false);
            expect(response.status).toBe(response_types_1.ResponseStatus.ERROR);
            expect(response.statusCode).toBe(response_types_1.HttpStatus.SERVICE_UNAVAILABLE);
        });
    });
    describe('fileUpload', () => {
        it('should create a file upload response', () => {
            const fileInfo = {
                originalName: 'document.pdf',
                filename: 'abc123.pdf',
                size: 1024,
                mimeType: 'application/pdf',
                url: '/uploads/abc123.pdf',
                hash: 'sha256hash',
            };
            const metadata = { uploadedBy: 'user123' };
            const response = response_types_1.ResponseUtils.fileUpload(fileInfo, metadata);
            expect(response.status).toBe(response_types_1.ResponseStatus.SUCCESS);
            expect(response.statusCode).toBe(response_types_1.HttpStatus.CREATED);
            expect(response.data.file).toEqual(fileInfo);
            expect(response.data.metadata).toEqual(metadata);
        });
    });
    describe('utility methods', () => {
        it('should check if response is successful', () => {
            const successResponse = response_types_1.ResponseUtils.success({ id: 1 });
            const errorResponse = response_types_1.ResponseUtils.error({ code: 'ERROR', message: 'Error' });
            expect(response_types_1.ResponseUtils.isSuccess(successResponse)).toBe(true);
            expect(response_types_1.ResponseUtils.isSuccess(errorResponse)).toBe(false);
        });
        it('should check if response is an error', () => {
            const successResponse = response_types_1.ResponseUtils.success({ id: 1 });
            const errorResponse = response_types_1.ResponseUtils.error({ code: 'ERROR', message: 'Error' });
            expect(response_types_1.ResponseUtils.isError(successResponse)).toBe(false);
            expect(response_types_1.ResponseUtils.isError(errorResponse)).toBe(true);
        });
        it('should extract error message', () => {
            const errorResponse = response_types_1.ResponseUtils.error({ code: 'ERROR', message: 'Primary error' }, 'Operation failed');
            const message = response_types_1.ResponseUtils.getErrorMessage(errorResponse);
            expect(message).toBe('Primary error');
        });
        it('should extract all error messages', () => {
            const errorResponse = response_types_1.ResponseUtils.validationError([
                { code: 'REQUIRED', message: 'Name required', field: 'name' },
                { code: 'INVALID', message: 'Email invalid', field: 'email' },
            ]);
            const messages = response_types_1.ResponseUtils.getAllErrorMessages(errorResponse);
            expect(messages).toContain('One or more validation errors occurred');
            expect(messages).toContain('Name required');
            expect(messages).toContain('Email invalid');
        });
        it('should add request ID to response', () => {
            const response = response_types_1.ResponseUtils.success({ id: 1 });
            const withRequestId = response_types_1.ResponseUtils.withRequestId(response, 'req-123');
            expect(withRequestId.requestId).toBe('req-123');
        });
        it('should add version to response', () => {
            const response = response_types_1.ResponseUtils.success({ id: 1 });
            const withVersion = response_types_1.ResponseUtils.withVersion(response, 'v1.0');
            expect(withVersion.version).toBe('v1.0');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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