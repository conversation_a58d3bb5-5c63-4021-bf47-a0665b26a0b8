faf258feebf19707a81b0c61d1bfb8e4
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const common_1 = require("@nestjs/common");
const security_orchestrator_service_1 = require("../security-orchestrator.service");
const event_repository_1 = require("../../../domain/repositories/event.repository");
const threat_repository_1 = require("../../../domain/repositories/threat.repository");
const vulnerability_repository_1 = require("../../../domain/repositories/vulnerability.repository");
const response_action_repository_1 = require("../../../domain/repositories/response-action.repository");
const event_processor_interface_1 = require("../../../domain/interfaces/services/event-processor.interface");
const threat_detector_interface_1 = require("../../../domain/interfaces/services/threat-detector.interface");
const vulnerability_scanner_interface_1 = require("../../../domain/interfaces/services/vulnerability-scanner.interface");
const response_executor_interface_1 = require("../../../domain/interfaces/services/response-executor.interface");
const event_severity_enum_1 = require("../../../domain/enums/event-severity.enum");
const event_type_enum_1 = require("../../../domain/enums/event-type.enum");
const event_status_enum_1 = require("../../../domain/enums/event-status.enum");
const threat_severity_enum_1 = require("../../../domain/enums/threat-severity.enum");
const vulnerability_severity_enum_1 = require("../../../domain/enums/vulnerability-severity.enum");
const action_type_enum_1 = require("../../../domain/enums/action-type.enum");
const action_status_enum_1 = require("../../../domain/enums/action-status.enum");
const unique_entity_id_value_object_1 = require("../../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const node_test_1 = require("node:test");
const node_test_2 = require("node:test");
const node_test_3 = require("node:test");
const node_test_4 = require("node:test");
(0, node_test_2.describe)('SecurityOrchestratorService', () => {
    let service;
    let eventRepository;
    let threatRepository;
    let vulnerabilityRepository;
    let responseActionRepository;
    let eventProcessor;
    let threatDetector;
    let vulnerabilityScanner;
    let responseExecutor;
    // Test data factories
    const createMockEvent = (overrides = {}) => {
        const mockEvent = {
            id: unique_entity_id_value_object_1.UniqueEntityId.generate(),
            type: event_type_enum_1.EventType.SECURITY_ALERT,
            severity: event_severity_enum_1.EventSeverity.MEDIUM,
            status: event_status_enum_1.EventStatus.NEW,
            riskScore: 50,
            timestamp: new Date(),
            source: { toString: () => 'test-source' },
            isHighSeverity: () => false,
            ...overrides,
        };
        return mockEvent;
    };
    const createMockThreatAnalysis = (threatCount = 1) => ({
        threats: Array.from({ length: threatCount }, (_, i) => ({
            id: `threat-${i}`,
            type: 'MALWARE',
            category: 'NETWORK',
            severity: threat_severity_enum_1.ThreatSeverity.HIGH,
            confidence: 85,
            name: `Test Threat ${i}`,
            description: `Test threat description ${i}`,
            indicators: [`indicator-${i}`],
            source: 'INTERNAL_ANALYSIS',
            timestamp: new Date(),
        })),
        confidence: 85,
        analysisTime: new Date(),
        analysisDurationMs: 1000,
        analysisMethod: 'MACHINE_LEARNING',
        riskScore: 75,
        indicators: [],
        recommendations: ['Investigate further'],
        falsePositiveProbability: 15,
    });
    const createMockVulnerabilityScan = (vulnerabilityCount = 1) => ({
        vulnerabilities: Array.from({ length: vulnerabilityCount }, (_, i) => ({
            id: `vuln-${i}`,
            title: `Test Vulnerability ${i}`,
            severity: vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH,
            cvssScore: 7.5,
            riskScore: 80,
            category: 'INJECTION',
            type: 'KNOWN_VULNERABILITY',
            affectedComponents: [],
            discoveredAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
        })),
        scanTime: new Date(),
        scanDuration: 2000,
        scanMethod: 'AUTHENTICATED',
        coverage: 95,
        confidence: 90,
        target: { id: 'test-target', type: 'APPLICATION', address: 'test-app' },
        statistics: {
            totalVulnerabilities: vulnerabilityCount,
            vulnerabilitiesBySeverity: {},
            vulnerabilitiesByCategory: {},
            vulnerabilitiesByType: {},
            coverage: { portsScanned: 100, servicesIdentified: 10, componentsAnalyzed: 5 },
            performance: { scanRate: 10, throughput: 100, accuracy: 95 },
        },
        recommendations: ['Apply security patches'],
        errors: [],
        warnings: [],
    });
    const createMockResponseResult = (actionsExecuted = 1) => ({
        success: true,
        executionId: 'exec-123',
        actionsExecuted,
        actionsFailed: 0,
        actionsSkipped: 0,
        executionTime: 5000,
        executedActions: Array.from({ length: actionsExecuted }, (_, i) => ({
            actionId: `action-${i}`,
            actionType: action_type_enum_1.ActionType.BLOCK_IP,
            actionName: `Block Action ${i}`,
            status: action_status_enum_1.ActionStatus.COMPLETED,
            startTime: new Date(),
            endTime: new Date(),
            duration: 1000,
            parameters: {},
            progress: 100,
            impact: {
                resourceImpact: 'LOW',
                networkImpact: 'MEDIUM',
                userImpact: 'LOW',
                businessImpact: 'LOW',
                securityEffectiveness: 'HIGH',
            },
        })),
        effectivenessScore: 100,
        impact: {
            threatMitigation: 'SUBSTANTIAL',
            vulnerabilityRemediation: 'PARTIAL',
            systemAvailability: 'NONE',
            performanceImpact: 'LOW',
            securityImprovement: 'HIGH',
            businessContinuity: 'NONE',
            complianceImpact: 'NONE',
        },
        errors: [],
        warnings: [],
        recommendations: [],
    });
    (0, node_test_4.beforeEach)(async () => {
        // Create mocks
        const mockEventRepository = {
            save: jest.fn(),
            findById: jest.fn(),
            findByTimeRange: jest.fn(),
            findEventsForCorrelation: jest.fn(),
            getEventStatistics: jest.fn(),
        };
        const mockThreatRepository = {
            save: jest.fn(),
            findById: jest.fn(),
            getThreatStatistics: jest.fn(),
        };
        const mockVulnerabilityRepository = {
            save: jest.fn(),
            findById: jest.fn(),
            getVulnerabilityStatistics: jest.fn(),
        };
        const mockResponseActionRepository = {
            save: jest.fn(),
            findById: jest.fn(),
        };
        const mockEventProcessor = {
            processEvent: jest.fn(),
            processBatch: jest.fn(),
        };
        const mockThreatDetector = {
            analyzeEvent: jest.fn(),
            analyzeEvents: jest.fn(),
        };
        const mockVulnerabilityScanner = {
            scanEvent: jest.fn(),
            scanTargets: jest.fn(),
        };
        const mockResponseExecutor = {
            executeResponse: jest.fn(),
            executeAction: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                security_orchestrator_service_1.SecurityOrchestratorService,
                { provide: event_repository_1.EventRepository, useValue: mockEventRepository },
                { provide: threat_repository_1.ThreatRepository, useValue: mockThreatRepository },
                { provide: vulnerability_repository_1.VulnerabilityRepository, useValue: mockVulnerabilityRepository },
                { provide: response_action_repository_1.ResponseActionRepository, useValue: mockResponseActionRepository },
                { provide: event_processor_interface_1.EventProcessor, useValue: mockEventProcessor },
                { provide: threat_detector_interface_1.ThreatDetector, useValue: mockThreatDetector },
                { provide: vulnerability_scanner_interface_1.VulnerabilityScanner, useValue: mockVulnerabilityScanner },
                { provide: response_executor_interface_1.ResponseExecutor, useValue: mockResponseExecutor },
            ],
        }).compile();
        service = module.get(security_orchestrator_service_1.SecurityOrchestratorService);
        eventRepository = module.get(event_repository_1.EventRepository);
        threatRepository = module.get(threat_repository_1.ThreatRepository);
        vulnerabilityRepository = module.get(vulnerability_repository_1.VulnerabilityRepository);
        responseActionRepository = module.get(response_action_repository_1.ResponseActionRepository);
        eventProcessor = module.get(event_processor_interface_1.EventProcessor);
        threatDetector = module.get(threat_detector_interface_1.ThreatDetector);
        vulnerabilityScanner = module.get(vulnerability_scanner_interface_1.VulnerabilityScanner);
        responseExecutor = module.get(response_executor_interface_1.ResponseExecutor);
        // Suppress console logs during tests
        jest.spyOn(common_1.Logger.prototype, 'log').mockImplementation();
        jest.spyOn(common_1.Logger.prototype, 'error').mockImplementation();
        jest.spyOn(common_1.Logger.prototype, 'warn').mockImplementation();
        jest.spyOn(common_1.Logger.prototype, 'debug').mockImplementation();
    });
    (0, node_test_3.afterEach)(() => {
        jest.clearAllMocks();
    });
    (0, node_test_2.describe)('Service Initialization', () => {
        (0, node_test_1.it)('should be defined', () => {
            expect(service).toBeDefined();
        });
        (0, node_test_1.it)('should extend BaseService', () => {
            expect(service).toHaveProperty('executeOperation');
            expect(service).toHaveProperty('createContext');
        });
    });
    (0, node_test_2.describe)('orchestrateSecurityWorkflow', () => {
        (0, node_test_1.it)('should successfully orchestrate security workflow with multiple events', async () => {
            // Arrange
            const events = [
                createMockEvent({ severity: event_severity_enum_1.EventSeverity.HIGH }),
                createMockEvent({ severity: event_severity_enum_1.EventSeverity.MEDIUM }),
                createMockEvent({ severity: event_severity_enum_1.EventSeverity.LOW }),
            ];
            const mockProcessingResult = {
                success: true,
                finalStatus: 'COMPLETED',
                stagesCompleted: ['VALIDATION', 'NORMALIZATION'],
                stagesFailed: [],
                processingDurationMs: 1000,
                errors: [],
                warnings: [],
                eventsCreated: {},
                metrics: {
                    eventsProcessed: 1,
                    eventsSuccessful: 1,
                    eventsFailed: 0,
                    eventsSkipped: 0,
                    avgProcessingTimeMs: 1000,
                    peakProcessingTimeMs: 1000,
                    stageMetrics: {},
                },
            };
            eventProcessor.processEvent.mockResolvedValue(mockProcessingResult);
            threatDetector.analyzeEvent.mockResolvedValue(createMockThreatAnalysis(2));
            vulnerabilityScanner.scanEvent.mockResolvedValue(createMockVulnerabilityScan(1));
            responseExecutor.executeResponse.mockResolvedValue(createMockResponseResult(3));
            eventRepository.findEventsForCorrelation.mockResolvedValue([]);
            const context = {
                priority: response_executor_interface_1.ResponsePriority.HIGH,
                mode: response_executor_interface_1.ResponseMode.AUTOMATIC,
                autoResponse: true,
            };
            // Act
            const result = await service.orchestrateSecurityWorkflow(events, context);
            // Assert
            expect(result.success).toBe(true);
            expect(result.data).toBeDefined();
            expect(result.data.eventsProcessed).toBe(3);
            expect(result.data.threatsDetected).toBe(6); // 2 threats per event * 3 events
            expect(result.data.vulnerabilitiesFound).toBe(3); // 1 vulnerability per event * 3 events
            expect(result.data.actionsExecuted).toBe(9); // 3 actions per event * 3 events
            expect(result.data.success).toBe(true);
            expect(result.data.recommendations).toBeDefined();
            expect(result.data.metrics).toBeDefined();
        });
        (0, node_test_1.it)('should handle orchestration with custom rules', async () => {
            // Arrange
            const events = [
                createMockEvent({ severity: event_severity_enum_1.EventSeverity.HIGH, riskScore: 90 }),
                createMockEvent({ severity: event_severity_enum_1.EventSeverity.LOW, riskScore: 20 }),
            ];
            const context = {
                customRules: [
                    {
                        id: 'skip-low-risk',
                        name: 'Skip Low Risk Events',
                        description: 'Skip processing events with risk score below 50',
                        condition: { riskScoreThreshold: 50 },
                        action: security_orchestrator_service_1.OrchestrationAction.SKIP_PROCESSING,
                        priority: 1,
                        enabled: true,
                    },
                ],
            };
            eventProcessor.processEvent.mockResolvedValue({
                success: true,
                finalStatus: 'COMPLETED',
                stagesCompleted: [],
                stagesFailed: [],
                processingDurationMs: 1000,
                errors: [],
                warnings: [],
                eventsCreated: {},
                metrics: {
                    eventsProcessed: 1,
                    eventsSuccessful: 1,
                    eventsFailed: 0,
                    eventsSkipped: 0,
                    avgProcessingTimeMs: 1000,
                    peakProcessingTimeMs: 1000,
                    stageMetrics: {},
                },
            });
            threatDetector.analyzeEvent.mockResolvedValue(createMockThreatAnalysis(0));
            // Act
            const result = await service.orchestrateSecurityWorkflow(events, context);
            // Assert
            expect(result.success).toBe(true);
            expect(result.data.eventsProcessed).toBe(1); // Only high-risk event processed
            expect(eventProcessor.processEvent).toHaveBeenCalledTimes(1);
        });
        (0, node_test_1.it)('should handle errors gracefully during orchestration', async () => {
            // Arrange
            const events = [createMockEvent()];
            const error = new Error('Processing failed');
            eventProcessor.processEvent.mockRejectedValue(error);
            threatDetector.analyzeEvent.mockResolvedValue(createMockThreatAnalysis(0));
            // Act
            const result = await service.orchestrateSecurityWorkflow(events);
            // Assert
            expect(result.success).toBe(true); // Service operation succeeds even if individual events fail
            expect(result.data.errors).toHaveLength(1);
            expect(result.data.errors[0].errorCode).toBe('EVENT_PROCESSING_FAILED');
            expect(result.data.errors[0].recoverable).toBe(true);
        });
    });
    (0, node_test_2.describe)('processSecurityEvents', () => {
        (0, node_test_1.it)('should process events through the complete pipeline', async () => {
            // Arrange
            const events = [createMockEvent({ severity: event_severity_enum_1.EventSeverity.CRITICAL })];
            eventProcessor.processEvent.mockResolvedValue({
                success: true,
                finalStatus: 'COMPLETED',
                stagesCompleted: ['VALIDATION', 'NORMALIZATION', 'ENRICHMENT'],
                stagesFailed: [],
                processingDurationMs: 2000,
                errors: [],
                warnings: [],
                eventsCreated: {
                    normalized: 'norm-123',
                    enriched: 'enrich-123',
                },
                metrics: {
                    eventsProcessed: 1,
                    eventsSuccessful: 1,
                    eventsFailed: 0,
                    eventsSkipped: 0,
                    avgProcessingTimeMs: 2000,
                    peakProcessingTimeMs: 2000,
                    stageMetrics: {},
                },
            });
            // Act
            const result = await service.processSecurityEvents(events);
            // Assert
            expect(result.success).toBe(true);
            expect(result.data).toBeDefined();
            expect(eventProcessor.processEvent).toHaveBeenCalledWith(events[0], expect.objectContaining({
                requestId: expect.any(String),
                config: expect.objectContaining({
                    enableNormalization: true,
                    enableEnrichment: true,
                    enableCorrelation: true,
                    enableThreatAnalysis: true,
                }),
            }));
        });
    });
    (0, node_test_2.describe)('performThreatHunting', () => {
        (0, node_test_1.it)('should execute comprehensive threat hunting operation', async () => {
            // Arrange
            const criteria = {
                timeRange: {
                    start: new Date('2024-01-01'),
                    end: new Date('2024-01-02'),
                },
                eventTypes: [event_type_enum_1.EventType.SECURITY_ALERT, event_type_enum_1.EventType.MALWARE_DETECTED],
                severityLevels: [event_severity_enum_1.EventSeverity.HIGH, event_severity_enum_1.EventSeverity.CRITICAL],
                maxResults: 100,
                includeCorrelation: true,
                includePatternAnalysis: true,
            };
            const huntingEvents = [
                createMockEvent({ type: event_type_enum_1.EventType.SECURITY_ALERT, severity: event_severity_enum_1.EventSeverity.HIGH }),
                createMockEvent({ type: event_type_enum_1.EventType.MALWARE_DETECTED, severity: event_severity_enum_1.EventSeverity.CRITICAL }),
            ];
            eventRepository.findByTimeRange.mockResolvedValue(huntingEvents);
            threatDetector.analyzeEvent.mockResolvedValue(createMockThreatAnalysis(3));
            // Act
            const result = await service.performThreatHunting(criteria);
            // Assert
            expect(result.success).toBe(true);
            expect(result.data).toBeDefined();
            expect(result.data.eventsAnalyzed).toBe(2);
            expect(result.data.threatsFound).toBe(6); // 3 threats per event * 2 events
            expect(result.data.patternsDetected).toBeGreaterThanOrEqual(0);
            expect(result.data.correlations).toBeDefined();
            expect(result.data.recommendations).toBeDefined();
            expect(result.data.confidence).toBeGreaterThanOrEqual(0);
            expect(eventRepository.findByTimeRange).toHaveBeenCalledWith(criteria.timeRange.start, criteria.timeRange.end, criteria.maxResults);
        });
    });
    (0, node_test_2.describe)('executeIncidentResponse', () => {
        (0, node_test_1.it)('should execute incident response plan successfully', async () => {
            // Arrange
            const incidentResponsePlan = {
                incidentId: 'incident-123',
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                category: 'MALWARE',
                actions: [
                    {
                        id: 'action-1',
                        type: action_type_enum_1.ActionType.BLOCK_IP,
                        name: 'Block Malicious IP',
                        description: 'Block the source IP address',
                        parameters: { ipAddress: '*************' },
                        priority: 10,
                        dependencies: [],
                        timeout: 30000,
                        retryPolicy: {
                            maxAttempts: 3,
                            initialDelay: 1000,
                            backoffMultiplier: 2,
                            maxDelay: 10000,
                            retryableErrors: ['TIMEOUT', 'NETWORK_ERROR'],
                        },
                        approvalRequired: false,
                        rollbackSupported: true,
                    },
                ],
                escalationRules: [],
                timeline: {
                    detection: new Date(),
                },
                stakeholders: [],
                communicationPlan: {
                    channels: [],
                    frequency: 'IMMEDIATE',
                    templates: {},
                    stakeholderMatrix: {},
                },
            };
            responseExecutor.executeAction.mockResolvedValue({
                actionId: 'action-1',
                actionType: action_type_enum_1.ActionType.BLOCK_IP,
                actionName: 'Block Malicious IP',
                status: action_status_enum_1.ActionStatus.COMPLETED,
                startTime: new Date(),
                endTime: new Date(),
                duration: 5000,
                parameters: { ipAddress: '*************' },
                progress: 100,
                impact: {
                    resourceImpact: 'LOW',
                    networkImpact: 'MEDIUM',
                    userImpact: 'LOW',
                    businessImpact: 'LOW',
                    securityEffectiveness: 'HIGH',
                },
            });
            // Act
            const result = await service.executeIncidentResponse(incidentResponsePlan);
            // Assert
            expect(result.success).toBe(true);
            expect(result.data).toBeDefined();
            expect(result.data.success).toBe(true);
            expect(result.data.actionsExecuted).toBe(1);
            expect(result.data.actionsFailed).toBe(0);
            expect(result.data.effectivenessScore).toBe(100);
            expect(responseExecutor.executeAction).toHaveBeenCalledWith(action_type_enum_1.ActionType.BLOCK_IP, { ipAddress: '*************' });
        });
    });
    (0, node_test_2.describe)('analyzeSecurityPosture', () => {
        (0, node_test_1.it)('should perform comprehensive security posture analysis', async () => {
            // Arrange
            const timeRange = {
                start: new Date('2024-01-01'),
                end: new Date('2024-01-31'),
            };
            const mockEventStats = {
                totalEvents: 1000,
                eventsByType: { SECURITY_ALERT: 500, MALWARE_DETECTED: 300, INTRUSION_ATTEMPT: 200 },
                eventsBySeverity: { LOW: 400, MEDIUM: 400, HIGH: 150, CRITICAL: 50 },
                eventsByStatus: { NEW: 100, IN_PROGRESS: 200, RESOLVED: 700 },
                averageRiskScore: 45,
                highRiskEventCount: 200,
                processingMetrics: {
                    averageProcessingTime: 2500,
                    failedProcessingCount: 25,
                    successfulProcessingCount: 975,
                },
            };
            const mockThreatStats = {
                totalThreats: 150,
                activeThreats: 25,
                resolvedThreats: 125,
                threatsBySeverity: { LOW: 50, MEDIUM: 60, HIGH: 30, CRITICAL: 10 },
                threatsByCategory: { MALWARE: 60, INTRUSION: 40, PHISHING: 30, OTHER: 20 },
                threatsByType: { APT: 10, RANSOMWARE: 15, TROJAN: 35, OTHER: 90 },
                averageRiskScore: 65,
                highRiskThreatCount: 40,
                criticalThreatCount: 10,
                averageResolutionTime: 14400000, // 4 hours
                topIndicators: [{ indicator: 'malicious-ip', count: 25 }],
                topMitreTechniques: [{ technique: 'T1055', count: 15 }],
                sourceIpDistribution: { '*************': 10, '********': 8 },
                targetIpDistribution: { '**********': 12, '***********': 6 },
            };
            const mockVulnerabilityStats = {
                totalVulnerabilities: 500,
                activeVulnerabilities: 200,
                remediatedVulnerabilities: 300,
                vulnerabilitiesBySeverity: { LOW: 200, MEDIUM: 200, HIGH: 80, CRITICAL: 20 },
                vulnerabilitiesByStatus: { DISCOVERED: 50, CONFIRMED: 100, IN_PROGRESS: 50, REMEDIATED: 300 },
                vulnerabilitiesByCategory: { INJECTION: 100, XSS: 80, AUTHENTICATION: 60, OTHER: 260 },
                averageRiskScore: 55,
                averageCvssScore: 6.2,
                highRiskVulnerabilityCount: 100,
                criticalVulnerabilityCount: 20,
                zeroDayCount: 5,
                activelyExploitedCount: 8,
                averageRemediationTime: 604800000, // 7 days
                remediationMetrics: {
                    totalRemediated: 300,
                    averageTimeToRemediation: 518400000, // 6 days
                    remediationByPriority: { 1: 20, 2: 80, 3: 200 },
                },
                assetImpact: {
                    criticalAssetsAffected: 15,
                    externalAssetsAffected: 25,
                    totalAssetsAffected: 150,
                },
            };
            eventRepository.getEventStatistics.mockResolvedValue(mockEventStats);
            threatRepository.getThreatStatistics.mockResolvedValue(mockThreatStats);
            vulnerabilityRepository.getVulnerabilityStatistics.mockResolvedValue(mockVulnerabilityStats);
            // Act
            const result = await service.analyzeSecurityPosture(timeRange);
            // Assert
            expect(result.success).toBe(true);
            expect(result.data).toBeDefined();
            expect(result.data.timeRange).toEqual(timeRange);
            expect(result.data.securityScore).toBeGreaterThanOrEqual(0);
            expect(result.data.securityScore).toBeLessThanOrEqual(100);
            expect(result.data.riskLevel).toBeDefined();
            expect(result.data.eventStatistics).toEqual(mockEventStats);
            expect(result.data.threatStatistics).toEqual(mockThreatStats);
            expect(result.data.vulnerabilityStatistics).toEqual(mockVulnerabilityStats);
            expect(result.data.recommendations).toBeDefined();
            expect(result.data.generatedAt).toBeInstanceOf(Date);
        });
    });
    (0, node_test_2.describe)('Error Handling and Edge Cases', () => {
        (0, node_test_1.it)('should handle empty event arrays gracefully', async () => {
            // Arrange
            const events = [];
            // Act
            const result = await service.orchestrateSecurityWorkflow(events);
            // Assert
            expect(result.success).toBe(true);
            expect(result.data.eventsProcessed).toBe(0);
            expect(result.data.threatsDetected).toBe(0);
            expect(result.data.vulnerabilitiesFound).toBe(0);
            expect(result.data.actionsExecuted).toBe(0);
        });
        (0, node_test_1.it)('should handle repository connection failures', async () => {
            // Arrange
            const events = [createMockEvent()];
            const repositoryError = new Error('Database connection failed');
            eventRepository.findEventsForCorrelation.mockRejectedValue(repositoryError);
            eventProcessor.processEvent.mockResolvedValue({
                success: true,
                finalStatus: 'COMPLETED',
                stagesCompleted: [],
                stagesFailed: [],
                processingDurationMs: 1000,
                errors: [],
                warnings: [],
                eventsCreated: {},
                metrics: {
                    eventsProcessed: 1,
                    eventsSuccessful: 1,
                    eventsFailed: 0,
                    eventsSkipped: 0,
                    avgProcessingTimeMs: 1000,
                    peakProcessingTimeMs: 1000,
                    stageMetrics: {},
                },
            });
            threatDetector.analyzeEvent.mockResolvedValue(createMockThreatAnalysis(0));
            // Act
            const result = await service.orchestrateSecurityWorkflow(events);
            // Assert
            expect(result.success).toBe(true);
            expect(result.data.warnings).toContain('Correlation analysis failed: Database connection failed');
        });
    });
    (0, node_test_2.describe)('Performance and Metrics', () => {
        (0, node_test_1.it)('should calculate accurate performance metrics', async () => {
            // Arrange
            const events = Array.from({ length: 5 }, () => createMockEvent());
            const processingTime = 2000;
            eventProcessor.processEvent.mockResolvedValue({
                success: true,
                finalStatus: 'COMPLETED',
                stagesCompleted: [],
                stagesFailed: [],
                processingDurationMs: processingTime,
                errors: [],
                warnings: [],
                eventsCreated: {},
                metrics: {
                    eventsProcessed: 1,
                    eventsSuccessful: 1,
                    eventsFailed: 0,
                    eventsSkipped: 0,
                    avgProcessingTimeMs: processingTime,
                    peakProcessingTimeMs: processingTime,
                    stageMetrics: {},
                },
            });
            threatDetector.analyzeEvent.mockResolvedValue({
                ...createMockThreatAnalysis(1),
                analysisDurationMs: 500,
            });
            vulnerabilityScanner.scanEvent.mockResolvedValue({
                ...createMockVulnerabilityScan(1),
                scanDuration: 1000,
            });
            responseExecutor.executeResponse.mockResolvedValue({
                ...createMockResponseResult(1),
                executionTime: 3000,
            });
            // Act
            const result = await service.orchestrateSecurityWorkflow(events);
            // Assert
            expect(result.success).toBe(true);
            expect(result.data.metrics).toBeDefined();
            expect(result.data.metrics.totalProcessingTime).toBeGreaterThan(0);
            expect(result.data.metrics.averageEventProcessingTime).toBeGreaterThan(0);
            expect(result.data.metrics.threatDetectionTime).toBe(2500); // 5 events * 500ms each
            expect(result.data.metrics.vulnerabilityScanningTime).toBe(5000); // 5 events * 1000ms each
            expect(result.data.metrics.responseExecutionTime).toBe(15000); // 5 events * 3000ms each
            expect(result.data.metrics.throughput).toBeGreaterThan(0);
            expect(result.data.metrics.successRate).toBe(100);
            expect(result.data.metrics.errorRate).toBe(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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