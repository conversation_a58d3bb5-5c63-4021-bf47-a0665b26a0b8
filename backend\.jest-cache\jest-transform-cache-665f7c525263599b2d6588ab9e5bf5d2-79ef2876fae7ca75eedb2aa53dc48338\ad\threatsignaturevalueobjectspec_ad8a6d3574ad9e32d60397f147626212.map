{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\__tests__\\threat-signature.value-object.spec.ts", "mappings": ";;AAAA,oFAAwH;AACxH,gFAAuE;AAEvE,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;IAC5C,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,gBAAgB;YAChB,MAAM,SAAS,GAAG,+CAAe,CAAC,MAAM,CACtC,SAAS,EACT,wBAAwB,EACxB,6CAAa,CAAC,IAAI,EAClB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,IAAI,EACtB,mEAAmE,EACnE,gCAAgC,EAChC,eAAe,CAChB,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACtD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,6CAAa,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iDAAiB,CAAC,OAAO,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iDAAiB,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;YACpG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACrE,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,MAAM,CAAC,CAAC;YAC1D,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,gBAAgB;YAChB,MAAM,SAAS,GAAG,+CAAe,CAAC,MAAM,CACtC,SAAS,EACT,2BAA2B,EAC3B,6CAAa,CAAC,KAAK,EACnB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,QAAQ,EAC1B,oFAAoF,EACpF,wCAAwC,EACxC,mBAAmB,EACnB;gBACE,UAAU,EAAE,uCAAe,CAAC,IAAI;gBAChC,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;gBACxC,qBAAqB,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;gBACzC,eAAe,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC;gBACrC,eAAe,EAAE,eAAe;gBAChC,mBAAmB,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;gBAC5D,MAAM,EAAE,mBAAmB;gBAC3B,UAAU,EAAE,CAAC,mCAAmC,CAAC;aAClD,CACF,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;YACnE,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;YACpE,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;YAChE,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9D,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;YAC7F,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC5D,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,mCAAmC,CAAC,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,MAAM,CACjC,EAAE,EACF,gBAAgB,EAChB,6CAAa,CAAC,IAAI,EAClB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,MAAM,EACxB,cAAc,EACd,kBAAkB,EAClB,QAAQ,CACT,CAAC,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,MAAM,CACjC,SAAS,EACT,EAAE,EACF,6CAAa,CAAC,IAAI,EAClB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,MAAM,EACxB,cAAc,EACd,kBAAkB,EAClB,QAAQ,CACT,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC;gBAC/B,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,SAA0B;gBAChC,QAAQ,EAAE,iDAAiB,CAAC,OAAO;gBACnC,QAAQ,EAAE,iDAAiB,CAAC,MAAM;gBAClC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,kBAAkB;gBAC/B,UAAU,EAAE,uCAAe,CAAC,MAAM;gBAClC,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,IAAI,EAAE,EAAE;gBACR,qBAAqB,EAAE,EAAE;gBACzB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE;oBACd,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;iBACvB;gBACD,WAAW,EAAE;oBACX,cAAc,EAAE,CAAC;oBACjB,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,CAAC;oBACX,UAAU,EAAE,CAAC;iBACd;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,KAAK;iBACnB;gBACD,KAAK,EAAE;oBACL,eAAe,EAAE,CAAC;oBAClB,aAAa,EAAE,CAAC;oBAChB,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAE,CAAC;iBACjB;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC;gBAC/B,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,6CAAa,CAAC,IAAI;gBACxB,QAAQ,EAAE,SAA8B;gBACxC,QAAQ,EAAE,iDAAiB,CAAC,MAAM;gBAClC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,kBAAkB;gBAC/B,UAAU,EAAE,uCAAe,CAAC,MAAM;gBAClC,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,IAAI,EAAE,EAAE;gBACR,qBAAqB,EAAE,EAAE;gBACzB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE;oBACd,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;iBACvB;gBACD,WAAW,EAAE;oBACX,cAAc,EAAE,CAAC;oBACjB,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,CAAC;oBACX,UAAU,EAAE,CAAC;iBACd;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,KAAK;iBACnB;gBACD,KAAK,EAAE;oBACL,eAAe,EAAE,CAAC;oBAClB,aAAa,EAAE,CAAC;oBAChB,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAE,CAAC;iBACjB;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,MAAM,CACjC,SAAS,EACT,gBAAgB,EAChB,6CAAa,CAAC,IAAI,EAClB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,MAAM,EACxB,EAAE,EACF,kBAAkB,EAClB,QAAQ,CACT,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,sBAAsB;YAC9D,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,MAAM,CACjC,SAAS,EACT,gBAAgB,EAChB,6CAAa,CAAC,IAAI,EAClB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,MAAM,EACxB,WAAW,EACX,kBAAkB,EAClB,QAAQ,CACT,CAAC,CAAC,OAAO,CAAC,+DAA+D,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC;gBAC/B,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,6CAAa,CAAC,IAAI;gBACxB,QAAQ,EAAE,iDAAiB,CAAC,OAAO;gBACnC,QAAQ,EAAE,iDAAiB,CAAC,MAAM;gBAClC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,kBAAkB;gBAC/B,UAAU,EAAE,SAA4B;gBACxC,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,IAAI,EAAE,EAAE;gBACR,qBAAqB,EAAE,EAAE;gBACzB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE;oBACd,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;iBACvB;gBACD,WAAW,EAAE;oBACX,cAAc,EAAE,CAAC;oBACjB,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,CAAC;oBACX,UAAU,EAAE,CAAC;iBACd;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,KAAK;iBACnB;gBACD,KAAK,EAAE;oBACL,eAAe,EAAE,CAAC;oBAClB,aAAa,EAAE,CAAC;oBAChB,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAE,CAAC;iBACjB;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC;gBAC/B,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,6CAAa,CAAC,IAAI;gBACxB,QAAQ,EAAE,iDAAiB,CAAC,OAAO;gBACnC,QAAQ,EAAE,iDAAiB,CAAC,MAAM;gBAClC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,kBAAkB;gBAC/B,UAAU,EAAE,uCAAe,CAAC,MAAM;gBAClC,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,IAAI,EAAE,EAAE;gBACR,qBAAqB,EAAE,EAAE;gBACzB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE;oBACd,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,GAAG,EAAE,eAAe;oBACvC,gBAAgB,EAAE,IAAI;iBACvB;gBACD,WAAW,EAAE;oBACX,cAAc,EAAE,CAAC;oBACjB,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,CAAC;oBACX,UAAU,EAAE,CAAC;iBACd;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,KAAK;iBACnB;gBACD,KAAK,EAAE;oBACL,eAAe,EAAE,CAAC;oBAClB,aAAa,EAAE,CAAC;oBAChB,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAE,CAAC;iBACjB;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC,CAAC,CAAC,OAAO,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC;gBAC/B,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,6CAAa,CAAC,IAAI;gBACxB,QAAQ,EAAE,iDAAiB,CAAC,OAAO;gBACnC,QAAQ,EAAE,iDAAiB,CAAC,MAAM;gBAClC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,kBAAkB;gBAC/B,UAAU,EAAE,uCAAe,CAAC,MAAM;gBAClC,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,IAAI,EAAE,EAAE;gBACR,qBAAqB,EAAE,EAAE;gBACzB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE;oBACd,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;iBACvB;gBACD,WAAW,EAAE;oBACX,cAAc,EAAE,CAAC,GAAG,EAAE,oBAAoB;oBAC1C,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,CAAC;oBACX,UAAU,EAAE,CAAC;iBACd;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,KAAK;iBACnB;gBACD,KAAK,EAAE;oBACL,eAAe,EAAE,CAAC;oBAClB,aAAa,EAAE,CAAC;oBAChB,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAE,CAAC;iBACjB;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC;gBAC/B,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,6CAAa,CAAC,IAAI;gBACxB,QAAQ,EAAE,iDAAiB,CAAC,OAAO;gBACnC,QAAQ,EAAE,iDAAiB,CAAC,MAAM;gBAClC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,kBAAkB;gBAC/B,UAAU,EAAE,uCAAe,CAAC,MAAM;gBAClC,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,IAAI,EAAE,EAAE;gBACR,qBAAqB,EAAE,EAAE;gBACzB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE;oBACd,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;iBACvB;gBACD,WAAW,EAAE;oBACX,cAAc,EAAE,CAAC;oBACjB,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,CAAC;oBACX,UAAU,EAAE,CAAC;iBACd;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,KAAK;iBACnB;gBACD,KAAK,EAAE;oBACL,eAAe,EAAE,GAAG;oBACpB,aAAa,EAAE,GAAG,EAAE,6BAA6B;oBACjD,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAE,CAAC;iBACjB;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC,CAAC,CAAC,OAAO,CAAC,uDAAuD,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,oBAAoB,GAAG,+CAAe,CAAC,MAAM,CACjD,SAAS,EACT,gBAAgB,EAChB,6CAAa,CAAC,IAAI,EAClB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,MAAM,EACxB,cAAc,EACd,kBAAkB,EAClB,QAAQ,CACT,CAAC;YAEF,MAAM,kBAAkB,GAAG,IAAI,+CAAe,CAAC;gBAC7C,GAAG,oBAAoB,CAAC,KAAK;gBAC7B,UAAU,EAAE;oBACV,WAAW,EAAE,IAAI;oBACjB,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,WAAW,EAAE,iBAAiB;iBAC/B;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,wBAAwB,GAAG,IAAI,+CAAe,CAAC;gBACnD,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,sBAAsB;gBAC5B,IAAI,EAAE,6CAAa,CAAC,IAAI;gBACxB,QAAQ,EAAE,iDAAiB,CAAC,OAAO;gBACnC,QAAQ,EAAE,iDAAiB,CAAC,IAAI;gBAChC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,4BAA4B;gBACzC,UAAU,EAAE,uCAAe,CAAC,IAAI;gBAChC,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,IAAI,EAAE,EAAE;gBACR,qBAAqB,EAAE,EAAE;gBACzB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE;oBACd,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,IAAI,EAAE,0BAA0B;oBACnD,gBAAgB,EAAE,IAAI;iBACvB;gBACD,WAAW,EAAE;oBACX,cAAc,EAAE,CAAC;oBACjB,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,CAAC;oBACX,UAAU,EAAE,CAAC;iBACd;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,IAAI,EAAE,YAAY;iBAChC;gBACD,KAAK,EAAE;oBACL,eAAe,EAAE,CAAC;oBAClB,aAAa,EAAE,CAAC;oBAChB,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAE,CAAC;iBACjB;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,wBAAwB,GAAG,IAAI,+CAAe,CAAC;gBACnD,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,4BAA4B;gBAClC,IAAI,EAAE,6CAAa,CAAC,IAAI;gBACxB,QAAQ,EAAE,iDAAiB,CAAC,OAAO;gBACnC,QAAQ,EAAE,iDAAiB,CAAC,MAAM;gBAClC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,4BAA4B;gBACzC,UAAU,EAAE,uCAAe,CAAC,MAAM;gBAClC,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,IAAI,EAAE,EAAE;gBACR,qBAAqB,EAAE,EAAE;gBACzB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE;oBACd,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;iBACvB;gBACD,WAAW,EAAE;oBACX,cAAc,EAAE,EAAE,EAAE,UAAU;oBAC9B,WAAW,EAAE,IAAI;oBACjB,QAAQ,EAAE,CAAC,EAAE,QAAQ;oBACrB,UAAU,EAAE,IAAI,EAAE,oBAAoB;iBACvC;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,KAAK;iBACnB;gBACD,KAAK,EAAE;oBACL,eAAe,EAAE,CAAC;oBAClB,aAAa,EAAE,CAAC;oBAChB,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAE,CAAC;iBACjB;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,kBAAkB,GAAG,IAAI,+CAAe,CAAC;gBAC7C,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,6CAAa,CAAC,IAAI;gBACxB,QAAQ,EAAE,iDAAiB,CAAC,OAAO;gBACnC,QAAQ,EAAE,iDAAiB,CAAC,MAAM;gBAClC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,qBAAqB;gBAClC,UAAU,EAAE,uCAAe,CAAC,MAAM;gBAClC,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,IAAI,EAAE,EAAE;gBACR,qBAAqB,EAAE,EAAE;gBACzB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE;oBACd,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;iBACvB;gBACD,WAAW,EAAE;oBACX,cAAc,EAAE,CAAC;oBACjB,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,CAAC;oBACX,UAAU,EAAE,CAAC;iBACd;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,KAAK;iBACnB;gBACD,KAAK,EAAE;oBACL,eAAe,EAAE,GAAG;oBACpB,aAAa,EAAE,EAAE,EAAE,eAAe;oBAClC,cAAc,EAAE,EAAE;oBAClB,aAAa,EAAE,CAAC;iBACjB;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC,CAAC;YAEH,MAAM,oBAAoB,GAAG,IAAI,+CAAe,CAAC;gBAC/C,GAAG,kBAAkB,CAAC,KAAK;gBAC3B,KAAK,EAAE;oBACL,eAAe,EAAE,CAAC,EAAE,gBAAgB;oBACpC,aAAa,EAAE,CAAC;oBAChB,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAE,CAAC;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,SAAS,GAAG,IAAI,+CAAe,CAAC;gBACpC,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,6CAAa,CAAC,IAAI;gBACxB,QAAQ,EAAE,iDAAiB,CAAC,OAAO;gBACnC,QAAQ,EAAE,iDAAiB,CAAC,MAAM;gBAClC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,gBAAgB;gBAC7B,UAAU,EAAE,uCAAe,CAAC,IAAI;gBAChC,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,IAAI,EAAE,EAAE;gBACR,qBAAqB,EAAE,EAAE;gBACzB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE;oBACd,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;iBACvB;gBACD,WAAW,EAAE;oBACX,cAAc,EAAE,EAAE;oBAClB,WAAW,EAAE,IAAI;oBACjB,QAAQ,EAAE,CAAC;oBACX,UAAU,EAAE,IAAI;iBACjB;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,KAAK;iBACnB;gBACD,KAAK,EAAE;oBACL,eAAe,EAAE,GAAG;oBACpB,aAAa,EAAE,EAAE,EAAE,eAAe;oBAClC,cAAc,EAAE,EAAE;oBAClB,aAAa,EAAE,CAAC;iBACjB;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC,CAAC;YAEH,MAAM,kBAAkB,GAAG,SAAS,CAAC,qBAAqB,EAAE,CAAC;YAC7D,MAAM,CAAC,kBAAkB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,kBAAkB,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,uBAAuB,GAAG,+CAAe,CAAC,MAAM,CACpD,SAAS,EACT,2BAA2B,EAC3B,6CAAa,CAAC,IAAI,EAClB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,MAAM,EACxB,cAAc,EACd,2BAA2B,EAC3B,QAAQ,EACR,EAAE,UAAU,EAAE,uCAAe,CAAC,IAAI,EAAE,CACrC,CAAC;YAEF,MAAM,sBAAsB,GAAG,+CAAe,CAAC,MAAM,CACnD,SAAS,EACT,0BAA0B,EAC1B,6CAAa,CAAC,IAAI,EAClB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,MAAM,EACxB,cAAc,EACd,0BAA0B,EAC1B,QAAQ,EACR,EAAE,UAAU,EAAE,uCAAe,CAAC,GAAG,EAAE,CACpC,CAAC;YAEF,MAAM,CAAC,uBAAuB,CAAC,kBAAkB,EAAE,CAAC,CAAC,eAAe,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,CAAC;QACpH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,aAAa;YAChF,MAAM,SAAS,GAAG,IAAI,+CAAe,CAAC;gBACpC,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,6CAAa,CAAC,IAAI;gBACxB,QAAQ,EAAE,iDAAiB,CAAC,OAAO;gBACnC,QAAQ,EAAE,iDAAiB,CAAC,MAAM;gBAClC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,eAAe;gBAC5B,UAAU,EAAE,uCAAe,CAAC,MAAM;gBAClC,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,QAAQ;gBACnB,UAAU,EAAE,QAAQ;gBACpB,IAAI,EAAE,EAAE;gBACR,qBAAqB,EAAE,EAAE;gBACzB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE;oBACd,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;iBACvB;gBACD,WAAW,EAAE;oBACX,cAAc,EAAE,CAAC;oBACjB,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,CAAC;oBACX,UAAU,EAAE,CAAC;iBACd;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,KAAK;iBACnB;gBACD,KAAK,EAAE;oBACL,eAAe,EAAE,CAAC;oBAClB,aAAa,EAAE,CAAC;oBAChB,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAE,CAAC;iBACjB;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,uBAAuB,GAAG,IAAI,+CAAe,CAAC;gBAClD,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,2BAA2B;gBACjC,IAAI,EAAE,6CAAa,CAAC,IAAI;gBACxB,QAAQ,EAAE,iDAAiB,CAAC,OAAO;gBACnC,QAAQ,EAAE,iDAAiB,CAAC,MAAM;gBAClC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,2BAA2B;gBACxC,UAAU,EAAE,uCAAe,CAAC,MAAM;gBAClC,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,eAAe;gBAC9E,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC9D,IAAI,EAAE,EAAE;gBACR,qBAAqB,EAAE,EAAE;gBACzB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE;oBACd,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;iBACvB;gBACD,WAAW,EAAE;oBACX,cAAc,EAAE,CAAC;oBACjB,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,CAAC;oBACX,UAAU,EAAE,CAAC;iBACd;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,KAAK;iBACnB;gBACD,KAAK,EAAE;oBACL,eAAe,EAAE,GAAG;oBACpB,aAAa,EAAE,EAAE,EAAE,oCAAoC;oBACvD,cAAc,EAAE,EAAE;oBAClB,aAAa,EAAE,CAAC;iBACjB;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,kBAAkB,GAAG,IAAI,+CAAe,CAAC;gBAC7C,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,6CAAa,CAAC,IAAI;gBACxB,QAAQ,EAAE,iDAAiB,CAAC,OAAO;gBACnC,QAAQ,EAAE,iDAAiB,CAAC,MAAM;gBAClC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,qBAAqB;gBAClC,UAAU,EAAE,uCAAe,CAAC,SAAS;gBACrC,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,IAAI,EAAE,EAAE;gBACR,qBAAqB,EAAE,EAAE;gBACzB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE;oBACd,MAAM,EAAE,eAAe;oBACvB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,IAAI;iBACvB;gBACD,WAAW,EAAE;oBACX,cAAc,EAAE,EAAE;oBAClB,WAAW,EAAE,GAAG;oBAChB,QAAQ,EAAE,CAAC;oBACX,UAAU,EAAE,IAAI;iBACjB;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,IAAI;iBAClB;gBACD,KAAK,EAAE;oBACL,eAAe,EAAE,IAAI;oBACrB,aAAa,EAAE,GAAG;oBAClB,cAAc,EAAE,EAAE;oBAClB,aAAa,EAAE,CAAC;iBACjB;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,EAAE;oBACd,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,oBAAoB,GAAG,+CAAe,CAAC,MAAM,CACjD,SAAS,EACT,uBAAuB,EACvB,6CAAa,CAAC,IAAI,EAClB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,MAAM,EACxB,cAAc,EACd,uBAAuB,EACvB,QAAQ,CACT,CAAC;YAEF,MAAM,OAAO,GAAG,oBAAoB,CAAC,qBAAqB,EAAE,CAAC;YAC7D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,SAAS,GAAG,+CAAe,CAAC,MAAM,CACtC,SAAS,EACT,gBAAgB,EAChB,6CAAa,CAAC,IAAI,EAClB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,IAAI,EACtB,cAAc,EACd,kCAAkC,EAClC,eAAe,EACf;gBACE,IAAI,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;gBACzB,qBAAqB,EAAE,CAAC,OAAO,CAAC;aACjC,CACF,CAAC;YAEF,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,6CAAa,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iDAAiB,CAAC,OAAO,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iDAAiB,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAClE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,iBAAiB,GAAG,+CAAe,CAAC,MAAM,CAC9C,SAAS,EACT,oBAAoB,EACpB,6CAAa,CAAC,KAAK,EACnB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,QAAQ,EAC1B,6BAA6B,EAC7B,wCAAwC,EACxC,aAAa,CACd,CAAC;YAEF,MAAM,IAAI,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC;YACxC,MAAM,qBAAqB,GAAG,+CAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE7D,MAAM,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAC5D,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACxE,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACxE,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACtE,MAAM,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAC9E,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,SAAS,GAAG,+CAAe,CAAC,MAAM,CACtC,SAAS,EACT,gBAAgB,EAChB,6CAAa,CAAC,IAAI,EAClB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,MAAM,EACxB,cAAc,EACd,gBAAgB,EAChB,QAAQ,EACR;gBACE,IAAI,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC;gBACzB,qBAAqB,EAAE,CAAC,OAAO,CAAC;gBAChC,eAAe,EAAE,CAAC,SAAS,CAAC;aAC7B,CACF,CAAC;YAEF,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;YAC5B,MAAM,UAAU,GAAG,SAAS,CAAC,qBAAqB,CAAC;YACnD,MAAM,SAAS,GAAG,SAAS,CAAC,eAAe,CAAC;YAE5C,6BAA6B;YAC7B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtB,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE3B,yCAAyC;YACzC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,SAAS,GAAG,+CAAe,CAAC,MAAM,CACtC,SAAS,EACT,gBAAgB,EAChB,6CAAa,CAAC,IAAI,EAClB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,MAAM,EACxB,cAAc,EACd,gBAAgB,EAChB,QAAQ,CACT,CAAC;YAEF,MAAM,cAAc,GAAG,SAAS,CAAC,cAAc,CAAC;YAChD,MAAM,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC;YAC1C,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;YACxC,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;YAC9B,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;YAEpC,8BAA8B;YAC9B,cAAc,CAAC,iBAAiB,GAAG,IAAI,CAAC;YACxC,WAAW,CAAC,cAAc,GAAG,IAAI,CAAC;YAClC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC;YAC9B,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC;YAC7B,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC;YAE7B,yCAAyC;YACzC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,SAAS,GAAG,+CAAe,CAAC,MAAM,CACtC,SAAS,EACT,mBAAmB,EACnB,6CAAa,CAAC,MAAM,EACpB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,GAAG,EACrB,cAAc,EACd,yCAAyC,EACzC,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACpD,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,SAAS,GAAG,+CAAe,CAAC,MAAM,CACtC,aAAa,EACb,oBAAoB,EACpB,6CAAa,CAAC,IAAI,EAClB,iDAAiB,CAAC,OAAO,EACzB,iDAAiB,CAAC,MAAM,EACxB,kBAAkB,EAClB,sBAAsB,EACtB,YAAY,CACb,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\__tests__\\threat-signature.value-object.spec.ts"], "sourcesContent": ["import { ThreatSignature, SignatureType, SignatureCategory, SignatureSeverity } from '../threat-signature.value-object';\r\nimport { ConfidenceLevel } from '../../../enums/confidence-level.enum';\r\n\r\ndescribe('ThreatSignature Value Object', () => {\r\n  describe('creation', () => {\r\n    it('should create valid threat signature with required properties', () => {\r\n      // Arrange & Act\r\n      const signature = ThreatSignature.create(\r\n        'sig-001',\r\n        'Malware Detection Rule',\r\n        SignatureType.YARA,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.HIGH,\r\n        'rule MalwareDetection { strings: $a = \"malicious\" condition: $a }',\r\n        'Detects known malware patterns',\r\n        'security-team'\r\n      );\r\n\r\n      // Assert\r\n      expect(signature.id).toBe('sig-001');\r\n      expect(signature.name).toBe('Malware Detection Rule');\r\n      expect(signature.type).toBe(SignatureType.YARA);\r\n      expect(signature.category).toBe(SignatureCategory.MALWARE);\r\n      expect(signature.severity).toBe(SignatureSeverity.HIGH);\r\n      expect(signature.content).toBe('rule MalwareDetection { strings: $a = \"malicious\" condition: $a }');\r\n      expect(signature.description).toBe('Detects known malware patterns');\r\n      expect(signature.author).toBe('security-team');\r\n      expect(signature.confidence).toBe(ConfidenceLevel.MEDIUM);\r\n      expect(signature.version).toBe('1.0');\r\n      expect(signature.createdAt).toBeInstanceOf(Date);\r\n      expect(signature.modifiedAt).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should create signature with optional properties', () => {\r\n      // Arrange & Act\r\n      const signature = ThreatSignature.create(\r\n        'sig-002',\r\n        'Advanced Threat Detection',\r\n        SignatureType.SNORT,\r\n        SignatureCategory.EXPLOIT,\r\n        SignatureSeverity.CRITICAL,\r\n        'alert tcp any any -> any 80 (msg:\"Exploit detected\"; content:\"exploit\"; sid:1001;)',\r\n        'Detects advanced exploitation attempts',\r\n        'threat-intel-team',\r\n        {\r\n          confidence: ConfidenceLevel.HIGH,\r\n          version: '2.1',\r\n          tags: ['exploit', 'network', 'critical'],\r\n          mitreAttackTechniques: ['T1190', 'T1068'],\r\n          targetPlatforms: ['windows', 'linux'],\r\n          detectionMethod: 'pattern_match',\r\n          detectionParameters: { sensitivity: 'high', threshold: 0.8 },\r\n          source: 'internal-research',\r\n          references: ['https://example.com/threat-report'],\r\n        }\r\n      );\r\n\r\n      // Assert\r\n      expect(signature.confidence).toBe(ConfidenceLevel.HIGH);\r\n      expect(signature.version).toBe('2.1');\r\n      expect(signature.tags).toEqual(['exploit', 'network', 'critical']);\r\n      expect(signature.mitreAttackTechniques).toEqual(['T1190', 'T1068']);\r\n      expect(signature.targetPlatforms).toEqual(['windows', 'linux']);\r\n      expect(signature.detectionLogic.method).toBe('pattern_match');\r\n      expect(signature.detectionLogic.parameters).toEqual({ sensitivity: 'high', threshold: 0.8 });\r\n      expect(signature.metadata.source).toBe('internal-research');\r\n      expect(signature.metadata.references).toEqual(['https://example.com/threat-report']);\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should require signature ID', () => {\r\n      expect(() => ThreatSignature.create(\r\n        '',\r\n        'Test Signature',\r\n        SignatureType.YARA,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.MEDIUM,\r\n        'rule test {}',\r\n        'Test description',\r\n        'author'\r\n      )).toThrow('Threat signature must have an ID');\r\n    });\r\n\r\n    it('should require signature name', () => {\r\n      expect(() => ThreatSignature.create(\r\n        'sig-001',\r\n        '',\r\n        SignatureType.YARA,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.MEDIUM,\r\n        'rule test {}',\r\n        'Test description',\r\n        'author'\r\n      )).toThrow('Threat signature must have a name');\r\n    });\r\n\r\n    it('should require valid signature type', () => {\r\n      expect(() => new ThreatSignature({\r\n        id: 'sig-001',\r\n        name: 'Test Signature',\r\n        type: 'invalid' as SignatureType,\r\n        category: SignatureCategory.MALWARE,\r\n        severity: SignatureSeverity.MEDIUM,\r\n        content: 'rule test {}',\r\n        description: 'Test description',\r\n        confidence: ConfidenceLevel.MEDIUM,\r\n        version: '1.0',\r\n        author: 'author',\r\n        createdAt: new Date(),\r\n        modifiedAt: new Date(),\r\n        tags: [],\r\n        mitreAttackTechniques: [],\r\n        targetPlatforms: [],\r\n        detectionLogic: {\r\n          method: 'pattern_match',\r\n          parameters: {},\r\n          falsePositiveRate: 0.05,\r\n          truePositiveRate: 0.85,\r\n        },\r\n        performance: {\r\n          processingTime: 0,\r\n          memoryUsage: 0,\r\n          cpuUsage: 0,\r\n          throughput: 0,\r\n        },\r\n        validation: {\r\n          isValidated: false,\r\n        },\r\n        usage: {\r\n          totalDetections: 0,\r\n          truePositives: 0,\r\n          falsePositives: 0,\r\n          detectionRate: 0,\r\n        },\r\n        metadata: {\r\n          source: 'custom',\r\n          references: [],\r\n          relatedSignatures: [],\r\n        },\r\n      })).toThrow('Invalid signature type: invalid');\r\n    });\r\n\r\n    it('should require valid signature category', () => {\r\n      expect(() => new ThreatSignature({\r\n        id: 'sig-001',\r\n        name: 'Test Signature',\r\n        type: SignatureType.YARA,\r\n        category: 'invalid' as SignatureCategory,\r\n        severity: SignatureSeverity.MEDIUM,\r\n        content: 'rule test {}',\r\n        description: 'Test description',\r\n        confidence: ConfidenceLevel.MEDIUM,\r\n        version: '1.0',\r\n        author: 'author',\r\n        createdAt: new Date(),\r\n        modifiedAt: new Date(),\r\n        tags: [],\r\n        mitreAttackTechniques: [],\r\n        targetPlatforms: [],\r\n        detectionLogic: {\r\n          method: 'pattern_match',\r\n          parameters: {},\r\n          falsePositiveRate: 0.05,\r\n          truePositiveRate: 0.85,\r\n        },\r\n        performance: {\r\n          processingTime: 0,\r\n          memoryUsage: 0,\r\n          cpuUsage: 0,\r\n          throughput: 0,\r\n        },\r\n        validation: {\r\n          isValidated: false,\r\n        },\r\n        usage: {\r\n          totalDetections: 0,\r\n          truePositives: 0,\r\n          falsePositives: 0,\r\n          detectionRate: 0,\r\n        },\r\n        metadata: {\r\n          source: 'custom',\r\n          references: [],\r\n          relatedSignatures: [],\r\n        },\r\n      })).toThrow('Invalid signature category: invalid');\r\n    });\r\n\r\n    it('should require signature content', () => {\r\n      expect(() => ThreatSignature.create(\r\n        'sig-001',\r\n        'Test Signature',\r\n        SignatureType.YARA,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.MEDIUM,\r\n        '',\r\n        'Test description',\r\n        'author'\r\n      )).toThrow('Threat signature must have content');\r\n    });\r\n\r\n    it('should enforce content length limit', () => {\r\n      const longContent = 'x'.repeat(100001); // Exceeds 100KB limit\r\n      expect(() => ThreatSignature.create(\r\n        'sig-001',\r\n        'Test Signature',\r\n        SignatureType.YARA,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.MEDIUM,\r\n        longContent,\r\n        'Test description',\r\n        'author'\r\n      )).toThrow('Signature content exceeds maximum length of 100000 characters');\r\n    });\r\n\r\n    it('should require valid confidence level', () => {\r\n      expect(() => new ThreatSignature({\r\n        id: 'sig-001',\r\n        name: 'Test Signature',\r\n        type: SignatureType.YARA,\r\n        category: SignatureCategory.MALWARE,\r\n        severity: SignatureSeverity.MEDIUM,\r\n        content: 'rule test {}',\r\n        description: 'Test description',\r\n        confidence: 'invalid' as ConfidenceLevel,\r\n        version: '1.0',\r\n        author: 'author',\r\n        createdAt: new Date(),\r\n        modifiedAt: new Date(),\r\n        tags: [],\r\n        mitreAttackTechniques: [],\r\n        targetPlatforms: [],\r\n        detectionLogic: {\r\n          method: 'pattern_match',\r\n          parameters: {},\r\n          falsePositiveRate: 0.05,\r\n          truePositiveRate: 0.85,\r\n        },\r\n        performance: {\r\n          processingTime: 0,\r\n          memoryUsage: 0,\r\n          cpuUsage: 0,\r\n          throughput: 0,\r\n        },\r\n        validation: {\r\n          isValidated: false,\r\n        },\r\n        usage: {\r\n          totalDetections: 0,\r\n          truePositives: 0,\r\n          falsePositives: 0,\r\n          detectionRate: 0,\r\n        },\r\n        metadata: {\r\n          source: 'custom',\r\n          references: [],\r\n          relatedSignatures: [],\r\n        },\r\n      })).toThrow('Invalid confidence level: invalid');\r\n    });\r\n\r\n    it('should validate false positive rate range', () => {\r\n      expect(() => new ThreatSignature({\r\n        id: 'sig-001',\r\n        name: 'Test Signature',\r\n        type: SignatureType.YARA,\r\n        category: SignatureCategory.MALWARE,\r\n        severity: SignatureSeverity.MEDIUM,\r\n        content: 'rule test {}',\r\n        description: 'Test description',\r\n        confidence: ConfidenceLevel.MEDIUM,\r\n        version: '1.0',\r\n        author: 'author',\r\n        createdAt: new Date(),\r\n        modifiedAt: new Date(),\r\n        tags: [],\r\n        mitreAttackTechniques: [],\r\n        targetPlatforms: [],\r\n        detectionLogic: {\r\n          method: 'pattern_match',\r\n          parameters: {},\r\n          falsePositiveRate: 1.5, // Invalid: > 1\r\n          truePositiveRate: 0.85,\r\n        },\r\n        performance: {\r\n          processingTime: 0,\r\n          memoryUsage: 0,\r\n          cpuUsage: 0,\r\n          throughput: 0,\r\n        },\r\n        validation: {\r\n          isValidated: false,\r\n        },\r\n        usage: {\r\n          totalDetections: 0,\r\n          truePositives: 0,\r\n          falsePositives: 0,\r\n          detectionRate: 0,\r\n        },\r\n        metadata: {\r\n          source: 'custom',\r\n          references: [],\r\n          relatedSignatures: [],\r\n        },\r\n      })).toThrow('False positive rate must be between 0 and 1');\r\n    });\r\n\r\n    it('should validate performance metrics', () => {\r\n      expect(() => new ThreatSignature({\r\n        id: 'sig-001',\r\n        name: 'Test Signature',\r\n        type: SignatureType.YARA,\r\n        category: SignatureCategory.MALWARE,\r\n        severity: SignatureSeverity.MEDIUM,\r\n        content: 'rule test {}',\r\n        description: 'Test description',\r\n        confidence: ConfidenceLevel.MEDIUM,\r\n        version: '1.0',\r\n        author: 'author',\r\n        createdAt: new Date(),\r\n        modifiedAt: new Date(),\r\n        tags: [],\r\n        mitreAttackTechniques: [],\r\n        targetPlatforms: [],\r\n        detectionLogic: {\r\n          method: 'pattern_match',\r\n          parameters: {},\r\n          falsePositiveRate: 0.05,\r\n          truePositiveRate: 0.85,\r\n        },\r\n        performance: {\r\n          processingTime: -100, // Invalid: negative\r\n          memoryUsage: 0,\r\n          cpuUsage: 0,\r\n          throughput: 0,\r\n        },\r\n        validation: {\r\n          isValidated: false,\r\n        },\r\n        usage: {\r\n          totalDetections: 0,\r\n          truePositives: 0,\r\n          falsePositives: 0,\r\n          detectionRate: 0,\r\n        },\r\n        metadata: {\r\n          source: 'custom',\r\n          references: [],\r\n          relatedSignatures: [],\r\n        },\r\n      })).toThrow('Processing time cannot be negative');\r\n    });\r\n\r\n    it('should validate usage statistics', () => {\r\n      expect(() => new ThreatSignature({\r\n        id: 'sig-001',\r\n        name: 'Test Signature',\r\n        type: SignatureType.YARA,\r\n        category: SignatureCategory.MALWARE,\r\n        severity: SignatureSeverity.MEDIUM,\r\n        content: 'rule test {}',\r\n        description: 'Test description',\r\n        confidence: ConfidenceLevel.MEDIUM,\r\n        version: '1.0',\r\n        author: 'author',\r\n        createdAt: new Date(),\r\n        modifiedAt: new Date(),\r\n        tags: [],\r\n        mitreAttackTechniques: [],\r\n        targetPlatforms: [],\r\n        detectionLogic: {\r\n          method: 'pattern_match',\r\n          parameters: {},\r\n          falsePositiveRate: 0.05,\r\n          truePositiveRate: 0.85,\r\n        },\r\n        performance: {\r\n          processingTime: 0,\r\n          memoryUsage: 0,\r\n          cpuUsage: 0,\r\n          throughput: 0,\r\n        },\r\n        validation: {\r\n          isValidated: false,\r\n        },\r\n        usage: {\r\n          totalDetections: 100,\r\n          truePositives: 150, // Invalid: > totalDetections\r\n          falsePositives: 0,\r\n          detectionRate: 0,\r\n        },\r\n        metadata: {\r\n          source: 'custom',\r\n          references: [],\r\n          relatedSignatures: [],\r\n        },\r\n      })).toThrow('True positives must be between 0 and total detections');\r\n    });\r\n  });\r\n\r\n  describe('business logic', () => {\r\n    it('should check if signature is validated', () => {\r\n      const unvalidatedSignature = ThreatSignature.create(\r\n        'sig-001',\r\n        'Test Signature',\r\n        SignatureType.YARA,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.MEDIUM,\r\n        'rule test {}',\r\n        'Test description',\r\n        'author'\r\n      );\r\n\r\n      const validatedSignature = new ThreatSignature({\r\n        ...unvalidatedSignature.value,\r\n        validation: {\r\n          isValidated: true,\r\n          validatedAt: new Date(),\r\n          validatedBy: 'security-expert',\r\n        },\r\n      });\r\n\r\n      expect(unvalidatedSignature.isValidated()).toBe(false);\r\n      expect(validatedSignature.isValidated()).toBe(true);\r\n    });\r\n\r\n    it('should check if signature is production ready', () => {\r\n      const productionReadySignature = new ThreatSignature({\r\n        id: 'sig-001',\r\n        name: 'Production Signature',\r\n        type: SignatureType.YARA,\r\n        category: SignatureCategory.MALWARE,\r\n        severity: SignatureSeverity.HIGH,\r\n        content: 'rule test {}',\r\n        description: 'Production ready signature',\r\n        confidence: ConfidenceLevel.HIGH,\r\n        version: '1.0',\r\n        author: 'author',\r\n        createdAt: new Date(),\r\n        modifiedAt: new Date(),\r\n        tags: [],\r\n        mitreAttackTechniques: [],\r\n        targetPlatforms: [],\r\n        detectionLogic: {\r\n          method: 'pattern_match',\r\n          parameters: {},\r\n          falsePositiveRate: 0.05, // Low false positive rate\r\n          truePositiveRate: 0.85,\r\n        },\r\n        performance: {\r\n          processingTime: 0,\r\n          memoryUsage: 0,\r\n          cpuUsage: 0,\r\n          throughput: 0,\r\n        },\r\n        validation: {\r\n          isValidated: true, // Validated\r\n        },\r\n        usage: {\r\n          totalDetections: 0,\r\n          truePositives: 0,\r\n          falsePositives: 0,\r\n          detectionRate: 0,\r\n        },\r\n        metadata: {\r\n          source: 'custom',\r\n          references: [],\r\n          relatedSignatures: [],\r\n        },\r\n      });\r\n\r\n      expect(productionReadySignature.isProductionReady()).toBe(true);\r\n    });\r\n\r\n    it('should check if signature is high performance', () => {\r\n      const highPerformanceSignature = new ThreatSignature({\r\n        id: 'sig-001',\r\n        name: 'High Performance Signature',\r\n        type: SignatureType.YARA,\r\n        category: SignatureCategory.MALWARE,\r\n        severity: SignatureSeverity.MEDIUM,\r\n        content: 'rule test {}',\r\n        description: 'High performance signature',\r\n        confidence: ConfidenceLevel.MEDIUM,\r\n        version: '1.0',\r\n        author: 'author',\r\n        createdAt: new Date(),\r\n        modifiedAt: new Date(),\r\n        tags: [],\r\n        mitreAttackTechniques: [],\r\n        targetPlatforms: [],\r\n        detectionLogic: {\r\n          method: 'pattern_match',\r\n          parameters: {},\r\n          falsePositiveRate: 0.05,\r\n          truePositiveRate: 0.85,\r\n        },\r\n        performance: {\r\n          processingTime: 50, // < 100ms\r\n          memoryUsage: 1024,\r\n          cpuUsage: 5, // < 10%\r\n          throughput: 2000, // > 1000 events/sec\r\n        },\r\n        validation: {\r\n          isValidated: false,\r\n        },\r\n        usage: {\r\n          totalDetections: 0,\r\n          truePositives: 0,\r\n          falsePositives: 0,\r\n          detectionRate: 0,\r\n        },\r\n        metadata: {\r\n          source: 'custom',\r\n          references: [],\r\n          relatedSignatures: [],\r\n        },\r\n      });\r\n\r\n      expect(highPerformanceSignature.isHighPerformance()).toBe(true);\r\n    });\r\n\r\n    it('should check if signature is effective', () => {\r\n      const effectiveSignature = new ThreatSignature({\r\n        id: 'sig-001',\r\n        name: 'Effective Signature',\r\n        type: SignatureType.YARA,\r\n        category: SignatureCategory.MALWARE,\r\n        severity: SignatureSeverity.MEDIUM,\r\n        content: 'rule test {}',\r\n        description: 'Effective signature',\r\n        confidence: ConfidenceLevel.MEDIUM,\r\n        version: '1.0',\r\n        author: 'author',\r\n        createdAt: new Date(),\r\n        modifiedAt: new Date(),\r\n        tags: [],\r\n        mitreAttackTechniques: [],\r\n        targetPlatforms: [],\r\n        detectionLogic: {\r\n          method: 'pattern_match',\r\n          parameters: {},\r\n          falsePositiveRate: 0.05,\r\n          truePositiveRate: 0.85,\r\n        },\r\n        performance: {\r\n          processingTime: 0,\r\n          memoryUsage: 0,\r\n          cpuUsage: 0,\r\n          throughput: 0,\r\n        },\r\n        validation: {\r\n          isValidated: false,\r\n        },\r\n        usage: {\r\n          totalDetections: 100,\r\n          truePositives: 85, // 85% accuracy\r\n          falsePositives: 15,\r\n          detectionRate: 0,\r\n        },\r\n        metadata: {\r\n          source: 'custom',\r\n          references: [],\r\n          relatedSignatures: [],\r\n        },\r\n      });\r\n\r\n      const ineffectiveSignature = new ThreatSignature({\r\n        ...effectiveSignature.value,\r\n        usage: {\r\n          totalDetections: 0, // No detections\r\n          truePositives: 0,\r\n          falsePositives: 0,\r\n          detectionRate: 0,\r\n        },\r\n      });\r\n\r\n      expect(effectiveSignature.isEffective()).toBe(true);\r\n      expect(ineffectiveSignature.isEffective()).toBe(false);\r\n    });\r\n\r\n    it('should calculate effectiveness score', () => {\r\n      const signature = new ThreatSignature({\r\n        id: 'sig-001',\r\n        name: 'Test Signature',\r\n        type: SignatureType.YARA,\r\n        category: SignatureCategory.MALWARE,\r\n        severity: SignatureSeverity.MEDIUM,\r\n        content: 'rule test {}',\r\n        description: 'Test signature',\r\n        confidence: ConfidenceLevel.HIGH,\r\n        version: '1.0',\r\n        author: 'author',\r\n        createdAt: new Date(),\r\n        modifiedAt: new Date(),\r\n        tags: [],\r\n        mitreAttackTechniques: [],\r\n        targetPlatforms: [],\r\n        detectionLogic: {\r\n          method: 'pattern_match',\r\n          parameters: {},\r\n          falsePositiveRate: 0.05,\r\n          truePositiveRate: 0.85,\r\n        },\r\n        performance: {\r\n          processingTime: 50,\r\n          memoryUsage: 1024,\r\n          cpuUsage: 5,\r\n          throughput: 2000,\r\n        },\r\n        validation: {\r\n          isValidated: false,\r\n        },\r\n        usage: {\r\n          totalDetections: 100,\r\n          truePositives: 90, // 90% accuracy\r\n          falsePositives: 10,\r\n          detectionRate: 0,\r\n        },\r\n        metadata: {\r\n          source: 'custom',\r\n          references: [],\r\n          relatedSignatures: [],\r\n        },\r\n      });\r\n\r\n      const effectivenessScore = signature.getEffectivenessScore();\r\n      expect(effectivenessScore).toBeGreaterThan(0);\r\n      expect(effectivenessScore).toBeLessThanOrEqual(100);\r\n    });\r\n\r\n    it('should calculate confidence score', () => {\r\n      const highConfidenceSignature = ThreatSignature.create(\r\n        'sig-001',\r\n        'High Confidence Signature',\r\n        SignatureType.YARA,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.MEDIUM,\r\n        'rule test {}',\r\n        'High confidence signature',\r\n        'author',\r\n        { confidence: ConfidenceLevel.HIGH }\r\n      );\r\n\r\n      const lowConfidenceSignature = ThreatSignature.create(\r\n        'sig-002',\r\n        'Low Confidence Signature',\r\n        SignatureType.YARA,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.MEDIUM,\r\n        'rule test {}',\r\n        'Low confidence signature',\r\n        'author',\r\n        { confidence: ConfidenceLevel.LOW }\r\n      );\r\n\r\n      expect(highConfidenceSignature.getConfidenceScore()).toBeGreaterThan(lowConfidenceSignature.getConfidenceScore());\r\n    });\r\n\r\n    it('should calculate signature age', () => {\r\n      const pastDate = new Date(Date.now() - (5 * 24 * 60 * 60 * 1000)); // 5 days ago\r\n      const signature = new ThreatSignature({\r\n        id: 'sig-001',\r\n        name: 'Old Signature',\r\n        type: SignatureType.YARA,\r\n        category: SignatureCategory.MALWARE,\r\n        severity: SignatureSeverity.MEDIUM,\r\n        content: 'rule test {}',\r\n        description: 'Old signature',\r\n        confidence: ConfidenceLevel.MEDIUM,\r\n        version: '1.0',\r\n        author: 'author',\r\n        createdAt: pastDate,\r\n        modifiedAt: pastDate,\r\n        tags: [],\r\n        mitreAttackTechniques: [],\r\n        targetPlatforms: [],\r\n        detectionLogic: {\r\n          method: 'pattern_match',\r\n          parameters: {},\r\n          falsePositiveRate: 0.05,\r\n          truePositiveRate: 0.85,\r\n        },\r\n        performance: {\r\n          processingTime: 0,\r\n          memoryUsage: 0,\r\n          cpuUsage: 0,\r\n          throughput: 0,\r\n        },\r\n        validation: {\r\n          isValidated: false,\r\n        },\r\n        usage: {\r\n          totalDetections: 0,\r\n          truePositives: 0,\r\n          falsePositives: 0,\r\n          detectionRate: 0,\r\n        },\r\n        metadata: {\r\n          source: 'custom',\r\n          references: [],\r\n          relatedSignatures: [],\r\n        },\r\n      });\r\n\r\n      expect(signature.getAge()).toBe(5);\r\n    });\r\n\r\n    it('should determine if signature needs update', () => {\r\n      const oldIneffectiveSignature = new ThreatSignature({\r\n        id: 'sig-001',\r\n        name: 'Old Ineffective Signature',\r\n        type: SignatureType.YARA,\r\n        category: SignatureCategory.MALWARE,\r\n        severity: SignatureSeverity.MEDIUM,\r\n        content: 'rule test {}',\r\n        description: 'Old ineffective signature',\r\n        confidence: ConfidenceLevel.MEDIUM,\r\n        version: '1.0',\r\n        author: 'author',\r\n        createdAt: new Date(Date.now() - (400 * 24 * 60 * 60 * 1000)), // 400 days ago\r\n        modifiedAt: new Date(Date.now() - (400 * 24 * 60 * 60 * 1000)),\r\n        tags: [],\r\n        mitreAttackTechniques: [],\r\n        targetPlatforms: [],\r\n        detectionLogic: {\r\n          method: 'pattern_match',\r\n          parameters: {},\r\n          falsePositiveRate: 0.05,\r\n          truePositiveRate: 0.85,\r\n        },\r\n        performance: {\r\n          processingTime: 0,\r\n          memoryUsage: 0,\r\n          cpuUsage: 0,\r\n          throughput: 0,\r\n        },\r\n        validation: {\r\n          isValidated: false,\r\n        },\r\n        usage: {\r\n          totalDetections: 100,\r\n          truePositives: 30, // 30% accuracy - poor effectiveness\r\n          falsePositives: 70,\r\n          detectionRate: 0,\r\n        },\r\n        metadata: {\r\n          source: 'custom',\r\n          references: [],\r\n          relatedSignatures: [],\r\n        },\r\n      });\r\n\r\n      expect(oldIneffectiveSignature.needsUpdate()).toBe(true);\r\n    });\r\n\r\n    it('should provide quality rating', () => {\r\n      const excellentSignature = new ThreatSignature({\r\n        id: 'sig-001',\r\n        name: 'Excellent Signature',\r\n        type: SignatureType.YARA,\r\n        category: SignatureCategory.MALWARE,\r\n        severity: SignatureSeverity.MEDIUM,\r\n        content: 'rule test {}',\r\n        description: 'Excellent signature',\r\n        confidence: ConfidenceLevel.CONFIRMED,\r\n        version: '1.0',\r\n        author: 'author',\r\n        createdAt: new Date(),\r\n        modifiedAt: new Date(),\r\n        tags: [],\r\n        mitreAttackTechniques: [],\r\n        targetPlatforms: [],\r\n        detectionLogic: {\r\n          method: 'pattern_match',\r\n          parameters: {},\r\n          falsePositiveRate: 0.01,\r\n          truePositiveRate: 0.95,\r\n        },\r\n        performance: {\r\n          processingTime: 10,\r\n          memoryUsage: 512,\r\n          cpuUsage: 2,\r\n          throughput: 5000,\r\n        },\r\n        validation: {\r\n          isValidated: true,\r\n        },\r\n        usage: {\r\n          totalDetections: 1000,\r\n          truePositives: 950,\r\n          falsePositives: 50,\r\n          detectionRate: 0,\r\n        },\r\n        metadata: {\r\n          source: 'custom',\r\n          references: [],\r\n          relatedSignatures: [],\r\n        },\r\n      });\r\n\r\n      expect(excellentSignature.getQualityRating()).toBe('excellent');\r\n    });\r\n\r\n    it('should provide recommended actions', () => {\r\n      const unvalidatedSignature = ThreatSignature.create(\r\n        'sig-001',\r\n        'Unvalidated Signature',\r\n        SignatureType.YARA,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.MEDIUM,\r\n        'rule test {}',\r\n        'Unvalidated signature',\r\n        'author'\r\n      );\r\n\r\n      const actions = unvalidatedSignature.getRecommendedActions();\r\n      expect(actions).toContain('Validate signature');\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    it('should serialize to JSON correctly', () => {\r\n      const signature = ThreatSignature.create(\r\n        'sig-001',\r\n        'Test Signature',\r\n        SignatureType.YARA,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.HIGH,\r\n        'rule test {}',\r\n        'Test signature for serialization',\r\n        'security-team',\r\n        {\r\n          tags: ['test', 'malware'],\r\n          mitreAttackTechniques: ['T1055'],\r\n        }\r\n      );\r\n\r\n      const json = signature.toJSON();\r\n\r\n      expect(json.id).toBe('sig-001');\r\n      expect(json.name).toBe('Test Signature');\r\n      expect(json.type).toBe(SignatureType.YARA);\r\n      expect(json.category).toBe(SignatureCategory.MALWARE);\r\n      expect(json.severity).toBe(SignatureSeverity.HIGH);\r\n      expect(json.content).toBe('rule test {}');\r\n      expect(json.description).toBe('Test signature for serialization');\r\n      expect(json.author).toBe('security-team');\r\n      expect(json.tags).toEqual(['test', 'malware']);\r\n      expect(json.mitreAttackTechniques).toEqual(['T1055']);\r\n      expect(json.analysis).toBeDefined();\r\n      expect(json.analysis.isValidated).toBe(false);\r\n      expect(json.analysis.effectivenessScore).toBeDefined();\r\n      expect(json.analysis.qualityRating).toBeDefined();\r\n    });\r\n\r\n    it('should deserialize from JSON correctly', () => {\r\n      const originalSignature = ThreatSignature.create(\r\n        'sig-002',\r\n        'Original Signature',\r\n        SignatureType.SNORT,\r\n        SignatureCategory.EXPLOIT,\r\n        SignatureSeverity.CRITICAL,\r\n        'alert tcp any any -> any 80',\r\n        'Original signature for deserialization',\r\n        'threat-team'\r\n      );\r\n\r\n      const json = originalSignature.toJSON();\r\n      const deserializedSignature = ThreatSignature.fromJSON(json);\r\n\r\n      expect(deserializedSignature.id).toBe(originalSignature.id);\r\n      expect(deserializedSignature.name).toBe(originalSignature.name);\r\n      expect(deserializedSignature.type).toBe(originalSignature.type);\r\n      expect(deserializedSignature.category).toBe(originalSignature.category);\r\n      expect(deserializedSignature.severity).toBe(originalSignature.severity);\r\n      expect(deserializedSignature.content).toBe(originalSignature.content);\r\n      expect(deserializedSignature.description).toBe(originalSignature.description);\r\n      expect(deserializedSignature.author).toBe(originalSignature.author);\r\n    });\r\n  });\r\n\r\n  describe('immutability', () => {\r\n    it('should return defensive copies of arrays', () => {\r\n      const signature = ThreatSignature.create(\r\n        'sig-001',\r\n        'Test Signature',\r\n        SignatureType.YARA,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.MEDIUM,\r\n        'rule test {}',\r\n        'Test signature',\r\n        'author',\r\n        {\r\n          tags: ['original', 'tag'],\r\n          mitreAttackTechniques: ['T1055'],\r\n          targetPlatforms: ['windows'],\r\n        }\r\n      );\r\n\r\n      const tags = signature.tags;\r\n      const techniques = signature.mitreAttackTechniques;\r\n      const platforms = signature.targetPlatforms;\r\n\r\n      // Modify the returned arrays\r\n      tags.push('modified');\r\n      techniques.push('T9999');\r\n      platforms.push('modified');\r\n\r\n      // Original signature should be unchanged\r\n      expect(signature.tags).toEqual(['original', 'tag']);\r\n      expect(signature.mitreAttackTechniques).toEqual(['T1055']);\r\n      expect(signature.targetPlatforms).toEqual(['windows']);\r\n    });\r\n\r\n    it('should return defensive copies of objects', () => {\r\n      const signature = ThreatSignature.create(\r\n        'sig-001',\r\n        'Test Signature',\r\n        SignatureType.YARA,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.MEDIUM,\r\n        'rule test {}',\r\n        'Test signature',\r\n        'author'\r\n      );\r\n\r\n      const detectionLogic = signature.detectionLogic;\r\n      const performance = signature.performance;\r\n      const validation = signature.validation;\r\n      const usage = signature.usage;\r\n      const metadata = signature.metadata;\r\n\r\n      // Modify the returned objects\r\n      detectionLogic.falsePositiveRate = 0.99;\r\n      performance.processingTime = 9999;\r\n      validation.isValidated = true;\r\n      usage.totalDetections = 9999;\r\n      metadata.source = 'modified';\r\n\r\n      // Original signature should be unchanged\r\n      expect(signature.detectionLogic.falsePositiveRate).toBe(0.05);\r\n      expect(signature.performance.processingTime).toBe(0);\r\n      expect(signature.validation.isValidated).toBe(false);\r\n      expect(signature.usage.totalDetections).toBe(0);\r\n      expect(signature.metadata.source).toBe('custom');\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle empty arrays and objects gracefully', () => {\r\n      const signature = ThreatSignature.create(\r\n        'sig-001',\r\n        'Minimal Signature',\r\n        SignatureType.CUSTOM,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.LOW,\r\n        'minimal rule',\r\n        'Minimal signature with no optional data',\r\n        'author'\r\n      );\r\n\r\n      expect(signature.tags).toEqual([]);\r\n      expect(signature.mitreAttackTechniques).toEqual([]);\r\n      expect(signature.targetPlatforms).toEqual([]);\r\n      expect(signature.metadata.references).toEqual([]);\r\n      expect(signature.metadata.relatedSignatures).toEqual([]);\r\n    });\r\n\r\n    it('should handle whitespace in string inputs', () => {\r\n      const signature = ThreatSignature.create(\r\n        '  sig-001  ',\r\n        '  Test Signature  ',\r\n        SignatureType.YARA,\r\n        SignatureCategory.MALWARE,\r\n        SignatureSeverity.MEDIUM,\r\n        '  rule test {}  ',\r\n        '  Test description  ',\r\n        '  author  '\r\n      );\r\n\r\n      expect(signature.id).toBe('sig-001');\r\n      expect(signature.name).toBe('Test Signature');\r\n      expect(signature.content).toBe('rule test {}');\r\n      expect(signature.description).toBe('Test description');\r\n      expect(signature.author).toBe('author');\r\n    });\r\n  });\r\n});"], "version": 3}