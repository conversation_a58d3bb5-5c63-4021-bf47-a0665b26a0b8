{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\factories\\ai-model.factory.ts", "mappings": ";;;AAAA,yHAAuG;AACvG,iEAUqC;AAmDrC,MAAa,cAAc;IACzB;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,OAA6B,EAAE,EAAmB;QACrE,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAEpC,MAAM,KAAK,GAAkE;YAC3E,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;YACzB,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;YAC/B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,aAAa,EAAE,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,aAAa,CAAC;YACrE,WAAW,EAAE,IAAI,CAAC,wBAAwB,EAAE;YAC5C,MAAM,EAAE,6BAAW,CAAC,QAAQ,EAAE,+BAA+B;YAC7D,YAAY,EAAE,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,YAAY,CAAC;YAClE,oBAAoB,EAAE,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,oBAAoB,CAAC;YAC1F,kBAAkB,EAAE,CAAC,GAAG,OAAO,CAAC,kBAAkB,CAAC;YACnD,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3C,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,GAAG;YACjC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,GAAG;YAC7B,qBAAqB,EAAE,OAAO,CAAC,qBAAqB,IAAI,GAAG;YAC3D,eAAe,EAAE,SAAS;YAC1B,QAAQ,EAAE,SAAS;YACnB,UAAU,EAAE,SAAS;YACrB,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE;SAC1D,CAAC;QAEF,OAAO,yBAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,YAAY,CAAC,IAAwB;QACjD,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QAEtC,MAAM,KAAK,GAAiB;YAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YACjD,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;QAEF,MAAM,EAAE,GAAG,8CAAc,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9C,OAAO,yBAAO,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CAAC,OAAqC;QAC7E,OAAO;YACL,QAAQ,EAAE,OAAO,EAAE,QAAQ;YAC3B,MAAM,EAAE,OAAO,EAAE,MAAM;YACvB,OAAO,EAAE,OAAO,EAAE,OAAO,IAAI,KAAK,EAAE,aAAa;YACjD,OAAO,EAAE,OAAO,EAAE,OAAO,IAAI,CAAC;YAC9B,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,EAAE;YACnC,cAAc,EAAE,OAAO,EAAE,cAAc,IAAI,EAAE;SAC9C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB;QACrC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,OAAO;YACL,aAAa,EAAE,CAAC;YAChB,kBAAkB,EAAE,CAAC;YACrB,cAAc,EAAE,CAAC;YACjB,cAAc,EAAE,CAAC;YACjB,UAAU,EAAE,CAAC;YACb,UAAU,EAAE,CAAC;YACb,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE,CAAC;YACZ,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,CAAC;YACV,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,GAAG;SACjB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,yBAAyB,CAAC,OAAoC;QAC3E,OAAO;YACL,cAAc,EAAE,OAAO,EAAE,cAAc,IAAI,IAAI;YAC/C,eAAe,EAAE,OAAO,EAAE,eAAe,IAAI,IAAI;YACjD,aAAa,EAAE,OAAO,EAAE,aAAa,IAAI,KAAK;YAC9C,iBAAiB,EAAE,OAAO,EAAE,iBAAiB,IAAI,KAAK;YACtD,kBAAkB,EAAE,OAAO,EAAE,kBAAkB,IAAI,KAAK;YACxD,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC;YACvC,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC;SAC5C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iCAAiC,CAAC,OAAuC;QACtF,OAAO;YACL,GAAG,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;YACtB,MAAM,EAAE,OAAO,EAAE,MAAM,IAAI,IAAI,EAAE,KAAK;YACtC,GAAG,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;YACtB,OAAO,EAAE,OAAO,EAAE,OAAO,IAAI,IAAI,EAAE,KAAK;YACxC,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,GAAG,EAAE,OAAO;SAC9C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAAC,OAA6B;QAChE,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,4BAAU,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,2BAAS,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3E,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;YAC/E,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC;YACtF,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC;YAChF,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,OAAO,CAAC,qBAAqB,KAAK,SAAS,IAAI,OAAO,CAAC,qBAAqB,IAAI,CAAC,EAAE,CAAC;YACtF,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,OAAO,CAAC,aAAa,EAAE,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC;YACvF,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,OAAO,CAAC,aAAa,EAAE,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;YACtF,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,OAAO,CAAC,aAAa,EAAE,SAAS,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,CAAC,SAAS,IAAI,CAAC,EAAE,CAAC;YAC3F,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CAAC,IAAwB;QAChE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,8CAAc,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,4BAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,2BAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,6BAAW,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,IAAI,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB,CAAC,SAAyC;QACtE,MAAM,cAAc,GAAyB;YAC3C,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,4BAAU,CAAC,SAAS;YAC9B,SAAS,EAAE,2BAAS,CAAC,cAAc;YACnC,kBAAkB,EAAE,CAAC,gBAAgB,EAAE,UAAU,CAAC;YAClD,IAAI,EAAE,CAAC,MAAM,CAAC;YACd,QAAQ,EAAE,GAAG;YACb,MAAM,EAAE,GAAG;YACX,qBAAqB,EAAE,EAAE;YACzB,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;SACzB,CAAC;QAEF,MAAM,OAAO,GAAG,EAAE,GAAG,cAAc,EAAE,GAAG,SAAS,EAAE,CAAC;QACpD,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;CACF;AA9QD,wCA8QC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\factories\\ai-model.factory.ts"], "sourcesContent": ["import { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { \r\n  AIModel, \r\n  AIModelProps, \r\n  AIProvider, \r\n  ModelType, \r\n  ModelStatus,\r\n  ModelConfiguration,\r\n  ModelPerformance,\r\n  ModelCapabilities,\r\n  ResourceRequirements\r\n} from '../entities/ai-model.entity';\r\n\r\n/**\r\n * AI Model Factory\r\n * \r\n * Factory for creating AI Model entities with proper validation\r\n * and default values. Encapsulates the complex creation logic\r\n * and ensures consistent model initialization.\r\n */\r\n\r\nexport interface CreateAIModelRequest {\r\n  name: string;\r\n  version: string;\r\n  provider: AIProvider;\r\n  modelType: ModelType;\r\n  configuration?: Partial<ModelConfiguration>;\r\n  capabilities?: Partial<ModelCapabilities>;\r\n  resourceRequirements?: Partial<ResourceRequirements>;\r\n  supportedTaskTypes: string[];\r\n  tags?: string[];\r\n  priority?: number;\r\n  weight?: number;\r\n  maxConcurrentRequests?: number;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\nexport interface ReconstitutionData {\r\n  id: string;\r\n  name: string;\r\n  version: string;\r\n  provider: AIProvider;\r\n  modelType: ModelType;\r\n  configuration: ModelConfiguration;\r\n  performance: ModelPerformance;\r\n  status: ModelStatus;\r\n  capabilities: ModelCapabilities;\r\n  resourceRequirements: ResourceRequirements;\r\n  supportedTaskTypes: string[];\r\n  tags: string[];\r\n  priority: number;\r\n  weight: number;\r\n  maxConcurrentRequests: number;\r\n  currentLoad: number;\r\n  lastHealthCheck?: Date;\r\n  lastUsed?: Date;\r\n  deployedAt?: Date;\r\n  metadata: Record<string, any>;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nexport class AIModelFactory {\r\n  /**\r\n   * Creates a new AI Model entity\r\n   */\r\n  public static create(request: CreateAIModelRequest, id?: UniqueEntityId): AIModel {\r\n    this.validateCreateRequest(request);\r\n\r\n    const props: Omit<AIModelProps, 'createdAt' | 'updatedAt' | 'currentLoad'> = {\r\n      name: request.name.trim(),\r\n      version: request.version.trim(),\r\n      provider: request.provider,\r\n      modelType: request.modelType,\r\n      configuration: this.createDefaultConfiguration(request.configuration),\r\n      performance: this.createDefaultPerformance(),\r\n      status: ModelStatus.INACTIVE, // New models start as inactive\r\n      capabilities: this.createDefaultCapabilities(request.capabilities),\r\n      resourceRequirements: this.createDefaultResourceRequirements(request.resourceRequirements),\r\n      supportedTaskTypes: [...request.supportedTaskTypes],\r\n      tags: request.tags ? [...request.tags] : [],\r\n      priority: request.priority ?? 1.0,\r\n      weight: request.weight ?? 1.0,\r\n      maxConcurrentRequests: request.maxConcurrentRequests ?? 100,\r\n      lastHealthCheck: undefined,\r\n      lastUsed: undefined,\r\n      deployedAt: undefined,\r\n      metadata: request.metadata ? { ...request.metadata } : {},\r\n    };\r\n\r\n    return AIModel.create(props, id);\r\n  }\r\n\r\n  /**\r\n   * Reconstitutes an AI Model entity from persistence data\r\n   */\r\n  public static reconstitute(data: ReconstitutionData): AIModel {\r\n    this.validateReconstitutionData(data);\r\n\r\n    const props: AIModelProps = {\r\n      name: data.name,\r\n      version: data.version,\r\n      provider: data.provider,\r\n      modelType: data.modelType,\r\n      configuration: data.configuration,\r\n      performance: data.performance,\r\n      status: data.status,\r\n      capabilities: data.capabilities,\r\n      resourceRequirements: data.resourceRequirements,\r\n      supportedTaskTypes: data.supportedTaskTypes,\r\n      tags: data.tags,\r\n      priority: data.priority,\r\n      weight: data.weight,\r\n      maxConcurrentRequests: data.maxConcurrentRequests,\r\n      currentLoad: data.currentLoad,\r\n      lastHealthCheck: data.lastHealthCheck,\r\n      lastUsed: data.lastUsed,\r\n      deployedAt: data.deployedAt,\r\n      metadata: data.metadata,\r\n      createdAt: data.createdAt,\r\n      updatedAt: data.updatedAt,\r\n    };\r\n\r\n    const id = UniqueEntityId.fromString(data.id);\r\n    return AIModel.reconstitute(props, id);\r\n  }\r\n\r\n  /**\r\n   * Creates a default configuration\r\n   */\r\n  private static createDefaultConfiguration(partial?: Partial<ModelConfiguration>): ModelConfiguration {\r\n    return {\r\n      endpoint: partial?.endpoint,\r\n      apiKey: partial?.apiKey,\r\n      timeout: partial?.timeout ?? 30000, // 30 seconds\r\n      retries: partial?.retries ?? 3,\r\n      batchSize: partial?.batchSize ?? 10,\r\n      customSettings: partial?.customSettings ?? {},\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates default performance metrics\r\n   */\r\n  private static createDefaultPerformance(): ModelPerformance {\r\n    const now = new Date();\r\n    return {\r\n      totalRequests: 0,\r\n      successfulRequests: 0,\r\n      failedRequests: 0,\r\n      averageLatency: 0,\r\n      p95Latency: 0,\r\n      p99Latency: 0,\r\n      accuracy: 0,\r\n      precision: 0,\r\n      recall: 0,\r\n      f1Score: 0,\r\n      throughput: 0,\r\n      lastUpdated: now,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates default capabilities\r\n   */\r\n  private static createDefaultCapabilities(partial?: Partial<ModelCapabilities>): ModelCapabilities {\r\n    return {\r\n      maxInputLength: partial?.maxInputLength ?? 4096,\r\n      maxOutputLength: partial?.maxOutputLength ?? 2048,\r\n      supportsBatch: partial?.supportsBatch ?? false,\r\n      supportsStreaming: partial?.supportsStreaming ?? false,\r\n      supportsFineTuning: partial?.supportsFineTuning ?? false,\r\n      languages: partial?.languages ?? ['en'],\r\n      modalities: partial?.modalities ?? ['text'],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates default resource requirements\r\n   */\r\n  private static createDefaultResourceRequirements(partial?: Partial<ResourceRequirements>): ResourceRequirements {\r\n    return {\r\n      cpu: partial?.cpu ?? 1,\r\n      memory: partial?.memory ?? 1024, // MB\r\n      gpu: partial?.gpu ?? 0,\r\n      storage: partial?.storage ?? 1024, // MB\r\n      bandwidth: partial?.bandwidth ?? 100, // Mbps\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Validates create request\r\n   */\r\n  private static validateCreateRequest(request: CreateAIModelRequest): void {\r\n    if (!request.name || request.name.trim().length === 0) {\r\n      throw new Error('Model name is required');\r\n    }\r\n\r\n    if (request.name.length > 255) {\r\n      throw new Error('Model name cannot exceed 255 characters');\r\n    }\r\n\r\n    if (!request.version || request.version.trim().length === 0) {\r\n      throw new Error('Model version is required');\r\n    }\r\n\r\n    if (request.version.length > 50) {\r\n      throw new Error('Model version cannot exceed 50 characters');\r\n    }\r\n\r\n    if (!Object.values(AIProvider).includes(request.provider)) {\r\n      throw new Error('Invalid AI provider');\r\n    }\r\n\r\n    if (!Object.values(ModelType).includes(request.modelType)) {\r\n      throw new Error('Invalid model type');\r\n    }\r\n\r\n    if (!request.supportedTaskTypes || request.supportedTaskTypes.length === 0) {\r\n      throw new Error('At least one supported task type is required');\r\n    }\r\n\r\n    if (request.supportedTaskTypes.some(task => !task || task.trim().length === 0)) {\r\n      throw new Error('All supported task types must be non-empty strings');\r\n    }\r\n\r\n    if (request.priority !== undefined && (request.priority < 0 || request.priority > 10)) {\r\n      throw new Error('Priority must be between 0 and 10');\r\n    }\r\n\r\n    if (request.weight !== undefined && (request.weight < 0 || request.weight > 10)) {\r\n      throw new Error('Weight must be between 0 and 10');\r\n    }\r\n\r\n    if (request.maxConcurrentRequests !== undefined && request.maxConcurrentRequests <= 0) {\r\n      throw new Error('Max concurrent requests must be greater than 0');\r\n    }\r\n\r\n    if (request.configuration?.timeout !== undefined && request.configuration.timeout <= 0) {\r\n      throw new Error('Configuration timeout must be greater than 0');\r\n    }\r\n\r\n    if (request.configuration?.retries !== undefined && request.configuration.retries < 0) {\r\n      throw new Error('Configuration retries cannot be negative');\r\n    }\r\n\r\n    if (request.configuration?.batchSize !== undefined && request.configuration.batchSize <= 0) {\r\n      throw new Error('Configuration batch size must be greater than 0');\r\n    }\r\n\r\n    if (request.tags && request.tags.some(tag => !tag || tag.trim().length === 0)) {\r\n      throw new Error('All tags must be non-empty strings');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validates reconstitution data\r\n   */\r\n  private static validateReconstitutionData(data: ReconstitutionData): void {\r\n    if (!data.id || !UniqueEntityId.isValid(data.id)) {\r\n      throw new Error('Valid ID is required for reconstitution');\r\n    }\r\n\r\n    if (!data.name || data.name.trim().length === 0) {\r\n      throw new Error('Model name is required');\r\n    }\r\n\r\n    if (!data.version || data.version.trim().length === 0) {\r\n      throw new Error('Model version is required');\r\n    }\r\n\r\n    if (!Object.values(AIProvider).includes(data.provider)) {\r\n      throw new Error('Invalid AI provider');\r\n    }\r\n\r\n    if (!Object.values(ModelType).includes(data.modelType)) {\r\n      throw new Error('Invalid model type');\r\n    }\r\n\r\n    if (!Object.values(ModelStatus).includes(data.status)) {\r\n      throw new Error('Invalid model status');\r\n    }\r\n\r\n    if (!data.supportedTaskTypes || data.supportedTaskTypes.length === 0) {\r\n      throw new Error('At least one supported task type is required');\r\n    }\r\n\r\n    if (data.priority < 0 || data.priority > 10) {\r\n      throw new Error('Priority must be between 0 and 10');\r\n    }\r\n\r\n    if (data.weight < 0 || data.weight > 10) {\r\n      throw new Error('Weight must be between 0 and 10');\r\n    }\r\n\r\n    if (data.maxConcurrentRequests <= 0) {\r\n      throw new Error('Max concurrent requests must be greater than 0');\r\n    }\r\n\r\n    if (data.currentLoad < 0) {\r\n      throw new Error('Current load cannot be negative');\r\n    }\r\n\r\n    if (!data.createdAt || !(data.createdAt instanceof Date)) {\r\n      throw new Error('Valid creation date is required');\r\n    }\r\n\r\n    if (!data.updatedAt || !(data.updatedAt instanceof Date)) {\r\n      throw new Error('Valid update date is required');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates a test AI Model with minimal configuration\r\n   */\r\n  public static createForTesting(overrides?: Partial<CreateAIModelRequest>): AIModel {\r\n    const defaultRequest: CreateAIModelRequest = {\r\n      name: 'Test Model',\r\n      version: '1.0.0',\r\n      provider: AIProvider.PYTHON_AI,\r\n      modelType: ModelType.CLASSIFICATION,\r\n      supportedTaskTypes: ['classification', 'analysis'],\r\n      tags: ['test'],\r\n      priority: 1.0,\r\n      weight: 1.0,\r\n      maxConcurrentRequests: 10,\r\n      metadata: { test: true },\r\n    };\r\n\r\n    const request = { ...defaultRequest, ...overrides };\r\n    return this.create(request);\r\n  }\r\n}"], "version": 3}