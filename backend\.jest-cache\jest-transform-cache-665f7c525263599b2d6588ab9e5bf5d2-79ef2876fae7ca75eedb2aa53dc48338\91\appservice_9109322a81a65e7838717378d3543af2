adbced0de7ae31123e1e170bc31dc9bd
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
/**
 * Root application service
 * Provides application information, status, and version details
 */
let AppService = class AppService {
    constructor(configService) {
        this.configService = configService;
    }
    /**
     * Get application information
     */
    getApplicationInfo() {
        const startTime = process.env.APP_START_TIME
            ? new Date(parseInt(process.env.APP_START_TIME))
            : new Date();
        return {
            name: 'Sentinel Vulnerability Assessment Platform',
            version: this.configService.get('APP_VERSION', '1.0.0'),
            description: 'Enterprise-grade vulnerability assessment and threat intelligence platform providing comprehensive security analysis, predictive risk assessment, and automated mitigation recommendations.',
            environment: this.configService.get('NODE_ENV', 'development'),
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            startTime: startTime.toISOString(),
            features: [
                'Vulnerability Assessment',
                'Threat Intelligence',
                'Predictive Risk Analysis',
                'Automated Mitigation',
                'Security Analytics',
                'Real-time Monitoring'
            ],
            api: {
                version: this.configService.get('API_VERSION', 'v1'),
                prefix: this.configService.get('API_PREFIX', 'api'),
                documentation: this.configService.get('NODE_ENV') !== 'production' ? '/api/docs' : null
            }
        };
    }
    /**
     * Get application status
     */
    getStatus() {
        const memoryUsage = process.memoryUsage();
        const totalMemory = memoryUsage.heapTotal + memoryUsage.external;
        const usedMemory = memoryUsage.heapUsed;
        return {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: {
                used: Math.round(usedMemory / 1024 / 1024 * 100) / 100, // MB
                total: Math.round(totalMemory / 1024 / 1024 * 100) / 100, // MB
                percentage: Math.round((usedMemory / totalMemory) * 100 * 100) / 100,
                heap: {
                    used: Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100,
                    total: Math.round(memoryUsage.heapTotal / 1024 / 1024 * 100) / 100
                },
                external: Math.round(memoryUsage.external / 1024 / 1024 * 100) / 100,
                rss: Math.round(memoryUsage.rss / 1024 / 1024 * 100) / 100
            },
            version: this.configService.get('APP_VERSION', '1.0.0'),
            environment: this.configService.get('NODE_ENV', 'development'),
            pid: process.pid,
            platform: process.platform,
            nodeVersion: process.version
        };
    }
    /**
     * Get version information
     */
    getVersion() {
        return {
            version: this.configService.get('APP_VERSION', '1.0.0'),
            buildDate: this.configService.get('BUILD_DATE', new Date().toISOString()),
            gitCommit: this.configService.get('GIT_COMMIT', 'unknown'),
            gitBranch: this.configService.get('GIT_BRANCH', 'unknown'),
            nodeVersion: process.version,
            platform: process.platform,
            architecture: process.arch,
            dependencies: {
                nestjs: this.getNestJSVersion(),
                typescript: this.getTypeScriptVersion(),
                node: process.version
            },
            buildInfo: {
                timestamp: this.configService.get('BUILD_TIMESTAMP', Date.now()),
                environment: this.configService.get('BUILD_ENV', 'development'),
                ci: this.configService.get('CI', false)
            }
        };
    }
    /**
     * Get NestJS version from package.json
     */
    getNestJSVersion() {
        try {
            // In a real implementation, you might read from package.json
            return '10.0.0'; // Placeholder
        }
        catch {
            return 'unknown';
        }
    }
    /**
     * Get TypeScript version
     */
    getTypeScriptVersion() {
        try {
            // In a real implementation, you might read from package.json
            return '5.1.3'; // Placeholder
        }
        catch {
            return 'unknown';
        }
    }
};
exports.AppService = AppService;
exports.AppService = AppService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], AppService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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