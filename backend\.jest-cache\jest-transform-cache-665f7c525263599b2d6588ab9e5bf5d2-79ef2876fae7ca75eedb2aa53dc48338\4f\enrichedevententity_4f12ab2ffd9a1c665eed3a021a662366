0bd4f2490af9b2dcd210820c238d2537
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnrichedEvent = exports.EnrichmentStatus = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const event_severity_enum_1 = require("../enums/event-severity.enum");
const enrichment_source_enum_1 = require("../enums/enrichment-source.enum");
const enriched_event_created_domain_event_1 = require("../events/enriched-event-created.domain-event");
const enriched_event_status_changed_domain_event_1 = require("../events/enriched-event-status-changed.domain-event");
const enriched_event_enrichment_failed_domain_event_1 = require("../events/enriched-event-enrichment-failed.domain-event");
/**
 * Enrichment Status Enum
 */
var EnrichmentStatus;
(function (EnrichmentStatus) {
    EnrichmentStatus["PENDING"] = "PENDING";
    EnrichmentStatus["IN_PROGRESS"] = "IN_PROGRESS";
    EnrichmentStatus["COMPLETED"] = "COMPLETED";
    EnrichmentStatus["FAILED"] = "FAILED";
    EnrichmentStatus["PARTIAL"] = "PARTIAL";
    EnrichmentStatus["SKIPPED"] = "SKIPPED";
})(EnrichmentStatus || (exports.EnrichmentStatus = EnrichmentStatus = {}));
/**
 * EnrichedEvent Entity
 *
 * Represents a security event that has been processed through the enrichment pipeline.
 * Enriched events have additional context from threat intelligence, asset management,
 * user directories, and other external sources.
 *
 * Key responsibilities:
 * - Maintain enriched event state and lifecycle
 * - Enforce enrichment business rules and data quality standards
 * - Track enrichment process and applied rules
 * - Generate domain events for enrichment state changes
 * - Support threat intelligence integration and context building
 * - Manage manual review workflow for complex enrichments
 *
 * Business Rules:
 * - Enriched events must reference a valid normalized event
 * - Enrichment data must include source attribution and confidence scores
 * - Threat intelligence scores must be calculated based on multiple sources
 * - High-risk enrichments may require manual review before processing
 * - Enrichment attempts are tracked and limited
 * - Failed enrichment must preserve data integrity and provide fallback
 */
class EnrichedEvent extends shared_kernel_1.BaseAggregateRoot {
    constructor(props, id) {
        super(props, id);
        this.validateInvariants();
    }
    /**
     * Create a new EnrichedEvent
     */
    static create(props, id) {
        const enrichedEvent = new EnrichedEvent(props, id);
        // Add domain event for enriched event creation
        enrichedEvent.addDomainEvent(new enriched_event_created_domain_event_1.EnrichedEventCreatedDomainEvent(enrichedEvent.id, {
            normalizedEventId: props.normalizedEventId,
            eventType: props.type,
            severity: props.severity,
            enrichmentStatus: props.enrichmentStatus,
            enrichmentQualityScore: props.enrichmentQualityScore,
            appliedRulesCount: props.appliedRules.length,
            enrichmentDataCount: props.enrichmentData.length,
            threatIntelScore: props.threatIntelScore,
            requiresManualReview: props.requiresManualReview || false,
        }));
        return enrichedEvent;
    }
    validateInvariants() {
        super.validateInvariants();
        if (!this.props.normalizedEventId) {
            throw new Error('EnrichedEvent must reference a normalized event');
        }
        if (!this.props.metadata) {
            throw new Error('EnrichedEvent must have metadata');
        }
        if (!this.props.type) {
            throw new Error('EnrichedEvent must have a type');
        }
        if (!this.props.severity) {
            throw new Error('EnrichedEvent must have a severity');
        }
        if (!this.props.status) {
            throw new Error('EnrichedEvent must have a status');
        }
        if (!this.props.processingStatus) {
            throw new Error('EnrichedEvent must have a processing status');
        }
        if (!this.props.enrichmentStatus) {
            throw new Error('EnrichedEvent must have an enrichment status');
        }
        if (!this.props.normalizedData) {
            throw new Error('EnrichedEvent must have normalized data');
        }
        if (!this.props.enrichedData) {
            throw new Error('EnrichedEvent must have enriched data');
        }
        if (!this.props.title || this.props.title.trim().length === 0) {
            throw new Error('EnrichedEvent must have a non-empty title');
        }
        if (!Array.isArray(this.props.appliedRules)) {
            throw new Error('EnrichedEvent must have applied rules array');
        }
        if (!Array.isArray(this.props.enrichmentData)) {
            throw new Error('EnrichedEvent must have enrichment data array');
        }
        if (this.props.enrichmentData.length > EnrichedEvent.MAX_ENRICHMENT_DATA_SOURCES) {
            throw new Error(`EnrichedEvent cannot have more than ${EnrichedEvent.MAX_ENRICHMENT_DATA_SOURCES} enrichment data sources`);
        }
        if (this.props.enrichmentQualityScore !== undefined &&
            (this.props.enrichmentQualityScore < 0 || this.props.enrichmentQualityScore > 100)) {
            throw new Error('Enrichment quality score must be between 0 and 100');
        }
        if (this.props.threatIntelScore !== undefined &&
            (this.props.threatIntelScore < 0 || this.props.threatIntelScore > 100)) {
            throw new Error('Threat intelligence score must be between 0 and 100');
        }
        if (this.props.riskScore !== undefined &&
            (this.props.riskScore < 0 || this.props.riskScore > 100)) {
            throw new Error('Risk score must be between 0 and 100');
        }
        if (this.props.confidenceLevel !== undefined &&
            (this.props.confidenceLevel < 0 || this.props.confidenceLevel > 100)) {
            throw new Error('Confidence level must be between 0 and 100');
        }
        if (this.props.enrichmentAttempts !== undefined && this.props.enrichmentAttempts < 0) {
            throw new Error('Enrichment attempts cannot be negative');
        }
        if (this.props.validationErrors &&
            this.props.validationErrors.length > EnrichedEvent.MAX_VALIDATION_ERRORS) {
            throw new Error(`Cannot have more than ${EnrichedEvent.MAX_VALIDATION_ERRORS} validation errors`);
        }
        // Validate enrichment status consistency
        this.validateEnrichmentStatusConsistency();
        // Validate enrichment data integrity
        this.validateEnrichmentDataIntegrity();
    }
    validateEnrichmentStatusConsistency() {
        // If enrichment is completed, it should have completion timestamp
        if (this.props.enrichmentStatus === EnrichmentStatus.COMPLETED) {
            if (!this.props.enrichmentCompletedAt) {
                throw new Error('Completed enrichment must have completion timestamp');
            }
            if (!this.props.enrichmentResult) {
                throw new Error('Completed enrichment must have result');
            }
        }
        // If enrichment failed, it should have error information
        if (this.props.enrichmentStatus === EnrichmentStatus.FAILED) {
            if (!this.props.lastEnrichmentError &&
                (!this.props.enrichmentResult || this.props.enrichmentResult.errors.length === 0)) {
                throw new Error('Failed enrichment must have error information');
            }
        }
        // If enrichment is in progress, it should have started timestamp
        if (this.props.enrichmentStatus === EnrichmentStatus.IN_PROGRESS) {
            if (!this.props.enrichmentStartedAt) {
                throw new Error('In-progress enrichment must have start timestamp');
            }
        }
        // Manual review consistency
        if (this.props.requiresManualReview && this.props.reviewedAt) {
            if (!this.props.reviewedBy) {
                throw new Error('Reviewed events must have reviewer information');
            }
        }
    }
    validateEnrichmentDataIntegrity() {
        // Validate enrichment data structure
        for (const enrichmentData of this.props.enrichmentData) {
            if (!enrichmentData.source) {
                throw new Error('Enrichment data must have a source');
            }
            if (!enrichmentData.type) {
                throw new Error('Enrichment data must have a type');
            }
            if (!enrichmentData.data) {
                throw new Error('Enrichment data must have data');
            }
            if (enrichmentData.confidence < 0 || enrichmentData.confidence > 100) {
                throw new Error('Enrichment data confidence must be between 0 and 100');
            }
            if (!enrichmentData.timestamp) {
                throw new Error('Enrichment data must have a timestamp');
            }
        }
        // Validate reputation scores
        if (this.props.reputationScores) {
            for (const [source, score] of Object.entries(this.props.reputationScores)) {
                if (typeof score !== 'number' || score < 0 || score > 100) {
                    throw new Error(`Reputation score for ${source} must be between 0 and 100`);
                }
            }
        }
    }
    // Getters
    get normalizedEventId() {
        return this.props.normalizedEventId;
    }
    get metadata() {
        return this.props.metadata;
    }
    get type() {
        return this.props.type;
    }
    get severity() {
        return this.props.severity;
    }
    get status() {
        return this.props.status;
    }
    get processingStatus() {
        return this.props.processingStatus;
    }
    get enrichmentStatus() {
        return this.props.enrichmentStatus;
    }
    get normalizedData() {
        return { ...this.props.normalizedData };
    }
    get enrichedData() {
        return { ...this.props.enrichedData };
    }
    get title() {
        return this.props.title;
    }
    get description() {
        return this.props.description;
    }
    get tags() {
        return this.props.tags ? [...this.props.tags] : [];
    }
    get riskScore() {
        return this.props.riskScore;
    }
    get confidenceLevel() {
        return this.props.confidenceLevel;
    }
    get attributes() {
        return this.props.attributes ? { ...this.props.attributes } : {};
    }
    get correlationId() {
        return this.props.correlationId;
    }
    get parentEventId() {
        return this.props.parentEventId;
    }
    get appliedRules() {
        return [...this.props.appliedRules];
    }
    get enrichmentData() {
        return [...this.props.enrichmentData];
    }
    get enrichmentResult() {
        return this.props.enrichmentResult ? { ...this.props.enrichmentResult } : undefined;
    }
    get enrichmentStartedAt() {
        return this.props.enrichmentStartedAt;
    }
    get enrichmentCompletedAt() {
        return this.props.enrichmentCompletedAt;
    }
    get enrichmentAttempts() {
        return this.props.enrichmentAttempts || 0;
    }
    get lastEnrichmentError() {
        return this.props.lastEnrichmentError;
    }
    get threatIntelScore() {
        return this.props.threatIntelScore;
    }
    get assetContext() {
        return this.props.assetContext ? { ...this.props.assetContext } : {};
    }
    get userContext() {
        return this.props.userContext ? { ...this.props.userContext } : {};
    }
    get networkContext() {
        return this.props.networkContext ? { ...this.props.networkContext } : {};
    }
    get geolocationContext() {
        return this.props.geolocationContext ? { ...this.props.geolocationContext } : {};
    }
    get reputationScores() {
        return this.props.reputationScores ? { ...this.props.reputationScores } : {};
    }
    get requiresManualReview() {
        return this.props.requiresManualReview || false;
    }
    get reviewNotes() {
        return this.props.reviewNotes;
    }
    get reviewedBy() {
        return this.props.reviewedBy;
    }
    get reviewedAt() {
        return this.props.reviewedAt;
    }
    get enrichmentQualityScore() {
        return this.props.enrichmentQualityScore;
    }
    get validationErrors() {
        return this.props.validationErrors ? [...this.props.validationErrors] : [];
    }
    // Business methods
    /**
     * Start enrichment process
     */
    startEnrichment() {
        if (this.props.enrichmentStatus !== EnrichmentStatus.PENDING) {
            throw new Error('Can only start enrichment for pending events');
        }
        this.props.enrichmentStatus = EnrichmentStatus.IN_PROGRESS;
        this.props.enrichmentStartedAt = new Date();
        this.props.enrichmentAttempts = (this.props.enrichmentAttempts || 0) + 1;
        this.validateInvariants();
    }
    /**
     * Complete enrichment process
     */
    completeEnrichment(result) {
        if (this.props.enrichmentStatus !== EnrichmentStatus.IN_PROGRESS) {
            throw new Error('Can only complete enrichment for in-progress events');
        }
        this.props.enrichmentStatus = result.success ? EnrichmentStatus.COMPLETED : EnrichmentStatus.PARTIAL;
        this.props.enrichmentCompletedAt = new Date();
        this.props.enrichmentResult = result;
        this.props.lastEnrichmentError = undefined;
        // Calculate enrichment quality score based on result
        this.calculateEnrichmentQualityScore(result);
        // Determine if manual review is required
        this.determineManualReviewRequirement();
        this.addDomainEvent(new enriched_event_status_changed_domain_event_1.EnrichedEventStatusChangedDomainEvent(this.id, {
            oldStatus: EnrichmentStatus.IN_PROGRESS,
            newStatus: this.props.enrichmentStatus,
            result,
            enrichmentQualityScore: this.props.enrichmentQualityScore,
            requiresManualReview: this.props.requiresManualReview || false,
        }));
        this.validateInvariants();
    }
    /**
     * Fail enrichment process
     */
    failEnrichment(error, result) {
        if (this.props.enrichmentStatus !== EnrichmentStatus.IN_PROGRESS) {
            throw new Error('Can only fail enrichment for in-progress events');
        }
        this.props.enrichmentStatus = EnrichmentStatus.FAILED;
        this.props.lastEnrichmentError = error;
        if (result) {
            this.props.enrichmentResult = {
                success: false,
                appliedRules: result.appliedRules || [],
                failedRules: result.failedRules || [],
                warnings: result.warnings || [],
                errors: result.errors || [error],
                processingDurationMs: result.processingDurationMs || 0,
                confidenceScore: result.confidenceScore || 0,
                sourcesUsed: result.sourcesUsed || 0,
                dataPointsEnriched: result.dataPointsEnriched || 0,
            };
        }
        this.addDomainEvent(new enriched_event_enrichment_failed_domain_event_1.EnrichedEventEnrichmentFailedDomainEvent(this.id, {
            normalizedEventId: this.props.normalizedEventId,
            error,
            attempt: this.enrichmentAttempts,
            maxAttemptsExceeded: this.hasExceededMaxEnrichmentAttempts(),
        }));
        this.validateInvariants();
    }
    /**
     * Skip enrichment process
     */
    skipEnrichment(reason) {
        if (![EnrichmentStatus.PENDING, EnrichmentStatus.FAILED].includes(this.props.enrichmentStatus)) {
            throw new Error('Can only skip enrichment for pending or failed events');
        }
        this.props.enrichmentStatus = EnrichmentStatus.SKIPPED;
        this.props.lastEnrichmentError = undefined;
        this.props.reviewNotes = reason;
        this.validateInvariants();
    }
    /**
     * Reset enrichment for retry
     */
    resetEnrichment() {
        if (this.hasExceededMaxEnrichmentAttempts()) {
            throw new Error('Cannot reset enrichment: maximum attempts exceeded');
        }
        this.props.enrichmentStatus = EnrichmentStatus.PENDING;
        this.props.enrichmentStartedAt = undefined;
        this.props.enrichmentCompletedAt = undefined;
        this.props.lastEnrichmentError = undefined;
        this.props.enrichmentResult = undefined;
        this.validateInvariants();
    }
    /**
     * Add enrichment data
     */
    addEnrichmentData(enrichmentData) {
        if (this.props.enrichmentData.length >= EnrichedEvent.MAX_ENRICHMENT_DATA_SOURCES) {
            throw new Error(`Cannot add more than ${EnrichedEvent.MAX_ENRICHMENT_DATA_SOURCES} enrichment data sources`);
        }
        // Check for duplicate source and type combination
        const existingData = this.props.enrichmentData.find(data => data.source === enrichmentData.source && data.type === enrichmentData.type);
        if (existingData) {
            // Update existing data
            Object.assign(existingData, enrichmentData);
        }
        else {
            // Add new data
            this.props.enrichmentData.push(enrichmentData);
        }
        this.validateInvariants();
    }
    /**
     * Update enriched data
     */
    updateEnrichedData(enrichedData) {
        this.props.enrichedData = { ...this.props.enrichedData, ...enrichedData };
    }
    /**
     * Add applied enrichment rule
     */
    addAppliedRule(rule) {
        const existingRule = this.props.appliedRules.find(r => r.id === rule.id);
        if (!existingRule) {
            this.props.appliedRules.push(rule);
        }
    }
    /**
     * Update threat intelligence score
     */
    updateThreatIntelScore(score) {
        if (score < 0 || score > 100) {
            throw new Error('Threat intelligence score must be between 0 and 100');
        }
        this.props.threatIntelScore = score;
        this.determineManualReviewRequirement();
    }
    /**
     * Update asset context
     */
    updateAssetContext(context) {
        this.props.assetContext = { ...this.props.assetContext, ...context };
    }
    /**
     * Update user context
     */
    updateUserContext(context) {
        this.props.userContext = { ...this.props.userContext, ...context };
    }
    /**
     * Update network context
     */
    updateNetworkContext(context) {
        this.props.networkContext = { ...this.props.networkContext, ...context };
    }
    /**
     * Update geolocation context
     */
    updateGeolocationContext(context) {
        this.props.geolocationContext = { ...this.props.geolocationContext, ...context };
    }
    /**
     * Add reputation score
     */
    addReputationScore(source, score) {
        if (score < 0 || score > 100) {
            throw new Error('Reputation score must be between 0 and 100');
        }
        if (!this.props.reputationScores) {
            this.props.reputationScores = {};
        }
        this.props.reputationScores[source] = score;
    }
    /**
     * Update enrichment quality score
     */
    updateEnrichmentQualityScore(score) {
        if (score < 0 || score > 100) {
            throw new Error('Enrichment quality score must be between 0 and 100');
        }
        this.props.enrichmentQualityScore = score;
        this.determineManualReviewRequirement();
    }
    /**
     * Add validation errors
     */
    addValidationErrors(errors) {
        const currentErrors = this.props.validationErrors || [];
        const newErrors = [...currentErrors, ...errors];
        if (newErrors.length > EnrichedEvent.MAX_VALIDATION_ERRORS) {
            throw new Error(`Cannot have more than ${EnrichedEvent.MAX_VALIDATION_ERRORS} validation errors`);
        }
        this.props.validationErrors = newErrors;
    }
    /**
     * Clear validation errors
     */
    clearValidationErrors() {
        this.props.validationErrors = [];
    }
    /**
     * Mark for manual review
     */
    markForManualReview(reason) {
        this.props.requiresManualReview = true;
        this.props.reviewNotes = reason;
    }
    /**
     * Complete manual review
     */
    completeManualReview(reviewedBy, notes) {
        if (!this.props.requiresManualReview) {
            throw new Error('Event is not marked for manual review');
        }
        this.props.reviewedBy = reviewedBy;
        this.props.reviewedAt = new Date();
        if (notes) {
            this.props.reviewNotes = notes;
        }
    }
    // Query methods
    /**
     * Check if enrichment is completed
     */
    isEnrichmentCompleted() {
        return this.props.enrichmentStatus === EnrichmentStatus.COMPLETED;
    }
    /**
     * Check if enrichment failed
     */
    isEnrichmentFailed() {
        return this.props.enrichmentStatus === EnrichmentStatus.FAILED;
    }
    /**
     * Check if enrichment is in progress
     */
    isEnrichmentInProgress() {
        return this.props.enrichmentStatus === EnrichmentStatus.IN_PROGRESS;
    }
    /**
     * Check if enrichment was skipped
     */
    isEnrichmentSkipped() {
        return this.props.enrichmentStatus === EnrichmentStatus.SKIPPED;
    }
    /**
     * Check if enrichment is partial
     */
    isEnrichmentPartial() {
        return this.props.enrichmentStatus === EnrichmentStatus.PARTIAL;
    }
    /**
     * Check if event has high enrichment quality
     */
    hasHighEnrichmentQuality() {
        return (this.props.enrichmentQualityScore || 0) >= EnrichedEvent.MIN_ENRICHMENT_QUALITY_SCORE;
    }
    /**
     * Check if event has validation errors
     */
    hasValidationErrors() {
        return (this.props.validationErrors?.length || 0) > 0;
    }
    /**
     * Check if event has exceeded max enrichment attempts
     */
    hasExceededMaxEnrichmentAttempts() {
        return this.enrichmentAttempts >= EnrichedEvent.MAX_ENRICHMENT_ATTEMPTS;
    }
    /**
     * Check if event is ready for next processing stage
     */
    isReadyForNextStage() {
        return (this.isEnrichmentCompleted() || this.isEnrichmentPartial()) &&
            this.hasHighEnrichmentQuality() &&
            !this.hasValidationErrors() &&
            (!this.requiresManualReview || this.reviewedAt !== undefined);
    }
    /**
     * Check if event is high risk based on threat intelligence
     */
    isHighThreatRisk() {
        return (this.props.threatIntelScore || 0) >= EnrichedEvent.HIGH_RISK_REVIEW_THRESHOLD;
    }
    /**
     * Check if event has threat intelligence data
     */
    hasThreatIntelligence() {
        return this.props.enrichmentData.some(data => [enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL, enrichment_source_enum_1.EnrichmentSource.OSINT,
            enrichment_source_enum_1.EnrichmentSource.GOVERNMENT_INTEL, enrichment_source_enum_1.EnrichmentSource.TIP].includes(data.source));
    }
    /**
     * Check if event has reputation data
     */
    hasReputationData() {
        return Object.keys(this.props.reputationScores || {}).length > 0;
    }
    /**
     * Check if event has geolocation data
     */
    hasGeolocationData() {
        return Object.keys(this.props.geolocationContext || {}).length > 0;
    }
    /**
     * Get enrichment duration
     */
    getEnrichmentDuration() {
        if (!this.props.enrichmentStartedAt) {
            return null;
        }
        const endTime = this.props.enrichmentCompletedAt || new Date();
        return endTime.getTime() - this.props.enrichmentStartedAt.getTime();
    }
    /**
     * Get applied rule names
     */
    getAppliedRuleNames() {
        return this.props.appliedRules.map(rule => rule.name);
    }
    /**
     * Check if specific rule was applied
     */
    hasAppliedRule(ruleId) {
        return this.props.appliedRules.some(rule => rule.id === ruleId);
    }
    /**
     * Get enrichment data by source
     */
    getEnrichmentDataBySource(source) {
        return this.props.enrichmentData.filter(data => data.source === source);
    }
    /**
     * Get enrichment data by type
     */
    getEnrichmentDataByType(type) {
        return this.props.enrichmentData.filter(data => data.type === type);
    }
    /**
     * Get average reputation score
     */
    getAverageReputationScore() {
        const scores = Object.values(this.props.reputationScores || {});
        if (scores.length === 0)
            return null;
        return scores.reduce((sum, score) => sum + score, 0) / scores.length;
    }
    // Private helper methods
    calculateEnrichmentQualityScore(result) {
        let score = 100;
        // Reduce score for failed rules
        const failedRulesPenalty = result.failedRules.length * 15;
        score -= failedRulesPenalty;
        // Reduce score for warnings
        const warningsPenalty = result.warnings.length * 5;
        score -= warningsPenalty;
        // Reduce score for errors
        const errorsPenalty = result.errors.length * 20;
        score -= errorsPenalty;
        // Reduce score for low confidence
        if (result.confidenceScore < 70) {
            score -= (70 - result.confidenceScore);
        }
        // Reduce score for low source usage
        if (result.sourcesUsed < 3) {
            score -= (3 - result.sourcesUsed) * 10;
        }
        // Ensure score is within valid range
        this.props.enrichmentQualityScore = Math.max(0, Math.min(100, score));
    }
    determineManualReviewRequirement() {
        // High threat intelligence risk requires manual review
        if (this.isHighThreatRisk()) {
            this.props.requiresManualReview = true;
            return;
        }
        // Low enrichment quality requires manual review
        if (!this.hasHighEnrichmentQuality()) {
            this.props.requiresManualReview = true;
            return;
        }
        // Events with validation errors require manual review
        if (this.hasValidationErrors()) {
            this.props.requiresManualReview = true;
            return;
        }
        // Critical events require manual review
        if (this.severity === event_severity_enum_1.EventSeverity.CRITICAL) {
            this.props.requiresManualReview = true;
            return;
        }
        // High risk events require manual review
        if ((this.props.riskScore || 0) >= 90) {
            this.props.requiresManualReview = true;
            return;
        }
        // Otherwise, no manual review required
        this.props.requiresManualReview = false;
    }
    /**
     * Get event summary for display
     */
    getSummary() {
        return {
            id: this.id.toString(),
            normalizedEventId: this.props.normalizedEventId.toString(),
            title: this.props.title,
            type: this.props.type,
            severity: this.props.severity,
            status: this.props.status,
            enrichmentStatus: this.props.enrichmentStatus,
            enrichmentQualityScore: this.props.enrichmentQualityScore,
            threatIntelScore: this.props.threatIntelScore,
            riskScore: this.props.riskScore,
            appliedRulesCount: this.props.appliedRules.length,
            enrichmentDataCount: this.props.enrichmentData.length,
            hasValidationErrors: this.hasValidationErrors(),
            requiresManualReview: this.requiresManualReview,
            isReadyForNextStage: this.isReadyForNextStage(),
            hasThreatIntelligence: this.hasThreatIntelligence(),
            hasReputationData: this.hasReputationData(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            normalizedEventId: this.props.normalizedEventId.toString(),
            metadata: this.props.metadata.toJSON(),
            type: this.props.type,
            severity: this.props.severity,
            status: this.props.status,
            processingStatus: this.props.processingStatus,
            enrichmentStatus: this.props.enrichmentStatus,
            normalizedData: this.props.normalizedData,
            enrichedData: this.props.enrichedData,
            title: this.props.title,
            description: this.props.description,
            tags: this.props.tags,
            riskScore: this.props.riskScore,
            confidenceLevel: this.props.confidenceLevel,
            attributes: this.props.attributes,
            correlationId: this.props.correlationId,
            parentEventId: this.props.parentEventId?.toString(),
            appliedRules: this.props.appliedRules,
            enrichmentData: this.props.enrichmentData,
            enrichmentResult: this.props.enrichmentResult,
            enrichmentStartedAt: this.props.enrichmentStartedAt?.toISOString(),
            enrichmentCompletedAt: this.props.enrichmentCompletedAt?.toISOString(),
            enrichmentAttempts: this.props.enrichmentAttempts,
            lastEnrichmentError: this.props.lastEnrichmentError,
            threatIntelScore: this.props.threatIntelScore,
            assetContext: this.props.assetContext,
            userContext: this.props.userContext,
            networkContext: this.props.networkContext,
            geolocationContext: this.props.geolocationContext,
            reputationScores: this.props.reputationScores,
            requiresManualReview: this.props.requiresManualReview,
            reviewNotes: this.props.reviewNotes,
            reviewedBy: this.props.reviewedBy,
            reviewedAt: this.props.reviewedAt?.toISOString(),
            enrichmentQualityScore: this.props.enrichmentQualityScore,
            validationErrors: this.props.validationErrors,
            summary: this.getSummary(),
        };
    }
}
exports.EnrichedEvent = EnrichedEvent;
EnrichedEvent.MAX_ENRICHMENT_ATTEMPTS = 3;
EnrichedEvent.MIN_ENRICHMENT_QUALITY_SCORE = 70;
EnrichedEvent.HIGH_RISK_REVIEW_THRESHOLD = 85;
EnrichedEvent.MAX_VALIDATION_ERRORS = 10;
EnrichedEvent.MAX_ENRICHMENT_DATA_SOURCES = 50;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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