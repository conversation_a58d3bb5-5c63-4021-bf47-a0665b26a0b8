40fb3cd06b95917d84c670591a1df5bf
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportDefinitionController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../../infrastructure/auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../../infrastructure/auth/guards/roles.guard");
const roles_decorator_1 = require("../../../infrastructure/auth/decorators/roles.decorator");
const current_user_decorator_1 = require("../../../infrastructure/auth/decorators/current-user.decorator");
const audit_interceptor_1 = require("../../../infrastructure/interceptors/audit.interceptor");
const cache_interceptor_1 = require("../../../infrastructure/interceptors/cache.interceptor");
const report_definition_service_1 = require("../services/report-definition.service");
const create_report_definition_dto_1 = require("../dto/create-report-definition.dto");
const update_report_definition_dto_1 = require("../dto/update-report-definition.dto");
const report_definition_response_dto_1 = require("../dto/report-definition-response.dto");
const report_definition_list_response_dto_1 = require("../dto/report-definition-list-response.dto");
const optimization_recommendations_dto_1 = require("../dto/optimization-recommendations.dto");
const clone_report_definition_dto_1 = require("../dto/clone-report-definition.dto");
/**
 * Report Definition Controller
 *
 * Provides REST endpoints for report definition management including:
 * - CRUD operations with comprehensive validation and error handling
 * - Template management and cloning capabilities
 * - Access control validation and user permission checking
 * - Configuration validation and optimization recommendations
 * - Usage statistics and analytics with filtering and pagination
 * - Integration with audit logging and security measures
 */
let ReportDefinitionController = class ReportDefinitionController {
    constructor(reportDefinitionService) {
        this.reportDefinitionService = reportDefinitionService;
    }
    /**
     * Create a new report definition
     */
    async createReportDefinition(createDto, user) {
        const reportDefinition = await this.reportDefinitionService.createReportDefinition(createDto, user.id);
        return new report_definition_response_dto_1.ReportDefinitionResponseDto(reportDefinition);
    }
    /**
     * Get all report definitions with filtering and pagination
     */
    async getReportDefinitions(page, limit, reportType, category, ownerId, isTemplate, tags, search, user) {
        const result = await this.reportDefinitionService.getReportDefinitions({
            page,
            limit,
            reportType,
            category,
            ownerId,
            isTemplate,
            tags,
            search,
        }, user.id, user.roles);
        return new report_definition_list_response_dto_1.ReportDefinitionListResponseDto(result);
    }
    /**
     * Get report definition by ID
     */
    async getReportDefinitionById(id, user) {
        const reportDefinition = await this.reportDefinitionService.getReportDefinitionById(id, user.id, user.roles);
        return new report_definition_response_dto_1.ReportDefinitionResponseDto(reportDefinition);
    }
    /**
     * Update report definition
     */
    async updateReportDefinition(id, updateDto, user) {
        const reportDefinition = await this.reportDefinitionService.updateReportDefinition(id, updateDto, user.id, user.roles);
        return new report_definition_response_dto_1.ReportDefinitionResponseDto(reportDefinition);
    }
    /**
     * Delete report definition
     */
    async deleteReportDefinition(id, user) {
        await this.reportDefinitionService.deleteReportDefinition(id, user.id, user.roles);
    }
    /**
     * Clone report definition from template
     */
    async cloneReportDefinition(templateId, cloneDto, user) {
        const reportDefinition = await this.reportDefinitionService.cloneReportDefinition(templateId, cloneDto.name, user.id, user.roles);
        return new report_definition_response_dto_1.ReportDefinitionResponseDto(reportDefinition);
    }
    /**
     * Get optimization recommendations
     */
    async getOptimizationRecommendations(id, user) {
        const recommendations = await this.reportDefinitionService.getOptimizationRecommendations(id, user.id, user.roles);
        return new optimization_recommendations_dto_1.OptimizationRecommendationsDto(recommendations);
    }
    /**
     * Get report templates
     */
    async getReportTemplates(category, reportType, user) {
        const result = await this.reportDefinitionService.getReportDefinitions({
            isTemplate: true,
            category,
            reportType,
            page: 1,
            limit: 100,
        }, user.id, user.roles);
        return new report_definition_list_response_dto_1.ReportDefinitionListResponseDto(result);
    }
    /**
     * Validate report configuration
     */
    async validateReportConfiguration(configDto) {
        // This would use the validation logic from the service
        // For now, return a mock validation result
        return {
            isValid: true,
            errors: [],
            warnings: [],
            recommendations: {
                performance: ['Enable caching for better performance'],
                security: ['Consider enabling audit requirements'],
                usability: ['Enable export functionality'],
                compliance: ['Extend retention period for compliance frameworks'],
            },
        };
    }
};
exports.ReportDefinitionController = ReportDefinitionController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('compliance_admin', 'security_analyst', 'auditor', 'report_creator'),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new report definition',
        description: 'Creates a new report definition with comprehensive configuration including data sources, visualizations, export settings, and access control.',
    }),
    (0, swagger_1.ApiBody)({
        type: create_report_definition_dto_1.CreateReportDefinitionDto,
        description: 'Report definition configuration',
        examples: {
            complianceReport: {
                summary: 'Compliance Dashboard Report',
                value: {
                    name: 'SOX Compliance Dashboard',
                    description: 'Comprehensive SOX compliance status report with key metrics and trends',
                    reportType: 'compliance_dashboard',
                    category: 'compliance',
                    priority: 'high',
                    dataSourceConfig: {
                        sources: [
                            {
                                type: 'compliance',
                                query: 'frameworks',
                                filters: { frameworkType: 'financial' },
                            },
                        ],
                    },
                    visualizationConfig: {
                        chartType: 'bar',
                        layout: { width: 800, height: 600, responsive: true },
                        styling: {
                            colors: ['#1f77b4', '#ff7f0e', '#2ca02c'],
                            theme: 'light',
                            fontSize: 12,
                            fontFamily: 'Arial',
                        },
                        series: [
                            {
                                name: 'Compliance Score',
                                dataField: 'score',
                                aggregation: 'avg',
                            },
                        ],
                        interactivity: {
                            drillDown: true,
                            filtering: true,
                            sorting: true,
                            export: true,
                        },
                    },
                    exportConfig: {
                        formats: ['pdf', 'excel', 'csv'],
                        defaultFormat: 'pdf',
                        deliveryOptions: {
                            email: true,
                            download: true,
                            webhook: false,
                            storage: true,
                        },
                    },
                    accessControl: {
                        visibility: 'shared',
                        allowedRoles: ['compliance_admin', 'auditor'],
                        allowedUsers: [],
                        permissions: {
                            view: true,
                            edit: true,
                            delete: false,
                            export: true,
                            share: true,
                        },
                    },
                    performanceConfig: {
                        caching: {
                            enabled: true,
                            strategy: 'redis',
                            ttl: 3600,
                            invalidationRules: ['compliance_data_updated'],
                        },
                        optimization: {
                            dataLimit: 10000,
                            queryTimeout: 30000,
                            parallelProcessing: true,
                            indexHints: ['framework_id', 'created_at'],
                        },
                        monitoring: {
                            trackExecutionTime: true,
                            trackDataVolume: true,
                            alertThresholds: {
                                executionTime: 60000,
                                dataVolume: 50000,
                                errorRate: 5,
                            },
                        },
                    },
                    tags: ['sox', 'compliance', 'financial'],
                    complianceFrameworks: ['sox'],
                    dataClassification: 'confidential',
                    auditRequired: true,
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Report definition created successfully',
        type: report_definition_response_dto_1.ReportDefinitionResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid request data or configuration validation failed',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 400 },
                message: { type: 'string', example: 'Configuration validation failed: At least one data source must be configured' },
                error: { type: 'string', example: 'Bad Request' },
                timestamp: { type: 'string', format: 'date-time' },
                path: { type: 'string', example: '/api/v1/reporting/definitions' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions to create report definitions',
    }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof create_report_definition_dto_1.CreateReportDefinitionDto !== "undefined" && create_report_definition_dto_1.CreateReportDefinitionDto) === "function" ? _b : Object, Object]),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], ReportDefinitionController.prototype, "createReportDefinition", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('compliance_admin', 'security_analyst', 'auditor', 'report_viewer'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    (0, swagger_1.ApiOperation)({
        summary: 'Get report definitions',
        description: 'Retrieves a paginated list of report definitions with optional filtering by type, category, owner, and search terms.',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number (1-based)',
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Number of items per page (max 100)',
        example: 20,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'reportType',
        required: false,
        type: String,
        description: 'Filter by report type',
        enum: [
            'compliance_dashboard',
            'assessment_report',
            'audit_summary',
            'risk_analysis',
            'evidence_report',
            'framework_status',
            'control_effectiveness',
            'violation_analysis',
            'trend_analysis',
            'executive_summary',
            'regulatory_report',
            'custom_report',
        ],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'category',
        required: false,
        type: String,
        description: 'Filter by category',
        enum: ['compliance', 'security', 'risk', 'audit', 'operational', 'financial', 'executive', 'regulatory'],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'ownerId',
        required: false,
        type: String,
        description: 'Filter by owner ID',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'isTemplate',
        required: false,
        type: Boolean,
        description: 'Filter by template status',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'tags',
        required: false,
        type: [String],
        description: 'Filter by tags',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Search in name and description',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Report definitions retrieved successfully',
        type: report_definition_list_response_dto_1.ReportDefinitionListResponseDto,
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('reportType')),
    __param(3, (0, common_1.Query)('category')),
    __param(4, (0, common_1.Query)('ownerId')),
    __param(5, (0, common_1.Query)('isTemplate')),
    __param(6, (0, common_1.Query)('tags')),
    __param(7, (0, common_1.Query)('search')),
    __param(8, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String, String, Boolean, Array, String, Object]),
    __metadata("design:returntype", typeof (_d = typeof Promise !== "undefined" && Promise) === "function" ? _d : Object)
], ReportDefinitionController.prototype, "getReportDefinitions", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('compliance_admin', 'security_analyst', 'auditor', 'report_viewer'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    (0, swagger_1.ApiOperation)({
        summary: 'Get report definition by ID',
        description: 'Retrieves a specific report definition by its ID with access control validation.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        type: String,
        format: 'uuid',
        description: 'Report definition ID',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Report definition retrieved successfully',
        type: report_definition_response_dto_1.ReportDefinitionResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Report definition not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions to view this report definition',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], ReportDefinitionController.prototype, "getReportDefinitionById", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)('compliance_admin', 'security_analyst', 'auditor', 'report_creator'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update report definition',
        description: 'Updates an existing report definition with partial or complete configuration changes.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        type: String,
        format: 'uuid',
        description: 'Report definition ID',
    }),
    (0, swagger_1.ApiBody)({
        type: update_report_definition_dto_1.UpdateReportDefinitionDto,
        description: 'Updated report definition configuration',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Report definition updated successfully',
        type: report_definition_response_dto_1.ReportDefinitionResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Report definition not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions to update this report definition',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid update data or configuration validation failed',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_f = typeof update_report_definition_dto_1.UpdateReportDefinitionDto !== "undefined" && update_report_definition_dto_1.UpdateReportDefinitionDto) === "function" ? _f : Object, Object]),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], ReportDefinitionController.prototype, "updateReportDefinition", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('compliance_admin', 'security_analyst', 'report_creator'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete report definition',
        description: 'Soft deletes a report definition. The definition will be marked as inactive but preserved for audit purposes.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        type: String,
        format: 'uuid',
        description: 'Report definition ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NO_CONTENT,
        description: 'Report definition deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Report definition not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions to delete this report definition',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Cannot delete report with active executions',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], ReportDefinitionController.prototype, "deleteReportDefinition", null);
__decorate([
    (0, common_1.Post)(':templateId/clone'),
    (0, roles_decorator_1.Roles)('compliance_admin', 'security_analyst', 'auditor', 'report_creator'),
    (0, swagger_1.ApiOperation)({
        summary: 'Clone report definition from template',
        description: 'Creates a new report definition by cloning an existing template with customizable name and settings.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'templateId',
        type: String,
        format: 'uuid',
        description: 'Template report definition ID',
    }),
    (0, swagger_1.ApiBody)({
        type: clone_report_definition_dto_1.CloneReportDefinitionDto,
        description: 'Clone configuration',
        examples: {
            cloneExample: {
                summary: 'Clone SOX template',
                value: {
                    name: 'Q4 SOX Compliance Report',
                    description: 'Quarterly SOX compliance report for Q4 2024',
                    customizations: {
                        dataSourceConfig: {
                            sources: [
                                {
                                    type: 'compliance',
                                    query: 'frameworks',
                                    filters: { quarter: 'Q4', year: 2024 },
                                },
                            ],
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Report definition cloned successfully',
        type: report_definition_response_dto_1.ReportDefinitionResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Template not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Source report is not a template or name already exists',
    }),
    __param(0, (0, common_1.Param)('templateId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_j = typeof clone_report_definition_dto_1.CloneReportDefinitionDto !== "undefined" && clone_report_definition_dto_1.CloneReportDefinitionDto) === "function" ? _j : Object, Object]),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], ReportDefinitionController.prototype, "cloneReportDefinition", null);
__decorate([
    (0, common_1.Get)(':id/recommendations'),
    (0, roles_decorator_1.Roles)('compliance_admin', 'security_analyst', 'auditor', 'report_creator'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    (0, swagger_1.ApiOperation)({
        summary: 'Get optimization recommendations',
        description: 'Analyzes the report definition and provides recommendations for performance, security, usability, and compliance improvements.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        type: String,
        format: 'uuid',
        description: 'Report definition ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Optimization recommendations retrieved successfully',
        type: optimization_recommendations_dto_1.OptimizationRecommendationsDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Report definition not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions to view recommendations for this report',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_l = typeof Promise !== "undefined" && Promise) === "function" ? _l : Object)
], ReportDefinitionController.prototype, "getOptimizationRecommendations", null);
__decorate([
    (0, common_1.Get)('templates/list'),
    (0, roles_decorator_1.Roles)('compliance_admin', 'security_analyst', 'auditor', 'report_creator'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    (0, swagger_1.ApiOperation)({
        summary: 'Get available report templates',
        description: 'Retrieves a list of available report templates that can be used to create new reports.',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'category',
        required: false,
        type: String,
        description: 'Filter templates by category',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'reportType',
        required: false,
        type: String,
        description: 'Filter templates by report type',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Report templates retrieved successfully',
        type: report_definition_list_response_dto_1.ReportDefinitionListResponseDto,
    }),
    __param(0, (0, common_1.Query)('category')),
    __param(1, (0, common_1.Query)('reportType')),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", typeof (_m = typeof Promise !== "undefined" && Promise) === "function" ? _m : Object)
], ReportDefinitionController.prototype, "getReportTemplates", null);
__decorate([
    (0, common_1.Post)('validate'),
    (0, roles_decorator_1.Roles)('compliance_admin', 'security_analyst', 'auditor', 'report_creator'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Validate report configuration',
        description: 'Validates a report configuration without creating the report. Useful for form validation and configuration testing.',
    }),
    (0, swagger_1.ApiBody)({
        type: create_report_definition_dto_1.CreateReportDefinitionDto,
        description: 'Report configuration to validate',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Configuration is valid',
        schema: {
            type: 'object',
            properties: {
                isValid: { type: 'boolean', example: true },
                errors: { type: 'array', items: { type: 'string' }, example: [] },
                warnings: { type: 'array', items: { type: 'string' }, example: [] },
                recommendations: {
                    type: 'object',
                    properties: {
                        performance: { type: 'array', items: { type: 'string' } },
                        security: { type: 'array', items: { type: 'string' } },
                        usability: { type: 'array', items: { type: 'string' } },
                        compliance: { type: 'array', items: { type: 'string' } },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Configuration validation failed',
        schema: {
            type: 'object',
            properties: {
                isValid: { type: 'boolean', example: false },
                errors: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['At least one data source must be configured', 'Chart type must be specified'],
                },
                warnings: { type: 'array', items: { type: 'string' } },
            },
        },
    }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_o = typeof create_report_definition_dto_1.CreateReportDefinitionDto !== "undefined" && create_report_definition_dto_1.CreateReportDefinitionDto) === "function" ? _o : Object]),
    __metadata("design:returntype", typeof (_p = typeof Promise !== "undefined" && Promise) === "function" ? _p : Object)
], ReportDefinitionController.prototype, "validateReportConfiguration", null);
exports.ReportDefinitionController = ReportDefinitionController = __decorate([
    (0, swagger_1.ApiTags)('Report Definitions'),
    (0, common_1.Controller)('api/v1/reporting/definitions'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.UseInterceptors)(audit_interceptor_1.AuditInterceptor),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [typeof (_a = typeof report_definition_service_1.ReportDefinitionService !== "undefined" && report_definition_service_1.ReportDefinitionService) === "function" ? _a : Object])
], ReportDefinitionController);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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