{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\ai.config.ts", "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,qDAAsG;AACtG,yDAAoD;AAEpD,oEAAoE;AACpE,MAAM,QAAQ,GAAG;IACf,OAAO,EAAE,CAAC,KAAyB,EAAE,QAAQ,GAAG,KAAK,EAAW,EAAE,CAChE,KAAK,EAAE,WAAW,EAAE,KAAK,MAAM,IAAI,QAAQ;IAE7C,MAAM,EAAE,CAAC,KAAyB,EAAE,QAAgB,EAAE,KAAK,GAAG,EAAE,EAAU,EAAE;QAC1E,IAAI,CAAC,KAAK;YAAE,OAAO,QAAQ,CAAC;QAC5B,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACtC,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;IAClD,CAAC;IAED,KAAK,EAAE,CAAC,KAAyB,EAAE,QAAgB,EAAU,EAAE;QAC7D,IAAI,CAAC,KAAK;YAAE,OAAO,QAAQ,CAAC;QAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACxC,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;IAClD,CAAC;IAED,MAAM,EAAE,CAAC,KAAyB,EAAE,QAAgB,EAAU,EAAE,CAAC,KAAK,IAAI,QAAQ;CAC1E,CAAC;AAwFX,2DAA2D;AAC3D,MAAM,cAAc,GAAG,GAAa,EAAE;IACpC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAExB,MAAM,OAAO,GAAkB;QAC7B,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QACpD,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,uBAAuB,CAAC;QACpE,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,yBAAyB,CAAC;QAC7E,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,KAAK,CAAC;QAC1D,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,IAAI,CAAC;KAC1D,CAAC;IAEF,MAAM,KAAK,GAAgB;QACzB,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,EAAE,CAAC,CAAC;QAC9D,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,EAAE,IAAI,CAAC;QAC3D,iBAAiB,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,+BAA+B,CAAC,EAAE,CAAC,CAAC;QAC1E,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,KAAK,CAAC;KAC9D,CAAC;IAEF,MAAM,cAAc,GAAyB;QAC3C,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QACpE,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,CAAC,EAAE,CAAC,CAAC;QAC1E,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,oCAAoC,CAAC,EAAE,KAAK,CAAC;QAC1E,YAAY,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,CAAC,EAAE,KAAK,CAAC;QACrF,gBAAgB,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,EAAE,KAAK,CAAC;KAC9E,CAAC;IAEF,MAAM,KAAK,GAAgB;QACzB,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAClD,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC;QAC/C,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,IAAI,CAAC;QAC1D,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,KAAK,CAAC;KAC9D,CAAC;IAEF,MAAM,SAAS,GAAoB;QACjC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACvD,iBAAiB,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,EAAE,EAAE,CAAC;QACrE,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE,CAAC;KACvD,CAAC;IAEF,MAAM,MAAM,GAAiB;QAC3B,qBAAqB,EAAE;YACrB,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,EAAE,2BAA2B,CAAC;YACjF,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,CAAC,EAAE,KAAK,CAAC;YAChE,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,CAAC,EAAE,IAAI,CAAC;SACrE;QACD,eAAe,EAAE;YACf,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,oBAAoB,CAAC;YACnE,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,KAAK,CAAC;YACzD,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,IAAI,CAAC;SAC9D;QACD,gBAAgB,EAAE;YAChB,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,qBAAqB,CAAC;YACrE,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,KAAK,CAAC;YAC1D,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,GAAG,CAAC;SAC9D;KACF,CAAC;IAEF,MAAM,KAAK,GAAgB;QACzB,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAClD,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,eAAe,CAAC;QAC5D,WAAW,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;QAC5D,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;QAC3D,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,IAAI,CAAC;KAC/D,CAAC;IAEF,MAAM,WAAW,GAAsB;QACrC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACzD,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,CAAC,EAAE,KAAK,CAAC;QACjE,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,CAAC,EAAE,IAAI,CAAC;QAC9D,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,CAAC,EAAE,SAAS,CAAC;KACtE,CAAC;IAEF,MAAM,UAAU,GAAqB;QACnC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACvD,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,KAAK,CAAC;QACnE,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACrD,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACvD,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;KAChE,CAAC;IAEF,OAAO;QACL,OAAO;QACP,KAAK;QACL,cAAc;QACd,KAAK;QACL,SAAS;QACT,MAAM;QACN,KAAK;QACL,WAAW;QACX,UAAU;KACX,CAAC;AACJ,CAAC,CAAC;AAEF;;;GAGG;AACU,QAAA,QAAQ,GAAG,IAAA,mBAAU,EAAC,IAAI,EAAE,cAAc,CAAC,CAAC;AAEzD,mEAAmE;AACnE,MAAa,kBAAkB;CAkB9B;AAlBD,gDAkBC;AAfC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;mDAChC;AAGlB;IADC,IAAA,0BAAQ,GAAE;;+CACE;AAGb;IADC,IAAA,0BAAQ,GAAE;;kDACK;AAKhB;IAHC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,IAAI,CAAC;IACT,IAAA,qBAAG,EAAC,MAAM,CAAC;;mDACK;AAGjB;IADC,IAAA,0BAAQ,GAAE;;mDACM;AAGnB,MAAa,gBAAgB;CAiB5B;AAjBD,4CAiBC;AAbC;IAHC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,EAAE,CAAC;;kDACU;AAIlB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,GAAG,CAAC;;+CACM;AAIf;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;2DACoB;AAI3B;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,IAAI,CAAC;;kDACQ;AAGpB,MAAa,yBAAyB;CAoBrC;AApBD,8DAoBC;AAjBC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;0DAChC;AAIlB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;4DACY;AAInB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,IAAI,CAAC;;0DACO;AAIjB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,IAAI,CAAC;;+DACY;AAItB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,IAAI,CAAC;;mEACgB;AAG5B,MAAa,gBAAgB;CAe5B;AAfD,4CAeC;AAZC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;iDAChC;AAIlB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,EAAE,CAAC;;6CACK;AAIb;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,GAAG,CAAC;;kDACS;AAGlB;IADC,IAAA,0BAAQ,GAAE;;mDACQ;AAGrB,MAAa,oBAAoB;CAYhC;AAZD,oDAYC;AATC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;qDAChC;AAIlB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;+DACoB;AAI3B;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;wDACa;AAGtB,MAAa,gBAAgB;CAiB5B;AAjBD,4CAiBC;AAfC;IADC,IAAA,0BAAQ,GAAE;;8CACG;AAId;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,IAAI,CAAC;;iDACO;AAKjB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,GAAG,CAAC;;mDACU;AAKnB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;mDACY;AAGrB,MAAa,iBAAiB;CAY7B;AAZD,8CAYC;AATC;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC;8BACL,gBAAgB;gEAAC;AAIzC;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC;8BACX,gBAAgB;0DAAC;AAInC;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC;8BACV,gBAAgB;2DAAC;AAGtC,MAAa,gBAAgB;CAmB5B;AAnBD,4CAmBC;AAhBC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;iDAChC;AAGlB;IADC,IAAA,0BAAQ,GAAE;;8CACG;AAId;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;qDACc;AAIrB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;oDACa;AAIpB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,IAAI,CAAC;;oDACU;AAGtB,MAAa,sBAAsB;CAelC;AAfD,wDAeC;AAZC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;uDAChC;AAIlB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,IAAI,CAAC;;wDACQ;AAIlB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,IAAI,CAAC;;uDACO;AAGjB;IADC,IAAA,0BAAQ,GAAE;;wDACO;AAGpB,MAAa,qBAAqB;CAoBjC;AApBD,sDAoBC;AAjBC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;sDAChC;AAIlB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,KAAK,CAAC;;8DACc;AAIzB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;0DAC5B;AAItB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;2DAC3B;AAIvB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;+DACvB;AAG7B;;GAEG;AACH,MAAa,WAAW;CAoCvB;AApCD,kCAoCC;AAjCC;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC;8BACrB,kBAAkB;4CAAC;AAI7B;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC;8BACrB,gBAAgB;0CAAC;AAIzB;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,yBAAyB,CAAC;8BACrB,yBAAyB;mDAAC;AAI3C;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC;8BACrB,gBAAgB;0CAAC;AAIzB;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,oBAAoB,CAAC;8BACrB,oBAAoB;8CAAC;AAIjC;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,iBAAiB,CAAC;8BACrB,iBAAiB;2CAAC;AAI3B;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC;8BACrB,gBAAgB;0CAAC;AAIzB;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,sBAAsB,CAAC;8BACrB,sBAAsB;gDAAC;AAIrC;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,qBAAqB,CAAC;8BACrB,qBAAqB;+CAAC;AAGrC,6CAA6C;AAChC,QAAA,kBAAkB,GAAG;IAChC,eAAe,EAAE,KAAK;IACtB,cAAc,EAAE,CAAC;IACjB,SAAS,EAAE,IAAI;IACf,iBAAiB,EAAE,CAAC;IACpB,qBAAqB,EAAE,KAAK;IAC5B,2BAA2B,EAAE,KAAK;CAC1B,CAAC;AAEX,8CAA8C;AACjC,QAAA,WAAW,GAAG;IACzB,OAAO,EAAE;QACP,OAAO,EAAE,oBAAoB;QAC7B,GAAG,EAAE,gBAAgB;QACrB,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,oBAAoB;KAC9B;IACD,KAAK,EAAE;QACL,QAAQ,EAAE,2BAA2B;QACrC,KAAK,EAAE,wBAAwB;QAC/B,kBAAkB,EAAE,+BAA+B;QACnD,SAAS,EAAE,sBAAsB;KAClC;IACD,eAAe,EAAE;QACf,OAAO,EAAE,oCAAoC;QAC7C,SAAS,EAAE,sCAAsC;QACjD,OAAO,EAAE,oCAAoC;QAC7C,aAAa,EAAE,0CAA0C;QACzD,iBAAiB,EAAE,8BAA8B;KAClD;IACD,KAAK,EAAE;QACL,OAAO,EAAE,kBAAkB;QAC3B,GAAG,EAAE,cAAc;QACnB,SAAS,EAAE,oBAAoB;QAC/B,UAAU,EAAE,qBAAqB;KAClC;IACD,UAAU,EAAE;QACV,OAAO,EAAE,uBAAuB;QAChC,mBAAmB,EAAE,wBAAwB;QAC7C,WAAW,EAAE,gBAAgB;KAC9B;IACD,MAAM,EAAE;QACN,mBAAmB,EAAE,wBAAwB;QAC7C,qBAAqB,EAAE,0BAA0B;QACjD,wBAAwB,EAAE,6BAA6B;QACvD,YAAY,EAAE,iBAAiB;QAC/B,cAAc,EAAE,mBAAmB;QACnC,iBAAiB,EAAE,sBAAsB;QACzC,aAAa,EAAE,kBAAkB;QACjC,eAAe,EAAE,oBAAoB;QACrC,kBAAkB,EAAE,uBAAuB;KAC5C;IACD,KAAK,EAAE;QACL,OAAO,EAAE,kBAAkB;QAC3B,IAAI,EAAE,eAAe;QACrB,WAAW,EAAE,sBAAsB;QACnC,WAAW,EAAE,sBAAsB;QACnC,WAAW,EAAE,sBAAsB;KACpC;IACD,YAAY,EAAE;QACZ,OAAO,EAAE,yBAAyB;QAClC,QAAQ,EAAE,0BAA0B;QACpC,OAAO,EAAE,yBAAyB;QAClC,QAAQ,EAAE,0BAA0B;KACrC;IACD,UAAU,EAAE;QACV,OAAO,EAAE,uBAAuB;QAChC,gBAAgB,EAAE,qBAAqB;QACvC,YAAY,EAAE,iBAAiB;QAC/B,aAAa,EAAE,kBAAkB;QACjC,iBAAiB,EAAE,sBAAsB;KAC1C;CACO,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\ai.config.ts"], "sourcesContent": ["import { registerAs } from '@nestjs/config';\r\nimport { IsBoolean, Is<PERSON><PERSON>ber, IsString, IsO<PERSON>al, <PERSON>, Max, ValidateNested } from 'class-validator';\r\nimport { Type, Transform } from 'class-transformer';\r\n\r\n// Optimized environment parsing utilities with enhanced type safety\r\nconst parseEnv = {\r\n  boolean: (value: string | undefined, fallback = false): boolean => \r\n    value?.toLowerCase() === 'true' || fallback,\r\n  \r\n  number: (value: string | undefined, fallback: number, radix = 10): number => {\r\n    if (!value) return fallback;\r\n    const parsed = parseInt(value, radix);\r\n    return Number.isNaN(parsed) ? fallback : parsed;\r\n  },\r\n  \r\n  float: (value: string | undefined, fallback: number): number => {\r\n    if (!value) return fallback;\r\n    const parsed = Number.parseFloat(value);\r\n    return Number.isNaN(parsed) ? fallback : parsed;\r\n  },\r\n  \r\n  string: (value: string | undefined, fallback: string): string => value ?? fallback,\r\n} as const;\r\n\r\n// Configuration section interfaces for better type safety\r\ninterface ServiceConfig {\r\n  readonly enabled: boolean;\r\n  readonly url: string;\r\n  readonly apiKey: string;\r\n  readonly timeout: number;\r\n  readonly version: string;\r\n}\r\n\r\ninterface RetryConfig {\r\n  readonly attempts: number;\r\n  readonly delay: number;\r\n  readonly backoffMultiplier: number;\r\n  readonly maxDelay: number;\r\n}\r\n\r\ninterface CircuitBreakerConfig {\r\n  readonly enabled: boolean;\r\n  readonly threshold: number;\r\n  readonly timeout: number;\r\n  readonly resetTimeout: number;\r\n  readonly monitoringPeriod: number;\r\n}\r\n\r\ninterface CacheConfig {\r\n  readonly enabled: boolean;\r\n  readonly ttl: number;\r\n  readonly maxItems: number;\r\n  readonly keyPrefix: string;\r\n}\r\n\r\ninterface RateLimitConfig {\r\n  readonly enabled: boolean;\r\n  readonly requestsPerMinute: number;\r\n  readonly burstLimit: number;\r\n}\r\n\r\ninterface ModelConfig {\r\n  readonly name: string;\r\n  readonly timeout: number;\r\n  readonly maxTokens?: number;\r\n  readonly batchSize?: number;\r\n}\r\n\r\ninterface ModelsConfig {\r\n  readonly vulnerabilityAnalysis: ModelConfig;\r\n  readonly threatDetection: ModelConfig;\r\n  readonly anomalyDetection: ModelConfig;\r\n}\r\n\r\ninterface QueueConfig {\r\n  readonly enabled: boolean;\r\n  readonly name: string;\r\n  readonly concurrency: number;\r\n  readonly maxRetries: number;\r\n  readonly retryDelay: number;\r\n}\r\n\r\ninterface HealthCheckConfig {\r\n  readonly enabled: boolean;\r\n  readonly interval: number;\r\n  readonly timeout: number;\r\n  readonly endpoint: string;\r\n}\r\n\r\ninterface MonitoringConfig {\r\n  readonly enabled: boolean;\r\n  readonly metricsInterval: number;\r\n  readonly logRequests: boolean;\r\n  readonly logResponses: boolean;\r\n  readonly trackPerformance: boolean;\r\n}\r\n\r\n// Main AI configuration interface\r\nexport interface AIConfig {\r\n  readonly service: ServiceConfig;\r\n  readonly retry: RetryConfig;\r\n  readonly circuitBreaker: CircuitBreakerConfig;\r\n  readonly cache: CacheConfig;\r\n  readonly rateLimit: RateLimitConfig;\r\n  readonly models: ModelsConfig;\r\n  readonly queue: QueueConfig;\r\n  readonly healthCheck: HealthCheckConfig;\r\n  readonly monitoring: MonitoringConfig;\r\n}\r\n\r\n// Configuration factory with optimized environment parsing\r\nconst createAIConfig = (): AIConfig => {\r\n  const env = process.env;\r\n\r\n  const service: ServiceConfig = {\r\n    enabled: parseEnv.boolean(env['AI_SERVICE_ENABLED']),\r\n    url: parseEnv.string(env['AI_SERVICE_URL'], 'http://localhost:8000'),\r\n    apiKey: parseEnv.string(env['AI_SERVICE_API_KEY'], 'your-ai-service-api-key'),\r\n    timeout: parseEnv.number(env['AI_SERVICE_TIMEOUT'], 30000),\r\n    version: parseEnv.string(env['AI_SERVICE_VERSION'], 'v1'),\r\n  };\r\n\r\n  const retry: RetryConfig = {\r\n    attempts: parseEnv.number(env['AI_SERVICE_RETRY_ATTEMPTS'], 3),\r\n    delay: parseEnv.number(env['AI_SERVICE_RETRY_DELAY'], 1000),\r\n    backoffMultiplier: parseEnv.float(env['AI_SERVICE_BACKOFF_MULTIPLIER'], 2),\r\n    maxDelay: parseEnv.number(env['AI_SERVICE_MAX_DELAY'], 10000),\r\n  };\r\n\r\n  const circuitBreaker: CircuitBreakerConfig = {\r\n    enabled: parseEnv.boolean(env['AI_SERVICE_CIRCUIT_BREAKER_ENABLED']),\r\n    threshold: parseEnv.number(env['AI_SERVICE_CIRCUIT_BREAKER_THRESHOLD'], 5),\r\n    timeout: parseEnv.number(env['AI_SERVICE_CIRCUIT_BREAKER_TIMEOUT'], 60000),\r\n    resetTimeout: parseEnv.number(env['AI_SERVICE_CIRCUIT_BREAKER_RESET_TIMEOUT'], 30000),\r\n    monitoringPeriod: parseEnv.number(env['AI_SERVICE_MONITORING_PERIOD'], 10000),\r\n  };\r\n\r\n  const cache: CacheConfig = {\r\n    enabled: parseEnv.boolean(env['AI_CACHE_ENABLED']),\r\n    ttl: parseEnv.number(env['AI_CACHE_TTL'], 3600),\r\n    maxItems: parseEnv.number(env['AI_CACHE_MAX_ITEMS'], 1000),\r\n    keyPrefix: parseEnv.string(env['AI_CACHE_KEY_PREFIX'], 'ai:'),\r\n  };\r\n\r\n  const rateLimit: RateLimitConfig = {\r\n    enabled: parseEnv.boolean(env['AI_RATE_LIMIT_ENABLED']),\r\n    requestsPerMinute: parseEnv.number(env['AI_REQUESTS_PER_MINUTE'], 60),\r\n    burstLimit: parseEnv.number(env['AI_BURST_LIMIT'], 10),\r\n  };\r\n\r\n  const models: ModelsConfig = {\r\n    vulnerabilityAnalysis: {\r\n      name: parseEnv.string(env['AI_VULNERABILITY_MODEL'], 'vulnerability-analyzer-v1'),\r\n      timeout: parseEnv.number(env['AI_VULNERABILITY_TIMEOUT'], 15000),\r\n      maxTokens: parseEnv.number(env['AI_VULNERABILITY_MAX_TOKENS'], 2048),\r\n    },\r\n    threatDetection: {\r\n      name: parseEnv.string(env['AI_THREAT_MODEL'], 'threat-detector-v1'),\r\n      timeout: parseEnv.number(env['AI_THREAT_TIMEOUT'], 10000),\r\n      maxTokens: parseEnv.number(env['AI_THREAT_MAX_TOKENS'], 1024),\r\n    },\r\n    anomalyDetection: {\r\n      name: parseEnv.string(env['AI_ANOMALY_MODEL'], 'anomaly-detector-v1'),\r\n      timeout: parseEnv.number(env['AI_ANOMALY_TIMEOUT'], 20000),\r\n      batchSize: parseEnv.number(env['AI_ANOMALY_BATCH_SIZE'], 100),\r\n    },\r\n  };\r\n\r\n  const queue: QueueConfig = {\r\n    enabled: parseEnv.boolean(env['AI_QUEUE_ENABLED']),\r\n    name: parseEnv.string(env['AI_QUEUE_NAME'], 'ai-processing'),\r\n    concurrency: parseEnv.number(env['AI_QUEUE_CONCURRENCY'], 5),\r\n    maxRetries: parseEnv.number(env['AI_QUEUE_MAX_RETRIES'], 3),\r\n    retryDelay: parseEnv.number(env['AI_QUEUE_RETRY_DELAY'], 5000),\r\n  };\r\n\r\n  const healthCheck: HealthCheckConfig = {\r\n    enabled: parseEnv.boolean(env['AI_HEALTH_CHECK_ENABLED']),\r\n    interval: parseEnv.number(env['AI_HEALTH_CHECK_INTERVAL'], 30000),\r\n    timeout: parseEnv.number(env['AI_HEALTH_CHECK_TIMEOUT'], 5000),\r\n    endpoint: parseEnv.string(env['AI_HEALTH_CHECK_ENDPOINT'], '/health'),\r\n  };\r\n\r\n  const monitoring: MonitoringConfig = {\r\n    enabled: parseEnv.boolean(env['AI_MONITORING_ENABLED']),\r\n    metricsInterval: parseEnv.number(env['AI_METRICS_INTERVAL'], 60000),\r\n    logRequests: parseEnv.boolean(env['AI_LOG_REQUESTS']),\r\n    logResponses: parseEnv.boolean(env['AI_LOG_RESPONSES']),\r\n    trackPerformance: parseEnv.boolean(env['AI_TRACK_PERFORMANCE']),\r\n  };\r\n\r\n  return {\r\n    service,\r\n    retry,\r\n    circuitBreaker,\r\n    cache,\r\n    rateLimit,\r\n    models,\r\n    queue,\r\n    healthCheck,\r\n    monitoring,\r\n  };\r\n};\r\n\r\n/**\r\n * AI Service Configuration with optimized environment parsing and validation.\r\n * Provides enterprise-grade settings for AI service integration with enhanced type safety.\r\n */\r\nexport const aiConfig = registerAs('ai', createAIConfig);\r\n\r\n// Validation DTOs with enhanced decorators and better organization\r\nexport class AIServiceConfigDto {\r\n  @IsBoolean()\r\n  @Transform(({ value }) => parseEnv.boolean(value))\r\n  enabled!: boolean;\r\n\r\n  @IsString()\r\n  url!: string;\r\n\r\n  @IsString()\r\n  apiKey!: string;\r\n\r\n  @IsNumber()\r\n  @Min(1000)\r\n  @Max(300000)\r\n  timeout!: number;\r\n\r\n  @IsString()\r\n  version!: string;\r\n}\r\n\r\nexport class AIRetryConfigDto {\r\n  @IsNumber()\r\n  @Min(1)\r\n  @Max(10)\r\n  attempts!: number;\r\n\r\n  @IsNumber()\r\n  @Min(100)\r\n  delay!: number;\r\n\r\n  @IsNumber()\r\n  @Min(1)\r\n  backoffMultiplier!: number;\r\n\r\n  @IsNumber()\r\n  @Min(1000)\r\n  maxDelay!: number;\r\n}\r\n\r\nexport class AICircuitBreakerConfigDto {\r\n  @IsBoolean()\r\n  @Transform(({ value }) => parseEnv.boolean(value))\r\n  enabled!: boolean;\r\n\r\n  @IsNumber()\r\n  @Min(1)\r\n  threshold!: number;\r\n\r\n  @IsNumber()\r\n  @Min(1000)\r\n  timeout!: number;\r\n\r\n  @IsNumber()\r\n  @Min(1000)\r\n  resetTimeout!: number;\r\n\r\n  @IsNumber()\r\n  @Min(1000)\r\n  monitoringPeriod!: number;\r\n}\r\n\r\nexport class AICacheConfigDto {\r\n  @IsBoolean()\r\n  @Transform(({ value }) => parseEnv.boolean(value))\r\n  enabled!: boolean;\r\n\r\n  @IsNumber()\r\n  @Min(60)\r\n  ttl!: number;\r\n\r\n  @IsNumber()\r\n  @Min(100)\r\n  maxItems!: number;\r\n\r\n  @IsString()\r\n  keyPrefix!: string;\r\n}\r\n\r\nexport class AIRateLimitConfigDto {\r\n  @IsBoolean()\r\n  @Transform(({ value }) => parseEnv.boolean(value))\r\n  enabled!: boolean;\r\n\r\n  @IsNumber()\r\n  @Min(1)\r\n  requestsPerMinute!: number;\r\n\r\n  @IsNumber()\r\n  @Min(1)\r\n  burstLimit!: number;\r\n}\r\n\r\nexport class AIModelConfigDto {\r\n  @IsString()\r\n  name!: string;\r\n\r\n  @IsNumber()\r\n  @Min(1000)\r\n  timeout!: number;\r\n\r\n  @IsOptional()\r\n  @IsNumber()\r\n  @Min(256)\r\n  maxTokens?: number;\r\n\r\n  @IsOptional()\r\n  @IsNumber()\r\n  @Min(1)\r\n  batchSize?: number;\r\n}\r\n\r\nexport class AIModelsConfigDto {\r\n  @ValidateNested()\r\n  @Type(() => AIModelConfigDto)\r\n  vulnerabilityAnalysis!: AIModelConfigDto;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AIModelConfigDto)\r\n  threatDetection!: AIModelConfigDto;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AIModelConfigDto)\r\n  anomalyDetection!: AIModelConfigDto;\r\n}\r\n\r\nexport class AIQueueConfigDto {\r\n  @IsBoolean()\r\n  @Transform(({ value }) => parseEnv.boolean(value))\r\n  enabled!: boolean;\r\n\r\n  @IsString()\r\n  name!: string;\r\n\r\n  @IsNumber()\r\n  @Min(1)\r\n  concurrency!: number;\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  maxRetries!: number;\r\n\r\n  @IsNumber()\r\n  @Min(1000)\r\n  retryDelay!: number;\r\n}\r\n\r\nexport class AIHealthCheckConfigDto {\r\n  @IsBoolean()\r\n  @Transform(({ value }) => parseEnv.boolean(value))\r\n  enabled!: boolean;\r\n\r\n  @IsNumber()\r\n  @Min(5000)\r\n  interval!: number;\r\n\r\n  @IsNumber()\r\n  @Min(1000)\r\n  timeout!: number;\r\n\r\n  @IsString()\r\n  endpoint!: string;\r\n}\r\n\r\nexport class AIMonitoringConfigDto {\r\n  @IsBoolean()\r\n  @Transform(({ value }) => parseEnv.boolean(value))\r\n  enabled!: boolean;\r\n\r\n  @IsNumber()\r\n  @Min(10000)\r\n  metricsInterval!: number;\r\n\r\n  @IsBoolean()\r\n  @Transform(({ value }) => parseEnv.boolean(value))\r\n  logRequests!: boolean;\r\n\r\n  @IsBoolean()\r\n  @Transform(({ value }) => parseEnv.boolean(value))\r\n  logResponses!: boolean;\r\n\r\n  @IsBoolean()\r\n  @Transform(({ value }) => parseEnv.boolean(value))\r\n  trackPerformance!: boolean;\r\n}\r\n\r\n/**\r\n * Complete AI Configuration DTO with nested validation\r\n */\r\nexport class AIConfigDto {\r\n  @ValidateNested()\r\n  @Type(() => AIServiceConfigDto)\r\n  service!: AIServiceConfigDto;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AIRetryConfigDto)\r\n  retry!: AIRetryConfigDto;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AICircuitBreakerConfigDto)\r\n  circuitBreaker!: AICircuitBreakerConfigDto;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AICacheConfigDto)\r\n  cache!: AICacheConfigDto;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AIRateLimitConfigDto)\r\n  rateLimit!: AIRateLimitConfigDto;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AIModelsConfigDto)\r\n  models!: AIModelsConfigDto;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AIQueueConfigDto)\r\n  queue!: AIQueueConfigDto;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AIHealthCheckConfigDto)\r\n  healthCheck!: AIHealthCheckConfigDto;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AIMonitoringConfigDto)\r\n  monitoring!: AIMonitoringConfigDto;\r\n}\r\n\r\n// Configuration constants for easy reference\r\nexport const AI_CONFIG_DEFAULTS = {\r\n  SERVICE_TIMEOUT: 30000,\r\n  RETRY_ATTEMPTS: 3,\r\n  CACHE_TTL: 3600,\r\n  QUEUE_CONCURRENCY: 5,\r\n  HEALTH_CHECK_INTERVAL: 30000,\r\n  MONITORING_METRICS_INTERVAL: 60000,\r\n} as const;\r\n\r\n// Environment variable keys for documentation\r\nexport const AI_ENV_KEYS = {\r\n  SERVICE: {\r\n    ENABLED: 'AI_SERVICE_ENABLED',\r\n    URL: 'AI_SERVICE_URL',\r\n    API_KEY: 'AI_SERVICE_API_KEY',\r\n    TIMEOUT: 'AI_SERVICE_TIMEOUT',\r\n    VERSION: 'AI_SERVICE_VERSION',\r\n  },\r\n  RETRY: {\r\n    ATTEMPTS: 'AI_SERVICE_RETRY_ATTEMPTS',\r\n    DELAY: 'AI_SERVICE_RETRY_DELAY',\r\n    BACKOFF_MULTIPLIER: 'AI_SERVICE_BACKOFF_MULTIPLIER',\r\n    MAX_DELAY: 'AI_SERVICE_MAX_DELAY',\r\n  },\r\n  CIRCUIT_BREAKER: {\r\n    ENABLED: 'AI_SERVICE_CIRCUIT_BREAKER_ENABLED',\r\n    THRESHOLD: 'AI_SERVICE_CIRCUIT_BREAKER_THRESHOLD',\r\n    TIMEOUT: 'AI_SERVICE_CIRCUIT_BREAKER_TIMEOUT',\r\n    RESET_TIMEOUT: 'AI_SERVICE_CIRCUIT_BREAKER_RESET_TIMEOUT',\r\n    MONITORING_PERIOD: 'AI_SERVICE_MONITORING_PERIOD',\r\n  },\r\n  CACHE: {\r\n    ENABLED: 'AI_CACHE_ENABLED',\r\n    TTL: 'AI_CACHE_TTL',\r\n    MAX_ITEMS: 'AI_CACHE_MAX_ITEMS',\r\n    KEY_PREFIX: 'AI_CACHE_KEY_PREFIX',\r\n  },\r\n  RATE_LIMIT: {\r\n    ENABLED: 'AI_RATE_LIMIT_ENABLED',\r\n    REQUESTS_PER_MINUTE: 'AI_REQUESTS_PER_MINUTE',\r\n    BURST_LIMIT: 'AI_BURST_LIMIT',\r\n  },\r\n  MODELS: {\r\n    VULNERABILITY_MODEL: 'AI_VULNERABILITY_MODEL',\r\n    VULNERABILITY_TIMEOUT: 'AI_VULNERABILITY_TIMEOUT',\r\n    VULNERABILITY_MAX_TOKENS: 'AI_VULNERABILITY_MAX_TOKENS',\r\n    THREAT_MODEL: 'AI_THREAT_MODEL',\r\n    THREAT_TIMEOUT: 'AI_THREAT_TIMEOUT',\r\n    THREAT_MAX_TOKENS: 'AI_THREAT_MAX_TOKENS',\r\n    ANOMALY_MODEL: 'AI_ANOMALY_MODEL',\r\n    ANOMALY_TIMEOUT: 'AI_ANOMALY_TIMEOUT',\r\n    ANOMALY_BATCH_SIZE: 'AI_ANOMALY_BATCH_SIZE',\r\n  },\r\n  QUEUE: {\r\n    ENABLED: 'AI_QUEUE_ENABLED',\r\n    NAME: 'AI_QUEUE_NAME',\r\n    CONCURRENCY: 'AI_QUEUE_CONCURRENCY',\r\n    MAX_RETRIES: 'AI_QUEUE_MAX_RETRIES',\r\n    RETRY_DELAY: 'AI_QUEUE_RETRY_DELAY',\r\n  },\r\n  HEALTH_CHECK: {\r\n    ENABLED: 'AI_HEALTH_CHECK_ENABLED',\r\n    INTERVAL: 'AI_HEALTH_CHECK_INTERVAL',\r\n    TIMEOUT: 'AI_HEALTH_CHECK_TIMEOUT',\r\n    ENDPOINT: 'AI_HEALTH_CHECK_ENDPOINT',\r\n  },\r\n  MONITORING: {\r\n    ENABLED: 'AI_MONITORING_ENABLED',\r\n    METRICS_INTERVAL: 'AI_METRICS_INTERVAL',\r\n    LOG_REQUESTS: 'AI_LOG_REQUESTS',\r\n    LOG_RESPONSES: 'AI_LOG_RESPONSES',\r\n    TRACK_PERFORMANCE: 'AI_TRACK_PERFORMANCE',\r\n  },\r\n} as const;"], "version": 3}