{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\ai-model-created.domain-event.ts", "mappings": ";;;AAAA,0FAAqF;AAIrF;;;;;;GAMG;AACH,MAAa,mBAAoB,SAAQ,mCAAe;IACtD,YACkB,OAAuB,EACvB,SAAiB,EACjB,QAAoB,EACpB,SAAoB,EACpC,OAAwB,EACxB,UAAiB;QAEjB,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAPX,YAAO,GAAP,OAAO,CAAgB;QACvB,cAAS,GAAT,SAAS,CAAQ;QACjB,aAAQ,GAAR,QAAQ,CAAY;QACpB,cAAS,GAAT,SAAS,CAAW;IAKtC,CAAC;IAEM,YAAY;QACjB,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEM,eAAe;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,YAAY;QACjB,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAChC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;CACF;AA5BD,kDA4BC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\ai-model-created.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent } from '../../../../shared-kernel/domain/base-domain-event';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { AIProvider, ModelType } from '../entities/ai-model.entity';\r\n\r\n/**\r\n * AI Model Created Domain Event\r\n * \r\n * Published when a new AI model is created in the system.\r\n * This event can trigger various downstream processes such as\r\n * model registration, health check initialization, and monitoring setup.\r\n */\r\nexport class AIModelCreatedEvent extends BaseDomainEvent {\r\n  constructor(\r\n    public readonly modelId: UniqueEntityId,\r\n    public readonly modelName: string,\r\n    public readonly provider: AIProvider,\r\n    public readonly modelType: ModelType,\r\n    eventId?: UniqueEntityId,\r\n    occurredOn?: Date\r\n  ) {\r\n    super(eventId, occurredOn);\r\n  }\r\n\r\n  public getEventName(): string {\r\n    return 'AIModelCreated';\r\n  }\r\n\r\n  public getEventVersion(): string {\r\n    return '1.0';\r\n  }\r\n\r\n  public getEventData(): Record<string, any> {\r\n    return {\r\n      modelId: this.modelId.toString(),\r\n      modelName: this.modelName,\r\n      provider: this.provider,\r\n      modelType: this.modelType,\r\n    };\r\n  }\r\n}"], "version": 3}