{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\__tests__\\security-policy.spec.ts", "mappings": ";;AAAA,wDAA4J;AAE5J,8GAA6F;AAC7F,0GAAyF;AACzF,8GAA8F;AAC9F,uGAAmG;AAEnG,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,IAAI,QAAkB,CAAC;IACvB,IAAI,MAAc,CAAC;IACnB,IAAI,SAAuB,CAAC;IAC5B,IAAI,YAAqC,CAAC;IAE1C,UAAU,CAAC,GAAG,EAAE;QACd,QAAQ,GAAG,iCAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACzC,MAAM,GAAG,6BAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAEnC,SAAS,GAAG;YACV,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,aAAa;YAC1B,SAAS,EAAE,uEAAuE;YAClF,MAAM,EAAE;gBACN,IAAI,EAAE,oCAAkB,CAAC,WAAW;gBACpC,UAAU,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE;gBACjC,WAAW,EAAE,IAAI;gBACjB,gBAAgB,EAAE,KAAK;aACxB;YACD,QAAQ,EAAE,sCAAoB,CAAC,IAAI;YACnC,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,EAAE;SACb,CAAC;QAEF,YAAY,GAAG;YACb,SAAS,EAAE;gBACT,IAAI,EAAE,cAAc;gBACpB,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,eAAe;aAC1B;YACD,WAAW,EAAE;gBACX,MAAM;gBACN,QAAQ;gBACR,KAAK,EAAE,CAAC,MAAM,CAAC;gBACf,WAAW,EAAE,CAAC,MAAM,CAAC;aACtB;YACD,aAAa,EAAE;gBACb,SAAS,EAAE,kCAAS,CAAC,GAAG,EAAE;gBAC1B,QAAQ,EAAE,eAAe;gBACzB,SAAS,EAAE,aAAa;aACzB;SACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,MAAM,GAAG,gCAAc,CAAC,MAAM,CAAC;gBACnC,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,eAAe;gBAC5B,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,KAAK,EAAE,CAAC,SAAS,CAAC;gBAClB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,GAAG,EAAE;gBACV,gCAAc,CAAC,MAAM,CAAC;oBACpB,IAAI,EAAE,EAAE;oBACR,WAAW,EAAE,eAAe;oBAC5B,OAAO,EAAE,OAAO;oBAChB,QAAQ;oBACR,KAAK,EAAE,EAAE;oBACT,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,MAAM;iBAClB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,CAAC,GAAG,EAAE;gBACV,gCAAc,CAAC,MAAM,CAAC;oBACpB,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,EAAE;oBACf,OAAO,EAAE,OAAO;oBAChB,QAAQ;oBACR,KAAK,EAAE,EAAE;oBACT,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,MAAM;iBAClB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,WAAW,GAAG,EAAE,GAAG,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC;YAEhE,MAAM,CAAC,GAAG,EAAE;gBACV,gCAAc,CAAC,MAAM,CAAC;oBACpB,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,eAAe;oBAC5B,OAAO,EAAE,OAAO;oBAChB,QAAQ;oBACR,KAAK,EAAE,CAAC,WAAW,CAAC;oBACpB,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,MAAM;iBAClB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,gCAAc,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,eAAe;gBAC5B,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,KAAK,EAAE,CAAC,SAAS,CAAC;gBAClB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,wBAAwB,GAAG;gBAC/B,GAAG,YAAY;gBACf,SAAS,EAAE,EAAE,GAAG,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,eAAe,EAAE;aAChE,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;YAE1D,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvB,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,YAAY,GAAG,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;YACpE,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAErC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB;YACzD,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,gBAAgB,GAAG;gBACvB,GAAG,SAAS;gBACZ,EAAE,EAAE,WAAW;gBACf,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,sCAAoB,CAAC,IAAI;aACpC,CAAC;YAEF,MAAM,eAAe,GAAG;gBACtB,GAAG,SAAS;gBACZ,EAAE,EAAE,UAAU;gBACd,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,sCAAoB,CAAC,GAAG;aACnC,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAExC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,yBAAyB;YACtE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB;YAC5D,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,kBAAkB;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,YAAY,GAAG;gBACnB,GAAG,SAAS;gBACZ,EAAE,EAAE,eAAe;gBACnB,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,sCAAoB,CAAC,QAAQ;aACxC,CAAC;YAEF,MAAM,UAAU,GAAG;gBACjB,GAAG,SAAS;gBACZ,EAAE,EAAE,aAAa;gBACjB,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,sCAAoB,CAAC,MAAM;aACtC,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAEnC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9C,0CAA0C;YAC1C,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,gCAAc,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,eAAe;gBAC5B,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAiB;gBAC5B,GAAG,SAAS;gBACZ,EAAE,EAAE,UAAU;gBACd,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;oBACxB,GAAG,EAAE;wBACH,EAAE,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE;wBACtE,EAAE,KAAK,EAAE,oBAAoB,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC,EAAE;qBACpE;iBACF,CAAC;aACH,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAChC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,MAAM,GAAiB;gBAC3B,GAAG,SAAS;gBACZ,EAAE,EAAE,SAAS;gBACb,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;oBACxB,EAAE,EAAE;wBACF,EAAE,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE;wBACtE,EAAE,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,gBAAgB,EAAE;qBACzE;iBACF,CAAC;aACH,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC/B,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAiB;gBAC5B,GAAG,SAAS;gBACZ,EAAE,EAAE,UAAU;gBACd,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;oBACxB,GAAG,EAAE;wBACH,KAAK,EAAE,gBAAgB;wBACvB,QAAQ,EAAE,QAAQ;wBAClB,KAAK,EAAE,eAAe;qBACvB;iBACF,CAAC;aACH,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAChC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,UAAU,GAAiB;gBAC/B,GAAG,SAAS;gBACZ,EAAE,EAAE,aAAa;gBACjB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;oBACxB,GAAG,EAAE;wBACH;4BACE,EAAE,EAAE;gCACF,EAAE,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE;gCACtE,EAAE,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,gBAAgB,EAAE;6BACzE;yBACF;wBACD,EAAE,KAAK,EAAE,oBAAoB,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC,EAAE;qBACpE;iBACF,CAAC;aACH,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YACnC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,SAAS,GAAG;gBAChB,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACvF,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACtF,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACpF,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACrG,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,2BAA2B,EAAE,QAAQ,EAAE,IAAI,EAAE;aACvG,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE;gBAChE,MAAM,IAAI,GAAiB;oBACzB,GAAG,SAAS;oBACZ,EAAE,EAAE,aAAa,KAAK,EAAE;oBACxB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;iBACtD,CAAC;gBAEF,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC7B,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gBAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,aAAa,KAAK,EAAE,CAAC,CAAC;gBAEpE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,gCAAc,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,eAAe;gBAC5B,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,KAAK,EAAE,CAAC,SAAS,CAAC;gBAClB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,MAAM,OAAO,GAAG,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAEhC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,EAAE,MAAM,CAAC,CAAC;YAE9D,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;YAC9D,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YAC9B,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAEpC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,gCAAc,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,eAAe;gBAC5B,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,KAAK,EAAE,CAAC,SAAS,CAAC;gBAClB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YAC9B,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACtB,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,gCAAc,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,eAAe;gBAC5B,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,KAAK,EAAE,CAAC,SAAS,CAAC;gBAClB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9B,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,uCAAqB,CAAC,OAAO,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,0BAA0B;YAC1B,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC9B,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9B,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,gCAAc,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,eAAe;gBAC5B,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,YAAY,GAAG,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,sCAAoB,CAAC,QAAQ,EAAE,CAAC;YAC/F,MAAM,OAAO,GAAG,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,sCAAoB,CAAC,GAAG,EAAE,CAAC;YAEhF,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAEhC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC9C,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC;YAClE,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC;YAExD,MAAM,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,eAAe,CAAC,SAAS,EAAE,UAAU,IAAI,CAAC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAElC,MAAM,iBAAiB,GAAG;gBACxB,GAAG,YAAY;gBACf,aAAa,EAAE;oBACb,WAAW,EAAE,MAAM;oBACnB,UAAU,EAAE,CAAC,cAAc,CAAC;oBAC5B,UAAU,EAAE,GAAG;iBAChB;aACF,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,gCAAc,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,eAAe;gBAC5B,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,WAAW,GAAG,EAAE,GAAG,SAAS,EAAE,SAAS,EAAE,mEAAmE,EAAE,CAAC;YACrH,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAEpC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,WAAW,GAAG,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,0BAA0B,EAAE,CAAC;YAC3F,MAAM,UAAU,GAAG,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;YAEjD,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAEnC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvE,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\__tests__\\security-policy.spec.ts"], "sourcesContent": ["import { SecurityPolicy, SecurityRule, SecurityActionType, SecurityRuleSeverity, PolicyEvaluationContext, PolicyExecutionStatus } from '../security-policy';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { TenantId } from '../../../../../shared-kernel/value-objects/tenant-id.value-object';\r\nimport { UserId } from '../../../../../shared-kernel/value-objects/user-id.value-object';\r\nimport { Timestamp } from '../../../../../shared-kernel/value-objects/timestamp.value-object';\r\nimport { ValidationException } from '../../../../../shared-kernel/exceptions/validation.exception';\r\n\r\ndescribe('SecurityPolicy', () => {\r\n  let tenantId: TenantId;\r\n  let userId: UserId;\r\n  let validRule: SecurityRule;\r\n  let validContext: PolicyEvaluationContext;\r\n\r\n  beforeEach(() => {\r\n    tenantId = TenantId.create('tenant-123');\r\n    userId = UserId.create('user-123');\r\n    \r\n    validRule = {\r\n      id: 'rule-1',\r\n      name: 'Test Rule',\r\n      description: 'A test rule',\r\n      condition: '{\"field\":\"eventData.type\",\"operator\":\"equals\",\"value\":\"login_failed\"}',\r\n      action: {\r\n        type: SecurityActionType.ALERT_ADMIN,\r\n        parameters: { message: 'Alert!' },\r\n        autoExecute: true,\r\n        requiresApproval: false\r\n      },\r\n      severity: SecurityRuleSeverity.HIGH,\r\n      enabled: true,\r\n      priority: 80\r\n    };\r\n\r\n    validContext = {\r\n      eventData: {\r\n        type: 'login_failed',\r\n        attempts: 3,\r\n        sourceIp: '*************'\r\n      },\r\n      userContext: {\r\n        userId,\r\n        tenantId,\r\n        roles: ['user'],\r\n        permissions: ['read']\r\n      },\r\n      systemContext: {\r\n        timestamp: Timestamp.now(),\r\n        sourceIp: '*************',\r\n        userAgent: 'Mozilla/5.0'\r\n      }\r\n    };\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create a valid security policy', () => {\r\n      const policy = SecurityPolicy.create({\r\n        name: 'Test Policy',\r\n        description: 'A test policy',\r\n        version: '1.0.0',\r\n        tenantId,\r\n        rules: [validRule],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n\r\n      expect(policy).toBeDefined();\r\n      expect(policy.name).toBe('Test Policy');\r\n      expect(policy.description).toBe('A test policy');\r\n      expect(policy.version).toBe('1.0.0');\r\n      expect(policy.tenantId).toBe(tenantId);\r\n      expect(policy.enabled).toBe(true);\r\n      expect(policy.rules).toHaveLength(1);\r\n      expect(policy.createdBy).toBe(userId);\r\n      expect(policy.lastModifiedBy).toBe(userId);\r\n    });\r\n\r\n    it('should throw validation exception for missing name', () => {\r\n      expect(() => {\r\n        SecurityPolicy.create({\r\n          name: '',\r\n          description: 'A test policy',\r\n          version: '1.0.0',\r\n          tenantId,\r\n          rules: [],\r\n          enabled: true,\r\n          createdBy: userId\r\n        });\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should throw validation exception for missing description', () => {\r\n      expect(() => {\r\n        SecurityPolicy.create({\r\n          name: 'Test Policy',\r\n          description: '',\r\n          version: '1.0.0',\r\n          tenantId,\r\n          rules: [],\r\n          enabled: true,\r\n          createdBy: userId\r\n        });\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should throw validation exception for invalid rule', () => {\r\n      const invalidRule = { ...validRule, condition: 'invalid json' };\r\n      \r\n      expect(() => {\r\n        SecurityPolicy.create({\r\n          name: 'Test Policy',\r\n          description: 'A test policy',\r\n          version: '1.0.0',\r\n          tenantId,\r\n          rules: [invalidRule],\r\n          enabled: true,\r\n          createdBy: userId\r\n        });\r\n      }).toThrow(ValidationException);\r\n    });\r\n  });\r\n\r\n  describe('rule evaluation', () => {\r\n    let policy: SecurityPolicy;\r\n\r\n    beforeEach(() => {\r\n      policy = SecurityPolicy.create({\r\n        name: 'Test Policy',\r\n        description: 'A test policy',\r\n        version: '1.0.0',\r\n        tenantId,\r\n        rules: [validRule],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should evaluate rule and return match when condition is met', () => {\r\n      const results = policy.evaluate(validContext);\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].matched).toBe(true);\r\n      expect(results[0].ruleId).toBe('rule-1');\r\n      expect(results[0].action).toBeDefined();\r\n      expect(results[0].confidence).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should evaluate rule and return no match when condition is not met', () => {\r\n      const contextWithDifferentType = {\r\n        ...validContext,\r\n        eventData: { ...validContext.eventData, type: 'login_success' }\r\n      };\r\n\r\n      const results = policy.evaluate(contextWithDifferentType);\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].matched).toBe(false);\r\n      expect(results[0].action).toBeUndefined();\r\n      expect(results[0].confidence).toBe(0);\r\n    });\r\n\r\n    it('should not evaluate rules when policy is disabled', () => {\r\n      policy.disable(userId);\r\n      const results = policy.evaluate(validContext);\r\n\r\n      expect(results).toHaveLength(0);\r\n    });\r\n\r\n    it('should not evaluate disabled rules', () => {\r\n      const disabledRule = { ...validRule, id: 'rule-2', enabled: false };\r\n      policy.addRule(disabledRule, userId);\r\n\r\n      const results = policy.evaluate(validContext);\r\n\r\n      expect(results).toHaveLength(1); // Only the enabled rule\r\n      expect(results[0].ruleId).toBe('rule-1');\r\n    });\r\n\r\n    it('should evaluate rules in priority order', () => {\r\n      const highPriorityRule = {\r\n        ...validRule,\r\n        id: 'rule-high',\r\n        priority: 100,\r\n        severity: SecurityRuleSeverity.HIGH\r\n      };\r\n      \r\n      const lowPriorityRule = {\r\n        ...validRule,\r\n        id: 'rule-low',\r\n        priority: 10,\r\n        severity: SecurityRuleSeverity.LOW\r\n      };\r\n\r\n      policy.addRule(highPriorityRule, userId);\r\n      policy.addRule(lowPriorityRule, userId);\r\n\r\n      const results = policy.evaluate(validContext);\r\n\r\n      expect(results).toHaveLength(3);\r\n      expect(results[0].ruleId).toBe('rule-high'); // Highest priority first\r\n      expect(results[1].ruleId).toBe('rule-1'); // Medium priority\r\n      expect(results[2].ruleId).toBe('rule-low'); // Lowest priority\r\n    });\r\n\r\n    it('should stop evaluation after critical rule match', () => {\r\n      const criticalRule = {\r\n        ...validRule,\r\n        id: 'rule-critical',\r\n        priority: 100,\r\n        severity: SecurityRuleSeverity.CRITICAL\r\n      };\r\n\r\n      const normalRule = {\r\n        ...validRule,\r\n        id: 'rule-normal',\r\n        priority: 50,\r\n        severity: SecurityRuleSeverity.MEDIUM\r\n      };\r\n\r\n      policy.addRule(criticalRule, userId);\r\n      policy.addRule(normalRule, userId);\r\n\r\n      const results = policy.evaluate(validContext);\r\n\r\n      // Should stop after critical rule matches\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].ruleId).toBe('rule-critical');\r\n    });\r\n  });\r\n\r\n  describe('complex rule conditions', () => {\r\n    let policy: SecurityPolicy;\r\n\r\n    beforeEach(() => {\r\n      policy = SecurityPolicy.create({\r\n        name: 'Test Policy',\r\n        description: 'A test policy',\r\n        version: '1.0.0',\r\n        tenantId,\r\n        rules: [],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should evaluate AND conditions correctly', () => {\r\n      const andRule: SecurityRule = {\r\n        ...validRule,\r\n        id: 'and-rule',\r\n        condition: JSON.stringify({\r\n          and: [\r\n            { field: 'eventData.type', operator: 'equals', value: 'login_failed' },\r\n            { field: 'eventData.attempts', operator: 'greater_than', value: 2 }\r\n          ]\r\n        })\r\n      };\r\n\r\n      policy.addRule(andRule, userId);\r\n      const results = policy.evaluate(validContext);\r\n\r\n      expect(results[0].matched).toBe(true);\r\n    });\r\n\r\n    it('should evaluate OR conditions correctly', () => {\r\n      const orRule: SecurityRule = {\r\n        ...validRule,\r\n        id: 'or-rule',\r\n        condition: JSON.stringify({\r\n          or: [\r\n            { field: 'eventData.type', operator: 'equals', value: 'login_failed' },\r\n            { field: 'eventData.type', operator: 'equals', value: 'password_reset' }\r\n          ]\r\n        })\r\n      };\r\n\r\n      policy.addRule(orRule, userId);\r\n      const results = policy.evaluate(validContext);\r\n\r\n      expect(results[0].matched).toBe(true);\r\n    });\r\n\r\n    it('should evaluate NOT conditions correctly', () => {\r\n      const notRule: SecurityRule = {\r\n        ...validRule,\r\n        id: 'not-rule',\r\n        condition: JSON.stringify({\r\n          not: {\r\n            field: 'eventData.type',\r\n            operator: 'equals',\r\n            value: 'login_success'\r\n          }\r\n        })\r\n      };\r\n\r\n      policy.addRule(notRule, userId);\r\n      const results = policy.evaluate(validContext);\r\n\r\n      expect(results[0].matched).toBe(true);\r\n    });\r\n\r\n    it('should evaluate nested conditions correctly', () => {\r\n      const nestedRule: SecurityRule = {\r\n        ...validRule,\r\n        id: 'nested-rule',\r\n        condition: JSON.stringify({\r\n          and: [\r\n            {\r\n              or: [\r\n                { field: 'eventData.type', operator: 'equals', value: 'login_failed' },\r\n                { field: 'eventData.type', operator: 'equals', value: 'password_reset' }\r\n              ]\r\n            },\r\n            { field: 'eventData.attempts', operator: 'greater_than', value: 1 }\r\n          ]\r\n        })\r\n      };\r\n\r\n      policy.addRule(nestedRule, userId);\r\n      const results = policy.evaluate(validContext);\r\n\r\n      expect(results[0].matched).toBe(true);\r\n    });\r\n\r\n    it('should handle various field operators', () => {\r\n      const testCases = [\r\n        { operator: 'contains', field: 'eventData.sourceIp', value: '192.168', expected: true },\r\n        { operator: 'starts_with', field: 'eventData.sourceIp', value: '192', expected: true },\r\n        { operator: 'ends_with', field: 'eventData.sourceIp', value: '100', expected: true },\r\n        { operator: 'in', field: 'eventData.type', value: ['login_failed', 'login_success'], expected: true },\r\n        { operator: 'regex', field: 'eventData.sourceIp', value: '^192\\\\.168\\\\.\\\\d+\\\\.\\\\d+$', expected: true }\r\n      ];\r\n\r\n      testCases.forEach(({ operator, field, value, expected }, index) => {\r\n        const rule: SecurityRule = {\r\n          ...validRule,\r\n          id: `test-rule-${index}`,\r\n          condition: JSON.stringify({ field, operator, value })\r\n        };\r\n\r\n        policy.addRule(rule, userId);\r\n        const results = policy.evaluate(validContext);\r\n        const result = results.find(r => r.ruleId === `test-rule-${index}`);\r\n\r\n        expect(result?.matched).toBe(expected);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('rule management', () => {\r\n    let policy: SecurityPolicy;\r\n\r\n    beforeEach(() => {\r\n      policy = SecurityPolicy.create({\r\n        name: 'Test Policy',\r\n        description: 'A test policy',\r\n        version: '1.0.0',\r\n        tenantId,\r\n        rules: [validRule],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should add a new rule', () => {\r\n      const newRule = { ...validRule, id: 'rule-2', name: 'New Rule' };\r\n      policy.addRule(newRule, userId);\r\n\r\n      expect(policy.rules).toHaveLength(2);\r\n      expect(policy.rules.find(r => r.id === 'rule-2')).toBeDefined();\r\n    });\r\n\r\n    it('should throw error when adding duplicate rule ID', () => {\r\n      expect(() => {\r\n        policy.addRule(validRule, userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should update an existing rule', () => {\r\n      policy.updateRule('rule-1', { name: 'Updated Rule' }, userId);\r\n\r\n      const updatedRule = policy.rules.find(r => r.id === 'rule-1');\r\n      expect(updatedRule?.name).toBe('Updated Rule');\r\n    });\r\n\r\n    it('should throw error when updating non-existent rule', () => {\r\n      expect(() => {\r\n        policy.updateRule('non-existent', { name: 'Updated' }, userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should remove a rule', () => {\r\n      policy.removeRule('rule-1', userId);\r\n\r\n      expect(policy.rules).toHaveLength(0);\r\n    });\r\n\r\n    it('should throw error when removing non-existent rule', () => {\r\n      expect(() => {\r\n        policy.removeRule('non-existent', userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n  });\r\n\r\n  describe('policy state management', () => {\r\n    let policy: SecurityPolicy;\r\n\r\n    beforeEach(() => {\r\n      policy = SecurityPolicy.create({\r\n        name: 'Test Policy',\r\n        description: 'A test policy',\r\n        version: '1.0.0',\r\n        tenantId,\r\n        rules: [validRule],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should enable policy', () => {\r\n      policy.disable(userId);\r\n      expect(policy.enabled).toBe(false);\r\n\r\n      policy.enable(userId);\r\n      expect(policy.enabled).toBe(true);\r\n      expect(policy.lastModifiedBy).toBe(userId);\r\n    });\r\n\r\n    it('should disable policy', () => {\r\n      policy.disable(userId);\r\n      expect(policy.enabled).toBe(false);\r\n      expect(policy.lastModifiedBy).toBe(userId);\r\n    });\r\n  });\r\n\r\n  describe('audit trail', () => {\r\n    let policy: SecurityPolicy;\r\n\r\n    beforeEach(() => {\r\n      policy = SecurityPolicy.create({\r\n        name: 'Test Policy',\r\n        description: 'A test policy',\r\n        version: '1.0.0',\r\n        tenantId,\r\n        rules: [validRule],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should create audit entries when evaluating rules', () => {\r\n      policy.evaluate(validContext);\r\n\r\n      expect(policy.auditTrail).toHaveLength(1);\r\n      expect(policy.auditTrail[0].ruleId).toBe('rule-1');\r\n      expect(policy.auditTrail[0].executionStatus).toBe(PolicyExecutionStatus.PENDING);\r\n    });\r\n\r\n    it('should maintain audit trail history', () => {\r\n      // Evaluate multiple times\r\n      policy.evaluate(validContext);\r\n      policy.evaluate(validContext);\r\n\r\n      expect(policy.auditTrail).toHaveLength(2);\r\n    });\r\n  });\r\n\r\n  describe('confidence calculation', () => {\r\n    let policy: SecurityPolicy;\r\n\r\n    beforeEach(() => {\r\n      policy = SecurityPolicy.create({\r\n        name: 'Test Policy',\r\n        description: 'A test policy',\r\n        version: '1.0.0',\r\n        tenantId,\r\n        rules: [],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should calculate higher confidence for critical severity rules', () => {\r\n      const criticalRule = { ...validRule, id: 'critical', severity: SecurityRuleSeverity.CRITICAL };\r\n      const lowRule = { ...validRule, id: 'low', severity: SecurityRuleSeverity.LOW };\r\n\r\n      policy.addRule(criticalRule, userId);\r\n      policy.addRule(lowRule, userId);\r\n\r\n      const results = policy.evaluate(validContext);\r\n      const criticalResult = results.find(r => r.ruleId === 'critical');\r\n      const lowResult = results.find(r => r.ruleId === 'low');\r\n\r\n      expect(criticalResult?.confidence).toBeGreaterThan(lowResult?.confidence || 0);\r\n    });\r\n\r\n    it('should adjust confidence based on threat context', () => {\r\n      policy.addRule(validRule, userId);\r\n\r\n      const contextWithThreat = {\r\n        ...validContext,\r\n        threatContext: {\r\n          threatLevel: 'HIGH',\r\n          indicators: ['malicious-ip'],\r\n          confidence: 0.8\r\n        }\r\n      };\r\n\r\n      const results = policy.evaluate(contextWithThreat);\r\n      expect(results[0].confidence).toBeGreaterThan(0.8);\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    let policy: SecurityPolicy;\r\n\r\n    beforeEach(() => {\r\n      policy = SecurityPolicy.create({\r\n        name: 'Test Policy',\r\n        description: 'A test policy',\r\n        version: '1.0.0',\r\n        tenantId,\r\n        rules: [],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should handle invalid rule conditions gracefully', () => {\r\n      const invalidRule = { ...validRule, condition: '{\"field\":{\"nested\":\"invalid\"},\"operator\":\"equals\",\"value\":\"test\"}' };\r\n      policy.addRule(invalidRule, userId);\r\n\r\n      const results = policy.evaluate(validContext);\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].matched).toBe(false);\r\n      expect(results[0].reason).toContain('evaluation failed');\r\n    });\r\n\r\n    it('should continue evaluation after rule error', () => {\r\n      const invalidRule = { ...validRule, id: 'invalid', condition: '{\"invalid\": \"structure\"}' };\r\n      const validRule2 = { ...validRule, id: 'valid' };\r\n\r\n      policy.addRule(invalidRule, userId);\r\n      policy.addRule(validRule2, userId);\r\n\r\n      const results = policy.evaluate(validContext);\r\n\r\n      expect(results).toHaveLength(2);\r\n      expect(results.find(r => r.ruleId === 'invalid')?.matched).toBe(false);\r\n      expect(results.find(r => r.ruleId === 'valid')?.matched).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}