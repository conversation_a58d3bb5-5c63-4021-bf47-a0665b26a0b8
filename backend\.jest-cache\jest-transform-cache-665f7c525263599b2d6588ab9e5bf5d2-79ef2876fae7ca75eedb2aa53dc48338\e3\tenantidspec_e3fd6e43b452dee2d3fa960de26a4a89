7aa28ab4216bc670c749e0fb303e03a6
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const node_test_1 = require("node:test");
const node_test_2 = require("node:test");
const node_test_3 = require("node:test");
const tenant_id_value_object_1 = require("../../value-objects/tenant-id.value-object");
const unique_entity_id_value_object_1 = require("../../value-objects/unique-entity-id.value-object");
(0, node_test_2.describe)('TenantId', () => {
    (0, node_test_2.describe)('creation', () => {
        (0, node_test_1.it)('should create a valid tenant ID from UUID string', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const tenantId = tenant_id_value_object_1.TenantId.fromString(uuid);
            expect(tenantId.value).toBe(uuid);
            expect(tenantId.isValid()).toBe(true);
        });
        (0, node_test_1.it)('should generate a new tenant ID', () => {
            const tenantId = tenant_id_value_object_1.TenantId.generate();
            expect(tenantId.value).toBeDefined();
            expect(tenantId.isValid()).toBe(true);
            expect(tenant_id_value_object_1.TenantId.isValid(tenantId.value)).toBe(true);
        });
        (0, node_test_1.it)('should create tenant ID from UniqueEntityId', () => {
            const uniqueId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const tenantId = tenant_id_value_object_1.TenantId.fromUniqueEntityId(uniqueId);
            expect(tenantId.value).toBe(uniqueId.value);
            expect(tenantId.uniqueId.equals(uniqueId)).toBe(true);
        });
        (0, node_test_1.it)('should create tenant ID from existing ID', () => {
            const original = tenant_id_value_object_1.TenantId.generate();
            const copy = tenant_id_value_object_1.TenantId.fromId(original);
            expect(copy.value).toBe(original.value);
            expect(copy.equals(original)).toBe(true);
            expect(copy).not.toBe(original); // Different instances
        });
    });
    (0, node_test_2.describe)('validation', () => {
        (0, node_test_1.it)('should throw error for null value', () => {
            expect(() => new tenant_id_value_object_1.TenantId(null)).toThrow('TenantId cannot be null or undefined');
        });
        (0, node_test_1.it)('should throw error for undefined value', () => {
            expect(() => new tenant_id_value_object_1.TenantId(undefined)).toThrow('TenantId cannot be null or undefined');
        });
        (0, node_test_1.it)('should throw error for empty string', () => {
            expect(() => new tenant_id_value_object_1.TenantId('')).toThrow('TenantId cannot be empty');
        });
        (0, node_test_1.it)('should throw error for whitespace string', () => {
            expect(() => new tenant_id_value_object_1.TenantId('   ')).toThrow('TenantId cannot be empty');
        });
        (0, node_test_1.it)('should throw error for non-string value', () => {
            expect(() => new tenant_id_value_object_1.TenantId(123)).toThrow('TenantId must be a string');
        });
        (0, node_test_1.it)('should throw error for invalid UUID format', () => {
            expect(() => new tenant_id_value_object_1.TenantId('invalid-uuid')).toThrow('Invalid TenantId format');
        });
        (0, node_test_1.it)('should throw error for non-UUID v4 format', () => {
            expect(() => new tenant_id_value_object_1.TenantId('123e4567-e89b-22d3-a456-************')).toThrow('Invalid TenantId format');
        });
    });
    (0, node_test_2.describe)('static validation methods', () => {
        (0, node_test_1.it)('should validate correct UUID strings', () => {
            const validUuid = '123e4567-e89b-42d3-a456-************';
            expect(tenant_id_value_object_1.TenantId.isValid(validUuid)).toBe(true);
        });
        (0, node_test_1.it)('should reject invalid UUID strings', () => {
            expect(tenant_id_value_object_1.TenantId.isValid('invalid-uuid')).toBe(false);
            expect(tenant_id_value_object_1.TenantId.isValid('')).toBe(false);
            expect(tenant_id_value_object_1.TenantId.isValid(null)).toBe(false);
            expect(tenant_id_value_object_1.TenantId.isValid(undefined)).toBe(false);
        });
        (0, node_test_1.it)('should try parse valid UUID', () => {
            const validUuid = '123e4567-e89b-42d3-a456-************';
            const result = tenant_id_value_object_1.TenantId.tryParse(validUuid);
            expect(result).not.toBeNull();
            expect(result.value).toBe(validUuid);
        });
        (0, node_test_1.it)('should return null for invalid UUID in tryParse', () => {
            const result = tenant_id_value_object_1.TenantId.tryParse('invalid-uuid');
            expect(result).toBeNull();
        });
    });
    (0, node_test_2.describe)('utility methods', () => {
        let tenantId;
        (0, node_test_3.beforeEach)(() => {
            tenantId = tenant_id_value_object_1.TenantId.fromString('123e4567-e89b-42d3-a456-************');
        });
        (0, node_test_1.it)('should get version', () => {
            expect(tenantId.getVersion()).toBe(4);
        });
        (0, node_test_1.it)('should get variant', () => {
            expect(tenantId.getVariant()).toBe('RFC4122');
        });
        (0, node_test_1.it)('should get short string representation', () => {
            const shortString = tenantId.toShortString();
            expect(shortString).toBe('123e4567');
            expect(tenantId.toShortString(4)).toBe('123e');
        });
        (0, node_test_1.it)('should get compact string representation', () => {
            const compactString = tenantId.toCompactString();
            expect(compactString).toBe('123e4567e89b42d3a456************');
        });
        (0, node_test_1.it)('should convert to uppercase', () => {
            const upperCase = tenantId.toUpperCase();
            expect(upperCase).toBe('123E4567-E89B-42D3-A456-************');
        });
        (0, node_test_1.it)('should convert to lowercase', () => {
            const lowerCase = tenantId.toLowerCase();
            expect(lowerCase).toBe('123e4567-e89b-42d3-a456-************');
        });
    });
    (0, node_test_2.describe)('equality and comparison', () => {
        (0, node_test_1.it)('should be equal to itself', () => {
            const tenantId = tenant_id_value_object_1.TenantId.generate();
            expect(tenantId.equals(tenantId)).toBe(true);
            expect(tenantId.matches(tenantId)).toBe(true);
        });
        (0, node_test_1.it)('should be equal to tenant ID with same value', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const tenantId1 = tenant_id_value_object_1.TenantId.fromString(uuid);
            const tenantId2 = tenant_id_value_object_1.TenantId.fromString(uuid);
            expect(tenantId1.equals(tenantId2)).toBe(true);
            expect(tenantId1.matches(tenantId2)).toBe(true);
        });
        (0, node_test_1.it)('should not be equal to tenant ID with different value', () => {
            const tenantId1 = tenant_id_value_object_1.TenantId.generate();
            const tenantId2 = tenant_id_value_object_1.TenantId.generate();
            expect(tenantId1.equals(tenantId2)).toBe(false);
            expect(tenantId1.matches(tenantId2)).toBe(false);
        });
        (0, node_test_1.it)('should not be equal to null or undefined', () => {
            const tenantId = tenant_id_value_object_1.TenantId.generate();
            expect(tenantId.equals(null)).toBe(false);
            expect(tenantId.equals(undefined)).toBe(false);
        });
        (0, node_test_1.it)('should not be equal to non-TenantId object', () => {
            const tenantId = tenant_id_value_object_1.TenantId.generate();
            expect(tenantId.equals({})).toBe(false);
        });
    });
    (0, node_test_2.describe)('special tenant IDs', () => {
        (0, node_test_1.it)('should create system tenant ID', () => {
            const systemTenant = tenant_id_value_object_1.TenantId.system();
            expect(systemTenant.isSystem()).toBe(true);
            expect(systemTenant.isDefault()).toBe(false);
        });
        (0, node_test_1.it)('should create default tenant ID', () => {
            const defaultTenant = tenant_id_value_object_1.TenantId.default();
            expect(defaultTenant.isDefault()).toBe(true);
            expect(defaultTenant.isSystem()).toBe(false);
        });
        (0, node_test_1.it)('should create deterministic tenant ID from seed', () => {
            const tenant1 = tenant_id_value_object_1.TenantId.fromSeed('test');
            const tenant2 = tenant_id_value_object_1.TenantId.fromSeed('test');
            expect(tenant1.equals(tenant2)).toBe(true);
        });
        (0, node_test_1.it)('should create different tenant IDs from different seeds', () => {
            const tenant1 = tenant_id_value_object_1.TenantId.fromSeed('test1');
            const tenant2 = tenant_id_value_object_1.TenantId.fromSeed('test2');
            expect(tenant1.equals(tenant2)).toBe(false);
        });
    });
    (0, node_test_2.describe)('bulk operations', () => {
        (0, node_test_1.it)('should generate multiple tenant IDs', () => {
            const tenantIds = tenant_id_value_object_1.TenantId.generateMany(5);
            expect(tenantIds).toHaveLength(5);
            expect(tenantIds.every(id => id.isValid())).toBe(true);
            // All should be unique
            const uniqueValues = new Set(tenantIds.map(id => id.value));
            expect(uniqueValues.size).toBe(5);
        });
        (0, node_test_1.it)('should handle zero count for generateMany', () => {
            const tenantIds = tenant_id_value_object_1.TenantId.generateMany(0);
            expect(tenantIds).toHaveLength(0);
        });
        (0, node_test_1.it)('should throw error for negative count', () => {
            expect(() => tenant_id_value_object_1.TenantId.generateMany(-1)).toThrow('Count must be non-negative');
        });
    });
    (0, node_test_2.describe)('serialization', () => {
        (0, node_test_1.it)('should convert to string', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const tenantId = tenant_id_value_object_1.TenantId.fromString(uuid);
            expect(tenantId.toString()).toBe(uuid);
        });
        (0, node_test_1.it)('should convert to JSON', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const tenantId = tenant_id_value_object_1.TenantId.fromString(uuid);
            const json = tenantId.toJSON();
            expect(json.value).toBe(uuid);
            expect(json.type).toBe('TenantId');
            expect(json.version).toBe(4);
            expect(json.variant).toBe('RFC4122');
            expect(json.shortString).toBe('123e4567');
            expect(json.compactString).toBe('123e4567e89b42d3a456************');
        });
        (0, node_test_1.it)('should create from JSON', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const json = { value: uuid };
            const tenantId = tenant_id_value_object_1.TenantId.fromJSON(json);
            expect(tenantId.value).toBe(uuid);
        });
    });
    (0, node_test_2.describe)('immutability', () => {
        (0, node_test_1.it)('should be immutable after creation', () => {
            const tenantId = tenant_id_value_object_1.TenantId.generate();
            const originalValue = tenantId.value;
            // Attempt to modify (should throw due to frozen object)
            expect(() => {
                tenantId._value = 'modified';
            }).toThrow(); // Frozen objects throw when attempting to modify
            // Value should remain unchanged
            expect(tenantId.value).toBe(originalValue);
        });
        (0, node_test_1.it)('should be frozen', () => {
            const tenantId = tenant_id_value_object_1.TenantId.generate();
            expect(Object.isFrozen(tenantId)).toBe(true);
        });
    });
    (0, node_test_2.describe)('edge cases', () => {
        (0, node_test_1.it)('should handle case-insensitive UUID comparison', () => {
            const uuid1 = '123e4567-e89b-42d3-a456-************';
            const uuid2 = '123e4567-e89b-42d3-a456-************';
            const tenantId1 = tenant_id_value_object_1.TenantId.fromString(uuid1);
            const tenantId2 = tenant_id_value_object_1.TenantId.fromString(uuid2);
            expect(tenantId1.equals(tenantId2)).toBe(true);
        });
        (0, node_test_1.it)('should maintain original case in value', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const tenantId = tenant_id_value_object_1.TenantId.fromString(uuid);
            expect(tenantId.value).toBe(uuid);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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