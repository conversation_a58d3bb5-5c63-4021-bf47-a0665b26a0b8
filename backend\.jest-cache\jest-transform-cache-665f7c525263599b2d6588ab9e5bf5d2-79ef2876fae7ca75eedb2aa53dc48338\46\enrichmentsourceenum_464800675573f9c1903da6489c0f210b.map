{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\enrichment-source.enum.ts", "mappings": ";;;AAAA;;;;;;GAMG;AACH,IAAY,gBAoQX;AApQD,WAAY,gBAAgB;IAC1B;;OAEG;IAEH,2CAA2C;IAC3C,uEAAmD,CAAA;IAEnD,sCAAsC;IACtC,mCAAe,CAAA;IAEf,qCAAqC;IACrC,yDAAqC,CAAA;IAErC,8BAA8B;IAC9B,yDAAqC,CAAA;IAErC,mCAAmC;IACnC,qDAAiC,CAAA;IAEjC,oCAAoC;IACpC,+BAAW,CAAA;IAEX,kDAAkD;IAClD,iCAAa,CAAA;IAEb,uBAAuB;IACvB,6CAAyB,CAAA;IAEzB;;OAEG;IAEH,6BAA6B;IAC7B,mDAA+B,CAAA;IAE/B,iCAAiC;IACjC,2DAAuC,CAAA;IAEvC,8BAA8B;IAC9B,qDAAiC,CAAA;IAEjC,2BAA2B;IAC3B,uDAAmC,CAAA;IAEnC,gCAAgC;IAChC,yDAAqC,CAAA;IAErC;;OAEG;IAEH,+BAA+B;IAC/B,qDAAiC,CAAA;IAEjC,+CAA+C;IAC/C,iDAA6B,CAAA;IAE7B,+BAA+B;IAC/B,iDAA6B,CAAA;IAE7B;;OAEG;IAEH,sCAAsC;IACtC,+BAAW,CAAA;IAEX,oBAAoB;IACpB,iDAA6B,CAAA;IAE7B,4BAA4B;IAC5B,mDAA+B,CAAA;IAE/B,wBAAwB;IACxB,yDAAqC,CAAA;IAErC,iCAAiC;IACjC,2DAAuC,CAAA;IAEvC;;OAEG;IAEH,wCAAwC;IACxC,iCAAa,CAAA;IAEb,+BAA+B;IAC/B,yDAAqC,CAAA;IAErC,8BAA8B;IAC9B,2DAAuC,CAAA;IAEvC,6BAA6B;IAC7B,mEAA+C,CAAA;IAE/C,mCAAmC;IACnC,yDAAqC,CAAA;IAErC;;OAEG;IAEH,uBAAuB;IACvB,yDAAqC,CAAA;IAErC,uBAAuB;IACvB,iCAAa,CAAA;IAEb,yBAAyB;IACzB,2DAAuC,CAAA;IAEvC,iBAAiB;IACjB,2CAAuB,CAAA;IAEvB,mCAAmC;IACnC,6CAAyB,CAAA;IAEzB;;OAEG;IAEH,8BAA8B;IAC9B,qDAAiC,CAAA;IAEjC,sBAAsB;IACtB,mCAAe,CAAA;IAEf,iCAAiC;IACjC,yDAAqC,CAAA;IAErC,8BAA8B;IAC9B,qDAAiC,CAAA;IAEjC,oCAAoC;IACpC,qDAAiC,CAAA;IAEjC;;OAEG;IAEH,8BAA8B;IAC9B,+BAAW,CAAA;IAEX,gCAAgC;IAChC,+BAAW,CAAA;IAEX,8BAA8B;IAC9B,2CAAuB,CAAA;IAEvB,4BAA4B;IAC5B,iEAA6C,CAAA;IAE7C,gCAAgC;IAChC,2DAAuC,CAAA;IAEvC;;OAEG;IAEH,4BAA4B;IAC5B,2DAAuC,CAAA;IAEvC,6BAA6B;IAC7B,yDAAqC,CAAA;IAErC,iBAAiB;IACjB,6CAAyB,CAAA;IAEzB,2BAA2B;IAC3B,+DAA2C,CAAA;IAE3C,2BAA2B;IAC3B,uDAAmC,CAAA;IAEnC;;OAEG;IAEH,iBAAiB;IACjB,6CAAyB,CAAA;IAEzB,aAAa;IACb,qCAAiB,CAAA;IAEjB,aAAa;IACb,qCAAiB,CAAA;IAEjB,mBAAmB;IACnB,iDAA6B,CAAA;IAE7B,cAAc;IACd,uCAAmB,CAAA;IAEnB,sBAAsB;IACtB,uDAAmC,CAAA;IAEnC;;OAEG;IAEH,4BAA4B;IAC5B,iDAA6B,CAAA;IAE7B,8BAA8B;IAC9B,qDAAiC,CAAA;IAEjC,4BAA4B;IAC5B,iDAA6B,CAAA;IAE7B,0BAA0B;IAC1B,mDAA+B,CAAA;IAE/B,oCAAoC;IACpC,iCAAa,CAAA;IAEb;;OAEG;IAEH,wBAAwB;IACxB,uCAAmB,CAAA;IAEnB,iCAAiC;IACjC,yDAAqC,CAAA;IAErC,4BAA4B;IAC5B,uDAAmC,CAAA;IAEnC,gCAAgC;IAChC,+DAA2C,CAAA;IAE3C;;OAEG;IAEH,8BAA8B;IAC9B,uEAAmD,CAAA;IAEnD,8BAA8B;IAC9B,iDAA6B,CAAA;IAE7B,2BAA2B;IAC3B,uDAAmC,CAAA;IAEnC,4BAA4B;IAC5B,mEAA+C,CAAA;IAE/C,iCAAiC;IACjC,iDAA6B,CAAA;IAE7B;;OAEG;IAEH,gCAAgC;IAChC,uCAAmB,CAAA;IAEnB,oCAAoC;IACpC,mCAAe,CAAA;AACjB,CAAC,EApQW,gBAAgB,gCAAhB,gBAAgB,QAoQ3B;AAED;;GAEG;AACH,MAAa,qBAAqB;IAChC;;OAEG;IACH,MAAM,CAAC,aAAa;QAClB,OAAO,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB;QAC1B,OAAO;YACL,gBAAgB,CAAC,uBAAuB;YACxC,gBAAgB,CAAC,KAAK;YACtB,gBAAgB,CAAC,gBAAgB;YACjC,gBAAgB,CAAC,gBAAgB;YACjC,gBAAgB,CAAC,cAAc;YAC/B,gBAAgB,CAAC,GAAG;YACpB,gBAAgB,CAAC,IAAI;YACrB,gBAAgB,CAAC,UAAU;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB;QACzB,OAAO;YACL,gBAAgB,CAAC,aAAa;YAC9B,gBAAgB,CAAC,iBAAiB;YAClC,gBAAgB,CAAC,cAAc;YAC/B,gBAAgB,CAAC,eAAe;YAChC,gBAAgB,CAAC,gBAAgB;SAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB;QAC1B,OAAO;YACL,gBAAgB,CAAC,UAAU;YAC3B,gBAAgB,CAAC,MAAM;YACvB,gBAAgB,CAAC,MAAM;YACvB,gBAAgB,CAAC,YAAY;YAC7B,gBAAgB,CAAC,OAAO;YACxB,gBAAgB,CAAC,eAAe;SACjC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB;QACvB,OAAO;YACL,gBAAgB,CAAC,uBAAuB;YACxC,gBAAgB,CAAC,YAAY;YAC7B,gBAAgB,CAAC,eAAe;YAChC,gBAAgB,CAAC,qBAAqB;YACtC,gBAAgB,CAAC,YAAY;YAC7B,gBAAgB,CAAC,iBAAiB;YAClC,gBAAgB,CAAC,gBAAgB;SAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB;QACvB,OAAO;YACL,gBAAgB,CAAC,aAAa;YAC9B,gBAAgB,CAAC,iBAAiB;YAClC,gBAAgB,CAAC,cAAc;YAC/B,gBAAgB,CAAC,cAAc;YAC/B,gBAAgB,CAAC,cAAc;YAC/B,gBAAgB,CAAC,UAAU;YAC3B,gBAAgB,CAAC,qBAAqB;YACtC,gBAAgB,CAAC,YAAY;SAC9B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe;QACpB,OAAO;YACL,gBAAgB,CAAC,uBAAuB;YACxC,gBAAgB,CAAC,GAAG;YACpB,gBAAgB,CAAC,YAAY;YAC7B,gBAAgB,CAAC,IAAI;YACrB,gBAAgB,CAAC,gBAAgB;YACjC,gBAAgB,CAAC,iBAAiB;YAClC,gBAAgB,CAAC,mBAAmB;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,MAAwB;QACxC,MAAM,eAAe,GAAG;YACtB,GAAG,qBAAqB,CAAC,qBAAqB,EAAE;YAChD,GAAG,qBAAqB,CAAC,oBAAoB,EAAE;YAC/C,gBAAgB,CAAC,uBAAuB;YACxC,gBAAgB,CAAC,KAAK;YACtB,gBAAgB,CAAC,gBAAgB;YACjC,gBAAgB,CAAC,GAAG;YACpB,gBAAgB,CAAC,YAAY;SAC9B,CAAC;QACF,OAAO,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,MAAwB;QACxC,OAAO,qBAAqB,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,MAAwB;QACxC,OAAO,qBAAqB,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,MAAwB;QAC9C,OAAO,qBAAqB,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,MAAwB;QACjD,MAAM,MAAM,GAA8C;YACxD,2BAA2B;YAC3B,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,EAAE,EAAE;YACvC,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,EAAE;YAC1B,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,EAAE;YACnC,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,EAAE,EAAE;YAC9C,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE;YAC3B,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,EAAE,EAAE;YAEvC,0BAA0B;YAC1B,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,EAAE,EAAE;YACvC,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,EAAE;YACjC,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,EAAE;YACpC,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,EAAE;YACxC,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,EAAE;YAErC,qBAAqB;YACrB,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,EAAE;YAC5B,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,EAAE;YAC7B,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,EAAE;YAC5B,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,EAAE;YACrC,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,EAAE;YAEhC,oBAAoB;YACpB,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,EAAE;YACtC,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,EAAE;YACnC,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,EAAE;YAC9B,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,EAAE;SAC7B,CAAC;QAEF,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,6BAA6B;IAC5D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAAC,MAAwB;QACrD,MAAM,SAAS,GAA8C;YAC3D,oCAAoC;YACpC,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,IAAI,EAAO,aAAa;YAC1D,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,IAAI,EAAG,aAAa;YAC1D,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,GAAG,EAAO,aAAa;YAC1D,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,GAAG,EAAS,YAAY;YAEzD,iBAAiB;YACjB,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,EAAE,CAAC;YAC7C,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC;YAChC,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC;YAEpC,gBAAgB;YAChB,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,EAAE;YAC1B,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,EAAE;YACnC,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,EAAE;YAC5B,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,EAAE;YAE5B,iBAAiB;YACjB,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,GAAG;YAC5B,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,EAAE,GAAG;YACxC,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,EAAE,GAAG;YAE3C,kBAAkB;YAClB,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,GAAG;YACjC,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,GAAG;SAC1C,CAAC;QAEF,OAAO,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,wBAAwB;IAC1D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,MAAwB;QAC7C,MAAM,KAAK,GAAyF;YAClG,eAAe;YACf,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,MAAM;YAChC,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,MAAM;YAC9B,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,MAAM;YACvC,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,MAAM;YACzC,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,MAAM;YAEhC,WAAW;YACX,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,KAAK;YACxC,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,KAAK;YACpC,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,KAAK;YAEjC,cAAc;YACd,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,QAAQ;YAC1C,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,QAAQ;YAC9C,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,QAAQ;YACnC,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,QAAQ;YAEnC,YAAY;YACZ,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,EAAE,MAAM;YAClD,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,MAAM;YACvC,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,MAAM;YAE1C,kBAAkB;YAClB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,EAAE,YAAY;YACjD,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,YAAY;YACpC,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,YAAY;SACtC,CAAC;QAEF,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,MAAwB;QAC1C,MAAM,UAAU,GAA8C;YAC5D,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,EAAS,YAAY;YACrD,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,EAAa,YAAY;YACrD,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,IAAI,EAAS,iBAAiB;YAC1D,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,oBAAoB;YAC7D,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,qBAAqB;YAC9D,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,GAAG,EAAY,wBAAwB;YACjE,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,EAAE,IAAI,EAAE,eAAe;YACjE,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,IAAI,EAAG,eAAe;YACxD,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,IAAI,EAAE,eAAe;SAC5D,CAAC;QAEF,OAAO,UAAU,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,6BAA6B;IACjE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,MAAwB;QACnD,MAAM,SAAS,GAAgD;YAC7D,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC,YAAY,EAAE,kBAAkB,EAAE,YAAY,CAAC;YAClF,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,CAAC,QAAQ,EAAE,kBAAkB,EAAE,YAAY,CAAC;YAClF,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,kBAAkB,EAAE,YAAY,CAAC;YAC5E,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;YACvF,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,CAAC;YACnF,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,CAAC;YAC9E,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,cAAc,CAAC;YAC7E,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,CAAC;YAC7E,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;YACtF,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,CAAC;SACtF,CAAC;QAEF,OAAO,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,MAAwB;QAC5C,MAAM,YAAY,GAA8C;YAC9D,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,EAAE,mDAAmD;YAC/F,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,8CAA8C;YACxE,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,EAAE,oDAAoD;YACzF,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,mDAAmD;YACrF,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,+CAA+C;YACrF,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,0CAA0C;YACzE,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,iCAAiC;YACzD,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,+CAA+C;YAChF,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,mCAAmC;YAC5D,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,EAAE,qCAAqC;YAC1E,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,oCAAoC;YACvE,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,uCAAuC;YACjE,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,iCAAiC;YACpE,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,gDAAgD;YAC3E,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,+CAA+C;YAC7E,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,sCAAsC;YAC1E,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,EAAE,0CAA0C;YACpF,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,gCAAgC;YACtE,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,2CAA2C;YACvE,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,yCAAyC;SACpE,CAAC;QAEF,OAAO,YAAY,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,oBAAoB,CAAC;IAChG,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,MAAwB;QACzC,IAAI,qBAAqB,CAAC,qBAAqB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,qBAAqB,CAAC;QACjG,IAAI,qBAAqB,CAAC,oBAAoB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,qBAAqB,CAAC;QAChG,IAAI,qBAAqB,CAAC,qBAAqB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,eAAe,CAAC;QAE3F,MAAM,UAAU,GAAuC;YACrD,aAAa,EAAE,CAAC,gBAAgB,CAAC,cAAc,EAAE,gBAAgB,CAAC,YAAY,EAAE,gBAAgB,CAAC,YAAY,CAAC;YAC9G,eAAe,EAAE,CAAC,gBAAgB,CAAC,GAAG,EAAE,gBAAgB,CAAC,YAAY,EAAE,gBAAgB,CAAC,aAAa,EAAE,gBAAgB,CAAC,gBAAgB,CAAC;YACzI,kBAAkB,EAAE,CAAC,gBAAgB,CAAC,IAAI,EAAE,gBAAgB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,gBAAgB,CAAC;YACjH,UAAU,EAAE,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,IAAI,EAAE,gBAAgB,CAAC,SAAS,CAAC;YAClG,SAAS,EAAE,CAAC,gBAAgB,CAAC,cAAc,EAAE,gBAAgB,CAAC,KAAK,EAAE,gBAAgB,CAAC,gBAAgB,CAAC;YACvG,WAAW,EAAE,CAAC,gBAAgB,CAAC,GAAG,EAAE,gBAAgB,CAAC,SAAS,EAAE,gBAAgB,CAAC,iBAAiB,CAAC;YACnG,OAAO,EAAE,CAAC,gBAAgB,CAAC,YAAY,EAAE,gBAAgB,CAAC,cAAc,EAAE,gBAAgB,CAAC,YAAY,CAAC;YACxG,UAAU,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,gBAAgB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,eAAe,CAAC;YAC3G,UAAU,EAAE,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,gBAAgB,CAAC,YAAY,EAAE,gBAAgB,CAAC,eAAe,CAAC;SACxH,CAAC;QAEF,KAAK,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7D,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAAE,OAAO,QAAQ,CAAC;QAChD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,MAAc;QAC3B,OAAO,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,MAA0B,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,KAAa;QAC7B,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QACtE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAChD,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,IAAI,IAAI,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,MAAwB,EAAE,QAAgB;QACrE,MAAM,eAAe,GAAG,qBAAqB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC1E,MAAM,UAAU,GAAG,qBAAqB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC5D,MAAM,YAAY,GAAG,qBAAqB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAEnE,IAAI,QAAQ,GAAG,eAAe,CAAC;QAE/B,uCAAuC;QACvC,IAAI,UAAU;YAAE,QAAQ,IAAI,EAAE,CAAC;QAE/B,4EAA4E;QAC5E,MAAM,cAAc,GAAG;YACrB,MAAM,EAAE,CAAC;YACT,KAAK,EAAE,CAAC;YACR,QAAQ,EAAE,CAAC;YACX,MAAM,EAAE,CAAC,CAAC;YACV,YAAY,EAAE,CAAC,CAAC;SACjB,CAAC;QACF,QAAQ,IAAI,cAAc,CAAC,YAAY,CAAC,CAAC;QAEzC,sCAAsC;QACtC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,MAAwB;QAChD,MAAM,cAAc,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QAC7E,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC,uCAAuC;IAClF,CAAC;CACF;AArYD,sDAqYC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\enrichment-source.enum.ts"], "sourcesContent": ["/**\r\n * Enrichment Source Enum\r\n * \r\n * Represents the various sources of data used to enrich security events\r\n * with additional context, threat intelligence, and metadata. Used for\r\n * tracking data provenance, quality assessment, and source reliability.\r\n */\r\nexport enum EnrichmentSource {\r\n  /**\r\n   * Threat Intelligence Sources\r\n   */\r\n  \r\n  /** Commercial threat intelligence feeds */\r\n  COMMERCIAL_THREAT_INTEL = 'commercial_threat_intel',\r\n  \r\n  /** Open source threat intelligence */\r\n  OSINT = 'osint',\r\n  \r\n  /** Government threat intelligence */\r\n  GOVERNMENT_INTEL = 'government_intel',\r\n  \r\n  /** Industry sharing groups */\r\n  INDUSTRY_SHARING = 'industry_sharing',\r\n  \r\n  /** Internal threat intelligence */\r\n  INTERNAL_INTEL = 'internal_intel',\r\n  \r\n  /** Threat intelligence platforms */\r\n  TIP = 'tip',\r\n  \r\n  /** MISP (Malware Information Sharing Platform) */\r\n  MISP = 'misp',\r\n  \r\n  /** STIX/TAXII feeds */\r\n  STIX_TAXII = 'stix_taxii',\r\n\r\n  /**\r\n   * Reputation Services\r\n   */\r\n  \r\n  /** IP reputation services */\r\n  IP_REPUTATION = 'ip_reputation',\r\n  \r\n  /** Domain reputation services */\r\n  DOMAIN_REPUTATION = 'domain_reputation',\r\n  \r\n  /** URL reputation services */\r\n  URL_REPUTATION = 'url_reputation',\r\n  \r\n  /** File hash reputation */\r\n  FILE_REPUTATION = 'file_reputation',\r\n  \r\n  /** Email reputation services */\r\n  EMAIL_REPUTATION = 'email_reputation',\r\n\r\n  /**\r\n   * Geolocation Services\r\n   */\r\n  \r\n  /** IP geolocation databases */\r\n  IP_GEOLOCATION = 'ip_geolocation',\r\n  \r\n  /** ASN (Autonomous System Number) databases */\r\n  ASN_DATABASE = 'asn_database',\r\n  \r\n  /** ISP information services */\r\n  ISP_DATABASE = 'isp_database',\r\n\r\n  /**\r\n   * Vulnerability Databases\r\n   */\r\n  \r\n  /** National Vulnerability Database */\r\n  NVD = 'nvd',\r\n  \r\n  /** CVE databases */\r\n  CVE_DATABASE = 'cve_database',\r\n  \r\n  /** CVSS scoring services */\r\n  CVSS_DATABASE = 'cvss_database',\r\n  \r\n  /** Exploit databases */\r\n  EXPLOIT_DATABASE = 'exploit_database',\r\n  \r\n  /** Vendor security advisories */\r\n  VENDOR_ADVISORIES = 'vendor_advisories',\r\n\r\n  /**\r\n   * Asset and Configuration Sources\r\n   */\r\n  \r\n  /** Configuration Management Database */\r\n  CMDB = 'cmdb',\r\n  \r\n  /** Asset management systems */\r\n  ASSET_MANAGEMENT = 'asset_management',\r\n  \r\n  /** Network discovery tools */\r\n  NETWORK_DISCOVERY = 'network_discovery',\r\n  \r\n  /** Vulnerability scanners */\r\n  VULNERABILITY_SCANNER = 'vulnerability_scanner',\r\n  \r\n  /** Inventory management systems */\r\n  INVENTORY_SYSTEM = 'inventory_system',\r\n\r\n  /**\r\n   * Identity and Access Sources\r\n   */\r\n  \r\n  /** Active Directory */\r\n  ACTIVE_DIRECTORY = 'active_directory',\r\n  \r\n  /** LDAP directories */\r\n  LDAP = 'ldap',\r\n  \r\n  /** Identity providers */\r\n  IDENTITY_PROVIDER = 'identity_provider',\r\n  \r\n  /** HR systems */\r\n  HR_SYSTEM = 'hr_system',\r\n  \r\n  /** Privileged access management */\r\n  PAM_SYSTEM = 'pam_system',\r\n\r\n  /**\r\n   * Network and Infrastructure Sources\r\n   */\r\n  \r\n  /** DNS resolution services */\r\n  DNS_RESOLUTION = 'dns_resolution',\r\n  \r\n  /** WHOIS databases */\r\n  WHOIS = 'whois',\r\n  \r\n  /** Network topology discovery */\r\n  NETWORK_TOPOLOGY = 'network_topology',\r\n  \r\n  /** Firewall rule databases */\r\n  FIREWALL_RULES = 'firewall_rules',\r\n  \r\n  /** Network device configurations */\r\n  NETWORK_CONFIG = 'network_config',\r\n\r\n  /**\r\n   * Behavioral and Analytics Sources\r\n   */\r\n  \r\n  /** User behavior analytics */\r\n  UBA = 'uba',\r\n  \r\n  /** Entity behavior analytics */\r\n  EBA = 'eba',\r\n  \r\n  /** Machine learning models */\r\n  ML_MODELS = 'ml_models',\r\n  \r\n  /** Statistical baselines */\r\n  STATISTICAL_BASELINE = 'statistical_baseline',\r\n  \r\n  /** Anomaly detection systems */\r\n  ANOMALY_DETECTION = 'anomaly_detection',\r\n\r\n  /**\r\n   * Historical and Context Sources\r\n   */\r\n  \r\n  /** Historical event data */\r\n  HISTORICAL_EVENTS = 'historical_events',\r\n  \r\n  /** Previous incident data */\r\n  INCIDENT_HISTORY = 'incident_history',\r\n  \r\n  /** Audit logs */\r\n  AUDIT_LOGS = 'audit_logs',\r\n  \r\n  /** Compliance databases */\r\n  COMPLIANCE_DATABASE = 'compliance_database',\r\n  \r\n  /** Risk assessment data */\r\n  RISK_ASSESSMENT = 'risk_assessment',\r\n\r\n  /**\r\n   * External Services and APIs\r\n   */\r\n  \r\n  /** VirusTotal */\r\n  VIRUSTOTAL = 'virustotal',\r\n  \r\n  /** Shodan */\r\n  SHODAN = 'shodan',\r\n  \r\n  /** Censys */\r\n  CENSYS = 'censys',\r\n  \r\n  /** PassiveTotal */\r\n  PASSIVETOTAL = 'passivetotal',\r\n  \r\n  /** URLVoid */\r\n  URLVOID = 'urlvoid',\r\n  \r\n  /** Hybrid Analysis */\r\n  HYBRID_ANALYSIS = 'hybrid_analysis',\r\n\r\n  /**\r\n   * Cloud and SaaS Sources\r\n   */\r\n  \r\n  /** AWS security services */\r\n  AWS_SECURITY = 'aws_security',\r\n  \r\n  /** Azure security services */\r\n  AZURE_SECURITY = 'azure_security',\r\n  \r\n  /** Google Cloud security */\r\n  GCP_SECURITY = 'gcp_security',\r\n  \r\n  /** Office 365 security */\r\n  O365_SECURITY = 'o365_security',\r\n  \r\n  /** Cloud access security brokers */\r\n  CASB = 'casb',\r\n\r\n  /**\r\n   * Sandbox and Analysis Sources\r\n   */\r\n  \r\n  /** Malware sandboxes */\r\n  SANDBOX = 'sandbox',\r\n  \r\n  /** Dynamic analysis platforms */\r\n  DYNAMIC_ANALYSIS = 'dynamic_analysis',\r\n  \r\n  /** Static analysis tools */\r\n  STATIC_ANALYSIS = 'static_analysis',\r\n  \r\n  /** Reverse engineering tools */\r\n  REVERSE_ENGINEERING = 'reverse_engineering',\r\n\r\n  /**\r\n   * Internal Sources\r\n   */\r\n  \r\n  /** Internal security tools */\r\n  INTERNAL_SECURITY_TOOLS = 'internal_security_tools',\r\n  \r\n  /** Custom enrichment rules */\r\n  CUSTOM_RULES = 'custom_rules',\r\n  \r\n  /** Manual analyst input */\r\n  MANUAL_ANALYSIS = 'manual_analysis',\r\n  \r\n  /** Automated correlation */\r\n  AUTOMATED_CORRELATION = 'automated_correlation',\r\n  \r\n  /** Machine learning inference */\r\n  ML_INFERENCE = 'ml_inference',\r\n\r\n  /**\r\n   * Unknown or Other Sources\r\n   */\r\n  \r\n  /** Unknown enrichment source */\r\n  UNKNOWN = 'unknown',\r\n  \r\n  /** Other sources not categorized */\r\n  OTHER = 'other',\r\n}\r\n\r\n/**\r\n * Enrichment Source Utilities\r\n */\r\nexport class EnrichmentSourceUtils {\r\n  /**\r\n   * Get all enrichment sources\r\n   */\r\n  static getAllSources(): EnrichmentSource[] {\r\n    return Object.values(EnrichmentSource);\r\n  }\r\n\r\n  /**\r\n   * Get threat intelligence sources\r\n   */\r\n  static getThreatIntelSources(): EnrichmentSource[] {\r\n    return [\r\n      EnrichmentSource.COMMERCIAL_THREAT_INTEL,\r\n      EnrichmentSource.OSINT,\r\n      EnrichmentSource.GOVERNMENT_INTEL,\r\n      EnrichmentSource.INDUSTRY_SHARING,\r\n      EnrichmentSource.INTERNAL_INTEL,\r\n      EnrichmentSource.TIP,\r\n      EnrichmentSource.MISP,\r\n      EnrichmentSource.STIX_TAXII,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get reputation service sources\r\n   */\r\n  static getReputationSources(): EnrichmentSource[] {\r\n    return [\r\n      EnrichmentSource.IP_REPUTATION,\r\n      EnrichmentSource.DOMAIN_REPUTATION,\r\n      EnrichmentSource.URL_REPUTATION,\r\n      EnrichmentSource.FILE_REPUTATION,\r\n      EnrichmentSource.EMAIL_REPUTATION,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get external API sources\r\n   */\r\n  static getExternalAPISources(): EnrichmentSource[] {\r\n    return [\r\n      EnrichmentSource.VIRUSTOTAL,\r\n      EnrichmentSource.SHODAN,\r\n      EnrichmentSource.CENSYS,\r\n      EnrichmentSource.PASSIVETOTAL,\r\n      EnrichmentSource.URLVOID,\r\n      EnrichmentSource.HYBRID_ANALYSIS,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get internal sources\r\n   */\r\n  static getInternalSources(): EnrichmentSource[] {\r\n    return [\r\n      EnrichmentSource.INTERNAL_SECURITY_TOOLS,\r\n      EnrichmentSource.CUSTOM_RULES,\r\n      EnrichmentSource.MANUAL_ANALYSIS,\r\n      EnrichmentSource.AUTOMATED_CORRELATION,\r\n      EnrichmentSource.ML_INFERENCE,\r\n      EnrichmentSource.HISTORICAL_EVENTS,\r\n      EnrichmentSource.INCIDENT_HISTORY,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get real-time sources (provide immediate enrichment)\r\n   */\r\n  static getRealTimeSources(): EnrichmentSource[] {\r\n    return [\r\n      EnrichmentSource.IP_REPUTATION,\r\n      EnrichmentSource.DOMAIN_REPUTATION,\r\n      EnrichmentSource.URL_REPUTATION,\r\n      EnrichmentSource.DNS_RESOLUTION,\r\n      EnrichmentSource.IP_GEOLOCATION,\r\n      EnrichmentSource.VIRUSTOTAL,\r\n      EnrichmentSource.AUTOMATED_CORRELATION,\r\n      EnrichmentSource.ML_INFERENCE,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get batch processing sources (require scheduled updates)\r\n   */\r\n  static getBatchSources(): EnrichmentSource[] {\r\n    return [\r\n      EnrichmentSource.COMMERCIAL_THREAT_INTEL,\r\n      EnrichmentSource.NVD,\r\n      EnrichmentSource.CVE_DATABASE,\r\n      EnrichmentSource.CMDB,\r\n      EnrichmentSource.ASSET_MANAGEMENT,\r\n      EnrichmentSource.HISTORICAL_EVENTS,\r\n      EnrichmentSource.COMPLIANCE_DATABASE,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Check if source is external\r\n   */\r\n  static isExternal(source: EnrichmentSource): boolean {\r\n    const externalSources = [\r\n      ...EnrichmentSourceUtils.getExternalAPISources(),\r\n      ...EnrichmentSourceUtils.getReputationSources(),\r\n      EnrichmentSource.COMMERCIAL_THREAT_INTEL,\r\n      EnrichmentSource.OSINT,\r\n      EnrichmentSource.GOVERNMENT_INTEL,\r\n      EnrichmentSource.NVD,\r\n      EnrichmentSource.CVE_DATABASE,\r\n    ];\r\n    return externalSources.includes(source);\r\n  }\r\n\r\n  /**\r\n   * Check if source is internal\r\n   */\r\n  static isInternal(source: EnrichmentSource): boolean {\r\n    return EnrichmentSourceUtils.getInternalSources().includes(source);\r\n  }\r\n\r\n  /**\r\n   * Check if source provides real-time data\r\n   */\r\n  static isRealTime(source: EnrichmentSource): boolean {\r\n    return EnrichmentSourceUtils.getRealTimeSources().includes(source);\r\n  }\r\n\r\n  /**\r\n   * Check if source requires batch processing\r\n   */\r\n  static isBatchProcessed(source: EnrichmentSource): boolean {\r\n    return EnrichmentSourceUtils.getBatchSources().includes(source);\r\n  }\r\n\r\n  /**\r\n   * Get reliability score for source (0-100)\r\n   */\r\n  static getReliabilityScore(source: EnrichmentSource): number {\r\n    const scores: Partial<Record<EnrichmentSource, number>> = {\r\n      // High reliability sources\r\n      [EnrichmentSource.GOVERNMENT_INTEL]: 95,\r\n      [EnrichmentSource.NVD]: 95,\r\n      [EnrichmentSource.CVE_DATABASE]: 95,\r\n      [EnrichmentSource.COMMERCIAL_THREAT_INTEL]: 90,\r\n      [EnrichmentSource.CMDB]: 90,\r\n      [EnrichmentSource.ACTIVE_DIRECTORY]: 90,\r\n      \r\n      // Medium-high reliability\r\n      [EnrichmentSource.INDUSTRY_SHARING]: 85,\r\n      [EnrichmentSource.VIRUSTOTAL]: 85,\r\n      [EnrichmentSource.IP_REPUTATION]: 80,\r\n      [EnrichmentSource.DOMAIN_REPUTATION]: 80,\r\n      [EnrichmentSource.INTERNAL_INTEL]: 80,\r\n      \r\n      // Medium reliability\r\n      [EnrichmentSource.OSINT]: 70,\r\n      [EnrichmentSource.SHODAN]: 75,\r\n      [EnrichmentSource.WHOIS]: 75,\r\n      [EnrichmentSource.DNS_RESOLUTION]: 75,\r\n      [EnrichmentSource.ML_MODELS]: 70,\r\n      \r\n      // Lower reliability\r\n      [EnrichmentSource.MANUAL_ANALYSIS]: 60,\r\n      [EnrichmentSource.CUSTOM_RULES]: 65,\r\n      [EnrichmentSource.UNKNOWN]: 30,\r\n      [EnrichmentSource.OTHER]: 40,\r\n    };\r\n\r\n    return scores[source] || 50; // Default medium reliability\r\n  }\r\n\r\n  /**\r\n   * Get data freshness requirement in hours\r\n   */\r\n  static getFreshnessRequirement(source: EnrichmentSource): number {\r\n    const freshness: Partial<Record<EnrichmentSource, number>> = {\r\n      // Real-time requirements (< 1 hour)\r\n      [EnrichmentSource.IP_REPUTATION]: 0.25,      // 15 minutes\r\n      [EnrichmentSource.DOMAIN_REPUTATION]: 0.25,  // 15 minutes\r\n      [EnrichmentSource.DNS_RESOLUTION]: 0.5,      // 30 minutes\r\n      [EnrichmentSource.ML_INFERENCE]: 0.1,        // 6 minutes\r\n      \r\n      // Hourly updates\r\n      [EnrichmentSource.COMMERCIAL_THREAT_INTEL]: 1,\r\n      [EnrichmentSource.VIRUSTOTAL]: 1,\r\n      [EnrichmentSource.IP_GEOLOCATION]: 2,\r\n      \r\n      // Daily updates\r\n      [EnrichmentSource.NVD]: 24,\r\n      [EnrichmentSource.CVE_DATABASE]: 24,\r\n      [EnrichmentSource.OSINT]: 24,\r\n      [EnrichmentSource.WHOIS]: 24,\r\n      \r\n      // Weekly updates\r\n      [EnrichmentSource.CMDB]: 168,\r\n      [EnrichmentSource.ASSET_MANAGEMENT]: 168,\r\n      [EnrichmentSource.COMPLIANCE_DATABASE]: 168,\r\n      \r\n      // Monthly updates\r\n      [EnrichmentSource.HR_SYSTEM]: 720,\r\n      [EnrichmentSource.HISTORICAL_EVENTS]: 720,\r\n    };\r\n\r\n    return freshness[source] || 24; // Default daily updates\r\n  }\r\n\r\n  /**\r\n   * Get cost category for source\r\n   */\r\n  static getCostCategory(source: EnrichmentSource): 'free' | 'low' | 'medium' | 'high' | 'enterprise' {\r\n    const costs: Partial<Record<EnrichmentSource, 'free' | 'low' | 'medium' | 'high' | 'enterprise'>> = {\r\n      // Free sources\r\n      [EnrichmentSource.OSINT]: 'free',\r\n      [EnrichmentSource.NVD]: 'free',\r\n      [EnrichmentSource.CVE_DATABASE]: 'free',\r\n      [EnrichmentSource.DNS_RESOLUTION]: 'free',\r\n      [EnrichmentSource.WHOIS]: 'free',\r\n      \r\n      // Low cost\r\n      [EnrichmentSource.IP_GEOLOCATION]: 'low',\r\n      [EnrichmentSource.VIRUSTOTAL]: 'low',\r\n      [EnrichmentSource.URLVOID]: 'low',\r\n      \r\n      // Medium cost\r\n      [EnrichmentSource.IP_REPUTATION]: 'medium',\r\n      [EnrichmentSource.DOMAIN_REPUTATION]: 'medium',\r\n      [EnrichmentSource.SHODAN]: 'medium',\r\n      [EnrichmentSource.CENSYS]: 'medium',\r\n      \r\n      // High cost\r\n      [EnrichmentSource.COMMERCIAL_THREAT_INTEL]: 'high',\r\n      [EnrichmentSource.PASSIVETOTAL]: 'high',\r\n      [EnrichmentSource.HYBRID_ANALYSIS]: 'high',\r\n      \r\n      // Enterprise cost\r\n      [EnrichmentSource.GOVERNMENT_INTEL]: 'enterprise',\r\n      [EnrichmentSource.TIP]: 'enterprise',\r\n      [EnrichmentSource.CASB]: 'enterprise',\r\n    };\r\n\r\n    return costs[source] || 'medium';\r\n  }\r\n\r\n  /**\r\n   * Get API rate limits (requests per minute)\r\n   */\r\n  static getRateLimit(source: EnrichmentSource): number {\r\n    const rateLimits: Partial<Record<EnrichmentSource, number>> = {\r\n      [EnrichmentSource.VIRUSTOTAL]: 4,        // Free tier\r\n      [EnrichmentSource.SHODAN]: 1,            // Free tier\r\n      [EnrichmentSource.URLVOID]: 1000,        // Generous limit\r\n      [EnrichmentSource.DNS_RESOLUTION]: 1000, // Usually unlimited\r\n      [EnrichmentSource.IP_GEOLOCATION]: 1000, // Varies by provider\r\n      [EnrichmentSource.WHOIS]: 100,           // Conservative estimate\r\n      [EnrichmentSource.COMMERCIAL_THREAT_INTEL]: 1000, // Paid service\r\n      [EnrichmentSource.IP_REPUTATION]: 1000,  // Paid service\r\n      [EnrichmentSource.DOMAIN_REPUTATION]: 1000, // Paid service\r\n    };\r\n\r\n    return rateLimits[source] || 100; // Default conservative limit\r\n  }\r\n\r\n  /**\r\n   * Get supported data types for source\r\n   */\r\n  static getSupportedDataTypes(source: EnrichmentSource): string[] {\r\n    const dataTypes: Partial<Record<EnrichmentSource, string[]>> = {\r\n      [EnrichmentSource.IP_REPUTATION]: ['ip_address', 'reputation_score', 'categories'],\r\n      [EnrichmentSource.DOMAIN_REPUTATION]: ['domain', 'reputation_score', 'categories'],\r\n      [EnrichmentSource.URL_REPUTATION]: ['url', 'reputation_score', 'categories'],\r\n      [EnrichmentSource.FILE_REPUTATION]: ['file_hash', 'reputation_score', 'malware_family'],\r\n      [EnrichmentSource.IP_GEOLOCATION]: ['ip_address', 'country', 'city', 'coordinates'],\r\n      [EnrichmentSource.WHOIS]: ['domain', 'registrar', 'creation_date', 'contacts'],\r\n      [EnrichmentSource.DNS_RESOLUTION]: ['domain', 'ip_addresses', 'record_types'],\r\n      [EnrichmentSource.VIRUSTOTAL]: ['file_hash', 'url', 'domain', 'scan_results'],\r\n      [EnrichmentSource.CVE_DATABASE]: ['cve_id', 'cvss_score', 'description', 'references'],\r\n      [EnrichmentSource.ASSET_MANAGEMENT]: ['asset_id', 'owner', 'location', 'criticality'],\r\n    };\r\n\r\n    return dataTypes[source] || ['generic_data'];\r\n  }\r\n\r\n  /**\r\n   * Get human-readable description\r\n   */\r\n  static getDescription(source: EnrichmentSource): string {\r\n    const descriptions: Partial<Record<EnrichmentSource, string>> = {\r\n      [EnrichmentSource.COMMERCIAL_THREAT_INTEL]: 'Commercial threat intelligence feeds and services',\r\n      [EnrichmentSource.OSINT]: 'Open source intelligence from public sources',\r\n      [EnrichmentSource.GOVERNMENT_INTEL]: 'Government and law enforcement threat intelligence',\r\n      [EnrichmentSource.IP_REPUTATION]: 'IP address reputation and categorization services',\r\n      [EnrichmentSource.DOMAIN_REPUTATION]: 'Domain reputation and categorization services',\r\n      [EnrichmentSource.VIRUSTOTAL]: 'VirusTotal file and URL analysis service',\r\n      [EnrichmentSource.NVD]: 'National Vulnerability Database',\r\n      [EnrichmentSource.CVE_DATABASE]: 'Common Vulnerabilities and Exposures database',\r\n      [EnrichmentSource.CMDB]: 'Configuration Management Database',\r\n      [EnrichmentSource.ACTIVE_DIRECTORY]: 'Microsoft Active Directory services',\r\n      [EnrichmentSource.DNS_RESOLUTION]: 'DNS resolution and lookup services',\r\n      [EnrichmentSource.WHOIS]: 'WHOIS domain registration information',\r\n      [EnrichmentSource.IP_GEOLOCATION]: 'IP address geolocation services',\r\n      [EnrichmentSource.SHODAN]: 'Shodan internet-connected device search engine',\r\n      [EnrichmentSource.ML_MODELS]: 'Machine learning models and inference engines',\r\n      [EnrichmentSource.MANUAL_ANALYSIS]: 'Manual analysis by security analysts',\r\n      [EnrichmentSource.AUTOMATED_CORRELATION]: 'Automated event correlation and analysis',\r\n      [EnrichmentSource.HISTORICAL_EVENTS]: 'Historical security event data',\r\n      [EnrichmentSource.UNKNOWN]: 'Unknown or unidentified enrichment source',\r\n      [EnrichmentSource.OTHER]: 'Other enrichment source not categorized',\r\n    };\r\n\r\n    return descriptions[source] || `${source.replace(/_/g, ' ').toLowerCase()} enrichment source`;\r\n  }\r\n\r\n  /**\r\n   * Get category for source\r\n   */\r\n  static getCategory(source: EnrichmentSource): string {\r\n    if (EnrichmentSourceUtils.getThreatIntelSources().includes(source)) return 'Threat Intelligence';\r\n    if (EnrichmentSourceUtils.getReputationSources().includes(source)) return 'Reputation Services';\r\n    if (EnrichmentSourceUtils.getExternalAPISources().includes(source)) return 'External APIs';\r\n    \r\n    const categories: Record<string, EnrichmentSource[]> = {\r\n      'Geolocation': [EnrichmentSource.IP_GEOLOCATION, EnrichmentSource.ASN_DATABASE, EnrichmentSource.ISP_DATABASE],\r\n      'Vulnerability': [EnrichmentSource.NVD, EnrichmentSource.CVE_DATABASE, EnrichmentSource.CVSS_DATABASE, EnrichmentSource.EXPLOIT_DATABASE],\r\n      'Asset Management': [EnrichmentSource.CMDB, EnrichmentSource.ASSET_MANAGEMENT, EnrichmentSource.INVENTORY_SYSTEM],\r\n      'Identity': [EnrichmentSource.ACTIVE_DIRECTORY, EnrichmentSource.LDAP, EnrichmentSource.HR_SYSTEM],\r\n      'Network': [EnrichmentSource.DNS_RESOLUTION, EnrichmentSource.WHOIS, EnrichmentSource.NETWORK_TOPOLOGY],\r\n      'Analytics': [EnrichmentSource.UBA, EnrichmentSource.ML_MODELS, EnrichmentSource.ANOMALY_DETECTION],\r\n      'Cloud': [EnrichmentSource.AWS_SECURITY, EnrichmentSource.AZURE_SECURITY, EnrichmentSource.GCP_SECURITY],\r\n      'Analysis': [EnrichmentSource.SANDBOX, EnrichmentSource.DYNAMIC_ANALYSIS, EnrichmentSource.STATIC_ANALYSIS],\r\n      'Internal': [EnrichmentSource.INTERNAL_SECURITY_TOOLS, EnrichmentSource.CUSTOM_RULES, EnrichmentSource.MANUAL_ANALYSIS],\r\n    };\r\n    \r\n    for (const [category, sources] of Object.entries(categories)) {\r\n      if (sources.includes(source)) return category;\r\n    }\r\n    \r\n    return 'Other';\r\n  }\r\n\r\n  /**\r\n   * Validate enrichment source\r\n   */\r\n  static isValid(source: string): boolean {\r\n    return Object.values(EnrichmentSource).includes(source as EnrichmentSource);\r\n  }\r\n\r\n  /**\r\n   * Get enrichment source from string (case-insensitive)\r\n   */\r\n  static fromString(value: string): EnrichmentSource | null {\r\n    const normalized = value.toLowerCase().trim().replace(/[^a-z]/g, '_');\r\n    const sources = Object.values(EnrichmentSource);\r\n    return sources.find(source => source === normalized) || null;\r\n  }\r\n\r\n  /**\r\n   * Get enrichment priority based on source reliability and data type\r\n   */\r\n  static getEnrichmentPriority(source: EnrichmentSource, dataType: string): number {\r\n    const baseReliability = EnrichmentSourceUtils.getReliabilityScore(source);\r\n    const isRealTime = EnrichmentSourceUtils.isRealTime(source);\r\n    const costCategory = EnrichmentSourceUtils.getCostCategory(source);\r\n    \r\n    let priority = baseReliability;\r\n    \r\n    // Boost priority for real-time sources\r\n    if (isRealTime) priority += 10;\r\n    \r\n    // Adjust for cost (lower cost = higher priority for equivalent reliability)\r\n    const costAdjustment = {\r\n      'free': 5,\r\n      'low': 3,\r\n      'medium': 0,\r\n      'high': -2,\r\n      'enterprise': -5,\r\n    };\r\n    priority += costAdjustment[costCategory];\r\n    \r\n    // Ensure priority stays within bounds\r\n    return Math.max(0, Math.min(100, priority));\r\n  }\r\n\r\n  /**\r\n   * Get recommended refresh interval in minutes\r\n   */\r\n  static getRefreshInterval(source: EnrichmentSource): number {\r\n    const freshnessHours = EnrichmentSourceUtils.getFreshnessRequirement(source);\r\n    return Math.max(1, freshnessHours * 60); // Convert to minutes, minimum 1 minute\r\n  }\r\n}"], "version": 3}