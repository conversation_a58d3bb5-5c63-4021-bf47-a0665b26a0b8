7c711f9854f74669c87cc7363b21648b
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityHeadersMiddleware = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const csp_config_1 = require("./csp.config");
const hsts_config_1 = require("./hsts.config");
/**
 * Middleware to apply comprehensive security headers to all HTTP responses
 * Implements OWASP security header recommendations
 */
let SecurityHeadersMiddleware = class SecurityHeadersMiddleware {
    constructor(configService, cspConfig, hstsConfig) {
        this.configService = configService;
        this.cspConfig = cspConfig;
        this.hstsConfig = hstsConfig;
    }
    use(req, res, next) {
        try {
            // Content Security Policy
            const cspHeader = this.cspConfig.generateCSPHeader();
            res.setHeader('Content-Security-Policy', cspHeader);
        }
        catch (error) {
            console.error('Failed to generate CSP header:', error);
            // Set a basic fallback CSP
            res.setHeader('Content-Security-Policy', "default-src 'self'");
        }
        try {
            // Strict Transport Security (HSTS)
            if (this.shouldApplyHSTS(req)) {
                const hstsHeader = this.hstsConfig.generateHSTSHeader();
                res.setHeader('Strict-Transport-Security', hstsHeader);
            }
        }
        catch (error) {
            console.error('Failed to generate HSTS header:', error);
            // Set a basic fallback HSTS for HTTPS requests
            if (this.shouldApplyHSTS(req)) {
                res.setHeader('Strict-Transport-Security', 'max-age=31536000');
            }
        }
        // X-Frame-Options - Prevent clickjacking
        res.setHeader('X-Frame-Options', 'DENY');
        // X-Content-Type-Options - Prevent MIME type sniffing
        res.setHeader('X-Content-Type-Options', 'nosniff');
        // X-XSS-Protection - Enable XSS filtering (legacy browsers)
        res.setHeader('X-XSS-Protection', '1; mode=block');
        // Referrer Policy - Control referrer information
        res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        // Permissions Policy - Control browser features
        const permissionsPolicy = this.generatePermissionsPolicy();
        res.setHeader('Permissions-Policy', permissionsPolicy);
        // X-DNS-Prefetch-Control - Control DNS prefetching
        res.setHeader('X-DNS-Prefetch-Control', 'off');
        // X-Download-Options - Prevent file downloads from opening directly
        res.setHeader('X-Download-Options', 'noopen');
        // X-Permitted-Cross-Domain-Policies - Control cross-domain policies
        res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
        // Cache-Control for sensitive endpoints
        if (this.isSensitiveEndpoint(req.path)) {
            res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
            res.setHeader('Pragma', 'no-cache');
            res.setHeader('Expires', '0');
        }
        // Remove server information
        res.removeHeader('X-Powered-By');
        res.removeHeader('Server');
        // Add custom security headers for API identification
        res.setHeader('X-API-Version', this.configService.get('app.version', '1.0.0'));
        res.setHeader('X-Request-ID', req.headers['x-correlation-id'] || this.generateRequestId());
        next();
    }
    /**
     * Determine if HSTS should be applied based on request context
     */
    shouldApplyHSTS(req) {
        // Only apply HSTS for HTTPS requests
        return req.secure || req.headers['x-forwarded-proto'] === 'https';
    }
    /**
     * Generate Permissions Policy header value
     */
    generatePermissionsPolicy() {
        const policies = [
            'accelerometer=()',
            'ambient-light-sensor=()',
            'autoplay=()',
            'battery=()',
            'camera=()',
            'cross-origin-isolated=()',
            'display-capture=()',
            'document-domain=()',
            'encrypted-media=()',
            'execution-while-not-rendered=()',
            'execution-while-out-of-viewport=()',
            'fullscreen=()',
            'geolocation=()',
            'gyroscope=()',
            'keyboard-map=()',
            'magnetometer=()',
            'microphone=()',
            'midi=()',
            'navigation-override=()',
            'payment=()',
            'picture-in-picture=()',
            'publickey-credentials-get=()',
            'screen-wake-lock=()',
            'sync-xhr=()',
            'usb=()',
            'web-share=()',
            'xr-spatial-tracking=()',
        ];
        return policies.join(', ');
    }
    /**
     * Check if the endpoint contains sensitive data
     */
    isSensitiveEndpoint(path) {
        const sensitivePatterns = [
            '/auth',
            '/login',
            '/logout',
            '/admin',
            '/api/v1/auth',
            '/api/v2/auth',
            '/health/detailed',
            '/metrics/sensitive',
        ];
        return sensitivePatterns.some(pattern => path.includes(pattern));
    }
    /**
     * Generate a unique request ID for tracking
     */
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.SecurityHeadersMiddleware = SecurityHeadersMiddleware;
exports.SecurityHeadersMiddleware = SecurityHeadersMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof csp_config_1.CspConfig !== "undefined" && csp_config_1.CspConfig) === "function" ? _b : Object, typeof (_c = typeof hsts_config_1.HstsConfig !== "undefined" && hsts_config_1.HstsConfig) === "function" ? _c : Object])
], SecurityHeadersMiddleware);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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