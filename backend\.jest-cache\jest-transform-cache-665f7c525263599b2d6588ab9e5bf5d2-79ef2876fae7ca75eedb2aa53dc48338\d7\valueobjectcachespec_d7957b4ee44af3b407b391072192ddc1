238603705b2a984868680e7126c96bed
"use strict";
/**
 * Value Object Cache Tests
 *
 * Comprehensive tests for value object caching functionality including
 * LRU eviction, TTL expiration, statistics, and performance validation.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const value_object_cache_1 = require("../../patterns/value-object-cache");
const base_value_object_1 = require("../../value-objects/base-value-object");
// Test value object implementation
class TestValueObject extends base_value_object_1.BaseValueObject {
    constructor(value) {
        super();
        this.value = value;
    }
    getEqualityComponents() {
        return [this.value];
    }
    getHashCode() {
        return this.value.split('').reduce((hash, char) => {
            return ((hash << 5) - hash) + char.charCodeAt(0);
        }, 0);
    }
    toString() {
        return this.value;
    }
}
describe('ValueObjectCache', () => {
    let cache;
    const config = {
        maxSize: 3,
        ttlMs: 1000,
        enableStatistics: true,
    };
    beforeEach(() => {
        cache = new value_object_cache_1.ValueObjectCache(config);
    });
    describe('basic operations', () => {
        it('should store and retrieve value objects', () => {
            // Arrange
            const key = 'test-key';
            const value = new TestValueObject('test-value');
            // Act
            cache.put(key, value);
            const retrieved = cache.get(key);
            // Assert
            expect(retrieved).toBeDefined();
            expect(retrieved?.toString()).toBe('test-value');
        });
        it('should return undefined for non-existent keys', () => {
            // Act
            const result = cache.get('non-existent');
            // Assert
            expect(result).toBeUndefined();
        });
        it('should check key existence correctly', () => {
            // Arrange
            const key = 'test-key';
            const value = new TestValueObject('test-value');
            // Act & Assert
            expect(cache.has(key)).toBe(false);
            cache.put(key, value);
            expect(cache.has(key)).toBe(true);
        });
        it('should delete entries correctly', () => {
            // Arrange
            const key = 'test-key';
            const value = new TestValueObject('test-value');
            cache.put(key, value);
            // Act
            const deleted = cache.delete(key);
            // Assert
            expect(deleted).toBe(true);
            expect(cache.has(key)).toBe(false);
            expect(cache.get(key)).toBeUndefined();
        });
        it('should clear all entries', () => {
            // Arrange
            cache.put('key1', new TestValueObject('value1'));
            cache.put('key2', new TestValueObject('value2'));
            // Act
            cache.clear();
            // Assert
            expect(cache.size()).toBe(0);
            expect(cache.has('key1')).toBe(false);
            expect(cache.has('key2')).toBe(false);
        });
    });
    describe('LRU eviction', () => {
        it('should evict least recently used item when at capacity', () => {
            // Arrange
            cache.put('key1', new TestValueObject('value1'));
            cache.put('key2', new TestValueObject('value2'));
            cache.put('key3', new TestValueObject('value3'));
            // Access key1 to make it recently used
            cache.get('key1');
            // Act - add fourth item, should evict key2 (least recently used)
            cache.put('key4', new TestValueObject('value4'));
            // Assert
            expect(cache.size()).toBe(3);
            expect(cache.has('key1')).toBe(true); // Recently accessed
            expect(cache.has('key2')).toBe(false); // Should be evicted
            expect(cache.has('key3')).toBe(true);
            expect(cache.has('key4')).toBe(true);
        });
        it('should update access time when retrieving items', () => {
            // Arrange
            cache.put('key1', new TestValueObject('value1'));
            cache.put('key2', new TestValueObject('value2'));
            cache.put('key3', new TestValueObject('value3'));
            // Act - access key1 multiple times
            cache.get('key1');
            cache.get('key1');
            cache.get('key1');
            // Add fourth item
            cache.put('key4', new TestValueObject('value4'));
            // Assert - key1 should still be present due to recent access
            expect(cache.has('key1')).toBe(true);
        });
    });
    describe('TTL expiration', () => {
        it('should expire entries after TTL', async () => {
            // Arrange
            const shortTtlCache = new value_object_cache_1.ValueObjectCache({
                maxSize: 10,
                ttlMs: 50, // 50ms TTL
                enableStatistics: true,
            });
            const key = 'test-key';
            const value = new TestValueObject('test-value');
            // Act
            shortTtlCache.put(key, value);
            expect(shortTtlCache.has(key)).toBe(true);
            // Wait for expiration
            await new Promise(resolve => setTimeout(resolve, 100));
            // Assert
            expect(shortTtlCache.has(key)).toBe(false);
            expect(shortTtlCache.get(key)).toBeUndefined();
        });
        it('should cleanup expired entries', async () => {
            // Arrange
            const shortTtlCache = new value_object_cache_1.ValueObjectCache({
                maxSize: 10,
                ttlMs: 50,
                enableStatistics: true,
            });
            shortTtlCache.put('key1', new TestValueObject('value1'));
            shortTtlCache.put('key2', new TestValueObject('value2'));
            // Wait for expiration
            await new Promise(resolve => setTimeout(resolve, 100));
            // Act
            const removedCount = shortTtlCache.cleanup();
            // Assert
            expect(removedCount).toBe(2);
            expect(shortTtlCache.size()).toBe(0);
        });
    });
    describe('getOrCompute functionality', () => {
        it('should return cached value if present', () => {
            // Arrange
            const key = 'test-key';
            const cachedValue = new TestValueObject('cached-value');
            cache.put(key, cachedValue);
            let factoryCalled = false;
            const factory = () => {
                factoryCalled = true;
                return new TestValueObject('factory-value');
            };
            // Act
            const result = cache.getOrCompute(key, factory);
            // Assert
            expect(result.toString()).toBe('cached-value');
            expect(factoryCalled).toBe(false);
        });
        it('should compute and cache value if not present', () => {
            // Arrange
            const key = 'test-key';
            let factoryCalled = false;
            const factory = () => {
                factoryCalled = true;
                return new TestValueObject('factory-value');
            };
            // Act
            const result = cache.getOrCompute(key, factory);
            // Assert
            expect(result.toString()).toBe('factory-value');
            expect(factoryCalled).toBe(true);
            expect(cache.has(key)).toBe(true);
            expect(cache.get(key)?.toString()).toBe('factory-value');
        });
    });
    describe('statistics', () => {
        it('should track cache hits and misses', () => {
            // Arrange
            const key = 'test-key';
            const value = new TestValueObject('test-value');
            // Act
            cache.get('non-existent'); // Miss
            cache.put(key, value);
            cache.get(key); // Hit
            cache.get(key); // Hit
            cache.get('another-non-existent'); // Miss
            const stats = cache.getStatistics();
            // Assert
            expect(stats.hits).toBe(2);
            expect(stats.misses).toBe(2);
            expect(stats.hitRate).toBe(0.5);
        });
        it('should track evictions', () => {
            // Arrange & Act
            cache.put('key1', new TestValueObject('value1'));
            cache.put('key2', new TestValueObject('value2'));
            cache.put('key3', new TestValueObject('value3'));
            cache.put('key4', new TestValueObject('value4')); // Should cause eviction
            const stats = cache.getStatistics();
            // Assert
            expect(stats.evictions).toBe(1);
            expect(stats.size).toBe(3);
            expect(stats.maxSize).toBe(3);
        });
        it('should estimate memory usage', () => {
            // Arrange
            cache.put('key1', new TestValueObject('value1'));
            cache.put('key2', new TestValueObject('value2'));
            // Act
            const stats = cache.getStatistics();
            // Assert
            expect(stats.memoryUsage).toBeGreaterThan(0);
        });
        it('should reset statistics', () => {
            // Arrange
            cache.put('key1', new TestValueObject('value1'));
            cache.get('key1');
            cache.get('non-existent');
            // Act
            cache.resetStatistics();
            const stats = cache.getStatistics();
            // Assert
            expect(stats.hits).toBe(0);
            expect(stats.misses).toBe(0);
            expect(stats.evictions).toBe(0);
        });
    });
    describe('entry analysis', () => {
        it('should return entries by frequency', () => {
            // Arrange
            cache.put('key1', new TestValueObject('value1'));
            cache.put('key2', new TestValueObject('value2'));
            cache.put('key3', new TestValueObject('value3'));
            // Access key2 multiple times
            cache.get('key2');
            cache.get('key2');
            cache.get('key1');
            // Act
            const entriesByFrequency = cache.getEntriesByFrequency();
            // Assert
            expect(entriesByFrequency).toHaveLength(3);
            expect(entriesByFrequency[0].key).toBe('key2'); // Most accessed
            expect(entriesByFrequency[0].accessCount).toBe(3); // Initial put + 2 gets
        });
        it('should return entries by recency', () => {
            // Arrange
            cache.put('key1', new TestValueObject('value1'));
            // Small delay to ensure different timestamps
            setTimeout(() => {
                cache.put('key2', new TestValueObject('value2'));
            }, 10);
            setTimeout(() => {
                // Act
                const entriesByRecency = cache.getEntriesByRecency();
                // Assert
                expect(entriesByRecency).toHaveLength(2);
                expect(entriesByRecency[0].key).toBe('key2'); // Most recent
            }, 20);
        });
        it('should warm up cache with provided entries', () => {
            // Arrange
            const entries = [
                { key: 'key1', value: new TestValueObject('value1') },
                { key: 'key2', value: new TestValueObject('value2') },
            ];
            // Act
            cache.warmUp(entries);
            // Assert
            expect(cache.size()).toBe(2);
            expect(cache.has('key1')).toBe(true);
            expect(cache.has('key2')).toBe(true);
        });
    });
    describe('keys retrieval', () => {
        it('should return all cache keys', () => {
            // Arrange
            cache.put('key1', new TestValueObject('value1'));
            cache.put('key2', new TestValueObject('value2'));
            cache.put('key3', new TestValueObject('value3'));
            // Act
            const keys = cache.keys();
            // Assert
            expect(keys).toHaveLength(3);
            expect(keys).toContain('key1');
            expect(keys).toContain('key2');
            expect(keys).toContain('key3');
        });
    });
});
describe('ValueObjectCacheManager', () => {
    let manager;
    beforeEach(() => {
        manager = value_object_cache_1.ValueObjectCacheManager.getInstance();
        manager.clearAll(); // Clean state for each test
    });
    describe('cache management', () => {
        it('should create and retrieve caches', () => {
            // Act
            const cache1 = manager.getCache('test-cache');
            const cache2 = manager.getCache('test-cache');
            // Assert
            expect(cache1).toBe(cache2); // Should return same instance
        });
        it('should create caches with custom configuration', () => {
            // Arrange
            const config = {
                maxSize: 500,
                ttlMs: 600000,
                enableStatistics: false,
            };
            // Act
            const cache = manager.getCache('custom-cache', config);
            cache.put('test', new TestValueObject('test'));
            const stats = cache.getStatistics();
            // Assert
            expect(stats.maxSize).toBe(500);
        });
        it('should get statistics from all caches', () => {
            // Arrange
            const cache1 = manager.getCache('cache1');
            const cache2 = manager.getCache('cache2');
            cache1.put('key1', new TestValueObject('value1'));
            cache2.put('key2', new TestValueObject('value2'));
            // Act
            const allStats = manager.getAllStatistics();
            // Assert
            expect(Object.keys(allStats)).toHaveLength(2);
            expect(allStats['cache1']).toBeDefined();
            expect(allStats['cache2']).toBeDefined();
            expect(allStats['cache1'].size).toBe(1);
            expect(allStats['cache2'].size).toBe(1);
        });
        it('should clear all caches', () => {
            // Arrange
            const cache1 = manager.getCache('cache1');
            const cache2 = manager.getCache('cache2');
            cache1.put('key1', new TestValueObject('value1'));
            cache2.put('key2', new TestValueObject('value2'));
            // Act
            manager.clearAll();
            // Assert
            expect(cache1.size()).toBe(0);
            expect(cache2.size()).toBe(0);
        });
        it('should cleanup expired entries in all caches', async () => {
            // Arrange
            const cache1 = manager.getCache('cache1', { maxSize: 10, ttlMs: 50 });
            const cache2 = manager.getCache('cache2', { maxSize: 10, ttlMs: 50 });
            cache1.put('key1', new TestValueObject('value1'));
            cache2.put('key2', new TestValueObject('value2'));
            // Wait for expiration
            await new Promise(resolve => setTimeout(resolve, 100));
            // Act
            const totalRemoved = manager.cleanupAll();
            // Assert
            expect(totalRemoved).toBe(2);
        });
        it('should calculate total memory usage', () => {
            // Arrange
            const cache1 = manager.getCache('cache1');
            const cache2 = manager.getCache('cache2');
            cache1.put('key1', new TestValueObject('value1'));
            cache2.put('key2', new TestValueObject('value2'));
            // Act
            const totalMemory = manager.getTotalMemoryUsage();
            // Assert
            expect(totalMemory).toBeGreaterThan(0);
        });
    });
    describe('singleton behavior', () => {
        it('should return same instance', () => {
            // Act
            const instance1 = value_object_cache_1.ValueObjectCacheManager.getInstance();
            const instance2 = value_object_cache_1.ValueObjectCacheManager.getInstance();
            // Assert
            expect(instance1).toBe(instance2);
        });
    });
});
describe('Performance Tests', () => {
    describe('cache performance', () => {
        it('should handle high-volume operations efficiently', () => {
            // Arrange
            const cache = new value_object_cache_1.ValueObjectCache({
                maxSize: 1000,
                ttlMs: 300000,
                enableStatistics: true,
            });
            const startTime = Date.now();
            // Act - Perform 10,000 operations
            for (let i = 0; i < 10000; i++) {
                const key = `key-${i % 100}`; // Reuse keys to test cache hits
                const value = new TestValueObject(`value-${i}`);
                if (i % 2 === 0) {
                    cache.put(key, value);
                }
                else {
                    cache.get(key);
                }
            }
            const endTime = Date.now();
            const duration = endTime - startTime;
            // Assert
            expect(duration).toBeLessThan(1000); // Should complete in under 1 second
            const stats = cache.getStatistics();
            expect(stats.hitRate).toBeGreaterThan(0); // Should have cache hits
        });
        it('should maintain performance with frequent evictions', () => {
            // Arrange
            const cache = new value_object_cache_1.ValueObjectCache({
                maxSize: 10, // Small cache to force evictions
                ttlMs: 300000,
                enableStatistics: true,
            });
            const startTime = Date.now();
            // Act - Add many items to force evictions
            for (let i = 0; i < 1000; i++) {
                cache.put(`key-${i}`, new TestValueObject(`value-${i}`));
            }
            const endTime = Date.now();
            const duration = endTime - startTime;
            // Assert
            expect(duration).toBeLessThan(500); // Should handle evictions efficiently
            expect(cache.size()).toBe(10); // Should maintain max size
            const stats = cache.getStatistics();
            expect(stats.evictions).toBeGreaterThan(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxzaGFyZWQta2VybmVsXFxfX3Rlc3RzX19cXHBhdHRlcm5zXFx2YWx1ZS1vYmplY3QtY2FjaGUuc3BlYy50cyIsIm1hcHBpbmdzIjoiO0FBQUE7Ozs7O0dBS0c7O0FBRUgsMEVBQTJHO0FBQzNHLDZFQUF3RTtBQUV4RSxtQ0FBbUM7QUFDbkMsTUFBTSxlQUFnQixTQUFRLG1DQUFlO0lBQzNDLFlBQTZCLEtBQWE7UUFDeEMsS0FBSyxFQUFFLENBQUM7UUFEbUIsVUFBSyxHQUFMLEtBQUssQ0FBUTtJQUUxQyxDQUFDO0lBRVMscUJBQXFCO1FBQzdCLE9BQU8sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7SUFDdEIsQ0FBQztJQUVELFdBQVc7UUFDVCxPQUFPLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLEVBQUUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksRUFBRSxJQUFJLEVBQUUsRUFBRTtZQUNoRCxPQUFPLENBQUMsQ0FBQyxJQUFJLElBQUksQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNuRCxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7SUFDUixDQUFDO0lBRUQsUUFBUTtRQUNOLE9BQU8sSUFBSSxDQUFDLEtBQUssQ0FBQztJQUNwQixDQUFDO0NBQ0Y7QUFFRCxRQUFRLENBQUMsa0JBQWtCLEVBQUUsR0FBRyxFQUFFO0lBQ2hDLElBQUksS0FBd0MsQ0FBQztJQUM3QyxNQUFNLE1BQU0sR0FBZ0I7UUFDMUIsT0FBTyxFQUFFLENBQUM7UUFDVixLQUFLLEVBQUUsSUFBSTtRQUNYLGdCQUFnQixFQUFFLElBQUk7S0FDdkIsQ0FBQztJQUVGLFVBQVUsQ0FBQyxHQUFHLEVBQUU7UUFDZCxLQUFLLEdBQUcsSUFBSSxxQ0FBZ0IsQ0FBQyxNQUFNLENBQUMsQ0FBQztJQUN2QyxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxrQkFBa0IsRUFBRSxHQUFHLEVBQUU7UUFDaEMsRUFBRSxDQUFDLHlDQUF5QyxFQUFFLEdBQUcsRUFBRTtZQUNqRCxVQUFVO1lBQ1YsTUFBTSxHQUFHLEdBQUcsVUFBVSxDQUFDO1lBQ3ZCLE1BQU0sS0FBSyxHQUFHLElBQUksZUFBZSxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRWhELE1BQU07WUFDTixLQUFLLENBQUMsR0FBRyxDQUFDLEdBQUcsRUFBRSxLQUFLLENBQUMsQ0FBQztZQUN0QixNQUFNLFNBQVMsR0FBRyxLQUFLLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBRWpDLFNBQVM7WUFDVCxNQUFNLENBQUMsU0FBUyxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7WUFDaEMsTUFBTSxDQUFDLFNBQVMsRUFBRSxRQUFRLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztRQUNuRCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQywrQ0FBK0MsRUFBRSxHQUFHLEVBQUU7WUFDdkQsTUFBTTtZQUNOLE1BQU0sTUFBTSxHQUFHLEtBQUssQ0FBQyxHQUFHLENBQUMsY0FBYyxDQUFDLENBQUM7WUFFekMsU0FBUztZQUNULE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxhQUFhLEVBQUUsQ0FBQztRQUNqQyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxzQ0FBc0MsRUFBRSxHQUFHLEVBQUU7WUFDOUMsVUFBVTtZQUNWLE1BQU0sR0FBRyxHQUFHLFVBQVUsQ0FBQztZQUN2QixNQUFNLEtBQUssR0FBRyxJQUFJLGVBQWUsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUVoRCxlQUFlO1lBQ2YsTUFBTSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDbkMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxHQUFHLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDdEIsTUFBTSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDcEMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsaUNBQWlDLEVBQUUsR0FBRyxFQUFFO1lBQ3pDLFVBQVU7WUFDVixNQUFNLEdBQUcsR0FBRyxVQUFVLENBQUM7WUFDdkIsTUFBTSxLQUFLLEdBQUcsSUFBSSxlQUFlLENBQUMsWUFBWSxDQUFDLENBQUM7WUFDaEQsS0FBSyxDQUFDLEdBQUcsQ0FBQyxHQUFHLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFFdEIsTUFBTTtZQUNOLE1BQU0sT0FBTyxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUM7WUFFbEMsU0FBUztZQUNULE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDM0IsTUFBTSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDbkMsTUFBTSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxhQUFhLEVBQUUsQ0FBQztRQUN6QyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQywwQkFBMEIsRUFBRSxHQUFHLEVBQUU7WUFDbEMsVUFBVTtZQUNWLEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7WUFDakQsS0FBSyxDQUFDLEdBQUcsQ0FBQyxNQUFNLEVBQUUsSUFBSSxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztZQUVqRCxNQUFNO1lBQ04sS0FBSyxDQUFDLEtBQUssRUFBRSxDQUFDO1lBRWQsU0FBUztZQUNULE1BQU0sQ0FBQyxLQUFLLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDN0IsTUFBTSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDdEMsTUFBTSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDeEMsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxjQUFjLEVBQUUsR0FBRyxFQUFFO1FBQzVCLEVBQUUsQ0FBQyx3REFBd0QsRUFBRSxHQUFHLEVBQUU7WUFDaEUsVUFBVTtZQUNWLEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7WUFDakQsS0FBSyxDQUFDLEdBQUcsQ0FBQyxNQUFNLEVBQUUsSUFBSSxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztZQUNqRCxLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxJQUFJLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBRWpELHVDQUF1QztZQUN2QyxLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBRWxCLGlFQUFpRTtZQUNqRSxLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxJQUFJLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBRWpELFNBQVM7WUFDVCxNQUFNLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQzdCLE1BQU0sQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsb0JBQW9CO1lBQzFELE1BQU0sQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsb0JBQW9CO1lBQzNELE1BQU0sQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3JDLE1BQU0sQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQ3ZDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGlEQUFpRCxFQUFFLEdBQUcsRUFBRTtZQUN6RCxVQUFVO1lBQ1YsS0FBSyxDQUFDLEdBQUcsQ0FBQyxNQUFNLEVBQUUsSUFBSSxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztZQUNqRCxLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxJQUFJLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBQ2pELEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7WUFFakQsbUNBQW1DO1lBQ25DLEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDbEIsS0FBSyxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUNsQixLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBRWxCLGtCQUFrQjtZQUNsQixLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxJQUFJLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBRWpELDZEQUE2RDtZQUM3RCxNQUFNLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUN2QyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGdCQUFnQixFQUFFLEdBQUcsRUFBRTtRQUM5QixFQUFFLENBQUMsaUNBQWlDLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDL0MsVUFBVTtZQUNWLE1BQU0sYUFBYSxHQUFHLElBQUkscUNBQWdCLENBQWtCO2dCQUMxRCxPQUFPLEVBQUUsRUFBRTtnQkFDWCxLQUFLLEVBQUUsRUFBRSxFQUFFLFdBQVc7Z0JBQ3RCLGdCQUFnQixFQUFFLElBQUk7YUFDdkIsQ0FBQyxDQUFDO1lBRUgsTUFBTSxHQUFHLEdBQUcsVUFBVSxDQUFDO1lBQ3ZCLE1BQU0sS0FBSyxHQUFHLElBQUksZUFBZSxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRWhELE1BQU07WUFDTixhQUFhLENBQUMsR0FBRyxDQUFDLEdBQUcsRUFBRSxLQUFLLENBQUMsQ0FBQztZQUM5QixNQUFNLENBQUMsYUFBYSxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUUxQyxzQkFBc0I7WUFDdEIsTUFBTSxJQUFJLE9BQU8sQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDLFVBQVUsQ0FBQyxPQUFPLEVBQUUsR0FBRyxDQUFDLENBQUMsQ0FBQztZQUV2RCxTQUFTO1lBQ1QsTUFBTSxDQUFDLGFBQWEsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDM0MsTUFBTSxDQUFDLGFBQWEsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxhQUFhLEVBQUUsQ0FBQztRQUNqRCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxnQ0FBZ0MsRUFBRSxLQUFLLElBQUksRUFBRTtZQUM5QyxVQUFVO1lBQ1YsTUFBTSxhQUFhLEdBQUcsSUFBSSxxQ0FBZ0IsQ0FBa0I7Z0JBQzFELE9BQU8sRUFBRSxFQUFFO2dCQUNYLEtBQUssRUFBRSxFQUFFO2dCQUNULGdCQUFnQixFQUFFLElBQUk7YUFDdkIsQ0FBQyxDQUFDO1lBRUgsYUFBYSxDQUFDLEdBQUcsQ0FBQyxNQUFNLEVBQUUsSUFBSSxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztZQUN6RCxhQUFhLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxJQUFJLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBRXpELHNCQUFzQjtZQUN0QixNQUFNLElBQUksT0FBTyxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUMsVUFBVSxDQUFDLE9BQU8sRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDO1lBRXZELE1BQU07WUFDTixNQUFNLFlBQVksR0FBRyxhQUFhLENBQUMsT0FBTyxFQUFFLENBQUM7WUFFN0MsU0FBUztZQUNULE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDN0IsTUFBTSxDQUFDLGFBQWEsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN2QyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLDRCQUE0QixFQUFFLEdBQUcsRUFBRTtRQUMxQyxFQUFFLENBQUMsdUNBQXVDLEVBQUUsR0FBRyxFQUFFO1lBQy9DLFVBQVU7WUFDVixNQUFNLEdBQUcsR0FBRyxVQUFVLENBQUM7WUFDdkIsTUFBTSxXQUFXLEdBQUcsSUFBSSxlQUFlLENBQUMsY0FBYyxDQUFDLENBQUM7WUFDeEQsS0FBSyxDQUFDLEdBQUcsQ0FBQyxHQUFHLEVBQUUsV0FBVyxDQUFDLENBQUM7WUFFNUIsSUFBSSxhQUFhLEdBQUcsS0FBSyxDQUFDO1lBQzFCLE1BQU0sT0FBTyxHQUFHLEdBQUcsRUFBRTtnQkFDbkIsYUFBYSxHQUFHLElBQUksQ0FBQztnQkFDckIsT0FBTyxJQUFJLGVBQWUsQ0FBQyxlQUFlLENBQUMsQ0FBQztZQUM5QyxDQUFDLENBQUM7WUFFRixNQUFNO1lBQ04sTUFBTSxNQUFNLEdBQUcsS0FBSyxDQUFDLFlBQVksQ0FBQyxHQUFHLEVBQUUsT0FBTyxDQUFDLENBQUM7WUFFaEQsU0FBUztZQUNULE1BQU0sQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUM7WUFDL0MsTUFBTSxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNwQyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQywrQ0FBK0MsRUFBRSxHQUFHLEVBQUU7WUFDdkQsVUFBVTtZQUNWLE1BQU0sR0FBRyxHQUFHLFVBQVUsQ0FBQztZQUN2QixJQUFJLGFBQWEsR0FBRyxLQUFLLENBQUM7WUFDMUIsTUFBTSxPQUFPLEdBQUcsR0FBRyxFQUFFO2dCQUNuQixhQUFhLEdBQUcsSUFBSSxDQUFDO2dCQUNyQixPQUFPLElBQUksZUFBZSxDQUFDLGVBQWUsQ0FBQyxDQUFDO1lBQzlDLENBQUMsQ0FBQztZQUVGLE1BQU07WUFDTixNQUFNLE1BQU0sR0FBRyxLQUFLLENBQUMsWUFBWSxDQUFDLEdBQUcsRUFBRSxPQUFPLENBQUMsQ0FBQztZQUVoRCxTQUFTO1lBQ1QsTUFBTSxDQUFDLE1BQU0sQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztZQUNoRCxNQUFNLENBQUMsYUFBYSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ2pDLE1BQU0sQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ2xDLE1BQU0sQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxFQUFFLFFBQVEsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDO1FBQzNELENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsWUFBWSxFQUFFLEdBQUcsRUFBRTtRQUMxQixFQUFFLENBQUMsb0NBQW9DLEVBQUUsR0FBRyxFQUFFO1lBQzVDLFVBQVU7WUFDVixNQUFNLEdBQUcsR0FBRyxVQUFVLENBQUM7WUFDdkIsTUFBTSxLQUFLLEdBQUcsSUFBSSxlQUFlLENBQUMsWUFBWSxDQUFDLENBQUM7WUFFaEQsTUFBTTtZQUNOLEtBQUssQ0FBQyxHQUFHLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQyxPQUFPO1lBQ2xDLEtBQUssQ0FBQyxHQUFHLENBQUMsR0FBRyxFQUFFLEtBQUssQ0FBQyxDQUFDO1lBQ3RCLEtBQUssQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxNQUFNO1lBQ3RCLEtBQUssQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxNQUFNO1lBQ3RCLEtBQUssQ0FBQyxHQUFHLENBQUMsc0JBQXNCLENBQUMsQ0FBQyxDQUFDLE9BQU87WUFFMUMsTUFBTSxLQUFLLEdBQUcsS0FBSyxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBRXBDLFNBQVM7WUFDVCxNQUFNLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMzQixNQUFNLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUM3QixNQUFNLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztRQUNsQyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx3QkFBd0IsRUFBRSxHQUFHLEVBQUU7WUFDaEMsZ0JBQWdCO1lBQ2hCLEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7WUFDakQsS0FBSyxDQUFDLEdBQUcsQ0FBQyxNQUFNLEVBQUUsSUFBSSxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztZQUNqRCxLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxJQUFJLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBQ2pELEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyx3QkFBd0I7WUFFMUUsTUFBTSxLQUFLLEdBQUcsS0FBSyxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBRXBDLFNBQVM7WUFDVCxNQUFNLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNoQyxNQUFNLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMzQixNQUFNLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNoQyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw4QkFBOEIsRUFBRSxHQUFHLEVBQUU7WUFDdEMsVUFBVTtZQUNWLEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7WUFDakQsS0FBSyxDQUFDLEdBQUcsQ0FBQyxNQUFNLEVBQUUsSUFBSSxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztZQUVqRCxNQUFNO1lBQ04sTUFBTSxLQUFLLEdBQUcsS0FBSyxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBRXBDLFNBQVM7WUFDVCxNQUFNLENBQUMsS0FBSyxDQUFDLFdBQVcsQ0FBQyxDQUFDLGVBQWUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUMvQyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx5QkFBeUIsRUFBRSxHQUFHLEVBQUU7WUFDakMsVUFBVTtZQUNWLEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7WUFDakQsS0FBSyxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUNsQixLQUFLLENBQUMsR0FBRyxDQUFDLGNBQWMsQ0FBQyxDQUFDO1lBRTFCLE1BQU07WUFDTixLQUFLLENBQUMsZUFBZSxFQUFFLENBQUM7WUFDeEIsTUFBTSxLQUFLLEdBQUcsS0FBSyxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBRXBDLFNBQVM7WUFDVCxNQUFNLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMzQixNQUFNLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUM3QixNQUFNLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNsQyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGdCQUFnQixFQUFFLEdBQUcsRUFBRTtRQUM5QixFQUFFLENBQUMsb0NBQW9DLEVBQUUsR0FBRyxFQUFFO1lBQzVDLFVBQVU7WUFDVixLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxJQUFJLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBQ2pELEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7WUFDakQsS0FBSyxDQUFDLEdBQUcsQ0FBQyxNQUFNLEVBQUUsSUFBSSxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztZQUVqRCw2QkFBNkI7WUFDN0IsS0FBSyxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUNsQixLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQ2xCLEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLENBQUM7WUFFbEIsTUFBTTtZQUNOLE1BQU0sa0JBQWtCLEdBQUcsS0FBSyxDQUFDLHFCQUFxQixFQUFFLENBQUM7WUFFekQsU0FBUztZQUNULE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMzQyxNQUFNLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsZ0JBQWdCO1lBQ2hFLE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsQ0FBQyxXQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyx1QkFBdUI7UUFDNUUsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsa0NBQWtDLEVBQUUsR0FBRyxFQUFFO1lBQzFDLFVBQVU7WUFDVixLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxJQUFJLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBRWpELDZDQUE2QztZQUM3QyxVQUFVLENBQUMsR0FBRyxFQUFFO2dCQUNkLEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7WUFDbkQsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO1lBRVAsVUFBVSxDQUFDLEdBQUcsRUFBRTtnQkFDZCxNQUFNO2dCQUNOLE1BQU0sZ0JBQWdCLEdBQUcsS0FBSyxDQUFDLG1CQUFtQixFQUFFLENBQUM7Z0JBRXJELFNBQVM7Z0JBQ1QsTUFBTSxDQUFDLGdCQUFnQixDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO2dCQUN6QyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsY0FBYztZQUM5RCxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFDVCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw0Q0FBNEMsRUFBRSxHQUFHLEVBQUU7WUFDcEQsVUFBVTtZQUNWLE1BQU0sT0FBTyxHQUFHO2dCQUNkLEVBQUUsR0FBRyxFQUFFLE1BQU0sRUFBRSxLQUFLLEVBQUUsSUFBSSxlQUFlLENBQUMsUUFBUSxDQUFDLEVBQUU7Z0JBQ3JELEVBQUUsR0FBRyxFQUFFLE1BQU0sRUFBRSxLQUFLLEVBQUUsSUFBSSxlQUFlLENBQUMsUUFBUSxDQUFDLEVBQUU7YUFDdEQsQ0FBQztZQUVGLE1BQU07WUFDTixLQUFLLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRXRCLFNBQVM7WUFDVCxNQUFNLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQzdCLE1BQU0sQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3JDLE1BQU0sQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQ3ZDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsZ0JBQWdCLEVBQUUsR0FBRyxFQUFFO1FBQzlCLEVBQUUsQ0FBQyw4QkFBOEIsRUFBRSxHQUFHLEVBQUU7WUFDdEMsVUFBVTtZQUNWLEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7WUFDakQsS0FBSyxDQUFDLEdBQUcsQ0FBQyxNQUFNLEVBQUUsSUFBSSxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztZQUNqRCxLQUFLLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxJQUFJLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBRWpELE1BQU07WUFDTixNQUFNLElBQUksR0FBRyxLQUFLLENBQUMsSUFBSSxFQUFFLENBQUM7WUFFMUIsU0FBUztZQUNULE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDN0IsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUMvQixNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQy9CLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUM7UUFDakMsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztBQUNMLENBQUMsQ0FBQyxDQUFDO0FBRUgsUUFBUSxDQUFDLHlCQUF5QixFQUFFLEdBQUcsRUFBRTtJQUN2QyxJQUFJLE9BQWdDLENBQUM7SUFFckMsVUFBVSxDQUFDLEdBQUcsRUFBRTtRQUNkLE9BQU8sR0FBRyw0Q0FBdUIsQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUNoRCxPQUFPLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyw0QkFBNEI7SUFDbEQsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsa0JBQWtCLEVBQUUsR0FBRyxFQUFFO1FBQ2hDLEVBQUUsQ0FBQyxtQ0FBbUMsRUFBRSxHQUFHLEVBQUU7WUFDM0MsTUFBTTtZQUNOLE1BQU0sTUFBTSxHQUFHLE9BQU8sQ0FBQyxRQUFRLENBQWtCLFlBQVksQ0FBQyxDQUFDO1lBQy9ELE1BQU0sTUFBTSxHQUFHLE9BQU8sQ0FBQyxRQUFRLENBQWtCLFlBQVksQ0FBQyxDQUFDO1lBRS9ELFNBQVM7WUFDVCxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsOEJBQThCO1FBQzdELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGdEQUFnRCxFQUFFLEdBQUcsRUFBRTtZQUN4RCxVQUFVO1lBQ1YsTUFBTSxNQUFNLEdBQWdCO2dCQUMxQixPQUFPLEVBQUUsR0FBRztnQkFDWixLQUFLLEVBQUUsTUFBTTtnQkFDYixnQkFBZ0IsRUFBRSxLQUFLO2FBQ3hCLENBQUM7WUFFRixNQUFNO1lBQ04sTUFBTSxLQUFLLEdBQUcsT0FBTyxDQUFDLFFBQVEsQ0FBa0IsY0FBYyxFQUFFLE1BQU0sQ0FBQyxDQUFDO1lBQ3hFLEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUM7WUFFL0MsTUFBTSxLQUFLLEdBQUcsS0FBSyxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBRXBDLFNBQVM7WUFDVCxNQUFNLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztRQUNsQyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx1Q0FBdUMsRUFBRSxHQUFHLEVBQUU7WUFDL0MsVUFBVTtZQUNWLE1BQU0sTUFBTSxHQUFHLE9BQU8sQ0FBQyxRQUFRLENBQWtCLFFBQVEsQ0FBQyxDQUFDO1lBQzNELE1BQU0sTUFBTSxHQUFHLE9BQU8sQ0FBQyxRQUFRLENBQWtCLFFBQVEsQ0FBQyxDQUFDO1lBRTNELE1BQU0sQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7WUFDbEQsTUFBTSxDQUFDLEdBQUcsQ0FBQyxNQUFNLEVBQUUsSUFBSSxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztZQUVsRCxNQUFNO1lBQ04sTUFBTSxRQUFRLEdBQUcsT0FBTyxDQUFDLGdCQUFnQixFQUFFLENBQUM7WUFFNUMsU0FBUztZQUNULE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQzlDLE1BQU0sQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUN6QyxNQUFNLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7WUFDekMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDeEMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDMUMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMseUJBQXlCLEVBQUUsR0FBRyxFQUFFO1lBQ2pDLFVBQVU7WUFDVixNQUFNLE1BQU0sR0FBRyxPQUFPLENBQUMsUUFBUSxDQUFrQixRQUFRLENBQUMsQ0FBQztZQUMzRCxNQUFNLE1BQU0sR0FBRyxPQUFPLENBQUMsUUFBUSxDQUFrQixRQUFRLENBQUMsQ0FBQztZQUUzRCxNQUFNLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxJQUFJLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBQ2xELE1BQU0sQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7WUFFbEQsTUFBTTtZQUNOLE9BQU8sQ0FBQyxRQUFRLEVBQUUsQ0FBQztZQUVuQixTQUFTO1lBQ1QsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUM5QixNQUFNLENBQUMsTUFBTSxDQUFDLElBQUksRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ2hDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDhDQUE4QyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQzVELFVBQVU7WUFDVixNQUFNLE1BQU0sR0FBRyxPQUFPLENBQUMsUUFBUSxDQUFrQixRQUFRLEVBQUUsRUFBRSxPQUFPLEVBQUUsRUFBRSxFQUFFLEtBQUssRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDO1lBQ3ZGLE1BQU0sTUFBTSxHQUFHLE9BQU8sQ0FBQyxRQUFRLENBQWtCLFFBQVEsRUFBRSxFQUFFLE9BQU8sRUFBRSxFQUFFLEVBQUUsS0FBSyxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUM7WUFFdkYsTUFBTSxDQUFDLEdBQUcsQ0FBQyxNQUFNLEVBQUUsSUFBSSxlQUFlLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztZQUNsRCxNQUFNLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxJQUFJLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBRWxELHNCQUFzQjtZQUN0QixNQUFNLElBQUksT0FBTyxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUMsVUFBVSxDQUFDLE9BQU8sRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDO1lBRXZELE1BQU07WUFDTixNQUFNLFlBQVksR0FBRyxPQUFPLENBQUMsVUFBVSxFQUFFLENBQUM7WUFFMUMsU0FBUztZQUNULE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDL0IsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMscUNBQXFDLEVBQUUsR0FBRyxFQUFFO1lBQzdDLFVBQVU7WUFDVixNQUFNLE1BQU0sR0FBRyxPQUFPLENBQUMsUUFBUSxDQUFrQixRQUFRLENBQUMsQ0FBQztZQUMzRCxNQUFNLE1BQU0sR0FBRyxPQUFPLENBQUMsUUFBUSxDQUFrQixRQUFRLENBQUMsQ0FBQztZQUUzRCxNQUFNLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxJQUFJLGVBQWUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1lBQ2xELE1BQU0sQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUM7WUFFbEQsTUFBTTtZQUNOLE1BQU0sV0FBVyxHQUFHLE9BQU8sQ0FBQyxtQkFBbUIsRUFBRSxDQUFDO1lBRWxELFNBQVM7WUFDVCxNQUFNLENBQUMsV0FBVyxDQUFDLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3pDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsb0JBQW9CLEVBQUUsR0FBRyxFQUFFO1FBQ2xDLEVBQUUsQ0FBQyw2QkFBNkIsRUFBRSxHQUFHLEVBQUU7WUFDckMsTUFBTTtZQUNOLE1BQU0sU0FBUyxHQUFHLDRDQUF1QixDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3hELE1BQU0sU0FBUyxHQUFHLDRDQUF1QixDQUFDLFdBQVcsRUFBRSxDQUFDO1lBRXhELFNBQVM7WUFDVCxNQUFNLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBQ3BDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQztBQUVILFFBQVEsQ0FBQyxtQkFBbUIsRUFBRSxHQUFHLEVBQUU7SUFDakMsUUFBUSxDQUFDLG1CQUFtQixFQUFFLEdBQUcsRUFBRTtRQUNqQyxFQUFFLENBQUMsa0RBQWtELEVBQUUsR0FBRyxFQUFFO1lBQzFELFVBQVU7WUFDVixNQUFNLEtBQUssR0FBRyxJQUFJLHFDQUFnQixDQUFrQjtnQkFDbEQsT0FBTyxFQUFFLElBQUk7Z0JBQ2IsS0FBSyxFQUFFLE1BQU07Z0JBQ2IsZ0JBQWdCLEVBQUUsSUFBSTthQUN2QixDQUFDLENBQUM7WUFFSCxNQUFNLFNBQVMsR0FBRyxJQUFJLENBQUMsR0FBRyxFQUFFLENBQUM7WUFFN0Isa0NBQWtDO1lBQ2xDLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxLQUFLLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQztnQkFDL0IsTUFBTSxHQUFHLEdBQUcsT0FBTyxDQUFDLEdBQUcsR0FBRyxFQUFFLENBQUMsQ0FBQyxnQ0FBZ0M7Z0JBQzlELE1BQU0sS0FBSyxHQUFHLElBQUksZUFBZSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsQ0FBQztnQkFFaEQsSUFBSSxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsRUFBRSxDQUFDO29CQUNoQixLQUFLLENBQUMsR0FBRyxDQUFDLEdBQUcsRUFBRSxLQUFLLENBQUMsQ0FBQztnQkFDeEIsQ0FBQztxQkFBTSxDQUFDO29CQUNOLEtBQUssQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUM7Z0JBQ2pCLENBQUM7WUFDSCxDQUFDO1lBRUQsTUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFDO1lBQzNCLE1BQU0sUUFBUSxHQUFHLE9BQU8sR0FBRyxTQUFTLENBQUM7WUFFckMsU0FBUztZQUNULE1BQU0sQ0FBQyxRQUFRLENBQUMsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxvQ0FBb0M7WUFFekUsTUFBTSxLQUFLLEdBQUcsS0FBSyxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBQ3BDLE1BQU0sQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMseUJBQXlCO1FBQ3JFLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHFEQUFxRCxFQUFFLEdBQUcsRUFBRTtZQUM3RCxVQUFVO1lBQ1YsTUFBTSxLQUFLLEdBQUcsSUFBSSxxQ0FBZ0IsQ0FBa0I7Z0JBQ2xELE9BQU8sRUFBRSxFQUFFLEVBQUUsaUNBQWlDO2dCQUM5QyxLQUFLLEVBQUUsTUFBTTtnQkFDYixnQkFBZ0IsRUFBRSxJQUFJO2FBQ3ZCLENBQUMsQ0FBQztZQUVILE1BQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxHQUFHLEVBQUUsQ0FBQztZQUU3QiwwQ0FBMEM7WUFDMUMsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDO2dCQUM5QixLQUFLLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQyxFQUFFLEVBQUUsSUFBSSxlQUFlLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7WUFDM0QsQ0FBQztZQUVELE1BQU0sT0FBTyxHQUFHLElBQUksQ0FBQyxHQUFHLEVBQUUsQ0FBQztZQUMzQixNQUFNLFFBQVEsR0FBRyxPQUFPLEdBQUcsU0FBUyxDQUFDO1lBRXJDLFNBQVM7WUFDVCxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsWUFBWSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsc0NBQXNDO1lBQzFFLE1BQU0sQ0FBQyxLQUFLLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQywyQkFBMkI7WUFFMUQsTUFBTSxLQUFLLEdBQUcsS0FBSyxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBQ3BDLE1BQU0sQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzdDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXHNoYXJlZC1rZXJuZWxcXF9fdGVzdHNfX1xccGF0dGVybnNcXHZhbHVlLW9iamVjdC1jYWNoZS5zcGVjLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxyXG4gKiBWYWx1ZSBPYmplY3QgQ2FjaGUgVGVzdHNcclxuICogXHJcbiAqIENvbXByZWhlbnNpdmUgdGVzdHMgZm9yIHZhbHVlIG9iamVjdCBjYWNoaW5nIGZ1bmN0aW9uYWxpdHkgaW5jbHVkaW5nXHJcbiAqIExSVSBldmljdGlvbiwgVFRMIGV4cGlyYXRpb24sIHN0YXRpc3RpY3MsIGFuZCBwZXJmb3JtYW5jZSB2YWxpZGF0aW9uLlxyXG4gKi9cclxuXHJcbmltcG9ydCB7IFZhbHVlT2JqZWN0Q2FjaGUsIFZhbHVlT2JqZWN0Q2FjaGVNYW5hZ2VyLCBDYWNoZUNvbmZpZyB9IGZyb20gJy4uLy4uL3BhdHRlcm5zL3ZhbHVlLW9iamVjdC1jYWNoZSc7XHJcbmltcG9ydCB7IEJhc2VWYWx1ZU9iamVjdCB9IGZyb20gJy4uLy4uL3ZhbHVlLW9iamVjdHMvYmFzZS12YWx1ZS1vYmplY3QnO1xyXG5cclxuLy8gVGVzdCB2YWx1ZSBvYmplY3QgaW1wbGVtZW50YXRpb25cclxuY2xhc3MgVGVzdFZhbHVlT2JqZWN0IGV4dGVuZHMgQmFzZVZhbHVlT2JqZWN0IHtcclxuICBjb25zdHJ1Y3Rvcihwcml2YXRlIHJlYWRvbmx5IHZhbHVlOiBzdHJpbmcpIHtcclxuICAgIHN1cGVyKCk7XHJcbiAgfVxyXG5cclxuICBwcm90ZWN0ZWQgZ2V0RXF1YWxpdHlDb21wb25lbnRzKCk6IGFueVtdIHtcclxuICAgIHJldHVybiBbdGhpcy52YWx1ZV07XHJcbiAgfVxyXG5cclxuICBnZXRIYXNoQ29kZSgpOiBudW1iZXIge1xyXG4gICAgcmV0dXJuIHRoaXMudmFsdWUuc3BsaXQoJycpLnJlZHVjZSgoaGFzaCwgY2hhcikgPT4ge1xyXG4gICAgICByZXR1cm4gKChoYXNoIDw8IDUpIC0gaGFzaCkgKyBjaGFyLmNoYXJDb2RlQXQoMCk7XHJcbiAgICB9LCAwKTtcclxuICB9XHJcblxyXG4gIHRvU3RyaW5nKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gdGhpcy52YWx1ZTtcclxuICB9XHJcbn1cclxuXHJcbmRlc2NyaWJlKCdWYWx1ZU9iamVjdENhY2hlJywgKCkgPT4ge1xyXG4gIGxldCBjYWNoZTogVmFsdWVPYmplY3RDYWNoZTxUZXN0VmFsdWVPYmplY3Q+O1xyXG4gIGNvbnN0IGNvbmZpZzogQ2FjaGVDb25maWcgPSB7XHJcbiAgICBtYXhTaXplOiAzLFxyXG4gICAgdHRsTXM6IDEwMDAsXHJcbiAgICBlbmFibGVTdGF0aXN0aWNzOiB0cnVlLFxyXG4gIH07XHJcblxyXG4gIGJlZm9yZUVhY2goKCkgPT4ge1xyXG4gICAgY2FjaGUgPSBuZXcgVmFsdWVPYmplY3RDYWNoZShjb25maWcpO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnYmFzaWMgb3BlcmF0aW9ucycsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgc3RvcmUgYW5kIHJldHJpZXZlIHZhbHVlIG9iamVjdHMnLCAoKSA9PiB7XHJcbiAgICAgIC8vIEFycmFuZ2VcclxuICAgICAgY29uc3Qga2V5ID0gJ3Rlc3Qta2V5JztcclxuICAgICAgY29uc3QgdmFsdWUgPSBuZXcgVGVzdFZhbHVlT2JqZWN0KCd0ZXN0LXZhbHVlJyk7XHJcblxyXG4gICAgICAvLyBBY3RcclxuICAgICAgY2FjaGUucHV0KGtleSwgdmFsdWUpO1xyXG4gICAgICBjb25zdCByZXRyaWV2ZWQgPSBjYWNoZS5nZXQoa2V5KTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3QocmV0cmlldmVkKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgICBleHBlY3QocmV0cmlldmVkPy50b1N0cmluZygpKS50b0JlKCd0ZXN0LXZhbHVlJyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHJldHVybiB1bmRlZmluZWQgZm9yIG5vbi1leGlzdGVudCBrZXlzJywgKCkgPT4ge1xyXG4gICAgICAvLyBBY3RcclxuICAgICAgY29uc3QgcmVzdWx0ID0gY2FjaGUuZ2V0KCdub24tZXhpc3RlbnQnKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0JlVW5kZWZpbmVkKCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGNoZWNrIGtleSBleGlzdGVuY2UgY29ycmVjdGx5JywgKCkgPT4ge1xyXG4gICAgICAvLyBBcnJhbmdlXHJcbiAgICAgIGNvbnN0IGtleSA9ICd0ZXN0LWtleSc7XHJcbiAgICAgIGNvbnN0IHZhbHVlID0gbmV3IFRlc3RWYWx1ZU9iamVjdCgndGVzdC12YWx1ZScpO1xyXG5cclxuICAgICAgLy8gQWN0ICYgQXNzZXJ0XHJcbiAgICAgIGV4cGVjdChjYWNoZS5oYXMoa2V5KSkudG9CZShmYWxzZSk7XHJcbiAgICAgIGNhY2hlLnB1dChrZXksIHZhbHVlKTtcclxuICAgICAgZXhwZWN0KGNhY2hlLmhhcyhrZXkpKS50b0JlKHRydWUpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBkZWxldGUgZW50cmllcyBjb3JyZWN0bHknLCAoKSA9PiB7XHJcbiAgICAgIC8vIEFycmFuZ2VcclxuICAgICAgY29uc3Qga2V5ID0gJ3Rlc3Qta2V5JztcclxuICAgICAgY29uc3QgdmFsdWUgPSBuZXcgVGVzdFZhbHVlT2JqZWN0KCd0ZXN0LXZhbHVlJyk7XHJcbiAgICAgIGNhY2hlLnB1dChrZXksIHZhbHVlKTtcclxuXHJcbiAgICAgIC8vIEFjdFxyXG4gICAgICBjb25zdCBkZWxldGVkID0gY2FjaGUuZGVsZXRlKGtleSk7XHJcblxyXG4gICAgICAvLyBBc3NlcnRcclxuICAgICAgZXhwZWN0KGRlbGV0ZWQpLnRvQmUodHJ1ZSk7XHJcbiAgICAgIGV4cGVjdChjYWNoZS5oYXMoa2V5KSkudG9CZShmYWxzZSk7XHJcbiAgICAgIGV4cGVjdChjYWNoZS5nZXQoa2V5KSkudG9CZVVuZGVmaW5lZCgpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjbGVhciBhbGwgZW50cmllcycsICgpID0+IHtcclxuICAgICAgLy8gQXJyYW5nZVxyXG4gICAgICBjYWNoZS5wdXQoJ2tleTEnLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTEnKSk7XHJcbiAgICAgIGNhY2hlLnB1dCgna2V5MicsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlMicpKTtcclxuXHJcbiAgICAgIC8vIEFjdFxyXG4gICAgICBjYWNoZS5jbGVhcigpO1xyXG5cclxuICAgICAgLy8gQXNzZXJ0XHJcbiAgICAgIGV4cGVjdChjYWNoZS5zaXplKCkpLnRvQmUoMCk7XHJcbiAgICAgIGV4cGVjdChjYWNoZS5oYXMoJ2tleTEnKSkudG9CZShmYWxzZSk7XHJcbiAgICAgIGV4cGVjdChjYWNoZS5oYXMoJ2tleTInKSkudG9CZShmYWxzZSk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ0xSVSBldmljdGlvbicsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgZXZpY3QgbGVhc3QgcmVjZW50bHkgdXNlZCBpdGVtIHdoZW4gYXQgY2FwYWNpdHknLCAoKSA9PiB7XHJcbiAgICAgIC8vIEFycmFuZ2VcclxuICAgICAgY2FjaGUucHV0KCdrZXkxJywgbmV3IFRlc3RWYWx1ZU9iamVjdCgndmFsdWUxJykpO1xyXG4gICAgICBjYWNoZS5wdXQoJ2tleTInLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTInKSk7XHJcbiAgICAgIGNhY2hlLnB1dCgna2V5MycsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlMycpKTtcclxuXHJcbiAgICAgIC8vIEFjY2VzcyBrZXkxIHRvIG1ha2UgaXQgcmVjZW50bHkgdXNlZFxyXG4gICAgICBjYWNoZS5nZXQoJ2tleTEnKTtcclxuXHJcbiAgICAgIC8vIEFjdCAtIGFkZCBmb3VydGggaXRlbSwgc2hvdWxkIGV2aWN0IGtleTIgKGxlYXN0IHJlY2VudGx5IHVzZWQpXHJcbiAgICAgIGNhY2hlLnB1dCgna2V5NCcsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlNCcpKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3QoY2FjaGUuc2l6ZSgpKS50b0JlKDMpO1xyXG4gICAgICBleHBlY3QoY2FjaGUuaGFzKCdrZXkxJykpLnRvQmUodHJ1ZSk7IC8vIFJlY2VudGx5IGFjY2Vzc2VkXHJcbiAgICAgIGV4cGVjdChjYWNoZS5oYXMoJ2tleTInKSkudG9CZShmYWxzZSk7IC8vIFNob3VsZCBiZSBldmljdGVkXHJcbiAgICAgIGV4cGVjdChjYWNoZS5oYXMoJ2tleTMnKSkudG9CZSh0cnVlKTtcclxuICAgICAgZXhwZWN0KGNhY2hlLmhhcygna2V5NCcpKS50b0JlKHRydWUpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB1cGRhdGUgYWNjZXNzIHRpbWUgd2hlbiByZXRyaWV2aW5nIGl0ZW1zJywgKCkgPT4ge1xyXG4gICAgICAvLyBBcnJhbmdlXHJcbiAgICAgIGNhY2hlLnB1dCgna2V5MScsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlMScpKTtcclxuICAgICAgY2FjaGUucHV0KCdrZXkyJywgbmV3IFRlc3RWYWx1ZU9iamVjdCgndmFsdWUyJykpO1xyXG4gICAgICBjYWNoZS5wdXQoJ2tleTMnLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTMnKSk7XHJcblxyXG4gICAgICAvLyBBY3QgLSBhY2Nlc3Mga2V5MSBtdWx0aXBsZSB0aW1lc1xyXG4gICAgICBjYWNoZS5nZXQoJ2tleTEnKTtcclxuICAgICAgY2FjaGUuZ2V0KCdrZXkxJyk7XHJcbiAgICAgIGNhY2hlLmdldCgna2V5MScpO1xyXG5cclxuICAgICAgLy8gQWRkIGZvdXJ0aCBpdGVtXHJcbiAgICAgIGNhY2hlLnB1dCgna2V5NCcsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlNCcpKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydCAtIGtleTEgc2hvdWxkIHN0aWxsIGJlIHByZXNlbnQgZHVlIHRvIHJlY2VudCBhY2Nlc3NcclxuICAgICAgZXhwZWN0KGNhY2hlLmhhcygna2V5MScpKS50b0JlKHRydWUpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdUVEwgZXhwaXJhdGlvbicsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgZXhwaXJlIGVudHJpZXMgYWZ0ZXIgVFRMJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICAvLyBBcnJhbmdlXHJcbiAgICAgIGNvbnN0IHNob3J0VHRsQ2FjaGUgPSBuZXcgVmFsdWVPYmplY3RDYWNoZTxUZXN0VmFsdWVPYmplY3Q+KHtcclxuICAgICAgICBtYXhTaXplOiAxMCxcclxuICAgICAgICB0dGxNczogNTAsIC8vIDUwbXMgVFRMXHJcbiAgICAgICAgZW5hYmxlU3RhdGlzdGljczogdHJ1ZSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjb25zdCBrZXkgPSAndGVzdC1rZXknO1xyXG4gICAgICBjb25zdCB2YWx1ZSA9IG5ldyBUZXN0VmFsdWVPYmplY3QoJ3Rlc3QtdmFsdWUnKTtcclxuXHJcbiAgICAgIC8vIEFjdFxyXG4gICAgICBzaG9ydFR0bENhY2hlLnB1dChrZXksIHZhbHVlKTtcclxuICAgICAgZXhwZWN0KHNob3J0VHRsQ2FjaGUuaGFzKGtleSkpLnRvQmUodHJ1ZSk7XHJcblxyXG4gICAgICAvLyBXYWl0IGZvciBleHBpcmF0aW9uXHJcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3Qoc2hvcnRUdGxDYWNoZS5oYXMoa2V5KSkudG9CZShmYWxzZSk7XHJcbiAgICAgIGV4cGVjdChzaG9ydFR0bENhY2hlLmdldChrZXkpKS50b0JlVW5kZWZpbmVkKCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGNsZWFudXAgZXhwaXJlZCBlbnRyaWVzJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICAvLyBBcnJhbmdlXHJcbiAgICAgIGNvbnN0IHNob3J0VHRsQ2FjaGUgPSBuZXcgVmFsdWVPYmplY3RDYWNoZTxUZXN0VmFsdWVPYmplY3Q+KHtcclxuICAgICAgICBtYXhTaXplOiAxMCxcclxuICAgICAgICB0dGxNczogNTAsXHJcbiAgICAgICAgZW5hYmxlU3RhdGlzdGljczogdHJ1ZSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBzaG9ydFR0bENhY2hlLnB1dCgna2V5MScsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlMScpKTtcclxuICAgICAgc2hvcnRUdGxDYWNoZS5wdXQoJ2tleTInLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTInKSk7XHJcblxyXG4gICAgICAvLyBXYWl0IGZvciBleHBpcmF0aW9uXHJcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKTtcclxuXHJcbiAgICAgIC8vIEFjdFxyXG4gICAgICBjb25zdCByZW1vdmVkQ291bnQgPSBzaG9ydFR0bENhY2hlLmNsZWFudXAoKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3QocmVtb3ZlZENvdW50KS50b0JlKDIpO1xyXG4gICAgICBleHBlY3Qoc2hvcnRUdGxDYWNoZS5zaXplKCkpLnRvQmUoMCk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2dldE9yQ29tcHV0ZSBmdW5jdGlvbmFsaXR5JywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCByZXR1cm4gY2FjaGVkIHZhbHVlIGlmIHByZXNlbnQnLCAoKSA9PiB7XHJcbiAgICAgIC8vIEFycmFuZ2VcclxuICAgICAgY29uc3Qga2V5ID0gJ3Rlc3Qta2V5JztcclxuICAgICAgY29uc3QgY2FjaGVkVmFsdWUgPSBuZXcgVGVzdFZhbHVlT2JqZWN0KCdjYWNoZWQtdmFsdWUnKTtcclxuICAgICAgY2FjaGUucHV0KGtleSwgY2FjaGVkVmFsdWUpO1xyXG5cclxuICAgICAgbGV0IGZhY3RvcnlDYWxsZWQgPSBmYWxzZTtcclxuICAgICAgY29uc3QgZmFjdG9yeSA9ICgpID0+IHtcclxuICAgICAgICBmYWN0b3J5Q2FsbGVkID0gdHJ1ZTtcclxuICAgICAgICByZXR1cm4gbmV3IFRlc3RWYWx1ZU9iamVjdCgnZmFjdG9yeS12YWx1ZScpO1xyXG4gICAgICB9O1xyXG5cclxuICAgICAgLy8gQWN0XHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGNhY2hlLmdldE9yQ29tcHV0ZShrZXksIGZhY3RvcnkpO1xyXG5cclxuICAgICAgLy8gQXNzZXJ0XHJcbiAgICAgIGV4cGVjdChyZXN1bHQudG9TdHJpbmcoKSkudG9CZSgnY2FjaGVkLXZhbHVlJyk7XHJcbiAgICAgIGV4cGVjdChmYWN0b3J5Q2FsbGVkKS50b0JlKGZhbHNlKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY29tcHV0ZSBhbmQgY2FjaGUgdmFsdWUgaWYgbm90IHByZXNlbnQnLCAoKSA9PiB7XHJcbiAgICAgIC8vIEFycmFuZ2VcclxuICAgICAgY29uc3Qga2V5ID0gJ3Rlc3Qta2V5JztcclxuICAgICAgbGV0IGZhY3RvcnlDYWxsZWQgPSBmYWxzZTtcclxuICAgICAgY29uc3QgZmFjdG9yeSA9ICgpID0+IHtcclxuICAgICAgICBmYWN0b3J5Q2FsbGVkID0gdHJ1ZTtcclxuICAgICAgICByZXR1cm4gbmV3IFRlc3RWYWx1ZU9iamVjdCgnZmFjdG9yeS12YWx1ZScpO1xyXG4gICAgICB9O1xyXG5cclxuICAgICAgLy8gQWN0XHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGNhY2hlLmdldE9yQ29tcHV0ZShrZXksIGZhY3RvcnkpO1xyXG5cclxuICAgICAgLy8gQXNzZXJ0XHJcbiAgICAgIGV4cGVjdChyZXN1bHQudG9TdHJpbmcoKSkudG9CZSgnZmFjdG9yeS12YWx1ZScpO1xyXG4gICAgICBleHBlY3QoZmFjdG9yeUNhbGxlZCkudG9CZSh0cnVlKTtcclxuICAgICAgZXhwZWN0KGNhY2hlLmhhcyhrZXkpKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QoY2FjaGUuZ2V0KGtleSk/LnRvU3RyaW5nKCkpLnRvQmUoJ2ZhY3RvcnktdmFsdWUnKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnc3RhdGlzdGljcycsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgdHJhY2sgY2FjaGUgaGl0cyBhbmQgbWlzc2VzJywgKCkgPT4ge1xyXG4gICAgICAvLyBBcnJhbmdlXHJcbiAgICAgIGNvbnN0IGtleSA9ICd0ZXN0LWtleSc7XHJcbiAgICAgIGNvbnN0IHZhbHVlID0gbmV3IFRlc3RWYWx1ZU9iamVjdCgndGVzdC12YWx1ZScpO1xyXG5cclxuICAgICAgLy8gQWN0XHJcbiAgICAgIGNhY2hlLmdldCgnbm9uLWV4aXN0ZW50Jyk7IC8vIE1pc3NcclxuICAgICAgY2FjaGUucHV0KGtleSwgdmFsdWUpO1xyXG4gICAgICBjYWNoZS5nZXQoa2V5KTsgLy8gSGl0XHJcbiAgICAgIGNhY2hlLmdldChrZXkpOyAvLyBIaXRcclxuICAgICAgY2FjaGUuZ2V0KCdhbm90aGVyLW5vbi1leGlzdGVudCcpOyAvLyBNaXNzXHJcblxyXG4gICAgICBjb25zdCBzdGF0cyA9IGNhY2hlLmdldFN0YXRpc3RpY3MoKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3Qoc3RhdHMuaGl0cykudG9CZSgyKTtcclxuICAgICAgZXhwZWN0KHN0YXRzLm1pc3NlcykudG9CZSgyKTtcclxuICAgICAgZXhwZWN0KHN0YXRzLmhpdFJhdGUpLnRvQmUoMC41KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdHJhY2sgZXZpY3Rpb25zJywgKCkgPT4ge1xyXG4gICAgICAvLyBBcnJhbmdlICYgQWN0XHJcbiAgICAgIGNhY2hlLnB1dCgna2V5MScsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlMScpKTtcclxuICAgICAgY2FjaGUucHV0KCdrZXkyJywgbmV3IFRlc3RWYWx1ZU9iamVjdCgndmFsdWUyJykpO1xyXG4gICAgICBjYWNoZS5wdXQoJ2tleTMnLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTMnKSk7XHJcbiAgICAgIGNhY2hlLnB1dCgna2V5NCcsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlNCcpKTsgLy8gU2hvdWxkIGNhdXNlIGV2aWN0aW9uXHJcblxyXG4gICAgICBjb25zdCBzdGF0cyA9IGNhY2hlLmdldFN0YXRpc3RpY3MoKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3Qoc3RhdHMuZXZpY3Rpb25zKS50b0JlKDEpO1xyXG4gICAgICBleHBlY3Qoc3RhdHMuc2l6ZSkudG9CZSgzKTtcclxuICAgICAgZXhwZWN0KHN0YXRzLm1heFNpemUpLnRvQmUoMyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGVzdGltYXRlIG1lbW9yeSB1c2FnZScsICgpID0+IHtcclxuICAgICAgLy8gQXJyYW5nZVxyXG4gICAgICBjYWNoZS5wdXQoJ2tleTEnLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTEnKSk7XHJcbiAgICAgIGNhY2hlLnB1dCgna2V5MicsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlMicpKTtcclxuXHJcbiAgICAgIC8vIEFjdFxyXG4gICAgICBjb25zdCBzdGF0cyA9IGNhY2hlLmdldFN0YXRpc3RpY3MoKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3Qoc3RhdHMubWVtb3J5VXNhZ2UpLnRvQmVHcmVhdGVyVGhhbigwKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgcmVzZXQgc3RhdGlzdGljcycsICgpID0+IHtcclxuICAgICAgLy8gQXJyYW5nZVxyXG4gICAgICBjYWNoZS5wdXQoJ2tleTEnLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTEnKSk7XHJcbiAgICAgIGNhY2hlLmdldCgna2V5MScpO1xyXG4gICAgICBjYWNoZS5nZXQoJ25vbi1leGlzdGVudCcpO1xyXG5cclxuICAgICAgLy8gQWN0XHJcbiAgICAgIGNhY2hlLnJlc2V0U3RhdGlzdGljcygpO1xyXG4gICAgICBjb25zdCBzdGF0cyA9IGNhY2hlLmdldFN0YXRpc3RpY3MoKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3Qoc3RhdHMuaGl0cykudG9CZSgwKTtcclxuICAgICAgZXhwZWN0KHN0YXRzLm1pc3NlcykudG9CZSgwKTtcclxuICAgICAgZXhwZWN0KHN0YXRzLmV2aWN0aW9ucykudG9CZSgwKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnZW50cnkgYW5hbHlzaXMnLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIHJldHVybiBlbnRyaWVzIGJ5IGZyZXF1ZW5jeScsICgpID0+IHtcclxuICAgICAgLy8gQXJyYW5nZVxyXG4gICAgICBjYWNoZS5wdXQoJ2tleTEnLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTEnKSk7XHJcbiAgICAgIGNhY2hlLnB1dCgna2V5MicsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlMicpKTtcclxuICAgICAgY2FjaGUucHV0KCdrZXkzJywgbmV3IFRlc3RWYWx1ZU9iamVjdCgndmFsdWUzJykpO1xyXG5cclxuICAgICAgLy8gQWNjZXNzIGtleTIgbXVsdGlwbGUgdGltZXNcclxuICAgICAgY2FjaGUuZ2V0KCdrZXkyJyk7XHJcbiAgICAgIGNhY2hlLmdldCgna2V5MicpO1xyXG4gICAgICBjYWNoZS5nZXQoJ2tleTEnKTtcclxuXHJcbiAgICAgIC8vIEFjdFxyXG4gICAgICBjb25zdCBlbnRyaWVzQnlGcmVxdWVuY3kgPSBjYWNoZS5nZXRFbnRyaWVzQnlGcmVxdWVuY3koKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3QoZW50cmllc0J5RnJlcXVlbmN5KS50b0hhdmVMZW5ndGgoMyk7XHJcbiAgICAgIGV4cGVjdChlbnRyaWVzQnlGcmVxdWVuY3lbMF0ua2V5KS50b0JlKCdrZXkyJyk7IC8vIE1vc3QgYWNjZXNzZWRcclxuICAgICAgZXhwZWN0KGVudHJpZXNCeUZyZXF1ZW5jeVswXS5hY2Nlc3NDb3VudCkudG9CZSgzKTsgLy8gSW5pdGlhbCBwdXQgKyAyIGdldHNcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgcmV0dXJuIGVudHJpZXMgYnkgcmVjZW5jeScsICgpID0+IHtcclxuICAgICAgLy8gQXJyYW5nZVxyXG4gICAgICBjYWNoZS5wdXQoJ2tleTEnLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTEnKSk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBTbWFsbCBkZWxheSB0byBlbnN1cmUgZGlmZmVyZW50IHRpbWVzdGFtcHNcclxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgY2FjaGUucHV0KCdrZXkyJywgbmV3IFRlc3RWYWx1ZU9iamVjdCgndmFsdWUyJykpO1xyXG4gICAgICB9LCAxMCk7XHJcblxyXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAvLyBBY3RcclxuICAgICAgICBjb25zdCBlbnRyaWVzQnlSZWNlbmN5ID0gY2FjaGUuZ2V0RW50cmllc0J5UmVjZW5jeSgpO1xyXG5cclxuICAgICAgICAvLyBBc3NlcnRcclxuICAgICAgICBleHBlY3QoZW50cmllc0J5UmVjZW5jeSkudG9IYXZlTGVuZ3RoKDIpO1xyXG4gICAgICAgIGV4cGVjdChlbnRyaWVzQnlSZWNlbmN5WzBdLmtleSkudG9CZSgna2V5MicpOyAvLyBNb3N0IHJlY2VudFxyXG4gICAgICB9LCAyMCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHdhcm0gdXAgY2FjaGUgd2l0aCBwcm92aWRlZCBlbnRyaWVzJywgKCkgPT4ge1xyXG4gICAgICAvLyBBcnJhbmdlXHJcbiAgICAgIGNvbnN0IGVudHJpZXMgPSBbXHJcbiAgICAgICAgeyBrZXk6ICdrZXkxJywgdmFsdWU6IG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlMScpIH0sXHJcbiAgICAgICAgeyBrZXk6ICdrZXkyJywgdmFsdWU6IG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlMicpIH0sXHJcbiAgICAgIF07XHJcblxyXG4gICAgICAvLyBBY3RcclxuICAgICAgY2FjaGUud2FybVVwKGVudHJpZXMpO1xyXG5cclxuICAgICAgLy8gQXNzZXJ0XHJcbiAgICAgIGV4cGVjdChjYWNoZS5zaXplKCkpLnRvQmUoMik7XHJcbiAgICAgIGV4cGVjdChjYWNoZS5oYXMoJ2tleTEnKSkudG9CZSh0cnVlKTtcclxuICAgICAgZXhwZWN0KGNhY2hlLmhhcygna2V5MicpKS50b0JlKHRydWUpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdrZXlzIHJldHJpZXZhbCcsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgcmV0dXJuIGFsbCBjYWNoZSBrZXlzJywgKCkgPT4ge1xyXG4gICAgICAvLyBBcnJhbmdlXHJcbiAgICAgIGNhY2hlLnB1dCgna2V5MScsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlMScpKTtcclxuICAgICAgY2FjaGUucHV0KCdrZXkyJywgbmV3IFRlc3RWYWx1ZU9iamVjdCgndmFsdWUyJykpO1xyXG4gICAgICBjYWNoZS5wdXQoJ2tleTMnLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTMnKSk7XHJcblxyXG4gICAgICAvLyBBY3RcclxuICAgICAgY29uc3Qga2V5cyA9IGNhY2hlLmtleXMoKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3Qoa2V5cykudG9IYXZlTGVuZ3RoKDMpO1xyXG4gICAgICBleHBlY3Qoa2V5cykudG9Db250YWluKCdrZXkxJyk7XHJcbiAgICAgIGV4cGVjdChrZXlzKS50b0NvbnRhaW4oJ2tleTInKTtcclxuICAgICAgZXhwZWN0KGtleXMpLnRvQ29udGFpbigna2V5MycpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcbn0pO1xyXG5cclxuZGVzY3JpYmUoJ1ZhbHVlT2JqZWN0Q2FjaGVNYW5hZ2VyJywgKCkgPT4ge1xyXG4gIGxldCBtYW5hZ2VyOiBWYWx1ZU9iamVjdENhY2hlTWFuYWdlcjtcclxuXHJcbiAgYmVmb3JlRWFjaCgoKSA9PiB7XHJcbiAgICBtYW5hZ2VyID0gVmFsdWVPYmplY3RDYWNoZU1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcclxuICAgIG1hbmFnZXIuY2xlYXJBbGwoKTsgLy8gQ2xlYW4gc3RhdGUgZm9yIGVhY2ggdGVzdFxyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnY2FjaGUgbWFuYWdlbWVudCcsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgY3JlYXRlIGFuZCByZXRyaWV2ZSBjYWNoZXMnLCAoKSA9PiB7XHJcbiAgICAgIC8vIEFjdFxyXG4gICAgICBjb25zdCBjYWNoZTEgPSBtYW5hZ2VyLmdldENhY2hlPFRlc3RWYWx1ZU9iamVjdD4oJ3Rlc3QtY2FjaGUnKTtcclxuICAgICAgY29uc3QgY2FjaGUyID0gbWFuYWdlci5nZXRDYWNoZTxUZXN0VmFsdWVPYmplY3Q+KCd0ZXN0LWNhY2hlJyk7XHJcblxyXG4gICAgICAvLyBBc3NlcnRcclxuICAgICAgZXhwZWN0KGNhY2hlMSkudG9CZShjYWNoZTIpOyAvLyBTaG91bGQgcmV0dXJuIHNhbWUgaW5zdGFuY2VcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY3JlYXRlIGNhY2hlcyB3aXRoIGN1c3RvbSBjb25maWd1cmF0aW9uJywgKCkgPT4ge1xyXG4gICAgICAvLyBBcnJhbmdlXHJcbiAgICAgIGNvbnN0IGNvbmZpZzogQ2FjaGVDb25maWcgPSB7XHJcbiAgICAgICAgbWF4U2l6ZTogNTAwLFxyXG4gICAgICAgIHR0bE1zOiA2MDAwMDAsXHJcbiAgICAgICAgZW5hYmxlU3RhdGlzdGljczogZmFsc2UsXHJcbiAgICAgIH07XHJcblxyXG4gICAgICAvLyBBY3RcclxuICAgICAgY29uc3QgY2FjaGUgPSBtYW5hZ2VyLmdldENhY2hlPFRlc3RWYWx1ZU9iamVjdD4oJ2N1c3RvbS1jYWNoZScsIGNvbmZpZyk7XHJcbiAgICAgIGNhY2hlLnB1dCgndGVzdCcsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3Rlc3QnKSk7XHJcblxyXG4gICAgICBjb25zdCBzdGF0cyA9IGNhY2hlLmdldFN0YXRpc3RpY3MoKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3Qoc3RhdHMubWF4U2l6ZSkudG9CZSg1MDApO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBnZXQgc3RhdGlzdGljcyBmcm9tIGFsbCBjYWNoZXMnLCAoKSA9PiB7XHJcbiAgICAgIC8vIEFycmFuZ2VcclxuICAgICAgY29uc3QgY2FjaGUxID0gbWFuYWdlci5nZXRDYWNoZTxUZXN0VmFsdWVPYmplY3Q+KCdjYWNoZTEnKTtcclxuICAgICAgY29uc3QgY2FjaGUyID0gbWFuYWdlci5nZXRDYWNoZTxUZXN0VmFsdWVPYmplY3Q+KCdjYWNoZTInKTtcclxuXHJcbiAgICAgIGNhY2hlMS5wdXQoJ2tleTEnLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTEnKSk7XHJcbiAgICAgIGNhY2hlMi5wdXQoJ2tleTInLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTInKSk7XHJcblxyXG4gICAgICAvLyBBY3RcclxuICAgICAgY29uc3QgYWxsU3RhdHMgPSBtYW5hZ2VyLmdldEFsbFN0YXRpc3RpY3MoKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3QoT2JqZWN0LmtleXMoYWxsU3RhdHMpKS50b0hhdmVMZW5ndGgoMik7XHJcbiAgICAgIGV4cGVjdChhbGxTdGF0c1snY2FjaGUxJ10pLnRvQmVEZWZpbmVkKCk7XHJcbiAgICAgIGV4cGVjdChhbGxTdGF0c1snY2FjaGUyJ10pLnRvQmVEZWZpbmVkKCk7XHJcbiAgICAgIGV4cGVjdChhbGxTdGF0c1snY2FjaGUxJ10uc2l6ZSkudG9CZSgxKTtcclxuICAgICAgZXhwZWN0KGFsbFN0YXRzWydjYWNoZTInXS5zaXplKS50b0JlKDEpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjbGVhciBhbGwgY2FjaGVzJywgKCkgPT4ge1xyXG4gICAgICAvLyBBcnJhbmdlXHJcbiAgICAgIGNvbnN0IGNhY2hlMSA9IG1hbmFnZXIuZ2V0Q2FjaGU8VGVzdFZhbHVlT2JqZWN0PignY2FjaGUxJyk7XHJcbiAgICAgIGNvbnN0IGNhY2hlMiA9IG1hbmFnZXIuZ2V0Q2FjaGU8VGVzdFZhbHVlT2JqZWN0PignY2FjaGUyJyk7XHJcblxyXG4gICAgICBjYWNoZTEucHV0KCdrZXkxJywgbmV3IFRlc3RWYWx1ZU9iamVjdCgndmFsdWUxJykpO1xyXG4gICAgICBjYWNoZTIucHV0KCdrZXkyJywgbmV3IFRlc3RWYWx1ZU9iamVjdCgndmFsdWUyJykpO1xyXG5cclxuICAgICAgLy8gQWN0XHJcbiAgICAgIG1hbmFnZXIuY2xlYXJBbGwoKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3QoY2FjaGUxLnNpemUoKSkudG9CZSgwKTtcclxuICAgICAgZXhwZWN0KGNhY2hlMi5zaXplKCkpLnRvQmUoMCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGNsZWFudXAgZXhwaXJlZCBlbnRyaWVzIGluIGFsbCBjYWNoZXMnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIC8vIEFycmFuZ2VcclxuICAgICAgY29uc3QgY2FjaGUxID0gbWFuYWdlci5nZXRDYWNoZTxUZXN0VmFsdWVPYmplY3Q+KCdjYWNoZTEnLCB7IG1heFNpemU6IDEwLCB0dGxNczogNTAgfSk7XHJcbiAgICAgIGNvbnN0IGNhY2hlMiA9IG1hbmFnZXIuZ2V0Q2FjaGU8VGVzdFZhbHVlT2JqZWN0PignY2FjaGUyJywgeyBtYXhTaXplOiAxMCwgdHRsTXM6IDUwIH0pO1xyXG5cclxuICAgICAgY2FjaGUxLnB1dCgna2V5MScsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlMScpKTtcclxuICAgICAgY2FjaGUyLnB1dCgna2V5MicsIG5ldyBUZXN0VmFsdWVPYmplY3QoJ3ZhbHVlMicpKTtcclxuXHJcbiAgICAgIC8vIFdhaXQgZm9yIGV4cGlyYXRpb25cclxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMCkpO1xyXG5cclxuICAgICAgLy8gQWN0XHJcbiAgICAgIGNvbnN0IHRvdGFsUmVtb3ZlZCA9IG1hbmFnZXIuY2xlYW51cEFsbCgpO1xyXG5cclxuICAgICAgLy8gQXNzZXJ0XHJcbiAgICAgIGV4cGVjdCh0b3RhbFJlbW92ZWQpLnRvQmUoMik7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGNhbGN1bGF0ZSB0b3RhbCBtZW1vcnkgdXNhZ2UnLCAoKSA9PiB7XHJcbiAgICAgIC8vIEFycmFuZ2VcclxuICAgICAgY29uc3QgY2FjaGUxID0gbWFuYWdlci5nZXRDYWNoZTxUZXN0VmFsdWVPYmplY3Q+KCdjYWNoZTEnKTtcclxuICAgICAgY29uc3QgY2FjaGUyID0gbWFuYWdlci5nZXRDYWNoZTxUZXN0VmFsdWVPYmplY3Q+KCdjYWNoZTInKTtcclxuXHJcbiAgICAgIGNhY2hlMS5wdXQoJ2tleTEnLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTEnKSk7XHJcbiAgICAgIGNhY2hlMi5wdXQoJ2tleTInLCBuZXcgVGVzdFZhbHVlT2JqZWN0KCd2YWx1ZTInKSk7XHJcblxyXG4gICAgICAvLyBBY3RcclxuICAgICAgY29uc3QgdG90YWxNZW1vcnkgPSBtYW5hZ2VyLmdldFRvdGFsTWVtb3J5VXNhZ2UoKTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3QodG90YWxNZW1vcnkpLnRvQmVHcmVhdGVyVGhhbigwKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnc2luZ2xldG9uIGJlaGF2aW9yJywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCByZXR1cm4gc2FtZSBpbnN0YW5jZScsICgpID0+IHtcclxuICAgICAgLy8gQWN0XHJcbiAgICAgIGNvbnN0IGluc3RhbmNlMSA9IFZhbHVlT2JqZWN0Q2FjaGVNYW5hZ2VyLmdldEluc3RhbmNlKCk7XHJcbiAgICAgIGNvbnN0IGluc3RhbmNlMiA9IFZhbHVlT2JqZWN0Q2FjaGVNYW5hZ2VyLmdldEluc3RhbmNlKCk7XHJcblxyXG4gICAgICAvLyBBc3NlcnRcclxuICAgICAgZXhwZWN0KGluc3RhbmNlMSkudG9CZShpbnN0YW5jZTIpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcbn0pO1xyXG5cclxuZGVzY3JpYmUoJ1BlcmZvcm1hbmNlIFRlc3RzJywgKCkgPT4ge1xyXG4gIGRlc2NyaWJlKCdjYWNoZSBwZXJmb3JtYW5jZScsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgaGFuZGxlIGhpZ2gtdm9sdW1lIG9wZXJhdGlvbnMgZWZmaWNpZW50bHknLCAoKSA9PiB7XHJcbiAgICAgIC8vIEFycmFuZ2VcclxuICAgICAgY29uc3QgY2FjaGUgPSBuZXcgVmFsdWVPYmplY3RDYWNoZTxUZXN0VmFsdWVPYmplY3Q+KHtcclxuICAgICAgICBtYXhTaXplOiAxMDAwLFxyXG4gICAgICAgIHR0bE1zOiAzMDAwMDAsXHJcbiAgICAgICAgZW5hYmxlU3RhdGlzdGljczogdHJ1ZSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xyXG5cclxuICAgICAgLy8gQWN0IC0gUGVyZm9ybSAxMCwwMDAgb3BlcmF0aW9uc1xyXG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IDEwMDAwOyBpKyspIHtcclxuICAgICAgICBjb25zdCBrZXkgPSBga2V5LSR7aSAlIDEwMH1gOyAvLyBSZXVzZSBrZXlzIHRvIHRlc3QgY2FjaGUgaGl0c1xyXG4gICAgICAgIGNvbnN0IHZhbHVlID0gbmV3IFRlc3RWYWx1ZU9iamVjdChgdmFsdWUtJHtpfWApO1xyXG4gICAgICAgIFxyXG4gICAgICAgIGlmIChpICUgMiA9PT0gMCkge1xyXG4gICAgICAgICAgY2FjaGUucHV0KGtleSwgdmFsdWUpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBjYWNoZS5nZXQoa2V5KTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IGVuZFRpbWUgPSBEYXRlLm5vdygpO1xyXG4gICAgICBjb25zdCBkdXJhdGlvbiA9IGVuZFRpbWUgLSBzdGFydFRpbWU7XHJcblxyXG4gICAgICAvLyBBc3NlcnRcclxuICAgICAgZXhwZWN0KGR1cmF0aW9uKS50b0JlTGVzc1RoYW4oMTAwMCk7IC8vIFNob3VsZCBjb21wbGV0ZSBpbiB1bmRlciAxIHNlY29uZFxyXG4gICAgICBcclxuICAgICAgY29uc3Qgc3RhdHMgPSBjYWNoZS5nZXRTdGF0aXN0aWNzKCk7XHJcbiAgICAgIGV4cGVjdChzdGF0cy5oaXRSYXRlKS50b0JlR3JlYXRlclRoYW4oMCk7IC8vIFNob3VsZCBoYXZlIGNhY2hlIGhpdHNcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgbWFpbnRhaW4gcGVyZm9ybWFuY2Ugd2l0aCBmcmVxdWVudCBldmljdGlvbnMnLCAoKSA9PiB7XHJcbiAgICAgIC8vIEFycmFuZ2VcclxuICAgICAgY29uc3QgY2FjaGUgPSBuZXcgVmFsdWVPYmplY3RDYWNoZTxUZXN0VmFsdWVPYmplY3Q+KHtcclxuICAgICAgICBtYXhTaXplOiAxMCwgLy8gU21hbGwgY2FjaGUgdG8gZm9yY2UgZXZpY3Rpb25zXHJcbiAgICAgICAgdHRsTXM6IDMwMDAwMCxcclxuICAgICAgICBlbmFibGVTdGF0aXN0aWNzOiB0cnVlLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XHJcblxyXG4gICAgICAvLyBBY3QgLSBBZGQgbWFueSBpdGVtcyB0byBmb3JjZSBldmljdGlvbnNcclxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCAxMDAwOyBpKyspIHtcclxuICAgICAgICBjYWNoZS5wdXQoYGtleS0ke2l9YCwgbmV3IFRlc3RWYWx1ZU9iamVjdChgdmFsdWUtJHtpfWApKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgZW5kVGltZSA9IERhdGUubm93KCk7XHJcbiAgICAgIGNvbnN0IGR1cmF0aW9uID0gZW5kVGltZSAtIHN0YXJ0VGltZTtcclxuXHJcbiAgICAgIC8vIEFzc2VydFxyXG4gICAgICBleHBlY3QoZHVyYXRpb24pLnRvQmVMZXNzVGhhbig1MDApOyAvLyBTaG91bGQgaGFuZGxlIGV2aWN0aW9ucyBlZmZpY2llbnRseVxyXG4gICAgICBleHBlY3QoY2FjaGUuc2l6ZSgpKS50b0JlKDEwKTsgLy8gU2hvdWxkIG1haW50YWluIG1heCBzaXplXHJcbiAgICAgIFxyXG4gICAgICBjb25zdCBzdGF0cyA9IGNhY2hlLmdldFN0YXRpc3RpY3MoKTtcclxuICAgICAgZXhwZWN0KHN0YXRzLmV2aWN0aW9ucykudG9CZUdyZWF0ZXJUaGFuKDApO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcbn0pOyJdLCJ2ZXJzaW9uIjozfQ==