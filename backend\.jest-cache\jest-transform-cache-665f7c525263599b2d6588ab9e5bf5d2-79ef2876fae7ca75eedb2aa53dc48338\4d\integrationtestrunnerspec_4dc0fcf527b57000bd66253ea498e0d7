f0659b9b8fd3a6e55c7c6707331bb03f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const security_orchestrator_service_1 = require("../../application/services/security-orchestrator.service");
const event_factory_1 = require("../../domain/factories/event.factory");
const event_repository_1 = require("../../domain/repositories/event.repository");
const threat_repository_1 = require("../../domain/repositories/threat.repository");
const vulnerability_repository_1 = require("../../domain/repositories/vulnerability.repository");
const response_action_repository_1 = require("../../domain/repositories/response-action.repository");
const event_processor_interface_1 = require("../../domain/interfaces/services/event-processor.interface");
const threat_detector_interface_1 = require("../../domain/interfaces/services/threat-detector.interface");
const vulnerability_scanner_interface_1 = require("../../domain/interfaces/services/vulnerability-scanner.interface");
const response_executor_interface_1 = require("../../domain/interfaces/services/response-executor.interface");
const event_metadata_value_object_1 = require("../../domain/value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../domain/value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../domain/value-objects/event-metadata/event-source.value-object");
const event_type_enum_1 = require("../../domain/enums/event-type.enum");
const event_severity_enum_1 = require("../../domain/enums/event-severity.enum");
const event_status_enum_1 = require("../../domain/enums/event-status.enum");
const event_source_type_enum_1 = require("../../domain/enums/event-source-type.enum");
const threat_severity_enum_1 = require("../../domain/enums/threat-severity.enum");
const unique_entity_id_value_object_1 = require("../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const correlation_id_value_object_1 = require("../../../../shared-kernel/value-objects/correlation-id.value-object");
const tenant_id_value_object_1 = require("../../../../shared-kernel/value-objects/tenant-id.value-object");
const user_id_value_object_1 = require("../../../../shared-kernel/value-objects/user-id.value-object");
/**
 * Comprehensive Integration Test Runner
 *
 * This test suite runs comprehensive integration scenarios that test
 * the complete security domain workflow from end to end.
 */
class ComprehensiveEventRepository {
    constructor() {
        this.events = new Map();
        this.metrics = {
            saveCount: 0,
            findCount: 0,
            deleteCount: 0,
            queryCount: 0,
        };
    }
    async save(event) {
        this.metrics.saveCount++;
        this.events.set(event.id.toString(), event);
    }
    async findById(id) {
        this.metrics.findCount++;
        return this.events.get(id.toString()) || null;
    }
    async findAll() {
        this.metrics.queryCount++;
        return Array.from(this.events.values());
    }
    async delete(id) {
        this.metrics.deleteCount++;
        this.events.delete(id.toString());
    }
    async findByTimeRange(start, end) {
        this.metrics.queryCount++;
        return Array.from(this.events.values()).filter(event => {
            const eventTime = event.timestamp.toDate();
            return eventTime >= start && eventTime <= end;
        });
    }
    async findBySource(source) {
        this.metrics.queryCount++;
        return Array.from(this.events.values()).filter(event => event.source.equals(source));
    }
    async findBySeverity(severity) {
        this.metrics.queryCount++;
        return Array.from(this.events.values()).filter(event => event.severity === severity);
    }
    async findEventsForCorrelation(timeWindowMs, eventTypes, minSeverity) {
        this.metrics.queryCount++;
        const cutoffTime = new Date(Date.now() - timeWindowMs);
        return Array.from(this.events.values()).filter(event => {
            const eventTime = event.timestamp.toDate();
            const timeMatch = eventTime >= cutoffTime;
            const typeMatch = !eventTypes || eventTypes.includes(event.type);
            const severityMatch = !minSeverity || this.compareSeverity(event.severity, minSeverity) >= 0;
            return timeMatch && typeMatch && severityMatch;
        });
    }
    compareSeverity(a, b) {
        const severityOrder = [event_severity_enum_1.EventSeverity.LOW, event_severity_enum_1.EventSeverity.MEDIUM, event_severity_enum_1.EventSeverity.HIGH, event_severity_enum_1.EventSeverity.CRITICAL];
        return severityOrder.indexOf(a) - severityOrder.indexOf(b);
    }
    getMetrics() {
        return { ...this.metrics };
    }
    clear() {
        this.events.clear();
        this.metrics = {
            saveCount: 0,
            findCount: 0,
            deleteCount: 0,
            queryCount: 0,
        };
    }
}
class ComprehensiveEventProcessor {
    constructor() {
        this.metrics = {
            processEventCount: 0,
            processEventsCount: 0,
            totalProcessingTime: 0,
        };
    }
    async processEvent(event, context) {
        const startTime = Date.now();
        this.metrics.processEventCount++;
        // Simulate realistic processing time
        await new Promise(resolve => setTimeout(resolve, 10));
        const endTime = Date.now();
        const duration = endTime - startTime;
        this.metrics.totalProcessingTime += duration;
        return {
            success: true,
            eventId: event.id.toString(),
            processingSteps: ['normalize', 'enrich', 'correlate'],
            duration,
            normalizedEvent: {
                id: unique_entity_id_value_object_1.UniqueEntityId.create().toString(),
                originalEventId: event.id.toString(),
                normalizedAt: new Date(),
            },
            enrichedEvent: {
                id: unique_entity_id_value_object_1.UniqueEntityId.create().toString(),
                originalEventId: event.id.toString(),
                enrichedAt: new Date(),
                enrichmentSources: ['threat-intel', 'geo-location'],
            },
            correlatedEvents: [],
            errors: [],
        };
    }
    async processEvents(events, context) {
        this.metrics.processEventsCount++;
        const results = await Promise.all(events.map(event => this.processEvent(event, context)));
        return {
            success: true,
            totalEvents: events.length,
            processedEvents: results.length,
            results,
        };
    }
    getMetrics() {
        return { ...this.metrics };
    }
    clear() {
        this.metrics = {
            processEventCount: 0,
            processEventsCount: 0,
            totalProcessingTime: 0,
        };
    }
}
class ComprehensiveThreatDetector {
    constructor() {
        this.metrics = {
            analyzeEventCount: 0,
            analyzeEventsCount: 0,
            threatsDetected: 0,
            totalAnalysisTime: 0,
        };
    }
    async analyzeEvent(event, context) {
        const startTime = Date.now();
        this.metrics.analyzeEventCount++;
        // Simulate realistic analysis time
        await new Promise(resolve => setTimeout(resolve, 15));
        const threats = this.generateThreats(event);
        this.metrics.threatsDetected += threats.length;
        const endTime = Date.now();
        const analysisTime = endTime - startTime;
        this.metrics.totalAnalysisTime += analysisTime;
        return {
            eventId: event.id.toString(),
            threats,
            confidence: threats.length > 0 ? 85 : 10,
            analysisTime,
            indicators: threats.flatMap(t => t.indicators || []),
            recommendations: this.generateRecommendations(threats),
        };
    }
    async analyzeEvents(events, context) {
        this.metrics.analyzeEventsCount++;
        const analyses = await Promise.all(events.map(event => this.analyzeEvent(event, context)));
        return {
            totalEvents: events.length,
            analyses,
            aggregatedThreats: analyses.flatMap(a => a.threats),
        };
    }
    generateThreats(event) {
        const threats = [];
        if (event.severity === event_severity_enum_1.EventSeverity.HIGH || event.severity === event_severity_enum_1.EventSeverity.CRITICAL) {
            threats.push({
                id: unique_entity_id_value_object_1.UniqueEntityId.create().toString(),
                signature: `threat-${event.type}-${Date.now()}`,
                severity: event.severity === event_severity_enum_1.EventSeverity.CRITICAL ? threat_severity_enum_1.ThreatSeverity.CRITICAL : threat_severity_enum_1.ThreatSeverity.HIGH,
                confidence: 85 + Math.random() * 10,
                indicators: [`${event.type.toLowerCase()}-pattern`],
                mitreTechniques: this.getMitreTechniques(event.type),
                detectedAt: new Date(),
            });
        }
        if (event.type === event_type_enum_1.EventType.NETWORK_INTRUSION) {
            threats.push({
                id: unique_entity_id_value_object_1.UniqueEntityId.create().toString(),
                signature: 'network-intrusion-threat',
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                confidence: 90,
                indicators: ['suspicious-network-activity', 'port-scanning'],
                mitreTechniques: ['T1190', 'T1046'],
                detectedAt: new Date(),
            });
        }
        return threats;
    }
    getMitreTechniques(eventType) {
        const techniqueMap = {
            [event_type_enum_1.EventType.NETWORK_INTRUSION]: ['T1190', 'T1046'],
            [event_type_enum_1.EventType.MALWARE_DETECTED]: ['T1055', 'T1059'],
            [event_type_enum_1.EventType.AUTHENTICATION_FAILURE]: ['T1110', 'T1078'],
            [event_type_enum_1.EventType.DATA_BREACH]: ['T1041', 'T1048'],
            [event_type_enum_1.EventType.SECURITY_ALERT]: ['T1082', 'T1083'],
        };
        return techniqueMap[eventType] || ['T1000'];
    }
    generateRecommendations(threats) {
        if (threats.length === 0)
            return [];
        const recommendations = ['Investigate immediately'];
        if (threats.some(t => t.severity === threat_severity_enum_1.ThreatSeverity.CRITICAL)) {
            recommendations.push('Escalate to security team');
            recommendations.push('Consider system isolation');
        }
        if (threats.some(t => t.indicators?.includes('network-intrusion-pattern'))) {
            recommendations.push('Block source IP address');
            recommendations.push('Review firewall rules');
        }
        return recommendations;
    }
    getMetrics() {
        return { ...this.metrics };
    }
    clear() {
        this.metrics = {
            analyzeEventCount: 0,
            analyzeEventsCount: 0,
            threatsDetected: 0,
            totalAnalysisTime: 0,
        };
    }
}
describe('Comprehensive Integration Test Runner', () => {
    let module;
    let orchestratorService;
    let eventRepository;
    let eventProcessor;
    let threatDetector;
    beforeEach(async () => {
        eventRepository = new ComprehensiveEventRepository();
        eventProcessor = new ComprehensiveEventProcessor();
        threatDetector = new ComprehensiveThreatDetector();
        module = await testing_1.Test.createTestingModule({
            providers: [
                security_orchestrator_service_1.SecurityOrchestratorService,
                { provide: event_repository_1.EventRepository, useValue: eventRepository },
                { provide: threat_repository_1.ThreatRepository, useValue: {} },
                { provide: vulnerability_repository_1.VulnerabilityRepository, useValue: {} },
                { provide: response_action_repository_1.ResponseActionRepository, useValue: {} },
                { provide: event_processor_interface_1.EventProcessor, useValue: eventProcessor },
                { provide: threat_detector_interface_1.ThreatDetector, useValue: threatDetector },
                { provide: vulnerability_scanner_interface_1.VulnerabilityScanner, useValue: {} },
                { provide: response_executor_interface_1.ResponseExecutor, useValue: {} },
            ],
        }).compile();
        orchestratorService = module.get(security_orchestrator_service_1.SecurityOrchestratorService);
    });
    afterEach(async () => {
        eventRepository.clear();
        eventProcessor.clear();
        threatDetector.clear();
        await module.close();
    });
    describe('End-to-End Security Workflow Integration', () => {
        it('should execute complete security incident response workflow', async () => {
            // Arrange - Create a realistic security incident scenario
            const tenantId = tenant_id_value_object_1.TenantId.create();
            const userId = user_id_value_object_1.UserId.create();
            const correlationId = correlation_id_value_object_1.CorrelationId.create();
            const incidentTime = new Date();
            // Initial network intrusion event
            const networkIntrusionEvent = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({
                    source: 'firewall',
                    version: '1.0',
                    tags: ['security', 'network', 'intrusion'],
                    customFields: {
                        severity: 'high',
                        sourceCountry: 'Unknown',
                        targetAsset: 'web-server-01'
                    },
                }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(incidentTime),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.FIREWALL,
                    identifier: 'fw-dmz-001',
                    name: 'DMZ Firewall',
                }),
                type: event_type_enum_1.EventType.NETWORK_INTRUSION,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: {
                    sourceIp: '*************',
                    targetIp: '************',
                    targetPort: 443,
                    protocol: 'TCP',
                    attackVector: 'port-scanning',
                },
                tenantId,
                userId,
            });
            // Follow-up malware detection event
            const malwareEvent = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({
                    source: 'endpoint-protection',
                    version: '1.0',
                    tags: ['security', 'malware', 'endpoint'],
                    customFields: {
                        severity: 'critical',
                        hostName: 'web-server-01',
                        malwareFamily: 'trojan'
                    },
                }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date(incidentTime.getTime() + 300000)), // 5 minutes later
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.ENDPOINT,
                    identifier: 'epp-001',
                    name: 'Endpoint Protection Platform',
                }),
                type: event_type_enum_1.EventType.MALWARE_DETECTED,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: {
                    hostId: 'web-server-01',
                    malwareType: 'trojan.generic',
                    filePath: '/tmp/suspicious.exe',
                    hash: 'a1b2c3d4e5f6789012345678901234567890abcd',
                },
                tenantId,
                userId,
            });
            // Data breach alert
            const dataBreachEvent = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({
                    source: 'dlp-system',
                    version: '1.0',
                    tags: ['security', 'data-breach', 'compliance'],
                    customFields: {
                        severity: 'critical',
                        dataClassification: 'PII',
                        complianceFramework: 'GDPR'
                    },
                }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date(incidentTime.getTime() + 600000)), // 10 minutes later
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.DLP,
                    identifier: 'dlp-001',
                    name: 'Data Loss Prevention System',
                }),
                type: event_type_enum_1.EventType.DATA_BREACH,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: {
                    dataType: 'customer-records',
                    recordCount: 1500,
                    exfiltrationMethod: 'network-transfer',
                    destinationIp: '*************',
                },
                tenantId,
                userId,
            });
            const incidentEvents = [networkIntrusionEvent, malwareEvent, dataBreachEvent];
            // Act - Execute comprehensive security workflow
            const startTime = Date.now();
            const result = await orchestratorService.orchestrateSecurityWorkflow(incidentEvents, {
                tenantId: tenantId.value,
                userId: userId.value,
                correlationId: correlationId.value,
                priority: 'CRITICAL',
                mode: 'AUTOMATIC',
                autoResponse: true,
                enableThreatHunting: true,
                enableVulnerabilityScanning: true,
                workflowConfig: {
                    enableEventProcessing: true,
                    enableThreatDetection: true,
                    enableVulnerabilityScanning: true,
                    enableResponseExecution: true,
                    enableCorrelation: true,
                    enableEnrichment: true,
                    batchSize: 5,
                    maxConcurrentOperations: 3,
                    retryAttempts: 2,
                    timeoutMs: 120000,
                },
                customRules: [
                    {
                        id: 'critical-incident-rule',
                        name: 'Critical Security Incident Response',
                        description: 'Automated response for critical security incidents',
                        condition: {
                            eventTypes: [event_type_enum_1.EventType.DATA_BREACH, event_type_enum_1.EventType.MALWARE_DETECTED],
                            severityLevels: [event_severity_enum_1.EventSeverity.CRITICAL],
                            riskScoreThreshold: 8.0,
                        },
                        action: 'AUTO_RESPOND',
                        priority: 1,
                        enabled: true,
                    },
                ],
            });
            const endTime = Date.now();
            // Assert - Comprehensive validation
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            // Workflow execution validation
            expect(orchestrationResult.success).toBe(true);
            expect(orchestrationResult.eventsProcessed).toBe(3);
            expect(orchestrationResult.duration).toBeGreaterThan(0);
            expect(orchestrationResult.duration).toBeLessThan(10000); // Should complete within 10 seconds
            // Event processing validation
            expect(orchestrationResult.processingResults).toHaveLength(3);
            orchestrationResult.processingResults.forEach(result => {
                expect(result.success).toBe(true);
                expect(result.processingSteps).toContain('normalize');
                expect(result.processingSteps).toContain('enrich');
                expect(result.processingSteps).toContain('correlate');
            });
            // Threat detection validation
            expect(orchestrationResult.threatAnalyses).toHaveLength(3);
            expect(orchestrationResult.threatsDetected).toBeGreaterThan(0);
            const criticalThreats = orchestrationResult.threatAnalyses
                .flatMap(analysis => analysis.threats)
                .filter(threat => threat.severity === threat_severity_enum_1.ThreatSeverity.CRITICAL);
            expect(criticalThreats.length).toBeGreaterThan(0);
            // Response execution validation
            expect(orchestrationResult.responseResults).toHaveLength(3);
            expect(orchestrationResult.actionsExecuted).toBeGreaterThan(0);
            // Correlation validation
            expect(orchestrationResult.correlationsCreated).toBeGreaterThanOrEqual(0);
            // Error handling validation
            expect(orchestrationResult.errors).toHaveLength(0);
            // Performance metrics validation
            expect(orchestrationResult.metrics).toBeDefined();
            expect(orchestrationResult.metrics.totalProcessingTime).toBeGreaterThan(0);
            expect(orchestrationResult.metrics.throughput).toBeGreaterThan(0);
            expect(orchestrationResult.metrics.successRate).toBe(100);
            // Recommendations validation
            expect(orchestrationResult.recommendations).toBeDefined();
            expect(orchestrationResult.recommendations.length).toBeGreaterThan(0);
            expect(orchestrationResult.recommendations).toContain('Investigate immediately');
            // Repository metrics validation
            const repoMetrics = eventRepository.getMetrics();
            expect(repoMetrics.saveCount).toBeGreaterThan(0);
            expect(repoMetrics.queryCount).toBeGreaterThan(0);
            // Service metrics validation
            const processorMetrics = eventProcessor.getMetrics();
            expect(processorMetrics.processEventCount).toBe(3);
            expect(processorMetrics.totalProcessingTime).toBeGreaterThan(0);
            const detectorMetrics = threatDetector.getMetrics();
            expect(detectorMetrics.analyzeEventCount).toBe(3);
            expect(detectorMetrics.threatsDetected).toBeGreaterThan(0);
            expect(detectorMetrics.totalAnalysisTime).toBeGreaterThan(0);
            // Audit trail validation
            expect(orchestrationResult.orchestrationId).toBeDefined();
            expect(orchestrationResult.startTime).toBeInstanceOf(Date);
            expect(orchestrationResult.endTime).toBeInstanceOf(Date);
            expect(orchestrationResult.endTime.getTime()).toBeGreaterThan(orchestrationResult.startTime.getTime());
        });
        it('should handle high-volume security event processing efficiently', async () => {
            // Arrange - Generate high volume of diverse security events
            const eventCount = 100;
            const tenantId = tenant_id_value_object_1.TenantId.create();
            const userId = user_id_value_object_1.UserId.create();
            const eventTypes = [
                event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                event_type_enum_1.EventType.NETWORK_INTRUSION,
                event_type_enum_1.EventType.MALWARE_DETECTED,
                event_type_enum_1.EventType.SECURITY_ALERT,
                event_type_enum_1.EventType.DATA_BREACH,
            ];
            const severityLevels = [
                event_severity_enum_1.EventSeverity.LOW,
                event_severity_enum_1.EventSeverity.MEDIUM,
                event_severity_enum_1.EventSeverity.HIGH,
                event_severity_enum_1.EventSeverity.CRITICAL,
            ];
            const events = Array.from({ length: eventCount }, (_, i) => {
                const eventType = eventTypes[i % eventTypes.length];
                const severity = severityLevels[i % severityLevels.length];
                return event_factory_1.EventFactory.create({
                    metadata: event_metadata_value_object_1.EventMetadata.create({
                        source: `source-${i % 10}`,
                        version: '1.0',
                        tags: ['security', 'bulk-test'],
                        customFields: { batchId: Math.floor(i / 10) },
                    }),
                    timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date(Date.now() + i * 1000)),
                    source: event_source_value_object_1.EventSource.create({
                        type: event_source_type_enum_1.EventSourceType.APPLICATION,
                        identifier: `app-${i % 20}`,
                        name: `Application ${i % 20}`,
                    }),
                    type: eventType,
                    severity,
                    status: event_status_enum_1.EventStatus.RECEIVED,
                    payload: {
                        eventIndex: i,
                        batchId: Math.floor(i / 10),
                        sourceIp: `192.168.${Math.floor(i / 256)}.${i % 256}`,
                    },
                    tenantId,
                    userId,
                });
            });
            // Act - Process high volume with optimized configuration
            const startTime = Date.now();
            const result = await orchestratorService.orchestrateSecurityWorkflow(events, {
                tenantId: tenantId.value,
                userId: userId.value,
                priority: 'MEDIUM',
                mode: 'AUTOMATIC',
                autoResponse: false, // Disable to focus on processing performance
                enableThreatHunting: true,
                enableVulnerabilityScanning: false, // Disable to focus on core processing
                workflowConfig: {
                    enableEventProcessing: true,
                    enableThreatDetection: true,
                    enableVulnerabilityScanning: false,
                    enableResponseExecution: false,
                    enableCorrelation: true,
                    enableEnrichment: true,
                    batchSize: 10,
                    maxConcurrentOperations: 5,
                    retryAttempts: 1,
                    timeoutMs: 300000, // 5 minutes
                },
            });
            const endTime = Date.now();
            const totalTime = endTime - startTime;
            // Assert - Performance and correctness validation
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            // Correctness validation
            expect(orchestrationResult.success).toBe(true);
            expect(orchestrationResult.eventsProcessed).toBe(eventCount);
            expect(orchestrationResult.processingResults).toHaveLength(eventCount);
            expect(orchestrationResult.threatAnalyses).toHaveLength(eventCount);
            // Performance validation
            expect(totalTime).toBeLessThan(30000); // Should complete within 30 seconds
            expect(orchestrationResult.duration).toBeGreaterThan(0);
            const throughput = eventCount / (totalTime / 1000); // events per second
            expect(throughput).toBeGreaterThan(3); // At least 3 events per second
            // Verify all events processed successfully
            const successfulProcessing = orchestrationResult.processingResults.filter(r => r.success);
            expect(successfulProcessing).toHaveLength(eventCount);
            // Verify threat detection worked for high/critical severity events
            const highSeverityEvents = events.filter(e => e.severity === event_severity_enum_1.EventSeverity.HIGH || e.severity === event_severity_enum_1.EventSeverity.CRITICAL);
            expect(orchestrationResult.threatsDetected).toBeGreaterThanOrEqual(highSeverityEvents.length);
            // Verify no errors in bulk processing
            expect(orchestrationResult.errors).toHaveLength(0);
            // Verify metrics are reasonable
            expect(orchestrationResult.metrics.successRate).toBe(100);
            expect(orchestrationResult.metrics.throughput).toBeGreaterThan(0);
            expect(orchestrationResult.metrics.averageEventProcessingTime).toBeGreaterThan(0);
            expect(orchestrationResult.metrics.averageEventProcessingTime).toBeLessThan(1000); // Less than 1 second per event
            // Repository performance validation
            const repoMetrics = eventRepository.getMetrics();
            expect(repoMetrics.saveCount).toBeGreaterThanOrEqual(eventCount);
            // Service performance validation
            const processorMetrics = eventProcessor.getMetrics();
            expect(processorMetrics.processEventCount).toBe(eventCount);
            expect(processorMetrics.totalProcessingTime).toBeGreaterThan(0);
            const detectorMetrics = threatDetector.getMetrics();
            expect(detectorMetrics.analyzeEventCount).toBe(eventCount);
            expect(detectorMetrics.totalAnalysisTime).toBeGreaterThan(0);
        });
        it('should maintain data consistency across all integration points', async () => {
            // Arrange - Create events that will test all integration points
            const tenantId = tenant_id_value_object_1.TenantId.create();
            const userId = user_id_value_object_1.UserId.create();
            const correlationId = correlation_id_value_object_1.CorrelationId.create();
            const events = [
                // Network security event
                event_factory_1.EventFactory.create({
                    metadata: event_metadata_value_object_1.EventMetadata.create({
                        source: 'network-monitor',
                        version: '1.0',
                        tags: ['network', 'security'],
                    }),
                    timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                    source: event_source_value_object_1.EventSource.create({
                        type: event_source_type_enum_1.EventSourceType.FIREWALL,
                        identifier: 'fw-001',
                        name: 'Main Firewall',
                    }),
                    type: event_type_enum_1.EventType.NETWORK_INTRUSION,
                    severity: event_severity_enum_1.EventSeverity.HIGH,
                    status: event_status_enum_1.EventStatus.RECEIVED,
                    payload: { sourceIp: '**********', targetPort: 22 },
                    tenantId,
                    userId,
                }),
                // Application security event
                event_factory_1.EventFactory.create({
                    metadata: event_metadata_value_object_1.EventMetadata.create({
                        source: 'app-security',
                        version: '1.0',
                        tags: ['application', 'authentication'],
                    }),
                    timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                    source: event_source_value_object_1.EventSource.create({
                        type: event_source_type_enum_1.EventSourceType.APPLICATION,
                        identifier: 'app-001',
                        name: 'Web Application',
                    }),
                    type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                    severity: event_severity_enum_1.EventSeverity.MEDIUM,
                    status: event_status_enum_1.EventStatus.RECEIVED,
                    payload: { username: 'admin', attempts: 5 },
                    tenantId,
                    userId,
                }),
            ];
            // Act - Process with full workflow enabled
            const result = await orchestratorService.orchestrateSecurityWorkflow(events, {
                tenantId: tenantId.value,
                userId: userId.value,
                correlationId: correlationId.value,
                priority: 'HIGH',
                autoResponse: true,
                enableThreatHunting: true,
                enableVulnerabilityScanning: true,
                workflowConfig: {
                    enableEventProcessing: true,
                    enableThreatDetection: true,
                    enableVulnerabilityScanning: true,
                    enableResponseExecution: true,
                    enableCorrelation: true,
                    enableEnrichment: true,
                    batchSize: 2,
                    maxConcurrentOperations: 2,
                    retryAttempts: 2,
                    timeoutMs: 60000,
                },
            });
            // Assert - Data consistency validation
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            // Verify orchestration metadata consistency
            expect(orchestrationResult.orchestrationId).toBeDefined();
            expect(orchestrationResult.startTime).toBeInstanceOf(Date);
            expect(orchestrationResult.endTime).toBeInstanceOf(Date);
            expect(orchestrationResult.endTime.getTime()).toBeGreaterThanOrEqual(orchestrationResult.startTime.getTime());
            // Verify event processing consistency
            expect(orchestrationResult.eventsProcessed).toBe(events.length);
            expect(orchestrationResult.processingResults).toHaveLength(events.length);
            orchestrationResult.processingResults.forEach((result, index) => {
                expect(result.eventId).toBe(events[index].id.toString());
                expect(result.success).toBe(true);
                expect(result.duration).toBeGreaterThan(0);
            });
            // Verify threat analysis consistency
            expect(orchestrationResult.threatAnalyses).toHaveLength(events.length);
            orchestrationResult.threatAnalyses.forEach((analysis, index) => {
                expect(analysis.eventId).toBe(events[index].id.toString());
                expect(analysis.confidence).toBeGreaterThanOrEqual(0);
                expect(analysis.confidence).toBeLessThanOrEqual(100);
                expect(analysis.analysisTime).toBeGreaterThan(0);
            });
            // Verify metrics consistency
            expect(orchestrationResult.metrics).toBeDefined();
            expect(orchestrationResult.metrics.totalProcessingTime).toBeGreaterThan(0);
            expect(orchestrationResult.metrics.throughput).toBeGreaterThan(0);
            expect(orchestrationResult.metrics.successRate).toBe(100);
            // Verify no data corruption or inconsistencies
            expect(orchestrationResult.errors).toHaveLength(0);
            expect(orchestrationResult.warnings).toBeDefined();
            // Cross-validate with repository state
            const savedEvents = await eventRepository.findAll();
            expect(savedEvents.length).toBeGreaterThanOrEqual(events.length);
            // Verify tenant and user context maintained throughout
            orchestrationResult.processingResults.forEach(result => {
                expect(result.eventId).toBeDefined();
                // Additional context validation would go here in a real implementation
            });
        });
    });
    describe('Integration Test Metrics and Reporting', () => {
        it('should provide comprehensive integration test metrics', async () => {
            // Arrange
            const testStartTime = Date.now();
            const events = Array.from({ length: 10 }, (_, i) => event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: `metrics-test-${i}`, version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: `metrics-app-${i}`,
                    name: `Metrics App ${i}`,
                }),
                type: event_type_enum_1.EventType.SECURITY_ALERT,
                severity: i % 2 === 0 ? event_severity_enum_1.EventSeverity.HIGH : event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { testIndex: i },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            }));
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow(events, {
                priority: 'MEDIUM',
                workflowConfig: {
                    enableEventProcessing: true,
                    enableThreatDetection: true,
                    enableVulnerabilityScanning: false,
                    enableResponseExecution: false,
                    enableCorrelation: true,
                    enableEnrichment: true,
                    batchSize: 3,
                    maxConcurrentOperations: 2,
                    retryAttempts: 1,
                    timeoutMs: 60000,
                },
            });
            const testEndTime = Date.now();
            // Assert - Comprehensive metrics validation
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            // Test execution metrics
            const testDuration = testEndTime - testStartTime;
            expect(testDuration).toBeGreaterThan(0);
            expect(testDuration).toBeLessThan(10000); // Should complete within 10 seconds
            // Orchestration metrics
            expect(orchestrationResult.metrics).toBeDefined();
            expect(orchestrationResult.metrics.totalProcessingTime).toBeGreaterThan(0);
            expect(orchestrationResult.metrics.averageEventProcessingTime).toBeGreaterThan(0);
            expect(orchestrationResult.metrics.throughput).toBeGreaterThan(0);
            expect(orchestrationResult.metrics.successRate).toBe(100);
            expect(orchestrationResult.metrics.errorRate).toBe(0);
            // Resource utilization metrics (would be real in production)
            expect(orchestrationResult.metrics.resourceUtilization).toBeDefined();
            expect(orchestrationResult.metrics.resourceUtilization.cpuUsage).toBeGreaterThanOrEqual(0);
            expect(orchestrationResult.metrics.resourceUtilization.memoryUsage).toBeGreaterThanOrEqual(0);
            // Component-specific metrics
            const repoMetrics = eventRepository.getMetrics();
            const processorMetrics = eventProcessor.getMetrics();
            const detectorMetrics = threatDetector.getMetrics();
            // Repository metrics
            expect(repoMetrics.saveCount).toBeGreaterThan(0);
            expect(repoMetrics.queryCount).toBeGreaterThan(0);
            // Processor metrics
            expect(processorMetrics.processEventCount).toBe(events.length);
            expect(processorMetrics.totalProcessingTime).toBeGreaterThan(0);
            // Detector metrics
            expect(detectorMetrics.analyzeEventCount).toBe(events.length);
            expect(detectorMetrics.threatsDetected).toBeGreaterThan(0);
            expect(detectorMetrics.totalAnalysisTime).toBeGreaterThan(0);
            // Performance ratios and efficiency metrics
            const avgProcessingTime = processorMetrics.totalProcessingTime / processorMetrics.processEventCount;
            const avgAnalysisTime = detectorMetrics.totalAnalysisTime / detectorMetrics.analyzeEventCount;
            expect(avgProcessingTime).toBeGreaterThan(0);
            expect(avgProcessingTime).toBeLessThan(100); // Should be under 100ms per event
            expect(avgAnalysisTime).toBeGreaterThan(0);
            expect(avgAnalysisTime).toBeLessThan(100); // Should be under 100ms per analysis
            // Integration test summary
            const integrationTestSummary = {
                testDuration,
                eventsProcessed: orchestrationResult.eventsProcessed,
                threatsDetected: orchestrationResult.threatsDetected,
                actionsExecuted: orchestrationResult.actionsExecuted,
                errorCount: orchestrationResult.errors.length,
                successRate: orchestrationResult.metrics.successRate,
                throughput: orchestrationResult.metrics.throughput,
                avgProcessingTime,
                avgAnalysisTime,
                repositoryOperations: repoMetrics.saveCount + repoMetrics.queryCount + repoMetrics.findCount,
            };
            // Validate integration test summary
            expect(integrationTestSummary.eventsProcessed).toBe(events.length);
            expect(integrationTestSummary.errorCount).toBe(0);
            expect(integrationTestSummary.successRate).toBe(100);
            expect(integrationTestSummary.throughput).toBeGreaterThan(0);
            expect(integrationTestSummary.repositoryOperations).toBeGreaterThan(0);
            // Log summary for test reporting (in real implementation)
            console.log('Integration Test Summary:', integrationTestSummary);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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