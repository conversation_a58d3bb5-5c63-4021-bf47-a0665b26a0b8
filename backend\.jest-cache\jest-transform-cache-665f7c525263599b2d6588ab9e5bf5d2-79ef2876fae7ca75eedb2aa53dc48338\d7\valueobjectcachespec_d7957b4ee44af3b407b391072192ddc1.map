{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\value-object-cache.spec.ts", "mappings": ";AAAA;;;;;GAKG;;AAEH,0EAA2G;AAC3G,6EAAwE;AAExE,mCAAmC;AACnC,MAAM,eAAgB,SAAQ,mCAAe;IAC3C,YAA6B,KAAa;QACxC,KAAK,EAAE,CAAC;QADmB,UAAK,GAAL,KAAK,CAAQ;IAE1C,CAAC;IAES,qBAAqB;QAC7B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACtB,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YAChD,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;CACF;AAED,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,IAAI,KAAwC,CAAC;IAC7C,MAAM,MAAM,GAAgB;QAC1B,OAAO,EAAE,CAAC;QACV,KAAK,EAAE,IAAI;QACX,gBAAgB,EAAE,IAAI;KACvB,CAAC;IAEF,UAAU,CAAC,GAAG,EAAE;QACd,KAAK,GAAG,IAAI,qCAAgB,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,UAAU;YACV,MAAM,GAAG,GAAG,UAAU,CAAC;YACvB,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,YAAY,CAAC,CAAC;YAEhD,MAAM;YACN,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACtB,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAEjC,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM;YACN,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAEzC,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,UAAU;YACV,MAAM,GAAG,GAAG,UAAU,CAAC;YACvB,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,YAAY,CAAC,CAAC;YAEhD,eAAe;YACf,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACtB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,UAAU;YACV,MAAM,GAAG,GAAG,UAAU,CAAC;YACvB,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,YAAY,CAAC,CAAC;YAChD,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAEtB,MAAM;YACN,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAElC,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3B,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,UAAU;YACV,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEjD,MAAM;YACN,KAAK,CAAC,KAAK,EAAE,CAAC;YAEd,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,UAAU;YACV,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEjD,uCAAuC;YACvC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAElB,iEAAiE;YACjE,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEjD,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;YAC1D,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,oBAAoB;YAC3D,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,UAAU;YACV,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEjD,mCAAmC;YACnC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAClB,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAClB,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAElB,kBAAkB;YAClB,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEjD,6DAA6D;YAC7D,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,UAAU;YACV,MAAM,aAAa,GAAG,IAAI,qCAAgB,CAAkB;gBAC1D,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,EAAE,EAAE,WAAW;gBACtB,gBAAgB,EAAE,IAAI;aACvB,CAAC,CAAC;YAEH,MAAM,GAAG,GAAG,UAAU,CAAC;YACvB,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,YAAY,CAAC,CAAC;YAEhD,MAAM;YACN,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9B,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1C,sBAAsB;YACtB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAEvD,SAAS;YACT,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,UAAU;YACV,MAAM,aAAa,GAAG,IAAI,qCAAgB,CAAkB;gBAC1D,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,EAAE;gBACT,gBAAgB,EAAE,IAAI;aACvB,CAAC,CAAC;YAEH,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACzD,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEzD,sBAAsB;YACtB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAEvD,MAAM;YACN,MAAM,YAAY,GAAG,aAAa,CAAC,OAAO,EAAE,CAAC;YAE7C,SAAS;YACT,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,UAAU;YACV,MAAM,GAAG,GAAG,UAAU,CAAC;YACvB,MAAM,WAAW,GAAG,IAAI,eAAe,CAAC,cAAc,CAAC,CAAC;YACxD,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;YAE5B,IAAI,aAAa,GAAG,KAAK,CAAC;YAC1B,MAAM,OAAO,GAAG,GAAG,EAAE;gBACnB,aAAa,GAAG,IAAI,CAAC;gBACrB,OAAO,IAAI,eAAe,CAAC,eAAe,CAAC,CAAC;YAC9C,CAAC,CAAC;YAEF,MAAM;YACN,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAEhD,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,UAAU;YACV,MAAM,GAAG,GAAG,UAAU,CAAC;YACvB,IAAI,aAAa,GAAG,KAAK,CAAC;YAC1B,MAAM,OAAO,GAAG,GAAG,EAAE;gBACnB,aAAa,GAAG,IAAI,CAAC;gBACrB,OAAO,IAAI,eAAe,CAAC,eAAe,CAAC,CAAC;YAC9C,CAAC,CAAC;YAEF,MAAM;YACN,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAEhD,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAChD,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,UAAU;YACV,MAAM,GAAG,GAAG,UAAU,CAAC;YACvB,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,YAAY,CAAC,CAAC;YAEhD,MAAM;YACN,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO;YAClC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACtB,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;YACtB,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;YACtB,KAAK,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC,CAAC,OAAO;YAE1C,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;YAEpC,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,gBAAgB;YAChB,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,wBAAwB;YAE1E,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;YAEpC,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,UAAU;YACV,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEjD,MAAM;YACN,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;YAEpC,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,UAAU;YACV,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAClB,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAE1B,MAAM;YACN,KAAK,CAAC,eAAe,EAAE,CAAC;YACxB,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;YAEpC,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,UAAU;YACV,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEjD,6BAA6B;YAC7B,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAClB,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAClB,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAElB,MAAM;YACN,MAAM,kBAAkB,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC;YAEzD,SAAS;YACT,MAAM,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,gBAAgB;YAChE,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,UAAU;YACV,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEjD,6CAA6C;YAC7C,UAAU,CAAC,GAAG,EAAE;gBACd,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACnD,CAAC,EAAE,EAAE,CAAC,CAAC;YAEP,UAAU,CAAC,GAAG,EAAE;gBACd,MAAM;gBACN,MAAM,gBAAgB,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;gBAErD,SAAS;gBACT,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACzC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc;YAC9D,CAAC,EAAE,EAAE,CAAC,CAAC;QACT,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,UAAU;YACV,MAAM,OAAO,GAAG;gBACd,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE;gBACrD,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE;aACtD,CAAC;YAEF,MAAM;YACN,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAEtB,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,UAAU;YACV,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEjD,MAAM;YACN,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;YAE1B,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;IACvC,IAAI,OAAgC,CAAC;IAErC,UAAU,CAAC,GAAG,EAAE;QACd,OAAO,GAAG,4CAAuB,CAAC,WAAW,EAAE,CAAC;QAChD,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,4BAA4B;IAClD,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM;YACN,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAkB,YAAY,CAAC,CAAC;YAC/D,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAkB,YAAY,CAAC,CAAC;YAE/D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,8BAA8B;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,UAAU;YACV,MAAM,MAAM,GAAgB;gBAC1B,OAAO,EAAE,GAAG;gBACZ,KAAK,EAAE,MAAM;gBACb,gBAAgB,EAAE,KAAK;aACxB,CAAC;YAEF,MAAM;YACN,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAkB,cAAc,EAAE,MAAM,CAAC,CAAC;YACxE,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;YAE/C,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;YAEpC,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,UAAU;YACV,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAkB,QAAQ,CAAC,CAAC;YAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAkB,QAAQ,CAAC,CAAC;YAE3D,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAElD,MAAM;YACN,MAAM,QAAQ,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAE5C,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,UAAU;YACV,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAkB,QAAQ,CAAC,CAAC;YAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAkB,QAAQ,CAAC,CAAC;YAE3D,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAElD,MAAM;YACN,OAAO,CAAC,QAAQ,EAAE,CAAC;YAEnB,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,UAAU;YACV,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAkB,QAAQ,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;YACvF,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAkB,QAAQ,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;YAEvF,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAElD,sBAAsB;YACtB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAEvD,MAAM;YACN,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;YAE1C,SAAS;YACT,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,UAAU;YACV,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAkB,QAAQ,CAAC,CAAC;YAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAkB,QAAQ,CAAC,CAAC;YAE3D,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAElD,MAAM;YACN,MAAM,WAAW,GAAG,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAElD,SAAS;YACT,MAAM,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM;YACN,MAAM,SAAS,GAAG,4CAAuB,CAAC,WAAW,EAAE,CAAC;YACxD,MAAM,SAAS,GAAG,4CAAuB,CAAC,WAAW,EAAE,CAAC;YAExD,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,UAAU;YACV,MAAM,KAAK,GAAG,IAAI,qCAAgB,CAAkB;gBAClD,OAAO,EAAE,IAAI;gBACb,KAAK,EAAE,MAAM;gBACb,gBAAgB,EAAE,IAAI;aACvB,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,kCAAkC;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC/B,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,gCAAgC;gBAC9D,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBAEhD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;oBAChB,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBACxB,CAAC;qBAAM,CAAC;oBACN,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACjB,CAAC;YACH,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAErC,SAAS;YACT,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,oCAAoC;YAEzE,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,yBAAyB;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,UAAU;YACV,MAAM,KAAK,GAAG,IAAI,qCAAgB,CAAkB;gBAClD,OAAO,EAAE,EAAE,EAAE,iCAAiC;gBAC9C,KAAK,EAAE,MAAM;gBACb,gBAAgB,EAAE,IAAI;aACvB,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,0CAA0C;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9B,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3D,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAErC,SAAS;YACT,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,sCAAsC;YAC1E,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,2BAA2B;YAE1D,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\value-object-cache.spec.ts"], "sourcesContent": ["/**\r\n * Value Object Cache Tests\r\n * \r\n * Comprehensive tests for value object caching functionality including\r\n * LRU eviction, TTL expiration, statistics, and performance validation.\r\n */\r\n\r\nimport { ValueObjectCache, ValueObjectCacheManager, CacheConfig } from '../../patterns/value-object-cache';\r\nimport { BaseValueObject } from '../../value-objects/base-value-object';\r\n\r\n// Test value object implementation\r\nclass TestValueObject extends BaseValueObject {\r\n  constructor(private readonly value: string) {\r\n    super();\r\n  }\r\n\r\n  protected getEqualityComponents(): any[] {\r\n    return [this.value];\r\n  }\r\n\r\n  getHashCode(): number {\r\n    return this.value.split('').reduce((hash, char) => {\r\n      return ((hash << 5) - hash) + char.charCodeAt(0);\r\n    }, 0);\r\n  }\r\n\r\n  toString(): string {\r\n    return this.value;\r\n  }\r\n}\r\n\r\ndescribe('ValueObjectCache', () => {\r\n  let cache: ValueObjectCache<TestValueObject>;\r\n  const config: CacheConfig = {\r\n    maxSize: 3,\r\n    ttlMs: 1000,\r\n    enableStatistics: true,\r\n  };\r\n\r\n  beforeEach(() => {\r\n    cache = new ValueObjectCache(config);\r\n  });\r\n\r\n  describe('basic operations', () => {\r\n    it('should store and retrieve value objects', () => {\r\n      // Arrange\r\n      const key = 'test-key';\r\n      const value = new TestValueObject('test-value');\r\n\r\n      // Act\r\n      cache.put(key, value);\r\n      const retrieved = cache.get(key);\r\n\r\n      // Assert\r\n      expect(retrieved).toBeDefined();\r\n      expect(retrieved?.toString()).toBe('test-value');\r\n    });\r\n\r\n    it('should return undefined for non-existent keys', () => {\r\n      // Act\r\n      const result = cache.get('non-existent');\r\n\r\n      // Assert\r\n      expect(result).toBeUndefined();\r\n    });\r\n\r\n    it('should check key existence correctly', () => {\r\n      // Arrange\r\n      const key = 'test-key';\r\n      const value = new TestValueObject('test-value');\r\n\r\n      // Act & Assert\r\n      expect(cache.has(key)).toBe(false);\r\n      cache.put(key, value);\r\n      expect(cache.has(key)).toBe(true);\r\n    });\r\n\r\n    it('should delete entries correctly', () => {\r\n      // Arrange\r\n      const key = 'test-key';\r\n      const value = new TestValueObject('test-value');\r\n      cache.put(key, value);\r\n\r\n      // Act\r\n      const deleted = cache.delete(key);\r\n\r\n      // Assert\r\n      expect(deleted).toBe(true);\r\n      expect(cache.has(key)).toBe(false);\r\n      expect(cache.get(key)).toBeUndefined();\r\n    });\r\n\r\n    it('should clear all entries', () => {\r\n      // Arrange\r\n      cache.put('key1', new TestValueObject('value1'));\r\n      cache.put('key2', new TestValueObject('value2'));\r\n\r\n      // Act\r\n      cache.clear();\r\n\r\n      // Assert\r\n      expect(cache.size()).toBe(0);\r\n      expect(cache.has('key1')).toBe(false);\r\n      expect(cache.has('key2')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('LRU eviction', () => {\r\n    it('should evict least recently used item when at capacity', () => {\r\n      // Arrange\r\n      cache.put('key1', new TestValueObject('value1'));\r\n      cache.put('key2', new TestValueObject('value2'));\r\n      cache.put('key3', new TestValueObject('value3'));\r\n\r\n      // Access key1 to make it recently used\r\n      cache.get('key1');\r\n\r\n      // Act - add fourth item, should evict key2 (least recently used)\r\n      cache.put('key4', new TestValueObject('value4'));\r\n\r\n      // Assert\r\n      expect(cache.size()).toBe(3);\r\n      expect(cache.has('key1')).toBe(true); // Recently accessed\r\n      expect(cache.has('key2')).toBe(false); // Should be evicted\r\n      expect(cache.has('key3')).toBe(true);\r\n      expect(cache.has('key4')).toBe(true);\r\n    });\r\n\r\n    it('should update access time when retrieving items', () => {\r\n      // Arrange\r\n      cache.put('key1', new TestValueObject('value1'));\r\n      cache.put('key2', new TestValueObject('value2'));\r\n      cache.put('key3', new TestValueObject('value3'));\r\n\r\n      // Act - access key1 multiple times\r\n      cache.get('key1');\r\n      cache.get('key1');\r\n      cache.get('key1');\r\n\r\n      // Add fourth item\r\n      cache.put('key4', new TestValueObject('value4'));\r\n\r\n      // Assert - key1 should still be present due to recent access\r\n      expect(cache.has('key1')).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('TTL expiration', () => {\r\n    it('should expire entries after TTL', async () => {\r\n      // Arrange\r\n      const shortTtlCache = new ValueObjectCache<TestValueObject>({\r\n        maxSize: 10,\r\n        ttlMs: 50, // 50ms TTL\r\n        enableStatistics: true,\r\n      });\r\n\r\n      const key = 'test-key';\r\n      const value = new TestValueObject('test-value');\r\n\r\n      // Act\r\n      shortTtlCache.put(key, value);\r\n      expect(shortTtlCache.has(key)).toBe(true);\r\n\r\n      // Wait for expiration\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      // Assert\r\n      expect(shortTtlCache.has(key)).toBe(false);\r\n      expect(shortTtlCache.get(key)).toBeUndefined();\r\n    });\r\n\r\n    it('should cleanup expired entries', async () => {\r\n      // Arrange\r\n      const shortTtlCache = new ValueObjectCache<TestValueObject>({\r\n        maxSize: 10,\r\n        ttlMs: 50,\r\n        enableStatistics: true,\r\n      });\r\n\r\n      shortTtlCache.put('key1', new TestValueObject('value1'));\r\n      shortTtlCache.put('key2', new TestValueObject('value2'));\r\n\r\n      // Wait for expiration\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      // Act\r\n      const removedCount = shortTtlCache.cleanup();\r\n\r\n      // Assert\r\n      expect(removedCount).toBe(2);\r\n      expect(shortTtlCache.size()).toBe(0);\r\n    });\r\n  });\r\n\r\n  describe('getOrCompute functionality', () => {\r\n    it('should return cached value if present', () => {\r\n      // Arrange\r\n      const key = 'test-key';\r\n      const cachedValue = new TestValueObject('cached-value');\r\n      cache.put(key, cachedValue);\r\n\r\n      let factoryCalled = false;\r\n      const factory = () => {\r\n        factoryCalled = true;\r\n        return new TestValueObject('factory-value');\r\n      };\r\n\r\n      // Act\r\n      const result = cache.getOrCompute(key, factory);\r\n\r\n      // Assert\r\n      expect(result.toString()).toBe('cached-value');\r\n      expect(factoryCalled).toBe(false);\r\n    });\r\n\r\n    it('should compute and cache value if not present', () => {\r\n      // Arrange\r\n      const key = 'test-key';\r\n      let factoryCalled = false;\r\n      const factory = () => {\r\n        factoryCalled = true;\r\n        return new TestValueObject('factory-value');\r\n      };\r\n\r\n      // Act\r\n      const result = cache.getOrCompute(key, factory);\r\n\r\n      // Assert\r\n      expect(result.toString()).toBe('factory-value');\r\n      expect(factoryCalled).toBe(true);\r\n      expect(cache.has(key)).toBe(true);\r\n      expect(cache.get(key)?.toString()).toBe('factory-value');\r\n    });\r\n  });\r\n\r\n  describe('statistics', () => {\r\n    it('should track cache hits and misses', () => {\r\n      // Arrange\r\n      const key = 'test-key';\r\n      const value = new TestValueObject('test-value');\r\n\r\n      // Act\r\n      cache.get('non-existent'); // Miss\r\n      cache.put(key, value);\r\n      cache.get(key); // Hit\r\n      cache.get(key); // Hit\r\n      cache.get('another-non-existent'); // Miss\r\n\r\n      const stats = cache.getStatistics();\r\n\r\n      // Assert\r\n      expect(stats.hits).toBe(2);\r\n      expect(stats.misses).toBe(2);\r\n      expect(stats.hitRate).toBe(0.5);\r\n    });\r\n\r\n    it('should track evictions', () => {\r\n      // Arrange & Act\r\n      cache.put('key1', new TestValueObject('value1'));\r\n      cache.put('key2', new TestValueObject('value2'));\r\n      cache.put('key3', new TestValueObject('value3'));\r\n      cache.put('key4', new TestValueObject('value4')); // Should cause eviction\r\n\r\n      const stats = cache.getStatistics();\r\n\r\n      // Assert\r\n      expect(stats.evictions).toBe(1);\r\n      expect(stats.size).toBe(3);\r\n      expect(stats.maxSize).toBe(3);\r\n    });\r\n\r\n    it('should estimate memory usage', () => {\r\n      // Arrange\r\n      cache.put('key1', new TestValueObject('value1'));\r\n      cache.put('key2', new TestValueObject('value2'));\r\n\r\n      // Act\r\n      const stats = cache.getStatistics();\r\n\r\n      // Assert\r\n      expect(stats.memoryUsage).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should reset statistics', () => {\r\n      // Arrange\r\n      cache.put('key1', new TestValueObject('value1'));\r\n      cache.get('key1');\r\n      cache.get('non-existent');\r\n\r\n      // Act\r\n      cache.resetStatistics();\r\n      const stats = cache.getStatistics();\r\n\r\n      // Assert\r\n      expect(stats.hits).toBe(0);\r\n      expect(stats.misses).toBe(0);\r\n      expect(stats.evictions).toBe(0);\r\n    });\r\n  });\r\n\r\n  describe('entry analysis', () => {\r\n    it('should return entries by frequency', () => {\r\n      // Arrange\r\n      cache.put('key1', new TestValueObject('value1'));\r\n      cache.put('key2', new TestValueObject('value2'));\r\n      cache.put('key3', new TestValueObject('value3'));\r\n\r\n      // Access key2 multiple times\r\n      cache.get('key2');\r\n      cache.get('key2');\r\n      cache.get('key1');\r\n\r\n      // Act\r\n      const entriesByFrequency = cache.getEntriesByFrequency();\r\n\r\n      // Assert\r\n      expect(entriesByFrequency).toHaveLength(3);\r\n      expect(entriesByFrequency[0].key).toBe('key2'); // Most accessed\r\n      expect(entriesByFrequency[0].accessCount).toBe(3); // Initial put + 2 gets\r\n    });\r\n\r\n    it('should return entries by recency', () => {\r\n      // Arrange\r\n      cache.put('key1', new TestValueObject('value1'));\r\n      \r\n      // Small delay to ensure different timestamps\r\n      setTimeout(() => {\r\n        cache.put('key2', new TestValueObject('value2'));\r\n      }, 10);\r\n\r\n      setTimeout(() => {\r\n        // Act\r\n        const entriesByRecency = cache.getEntriesByRecency();\r\n\r\n        // Assert\r\n        expect(entriesByRecency).toHaveLength(2);\r\n        expect(entriesByRecency[0].key).toBe('key2'); // Most recent\r\n      }, 20);\r\n    });\r\n\r\n    it('should warm up cache with provided entries', () => {\r\n      // Arrange\r\n      const entries = [\r\n        { key: 'key1', value: new TestValueObject('value1') },\r\n        { key: 'key2', value: new TestValueObject('value2') },\r\n      ];\r\n\r\n      // Act\r\n      cache.warmUp(entries);\r\n\r\n      // Assert\r\n      expect(cache.size()).toBe(2);\r\n      expect(cache.has('key1')).toBe(true);\r\n      expect(cache.has('key2')).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('keys retrieval', () => {\r\n    it('should return all cache keys', () => {\r\n      // Arrange\r\n      cache.put('key1', new TestValueObject('value1'));\r\n      cache.put('key2', new TestValueObject('value2'));\r\n      cache.put('key3', new TestValueObject('value3'));\r\n\r\n      // Act\r\n      const keys = cache.keys();\r\n\r\n      // Assert\r\n      expect(keys).toHaveLength(3);\r\n      expect(keys).toContain('key1');\r\n      expect(keys).toContain('key2');\r\n      expect(keys).toContain('key3');\r\n    });\r\n  });\r\n});\r\n\r\ndescribe('ValueObjectCacheManager', () => {\r\n  let manager: ValueObjectCacheManager;\r\n\r\n  beforeEach(() => {\r\n    manager = ValueObjectCacheManager.getInstance();\r\n    manager.clearAll(); // Clean state for each test\r\n  });\r\n\r\n  describe('cache management', () => {\r\n    it('should create and retrieve caches', () => {\r\n      // Act\r\n      const cache1 = manager.getCache<TestValueObject>('test-cache');\r\n      const cache2 = manager.getCache<TestValueObject>('test-cache');\r\n\r\n      // Assert\r\n      expect(cache1).toBe(cache2); // Should return same instance\r\n    });\r\n\r\n    it('should create caches with custom configuration', () => {\r\n      // Arrange\r\n      const config: CacheConfig = {\r\n        maxSize: 500,\r\n        ttlMs: 600000,\r\n        enableStatistics: false,\r\n      };\r\n\r\n      // Act\r\n      const cache = manager.getCache<TestValueObject>('custom-cache', config);\r\n      cache.put('test', new TestValueObject('test'));\r\n\r\n      const stats = cache.getStatistics();\r\n\r\n      // Assert\r\n      expect(stats.maxSize).toBe(500);\r\n    });\r\n\r\n    it('should get statistics from all caches', () => {\r\n      // Arrange\r\n      const cache1 = manager.getCache<TestValueObject>('cache1');\r\n      const cache2 = manager.getCache<TestValueObject>('cache2');\r\n\r\n      cache1.put('key1', new TestValueObject('value1'));\r\n      cache2.put('key2', new TestValueObject('value2'));\r\n\r\n      // Act\r\n      const allStats = manager.getAllStatistics();\r\n\r\n      // Assert\r\n      expect(Object.keys(allStats)).toHaveLength(2);\r\n      expect(allStats['cache1']).toBeDefined();\r\n      expect(allStats['cache2']).toBeDefined();\r\n      expect(allStats['cache1'].size).toBe(1);\r\n      expect(allStats['cache2'].size).toBe(1);\r\n    });\r\n\r\n    it('should clear all caches', () => {\r\n      // Arrange\r\n      const cache1 = manager.getCache<TestValueObject>('cache1');\r\n      const cache2 = manager.getCache<TestValueObject>('cache2');\r\n\r\n      cache1.put('key1', new TestValueObject('value1'));\r\n      cache2.put('key2', new TestValueObject('value2'));\r\n\r\n      // Act\r\n      manager.clearAll();\r\n\r\n      // Assert\r\n      expect(cache1.size()).toBe(0);\r\n      expect(cache2.size()).toBe(0);\r\n    });\r\n\r\n    it('should cleanup expired entries in all caches', async () => {\r\n      // Arrange\r\n      const cache1 = manager.getCache<TestValueObject>('cache1', { maxSize: 10, ttlMs: 50 });\r\n      const cache2 = manager.getCache<TestValueObject>('cache2', { maxSize: 10, ttlMs: 50 });\r\n\r\n      cache1.put('key1', new TestValueObject('value1'));\r\n      cache2.put('key2', new TestValueObject('value2'));\r\n\r\n      // Wait for expiration\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      // Act\r\n      const totalRemoved = manager.cleanupAll();\r\n\r\n      // Assert\r\n      expect(totalRemoved).toBe(2);\r\n    });\r\n\r\n    it('should calculate total memory usage', () => {\r\n      // Arrange\r\n      const cache1 = manager.getCache<TestValueObject>('cache1');\r\n      const cache2 = manager.getCache<TestValueObject>('cache2');\r\n\r\n      cache1.put('key1', new TestValueObject('value1'));\r\n      cache2.put('key2', new TestValueObject('value2'));\r\n\r\n      // Act\r\n      const totalMemory = manager.getTotalMemoryUsage();\r\n\r\n      // Assert\r\n      expect(totalMemory).toBeGreaterThan(0);\r\n    });\r\n  });\r\n\r\n  describe('singleton behavior', () => {\r\n    it('should return same instance', () => {\r\n      // Act\r\n      const instance1 = ValueObjectCacheManager.getInstance();\r\n      const instance2 = ValueObjectCacheManager.getInstance();\r\n\r\n      // Assert\r\n      expect(instance1).toBe(instance2);\r\n    });\r\n  });\r\n});\r\n\r\ndescribe('Performance Tests', () => {\r\n  describe('cache performance', () => {\r\n    it('should handle high-volume operations efficiently', () => {\r\n      // Arrange\r\n      const cache = new ValueObjectCache<TestValueObject>({\r\n        maxSize: 1000,\r\n        ttlMs: 300000,\r\n        enableStatistics: true,\r\n      });\r\n\r\n      const startTime = Date.now();\r\n\r\n      // Act - Perform 10,000 operations\r\n      for (let i = 0; i < 10000; i++) {\r\n        const key = `key-${i % 100}`; // Reuse keys to test cache hits\r\n        const value = new TestValueObject(`value-${i}`);\r\n        \r\n        if (i % 2 === 0) {\r\n          cache.put(key, value);\r\n        } else {\r\n          cache.get(key);\r\n        }\r\n      }\r\n\r\n      const endTime = Date.now();\r\n      const duration = endTime - startTime;\r\n\r\n      // Assert\r\n      expect(duration).toBeLessThan(1000); // Should complete in under 1 second\r\n      \r\n      const stats = cache.getStatistics();\r\n      expect(stats.hitRate).toBeGreaterThan(0); // Should have cache hits\r\n    });\r\n\r\n    it('should maintain performance with frequent evictions', () => {\r\n      // Arrange\r\n      const cache = new ValueObjectCache<TestValueObject>({\r\n        maxSize: 10, // Small cache to force evictions\r\n        ttlMs: 300000,\r\n        enableStatistics: true,\r\n      });\r\n\r\n      const startTime = Date.now();\r\n\r\n      // Act - Add many items to force evictions\r\n      for (let i = 0; i < 1000; i++) {\r\n        cache.put(`key-${i}`, new TestValueObject(`value-${i}`));\r\n      }\r\n\r\n      const endTime = Date.now();\r\n      const duration = endTime - startTime;\r\n\r\n      // Assert\r\n      expect(duration).toBeLessThan(500); // Should handle evictions efficiently\r\n      expect(cache.size()).toBe(10); // Should maintain max size\r\n      \r\n      const stats = cache.getStatistics();\r\n      expect(stats.evictions).toBeGreaterThan(0);\r\n    });\r\n  });\r\n});"], "version": 3}