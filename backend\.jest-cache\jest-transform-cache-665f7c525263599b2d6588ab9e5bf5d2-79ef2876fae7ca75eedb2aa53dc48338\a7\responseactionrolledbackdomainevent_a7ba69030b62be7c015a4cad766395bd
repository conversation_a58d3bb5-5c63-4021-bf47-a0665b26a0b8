db548c44587f06f8780f378ca9224b2e
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseActionRolledBackDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const action_type_enum_1 = require("../enums/action-type.enum");
/**
 * Response Action Rolled Back Domain Event
 *
 * Raised when a response action has been successfully rolled back.
 * This event indicates that a previously executed action has been
 * reversed or undone, typically to restore a previous state.
 *
 * Key information:
 * - Action type and rollback details
 * - Who performed the rollback
 * - Rollback timestamp and results
 * - Original execution context
 *
 * Use cases:
 * - Track action rollback completion
 * - Update system state after rollback
 * - Notify stakeholders of rollback
 * - Generate rollback audit trails
 * - Trigger post-rollback validation
 * - Update monitoring and dashboards
 */
class ResponseActionRolledBackDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the action type that was rolled back
     */
    get actionType() {
        return this.eventData.actionType;
    }
    /**
     * Get who performed the rollback
     */
    get rolledBackBy() {
        return this.eventData.rolledBackBy;
    }
    /**
     * Get when the rollback was performed
     */
    get rolledBackAt() {
        return this.eventData.rolledBackAt;
    }
    /**
     * Get the rollback results
     */
    get rollbackResults() {
        return this.eventData.rollbackResults;
    }
    /**
     * Get the original execution results
     */
    get originalExecutionResults() {
        return this.eventData.originalExecutionResults;
    }
    /**
     * Check if the rollback was successful
     */
    isRollbackSuccessful() {
        // Assume successful if rollback results exist and no error is indicated
        return this.eventData.rollbackResults !== undefined &&
            !this.eventData.rollbackResults?.error;
    }
    /**
     * Check if this was an automated rollback
     */
    isAutomatedRollback() {
        return this.eventData.rolledBackBy.includes('system') ||
            this.eventData.rolledBackBy.includes('automation') ||
            this.eventData.rolledBackBy.includes('bot');
    }
    /**
     * Check if this was a manual rollback
     */
    isManualRollback() {
        return !this.isAutomatedRollback();
    }
    /**
     * Check if this is a security-critical action rollback
     */
    isSecurityCriticalRollback() {
        const criticalActions = [
            action_type_enum_1.ActionType.ISOLATE_SYSTEM,
            action_type_enum_1.ActionType.SHUTDOWN_SYSTEM,
            action_type_enum_1.ActionType.DELETE_FILE,
            action_type_enum_1.ActionType.REMOVE_MALWARE,
            action_type_enum_1.ActionType.DISABLE_ACCOUNT,
            action_type_enum_1.ActionType.BLOCK_IP,
            action_type_enum_1.ActionType.QUARANTINE_FILE,
            action_type_enum_1.ActionType.UPDATE_FIREWALL,
            action_type_enum_1.ActionType.PATCH_VULNERABILITY,
        ];
        return criticalActions.includes(this.eventData.actionType);
    }
    /**
     * Check if this is a containment action rollback
     */
    isContainmentRollback() {
        const containmentActions = [
            action_type_enum_1.ActionType.ISOLATE_SYSTEM,
            action_type_enum_1.ActionType.QUARANTINE_FILE,
            action_type_enum_1.ActionType.BLOCK_IP,
            action_type_enum_1.ActionType.BLOCK_DOMAIN,
            action_type_enum_1.ActionType.BLOCK_URL,
            action_type_enum_1.ActionType.DISABLE_ACCOUNT,
            action_type_enum_1.ActionType.REVOKE_TOKEN,
            action_type_enum_1.ActionType.TERMINATE_CONNECTION,
        ];
        return containmentActions.includes(this.eventData.actionType);
    }
    /**
     * Check if this is a recovery action rollback
     */
    isRecoveryRollback() {
        const recoveryActions = [
            action_type_enum_1.ActionType.RESTORE_BACKUP,
            action_type_enum_1.ActionType.REBUILD_SYSTEM,
            action_type_enum_1.ActionType.RESTORE_NETWORK,
            action_type_enum_1.ActionType.ENABLE_SERVICE,
            action_type_enum_1.ActionType.RESET_PASSWORD,
            action_type_enum_1.ActionType.REGENERATE_CERTIFICATE,
        ];
        return recoveryActions.includes(this.eventData.actionType);
    }
    /**
     * Check if this is a configuration change rollback
     */
    isConfigurationRollback() {
        const configActions = [
            action_type_enum_1.ActionType.UPDATE_FIREWALL,
            action_type_enum_1.ActionType.RECONFIGURE_SYSTEM,
            action_type_enum_1.ActionType.UPDATE_SOFTWARE,
            action_type_enum_1.ActionType.CONFIGURE_SEGMENTATION,
            action_type_enum_1.ActionType.UPDATE_IDS_RULES,
        ];
        return configActions.includes(this.eventData.actionType);
    }
    /**
     * Get rollback impact level
     */
    getRollbackImpact() {
        if (this.isSecurityCriticalRollback()) {
            return this.isContainmentRollback() ? 'critical' : 'high';
        }
        if (this.isContainmentRollback()) {
            return 'high'; // Rolling back containment may expose systems
        }
        if (this.isRecoveryRollback()) {
            return 'medium'; // May affect service availability
        }
        if (this.isConfigurationRollback()) {
            return 'medium';
        }
        return 'low';
    }
    /**
     * Get security implications of the rollback
     */
    getSecurityImplications() {
        const implications = [];
        if (this.isContainmentRollback()) {
            implications.push('Containment measures removed - systems may be exposed');
            implications.push('Threat may regain access to previously contained resources');
            implications.push('Increased risk exposure until alternative measures implemented');
        }
        if (this.eventData.actionType === action_type_enum_1.ActionType.BLOCK_IP) {
            implications.push('IP address unblocked - traffic from this IP now allowed');
            implications.push('Monitor for suspicious activity from unblocked IP');
        }
        if (this.eventData.actionType === action_type_enum_1.ActionType.DISABLE_ACCOUNT) {
            implications.push('User account re-enabled - access restored');
            implications.push('Monitor account activity for suspicious behavior');
        }
        if (this.eventData.actionType === action_type_enum_1.ActionType.QUARANTINE_FILE) {
            implications.push('File removed from quarantine - file now accessible');
            implications.push('Ensure file is safe before allowing access');
        }
        if (this.eventData.actionType === action_type_enum_1.ActionType.UPDATE_FIREWALL) {
            implications.push('Firewall rules reverted - network access patterns changed');
            implications.push('Review network traffic for unexpected patterns');
        }
        if (this.eventData.actionType === action_type_enum_1.ActionType.PATCH_VULNERABILITY) {
            implications.push('Vulnerability patch removed - system may be vulnerable again');
            implications.push('Implement alternative protection measures');
        }
        return implications;
    }
    /**
     * Get recommended post-rollback actions
     */
    getRecommendedPostActions() {
        const actions = [];
        // General post-rollback actions
        actions.push('Validate rollback completion');
        actions.push('Document rollback rationale');
        if (this.isSecurityCriticalRollback()) {
            actions.push('Assess security posture after rollback');
            actions.push('Implement alternative security measures if needed');
            actions.push('Monitor for security incidents');
        }
        if (this.isContainmentRollback()) {
            actions.push('Implement alternative containment measures');
            actions.push('Monitor for threat activity');
            actions.push('Assess threat landscape changes');
        }
        if (this.isRecoveryRollback()) {
            actions.push('Verify system functionality after rollback');
            actions.push('Check service availability');
            actions.push('Consider alternative recovery approaches');
        }
        if (this.isConfigurationRollback()) {
            actions.push('Validate configuration state');
            actions.push('Test system functionality');
            actions.push('Update configuration documentation');
        }
        // Action-specific recommendations
        switch (this.eventData.actionType) {
            case action_type_enum_1.ActionType.BLOCK_IP:
                actions.push('Monitor traffic from unblocked IP address');
                actions.push('Consider alternative blocking methods');
                break;
            case action_type_enum_1.ActionType.DISABLE_ACCOUNT:
                actions.push('Monitor re-enabled account activity');
                actions.push('Review account permissions');
                break;
            case action_type_enum_1.ActionType.QUARANTINE_FILE:
                actions.push('Re-scan file for threats');
                actions.push('Monitor file access patterns');
                break;
            case action_type_enum_1.ActionType.ISOLATE_SYSTEM:
                actions.push('Monitor system for suspicious activity');
                actions.push('Implement network monitoring');
                break;
        }
        return actions;
    }
    /**
     * Get stakeholders to notify
     */
    getNotificationTargets() {
        const targets = [];
        // Always notify the rollback requestor and original action requestor
        targets.push('rollback-requestor');
        targets.push('original-action-requestor');
        if (this.isSecurityCriticalRollback()) {
            targets.push('security-team');
            targets.push('incident-response-team');
            if (this.isContainmentRollback()) {
                targets.push('security-managers');
            }
        }
        if (this.isContainmentRollback()) {
            targets.push('containment-specialists');
            targets.push('threat-analysts');
        }
        if (this.isRecoveryRollback()) {
            targets.push('service-owners');
            targets.push('operations-team');
        }
        if (this.isConfigurationRollback()) {
            targets.push('system-administrators');
            targets.push('configuration-managers');
        }
        return targets;
    }
    /**
     * Get compliance considerations
     */
    getComplianceConsiderations() {
        const considerations = [];
        considerations.push('Document rollback decision and rationale');
        considerations.push('Maintain audit trail of rollback process');
        if (this.isSecurityCriticalRollback()) {
            considerations.push('Ensure rollback approval was properly obtained');
            considerations.push('Document security impact assessment');
        }
        if (this.isContainmentRollback()) {
            considerations.push('Document risk acceptance for containment removal');
            considerations.push('Ensure alternative controls are in place');
        }
        if (this.eventData.actionType === action_type_enum_1.ActionType.DELETE_FILE) {
            considerations.push('Ensure data recovery complies with retention policies');
            considerations.push('Document data restoration rationale');
        }
        return considerations;
    }
    /**
     * Get monitoring requirements
     */
    getMonitoringRequirements() {
        let duration = 'short';
        let intensity = 'basic';
        const focus = [];
        if (this.isSecurityCriticalRollback()) {
            duration = 'long';
            intensity = 'intensive';
            focus.push('security-events');
            focus.push('threat-indicators');
        }
        if (this.isContainmentRollback()) {
            duration = 'ongoing';
            intensity = 'intensive';
            focus.push('threat-activity');
            focus.push('network-traffic');
            focus.push('system-access');
        }
        if (this.isRecoveryRollback()) {
            duration = 'medium';
            intensity = 'enhanced';
            focus.push('system-performance');
            focus.push('service-availability');
        }
        if (this.isConfigurationRollback()) {
            duration = 'medium';
            intensity = 'enhanced';
            focus.push('configuration-drift');
            focus.push('system-behavior');
        }
        return { duration, intensity, focus };
    }
    /**
     * Get rollback metrics
     */
    getRollbackMetrics() {
        return {
            actionType: this.eventData.actionType,
            rollbackImpact: this.getRollbackImpact(),
            isSecurityCritical: this.isSecurityCriticalRollback(),
            isAutomated: this.isAutomatedRollback(),
            isSuccessful: this.isRollbackSuccessful(),
            securityImplicationsCount: this.getSecurityImplications().length,
        };
    }
    /**
     * Convert to integration event format
     */
    toIntegrationEvent() {
        const monitoring = this.getMonitoringRequirements();
        return {
            eventType: 'ResponseActionRolledBack',
            action: 'response_action_rolled_back',
            resource: 'ResponseAction',
            resourceId: this.aggregateId.toString(),
            data: this.eventData,
            metadata: {
                rollbackImpact: this.getRollbackImpact(),
                isSecurityCritical: this.isSecurityCriticalRollback(),
                isContainmentRollback: this.isContainmentRollback(),
                isSuccessful: this.isRollbackSuccessful(),
                requiresMonitoring: monitoring.intensity !== 'basic',
            },
        };
    }
}
exports.ResponseActionRolledBackDomainEvent = ResponseActionRolledBackDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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