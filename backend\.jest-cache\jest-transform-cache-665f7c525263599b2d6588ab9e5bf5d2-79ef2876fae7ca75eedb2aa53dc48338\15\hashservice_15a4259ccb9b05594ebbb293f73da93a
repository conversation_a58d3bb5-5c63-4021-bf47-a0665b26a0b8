2937a6fa8bc7941e1c213270772df81e
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.HashService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const crypto = __importStar(require("crypto"));
const bcrypt = __importStar(require("bcrypt"));
let HashService = class HashService {
    constructor(configService) {
        this.configService = configService;
        this.defaultAlgorithm = 'sha256';
        this.defaultEncoding = 'hex';
        this.defaultSaltRounds = 12;
    }
    /**
     * Hash data using specified algorithm
     */
    hash(data, options = {}) {
        const algorithm = options.algorithm || this.defaultAlgorithm;
        const encoding = options.encoding || this.defaultEncoding;
        const hash = crypto.createHash(algorithm);
        hash.update(data);
        return hash.digest(encoding);
    }
    /**
     * Hash data with SHA-256
     */
    sha256(data, encoding = this.defaultEncoding) {
        return this.hash(data, { algorithm: 'sha256', encoding });
    }
    /**
     * Hash data with SHA-512
     */
    sha512(data, encoding = this.defaultEncoding) {
        return this.hash(data, { algorithm: 'sha512', encoding });
    }
    /**
     * Hash data with SHA-1 (deprecated, use only for legacy compatibility)
     */
    sha1(data, encoding = this.defaultEncoding) {
        return this.hash(data, { algorithm: 'sha1', encoding });
    }
    /**
     * Hash data with MD5 (deprecated, use only for legacy compatibility)
     */
    md5(data, encoding = this.defaultEncoding) {
        return this.hash(data, { algorithm: 'md5', encoding });
    }
    /**
     * Create HMAC (Hash-based Message Authentication Code)
     */
    hmac(data, key, options = {}) {
        const algorithm = options.algorithm || this.defaultAlgorithm;
        const encoding = options.encoding || this.defaultEncoding;
        const hmac = crypto.createHmac(algorithm, key);
        hmac.update(data);
        return hmac.digest(encoding);
    }
    /**
     * Create HMAC-SHA256
     */
    hmacSha256(data, key) {
        const secretKey = key || this.configService.get('HMAC_SECRET_KEY');
        if (!secretKey) {
            throw new Error('HMAC secret key not found in configuration');
        }
        return this.hmac(data, secretKey, { algorithm: 'sha256' });
    }
    /**
     * Create HMAC-SHA512
     */
    hmacSha512(data, key) {
        const secretKey = key || this.configService.get('HMAC_SECRET_KEY');
        if (!secretKey) {
            throw new Error('HMAC secret key not found in configuration');
        }
        return this.hmac(data, secretKey, { algorithm: 'sha512' });
    }
    /**
     * Hash password using bcrypt
     */
    async hashPassword(password, options = {}) {
        const saltRounds = options.saltRounds || this.defaultSaltRounds;
        try {
            return await bcrypt.hash(password, saltRounds);
        }
        catch (error) {
            throw new Error(`Password hashing failed: ${error.message}`);
        }
    }
    /**
     * Verify password against bcrypt hash
     */
    async verifyPassword(password, hash) {
        try {
            return await bcrypt.compare(password, hash);
        }
        catch (error) {
            throw new Error(`Password verification failed: ${error.message}`);
        }
    }
    /**
     * Generate salt for password hashing
     */
    async generateSalt(rounds = this.defaultSaltRounds) {
        try {
            return await bcrypt.genSalt(rounds);
        }
        catch (error) {
            throw new Error(`Salt generation failed: ${error.message}`);
        }
    }
    /**
     * Hash data with PBKDF2 (Password-Based Key Derivation Function 2)
     */
    pbkdf2(password, salt, iterations = 100000, keyLength = 64, digest = 'sha256') {
        return new Promise((resolve, reject) => {
            crypto.pbkdf2(password, salt, iterations, keyLength, digest, (err, derivedKey) => {
                if (err) {
                    reject(new Error(`PBKDF2 hashing failed: ${err.message}`));
                }
                else {
                    resolve(derivedKey.toString('hex'));
                }
            });
        });
    }
    /**
     * Hash data with scrypt
     */
    scrypt(password, salt, keyLength = 64, options = {}) {
        return new Promise((resolve, reject) => {
            crypto.scrypt(password, salt, keyLength, options, (err, derivedKey) => {
                if (err) {
                    reject(new Error(`Scrypt hashing failed: ${err.message}`));
                }
                else {
                    resolve(derivedKey.toString('hex'));
                }
            });
        });
    }
    /**
     * Generate cryptographically secure random salt
     */
    generateRandomSalt(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }
    /**
     * Verify HMAC
     */
    verifyHmac(data, signature, key, algorithm = 'sha256') {
        const expectedSignature = this.hmac(data, key, { algorithm });
        // Use timing-safe comparison to prevent timing attacks
        return crypto.timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(expectedSignature, 'hex'));
    }
    /**
     * Hash file content
     */
    async hashFile(filePath, algorithm = 'sha256') {
        return new Promise((resolve, reject) => {
            const hash = crypto.createHash(algorithm);
            const fs = require('fs');
            const stream = fs.createReadStream(filePath);
            stream.on('data', (data) => {
                hash.update(data);
            });
            stream.on('end', () => {
                resolve(hash.digest('hex'));
            });
            stream.on('error', (error) => {
                reject(new Error(`File hashing failed: ${error.message}`));
            });
        });
    }
    /**
     * Generate checksum for data integrity verification
     */
    generateChecksum(data, algorithm = 'sha256') {
        return this.hash(data, { algorithm });
    }
    /**
     * Verify data integrity using checksum
     */
    verifyChecksum(data, expectedChecksum, algorithm = 'sha256') {
        const actualChecksum = this.generateChecksum(data, algorithm);
        // Use timing-safe comparison
        return crypto.timingSafeEqual(Buffer.from(actualChecksum, 'hex'), Buffer.from(expectedChecksum, 'hex'));
    }
};
exports.HashService = HashService;
exports.HashService = HashService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], HashService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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