5cb3a4db840dfa0334e116c3876b59a9
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var IOCService_1;
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IOCService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const ioc_entity_1 = require("../../domain/entities/ioc.entity");
const threat_feed_entity_1 = require("../../domain/entities/threat-feed.entity");
const threat_actor_entity_1 = require("../../domain/entities/threat-actor.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
/**
 * IOC service that handles Indicator of Compromise management operations
 * Provides CRUD operations and business logic for IOCs
 */
let IOCService = IOCService_1 = class IOCService {
    constructor(iocRepository, threatFeedRepository, threatActorRepository, loggerService, auditService) {
        this.iocRepository = iocRepository;
        this.threatFeedRepository = threatFeedRepository;
        this.threatActorRepository = threatActorRepository;
        this.loggerService = loggerService;
        this.auditService = auditService;
        this.logger = new common_1.Logger(IOCService_1.name);
    }
    /**
     * Create a new IOC
     * @param iocData IOC data
     * @param createdBy User creating the IOC
     * @returns Created IOC
     */
    async create(iocData, createdBy) {
        try {
            this.logger.debug('Creating new IOC', {
                type: iocData.type,
                value: this.sanitizeValue(iocData.value),
                createdBy,
            });
            // Validate required fields
            if (!iocData.type || !iocData.value) {
                throw new common_1.BadRequestException('Type and value are required');
            }
            // Check for duplicate IOC
            const existingIOC = await this.findByTypeAndValue(iocData.type, iocData.value);
            if (existingIOC) {
                // Update existing IOC instead of creating duplicate
                existingIOC.updateLastSeen();
                const updatedIOC = await this.iocRepository.save(existingIOC);
                await this.auditService.logUserAction(createdBy, 'update_existing', 'ioc', updatedIOC.id, { reason: 'duplicate_submission', observationCount: updatedIOC.observationCount });
                return updatedIOC;
            }
            // Set default values
            const now = new Date();
            const ioc = this.iocRepository.create({
                ...iocData,
                value: this.normalizeValue(iocData.type, iocData.value),
                firstSeen: iocData.firstSeen || now,
                lastSeen: iocData.lastSeen || now,
                confidence: iocData.confidence || 50,
                severity: iocData.severity || 'medium',
                status: iocData.status || 'active',
                tlp: iocData.tlp || 'white',
                isMonitored: iocData.isMonitored !== false,
                observationCount: 1,
                createdBy,
            });
            // Enrich IOC with additional data
            await this.enrichIOC(ioc);
            const savedIOC = await this.iocRepository.save(ioc);
            // Log audit event
            await this.auditService.logUserAction(createdBy, 'create', 'ioc', savedIOC.id, {
                type: savedIOC.type,
                value: this.sanitizeValue(savedIOC.value),
                severity: savedIOC.severity,
                confidence: savedIOC.confidence,
            });
            this.logger.log('IOC created successfully', {
                iocId: savedIOC.id,
                type: savedIOC.type,
                createdBy,
            });
            return savedIOC;
        }
        catch (error) {
            this.logger.error('Failed to create IOC', {
                error: error.message,
                type: iocData.type,
                createdBy,
            });
            throw error;
        }
    }
    /**
     * Find IOC by ID
     * @param id IOC ID
     * @returns IOC or null
     */
    async findById(id) {
        try {
            const ioc = await this.iocRepository.findOne({
                where: { id },
                relations: ['threatFeed', 'threatActors'],
            });
            if (!ioc) {
                this.logger.warn('IOC not found', { id });
                return null;
            }
            return ioc;
        }
        catch (error) {
            this.logger.error('Failed to find IOC by ID', {
                id,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Find IOC by type and value
     * @param type IOC type
     * @param value IOC value
     * @returns IOC or null
     */
    async findByTypeAndValue(type, value) {
        try {
            const normalizedValue = this.normalizeValue(type, value);
            return await this.iocRepository.findOne({
                where: { type, value: normalizedValue },
                relations: ['threatFeed', 'threatActors'],
            });
        }
        catch (error) {
            this.logger.error('Failed to find IOC by type and value', {
                type,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Find IOCs with pagination and filtering
     * @param options Query options
     * @returns Paginated IOCs
     */
    async findMany(options) {
        try {
            const { page = 1, limit = 20, type, severity, status, confidence, threatFeedId, search, sortBy = 'lastSeen', sortOrder = 'DESC', } = options;
            const queryBuilder = this.iocRepository.createQueryBuilder('ioc')
                .leftJoinAndSelect('ioc.threatFeed', 'threatFeed')
                .leftJoinAndSelect('ioc.threatActors', 'threatActors');
            // Apply filters
            if (type && type.length > 0) {
                queryBuilder.andWhere('ioc.type IN (:...type)', { type });
            }
            if (severity && severity.length > 0) {
                queryBuilder.andWhere('ioc.severity IN (:...severity)', { severity });
            }
            if (status && status.length > 0) {
                queryBuilder.andWhere('ioc.status IN (:...status)', { status });
            }
            if (confidence) {
                if (confidence.min !== undefined) {
                    queryBuilder.andWhere('ioc.confidence >= :minConfidence', { minConfidence: confidence.min });
                }
                if (confidence.max !== undefined) {
                    queryBuilder.andWhere('ioc.confidence <= :maxConfidence', { maxConfidence: confidence.max });
                }
            }
            if (threatFeedId) {
                queryBuilder.andWhere('ioc.threatFeedId = :threatFeedId', { threatFeedId });
            }
            if (search) {
                queryBuilder.andWhere('(ioc.value ILIKE :search OR ioc.description ILIKE :search)', { search: `%${search}%` });
            }
            // Apply sorting
            queryBuilder.orderBy(`ioc.${sortBy}`, sortOrder);
            // Apply pagination
            const offset = (page - 1) * limit;
            queryBuilder.skip(offset).take(limit);
            const [iocs, total] = await queryBuilder.getManyAndCount();
            const totalPages = Math.ceil(total / limit);
            this.logger.debug('IOCs retrieved', {
                total,
                page,
                limit,
                totalPages,
                filters: { type, severity, status, confidence, threatFeedId, search },
            });
            return {
                iocs,
                total,
                page,
                totalPages,
            };
        }
        catch (error) {
            this.logger.error('Failed to find IOCs', {
                error: error.message,
                options,
            });
            throw error;
        }
    }
    /**
     * Update IOC
     * @param id IOC ID
     * @param updateData Update data
     * @param updatedBy User performing the update
     * @returns Updated IOC
     */
    async update(id, updateData, updatedBy) {
        try {
            const ioc = await this.findById(id);
            if (!ioc) {
                throw new common_1.NotFoundException('IOC not found');
            }
            this.logger.debug('Updating IOC', {
                iocId: id,
                updateData: this.sanitizeUpdateData(updateData),
                updatedBy,
            });
            // Store original data for audit
            const originalData = {
                status: ioc.status,
                severity: ioc.severity,
                confidence: ioc.confidence,
                isMonitored: ioc.isMonitored,
            };
            // Update fields
            Object.assign(ioc, updateData);
            ioc.updatedBy = updatedBy;
            const updatedIOC = await this.iocRepository.save(ioc);
            // Log audit event
            await this.auditService.logUserAction(updatedBy, 'update', 'ioc', id, {
                originalData,
                updateData: this.sanitizeUpdateData(updateData),
            });
            this.logger.log('IOC updated successfully', {
                iocId: id,
                updatedBy,
            });
            return updatedIOC;
        }
        catch (error) {
            this.logger.error('Failed to update IOC', {
                iocId: id,
                error: error.message,
                updatedBy,
            });
            throw error;
        }
    }
    /**
     * Delete IOC
     * @param id IOC ID
     * @param deletedBy User performing the deletion
     */
    async delete(id, deletedBy) {
        try {
            const ioc = await this.findById(id);
            if (!ioc) {
                throw new common_1.NotFoundException('IOC not found');
            }
            this.logger.debug('Deleting IOC', {
                iocId: id,
                deletedBy,
            });
            await this.iocRepository.remove(ioc);
            // Log audit event
            await this.auditService.logUserAction(deletedBy, 'delete', 'ioc', id, {
                type: ioc.type,
                value: this.sanitizeValue(ioc.value),
                severity: ioc.severity,
            });
            this.logger.log('IOC deleted successfully', {
                iocId: id,
                deletedBy,
            });
        }
        catch (error) {
            this.logger.error('Failed to delete IOC', {
                iocId: id,
                error: error.message,
                deletedBy,
            });
            throw error;
        }
    }
    /**
     * Bulk create IOCs
     * @param iocsData Array of IOC data
     * @param createdBy User creating the IOCs
     * @returns Creation results
     */
    async bulkCreate(iocsData, createdBy) {
        try {
            this.logger.debug('Bulk creating IOCs', {
                count: iocsData.length,
                createdBy,
            });
            const results = {
                created: [],
                updated: [],
                errors: [],
            };
            for (const iocData of iocsData) {
                try {
                    const ioc = await this.create(iocData, createdBy);
                    if (ioc.observationCount > 1) {
                        results.updated.push(ioc);
                    }
                    else {
                        results.created.push(ioc);
                    }
                }
                catch (error) {
                    results.errors.push({
                        iocData: this.sanitizeIOCData(iocData),
                        error: error.message,
                    });
                }
            }
            // Log bulk operation
            await this.auditService.logUserAction(createdBy, 'bulk_create', 'ioc', null, {
                totalRequested: iocsData.length,
                created: results.created.length,
                updated: results.updated.length,
                errors: results.errors.length,
            });
            this.logger.log('Bulk IOC creation completed', {
                totalRequested: iocsData.length,
                created: results.created.length,
                updated: results.updated.length,
                errors: results.errors.length,
                createdBy,
            });
            return results;
        }
        catch (error) {
            this.logger.error('Failed to bulk create IOCs', {
                error: error.message,
                count: iocsData.length,
                createdBy,
            });
            throw error;
        }
    }
    /**
     * Associate IOC with threat actors
     * @param iocId IOC ID
     * @param threatActorIds Array of threat actor IDs
     * @param updatedBy User performing the association
     * @returns Updated IOC
     */
    async associateWithThreatActors(iocId, threatActorIds, updatedBy) {
        try {
            const ioc = await this.findById(iocId);
            if (!ioc) {
                throw new common_1.NotFoundException('IOC not found');
            }
            const threatActors = await this.threatActorRepository.findBy({
                id: (0, typeorm_2.In)(threatActorIds),
            });
            if (threatActors.length !== threatActorIds.length) {
                throw new common_1.BadRequestException('One or more threat actors not found');
            }
            ioc.threatActors = threatActors;
            ioc.updatedBy = updatedBy;
            const updatedIOC = await this.iocRepository.save(ioc);
            // Log audit event
            await this.auditService.logUserAction(updatedBy, 'associate_threat_actors', 'ioc', iocId, {
                threatActorIds,
                threatActorNames: threatActors.map(ta => ta.name),
            });
            this.logger.log('IOC associated with threat actors', {
                iocId,
                threatActorCount: threatActors.length,
                updatedBy,
            });
            return updatedIOC;
        }
        catch (error) {
            this.logger.error('Failed to associate IOC with threat actors', {
                iocId,
                threatActorIds,
                error: error.message,
                updatedBy,
            });
            throw error;
        }
    }
    /**
     * Get IOC statistics
     * @returns IOC statistics
     */
    async getStatistics() {
        try {
            const [total, active, inactive, expired, critical, high, medium, low, ipAddresses, domains, hashes, urls,] = await Promise.all([
                this.iocRepository.count(),
                this.iocRepository.count({ where: { status: 'active' } }),
                this.iocRepository.count({ where: { status: 'inactive' } }),
                this.iocRepository.count({ where: { status: 'expired' } }),
                this.iocRepository.count({ where: { severity: 'critical' } }),
                this.iocRepository.count({ where: { severity: 'high' } }),
                this.iocRepository.count({ where: { severity: 'medium' } }),
                this.iocRepository.count({ where: { severity: 'low' } }),
                this.iocRepository.count({ where: { type: 'ip_address' } }),
                this.iocRepository.count({ where: { type: 'domain' } }),
                this.iocRepository.count({ where: { type: (0, typeorm_2.In)(['file_hash_md5', 'file_hash_sha1', 'file_hash_sha256']) } }),
                this.iocRepository.count({ where: { type: 'url' } }),
            ]);
            return {
                total,
                byStatus: {
                    active,
                    inactive,
                    expired,
                },
                bySeverity: {
                    critical,
                    high,
                    medium,
                    low,
                },
                byType: {
                    ipAddresses,
                    domains,
                    hashes,
                    urls,
                    other: total - ipAddresses - domains - hashes - urls,
                },
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error('Failed to get IOC statistics', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Enrich IOC with additional data
     * @param ioc IOC to enrich
     */
    async enrichIOC(ioc) {
        try {
            // Add geolocation for IP addresses
            if (ioc.type === 'ip_address') {
                // This would typically call an external geolocation service
                // For now, this is a placeholder
                ioc.geolocation = {
                    country: 'Unknown',
                    region: 'Unknown',
                    city: 'Unknown',
                };
            }
            // Add context based on IOC type
            if (ioc.type === 'domain') {
                // This would typically perform domain analysis
                // For now, this is a placeholder
            }
        }
        catch (error) {
            this.logger.warn('Failed to enrich IOC', {
                iocId: ioc.id,
                error: error.message,
            });
        }
    }
    /**
     * Normalize IOC value based on type
     * @param type IOC type
     * @param value IOC value
     * @returns Normalized value
     */
    normalizeValue(type, value) {
        switch (type) {
            case 'domain':
            case 'email':
                return value.toLowerCase().trim();
            case 'file_hash_md5':
            case 'file_hash_sha1':
            case 'file_hash_sha256':
                return value.toLowerCase().trim();
            case 'ip_address':
                return value.trim();
            case 'url':
                return value.trim();
            default:
                return value.trim();
        }
    }
    /**
     * Sanitize IOC value for logging
     * @param value IOC value
     * @returns Sanitized value
     */
    sanitizeValue(value) {
        if (!value)
            return '[EMPTY]';
        if (value.length > 50) {
            return value.substring(0, 47) + '...';
        }
        return value;
    }
    /**
     * Sanitize update data for logging
     * @param data Update data
     * @returns Sanitized data
     */
    sanitizeUpdateData(data) {
        const sanitized = { ...data };
        if (sanitized.value) {
            sanitized.value = this.sanitizeValue(sanitized.value);
        }
        return sanitized;
    }
    /**
     * Sanitize IOC data for logging
     * @param data IOC data
     * @returns Sanitized data
     */
    sanitizeIOCData(data) {
        const sanitized = { ...data };
        if (sanitized.value) {
            sanitized.value = this.sanitizeValue(sanitized.value);
        }
        delete sanitized.enrichmentData;
        delete sanitized.metadata;
        return sanitized;
    }
};
exports.IOCService = IOCService;
exports.IOCService = IOCService = IOCService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ioc_entity_1.IOC)),
    __param(1, (0, typeorm_1.InjectRepository)(threat_feed_entity_1.ThreatFeed)),
    __param(2, (0, typeorm_1.InjectRepository)(threat_actor_entity_1.ThreatActor)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _d : Object, typeof (_e = typeof audit_service_1.AuditService !== "undefined" && audit_service_1.AuditService) === "function" ? _e : Object])
], IOCService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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