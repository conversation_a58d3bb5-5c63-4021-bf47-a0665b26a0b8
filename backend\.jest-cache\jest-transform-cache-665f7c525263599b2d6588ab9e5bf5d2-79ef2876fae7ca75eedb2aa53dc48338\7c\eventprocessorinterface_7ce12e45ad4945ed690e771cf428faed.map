{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\services\\event-processor.interface.ts", "mappings": ";;;AA6EA;;GAEG;AACH,IAAY,gBASX;AATD,WAAY,gBAAgB;IAC1B,iCAAa,CAAA;IACb,6CAAyB,CAAA;IACzB,mCAAe,CAAA;IACf,qDAAiC,CAAA;IACjC,6CAAyB,CAAA;IACzB,iDAA6B,CAAA;IAC7B,mDAA+B,CAAA;IAC/B,qCAAiB,CAAA;AACnB,CAAC,EATW,gBAAgB,gCAAhB,gBAAgB,QAS3B;AAsDD;;GAEG;AACH,IAAY,eAOX;AAPD,WAAY,eAAe;IACzB,4CAAyB,CAAA;IACzB,kDAA+B,CAAA;IAC/B,4CAAyB,CAAA;IACzB,8CAA2B,CAAA;IAC3B,sDAAmC,CAAA;IACnC,8DAA2C,CAAA;AAC7C,CAAC,EAPW,eAAe,+BAAf,eAAe,QAO1B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\services\\event-processor.interface.ts"], "sourcesContent": ["import { Event } from '../../entities/event.entity';\r\nimport { NormalizedEvent } from '../../entities/normalized-event.entity';\r\nimport { EnrichedEvent } from '../../entities/enriched-event.entity';\r\nimport { CorrelatedEvent } from '../../entities/correlated-event.entity';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\n\r\n/**\r\n * Processing Pipeline Configuration\r\n */\r\nexport interface ProcessingPipelineConfig {\r\n  /** Enable normalization stage */\r\n  enableNormalization?: boolean;\r\n  /** Enable enrichment stage */\r\n  enableEnrichment?: boolean;\r\n  /** Enable correlation stage */\r\n  enableCorrelation?: boolean;\r\n  /** Enable threat analysis stage */\r\n  enableThreatAnalysis?: boolean;\r\n  /** Processing timeout in milliseconds */\r\n  timeoutMs?: number;\r\n  /** Maximum retry attempts */\r\n  maxRetries?: number;\r\n  /** Batch processing size */\r\n  batchSize?: number;\r\n  /** Priority processing for high-severity events */\r\n  prioritizeHighSeverity?: boolean;\r\n  /** Skip processing for certain event types */\r\n  skipEventTypes?: EventType[];\r\n  /** Custom processing rules */\r\n  customRules?: ProcessingRule[];\r\n}\r\n\r\n/**\r\n * Processing Rule Interface\r\n */\r\nexport interface ProcessingRule {\r\n  /** Rule identifier */\r\n  id: string;\r\n  /** Rule name */\r\n  name: string;\r\n  /** Rule description */\r\n  description: string;\r\n  /** Rule condition */\r\n  condition: ProcessingCondition;\r\n  /** Action to take when condition is met */\r\n  action: ProcessingAction;\r\n  /** Rule priority */\r\n  priority: number;\r\n  /** Whether rule is enabled */\r\n  enabled: boolean;\r\n}\r\n\r\n/**\r\n * Processing Condition Interface\r\n */\r\nexport interface ProcessingCondition {\r\n  /** Event type conditions */\r\n  eventTypes?: EventType[];\r\n  /** Severity level conditions */\r\n  severityLevels?: EventSeverity[];\r\n  /** Source conditions */\r\n  sources?: string[];\r\n  /** Custom field conditions */\r\n  customFields?: Record<string, any>;\r\n  /** Time-based conditions */\r\n  timeConditions?: {\r\n    withinLast?: number; // milliseconds\r\n    olderThan?: number; // milliseconds\r\n    timeRange?: {\r\n      start: Date;\r\n      end: Date;\r\n    };\r\n  };\r\n}\r\n\r\n/**\r\n * Processing Action Enum\r\n */\r\nexport enum ProcessingAction {\r\n  SKIP = 'SKIP',\r\n  PRIORITIZE = 'PRIORITIZE',\r\n  DELAY = 'DELAY',\r\n  ROUTE_TO_QUEUE = 'ROUTE_TO_QUEUE',\r\n  APPLY_TAGS = 'APPLY_TAGS',\r\n  SET_SEVERITY = 'SET_SEVERITY',\r\n  TRIGGER_ALERT = 'TRIGGER_ALERT',\r\n  CUSTOM = 'CUSTOM',\r\n}\r\n\r\n/**\r\n * Processing Context Interface\r\n */\r\nexport interface ProcessingContext {\r\n  /** Processing request ID */\r\n  requestId: string;\r\n  /** Processing start time */\r\n  startTime: Date;\r\n  /** Processing configuration */\r\n  config: ProcessingPipelineConfig;\r\n  /** User context */\r\n  userContext?: {\r\n    userId: string;\r\n    tenantId: string;\r\n    permissions: string[];\r\n  };\r\n  /** Processing metadata */\r\n  metadata?: Record<string, any>;\r\n  /** Correlation ID for tracking */\r\n  correlationId?: string;\r\n}\r\n\r\n/**\r\n * Processing Result Interface\r\n */\r\nexport interface ProcessingResult {\r\n  /** Whether processing was successful */\r\n  success: boolean;\r\n  /** Final processing status */\r\n  finalStatus: EventProcessingStatus;\r\n  /** Processing stages completed */\r\n  stagesCompleted: ProcessingStage[];\r\n  /** Processing stages failed */\r\n  stagesFailed: ProcessingStage[];\r\n  /** Processing duration in milliseconds */\r\n  processingDurationMs: number;\r\n  /** Processing errors */\r\n  errors: ProcessingError[];\r\n  /** Processing warnings */\r\n  warnings: string[];\r\n  /** Events created during processing */\r\n  eventsCreated: {\r\n    normalized?: string; // Event ID\r\n    enriched?: string; // Event ID\r\n    correlated?: string; // Event ID\r\n  };\r\n  /** Processing metrics */\r\n  metrics: ProcessingMetrics;\r\n  /** Additional metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Processing Stage Enum\r\n */\r\nexport enum ProcessingStage {\r\n  VALIDATION = 'VALIDATION',\r\n  NORMALIZATION = 'NORMALIZATION',\r\n  ENRICHMENT = 'ENRICHMENT',\r\n  CORRELATION = 'CORRELATION',\r\n  THREAT_ANALYSIS = 'THREAT_ANALYSIS',\r\n  RESPONSE_GENERATION = 'RESPONSE_GENERATION',\r\n}\r\n\r\n/**\r\n * Processing Error Interface\r\n */\r\nexport interface ProcessingError {\r\n  /** Error stage */\r\n  stage: ProcessingStage;\r\n  /** Error code */\r\n  code: string;\r\n  /** Error message */\r\n  message: string;\r\n  /** Error details */\r\n  details?: Record<string, any>;\r\n  /** Error timestamp */\r\n  timestamp: Date;\r\n  /** Whether error is recoverable */\r\n  recoverable: boolean;\r\n}\r\n\r\n/**\r\n * Processing Metrics Interface\r\n */\r\nexport interface ProcessingMetrics {\r\n  /** Total events processed */\r\n  eventsProcessed: number;\r\n  /** Events processed successfully */\r\n  eventsSuccessful: number;\r\n  /** Events failed */\r\n  eventsFailed: number;\r\n  /** Events skipped */\r\n  eventsSkipped: number;\r\n  /** Average processing time per event */\r\n  avgProcessingTimeMs: number;\r\n  /** Peak processing time */\r\n  peakProcessingTimeMs: number;\r\n  /** Memory usage during processing */\r\n  memoryUsageMB?: number;\r\n  /** CPU usage during processing */\r\n  cpuUsagePercent?: number;\r\n  /** Stage-specific metrics */\r\n  stageMetrics: Record<ProcessingStage, {\r\n    duration: number;\r\n    success: boolean;\r\n    attempts: number;\r\n  }>;\r\n}\r\n\r\n/**\r\n * Batch Processing Options\r\n */\r\nexport interface BatchProcessingOptions {\r\n  /** Batch size */\r\n  batchSize: number;\r\n  /** Maximum concurrent batches */\r\n  maxConcurrentBatches?: number;\r\n  /** Batch timeout in milliseconds */\r\n  batchTimeoutMs?: number;\r\n  /** Whether to fail entire batch on single failure */\r\n  failOnSingleError?: boolean;\r\n  /** Batch processing priority */\r\n  priority?: 'low' | 'normal' | 'high';\r\n}\r\n\r\n/**\r\n * Event Processing Status Update\r\n */\r\nexport interface ProcessingStatusUpdate {\r\n  /** Event ID */\r\n  eventId: string;\r\n  /** Old status */\r\n  oldStatus: EventProcessingStatus;\r\n  /** New status */\r\n  newStatus: EventProcessingStatus;\r\n  /** Update timestamp */\r\n  timestamp: Date;\r\n  /** Update reason */\r\n  reason?: string;\r\n  /** Additional metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Event Processor Interface\r\n * \r\n * Defines the contract for processing security events through the complete pipeline.\r\n * Handles normalization, enrichment, correlation, and threat analysis stages.\r\n */\r\nexport interface EventProcessor {\r\n  /**\r\n   * Process a single event through the complete pipeline\r\n   * @param event - The event to process\r\n   * @param context - Processing context and configuration\r\n   * @returns Processing result with status and created events\r\n   */\r\n  processEvent(event: Event, context: ProcessingContext): Promise<ProcessingResult>;\r\n\r\n  /**\r\n   * Process multiple events in batch\r\n   * @param events - Array of events to process\r\n   * @param context - Processing context and configuration\r\n   * @param options - Batch processing options\r\n   * @returns Array of processing results\r\n   */\r\n  processBatch(\r\n    events: Event[], \r\n    context: ProcessingContext, \r\n    options?: BatchProcessingOptions\r\n  ): Promise<ProcessingResult[]>;\r\n\r\n  /**\r\n   * Process event through specific stage only\r\n   * @param event - The event to process\r\n   * @param stage - Specific processing stage\r\n   * @param context - Processing context\r\n   * @returns Processing result for the stage\r\n   */\r\n  processStage(\r\n    event: Event, \r\n    stage: ProcessingStage, \r\n    context: ProcessingContext\r\n  ): Promise<ProcessingResult>;\r\n\r\n  /**\r\n   * Normalize raw event data to standard format\r\n   * @param event - Raw event to normalize\r\n   * @param context - Processing context\r\n   * @returns Normalized event\r\n   */\r\n  normalizeEvent(event: Event, context: ProcessingContext): Promise<NormalizedEvent>;\r\n\r\n  /**\r\n   * Enrich normalized event with additional context\r\n   * @param normalizedEvent - Normalized event to enrich\r\n   * @param context - Processing context\r\n   * @returns Enriched event\r\n   */\r\n  enrichEvent(normalizedEvent: NormalizedEvent, context: ProcessingContext): Promise<EnrichedEvent>;\r\n\r\n  /**\r\n   * Correlate enriched event with other events\r\n   * @param enrichedEvent - Enriched event to correlate\r\n   * @param context - Processing context\r\n   * @returns Correlated event\r\n   */\r\n  correlateEvent(enrichedEvent: EnrichedEvent, context: ProcessingContext): Promise<CorrelatedEvent>;\r\n\r\n  /**\r\n   * Validate event data and structure\r\n   * @param event - Event to validate\r\n   * @param context - Processing context\r\n   * @returns Validation result\r\n   */\r\n  validateEvent(event: Event, context: ProcessingContext): Promise<{\r\n    isValid: boolean;\r\n    errors: string[];\r\n    warnings: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Apply processing rules to event\r\n   * @param event - Event to apply rules to\r\n   * @param rules - Processing rules to apply\r\n   * @param context - Processing context\r\n   * @returns Rule application result\r\n   */\r\n  applyProcessingRules(\r\n    event: Event, \r\n    rules: ProcessingRule[], \r\n    context: ProcessingContext\r\n  ): Promise<{\r\n    appliedRules: string[];\r\n    actions: ProcessingAction[];\r\n    modifications: Record<string, any>;\r\n  }>;\r\n\r\n  /**\r\n   * Get processing status for an event\r\n   * @param eventId - Event ID to check\r\n   * @returns Current processing status\r\n   */\r\n  getProcessingStatus(eventId: string): Promise<EventProcessingStatus>;\r\n\r\n  /**\r\n   * Update processing status for an event\r\n   * @param eventId - Event ID to update\r\n   * @param status - New processing status\r\n   * @param reason - Reason for status change\r\n   * @returns Status update confirmation\r\n   */\r\n  updateProcessingStatus(\r\n    eventId: string, \r\n    status: EventProcessingStatus, \r\n    reason?: string\r\n  ): Promise<ProcessingStatusUpdate>;\r\n\r\n  /**\r\n   * Retry failed event processing\r\n   * @param eventId - Event ID to retry\r\n   * @param context - Processing context\r\n   * @returns Retry processing result\r\n   */\r\n  retryProcessing(eventId: string, context: ProcessingContext): Promise<ProcessingResult>;\r\n\r\n  /**\r\n   * Cancel ongoing event processing\r\n   * @param eventId - Event ID to cancel\r\n   * @param reason - Cancellation reason\r\n   * @returns Cancellation confirmation\r\n   */\r\n  cancelProcessing(eventId: string, reason?: string): Promise<{\r\n    cancelled: boolean;\r\n    reason: string;\r\n    timestamp: Date;\r\n  }>;\r\n\r\n  /**\r\n   * Get processing pipeline health status\r\n   * @returns Pipeline health information\r\n   */\r\n  getPipelineHealth(): Promise<{\r\n    status: 'healthy' | 'degraded' | 'unhealthy';\r\n    stages: Record<ProcessingStage, {\r\n      status: 'healthy' | 'degraded' | 'unhealthy';\r\n      latency: number;\r\n      errorRate: number;\r\n      throughput: number;\r\n    }>;\r\n    metrics: ProcessingMetrics;\r\n    lastHealthCheck: Date;\r\n  }>;\r\n\r\n  /**\r\n   * Get processing pipeline configuration\r\n   * @returns Current pipeline configuration\r\n   */\r\n  getPipelineConfig(): Promise<ProcessingPipelineConfig>;\r\n\r\n  /**\r\n   * Update processing pipeline configuration\r\n   * @param config - New pipeline configuration\r\n   * @returns Configuration update confirmation\r\n   */\r\n  updatePipelineConfig(config: Partial<ProcessingPipelineConfig>): Promise<{\r\n    updated: boolean;\r\n    config: ProcessingPipelineConfig;\r\n    timestamp: Date;\r\n  }>;\r\n\r\n  /**\r\n   * Get processing metrics for a time period\r\n   * @param startTime - Start time for metrics\r\n   * @param endTime - End time for metrics\r\n   * @returns Processing metrics\r\n   */\r\n  getProcessingMetrics(startTime: Date, endTime: Date): Promise<ProcessingMetrics>;\r\n\r\n  /**\r\n   * Pause event processing\r\n   * @param reason - Reason for pausing\r\n   * @returns Pause confirmation\r\n   */\r\n  pauseProcessing(reason?: string): Promise<{\r\n    paused: boolean;\r\n    reason: string;\r\n    timestamp: Date;\r\n  }>;\r\n\r\n  /**\r\n   * Resume event processing\r\n   * @param reason - Reason for resuming\r\n   * @returns Resume confirmation\r\n   */\r\n  resumeProcessing(reason?: string): Promise<{\r\n    resumed: boolean;\r\n    reason: string;\r\n    timestamp: Date;\r\n  }>;\r\n}"], "version": 3}