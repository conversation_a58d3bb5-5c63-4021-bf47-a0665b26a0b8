8660df3dddbe2cfc589978be5f7bd6dd
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggingModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const nest_winston_1 = require("nest-winston");
const winston = __importStar(require("winston"));
const logger_service_1 = require("./logger.service");
const audit_service_1 = require("./audit/audit.service");
const security_audit_service_1 = require("./audit/security-audit.service");
const correlation_id_service_1 = require("./correlation-id.service");
const transport_manager_service_1 = require("./transport-manager.service");
const logging_config_1 = require("../config/logging.config");
/**
 * Logging module that provides structured logging with Winston
 * Supports multiple transports, audit logging, and correlation tracking
 *
 * Features:
 * - Structured JSON logging
 * - Multiple log levels and transports
 * - Audit logging for security events
 * - Correlation ID tracking
 * - Log rotation and archiving
 * - Environment-specific configuration
 *
 * @module LoggingModule
 */
let LoggingModule = class LoggingModule {
};
exports.LoggingModule = LoggingModule;
exports.LoggingModule = LoggingModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            nest_winston_1.WinstonModule.forRootAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: async (configService) => {
                    const loggingConfig = configService.get('logging');
                    // Create transport manager instance for configuration
                    const transportManager = new transport_manager_service_1.TransportManagerService(configService);
                    await transportManager.onModuleInit();
                    // Get transports from transport manager (includes new formatters and transports)
                    const managedTransports = transportManager.getWinstonTransports();
                    // Legacy console transport for backward compatibility
                    const consoleTransports = [];
                    if (loggingConfig.console.enabled) {
                        consoleTransports.push(new winston.transports.Console({
                            level: loggingConfig.console.level,
                            format: winston.format.combine(winston.format.timestamp(), winston.format.colorize({ all: loggingConfig.console.colorize }), winston.format.printf(({ timestamp, level, message, context, correlationId, ...meta }) => {
                                const contextStr = context ? `[${context}] ` : '';
                                const correlationStr = correlationId ? `[${correlationId}] ` : '';
                                const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
                                return `${timestamp} ${level}: ${correlationStr}${contextStr}${message}${metaStr}`;
                            })),
                        }));
                    }
                    // Combine managed transports with console transport
                    const allTransports = [...consoleTransports, ...managedTransports];
                    return {
                        level: loggingConfig.level,
                        format: (0, logging_config_1.createWinstonFormat)(loggingConfig),
                        transports: allTransports,
                        silent: loggingConfig.silent,
                        exitOnError: false,
                        handleExceptions: true,
                        handleRejections: true,
                    };
                },
            }),
        ],
        providers: [
            transport_manager_service_1.TransportManagerService,
            correlation_id_service_1.CorrelationIdService,
            logger_service_1.LoggerService,
            audit_service_1.AuditService,
            security_audit_service_1.SecurityAuditService,
        ],
        exports: [
            transport_manager_service_1.TransportManagerService,
            correlation_id_service_1.CorrelationIdService,
            logger_service_1.LoggerService,
            audit_service_1.AuditService,
            security_audit_service_1.SecurityAuditService,
            nest_winston_1.WinstonModule,
        ],
    })
], LoggingModule);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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