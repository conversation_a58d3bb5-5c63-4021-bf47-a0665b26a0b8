{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai-ml\\application\\services\\vulnerability-analysis.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAoE;AAGpE,qFAAgF;AAChF,mFAAwE;AACxE,+EAAqE;AACrE,iGAAsF;AACtF,2GAA8F;AAC9F,sFAAkF;AAClF,0FAAsF;AAEtF,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;IAC5C,IAAI,OAAqC,CAAC;IAC1C,IAAI,qBAA8C,CAAC;IACnD,IAAI,oBAA4C,CAAC;IACjD,IAAI,qBAAqD,CAAC;IAC1D,IAAI,aAAoB,CAAC;IACzB,IAAI,iBAAoC,CAAC;IACzC,IAAI,aAA4B,CAAC;IACjC,IAAI,YAA0B,CAAC;IAE/B,MAAM,yBAAyB,GAAG;QAChC,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;KAClB,CAAC;IAEF,MAAM,wBAAwB,GAAG;QAC/B,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;KAChB,CAAC;IAEF,MAAM,yBAAyB,GAAG;QAChC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;KACnB,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;KACf,CAAC;IAEF,MAAM,qBAAqB,GAAG;QAC5B,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;KACxB,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;QACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACjB,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB,CAAC;IAEF,MAAM,sBAAsB,GAAgC;QAC1D,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,0BAA0B;QAChC,WAAW,EAAE,4BAA4B;QACzC,IAAI,EAAE,uBAAuB;QAC7B,QAAQ,EAAE,QAAQ;QAClB,OAAO,EAAE,oBAAoB;QAC7B,MAAM,EAAE,QAAQ;QAChB,SAAS,EAAE,IAAI;KAChB,CAAC;IAEF,MAAM,eAAe,GAAyB;QAC5C,EAAE,EAAE,SAAS;QACb,IAAI,EAAE,wBAAwB;QAC9B,KAAK,EAAE,kDAAkD;QACzD,MAAM,EAAE,SAAS;QACjB,QAAQ,EAAE,MAAM;QAChB,QAAQ,EAAE,CAAC;QACX,SAAS,EAAE,UAAU;QACrB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;QAC1B,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;QACvB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC5B,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,6DAA4B;gBAC5B;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,iCAAW,CAAC;oBACxC,QAAQ,EAAE,yBAAyB;iBACpC;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,8BAAU,CAAC;oBACvC,QAAQ,EAAE,wBAAwB;iBACnC;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,+CAAkB,CAAC;oBAC/C,QAAQ,EAAE,yBAAyB;iBACpC;gBACD;oBACE,OAAO,EAAE,IAAA,uBAAa,EAAC,aAAa,CAAC;oBACrC,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD;oBACE,OAAO,EAAE,+CAAiB;oBAC1B,QAAQ,EAAE,qBAAqB;iBAChC;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE,gBAAgB;iBAC3B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAA+B,6DAA4B,CAAC,CAAC;QACjF,qBAAqB,GAAG,MAAM,CAAC,GAAG,CAA0B,IAAA,4BAAkB,EAAC,iCAAW,CAAC,CAAC,CAAC;QAC7F,oBAAoB,GAAG,MAAM,CAAC,GAAG,CAAyB,IAAA,4BAAkB,EAAC,8BAAU,CAAC,CAAC,CAAC;QAC1F,qBAAqB,GAAG,MAAM,CAAC,GAAG,CAAiC,IAAA,4BAAkB,EAAC,+CAAkB,CAAC,CAAC,CAAC;QAC3G,aAAa,GAAG,MAAM,CAAC,GAAG,CAAQ,IAAA,uBAAa,EAAC,aAAa,CAAC,CAAC,CAAC;QAChE,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAoB,+CAAiB,CAAC,CAAC;QACrE,aAAa,GAAG,MAAM,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;QACzD,YAAY,GAAG,MAAM,CAAC,GAAG,CAAe,4BAAY,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,gEAAgE,EAAE,KAAK,IAAI,EAAE;YAC9E,MAAM,iBAAiB,GAAG;gBACxB,KAAK,EAAE,gBAAgB;gBACvB,WAAW,EAAE,oBAAoB;gBACjC,SAAS,EAAE,GAAG;gBACd,gBAAgB,EAAE,eAAe;gBACjC,gBAAgB,EAAE,IAAI;gBACtB,cAAc,EAAE,KAAK;aACtB,CAAC;YACF,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YAC5E,yBAAyB,CAAC,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAClE,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAClE,iBAAiB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;YAEjE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAExE,MAAM,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC7D,KAAK,EAAE;oBACL,IAAI,EAAE,uBAAuB;oBAC7B,MAAM,EAAE,QAAQ;oBAChB,SAAS,EAAE,IAAI;iBAChB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAC3D,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,kDAAkD;gBACzD,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,MAAM,EAAE,+CAA+C;gBACjE,SAAS,EAAE,iBAAiB;gBAC5B,oBAAoB,EAAE,sBAAsB,CAAC,EAAE;gBAC/C,SAAS,EAAE,MAAM;aAClB,CAAC,CACH,CAAC;YAEF,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAE7E,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAChD,gCAAgC,EAChC;gBACE,KAAK,EAAE,eAAe,CAAC,EAAE;gBACzB,iBAAiB;gBACjB,OAAO,EAAE,sBAAsB,CAAC,EAAE;aACnC,EACD,MAAM,CAAC,gBAAgB,CAAC;gBACtB,QAAQ,EAAE,CAAC,EAAE,gBAAgB;gBAC7B,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,aAAa;aACvB,CAAC,CACH,CAAC;YAEF,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,cAAc,EACd,eAAe,CAAC,EAAE,EAClB,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,iBAAiB,CAAC,KAAK;gBAC9B,OAAO,EAAE,sBAAsB,CAAC,EAAE;aACnC,CAAC,CACH,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,iBAAiB,GAAG;gBACxB,WAAW,EAAE,oBAAoB;gBACjC,SAAS,EAAE,GAAG;aACf,CAAC;YACF,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE1D,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;iBAC7D,OAAO,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,KAAK,IAAI,EAAE;YAC7E,MAAM,SAAS,GAAG;gBAChB;oBACE,IAAI,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,gBAAgB,EAAE,IAAI,EAAE;oBAChD,gBAAgB,EAAE,QAAQ;iBAC3B;gBACD;oBACE,IAAI,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,gBAAgB,EAAE,KAAK,EAAE;oBACjD,gBAAgB,EAAE,MAAM;iBACzB;gBACD;oBACE,IAAI,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,gBAAgB,EAAE,KAAK,EAAE;oBACjD,gBAAgB,EAAE,QAAQ;iBAC3B;gBACD;oBACE,IAAI,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,gBAAgB,EAAE,KAAK,EAAE;oBACjD,gBAAgB,EAAE,KAAK;iBACxB;aACF,CAAC;YAEF,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YAC5E,yBAAyB,CAAC,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAClE,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAClE,iBAAiB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;YAEjE,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,MAAM,iBAAiB,GAAG;oBACxB,WAAW,EAAE,oBAAoB;oBACjC,GAAG,QAAQ,CAAC,IAAI;iBACjB,CAAC;gBAEF,MAAM,OAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;gBAE7D,MAAM,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAC3D,MAAM,CAAC,gBAAgB,CAAC;oBACtB,QAAQ,EAAE,QAAQ,CAAC,gBAAgB;iBACpC,CAAC,CACH,CAAC;gBAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;gBAC5E,yBAAyB,CAAC,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;gBAClE,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;gBAClE,iBAAiB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;YACnE,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;YAC3E,MAAM,iBAAiB,GAAG;gBACxB,KAAK,EAAE,gBAAgB;gBACvB,WAAW,EAAE,oBAAoB;gBACjC,SAAS,EAAE,GAAG;gBACd,YAAY,EAAE,SAAS;gBACvB,gBAAgB,EAAE,KAAK;gBACvB,kBAAkB,EAAE,MAAM;gBAC1B,eAAe,EAAE,MAAM;gBACvB,cAAc,EAAE,IAAI;gBACpB,SAAS,EAAE,EAAE;aACd,CAAC;YACF,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC;gBAClD,GAAG,sBAAsB;gBACzB,IAAI,EAAE,mBAAmB;aAC1B,CAAC,CAAC;YACH,yBAAyB,CAAC,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAClE,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAClE,iBAAiB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;YAEjE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAElF,MAAM,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC7D,KAAK,EAAE;oBACL,IAAI,EAAE,mBAAmB;oBACzB,MAAM,EAAE,QAAQ;oBAChB,SAAS,EAAE,IAAI;iBAChB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAC3D,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,+CAA+C;gBACtD,SAAS,EAAE,iBAAiB;aAC7B,CAAC,CACH,CAAC;YAEF,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAChD,6BAA6B,EAC7B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,eAAe,CAAC,EAAE;gBACzB,iBAAiB;aAClB,CAAC,EACF,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;YAC1E,MAAM,iBAAiB,GAAG;gBACxB,KAAK,EAAE,gBAAgB;gBACvB,WAAW,EAAE,oBAAoB;gBACjC,gBAAgB,EAAE,eAAe;gBACjC,OAAO,EAAE,OAAO;gBAChB,QAAQ,EAAE,MAAM;gBAChB,gBAAgB,EAAE,KAAK;gBACvB,cAAc,EAAE,IAAI;aACrB,CAAC;YAEF,MAAM,kBAAkB,GAAG;gBACzB,eAAe,EAAE,cAAc;gBAC/B,YAAY,EAAE,QAAQ;gBACtB,mBAAmB,EAAE,MAAM;gBAC3B,iBAAiB,EAAE,0BAA0B;gBAC7C,sBAAsB,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC;aAC7C,CAAC;YAEF,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC;gBAClD,GAAG,sBAAsB;gBACzB,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;YACH,yBAAyB,CAAC,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAClE,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAClE,iBAAiB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;YAEjE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kCAAkC,CAC7D,iBAAiB,EACjB,kBAAkB,EAClB,MAAM,CACP,CAAC;YAEF,MAAM,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC7D,KAAK,EAAE;oBACL,IAAI,EAAE,eAAe;oBACrB,MAAM,EAAE,QAAQ;oBAChB,SAAS,EAAE,IAAI;iBAChB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAC3D,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,8CAA8C;gBACrD,SAAS,EAAE;oBACT,aAAa,EAAE,iBAAiB;oBAChC,WAAW,EAAE,kBAAkB;iBAChC;aACF,CAAC,CACH,CAAC;YAEF,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAChD,sCAAsC,EACtC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,eAAe,CAAC,EAAE;gBACzB,iBAAiB;gBACjB,kBAAkB;aACnB,CAAC,EACF,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,eAAe,GAAG;gBACtB;oBACE,EAAE,EAAE,QAAQ;oBACZ,KAAK,EAAE,gBAAgB;oBACvB,WAAW,EAAE,sBAAsB;oBACnC,SAAS,EAAE,GAAG;iBACf;gBACD;oBACE,EAAE,EAAE,QAAQ;oBACZ,KAAK,EAAE,gBAAgB;oBACvB,WAAW,EAAE,sBAAsB;oBACnC,SAAS,EAAE,GAAG;iBACf;aACF,CAAC;YACF,MAAM,YAAY,GAAG,UAAU,CAAC;YAChC,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YAC5E,yBAAyB,CAAC,MAAM,CAAC,eAAe,CAAC;gBAC/C,GAAG,eAAe;gBAClB,WAAW,EAAE,CAAC;aACf,CAAC,CAAC;YACH,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAClE,iBAAiB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;YAEjE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,eAAe,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;YAEjF,MAAM,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAC3D,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,6CAA6C;gBACpD,SAAS,EAAE;oBACT,eAAe;oBACf,YAAY;oBACZ,SAAS,EAAE,CAAC;iBACb;gBACD,WAAW,EAAE,CAAC;aACf,CAAC,CACH,CAAC;YAEF,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAChD,8BAA8B,EAC9B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,eAAe,CAAC,EAAE;gBACzB,eAAe;gBACf,YAAY;aACb,CAAC,EACF,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC5D,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,WAAW,EAAE,sBAAsB,CAAC,EAAE;gBACtC,SAAS,EAAE,GAAG;aACf,CAAC,CAAC,CAAC;YAEJ,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YAC5E,yBAAyB,CAAC,MAAM,CAAC,eAAe,CAAC;gBAC/C,GAAG,eAAe;gBAClB,WAAW,EAAE,CAAC,EAAE,gDAAgD;aACjE,CAAC,CAAC;YACH,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAClE,iBAAiB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;YAEjE,MAAM,OAAO,CAAC,YAAY,CAAC,eAAe,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YAEpE,MAAM,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAC3D,MAAM,CAAC,gBAAgB,CAAC;gBACtB,SAAS,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBACjC,SAAS,EAAE,EAAE;iBACd,CAAC;gBACF,WAAW,EAAE,CAAC;aACf,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,KAAK,GAAG,SAAS,CAAC;YACxB,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAErE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAEnD,MAAM,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC7D,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE;gBACpB,SAAS,EAAE,CAAC,oBAAoB,CAAC;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,KAAK,GAAG,kBAAkB,CAAC;YACjC,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE1D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAEnD,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,KAAK,GAAG,SAAS,CAAC;YACxB,MAAM,eAAe,GAAG;gBACtB,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,wBAAwB,EAAE,UAAU,EAAE,IAAI,EAAE;gBAClE,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,qBAAqB,EAAE,UAAU,EAAE,IAAI,EAAE;aAChE,CAAC;YAEF,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAEjE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAEtD,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBACzD,KAAK,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE;gBAC/B,SAAS,EAAE,CAAC,oBAAoB,CAAC;gBACjC,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;aAC7B,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,KAAK,GAAG,SAAS,CAAC;YACxB,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,OAAO,GAAG;gBACd,GAAG,eAAe;gBAClB,UAAU,EAAE,KAAK;gBACjB,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;aAC3B,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC,iBAAiB,CAAC,OAAc,CAAC,CAAC;YACxE,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE1D,MAAM,OAAO,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;YAC1E,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACrE,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,cAAc,EACd,KAAK,EACL,EAAE,MAAM,EAAE,cAAc,EAAE,CAC3B,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,KAAK,GAAG,kBAAkB,CAAC;YACjC,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE9D,MAAM,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;iBACnD,OAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,KAAK,GAAG,SAAS,CAAC;YACxB,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,OAAO,GAAG;gBACd,GAAG,eAAe;gBAClB,UAAU,EAAE,IAAI;aACjB,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC,iBAAiB,CAAC,OAAc,CAAC,CAAC;YAExE,MAAM,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;iBACnD,OAAO,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai-ml\\application\\services\\vulnerability-analysis.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken, getQueueToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { Queue } from 'bull';\r\nimport { VulnerabilityAnalysisService } from './vulnerability-analysis.service';\r\nimport { AnalysisJob } from '../../domain/entities/analysis-job.entity';\r\nimport { Prediction } from '../../domain/entities/prediction.entity';\r\nimport { ModelConfiguration } from '../../domain/entities/model-configuration.entity';\r\nimport { AIServiceProvider } from '../../infrastructure/services/ai-service-provider.service';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\n\r\ndescribe('VulnerabilityAnalysisService', () => {\r\n  let service: VulnerabilityAnalysisService;\r\n  let analysisJobRepository: Repository<AnalysisJob>;\r\n  let predictionRepository: Repository<Prediction>;\r\n  let modelConfigRepository: Repository<ModelConfiguration>;\r\n  let analysisQueue: Queue;\r\n  let aiServiceProvider: AIServiceProvider;\r\n  let loggerService: LoggerService;\r\n  let auditService: AuditService;\r\n\r\n  const mockAnalysisJobRepository = {\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    findOne: jest.fn(),\r\n    find: jest.fn(),\r\n    remove: jest.fn(),\r\n  };\r\n\r\n  const mockPredictionRepository = {\r\n    find: jest.fn(),\r\n  };\r\n\r\n  const mockModelConfigRepository = {\r\n    findOne: jest.fn(),\r\n  };\r\n\r\n  const mockAnalysisQueue = {\r\n    add: jest.fn(),\r\n  };\r\n\r\n  const mockAIServiceProvider = {\r\n    predict: jest.fn(),\r\n    batchPredict: jest.fn(),\r\n  };\r\n\r\n  const mockLoggerService = {\r\n    debug: jest.fn(),\r\n    log: jest.fn(),\r\n    warn: jest.fn(),\r\n    error: jest.fn(),\r\n  };\r\n\r\n  const mockAuditService = {\r\n    logUserAction: jest.fn(),\r\n  };\r\n\r\n  const mockModelConfiguration: Partial<ModelConfiguration> = {\r\n    id: 'model-123',\r\n    name: 'vulnerability_scanner_v1',\r\n    displayName: 'Vulnerability Scanner v1.0',\r\n    type: 'vulnerability_scanner',\r\n    provider: 'openai',\r\n    version: 'gpt-4-1106-preview',\r\n    status: 'active',\r\n    isDefault: true,\r\n  };\r\n\r\n  const mockAnalysisJob: Partial<AnalysisJob> = {\r\n    id: 'job-123',\r\n    type: 'vulnerability_analysis',\r\n    title: 'Vulnerability Severity Analysis - CVE-2023-12345',\r\n    status: 'pending',\r\n    priority: 'high',\r\n    progress: 0,\r\n    createdBy: 'user-123',\r\n    markAsStarted: jest.fn(),\r\n    markAsCompleted: jest.fn(),\r\n    markAsFailed: jest.fn(),\r\n    updateProgress: jest.fn(),\r\n    addResourceUsage: jest.fn(),\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        VulnerabilityAnalysisService,\r\n        {\r\n          provide: getRepositoryToken(AnalysisJob),\r\n          useValue: mockAnalysisJobRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(Prediction),\r\n          useValue: mockPredictionRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(ModelConfiguration),\r\n          useValue: mockModelConfigRepository,\r\n        },\r\n        {\r\n          provide: getQueueToken('ai-analysis'),\r\n          useValue: mockAnalysisQueue,\r\n        },\r\n        {\r\n          provide: AIServiceProvider,\r\n          useValue: mockAIServiceProvider,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: mockLoggerService,\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: mockAuditService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<VulnerabilityAnalysisService>(VulnerabilityAnalysisService);\r\n    analysisJobRepository = module.get<Repository<AnalysisJob>>(getRepositoryToken(AnalysisJob));\r\n    predictionRepository = module.get<Repository<Prediction>>(getRepositoryToken(Prediction));\r\n    modelConfigRepository = module.get<Repository<ModelConfiguration>>(getRepositoryToken(ModelConfiguration));\r\n    analysisQueue = module.get<Queue>(getQueueToken('ai-analysis'));\r\n    aiServiceProvider = module.get<AIServiceProvider>(AIServiceProvider);\r\n    loggerService = module.get<LoggerService>(LoggerService);\r\n    auditService = module.get<AuditService>(AuditService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('analyzeSeverity', () => {\r\n    it('should create vulnerability severity analysis job successfully', async () => {\r\n      const vulnerabilityData = {\r\n        cveId: 'CVE-2023-12345',\r\n        description: 'Test vulnerability',\r\n        cvssScore: 7.5,\r\n        affectedSoftware: 'Test Software',\r\n        exploitAvailable: true,\r\n        patchAvailable: false,\r\n      };\r\n      const userId = 'user-123';\r\n\r\n      mockModelConfigRepository.findOne.mockResolvedValue(mockModelConfiguration);\r\n      mockAnalysisJobRepository.create.mockReturnValue(mockAnalysisJob);\r\n      mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);\r\n      mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });\r\n\r\n      const result = await service.analyzeSeverity(vulnerabilityData, userId);\r\n\r\n      expect(mockModelConfigRepository.findOne).toHaveBeenCalledWith({\r\n        where: {\r\n          type: 'vulnerability_scanner',\r\n          status: 'active',\r\n          isDefault: true,\r\n        },\r\n      });\r\n\r\n      expect(mockAnalysisJobRepository.create).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          type: 'vulnerability_analysis',\r\n          title: 'Vulnerability Severity Analysis - CVE-2023-12345',\r\n          status: 'pending',\r\n          priority: 'high', // Based on CVSS score and exploit availability\r\n          inputData: vulnerabilityData,\r\n          modelConfigurationId: mockModelConfiguration.id,\r\n          createdBy: userId,\r\n        }),\r\n      );\r\n\r\n      expect(mockAnalysisJobRepository.save).toHaveBeenCalledWith(mockAnalysisJob);\r\n\r\n      expect(mockAnalysisQueue.add).toHaveBeenCalledWith(\r\n        'analyze-vulnerability-severity',\r\n        {\r\n          jobId: mockAnalysisJob.id,\r\n          vulnerabilityData,\r\n          modelId: mockModelConfiguration.id,\r\n        },\r\n        expect.objectContaining({\r\n          priority: 2, // High priority\r\n          attempts: 3,\r\n          backoff: 'exponential',\r\n        }),\r\n      );\r\n\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'create',\r\n        'analysis_job',\r\n        mockAnalysisJob.id,\r\n        expect.objectContaining({\r\n          type: 'vulnerability_analysis',\r\n          cveId: vulnerabilityData.cveId,\r\n          modelId: mockModelConfiguration.id,\r\n        }),\r\n      );\r\n\r\n      expect(result).toEqual(mockAnalysisJob);\r\n    });\r\n\r\n    it('should throw error when no default model is found', async () => {\r\n      const vulnerabilityData = {\r\n        description: 'Test vulnerability',\r\n        cvssScore: 5.0,\r\n      };\r\n      const userId = 'user-123';\r\n\r\n      mockModelConfigRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(service.analyzeSeverity(vulnerabilityData, userId))\r\n        .rejects.toThrow('No default model found for type: vulnerability_scanner');\r\n    });\r\n\r\n    it('should determine correct priority based on vulnerability data', async () => {\r\n      const testCases = [\r\n        {\r\n          data: { cvssScore: 9.5, exploitAvailable: true },\r\n          expectedPriority: 'urgent',\r\n        },\r\n        {\r\n          data: { cvssScore: 8.0, exploitAvailable: false },\r\n          expectedPriority: 'high',\r\n        },\r\n        {\r\n          data: { cvssScore: 5.0, exploitAvailable: false },\r\n          expectedPriority: 'normal',\r\n        },\r\n        {\r\n          data: { cvssScore: 2.0, exploitAvailable: false },\r\n          expectedPriority: 'low',\r\n        },\r\n      ];\r\n\r\n      mockModelConfigRepository.findOne.mockResolvedValue(mockModelConfiguration);\r\n      mockAnalysisJobRepository.create.mockReturnValue(mockAnalysisJob);\r\n      mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);\r\n      mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });\r\n\r\n      for (const testCase of testCases) {\r\n        const vulnerabilityData = {\r\n          description: 'Test vulnerability',\r\n          ...testCase.data,\r\n        };\r\n\r\n        await service.analyzeSeverity(vulnerabilityData, 'user-123');\r\n\r\n        expect(mockAnalysisJobRepository.create).toHaveBeenCalledWith(\r\n          expect.objectContaining({\r\n            priority: testCase.expectedPriority,\r\n          }),\r\n        );\r\n\r\n        jest.clearAllMocks();\r\n        mockModelConfigRepository.findOne.mockResolvedValue(mockModelConfiguration);\r\n        mockAnalysisJobRepository.create.mockReturnValue(mockAnalysisJob);\r\n        mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);\r\n        mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('analyzeExploitProbability', () => {\r\n    it('should create exploit probability analysis job successfully', async () => {\r\n      const vulnerabilityData = {\r\n        cveId: 'CVE-2023-12345',\r\n        description: 'Test vulnerability',\r\n        cvssScore: 7.5,\r\n        attackVector: 'network',\r\n        attackComplexity: 'low',\r\n        privilegesRequired: 'none',\r\n        userInteraction: 'none',\r\n        publicExploits: true,\r\n        ageInDays: 30,\r\n      };\r\n      const userId = 'user-123';\r\n\r\n      mockModelConfigRepository.findOne.mockResolvedValue({\r\n        ...mockModelConfiguration,\r\n        type: 'threat_classifier',\r\n      });\r\n      mockAnalysisJobRepository.create.mockReturnValue(mockAnalysisJob);\r\n      mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);\r\n      mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });\r\n\r\n      const result = await service.analyzeExploitProbability(vulnerabilityData, userId);\r\n\r\n      expect(mockModelConfigRepository.findOne).toHaveBeenCalledWith({\r\n        where: {\r\n          type: 'threat_classifier',\r\n          status: 'active',\r\n          isDefault: true,\r\n        },\r\n      });\r\n\r\n      expect(mockAnalysisJobRepository.create).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          type: 'vulnerability_analysis',\r\n          title: 'Exploit Probability Analysis - CVE-2023-12345',\r\n          inputData: vulnerabilityData,\r\n        }),\r\n      );\r\n\r\n      expect(mockAnalysisQueue.add).toHaveBeenCalledWith(\r\n        'analyze-exploit-probability',\r\n        expect.objectContaining({\r\n          jobId: mockAnalysisJob.id,\r\n          vulnerabilityData,\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n\r\n      expect(result).toEqual(mockAnalysisJob);\r\n    });\r\n  });\r\n\r\n  describe('generateRemediationRecommendations', () => {\r\n    it('should create remediation recommendations job successfully', async () => {\r\n      const vulnerabilityData = {\r\n        cveId: 'CVE-2023-12345',\r\n        description: 'Test vulnerability',\r\n        affectedSoftware: 'Test Software',\r\n        version: '1.0.0',\r\n        severity: 'high',\r\n        exploitAvailable: false,\r\n        patchAvailable: true,\r\n      };\r\n\r\n      const environmentContext = {\r\n        operatingSystem: 'Ubuntu 20.04',\r\n        architecture: 'x86_64',\r\n        businessCriticality: 'high',\r\n        maintenanceWindow: 'weekends 02:00-06:00 UTC',\r\n        complianceRequirements: ['PCI-DSS', 'HIPAA'],\r\n      };\r\n\r\n      const userId = 'user-123';\r\n\r\n      mockModelConfigRepository.findOne.mockResolvedValue({\r\n        ...mockModelConfiguration,\r\n        type: 'nlp_processor',\r\n      });\r\n      mockAnalysisJobRepository.create.mockReturnValue(mockAnalysisJob);\r\n      mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);\r\n      mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });\r\n\r\n      const result = await service.generateRemediationRecommendations(\r\n        vulnerabilityData,\r\n        environmentContext,\r\n        userId,\r\n      );\r\n\r\n      expect(mockModelConfigRepository.findOne).toHaveBeenCalledWith({\r\n        where: {\r\n          type: 'nlp_processor',\r\n          status: 'active',\r\n          isDefault: true,\r\n        },\r\n      });\r\n\r\n      expect(mockAnalysisJobRepository.create).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          type: 'vulnerability_analysis',\r\n          title: 'Remediation Recommendations - CVE-2023-12345',\r\n          inputData: {\r\n            vulnerability: vulnerabilityData,\r\n            environment: environmentContext,\r\n          },\r\n        }),\r\n      );\r\n\r\n      expect(mockAnalysisQueue.add).toHaveBeenCalledWith(\r\n        'generate-remediation-recommendations',\r\n        expect.objectContaining({\r\n          jobId: mockAnalysisJob.id,\r\n          vulnerabilityData,\r\n          environmentContext,\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n\r\n      expect(result).toEqual(mockAnalysisJob);\r\n    });\r\n  });\r\n\r\n  describe('batchAnalyze', () => {\r\n    it('should create batch analysis job successfully', async () => {\r\n      const vulnerabilities = [\r\n        {\r\n          id: 'vuln-1',\r\n          cveId: 'CVE-2023-12345',\r\n          description: 'Test vulnerability 1',\r\n          cvssScore: 7.5,\r\n        },\r\n        {\r\n          id: 'vuln-2',\r\n          cveId: 'CVE-2023-12346',\r\n          description: 'Test vulnerability 2',\r\n          cvssScore: 8.0,\r\n        },\r\n      ];\r\n      const analysisType = 'severity';\r\n      const userId = 'user-123';\r\n\r\n      mockModelConfigRepository.findOne.mockResolvedValue(mockModelConfiguration);\r\n      mockAnalysisJobRepository.create.mockReturnValue({\r\n        ...mockAnalysisJob,\r\n        totalStages: 1,\r\n      });\r\n      mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);\r\n      mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });\r\n\r\n      const result = await service.batchAnalyze(vulnerabilities, analysisType, userId);\r\n\r\n      expect(mockAnalysisJobRepository.create).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          type: 'vulnerability_analysis',\r\n          title: 'Batch severity Analysis - 2 vulnerabilities',\r\n          inputData: {\r\n            vulnerabilities,\r\n            analysisType,\r\n            batchSize: 2,\r\n          },\r\n          totalStages: 1,\r\n        }),\r\n      );\r\n\r\n      expect(mockAnalysisQueue.add).toHaveBeenCalledWith(\r\n        'batch-vulnerability-analysis',\r\n        expect.objectContaining({\r\n          jobId: mockAnalysisJob.id,\r\n          vulnerabilities,\r\n          analysisType,\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n\r\n      expect(result).toEqual(mockAnalysisJob);\r\n    });\r\n\r\n    it('should limit batch size to maximum of 10', async () => {\r\n      const vulnerabilities = Array.from({ length: 15 }, (_, i) => ({\r\n        id: `vuln-${i}`,\r\n        description: `Test vulnerability ${i}`,\r\n        cvssScore: 5.0,\r\n      }));\r\n\r\n      mockModelConfigRepository.findOne.mockResolvedValue(mockModelConfiguration);\r\n      mockAnalysisJobRepository.create.mockReturnValue({\r\n        ...mockAnalysisJob,\r\n        totalStages: 2, // 15 vulnerabilities / 10 batch size = 2 stages\r\n      });\r\n      mockAnalysisJobRepository.save.mockResolvedValue(mockAnalysisJob);\r\n      mockAnalysisQueue.add.mockResolvedValue({ id: 'queue-job-123' });\r\n\r\n      await service.batchAnalyze(vulnerabilities, 'severity', 'user-123');\r\n\r\n      expect(mockAnalysisJobRepository.create).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          inputData: expect.objectContaining({\r\n            batchSize: 10,\r\n          }),\r\n          totalStages: 2,\r\n        }),\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('getAnalysisJob', () => {\r\n    it('should return analysis job when found', async () => {\r\n      const jobId = 'job-123';\r\n      mockAnalysisJobRepository.findOne.mockResolvedValue(mockAnalysisJob);\r\n\r\n      const result = await service.getAnalysisJob(jobId);\r\n\r\n      expect(mockAnalysisJobRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: jobId },\r\n        relations: ['modelConfiguration'],\r\n      });\r\n      expect(result).toEqual(mockAnalysisJob);\r\n    });\r\n\r\n    it('should return null when job not found', async () => {\r\n      const jobId = 'non-existent-job';\r\n      mockAnalysisJobRepository.findOne.mockResolvedValue(null);\r\n\r\n      const result = await service.getAnalysisJob(jobId);\r\n\r\n      expect(result).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('getJobPredictions', () => {\r\n    it('should return predictions for analysis job', async () => {\r\n      const jobId = 'job-123';\r\n      const mockPredictions = [\r\n        { id: 'pred-1', type: 'vulnerability_severity', confidence: 0.85 },\r\n        { id: 'pred-2', type: 'exploit_probability', confidence: 0.72 },\r\n      ];\r\n\r\n      mockPredictionRepository.find.mockResolvedValue(mockPredictions);\r\n\r\n      const result = await service.getJobPredictions(jobId);\r\n\r\n      expect(mockPredictionRepository.find).toHaveBeenCalledWith({\r\n        where: { analysisJobId: jobId },\r\n        relations: ['modelConfiguration'],\r\n        order: { createdAt: 'DESC' },\r\n      });\r\n      expect(result).toEqual(mockPredictions);\r\n    });\r\n  });\r\n\r\n  describe('cancelAnalysisJob', () => {\r\n    it('should cancel analysis job successfully', async () => {\r\n      const jobId = 'job-123';\r\n      const userId = 'user-123';\r\n      const mockJob = {\r\n        ...mockAnalysisJob,\r\n        isTerminal: false,\r\n        markAsCancelled: jest.fn(),\r\n      };\r\n\r\n      jest.spyOn(service, 'getAnalysisJob').mockResolvedValue(mockJob as any);\r\n      mockAnalysisJobRepository.save.mockResolvedValue(mockJob);\r\n\r\n      await service.cancelAnalysisJob(jobId, userId);\r\n\r\n      expect(service.getAnalysisJob).toHaveBeenCalledWith(jobId);\r\n      expect(mockJob.markAsCancelled).toHaveBeenCalledWith('Cancelled by user');\r\n      expect(mockAnalysisJobRepository.save).toHaveBeenCalledWith(mockJob);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'cancel',\r\n        'analysis_job',\r\n        jobId,\r\n        { reason: 'user_request' },\r\n      );\r\n    });\r\n\r\n    it('should throw error when job not found', async () => {\r\n      const jobId = 'non-existent-job';\r\n      const userId = 'user-123';\r\n\r\n      jest.spyOn(service, 'getAnalysisJob').mockResolvedValue(null);\r\n\r\n      await expect(service.cancelAnalysisJob(jobId, userId))\r\n        .rejects.toThrow('Analysis job not found');\r\n    });\r\n\r\n    it('should throw error when trying to cancel completed job', async () => {\r\n      const jobId = 'job-123';\r\n      const userId = 'user-123';\r\n      const mockJob = {\r\n        ...mockAnalysisJob,\r\n        isTerminal: true,\r\n      };\r\n\r\n      jest.spyOn(service, 'getAnalysisJob').mockResolvedValue(mockJob as any);\r\n\r\n      await expect(service.cancelAnalysisJob(jobId, userId))\r\n        .rejects.toThrow('Cannot cancel completed job');\r\n    });\r\n  });\r\n});\r\n"], "version": 3}