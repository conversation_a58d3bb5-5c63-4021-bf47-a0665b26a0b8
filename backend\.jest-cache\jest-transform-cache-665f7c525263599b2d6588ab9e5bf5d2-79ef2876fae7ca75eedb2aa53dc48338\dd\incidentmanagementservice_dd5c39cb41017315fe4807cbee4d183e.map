{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\incident-response\\application\\services\\incident-management.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAA4F;AAC5F,6CAAmD;AACnD,qCAA0D;AAC1D,2EAAiE;AACjE,qFAA0E;AAC1E,6FAAkF;AAClF,6FAAkF;AAClF,qFAA0E;AAC1E,sFAAkF;AAClF,0FAAsF;AACtF,iEAA6D;AAC7D,qFAAgF;AAEhF;;;GAGG;AAEI,IAAM,yBAAyB,iCAA/B,MAAM,yBAAyB;IAGpC,YAEE,kBAAyD,EAEzD,cAAyD,EAEzD,kBAAiE,EAEjE,kBAAiE,EAEjE,sBAAiE,EAChD,aAA4B,EAC5B,YAA0B,EAC1B,mBAAwC,EACxC,4BAA0D;QAZ1D,uBAAkB,GAAlB,kBAAkB,CAAsB;QAExC,mBAAc,GAAd,cAAc,CAA0B;QAExC,uBAAkB,GAAlB,kBAAkB,CAA8B;QAEhD,uBAAkB,GAAlB,kBAAkB,CAA8B;QAEhD,2BAAsB,GAAtB,sBAAsB,CAA0B;QAChD,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;QAC1B,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,iCAA4B,GAA5B,4BAA4B,CAA8B;QAhB5D,WAAM,GAAG,IAAI,eAAM,CAAC,2BAAyB,CAAC,IAAI,CAAC,CAAC;IAiBlE,CAAC;IAEJ;;;;;OAKG;IACH,KAAK,CAAC,cAAc,CAClB,YAYC,EACD,MAAc;QAEd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,KAAK,EAAE,YAAY,CAAC,KAAK;gBACzB,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;gBAC9C,GAAG,YAAY;gBACf,UAAU,EAAE,YAAY,CAAC,UAAU,IAAI,IAAI,IAAI,EAAE;gBACjD,QAAQ,EAAE,YAAY,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,QAAQ,CAAC;gBACvF,MAAM,EAAE,YAAY,CAAC,MAAM,IAAI,QAAQ;gBACvC,eAAe,EAAE,YAAY,CAAC,eAAe,IAAI,UAAU;gBAC3D,IAAI,EAAE,YAAY,CAAC,IAAI,IAAI,EAAE;gBAC7B,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEnE,gCAAgC;YAChC,MAAM,IAAI,CAAC,gBAAgB,CACzB,aAAa,CAAC,EAAE,EAChB,kBAAkB,EAClB,kBAAkB,EAClB,MAAM,EACN,EAAE,QAAQ,EAAE,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,QAAQ,EAAE,CACrE,CAAC;YAEF,2CAA2C;YAC3C,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAE5C,qBAAqB;YACrB,MAAM,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YAElF,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,UAAU,EACV,aAAa,CAAC,EAAE,EAChB;gBACE,KAAK,EAAE,YAAY,CAAC,KAAK;gBACzB,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,QAAQ,EAAE,YAAY,CAAC,QAAQ;aAChC,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,EAAE;gBAC/C,UAAU,EAAE,aAAa,CAAC,EAAE;gBAC5B,KAAK,EAAE,YAAY,CAAC,KAAK;gBACzB,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,YAAY;gBACZ,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,EAAU;QACvB,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBAC3C,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,UAAU,EAAE,cAAc,CAAC;aAC/E,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBACjD,EAAE;gBACF,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,OAad;QACC,IAAI,CAAC;YACH,MAAM,EACJ,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,MAAM,EACN,IAAI,EACJ,SAAS,EACT,MAAM,GAAG,WAAW,EACpB,SAAS,GAAG,MAAM,GACnB,GAAG,OAAO,CAAC;YAEZ,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAE5E,gBAAgB;YAChB,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,YAAY,CAAC,QAAQ,CAAC,iCAAiC,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YACvE,CAAC;YAED,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,YAAY,CAAC,QAAQ,CAAC,qCAAqC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,YAAY,CAAC,QAAQ,CAAC,qCAAqC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,YAAY,CAAC,QAAQ,CAAC,qCAAqC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,UAAU,EAAE,CAAC;gBACf,YAAY,CAAC,QAAQ,CAAC,mCAAmC,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACX,YAAY,CAAC,QAAQ,CACnB,sEAAsE,EACtE,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,EAAE,CAC1B,CAAC;YACJ,CAAC;YAED,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,YAAY,CAAC,QAAQ,CAAC,wBAAwB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;YAC5D,CAAC;YAED,IAAI,SAAS,EAAE,CAAC;gBACd,YAAY,CAAC,QAAQ,CAAC,6CAA6C,EAAE;oBACnE,KAAK,EAAE,SAAS,CAAC,KAAK;oBACtB,GAAG,EAAE,SAAS,CAAC,GAAG;iBACnB,CAAC,CAAC;YACL,CAAC;YAED,gBAAgB;YAChB,YAAY,CAAC,OAAO,CAAC,YAAY,MAAM,EAAE,EAAE,SAAS,CAAC,CAAC;YAEtD,mBAAmB;YACnB,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAClC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;YAChE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;YAE5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;gBACvC,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,UAAU;gBACV,OAAO,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE;aAC5E,CAAC,CAAC;YAEH,OAAO;gBACL,SAAS;gBACT,KAAK;gBACL,IAAI;gBACJ,UAAU;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBAC5C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,OAAO;aACR,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,cAAc,CAClB,EAAU,EACV,UAA6B,EAC7B,MAAc;QAEd,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,CAAC,CAAC;YACpD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE;gBACrC,UAAU,EAAE,EAAE;gBACd,UAAU;gBACV,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC;YACvC,MAAM,gBAAgB,GAAG,QAAQ,CAAC,QAAQ,CAAC;YAC3C,MAAM,gBAAgB,GAAG,QAAQ,CAAC,QAAQ,CAAC;YAE3C,6BAA6B;YAC7B,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE;gBAClC,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAErE,kDAAkD;YAClD,IAAI,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;gBAC9D,MAAM,IAAI,CAAC,gBAAgB,CACzB,EAAE,EACF,gBAAgB,EAChB,uBAAuB,cAAc,OAAO,UAAU,CAAC,MAAM,EAAE,EAC/D,MAAM,EACN,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,CAAC,MAAM,EAAE,CACjD,CAAC;YACJ,CAAC;YAED,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,KAAK,gBAAgB,EAAE,CAAC;gBACpE,MAAM,IAAI,CAAC,gBAAgB,CACzB,EAAE,EACF,kBAAkB,EAClB,yBAAyB,gBAAgB,OAAO,UAAU,CAAC,QAAQ,EAAE,EACrE,MAAM,EACN,EAAE,gBAAgB,EAAE,WAAW,EAAE,UAAU,CAAC,QAAQ,EAAE,CACvD,CAAC;YACJ,CAAC;YAED,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,KAAK,gBAAgB,EAAE,CAAC;gBACpE,MAAM,IAAI,CAAC,gBAAgB,CACzB,EAAE,EACF,kBAAkB,EAClB,yBAAyB,gBAAgB,OAAO,UAAU,CAAC,QAAQ,EAAE,EACrE,MAAM,EACN,EAAE,gBAAgB,EAAE,WAAW,EAAE,UAAU,CAAC,QAAQ,EAAE,CACvD,CAAC;YACJ,CAAC;YAED,wCAAwC;YACxC,IAAI,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;gBAC9D,MAAM,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC;YAC7F,CAAC;YAED,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,UAAU,EACV,EAAE,EACF;gBACE,OAAO,EAAE,UAAU;gBACnB,cAAc,EAAE,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,EAAE,gBAAgB,EAAE;aACnG,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,EAAE;gBAC/C,UAAU,EAAE,EAAE;gBACd,MAAM;gBACN,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;aACjC,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,mBAAmB,CAAC,EAAU,EAAE,MAAc;QAClD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,CAAC,CAAC;YACpD,CAAC;YAED,IAAI,QAAQ,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;gBAC9B,MAAM,IAAI,4BAAmB,CAAC,wCAAwC,CAAC,CAAC;YAC1E,CAAC;YAED,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC7B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAErE,MAAM,IAAI,CAAC,gBAAgB,CACzB,EAAE,EACF,cAAc,EACd,iDAAiD,EACjD,MAAM,EACN,EAAE,iBAAiB,EAAE,QAAQ,CAAC,OAAO,EAAE,iBAAiB,EAAE,CAC3D,CAAC;YAEF,MAAM,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;YAEzF,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,aAAa,EACb,UAAU,EACV,EAAE,EACF,EAAE,iBAAiB,EAAE,QAAQ,CAAC,OAAO,EAAE,iBAAiB,EAAE,CAC3D,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,EAAE;gBACvC,UAAU,EAAE,EAAE;gBACd,MAAM;gBACN,iBAAiB,EAAE,QAAQ,CAAC,OAAO,EAAE,iBAAiB;aACvD,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,gBAAgB,CAAC,EAAU,EAAE,MAAc,EAAE,MAAc;QAC/D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,CAAC,CAAC;YACpD,CAAC;YAED,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAClC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAErE,MAAM,IAAI,CAAC,gBAAgB,CACzB,EAAE,EACF,WAAW,EACX,uBAAuB,MAAM,EAAE,EAC/B,MAAM,EACN,EAAE,eAAe,EAAE,QAAQ,CAAC,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE,CAC/D,CAAC;YAEF,MAAM,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;YAEtF,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,UAAU,EACV,UAAU,EACV,EAAE,EACF,EAAE,MAAM,EAAE,eAAe,EAAE,QAAQ,CAAC,OAAO,EAAE,eAAe,EAAE,CAC/D,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE;gBACpC,UAAU,EAAE,EAAE;gBACd,MAAM;gBACN,MAAM;gBACN,eAAe,EAAE,QAAQ,CAAC,OAAO,EAAE,eAAe;aACnD,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,eAAe,CACnB,EAAU,EACV,UAAkB,EAClB,MAAc;QAEd,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,CAAC,CAAC;YACpD,CAAC;YAED,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACrC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAErE,MAAM,IAAI,CAAC,gBAAgB,CACzB,EAAE,EACF,UAAU,EACV,sBAAsB,UAAU,EAAE,EAClC,MAAM,EACN;gBACE,gBAAgB,EAAE,QAAQ,CAAC,OAAO,EAAE,gBAAgB;gBACpD,YAAY,EAAE,QAAQ,CAAC,OAAO,EAAE,YAAY;gBAC5C,UAAU;aACX,CACF,CAAC;YAEF,MAAM,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;YAErF,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,SAAS,EACT,UAAU,EACV,EAAE,EACF;gBACE,UAAU;gBACV,gBAAgB,EAAE,QAAQ,CAAC,OAAO,EAAE,gBAAgB;gBACpD,YAAY,EAAE,QAAQ,CAAC,OAAO,EAAE,YAAY;aAC7C,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,EAAE;gBACnC,UAAU,EAAE,EAAE;gBACd,MAAM;gBACN,gBAAgB,EAAE,QAAQ,CAAC,OAAO,EAAE,gBAAgB;gBACpD,YAAY,EAAE,QAAQ,CAAC,OAAO,EAAE,YAAY;aAC7C,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBAC9C,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,aAAa,CAAC,EAAU,EAAE,MAAc;QAC5C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,CAAC,CAAC;YACpD,CAAC;YAED,IAAI,QAAQ,CAAC,MAAM,KAAK,eAAe,EAAE,CAAC;gBACxC,MAAM,IAAI,4BAAmB,CAAC,0CAA0C,CAAC,CAAC;YAC5E,CAAC;YAED,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACvB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAErE,MAAM,IAAI,CAAC,gBAAgB,CACzB,EAAE,EACF,QAAQ,EACR,iBAAiB,EACjB,MAAM,CACP,CAAC;YAEF,MAAM,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAEnF,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,OAAO,EACP,UAAU,EACV,EAAE,CACH,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE;gBACjC,UAAU,EAAE,EAAE;gBACd,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBAC5C,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,aAAa,CACjB,EAAU,EACV,MAAc,EACd,IAAY,EACZ,WAAqB,EACrB,OAAe;QAEf,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,CAAC,CAAC;YACpD,CAAC;YAED,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;YAClD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAErE,MAAM,IAAI,CAAC,gBAAgB,CACzB,EAAE,EACF,mBAAmB,EACnB,sBAAsB,IAAI,EAAE,EAC5B,OAAO,EACP,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,CAC9B,CAAC;YAEF,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,OAAO,EACP,iBAAiB,EACjB,UAAU,EACV,EAAE,EACF,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,CAC9B,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,EAAE;gBAC/C,UAAU,EAAE,EAAE;gBACd,MAAM;gBACN,IAAI;gBACJ,OAAO;aACR,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;gBACN,OAAO;aACR,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,MAAM,CACJ,cAAc,EACd,aAAa,EACb,iBAAiB,EACjB,gBAAgB,EAChB,iBAAiB,EACjB,mBAAmB,EACnB,qBAAqB,EACrB,aAAa,EACd,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE;gBAC/B,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,IAAA,YAAE,EAAC,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC,EAAE,EAAE,CAAC;gBAC5H,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,CAAC;gBAClE,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,gBAAgB,EAAE;aACxB,CAAC,CAAC;YAEH,OAAO;gBACL,cAAc;gBACd,aAAa;gBACb,iBAAiB;gBACjB,gBAAgB;gBAChB,QAAQ,EAAE,iBAAiB;gBAC3B,UAAU,EAAE,mBAAmB;gBAC/B,WAAW,EAAE;oBACX,qBAAqB;oBACrB,aAAa;iBACd;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,iBAAiB,CAAC,QAAkB;QAChD,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;gBAC3D,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC/C,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CACtC,CAAC;YAEF,IAAI,cAAc,EAAE,CAAC;gBACnB,QAAQ,CAAC,cAAc,GAAG,cAAc,CAAC,EAAE,CAAC;gBAC5C,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAE7C,+BAA+B;gBAC/B,MAAM,IAAI,CAAC,4BAA4B,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;gBAE5F,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,EAAE;oBACnD,UAAU,EAAE,QAAQ,CAAC,EAAE;oBACvB,cAAc,EAAE,cAAc,CAAC,EAAE;oBACjC,QAAQ,EAAE,cAAc,CAAC,IAAI;iBAC9B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;gBAChD,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACK,KAAK,CAAC,gBAAgB,CAC5B,UAAkB,EAClB,SAAiB,EACjB,WAAmB,EACnB,MAAc,EACd,QAAc;QAEd,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;gBACnD,UAAU;gBACV,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC/C,UAAU;gBACV,SAAS;gBACT,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,wBAAwB,CAAC,QAAgB;QAC/C,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,UAAU;gBACb,OAAO,QAAQ,CAAC;YAClB,KAAK,MAAM;gBACT,OAAO,MAAM,CAAC;YAChB,KAAK,QAAQ;gBACX,OAAO,QAAQ,CAAC;YAClB,KAAK,KAAK,CAAC;YACX;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED,gCAAgC;IACxB,KAAK,CAAC,wBAAwB;QACpC,kDAAkD;QAClD,gCAAgC;QAChC,OAAO,CAAC,CAAC;IACX,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB;aACzC,kBAAkB,CAAC,UAAU,CAAC;aAC9B,MAAM,CAAC,iBAAiB,EAAE,QAAQ,CAAC;aACnC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,OAAO,CAAC,iBAAiB,CAAC;aAC1B,UAAU,EAAE,CAAC;QAEhB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACjC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAClC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB;aACzC,kBAAkB,CAAC,UAAU,CAAC;aAC9B,MAAM,CAAC,mBAAmB,EAAE,UAAU,CAAC;aACvC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,OAAO,CAAC,mBAAmB,CAAC;aAC5B,UAAU,EAAE,CAAC;QAEhB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACjC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEO,KAAK,CAAC,wBAAwB;QACpC,0CAA0C;QAC1C,gCAAgC;QAChC,OAAO,GAAG,CAAC,CAAC,qBAAqB;IACnC,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,yDAAyD;QACzD,gCAAgC;QAChC,OAAO,IAAI,CAAC,CAAC,mBAAmB;IAClC,CAAC;CACF,CAAA;AAhyBY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,0BAAQ,CAAC,CAAA;IAE1B,WAAA,IAAA,0BAAgB,EAAC,mCAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,0BAAgB,EAAC,2CAAgB,CAAC,CAAA;IAElC,WAAA,IAAA,0BAAgB,EAAC,2CAAgB,CAAC,CAAA;IAElC,WAAA,IAAA,0BAAgB,EAAC,mCAAY,CAAC,CAAA;yDAPM,oBAAU,oBAAV,oBAAU,oDAEd,oBAAU,oBAAV,oBAAU,oDAEN,oBAAU,oBAAV,oBAAU,oDAEV,oBAAU,oBAAV,oBAAU,oDAEN,oBAAU,oBAAV,oBAAU,oDACnB,8BAAa,oBAAb,8BAAa,oDACd,4BAAY,oBAAZ,4BAAY,oDACL,0CAAmB,oBAAnB,0CAAmB,oDACV,6DAA4B,oBAA5B,6DAA4B;GAjBlE,yBAAyB,CAgyBrC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\incident-response\\application\\services\\incident-management.service.ts"], "sourcesContent": ["import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, FindManyOptions, In } from 'typeorm';\r\nimport { Incident } from '../../domain/entities/incident.entity';\r\nimport { IncidentTask } from '../../domain/entities/incident-task.entity';\r\nimport { IncidentEvidence } from '../../domain/entities/incident-evidence.entity';\r\nimport { IncidentTimeline } from '../../domain/entities/incident-timeline.entity';\r\nimport { ResponsePlan } from '../../domain/entities/response-plan.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from './notification.service';\r\nimport { ResponseOrchestrationService } from './response-orchestration.service';\r\n\r\n/**\r\n * Incident Management service\r\n * Handles incident lifecycle management, escalation, and coordination\r\n */\r\n@Injectable()\r\nexport class IncidentManagementService {\r\n  private readonly logger = new Logger(IncidentManagementService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(Incident)\r\n    private readonly incidentRepository: Repository<Incident>,\r\n    @InjectRepository(IncidentTask)\r\n    private readonly taskRepository: Repository<IncidentTask>,\r\n    @InjectRepository(IncidentEvidence)\r\n    private readonly evidenceRepository: Repository<IncidentEvidence>,\r\n    @InjectRepository(IncidentTimeline)\r\n    private readonly timelineRepository: Repository<IncidentTimeline>,\r\n    @InjectRepository(ResponsePlan)\r\n    private readonly responsePlanRepository: Repository<ResponsePlan>,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly auditService: AuditService,\r\n    private readonly notificationService: NotificationService,\r\n    private readonly responseOrchestrationService: ResponseOrchestrationService,\r\n  ) {}\r\n\r\n  /**\r\n   * Create a new incident\r\n   * @param incidentData Incident creation data\r\n   * @param userId User creating the incident\r\n   * @returns Created incident\r\n   */\r\n  async createIncident(\r\n    incidentData: {\r\n      title: string;\r\n      description: string;\r\n      severity: 'low' | 'medium' | 'high' | 'critical';\r\n      priority?: 'low' | 'medium' | 'high' | 'urgent';\r\n      category: string;\r\n      source?: string;\r\n      detectedAt?: Date;\r\n      occurredAt?: Date;\r\n      details?: any;\r\n      tags?: string[];\r\n      confidentiality?: 'public' | 'internal' | 'confidential' | 'restricted';\r\n    },\r\n    userId: string,\r\n  ): Promise<Incident> {\r\n    try {\r\n      this.logger.debug('Creating new incident', {\r\n        title: incidentData.title,\r\n        severity: incidentData.severity,\r\n        category: incidentData.category,\r\n        userId,\r\n      });\r\n\r\n      const incident = this.incidentRepository.create({\r\n        ...incidentData,\r\n        detectedAt: incidentData.detectedAt || new Date(),\r\n        priority: incidentData.priority || this.calculateDefaultPriority(incidentData.severity),\r\n        source: incidentData.source || 'manual',\r\n        confidentiality: incidentData.confidentiality || 'internal',\r\n        tags: incidentData.tags || [],\r\n        createdBy: userId,\r\n      });\r\n\r\n      const savedIncident = await this.incidentRepository.save(incident);\r\n\r\n      // Create initial timeline entry\r\n      await this.addTimelineEntry(\r\n        savedIncident.id,\r\n        'incident_created',\r\n        'Incident created',\r\n        userId,\r\n        { severity: incidentData.severity, category: incidentData.category },\r\n      );\r\n\r\n      // Find and apply appropriate response plan\r\n      await this.applyResponsePlan(savedIncident);\r\n\r\n      // Send notifications\r\n      await this.notificationService.sendIncidentNotification(savedIncident, 'created');\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'create',\r\n        'incident',\r\n        savedIncident.id,\r\n        {\r\n          title: incidentData.title,\r\n          severity: incidentData.severity,\r\n          category: incidentData.category,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Incident created successfully', {\r\n        incidentId: savedIncident.id,\r\n        title: incidentData.title,\r\n        userId,\r\n      });\r\n\r\n      return savedIncident;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create incident', {\r\n        error: error.message,\r\n        incidentData,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find incident by ID\r\n   * @param id Incident ID\r\n   * @returns Incident or null\r\n   */\r\n  async findById(id: string): Promise<Incident | null> {\r\n    try {\r\n      return await this.incidentRepository.findOne({\r\n        where: { id },\r\n        relations: ['tasks', 'evidence', 'communications', 'timeline', 'responsePlan'],\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to find incident by ID', {\r\n        id,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find incidents with filtering and pagination\r\n   * @param options Query options\r\n   * @returns Paginated incidents\r\n   */\r\n  async findMany(options: {\r\n    page?: number;\r\n    limit?: number;\r\n    status?: string[];\r\n    severity?: string[];\r\n    priority?: string[];\r\n    category?: string[];\r\n    assignedTo?: string;\r\n    search?: string;\r\n    tags?: string[];\r\n    dateRange?: { start: Date; end: Date };\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n  }): Promise<{ incidents: Incident[]; total: number; page: number; totalPages: number }> {\r\n    try {\r\n      const {\r\n        page = 1,\r\n        limit = 20,\r\n        status,\r\n        severity,\r\n        priority,\r\n        category,\r\n        assignedTo,\r\n        search,\r\n        tags,\r\n        dateRange,\r\n        sortBy = 'createdAt',\r\n        sortOrder = 'DESC',\r\n      } = options;\r\n\r\n      const queryBuilder = this.incidentRepository.createQueryBuilder('incident');\r\n\r\n      // Apply filters\r\n      if (status && status.length > 0) {\r\n        queryBuilder.andWhere('incident.status IN (:...status)', { status });\r\n      }\r\n\r\n      if (severity && severity.length > 0) {\r\n        queryBuilder.andWhere('incident.severity IN (:...severity)', { severity });\r\n      }\r\n\r\n      if (priority && priority.length > 0) {\r\n        queryBuilder.andWhere('incident.priority IN (:...priority)', { priority });\r\n      }\r\n\r\n      if (category && category.length > 0) {\r\n        queryBuilder.andWhere('incident.category IN (:...category)', { category });\r\n      }\r\n\r\n      if (assignedTo) {\r\n        queryBuilder.andWhere('incident.assignedTo = :assignedTo', { assignedTo });\r\n      }\r\n\r\n      if (search) {\r\n        queryBuilder.andWhere(\r\n          '(incident.title ILIKE :search OR incident.description ILIKE :search)',\r\n          { search: `%${search}%` },\r\n        );\r\n      }\r\n\r\n      if (tags && tags.length > 0) {\r\n        queryBuilder.andWhere('incident.tags && :tags', { tags });\r\n      }\r\n\r\n      if (dateRange) {\r\n        queryBuilder.andWhere('incident.detectedAt BETWEEN :start AND :end', {\r\n          start: dateRange.start,\r\n          end: dateRange.end,\r\n        });\r\n      }\r\n\r\n      // Apply sorting\r\n      queryBuilder.orderBy(`incident.${sortBy}`, sortOrder);\r\n\r\n      // Apply pagination\r\n      const offset = (page - 1) * limit;\r\n      queryBuilder.skip(offset).take(limit);\r\n\r\n      const [incidents, total] = await queryBuilder.getManyAndCount();\r\n      const totalPages = Math.ceil(total / limit);\r\n\r\n      this.logger.debug('Incidents retrieved', {\r\n        total,\r\n        page,\r\n        limit,\r\n        totalPages,\r\n        filters: { status, severity, priority, category, assignedTo, search, tags },\r\n      });\r\n\r\n      return {\r\n        incidents,\r\n        total,\r\n        page,\r\n        totalPages,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to find incidents', {\r\n        error: error.message,\r\n        options,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update incident\r\n   * @param id Incident ID\r\n   * @param updateData Update data\r\n   * @param userId User updating the incident\r\n   * @returns Updated incident\r\n   */\r\n  async updateIncident(\r\n    id: string,\r\n    updateData: Partial<Incident>,\r\n    userId: string,\r\n  ): Promise<Incident> {\r\n    try {\r\n      const incident = await this.findById(id);\r\n      if (!incident) {\r\n        throw new NotFoundException('Incident not found');\r\n      }\r\n\r\n      this.logger.debug('Updating incident', {\r\n        incidentId: id,\r\n        updateData,\r\n        userId,\r\n      });\r\n\r\n      const previousStatus = incident.status;\r\n      const previousSeverity = incident.severity;\r\n      const previousPriority = incident.priority;\r\n\r\n      // Update incident properties\r\n      Object.assign(incident, updateData, {\r\n        updatedBy: userId,\r\n      });\r\n\r\n      const updatedIncident = await this.incidentRepository.save(incident);\r\n\r\n      // Create timeline entries for significant changes\r\n      if (updateData.status && updateData.status !== previousStatus) {\r\n        await this.addTimelineEntry(\r\n          id,\r\n          'status_changed',\r\n          `Status changed from ${previousStatus} to ${updateData.status}`,\r\n          userId,\r\n          { previousStatus, newStatus: updateData.status },\r\n        );\r\n      }\r\n\r\n      if (updateData.severity && updateData.severity !== previousSeverity) {\r\n        await this.addTimelineEntry(\r\n          id,\r\n          'severity_changed',\r\n          `Severity changed from ${previousSeverity} to ${updateData.severity}`,\r\n          userId,\r\n          { previousSeverity, newSeverity: updateData.severity },\r\n        );\r\n      }\r\n\r\n      if (updateData.priority && updateData.priority !== previousPriority) {\r\n        await this.addTimelineEntry(\r\n          id,\r\n          'priority_changed',\r\n          `Priority changed from ${previousPriority} to ${updateData.priority}`,\r\n          userId,\r\n          { previousPriority, newPriority: updateData.priority },\r\n        );\r\n      }\r\n\r\n      // Send notifications for status changes\r\n      if (updateData.status && updateData.status !== previousStatus) {\r\n        await this.notificationService.sendIncidentNotification(updatedIncident, 'status_changed');\r\n      }\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'update',\r\n        'incident',\r\n        id,\r\n        {\r\n          changes: updateData,\r\n          previousValues: { status: previousStatus, severity: previousSeverity, priority: previousPriority },\r\n        },\r\n      );\r\n\r\n      this.logger.log('Incident updated successfully', {\r\n        incidentId: id,\r\n        userId,\r\n        changes: Object.keys(updateData),\r\n      });\r\n\r\n      return updatedIncident;\r\n    } catch (error) {\r\n      this.logger.error('Failed to update incident', {\r\n        incidentId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Acknowledge incident\r\n   * @param id Incident ID\r\n   * @param userId User acknowledging the incident\r\n   * @returns Updated incident\r\n   */\r\n  async acknowledgeIncident(id: string, userId: string): Promise<Incident> {\r\n    try {\r\n      const incident = await this.findById(id);\r\n      if (!incident) {\r\n        throw new NotFoundException('Incident not found');\r\n      }\r\n\r\n      if (incident.status !== 'new') {\r\n        throw new BadRequestException('Incident has already been acknowledged');\r\n      }\r\n\r\n      incident.acknowledge(userId);\r\n      const updatedIncident = await this.incidentRepository.save(incident);\r\n\r\n      await this.addTimelineEntry(\r\n        id,\r\n        'acknowledged',\r\n        'Incident acknowledged and investigation started',\r\n        userId,\r\n        { timeToAcknowledge: incident.metrics?.timeToAcknowledge },\r\n      );\r\n\r\n      await this.notificationService.sendIncidentNotification(updatedIncident, 'acknowledged');\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'acknowledge',\r\n        'incident',\r\n        id,\r\n        { timeToAcknowledge: incident.metrics?.timeToAcknowledge },\r\n      );\r\n\r\n      this.logger.log('Incident acknowledged', {\r\n        incidentId: id,\r\n        userId,\r\n        timeToAcknowledge: incident.metrics?.timeToAcknowledge,\r\n      });\r\n\r\n      return updatedIncident;\r\n    } catch (error) {\r\n      this.logger.error('Failed to acknowledge incident', {\r\n        incidentId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Escalate incident\r\n   * @param id Incident ID\r\n   * @param reason Escalation reason\r\n   * @param userId User escalating the incident\r\n   * @returns Updated incident\r\n   */\r\n  async escalateIncident(id: string, reason: string, userId: string): Promise<Incident> {\r\n    try {\r\n      const incident = await this.findById(id);\r\n      if (!incident) {\r\n        throw new NotFoundException('Incident not found');\r\n      }\r\n\r\n      incident.escalate(reason, userId);\r\n      const updatedIncident = await this.incidentRepository.save(incident);\r\n\r\n      await this.addTimelineEntry(\r\n        id,\r\n        'escalated',\r\n        `Incident escalated: ${reason}`,\r\n        userId,\r\n        { escalationLevel: incident.metrics?.escalationLevel, reason },\r\n      );\r\n\r\n      await this.notificationService.sendIncidentNotification(updatedIncident, 'escalated');\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'escalate',\r\n        'incident',\r\n        id,\r\n        { reason, escalationLevel: incident.metrics?.escalationLevel },\r\n      );\r\n\r\n      this.logger.log('Incident escalated', {\r\n        incidentId: id,\r\n        userId,\r\n        reason,\r\n        escalationLevel: incident.metrics?.escalationLevel,\r\n      });\r\n\r\n      return updatedIncident;\r\n    } catch (error) {\r\n      this.logger.error('Failed to escalate incident', {\r\n        incidentId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Resolve incident\r\n   * @param id Incident ID\r\n   * @param resolution Resolution details\r\n   * @param userId User resolving the incident\r\n   * @returns Updated incident\r\n   */\r\n  async resolveIncident(\r\n    id: string,\r\n    resolution: string,\r\n    userId: string,\r\n  ): Promise<Incident> {\r\n    try {\r\n      const incident = await this.findById(id);\r\n      if (!incident) {\r\n        throw new NotFoundException('Incident not found');\r\n      }\r\n\r\n      incident.resolve(userId, resolution);\r\n      const updatedIncident = await this.incidentRepository.save(incident);\r\n\r\n      await this.addTimelineEntry(\r\n        id,\r\n        'resolved',\r\n        `Incident resolved: ${resolution}`,\r\n        userId,\r\n        { \r\n          timeToResolution: incident.metrics?.timeToResolution,\r\n          slaCompliant: incident.metrics?.slaCompliant,\r\n          resolution,\r\n        },\r\n      );\r\n\r\n      await this.notificationService.sendIncidentNotification(updatedIncident, 'resolved');\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'resolve',\r\n        'incident',\r\n        id,\r\n        {\r\n          resolution,\r\n          timeToResolution: incident.metrics?.timeToResolution,\r\n          slaCompliant: incident.metrics?.slaCompliant,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Incident resolved', {\r\n        incidentId: id,\r\n        userId,\r\n        timeToResolution: incident.metrics?.timeToResolution,\r\n        slaCompliant: incident.metrics?.slaCompliant,\r\n      });\r\n\r\n      return updatedIncident;\r\n    } catch (error) {\r\n      this.logger.error('Failed to resolve incident', {\r\n        incidentId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Close incident\r\n   * @param id Incident ID\r\n   * @param userId User closing the incident\r\n   * @returns Updated incident\r\n   */\r\n  async closeIncident(id: string, userId: string): Promise<Incident> {\r\n    try {\r\n      const incident = await this.findById(id);\r\n      if (!incident) {\r\n        throw new NotFoundException('Incident not found');\r\n      }\r\n\r\n      if (incident.status !== 'post_incident') {\r\n        throw new BadRequestException('Incident must be resolved before closing');\r\n      }\r\n\r\n      incident.close(userId);\r\n      const updatedIncident = await this.incidentRepository.save(incident);\r\n\r\n      await this.addTimelineEntry(\r\n        id,\r\n        'closed',\r\n        'Incident closed',\r\n        userId,\r\n      );\r\n\r\n      await this.notificationService.sendIncidentNotification(updatedIncident, 'closed');\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'close',\r\n        'incident',\r\n        id,\r\n      );\r\n\r\n      this.logger.log('Incident closed', {\r\n        incidentId: id,\r\n        userId,\r\n      });\r\n\r\n      return updatedIncident;\r\n    } catch (error) {\r\n      this.logger.error('Failed to close incident', {\r\n        incidentId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add team member to incident\r\n   * @param id Incident ID\r\n   * @param userId User to add\r\n   * @param role Role of the user\r\n   * @param permissions Permissions for the user\r\n   * @param addedBy User adding the team member\r\n   * @returns Updated incident\r\n   */\r\n  async addTeamMember(\r\n    id: string,\r\n    userId: string,\r\n    role: string,\r\n    permissions: string[],\r\n    addedBy: string,\r\n  ): Promise<Incident> {\r\n    try {\r\n      const incident = await this.findById(id);\r\n      if (!incident) {\r\n        throw new NotFoundException('Incident not found');\r\n      }\r\n\r\n      incident.addTeamMember(userId, role, permissions);\r\n      const updatedIncident = await this.incidentRepository.save(incident);\r\n\r\n      await this.addTimelineEntry(\r\n        id,\r\n        'team_member_added',\r\n        `Team member added: ${role}`,\r\n        addedBy,\r\n        { userId, role, permissions },\r\n      );\r\n\r\n      await this.auditService.logUserAction(\r\n        addedBy,\r\n        'add_team_member',\r\n        'incident',\r\n        id,\r\n        { userId, role, permissions },\r\n      );\r\n\r\n      this.logger.log('Team member added to incident', {\r\n        incidentId: id,\r\n        userId,\r\n        role,\r\n        addedBy,\r\n      });\r\n\r\n      return updatedIncident;\r\n    } catch (error) {\r\n      this.logger.error('Failed to add team member', {\r\n        incidentId: id,\r\n        error: error.message,\r\n        userId,\r\n        addedBy,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get incident statistics\r\n   * @returns Incident statistics\r\n   */\r\n  async getStatistics(): Promise<any> {\r\n    try {\r\n      const [\r\n        totalIncidents,\r\n        openIncidents,\r\n        criticalIncidents,\r\n        overdueIncidents,\r\n        incidentsByStatus,\r\n        incidentsBySeverity,\r\n        averageResolutionTime,\r\n        slaCompliance,\r\n      ] = await Promise.all([\r\n        this.incidentRepository.count(),\r\n        this.incidentRepository.count({ where: { status: In(['new', 'investigating', 'containment', 'eradication', 'recovery']) } }),\r\n        this.incidentRepository.count({ where: { severity: 'critical' } }),\r\n        this.getOverdueIncidentsCount(),\r\n        this.getIncidentsByStatus(),\r\n        this.getIncidentsBySeverity(),\r\n        this.getAverageResolutionTime(),\r\n        this.getSlaCompliance(),\r\n      ]);\r\n\r\n      return {\r\n        totalIncidents,\r\n        openIncidents,\r\n        criticalIncidents,\r\n        overdueIncidents,\r\n        byStatus: incidentsByStatus,\r\n        bySeverity: incidentsBySeverity,\r\n        performance: {\r\n          averageResolutionTime,\r\n          slaCompliance,\r\n        },\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to get incident statistics', {\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Apply response plan to incident\r\n   * @param incident Incident to apply plan to\r\n   */\r\n  private async applyResponsePlan(incident: Incident): Promise<void> {\r\n    try {\r\n      const responsePlans = await this.responsePlanRepository.find({\r\n        where: { isActive: true },\r\n      });\r\n\r\n      const applicablePlan = responsePlans.find(plan => \r\n        plan.isApplicableToIncident(incident)\r\n      );\r\n\r\n      if (applicablePlan) {\r\n        incident.responsePlanId = applicablePlan.id;\r\n        await this.incidentRepository.save(incident);\r\n\r\n        // Start response orchestration\r\n        await this.responseOrchestrationService.executeResponsePlan(incident.id, applicablePlan.id);\r\n\r\n        this.logger.log('Response plan applied to incident', {\r\n          incidentId: incident.id,\r\n          responsePlanId: applicablePlan.id,\r\n          planName: applicablePlan.name,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      this.logger.warn('Failed to apply response plan', {\r\n        incidentId: incident.id,\r\n        error: error.message,\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add timeline entry\r\n   * @param incidentId Incident ID\r\n   * @param eventType Event type\r\n   * @param description Event description\r\n   * @param userId User who performed the action\r\n   * @param metadata Additional metadata\r\n   */\r\n  private async addTimelineEntry(\r\n    incidentId: string,\r\n    eventType: string,\r\n    description: string,\r\n    userId: string,\r\n    metadata?: any,\r\n  ): Promise<void> {\r\n    try {\r\n      const timelineEntry = this.timelineRepository.create({\r\n        incidentId,\r\n        eventType,\r\n        description,\r\n        metadata,\r\n        createdBy: userId,\r\n      });\r\n\r\n      await this.timelineRepository.save(timelineEntry);\r\n    } catch (error) {\r\n      this.logger.warn('Failed to add timeline entry', {\r\n        incidentId,\r\n        eventType,\r\n        error: error.message,\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate default priority based on severity\r\n   * @param severity Incident severity\r\n   * @returns Default priority\r\n   */\r\n  private calculateDefaultPriority(severity: string): 'low' | 'medium' | 'high' | 'urgent' {\r\n    switch (severity) {\r\n      case 'critical':\r\n        return 'urgent';\r\n      case 'high':\r\n        return 'high';\r\n      case 'medium':\r\n        return 'medium';\r\n      case 'low':\r\n      default:\r\n        return 'low';\r\n    }\r\n  }\r\n\r\n  // Helper methods for statistics\r\n  private async getOverdueIncidentsCount(): Promise<number> {\r\n    // This would implement actual overdue calculation\r\n    // For now, return a placeholder\r\n    return 0;\r\n  }\r\n\r\n  private async getIncidentsByStatus(): Promise<Record<string, number>> {\r\n    const result = await this.incidentRepository\r\n      .createQueryBuilder('incident')\r\n      .select('incident.status', 'status')\r\n      .addSelect('COUNT(*)', 'count')\r\n      .groupBy('incident.status')\r\n      .getRawMany();\r\n\r\n    return result.reduce((acc, item) => {\r\n      acc[item.status] = parseInt(item.count);\r\n      return acc;\r\n    }, {});\r\n  }\r\n\r\n  private async getIncidentsBySeverity(): Promise<Record<string, number>> {\r\n    const result = await this.incidentRepository\r\n      .createQueryBuilder('incident')\r\n      .select('incident.severity', 'severity')\r\n      .addSelect('COUNT(*)', 'count')\r\n      .groupBy('incident.severity')\r\n      .getRawMany();\r\n\r\n    return result.reduce((acc, item) => {\r\n      acc[item.severity] = parseInt(item.count);\r\n      return acc;\r\n    }, {});\r\n  }\r\n\r\n  private async getAverageResolutionTime(): Promise<number> {\r\n    // This would implement actual calculation\r\n    // For now, return a placeholder\r\n    return 240; // 4 hours in minutes\r\n  }\r\n\r\n  private async getSlaCompliance(): Promise<number> {\r\n    // This would implement actual SLA compliance calculation\r\n    // For now, return a placeholder\r\n    return 85.5; // 85.5% compliance\r\n  }\r\n}\r\n"], "version": 3}