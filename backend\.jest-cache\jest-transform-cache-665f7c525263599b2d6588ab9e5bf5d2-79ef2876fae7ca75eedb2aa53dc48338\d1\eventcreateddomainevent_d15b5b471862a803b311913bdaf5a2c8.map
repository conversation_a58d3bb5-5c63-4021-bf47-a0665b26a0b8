{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\event-created.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAE5E,sEAA6D;AAsB7D;;;;;;;;;GASG;AACH,MAAa,uBAAwB,SAAQ,+BAAsC;IACjF,YACE,WAA2B,EAC3B,SAAgC,EAChC,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,CAAC,mCAAa,CAAC,IAAI,EAAE,mCAAa,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,eAAe;QAYb,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YACpC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YAC5B,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YACxC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YACrC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;SAC9B,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;SACrC,CAAC;IACJ,CAAC;CACF;AArHD,0DAqHC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\event-created.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventSource } from '../value-objects/event-metadata/event-source.value-object';\r\nimport { EventTimestamp } from '../value-objects/event-metadata/event-timestamp.value-object';\r\n\r\n/**\r\n * Event Created Domain Event Data\r\n */\r\nexport interface EventCreatedEventData {\r\n  /** Type of the created event */\r\n  eventType: EventType;\r\n  /** Severity of the created event */\r\n  severity: EventSeverity;\r\n  /** Source of the created event */\r\n  source: EventSource;\r\n  /** Timestamp of the created event */\r\n  timestamp: EventTimestamp;\r\n  /** Title of the created event */\r\n  title: string;\r\n  /** Risk score of the created event */\r\n  riskScore?: number;\r\n}\r\n\r\n/**\r\n * Event Created Domain Event\r\n * \r\n * Raised when a new security event is created in the system.\r\n * This event triggers various downstream processes including:\r\n * - Event processing pipeline initiation\r\n * - Notification systems\r\n * - Metrics collection\r\n * - Audit logging\r\n */\r\nexport class EventCreatedDomainEvent extends BaseDomainEvent<EventCreatedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: EventCreatedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the type of the created event\r\n   */\r\n  get eventType(): EventType {\r\n    return this.eventData.eventType;\r\n  }\r\n\r\n  /**\r\n   * Get the severity of the created event\r\n   */\r\n  get severity(): EventSeverity {\r\n    return this.eventData.severity;\r\n  }\r\n\r\n  /**\r\n   * Get the source of the created event\r\n   */\r\n  get source(): EventSource {\r\n    return this.eventData.source;\r\n  }\r\n\r\n  /**\r\n   * Get the timestamp of the created event\r\n   */\r\n  get timestamp(): EventTimestamp {\r\n    return this.eventData.timestamp;\r\n  }\r\n\r\n  /**\r\n   * Get the title of the created event\r\n   */\r\n  get title(): string {\r\n    return this.eventData.title;\r\n  }\r\n\r\n  /**\r\n   * Get the risk score of the created event\r\n   */\r\n  get riskScore(): number | undefined {\r\n    return this.eventData.riskScore;\r\n  }\r\n\r\n  /**\r\n   * Check if the created event is high severity\r\n   */\r\n  isHighSeverity(): boolean {\r\n    return [EventSeverity.HIGH, EventSeverity.CRITICAL].includes(this.severity);\r\n  }\r\n\r\n  /**\r\n   * Check if the created event is critical\r\n   */\r\n  isCritical(): boolean {\r\n    return this.severity === EventSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if the created event has high risk score\r\n   */\r\n  isHighRisk(): boolean {\r\n    return (this.riskScore || 0) >= 70;\r\n  }\r\n\r\n  /**\r\n   * Get event summary for handlers\r\n   */\r\n  getEventSummary(): {\r\n    eventId: string;\r\n    eventType: EventType;\r\n    severity: EventSeverity;\r\n    sourceType: string;\r\n    sourceIdentifier: string;\r\n    title: string;\r\n    riskScore?: number;\r\n    isHighSeverity: boolean;\r\n    isCritical: boolean;\r\n    isHighRisk: boolean;\r\n  } {\r\n    return {\r\n      eventId: this.aggregateId.toString(),\r\n      eventType: this.eventType,\r\n      severity: this.severity,\r\n      sourceType: this.source.type,\r\n      sourceIdentifier: this.source.identifier,\r\n      title: this.title,\r\n      riskScore: this.riskScore,\r\n      isHighSeverity: this.isHighSeverity(),\r\n      isCritical: this.isCritical(),\r\n      isHighRisk: this.isHighRisk(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventSummary: this.getEventSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}