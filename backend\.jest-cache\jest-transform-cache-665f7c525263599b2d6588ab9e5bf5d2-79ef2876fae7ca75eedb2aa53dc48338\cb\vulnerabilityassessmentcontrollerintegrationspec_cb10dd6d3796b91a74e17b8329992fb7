6ca76ce9a020c86aff52da2fd3994066
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const request = __importStar(require("supertest"));
const typeorm_2 = require("@nestjs/typeorm");
const vulnerability_assessment_controller_1 = require("../vulnerability-assessment.controller");
const vulnerability_assessment_service_1 = require("../../../application/services/vulnerability-assessment.service");
const vulnerability_entity_1 = require("../../../domain/entities/vulnerability.entity");
const vulnerability_assessment_entity_1 = require("../../../domain/entities/vulnerability-assessment.entity");
const vulnerability_scan_entity_1 = require("../../../domain/entities/vulnerability-scan.entity");
const asset_entity_1 = require("../../../../asset-management/domain/entities/asset.entity");
const logger_service_1 = require("../../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../../infrastructure/notification/notification.service");
const jwt_auth_guard_1 = require("../../../../../infrastructure/auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../../../../infrastructure/auth/guards/roles.guard");
describe('VulnerabilityAssessmentController (Integration)', () => {
    let app;
    let vulnerabilityRepository;
    let assessmentRepository;
    let scanRepository;
    let assetRepository;
    const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        roles: ['security_analyst'],
    };
    const mockVulnerability = {
        identifier: 'CVE-2023-1234',
        title: 'Test Vulnerability',
        description: 'Test vulnerability description',
        severity: 'high',
        cvssScore: 7.5,
        affectedProducts: [
            {
                vendor: 'Test Corp',
                product: 'Web Application',
                version: '1.0.0',
            },
        ],
        publishedDate: '2023-01-01T00:00:00Z',
        dataSource: {
            name: 'Test Source',
            type: 'internal',
            confidence: 'high',
            lastUpdated: '2023-01-01T00:00:00Z',
        },
    };
    const mockAsset = {
        name: 'Test Server',
        type: 'server',
        ipAddress: '*************',
        operatingSystem: 'Ubuntu 20.04',
        status: 'active',
        criticality: 'high',
        owner: 'IT Department',
        location: 'Data Center 1',
    };
    const mockAssessment = {
        assessmentType: 'manual',
        findings: 'Confirmed vulnerability through manual testing',
        recommendedActions: ['Apply security patch', 'Update configuration'],
        businessImpact: 'High impact on customer data security',
        technicalImpact: 'Potential data breach through SQL injection',
        assessedSeverity: 'high',
        assessedCvssScore: 7.5,
        confidenceLevel: 90,
    };
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                typeorm_1.TypeOrmModule.forRoot({
                    type: 'sqlite',
                    database: ':memory:',
                    entities: [vulnerability_entity_1.Vulnerability, vulnerability_assessment_entity_1.VulnerabilityAssessment, vulnerability_scan_entity_1.VulnerabilityScan, asset_entity_1.Asset],
                    synchronize: true,
                    logging: false,
                }),
                typeorm_1.TypeOrmModule.forFeature([
                    vulnerability_entity_1.Vulnerability,
                    vulnerability_assessment_entity_1.VulnerabilityAssessment,
                    vulnerability_scan_entity_1.VulnerabilityScan,
                    asset_entity_1.Asset,
                ]),
            ],
            controllers: [vulnerability_assessment_controller_1.VulnerabilityAssessmentController],
            providers: [
                vulnerability_assessment_service_1.VulnerabilityAssessmentService,
                {
                    provide: logger_service_1.LoggerService,
                    useValue: {
                        debug: jest.fn(),
                        log: jest.fn(),
                        warn: jest.fn(),
                        error: jest.fn(),
                    },
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: {
                        logUserAction: jest.fn(),
                    },
                },
                {
                    provide: notification_service_1.NotificationService,
                    useValue: {
                        sendAssessmentNotification: jest.fn(),
                    },
                },
            ],
        })
            .overrideGuard(jwt_auth_guard_1.JwtAuthGuard)
            .useValue({
            canActivate: (context) => {
                const request = context.switchToHttp().getRequest();
                request.user = mockUser;
                return true;
            },
        })
            .overrideGuard(roles_guard_1.RolesGuard)
            .useValue({
            canActivate: () => true,
        })
            .compile();
        app = moduleFixture.createNestApplication();
        await app.init();
        vulnerabilityRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(vulnerability_entity_1.Vulnerability));
        assessmentRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(vulnerability_assessment_entity_1.VulnerabilityAssessment));
        scanRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(vulnerability_scan_entity_1.VulnerabilityScan));
        assetRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(asset_entity_1.Asset));
    });
    afterAll(async () => {
        await app.close();
    });
    beforeEach(async () => {
        // Clean up database before each test
        await assessmentRepository.clear();
        await vulnerabilityRepository.clear();
        await scanRepository.clear();
        await assetRepository.clear();
    });
    describe('POST /api/vulnerability-assessments', () => {
        it('should create a new assessment', async () => {
            // Create prerequisite vulnerability
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            // Create prerequisite asset
            const asset = assetRepository.create(mockAsset);
            const savedAsset = await assetRepository.save(asset);
            const assessmentData = {
                ...mockAssessment,
                vulnerabilityId: savedVuln.id,
                assetId: savedAsset.id,
            };
            const response = await request(app.getHttpServer())
                .post('/api/vulnerability-assessments')
                .send(assessmentData)
                .expect(201);
            expect(response.body).toHaveProperty('id');
            expect(response.body.vulnerabilityId).toBe(savedVuln.id);
            expect(response.body.assetId).toBe(savedAsset.id);
            expect(response.body.findings).toBe(assessmentData.findings);
            expect(response.body.status).toBe('pending');
            // Verify it was saved to database
            const savedAssessment = await assessmentRepository.findOne({
                where: { vulnerabilityId: savedVuln.id },
            });
            expect(savedAssessment).toBeDefined();
            expect(savedAssessment.findings).toBe(assessmentData.findings);
        });
        it('should return 400 for invalid assessment data', async () => {
            const invalidAssessment = {
                ...mockAssessment,
                vulnerabilityId: 'invalid-uuid',
                assessmentType: 'invalid-type',
                confidenceLevel: 150, // Invalid: > 100
            };
            const response = await request(app.getHttpServer())
                .post('/api/vulnerability-assessments')
                .send(invalidAssessment)
                .expect(400);
            expect(response.body.message).toContain('validation failed');
        });
        it('should return 404 for non-existent vulnerability', async () => {
            const assessmentData = {
                ...mockAssessment,
                vulnerabilityId: '123e4567-e89b-12d3-a456-************',
            };
            const response = await request(app.getHttpServer())
                .post('/api/vulnerability-assessments')
                .send(assessmentData)
                .expect(404);
            expect(response.body.message).toContain('Vulnerability not found');
        });
    });
    describe('GET /api/vulnerability-assessments', () => {
        it('should return paginated assessments', async () => {
            // Create test data
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            const assessments = [];
            for (let i = 1; i <= 15; i++) {
                assessments.push(assessmentRepository.create({
                    vulnerabilityId: savedVuln.id,
                    assessmentType: 'manual',
                    status: 'pending',
                    findings: `Test findings ${i}`,
                    assessedBy: mockUser.id,
                    assessedAt: new Date(),
                }));
            }
            await assessmentRepository.save(assessments);
            const response = await request(app.getHttpServer())
                .get('/api/vulnerability-assessments')
                .query({ page: 1, limit: 10 })
                .expect(200);
            expect(response.body).toHaveProperty('assessments');
            expect(response.body).toHaveProperty('total');
            expect(response.body).toHaveProperty('page');
            expect(response.body).toHaveProperty('totalPages');
            expect(response.body.assessments).toHaveLength(10);
            expect(response.body.total).toBe(15);
            expect(response.body.totalPages).toBe(2);
        });
        it('should filter assessments by status', async () => {
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            const assessment1 = assessmentRepository.create({
                vulnerabilityId: savedVuln.id,
                assessmentType: 'manual',
                status: 'pending',
                findings: 'Pending assessment',
                assessedBy: mockUser.id,
                assessedAt: new Date(),
            });
            const assessment2 = assessmentRepository.create({
                vulnerabilityId: savedVuln.id,
                assessmentType: 'manual',
                status: 'completed',
                findings: 'Completed assessment',
                assessedBy: mockUser.id,
                assessedAt: new Date(),
            });
            await assessmentRepository.save([assessment1, assessment2]);
            const response = await request(app.getHttpServer())
                .get('/api/vulnerability-assessments')
                .query({ statuses: 'pending' })
                .expect(200);
            expect(response.body.assessments).toHaveLength(1);
            expect(response.body.assessments[0].status).toBe('pending');
        });
    });
    describe('GET /api/vulnerability-assessments/:id', () => {
        it('should return assessment details', async () => {
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            const assessment = assessmentRepository.create({
                vulnerabilityId: savedVuln.id,
                assessmentType: 'manual',
                status: 'pending',
                findings: 'Test findings',
                assessedBy: mockUser.id,
                assessedAt: new Date(),
            });
            const savedAssessment = await assessmentRepository.save(assessment);
            const response = await request(app.getHttpServer())
                .get(`/api/vulnerability-assessments/${savedAssessment.id}`)
                .expect(200);
            expect(response.body).toHaveProperty('id');
            expect(response.body.vulnerabilityId).toBe(savedVuln.id);
            expect(response.body.findings).toBe('Test findings');
        });
        it('should return 404 for non-existent assessment', async () => {
            const response = await request(app.getHttpServer())
                .get('/api/vulnerability-assessments/123e4567-e89b-12d3-a456-************')
                .expect(404);
            expect(response.body.message).toContain('Assessment not found');
        });
    });
    describe('PUT /api/vulnerability-assessments/:id', () => {
        it('should update an existing assessment', async () => {
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            const assessment = assessmentRepository.create({
                vulnerabilityId: savedVuln.id,
                assessmentType: 'manual',
                status: 'pending',
                findings: 'Original findings',
                assessedBy: mockUser.id,
                assessedAt: new Date(),
            });
            const savedAssessment = await assessmentRepository.save(assessment);
            const updates = {
                findings: 'Updated findings after further analysis',
                assessedSeverity: 'critical',
                confidenceLevel: 95,
            };
            const response = await request(app.getHttpServer())
                .put(`/api/vulnerability-assessments/${savedAssessment.id}`)
                .send(updates)
                .expect(200);
            expect(response.body.findings).toBe(updates.findings);
            expect(response.body.assessedSeverity).toBe(updates.assessedSeverity);
            expect(response.body.confidenceLevel).toBe(updates.confidenceLevel);
            // Verify database was updated
            const updatedAssessment = await assessmentRepository.findOne({
                where: { id: savedAssessment.id },
            });
            expect(updatedAssessment.findings).toBe(updates.findings);
        });
    });
    describe('POST /api/vulnerability-assessments/:id/complete', () => {
        it('should complete an assessment', async () => {
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            const assessment = assessmentRepository.create({
                vulnerabilityId: savedVuln.id,
                assessmentType: 'manual',
                status: 'in_progress',
                findings: 'Assessment in progress',
                assessedBy: mockUser.id,
                assessedAt: new Date(),
            });
            const savedAssessment = await assessmentRepository.save(assessment);
            const response = await request(app.getHttpServer())
                .post(`/api/vulnerability-assessments/${savedAssessment.id}/complete`)
                .expect(200);
            expect(response.body.status).toBe('completed');
            // Verify database was updated
            const completedAssessment = await assessmentRepository.findOne({
                where: { id: savedAssessment.id },
            });
            expect(completedAssessment.status).toBe('completed');
        });
        it('should return 404 for non-existent assessment', async () => {
            const response = await request(app.getHttpServer())
                .post('/api/vulnerability-assessments/123e4567-e89b-12d3-a456-************/complete')
                .expect(404);
            expect(response.body.message).toContain('Assessment not found');
        });
    });
    describe('POST /api/vulnerability-assessments/:id/review', () => {
        it('should review and approve an assessment', async () => {
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            const assessment = assessmentRepository.create({
                vulnerabilityId: savedVuln.id,
                assessmentType: 'manual',
                status: 'completed',
                findings: 'Completed assessment',
                assessedBy: mockUser.id,
                assessedAt: new Date(),
            });
            const savedAssessment = await assessmentRepository.save(assessment);
            const reviewData = {
                approved: true,
                comments: 'Assessment approved after review',
            };
            const response = await request(app.getHttpServer())
                .post(`/api/vulnerability-assessments/${savedAssessment.id}/review`)
                .send(reviewData)
                .expect(200);
            expect(response.body.reviewComments).toBe(reviewData.comments);
            expect(response.body.reviewedBy).toBe(mockUser.id);
            // Verify database was updated
            const reviewedAssessment = await assessmentRepository.findOne({
                where: { id: savedAssessment.id },
            });
            expect(reviewedAssessment.reviewComments).toBe(reviewData.comments);
            expect(reviewedAssessment.reviewedBy).toBe(mockUser.id);
        });
        it('should review and reject an assessment', async () => {
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            const assessment = assessmentRepository.create({
                vulnerabilityId: savedVuln.id,
                assessmentType: 'manual',
                status: 'completed',
                findings: 'Completed assessment',
                assessedBy: mockUser.id,
                assessedAt: new Date(),
            });
            const savedAssessment = await assessmentRepository.save(assessment);
            const reviewData = {
                approved: false,
                comments: 'Assessment needs more detailed analysis',
            };
            const response = await request(app.getHttpServer())
                .post(`/api/vulnerability-assessments/${savedAssessment.id}/review`)
                .send(reviewData)
                .expect(200);
            expect(response.body.status).toBe('rejected');
            expect(response.body.reviewComments).toBe(reviewData.comments);
        });
    });
    describe('DELETE /api/vulnerability-assessments/:id', () => {
        it('should delete an assessment', async () => {
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            const assessment = assessmentRepository.create({
                vulnerabilityId: savedVuln.id,
                assessmentType: 'manual',
                status: 'pending',
                findings: 'Test assessment',
                assessedBy: mockUser.id,
                assessedAt: new Date(),
            });
            const savedAssessment = await assessmentRepository.save(assessment);
            await request(app.getHttpServer())
                .delete(`/api/vulnerability-assessments/${savedAssessment.id}`)
                .expect(204);
            // Verify it was deleted from database
            const deletedAssessment = await assessmentRepository.findOne({
                where: { id: savedAssessment.id },
            });
            expect(deletedAssessment).toBeNull();
        });
        it('should return 404 for non-existent assessment', async () => {
            const response = await request(app.getHttpServer())
                .delete('/api/vulnerability-assessments/123e4567-e89b-12d3-a456-************')
                .expect(404);
            expect(response.body.message).toContain('Assessment not found');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************