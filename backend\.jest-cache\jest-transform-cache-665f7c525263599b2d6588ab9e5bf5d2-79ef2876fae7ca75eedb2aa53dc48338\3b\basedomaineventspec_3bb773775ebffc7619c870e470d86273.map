{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-domain-event.spec.ts", "mappings": ";;AAAA,sEAAiE;AACjE,qGAAmF;AAQnF,MAAM,eAAgB,SAAQ,mCAA8B;IAC1D,YACE,WAA2B,EAC3B,IAAmB,EACnB,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACpC,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAChC,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;CACF;AAED,MAAM,gBAAiB,SAAQ,mCAAmC;IAChE,YAAY,WAA2B,EAAE,MAAc;QACrD,KAAK,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;IACjC,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B,CAAC;CACF;AAED,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,IAAI,WAA2B,CAAC;IAChC,IAAI,SAAwB,CAAC;IAE7B,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;QACxC,SAAS,GAAG,EAAE,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,8CAAc,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,OAAO,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC1C,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;YAEvE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;YAClC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC1D,MAAM,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;YAEjC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC;YACpF,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,mBAAmB,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACpD,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;YAE1E,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC;YAE/E,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,aAAa,GAAG,iBAAiB,CAAC;YACxC,MAAM,WAAW,GAAG,eAAe,CAAC;YACpC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE;gBACxD,aAAa;gBACb,WAAW;aACZ,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,QAAQ,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;YACtD,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAExE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,QAAQ,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;YACpC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAExE,qEAAqE;YACrE,2FAA2F;YAC3F,MAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC;YACpC,YAAY,CAAC,MAAM,GAAG,UAAU,CAAC;YAEjC,wDAAwD;YACxD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,aAAa,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,QAAQ,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;YACpC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAExE,MAAM,iBAAiB,GAAG,KAAK,CAAC,QAAQ,CAAC;YACzC,MAAM,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC5C,MAAM,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEvC,MAAM,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;YAClC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACzB,MAAM,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;YAEjC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,KAAK,CAAC,YAAa,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC;YACvF,MAAM,CAAC,KAAK,CAAC,YAAa,CAAC,OAAO,EAAE,CAAC,CAAC,mBAAmB,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAEzB,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,OAAO,CAC5C,SAAS,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,8BAA8B,CAChE,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC9D,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;YAEpF,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAC3B,MAAM,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB;YACjE,MAAM,CAAC,GAAG,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC9D,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;YAEpF,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;YAC7C,MAAM,CAAC,YAAY,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,YAAY,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,kBAAkB;YAClE,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;YAEpF,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;YAC7C,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,eAAe;YAC/D,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,iBAAiB;YAE/D,MAAM,WAAW,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC;YAC5F,MAAM,QAAQ,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;YAEtF,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,0BAA0B;YACzE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE;gBACxD,QAAQ,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE;aACjC,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;YAEnF,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBACrC,MAAM,EAAE,UAAU,EAAE,kBAAkB;gBACtC,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/D,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC1D,MAAM,aAAa,GAAG,oBAAoB,CAAC;YAE3C,MAAM,aAAa,GAAG,KAAK,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAE7D,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACxD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC1D,MAAM,WAAW,GAAG,kBAAkB,CAAC;YAEvC,MAAM,aAAa,GAAG,KAAK,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAEzD,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,OAAO,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC1C,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;YACxE,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;YAExE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC3D,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE3D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mBAAmB,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACpD,MAAM,aAAa,GAAG,iBAAiB,CAAC;YACxC,MAAM,WAAW,GAAG,eAAe,CAAC;YACpC,MAAM,QAAQ,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;YAEpC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE;gBACxD,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,QAAQ;gBACR,YAAY,EAAE,CAAC;aAChB,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAE5B,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBACnB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE;gBACjC,SAAS,EAAE,iBAAiB;gBAC5B,UAAU,EAAE,UAAU,CAAC,WAAW,EAAE;gBACpC,WAAW,EAAE,WAAW,CAAC,QAAQ,EAAE;gBACnC,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,SAAS;gBACpB,YAAY,EAAE,KAAK;gBACnB,YAAY,EAAE,SAAS;gBACvB,aAAa;gBACb,WAAW;gBACX,QAAQ;aACT,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC1D,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAEzB,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAE5B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,aAAa,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE;gBAChE,aAAa,EAAE,iBAAiB;gBAChC,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;aAC7B,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,iBAAiB,GAAG,mCAAe,CAAC,QAAQ,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;YAE1E,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3E,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnF,MAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YACrE,MAAM,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YAC1E,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,CAAC,GAAG,EAAE;gBACT,KAAK,CAAC,SAAiB,CAAC,OAAO,GAAG,UAAU,CAAC;YAChD,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,QAAQ,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;YACpC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAExE,gFAAgF;YAChF,MAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC;YACpC,MAAM,CAAC,GAAG,EAAE;gBACV,YAAY,CAAC,MAAM,GAAG,UAAU,CAAC;YACnC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,4DAA4D;YAC5D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,CAAC,GAAG,EAAE;gBACT,KAAa,CAAC,OAAO,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrD,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YAEb,MAAM,CAAC,GAAG,EAAE;gBACT,KAAa,CAAC,WAAW,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACzD,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YAEb,MAAM,CAAC,GAAG,EAAE;gBACT,KAAa,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YACzC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,SAAS,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC9D,MAAM,YAAY,GAAG,IAAI,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAEjE,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,mCAAe,CAAC,CAAC;YAClD,MAAM,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,mCAAe,CAAC,CAAC;YAErD,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACpD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,SAAS,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC9D,MAAM,YAAY,GAAG,IAAI,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAEjE,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,cAAe,SAAQ,mCAAmB;gBAC9C,YAAY,WAA2B;oBACrC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;gBACzB,CAAC;aACF;YAED,MAAM,KAAK,GAAG,IAAI,cAAc,CAAC,WAAW,CAAC,CAAC;YAE9C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YAMjD,MAAM,aAAc,SAAQ,mCAAkC;gBAC5D,YAAY,WAA2B,EAAE,IAAuB;oBAC9D,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBAC3B,CAAC;aACF;YAED,MAAM,KAAK,GAAG,IAAI,aAAa,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAE9D,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;YACzC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YAYjD,MAAM,YAAa,SAAQ,mCAAiC;gBAC1D,YAAY,WAA2B,EAAE,IAAsB;oBAC7D,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBAC3B,CAAC;aACF;YAED,MAAM,WAAW,GAAqB;gBACpC,IAAI,EAAE;oBACJ,EAAE,EAAE,UAAU;oBACd,OAAO,EAAE;wBACP,IAAI,EAAE,UAAU;wBAChB,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE;qBACjD;iBACF;gBACD,OAAO,EAAE,CAAC,OAAO,EAAE,gBAAgB,CAAC;aACrC,CAAC;YAEF,MAAM,KAAK,GAAG,IAAI,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAEzD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACrD,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,CAAC;YAEvF,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,oBAAoB;YACxE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,qBAAqB;YACrE,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC;YAEtF,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-domain-event.spec.ts"], "sourcesContent": ["import { BaseDomainEvent } from '../../domain/base-domain-event';\r\nimport { UniqueEntityId } from '../../value-objects/unique-entity-id.value-object';\r\n\r\n// Test domain event implementations\r\ninterface TestEventData {\r\n  message: string;\r\n  value: number;\r\n}\r\n\r\nclass TestDomainEvent extends BaseDomainEvent<TestEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    data: TestEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, data, options);\r\n  }\r\n\r\n  get message(): string {\r\n    return this.eventData.message;\r\n  }\r\n\r\n  get value(): number {\r\n    return this.eventData.value;\r\n  }\r\n}\r\n\r\nclass AnotherTestEvent extends BaseDomainEvent<{ status: string }> {\r\n  constructor(aggregateId: UniqueEntityId, status: string) {\r\n    super(aggregateId, { status });\r\n  }\r\n\r\n  get status(): string {\r\n    return this.eventData.status;\r\n  }\r\n}\r\n\r\ndescribe('BaseDomainEvent', () => {\r\n  let aggregateId: UniqueEntityId;\r\n  let eventData: TestEventData;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.generate();\r\n    eventData = { message: 'Test message', value: 42 };\r\n  });\r\n\r\n  describe('construction', () => {\r\n    it('should create event with required parameters', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.aggregateId.equals(aggregateId)).toBe(true);\r\n      expect(event.eventData).toEqual(eventData);\r\n      expect(event.message).toBe('Test message');\r\n      expect(event.value).toBe(42);\r\n    });\r\n\r\n    it('should generate event ID if not provided', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.eventId).toBeInstanceOf(UniqueEntityId);\r\n      expect(event.eventId.value).toBeDefined();\r\n    });\r\n\r\n    it('should use provided event ID', () => {\r\n      const eventId = UniqueEntityId.generate();\r\n      const event = new TestDomainEvent(aggregateId, eventData, { eventId });\r\n\r\n      expect(event.eventId.equals(eventId)).toBe(true);\r\n    });\r\n\r\n    it('should set occurred time to now if not provided', () => {\r\n      const beforeCreation = new Date();\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n      const afterCreation = new Date();\r\n\r\n      expect(event.occurredOn.getTime()).toBeGreaterThanOrEqual(beforeCreation.getTime());\r\n      expect(event.occurredOn.getTime()).toBeLessThanOrEqual(afterCreation.getTime());\r\n    });\r\n\r\n    it('should use provided occurred time', () => {\r\n      const occurredOn = new Date('2023-01-01T00:00:00Z');\r\n      const event = new TestDomainEvent(aggregateId, eventData, { occurredOn });\r\n\r\n      expect(event.occurredOn).toEqual(occurredOn);\r\n    });\r\n\r\n    it('should default event version to 1', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.eventVersion).toBe(1);\r\n    });\r\n\r\n    it('should use provided event version', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData, { eventVersion: 2 });\r\n\r\n      expect(event.eventVersion).toBe(2);\r\n    });\r\n\r\n    it('should set correlation and causation IDs if provided', () => {\r\n      const correlationId = 'correlation-123';\r\n      const causationId = 'causation-456';\r\n      const event = new TestDomainEvent(aggregateId, eventData, {\r\n        correlationId,\r\n        causationId\r\n      });\r\n\r\n      expect(event.correlationId).toBe(correlationId);\r\n      expect(event.causationId).toBe(causationId);\r\n    });\r\n\r\n    it('should set metadata if provided', () => {\r\n      const metadata = { source: 'test', priority: 'high' };\r\n      const event = new TestDomainEvent(aggregateId, eventData, { metadata });\r\n\r\n      expect(event.metadata).toEqual(metadata);\r\n    });\r\n\r\n    it('should freeze event data for immutability', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      expect(Object.isFrozen(event.eventData)).toBe(true);\r\n    });\r\n\r\n    it('should freeze metadata for immutability', () => {\r\n      const metadata = { source: 'test' };\r\n      const event = new TestDomainEvent(aggregateId, eventData, { metadata });\r\n\r\n      // The internal metadata should be frozen (we can't test it directly)\r\n      // But we can test that the metadata getter returns a copy that doesn't affect the original\r\n      const metadataCopy = event.metadata;\r\n      metadataCopy.source = 'modified';\r\n      \r\n      // The original event's metadata should remain unchanged\r\n      expect(event.metadata.source).toBe('test');\r\n    });\r\n  });\r\n\r\n  describe('event properties', () => {\r\n    it('should provide event type name', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.eventType).toBe('TestDomainEvent');\r\n    });\r\n\r\n    it('should start as not dispatched', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.isDispatched).toBe(false);\r\n      expect(event.dispatchedAt).toBeUndefined();\r\n    });\r\n\r\n    it('should provide empty metadata by default', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.metadata).toEqual({});\r\n    });\r\n\r\n    it('should return copy of metadata', () => {\r\n      const metadata = { source: 'test' };\r\n      const event = new TestDomainEvent(aggregateId, eventData, { metadata });\r\n\r\n      const retrievedMetadata = event.metadata;\r\n      expect(retrievedMetadata).toEqual(metadata);\r\n      expect(retrievedMetadata).not.toBe(metadata); // Different reference\r\n    });\r\n  });\r\n\r\n  describe('dispatch management', () => {\r\n    it('should mark event as dispatched', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.isDispatched).toBe(false);\r\n\r\n      const beforeDispatch = new Date();\r\n      event.markAsDispatched();\r\n      const afterDispatch = new Date();\r\n\r\n      expect(event.isDispatched).toBe(true);\r\n      expect(event.dispatchedAt).toBeDefined();\r\n      expect(event.dispatchedAt!.getTime()).toBeGreaterThanOrEqual(beforeDispatch.getTime());\r\n      expect(event.dispatchedAt!.getTime()).toBeLessThanOrEqual(afterDispatch.getTime());\r\n    });\r\n\r\n    it('should throw error when marking already dispatched event', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      event.markAsDispatched();\r\n\r\n      expect(() => event.markAsDispatched()).toThrow(\r\n        `Event ${event.eventId.toString()} has already been dispatched`\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('age calculations', () => {\r\n    it('should calculate age in milliseconds', () => {\r\n      const pastTime = new Date(Date.now() - 5000); // 5 seconds ago\r\n      const event = new TestDomainEvent(aggregateId, eventData, { occurredOn: pastTime });\r\n\r\n      const age = event.getAge();\r\n      expect(age).toBeGreaterThanOrEqual(4900); // Allow some tolerance\r\n      expect(age).toBeLessThanOrEqual(5100);\r\n    });\r\n\r\n    it('should calculate age in seconds', () => {\r\n      const pastTime = new Date(Date.now() - 5000); // 5 seconds ago\r\n      const event = new TestDomainEvent(aggregateId, eventData, { occurredOn: pastTime });\r\n\r\n      const ageInSeconds = event.getAgeInSeconds();\r\n      expect(ageInSeconds).toBeGreaterThanOrEqual(4);\r\n      expect(ageInSeconds).toBeLessThanOrEqual(5);\r\n    });\r\n\r\n    it('should calculate age in minutes', () => {\r\n      const pastTime = new Date(Date.now() - 150000); // 2.5 minutes ago\r\n      const event = new TestDomainEvent(aggregateId, eventData, { occurredOn: pastTime });\r\n\r\n      const ageInMinutes = event.getAgeInMinutes();\r\n      expect(ageInMinutes).toBe(2);\r\n    });\r\n\r\n    it('should check if event is stale', () => {\r\n      const recentTime = new Date(Date.now() - 1000); // 1 second ago\r\n      const oldTime = new Date(Date.now() - 10000); // 10 seconds ago\r\n\r\n      const recentEvent = new TestDomainEvent(aggregateId, eventData, { occurredOn: recentTime });\r\n      const oldEvent = new TestDomainEvent(aggregateId, eventData, { occurredOn: oldTime });\r\n\r\n      expect(recentEvent.isStale(5000)).toBe(false); // Not stale (< 5 seconds)\r\n      expect(oldEvent.isStale(5000)).toBe(true); // Stale (> 5 seconds)\r\n    });\r\n  });\r\n\r\n  describe('event modification', () => {\r\n    it('should create event with additional metadata', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData, {\r\n        metadata: { source: 'original' }\r\n      });\r\n\r\n      const modifiedEvent = event.withMetadata({ priority: 'high', source: 'modified' });\r\n\r\n      expect(modifiedEvent).not.toBe(event);\r\n      expect(modifiedEvent.metadata).toEqual({\r\n        source: 'modified', // Should override\r\n        priority: 'high'\r\n      });\r\n      expect(modifiedEvent.eventId.equals(event.eventId)).toBe(true);\r\n      expect(modifiedEvent.aggregateId.equals(event.aggregateId)).toBe(true);\r\n    });\r\n\r\n    it('should create event with correlation ID', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n      const correlationId = 'new-correlation-id';\r\n\r\n      const modifiedEvent = event.withCorrelationId(correlationId);\r\n\r\n      expect(modifiedEvent).not.toBe(event);\r\n      expect(modifiedEvent.correlationId).toBe(correlationId);\r\n      expect(modifiedEvent.eventId.equals(event.eventId)).toBe(true);\r\n    });\r\n\r\n    it('should create event with causation ID', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n      const causationId = 'new-causation-id';\r\n\r\n      const modifiedEvent = event.withCausationId(causationId);\r\n\r\n      expect(modifiedEvent).not.toBe(event);\r\n      expect(modifiedEvent.causationId).toBe(causationId);\r\n      expect(modifiedEvent.eventId.equals(event.eventId)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('equality comparison', () => {\r\n    it('should be equal to itself', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.equals(event)).toBe(true);\r\n    });\r\n\r\n    it('should be equal to event with same event ID', () => {\r\n      const eventId = UniqueEntityId.generate();\r\n      const event1 = new TestDomainEvent(aggregateId, eventData, { eventId });\r\n      const event2 = new TestDomainEvent(aggregateId, eventData, { eventId });\r\n\r\n      expect(event1.equals(event2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to event with different event ID', () => {\r\n      const event1 = new TestDomainEvent(aggregateId, eventData);\r\n      const event2 = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event1.equals(event2)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to null or undefined', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.equals(null)).toBe(false);\r\n      expect(event.equals(undefined)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('string representation', () => {\r\n    it('should provide string representation', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      const stringRep = event.toString();\r\n      expect(stringRep).toBe(`TestDomainEvent(${event.eventId.toString()})`);\r\n    });\r\n  });\r\n\r\n  describe('JSON serialization', () => {\r\n    it('should serialize to JSON', () => {\r\n      const occurredOn = new Date('2023-01-01T00:00:00Z');\r\n      const correlationId = 'correlation-123';\r\n      const causationId = 'causation-456';\r\n      const metadata = { source: 'test' };\r\n\r\n      const event = new TestDomainEvent(aggregateId, eventData, {\r\n        occurredOn,\r\n        correlationId,\r\n        causationId,\r\n        metadata,\r\n        eventVersion: 2\r\n      });\r\n\r\n      const json = event.toJSON();\r\n\r\n      expect(json).toEqual({\r\n        eventId: event.eventId.toString(),\r\n        eventType: 'TestDomainEvent',\r\n        occurredOn: occurredOn.toISOString(),\r\n        aggregateId: aggregateId.toString(),\r\n        eventVersion: 2,\r\n        eventData: eventData,\r\n        isDispatched: false,\r\n        dispatchedAt: undefined,\r\n        correlationId,\r\n        causationId,\r\n        metadata\r\n      });\r\n    });\r\n\r\n    it('should include dispatch information in JSON', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n      event.markAsDispatched();\r\n\r\n      const json = event.toJSON();\r\n\r\n      expect(json.isDispatched).toBe(true);\r\n      expect(json.dispatchedAt).toBeDefined();\r\n    });\r\n\r\n    it('should deserialize from JSON', () => {\r\n      const originalEvent = new TestDomainEvent(aggregateId, eventData, {\r\n        correlationId: 'correlation-123',\r\n        metadata: { source: 'test' }\r\n      });\r\n\r\n      const json = originalEvent.toJSON();\r\n      const deserializedEvent = BaseDomainEvent.fromJSON(json, TestDomainEvent);\r\n\r\n      expect(deserializedEvent.eventId.equals(originalEvent.eventId)).toBe(true);\r\n      expect(deserializedEvent.aggregateId.equals(originalEvent.aggregateId)).toBe(true);\r\n      expect(deserializedEvent.eventData).toEqual(originalEvent.eventData);\r\n      expect(deserializedEvent.correlationId).toBe(originalEvent.correlationId);\r\n      expect(deserializedEvent.metadata).toEqual(originalEvent.metadata);\r\n    });\r\n  });\r\n\r\n  describe('immutability', () => {\r\n    it('should not allow modification of event data', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      expect(() => {\r\n        (event.eventData as any).message = 'modified';\r\n      }).toThrow();\r\n    });\r\n\r\n    it('should not allow modification of metadata', () => {\r\n      const metadata = { source: 'test' };\r\n      const event = new TestDomainEvent(aggregateId, eventData, { metadata });\r\n\r\n      // The metadata getter returns a copy, so modifying it won't affect the original\r\n      const metadataCopy = event.metadata;\r\n      expect(() => {\r\n        metadataCopy.source = 'modified';\r\n      }).not.toThrow();\r\n      \r\n      // But the original event's metadata should remain unchanged\r\n      expect(event.metadata.source).toBe('test');\r\n    });\r\n\r\n    it('should not allow modification of core properties', () => {\r\n      const event = new TestDomainEvent(aggregateId, eventData);\r\n\r\n      expect(() => {\r\n        (event as any).eventId = UniqueEntityId.generate();\r\n      }).toThrow();\r\n\r\n      expect(() => {\r\n        (event as any).aggregateId = UniqueEntityId.generate();\r\n      }).toThrow();\r\n\r\n      expect(() => {\r\n        (event as any).occurredOn = new Date();\r\n      }).toThrow();\r\n    });\r\n  });\r\n\r\n  describe('inheritance', () => {\r\n    it('should support inheritance with different event data types', () => {\r\n      const testEvent = new TestDomainEvent(aggregateId, eventData);\r\n      const anotherEvent = new AnotherTestEvent(aggregateId, 'active');\r\n\r\n      expect(testEvent).toBeInstanceOf(TestDomainEvent);\r\n      expect(testEvent).toBeInstanceOf(BaseDomainEvent);\r\n      expect(anotherEvent).toBeInstanceOf(AnotherTestEvent);\r\n      expect(anotherEvent).toBeInstanceOf(BaseDomainEvent);\r\n\r\n      expect(testEvent.eventType).toBe('TestDomainEvent');\r\n      expect(anotherEvent.eventType).toBe('AnotherTestEvent');\r\n    });\r\n\r\n    it('should maintain type safety for event data', () => {\r\n      const testEvent = new TestDomainEvent(aggregateId, eventData);\r\n      const anotherEvent = new AnotherTestEvent(aggregateId, 'active');\r\n\r\n      expect(testEvent.message).toBe('Test message');\r\n      expect(testEvent.value).toBe(42);\r\n      expect(anotherEvent.status).toBe('active');\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle empty event data', () => {\r\n      class EmptyDataEvent extends BaseDomainEvent<{}> {\r\n        constructor(aggregateId: UniqueEntityId) {\r\n          super(aggregateId, {});\r\n        }\r\n      }\r\n\r\n      const event = new EmptyDataEvent(aggregateId);\r\n\r\n      expect(event.eventData).toEqual({});\r\n      expect(event.eventType).toBe('EmptyDataEvent');\r\n    });\r\n\r\n    it('should handle null values in event data', () => {\r\n      interface NullableEventData {\r\n        value: string | null;\r\n        optional?: number;\r\n      }\r\n\r\n      class NullableEvent extends BaseDomainEvent<NullableEventData> {\r\n        constructor(aggregateId: UniqueEntityId, data: NullableEventData) {\r\n          super(aggregateId, data);\r\n        }\r\n      }\r\n\r\n      const event = new NullableEvent(aggregateId, { value: null });\r\n\r\n      expect(event.eventData.value).toBeNull();\r\n      expect(event.eventData.optional).toBeUndefined();\r\n    });\r\n\r\n    it('should handle complex nested event data', () => {\r\n      interface ComplexEventData {\r\n        user: {\r\n          id: string;\r\n          profile: {\r\n            name: string;\r\n            settings: Record<string, any>;\r\n          };\r\n        };\r\n        actions: string[];\r\n      }\r\n\r\n      class ComplexEvent extends BaseDomainEvent<ComplexEventData> {\r\n        constructor(aggregateId: UniqueEntityId, data: ComplexEventData) {\r\n          super(aggregateId, data);\r\n        }\r\n      }\r\n\r\n      const complexData: ComplexEventData = {\r\n        user: {\r\n          id: 'user-123',\r\n          profile: {\r\n            name: 'John Doe',\r\n            settings: { theme: 'dark', notifications: true }\r\n          }\r\n        },\r\n        actions: ['login', 'view_dashboard']\r\n      };\r\n\r\n      const event = new ComplexEvent(aggregateId, complexData);\r\n\r\n      expect(event.eventData).toEqual(complexData);\r\n      expect(Object.isFrozen(event.eventData)).toBe(true);\r\n    });\r\n\r\n    it('should handle very old events', () => {\r\n      const veryOldTime = new Date('1970-01-01T00:00:00Z');\r\n      const event = new TestDomainEvent(aggregateId, eventData, { occurredOn: veryOldTime });\r\n\r\n      expect(event.getAge()).toBeGreaterThan(1000000000); // Very large number\r\n      expect(event.isStale(1000)).toBe(true);\r\n    });\r\n\r\n    it('should handle future events (clock skew)', () => {\r\n      const futureTime = new Date(Date.now() + 1000); // 1 second in future\r\n      const event = new TestDomainEvent(aggregateId, eventData, { occurredOn: futureTime });\r\n\r\n      expect(event.getAge()).toBeLessThan(0); // Negative age\r\n    });\r\n  });\r\n});"], "version": 3}