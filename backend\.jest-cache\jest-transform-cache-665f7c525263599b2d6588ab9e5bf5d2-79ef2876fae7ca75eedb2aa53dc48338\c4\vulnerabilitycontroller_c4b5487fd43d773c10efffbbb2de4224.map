{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\api\\controllers\\vulnerability.controller.ts", "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAawB;AACxB,6CAQyB;AACzB,0FAAqF;AACrF,oFAAgF;AAChF,gGAAmF;AACnF,8GAAgG;AAChG,4FAAwF;AACxF,8EAAyE;AACzE,8EAAyE;AACzE,kFAA6E;AAE7E;;;GAGG;AAKI,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAClC,YAA6B,oBAA0C;QAA1C,yBAAoB,GAApB,oBAAoB,CAAsB;IAAG,CAAC;IAE3E;;OAEG;IAQG,AAAN,KAAK,CAAC,YAAY;QAChB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,EAAE,CAAC;IACrE,CAAC;IAED;;OAEG;IAkBG,AAAN,KAAK,CAAC,qBAAqB,CAAwB,KAA+B;QAChF,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IAaG,AAAN,KAAK,CAAC,uBAAuB,CAA6B,EAAU;QAClE,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IAaG,AAAN,KAAK,CAAC,mBAAmB,CACD,sBAA8C,EACrD,IAAS;QAExB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,sBAAsB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9F,CAAC;IAED;;OAEG;IAcG,AAAN,KAAK,CAAC,mBAAmB,CACK,EAAU,EAChB,sBAA8C,EACrD,IAAS;QAExB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,EAAE,EAAE,sBAAsB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAClG,CAAC;IAED;;OAEG;IAaG,AAAN,KAAK,CAAC,mBAAmB,CACK,EAAU,EACvB,IAAS;QAExB,MAAM,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IASG,AAAN,KAAK,CAAC,uBAAuB,CAA6B,EAAU;QAClE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;QAClF,OAAO,aAAa,CAAC,UAAU,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IASG,AAAN,KAAK,CAAC,mBAAmB,CAA6B,EAAU;QAC9D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;QAClF,OAAO,aAAa,CAAC,kBAAkB,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IAoBG,AAAN,KAAK,CAAC,mBAAmB,CACK,EAAU,EAC9B,WAIP,EACc,IAAS;QAExB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;QAClF,aAAa,CAAC,mBAAmB,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;QAEjF,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC;YAC5B,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;QACtD,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IAmBG,AAAN,KAAK,CAAC,iBAAiB,CACO,EAAU,EAC9B,SAGP,EACc,IAAS;QAExB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;QAClF,aAAa,CAAC,iBAAiB,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;QAE/E,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IAkBG,AAAN,KAAK,CAAC,MAAM,CACkB,EAAU,EAC9B,OAAwB,EACjB,IAAS;QAExB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;QAClF,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAElC,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IAUG,AAAN,KAAK,CAAC,SAAS,CACe,EAAU,EACxB,GAAW,EACV,IAAS;QAExB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;QAClF,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAE7B,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IASG,AAAN,KAAK,CAAC,YAAY,CAA6B,EAAU;QACvD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;QAClF,OAAO;YACL,eAAe,EAAE,EAAE;YACnB,SAAS,EAAE,aAAa,CAAC,kBAAkB,EAAE;YAC7C,OAAO,EAAE;gBACP,SAAS,EAAE,aAAa,CAAC,SAAS;gBAClC,QAAQ,EAAE,aAAa,CAAC,QAAQ;gBAChC,WAAW,EAAE,aAAa,CAAC,WAAW;gBACtC,UAAU,EAAE,aAAa,CAAC,UAAU;gBACpC,SAAS,EAAE,aAAa,CAAC,SAAS;gBAClC,cAAc,EAAE,aAAa,CAAC,cAAc;gBAC5C,QAAQ,EAAE,aAAa,CAAC,QAAQ;gBAChC,SAAS,EAAE,aAAa,CAAC,SAAS;aACnC;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AA9TY,0DAAuB;AAa5B;IAPL,IAAA,YAAG,EAAC,WAAW,CAAC;IAChB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACtD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;IAC7D,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,uCAAuC;KACrD,CAAC;;;;2DAGD;AAsBK;IAjBL,IAAA,YAAG,GAAE;IACL,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,QAAQ,CAAC;IAChE,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,sDAAsD,EAAE,CAAC;IACjF,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IACrF,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IACzF,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IACtG,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,0BAA0B,EAAE,CAAC;IAC1G,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,gCAAgC,EAAE,CAAC;IAC/G,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,8BAA8B,EAAE,CAAC;IAC5G,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,8BAA8B,EAAE,CAAC;IACjH,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IAC3F,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC;IACtF,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC;IAClG,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,wCAAwC;KACtD,CAAC;IAC2B,WAAA,IAAA,cAAK,EAAC,uBAAc,CAAC,CAAA;;yDAAQ,qDAAwB,oBAAxB,qDAAwB;;oEAEjF;AAiBK;IAZL,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,QAAQ,CAAC;IAChE,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;IAC5D,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACzD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,8CAA8C;KAC5D,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,yBAAyB;KACvC,CAAC;IAC6B,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;;;;sEAExD;AAiBK;IAZL,IAAA,aAAI,GAAE;IACN,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACtD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACvD,IAAA,iBAAO,EAAC,EAAE,IAAI,EAAE,iDAAsB,EAAE,CAAC;IACzC,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,OAAO;QAC1B,WAAW,EAAE,oCAAoC;KAClD,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,WAAW;QAC9B,WAAW,EAAE,4BAA4B;KAC1C,CAAC;IAEC,WAAA,IAAA,aAAI,EAAC,uBAAc,CAAC,CAAA;IACpB,WAAA,IAAA,oCAAW,GAAE,CAAA;;yDADgC,iDAAsB,oBAAtB,iDAAsB;;kEAIrE;AAkBK;IAbL,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACtD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IACjD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACzD,IAAA,iBAAO,EAAC,EAAE,IAAI,EAAE,iDAAsB,EAAE,CAAC;IACzC,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,oCAAoC;KAClD,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,yBAAyB;KACvC,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,EAAC,uBAAc,CAAC,CAAA;IACpB,WAAA,IAAA,oCAAW,GAAE,CAAA;;iEADgC,iDAAsB,oBAAtB,iDAAsB;;kEAIrE;AAiBK;IAZL,IAAA,eAAM,EAAC,KAAK,CAAC;IACb,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,CAAC;IAClC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IACjD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACzD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,UAAU;QAC7B,WAAW,EAAE,oCAAoC;KAClD,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,yBAAyB;KACvC,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,oCAAW,GAAE,CAAA;;;;kEAGf;AAaK;IARL,IAAA,YAAG,EAAC,aAAa,CAAC;IAClB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,QAAQ,CAAC;IAChE,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;IACtD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACzD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,8CAA8C;KAC5D,CAAC;IAC6B,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;;;;sEAGxD;AAaK;IARL,IAAA,YAAG,EAAC,YAAY,CAAC;IACjB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACtD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,yCAAyC,EAAE,CAAC;IACpE,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACzD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,0CAA0C;KACxD,CAAC;IACyB,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;;;;kEAGpD;AAwBK;IAnBL,IAAA,YAAG,EAAC,oBAAoB,CAAC;IACzB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACtD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC;IAChE,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACzD,IAAA,iBAAO,EAAC;QACP,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,UAAU,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;gBAC/B,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;gBAC9B,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAChC;YACD,QAAQ,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC;SACtC;KACF,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,qCAAqC;KACnD,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,GAAE,CAAA;IAKN,WAAA,IAAA,oCAAW,GAAE,CAAA;;;;kEAUf;AAuBK;IAlBL,IAAA,YAAG,EAAC,kBAAkB,CAAC;IACvB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACtD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;IAC9D,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACzD,IAAA,iBAAO,EAAC;QACP,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,cAAc,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnC,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC9B;YACD,QAAQ,EAAE,CAAC,gBAAgB,CAAC;SAC7B;KACF,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,mCAAmC;KACjD,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,GAAE,CAAA;IAIN,WAAA,IAAA,oCAAW,GAAE,CAAA;;;;gEAMf;AAsBK;IAjBL,IAAA,aAAI,EAAC,UAAU,CAAC;IAChB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACtD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;IACrD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACzD,IAAA,iBAAO,EAAC;QACP,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aACxB;YACD,QAAQ,EAAE,CAAC,KAAK,CAAC;SAClB;KACF,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,wBAAwB;KACtC,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,oCAAW,GAAE,CAAA;;;;qDAMf;AAcK;IATL,IAAA,eAAM,EAAC,eAAe,CAAC;IACvB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACtD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;IAC1D,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACzD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IACvD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,0BAA0B;KACxC,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,cAAK,EAAC,KAAK,CAAC,CAAA;IACZ,WAAA,IAAA,oCAAW,GAAE,CAAA;;;;wDAMf;AAaK;IARL,IAAA,YAAG,EAAC,gBAAgB,CAAC;IACrB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,QAAQ,CAAC;IAChE,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC;IAC/D,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACzD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,oCAAoC;KAClD,CAAC;IACkB,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;;;;2DAgB7C;kCA7TU,uBAAuB;IAJnC,IAAA,iBAAO,EAAC,iBAAiB,CAAC;IAC1B,IAAA,uBAAa,GAAE;IACf,IAAA,kBAAS,EAAC,6BAAY,EAAE,wBAAU,CAAC;IACnC,IAAA,mBAAU,EAAC,qBAAqB,CAAC;yDAEmB,4CAAoB,oBAApB,4CAAoB;GAD5D,uBAAuB,CA8TnC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\api\\controllers\\vulnerability.controller.ts"], "sourcesContent": ["import {\r\n  Controller,\r\n  Get,\r\n  Post,\r\n  Put,\r\n  Delete,\r\n  Body,\r\n  Param,\r\n  Query,\r\n  UseGuards,\r\n  HttpStatus,\r\n  ParseUUIDPipe,\r\n  ValidationPipe,\r\n} from '@nestjs/common';\r\nimport {\r\n  ApiTags,\r\n  ApiOperation,\r\n  ApiResponse,\r\n  ApiParam,\r\n  ApiQuery,\r\n  ApiBearerAuth,\r\n  ApiBody,\r\n} from '@nestjs/swagger';\r\nimport { JwtAuthGuard } from '../../../../infrastructure/auth/guards/jwt-auth.guard';\r\nimport { RolesGuard } from '../../../../infrastructure/auth/guards/roles.guard';\r\nimport { Roles } from '../../../../infrastructure/auth/decorators/roles.decorator';\r\nimport { CurrentUser } from '../../../../infrastructure/auth/decorators/current-user.decorator';\r\nimport { VulnerabilityService } from '../../application/services/vulnerability.service';\r\nimport { CreateVulnerabilityDto } from '../dto/create-vulnerability.dto';\r\nimport { UpdateVulnerabilityDto } from '../dto/update-vulnerability.dto';\r\nimport { SearchVulnerabilitiesDto } from '../dto/search-vulnerabilities.dto';\r\n\r\n/**\r\n * Vulnerability controller\r\n * Handles vulnerability management API endpoints\r\n */\r\n@ApiTags('Vulnerabilities')\r\n@ApiBearerAuth()\r\n@UseGuards(JwtAuthGuard, RolesGuard)\r\n@Controller('api/vulnerabilities')\r\nexport class VulnerabilityController {\r\n  constructor(private readonly vulnerabilityService: VulnerabilityService) {}\r\n\r\n  /**\r\n   * Get vulnerability dashboard\r\n   */\r\n  @Get('dashboard')\r\n  @Roles('admin', 'security_analyst', 'security_manager')\r\n  @ApiOperation({ summary: 'Get vulnerability dashboard data' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Dashboard data retrieved successfully',\r\n  })\r\n  async getDashboard() {\r\n    return await this.vulnerabilityService.getVulnerabilityDashboard();\r\n  }\r\n\r\n  /**\r\n   * Search vulnerabilities\r\n   */\r\n  @Get()\r\n  @Roles('admin', 'security_analyst', 'security_manager', 'viewer')\r\n  @ApiOperation({ summary: 'Search vulnerabilities with filtering and pagination' })\r\n  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })\r\n  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })\r\n  @ApiQuery({ name: 'severities', required: false, type: [String], description: 'Filter by severities' })\r\n  @ApiQuery({ name: 'exploitable', required: false, type: Boolean, description: 'Filter by exploitability' })\r\n  @ApiQuery({ name: 'hasExploit', required: false, type: Boolean, description: 'Filter by exploit availability' })\r\n  @ApiQuery({ name: 'inTheWild', required: false, type: Boolean, description: 'Filter by in-the-wild status' })\r\n  @ApiQuery({ name: 'patchAvailable', required: false, type: Boolean, description: 'Filter by patch availability' })\r\n  @ApiQuery({ name: 'searchText', required: false, type: String, description: 'Search text' })\r\n  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })\r\n  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Vulnerabilities retrieved successfully',\r\n  })\r\n  async searchVulnerabilities(@Query(ValidationPipe) query: SearchVulnerabilitiesDto) {\r\n    return await this.vulnerabilityService.searchVulnerabilities(query);\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability details\r\n   */\r\n  @Get(':id')\r\n  @Roles('admin', 'security_analyst', 'security_manager', 'viewer')\r\n  @ApiOperation({ summary: 'Get vulnerability details by ID' })\r\n  @ApiParam({ name: 'id', description: 'Vulnerability ID' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Vulnerability details retrieved successfully',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NOT_FOUND,\r\n    description: 'Vulnerability not found',\r\n  })\r\n  async getVulnerabilityDetails(@Param('id', ParseUUIDPipe) id: string) {\r\n    return await this.vulnerabilityService.getVulnerabilityDetails(id);\r\n  }\r\n\r\n  /**\r\n   * Create vulnerability\r\n   */\r\n  @Post()\r\n  @Roles('admin', 'security_analyst', 'security_manager')\r\n  @ApiOperation({ summary: 'Create a new vulnerability' })\r\n  @ApiBody({ type: CreateVulnerabilityDto })\r\n  @ApiResponse({\r\n    status: HttpStatus.CREATED,\r\n    description: 'Vulnerability created successfully',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.BAD_REQUEST,\r\n    description: 'Invalid vulnerability data',\r\n  })\r\n  async createVulnerability(\r\n    @Body(ValidationPipe) createVulnerabilityDto: CreateVulnerabilityDto,\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    return await this.vulnerabilityService.createVulnerability(createVulnerabilityDto, user.id);\r\n  }\r\n\r\n  /**\r\n   * Update vulnerability\r\n   */\r\n  @Put(':id')\r\n  @Roles('admin', 'security_analyst', 'security_manager')\r\n  @ApiOperation({ summary: 'Update vulnerability' })\r\n  @ApiParam({ name: 'id', description: 'Vulnerability ID' })\r\n  @ApiBody({ type: UpdateVulnerabilityDto })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Vulnerability updated successfully',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NOT_FOUND,\r\n    description: 'Vulnerability not found',\r\n  })\r\n  async updateVulnerability(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Body(ValidationPipe) updateVulnerabilityDto: UpdateVulnerabilityDto,\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    return await this.vulnerabilityService.updateVulnerability(id, updateVulnerabilityDto, user.id);\r\n  }\r\n\r\n  /**\r\n   * Delete vulnerability\r\n   */\r\n  @Delete(':id')\r\n  @Roles('admin', 'security_manager')\r\n  @ApiOperation({ summary: 'Delete vulnerability' })\r\n  @ApiParam({ name: 'id', description: 'Vulnerability ID' })\r\n  @ApiResponse({\r\n    status: HttpStatus.NO_CONTENT,\r\n    description: 'Vulnerability deleted successfully',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NOT_FOUND,\r\n    description: 'Vulnerability not found',\r\n  })\r\n  async deleteVulnerability(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    await this.vulnerabilityService.deleteVulnerability(id, user.id);\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability summary\r\n   */\r\n  @Get(':id/summary')\r\n  @Roles('admin', 'security_analyst', 'security_manager', 'viewer')\r\n  @ApiOperation({ summary: 'Get vulnerability summary' })\r\n  @ApiParam({ name: 'id', description: 'Vulnerability ID' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Vulnerability summary retrieved successfully',\r\n  })\r\n  async getVulnerabilitySummary(@Param('id', ParseUUIDPipe) id: string) {\r\n    const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);\r\n    return vulnerability.getSummary();\r\n  }\r\n\r\n  /**\r\n   * Export vulnerability for reporting\r\n   */\r\n  @Get(':id/export')\r\n  @Roles('admin', 'security_analyst', 'security_manager')\r\n  @ApiOperation({ summary: 'Export vulnerability data for reporting' })\r\n  @ApiParam({ name: 'id', description: 'Vulnerability ID' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Vulnerability data exported successfully',\r\n  })\r\n  async exportVulnerability(@Param('id', ParseUUIDPipe) id: string) {\r\n    const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);\r\n    return vulnerability.exportForReporting();\r\n  }\r\n\r\n  /**\r\n   * Update exploit status\r\n   */\r\n  @Put(':id/exploit-status')\r\n  @Roles('admin', 'security_analyst', 'security_manager')\r\n  @ApiOperation({ summary: 'Update vulnerability exploit status' })\r\n  @ApiParam({ name: 'id', description: 'Vulnerability ID' })\r\n  @ApiBody({\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        hasExploit: { type: 'boolean' },\r\n        inTheWild: { type: 'boolean' },\r\n        exploitInfo: { type: 'object' },\r\n      },\r\n      required: ['hasExploit', 'inTheWild'],\r\n    },\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Exploit status updated successfully',\r\n  })\r\n  async updateExploitStatus(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Body() exploitData: {\r\n      hasExploit: boolean;\r\n      inTheWild: boolean;\r\n      exploitInfo?: any;\r\n    },\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);\r\n    vulnerability.updateExploitStatus(exploitData.hasExploit, exploitData.inTheWild);\r\n\r\n    if (exploitData.exploitInfo) {\r\n      vulnerability.exploitInfo = exploitData.exploitInfo;\r\n    }\r\n\r\n    return await this.vulnerabilityService.updateVulnerability(id, vulnerability, user.id);\r\n  }\r\n\r\n  /**\r\n   * Update patch status\r\n   */\r\n  @Put(':id/patch-status')\r\n  @Roles('admin', 'security_analyst', 'security_manager')\r\n  @ApiOperation({ summary: 'Update vulnerability patch status' })\r\n  @ApiParam({ name: 'id', description: 'Vulnerability ID' })\r\n  @ApiBody({\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        patchAvailable: { type: 'boolean' },\r\n        patchInfo: { type: 'object' },\r\n      },\r\n      required: ['patchAvailable'],\r\n    },\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Patch status updated successfully',\r\n  })\r\n  async updatePatchStatus(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Body() patchData: {\r\n      patchAvailable: boolean;\r\n      patchInfo?: any;\r\n    },\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);\r\n    vulnerability.updatePatchStatus(patchData.patchAvailable, patchData.patchInfo);\r\n\r\n    return await this.vulnerabilityService.updateVulnerability(id, vulnerability, user.id);\r\n  }\r\n\r\n  /**\r\n   * Add tag to vulnerability\r\n   */\r\n  @Post(':id/tags')\r\n  @Roles('admin', 'security_analyst', 'security_manager')\r\n  @ApiOperation({ summary: 'Add tag to vulnerability' })\r\n  @ApiParam({ name: 'id', description: 'Vulnerability ID' })\r\n  @ApiBody({\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        tag: { type: 'string' },\r\n      },\r\n      required: ['tag'],\r\n    },\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Tag added successfully',\r\n  })\r\n  async addTag(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Body() tagData: { tag: string },\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);\r\n    vulnerability.addTag(tagData.tag);\r\n\r\n    return await this.vulnerabilityService.updateVulnerability(id, vulnerability, user.id);\r\n  }\r\n\r\n  /**\r\n   * Remove tag from vulnerability\r\n   */\r\n  @Delete(':id/tags/:tag')\r\n  @Roles('admin', 'security_analyst', 'security_manager')\r\n  @ApiOperation({ summary: 'Remove tag from vulnerability' })\r\n  @ApiParam({ name: 'id', description: 'Vulnerability ID' })\r\n  @ApiParam({ name: 'tag', description: 'Tag to remove' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Tag removed successfully',\r\n  })\r\n  async removeTag(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Param('tag') tag: string,\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);\r\n    vulnerability.removeTag(tag);\r\n\r\n    return await this.vulnerabilityService.updateVulnerability(id, vulnerability, user.id);\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability risk score\r\n   */\r\n  @Get(':id/risk-score')\r\n  @Roles('admin', 'security_analyst', 'security_manager', 'viewer')\r\n  @ApiOperation({ summary: 'Calculate vulnerability risk score' })\r\n  @ApiParam({ name: 'id', description: 'Vulnerability ID' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Risk score calculated successfully',\r\n  })\r\n  async getRiskScore(@Param('id', ParseUUIDPipe) id: string) {\r\n    const vulnerability = await this.vulnerabilityService.getVulnerabilityDetails(id);\r\n    return {\r\n      vulnerabilityId: id,\r\n      riskScore: vulnerability.calculateRiskScore(),\r\n      factors: {\r\n        cvssScore: vulnerability.cvssScore,\r\n        severity: vulnerability.severity,\r\n        exploitable: vulnerability.exploitable,\r\n        hasExploit: vulnerability.hasExploit,\r\n        inTheWild: vulnerability.inTheWild,\r\n        patchAvailable: vulnerability.patchAvailable,\r\n        isRecent: vulnerability.isRecent,\r\n        epssScore: vulnerability.epssScore,\r\n      },\r\n    };\r\n  }\r\n}\r\n"], "version": 3}