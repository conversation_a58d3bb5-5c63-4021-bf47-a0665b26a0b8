{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\normalized-event.entity.spec.ts", "mappings": ";;AAAA,wEAAgJ;AAChJ,gEAA8D;AAC9D,gHAA+F;AAC/F,kHAAiG;AACjG,4GAA2F;AAC3F,iEAAwD;AACxD,yEAAgE;AAChE,qEAA4D;AAC5D,2FAAiF;AACjF,+EAAqE;AACrE,yCAA+B;AAE/B,yCAAqC;AAarC,yCAAuC;AAoEvC,IAAA,oBAAQ,EAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,IAAI,UAAgC,CAAC;IACrC,IAAI,YAA2B,CAAC;IAChC,IAAI,QAA2B,CAAC;IAEhC,IAAA,sBAAU,EAAC,GAAG,EAAE;QACd,uBAAuB;QACvB,MAAM,SAAS,GAAG,6CAAc,CAAC,MAAM,EAAE,CAAC;QAC1C,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QACrE,YAAY,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAEvD,iCAAiC;QACjC,QAAQ,GAAG;YACT,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,yBAAyB;YACtC,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,KAAK;SAChB,CAAC;QAEF,qBAAqB;QACrB,UAAU,GAAG;YACX,eAAe,EAAE,8BAAc,CAAC,MAAM,EAAE;YACxC,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,2BAAS,CAAC,eAAe;YAC/B,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,MAAM,EAAE,+BAAW,CAAC,MAAM;YAC1B,gBAAgB,EAAE,oDAAqB,CAAC,UAAU;YAClD,mBAAmB,EAAE,6CAAmB,CAAC,SAAS;YAClD,YAAY,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE;YAC7B,cAAc,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;YACtC,KAAK,EAAE,uBAAuB;YAC9B,YAAY,EAAE,CAAC,QAAQ,CAAC;YACxB,aAAa,EAAE,OAAO;YACtB,gBAAgB,EAAE,EAAE;YACpB,qBAAqB,EAAE,CAAC;SACzB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,UAAU,EAAE,GAAG,EAAE;QACxB,IAAA,cAAE,EAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM;YACN,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAE3D,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,yCAAe,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YAC5E,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;YAC7D,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,6CAAmB,CAAC,SAAS,CAAC,CAAC;YAChF,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM;YACN,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAE3D,SAAS;YACT,MAAM,YAAY,GAAG,eAAe,CAAC,oBAAoB,EAAE,CAAC;YAC5D,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,UAAU;YACV,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,eAAe,CAAC;YAE7C,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;QACjH,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,UAAU;YACV,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,QAAQ,CAAC;YAEtC,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,UAAU;YACV,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,cAAc,CAAC;YAE5C,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;QAC1G,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,UAAU;YACV,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,aAAa,CAAC;YAE3C,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,UAAU;YACV,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,YAAY,EAAE,cAAqB,EAAE,CAAC;YAE5E,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;QAC9G,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,IAAA,cAAE,EAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,UAAU;YACV,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,gBAAgB,EAAE,GAAG,EAAE,CAAC;YAE9D,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;QAC7G,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,UAAU;YACV,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YAEvD,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,UAAU;YACV,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,eAAe,EAAE,GAAG,EAAE,CAAC;YAE7D,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,UAAU;YACV,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,qBAAqB,EAAE,CAAC,CAAC,EAAE,CAAC;YAElE,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;QAC1G,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,UAAU;YACV,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,gBAAgB,EAAE,aAAa,EAAE,CAAC;YAExE,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,IAAA,cAAE,EAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,UAAU;YACV,MAAM,YAAY,GAAG;gBACnB,GAAG,UAAU;gBACb,mBAAmB,EAAE,6CAAmB,CAAC,SAAS;gBAClD,wBAAwB,EAAE,SAAS;aACpC,CAAC;YAEF,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC;QACvH,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,UAAU;YACV,MAAM,YAAY,GAAG;gBACnB,GAAG,UAAU;gBACb,mBAAmB,EAAE,6CAAmB,CAAC,SAAS;gBAClD,wBAAwB,EAAE,IAAI,IAAI,EAAE;gBACpC,mBAAmB,EAAE,SAAS;aAC/B,CAAC;YAEF,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;QACzG,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,UAAU;YACV,MAAM,YAAY,GAAG;gBACnB,GAAG,UAAU;gBACb,mBAAmB,EAAE,6CAAmB,CAAC,MAAM;gBAC/C,sBAAsB,EAAE,SAAS;gBACjC,mBAAmB,EAAE,SAAS;aAC/B,CAAC;YAEF,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;QACjH,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,UAAU;YACV,MAAM,YAAY,GAAG;gBACnB,GAAG,UAAU;gBACb,mBAAmB,EAAE,6CAAmB,CAAC,WAAW;gBACpD,sBAAsB,EAAE,SAAS;aAClC,CAAC;YAEF,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,qDAAqD,CAAC,CAAC;QACpH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,IAAI,eAAgC,CAAC;QAErC,IAAA,sBAAU,EAAC,GAAG,EAAE;YACd,MAAM,YAAY,GAAG;gBACnB,GAAG,UAAU;gBACb,mBAAmB,EAAE,6CAAmB,CAAC,OAAO;gBAChD,wBAAwB,EAAE,SAAS;gBACnC,mBAAmB,EAAE,SAAS;aAC/B,CAAC;YACF,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,IAAA,cAAE,EAAC,+CAA+C,EAAE,GAAG,EAAE;gBACvD,MAAM;gBACN,eAAe,CAAC,kBAAkB,EAAE,CAAC;gBAErC,SAAS;gBACT,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,6CAAmB,CAAC,WAAW,CAAC,CAAC;gBAClF,MAAM,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7D,MAAM,CAAC,eAAe,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,uEAAuE,EAAE,GAAG,EAAE;gBAC/E,UAAU;gBACV,eAAe,CAAC,kBAAkB,EAAE,CAAC;gBAErC,eAAe;gBACf,MAAM,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;YAChH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,IAAI,UAA+B,CAAC;YAEpC,IAAA,sBAAU,EAAC,GAAG,EAAE;gBACd,UAAU,GAAG;oBACX,OAAO,EAAE,IAAI;oBACb,YAAY,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;oBAChC,WAAW,EAAE,EAAE;oBACf,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,EAAE;oBACV,oBAAoB,EAAE,IAAI;oBAC1B,eAAe,EAAE,EAAE;iBACpB,CAAC;gBACF,eAAe,CAAC,kBAAkB,EAAE,CAAC;YACvC,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,sDAAsD,EAAE,GAAG,EAAE;gBAC9D,MAAM;gBACN,eAAe,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;gBAElD,SAAS;gBACT,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,6CAAmB,CAAC,SAAS,CAAC,CAAC;gBAChF,MAAM,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC/D,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBAChE,MAAM,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC,aAAa,EAAE,CAAC;YACjE,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,2DAA2D,EAAE,GAAG,EAAE;gBACnE,MAAM;gBACN,eAAe,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;gBAElD,SAAS;gBACT,MAAM,YAAY,GAAG,eAAe,CAAC,oBAAoB,EAAE,CAAC;gBAC5D,MAAM,kBAAkB,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,yCAAyC,CAAC,CAAC;gBACpH,MAAM,CAAC,kBAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3C,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,6EAA6E,EAAE,GAAG,EAAE;gBACrF,UAAU;gBACV,eAAe,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;gBAElD,eAAe;gBACf,MAAM,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC;YACpI,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,IAAA,sBAAU,EAAC,GAAG,EAAE;gBACd,eAAe,CAAC,kBAAkB,EAAE,CAAC;YACvC,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,kDAAkD,EAAE,GAAG,EAAE;gBAC1D,MAAM;gBACN,eAAe,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAEhD,SAAS;gBACT,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,6CAAmB,CAAC,MAAM,CAAC,CAAC;gBAC7E,MAAM,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,uDAAuD,EAAE,GAAG,EAAE;gBAC/D,MAAM;gBACN,eAAe,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAEhD,SAAS;gBACT,MAAM,YAAY,GAAG,eAAe,CAAC,oBAAoB,EAAE,CAAC;gBAC5D,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,4CAA4C,CAAC,CAAC;gBAChH,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,0EAA0E,EAAE,GAAG,EAAE;gBAClF,UAAU;gBACV,eAAe,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAEhD,eAAe;gBACf,MAAM,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,oDAAoD,CAAC,CAAC;YACjI,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,IAAA,cAAE,EAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM;gBACN,eAAe,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;gBAEjD,SAAS;gBACT,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,6CAAmB,CAAC,OAAO,CAAC,CAAC;gBAC9E,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,UAAU;gBACV,eAAe,CAAC,kBAAkB,EAAE,CAAC;gBACrC,eAAe,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAEhD,MAAM;gBACN,eAAe,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;gBAEjD,SAAS;gBACT,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,6CAAmB,CAAC,OAAO,CAAC,CAAC;YAChF,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,qEAAqE,EAAE,GAAG,EAAE;gBAC7E,UAAU;gBACV,eAAe,CAAC,kBAAkB,EAAE,CAAC;gBAErC,eAAe;gBACf,MAAM,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,0DAA0D,CAAC,CAAC;YACrI,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,IAAA,cAAE,EAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,UAAU;gBACV,eAAe,CAAC,kBAAkB,EAAE,CAAC;gBACrC,eAAe,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAEhD,MAAM;gBACN,eAAe,CAAC,kBAAkB,EAAE,CAAC;gBAErC,SAAS;gBACT,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,6CAAmB,CAAC,OAAO,CAAC,CAAC;gBAC9E,MAAM,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC,aAAa,EAAE,CAAC;gBAC/D,MAAM,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC,aAAa,EAAE,CAAC;YACjE,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,+DAA+D,EAAE,GAAG,EAAE;gBACvE,UAAU;gBACV,MAAM,oBAAoB,GAAG;oBAC3B,GAAG,UAAU;oBACb,mBAAmB,EAAE,6CAAmB,CAAC,MAAM;oBAC/C,qBAAqB,EAAE,CAAC;iBACzB,CAAC;gBACF,MAAM,oBAAoB,GAAG,yCAAe,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;gBAE1E,eAAe;gBACf,MAAM,CAAC,GAAG,EAAE,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,CAAC,CAAC,OAAO,CAAC,uDAAuD,CAAC,CAAC;YAC3H,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,IAAI,eAAgC,CAAC;QAErC,IAAA,sBAAU,EAAC,GAAG,EAAE;YACd,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,IAAA,cAAE,EAAC,wDAAwD,EAAE,GAAG,EAAE;gBAChE,UAAU;gBACV,MAAM,YAAY,GAAG;oBACnB,GAAG,UAAU;oBACb,mBAAmB,EAAE,6CAAmB,CAAC,OAAO;iBACjD,CAAC;gBACF,MAAM,YAAY,GAAG,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBAC1D,MAAM,OAAO,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;gBAEpC,MAAM;gBACN,YAAY,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;gBAE3C,SAAS;gBACT,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,uEAAuE,EAAE,GAAG,EAAE;gBAC/E,UAAU;gBACV,MAAM,OAAO,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;gBAEpC,eAAe;gBACf,MAAM,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,2DAA2D,CAAC,CAAC;YACnI,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,IAAA,cAAE,EAAC,6BAA6B,EAAE,GAAG,EAAE;gBACrC,UAAU;gBACV,MAAM,OAAO,GAAsB;oBACjC,EAAE,EAAE,UAAU;oBACd,IAAI,EAAE,UAAU;oBAChB,WAAW,EAAE,wBAAwB;oBACrC,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,IAAI;iBACf,CAAC;gBAEF,MAAM;gBACN,eAAe,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBAExC,SAAS;gBACT,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,UAAU;gBACV,MAAM,YAAY,GAAG,eAAe,CAAC,YAAY,CAAC,MAAM,CAAC;gBAEzD,MAAM;gBACN,eAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAEzC,SAAS;gBACT,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,wBAAwB,EAAE,GAAG,EAAE;YACtC,IAAA,cAAE,EAAC,qDAAqD,EAAE,GAAG,EAAE;gBAC7D,MAAM;gBACN,eAAe,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;gBAE3C,SAAS;gBACT,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,mDAAmD,EAAE,GAAG,EAAE;gBAC3D,eAAe;gBACf,MAAM,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;YACpH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,8BAA8B,EAAE,GAAG,EAAE;YAC5C,IAAA,cAAE,EAAC,8BAA8B,EAAE,GAAG,EAAE;gBACtC,UAAU;gBACV,MAAM,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBAEtC,MAAM;gBACN,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBAE5C,SAAS;gBACT,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,UAAU;gBACV,eAAe,CAAC,mBAAmB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gBAEjD,MAAM;gBACN,eAAe,CAAC,qBAAqB,EAAE,CAAC;gBAExC,SAAS;gBACT,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,2DAA2D,EAAE,GAAG,EAAE;gBACnE,UAAU;gBACV,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAE9C,eAAe;gBACf,MAAM,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;YACzH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAI,eAAgC,CAAC;QAErC,IAAA,sBAAU,EAAC,GAAG,EAAE;YACd,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,qBAAqB,EAAE,GAAG,EAAE;YACnC,IAAA,cAAE,EAAC,qCAAqC,EAAE,GAAG,EAAE;gBAC7C,MAAM;gBACN,eAAe,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;gBAEvD,SAAS;gBACT,MAAM,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxD,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,oBAAQ,EAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,IAAA,cAAE,EAAC,4DAA4D,EAAE,GAAG,EAAE;gBACpE,UAAU;gBACV,eAAe,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;gBAEnD,MAAM;gBACN,eAAe,CAAC,oBAAoB,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,CAAC;gBAEhF,SAAS;gBACT,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAC/D,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;gBACjD,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;YAEH,IAAA,cAAE,EAAC,4EAA4E,EAAE,GAAG,EAAE;gBACpF,eAAe;gBACf,MAAM,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC,uCAAuC,CAAC,CAAC;YAC7H,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAI,eAAgC,CAAC;QAErC,IAAA,sBAAU,EAAC,GAAG,EAAE;YACd,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,eAAe,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,eAAe,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,CAAC,eAAe,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,CAAC,eAAe,CAAC,mCAAmC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,CAAC,eAAe,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,UAAU;YACV,MAAM,aAAa,GAAG,EAAE,GAAG,UAAU,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;YACvD,MAAM,aAAa,GAAG,yCAAe,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAE5D,SAAS;YACT,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,UAAU;YACV,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YACrD,MAAM,iBAAiB,GAAG;gBACxB,GAAG,UAAU;gBACb,sBAAsB,EAAE,SAAS;gBACjC,wBAAwB,EAAE,OAAO;aAClC,CAAC;YACF,MAAM,iBAAiB,GAAG,yCAAe,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAEpE,MAAM;YACN,MAAM,QAAQ,GAAG,iBAAiB,CAAC,wBAAwB,EAAE,CAAC;YAE9D,SAAS;YACT,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM;YACN,MAAM,SAAS,GAAG,eAAe,CAAC,mBAAmB,EAAE,CAAC;YAExD,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,eAAe;YACf,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/D,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAA,cAAE,EAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAE3D,MAAM;YACN,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC;YAEtC,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,2BAAS,CAAC,eAAe,CAAC,CAAC;YAC/D,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,UAAU,EAAE,mCAAa,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,qBAAqB,EAAE,6CAAmB,CAAC,SAAS,CAAC,CAAC;YAClF,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAE3D,MAAM;YACN,MAAM,OAAO,GAAG,eAAe,CAAC,UAAU,EAAE,CAAC;YAE7C,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,OAAO,EAAE,uBAAuB,CAAC,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,2BAAS,CAAC,eAAe,CAAC,CAAC;YAClE,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,UAAU,EAAE,mCAAa,CAAC,IAAI,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,qBAAqB,EAAE,6CAAmB,CAAC,SAAS,CAAC,CAAC;YACrF,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\normalized-event.entity.spec.ts"], "sourcesContent": ["import { NormalizedEvent, NormalizedEventProps, NormalizationStatus, NormalizationRule, NormalizationResult } from '../normalized-event.entity';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { EventMetadata } from '../../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../value-objects/event-metadata/event-source.value-object';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventStatus } from '../../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../../enums/event-source-type.enum';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\n\r\ndescribe('NormalizedEvent Entity', () => {\r\n  let validProps: NormalizedEventProps;\r\n  let mockMetadata: EventMetadata;\r\n  let mockRule: NormalizationRule;\r\n\r\n  beforeEach(() => {\r\n    // Create mock metadata\r\n    const timestamp = EventTimestamp.create();\r\n    const source = EventSource.create(EventSourceType.SIEM, 'test-siem');\r\n    mockMetadata = EventMetadata.create(timestamp, source);\r\n\r\n    // Create mock normalization rule\r\n    mockRule = {\r\n      id: 'test-rule',\r\n      name: 'Test Rule',\r\n      description: 'Test normalization rule',\r\n      priority: 100,\r\n      required: false,\r\n    };\r\n\r\n    // Create valid props\r\n    validProps = {\r\n      originalEventId: UniqueEntityId.create(),\r\n      metadata: mockMetadata,\r\n      type: EventType.THREAT_DETECTED,\r\n      severity: EventSeverity.HIGH,\r\n      status: EventStatus.ACTIVE,\r\n      processingStatus: EventProcessingStatus.NORMALIZED,\r\n      normalizationStatus: NormalizationStatus.COMPLETED,\r\n      originalData: { raw: 'data' },\r\n      normalizedData: { normalized: 'data' },\r\n      title: 'Test Normalized Event',\r\n      appliedRules: [mockRule],\r\n      schemaVersion: '1.0.0',\r\n      dataQualityScore: 85,\r\n      normalizationAttempts: 1,\r\n    };\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create a valid normalized event with required properties', () => {\r\n      // Act\r\n      const normalizedEvent = NormalizedEvent.create(validProps);\r\n\r\n      // Assert\r\n      expect(normalizedEvent).toBeInstanceOf(NormalizedEvent);\r\n      expect(normalizedEvent.originalEventId).toEqual(validProps.originalEventId);\r\n      expect(normalizedEvent.type).toBe(EventType.THREAT_DETECTED);\r\n      expect(normalizedEvent.severity).toBe(EventSeverity.HIGH);\r\n      expect(normalizedEvent.normalizationStatus).toBe(NormalizationStatus.COMPLETED);\r\n      expect(normalizedEvent.schemaVersion).toBe('1.0.0');\r\n      expect(normalizedEvent.dataQualityScore).toBe(85);\r\n    });\r\n\r\n    it('should generate domain event when created', () => {\r\n      // Act\r\n      const normalizedEvent = NormalizedEvent.create(validProps);\r\n\r\n      // Assert\r\n      const domainEvents = normalizedEvent.getUncommittedEvents();\r\n      expect(domainEvents).toHaveLength(1);\r\n      expect(domainEvents[0].constructor.name).toBe('NormalizedEventCreatedDomainEvent');\r\n    });\r\n\r\n    it('should throw error when original event ID is missing', () => {\r\n      // Arrange\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).originalEventId;\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('NormalizedEvent must reference an original event');\r\n    });\r\n\r\n    it('should throw error when metadata is missing', () => {\r\n      // Arrange\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).metadata;\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('NormalizedEvent must have metadata');\r\n    });\r\n\r\n    it('should throw error when normalized data is missing', () => {\r\n      // Arrange\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).normalizedData;\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('NormalizedEvent must have normalized data');\r\n    });\r\n\r\n    it('should throw error when schema version is missing', () => {\r\n      // Arrange\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).schemaVersion;\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('NormalizedEvent must have a schema version');\r\n    });\r\n\r\n    it('should throw error when applied rules is not an array', () => {\r\n      // Arrange\r\n      const invalidProps = { ...validProps, appliedRules: 'not-an-array' as any };\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('NormalizedEvent must have applied rules array');\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should validate data quality score range', () => {\r\n      // Arrange\r\n      const invalidProps = { ...validProps, dataQualityScore: 150 };\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('Data quality score must be between 0 and 100');\r\n    });\r\n\r\n    it('should validate risk score range', () => {\r\n      // Arrange\r\n      const invalidProps = { ...validProps, riskScore: -10 };\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('Risk score must be between 0 and 100');\r\n    });\r\n\r\n    it('should validate confidence level range', () => {\r\n      // Arrange\r\n      const invalidProps = { ...validProps, confidenceLevel: 110 };\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('Confidence level must be between 0 and 100');\r\n    });\r\n\r\n    it('should validate normalization attempts cannot be negative', () => {\r\n      // Arrange\r\n      const invalidProps = { ...validProps, normalizationAttempts: -1 };\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('Normalization attempts cannot be negative');\r\n    });\r\n\r\n    it('should validate maximum validation errors', () => {\r\n      // Arrange\r\n      const tooManyErrors = Array(15).fill('error');\r\n      const invalidProps = { ...validProps, validationErrors: tooManyErrors };\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('Cannot have more than 10 validation errors');\r\n    });\r\n  });\r\n\r\n  describe('normalization status consistency', () => {\r\n    it('should require completion timestamp for completed normalization', () => {\r\n      // Arrange\r\n      const invalidProps = {\r\n        ...validProps,\r\n        normalizationStatus: NormalizationStatus.COMPLETED,\r\n        normalizationCompletedAt: undefined,\r\n      };\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('Completed normalization must have completion timestamp');\r\n    });\r\n\r\n    it('should require normalization result for completed normalization', () => {\r\n      // Arrange\r\n      const invalidProps = {\r\n        ...validProps,\r\n        normalizationStatus: NormalizationStatus.COMPLETED,\r\n        normalizationCompletedAt: new Date(),\r\n        normalizationResult: undefined,\r\n      };\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('Completed normalization must have result');\r\n    });\r\n\r\n    it('should require error information for failed normalization', () => {\r\n      // Arrange\r\n      const invalidProps = {\r\n        ...validProps,\r\n        normalizationStatus: NormalizationStatus.FAILED,\r\n        lastNormalizationError: undefined,\r\n        normalizationResult: undefined,\r\n      };\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('Failed normalization must have error information');\r\n    });\r\n\r\n    it('should require start timestamp for in-progress normalization', () => {\r\n      // Arrange\r\n      const invalidProps = {\r\n        ...validProps,\r\n        normalizationStatus: NormalizationStatus.IN_PROGRESS,\r\n        normalizationStartedAt: undefined,\r\n      };\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEvent.create(invalidProps)).toThrow('In-progress normalization must have start timestamp');\r\n    });\r\n  });\r\n\r\n  describe('business methods', () => {\r\n    let normalizedEvent: NormalizedEvent;\r\n\r\n    beforeEach(() => {\r\n      const pendingProps = {\r\n        ...validProps,\r\n        normalizationStatus: NormalizationStatus.PENDING,\r\n        normalizationCompletedAt: undefined,\r\n        normalizationResult: undefined,\r\n      };\r\n      normalizedEvent = NormalizedEvent.create(pendingProps);\r\n    });\r\n\r\n    describe('startNormalization', () => {\r\n      it('should start normalization for pending events', () => {\r\n        // Act\r\n        normalizedEvent.startNormalization();\r\n\r\n        // Assert\r\n        expect(normalizedEvent.normalizationStatus).toBe(NormalizationStatus.IN_PROGRESS);\r\n        expect(normalizedEvent.normalizationStartedAt).toBeDefined();\r\n        expect(normalizedEvent.normalizationAttempts).toBe(1);\r\n      });\r\n\r\n      it('should throw error when starting normalization for non-pending events', () => {\r\n        // Arrange\r\n        normalizedEvent.startNormalization();\r\n\r\n        // Act & Assert\r\n        expect(() => normalizedEvent.startNormalization()).toThrow('Can only start normalization for pending events');\r\n      });\r\n    });\r\n\r\n    describe('completeNormalization', () => {\r\n      let mockResult: NormalizationResult;\r\n\r\n      beforeEach(() => {\r\n        mockResult = {\r\n          success: true,\r\n          appliedRules: ['rule1', 'rule2'],\r\n          failedRules: [],\r\n          warnings: [],\r\n          errors: [],\r\n          processingDurationMs: 1000,\r\n          confidenceScore: 90,\r\n        };\r\n        normalizedEvent.startNormalization();\r\n      });\r\n\r\n      it('should complete normalization for in-progress events', () => {\r\n        // Act\r\n        normalizedEvent.completeNormalization(mockResult);\r\n\r\n        // Assert\r\n        expect(normalizedEvent.normalizationStatus).toBe(NormalizationStatus.COMPLETED);\r\n        expect(normalizedEvent.normalizationCompletedAt).toBeDefined();\r\n        expect(normalizedEvent.normalizationResult).toEqual(mockResult);\r\n        expect(normalizedEvent.lastNormalizationError).toBeUndefined();\r\n      });\r\n\r\n      it('should generate domain event when normalization completes', () => {\r\n        // Act\r\n        normalizedEvent.completeNormalization(mockResult);\r\n\r\n        // Assert\r\n        const domainEvents = normalizedEvent.getUncommittedEvents();\r\n        const statusChangedEvent = domainEvents.find(e => e.constructor.name === 'NormalizedEventStatusChangedDomainEvent');\r\n        expect(statusChangedEvent).toBeDefined();\r\n      });\r\n\r\n      it('should throw error when completing normalization for non-in-progress events', () => {\r\n        // Arrange\r\n        normalizedEvent.completeNormalization(mockResult);\r\n\r\n        // Act & Assert\r\n        expect(() => normalizedEvent.completeNormalization(mockResult)).toThrow('Can only complete normalization for in-progress events');\r\n      });\r\n    });\r\n\r\n    describe('failNormalization', () => {\r\n      beforeEach(() => {\r\n        normalizedEvent.startNormalization();\r\n      });\r\n\r\n      it('should fail normalization for in-progress events', () => {\r\n        // Act\r\n        normalizedEvent.failNormalization('Test error');\r\n\r\n        // Assert\r\n        expect(normalizedEvent.normalizationStatus).toBe(NormalizationStatus.FAILED);\r\n        expect(normalizedEvent.lastNormalizationError).toBe('Test error');\r\n      });\r\n\r\n      it('should generate domain event when normalization fails', () => {\r\n        // Act\r\n        normalizedEvent.failNormalization('Test error');\r\n\r\n        // Assert\r\n        const domainEvents = normalizedEvent.getUncommittedEvents();\r\n        const failedEvent = domainEvents.find(e => e.constructor.name === 'NormalizedEventValidationFailedDomainEvent');\r\n        expect(failedEvent).toBeDefined();\r\n      });\r\n\r\n      it('should throw error when failing normalization for non-in-progress events', () => {\r\n        // Arrange\r\n        normalizedEvent.failNormalization('Test error');\r\n\r\n        // Act & Assert\r\n        expect(() => normalizedEvent.failNormalization('Another error')).toThrow('Can only fail normalization for in-progress events');\r\n      });\r\n    });\r\n\r\n    describe('skipNormalization', () => {\r\n      it('should skip normalization for pending events', () => {\r\n        // Act\r\n        normalizedEvent.skipNormalization('Test reason');\r\n\r\n        // Assert\r\n        expect(normalizedEvent.normalizationStatus).toBe(NormalizationStatus.SKIPPED);\r\n        expect(normalizedEvent.reviewNotes).toBe('Test reason');\r\n      });\r\n\r\n      it('should skip normalization for failed events', () => {\r\n        // Arrange\r\n        normalizedEvent.startNormalization();\r\n        normalizedEvent.failNormalization('Test error');\r\n\r\n        // Act\r\n        normalizedEvent.skipNormalization('Skip reason');\r\n\r\n        // Assert\r\n        expect(normalizedEvent.normalizationStatus).toBe(NormalizationStatus.SKIPPED);\r\n      });\r\n\r\n      it('should throw error when skipping normalization for invalid statuses', () => {\r\n        // Arrange\r\n        normalizedEvent.startNormalization();\r\n\r\n        // Act & Assert\r\n        expect(() => normalizedEvent.skipNormalization('Test reason')).toThrow('Can only skip normalization for pending or failed events');\r\n      });\r\n    });\r\n\r\n    describe('resetNormalization', () => {\r\n      it('should reset normalization for failed events', () => {\r\n        // Arrange\r\n        normalizedEvent.startNormalization();\r\n        normalizedEvent.failNormalization('Test error');\r\n\r\n        // Act\r\n        normalizedEvent.resetNormalization();\r\n\r\n        // Assert\r\n        expect(normalizedEvent.normalizationStatus).toBe(NormalizationStatus.PENDING);\r\n        expect(normalizedEvent.normalizationStartedAt).toBeUndefined();\r\n        expect(normalizedEvent.lastNormalizationError).toBeUndefined();\r\n      });\r\n\r\n      it('should throw error when resetting after max attempts exceeded', () => {\r\n        // Arrange\r\n        const propsWithMaxAttempts = {\r\n          ...validProps,\r\n          normalizationStatus: NormalizationStatus.FAILED,\r\n          normalizationAttempts: 3,\r\n        };\r\n        const eventWithMaxAttempts = NormalizedEvent.create(propsWithMaxAttempts);\r\n\r\n        // Act & Assert\r\n        expect(() => eventWithMaxAttempts.resetNormalization()).toThrow('Cannot reset normalization: maximum attempts exceeded');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('data management', () => {\r\n    let normalizedEvent: NormalizedEvent;\r\n\r\n    beforeEach(() => {\r\n      normalizedEvent = NormalizedEvent.create(validProps);\r\n    });\r\n\r\n    describe('updateNormalizedData', () => {\r\n      it('should update normalized data for non-completed events', () => {\r\n        // Arrange\r\n        const pendingProps = {\r\n          ...validProps,\r\n          normalizationStatus: NormalizationStatus.PENDING,\r\n        };\r\n        const pendingEvent = NormalizedEvent.create(pendingProps);\r\n        const newData = { updated: 'data' };\r\n\r\n        // Act\r\n        pendingEvent.updateNormalizedData(newData);\r\n\r\n        // Assert\r\n        expect(pendingEvent.normalizedData).toEqual(newData);\r\n      });\r\n\r\n      it('should throw error when updating normalized data for completed events', () => {\r\n        // Arrange\r\n        const newData = { updated: 'data' };\r\n\r\n        // Act & Assert\r\n        expect(() => normalizedEvent.updateNormalizedData(newData)).toThrow('Cannot update normalized data for completed normalization');\r\n      });\r\n    });\r\n\r\n    describe('addAppliedRule', () => {\r\n      it('should add new applied rule', () => {\r\n        // Arrange\r\n        const newRule: NormalizationRule = {\r\n          id: 'new-rule',\r\n          name: 'New Rule',\r\n          description: 'New normalization rule',\r\n          priority: 50,\r\n          required: true,\r\n        };\r\n\r\n        // Act\r\n        normalizedEvent.addAppliedRule(newRule);\r\n\r\n        // Assert\r\n        expect(normalizedEvent.appliedRules).toContain(newRule);\r\n      });\r\n\r\n      it('should not add duplicate rules', () => {\r\n        // Arrange\r\n        const initialCount = normalizedEvent.appliedRules.length;\r\n\r\n        // Act\r\n        normalizedEvent.addAppliedRule(mockRule);\r\n\r\n        // Assert\r\n        expect(normalizedEvent.appliedRules).toHaveLength(initialCount);\r\n      });\r\n    });\r\n\r\n    describe('updateDataQualityScore', () => {\r\n      it('should update data quality score within valid range', () => {\r\n        // Act\r\n        normalizedEvent.updateDataQualityScore(75);\r\n\r\n        // Assert\r\n        expect(normalizedEvent.dataQualityScore).toBe(75);\r\n      });\r\n\r\n      it('should throw error for invalid data quality score', () => {\r\n        // Act & Assert\r\n        expect(() => normalizedEvent.updateDataQualityScore(150)).toThrow('Data quality score must be between 0 and 100');\r\n      });\r\n    });\r\n\r\n    describe('validation errors management', () => {\r\n      it('should add validation errors', () => {\r\n        // Arrange\r\n        const errors = ['Error 1', 'Error 2'];\r\n\r\n        // Act\r\n        normalizedEvent.addValidationErrors(errors);\r\n\r\n        // Assert\r\n        expect(normalizedEvent.validationErrors).toEqual(errors);\r\n      });\r\n\r\n      it('should clear validation errors', () => {\r\n        // Arrange\r\n        normalizedEvent.addValidationErrors(['Error 1']);\r\n\r\n        // Act\r\n        normalizedEvent.clearValidationErrors();\r\n\r\n        // Assert\r\n        expect(normalizedEvent.validationErrors).toEqual([]);\r\n      });\r\n\r\n      it('should throw error when adding too many validation errors', () => {\r\n        // Arrange\r\n        const tooManyErrors = Array(15).fill('error');\r\n\r\n        // Act & Assert\r\n        expect(() => normalizedEvent.addValidationErrors(tooManyErrors)).toThrow('Cannot have more than 10 validation errors');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('manual review', () => {\r\n    let normalizedEvent: NormalizedEvent;\r\n\r\n    beforeEach(() => {\r\n      normalizedEvent = NormalizedEvent.create(validProps);\r\n    });\r\n\r\n    describe('markForManualReview', () => {\r\n      it('should mark event for manual review', () => {\r\n        // Act\r\n        normalizedEvent.markForManualReview('High risk event');\r\n\r\n        // Assert\r\n        expect(normalizedEvent.requiresManualReview).toBe(true);\r\n        expect(normalizedEvent.reviewNotes).toBe('High risk event');\r\n      });\r\n    });\r\n\r\n    describe('completeManualReview', () => {\r\n      it('should complete manual review for events marked for review', () => {\r\n        // Arrange\r\n        normalizedEvent.markForManualReview('Test reason');\r\n\r\n        // Act\r\n        normalizedEvent.completeManualReview('<EMAIL>', 'Review completed');\r\n\r\n        // Assert\r\n        expect(normalizedEvent.reviewedBy).toBe('<EMAIL>');\r\n        expect(normalizedEvent.reviewedAt).toBeDefined();\r\n        expect(normalizedEvent.reviewNotes).toBe('Review completed');\r\n      });\r\n\r\n      it('should throw error when completing review for events not marked for review', () => {\r\n        // Act & Assert\r\n        expect(() => normalizedEvent.completeManualReview('<EMAIL>')).toThrow('Event is not marked for manual review');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('query methods', () => {\r\n    let normalizedEvent: NormalizedEvent;\r\n\r\n    beforeEach(() => {\r\n      normalizedEvent = NormalizedEvent.create(validProps);\r\n    });\r\n\r\n    it('should check if normalization is completed', () => {\r\n      expect(normalizedEvent.isNormalizationCompleted()).toBe(true);\r\n    });\r\n\r\n    it('should check if normalization failed', () => {\r\n      expect(normalizedEvent.isNormalizationFailed()).toBe(false);\r\n    });\r\n\r\n    it('should check if normalization is in progress', () => {\r\n      expect(normalizedEvent.isNormalizationInProgress()).toBe(false);\r\n    });\r\n\r\n    it('should check if normalization was skipped', () => {\r\n      expect(normalizedEvent.isNormalizationSkipped()).toBe(false);\r\n    });\r\n\r\n    it('should check if event has high data quality', () => {\r\n      expect(normalizedEvent.hasHighDataQuality()).toBe(true);\r\n    });\r\n\r\n    it('should check if event has validation errors', () => {\r\n      expect(normalizedEvent.hasValidationErrors()).toBe(false);\r\n    });\r\n\r\n    it('should check if event has exceeded max normalization attempts', () => {\r\n      expect(normalizedEvent.hasExceededMaxNormalizationAttempts()).toBe(false);\r\n    });\r\n\r\n    it('should check if event is ready for next stage', () => {\r\n      expect(normalizedEvent.isReadyForNextStage()).toBe(true);\r\n    });\r\n\r\n    it('should check if event is high risk', () => {\r\n      // Arrange\r\n      const highRiskProps = { ...validProps, riskScore: 85 };\r\n      const highRiskEvent = NormalizedEvent.create(highRiskProps);\r\n\r\n      // Assert\r\n      expect(highRiskEvent.isHighRisk()).toBe(true);\r\n    });\r\n\r\n    it('should get normalization duration', () => {\r\n      // Arrange\r\n      const startTime = new Date();\r\n      const endTime = new Date(startTime.getTime() + 5000);\r\n      const propsWithDuration = {\r\n        ...validProps,\r\n        normalizationStartedAt: startTime,\r\n        normalizationCompletedAt: endTime,\r\n      };\r\n      const eventWithDuration = NormalizedEvent.create(propsWithDuration);\r\n\r\n      // Act\r\n      const duration = eventWithDuration.getNormalizationDuration();\r\n\r\n      // Assert\r\n      expect(duration).toBe(5000);\r\n    });\r\n\r\n    it('should get applied rule names', () => {\r\n      // Act\r\n      const ruleNames = normalizedEvent.getAppliedRuleNames();\r\n\r\n      // Assert\r\n      expect(ruleNames).toEqual(['Test Rule']);\r\n    });\r\n\r\n    it('should check if specific rule was applied', () => {\r\n      // Act & Assert\r\n      expect(normalizedEvent.hasAppliedRule('test-rule')).toBe(true);\r\n      expect(normalizedEvent.hasAppliedRule('non-existent-rule')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    it('should convert to JSON representation', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(validProps);\r\n\r\n      // Act\r\n      const json = normalizedEvent.toJSON();\r\n\r\n      // Assert\r\n      expect(json).toHaveProperty('id');\r\n      expect(json).toHaveProperty('originalEventId');\r\n      expect(json).toHaveProperty('type', EventType.THREAT_DETECTED);\r\n      expect(json).toHaveProperty('severity', EventSeverity.HIGH);\r\n      expect(json).toHaveProperty('normalizationStatus', NormalizationStatus.COMPLETED);\r\n      expect(json).toHaveProperty('schemaVersion', '1.0.0');\r\n      expect(json).toHaveProperty('summary');\r\n      expect(json.summary).toHaveProperty('isReadyForNextStage');\r\n    });\r\n\r\n    it('should get event summary', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(validProps);\r\n\r\n      // Act\r\n      const summary = normalizedEvent.getSummary();\r\n\r\n      // Assert\r\n      expect(summary).toHaveProperty('id');\r\n      expect(summary).toHaveProperty('originalEventId');\r\n      expect(summary).toHaveProperty('title', 'Test Normalized Event');\r\n      expect(summary).toHaveProperty('type', EventType.THREAT_DETECTED);\r\n      expect(summary).toHaveProperty('severity', EventSeverity.HIGH);\r\n      expect(summary).toHaveProperty('normalizationStatus', NormalizationStatus.COMPLETED);\r\n      expect(summary).toHaveProperty('dataQualityScore', 85);\r\n      expect(summary).toHaveProperty('appliedRulesCount', 1);\r\n      expect(summary).toHaveProperty('hasValidationErrors', false);\r\n      expect(summary).toHaveProperty('requiresManualReview', false);\r\n      expect(summary).toHaveProperty('isReadyForNextStage', true);\r\n    });\r\n  });\r\n});"], "version": 3}