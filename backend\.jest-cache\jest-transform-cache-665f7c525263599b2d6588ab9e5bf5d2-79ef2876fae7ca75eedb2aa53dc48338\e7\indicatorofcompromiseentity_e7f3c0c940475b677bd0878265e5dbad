629c4b4dd8ec777531ca485ba8d8779d
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndicatorOfCompromise = exports.IOCConfidence = exports.IOCStatus = exports.IOCType = void 0;
const typeorm_1 = require("typeorm");
const class_validator_1 = require("class-validator");
const threat_intelligence_entity_1 = require("./threat-intelligence.entity");
/**
 * IOC types based on common threat intelligence standards
 */
var IOCType;
(function (IOCType) {
    // Network indicators
    IOCType["IP_ADDRESS"] = "ip_address";
    IOCType["DOMAIN"] = "domain";
    IOCType["URL"] = "url";
    IOCType["EMAIL"] = "email";
    IOCType["USER_AGENT"] = "user_agent";
    // File indicators
    IOCType["FILE_HASH_MD5"] = "file_hash_md5";
    IOCType["FILE_HASH_SHA1"] = "file_hash_sha1";
    IOCType["FILE_HASH_SHA256"] = "file_hash_sha256";
    IOCType["FILE_NAME"] = "file_name";
    IOCType["FILE_PATH"] = "file_path";
    IOCType["FILE_SIZE"] = "file_size";
    // Registry indicators
    IOCType["REGISTRY_KEY"] = "registry_key";
    IOCType["REGISTRY_VALUE"] = "registry_value";
    // Process indicators
    IOCType["PROCESS_NAME"] = "process_name";
    IOCType["PROCESS_PATH"] = "process_path";
    IOCType["COMMAND_LINE"] = "command_line";
    // Certificate indicators
    IOCType["CERTIFICATE_FINGERPRINT"] = "certificate_fingerprint";
    IOCType["CERTIFICATE_SERIAL"] = "certificate_serial";
    // Other indicators
    IOCType["MUTEX"] = "mutex";
    IOCType["PIPE_NAME"] = "pipe_name";
    IOCType["SERVICE_NAME"] = "service_name";
    IOCType["YARA_RULE"] = "yara_rule";
    IOCType["CUSTOM"] = "custom";
})(IOCType || (exports.IOCType = IOCType = {}));
/**
 * IOC status
 */
var IOCStatus;
(function (IOCStatus) {
    IOCStatus["ACTIVE"] = "active";
    IOCStatus["INACTIVE"] = "inactive";
    IOCStatus["EXPIRED"] = "expired";
    IOCStatus["FALSE_POSITIVE"] = "false_positive";
    IOCStatus["WHITELISTED"] = "whitelisted";
    IOCStatus["UNDER_REVIEW"] = "under_review";
})(IOCStatus || (exports.IOCStatus = IOCStatus = {}));
/**
 * IOC confidence levels
 */
var IOCConfidence;
(function (IOCConfidence) {
    IOCConfidence["HIGH"] = "high";
    IOCConfidence["MEDIUM"] = "medium";
    IOCConfidence["LOW"] = "low";
    IOCConfidence["UNKNOWN"] = "unknown";
})(IOCConfidence || (exports.IOCConfidence = IOCConfidence = {}));
/**
 * Indicator of Compromise entity
 * Represents specific observable artifacts that indicate potential compromise
 */
let IndicatorOfCompromise = class IndicatorOfCompromise {
    /**
     * Check if IOC is currently active
     */
    get isActive() {
        return this.status === IOCStatus.ACTIVE &&
            !this.isWhitelisted &&
            !this.isFalsePositive &&
            (!this.expiresAt || this.expiresAt > new Date());
    }
    /**
     * Check if IOC is expired
     */
    get isExpired() {
        return this.expiresAt ? this.expiresAt <= new Date() : false;
    }
    /**
     * Get age in days
     */
    get ageInDays() {
        return Math.floor((Date.now() - this.firstSeen.getTime()) / (1000 * 60 * 60 * 24));
    }
    /**
     * Get IOC summary
     */
    getSummary() {
        return {
            id: this.id,
            iocType: this.iocType,
            value: this.value,
            status: this.status,
            confidence: this.confidence,
            isActive: this.isActive,
            isExpired: this.isExpired,
            ageInDays: this.ageInDays,
            hitCount: this.hitCount,
            lastHit: this.lastHit,
            severity: this.severity,
            isWhitelisted: this.isWhitelisted,
            isFalsePositive: this.isFalsePositive,
            threatIntelligenceId: this.threatIntelligenceId,
            tags: this.tags,
        };
    }
    /**
     * Record a hit/detection of this IOC
     */
    recordHit(context) {
        this.hitCount += 1;
        this.lastHit = new Date();
        this.lastSeen = new Date();
        if (context) {
            this.contextualData = {
                ...this.contextualData,
                lastHitContext: context,
            };
        }
    }
    /**
     * Validate IOC value based on type
     */
    validateValue() {
        const value = this.value.trim();
        switch (this.iocType) {
            case IOCType.IP_ADDRESS:
                return this.isValidIPAddress(value);
            case IOCType.DOMAIN:
                return this.isValidDomain(value);
            case IOCType.URL:
                return this.isValidURL(value);
            case IOCType.EMAIL:
                return this.isValidEmail(value);
            case IOCType.FILE_HASH_MD5:
                return /^[a-fA-F0-9]{32}$/.test(value);
            case IOCType.FILE_HASH_SHA1:
                return /^[a-fA-F0-9]{40}$/.test(value);
            case IOCType.FILE_HASH_SHA256:
                return /^[a-fA-F0-9]{64}$/.test(value);
            default:
                return value.length > 0;
        }
    }
    /**
     * Normalize IOC value based on type
     */
    normalizeValue() {
        let normalized = this.value.trim();
        switch (this.iocType) {
            case IOCType.IP_ADDRESS:
                // Remove leading zeros from IP octets
                normalized = normalized.replace(/\b0+(\d)/g, '$1');
                break;
            case IOCType.DOMAIN:
                normalized = normalized.toLowerCase();
                // Remove trailing dot
                if (normalized.endsWith('.')) {
                    normalized = normalized.slice(0, -1);
                }
                break;
            case IOCType.URL:
                normalized = normalized.toLowerCase();
                break;
            case IOCType.EMAIL:
                normalized = normalized.toLowerCase();
                break;
            case IOCType.FILE_HASH_MD5:
            case IOCType.FILE_HASH_SHA1:
            case IOCType.FILE_HASH_SHA256:
                normalized = normalized.toLowerCase();
                break;
        }
        this.value = normalized;
    }
    /**
     * Add enrichment data
     */
    addEnrichment(source, data, confidence = 0.8) {
        if (!this.enrichmentData) {
            this.enrichmentData = [];
        }
        // Remove existing enrichment from same source
        this.enrichmentData = this.enrichmentData.filter(e => e.source !== source);
        // Add new enrichment
        this.enrichmentData.push({
            source,
            enrichedAt: new Date(),
            data,
            confidence,
        });
    }
    /**
     * Mark as false positive
     */
    markAsFalsePositive(reason, analyst) {
        this.isFalsePositive = true;
        this.falsePositiveReason = reason;
        this.status = IOCStatus.FALSE_POSITIVE;
        if (analyst) {
            this.customAttributes = {
                ...this.customAttributes,
                falsePositiveAnalyst: analyst,
                falsePositiveDate: new Date(),
            };
        }
    }
    /**
     * Add to whitelist
     */
    addToWhitelist(reason, analyst) {
        this.isWhitelisted = true;
        this.whitelistReason = reason;
        this.status = IOCStatus.WHITELISTED;
        if (analyst) {
            this.customAttributes = {
                ...this.customAttributes,
                whitelistAnalyst: analyst,
                whitelistDate: new Date(),
            };
        }
    }
    /**
     * Remove from whitelist
     */
    removeFromWhitelist() {
        this.isWhitelisted = false;
        this.whitelistReason = null;
        this.status = IOCStatus.ACTIVE;
    }
    /**
     * Calculate threat score based on various factors
     */
    calculateThreatScore() {
        let score = 0;
        // Base score from confidence
        const confidenceScores = {
            [IOCConfidence.HIGH]: 8.0,
            [IOCConfidence.MEDIUM]: 6.0,
            [IOCConfidence.LOW]: 4.0,
            [IOCConfidence.UNKNOWN]: 2.0,
        };
        score += confidenceScores[this.confidence];
        // Hit frequency boost
        if (this.hitCount > 10) {
            score += 2.0;
        }
        else if (this.hitCount > 5) {
            score += 1.0;
        }
        else if (this.hitCount > 0) {
            score += 0.5;
        }
        // Recency factor
        const daysSinceLastHit = this.lastHit ?
            Math.floor((Date.now() - this.lastHit.getTime()) / (1000 * 60 * 60 * 24)) :
            this.ageInDays;
        if (daysSinceLastHit <= 1) {
            score += 2.0; // Very recent
        }
        else if (daysSinceLastHit <= 7) {
            score += 1.0; // Recent
        }
        else if (daysSinceLastHit <= 30) {
            score += 0.5; // Somewhat recent
        }
        // Threat intelligence association
        if (this.threatIntelligence) {
            score += 1.0;
        }
        // Enrichment data quality
        if (this.enrichmentData && this.enrichmentData.length > 0) {
            const avgConfidence = this.enrichmentData.reduce((sum, e) => sum + e.confidence, 0) / this.enrichmentData.length;
            score += avgConfidence;
        }
        return Math.min(score, 10.0);
    }
    /**
     * Export IOC for reporting
     */
    exportForReporting() {
        return {
            id: this.id,
            iocType: this.iocType,
            value: this.value,
            description: this.description,
            status: this.status,
            confidence: this.confidence,
            firstSeen: this.firstSeen,
            lastSeen: this.lastSeen,
            expiresAt: this.expiresAt,
            tags: this.tags,
            hitCount: this.hitCount,
            lastHit: this.lastHit,
            severity: this.severity,
            isWhitelisted: this.isWhitelisted,
            isFalsePositive: this.isFalsePositive,
            threatScore: this.calculateThreatScore(),
            threatIntelligenceId: this.threatIntelligenceId,
            enrichmentSources: this.enrichmentData?.map(e => e.source) || [],
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
    // Private validation methods
    isValidIPAddress(ip) {
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
        return ipv4Regex.test(ip) || ipv6Regex.test(ip);
    }
    isValidDomain(domain) {
        const domainRegex = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
        return domainRegex.test(domain);
    }
    isValidURL(url) {
        try {
            new URL(url);
            return true;
        }
        catch {
            return false;
        }
    }
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
};
exports.IndicatorOfCompromise = IndicatorOfCompromise;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], IndicatorOfCompromise.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: IOCType,
    }),
    (0, class_validator_1.IsEnum)(IOCType),
    __metadata("design:type", String)
], IndicatorOfCompromise.prototype, "iocType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], IndicatorOfCompromise.prototype, "value", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], IndicatorOfCompromise.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: IOCStatus,
        default: IOCStatus.ACTIVE,
    }),
    (0, class_validator_1.IsEnum)(IOCStatus),
    __metadata("design:type", String)
], IndicatorOfCompromise.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: IOCConfidence,
        default: IOCConfidence.MEDIUM,
    }),
    (0, class_validator_1.IsEnum)(IOCConfidence),
    __metadata("design:type", String)
], IndicatorOfCompromise.prototype, "confidence", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], IndicatorOfCompromise.prototype, "firstSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], IndicatorOfCompromise.prototype, "lastSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], IndicatorOfCompromise.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], IndicatorOfCompromise.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], IndicatorOfCompromise.prototype, "detectionContext", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], IndicatorOfCompromise.prototype, "enrichmentData", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', default: 0 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], IndicatorOfCompromise.prototype, "hitCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], IndicatorOfCompromise.prototype, "lastHit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 1, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], IndicatorOfCompromise.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], IndicatorOfCompromise.prototype, "isWhitelisted", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], IndicatorOfCompromise.prototype, "whitelistReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], IndicatorOfCompromise.prototype, "isFalsePositive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], IndicatorOfCompromise.prototype, "falsePositiveReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_e = typeof Record !== "undefined" && Record) === "function" ? _e : Object)
], IndicatorOfCompromise.prototype, "contextualData", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], IndicatorOfCompromise.prototype, "relatedIOCs", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_f = typeof Record !== "undefined" && Record) === "function" ? _f : Object)
], IndicatorOfCompromise.prototype, "customAttributes", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => threat_intelligence_entity_1.ThreatIntelligence, (threat) => threat.indicators, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'threat_intelligence_id' }),
    __metadata("design:type", typeof (_g = typeof threat_intelligence_entity_1.ThreatIntelligence !== "undefined" && threat_intelligence_entity_1.ThreatIntelligence) === "function" ? _g : Object)
], IndicatorOfCompromise.prototype, "threatIntelligence", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], IndicatorOfCompromise.prototype, "threatIntelligenceId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_h = typeof Date !== "undefined" && Date) === "function" ? _h : Object)
], IndicatorOfCompromise.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_j = typeof Date !== "undefined" && Date) === "function" ? _j : Object)
], IndicatorOfCompromise.prototype, "updatedAt", void 0);
exports.IndicatorOfCompromise = IndicatorOfCompromise = __decorate([
    (0, typeorm_1.Entity)('indicators_of_compromise'),
    (0, typeorm_1.Index)(['iocType', 'status']),
    (0, typeorm_1.Index)(['value'], { unique: false }) // Allow duplicates with different contexts
    ,
    (0, typeorm_1.Index)(['threatIntelligenceId']),
    (0, typeorm_1.Index)(['firstSeen', 'lastSeen']),
    (0, typeorm_1.Index)(['confidence', 'status'])
], IndicatorOfCompromise);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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