{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\tenant-id.spec.ts", "mappings": ";;AAAA,yCAA+B;AAE/B,yCAAqC;AA6BrC,yCAAuC;AAqBvC,uFAAsE;AACtE,qGAAmF;AAEnF,IAAA,oBAAQ,EAAC,UAAU,EAAE,GAAG,EAAE;IACxB,IAAA,oBAAQ,EAAC,UAAU,EAAE,GAAG,EAAE;QACxB,IAAA,cAAE,EAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,QAAQ,GAAG,iCAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAE3C,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,QAAQ,GAAG,iCAAQ,CAAC,QAAQ,EAAE,CAAC;YAErC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,iCAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,QAAQ,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,QAAQ,GAAG,iCAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAEvD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,QAAQ,GAAG,iCAAQ,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,IAAI,GAAG,iCAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAEvC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,IAAA,cAAE,EAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iCAAQ,CAAC,IAAW,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iCAAQ,CAAC,SAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iCAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iCAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iCAAQ,CAAC,GAAU,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iCAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iCAAQ,CAAC,sCAAsC,CAAC,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QACxG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,IAAA,cAAE,EAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,SAAS,GAAG,sCAAsC,CAAC;YACzD,MAAM,CAAC,iCAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,iCAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,iCAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,iCAAQ,CAAC,OAAO,CAAC,IAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,iCAAQ,CAAC,OAAO,CAAC,SAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,SAAS,GAAG,sCAAsC,CAAC;YACzD,MAAM,MAAM,GAAG,iCAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAE5C,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,CAAC,MAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,MAAM,GAAG,iCAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,IAAI,QAAkB,CAAC;QAEvB,IAAA,sBAAU,EAAC,GAAG,EAAE;YACd,QAAQ,GAAG,iCAAQ,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC7C,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,aAAa,GAAG,QAAQ,CAAC,eAAe,EAAE,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,IAAA,cAAE,EAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,QAAQ,GAAG,iCAAQ,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,SAAS,GAAG,iCAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,SAAS,GAAG,iCAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAE5C,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,SAAS,GAAG,iCAAQ,CAAC,QAAQ,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,iCAAQ,CAAC,QAAQ,EAAE,CAAC;YAEtC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,QAAQ,GAAG,iCAAQ,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,QAAQ,GAAG,iCAAQ,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,IAAA,cAAE,EAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,YAAY,GAAG,iCAAQ,CAAC,MAAM,EAAE,CAAC;YACvC,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,aAAa,GAAG,iCAAQ,CAAC,OAAO,EAAE,CAAC;YACzC,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,OAAO,GAAG,iCAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,OAAO,GAAG,iCAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,OAAO,GAAG,iCAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC3C,MAAM,OAAO,GAAG,iCAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,IAAA,cAAE,EAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,SAAS,GAAG,iCAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE3C,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,uBAAuB;YACvB,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAC5D,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,SAAS,GAAG,iCAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAA,cAAE,EAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,QAAQ,GAAG,iCAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,QAAQ,GAAG,iCAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;YAE/B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,IAAI,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,iCAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEzC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,IAAA,cAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,QAAQ,GAAG,iCAAQ,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC;YAErC,wDAAwD;YACxD,MAAM,CAAC,GAAG,EAAE;gBACT,QAAgB,CAAC,MAAM,GAAG,UAAU,CAAC;YACxC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,iDAAiD;YAE/D,gCAAgC;YAChC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,kBAAkB,EAAE,GAAG,EAAE;YAC1B,MAAM,QAAQ,GAAG,iCAAQ,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,IAAA,cAAE,EAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,KAAK,GAAG,sCAAsC,CAAC;YACrD,MAAM,KAAK,GAAG,sCAAsC,CAAC;YAErD,MAAM,SAAS,GAAG,iCAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,SAAS,GAAG,iCAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAE7C,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,QAAQ,GAAG,iCAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\tenant-id.spec.ts"], "sourcesContent": ["import { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { TenantId } from '../../value-objects/tenant-id.value-object';\r\nimport { UniqueEntityId } from '../../value-objects/unique-entity-id.value-object';\r\n\r\ndescribe('TenantId', () => {\r\n  describe('creation', () => {\r\n    it('should create a valid tenant ID from UUID string', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const tenantId = TenantId.fromString(uuid);\r\n\r\n      expect(tenantId.value).toBe(uuid);\r\n      expect(tenantId.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should generate a new tenant ID', () => {\r\n      const tenantId = TenantId.generate();\r\n\r\n      expect(tenantId.value).toBeDefined();\r\n      expect(tenantId.isValid()).toBe(true);\r\n      expect(TenantId.isValid(tenantId.value)).toBe(true);\r\n    });\r\n\r\n    it('should create tenant ID from UniqueEntityId', () => {\r\n      const uniqueId = UniqueEntityId.generate();\r\n      const tenantId = TenantId.fromUniqueEntityId(uniqueId);\r\n\r\n      expect(tenantId.value).toBe(uniqueId.value);\r\n      expect(tenantId.uniqueId.equals(uniqueId)).toBe(true);\r\n    });\r\n\r\n    it('should create tenant ID from existing ID', () => {\r\n      const original = TenantId.generate();\r\n      const copy = TenantId.fromId(original);\r\n\r\n      expect(copy.value).toBe(original.value);\r\n      expect(copy.equals(original)).toBe(true);\r\n      expect(copy).not.toBe(original); // Different instances\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should throw error for null value', () => {\r\n      expect(() => new TenantId(null as any)).toThrow('TenantId cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for undefined value', () => {\r\n      expect(() => new TenantId(undefined as any)).toThrow('TenantId cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for empty string', () => {\r\n      expect(() => new TenantId('')).toThrow('TenantId cannot be empty');\r\n    });\r\n\r\n    it('should throw error for whitespace string', () => {\r\n      expect(() => new TenantId('   ')).toThrow('TenantId cannot be empty');\r\n    });\r\n\r\n    it('should throw error for non-string value', () => {\r\n      expect(() => new TenantId(123 as any)).toThrow('TenantId must be a string');\r\n    });\r\n\r\n    it('should throw error for invalid UUID format', () => {\r\n      expect(() => new TenantId('invalid-uuid')).toThrow('Invalid TenantId format');\r\n    });\r\n\r\n    it('should throw error for non-UUID v4 format', () => {\r\n      expect(() => new TenantId('123e4567-e89b-22d3-a456-************')).toThrow('Invalid TenantId format');\r\n    });\r\n  });\r\n\r\n  describe('static validation methods', () => {\r\n    it('should validate correct UUID strings', () => {\r\n      const validUuid = '123e4567-e89b-42d3-a456-************';\r\n      expect(TenantId.isValid(validUuid)).toBe(true);\r\n    });\r\n\r\n    it('should reject invalid UUID strings', () => {\r\n      expect(TenantId.isValid('invalid-uuid')).toBe(false);\r\n      expect(TenantId.isValid('')).toBe(false);\r\n      expect(TenantId.isValid(null as any)).toBe(false);\r\n      expect(TenantId.isValid(undefined as any)).toBe(false);\r\n    });\r\n\r\n    it('should try parse valid UUID', () => {\r\n      const validUuid = '123e4567-e89b-42d3-a456-************';\r\n      const result = TenantId.tryParse(validUuid);\r\n\r\n      expect(result).not.toBeNull();\r\n      expect(result!.value).toBe(validUuid);\r\n    });\r\n\r\n    it('should return null for invalid UUID in tryParse', () => {\r\n      const result = TenantId.tryParse('invalid-uuid');\r\n      expect(result).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('utility methods', () => {\r\n    let tenantId: TenantId;\r\n\r\n    beforeEach(() => {\r\n      tenantId = TenantId.fromString('123e4567-e89b-42d3-a456-************');\r\n    });\r\n\r\n    it('should get version', () => {\r\n      expect(tenantId.getVersion()).toBe(4);\r\n    });\r\n\r\n    it('should get variant', () => {\r\n      expect(tenantId.getVariant()).toBe('RFC4122');\r\n    });\r\n\r\n    it('should get short string representation', () => {\r\n      const shortString = tenantId.toShortString();\r\n      expect(shortString).toBe('123e4567');\r\n      expect(tenantId.toShortString(4)).toBe('123e');\r\n    });\r\n\r\n    it('should get compact string representation', () => {\r\n      const compactString = tenantId.toCompactString();\r\n      expect(compactString).toBe('123e4567e89b42d3a456************');\r\n    });\r\n\r\n    it('should convert to uppercase', () => {\r\n      const upperCase = tenantId.toUpperCase();\r\n      expect(upperCase).toBe('123E4567-E89B-42D3-A456-************');\r\n    });\r\n\r\n    it('should convert to lowercase', () => {\r\n      const lowerCase = tenantId.toLowerCase();\r\n      expect(lowerCase).toBe('123e4567-e89b-42d3-a456-************');\r\n    });\r\n  });\r\n\r\n  describe('equality and comparison', () => {\r\n    it('should be equal to itself', () => {\r\n      const tenantId = TenantId.generate();\r\n      expect(tenantId.equals(tenantId)).toBe(true);\r\n      expect(tenantId.matches(tenantId)).toBe(true);\r\n    });\r\n\r\n    it('should be equal to tenant ID with same value', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const tenantId1 = TenantId.fromString(uuid);\r\n      const tenantId2 = TenantId.fromString(uuid);\r\n\r\n      expect(tenantId1.equals(tenantId2)).toBe(true);\r\n      expect(tenantId1.matches(tenantId2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to tenant ID with different value', () => {\r\n      const tenantId1 = TenantId.generate();\r\n      const tenantId2 = TenantId.generate();\r\n\r\n      expect(tenantId1.equals(tenantId2)).toBe(false);\r\n      expect(tenantId1.matches(tenantId2)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to null or undefined', () => {\r\n      const tenantId = TenantId.generate();\r\n      expect(tenantId.equals(null as any)).toBe(false);\r\n      expect(tenantId.equals(undefined as any)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to non-TenantId object', () => {\r\n      const tenantId = TenantId.generate();\r\n      expect(tenantId.equals({} as any)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('special tenant IDs', () => {\r\n    it('should create system tenant ID', () => {\r\n      const systemTenant = TenantId.system();\r\n      expect(systemTenant.isSystem()).toBe(true);\r\n      expect(systemTenant.isDefault()).toBe(false);\r\n    });\r\n\r\n    it('should create default tenant ID', () => {\r\n      const defaultTenant = TenantId.default();\r\n      expect(defaultTenant.isDefault()).toBe(true);\r\n      expect(defaultTenant.isSystem()).toBe(false);\r\n    });\r\n\r\n    it('should create deterministic tenant ID from seed', () => {\r\n      const tenant1 = TenantId.fromSeed('test');\r\n      const tenant2 = TenantId.fromSeed('test');\r\n      expect(tenant1.equals(tenant2)).toBe(true);\r\n    });\r\n\r\n    it('should create different tenant IDs from different seeds', () => {\r\n      const tenant1 = TenantId.fromSeed('test1');\r\n      const tenant2 = TenantId.fromSeed('test2');\r\n      expect(tenant1.equals(tenant2)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('bulk operations', () => {\r\n    it('should generate multiple tenant IDs', () => {\r\n      const tenantIds = TenantId.generateMany(5);\r\n\r\n      expect(tenantIds).toHaveLength(5);\r\n      expect(tenantIds.every(id => id.isValid())).toBe(true);\r\n      \r\n      // All should be unique\r\n      const uniqueValues = new Set(tenantIds.map(id => id.value));\r\n      expect(uniqueValues.size).toBe(5);\r\n    });\r\n\r\n    it('should handle zero count for generateMany', () => {\r\n      const tenantIds = TenantId.generateMany(0);\r\n      expect(tenantIds).toHaveLength(0);\r\n    });\r\n\r\n    it('should throw error for negative count', () => {\r\n      expect(() => TenantId.generateMany(-1)).toThrow('Count must be non-negative');\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    it('should convert to string', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const tenantId = TenantId.fromString(uuid);\r\n      expect(tenantId.toString()).toBe(uuid);\r\n    });\r\n\r\n    it('should convert to JSON', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const tenantId = TenantId.fromString(uuid);\r\n      const json = tenantId.toJSON();\r\n\r\n      expect(json.value).toBe(uuid);\r\n      expect(json.type).toBe('TenantId');\r\n      expect(json.version).toBe(4);\r\n      expect(json.variant).toBe('RFC4122');\r\n      expect(json.shortString).toBe('123e4567');\r\n      expect(json.compactString).toBe('123e4567e89b42d3a456************');\r\n    });\r\n\r\n    it('should create from JSON', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const json = { value: uuid };\r\n      const tenantId = TenantId.fromJSON(json);\r\n\r\n      expect(tenantId.value).toBe(uuid);\r\n    });\r\n  });\r\n\r\n  describe('immutability', () => {\r\n    it('should be immutable after creation', () => {\r\n      const tenantId = TenantId.generate();\r\n      const originalValue = tenantId.value;\r\n\r\n      // Attempt to modify (should throw due to frozen object)\r\n      expect(() => {\r\n        (tenantId as any)._value = 'modified';\r\n      }).toThrow(); // Frozen objects throw when attempting to modify\r\n\r\n      // Value should remain unchanged\r\n      expect(tenantId.value).toBe(originalValue);\r\n    });\r\n\r\n    it('should be frozen', () => {\r\n      const tenantId = TenantId.generate();\r\n      expect(Object.isFrozen(tenantId)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle case-insensitive UUID comparison', () => {\r\n      const uuid1 = '123e4567-e89b-42d3-a456-************';\r\n      const uuid2 = '123e4567-e89b-42d3-a456-************';\r\n      \r\n      const tenantId1 = TenantId.fromString(uuid1);\r\n      const tenantId2 = TenantId.fromString(uuid2);\r\n      \r\n      expect(tenantId1.equals(tenantId2)).toBe(true);\r\n    });\r\n\r\n    it('should maintain original case in value', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const tenantId = TenantId.fromString(uuid);\r\n      expect(tenantId.value).toBe(uuid);\r\n    });\r\n  });\r\n});\r\n"], "version": 3}