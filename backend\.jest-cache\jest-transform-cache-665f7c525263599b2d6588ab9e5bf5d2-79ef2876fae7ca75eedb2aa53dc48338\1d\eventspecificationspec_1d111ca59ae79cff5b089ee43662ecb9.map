{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\event.specification.spec.ts", "mappings": ";;AAAA,gEAsBgC;AAChC,iEAA6D;AAE7D,iEAAwD;AACxD,yEAAgE;AAChE,qEAA4D;AAC5D,2FAAiF;AACjF,+EAAqE;AAGrE,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,IAAI,iBAAwB,CAAC;IAC7B,IAAI,gBAAuB,CAAC;IAC5B,IAAI,aAAoB,CAAC;IACzB,IAAI,WAAkB,CAAC;IACvB,IAAI,aAAoB,CAAC;IACzB,IAAI,aAAoB,CAAC;IACzB,IAAI,YAAmB,CAAC;IACxB,IAAI,WAAkB,CAAC;IACvB,IAAI,QAAe,CAAC;IAEpB,UAAU,CAAC,GAAG,EAAE;QACd,oDAAoD;QACpD,iBAAiB,GAAG,4BAAY,CAAC,MAAM,CAAC;YACtC,IAAI,EAAE,2BAAS,CAAC,eAAe;YAC/B,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,KAAK,EAAE,sBAAsB;YAC7B,OAAO,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE;YAClC,UAAU,EAAE,wCAAe,CAAC,QAAQ;YACpC,gBAAgB,EAAE,OAAO;YACzB,SAAS,EAAE,EAAE;YACb,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;SAC5B,CAAC,CAAC;QAEH,gBAAgB,GAAG,4BAAY,CAAC,MAAM,CAAC;YACrC,IAAI,EAAE,2BAAS,CAAC,cAAc;YAC9B,QAAQ,EAAE,mCAAa,CAAC,GAAG;YAC3B,KAAK,EAAE,gBAAgB;YACvB,OAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE;YAC9B,UAAU,EAAE,wCAAe,CAAC,gBAAgB;YAC5C,gBAAgB,EAAE,WAAW;YAC7B,SAAS,EAAE,EAAE;YACb,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;SAC5B,CAAC,CAAC;QAEH,aAAa,GAAG,4BAAY,CAAC,MAAM,CAAC;YAClC,IAAI,EAAE,2BAAS,CAAC,WAAW;YAC3B,QAAQ,EAAE,mCAAa,CAAC,QAAQ;YAChC,KAAK,EAAE,aAAa;YACpB,OAAO,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE;YAC/B,UAAU,EAAE,wCAAe,CAAC,GAAG;YAC/B,gBAAgB,EAAE,QAAQ;YAC1B,SAAS,EAAE,EAAE;YACb,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;SAC7B,CAAC,CAAC;QAEH,WAAW,GAAG,4BAAY,CAAC,MAAM,CAAC;YAChC,IAAI,EAAE,2BAAS,CAAC,gBAAgB;YAChC,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,KAAK,EAAE,gBAAgB;YACvB,OAAO,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE;YAC9B,UAAU,EAAE,wCAAe,CAAC,SAAS;YACrC,gBAAgB,EAAE,OAAO;YACzB,MAAM,EAAE,+BAAW,CAAC,MAAM;SAC3B,CAAC,CAAC;QAEH,aAAa,GAAG,4BAAY,CAAC,MAAM,CAAC;YAClC,IAAI,EAAE,2BAAS,CAAC,sBAAsB;YACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;YAC9B,KAAK,EAAE,wBAAwB;YAC/B,OAAO,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;YAC7B,UAAU,EAAE,wCAAe,CAAC,qBAAqB;YACjD,gBAAgB,EAAE,OAAO;YACzB,MAAM,EAAE,+BAAW,CAAC,QAAQ;SAC7B,CAAC,CAAC;QAEH,aAAa,GAAG,4BAAY,CAAC,MAAM,CAAC;YAClC,IAAI,EAAE,2BAAS,CAAC,eAAe;YAC/B,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,KAAK,EAAE,iBAAiB;YACxB,OAAO,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;YACzB,UAAU,EAAE,wCAAe,CAAC,mBAAmB;YAC/C,gBAAgB,EAAE,OAAO;YACzB,SAAS,EAAE,EAAE;SACd,CAAC,CAAC;QAEH,YAAY,GAAG,4BAAY,CAAC,MAAM,CAAC;YACjC,IAAI,EAAE,2BAAS,CAAC,SAAS;YACzB,QAAQ,EAAE,mCAAa,CAAC,GAAG;YAC3B,KAAK,EAAE,gBAAgB;YACvB,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;YACzB,UAAU,EAAE,wCAAe,CAAC,UAAU;YACtC,gBAAgB,EAAE,UAAU;YAC5B,SAAS,EAAE,EAAE;SACd,CAAC,CAAC;QAEH,WAAW,GAAG,4BAAY,CAAC,MAAM,CAAC;YAChC,IAAI,EAAE,2BAAS,CAAC,aAAa;YAC7B,QAAQ,EAAE,mCAAa,CAAC,GAAG;YAC3B,KAAK,EAAE,cAAc;YACrB,OAAO,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE;YAC5B,UAAU,EAAE,wCAAe,CAAC,iBAAiB;YAC7C,gBAAgB,EAAE,OAAO;YACzB,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,eAAe;SACvC,CAAC,CAAC;QAEH,iEAAiE;QACjE,MAAM,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5B,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,eAAe;QAC5D,QAAQ,GAAG,4BAAY,CAAC,MAAM,CAAC;YAC7B,IAAI,EAAE,2BAAS,CAAC,aAAa;YAC7B,QAAQ,EAAE,mCAAa,CAAC,GAAG;YAC3B,KAAK,EAAE,WAAW;YAClB,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;YACzB,UAAU,EAAE,wCAAe,CAAC,iBAAiB;YAC7C,gBAAgB,EAAE,OAAO;YACzB,SAAS,EAAE,QAAQ;SACpB,CAAC,CAAC;QAEH,yCAAyC;QACzC,aAAa,CAAC,YAAY,CAAC,+BAAW,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACvE,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,IAAI,IAAoC,CAAC;QAEzC,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,oDAA8B,EAAE,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,IAAI,IAAgC,CAAC;QAErC,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,gDAA0B,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,IAAI,IAA8B,CAAC;QAEnC,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,8CAAwB,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,IAAI,IAAgC,CAAC;QAErC,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,gDAA0B,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,IAAI,GAAG,IAAI,8CAAwB,EAAE,CAAC;YAE5C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,IAAI,GAAG,IAAI,8CAAwB,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,aAAa;YAExE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG,IAAI,8CAAwB,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YAC7D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,IAAI,GAAG,IAAI,6CAAuB,EAAE,CAAC;YAE3C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG,IAAI,6CAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW;YAC/D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,IAAI,GAAG,IAAI,4CAAsB,CAAC,CAAC,2BAAS,CAAC,eAAe,EAAE,2BAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAEjG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB;YAC5E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;YACvE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG,IAAI,4CAAsB,CAAC,CAAC,2BAAS,CAAC,eAAe,EAAE,2BAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACjG,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,IAAI,GAAG,IAAI,gDAA0B,CAAC,CAAC,mCAAa,CAAC,IAAI,EAAE,mCAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;YAE1F,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG,IAAI,gDAA0B,CAAC,CAAC,mCAAa,CAAC,IAAI,EAAE,mCAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC1F,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,IAAI,GAAG,IAAI,8CAAwB,CAAC,CAAC,+BAAW,CAAC,MAAM,EAAE,+BAAW,CAAC,aAAa,CAAC,CAAC,CAAC;YAE3F,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG,IAAI,8CAAwB,CAAC,CAAC,+BAAW,CAAC,MAAM,CAAC,CAAC,CAAC;YAChE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,IAAI,GAAG,IAAI,wDAAkC,CAAC,CAAC,oDAAqB,CAAC,GAAG,EAAE,oDAAqB,CAAC,UAAU,CAAC,CAAC,CAAC;YAEnH,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;YAC3E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG,IAAI,wDAAkC,CAAC,CAAC,oDAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;YACjF,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,IAAI,GAAG,IAAI,kDAA4B,CAAC,CAAC,wCAAe,CAAC,QAAQ,EAAE,wCAAe,CAAC,SAAS,CAAC,CAAC,CAAC;YAErG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YACrE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY;YAChE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,mBAAmB;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG,IAAI,kDAA4B,CAAC,CAAC,wCAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC1E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,IAAI,GAAG,IAAI,2CAAqB,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,CAAC;YAErE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;YAC7E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,IAAI,GAAG,IAAI,2CAAqB,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC;YAEpE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC1E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,wBAAwB;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,IAAI,GAAG,IAAI,2CAAqB,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,IAAI,GAAG,IAAI,2CAAqB,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC;YACpE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,IAAI,GAAG,IAAI,sDAAgC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAE1D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YACjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW;YACjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,uBAAuB;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,IAAI,GAAG,IAAI,sDAAgC,CAAC,EAAE,CAAC,CAAC;YAEtD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YACjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YACjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,IAAI,GAAG,IAAI,sDAAgC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAEjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YAChE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,SAAS,GAAG,IAAI,sDAAgC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAC/D,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAEjF,MAAM,OAAO,GAAG,IAAI,sDAAgC,CAAC,EAAE,CAAC,CAAC;YACzD,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAEzE,MAAM,OAAO,GAAG,IAAI,sDAAgC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YACpE,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,IAAI,eAAsB,CAAC;QAC3B,IAAI,WAAkB,CAAC;QAEvB,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,IAAI,EAAE,2BAAS,CAAC,eAAe;gBAC/B,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACzB,UAAU,EAAE,wCAAe,CAAC,QAAQ;gBACpC,gBAAgB,EAAE,OAAO;aAC1B,CAAC,CAAC;YAEH,eAAe,GAAG,4BAAY,CAAC,qBAAqB,CAClD,WAAW,EACX,2BAAS,CAAC,sBAAsB,EAChC,aAAa,EACb,EAAE,KAAK,EAAE,IAAI,EAAE,CAChB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,IAAI,GAAG,IAAI,mDAA6B,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAE9E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,IAAI,GAAG,IAAI,mDAA6B,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC;YAE1E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,IAAI,GAAG,IAAI,mDAA6B,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAE3E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,IAAI,GAAG,IAAI,mDAA6B,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YAE5E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;YAC3E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,IAAI,WAAkB,CAAC;QAEvB,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,IAAI,EAAE,2BAAS,CAAC,eAAe;gBAC/B,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACzB,UAAU,EAAE,wCAAe,CAAC,QAAQ;gBACpC,gBAAgB,EAAE,OAAO;aAC1B,CAAC,CAAC;YACH,WAAW,CAAC,sBAAsB,CAAC,oDAAqB,CAAC,MAAM,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,IAAI,GAAG,IAAI,wDAAkC,EAAE,CAAC;YAEtD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG,IAAI,wDAAkC,EAAE,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,IAAI,gBAAuB,CAAC;QAE5B,UAAU,CAAC,GAAG,EAAE;YACd,gBAAgB,GAAG,4BAAY,CAAC,MAAM,CAAC;gBACrC,IAAI,EAAE,2BAAS,CAAC,eAAe;gBAC/B,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,KAAK,EAAE,oBAAoB;gBAC3B,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;gBAC5B,UAAU,EAAE,wCAAe,CAAC,QAAQ;gBACpC,gBAAgB,EAAE,OAAO;aAC1B,CAAC,CAAC;YAEH,kCAAkC;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,gBAAgB,CAAC,uBAAuB,EAAE,CAAC;YAC7C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,IAAI,GAAG,IAAI,sDAAgC,EAAE,CAAC;YAEpD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG,IAAI,sDAAgC,EAAE,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,IAAI,kBAAyB,CAAC;QAE9B,UAAU,CAAC,GAAG,EAAE;YACd,kBAAkB,GAAG,4BAAY,CAAC,MAAM,CAAC;gBACvC,IAAI,EAAE,2BAAS,CAAC,eAAe;gBAC/B,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,KAAK,EAAE,qBAAqB;gBAC5B,OAAO,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE;gBAChC,UAAU,EAAE,wCAAe,CAAC,QAAQ;gBACpC,gBAAgB,EAAE,OAAO;gBACzB,MAAM,EAAE,+BAAW,CAAC,aAAa;aAClC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,IAAI,GAAG,IAAI,oDAA8B,EAAE,CAAC;YAElD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC1E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;YAC1E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY;YAClE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB;YACjF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,yBAAyB;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG,IAAI,oDAA8B,EAAE,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,sGAAsG,CAAC,CAAC;QAC7I,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,IAAI,GAAG,IAAI,gDAA0B,EAAE,CAAC;YAE9C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB;YAC5E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;YACvE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;YACtE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,+BAA+B;QAC3F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,WAAW,GAAG,4BAAY,CAAC,MAAM,CAAC;gBACtC,IAAI,EAAE,2BAAS,CAAC,MAAM;gBACtB,QAAQ,EAAE,mCAAa,CAAC,GAAG;gBAC3B,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;gBACzB,UAAU,EAAE,wCAAe,CAAC,kBAAkB;gBAC9C,gBAAgB,EAAE,QAAQ;gBAC1B,IAAI,EAAE,CAAC,gBAAgB,CAAC;aACzB,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,IAAI,gDAA0B,EAAE,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG,IAAI,gDAA0B,EAAE,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;QACrH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,IAAI,GAAG,+CAAyB,CAAC,MAAM,EAAE;iBAC5C,YAAY,EAAE;iBACd,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,IAAI,GAAG,+CAAyB,CAAC,MAAM,EAAE;iBAC5C,YAAY,EAAE;iBACd,MAAM,EAAE;iBACR,QAAQ,EAAE;iBACV,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAmC;YAC7F,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa;YACpE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,IAAI,GAAG,+CAAyB,CAAC,MAAM,EAAE;iBAC5C,QAAQ,EAAE;iBACV,QAAQ,EAAE;iBACV,WAAW,EAAE,CAAC;YAEjB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YACjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY;YAClE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,iCAAiC;QAC7F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,IAAI,GAAG,+CAAyB,CAAC,MAAM,EAAE;iBAC5C,OAAO,CAAC,2BAAS,CAAC,eAAe,EAAE,2BAAS,CAAC,gBAAgB,CAAC;iBAC9D,cAAc,CAAC,mCAAa,CAAC,IAAI,EAAE,mCAAa,CAAC,QAAQ,CAAC;iBAC1D,WAAW,CAAC,wCAAe,CAAC,QAAQ,EAAE,wCAAe,CAAC,SAAS,CAAC;iBAChE,QAAQ,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC;iBACvC,cAAc,CAAC,EAAE,EAAE,GAAG,CAAC;iBACvB,MAAM,CAAC,OAAO,CAAC;iBACf,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB;YACjF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,+BAA+B;QAC3F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,CAAC,GAAG,EAAE;gBACV,+CAAyB,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC;YAC7C,CAAC,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,gBAAgB,GAAG,IAAI,oDAA8B,EAAE,CAAC;YAC9D,MAAM,UAAU,GAAG,IAAI,8CAAwB,EAAE,CAAC;YAClD,MAAM,YAAY,GAAG,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAEtD,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,2BAA2B;YAC7F,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,YAAY,GAAG,IAAI,gDAA0B,EAAE,CAAC;YACtD,MAAM,YAAY,GAAG,IAAI,gDAA0B,EAAE,CAAC;YACtD,MAAM,YAAY,GAAG,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;YAEnD,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YACzE,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY;YAC1E,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,gBAAgB,GAAG,IAAI,oDAA8B,EAAE,CAAC;YAC9D,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC;YAEnD,MAAM,CAAC,mBAAmB,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;YAC5F,MAAM,CAAC,mBAAmB,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,gBAAgB,GAAG,IAAI,oDAA8B,EAAE,CAAC;YAC9D,MAAM,UAAU,GAAG,IAAI,8CAAwB,EAAE,CAAC;YAClD,MAAM,YAAY,GAAG,IAAI,gDAA0B,EAAE,CAAC;YAEtD,0CAA0C;YAC1C,MAAM,WAAW,GAAG,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;YAEtE,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,uCAAuC;YACxG,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,wCAAwC;YACrG,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,yBAAyB;QACxF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\event.specification.spec.ts"], "sourcesContent": ["import {\r\n  EventSpecification,\r\n  HighSeverityEventSpecification,\r\n  CriticalEventSpecification,\r\n  ActiveEventSpecification,\r\n  HighRiskEventSpecification,\r\n  RecentEventSpecification,\r\n  StaleEventSpecification,\r\n  EventTypeSpecification,\r\n  EventSeveritySpecification,\r\n  EventStatusSpecification,\r\n  EventProcessingStatusSpecification,\r\n  EventSourceTypeSpecification,\r\n  EventTagSpecification,\r\n  EventRiskScoreRangeSpecification,\r\n  EventAgeRangeSpecification,\r\n  EventCorrelationSpecification,\r\n  FailedProcessingEventSpecification,\r\n  MaxAttemptsExceededSpecification,\r\n  RequiresAttentionSpecification,\r\n  SecurityAlertSpecification,\r\n  EventSpecificationBuilder,\r\n} from '../event.specification';\r\nimport { EventFactory } from '../../factories/event.factory';\r\nimport { Event } from '../../entities/event.entity';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventStatus } from '../../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../../enums/event-source-type.enum';\r\nimport { UniqueEntityId } from '../../../../shared-kernel';\r\n\r\ndescribe('Event Specifications', () => {\r\n  let highSeverityEvent: Event;\r\n  let lowSeverityEvent: Event;\r\n  let criticalEvent: Event;\r\n  let activeEvent: Event;\r\n  let resolvedEvent: Event;\r\n  let highRiskEvent: Event;\r\n  let lowRiskEvent: Event;\r\n  let recentEvent: Event;\r\n  let oldEvent: Event;\r\n\r\n  beforeEach(() => {\r\n    // Create test events with different characteristics\r\n    highSeverityEvent = EventFactory.create({\r\n      type: EventType.THREAT_DETECTED,\r\n      severity: EventSeverity.HIGH,\r\n      title: 'High Severity Threat',\r\n      rawData: { threat: 'high-threat' },\r\n      sourceType: EventSourceType.FIREWALL,\r\n      sourceIdentifier: 'fw-01',\r\n      riskScore: 80,\r\n      tags: ['threat', 'network'],\r\n    });\r\n\r\n    lowSeverityEvent = EventFactory.create({\r\n      type: EventType.SYSTEM_STARTUP,\r\n      severity: EventSeverity.LOW,\r\n      title: 'System Startup',\r\n      rawData: { system: 'started' },\r\n      sourceType: EventSourceType.OPERATING_SYSTEM,\r\n      sourceIdentifier: 'server-01',\r\n      riskScore: 20,\r\n      tags: ['system', 'startup'],\r\n    });\r\n\r\n    criticalEvent = EventFactory.create({\r\n      type: EventType.DATA_BREACH,\r\n      severity: EventSeverity.CRITICAL,\r\n      title: 'Data Breach',\r\n      rawData: { breach: 'critical' },\r\n      sourceType: EventSourceType.DLP,\r\n      sourceIdentifier: 'dlp-01',\r\n      riskScore: 95,\r\n      tags: ['breach', 'critical'],\r\n    });\r\n\r\n    activeEvent = EventFactory.create({\r\n      type: EventType.MALWARE_DETECTED,\r\n      severity: EventSeverity.HIGH,\r\n      title: 'Active Malware',\r\n      rawData: { malware: 'active' },\r\n      sourceType: EventSourceType.ANTIVIRUS,\r\n      sourceIdentifier: 'av-01',\r\n      status: EventStatus.ACTIVE,\r\n    });\r\n\r\n    resolvedEvent = EventFactory.create({\r\n      type: EventType.VULNERABILITY_DETECTED,\r\n      severity: EventSeverity.MEDIUM,\r\n      title: 'Resolved Vulnerability',\r\n      rawData: { vuln: 'resolved' },\r\n      sourceType: EventSourceType.VULNERABILITY_SCANNER,\r\n      sourceIdentifier: 'vs-01',\r\n      status: EventStatus.RESOLVED,\r\n    });\r\n\r\n    highRiskEvent = EventFactory.create({\r\n      type: EventType.THREAT_DETECTED,\r\n      severity: EventSeverity.HIGH,\r\n      title: 'High Risk Event',\r\n      rawData: { risk: 'high' },\r\n      sourceType: EventSourceType.THREAT_INTELLIGENCE,\r\n      sourceIdentifier: 'ti-01',\r\n      riskScore: 85,\r\n    });\r\n\r\n    lowRiskEvent = EventFactory.create({\r\n      type: EventType.AUDIT_LOG,\r\n      severity: EventSeverity.LOW,\r\n      title: 'Low Risk Event',\r\n      rawData: { audit: 'log' },\r\n      sourceType: EventSourceType.COMPLIANCE,\r\n      sourceIdentifier: 'audit-01',\r\n      riskScore: 15,\r\n    });\r\n\r\n    recentEvent = EventFactory.create({\r\n      type: EventType.LOGIN_SUCCESS,\r\n      severity: EventSeverity.LOW,\r\n      title: 'Recent Login',\r\n      rawData: { login: 'recent' },\r\n      sourceType: EventSourceType.DIRECTORY_SERVICE,\r\n      sourceIdentifier: 'ad-01',\r\n      timestamp: new Date(), // Current time\r\n    });\r\n\r\n    // Create an old event (simulate by creating with past timestamp)\r\n    const pastDate = new Date();\r\n    pastDate.setHours(pastDate.getHours() - 25); // 25 hours ago\r\n    oldEvent = EventFactory.create({\r\n      type: EventType.LOGIN_SUCCESS,\r\n      severity: EventSeverity.LOW,\r\n      title: 'Old Login',\r\n      rawData: { login: 'old' },\r\n      sourceType: EventSourceType.DIRECTORY_SERVICE,\r\n      sourceIdentifier: 'ad-01',\r\n      timestamp: pastDate,\r\n    });\r\n\r\n    // Simulate resolution for resolved event\r\n    resolvedEvent.changeStatus(EventStatus.RESOLVED, 'analyst', 'Fixed');\r\n  });\r\n\r\n  describe('HighSeverityEventSpecification', () => {\r\n    let spec: HighSeverityEventSpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new HighSeverityEventSpecification();\r\n    });\r\n\r\n    it('should satisfy high severity events', () => {\r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(criticalEvent)).toBe(true);\r\n    });\r\n\r\n    it('should not satisfy low severity events', () => {\r\n      expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false);\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      expect(spec.getDescription()).toBe('Event has high or critical severity');\r\n    });\r\n  });\r\n\r\n  describe('CriticalEventSpecification', () => {\r\n    let spec: CriticalEventSpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new CriticalEventSpecification();\r\n    });\r\n\r\n    it('should satisfy only critical events', () => {\r\n      expect(spec.isSatisfiedBy(criticalEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(false);\r\n      expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false);\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      expect(spec.getDescription()).toBe('Event has critical severity');\r\n    });\r\n  });\r\n\r\n  describe('ActiveEventSpecification', () => {\r\n    let spec: ActiveEventSpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new ActiveEventSpecification();\r\n    });\r\n\r\n    it('should satisfy active events', () => {\r\n      expect(spec.isSatisfiedBy(activeEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // Default is ACTIVE\r\n    });\r\n\r\n    it('should not satisfy resolved events', () => {\r\n      expect(spec.isSatisfiedBy(resolvedEvent)).toBe(false);\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      expect(spec.getDescription()).toBe('Event is active (not resolved or closed)');\r\n    });\r\n  });\r\n\r\n  describe('HighRiskEventSpecification', () => {\r\n    let spec: HighRiskEventSpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new HighRiskEventSpecification();\r\n    });\r\n\r\n    it('should satisfy high risk events', () => {\r\n      expect(spec.isSatisfiedBy(highRiskEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(criticalEvent)).toBe(true);\r\n    });\r\n\r\n    it('should not satisfy low risk events', () => {\r\n      expect(spec.isSatisfiedBy(lowRiskEvent)).toBe(false);\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      expect(spec.getDescription()).toBe('Event has high risk score (>= 70)');\r\n    });\r\n  });\r\n\r\n  describe('RecentEventSpecification', () => {\r\n    it('should satisfy recent events with default time window', () => {\r\n      const spec = new RecentEventSpecification();\r\n      \r\n      expect(spec.isSatisfiedBy(recentEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(oldEvent)).toBe(false);\r\n    });\r\n\r\n    it('should satisfy events within custom time window', () => {\r\n      const spec = new RecentEventSpecification(30 * 60 * 1000); // 30 minutes\r\n      \r\n      expect(spec.isSatisfiedBy(recentEvent)).toBe(true);\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      const spec = new RecentEventSpecification(3600000); // 1 hour\r\n      expect(spec.getDescription()).toBe('Event occurred within 1h 0m');\r\n    });\r\n  });\r\n\r\n  describe('StaleEventSpecification', () => {\r\n    it('should satisfy stale events with default time window', () => {\r\n      const spec = new StaleEventSpecification();\r\n      \r\n      expect(spec.isSatisfiedBy(oldEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(recentEvent)).toBe(false);\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      const spec = new StaleEventSpecification(86400000); // 24 hours\r\n      expect(spec.getDescription()).toBe('Event is older than 24 hours');\r\n    });\r\n  });\r\n\r\n  describe('EventTypeSpecification', () => {\r\n    it('should satisfy events of specified types', () => {\r\n      const spec = new EventTypeSpecification([EventType.THREAT_DETECTED, EventType.MALWARE_DETECTED]);\r\n      \r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // THREAT_DETECTED\r\n      expect(spec.isSatisfiedBy(activeEvent)).toBe(true); // MALWARE_DETECTED\r\n      expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // SYSTEM_STARTUP\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      const spec = new EventTypeSpecification([EventType.THREAT_DETECTED, EventType.MALWARE_DETECTED]);\r\n      expect(spec.getDescription()).toBe('Event type is one of: THREAT_DETECTED, MALWARE_DETECTED');\r\n    });\r\n  });\r\n\r\n  describe('EventSeveritySpecification', () => {\r\n    it('should satisfy events of specified severities', () => {\r\n      const spec = new EventSeveritySpecification([EventSeverity.HIGH, EventSeverity.CRITICAL]);\r\n      \r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(criticalEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false);\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      const spec = new EventSeveritySpecification([EventSeverity.HIGH, EventSeverity.CRITICAL]);\r\n      expect(spec.getDescription()).toBe('Event severity is one of: HIGH, CRITICAL');\r\n    });\r\n  });\r\n\r\n  describe('EventStatusSpecification', () => {\r\n    it('should satisfy events of specified statuses', () => {\r\n      const spec = new EventStatusSpecification([EventStatus.ACTIVE, EventStatus.INVESTIGATING]);\r\n      \r\n      expect(spec.isSatisfiedBy(activeEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(resolvedEvent)).toBe(false);\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      const spec = new EventStatusSpecification([EventStatus.ACTIVE]);\r\n      expect(spec.getDescription()).toBe('Event status is one of: ACTIVE');\r\n    });\r\n  });\r\n\r\n  describe('EventProcessingStatusSpecification', () => {\r\n    it('should satisfy events of specified processing statuses', () => {\r\n      const spec = new EventProcessingStatusSpecification([EventProcessingStatus.RAW, EventProcessingStatus.NORMALIZED]);\r\n      \r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // Default is RAW\r\n      expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(true); // Default is RAW\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      const spec = new EventProcessingStatusSpecification([EventProcessingStatus.RAW]);\r\n      expect(spec.getDescription()).toBe('Event processing status is one of: raw');\r\n    });\r\n  });\r\n\r\n  describe('EventSourceTypeSpecification', () => {\r\n    it('should satisfy events from specified source types', () => {\r\n      const spec = new EventSourceTypeSpecification([EventSourceType.FIREWALL, EventSourceType.ANTIVIRUS]);\r\n      \r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // FIREWALL\r\n      expect(spec.isSatisfiedBy(activeEvent)).toBe(true); // ANTIVIRUS\r\n      expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // OPERATING_SYSTEM\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      const spec = new EventSourceTypeSpecification([EventSourceType.FIREWALL]);\r\n      expect(spec.getDescription()).toBe('Event source type is one of: firewall');\r\n    });\r\n  });\r\n\r\n  describe('EventTagSpecification', () => {\r\n    it('should satisfy events with any of the specified tags', () => {\r\n      const spec = new EventTagSpecification(['threat', 'malware'], false);\r\n      \r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // Has 'threat' tag\r\n      expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // Has neither tag\r\n    });\r\n\r\n    it('should satisfy events with all of the specified tags', () => {\r\n      const spec = new EventTagSpecification(['threat', 'network'], true);\r\n      \r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // Has both tags\r\n      expect(spec.isSatisfiedBy(criticalEvent)).toBe(false); // Missing 'network' tag\r\n    });\r\n\r\n    it('should have correct description for any tags', () => {\r\n      const spec = new EventTagSpecification(['threat', 'malware'], false);\r\n      expect(spec.getDescription()).toBe('Event has any of these tags: threat, malware');\r\n    });\r\n\r\n    it('should have correct description for all tags', () => {\r\n      const spec = new EventTagSpecification(['threat', 'network'], true);\r\n      expect(spec.getDescription()).toBe('Event has all of these tags: threat, network');\r\n    });\r\n  });\r\n\r\n  describe('EventRiskScoreRangeSpecification', () => {\r\n    it('should satisfy events within risk score range', () => {\r\n      const spec = new EventRiskScoreRangeSpecification(70, 90);\r\n      \r\n      expect(spec.isSatisfiedBy(highRiskEvent)).toBe(true); // Score 85\r\n      expect(spec.isSatisfiedBy(lowRiskEvent)).toBe(false); // Score 15\r\n      expect(spec.isSatisfiedBy(criticalEvent)).toBe(false); // Score 95 (above max)\r\n    });\r\n\r\n    it('should satisfy events with minimum score only', () => {\r\n      const spec = new EventRiskScoreRangeSpecification(70);\r\n      \r\n      expect(spec.isSatisfiedBy(highRiskEvent)).toBe(true); // Score 85\r\n      expect(spec.isSatisfiedBy(criticalEvent)).toBe(true); // Score 95\r\n      expect(spec.isSatisfiedBy(lowRiskEvent)).toBe(false); // Score 15\r\n    });\r\n\r\n    it('should satisfy events with maximum score only', () => {\r\n      const spec = new EventRiskScoreRangeSpecification(undefined, 50);\r\n      \r\n      expect(spec.isSatisfiedBy(lowRiskEvent)).toBe(true); // Score 15\r\n      expect(spec.isSatisfiedBy(highRiskEvent)).toBe(false); // Score 85\r\n    });\r\n\r\n    it('should have correct descriptions', () => {\r\n      const rangeSpec = new EventRiskScoreRangeSpecification(70, 90);\r\n      expect(rangeSpec.getDescription()).toBe('Event risk score is between 70 and 90');\r\n\r\n      const minSpec = new EventRiskScoreRangeSpecification(70);\r\n      expect(minSpec.getDescription()).toBe('Event risk score is at least 70');\r\n\r\n      const maxSpec = new EventRiskScoreRangeSpecification(undefined, 50);\r\n      expect(maxSpec.getDescription()).toBe('Event risk score is at most 50');\r\n    });\r\n  });\r\n\r\n  describe('EventCorrelationSpecification', () => {\r\n    let correlatedEvent: Event;\r\n    let parentEvent: Event;\r\n\r\n    beforeEach(() => {\r\n      parentEvent = EventFactory.create({\r\n        type: EventType.THREAT_DETECTED,\r\n        severity: EventSeverity.HIGH,\r\n        title: 'Parent Event',\r\n        rawData: { parent: true },\r\n        sourceType: EventSourceType.FIREWALL,\r\n        sourceIdentifier: 'fw-01',\r\n      });\r\n\r\n      correlatedEvent = EventFactory.createCorrelatedEvent(\r\n        parentEvent,\r\n        EventType.CONNECTION_ESTABLISHED,\r\n        'Child Event',\r\n        { child: true }\r\n      );\r\n    });\r\n\r\n    it('should satisfy events with specific correlation ID', () => {\r\n      const spec = new EventCorrelationSpecification(correlatedEvent.correlationId);\r\n      \r\n      expect(spec.isSatisfiedBy(correlatedEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(parentEvent)).toBe(false);\r\n    });\r\n\r\n    it('should satisfy events with specific parent ID', () => {\r\n      const spec = new EventCorrelationSpecification(undefined, parentEvent.id);\r\n      \r\n      expect(spec.isSatisfiedBy(correlatedEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(parentEvent)).toBe(false);\r\n    });\r\n\r\n    it('should satisfy events with any correlation', () => {\r\n      const spec = new EventCorrelationSpecification(undefined, undefined, true);\r\n      \r\n      expect(spec.isSatisfiedBy(correlatedEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(false); // No correlation\r\n    });\r\n\r\n    it('should satisfy events with no correlation', () => {\r\n      const spec = new EventCorrelationSpecification(undefined, undefined, false);\r\n      \r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // No correlation\r\n      expect(spec.isSatisfiedBy(correlatedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('FailedProcessingEventSpecification', () => {\r\n    let failedEvent: Event;\r\n\r\n    beforeEach(() => {\r\n      failedEvent = EventFactory.create({\r\n        type: EventType.THREAT_DETECTED,\r\n        severity: EventSeverity.HIGH,\r\n        title: 'Failed Event',\r\n        rawData: { failed: true },\r\n        sourceType: EventSourceType.FIREWALL,\r\n        sourceIdentifier: 'fw-01',\r\n      });\r\n      failedEvent.changeProcessingStatus(EventProcessingStatus.FAILED);\r\n    });\r\n\r\n    it('should satisfy events with failed processing', () => {\r\n      const spec = new FailedProcessingEventSpecification();\r\n      \r\n      expect(spec.isSatisfiedBy(failedEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(false);\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      const spec = new FailedProcessingEventSpecification();\r\n      expect(spec.getDescription()).toBe('Event processing has failed');\r\n    });\r\n  });\r\n\r\n  describe('MaxAttemptsExceededSpecification', () => {\r\n    let maxAttemptsEvent: Event;\r\n\r\n    beforeEach(() => {\r\n      maxAttemptsEvent = EventFactory.create({\r\n        type: EventType.THREAT_DETECTED,\r\n        severity: EventSeverity.HIGH,\r\n        title: 'Max Attempts Event',\r\n        rawData: { attempts: 'max' },\r\n        sourceType: EventSourceType.FIREWALL,\r\n        sourceIdentifier: 'fw-01',\r\n      });\r\n      \r\n      // Record 5 attempts to exceed max\r\n      for (let i = 0; i < 5; i++) {\r\n        maxAttemptsEvent.recordProcessingAttempt();\r\n      }\r\n    });\r\n\r\n    it('should satisfy events that exceeded max attempts', () => {\r\n      const spec = new MaxAttemptsExceededSpecification();\r\n      \r\n      expect(spec.isSatisfiedBy(maxAttemptsEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(false);\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      const spec = new MaxAttemptsExceededSpecification();\r\n      expect(spec.getDescription()).toBe('Event has exceeded maximum processing attempts');\r\n    });\r\n  });\r\n\r\n  describe('RequiresAttentionSpecification', () => {\r\n    let investigatingEvent: Event;\r\n\r\n    beforeEach(() => {\r\n      investigatingEvent = EventFactory.create({\r\n        type: EventType.THREAT_DETECTED,\r\n        severity: EventSeverity.MEDIUM,\r\n        title: 'Investigating Event',\r\n        rawData: { investigating: true },\r\n        sourceType: EventSourceType.FIREWALL,\r\n        sourceIdentifier: 'fw-01',\r\n        status: EventStatus.INVESTIGATING,\r\n      });\r\n    });\r\n\r\n    it('should satisfy events requiring attention', () => {\r\n      const spec = new RequiresAttentionSpecification();\r\n      \r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // High severity\r\n      expect(spec.isSatisfiedBy(criticalEvent)).toBe(true); // Critical severity\r\n      expect(spec.isSatisfiedBy(highRiskEvent)).toBe(true); // High risk\r\n      expect(spec.isSatisfiedBy(investigatingEvent)).toBe(true); // Under investigation\r\n      expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // Low severity, low risk\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      const spec = new RequiresAttentionSpecification();\r\n      expect(spec.getDescription()).toBe('Event requires human attention (high severity, high risk, failed processing, or under investigation)');\r\n    });\r\n  });\r\n\r\n  describe('SecurityAlertSpecification', () => {\r\n    it('should satisfy security alert events', () => {\r\n      const spec = new SecurityAlertSpecification();\r\n      \r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // THREAT_DETECTED\r\n      expect(spec.isSatisfiedBy(activeEvent)).toBe(true); // MALWARE_DETECTED\r\n      expect(spec.isSatisfiedBy(criticalEvent)).toBe(true); // High severity\r\n      expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // SYSTEM_STARTUP, low severity\r\n    });\r\n\r\n    it('should satisfy events with security-alert tag', () => {\r\n      const taggedEvent = EventFactory.create({\r\n        type: EventType.CUSTOM,\r\n        severity: EventSeverity.LOW,\r\n        title: 'Tagged Event',\r\n        rawData: { tagged: true },\r\n        sourceType: EventSourceType.CUSTOM_APPLICATION,\r\n        sourceIdentifier: 'app-01',\r\n        tags: ['security-alert'],\r\n      });\r\n\r\n      const spec = new SecurityAlertSpecification();\r\n      expect(spec.isSatisfiedBy(taggedEvent)).toBe(true);\r\n    });\r\n\r\n    it('should have correct description', () => {\r\n      const spec = new SecurityAlertSpecification();\r\n      expect(spec.getDescription()).toBe('Event is a security alert (threat, malware, vulnerability, or high severity)');\r\n    });\r\n  });\r\n\r\n  describe('EventSpecificationBuilder', () => {\r\n    it('should build specification with single condition', () => {\r\n      const spec = EventSpecificationBuilder.create()\r\n        .highSeverity()\r\n        .build();\r\n      \r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true);\r\n      expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false);\r\n    });\r\n\r\n    it('should build specification with multiple AND conditions', () => {\r\n      const spec = EventSpecificationBuilder.create()\r\n        .highSeverity()\r\n        .active()\r\n        .highRisk()\r\n        .build();\r\n      \r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // High severity, active, high risk\r\n      expect(spec.isSatisfiedBy(resolvedEvent)).toBe(false); // Not active\r\n      expect(spec.isSatisfiedBy(lowRiskEvent)).toBe(false); // Not high risk\r\n    });\r\n\r\n    it('should build specification with multiple OR conditions', () => {\r\n      const spec = EventSpecificationBuilder.create()\r\n        .critical()\r\n        .highRisk()\r\n        .buildWithOr();\r\n      \r\n      expect(spec.isSatisfiedBy(criticalEvent)).toBe(true); // Critical\r\n      expect(spec.isSatisfiedBy(highRiskEvent)).toBe(true); // High risk\r\n      expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // Neither critical nor high risk\r\n    });\r\n\r\n    it('should build specification with various filters', () => {\r\n      const spec = EventSpecificationBuilder.create()\r\n        .ofTypes(EventType.THREAT_DETECTED, EventType.MALWARE_DETECTED)\r\n        .withSeverities(EventSeverity.HIGH, EventSeverity.CRITICAL)\r\n        .fromSources(EventSourceType.FIREWALL, EventSourceType.ANTIVIRUS)\r\n        .withTags(['threat', 'security'], false)\r\n        .riskScoreRange(70, 100)\r\n        .recent(3600000)\r\n        .build();\r\n      \r\n      expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // Matches all criteria\r\n      expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // Wrong type, severity, source\r\n    });\r\n\r\n    it('should throw error when building without conditions', () => {\r\n      expect(() => {\r\n        EventSpecificationBuilder.create().build();\r\n      }).toThrow('At least one specification must be added');\r\n    });\r\n  });\r\n\r\n  describe('Specification Composition', () => {\r\n    it('should combine specifications with AND', () => {\r\n      const highSeveritySpec = new HighSeverityEventSpecification();\r\n      const activeSpec = new ActiveEventSpecification();\r\n      const combinedSpec = highSeveritySpec.and(activeSpec);\r\n      \r\n      expect(combinedSpec.isSatisfiedBy(highSeverityEvent)).toBe(true); // High severity and active\r\n      expect(combinedSpec.isSatisfiedBy(resolvedEvent)).toBe(false); // Not active\r\n    });\r\n\r\n    it('should combine specifications with OR', () => {\r\n      const criticalSpec = new CriticalEventSpecification();\r\n      const highRiskSpec = new HighRiskEventSpecification();\r\n      const combinedSpec = criticalSpec.or(highRiskSpec);\r\n      \r\n      expect(combinedSpec.isSatisfiedBy(criticalEvent)).toBe(true); // Critical\r\n      expect(combinedSpec.isSatisfiedBy(highRiskEvent)).toBe(true); // High risk\r\n      expect(combinedSpec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // Neither\r\n    });\r\n\r\n    it('should negate specifications with NOT', () => {\r\n      const highSeveritySpec = new HighSeverityEventSpecification();\r\n      const notHighSeveritySpec = highSeveritySpec.not();\r\n      \r\n      expect(notHighSeveritySpec.isSatisfiedBy(lowSeverityEvent)).toBe(true); // Not high severity\r\n      expect(notHighSeveritySpec.isSatisfiedBy(highSeverityEvent)).toBe(false); // High severity\r\n    });\r\n\r\n    it('should create complex combinations', () => {\r\n      const highSeveritySpec = new HighSeverityEventSpecification();\r\n      const activeSpec = new ActiveEventSpecification();\r\n      const highRiskSpec = new HighRiskEventSpecification();\r\n      \r\n      // (High severity AND active) OR high risk\r\n      const complexSpec = highSeveritySpec.and(activeSpec).or(highRiskSpec);\r\n      \r\n      expect(complexSpec.isSatisfiedBy(highSeverityEvent)).toBe(true); // High severity, active, and high risk\r\n      expect(complexSpec.isSatisfiedBy(highRiskEvent)).toBe(true); // High risk (even if not high severity)\r\n      expect(complexSpec.isSatisfiedBy(lowRiskEvent)).toBe(false); // None of the conditions\r\n    });\r\n  });\r\n});"], "version": 3}