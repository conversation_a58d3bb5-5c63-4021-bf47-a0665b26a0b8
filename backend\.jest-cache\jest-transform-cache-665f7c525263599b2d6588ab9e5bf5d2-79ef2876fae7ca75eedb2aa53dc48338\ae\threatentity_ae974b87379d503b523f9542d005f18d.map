{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\threat\\threat.entity.ts", "mappings": ";;;AAAA,8FAAyF;AAIzF,2EAAkE;AAClE,8EAAyE;AACzE,8FAAwF;AA0LxF;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAa,MAAO,SAAQ,uCAA8B;IAMxD,YAAY,KAAkB,EAAE,EAAmB;QACjD,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACnB,CAAC;IAES,QAAQ;QAChB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1E,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qCAAc,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,KAAK,CAAC,4BAA4B,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;YACnG,MAAM,IAAI,KAAK,CAAC,qCAAqC,MAAM,CAAC,cAAc,QAAQ,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;QAC7G,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,GAAG,MAAM,CAAC,cAAc;YAC3D,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;YAChE,MAAM,IAAI,KAAK,CAAC,8BAA8B,MAAM,CAAC,cAAc,QAAQ,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;QACtG,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,gCAAgC;QAChC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CACX,IAAY,EACZ,WAAmB,EACnB,QAAwB,EACxB,QAAgB,EAChB,IAAY,EACZ,UAAkB,EAClB,OAUC;QAED,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,MAAM,KAAK,GAAgB;YACzB,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;YACjB,WAAW,EAAE,WAAW,CAAC,IAAI,EAAE;YAC/B,QAAQ;YACR,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE;YACzB,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE;YACzC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;YACjB,UAAU;YACV,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,EAAE;YACrC,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,EAAE;YACrC,WAAW,EAAE,OAAO,EAAE,WAAW;YACjC,aAAa,EAAE,OAAO,EAAE,aAAa;YACrC,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,EAAE;YACrC,cAAc,EAAE,OAAO,EAAE,cAAc,IAAI,EAAE;YAC7C,QAAQ,EAAE;gBACR,aAAa,EAAE,GAAG;gBAClB,QAAQ,EAAE,GAAG;aACd;YACD,gBAAgB,EAAE;gBAChB,MAAM,EAAE,UAAU;gBAClB,YAAY,EAAE,EAAE;gBAChB,YAAY,EAAE,EAAE;gBAChB,WAAW,EAAE,EAAE;aAChB;YACD,cAAc,EAAE,MAAM,CAAC,8BAA8B,CAAC,QAAQ,EAAE,UAAU,CAAC;YAC3E,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,EAAE;YACzB,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,EAAE;SACtC,CAAC;QAEF,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;QAEjC,uBAAuB;QACvB,MAAM,CAAC,cAAc,CAAC,IAAI,2CAAmB,CAC3C,MAAM,CAAC,EAAE,EACT;YACE,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC9B,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;YACvB,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ;YAC/B,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ;YAC/B,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;YACvB,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU;YACnC,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS;YAChD,cAAc,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM;YAC9C,kBAAkB,EAAE,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM;YACtD,SAAS,EAAE,GAAG,CAAC,WAAW,EAAE;SAC7B,CACF,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,8BAA8B,CAC3C,QAAwB,EACxB,UAAkB;QAElB,gCAAgC;QAChC,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,qCAAc,CAAC,QAAQ;gBAAE,SAAS,GAAG,EAAE,CAAC;gBAAC,MAAM;YACpD,KAAK,qCAAc,CAAC,IAAI;gBAAE,SAAS,GAAG,EAAE,CAAC;gBAAC,MAAM;YAChD,KAAK,qCAAc,CAAC,MAAM;gBAAE,SAAS,GAAG,EAAE,CAAC;gBAAC,MAAM;YAClD,KAAK,qCAAc,CAAC,GAAG;gBAAE,SAAS,GAAG,EAAE,CAAC;gBAAC,MAAM;YAC/C,KAAK,qCAAc,CAAC,OAAO;gBAAE,SAAS,GAAG,EAAE,CAAC;gBAAC,MAAM;QACrD,CAAC;QAED,uBAAuB;QACvB,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC;QAEvD,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAChD,MAAM,EAAE;gBACN,eAAe,EAAE,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;gBACzE,SAAS,EAAE,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;gBACnE,YAAY,EAAE,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;gBACtE,SAAS,EAAE,CAAC,EAAE,iBAAiB;gBAC/B,YAAY,EAAE,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;aACpE;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,QAAQ;gBACxB,aAAa,EAAE,QAAQ;gBACvB,MAAM,EAAE,KAAK;aACd;YACD,cAAc,EAAE,EAAE;YAClB,sBAAsB,EAAE,EAAE;SAC3B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAA2B,EAAE,MAAc;QACxD,IAAI,WAAW,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACxC,OAAO,CAAC,mBAAmB;QAC7B,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;QACxC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,WAAW,CAAC;QAElC,8BAA8B;QAC9B,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,uBAAuB;QACvB,IAAI,CAAC,cAAc,CAAC,IAAI,0DAA0B,CAChD,IAAI,CAAC,EAAE,EACP;YACE,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC5B,WAAW;YACX,WAAW;YACX,MAAM;YACN,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS;YACjD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAiB;QAC7B,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CACrC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CACjF,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC;QAC7C,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,MAA2C;QAC7D,MAAM,gBAAgB,GAAqB;YACzC,GAAG,MAAM;YACT,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,sBAAsB,CACpB,MAAkC,EAClC,OAKC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;QAE5C,IAAI,OAAO,EAAE,eAAe,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QACxE,CAAC;QAED,IAAI,OAAO,EAAE,iBAAiB,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QAC5E,CAAC;QAED,IAAI,OAAO,EAAE,YAAY,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QAClE,CAAC;QAED,IAAI,OAAO,EAAE,WAAW,EAAE,CAAC;YACzB,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QAChE,CAAC;QAED,kCAAkC;QAClC,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC;IAEO,uBAAuB,CAAC,MAAkC;QAChE,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,YAAY;gBACf,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;oBAC5C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,GAAG,GAAG,CAAC;gBAC/C,CAAC;gBACD,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;oBACnC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,GAAG,GAAG,CAAC;gBACtC,CAAC;gBACD,MAAM;YACR,KAAK,aAAa;gBAChB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;oBAC5C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,GAAG,GAAG,CAAC;gBAC/C,CAAC;gBACD,MAAM;YACR,KAAK,YAAY;gBACf,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;oBACpC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAC;gBACvC,CAAC;gBACD,MAAM;YACR,KAAK,YAAY;gBACf,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;oBACzC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,GAAG,GAAG,CAAC;gBAC5C,CAAC;gBACD,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;oBAC3C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,iBAAiB,GAAG,GAAG,CAAC;gBAC9C,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,MAAM,cAAc,GAAG,CAAC,UAAU,EAAE,eAAe,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;QAChG,OAAO,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,KAAK,WAAW,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,qCAAc,CAAC,IAAI;YAC3C,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ;YAC/C,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,qCAAc,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACvF,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC/B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC;QAEpD,wBAAwB;QACxB,MAAM,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC;QACzF,SAAS,IAAI,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;QAE/C,yBAAyB;QACzB,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC;QACvF,SAAS,IAAI,cAAc,CAAC,MAAM,GAAG,EAAE,CAAC;QAExC,6BAA6B;QAC7B,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YACtB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YAC1D,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAC9D,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;YACvC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;YACzC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;YACzC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,QAAQ,EAAE;gBACR,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;gBACzB,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;gBAC7B,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;gBACrC,0BAA0B,EAAE,IAAI,CAAC,0BAA0B,EAAE;gBAC7D,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE;aACnB;YACD,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE;YACxC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE;SACzC,CAAC;IACJ,CAAC;;AAnfH,wBAofC;AAnfyB,qBAAc,GAAG,CAAC,CAAC;AACnB,qBAAc,GAAG,GAAG,CAAC;AACrB,qBAAc,GAAG,CAAC,CAAC;AACnB,qBAAc,GAAG,GAAG,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\threat\\threat.entity.ts"], "sourcesContent": ["import { BaseAggregateRoot } from '../../../../shared-kernel/domain/base-aggregate-root';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { IOC } from '../../value-objects/threat-indicators/ioc.value-object';\r\nimport { CVSSScore } from '../../value-objects/threat-indicators/cvss-score.value-object';\r\nimport { ThreatSeverity } from '../../enums/threat-severity.enum';\r\nimport { ThreatDetectedEvent } from '../../events/threat-detected.event';\r\nimport { ThreatSeverityChangedEvent } from '../../events/threat-severity-changed.event';\r\n\r\n/**\r\n * Threat Properties\r\n */\r\nexport interface ThreatProps {\r\n  /** Threat name/title */\r\n  name: string;\r\n  /** Threat description */\r\n  description: string;\r\n  /** Threat severity level */\r\n  severity: ThreatSeverity;\r\n  /** Threat category */\r\n  category: string;\r\n  /** Threat subcategory */\r\n  subcategory?: string;\r\n  /** Threat type */\r\n  type: string;\r\n  /** Threat confidence score (0-100) */\r\n  confidence: number;\r\n  /** Associated indicators of compromise */\r\n  indicators: IOC[];\r\n  /** CVSS scores if applicable */\r\n  cvssScores: CVSSScore[];\r\n  /** Threat actor attribution */\r\n  attribution?: ThreatAttribution;\r\n  /** Malware family information */\r\n  malwareFamily?: MalwareFamily;\r\n  /** Attack techniques used */\r\n  techniques: AttackTechnique[];\r\n  /** Affected assets */\r\n  affectedAssets: string[];\r\n  /** Threat timeline */\r\n  timeline: ThreatTimeline;\r\n  /** Mitigation status */\r\n  mitigationStatus: MitigationStatus;\r\n  /** Risk assessment */\r\n  riskAssessment: RiskAssessment;\r\n  /** Threat tags */\r\n  tags: string[];\r\n  /** Custom attributes */\r\n  attributes: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Threat Attribution\r\n */\r\nexport interface ThreatAttribution {\r\n  /** Threat actor name */\r\n  actor: string;\r\n  /** Attribution confidence (0-100) */\r\n  confidence: number;\r\n  /** Known aliases */\r\n  aliases: string[];\r\n  /** Motivation */\r\n  motivation: string[];\r\n  /** Capabilities */\r\n  capabilities: string[];\r\n  /** Geographic origin */\r\n  origin?: string;\r\n  /** Associated campaigns */\r\n  campaigns: string[];\r\n}\r\n\r\n/**\r\n * Malware Family\r\n */\r\nexport interface MalwareFamily {\r\n  /** Family name */\r\n  name: string;\r\n  /** Variant */\r\n  variant?: string;\r\n  /** Family confidence (0-100) */\r\n  confidence: number;\r\n  /** Known capabilities */\r\n  capabilities: string[];\r\n  /** Communication protocols */\r\n  protocols: string[];\r\n  /** Persistence mechanisms */\r\n  persistence: string[];\r\n}\r\n\r\n/**\r\n * Attack Technique\r\n */\r\nexport interface AttackTechnique {\r\n  /** Technique ID (e.g., MITRE ATT&CK) */\r\n  id: string;\r\n  /** Technique name */\r\n  name: string;\r\n  /** Tactic */\r\n  tactic: string;\r\n  /** Technique description */\r\n  description: string;\r\n  /** Detection confidence (0-100) */\r\n  confidence: number;\r\n  /** Evidence supporting this technique */\r\n  evidence: string[];\r\n}\r\n\r\n/**\r\n * Threat Timeline\r\n */\r\nexport interface ThreatTimeline {\r\n  /** When threat was first detected */\r\n  firstDetected: Date;\r\n  /** When threat was last seen */\r\n  lastSeen: Date;\r\n  /** When threat was confirmed */\r\n  confirmed?: Date;\r\n  /** When containment started */\r\n  containmentStarted?: Date;\r\n  /** When threat was contained */\r\n  contained?: Date;\r\n  /** When eradication started */\r\n  eradicationStarted?: Date;\r\n  /** When threat was eradicated */\r\n  eradicated?: Date;\r\n  /** When recovery started */\r\n  recoveryStarted?: Date;\r\n  /** When recovery was completed */\r\n  recoveryCompleted?: Date;\r\n}\r\n\r\n/**\r\n * Mitigation Status\r\n */\r\nexport interface MitigationStatus {\r\n  /** Current status */\r\n  status: 'detected' | 'investigating' | 'containing' | 'contained' | 'eradicating' | 'eradicated' | 'recovering' | 'recovered';\r\n  /** Mitigation actions taken */\r\n  actionsTaken: MitigationAction[];\r\n  /** Assigned analyst */\r\n  assignedAnalyst?: string;\r\n  /** Incident commander */\r\n  incidentCommander?: string;\r\n  /** Response team members */\r\n  responseTeam: string[];\r\n  /** Next planned actions */\r\n  nextActions: string[];\r\n}\r\n\r\n/**\r\n * Mitigation Action\r\n */\r\nexport interface MitigationAction {\r\n  /** Action type */\r\n  type: 'isolation' | 'quarantine' | 'blocking' | 'monitoring' | 'patching' | 'removal' | 'analysis';\r\n  /** Action description */\r\n  description: string;\r\n  /** When action was taken */\r\n  timestamp: Date;\r\n  /** Who performed the action */\r\n  performedBy: string;\r\n  /** Action result */\r\n  result: 'success' | 'failure' | 'partial' | 'pending';\r\n  /** Additional notes */\r\n  notes?: string;\r\n}\r\n\r\n/**\r\n * Risk Assessment\r\n */\r\nexport interface RiskAssessment {\r\n  /** Overall risk score (0-100) */\r\n  riskScore: number;\r\n  /** Impact assessment */\r\n  impact: {\r\n    confidentiality: 'none' | 'low' | 'medium' | 'high';\r\n    integrity: 'none' | 'low' | 'medium' | 'high';\r\n    availability: 'none' | 'low' | 'medium' | 'high';\r\n    financial: number; // Estimated financial impact\r\n    reputational: 'none' | 'low' | 'medium' | 'high';\r\n  };\r\n  /** Likelihood assessment */\r\n  likelihood: {\r\n    exploitability: 'none' | 'low' | 'medium' | 'high';\r\n    weaponization: 'none' | 'low' | 'medium' | 'high';\r\n    spread: 'none' | 'low' | 'medium' | 'high';\r\n  };\r\n  /** Business impact */\r\n  businessImpact: string[];\r\n  /** Compliance implications */\r\n  complianceImplications: string[];\r\n}\r\n\r\n/**\r\n * Threat Entity\r\n * \r\n * Represents a security threat with comprehensive tracking and management capabilities.\r\n * Serves as the aggregate root for threat response workflows.\r\n * \r\n * Key responsibilities:\r\n * - Threat lifecycle management\r\n * - Severity and risk assessment\r\n * - Mitigation tracking\r\n * - Attribution and intelligence\r\n * - Timeline and status management\r\n * \r\n * Business Rules:\r\n * - Severity changes must be justified and tracked\r\n * - Mitigation actions must be recorded with timestamps\r\n * - High-severity threats require immediate assignment\r\n * - Risk scores must be recalculated when indicators change\r\n */\r\nexport class Threat extends BaseAggregateRoot<ThreatProps> {\r\n  private static readonly MIN_CONFIDENCE = 0;\r\n  private static readonly MAX_CONFIDENCE = 100;\r\n  private static readonly MIN_RISK_SCORE = 0;\r\n  private static readonly MAX_RISK_SCORE = 100;\r\n\r\n  constructor(props: ThreatProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n  }\r\n\r\n  protected validate(): void {\r\n    if (!this.props.name || this.props.name.trim().length === 0) {\r\n      throw new Error('Threat must have a name');\r\n    }\r\n\r\n    if (!this.props.description || this.props.description.trim().length === 0) {\r\n      throw new Error('Threat must have a description');\r\n    }\r\n\r\n    if (!Object.values(ThreatSeverity).includes(this.props.severity)) {\r\n      throw new Error(`Invalid threat severity: ${this.props.severity}`);\r\n    }\r\n\r\n    if (!this.props.category || this.props.category.trim().length === 0) {\r\n      throw new Error('Threat must have a category');\r\n    }\r\n\r\n    if (!this.props.type || this.props.type.trim().length === 0) {\r\n      throw new Error('Threat must have a type');\r\n    }\r\n\r\n    if (this.props.confidence < Threat.MIN_CONFIDENCE || this.props.confidence > Threat.MAX_CONFIDENCE) {\r\n      throw new Error(`Threat confidence must be between ${Threat.MIN_CONFIDENCE} and ${Threat.MAX_CONFIDENCE}`);\r\n    }\r\n\r\n    if (this.props.riskAssessment.riskScore < Threat.MIN_RISK_SCORE || \r\n        this.props.riskAssessment.riskScore > Threat.MAX_RISK_SCORE) {\r\n      throw new Error(`Risk score must be between ${Threat.MIN_RISK_SCORE} and ${Threat.MAX_RISK_SCORE}`);\r\n    }\r\n\r\n    if (!this.props.timeline.firstDetected) {\r\n      throw new Error('Threat must have a first detected timestamp');\r\n    }\r\n\r\n    if (!this.props.mitigationStatus.status) {\r\n      throw new Error('Threat must have a mitigation status');\r\n    }\r\n\r\n    // Validate timeline consistency\r\n    if (this.props.timeline.lastSeen < this.props.timeline.firstDetected) {\r\n      throw new Error('Last seen cannot be before first detected');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a new threat\r\n   */\r\n  static create(\r\n    name: string,\r\n    description: string,\r\n    severity: ThreatSeverity,\r\n    category: string,\r\n    type: string,\r\n    confidence: number,\r\n    options?: {\r\n      subcategory?: string;\r\n      indicators?: IOC[];\r\n      cvssScores?: CVSSScore[];\r\n      attribution?: ThreatAttribution;\r\n      malwareFamily?: MalwareFamily;\r\n      techniques?: AttackTechnique[];\r\n      affectedAssets?: string[];\r\n      tags?: string[];\r\n      attributes?: Record<string, any>;\r\n    }\r\n  ): Threat {\r\n    const now = new Date();\r\n    \r\n    const props: ThreatProps = {\r\n      name: name.trim(),\r\n      description: description.trim(),\r\n      severity,\r\n      category: category.trim(),\r\n      subcategory: options?.subcategory?.trim(),\r\n      type: type.trim(),\r\n      confidence,\r\n      indicators: options?.indicators || [],\r\n      cvssScores: options?.cvssScores || [],\r\n      attribution: options?.attribution,\r\n      malwareFamily: options?.malwareFamily,\r\n      techniques: options?.techniques || [],\r\n      affectedAssets: options?.affectedAssets || [],\r\n      timeline: {\r\n        firstDetected: now,\r\n        lastSeen: now,\r\n      },\r\n      mitigationStatus: {\r\n        status: 'detected',\r\n        actionsTaken: [],\r\n        responseTeam: [],\r\n        nextActions: [],\r\n      },\r\n      riskAssessment: Threat.calculateInitialRiskAssessment(severity, confidence),\r\n      tags: options?.tags || [],\r\n      attributes: options?.attributes || {},\r\n    };\r\n\r\n    const threat = new Threat(props);\r\n\r\n    // Publish domain event\r\n    threat.addDomainEvent(new ThreatDetectedEvent(\r\n      threat.id,\r\n      {\r\n        threatId: threat.id.toString(),\r\n        name: threat.props.name,\r\n        severity: threat.props.severity,\r\n        category: threat.props.category,\r\n        type: threat.props.type,\r\n        confidence: threat.props.confidence,\r\n        riskScore: threat.props.riskAssessment.riskScore,\r\n        indicatorCount: threat.props.indicators.length,\r\n        affectedAssetCount: threat.props.affectedAssets.length,\r\n        timestamp: now.toISOString(),\r\n      }\r\n    ));\r\n\r\n    return threat;\r\n  }\r\n\r\n  private static calculateInitialRiskAssessment(\r\n    severity: ThreatSeverity,\r\n    confidence: number\r\n  ): RiskAssessment {\r\n    // Base risk score from severity\r\n    let riskScore = 0;\r\n    switch (severity) {\r\n      case ThreatSeverity.CRITICAL: riskScore = 90; break;\r\n      case ThreatSeverity.HIGH: riskScore = 70; break;\r\n      case ThreatSeverity.MEDIUM: riskScore = 50; break;\r\n      case ThreatSeverity.LOW: riskScore = 30; break;\r\n      case ThreatSeverity.UNKNOWN: riskScore = 20; break;\r\n    }\r\n\r\n    // Adjust by confidence\r\n    riskScore = Math.round(riskScore * (confidence / 100));\r\n\r\n    return {\r\n      riskScore: Math.max(0, Math.min(100, riskScore)),\r\n      impact: {\r\n        confidentiality: severity === ThreatSeverity.CRITICAL ? 'high' : 'medium',\r\n        integrity: severity === ThreatSeverity.CRITICAL ? 'high' : 'medium',\r\n        availability: severity === ThreatSeverity.CRITICAL ? 'high' : 'medium',\r\n        financial: 0, // To be assessed\r\n        reputational: severity === ThreatSeverity.CRITICAL ? 'high' : 'low',\r\n      },\r\n      likelihood: {\r\n        exploitability: 'medium',\r\n        weaponization: 'medium',\r\n        spread: 'low',\r\n      },\r\n      businessImpact: [],\r\n      complianceImplications: [],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get threat name\r\n   */\r\n  get name(): string {\r\n    return this.props.name;\r\n  }\r\n\r\n  /**\r\n   * Get threat description\r\n   */\r\n  get description(): string {\r\n    return this.props.description;\r\n  }\r\n\r\n  /**\r\n   * Get threat severity\r\n   */\r\n  get severity(): ThreatSeverity {\r\n    return this.props.severity;\r\n  }\r\n\r\n  /**\r\n   * Get threat category\r\n   */\r\n  get category(): string {\r\n    return this.props.category;\r\n  }\r\n\r\n  /**\r\n   * Get threat type\r\n   */\r\n  get type(): string {\r\n    return this.props.type;\r\n  }\r\n\r\n  /**\r\n   * Get confidence score\r\n   */\r\n  get confidence(): number {\r\n    return this.props.confidence;\r\n  }\r\n\r\n  /**\r\n   * Get indicators\r\n   */\r\n  get indicators(): IOC[] {\r\n    return [...this.props.indicators];\r\n  }\r\n\r\n  /**\r\n   * Get CVSS scores\r\n   */\r\n  get cvssScores(): CVSSScore[] {\r\n    return [...this.props.cvssScores];\r\n  }\r\n\r\n  /**\r\n   * Get attribution\r\n   */\r\n  get attribution(): ThreatAttribution | undefined {\r\n    return this.props.attribution;\r\n  }\r\n\r\n  /**\r\n   * Get malware family\r\n   */\r\n  get malwareFamily(): MalwareFamily | undefined {\r\n    return this.props.malwareFamily;\r\n  }\r\n\r\n  /**\r\n   * Get attack techniques\r\n   */\r\n  get techniques(): AttackTechnique[] {\r\n    return [...this.props.techniques];\r\n  }\r\n\r\n  /**\r\n   * Get affected assets\r\n   */\r\n  get affectedAssets(): string[] {\r\n    return [...this.props.affectedAssets];\r\n  }\r\n\r\n  /**\r\n   * Get timeline\r\n   */\r\n  get timeline(): ThreatTimeline {\r\n    return this.props.timeline;\r\n  }\r\n\r\n  /**\r\n   * Get mitigation status\r\n   */\r\n  get mitigationStatus(): MitigationStatus {\r\n    return this.props.mitigationStatus;\r\n  }\r\n\r\n  /**\r\n   * Get risk assessment\r\n   */\r\n  get riskAssessment(): RiskAssessment {\r\n    return this.props.riskAssessment;\r\n  }\r\n\r\n  /**\r\n   * Get tags\r\n   */\r\n  get tags(): string[] {\r\n    return [...this.props.tags];\r\n  }\r\n\r\n  /**\r\n   * Get attributes\r\n   */\r\n  get attributes(): Record<string, any> {\r\n    return { ...this.props.attributes };\r\n  }\r\n\r\n  /**\r\n   * Change threat severity\r\n   */\r\n  changeSeverity(newSeverity: ThreatSeverity, reason: string): void {\r\n    if (newSeverity === this.props.severity) {\r\n      return; // No change needed\r\n    }\r\n\r\n    const oldSeverity = this.props.severity;\r\n    this.props.severity = newSeverity;\r\n\r\n    // Recalculate risk assessment\r\n    this.recalculateRiskAssessment();\r\n\r\n    // Publish domain event\r\n    this.addDomainEvent(new ThreatSeverityChangedEvent(\r\n      this.id,\r\n      {\r\n        threatId: this.id.toString(),\r\n        oldSeverity,\r\n        newSeverity,\r\n        reason,\r\n        newRiskScore: this.props.riskAssessment.riskScore,\r\n        timestamp: new Date().toISOString(),\r\n      }\r\n    ));\r\n  }\r\n\r\n  /**\r\n   * Add indicators\r\n   */\r\n  addIndicators(indicators: IOC[]): void {\r\n    const newIndicators = indicators.filter(\r\n      newIoc => !this.props.indicators.some(existingIoc => existingIoc.equals(newIoc))\r\n    );\r\n    \r\n    this.props.indicators.push(...newIndicators);\r\n    this.recalculateRiskAssessment();\r\n  }\r\n\r\n  /**\r\n   * Add mitigation action\r\n   */\r\n  addMitigationAction(action: Omit<MitigationAction, 'timestamp'>): void {\r\n    const mitigationAction: MitigationAction = {\r\n      ...action,\r\n      timestamp: new Date(),\r\n    };\r\n    \r\n    this.props.mitigationStatus.actionsTaken.push(mitigationAction);\r\n  }\r\n\r\n  /**\r\n   * Update mitigation status\r\n   */\r\n  updateMitigationStatus(\r\n    status: MitigationStatus['status'],\r\n    options?: {\r\n      assignedAnalyst?: string;\r\n      incidentCommander?: string;\r\n      responseTeam?: string[];\r\n      nextActions?: string[];\r\n    }\r\n  ): void {\r\n    this.props.mitigationStatus.status = status;\r\n    \r\n    if (options?.assignedAnalyst) {\r\n      this.props.mitigationStatus.assignedAnalyst = options.assignedAnalyst;\r\n    }\r\n    \r\n    if (options?.incidentCommander) {\r\n      this.props.mitigationStatus.incidentCommander = options.incidentCommander;\r\n    }\r\n    \r\n    if (options?.responseTeam) {\r\n      this.props.mitigationStatus.responseTeam = options.responseTeam;\r\n    }\r\n    \r\n    if (options?.nextActions) {\r\n      this.props.mitigationStatus.nextActions = options.nextActions;\r\n    }\r\n\r\n    // Update timeline based on status\r\n    this.updateTimelineForStatus(status);\r\n  }\r\n\r\n  private updateTimelineForStatus(status: MitigationStatus['status']): void {\r\n    const now = new Date();\r\n    \r\n    switch (status) {\r\n      case 'containing':\r\n        if (!this.props.timeline.containmentStarted) {\r\n          this.props.timeline.containmentStarted = now;\r\n        }\r\n        break;\r\n      case 'contained':\r\n        if (!this.props.timeline.contained) {\r\n          this.props.timeline.contained = now;\r\n        }\r\n        break;\r\n      case 'eradicating':\r\n        if (!this.props.timeline.eradicationStarted) {\r\n          this.props.timeline.eradicationStarted = now;\r\n        }\r\n        break;\r\n      case 'eradicated':\r\n        if (!this.props.timeline.eradicated) {\r\n          this.props.timeline.eradicated = now;\r\n        }\r\n        break;\r\n      case 'recovering':\r\n        if (!this.props.timeline.recoveryStarted) {\r\n          this.props.timeline.recoveryStarted = now;\r\n        }\r\n        break;\r\n      case 'recovered':\r\n        if (!this.props.timeline.recoveryCompleted) {\r\n          this.props.timeline.recoveryCompleted = now;\r\n        }\r\n        break;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if threat is active\r\n   */\r\n  isActive(): boolean {\r\n    const activeStatuses = ['detected', 'investigating', 'containing', 'eradicating', 'recovering'];\r\n    return activeStatuses.includes(this.props.mitigationStatus.status);\r\n  }\r\n\r\n  /**\r\n   * Check if threat is resolved\r\n   */\r\n  isResolved(): boolean {\r\n    return this.props.mitigationStatus.status === 'recovered';\r\n  }\r\n\r\n  /**\r\n   * Check if threat is high severity\r\n   */\r\n  isHighSeverity(): boolean {\r\n    return this.props.severity === ThreatSeverity.HIGH || \r\n           this.props.severity === ThreatSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if threat requires immediate attention\r\n   */\r\n  requiresImmediateAttention(): boolean {\r\n    return this.props.severity === ThreatSeverity.CRITICAL ||\r\n           (this.props.severity === ThreatSeverity.HIGH && this.props.confidence >= 80);\r\n  }\r\n\r\n  /**\r\n   * Get threat age in hours\r\n   */\r\n  getAge(): number {\r\n    return (Date.now() - this.props.timeline.firstDetected.getTime()) / (1000 * 60 * 60);\r\n  }\r\n\r\n  /**\r\n   * Recalculate risk assessment\r\n   */\r\n  private recalculateRiskAssessment(): void {\r\n    let riskScore = this.props.riskAssessment.riskScore;\r\n\r\n    // Adjust for indicators\r\n    const highSeverityIndicators = this.props.indicators.filter(ioc => ioc.isHighSeverity());\r\n    riskScore += highSeverityIndicators.length * 5;\r\n\r\n    // Adjust for CVSS scores\r\n    const highCVSSScores = this.props.cvssScores.filter(score => score.isHighOrCritical());\r\n    riskScore += highCVSSScores.length * 10;\r\n\r\n    // Adjust for affected assets\r\n    riskScore += Math.min(this.props.affectedAssets.length * 2, 20);\r\n\r\n    this.props.riskAssessment.riskScore = Math.max(0, Math.min(100, riskScore));\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      id: this.id.toString(),\r\n      name: this.props.name,\r\n      description: this.props.description,\r\n      severity: this.props.severity,\r\n      category: this.props.category,\r\n      subcategory: this.props.subcategory,\r\n      type: this.props.type,\r\n      confidence: this.props.confidence,\r\n      indicators: this.props.indicators.map(ioc => ioc.toJSON()),\r\n      cvssScores: this.props.cvssScores.map(score => score.toJSON()),\r\n      attribution: this.props.attribution,\r\n      malwareFamily: this.props.malwareFamily,\r\n      techniques: this.props.techniques,\r\n      affectedAssets: this.props.affectedAssets,\r\n      timeline: this.props.timeline,\r\n      mitigationStatus: this.props.mitigationStatus,\r\n      riskAssessment: this.props.riskAssessment,\r\n      tags: this.props.tags,\r\n      attributes: this.props.attributes,\r\n      analysis: {\r\n        isActive: this.isActive(),\r\n        isResolved: this.isResolved(),\r\n        isHighSeverity: this.isHighSeverity(),\r\n        requiresImmediateAttention: this.requiresImmediateAttention(),\r\n        age: this.getAge(),\r\n      },\r\n      createdAt: this.createdAt?.toISOString(),\r\n      updatedAt: this.updatedAt?.toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}