{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\config.module.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAwC;AACxC,2CAAkE;AAClE,yDAA2D;AAC3D,mCAWiB;AACjB,6CAAsD;AACtD,iEAAsF;AACtF,mEAA+D;AAC/D,qFAA+E;AAC/E,oEAAwE;AAExE,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,aAAa,CAAC;AAE1D,MAAM,cAAc,GAAG;IACrB,QAAQ,QAAQ,EAAE;IAClB,YAAY;IACZ,MAAM;CACP,CAAC;AAEF,MAAM,cAAc,GAAG;IACrB,sBAAc;IACd,kBAAU;IACV,iBAAS;IACT,mBAAW;IACX,gBAAQ;IACR,mBAAW;IACX,qBAAa;IACb,sBAAc;IACd,wBAAgB;IAChB,uBAAqB;CACtB,CAAC;AAEF;;;;;GAKG;AAyCI,IAAM,YAAY,GAAlB,MAAM,YAAY;CAAG,CAAA;AAAf,oCAAY;uBAAZ,YAAY;IAxCxB,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,qBAAgB,CAAC,OAAO,CAAC;gBACvB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;gBACX,eAAe,EAAE,IAAI;gBACrB,WAAW,EAAE,cAAc;gBAC3B,IAAI,EAAE,cAAc;gBACpB,gBAAgB,EAAE,mCAAsB;gBACxC,iBAAiB,EAAE;oBACjB,YAAY,EAAE,IAAI;oBAClB,UAAU,EAAE,KAAK;iBAClB;aACF,CAAC;YACF,kCAAkB,CAAC,OAAO,CAAC;gBACzB,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,KAAK;gBAClB,cAAc,EAAE,KAAK;gBACrB,YAAY,EAAE,EAAE;gBAChB,iBAAiB,EAAE,KAAK;gBACxB,YAAY,EAAE,KAAK;aACpB,CAAC;SACH;QACD,SAAS,EAAE;YACT,4CAA4B;YAC5B,oCAA4B;YAC5B,4CAAoB;YACpB,4DAA2B;YAC3B,0CAAuB;SACxB;QACD,OAAO,EAAE;YACP,qBAAgB;YAChB,4CAA4B;YAC5B,oCAA4B;YAC5B,4CAAoB;YACpB,4DAA2B;YAC3B,0CAAuB;SACxB;KACF,CAAC;GACW,YAAY,CAAG", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\config.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { ConfigModule as NestConfigModule } from '@nestjs/config';\r\nimport { EventEmitterModule } from '@nestjs/event-emitter';\r\nimport { \r\n  databaseConfig, \r\n  DatabaseConfigurationService,\r\n  authConfig,\r\n  jwtConfig,\r\n  oauthConfig,\r\n  aiConfig, \r\n  redisConfig, \r\n  loggingConfig, \r\n  securityConfig, \r\n  monitoringConfig \r\n} from './index';\r\nimport { configValidationSchema } from './validation';\r\nimport sentinelConfiguration, { SentinelConfigurationService } from './configuration';\r\nimport { EnvironmentValidator } from './environment.validator';\r\nimport { ConfigChangeDetectorService } from './config-change-detector.service';\r\nimport { ConfigValidationService } from './validators/config.validator';\r\n\r\nconst NODE_ENV = process.env['NODE_ENV'] ?? 'development';\r\n\r\nconst ENV_FILE_PATHS = [\r\n  `.env.${NODE_ENV}`,\r\n  '.env.local',\r\n  '.env'\r\n];\r\n\r\nconst CONFIG_LOADERS = [\r\n  databaseConfig,\r\n  authConfig,\r\n  jwtConfig,\r\n  oauthConfig,\r\n  aiConfig,\r\n  redisConfig,\r\n  loggingConfig,\r\n  securityConfig,\r\n  monitoringConfig,\r\n  sentinelConfiguration,\r\n];\r\n\r\n/**\r\n * Centralized configuration module for Sentinel vulnerability assessment platform.\r\n * \r\n * @description Provides environment-specific loading, validation, type-safety, and hot-reload support\r\n * @module ConfigModule\r\n */\r\n@Module({\r\n  imports: [\r\n    NestConfigModule.forRoot({\r\n      isGlobal: true,\r\n      cache: true,\r\n      expandVariables: true,\r\n      envFilePath: ENV_FILE_PATHS,\r\n      load: CONFIG_LOADERS,\r\n      validationSchema: configValidationSchema,\r\n      validationOptions: {\r\n        allowUnknown: true,\r\n        abortEarly: false,\r\n      },\r\n    }),\r\n    EventEmitterModule.forRoot({\r\n      wildcard: false,\r\n      delimiter: '.',\r\n      newListener: false,\r\n      removeListener: false,\r\n      maxListeners: 10,\r\n      verboseMemoryLeak: false,\r\n      ignoreErrors: false,\r\n    }),\r\n  ],\r\n  providers: [\r\n    SentinelConfigurationService,\r\n    DatabaseConfigurationService,\r\n    EnvironmentValidator,\r\n    ConfigChangeDetectorService,\r\n    ConfigValidationService,\r\n  ],\r\n  exports: [\r\n    NestConfigModule,\r\n    SentinelConfigurationService,\r\n    DatabaseConfigurationService,\r\n    EnvironmentValidator,\r\n    ConfigChangeDetectorService,\r\n    ConfigValidationService,\r\n  ],\r\n})\r\nexport class ConfigModule {}"], "version": 3}