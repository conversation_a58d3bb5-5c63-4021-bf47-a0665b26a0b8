c777e95fdc9e5586f87db9e79819c377
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const response_action_failed_domain_event_1 = require("../response-action-failed.domain-event");
const shared_kernel_1 = require("../../../../../shared-kernel");
const action_type_enum_1 = require("../../enums/action-type.enum");
describe('ResponseActionFailedDomainEvent', () => {
    let aggregateId;
    let eventData;
    let event;
    beforeEach(() => {
        aggregateId = shared_kernel_1.UniqueEntityId.generate();
        eventData = {
            actionType: action_type_enum_1.ActionType.BLOCK_IP,
            error: 'Network timeout while connecting to firewall',
            executedBy: 'system@automation',
            failedAt: new Date(),
            retryCount: 1,
            canRetry: true,
        };
        event = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, eventData);
    });
    describe('creation', () => {
        it('should create event with required data', () => {
            expect(event).toBeInstanceOf(response_action_failed_domain_event_1.ResponseActionFailedDomainEvent);
            expect(event.aggregateId).toBe(aggregateId);
            expect(event.eventData).toBe(eventData);
            expect(event.occurredOn).toBeInstanceOf(Date);
        });
        it('should create event with custom options', () => {
            const customOptions = {
                eventId: shared_kernel_1.UniqueEntityId.generate(),
                occurredOn: new Date('2023-01-01'),
                eventVersion: 2,
                correlationId: 'corr-123',
                causationId: 'cause-456',
                metadata: { source: 'test' },
            };
            const customEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, eventData, customOptions);
            expect(customEvent.eventId).toBe(customOptions.eventId);
            expect(customEvent.occurredOn).toBe(customOptions.occurredOn);
            expect(customEvent.eventVersion).toBe(customOptions.eventVersion);
            expect(customEvent.correlationId).toBe(customOptions.correlationId);
            expect(customEvent.causationId).toBe(customOptions.causationId);
            expect(customEvent.metadata).toEqual(customOptions.metadata);
        });
    });
    describe('getters', () => {
        it('should provide access to event data properties', () => {
            expect(event.actionType).toBe(action_type_enum_1.ActionType.BLOCK_IP);
            expect(event.error).toBe('Network timeout while connecting to firewall');
            expect(event.executedBy).toBe('system@automation');
            expect(event.failedAt).toBe(eventData.failedAt);
            expect(event.retryCount).toBe(1);
            expect(event.canRetry).toBe(true);
        });
    });
    describe('failure analysis', () => {
        it('should identify first-time failures', () => {
            const firstFailureEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                retryCount: 0,
            });
            expect(firstFailureEvent.isFirstFailure()).toBe(true);
            expect(event.isFirstFailure()).toBe(false);
        });
        it('should identify repeated failures', () => {
            expect(event.isRepeatedFailure()).toBe(true);
            const firstFailureEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                retryCount: 0,
            });
            expect(firstFailureEvent.isRepeatedFailure()).toBe(false);
        });
        it('should identify final failures', () => {
            const finalFailureEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                canRetry: false,
            });
            expect(finalFailureEvent.isFinalFailure()).toBe(true);
            expect(event.isFinalFailure()).toBe(false);
        });
    });
    describe('execution type analysis', () => {
        it('should identify automated failures', () => {
            expect(event.isAutomatedFailure()).toBe(true);
            expect(event.isManualFailure()).toBe(false);
            const systemEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                executedBy: 'automation-bot',
            });
            expect(systemEvent.isAutomatedFailure()).toBe(true);
            const botEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                executedBy: 'security-bot',
            });
            expect(botEvent.isAutomatedFailure()).toBe(true);
        });
        it('should identify manual failures', () => {
            const manualEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                executedBy: '<EMAIL>',
            });
            expect(manualEvent.isAutomatedFailure()).toBe(false);
            expect(manualEvent.isManualFailure()).toBe(true);
        });
        it('should handle missing executedBy', () => {
            const noExecutorEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                executedBy: undefined,
            });
            expect(noExecutorEvent.isAutomatedFailure()).toBe(false);
            expect(noExecutorEvent.isManualFailure()).toBe(false);
        });
    });
    describe('action type classification', () => {
        it('should identify security-critical failures', () => {
            expect(event.isSecurityCriticalFailure()).toBe(true);
            const isolateEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.ISOLATE_SYSTEM,
            });
            expect(isolateEvent.isSecurityCriticalFailure()).toBe(true);
            const shutdownEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SHUTDOWN_SYSTEM,
            });
            expect(shutdownEvent.isSecurityCriticalFailure()).toBe(true);
            const deleteEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.DELETE_FILE,
            });
            expect(deleteEvent.isSecurityCriticalFailure()).toBe(true);
            const patchEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.PATCH_VULNERABILITY,
            });
            expect(patchEvent.isSecurityCriticalFailure()).toBe(true);
            const emailEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(emailEvent.isSecurityCriticalFailure()).toBe(false);
        });
        it('should identify containment failures', () => {
            expect(event.isContainmentFailure()).toBe(true);
            const quarantineEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
            });
            expect(quarantineEvent.isContainmentFailure()).toBe(true);
            const blockDomainEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.BLOCK_DOMAIN,
            });
            expect(blockDomainEvent.isContainmentFailure()).toBe(true);
            const disableAccountEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.DISABLE_ACCOUNT,
            });
            expect(disableAccountEvent.isContainmentFailure()).toBe(true);
            const terminateEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.TERMINATE_CONNECTION,
            });
            expect(terminateEvent.isContainmentFailure()).toBe(true);
            const emailEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(emailEvent.isContainmentFailure()).toBe(false);
        });
        it('should identify recovery failures', () => {
            const backupEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            expect(backupEvent.isRecoveryFailure()).toBe(true);
            const rebuildEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.REBUILD_SYSTEM,
            });
            expect(rebuildEvent.isRecoveryFailure()).toBe(true);
            const enableEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.ENABLE_SERVICE,
            });
            expect(enableEvent.isRecoveryFailure()).toBe(true);
            const resetEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESET_PASSWORD,
            });
            expect(resetEvent.isRecoveryFailure()).toBe(true);
            expect(event.isRecoveryFailure()).toBe(false);
        });
        it('should identify notification failures', () => {
            const emailEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(emailEvent.isNotificationFailure()).toBe(true);
            const smsEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_SMS,
            });
            expect(smsEvent.isNotificationFailure()).toBe(true);
            const slackEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_SLACK,
            });
            expect(slackEvent.isNotificationFailure()).toBe(true);
            const webhookEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.TRIGGER_WEBHOOK,
            });
            expect(webhookEvent.isNotificationFailure()).toBe(true);
            expect(event.isNotificationFailure()).toBe(false);
        });
    });
    describe('failure severity assessment', () => {
        it('should assess critical severity for final security-critical failures', () => {
            const criticalEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                canRetry: false,
            });
            expect(criticalEvent.getFailureSeverity()).toBe('critical');
        });
        it('should assess high severity for security-critical failures with retries', () => {
            expect(event.getFailureSeverity()).toBe('high');
        });
        it('should assess high severity for final containment failures', () => {
            const finalContainmentEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                canRetry: false,
            });
            expect(finalContainmentEvent.getFailureSeverity()).toBe('critical');
        });
        it('should assess medium severity for containment failures with retries', () => {
            const containmentEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
            });
            expect(containmentEvent.getFailureSeverity()).toBe('high');
        });
        it('should assess high severity for final recovery failures', () => {
            const finalRecoveryEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
                canRetry: false,
            });
            expect(finalRecoveryEvent.getFailureSeverity()).toBe('high');
        });
        it('should assess medium severity for recovery failures with retries', () => {
            const recoveryEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            expect(recoveryEvent.getFailureSeverity()).toBe('medium');
        });
        it('should assess medium severity for notification failures', () => {
            const notificationEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(notificationEvent.getFailureSeverity()).toBe('medium');
        });
        it('should assess low severity for other failures', () => {
            const otherEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.EXECUTE_SCRIPT,
            });
            expect(otherEvent.getFailureSeverity()).toBe('low');
        });
    });
    describe('failure categorization', () => {
        it('should categorize permission failures', () => {
            const permissionEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Permission denied: unauthorized access',
            });
            expect(permissionEvent.getFailureCategory()).toBe('permission');
            const forbiddenEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Forbidden: insufficient privileges',
            });
            expect(forbiddenEvent.getFailureCategory()).toBe('permission');
        });
        it('should categorize timeout failures', () => {
            const timeoutEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Operation timed out after 30 seconds',
            });
            expect(timeoutEvent.getFailureCategory()).toBe('timeout');
        });
        it('should categorize resource failures', () => {
            const resourceEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Insufficient memory to complete operation',
            });
            expect(resourceEvent.getFailureCategory()).toBe('resource');
            const diskEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Disk space full',
            });
            expect(diskEvent.getFailureCategory()).toBe('resource');
        });
        it('should categorize configuration failures', () => {
            const configEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Invalid configuration parameter',
            });
            expect(configEvent.getFailureCategory()).toBe('configuration');
            const settingEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Missing required setting',
            });
            expect(settingEvent.getFailureCategory()).toBe('configuration');
        });
        it('should categorize network failures', () => {
            const networkEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Network connection refused',
            });
            expect(networkEvent.getFailureCategory()).toBe('network');
            const dnsEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'DNS resolution failed',
            });
            expect(dnsEvent.getFailureCategory()).toBe('network');
        });
        it('should categorize technical failures', () => {
            const exceptionEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Unhandled exception occurred',
            });
            expect(exceptionEvent.getFailureCategory()).toBe('technical');
            const errorEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'System error: operation failed',
            });
            expect(errorEvent.getFailureCategory()).toBe('technical');
        });
        it('should categorize unknown failures', () => {
            const unknownEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Something went wrong',
            });
            expect(unknownEvent.getFailureCategory()).toBe('unknown');
        });
    });
    describe('immediate action recommendations', () => {
        it('should recommend immediate actions for security-critical failures', () => {
            const actions = event.getImmediateActions();
            expect(actions).toContain('Escalate to incident response team');
            expect(actions).toContain('Assess security impact of failure');
            expect(actions).toContain('Consider manual fallback procedures');
        });
        it('should recommend immediate actions for containment failures', () => {
            const actions = event.getImmediateActions();
            expect(actions).toContain('Implement alternative containment measures');
            expect(actions).toContain('Monitor for threat spread');
            expect(actions).toContain('Assess containment gap impact');
        });
        it('should recommend immediate actions for recovery failures', () => {
            const recoveryEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            const actions = recoveryEvent.getImmediateActions();
            expect(actions).toContain('Assess service impact');
            expect(actions).toContain('Consider alternative recovery methods');
            expect(actions).toContain('Notify affected stakeholders');
        });
        it('should recommend retry analysis for retryable failures', () => {
            const actions = event.getImmediateActions();
            expect(actions).toContain('Analyze failure cause before retry');
            expect(actions).toContain('Adjust retry parameters if needed');
        });
        it('should recommend investigation for non-retryable failures', () => {
            const finalEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                canRetry: false,
            });
            const actions = finalEvent.getImmediateActions();
            expect(actions).toContain('Investigate root cause of failure');
            expect(actions).toContain('Consider alternative action approaches');
        });
        it('should recommend category-specific actions for permission failures', () => {
            const permissionEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Permission denied',
            });
            const actions = permissionEvent.getImmediateActions();
            expect(actions).toContain('Verify action permissions and credentials');
            expect(actions).toContain('Check role-based access controls');
        });
        it('should recommend category-specific actions for timeout failures', () => {
            const timeoutEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Operation timed out',
            });
            const actions = timeoutEvent.getImmediateActions();
            expect(actions).toContain('Check system performance and load');
            expect(actions).toContain('Consider increasing timeout limits');
        });
        it('should recommend category-specific actions for resource failures', () => {
            const resourceEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Insufficient memory',
            });
            const actions = resourceEvent.getImmediateActions();
            expect(actions).toContain('Check system resource availability');
            expect(actions).toContain('Free up resources if possible');
        });
        it('should recommend category-specific actions for configuration failures', () => {
            const configEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Invalid configuration',
            });
            const actions = configEvent.getImmediateActions();
            expect(actions).toContain('Verify action configuration parameters');
            expect(actions).toContain('Check system configuration settings');
        });
        it('should recommend category-specific actions for network failures', () => {
            const networkEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Network connection failed',
            });
            const actions = networkEvent.getImmediateActions();
            expect(actions).toContain('Check network connectivity');
            expect(actions).toContain('Verify network configuration');
        });
    });
    describe('notification targets', () => {
        it('should identify targets for all failures', () => {
            const targets = event.getNotificationTargets();
            expect(targets).toContain('action-requestor');
        });
        it('should identify targets for security-critical failures', () => {
            const targets = event.getNotificationTargets();
            expect(targets).toContain('security-team');
            expect(targets).toContain('incident-response-team');
        });
        it('should identify targets for final security-critical failures', () => {
            const finalEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                canRetry: false,
            });
            const targets = finalEvent.getNotificationTargets();
            expect(targets).toContain('security-managers');
            expect(targets).toContain('on-call-engineers');
        });
        it('should identify targets for containment failures', () => {
            const targets = event.getNotificationTargets();
            expect(targets).toContain('containment-specialists');
            expect(targets).toContain('security-analysts');
        });
        it('should identify targets for recovery failures', () => {
            const recoveryEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            const targets = recoveryEvent.getNotificationTargets();
            expect(targets).toContain('recovery-team');
            expect(targets).toContain('service-owners');
        });
        it('should identify targets for notification failures', () => {
            const notificationEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            const targets = notificationEvent.getNotificationTargets();
            expect(targets).toContain('communication-team');
        });
        it('should identify targets for repeated failures', () => {
            const targets = event.getNotificationTargets();
            expect(targets).toContain('technical-support');
        });
        it('should identify targets for final failures', () => {
            const finalEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                canRetry: false,
            });
            const targets = finalEvent.getNotificationTargets();
            expect(targets).toContain('escalation-team');
        });
    });
    describe('escalation requirements', () => {
        it('should require immediate executive escalation for final security-critical failures', () => {
            const finalCriticalEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                canRetry: false,
            });
            const escalation = finalCriticalEvent.getEscalationRequirements();
            expect(escalation.immediate).toBe(true);
            expect(escalation.level).toBe('executive');
            expect(escalation.reason).toBe('Critical security action failed with no retry options');
        });
        it('should require immediate management escalation for security-critical failures', () => {
            const escalation = event.getEscalationRequirements();
            expect(escalation.immediate).toBe(true);
            expect(escalation.level).toBe('management');
            expect(escalation.reason).toBe('Critical security action failed');
        });
        it('should require immediate management escalation for final containment failures', () => {
            const finalContainmentEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
                canRetry: false,
            });
            const escalation = finalContainmentEvent.getEscalationRequirements();
            expect(escalation.immediate).toBe(true);
            expect(escalation.level).toBe('executive');
            expect(escalation.reason).toBe('Critical security action failed with no retry options');
        });
        it('should require technical escalation for repeated high-severity failures', () => {
            const repeatedHighEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.ISOLATE_SYSTEM,
                retryCount: 2,
            });
            const escalation = repeatedHighEvent.getEscalationRequirements();
            expect(escalation.immediate).toBe(true);
            expect(escalation.level).toBe('management');
            expect(escalation.reason).toBe('Critical security action failed');
        });
        it('should require no escalation for standard failures', () => {
            const standardEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
                retryCount: 0,
            });
            const escalation = standardEvent.getEscalationRequirements();
            expect(escalation.immediate).toBe(false);
            expect(escalation.level).toBe('none');
            expect(escalation.reason).toBe('Standard failure handling applies');
        });
    });
    describe('retry recommendations', () => {
        it('should recommend retry for retryable failures', () => {
            const recommendations = event.getRetryRecommendations();
            expect(recommendations.shouldRetry).toBe(true);
            expect(recommendations.delayMinutes).toBe(10);
            expect(recommendations.maxRetries).toBe(2);
            expect(recommendations.conditions).toContain('Verify system performance before retry');
        });
        it('should not recommend retry for non-retryable failures', () => {
            const finalEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                canRetry: false,
            });
            const recommendations = finalEvent.getRetryRecommendations();
            expect(recommendations.shouldRetry).toBe(false);
            expect(recommendations.delayMinutes).toBe(0);
            expect(recommendations.maxRetries).toBe(0);
            expect(recommendations.conditions).toContain('Action cannot be retried');
        });
        it('should provide timeout-specific retry recommendations', () => {
            const timeoutEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Operation timed out',
            });
            const recommendations = timeoutEvent.getRetryRecommendations();
            expect(recommendations.delayMinutes).toBe(10);
            expect(recommendations.maxRetries).toBe(2);
            expect(recommendations.conditions).toContain('Verify system performance before retry');
        });
        it('should provide resource-specific retry recommendations', () => {
            const resourceEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Insufficient memory',
            });
            const recommendations = resourceEvent.getRetryRecommendations();
            expect(recommendations.delayMinutes).toBe(15);
            expect(recommendations.maxRetries).toBe(2);
            expect(recommendations.conditions).toContain('Ensure sufficient resources available');
        });
        it('should provide network-specific retry recommendations', () => {
            const networkEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Network connection failed',
            });
            const recommendations = networkEvent.getRetryRecommendations();
            expect(recommendations.delayMinutes).toBe(5);
            expect(recommendations.maxRetries).toBe(3);
            expect(recommendations.conditions).toContain('Verify network connectivity');
        });
        it('should provide permission-specific retry recommendations', () => {
            const permissionEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Permission denied',
            });
            const recommendations = permissionEvent.getRetryRecommendations();
            expect(recommendations.delayMinutes).toBe(0);
            expect(recommendations.maxRetries).toBe(1);
            expect(recommendations.conditions).toContain('Fix permission issues before retry');
        });
        it('should provide configuration-specific retry recommendations', () => {
            const configEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'Invalid configuration',
            });
            const recommendations = configEvent.getRetryRecommendations();
            expect(recommendations.delayMinutes).toBe(0);
            expect(recommendations.maxRetries).toBe(1);
            expect(recommendations.conditions).toContain('Correct configuration before retry');
        });
        it('should require approval for security-critical action retries', () => {
            const recommendations = event.getRetryRecommendations();
            expect(recommendations.conditions).toContain('Get approval for critical action retry');
        });
    });
    describe('failure metrics', () => {
        it('should generate comprehensive failure metrics', () => {
            const metrics = event.getFailureMetrics();
            expect(metrics.actionType).toBe(action_type_enum_1.ActionType.BLOCK_IP);
            expect(metrics.failureCategory).toBe('timeout');
            expect(metrics.failureSeverity).toBe('high');
            expect(metrics.retryCount).toBe(1);
            expect(metrics.canRetry).toBe(true);
            expect(metrics.isSecurityCritical).toBe(true);
            expect(metrics.isAutomated).toBe(true);
            expect(metrics.isFinalFailure).toBe(false);
        });
        it('should generate metrics for final failures', () => {
            const finalEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                canRetry: false,
                executedBy: '<EMAIL>',
            });
            const metrics = finalEvent.getFailureMetrics();
            expect(metrics.isFinalFailure).toBe(true);
            expect(metrics.isAutomated).toBe(false);
            expect(metrics.failureSeverity).toBe('critical');
        });
    });
    describe('integration event conversion', () => {
        it('should convert to integration event format', () => {
            const integrationEvent = event.toIntegrationEvent();
            expect(integrationEvent.eventType).toBe('ResponseActionFailed');
            expect(integrationEvent.action).toBe('response_action_failed');
            expect(integrationEvent.resource).toBe('ResponseAction');
            expect(integrationEvent.resourceId).toBe(aggregateId.toString());
            expect(integrationEvent.data).toBe(eventData);
            expect(integrationEvent.metadata).toEqual({
                failureSeverity: 'high',
                failureCategory: 'timeout',
                isSecurityCritical: true,
                isFinalFailure: false,
                escalationRequired: true,
                canRetry: true,
            });
        });
    });
    describe('edge cases', () => {
        it('should handle events without executedBy', () => {
            const noExecutorEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                executedBy: undefined,
            });
            expect(noExecutorEvent.executedBy).toBeUndefined();
            expect(noExecutorEvent.isAutomatedFailure()).toBe(false);
            expect(noExecutorEvent.isManualFailure()).toBe(false);
        });
        it('should handle unknown action types gracefully', () => {
            const unknownEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                actionType: 'UNKNOWN_ACTION',
            });
            expect(unknownEvent.isSecurityCriticalFailure()).toBe(false);
            expect(unknownEvent.isContainmentFailure()).toBe(false);
            expect(unknownEvent.isRecoveryFailure()).toBe(false);
            expect(unknownEvent.isNotificationFailure()).toBe(false);
            expect(unknownEvent.getFailureSeverity()).toBe('low');
        });
        it('should handle empty error messages', () => {
            const emptyErrorEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: '',
            });
            expect(emptyErrorEvent.getFailureCategory()).toBe('unknown');
        });
        it('should handle case-insensitive error categorization', () => {
            const upperCaseEvent = new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(aggregateId, {
                ...eventData,
                error: 'PERMISSION DENIED',
            });
            expect(upperCaseEvent.getFailureCategory()).toBe('permission');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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