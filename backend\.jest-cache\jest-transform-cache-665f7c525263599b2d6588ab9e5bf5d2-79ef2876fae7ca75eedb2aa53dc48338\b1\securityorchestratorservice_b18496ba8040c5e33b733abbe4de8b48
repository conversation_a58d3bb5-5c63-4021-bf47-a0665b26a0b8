62ac5856c20622aa0abc503f37af755d
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityRiskLevel = exports.SecurityOrchestratorService = exports.RollbackStepType = exports.CommunicationFrequency = exports.ChannelType = exports.EscalationActionType = exports.EscalationTrigger = exports.CorrelationSignificance = exports.CorrelationType = exports.ThreatPatternType = exports.OrchestrationStage = exports.OrchestrationAction = void 0;
const common_1 = require("@nestjs/common");
const base_service_1 = require("../../../../shared-kernel/domain/base-service");
const event_repository_1 = require("../../domain/repositories/event.repository");
const threat_repository_1 = require("../../domain/repositories/threat.repository");
const vulnerability_repository_1 = require("../../domain/repositories/vulnerability.repository");
const response_action_repository_1 = require("../../domain/repositories/response-action.repository");
const event_processor_interface_1 = require("../../domain/interfaces/services/event-processor.interface");
const threat_detector_interface_1 = require("../../domain/interfaces/services/threat-detector.interface");
const vulnerability_scanner_interface_1 = require("../../domain/interfaces/services/vulnerability-scanner.interface");
const response_executor_interface_1 = require("../../domain/interfaces/services/response-executor.interface");
const event_severity_enum_1 = require("../../domain/enums/event-severity.enum");
const event_type_enum_1 = require("../../domain/enums/event-type.enum");
const threat_severity_enum_1 = require("../../domain/enums/threat-severity.enum");
const vulnerability_severity_enum_1 = require("../../domain/enums/vulnerability-severity.enum");
const action_status_enum_1 = require("../../domain/enums/action-status.enum");
const unique_entity_id_value_object_1 = require("../../../../shared-kernel/value-objects/unique-entity-id.value-object");
/**
 * Orchestration Action Enum
 */
var OrchestrationAction;
(function (OrchestrationAction) {
    OrchestrationAction["SKIP_PROCESSING"] = "SKIP_PROCESSING";
    OrchestrationAction["PRIORITIZE"] = "PRIORITIZE";
    OrchestrationAction["ESCALATE"] = "ESCALATE";
    OrchestrationAction["AUTO_RESPOND"] = "AUTO_RESPOND";
    OrchestrationAction["MANUAL_REVIEW"] = "MANUAL_REVIEW";
    OrchestrationAction["QUARANTINE"] = "QUARANTINE";
    OrchestrationAction["ALERT"] = "ALERT";
    OrchestrationAction["CUSTOM"] = "CUSTOM";
})(OrchestrationAction || (exports.OrchestrationAction = OrchestrationAction = {}));
/**
 * Orchestration Stage Enum
 */
var OrchestrationStage;
(function (OrchestrationStage) {
    OrchestrationStage["INITIALIZATION"] = "INITIALIZATION";
    OrchestrationStage["EVENT_PROCESSING"] = "EVENT_PROCESSING";
    OrchestrationStage["THREAT_DETECTION"] = "THREAT_DETECTION";
    OrchestrationStage["VULNERABILITY_SCANNING"] = "VULNERABILITY_SCANNING";
    OrchestrationStage["CORRELATION"] = "CORRELATION";
    OrchestrationStage["ENRICHMENT"] = "ENRICHMENT";
    OrchestrationStage["RESPONSE_EXECUTION"] = "RESPONSE_EXECUTION";
    OrchestrationStage["FINALIZATION"] = "FINALIZATION";
})(OrchestrationStage || (exports.OrchestrationStage = OrchestrationStage = {}));
/**
 * Threat Pattern Type Enum
 */
var ThreatPatternType;
(function (ThreatPatternType) {
    ThreatPatternType["FREQUENCY_ANOMALY"] = "FREQUENCY_ANOMALY";
    ThreatPatternType["TEMPORAL_PATTERN"] = "TEMPORAL_PATTERN";
    ThreatPatternType["SOURCE_CORRELATION"] = "SOURCE_CORRELATION";
    ThreatPatternType["ATTACK_CHAIN"] = "ATTACK_CHAIN";
    ThreatPatternType["LATERAL_MOVEMENT"] = "LATERAL_MOVEMENT";
    ThreatPatternType["DATA_EXFILTRATION"] = "DATA_EXFILTRATION";
    ThreatPatternType["PRIVILEGE_ESCALATION"] = "PRIVILEGE_ESCALATION";
    ThreatPatternType["PERSISTENCE"] = "PERSISTENCE";
})(ThreatPatternType || (exports.ThreatPatternType = ThreatPatternType = {}));
/**
 * Correlation Type Enum
 */
var CorrelationType;
(function (CorrelationType) {
    CorrelationType["TEMPORAL"] = "TEMPORAL";
    CorrelationType["SPATIAL"] = "SPATIAL";
    CorrelationType["CAUSAL"] = "CAUSAL";
    CorrelationType["BEHAVIORAL"] = "BEHAVIORAL";
    CorrelationType["CONTEXTUAL"] = "CONTEXTUAL";
})(CorrelationType || (exports.CorrelationType = CorrelationType = {}));
/**
 * Correlation Significance Enum
 */
var CorrelationSignificance;
(function (CorrelationSignificance) {
    CorrelationSignificance["LOW"] = "LOW";
    CorrelationSignificance["MEDIUM"] = "MEDIUM";
    CorrelationSignificance["HIGH"] = "HIGH";
    CorrelationSignificance["CRITICAL"] = "CRITICAL";
})(CorrelationSignificance || (exports.CorrelationSignificance = CorrelationSignificance = {}));
/**
 * Escalation Trigger Enum
 */
var EscalationTrigger;
(function (EscalationTrigger) {
    EscalationTrigger["TIME_ELAPSED"] = "TIME_ELAPSED";
    EscalationTrigger["FAILED_ACTIONS"] = "FAILED_ACTIONS";
    EscalationTrigger["SEVERITY_INCREASE"] = "SEVERITY_INCREASE";
    EscalationTrigger["IMPACT_THRESHOLD"] = "IMPACT_THRESHOLD";
    EscalationTrigger["MANUAL_TRIGGER"] = "MANUAL_TRIGGER";
    EscalationTrigger["CUSTOM"] = "CUSTOM";
})(EscalationTrigger || (exports.EscalationTrigger = EscalationTrigger = {}));
/**
 * Escalation Action Type Enum
 */
var EscalationActionType;
(function (EscalationActionType) {
    EscalationActionType["NOTIFY"] = "NOTIFY";
    EscalationActionType["CREATE_TICKET"] = "CREATE_TICKET";
    EscalationActionType["ALERT_ONCALL"] = "ALERT_ONCALL";
    EscalationActionType["ACTIVATE_TEAM"] = "ACTIVATE_TEAM";
    EscalationActionType["EMERGENCY_PROCEDURE"] = "EMERGENCY_PROCEDURE";
    EscalationActionType["CUSTOM"] = "CUSTOM";
})(EscalationActionType || (exports.EscalationActionType = EscalationActionType = {}));
/**
 * Channel Type Enum
 */
var ChannelType;
(function (ChannelType) {
    ChannelType["EMAIL"] = "EMAIL";
    ChannelType["SLACK"] = "SLACK";
    ChannelType["TEAMS"] = "TEAMS";
    ChannelType["SMS"] = "SMS";
    ChannelType["WEBHOOK"] = "WEBHOOK";
    ChannelType["DASHBOARD"] = "DASHBOARD";
})(ChannelType || (exports.ChannelType = ChannelType = {}));
/**
 * Communication Frequency Enum
 */
var CommunicationFrequency;
(function (CommunicationFrequency) {
    CommunicationFrequency["IMMEDIATE"] = "IMMEDIATE";
    CommunicationFrequency["HOURLY"] = "HOURLY";
    CommunicationFrequency["DAILY"] = "DAILY";
    CommunicationFrequency["ON_CHANGE"] = "ON_CHANGE";
    CommunicationFrequency["CUSTOM"] = "CUSTOM";
})(CommunicationFrequency || (exports.CommunicationFrequency = CommunicationFrequency = {}));
/**
 * Rollback Step Type Enum
 */
var RollbackStepType;
(function (RollbackStepType) {
    RollbackStepType["CONFIGURATION_RESTORE"] = "CONFIGURATION_RESTORE";
    RollbackStepType["SERVICE_RESTART"] = "SERVICE_RESTART";
    RollbackStepType["RULE_REMOVAL"] = "RULE_REMOVAL";
    RollbackStepType["POLICY_REVERT"] = "POLICY_REVERT";
    RollbackStepType["NETWORK_RESTORE"] = "NETWORK_RESTORE";
    RollbackStepType["DATA_RESTORE"] = "DATA_RESTORE";
    RollbackStepType["CUSTOM"] = "CUSTOM";
})(RollbackStepType || (exports.RollbackStepType = RollbackStepType = {}));
/**
 * Security Orchestrator Service
 *
 * Comprehensive application service that coordinates complex security workflows.
 * Implements sophisticated orchestration logic for event processing, threat detection,
 * vulnerability management, and automated response execution.
 *
 * Key responsibilities:
 * - Coordinate security event processing pipeline
 * - Orchestrate threat detection and analysis
 * - Manage vulnerability scanning workflows
 * - Execute automated response actions
 * - Perform threat hunting operations
 * - Handle incident response coordination
 * - Manage cross-cutting concerns (transactions, logging, metrics)
 */
let SecurityOrchestratorService = class SecurityOrchestratorService extends base_service_1.BaseService {
    constructor(eventRepository, threatRepository, vulnerabilityRepository, responseActionRepository, eventProcessor, threatDetector, vulnerabilityScanner, responseExecutor) {
        super('SecurityOrchestratorService');
        this.eventRepository = eventRepository;
        this.threatRepository = threatRepository;
        this.vulnerabilityRepository = vulnerabilityRepository;
        this.responseActionRepository = responseActionRepository;
        this.eventProcessor = eventProcessor;
        this.threatDetector = threatDetector;
        this.vulnerabilityScanner = vulnerabilityScanner;
        this.responseExecutor = responseExecutor;
    }
    /**
     * Orchestrate comprehensive security event processing
     * Main entry point for security workflow coordination
     */
    async orchestrateSecurityWorkflow(events, context) {
        const orchestrationContext = this.createContext({
            priority: response_executor_interface_1.ResponsePriority.MEDIUM,
            mode: response_executor_interface_1.ResponseMode.AUTOMATIC,
            timeoutMs: 300000, // 5 minutes
            autoResponse: true,
            enableThreatHunting: true,
            enableVulnerabilityScanning: true,
            workflowConfig: this.getDefaultWorkflowConfig(),
            ...context,
        });
        return this.executeOperation(async () => this.performSecurityOrchestration(events, orchestrationContext), orchestrationContext, 'orchestrateSecurityWorkflow');
    }
    /**
     * Process security events through the complete pipeline
     */
    async processSecurityEvents(events, context) {
        const processingContext = this.createContext({
            priority: response_executor_interface_1.ResponsePriority.HIGH,
            mode: response_executor_interface_1.ResponseMode.AUTOMATIC,
            timeoutMs: 180000, // 3 minutes
            workflowConfig: this.getDefaultWorkflowConfig(),
            ...context,
        });
        return this.executeOperation(async () => this.executeEventProcessing(events, processingContext), processingContext, 'processSecurityEvents');
    }
    /**
     * Perform comprehensive threat hunting operation
     */
    async performThreatHunting(criteria, context) {
        const huntingContext = this.createContext({
            priority: response_executor_interface_1.ResponsePriority.MEDIUM,
            mode: response_executor_interface_1.ResponseMode.MANUAL,
            timeoutMs: 600000, // 10 minutes
            enableThreatHunting: true,
            ...context,
        });
        return this.executeOperation(async () => this.executeThreatHunting(criteria, huntingContext), huntingContext, 'performThreatHunting');
    }
    /**
     * Execute incident response plan
     */
    async executeIncidentResponse(incidentResponsePlan, context) {
        const responseContext = this.createContext({
            priority: response_executor_interface_1.ResponsePriority.CRITICAL,
            mode: response_executor_interface_1.ResponseMode.SEMI_AUTOMATIC,
            timeoutMs: 1800000, // 30 minutes
            autoResponse: false,
            ...context,
        });
        return this.executeOperation(async () => this.executeIncidentResponsePlan(incidentResponsePlan, responseContext), responseContext, 'executeIncidentResponse');
    }
    /**
     * Analyze security posture and generate recommendations
     */
    async analyzeSecurityPosture(timeRange, context) {
        const analysisContext = this.createContext({
            priority: response_executor_interface_1.ResponsePriority.LOW,
            mode: response_executor_interface_1.ResponseMode.MANUAL,
            timeoutMs: 900000, // 15 minutes
            ...context,
        });
        return this.executeOperation(async () => this.performSecurityPostureAnalysis(timeRange, analysisContext), analysisContext, 'analyzeSecurityPosture');
    }
    /**
     * Main orchestration logic implementation
     */
    async performSecurityOrchestration(events, context) {
        const orchestrationId = this.generateCorrelationId();
        const startTime = new Date();
        const result = {
            success: true,
            orchestrationId,
            startTime,
            endTime: new Date(),
            duration: 0,
            eventsProcessed: 0,
            threatsDetected: 0,
            vulnerabilitiesFound: 0,
            actionsExecuted: 0,
            correlationsCreated: 0,
            enrichmentsPerformed: 0,
            processingResults: [],
            threatAnalyses: [],
            vulnerabilityScans: [],
            responseResults: [],
            errors: [],
            warnings: [],
            metrics: this.initializeMetrics(),
            recommendations: [],
        };
        try {
            this.logger.log(`Starting security orchestration for ${events.length} events`, {
                orchestrationId,
                eventCount: events.length,
                priority: context.priority,
                mode: context.mode,
            });
            // Apply orchestration rules
            const filteredEvents = await this.applyOrchestrationRules(events, context);
            // Process events in batches
            const batchSize = context.workflowConfig?.batchSize || 10;
            const batches = this.createBatches(filteredEvents, batchSize);
            for (const batch of batches) {
                await this.processBatch(batch, context, result);
            }
            // Perform correlation analysis
            if (context.workflowConfig?.enableCorrelation) {
                await this.performCorrelationAnalysis(filteredEvents, context, result);
            }
            // Generate recommendations
            result.recommendations = await this.generateRecommendations(result, context);
            result.endTime = new Date();
            result.duration = result.endTime.getTime() - startTime.getTime();
            result.metrics = await this.calculateMetrics(result, context);
            this.logger.log(`Security orchestration completed`, {
                orchestrationId,
                duration: result.duration,
                eventsProcessed: result.eventsProcessed,
                threatsDetected: result.threatsDetected,
                success: result.success,
            });
            return result;
        }
        catch (error) {
            this.logger.error(`Security orchestration failed`, {
                orchestrationId,
                error: error.message,
                stack: error.stack,
            });
            result.success = false;
            result.errors.push({
                stage: OrchestrationStage.INITIALIZATION,
                errorCode: 'ORCHESTRATION_FAILED',
                message: error.message,
                timestamp: new Date(),
                recoverable: false,
            });
            return result;
        }
    }
    /**
     * Process a batch of events
     */
    async processBatch(events, context, result) {
        const batchPromises = events.map(async (event) => {
            try {
                // Process event through pipeline
                if (context.workflowConfig?.enableEventProcessing) {
                    const processingResult = await this.processEvent(event, context);
                    result.processingResults.push(processingResult);
                    result.eventsProcessed++;
                }
                // Threat detection
                if (context.workflowConfig?.enableThreatDetection) {
                    const threatAnalysis = await this.detectThreats(event, context);
                    result.threatAnalyses.push(threatAnalysis);
                    result.threatsDetected += threatAnalysis.threats.length;
                }
                // Vulnerability scanning
                if (context.workflowConfig?.enableVulnerabilityScanning && this.shouldScanForVulnerabilities(event)) {
                    const vulnerabilityScan = await this.scanForVulnerabilities(event, context);
                    result.vulnerabilityScans.push(vulnerabilityScan);
                    result.vulnerabilitiesFound += vulnerabilityScan.vulnerabilities.length;
                }
                // Automated response
                if (context.autoResponse && context.workflowConfig?.enableResponseExecution) {
                    const responseResult = await this.executeAutomatedResponse(event, context, result);
                    if (responseResult) {
                        result.responseResults.push(responseResult);
                        result.actionsExecuted += responseResult.actionsExecuted;
                    }
                }
            }
            catch (error) {
                this.logger.error(`Error processing event ${event.id}`, {
                    eventId: event.id.toString(),
                    error: error.message,
                });
                result.errors.push({
                    stage: OrchestrationStage.EVENT_PROCESSING,
                    eventId: event.id.toString(),
                    errorCode: 'EVENT_PROCESSING_FAILED',
                    message: error.message,
                    timestamp: new Date(),
                    recoverable: true,
                });
            }
        });
        await Promise.allSettled(batchPromises);
    }
    /**
     * Process individual event through pipeline
     */
    async processEvent(event, context) {
        const processingContext = {
            requestId: this.generateCorrelationId(),
            startTime: new Date(),
            config: {
                enableNormalization: true,
                enableEnrichment: true,
                enableCorrelation: true,
                enableThreatAnalysis: true,
                timeoutMs: context.timeoutMs || 60000,
                maxRetries: 3,
                batchSize: 1,
            },
            userContext: {
                userId: context.userId || 'system',
                tenantId: context.tenantId || 'default',
                permissions: ['security:process'],
            },
            correlationId: context.correlationId,
        };
        return this.eventProcessor.processEvent(event, processingContext);
    }
    /**
     * Detect threats for an event
     */
    async detectThreats(event, context) {
        const detectionContext = {
            requestId: this.generateCorrelationId(),
            config: {
                enableRealTimeDetection: true,
                sensitivity: 80,
                minConfidenceThreshold: 70,
                maxAnalysisTimeMs: 30000,
                enableMLModels: true,
                enableBehavioralAnalysis: true,
                enableThreatIntel: true,
            },
            userContext: {
                userId: context.userId || 'system',
                tenantId: context.tenantId || 'default',
            },
        };
        return this.threatDetector.analyzeEvent(event, detectionContext);
    }
    /**
     * Scan for vulnerabilities
     */
    async scanForVulnerabilities(event, context) {
        const scannerContext = {
            requestId: this.generateCorrelationId(),
            config: {
                scanType: 'QUICK',
                includeLowSeverity: false,
                timeoutMs: 60000,
                maxConcurrentScans: 5,
            },
            userContext: {
                userId: context.userId || 'system',
                tenantId: context.tenantId || 'default',
            },
        };
        return this.vulnerabilityScanner.scanEvent(event, scannerContext);
    }
    /**
     * Execute automated response
     */
    async executeAutomatedResponse(event, context, orchestrationResult) {
        // Determine if response is needed
        const threatAnalysis = orchestrationResult.threatAnalyses.find(ta => ta.threats.some(t => t.id === event.id.toString()));
        const vulnerabilityScan = orchestrationResult.vulnerabilityScans.find(vs => vs.target.id === event.id.toString());
        if (!this.shouldExecuteResponse(event, threatAnalysis, vulnerabilityScan)) {
            return null;
        }
        const responseContext = {
            threats: threatAnalysis?.threats || [],
            vulnerabilities: vulnerabilityScan?.vulnerabilities || [],
            event,
            priority: this.determineResponsePriority(event, threatAnalysis, vulnerabilityScan),
            mode: context.mode || response_executor_interface_1.ResponseMode.AUTOMATIC,
            userContext: {
                userId: context.userId || 'system',
                tenantId: context.tenantId || 'default',
                permissions: ['security:respond'],
            },
            constraints: {
                maxExecutionTimeMs: 120000,
                maxConcurrentActions: 3,
                requiresApproval: false,
                requiresRollback: true,
            },
            correlationId: context.correlationId,
        };
        return this.responseExecutor.executeResponse(event, responseContext);
    }
    /**
     * Perform correlation analysis
     */
    async performCorrelationAnalysis(events, context, result) {
        try {
            // Find events suitable for correlation
            const correlationCandidates = await this.eventRepository.findEventsForCorrelation(3600000, // 1 hour window
            undefined, // all event types
            event_severity_enum_1.EventSeverity.MEDIUM // minimum severity
            );
            // Perform correlation logic
            const correlations = await this.detectEventCorrelations(correlationCandidates);
            result.correlationsCreated = correlations.length;
            this.logger.debug(`Created ${correlations.length} event correlations`);
        }
        catch (error) {
            this.logger.error('Error in correlation analysis', { error: error.message });
            result.warnings.push(`Correlation analysis failed: ${error.message}`);
        }
    }
    /**
     * Execute threat hunting operation
     */
    async executeThreatHunting(criteria, context) {
        const huntId = this.generateCorrelationId();
        const startTime = new Date();
        try {
            // Retrieve events based on criteria
            const events = await this.findEventsForHunting(criteria);
            // Analyze events for threats
            const threats = [];
            const threatAnalyses = await Promise.all(events.map(event => this.detectThreats(event, context)));
            for (const analysis of threatAnalyses) {
                threats.push(...analysis.threats);
            }
            // Detect patterns
            const patterns = await this.detectThreatPatterns(events, threats);
            // Create correlations if enabled
            const correlations = criteria.includeCorrelation
                ? await this.detectEventCorrelations(events)
                : [];
            const endTime = new Date();
            const result = {
                huntId,
                criteria,
                startTime,
                endTime,
                duration: endTime.getTime() - startTime.getTime(),
                eventsAnalyzed: events.length,
                threatsFound: threats.length,
                patternsDetected: patterns.length,
                events,
                threats,
                patterns,
                correlations,
                recommendations: await this.generateThreatHuntingRecommendations(threats, patterns),
                confidence: this.calculateHuntingConfidence(threats, patterns),
            };
            this.logger.log(`Threat hunting completed`, {
                huntId,
                eventsAnalyzed: events.length,
                threatsFound: threats.length,
                patternsDetected: patterns.length,
            });
            return result;
        }
        catch (error) {
            this.logger.error(`Threat hunting failed`, {
                huntId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Execute incident response plan
     */
    async executeIncidentResponsePlan(plan, context) {
        const executionId = this.generateCorrelationId();
        const startTime = new Date();
        try {
            this.logger.log(`Executing incident response plan`, {
                incidentId: plan.incidentId,
                executionId,
                actionCount: plan.actions.length,
            });
            // Sort actions by priority
            const sortedActions = plan.actions.sort((a, b) => b.priority - a.priority);
            // Execute actions
            const executedActions = [];
            let actionsExecuted = 0;
            let actionsFailed = 0;
            for (const action of sortedActions) {
                try {
                    const actionResult = await this.responseExecutor.executeAction(action.type, action.parameters);
                    executedActions.push(actionResult);
                    if (actionResult.status === action_status_enum_1.ActionStatus.COMPLETED) {
                        actionsExecuted++;
                    }
                    else {
                        actionsFailed++;
                    }
                }
                catch (error) {
                    this.logger.error(`Action execution failed`, {
                        actionId: action.id,
                        actionType: action.type,
                        error: error.message,
                    });
                    actionsFailed++;
                }
            }
            // Check escalation rules
            let escalations = 0;
            for (const rule of plan.escalationRules) {
                if (await this.evaluateEscalationRule(rule, plan, context)) {
                    await this.executeEscalation(rule.action, context);
                    escalations++;
                }
            }
            const endTime = new Date();
            const result = {
                success: actionsFailed === 0,
                executionId,
                actionsExecuted,
                actionsFailed,
                actionsSkipped: 0,
                executionTime: endTime.getTime() - startTime.getTime(),
                executedActions,
                effectivenessScore: this.calculateEffectivenessScore(actionsExecuted, actionsFailed),
                impact: await this.assessResponseImpact(executedActions),
                errors: [],
                warnings: [],
                recommendations: [],
            };
            this.logger.log(`Incident response completed`, {
                incidentId: plan.incidentId,
                executionId,
                actionsExecuted,
                actionsFailed,
                escalations,
            });
            return result;
        }
        catch (error) {
            this.logger.error(`Incident response execution failed`, {
                incidentId: plan.incidentId,
                executionId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Perform security posture analysis
     */
    async performSecurityPostureAnalysis(timeRange, context) {
        try {
            // Get comprehensive statistics
            const [eventStats, threatStats, vulnerabilityStats] = await Promise.all([
                this.eventRepository.getEventStatistics(timeRange.start, timeRange.end),
                this.threatRepository.getThreatStatistics(timeRange.start, timeRange.end),
                this.vulnerabilityRepository.getVulnerabilityStatistics(timeRange.start, timeRange.end),
            ]);
            // Calculate security scores
            const securityScore = this.calculateSecurityScore(eventStats, threatStats, vulnerabilityStats);
            const riskLevel = this.determineRiskLevel(securityScore);
            const trends = await this.analyzeTrends(timeRange);
            const recommendations = await this.generateSecurityRecommendations(eventStats, threatStats, vulnerabilityStats);
            return {
                timeRange,
                securityScore,
                riskLevel,
                eventStatistics: eventStats,
                threatStatistics: threatStats,
                vulnerabilityStatistics: vulnerabilityStats,
                trends,
                recommendations,
                generatedAt: new Date(),
            };
        }
        catch (error) {
            this.logger.error('Security posture analysis failed', { error: error.message });
            throw error;
        }
    }
    // Helper methods
    getDefaultWorkflowConfig() {
        return {
            enableEventProcessing: true,
            enableThreatDetection: true,
            enableVulnerabilityScanning: true,
            enableResponseExecution: true,
            enableCorrelation: true,
            enableEnrichment: true,
            batchSize: 10,
            maxConcurrentOperations: 5,
            retryAttempts: 3,
            timeoutMs: 300000,
        };
    }
    async applyOrchestrationRules(events, context) {
        if (!context.customRules || context.customRules.length === 0) {
            return events;
        }
        const filteredEvents = [];
        for (const event of events) {
            let shouldProcess = true;
            for (const rule of context.customRules) {
                if (rule.enabled && this.evaluateOrchestrationRule(rule, event)) {
                    if (rule.action === OrchestrationAction.SKIP_PROCESSING) {
                        shouldProcess = false;
                        break;
                    }
                }
            }
            if (shouldProcess) {
                filteredEvents.push(event);
            }
        }
        return filteredEvents;
    }
    evaluateOrchestrationRule(rule, event) {
        const condition = rule.condition;
        // Check event types
        if (condition.eventTypes && !condition.eventTypes.includes(event.type)) {
            return false;
        }
        // Check severity levels
        if (condition.severityLevels && !condition.severityLevels.includes(event.severity)) {
            return false;
        }
        // Check risk score threshold
        if (condition.riskScoreThreshold && event.riskScore < condition.riskScoreThreshold) {
            return false;
        }
        return true;
    }
    createBatches(items, batchSize) {
        const batches = [];
        for (let i = 0; i < items.length; i += batchSize) {
            batches.push(items.slice(i, i + batchSize));
        }
        return batches;
    }
    shouldScanForVulnerabilities(event) {
        const vulnerabilityEventTypes = [
            event_type_enum_1.EventType.VULNERABILITY_DETECTED,
            event_type_enum_1.EventType.SYSTEM_STARTUP,
            event_type_enum_1.EventType.CONFIGURATION_CHANGE,
            event_type_enum_1.EventType.PATCH_APPLIED,
        ];
        return vulnerabilityEventTypes.includes(event.type);
    }
    shouldExecuteResponse(event, threatAnalysis, vulnerabilityScan) {
        return (event.severity >= event_severity_enum_1.EventSeverity.HIGH ||
            (threatAnalysis && threatAnalysis.threats.length > 0) ||
            (vulnerabilityScan && vulnerabilityScan.vulnerabilities.length > 0));
    }
    determineResponsePriority(event, threatAnalysis, vulnerabilityScan) {
        if (event.severity === event_severity_enum_1.EventSeverity.CRITICAL) {
            return response_executor_interface_1.ResponsePriority.CRITICAL;
        }
        if (threatAnalysis?.threats.some(t => t.severity === event_severity_enum_1.EventSeverity.CRITICAL)) {
            return response_executor_interface_1.ResponsePriority.CRITICAL;
        }
        if (vulnerabilityScan?.vulnerabilities.some(v => v.severity === vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL)) {
            return response_executor_interface_1.ResponsePriority.CRITICAL;
        }
        if (event.severity === event_severity_enum_1.EventSeverity.HIGH) {
            return response_executor_interface_1.ResponsePriority.HIGH;
        }
        return response_executor_interface_1.ResponsePriority.MEDIUM;
    }
    async findEventsForHunting(criteria) {
        // Implementation would use repository methods to find events based on criteria
        return this.eventRepository.findByTimeRange(criteria.timeRange.start, criteria.timeRange.end, criteria.maxResults);
    }
    async detectThreatPatterns(events, threats) {
        const patterns = [];
        // Frequency analysis
        const sourceFrequency = new Map();
        events.forEach(event => {
            const source = event.source.toString();
            sourceFrequency.set(source, (sourceFrequency.get(source) || 0) + 1);
        });
        for (const [source, frequency] of sourceFrequency.entries()) {
            if (frequency > 10) {
                patterns.push({
                    id: unique_entity_id_value_object_1.UniqueEntityId.generate().toString(),
                    type: ThreatPatternType.FREQUENCY_ANOMALY,
                    name: 'High Frequency Source',
                    description: `High frequency of events from source: ${source}`,
                    confidence: Math.min(frequency / 20 * 100, 100),
                    severity: frequency > 50 ? threat_severity_enum_1.ThreatSeverity.HIGH : threat_severity_enum_1.ThreatSeverity.MEDIUM,
                    indicators: [source],
                    events: events.filter(e => e.source.toString() === source).map(e => e.id.toString()),
                    timeline: {
                        start: events[0].timestamp,
                        end: events[events.length - 1].timestamp,
                    },
                    mitreTechniques: [],
                    recommendations: [`Investigate source ${source} for potential compromise`],
                });
            }
        }
        return patterns;
    }
    async detectEventCorrelations(events) {
        const correlations = [];
        // Simple temporal correlation
        const timeWindow = 300000; // 5 minutes
        const correlatedGroups = new Map();
        events.forEach(event => {
            const timeKey = Math.floor(event.timestamp.getTime() / timeWindow).toString();
            if (!correlatedGroups.has(timeKey)) {
                correlatedGroups.set(timeKey, []);
            }
            correlatedGroups.get(timeKey).push(event);
        });
        for (const [timeKey, groupEvents] of correlatedGroups.entries()) {
            if (groupEvents.length > 1) {
                correlations.push({
                    id: unique_entity_id_value_object_1.UniqueEntityId.generate().toString(),
                    type: CorrelationType.TEMPORAL,
                    events: groupEvents.map(e => e.id.toString()),
                    confidence: Math.min(groupEvents.length * 20, 100),
                    timeWindow,
                    description: `${groupEvents.length} events occurred within ${timeWindow / 1000} seconds`,
                    significance: groupEvents.length > 5 ? CorrelationSignificance.HIGH : CorrelationSignificance.MEDIUM,
                });
            }
        }
        return correlations;
    }
    async generateRecommendations(result, context) {
        const recommendations = [];
        if (result.threatsDetected > 0) {
            recommendations.push(`${result.threatsDetected} threats detected - review and prioritize response actions`);
        }
        if (result.vulnerabilitiesFound > 0) {
            recommendations.push(`${result.vulnerabilitiesFound} vulnerabilities found - schedule remediation activities`);
        }
        if (result.errors.length > 0) {
            recommendations.push(`${result.errors.length} processing errors occurred - review system health and configuration`);
        }
        if (result.correlationsCreated > 0) {
            recommendations.push(`${result.correlationsCreated} event correlations identified - investigate potential attack patterns`);
        }
        return recommendations;
    }
    async generateThreatHuntingRecommendations(threats, patterns) {
        const recommendations = [];
        if (threats.length > 0) {
            recommendations.push(`Investigate ${threats.length} identified threats for potential security incidents`);
        }
        if (patterns.length > 0) {
            recommendations.push(`Analyze ${patterns.length} detected patterns for attack campaign indicators`);
        }
        return recommendations;
    }
    calculateHuntingConfidence(threats, patterns) {
        if (threats.length === 0 && patterns.length === 0) {
            return 0;
        }
        const threatConfidence = threats.reduce((sum, threat) => sum + (threat.confidence || 0), 0) / threats.length || 0;
        const patternConfidence = patterns.reduce((sum, pattern) => sum + pattern.confidence, 0) / patterns.length || 0;
        return (threatConfidence + patternConfidence) / 2;
    }
    async evaluateEscalationRule(rule, plan, context) {
        // Simple escalation logic - can be extended
        switch (rule.condition.type) {
            case EscalationTrigger.TIME_ELAPSED:
                const elapsed = Date.now() - plan.timeline.detection.getTime();
                return elapsed > (rule.condition.timeWindow || 3600000); // 1 hour default
            case EscalationTrigger.SEVERITY_INCREASE:
                return plan.severity === event_severity_enum_1.EventSeverity.CRITICAL;
            default:
                return false;
        }
    }
    async executeEscalation(action, context) {
        this.logger.log(`Executing escalation action: ${action.type}`, {
            actionType: action.type,
            recipients: action.recipients,
        });
        // Implementation would integrate with notification systems
        // For now, just log the escalation
    }
    calculateEffectivenessScore(actionsExecuted, actionsFailed) {
        const total = actionsExecuted + actionsFailed;
        if (total === 0)
            return 0;
        return (actionsExecuted / total) * 100;
    }
    async assessResponseImpact(executedActions) {
        // Implementation would assess the impact of executed actions
        return {
            threatMitigation: 'PARTIAL',
            vulnerabilityRemediation: 'MINIMAL',
            systemAvailability: 'NONE',
            performanceImpact: 'LOW',
            securityImprovement: 'MEDIUM',
            businessContinuity: 'NONE',
            complianceImpact: 'NONE',
        };
    }
    calculateSecurityScore(eventStats, threatStats, vulnerabilityStats) {
        // Simple scoring algorithm - can be enhanced
        let score = 100;
        // Deduct points for high-severity events
        score -= (eventStats.eventsBySeverity?.CRITICAL || 0) * 10;
        score -= (eventStats.eventsBySeverity?.HIGH || 0) * 5;
        // Deduct points for active threats
        score -= (threatStats.activeThreats || 0) * 8;
        // Deduct points for critical vulnerabilities
        score -= (vulnerabilityStats.vulnerabilitiesBySeverity?.CRITICAL || 0) * 12;
        return Math.max(0, Math.min(100, score));
    }
    determineRiskLevel(securityScore) {
        if (securityScore >= 80)
            return SecurityRiskLevel.LOW;
        if (securityScore >= 60)
            return SecurityRiskLevel.MEDIUM;
        if (securityScore >= 40)
            return SecurityRiskLevel.HIGH;
        return SecurityRiskLevel.CRITICAL;
    }
    async analyzeTrends(timeRange) {
        // Implementation would analyze trends over time
        return [];
    }
    async generateSecurityRecommendations(eventStats, threatStats, vulnerabilityStats) {
        const recommendations = [];
        if (threatStats.criticalThreatCount > 0) {
            recommendations.push(`Address ${threatStats.criticalThreatCount} critical threats immediately`);
        }
        if (vulnerabilityStats.criticalVulnerabilityCount > 0) {
            recommendations.push(`Remediate ${vulnerabilityStats.criticalVulnerabilityCount} critical vulnerabilities`);
        }
        return recommendations;
    }
    initializeMetrics() {
        return {
            totalProcessingTime: 0,
            averageEventProcessingTime: 0,
            threatDetectionTime: 0,
            vulnerabilityScanningTime: 0,
            responseExecutionTime: 0,
            throughput: 0,
            errorRate: 0,
            successRate: 0,
            resourceUtilization: {
                cpuUsage: 0,
                memoryUsage: 0,
                networkUsage: 0,
            },
        };
    }
    async calculateMetrics(result, context) {
        return {
            totalProcessingTime: result.duration,
            averageEventProcessingTime: result.eventsProcessed > 0 ? result.duration / result.eventsProcessed : 0,
            threatDetectionTime: result.threatAnalyses.reduce((sum, ta) => sum + ta.analysisDurationMs, 0),
            vulnerabilityScanningTime: result.vulnerabilityScans.reduce((sum, vs) => sum + vs.scanDuration, 0),
            responseExecutionTime: result.responseResults.reduce((sum, rr) => sum + rr.executionTime, 0),
            throughput: result.eventsProcessed / (result.duration / 1000), // events per second
            errorRate: result.errors.length / Math.max(result.eventsProcessed, 1) * 100,
            successRate: 100 - (result.errors.length / Math.max(result.eventsProcessed, 1) * 100),
            resourceUtilization: {
                cpuUsage: 0, // Would be measured from system metrics
                memoryUsage: 0,
                networkUsage: 0,
            },
        };
    }
};
exports.SecurityOrchestratorService = SecurityOrchestratorService;
exports.SecurityOrchestratorService = SecurityOrchestratorService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof event_repository_1.EventRepository !== "undefined" && event_repository_1.EventRepository) === "function" ? _a : Object, typeof (_b = typeof threat_repository_1.ThreatRepository !== "undefined" && threat_repository_1.ThreatRepository) === "function" ? _b : Object, typeof (_c = typeof vulnerability_repository_1.VulnerabilityRepository !== "undefined" && vulnerability_repository_1.VulnerabilityRepository) === "function" ? _c : Object, typeof (_d = typeof response_action_repository_1.ResponseActionRepository !== "undefined" && response_action_repository_1.ResponseActionRepository) === "function" ? _d : Object, typeof (_e = typeof event_processor_interface_1.EventProcessor !== "undefined" && event_processor_interface_1.EventProcessor) === "function" ? _e : Object, typeof (_f = typeof threat_detector_interface_1.ThreatDetector !== "undefined" && threat_detector_interface_1.ThreatDetector) === "function" ? _f : Object, typeof (_g = typeof vulnerability_scanner_interface_1.VulnerabilityScanner !== "undefined" && vulnerability_scanner_interface_1.VulnerabilityScanner) === "function" ? _g : Object, typeof (_h = typeof response_executor_interface_1.ResponseExecutor !== "undefined" && response_executor_interface_1.ResponseExecutor) === "function" ? _h : Object])
], SecurityOrchestratorService);
var SecurityRiskLevel;
(function (SecurityRiskLevel) {
    SecurityRiskLevel["LOW"] = "LOW";
    SecurityRiskLevel["MEDIUM"] = "MEDIUM";
    SecurityRiskLevel["HIGH"] = "HIGH";
    SecurityRiskLevel["CRITICAL"] = "CRITICAL";
})(SecurityRiskLevel || (exports.SecurityRiskLevel = SecurityRiskLevel = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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