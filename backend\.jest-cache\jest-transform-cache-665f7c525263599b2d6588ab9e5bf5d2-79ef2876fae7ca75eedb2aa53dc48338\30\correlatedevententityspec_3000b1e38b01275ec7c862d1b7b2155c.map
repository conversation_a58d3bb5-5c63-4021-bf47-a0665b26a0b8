{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\correlated-event.entity.spec.ts", "mappings": ";;AAAA,wEASoC;AACpC,iFAAwE;AACxE,gEAA8D;AAC9D,gHAA+F;AAC/F,iEAAwD;AACxD,yEAAgE;AAChE,qEAA4D;AAC5D,2FAAiF;AACjF,6EAAoE;AACpE,kHAAiG;AACjG,4GAA2F;AAC3F,yCAA+B;AAI/B,yCAAqC;AAErC,yCAAuC;AAiDvC,IAAA,oBAAQ,EAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,IAAI,UAAgC,CAAC;IACrC,IAAI,iBAAgC,CAAC;IAErC,IAAA,sBAAU,EAAC,GAAG,EAAE;QACd,6BAA6B;QAC7B,iBAAiB,GAAG,2CAAa,CAAC,MAAM,CAAC;YACvC,SAAS,EAAE,6CAAc,CAAC,GAAG,EAAE;YAC/B,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;gBACzB,IAAI,EAAE,MAAM;gBACZ,UAAU,EAAE,eAAe;gBAC3B,IAAI,EAAE,kBAAkB;aACzB,CAAC;YACF,cAAc,EAAE;gBACd,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,kBAAkB,EAAE,GAAG;gBACvB,OAAO,EAAE,OAAO;aACjB;SACF,CAAC,CAAC;QAEH,UAAU,GAAG;YACX,eAAe,EAAE,8BAAc,CAAC,MAAM,EAAE;YACxC,QAAQ,EAAE,iBAAiB;YAC3B,IAAI,EAAE,2BAAS,CAAC,cAAc;YAC9B,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,MAAM,EAAE,+BAAW,CAAC,MAAM;YAC1B,gBAAgB,EAAE,oDAAqB,CAAC,UAAU;YAClD,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;YAC9C,YAAY,EAAE;gBACZ,cAAc,EAAE,YAAY;gBAC5B,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACrC,iBAAiB,EAAE,CAAC,cAAc,EAAE,mBAAmB,CAAC;aACzD;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,UAAU;gBAC1B,cAAc,EAAE,CAAC;gBACjB,qBAAqB,EAAE,IAAI;gBAC3B,gBAAgB,EAAE,EAAE;aACrB;YACD,KAAK,EAAE,gCAAgC;YACvC,WAAW,EAAE,mDAAmD;YAChE,IAAI,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,UAAU,CAAC;YACzC,SAAS,EAAE,EAAE;YACb,eAAe,EAAE,uCAAe,CAAC,IAAI;YACrC,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;aAC7B;YACD,aAAa,EAAE,eAAe;YAC9B,aAAa,EAAE,EAAE;YACjB,YAAY,EAAE,EAAE;YAChB,kBAAkB,EAAE,EAAE;YACtB,eAAe,EAAE,EAAE;YACnB,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,eAAe,CAAC;YAC3D,uBAAuB,EAAE,EAAE;YAC3B,oBAAoB,EAAE,KAAK;SAC5B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,UAAU,EAAE,GAAG,EAAE;QACxB,IAAA,cAAE,EAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAE3D,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,yCAAe,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YAC5E,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC9D,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAC3E,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAC7E,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YACzE,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC3D,MAAM,YAAY,GAAG,eAAe,CAAC,oBAAoB,EAAE,CAAC;YAE5D,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,eAAe,CAAC;YAE7C,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACxD,kDAAkD,CACnD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,QAAQ,CAAC;YAEtC,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACxD,oCAAoC,CACrC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;YAElD,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACxD,6CAA6C,CAC9C,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,aAAa,EAAE,EAAE,EAAE,CAAC;YAE1D,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACxD,4CAA4C,CAC7C,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,IAAI,eAAgC,CAAC;QAErC,IAAA,sBAAU,EAAC,GAAG,EAAE;YACd,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,iBAAiB,EAAE,2CAAiB,CAAC,OAAO,EAAE,CAAC;YACrF,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,eAAe,CAAC,gBAAgB,EAAE,CAAC;YAEnC,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,2CAAiB,CAAC,WAAW,CAAC,CAAC;YAC9E,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC,WAAW,EAAE,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,eAAe,CAAC,gBAAgB,EAAE,CAAC;YAEnC,MAAM,MAAM,GAAsB;gBAChC,OAAO,EAAE,IAAI;gBACb,YAAY,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;gBAChC,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE,EAAE;gBACV,oBAAoB,EAAE,IAAI;gBAC1B,eAAe,EAAE,EAAE;gBACnB,SAAS,EAAE,CAAC;gBACZ,YAAY,EAAE,CAAC;gBACf,kBAAkB,EAAE,CAAC,mBAAmB,CAAC;aAC1C,CAAC;YAEF,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE5C,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,2CAAiB,CAAC,SAAS,CAAC,CAAC;YAC5E,MAAM,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7D,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,eAAe,CAAC,gBAAgB,EAAE,CAAC;YACnC,MAAM,KAAK,GAAG,4BAA4B,CAAC;YAE3C,eAAe,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAEvC,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,2CAAiB,CAAC,MAAM,CAAC,CAAC;YACzE,MAAM,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,MAAM,GAAG,0CAA0C,CAAC;YAC1D,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAExC,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,2CAAiB,CAAC,OAAO,CAAC,CAAC;YAC1E,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,eAAe,CAAC,gBAAgB,EAAE,CAAC;YACnC,eAAe,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAE9C,eAAe,CAAC,gBAAgB,EAAE,CAAC;YAEnC,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,2CAAiB,CAAC,OAAO,CAAC,CAAC;YAC1E,MAAM,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC,aAAa,EAAE,CAAC;YAC7D,MAAM,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC,aAAa,EAAE,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,iCAAiC;YACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,eAAe,CAAC,gBAAgB,EAAE,CAAC;gBACnC,eAAe,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;gBAC9C,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACV,eAAe,CAAC,gBAAgB,EAAE,CAAC;gBACrC,CAAC;YACH,CAAC;YAED,MAAM,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,gBAAgB,EAAE,CAAC,CAAC,OAAO,CACtD,qDAAqD,CACtD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,IAAI,eAAgC,CAAC;QAErC,IAAA,sBAAU,EAAC,GAAG,EAAE;YACd,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,KAAK,GAAqB;gBAC9B,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;gBAChC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,QAAQ;gBACxC,MAAM,EAAE,iBAAiB;gBACzB,OAAO,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE;gBAChC,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,GAAG;aACZ,CAAC;YAEF,eAAe,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAE3C,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,OAAO,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,WAAW,CAAC;YAE3B,MAAM,MAAM,GAAqB;gBAC/B,OAAO;gBACP,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,OAAO;gBACvC,MAAM;gBACN,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,GAAG;aACZ,CAAC;YAEF,MAAM,MAAM,GAAqB;gBAC/B,OAAO;gBACP,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,OAAO;gBACvC,MAAM;gBACN,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;gBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC5C,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE5C,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,MAAM,GAAG,WAAW,CAAC;YAC3B,MAAM,MAAM,GAAqB;gBAC/B,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;gBAChC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,QAAQ;gBACxC,MAAM;gBACN,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,GAAG;aACZ,CAAC;YAEF,MAAM,MAAM,GAAqB;gBAC/B,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;gBAChC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,OAAO;gBACvC,MAAM,EAAE,YAAY;gBACpB,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC5C,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE5C,MAAM,aAAa,GAAG,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC/D,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,MAAM,GAAqB;gBAC/B,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;gBAChC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,QAAQ;gBACxC,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,GAAG;aACZ,CAAC;YAEF,MAAM,MAAM,GAAqB;gBAC/B,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;gBAChC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,QAAQ;gBACxC,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC5C,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE5C,MAAM,eAAe,GAAG,eAAe,CAAC,gBAAgB,CAAC,8CAAoB,CAAC,QAAQ,CAAC,CAAC;YACxF,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,MAAM,GAAqB;gBAC/B,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;gBAChC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,QAAQ;gBACxC,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,GAAG;aACZ,CAAC;YAEF,MAAM,MAAM,GAAqB;gBAC/B,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;gBAChC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,OAAO;gBACvC,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,GAAG;aACZ,CAAC;YAEF,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC5C,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE5C,MAAM,aAAa,GAAG,eAAe,CAAC,yBAAyB,EAAE,CAAC;YAClE,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,IAAI,eAAgC,CAAC;QAErC,IAAA,sBAAU,EAAC,GAAG,EAAE;YACd,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,WAAW,GAAgB;gBAC/B,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,yCAAyC;gBACtD,MAAM,EAAE;oBACN;wBACE,EAAE,EAAE,SAAS;wBACb,IAAI,EAAE,gBAAgB;wBACtB,WAAW,EAAE,oBAAoB;wBACjC,QAAQ,EAAE,CAAC,8BAAc,CAAC,MAAM,EAAE,CAAC;wBACnC,KAAK,EAAE,CAAC;wBACR,UAAU,EAAE,uCAAe,CAAC,IAAI;wBAChC,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,MAAM,EAAE,gBAAgB;qBACzB;iBACF;gBACD,UAAU,EAAE,uCAAe,CAAC,IAAI;gBAChC,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,QAAQ,EAAE;oBACR,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC;oBACzC,OAAO,EAAE,IAAI,IAAI,EAAE;oBACnB,QAAQ,EAAE,OAAO;iBAClB;aACF,CAAC;YAEF,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAE5C,MAAM,CAAC,eAAe,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,kBAAkB,GAAgB;gBACtC,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,uCAAuC;gBACpD,MAAM,EAAE;oBACN;wBACE,EAAE,EAAE,SAAS;wBACb,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,aAAa;wBAC1B,QAAQ,EAAE,CAAC,8BAAc,CAAC,MAAM,EAAE,CAAC;wBACnC,KAAK,EAAE,CAAC,EAAE,gBAAgB;wBAC1B,UAAU,EAAE,uCAAe,CAAC,IAAI;wBAChC,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB;oBACD;wBACE,EAAE,EAAE,SAAS;wBACb,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,cAAc;wBAC3B,QAAQ,EAAE,CAAC,8BAAc,CAAC,MAAM,EAAE,CAAC;wBACnC,KAAK,EAAE,CAAC,EAAE,gBAAgB;wBAC1B,UAAU,EAAE,uCAAe,CAAC,IAAI;wBAChC,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB;iBACF;gBACD,UAAU,EAAE,uCAAe,CAAC,IAAI;gBAChC,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,QAAQ,EAAE;oBACR,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC;oBACzC,OAAO,EAAE,IAAI,IAAI,EAAE;oBACnB,QAAQ,EAAE,OAAO;iBAClB;aACF,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CACtE,oDAAoD,CACrD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,IAAA,cAAE,EAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,cAAc,GAAG,EAAE,GAAG,UAAU,EAAE,iBAAiB,EAAE,2CAAiB,CAAC,SAAS,EAAE,CAAC;YACzF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAE/D,MAAM,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,WAAW,GAAG,EAAE,GAAG,UAAU,EAAE,iBAAiB,EAAE,2CAAiB,CAAC,MAAM,EAAE,CAAC;YACnF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAE5D,MAAM,CAAC,eAAe,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,eAAe,GAAG,EAAE,GAAG,UAAU,EAAE,iBAAiB,EAAE,2CAAiB,CAAC,WAAW,EAAE,CAAC;YAC5F,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAEhE,MAAM,CAAC,eAAe,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,gBAAgB,GAAG,EAAE,GAAG,UAAU,EAAE,uBAAuB,EAAE,EAAE,EAAE,CAAC;YACxE,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YAEjE,MAAM,CAAC,eAAe,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,UAAU,GAAG,EAAE,GAAG,UAAU,EAAE,gBAAgB,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC;YAC/E,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAE3D,MAAM,CAAC,eAAe,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,UAAU,GAAG;gBACjB,GAAG,UAAU;gBACb,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;gBAC9C,uBAAuB,EAAE,EAAE;gBAC3B,gBAAgB,EAAE,EAAE;gBACpB,oBAAoB,EAAE,KAAK;aAC5B,CAAC;YACF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAE3D,MAAM,CAAC,eAAe,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,mBAAmB,GAAG;gBAC1B,GAAG,UAAU;gBACb,iBAAiB,EAAE;oBACjB,OAAO,EAAE,IAAI;oBACb,YAAY,EAAE,EAAE;oBAChB,WAAW,EAAE,EAAE;oBACf,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,EAAE;oBACV,oBAAoB,EAAE,IAAI;oBAC1B,eAAe,EAAE,EAAE;oBACnB,SAAS,EAAE,CAAC;oBACZ,YAAY,EAAE,CAAC;oBACf,kBAAkB,EAAE,EAAE;iBACvB;aACF,CAAC;YACF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;YAEpE,MAAM,CAAC,eAAe,CAAC,2BAA2B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,wCAAwC,EAAE,GAAG,EAAE;QACtD,IAAI,eAAgC,CAAC;QAErC,IAAA,sBAAU,EAAC,GAAG,EAAE;YACd,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,OAAO,GAAG,oBAAoB,CAAC;YACrC,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAE/C,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,cAAc,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAC/C,eAAe,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YAEhD,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,YAAY,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAC7C,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,cAAc,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAC/C,eAAe,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YAChD,eAAe,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YAEhD,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,IAAI,eAAgC,CAAC;QAErC,IAAA,sBAAU,EAAC,GAAG,EAAE;YACd,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,iBAAiB,EAAE,2CAAiB,CAAC,OAAO,EAAE,CAAC;YACrF,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,eAAe,CAAC,gBAAgB,EAAE,CAAC;YAEnC,gCAAgC;YAChC,MAAM,SAAS,GAAG,eAAe,CAAC,oBAAqB,CAAC;YACxD,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,kBAAkB;YAExE,MAAM,MAAM,GAAsB;gBAChC,OAAO,EAAE,IAAI;gBACb,YAAY,EAAE,EAAE;gBAChB,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE,EAAE;gBACV,oBAAoB,EAAE,IAAI;gBAC1B,eAAe,EAAE,EAAE;gBACnB,SAAS,EAAE,CAAC;gBACZ,YAAY,EAAE,CAAC;gBACf,kBAAkB,EAAE,EAAE;aACvB,CAAC;YAEF,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE5C,MAAM,QAAQ,GAAG,eAAe,CAAC,sBAAsB,EAAE,CAAC;YAC1D,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,IAAI,eAAgC,CAAC;QAErC,IAAA,sBAAU,EAAC,GAAG,EAAE;YACd,MAAM,IAAI,GAAoB;gBAC5B,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,yBAAyB;gBACtC,IAAI,EAAE,6CAAmB,CAAC,QAAQ;gBAClC,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,KAAK;gBACf,YAAY,EAAE,OAAO;gBACrB,aAAa,EAAE,EAAE;aAClB,CAAC;YAEF,MAAM,cAAc,GAAG;gBACrB,GAAG,UAAU;gBACb,YAAY,EAAE,CAAC,IAAI,CAAC;aACrB,CAAC;YAEF,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,IAAA,cAAE,EAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,uBAAuB,EAAE,GAAG,EAAE,CAAC;YAErE,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACxD,qDAAqD,CACtD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YAEvD,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACxD,sCAAsC,CACvC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,mBAAmB,EAAE,CAAC,CAAC,EAAE,CAAC;YAEhE,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACxD,yCAAyC,CAC1C,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,cAAc,GAAuB,EAAE,CAAC;YAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC7B,cAAc,CAAC,IAAI,CAAC;oBAClB,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;oBAChC,UAAU,EAAE,EAAE;oBACd,SAAS,EAAE,8CAAoB,CAAC,OAAO;oBACvC,MAAM,EAAE,QAAQ,CAAC,EAAE;oBACnB,OAAO,EAAE,EAAE;oBACX,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,MAAM,EAAE,GAAG;iBACZ,CAAC,CAAC;YACL,CAAC;YAED,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,kBAAkB,EAAE,cAAc,EAAE,CAAC;YAE3E,MAAM,CAAC,GAAG,EAAE,CAAC,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACxD,+DAA+D,CAChE,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\correlated-event.entity.spec.ts"], "sourcesContent": ["import { \r\n  CorrelatedEvent, \r\n  CorrelatedEventProps, \r\n  CorrelationRule, \r\n  CorrelationMatch, \r\n  CorrelationResult, \r\n  AttackChain, \r\n  CorrelationRuleType, \r\n  CorrelationMatchType \r\n} from '../correlated-event.entity';\r\nimport { CorrelationStatus } from '../../enums/correlation-status.enum';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { EventMetadata } from '../../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventStatus } from '../../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\nimport { EventTimestamp } from '../../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../value-objects/event-metadata/event-source.value-object';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\n\r\ndescribe('CorrelatedEvent', () => {\r\n  let validProps: CorrelatedEventProps;\r\n  let mockEventMetadata: EventMetadata;\r\n\r\n  beforeEach(() => {\r\n    // Create mock event metadata\r\n    mockEventMetadata = EventMetadata.create({\r\n      timestamp: EventTimestamp.now(),\r\n      source: EventSource.create({\r\n        type: 'SIEM',\r\n        identifier: 'test-siem-001',\r\n        name: 'Test SIEM System'\r\n      }),\r\n      processingInfo: {\r\n        receivedAt: new Date(),\r\n        processedAt: new Date(),\r\n        processingDuration: 100,\r\n        version: '1.0.0'\r\n      }\r\n    });\r\n\r\n    validProps = {\r\n      enrichedEventId: UniqueEntityId.create(),\r\n      metadata: mockEventMetadata,\r\n      type: EventType.SECURITY_ALERT,\r\n      severity: EventSeverity.HIGH,\r\n      status: EventStatus.ACTIVE,\r\n      processingStatus: EventProcessingStatus.CORRELATED,\r\n      correlationStatus: CorrelationStatus.COMPLETED,\r\n      enrichedData: {\r\n        original_event: 'test_event',\r\n        enriched_at: new Date().toISOString(),\r\n        threat_indicators: ['malicious_ip', 'suspicious_domain']\r\n      },\r\n      correlatedData: {\r\n        correlation_id: 'corr_123',\r\n        related_events: 5,\r\n        attack_chain_detected: true,\r\n        confidence_score: 85\r\n      },\r\n      title: 'Test Correlated Security Event',\r\n      description: 'A test correlated security event for unit testing',\r\n      tags: ['test', 'correlation', 'security'],\r\n      riskScore: 75,\r\n      confidenceLevel: ConfidenceLevel.HIGH,\r\n      attributes: {\r\n        test_attribute: 'test_value'\r\n      },\r\n      correlationId: 'corr_test_123',\r\n      childEventIds: [],\r\n      appliedRules: [],\r\n      correlationMatches: [],\r\n      relatedEventIds: [],\r\n      correlationPatterns: ['temporal_sequence', 'ip_clustering'],\r\n      correlationQualityScore: 85,\r\n      requiresManualReview: false\r\n    };\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create a valid CorrelatedEvent with required properties', () => {\r\n      const correlatedEvent = CorrelatedEvent.create(validProps);\r\n\r\n      expect(correlatedEvent).toBeInstanceOf(CorrelatedEvent);\r\n      expect(correlatedEvent.id).toBeDefined();\r\n      expect(correlatedEvent.enrichedEventId).toEqual(validProps.enrichedEventId);\r\n      expect(correlatedEvent.metadata).toEqual(validProps.metadata);\r\n      expect(correlatedEvent.type).toBe(validProps.type);\r\n      expect(correlatedEvent.severity).toBe(validProps.severity);\r\n      expect(correlatedEvent.status).toBe(validProps.status);\r\n      expect(correlatedEvent.processingStatus).toBe(validProps.processingStatus);\r\n      expect(correlatedEvent.correlationStatus).toBe(validProps.correlationStatus);\r\n      expect(correlatedEvent.title).toBe(validProps.title);\r\n      expect(correlatedEvent.confidenceLevel).toBe(validProps.confidenceLevel);\r\n      expect(correlatedEvent.correlationId).toBe(validProps.correlationId);\r\n    });\r\n\r\n    it('should generate domain event when created', () => {\r\n      const correlatedEvent = CorrelatedEvent.create(validProps);\r\n      const domainEvents = correlatedEvent.getUncommittedEvents();\r\n\r\n      expect(domainEvents).toHaveLength(1);\r\n      expect(domainEvents[0].eventName).toBe('CorrelatedEventCreatedDomainEvent');\r\n    });\r\n\r\n    it('should throw error when enrichedEventId is missing', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).enrichedEventId;\r\n\r\n      expect(() => CorrelatedEvent.create(invalidProps)).toThrow(\r\n        'CorrelatedEvent must reference an enriched event'\r\n      );\r\n    });\r\n\r\n    it('should throw error when metadata is missing', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).metadata;\r\n\r\n      expect(() => CorrelatedEvent.create(invalidProps)).toThrow(\r\n        'CorrelatedEvent must have metadata'\r\n      );\r\n    });\r\n\r\n    it('should throw error when title is empty', () => {\r\n      const invalidProps = { ...validProps, title: '' };\r\n\r\n      expect(() => CorrelatedEvent.create(invalidProps)).toThrow(\r\n        'CorrelatedEvent must have a non-empty title'\r\n      );\r\n    });\r\n\r\n    it('should throw error when correlationId is empty', () => {\r\n      const invalidProps = { ...validProps, correlationId: '' };\r\n\r\n      expect(() => CorrelatedEvent.create(invalidProps)).toThrow(\r\n        'CorrelatedEvent must have a correlation ID'\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('correlation status management', () => {\r\n    let correlatedEvent: CorrelatedEvent;\r\n\r\n    beforeEach(() => {\r\n      const pendingProps = { ...validProps, correlationStatus: CorrelationStatus.PENDING };\r\n      correlatedEvent = CorrelatedEvent.create(pendingProps);\r\n    });\r\n\r\n    it('should start correlation process', () => {\r\n      correlatedEvent.startCorrelation();\r\n\r\n      expect(correlatedEvent.correlationStatus).toBe(CorrelationStatus.IN_PROGRESS);\r\n      expect(correlatedEvent.correlationAttempts).toBe(1);\r\n      expect(correlatedEvent.correlationStartedAt).toBeDefined();\r\n    });\r\n\r\n    it('should complete correlation process', () => {\r\n      correlatedEvent.startCorrelation();\r\n      \r\n      const result: CorrelationResult = {\r\n        success: true,\r\n        appliedRules: ['rule1', 'rule2'],\r\n        failedRules: [],\r\n        warnings: [],\r\n        errors: [],\r\n        processingDurationMs: 1500,\r\n        confidenceScore: 85,\r\n        rulesUsed: 2,\r\n        matchesFound: 5,\r\n        patternsIdentified: ['temporal_sequence']\r\n      };\r\n\r\n      correlatedEvent.completeCorrelation(result);\r\n\r\n      expect(correlatedEvent.correlationStatus).toBe(CorrelationStatus.COMPLETED);\r\n      expect(correlatedEvent.correlationCompletedAt).toBeDefined();\r\n      expect(correlatedEvent.correlationResult).toEqual(result);\r\n    });\r\n\r\n    it('should fail correlation process', () => {\r\n      correlatedEvent.startCorrelation();\r\n      const error = 'Correlation engine timeout';\r\n\r\n      correlatedEvent.failCorrelation(error);\r\n\r\n      expect(correlatedEvent.correlationStatus).toBe(CorrelationStatus.FAILED);\r\n      expect(correlatedEvent.lastCorrelationError).toBe(error);\r\n    });\r\n\r\n    it('should skip correlation process', () => {\r\n      const reason = 'Event does not meet correlation criteria';\r\n      correlatedEvent.skipCorrelation(reason);\r\n\r\n      expect(correlatedEvent.correlationStatus).toBe(CorrelationStatus.SKIPPED);\r\n      expect(correlatedEvent.reviewNotes).toBe(reason);\r\n    });\r\n\r\n    it('should reset correlation for retry', () => {\r\n      correlatedEvent.startCorrelation();\r\n      correlatedEvent.failCorrelation('Test error');\r\n\r\n      correlatedEvent.resetCorrelation();\r\n\r\n      expect(correlatedEvent.correlationStatus).toBe(CorrelationStatus.PENDING);\r\n      expect(correlatedEvent.correlationStartedAt).toBeUndefined();\r\n      expect(correlatedEvent.lastCorrelationError).toBeUndefined();\r\n    });\r\n\r\n    it('should not allow reset when max attempts exceeded', () => {\r\n      // Simulate max attempts exceeded\r\n      for (let i = 0; i < 3; i++) {\r\n        correlatedEvent.startCorrelation();\r\n        correlatedEvent.failCorrelation('Test error');\r\n        if (i < 2) {\r\n          correlatedEvent.resetCorrelation();\r\n        }\r\n      }\r\n\r\n      expect(() => correlatedEvent.resetCorrelation()).toThrow(\r\n        'Cannot reset correlation: maximum attempts exceeded'\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('correlation matches management', () => {\r\n    let correlatedEvent: CorrelatedEvent;\r\n\r\n    beforeEach(() => {\r\n      correlatedEvent = CorrelatedEvent.create(validProps);\r\n    });\r\n\r\n    it('should add correlation match', () => {\r\n      const match: CorrelationMatch = {\r\n        eventId: UniqueEntityId.create(),\r\n        confidence: 80,\r\n        matchType: CorrelationMatchType.TEMPORAL,\r\n        ruleId: 'temporal_rule_1',\r\n        details: { timeWindow: 3600000 },\r\n        timestamp: new Date(),\r\n        weight: 0.8\r\n      };\r\n\r\n      correlatedEvent.addCorrelationMatch(match);\r\n\r\n      expect(correlatedEvent.correlationMatches).toHaveLength(1);\r\n      expect(correlatedEvent.correlationMatches[0]).toEqual(match);\r\n    });\r\n\r\n    it('should update existing match with higher confidence', () => {\r\n      const eventId = UniqueEntityId.create();\r\n      const ruleId = 'test_rule';\r\n\r\n      const match1: CorrelationMatch = {\r\n        eventId,\r\n        confidence: 70,\r\n        matchType: CorrelationMatchType.PATTERN,\r\n        ruleId,\r\n        details: {},\r\n        timestamp: new Date(),\r\n        weight: 0.7\r\n      };\r\n\r\n      const match2: CorrelationMatch = {\r\n        eventId,\r\n        confidence: 85,\r\n        matchType: CorrelationMatchType.PATTERN,\r\n        ruleId,\r\n        details: { updated: true },\r\n        timestamp: new Date(),\r\n        weight: 0.85\r\n      };\r\n\r\n      correlatedEvent.addCorrelationMatch(match1);\r\n      correlatedEvent.addCorrelationMatch(match2);\r\n\r\n      expect(correlatedEvent.correlationMatches).toHaveLength(1);\r\n      expect(correlatedEvent.correlationMatches[0].confidence).toBe(85);\r\n    });\r\n\r\n    it('should get matches by rule', () => {\r\n      const ruleId = 'test_rule';\r\n      const match1: CorrelationMatch = {\r\n        eventId: UniqueEntityId.create(),\r\n        confidence: 80,\r\n        matchType: CorrelationMatchType.TEMPORAL,\r\n        ruleId,\r\n        details: {},\r\n        timestamp: new Date(),\r\n        weight: 0.8\r\n      };\r\n\r\n      const match2: CorrelationMatch = {\r\n        eventId: UniqueEntityId.create(),\r\n        confidence: 75,\r\n        matchType: CorrelationMatchType.SPATIAL,\r\n        ruleId: 'other_rule',\r\n        details: {},\r\n        timestamp: new Date(),\r\n        weight: 0.75\r\n      };\r\n\r\n      correlatedEvent.addCorrelationMatch(match1);\r\n      correlatedEvent.addCorrelationMatch(match2);\r\n\r\n      const matchesByRule = correlatedEvent.getMatchesByRule(ruleId);\r\n      expect(matchesByRule).toHaveLength(1);\r\n      expect(matchesByRule[0].ruleId).toBe(ruleId);\r\n    });\r\n\r\n    it('should get matches by type', () => {\r\n      const match1: CorrelationMatch = {\r\n        eventId: UniqueEntityId.create(),\r\n        confidence: 80,\r\n        matchType: CorrelationMatchType.TEMPORAL,\r\n        ruleId: 'rule1',\r\n        details: {},\r\n        timestamp: new Date(),\r\n        weight: 0.8\r\n      };\r\n\r\n      const match2: CorrelationMatch = {\r\n        eventId: UniqueEntityId.create(),\r\n        confidence: 75,\r\n        matchType: CorrelationMatchType.TEMPORAL,\r\n        ruleId: 'rule2',\r\n        details: {},\r\n        timestamp: new Date(),\r\n        weight: 0.75\r\n      };\r\n\r\n      correlatedEvent.addCorrelationMatch(match1);\r\n      correlatedEvent.addCorrelationMatch(match2);\r\n\r\n      const temporalMatches = correlatedEvent.getMatchesByType(CorrelationMatchType.TEMPORAL);\r\n      expect(temporalMatches).toHaveLength(2);\r\n    });\r\n\r\n    it('should calculate average match confidence', () => {\r\n      const match1: CorrelationMatch = {\r\n        eventId: UniqueEntityId.create(),\r\n        confidence: 80,\r\n        matchType: CorrelationMatchType.TEMPORAL,\r\n        ruleId: 'rule1',\r\n        details: {},\r\n        timestamp: new Date(),\r\n        weight: 0.8\r\n      };\r\n\r\n      const match2: CorrelationMatch = {\r\n        eventId: UniqueEntityId.create(),\r\n        confidence: 90,\r\n        matchType: CorrelationMatchType.SPATIAL,\r\n        ruleId: 'rule2',\r\n        details: {},\r\n        timestamp: new Date(),\r\n        weight: 0.9\r\n      };\r\n\r\n      correlatedEvent.addCorrelationMatch(match1);\r\n      correlatedEvent.addCorrelationMatch(match2);\r\n\r\n      const avgConfidence = correlatedEvent.getAverageMatchConfidence();\r\n      expect(avgConfidence).toBe(85);\r\n    });\r\n  });\r\n\r\n  describe('attack chain management', () => {\r\n    let correlatedEvent: CorrelatedEvent;\r\n\r\n    beforeEach(() => {\r\n      correlatedEvent = CorrelatedEvent.create(validProps);\r\n    });\r\n\r\n    it('should set attack chain', () => {\r\n      const attackChain: AttackChain = {\r\n        id: 'attack_chain_1',\r\n        name: 'Multi-stage Attack',\r\n        description: 'Coordinated attack with multiple stages',\r\n        stages: [\r\n          {\r\n            id: 'stage_1',\r\n            name: 'Initial Access',\r\n            description: 'Initial compromise',\r\n            eventIds: [UniqueEntityId.create()],\r\n            order: 1,\r\n            confidence: ConfidenceLevel.HIGH,\r\n            timestamp: new Date(),\r\n            tactic: 'Initial Access'\r\n          }\r\n        ],\r\n        confidence: ConfidenceLevel.HIGH,\r\n        severity: EventSeverity.HIGH,\r\n        timeline: {\r\n          startTime: new Date(Date.now() - 3600000),\r\n          endTime: new Date(),\r\n          duration: 3600000\r\n        }\r\n      };\r\n\r\n      correlatedEvent.setAttackChain(attackChain);\r\n\r\n      expect(correlatedEvent.hasAttackChain()).toBe(true);\r\n      expect(correlatedEvent.attackChain).toEqual(attackChain);\r\n    });\r\n\r\n    it('should validate attack chain chronological order', () => {\r\n      const invalidAttackChain: AttackChain = {\r\n        id: 'attack_chain_1',\r\n        name: 'Invalid Attack Chain',\r\n        description: 'Attack chain with invalid stage order',\r\n        stages: [\r\n          {\r\n            id: 'stage_1',\r\n            name: 'Stage 1',\r\n            description: 'First stage',\r\n            eventIds: [UniqueEntityId.create()],\r\n            order: 2, // Invalid order\r\n            confidence: ConfidenceLevel.HIGH,\r\n            timestamp: new Date(),\r\n          },\r\n          {\r\n            id: 'stage_2',\r\n            name: 'Stage 2',\r\n            description: 'Second stage',\r\n            eventIds: [UniqueEntityId.create()],\r\n            order: 1, // Invalid order\r\n            confidence: ConfidenceLevel.HIGH,\r\n            timestamp: new Date(),\r\n          }\r\n        ],\r\n        confidence: ConfidenceLevel.HIGH,\r\n        severity: EventSeverity.HIGH,\r\n        timeline: {\r\n          startTime: new Date(Date.now() - 3600000),\r\n          endTime: new Date(),\r\n          duration: 3600000\r\n        }\r\n      };\r\n\r\n      expect(() => correlatedEvent.setAttackChain(invalidAttackChain)).toThrow(\r\n        'Attack chain stages must be in chronological order'\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('business rule validation', () => {\r\n    it('should check if correlation is completed', () => {\r\n      const completedProps = { ...validProps, correlationStatus: CorrelationStatus.COMPLETED };\r\n      const correlatedEvent = CorrelatedEvent.create(completedProps);\r\n\r\n      expect(correlatedEvent.isCorrelationCompleted()).toBe(true);\r\n    });\r\n\r\n    it('should check if correlation failed', () => {\r\n      const failedProps = { ...validProps, correlationStatus: CorrelationStatus.FAILED };\r\n      const correlatedEvent = CorrelatedEvent.create(failedProps);\r\n\r\n      expect(correlatedEvent.isCorrelationFailed()).toBe(true);\r\n    });\r\n\r\n    it('should check if correlation is in progress', () => {\r\n      const inProgressProps = { ...validProps, correlationStatus: CorrelationStatus.IN_PROGRESS };\r\n      const correlatedEvent = CorrelatedEvent.create(inProgressProps);\r\n\r\n      expect(correlatedEvent.isCorrelationInProgress()).toBe(true);\r\n    });\r\n\r\n    it('should check if has high correlation quality', () => {\r\n      const highQualityProps = { ...validProps, correlationQualityScore: 85 };\r\n      const correlatedEvent = CorrelatedEvent.create(highQualityProps);\r\n\r\n      expect(correlatedEvent.hasHighCorrelationQuality()).toBe(true);\r\n    });\r\n\r\n    it('should check if has validation errors', () => {\r\n      const errorProps = { ...validProps, validationErrors: ['Error 1', 'Error 2'] };\r\n      const correlatedEvent = CorrelatedEvent.create(errorProps);\r\n\r\n      expect(correlatedEvent.hasValidationErrors()).toBe(true);\r\n    });\r\n\r\n    it('should check if ready for next stage', () => {\r\n      const readyProps = {\r\n        ...validProps,\r\n        correlationStatus: CorrelationStatus.COMPLETED,\r\n        correlationQualityScore: 85,\r\n        validationErrors: [],\r\n        requiresManualReview: false\r\n      };\r\n      const correlatedEvent = CorrelatedEvent.create(readyProps);\r\n\r\n      expect(correlatedEvent.isReadyForNextStage()).toBe(true);\r\n    });\r\n\r\n    it('should check if has high confidence correlation', () => {\r\n      const highConfidenceProps = {\r\n        ...validProps,\r\n        correlationResult: {\r\n          success: true,\r\n          appliedRules: [],\r\n          failedRules: [],\r\n          warnings: [],\r\n          errors: [],\r\n          processingDurationMs: 1000,\r\n          confidenceScore: 90,\r\n          rulesUsed: 2,\r\n          matchesFound: 5,\r\n          patternsIdentified: []\r\n        }\r\n      };\r\n      const correlatedEvent = CorrelatedEvent.create(highConfidenceProps);\r\n\r\n      expect(correlatedEvent.isHighConfidenceCorrelation()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('correlation patterns and relationships', () => {\r\n    let correlatedEvent: CorrelatedEvent;\r\n\r\n    beforeEach(() => {\r\n      correlatedEvent = CorrelatedEvent.create(validProps);\r\n    });\r\n\r\n    it('should add correlation pattern', () => {\r\n      const pattern = 'behavioral_anomaly';\r\n      correlatedEvent.addCorrelationPattern(pattern);\r\n\r\n      expect(correlatedEvent.correlationPatterns).toContain(pattern);\r\n    });\r\n\r\n    it('should add related event', () => {\r\n      const relatedEventId = UniqueEntityId.create();\r\n      correlatedEvent.addRelatedEvent(relatedEventId);\r\n\r\n      expect(correlatedEvent.relatedEventIds).toContain(relatedEventId);\r\n    });\r\n\r\n    it('should add child event', () => {\r\n      const childEventId = UniqueEntityId.create();\r\n      correlatedEvent.addChildEvent(childEventId);\r\n\r\n      expect(correlatedEvent.childEventIds).toContain(childEventId);\r\n    });\r\n\r\n    it('should not add duplicate related events', () => {\r\n      const relatedEventId = UniqueEntityId.create();\r\n      correlatedEvent.addRelatedEvent(relatedEventId);\r\n      correlatedEvent.addRelatedEvent(relatedEventId);\r\n\r\n      expect(correlatedEvent.relatedEventIds.filter(id => id.equals(relatedEventId))).toHaveLength(1);\r\n    });\r\n  });\r\n\r\n  describe('correlation duration calculation', () => {\r\n    let correlatedEvent: CorrelatedEvent;\r\n\r\n    beforeEach(() => {\r\n      const pendingProps = { ...validProps, correlationStatus: CorrelationStatus.PENDING };\r\n      correlatedEvent = CorrelatedEvent.create(pendingProps);\r\n    });\r\n\r\n    it('should return null when correlation not started', () => {\r\n      expect(correlatedEvent.getCorrelationDuration()).toBeNull();\r\n    });\r\n\r\n    it('should calculate duration when correlation completed', () => {\r\n      correlatedEvent.startCorrelation();\r\n      \r\n      // Simulate some processing time\r\n      const startTime = correlatedEvent.correlationStartedAt!;\r\n      const endTime = new Date(startTime.getTime() + 5000); // 5 seconds later\r\n      \r\n      const result: CorrelationResult = {\r\n        success: true,\r\n        appliedRules: [],\r\n        failedRules: [],\r\n        warnings: [],\r\n        errors: [],\r\n        processingDurationMs: 5000,\r\n        confidenceScore: 85,\r\n        rulesUsed: 1,\r\n        matchesFound: 3,\r\n        patternsIdentified: []\r\n      };\r\n\r\n      correlatedEvent.completeCorrelation(result);\r\n\r\n      const duration = correlatedEvent.getCorrelationDuration();\r\n      expect(duration).toBeGreaterThan(0);\r\n    });\r\n  });\r\n\r\n  describe('applied rules management', () => {\r\n    let correlatedEvent: CorrelatedEvent;\r\n\r\n    beforeEach(() => {\r\n      const rule: CorrelationRule = {\r\n        id: 'test_rule_1',\r\n        name: 'Test Rule',\r\n        description: 'A test correlation rule',\r\n        type: CorrelationRuleType.TEMPORAL,\r\n        priority: 100,\r\n        required: false,\r\n        timeWindowMs: 3600000,\r\n        minConfidence: 70\r\n      };\r\n\r\n      const propsWithRules = {\r\n        ...validProps,\r\n        appliedRules: [rule]\r\n      };\r\n\r\n      correlatedEvent = CorrelatedEvent.create(propsWithRules);\r\n    });\r\n\r\n    it('should check if specific rule was applied', () => {\r\n      expect(correlatedEvent.hasAppliedRule('test_rule_1')).toBe(true);\r\n      expect(correlatedEvent.hasAppliedRule('non_existent_rule')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('data integrity validation', () => {\r\n    it('should validate correlation quality score range', () => {\r\n      const invalidProps = { ...validProps, correlationQualityScore: 150 };\r\n\r\n      expect(() => CorrelatedEvent.create(invalidProps)).toThrow(\r\n        'Correlation quality score must be between 0 and 100'\r\n      );\r\n    });\r\n\r\n    it('should validate risk score range', () => {\r\n      const invalidProps = { ...validProps, riskScore: -10 };\r\n\r\n      expect(() => CorrelatedEvent.create(invalidProps)).toThrow(\r\n        'Risk score must be between 0 and 100'\r\n      );\r\n    });\r\n\r\n    it('should validate correlation attempts cannot be negative', () => {\r\n      const invalidProps = { ...validProps, correlationAttempts: -1 };\r\n\r\n      expect(() => CorrelatedEvent.create(invalidProps)).toThrow(\r\n        'Correlation attempts cannot be negative'\r\n      );\r\n    });\r\n\r\n    it('should validate maximum correlation matches limit', () => {\r\n      const tooManyMatches: CorrelationMatch[] = [];\r\n      for (let i = 0; i < 101; i++) {\r\n        tooManyMatches.push({\r\n          eventId: UniqueEntityId.create(),\r\n          confidence: 80,\r\n          matchType: CorrelationMatchType.PATTERN,\r\n          ruleId: `rule_${i}`,\r\n          details: {},\r\n          timestamp: new Date(),\r\n          weight: 0.8\r\n        });\r\n      }\r\n\r\n      const invalidProps = { ...validProps, correlationMatches: tooManyMatches };\r\n\r\n      expect(() => CorrelatedEvent.create(invalidProps)).toThrow(\r\n        'CorrelatedEvent cannot have more than 100 correlation matches'\r\n      );\r\n    });\r\n  });\r\n});"], "version": 3}