0643ebcd6392fa817fb09aecd1ff4ae7
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NormalizedEvent = exports.NormalizationStatus = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const event_severity_enum_1 = require("../enums/event-severity.enum");
const normalized_event_created_domain_event_1 = require("../events/normalized-event-created.domain-event");
const normalized_event_status_changed_domain_event_1 = require("../events/normalized-event-status-changed.domain-event");
const normalized_event_validation_failed_domain_event_1 = require("../events/normalized-event-validation-failed.domain-event");
/**
 * Normalization Status Enum
 */
var NormalizationStatus;
(function (NormalizationStatus) {
    NormalizationStatus["PENDING"] = "PENDING";
    NormalizationStatus["IN_PROGRESS"] = "IN_PROGRESS";
    NormalizationStatus["COMPLETED"] = "COMPLETED";
    NormalizationStatus["FAILED"] = "FAILED";
    NormalizationStatus["SKIPPED"] = "SKIPPED";
})(NormalizationStatus || (exports.NormalizationStatus = NormalizationStatus = {}));
/**
 * NormalizedEvent Entity
 *
 * Represents a security event that has been processed through the normalization pipeline.
 * Normalized events have standardized data formats and validated content.
 *
 * Key responsibilities:
 * - Maintain normalized event state and lifecycle
 * - Enforce normalization business rules and data quality standards
 * - Track normalization process and applied rules
 * - Generate domain events for normalization state changes
 * - Support data quality assessment and validation
 * - Manage manual review workflow for complex events
 *
 * Business Rules:
 * - Normalized events must reference a valid original event
 * - Normalized data must conform to the specified schema version
 * - Data quality score must be calculated based on validation results
 * - High-risk events may require manual review before processing
 * - Normalization attempts are tracked and limited
 * - Failed normalization must preserve original data integrity
 */
class NormalizedEvent extends shared_kernel_1.BaseAggregateRoot {
    constructor(props, id) {
        super(props, id);
        this.validateInvariants();
    }
    /**
     * Create a new NormalizedEvent
     */
    static create(props, id) {
        const normalizedEvent = new NormalizedEvent(props, id);
        // Add domain event for normalized event creation
        normalizedEvent.addDomainEvent(new normalized_event_created_domain_event_1.NormalizedEventCreatedDomainEvent(normalizedEvent.id, {
            originalEventId: props.originalEventId,
            eventType: props.type,
            severity: props.severity,
            normalizationStatus: props.normalizationStatus,
            schemaVersion: props.schemaVersion,
            dataQualityScore: props.dataQualityScore,
            appliedRulesCount: props.appliedRules.length,
            requiresManualReview: props.requiresManualReview || false,
        }));
        return normalizedEvent;
    }
    validateInvariants() {
        super.validateInvariants();
        if (!this.props.originalEventId) {
            throw new Error('NormalizedEvent must reference an original event');
        }
        if (!this.props.metadata) {
            throw new Error('NormalizedEvent must have metadata');
        }
        if (!this.props.type) {
            throw new Error('NormalizedEvent must have a type');
        }
        if (!this.props.severity) {
            throw new Error('NormalizedEvent must have a severity');
        }
        if (!this.props.status) {
            throw new Error('NormalizedEvent must have a status');
        }
        if (!this.props.processingStatus) {
            throw new Error('NormalizedEvent must have a processing status');
        }
        if (!this.props.normalizationStatus) {
            throw new Error('NormalizedEvent must have a normalization status');
        }
        if (!this.props.originalData) {
            throw new Error('NormalizedEvent must have original data');
        }
        if (!this.props.normalizedData) {
            throw new Error('NormalizedEvent must have normalized data');
        }
        if (!this.props.title || this.props.title.trim().length === 0) {
            throw new Error('NormalizedEvent must have a non-empty title');
        }
        if (!this.props.schemaVersion || this.props.schemaVersion.trim().length === 0) {
            throw new Error('NormalizedEvent must have a schema version');
        }
        if (!Array.isArray(this.props.appliedRules)) {
            throw new Error('NormalizedEvent must have applied rules array');
        }
        if (this.props.dataQualityScore !== undefined &&
            (this.props.dataQualityScore < 0 || this.props.dataQualityScore > 100)) {
            throw new Error('Data quality score must be between 0 and 100');
        }
        if (this.props.riskScore !== undefined &&
            (this.props.riskScore < 0 || this.props.riskScore > 100)) {
            throw new Error('Risk score must be between 0 and 100');
        }
        if (this.props.confidenceLevel !== undefined &&
            (this.props.confidenceLevel < 0 || this.props.confidenceLevel > 100)) {
            throw new Error('Confidence level must be between 0 and 100');
        }
        if (this.props.normalizationAttempts !== undefined && this.props.normalizationAttempts < 0) {
            throw new Error('Normalization attempts cannot be negative');
        }
        if (this.props.validationErrors &&
            this.props.validationErrors.length > NormalizedEvent.MAX_VALIDATION_ERRORS) {
            throw new Error(`Cannot have more than ${NormalizedEvent.MAX_VALIDATION_ERRORS} validation errors`);
        }
        // Validate normalization status consistency
        this.validateNormalizationStatusConsistency();
    }
    validateNormalizationStatusConsistency() {
        // If normalization is completed, it should have completion timestamp
        if (this.props.normalizationStatus === NormalizationStatus.COMPLETED) {
            if (!this.props.normalizationCompletedAt) {
                throw new Error('Completed normalization must have completion timestamp');
            }
            if (!this.props.normalizationResult) {
                throw new Error('Completed normalization must have result');
            }
        }
        // If normalization failed, it should have error information
        if (this.props.normalizationStatus === NormalizationStatus.FAILED) {
            if (!this.props.lastNormalizationError &&
                (!this.props.normalizationResult || this.props.normalizationResult.errors.length === 0)) {
                throw new Error('Failed normalization must have error information');
            }
        }
        // If normalization is in progress, it should have started timestamp
        if (this.props.normalizationStatus === NormalizationStatus.IN_PROGRESS) {
            if (!this.props.normalizationStartedAt) {
                throw new Error('In-progress normalization must have start timestamp');
            }
        }
        // Manual review consistency
        if (this.props.requiresManualReview && this.props.reviewedAt) {
            if (!this.props.reviewedBy) {
                throw new Error('Reviewed events must have reviewer information');
            }
        }
    }
    // Getters
    get originalEventId() {
        return this.props.originalEventId;
    }
    get metadata() {
        return this.props.metadata;
    }
    get type() {
        return this.props.type;
    }
    get severity() {
        return this.props.severity;
    }
    get status() {
        return this.props.status;
    }
    get processingStatus() {
        return this.props.processingStatus;
    }
    get normalizationStatus() {
        return this.props.normalizationStatus;
    }
    get originalData() {
        return { ...this.props.originalData };
    }
    get normalizedData() {
        return { ...this.props.normalizedData };
    }
    get title() {
        return this.props.title;
    }
    get description() {
        return this.props.description;
    }
    get tags() {
        return this.props.tags ? [...this.props.tags] : [];
    }
    get riskScore() {
        return this.props.riskScore;
    }
    get confidenceLevel() {
        return this.props.confidenceLevel;
    }
    get attributes() {
        return this.props.attributes ? { ...this.props.attributes } : {};
    }
    get correlationId() {
        return this.props.correlationId;
    }
    get parentEventId() {
        return this.props.parentEventId;
    }
    get appliedRules() {
        return [...this.props.appliedRules];
    }
    get normalizationResult() {
        return this.props.normalizationResult ? { ...this.props.normalizationResult } : undefined;
    }
    get schemaVersion() {
        return this.props.schemaVersion;
    }
    get normalizationStartedAt() {
        return this.props.normalizationStartedAt;
    }
    get normalizationCompletedAt() {
        return this.props.normalizationCompletedAt;
    }
    get normalizationAttempts() {
        return this.props.normalizationAttempts || 0;
    }
    get lastNormalizationError() {
        return this.props.lastNormalizationError;
    }
    get dataQualityScore() {
        return this.props.dataQualityScore;
    }
    get validationErrors() {
        return this.props.validationErrors ? [...this.props.validationErrors] : [];
    }
    get requiresManualReview() {
        return this.props.requiresManualReview || false;
    }
    get reviewNotes() {
        return this.props.reviewNotes;
    }
    get reviewedBy() {
        return this.props.reviewedBy;
    }
    get reviewedAt() {
        return this.props.reviewedAt;
    }
    // Business methods
    /**
     * Start normalization process
     */
    startNormalization() {
        if (this.props.normalizationStatus !== NormalizationStatus.PENDING) {
            throw new Error('Can only start normalization for pending events');
        }
        this.props.normalizationStatus = NormalizationStatus.IN_PROGRESS;
        this.props.normalizationStartedAt = new Date();
        this.props.normalizationAttempts = (this.props.normalizationAttempts || 0) + 1;
        this.validateInvariants();
    }
    /**
     * Complete normalization process
     */
    completeNormalization(result) {
        if (this.props.normalizationStatus !== NormalizationStatus.IN_PROGRESS) {
            throw new Error('Can only complete normalization for in-progress events');
        }
        this.props.normalizationStatus = NormalizationStatus.COMPLETED;
        this.props.normalizationCompletedAt = new Date();
        this.props.normalizationResult = result;
        this.props.lastNormalizationError = undefined;
        // Calculate data quality score based on result
        this.calculateDataQualityScore(result);
        // Determine if manual review is required
        this.determineManualReviewRequirement();
        this.addDomainEvent(new normalized_event_status_changed_domain_event_1.NormalizedEventStatusChangedDomainEvent(this.id, {
            oldStatus: NormalizationStatus.IN_PROGRESS,
            newStatus: NormalizationStatus.COMPLETED,
            result,
            dataQualityScore: this.props.dataQualityScore,
            requiresManualReview: this.props.requiresManualReview || false,
        }));
        this.validateInvariants();
    }
    /**
     * Fail normalization process
     */
    failNormalization(error, result) {
        if (this.props.normalizationStatus !== NormalizationStatus.IN_PROGRESS) {
            throw new Error('Can only fail normalization for in-progress events');
        }
        this.props.normalizationStatus = NormalizationStatus.FAILED;
        this.props.lastNormalizationError = error;
        if (result) {
            this.props.normalizationResult = {
                success: false,
                appliedRules: result.appliedRules || [],
                failedRules: result.failedRules || [],
                warnings: result.warnings || [],
                errors: result.errors || [error],
                processingDurationMs: result.processingDurationMs || 0,
                confidenceScore: result.confidenceScore || 0,
            };
        }
        this.addDomainEvent(new normalized_event_validation_failed_domain_event_1.NormalizedEventValidationFailedDomainEvent(this.id, {
            originalEventId: this.props.originalEventId,
            error,
            attempt: this.normalizationAttempts,
            maxAttemptsExceeded: this.hasExceededMaxNormalizationAttempts(),
        }));
        this.validateInvariants();
    }
    /**
     * Skip normalization process
     */
    skipNormalization(reason) {
        if (![NormalizationStatus.PENDING, NormalizationStatus.FAILED].includes(this.props.normalizationStatus)) {
            throw new Error('Can only skip normalization for pending or failed events');
        }
        this.props.normalizationStatus = NormalizationStatus.SKIPPED;
        this.props.lastNormalizationError = undefined;
        this.props.reviewNotes = reason;
        this.validateInvariants();
    }
    /**
     * Reset normalization for retry
     */
    resetNormalization() {
        if (this.hasExceededMaxNormalizationAttempts()) {
            throw new Error('Cannot reset normalization: maximum attempts exceeded');
        }
        this.props.normalizationStatus = NormalizationStatus.PENDING;
        this.props.normalizationStartedAt = undefined;
        this.props.normalizationCompletedAt = undefined;
        this.props.lastNormalizationError = undefined;
        this.props.normalizationResult = undefined;
        this.validateInvariants();
    }
    /**
     * Update normalized data
     */
    updateNormalizedData(normalizedData) {
        if (this.props.normalizationStatus === NormalizationStatus.COMPLETED) {
            throw new Error('Cannot update normalized data for completed normalization');
        }
        this.props.normalizedData = { ...normalizedData };
    }
    /**
     * Add applied normalization rule
     */
    addAppliedRule(rule) {
        const existingRule = this.props.appliedRules.find(r => r.id === rule.id);
        if (!existingRule) {
            this.props.appliedRules.push(rule);
        }
    }
    /**
     * Update data quality score
     */
    updateDataQualityScore(score) {
        if (score < 0 || score > 100) {
            throw new Error('Data quality score must be between 0 and 100');
        }
        this.props.dataQualityScore = score;
        this.determineManualReviewRequirement();
    }
    /**
     * Add validation errors
     */
    addValidationErrors(errors) {
        const currentErrors = this.props.validationErrors || [];
        const newErrors = [...currentErrors, ...errors];
        if (newErrors.length > NormalizedEvent.MAX_VALIDATION_ERRORS) {
            throw new Error(`Cannot have more than ${NormalizedEvent.MAX_VALIDATION_ERRORS} validation errors`);
        }
        this.props.validationErrors = newErrors;
    }
    /**
     * Clear validation errors
     */
    clearValidationErrors() {
        this.props.validationErrors = [];
    }
    /**
     * Mark for manual review
     */
    markForManualReview(reason) {
        this.props.requiresManualReview = true;
        this.props.reviewNotes = reason;
    }
    /**
     * Complete manual review
     */
    completeManualReview(reviewedBy, notes) {
        if (!this.props.requiresManualReview) {
            throw new Error('Event is not marked for manual review');
        }
        this.props.reviewedBy = reviewedBy;
        this.props.reviewedAt = new Date();
        if (notes) {
            this.props.reviewNotes = notes;
        }
    }
    /**
     * Update risk score
     */
    updateRiskScore(riskScore) {
        if (riskScore < 0 || riskScore > 100) {
            throw new Error('Risk score must be between 0 and 100');
        }
        this.props.riskScore = riskScore;
        this.determineManualReviewRequirement();
    }
    // Query methods
    /**
     * Check if normalization is completed
     */
    isNormalizationCompleted() {
        return this.props.normalizationStatus === NormalizationStatus.COMPLETED;
    }
    /**
     * Check if normalization failed
     */
    isNormalizationFailed() {
        return this.props.normalizationStatus === NormalizationStatus.FAILED;
    }
    /**
     * Check if normalization is in progress
     */
    isNormalizationInProgress() {
        return this.props.normalizationStatus === NormalizationStatus.IN_PROGRESS;
    }
    /**
     * Check if normalization was skipped
     */
    isNormalizationSkipped() {
        return this.props.normalizationStatus === NormalizationStatus.SKIPPED;
    }
    /**
     * Check if event has high data quality
     */
    hasHighDataQuality() {
        return (this.props.dataQualityScore || 0) >= NormalizedEvent.MIN_DATA_QUALITY_SCORE;
    }
    /**
     * Check if event has validation errors
     */
    hasValidationErrors() {
        return (this.props.validationErrors?.length || 0) > 0;
    }
    /**
     * Check if event has exceeded max normalization attempts
     */
    hasExceededMaxNormalizationAttempts() {
        return this.normalizationAttempts >= NormalizedEvent.MAX_NORMALIZATION_ATTEMPTS;
    }
    /**
     * Check if event is ready for next processing stage
     */
    isReadyForNextStage() {
        return this.isNormalizationCompleted() &&
            this.hasHighDataQuality() &&
            !this.hasValidationErrors() &&
            (!this.requiresManualReview || this.reviewedAt !== undefined);
    }
    /**
     * Check if event is high risk
     */
    isHighRisk() {
        return (this.props.riskScore || 0) >= NormalizedEvent.HIGH_RISK_REVIEW_THRESHOLD;
    }
    /**
     * Get normalization duration
     */
    getNormalizationDuration() {
        if (!this.props.normalizationStartedAt) {
            return null;
        }
        const endTime = this.props.normalizationCompletedAt || new Date();
        return endTime.getTime() - this.props.normalizationStartedAt.getTime();
    }
    /**
     * Get applied rule names
     */
    getAppliedRuleNames() {
        return this.props.appliedRules.map(rule => rule.name);
    }
    /**
     * Check if specific rule was applied
     */
    hasAppliedRule(ruleId) {
        return this.props.appliedRules.some(rule => rule.id === ruleId);
    }
    // Private helper methods
    calculateDataQualityScore(result) {
        let score = 100;
        // Reduce score for failed rules
        const failedRulesPenalty = result.failedRules.length * 10;
        score -= failedRulesPenalty;
        // Reduce score for warnings
        const warningsPenalty = result.warnings.length * 5;
        score -= warningsPenalty;
        // Reduce score for errors
        const errorsPenalty = result.errors.length * 15;
        score -= errorsPenalty;
        // Reduce score for low confidence
        if (result.confidenceScore < 70) {
            score -= (70 - result.confidenceScore);
        }
        // Ensure score is within valid range
        this.props.dataQualityScore = Math.max(0, Math.min(100, score));
    }
    determineManualReviewRequirement() {
        // High-risk events require manual review
        if (this.isHighRisk()) {
            this.props.requiresManualReview = true;
            return;
        }
        // Low data quality requires manual review
        if (!this.hasHighDataQuality()) {
            this.props.requiresManualReview = true;
            return;
        }
        // Events with validation errors require manual review
        if (this.hasValidationErrors()) {
            this.props.requiresManualReview = true;
            return;
        }
        // Critical events require manual review
        if (this.severity === event_severity_enum_1.EventSeverity.CRITICAL) {
            this.props.requiresManualReview = true;
            return;
        }
        // Otherwise, no manual review required
        this.props.requiresManualReview = false;
    }
    /**
     * Get event summary for display
     */
    getSummary() {
        return {
            id: this.id.toString(),
            originalEventId: this.props.originalEventId.toString(),
            title: this.props.title,
            type: this.props.type,
            severity: this.props.severity,
            status: this.props.status,
            normalizationStatus: this.props.normalizationStatus,
            dataQualityScore: this.props.dataQualityScore,
            riskScore: this.props.riskScore,
            schemaVersion: this.props.schemaVersion,
            appliedRulesCount: this.props.appliedRules.length,
            hasValidationErrors: this.hasValidationErrors(),
            requiresManualReview: this.requiresManualReview,
            isReadyForNextStage: this.isReadyForNextStage(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            originalEventId: this.props.originalEventId.toString(),
            metadata: this.props.metadata.toJSON(),
            type: this.props.type,
            severity: this.props.severity,
            status: this.props.status,
            processingStatus: this.props.processingStatus,
            normalizationStatus: this.props.normalizationStatus,
            originalData: this.props.originalData,
            normalizedData: this.props.normalizedData,
            title: this.props.title,
            description: this.props.description,
            tags: this.props.tags,
            riskScore: this.props.riskScore,
            confidenceLevel: this.props.confidenceLevel,
            attributes: this.props.attributes,
            correlationId: this.props.correlationId,
            parentEventId: this.props.parentEventId?.toString(),
            appliedRules: this.props.appliedRules,
            normalizationResult: this.props.normalizationResult,
            schemaVersion: this.props.schemaVersion,
            normalizationStartedAt: this.props.normalizationStartedAt?.toISOString(),
            normalizationCompletedAt: this.props.normalizationCompletedAt?.toISOString(),
            normalizationAttempts: this.props.normalizationAttempts,
            lastNormalizationError: this.props.lastNormalizationError,
            dataQualityScore: this.props.dataQualityScore,
            validationErrors: this.props.validationErrors,
            requiresManualReview: this.props.requiresManualReview,
            reviewNotes: this.props.reviewNotes,
            reviewedBy: this.props.reviewedBy,
            reviewedAt: this.props.reviewedAt?.toISOString(),
            summary: this.getSummary(),
        };
    }
}
exports.NormalizedEvent = NormalizedEvent;
NormalizedEvent.MAX_NORMALIZATION_ATTEMPTS = 3;
NormalizedEvent.MIN_DATA_QUALITY_SCORE = 60;
NormalizedEvent.HIGH_RISK_REVIEW_THRESHOLD = 80;
NormalizedEvent.MAX_VALIDATION_ERRORS = 10;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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