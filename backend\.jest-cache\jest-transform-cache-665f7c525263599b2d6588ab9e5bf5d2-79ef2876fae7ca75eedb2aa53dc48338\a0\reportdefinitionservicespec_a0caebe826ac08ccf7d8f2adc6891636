1ac90869eec14f23afd85f2c7e6c5d82
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const cache_service_1 = require("../../../../infrastructure/cache/cache.service");
const metrics_service_1 = require("../../../../infrastructure/metrics/metrics.service");
const report_definition_service_1 = require("../../../services/report-definition.service");
const report_definition_entity_1 = require("../../../entities/report-definition.entity");
/**
 * Unit Tests for Report Definition Service
 *
 * Tests all service methods including:
 * - CRUD operations with validation
 * - Business logic and error handling
 * - Cache integration and invalidation
 * - Metrics collection and monitoring
 * - Data transformation and validation
 */
describe('ReportDefinitionService', () => {
    let service;
    let repository;
    let cacheService;
    let metricsService;
    const mockReportDefinition = {
        id: 'test-report-id',
        name: 'Test Report',
        description: 'Test report description',
        category: 'compliance',
        type: 'tabular',
        dataSource: 'compliance',
        query: 'SELECT * FROM compliance_data',
        parameters: [
            {
                name: 'startDate',
                type: 'date',
                required: true,
                defaultValue: null,
            },
        ],
        outputFormats: ['pdf', 'excel'],
        schedule: {
            enabled: true,
            cronExpression: '0 9 * * 1',
            timezone: 'UTC',
        },
        isActive: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        createdBy: 'test-user',
        updatedBy: 'test-user',
    };
    beforeEach(async () => {
        const mockRepository = {
            find: jest.fn(),
            findOne: jest.fn(),
            findOneBy: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            create: jest.fn(),
            count: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
                where: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getMany: jest.fn(),
                getCount: jest.fn(),
            })),
        };
        const mockCacheService = {
            get: jest.fn(),
            set: jest.fn(),
            del: jest.fn(),
            delPattern: jest.fn(),
        };
        const mockMetricsService = {
            recordCounter: jest.fn(),
            recordHistogram: jest.fn(),
            recordGauge: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                report_definition_service_1.ReportDefinitionService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(report_definition_entity_1.ReportDefinition),
                    useValue: mockRepository,
                },
                {
                    provide: cache_service_1.CacheService,
                    useValue: mockCacheService,
                },
                {
                    provide: metrics_service_1.MetricsService,
                    useValue: mockMetricsService,
                },
            ],
        }).compile();
        service = module.get(report_definition_service_1.ReportDefinitionService);
        repository = module.get((0, typeorm_1.getRepositoryToken)(report_definition_entity_1.ReportDefinition));
        cacheService = module.get(cache_service_1.CacheService);
        metricsService = module.get(metrics_service_1.MetricsService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('createReportDefinition', () => {
        const createDto = {
            name: 'New Report',
            description: 'New report description',
            category: 'compliance',
            type: 'tabular',
            dataSource: 'compliance',
            query: 'SELECT * FROM compliance_data',
            parameters: [],
            outputFormats: ['pdf'],
        };
        it('should create a new report definition successfully', async () => {
            const savedReport = { ...mockReportDefinition, ...createDto };
            repository.create.mockReturnValue(savedReport);
            repository.save.mockResolvedValue(savedReport);
            const result = await service.createReportDefinition(createDto, 'test-user');
            expect(repository.create).toHaveBeenCalledWith({
                ...createDto,
                createdBy: 'test-user',
                updatedBy: 'test-user',
                isActive: true,
            });
            expect(repository.save).toHaveBeenCalledWith(savedReport);
            expect(cacheService.delPattern).toHaveBeenCalledWith('report_definitions_*');
            expect(metricsService.recordCounter).toHaveBeenCalledWith('report_definition_created', {
                category: createDto.category,
                type: createDto.type,
            });
            expect(result).toEqual(savedReport);
        });
        it('should throw error for duplicate report name', async () => {
            repository.findOneBy.mockResolvedValue(mockReportDefinition);
            await expect(service.createReportDefinition(createDto, 'test-user'))
                .rejects.toThrow('Report definition with this name already exists');
            expect(repository.save).not.toHaveBeenCalled();
        });
        it('should validate required parameters', async () => {
            const invalidDto = { ...createDto, name: '' };
            await expect(service.createReportDefinition(invalidDto, 'test-user'))
                .rejects.toThrow();
        });
    });
    describe('getReportDefinitionById', () => {
        it('should return cached report definition if available', async () => {
            cacheService.get.mockResolvedValue(mockReportDefinition);
            const result = await service.getReportDefinitionById('test-report-id');
            expect(cacheService.get).toHaveBeenCalledWith('report_definition_test-report-id');
            expect(repository.findOneBy).not.toHaveBeenCalled();
            expect(result).toEqual(mockReportDefinition);
        });
        it('should fetch from database and cache if not in cache', async () => {
            cacheService.get.mockResolvedValue(null);
            repository.findOneBy.mockResolvedValue(mockReportDefinition);
            const result = await service.getReportDefinitionById('test-report-id');
            expect(cacheService.get).toHaveBeenCalledWith('report_definition_test-report-id');
            expect(repository.findOneBy).toHaveBeenCalledWith({ id: 'test-report-id', isActive: true });
            expect(cacheService.set).toHaveBeenCalledWith('report_definition_test-report-id', mockReportDefinition, 3600);
            expect(result).toEqual(mockReportDefinition);
        });
        it('should return null for non-existent report definition', async () => {
            cacheService.get.mockResolvedValue(null);
            repository.findOneBy.mockResolvedValue(null);
            const result = await service.getReportDefinitionById('non-existent-id');
            expect(result).toBeNull();
            expect(cacheService.set).not.toHaveBeenCalled();
        });
    });
    describe('updateReportDefinition', () => {
        const updateDto = {
            name: 'Updated Report',
            description: 'Updated description',
        };
        it('should update report definition successfully', async () => {
            const updatedReport = { ...mockReportDefinition, ...updateDto };
            repository.findOneBy.mockResolvedValue(mockReportDefinition);
            repository.save.mockResolvedValue(updatedReport);
            const result = await service.updateReportDefinition('test-report-id', updateDto, 'test-user');
            expect(repository.findOneBy).toHaveBeenCalledWith({ id: 'test-report-id', isActive: true });
            expect(repository.save).toHaveBeenCalledWith({
                ...mockReportDefinition,
                ...updateDto,
                updatedBy: 'test-user',
                updatedAt: expect.any(Date),
            });
            expect(cacheService.del).toHaveBeenCalledWith('report_definition_test-report-id');
            expect(cacheService.delPattern).toHaveBeenCalledWith('report_definitions_*');
            expect(result).toEqual(updatedReport);
        });
        it('should throw error for non-existent report definition', async () => {
            repository.findOneBy.mockResolvedValue(null);
            await expect(service.updateReportDefinition('non-existent-id', updateDto, 'test-user'))
                .rejects.toThrow('Report definition not found');
            expect(repository.save).not.toHaveBeenCalled();
        });
    });
    describe('deleteReportDefinition', () => {
        it('should soft delete report definition successfully', async () => {
            repository.findOneBy.mockResolvedValue(mockReportDefinition);
            repository.save.mockResolvedValue({ ...mockReportDefinition, isActive: false });
            await service.deleteReportDefinition('test-report-id', 'test-user');
            expect(repository.findOneBy).toHaveBeenCalledWith({ id: 'test-report-id', isActive: true });
            expect(repository.save).toHaveBeenCalledWith({
                ...mockReportDefinition,
                isActive: false,
                updatedBy: 'test-user',
                updatedAt: expect.any(Date),
            });
            expect(cacheService.del).toHaveBeenCalledWith('report_definition_test-report-id');
            expect(cacheService.delPattern).toHaveBeenCalledWith('report_definitions_*');
        });
        it('should throw error for non-existent report definition', async () => {
            repository.findOneBy.mockResolvedValue(null);
            await expect(service.deleteReportDefinition('non-existent-id', 'test-user'))
                .rejects.toThrow('Report definition not found');
            expect(repository.save).not.toHaveBeenCalled();
        });
    });
    describe('getReportDefinitions', () => {
        const mockQueryBuilder = {
            where: jest.fn().mockReturnThis(),
            andWhere: jest.fn().mockReturnThis(),
            orderBy: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getMany: jest.fn(),
            getCount: jest.fn(),
        };
        beforeEach(() => {
            repository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
        });
        it('should return paginated report definitions with filters', async () => {
            const filters = {
                category: 'compliance',
                type: 'tabular',
                search: 'test',
                page: 1,
                limit: 10,
            };
            mockQueryBuilder.getMany.mockResolvedValue([mockReportDefinition]);
            mockQueryBuilder.getCount.mockResolvedValue(1);
            const result = await service.getReportDefinitions(filters);
            expect(repository.createQueryBuilder).toHaveBeenCalledWith('report');
            expect(mockQueryBuilder.where).toHaveBeenCalledWith('report.isActive = :isActive', { isActive: true });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('report.category = :category', { category: 'compliance' });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('report.type = :type', { type: 'tabular' });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('(report.name ILIKE :search OR report.description ILIKE :search)', { search: '%test%' });
            expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('report.createdAt', 'DESC');
            expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0);
            expect(mockQueryBuilder.take).toHaveBeenCalledWith(10);
            expect(result).toEqual({
                reportDefinitions: [mockReportDefinition],
                total: 1,
                page: 1,
                limit: 10,
                totalPages: 1,
            });
        });
        it('should return all report definitions without filters', async () => {
            mockQueryBuilder.getMany.mockResolvedValue([mockReportDefinition]);
            mockQueryBuilder.getCount.mockResolvedValue(1);
            const result = await service.getReportDefinitions({});
            expect(mockQueryBuilder.where).toHaveBeenCalledWith('report.isActive = :isActive', { isActive: true });
            expect(mockQueryBuilder.andWhere).not.toHaveBeenCalled();
            expect(result.reportDefinitions).toEqual([mockReportDefinition]);
        });
    });
    describe('validateReportDefinition', () => {
        it('should validate report definition successfully', async () => {
            const validDefinition = {
                name: 'Valid Report',
                dataSource: 'compliance',
                query: 'SELECT * FROM compliance_data',
                parameters: [],
            };
            const result = await service.validateReportDefinition(validDefinition);
            expect(result.isValid).toBe(true);
            expect(result.errors).toEqual([]);
        });
        it('should return validation errors for invalid definition', async () => {
            const invalidDefinition = {
                name: '',
                dataSource: 'invalid_source',
                query: '',
                parameters: [],
            };
            const result = await service.validateReportDefinition(invalidDefinition);
            expect(result.isValid).toBe(false);
            expect(result.errors).toContain('Report name is required');
            expect(result.errors).toContain('Invalid data source');
            expect(result.errors).toContain('Query is required');
        });
    });
    describe('error handling', () => {
        it('should handle database errors gracefully', async () => {
            repository.findOneBy.mockRejectedValue(new Error('Database connection failed'));
            await expect(service.getReportDefinitionById('test-id'))
                .rejects.toThrow('Database connection failed');
            expect(metricsService.recordCounter).toHaveBeenCalledWith('report_definition_error', {
                operation: 'get',
                error: 'Database connection failed',
            });
        });
        it('should handle cache errors gracefully', async () => {
            cacheService.get.mockRejectedValue(new Error('Cache unavailable'));
            repository.findOneBy.mockResolvedValue(mockReportDefinition);
            const result = await service.getReportDefinitionById('test-id');
            expect(result).toEqual(mockReportDefinition);
            expect(repository.findOneBy).toHaveBeenCalled();
        });
    });
    describe('metrics collection', () => {
        it('should record metrics for successful operations', async () => {
            repository.findOneBy.mockResolvedValue(mockReportDefinition);
            await service.getReportDefinitionById('test-id');
            expect(metricsService.recordCounter).toHaveBeenCalledWith('report_definition_accessed', {
                id: 'test-id',
                cached: false,
            });
        });
        it('should record metrics for cache hits', async () => {
            cacheService.get.mockResolvedValue(mockReportDefinition);
            await service.getReportDefinitionById('test-id');
            expect(metricsService.recordCounter).toHaveBeenCalledWith('report_definition_accessed', {
                id: 'test-id',
                cached: true,
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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