34c90a9f5e1ef7a9a3e69907c94a9053
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const action_type_enum_1 = require("../action-type.enum");
describe('ActionType', () => {
    describe('enum values', () => {
        it('should have all expected action types', () => {
            expect(action_type_enum_1.ActionType.VULNERABILITY_SCAN).toBe('vulnerability_scan');
            expect(action_type_enum_1.ActionType.ISOLATE_SYSTEM).toBe('isolate_system');
            expect(action_type_enum_1.ActionType.BLOCK_IP).toBe('block_ip');
            expect(action_type_enum_1.ActionType.SEND_EMAIL).toBe('send_email');
            expect(action_type_enum_1.ActionType.MANUAL_INVESTIGATION).toBe('manual_investigation');
            expect(action_type_enum_1.ActionType.NO_ACTION).toBe('no_action');
            expect(action_type_enum_1.ActionType.UNKNOWN).toBe('unknown');
        });
    });
    describe('ActionTypeUtils', () => {
        describe('getAllActionTypes', () => {
            it('should return all action types', () => {
                const actionTypes = action_type_enum_1.ActionTypeUtils.getAllActionTypes();
                expect(actionTypes.length).toBeGreaterThan(50); // We have many action types
                expect(actionTypes).toContain(action_type_enum_1.ActionType.VULNERABILITY_SCAN);
                expect(actionTypes).toContain(action_type_enum_1.ActionType.ISOLATE_SYSTEM);
                expect(actionTypes).toContain(action_type_enum_1.ActionType.BLOCK_IP);
            });
        });
        describe('getContainmentActionTypes', () => {
            it('should return containment action types', () => {
                const containmentActions = action_type_enum_1.ActionTypeUtils.getContainmentActionTypes();
                expect(containmentActions).toContain(action_type_enum_1.ActionType.ISOLATE_SYSTEM);
                expect(containmentActions).toContain(action_type_enum_1.ActionType.BLOCK_IP);
                expect(containmentActions).toContain(action_type_enum_1.ActionType.QUARANTINE_FILE);
                expect(containmentActions).toContain(action_type_enum_1.ActionType.DISABLE_ACCOUNT);
            });
        });
        describe('getAutomatedActionTypes', () => {
            it('should return automated action types', () => {
                const automatedActions = action_type_enum_1.ActionTypeUtils.getAutomatedActionTypes();
                expect(automatedActions).toContain(action_type_enum_1.ActionType.VULNERABILITY_SCAN);
                expect(automatedActions).toContain(action_type_enum_1.ActionType.BLOCK_IP);
                expect(automatedActions).toContain(action_type_enum_1.ActionType.SEND_EMAIL);
                expect(automatedActions).not.toContain(action_type_enum_1.ActionType.MANUAL_INVESTIGATION);
            });
        });
        describe('getManualActionTypes', () => {
            it('should return manual action types', () => {
                const manualActions = action_type_enum_1.ActionTypeUtils.getManualActionTypes();
                expect(manualActions).toContain(action_type_enum_1.ActionType.MANUAL_INVESTIGATION);
                expect(manualActions).toContain(action_type_enum_1.ActionType.FORENSIC_ANALYSIS);
                expect(manualActions).toContain(action_type_enum_1.ActionType.ISOLATE_SYSTEM);
                expect(manualActions).not.toContain(action_type_enum_1.ActionType.SEND_EMAIL);
            });
        });
        describe('getHighRiskActionTypes', () => {
            it('should return high-risk action types', () => {
                const highRiskActions = action_type_enum_1.ActionTypeUtils.getHighRiskActionTypes();
                expect(highRiskActions).toContain(action_type_enum_1.ActionType.ISOLATE_SYSTEM);
                expect(highRiskActions).toContain(action_type_enum_1.ActionType.SHUTDOWN_SYSTEM);
                expect(highRiskActions).toContain(action_type_enum_1.ActionType.DELETE_FILE);
                expect(highRiskActions).not.toContain(action_type_enum_1.ActionType.SEND_EMAIL);
            });
        });
        describe('isAutomated', () => {
            it('should correctly identify automated actions', () => {
                expect(action_type_enum_1.ActionTypeUtils.isAutomated(action_type_enum_1.ActionType.VULNERABILITY_SCAN)).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isAutomated(action_type_enum_1.ActionType.BLOCK_IP)).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isAutomated(action_type_enum_1.ActionType.SEND_EMAIL)).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isAutomated(action_type_enum_1.ActionType.MANUAL_INVESTIGATION)).toBe(false);
                expect(action_type_enum_1.ActionTypeUtils.isAutomated(action_type_enum_1.ActionType.FORENSIC_ANALYSIS)).toBe(false);
            });
        });
        describe('isManual', () => {
            it('should correctly identify manual actions', () => {
                expect(action_type_enum_1.ActionTypeUtils.isManual(action_type_enum_1.ActionType.MANUAL_INVESTIGATION)).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isManual(action_type_enum_1.ActionType.FORENSIC_ANALYSIS)).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isManual(action_type_enum_1.ActionType.ISOLATE_SYSTEM)).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isManual(action_type_enum_1.ActionType.SEND_EMAIL)).toBe(false);
                expect(action_type_enum_1.ActionTypeUtils.isManual(action_type_enum_1.ActionType.BLOCK_IP)).toBe(false);
            });
        });
        describe('isHighRisk', () => {
            it('should correctly identify high-risk actions', () => {
                expect(action_type_enum_1.ActionTypeUtils.isHighRisk(action_type_enum_1.ActionType.ISOLATE_SYSTEM)).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isHighRisk(action_type_enum_1.ActionType.SHUTDOWN_SYSTEM)).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isHighRisk(action_type_enum_1.ActionType.DELETE_FILE)).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isHighRisk(action_type_enum_1.ActionType.SEND_EMAIL)).toBe(false);
                expect(action_type_enum_1.ActionTypeUtils.isHighRisk(action_type_enum_1.ActionType.BLOCK_IP)).toBe(false);
            });
        });
        describe('isReversible', () => {
            it('should correctly identify reversible actions', () => {
                expect(action_type_enum_1.ActionTypeUtils.isReversible(action_type_enum_1.ActionType.BLOCK_IP)).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isReversible(action_type_enum_1.ActionType.DISABLE_ACCOUNT)).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isReversible(action_type_enum_1.ActionType.QUARANTINE_FILE)).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isReversible(action_type_enum_1.ActionType.DELETE_FILE)).toBe(false);
                expect(action_type_enum_1.ActionTypeUtils.isReversible(action_type_enum_1.ActionType.REMOVE_MALWARE)).toBe(false);
            });
        });
        describe('getCategory', () => {
            it('should return correct categories', () => {
                expect(action_type_enum_1.ActionTypeUtils.getCategory(action_type_enum_1.ActionType.VULNERABILITY_SCAN)).toBe('Detection');
                expect(action_type_enum_1.ActionTypeUtils.getCategory(action_type_enum_1.ActionType.ISOLATE_SYSTEM)).toBe('Containment');
                expect(action_type_enum_1.ActionTypeUtils.getCategory(action_type_enum_1.ActionType.REMOVE_MALWARE)).toBe('Eradication');
                expect(action_type_enum_1.ActionTypeUtils.getCategory(action_type_enum_1.ActionType.RESTORE_BACKUP)).toBe('Recovery');
                expect(action_type_enum_1.ActionTypeUtils.getCategory(action_type_enum_1.ActionType.SEND_EMAIL)).toBe('Notification');
            });
        });
        describe('getExecutionTimeEstimate', () => {
            it('should return reasonable time estimates', () => {
                expect(action_type_enum_1.ActionTypeUtils.getExecutionTimeEstimate(action_type_enum_1.ActionType.BLOCK_IP)).toBe(1);
                expect(action_type_enum_1.ActionTypeUtils.getExecutionTimeEstimate(action_type_enum_1.ActionType.SEND_EMAIL)).toBe(1);
                expect(action_type_enum_1.ActionTypeUtils.getExecutionTimeEstimate(action_type_enum_1.ActionType.VULNERABILITY_SCAN)).toBe(15);
                expect(action_type_enum_1.ActionTypeUtils.getExecutionTimeEstimate(action_type_enum_1.ActionType.FORENSIC_ANALYSIS)).toBe(120);
                expect(action_type_enum_1.ActionTypeUtils.getExecutionTimeEstimate(action_type_enum_1.ActionType.REBUILD_SYSTEM)).toBe(240);
            });
        });
        describe('getRequiredPermissions', () => {
            it('should return appropriate permissions', () => {
                const blockIpPermissions = action_type_enum_1.ActionTypeUtils.getRequiredPermissions(action_type_enum_1.ActionType.BLOCK_IP);
                expect(blockIpPermissions).toContain('network.firewall.write');
                const isolatePermissions = action_type_enum_1.ActionTypeUtils.getRequiredPermissions(action_type_enum_1.ActionType.ISOLATE_SYSTEM);
                expect(isolatePermissions).toContain('system.network.isolate');
                const defaultPermissions = action_type_enum_1.ActionTypeUtils.getRequiredPermissions(action_type_enum_1.ActionType.SEND_EMAIL);
                expect(defaultPermissions).toContain('security.action.execute');
            });
        });
        describe('getApprovalRequirements', () => {
            it('should return correct approval requirements', () => {
                const highRiskApproval = action_type_enum_1.ActionTypeUtils.getApprovalRequirements(action_type_enum_1.ActionType.ISOLATE_SYSTEM);
                expect(highRiskApproval.required).toBe(true);
                expect(highRiskApproval.level).toBe('manager');
                const destructiveApproval = action_type_enum_1.ActionTypeUtils.getApprovalRequirements(action_type_enum_1.ActionType.DELETE_FILE);
                expect(destructiveApproval.required).toBe(true);
                expect(destructiveApproval.level).toBe('director');
                const automatedApproval = action_type_enum_1.ActionTypeUtils.getApprovalRequirements(action_type_enum_1.ActionType.SEND_EMAIL);
                expect(automatedApproval.required).toBe(false);
                expect(automatedApproval.level).toBe('none');
            });
        });
        describe('getDescription', () => {
            it('should return meaningful descriptions', () => {
                const description = action_type_enum_1.ActionTypeUtils.getDescription(action_type_enum_1.ActionType.BLOCK_IP);
                expect(description).toContain('Block malicious IP address');
                const unknownDescription = action_type_enum_1.ActionTypeUtils.getDescription(action_type_enum_1.ActionType.CUSTOM_ACTION);
                expect(typeof unknownDescription).toBe('string');
                expect(description.length).toBeGreaterThan(0);
            });
        });
        describe('getSuccessCriteria', () => {
            it('should return success criteria for actions', () => {
                const blockIpCriteria = action_type_enum_1.ActionTypeUtils.getSuccessCriteria(action_type_enum_1.ActionType.BLOCK_IP);
                expect(blockIpCriteria).toContain('IP address blocked in firewall');
                expect(blockIpCriteria).toContain('Traffic from IP stopped');
                const defaultCriteria = action_type_enum_1.ActionTypeUtils.getSuccessCriteria(action_type_enum_1.ActionType.CUSTOM_ACTION);
                expect(defaultCriteria).toContain('Action completed successfully');
            });
        });
        describe('isValid', () => {
            it('should validate action type strings', () => {
                expect(action_type_enum_1.ActionTypeUtils.isValid('block_ip')).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isValid('isolate_system')).toBe(true);
                expect(action_type_enum_1.ActionTypeUtils.isValid('invalid_action')).toBe(false);
                expect(action_type_enum_1.ActionTypeUtils.isValid('')).toBe(false);
            });
        });
        describe('fromString', () => {
            it('should convert string to action type', () => {
                expect(action_type_enum_1.ActionTypeUtils.fromString('block_ip')).toBe(action_type_enum_1.ActionType.BLOCK_IP);
                expect(action_type_enum_1.ActionTypeUtils.fromString('isolate_system')).toBe(action_type_enum_1.ActionType.ISOLATE_SYSTEM);
                expect(action_type_enum_1.ActionTypeUtils.fromString('SEND_EMAIL')).toBe(action_type_enum_1.ActionType.SEND_EMAIL);
                expect(action_type_enum_1.ActionTypeUtils.fromString('invalid_action')).toBeNull();
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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