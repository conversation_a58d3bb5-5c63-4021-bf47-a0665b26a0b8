{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\utils\\test-database.util.ts", "mappings": ";;;AAAA,qCAAiD;AACjD,6CAAsD;AACtD,6CAAgD;AAChD,2CAA6D;AAE7D;;;;;;;;;;;;GAYG;AACH,MAAa,gBAAgB;IAM3B;QAFQ,iBAAY,GAAG,IAAI,GAAG,EAA2B,CAAC;IAEnC,CAAC;IAExB;;OAEG;IACH,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;YAC/B,gBAAgB,CAAC,QAAQ,GAAG,IAAI,gBAAgB,EAAE,CAAC;QACrD,CAAC;QACD,OAAO,gBAAgB,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAAC,QAAe;QAC1C,IAAI,CAAC,aAAa,GAAG,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClD,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC;iBACnC,CAAC;gBACF,uBAAa,CAAC,YAAY,CAAC;oBACzB,OAAO,EAAE,CAAC,qBAAY,CAAC;oBACvB,UAAU,EAAE,KAAK,EAAE,aAA4B,EAAE,EAAE,CAAC,CAAC;wBACnD,IAAI,EAAE,UAAU;wBAChB,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;wBACpD,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;wBAC7C,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC;wBACvD,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC;wBACvD,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;wBAC5D,QAAQ;wBACR,WAAW,EAAE,IAAI;wBACjB,UAAU,EAAE,IAAI;wBAChB,OAAO,EAAE,aAAa,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,KAAK,MAAM;wBACjE,qBAAqB,EAAE,IAAI;wBAC3B,KAAK,EAAE;4BACL,eAAe,EAAE,EAAE;4BACnB,cAAc,EAAE,KAAK;4BACrB,OAAO,EAAE,KAAK;yBACf;qBACF,CAAC;oBACF,MAAM,EAAE,CAAC,sBAAa,CAAC;iBACxB,CAAC;gBACF,uBAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;aACnC;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAa,oBAAU,CAAC,CAAC;QAEjE,0BAA0B;QAC1B,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YACzD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,aAAa,CAAI,MAAmB;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,MAAM,CAAC,IAAI,YAAY,CAAC,CAAC;QACpE,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QACpB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB;QACvB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB;QACrB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;QAEjD,6BAA6B;QAC7B,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAE1D,sBAAsB;QACtB,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBAAkB,MAAM,CAAC,SAAS,2BAA2B,CAAC,CAAC;QAC7F,CAAC;QAED,+BAA+B;QAC/B,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;QACrC,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAA+B;QAChD,KAAK,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACrD,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAI,MAAmB;QAC1C,OAAO,IAAI,eAAe,CAAI,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAIlB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC;YACH,kDAAkD;YAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;YACjD,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;gBAC9B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAC7C,wEAAwE,MAAM,CAAC,SAAS,GAAG,CAC5F,CAAC;gBAEF,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC7B,MAAM,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,SAAS,iBAAiB,CAAC,CAAC;gBAC1D,CAAC;YACH,CAAC;YAED,gCAAgC;YAChC,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;gBAC9B,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;oBACxC,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;wBAChD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;oCAG7B,MAAM,CAAC,SAAS;;aAEvC,CAAC,CAAC;wBAEH,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACrE,MAAM,CAAC,IAAI,CAAC,sCAAsC,MAAM,CAAC,SAAS,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;wBACjG,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,gBAAgB;YAChB,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;gBAC9B,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;iCAGzB,MAAM,CAAC,SAAS;+BAClB,KAAK,CAAC,IAAI;WAC9B,CAAC,CAAC;oBAEH,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC7B,MAAM,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,qBAAqB,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;oBAC1E,CAAC;gBACH,CAAC;YACH,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB;QAKvB,MAAM,gBAAgB,GAA2B,EAAE,CAAC;QACpD,MAAM,WAAW,GAA+C,EAAE,CAAC;QAEnE,6BAA6B;QAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;QACjD,KAAK,MAAM,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,wBAAwB;YACnE,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACtD,IAAI,UAAU,EAAE,CAAC;gBACf,0BAA0B;gBAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC/B,MAAM,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;gBACrC,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC;gBAChD,gBAAgB,CAAC,GAAG,MAAM,CAAC,IAAI,SAAS,CAAC,GAAG,cAAc,CAAC;gBAE3D,IAAI,cAAc,GAAG,IAAI,EAAE,CAAC;oBAC1B,WAAW,CAAC,IAAI,CAAC;wBACf,KAAK,EAAE,iBAAiB,MAAM,CAAC,SAAS,YAAY;wBACpD,QAAQ,EAAE,cAAc;qBACzB,CAAC,CAAC;gBACL,CAAC;gBAED,yBAAyB;gBACzB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC9B,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC;gBACzB,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC;gBAC9C,gBAAgB,CAAC,GAAG,MAAM,CAAC,IAAI,QAAQ,CAAC,GAAG,aAAa,CAAC;gBAEzD,IAAI,aAAa,GAAG,GAAG,EAAE,CAAC;oBACxB,WAAW,CAAC,IAAI,CAAC;wBACf,KAAK,EAAE,wBAAwB,MAAM,CAAC,SAAS,EAAE;wBACjD,QAAQ,EAAE,aAAa;qBACxB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,MAAM,mBAAmB,GAAG;YAC1B,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,IAAI,CAAC;YAC9D,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC;YAC5D,kBAAkB,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,IAAI,CAAC;SACnE,CAAC;QAEF,OAAO;YACL,gBAAgB;YAChB,mBAAmB;YACnB,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB;QAIvB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;YACjD,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;gBAC9B,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;oBACxC,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;wBAChD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;qBAE3C,MAAM,CAAC,SAAS;0BACX,QAAQ,CAAC,qBAAqB,CAAC,SAAS;sBAC5C,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,YAAY,SAAS,QAAQ,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,YAAY;yBACzG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,YAAY;uBACvC,QAAQ,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,YAAY;aACxE,CAAC,CAAC;wBAEH,IAAI,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;4BAClC,MAAM,CAAC,IAAI,CAAC,SAAS,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,wBAAwB,MAAM,CAAC,SAAS,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;wBACpH,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,2BAA2B;YAC3B,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;gBAC9B,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACpC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;qBACpC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;mBAC/B,MAAM,CAAC,SAAS;uBACZ,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;WAEzC,CAAC,CAAC;oBAEH,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC1B,MAAM,CAAC,IAAI,CAAC,+CAA+C,MAAM,CAAC,IAAI,aAAa,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;oBACzG,CAAC;gBACH,CAAC;YACH,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,OAAO;YACL,UAAU,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC/B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACnB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;YACrD,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;CACF;AA7VD,4CA6VC;AAED;;;;GAIG;AACH,MAAa,eAAe;IAI1B,YACU,MAAmB,EACnB,UAAyB;QADzB,WAAM,GAAN,MAAM,CAAa;QACnB,eAAU,GAAV,UAAU,CAAe;QAL3B,kBAAa,GAAG,IAAI,GAAG,EAAe,CAAC;QACvC,cAAS,GAAG,IAAI,GAAG,EAAkB,CAAC;IAK3C,CAAC;IAEJ;;OAEG;IACH,UAAU,CAAC,QAAiB,EAAE,KAAU;QACtC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAkB,EAAE,KAAK,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAAiB,EAAE,aAAqB,CAAC;QACnD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAkB,EAAE,UAAU,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,YAAwB,EAAE;QACrC,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;QAErC,uBAAuB;QACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7D,IAAI,CAAC,CAAC,QAAQ,IAAI,SAAS,CAAC,EAAE,CAAC;gBAC5B,UAAkB,CAAC,QAAQ,CAAC,GAAG,OAAO,KAAK,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YAChF,CAAC;QACH,CAAC;QAED,wBAAwB;QACxB,KAAK,MAAM,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,CAAC,CAAC,QAAQ,IAAI,SAAS,CAAC,EAAE,CAAC;gBAC5B,UAAkB,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAC;gBAC7C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAErC,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,KAAa,EAAE,YAAwB,EAAE;QACxD,MAAM,QAAQ,GAAQ,EAAE,CAAC;QAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC5C,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxB,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAwB,EAAE;QAC9B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;QAErC,uBAAuB;QACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7D,IAAI,CAAC,CAAC,QAAQ,IAAI,SAAS,CAAC,EAAE,CAAC;gBAC5B,UAAkB,CAAC,QAAQ,CAAC,GAAG,OAAO,KAAK,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YAChF,CAAC;QACH,CAAC;QAED,wBAAwB;QACxB,KAAK,MAAM,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,CAAC,CAAC,QAAQ,IAAI,SAAS,CAAC,EAAE,CAAC;gBAC5B,UAAkB,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAC;gBAC7C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAErC,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,KAAa,EAAE,YAAwB,EAAE;QACjD,MAAM,QAAQ,GAAQ,EAAE,CAAC;QAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACrC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxB,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AA1GD,0CA0GC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\utils\\test-database.util.ts"], "sourcesContent": ["import { DataSource, Repository } from 'typeorm';\r\nimport { Test, TestingModule } from '@nestjs/testing';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ConfigModule, ConfigService } from '@nestjs/config';\r\n\r\n/**\r\n * Test Database Utility\r\n * \r\n * Comprehensive database testing utility providing:\r\n * - Isolated test database setup with transaction management\r\n * - Test data factory and fixture management\r\n * - Database cleanup and reset utilities\r\n * - Migration testing and schema validation\r\n * - Performance testing with query optimization\r\n * - Data consistency validation and integrity checks\r\n * - Mock data generation with realistic test scenarios\r\n * - Transaction isolation and rollback capabilities\r\n */\r\nexport class TestDatabaseUtil {\r\n  private static instance: TestDatabaseUtil;\r\n  private dataSource: DataSource;\r\n  private testingModule: TestingModule;\r\n  private repositories = new Map<string, Repository<any>>();\r\n\r\n  private constructor() {}\r\n\r\n  /**\r\n   * Get singleton instance\r\n   */\r\n  static getInstance(): TestDatabaseUtil {\r\n    if (!TestDatabaseUtil.instance) {\r\n      TestDatabaseUtil.instance = new TestDatabaseUtil();\r\n    }\r\n    return TestDatabaseUtil.instance;\r\n  }\r\n\r\n  /**\r\n   * Initialize test database\r\n   */\r\n  async initializeTestDatabase(entities: any[]): Promise<void> {\r\n    this.testingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: ['.env.test', '.env'],\r\n        }),\r\n        TypeOrmModule.forRootAsync({\r\n          imports: [ConfigModule],\r\n          useFactory: async (configService: ConfigService) => ({\r\n            type: 'postgres',\r\n            host: configService.get('TEST_DB_HOST', 'localhost'),\r\n            port: configService.get('TEST_DB_PORT', 5433),\r\n            username: configService.get('TEST_DB_USERNAME', 'test'),\r\n            password: configService.get('TEST_DB_PASSWORD', 'test'),\r\n            database: configService.get('TEST_DB_NAME', 'sentinel_test'),\r\n            entities,\r\n            synchronize: true,\r\n            dropSchema: true,\r\n            logging: configService.get('TEST_DB_LOGGING', 'false') === 'true',\r\n            maxQueryExecutionTime: 1000,\r\n            extra: {\r\n              connectionLimit: 10,\r\n              acquireTimeout: 30000,\r\n              timeout: 30000,\r\n            },\r\n          }),\r\n          inject: [ConfigService],\r\n        }),\r\n        TypeOrmModule.forFeature(entities),\r\n      ],\r\n    }).compile();\r\n\r\n    this.dataSource = this.testingModule.get<DataSource>(DataSource);\r\n    \r\n    // Initialize repositories\r\n    for (const entity of entities) {\r\n      const repository = this.dataSource.getRepository(entity);\r\n      this.repositories.set(entity.name, repository);\r\n    }\r\n\r\n    await this.dataSource.synchronize(true);\r\n  }\r\n\r\n  /**\r\n   * Get repository for entity\r\n   */\r\n  getRepository<T>(entity: new () => T): Repository<T> {\r\n    const repository = this.repositories.get(entity.name);\r\n    if (!repository) {\r\n      throw new Error(`Repository for entity ${entity.name} not found`);\r\n    }\r\n    return repository;\r\n  }\r\n\r\n  /**\r\n   * Get data source\r\n   */\r\n  getDataSource(): DataSource {\r\n    return this.dataSource;\r\n  }\r\n\r\n  /**\r\n   * Get testing module\r\n   */\r\n  getTestingModule(): TestingModule {\r\n    return this.testingModule;\r\n  }\r\n\r\n  /**\r\n   * Start transaction\r\n   */\r\n  async startTransaction(): Promise<void> {\r\n    await this.dataSource.query('BEGIN');\r\n  }\r\n\r\n  /**\r\n   * Rollback transaction\r\n   */\r\n  async rollbackTransaction(): Promise<void> {\r\n    await this.dataSource.query('ROLLBACK');\r\n  }\r\n\r\n  /**\r\n   * Commit transaction\r\n   */\r\n  async commitTransaction(): Promise<void> {\r\n    await this.dataSource.query('COMMIT');\r\n  }\r\n\r\n  /**\r\n   * Clean all tables\r\n   */\r\n  async cleanDatabase(): Promise<void> {\r\n    const entities = this.dataSource.entityMetadatas;\r\n    \r\n    // Disable foreign key checks\r\n    await this.dataSource.query('SET FOREIGN_KEY_CHECKS = 0');\r\n    \r\n    // Truncate all tables\r\n    for (const entity of entities) {\r\n      await this.dataSource.query(`TRUNCATE TABLE ${entity.tableName} RESTART IDENTITY CASCADE`);\r\n    }\r\n    \r\n    // Re-enable foreign key checks\r\n    await this.dataSource.query('SET FOREIGN_KEY_CHECKS = 1');\r\n  }\r\n\r\n  /**\r\n   * Reset database to initial state\r\n   */\r\n  async resetDatabase(): Promise<void> {\r\n    await this.dataSource.dropDatabase();\r\n    await this.dataSource.synchronize(true);\r\n  }\r\n\r\n  /**\r\n   * Seed test data\r\n   */\r\n  async seedTestData(seedData: Record<string, any[]>): Promise<void> {\r\n    for (const [entityName, data] of Object.entries(seedData)) {\r\n      const repository = this.repositories.get(entityName);\r\n      if (repository) {\r\n        await repository.save(data);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create test data factory\r\n   */\r\n  createTestDataFactory<T>(entity: new () => T): TestDataFactory<T> {\r\n    return new TestDataFactory<T>(entity, this.getRepository(entity));\r\n  }\r\n\r\n  /**\r\n   * Validate database schema\r\n   */\r\n  async validateSchema(): Promise<{\r\n    valid: boolean;\r\n    issues: string[];\r\n  }> {\r\n    const issues: string[] = [];\r\n    \r\n    try {\r\n      // Check if all entities have corresponding tables\r\n      const entities = this.dataSource.entityMetadatas;\r\n      for (const entity of entities) {\r\n        const tableExists = await this.dataSource.query(\r\n          `SELECT table_name FROM information_schema.tables WHERE table_name = '${entity.tableName}'`\r\n        );\r\n        \r\n        if (tableExists.length === 0) {\r\n          issues.push(`Table ${entity.tableName} does not exist`);\r\n        }\r\n      }\r\n\r\n      // Check foreign key constraints\r\n      for (const entity of entities) {\r\n        for (const relation of entity.relations) {\r\n          if (relation.isManyToOne || relation.isOneToOne) {\r\n            const constraintExists = await this.dataSource.query(`\r\n              SELECT constraint_name \r\n              FROM information_schema.table_constraints \r\n              WHERE table_name = '${entity.tableName}' \r\n              AND constraint_type = 'FOREIGN KEY'\r\n            `);\r\n            \r\n            if (constraintExists.length === 0 && relation.foreignKeys.length > 0) {\r\n              issues.push(`Foreign key constraint missing for ${entity.tableName}.${relation.propertyName}`);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // Check indexes\r\n      for (const entity of entities) {\r\n        for (const index of entity.indices) {\r\n          const indexExists = await this.dataSource.query(`\r\n            SELECT indexname \r\n            FROM pg_indexes \r\n            WHERE tablename = '${entity.tableName}' \r\n            AND indexname = '${index.name}'\r\n          `);\r\n          \r\n          if (indexExists.length === 0) {\r\n            issues.push(`Index ${index.name} missing on table ${entity.tableName}`);\r\n          }\r\n        }\r\n      }\r\n\r\n    } catch (error) {\r\n      issues.push(`Schema validation error: ${error.message}`);\r\n    }\r\n\r\n    return {\r\n      valid: issues.length === 0,\r\n      issues,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Run performance tests\r\n   */\r\n  async runPerformanceTests(): Promise<{\r\n    queryPerformance: Record<string, number>;\r\n    connectionPoolStats: any;\r\n    slowQueries: Array<{ query: string; duration: number }>;\r\n  }> {\r\n    const queryPerformance: Record<string, number> = {};\r\n    const slowQueries: Array<{ query: string; duration: number }> = [];\r\n\r\n    // Test basic CRUD operations\r\n    const entities = this.dataSource.entityMetadatas;\r\n    for (const entity of entities.slice(0, 3)) { // Test first 3 entities\r\n      const repository = this.repositories.get(entity.name);\r\n      if (repository) {\r\n        // Test SELECT performance\r\n        const selectStart = Date.now();\r\n        await repository.find({ take: 100 });\r\n        const selectDuration = Date.now() - selectStart;\r\n        queryPerformance[`${entity.name}_select`] = selectDuration;\r\n\r\n        if (selectDuration > 1000) {\r\n          slowQueries.push({\r\n            query: `SELECT * FROM ${entity.tableName} LIMIT 100`,\r\n            duration: selectDuration,\r\n          });\r\n        }\r\n\r\n        // Test COUNT performance\r\n        const countStart = Date.now();\r\n        await repository.count();\r\n        const countDuration = Date.now() - countStart;\r\n        queryPerformance[`${entity.name}_count`] = countDuration;\r\n\r\n        if (countDuration > 500) {\r\n          slowQueries.push({\r\n            query: `SELECT COUNT(*) FROM ${entity.tableName}`,\r\n            duration: countDuration,\r\n          });\r\n        }\r\n      }\r\n    }\r\n\r\n    // Get connection pool stats\r\n    const connectionPoolStats = {\r\n      totalConnections: this.dataSource.driver.pool?.totalCount || 0,\r\n      idleConnections: this.dataSource.driver.pool?.idleCount || 0,\r\n      waitingConnections: this.dataSource.driver.pool?.waitingCount || 0,\r\n    };\r\n\r\n    return {\r\n      queryPerformance,\r\n      connectionPoolStats,\r\n      slowQueries,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Test data consistency\r\n   */\r\n  async testDataConsistency(): Promise<{\r\n    consistent: boolean;\r\n    issues: string[];\r\n  }> {\r\n    const issues: string[] = [];\r\n\r\n    try {\r\n      // Check foreign key integrity\r\n      const entities = this.dataSource.entityMetadatas;\r\n      for (const entity of entities) {\r\n        for (const relation of entity.relations) {\r\n          if (relation.isManyToOne || relation.isOneToOne) {\r\n            const orphanedRecords = await this.dataSource.query(`\r\n              SELECT COUNT(*) as count\r\n              FROM ${entity.tableName} t1\r\n              LEFT JOIN ${relation.inverseEntityMetadata.tableName} t2 \r\n              ON t1.${relation.joinColumns[0]?.databaseName} = t2.${relation.inverseEntityMetadata.primaryColumns[0]?.databaseName}\r\n              WHERE t1.${relation.joinColumns[0]?.databaseName} IS NOT NULL \r\n              AND t2.${relation.inverseEntityMetadata.primaryColumns[0]?.databaseName} IS NULL\r\n            `);\r\n\r\n            if (orphanedRecords[0]?.count > 0) {\r\n              issues.push(`Found ${orphanedRecords[0].count} orphaned records in ${entity.tableName}.${relation.propertyName}`);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // Check unique constraints\r\n      for (const entity of entities) {\r\n        for (const unique of entity.uniques) {\r\n          const duplicates = await this.dataSource.query(`\r\n            SELECT ${unique.columnNames.join(', ')}, COUNT(*) as count\r\n            FROM ${entity.tableName}\r\n            GROUP BY ${unique.columnNames.join(', ')}\r\n            HAVING COUNT(*) > 1\r\n          `);\r\n\r\n          if (duplicates.length > 0) {\r\n            issues.push(`Found duplicate values in unique constraint ${unique.name} on table ${entity.tableName}`);\r\n          }\r\n        }\r\n      }\r\n\r\n    } catch (error) {\r\n      issues.push(`Data consistency check error: ${error.message}`);\r\n    }\r\n\r\n    return {\r\n      consistent: issues.length === 0,\r\n      issues,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Close database connection\r\n   */\r\n  async closeConnection(): Promise<void> {\r\n    if (this.dataSource && this.dataSource.isInitialized) {\r\n      await this.dataSource.destroy();\r\n    }\r\n    \r\n    if (this.testingModule) {\r\n      await this.testingModule.close();\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Test Data Factory\r\n * \r\n * Factory class for creating test data with realistic values\r\n */\r\nexport class TestDataFactory<T> {\r\n  private defaultValues = new Map<string, any>();\r\n  private sequences = new Map<string, number>();\r\n\r\n  constructor(\r\n    private entity: new () => T,\r\n    private repository: Repository<T>\r\n  ) {}\r\n\r\n  /**\r\n   * Set default value for property\r\n   */\r\n  setDefault(property: keyof T, value: any): this {\r\n    this.defaultValues.set(property as string, value);\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Set sequence for property\r\n   */\r\n  setSequence(property: keyof T, startValue: number = 1): this {\r\n    this.sequences.set(property as string, startValue);\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Create single entity\r\n   */\r\n  async create(overrides: Partial<T> = {}): Promise<T> {\r\n    const entityData = new this.entity();\r\n    \r\n    // Apply default values\r\n    for (const [property, value] of this.defaultValues.entries()) {\r\n      if (!(property in overrides)) {\r\n        (entityData as any)[property] = typeof value === 'function' ? value() : value;\r\n      }\r\n    }\r\n\r\n    // Apply sequence values\r\n    for (const [property, currentValue] of this.sequences.entries()) {\r\n      if (!(property in overrides)) {\r\n        (entityData as any)[property] = currentValue;\r\n        this.sequences.set(property, currentValue + 1);\r\n      }\r\n    }\r\n\r\n    // Apply overrides\r\n    Object.assign(entityData, overrides);\r\n\r\n    return await this.repository.save(entityData);\r\n  }\r\n\r\n  /**\r\n   * Create multiple entities\r\n   */\r\n  async createMany(count: number, overrides: Partial<T> = {}): Promise<T[]> {\r\n    const entities: T[] = [];\r\n    \r\n    for (let i = 0; i < count; i++) {\r\n      const entity = await this.create(overrides);\r\n      entities.push(entity);\r\n    }\r\n\r\n    return entities;\r\n  }\r\n\r\n  /**\r\n   * Build entity without saving\r\n   */\r\n  build(overrides: Partial<T> = {}): T {\r\n    const entityData = new this.entity();\r\n    \r\n    // Apply default values\r\n    for (const [property, value] of this.defaultValues.entries()) {\r\n      if (!(property in overrides)) {\r\n        (entityData as any)[property] = typeof value === 'function' ? value() : value;\r\n      }\r\n    }\r\n\r\n    // Apply sequence values\r\n    for (const [property, currentValue] of this.sequences.entries()) {\r\n      if (!(property in overrides)) {\r\n        (entityData as any)[property] = currentValue;\r\n        this.sequences.set(property, currentValue + 1);\r\n      }\r\n    }\r\n\r\n    // Apply overrides\r\n    Object.assign(entityData, overrides);\r\n\r\n    return entityData;\r\n  }\r\n\r\n  /**\r\n   * Build multiple entities without saving\r\n   */\r\n  buildMany(count: number, overrides: Partial<T> = {}): T[] {\r\n    const entities: T[] = [];\r\n    \r\n    for (let i = 0; i < count; i++) {\r\n      const entity = this.build(overrides);\r\n      entities.push(entity);\r\n    }\r\n\r\n    return entities;\r\n  }\r\n}\r\n"], "version": 3}