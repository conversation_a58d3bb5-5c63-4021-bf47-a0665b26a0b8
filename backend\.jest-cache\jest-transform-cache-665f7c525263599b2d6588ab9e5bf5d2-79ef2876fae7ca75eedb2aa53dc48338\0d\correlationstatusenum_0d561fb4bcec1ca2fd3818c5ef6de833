2054f25882bc11ce67f1ea79544160a0
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorrelationStatusUtils = exports.CorrelationStatus = void 0;
/**
 * Correlation Status Enum
 *
 * Represents the status of event correlation processing.
 * Used to track the lifecycle of correlation analysis and
 * indicate the current state of correlation operations.
 */
var CorrelationStatus;
(function (CorrelationStatus) {
    /**
     * Pending Correlation
     * - Event is queued for correlation analysis
     * - Correlation rules have not been applied yet
     * - Waiting for correlation engine processing
     */
    CorrelationStatus["PENDING"] = "PENDING";
    /**
     * Correlation In Progress
     * - Correlation analysis is currently running
     * - Rules are being applied and patterns analyzed
     * - Temporal and spatial analysis in progress
     */
    CorrelationStatus["IN_PROGRESS"] = "IN_PROGRESS";
    /**
     * Correlation Completed
     * - Correlation analysis has finished successfully
     * - All applicable rules have been processed
     * - Correlation results are available
     */
    CorrelationStatus["COMPLETED"] = "COMPLETED";
    /**
     * Correlation Failed
     * - Correlation analysis encountered errors
     * - Processing could not be completed
     * - Manual intervention may be required
     */
    CorrelationStatus["FAILED"] = "FAILED";
    /**
     * Partial Correlation
     * - Some correlation rules succeeded, others failed
     * - Partial results are available
     * - May require additional processing
     */
    CorrelationStatus["PARTIAL"] = "PARTIAL";
    /**
     * Correlation Skipped
     * - Correlation was intentionally bypassed
     * - Event does not meet correlation criteria
     * - No correlation analysis performed
     */
    CorrelationStatus["SKIPPED"] = "SKIPPED";
    /**
     * Correlation Timeout
     * - Correlation analysis exceeded time limits
     * - Processing was terminated due to timeout
     * - May be retried with different parameters
     */
    CorrelationStatus["TIMEOUT"] = "TIMEOUT";
})(CorrelationStatus || (exports.CorrelationStatus = CorrelationStatus = {}));
/**
 * Correlation Status Utilities
 */
class CorrelationStatusUtils {
    /**
     * Get all correlation statuses
     */
    static getAllStatuses() {
        return Object.values(CorrelationStatus);
    }
    /**
     * Get active correlation statuses (in progress)
     */
    static getActiveStatuses() {
        return [
            CorrelationStatus.PENDING,
            CorrelationStatus.IN_PROGRESS,
        ];
    }
    /**
     * Get completed correlation statuses
     */
    static getCompletedStatuses() {
        return [
            CorrelationStatus.COMPLETED,
            CorrelationStatus.PARTIAL,
        ];
    }
    /**
     * Get failed correlation statuses
     */
    static getFailedStatuses() {
        return [
            CorrelationStatus.FAILED,
            CorrelationStatus.TIMEOUT,
        ];
    }
    /**
     * Get terminal correlation statuses (no further processing)
     */
    static getTerminalStatuses() {
        return [
            CorrelationStatus.COMPLETED,
            CorrelationStatus.FAILED,
            CorrelationStatus.PARTIAL,
            CorrelationStatus.SKIPPED,
            CorrelationStatus.TIMEOUT,
        ];
    }
    /**
     * Check if status is active
     */
    static isActive(status) {
        return CorrelationStatusUtils.getActiveStatuses().includes(status);
    }
    /**
     * Check if status is completed
     */
    static isCompleted(status) {
        return CorrelationStatusUtils.getCompletedStatuses().includes(status);
    }
    /**
     * Check if status is failed
     */
    static isFailed(status) {
        return CorrelationStatusUtils.getFailedStatuses().includes(status);
    }
    /**
     * Check if status is terminal
     */
    static isTerminal(status) {
        return CorrelationStatusUtils.getTerminalStatuses().includes(status);
    }
    /**
     * Check if status allows retry
     */
    static allowsRetry(status) {
        return [
            CorrelationStatus.FAILED,
            CorrelationStatus.TIMEOUT,
        ].includes(status);
    }
    /**
     * Get next valid statuses from current status
     */
    static getNextValidStatuses(currentStatus) {
        const transitions = {
            [CorrelationStatus.PENDING]: [
                CorrelationStatus.IN_PROGRESS,
                CorrelationStatus.SKIPPED,
                CorrelationStatus.FAILED,
            ],
            [CorrelationStatus.IN_PROGRESS]: [
                CorrelationStatus.COMPLETED,
                CorrelationStatus.PARTIAL,
                CorrelationStatus.FAILED,
                CorrelationStatus.TIMEOUT,
            ],
            [CorrelationStatus.COMPLETED]: [], // Terminal
            [CorrelationStatus.FAILED]: [
                CorrelationStatus.PENDING, // For retry
            ],
            [CorrelationStatus.PARTIAL]: [
                CorrelationStatus.IN_PROGRESS, // For additional processing
            ],
            [CorrelationStatus.SKIPPED]: [], // Terminal
            [CorrelationStatus.TIMEOUT]: [
                CorrelationStatus.PENDING, // For retry
            ],
        };
        return transitions[currentStatus] || [];
    }
    /**
     * Check if status transition is valid
     */
    static isValidTransition(from, to) {
        const validNextStatuses = CorrelationStatusUtils.getNextValidStatuses(from);
        return validNextStatuses.includes(to);
    }
    /**
     * Get human-readable description
     */
    static getDescription(status) {
        const descriptions = {
            [CorrelationStatus.PENDING]: 'Event is queued for correlation analysis',
            [CorrelationStatus.IN_PROGRESS]: 'Correlation analysis is currently running',
            [CorrelationStatus.COMPLETED]: 'Correlation analysis completed successfully',
            [CorrelationStatus.FAILED]: 'Correlation analysis failed with errors',
            [CorrelationStatus.PARTIAL]: 'Correlation analysis completed with partial results',
            [CorrelationStatus.SKIPPED]: 'Correlation analysis was intentionally bypassed',
            [CorrelationStatus.TIMEOUT]: 'Correlation analysis exceeded time limits',
        };
        return descriptions[status];
    }
    /**
     * Get color code for UI display
     */
    static getColorCode(status) {
        const colors = {
            [CorrelationStatus.PENDING]: '#F59E0B', // Amber
            [CorrelationStatus.IN_PROGRESS]: '#3B82F6', // Blue
            [CorrelationStatus.COMPLETED]: '#059669', // Green
            [CorrelationStatus.FAILED]: '#DC2626', // Red
            [CorrelationStatus.PARTIAL]: '#D97706', // Orange
            [CorrelationStatus.SKIPPED]: '#6B7280', // Gray
            [CorrelationStatus.TIMEOUT]: '#EF4444', // Light Red
        };
        return colors[status];
    }
    /**
     * Get icon name for UI display
     */
    static getIconName(status) {
        const icons = {
            [CorrelationStatus.PENDING]: 'clock',
            [CorrelationStatus.IN_PROGRESS]: 'refresh',
            [CorrelationStatus.COMPLETED]: 'check-circle',
            [CorrelationStatus.FAILED]: 'x-circle',
            [CorrelationStatus.PARTIAL]: 'alert-triangle',
            [CorrelationStatus.SKIPPED]: 'skip-forward',
            [CorrelationStatus.TIMEOUT]: 'clock-x',
        };
        return icons[status];
    }
    /**
     * Get priority level for processing
     */
    static getPriorityLevel(status) {
        const priorities = {
            [CorrelationStatus.FAILED]: 5, // Highest priority for retry
            [CorrelationStatus.TIMEOUT]: 4, // High priority for retry
            [CorrelationStatus.PENDING]: 3, // Normal priority
            [CorrelationStatus.PARTIAL]: 2, // Lower priority for completion
            [CorrelationStatus.IN_PROGRESS]: 1, // Lowest priority (already processing)
            [CorrelationStatus.COMPLETED]: 0, // No priority (done)
            [CorrelationStatus.SKIPPED]: 0, // No priority (done)
        };
        return priorities[status];
    }
    /**
     * Validate correlation status
     */
    static isValid(status) {
        return Object.values(CorrelationStatus).includes(status);
    }
    /**
     * Get correlation status from string (case-insensitive)
     */
    static fromString(value) {
        const normalized = value.toUpperCase().trim();
        const statuses = Object.values(CorrelationStatus);
        return statuses.find(status => status === normalized) || null;
    }
}
exports.CorrelationStatusUtils = CorrelationStatusUtils;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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