{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\enriched-event.factory.ts", "mappings": ";;;AAAA,6EAAwI;AAIxI,sEAA6D;AAE7D,wFAA8E;AAC9E,4EAAmE;AAsHnE;;;;;;;;;;;;;;GAcG;AACH,MAAa,oBAAoB;IAM/B;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,OAAmC;QAC/C,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAEhD,kCAAkC;QAClC,MAAM,kBAAkB,GAAuB;YAC7C,iBAAiB,EAAE,eAAe,CAAC,EAAE;YACrC,QAAQ,EAAE,eAAe,CAAC,QAAQ;YAClC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,eAAe,CAAC,IAAI;YAC1C,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,eAAe,CAAC,QAAQ;YACtD,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,eAAe,CAAC,MAAM;YAChD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,oDAAqB,CAAC,QAAQ;YAC5E,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,wCAAgB,CAAC,OAAO;YACtE,cAAc,EAAE,eAAe,CAAC,cAAc;YAC9C,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,eAAe,CAAC,KAAK;YAC7C,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,eAAe,CAAC,WAAW;YAC/D,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,eAAe,CAAC,IAAI;YAC1C,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,eAAe,CAAC,SAAS;YACzD,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,eAAe,CAAC,eAAe;YAC3E,UAAU,EAAE;gBACV,GAAG,eAAe,CAAC,UAAU;gBAC7B,GAAG,OAAO,CAAC,UAAU;aACtB;YACD,aAAa,EAAE,eAAe,CAAC,aAAa;YAC5C,aAAa,EAAE,eAAe,CAAC,aAAa;YAC5C,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,EAAE;YACxC,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,EAAE;YAC5C,sBAAsB,EAAE,OAAO,CAAC,sBAAsB;YACtD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,oBAAoB,EAAE,OAAO,CAAC,iBAAiB;YAC/C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,kBAAkB,EAAE,CAAC;SACtB,CAAC;QAEF,OAAO,qCAAa,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CACzB,eAAgC,EAChC,SAAoC,EAAE;QAEtC,MAAM,UAAU,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAEjE,yBAAyB;QACzB,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,oBAAoB,CAChE,eAAe,CAAC,cAAc,EAC9B,UAAU,CAAC,cAAc,EACzB,UAAU,CAAC,cAAc,CAC1B,CAAC;QAEF,qCAAqC;QACrC,MAAM,sBAAsB,GAAG,oBAAoB,CAAC,+BAA+B,CACjF,gBAAgB,EAChB,UAAU,CACX,CAAC;QAEF,sCAAsC;QACtC,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,yBAAyB,CACrE,gBAAgB,CAAC,cAAc,CAChC,CAAC;QAEF,yCAAyC;QACzC,MAAM,oBAAoB,GAAG,oBAAoB,CAAC,gCAAgC,CAChF,eAAe,EACf,sBAAsB,EACtB,gBAAgB,EAChB,gBAAgB,CAAC,gBAAgB,EACjC,UAAU,CACX,CAAC;QAEF,OAAO,oBAAoB,CAAC,MAAM,CAAC;YACjC,eAAe;YACf,YAAY,EAAE,gBAAgB,CAAC,YAAY;YAC3C,YAAY,EAAE,gBAAgB,CAAC,YAAY;YAC3C,cAAc,EAAE,gBAAgB,CAAC,cAAc;YAC/C,gBAAgB,EAAE,gBAAgB,CAAC,OAAO;gBACxC,CAAC,CAAC,wCAAgB,CAAC,SAAS;gBAC5B,CAAC,CAAC,wCAAgB,CAAC,MAAM;YAC3B,sBAAsB;YACtB,gBAAgB;YAChB,YAAY,EAAE,gBAAgB,CAAC,YAAY;YAC3C,WAAW,EAAE,gBAAgB,CAAC,WAAW;YACzC,cAAc,EAAE,gBAAgB,CAAC,cAAc;YAC/C,kBAAkB,EAAE,gBAAgB,CAAC,kBAAkB;YACvD,gBAAgB,EAAE,gBAAgB,CAAC,gBAAgB;YACnD,gBAAgB,EAAE,gBAAgB,CAAC,gBAAgB;YACnD,iBAAiB,EAAE,oBAAoB;SACxC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,4BAA4B,CACjC,eAAgC,EAChC,kBAAgD;QAEhD,gDAAgD;QAChD,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,iCAAiC,CAC9E,eAAe,CAAC,cAAc,EAC9B,kBAAkB,CACnB,CAAC;QAEF,sCAAsC;QACtC,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,yBAAyB,CACrE,iBAAiB,CAAC,cAAc,CACjC,CAAC;QAEF,uDAAuD;QACvD,MAAM,YAAY,GAAG;YACnB,GAAG,eAAe,CAAC,cAAc;YACjC,GAAG,iBAAiB,CAAC,YAAY;YACjC,mBAAmB,EAAE;gBACnB,KAAK,EAAE,gBAAgB;gBACvB,YAAY,EAAE,iBAAiB,CAAC,WAAW;gBAC3C,gBAAgB,EAAE,iBAAiB,CAAC,eAAe;gBACnD,UAAU,EAAE,iBAAiB,CAAC,UAAU;aACzC;SACF,CAAC;QAEF,OAAO,oBAAoB,CAAC,MAAM,CAAC;YACjC,eAAe;YACf,YAAY;YACZ,YAAY,EAAE,iBAAiB,CAAC,YAAY;YAC5C,cAAc,EAAE,iBAAiB,CAAC,cAAc;YAChD,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;YAC5C,gBAAgB;YAChB,gBAAgB,EAAE,iBAAiB,CAAC,gBAAgB;YACpD,kBAAkB,EAAE,iBAAiB,CAAC,kBAAkB;YACxD,sBAAsB,EAAE,iBAAiB,CAAC,YAAY;SACvD,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,OAA+B;QAUhD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,UAAU,GAAoB,EAAE,CAAC;QACvC,MAAM,MAAM,GAA0D,EAAE,CAAC;QAEzE,KAAK,MAAM,eAAe,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACvD,IAAI,CAAC;gBACH,MAAM,aAAa,GAAG,oBAAoB,CAAC,MAAM,CAAC;oBAChD,eAAe;oBACf,YAAY,EAAE,oBAAoB,CAAC,eAAe,CAChD,eAAe,CAAC,cAAc,EAC9B,OAAO,CAAC,OAAO,CAChB;oBACD,YAAY,EAAE,OAAO,CAAC,KAAK;iBAC5B,CAAC,CAAC;gBAEH,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACjC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC9E,MAAM,CAAC,IAAI,CAAC,EAAE,eAAe,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;gBAEtD,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;oBAC1B,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAEhD,OAAO;YACL,UAAU;YACV,MAAM;YACN,OAAO,EAAE;gBACP,KAAK,EAAE,OAAO,CAAC,gBAAgB,CAAC,MAAM;gBACtC,UAAU,EAAE,UAAU,CAAC,MAAM;gBAC7B,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,gBAAgB;aACjB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CACrB,YAAiD,EAAE;QAEnD,iDAAiD;QACjD,MAAM,mBAAmB,GAAG,SAAS,CAAC,eAAe;YACnD,oBAAoB,CAAC,yBAAyB,EAAE,CAAC;QAEnD,MAAM,cAAc,GAA+B;YACjD,eAAe,EAAE,mBAAmB;YACpC,YAAY,EAAE;gBACZ,GAAG,mBAAmB,CAAC,cAAc;gBACrC,QAAQ,EAAE,IAAI;gBACd,oBAAoB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBAC9C,mBAAmB,EAAE;oBACnB,KAAK,EAAE,EAAE;oBACT,YAAY,EAAE,CAAC;oBACf,gBAAgB,EAAE,CAAC;iBACpB;gBACD,UAAU,EAAE;oBACV,aAAa,EAAE,EAAE;oBACjB,iBAAiB,EAAE,EAAE;iBACtB;gBACD,WAAW,EAAE;oBACX,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,UAAU;oBAChB,WAAW,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE;iBAC7C;aACF;YACD,YAAY,EAAE,EAAE;YAChB,cAAc,EAAE;gBACd;oBACE,MAAM,EAAE,yCAAgB,CAAC,aAAa;oBACtC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;oBACtC,UAAU,EAAE,EAAE;oBACd,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;gBACD;oBACE,MAAM,EAAE,yCAAgB,CAAC,cAAc;oBACvC,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE;oBACzC,UAAU,EAAE,EAAE;oBACd,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;aACF;YACD,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;YAC5C,sBAAsB,EAAE,EAAE;YAC1B,gBAAgB,EAAE,EAAE;SACrB,CAAC;QAEF,OAAO,oBAAoB,CAAC,MAAM,CAAC;YACjC,GAAG,cAAc;YACjB,GAAG,SAAS;SACb,CAAC,CAAC;IACL,CAAC;IAED,yBAAyB;IAEjB,MAAM,CAAC,gBAAgB,CAAC,MAAiC;QAC/D,OAAO;YACL,cAAc,EAAE,EAAE;YAClB,6BAA6B,EAAE,oBAAoB,CAAC,8BAA8B;YAClF,8BAA8B,EAAE,IAAI;YACpC,8BAA8B,EAAE,IAAI;YACpC,mBAAmB,EAAE,oBAAoB,CAAC,6BAA6B;YACvE,mBAAmB,EAAE,oBAAoB,CAAC,0BAA0B;YACpE,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,yCAAgB,CAAC;YAC/C,gBAAgB,EAAE,EAAE;YACpB,qBAAqB,EAAE,oBAAoB,CAAC,+BAA+B;YAC3E,GAAG,MAAM;SACV,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,oBAAoB,CACjC,cAAmC,EACnC,KAAuB,EACvB,cAAkC;QAalC,MAAM,YAAY,GAAG,EAAE,GAAG,cAAc,EAAE,CAAC;QAC3C,MAAM,YAAY,GAAqB,EAAE,CAAC;QAC1C,MAAM,cAAc,GAAqB,EAAE,CAAC;QAC5C,MAAM,gBAAgB,GAAa,EAAE,CAAC;QAEtC,iDAAiD;QACjD,MAAM,WAAW,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QAEvE,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,oCAAoC;gBACpC,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACtD,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAChC,CAAC;gBAEF,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACpC,SAAS,CAAC,sCAAsC;gBAClD,CAAC;gBAED,+CAA+C;gBAC/C,MAAM,UAAU,GAAG,oBAAoB,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;gBAEtE,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;oBACvB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACxB,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;oBAE7C,IAAI,UAAU,CAAC,cAAc,EAAE,CAAC;wBAC9B,cAAc,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC;oBACpD,CAAC;gBACH,CAAC;qBAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACzB,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,aAAa,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;gBACpF,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC9E,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,kBAAkB,YAAY,EAAE,CAAC,CAAC;gBACrF,CAAC;YACH,CAAC;QACH,CAAC;QAED,8BAA8B;QAC9B,MAAM,QAAQ,GAAG,oBAAoB,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;QAEtE,OAAO;YACL,OAAO,EAAE,gBAAgB,CAAC,MAAM,KAAK,CAAC;YACtC,YAAY;YACZ,YAAY;YACZ,cAAc;YACd,gBAAgB;YAChB,GAAG,QAAQ;SACZ,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,SAAS,CACtB,IAAyB,EACzB,IAAoB;QAOpB,4EAA4E;QAC5E,QAAQ,IAAI,CAAC,EAAE,EAAE,CAAC;YAChB,KAAK,0BAA0B;gBAC7B,OAAO,oBAAoB,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAC3D,KAAK,wBAAwB;gBAC3B,OAAO,oBAAoB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC1D,KAAK,yBAAyB;gBAC5B,OAAO,oBAAoB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC1D,KAAK,0BAA0B;gBAC7B,OAAO,oBAAoB,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAC3D;gBACE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,IAAyB;QAM7D,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,UAAU,CAAC;QAE3E,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0CAA0C,EAAE,CAAC;QAC/E,CAAC;QAED,uBAAuB;QACvB,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;QACxD,MAAM,QAAQ,GAAG,eAAe,GAAG,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,GAAG,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC;QAEpG,MAAM,cAAc,GAAmB;YACrC,MAAM,EAAE,yCAAgB,CAAC,aAAa;YACtC,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE;gBACJ,UAAU,EAAE,SAAS;gBACrB,gBAAgB,EAAE,eAAe;gBACjC,QAAQ;gBACR,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;YACD,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,aAAa,EAAE;oBACb,KAAK,EAAE,eAAe;oBACtB,QAAQ;iBACT;aACF;YACD,cAAc,EAAE,CAAC,cAAc,CAAC;SACjC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,IAAyB;QAM5D,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,UAAU,CAAC;QAE3E,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,qCAAqC,EAAE,CAAC;QAC1E,CAAC;QAED,wBAAwB;QACxB,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7D,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QACxE,MAAM,MAAM,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QACvF,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAE/D,MAAM,cAAc,GAAmB;YACrC,MAAM,EAAE,yCAAgB,CAAC,cAAc;YACvC,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE;gBACJ,UAAU,EAAE,SAAS;gBACrB,OAAO;gBACP,IAAI;gBACJ,WAAW,EAAE;oBACX,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE;oBAC7B,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG;iBAC/B;aACF;YACD,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,WAAW,EAAE;oBACX,OAAO;oBACP,IAAI;iBACL;aACF;YACD,cAAc,EAAE,CAAC,cAAc,CAAC;SACjC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,IAAyB;QAM5D,sCAAsC;QACtC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;QACpD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;QAEjD,MAAM,cAAc,GAAmB;YACrC,MAAM,EAAE,yCAAgB,CAAC,uBAAuB;YAChD,IAAI,EAAE,qBAAqB;YAC3B,IAAI,EAAE;gBACJ,YAAY,EAAE,WAAW;gBACzB,gBAAgB,EAAE,UAAU;gBAC5B,YAAY,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;gBACrC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACvC;YACD,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,mBAAmB,EAAE;oBACnB,KAAK,EAAE,WAAW;oBAClB,gBAAgB,EAAE,UAAU;iBAC7B;aACF;YACD,cAAc,EAAE,CAAC,cAAc,CAAC;SACjC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,IAAyB;QAM7D,gCAAgC;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC;QAE5D,MAAM,cAAc,GAAmB;YACrC,MAAM,EAAE,yCAAgB,CAAC,gBAAgB;YACzC,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE;gBACJ,QAAQ,EAAE,OAAO;gBACjB,KAAK,EAAE,eAAe;gBACtB,WAAW,EAAE,MAAM;gBACnB,QAAQ,EAAE,eAAe;gBACzB,EAAE,EAAE,qBAAqB;aAC1B;YACD,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,aAAa,EAAE;oBACb,KAAK,EAAE,eAAe;oBACtB,WAAW,EAAE,MAAM;iBACpB;aACF;YACD,cAAc,EAAE,CAAC,cAAc,CAAC;SACjC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,iCAAiC,CAC9C,cAAmC,EACnC,OAAqC;QAYrC,4CAA4C;QAC5C,MAAM,YAAY,GAAG,EAAE,GAAG,cAAc,EAAE,CAAC;QAC3C,MAAM,cAAc,GAAqB,EAAE,CAAC;QAC5C,MAAM,gBAAgB,GAA2B,EAAE,CAAC;QAEpD,gCAAgC;QAChC,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACrC,MAAM,QAAQ,GAAmB;gBAC/B,MAAM;gBACN,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE;oBACJ,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;oBAC7C,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC;oBAC1C,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;iBAC5C;gBACD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;gBAC3C,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,IAAI,QAAQ,CAAC,UAAU,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBACjD,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC9B,gBAAgB,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC;YACxD,CAAC;QACH,CAAC;QAED,OAAO;YACL,YAAY;YACZ,YAAY,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;YACvC,cAAc;YACd,WAAW,EAAE,cAAc,CAAC,MAAM;YAClC,eAAe,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YAC3F,UAAU,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC;gBACnC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM;gBACxF,CAAC,CAAC,CAAC;YACL,gBAAgB;YAChB,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE;YACzF,YAAY,EAAE,EAAE;SACjB,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,+BAA+B,CAC5C,MAAwF,EACxF,MAAwB;QAExB,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,qCAAqC;QACrC,KAAK,IAAI,MAAM,CAAC,gBAAgB,CAAC,MAAM,GAAG,EAAE,CAAC;QAE7C,oCAAoC;QACpC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,+CAA+C;QAC/C,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1E,MAAM,oBAAoB,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/E,MAAM,oBAAoB,GAAG,aAAa,CAAC,MAAM,GAAG,oBAAoB,CAAC,MAAM,CAAC;QAChF,KAAK,IAAI,oBAAoB,GAAG,EAAE,CAAC;QAEnC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC3C,CAAC;IAEO,MAAM,CAAC,yBAAyB,CAAC,cAAgC;QACvE,MAAM,eAAe,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACnD,IAAI,CAAC,IAAI,KAAK,qBAAqB;YACnC,IAAI,CAAC,MAAM,KAAK,yCAAgB,CAAC,uBAAuB;YACxD,IAAI,CAAC,MAAM,KAAK,yCAAgB,CAAC,KAAK,CACvC,CAAC;QAEF,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE3C,MAAM,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC;QACxE,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAE/E,uBAAuB;QACvB,MAAM,gBAAgB,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC,MAAM,GAAG,GAAG,CAAC;QAExH,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,gBAAgB,CAAC,CAAC;IACjD,CAAC;IAEO,MAAM,CAAC,gCAAgC,CAC7C,eAAgC,EAChC,sBAA8B,EAC9B,gBAAwB,EACxB,gBAA0B,EAC1B,MAAwB;QAExB,wDAAwD;QACxD,IAAI,gBAAgB,IAAI,EAAE,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,yCAAyC;QACzC,IAAI,MAAM,CAAC,8BAA8B,IAAI,eAAe,CAAC,UAAU,EAAE,EAAE,CAAC;YAC1E,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wCAAwC;QACxC,IAAI,MAAM,CAAC,8BAA8B,IAAI,eAAe,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,EAAE,CAAC;YACjG,OAAO,IAAI,CAAC;QACd,CAAC;QAED,gDAAgD;QAChD,IAAI,sBAAsB,GAAG,MAAM,CAAC,6BAA6B,EAAE,CAAC;YAClE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,mDAAmD;QACnD,IAAI,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,MAAM,CAAC,eAAe,CAAC,cAAgC;QAO7D,MAAM,QAAQ,GAAQ,EAAE,CAAC;QAEzB,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE,CAAC;YAClC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,eAAe;oBAClB,QAAQ,CAAC,YAAY,GAAG,EAAE,GAAG,QAAQ,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;oBACnE,MAAM;gBACR,KAAK,cAAc;oBACjB,QAAQ,CAAC,WAAW,GAAG,EAAE,GAAG,QAAQ,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;oBACjE,MAAM;gBACR,KAAK,iBAAiB;oBACpB,QAAQ,CAAC,cAAc,GAAG,EAAE,GAAG,QAAQ,CAAC,cAAc,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;oBACvE,MAAM;gBACR,KAAK,aAAa;oBAChB,QAAQ,CAAC,kBAAkB,GAAG,EAAE,GAAG,QAAQ,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;oBAC/E,MAAM;gBACR,KAAK,YAAY;oBACf,IAAI,CAAC,QAAQ,CAAC,gBAAgB;wBAAE,QAAQ,CAAC,gBAAgB,GAAG,EAAE,CAAC;oBAC/D,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;oBACvF,MAAM;YACV,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,MAAM,CAAC,eAAe,CAC5B,cAAmC,EACnC,OAA4B;QAE5B,kEAAkE;QAClE,OAAO;YACL,GAAG,cAAc;YACjB,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACrC,kBAAkB,EAAE,OAAO,IAAI,EAAE;SAClC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,yBAAyB;QACtC,uEAAuE;QACvE,4EAA4E;QAC5E,OAAO,EAAqB,CAAC;IAC/B,CAAC;;AAxsBH,oDAysBC;AAxsByB,mDAA8B,GAAG,EAAE,CAAC;AACpC,kDAA6B,GAAG,EAAE,CAAC;AACnC,+CAA0B,GAAG,KAAK,CAAC,CAAC,aAAa;AACjD,oDAA+B,GAAG,EAAE,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\enriched-event.factory.ts"], "sourcesContent": ["import { EnrichedEvent, EnrichedEventProps, EnrichmentStatus, EnrichmentRule, EnrichmentData } from '../entities/enriched-event.entity';\r\nimport { NormalizedEvent } from '../entities/normalized-event.entity';\r\nimport { UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../enums/event-processing-status.enum';\r\nimport { EnrichmentSource } from '../enums/enrichment-source.enum';\r\n\r\n/**\r\n * Enriched Event Creation Options\r\n */\r\nexport interface CreateEnrichedEventOptions {\r\n  /** Enriched event ID (optional, will be generated if not provided) */\r\n  id?: UniqueEntityId;\r\n  /** Normalized event to enrich */\r\n  normalizedEvent: NormalizedEvent;\r\n  /** Enriched data with additional context */\r\n  enrichedData: Record<string, any>;\r\n  /** Applied enrichment rules */\r\n  appliedRules?: EnrichmentRule[];\r\n  /** Enrichment data from various sources */\r\n  enrichmentData?: EnrichmentData[];\r\n  /** Initial enrichment status (optional, defaults to PENDING) */\r\n  enrichmentStatus?: EnrichmentStatus;\r\n  /** Override event type (optional, uses normalized event type if not provided) */\r\n  type?: EventType;\r\n  /** Override event severity (optional, uses normalized event severity if not provided) */\r\n  severity?: EventSeverity;\r\n  /** Override event status (optional, uses normalized event status if not provided) */\r\n  status?: EventStatus;\r\n  /** Override processing status (optional, uses ENRICHED if not provided) */\r\n  processingStatus?: EventProcessingStatus;\r\n  /** Override title (optional, uses normalized event title if not provided) */\r\n  title?: string;\r\n  /** Override description (optional, uses normalized event description if not provided) */\r\n  description?: string;\r\n  /** Override tags (optional, uses normalized event tags if not provided) */\r\n  tags?: string[];\r\n  /** Override risk score (optional, uses normalized event risk score if not provided) */\r\n  riskScore?: number;\r\n  /** Override confidence level (optional, uses normalized event confidence level if not provided) */\r\n  confidenceLevel?: number;\r\n  /** Additional attributes */\r\n  attributes?: Record<string, any>;\r\n  /** Force manual review requirement */\r\n  forceManualReview?: boolean;\r\n  /** Initial enrichment quality score */\r\n  enrichmentQualityScore?: number;\r\n  /** Initial threat intelligence score */\r\n  threatIntelScore?: number;\r\n  /** Asset context information */\r\n  assetContext?: Record<string, any>;\r\n  /** User context information */\r\n  userContext?: Record<string, any>;\r\n  /** Network context information */\r\n  networkContext?: Record<string, any>;\r\n  /** Geolocation context */\r\n  geolocationContext?: Record<string, any>;\r\n  /** Reputation scores from various sources */\r\n  reputationScores?: Record<string, number>;\r\n  /** Initial validation errors */\r\n  validationErrors?: string[];\r\n}\r\n\r\n/**\r\n * Enrichment Configuration\r\n */\r\nexport interface EnrichmentConfig {\r\n  /** Available enrichment rules */\r\n  availableRules: EnrichmentRule[];\r\n  /** Minimum enrichment quality threshold */\r\n  minEnrichmentQualityThreshold: number;\r\n  /** Whether to require manual review for high-risk events */\r\n  requireManualReviewForHighRisk: boolean;\r\n  /** Whether to require manual review for critical events */\r\n  requireManualReviewForCritical: boolean;\r\n  /** Maximum allowed validation errors */\r\n  maxValidationErrors: number;\r\n  /** Default enrichment timeout in milliseconds */\r\n  enrichmentTimeoutMs: number;\r\n  /** Enabled enrichment sources */\r\n  enabledSources: EnrichmentSource[];\r\n  /** Source priority mapping */\r\n  sourcePriorities: Record<EnrichmentSource, number>;\r\n  /** Maximum concurrent enrichment requests */\r\n  maxConcurrentRequests: number;\r\n}\r\n\r\n/**\r\n * Batch Enrichment Options\r\n */\r\nexport interface BatchEnrichmentOptions {\r\n  /** Normalized events to enrich */\r\n  normalizedEvents: NormalizedEvent[];\r\n  /** Rules to apply to all events */\r\n  rules: EnrichmentRule[];\r\n  /** Whether to stop on first failure */\r\n  stopOnFailure?: boolean;\r\n  /** Maximum concurrent enrichments */\r\n  maxConcurrency?: number;\r\n  /** Batch processing timeout */\r\n  batchTimeoutMs?: number;\r\n  /** Enrichment sources to use */\r\n  sources?: EnrichmentSource[];\r\n}\r\n\r\n/**\r\n * Threat Intelligence Enrichment Options\r\n */\r\nexport interface ThreatIntelEnrichmentOptions {\r\n  /** Threat intelligence sources to query */\r\n  sources: EnrichmentSource[];\r\n  /** Minimum confidence threshold */\r\n  minConfidence: number;\r\n  /** Maximum age of threat intelligence data in hours */\r\n  maxAgeHours: number;\r\n  /** Whether to include reputation data */\r\n  includeReputation: boolean;\r\n  /** Whether to include geolocation data */\r\n  includeGeolocation: boolean;\r\n  /** Custom threat intelligence rules */\r\n  customRules?: EnrichmentRule[];\r\n}\r\n\r\n/**\r\n * EnrichedEvent Factory\r\n * \r\n * Factory class for creating EnrichedEvent entities with proper validation and defaults.\r\n * Handles complex enrichment scenarios and ensures all business rules are applied.\r\n * \r\n * Key responsibilities:\r\n * - Create enriched events from normalized events\r\n * - Apply enrichment rules and validation\r\n * - Calculate enrichment quality scores\r\n * - Determine manual review requirements\r\n * - Handle batch enrichment operations\r\n * - Manage threat intelligence integration\r\n * - Support various enrichment sources and contexts\r\n */\r\nexport class EnrichedEventFactory {\r\n  private static readonly DEFAULT_MIN_ENRICHMENT_QUALITY = 70;\r\n  private static readonly DEFAULT_MAX_VALIDATION_ERRORS = 10;\r\n  private static readonly DEFAULT_ENRICHMENT_TIMEOUT = 60000; // 60 seconds\r\n  private static readonly DEFAULT_MAX_CONCURRENT_REQUESTS = 10;\r\n\r\n  /**\r\n   * Create a new EnrichedEvent from a NormalizedEvent\r\n   */\r\n  static create(options: CreateEnrichedEventOptions): EnrichedEvent {\r\n    const normalizedEvent = options.normalizedEvent;\r\n\r\n    // Build enriched event properties\r\n    const enrichedEventProps: EnrichedEventProps = {\r\n      normalizedEventId: normalizedEvent.id,\r\n      metadata: normalizedEvent.metadata,\r\n      type: options.type || normalizedEvent.type,\r\n      severity: options.severity || normalizedEvent.severity,\r\n      status: options.status || normalizedEvent.status,\r\n      processingStatus: options.processingStatus || EventProcessingStatus.ENRICHED,\r\n      enrichmentStatus: options.enrichmentStatus || EnrichmentStatus.PENDING,\r\n      normalizedData: normalizedEvent.normalizedData,\r\n      enrichedData: options.enrichedData,\r\n      title: options.title || normalizedEvent.title,\r\n      description: options.description || normalizedEvent.description,\r\n      tags: options.tags || normalizedEvent.tags,\r\n      riskScore: options.riskScore || normalizedEvent.riskScore,\r\n      confidenceLevel: options.confidenceLevel || normalizedEvent.confidenceLevel,\r\n      attributes: {\r\n        ...normalizedEvent.attributes,\r\n        ...options.attributes,\r\n      },\r\n      correlationId: normalizedEvent.correlationId,\r\n      parentEventId: normalizedEvent.parentEventId,\r\n      appliedRules: options.appliedRules || [],\r\n      enrichmentData: options.enrichmentData || [],\r\n      enrichmentQualityScore: options.enrichmentQualityScore,\r\n      threatIntelScore: options.threatIntelScore,\r\n      assetContext: options.assetContext,\r\n      userContext: options.userContext,\r\n      networkContext: options.networkContext,\r\n      geolocationContext: options.geolocationContext,\r\n      reputationScores: options.reputationScores,\r\n      requiresManualReview: options.forceManualReview,\r\n      validationErrors: options.validationErrors,\r\n      enrichmentAttempts: 0,\r\n    };\r\n\r\n    return EnrichedEvent.create(enrichedEventProps, options.id);\r\n  }\r\n\r\n  /**\r\n   * Create an EnrichedEvent with automatic enrichment\r\n   */\r\n  static createWithEnrichment(\r\n    normalizedEvent: NormalizedEvent,\r\n    config: Partial<EnrichmentConfig> = {}\r\n  ): EnrichedEvent {\r\n    const fullConfig = EnrichedEventFactory.getDefaultConfig(config);\r\n    \r\n    // Apply enrichment rules\r\n    const enrichmentResult = EnrichedEventFactory.applyEnrichmentRules(\r\n      normalizedEvent.normalizedData,\r\n      fullConfig.availableRules,\r\n      fullConfig.enabledSources\r\n    );\r\n\r\n    // Calculate enrichment quality score\r\n    const enrichmentQualityScore = EnrichedEventFactory.calculateEnrichmentQualityScore(\r\n      enrichmentResult,\r\n      fullConfig\r\n    );\r\n\r\n    // Calculate threat intelligence score\r\n    const threatIntelScore = EnrichedEventFactory.calculateThreatIntelScore(\r\n      enrichmentResult.enrichmentData\r\n    );\r\n\r\n    // Determine if manual review is required\r\n    const requiresManualReview = EnrichedEventFactory.determineManualReviewRequirement(\r\n      normalizedEvent,\r\n      enrichmentQualityScore,\r\n      threatIntelScore,\r\n      enrichmentResult.validationErrors,\r\n      fullConfig\r\n    );\r\n\r\n    return EnrichedEventFactory.create({\r\n      normalizedEvent,\r\n      enrichedData: enrichmentResult.enrichedData,\r\n      appliedRules: enrichmentResult.appliedRules,\r\n      enrichmentData: enrichmentResult.enrichmentData,\r\n      enrichmentStatus: enrichmentResult.success \r\n        ? EnrichmentStatus.COMPLETED \r\n        : EnrichmentStatus.FAILED,\r\n      enrichmentQualityScore,\r\n      threatIntelScore,\r\n      assetContext: enrichmentResult.assetContext,\r\n      userContext: enrichmentResult.userContext,\r\n      networkContext: enrichmentResult.networkContext,\r\n      geolocationContext: enrichmentResult.geolocationContext,\r\n      reputationScores: enrichmentResult.reputationScores,\r\n      validationErrors: enrichmentResult.validationErrors,\r\n      forceManualReview: requiresManualReview,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an EnrichedEvent with threat intelligence focus\r\n   */\r\n  static createWithThreatIntelligence(\r\n    normalizedEvent: NormalizedEvent,\r\n    threatIntelOptions: ThreatIntelEnrichmentOptions\r\n  ): EnrichedEvent {\r\n    // Apply threat intelligence specific enrichment\r\n    const threatIntelResult = EnrichedEventFactory.applyThreatIntelligenceEnrichment(\r\n      normalizedEvent.normalizedData,\r\n      threatIntelOptions\r\n    );\r\n\r\n    // Calculate threat intelligence score\r\n    const threatIntelScore = EnrichedEventFactory.calculateThreatIntelScore(\r\n      threatIntelResult.enrichmentData\r\n    );\r\n\r\n    // Build enriched data with threat intelligence context\r\n    const enrichedData = {\r\n      ...normalizedEvent.normalizedData,\r\n      ...threatIntelResult.enrichedData,\r\n      threat_intelligence: {\r\n        score: threatIntelScore,\r\n        sources_used: threatIntelResult.sourcesUsed,\r\n        indicators_found: threatIntelResult.indicatorsFound,\r\n        confidence: threatIntelResult.confidence,\r\n      },\r\n    };\r\n\r\n    return EnrichedEventFactory.create({\r\n      normalizedEvent,\r\n      enrichedData,\r\n      appliedRules: threatIntelResult.appliedRules,\r\n      enrichmentData: threatIntelResult.enrichmentData,\r\n      enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n      threatIntelScore,\r\n      reputationScores: threatIntelResult.reputationScores,\r\n      geolocationContext: threatIntelResult.geolocationContext,\r\n      enrichmentQualityScore: threatIntelResult.qualityScore,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create multiple EnrichedEvents in batch\r\n   */\r\n  static createBatch(options: BatchEnrichmentOptions): {\r\n    successful: EnrichedEvent[];\r\n    failed: { normalizedEvent: NormalizedEvent; error: string }[];\r\n    summary: {\r\n      total: number;\r\n      successful: number;\r\n      failed: number;\r\n      processingTimeMs: number;\r\n    };\r\n  } {\r\n    const startTime = Date.now();\r\n    const successful: EnrichedEvent[] = [];\r\n    const failed: { normalizedEvent: NormalizedEvent; error: string }[] = [];\r\n\r\n    for (const normalizedEvent of options.normalizedEvents) {\r\n      try {\r\n        const enrichedEvent = EnrichedEventFactory.create({\r\n          normalizedEvent,\r\n          enrichedData: EnrichedEventFactory.enrichEventData(\r\n            normalizedEvent.normalizedData,\r\n            options.sources\r\n          ),\r\n          appliedRules: options.rules,\r\n        });\r\n\r\n        successful.push(enrichedEvent);\r\n      } catch (error) {\r\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n        failed.push({ normalizedEvent, error: errorMessage });\r\n\r\n        if (options.stopOnFailure) {\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    const processingTimeMs = Date.now() - startTime;\r\n\r\n    return {\r\n      successful,\r\n      failed,\r\n      summary: {\r\n        total: options.normalizedEvents.length,\r\n        successful: successful.length,\r\n        failed: failed.length,\r\n        processingTimeMs,\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create an EnrichedEvent for testing purposes\r\n   */\r\n  static createForTesting(\r\n    overrides: Partial<CreateEnrichedEventOptions> = {}\r\n  ): EnrichedEvent {\r\n    // Create a mock normalized event if not provided\r\n    const mockNormalizedEvent = overrides.normalizedEvent || \r\n      EnrichedEventFactory.createMockNormalizedEvent();\r\n\r\n    const defaultOptions: CreateEnrichedEventOptions = {\r\n      normalizedEvent: mockNormalizedEvent,\r\n      enrichedData: {\r\n        ...mockNormalizedEvent.normalizedData,\r\n        enriched: true,\r\n        enrichment_timestamp: new Date().toISOString(),\r\n        threat_intelligence: {\r\n          score: 45,\r\n          sources_used: 3,\r\n          indicators_found: 2,\r\n        },\r\n        reputation: {\r\n          ip_reputation: 75,\r\n          domain_reputation: 80,\r\n        },\r\n        geolocation: {\r\n          country: 'US',\r\n          city: 'New York',\r\n          coordinates: { lat: 40.7128, lng: -74.0060 },\r\n        },\r\n      },\r\n      appliedRules: [],\r\n      enrichmentData: [\r\n        {\r\n          source: EnrichmentSource.IP_REPUTATION,\r\n          type: 'reputation',\r\n          data: { score: 75, category: 'clean' },\r\n          confidence: 85,\r\n          timestamp: new Date(),\r\n        },\r\n        {\r\n          source: EnrichmentSource.IP_GEOLOCATION,\r\n          type: 'geolocation',\r\n          data: { country: 'US', city: 'New York' },\r\n          confidence: 95,\r\n          timestamp: new Date(),\r\n        },\r\n      ],\r\n      enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n      enrichmentQualityScore: 85,\r\n      threatIntelScore: 45,\r\n    };\r\n\r\n    return EnrichedEventFactory.create({\r\n      ...defaultOptions,\r\n      ...overrides,\r\n    });\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private static getDefaultConfig(config: Partial<EnrichmentConfig>): EnrichmentConfig {\r\n    return {\r\n      availableRules: [],\r\n      minEnrichmentQualityThreshold: EnrichedEventFactory.DEFAULT_MIN_ENRICHMENT_QUALITY,\r\n      requireManualReviewForHighRisk: true,\r\n      requireManualReviewForCritical: true,\r\n      maxValidationErrors: EnrichedEventFactory.DEFAULT_MAX_VALIDATION_ERRORS,\r\n      enrichmentTimeoutMs: EnrichedEventFactory.DEFAULT_ENRICHMENT_TIMEOUT,\r\n      enabledSources: Object.values(EnrichmentSource),\r\n      sourcePriorities: {},\r\n      maxConcurrentRequests: EnrichedEventFactory.DEFAULT_MAX_CONCURRENT_REQUESTS,\r\n      ...config,\r\n    };\r\n  }\r\n\r\n  private static applyEnrichmentRules(\r\n    normalizedData: Record<string, any>,\r\n    rules: EnrichmentRule[],\r\n    enabledSources: EnrichmentSource[]\r\n  ): {\r\n    success: boolean;\r\n    enrichedData: Record<string, any>;\r\n    appliedRules: EnrichmentRule[];\r\n    enrichmentData: EnrichmentData[];\r\n    validationErrors: string[];\r\n    assetContext?: Record<string, any>;\r\n    userContext?: Record<string, any>;\r\n    networkContext?: Record<string, any>;\r\n    geolocationContext?: Record<string, any>;\r\n    reputationScores?: Record<string, number>;\r\n  } {\r\n    const enrichedData = { ...normalizedData };\r\n    const appliedRules: EnrichmentRule[] = [];\r\n    const enrichmentData: EnrichmentData[] = [];\r\n    const validationErrors: string[] = [];\r\n\r\n    // Sort rules by priority (higher priority first)\r\n    const sortedRules = [...rules].sort((a, b) => b.priority - a.priority);\r\n\r\n    for (const rule of sortedRules) {\r\n      try {\r\n        // Check if rule sources are enabled\r\n        const enabledRuleSources = rule.sources.filter(source => \r\n          enabledSources.includes(source)\r\n        );\r\n\r\n        if (enabledRuleSources.length === 0) {\r\n          continue; // Skip rule if no sources are enabled\r\n        }\r\n\r\n        // Apply rule logic (simplified implementation)\r\n        const ruleResult = EnrichedEventFactory.applyRule(enrichedData, rule);\r\n        \r\n        if (ruleResult.success) {\r\n          appliedRules.push(rule);\r\n          Object.assign(enrichedData, ruleResult.data);\r\n          \r\n          if (ruleResult.enrichmentData) {\r\n            enrichmentData.push(...ruleResult.enrichmentData);\r\n          }\r\n        } else if (rule.required) {\r\n          validationErrors.push(`Required rule '${rule.name}' failed: ${ruleResult.error}`);\r\n        }\r\n      } catch (error) {\r\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n        if (rule.required) {\r\n          validationErrors.push(`Required rule '${rule.name}' threw error: ${errorMessage}`);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Extract context information\r\n    const contexts = EnrichedEventFactory.extractContexts(enrichmentData);\r\n\r\n    return {\r\n      success: validationErrors.length === 0,\r\n      enrichedData,\r\n      appliedRules,\r\n      enrichmentData,\r\n      validationErrors,\r\n      ...contexts,\r\n    };\r\n  }\r\n\r\n  private static applyRule(\r\n    data: Record<string, any>,\r\n    rule: EnrichmentRule\r\n  ): { \r\n    success: boolean; \r\n    data?: Record<string, any>; \r\n    error?: string;\r\n    enrichmentData?: EnrichmentData[];\r\n  } {\r\n    // Simplified implementation - in practice, this would be more sophisticated\r\n    switch (rule.id) {\r\n      case 'ip_reputation_enrichment':\r\n        return EnrichedEventFactory.enrichWithIPReputation(data);\r\n      case 'geolocation_enrichment':\r\n        return EnrichedEventFactory.enrichWithGeolocation(data);\r\n      case 'threat_intel_enrichment':\r\n        return EnrichedEventFactory.enrichWithThreatIntel(data);\r\n      case 'asset_context_enrichment':\r\n        return EnrichedEventFactory.enrichWithAssetContext(data);\r\n      default:\r\n        return { success: true, data };\r\n    }\r\n  }\r\n\r\n  private static enrichWithIPReputation(data: Record<string, any>): {\r\n    success: boolean;\r\n    data?: Record<string, any>;\r\n    error?: string;\r\n    enrichmentData?: EnrichmentData[];\r\n  } {\r\n    const ipAddress = data.source_ip || data.destination_ip || data.ip_address;\r\n    \r\n    if (!ipAddress) {\r\n      return { success: false, error: 'No IP address found for reputation check' };\r\n    }\r\n\r\n    // Mock reputation data\r\n    const reputationScore = Math.floor(Math.random() * 100);\r\n    const category = reputationScore > 70 ? 'clean' : reputationScore > 30 ? 'suspicious' : 'malicious';\r\n\r\n    const enrichmentData: EnrichmentData = {\r\n      source: EnrichmentSource.IP_REPUTATION,\r\n      type: 'reputation',\r\n      data: {\r\n        ip_address: ipAddress,\r\n        reputation_score: reputationScore,\r\n        category,\r\n        last_seen: new Date().toISOString(),\r\n      },\r\n      confidence: 85,\r\n      timestamp: new Date(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        ...data,\r\n        ip_reputation: {\r\n          score: reputationScore,\r\n          category,\r\n        },\r\n      },\r\n      enrichmentData: [enrichmentData],\r\n    };\r\n  }\r\n\r\n  private static enrichWithGeolocation(data: Record<string, any>): {\r\n    success: boolean;\r\n    data?: Record<string, any>;\r\n    error?: string;\r\n    enrichmentData?: EnrichmentData[];\r\n  } {\r\n    const ipAddress = data.source_ip || data.destination_ip || data.ip_address;\r\n    \r\n    if (!ipAddress) {\r\n      return { success: false, error: 'No IP address found for geolocation' };\r\n    }\r\n\r\n    // Mock geolocation data\r\n    const countries = ['US', 'GB', 'DE', 'FR', 'JP', 'CN', 'RU'];\r\n    const country = countries[Math.floor(Math.random() * countries.length)];\r\n    const cities = ['New York', 'London', 'Berlin', 'Paris', 'Tokyo', 'Beijing', 'Moscow'];\r\n    const city = cities[Math.floor(Math.random() * cities.length)];\r\n\r\n    const enrichmentData: EnrichmentData = {\r\n      source: EnrichmentSource.IP_GEOLOCATION,\r\n      type: 'geolocation',\r\n      data: {\r\n        ip_address: ipAddress,\r\n        country,\r\n        city,\r\n        coordinates: {\r\n          lat: Math.random() * 180 - 90,\r\n          lng: Math.random() * 360 - 180,\r\n        },\r\n      },\r\n      confidence: 95,\r\n      timestamp: new Date(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        ...data,\r\n        geolocation: {\r\n          country,\r\n          city,\r\n        },\r\n      },\r\n      enrichmentData: [enrichmentData],\r\n    };\r\n  }\r\n\r\n  private static enrichWithThreatIntel(data: Record<string, any>): {\r\n    success: boolean;\r\n    data?: Record<string, any>;\r\n    error?: string;\r\n    enrichmentData?: EnrichmentData[];\r\n  } {\r\n    // Mock threat intelligence enrichment\r\n    const threatScore = Math.floor(Math.random() * 100);\r\n    const indicators = Math.floor(Math.random() * 5);\r\n\r\n    const enrichmentData: EnrichmentData = {\r\n      source: EnrichmentSource.COMMERCIAL_THREAT_INTEL,\r\n      type: 'threat_intelligence',\r\n      data: {\r\n        threat_score: threatScore,\r\n        indicators_found: indicators,\r\n        threat_types: ['malware', 'phishing'],\r\n        last_updated: new Date().toISOString(),\r\n      },\r\n      confidence: 80,\r\n      timestamp: new Date(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        ...data,\r\n        threat_intelligence: {\r\n          score: threatScore,\r\n          indicators_found: indicators,\r\n        },\r\n      },\r\n      enrichmentData: [enrichmentData],\r\n    };\r\n  }\r\n\r\n  private static enrichWithAssetContext(data: Record<string, any>): {\r\n    success: boolean;\r\n    data?: Record<string, any>;\r\n    error?: string;\r\n    enrichmentData?: EnrichmentData[];\r\n  } {\r\n    // Mock asset context enrichment\r\n    const assetId = data.asset_id || data.hostname || 'unknown';\r\n    \r\n    const enrichmentData: EnrichmentData = {\r\n      source: EnrichmentSource.ASSET_MANAGEMENT,\r\n      type: 'asset_context',\r\n      data: {\r\n        asset_id: assetId,\r\n        owner: 'IT Department',\r\n        criticality: 'high',\r\n        location: 'Data Center 1',\r\n        os: 'Windows Server 2019',\r\n      },\r\n      confidence: 90,\r\n      timestamp: new Date(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        ...data,\r\n        asset_context: {\r\n          owner: 'IT Department',\r\n          criticality: 'high',\r\n        },\r\n      },\r\n      enrichmentData: [enrichmentData],\r\n    };\r\n  }\r\n\r\n  private static applyThreatIntelligenceEnrichment(\r\n    normalizedData: Record<string, any>,\r\n    options: ThreatIntelEnrichmentOptions\r\n  ): {\r\n    enrichedData: Record<string, any>;\r\n    appliedRules: EnrichmentRule[];\r\n    enrichmentData: EnrichmentData[];\r\n    sourcesUsed: number;\r\n    indicatorsFound: number;\r\n    confidence: number;\r\n    reputationScores: Record<string, number>;\r\n    geolocationContext: Record<string, any>;\r\n    qualityScore: number;\r\n  } {\r\n    // Simplified threat intelligence enrichment\r\n    const enrichedData = { ...normalizedData };\r\n    const enrichmentData: EnrichmentData[] = [];\r\n    const reputationScores: Record<string, number> = {};\r\n    \r\n    // Mock threat intelligence data\r\n    for (const source of options.sources) {\r\n      const mockData: EnrichmentData = {\r\n        source,\r\n        type: 'threat_intelligence',\r\n        data: {\r\n          threat_score: Math.floor(Math.random() * 100),\r\n          indicators: Math.floor(Math.random() * 10),\r\n          confidence: Math.floor(Math.random() * 100),\r\n        },\r\n        confidence: Math.floor(Math.random() * 100),\r\n        timestamp: new Date(),\r\n      };\r\n      \r\n      if (mockData.confidence >= options.minConfidence) {\r\n        enrichmentData.push(mockData);\r\n        reputationScores[source] = mockData.data.threat_score;\r\n      }\r\n    }\r\n\r\n    return {\r\n      enrichedData,\r\n      appliedRules: options.customRules || [],\r\n      enrichmentData,\r\n      sourcesUsed: enrichmentData.length,\r\n      indicatorsFound: enrichmentData.reduce((sum, data) => sum + (data.data.indicators || 0), 0),\r\n      confidence: enrichmentData.length > 0 \r\n        ? enrichmentData.reduce((sum, data) => sum + data.confidence, 0) / enrichmentData.length \r\n        : 0,\r\n      reputationScores,\r\n      geolocationContext: options.includeGeolocation ? { country: 'US', city: 'New York' } : {},\r\n      qualityScore: 85,\r\n    };\r\n  }\r\n\r\n  private static calculateEnrichmentQualityScore(\r\n    result: { success: boolean; appliedRules: EnrichmentRule[]; validationErrors: string[] },\r\n    config: EnrichmentConfig\r\n  ): number {\r\n    let score = 100;\r\n\r\n    // Reduce score for validation errors\r\n    score -= result.validationErrors.length * 15;\r\n\r\n    // Reduce score if enrichment failed\r\n    if (!result.success) {\r\n      score -= 25;\r\n    }\r\n\r\n    // Reduce score based on missing required rules\r\n    const requiredRules = config.availableRules.filter(rule => rule.required);\r\n    const appliedRequiredRules = result.appliedRules.filter(rule => rule.required);\r\n    const missingRequiredRules = requiredRules.length - appliedRequiredRules.length;\r\n    score -= missingRequiredRules * 20;\r\n\r\n    return Math.max(0, Math.min(100, score));\r\n  }\r\n\r\n  private static calculateThreatIntelScore(enrichmentData: EnrichmentData[]): number {\r\n    const threatIntelData = enrichmentData.filter(data => \r\n      data.type === 'threat_intelligence' || \r\n      data.source === EnrichmentSource.COMMERCIAL_THREAT_INTEL ||\r\n      data.source === EnrichmentSource.OSINT\r\n    );\r\n\r\n    if (threatIntelData.length === 0) return 0;\r\n\r\n    const scores = threatIntelData.map(data => data.data.threat_score || 0);\r\n    const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n    \r\n    // Weight by confidence\r\n    const confidenceWeight = threatIntelData.reduce((sum, data) => sum + data.confidence, 0) / threatIntelData.length / 100;\r\n    \r\n    return Math.floor(avgScore * confidenceWeight);\r\n  }\r\n\r\n  private static determineManualReviewRequirement(\r\n    normalizedEvent: NormalizedEvent,\r\n    enrichmentQualityScore: number,\r\n    threatIntelScore: number,\r\n    validationErrors: string[],\r\n    config: EnrichmentConfig\r\n  ): boolean {\r\n    // High threat intelligence score requires manual review\r\n    if (threatIntelScore >= 85) {\r\n      return true;\r\n    }\r\n\r\n    // High-risk events require manual review\r\n    if (config.requireManualReviewForHighRisk && normalizedEvent.isHighRisk()) {\r\n      return true;\r\n    }\r\n\r\n    // Critical events require manual review\r\n    if (config.requireManualReviewForCritical && normalizedEvent.severity === EventSeverity.CRITICAL) {\r\n      return true;\r\n    }\r\n\r\n    // Low enrichment quality requires manual review\r\n    if (enrichmentQualityScore < config.minEnrichmentQualityThreshold) {\r\n      return true;\r\n    }\r\n\r\n    // Too many validation errors require manual review\r\n    if (validationErrors.length > config.maxValidationErrors) {\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  private static extractContexts(enrichmentData: EnrichmentData[]): {\r\n    assetContext?: Record<string, any>;\r\n    userContext?: Record<string, any>;\r\n    networkContext?: Record<string, any>;\r\n    geolocationContext?: Record<string, any>;\r\n    reputationScores?: Record<string, number>;\r\n  } {\r\n    const contexts: any = {};\r\n\r\n    for (const data of enrichmentData) {\r\n      switch (data.type) {\r\n        case 'asset_context':\r\n          contexts.assetContext = { ...contexts.assetContext, ...data.data };\r\n          break;\r\n        case 'user_context':\r\n          contexts.userContext = { ...contexts.userContext, ...data.data };\r\n          break;\r\n        case 'network_context':\r\n          contexts.networkContext = { ...contexts.networkContext, ...data.data };\r\n          break;\r\n        case 'geolocation':\r\n          contexts.geolocationContext = { ...contexts.geolocationContext, ...data.data };\r\n          break;\r\n        case 'reputation':\r\n          if (!contexts.reputationScores) contexts.reputationScores = {};\r\n          contexts.reputationScores[data.source] = data.data.reputation_score || data.data.score;\r\n          break;\r\n      }\r\n    }\r\n\r\n    return contexts;\r\n  }\r\n\r\n  private static enrichEventData(\r\n    normalizedData: Record<string, any>,\r\n    sources?: EnrichmentSource[]\r\n  ): Record<string, any> {\r\n    // Basic enrichment - in practice this would be more sophisticated\r\n    return {\r\n      ...normalizedData,\r\n      enriched_at: new Date().toISOString(),\r\n      enrichment_sources: sources || [],\r\n    };\r\n  }\r\n\r\n  private static createMockNormalizedEvent(): NormalizedEvent {\r\n    // This would create a proper mock NormalizedEvent - simplified for now\r\n    // In practice, you'd use the NormalizedEventFactory to create a proper mock\r\n    return {} as NormalizedEvent;\r\n  }\r\n}"], "version": 3}