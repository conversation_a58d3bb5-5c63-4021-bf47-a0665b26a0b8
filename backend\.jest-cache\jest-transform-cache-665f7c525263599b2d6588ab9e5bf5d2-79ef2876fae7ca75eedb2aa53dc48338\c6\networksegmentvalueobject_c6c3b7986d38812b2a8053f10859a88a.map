{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\network-segment.value-object.ts", "mappings": ";;;AAAA,oGAA+F;AAC/F,uEAAqE;AAErE;;GAEG;AACH,IAAY,kBAGX;AAHD,WAAY,kBAAkB;IAC5B,mCAAa,CAAA;IACb,mCAAa,CAAA;AACf,CAAC,EAHW,kBAAkB,kCAAlB,kBAAkB,QAG7B;AAED;;GAEG;AACH,IAAY,mBAOX;AAPD,WAAY,mBAAmB;IAC7B,wCAAiB,CAAA;IACjB,0CAAmB,CAAA;IACnB,4CAAqB,CAAA;IACrB,8CAAuB,CAAA;IACvB,gDAAyB,CAAA;IACzB,4CAAqB,CAAA;AACvB,CAAC,EAPW,mBAAmB,mCAAnB,mBAAmB,QAO9B;AAYD;;;;;;;;;;;;;GAaG;AACH,MAAa,cAAe,SAAQ,mCAAoC;IAItE,YAAY,KAA0B;QACpC,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IAES,oBAAoB;QAC3B,IAAY,CAAC,UAAU,GAAG,mCAAS,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAC3E,IAAY,CAAC,KAAK,GAAI,IAAY,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC;QAC5G,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAES,QAAQ;QAChB,2BAA2B;QAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClF,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,yBAAyB;QACzB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,0CAA0C;QAC1C,MAAM,MAAM,GAAG,6FAA6F,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAC9I,MAAM,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QAE1C,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,eAAe,EAAE,CAAC;YAC/E,MAAM,IAAI,KAAK,CAAC,uCAAuC,eAAe,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAES,sBAAsB;QAC9B,4CAA4C;QAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QAC5D,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,eAAe,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,uCAAuC,eAAe,EAAE,CAAC,CAAC;QAC5E,CAAC;QAED,kEAAkE;QAClE,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7B,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACpC,CAAC;IACH,CAAC;IAEO,0BAA0B;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;QACnD,MAAM,IAAI,GAAG,CAAC,UAAU,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC;QACnE,MAAM,kBAAkB,GAAG,UAAU,GAAG,IAAI,CAAC;QAE7C,IAAI,UAAU,KAAK,kBAAkB,EAAE,CAAC;YACtC,MAAM,cAAc,GAAG;gBACrB,CAAC,kBAAkB,KAAK,EAAE,CAAC,GAAG,IAAI;gBAClC,CAAC,kBAAkB,KAAK,EAAE,CAAC,GAAG,IAAI;gBAClC,CAAC,kBAAkB,KAAK,CAAC,CAAC,GAAG,IAAI;gBACjC,kBAAkB,GAAG,IAAI;aAC1B,CAAC;YACF,MAAM,eAAe,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,qCAAqC,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;QACtG,CAAC;IACH,CAAC;IAEO,0BAA0B;QAChC,6CAA6C;QAC7C,sFAAsF;QACtF,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACvC,2DAA2D;YAC3D,OAAO;QACT,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAY;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAE5C,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,IAAI,cAAc,CAAC,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,cAAsB,EAAE,YAAoB;QACxD,OAAO,IAAI,cAAc,CAAC,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa;QAClB,OAAO,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,CAAC,aAAa;QAClB,OAAO,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;IAClD,CAAC;IAED,MAAM,CAAC,aAAa;QAClB,OAAO,cAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;IACnD,CAAC;IAED,MAAM,CAAC,SAAS;QACd,OAAO,cAAc,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,KAAK,KAAK,kBAAkB,CAAC,IAAI,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,KAAK,KAAK,kBAAkB,CAAC,IAAI,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,SAA6B;QACpC,MAAM,EAAE,GAAG,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,mCAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAEvF,2BAA2B;QAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;YACvE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QAEhD,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,QAAQ;gBACX,OAAO,mBAAmB,CAAC,MAAM,CAAC;YACpC,KAAK,SAAS;gBACZ,OAAO,mBAAmB,CAAC,OAAO,CAAC;YACrC,KAAK,UAAU;gBACb,OAAO,mBAAmB,CAAC,QAAQ,CAAC;YACtC,KAAK,WAAW;gBACd,OAAO,mBAAmB,CAAC,SAAS,CAAC;YACvC,KAAK,YAAY;gBACf,OAAO,mBAAmB,CAAC,UAAU,CAAC;YACxC,KAAK,UAAU;gBACb,OAAO,mBAAmB,CAAC,QAAQ,CAAC;YACtC;gBACE,OAAO,mBAAmB,CAAC,QAAQ,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,QAAQ,EAAE,KAAK,mBAAmB,CAAC,OAAO,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,QAAQ,EAAE,KAAK,mBAAmB,CAAC,MAAM,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClB,MAAM,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YAC/C,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;QACvC,CAAC;aAAM,CAAC;YACN,MAAM,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YAChD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEvC,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC;YACnD,oDAAoD;YACpD,OAAO,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,6CAA6C;QAC7C,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,IAAI,GAAG,CAAC,UAAU,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC;QACnE,MAAM,MAAM,GAAG;YACb,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG,IAAI;YACpB,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG,IAAI;YACpB,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI;YACnB,IAAI,GAAG,IAAI;SACZ,CAAC;QAEF,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,eAAe;QACb,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,YAAY,GAAG,CAAC,UAAU,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAC/D,MAAM,MAAM,GAAG;YACb,CAAC,YAAY,KAAK,EAAE,CAAC,GAAG,IAAI;YAC5B,CAAC,YAAY,KAAK,EAAE,CAAC,GAAG,IAAI;YAC5B,CAAC,YAAY,KAAK,CAAC,CAAC,GAAG,IAAI;YAC3B,YAAY,GAAG,IAAI;SACpB,CAAC;QAEF,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;QACnD,MAAM,QAAQ,GAAG,CAAC,UAAU,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAC3D,MAAM,YAAY,GAAG,UAAU,GAAG,QAAQ,CAAC;QAE3C,MAAM,MAAM,GAAG;YACb,CAAC,YAAY,KAAK,EAAE,CAAC,GAAG,IAAI;YAC5B,CAAC,YAAY,KAAK,EAAE,CAAC,GAAG,IAAI;YAC5B,CAAC,YAAY,KAAK,CAAC,CAAC,GAAG,IAAI;YAC3B,YAAY,GAAG,IAAI;SACpB,CAAC;QAEF,OAAO,mCAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;gBACnC,0DAA0D;gBAC1D,OAAO,IAAI,CAAC,UAAU,CAAC;YACzB,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;YACnD,MAAM,YAAY,GAAG,UAAU,GAAG,CAAC,CAAC;YAEpC,MAAM,MAAM,GAAG;gBACb,CAAC,YAAY,KAAK,EAAE,CAAC,GAAG,IAAI;gBAC5B,CAAC,YAAY,KAAK,EAAE,CAAC,GAAG,IAAI;gBAC5B,CAAC,YAAY,KAAK,CAAC,CAAC,GAAG,IAAI;gBAC3B,YAAY,GAAG,IAAI;aACpB,CAAC;YAEF,OAAO,mCAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9E,CAAC;aAAM,CAAC;YACN,gEAAgE;YAChE,sCAAsC;YACtC,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;gBACnC,iDAAiD;gBACjD,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,EAAE,EAAE,CAAC;oBACpC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;oBACnD,MAAM,WAAW,GAAG,UAAU,GAAG,CAAC,CAAC;oBAEnC,MAAM,MAAM,GAAG;wBACb,CAAC,WAAW,KAAK,EAAE,CAAC,GAAG,IAAI;wBAC3B,CAAC,WAAW,KAAK,EAAE,CAAC,GAAG,IAAI;wBAC3B,CAAC,WAAW,KAAK,CAAC,CAAC,GAAG,IAAI;wBAC1B,WAAW,GAAG,IAAI;qBACnB,CAAC;oBAEF,OAAO,mCAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9E,CAAC;gBACD,4BAA4B;gBAC5B,OAAO,IAAI,CAAC,UAAU,CAAC;YACzB,CAAC;YAED,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC,aAAa,EAAE,CAAC;YAChE,MAAM,WAAW,GAAG,YAAY,GAAG,CAAC,CAAC;YAErC,MAAM,MAAM,GAAG;gBACb,CAAC,WAAW,KAAK,EAAE,CAAC,GAAG,IAAI;gBAC3B,CAAC,WAAW,KAAK,EAAE,CAAC,GAAG,IAAI;gBAC3B,CAAC,WAAW,KAAK,CAAC,CAAC,GAAG,IAAI;gBAC1B,WAAW,GAAG,IAAI;aACnB,CAAC;YAEF,OAAO,mCAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9E,CAAC;aAAM,CAAC;YACN,iCAAiC;YACjC,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAuB;QAC3B,IAAI,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;QAClF,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QACjD,IAAI,eAAe,GAAG,eAAe,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,mCAAmC,eAAe,EAAE,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,OAAO,GAAqB,EAAE,CAAC;QACrC,MAAM,SAAS,GAAG,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAC7D,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,eAAe,CAAC,CAAC;QAErD,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;QAEvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,MAAM,gBAAgB,GAAG,cAAc,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;YAE3D,MAAM,MAAM,GAAG;gBACb,CAAC,gBAAgB,KAAK,EAAE,CAAC,GAAG,IAAI;gBAChC,CAAC,gBAAgB,KAAK,EAAE,CAAC,GAAG,IAAI;gBAChC,CAAC,gBAAgB,KAAK,CAAC,CAAC,GAAG,IAAI;gBAC/B,gBAAgB,GAAG,IAAI;aACxB,CAAC;YAEF,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,KAAqB;QAC5B,2BAA2B;QAC3B,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,+DAA+D;QAC/D,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,KAAqB;QAC9B,2BAA2B;QAC3B,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,+BAA+B;QAC/B,4CAA4C;QAC5C,2DAA2D;QAC3D,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,YAAY;YACpD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,KAAqB;QAChC,OAAO,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,eAAe;QAKb,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,IAAI,SAAS,GAA2C,KAAK,CAAC;QAE9D,yCAAyC;QACzC,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEvC,IAAI,cAAc,KAAK,mBAAmB,CAAC,MAAM,EAAE,CAAC;YAClD,SAAS,GAAG,MAAM,CAAC;YACnB,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC3D,eAAe,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YACxD,eAAe,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACnE,CAAC;QAED,+BAA+B;QAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChD,IAAI,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YACrC,IAAI,SAAS,KAAK,KAAK;gBAAE,SAAS,GAAG,QAAQ,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC3C,eAAe,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACxD,CAAC;QAED,gCAAgC;QAChC,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC;YACnD,IAAI,SAAS,KAAK,KAAK;gBAAE,SAAS,GAAG,QAAQ,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACzC,eAAe,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QACzD,CAAC;QAED,qCAAqC;QACrC,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YACjC,MAAM,gBAAgB,GAAG;gBACvB,WAAW,EAAM,gBAAgB;gBACjC,YAAY,EAAK,sBAAsB;gBACvC,eAAe,EAAE,gBAAgB;gBACjC,gBAAgB,CAAC,uBAAuB;aACzC,CAAC;YAEF,IAAI,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC1C,OAAO,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;gBAC1D,eAAe,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACzC,eAAe,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,cAAc;QAgBZ,MAAM,IAAI,GAAQ;YAChB,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE;YACnB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,cAAc,EAAE,IAAI,CAAC,QAAQ,EAAE;YAC/B,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;YAC1C,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;YACtC,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,QAAQ,EAAE;YACnD,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,QAAQ,EAAE;YACrD,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;SAC1B,CAAC;QAEF,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACvC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAE3C,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC;gBAClC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC,QAAQ,EAAE,CAAC;YAChE,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC;gBAClC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC,QAAQ,EAAE,CAAC;gBACvD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,CAAC;YACvD,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAsB;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,KAAK,KAAK,CAAC,MAAM,CAAC,cAAc;YAC1D,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC;IAChE,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,IAAI,CAAC,cAAc,EAAE;YACxB,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,OAAO,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,IAAY;QAC7B,IAAI,CAAC;YACH,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC9B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,KAAa;QACxB,IAAI,CAAC;YACH,wBAAwB;YACxB,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxB,OAAO,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACxC,CAAC;YAED,iEAAiE;YACjE,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,YAAY,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;YAC5C,OAAO,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QACpD,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAvnBD,wCAunBC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\network-segment.value-object.ts"], "sourcesContent": ["import { BaseValueObject } from '../../../../../shared-kernel/value-objects/base-value-object';\r\nimport { IPAddress, IPAddressType } from './ip-address.value-object';\r\n\r\n/**\r\n * Network Segment Type\r\n */\r\nexport enum NetworkSegmentType {\r\n  IPv4 = 'ipv4',\r\n  IPv6 = 'ipv6',\r\n}\r\n\r\n/**\r\n * Network Segment Classification\r\n */\r\nexport enum NetworkSegmentClass {\r\n  PUBLIC = 'public',\r\n  PRIVATE = 'private',\r\n  RESERVED = 'reserved',\r\n  MULTICAST = 'multicast',\r\n  LINK_LOCAL = 'link_local',\r\n  LOOPBACK = 'loopback',\r\n}\r\n\r\n/**\r\n * Network Segment Properties\r\n */\r\nexport interface NetworkSegmentProps {\r\n  /** Network address (e.g., \"***********\") */\r\n  networkAddress: string;\r\n  /** Prefix length (e.g., 24 for /24) */\r\n  prefixLength: number;\r\n}\r\n\r\n/**\r\n * Network Segment Value Object\r\n * \r\n * Represents a network segment using CIDR notation with comprehensive\r\n * network analysis and subnet calculation capabilities.\r\n * \r\n * Key features:\r\n * - CIDR notation validation and parsing\r\n * - IPv4 and IPv6 support\r\n * - Subnet calculations and network analysis\r\n * - IP address membership testing\r\n * - Network classification and security analysis\r\n * - Subnet splitting and aggregation utilities\r\n */\r\nexport class NetworkSegment extends BaseValueObject<NetworkSegmentProps> {\r\n  private readonly _networkIP: IPAddress;\r\n  private readonly _type: NetworkSegmentType;\r\n\r\n  constructor(props: NetworkSegmentProps) {\r\n    super(props);\r\n  }\r\n\r\n  protected initializeProperties(): void {\r\n    (this as any)._networkIP = IPAddress.fromString(this._value.networkAddress);\r\n    (this as any)._type = (this as any)._networkIP.isIPv4() ? NetworkSegmentType.IPv4 : NetworkSegmentType.IPv6;\r\n    this.validateNetworkAddress();\r\n  }\r\n\r\n  protected validate(): void {\r\n    // Validate network address\r\n    if (!this._value.networkAddress || this._value.networkAddress.trim().length === 0) {\r\n      throw new Error('Network address cannot be empty');\r\n    }\r\n\r\n    // Validate prefix length\r\n    if (!Number.isInteger(this._value.prefixLength)) {\r\n      throw new Error('Prefix length must be an integer');\r\n    }\r\n\r\n    // Determine IP type for proper validation\r\n    const isIPv4 = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(this._value.networkAddress);\r\n    const maxPrefixLength = isIPv4 ? 32 : 128;\r\n    \r\n    if (this._value.prefixLength < 0 || this._value.prefixLength > maxPrefixLength) {\r\n      throw new Error(`Prefix length must be between 0 and ${maxPrefixLength}`);\r\n    }\r\n  }\r\n\r\n  protected validateNetworkAddress(): void {\r\n    // This is called after initializeProperties\r\n    const maxPrefixLength = this._networkIP.isIPv4() ? 32 : 128;\r\n    if (this._value.prefixLength > maxPrefixLength) {\r\n      throw new Error(`Prefix length must be between 0 and ${maxPrefixLength}`);\r\n    }\r\n\r\n    // Validate that the network address is actually a network address\r\n    if (this._networkIP.isIPv4()) {\r\n      this.validateIPv4NetworkAddress();\r\n    } else {\r\n      this.validateIPv6NetworkAddress();\r\n    }\r\n  }\r\n\r\n  private validateIPv4NetworkAddress(): void {\r\n    const networkInt = this._networkIP.toIPv4Integer();\r\n    const mask = (0xFFFFFFFF << (32 - this._value.prefixLength)) >>> 0;\r\n    const expectedNetworkInt = networkInt & mask;\r\n\r\n    if (networkInt !== expectedNetworkInt) {\r\n      const expectedOctets = [\r\n        (expectedNetworkInt >>> 24) & 0xFF,\r\n        (expectedNetworkInt >>> 16) & 0xFF,\r\n        (expectedNetworkInt >>> 8) & 0xFF,\r\n        expectedNetworkInt & 0xFF,\r\n      ];\r\n      const expectedAddress = expectedOctets.join('.');\r\n      throw new Error(`Invalid network address. Expected ${expectedAddress}/${this._value.prefixLength}`);\r\n    }\r\n  }\r\n\r\n  private validateIPv6NetworkAddress(): void {\r\n    // Simplified IPv6 network address validation\r\n    // In a full implementation, this would perform proper IPv6 network address validation\r\n    if (this._value.prefixLength % 4 !== 0) {\r\n      // For simplicity, we'll only validate on nibble boundaries\r\n      return;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create network segment from CIDR string\r\n   */\r\n  static fromCIDR(cidr: string): NetworkSegment {\r\n    const parts = cidr.trim().split('/');\r\n    if (parts.length !== 2) {\r\n      throw new Error('CIDR notation must be in format \"address/prefix\"');\r\n    }\r\n\r\n    const networkAddress = parts[0];\r\n    const prefixLength = parseInt(parts[1], 10);\r\n\r\n    if (isNaN(prefixLength)) {\r\n      throw new Error('Invalid prefix length in CIDR notation');\r\n    }\r\n\r\n    return new NetworkSegment({ networkAddress, prefixLength });\r\n  }\r\n\r\n  /**\r\n   * Create network segment from network address and prefix\r\n   */\r\n  static create(networkAddress: string, prefixLength: number): NetworkSegment {\r\n    return new NetworkSegment({ networkAddress, prefixLength });\r\n  }\r\n\r\n  /**\r\n   * Create common private network segments\r\n   */\r\n  static privateClassA(): NetworkSegment {\r\n    return NetworkSegment.fromCIDR('10.0.0.0/8');\r\n  }\r\n\r\n  static privateClassB(): NetworkSegment {\r\n    return NetworkSegment.fromCIDR('**********/12');\r\n  }\r\n\r\n  static privateClassC(): NetworkSegment {\r\n    return NetworkSegment.fromCIDR('***********/16');\r\n  }\r\n\r\n  static localhost(): NetworkSegment {\r\n    return NetworkSegment.fromCIDR('*********/8');\r\n  }\r\n\r\n  /**\r\n   * Get network address\r\n   */\r\n  get networkAddress(): string {\r\n    return this._value.networkAddress;\r\n  }\r\n\r\n  /**\r\n   * Get prefix length\r\n   */\r\n  get prefixLength(): number {\r\n    return this._value.prefixLength;\r\n  }\r\n\r\n  /**\r\n   * Get network type\r\n   */\r\n  get type(): NetworkSegmentType {\r\n    return this._type;\r\n  }\r\n\r\n  /**\r\n   * Check if this is an IPv4 network\r\n   */\r\n  isIPv4(): boolean {\r\n    return this._type === NetworkSegmentType.IPv4;\r\n  }\r\n\r\n  /**\r\n   * Check if this is an IPv6 network\r\n   */\r\n  isIPv6(): boolean {\r\n    return this._type === NetworkSegmentType.IPv6;\r\n  }\r\n\r\n  /**\r\n   * Get CIDR notation string\r\n   */\r\n  toCIDR(): string {\r\n    return `${this._value.networkAddress}/${this._value.prefixLength}`;\r\n  }\r\n\r\n  /**\r\n   * Check if an IP address belongs to this network segment\r\n   */\r\n  contains(ipAddress: IPAddress | string): boolean {\r\n    const ip = typeof ipAddress === 'string' ? IPAddress.fromString(ipAddress) : ipAddress;\r\n    \r\n    // Type compatibility check\r\n    if ((this.isIPv4() && !ip.isIPv4()) || (this.isIPv6() && !ip.isIPv6())) {\r\n      return false;\r\n    }\r\n\r\n    return ip.isInNetwork(this._value.networkAddress, this._value.prefixLength);\r\n  }\r\n\r\n  /**\r\n   * Get network classification\r\n   */\r\n  classify(): NetworkSegmentClass {\r\n    const networkClass = this._networkIP.classify();\r\n    \r\n    switch (networkClass) {\r\n      case 'public':\r\n        return NetworkSegmentClass.PUBLIC;\r\n      case 'private':\r\n        return NetworkSegmentClass.PRIVATE;\r\n      case 'reserved':\r\n        return NetworkSegmentClass.RESERVED;\r\n      case 'multicast':\r\n        return NetworkSegmentClass.MULTICAST;\r\n      case 'link_local':\r\n        return NetworkSegmentClass.LINK_LOCAL;\r\n      case 'loopback':\r\n        return NetworkSegmentClass.LOOPBACK;\r\n      default:\r\n        return NetworkSegmentClass.RESERVED;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if network is private\r\n   */\r\n  isPrivate(): boolean {\r\n    return this.classify() === NetworkSegmentClass.PRIVATE;\r\n  }\r\n\r\n  /**\r\n   * Check if network is public\r\n   */\r\n  isPublic(): boolean {\r\n    return this.classify() === NetworkSegmentClass.PUBLIC;\r\n  }\r\n\r\n  /**\r\n   * Get total number of addresses in this network\r\n   */\r\n  getTotalAddresses(): bigint {\r\n    if (this.isIPv4()) {\r\n      const hostBits = 32 - this._value.prefixLength;\r\n      return BigInt(Math.pow(2, hostBits));\r\n    } else {\r\n      const hostBits = 128 - this._value.prefixLength;\r\n      return BigInt(2) ** BigInt(hostBits);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get number of usable host addresses (excluding network and broadcast for IPv4)\r\n   */\r\n  getUsableAddresses(): bigint {\r\n    const total = this.getTotalAddresses();\r\n    \r\n    if (this.isIPv4() && this._value.prefixLength < 31) {\r\n      // Subtract network and broadcast addresses for IPv4\r\n      return total - BigInt(2);\r\n    }\r\n    \r\n    return total;\r\n  }\r\n\r\n  /**\r\n   * Get subnet mask for IPv4 networks\r\n   */\r\n  getSubnetMask(): string {\r\n    if (!this.isIPv4()) {\r\n      throw new Error('Subnet mask is only applicable to IPv4 networks');\r\n    }\r\n\r\n    // Handle special case for /0 (default route)\r\n    if (this._value.prefixLength === 0) {\r\n      return '0.0.0.0';\r\n    }\r\n\r\n    const mask = (0xFFFFFFFF << (32 - this._value.prefixLength)) >>> 0;\r\n    const octets = [\r\n      (mask >>> 24) & 0xFF,\r\n      (mask >>> 16) & 0xFF,\r\n      (mask >>> 8) & 0xFF,\r\n      mask & 0xFF,\r\n    ];\r\n\r\n    return octets.join('.');\r\n  }\r\n\r\n  /**\r\n   * Get wildcard mask for IPv4 networks\r\n   */\r\n  getWildcardMask(): string {\r\n    if (!this.isIPv4()) {\r\n      throw new Error('Wildcard mask is only applicable to IPv4 networks');\r\n    }\r\n\r\n    const wildcardMask = (0xFFFFFFFF >>> this._value.prefixLength);\r\n    const octets = [\r\n      (wildcardMask >>> 24) & 0xFF,\r\n      (wildcardMask >>> 16) & 0xFF,\r\n      (wildcardMask >>> 8) & 0xFF,\r\n      wildcardMask & 0xFF,\r\n    ];\r\n\r\n    return octets.join('.');\r\n  }\r\n\r\n  /**\r\n   * Get broadcast address for IPv4 networks\r\n   */\r\n  getBroadcastAddress(): IPAddress {\r\n    if (!this.isIPv4()) {\r\n      throw new Error('Broadcast address is only applicable to IPv4 networks');\r\n    }\r\n\r\n    const networkInt = this._networkIP.toIPv4Integer();\r\n    const hostMask = (0xFFFFFFFF >>> this._value.prefixLength);\r\n    const broadcastInt = networkInt | hostMask;\r\n\r\n    const octets = [\r\n      (broadcastInt >>> 24) & 0xFF,\r\n      (broadcastInt >>> 16) & 0xFF,\r\n      (broadcastInt >>> 8) & 0xFF,\r\n      broadcastInt & 0xFF,\r\n    ];\r\n\r\n    return IPAddress.fromIPv4Octets(octets[0], octets[1], octets[2], octets[3]);\r\n  }\r\n\r\n  /**\r\n   * Get first usable host address\r\n   */\r\n  getFirstHostAddress(): IPAddress {\r\n    if (this.isIPv4()) {\r\n      if (this._value.prefixLength >= 31) {\r\n        // For /31 and /32 networks, the network address is usable\r\n        return this._networkIP;\r\n      }\r\n      \r\n      const networkInt = this._networkIP.toIPv4Integer();\r\n      const firstHostInt = networkInt + 1;\r\n      \r\n      const octets = [\r\n        (firstHostInt >>> 24) & 0xFF,\r\n        (firstHostInt >>> 16) & 0xFF,\r\n        (firstHostInt >>> 8) & 0xFF,\r\n        firstHostInt & 0xFF,\r\n      ];\r\n      \r\n      return IPAddress.fromIPv4Octets(octets[0], octets[1], octets[2], octets[3]);\r\n    } else {\r\n      // For IPv6, the network address is typically not used for hosts\r\n      // This is a simplified implementation\r\n      return this._networkIP;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get last usable host address\r\n   */\r\n  getLastHostAddress(): IPAddress {\r\n    if (this.isIPv4()) {\r\n      if (this._value.prefixLength >= 31) {\r\n        // For /31 networks, calculate the second address\r\n        if (this._value.prefixLength === 31) {\r\n          const networkInt = this._networkIP.toIPv4Integer();\r\n          const lastHostInt = networkInt + 1;\r\n          \r\n          const octets = [\r\n            (lastHostInt >>> 24) & 0xFF,\r\n            (lastHostInt >>> 16) & 0xFF,\r\n            (lastHostInt >>> 8) & 0xFF,\r\n            lastHostInt & 0xFF,\r\n          ];\r\n          \r\n          return IPAddress.fromIPv4Octets(octets[0], octets[1], octets[2], octets[3]);\r\n        }\r\n        // For /32, only one address\r\n        return this._networkIP;\r\n      }\r\n      \r\n      const broadcastInt = this.getBroadcastAddress().toIPv4Integer();\r\n      const lastHostInt = broadcastInt - 1;\r\n      \r\n      const octets = [\r\n        (lastHostInt >>> 24) & 0xFF,\r\n        (lastHostInt >>> 16) & 0xFF,\r\n        (lastHostInt >>> 8) & 0xFF,\r\n        lastHostInt & 0xFF,\r\n      ];\r\n      \r\n      return IPAddress.fromIPv4Octets(octets[0], octets[1], octets[2], octets[3]);\r\n    } else {\r\n      // Simplified IPv6 implementation\r\n      throw new Error('IPv6 last host address calculation not implemented');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Split network into smaller subnets\r\n   */\r\n  split(newPrefixLength: number): NetworkSegment[] {\r\n    if (newPrefixLength <= this._value.prefixLength) {\r\n      throw new Error('New prefix length must be greater than current prefix length');\r\n    }\r\n\r\n    const maxPrefixLength = this.isIPv4() ? 32 : 128;\r\n    if (newPrefixLength > maxPrefixLength) {\r\n      throw new Error(`New prefix length cannot exceed ${maxPrefixLength}`);\r\n    }\r\n\r\n    if (!this.isIPv4()) {\r\n      throw new Error('IPv6 subnet splitting not implemented');\r\n    }\r\n\r\n    const subnets: NetworkSegment[] = [];\r\n    const bitsToAdd = newPrefixLength - this._value.prefixLength;\r\n    const numberOfSubnets = Math.pow(2, bitsToAdd);\r\n    const subnetSize = Math.pow(2, 32 - newPrefixLength);\r\n\r\n    const baseNetworkInt = this._networkIP.toIPv4Integer();\r\n\r\n    for (let i = 0; i < numberOfSubnets; i++) {\r\n      const subnetNetworkInt = baseNetworkInt + (i * subnetSize);\r\n      \r\n      const octets = [\r\n        (subnetNetworkInt >>> 24) & 0xFF,\r\n        (subnetNetworkInt >>> 16) & 0xFF,\r\n        (subnetNetworkInt >>> 8) & 0xFF,\r\n        subnetNetworkInt & 0xFF,\r\n      ];\r\n      \r\n      const subnetAddress = octets.join('.');\r\n      subnets.push(NetworkSegment.create(subnetAddress, newPrefixLength));\r\n    }\r\n\r\n    return subnets;\r\n  }\r\n\r\n  /**\r\n   * Check if this network overlaps with another network\r\n   */\r\n  overlaps(other: NetworkSegment): boolean {\r\n    // Type compatibility check\r\n    if (this._type !== other._type) {\r\n      return false;\r\n    }\r\n\r\n    // Check if either network contains the other's network address\r\n    return this.contains(other._networkIP) || other.contains(this._networkIP);\r\n  }\r\n\r\n  /**\r\n   * Check if this network is a subnet of another network\r\n   */\r\n  isSubnetOf(other: NetworkSegment): boolean {\r\n    // Type compatibility check\r\n    if (this._type !== other._type) {\r\n      return false;\r\n    }\r\n\r\n    // This network is a subnet if:\r\n    // 1. It has a longer prefix (more specific)\r\n    // 2. Its network address is contained in the other network\r\n    return this._value.prefixLength > other._value.prefixLength && \r\n           other.contains(this._networkIP);\r\n  }\r\n\r\n  /**\r\n   * Check if this network is a supernet of another network\r\n   */\r\n  isSupernetOf(other: NetworkSegment): boolean {\r\n    return other.isSubnetOf(this);\r\n  }\r\n\r\n  /**\r\n   * Get network security risk assessment\r\n   */\r\n  getSecurityRisk(): {\r\n    riskLevel: 'low' | 'medium' | 'high' | 'critical';\r\n    reasons: string[];\r\n    recommendations: string[];\r\n  } {\r\n    const reasons: string[] = [];\r\n    const recommendations: string[] = [];\r\n    let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';\r\n\r\n    // Assess based on network classification\r\n    const classification = this.classify();\r\n    \r\n    if (classification === NetworkSegmentClass.PUBLIC) {\r\n      riskLevel = 'high';\r\n      reasons.push('Public network segment exposed to internet');\r\n      recommendations.push('Implement strict firewall rules');\r\n      recommendations.push('Monitor for unauthorized access attempts');\r\n    }\r\n\r\n    // Assess based on network size\r\n    const totalAddresses = this.getTotalAddresses();\r\n    if (totalAddresses > BigInt(1000000)) {\r\n      if (riskLevel === 'low') riskLevel = 'medium';\r\n      reasons.push('Very large network segment');\r\n      recommendations.push('Consider network segmentation');\r\n    }\r\n\r\n    // Assess based on prefix length\r\n    if (this.isIPv4() && this._value.prefixLength < 16) {\r\n      if (riskLevel === 'low') riskLevel = 'medium';\r\n      reasons.push('Very broad network range');\r\n      recommendations.push('Implement network segmentation');\r\n    }\r\n\r\n    // Check for common vulnerable ranges\r\n    if (this.isIPv4()) {\r\n      const networkStr = this.toCIDR();\r\n      const vulnerableRanges = [\r\n        '0.0.0.0/0',     // Default route\r\n        '10.0.0.0/8',    // Large private range\r\n        '**********/12', // Private range\r\n        '***********/16' // Common private range\r\n      ];\r\n\r\n      if (vulnerableRanges.includes(networkStr)) {\r\n        reasons.push('Common network range that may be targeted');\r\n        recommendations.push('Implement additional monitoring');\r\n      }\r\n    }\r\n\r\n    if (reasons.length === 0) {\r\n      reasons.push('Standard network segment');\r\n      recommendations.push('Maintain regular security monitoring');\r\n    }\r\n\r\n    return { riskLevel, reasons, recommendations };\r\n  }\r\n\r\n  /**\r\n   * Get network information summary\r\n   */\r\n  getNetworkInfo(): {\r\n    cidr: string;\r\n    type: string;\r\n    classification: string;\r\n    networkAddress: string;\r\n    prefixLength: number;\r\n    totalAddresses: string;\r\n    usableAddresses: string;\r\n    subnetMask?: string;\r\n    wildcardMask?: string;\r\n    broadcastAddress?: string;\r\n    firstHost?: string;\r\n    lastHost?: string;\r\n    isPrivate: boolean;\r\n    isPublic: boolean;\r\n  } {\r\n    const info: any = {\r\n      cidr: this.toCIDR(),\r\n      type: this._type,\r\n      classification: this.classify(),\r\n      networkAddress: this._value.networkAddress,\r\n      prefixLength: this._value.prefixLength,\r\n      totalAddresses: this.getTotalAddresses().toString(),\r\n      usableAddresses: this.getUsableAddresses().toString(),\r\n      isPrivate: this.isPrivate(),\r\n      isPublic: this.isPublic(),\r\n    };\r\n\r\n    if (this.isIPv4()) {\r\n      info.subnetMask = this.getSubnetMask();\r\n      info.wildcardMask = this.getWildcardMask();\r\n      \r\n      if (this._value.prefixLength < 32) {\r\n        info.broadcastAddress = this.getBroadcastAddress().toString();\r\n      }\r\n      \r\n      if (this._value.prefixLength < 31) {\r\n        info.firstHost = this.getFirstHostAddress().toString();\r\n        info.lastHost = this.getLastHostAddress().toString();\r\n      }\r\n    }\r\n\r\n    return info;\r\n  }\r\n\r\n  /**\r\n   * Compare network segments for equality\r\n   */\r\n  public equals(other?: NetworkSegment): boolean {\r\n    if (!other) {\r\n      return false;\r\n    }\r\n\r\n    if (this === other) {\r\n      return true;\r\n    }\r\n\r\n    return this._value.networkAddress === other._value.networkAddress &&\r\n           this._value.prefixLength === other._value.prefixLength;\r\n  }\r\n\r\n  /**\r\n   * Convert to string representation\r\n   */\r\n  public toString(): string {\r\n    return this.toCIDR();\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...this.getNetworkInfo(),\r\n      securityRisk: this.getSecurityRisk(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create NetworkSegment from JSON\r\n   */\r\n  static fromJSON(json: Record<string, any>): NetworkSegment {\r\n    return NetworkSegment.create(json.networkAddress, json.prefixLength);\r\n  }\r\n\r\n  /**\r\n   * Validate CIDR format without creating instance\r\n   */\r\n  static isValidCIDR(cidr: string): boolean {\r\n    try {\r\n      NetworkSegment.fromCIDR(cidr);\r\n      return true;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Parse network segment from various formats\r\n   */\r\n  static parse(input: string): NetworkSegment | null {\r\n    try {\r\n      // Try CIDR format first\r\n      if (input.includes('/')) {\r\n        return NetworkSegment.fromCIDR(input);\r\n      }\r\n\r\n      // Try to parse as single IP (assume /32 for IPv4, /128 for IPv6)\r\n      const ip = IPAddress.fromString(input);\r\n      const prefixLength = ip.isIPv4() ? 32 : 128;\r\n      return NetworkSegment.create(input, prefixLength);\r\n    } catch {\r\n      return null;\r\n    }\r\n  }\r\n}"], "version": 3}