{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting-analytics\\application\\services\\report-generation.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AACrD,qCAAiD;AACjD,2EAAsE;AACtE,uEAA6D;AAC7D,2FAAgF;AAChF,sFAAkF;AAClF,0FAAsF;AACtF,yEAAoE;AACpE,mEAA8D;AAE9D,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;IACvC,IAAI,OAAgC,CAAC;IACrC,IAAI,gBAAoC,CAAC;IACzC,IAAI,mBAAgD,CAAC;IACrD,IAAI,UAAsB,CAAC;IAC3B,IAAI,aAA4B,CAAC;IACjC,IAAI,YAA0B,CAAC;IAC/B,IAAI,sBAA8C,CAAC;IACnD,IAAI,mBAAwC,CAAC;IAE7C,MAAM,oBAAoB,GAAG;QAC3B,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;KAChB,CAAC;IAEF,MAAM,uBAAuB,GAAG;QAC9B,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;KACnB,CAAC;IAEF,MAAM,cAAc,GAAG;QACrB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACjB,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;QACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACjB,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB,CAAC;IAEF,MAAM,0BAA0B,GAAG;QACjC,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB,CAAC;IAEF,MAAM,uBAAuB,GAAG;QAC9B,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;KACxB,CAAC;IAEF,MAAM,UAAU,GAAoB;QAClC,EAAE,EAAE,YAAY;QAChB,IAAI,EAAE,aAAa;QACnB,IAAI,EAAE,uBAAuB;QAC7B,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,QAAQ;QAChB,aAAa,EAAE;YACb,WAAW,EAAE,CAAC,iBAAiB,CAAC;YAChC,SAAS,EAAE;gBACT,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,KAAK;aACb;YACD,OAAO,EAAE;gBACP,UAAU,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;aACjC;YACD,cAAc,EAAE;gBACd;oBACE,IAAI,EAAE,OAAO;oBACb,SAAS,EAAE,KAAK;oBAChB,KAAK,EAAE,6BAA6B;oBACpC,UAAU,EAAE,iBAAiB;oBAC7B,aAAa,EAAE,EAAE;iBAClB;aACF;SACF;QACD,qBAAqB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;QAC/E,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,iBAAiB,CAAC,CAAC;QAC9D,yBAAyB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACtE,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;KAChC,CAAC;IAEF,MAAM,aAAa,GAA6B;QAC9C,EAAE,EAAE,eAAe;QACnB,QAAQ,EAAE,YAAY;QACtB,MAAM,EAAE,SAAS;QACjB,WAAW,EAAE,QAAQ;QACrB,UAAU,EAAE,UAAU;QACtB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,SAAS,EAAE,KAAK;QAChB,WAAW,EAAE,KAAK;QAClB,QAAQ,EAAE,KAAK;KAChB,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,mDAAuB;gBACvB;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,sBAAM,CAAC;oBACnC,QAAQ,EAAE,oBAAoB;iBAC/B;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,yCAAe,CAAC;oBAC5C,QAAQ,EAAE,uBAAuB;iBAClC;gBACD;oBACE,OAAO,EAAE,oBAAU;oBACnB,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE,gBAAgB;iBAC3B;gBACD;oBACE,OAAO,EAAE,iDAAsB;oBAC/B,QAAQ,EAAE,0BAA0B;iBACrC;gBACD;oBACE,OAAO,EAAE,2CAAmB;oBAC5B,QAAQ,EAAE,uBAAuB;iBAClC;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAA0B,mDAAuB,CAAC,CAAC;QACvE,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAqB,IAAA,4BAAkB,EAAC,sBAAM,CAAC,CAAC,CAAC;QAC9E,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAA8B,IAAA,4BAAkB,EAAC,yCAAe,CAAC,CAAC,CAAC;QACnG,UAAU,GAAG,MAAM,CAAC,GAAG,CAAa,oBAAU,CAAC,CAAC;QAChD,aAAa,GAAG,MAAM,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;QACzD,YAAY,GAAG,MAAM,CAAC,GAAG,CAAe,4BAAY,CAAC,CAAC;QACtD,sBAAsB,GAAG,MAAM,CAAC,GAAG,CAAyB,iDAAsB,CAAC,CAAC;QACpF,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAsB,2CAAmB,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG,YAAY,CAAC;YAC9B,MAAM,UAAU,GAAG;gBACjB,SAAS,EAAE;oBACT,IAAI,EAAE,UAAmB;oBACzB,KAAK,EAAE,IAAI;iBACZ;gBACD,YAAY,EAAE,KAAK;aACpB,CAAC;YACF,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAC3D,uBAAuB,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAC9D,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAE9D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YAEzE,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACxD,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;aACxB,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC5D,MAAM,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAC1D,QAAQ;gBACR,MAAM,EAAE,SAAS;gBACjB,WAAW,EAAE,QAAQ;gBACrB,UAAU;gBACV,UAAU,EAAE,MAAM;aACnB,CAAC,CAAC;YACH,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;YACzE,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,MAAM,CAAC,gBAAgB,CAAC;gBACtB,WAAW,EAAE,aAAa,CAAC,EAAE;gBAC7B,WAAW,EAAE,QAAQ;gBACrB,UAAU;aACX,CAAC,CACH,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,QAAQ,GAAG,cAAc,CAAC;YAChC,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAErD,MAAM,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;iBACtD,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,QAAQ,GAAG,YAAY,CAAC;YAC9B,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,cAAc,GAAG,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;YAE1D,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAE/D,MAAM,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;iBACtD,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,MAAM,QAAQ,GAAG,YAAY,CAAC;YAC9B,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,aAAa,GAAG;gBACpB,GAAG,UAAU;gBACb,qBAAqB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC;oBAC/C,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,CAAC,yBAAyB,CAAC;iBACpC,CAAC;aACH,CAAC;YAEF,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAE9D,MAAM,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;iBACtD,OAAO,CAAC,OAAO,CAAC,0DAA0D,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,WAAW,GAAG,eAAe,CAAC;YACpC,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAEjE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAEvD,MAAM,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;gBAC1B,SAAS,EAAE,CAAC,QAAQ,CAAC;aACtB,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,WAAW,GAAG,cAAc,CAAC;YACnC,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,WAAW,GAAG,eAAe,CAAC;YACpC,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,gBAAgB,GAAG;gBACvB,GAAG,aAAa;gBAChB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,YAAY;aACvB,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,iBAAiB,CAAC,gBAAmC,CAAC,CAAC;YAC3F,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAEjE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAElE,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAC/D,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;YAC1E,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;YAC5E,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,kBAAkB,EAClB,WAAW,EACX;gBACE,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;gBACnC,MAAM,EAAE,cAAc;aACvB,CACF,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,WAAW,GAAG,cAAc,CAAC;YACnC,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE5D,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;iBACvD,OAAO,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,WAAW,GAAG,eAAe,CAAC;YACpC,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,kBAAkB,GAAG;gBACzB,GAAG,aAAa;gBAChB,SAAS,EAAE,KAAK;aACjB,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,iBAAiB,CAAC,kBAAqC,CAAC,CAAC;YAE7F,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;iBACvD,OAAO,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,SAAS,GAAG;gBAChB,GAAG,aAAa;gBAChB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;gBAChB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;gBACzB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;gBACnB,UAAU,EAAE,EAAE;aACR,CAAC;YAET,MAAM,QAAQ,GAAG;gBACf,eAAe,EAAE;oBACf,IAAI,EAAE;wBACJ,EAAE,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;wBAC3C,EAAE,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE;qBACrD;iBACF;aACF,CAAC;YAEF,MAAM,iBAAiB,GAAG;gBACxB,YAAY,EAAE,CAAC;gBACf,UAAU,EAAE,CAAC;gBACb,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE;oBACN;wBACE,EAAE,EAAE,uBAAuB;wBAC3B,KAAK,EAAE,iBAAiB;wBACxB,OAAO,EAAE,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC;wBACrC,IAAI,EAAE;4BACJ,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC;4BACnB,CAAC,CAAC,EAAE,UAAU,EAAE,WAAW,CAAC;yBAC7B;qBACF;iBACF;gBACD,OAAO,EAAE,EAAE;aACZ,CAAC;YAEF,MAAM,kBAAkB,GAAG;gBACzB,MAAM,EAAE;oBACN;wBACE,EAAE,EAAE,WAAW;wBACf,KAAK,EAAE,6BAA6B;wBACpC,IAAI,EAAE,KAAK;wBACX,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;qBACrE;iBACF;aACF,CAAC;YAEF,0BAA0B,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;YACrF,uBAAuB,CAAC,YAAY,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;YAC9E,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAC1D,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAExD,0BAA0B;YAC1B,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,aAAa,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAElE,MAAO,OAAe,CAAC,sBAAsB,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YAEjF,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC3C,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC;YAC7E,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC;YAC7E,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,EAAE,EAAE,2BAA2B,CAAC,CAAC;YACvF,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAC;YAC9E,MAAM,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACnE,iBAAiB,EACjB,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,aAAa,CAAC,CAClD,CAAC;YACF,MAAM,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAChE,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC9C,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,SAAS,GAAG;gBAChB,GAAG,aAAa;gBAChB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;gBAChB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;gBACzB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACf,UAAU,EAAE,EAAE;aACR,CAAC;YAET,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YACnD,0BAA0B,CAAC,aAAa,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAClE,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAC1D,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAExD,MAAO,OAAe,CAAC,sBAAsB,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YAEjF,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC3C,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAC1D,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,CAAC,EACD,QAAQ,CACT,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,IAAI,GAAG;gBACX,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE;gBAC9C,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE;gBAC/C,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE;aACnD,CAAC;YACF,MAAM,OAAO,GAAG,CAAC,UAAU,CAAC,CAAC;YAE7B,MAAM,MAAM,GAAI,OAAe,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBACxB,KAAK,EAAE,MAAM;gBACb,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE;oBACL,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE;oBAC9C,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE;iBAChD;aACF,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBACxB,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE;oBACL,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE;iBACnD;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG;gBACX,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;gBACpC,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;gBACpC,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,EAAE;aACzC,CAAC;YACF,MAAM,YAAY,GAAG;gBACnB,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,aAAa,EAAE;gBAC9D,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE;gBAC1D,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE;aAC3D,CAAC;YAEF,MAAM,MAAM,GAAI,OAAe,CAAC,aAAa,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAElE,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBACxB,EAAE,EAAE,aAAa;gBACjB,KAAK,EAAE,aAAa;gBACpB,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBACxB,EAAE,EAAE,UAAU;gBACd,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,iBAAiB;gBACxB,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBACxB,EAAE,EAAE,UAAU;gBACd,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,GAAG;gBACV,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,SAAS,GAAG;gBAChB,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,YAAY;gBACnB,UAAU,EAAE,iBAAiB;gBAC7B,aAAa,EAAE;oBACb,MAAM,EAAE,CAAC,SAAS,CAAC;iBACpB;aACF,CAAC;YAEF,MAAM,aAAa,GAAG;gBACpB,MAAM,EAAE;oBACN;wBACE,EAAE,EAAE,uBAAuB;wBAC3B,KAAK,EAAE,iBAAiB;wBACxB,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;wBAC9B,IAAI,EAAE;4BACJ,CAAC,MAAM,EAAE,CAAC,CAAC;4BACX,CAAC,UAAU,EAAE,CAAC,CAAC;yBAChB;qBACF;iBACF;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAO,OAAe,CAAC,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YAE9E,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,MAAM,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;gBAC5B,QAAQ,EAAE,CAAC;wBACT,KAAK,EAAE,YAAY;wBACnB,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;wBACZ,eAAe,EAAE,CAAC,SAAS,CAAC;qBAC7B,CAAC;aACH,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting-analytics\\application\\services\\report-generation.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository, DataSource } from 'typeorm';\r\nimport { ReportGenerationService } from './report-generation.service';\r\nimport { Report } from '../../domain/entities/report.entity';\r\nimport { ReportExecution } from '../../domain/entities/report-execution.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { DataAggregationService } from './data-aggregation.service';\r\nimport { ReportExportService } from './report-export.service';\r\n\r\ndescribe('ReportGenerationService', () => {\r\n  let service: ReportGenerationService;\r\n  let reportRepository: Repository<Report>;\r\n  let executionRepository: Repository<ReportExecution>;\r\n  let dataSource: DataSource;\r\n  let loggerService: LoggerService;\r\n  let auditService: AuditService;\r\n  let dataAggregationService: DataAggregationService;\r\n  let reportExportService: ReportExportService;\r\n\r\n  const mockReportRepository = {\r\n    findOne: jest.fn(),\r\n    save: jest.fn(),\r\n  };\r\n\r\n  const mockExecutionRepository = {\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    findOne: jest.fn(),\r\n  };\r\n\r\n  const mockDataSource = {\r\n    query: jest.fn(),\r\n  };\r\n\r\n  const mockLoggerService = {\r\n    debug: jest.fn(),\r\n    log: jest.fn(),\r\n    warn: jest.fn(),\r\n    error: jest.fn(),\r\n  };\r\n\r\n  const mockAuditService = {\r\n    logUserAction: jest.fn(),\r\n  };\r\n\r\n  const mockDataAggregationService = {\r\n    aggregateData: jest.fn(),\r\n  };\r\n\r\n  const mockReportExportService = {\r\n    exportReport: jest.fn(),\r\n  };\r\n\r\n  const mockReport: Partial<Report> = {\r\n    id: 'report-123',\r\n    name: 'Test Report',\r\n    type: 'vulnerability_summary',\r\n    category: 'security',\r\n    status: 'active',\r\n    configuration: {\r\n      dataSources: ['vulnerabilities'],\r\n      timeRange: {\r\n        type: 'relative',\r\n        value: '30d',\r\n      },\r\n      filters: {\r\n        severities: ['high', 'critical'],\r\n      },\r\n      visualizations: [\r\n        {\r\n          type: 'chart',\r\n          chartType: 'bar',\r\n          title: 'Vulnerabilities by Severity',\r\n          dataSource: 'vulnerabilities',\r\n          configuration: {},\r\n        },\r\n      ],\r\n    },\r\n    validateConfiguration: jest.fn().mockReturnValue({ isValid: true, errors: [] }),\r\n    getDataSources: jest.fn().mockReturnValue(['vulnerabilities']),\r\n    getSupportedExportFormats: jest.fn().mockReturnValue(['pdf', 'excel']),\r\n    updateExecutionStats: jest.fn(),\r\n  };\r\n\r\n  const mockExecution: Partial<ReportExecution> = {\r\n    id: 'execution-123',\r\n    reportId: 'report-123',\r\n    status: 'pending',\r\n    triggerType: 'manual',\r\n    executedBy: 'user-123',\r\n    start: jest.fn(),\r\n    updateProgress: jest.fn(),\r\n    complete: jest.fn(),\r\n    fail: jest.fn(),\r\n    cancel: jest.fn(),\r\n    isRunning: false,\r\n    isCompleted: false,\r\n    isFailed: false,\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        ReportGenerationService,\r\n        {\r\n          provide: getRepositoryToken(Report),\r\n          useValue: mockReportRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(ReportExecution),\r\n          useValue: mockExecutionRepository,\r\n        },\r\n        {\r\n          provide: DataSource,\r\n          useValue: mockDataSource,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: mockLoggerService,\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: mockAuditService,\r\n        },\r\n        {\r\n          provide: DataAggregationService,\r\n          useValue: mockDataAggregationService,\r\n        },\r\n        {\r\n          provide: ReportExportService,\r\n          useValue: mockReportExportService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<ReportGenerationService>(ReportGenerationService);\r\n    reportRepository = module.get<Repository<Report>>(getRepositoryToken(Report));\r\n    executionRepository = module.get<Repository<ReportExecution>>(getRepositoryToken(ReportExecution));\r\n    dataSource = module.get<DataSource>(DataSource);\r\n    loggerService = module.get<LoggerService>(LoggerService);\r\n    auditService = module.get<AuditService>(AuditService);\r\n    dataAggregationService = module.get<DataAggregationService>(DataAggregationService);\r\n    reportExportService = module.get<ReportExportService>(ReportExportService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('executeReport', () => {\r\n    it('should start report execution successfully', async () => {\r\n      const reportId = 'report-123';\r\n      const parameters = {\r\n        timeRange: {\r\n          type: 'relative' as const,\r\n          value: '7d',\r\n        },\r\n        exportFormat: 'pdf',\r\n      };\r\n      const userId = 'user-123';\r\n\r\n      mockReportRepository.findOne.mockResolvedValue(mockReport);\r\n      mockExecutionRepository.create.mockReturnValue(mockExecution);\r\n      mockExecutionRepository.save.mockResolvedValue(mockExecution);\r\n\r\n      const result = await service.executeReport(reportId, parameters, userId);\r\n\r\n      expect(mockReportRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: reportId },\r\n      });\r\n      expect(mockReport.validateConfiguration).toHaveBeenCalled();\r\n      expect(mockExecutionRepository.create).toHaveBeenCalledWith({\r\n        reportId,\r\n        status: 'pending',\r\n        triggerType: 'manual',\r\n        parameters,\r\n        executedBy: userId,\r\n      });\r\n      expect(mockExecutionRepository.save).toHaveBeenCalledWith(mockExecution);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'execute',\r\n        'report',\r\n        reportId,\r\n        expect.objectContaining({\r\n          executionId: mockExecution.id,\r\n          triggerType: 'manual',\r\n          parameters,\r\n        }),\r\n      );\r\n      expect(result).toEqual(mockExecution);\r\n    });\r\n\r\n    it('should throw error when report not found', async () => {\r\n      const reportId = 'non-existent';\r\n      const userId = 'user-123';\r\n\r\n      mockReportRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(service.executeReport(reportId, {}, userId))\r\n        .rejects.toThrow('Report not found');\r\n    });\r\n\r\n    it('should throw error when report is not active', async () => {\r\n      const reportId = 'report-123';\r\n      const userId = 'user-123';\r\n      const inactiveReport = { ...mockReport, status: 'draft' };\r\n\r\n      mockReportRepository.findOne.mockResolvedValue(inactiveReport);\r\n\r\n      await expect(service.executeReport(reportId, {}, userId))\r\n        .rejects.toThrow('Report is not active');\r\n    });\r\n\r\n    it('should throw error when report configuration is invalid', async () => {\r\n      const reportId = 'report-123';\r\n      const userId = 'user-123';\r\n      const invalidReport = {\r\n        ...mockReport,\r\n        validateConfiguration: jest.fn().mockReturnValue({\r\n          isValid: false,\r\n          errors: ['Data source is required'],\r\n        }),\r\n      };\r\n\r\n      mockReportRepository.findOne.mockResolvedValue(invalidReport);\r\n\r\n      await expect(service.executeReport(reportId, {}, userId))\r\n        .rejects.toThrow('Report configuration is invalid: Data source is required');\r\n    });\r\n  });\r\n\r\n  describe('getExecution', () => {\r\n    it('should return execution when found', async () => {\r\n      const executionId = 'execution-123';\r\n      mockExecutionRepository.findOne.mockResolvedValue(mockExecution);\r\n\r\n      const result = await service.getExecution(executionId);\r\n\r\n      expect(mockExecutionRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: executionId },\r\n        relations: ['report'],\r\n      });\r\n      expect(result).toEqual(mockExecution);\r\n    });\r\n\r\n    it('should return null when execution not found', async () => {\r\n      const executionId = 'non-existent';\r\n      mockExecutionRepository.findOne.mockResolvedValue(null);\r\n\r\n      const result = await service.getExecution(executionId);\r\n\r\n      expect(result).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('cancelExecution', () => {\r\n    it('should cancel running execution successfully', async () => {\r\n      const executionId = 'execution-123';\r\n      const userId = 'user-123';\r\n      const runningExecution = {\r\n        ...mockExecution,\r\n        isRunning: true,\r\n        reportId: 'report-123',\r\n      };\r\n\r\n      jest.spyOn(service, 'getExecution').mockResolvedValue(runningExecution as ReportExecution);\r\n      mockExecutionRepository.save.mockResolvedValue(runningExecution);\r\n\r\n      const result = await service.cancelExecution(executionId, userId);\r\n\r\n      expect(service.getExecution).toHaveBeenCalledWith(executionId);\r\n      expect(runningExecution.cancel).toHaveBeenCalledWith('Cancelled by user');\r\n      expect(mockExecutionRepository.save).toHaveBeenCalledWith(runningExecution);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'cancel',\r\n        'report_execution',\r\n        executionId,\r\n        {\r\n          reportId: runningExecution.reportId,\r\n          reason: 'user_request',\r\n        },\r\n      );\r\n      expect(result).toEqual(runningExecution);\r\n    });\r\n\r\n    it('should throw error when execution not found', async () => {\r\n      const executionId = 'non-existent';\r\n      const userId = 'user-123';\r\n\r\n      jest.spyOn(service, 'getExecution').mockResolvedValue(null);\r\n\r\n      await expect(service.cancelExecution(executionId, userId))\r\n        .rejects.toThrow('Report execution not found');\r\n    });\r\n\r\n    it('should throw error when execution is not running', async () => {\r\n      const executionId = 'execution-123';\r\n      const userId = 'user-123';\r\n      const completedExecution = {\r\n        ...mockExecution,\r\n        isRunning: false,\r\n      };\r\n\r\n      jest.spyOn(service, 'getExecution').mockResolvedValue(completedExecution as ReportExecution);\r\n\r\n      await expect(service.cancelExecution(executionId, userId))\r\n        .rejects.toThrow('Report execution is not running');\r\n    });\r\n  });\r\n\r\n  describe('processReportExecution', () => {\r\n    it('should process execution successfully', async () => {\r\n      const execution = {\r\n        ...mockExecution,\r\n        start: jest.fn(),\r\n        updateProgress: jest.fn(),\r\n        complete: jest.fn(),\r\n        parameters: {},\r\n      } as any;\r\n\r\n      const mockData = {\r\n        vulnerabilities: {\r\n          data: [\r\n            { id: 1, severity: 'high', status: 'open' },\r\n            { id: 2, severity: 'critical', status: 'confirmed' },\r\n          ],\r\n        },\r\n      };\r\n\r\n      const mockProcessedData = {\r\n        totalRecords: 2,\r\n        dataPoints: 2,\r\n        sections: [],\r\n        tables: [\r\n          {\r\n            id: 'table-vulnerabilities',\r\n            title: 'vulnerabilities',\r\n            headers: ['id', 'severity', 'status'],\r\n            rows: [\r\n              [1, 'high', 'open'],\r\n              [2, 'critical', 'confirmed'],\r\n            ],\r\n          },\r\n        ],\r\n        metrics: [],\r\n      };\r\n\r\n      const mockVisualizations = {\r\n        charts: [\r\n          {\r\n            id: 'chart-123',\r\n            title: 'Vulnerabilities by Severity',\r\n            type: 'bar',\r\n            data: { labels: ['high', 'critical'], datasets: [{ data: [1, 1] }] },\r\n          },\r\n        ],\r\n      };\r\n\r\n      mockDataAggregationService.aggregateData.mockResolvedValue(mockData.vulnerabilities);\r\n      mockReportExportService.exportReport.mockResolvedValue('/path/to/report.pdf');\r\n      mockExecutionRepository.save.mockResolvedValue(execution);\r\n      mockReportRepository.save.mockResolvedValue(mockReport);\r\n\r\n      // Mock file size function\r\n      jest.spyOn(service as any, 'getFileSize').mockResolvedValue(1024);\r\n\r\n      await (service as any).processReportExecution(execution, mockReport, 'user-123');\r\n\r\n      expect(execution.start).toHaveBeenCalled();\r\n      expect(execution.updateProgress).toHaveBeenCalledWith(10, 'Collecting data');\r\n      expect(execution.updateProgress).toHaveBeenCalledWith(40, 'Processing data');\r\n      expect(execution.updateProgress).toHaveBeenCalledWith(70, 'Generating visualizations');\r\n      expect(execution.updateProgress).toHaveBeenCalledWith(90, 'Exporting report');\r\n      expect(mockDataAggregationService.aggregateData).toHaveBeenCalledWith(\r\n        'vulnerabilities',\r\n        expect.objectContaining(mockReport.configuration),\r\n      );\r\n      expect(mockReportExportService.exportReport).toHaveBeenCalled();\r\n      expect(execution.complete).toHaveBeenCalled();\r\n      expect(mockReport.updateExecutionStats).toHaveBeenCalled();\r\n    });\r\n\r\n    it('should handle execution failure', async () => {\r\n      const execution = {\r\n        ...mockExecution,\r\n        start: jest.fn(),\r\n        updateProgress: jest.fn(),\r\n        fail: jest.fn(),\r\n        parameters: {},\r\n      } as any;\r\n\r\n      const error = new Error('Data aggregation failed');\r\n      mockDataAggregationService.aggregateData.mockRejectedValue(error);\r\n      mockExecutionRepository.save.mockResolvedValue(execution);\r\n      mockReportRepository.save.mockResolvedValue(mockReport);\r\n\r\n      await (service as any).processReportExecution(execution, mockReport, 'user-123');\r\n\r\n      expect(execution.start).toHaveBeenCalled();\r\n      expect(execution.fail).toHaveBeenCalledWith(error);\r\n      expect(mockReport.updateExecutionStats).toHaveBeenCalledWith(\r\n        expect.any(Number),\r\n        0,\r\n        'failed',\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('data processing methods', () => {\r\n    it('should group data correctly', () => {\r\n      const data = [\r\n        { severity: 'high', status: 'open', count: 1 },\r\n        { severity: 'high', status: 'fixed', count: 2 },\r\n        { severity: 'critical', status: 'open', count: 3 },\r\n      ];\r\n      const groupBy = ['severity'];\r\n\r\n      const result = (service as any).groupData(data, groupBy);\r\n\r\n      expect(result).toHaveLength(2);\r\n      expect(result[0]).toEqual({\r\n        group: 'high',\r\n        count: 2,\r\n        items: [\r\n          { severity: 'high', status: 'open', count: 1 },\r\n          { severity: 'high', status: 'fixed', count: 2 },\r\n        ],\r\n      });\r\n      expect(result[1]).toEqual({\r\n        group: 'critical',\r\n        count: 1,\r\n        items: [\r\n          { severity: 'critical', status: 'open', count: 3 },\r\n        ],\r\n      });\r\n    });\r\n\r\n    it('should aggregate data correctly', () => {\r\n      const data = [\r\n        { severity: 'high', riskScore: 7.5 },\r\n        { severity: 'high', riskScore: 8.0 },\r\n        { severity: 'critical', riskScore: 9.5 },\r\n      ];\r\n      const aggregations = [\r\n        { function: 'count', field: 'severity', alias: 'total_count' },\r\n        { function: 'avg', field: 'riskScore', alias: 'avg_risk' },\r\n        { function: 'max', field: 'riskScore', alias: 'max_risk' },\r\n      ];\r\n\r\n      const result = (service as any).aggregateData(data, aggregations);\r\n\r\n      expect(result).toHaveLength(3);\r\n      expect(result[0]).toEqual({\r\n        id: 'total_count',\r\n        title: 'total_count',\r\n        value: 3,\r\n        unit: undefined,\r\n      });\r\n      expect(result[1]).toEqual({\r\n        id: 'avg_risk',\r\n        title: 'avg_risk',\r\n        value: 8.333333333333334,\r\n        unit: undefined,\r\n      });\r\n      expect(result[2]).toEqual({\r\n        id: 'max_risk',\r\n        title: 'max_risk',\r\n        value: 9.5,\r\n        unit: undefined,\r\n      });\r\n    });\r\n\r\n    it('should generate chart data correctly', async () => {\r\n      const vizConfig = {\r\n        type: 'chart',\r\n        chartType: 'bar',\r\n        title: 'Test Chart',\r\n        dataSource: 'vulnerabilities',\r\n        configuration: {\r\n          colors: ['#ff0000'],\r\n        },\r\n      };\r\n\r\n      const processedData = {\r\n        tables: [\r\n          {\r\n            id: 'table-vulnerabilities',\r\n            title: 'vulnerabilities',\r\n            headers: ['severity', 'count'],\r\n            rows: [\r\n              ['high', 5],\r\n              ['critical', 3],\r\n            ],\r\n          },\r\n        ],\r\n      };\r\n\r\n      const result = await (service as any).generateChart(vizConfig, processedData);\r\n\r\n      expect(result).toEqual({\r\n        labels: ['high', 'critical'],\r\n        datasets: [{\r\n          label: 'Test Chart',\r\n          data: [5, 3],\r\n          backgroundColor: ['#ff0000'],\r\n        }],\r\n      });\r\n    });\r\n  });\r\n});\r\n"], "version": 3}