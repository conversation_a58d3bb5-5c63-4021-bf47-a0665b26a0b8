{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\ioc.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAWiB;AACjB,6DAAkD;AAClD,+DAAoD;AAEpD;;;GAGG;AASI,IAAM,GAAG,GAAT,MAAM,GAAG;IAwOd;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACxD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,eAAe,GAAG;YACtB,GAAG,EAAE,CAAC;YACN,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE,CAAC;SACZ,CAAC;QAEF,MAAM,cAAc,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtD,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;QAE/C,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,gBAAgB,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,CAAC,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,QAAQ,CAAC;YACd,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAClC,KAAK,eAAe,CAAC;YACrB,KAAK,gBAAgB,CAAC;YACtB,KAAK,kBAAkB;gBACrB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAClC,KAAK,YAAY;gBACf,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC3B;gBACE,OAAO,IAAI,CAAC,KAAK,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3B,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,KAAc;QAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,MAAe;QACjC,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAc,EAAE,IAAS;QACzC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG;YAC5B,GAAG,IAAI;YACP,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;CACF,CAAA;AAtVY,kBAAG;AAEd;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;+BACpB;AAyBX;IApBC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,YAAY;YACZ,QAAQ;YACR,KAAK;YACL,eAAe;YACf,gBAAgB;YAChB,kBAAkB;YAClB,OAAO;YACP,cAAc;YACd,WAAW;YACX,OAAO;YACP,YAAY;YACZ,aAAa;YACb,KAAK;YACL,MAAM;YACN,OAAO;SACR;KACF,CAAC;;iCACW;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;kCACX;AAMd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCACpB;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;uCACtB;AAUnB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;QAC3C,OAAO,EAAE,QAAQ;KAClB,CAAC;;qCAC+C;AAUjD;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,gBAAgB,EAAE,aAAa,CAAC;QACxE,OAAO,EAAE,QAAQ;KAClB,CAAC;;mCAC2E;AAU7E;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC;QACxC,OAAO,EAAE,OAAO;KACjB,CAAC;;gCACuC;AAMzC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iCAC1B;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CACzC;AAM3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CAC1C;AAM3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;sCAAC;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;qCAAC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;sCAAC;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mCACX;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uCACpB;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;6CAC1C;AAMzB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;wCAC3B;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;wCAC5B;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CAC1C;AAMzB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCASxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oCAOxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2CAMjE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;2CAAc;AAMrC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAC/B,MAAM,oBAAN,MAAM;qCAAc;AAM/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sCAC1C;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sCAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;sCAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;sCAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+BAAU,EAAE,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC9E,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;kDAC1B,+BAAU,oBAAV,+BAAU;uCAAC;AAGxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCAC3C;AAQtB;IANC,IAAA,oBAAU,EAAC,GAAG,EAAE,CAAC,iCAAW,EAAE,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC;IAC9D,IAAA,mBAAS,EAAC;QACT,IAAI,EAAE,mBAAmB;QACzB,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,oBAAoB,EAAE,IAAI,EAAE;QAC1D,iBAAiB,EAAE,EAAE,IAAI,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,IAAI,EAAE;KAC3E,CAAC;;yCAC0B;cAtOjB,GAAG;IARf,IAAA,gBAAM,EAAC,MAAM,CAAC;IACd,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,OAAO,CAAC,CAAC;IAChB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;GACP,GAAG,CAsVf", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\ioc.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n  ManyToMany,\r\n  JoinTable,\r\n} from 'typeorm';\r\nimport { ThreatFeed } from './threat-feed.entity';\r\nimport { ThreatActor } from './threat-actor.entity';\r\n\r\n/**\r\n * Indicator of Compromise (IOC) entity\r\n * Represents security indicators used for threat detection and analysis\r\n */\r\n@Entity('iocs')\r\n@Index(['type'])\r\n@Index(['value'])\r\n@Index(['confidence'])\r\n@Index(['severity'])\r\n@Index(['status'])\r\n@Index(['firstSeen'])\r\n@Index(['lastSeen'])\r\nexport class IOC {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * IOC type (IP, domain, hash, URL, etc.)\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'ip_address',\r\n      'domain',\r\n      'url',\r\n      'file_hash_md5',\r\n      'file_hash_sha1',\r\n      'file_hash_sha256',\r\n      'email',\r\n      'registry_key',\r\n      'file_path',\r\n      'mutex',\r\n      'user_agent',\r\n      'certificate',\r\n      'asn',\r\n      'cidr',\r\n      'other',\r\n    ],\r\n  })\r\n  type: string;\r\n\r\n  /**\r\n   * IOC value (the actual indicator)\r\n   */\r\n  @Column({ length: 2048 })\r\n  value: string;\r\n\r\n  /**\r\n   * IOC description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Confidence level (0-100)\r\n   */\r\n  @Column({ type: 'integer', default: 50 })\r\n  confidence: number;\r\n\r\n  /**\r\n   * Severity level\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high', 'critical'],\r\n    default: 'medium',\r\n  })\r\n  severity: 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  /**\r\n   * IOC status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['active', 'inactive', 'expired', 'false_positive', 'whitelisted'],\r\n    default: 'active',\r\n  })\r\n  status: 'active' | 'inactive' | 'expired' | 'false_positive' | 'whitelisted';\r\n\r\n  /**\r\n   * TLP (Traffic Light Protocol) classification\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['white', 'green', 'amber', 'red'],\r\n    default: 'white',\r\n  })\r\n  tlp: 'white' | 'green' | 'amber' | 'red';\r\n\r\n  /**\r\n   * Tags for categorization\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  tags?: string[];\r\n\r\n  /**\r\n   * MITRE ATT&CK techniques\r\n   */\r\n  @Column({ name: 'mitre_techniques', type: 'jsonb', nullable: true })\r\n  mitreTechniques?: string[];\r\n\r\n  /**\r\n   * Kill chain phases\r\n   */\r\n  @Column({ name: 'kill_chain_phases', type: 'jsonb', nullable: true })\r\n  killChainPhases?: string[];\r\n\r\n  /**\r\n   * First time this IOC was seen\r\n   */\r\n  @Column({ name: 'first_seen', type: 'timestamp with time zone' })\r\n  firstSeen: Date;\r\n\r\n  /**\r\n   * Last time this IOC was seen\r\n   */\r\n  @Column({ name: 'last_seen', type: 'timestamp with time zone' })\r\n  lastSeen: Date;\r\n\r\n  /**\r\n   * Expiration date for the IOC\r\n   */\r\n  @Column({ name: 'expires_at', type: 'timestamp with time zone', nullable: true })\r\n  expiresAt?: Date;\r\n\r\n  /**\r\n   * Source of the IOC\r\n   */\r\n  @Column({ nullable: true })\r\n  source?: string;\r\n\r\n  /**\r\n   * External references\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  references?: string[];\r\n\r\n  /**\r\n   * Number of times this IOC has been observed\r\n   */\r\n  @Column({ name: 'observation_count', type: 'integer', default: 1 })\r\n  observationCount: number;\r\n\r\n  /**\r\n   * Whether this IOC is currently being monitored\r\n   */\r\n  @Column({ name: 'is_monitored', default: true })\r\n  isMonitored: boolean;\r\n\r\n  /**\r\n   * Whether this IOC has been validated\r\n   */\r\n  @Column({ name: 'is_validated', default: false })\r\n  isValidated: boolean;\r\n\r\n  /**\r\n   * Validation notes\r\n   */\r\n  @Column({ name: 'validation_notes', type: 'text', nullable: true })\r\n  validationNotes?: string;\r\n\r\n  /**\r\n   * Geolocation information (for IP addresses)\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  geolocation?: {\r\n    country?: string;\r\n    region?: string;\r\n    city?: string;\r\n    latitude?: number;\r\n    longitude?: number;\r\n    asn?: string;\r\n    isp?: string;\r\n  };\r\n\r\n  /**\r\n   * Additional context information\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  context?: {\r\n    malwareFamily?: string;\r\n    campaign?: string;\r\n    threatType?: string;\r\n    industry?: string;\r\n    targetedCountries?: string[];\r\n  };\r\n\r\n  /**\r\n   * Detection rules associated with this IOC\r\n   */\r\n  @Column({ name: 'detection_rules', type: 'jsonb', nullable: true })\r\n  detectionRules?: {\r\n    yara?: string;\r\n    snort?: string;\r\n    sigma?: string;\r\n    custom?: string[];\r\n  };\r\n\r\n  /**\r\n   * Enrichment data from external sources\r\n   */\r\n  @Column({ name: 'enrichment_data', type: 'jsonb', nullable: true })\r\n  enrichmentData?: Record<string, any>;\r\n\r\n  /**\r\n   * Additional metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: Record<string, any>;\r\n\r\n  /**\r\n   * User who created this IOC\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid', nullable: true })\r\n  createdBy?: string;\r\n\r\n  /**\r\n   * User who last updated this IOC\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => ThreatFeed, threatFeed => threatFeed.iocs, { nullable: true })\r\n  @JoinColumn({ name: 'threat_feed_id' })\r\n  threatFeed?: ThreatFeed;\r\n\r\n  @Column({ name: 'threat_feed_id', type: 'uuid', nullable: true })\r\n  threatFeedId?: string;\r\n\r\n  @ManyToMany(() => ThreatActor, threatActor => threatActor.iocs)\r\n  @JoinTable({\r\n    name: 'ioc_threat_actors',\r\n    joinColumn: { name: 'ioc_id', referencedColumnName: 'id' },\r\n    inverseJoinColumn: { name: 'threat_actor_id', referencedColumnName: 'id' },\r\n  })\r\n  threatActors: ThreatActor[];\r\n\r\n  /**\r\n   * Check if IOC is expired\r\n   */\r\n  get isExpired(): boolean {\r\n    return this.expiresAt ? new Date() > this.expiresAt : false;\r\n  }\r\n\r\n  /**\r\n   * Check if IOC is active and valid\r\n   */\r\n  get isActive(): boolean {\r\n    return this.status === 'active' && !this.isExpired;\r\n  }\r\n\r\n  /**\r\n   * Get age of IOC in days\r\n   */\r\n  get ageInDays(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.firstSeen.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get risk score based on confidence and severity\r\n   */\r\n  get riskScore(): number {\r\n    const severityWeights = {\r\n      low: 1,\r\n      medium: 2,\r\n      high: 3,\r\n      critical: 4,\r\n    };\r\n\r\n    const severityWeight = severityWeights[this.severity];\r\n    const confidenceWeight = this.confidence / 100;\r\n\r\n    return Math.round(severityWeight * confidenceWeight * 2.5 * 10) / 10;\r\n  }\r\n\r\n  /**\r\n   * Check if IOC type is a hash\r\n   */\r\n  get isHash(): boolean {\r\n    return this.type.startsWith('file_hash_');\r\n  }\r\n\r\n  /**\r\n   * Check if IOC type is network-related\r\n   */\r\n  get isNetworkIndicator(): boolean {\r\n    return ['ip_address', 'domain', 'url', 'cidr'].includes(this.type);\r\n  }\r\n\r\n  /**\r\n   * Get normalized IOC value for comparison\r\n   */\r\n  get normalizedValue(): string {\r\n    switch (this.type) {\r\n      case 'domain':\r\n      case 'email':\r\n        return this.value.toLowerCase();\r\n      case 'file_hash_md5':\r\n      case 'file_hash_sha1':\r\n      case 'file_hash_sha256':\r\n        return this.value.toLowerCase();\r\n      case 'ip_address':\r\n        return this.value.trim();\r\n      default:\r\n        return this.value;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update last seen timestamp\r\n   */\r\n  updateLastSeen(): void {\r\n    this.lastSeen = new Date();\r\n    this.observationCount += 1;\r\n  }\r\n\r\n  /**\r\n   * Mark IOC as validated\r\n   */\r\n  markAsValidated(notes?: string): void {\r\n    this.isValidated = true;\r\n    this.validationNotes = notes;\r\n  }\r\n\r\n  /**\r\n   * Mark IOC as false positive\r\n   */\r\n  markAsFalsePositive(reason?: string): void {\r\n    this.status = 'false_positive';\r\n    this.validationNotes = reason;\r\n    this.isMonitored = false;\r\n  }\r\n\r\n  /**\r\n   * Add enrichment data\r\n   */\r\n  addEnrichmentData(source: string, data: any): void {\r\n    if (!this.enrichmentData) {\r\n      this.enrichmentData = {};\r\n    }\r\n    this.enrichmentData[source] = {\r\n      ...data,\r\n      enrichedAt: new Date().toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}