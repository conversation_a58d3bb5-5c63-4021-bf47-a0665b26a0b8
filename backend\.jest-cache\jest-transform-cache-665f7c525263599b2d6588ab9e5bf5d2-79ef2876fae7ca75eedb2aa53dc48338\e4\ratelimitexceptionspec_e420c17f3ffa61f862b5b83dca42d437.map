{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\rate-limit.exception.spec.ts", "mappings": ";;AAAA,gFAA2E;AAE3E,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAEvD,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,SAAS,GAAG,IAAI,yCAAkB,CACtC,qBAAqB,EACrB,cAAc,EACd,UAAU,EACV,GAAG,EACH,GAAG,EACH,aAAa,EACb,IAAI,CACL,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,yCAAkB,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACtD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACnD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,SAAS,GAAG,IAAI,yCAAkB,CACtC,qBAAqB,EACrB,cAAc,EACd,UAAU,EACV,GAAG,EACH,EAAE,EACF,aAAa,EACb,IAAI,CACL,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,qBAAqB;YAC5E,MAAM,SAAS,GAAG,IAAI,yCAAkB,CACtC,qBAAqB,EACrB,cAAc,EACd,UAAU,EACV,GAAG,EACH,GAAG,EACH,eAAe,EACf,IAAI,CACL,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;YAC/B,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;gBACvE,MAAM,SAAS,GAAG,yCAAkB,CAAC,eAAe,CAClD,IAAI,EACJ,IAAI,EACJ,aAAa,EACb,IAAI,EACJ;oBACE,MAAM,EAAE,UAAU;oBAClB,QAAQ,EAAE,YAAY;oBACtB,MAAM,EAAE,KAAK;iBACd,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,2FAA2F,CAC5F,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACjD,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC9C,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClC,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;gBAC1E,MAAM,SAAS,GAAG,yCAAkB,CAAC,eAAe,CAClD,GAAG,EACH,GAAG,EACH,aAAa,EACb,IAAI,EACJ;oBACE,MAAM,EAAE,SAAS;iBAClB,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAChD,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;gBACzE,MAAM,SAAS,GAAG,yCAAkB,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;gBAE9E,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACvC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,uEAAuE,CACxE,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,SAAS,GAAG,yCAAkB,CAAC,cAAc,CACjD,aAAa,EACb,EAAE,EACF,EAAE,EACF,aAAa,EACb,IAAI,EACJ;oBACE,MAAM,EAAE,UAAU;iBACnB,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,oFAAoF,CACrF,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC/C,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC9C,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;gBAC1D,MAAM,SAAS,GAAG,yCAAkB,CAAC,kBAAkB,CACrD,SAAS,EACT,IAAI,EACJ,IAAI,EACJ,aAAa,EACb,KAAK,EACL;oBACE,QAAQ,EAAE,YAAY;oBACtB,IAAI,EAAE,IAAI;iBACX,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,qEAAqE,CACtE,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACnD,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;YACxC,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;gBAChE,MAAM,SAAS,GAAG,yCAAkB,CAAC,wBAAwB,CAC3D,iBAAiB,EACjB,CAAC,EACD,CAAC,EACD;oBACE,MAAM,EAAE,UAAU;iBACnB,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,2EAA2E,CAC5E,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBAC1D,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC9C,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAM,CAAC,SAAS,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,SAAS,GAAG,yCAAkB,CAAC,cAAc,CACjD,OAAO,EACP,OAAO,EACP,aAAa,EACb,IAAI,EACJ;oBACE,MAAM,EAAE,UAAU;oBAClB,IAAI,EAAE,OAAO;oBACb,SAAS,EAAE,QAAQ;iBACpB,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,6EAA6E,CAC9E,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC9C,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC9C,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,SAAS,GAAG,yCAAkB,CAAC,UAAU,CAC7C,WAAW,EACX,KAAK,EACL,KAAK,EACL,aAAa,EACb,OAAO,EAAE,UAAU;gBACnB;oBACE,QAAQ,EAAE,YAAY;oBACtB,IAAI,EAAE,OAAO;iBACd,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,4DAA4D,CAC7D,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC1C,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,YAAY,GAAG,yCAAkB,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;YACjF,MAAM,kBAAkB,GAAG,yCAAkB,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,CAAC;YAC9F,MAAM,iBAAiB,GAAG,yCAAkB,CAAC,kBAAkB,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;YACtG,MAAM,mBAAmB,GAAG,yCAAkB,CAAC,wBAAwB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACzF,MAAM,kBAAkB,GAAG,yCAAkB,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;YACxF,MAAM,cAAc,GAAG,yCAAkB,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;YAEhG,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEpD,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,kBAAkB,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE9D,MAAM,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEnE,MAAM,CAAC,mBAAmB,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpE,MAAM,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE3D,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtD,MAAM,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,SAAS,GAAG,IAAI,yCAAkB,CACtC,MAAM,EACN,cAAc,EACd,UAAU,EACV,GAAG,EACH,EAAE,EACF,aAAa,EACb,IAAI,CACL,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,MAAM,SAAS,GAAG,IAAI,yCAAkB,CACtC,MAAM,EACN,cAAc,EACd,UAAU,EACV,GAAG,EACH,GAAG,EACH,aAAa,EACb,IAAI,CACL,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;gBAChD,MAAM,SAAS,GAAG,IAAI,yCAAkB,CACtC,MAAM,EACN,cAAc,EACd,UAAU,EACV,GAAG,EACH,GAAG,EACH,aAAa,EACb,IAAI,CACL,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;gBAC9D,MAAM,SAAS,GAAG,IAAI,yCAAkB,CACtC,MAAM,EACN,cAAc,EACd,UAAU,EACV,GAAG,EACH,EAAE,EACF,aAAa,EACb,IAAI,CACL,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,qBAAqB;gBAC5E,MAAM,SAAS,GAAG,IAAI,yCAAkB,CACtC,MAAM,EACN,cAAc,EACd,UAAU,EACV,GAAG,EACH,GAAG,EACH,eAAe,EACf,IAAI,CACL,CAAC;gBAEF,MAAM,cAAc,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;gBACrD,MAAM,CAAC,cAAc,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBAC5C,MAAM,CAAC,cAAc,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;gBAC7C,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,gBAAgB;gBACrE,MAAM,SAAS,GAAG,IAAI,yCAAkB,CACtC,MAAM,EACN,cAAc,EACd,UAAU,EACV,GAAG,EACH,GAAG,EACH,aAAa,EACb,IAAI,CACL,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;YAC1C,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,sBAAsB;gBAC5E,MAAM,SAAS,GAAG,IAAI,yCAAkB,CACtC,MAAM,EACN,cAAc,EACd,UAAU,EACV,GAAG,EACH,GAAG,EACH,eAAe,EACf,IAAI,CACL,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,0BAA0B,EAAE,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC5E,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,qBAAqB;gBAC5E,MAAM,SAAS,GAAG,IAAI,yCAAkB,CACtC,MAAM,EACN,cAAc,EACd,UAAU,EACV,GAAG,EACH,GAAG,EACH,eAAe,EACf,IAAI,CACL,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,0BAA0B,EAAE,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC5E,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;gBACvC,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,mBAAmB;gBAC3E,MAAM,SAAS,GAAG,IAAI,yCAAkB,CACtC,MAAM,EACN,cAAc,EACd,UAAU,EACV,GAAG,EACH,GAAG,EACH,eAAe,EACf,IAAI,CACL,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,0BAA0B,EAAE,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;YACtC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,SAAS,GAAG,yCAAkB,CAAC,wBAAwB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC/E,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;gBAC/B,MAAM,SAAS,GAAG,IAAI,yCAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;gBACjG,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;gBAC/B,MAAM,SAAS,GAAG,IAAI,yCAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC;gBAClG,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,qBAAqB,EAAE,GAAG,EAAE;gBAC7B,MAAM,SAAS,GAAG,IAAI,yCAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;gBACnG,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;gBAC5B,MAAM,SAAS,GAAG,IAAI,yCAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;gBACpG,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;YAC5B,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;gBACjC,MAAM,SAAS,GAAG,IAAI,yCAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;gBACnG,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;gBACnC,MAAM,SAAS,GAAG,IAAI,yCAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;gBACrG,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;gBACnC,MAAM,SAAS,GAAG,IAAI,yCAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;gBACjG,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YAC9E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACvC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,SAAS,GAAG,yCAAkB,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;gBAC9E,MAAM,eAAe,GAAG,SAAS,CAAC,uBAAuB,EAAE,CAAC;gBAE5D,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC5D,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;gBAClE,MAAM,SAAS,GAAG,yCAAkB,CAAC,wBAAwB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC/E,MAAM,eAAe,GAAG,SAAS,CAAC,uBAAuB,EAAE,CAAC;gBAE5D,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACvD,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC;YACtD,MAAM,SAAS,GAAG,yCAAkB,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC;YAEhF,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,OAAO,CACxC,qGAAqG,CACtG,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC;YACtD,MAAM,SAAS,GAAG,yCAAkB,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,CAAC;YAEvF,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,OAAO,CACxC,2FAA2F,CAC5F,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,SAAS,GAAG,yCAAkB,CAAC,wBAAwB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE/E,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CACrC,6GAA6G,CAC9G,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,SAAS,GAAG,yCAAkB,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;YAE9E,MAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;YAE3C,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;YAC1E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;gBACrC,SAAS,EAAE,cAAc;gBACzB,KAAK,EAAE,GAAG;gBACV,IAAI,EAAE,GAAG;gBACT,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,aAAa,CAAC,WAAW,EAAE;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;gBACrC,mBAAmB,EAAE,KAAK;gBAC1B,uBAAuB,EAAE,GAAG;gBAC5B,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE;gBAC1E,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAClC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,SAAS,GAAG,yCAAkB,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE;gBAClF,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,YAAY;gBACtB,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;gBACzB,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,qBAAqB;gBAC3B,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,YAAY;gBACtB,SAAS,EAAE,cAAc;gBACzB,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,GAAG;gBACV,IAAI,EAAE,GAAG;gBACT,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,aAAa,CAAC,WAAW,EAAE;gBACtC,UAAU,EAAE,IAAI;gBAChB,eAAe,EAAE,GAAG;gBACpB,qBAAqB,EAAE,IAAI;gBAC3B,mBAAmB,EAAE,WAAW;gBAChC,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE;gBAC3C,iBAAiB,EAAE,IAAI;gBACvB,gBAAgB,EAAE,KAAK;gBACvB,oBAAoB,EAAE,KAAK;gBAC3B,0BAA0B,EAAE,KAAK;gBACjC,gBAAgB,EAAE,KAAK;gBACvB,YAAY,EAAE,KAAK;aACpB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,SAAS,GAAG,IAAI,yCAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;YAEnG,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,SAAS,GAAG,IAAI,yCAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;YAEnG,MAAM,CAAC,SAAS,YAAY,yCAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,YAAY,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\rate-limit.exception.spec.ts"], "sourcesContent": ["import { RateLimitException } from '../../exceptions/rate-limit.exception';\r\n\r\ndescribe('RateLimitException', () => {\r\n  const mockResetTime = new Date('2023-01-01T12:00:00Z');\r\n\r\n  describe('constructor', () => {\r\n    it('should create exception with required parameters', () => {\r\n      const exception = new RateLimitException(\r\n        'Rate limit exceeded',\r\n        'api_requests',\r\n        'user:123',\r\n        100,\r\n        105,\r\n        mockResetTime,\r\n        3600\r\n      );\r\n\r\n      expect(exception).toBeInstanceOf(RateLimitException);\r\n      expect(exception.message).toBe('Rate limit exceeded');\r\n      expect(exception.code).toBe('RATE_LIMIT_EXCEEDED');\r\n      expect(exception.severity).toBe('medium');\r\n      expect(exception.category).toBe('rate_limit');\r\n      expect(exception.limitType).toBe('api_requests');\r\n      expect(exception.scope).toBe('user:123');\r\n      expect(exception.limit).toBe(100);\r\n      expect(exception.used).toBe(105);\r\n      expect(exception.remaining).toBe(0);\r\n      expect(exception.resetTime).toEqual(mockResetTime);\r\n      expect(exception.windowSize).toBe(3600);\r\n    });\r\n\r\n    it('should calculate remaining correctly when not exceeded', () => {\r\n      const exception = new RateLimitException(\r\n        'Rate limit exceeded',\r\n        'api_requests',\r\n        'user:123',\r\n        100,\r\n        80,\r\n        mockResetTime,\r\n        3600\r\n      );\r\n\r\n      expect(exception.remaining).toBe(20);\r\n    });\r\n\r\n    it('should calculate retryAfter correctly', () => {\r\n      const futureResetTime = new Date(Date.now() + 300000); // 5 minutes from now\r\n      const exception = new RateLimitException(\r\n        'Rate limit exceeded',\r\n        'api_requests',\r\n        'user:123',\r\n        100,\r\n        105,\r\n        futureResetTime,\r\n        3600\r\n      );\r\n\r\n      expect(exception.retryAfter).toBeGreaterThan(290);\r\n      expect(exception.retryAfter).toBeLessThanOrEqual(300);\r\n    });\r\n  });\r\n\r\n  describe('static factory methods', () => {\r\n    describe('apiRequestLimit', () => {\r\n      it('should create exception for API request limit with user scope', () => {\r\n        const exception = RateLimitException.apiRequestLimit(\r\n          1000,\r\n          1005,\r\n          mockResetTime,\r\n          3600,\r\n          {\r\n            userId: 'user-123',\r\n            endpoint: '/api/users',\r\n            method: 'GET',\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          'API request rate limit exceeded for GET /api/users. Limit: 1000 requests per 3600 seconds'\r\n        );\r\n        expect(exception.limitType).toBe('api_requests');\r\n        expect(exception.scope).toBe('user:user-123');\r\n        expect(exception.limit).toBe(1000);\r\n        expect(exception.used).toBe(1005);\r\n        expect(exception.isApiRequestLimit()).toBe(true);\r\n      });\r\n\r\n      it('should create exception for API request limit with API key scope', () => {\r\n        const exception = RateLimitException.apiRequestLimit(\r\n          500,\r\n          505,\r\n          mockResetTime,\r\n          3600,\r\n          {\r\n            apiKey: 'key-456',\r\n          }\r\n        );\r\n\r\n        expect(exception.scope).toBe('api_key:key-456');\r\n        expect(exception.isApiRequestLimit()).toBe(true);\r\n      });\r\n\r\n      it('should create exception for API request limit with global scope', () => {\r\n        const exception = RateLimitException.apiRequestLimit(100, 105, mockResetTime);\r\n\r\n        expect(exception.scope).toBe('global');\r\n        expect(exception.message).toBe(\r\n          'API request rate limit exceeded. Limit: 100 requests per 3600 seconds'\r\n        );\r\n      });\r\n    });\r\n\r\n    describe('operationLimit', () => {\r\n      it('should create exception for operation limit', () => {\r\n        const exception = RateLimitException.operationLimit(\r\n          'file_upload',\r\n          10,\r\n          12,\r\n          mockResetTime,\r\n          3600,\r\n          {\r\n            userId: 'user-123',\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          \"Operation 'file_upload' rate limit exceeded. Limit: 10 operations per 3600 seconds\"\r\n        );\r\n        expect(exception.limitType).toBe('operations');\r\n        expect(exception.scope).toBe('user:user-123');\r\n        expect(exception.isOperationLimit()).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('resourceUsageLimit', () => {\r\n      it('should create exception for resource usage limit', () => {\r\n        const exception = RateLimitException.resourceUsageLimit(\r\n          'storage',\r\n          1000,\r\n          1200,\r\n          mockResetTime,\r\n          86400,\r\n          {\r\n            tenantId: 'tenant-123',\r\n            unit: 'MB',\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          'storage usage rate limit exceeded. Limit: 1000 MB per 86400 seconds'\r\n        );\r\n        expect(exception.limitType).toBe('resource_usage');\r\n        expect(exception.scope).toBe('tenant:tenant-123');\r\n        expect(exception.isResourceUsageLimit()).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('concurrentOperationLimit', () => {\r\n      it('should create exception for concurrent operation limit', () => {\r\n        const exception = RateLimitException.concurrentOperationLimit(\r\n          'data_processing',\r\n          5,\r\n          7,\r\n          {\r\n            userId: 'user-123',\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          'Concurrent data_processing limit exceeded. Limit: 5 concurrent operations'\r\n        );\r\n        expect(exception.limitType).toBe('concurrent_operations');\r\n        expect(exception.scope).toBe('user:user-123');\r\n        expect(exception.windowSize).toBe(0);\r\n        expect(exception.isConcurrentOperationLimit()).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('bandwidthLimit', () => {\r\n      it('should create exception for bandwidth limit', () => {\r\n        const exception = RateLimitException.bandwidthLimit(\r\n          1000000,\r\n          1200000,\r\n          mockResetTime,\r\n          3600,\r\n          {\r\n            userId: 'user-123',\r\n            unit: 'bytes',\r\n            direction: 'upload',\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          'Bandwidth upload rate limit exceeded. Limit: 1000000 bytes per 3600 seconds'\r\n        );\r\n        expect(exception.limitType).toBe('bandwidth');\r\n        expect(exception.scope).toBe('user:user-123');\r\n        expect(exception.isBandwidthLimit()).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('quotaLimit', () => {\r\n      it('should create exception for quota limit', () => {\r\n        const exception = RateLimitException.quotaLimit(\r\n          'API calls',\r\n          10000,\r\n          10500,\r\n          mockResetTime,\r\n          2592000, // 30 days\r\n          {\r\n            tenantId: 'tenant-123',\r\n            unit: 'calls',\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          'API calls quota exceeded. Limit: 10000 calls per 30 day(s)'\r\n        );\r\n        expect(exception.limitType).toBe('quota');\r\n        expect(exception.scope).toBe('tenant:tenant-123');\r\n        expect(exception.isQuotaLimit()).toBe(true);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('type checking methods', () => {\r\n    it('should correctly identify rate limit types', () => {\r\n      const apiException = RateLimitException.apiRequestLimit(100, 105, mockResetTime);\r\n      const operationException = RateLimitException.operationLimit('upload', 10, 12, mockResetTime);\r\n      const resourceException = RateLimitException.resourceUsageLimit('storage', 1000, 1200, mockResetTime);\r\n      const concurrentException = RateLimitException.concurrentOperationLimit('process', 5, 7);\r\n      const bandwidthException = RateLimitException.bandwidthLimit(1000, 1200, mockResetTime);\r\n      const quotaException = RateLimitException.quotaLimit('API', 10000, 10500, mockResetTime, 86400);\r\n\r\n      expect(apiException.isApiRequestLimit()).toBe(true);\r\n      expect(apiException.isOperationLimit()).toBe(false);\r\n\r\n      expect(operationException.isOperationLimit()).toBe(true);\r\n      expect(operationException.isResourceUsageLimit()).toBe(false);\r\n\r\n      expect(resourceException.isResourceUsageLimit()).toBe(true);\r\n      expect(resourceException.isConcurrentOperationLimit()).toBe(false);\r\n\r\n      expect(concurrentException.isConcurrentOperationLimit()).toBe(true);\r\n      expect(concurrentException.isBandwidthLimit()).toBe(false);\r\n\r\n      expect(bandwidthException.isBandwidthLimit()).toBe(true);\r\n      expect(bandwidthException.isQuotaLimit()).toBe(false);\r\n\r\n      expect(quotaException.isQuotaLimit()).toBe(true);\r\n      expect(quotaException.isApiRequestLimit()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('utility methods', () => {\r\n    describe('getUsagePercentage', () => {\r\n      it('should calculate usage percentage correctly', () => {\r\n        const exception = new RateLimitException(\r\n          'Test',\r\n          'api_requests',\r\n          'user:123',\r\n          100,\r\n          75,\r\n          mockResetTime,\r\n          3600\r\n        );\r\n\r\n        expect(exception.getUsagePercentage()).toBe(75);\r\n      });\r\n\r\n      it('should handle over-limit usage', () => {\r\n        const exception = new RateLimitException(\r\n          'Test',\r\n          'api_requests',\r\n          'user:123',\r\n          100,\r\n          120,\r\n          mockResetTime,\r\n          3600\r\n        );\r\n\r\n        expect(exception.getUsagePercentage()).toBe(120);\r\n      });\r\n    });\r\n\r\n    describe('isCompletelyExhausted', () => {\r\n      it('should return true when remaining is 0', () => {\r\n        const exception = new RateLimitException(\r\n          'Test',\r\n          'api_requests',\r\n          'user:123',\r\n          100,\r\n          100,\r\n          mockResetTime,\r\n          3600\r\n        );\r\n\r\n        expect(exception.isCompletelyExhausted()).toBe(true);\r\n      });\r\n\r\n      it('should return false when remaining is greater than 0', () => {\r\n        const exception = new RateLimitException(\r\n          'Test',\r\n          'api_requests',\r\n          'user:123',\r\n          100,\r\n          80,\r\n          mockResetTime,\r\n          3600\r\n        );\r\n\r\n        expect(exception.isCompletelyExhausted()).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('getTimeUntilReset', () => {\r\n      it('should return time until reset in seconds', () => {\r\n        const futureResetTime = new Date(Date.now() + 300000); // 5 minutes from now\r\n        const exception = new RateLimitException(\r\n          'Test',\r\n          'api_requests',\r\n          'user:123',\r\n          100,\r\n          105,\r\n          futureResetTime,\r\n          3600\r\n        );\r\n\r\n        const timeUntilReset = exception.getTimeUntilReset();\r\n        expect(timeUntilReset).toBeGreaterThan(290);\r\n        expect(timeUntilReset).toBeLessThanOrEqual(300);\r\n      });\r\n\r\n      it('should return 0 for past reset time', () => {\r\n        const pastResetTime = new Date(Date.now() - 300000); // 5 minutes ago\r\n        const exception = new RateLimitException(\r\n          'Test',\r\n          'api_requests',\r\n          'user:123',\r\n          100,\r\n          105,\r\n          pastResetTime,\r\n          3600\r\n        );\r\n\r\n        expect(exception.getTimeUntilReset()).toBe(0);\r\n      });\r\n    });\r\n\r\n    describe('getTimeUntilResetFormatted', () => {\r\n      it('should format seconds correctly', () => {\r\n        const futureResetTime = new Date(Date.now() + 30000); // 30 seconds from now\r\n        const exception = new RateLimitException(\r\n          'Test',\r\n          'api_requests',\r\n          'user:123',\r\n          100,\r\n          105,\r\n          futureResetTime,\r\n          3600\r\n        );\r\n\r\n        expect(exception.getTimeUntilResetFormatted()).toMatch(/\\d+ second\\(s\\)/);\r\n      });\r\n\r\n      it('should format minutes correctly', () => {\r\n        const futureResetTime = new Date(Date.now() + 300000); // 5 minutes from now\r\n        const exception = new RateLimitException(\r\n          'Test',\r\n          'api_requests',\r\n          'user:123',\r\n          100,\r\n          105,\r\n          futureResetTime,\r\n          3600\r\n        );\r\n\r\n        expect(exception.getTimeUntilResetFormatted()).toMatch(/\\d+ minute\\(s\\)/);\r\n      });\r\n\r\n      it('should format hours correctly', () => {\r\n        const futureResetTime = new Date(Date.now() + 7200000); // 2 hours from now\r\n        const exception = new RateLimitException(\r\n          'Test',\r\n          'api_requests',\r\n          'user:123',\r\n          100,\r\n          105,\r\n          futureResetTime,\r\n          3600\r\n        );\r\n\r\n        expect(exception.getTimeUntilResetFormatted()).toMatch(/\\d+ hour\\(s\\)/);\r\n      });\r\n    });\r\n\r\n    describe('getWindowSizeFormatted', () => {\r\n      it('should format concurrent window', () => {\r\n        const exception = RateLimitException.concurrentOperationLimit('process', 5, 7);\r\n        expect(exception.getWindowSizeFormatted()).toBe('concurrent');\r\n      });\r\n\r\n      it('should format seconds', () => {\r\n        const exception = new RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 30);\r\n        expect(exception.getWindowSizeFormatted()).toBe('30 second(s)');\r\n      });\r\n\r\n      it('should format minutes', () => {\r\n        const exception = new RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 300);\r\n        expect(exception.getWindowSizeFormatted()).toBe('5 minute(s)');\r\n      });\r\n\r\n      it('should format hours', () => {\r\n        const exception = new RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 3600);\r\n        expect(exception.getWindowSizeFormatted()).toBe('1 hour(s)');\r\n      });\r\n\r\n      it('should format days', () => {\r\n        const exception = new RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 86400);\r\n        expect(exception.getWindowSizeFormatted()).toBe('1 day(s)');\r\n      });\r\n    });\r\n\r\n    describe('getScopeInfo', () => {\r\n      it('should parse user scope', () => {\r\n        const exception = new RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 3600);\r\n        expect(exception.getScopeInfo()).toEqual({ type: 'user', id: '123' });\r\n      });\r\n\r\n      it('should parse tenant scope', () => {\r\n        const exception = new RateLimitException('Test', 'api', 'tenant:456', 100, 105, mockResetTime, 3600);\r\n        expect(exception.getScopeInfo()).toEqual({ type: 'tenant', id: '456' });\r\n      });\r\n\r\n      it('should parse global scope', () => {\r\n        const exception = new RateLimitException('Test', 'api', 'global', 100, 105, mockResetTime, 3600);\r\n        expect(exception.getScopeInfo()).toEqual({ type: 'global', id: undefined });\r\n      });\r\n    });\r\n\r\n    describe('getRetryRecommendations', () => {\r\n      it('should provide recommendations for API requests', () => {\r\n        const exception = RateLimitException.apiRequestLimit(100, 105, mockResetTime);\r\n        const recommendations = exception.getRetryRecommendations();\r\n\r\n        expect(recommendations.backoffStrategy).toBe('exponential');\r\n        expect(recommendations.maxRetries).toBe(3);\r\n      });\r\n\r\n      it('should provide recommendations for concurrent operations', () => {\r\n        const exception = RateLimitException.concurrentOperationLimit('process', 5, 7);\r\n        const recommendations = exception.getRetryRecommendations();\r\n\r\n        expect(recommendations.backoffStrategy).toBe('linear');\r\n        expect(recommendations.maxRetries).toBe(10);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('getUserMessage', () => {\r\n    it('should return user-friendly message for API requests', () => {\r\n      const futureResetTime = new Date(Date.now() + 300000);\r\n      const exception = RateLimitException.apiRequestLimit(100, 105, futureResetTime);\r\n      \r\n      expect(exception.getUserMessage()).toMatch(\r\n        /You have exceeded the API request limit\\. Please wait \\d+ minute\\(s\\) before making more requests\\./\r\n      );\r\n    });\r\n\r\n    it('should return user-friendly message for operations', () => {\r\n      const futureResetTime = new Date(Date.now() + 300000);\r\n      const exception = RateLimitException.operationLimit('upload', 10, 12, futureResetTime);\r\n      \r\n      expect(exception.getUserMessage()).toMatch(\r\n        /You have exceeded the operation limit\\. Please wait \\d+ minute\\(s\\) before trying again\\./\r\n      );\r\n    });\r\n\r\n    it('should return user-friendly message for concurrent operations', () => {\r\n      const exception = RateLimitException.concurrentOperationLimit('process', 5, 7);\r\n      \r\n      expect(exception.getUserMessage()).toBe(\r\n        'You have too many concurrent operations running. Please wait for some to complete before starting new ones.'\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('toApiResponse', () => {\r\n    it('should convert to API response format with headers', () => {\r\n      const exception = RateLimitException.apiRequestLimit(100, 105, mockResetTime);\r\n\r\n      const response = exception.toApiResponse();\r\n\r\n      expect(response.error).toMatch(/You have exceeded the API request limit/);\r\n      expect(response.code).toBe('RATE_LIMIT_EXCEEDED');\r\n      expect(response.details).toMatchObject({\r\n        limitType: 'api_requests',\r\n        limit: 100,\r\n        used: 105,\r\n        remaining: 0,\r\n        resetTime: mockResetTime.toISOString(),\r\n      });\r\n      expect(response.headers).toMatchObject({\r\n        'X-RateLimit-Limit': '100',\r\n        'X-RateLimit-Remaining': '0',\r\n        'X-RateLimit-Reset': Math.floor(mockResetTime.getTime() / 1000).toString(),\r\n        'Retry-After': expect.any(String),\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('toJSON', () => {\r\n    it('should convert to JSON with detailed information', () => {\r\n      const exception = RateLimitException.apiRequestLimit(100, 105, mockResetTime, 3600, {\r\n        userId: 'user-123',\r\n        endpoint: '/api/users',\r\n        method: 'GET',\r\n      });\r\n\r\n      const json = exception.toJSON();\r\n\r\n      expect(json).toMatchObject({\r\n        name: 'RateLimitException',\r\n        code: 'RATE_LIMIT_EXCEEDED',\r\n        severity: 'medium',\r\n        category: 'rate_limit',\r\n        limitType: 'api_requests',\r\n        scope: 'user:user-123',\r\n        limit: 100,\r\n        used: 105,\r\n        remaining: 0,\r\n        resetTime: mockResetTime.toISOString(),\r\n        windowSize: 3600,\r\n        usagePercentage: 105,\r\n        isCompletelyExhausted: true,\r\n        windowSizeFormatted: '1 hour(s)',\r\n        scopeInfo: { type: 'user', id: 'user-123' },\r\n        isApiRequestLimit: true,\r\n        isOperationLimit: false,\r\n        isResourceUsageLimit: false,\r\n        isConcurrentOperationLimit: false,\r\n        isBandwidthLimit: false,\r\n        isQuotaLimit: false,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('inheritance', () => {\r\n    it('should be instance of Error and DomainException', () => {\r\n      const exception = new RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 3600);\r\n\r\n      expect(exception).toBeInstanceOf(Error);\r\n      expect(exception.name).toBe('RateLimitException');\r\n      expect(exception.code).toBe('RATE_LIMIT_EXCEEDED');\r\n    });\r\n\r\n    it('should maintain proper prototype chain', () => {\r\n      const exception = new RateLimitException('Test', 'api', 'user:123', 100, 105, mockResetTime, 3600);\r\n\r\n      expect(exception instanceof RateLimitException).toBe(true);\r\n      expect(exception instanceof Error).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}