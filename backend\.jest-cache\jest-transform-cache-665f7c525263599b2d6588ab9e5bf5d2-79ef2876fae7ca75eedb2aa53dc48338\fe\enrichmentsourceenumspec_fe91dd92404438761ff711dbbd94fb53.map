{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\__tests__\\enrichment-source.enum.spec.ts", "mappings": ";;AAAA,sEAAoF;AAEpF,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACjF,MAAM,CAAC,yCAAgB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC7D,MAAM,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACvD,MAAM,CAAC,yCAAgB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,yCAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;YAC7B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,OAAO,GAAG,8CAAqB,CAAC,aAAa,EAAE,CAAC;gBACtD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,uBAAuB;gBACnE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC;gBACpE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC;gBACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,GAAG,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,kBAAkB,GAAG,8CAAqB,CAAC,qBAAqB,EAAE,CAAC;gBACzE,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC;gBAC/E,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,KAAK,CAAC,CAAC;gBAC7D,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,gBAAgB,CAAC,CAAC;gBACxE,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,IAAI,CAAC,CAAC;gBAC5D,MAAM,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC;YAC3E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;gBAClD,MAAM,iBAAiB,GAAG,8CAAqB,CAAC,oBAAoB,EAAE,CAAC;gBACvE,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC;gBACpE,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,iBAAiB,CAAC,CAAC;gBACxE,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,cAAc,CAAC,CAAC;gBACrE,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,eAAe,CAAC,CAAC;gBACtE,MAAM,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;gBAC5C,MAAM,kBAAkB,GAAG,8CAAqB,CAAC,qBAAqB,EAAE,CAAC;gBACzE,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC;gBAClE,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,MAAM,CAAC,CAAC;gBAC9D,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,MAAM,CAAC,CAAC;gBAC9D,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,YAAY,CAAC,CAAC;gBACpE,MAAM,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC;YACrF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,MAAM,eAAe,GAAG,8CAAqB,CAAC,kBAAkB,EAAE,CAAC;gBACnE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC;gBAC5E,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,YAAY,CAAC,CAAC;gBACjE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,eAAe,CAAC,CAAC;gBACpE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,qBAAqB,CAAC,CAAC;gBAC1E,MAAM,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,eAAe,GAAG,8CAAqB,CAAC,kBAAkB,EAAE,CAAC;gBACnE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC;gBAClE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,cAAc,CAAC,CAAC;gBACnE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC;gBAC/D,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,YAAY,CAAC,CAAC;gBACjE,MAAM,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,yCAAgB,CAAC,GAAG,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;YAC/B,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;gBAChD,MAAM,YAAY,GAAG,8CAAqB,CAAC,eAAe,EAAE,CAAC;gBAC7D,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC;gBACzE,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,GAAG,CAAC,CAAC;gBACrD,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,YAAY,CAAC,CAAC;gBAC9D,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,yCAAgB,CAAC,IAAI,CAAC,CAAC;gBACtD,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjF,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC7E,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpF,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9F,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC/F,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9F,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnF,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtF,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAClF,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpF,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrF,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjF,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC3E,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,yCAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAChC,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;gBAC3D,MAAM,CAAC,8CAAqB,CAAC,gBAAgB,CAAC,yCAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChF,MAAM,CAAC,8CAAqB,CAAC,gBAAgB,CAAC,yCAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzF,MAAM,CAAC,8CAAqB,CAAC,gBAAgB,CAAC,yCAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjF,MAAM,CAAC,8CAAqB,CAAC,gBAAgB,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC3F,MAAM,CAAC,8CAAqB,CAAC,gBAAgB,CAAC,yCAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9F,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;YACnC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,CAAC,8CAAqB,CAAC,mBAAmB,CAAC,yCAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC9F,MAAM,CAAC,8CAAqB,CAAC,mBAAmB,CAAC,yCAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACjF,MAAM,CAAC,8CAAqB,CAAC,mBAAmB,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrG,MAAM,CAAC,8CAAqB,CAAC,mBAAmB,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACxF,MAAM,CAAC,8CAAqB,CAAC,mBAAmB,CAAC,yCAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACnF,MAAM,CAAC,8CAAqB,CAAC,mBAAmB,CAAC,yCAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAErF,6CAA6C;gBAC7C,MAAM,YAAY,GAAG,8CAAqB,CAAC,mBAAmB,CAAC,yCAAgB,CAAC,KAAK,CAAC,CAAC;gBACvF,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACvC,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;gBACnE,MAAM,CAAC,8CAAqB,CAAC,uBAAuB,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa;gBAC/G,MAAM,CAAC,8CAAqB,CAAC,uBAAuB,CAAC,yCAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa;gBAC/G,MAAM,CAAC,8CAAqB,CAAC,uBAAuB,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;gBAClH,MAAM,CAAC,8CAAqB,CAAC,uBAAuB,CAAC,yCAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ;gBAC9F,MAAM,CAAC,8CAAqB,CAAC,uBAAuB,CAAC,yCAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;gBAEjG,6CAA6C;gBAC7C,MAAM,gBAAgB,GAAG,8CAAqB,CAAC,uBAAuB,CAAC,yCAAgB,CAAC,KAAK,CAAC,CAAC;gBAC/F,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;YAC/B,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,CAAC,8CAAqB,CAAC,eAAe,CAAC,yCAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACnF,MAAM,CAAC,8CAAqB,CAAC,eAAe,CAAC,yCAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACjF,MAAM,CAAC,8CAAqB,CAAC,eAAe,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvF,MAAM,CAAC,8CAAqB,CAAC,eAAe,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC7F,MAAM,CAAC,8CAAqB,CAAC,eAAe,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrG,MAAM,CAAC,8CAAqB,CAAC,eAAe,CAAC,yCAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAEpG,6CAA6C;gBAC7C,MAAM,WAAW,GAAG,8CAAqB,CAAC,eAAe,CAAC,yCAAgB,CAAC,KAAK,CAAC,CAAC;gBAClF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;YAC5B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,CAAC,8CAAqB,CAAC,YAAY,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAChF,MAAM,CAAC,8CAAqB,CAAC,YAAY,CAAC,yCAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC5E,MAAM,CAAC,8CAAqB,CAAC,YAAY,CAAC,yCAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvF,MAAM,CAAC,8CAAqB,CAAC,YAAY,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEhG,6CAA6C;gBAC7C,MAAM,WAAW,GAAG,8CAAqB,CAAC,YAAY,CAAC,yCAAgB,CAAC,KAAK,CAAC,CAAC;gBAC/E,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;gBACxD,MAAM,iBAAiB,GAAG,8CAAqB,CAAC,qBAAqB,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC;gBACtG,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBAClD,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;gBAExD,MAAM,eAAe,GAAG,8CAAqB,CAAC,qBAAqB,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC;gBACjG,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAC/C,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACzC,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBAE5C,MAAM,QAAQ,GAAG,8CAAqB,CAAC,qBAAqB,CAAC,yCAAgB,CAAC,YAAY,CAAC,CAAC;gBAC5F,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBACrC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBAEzC,6CAA6C;gBAC7C,MAAM,YAAY,GAAG,8CAAqB,CAAC,qBAAqB,CAAC,yCAAgB,CAAC,KAAK,CAAC,CAAC;gBACzF,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,cAAc,GAAG,8CAAqB,CAAC,cAAc,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC;gBACzF,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBAE/C,MAAM,SAAS,GAAG,8CAAqB,CAAC,cAAc,CAAC,yCAAgB,CAAC,KAAK,CAAC,CAAC;gBAC/E,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;gBAExD,MAAM,OAAO,GAAG,8CAAqB,CAAC,cAAc,CAAC,yCAAgB,CAAC,GAAG,CAAC,CAAC;gBAC3E,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;gBAE7D,2DAA2D;gBAC3D,MAAM,SAAS,GAAG,8CAAqB,CAAC,cAAc,CAAC,yCAAgB,CAAC,KAAK,CAAC,CAAC;gBAC/E,MAAM,CAAC,OAAO,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACxC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;YAC3B,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;gBAC1C,MAAM,CAAC,8CAAqB,CAAC,WAAW,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAChH,MAAM,CAAC,8CAAqB,CAAC,WAAW,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACtG,MAAM,CAAC,8CAAqB,CAAC,WAAW,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC7F,MAAM,CAAC,8CAAqB,CAAC,WAAW,CAAC,yCAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBACtF,MAAM,CAAC,8CAAqB,CAAC,WAAW,CAAC,yCAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAC1F,MAAM,CAAC,8CAAqB,CAAC,WAAW,CAAC,yCAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC9F,MAAM,CAAC,8CAAqB,CAAC,WAAW,CAAC,yCAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC3F,MAAM,CAAC,8CAAqB,CAAC,WAAW,CAAC,yCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACxF,MAAM,CAAC,8CAAqB,CAAC,WAAW,CAAC,yCAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACvF,MAAM,CAAC,8CAAqB,CAAC,WAAW,CAAC,yCAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACrF,MAAM,CAAC,8CAAqB,CAAC,WAAW,CAAC,yCAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvG,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,CAAC,8CAAqB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/D,MAAM,CAAC,8CAAqB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClE,MAAM,CAAC,8CAAqB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpE,MAAM,CAAC,8CAAqB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,yCAAgB,CAAC,UAAU,CAAC,CAAC;gBACzF,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC;gBAC/F,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,yCAAgB,CAAC,KAAK,CAAC,CAAC;gBAC/E,MAAM,CAAC,8CAAqB,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACxE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;gBACxD,MAAM,uBAAuB,GAAG,8CAAqB,CAAC,qBAAqB,CACzE,yCAAgB,CAAC,gBAAgB,EACjC,cAAc,CACf,CAAC;gBACF,MAAM,sBAAsB,GAAG,8CAAqB,CAAC,qBAAqB,CACxE,yCAAgB,CAAC,OAAO,EACxB,SAAS,CACV,CAAC;gBAEF,MAAM,CAAC,uBAAuB,CAAC,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC;gBACxE,MAAM,CAAC,uBAAuB,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBAC1D,MAAM,CAAC,uBAAuB,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,gBAAgB,GAAG,8CAAqB,CAAC,qBAAqB,CAClE,yCAAgB,CAAC,aAAa,EAC9B,YAAY,CACb,CAAC;gBACF,MAAM,aAAa,GAAG,8CAAqB,CAAC,qBAAqB,CAC/D,yCAAgB,CAAC,GAAG,EACpB,eAAe,CAChB,CAAC;gBAEF,8CAA8C;gBAC9C,MAAM,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,oBAAoB,GAAG,8CAAqB,CAAC,kBAAkB,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC;gBACtG,MAAM,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,0BAA0B;gBAEjE,MAAM,WAAW,GAAG,8CAAqB,CAAC,kBAAkB,CAAC,yCAAgB,CAAC,GAAG,CAAC,CAAC;gBACnF,MAAM,CAAC,oBAAoB,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,2CAA2C;gBAEnG,kCAAkC;gBAClC,MAAM,YAAY,GAAG;oBACnB,yCAAgB,CAAC,aAAa;oBAC9B,yCAAgB,CAAC,cAAc;oBAC/B,yCAAgB,CAAC,YAAY;iBAC9B,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,8CAAqB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;gBAElE,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAC9B,MAAM,CAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBAC7C,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\__tests__\\enrichment-source.enum.spec.ts"], "sourcesContent": ["import { EnrichmentSource, EnrichmentSourceUtils } from '../enrichment-source.enum';\r\n\r\ndescribe('EnrichmentSource', () => {\r\n  describe('enum values', () => {\r\n    it('should have all expected enrichment sources', () => {\r\n      expect(EnrichmentSource.COMMERCIAL_THREAT_INTEL).toBe('commercial_threat_intel');\r\n      expect(EnrichmentSource.OSINT).toBe('osint');\r\n      expect(EnrichmentSource.IP_REPUTATION).toBe('ip_reputation');\r\n      expect(EnrichmentSource.VIRUSTOTAL).toBe('virustotal');\r\n      expect(EnrichmentSource.NVD).toBe('nvd');\r\n      expect(EnrichmentSource.UNKNOWN).toBe('unknown');\r\n    });\r\n  });\r\n\r\n  describe('EnrichmentSourceUtils', () => {\r\n    describe('getAllSources', () => {\r\n      it('should return all enrichment sources', () => {\r\n        const sources = EnrichmentSourceUtils.getAllSources();\r\n        expect(sources.length).toBeGreaterThan(50); // We have many sources\r\n        expect(sources).toContain(EnrichmentSource.COMMERCIAL_THREAT_INTEL);\r\n        expect(sources).toContain(EnrichmentSource.VIRUSTOTAL);\r\n        expect(sources).toContain(EnrichmentSource.NVD);\r\n      });\r\n    });\r\n\r\n    describe('getThreatIntelSources', () => {\r\n      it('should return threat intelligence sources', () => {\r\n        const threatIntelSources = EnrichmentSourceUtils.getThreatIntelSources();\r\n        expect(threatIntelSources).toContain(EnrichmentSource.COMMERCIAL_THREAT_INTEL);\r\n        expect(threatIntelSources).toContain(EnrichmentSource.OSINT);\r\n        expect(threatIntelSources).toContain(EnrichmentSource.GOVERNMENT_INTEL);\r\n        expect(threatIntelSources).toContain(EnrichmentSource.MISP);\r\n        expect(threatIntelSources).not.toContain(EnrichmentSource.IP_REPUTATION);\r\n      });\r\n    });\r\n\r\n    describe('getReputationSources', () => {\r\n      it('should return reputation service sources', () => {\r\n        const reputationSources = EnrichmentSourceUtils.getReputationSources();\r\n        expect(reputationSources).toContain(EnrichmentSource.IP_REPUTATION);\r\n        expect(reputationSources).toContain(EnrichmentSource.DOMAIN_REPUTATION);\r\n        expect(reputationSources).toContain(EnrichmentSource.URL_REPUTATION);\r\n        expect(reputationSources).toContain(EnrichmentSource.FILE_REPUTATION);\r\n        expect(reputationSources).not.toContain(EnrichmentSource.VIRUSTOTAL);\r\n      });\r\n    });\r\n\r\n    describe('getExternalAPISources', () => {\r\n      it('should return external API sources', () => {\r\n        const externalAPISources = EnrichmentSourceUtils.getExternalAPISources();\r\n        expect(externalAPISources).toContain(EnrichmentSource.VIRUSTOTAL);\r\n        expect(externalAPISources).toContain(EnrichmentSource.SHODAN);\r\n        expect(externalAPISources).toContain(EnrichmentSource.CENSYS);\r\n        expect(externalAPISources).toContain(EnrichmentSource.PASSIVETOTAL);\r\n        expect(externalAPISources).not.toContain(EnrichmentSource.INTERNAL_SECURITY_TOOLS);\r\n      });\r\n    });\r\n\r\n    describe('getInternalSources', () => {\r\n      it('should return internal sources', () => {\r\n        const internalSources = EnrichmentSourceUtils.getInternalSources();\r\n        expect(internalSources).toContain(EnrichmentSource.INTERNAL_SECURITY_TOOLS);\r\n        expect(internalSources).toContain(EnrichmentSource.CUSTOM_RULES);\r\n        expect(internalSources).toContain(EnrichmentSource.MANUAL_ANALYSIS);\r\n        expect(internalSources).toContain(EnrichmentSource.AUTOMATED_CORRELATION);\r\n        expect(internalSources).not.toContain(EnrichmentSource.VIRUSTOTAL);\r\n      });\r\n    });\r\n\r\n    describe('getRealTimeSources', () => {\r\n      it('should return real-time sources', () => {\r\n        const realTimeSources = EnrichmentSourceUtils.getRealTimeSources();\r\n        expect(realTimeSources).toContain(EnrichmentSource.IP_REPUTATION);\r\n        expect(realTimeSources).toContain(EnrichmentSource.DNS_RESOLUTION);\r\n        expect(realTimeSources).toContain(EnrichmentSource.VIRUSTOTAL);\r\n        expect(realTimeSources).toContain(EnrichmentSource.ML_INFERENCE);\r\n        expect(realTimeSources).not.toContain(EnrichmentSource.NVD);\r\n      });\r\n    });\r\n\r\n    describe('getBatchSources', () => {\r\n      it('should return batch processing sources', () => {\r\n        const batchSources = EnrichmentSourceUtils.getBatchSources();\r\n        expect(batchSources).toContain(EnrichmentSource.COMMERCIAL_THREAT_INTEL);\r\n        expect(batchSources).toContain(EnrichmentSource.NVD);\r\n        expect(batchSources).toContain(EnrichmentSource.CVE_DATABASE);\r\n        expect(batchSources).toContain(EnrichmentSource.CMDB);\r\n        expect(batchSources).not.toContain(EnrichmentSource.IP_REPUTATION);\r\n      });\r\n    });\r\n\r\n    describe('isExternal', () => {\r\n      it('should correctly identify external sources', () => {\r\n        expect(EnrichmentSourceUtils.isExternal(EnrichmentSource.VIRUSTOTAL)).toBe(true);\r\n        expect(EnrichmentSourceUtils.isExternal(EnrichmentSource.SHODAN)).toBe(true);\r\n        expect(EnrichmentSourceUtils.isExternal(EnrichmentSource.IP_REPUTATION)).toBe(true);\r\n        expect(EnrichmentSourceUtils.isExternal(EnrichmentSource.COMMERCIAL_THREAT_INTEL)).toBe(true);\r\n        expect(EnrichmentSourceUtils.isExternal(EnrichmentSource.INTERNAL_SECURITY_TOOLS)).toBe(false);\r\n        expect(EnrichmentSourceUtils.isExternal(EnrichmentSource.MANUAL_ANALYSIS)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isInternal', () => {\r\n      it('should correctly identify internal sources', () => {\r\n        expect(EnrichmentSourceUtils.isInternal(EnrichmentSource.INTERNAL_SECURITY_TOOLS)).toBe(true);\r\n        expect(EnrichmentSourceUtils.isInternal(EnrichmentSource.CUSTOM_RULES)).toBe(true);\r\n        expect(EnrichmentSourceUtils.isInternal(EnrichmentSource.MANUAL_ANALYSIS)).toBe(true);\r\n        expect(EnrichmentSourceUtils.isInternal(EnrichmentSource.VIRUSTOTAL)).toBe(false);\r\n        expect(EnrichmentSourceUtils.isInternal(EnrichmentSource.SHODAN)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isRealTime', () => {\r\n      it('should correctly identify real-time sources', () => {\r\n        expect(EnrichmentSourceUtils.isRealTime(EnrichmentSource.IP_REPUTATION)).toBe(true);\r\n        expect(EnrichmentSourceUtils.isRealTime(EnrichmentSource.DNS_RESOLUTION)).toBe(true);\r\n        expect(EnrichmentSourceUtils.isRealTime(EnrichmentSource.VIRUSTOTAL)).toBe(true);\r\n        expect(EnrichmentSourceUtils.isRealTime(EnrichmentSource.NVD)).toBe(false);\r\n        expect(EnrichmentSourceUtils.isRealTime(EnrichmentSource.CMDB)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isBatchProcessed', () => {\r\n      it('should correctly identify batch processed sources', () => {\r\n        expect(EnrichmentSourceUtils.isBatchProcessed(EnrichmentSource.NVD)).toBe(true);\r\n        expect(EnrichmentSourceUtils.isBatchProcessed(EnrichmentSource.CVE_DATABASE)).toBe(true);\r\n        expect(EnrichmentSourceUtils.isBatchProcessed(EnrichmentSource.CMDB)).toBe(true);\r\n        expect(EnrichmentSourceUtils.isBatchProcessed(EnrichmentSource.IP_REPUTATION)).toBe(false);\r\n        expect(EnrichmentSourceUtils.isBatchProcessed(EnrichmentSource.DNS_RESOLUTION)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('getReliabilityScore', () => {\r\n      it('should return appropriate reliability scores', () => {\r\n        expect(EnrichmentSourceUtils.getReliabilityScore(EnrichmentSource.GOVERNMENT_INTEL)).toBe(95);\r\n        expect(EnrichmentSourceUtils.getReliabilityScore(EnrichmentSource.NVD)).toBe(95);\r\n        expect(EnrichmentSourceUtils.getReliabilityScore(EnrichmentSource.COMMERCIAL_THREAT_INTEL)).toBe(90);\r\n        expect(EnrichmentSourceUtils.getReliabilityScore(EnrichmentSource.VIRUSTOTAL)).toBe(85);\r\n        expect(EnrichmentSourceUtils.getReliabilityScore(EnrichmentSource.OSINT)).toBe(70);\r\n        expect(EnrichmentSourceUtils.getReliabilityScore(EnrichmentSource.UNKNOWN)).toBe(30);\r\n        \r\n        // Should return default for unmapped sources\r\n        const defaultScore = EnrichmentSourceUtils.getReliabilityScore(EnrichmentSource.OTHER);\r\n        expect(defaultScore).toBe(50);\r\n      });\r\n    });\r\n\r\n    describe('getFreshnessRequirement', () => {\r\n      it('should return appropriate freshness requirements in hours', () => {\r\n        expect(EnrichmentSourceUtils.getFreshnessRequirement(EnrichmentSource.IP_REPUTATION)).toBe(0.25); // 15 minutes\r\n        expect(EnrichmentSourceUtils.getFreshnessRequirement(EnrichmentSource.DNS_RESOLUTION)).toBe(0.5); // 30 minutes\r\n        expect(EnrichmentSourceUtils.getFreshnessRequirement(EnrichmentSource.COMMERCIAL_THREAT_INTEL)).toBe(1); // 1 hour\r\n        expect(EnrichmentSourceUtils.getFreshnessRequirement(EnrichmentSource.NVD)).toBe(24); // Daily\r\n        expect(EnrichmentSourceUtils.getFreshnessRequirement(EnrichmentSource.CMDB)).toBe(168); // Weekly\r\n        \r\n        // Should return default for unmapped sources\r\n        const defaultFreshness = EnrichmentSourceUtils.getFreshnessRequirement(EnrichmentSource.OTHER);\r\n        expect(defaultFreshness).toBe(24);\r\n      });\r\n    });\r\n\r\n    describe('getCostCategory', () => {\r\n      it('should return appropriate cost categories', () => {\r\n        expect(EnrichmentSourceUtils.getCostCategory(EnrichmentSource.OSINT)).toBe('free');\r\n        expect(EnrichmentSourceUtils.getCostCategory(EnrichmentSource.NVD)).toBe('free');\r\n        expect(EnrichmentSourceUtils.getCostCategory(EnrichmentSource.VIRUSTOTAL)).toBe('low');\r\n        expect(EnrichmentSourceUtils.getCostCategory(EnrichmentSource.IP_REPUTATION)).toBe('medium');\r\n        expect(EnrichmentSourceUtils.getCostCategory(EnrichmentSource.COMMERCIAL_THREAT_INTEL)).toBe('high');\r\n        expect(EnrichmentSourceUtils.getCostCategory(EnrichmentSource.GOVERNMENT_INTEL)).toBe('enterprise');\r\n        \r\n        // Should return default for unmapped sources\r\n        const defaultCost = EnrichmentSourceUtils.getCostCategory(EnrichmentSource.OTHER);\r\n        expect(defaultCost).toBe('medium');\r\n      });\r\n    });\r\n\r\n    describe('getRateLimit', () => {\r\n      it('should return appropriate rate limits', () => {\r\n        expect(EnrichmentSourceUtils.getRateLimit(EnrichmentSource.VIRUSTOTAL)).toBe(4);\r\n        expect(EnrichmentSourceUtils.getRateLimit(EnrichmentSource.SHODAN)).toBe(1);\r\n        expect(EnrichmentSourceUtils.getRateLimit(EnrichmentSource.DNS_RESOLUTION)).toBe(1000);\r\n        expect(EnrichmentSourceUtils.getRateLimit(EnrichmentSource.COMMERCIAL_THREAT_INTEL)).toBe(1000);\r\n        \r\n        // Should return default for unmapped sources\r\n        const defaultRate = EnrichmentSourceUtils.getRateLimit(EnrichmentSource.OTHER);\r\n        expect(defaultRate).toBe(100);\r\n      });\r\n    });\r\n\r\n    describe('getSupportedDataTypes', () => {\r\n      it('should return supported data types for sources', () => {\r\n        const ipReputationTypes = EnrichmentSourceUtils.getSupportedDataTypes(EnrichmentSource.IP_REPUTATION);\r\n        expect(ipReputationTypes).toContain('ip_address');\r\n        expect(ipReputationTypes).toContain('reputation_score');\r\n        \r\n        const virusTotalTypes = EnrichmentSourceUtils.getSupportedDataTypes(EnrichmentSource.VIRUSTOTAL);\r\n        expect(virusTotalTypes).toContain('file_hash');\r\n        expect(virusTotalTypes).toContain('url');\r\n        expect(virusTotalTypes).toContain('domain');\r\n        \r\n        const cveTypes = EnrichmentSourceUtils.getSupportedDataTypes(EnrichmentSource.CVE_DATABASE);\r\n        expect(cveTypes).toContain('cve_id');\r\n        expect(cveTypes).toContain('cvss_score');\r\n        \r\n        // Should return default for unmapped sources\r\n        const defaultTypes = EnrichmentSourceUtils.getSupportedDataTypes(EnrichmentSource.OTHER);\r\n        expect(defaultTypes).toContain('generic_data');\r\n      });\r\n    });\r\n\r\n    describe('getDescription', () => {\r\n      it('should return meaningful descriptions', () => {\r\n        const virusTotalDesc = EnrichmentSourceUtils.getDescription(EnrichmentSource.VIRUSTOTAL);\r\n        expect(virusTotalDesc).toContain('VirusTotal');\r\n        \r\n        const osintDesc = EnrichmentSourceUtils.getDescription(EnrichmentSource.OSINT);\r\n        expect(osintDesc).toContain('Open source intelligence');\r\n        \r\n        const nvdDesc = EnrichmentSourceUtils.getDescription(EnrichmentSource.NVD);\r\n        expect(nvdDesc).toContain('National Vulnerability Database');\r\n        \r\n        // Should return formatted description for unmapped sources\r\n        const otherDesc = EnrichmentSourceUtils.getDescription(EnrichmentSource.OTHER);\r\n        expect(typeof otherDesc).toBe('string');\r\n        expect(otherDesc.length).toBeGreaterThan(0);\r\n      });\r\n    });\r\n\r\n    describe('getCategory', () => {\r\n      it('should return correct categories', () => {\r\n        expect(EnrichmentSourceUtils.getCategory(EnrichmentSource.COMMERCIAL_THREAT_INTEL)).toBe('Threat Intelligence');\r\n        expect(EnrichmentSourceUtils.getCategory(EnrichmentSource.IP_REPUTATION)).toBe('Reputation Services');\r\n        expect(EnrichmentSourceUtils.getCategory(EnrichmentSource.VIRUSTOTAL)).toBe('External APIs');\r\n        expect(EnrichmentSourceUtils.getCategory(EnrichmentSource.NVD)).toBe('Vulnerability');\r\n        expect(EnrichmentSourceUtils.getCategory(EnrichmentSource.CMDB)).toBe('Asset Management');\r\n        expect(EnrichmentSourceUtils.getCategory(EnrichmentSource.ACTIVE_DIRECTORY)).toBe('Identity');\r\n        expect(EnrichmentSourceUtils.getCategory(EnrichmentSource.DNS_RESOLUTION)).toBe('Network');\r\n        expect(EnrichmentSourceUtils.getCategory(EnrichmentSource.ML_MODELS)).toBe('Analytics');\r\n        expect(EnrichmentSourceUtils.getCategory(EnrichmentSource.AWS_SECURITY)).toBe('Cloud');\r\n        expect(EnrichmentSourceUtils.getCategory(EnrichmentSource.SANDBOX)).toBe('Analysis');\r\n        expect(EnrichmentSourceUtils.getCategory(EnrichmentSource.INTERNAL_SECURITY_TOOLS)).toBe('Internal');\r\n      });\r\n    });\r\n\r\n    describe('isValid', () => {\r\n      it('should validate enrichment source strings', () => {\r\n        expect(EnrichmentSourceUtils.isValid('virustotal')).toBe(true);\r\n        expect(EnrichmentSourceUtils.isValid('ip_reputation')).toBe(true);\r\n        expect(EnrichmentSourceUtils.isValid('invalid_source')).toBe(false);\r\n        expect(EnrichmentSourceUtils.isValid('')).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('fromString', () => {\r\n      it('should convert string to enrichment source', () => {\r\n        expect(EnrichmentSourceUtils.fromString('virustotal')).toBe(EnrichmentSource.VIRUSTOTAL);\r\n        expect(EnrichmentSourceUtils.fromString('ip_reputation')).toBe(EnrichmentSource.IP_REPUTATION);\r\n        expect(EnrichmentSourceUtils.fromString('OSINT')).toBe(EnrichmentSource.OSINT);\r\n        expect(EnrichmentSourceUtils.fromString('invalid_source')).toBeNull();\r\n      });\r\n    });\r\n\r\n    describe('getEnrichmentPriority', () => {\r\n      it('should calculate enrichment priority correctly', () => {\r\n        const highReliabilityPriority = EnrichmentSourceUtils.getEnrichmentPriority(\r\n          EnrichmentSource.GOVERNMENT_INTEL, \r\n          'threat_intel'\r\n        );\r\n        const lowReliabilityPriority = EnrichmentSourceUtils.getEnrichmentPriority(\r\n          EnrichmentSource.UNKNOWN, \r\n          'generic'\r\n        );\r\n        \r\n        expect(highReliabilityPriority).toBeGreaterThan(lowReliabilityPriority);\r\n        expect(highReliabilityPriority).toBeGreaterThanOrEqual(0);\r\n        expect(highReliabilityPriority).toBeLessThanOrEqual(100);\r\n      });\r\n\r\n      it('should boost priority for real-time sources', () => {\r\n        const realTimePriority = EnrichmentSourceUtils.getEnrichmentPriority(\r\n          EnrichmentSource.IP_REPUTATION, \r\n          'ip_address'\r\n        );\r\n        const batchPriority = EnrichmentSourceUtils.getEnrichmentPriority(\r\n          EnrichmentSource.NVD, \r\n          'vulnerability'\r\n        );\r\n        \r\n        // Real-time sources should get priority boost\r\n        expect(realTimePriority).toBeGreaterThan(batchPriority);\r\n      });\r\n    });\r\n\r\n    describe('getRefreshInterval', () => {\r\n      it('should return refresh interval in minutes', () => {\r\n        const ipReputationInterval = EnrichmentSourceUtils.getRefreshInterval(EnrichmentSource.IP_REPUTATION);\r\n        expect(ipReputationInterval).toBe(15); // 0.25 hours * 60 minutes\r\n        \r\n        const nvdInterval = EnrichmentSourceUtils.getRefreshInterval(EnrichmentSource.NVD);\r\n        expect(ipReputationInterval).toBeLessThan(nvdInterval); // Real-time should refresh more frequently\r\n        \r\n        // Should have minimum of 1 minute\r\n        const allIntervals = [\r\n          EnrichmentSource.IP_REPUTATION,\r\n          EnrichmentSource.DNS_RESOLUTION,\r\n          EnrichmentSource.ML_INFERENCE\r\n        ].map(source => EnrichmentSourceUtils.getRefreshInterval(source));\r\n        \r\n        allIntervals.forEach(interval => {\r\n          expect(interval).toBeGreaterThanOrEqual(1);\r\n        });\r\n      });\r\n    });\r\n  });\r\n});"], "version": 3}