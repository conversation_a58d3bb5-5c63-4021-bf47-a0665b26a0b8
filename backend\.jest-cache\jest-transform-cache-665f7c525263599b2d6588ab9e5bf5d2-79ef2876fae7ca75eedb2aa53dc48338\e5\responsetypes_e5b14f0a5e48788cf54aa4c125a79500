244196d1c73b756c7266661fb4a7fbd8
"use strict";
/**
 * Response Types
 *
 * Common types and interfaces for API responses across the application.
 * Provides consistent response patterns and utilities.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseUtils = exports.ResponseStatus = exports.HttpStatus = void 0;
/**
 * HTTP Status Codes
 */
var HttpStatus;
(function (HttpStatus) {
    // Success
    HttpStatus[HttpStatus["OK"] = 200] = "OK";
    HttpStatus[HttpStatus["CREATED"] = 201] = "CREATED";
    HttpStatus[HttpStatus["ACCEPTED"] = 202] = "ACCEPTED";
    HttpStatus[HttpStatus["NO_CONTENT"] = 204] = "NO_CONTENT";
    // Redirection
    HttpStatus[HttpStatus["MOVED_PERMANENTLY"] = 301] = "MOVED_PERMANENTLY";
    HttpStatus[HttpStatus["FOUND"] = 302] = "FOUND";
    HttpStatus[HttpStatus["NOT_MODIFIED"] = 304] = "NOT_MODIFIED";
    // Client Error
    HttpStatus[HttpStatus["BAD_REQUEST"] = 400] = "BAD_REQUEST";
    HttpStatus[HttpStatus["UNAUTHORIZED"] = 401] = "UNAUTHORIZED";
    HttpStatus[HttpStatus["FORBIDDEN"] = 403] = "FORBIDDEN";
    HttpStatus[HttpStatus["NOT_FOUND"] = 404] = "NOT_FOUND";
    HttpStatus[HttpStatus["METHOD_NOT_ALLOWED"] = 405] = "METHOD_NOT_ALLOWED";
    HttpStatus[HttpStatus["CONFLICT"] = 409] = "CONFLICT";
    HttpStatus[HttpStatus["UNPROCESSABLE_ENTITY"] = 422] = "UNPROCESSABLE_ENTITY";
    HttpStatus[HttpStatus["TOO_MANY_REQUESTS"] = 429] = "TOO_MANY_REQUESTS";
    // Server Error
    HttpStatus[HttpStatus["INTERNAL_SERVER_ERROR"] = 500] = "INTERNAL_SERVER_ERROR";
    HttpStatus[HttpStatus["NOT_IMPLEMENTED"] = 501] = "NOT_IMPLEMENTED";
    HttpStatus[HttpStatus["BAD_GATEWAY"] = 502] = "BAD_GATEWAY";
    HttpStatus[HttpStatus["SERVICE_UNAVAILABLE"] = 503] = "SERVICE_UNAVAILABLE";
    HttpStatus[HttpStatus["GATEWAY_TIMEOUT"] = 504] = "GATEWAY_TIMEOUT";
})(HttpStatus || (exports.HttpStatus = HttpStatus = {}));
/**
 * Response Status
 */
var ResponseStatus;
(function (ResponseStatus) {
    ResponseStatus["SUCCESS"] = "success";
    ResponseStatus["ERROR"] = "error";
    ResponseStatus["WARNING"] = "warning";
    ResponseStatus["INFO"] = "info";
})(ResponseStatus || (exports.ResponseStatus = ResponseStatus = {}));
/**
 * Response Utilities
 */
class ResponseUtils {
    /**
     * Create a success response
     */
    static success(data, message = 'Operation completed successfully', statusCode = HttpStatus.OK, meta) {
        return {
            status: ResponseStatus.SUCCESS,
            statusCode,
            message,
            data,
            timestamp: new Date().toISOString(),
            meta,
        };
    }
    /**
     * Create an error response
     */
    static error(error, message = 'Operation failed', statusCode = HttpStatus.INTERNAL_SERVER_ERROR, additionalErrors) {
        return {
            status: ResponseStatus.ERROR,
            statusCode,
            message,
            error,
            errors: additionalErrors,
            timestamp: new Date().toISOString(),
        };
    }
    /**
     * Create a validation error response
     */
    static validationError(errors, message = 'Validation failed') {
        const primaryError = {
            code: 'VALIDATION_ERROR',
            message: 'One or more validation errors occurred',
        };
        return ResponseUtils.error(primaryError, message, HttpStatus.UNPROCESSABLE_ENTITY, errors);
    }
    /**
     * Create a not found response
     */
    static notFound(resource, identifier) {
        const message = identifier
            ? `${resource} with identifier '${identifier}' not found`
            : `${resource} not found`;
        const error = {
            code: 'NOT_FOUND',
            message,
            context: { resource, identifier },
        };
        return ResponseUtils.error(error, message, HttpStatus.NOT_FOUND);
    }
    /**
     * Create an unauthorized response
     */
    static unauthorized(message = 'Authentication required') {
        const error = {
            code: 'UNAUTHORIZED',
            message,
        };
        return ResponseUtils.error(error, message, HttpStatus.UNAUTHORIZED);
    }
    /**
     * Create a forbidden response
     */
    static forbidden(message = 'Access denied') {
        const error = {
            code: 'FORBIDDEN',
            message,
        };
        return ResponseUtils.error(error, message, HttpStatus.FORBIDDEN);
    }
    /**
     * Create a conflict response
     */
    static conflict(message = 'Resource conflict', context) {
        const error = {
            code: 'CONFLICT',
            message,
            context,
        };
        return ResponseUtils.error(error, message, HttpStatus.CONFLICT);
    }
    /**
     * Create a rate limit response
     */
    static rateLimited(retryAfter, message = 'Rate limit exceeded') {
        const error = {
            code: 'RATE_LIMITED',
            message,
            context: { retryAfter },
        };
        return ResponseUtils.error(error, message, HttpStatus.TOO_MANY_REQUESTS);
    }
    /**
     * Create a paginated response
     */
    static paginated(items, pagination, baseUrl, message = 'Data retrieved successfully') {
        const links = {
            first: `${baseUrl}?page=1&limit=${pagination.itemsPerPage}`,
            last: `${baseUrl}?page=${pagination.totalPages}&limit=${pagination.itemsPerPage}`,
            self: `${baseUrl}?page=${pagination.currentPage}&limit=${pagination.itemsPerPage}`,
            ...(pagination.hasNextPage && pagination.nextPage && {
                next: `${baseUrl}?page=${pagination.nextPage}&limit=${pagination.itemsPerPage}`,
            }),
            ...(pagination.hasPreviousPage && pagination.previousPage && {
                previous: `${baseUrl}?page=${pagination.previousPage}&limit=${pagination.itemsPerPage}`,
            }),
        };
        return {
            status: ResponseStatus.SUCCESS,
            statusCode: HttpStatus.OK,
            message,
            data: items,
            pagination,
            links,
            timestamp: new Date().toISOString(),
        };
    }
    /**
     * Create a bulk operation response
     */
    static bulkOperation(results, message) {
        const successful = results.filter(r => r.status === 'success').length;
        const failed = results.filter(r => r.status === 'error').length;
        const total = results.length;
        // Create error summary
        const errorSummary = {};
        results
            .filter(r => r.status === 'error' && r.error)
            .forEach(r => {
            const code = r.error.code;
            errorSummary[code] = (errorSummary[code] || 0) + 1;
        });
        const defaultMessage = failed === 0
            ? `All ${total} operations completed successfully`
            : `${successful} of ${total} operations completed successfully, ${failed} failed`;
        return {
            status: failed === 0 ? ResponseStatus.SUCCESS : ResponseStatus.WARNING,
            statusCode: failed === 0 ? HttpStatus.OK : HttpStatus.ACCEPTED,
            message: message || defaultMessage,
            timestamp: new Date().toISOString(),
            total,
            successful,
            failed,
            results,
            errorSummary: Object.keys(errorSummary).length > 0 ? errorSummary : undefined,
        };
    }
    /**
     * Create a health check response
     */
    static healthCheck(checks, systemInfo) {
        const healthy = Object.values(checks).every(check => check.status === 'healthy');
        const now = new Date().toISOString();
        // Add lastChecked timestamp to all checks
        const checksWithTimestamp = Object.entries(checks).reduce((acc, [key, check]) => {
            acc[key] = {
                ...check,
                lastChecked: now,
            };
            return acc;
        }, {});
        return {
            status: healthy ? ResponseStatus.SUCCESS : ResponseStatus.ERROR,
            statusCode: healthy ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE,
            message: healthy ? 'System is healthy' : 'System has health issues',
            timestamp: now,
            healthy,
            checks: checksWithTimestamp,
            system: {
                uptime: systemInfo.uptime,
                memory: {
                    used: systemInfo.memory.used,
                    total: systemInfo.memory.total,
                    percentage: Math.round((systemInfo.memory.used / systemInfo.memory.total) * 100),
                },
                cpu: {
                    usage: systemInfo.cpu.usage,
                },
            },
        };
    }
    /**
     * Create a file upload response
     */
    static fileUpload(fileInfo, metadata, message = 'File uploaded successfully') {
        return {
            status: ResponseStatus.SUCCESS,
            statusCode: HttpStatus.CREATED,
            message,
            timestamp: new Date().toISOString(),
            data: {
                file: fileInfo,
                metadata,
            },
        };
    }
    /**
     * Check if response is successful
     */
    static isSuccess(response) {
        return response.status === ResponseStatus.SUCCESS;
    }
    /**
     * Check if response is an error
     */
    static isError(response) {
        return response.status === ResponseStatus.ERROR;
    }
    /**
     * Extract error message from response
     */
    static getErrorMessage(response) {
        if (ResponseUtils.isError(response)) {
            return response.error.message || response.message;
        }
        return response.message;
    }
    /**
     * Extract all error messages from response
     */
    static getAllErrorMessages(response) {
        if (!ResponseUtils.isError(response)) {
            return [];
        }
        const messages = [response.error.message];
        if (response.errors) {
            messages.push(...response.errors.map(error => error.message));
        }
        return messages.filter(Boolean);
    }
    /**
     * Create response with request ID
     */
    static withRequestId(response, requestId) {
        return {
            ...response,
            requestId,
        };
    }
    /**
     * Create response with version
     */
    static withVersion(response, version) {
        return {
            ...response,
            version,
        };
    }
}
exports.ResponseUtils = ResponseUtils;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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