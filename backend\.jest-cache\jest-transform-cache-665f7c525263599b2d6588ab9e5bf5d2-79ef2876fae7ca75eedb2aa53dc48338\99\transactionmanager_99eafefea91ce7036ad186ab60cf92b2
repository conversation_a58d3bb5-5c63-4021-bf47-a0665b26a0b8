9173293fbd38f3a90e0e0e17ac634294
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TransactionManager_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionManager = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
// Helper function to safely extract error information
const getErrorInfo = (error) => ({
    message: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
});
/**
 * Transaction manager for handling database transactions
 * Provides utilities for transaction management, rollback, and error handling
 */
let TransactionManager = TransactionManager_1 = class TransactionManager {
    constructor(dataSource) {
        this.logger = new common_1.Logger(TransactionManager_1.name);
        this.dataSource = dataSource;
    }
    /**
     * Execute a function within a database transaction
     * Automatically handles commit/rollback based on success/failure
     *
     * @param operation Function to execute within transaction
     * @param isolationLevel Transaction isolation level
     * @returns Promise<T> Result of the operation
     */
    async executeInTransaction(operation, isolationLevel) {
        const queryRunner = this.dataSource.createQueryRunner();
        try {
            await queryRunner.connect();
            if (isolationLevel) {
                await queryRunner.startTransaction(isolationLevel);
            }
            else {
                await queryRunner.startTransaction();
            }
            this.logger.debug('Transaction started', {
                isolationLevel: isolationLevel || 'default',
                connectionId: queryRunner.connection.options.database,
            });
            const result = await operation(queryRunner.manager);
            await queryRunner.commitTransaction();
            this.logger.debug('Transaction committed successfully');
            return result;
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error('Transaction failed, rolling back', {
                error: errorInfo.message,
                stack: errorInfo.stack,
            });
            try {
                await queryRunner.rollbackTransaction();
                this.logger.debug('Transaction rolled back successfully');
            }
            catch (rollbackError) {
                this.logger.error('Failed to rollback transaction', {
                    error: getErrorInfo(rollbackError).message,
                });
            }
            throw error;
        }
        finally {
            try {
                await queryRunner.release();
                this.logger.debug('Query runner released');
            }
            catch (releaseError) {
                this.logger.error('Failed to release query runner', {
                    error: getErrorInfo(releaseError).message,
                });
            }
        }
    }
    /**
     * Execute multiple operations in a single transaction
     * All operations must succeed or all will be rolled back
     *
     * @param operations Array of operations to execute
     * @param isolationLevel Transaction isolation level
     * @returns Promise<T[]> Array of operation results
     */
    async executeMultipleInTransaction(operations, isolationLevel) {
        return this.executeInTransaction(async (manager) => {
            const results = [];
            for (let i = 0; i < operations.length; i++) {
                try {
                    this.logger.debug(`Executing operation ${i + 1}/${operations.length}`);
                    const result = await operations[i](manager);
                    results.push(result);
                }
                catch (error) {
                    const errorInfo = getErrorInfo(error);
                    this.logger.error(`Operation ${i + 1} failed`, {
                        error: errorInfo.message,
                        operationIndex: i,
                    });
                    throw error;
                }
            }
            return results;
        }, isolationLevel);
    }
    /**
     * Execute a batch of operations with savepoints
     * Allows partial rollback to specific savepoints
     *
     * @param operations Array of operations with savepoint names
     * @returns Promise<T[]> Array of operation results
     */
    async executeWithSavepoints(operations) {
        const queryRunner = this.dataSource.createQueryRunner();
        const results = [];
        try {
            await queryRunner.connect();
            await queryRunner.startTransaction();
            this.logger.debug('Transaction with savepoints started', {
                operationCount: operations.length,
            });
            for (let i = 0; i < operations.length; i++) {
                const { name, operation } = operations[i];
                try {
                    // Create savepoint
                    await queryRunner.query(`SAVEPOINT ${name}`);
                    this.logger.debug(`Savepoint '${name}' created`);
                    // Execute operation
                    const result = await operation(queryRunner.manager);
                    results.push(result);
                    this.logger.debug(`Operation '${name}' completed successfully`);
                }
                catch (error) {
                    const errorInfo = getErrorInfo(error);
                    this.logger.error(`Operation '${name}' failed, rolling back to savepoint`, {
                        error: errorInfo.message,
                        operationIndex: i,
                    });
                    try {
                        await queryRunner.query(`ROLLBACK TO SAVEPOINT ${name}`);
                        this.logger.debug(`Rolled back to savepoint '${name}'`);
                    }
                    catch (rollbackError) {
                        this.logger.error(`Failed to rollback to savepoint '${name}'`, {
                            error: getErrorInfo(rollbackError).message,
                        });
                        throw rollbackError;
                    }
                    throw error;
                }
            }
            await queryRunner.commitTransaction();
            this.logger.debug('Transaction with savepoints committed successfully');
            return results;
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error('Transaction with savepoints failed', {
                error: errorInfo.message,
            });
            try {
                await queryRunner.rollbackTransaction();
                this.logger.debug('Transaction rolled back');
            }
            catch (rollbackError) {
                this.logger.error('Failed to rollback transaction', {
                    error: getErrorInfo(rollbackError).message,
                });
            }
            throw error;
        }
        finally {
            try {
                await queryRunner.release();
            }
            catch (releaseError) {
                this.logger.error('Failed to release query runner', {
                    error: getErrorInfo(releaseError).message,
                });
            }
        }
    }
    /**
     * Execute operation with retry logic in case of deadlocks
     *
     * @param operation Function to execute
     * @param maxRetries Maximum number of retry attempts
     * @param retryDelay Delay between retries in milliseconds
     * @returns Promise<T> Result of the operation
     */
    async executeWithRetry(operation, maxRetries = 3, retryDelay = 1000) {
        let lastError;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                this.logger.debug(`Executing transaction attempt ${attempt}/${maxRetries}`);
                return await this.executeInTransaction(operation);
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                // Check if error is retryable (deadlock, serialization failure, etc.)
                const isRetryable = this.isRetryableError(error);
                if (!isRetryable || attempt === maxRetries) {
                    const errorInfo = getErrorInfo(error);
                    this.logger.error(`Transaction failed after ${attempt} attempts`, {
                        error: errorInfo.message,
                        isRetryable,
                        finalAttempt: attempt === maxRetries,
                    });
                    throw error;
                }
                const errorInfo = getErrorInfo(error);
                this.logger.warn(`Transaction failed, retrying in ${retryDelay}ms`, {
                    error: errorInfo.message,
                    attempt,
                    maxRetries,
                });
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }
        throw lastError;
    }
    /**
     * Check if an error is retryable (deadlock, serialization failure, etc.)
     *
     * @param error Error to check
     * @returns boolean True if error is retryable
     */
    isRetryableError(error) {
        if (!error.code) {
            return false;
        }
        // PostgreSQL error codes that are retryable
        const retryableErrorCodes = [
            '40001', // serialization_failure
            '40P01', // deadlock_detected
            '53300', // too_many_connections
            '08006', // connection_failure
            '08001', // sqlclient_unable_to_establish_sqlconnection
        ];
        return retryableErrorCodes.includes(error.code);
    }
    /**
     * Get transaction statistics
     *
     * @returns Promise<Record<string, any>> Transaction statistics
     */
    async getTransactionStats() {
        try {
            const stats = await this.dataSource.query(`
        SELECT 
          xact_commit as committed_transactions,
          xact_rollback as rolled_back_transactions,
          deadlocks,
          temp_files,
          temp_bytes
        FROM pg_stat_database 
        WHERE datname = current_database()
      `);
            return {
                ...stats[0],
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error('Failed to get transaction statistics', {
                error: errorInfo.message,
            });
            return {
                error: 'Failed to retrieve transaction statistics',
                timestamp: new Date().toISOString(),
            };
        }
    }
    /**
     * Get current active transactions
     *
     * @returns Promise<any[]> List of active transactions
     */
    async getActiveTransactions() {
        try {
            return await this.dataSource.query(`
        SELECT 
          pid,
          usename,
          application_name,
          client_addr,
          state,
          query_start,
          xact_start,
          query
        FROM pg_stat_activity 
        WHERE state IN ('active', 'idle in transaction')
        AND pid != pg_backend_pid()
        ORDER BY xact_start
      `);
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error('Failed to get active transactions', {
                error: errorInfo.message,
            });
            return [];
        }
    }
};
exports.TransactionManager = TransactionManager;
exports.TransactionManager = TransactionManager = TransactionManager_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.DataSource !== "undefined" && typeorm_2.DataSource) === "function" ? _a : Object])
], TransactionManager);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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