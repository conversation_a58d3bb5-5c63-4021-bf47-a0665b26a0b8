e8012e34738893e4debc46cdb143a61e
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorrelatedEventFactory = void 0;
const correlated_event_entity_1 = require("../entities/correlated-event.entity");
const shared_kernel_1 = require("../../../../shared-kernel");
const event_severity_enum_1 = require("../enums/event-severity.enum");
const event_processing_status_enum_1 = require("../enums/event-processing-status.enum");
const confidence_level_enum_1 = require("../enums/confidence-level.enum");
/**
 * CorrelatedEvent Factory
 *
 * Factory class for creating CorrelatedEvent entities with proper validation and defaults.
 * Handles complex correlation scenarios and ensures all business rules are applied.
 *
 * Key responsibilities:
 * - Create correlated events from enriched events
 * - Apply correlation rules and validation
 * - Calculate correlation quality scores
 * - Determine manual review requirements
 * - Handle batch correlation operations
 * - Support attack chain detection and analysis
 * - Manage temporal, spatial, and behavioral correlations
 */
class CorrelatedEventFactory {
    /**
     * Create a new CorrelatedEvent from an EnrichedEvent
     */
    static create(options) {
        const enrichedEvent = options.enrichedEvent;
        // Generate correlation ID if not provided
        const correlationId = options.correlationId || CorrelatedEventFactory.generateCorrelationId();
        // Determine confidence level
        const confidenceLevel = options.confidenceLevel ||
            CorrelatedEventFactory.calculateConfidenceLevel(options.correlationMatches || []);
        // Build correlated event properties
        const correlatedEventProps = {
            enrichedEventId: enrichedEvent.id,
            metadata: enrichedEvent.metadata,
            type: options.type || enrichedEvent.type,
            severity: options.severity || enrichedEvent.severity,
            status: options.status || enrichedEvent.status,
            processingStatus: options.processingStatus || event_processing_status_enum_1.EventProcessingStatus.CORRELATED,
            correlationStatus: options.correlationStatus || correlated_event_entity_1.CorrelationStatus.PENDING,
            enrichedData: enrichedEvent.enrichedData,
            correlatedData: options.correlatedData,
            title: options.title || enrichedEvent.title,
            description: options.description || enrichedEvent.description,
            tags: options.tags || enrichedEvent.tags,
            riskScore: options.riskScore || enrichedEvent.riskScore,
            confidenceLevel,
            attributes: {
                ...enrichedEvent.attributes,
                ...options.attributes,
            },
            correlationId,
            parentEventId: options.parentEventId || enrichedEvent.parentEventId,
            childEventIds: options.childEventIds || [],
            appliedRules: options.appliedRules || [],
            correlationMatches: options.correlationMatches || [],
            relatedEventIds: options.relatedEventIds || [],
            correlationPatterns: options.correlationPatterns || [],
            attackChain: options.attackChain,
            temporalCorrelation: options.temporalCorrelation,
            spatialCorrelation: options.spatialCorrelation,
            behavioralCorrelation: options.behavioralCorrelation,
            correlationQualityScore: options.correlationQualityScore,
            requiresManualReview: options.forceManualReview,
            validationErrors: options.validationErrors,
            correlationAttempts: 0,
        };
        return correlated_event_entity_1.CorrelatedEvent.create(correlatedEventProps, options.id);
    }
    /**
     * Create a CorrelatedEvent with automatic correlation
     */
    static createWithCorrelation(enrichedEvent, config = {}) {
        const fullConfig = CorrelatedEventFactory.getDefaultConfig(config);
        // Apply correlation rules
        const correlationResult = CorrelatedEventFactory.applyCorrelationRules(enrichedEvent.enrichedData, fullConfig.availableRules, fullConfig.enabledRuleTypes);
        // Calculate correlation quality score
        const correlationQualityScore = CorrelatedEventFactory.calculateCorrelationQualityScore(correlationResult, fullConfig);
        // Calculate confidence level
        const confidenceLevel = CorrelatedEventFactory.calculateConfidenceLevel(correlationResult.correlationMatches);
        // Determine if manual review is required
        const requiresManualReview = CorrelatedEventFactory.determineManualReviewRequirement(enrichedEvent, correlationQualityScore, confidenceLevel, correlationResult.validationErrors, fullConfig);
        return CorrelatedEventFactory.create({
            enrichedEvent,
            correlatedData: correlationResult.correlatedData,
            appliedRules: correlationResult.appliedRules,
            correlationMatches: correlationResult.correlationMatches,
            correlationStatus: correlationResult.success
                ? correlated_event_entity_1.CorrelationStatus.COMPLETED
                : correlated_event_entity_1.CorrelationStatus.FAILED,
            confidenceLevel,
            correlationQualityScore,
            relatedEventIds: correlationResult.relatedEventIds,
            correlationPatterns: correlationResult.correlationPatterns,
            temporalCorrelation: correlationResult.temporalCorrelation,
            spatialCorrelation: correlationResult.spatialCorrelation,
            behavioralCorrelation: correlationResult.behavioralCorrelation,
            validationErrors: correlationResult.validationErrors,
            forceManualReview: requiresManualReview,
        });
    }
    /**
     * Create a CorrelatedEvent with attack chain detection
     */
    static createWithAttackChainDetection(enrichedEvent, attackChainOptions) {
        // Detect attack chain
        const attackChainResult = CorrelatedEventFactory.detectAttackChain(enrichedEvent, attackChainOptions);
        // Build correlated data with attack chain context
        const correlatedData = {
            ...enrichedEvent.enrichedData,
            ...attackChainResult.correlatedData,
            attack_chain: attackChainResult.attackChain ? {
                id: attackChainResult.attackChain.id,
                name: attackChainResult.attackChain.name,
                stages: attackChainResult.attackChain.stages.length,
                confidence: attackChainResult.attackChain.confidence,
                severity: attackChainResult.attackChain.severity,
            } : null,
        };
        return CorrelatedEventFactory.create({
            enrichedEvent,
            correlatedData,
            appliedRules: attackChainResult.appliedRules,
            correlationMatches: attackChainResult.correlationMatches,
            correlationStatus: correlated_event_entity_1.CorrelationStatus.COMPLETED,
            confidenceLevel: attackChainResult.attackChain?.confidence || confidence_level_enum_1.ConfidenceLevel.MEDIUM,
            attackChain: attackChainResult.attackChain,
            relatedEventIds: attackChainResult.relatedEventIds,
            correlationPatterns: attackChainResult.correlationPatterns,
            correlationQualityScore: attackChainResult.qualityScore,
        });
    }
    /**
     * Create multiple CorrelatedEvents in batch
     */
    static createBatch(options) {
        const startTime = Date.now();
        const successful = [];
        const failed = [];
        for (const enrichedEvent of options.enrichedEvents) {
            try {
                const correlatedEvent = CorrelatedEventFactory.create({
                    enrichedEvent,
                    correlatedData: CorrelatedEventFactory.correlateEventData(enrichedEvent.enrichedData, options.ruleTypes),
                    appliedRules: options.rules,
                });
                successful.push(correlatedEvent);
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                failed.push({ enrichedEvent, error: errorMessage });
                if (options.stopOnFailure) {
                    break;
                }
            }
        }
        const processingTimeMs = Date.now() - startTime;
        return {
            successful,
            failed,
            summary: {
                total: options.enrichedEvents.length,
                successful: successful.length,
                failed: failed.length,
                processingTimeMs,
            },
        };
    }
    /**
     * Create a CorrelatedEvent for testing purposes
     */
    static createForTesting(overrides = {}) {
        // Create a mock enriched event if not provided
        const mockEnrichedEvent = overrides.enrichedEvent ||
            CorrelatedEventFactory.createMockEnrichedEvent();
        const defaultOptions = {
            enrichedEvent: mockEnrichedEvent,
            correlatedData: {
                ...mockEnrichedEvent.enrichedData,
                correlated: true,
                correlation_timestamp: new Date().toISOString(),
                correlation_patterns: ['temporal_sequence', 'ip_clustering'],
                attack_chain: {
                    detected: true,
                    stages: 3,
                    confidence: 'high',
                },
                related_events: 5,
            },
            appliedRules: [
                CorrelatedEventFactory.createMockCorrelationRule('temporal_correlation'),
                CorrelatedEventFactory.createMockCorrelationRule('spatial_correlation'),
            ],
            correlationMatches: [
                CorrelatedEventFactory.createMockCorrelationMatch(),
                CorrelatedEventFactory.createMockCorrelationMatch(),
            ],
            correlationStatus: correlated_event_entity_1.CorrelationStatus.COMPLETED,
            confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH,
            correlationQualityScore: 85,
            relatedEventIds: [new shared_kernel_1.UniqueEntityId(), new shared_kernel_1.UniqueEntityId()],
            correlationPatterns: ['temporal_sequence', 'ip_clustering'],
        };
        return CorrelatedEventFactory.create({
            ...defaultOptions,
            ...overrides,
        });
    }
    // Private helper methods
    static getDefaultConfig(config) {
        return {
            availableRules: [],
            minCorrelationQualityThreshold: CorrelatedEventFactory.DEFAULT_MIN_CORRELATION_QUALITY,
            requireManualReviewForHighConfidence: true,
            requireManualReviewForAttackChains: true,
            maxValidationErrors: CorrelatedEventFactory.DEFAULT_MAX_VALIDATION_ERRORS,
            correlationTimeoutMs: CorrelatedEventFactory.DEFAULT_CORRELATION_TIMEOUT,
            enabledRuleTypes: Object.values(correlated_event_entity_1.CorrelationRuleType),
            ruleTypePriorities: {
                [correlated_event_entity_1.CorrelationRuleType.TEMPORAL]: 1,
                [correlated_event_entity_1.CorrelationRuleType.SPATIAL]: 2,
                [correlated_event_entity_1.CorrelationRuleType.PATTERN]: 3,
                [correlated_event_entity_1.CorrelationRuleType.BEHAVIORAL]: 4,
                [correlated_event_entity_1.CorrelationRuleType.SIGNATURE]: 5,
                [correlated_event_entity_1.CorrelationRuleType.STATISTICAL]: 6,
                [correlated_event_entity_1.CorrelationRuleType.SEMANTIC]: 7,
                [correlated_event_entity_1.CorrelationRuleType.CAUSAL]: 8,
            },
            maxConcurrentRequests: CorrelatedEventFactory.DEFAULT_MAX_CONCURRENT_REQUESTS,
            temporalWindowMs: CorrelatedEventFactory.DEFAULT_TEMPORAL_WINDOW,
            maxEventsToCorrelate: CorrelatedEventFactory.DEFAULT_MAX_EVENTS_TO_CORRELATE,
            ...config,
        };
    }
    static applyCorrelationRules(enrichedData, rules, enabledRuleTypes) {
        const correlatedData = { ...enrichedData };
        const appliedRules = [];
        const correlationMatches = [];
        const validationErrors = [];
        const relatedEventIds = [];
        const correlationPatterns = [];
        // Sort rules by priority (higher priority first)
        const sortedRules = [...rules].sort((a, b) => b.priority - a.priority);
        for (const rule of sortedRules) {
            try {
                // Check if rule type is enabled
                if (!enabledRuleTypes.includes(rule.type)) {
                    continue;
                }
                // Apply rule logic
                const ruleResult = CorrelatedEventFactory.applyRule(correlatedData, rule);
                if (ruleResult.success) {
                    appliedRules.push(rule);
                    Object.assign(correlatedData, ruleResult.data);
                    if (ruleResult.correlationMatches) {
                        correlationMatches.push(...ruleResult.correlationMatches);
                    }
                    if (ruleResult.relatedEventIds) {
                        relatedEventIds.push(...ruleResult.relatedEventIds);
                    }
                    if (ruleResult.patterns) {
                        correlationPatterns.push(...ruleResult.patterns);
                    }
                }
                else if (rule.required) {
                    validationErrors.push(`Required rule '${rule.name}' failed: ${ruleResult.error}`);
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                if (rule.required) {
                    validationErrors.push(`Required rule '${rule.name}' threw error: ${errorMessage}`);
                }
            }
        }
        // Extract correlation contexts
        const contexts = CorrelatedEventFactory.extractCorrelationContexts(correlationMatches);
        return {
            success: validationErrors.length === 0,
            correlatedData,
            appliedRules,
            correlationMatches,
            validationErrors,
            relatedEventIds,
            correlationPatterns,
            ...contexts,
        };
    }
    static applyRule(data, rule) {
        // Simplified implementation - in practice, this would be more sophisticated
        switch (rule.type) {
            case correlated_event_entity_1.CorrelationRuleType.TEMPORAL:
                return CorrelatedEventFactory.applyTemporalCorrelation(data, rule);
            case correlated_event_entity_1.CorrelationRuleType.SPATIAL:
                return CorrelatedEventFactory.applySpatialCorrelation(data, rule);
            case correlated_event_entity_1.CorrelationRuleType.PATTERN:
                return CorrelatedEventFactory.applyPatternCorrelation(data, rule);
            case correlated_event_entity_1.CorrelationRuleType.BEHAVIORAL:
                return CorrelatedEventFactory.applyBehavioralCorrelation(data, rule);
            default:
                return { success: true, data };
        }
    }
    static applyTemporalCorrelation(data, rule) {
        // Mock temporal correlation
        const matches = [];
        const patterns = [];
        // Simulate finding temporal patterns
        if (data.timestamp) {
            matches.push({
                eventId: new shared_kernel_1.UniqueEntityId(),
                confidence: 75,
                matchType: correlated_event_entity_1.CorrelationMatchType.TEMPORAL,
                ruleId: rule.id,
                details: { timeWindow: rule.timeWindowMs, pattern: 'sequence' },
                timestamp: new Date(),
                weight: 0.8,
            });
            patterns.push('temporal_sequence');
        }
        return {
            success: true,
            data: {
                ...data,
                temporal_correlation: {
                    patterns_found: patterns.length,
                    matches_found: matches.length,
                    confidence: matches.length > 0 ? 75 : 0,
                },
            },
            correlationMatches: matches,
            patterns,
        };
    }
    static applySpatialCorrelation(data, rule) {
        // Mock spatial correlation
        const matches = [];
        const patterns = [];
        // Simulate finding spatial patterns
        if (data.source_ip || data.destination_ip) {
            matches.push({
                eventId: new shared_kernel_1.UniqueEntityId(),
                confidence: 80,
                matchType: correlated_event_entity_1.CorrelationMatchType.SPATIAL,
                ruleId: rule.id,
                details: { ipCluster: true, networkSegment: '***********/24' },
                timestamp: new Date(),
                weight: 0.9,
            });
            patterns.push('ip_clustering');
        }
        return {
            success: true,
            data: {
                ...data,
                spatial_correlation: {
                    patterns_found: patterns.length,
                    matches_found: matches.length,
                    confidence: matches.length > 0 ? 80 : 0,
                },
            },
            correlationMatches: matches,
            patterns,
        };
    }
    static applyPatternCorrelation(data, rule) {
        // Mock pattern correlation
        const matches = [];
        const patterns = [];
        // Simulate finding signature patterns
        if (data.event_type && data.severity) {
            matches.push({
                eventId: new shared_kernel_1.UniqueEntityId(),
                confidence: 70,
                matchType: correlated_event_entity_1.CorrelationMatchType.PATTERN,
                ruleId: rule.id,
                details: { signatureMatch: true, pattern: 'attack_signature' },
                timestamp: new Date(),
                weight: 0.7,
            });
            patterns.push('attack_signature');
        }
        return {
            success: true,
            data: {
                ...data,
                pattern_correlation: {
                    patterns_found: patterns.length,
                    matches_found: matches.length,
                    confidence: matches.length > 0 ? 70 : 0,
                },
            },
            correlationMatches: matches,
            patterns,
        };
    }
    static applyBehavioralCorrelation(data, rule) {
        // Mock behavioral correlation
        const matches = [];
        const patterns = [];
        // Simulate finding behavioral patterns
        if (data.user_id || data.asset_id) {
            matches.push({
                eventId: new shared_kernel_1.UniqueEntityId(),
                confidence: 65,
                matchType: correlated_event_entity_1.CorrelationMatchType.PATTERN,
                ruleId: rule.id,
                details: { behavioralAnomaly: true, deviationScore: 85 },
                timestamp: new Date(),
                weight: 0.6,
            });
            patterns.push('behavioral_anomaly');
        }
        return {
            success: true,
            data: {
                ...data,
                behavioral_correlation: {
                    patterns_found: patterns.length,
                    matches_found: matches.length,
                    confidence: matches.length > 0 ? 65 : 0,
                    anomaly_score: 85,
                },
            },
            correlationMatches: matches,
            patterns,
        };
    }
    static detectAttackChain(enrichedEvent, options) {
        // Simplified attack chain detection
        const correlatedData = { ...enrichedEvent.enrichedData };
        const appliedRules = options.customRules || [];
        const correlationMatches = [];
        const relatedEventIds = [];
        const correlationPatterns = [];
        // Mock attack chain detection
        const hasAttackChain = Math.random() > 0.7; // 30% chance of attack chain
        if (hasAttackChain) {
            const attackChain = {
                id: `attack_chain_${Date.now()}`,
                name: 'Multi-stage Attack Campaign',
                description: 'Coordinated attack with multiple stages',
                stages: [
                    {
                        id: 'stage_1',
                        name: 'Initial Access',
                        description: 'Initial compromise',
                        eventIds: [enrichedEvent.id],
                        order: 1,
                        confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                        timestamp: new Date(),
                        tactic: 'Initial Access',
                        techniques: ['T1566.001'],
                    },
                ],
                confidence: options.minConfidence,
                severity: enrichedEvent.severity,
                timeline: {
                    startTime: new Date(Date.now() - 3600000), // 1 hour ago
                    endTime: new Date(),
                    duration: 3600000,
                },
            };
            correlationPatterns.push('attack_chain', 'multi_stage_attack');
            return {
                correlatedData: {
                    ...correlatedData,
                    attack_chain_detected: true,
                    attack_chain_id: attackChain.id,
                },
                appliedRules,
                correlationMatches,
                attackChain,
                relatedEventIds,
                correlationPatterns,
                qualityScore: 90,
            };
        }
        return {
            correlatedData,
            appliedRules,
            correlationMatches,
            relatedEventIds,
            correlationPatterns,
            qualityScore: 70,
        };
    }
    static calculateCorrelationQualityScore(result, config) {
        let score = 100;
        // Reduce score for validation errors
        score -= result.validationErrors.length * 15;
        // Reduce score if correlation failed
        if (!result.success) {
            score -= 25;
        }
        // Reduce score based on missing required rules
        const requiredRules = config.availableRules.filter(rule => rule.required);
        const appliedRequiredRules = result.appliedRules.filter(rule => rule.required);
        const missingRequiredRules = requiredRules.length - appliedRequiredRules.length;
        score -= missingRequiredRules * 20;
        return Math.max(0, Math.min(100, score));
    }
    static calculateConfidenceLevel(matches) {
        if (matches.length === 0)
            return confidence_level_enum_1.ConfidenceLevel.LOW;
        const avgConfidence = matches.reduce((sum, match) => sum + match.confidence, 0) / matches.length;
        return confidence_level_enum_1.ConfidenceLevelUtils.fromNumericValue(avgConfidence);
    }
    static determineManualReviewRequirement(enrichedEvent, correlationQualityScore, confidenceLevel, validationErrors, config) {
        // High confidence correlations require manual review
        if (config.requireManualReviewForHighConfidence &&
            confidence_level_enum_1.ConfidenceLevelUtils.isHigh(confidenceLevel)) {
            return true;
        }
        // Critical events require manual review
        if (enrichedEvent.severity === event_severity_enum_1.EventSeverity.CRITICAL) {
            return true;
        }
        // Low correlation quality requires manual review
        if (correlationQualityScore < config.minCorrelationQualityThreshold) {
            return true;
        }
        // Too many validation errors require manual review
        if (validationErrors.length > config.maxValidationErrors) {
            return true;
        }
        return false;
    }
    static extractCorrelationContexts(matches) {
        const contexts = {};
        const temporalMatches = matches.filter(m => m.matchType === correlated_event_entity_1.CorrelationMatchType.TEMPORAL);
        if (temporalMatches.length > 0) {
            contexts.temporalCorrelation = {
                timeWindow: 3600000, // 1 hour
                eventSequence: temporalMatches.map(m => m.eventId),
                patternType: 'sequence',
                confidence: temporalMatches.reduce((sum, m) => sum + m.confidence, 0) / temporalMatches.length,
            };
        }
        const spatialMatches = matches.filter(m => m.matchType === correlated_event_entity_1.CorrelationMatchType.SPATIAL);
        if (spatialMatches.length > 0) {
            contexts.spatialCorrelation = {
                sourceIps: ['*************'],
                targetIps: ['*********'],
                networkSegments: ['***********/24'],
                geographicRegions: ['US-East'],
                confidence: spatialMatches.reduce((sum, m) => sum + m.confidence, 0) / spatialMatches.length,
            };
        }
        return contexts;
    }
    static correlateEventData(enrichedData, ruleTypes) {
        // Basic correlation - in practice this would be more sophisticated
        return {
            ...enrichedData,
            correlated_at: new Date().toISOString(),
            correlation_rule_types: ruleTypes || [],
        };
    }
    static generateCorrelationId() {
        return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    static createMockEnrichedEvent() {
        // This would create a proper mock EnrichedEvent - simplified for now
        // In practice, you'd use the EnrichedEventFactory to create a proper mock
        return {};
    }
    static createMockCorrelationRule(type) {
        return {
            id: `rule_${type}`,
            name: `${type} Rule`,
            description: `Mock ${type} correlation rule`,
            type: correlated_event_entity_1.CorrelationRuleType.TEMPORAL,
            priority: 100,
            required: false,
            timeWindowMs: 3600000,
            minConfidence: 70,
        };
    }
    static createMockCorrelationMatch() {
        return {
            eventId: new shared_kernel_1.UniqueEntityId(),
            confidence: 80,
            matchType: correlated_event_entity_1.CorrelationMatchType.PATTERN,
            ruleId: 'mock_rule',
            details: { mock: true },
            timestamp: new Date(),
            weight: 0.8,
        };
    }
}
exports.CorrelatedEventFactory = CorrelatedEventFactory;
CorrelatedEventFactory.DEFAULT_MIN_CORRELATION_QUALITY = 70;
CorrelatedEventFactory.DEFAULT_MAX_VALIDATION_ERRORS = 10;
CorrelatedEventFactory.DEFAULT_CORRELATION_TIMEOUT = 120000; // 2 minutes
CorrelatedEventFactory.DEFAULT_MAX_CONCURRENT_REQUESTS = 5;
CorrelatedEventFactory.DEFAULT_TEMPORAL_WINDOW = 3600000; // 1 hour
CorrelatedEventFactory.DEFAULT_MAX_EVENTS_TO_CORRELATE = 100;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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