{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\ioc.value-object.ts", "mappings": ";;;AAAA,oGAA+F;AAE/F;;GAEG;AACH,IAAY,OAqBX;AArBD,WAAY,OAAO;IACjB,oCAAyB,CAAA;IACzB,4BAAiB,CAAA;IACjB,sBAAW,CAAA;IACX,kCAAuB,CAAA;IACvB,0BAAe,CAAA;IACf,oCAAyB,CAAA;IACzB,wCAA6B,CAAA;IAC7B,kCAAuB,CAAA;IACvB,wCAA6B,CAAA;IAC7B,sCAA2B,CAAA;IAC3B,0BAAe,CAAA;IACf,wBAAa,CAAA;IACb,8BAAmB,CAAA;IACnB,kCAAuB,CAAA;IACvB,oCAAyB,CAAA;IACzB,sBAAW,CAAA;IACX,4CAAiC,CAAA;IACjC,4CAAiC,CAAA;IACjC,gCAAqB,CAAA;IACrB,wCAA6B,CAAA;AAC/B,CAAC,EArBW,OAAO,uBAAP,OAAO,QAqBlB;AAED;;GAEG;AACH,IAAY,aAKX;AALD,WAAY,aAAa;IACvB,4BAAW,CAAA;IACX,kCAAiB,CAAA;IACjB,8BAAa,CAAA;IACb,wCAAuB,CAAA;AACzB,CAAC,EALW,aAAa,6BAAb,aAAa,QAKxB;AAED;;GAEG;AACH,IAAY,WAMX;AAND,WAAY,WAAW;IACrB,4BAAa,CAAA;IACb,0BAAW,CAAA;IACX,gCAAiB,CAAA;IACjB,4BAAa,CAAA;IACb,oCAAqB,CAAA;AACvB,CAAC,EANW,WAAW,2BAAX,WAAW,QAMtB;AA8BD;;;;;;;;;;;;;GAaG;AACH,MAAa,GAAI,SAAQ,mCAAyB;IAChD,YAAY,KAAe;QACzB,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IAES,QAAQ;QAChB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/D,MAAM,IAAI,KAAK,CAAC,yBAAyB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QACnE,CAAC;QAED,6CAA6C;QAC7C,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,kCAAkC;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC7C,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,EAAE,CAAC;YACjE,yCAAyC;YACzC,OAAO,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,cAAc,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,sBAAsB;QAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAEvC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACzB,KAAK,OAAO,CAAC,UAAU;gBACrB,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAC9B,MAAM;YACR,KAAK,OAAO,CAAC,MAAM;gBACjB,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;YACR,KAAK,OAAO,CAAC,GAAG;gBACd,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;YACR,KAAK,OAAO,CAAC,SAAS;gBACpB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBAC7B,MAAM;YACR,KAAK,OAAO,CAAC,KAAK;gBAChB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1B,MAAM;YACR,KAAK,OAAO,CAAC,GAAG;gBACd,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;YACR,iCAAiC;QACnC,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,KAAa;QACrC,MAAM,SAAS,GAAG,6FAA6F,CAAC;QAChH,MAAM,SAAS,GAAG,uDAAuD,CAAC;QAE1E,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,KAAa;QAClC,MAAM,WAAW,GAAG,+FAA+F,CAAC;QAEpH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,KAAa;QAC/B,IAAI,CAAC;YACH,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,EAAE,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,KAAa;QACpC,MAAM,QAAQ,GAAG,mBAAmB,CAAC;QACrC,MAAM,SAAS,GAAG,mBAAmB,CAAC;QACtC,MAAM,WAAW,GAAG,mBAAmB,CAAC;QAExC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAChF,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,KAAa;QACjC,MAAM,UAAU,GAAG,4BAA4B,CAAC;QAEhD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,KAAa;QAC/B,MAAM,QAAQ,GAAG,oBAAoB,CAAC;QAEtC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,EAAE,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CACX,IAAa,EACb,KAAa,EACb,UAAyB,EACzB,QAAqB,EACrB,OAA+E;QAE/E,OAAO,IAAI,GAAG,CAAC;YACb,IAAI;YACJ,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE;YACnB,UAAU;YACV,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CACpB,SAAiB,EACjB,UAAyB,EACzB,QAAqB,EACrB,OAA+E;QAE/E,OAAO,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAClF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CACjB,MAAc,EACd,UAAyB,EACzB,QAAqB,EACrB,OAA+E;QAE/E,OAAO,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CACnB,IAAY,EACZ,UAAyB,EACzB,QAAqB,EACrB,OAA+E;QAE/E,OAAO,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,aAAa,CAAC,IAAI;YAC7C,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,aAAa,CAAC,SAAS,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,WAAW,CAAC,IAAI;YACzC,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,WAAW,CAAC,QAAQ,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,WAAW,CAAC,QAAQ,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC5F,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC5F,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,2BAA2B;QAC3B,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC7B,KAAK,WAAW,CAAC,QAAQ;gBAAE,KAAK,IAAI,EAAE,CAAC;gBAAC,MAAM;YAC9C,KAAK,WAAW,CAAC,IAAI;gBAAE,KAAK,IAAI,EAAE,CAAC;gBAAC,MAAM;YAC1C,KAAK,WAAW,CAAC,MAAM;gBAAE,KAAK,IAAI,EAAE,CAAC;gBAAC,MAAM;YAC5C,KAAK,WAAW,CAAC,GAAG;gBAAE,KAAK,IAAI,EAAE,CAAC;gBAAC,MAAM;YACzC,KAAK,WAAW,CAAC,IAAI;gBAAE,KAAK,IAAI,CAAC,CAAC;gBAAC,MAAM;QAC3C,CAAC;QAED,wBAAwB;QACxB,QAAQ,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC/B,KAAK,aAAa,CAAC,SAAS;gBAAE,KAAK,IAAI,GAAG,CAAC;gBAAC,MAAM;YAClD,KAAK,aAAa,CAAC,IAAI;gBAAE,KAAK,IAAI,GAAG,CAAC;gBAAC,MAAM;YAC7C,KAAK,aAAa,CAAC,MAAM;gBAAE,KAAK,IAAI,GAAG,CAAC;gBAAC,MAAM;YAC/C,KAAK,aAAa,CAAC,GAAG;gBAAE,KAAK,IAAI,GAAG,CAAC;gBAAC,MAAM;QAC9C,CAAC;QAED,6CAA6C;QAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC1B,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjB,IAAI,GAAG,GAAG,GAAG;gBAAE,KAAK,IAAI,GAAG,CAAC,CAAC,WAAW;iBACnC,IAAI,GAAG,GAAG,EAAE;gBAAE,KAAK,IAAI,GAAG,CAAC,CAAC,MAAM;iBAClC,IAAI,GAAG,GAAG,EAAE;gBAAE,KAAK,IAAI,GAAG,CAAC,CAAC,eAAe;QAClD,CAAC;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACrB,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;QACxC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,EAAE,CAAC,CAAC,OAAO,KAAK,CAAC;YACtB,KAAK,EAAE,CAAC,CAAC,OAAO,MAAM,CAAC;YACvB,KAAK,EAAE,CAAC,CAAC,OAAO,QAAQ,CAAC;YACzB,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAiB,IAAI,IAAI,EAAE;QACxC,OAAO,IAAI,GAAG,CAAC;YACb,GAAG,IAAI,CAAC,MAAM;YACd,QAAQ;SACT,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,OAAiB;QACxB,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC;QAC/B,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAE5D,OAAO,IAAI,GAAG,CAAC;YACb,GAAG,IAAI,CAAC,MAAM;YACd,IAAI,EAAE,OAAO;SACd,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,OAA4B;QACtC,OAAO,IAAI,GAAG,CAAC;YACb,GAAG,IAAI,CAAC,MAAM;YACd,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE;SACzC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,SAAe;QAC5B,OAAO,IAAI,GAAG,CAAC;YACb,GAAG,IAAI,CAAC,MAAM;YACd,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,UAAU;QAaR,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YAClC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACzC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YACrC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE;YAClB,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YAClD,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;SAC3B,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAW;QACvB,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,CAAC,IAAI;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YAClC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YAC1B,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE;YAC/C,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,EAAE;YAC7C,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE;YAC/C,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YAC1B,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;SAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,OAAO,IAAI,GAAG,CAAC;YACb,IAAI,EAAE,IAAI,CAAC,IAAe;YAC1B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,IAAI,CAAC,UAA2B;YAC5C,QAAQ,EAAE,IAAI,CAAC,QAAuB;YACtC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS;YAChE,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS;YAC7D,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS;YAChE,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,IAAa,EAAE,KAAa;QACzC,IAAI,CAAC;YACH,IAAI,GAAG,CAAC;gBACN,IAAI;gBACJ,KAAK;gBACL,UAAU,EAAE,aAAa,CAAC,MAAM;gBAChC,QAAQ,EAAE,WAAW,CAAC,MAAM;aAC7B,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AA5gBD,kBA4gBC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\ioc.value-object.ts"], "sourcesContent": ["import { BaseValueObject } from '../../../../../shared-kernel/value-objects/base-value-object';\r\n\r\n/**\r\n * IOC (Indicator of Compromise) Type\r\n */\r\nexport enum IOCType {\r\n  IP_ADDRESS = 'ip_address',\r\n  DOMAIN = 'domain',\r\n  URL = 'url',\r\n  FILE_HASH = 'file_hash',\r\n  EMAIL = 'email',\r\n  USER_AGENT = 'user_agent',\r\n  REGISTRY_KEY = 'registry_key',\r\n  FILE_PATH = 'file_path',\r\n  PROCESS_NAME = 'process_name',\r\n  CERTIFICATE = 'certificate',\r\n  MUTEX = 'mutex',\r\n  PIPE = 'pipe',\r\n  SERVICE = 'service',\r\n  YARA_RULE = 'yara_rule',\r\n  SIGMA_RULE = 'sigma_rule',\r\n  CVE = 'cve',\r\n  ATTACK_PATTERN = 'attack_pattern',\r\n  MALWARE_FAMILY = 'malware_family',\r\n  CAMPAIGN = 'campaign',\r\n  THREAT_ACTOR = 'threat_actor',\r\n}\r\n\r\n/**\r\n * IOC Confidence Level\r\n */\r\nexport enum IOCConfidence {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CONFIRMED = 'confirmed',\r\n}\r\n\r\n/**\r\n * IOC Severity Level\r\n */\r\nexport enum IOCSeverity {\r\n  INFO = 'info',\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CRITICAL = 'critical',\r\n}\r\n\r\n/**\r\n * IOC Properties\r\n */\r\nexport interface IOCProps {\r\n  /** Type of the indicator */\r\n  type: IOCType;\r\n  /** The actual indicator value */\r\n  value: string;\r\n  /** Confidence level in the indicator */\r\n  confidence: IOCConfidence;\r\n  /** Severity level of the threat */\r\n  severity: IOCSeverity;\r\n  /** Description of the indicator */\r\n  description?: string;\r\n  /** Source of the indicator */\r\n  source?: string;\r\n  /** Tags associated with the indicator */\r\n  tags?: string[];\r\n  /** When the indicator was first seen */\r\n  firstSeen?: Date;\r\n  /** When the indicator was last seen */\r\n  lastSeen?: Date;\r\n  /** When the indicator expires */\r\n  expiresAt?: Date;\r\n  /** Additional context metadata */\r\n  context?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * IOC (Indicator of Compromise) Value Object\r\n * \r\n * Represents a security indicator that can be used to detect threats.\r\n * Provides validation, classification, and threat intelligence capabilities.\r\n * \r\n * Key features:\r\n * - Multiple IOC types support (IP, domain, hash, etc.)\r\n * - Confidence and severity scoring\r\n * - Temporal tracking (first/last seen, expiration)\r\n * - Source attribution and tagging\r\n * - Validation and normalization\r\n * - Threat intelligence integration\r\n */\r\nexport class IOC extends BaseValueObject<IOCProps> {\r\n  constructor(props: IOCProps) {\r\n    super(props);\r\n  }\r\n\r\n  protected validate(): void {\r\n    if (!this._value.type) {\r\n      throw new Error('IOC must have a type');\r\n    }\r\n\r\n    if (!Object.values(IOCType).includes(this._value.type)) {\r\n      throw new Error(`Invalid IOC type: ${this._value.type}`);\r\n    }\r\n\r\n    if (!this._value.value || this._value.value.trim().length === 0) {\r\n      throw new Error('IOC must have a non-empty value');\r\n    }\r\n\r\n    if (!Object.values(IOCConfidence).includes(this._value.confidence)) {\r\n      throw new Error(`Invalid IOC confidence: ${this._value.confidence}`);\r\n    }\r\n\r\n    if (!Object.values(IOCSeverity).includes(this._value.severity)) {\r\n      throw new Error(`Invalid IOC severity: ${this._value.severity}`);\r\n    }\r\n\r\n    // Validate the indicator value based on type\r\n    this.validateIndicatorValue();\r\n\r\n    // Validate temporal relationships\r\n    if (this._value.firstSeen && this._value.lastSeen && \r\n        this._value.firstSeen > this._value.lastSeen) {\r\n      throw new Error('IOC firstSeen cannot be after lastSeen');\r\n    }\r\n\r\n    if (this._value.expiresAt && this._value.expiresAt <= new Date()) {\r\n      // Allow expired IOCs but warn about them\r\n      console.warn(`IOC ${this._value.value} has expired`);\r\n    }\r\n  }\r\n\r\n  private validateIndicatorValue(): void {\r\n    const value = this._value.value.trim();\r\n\r\n    switch (this._value.type) {\r\n      case IOCType.IP_ADDRESS:\r\n        this.validateIPAddress(value);\r\n        break;\r\n      case IOCType.DOMAIN:\r\n        this.validateDomain(value);\r\n        break;\r\n      case IOCType.URL:\r\n        this.validateURL(value);\r\n        break;\r\n      case IOCType.FILE_HASH:\r\n        this.validateFileHash(value);\r\n        break;\r\n      case IOCType.EMAIL:\r\n        this.validateEmail(value);\r\n        break;\r\n      case IOCType.CVE:\r\n        this.validateCVE(value);\r\n        break;\r\n      // Add more validations as needed\r\n    }\r\n  }\r\n\r\n  private validateIPAddress(value: string): void {\r\n    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\r\n    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;\r\n    \r\n    if (!ipv4Regex.test(value) && !ipv6Regex.test(value)) {\r\n      throw new Error(`Invalid IP address format: ${value}`);\r\n    }\r\n  }\r\n\r\n  private validateDomain(value: string): void {\r\n    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\r\n    \r\n    if (!domainRegex.test(value) || value.length > 253) {\r\n      throw new Error(`Invalid domain format: ${value}`);\r\n    }\r\n  }\r\n\r\n  private validateURL(value: string): void {\r\n    try {\r\n      new URL(value);\r\n    } catch {\r\n      throw new Error(`Invalid URL format: ${value}`);\r\n    }\r\n  }\r\n\r\n  private validateFileHash(value: string): void {\r\n    const md5Regex = /^[a-fA-F0-9]{32}$/;\r\n    const sha1Regex = /^[a-fA-F0-9]{40}$/;\r\n    const sha256Regex = /^[a-fA-F0-9]{64}$/;\r\n    \r\n    if (!md5Regex.test(value) && !sha1Regex.test(value) && !sha256Regex.test(value)) {\r\n      throw new Error(`Invalid file hash format: ${value}`);\r\n    }\r\n  }\r\n\r\n  private validateEmail(value: string): void {\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    \r\n    if (!emailRegex.test(value)) {\r\n      throw new Error(`Invalid email format: ${value}`);\r\n    }\r\n  }\r\n\r\n  private validateCVE(value: string): void {\r\n    const cveRegex = /^CVE-\\d{4}-\\d{4,}$/;\r\n    \r\n    if (!cveRegex.test(value)) {\r\n      throw new Error(`Invalid CVE format: ${value}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create an IOC with required fields\r\n   */\r\n  static create(\r\n    type: IOCType,\r\n    value: string,\r\n    confidence: IOCConfidence,\r\n    severity: IOCSeverity,\r\n    options?: Partial<Omit<IOCProps, 'type' | 'value' | 'confidence' | 'severity'>>\r\n  ): IOC {\r\n    return new IOC({\r\n      type,\r\n      value: value.trim(),\r\n      confidence,\r\n      severity,\r\n      firstSeen: new Date(),\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an IP address IOC\r\n   */\r\n  static createIPAddress(\r\n    ipAddress: string,\r\n    confidence: IOCConfidence,\r\n    severity: IOCSeverity,\r\n    options?: Partial<Omit<IOCProps, 'type' | 'value' | 'confidence' | 'severity'>>\r\n  ): IOC {\r\n    return IOC.create(IOCType.IP_ADDRESS, ipAddress, confidence, severity, options);\r\n  }\r\n\r\n  /**\r\n   * Create a domain IOC\r\n   */\r\n  static createDomain(\r\n    domain: string,\r\n    confidence: IOCConfidence,\r\n    severity: IOCSeverity,\r\n    options?: Partial<Omit<IOCProps, 'type' | 'value' | 'confidence' | 'severity'>>\r\n  ): IOC {\r\n    return IOC.create(IOCType.DOMAIN, domain, confidence, severity, options);\r\n  }\r\n\r\n  /**\r\n   * Create a file hash IOC\r\n   */\r\n  static createFileHash(\r\n    hash: string,\r\n    confidence: IOCConfidence,\r\n    severity: IOCSeverity,\r\n    options?: Partial<Omit<IOCProps, 'type' | 'value' | 'confidence' | 'severity'>>\r\n  ): IOC {\r\n    return IOC.create(IOCType.FILE_HASH, hash, confidence, severity, options);\r\n  }\r\n\r\n  /**\r\n   * Get IOC type\r\n   */\r\n  get type(): IOCType {\r\n    return this._value.type;\r\n  }\r\n\r\n  /**\r\n   * Get IOC value\r\n   */\r\n  get value(): string {\r\n    return this._value.value;\r\n  }\r\n\r\n  /**\r\n   * Get confidence level\r\n   */\r\n  get confidence(): IOCConfidence {\r\n    return this._value.confidence;\r\n  }\r\n\r\n  /**\r\n   * Get severity level\r\n   */\r\n  get severity(): IOCSeverity {\r\n    return this._value.severity;\r\n  }\r\n\r\n  /**\r\n   * Get description\r\n   */\r\n  get description(): string | undefined {\r\n    return this._value.description;\r\n  }\r\n\r\n  /**\r\n   * Get source\r\n   */\r\n  get source(): string | undefined {\r\n    return this._value.source;\r\n  }\r\n\r\n  /**\r\n   * Get tags\r\n   */\r\n  get tags(): string[] {\r\n    return this._value.tags || [];\r\n  }\r\n\r\n  /**\r\n   * Get first seen date\r\n   */\r\n  get firstSeen(): Date | undefined {\r\n    return this._value.firstSeen;\r\n  }\r\n\r\n  /**\r\n   * Get last seen date\r\n   */\r\n  get lastSeen(): Date | undefined {\r\n    return this._value.lastSeen;\r\n  }\r\n\r\n  /**\r\n   * Get expiration date\r\n   */\r\n  get expiresAt(): Date | undefined {\r\n    return this._value.expiresAt;\r\n  }\r\n\r\n  /**\r\n   * Get context metadata\r\n   */\r\n  get context(): Record<string, any> {\r\n    return this._value.context || {};\r\n  }\r\n\r\n  /**\r\n   * Check if IOC is expired\r\n   */\r\n  isExpired(): boolean {\r\n    return this._value.expiresAt ? this._value.expiresAt <= new Date() : false;\r\n  }\r\n\r\n  /**\r\n   * Check if IOC is active (not expired)\r\n   */\r\n  isActive(): boolean {\r\n    return !this.isExpired();\r\n  }\r\n\r\n  /**\r\n   * Check if IOC is high confidence\r\n   */\r\n  isHighConfidence(): boolean {\r\n    return this._value.confidence === IOCConfidence.HIGH || \r\n           this._value.confidence === IOCConfidence.CONFIRMED;\r\n  }\r\n\r\n  /**\r\n   * Check if IOC is high severity\r\n   */\r\n  isHighSeverity(): boolean {\r\n    return this._value.severity === IOCSeverity.HIGH || \r\n           this._value.severity === IOCSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if IOC is critical\r\n   */\r\n  isCritical(): boolean {\r\n    return this._value.severity === IOCSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if IOC has specific tag\r\n   */\r\n  hasTag(tag: string): boolean {\r\n    return this.tags.includes(tag);\r\n  }\r\n\r\n  /**\r\n   * Get IOC age in days\r\n   */\r\n  getAge(): number | null {\r\n    if (!this._value.firstSeen) {\r\n      return null;\r\n    }\r\n    return Math.floor((Date.now() - this._value.firstSeen.getTime()) / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get days until expiration\r\n   */\r\n  getDaysUntilExpiration(): number | null {\r\n    if (!this._value.expiresAt) {\r\n      return null;\r\n    }\r\n    return Math.floor((this._value.expiresAt.getTime() - Date.now()) / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get threat score (0-100)\r\n   */\r\n  getThreatScore(): number {\r\n    let score = 0;\r\n\r\n    // Base score from severity\r\n    switch (this._value.severity) {\r\n      case IOCSeverity.CRITICAL: score += 60; break;\r\n      case IOCSeverity.HIGH: score += 45; break;\r\n      case IOCSeverity.MEDIUM: score += 30; break;\r\n      case IOCSeverity.LOW: score += 15; break;\r\n      case IOCSeverity.INFO: score += 5; break;\r\n    }\r\n\r\n    // Confidence multiplier\r\n    switch (this._value.confidence) {\r\n      case IOCConfidence.CONFIRMED: score *= 1.0; break;\r\n      case IOCConfidence.HIGH: score *= 0.9; break;\r\n      case IOCConfidence.MEDIUM: score *= 0.7; break;\r\n      case IOCConfidence.LOW: score *= 0.5; break;\r\n    }\r\n\r\n    // Age penalty (older IOCs are less relevant)\r\n    const age = this.getAge();\r\n    if (age !== null) {\r\n      if (age > 365) score *= 0.5; // Very old\r\n      else if (age > 90) score *= 0.7; // Old\r\n      else if (age > 30) score *= 0.9; // Somewhat old\r\n    }\r\n\r\n    // Expiration penalty\r\n    if (this.isExpired()) {\r\n      score *= 0.1;\r\n    }\r\n\r\n    return Math.min(100, Math.max(0, Math.round(score)));\r\n  }\r\n\r\n  /**\r\n   * Get hash type for file hashes\r\n   */\r\n  getHashType(): 'md5' | 'sha1' | 'sha256' | null {\r\n    if (this._value.type !== IOCType.FILE_HASH) {\r\n      return null;\r\n    }\r\n\r\n    const length = this._value.value.length;\r\n    switch (length) {\r\n      case 32: return 'md5';\r\n      case 40: return 'sha1';\r\n      case 64: return 'sha256';\r\n      default: return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a new IOC with updated last seen\r\n   */\r\n  updateLastSeen(lastSeen: Date = new Date()): IOC {\r\n    return new IOC({\r\n      ...this._value,\r\n      lastSeen,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new IOC with additional tags\r\n   */\r\n  withTags(newTags: string[]): IOC {\r\n    const existingTags = this.tags;\r\n    const allTags = [...new Set([...existingTags, ...newTags])];\r\n    \r\n    return new IOC({\r\n      ...this._value,\r\n      tags: allTags,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new IOC with updated context\r\n   */\r\n  withContext(context: Record<string, any>): IOC {\r\n    return new IOC({\r\n      ...this._value,\r\n      context: { ...this.context, ...context },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new IOC with updated expiration\r\n   */\r\n  withExpiration(expiresAt: Date): IOC {\r\n    return new IOC({\r\n      ...this._value,\r\n      expiresAt,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get IOC summary for analysis\r\n   */\r\n  getSummary(): {\r\n    type: string;\r\n    value: string;\r\n    confidence: string;\r\n    severity: string;\r\n    threatScore: number;\r\n    isActive: boolean;\r\n    isHighConfidence: boolean;\r\n    isHighSeverity: boolean;\r\n    age: number | null;\r\n    daysUntilExpiration: number | null;\r\n    tagCount: number;\r\n  } {\r\n    return {\r\n      type: this._value.type,\r\n      value: this._value.value,\r\n      confidence: this._value.confidence,\r\n      severity: this._value.severity,\r\n      threatScore: this.getThreatScore(),\r\n      isActive: this.isActive(),\r\n      isHighConfidence: this.isHighConfidence(),\r\n      isHighSeverity: this.isHighSeverity(),\r\n      age: this.getAge(),\r\n      daysUntilExpiration: this.getDaysUntilExpiration(),\r\n      tagCount: this.tags.length,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Compare IOCs for equality\r\n   */\r\n  public equals(other?: IOC): boolean {\r\n    if (!other) {\r\n      return false;\r\n    }\r\n\r\n    if (this === other) {\r\n      return true;\r\n    }\r\n\r\n    return this._value.type === other._value.type && \r\n           this._value.value === other._value.value;\r\n  }\r\n\r\n  /**\r\n   * Get string representation\r\n   */\r\n  public toString(): string {\r\n    return `${this._value.type}:${this._value.value}`;\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      type: this._value.type,\r\n      value: this._value.value,\r\n      confidence: this._value.confidence,\r\n      severity: this._value.severity,\r\n      description: this._value.description,\r\n      source: this._value.source,\r\n      tags: this._value.tags,\r\n      firstSeen: this._value.firstSeen?.toISOString(),\r\n      lastSeen: this._value.lastSeen?.toISOString(),\r\n      expiresAt: this._value.expiresAt?.toISOString(),\r\n      context: this._value.context,\r\n      summary: this.getSummary(),\r\n      hashType: this.getHashType(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create IOC from JSON\r\n   */\r\n  static fromJSON(json: Record<string, any>): IOC {\r\n    return new IOC({\r\n      type: json.type as IOCType,\r\n      value: json.value,\r\n      confidence: json.confidence as IOCConfidence,\r\n      severity: json.severity as IOCSeverity,\r\n      description: json.description,\r\n      source: json.source,\r\n      tags: json.tags,\r\n      firstSeen: json.firstSeen ? new Date(json.firstSeen) : undefined,\r\n      lastSeen: json.lastSeen ? new Date(json.lastSeen) : undefined,\r\n      expiresAt: json.expiresAt ? new Date(json.expiresAt) : undefined,\r\n      context: json.context,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Validate IOC format without creating instance\r\n   */\r\n  static isValid(type: IOCType, value: string): boolean {\r\n    try {\r\n      new IOC({\r\n        type,\r\n        value,\r\n        confidence: IOCConfidence.MEDIUM,\r\n        severity: IOCSeverity.MEDIUM,\r\n      });\r\n      return true;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}