552fa0817b7e0bb0e675198fc4d0e4ea
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var VulnerabilityFeedService_1;
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityFeedService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_1 = require("@nestjs/schedule");
const vulnerability_feed_entity_1 = require("../../domain/entities/vulnerability-feed.entity");
const vulnerability_entity_1 = require("../../domain/entities/vulnerability.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../infrastructure/notification/notification.service");
/**
 * Vulnerability Feed service
 * Handles vulnerability feed management and synchronization
 */
let VulnerabilityFeedService = VulnerabilityFeedService_1 = class VulnerabilityFeedService {
    constructor(feedRepository, vulnerabilityRepository, loggerService, auditService, notificationService) {
        this.feedRepository = feedRepository;
        this.vulnerabilityRepository = vulnerabilityRepository;
        this.loggerService = loggerService;
        this.auditService = auditService;
        this.notificationService = notificationService;
        this.logger = new common_1.Logger(VulnerabilityFeedService_1.name);
    }
    /**
     * Create vulnerability feed
     */
    async createFeed(feedData, userId) {
        try {
            this.logger.debug('Creating vulnerability feed', {
                name: feedData.name,
                feedType: feedData.feedType,
                provider: feedData.provider,
                userId,
            });
            const feed = this.feedRepository.create({
                ...feedData,
                status: 'inactive',
                isActive: false,
            });
            const savedFeed = await this.feedRepository.save(feed);
            await this.auditService.logUserAction(userId, 'create', 'vulnerability_feed', savedFeed.id, {
                name: feedData.name,
                feedType: feedData.feedType,
                provider: feedData.provider,
            });
            this.logger.log('Vulnerability feed created successfully', {
                feedId: savedFeed.id,
                name: feedData.name,
                userId,
            });
            return savedFeed;
        }
        catch (error) {
            this.logger.error('Failed to create vulnerability feed', {
                error: error.message,
                feedData,
                userId,
            });
            throw error;
        }
    }
    /**
     * Get feed details
     */
    async getFeedDetails(id) {
        try {
            const feed = await this.feedRepository.findOne({
                where: { id },
            });
            if (!feed) {
                throw new common_1.NotFoundException('Feed not found');
            }
            this.logger.debug('Feed details retrieved', {
                feedId: id,
                name: feed.name,
                status: feed.status,
            });
            return feed;
        }
        catch (error) {
            this.logger.error('Failed to get feed details', {
                feedId: id,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Search feeds with filtering
     */
    async searchFeeds(criteria) {
        try {
            const page = criteria.page || 1;
            const limit = Math.min(criteria.limit || 50, 1000);
            const offset = (page - 1) * limit;
            const queryBuilder = this.feedRepository.createQueryBuilder('feed');
            // Apply filters
            if (criteria.feedTypes?.length) {
                queryBuilder.andWhere('feed.feedType IN (:...feedTypes)', { feedTypes: criteria.feedTypes });
            }
            if (criteria.providers?.length) {
                queryBuilder.andWhere('feed.provider IN (:...providers)', { providers: criteria.providers });
            }
            if (criteria.statuses?.length) {
                queryBuilder.andWhere('feed.status IN (:...statuses)', { statuses: criteria.statuses });
            }
            if (criteria.isActive !== undefined) {
                queryBuilder.andWhere('feed.isActive = :isActive', { isActive: criteria.isActive });
            }
            if (criteria.priorities?.length) {
                queryBuilder.andWhere('feed.priority IN (:...priorities)', { priorities: criteria.priorities });
            }
            if (criteria.tags?.length) {
                queryBuilder.andWhere('feed.tags && :tags', { tags: criteria.tags });
            }
            if (criteria.searchText) {
                queryBuilder.andWhere('(feed.name ILIKE :searchText OR feed.description ILIKE :searchText OR feed.provider ILIKE :searchText)', { searchText: `%${criteria.searchText}%` });
            }
            // Apply sorting
            const sortBy = criteria.sortBy || 'createdAt';
            const sortOrder = criteria.sortOrder || 'DESC';
            queryBuilder.orderBy(`feed.${sortBy}`, sortOrder);
            // Apply pagination
            queryBuilder.skip(offset).take(limit);
            const [feeds, total] = await queryBuilder.getManyAndCount();
            this.logger.debug('Feed search completed', {
                total,
                page,
                limit,
                criteriaCount: Object.keys(criteria).length,
            });
            return {
                feeds,
                total,
                page,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            this.logger.error('Failed to search feeds', {
                error: error.message,
                criteria,
            });
            throw error;
        }
    }
    /**
     * Update feed
     */
    async updateFeed(id, updates, userId) {
        try {
            const feed = await this.feedRepository.findOne({
                where: { id },
            });
            if (!feed) {
                throw new common_1.NotFoundException('Feed not found');
            }
            this.logger.debug('Updating feed', {
                feedId: id,
                name: feed.name,
                userId,
            });
            // Track changes for audit
            const changes = {};
            Object.keys(updates).forEach(key => {
                if (feed[key] !== updates[key]) {
                    changes[key] = {
                        from: feed[key],
                        to: updates[key],
                    };
                }
            });
            Object.assign(feed, updates);
            const savedFeed = await this.feedRepository.save(feed);
            await this.auditService.logUserAction(userId, 'update', 'vulnerability_feed', id, {
                name: feed.name,
                changes,
            });
            this.logger.log('Feed updated successfully', {
                feedId: id,
                name: feed.name,
                changesCount: Object.keys(changes).length,
                userId,
            });
            return savedFeed;
        }
        catch (error) {
            this.logger.error('Failed to update feed', {
                feedId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Activate feed
     */
    async activateFeed(id, userId) {
        try {
            const feed = await this.feedRepository.findOne({
                where: { id },
            });
            if (!feed) {
                throw new common_1.NotFoundException('Feed not found');
            }
            this.logger.debug('Activating feed', {
                feedId: id,
                name: feed.name,
                userId,
            });
            feed.activate();
            const savedFeed = await this.feedRepository.save(feed);
            await this.auditService.logUserAction(userId, 'activate', 'vulnerability_feed', id, {
                name: feed.name,
            });
            this.logger.log('Feed activated successfully', {
                feedId: id,
                name: feed.name,
                userId,
            });
            return savedFeed;
        }
        catch (error) {
            this.logger.error('Failed to activate feed', {
                feedId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Deactivate feed
     */
    async deactivateFeed(id, userId) {
        try {
            const feed = await this.feedRepository.findOne({
                where: { id },
            });
            if (!feed) {
                throw new common_1.NotFoundException('Feed not found');
            }
            this.logger.debug('Deactivating feed', {
                feedId: id,
                name: feed.name,
                userId,
            });
            feed.deactivate();
            const savedFeed = await this.feedRepository.save(feed);
            await this.auditService.logUserAction(userId, 'deactivate', 'vulnerability_feed', id, {
                name: feed.name,
            });
            this.logger.log('Feed deactivated successfully', {
                feedId: id,
                name: feed.name,
                userId,
            });
            return savedFeed;
        }
        catch (error) {
            this.logger.error('Failed to deactivate feed', {
                feedId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Sync feed manually
     */
    async syncFeed(id, userId) {
        try {
            const feed = await this.feedRepository.findOne({
                where: { id },
            });
            if (!feed) {
                throw new common_1.NotFoundException('Feed not found');
            }
            if (!feed.isActive) {
                throw new Error('Cannot sync inactive feed');
            }
            this.logger.debug('Starting manual feed sync', {
                feedId: id,
                name: feed.name,
                userId,
            });
            const syncResult = await this.performFeedSync(feed);
            await this.auditService.logUserAction(userId, 'sync', 'vulnerability_feed', id, {
                name: feed.name,
                recordsProcessed: syncResult.recordsProcessed,
                recordsAdded: syncResult.recordsAdded,
                recordsUpdated: syncResult.recordsUpdated,
            });
            this.logger.log('Feed sync completed successfully', {
                feedId: id,
                name: feed.name,
                recordsProcessed: syncResult.recordsProcessed,
                userId,
            });
            return syncResult;
        }
        catch (error) {
            this.logger.error('Failed to sync feed', {
                feedId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Delete feed
     */
    async deleteFeed(id, userId) {
        try {
            const feed = await this.feedRepository.findOne({
                where: { id },
            });
            if (!feed) {
                throw new common_1.NotFoundException('Feed not found');
            }
            this.logger.debug('Deleting feed', {
                feedId: id,
                name: feed.name,
                userId,
            });
            await this.feedRepository.remove(feed);
            await this.auditService.logUserAction(userId, 'delete', 'vulnerability_feed', id, {
                name: feed.name,
                feedType: feed.feedType,
            });
            this.logger.log('Feed deleted successfully', {
                feedId: id,
                name: feed.name,
                userId,
            });
        }
        catch (error) {
            this.logger.error('Failed to delete feed', {
                feedId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Monitor feeds for scheduled synchronization
     */
    async monitorFeeds() {
        try {
            this.logger.debug('Checking feeds for synchronization');
            const feedsDueForSync = await this.feedRepository.find({
                where: {
                    isActive: true,
                    status: 'active',
                    nextSyncAt: (0, typeorm_2.LessThan)(new Date()),
                },
            });
            for (const feed of feedsDueForSync) {
                try {
                    await this.performFeedSync(feed);
                }
                catch (error) {
                    this.logger.error('Failed to sync feed during monitoring', {
                        feedId: feed.id,
                        feedName: feed.name,
                        error: error.message,
                    });
                }
            }
            if (feedsDueForSync.length > 0) {
                this.logger.log('Processed scheduled feed syncs', {
                    count: feedsDueForSync.length,
                });
            }
        }
        catch (error) {
            this.logger.error('Failed to monitor feeds', {
                error: error.message,
            });
        }
    }
    /**
     * Perform feed synchronization
     */
    async performFeedSync(feed) {
        const startTime = Date.now();
        try {
            feed.startSync();
            await this.feedRepository.save(feed);
            // Simulate feed processing - in real implementation, this would:
            // 1. Fetch data from the feed URL
            // 2. Parse the data according to the feed format
            // 3. Validate and transform the data
            // 4. Create or update vulnerabilities
            // 5. Track statistics
            const mockResults = {
                duration: Math.floor((Date.now() - startTime) / 1000),
                recordsProcessed: Math.floor(Math.random() * 100) + 10,
                recordsAdded: Math.floor(Math.random() * 20) + 1,
                recordsUpdated: Math.floor(Math.random() * 30) + 5,
                recordsSkipped: Math.floor(Math.random() * 10),
            };
            feed.completeSync(mockResults);
            await this.feedRepository.save(feed);
            return mockResults;
        }
        catch (error) {
            feed.failSync(error.message);
            await this.feedRepository.save(feed);
            throw error;
        }
    }
};
exports.VulnerabilityFeedService = VulnerabilityFeedService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_5_MINUTES),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], VulnerabilityFeedService.prototype, "monitorFeeds", null);
exports.VulnerabilityFeedService = VulnerabilityFeedService = VulnerabilityFeedService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(vulnerability_feed_entity_1.VulnerabilityFeed)),
    __param(1, (0, typeorm_1.InjectRepository)(vulnerability_entity_1.Vulnerability)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _c : Object, typeof (_d = typeof audit_service_1.AuditService !== "undefined" && audit_service_1.AuditService) === "function" ? _d : Object, typeof (_e = typeof notification_service_1.NotificationService !== "undefined" && notification_service_1.NotificationService) === "function" ? _e : Object])
], VulnerabilityFeedService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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