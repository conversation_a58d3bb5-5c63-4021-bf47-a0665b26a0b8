{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\__tests__\\is-ip-address.validator.spec.ts", "mappings": ";;;;;;;;;;;AAAA,qDAA2C;AAC3C,yDAAiD;AACjD,wEAOoC;AAEpC,MAAM,SAAS;CAYd;AAVC;IADC,IAAA,qCAAW,GAAE;;4CACI;AAGlB;IADC,IAAA,uCAAa,GAAE;;8CACI;AAGpB;IADC,IAAA,uCAAa,GAAE;;8CACI;AAGpB;IADC,IAAA,2CAAiB,GAAE;;kDACI;AAG1B,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,IAAI,UAAiC,CAAC;IAEtC,UAAU,CAAC,GAAG,EAAE;QACd,UAAU,GAAG,IAAI,+CAAqB,EAAE,CAAC;IAC3C,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;YACxB,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,MAAM,SAAS,GAAG;oBAChB,aAAa;oBACb,UAAU;oBACV,YAAY;oBACZ,SAAS;oBACT,WAAW;oBACX,iBAAiB;iBAClB,CAAC;gBAEF,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;oBACrB,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3E,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;gBAC9D,MAAM,YAAY,GAAG,CAAC,SAAS,CAAC,CAAC;gBACjC,MAAM,OAAO,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;gBAExC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;oBACxB,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChF,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,MAAM,SAAS,GAAG;oBAChB,KAAK;oBACL,SAAS;oBACT,sBAAsB,EAAE,aAAa;oBACrC,SAAS,EAAE,uBAAuB;iBACnC,CAAC;gBAEF,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;oBACrB,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3E,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;gBAC9D,MAAM,YAAY,GAAG;oBACnB,IAAI;oBACJ,aAAa;oBACb,yCAAyC;oBACzC,8BAA8B;iBAC/B,CAAC;gBACF,MAAM,OAAO,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;gBAExC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;oBACxB,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChF,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;gBAC5C,MAAM,UAAU,GAAG;oBACjB,WAAW,EAAE,eAAe;oBAC5B,WAAW,EAAE,kBAAkB;oBAC/B,eAAe,EAAE,kBAAkB;oBACnC,SAAS,EAAE,YAAY;oBACvB,EAAE,EAAE,eAAe;oBACnB,IAAI,EAAE,aAAa;oBACnB,SAAS,EAAE,kBAAkB;oBAC7B,GAAG,EAAE,2BAA2B;oBAChC,SAAS,EAAE,eAAe;iBAC3B,CAAC;gBAEF,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;oBACtB,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5E,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,OAAO,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;gBAEtD,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC1F,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,OAAO,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;gBAEtD,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzF,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;gBAC5C,MAAM,OAAO,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;gBAExC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC1F,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;gBAC7C,MAAM,OAAO,GAAG,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;gBAEzC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACxF,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAClF,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;gBAC1C,MAAM,CAAC,wCAAc,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACzD,MAAM,CAAC,wCAAc,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACjD,MAAM,CAAC,wCAAc,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;gBAChD,MAAM,CAAC,wCAAc,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzD,MAAM,CAAC,wCAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjD,MAAM,CAAC,wCAAc,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;gBAC3C,MAAM,CAAC,wCAAc,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnF,MAAM,CAAC,wCAAc,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;YACzB,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,CAAC,wCAAc,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,UAAU,GAAG,wCAAc,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;gBACpE,MAAM,CAAC,UAAU,CAAC,CAAC,UAAU,EAAE,CAAC;gBAChC,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;gBAClD,MAAM,CAAC,wCAAc,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACzD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;YAC7B,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,CAAC,wCAAc,CAAC,aAAa,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnF,MAAM,CAAC,wCAAc,CAAC,aAAa,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpF,MAAM,CAAC,wCAAc,CAAC,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5E,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;gBAC7C,MAAM,CAAC,wCAAc,CAAC,aAAa,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,GAAG,GAAG,IAAA,gCAAY,EAAC,SAAS,EAAE;gBAClC,SAAS,EAAE,aAAa;gBACxB,WAAW,EAAE,UAAU;gBACvB,WAAW,EAAE,KAAK;gBAClB,eAAe,EAAE,SAAS;aAC3B,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,GAAG,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,GAAG,GAAG,IAAA,gCAAY,EAAC,SAAS,EAAE;gBAClC,SAAS,EAAE,YAAY;gBACvB,WAAW,EAAE,KAAK,EAAE,qBAAqB;gBACzC,WAAW,EAAE,aAAa,EAAE,qBAAqB;gBACjD,eAAe,EAAE,aAAa,EAAE,6BAA6B;aAC9D,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,GAAG,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\__tests__\\is-ip-address.validator.spec.ts"], "sourcesContent": ["import { validate } from 'class-validator';\r\nimport { plainToClass } from 'class-transformer';\r\nimport {\r\n  IsIpAddress,\r\n  IsIPv4Address,\r\n  IsIPv6Address,\r\n  IsPublicIpAddress,\r\n  IpAddressUtils,\r\n  IsIpAddressConstraint,\r\n} from '../is-ip-address.validator';\r\n\r\nclass TestIpDto {\r\n  @IsIpAddress()\r\n  ipAddress: string;\r\n\r\n  @IsIPv4Address()\r\n  ipv4Address: string;\r\n\r\n  @IsIPv6Address()\r\n  ipv6Address: string;\r\n\r\n  @IsPublicIpAddress()\r\n  publicIpAddress: string;\r\n}\r\n\r\ndescribe('IP Address Validator', () => {\r\n  let constraint: IsIpAddressConstraint;\r\n\r\n  beforeEach(() => {\r\n    constraint = new IsIpAddressConstraint();\r\n  });\r\n\r\n  describe('IsIpAddressConstraint', () => {\r\n    describe('validate', () => {\r\n      it('should validate IPv4 addresses', () => {\r\n        const validIPv4 = [\r\n          '***********',\r\n          '********',\r\n          '**********',\r\n          '*******',\r\n          '127.0.0.1',\r\n          '***************',\r\n        ];\r\n\r\n        validIPv4.forEach(ip => {\r\n          expect(constraint.validate(ip, { constraints: [{}] } as any)).toBe(true);\r\n        });\r\n      });\r\n\r\n      it('should validate reserved IPv4 addresses when allowed', () => {\r\n        const reservedIPv4 = ['0.0.0.0'];\r\n        const options = { allowReserved: true };\r\n\r\n        reservedIPv4.forEach(ip => {\r\n          expect(constraint.validate(ip, { constraints: [options] } as any)).toBe(true);\r\n        });\r\n      });\r\n\r\n      it('should validate IPv6 addresses', () => {\r\n        const validIPv6 = [\r\n          '::1',\r\n          'fe80::1',\r\n          '2001:4860:4860::8888', // Google DNS\r\n          'fc00::1', // Unique Local Address\r\n        ];\r\n\r\n        validIPv6.forEach(ip => {\r\n          expect(constraint.validate(ip, { constraints: [{}] } as any)).toBe(true);\r\n        });\r\n      });\r\n\r\n      it('should validate reserved IPv6 addresses when allowed', () => {\r\n        const reservedIPv6 = [\r\n          '::',  \r\n          '2001:db8::1',\r\n          '2001:0db8:85a3:0000:0000:8a2e:0370:7334',\r\n          '2001:db8:85a3::8a2e:370:7334',\r\n        ];\r\n        const options = { allowReserved: true };\r\n\r\n        reservedIPv6.forEach(ip => {\r\n          expect(constraint.validate(ip, { constraints: [options] } as any)).toBe(true);\r\n        });\r\n      });\r\n\r\n      it('should reject invalid IP addresses', () => {\r\n        const invalidIPs = [\r\n          '256.1.1.1', // Invalid IPv4\r\n          '192.168.1', // Incomplete IPv4\r\n          '***********.1', // Too many octets\r\n          'invalid', // Not an IP\r\n          '', // Empty string\r\n          null, // Null value\r\n          undefined, // Undefined value\r\n          123, // Number instead of string\r\n          'gggg::1', // Invalid IPv6\r\n        ];\r\n\r\n        invalidIPs.forEach(ip => {\r\n          expect(constraint.validate(ip, { constraints: [{}] } as any)).toBe(false);\r\n        });\r\n      });\r\n\r\n      it('should respect allowIPv4 option', () => {\r\n        const options = { allowIPv4: false, allowIPv6: true };\r\n        \r\n        expect(constraint.validate('***********', { constraints: [options] } as any)).toBe(false);\r\n        expect(constraint.validate('::1', { constraints: [options] } as any)).toBe(true);\r\n      });\r\n\r\n      it('should respect allowIPv6 option', () => {\r\n        const options = { allowIPv4: true, allowIPv6: false };\r\n        \r\n        expect(constraint.validate('***********', { constraints: [options] } as any)).toBe(true);\r\n        expect(constraint.validate('::1', { constraints: [options] } as any)).toBe(false);\r\n      });\r\n\r\n      it('should respect allowPrivate option', () => {\r\n        const options = { allowPrivate: false };\r\n        \r\n        expect(constraint.validate('***********', { constraints: [options] } as any)).toBe(false);\r\n        expect(constraint.validate('*******', { constraints: [options] } as any)).toBe(true);\r\n      });\r\n\r\n      it('should respect allowLoopback option', () => {\r\n        const options = { allowLoopback: false };\r\n        \r\n        expect(constraint.validate('127.0.0.1', { constraints: [options] } as any)).toBe(false);\r\n        expect(constraint.validate('::1', { constraints: [options] } as any)).toBe(false);\r\n        expect(constraint.validate('*******', { constraints: [options] } as any)).toBe(true);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('IpAddressUtils', () => {\r\n    describe('getVersion', () => {\r\n      it('should return correct IP version', () => {\r\n        expect(IpAddressUtils.getVersion('***********')).toBe(4);\r\n        expect(IpAddressUtils.getVersion('::1')).toBe(6);\r\n        expect(IpAddressUtils.getVersion('invalid')).toBe(0);\r\n      });\r\n    });\r\n\r\n    describe('isValid', () => {\r\n      it('should validate IP addresses correctly', () => {\r\n        expect(IpAddressUtils.isValid('***********')).toBe(true);\r\n        expect(IpAddressUtils.isValid('::1')).toBe(true);\r\n        expect(IpAddressUtils.isValid('invalid')).toBe(false);\r\n      });\r\n\r\n      it('should respect validation options', () => {\r\n        expect(IpAddressUtils.isValid('***********', { allowPrivate: false })).toBe(false);\r\n        expect(IpAddressUtils.isValid('*******', { allowPrivate: false })).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('normalize', () => {\r\n      it('should normalize IPv4 addresses', () => {\r\n        expect(IpAddressUtils.normalize('***********')).toBe('***********');\r\n      });\r\n\r\n      it('should normalize IPv6 addresses', () => {\r\n        const normalized = IpAddressUtils.normalize('2001:4860:4860::8888');\r\n        expect(normalized).toBeTruthy();\r\n        expect(normalized?.includes(':')).toBe(true);\r\n      });\r\n\r\n      it('should return null for invalid addresses', () => {\r\n        expect(IpAddressUtils.normalize('invalid')).toBeNull();\r\n      });\r\n    });\r\n\r\n    describe('isInCidrRange', () => {\r\n      it('should check IPv4 CIDR ranges correctly', () => {\r\n        expect(IpAddressUtils.isInCidrRange('***********00', '***********/24')).toBe(true);\r\n        expect(IpAddressUtils.isInCidrRange('*************', '***********/24')).toBe(false);\r\n        expect(IpAddressUtils.isInCidrRange('********', '10.0.0.0/8')).toBe(true);\r\n      });\r\n\r\n      it('should handle different IP versions', () => {\r\n        expect(IpAddressUtils.isInCidrRange('***********', '::1/128')).toBe(false);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('DTO Validation', () => {\r\n    it('should validate DTO with valid IP addresses', async () => {\r\n      const dto = plainToClass(TestIpDto, {\r\n        ipAddress: '***********',\r\n        ipv4Address: '********',\r\n        ipv6Address: '::1',\r\n        publicIpAddress: '*******',\r\n      });\r\n\r\n      const errors = await validate(dto);\r\n      expect(errors).toHaveLength(0);\r\n    });\r\n\r\n    it('should reject DTO with invalid IP addresses', async () => {\r\n      const dto = plainToClass(TestIpDto, {\r\n        ipAddress: 'invalid-ip',\r\n        ipv4Address: '::1', // IPv6 in IPv4 field\r\n        ipv6Address: '***********', // IPv4 in IPv6 field\r\n        publicIpAddress: '***********', // Private IP in public field\r\n      });\r\n\r\n      const errors = await validate(dto);\r\n      expect(errors.length).toBeGreaterThan(0);\r\n    });\r\n  });\r\n});"], "version": 3}