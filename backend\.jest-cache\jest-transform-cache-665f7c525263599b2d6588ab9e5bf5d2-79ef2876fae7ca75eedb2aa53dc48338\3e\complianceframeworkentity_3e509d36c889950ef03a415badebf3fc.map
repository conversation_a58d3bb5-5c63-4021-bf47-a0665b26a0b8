{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\domain\\entities\\compliance-framework.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAQiB;AACjB,iFAAsE;AACtE,yEAA8D;AAE9D;;;GAGG;AAKI,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IA+N9B;;OAEG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CACzD,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,QAAgD;QACpE,MAAM,QAAQ,GAAU,EAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1C,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAChC,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBAClC,QAAQ,CAAC,IAAI,CAAC,EAAE,GAAG,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,MAAM,QAAQ,GAAU,EAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1C,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAChC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;oBACxB,QAAQ,CAAC,IAAI,CAAC,EAAE,GAAG,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,SAAiB;QAC9B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAChD,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;YAC9D,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,EAAE,GAAG,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC;YACtE,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,QAAgB;QAC5B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,IAAI,IAAI,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,gBAAgB;QAChB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3E,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,8BAA8B;YAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5D,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC;YAC3C,IAAI,SAAS,CAAC,MAAM,KAAK,eAAe,CAAC,IAAI,EAAE,CAAC;gBAC9C,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC3C,CAAC;YAED,iBAAiB;YACjB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBACzD,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACrD,MAAM,CAAC,IAAI,CAAC,UAAU,WAAW,GAAG,CAAC,iCAAiC,CAAC,CAAC;gBAC1E,CAAC;qBAAM,CAAC;oBACN,6CAA6C;oBAC7C,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBAClD,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;oBAC7C,IAAI,UAAU,CAAC,MAAM,KAAK,gBAAgB,CAAC,IAAI,EAAE,CAAC;wBAChD,MAAM,CAAC,IAAI,CAAC,yBAAyB,MAAM,CAAC,IAAI,iBAAiB,CAAC,CAAC;oBACrE,CAAC;oBAED,gCAAgC;oBAChC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE;wBAChD,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;4BAC1C,MAAM,CAAC,IAAI,CAAC,WAAW,YAAY,GAAG,CAAC,cAAc,MAAM,CAAC,IAAI,iCAAiC,CAAC,CAAC;wBACrG,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC;QACvE,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;QAC/D,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;QAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC;QAElD,kCAAkC;QAClC,MAAM,iBAAiB,GAAG,aAAa,GAAG,CAAC,CAAC;QAC5C,MAAM,kBAAkB,GAAG,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QACvE,MAAM,mBAAmB,GAAG,mBAAmB,GAAG,GAAG,CAAC;QACtD,MAAM,gBAAgB,GAAG,OAAO,GAAG,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,GAAG,kBAAkB,GAAG,gBAAgB,GAAG,mBAAmB,CAAC,CAAC;IACtG,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACnD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC;QAE9C,MAAM,MAAM,GAAG;YACb;gBACE,IAAI,EAAE,2BAA2B;gBACjC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;gBACrC,UAAU,EAAE,CAAC,sBAAsB,EAAE,cAAc,EAAE,yBAAyB,CAAC;aAChF;YACD;gBACE,IAAI,EAAE,kCAAkC;gBACxC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;gBACrC,UAAU,EAAE,CAAC,iBAAiB,EAAE,yBAAyB,EAAE,oBAAoB,CAAC;aACjF;YACD;gBACE,IAAI,EAAE,gBAAgB;gBACtB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;gBACrC,UAAU,EAAE,CAAC,wBAAwB,EAAE,sBAAsB,EAAE,oBAAoB,CAAC;aACrF;YACD;gBACE,IAAI,EAAE,wBAAwB;gBAC9B,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;gBACrC,UAAU,EAAE,CAAC,iBAAiB,EAAE,YAAY,EAAE,aAAa,CAAC;aAC7D;SACF,CAAC;QAEF,OAAO;YACL,MAAM;YACN,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;SACxE,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAY,EAAE,UAAkB,KAAK;QACzC,OAAO;YACL,IAAI;YACJ,WAAW,EAAE,GAAG,IAAI,CAAC,WAAW,eAAe;YAC/C,IAAI,EAAE,QAAQ;YACd,OAAO;YACP,QAAQ,EAAE,KAAK,EAAE,gCAAgC;YACjD,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC7D,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC;SACzC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACpC,UAAU,EAAE,mBAAmB;SAChC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM;YAC1C,eAAe,EAAE,IAAI,CAAC,wBAAwB,EAAE;YAChD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM;YACpD,gBAAgB,EAAE,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,MAAM;YAC/D,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;CACF,CAAA;AAlbY,kDAAmB;AAE9B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;+CACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;iDACX;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;wDACL;AAuBpB;IAlBC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,MAAM;YACN,UAAU;YACV,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,cAAc;YACd,OAAO;YACP,MAAM;YACN,KAAK;YACL,OAAO;YACP,SAAS;YACT,QAAQ;SACT;KACF,CAAC;;iDACW;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;oDACX;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;qDAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;0DA4FxB;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAuCxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;iDACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;sDAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;sDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;sDAAC;AAIhB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,mDAAoB,EAAE,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC;;wDACtC;AAGpC;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,2CAAgB,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC;;qDACjC;8BA7NlB,mBAAmB;IAJ/B,IAAA,gBAAM,EAAC,uBAAuB,CAAC;IAC/B,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,SAAS,CAAC,CAAC;GACN,mBAAmB,CAkb/B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\domain\\entities\\compliance-framework.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n} from 'typeorm';\r\nimport { ComplianceAssessment } from './compliance-assessment.entity';\r\nimport { PolicyDefinition } from './policy-definition.entity';\r\n\r\n/**\r\n * Compliance Framework entity\r\n * Represents regulatory and compliance frameworks (SOC2, ISO27001, GDPR, HIPAA, PCI-DSS, etc.)\r\n */\r\n@Entity('compliance_frameworks')\r\n@Index(['type'])\r\n@Index(['isActive'])\r\n@Index(['version'])\r\nexport class ComplianceFramework {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Framework name\r\n   */\r\n  @Column({ length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Framework description\r\n   */\r\n  @Column({ type: 'text' })\r\n  description: string;\r\n\r\n  /**\r\n   * Framework type/category\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'SOC2',\r\n      'ISO27001',\r\n      'GDPR',\r\n      'HIPAA',\r\n      'PCI_DSS',\r\n      'NIST_CSF',\r\n      'CIS_CONTROLS',\r\n      'COBIT',\r\n      'ITIL',\r\n      'SOX',\r\n      'FISMA',\r\n      'FedRAMP',\r\n      'CUSTOM',\r\n    ],\r\n  })\r\n  type: string;\r\n\r\n  /**\r\n   * Framework version\r\n   */\r\n  @Column({ default: '1.0' })\r\n  version: string;\r\n\r\n  /**\r\n   * Whether framework is active\r\n   */\r\n  @Column({ name: 'is_active', default: true })\r\n  isActive: boolean;\r\n\r\n  /**\r\n   * Framework configuration and requirements\r\n   */\r\n  @Column({ type: 'jsonb' })\r\n  configuration: {\r\n    // Framework scope and applicability\r\n    scope?: {\r\n      description: string;\r\n      applicableAssets?: string[];\r\n      applicableProcesses?: string[];\r\n      exclusions?: string[];\r\n    };\r\n    \r\n    // Control domains and categories\r\n    domains: Array<{\r\n      id: string;\r\n      name: string;\r\n      description: string;\r\n      weight?: number; // for scoring\r\n      controls: Array<{\r\n        id: string;\r\n        name: string;\r\n        description: string;\r\n        type: 'preventive' | 'detective' | 'corrective' | 'compensating';\r\n        priority: 'low' | 'medium' | 'high' | 'critical';\r\n        frequency: 'continuous' | 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';\r\n        automatable: boolean;\r\n        evidenceRequirements: string[];\r\n        testingProcedures?: string[];\r\n        maturityLevels?: Array<{\r\n          level: number;\r\n          description: string;\r\n          criteria: string[];\r\n        }>;\r\n      }>;\r\n    }>;\r\n    \r\n    // Assessment methodology\r\n    assessment?: {\r\n      methodology: string;\r\n      scoringModel: 'binary' | 'maturity' | 'weighted' | 'risk_based';\r\n      passingScore?: number;\r\n      assessmentFrequency: 'monthly' | 'quarterly' | 'semi_annually' | 'annually';\r\n      evidenceRetention: number; // days\r\n      assessorRequirements?: string[];\r\n    };\r\n    \r\n    // Reporting requirements\r\n    reporting?: {\r\n      requiredReports: string[];\r\n      reportingFrequency: 'monthly' | 'quarterly' | 'annually';\r\n      stakeholders: string[];\r\n      deliverables: string[];\r\n      certificationRequired?: boolean;\r\n      externalAuditorRequired?: boolean;\r\n    };\r\n    \r\n    // Regulatory information\r\n    regulatory?: {\r\n      jurisdiction: string[];\r\n      regulatoryBody: string;\r\n      penalties?: {\r\n        type: string;\r\n        description: string;\r\n        maxPenalty?: string;\r\n      }[];\r\n      complianceDeadlines?: Array<{\r\n        requirement: string;\r\n        deadline: string;\r\n        description: string;\r\n      }>;\r\n    };\r\n    \r\n    // Integration mappings\r\n    mappings?: {\r\n      // Map to other frameworks\r\n      frameworkMappings?: Array<{\r\n        frameworkId: string;\r\n        controlMappings: Array<{\r\n          sourceControlId: string;\r\n          targetControlId: string;\r\n          mappingType: 'equivalent' | 'partial' | 'related';\r\n        }>;\r\n      }>;\r\n      \r\n      // Map to internal systems\r\n      systemMappings?: Array<{\r\n        systemType: 'vulnerability_management' | 'incident_response' | 'asset_management';\r\n        mappings: Array<{\r\n          controlId: string;\r\n          systemField: string;\r\n          evaluationCriteria: string;\r\n        }>;\r\n      }>;\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Framework metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: {\r\n    // Publication information\r\n    publishedBy?: string;\r\n    publishedDate?: string;\r\n    lastUpdated?: string;\r\n    nextReview?: string;\r\n    \r\n    // Implementation guidance\r\n    implementationGuide?: string;\r\n    bestPractices?: string[];\r\n    commonPitfalls?: string[];\r\n    \r\n    // Industry information\r\n    applicableIndustries?: string[];\r\n    organizationSize?: 'small' | 'medium' | 'large' | 'enterprise' | 'any';\r\n    geographicScope?: string[];\r\n    \r\n    // Certification information\r\n    certificationBody?: string;\r\n    certificationProcess?: string;\r\n    certificationValidity?: number; // months\r\n    \r\n    // Cost and effort estimates\r\n    implementationCost?: {\r\n      low: number;\r\n      high: number;\r\n      currency: string;\r\n    };\r\n    implementationTime?: {\r\n      low: number; // months\r\n      high: number;\r\n    };\r\n    maintenanceEffort?: string;\r\n    \r\n    // Dependencies\r\n    prerequisites?: string[];\r\n    dependencies?: string[];\r\n    relatedFrameworks?: string[];\r\n  };\r\n\r\n  /**\r\n   * Framework tags for categorization\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * User who created the framework\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  /**\r\n   * User who last updated the framework\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @OneToMany(() => ComplianceAssessment, assessment => assessment.framework)\r\n  assessments: ComplianceAssessment[];\r\n\r\n  @OneToMany(() => PolicyDefinition, policy => policy.framework)\r\n  policies: PolicyDefinition[];\r\n\r\n  /**\r\n   * Get total number of controls\r\n   */\r\n  get totalControls(): number {\r\n    return this.configuration.domains.reduce((total, domain) => \r\n      total + domain.controls.length, 0\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get controls by priority\r\n   */\r\n  getControlsByPriority(priority: 'low' | 'medium' | 'high' | 'critical'): any[] {\r\n    const controls: any[] = [];\r\n    this.configuration.domains.forEach(domain => {\r\n      domain.controls.forEach(control => {\r\n        if (control.priority === priority) {\r\n          controls.push({ ...control, domainId: domain.id, domainName: domain.name });\r\n        }\r\n      });\r\n    });\r\n    return controls;\r\n  }\r\n\r\n  /**\r\n   * Get automatable controls\r\n   */\r\n  get automatableControls(): any[] {\r\n    const controls: any[] = [];\r\n    this.configuration.domains.forEach(domain => {\r\n      domain.controls.forEach(control => {\r\n        if (control.automatable) {\r\n          controls.push({ ...control, domainId: domain.id, domainName: domain.name });\r\n        }\r\n      });\r\n    });\r\n    return controls;\r\n  }\r\n\r\n  /**\r\n   * Get control by ID\r\n   */\r\n  getControlById(controlId: string): any | null {\r\n    for (const domain of this.configuration.domains) {\r\n      const control = domain.controls.find(c => c.id === controlId);\r\n      if (control) {\r\n        return { ...control, domainId: domain.id, domainName: domain.name };\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Get domain by ID\r\n   */\r\n  getDomainById(domainId: string): any | null {\r\n    return this.configuration.domains.find(d => d.id === domainId) || null;\r\n  }\r\n\r\n  /**\r\n   * Validate framework configuration\r\n   */\r\n  validateConfiguration(): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    // Check domains\r\n    if (!this.configuration.domains || this.configuration.domains.length === 0) {\r\n      errors.push('At least one domain is required');\r\n    } else {\r\n      // Check domain IDs are unique\r\n      const domainIds = this.configuration.domains.map(d => d.id);\r\n      const uniqueDomainIds = new Set(domainIds);\r\n      if (domainIds.length !== uniqueDomainIds.size) {\r\n        errors.push('Domain IDs must be unique');\r\n      }\r\n\r\n      // Check controls\r\n      this.configuration.domains.forEach((domain, domainIndex) => {\r\n        if (!domain.controls || domain.controls.length === 0) {\r\n          errors.push(`Domain ${domainIndex + 1} must have at least one control`);\r\n        } else {\r\n          // Check control IDs are unique within domain\r\n          const controlIds = domain.controls.map(c => c.id);\r\n          const uniqueControlIds = new Set(controlIds);\r\n          if (controlIds.length !== uniqueControlIds.size) {\r\n            errors.push(`Control IDs in domain ${domain.name} must be unique`);\r\n          }\r\n\r\n          // Check required control fields\r\n          domain.controls.forEach((control, controlIndex) => {\r\n            if (!control.name || !control.description) {\r\n              errors.push(`Control ${controlIndex + 1} in domain ${domain.name} is missing name or description`);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate framework complexity score\r\n   */\r\n  calculateComplexityScore(): number {\r\n    const totalControls = this.totalControls;\r\n    const criticalControls = this.getControlsByPriority('critical').length;\r\n    const highControls = this.getControlsByPriority('high').length;\r\n    const automatableControls = this.automatableControls.length;\r\n    const domains = this.configuration.domains.length;\r\n\r\n    // Weighted complexity calculation\r\n    const controlComplexity = totalControls * 1;\r\n    const priorityComplexity = (criticalControls * 3) + (highControls * 2);\r\n    const automationReduction = automatableControls * 0.5;\r\n    const domainComplexity = domains * 2;\r\n\r\n    return Math.max(1, controlComplexity + priorityComplexity + domainComplexity - automationReduction);\r\n  }\r\n\r\n  /**\r\n   * Get implementation timeline estimate\r\n   */\r\n  getImplementationTimeline(): { phases: any[]; totalMonths: number } {\r\n    const complexity = this.calculateComplexityScore();\r\n    const baseMonths = Math.ceil(complexity / 10);\r\n\r\n    const phases = [\r\n      {\r\n        name: 'Planning and Gap Analysis',\r\n        duration: Math.ceil(baseMonths * 0.2),\r\n        activities: ['Framework assessment', 'Gap analysis', 'Implementation planning'],\r\n      },\r\n      {\r\n        name: 'Policy and Procedure Development',\r\n        duration: Math.ceil(baseMonths * 0.3),\r\n        activities: ['Policy creation', 'Procedure documentation', 'Training materials'],\r\n      },\r\n      {\r\n        name: 'Implementation',\r\n        duration: Math.ceil(baseMonths * 0.4),\r\n        activities: ['Control implementation', 'System configuration', 'Process deployment'],\r\n      },\r\n      {\r\n        name: 'Testing and Validation',\r\n        duration: Math.ceil(baseMonths * 0.1),\r\n        activities: ['Control testing', 'Validation', 'Remediation'],\r\n      },\r\n    ];\r\n\r\n    return {\r\n      phases,\r\n      totalMonths: phases.reduce((total, phase) => total + phase.duration, 0),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clone framework for customization\r\n   */\r\n  clone(name: string, version: string = '1.0'): Partial<ComplianceFramework> {\r\n    return {\r\n      name,\r\n      description: `${this.description} (Customized)`,\r\n      type: 'CUSTOM',\r\n      version,\r\n      isActive: false, // New frameworks start inactive\r\n      configuration: JSON.parse(JSON.stringify(this.configuration)),\r\n      metadata: JSON.parse(JSON.stringify(this.metadata)),\r\n      tags: [...this.tags, 'custom', 'cloned'],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export framework for sharing\r\n   */\r\n  exportFramework(): any {\r\n    return {\r\n      name: this.name,\r\n      description: this.description,\r\n      type: this.type,\r\n      version: this.version,\r\n      configuration: this.configuration,\r\n      metadata: this.metadata,\r\n      tags: this.tags,\r\n      exportedAt: new Date().toISOString(),\r\n      exportedBy: 'Sentinel Platform',\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get framework summary\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      name: this.name,\r\n      type: this.type,\r\n      version: this.version,\r\n      isActive: this.isActive,\r\n      totalControls: this.totalControls,\r\n      domains: this.configuration.domains.length,\r\n      complexityScore: this.calculateComplexityScore(),\r\n      automatableControls: this.automatableControls.length,\r\n      criticalControls: this.getControlsByPriority('critical').length,\r\n      createdAt: this.createdAt,\r\n      updatedAt: this.updatedAt,\r\n    };\r\n  }\r\n}\r\n"], "version": 3}