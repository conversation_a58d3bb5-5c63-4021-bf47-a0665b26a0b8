a8244619dd818484a0edc0c0b55f9b46
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventType = void 0;
var EventType;
(function (EventType) {
    // Authentication events
    EventType["LOGIN_SUCCESS"] = "LOGIN_SUCCESS";
    EventType["LOGIN_FAILURE"] = "LOGIN_FAILURE";
    EventType["LOGOUT"] = "LOGOUT";
    EventType["PASSWORD_CHANGE"] = "PASSWORD_CHANGE";
    EventType["MFA_ENABLED"] = "MFA_ENABLED";
    EventType["MFA_DISABLED"] = "MFA_DISABLED";
    EventType["ACCOUNT_LOCKED"] = "ACCOUNT_LOCKED";
    EventType["ACCOUNT_UNLOCKED"] = "ACCOUNT_UNLOCKED";
    // Authorization events
    EventType["PERMISSION_GRANTED"] = "PERMISSION_GRANTED";
    EventType["PERMISSION_DENIED"] = "PERMISSION_DENIED";
    EventType["ROLE_ASSIGNED"] = "ROLE_ASSIGNED";
    EventType["ROLE_REMOVED"] = "ROLE_REMOVED";
    EventType["ACCESS_DENIED"] = "ACCESS_DENIED";
    // Network events
    EventType["CONNECTION_ESTABLISHED"] = "CONNECTION_ESTABLISHED";
    EventType["CONNECTION_CLOSED"] = "CONNECTION_CLOSED";
    EventType["SUSPICIOUS_CONNECTION"] = "SUSPICIOUS_CONNECTION";
    EventType["PORT_SCAN_DETECTED"] = "PORT_SCAN_DETECTED";
    EventType["DDoS_ATTACK"] = "DDOS_ATTACK";
    // Vulnerability events
    EventType["VULNERABILITY_DETECTED"] = "VULNERABILITY_DETECTED";
    EventType["VULNERABILITY_FIXED"] = "VULNERABILITY_FIXED";
    EventType["VULNERABILITY_EXPLOITED"] = "VULNERABILITY_EXPLOITED";
    EventType["PATCH_APPLIED"] = "PATCH_APPLIED";
    EventType["PATCH_FAILED"] = "PATCH_FAILED";
    // Malware events
    EventType["MALWARE_DETECTED"] = "MALWARE_DETECTED";
    EventType["MALWARE_QUARANTINED"] = "MALWARE_QUARANTINED";
    EventType["MALWARE_REMOVED"] = "MALWARE_REMOVED";
    EventType["SUSPICIOUS_FILE"] = "SUSPICIOUS_FILE";
    // Data events
    EventType["DATA_ACCESS"] = "DATA_ACCESS";
    EventType["DATA_MODIFICATION"] = "DATA_MODIFICATION";
    EventType["DATA_DELETION"] = "DATA_DELETION";
    EventType["DATA_EXPORT"] = "DATA_EXPORT";
    EventType["DATA_BREACH"] = "DATA_BREACH";
    // System events
    EventType["SYSTEM_STARTUP"] = "SYSTEM_STARTUP";
    EventType["SYSTEM_SHUTDOWN"] = "SYSTEM_SHUTDOWN";
    EventType["SERVICE_STARTED"] = "SERVICE_STARTED";
    EventType["SERVICE_STOPPED"] = "SERVICE_STOPPED";
    EventType["CONFIGURATION_CHANGE"] = "CONFIGURATION_CHANGE";
    // Threat events
    EventType["THREAT_DETECTED"] = "THREAT_DETECTED";
    EventType["THREAT_BLOCKED"] = "THREAT_BLOCKED";
    EventType["THREAT_MITIGATED"] = "THREAT_MITIGATED";
    EventType["IOC_DETECTED"] = "IOC_DETECTED";
    // Compliance events
    EventType["COMPLIANCE_VIOLATION"] = "COMPLIANCE_VIOLATION";
    EventType["COMPLIANCE_CHECK"] = "COMPLIANCE_CHECK";
    EventType["AUDIT_LOG"] = "AUDIT_LOG";
    // Custom events
    EventType["CUSTOM"] = "CUSTOM";
})(EventType || (exports.EventType = EventType = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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