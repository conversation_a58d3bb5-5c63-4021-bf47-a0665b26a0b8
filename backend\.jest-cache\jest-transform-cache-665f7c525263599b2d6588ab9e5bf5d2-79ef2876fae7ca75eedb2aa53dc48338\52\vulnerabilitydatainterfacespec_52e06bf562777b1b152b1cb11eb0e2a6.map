{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\data\\__tests__\\vulnerability-data.interface.spec.ts", "mappings": ";;AAAA,kFA6ByC;AACzC,4FAAmF;AACnF,gFAAuE;AAEvE,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;IAC3C,IAAI,qBAAwC,CAAC;IAC7C,IAAI,kBAAgD,CAAC;IACrD,IAAI,cAAuC,CAAC;IAC5C,IAAI,oBAAoD,CAAC;IACzD,IAAI,cAAwC,CAAC;IAC7C,IAAI,gBAA+C,CAAC;IACpD,IAAI,eAA6C,CAAC;IAClD,IAAI,cAA2C,CAAC;IAChD,IAAI,oBAAmD,CAAC;IACxD,IAAI,kBAA+C,CAAC;IACpD,IAAI,oBAAmD,CAAC;IACxD,IAAI,iBAA6C,CAAC;IAClD,IAAI,iBAA6C,CAAC;IAClD,IAAI,gBAA6C,CAAC;IAClD,IAAI,eAAyC,CAAC;IAC9C,IAAI,kBAAmD,CAAC;IAExD,UAAU,CAAC,GAAG,EAAE;QACd,kBAAkB,GAAG;YACnB;gBACE,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,iBAAiB;gBACxB,MAAM,EAAE,sBAAsB;aAC/B;YACD;gBACE,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,cAAc;gBACrB,MAAM,EAAE,mBAAmB;aAC5B;SACF,CAAC;QAEF,cAAc,GAAG;YACf;gBACE,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,GAAG;gBACd,aAAa,EAAE,GAAG;gBAClB,kBAAkB,EAAE,GAAG;gBACvB,UAAU,EAAE,8CAA8C;gBAC1D,cAAc,EAAE,4DAA4D;gBAC5E,mBAAmB,EAAE,6DAA6D;gBAClF,YAAY,EAAE,MAAM;gBACpB,gBAAgB,EAAE,MAAM;gBACxB,qBAAqB,EAAE,MAAM;gBAC7B,mBAAmB,EAAE,GAAG;gBACxB,WAAW,EAAE,GAAG;gBAChB,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,sBAAsB;aACnC;SACF,CAAC;QAEF,oBAAoB,GAAG;YACrB;gBACE,MAAM,EAAE,QAAQ;gBAChB,OAAO,EAAE,aAAa;gBACtB,OAAO,EAAE,QAAQ;gBACjB,YAAY,EAAE;oBACZ,YAAY,EAAE,OAAO;oBACrB,UAAU,EAAE,QAAQ;oBACpB,cAAc,EAAE,IAAI;oBACpB,YAAY,EAAE,KAAK;oBACnB,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD,QAAQ,EAAE,OAAO;gBACjB,aAAa,EAAE,uBAAuB;gBACtC,GAAG,EAAE,mDAAmD;gBACxD,WAAW,EAAE;oBACX,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,OAAO;oBACb,OAAO,EAAE,UAAU;oBACnB,SAAS,EAAE,MAAM;oBACjB,IAAI,EAAE,6BAA6B;iBACpC;aACF;SACF,CAAC;QAEF,cAAc,GAAG;YACf;gBACE,GAAG,EAAE,gDAAgD;gBACrD,IAAI,EAAE,WAAW;gBACjB,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,yDAA0B,CAAC,QAAQ;gBACzC,IAAI,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;aACjC;YACD;gBACE,GAAG,EAAE,gDAAgD;gBACrD,IAAI,EAAE,0BAA0B;gBAChC,MAAM,EAAE,QAAQ;gBAChB,IAAI,EAAE,yDAA0B,CAAC,MAAM;gBACvC,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;aAC7B;SACF,CAAC;QAEF,gBAAgB,GAAG;YACjB,MAAM,EAAE,8DAA+B,CAAC,gBAAgB;YACxD,UAAU,EAAE,QAAQ;YACpB,aAAa,EAAE;gBACb;oBACE,IAAI,EAAE,wBAAwB;oBAC9B,IAAI,EAAE,uBAAuB;oBAC7B,MAAM,EAAE,WAAW;oBACnB,GAAG,EAAE,uCAAuC;oBAC5C,IAAI,EAAE,sBAAsB;oBAC5B,QAAQ,EAAE,kBAAkB;iBAC7B;aACF;YACD,QAAQ,EAAE;gBACR,gBAAgB,EAAE,sBAAsB;gBACxC,eAAe,EAAE,sBAAsB;gBACvC,KAAK,EAAE,YAAY;gBACnB,SAAS,EAAE,YAAY;aACxB;YACD,OAAO,EAAE;gBACP,YAAY,EAAE,SAAS;gBACvB,gBAAgB,EAAE,KAAK;gBACvB,kBAAkB,EAAE,MAAM;gBAC1B,eAAe,EAAE,MAAM;gBACvB,KAAK,EAAE,WAAW;aACnB;YACD,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAC;QAEF,eAAe,GAAG;YAChB,MAAM,EAAE,6DAA8B,CAAC,SAAS;YAChD,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,uCAAuC;oBACpD,OAAO,EAAE,QAAQ;oBACjB,GAAG,EAAE,wDAAwD;oBAC7D,IAAI,EAAE,sBAAsB;oBAC5B,aAAa,EAAE,UAAU;iBAC1B;aACF;YACD,WAAW,EAAE;gBACX;oBACE,WAAW,EAAE,2BAA2B;oBACxC,KAAK,EAAE,CAAC,iBAAiB,EAAE,wBAAwB,EAAE,iBAAiB,CAAC;oBACvE,aAAa,EAAE,MAAM;oBACrB,gBAAgB,EAAE,QAAQ;oBAC1B,UAAU,EAAE,KAAK;iBAClB;aACF;YACD,WAAW,EAAE;gBACX;oBACE,IAAI,EAAE,YAAY;oBAClB,WAAW,EAAE,oCAAoC;oBACjD,QAAQ,EAAE,0CAA0C;oBACpD,aAAa,EAAE,QAAQ;oBACvB,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;iBACzB;aACF;YACD,QAAQ,EAAE;gBACR,kBAAkB,EAAE,sBAAsB;gBAC1C,qBAAqB,EAAE,sBAAsB;gBAC7C,YAAY,EAAE,sBAAsB;gBACpC,eAAe,EAAE,sBAAsB;aACxC;YACD,MAAM,EAAE;gBACN,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,KAAK;gBACjB,SAAS,EAAE,CAAC,sBAAsB,CAAC;gBACnC,YAAY,EAAE,CAAC,oBAAoB,CAAC;gBACpC,eAAe,EAAE,KAAK;aACvB;SACF,CAAC;QAEF,cAAc,GAAG;YACf,SAAS,EAAE,EAAE;YACb,MAAM,EAAE;gBACN,eAAe,EAAE,UAAU;gBAC3B,SAAS,EAAE,UAAU;gBACrB,YAAY,EAAE,SAAS;gBACvB,QAAQ,EAAE,MAAM;gBAChB,SAAS,EAAE,QAAQ;gBACnB,YAAY,EAAE,MAAM;aACrB;YACD,UAAU,EAAE;gBACV,YAAY,EAAE,QAAQ;gBACtB,mBAAmB,EAAE,MAAM;gBAC3B,kBAAkB,EAAE,MAAM;gBAC1B,oBAAoB,EAAE,QAAQ;aAC/B;YACD,QAAQ,EAAE,oDAAqB,CAAC,IAAI;YACpC,UAAU,EAAE,sBAAsB;YAClC,UAAU,EAAE,eAAe;YAC3B,WAAW,EAAE,8BAA8B;YAC3C,KAAK,EAAE,6CAA6C;SACrD,CAAC;QAEF,oBAAoB,GAAG;YACrB,UAAU,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC;YACtC,YAAY,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;YACpD,iBAAiB,EAAE,MAAM;YACzB,qBAAqB,EAAE,IAAI;YAC3B,QAAQ,EAAE,sBAAsB;YAChC,SAAS,EAAE,CAAC,OAAO,EAAE,oBAAoB,CAAC;SAC3C,CAAC;QAEF,kBAAkB,GAAG;YACnB,iBAAiB,EAAE,CAAC,cAAc,EAAE,iBAAiB,CAAC;YACtD,iBAAiB,EAAE,UAAU;YAC7B,aAAa,EAAE,QAAQ;YACvB,cAAc,EAAE,MAAM;YACtB,iBAAiB,EAAE,QAAQ;YAC3B,eAAe,EAAE,KAAK;SACvB,CAAC;QAEF,oBAAoB,GAAG;YACrB,SAAS,EAAE,oCAAoC;YAC/C,aAAa,EAAE,CAAC,2BAA2B,CAAC;YAC5C,aAAa,EAAE,CAAC,8BAA8B,CAAC;YAC/C,eAAe,EAAE,CAAC,uBAAuB,EAAE,iBAAiB,CAAC;YAC7D,kBAAkB,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC;YACrD,YAAY,EAAE;gBACZ;oBACE,QAAQ,EAAE,GAAG;oBACb,IAAI,EAAE,+CAA+C;oBACrD,IAAI,EAAE,YAAY;oBAClB,WAAW,EAAE,yBAAyB;iBACvC;aACF;SACF,CAAC;QAEF,iBAAiB,GAAG;YAClB,OAAO,EAAE,CAAC,2DAA4B,CAAC,eAAe,EAAE,2DAA4B,CAAC,kBAAkB,CAAC;YACxG,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,QAAQ;gBACjB,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,sBAAsB;gBAChC,aAAa,EAAE,EAAE,SAAS,EAAE,cAAc,EAAE;aAC7C;YACD,KAAK,EAAE;gBACL;oBACE,EAAE,EAAE,cAAc;oBAClB,IAAI,EAAE,oCAAoC;oBAC1C,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,wCAAwC;oBACjD,UAAU,EAAE,uCAAe,CAAC,IAAI;iBACjC;aACF;YACD,iBAAiB,EAAE,KAAK;YACxB,UAAU,EAAE,uCAAe,CAAC,IAAI;SACjC,CAAC;QAEF,iBAAiB,GAAG;YAClB,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;YACrC,YAAY,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC;YACxC,SAAS,EAAE,CAAC,qBAAqB,CAAC;YAClC,YAAY,EAAE;gBACZ;oBACE,MAAM,EAAE,mBAAmB;oBAC3B,IAAI,EAAE,cAAc;oBACpB,OAAO,EAAE,0CAA0C;oBACnD,UAAU,EAAE,uCAAe,CAAC,MAAM;oBAClC,IAAI,EAAE,sBAAsB;iBAC7B;aACF;SACF,CAAC;QAEF,gBAAgB,GAAG;YACjB;gBACE,OAAO,EAAE,WAAW;gBACpB,SAAS,EAAE,eAAe;gBAC1B,SAAS,EAAE,QAAQ;gBACnB,WAAW,EAAE,MAAM;gBACnB,QAAQ,EAAE,UAAU;gBACpB,WAAW,EAAE,YAAY;gBACzB,eAAe,EAAE;oBACf,UAAU,EAAE,cAAc;oBAC1B,YAAY,EAAE,sBAAsB;oBACpC,YAAY,EAAE,sBAAsB;oBACpC,MAAM,EAAE,MAAM;oBACd,KAAK,EAAE,6BAA6B;iBACrC;aACF;SACF,CAAC;QAEF,eAAe,GAAG;YAChB,GAAG,EAAE,OAAO;YACZ,YAAY,EAAE,CAAC,mBAAmB,CAAC;YACnC,gBAAgB,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;YAChD,YAAY,EAAE,kBAAkB;YAChC,SAAS,EAAE,sBAAsB;YACjC,OAAO,EAAE,cAAc;SACxB,CAAC;QAEF,kBAAkB,GAAG;YACnB,YAAY,EAAE,EAAE;YAChB,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,EAAE;YACb,YAAY,EAAE,EAAE;YAChB,UAAU,EAAE,sBAAsB;YAClC,MAAM,EAAE,CAAC,+BAA+B,CAAC;SAC1C,CAAC;QAEF,qBAAqB,GAAG;YACtB,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,UAAU;YACtB,KAAK,EAAE,eAAe;YACtB,cAAc,EAAE,kBAAkB;YAClC,QAAQ,EAAE,YAAY;YACtB,kBAAkB,EAAE,UAAU;YAC9B,iBAAiB,EAAE,6DAA8B,CAAC,CAAC;YACnD,KAAK,EAAE,oCAAoC;YAC3C,WAAW,EAAE,oFAAoF;YACjG,QAAQ,EAAE,mDAAqB,CAAC,IAAI;YACpC,QAAQ,EAAE,oDAAqB,CAAC,eAAe;YAC/C,IAAI,EAAE,gDAAiB,CAAC,kBAAkB;YAC1C,UAAU,EAAE,uCAAe,CAAC,IAAI;YAChC,MAAM,EAAE,kDAAmB,CAAC,SAAS;YACrC,WAAW,EAAE,sBAAsB;YACnC,UAAU,EAAE,sBAAsB;YAClC,YAAY,EAAE,sBAAsB;YACpC,WAAW,EAAE,sBAAsB;YACnC,UAAU,EAAE,cAAc;YAC1B,gBAAgB,EAAE,oBAAoB;YACtC,UAAU,EAAE,cAAc;YAC1B,YAAY,EAAE,gBAAgB;YAC9B,WAAW,EAAE,eAAe;YAC5B,UAAU,EAAE,cAAc;YAC1B,gBAAgB,EAAE,oBAAoB;YACtC,cAAc,EAAE,kBAAkB;YAClC,gBAAgB,EAAE,oBAAoB;YACtC,aAAa,EAAE,iBAAiB;YAChC,aAAa,EAAE,iBAAiB;YAChC,YAAY,EAAE,gBAAgB;YAC9B,WAAW,EAAE,eAAe;YAC5B,UAAU,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE;YACnC,IAAI,EAAE,CAAC,QAAQ,EAAE,iBAAiB,EAAE,KAAK,CAAC;YAC1C,cAAc,EAAE,kBAAkB;SACnC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACpD,MAAM,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACrD,MAAM,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/D,MAAM,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9D,MAAM,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YACxD,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACrD,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACrD,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YACjD,MAAM,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YACnD,MAAM,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YACxD,MAAM,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,MAAM,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7D,MAAM,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,MAAM,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,MAAM,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,qBAAqB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACzD,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACzD,MAAM,MAAM,GAAsB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,KAAK,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,6DAA8B,CAAC,CAAC;YACxE,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,oDAAqB,CAAC,CAAC;YACxD,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,oDAAqB,CAAC,eAAe,CAAC,CAAC;YACpE,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,oDAAqB,CAAC,aAAa,CAAC,CAAC;YAClE,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,oDAAqB,CAAC,oBAAoB,CAAC,CAAC;YACzE,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,oDAAqB,CAAC,qBAAqB,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,IAAI,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,IAAI,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,YAAa,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzD,MAAM,CAAC,OAAO,CAAC,YAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,YAAa,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,WAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,WAAY,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,SAAS,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,yDAA0B,CAAC,QAAQ,CAAC,CAAC;YACjE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,8DAA+B,CAAC,gBAAgB,CAAC,CAAC;YACvF,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,CAAC,gBAAgB,CAAC,aAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,gBAAgB,CAAC,QAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,6DAA8B,CAAC,SAAS,CAAC,CAAC;YAC9E,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,GAAG,GAAG,eAAe,CAAC,KAAM,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/B,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,CAAC,eAAe,CAAC,QAAS,CAAC,kBAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;YACnE,MAAM,CAAC,eAAe,CAAC,QAAS,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/D,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9D,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClE,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAC7D,MAAM,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5D,MAAM,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvE,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC9D,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAClF,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,WAAW,GAAG,oBAAoB,CAAC,YAAa,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2DAA4B,CAAC,eAAe,CAAC,CAAC;YAC1F,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,IAAI,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YACpD,MAAM,CAAC,iBAAiB,CAAC,WAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,CAAC,iBAAiB,CAAC,WAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,KAAK,GAAG,iBAAiB,CAAC,YAAa,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,MAAM,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,KAAK,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,KAAK,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,eAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjD,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7C,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjD,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,aAAa,GAA+B;gBAChD,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC;oBAClC,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,EAAE;oBACV,QAAQ,EAAE,EAAE;oBACZ,aAAa,EAAE,qBAAqB;oBACpC,iBAAiB,EAAE,kBAAkB;iBACtC,CAAC;gBACF,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;gBAChD,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,qBAAqB,CAAC;gBAC1D,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;aAC5C,CAAC;YAEF,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,cAAc,GAAgC;gBAClD,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;gBACvC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,qBAAqB,CAAC;gBAC1D,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;gBAC1C,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,qBAAqB,CAAC;gBAC7D,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxD,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,qBAAqB,CAAC;gBAC5D,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC;aAC3D,CAAC;YAEF,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YACjD,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,eAAe,GAAiC;gBACpD,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,qBAAqB,CAAC;gBAC3D,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;gBAClD,yBAAyB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBACpE,yBAAyB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACrE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,qBAAqB,CAAC;gBAC1D,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,qBAAqB,CAAC,CAAC;aAC9D,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1D,MAAM,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC,WAAW,EAAE,CAAC;YAChE,MAAM,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC,WAAW,EAAE,CAAC;YAChE,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,QAAQ,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAC7C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACzB,MAAM,aAAa,GAAG,EAAE,GAAG,qBAAqB,EAAE,OAAO,EAAE,CAAC;gBAC5D,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,UAAU,GAAG;gBACjB,GAAG,qBAAqB;gBACxB,OAAO,EAAE,OAAO;aACjB,CAAC;YAEF,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,aAAa,GAAG;gBACpB,GAAG,qBAAqB;gBACxB,UAAU,EAAE;oBACV,GAAG,qBAAqB,CAAC,UAAU;oBACnC,UAAU,EAAE,KAAK;oBACjB,aAAa,EAAE,KAAK;oBACpB,eAAe,EAAE,OAAO;iBACzB;aACF,CAAC;YAEF,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC9D,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,eAAe,GAAG;gBACtB,GAAG,qBAAqB;gBACxB,cAAc,EAAE;oBACd,GAAG,qBAAqB,CAAC,cAAe;oBACxC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE;oBACxD,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE;iBACjE;aACF,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,cAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAClE,MAAM,CAAC,eAAe,CAAC,cAAe,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,YAAY,GAAG;gBACnB,GAAG,qBAAqB;gBACxB,UAAU,EAAE;oBACV,GAAG,qBAAqB,CAAC,UAAU;oBACnC,kBAAkB,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC;oBAC9C,oBAAoB,EAAE,sBAAsB;oBAC5C,kBAAkB,EAAE,KAAK;iBAC1B;aACF,CAAC;YAEF,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;YACrE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,UAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\data\\__tests__\\vulnerability-data.interface.spec.ts"], "sourcesContent": ["import {\r\n  VulnerabilityData,\r\n  VulnerabilityDataValidator,\r\n  VulnerabilityDataSerializer,\r\n  VulnerabilityDataTransformer,\r\n  VulnerabilityAlternativeId,\r\n  VulnerabilitySourceReliability,\r\n  VulnerabilityCategory,\r\n  VulnerabilityType,\r\n  VulnerabilityStatus,\r\n  VulnerabilityCVSSData,\r\n  VulnerabilityAffectedProduct,\r\n  VulnerabilityReference,\r\n  VulnerabilityReferenceType,\r\n  VulnerabilityExploitationData,\r\n  VulnerabilityExploitationStatus,\r\n  VulnerabilityRemediationData,\r\n  VulnerabilityRemediationStatus,\r\n  VulnerabilityAssessmentData,\r\n  VulnerabilityPriority,\r\n  VulnerabilityComplianceImpact,\r\n  VulnerabilityBusinessImpact,\r\n  VulnerabilityTechnicalDetails,\r\n  VulnerabilityDetectionInfo,\r\n  VulnerabilityDetectionMethod,\r\n  VulnerabilityThreatContext,\r\n  VulnerabilityAssetContext,\r\n  VulnerabilitySharingInfo,\r\n  VulnerabilityDataQualityMetrics,\r\n} from '../vulnerability-data.interface';\r\nimport { VulnerabilitySeverity } from '../../../enums/vulnerability-severity.enum';\r\nimport { ConfidenceLevel } from '../../../enums/confidence-level.enum';\r\n\r\ndescribe('VulnerabilityData Interface', () => {\r\n  let mockVulnerabilityData: VulnerabilityData;\r\n  let mockAlternativeIds: VulnerabilityAlternativeId[];\r\n  let mockCVSSScores: VulnerabilityCVSSData[];\r\n  let mockAffectedProducts: VulnerabilityAffectedProduct[];\r\n  let mockReferences: VulnerabilityReference[];\r\n  let mockExploitation: VulnerabilityExploitationData;\r\n  let mockRemediation: VulnerabilityRemediationData;\r\n  let mockAssessment: VulnerabilityAssessmentData;\r\n  let mockComplianceImpact: VulnerabilityComplianceImpact;\r\n  let mockBusinessImpact: VulnerabilityBusinessImpact;\r\n  let mockTechnicalDetails: VulnerabilityTechnicalDetails;\r\n  let mockDetectionInfo: VulnerabilityDetectionInfo;\r\n  let mockThreatContext: VulnerabilityThreatContext;\r\n  let mockAssetContext: VulnerabilityAssetContext[];\r\n  let mockSharingInfo: VulnerabilitySharingInfo;\r\n  let mockQualityMetrics: VulnerabilityDataQualityMetrics;\r\n\r\n  beforeEach(() => {\r\n    mockAlternativeIds = [\r\n      {\r\n        type: 'vendor',\r\n        value: 'VENDOR-2024-001',\r\n        source: 'Vendor Security Team',\r\n      },\r\n      {\r\n        type: 'internal',\r\n        value: 'INT-VULN-123',\r\n        source: 'Internal Security',\r\n      },\r\n    ];\r\n\r\n    mockCVSSScores = [\r\n      {\r\n        version: '3.1',\r\n        baseScore: 8.5,\r\n        temporalScore: 7.8,\r\n        environmentalScore: 8.2,\r\n        baseVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',\r\n        temporalVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H/E:F/RL:O/RC:C',\r\n        environmentalVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H/CR:H/IR:H/AR:H',\r\n        baseSeverity: 'HIGH',\r\n        temporalSeverity: 'HIGH',\r\n        environmentalSeverity: 'HIGH',\r\n        exploitabilityScore: 3.9,\r\n        impactScore: 5.9,\r\n        source: 'NVD',\r\n        assessedAt: '2024-01-01T00:00:00Z',\r\n      },\r\n    ];\r\n\r\n    mockAffectedProducts = [\r\n      {\r\n        vendor: 'Apache',\r\n        product: 'HTTP Server',\r\n        version: '2.4.41',\r\n        versionRange: {\r\n          startVersion: '2.4.0',\r\n          endVersion: '2.4.42',\r\n          startInclusive: true,\r\n          endInclusive: false,\r\n          criteria: '>=2.4.0 <2.4.42',\r\n        },\r\n        platform: 'Linux',\r\n        configuration: 'Default configuration',\r\n        cpe: 'cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:*',\r\n        packageInfo: {\r\n          type: 'rpm',\r\n          name: 'httpd',\r\n          version: '2.4.41-1',\r\n          ecosystem: 'RHEL',\r\n          purl: 'pkg:rpm/rhel/httpd@2.4.41-1',\r\n        },\r\n      },\r\n    ];\r\n\r\n    mockReferences = [\r\n      {\r\n        url: 'https://nvd.nist.gov/vuln/detail/CVE-2024-0001',\r\n        name: 'NVD Entry',\r\n        source: 'NVD',\r\n        type: VulnerabilityReferenceType.ADVISORY,\r\n        tags: ['official', 'government'],\r\n      },\r\n      {\r\n        url: 'https://apache.org/security/CVE-2024-0001.html',\r\n        name: 'Apache Security Advisory',\r\n        source: 'Apache',\r\n        type: VulnerabilityReferenceType.VENDOR,\r\n        tags: ['vendor', 'official'],\r\n      },\r\n    ];\r\n\r\n    mockExploitation = {\r\n      status: VulnerabilityExploitationStatus.PROOF_OF_CONCEPT,\r\n      complexity: 'medium',\r\n      knownExploits: [\r\n        {\r\n          name: 'Apache HTTP Server RCE',\r\n          type: 'remote_code_execution',\r\n          source: 'ExploitDB',\r\n          url: 'https://exploit-db.com/exploits/12345',\r\n          date: '2024-01-15T00:00:00Z',\r\n          maturity: 'proof_of_concept',\r\n        },\r\n      ],\r\n      timeline: {\r\n        firstExploitSeen: '2024-01-15T00:00:00Z',\r\n        lastExploitSeen: '2024-01-20T00:00:00Z',\r\n        trend: 'increasing',\r\n        frequency: 'occasional',\r\n      },\r\n      context: {\r\n        attackVector: 'network',\r\n        attackComplexity: 'low',\r\n        privilegesRequired: 'none',\r\n        userInteraction: 'none',\r\n        scope: 'unchanged',\r\n      },\r\n      pocAvailable: true,\r\n      weaponized: false,\r\n      inTheWild: false,\r\n    };\r\n\r\n    mockRemediation = {\r\n      status: VulnerabilityRemediationStatus.AVAILABLE,\r\n      fixes: [\r\n        {\r\n          type: 'patch',\r\n          description: 'Security patch for Apache HTTP Server',\r\n          version: '2.4.42',\r\n          url: 'https://apache.org/dist/httpd/patches/apply_to_2.4.41/',\r\n          date: '2024-01-10T00:00:00Z',\r\n          effectiveness: 'complete',\r\n        },\r\n      ],\r\n      workarounds: [\r\n        {\r\n          description: 'Disable vulnerable module',\r\n          steps: ['Edit httpd.conf', 'Comment out LoadModule', 'Restart service'],\r\n          effectiveness: 'high',\r\n          functionalImpact: 'medium',\r\n          complexity: 'low',\r\n        },\r\n      ],\r\n      mitigations: [\r\n        {\r\n          type: 'preventive',\r\n          description: 'Configure web application firewall',\r\n          guidance: 'Block requests matching exploit patterns',\r\n          effectiveness: 'medium',\r\n          controls: ['WAF', 'IPS'],\r\n        },\r\n      ],\r\n      timeline: {\r\n        vendorAcknowledged: '2024-01-05T00:00:00Z',\r\n        fixDevelopmentStarted: '2024-01-06T00:00:00Z',\r\n        fixAvailable: '2024-01-10T00:00:00Z',\r\n        expectedFixDate: '2024-01-10T00:00:00Z',\r\n      },\r\n      effort: {\r\n        timeToFix: 2,\r\n        complexity: 'low',\r\n        resources: ['System Administrator'],\r\n        dependencies: ['Maintenance window'],\r\n        remediationRisk: 'low',\r\n      },\r\n    };\r\n\r\n    mockAssessment = {\r\n      riskScore: 85,\r\n      impact: {\r\n        confidentiality: 'complete',\r\n        integrity: 'complete',\r\n        availability: 'partial',\r\n        business: 'high',\r\n        financial: 'medium',\r\n        reputational: 'high',\r\n      },\r\n      likelihood: {\r\n        exploitation: 'medium',\r\n        vectorAccessibility: 'high',\r\n        attackerMotivation: 'high',\r\n        targetAttractiveness: 'medium',\r\n      },\r\n      priority: VulnerabilityPriority.HIGH,\r\n      assessedAt: '2024-01-01T00:00:00Z',\r\n      assessedBy: 'Security Team',\r\n      methodology: 'CVSS v3.1 + Business Context',\r\n      notes: 'High priority due to public-facing exposure',\r\n    };\r\n\r\n    mockComplianceImpact = {\r\n      frameworks: ['PCI-DSS', 'SOX', 'GDPR'],\r\n      requirements: ['Data Protection', 'System Security'],\r\n      violationSeverity: 'high',\r\n      notificationsRequired: true,\r\n      deadline: '2024-01-30T00:00:00Z',\r\n      penalties: ['Fines', 'Audit Requirements'],\r\n    };\r\n\r\n    mockBusinessImpact = {\r\n      affectedProcesses: ['Web Services', 'Customer Portal'],\r\n      serviceDisruption: 'moderate',\r\n      revenueImpact: 'medium',\r\n      customerImpact: 'high',\r\n      operationalImpact: 'medium',\r\n      strategicImpact: 'low',\r\n    };\r\n\r\n    mockTechnicalDetails = {\r\n      rootCause: 'Buffer overflow in request parsing',\r\n      attackVectors: ['HTTP Request Manipulation'],\r\n      prerequisites: ['Network access to web server'],\r\n      technicalImpact: ['Remote Code Execution', 'Data Disclosure'],\r\n      affectedComponents: ['mod_rewrite', 'request_parser'],\r\n      codeExamples: [\r\n        {\r\n          language: 'c',\r\n          code: 'char buffer[256]; strcpy(buffer, user_input);',\r\n          type: 'vulnerable',\r\n          description: 'Vulnerable code pattern',\r\n        },\r\n      ],\r\n    };\r\n\r\n    mockDetectionInfo = {\r\n      methods: [VulnerabilityDetectionMethod.STATIC_ANALYSIS, VulnerabilityDetectionMethod.AUTOMATED_SCANNING],\r\n      scannerInfo: {\r\n        name: 'Nessus',\r\n        version: '10.4.0',\r\n        vendor: 'Tenable',\r\n        scanDate: '2024-01-01T00:00:00Z',\r\n        configuration: { scan_type: 'credentialed' },\r\n      },\r\n      rules: [\r\n        {\r\n          id: 'NESSUS-12345',\r\n          name: 'Apache HTTP Server Buffer Overflow',\r\n          type: 'signature',\r\n          content: 'HTTP/1.1 request parsing vulnerability',\r\n          confidence: ConfidenceLevel.HIGH,\r\n        },\r\n      ],\r\n      falsePositiveRate: 'low',\r\n      confidence: ConfidenceLevel.HIGH,\r\n    };\r\n\r\n    mockThreatContext = {\r\n      threats: ['threat-123', 'threat-456'],\r\n      threatActors: ['APT29', 'Lazarus Group'],\r\n      campaigns: ['Operation WebStrike'],\r\n      intelligence: [\r\n        {\r\n          source: 'Threat Intel Feed',\r\n          type: 'exploitation',\r\n          content: 'Active exploitation observed in the wild',\r\n          confidence: ConfidenceLevel.MEDIUM,\r\n          date: '2024-01-15T00:00:00Z',\r\n        },\r\n      ],\r\n    };\r\n\r\n    mockAssetContext = [\r\n      {\r\n        assetId: 'asset-123',\r\n        assetName: 'Web Server 01',\r\n        assetType: 'server',\r\n        criticality: 'high',\r\n        exposure: 'external',\r\n        environment: 'production',\r\n        instanceDetails: {\r\n          instanceId: 'instance-456',\r\n          discoveredAt: '2024-01-01T00:00:00Z',\r\n          lastVerified: '2024-01-02T00:00:00Z',\r\n          status: 'open',\r\n          notes: 'Requires immediate patching',\r\n        },\r\n      },\r\n    ];\r\n\r\n    mockSharingInfo = {\r\n      tlp: 'amber',\r\n      restrictions: ['Internal use only'],\r\n      permittedActions: ['analyze', 'detect', 'block'],\r\n      agreementRef: 'vuln-sharing-001',\r\n      copyright: '© 2024 Security Corp',\r\n      license: 'CC BY-SA 4.0',\r\n    };\r\n\r\n    mockQualityMetrics = {\r\n      completeness: 90,\r\n      accuracy: 95,\r\n      timeliness: 85,\r\n      relevance: 88,\r\n      overallScore: 89,\r\n      assessedAt: '2024-01-01T00:00:00Z',\r\n      issues: ['Missing exploitation timeline'],\r\n    };\r\n\r\n    mockVulnerabilityData = {\r\n      version: '1.0.0',\r\n      externalId: 'vuln-123',\r\n      cveId: 'CVE-2024-0001',\r\n      alternativeIds: mockAlternativeIds,\r\n      sourceId: 'nvd-source',\r\n      sourceOrganization: 'NIST NVD',\r\n      sourceReliability: VulnerabilitySourceReliability.A,\r\n      title: 'Apache HTTP Server Buffer Overflow',\r\n      description: 'A buffer overflow vulnerability in Apache HTTP Server allows remote code execution',\r\n      severity: VulnerabilitySeverity.HIGH,\r\n      category: VulnerabilityCategory.BUFFER_OVERFLOW,\r\n      type: VulnerabilityType.IMPLEMENTATION_BUG,\r\n      confidence: ConfidenceLevel.HIGH,\r\n      status: VulnerabilityStatus.PUBLISHED,\r\n      publishedAt: '2024-01-01T00:00:00Z',\r\n      modifiedAt: '2024-01-02T00:00:00Z',\r\n      discoveredAt: '2023-12-15T00:00:00Z',\r\n      disclosedAt: '2024-01-01T00:00:00Z',\r\n      cvssScores: mockCVSSScores,\r\n      affectedProducts: mockAffectedProducts,\r\n      references: mockReferences,\r\n      exploitation: mockExploitation,\r\n      remediation: mockRemediation,\r\n      assessment: mockAssessment,\r\n      complianceImpact: mockComplianceImpact,\r\n      businessImpact: mockBusinessImpact,\r\n      technicalDetails: mockTechnicalDetails,\r\n      detectionInfo: mockDetectionInfo,\r\n      threatContext: mockThreatContext,\r\n      assetContext: mockAssetContext,\r\n      sharingInfo: mockSharingInfo,\r\n      attributes: { custom: 'attribute' },\r\n      tags: ['apache', 'buffer-overflow', 'rce'],\r\n      qualityMetrics: mockQualityMetrics,\r\n    };\r\n  });\r\n\r\n  describe('VulnerabilityData Structure', () => {\r\n    it('should have all required fields', () => {\r\n      expect(mockVulnerabilityData.version).toBeDefined();\r\n      expect(mockVulnerabilityData.externalId).toBeDefined();\r\n      expect(mockVulnerabilityData.sourceId).toBeDefined();\r\n      expect(mockVulnerabilityData.sourceOrganization).toBeDefined();\r\n      expect(mockVulnerabilityData.sourceReliability).toBeDefined();\r\n      expect(mockVulnerabilityData.title).toBeDefined();\r\n      expect(mockVulnerabilityData.description).toBeDefined();\r\n      expect(mockVulnerabilityData.severity).toBeDefined();\r\n      expect(mockVulnerabilityData.category).toBeDefined();\r\n      expect(mockVulnerabilityData.type).toBeDefined();\r\n      expect(mockVulnerabilityData.confidence).toBeDefined();\r\n      expect(mockVulnerabilityData.status).toBeDefined();\r\n      expect(mockVulnerabilityData.publishedAt).toBeDefined();\r\n      expect(mockVulnerabilityData.modifiedAt).toBeDefined();\r\n      expect(mockVulnerabilityData.cvssScores).toBeDefined();\r\n      expect(mockVulnerabilityData.affectedProducts).toBeDefined();\r\n      expect(mockVulnerabilityData.references).toBeDefined();\r\n      expect(mockVulnerabilityData.assessment).toBeDefined();\r\n      expect(mockVulnerabilityData.sharingInfo).toBeDefined();\r\n    });\r\n\r\n    it('should support versioning', () => {\r\n      expect(mockVulnerabilityData.version).toBe('1.0.0');\r\n      expect(typeof mockVulnerabilityData.version).toBe('string');\r\n    });\r\n\r\n    it('should be serializable to JSON', () => {\r\n      const jsonString = JSON.stringify(mockVulnerabilityData);\r\n      expect(jsonString).toBeDefined();\r\n      expect(jsonString.length).toBeGreaterThan(0);\r\n\r\n      const parsed = JSON.parse(jsonString);\r\n      expect(parsed.version).toBe(mockVulnerabilityData.version);\r\n      expect(parsed.externalId).toBe(mockVulnerabilityData.externalId);\r\n    });\r\n\r\n    it('should maintain data integrity after serialization', () => {\r\n      const jsonString = JSON.stringify(mockVulnerabilityData);\r\n      const parsed: VulnerabilityData = JSON.parse(jsonString);\r\n\r\n      expect(parsed.version).toBe(mockVulnerabilityData.version);\r\n      expect(parsed.externalId).toBe(mockVulnerabilityData.externalId);\r\n      expect(parsed.cveId).toBe(mockVulnerabilityData.cveId);\r\n      expect(parsed.title).toBe(mockVulnerabilityData.title);\r\n      expect(parsed.severity).toBe(mockVulnerabilityData.severity);\r\n      expect(parsed.cvssScores.length).toBe(mockVulnerabilityData.cvssScores.length);\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityAlternativeId', () => {\r\n    it('should contain alternative identifiers', () => {\r\n      const altId = mockAlternativeIds[0];\r\n      expect(altId.type).toBe('vendor');\r\n      expect(altId.value).toBe('VENDOR-2024-001');\r\n      expect(altId.source).toBe('Vendor Security Team');\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilitySourceReliability Enum', () => {\r\n    it('should contain all reliability levels', () => {\r\n      const reliabilityLevels = Object.values(VulnerabilitySourceReliability);\r\n      expect(reliabilityLevels).toContain('A');\r\n      expect(reliabilityLevels).toContain('B');\r\n      expect(reliabilityLevels).toContain('C');\r\n      expect(reliabilityLevels).toContain('D');\r\n      expect(reliabilityLevels).toContain('E');\r\n      expect(reliabilityLevels).toContain('F');\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityCategory Enum', () => {\r\n    it('should contain comprehensive vulnerability categories', () => {\r\n      const categories = Object.values(VulnerabilityCategory);\r\n      expect(categories).toContain(VulnerabilityCategory.BUFFER_OVERFLOW);\r\n      expect(categories).toContain(VulnerabilityCategory.SQL_INJECTION);\r\n      expect(categories).toContain(VulnerabilityCategory.CROSS_SITE_SCRIPTING);\r\n      expect(categories).toContain(VulnerabilityCategory.REMOTE_CODE_EXECUTION);\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityCVSSData', () => {\r\n    it('should contain CVSS scoring information', () => {\r\n      const cvss = mockCVSSScores[0];\r\n      expect(cvss.version).toBe('3.1');\r\n      expect(cvss.baseScore).toBe(8.5);\r\n      expect(cvss.baseVector).toContain('CVSS:3.1');\r\n      expect(cvss.baseSeverity).toBe('HIGH');\r\n      expect(cvss.source).toBe('NVD');\r\n    });\r\n\r\n    it('should support temporal and environmental scores', () => {\r\n      const cvss = mockCVSSScores[0];\r\n      expect(cvss.temporalScore).toBe(7.8);\r\n      expect(cvss.environmentalScore).toBe(8.2);\r\n      expect(cvss.temporalVector).toBeDefined();\r\n      expect(cvss.environmentalVector).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityAffectedProduct', () => {\r\n    it('should contain product information', () => {\r\n      const product = mockAffectedProducts[0];\r\n      expect(product.vendor).toBe('Apache');\r\n      expect(product.product).toBe('HTTP Server');\r\n      expect(product.version).toBe('2.4.41');\r\n      expect(product.cpe).toContain('apache:http_server');\r\n    });\r\n\r\n    it('should support version ranges', () => {\r\n      const product = mockAffectedProducts[0];\r\n      expect(product.versionRange).toBeDefined();\r\n      expect(product.versionRange!.startVersion).toBe('2.4.0');\r\n      expect(product.versionRange!.endVersion).toBe('2.4.42');\r\n      expect(product.versionRange!.startInclusive).toBe(true);\r\n    });\r\n\r\n    it('should support package information', () => {\r\n      const product = mockAffectedProducts[0];\r\n      expect(product.packageInfo).toBeDefined();\r\n      expect(product.packageInfo!.type).toBe('rpm');\r\n      expect(product.packageInfo!.purl).toContain('pkg:rpm');\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityReference', () => {\r\n    it('should contain reference information', () => {\r\n      const reference = mockReferences[0];\r\n      expect(reference.url).toContain('nvd.nist.gov');\r\n      expect(reference.type).toBe(VulnerabilityReferenceType.ADVISORY);\r\n      expect(Array.isArray(reference.tags)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityExploitationData', () => {\r\n    it('should contain exploitation information', () => {\r\n      expect(mockExploitation.status).toBe(VulnerabilityExploitationStatus.PROOF_OF_CONCEPT);\r\n      expect(mockExploitation.complexity).toBe('medium');\r\n      expect(mockExploitation.pocAvailable).toBe(true);\r\n      expect(mockExploitation.inTheWild).toBe(false);\r\n    });\r\n\r\n    it('should support known exploits', () => {\r\n      expect(Array.isArray(mockExploitation.knownExploits)).toBe(true);\r\n      expect(mockExploitation.knownExploits![0].name).toBe('Apache HTTP Server RCE');\r\n    });\r\n\r\n    it('should support exploitation timeline', () => {\r\n      expect(mockExploitation.timeline).toBeDefined();\r\n      expect(mockExploitation.timeline!.trend).toBe('increasing');\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityRemediationData', () => {\r\n    it('should contain remediation information', () => {\r\n      expect(mockRemediation.status).toBe(VulnerabilityRemediationStatus.AVAILABLE);\r\n      expect(Array.isArray(mockRemediation.fixes)).toBe(true);\r\n      expect(Array.isArray(mockRemediation.workarounds)).toBe(true);\r\n    });\r\n\r\n    it('should support fix information', () => {\r\n      const fix = mockRemediation.fixes![0];\r\n      expect(fix.type).toBe('patch');\r\n      expect(fix.version).toBe('2.4.42');\r\n      expect(fix.effectiveness).toBe('complete');\r\n    });\r\n\r\n    it('should support remediation timeline', () => {\r\n      expect(mockRemediation.timeline).toBeDefined();\r\n      expect(mockRemediation.timeline!.vendorAcknowledged).toBeDefined();\r\n      expect(mockRemediation.timeline!.fixAvailable).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityAssessmentData', () => {\r\n    it('should contain risk assessment', () => {\r\n      expect(mockAssessment.riskScore).toBe(85);\r\n      expect(mockAssessment.priority).toBe(VulnerabilityPriority.HIGH);\r\n      expect(mockAssessment.assessedBy).toBe('Security Team');\r\n    });\r\n\r\n    it('should support impact assessment', () => {\r\n      expect(mockAssessment.impact.confidentiality).toBe('complete');\r\n      expect(mockAssessment.impact.business).toBe('high');\r\n    });\r\n\r\n    it('should support likelihood assessment', () => {\r\n      expect(mockAssessment.likelihood.exploitation).toBe('medium');\r\n      expect(mockAssessment.likelihood.vectorAccessibility).toBe('high');\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityComplianceImpact', () => {\r\n    it('should contain compliance information', () => {\r\n      expect(Array.isArray(mockComplianceImpact.frameworks)).toBe(true);\r\n      expect(mockComplianceImpact.frameworks).toContain('PCI-DSS');\r\n      expect(mockComplianceImpact.violationSeverity).toBe('high');\r\n      expect(mockComplianceImpact.notificationsRequired).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityBusinessImpact', () => {\r\n    it('should contain business impact assessment', () => {\r\n      expect(Array.isArray(mockBusinessImpact.affectedProcesses)).toBe(true);\r\n      expect(mockBusinessImpact.serviceDisruption).toBe('moderate');\r\n      expect(mockBusinessImpact.customerImpact).toBe('high');\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityTechnicalDetails', () => {\r\n    it('should contain technical information', () => {\r\n      expect(mockTechnicalDetails.rootCause).toBe('Buffer overflow in request parsing');\r\n      expect(Array.isArray(mockTechnicalDetails.attackVectors)).toBe(true);\r\n      expect(Array.isArray(mockTechnicalDetails.codeExamples)).toBe(true);\r\n    });\r\n\r\n    it('should support code examples', () => {\r\n      const codeExample = mockTechnicalDetails.codeExamples![0];\r\n      expect(codeExample.language).toBe('c');\r\n      expect(codeExample.type).toBe('vulnerable');\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityDetectionInfo', () => {\r\n    it('should contain detection information', () => {\r\n      expect(Array.isArray(mockDetectionInfo.methods)).toBe(true);\r\n      expect(mockDetectionInfo.methods).toContain(VulnerabilityDetectionMethod.STATIC_ANALYSIS);\r\n      expect(mockDetectionInfo.confidence).toBe(ConfidenceLevel.HIGH);\r\n    });\r\n\r\n    it('should support scanner information', () => {\r\n      expect(mockDetectionInfo.scannerInfo).toBeDefined();\r\n      expect(mockDetectionInfo.scannerInfo!.name).toBe('Nessus');\r\n      expect(mockDetectionInfo.scannerInfo!.vendor).toBe('Tenable');\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityThreatContext', () => {\r\n    it('should contain threat intelligence', () => {\r\n      expect(Array.isArray(mockThreatContext.threats)).toBe(true);\r\n      expect(Array.isArray(mockThreatContext.threatActors)).toBe(true);\r\n      expect(mockThreatContext.threatActors).toContain('APT29');\r\n    });\r\n\r\n    it('should support threat intelligence', () => {\r\n      expect(Array.isArray(mockThreatContext.intelligence)).toBe(true);\r\n      const intel = mockThreatContext.intelligence![0];\r\n      expect(intel.confidence).toBe(ConfidenceLevel.MEDIUM);\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityAssetContext', () => {\r\n    it('should contain asset information', () => {\r\n      const asset = mockAssetContext[0];\r\n      expect(asset.assetId).toBe('asset-123');\r\n      expect(asset.criticality).toBe('high');\r\n      expect(asset.exposure).toBe('external');\r\n      expect(asset.environment).toBe('production');\r\n    });\r\n\r\n    it('should support instance details', () => {\r\n      const asset = mockAssetContext[0];\r\n      expect(asset.instanceDetails).toBeDefined();\r\n      expect(asset.instanceDetails!.status).toBe('open');\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilitySharingInfo', () => {\r\n    it('should contain sharing restrictions', () => {\r\n      expect(mockSharingInfo.tlp).toBe('amber');\r\n      expect(Array.isArray(mockSharingInfo.permittedActions)).toBe(true);\r\n      expect(mockSharingInfo.permittedActions).toContain('analyze');\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityDataQualityMetrics', () => {\r\n    it('should contain quality assessment', () => {\r\n      expect(mockQualityMetrics.completeness).toBe(90);\r\n      expect(mockQualityMetrics.accuracy).toBe(95);\r\n      expect(mockQualityMetrics.overallScore).toBe(89);\r\n      expect(mockQualityMetrics.assessedAt).toBeDefined();\r\n    });\r\n\r\n    it('should support quality issues tracking', () => {\r\n      expect(Array.isArray(mockQualityMetrics.issues)).toBe(true);\r\n      expect(mockQualityMetrics.issues).toContain('Missing exploitation timeline');\r\n    });\r\n  });\r\n\r\n  describe('Data Validation Interface', () => {\r\n    it('should define validation methods', () => {\r\n      const mockValidator: VulnerabilityDataValidator = {\r\n        validate: jest.fn().mockReturnValue({\r\n          isValid: true,\r\n          errors: [],\r\n          warnings: [],\r\n          sanitizedData: mockVulnerabilityData,\r\n          qualityAssessment: mockQualityMetrics,\r\n        }),\r\n        validateVersion: jest.fn().mockReturnValue(true),\r\n        sanitize: jest.fn().mockReturnValue(mockVulnerabilityData),\r\n        validateCVSS: jest.fn().mockReturnValue([]),\r\n      };\r\n\r\n      expect(mockValidator.validate).toBeDefined();\r\n      expect(mockValidator.validateVersion).toBeDefined();\r\n      expect(mockValidator.sanitize).toBeDefined();\r\n      expect(mockValidator.validateCVSS).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('Data Serialization Interface', () => {\r\n    it('should define serialization methods', () => {\r\n      const mockSerializer: VulnerabilityDataSerializer = {\r\n        toJson: jest.fn().mockReturnValue('{}'),\r\n        fromJson: jest.fn().mockReturnValue(mockVulnerabilityData),\r\n        toCveJson: jest.fn().mockReturnValue('{}'),\r\n        fromCveJson: jest.fn().mockReturnValue(mockVulnerabilityData),\r\n        toBinary: jest.fn().mockReturnValue(Buffer.from('test')),\r\n        fromBinary: jest.fn().mockReturnValue(mockVulnerabilityData),\r\n        getSupportedVersions: jest.fn().mockReturnValue(['1.0.0']),\r\n      };\r\n\r\n      expect(mockSerializer.toJson).toBeDefined();\r\n      expect(mockSerializer.fromJson).toBeDefined();\r\n      expect(mockSerializer.toCveJson).toBeDefined();\r\n      expect(mockSerializer.fromCveJson).toBeDefined();\r\n      expect(mockSerializer.toBinary).toBeDefined();\r\n      expect(mockSerializer.fromBinary).toBeDefined();\r\n      expect(mockSerializer.getSupportedVersions).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('Data Transformation Interface', () => {\r\n    it('should define transformation methods', () => {\r\n      const mockTransformer: VulnerabilityDataTransformer = {\r\n        transform: jest.fn().mockReturnValue(mockVulnerabilityData),\r\n        transformToExternal: jest.fn().mockReturnValue({}),\r\n        getSupportedSourceFormats: jest.fn().mockReturnValue(['nvd', 'cve']),\r\n        getSupportedTargetFormats: jest.fn().mockReturnValue(['json', 'xml']),\r\n        enrich: jest.fn().mockResolvedValue(mockVulnerabilityData),\r\n        normalize: jest.fn().mockReturnValue([mockVulnerabilityData]),\r\n      };\r\n\r\n      expect(mockTransformer.transform).toBeDefined();\r\n      expect(mockTransformer.transformToExternal).toBeDefined();\r\n      expect(mockTransformer.getSupportedSourceFormats).toBeDefined();\r\n      expect(mockTransformer.getSupportedTargetFormats).toBeDefined();\r\n      expect(mockTransformer.enrich).toBeDefined();\r\n      expect(mockTransformer.normalize).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('Version Compatibility', () => {\r\n    it('should support version checking', () => {\r\n      const versions = ['1.0.0', '1.1.0', '2.0.0'];\r\n      versions.forEach(version => {\r\n        const versionedData = { ...mockVulnerabilityData, version };\r\n        expect(versionedData.version).toBe(version);\r\n      });\r\n    });\r\n\r\n    it('should maintain backward compatibility', () => {\r\n      const legacyData = {\r\n        ...mockVulnerabilityData,\r\n        version: '0.9.0',\r\n      };\r\n\r\n      expect(legacyData.version).toBeDefined();\r\n      expect(legacyData.externalId).toBeDefined();\r\n      expect(legacyData.title).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('External Integration Support', () => {\r\n    it('should support CVE format compatibility', () => {\r\n      const cveCompatible = {\r\n        ...mockVulnerabilityData,\r\n        attributes: {\r\n          ...mockVulnerabilityData.attributes,\r\n          cve_format: '5.0',\r\n          cve_data_type: 'CVE',\r\n          cve_data_format: 'MITRE',\r\n        },\r\n      };\r\n\r\n      expect(cveCompatible.attributes).toHaveProperty('cve_format');\r\n      expect(cveCompatible.attributes).toHaveProperty('cve_data_type');\r\n    });\r\n\r\n    it('should support multiple vulnerability databases', () => {\r\n      const multiSourceData = {\r\n        ...mockVulnerabilityData,\r\n        alternativeIds: [\r\n          ...mockVulnerabilityData.alternativeIds!,\r\n          { type: 'osvdb', value: 'OSVDB-12345', source: 'OSVDB' },\r\n          { type: 'bugtraq', value: 'BID-67890', source: 'SecurityFocus' },\r\n        ],\r\n      };\r\n\r\n      expect(multiSourceData.alternativeIds!.length).toBeGreaterThan(2);\r\n      expect(multiSourceData.alternativeIds!.some(id => id.type === 'osvdb')).toBe(true);\r\n    });\r\n\r\n    it('should support enrichment from multiple sources', () => {\r\n      const enrichedData = {\r\n        ...mockVulnerabilityData,\r\n        attributes: {\r\n          ...mockVulnerabilityData.attributes,\r\n          enrichment_sources: ['nvd', 'mitre', 'vendor'],\r\n          enrichment_timestamp: '2024-01-01T00:00:00Z',\r\n          enrichment_version: '1.0',\r\n        },\r\n      };\r\n\r\n      expect(enrichedData.attributes).toHaveProperty('enrichment_sources');\r\n      expect(Array.isArray(enrichedData.attributes!.enrichment_sources)).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}