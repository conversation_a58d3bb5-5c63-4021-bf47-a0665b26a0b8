{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\repository-contracts.integration.spec.ts", "mappings": ";;AAAA,6CAAsD;AAQtD,wEAAoE;AACpE,0EAAsE;AACtE,wFAAoF;AACpF,4FAAuF;AAIvF,iFAA6E;AAC7E,mFAA+E;AAC/E,iGAA6F;AAC7F,qGAAgG;AAIhG,uHAAsG;AACtG,yHAAwG;AACxG,mHAAkG;AAClG,wGAAuF;AACvF,4FAA4E;AAE5E,kHAAiG;AACjG,wEAA+D;AAC/D,gFAAuE;AACvE,4EAAmE;AACnE,sFAA4E;AAC5E,kFAAyE;AACzE,gGAAuF;AACvF,0EAAiE;AACjE,8EAAqE;AAGrE,yHAAuG;AACvG,2GAA0F;AAC1F,uGAAsF;AAItF;;GAEG;AACH,MAAM,uBAAuB;IAA7B;QACU,WAAM,GAAuB,IAAI,GAAG,EAAE,CAAC;IA6DjD,CAAC;IA3DC,KAAK,CAAC,IAAI,CAAC,KAAY;QACrB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAkB;QAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAkB;QAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAW,EAAE,GAAS;QAC1C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACrD,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAC3C,OAAO,SAAS,IAAI,KAAK,IAAI,SAAS,IAAI,GAAG,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAmB;QACpC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACrD,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAC5B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAuB;QAC1C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACrD,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAC5B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,YAAoB,EAAE,UAAwB,EAAE,WAA2B;QACxG,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,CAAC;QACvD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACrD,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAC3C,MAAM,SAAS,GAAG,SAAS,IAAI,UAAU,CAAC;YAC1C,MAAM,SAAS,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,aAAa,GAAG,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;YAC7F,OAAO,SAAS,IAAI,SAAS,IAAI,aAAa,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,CAAgB,EAAE,CAAgB;QACxD,MAAM,aAAa,GAAG,CAAC,mCAAa,CAAC,GAAG,EAAE,mCAAa,CAAC,MAAM,EAAE,mCAAa,CAAC,IAAI,EAAE,mCAAa,CAAC,QAAQ,CAAC,CAAC;QAC5G,OAAO,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED,iCAAiC;IACjC,KAAK;QACH,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B,CAAC;CACF;AAED,MAAM,wBAAwB;IAA9B;QACU,YAAO,GAAwB,IAAI,GAAG,EAAE,CAAC;IAqCnD,CAAC;IAnCC,KAAK,CAAC,IAAI,CAAC,MAAc;QACvB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAkB;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAkB;QAC7B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAwB;QAC3C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAC7B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAmB;QACtC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CACpD,CAAC;IACJ,CAAC;IAED,KAAK;QACH,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;IAC3B,CAAC;CACF;AAED,MAAM,+BAA+B;IAArC;QACU,oBAAe,GAA+B,IAAI,GAAG,EAAE,CAAC;IAqClE,CAAC;IAnCC,KAAK,CAAC,IAAI,CAAC,aAA4B;QACrC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,aAAa,CAAC,CAAC;IACvE,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAkB;QAC/B,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAkB;QAC7B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAA+B;QAClD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAC7D,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAC3B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAU;QACzB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAC7D,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CACpD,CAAC;IACJ,CAAC;IAED,KAAK;QACH,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;IACnC,CAAC;CACF;AAED,MAAM,gCAAgC;IAAtC;QACU,YAAO,GAAgC,IAAI,GAAG,EAAE,CAAC;IAqC3D,CAAC;IAnCC,KAAK,CAAC,IAAI,CAAC,MAAsB;QAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAkB;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAkB;QAC7B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAoB;QACrC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,MAAM,KAAK,MAAM,CACzB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAgB;QAC/B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,IAAI,KAAK,IAAI,CACrB,CAAC;IACJ,CAAC;IAED,KAAK;QACH,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;IAC3B,CAAC;CACF;AAED,QAAQ,CAAC,wCAAwC,EAAE,GAAG,EAAE;IACtD,IAAI,MAAqB,CAAC;IAC1B,IAAI,eAAwC,CAAC;IAC7C,IAAI,gBAA0C,CAAC;IAC/C,IAAI,uBAAwD,CAAC;IAC7D,IAAI,wBAA0D,CAAC;IAE/D,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,eAAe,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAChD,gBAAgB,GAAG,IAAI,wBAAwB,EAAE,CAAC;QAClD,uBAAuB,GAAG,IAAI,+BAA+B,EAAE,CAAC;QAChE,wBAAwB,GAAG,IAAI,gCAAgC,EAAE,CAAC;QAElE,MAAM,GAAG,MAAM,cAAI,CAAC,mBAAmB,CAAC;YACtC,SAAS,EAAE;gBACT,EAAE,OAAO,EAAE,kCAAe,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACvD,EAAE,OAAO,EAAE,oCAAgB,EAAE,QAAQ,EAAE,gBAAgB,EAAE;gBACzD,EAAE,OAAO,EAAE,kDAAuB,EAAE,QAAQ,EAAE,uBAAuB,EAAE;gBACvE,EAAE,OAAO,EAAE,qDAAwB,EAAE,QAAQ,EAAE,wBAAwB,EAAE;aAC1E;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,eAAe,CAAC,KAAK,EAAE,CAAC;QACxB,gBAAgB,CAAC,KAAK,EAAE,CAAC;QACzB,uBAAuB,CAAC,KAAK,EAAE,CAAC;QAChC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACjC,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAClE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,GAAG;gBAC3B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;gBACjC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,wBAAwB;YACxB,MAAM,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACjE,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEvC,sBAAsB;YACtB,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC5D,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,UAAW,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,UAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAElD,wCAAwC;YACxC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACzB,MAAM,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC9D,MAAM,CAAC,YAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAW,CAAC,UAAU,CAAC,CAAC;YAE1D,wBAAwB;YACxB,MAAM,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACvC,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC9D,MAAM,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,UAAU;YACV,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,CAAC;YACrD,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,CAAC;YAEtD,MAAM,OAAO,GAAG,uCAAW,CAAC,MAAM,CAAC;gBACjC,IAAI,EAAE,wCAAe,CAAC,QAAQ;gBAC9B,UAAU,EAAE,QAAQ;gBACpB,IAAI,EAAE,YAAY;aACnB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,uCAAW,CAAC,MAAM,CAAC;gBACjC,IAAI,EAAE,wCAAe,CAAC,GAAG;gBACzB,UAAU,EAAE,SAAS;gBACrB,IAAI,EAAE,OAAO;aACd,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG;gBACb,4BAAY,CAAC,MAAM,CAAC;oBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;oBAChE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,UAAU,CAAC;oBAC5C,MAAM,EAAE,OAAO;oBACf,IAAI,EAAE,2BAAS,CAAC,iBAAiB;oBACjC,QAAQ,EAAE,mCAAa,CAAC,IAAI;oBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;oBAC5B,OAAO,EAAE,EAAE;oBACX,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC;gBACF,4BAAY,CAAC,MAAM,CAAC;oBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;oBACjE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,WAAW,CAAC;oBAC7C,MAAM,EAAE,OAAO;oBACf,IAAI,EAAE,2BAAS,CAAC,gBAAgB;oBAChC,QAAQ,EAAE,mCAAa,CAAC,QAAQ;oBAChC,MAAM,EAAE,+BAAW,CAAC,QAAQ;oBAC5B,OAAO,EAAE,EAAE;oBACX,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC;gBACF,4BAAY,CAAC,MAAM,CAAC;oBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;oBAChE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,GAAG,CAAC;oBACrC,MAAM,EAAE,OAAO;oBACf,IAAI,EAAE,2BAAS,CAAC,sBAAsB;oBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;oBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;oBAC5B,OAAO,EAAE,EAAE;oBACX,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC;aACH,CAAC;YAEF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,MAAM,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,CAAC;YAED,iCAAiC;YACjC,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAC5E,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAErC,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YACjF,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAElC,8BAA8B;YAC9B,MAAM,cAAc,GAAG,MAAM,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YACnE,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEvC,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC9D,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAElC,gCAAgC;YAChC,MAAM,kBAAkB,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YACpF,MAAM,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE3C,MAAM,cAAc,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,mCAAa,CAAC,QAAQ,CAAC,CAAC;YACpF,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEvC,0CAA0C;YAC1C,MAAM,qBAAqB,GAAG,MAAM,eAAe,CAAC,wBAAwB,CAC1E,OAAO,EAAE,SAAS;YAClB,CAAC,2BAAS,CAAC,iBAAiB,EAAE,2BAAS,CAAC,gBAAgB,CAAC,EACzD,mCAAa,CAAC,IAAI,CACnB,CAAC;YACF,MAAM,CAAC,qBAAqB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,UAAU;YACV,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACjD,4BAAY,CAAC,MAAM,CAAC;gBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACzE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,OAAO,CAAC,EAAE;oBACtB,IAAI,EAAE,OAAO,CAAC,EAAE;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;gBACvB,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CACH,CAAC;YAEF,qCAAqC;YACrC,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAEpE,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxC,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,OAAO,EAAE,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAEnC,wCAAwC;YACxC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,MAAM,KAAK,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACvD,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC5B,MAAM,CAAC,KAAM,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,mCAAmC;YACnC,MAAM,aAAa,GAAG,8CAAc,CAAC,MAAM,EAAE,CAAC;YAC9C,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC/D,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;YAE5B,uDAAuD;YACvD,MAAM,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAE3E,qBAAqB;YACrB,MAAM,cAAc,GAAG,MAAM,eAAe,CAAC,eAAe,CAC1D,IAAI,IAAI,CAAC,YAAY,CAAC,EACtB,IAAI,IAAI,CAAC,YAAY,CAAC,CACvB,CAAC;YACF,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEvC,MAAM,aAAa,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,mCAAa,CAAC,GAAG,CAAC,CAAC;YAC9E,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,UAAU;YACV,MAAM,MAAM,GAAG,8BAAa,CAAC,MAAM,CAAC;gBAClC,SAAS,EAAE,iBAAiB;gBAC5B,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;gBAC5B,QAAQ,EAAE,qCAAc,CAAC,IAAI;gBAC7B,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,mCAAS,CAAC,MAAM,CAAC,eAAe,CAAC;gBAC3C,UAAU,EAAE,CAAC,gBAAgB,CAAC;gBAC9B,eAAe,EAAE,CAAC,OAAO,CAAC;gBAC1B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,wBAAwB;YACxB,MAAM,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACnE,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAExC,sBAAsB;YACtB,MAAM,WAAW,GAAG,MAAM,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC/D,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,WAAY,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,WAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAEpD,wBAAwB;YACxB,MAAM,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,aAAa,GAAG,MAAM,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACjE,MAAM,CAAC,aAAc,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEhD,wBAAwB;YACxB,MAAM,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACzC,MAAM,aAAa,GAAG,MAAM,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACjE,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,UAAU;YACV,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAE/C,MAAM,OAAO,GAAG;gBACd,8BAAa,CAAC,MAAM,CAAC;oBACnB,SAAS,EAAE,YAAY;oBACvB,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;oBAC5B,QAAQ,EAAE,qCAAc,CAAC,IAAI;oBAC7B,UAAU,EAAE,EAAE;oBACd,QAAQ,EAAE,SAAS;oBACnB,UAAU,EAAE,CAAC,SAAS,CAAC;oBACvB,eAAe,EAAE,CAAC,OAAO,CAAC;oBAC1B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC;gBACF,8BAAa,CAAC,MAAM,CAAC;oBACnB,SAAS,EAAE,YAAY;oBACvB,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;oBAC5B,QAAQ,EAAE,qCAAc,CAAC,QAAQ;oBACjC,UAAU,EAAE,EAAE;oBACd,QAAQ,EAAE,SAAS;oBACnB,UAAU,EAAE,CAAC,SAAS,CAAC;oBACvB,eAAe,EAAE,CAAC,OAAO,CAAC;oBAC1B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC;gBACF,8BAAa,CAAC,MAAM,CAAC;oBACnB,SAAS,EAAE,YAAY;oBACvB,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;oBAC5B,QAAQ,EAAE,qCAAc,CAAC,MAAM;oBAC/B,UAAU,EAAE,EAAE;oBACd,QAAQ,EAAE,SAAS;oBACnB,UAAU,EAAE,CAAC,SAAS,CAAC;oBACvB,eAAe,EAAE,CAAC,OAAO,CAAC;oBAC1B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC;aACH,CAAC;YAEF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,CAAC;YAED,gCAAgC;YAChC,MAAM,WAAW,GAAG,MAAM,gBAAgB,CAAC,cAAc,CAAC,qCAAc,CAAC,IAAI,CAAC,CAAC;YAC/E,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEpC,MAAM,eAAe,GAAG,MAAM,gBAAgB,CAAC,cAAc,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;YACvF,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAExC,gCAAgC;YAChC,MAAM,cAAc,GAAG,MAAM,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACxE,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEvC,MAAM,cAAc,GAAG,MAAM,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACxE,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,UAAU;YACV,MAAM,aAAa,GAAG,4CAAoB,CAAC,MAAM,CAAC;gBAChD,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;gBAC5B,QAAQ,EAAE,mDAAqB,CAAC,IAAI;gBACpC,YAAY,EAAE,wBAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC7B,WAAW,EAAE,mCAAmC;gBAChD,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,wBAAwB;YACxB,MAAM,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACjF,MAAM,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE/C,sBAAsB;YACtB,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAC3E,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,CAAC,SAAU,CAAC,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,SAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAEnD,wBAAwB;YACxB,aAAa,CAAC,aAAa,EAAE,CAAC;YAC9B,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClD,MAAM,WAAW,GAAG,MAAM,uBAAuB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAC7E,MAAM,CAAC,WAAY,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE5C,wBAAwB;YACxB,MAAM,uBAAuB,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YACvD,MAAM,WAAW,GAAG,MAAM,uBAAuB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAC7E,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,UAAU;YACV,MAAM,MAAM,GAAG,wBAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC/B,MAAM,OAAO,GAAG,wBAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEjC,MAAM,eAAe,GAAG;gBACtB,4CAAoB,CAAC,MAAM,CAAC;oBAC1B,KAAK,EAAE,eAAe;oBACtB,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;oBAC5B,QAAQ,EAAE,mDAAqB,CAAC,QAAQ;oBACxC,YAAY,EAAE,MAAM;oBACpB,WAAW,EAAE,6BAA6B;oBAC1C,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC;gBACF,4CAAoB,CAAC,MAAM,CAAC;oBAC1B,KAAK,EAAE,eAAe;oBACtB,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;oBAC5B,QAAQ,EAAE,mDAAqB,CAAC,IAAI;oBACpC,YAAY,EAAE,OAAO;oBACrB,WAAW,EAAE,0BAA0B;oBACvC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC;gBACF,4CAAoB,CAAC,MAAM,CAAC;oBAC1B,KAAK,EAAE,eAAe;oBACtB,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;oBAC5B,QAAQ,EAAE,mDAAqB,CAAC,MAAM;oBACtC,YAAY,EAAE,MAAM;oBACpB,WAAW,EAAE,2BAA2B;oBACxC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC;aACH,CAAC;YAEF,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;gBACnC,MAAM,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,CAAC;YAED,gCAAgC;YAChC,MAAM,aAAa,GAAG,MAAM,uBAAuB,CAAC,cAAc,CAAC,mDAAqB,CAAC,QAAQ,CAAC,CAAC;YACnG,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEtC,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,cAAc,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC;YAC3F,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAElC,4BAA4B;YAC5B,MAAM,WAAW,GAAG,MAAM,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACrE,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEpC,MAAM,YAAY,GAAG,MAAM,uBAAuB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACvE,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,UAAU;YACV,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAC1C,IAAI,EAAE,6BAAU,CAAC,QAAQ;gBACzB,UAAU,EAAE,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAC1D,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,iCAAY,CAAC,OAAO;gBAC5B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,wBAAwB;YACxB,MAAM,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAC3E,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEhD,sBAAsB;YACtB,MAAM,WAAW,GAAG,MAAM,wBAAwB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACvE,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,WAAY,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,WAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAE5C,wBAAwB;YACxB,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,aAAa,GAAG,MAAM,wBAAwB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACzE,MAAM,CAAC,aAAc,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,WAAW,CAAC,CAAC;YAE7D,wBAAwB;YACxB,MAAM,wBAAwB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACjD,MAAM,aAAa,GAAG,MAAM,wBAAwB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACzE,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,UAAU;YACV,MAAM,OAAO,GAAG;gBACd,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,IAAI,EAAE,6BAAU,CAAC,QAAQ;oBACzB,UAAU,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE;oBACrC,QAAQ,EAAE,CAAC;oBACX,MAAM,EAAE,iCAAY,CAAC,OAAO;oBAC5B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC;gBACF,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,IAAI,EAAE,6BAAU,CAAC,YAAY;oBAC7B,UAAU,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE;oBAClC,QAAQ,EAAE,CAAC;oBACX,MAAM,EAAE,iCAAY,CAAC,WAAW;oBAChC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC;gBACF,+CAAqB,CAAC,MAAM,CAAC;oBAC3B,IAAI,EAAE,6BAAU,CAAC,QAAQ;oBACzB,UAAU,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE;oBACrC,QAAQ,EAAE,CAAC;oBACX,MAAM,EAAE,iCAAY,CAAC,SAAS;oBAC9B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC;aACH,CAAC;YAEF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,CAAC;YAED,8BAA8B;YAC9B,MAAM,cAAc,GAAG,MAAM,wBAAwB,CAAC,YAAY,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;YACzF,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEvC,MAAM,iBAAiB,GAAG,MAAM,wBAAwB,CAAC,YAAY,CAAC,iCAAY,CAAC,WAAW,CAAC,CAAC;YAChG,MAAM,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE1C,MAAM,gBAAgB,GAAG,MAAM,wBAAwB,CAAC,YAAY,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;YAC7F,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzC,4BAA4B;YAC5B,MAAM,cAAc,GAAG,MAAM,wBAAwB,CAAC,UAAU,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YACtF,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEvC,MAAM,kBAAkB,GAAG,MAAM,wBAAwB,CAAC,UAAU,CAAC,6BAAU,CAAC,YAAY,CAAC,CAAC;YAC9F,MAAM,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;YACzE,UAAU;YACV,MAAM,QAAQ,GAAG,iCAAQ,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,6BAAM,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,mCAAS,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAEnD,0BAA0B;YAC1B,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACtE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,QAAQ;oBAC9B,UAAU,EAAE,QAAQ;oBACpB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,iBAAiB;gBACjC,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,KAAK,EAAE;gBACrC,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,8BAAa,CAAC,MAAM,CAAC;gBAClC,SAAS,EAAE,kBAAkB;gBAC7B,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;gBAC5B,QAAQ,EAAE,qCAAc,CAAC,IAAI;gBAC7B,UAAU,EAAE,EAAE;gBACd,QAAQ;gBACR,UAAU,EAAE,CAAC,mBAAmB,CAAC;gBACjC,eAAe,EAAE,CAAC,OAAO,CAAC;gBAC1B,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAClD,IAAI,EAAE,6BAAU,CAAC,QAAQ;gBACzB,UAAU,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACzD,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,iCAAY,CAAC,OAAO;gBAC5B,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,wBAAwB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAEpD,yCAAyC;YACzC,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC5D,MAAM,WAAW,GAAG,MAAM,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC/D,MAAM,WAAW,GAAG,MAAM,wBAAwB,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YAE/E,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAElC,uCAAuC;YACvC,MAAM,WAAW,GAAG,MAAM,gBAAgB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACpE,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,cAAc,GAAG,MAAM,wBAAwB,CAAC,UAAU,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YACtF,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,UAAU;YACV,MAAM,QAAQ,GAAG,iCAAQ,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,6BAAM,CAAC,MAAM,EAAE,CAAC;YAE/B,iCAAiC;YACjC,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAChD,4BAAY,CAAC,MAAM,CAAC;gBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACzE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;gBACjE,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,OAAO,CAAC,EAAE;oBACtB,IAAI,EAAE,OAAO,CAAC,EAAE;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;gBACvB,QAAQ;gBACR,MAAM;aACP,CAAC,CACH,CAAC;YAEF,wBAAwB;YACxB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,MAAM,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,CAAC;YAED,mCAAmC;YACnC,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,OAAO,EAAE,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAElC,MAAM,oBAAoB,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,mCAAa,CAAC,MAAM,CAAC,CAAC;YACxF,MAAM,CAAC,oBAAoB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE7C,sBAAsB;YACtB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,MAAM,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACzC,CAAC;YAED,MAAM,eAAe,GAAG,MAAM,eAAe,CAAC,OAAO,EAAE,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\repository-contracts.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { Event } from '../../domain/entities/event.entity';\r\nimport { Threat } from '../../domain/entities/threat.entity';\r\nimport { Vulnerability } from '../../domain/entities/vulnerability.entity';\r\nimport { ResponseAction } from '../../domain/entities/response-action.entity';\r\nimport { NormalizedEvent } from '../../domain/entities/normalized-event.entity';\r\nimport { EnrichedEvent } from '../../domain/entities/enriched-event.entity';\r\nimport { CorrelatedEvent } from '../../domain/entities/correlated-event.entity';\r\nimport { EventFactory } from '../../domain/factories/event.factory';\r\nimport { ThreatFactory } from '../../domain/factories/threat.factory';\r\nimport { VulnerabilityFactory } from '../../domain/factories/vulnerability.factory';\r\nimport { ResponseActionFactory } from '../../domain/factories/response-action.factory';\r\nimport { NormalizedEventFactory } from '../../domain/factories/normalized-event.factory';\r\nimport { EnrichedEventFactory } from '../../domain/factories/enriched-event.factory';\r\nimport { CorrelatedEventFactory } from '../../domain/factories/correlated-event.factory';\r\nimport { EventRepository } from '../../domain/repositories/event.repository';\r\nimport { ThreatRepository } from '../../domain/repositories/threat.repository';\r\nimport { VulnerabilityRepository } from '../../domain/repositories/vulnerability.repository';\r\nimport { ResponseActionRepository } from '../../domain/repositories/response-action.repository';\r\nimport { NormalizedEventRepository } from '../../domain/repositories/normalized-event.repository';\r\nimport { EnrichedEventRepository } from '../../domain/repositories/enriched-event.repository';\r\nimport { CorrelatedEventRepository } from '../../domain/repositories/correlated-event.repository';\r\nimport { EventMetadata } from '../../domain/value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../domain/value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../domain/value-objects/event-metadata/event-source.value-object';\r\nimport { IpAddress } from '../../domain/value-objects/network/ip-address.value-object';\r\nimport { Port } from '../../domain/value-objects/network/port.value-object';\r\nimport { NetworkSegment } from '../../domain/value-objects/network/network-segment.value-object';\r\nimport { CvssScore } from '../../domain/value-objects/threat-indicators/cvss-score.value-object';\r\nimport { EventType } from '../../domain/enums/event-type.enum';\r\nimport { EventSeverity } from '../../domain/enums/event-severity.enum';\r\nimport { EventStatus } from '../../domain/enums/event-status.enum';\r\nimport { EventSourceType } from '../../domain/enums/event-source-type.enum';\r\nimport { ThreatSeverity } from '../../domain/enums/threat-severity.enum';\r\nimport { VulnerabilitySeverity } from '../../domain/enums/vulnerability-severity.enum';\r\nimport { ActionType } from '../../domain/enums/action-type.enum';\r\nimport { ActionStatus } from '../../domain/enums/action-status.enum';\r\nimport { CorrelationStatus } from '../../domain/enums/correlation-status.enum';\r\nimport { EnrichmentSource } from '../../domain/enums/enrichment-source.enum';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { TenantId } from '../../../../shared-kernel/value-objects/tenant-id.value-object';\r\nimport { UserId } from '../../../../shared-kernel/value-objects/user-id.value-object';\r\nimport { NotFoundException } from '../../../../shared-kernel/exceptions/not-found.exception';\r\nimport { ConflictException } from '../../../../shared-kernel/exceptions/conflict.exception';\r\n\r\n/**\r\n * In-Memory Repository Implementations for Contract Testing\r\n */\r\nclass InMemoryEventRepository implements EventRepository {\r\n  private events: Map<string, Event> = new Map();\r\n\r\n  async save(event: Event): Promise<void> {\r\n    this.events.set(event.id.toString(), event);\r\n  }\r\n\r\n  async findById(id: UniqueEntityId): Promise<Event | null> {\r\n    return this.events.get(id.toString()) || null;\r\n  }\r\n\r\n  async findAll(): Promise<Event[]> {\r\n    return Array.from(this.events.values());\r\n  }\r\n\r\n  async delete(id: UniqueEntityId): Promise<void> {\r\n    this.events.delete(id.toString());\r\n  }\r\n\r\n  async findByTimeRange(start: Date, end: Date): Promise<Event[]> {\r\n    return Array.from(this.events.values()).filter(event => {\r\n      const eventTime = event.timestamp.toDate();\r\n      return eventTime >= start && eventTime <= end;\r\n    });\r\n  }\r\n\r\n  async findBySource(source: EventSource): Promise<Event[]> {\r\n    return Array.from(this.events.values()).filter(event => \r\n      event.source.equals(source)\r\n    );\r\n  }\r\n\r\n  async findBySeverity(severity: EventSeverity): Promise<Event[]> {\r\n    return Array.from(this.events.values()).filter(event => \r\n      event.severity === severity\r\n    );\r\n  }\r\n\r\n  async findEventsForCorrelation(timeWindowMs: number, eventTypes?: EventType[], minSeverity?: EventSeverity): Promise<Event[]> {\r\n    const cutoffTime = new Date(Date.now() - timeWindowMs);\r\n    return Array.from(this.events.values()).filter(event => {\r\n      const eventTime = event.timestamp.toDate();\r\n      const timeMatch = eventTime >= cutoffTime;\r\n      const typeMatch = !eventTypes || eventTypes.includes(event.type);\r\n      const severityMatch = !minSeverity || this.compareSeverity(event.severity, minSeverity) >= 0;\r\n      return timeMatch && typeMatch && severityMatch;\r\n    });\r\n  }\r\n\r\n  private compareSeverity(a: EventSeverity, b: EventSeverity): number {\r\n    const severityOrder = [EventSeverity.LOW, EventSeverity.MEDIUM, EventSeverity.HIGH, EventSeverity.CRITICAL];\r\n    return severityOrder.indexOf(a) - severityOrder.indexOf(b);\r\n  }\r\n\r\n  // Additional methods for testing\r\n  clear(): void {\r\n    this.events.clear();\r\n  }\r\n\r\n  size(): number {\r\n    return this.events.size;\r\n  }\r\n}\r\n\r\nclass InMemoryThreatRepository implements ThreatRepository {\r\n  private threats: Map<string, Threat> = new Map();\r\n\r\n  async save(threat: Threat): Promise<void> {\r\n    this.threats.set(threat.id.toString(), threat);\r\n  }\r\n\r\n  async findById(id: UniqueEntityId): Promise<Threat | null> {\r\n    return this.threats.get(id.toString()) || null;\r\n  }\r\n\r\n  async findAll(): Promise<Threat[]> {\r\n    return Array.from(this.threats.values());\r\n  }\r\n\r\n  async delete(id: UniqueEntityId): Promise<void> {\r\n    this.threats.delete(id.toString());\r\n  }\r\n\r\n  async findBySeverity(severity: ThreatSeverity): Promise<Threat[]> {\r\n    return Array.from(this.threats.values()).filter(threat => \r\n      threat.severity === severity\r\n    );\r\n  }\r\n\r\n  async findBySourceIp(sourceIp: IpAddress): Promise<Threat[]> {\r\n    return Array.from(this.threats.values()).filter(threat => \r\n      threat.sourceIp && threat.sourceIp.equals(sourceIp)\r\n    );\r\n  }\r\n\r\n  clear(): void {\r\n    this.threats.clear();\r\n  }\r\n\r\n  size(): number {\r\n    return this.threats.size;\r\n  }\r\n}\r\n\r\nclass InMemoryVulnerabilityRepository implements VulnerabilityRepository {\r\n  private vulnerabilities: Map<string, Vulnerability> = new Map();\r\n\r\n  async save(vulnerability: Vulnerability): Promise<void> {\r\n    this.vulnerabilities.set(vulnerability.id.toString(), vulnerability);\r\n  }\r\n\r\n  async findById(id: UniqueEntityId): Promise<Vulnerability | null> {\r\n    return this.vulnerabilities.get(id.toString()) || null;\r\n  }\r\n\r\n  async findAll(): Promise<Vulnerability[]> {\r\n    return Array.from(this.vulnerabilities.values());\r\n  }\r\n\r\n  async delete(id: UniqueEntityId): Promise<void> {\r\n    this.vulnerabilities.delete(id.toString());\r\n  }\r\n\r\n  async findBySeverity(severity: VulnerabilitySeverity): Promise<Vulnerability[]> {\r\n    return Array.from(this.vulnerabilities.values()).filter(vuln => \r\n      vuln.severity === severity\r\n    );\r\n  }\r\n\r\n  async findByPort(port: Port): Promise<Vulnerability[]> {\r\n    return Array.from(this.vulnerabilities.values()).filter(vuln => \r\n      vuln.affectedPort && vuln.affectedPort.equals(port)\r\n    );\r\n  }\r\n\r\n  clear(): void {\r\n    this.vulnerabilities.clear();\r\n  }\r\n\r\n  size(): number {\r\n    return this.vulnerabilities.size;\r\n  }\r\n}\r\n\r\nclass InMemoryResponseActionRepository implements ResponseActionRepository {\r\n  private actions: Map<string, ResponseAction> = new Map();\r\n\r\n  async save(action: ResponseAction): Promise<void> {\r\n    this.actions.set(action.id.toString(), action);\r\n  }\r\n\r\n  async findById(id: UniqueEntityId): Promise<ResponseAction | null> {\r\n    return this.actions.get(id.toString()) || null;\r\n  }\r\n\r\n  async findAll(): Promise<ResponseAction[]> {\r\n    return Array.from(this.actions.values());\r\n  }\r\n\r\n  async delete(id: UniqueEntityId): Promise<void> {\r\n    this.actions.delete(id.toString());\r\n  }\r\n\r\n  async findByStatus(status: ActionStatus): Promise<ResponseAction[]> {\r\n    return Array.from(this.actions.values()).filter(action => \r\n      action.status === status\r\n    );\r\n  }\r\n\r\n  async findByType(type: ActionType): Promise<ResponseAction[]> {\r\n    return Array.from(this.actions.values()).filter(action => \r\n      action.type === type\r\n    );\r\n  }\r\n\r\n  clear(): void {\r\n    this.actions.clear();\r\n  }\r\n\r\n  size(): number {\r\n    return this.actions.size;\r\n  }\r\n}\r\n\r\ndescribe('Repository Contracts Integration Tests', () => {\r\n  let module: TestingModule;\r\n  let eventRepository: InMemoryEventRepository;\r\n  let threatRepository: InMemoryThreatRepository;\r\n  let vulnerabilityRepository: InMemoryVulnerabilityRepository;\r\n  let responseActionRepository: InMemoryResponseActionRepository;\r\n\r\n  beforeEach(async () => {\r\n    eventRepository = new InMemoryEventRepository();\r\n    threatRepository = new InMemoryThreatRepository();\r\n    vulnerabilityRepository = new InMemoryVulnerabilityRepository();\r\n    responseActionRepository = new InMemoryResponseActionRepository();\r\n\r\n    module = await Test.createTestingModule({\r\n      providers: [\r\n        { provide: EventRepository, useValue: eventRepository },\r\n        { provide: ThreatRepository, useValue: threatRepository },\r\n        { provide: VulnerabilityRepository, useValue: vulnerabilityRepository },\r\n        { provide: ResponseActionRepository, useValue: responseActionRepository },\r\n      ],\r\n    }).compile();\r\n  });\r\n\r\n  afterEach(async () => {\r\n    eventRepository.clear();\r\n    threatRepository.clear();\r\n    vulnerabilityRepository.clear();\r\n    responseActionRepository.clear();\r\n    await module.close();\r\n  });\r\n\r\n  describe('EventRepository Contract', () => {\r\n    it('should implement complete CRUD operations', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-001',\r\n          name: 'Test App',\r\n        }),\r\n        type: EventType.AUTHENTICATION_SUCCESS,\r\n        severity: EventSeverity.LOW,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { username: 'testuser' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Act & Assert - Create\r\n      await expect(eventRepository.save(event)).resolves.not.toThrow();\r\n      expect(eventRepository.size()).toBe(1);\r\n\r\n      // Act & Assert - Read\r\n      const foundEvent = await eventRepository.findById(event.id);\r\n      expect(foundEvent).toBeDefined();\r\n      expect(foundEvent!.id.equals(event.id)).toBe(true);\r\n      expect(foundEvent!.type).toBe(event.type);\r\n      expect(foundEvent!.severity).toBe(event.severity);\r\n\r\n      // Act & Assert - Update (save existing)\r\n      event.markAsProcessing();\r\n      await eventRepository.save(event);\r\n      const updatedEvent = await eventRepository.findById(event.id);\r\n      expect(updatedEvent!.status).toBe(EventStatus.PROCESSING);\r\n\r\n      // Act & Assert - Delete\r\n      await eventRepository.delete(event.id);\r\n      const deletedEvent = await eventRepository.findById(event.id);\r\n      expect(deletedEvent).toBeNull();\r\n      expect(eventRepository.size()).toBe(0);\r\n    });\r\n\r\n    it('should handle specialized query methods', async () => {\r\n      // Arrange\r\n      const now = new Date();\r\n      const oneHourAgo = new Date(now.getTime() - 3600000);\r\n      const twoHoursAgo = new Date(now.getTime() - 7200000);\r\n\r\n      const source1 = EventSource.create({\r\n        type: EventSourceType.FIREWALL,\r\n        identifier: 'fw-001',\r\n        name: 'Firewall 1',\r\n      });\r\n\r\n      const source2 = EventSource.create({\r\n        type: EventSourceType.IDS,\r\n        identifier: 'ids-001',\r\n        name: 'IDS 1',\r\n      });\r\n\r\n      const events = [\r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: 'fw', version: '1.0' }),\r\n          timestamp: EventTimestamp.create(oneHourAgo),\r\n          source: source1,\r\n          type: EventType.NETWORK_INTRUSION,\r\n          severity: EventSeverity.HIGH,\r\n          status: EventStatus.RECEIVED,\r\n          payload: {},\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        }),\r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: 'ids', version: '1.0' }),\r\n          timestamp: EventTimestamp.create(twoHoursAgo),\r\n          source: source2,\r\n          type: EventType.MALWARE_DETECTED,\r\n          severity: EventSeverity.CRITICAL,\r\n          status: EventStatus.RECEIVED,\r\n          payload: {},\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        }),\r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: 'fw', version: '1.0' }),\r\n          timestamp: EventTimestamp.create(now),\r\n          source: source1,\r\n          type: EventType.AUTHENTICATION_FAILURE,\r\n          severity: EventSeverity.MEDIUM,\r\n          status: EventStatus.RECEIVED,\r\n          payload: {},\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        }),\r\n      ];\r\n\r\n      for (const event of events) {\r\n        await eventRepository.save(event);\r\n      }\r\n\r\n      // Act & Assert - FindByTimeRange\r\n      const recentEvents = await eventRepository.findByTimeRange(oneHourAgo, now);\r\n      expect(recentEvents).toHaveLength(2);\r\n\r\n      const oldEvents = await eventRepository.findByTimeRange(twoHoursAgo, oneHourAgo);\r\n      expect(oldEvents).toHaveLength(1);\r\n\r\n      // Act & Assert - FindBySource\r\n      const firewallEvents = await eventRepository.findBySource(source1);\r\n      expect(firewallEvents).toHaveLength(2);\r\n\r\n      const idsEvents = await eventRepository.findBySource(source2);\r\n      expect(idsEvents).toHaveLength(1);\r\n\r\n      // Act & Assert - FindBySeverity\r\n      const highSeverityEvents = await eventRepository.findBySeverity(EventSeverity.HIGH);\r\n      expect(highSeverityEvents).toHaveLength(1);\r\n\r\n      const criticalEvents = await eventRepository.findBySeverity(EventSeverity.CRITICAL);\r\n      expect(criticalEvents).toHaveLength(1);\r\n\r\n      // Act & Assert - FindEventsForCorrelation\r\n      const correlationCandidates = await eventRepository.findEventsForCorrelation(\r\n        3600000, // 1 hour\r\n        [EventType.NETWORK_INTRUSION, EventType.MALWARE_DETECTED],\r\n        EventSeverity.HIGH\r\n      );\r\n      expect(correlationCandidates).toHaveLength(2); // HIGH and CRITICAL events\r\n    });\r\n\r\n    it('should handle concurrent operations safely', async () => {\r\n      // Arrange\r\n      const events = Array.from({ length: 10 }, (_, i) => \r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: `source-${i}`, version: '1.0' }),\r\n          timestamp: EventTimestamp.create(new Date()),\r\n          source: EventSource.create({\r\n            type: EventSourceType.APPLICATION,\r\n            identifier: `app-${i}`,\r\n            name: `App ${i}`,\r\n          }),\r\n          type: EventType.AUTHENTICATION_FAILURE,\r\n          severity: EventSeverity.MEDIUM,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { attempt: i },\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        })\r\n      );\r\n\r\n      // Act - Save all events concurrently\r\n      await Promise.all(events.map(event => eventRepository.save(event)));\r\n\r\n      // Assert\r\n      expect(eventRepository.size()).toBe(10);\r\n      const allEvents = await eventRepository.findAll();\r\n      expect(allEvents).toHaveLength(10);\r\n\r\n      // Verify all events are properly stored\r\n      for (const event of events) {\r\n        const found = await eventRepository.findById(event.id);\r\n        expect(found).toBeDefined();\r\n        expect(found!.id.equals(event.id)).toBe(true);\r\n      }\r\n    });\r\n\r\n    it('should handle edge cases and error conditions', async () => {\r\n      // Test finding non-existent entity\r\n      const nonExistentId = UniqueEntityId.create();\r\n      const notFound = await eventRepository.findById(nonExistentId);\r\n      expect(notFound).toBeNull();\r\n\r\n      // Test deleting non-existent entity (should not throw)\r\n      await expect(eventRepository.delete(nonExistentId)).resolves.not.toThrow();\r\n\r\n      // Test empty queries\r\n      const emptyTimeRange = await eventRepository.findByTimeRange(\r\n        new Date('2020-01-01'),\r\n        new Date('2020-01-02')\r\n      );\r\n      expect(emptyTimeRange).toHaveLength(0);\r\n\r\n      const emptySeverity = await eventRepository.findBySeverity(EventSeverity.LOW);\r\n      expect(emptySeverity).toHaveLength(0);\r\n    });\r\n  });\r\n\r\n  describe('ThreatRepository Contract', () => {\r\n    it('should implement complete CRUD operations', async () => {\r\n      // Arrange\r\n      const threat = ThreatFactory.create({\r\n        signature: 'test-threat-001',\r\n        score: CvssScore.create(7.5),\r\n        severity: ThreatSeverity.HIGH,\r\n        confidence: 90,\r\n        sourceIp: IpAddress.create('*************'),\r\n        indicators: ['malicious-hash'],\r\n        mitreTechniques: ['T1055'],\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Act & Assert - Create\r\n      await expect(threatRepository.save(threat)).resolves.not.toThrow();\r\n      expect(threatRepository.size()).toBe(1);\r\n\r\n      // Act & Assert - Read\r\n      const foundThreat = await threatRepository.findById(threat.id);\r\n      expect(foundThreat).toBeDefined();\r\n      expect(foundThreat!.id.equals(threat.id)).toBe(true);\r\n      expect(foundThreat!.severity).toBe(threat.severity);\r\n\r\n      // Act & Assert - Update\r\n      threat.markAsMitigated();\r\n      await threatRepository.save(threat);\r\n      const updatedThreat = await threatRepository.findById(threat.id);\r\n      expect(updatedThreat!.isMitigated()).toBe(true);\r\n\r\n      // Act & Assert - Delete\r\n      await threatRepository.delete(threat.id);\r\n      const deletedThreat = await threatRepository.findById(threat.id);\r\n      expect(deletedThreat).toBeNull();\r\n    });\r\n\r\n    it('should handle specialized query methods', async () => {\r\n      // Arrange\r\n      const sourceIp1 = IpAddress.create('********');\r\n      const sourceIp2 = IpAddress.create('********');\r\n\r\n      const threats = [\r\n        ThreatFactory.create({\r\n          signature: 'threat-001',\r\n          score: CvssScore.create(8.0),\r\n          severity: ThreatSeverity.HIGH,\r\n          confidence: 85,\r\n          sourceIp: sourceIp1,\r\n          indicators: ['ioc-001'],\r\n          mitreTechniques: ['T1001'],\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        }),\r\n        ThreatFactory.create({\r\n          signature: 'threat-002',\r\n          score: CvssScore.create(9.5),\r\n          severity: ThreatSeverity.CRITICAL,\r\n          confidence: 95,\r\n          sourceIp: sourceIp2,\r\n          indicators: ['ioc-002'],\r\n          mitreTechniques: ['T1002'],\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        }),\r\n        ThreatFactory.create({\r\n          signature: 'threat-003',\r\n          score: CvssScore.create(6.0),\r\n          severity: ThreatSeverity.MEDIUM,\r\n          confidence: 75,\r\n          sourceIp: sourceIp1,\r\n          indicators: ['ioc-003'],\r\n          mitreTechniques: ['T1003'],\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        }),\r\n      ];\r\n\r\n      for (const threat of threats) {\r\n        await threatRepository.save(threat);\r\n      }\r\n\r\n      // Act & Assert - FindBySeverity\r\n      const highThreats = await threatRepository.findBySeverity(ThreatSeverity.HIGH);\r\n      expect(highThreats).toHaveLength(1);\r\n\r\n      const criticalThreats = await threatRepository.findBySeverity(ThreatSeverity.CRITICAL);\r\n      expect(criticalThreats).toHaveLength(1);\r\n\r\n      // Act & Assert - FindBySourceIp\r\n      const threatsFromIp1 = await threatRepository.findBySourceIp(sourceIp1);\r\n      expect(threatsFromIp1).toHaveLength(2);\r\n\r\n      const threatsFromIp2 = await threatRepository.findBySourceIp(sourceIp2);\r\n      expect(threatsFromIp2).toHaveLength(1);\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityRepository Contract', () => {\r\n    it('should implement complete CRUD operations', async () => {\r\n      // Arrange\r\n      const vulnerability = VulnerabilityFactory.create({\r\n        cveId: 'CVE-2023-1234',\r\n        score: CvssScore.create(8.5),\r\n        severity: VulnerabilitySeverity.HIGH,\r\n        affectedPort: Port.create(80),\r\n        description: 'Critical web server vulnerability',\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Act & Assert - Create\r\n      await expect(vulnerabilityRepository.save(vulnerability)).resolves.not.toThrow();\r\n      expect(vulnerabilityRepository.size()).toBe(1);\r\n\r\n      // Act & Assert - Read\r\n      const foundVuln = await vulnerabilityRepository.findById(vulnerability.id);\r\n      expect(foundVuln).toBeDefined();\r\n      expect(foundVuln!.id.equals(vulnerability.id)).toBe(true);\r\n      expect(foundVuln!.cveId).toBe(vulnerability.cveId);\r\n\r\n      // Act & Assert - Update\r\n      vulnerability.markAsPatched();\r\n      await vulnerabilityRepository.save(vulnerability);\r\n      const updatedVuln = await vulnerabilityRepository.findById(vulnerability.id);\r\n      expect(updatedVuln!.isPatched()).toBe(true);\r\n\r\n      // Act & Assert - Delete\r\n      await vulnerabilityRepository.delete(vulnerability.id);\r\n      const deletedVuln = await vulnerabilityRepository.findById(vulnerability.id);\r\n      expect(deletedVuln).toBeNull();\r\n    });\r\n\r\n    it('should handle specialized query methods', async () => {\r\n      // Arrange\r\n      const port80 = Port.create(80);\r\n      const port443 = Port.create(443);\r\n\r\n      const vulnerabilities = [\r\n        VulnerabilityFactory.create({\r\n          cveId: 'CVE-2023-0001',\r\n          score: CvssScore.create(9.0),\r\n          severity: VulnerabilitySeverity.CRITICAL,\r\n          affectedPort: port80,\r\n          description: 'Critical HTTP vulnerability',\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        }),\r\n        VulnerabilityFactory.create({\r\n          cveId: 'CVE-2023-0002',\r\n          score: CvssScore.create(7.5),\r\n          severity: VulnerabilitySeverity.HIGH,\r\n          affectedPort: port443,\r\n          description: 'High HTTPS vulnerability',\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        }),\r\n        VulnerabilityFactory.create({\r\n          cveId: 'CVE-2023-0003',\r\n          score: CvssScore.create(5.0),\r\n          severity: VulnerabilitySeverity.MEDIUM,\r\n          affectedPort: port80,\r\n          description: 'Medium HTTP vulnerability',\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        }),\r\n      ];\r\n\r\n      for (const vuln of vulnerabilities) {\r\n        await vulnerabilityRepository.save(vuln);\r\n      }\r\n\r\n      // Act & Assert - FindBySeverity\r\n      const criticalVulns = await vulnerabilityRepository.findBySeverity(VulnerabilitySeverity.CRITICAL);\r\n      expect(criticalVulns).toHaveLength(1);\r\n\r\n      const highVulns = await vulnerabilityRepository.findBySeverity(VulnerabilitySeverity.HIGH);\r\n      expect(highVulns).toHaveLength(1);\r\n\r\n      // Act & Assert - FindByPort\r\n      const port80Vulns = await vulnerabilityRepository.findByPort(port80);\r\n      expect(port80Vulns).toHaveLength(2);\r\n\r\n      const port443Vulns = await vulnerabilityRepository.findByPort(port443);\r\n      expect(port443Vulns).toHaveLength(1);\r\n    });\r\n  });\r\n\r\n  describe('ResponseActionRepository Contract', () => {\r\n    it('should implement complete CRUD operations', async () => {\r\n      // Arrange\r\n      const action = ResponseActionFactory.create({\r\n        type: ActionType.BLOCK_IP,\r\n        parameters: { ipAddress: '*************', duration: 3600 },\r\n        priority: 1,\r\n        status: ActionStatus.PENDING,\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Act & Assert - Create\r\n      await expect(responseActionRepository.save(action)).resolves.not.toThrow();\r\n      expect(responseActionRepository.size()).toBe(1);\r\n\r\n      // Act & Assert - Read\r\n      const foundAction = await responseActionRepository.findById(action.id);\r\n      expect(foundAction).toBeDefined();\r\n      expect(foundAction!.id.equals(action.id)).toBe(true);\r\n      expect(foundAction!.type).toBe(action.type);\r\n\r\n      // Act & Assert - Update\r\n      action.execute();\r\n      await responseActionRepository.save(action);\r\n      const updatedAction = await responseActionRepository.findById(action.id);\r\n      expect(updatedAction!.status).toBe(ActionStatus.IN_PROGRESS);\r\n\r\n      // Act & Assert - Delete\r\n      await responseActionRepository.delete(action.id);\r\n      const deletedAction = await responseActionRepository.findById(action.id);\r\n      expect(deletedAction).toBeNull();\r\n    });\r\n\r\n    it('should handle specialized query methods', async () => {\r\n      // Arrange\r\n      const actions = [\r\n        ResponseActionFactory.create({\r\n          type: ActionType.BLOCK_IP,\r\n          parameters: { ipAddress: '********' },\r\n          priority: 1,\r\n          status: ActionStatus.PENDING,\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        }),\r\n        ResponseActionFactory.create({\r\n          type: ActionType.ISOLATE_HOST,\r\n          parameters: { hostId: 'host-001' },\r\n          priority: 2,\r\n          status: ActionStatus.IN_PROGRESS,\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        }),\r\n        ResponseActionFactory.create({\r\n          type: ActionType.BLOCK_IP,\r\n          parameters: { ipAddress: '********' },\r\n          priority: 3,\r\n          status: ActionStatus.COMPLETED,\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        }),\r\n      ];\r\n\r\n      for (const action of actions) {\r\n        await responseActionRepository.save(action);\r\n      }\r\n\r\n      // Act & Assert - FindByStatus\r\n      const pendingActions = await responseActionRepository.findByStatus(ActionStatus.PENDING);\r\n      expect(pendingActions).toHaveLength(1);\r\n\r\n      const inProgressActions = await responseActionRepository.findByStatus(ActionStatus.IN_PROGRESS);\r\n      expect(inProgressActions).toHaveLength(1);\r\n\r\n      const completedActions = await responseActionRepository.findByStatus(ActionStatus.COMPLETED);\r\n      expect(completedActions).toHaveLength(1);\r\n\r\n      // Act & Assert - FindByType\r\n      const blockIpActions = await responseActionRepository.findByType(ActionType.BLOCK_IP);\r\n      expect(blockIpActions).toHaveLength(2);\r\n\r\n      const isolateHostActions = await responseActionRepository.findByType(ActionType.ISOLATE_HOST);\r\n      expect(isolateHostActions).toHaveLength(1);\r\n    });\r\n  });\r\n\r\n  describe('Cross-Repository Integration', () => {\r\n    it('should maintain referential integrity across repositories', async () => {\r\n      // Arrange\r\n      const tenantId = TenantId.create();\r\n      const userId = UserId.create();\r\n      const sourceIp = IpAddress.create('*************');\r\n\r\n      // Create related entities\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'firewall', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.FIREWALL,\r\n          identifier: 'fw-001',\r\n          name: 'Firewall',\r\n        }),\r\n        type: EventType.NETWORK_INTRUSION,\r\n        severity: EventSeverity.HIGH,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { sourceIp: sourceIp.value },\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      const threat = ThreatFactory.create({\r\n        signature: 'intrusion-threat',\r\n        score: CvssScore.create(8.0),\r\n        severity: ThreatSeverity.HIGH,\r\n        confidence: 90,\r\n        sourceIp,\r\n        indicators: ['network-intrusion'],\r\n        mitreTechniques: ['T1190'],\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      const responseAction = ResponseActionFactory.create({\r\n        type: ActionType.BLOCK_IP,\r\n        parameters: { ipAddress: sourceIp.value, duration: 3600 },\r\n        priority: 1,\r\n        status: ActionStatus.PENDING,\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      // Act\r\n      await eventRepository.save(event);\r\n      await threatRepository.save(threat);\r\n      await responseActionRepository.save(responseAction);\r\n\r\n      // Assert - Verify all entities are saved\r\n      const savedEvent = await eventRepository.findById(event.id);\r\n      const savedThreat = await threatRepository.findById(threat.id);\r\n      const savedAction = await responseActionRepository.findById(responseAction.id);\r\n\r\n      expect(savedEvent).toBeDefined();\r\n      expect(savedThreat).toBeDefined();\r\n      expect(savedAction).toBeDefined();\r\n\r\n      // Verify relationships through queries\r\n      const threatsByIp = await threatRepository.findBySourceIp(sourceIp);\r\n      expect(threatsByIp).toHaveLength(1);\r\n      expect(threatsByIp[0].id.equals(threat.id)).toBe(true);\r\n\r\n      const blockIpActions = await responseActionRepository.findByType(ActionType.BLOCK_IP);\r\n      expect(blockIpActions).toHaveLength(1);\r\n      expect(blockIpActions[0].id.equals(responseAction.id)).toBe(true);\r\n    });\r\n\r\n    it('should handle cascading operations correctly', async () => {\r\n      // Arrange\r\n      const tenantId = TenantId.create();\r\n      const userId = UserId.create();\r\n\r\n      // Create multiple related events\r\n      const events = Array.from({ length: 5 }, (_, i) => \r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: `source-${i}`, version: '1.0' }),\r\n          timestamp: EventTimestamp.create(new Date(Date.now() + i * 1000)),\r\n          source: EventSource.create({\r\n            type: EventSourceType.APPLICATION,\r\n            identifier: `app-${i}`,\r\n            name: `App ${i}`,\r\n          }),\r\n          type: EventType.AUTHENTICATION_FAILURE,\r\n          severity: EventSeverity.MEDIUM,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { attempt: i },\r\n          tenantId,\r\n          userId,\r\n        })\r\n      );\r\n\r\n      // Act - Save all events\r\n      for (const event of events) {\r\n        await eventRepository.save(event);\r\n      }\r\n\r\n      // Assert - Verify batch operations\r\n      const allEvents = await eventRepository.findAll();\r\n      expect(allEvents).toHaveLength(5);\r\n\r\n      const mediumSeverityEvents = await eventRepository.findBySeverity(EventSeverity.MEDIUM);\r\n      expect(mediumSeverityEvents).toHaveLength(5);\r\n\r\n      // Test batch deletion\r\n      for (const event of events) {\r\n        await eventRepository.delete(event.id);\r\n      }\r\n\r\n      const remainingEvents = await eventRepository.findAll();\r\n      expect(remainingEvents).toHaveLength(0);\r\n    });\r\n  });\r\n});"], "version": 3}