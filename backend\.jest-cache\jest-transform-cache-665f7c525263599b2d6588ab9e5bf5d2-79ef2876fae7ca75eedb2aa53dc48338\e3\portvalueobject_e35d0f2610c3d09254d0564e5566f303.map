{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\port.value-object.ts", "mappings": ";;;AAAA,oGAA+F;AAE/F;;GAEG;AACH,IAAY,YAKX;AALD,WAAY,YAAY;IACtB,2BAAW,CAAA;IACX,2BAAW,CAAA;IACX,6BAAa,CAAA;IACb,6BAAa,CAAA;AACf,CAAC,EALW,YAAY,4BAAZ,YAAY,QAKvB;AAED;;GAEG;AACH,IAAY,SAIX;AAJD,WAAY,SAAS;IACnB,sCAAyB,CAAA;IACzB,sCAAyB,CAAA;IACzB,gCAAmB,CAAA;AACrB,CAAC,EAJW,SAAS,yBAAT,SAAS,QAIpB;AAED;;GAEG;AACH,IAAY,aAKX;AALD,WAAY,aAAa;IACvB,4BAAW,CAAA;IACX,kCAAiB,CAAA;IACjB,8BAAa,CAAA;IACb,sCAAqB,CAAA;AACvB,CAAC,EALW,aAAa,6BAAb,aAAa,QAKxB;AAYD;;;;;;;;;;;;;GAaG;AACH,MAAa,IAAK,SAAQ,mCAA0B;IAMlD,YAAY,KAAgB;QAC1B,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IAES,QAAQ;QAChB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC7E,MAAM,IAAI,KAAK,CAAC,+BAA+B,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,MAAc,EAAE,QAAsB;QAClD,OAAO,IAAI,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAG,CAAC,MAAc;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAG,CAAC,MAAc;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,UAAkB;QAClC,MAAM,KAAK,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC;QACrC,MAAM,QAAQ,GAAG,WAA2B,CAAC;QAC7C,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAErC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,qBAAqB,WAAW,EAAE,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK;QACH,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,YAAY,CAAC,GAAG,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,KAAK;QACH,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,YAAY,CAAC,GAAG,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC9C,OAAO,SAAS,CAAC,UAAU,CAAC;QAC9B,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACrD,OAAO,SAAS,CAAC,UAAU,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,OAAO,SAAS,CAAC,OAAO,CAAC;QAC3B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,iBAAiB,EAAE,KAAK,SAAS,CAAC,UAAU,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,iBAAiB,EAAE,KAAK,SAAS,CAAC,UAAU,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,iBAAiB,EAAE,KAAK,SAAS,CAAC,OAAO,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC7C,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QAC5D,OAAO,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,YAAY;QACV,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1C,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QAE5D,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACrC,OAAO,aAAa,CAAC,QAAQ,CAAC;QAChC,CAAC;aAAM,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACxC,OAAO,aAAa,CAAC,IAAI,CAAC;QAC5B,CAAC;aAAM,IAAI,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1C,OAAO,aAAa,CAAC,MAAM,CAAC;QAC9B,CAAC;QAED,OAAO,aAAa,CAAC,GAAG,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,UAAU;QACR,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACjC,OAAO,IAAI,KAAK,aAAa,CAAC,IAAI,IAAI,IAAI,KAAK,aAAa,CAAC,QAAQ,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,aAAa,GAAG;YACpB,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS;YACrE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;YAChE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;YACrE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW;YAC3D,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU;SAChE,CAAC;QAEF,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QAC5D,OAAO,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEtC,IAAI,SAAS,KAAK,aAAa,CAAC,QAAQ,EAAE,CAAC;YACzC,eAAe,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YACxE,eAAe,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACtB,eAAe,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YAC/D,eAAe,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YAChB,QAAQ,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC;gBAClC,KAAK,KAAK;oBACR,eAAe,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;oBACrD,eAAe,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBAC3C,eAAe,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;oBACxD,MAAM;gBACR,KAAK,QAAQ;oBACX,eAAe,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;oBAClE,MAAM;gBACR,KAAK,KAAK;oBACR,eAAe,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;oBACjD,MAAM;gBACR,KAAK,MAAM;oBACT,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;oBAC1C,MAAM;gBACR,KAAK,KAAK;oBACR,eAAe,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;oBAC5D,eAAe,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;oBAClD,MAAM;YACV,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC9B,eAAe,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YACjE,eAAe,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,WAAW;QACT,MAAM,cAAc,GAAG;YACrB,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;YAC/D,SAAS,EAAE,UAAU,EAAE,UAAU;SAClC,CAAC;QAEF,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QAC5D,OAAO,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,UAAU,CAAC;QAChD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI;YAAE,OAAO,mBAAmB,CAAC;QAC3D,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,KAAK;YAAE,OAAO,iBAAiB,CAAC;QAC1D,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,WAAW;QAUT,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YAC1B,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACxC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YAC9B,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;YAC/B,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7C,eAAe,EAAE,IAAI,CAAC,0BAA0B,EAAE;SACnD,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAY;QACxB,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,MAAM;YAC1C,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE;SACxB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAwB,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB;QACjC,OAAO;YACL,QAAQ,EAAE,UAAU;YACpB,QAAQ,EAAE,aAAa;YACvB,QAAQ,EAAE,KAAK;YACf,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE,MAAM;YAChB,QAAQ,EAAE,KAAK;YACf,QAAQ,EAAE,MAAM;YAChB,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,OAAO;YAClB,SAAS,EAAE,OAAO;YAClB,SAAS,EAAE,iBAAiB;YAC5B,SAAS,EAAE,OAAO;YAClB,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,KAAK;YACf,QAAQ,EAAE,aAAa;YACvB,QAAQ,EAAE,aAAa;YACvB,QAAQ,EAAE,MAAM;YAChB,SAAS,EAAE,KAAK;YAChB,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,WAAW;YACtB,SAAS,EAAE,qBAAqB;YAChC,SAAS,EAAE,iBAAiB;YAC5B,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE,YAAY;YACxB,UAAU,EAAE,QAAQ;YACpB,UAAU,EAAE,OAAO;YACnB,UAAU,EAAE,KAAK;YACjB,UAAU,EAAE,YAAY;YACxB,UAAU,EAAE,KAAK;YACjB,UAAU,EAAE,OAAO;YACnB,UAAU,EAAE,gBAAgB;YAC5B,WAAW,EAAE,SAAS;SACvB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB;QAK7B,OAAO;YACL,QAAQ,EAAE;gBACR,QAAQ,EAAK,uBAAuB;gBACpC,QAAQ,EAAK,oBAAoB;gBACjC,SAAS,EAAI,mBAAmB;gBAChC,SAAS,EAAI,uBAAuB;gBACpC,SAAS,EAAI,4BAA4B;aAC1C;YACD,IAAI,EAAE;gBACJ,QAAQ,EAAK,0BAA0B;gBACvC,UAAU,EAAG,0BAA0B;gBACvC,UAAU,EAAG,aAAa;gBAC1B,UAAU,EAAG,QAAQ;gBACrB,UAAU,EAAG,aAAa;gBAC1B,UAAU,EAAG,0BAA0B;gBACvC,WAAW,EAAE,4BAA4B;gBACzC,SAAS,EAAI,gCAAgC;aAC9C;YACD,MAAM,EAAE;gBACN,QAAQ,EAAK,OAAO;gBACpB,QAAQ,EAAK,qBAAqB;gBAClC,SAAS,EAAI,qBAAqB;gBAClC,SAAS,EAAI,qBAAqB;gBAClC,UAAU,EAAG,iBAAiB;gBAC9B,QAAQ,EAAK,qBAAqB;gBAClC,SAAS,EAAI,8BAA8B;aAC5C;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,MAAc,EAAE,QAAsB;QACnD,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,KAAa;QACxB,IAAI,CAAC;YACH,6BAA6B;YAC7B,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAChC,CAAC;YAED,oCAAoC;YACpC,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACnC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC1B,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;;AAjbH,oBAkbC;AAjbyB,aAAQ,GAAG,CAAC,CAAC;AACb,aAAQ,GAAG,KAAK,CAAC;AACjB,mBAAc,GAAG,IAAI,CAAC;AACtB,mBAAc,GAAG,KAAK,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\port.value-object.ts"], "sourcesContent": ["import { BaseValueObject } from '../../../../../shared-kernel/value-objects/base-value-object';\r\n\r\n/**\r\n * Port Protocol Type\r\n */\r\nexport enum PortProtocol {\r\n  TCP = 'tcp',\r\n  UDP = 'udp',\r\n  SCTP = 'sctp',\r\n  DCCP = 'dccp',\r\n}\r\n\r\n/**\r\n * Port Classification\r\n */\r\nexport enum PortClass {\r\n  WELL_KNOWN = 'well_known',      // 0-1023\r\n  REGISTERED = 'registered',      // 1024-49151\r\n  DYNAMIC = 'dynamic',            // 49152-65535\r\n}\r\n\r\n/**\r\n * Port Security Risk Level\r\n */\r\nexport enum PortRiskLevel {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CRITICAL = 'critical',\r\n}\r\n\r\n/**\r\n * Port Value Object Properties\r\n */\r\nexport interface PortProps {\r\n  /** Port number (0-65535) */\r\n  number: number;\r\n  /** Protocol type */\r\n  protocol: PortProtocol;\r\n}\r\n\r\n/**\r\n * Port Value Object\r\n * \r\n * Represents a network port with protocol information and security analysis.\r\n * Provides classification, risk assessment, and service identification capabilities.\r\n * \r\n * Key features:\r\n * - Port number validation (0-65535)\r\n * - Protocol support (TCP, UDP, SCTP, DCCP)\r\n * - Port classification (well-known, registered, dynamic)\r\n * - Security risk assessment\r\n * - Common service identification\r\n * - Vulnerability analysis support\r\n */\r\nexport class Port extends BaseValueObject<PortProps> {\r\n  private static readonly MIN_PORT = 0;\r\n  private static readonly MAX_PORT = 65535;\r\n  private static readonly WELL_KNOWN_MAX = 1023;\r\n  private static readonly REGISTERED_MAX = 49151;\r\n\r\n  constructor(props: PortProps) {\r\n    super(props);\r\n  }\r\n\r\n  protected validate(): void {\r\n    if (!Number.isInteger(this._value.number)) {\r\n      throw new Error('Port number must be an integer');\r\n    }\r\n\r\n    if (this._value.number < Port.MIN_PORT || this._value.number > Port.MAX_PORT) {\r\n      throw new Error(`Port number must be between ${Port.MIN_PORT} and ${Port.MAX_PORT}`);\r\n    }\r\n\r\n    if (!Object.values(PortProtocol).includes(this._value.protocol)) {\r\n      throw new Error(`Invalid protocol: ${this._value.protocol}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a port from number and protocol\r\n   */\r\n  static create(number: number, protocol: PortProtocol): Port {\r\n    return new Port({ number, protocol });\r\n  }\r\n\r\n  /**\r\n   * Create a TCP port\r\n   */\r\n  static tcp(number: number): Port {\r\n    return Port.create(number, PortProtocol.TCP);\r\n  }\r\n\r\n  /**\r\n   * Create a UDP port\r\n   */\r\n  static udp(number: number): Port {\r\n    return Port.create(number, PortProtocol.UDP);\r\n  }\r\n\r\n  /**\r\n   * Create from string format \"protocol/port\" (e.g., \"tcp/80\")\r\n   */\r\n  static fromString(portString: string): Port {\r\n    const parts = portString.toLowerCase().split('/');\r\n    if (parts.length !== 2) {\r\n      throw new Error('Port string must be in format \"protocol/port\"');\r\n    }\r\n\r\n    const [protocolStr, portStr] = parts;\r\n    const protocol = protocolStr as PortProtocol;\r\n    const number = parseInt(portStr, 10);\r\n\r\n    if (!Object.values(PortProtocol).includes(protocol)) {\r\n      throw new Error(`Invalid protocol: ${protocolStr}`);\r\n    }\r\n\r\n    return Port.create(number, protocol);\r\n  }\r\n\r\n  /**\r\n   * Get port number\r\n   */\r\n  get number(): number {\r\n    return this._value.number;\r\n  }\r\n\r\n  /**\r\n   * Get protocol\r\n   */\r\n  get protocol(): PortProtocol {\r\n    return this._value.protocol;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a TCP port\r\n   */\r\n  isTCP(): boolean {\r\n    return this._value.protocol === PortProtocol.TCP;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a UDP port\r\n   */\r\n  isUDP(): boolean {\r\n    return this._value.protocol === PortProtocol.UDP;\r\n  }\r\n\r\n  /**\r\n   * Get port classification\r\n   */\r\n  getClassification(): PortClass {\r\n    if (this._value.number <= Port.WELL_KNOWN_MAX) {\r\n      return PortClass.WELL_KNOWN;\r\n    } else if (this._value.number <= Port.REGISTERED_MAX) {\r\n      return PortClass.REGISTERED;\r\n    } else {\r\n      return PortClass.DYNAMIC;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if this is a well-known port\r\n   */\r\n  isWellKnown(): boolean {\r\n    return this.getClassification() === PortClass.WELL_KNOWN;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a registered port\r\n   */\r\n  isRegistered(): boolean {\r\n    return this.getClassification() === PortClass.REGISTERED;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a dynamic/ephemeral port\r\n   */\r\n  isDynamic(): boolean {\r\n    return this.getClassification() === PortClass.DYNAMIC;\r\n  }\r\n\r\n  /**\r\n   * Get common service name for well-known ports\r\n   */\r\n  getServiceName(): string | null {\r\n    const services = Port.getWellKnownServices();\r\n    const key = `${this._value.protocol}/${this._value.number}`;\r\n    return services[key] || null;\r\n  }\r\n\r\n  /**\r\n   * Get security risk level\r\n   */\r\n  getRiskLevel(): PortRiskLevel {\r\n    const riskPorts = Port.getHighRiskPorts();\r\n    const key = `${this._value.protocol}/${this._value.number}`;\r\n    \r\n    if (riskPorts.critical.includes(key)) {\r\n      return PortRiskLevel.CRITICAL;\r\n    } else if (riskPorts.high.includes(key)) {\r\n      return PortRiskLevel.HIGH;\r\n    } else if (riskPorts.medium.includes(key)) {\r\n      return PortRiskLevel.MEDIUM;\r\n    }\r\n    \r\n    return PortRiskLevel.LOW;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a high-risk port\r\n   */\r\n  isHighRisk(): boolean {\r\n    const risk = this.getRiskLevel();\r\n    return risk === PortRiskLevel.HIGH || risk === PortRiskLevel.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a commonly attacked port\r\n   */\r\n  isCommonlyAttacked(): boolean {\r\n    const commonTargets = [\r\n      'tcp/21', 'tcp/22', 'tcp/23', 'tcp/25', 'tcp/53', 'tcp/80', 'tcp/110',\r\n      'tcp/135', 'tcp/139', 'tcp/143', 'tcp/443', 'tcp/445', 'tcp/993',\r\n      'tcp/995', 'tcp/1433', 'tcp/1521', 'tcp/3306', 'tcp/3389', 'tcp/5432',\r\n      'tcp/5900', 'tcp/6379', 'tcp/8080', 'tcp/8443', 'tcp/27017',\r\n      'udp/53', 'udp/69', 'udp/123', 'udp/161', 'udp/162', 'udp/1900'\r\n    ];\r\n    \r\n    const key = `${this._value.protocol}/${this._value.number}`;\r\n    return commonTargets.includes(key);\r\n  }\r\n\r\n  /**\r\n   * Check if this port should be monitored for security\r\n   */\r\n  shouldMonitor(): boolean {\r\n    return this.isHighRisk() || this.isCommonlyAttacked() || this.isWellKnown();\r\n  }\r\n\r\n  /**\r\n   * Get security recommendations for this port\r\n   */\r\n  getSecurityRecommendations(): string[] {\r\n    const recommendations: string[] = [];\r\n    const serviceName = this.getServiceName();\r\n    const riskLevel = this.getRiskLevel();\r\n\r\n    if (riskLevel === PortRiskLevel.CRITICAL) {\r\n      recommendations.push('Consider disabling this service if not required');\r\n      recommendations.push('Implement strict access controls and monitoring');\r\n    }\r\n\r\n    if (this.isHighRisk()) {\r\n      recommendations.push('Enable detailed logging and monitoring');\r\n      recommendations.push('Implement rate limiting and intrusion detection');\r\n    }\r\n\r\n    if (serviceName) {\r\n      switch (serviceName.toLowerCase()) {\r\n        case 'ssh':\r\n          recommendations.push('Use key-based authentication');\r\n          recommendations.push('Disable root login');\r\n          recommendations.push('Change default port if possible');\r\n          break;\r\n        case 'telnet':\r\n          recommendations.push('Replace with SSH for secure remote access');\r\n          break;\r\n        case 'ftp':\r\n          recommendations.push('Use SFTP or FTPS instead');\r\n          break;\r\n        case 'http':\r\n          recommendations.push('Redirect to HTTPS');\r\n          break;\r\n        case 'rdp':\r\n          recommendations.push('Enable Network Level Authentication');\r\n          recommendations.push('Use VPN for remote access');\r\n          break;\r\n      }\r\n    }\r\n\r\n    if (this.isCommonlyAttacked()) {\r\n      recommendations.push('Implement fail2ban or similar protection');\r\n      recommendations.push('Use non-standard ports when possible');\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Check if port is typically encrypted\r\n   */\r\n  isEncrypted(): boolean {\r\n    const encryptedPorts = [\r\n      'tcp/22', 'tcp/443', 'tcp/465', 'tcp/587', 'tcp/636', 'tcp/993',\r\n      'tcp/995', 'tcp/8443', 'tcp/9443'\r\n    ];\r\n    \r\n    const key = `${this._value.protocol}/${this._value.number}`;\r\n    return encryptedPorts.includes(key);\r\n  }\r\n\r\n  /**\r\n   * Get port range category\r\n   */\r\n  getRangeCategory(): string {\r\n    if (this._value.number === 0) return 'Reserved';\r\n    if (this._value.number <= 1023) return 'System/Well-Known';\r\n    if (this._value.number <= 49151) return 'User/Registered';\r\n    return 'Dynamic/Private';\r\n  }\r\n\r\n  /**\r\n   * Convert to string representation\r\n   */\r\n  public toString(): string {\r\n    return `${this._value.protocol}/${this._value.number}`;\r\n  }\r\n\r\n  /**\r\n   * Get detailed port information\r\n   */\r\n  getPortInfo(): {\r\n    number: number;\r\n    protocol: string;\r\n    classification: string;\r\n    serviceName: string | null;\r\n    riskLevel: string;\r\n    isEncrypted: boolean;\r\n    isCommonlyAttacked: boolean;\r\n    recommendations: string[];\r\n  } {\r\n    return {\r\n      number: this._value.number,\r\n      protocol: this._value.protocol,\r\n      classification: this.getClassification(),\r\n      serviceName: this.getServiceName(),\r\n      riskLevel: this.getRiskLevel(),\r\n      isEncrypted: this.isEncrypted(),\r\n      isCommonlyAttacked: this.isCommonlyAttacked(),\r\n      recommendations: this.getSecurityRecommendations(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Compare ports for equality\r\n   */\r\n  public equals(other?: Port): boolean {\r\n    if (!other) {\r\n      return false;\r\n    }\r\n\r\n    if (this === other) {\r\n      return true;\r\n    }\r\n\r\n    return this._value.number === other._value.number && \r\n           this._value.protocol === other._value.protocol;\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...this.getPortInfo(),\r\n      string: this.toString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create Port from JSON\r\n   */\r\n  static fromJSON(json: Record<string, any>): Port {\r\n    return Port.create(json.number, json.protocol as PortProtocol);\r\n  }\r\n\r\n  /**\r\n   * Get well-known services mapping\r\n   */\r\n  private static getWellKnownServices(): Record<string, string> {\r\n    return {\r\n      'tcp/20': 'FTP Data',\r\n      'tcp/21': 'FTP Control',\r\n      'tcp/22': 'SSH',\r\n      'tcp/23': 'Telnet',\r\n      'tcp/25': 'SMTP',\r\n      'tcp/53': 'DNS',\r\n      'tcp/80': 'HTTP',\r\n      'tcp/110': 'POP3',\r\n      'tcp/143': 'IMAP',\r\n      'tcp/443': 'HTTPS',\r\n      'tcp/465': 'SMTPS',\r\n      'tcp/587': 'SMTP Submission',\r\n      'tcp/993': 'IMAPS',\r\n      'tcp/995': 'POP3S',\r\n      'udp/53': 'DNS',\r\n      'udp/67': 'DHCP Server',\r\n      'udp/68': 'DHCP Client',\r\n      'udp/69': 'TFTP',\r\n      'udp/123': 'NTP',\r\n      'udp/161': 'SNMP',\r\n      'udp/162': 'SNMP Trap',\r\n      'tcp/135': 'RPC Endpoint Mapper',\r\n      'tcp/139': 'NetBIOS Session',\r\n      'tcp/445': 'SMB',\r\n      'tcp/1433': 'SQL Server',\r\n      'tcp/1521': 'Oracle',\r\n      'tcp/3306': 'MySQL',\r\n      'tcp/3389': 'RDP',\r\n      'tcp/5432': 'PostgreSQL',\r\n      'tcp/5900': 'VNC',\r\n      'tcp/6379': 'Redis',\r\n      'tcp/8080': 'HTTP Alternate',\r\n      'tcp/27017': 'MongoDB',\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get high-risk ports categorized by risk level\r\n   */\r\n  private static getHighRiskPorts(): {\r\n    critical: string[];\r\n    high: string[];\r\n    medium: string[];\r\n  } {\r\n    return {\r\n      critical: [\r\n        'tcp/23',    // Telnet (unencrypted)\r\n        'tcp/21',    // FTP (unencrypted)\r\n        'tcp/135',   // RPC (vulnerable)\r\n        'tcp/139',   // NetBIOS (vulnerable)\r\n        'tcp/445',   // SMB (frequently attacked)\r\n      ],\r\n      high: [\r\n        'tcp/22',    // SSH (commonly attacked)\r\n        'tcp/3389',  // RDP (commonly attacked)\r\n        'tcp/1433',  // SQL Server\r\n        'tcp/3306',  // MySQL\r\n        'tcp/5432',  // PostgreSQL\r\n        'tcp/6379',  // Redis (often unsecured)\r\n        'tcp/27017', // MongoDB (often unsecured)\r\n        'udp/161',   // SNMP (information disclosure)\r\n      ],\r\n      medium: [\r\n        'tcp/25',    // SMTP\r\n        'tcp/80',    // HTTP (unencrypted)\r\n        'tcp/110',   // POP3 (unencrypted)\r\n        'tcp/143',   // IMAP (unencrypted)\r\n        'tcp/8080',  // HTTP Alternate\r\n        'udp/69',    // TFTP (unencrypted)\r\n        'udp/123',   // NTP (amplification attacks)\r\n      ],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Validate port format without creating instance\r\n   */\r\n  static isValid(number: number, protocol: PortProtocol): boolean {\r\n    try {\r\n      new Port({ number, protocol });\r\n      return true;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Parse port from various string formats\r\n   */\r\n  static parse(input: string): Port | null {\r\n    try {\r\n      // Try \"protocol/port\" format\r\n      if (input.includes('/')) {\r\n        return Port.fromString(input);\r\n      }\r\n\r\n      // Try just port number (assume TCP)\r\n      const number = parseInt(input, 10);\r\n      if (!isNaN(number)) {\r\n        return Port.tcp(number);\r\n      }\r\n\r\n      return null;\r\n    } catch {\r\n      return null;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}