{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\__tests__\\cvss-score.value-object.spec.ts", "mappings": ";;AAAA,wEAAkF;AAElF,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,gBAAgB;YAChB,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAChC,GAAG,EACH,qCAAW,CAAC,IAAI,EAChB,8CAA8C,CAC/C,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qCAAW,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YACp<PERSON>,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,gBAAgB;YAChB,MAAM,SAAS,GAAG,mCAAS,CAAC,UAAU,CACpC,GAAG,EACH,8CAA8C,CAC/C,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qCAAW,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,gBAAgB;YAChB,MAAM,SAAS,GAAG,mCAAS,CAAC,UAAU,CACpC,GAAG,EACH,iEAAiE,CAClE,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qCAAW,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,gBAAgB;YAChB,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAChC,GAAG,EACH,qCAAW,CAAC,IAAI,EAChB,8CAA8C,EAC9C;gBACE,aAAa,EAAE,GAAG;gBAClB,kBAAkB,EAAE,GAAG;gBACvB,mBAAmB,EAAE,GAAG;gBACxB,WAAW,EAAE,GAAG;gBAChB,MAAM,EAAE,KAAK;aACd,CACF,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;iBAClG,GAAG,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,IAAI,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;iBACnG,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,iBAAiB;YACjB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;iBACnG,OAAO,CAAC,0CAA0C,CAAC,CAAC;YACvD,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,IAAI,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;iBACnG,OAAO,CAAC,0CAA0C,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,uBAAuB;YACvB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,EAAE;gBACnG,aAAa,EAAE,GAAG;aACnB,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAElB,yBAAyB;YACzB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,EAAE;gBACnG,aAAa,EAAE,IAAI;aACpB,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,4BAA4B;YAC5B,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,EAAE;gBACnG,kBAAkB,EAAE,GAAG;aACxB,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAElB,8BAA8B;YAC9B,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,EAAE;gBACnG,kBAAkB,EAAE,CAAC,GAAG;aACzB,CAAC,CAAC,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,iBAAiB;YACjB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,EAAE,EAAE,8BAA8B,CAAC,CAAC;iBAChF,GAAG,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;iBAClG,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,kBAAkB;YAClB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,SAAwB,EAAE,8CAA8C,CAAC,CAAC;iBAC1G,OAAO,CAAC,+BAA+B,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;iBACtD,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,kBAAkB;YAClB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,EAAE,EAAE,8BAA8B,CAAC,CAAC;iBAChF,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,oBAAoB;YACpB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC;iBAClE,OAAO,CAAC,sCAAsC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,oBAAoB;YACpB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;iBAClG,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,oBAAoB;YACpB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;iBAClG,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,oBAAoB;YACpB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;iBACpE,OAAO,CAAC,sCAAsC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,oBAAoB;YACpB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,iEAAiE,CAAC,CAAC;iBACrH,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,oBAAoB;YACpB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;iBACpE,OAAO,CAAC,sCAAsC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,kBAAkB;YAClB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,EAAE;gBACnG,mBAAmB,EAAE,GAAG;gBACxB,WAAW,EAAE,GAAG;aACjB,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAElB,+BAA+B;YAC/B,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,EAAE;gBACnG,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC,CAAC,OAAO,CAAC,oDAAoD,CAAC,CAAC;YAElE,uBAAuB;YACvB,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,EAAE;gBACnG,WAAW,EAAE,CAAC,GAAG;aAClB,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC1G,MAAM,QAAQ,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YACzG,MAAM,WAAW,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC5G,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC1G,MAAM,aAAa,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAE9G,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,sCAAY,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,sCAAY,CAAC,GAAG,CAAC,CAAC;YACtD,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,sCAAY,CAAC,MAAM,CAAC,CAAC;YAC5D,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,sCAAY,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,sCAAY,CAAC,QAAQ,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,aAAa,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC9G,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAE1G,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,aAAa,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC9G,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC1G,MAAM,WAAW,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAE5G,MAAM,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,WAAW,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC5G,MAAM,WAAW,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAE5G,MAAM,CAAC,WAAW,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,WAAW,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,EAAE;gBACxG,aAAa,EAAE,GAAG;gBAClB,kBAAkB,EAAE,GAAG;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,EAAE;gBACxG,aAAa,EAAE,GAAG;aACnB,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAE1G,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,aAAa,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC9G,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC1G,MAAM,WAAW,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC5G,MAAM,QAAQ,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAEzG,MAAM,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtD,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC1G,MAAM,QAAQ,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAEzG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,aAAa,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC9G,MAAM,SAAS,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC1G,MAAM,WAAW,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAE5G,MAAM,gBAAgB,GAAG,aAAa,CAAC,sBAAsB,EAAE,CAAC;YAChE,MAAM,YAAY,GAAG,SAAS,CAAC,sBAAsB,EAAE,CAAC;YACxD,MAAM,cAAc,GAAG,WAAW,CAAC,sBAAsB,EAAE,CAAC;YAE5D,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,aAAa,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC9G,MAAM,QAAQ,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAEzG,MAAM,kBAAkB,GAAG,aAAa,CAAC,yBAAyB,EAAE,CAAC;YACrE,MAAM,aAAa,GAAG,QAAQ,CAAC,yBAAyB,EAAE,CAAC;YAE3D,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,aAAa,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC9G,MAAM,YAAY,GAAG,aAAa,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YAE1D,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,aAAa,EAAE,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,aAAa,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC9G,MAAM,YAAY,GAAG,aAAa,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;YAE/D,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,aAAa,EAAE,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,aAAa,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC9G,MAAM,YAAY,GAAG,aAAa,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE3D,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnD,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,aAAa,EAAE,CAAC;YAC1D,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,MAAM,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YACvG,MAAM,MAAM,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YACvG,MAAM,MAAM,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YACvG,MAAM,MAAM,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAEvG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,uBAAuB;YAClE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,oBAAoB;YAC/D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,KAAK,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YACtG,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,KAAK,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,EAAE;gBACpG,aAAa,EAAE,GAAG;gBAClB,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAE5B,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qCAAW,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAC/E,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,aAAa,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC;YAC9G,MAAM,IAAI,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,iBAAiB,GAAG,mCAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAClE,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAC9D,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,CAAC,mCAAS,CAAC,OAAO,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5G,MAAM,CAAC,mCAAS,CAAC,OAAO,CAAC,IAAI,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9G,MAAM,CAAC,mCAAS,CAAC,OAAO,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,KAAK,GAAG,mCAAS,CAAC,MAAM,CAAC,GAAG,EAAE,qCAAW,CAAC,IAAI,EAAE,8CAA8C,EAAE;gBACpG,aAAa,EAAE,GAAG;gBAClB,kBAAkB,EAAE,GAAG;aACxB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;YAExC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,sCAAY,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,YAAY,GAAG,8CAA8C,CAAC;YACpE,MAAM,KAAK,GAAG,mCAAS,CAAC,gBAAgB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;YAEtE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qCAAW,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,sEAAsE;YACtE,4CAA4C;YAC5C,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,gBAAgB,CAAC,8CAA8C,CAAC,CAAC;iBACrF,GAAG,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\__tests__\\cvss-score.value-object.spec.ts"], "sourcesContent": ["import { CVSSScore, CVSSVersion, CVSSSeverity } from '../cvss-score.value-object';\r\n\r\ndescribe('CVSSScore Value Object', () => {\r\n  describe('creation', () => {\r\n    it('should create valid CVSS score with required properties', () => {\r\n      // Arrange & Act\r\n      const cvssScore = CVSSScore.create(\r\n        7.5,\r\n        CVSSVersion.V3_1,\r\n        'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'\r\n      );\r\n\r\n      // Assert\r\n      expect(cvssScore.baseScore).toBe(7.5);\r\n      expect(cvssScore.version).toBe(CVSSVersion.V3_1);\r\n      expect(cvssScore.vectorString).toBe('CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      expect(cvssScore.calculatedAt).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should create CVSS v3.1 score using factory method', () => {\r\n      // Arrange & Act\r\n      const cvssScore = CVSSScore.createV3_1(\r\n        8.8,\r\n        'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H'\r\n      );\r\n\r\n      // Assert\r\n      expect(cvssScore.baseScore).toBe(8.8);\r\n      expect(cvssScore.version).toBe(CVSSVersion.V3_1);\r\n    });\r\n\r\n    it('should create CVSS v4.0 score using factory method', () => {\r\n      // Arrange & Act\r\n      const cvssScore = CVSSScore.createV4_0(\r\n        9.1,\r\n        'CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N'\r\n      );\r\n\r\n      // Assert\r\n      expect(cvssScore.baseScore).toBe(9.1);\r\n      expect(cvssScore.version).toBe(CVSSVersion.V4_0);\r\n    });\r\n\r\n    it('should create CVSS score with optional properties', () => {\r\n      // Arrange & Act\r\n      const cvssScore = CVSSScore.create(\r\n        6.5,\r\n        CVSSVersion.V3_1,\r\n        'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N',\r\n        {\r\n          temporalScore: 6.0,\r\n          environmentalScore: 5.8,\r\n          exploitabilityScore: 3.9,\r\n          impactScore: 3.6,\r\n          source: 'NVD',\r\n        }\r\n      );\r\n\r\n      // Assert\r\n      expect(cvssScore.temporalScore).toBe(6.0);\r\n      expect(cvssScore.environmentalScore).toBe(5.8);\r\n      expect(cvssScore.exploitabilityScore).toBe(3.9);\r\n      expect(cvssScore.impactScore).toBe(3.6);\r\n      expect(cvssScore.source).toBe('NVD');\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should validate base score range', () => {\r\n      // Valid scores\r\n      expect(() => CVSSScore.create(0.0, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N'))\r\n        .not.toThrow();\r\n      expect(() => CVSSScore.create(10.0, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))\r\n        .not.toThrow();\r\n\r\n      // Invalid scores\r\n      expect(() => CVSSScore.create(-0.1, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N'))\r\n        .toThrow('CVSS base score must be between 0 and 10');\r\n      expect(() => CVSSScore.create(10.1, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))\r\n        .toThrow('CVSS base score must be between 0 and 10');\r\n    });\r\n\r\n    it('should validate temporal score range', () => {\r\n      // Valid temporal score\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {\r\n        temporalScore: 7.0,\r\n      })).not.toThrow();\r\n\r\n      // Invalid temporal score\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {\r\n        temporalScore: 10.5,\r\n      })).toThrow('CVSS temporal score must be between 0 and 10');\r\n    });\r\n\r\n    it('should validate environmental score range', () => {\r\n      // Valid environmental score\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {\r\n        environmentalScore: 6.8,\r\n      })).not.toThrow();\r\n\r\n      // Invalid environmental score\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {\r\n        environmentalScore: -1.0,\r\n      })).toThrow('CVSS environmental score must be between 0 and 10');\r\n    });\r\n\r\n    it('should validate CVSS version', () => {\r\n      // Valid versions\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V2, '(AV:N/AC:M/Au:N/C:P/I:P/A:P)'))\r\n        .not.toThrow();\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V3_0, 'CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))\r\n        .not.toThrow();\r\n\r\n      // Invalid version\r\n      expect(() => CVSSScore.create(7.5, 'invalid' as CVSSVersion, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))\r\n        .toThrow('Invalid CVSS version: invalid');\r\n    });\r\n\r\n    it('should require vector string', () => {\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V3_1, ''))\r\n        .toThrow('CVSS vector string is required');\r\n    });\r\n\r\n    it('should validate CVSS v2 vector string format', () => {\r\n      // Valid v2 vector\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V2, '(AV:N/AC:M/Au:N/C:P/I:P/A:P)'))\r\n        .not.toThrow();\r\n\r\n      // Invalid v2 vector\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V2, 'invalid-vector'))\r\n        .toThrow('Invalid CVSS v2 vector string format');\r\n    });\r\n\r\n    it('should validate CVSS v3 vector string format', () => {\r\n      // Valid v3.1 vector\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))\r\n        .not.toThrow();\r\n\r\n      // Valid v3.0 vector\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V3_0, 'CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))\r\n        .not.toThrow();\r\n\r\n      // Invalid v3 vector\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V3_1, 'invalid-vector'))\r\n        .toThrow('Invalid CVSS v3 vector string format');\r\n    });\r\n\r\n    it('should validate CVSS v4 vector string format', () => {\r\n      // Valid v4.0 vector\r\n      expect(() => CVSSScore.create(9.1, CVSSVersion.V4_0, 'CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N'))\r\n        .not.toThrow();\r\n\r\n      // Invalid v4 vector\r\n      expect(() => CVSSScore.create(9.1, CVSSVersion.V4_0, 'invalid-vector'))\r\n        .toThrow('Invalid CVSS v4 vector string format');\r\n    });\r\n\r\n    it('should validate subscore ranges', () => {\r\n      // Valid subscores\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {\r\n        exploitabilityScore: 3.9,\r\n        impactScore: 5.9,\r\n      })).not.toThrow();\r\n\r\n      // Invalid exploitability score\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {\r\n        exploitabilityScore: 11.0,\r\n      })).toThrow('CVSS exploitability score must be between 0 and 10');\r\n\r\n      // Invalid impact score\r\n      expect(() => CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {\r\n        impactScore: -1.0,\r\n      })).toThrow('CVSS impact score must be between 0 and 10');\r\n    });\r\n  });\r\n\r\n  describe('severity classification', () => {\r\n    it('should classify severity correctly', () => {\r\n      const noneScore = CVSSScore.create(0.0, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N');\r\n      const lowScore = CVSSScore.create(2.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:L/AC:H/PR:H/UI:R/S:U/C:L/I:N/A:N');\r\n      const mediumScore = CVSSScore.create(5.0, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N');\r\n      const highScore = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N');\r\n      const criticalScore = CVSSScore.create(9.8, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n\r\n      expect(noneScore.getSeverity()).toBe(CVSSSeverity.NONE);\r\n      expect(lowScore.getSeverity()).toBe(CVSSSeverity.LOW);\r\n      expect(mediumScore.getSeverity()).toBe(CVSSSeverity.MEDIUM);\r\n      expect(highScore.getSeverity()).toBe(CVSSSeverity.HIGH);\r\n      expect(criticalScore.getSeverity()).toBe(CVSSSeverity.CRITICAL);\r\n    });\r\n\r\n    it('should identify critical scores', () => {\r\n      const criticalScore = CVSSScore.create(9.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      const highScore = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N');\r\n\r\n      expect(criticalScore.isCritical()).toBe(true);\r\n      expect(highScore.isCritical()).toBe(false);\r\n    });\r\n\r\n    it('should identify high or critical scores', () => {\r\n      const criticalScore = CVSSScore.create(9.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      const highScore = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N');\r\n      const mediumScore = CVSSScore.create(5.0, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N');\r\n\r\n      expect(criticalScore.isHighOrCritical()).toBe(true);\r\n      expect(highScore.isHighOrCritical()).toBe(true);\r\n      expect(mediumScore.isHighOrCritical()).toBe(false);\r\n    });\r\n\r\n    it('should identify scores requiring immediate attention', () => {\r\n      const urgentScore = CVSSScore.create(8.0, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N');\r\n      const normalScore = CVSSScore.create(6.0, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N');\r\n\r\n      expect(urgentScore.requiresImmediateAttention()).toBe(true);\r\n      expect(normalScore.requiresImmediateAttention()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('effective score calculation', () => {\r\n    it('should use environmental score when available', () => {\r\n      const cvssScore = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {\r\n        temporalScore: 7.0,\r\n        environmentalScore: 6.5,\r\n      });\r\n\r\n      expect(cvssScore.getEffectiveScore()).toBe(6.5);\r\n    });\r\n\r\n    it('should use temporal score when environmental is not available', () => {\r\n      const cvssScore = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {\r\n        temporalScore: 7.0,\r\n      });\r\n\r\n      expect(cvssScore.getEffectiveScore()).toBe(7.0);\r\n    });\r\n\r\n    it('should use base score when others are not available', () => {\r\n      const cvssScore = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n\r\n      expect(cvssScore.getEffectiveScore()).toBe(7.5);\r\n    });\r\n  });\r\n\r\n  describe('risk assessment', () => {\r\n    it('should provide risk level assessment', () => {\r\n      const criticalScore = CVSSScore.create(9.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      const highScore = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N');\r\n      const mediumScore = CVSSScore.create(5.0, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N');\r\n      const lowScore = CVSSScore.create(2.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:L/AC:H/PR:H/UI:R/S:U/C:L/I:N/A:N');\r\n\r\n      expect(criticalScore.getRiskLevel()).toBe('critical');\r\n      expect(highScore.getRiskLevel()).toBe('high');\r\n      expect(mediumScore.getRiskLevel()).toBe('medium');\r\n      expect(lowScore.getRiskLevel()).toBe('low');\r\n    });\r\n\r\n    it('should provide priority score for remediation', () => {\r\n      const highScore = CVSSScore.create(7.8, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N');\r\n      const lowScore = CVSSScore.create(2.3, CVSSVersion.V3_1, 'CVSS:3.1/AV:L/AC:H/PR:H/UI:R/S:U/C:L/I:N/A:N');\r\n\r\n      expect(highScore.getPriorityScore()).toBe(8);\r\n      expect(lowScore.getPriorityScore()).toBe(3);\r\n    });\r\n\r\n    it('should provide remediation timeline', () => {\r\n      const criticalScore = CVSSScore.create(9.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      const highScore = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N');\r\n      const mediumScore = CVSSScore.create(5.0, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N');\r\n\r\n      const criticalTimeline = criticalScore.getRemediationTimeline();\r\n      const highTimeline = highScore.getRemediationTimeline();\r\n      const mediumTimeline = mediumScore.getRemediationTimeline();\r\n\r\n      expect(criticalTimeline.immediate).toBe(true);\r\n      expect(criticalTimeline.days).toBe(1);\r\n      expect(highTimeline.immediate).toBe(false);\r\n      expect(highTimeline.days).toBe(7);\r\n      expect(mediumTimeline.days).toBe(30);\r\n    });\r\n\r\n    it('should provide compliance requirements', () => {\r\n      const criticalScore = CVSSScore.create(9.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      const lowScore = CVSSScore.create(2.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:L/AC:H/PR:H/UI:R/S:U/C:L/I:N/A:N');\r\n\r\n      const criticalCompliance = criticalScore.getComplianceRequirements();\r\n      const lowCompliance = lowScore.getComplianceRequirements();\r\n\r\n      expect(criticalCompliance.pciDss).toBe(true);\r\n      expect(criticalCompliance.sox).toBe(true);\r\n      expect(criticalCompliance.reporting).toBe(true);\r\n      expect(lowCompliance.pciDss).toBe(false);\r\n      expect(lowCompliance.sox).toBe(false);\r\n      expect(lowCompliance.reporting).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('immutability and updates', () => {\r\n    it('should create new score when adding temporal score', () => {\r\n      const originalScore = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      const updatedScore = originalScore.withTemporalScore(7.0);\r\n\r\n      expect(updatedScore).not.toBe(originalScore);\r\n      expect(updatedScore.temporalScore).toBe(7.0);\r\n      expect(originalScore.temporalScore).toBeUndefined();\r\n    });\r\n\r\n    it('should create new score when adding environmental score', () => {\r\n      const originalScore = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      const updatedScore = originalScore.withEnvironmentalScore(6.8);\r\n\r\n      expect(updatedScore).not.toBe(originalScore);\r\n      expect(updatedScore.environmentalScore).toBe(6.8);\r\n      expect(originalScore.environmentalScore).toBeUndefined();\r\n    });\r\n\r\n    it('should create new score when adding subscores', () => {\r\n      const originalScore = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      const updatedScore = originalScore.withSubscores(3.9, 5.9);\r\n\r\n      expect(updatedScore).not.toBe(originalScore);\r\n      expect(updatedScore.exploitabilityScore).toBe(3.9);\r\n      expect(updatedScore.impactScore).toBe(5.9);\r\n      expect(originalScore.exploitabilityScore).toBeUndefined();\r\n      expect(originalScore.impactScore).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('equality and comparison', () => {\r\n    it('should compare CVSS scores for equality', () => {\r\n      const score1 = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      const score2 = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      const score3 = CVSSScore.create(8.0, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      const score4 = CVSSScore.create(7.5, CVSSVersion.V3_0, 'CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n\r\n      expect(score1.equals(score2)).toBe(true);\r\n      expect(score1.equals(score3)).toBe(false); // Different base score\r\n      expect(score1.equals(score4)).toBe(false); // Different version\r\n      expect(score1.equals(undefined)).toBe(false);\r\n    });\r\n\r\n    it('should have consistent string representation', () => {\r\n      const score = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      expect(score.toString()).toBe('CVSS 3.1: 7.5 (high)');\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    it('should serialize to JSON correctly', () => {\r\n      const score = CVSSScore.create(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {\r\n        temporalScore: 7.0,\r\n        source: 'NVD',\r\n      });\r\n\r\n      const json = score.toJSON();\r\n\r\n      expect(json.baseScore).toBe(7.5);\r\n      expect(json.temporalScore).toBe(7.0);\r\n      expect(json.version).toBe(CVSSVersion.V3_1);\r\n      expect(json.vectorString).toBe('CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n      expect(json.source).toBe('NVD');\r\n      expect(json.summary).toBeDefined();\r\n      expect(json.compliance).toBeDefined();\r\n      expect(json.remediation).toBeDefined();\r\n    });\r\n\r\n    it('should deserialize from JSON correctly', () => {\r\n      const originalScore = CVSSScore.create(8.8, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H');\r\n      const json = originalScore.toJSON();\r\n      const deserializedScore = CVSSScore.fromJSON(json);\r\n\r\n      expect(deserializedScore.equals(originalScore)).toBe(true);\r\n      expect(deserializedScore.baseScore).toBe(originalScore.baseScore);\r\n      expect(deserializedScore.version).toBe(originalScore.version);\r\n      expect(deserializedScore.vectorString).toBe(originalScore.vectorString);\r\n    });\r\n  });\r\n\r\n  describe('validation utility', () => {\r\n    it('should validate CVSS score format without creating instance', () => {\r\n      expect(CVSSScore.isValid(7.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H')).toBe(true);\r\n      expect(CVSSScore.isValid(10.5, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H')).toBe(false);\r\n      expect(CVSSScore.isValid(7.5, CVSSVersion.V3_1, 'invalid-vector')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('score summary', () => {\r\n    it('should provide comprehensive score summary', () => {\r\n      const score = CVSSScore.create(8.8, CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H', {\r\n        temporalScore: 8.5,\r\n        environmentalScore: 8.0,\r\n      });\r\n\r\n      const summary = score.getScoreSummary();\r\n\r\n      expect(summary.baseScore).toBe(8.8);\r\n      expect(summary.temporalScore).toBe(8.5);\r\n      expect(summary.environmentalScore).toBe(8.0);\r\n      expect(summary.effectiveScore).toBe(8.0);\r\n      expect(summary.severity).toBe(CVSSSeverity.HIGH);\r\n      expect(summary.riskLevel).toBe('high');\r\n      expect(summary.requiresImmediateAttention).toBe(true);\r\n      expect(typeof summary.priorityScore).toBe('number');\r\n      expect(typeof summary.remediationDays).toBe('number');\r\n    });\r\n  });\r\n\r\n  describe('vector string parsing', () => {\r\n    it('should parse CVSS score from vector string', () => {\r\n      const vectorString = 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H';\r\n      const score = CVSSScore.fromVectorString(vectorString, 'test-source');\r\n\r\n      expect(score.vectorString).toBe(vectorString);\r\n      expect(score.version).toBe(CVSSVersion.V3_1);\r\n      expect(score.source).toBe('test-source');\r\n      expect(typeof score.baseScore).toBe('number');\r\n    });\r\n\r\n    it('should detect version from vector string', () => {\r\n      // This test would need the actual implementation of version detection\r\n      // For now, we'll test that it doesn't throw\r\n      expect(() => CVSSScore.fromVectorString('CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))\r\n        .not.toThrow();\r\n    });\r\n  });\r\n});"], "version": 3}