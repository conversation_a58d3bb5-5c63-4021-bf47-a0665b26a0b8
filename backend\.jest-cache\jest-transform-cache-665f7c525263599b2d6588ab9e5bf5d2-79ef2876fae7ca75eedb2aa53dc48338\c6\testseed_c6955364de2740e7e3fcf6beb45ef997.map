{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\seeds\\test.seed.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,2CAAwC;AACxC,+CAAiC;AAEjC;;;GAGG;AACH,MAAa,UAAU;IAGrB,YAA6B,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;QAFlC,WAAM,GAAG,IAAI,eAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAEA,CAAC;IAEvD;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAErD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAErC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAE3C,MAAM,KAAK,GAAG;YACZ;gBACE,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,yBAAyB;gBACtC,WAAW,EAAE,CAAC,GAAG,CAAC;gBAClB,cAAc,EAAE,KAAK;aACtB;YACD;gBACE,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,gBAAgB;gBAC7B,WAAW,EAAE,CAAC,MAAM,CAAC;gBACrB,cAAc,EAAE,KAAK;aACtB;SACF,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;OAQ3B,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAC3F,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,KAAK,CAAC,MAAM,aAAa,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAE3C,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAEtD,MAAM,KAAK,GAAG;YACZ;gBACE,KAAK,EAAE,wBAAwB;gBAC/B,aAAa,EAAE,YAAY;gBAC3B,UAAU,EAAE,MAAM;gBAClB,SAAS,EAAE,OAAO;gBAClB,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,IAAI;gBACf,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,KAAK,EAAE,uBAAuB;gBAC9B,aAAa,EAAE,YAAY;gBAC3B,UAAU,EAAE,MAAM;gBAClB,SAAS,EAAE,MAAM;gBACjB,IAAI,EAAE,WAAW;gBACjB,SAAS,EAAE,IAAI;gBACf,cAAc,EAAE,IAAI;aACrB;SACF,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;OAW3B,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QACxH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,KAAK,CAAC,MAAM,aAAa,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAE5C,MAAM,MAAM,GAAG;YACb;gBACE,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,UAAU;gBACtB,QAAQ,EAAE,aAAa;gBACvB,gBAAgB,EAAE,OAAO;gBACzB,UAAU,EAAE,OAAO;gBACnB,QAAQ,EAAE,UAAU;gBACpB,KAAK,EAAE,WAAW;gBAClB,WAAW,EAAE,QAAQ;gBACrB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;aAClC;YACD;gBACE,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE,aAAa;gBACnB,UAAU,EAAE,UAAU;gBACtB,QAAQ,EAAE,SAAS;gBACnB,gBAAgB,EAAE,SAAS;gBAC3B,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,UAAU;gBACpB,KAAK,EAAE,WAAW;gBAClB,WAAW,EAAE,KAAK;gBAClB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;aAClC;SACF,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;OAe3B,EAAE;gBACD,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ;gBACxD,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK;gBACrE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC;aAChE,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,MAAM,cAAc,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB;QACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAErD,MAAM,eAAe,GAAG;YACtB;gBACE,MAAM,EAAE,mBAAmB;gBAC3B,KAAK,EAAE,yBAAyB;gBAChC,WAAW,EAAE,8CAA8C;gBAC3D,QAAQ,EAAE,MAAM;gBAChB,UAAU,EAAE,GAAG;gBACf,WAAW,EAAE,8CAA8C;gBAC3D,MAAM,EAAE,QAAQ;gBAChB,iBAAiB,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7D,UAAU,EAAE,CAAC,EAAE,GAAG,EAAE,0BAA0B,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;gBAC/D,iBAAiB,EAAE,KAAK;gBACxB,eAAe,EAAE,IAAI;gBACrB,cAAc,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACtC,aAAa,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;aACtC;YACD;gBACE,MAAM,EAAE,mBAAmB;gBAC3B,KAAK,EAAE,2BAA2B;gBAClC,WAAW,EAAE,gDAAgD;gBAC7D,QAAQ,EAAE,QAAQ;gBAClB,UAAU,EAAE,GAAG;gBACf,WAAW,EAAE,8CAA8C;gBAC3D,MAAM,EAAE,QAAQ;gBAChB,iBAAiB,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7D,UAAU,EAAE,CAAC,EAAE,GAAG,EAAE,2BAA2B,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;gBAChE,iBAAiB,EAAE,KAAK;gBACxB,eAAe,EAAE,KAAK;gBACtB,cAAc,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACtC,aAAa,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;aACtC;SACF,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;OAiB3B,EAAE;gBACD,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ;gBACxD,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM;gBAC9C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;gBACvE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe;gBAC5C,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa;aACxC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,eAAe,CAAC,MAAM,uBAAuB,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAEhD,IAAI,CAAC;YACH,uCAAuC;YACvC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;YAC3E,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;YAChF,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC7E,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;YACpF,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;YACrE,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;YACzE,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;YACzE,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mEAAmE,CAAC,CAAC;YACjG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;YAC9F,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YACpE,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;YAChE,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;YACpF,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAC5E,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;YAEhE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAhRD,gCAgRC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\seeds\\test.seed.ts"], "sourcesContent": ["import { DataSource } from 'typeorm';\r\nimport { Logger } from '@nestjs/common';\r\nimport * as bcrypt from 'bcrypt';\r\n\r\n/**\r\n * Test environment database seeder\r\n * Creates minimal test data for automated testing\r\n */\r\nexport class TestSeeder {\r\n  private readonly logger = new Logger(TestSeeder.name);\r\n\r\n  constructor(private readonly dataSource: DataSource) {}\r\n\r\n  /**\r\n   * Execute all test seeds\r\n   */\r\n  async seed(): Promise<void> {\r\n    this.logger.log('Starting test database seeding...');\r\n\r\n    try {\r\n      await this.seedTestRoles();\r\n      await this.seedTestUsers();\r\n      await this.seedTestAssets();\r\n      await this.seedTestVulnerabilities();\r\n\r\n      this.logger.log('Test database seeding completed successfully');\r\n    } catch (error) {\r\n      this.logger.error('Test database seeding failed', {\r\n        error: error.message,\r\n        stack: error.stack,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Seed minimal test roles\r\n   */\r\n  private async seedTestRoles(): Promise<void> {\r\n    this.logger.debug('Seeding test roles...');\r\n\r\n    const roles = [\r\n      {\r\n        name: 'test_admin',\r\n        description: 'Test administrator role',\r\n        permissions: ['*'],\r\n        is_system_role: false,\r\n      },\r\n      {\r\n        name: 'test_user',\r\n        description: 'Test user role',\r\n        permissions: ['read'],\r\n        is_system_role: false,\r\n      },\r\n    ];\r\n\r\n    for (const role of roles) {\r\n      await this.dataSource.query(`\r\n        INSERT INTO roles (name, description, permissions, is_system_role)\r\n        VALUES ($1, $2, $3, $4)\r\n        ON CONFLICT (name) DO UPDATE SET\r\n          description = EXCLUDED.description,\r\n          permissions = EXCLUDED.permissions,\r\n          is_system_role = EXCLUDED.is_system_role,\r\n          updated_at = CURRENT_TIMESTAMP\r\n      `, [role.name, role.description, JSON.stringify(role.permissions), role.is_system_role]);\r\n    }\r\n\r\n    this.logger.debug(`Seeded ${roles.length} test roles`);\r\n  }\r\n\r\n  /**\r\n   * Seed minimal test users\r\n   */\r\n  private async seedTestUsers(): Promise<void> {\r\n    this.logger.debug('Seeding test users...');\r\n\r\n    const passwordHash = await bcrypt.hash('test123', 10);\r\n\r\n    const users = [\r\n      {\r\n        email: '<EMAIL>',\r\n        password_hash: passwordHash,\r\n        first_name: 'Test',\r\n        last_name: 'Admin',\r\n        role: 'test_admin',\r\n        is_active: true,\r\n        email_verified: true,\r\n      },\r\n      {\r\n        email: '<EMAIL>',\r\n        password_hash: passwordHash,\r\n        first_name: 'Test',\r\n        last_name: 'User',\r\n        role: 'test_user',\r\n        is_active: true,\r\n        email_verified: true,\r\n      },\r\n    ];\r\n\r\n    for (const user of users) {\r\n      await this.dataSource.query(`\r\n        INSERT INTO users (email, password_hash, first_name, last_name, role, is_active, email_verified)\r\n        VALUES ($1, $2, $3, $4, $5, $6, $7)\r\n        ON CONFLICT (email) DO UPDATE SET\r\n          password_hash = EXCLUDED.password_hash,\r\n          first_name = EXCLUDED.first_name,\r\n          last_name = EXCLUDED.last_name,\r\n          role = EXCLUDED.role,\r\n          is_active = EXCLUDED.is_active,\r\n          email_verified = EXCLUDED.email_verified,\r\n          updated_at = CURRENT_TIMESTAMP\r\n      `, [user.email, user.password_hash, user.first_name, user.last_name, user.role, user.is_active, user.email_verified]);\r\n    }\r\n\r\n    this.logger.debug(`Seeded ${users.length} test users`);\r\n  }\r\n\r\n  /**\r\n   * Seed minimal test assets\r\n   */\r\n  private async seedTestAssets(): Promise<void> {\r\n    this.logger.debug('Seeding test assets...');\r\n\r\n    const assets = [\r\n      {\r\n        name: 'Test Server',\r\n        type: 'SERVER',\r\n        ip_address: '********',\r\n        hostname: 'test-server',\r\n        operating_system: 'Linux',\r\n        os_version: '20.04',\r\n        location: 'Test Lab',\r\n        owner: 'Test Team',\r\n        criticality: 'MEDIUM',\r\n        status: 'ACTIVE',\r\n        metadata: { environment: 'test' }\r\n      },\r\n      {\r\n        name: 'Test Workstation',\r\n        type: 'WORKSTATION',\r\n        ip_address: '********',\r\n        hostname: 'test-ws',\r\n        operating_system: 'Windows',\r\n        os_version: '10',\r\n        location: 'Test Lab',\r\n        owner: 'Test Team',\r\n        criticality: 'LOW',\r\n        status: 'ACTIVE',\r\n        metadata: { environment: 'test' }\r\n      },\r\n    ];\r\n\r\n    for (const asset of assets) {\r\n      await this.dataSource.query(`\r\n        INSERT INTO assets (name, type, ip_address, hostname, operating_system, os_version, location, owner, criticality, status, metadata)\r\n        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)\r\n        ON CONFLICT (name) DO UPDATE SET\r\n          type = EXCLUDED.type,\r\n          ip_address = EXCLUDED.ip_address,\r\n          hostname = EXCLUDED.hostname,\r\n          operating_system = EXCLUDED.operating_system,\r\n          os_version = EXCLUDED.os_version,\r\n          location = EXCLUDED.location,\r\n          owner = EXCLUDED.owner,\r\n          criticality = EXCLUDED.criticality,\r\n          status = EXCLUDED.status,\r\n          metadata = EXCLUDED.metadata,\r\n          updated_at = CURRENT_TIMESTAMP\r\n      `, [\r\n        asset.name, asset.type, asset.ip_address, asset.hostname,\r\n        asset.operating_system, asset.os_version, asset.location, asset.owner,\r\n        asset.criticality, asset.status, JSON.stringify(asset.metadata)\r\n      ]);\r\n    }\r\n\r\n    this.logger.debug(`Seeded ${assets.length} test assets`);\r\n  }\r\n\r\n  /**\r\n   * Seed minimal test vulnerabilities\r\n   */\r\n  private async seedTestVulnerabilities(): Promise<void> {\r\n    this.logger.debug('Seeding test vulnerabilities...');\r\n\r\n    const vulnerabilities = [\r\n      {\r\n        cve_id: 'CVE-2023-TEST-001',\r\n        title: 'Test Vulnerability High',\r\n        description: 'Test vulnerability for high severity testing',\r\n        severity: 'HIGH',\r\n        cvss_score: 8.0,\r\n        cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N',\r\n        cwe_id: 'CWE-79',\r\n        affected_software: [{ name: 'TestApp', versions: ['1.0.0'] }],\r\n        references: [{ url: 'https://example.com/test', type: 'test' }],\r\n        exploit_available: false,\r\n        patch_available: true,\r\n        published_date: new Date('2023-01-01'),\r\n        modified_date: new Date('2023-01-01')\r\n      },\r\n      {\r\n        cve_id: 'CVE-2023-TEST-002',\r\n        title: 'Test Vulnerability Medium',\r\n        description: 'Test vulnerability for medium severity testing',\r\n        severity: 'MEDIUM',\r\n        cvss_score: 5.0,\r\n        cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N',\r\n        cwe_id: 'CWE-89',\r\n        affected_software: [{ name: 'TestApp', versions: ['2.0.0'] }],\r\n        references: [{ url: 'https://example.com/test2', type: 'test' }],\r\n        exploit_available: false,\r\n        patch_available: false,\r\n        published_date: new Date('2023-02-01'),\r\n        modified_date: new Date('2023-02-01')\r\n      },\r\n    ];\r\n\r\n    for (const vuln of vulnerabilities) {\r\n      await this.dataSource.query(`\r\n        INSERT INTO vulnerabilities (cve_id, title, description, severity, cvss_score, cvss_vector, cwe_id, affected_software, references, exploit_available, patch_available, published_date, modified_date)\r\n        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)\r\n        ON CONFLICT (cve_id) DO UPDATE SET\r\n          title = EXCLUDED.title,\r\n          description = EXCLUDED.description,\r\n          severity = EXCLUDED.severity,\r\n          cvss_score = EXCLUDED.cvss_score,\r\n          cvss_vector = EXCLUDED.cvss_vector,\r\n          cwe_id = EXCLUDED.cwe_id,\r\n          affected_software = EXCLUDED.affected_software,\r\n          references = EXCLUDED.references,\r\n          exploit_available = EXCLUDED.exploit_available,\r\n          patch_available = EXCLUDED.patch_available,\r\n          published_date = EXCLUDED.published_date,\r\n          modified_date = EXCLUDED.modified_date,\r\n          updated_at = CURRENT_TIMESTAMP\r\n      `, [\r\n        vuln.cve_id, vuln.title, vuln.description, vuln.severity,\r\n        vuln.cvss_score, vuln.cvss_vector, vuln.cwe_id,\r\n        JSON.stringify(vuln.affected_software), JSON.stringify(vuln.references),\r\n        vuln.exploit_available, vuln.patch_available,\r\n        vuln.published_date, vuln.modified_date\r\n      ]);\r\n    }\r\n\r\n    this.logger.debug(`Seeded ${vulnerabilities.length} test vulnerabilities`);\r\n  }\r\n\r\n  /**\r\n   * Clean up test data\r\n   */\r\n  async cleanup(): Promise<void> {\r\n    this.logger.log('Cleaning up test database...');\r\n\r\n    try {\r\n      // Clean up in reverse dependency order\r\n      await this.dataSource.query('DELETE FROM asset_vulnerabilities WHERE 1=1');\r\n      await this.dataSource.query('DELETE FROM security_event_attachments WHERE 1=1');\r\n      await this.dataSource.query('DELETE FROM security_event_comments WHERE 1=1');\r\n      await this.dataSource.query('DELETE FROM security_event_tag_assignments WHERE 1=1');\r\n      await this.dataSource.query('DELETE FROM security_events WHERE 1=1');\r\n      await this.dataSource.query('DELETE FROM security_event_tags WHERE 1=1');\r\n      await this.dataSource.query('DELETE FROM vulnerability_scans WHERE 1=1');\r\n      await this.dataSource.query('DELETE FROM vulnerabilities WHERE cve_id LIKE \\'CVE-2023-TEST-%\\'');\r\n      await this.dataSource.query('DELETE FROM assets WHERE metadata->>\\'environment\\' = \\'test\\'');\r\n      await this.dataSource.query('DELETE FROM refresh_tokens WHERE 1=1');\r\n      await this.dataSource.query('DELETE FROM user_roles WHERE 1=1');\r\n      await this.dataSource.query('DELETE FROM users WHERE email LIKE \\'%@example.com\\'');\r\n      await this.dataSource.query('DELETE FROM roles WHERE name LIKE \\'test_%\\'');\r\n      await this.dataSource.query('DELETE FROM audit_logs WHERE 1=1');\r\n\r\n      this.logger.log('Test database cleanup completed successfully');\r\n    } catch (error) {\r\n      this.logger.error('Test database cleanup failed', {\r\n        error: error.message,\r\n        stack: error.stack,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n}"], "version": 3}