{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting-analytics\\application\\services\\report-generation.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAA4F;AAC5F,6CAAmD;AACnD,qCAAiD;AACjD,uEAA6D;AAC7D,2FAAgF;AAChF,sFAAkF;AAClF,0FAAsF;AACtF,yEAAoE;AACpE,mEAA8D;AAE9D;;;GAGG;AAEI,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAGlC,YAEE,gBAAqD,EAErD,mBAAiE,EAChD,UAAsB,EACtB,aAA4B,EAC5B,YAA0B,EAC1B,sBAA8C,EAC9C,mBAAwC;QAPxC,qBAAgB,GAAhB,gBAAgB,CAAoB;QAEpC,wBAAmB,GAAnB,mBAAmB,CAA6B;QAChD,eAAU,GAAV,UAAU,CAAY;QACtB,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;QAC1B,2BAAsB,GAAtB,sBAAsB,CAAwB;QAC9C,wBAAmB,GAAnB,mBAAmB,CAAqB;QAX1C,WAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;IAYhE,CAAC;IAEJ;;;;;;;OAOG;IACH,KAAK,CAAC,aAAa,CACjB,QAAgB,EAChB,aAUI,EAAE,EACN,MAAc,EACd,cAA0D,QAAQ;QAElE,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,QAAQ;gBACR,UAAU;gBACV,MAAM;gBACN,WAAW;aACZ,CAAC,CAAC;YAEH,2BAA2B;YAC3B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBACjD,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;aACxB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,0BAAiB,CAAC,kBAAkB,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC/B,MAAM,IAAI,4BAAmB,CAAC,sBAAsB,CAAC,CAAC;YACxD,CAAC;YAED,gCAAgC;YAChC,MAAM,UAAU,GAAG,MAAM,CAAC,qBAAqB,EAAE,CAAC;YAClD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,MAAM,IAAI,4BAAmB,CAAC,oCAAoC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpG,CAAC;YAED,0BAA0B;YAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;gBAChD,QAAQ;gBACR,MAAM,EAAE,SAAS;gBACjB,WAAW;gBACX,UAAU;gBACV,UAAU,EAAE,MAAM;aACnB,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEtE,iCAAiC;YACjC,IAAI,CAAC,sBAAsB,CAAC,cAAc,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YAE5D,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,SAAS,EACT,QAAQ,EACR,QAAQ,EACR;gBACE,WAAW,EAAE,cAAc,CAAC,EAAE;gBAC9B,WAAW;gBACX,UAAU;aACX,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,EAAE;gBAC1C,QAAQ;gBACR,WAAW,EAAE,cAAc,CAAC,EAAE;gBAC9B,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,cAAc,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACpD,QAAQ;gBACR,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,sBAAsB,CAClC,SAA0B,EAC1B,MAAc,EACd,MAAc;QAEd,IAAI,CAAC;YACH,kBAAkB;YAClB,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE/C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,WAAW,EAAE,SAAS,CAAC,EAAE;aAC1B,CAAC,CAAC;YAEH,0BAA0B;YAC1B,SAAS,CAAC,cAAc,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC;YAChD,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE/C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;YAE9E,0BAA0B;YAC1B,SAAS,CAAC,cAAc,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC;YAChD,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE/C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAEvE,kCAAkC;YAClC,SAAS,CAAC,cAAc,CAAC,EAAE,EAAE,2BAA2B,CAAC,CAAC;YAC1D,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE/C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;YAEhF,wBAAwB;YACxB,SAAS,CAAC,cAAc,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAC;YACjD,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE/C,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,YAAY,IAAI,KAAK,CAAC;YACjE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;YAEhG,qBAAqB;YACrB,MAAM,eAAe,GAAG;gBACtB,OAAO,EAAE;oBACP,YAAY,EAAE,aAAa,CAAC,YAAY,IAAI,CAAC;oBAC7C,UAAU,EAAE,aAAa,CAAC,UAAU,IAAI,CAAC;oBACzC,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACrC,SAAS,EAAE,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,CAAC;iBACtE;gBACD,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,EAAE;gBACtC,MAAM,EAAE,cAAc,CAAC,MAAM,IAAI,EAAE;gBACnC,MAAM,EAAE,aAAa,CAAC,MAAM,IAAI,EAAE;gBAClC,OAAO,EAAE,aAAa,CAAC,OAAO,IAAI,EAAE;aACrC,CAAC;YAEF,SAAS,CAAC,QAAQ,CAAC,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;YAC9D,SAAS,CAAC,gBAAgB,GAAG,aAAa,CAAC,YAAY,IAAI,CAAC,CAAC;YAC7D,SAAS,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAE/D,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE/C,2BAA2B;YAC3B,MAAM,CAAC,oBAAoB,CACzB,SAAS,CAAC,UAAU,IAAI,CAAC,EACzB,MAAM,CAAC,SAAS,CAAC,eAAe,IAAI,CAAC,CAAC,EACtC,SAAS,CACV,CAAC;YACF,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEzC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,EAAE;gBACzD,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,WAAW,EAAE,SAAS,CAAC,EAAE;gBACzB,QAAQ,EAAE,SAAS,CAAC,iBAAiB;gBACrC,UAAU,EAAE,SAAS,CAAC,mBAAmB;aAC1C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBAC3C,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,WAAW,EAAE,SAAS,CAAC,EAAE;gBACzB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YAEH,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtB,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE/C,2BAA2B;YAC3B,MAAM,CAAC,oBAAoB,CACzB,SAAS,CAAC,UAAU,IAAI,CAAC,EACzB,CAAC,EACD,QAAQ,CACT,CAAC;YACF,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,iBAAiB,CAC7B,MAAc,EACd,UAAgB;QAEhB,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QAC5C,MAAM,aAAa,GAAwB,EAAE,CAAC;QAE9C,KAAK,MAAM,cAAc,IAAI,WAAW,EAAE,CAAC;YACzC,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAC1D,cAAc,EACd;oBACE,GAAG,MAAM,CAAC,aAAa;oBACvB,GAAG,UAAU;iBACd,CACF,CAAC;gBACF,aAAa,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;YACvC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE;oBACrD,UAAU,EAAE,cAAc;oBAC1B,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,CAAC,CAAC;gBACH,aAAa,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YACrE,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,iBAAiB,CAC7B,MAAc,EACd,OAA4B;QAE5B,MAAM,aAAa,GAAQ;YACzB,YAAY,EAAE,CAAC;YACf,UAAU,EAAE,CAAC;YACb,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;SACZ,CAAC;QAEF,2BAA2B;QAC3B,KAAK,MAAM,CAAC,cAAc,EAAE,UAAU,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACnE,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;gBACrB,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC;oBAC1B,EAAE,EAAE,SAAS,cAAc,EAAE;oBAC7B,KAAK,EAAE,YAAY,cAAc,EAAE;oBACnC,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,EAAE,OAAO,EAAE,wBAAwB,UAAU,CAAC,KAAK,EAAE,EAAE;iBAC9D,CAAC,CAAC;gBACH,SAAS;YACX,CAAC;YAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC;YAC3C,aAAa,CAAC,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAEpE,kCAAkC;YAClC,IAAI,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;gBACjC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBACvE,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC;oBAC1B,EAAE,EAAE,WAAW,cAAc,EAAE;oBAC/B,KAAK,EAAE,GAAG,cAAc,YAAY;oBACpC,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,WAAW;iBAClB,CAAC,CAAC;YACL,CAAC;YAED,qBAAqB;YACrB,IAAI,MAAM,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;gBACtC,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;gBACnF,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;YAChD,CAAC;YAED,gBAAgB;YAChB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3C,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAEnE,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;oBACxB,EAAE,EAAE,SAAS,cAAc,EAAE;oBAC7B,KAAK,EAAE,cAAc;oBACrB,OAAO;oBACP,IAAI;oBACJ,QAAQ,EAAE;wBACR,SAAS,EAAE,IAAI,CAAC,MAAM;wBACtB,UAAU,EAAE,cAAc;qBAC3B;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,sBAAsB,CAClC,MAAc,EACd,aAAkB;QAElB,MAAM,cAAc,GAAQ;YAC1B,MAAM,EAAE,EAAE;SACX,CAAC;QAEF,IAAI,MAAM,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YACxC,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;gBAC5D,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;oBACrE,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC;wBACzB,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;wBAC1C,KAAK,EAAE,SAAS,CAAC,KAAK;wBACtB,IAAI,EAAE,SAAS,CAAC,SAAS,IAAI,KAAK;wBAClC,IAAI,EAAE,SAAS;wBACf,aAAa,EAAE,SAAS,CAAC,aAAa;qBACvC,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE;wBACnD,aAAa,EAAE,SAAS,CAAC,KAAK;wBAC9B,KAAK,EAAE,KAAK,CAAC,OAAO;qBACrB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;;;;;;OAOG;IACK,KAAK,CAAC,YAAY,CACxB,MAAc,EACd,aAAkB,EAClB,cAAmB,EACnB,MAAc;QAEd,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAChD;YACE,MAAM;YACN,IAAI,EAAE,aAAa;YACnB,cAAc;SACf,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,WAAW,CAAC,QAAgB;QACxC,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC;YAClC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,OAAO,KAAK,CAAC,IAAI,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBAC1C,QAAQ;gBACR,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,uBAAuB,CAAC,MAAc,EAAE,UAAgB;QAC9D,MAAM,SAAS,GAAG,UAAU,EAAE,SAAS,IAAI,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;QAE1E,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAClC,OAAO,QAAQ,SAAS,CAAC,KAAK,EAAE,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,SAAS,CAAC,SAAS,OAAO,SAAS,CAAC,OAAO,EAAE,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,SAAS,CAAC,IAAW,EAAE,OAAiB;QAC9C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACxC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBACd,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YAChB,CAAC;YACD,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpB,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;YACpD,KAAK,EAAE,GAAG;YACV,KAAK,EAAG,KAAe,CAAC,MAAM;YAC9B,KAAK;SACN,CAAC,CAAC,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACK,aAAa,CAAC,IAAW,EAAE,YAAmB;QACpD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtD,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAC5B,IAAI,KAAU,CAAC;YACf,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;YAExB,QAAQ,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACrB,KAAK,OAAO;oBACV,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;oBACpB,MAAM;gBACR,KAAK,KAAK;oBACR,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACxE,MAAM;gBACR,KAAK,KAAK;oBACR,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC5E,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChD,MAAM;gBACR,KAAK,KAAK;oBACR,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBAChE,MAAM;gBACR,KAAK,KAAK;oBACR,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBAChE,MAAM;gBACR;oBACE,KAAK,GAAG,CAAC,CAAC;YACd,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,GAAG,CAAC,KAAK,IAAI,GAAG,GAAG,CAAC,QAAQ,IAAI,KAAK,EAAE;gBAC3C,KAAK,EAAE,GAAG,CAAC,KAAK,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,OAAO,KAAK,EAAE;gBAC/D,KAAK;gBACL,IAAI,EAAE,GAAG,CAAC,IAAI;aACf,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,aAAa,CAAC,SAAc,EAAE,aAAkB;QAC5D,wCAAwC;QACxC,8DAA8D;QAC9D,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;QAExF,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QACtC,CAAC;QAED,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,QAAQ,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;QAClF,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAE/D,OAAO;YACL,MAAM;YACN,QAAQ,EAAE,CAAC;oBACT,KAAK,EAAE,SAAS,CAAC,KAAK;oBACtB,IAAI,EAAE,MAAM;oBACZ,eAAe,EAAE,SAAS,CAAC,aAAa,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC;iBAChE,CAAC;SACH,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,YAAY,CAAC,WAAmB;QACpC,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;gBAC5C,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;gBAC1B,SAAS,EAAE,CAAC,QAAQ,CAAC;aACtB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,WAAW;gBACX,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,eAAe,CAAC,WAAmB,EAAE,MAAc;QACvD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACvD,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,CAAC,CAAC;YAC5D,CAAC;YAED,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;gBACzB,MAAM,IAAI,4BAAmB,CAAC,iCAAiC,CAAC,CAAC;YACnE,CAAC;YAED,SAAS,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;YACtC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAExE,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,kBAAkB,EAClB,WAAW,EACX;gBACE,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,MAAM,EAAE,cAAc;aACvB,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,EAAE;gBAC5C,WAAW;gBACX,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,WAAW;gBACX,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AArkBY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,sBAAM,CAAC,CAAA;IAExB,WAAA,IAAA,0BAAgB,EAAC,yCAAe,CAAC,CAAA;yDADC,oBAAU,oBAAV,oBAAU,oDAEP,oBAAU,oBAAV,oBAAU,oDACnB,oBAAU,oBAAV,oBAAU,oDACP,8BAAa,oBAAb,8BAAa,oDACd,4BAAY,oBAAZ,4BAAY,oDACF,iDAAsB,oBAAtB,iDAAsB,oDACzB,2CAAmB,oBAAnB,2CAAmB;GAZhD,uBAAuB,CAqkBnC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting-analytics\\application\\services\\report-generation.service.ts"], "sourcesContent": ["import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, DataSource } from 'typeorm';\r\nimport { Report } from '../../domain/entities/report.entity';\r\nimport { ReportExecution } from '../../domain/entities/report-execution.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { DataAggregationService } from './data-aggregation.service';\r\nimport { ReportExportService } from './report-export.service';\r\n\r\n/**\r\n * Report Generation service\r\n * Handles report execution, data processing, and output generation\r\n */\r\n@Injectable()\r\nexport class ReportGenerationService {\r\n  private readonly logger = new Logger(ReportGenerationService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(Report)\r\n    private readonly reportRepository: Repository<Report>,\r\n    @InjectRepository(ReportExecution)\r\n    private readonly executionRepository: Repository<ReportExecution>,\r\n    private readonly dataSource: DataSource,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly auditService: AuditService,\r\n    private readonly dataAggregationService: DataAggregationService,\r\n    private readonly reportExportService: ReportExportService,\r\n  ) {}\r\n\r\n  /**\r\n   * Execute a report and generate output\r\n   * @param reportId Report ID\r\n   * @param parameters Execution parameters\r\n   * @param userId User executing the report\r\n   * @param triggerType How the execution was triggered\r\n   * @returns Report execution\r\n   */\r\n  async executeReport(\r\n    reportId: string,\r\n    parameters: {\r\n      timeRange?: {\r\n        type: 'relative' | 'absolute';\r\n        value?: string;\r\n        startDate?: string;\r\n        endDate?: string;\r\n      };\r\n      filters?: Record<string, any>;\r\n      exportFormat?: string;\r\n      customParameters?: Record<string, any>;\r\n    } = {},\r\n    userId: string,\r\n    triggerType: 'manual' | 'scheduled' | 'api' | 'webhook' = 'manual',\r\n  ): Promise<ReportExecution> {\r\n    try {\r\n      this.logger.debug('Starting report execution', {\r\n        reportId,\r\n        parameters,\r\n        userId,\r\n        triggerType,\r\n      });\r\n\r\n      // Get report configuration\r\n      const report = await this.reportRepository.findOne({\r\n        where: { id: reportId },\r\n      });\r\n\r\n      if (!report) {\r\n        throw new NotFoundException('Report not found');\r\n      }\r\n\r\n      if (report.status !== 'active') {\r\n        throw new BadRequestException('Report is not active');\r\n      }\r\n\r\n      // Validate report configuration\r\n      const validation = report.validateConfiguration();\r\n      if (!validation.isValid) {\r\n        throw new BadRequestException(`Report configuration is invalid: ${validation.errors.join(', ')}`);\r\n      }\r\n\r\n      // Create execution record\r\n      const execution = this.executionRepository.create({\r\n        reportId,\r\n        status: 'pending',\r\n        triggerType,\r\n        parameters,\r\n        executedBy: userId,\r\n      });\r\n\r\n      const savedExecution = await this.executionRepository.save(execution);\r\n\r\n      // Start execution asynchronously\r\n      this.processReportExecution(savedExecution, report, userId);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'execute',\r\n        'report',\r\n        reportId,\r\n        {\r\n          executionId: savedExecution.id,\r\n          triggerType,\r\n          parameters,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Report execution started', {\r\n        reportId,\r\n        executionId: savedExecution.id,\r\n        userId,\r\n      });\r\n\r\n      return savedExecution;\r\n    } catch (error) {\r\n      this.logger.error('Failed to start report execution', {\r\n        reportId,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Process report execution\r\n   * @param execution Report execution\r\n   * @param report Report configuration\r\n   * @param userId User ID\r\n   */\r\n  private async processReportExecution(\r\n    execution: ReportExecution,\r\n    report: Report,\r\n    userId: string,\r\n  ): Promise<void> {\r\n    try {\r\n      // Start execution\r\n      execution.start();\r\n      await this.executionRepository.save(execution);\r\n\r\n      this.logger.debug('Processing report execution', {\r\n        reportId: report.id,\r\n        executionId: execution.id,\r\n      });\r\n\r\n      // Step 1: Data collection\r\n      execution.updateProgress(10, 'Collecting data');\r\n      await this.executionRepository.save(execution);\r\n\r\n      const reportData = await this.collectReportData(report, execution.parameters);\r\n\r\n      // Step 2: Data processing\r\n      execution.updateProgress(40, 'Processing data');\r\n      await this.executionRepository.save(execution);\r\n\r\n      const processedData = await this.processReportData(report, reportData);\r\n\r\n      // Step 3: Generate visualizations\r\n      execution.updateProgress(70, 'Generating visualizations');\r\n      await this.executionRepository.save(execution);\r\n\r\n      const visualizations = await this.generateVisualizations(report, processedData);\r\n\r\n      // Step 4: Export report\r\n      execution.updateProgress(90, 'Exporting report');\r\n      await this.executionRepository.save(execution);\r\n\r\n      const exportFormat = execution.parameters?.exportFormat || 'pdf';\r\n      const outputPath = await this.exportReport(report, processedData, visualizations, exportFormat);\r\n\r\n      // Complete execution\r\n      const finalReportData = {\r\n        summary: {\r\n          totalRecords: processedData.totalRecords || 0,\r\n          dataPoints: processedData.dataPoints || 0,\r\n          generatedAt: new Date().toISOString(),\r\n          timeRange: this.getTimeRangeDescription(report, execution.parameters),\r\n        },\r\n        sections: processedData.sections || [],\r\n        charts: visualizations.charts || [],\r\n        tables: processedData.tables || [],\r\n        metrics: processedData.metrics || [],\r\n      };\r\n\r\n      execution.complete(outputPath, exportFormat, finalReportData);\r\n      execution.recordsProcessed = processedData.totalRecords || 0;\r\n      execution.outputSizeBytes = await this.getFileSize(outputPath);\r\n\r\n      await this.executionRepository.save(execution);\r\n\r\n      // Update report statistics\r\n      report.updateExecutionStats(\r\n        execution.durationMs || 0,\r\n        Number(execution.outputSizeBytes || 0),\r\n        'success',\r\n      );\r\n      await this.reportRepository.save(report);\r\n\r\n      this.logger.log('Report execution completed successfully', {\r\n        reportId: report.id,\r\n        executionId: execution.id,\r\n        duration: execution.durationFormatted,\r\n        outputSize: execution.outputSizeFormatted,\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Report execution failed', {\r\n        reportId: report.id,\r\n        executionId: execution.id,\r\n        error: error.message,\r\n      });\r\n\r\n      execution.fail(error);\r\n      await this.executionRepository.save(execution);\r\n\r\n      // Update report statistics\r\n      report.updateExecutionStats(\r\n        execution.durationMs || 0,\r\n        0,\r\n        'failed',\r\n      );\r\n      await this.reportRepository.save(report);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Collect data for report\r\n   * @param report Report configuration\r\n   * @param parameters Execution parameters\r\n   * @returns Raw data\r\n   */\r\n  private async collectReportData(\r\n    report: Report,\r\n    parameters?: any,\r\n  ): Promise<any> {\r\n    const dataSources = report.getDataSources();\r\n    const collectedData: Record<string, any> = {};\r\n\r\n    for (const dataSourceName of dataSources) {\r\n      try {\r\n        const data = await this.dataAggregationService.aggregateData(\r\n          dataSourceName,\r\n          {\r\n            ...report.configuration,\r\n            ...parameters,\r\n          },\r\n        );\r\n        collectedData[dataSourceName] = data;\r\n      } catch (error) {\r\n        this.logger.warn('Failed to collect data from source', {\r\n          dataSource: dataSourceName,\r\n          error: error.message,\r\n        });\r\n        collectedData[dataSourceName] = { error: error.message, data: [] };\r\n      }\r\n    }\r\n\r\n    return collectedData;\r\n  }\r\n\r\n  /**\r\n   * Process collected data\r\n   * @param report Report configuration\r\n   * @param rawData Raw collected data\r\n   * @returns Processed data\r\n   */\r\n  private async processReportData(\r\n    report: Report,\r\n    rawData: Record<string, any>,\r\n  ): Promise<any> {\r\n    const processedData: any = {\r\n      totalRecords: 0,\r\n      dataPoints: 0,\r\n      sections: [],\r\n      tables: [],\r\n      metrics: [],\r\n    };\r\n\r\n    // Process each data source\r\n    for (const [dataSourceName, sourceData] of Object.entries(rawData)) {\r\n      if (sourceData.error) {\r\n        processedData.sections.push({\r\n          id: `error-${dataSourceName}`,\r\n          title: `Error in ${dataSourceName}`,\r\n          type: 'text',\r\n          data: { content: `Failed to load data: ${sourceData.error}` },\r\n        });\r\n        continue;\r\n      }\r\n\r\n      const data = sourceData.data || sourceData;\r\n      processedData.totalRecords += Array.isArray(data) ? data.length : 1;\r\n\r\n      // Apply grouping and aggregations\r\n      if (report.configuration.groupBy) {\r\n        const groupedData = this.groupData(data, report.configuration.groupBy);\r\n        processedData.sections.push({\r\n          id: `grouped-${dataSourceName}`,\r\n          title: `${dataSourceName} (Grouped)`,\r\n          type: 'table',\r\n          data: groupedData,\r\n        });\r\n      }\r\n\r\n      // Apply aggregations\r\n      if (report.configuration.aggregations) {\r\n        const aggregatedData = this.aggregateData(data, report.configuration.aggregations);\r\n        processedData.metrics.push(...aggregatedData);\r\n      }\r\n\r\n      // Create tables\r\n      if (Array.isArray(data) && data.length > 0) {\r\n        const headers = Object.keys(data[0]);\r\n        const rows = data.map(item => headers.map(header => item[header]));\r\n        \r\n        processedData.tables.push({\r\n          id: `table-${dataSourceName}`,\r\n          title: dataSourceName,\r\n          headers,\r\n          rows,\r\n          metadata: {\r\n            totalRows: data.length,\r\n            dataSource: dataSourceName,\r\n          },\r\n        });\r\n      }\r\n    }\r\n\r\n    return processedData;\r\n  }\r\n\r\n  /**\r\n   * Generate visualizations for report\r\n   * @param report Report configuration\r\n   * @param processedData Processed data\r\n   * @returns Visualization data\r\n   */\r\n  private async generateVisualizations(\r\n    report: Report,\r\n    processedData: any,\r\n  ): Promise<any> {\r\n    const visualizations: any = {\r\n      charts: [],\r\n    };\r\n\r\n    if (report.configuration.visualizations) {\r\n      for (const vizConfig of report.configuration.visualizations) {\r\n        try {\r\n          const chartData = await this.generateChart(vizConfig, processedData);\r\n          visualizations.charts.push({\r\n            id: `chart-${Date.now()}-${Math.random()}`,\r\n            title: vizConfig.title,\r\n            type: vizConfig.chartType || 'bar',\r\n            data: chartData,\r\n            configuration: vizConfig.configuration,\r\n          });\r\n        } catch (error) {\r\n          this.logger.warn('Failed to generate visualization', {\r\n            visualization: vizConfig.title,\r\n            error: error.message,\r\n          });\r\n        }\r\n      }\r\n    }\r\n\r\n    return visualizations;\r\n  }\r\n\r\n  /**\r\n   * Export report to specified format\r\n   * @param report Report configuration\r\n   * @param processedData Processed data\r\n   * @param visualizations Visualization data\r\n   * @param format Export format\r\n   * @returns Output file path\r\n   */\r\n  private async exportReport(\r\n    report: Report,\r\n    processedData: any,\r\n    visualizations: any,\r\n    format: string,\r\n  ): Promise<string> {\r\n    return await this.reportExportService.exportReport(\r\n      {\r\n        report,\r\n        data: processedData,\r\n        visualizations,\r\n      },\r\n      format,\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get file size in bytes\r\n   * @param filePath File path\r\n   * @returns File size in bytes\r\n   */\r\n  private async getFileSize(filePath: string): Promise<number> {\r\n    try {\r\n      const fs = require('fs').promises;\r\n      const stats = await fs.stat(filePath);\r\n      return stats.size;\r\n    } catch (error) {\r\n      this.logger.warn('Failed to get file size', {\r\n        filePath,\r\n        error: error.message,\r\n      });\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get time range description\r\n   * @param report Report configuration\r\n   * @param parameters Execution parameters\r\n   * @returns Time range description\r\n   */\r\n  private getTimeRangeDescription(report: Report, parameters?: any): string {\r\n    const timeRange = parameters?.timeRange || report.configuration.timeRange;\r\n    \r\n    if (!timeRange) {\r\n      return 'All time';\r\n    }\r\n\r\n    if (timeRange.type === 'relative') {\r\n      return `Last ${timeRange.value}`;\r\n    } else {\r\n      return `${timeRange.startDate} to ${timeRange.endDate}`;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Group data by specified fields\r\n   * @param data Data to group\r\n   * @param groupBy Fields to group by\r\n   * @returns Grouped data\r\n   */\r\n  private groupData(data: any[], groupBy: string[]): any {\r\n    if (!Array.isArray(data) || groupBy.length === 0) {\r\n      return data;\r\n    }\r\n\r\n    const grouped = data.reduce((acc, item) => {\r\n      const key = groupBy.map(field => item[field]).join('|');\r\n      if (!acc[key]) {\r\n        acc[key] = [];\r\n      }\r\n      acc[key].push(item);\r\n      return acc;\r\n    }, {});\r\n\r\n    return Object.entries(grouped).map(([key, items]) => ({\r\n      group: key,\r\n      count: (items as any[]).length,\r\n      items,\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Apply aggregations to data\r\n   * @param data Data to aggregate\r\n   * @param aggregations Aggregation configurations\r\n   * @returns Aggregated metrics\r\n   */\r\n  private aggregateData(data: any[], aggregations: any[]): any[] {\r\n    if (!Array.isArray(data) || aggregations.length === 0) {\r\n      return [];\r\n    }\r\n\r\n    return aggregations.map(agg => {\r\n      let value: any;\r\n      const field = agg.field;\r\n\r\n      switch (agg.function) {\r\n        case 'count':\r\n          value = data.length;\r\n          break;\r\n        case 'sum':\r\n          value = data.reduce((sum, item) => sum + (Number(item[field]) || 0), 0);\r\n          break;\r\n        case 'avg':\r\n          const sum = data.reduce((sum, item) => sum + (Number(item[field]) || 0), 0);\r\n          value = data.length > 0 ? sum / data.length : 0;\r\n          break;\r\n        case 'min':\r\n          value = Math.min(...data.map(item => Number(item[field]) || 0));\r\n          break;\r\n        case 'max':\r\n          value = Math.max(...data.map(item => Number(item[field]) || 0));\r\n          break;\r\n        default:\r\n          value = 0;\r\n      }\r\n\r\n      return {\r\n        id: agg.alias || `${agg.function}_${field}`,\r\n        title: agg.alias || `${agg.function.toUpperCase()} of ${field}`,\r\n        value,\r\n        unit: agg.unit,\r\n      };\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Generate chart data\r\n   * @param vizConfig Visualization configuration\r\n   * @param processedData Processed data\r\n   * @returns Chart data\r\n   */\r\n  private async generateChart(vizConfig: any, processedData: any): Promise<any> {\r\n    // This is a simplified chart generation\r\n    // In a real implementation, this would use a charting library\r\n    const dataSource = processedData.tables?.find(t => t.id.includes(vizConfig.dataSource));\r\n    \r\n    if (!dataSource) {\r\n      return { labels: [], datasets: [] };\r\n    }\r\n\r\n    const labels = dataSource.rows.map((row, index) => row[0] || `Item ${index + 1}`);\r\n    const values = dataSource.rows.map(row => Number(row[1]) || 0);\r\n\r\n    return {\r\n      labels,\r\n      datasets: [{\r\n        label: vizConfig.title,\r\n        data: values,\r\n        backgroundColor: vizConfig.configuration?.colors || ['#3498db'],\r\n      }],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get report execution by ID\r\n   * @param executionId Execution ID\r\n   * @returns Report execution\r\n   */\r\n  async getExecution(executionId: string): Promise<ReportExecution | null> {\r\n    try {\r\n      return await this.executionRepository.findOne({\r\n        where: { id: executionId },\r\n        relations: ['report'],\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to get report execution', {\r\n        executionId,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cancel running report execution\r\n   * @param executionId Execution ID\r\n   * @param userId User cancelling the execution\r\n   * @returns Updated execution\r\n   */\r\n  async cancelExecution(executionId: string, userId: string): Promise<ReportExecution> {\r\n    try {\r\n      const execution = await this.getExecution(executionId);\r\n      if (!execution) {\r\n        throw new NotFoundException('Report execution not found');\r\n      }\r\n\r\n      if (!execution.isRunning) {\r\n        throw new BadRequestException('Report execution is not running');\r\n      }\r\n\r\n      execution.cancel('Cancelled by user');\r\n      const updatedExecution = await this.executionRepository.save(execution);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'cancel',\r\n        'report_execution',\r\n        executionId,\r\n        {\r\n          reportId: execution.reportId,\r\n          reason: 'user_request',\r\n        },\r\n      );\r\n\r\n      this.logger.log('Report execution cancelled', {\r\n        executionId,\r\n        userId,\r\n      });\r\n\r\n      return updatedExecution;\r\n    } catch (error) {\r\n      this.logger.error('Failed to cancel report execution', {\r\n        executionId,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}