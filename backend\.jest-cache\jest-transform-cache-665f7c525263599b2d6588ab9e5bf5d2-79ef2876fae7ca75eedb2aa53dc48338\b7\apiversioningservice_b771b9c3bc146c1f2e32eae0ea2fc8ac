6b7d0df612a4044c931b9397fa9df8a6
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ApiVersioningService_1;
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiVersioningService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const logger_service_1 = require("../../infrastructure/logging/logger.service");
const distributed_cache_service_1 = require("../../infrastructure/cache/distributed-cache.service");
/**
 * API versioning service providing comprehensive version management
 * Handles version detection, compatibility checks, and deprecation warnings
 */
let ApiVersioningService = ApiVersioningService_1 = class ApiVersioningService {
    constructor(configService, loggerService, cacheService) {
        this.configService = configService;
        this.loggerService = loggerService;
        this.cacheService = cacheService;
        this.logger = new common_1.Logger(ApiVersioningService_1.name);
        this.supportedVersions = new Map();
        this.versionMappings = new Map();
        this.deprecationWarnings = new Map();
        this.initializeVersions();
    }
    /**
     * Extract API version from request
     */
    extractVersion(request) {
        // Check header first (preferred method)
        const headerVersion = request.headers['api-version'];
        if (headerVersion) {
            return this.normalizeVersion(headerVersion);
        }
        // Check URL path
        const pathMatch = request.path.match(/^\/api\/v(\d+(?:\.\d+)?)/);
        if (pathMatch) {
            return this.normalizeVersion(pathMatch[1]);
        }
        // Check query parameter
        const queryVersion = request.query['api-version'];
        if (queryVersion) {
            return this.normalizeVersion(queryVersion);
        }
        // Check Accept header for version
        const acceptHeader = request.headers.accept;
        if (acceptHeader) {
            const versionMatch = acceptHeader.match(/application\/vnd\.sentinel\.v(\d+(?:\.\d+)?)/);
            if (versionMatch) {
                return this.normalizeVersion(versionMatch[1]);
            }
        }
        // Default to latest stable version
        return this.getLatestStableVersion();
    }
    /**
     * Validate API version
     */
    validateVersion(version) {
        const normalizedVersion = this.normalizeVersion(version);
        return this.supportedVersions.has(normalizedVersion);
    }
    /**
     * Get version information
     */
    getVersionInfo(version) {
        const normalizedVersion = this.normalizeVersion(version);
        return this.supportedVersions.get(normalizedVersion) || null;
    }
    /**
     * Check if version is deprecated
     */
    isVersionDeprecated(version) {
        const versionInfo = this.getVersionInfo(version);
        return versionInfo?.status === 'deprecated' || versionInfo?.status === 'sunset';
    }
    /**
     * Get deprecation warnings for version
     */
    getDeprecationWarnings(version, endpoint) {
        const normalizedVersion = this.normalizeVersion(version);
        const warnings = this.deprecationWarnings.get(normalizedVersion) || [];
        if (endpoint) {
            return warnings.filter(warning => !warning.endpoint || warning.endpoint === endpoint);
        }
        return warnings;
    }
    /**
     * Check version compatibility
     */
    checkCompatibility(sourceVersion, targetVersion) {
        const source = this.getVersionInfo(sourceVersion);
        const target = this.getVersionInfo(targetVersion);
        if (!source || !target) {
            throw new common_1.BadRequestException('Invalid version specified');
        }
        const compatibility = {
            sourceVersion,
            targetVersion,
            compatible: this.areVersionsCompatible(sourceVersion, targetVersion),
            breakingChanges: this.getBreakingChangesBetweenVersions(sourceVersion, targetVersion),
            migrationRequired: false,
        };
        compatibility.migrationRequired = compatibility.breakingChanges.length > 0;
        if (compatibility.migrationRequired) {
            compatibility.migrationSteps = this.getMigrationSteps(sourceVersion, targetVersion);
        }
        return compatibility;
    }
    /**
     * Get supported versions
     */
    getSupportedVersions() {
        return Array.from(this.supportedVersions.values())
            .sort((a, b) => this.compareVersions(b.version, a.version));
    }
    /**
     * Get latest stable version
     */
    getLatestStableVersion() {
        const activeVersions = Array.from(this.supportedVersions.values())
            .filter(v => v.status === 'active')
            .sort((a, b) => this.compareVersions(b.version, a.version));
        return activeVersions[0]?.version || '2.0';
    }
    /**
     * Transform response for version compatibility
     */
    async transformResponse(data, targetVersion, sourceVersion) {
        if (!sourceVersion) {
            sourceVersion = this.getLatestStableVersion();
        }
        if (sourceVersion === targetVersion) {
            return data;
        }
        const cacheKey = `response_transform_${sourceVersion}_${targetVersion}_${JSON.stringify(data).substring(0, 100)}`;
        try {
            // Check cache first
            const cached = await this.cacheService.get(cacheKey);
            if (cached) {
                return cached;
            }
            // Apply version-specific transformations
            const transformed = await this.applyVersionTransformations(data, sourceVersion, targetVersion);
            // Cache for 5 minutes
            await this.cacheService.set(cacheKey, transformed, { ttl: 300 });
            return transformed;
        }
        catch (error) {
            this.loggerService.error('Response transformation failed', {
                sourceVersion,
                targetVersion,
                error: error.message,
            });
            return data; // Return original data on transformation failure
        }
    }
    /**
     * Generate version migration guide
     */
    generateMigrationGuide(fromVersion, toVersion) {
        const fromInfo = this.getVersionInfo(fromVersion);
        const toInfo = this.getVersionInfo(toVersion);
        if (!fromInfo || !toInfo) {
            throw new common_1.BadRequestException('Invalid version specified for migration guide');
        }
        return {
            overview: `Migration guide from API v${fromVersion} to v${toVersion}`,
            breakingChanges: this.getDetailedBreakingChanges(fromVersion, toVersion),
            newFeatures: toInfo.features.filter(f => !fromInfo.features.includes(f)),
            deprecatedFeatures: this.getDeprecatedFeatures(fromVersion, toVersion),
            migrationSteps: this.getDetailedMigrationSteps(fromVersion, toVersion),
        };
    }
    /**
     * Log version usage for analytics
     */
    async logVersionUsage(version, endpoint, userId) {
        try {
            const logData = {
                version,
                endpoint,
                userId,
                timestamp: new Date(),
                userAgent: 'unknown', // Would be extracted from request
            };
            // Log for analytics
            this.logger.log('API version usage', logData);
            // Store in cache for analytics aggregation
            const usageKey = `version_usage_${version}_${new Date().toISOString().split('T')[0]}`;
            const currentUsage = await this.cacheService.get(usageKey) || 0;
            await this.cacheService.set(usageKey, currentUsage + 1, { ttl: 86400 }); // 24 hours
        }
        catch (error) {
            this.logger.error('Failed to log version usage', { error: error.message });
        }
    }
    /**
     * Get version usage statistics
     */
    async getVersionUsageStats(timeRange = '7d') {
        try {
            // This would typically query a proper analytics database
            // For now, return mock data
            return {
                totalRequests: 10000,
                versionDistribution: {
                    '2.0': 7500,
                    '1.5': 2000,
                    '1.0': 500,
                },
                topEndpoints: [
                    { endpoint: '/api/v2/events', count: 2500, version: '2.0' },
                    { endpoint: '/api/v2/vulnerabilities', count: 2000, version: '2.0' },
                    { endpoint: '/api/v1/assets', count: 1500, version: '1.0' },
                ],
                deprecatedVersionUsage: {
                    '1.0': 500,
                    '1.5': 2000,
                },
            };
        }
        catch (error) {
            this.loggerService.error('Failed to get version usage stats', { error: error.message });
            return {
                totalRequests: 0,
                versionDistribution: {},
                topEndpoints: [],
                deprecatedVersionUsage: {},
            };
        }
    }
    // Private helper methods
    initializeVersions() {
        const versions = [
            {
                version: '1.0',
                status: 'sunset',
                releaseDate: new Date('2023-01-01'),
                deprecationDate: new Date('2024-01-01'),
                sunsetDate: new Date('2024-06-01'),
                features: ['basic-events', 'basic-assets', 'basic-vulnerabilities'],
                breakingChanges: [],
            },
            {
                version: '1.5',
                status: 'deprecated',
                releaseDate: new Date('2023-06-01'),
                deprecationDate: new Date('2024-06-01'),
                sunsetDate: new Date('2025-01-01'),
                features: ['basic-events', 'basic-assets', 'basic-vulnerabilities', 'threat-intelligence', 'basic-correlation'],
                breakingChanges: ['event-schema-change', 'asset-id-format-change'],
            },
            {
                version: '2.0',
                status: 'active',
                releaseDate: new Date('2024-01-01'),
                features: [
                    'advanced-events',
                    'advanced-assets',
                    'advanced-vulnerabilities',
                    'threat-intelligence',
                    'advanced-correlation',
                    'real-time-streaming',
                    'advanced-analytics',
                    'ai-ml-integration',
                    'incident-response',
                    'compliance-audit',
                ],
                breakingChanges: [
                    'complete-api-redesign',
                    'new-authentication-model',
                    'restructured-response-format',
                    'new-error-handling',
                ],
            },
        ];
        versions.forEach(version => {
            this.supportedVersions.set(version.version, version);
        });
        // Initialize version mappings
        this.versionMappings.set('1', '1.0');
        this.versionMappings.set('1.0', '1.0');
        this.versionMappings.set('1.5', '1.5');
        this.versionMappings.set('2', '2.0');
        this.versionMappings.set('2.0', '2.0');
        // Initialize deprecation warnings
        this.initializeDeprecationWarnings();
        this.logger.log('API versions initialized', {
            supportedVersions: Array.from(this.supportedVersions.keys()),
            latestVersion: this.getLatestStableVersion(),
        });
    }
    initializeDeprecationWarnings() {
        // Version 1.0 warnings
        this.deprecationWarnings.set('1.0', [
            {
                version: '1.0',
                deprecationDate: new Date('2024-01-01'),
                sunsetDate: new Date('2024-06-01'),
                replacement: 'v2.0',
                migrationGuide: '/docs/migration/v1-to-v2',
                severity: 'critical',
            },
        ]);
        // Version 1.5 warnings
        this.deprecationWarnings.set('1.5', [
            {
                version: '1.5',
                deprecationDate: new Date('2024-06-01'),
                sunsetDate: new Date('2025-01-01'),
                replacement: 'v2.0',
                migrationGuide: '/docs/migration/v1.5-to-v2',
                severity: 'warning',
            },
            {
                version: '1.5',
                endpoint: '/api/v1.5/events/legacy',
                feature: 'Legacy event format',
                deprecationDate: new Date('2024-03-01'),
                replacement: '/api/v2/events',
                severity: 'warning',
            },
        ]);
    }
    normalizeVersion(version) {
        // Remove 'v' prefix if present
        const cleanVersion = version.replace(/^v/, '');
        // Map to full version if needed
        return this.versionMappings.get(cleanVersion) || cleanVersion;
    }
    compareVersions(a, b) {
        const aParts = a.split('.').map(Number);
        const bParts = b.split('.').map(Number);
        for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
            const aPart = aParts[i] || 0;
            const bPart = bParts[i] || 0;
            if (aPart > bPart)
                return 1;
            if (aPart < bPart)
                return -1;
        }
        return 0;
    }
    areVersionsCompatible(sourceVersion, targetVersion) {
        // Major version changes are not compatible
        const sourceMajor = parseInt(sourceVersion.split('.')[0]);
        const targetMajor = parseInt(targetVersion.split('.')[0]);
        return sourceMajor === targetMajor;
    }
    getBreakingChangesBetweenVersions(fromVersion, toVersion) {
        const toInfo = this.getVersionInfo(toVersion);
        return toInfo?.breakingChanges || [];
    }
    getMigrationSteps(fromVersion, toVersion) {
        // Return version-specific migration steps
        if (fromVersion === '1.0' && toVersion === '2.0') {
            return [
                'Update authentication to use JWT tokens',
                'Migrate to new response format',
                'Update error handling',
                'Migrate event schema',
                'Update asset ID format',
            ];
        }
        if (fromVersion === '1.5' && toVersion === '2.0') {
            return [
                'Update authentication headers',
                'Migrate to new response format',
                'Update error handling',
            ];
        }
        return [];
    }
    async applyVersionTransformations(data, fromVersion, toVersion) {
        // Apply version-specific data transformations
        let transformed = { ...data };
        // Example transformations
        if (fromVersion === '1.0' && toVersion === '2.0') {
            // Transform v1.0 format to v2.0
            transformed = this.transformV1ToV2(transformed);
        }
        else if (fromVersion === '1.5' && toVersion === '2.0') {
            // Transform v1.5 format to v2.0
            transformed = this.transformV15ToV2(transformed);
        }
        return transformed;
    }
    transformV1ToV2(data) {
        // Example transformation from v1.0 to v2.0
        if (data.events) {
            data.events = data.events.map((event) => ({
                ...event,
                metadata: event.meta || {},
                timestamp: event.time || event.timestamp,
            }));
        }
        return data;
    }
    transformV15ToV2(data) {
        // Example transformation from v1.5 to v2.0
        if (data.pagination) {
            data.pagination = {
                ...data.pagination,
                hasMore: data.pagination.has_more,
            };
            delete data.pagination.has_more;
        }
        return data;
    }
    getDetailedBreakingChanges(fromVersion, toVersion) {
        // Return detailed breaking changes with solutions
        return [
            {
                change: 'Authentication model changed',
                impact: 'API keys no longer supported',
                solution: 'Migrate to JWT token authentication',
                example: 'Authorization: Bearer <jwt-token>',
            },
            {
                change: 'Response format restructured',
                impact: 'Response wrapper changed',
                solution: 'Update response parsing logic',
                example: '{ "success": true, "data": {...}, "metadata": {...} }',
            },
        ];
    }
    getDeprecatedFeatures(fromVersion, toVersion) {
        return [
            {
                feature: 'Legacy event format',
                replacement: 'New structured event format',
                timeline: 'Deprecated in v1.5, removed in v2.0',
            },
        ];
    }
    getDetailedMigrationSteps(fromVersion, toVersion) {
        return [
            {
                step: 1,
                title: 'Update Authentication',
                description: 'Replace API key authentication with JWT tokens',
                code: `// Old (v1.x)
fetch('/api/v1/events', {
  headers: { 'X-API-Key': 'your-api-key' }
});

// New (v2.0)
fetch('/api/v2/events', {
  headers: { 'Authorization': 'Bearer your-jwt-token' }
});`,
            },
            {
                step: 2,
                title: 'Update Response Handling',
                description: 'Adapt to new response format structure',
                code: `// Old (v1.x)
const events = response.events;

// New (v2.0)
const events = response.data.events;`,
            },
        ];
    }
};
exports.ApiVersioningService = ApiVersioningService;
exports.ApiVersioningService = ApiVersioningService = ApiVersioningService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _b : Object, typeof (_c = typeof distributed_cache_service_1.DistributedCacheService !== "undefined" && distributed_cache_service_1.DistributedCacheService) === "function" ? _c : Object])
], ApiVersioningService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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