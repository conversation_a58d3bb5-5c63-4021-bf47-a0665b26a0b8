66e4ac71bbea4b8b0218c46f8bde5d34
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const event_received_domain_event_1 = require("../event-received.domain-event");
const shared_kernel_1 = require("../../../../../shared-kernel");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const event_source_value_object_1 = require("../../value-objects/event-metadata/event-source.value-object");
const event_timestamp_value_object_1 = require("../../value-objects/event-metadata/event-timestamp.value-object");
describe('EventReceivedDomainEvent', () => {
    let aggregateId;
    let eventData;
    beforeEach(() => {
        aggregateId = shared_kernel_1.UniqueEntityId.generate();
        eventData = {
            eventType: event_type_enum_1.EventType.MALWARE_DETECTED,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            source: event_source_value_object_1.EventSource.create({
                type: 'endpoint_detection',
                identifier: 'edr-sensor-001',
                name: 'EDR Sensor 001',
                version: '2.1.0'
            }),
            receivedAt: event_timestamp_value_object_1.EventTimestamp.now(),
            originalTimestamp: event_timestamp_value_object_1.EventTimestamp.fromDate(new Date(Date.now() - 5000)),
            rawData: {
                malware_name: 'Trojan.Win32.Test',
                file_path: 'C:\\temp\\malicious.exe',
                process_id: 1234,
                user: 'SYSTEM'
            },
            title: 'Malware detected on endpoint',
            dataSize: 2048,
            priority: 'high'
        };
    });
    describe('constructor', () => {
        it('should create event with valid data', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            expect(event.aggregateId).toEqual(aggregateId);
            expect(event.eventType).toBe(event_type_enum_1.EventType.MALWARE_DETECTED);
            expect(event.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
            expect(event.title).toBe('Malware detected on endpoint');
            expect(event.priority).toBe('high');
            expect(event.dataSize).toBe(2048);
        });
        it('should set correct metadata', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            expect(event.metadata.eventType).toBe('EventReceived');
            expect(event.metadata.domain).toBe('Security');
            expect(event.metadata.aggregateType).toBe('Event');
            expect(event.metadata.processingStage).toBe('intake');
        });
        it('should accept custom options', () => {
            const correlationId = 'test-correlation-id';
            const customMetadata = { customField: 'customValue' };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData, {
                correlationId,
                metadata: customMetadata
            });
            expect(event.correlationId).toBe(correlationId);
            expect(event.metadata.customField).toBe('customValue');
        });
    });
    describe('priority checks', () => {
        it('should identify high priority events', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            expect(event.isHighPriority()).toBe(true);
        });
        it('should identify critical priority events', () => {
            const criticalEventData = { ...eventData, priority: 'critical' };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, criticalEventData);
            expect(event.isCriticalPriority()).toBe(true);
            expect(event.isHighPriority()).toBe(true);
        });
        it('should identify normal priority events', () => {
            const normalEventData = { ...eventData, priority: 'normal' };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, normalEventData);
            expect(event.isHighPriority()).toBe(false);
            expect(event.isCriticalPriority()).toBe(false);
        });
    });
    describe('severity checks', () => {
        it('should identify high severity events', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            expect(event.isHighSeverity()).toBe(true);
        });
        it('should identify critical severity events', () => {
            const criticalEventData = { ...eventData, severity: event_severity_enum_1.EventSeverity.CRITICAL };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, criticalEventData);
            expect(event.isCriticalSeverity()).toBe(true);
            expect(event.isHighSeverity()).toBe(true);
        });
        it('should identify medium severity events', () => {
            const mediumEventData = { ...eventData, severity: event_severity_enum_1.EventSeverity.MEDIUM };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, mediumEventData);
            expect(event.isHighSeverity()).toBe(false);
            expect(event.isCriticalSeverity()).toBe(false);
        });
    });
    describe('data size checks', () => {
        it('should identify large events', () => {
            const largeEventData = { ...eventData, dataSize: 2 * 1024 * 1024 }; // 2MB
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, largeEventData);
            expect(event.isLargeEvent()).toBe(true);
        });
        it('should identify normal size events', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            expect(event.isLargeEvent()).toBe(false);
        });
    });
    describe('staleness checks', () => {
        it('should identify stale events', () => {
            const staleEventData = {
                ...eventData,
                originalTimestamp: event_timestamp_value_object_1.EventTimestamp.fromDate(new Date(Date.now() - 10 * 60 * 1000)) // 10 minutes ago
            };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, staleEventData);
            expect(event.isStaleEvent()).toBe(true);
        });
        it('should identify fresh events', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            expect(event.isStaleEvent()).toBe(false);
        });
    });
    describe('processing latency', () => {
        it('should calculate processing latency correctly', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            const latency = event.getProcessingLatency();
            expect(latency).toBeGreaterThanOrEqual(5000); // At least 5 seconds
            expect(latency).toBeLessThan(10000); // Less than 10 seconds
        });
        it('should calculate processing latency in seconds', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            const latencySeconds = event.getProcessingLatencySeconds();
            expect(latencySeconds).toBeGreaterThanOrEqual(5);
            expect(latencySeconds).toBeLessThan(10);
        });
    });
    describe('timeout recommendations', () => {
        it('should recommend short timeout for critical priority', () => {
            const criticalEventData = { ...eventData, priority: 'critical' };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, criticalEventData);
            expect(event.getRecommendedTimeout()).toBe(1);
        });
        it('should recommend medium timeout for high priority', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            expect(event.getRecommendedTimeout()).toBe(5);
        });
        it('should recommend long timeout for normal priority', () => {
            const normalEventData = { ...eventData, priority: 'normal' };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, normalEventData);
            expect(event.getRecommendedTimeout()).toBe(30);
        });
    });
    describe('processing queue assignment', () => {
        it('should assign critical events to critical queue', () => {
            const criticalEventData = { ...eventData, priority: 'critical' };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, criticalEventData);
            expect(event.getProcessingQueue()).toBe('critical-events');
        });
        it('should assign high priority events to high priority queue', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            expect(event.getProcessingQueue()).toBe('high-priority-events');
        });
        it('should assign high severity events to high severity queue', () => {
            const highSeverityEventData = {
                ...eventData,
                priority: 'normal',
                severity: event_severity_enum_1.EventSeverity.HIGH
            };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, highSeverityEventData);
            expect(event.getProcessingQueue()).toBe('high-severity-events');
        });
        it('should assign normal events to standard queue', () => {
            const normalEventData = {
                ...eventData,
                priority: 'normal',
                severity: event_severity_enum_1.EventSeverity.MEDIUM
            };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, normalEventData);
            expect(event.getProcessingQueue()).toBe('standard-events');
        });
    });
    describe('resource requirements', () => {
        it('should require high resources for critical priority events', () => {
            const criticalEventData = { ...eventData, priority: 'critical' };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, criticalEventData);
            const resources = event.getProcessingResources();
            expect(resources.cpu).toBe('high');
        });
        it('should require high memory for large events', () => {
            const largeEventData = { ...eventData, dataSize: 2 * 1024 * 1024 }; // 2MB
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, largeEventData);
            const resources = event.getProcessingResources();
            expect(resources.memory).toBe('high');
            expect(resources.storage).toBe('high');
        });
    });
    describe('quality indicators', () => {
        it('should assess good quality for complete events', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            const quality = event.getQualityIndicators();
            expect(quality.hasRequiredFields).toBe(true);
            expect(quality.hasValidTimestamp).toBe(true);
            expect(quality.hasValidSource).toBe(true);
            expect(quality.dataIntegrity).toBe('good');
            expect(quality.completeness).toBeGreaterThan(70);
        });
        it('should assess poor quality for incomplete events', () => {
            const incompleteEventData = {
                ...eventData,
                rawData: { incomplete: null }
            };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, incompleteEventData);
            const quality = event.getQualityIndicators();
            expect(quality.completeness).toBeLessThan(70);
        });
    });
    describe('next processing steps', () => {
        it('should include validation for all events', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            const steps = event.getNextProcessingSteps();
            expect(steps).toContain('validate_event_structure');
            expect(steps).toContain('normalize_event_format');
            expect(steps).toContain('route_to_processing_pipeline');
        });
        it('should include priority assessment for high severity events', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            const steps = event.getNextProcessingSteps();
            expect(steps).toContain('priority_threat_assessment');
        });
        it('should include stale event handling for old events', () => {
            const staleEventData = {
                ...eventData,
                originalTimestamp: event_timestamp_value_object_1.EventTimestamp.fromDate(new Date(Date.now() - 10 * 60 * 1000))
            };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, staleEventData);
            const steps = event.getNextProcessingSteps();
            expect(steps).toContain('stale_event_handling');
        });
        it('should include large event processing for big events', () => {
            const largeEventData = { ...eventData, dataSize: 2 * 1024 * 1024 };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, largeEventData);
            const steps = event.getNextProcessingSteps();
            expect(steps).toContain('large_event_processing');
        });
    });
    describe('integration event conversion', () => {
        it('should convert to integration event format', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            const integrationEvent = event.toIntegrationEvent();
            expect(integrationEvent.eventType).toBe('EventReceived');
            expect(integrationEvent.version).toBe('1.0');
            expect(integrationEvent.data.eventId).toBe(aggregateId.toString());
            expect(integrationEvent.data.event.type).toBe(event_type_enum_1.EventType.MALWARE_DETECTED);
            expect(integrationEvent.data.event.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
            expect(integrationEvent.data.processing.priority).toBe('high');
            expect(integrationEvent.data.processing.queue).toBe('high-priority-events');
        });
    });
    describe('JSON serialization', () => {
        it('should serialize to JSON correctly', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            const json = event.toJSON();
            expect(json.eventData).toEqual(eventData);
            expect(json.analysis.isHighPriority).toBe(true);
            expect(json.analysis.isHighSeverity).toBe(true);
            expect(json.analysis.processingQueue).toBe('high-priority-events');
        });
        it('should deserialize from JSON correctly', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            const json = event.toJSON();
            const deserializedEvent = event_received_domain_event_1.EventReceivedDomainEvent.fromJSON(json);
            expect(deserializedEvent.aggregateId.equals(event.aggregateId)).toBe(true);
            expect(deserializedEvent.eventType).toBe(event.eventType);
            expect(deserializedEvent.severity).toBe(event.severity);
            expect(deserializedEvent.title).toBe(event.title);
        });
    });
    describe('human-readable description', () => {
        it('should generate appropriate description for high priority events', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            const description = event.getDescription();
            expect(description).toContain('high severity');
            expect(description).toContain('malware_detected');
            expect(description).toContain('high priority');
            expect(description).toContain('edr-sensor-001');
        });
        it('should include stale indicator for old events', () => {
            const staleEventData = {
                ...eventData,
                originalTimestamp: event_timestamp_value_object_1.EventTimestamp.fromDate(new Date(Date.now() - 10 * 60 * 1000))
            };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, staleEventData);
            const description = event.getDescription();
            expect(description).toContain('[STALE]');
        });
        it('should include large indicator for big events', () => {
            const largeEventData = { ...eventData, dataSize: 2 * 1024 * 1024 };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, largeEventData);
            const description = event.getDescription();
            expect(description).toContain('[LARGE]');
        });
    });
    describe('event summary', () => {
        it('should generate comprehensive summary', () => {
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, eventData);
            const summary = event.getSummary();
            expect(summary.eventType).toBe('EventReceived');
            expect(summary.eventId).toBe(aggregateId.toString());
            expect(summary.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
            expect(summary.source).toBe('edr-sensor-001');
            expect(summary.priority).toBe('high');
            expect(summary.processingLatency).toBeGreaterThan(0);
            expect(summary.qualityScore).toBeGreaterThan(0);
        });
    });
    describe('edge cases', () => {
        it('should handle events without priority', () => {
            const noPriorityEventData = { ...eventData };
            delete noPriorityEventData.priority;
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, noPriorityEventData);
            expect(event.priority).toBeUndefined();
            expect(event.isHighPriority()).toBe(false);
            expect(event.getProcessingQueue()).toBe('high-severity-events'); // Falls back to severity
        });
        it('should handle events without data size', () => {
            const noSizeEventData = { ...eventData };
            delete noSizeEventData.dataSize;
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, noSizeEventData);
            expect(event.dataSize).toBeUndefined();
            expect(event.isLargeEvent()).toBe(false);
        });
        it('should handle events with empty raw data', () => {
            const emptyDataEventData = { ...eventData, rawData: {} };
            const event = new event_received_domain_event_1.EventReceivedDomainEvent(aggregateId, emptyDataEventData);
            const quality = event.getQualityIndicators();
            expect(quality.completeness).toBe(0);
            expect(quality.dataIntegrity).toBe('poor');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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