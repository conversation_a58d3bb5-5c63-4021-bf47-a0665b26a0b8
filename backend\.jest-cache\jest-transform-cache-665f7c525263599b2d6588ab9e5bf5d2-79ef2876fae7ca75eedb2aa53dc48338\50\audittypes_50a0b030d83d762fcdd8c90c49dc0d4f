c12febe05305f6c530ee0d6dd181d881
"use strict";
/**
 * Audit Types
 *
 * Common types and interfaces for audit logging across the application.
 * Provides consistent audit patterns and utilities.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditUtils = exports.AuditCategory = exports.AuditSeverity = exports.AuditResult = exports.AuditAction = void 0;
/**
 * Audit Action Types
 */
var AuditAction;
(function (AuditAction) {
    AuditAction["CREATE"] = "create";
    AuditAction["READ"] = "read";
    AuditAction["UPDATE"] = "update";
    AuditAction["DELETE"] = "delete";
    AuditAction["LOGIN"] = "login";
    AuditAction["LOGOUT"] = "logout";
    AuditAction["ACCESS"] = "access";
    AuditAction["EXPORT"] = "export";
    AuditAction["IMPORT"] = "import";
    AuditAction["APPROVE"] = "approve";
    AuditAction["REJECT"] = "reject";
    AuditAction["EXECUTE"] = "execute";
    AuditAction["CANCEL"] = "cancel";
    AuditAction["SUSPEND"] = "suspend";
    AuditAction["ACTIVATE"] = "activate";
    AuditAction["DEACTIVATE"] = "deactivate";
    AuditAction["CONFIGURE"] = "configure";
    AuditAction["BACKUP"] = "backup";
    AuditAction["RESTORE"] = "restore";
    AuditAction["PURGE"] = "purge";
    AuditAction["ARCHIVE"] = "archive";
    AuditAction["UNARCHIVE"] = "unarchive";
})(AuditAction || (exports.AuditAction = AuditAction = {}));
/**
 * Audit Result Status
 */
var AuditResult;
(function (AuditResult) {
    AuditResult["SUCCESS"] = "success";
    AuditResult["FAILURE"] = "failure";
    AuditResult["PARTIAL"] = "partial";
    AuditResult["PENDING"] = "pending";
    AuditResult["CANCELLED"] = "cancelled";
})(AuditResult || (exports.AuditResult = AuditResult = {}));
/**
 * Audit Severity Levels
 */
var AuditSeverity;
(function (AuditSeverity) {
    AuditSeverity["LOW"] = "low";
    AuditSeverity["MEDIUM"] = "medium";
    AuditSeverity["HIGH"] = "high";
    AuditSeverity["CRITICAL"] = "critical";
})(AuditSeverity || (exports.AuditSeverity = AuditSeverity = {}));
/**
 * Audit Categories
 */
var AuditCategory;
(function (AuditCategory) {
    AuditCategory["AUTHENTICATION"] = "authentication";
    AuditCategory["AUTHORIZATION"] = "authorization";
    AuditCategory["DATA_ACCESS"] = "data_access";
    AuditCategory["DATA_MODIFICATION"] = "data_modification";
    AuditCategory["SYSTEM_CONFIGURATION"] = "system_configuration";
    AuditCategory["SECURITY_EVENT"] = "security_event";
    AuditCategory["COMPLIANCE"] = "compliance";
    AuditCategory["PERFORMANCE"] = "performance";
    AuditCategory["ERROR"] = "error";
    AuditCategory["BUSINESS_PROCESS"] = "business_process";
})(AuditCategory || (exports.AuditCategory = AuditCategory = {}));
/**
 * Audit Utilities
 */
class AuditUtils {
    /**
     * Create an audit entry
     */
    static createEntry(action, result, description, options = {}) {
        const now = new Date();
        return {
            id: crypto.randomUUID(),
            timestamp: now,
            action,
            result,
            severity: options.severity || AuditUtils.inferSeverity(action, result),
            category: options.category || AuditUtils.inferCategory(action),
            description,
            system: {
                application: 'sentinel',
                ...options.system,
            },
            ...options,
        };
    }
    /**
     * Infer severity from action and result
     */
    static inferSeverity(action, result) {
        // High severity actions
        if ([
            AuditAction.DELETE,
            AuditAction.PURGE,
            AuditAction.EXECUTE,
            AuditAction.CONFIGURE,
        ].includes(action)) {
            return result === AuditResult.FAILURE ? AuditSeverity.CRITICAL : AuditSeverity.HIGH;
        }
        // Medium severity actions
        if ([
            AuditAction.CREATE,
            AuditAction.UPDATE,
            AuditAction.APPROVE,
            AuditAction.REJECT,
            AuditAction.SUSPEND,
            AuditAction.ACTIVATE,
            AuditAction.DEACTIVATE,
        ].includes(action)) {
            return result === AuditResult.FAILURE ? AuditSeverity.HIGH : AuditSeverity.MEDIUM;
        }
        // Low severity actions
        return result === AuditResult.FAILURE ? AuditSeverity.MEDIUM : AuditSeverity.LOW;
    }
    /**
     * Infer category from action
     */
    static inferCategory(action) {
        switch (action) {
            case AuditAction.LOGIN:
            case AuditAction.LOGOUT:
                return AuditCategory.AUTHENTICATION;
            case AuditAction.ACCESS:
                return AuditCategory.AUTHORIZATION;
            case AuditAction.READ:
            case AuditAction.EXPORT:
                return AuditCategory.DATA_ACCESS;
            case AuditAction.CREATE:
            case AuditAction.UPDATE:
            case AuditAction.DELETE:
            case AuditAction.IMPORT:
                return AuditCategory.DATA_MODIFICATION;
            case AuditAction.CONFIGURE:
            case AuditAction.BACKUP:
            case AuditAction.RESTORE:
                return AuditCategory.SYSTEM_CONFIGURATION;
            default:
                return AuditCategory.BUSINESS_PROCESS;
        }
    }
    /**
     * Calculate risk score based on various factors
     */
    static calculateRiskScore(entry) {
        let score = 0;
        // Base score by severity
        switch (entry.severity) {
            case AuditSeverity.CRITICAL:
                score += 40;
                break;
            case AuditSeverity.HIGH:
                score += 30;
                break;
            case AuditSeverity.MEDIUM:
                score += 20;
                break;
            case AuditSeverity.LOW:
                score += 10;
                break;
        }
        // Action risk
        const highRiskActions = [
            AuditAction.DELETE,
            AuditAction.PURGE,
            AuditAction.EXECUTE,
            AuditAction.CONFIGURE,
        ];
        if (entry.action && highRiskActions.includes(entry.action)) {
            score += 20;
        }
        // Result impact
        if (entry.result === AuditResult.FAILURE) {
            score += 15;
        }
        // User context risk factors
        if (entry.user) {
            // Privileged user
            if (entry.user.roles?.includes('admin') || entry.user.roles?.includes('superuser')) {
                score += 10;
            }
            // Unusual location (simplified check)
            if (entry.user.location && entry.user.ipAddress) {
                // This would typically involve geolocation analysis
                // For now, just add a small risk for remote access
                score += 5;
            }
        }
        // Time-based risk (off-hours access)
        if (entry.timestamp) {
            const hour = entry.timestamp.getHours();
            if (hour < 6 || hour > 22) {
                score += 10;
            }
        }
        // Ensure score is within bounds
        return Math.min(Math.max(score, 0), 100);
    }
    /**
     * Sanitize sensitive data from audit entry
     */
    static sanitizeEntry(entry, config) {
        if (config.includeSensitiveData) {
            return entry;
        }
        const sanitized = { ...entry };
        // Remove sensitive user information
        if (sanitized.user) {
            sanitized.user = {
                ...sanitized.user,
                email: sanitized.user.email ? AuditUtils.maskEmail(sanitized.user.email) : undefined,
                ipAddress: sanitized.user.ipAddress ? AuditUtils.maskIpAddress(sanitized.user.ipAddress) : undefined,
            };
        }
        // Remove sensitive data changes
        if (sanitized.changes && !config.includeDataChanges) {
            delete sanitized.changes;
        }
        else if (sanitized.changes) {
            sanitized.changes = AuditUtils.sanitizeDataChanges(sanitized.changes);
        }
        // Remove sensitive metadata
        if (sanitized.metadata) {
            sanitized.metadata = AuditUtils.sanitizeMetadata(sanitized.metadata);
        }
        return sanitized;
    }
    /**
     * Mask email address
     */
    static maskEmail(email) {
        const [local, domain] = email.split('@');
        if (local.length <= 2) {
            return `${local[0]}***@${domain}`;
        }
        return `${local.substring(0, 2)}***@${domain}`;
    }
    /**
     * Mask IP address
     */
    static maskIpAddress(ip) {
        const parts = ip.split('.');
        if (parts.length === 4) {
            return `${parts[0]}.${parts[1]}.***.***.`;
        }
        // IPv6 or other format
        return ip.substring(0, 8) + '***';
    }
    /**
     * Sanitize data changes
     */
    static sanitizeDataChanges(changes) {
        const sensitiveFields = ['password', 'token', 'secret', 'key', 'credential'];
        const sanitizeObject = (obj) => {
            const sanitized = {};
            Object.entries(obj).forEach(([key, value]) => {
                if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
                    sanitized[key] = '***REDACTED***';
                }
                else {
                    sanitized[key] = value;
                }
            });
            return sanitized;
        };
        return {
            ...changes,
            before: changes.before ? sanitizeObject(changes.before) : undefined,
            after: changes.after ? sanitizeObject(changes.after) : undefined,
        };
    }
    /**
     * Sanitize metadata
     */
    static sanitizeMetadata(metadata) {
        const sensitiveKeys = ['password', 'token', 'secret', 'key', 'credential', 'authorization'];
        const sanitized = {};
        Object.entries(metadata).forEach(([key, value]) => {
            if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
                sanitized[key] = '***REDACTED***';
            }
            else {
                sanitized[key] = value;
            }
        });
        return sanitized;
    }
    /**
     * Check if entry should be audited based on configuration
     */
    static shouldAudit(entry, config) {
        if (!config.enabled) {
            return false;
        }
        if (entry.action && !config.auditedActions.includes(entry.action)) {
            return false;
        }
        if (entry.category && !config.auditedCategories.includes(entry.category)) {
            return false;
        }
        if (entry.severity && AuditUtils.getSeverityLevel(entry.severity) < AuditUtils.getSeverityLevel(config.minimumSeverity)) {
            return false;
        }
        return true;
    }
    /**
     * Get numeric severity level for comparison
     */
    static getSeverityLevel(severity) {
        switch (severity) {
            case AuditSeverity.LOW:
                return 1;
            case AuditSeverity.MEDIUM:
                return 2;
            case AuditSeverity.HIGH:
                return 3;
            case AuditSeverity.CRITICAL:
                return 4;
            default:
                return 0;
        }
    }
    /**
     * Format audit entry for display
     */
    static formatEntry(entry) {
        const timestamp = entry.timestamp.toISOString();
        const user = entry.user?.username || entry.user?.userId || 'system';
        const resource = entry.resource ? `${entry.resource.resourceType}:${entry.resource.resourceId}` : 'N/A';
        return `[${timestamp}] ${user} ${entry.action} ${resource} - ${entry.result} (${entry.severity})`;
    }
    /**
     * Create audit query from parameters
     */
    static createQuery(params) {
        const query = {};
        if (params.startDate) {
            query.timestamp = { $gte: params.startDate };
        }
        if (params.endDate) {
            query.timestamp = { ...query.timestamp, $lte: params.endDate };
        }
        if (params.userId) {
            query['user.userId'] = params.userId;
        }
        if (params.actions && params.actions.length > 0) {
            query.action = { $in: params.actions };
        }
        if (params.results && params.results.length > 0) {
            query.result = { $in: params.results };
        }
        if (params.severities && params.severities.length > 0) {
            query.severity = { $in: params.severities };
        }
        if (params.categories && params.categories.length > 0) {
            query.category = { $in: params.categories };
        }
        if (params.resourceTypes && params.resourceTypes.length > 0) {
            query['resource.resourceType'] = { $in: params.resourceTypes };
        }
        if (params.resourceIds && params.resourceIds.length > 0) {
            query['resource.resourceId'] = { $in: params.resourceIds };
        }
        if (params.search) {
            query.$or = [
                { description: { $regex: params.search, $options: 'i' } },
                { message: { $regex: params.search, $options: 'i' } },
            ];
        }
        if (params.minRiskScore !== undefined || params.maxRiskScore !== undefined) {
            query.riskScore = {};
            if (params.minRiskScore !== undefined) {
                query.riskScore.$gte = params.minRiskScore;
            }
            if (params.maxRiskScore !== undefined) {
                query.riskScore.$lte = params.maxRiskScore;
            }
        }
        if (params.complianceTags && params.complianceTags.length > 0) {
            query.complianceTags = { $in: params.complianceTags };
        }
        if (params.tenantId) {
            query['system.tenantId'] = params.tenantId;
        }
        if (params.applications && params.applications.length > 0) {
            query['system.application'] = { $in: params.applications };
        }
        if (params.environments && params.environments.length > 0) {
            query['system.environment'] = { $in: params.environments };
        }
        return query;
    }
}
exports.AuditUtils = AuditUtils;
/**
 * Default audit configuration
 */
AuditUtils.DEFAULT_CONFIG = {
    enabled: true,
    auditedActions: Object.values(AuditAction),
    auditedCategories: Object.values(AuditCategory),
    minimumSeverity: AuditSeverity.LOW,
    includeSensitiveData: false,
    includeDataChanges: true,
    maxRetentionDays: 2555, // 7 years
    compressOldEntries: true,
    batchSize: 1000,
    enableAlerts: true,
    alertThresholds: {
        failureRate: 10,
        riskScore: 75,
        timeWindow: 60,
    },
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxzaGFyZWQta2VybmVsXFx0eXBlc1xcYXVkaXQudHlwZXMudHMiLCJtYXBwaW5ncyI6IjtBQUFBOzs7OztHQUtHOzs7QUFFSDs7R0FFRztBQUNILElBQVksV0F1Qlg7QUF2QkQsV0FBWSxXQUFXO0lBQ3JCLGdDQUFpQixDQUFBO0lBQ2pCLDRCQUFhLENBQUE7SUFDYixnQ0FBaUIsQ0FBQTtJQUNqQixnQ0FBaUIsQ0FBQTtJQUNqQiw4QkFBZSxDQUFBO0lBQ2YsZ0NBQWlCLENBQUE7SUFDakIsZ0NBQWlCLENBQUE7SUFDakIsZ0NBQWlCLENBQUE7SUFDakIsZ0NBQWlCLENBQUE7SUFDakIsa0NBQW1CLENBQUE7SUFDbkIsZ0NBQWlCLENBQUE7SUFDakIsa0NBQW1CLENBQUE7SUFDbkIsZ0NBQWlCLENBQUE7SUFDakIsa0NBQW1CLENBQUE7SUFDbkIsb0NBQXFCLENBQUE7SUFDckIsd0NBQXlCLENBQUE7SUFDekIsc0NBQXVCLENBQUE7SUFDdkIsZ0NBQWlCLENBQUE7SUFDakIsa0NBQW1CLENBQUE7SUFDbkIsOEJBQWUsQ0FBQTtJQUNmLGtDQUFtQixDQUFBO0lBQ25CLHNDQUF1QixDQUFBO0FBQ3pCLENBQUMsRUF2QlcsV0FBVywyQkFBWCxXQUFXLFFBdUJ0QjtBQUVEOztHQUVHO0FBQ0gsSUFBWSxXQU1YO0FBTkQsV0FBWSxXQUFXO0lBQ3JCLGtDQUFtQixDQUFBO0lBQ25CLGtDQUFtQixDQUFBO0lBQ25CLGtDQUFtQixDQUFBO0lBQ25CLGtDQUFtQixDQUFBO0lBQ25CLHNDQUF1QixDQUFBO0FBQ3pCLENBQUMsRUFOVyxXQUFXLDJCQUFYLFdBQVcsUUFNdEI7QUFFRDs7R0FFRztBQUNILElBQVksYUFLWDtBQUxELFdBQVksYUFBYTtJQUN2Qiw0QkFBVyxDQUFBO0lBQ1gsa0NBQWlCLENBQUE7SUFDakIsOEJBQWEsQ0FBQTtJQUNiLHNDQUFxQixDQUFBO0FBQ3ZCLENBQUMsRUFMVyxhQUFhLDZCQUFiLGFBQWEsUUFLeEI7QUFFRDs7R0FFRztBQUNILElBQVksYUFXWDtBQVhELFdBQVksYUFBYTtJQUN2QixrREFBaUMsQ0FBQTtJQUNqQyxnREFBK0IsQ0FBQTtJQUMvQiw0Q0FBMkIsQ0FBQTtJQUMzQix3REFBdUMsQ0FBQTtJQUN2Qyw4REFBNkMsQ0FBQTtJQUM3QyxrREFBaUMsQ0FBQTtJQUNqQywwQ0FBeUIsQ0FBQTtJQUN6Qiw0Q0FBMkIsQ0FBQTtJQUMzQixnQ0FBZSxDQUFBO0lBQ2Ysc0RBQXFDLENBQUE7QUFDdkMsQ0FBQyxFQVhXLGFBQWEsNkJBQWIsYUFBYSxRQVd4QjtBQTBQRDs7R0FFRztBQUNILE1BQWEsVUFBVTtJQXNCckI7O09BRUc7SUFDSCxNQUFNLENBQUMsV0FBVyxDQUNoQixNQUFtQixFQUNuQixNQUFtQixFQUNuQixXQUFtQixFQUNuQixVQUErQixFQUFFO1FBRWpDLE1BQU0sR0FBRyxHQUFHLElBQUksSUFBSSxFQUFFLENBQUM7UUFFdkIsT0FBTztZQUNMLEVBQUUsRUFBRSxNQUFNLENBQUMsVUFBVSxFQUFFO1lBQ3ZCLFNBQVMsRUFBRSxHQUFHO1lBQ2QsTUFBTTtZQUNOLE1BQU07WUFDTixRQUFRLEVBQUUsT0FBTyxDQUFDLFFBQVEsSUFBSSxVQUFVLENBQUMsYUFBYSxDQUFDLE1BQU0sRUFBRSxNQUFNLENBQUM7WUFDdEUsUUFBUSxFQUFFLE9BQU8sQ0FBQyxRQUFRLElBQUksVUFBVSxDQUFDLGFBQWEsQ0FBQyxNQUFNLENBQUM7WUFDOUQsV0FBVztZQUNYLE1BQU0sRUFBRTtnQkFDTixXQUFXLEVBQUUsVUFBVTtnQkFDdkIsR0FBRyxPQUFPLENBQUMsTUFBTTthQUNsQjtZQUNELEdBQUcsT0FBTztTQUNYLENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSCxNQUFNLENBQUMsYUFBYSxDQUFDLE1BQW1CLEVBQUUsTUFBbUI7UUFDM0Qsd0JBQXdCO1FBQ3hCLElBQUk7WUFDRixXQUFXLENBQUMsTUFBTTtZQUNsQixXQUFXLENBQUMsS0FBSztZQUNqQixXQUFXLENBQUMsT0FBTztZQUNuQixXQUFXLENBQUMsU0FBUztTQUN0QixDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDO1lBQ25CLE9BQU8sTUFBTSxLQUFLLFdBQVcsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUM7UUFDdEYsQ0FBQztRQUVELDBCQUEwQjtRQUMxQixJQUFJO1lBQ0YsV0FBVyxDQUFDLE1BQU07WUFDbEIsV0FBVyxDQUFDLE1BQU07WUFDbEIsV0FBVyxDQUFDLE9BQU87WUFDbkIsV0FBVyxDQUFDLE1BQU07WUFDbEIsV0FBVyxDQUFDLE9BQU87WUFDbkIsV0FBVyxDQUFDLFFBQVE7WUFDcEIsV0FBVyxDQUFDLFVBQVU7U0FDdkIsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLEVBQUUsQ0FBQztZQUNuQixPQUFPLE1BQU0sS0FBSyxXQUFXLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxhQUFhLENBQUMsTUFBTSxDQUFDO1FBQ3BGLENBQUM7UUFFRCx1QkFBdUI7UUFDdkIsT0FBTyxNQUFNLEtBQUssV0FBVyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsYUFBYSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsYUFBYSxDQUFDLEdBQUcsQ0FBQztJQUNuRixDQUFDO0lBRUQ7O09BRUc7SUFDSCxNQUFNLENBQUMsYUFBYSxDQUFDLE1BQW1CO1FBQ3RDLFFBQVEsTUFBTSxFQUFFLENBQUM7WUFDZixLQUFLLFdBQVcsQ0FBQyxLQUFLLENBQUM7WUFDdkIsS0FBSyxXQUFXLENBQUMsTUFBTTtnQkFDckIsT0FBTyxhQUFhLENBQUMsY0FBYyxDQUFDO1lBRXRDLEtBQUssV0FBVyxDQUFDLE1BQU07Z0JBQ3JCLE9BQU8sYUFBYSxDQUFDLGFBQWEsQ0FBQztZQUVyQyxLQUFLLFdBQVcsQ0FBQyxJQUFJLENBQUM7WUFDdEIsS0FBSyxXQUFXLENBQUMsTUFBTTtnQkFDckIsT0FBTyxhQUFhLENBQUMsV0FBVyxDQUFDO1lBRW5DLEtBQUssV0FBVyxDQUFDLE1BQU0sQ0FBQztZQUN4QixLQUFLLFdBQVcsQ0FBQyxNQUFNLENBQUM7WUFDeEIsS0FBSyxXQUFXLENBQUMsTUFBTSxDQUFDO1lBQ3hCLEtBQUssV0FBVyxDQUFDLE1BQU07Z0JBQ3JCLE9BQU8sYUFBYSxDQUFDLGlCQUFpQixDQUFDO1lBRXpDLEtBQUssV0FBVyxDQUFDLFNBQVMsQ0FBQztZQUMzQixLQUFLLFdBQVcsQ0FBQyxNQUFNLENBQUM7WUFDeEIsS0FBSyxXQUFXLENBQUMsT0FBTztnQkFDdEIsT0FBTyxhQUFhLENBQUMsb0JBQW9CLENBQUM7WUFFNUM7Z0JBQ0UsT0FBTyxhQUFhLENBQUMsZ0JBQWdCLENBQUM7UUFDMUMsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxLQUEwQjtRQUNsRCxJQUFJLEtBQUssR0FBRyxDQUFDLENBQUM7UUFFZCx5QkFBeUI7UUFDekIsUUFBUSxLQUFLLENBQUMsUUFBUSxFQUFFLENBQUM7WUFDdkIsS0FBSyxhQUFhLENBQUMsUUFBUTtnQkFDekIsS0FBSyxJQUFJLEVBQUUsQ0FBQztnQkFDWixNQUFNO1lBQ1IsS0FBSyxhQUFhLENBQUMsSUFBSTtnQkFDckIsS0FBSyxJQUFJLEVBQUUsQ0FBQztnQkFDWixNQUFNO1lBQ1IsS0FBSyxhQUFhLENBQUMsTUFBTTtnQkFDdkIsS0FBSyxJQUFJLEVBQUUsQ0FBQztnQkFDWixNQUFNO1lBQ1IsS0FBSyxhQUFhLENBQUMsR0FBRztnQkFDcEIsS0FBSyxJQUFJLEVBQUUsQ0FBQztnQkFDWixNQUFNO1FBQ1YsQ0FBQztRQUVELGNBQWM7UUFDZCxNQUFNLGVBQWUsR0FBRztZQUN0QixXQUFXLENBQUMsTUFBTTtZQUNsQixXQUFXLENBQUMsS0FBSztZQUNqQixXQUFXLENBQUMsT0FBTztZQUNuQixXQUFXLENBQUMsU0FBUztTQUN0QixDQUFDO1FBQ0YsSUFBSSxLQUFLLENBQUMsTUFBTSxJQUFJLGVBQWUsQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUM7WUFDM0QsS0FBSyxJQUFJLEVBQUUsQ0FBQztRQUNkLENBQUM7UUFFRCxnQkFBZ0I7UUFDaEIsSUFBSSxLQUFLLENBQUMsTUFBTSxLQUFLLFdBQVcsQ0FBQyxPQUFPLEVBQUUsQ0FBQztZQUN6QyxLQUFLLElBQUksRUFBRSxDQUFDO1FBQ2QsQ0FBQztRQUVELDRCQUE0QjtRQUM1QixJQUFJLEtBQUssQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUNmLGtCQUFrQjtZQUNsQixJQUFJLEtBQUssQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsSUFBSSxLQUFLLENBQUMsSUFBSSxDQUFDLEtBQUssRUFBRSxRQUFRLENBQUMsV0FBVyxDQUFDLEVBQUUsQ0FBQztnQkFDbkYsS0FBSyxJQUFJLEVBQUUsQ0FBQztZQUNkLENBQUM7WUFFRCxzQ0FBc0M7WUFDdEMsSUFBSSxLQUFLLENBQUMsSUFBSSxDQUFDLFFBQVEsSUFBSSxLQUFLLENBQUMsSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFDO2dCQUNoRCxvREFBb0Q7Z0JBQ3BELG1EQUFtRDtnQkFDbkQsS0FBSyxJQUFJLENBQUMsQ0FBQztZQUNiLENBQUM7UUFDSCxDQUFDO1FBRUQscUNBQXFDO1FBQ3JDLElBQUksS0FBSyxDQUFDLFNBQVMsRUFBRSxDQUFDO1lBQ3BCLE1BQU0sSUFBSSxHQUFHLEtBQUssQ0FBQyxTQUFTLENBQUMsUUFBUSxFQUFFLENBQUM7WUFDeEMsSUFBSSxJQUFJLEdBQUcsQ0FBQyxJQUFJLElBQUksR0FBRyxFQUFFLEVBQUUsQ0FBQztnQkFDMUIsS0FBSyxJQUFJLEVBQUUsQ0FBQztZQUNkLENBQUM7UUFDSCxDQUFDO1FBRUQsZ0NBQWdDO1FBQ2hDLE9BQU8sSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEtBQUssRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQztJQUMzQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxNQUFNLENBQUMsYUFBYSxDQUFDLEtBQWlCLEVBQUUsTUFBbUI7UUFDekQsSUFBSSxNQUFNLENBQUMsb0JBQW9CLEVBQUUsQ0FBQztZQUNoQyxPQUFPLEtBQUssQ0FBQztRQUNmLENBQUM7UUFFRCxNQUFNLFNBQVMsR0FBRyxFQUFFLEdBQUcsS0FBSyxFQUFFLENBQUM7UUFFL0Isb0NBQW9DO1FBQ3BDLElBQUksU0FBUyxDQUFDLElBQUksRUFBRSxDQUFDO1lBQ25CLFNBQVMsQ0FBQyxJQUFJLEdBQUc7Z0JBQ2YsR0FBRyxTQUFTLENBQUMsSUFBSTtnQkFDakIsS0FBSyxFQUFFLFNBQVMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxVQUFVLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVM7Z0JBQ3BGLFNBQVMsRUFBRSxTQUFTLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLGFBQWEsQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTO2FBQ3JHLENBQUM7UUFDSixDQUFDO1FBRUQsZ0NBQWdDO1FBQ2hDLElBQUksU0FBUyxDQUFDLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxrQkFBa0IsRUFBRSxDQUFDO1lBQ3BELE9BQU8sU0FBUyxDQUFDLE9BQU8sQ0FBQztRQUMzQixDQUFDO2FBQU0sSUFBSSxTQUFTLENBQUMsT0FBTyxFQUFFLENBQUM7WUFDN0IsU0FBUyxDQUFDLE9BQU8sR0FBRyxVQUFVLENBQUMsbUJBQW1CLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDO1FBQ3hFLENBQUM7UUFFRCw0QkFBNEI7UUFDNUIsSUFBSSxTQUFTLENBQUMsUUFBUSxFQUFFLENBQUM7WUFDdkIsU0FBUyxDQUFDLFFBQVEsR0FBRyxVQUFVLENBQUMsZ0JBQWdCLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ3ZFLENBQUM7UUFFRCxPQUFPLFNBQVMsQ0FBQztJQUNuQixDQUFDO0lBRUQ7O09BRUc7SUFDSyxNQUFNLENBQUMsU0FBUyxDQUFDLEtBQWE7UUFDcEMsTUFBTSxDQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsR0FBRyxLQUFLLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ3pDLElBQUksS0FBSyxDQUFDLE1BQU0sSUFBSSxDQUFDLEVBQUUsQ0FBQztZQUN0QixPQUFPLEdBQUcsS0FBSyxDQUFDLENBQUMsQ0FBQyxPQUFPLE1BQU0sRUFBRSxDQUFDO1FBQ3BDLENBQUM7UUFDRCxPQUFPLEdBQUcsS0FBSyxDQUFDLFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLE9BQU8sTUFBTSxFQUFFLENBQUM7SUFDakQsQ0FBQztJQUVEOztPQUVHO0lBQ0ssTUFBTSxDQUFDLGFBQWEsQ0FBQyxFQUFVO1FBQ3JDLE1BQU0sS0FBSyxHQUFHLEVBQUUsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDNUIsSUFBSSxLQUFLLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRSxDQUFDO1lBQ3ZCLE9BQU8sR0FBRyxLQUFLLENBQUMsQ0FBQyxDQUFDLElBQUksS0FBSyxDQUFDLENBQUMsQ0FBQyxXQUFXLENBQUM7UUFDNUMsQ0FBQztRQUNELHVCQUF1QjtRQUN2QixPQUFPLEVBQUUsQ0FBQyxTQUFTLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxHQUFHLEtBQUssQ0FBQztJQUNwQyxDQUFDO0lBRUQ7O09BRUc7SUFDSyxNQUFNLENBQUMsbUJBQW1CLENBQUMsT0FBeUI7UUFDMUQsTUFBTSxlQUFlLEdBQUcsQ0FBQyxVQUFVLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxLQUFLLEVBQUUsWUFBWSxDQUFDLENBQUM7UUFFN0UsTUFBTSxjQUFjLEdBQUcsQ0FBQyxHQUF3QixFQUF1QixFQUFFO1lBQ3ZFLE1BQU0sU0FBUyxHQUF3QixFQUFFLENBQUM7WUFFMUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBRSxLQUFLLENBQUMsRUFBRSxFQUFFO2dCQUMzQyxJQUFJLGVBQWUsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLEVBQUUsQ0FBQyxHQUFHLENBQUMsV0FBVyxFQUFFLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsQ0FBQztvQkFDckUsU0FBUyxDQUFDLEdBQUcsQ0FBQyxHQUFHLGdCQUFnQixDQUFDO2dCQUNwQyxDQUFDO3FCQUFNLENBQUM7b0JBQ04sU0FBUyxDQUFDLEdBQUcsQ0FBQyxHQUFHLEtBQUssQ0FBQztnQkFDekIsQ0FBQztZQUNILENBQUMsQ0FBQyxDQUFDO1lBRUgsT0FBTyxTQUFTLENBQUM7UUFDbkIsQ0FBQyxDQUFDO1FBRUYsT0FBTztZQUNMLEdBQUcsT0FBTztZQUNWLE1BQU0sRUFBRSxPQUFPLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxjQUFjLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTO1lBQ25FLEtBQUssRUFBRSxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxjQUFjLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTO1NBQ2pFLENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsUUFBNkI7UUFDM0QsTUFBTSxhQUFhLEdBQUcsQ0FBQyxVQUFVLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxLQUFLLEVBQUUsWUFBWSxFQUFFLGVBQWUsQ0FBQyxDQUFDO1FBQzVGLE1BQU0sU0FBUyxHQUF3QixFQUFFLENBQUM7UUFFMUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBRSxLQUFLLENBQUMsRUFBRSxFQUFFO1lBQ2hELElBQUksYUFBYSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLENBQUMsRUFBRSxDQUFDO2dCQUMzRSxTQUFTLENBQUMsR0FBRyxDQUFDLEdBQUcsZ0JBQWdCLENBQUM7WUFDcEMsQ0FBQztpQkFBTSxDQUFDO2dCQUNOLFNBQVMsQ0FBQyxHQUFHLENBQUMsR0FBRyxLQUFLLENBQUM7WUFDekIsQ0FBQztRQUNILENBQUMsQ0FBQyxDQUFDO1FBRUgsT0FBTyxTQUFTLENBQUM7SUFDbkIsQ0FBQztJQUVEOztPQUVHO0lBQ0gsTUFBTSxDQUFDLFdBQVcsQ0FBQyxLQUEwQixFQUFFLE1BQW1CO1FBQ2hFLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxFQUFFLENBQUM7WUFDcEIsT0FBTyxLQUFLLENBQUM7UUFDZixDQUFDO1FBRUQsSUFBSSxLQUFLLENBQUMsTUFBTSxJQUFJLENBQUMsTUFBTSxDQUFDLGNBQWMsQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUM7WUFDbEUsT0FBTyxLQUFLLENBQUM7UUFDZixDQUFDO1FBRUQsSUFBSSxLQUFLLENBQUMsUUFBUSxJQUFJLENBQUMsTUFBTSxDQUFDLGlCQUFpQixDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQztZQUN6RSxPQUFPLEtBQUssQ0FBQztRQUNmLENBQUM7UUFFRCxJQUFJLEtBQUssQ0FBQyxRQUFRLElBQUksVUFBVSxDQUFDLGdCQUFnQixDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsR0FBRyxVQUFVLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxDQUFDLGVBQWUsQ0FBQyxFQUFFLENBQUM7WUFDeEgsT0FBTyxLQUFLLENBQUM7UUFDZixDQUFDO1FBRUQsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQ7O09BRUc7SUFDSyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsUUFBdUI7UUFDckQsUUFBUSxRQUFRLEVBQUUsQ0FBQztZQUNqQixLQUFLLGFBQWEsQ0FBQyxHQUFHO2dCQUNwQixPQUFPLENBQUMsQ0FBQztZQUNYLEtBQUssYUFBYSxDQUFDLE1BQU07Z0JBQ3ZCLE9BQU8sQ0FBQyxDQUFDO1lBQ1gsS0FBSyxhQUFhLENBQUMsSUFBSTtnQkFDckIsT0FBTyxDQUFDLENBQUM7WUFDWCxLQUFLLGFBQWEsQ0FBQyxRQUFRO2dCQUN6QixPQUFPLENBQUMsQ0FBQztZQUNYO2dCQUNFLE9BQU8sQ0FBQyxDQUFDO1FBQ2IsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILE1BQU0sQ0FBQyxXQUFXLENBQUMsS0FBaUI7UUFDbEMsTUFBTSxTQUFTLEdBQUcsS0FBSyxDQUFDLFNBQVMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUNoRCxNQUFNLElBQUksR0FBRyxLQUFLLENBQUMsSUFBSSxFQUFFLFFBQVEsSUFBSSxLQUFLLENBQUMsSUFBSSxFQUFFLE1BQU0sSUFBSSxRQUFRLENBQUM7UUFDcEUsTUFBTSxRQUFRLEdBQUcsS0FBSyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsR0FBRyxLQUFLLENBQUMsUUFBUSxDQUFDLFlBQVksSUFBSSxLQUFLLENBQUMsUUFBUSxDQUFDLFVBQVUsRUFBRSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUM7UUFFeEcsT0FBTyxJQUFJLFNBQVMsS0FBSyxJQUFJLElBQUksS0FBSyxDQUFDLE1BQU0sSUFBSSxRQUFRLE1BQU0sS0FBSyxDQUFDLE1BQU0sS0FBSyxLQUFLLENBQUMsUUFBUSxHQUFHLENBQUM7SUFDcEcsQ0FBQztJQUVEOztPQUVHO0lBQ0gsTUFBTSxDQUFDLFdBQVcsQ0FBQyxNQUF3QjtRQUN6QyxNQUFNLEtBQUssR0FBd0IsRUFBRSxDQUFDO1FBRXRDLElBQUksTUFBTSxDQUFDLFNBQVMsRUFBRSxDQUFDO1lBQ3JCLEtBQUssQ0FBQyxTQUFTLEdBQUcsRUFBRSxJQUFJLEVBQUUsTUFBTSxDQUFDLFNBQVMsRUFBRSxDQUFDO1FBQy9DLENBQUM7UUFFRCxJQUFJLE1BQU0sQ0FBQyxPQUFPLEVBQUUsQ0FBQztZQUNuQixLQUFLLENBQUMsU0FBUyxHQUFHLEVBQUUsR0FBRyxLQUFLLENBQUMsU0FBUyxFQUFFLElBQUksRUFBRSxNQUFNLENBQUMsT0FBTyxFQUFFLENBQUM7UUFDakUsQ0FBQztRQUVELElBQUksTUFBTSxDQUFDLE1BQU0sRUFBRSxDQUFDO1lBQ2xCLEtBQUssQ0FBQyxhQUFhLENBQUMsR0FBRyxNQUFNLENBQUMsTUFBTSxDQUFDO1FBQ3ZDLENBQUM7UUFFRCxJQUFJLE1BQU0sQ0FBQyxPQUFPLElBQUksTUFBTSxDQUFDLE9BQU8sQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDaEQsS0FBSyxDQUFDLE1BQU0sR0FBRyxFQUFFLEdBQUcsRUFBRSxNQUFNLENBQUMsT0FBTyxFQUFFLENBQUM7UUFDekMsQ0FBQztRQUVELElBQUksTUFBTSxDQUFDLE9BQU8sSUFBSSxNQUFNLENBQUMsT0FBTyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztZQUNoRCxLQUFLLENBQUMsTUFBTSxHQUFHLEVBQUUsR0FBRyxFQUFFLE1BQU0sQ0FBQyxPQUFPLEVBQUUsQ0FBQztRQUN6QyxDQUFDO1FBRUQsSUFBSSxNQUFNLENBQUMsVUFBVSxJQUFJLE1BQU0sQ0FBQyxVQUFVLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1lBQ3RELEtBQUssQ0FBQyxRQUFRLEdBQUcsRUFBRSxHQUFHLEVBQUUsTUFBTSxDQUFDLFVBQVUsRUFBRSxDQUFDO1FBQzlDLENBQUM7UUFFRCxJQUFJLE1BQU0sQ0FBQyxVQUFVLElBQUksTUFBTSxDQUFDLFVBQVUsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDdEQsS0FBSyxDQUFDLFFBQVEsR0FBRyxFQUFFLEdBQUcsRUFBRSxNQUFNLENBQUMsVUFBVSxFQUFFLENBQUM7UUFDOUMsQ0FBQztRQUVELElBQUksTUFBTSxDQUFDLGFBQWEsSUFBSSxNQUFNLENBQUMsYUFBYSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztZQUM1RCxLQUFLLENBQUMsdUJBQXVCLENBQUMsR0FBRyxFQUFFLEdBQUcsRUFBRSxNQUFNLENBQUMsYUFBYSxFQUFFLENBQUM7UUFDakUsQ0FBQztRQUVELElBQUksTUFBTSxDQUFDLFdBQVcsSUFBSSxNQUFNLENBQUMsV0FBVyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztZQUN4RCxLQUFLLENBQUMscUJBQXFCLENBQUMsR0FBRyxFQUFFLEdBQUcsRUFBRSxNQUFNLENBQUMsV0FBVyxFQUFFLENBQUM7UUFDN0QsQ0FBQztRQUVELElBQUksTUFBTSxDQUFDLE1BQU0sRUFBRSxDQUFDO1lBQ2xCLEtBQUssQ0FBQyxHQUFHLEdBQUc7Z0JBQ1YsRUFBRSxXQUFXLEVBQUUsRUFBRSxNQUFNLEVBQUUsTUFBTSxDQUFDLE1BQU0sRUFBRSxRQUFRLEVBQUUsR0FBRyxFQUFFLEVBQUU7Z0JBQ3pELEVBQUUsT0FBTyxFQUFFLEVBQUUsTUFBTSxFQUFFLE1BQU0sQ0FBQyxNQUFNLEVBQUUsUUFBUSxFQUFFLEdBQUcsRUFBRSxFQUFFO2FBQ3RELENBQUM7UUFDSixDQUFDO1FBRUQsSUFBSSxNQUFNLENBQUMsWUFBWSxLQUFLLFNBQVMsSUFBSSxNQUFNLENBQUMsWUFBWSxLQUFLLFNBQVMsRUFBRSxDQUFDO1lBQzNFLEtBQUssQ0FBQyxTQUFTLEdBQUcsRUFBRSxDQUFDO1lBQ3JCLElBQUksTUFBTSxDQUFDLFlBQVksS0FBSyxTQUFTLEVBQUUsQ0FBQztnQkFDdEMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxJQUFJLEdBQUcsTUFBTSxDQUFDLFlBQVksQ0FBQztZQUM3QyxDQUFDO1lBQ0QsSUFBSSxNQUFNLENBQUMsWUFBWSxLQUFLLFNBQVMsRUFBRSxDQUFDO2dCQUN0QyxLQUFLLENBQUMsU0FBUyxDQUFDLElBQUksR0FBRyxNQUFNLENBQUMsWUFBWSxDQUFDO1lBQzdDLENBQUM7UUFDSCxDQUFDO1FBRUQsSUFBSSxNQUFNLENBQUMsY0FBYyxJQUFJLE1BQU0sQ0FBQyxjQUFjLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1lBQzlELEtBQUssQ0FBQyxjQUFjLEdBQUcsRUFBRSxHQUFHLEVBQUUsTUFBTSxDQUFDLGNBQWMsRUFBRSxDQUFDO1FBQ3hELENBQUM7UUFFRCxJQUFJLE1BQU0sQ0FBQyxRQUFRLEVBQUUsQ0FBQztZQUNwQixLQUFLLENBQUMsaUJBQWlCLENBQUMsR0FBRyxNQUFNLENBQUMsUUFBUSxDQUFDO1FBQzdDLENBQUM7UUFFRCxJQUFJLE1BQU0sQ0FBQyxZQUFZLElBQUksTUFBTSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDMUQsS0FBSyxDQUFDLG9CQUFvQixDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsTUFBTSxDQUFDLFlBQVksRUFBRSxDQUFDO1FBQzdELENBQUM7UUFFRCxJQUFJLE1BQU0sQ0FBQyxZQUFZLElBQUksTUFBTSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDMUQsS0FBSyxDQUFDLG9CQUFvQixDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsTUFBTSxDQUFDLFlBQVksRUFBRSxDQUFDO1FBQzdELENBQUM7UUFFRCxPQUFPLEtBQUssQ0FBQztJQUNmLENBQUM7O0FBdlpILGdDQXdaQztBQXZaQzs7R0FFRztBQUNhLHlCQUFjLEdBQWdCO0lBQzVDLE9BQU8sRUFBRSxJQUFJO0lBQ2IsY0FBYyxFQUFFLE1BQU0sQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDO0lBQzFDLGlCQUFpQixFQUFFLE1BQU0sQ0FBQyxNQUFNLENBQUMsYUFBYSxDQUFDO0lBQy9DLGVBQWUsRUFBRSxhQUFhLENBQUMsR0FBRztJQUNsQyxvQkFBb0IsRUFBRSxLQUFLO0lBQzNCLGtCQUFrQixFQUFFLElBQUk7SUFDeEIsZ0JBQWdCLEVBQUUsSUFBSSxFQUFFLFVBQVU7SUFDbEMsa0JBQWtCLEVBQUUsSUFBSTtJQUN4QixTQUFTLEVBQUUsSUFBSTtJQUNmLFlBQVksRUFBRSxJQUFJO0lBQ2xCLGVBQWUsRUFBRTtRQUNmLFdBQVcsRUFBRSxFQUFFO1FBQ2YsU0FBUyxFQUFFLEVBQUU7UUFDYixVQUFVLEVBQUUsRUFBRTtLQUNmO0NBQ0YsQ0FBQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXHNoYXJlZC1rZXJuZWxcXHR5cGVzXFxhdWRpdC50eXBlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcclxuICogQXVkaXQgVHlwZXNcclxuICogXHJcbiAqIENvbW1vbiB0eXBlcyBhbmQgaW50ZXJmYWNlcyBmb3IgYXVkaXQgbG9nZ2luZyBhY3Jvc3MgdGhlIGFwcGxpY2F0aW9uLlxyXG4gKiBQcm92aWRlcyBjb25zaXN0ZW50IGF1ZGl0IHBhdHRlcm5zIGFuZCB1dGlsaXRpZXMuXHJcbiAqL1xyXG5cclxuLyoqXHJcbiAqIEF1ZGl0IEFjdGlvbiBUeXBlc1xyXG4gKi9cclxuZXhwb3J0IGVudW0gQXVkaXRBY3Rpb24ge1xyXG4gIENSRUFURSA9ICdjcmVhdGUnLFxyXG4gIFJFQUQgPSAncmVhZCcsXHJcbiAgVVBEQVRFID0gJ3VwZGF0ZScsXHJcbiAgREVMRVRFID0gJ2RlbGV0ZScsXHJcbiAgTE9HSU4gPSAnbG9naW4nLFxyXG4gIExPR09VVCA9ICdsb2dvdXQnLFxyXG4gIEFDQ0VTUyA9ICdhY2Nlc3MnLFxyXG4gIEVYUE9SVCA9ICdleHBvcnQnLFxyXG4gIElNUE9SVCA9ICdpbXBvcnQnLFxyXG4gIEFQUFJPVkUgPSAnYXBwcm92ZScsXHJcbiAgUkVKRUNUID0gJ3JlamVjdCcsXHJcbiAgRVhFQ1VURSA9ICdleGVjdXRlJyxcclxuICBDQU5DRUwgPSAnY2FuY2VsJyxcclxuICBTVVNQRU5EID0gJ3N1c3BlbmQnLFxyXG4gIEFDVElWQVRFID0gJ2FjdGl2YXRlJyxcclxuICBERUFDVElWQVRFID0gJ2RlYWN0aXZhdGUnLFxyXG4gIENPTkZJR1VSRSA9ICdjb25maWd1cmUnLFxyXG4gIEJBQ0tVUCA9ICdiYWNrdXAnLFxyXG4gIFJFU1RPUkUgPSAncmVzdG9yZScsXHJcbiAgUFVSR0UgPSAncHVyZ2UnLFxyXG4gIEFSQ0hJVkUgPSAnYXJjaGl2ZScsXHJcbiAgVU5BUkNISVZFID0gJ3VuYXJjaGl2ZScsXHJcbn1cclxuXHJcbi8qKlxyXG4gKiBBdWRpdCBSZXN1bHQgU3RhdHVzXHJcbiAqL1xyXG5leHBvcnQgZW51bSBBdWRpdFJlc3VsdCB7XHJcbiAgU1VDQ0VTUyA9ICdzdWNjZXNzJyxcclxuICBGQUlMVVJFID0gJ2ZhaWx1cmUnLFxyXG4gIFBBUlRJQUwgPSAncGFydGlhbCcsXHJcbiAgUEVORElORyA9ICdwZW5kaW5nJyxcclxuICBDQU5DRUxMRUQgPSAnY2FuY2VsbGVkJyxcclxufVxyXG5cclxuLyoqXHJcbiAqIEF1ZGl0IFNldmVyaXR5IExldmVsc1xyXG4gKi9cclxuZXhwb3J0IGVudW0gQXVkaXRTZXZlcml0eSB7XHJcbiAgTE9XID0gJ2xvdycsXHJcbiAgTUVESVVNID0gJ21lZGl1bScsXHJcbiAgSElHSCA9ICdoaWdoJyxcclxuICBDUklUSUNBTCA9ICdjcml0aWNhbCcsXHJcbn1cclxuXHJcbi8qKlxyXG4gKiBBdWRpdCBDYXRlZ29yaWVzXHJcbiAqL1xyXG5leHBvcnQgZW51bSBBdWRpdENhdGVnb3J5IHtcclxuICBBVVRIRU5USUNBVElPTiA9ICdhdXRoZW50aWNhdGlvbicsXHJcbiAgQVVUSE9SSVpBVElPTiA9ICdhdXRob3JpemF0aW9uJyxcclxuICBEQVRBX0FDQ0VTUyA9ICdkYXRhX2FjY2VzcycsXHJcbiAgREFUQV9NT0RJRklDQVRJT04gPSAnZGF0YV9tb2RpZmljYXRpb24nLFxyXG4gIFNZU1RFTV9DT05GSUdVUkFUSU9OID0gJ3N5c3RlbV9jb25maWd1cmF0aW9uJyxcclxuICBTRUNVUklUWV9FVkVOVCA9ICdzZWN1cml0eV9ldmVudCcsXHJcbiAgQ09NUExJQU5DRSA9ICdjb21wbGlhbmNlJyxcclxuICBQRVJGT1JNQU5DRSA9ICdwZXJmb3JtYW5jZScsXHJcbiAgRVJST1IgPSAnZXJyb3InLFxyXG4gIEJVU0lORVNTX1BST0NFU1MgPSAnYnVzaW5lc3NfcHJvY2VzcycsXHJcbn1cclxuXHJcbi8qKlxyXG4gKiBVc2VyIENvbnRleHQgZm9yIEF1ZGl0XHJcbiAqL1xyXG5leHBvcnQgaW50ZXJmYWNlIEF1ZGl0VXNlckNvbnRleHQge1xyXG4gIC8qKiBVc2VyIGlkZW50aWZpZXIgKi9cclxuICB1c2VySWQ6IHN0cmluZztcclxuICAvKiogVXNlcm5hbWUgKi9cclxuICB1c2VybmFtZT86IHN0cmluZztcclxuICAvKiogVXNlciBlbWFpbCAqL1xyXG4gIGVtYWlsPzogc3RyaW5nO1xyXG4gIC8qKiBVc2VyIHJvbGVzICovXHJcbiAgcm9sZXM/OiBzdHJpbmdbXTtcclxuICAvKiogVXNlciBwZXJtaXNzaW9ucyAqL1xyXG4gIHBlcm1pc3Npb25zPzogc3RyaW5nW107XHJcbiAgLyoqIFNlc3Npb24gaWRlbnRpZmllciAqL1xyXG4gIHNlc3Npb25JZD86IHN0cmluZztcclxuICAvKiogVXNlciBhZ2VudCAqL1xyXG4gIHVzZXJBZ2VudD86IHN0cmluZztcclxuICAvKiogSVAgYWRkcmVzcyAqL1xyXG4gIGlwQWRkcmVzcz86IHN0cmluZztcclxuICAvKiogR2VvZ3JhcGhpYyBsb2NhdGlvbiAqL1xyXG4gIGxvY2F0aW9uPzoge1xyXG4gICAgY291bnRyeT86IHN0cmluZztcclxuICAgIHJlZ2lvbj86IHN0cmluZztcclxuICAgIGNpdHk/OiBzdHJpbmc7XHJcbiAgICBjb29yZGluYXRlcz86IHtcclxuICAgICAgbGF0aXR1ZGU6IG51bWJlcjtcclxuICAgICAgbG9uZ2l0dWRlOiBudW1iZXI7XHJcbiAgICB9O1xyXG4gIH07XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBTeXN0ZW0gQ29udGV4dCBmb3IgQXVkaXRcclxuICovXHJcbmV4cG9ydCBpbnRlcmZhY2UgQXVkaXRTeXN0ZW1Db250ZXh0IHtcclxuICAvKiogQXBwbGljYXRpb24gbmFtZSAqL1xyXG4gIGFwcGxpY2F0aW9uOiBzdHJpbmc7XHJcbiAgLyoqIEFwcGxpY2F0aW9uIHZlcnNpb24gKi9cclxuICB2ZXJzaW9uPzogc3RyaW5nO1xyXG4gIC8qKiBFbnZpcm9ubWVudCAoZGV2LCBzdGFnaW5nLCBwcm9kKSAqL1xyXG4gIGVudmlyb25tZW50Pzogc3RyaW5nO1xyXG4gIC8qKiBTZXJ2aWNlIG5hbWUgKi9cclxuICBzZXJ2aWNlPzogc3RyaW5nO1xyXG4gIC8qKiBNb2R1bGUgbmFtZSAqL1xyXG4gIG1vZHVsZT86IHN0cmluZztcclxuICAvKiogU2VydmVyL2luc3RhbmNlIGlkZW50aWZpZXIgKi9cclxuICBpbnN0YW5jZUlkPzogc3RyaW5nO1xyXG4gIC8qKiBSZXF1ZXN0IElEIGZvciB0cmFjaW5nICovXHJcbiAgcmVxdWVzdElkPzogc3RyaW5nO1xyXG4gIC8qKiBDb3JyZWxhdGlvbiBJRCAqL1xyXG4gIGNvcnJlbGF0aW9uSWQ/OiBzdHJpbmc7XHJcbiAgLyoqIFRlbmFudCBpZGVudGlmaWVyIChmb3IgbXVsdGktdGVuYW50IHN5c3RlbXMpICovXHJcbiAgdGVuYW50SWQ/OiBzdHJpbmc7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBSZXNvdXJjZSBDb250ZXh0IGZvciBBdWRpdFxyXG4gKi9cclxuZXhwb3J0IGludGVyZmFjZSBBdWRpdFJlc291cmNlQ29udGV4dCB7XHJcbiAgLyoqIFJlc291cmNlIHR5cGUgKi9cclxuICByZXNvdXJjZVR5cGU6IHN0cmluZztcclxuICAvKiogUmVzb3VyY2UgaWRlbnRpZmllciAqL1xyXG4gIHJlc291cmNlSWQ/OiBzdHJpbmc7XHJcbiAgLyoqIFJlc291cmNlIG5hbWUgKi9cclxuICByZXNvdXJjZU5hbWU/OiBzdHJpbmc7XHJcbiAgLyoqIFBhcmVudCByZXNvdXJjZSBpbmZvcm1hdGlvbiAqL1xyXG4gIHBhcmVudFJlc291cmNlPzoge1xyXG4gICAgdHlwZTogc3RyaW5nO1xyXG4gICAgaWQ6IHN0cmluZztcclxuICAgIG5hbWU/OiBzdHJpbmc7XHJcbiAgfTtcclxuICAvKiogUmVzb3VyY2UgYXR0cmlidXRlcyAqL1xyXG4gIGF0dHJpYnV0ZXM/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xyXG59XHJcblxyXG4vKipcclxuICogRGF0YSBDaGFuZ2VzIGZvciBBdWRpdFxyXG4gKi9cclxuZXhwb3J0IGludGVyZmFjZSBBdWRpdERhdGFDaGFuZ2VzIHtcclxuICAvKiogUHJldmlvdXMgdmFsdWVzICovXHJcbiAgYmVmb3JlPzogUmVjb3JkPHN0cmluZywgYW55PjtcclxuICAvKiogTmV3IHZhbHVlcyAqL1xyXG4gIGFmdGVyPzogUmVjb3JkPHN0cmluZywgYW55PjtcclxuICAvKiogQ2hhbmdlZCBmaWVsZHMgKi9cclxuICBjaGFuZ2VkRmllbGRzPzogc3RyaW5nW107XHJcbiAgLyoqIENoYW5nZSBzdW1tYXJ5ICovXHJcbiAgc3VtbWFyeT86IHN0cmluZztcclxufVxyXG5cclxuLyoqXHJcbiAqIEF1ZGl0IEVudHJ5XHJcbiAqIENvbXBsZXRlIGF1ZGl0IGxvZyBlbnRyeVxyXG4gKi9cclxuZXhwb3J0IGludGVyZmFjZSBBdWRpdEVudHJ5IHtcclxuICAvKiogVW5pcXVlIGF1ZGl0IGVudHJ5IGlkZW50aWZpZXIgKi9cclxuICBpZDogc3RyaW5nO1xyXG4gIC8qKiBUaW1lc3RhbXAgb2YgdGhlIGV2ZW50ICovXHJcbiAgdGltZXN0YW1wOiBEYXRlO1xyXG4gIC8qKiBBY3Rpb24gcGVyZm9ybWVkICovXHJcbiAgYWN0aW9uOiBBdWRpdEFjdGlvbjtcclxuICAvKiogUmVzdWx0IG9mIHRoZSBhY3Rpb24gKi9cclxuICByZXN1bHQ6IEF1ZGl0UmVzdWx0O1xyXG4gIC8qKiBTZXZlcml0eSBsZXZlbCAqL1xyXG4gIHNldmVyaXR5OiBBdWRpdFNldmVyaXR5O1xyXG4gIC8qKiBBdWRpdCBjYXRlZ29yeSAqL1xyXG4gIGNhdGVnb3J5OiBBdWRpdENhdGVnb3J5O1xyXG4gIC8qKiBFdmVudCBkZXNjcmlwdGlvbiAqL1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbiAgLyoqIERldGFpbGVkIG1lc3NhZ2UgKi9cclxuICBtZXNzYWdlPzogc3RyaW5nO1xyXG4gIC8qKiBVc2VyIGNvbnRleHQgKi9cclxuICB1c2VyPzogQXVkaXRVc2VyQ29udGV4dDtcclxuICAvKiogU3lzdGVtIGNvbnRleHQgKi9cclxuICBzeXN0ZW06IEF1ZGl0U3lzdGVtQ29udGV4dDtcclxuICAvKiogUmVzb3VyY2UgY29udGV4dCAqL1xyXG4gIHJlc291cmNlPzogQXVkaXRSZXNvdXJjZUNvbnRleHQ7XHJcbiAgLyoqIERhdGEgY2hhbmdlcyAqL1xyXG4gIGNoYW5nZXM/OiBBdWRpdERhdGFDaGFuZ2VzO1xyXG4gIC8qKiBBZGRpdGlvbmFsIG1ldGFkYXRhICovXHJcbiAgbWV0YWRhdGE/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xyXG4gIC8qKiBEdXJhdGlvbiBvZiB0aGUgb3BlcmF0aW9uIChpbiBtaWxsaXNlY29uZHMpICovXHJcbiAgZHVyYXRpb24/OiBudW1iZXI7XHJcbiAgLyoqIEVycm9yIGluZm9ybWF0aW9uIChmb3IgZmFpbGVkIG9wZXJhdGlvbnMpICovXHJcbiAgZXJyb3I/OiB7XHJcbiAgICBjb2RlPzogc3RyaW5nO1xyXG4gICAgbWVzc2FnZT86IHN0cmluZztcclxuICAgIHN0YWNrPzogc3RyaW5nO1xyXG4gIH07XHJcbiAgLyoqIFJpc2sgc2NvcmUgKDAtMTAwKSAqL1xyXG4gIHJpc2tTY29yZT86IG51bWJlcjtcclxuICAvKiogQ29tcGxpYW5jZSB0YWdzICovXHJcbiAgY29tcGxpYW5jZVRhZ3M/OiBzdHJpbmdbXTtcclxuICAvKiogUmV0ZW50aW9uIHBlcmlvZCAoaW4gZGF5cykgKi9cclxuICByZXRlbnRpb25EYXlzPzogbnVtYmVyO1xyXG59XHJcblxyXG4vKipcclxuICogQXVkaXQgUXVlcnkgUGFyYW1ldGVyc1xyXG4gKi9cclxuZXhwb3J0IGludGVyZmFjZSBBdWRpdFF1ZXJ5UGFyYW1zIHtcclxuICAvKiogU3RhcnQgZGF0ZSBmb3IgcXVlcnkgKi9cclxuICBzdGFydERhdGU/OiBEYXRlO1xyXG4gIC8qKiBFbmQgZGF0ZSBmb3IgcXVlcnkgKi9cclxuICBlbmREYXRlPzogRGF0ZTtcclxuICAvKiogVXNlciBJRCBmaWx0ZXIgKi9cclxuICB1c2VySWQ/OiBzdHJpbmc7XHJcbiAgLyoqIEFjdGlvbiBmaWx0ZXIgKi9cclxuICBhY3Rpb25zPzogQXVkaXRBY3Rpb25bXTtcclxuICAvKiogUmVzdWx0IGZpbHRlciAqL1xyXG4gIHJlc3VsdHM/OiBBdWRpdFJlc3VsdFtdO1xyXG4gIC8qKiBTZXZlcml0eSBmaWx0ZXIgKi9cclxuICBzZXZlcml0aWVzPzogQXVkaXRTZXZlcml0eVtdO1xyXG4gIC8qKiBDYXRlZ29yeSBmaWx0ZXIgKi9cclxuICBjYXRlZ29yaWVzPzogQXVkaXRDYXRlZ29yeVtdO1xyXG4gIC8qKiBSZXNvdXJjZSB0eXBlIGZpbHRlciAqL1xyXG4gIHJlc291cmNlVHlwZXM/OiBzdHJpbmdbXTtcclxuICAvKiogUmVzb3VyY2UgSUQgZmlsdGVyICovXHJcbiAgcmVzb3VyY2VJZHM/OiBzdHJpbmdbXTtcclxuICAvKiogVGV4dCBzZWFyY2ggaW4gZGVzY3JpcHRpb24vbWVzc2FnZSAqL1xyXG4gIHNlYXJjaD86IHN0cmluZztcclxuICAvKiogTWluaW11bSByaXNrIHNjb3JlICovXHJcbiAgbWluUmlza1Njb3JlPzogbnVtYmVyO1xyXG4gIC8qKiBNYXhpbXVtIHJpc2sgc2NvcmUgKi9cclxuICBtYXhSaXNrU2NvcmU/OiBudW1iZXI7XHJcbiAgLyoqIENvbXBsaWFuY2UgdGFncyBmaWx0ZXIgKi9cclxuICBjb21wbGlhbmNlVGFncz86IHN0cmluZ1tdO1xyXG4gIC8qKiBUZW5hbnQgSUQgZmlsdGVyICovXHJcbiAgdGVuYW50SWQ/OiBzdHJpbmc7XHJcbiAgLyoqIEFwcGxpY2F0aW9uIGZpbHRlciAqL1xyXG4gIGFwcGxpY2F0aW9ucz86IHN0cmluZ1tdO1xyXG4gIC8qKiBFbnZpcm9ubWVudCBmaWx0ZXIgKi9cclxuICBlbnZpcm9ubWVudHM/OiBzdHJpbmdbXTtcclxufVxyXG5cclxuLyoqXHJcbiAqIEF1ZGl0IFN0YXRpc3RpY3NcclxuICovXHJcbmV4cG9ydCBpbnRlcmZhY2UgQXVkaXRTdGF0aXN0aWNzIHtcclxuICAvKiogVG90YWwgbnVtYmVyIG9mIGF1ZGl0IGVudHJpZXMgKi9cclxuICB0b3RhbEVudHJpZXM6IG51bWJlcjtcclxuICAvKiogRW50cmllcyBieSBhY3Rpb24gKi9cclxuICBieUFjdGlvbjogUmVjb3JkPEF1ZGl0QWN0aW9uLCBudW1iZXI+O1xyXG4gIC8qKiBFbnRyaWVzIGJ5IHJlc3VsdCAqL1xyXG4gIGJ5UmVzdWx0OiBSZWNvcmQ8QXVkaXRSZXN1bHQsIG51bWJlcj47XHJcbiAgLyoqIEVudHJpZXMgYnkgc2V2ZXJpdHkgKi9cclxuICBieVNldmVyaXR5OiBSZWNvcmQ8QXVkaXRTZXZlcml0eSwgbnVtYmVyPjtcclxuICAvKiogRW50cmllcyBieSBjYXRlZ29yeSAqL1xyXG4gIGJ5Q2F0ZWdvcnk6IFJlY29yZDxBdWRpdENhdGVnb3J5LCBudW1iZXI+O1xyXG4gIC8qKiBUb3AgdXNlcnMgYnkgYWN0aXZpdHkgKi9cclxuICB0b3BVc2VyczogQXJyYXk8e1xyXG4gICAgdXNlcklkOiBzdHJpbmc7XHJcbiAgICB1c2VybmFtZT86IHN0cmluZztcclxuICAgIGNvdW50OiBudW1iZXI7XHJcbiAgfT47XHJcbiAgLyoqIFRvcCByZXNvdXJjZXMgYnkgYWNjZXNzICovXHJcbiAgdG9wUmVzb3VyY2VzOiBBcnJheTx7XHJcbiAgICByZXNvdXJjZVR5cGU6IHN0cmluZztcclxuICAgIHJlc291cmNlSWQ6IHN0cmluZztcclxuICAgIHJlc291cmNlTmFtZT86IHN0cmluZztcclxuICAgIGNvdW50OiBudW1iZXI7XHJcbiAgfT47XHJcbiAgLyoqIFJpc2sgZGlzdHJpYnV0aW9uICovXHJcbiAgcmlza0Rpc3RyaWJ1dGlvbjoge1xyXG4gICAgbG93OiBudW1iZXI7ICAgIC8vIDAtMjVcclxuICAgIG1lZGl1bTogbnVtYmVyOyAvLyAyNi01MFxyXG4gICAgaGlnaDogbnVtYmVyOyAgIC8vIDUxLTc1XHJcbiAgICBjcml0aWNhbDogbnVtYmVyOyAvLyA3Ni0xMDBcclxuICB9O1xyXG4gIC8qKiBUaW1lIHBlcmlvZCAqL1xyXG4gIHBlcmlvZDoge1xyXG4gICAgc3RhcnQ6IERhdGU7XHJcbiAgICBlbmQ6IERhdGU7XHJcbiAgfTtcclxufVxyXG5cclxuLyoqXHJcbiAqIEF1ZGl0IENvbmZpZ3VyYXRpb25cclxuICovXHJcbmV4cG9ydCBpbnRlcmZhY2UgQXVkaXRDb25maWcge1xyXG4gIC8qKiBXaGV0aGVyIGF1ZGl0IGxvZ2dpbmcgaXMgZW5hYmxlZCAqL1xyXG4gIGVuYWJsZWQ6IGJvb2xlYW47XHJcbiAgLyoqIEFjdGlvbnMgdG8gYXVkaXQgKi9cclxuICBhdWRpdGVkQWN0aW9uczogQXVkaXRBY3Rpb25bXTtcclxuICAvKiogQ2F0ZWdvcmllcyB0byBhdWRpdCAqL1xyXG4gIGF1ZGl0ZWRDYXRlZ29yaWVzOiBBdWRpdENhdGVnb3J5W107XHJcbiAgLyoqIE1pbmltdW0gc2V2ZXJpdHkgdG8gbG9nICovXHJcbiAgbWluaW11bVNldmVyaXR5OiBBdWRpdFNldmVyaXR5O1xyXG4gIC8qKiBXaGV0aGVyIHRvIGluY2x1ZGUgc2Vuc2l0aXZlIGRhdGEgKi9cclxuICBpbmNsdWRlU2Vuc2l0aXZlRGF0YTogYm9vbGVhbjtcclxuICAvKiogV2hldGhlciB0byBpbmNsdWRlIGRhdGEgY2hhbmdlcyAqL1xyXG4gIGluY2x1ZGVEYXRhQ2hhbmdlczogYm9vbGVhbjtcclxuICAvKiogTWF4aW11bSByZXRlbnRpb24gcGVyaW9kIChpbiBkYXlzKSAqL1xyXG4gIG1heFJldGVudGlvbkRheXM6IG51bWJlcjtcclxuICAvKiogV2hldGhlciB0byBjb21wcmVzcyBvbGQgZW50cmllcyAqL1xyXG4gIGNvbXByZXNzT2xkRW50cmllczogYm9vbGVhbjtcclxuICAvKiogQmF0Y2ggc2l6ZSBmb3IgYnVsayBvcGVyYXRpb25zICovXHJcbiAgYmF0Y2hTaXplOiBudW1iZXI7XHJcbiAgLyoqIFdoZXRoZXIgdG8gZW5hYmxlIHJlYWwtdGltZSBhbGVydHMgKi9cclxuICBlbmFibGVBbGVydHM6IGJvb2xlYW47XHJcbiAgLyoqIEFsZXJ0IHRocmVzaG9sZHMgKi9cclxuICBhbGVydFRocmVzaG9sZHM6IHtcclxuICAgIGZhaWx1cmVSYXRlOiBudW1iZXI7IC8vIFBlcmNlbnRhZ2VcclxuICAgIHJpc2tTY29yZTogbnVtYmVyOyAgIC8vIDAtMTAwXHJcbiAgICB0aW1lV2luZG93OiBudW1iZXI7ICAvLyBNaW51dGVzXHJcbiAgfTtcclxufVxyXG5cclxuLyoqXHJcbiAqIEF1ZGl0IFV0aWxpdGllc1xyXG4gKi9cclxuZXhwb3J0IGNsYXNzIEF1ZGl0VXRpbHMge1xyXG4gIC8qKlxyXG4gICAqIERlZmF1bHQgYXVkaXQgY29uZmlndXJhdGlvblxyXG4gICAqL1xyXG4gIHN0YXRpYyByZWFkb25seSBERUZBVUxUX0NPTkZJRzogQXVkaXRDb25maWcgPSB7XHJcbiAgICBlbmFibGVkOiB0cnVlLFxyXG4gICAgYXVkaXRlZEFjdGlvbnM6IE9iamVjdC52YWx1ZXMoQXVkaXRBY3Rpb24pLFxyXG4gICAgYXVkaXRlZENhdGVnb3JpZXM6IE9iamVjdC52YWx1ZXMoQXVkaXRDYXRlZ29yeSksXHJcbiAgICBtaW5pbXVtU2V2ZXJpdHk6IEF1ZGl0U2V2ZXJpdHkuTE9XLFxyXG4gICAgaW5jbHVkZVNlbnNpdGl2ZURhdGE6IGZhbHNlLFxyXG4gICAgaW5jbHVkZURhdGFDaGFuZ2VzOiB0cnVlLFxyXG4gICAgbWF4UmV0ZW50aW9uRGF5czogMjU1NSwgLy8gNyB5ZWFyc1xyXG4gICAgY29tcHJlc3NPbGRFbnRyaWVzOiB0cnVlLFxyXG4gICAgYmF0Y2hTaXplOiAxMDAwLFxyXG4gICAgZW5hYmxlQWxlcnRzOiB0cnVlLFxyXG4gICAgYWxlcnRUaHJlc2hvbGRzOiB7XHJcbiAgICAgIGZhaWx1cmVSYXRlOiAxMCxcclxuICAgICAgcmlza1Njb3JlOiA3NSxcclxuICAgICAgdGltZVdpbmRvdzogNjAsXHJcbiAgICB9LFxyXG4gIH07XHJcblxyXG4gIC8qKlxyXG4gICAqIENyZWF0ZSBhbiBhdWRpdCBlbnRyeVxyXG4gICAqL1xyXG4gIHN0YXRpYyBjcmVhdGVFbnRyeShcclxuICAgIGFjdGlvbjogQXVkaXRBY3Rpb24sXHJcbiAgICByZXN1bHQ6IEF1ZGl0UmVzdWx0LFxyXG4gICAgZGVzY3JpcHRpb246IHN0cmluZyxcclxuICAgIG9wdGlvbnM6IFBhcnRpYWw8QXVkaXRFbnRyeT4gPSB7fVxyXG4gICk6IEF1ZGl0RW50cnkge1xyXG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcclxuICAgIFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgaWQ6IGNyeXB0by5yYW5kb21VVUlEKCksXHJcbiAgICAgIHRpbWVzdGFtcDogbm93LFxyXG4gICAgICBhY3Rpb24sXHJcbiAgICAgIHJlc3VsdCxcclxuICAgICAgc2V2ZXJpdHk6IG9wdGlvbnMuc2V2ZXJpdHkgfHwgQXVkaXRVdGlscy5pbmZlclNldmVyaXR5KGFjdGlvbiwgcmVzdWx0KSxcclxuICAgICAgY2F0ZWdvcnk6IG9wdGlvbnMuY2F0ZWdvcnkgfHwgQXVkaXRVdGlscy5pbmZlckNhdGVnb3J5KGFjdGlvbiksXHJcbiAgICAgIGRlc2NyaXB0aW9uLFxyXG4gICAgICBzeXN0ZW06IHtcclxuICAgICAgICBhcHBsaWNhdGlvbjogJ3NlbnRpbmVsJyxcclxuICAgICAgICAuLi5vcHRpb25zLnN5c3RlbSxcclxuICAgICAgfSxcclxuICAgICAgLi4ub3B0aW9ucyxcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBJbmZlciBzZXZlcml0eSBmcm9tIGFjdGlvbiBhbmQgcmVzdWx0XHJcbiAgICovXHJcbiAgc3RhdGljIGluZmVyU2V2ZXJpdHkoYWN0aW9uOiBBdWRpdEFjdGlvbiwgcmVzdWx0OiBBdWRpdFJlc3VsdCk6IEF1ZGl0U2V2ZXJpdHkge1xyXG4gICAgLy8gSGlnaCBzZXZlcml0eSBhY3Rpb25zXHJcbiAgICBpZiAoW1xyXG4gICAgICBBdWRpdEFjdGlvbi5ERUxFVEUsXHJcbiAgICAgIEF1ZGl0QWN0aW9uLlBVUkdFLFxyXG4gICAgICBBdWRpdEFjdGlvbi5FWEVDVVRFLFxyXG4gICAgICBBdWRpdEFjdGlvbi5DT05GSUdVUkUsXHJcbiAgICBdLmluY2x1ZGVzKGFjdGlvbikpIHtcclxuICAgICAgcmV0dXJuIHJlc3VsdCA9PT0gQXVkaXRSZXN1bHQuRkFJTFVSRSA/IEF1ZGl0U2V2ZXJpdHkuQ1JJVElDQUwgOiBBdWRpdFNldmVyaXR5LkhJR0g7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gTWVkaXVtIHNldmVyaXR5IGFjdGlvbnNcclxuICAgIGlmIChbXHJcbiAgICAgIEF1ZGl0QWN0aW9uLkNSRUFURSxcclxuICAgICAgQXVkaXRBY3Rpb24uVVBEQVRFLFxyXG4gICAgICBBdWRpdEFjdGlvbi5BUFBST1ZFLFxyXG4gICAgICBBdWRpdEFjdGlvbi5SRUpFQ1QsXHJcbiAgICAgIEF1ZGl0QWN0aW9uLlNVU1BFTkQsXHJcbiAgICAgIEF1ZGl0QWN0aW9uLkFDVElWQVRFLFxyXG4gICAgICBBdWRpdEFjdGlvbi5ERUFDVElWQVRFLFxyXG4gICAgXS5pbmNsdWRlcyhhY3Rpb24pKSB7XHJcbiAgICAgIHJldHVybiByZXN1bHQgPT09IEF1ZGl0UmVzdWx0LkZBSUxVUkUgPyBBdWRpdFNldmVyaXR5LkhJR0ggOiBBdWRpdFNldmVyaXR5Lk1FRElVTTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBMb3cgc2V2ZXJpdHkgYWN0aW9uc1xyXG4gICAgcmV0dXJuIHJlc3VsdCA9PT0gQXVkaXRSZXN1bHQuRkFJTFVSRSA/IEF1ZGl0U2V2ZXJpdHkuTUVESVVNIDogQXVkaXRTZXZlcml0eS5MT1c7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBJbmZlciBjYXRlZ29yeSBmcm9tIGFjdGlvblxyXG4gICAqL1xyXG4gIHN0YXRpYyBpbmZlckNhdGVnb3J5KGFjdGlvbjogQXVkaXRBY3Rpb24pOiBBdWRpdENhdGVnb3J5IHtcclxuICAgIHN3aXRjaCAoYWN0aW9uKSB7XHJcbiAgICAgIGNhc2UgQXVkaXRBY3Rpb24uTE9HSU46XHJcbiAgICAgIGNhc2UgQXVkaXRBY3Rpb24uTE9HT1VUOlxyXG4gICAgICAgIHJldHVybiBBdWRpdENhdGVnb3J5LkFVVEhFTlRJQ0FUSU9OO1xyXG4gICAgICBcclxuICAgICAgY2FzZSBBdWRpdEFjdGlvbi5BQ0NFU1M6XHJcbiAgICAgICAgcmV0dXJuIEF1ZGl0Q2F0ZWdvcnkuQVVUSE9SSVpBVElPTjtcclxuICAgICAgXHJcbiAgICAgIGNhc2UgQXVkaXRBY3Rpb24uUkVBRDpcclxuICAgICAgY2FzZSBBdWRpdEFjdGlvbi5FWFBPUlQ6XHJcbiAgICAgICAgcmV0dXJuIEF1ZGl0Q2F0ZWdvcnkuREFUQV9BQ0NFU1M7XHJcbiAgICAgIFxyXG4gICAgICBjYXNlIEF1ZGl0QWN0aW9uLkNSRUFURTpcclxuICAgICAgY2FzZSBBdWRpdEFjdGlvbi5VUERBVEU6XHJcbiAgICAgIGNhc2UgQXVkaXRBY3Rpb24uREVMRVRFOlxyXG4gICAgICBjYXNlIEF1ZGl0QWN0aW9uLklNUE9SVDpcclxuICAgICAgICByZXR1cm4gQXVkaXRDYXRlZ29yeS5EQVRBX01PRElGSUNBVElPTjtcclxuICAgICAgXHJcbiAgICAgIGNhc2UgQXVkaXRBY3Rpb24uQ09ORklHVVJFOlxyXG4gICAgICBjYXNlIEF1ZGl0QWN0aW9uLkJBQ0tVUDpcclxuICAgICAgY2FzZSBBdWRpdEFjdGlvbi5SRVNUT1JFOlxyXG4gICAgICAgIHJldHVybiBBdWRpdENhdGVnb3J5LlNZU1RFTV9DT05GSUdVUkFUSU9OO1xyXG4gICAgICBcclxuICAgICAgZGVmYXVsdDpcclxuICAgICAgICByZXR1cm4gQXVkaXRDYXRlZ29yeS5CVVNJTkVTU19QUk9DRVNTO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2FsY3VsYXRlIHJpc2sgc2NvcmUgYmFzZWQgb24gdmFyaW91cyBmYWN0b3JzXHJcbiAgICovXHJcbiAgc3RhdGljIGNhbGN1bGF0ZVJpc2tTY29yZShlbnRyeTogUGFydGlhbDxBdWRpdEVudHJ5Pik6IG51bWJlciB7XHJcbiAgICBsZXQgc2NvcmUgPSAwO1xyXG5cclxuICAgIC8vIEJhc2Ugc2NvcmUgYnkgc2V2ZXJpdHlcclxuICAgIHN3aXRjaCAoZW50cnkuc2V2ZXJpdHkpIHtcclxuICAgICAgY2FzZSBBdWRpdFNldmVyaXR5LkNSSVRJQ0FMOlxyXG4gICAgICAgIHNjb3JlICs9IDQwO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBjYXNlIEF1ZGl0U2V2ZXJpdHkuSElHSDpcclxuICAgICAgICBzY29yZSArPSAzMDtcclxuICAgICAgICBicmVhaztcclxuICAgICAgY2FzZSBBdWRpdFNldmVyaXR5Lk1FRElVTTpcclxuICAgICAgICBzY29yZSArPSAyMDtcclxuICAgICAgICBicmVhaztcclxuICAgICAgY2FzZSBBdWRpdFNldmVyaXR5LkxPVzpcclxuICAgICAgICBzY29yZSArPSAxMDtcclxuICAgICAgICBicmVhaztcclxuICAgIH1cclxuXHJcbiAgICAvLyBBY3Rpb24gcmlza1xyXG4gICAgY29uc3QgaGlnaFJpc2tBY3Rpb25zID0gW1xyXG4gICAgICBBdWRpdEFjdGlvbi5ERUxFVEUsXHJcbiAgICAgIEF1ZGl0QWN0aW9uLlBVUkdFLFxyXG4gICAgICBBdWRpdEFjdGlvbi5FWEVDVVRFLFxyXG4gICAgICBBdWRpdEFjdGlvbi5DT05GSUdVUkUsXHJcbiAgICBdO1xyXG4gICAgaWYgKGVudHJ5LmFjdGlvbiAmJiBoaWdoUmlza0FjdGlvbnMuaW5jbHVkZXMoZW50cnkuYWN0aW9uKSkge1xyXG4gICAgICBzY29yZSArPSAyMDtcclxuICAgIH1cclxuXHJcbiAgICAvLyBSZXN1bHQgaW1wYWN0XHJcbiAgICBpZiAoZW50cnkucmVzdWx0ID09PSBBdWRpdFJlc3VsdC5GQUlMVVJFKSB7XHJcbiAgICAgIHNjb3JlICs9IDE1O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFVzZXIgY29udGV4dCByaXNrIGZhY3RvcnNcclxuICAgIGlmIChlbnRyeS51c2VyKSB7XHJcbiAgICAgIC8vIFByaXZpbGVnZWQgdXNlclxyXG4gICAgICBpZiAoZW50cnkudXNlci5yb2xlcz8uaW5jbHVkZXMoJ2FkbWluJykgfHwgZW50cnkudXNlci5yb2xlcz8uaW5jbHVkZXMoJ3N1cGVydXNlcicpKSB7XHJcbiAgICAgICAgc2NvcmUgKz0gMTA7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC8vIFVudXN1YWwgbG9jYXRpb24gKHNpbXBsaWZpZWQgY2hlY2spXHJcbiAgICAgIGlmIChlbnRyeS51c2VyLmxvY2F0aW9uICYmIGVudHJ5LnVzZXIuaXBBZGRyZXNzKSB7XHJcbiAgICAgICAgLy8gVGhpcyB3b3VsZCB0eXBpY2FsbHkgaW52b2x2ZSBnZW9sb2NhdGlvbiBhbmFseXNpc1xyXG4gICAgICAgIC8vIEZvciBub3csIGp1c3QgYWRkIGEgc21hbGwgcmlzayBmb3IgcmVtb3RlIGFjY2Vzc1xyXG4gICAgICAgIHNjb3JlICs9IDU7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBUaW1lLWJhc2VkIHJpc2sgKG9mZi1ob3VycyBhY2Nlc3MpXHJcbiAgICBpZiAoZW50cnkudGltZXN0YW1wKSB7XHJcbiAgICAgIGNvbnN0IGhvdXIgPSBlbnRyeS50aW1lc3RhbXAuZ2V0SG91cnMoKTtcclxuICAgICAgaWYgKGhvdXIgPCA2IHx8IGhvdXIgPiAyMikge1xyXG4gICAgICAgIHNjb3JlICs9IDEwO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRW5zdXJlIHNjb3JlIGlzIHdpdGhpbiBib3VuZHNcclxuICAgIHJldHVybiBNYXRoLm1pbihNYXRoLm1heChzY29yZSwgMCksIDEwMCk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBTYW5pdGl6ZSBzZW5zaXRpdmUgZGF0YSBmcm9tIGF1ZGl0IGVudHJ5XHJcbiAgICovXHJcbiAgc3RhdGljIHNhbml0aXplRW50cnkoZW50cnk6IEF1ZGl0RW50cnksIGNvbmZpZzogQXVkaXRDb25maWcpOiBBdWRpdEVudHJ5IHtcclxuICAgIGlmIChjb25maWcuaW5jbHVkZVNlbnNpdGl2ZURhdGEpIHtcclxuICAgICAgcmV0dXJuIGVudHJ5O1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHNhbml0aXplZCA9IHsgLi4uZW50cnkgfTtcclxuXHJcbiAgICAvLyBSZW1vdmUgc2Vuc2l0aXZlIHVzZXIgaW5mb3JtYXRpb25cclxuICAgIGlmIChzYW5pdGl6ZWQudXNlcikge1xyXG4gICAgICBzYW5pdGl6ZWQudXNlciA9IHtcclxuICAgICAgICAuLi5zYW5pdGl6ZWQudXNlcixcclxuICAgICAgICBlbWFpbDogc2FuaXRpemVkLnVzZXIuZW1haWwgPyBBdWRpdFV0aWxzLm1hc2tFbWFpbChzYW5pdGl6ZWQudXNlci5lbWFpbCkgOiB1bmRlZmluZWQsXHJcbiAgICAgICAgaXBBZGRyZXNzOiBzYW5pdGl6ZWQudXNlci5pcEFkZHJlc3MgPyBBdWRpdFV0aWxzLm1hc2tJcEFkZHJlc3Moc2FuaXRpemVkLnVzZXIuaXBBZGRyZXNzKSA6IHVuZGVmaW5lZCxcclxuICAgICAgfTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBSZW1vdmUgc2Vuc2l0aXZlIGRhdGEgY2hhbmdlc1xyXG4gICAgaWYgKHNhbml0aXplZC5jaGFuZ2VzICYmICFjb25maWcuaW5jbHVkZURhdGFDaGFuZ2VzKSB7XHJcbiAgICAgIGRlbGV0ZSBzYW5pdGl6ZWQuY2hhbmdlcztcclxuICAgIH0gZWxzZSBpZiAoc2FuaXRpemVkLmNoYW5nZXMpIHtcclxuICAgICAgc2FuaXRpemVkLmNoYW5nZXMgPSBBdWRpdFV0aWxzLnNhbml0aXplRGF0YUNoYW5nZXMoc2FuaXRpemVkLmNoYW5nZXMpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFJlbW92ZSBzZW5zaXRpdmUgbWV0YWRhdGFcclxuICAgIGlmIChzYW5pdGl6ZWQubWV0YWRhdGEpIHtcclxuICAgICAgc2FuaXRpemVkLm1ldGFkYXRhID0gQXVkaXRVdGlscy5zYW5pdGl6ZU1ldGFkYXRhKHNhbml0aXplZC5tZXRhZGF0YSk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHNhbml0aXplZDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIE1hc2sgZW1haWwgYWRkcmVzc1xyXG4gICAqL1xyXG4gIHByaXZhdGUgc3RhdGljIG1hc2tFbWFpbChlbWFpbDogc3RyaW5nKTogc3RyaW5nIHtcclxuICAgIGNvbnN0IFtsb2NhbCwgZG9tYWluXSA9IGVtYWlsLnNwbGl0KCdAJyk7XHJcbiAgICBpZiAobG9jYWwubGVuZ3RoIDw9IDIpIHtcclxuICAgICAgcmV0dXJuIGAke2xvY2FsWzBdfSoqKkAke2RvbWFpbn1gO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIGAke2xvY2FsLnN1YnN0cmluZygwLCAyKX0qKipAJHtkb21haW59YDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIE1hc2sgSVAgYWRkcmVzc1xyXG4gICAqL1xyXG4gIHByaXZhdGUgc3RhdGljIG1hc2tJcEFkZHJlc3MoaXA6IHN0cmluZyk6IHN0cmluZyB7XHJcbiAgICBjb25zdCBwYXJ0cyA9IGlwLnNwbGl0KCcuJyk7XHJcbiAgICBpZiAocGFydHMubGVuZ3RoID09PSA0KSB7XHJcbiAgICAgIHJldHVybiBgJHtwYXJ0c1swXX0uJHtwYXJ0c1sxXX0uKioqLioqKi5gO1xyXG4gICAgfVxyXG4gICAgLy8gSVB2NiBvciBvdGhlciBmb3JtYXRcclxuICAgIHJldHVybiBpcC5zdWJzdHJpbmcoMCwgOCkgKyAnKioqJztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFNhbml0aXplIGRhdGEgY2hhbmdlc1xyXG4gICAqL1xyXG4gIHByaXZhdGUgc3RhdGljIHNhbml0aXplRGF0YUNoYW5nZXMoY2hhbmdlczogQXVkaXREYXRhQ2hhbmdlcyk6IEF1ZGl0RGF0YUNoYW5nZXMge1xyXG4gICAgY29uc3Qgc2Vuc2l0aXZlRmllbGRzID0gWydwYXNzd29yZCcsICd0b2tlbicsICdzZWNyZXQnLCAna2V5JywgJ2NyZWRlbnRpYWwnXTtcclxuICAgIFxyXG4gICAgY29uc3Qgc2FuaXRpemVPYmplY3QgPSAob2JqOiBSZWNvcmQ8c3RyaW5nLCBhbnk+KTogUmVjb3JkPHN0cmluZywgYW55PiA9PiB7XHJcbiAgICAgIGNvbnN0IHNhbml0aXplZDogUmVjb3JkPHN0cmluZywgYW55PiA9IHt9O1xyXG4gICAgICBcclxuICAgICAgT2JqZWN0LmVudHJpZXMob2JqKS5mb3JFYWNoKChba2V5LCB2YWx1ZV0pID0+IHtcclxuICAgICAgICBpZiAoc2Vuc2l0aXZlRmllbGRzLnNvbWUoZmllbGQgPT4ga2V5LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoZmllbGQpKSkge1xyXG4gICAgICAgICAgc2FuaXRpemVkW2tleV0gPSAnKioqUkVEQUNURUQqKionO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBzYW5pdGl6ZWRba2V5XSA9IHZhbHVlO1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICAgIFxyXG4gICAgICByZXR1cm4gc2FuaXRpemVkO1xyXG4gICAgfTtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICAuLi5jaGFuZ2VzLFxyXG4gICAgICBiZWZvcmU6IGNoYW5nZXMuYmVmb3JlID8gc2FuaXRpemVPYmplY3QoY2hhbmdlcy5iZWZvcmUpIDogdW5kZWZpbmVkLFxyXG4gICAgICBhZnRlcjogY2hhbmdlcy5hZnRlciA/IHNhbml0aXplT2JqZWN0KGNoYW5nZXMuYWZ0ZXIpIDogdW5kZWZpbmVkLFxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFNhbml0aXplIG1ldGFkYXRhXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzdGF0aWMgc2FuaXRpemVNZXRhZGF0YShtZXRhZGF0YTogUmVjb3JkPHN0cmluZywgYW55Pik6IFJlY29yZDxzdHJpbmcsIGFueT4ge1xyXG4gICAgY29uc3Qgc2Vuc2l0aXZlS2V5cyA9IFsncGFzc3dvcmQnLCAndG9rZW4nLCAnc2VjcmV0JywgJ2tleScsICdjcmVkZW50aWFsJywgJ2F1dGhvcml6YXRpb24nXTtcclxuICAgIGNvbnN0IHNhbml0aXplZDogUmVjb3JkPHN0cmluZywgYW55PiA9IHt9O1xyXG4gICAgXHJcbiAgICBPYmplY3QuZW50cmllcyhtZXRhZGF0YSkuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7XHJcbiAgICAgIGlmIChzZW5zaXRpdmVLZXlzLnNvbWUoc2Vuc2l0aXZlID0+IGtleS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlbnNpdGl2ZSkpKSB7XHJcbiAgICAgICAgc2FuaXRpemVkW2tleV0gPSAnKioqUkVEQUNURUQqKionO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNhbml0aXplZFtrZXldID0gdmFsdWU7XHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG4gICAgXHJcbiAgICByZXR1cm4gc2FuaXRpemVkO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgZW50cnkgc2hvdWxkIGJlIGF1ZGl0ZWQgYmFzZWQgb24gY29uZmlndXJhdGlvblxyXG4gICAqL1xyXG4gIHN0YXRpYyBzaG91bGRBdWRpdChlbnRyeTogUGFydGlhbDxBdWRpdEVudHJ5PiwgY29uZmlnOiBBdWRpdENvbmZpZyk6IGJvb2xlYW4ge1xyXG4gICAgaWYgKCFjb25maWcuZW5hYmxlZCkge1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKGVudHJ5LmFjdGlvbiAmJiAhY29uZmlnLmF1ZGl0ZWRBY3Rpb25zLmluY2x1ZGVzKGVudHJ5LmFjdGlvbikpIHtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChlbnRyeS5jYXRlZ29yeSAmJiAhY29uZmlnLmF1ZGl0ZWRDYXRlZ29yaWVzLmluY2x1ZGVzKGVudHJ5LmNhdGVnb3J5KSkge1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKGVudHJ5LnNldmVyaXR5ICYmIEF1ZGl0VXRpbHMuZ2V0U2V2ZXJpdHlMZXZlbChlbnRyeS5zZXZlcml0eSkgPCBBdWRpdFV0aWxzLmdldFNldmVyaXR5TGV2ZWwoY29uZmlnLm1pbmltdW1TZXZlcml0eSkpIHtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiB0cnVlO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IG51bWVyaWMgc2V2ZXJpdHkgbGV2ZWwgZm9yIGNvbXBhcmlzb25cclxuICAgKi9cclxuICBwcml2YXRlIHN0YXRpYyBnZXRTZXZlcml0eUxldmVsKHNldmVyaXR5OiBBdWRpdFNldmVyaXR5KTogbnVtYmVyIHtcclxuICAgIHN3aXRjaCAoc2V2ZXJpdHkpIHtcclxuICAgICAgY2FzZSBBdWRpdFNldmVyaXR5LkxPVzpcclxuICAgICAgICByZXR1cm4gMTtcclxuICAgICAgY2FzZSBBdWRpdFNldmVyaXR5Lk1FRElVTTpcclxuICAgICAgICByZXR1cm4gMjtcclxuICAgICAgY2FzZSBBdWRpdFNldmVyaXR5LkhJR0g6XHJcbiAgICAgICAgcmV0dXJuIDM7XHJcbiAgICAgIGNhc2UgQXVkaXRTZXZlcml0eS5DUklUSUNBTDpcclxuICAgICAgICByZXR1cm4gNDtcclxuICAgICAgZGVmYXVsdDpcclxuICAgICAgICByZXR1cm4gMDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEZvcm1hdCBhdWRpdCBlbnRyeSBmb3IgZGlzcGxheVxyXG4gICAqL1xyXG4gIHN0YXRpYyBmb3JtYXRFbnRyeShlbnRyeTogQXVkaXRFbnRyeSk6IHN0cmluZyB7XHJcbiAgICBjb25zdCB0aW1lc3RhbXAgPSBlbnRyeS50aW1lc3RhbXAudG9JU09TdHJpbmcoKTtcclxuICAgIGNvbnN0IHVzZXIgPSBlbnRyeS51c2VyPy51c2VybmFtZSB8fCBlbnRyeS51c2VyPy51c2VySWQgfHwgJ3N5c3RlbSc7XHJcbiAgICBjb25zdCByZXNvdXJjZSA9IGVudHJ5LnJlc291cmNlID8gYCR7ZW50cnkucmVzb3VyY2UucmVzb3VyY2VUeXBlfToke2VudHJ5LnJlc291cmNlLnJlc291cmNlSWR9YCA6ICdOL0EnO1xyXG4gICAgXHJcbiAgICByZXR1cm4gYFske3RpbWVzdGFtcH1dICR7dXNlcn0gJHtlbnRyeS5hY3Rpb259ICR7cmVzb3VyY2V9IC0gJHtlbnRyeS5yZXN1bHR9ICgke2VudHJ5LnNldmVyaXR5fSlgO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ3JlYXRlIGF1ZGl0IHF1ZXJ5IGZyb20gcGFyYW1ldGVyc1xyXG4gICAqL1xyXG4gIHN0YXRpYyBjcmVhdGVRdWVyeShwYXJhbXM6IEF1ZGl0UXVlcnlQYXJhbXMpOiBSZWNvcmQ8c3RyaW5nLCBhbnk+IHtcclxuICAgIGNvbnN0IHF1ZXJ5OiBSZWNvcmQ8c3RyaW5nLCBhbnk+ID0ge307XHJcblxyXG4gICAgaWYgKHBhcmFtcy5zdGFydERhdGUpIHtcclxuICAgICAgcXVlcnkudGltZXN0YW1wID0geyAkZ3RlOiBwYXJhbXMuc3RhcnREYXRlIH07XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHBhcmFtcy5lbmREYXRlKSB7XHJcbiAgICAgIHF1ZXJ5LnRpbWVzdGFtcCA9IHsgLi4ucXVlcnkudGltZXN0YW1wLCAkbHRlOiBwYXJhbXMuZW5kRGF0ZSB9O1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChwYXJhbXMudXNlcklkKSB7XHJcbiAgICAgIHF1ZXJ5Wyd1c2VyLnVzZXJJZCddID0gcGFyYW1zLnVzZXJJZDtcclxuICAgIH1cclxuXHJcbiAgICBpZiAocGFyYW1zLmFjdGlvbnMgJiYgcGFyYW1zLmFjdGlvbnMubGVuZ3RoID4gMCkge1xyXG4gICAgICBxdWVyeS5hY3Rpb24gPSB7ICRpbjogcGFyYW1zLmFjdGlvbnMgfTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAocGFyYW1zLnJlc3VsdHMgJiYgcGFyYW1zLnJlc3VsdHMubGVuZ3RoID4gMCkge1xyXG4gICAgICBxdWVyeS5yZXN1bHQgPSB7ICRpbjogcGFyYW1zLnJlc3VsdHMgfTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAocGFyYW1zLnNldmVyaXRpZXMgJiYgcGFyYW1zLnNldmVyaXRpZXMubGVuZ3RoID4gMCkge1xyXG4gICAgICBxdWVyeS5zZXZlcml0eSA9IHsgJGluOiBwYXJhbXMuc2V2ZXJpdGllcyB9O1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChwYXJhbXMuY2F0ZWdvcmllcyAmJiBwYXJhbXMuY2F0ZWdvcmllcy5sZW5ndGggPiAwKSB7XHJcbiAgICAgIHF1ZXJ5LmNhdGVnb3J5ID0geyAkaW46IHBhcmFtcy5jYXRlZ29yaWVzIH07XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHBhcmFtcy5yZXNvdXJjZVR5cGVzICYmIHBhcmFtcy5yZXNvdXJjZVR5cGVzLmxlbmd0aCA+IDApIHtcclxuICAgICAgcXVlcnlbJ3Jlc291cmNlLnJlc291cmNlVHlwZSddID0geyAkaW46IHBhcmFtcy5yZXNvdXJjZVR5cGVzIH07XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHBhcmFtcy5yZXNvdXJjZUlkcyAmJiBwYXJhbXMucmVzb3VyY2VJZHMubGVuZ3RoID4gMCkge1xyXG4gICAgICBxdWVyeVsncmVzb3VyY2UucmVzb3VyY2VJZCddID0geyAkaW46IHBhcmFtcy5yZXNvdXJjZUlkcyB9O1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChwYXJhbXMuc2VhcmNoKSB7XHJcbiAgICAgIHF1ZXJ5LiRvciA9IFtcclxuICAgICAgICB7IGRlc2NyaXB0aW9uOiB7ICRyZWdleDogcGFyYW1zLnNlYXJjaCwgJG9wdGlvbnM6ICdpJyB9IH0sXHJcbiAgICAgICAgeyBtZXNzYWdlOiB7ICRyZWdleDogcGFyYW1zLnNlYXJjaCwgJG9wdGlvbnM6ICdpJyB9IH0sXHJcbiAgICAgIF07XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHBhcmFtcy5taW5SaXNrU2NvcmUgIT09IHVuZGVmaW5lZCB8fCBwYXJhbXMubWF4Umlza1Njb3JlICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgcXVlcnkucmlza1Njb3JlID0ge307XHJcbiAgICAgIGlmIChwYXJhbXMubWluUmlza1Njb3JlICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICBxdWVyeS5yaXNrU2NvcmUuJGd0ZSA9IHBhcmFtcy5taW5SaXNrU2NvcmU7XHJcbiAgICAgIH1cclxuICAgICAgaWYgKHBhcmFtcy5tYXhSaXNrU2NvcmUgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICAgIHF1ZXJ5LnJpc2tTY29yZS4kbHRlID0gcGFyYW1zLm1heFJpc2tTY29yZTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGlmIChwYXJhbXMuY29tcGxpYW5jZVRhZ3MgJiYgcGFyYW1zLmNvbXBsaWFuY2VUYWdzLmxlbmd0aCA+IDApIHtcclxuICAgICAgcXVlcnkuY29tcGxpYW5jZVRhZ3MgPSB7ICRpbjogcGFyYW1zLmNvbXBsaWFuY2VUYWdzIH07XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHBhcmFtcy50ZW5hbnRJZCkge1xyXG4gICAgICBxdWVyeVsnc3lzdGVtLnRlbmFudElkJ10gPSBwYXJhbXMudGVuYW50SWQ7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHBhcmFtcy5hcHBsaWNhdGlvbnMgJiYgcGFyYW1zLmFwcGxpY2F0aW9ucy5sZW5ndGggPiAwKSB7XHJcbiAgICAgIHF1ZXJ5WydzeXN0ZW0uYXBwbGljYXRpb24nXSA9IHsgJGluOiBwYXJhbXMuYXBwbGljYXRpb25zIH07XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHBhcmFtcy5lbnZpcm9ubWVudHMgJiYgcGFyYW1zLmVudmlyb25tZW50cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgIHF1ZXJ5WydzeXN0ZW0uZW52aXJvbm1lbnQnXSA9IHsgJGluOiBwYXJhbXMuZW52aXJvbm1lbnRzIH07XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHF1ZXJ5O1xyXG4gIH1cclxufSJdLCJ2ZXJzaW9uIjozfQ==