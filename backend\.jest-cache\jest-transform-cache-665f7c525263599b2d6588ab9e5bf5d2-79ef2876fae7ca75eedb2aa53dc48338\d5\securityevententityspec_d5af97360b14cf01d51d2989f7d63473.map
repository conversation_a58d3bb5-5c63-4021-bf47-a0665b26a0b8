{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\event\\security-event.entity.spec.ts", "mappings": ";;AAAA,mEAAwD;AACxD,gHAA+F;AAC/F,kHAAiG;AACjG,4GAA2F;AAC3F,2FAAiF;AACjF,+EAAqE;AACrE,4FAAsF;AACtF,0GAAmG;AAEnG,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,IAAI,QAAuB,CAAC;IAC5B,IAAI,OAA4B,CAAC;IAEjC,UAAU,CAAC,GAAG,EAAE;QACd,MAAM,SAAS,GAAG,6CAAc,CAAC,MAAM,EAAE,CAAC;QAC1C,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACtE,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACnD,OAAO,GAAG;YACR,QAAQ,EAAE,eAAe;YACzB,aAAa,EAAE,WAAW;YAC1B,IAAI,EAAE,GAAG;YACT,QAAQ,EAAE,KAAK;YACf,MAAM,EAAE,OAAO;SAChB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,KAAK,GAAG,qCAAa,CAAC,MAAM,CAChC,QAAQ,EACR,OAAO,EACP,6BAA6B,EAC7B;gBACE,WAAW,EAAE,uCAAuC;gBACpD,QAAQ,EAAE,SAAS;gBACnB,WAAW,EAAE,UAAU;gBACvB,IAAI,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;gBAC/B,UAAU,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;aACnC,CACF,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACxE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,GAAG,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,KAAK,GAAG,qCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;YACpE,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;YAE7C,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,wDAAyB,CAAC,CAAC;YAElE,MAAM,YAAY,GAAG,YAAY,CAAC,CAAC,CAA8B,CAAC;YAClE,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACnD,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,QAAQ,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;YACtG,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;YACtG,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClC,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;QACvH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,eAAe,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC,CAAC;iBAC5F,OAAO,CAAC,iDAAiD,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,IAAI,KAAoB,CAAC;QAEzB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,qCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;YAC9D,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,yCAAyC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;YAEtD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;YAC7D,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;YACtD,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;YAE7C,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,qEAA+B,CAAC,CAAC;YAExE,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAoC,CAAC;YACvE,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,GAAG,CAAC,CAAC;YAC9D,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,QAAQ,CAAC,CAAC;iBAC7D,OAAO,CAAC,gDAAgD,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;YACtD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,UAAU,CAAC,CAAC;YACrD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,SAAS,CAAC,CAAC;YACpD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,QAAQ,CAAC,CAAC;YACnD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,SAAS,CAAC,CAAC;YACpD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,QAAQ,CAAC,CAAC;YACnD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;iBAChE,OAAO,CAAC,oDAAoD,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;YACtD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,MAAM,EAAE;gBAC/C,YAAY,EAAE,uCAAuC;gBACrD,kBAAkB,EAAE,IAAI;aACzB,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACzE,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEzC,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B;YAErE,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,YAAY,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,IAAI,KAAoB,CAAC;QAEzB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,qCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEvC,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;YACtD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,UAAU,CAAC,CAAC;YACrD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,SAAS,CAAC,CAAC;YACpD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,QAAQ,CAAC,CAAC;YACnD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,SAAS,CAAC,CAAC;YACpD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,QAAQ,CAAC,CAAC;YACnD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEzC,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAExC,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,UAAU,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE9C,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;YACtD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAErC,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;YACtD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpC,gCAAgC;YAChC,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,YAAY,CAAC,CAAC;YACvD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,MAAM,CAAC,CAAC;YACjD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,YAAY,CAAC,CAAC;YACvD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,IAAI,KAAoB,CAAC;QAEzB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,qCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE;gBAC5D,IAAI,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;aAC1B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qBAAqB,EAAE,GAAG,EAAE;YAC7B,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC;YAErC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,KAAK,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;YAElC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,KAAK,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YAE9B,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,IAAI,KAAoB,CAAC;QAEzB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,qCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE;gBAC5D,UAAU,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,EAAE;aAChD,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,KAAK,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAE5C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,KAAK,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YAElC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,IAAI,KAAoB,CAAC;QAEzB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,qCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;YACtD,KAAK,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAEvC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,4DAA4D;YAC5D,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;YACtD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,UAAU,CAAC,CAAC;YACrD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,SAAS,CAAC,CAAC;YACpD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,QAAQ,CAAC,CAAC;YACnD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,SAAS,CAAC,CAAC;YACpD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,QAAQ,CAAC,CAAC;YAEnD,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAE3B,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,QAAQ,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,KAAK,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;YAE/C,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,eAAe,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAEzC,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;iBACnD,OAAO,CAAC,2CAA2C,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,IAAI,KAAoB,CAAC;QAEzB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,qCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAE3B,MAAM,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,WAAW,CAAC,CAAC;YACtD,KAAK,CAAC,YAAY,CAAC,oDAAqB,CAAC,MAAM,EAAE,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC,CAAC;YAEjF,MAAM,OAAO,GAAG,KAAK,CAAC,oBAAoB,EAAE,CAAC;YAE7C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,MAAM,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAE5B,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,GAAG,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,IAAI,KAAoB,CAAC;QAEzB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,qCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC;YAC3B,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;YAE3B,2CAA2C;YAC3C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACxB,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEtB,uCAAuC;YACvC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACpC,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;YACpC,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC;YAEjC,6CAA6C;YAC7C,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\event\\security-event.entity.spec.ts"], "sourcesContent": ["import { SecurityEvent } from './security-event.entity';\r\nimport { EventMetadata } from '../../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../value-objects/event-metadata/event-source.value-object';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../../enums/event-source-type.enum';\r\nimport { SecurityEventCreatedEvent } from '../../events/security-event-created.event';\r\nimport { SecurityEventStatusChangedEvent } from '../../events/security-event-status-changed.event';\r\n\r\ndescribe('SecurityEvent Entity', () => {\r\n  let metadata: EventMetadata;\r\n  let rawData: Record<string, any>;\r\n\r\n  beforeEach(() => {\r\n    const timestamp = EventTimestamp.create();\r\n    const source = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n    metadata = EventMetadata.create(timestamp, source);\r\n    rawData = {\r\n      sourceIp: '*************',\r\n      destinationIp: '*********',\r\n      port: 443,\r\n      protocol: 'TCP',\r\n      action: 'BLOCK',\r\n    };\r\n  });\r\n\r\n  describe('Creation', () => {\r\n    it('should create a new security event', () => {\r\n      const event = SecurityEvent.create(\r\n        metadata,\r\n        rawData,\r\n        'Firewall blocked connection',\r\n        {\r\n          description: 'Suspicious connection attempt blocked',\r\n          category: 'network',\r\n          subcategory: 'firewall',\r\n          tags: ['blocked', 'suspicious'],\r\n          attributes: { severity: 'medium' },\r\n        }\r\n      );\r\n\r\n      expect(event.title).toBe('Firewall blocked connection');\r\n      expect(event.description).toBe('Suspicious connection attempt blocked');\r\n      expect(event.category).toBe('network');\r\n      expect(event.subcategory).toBe('firewall');\r\n      expect(event.status).toBe(EventProcessingStatus.RAW);\r\n      expect(event.tags).toEqual(['blocked', 'suspicious']);\r\n      expect(event.getAttribute('severity')).toBe('medium');\r\n      expect(event.processingAttempts).toBe(0);\r\n    });\r\n\r\n    it('should publish SecurityEventCreatedEvent on creation', () => {\r\n      const event = SecurityEvent.create(metadata, rawData, 'Test Event');\r\n      const domainEvents = event.getDomainEvents();\r\n\r\n      expect(domainEvents).toHaveLength(1);\r\n      expect(domainEvents[0]).toBeInstanceOf(SecurityEventCreatedEvent);\r\n      \r\n      const createdEvent = domainEvents[0] as SecurityEventCreatedEvent;\r\n      expect(createdEvent.eventTitle).toBe('Test Event');\r\n      expect(createdEvent.sourceType).toBe(EventSourceType.FIREWALL);\r\n    });\r\n\r\n    it('should validate required fields', () => {\r\n      expect(() => SecurityEvent.create(metadata, {}, 'Test')).toThrow('Security event must have raw data');\r\n      expect(() => SecurityEvent.create(metadata, rawData, '')).toThrow('Security event must have a title');\r\n      expect(() => SecurityEvent.create(metadata, rawData, '   ')).toThrow('Security event must have a title');\r\n    });\r\n\r\n    it('should validate title length', () => {\r\n      const longTitle = 'a'.repeat(256);\r\n      expect(() => SecurityEvent.create(metadata, rawData, longTitle)).toThrow('Event title cannot exceed 255 characters');\r\n    });\r\n\r\n    it('should validate description length', () => {\r\n      const longDescription = 'a'.repeat(2001);\r\n      expect(() => SecurityEvent.create(metadata, rawData, 'Test', { description: longDescription }))\r\n        .toThrow('Event description cannot exceed 2000 characters');\r\n    });\r\n  });\r\n\r\n  describe('Status Management', () => {\r\n    let event: SecurityEvent;\r\n\r\n    beforeEach(() => {\r\n      event = SecurityEvent.create(metadata, rawData, 'Test Event');\r\n      event.clearDomainEvents(); // Clear creation event for cleaner tests\r\n    });\r\n\r\n    it('should change status to valid next status', () => {\r\n      event.changeStatus(EventProcessingStatus.NORMALIZING);\r\n\r\n      expect(event.status).toBe(EventProcessingStatus.NORMALIZING);\r\n      expect(event.processingAttempts).toBe(1);\r\n      expect(event.lastProcessedAt).toBeDefined();\r\n    });\r\n\r\n    it('should publish SecurityEventStatusChangedEvent on status change', () => {\r\n      event.changeStatus(EventProcessingStatus.NORMALIZING);\r\n      const domainEvents = event.getDomainEvents();\r\n\r\n      expect(domainEvents).toHaveLength(1);\r\n      expect(domainEvents[0]).toBeInstanceOf(SecurityEventStatusChangedEvent);\r\n      \r\n      const statusEvent = domainEvents[0] as SecurityEventStatusChangedEvent;\r\n      expect(statusEvent.oldStatus).toBe(EventProcessingStatus.RAW);\r\n      expect(statusEvent.newStatus).toBe(EventProcessingStatus.NORMALIZING);\r\n    });\r\n\r\n    it('should reject invalid status transitions', () => {\r\n      expect(() => event.changeStatus(EventProcessingStatus.RESOLVED))\r\n        .toThrow('Invalid status transition from raw to resolved');\r\n    });\r\n\r\n    it('should prevent status changes from terminal states', () => {\r\n      event.changeStatus(EventProcessingStatus.NORMALIZING);\r\n      event.changeStatus(EventProcessingStatus.NORMALIZED);\r\n      event.changeStatus(EventProcessingStatus.ENRICHING);\r\n      event.changeStatus(EventProcessingStatus.ENRICHED);\r\n      event.changeStatus(EventProcessingStatus.ANALYZING);\r\n      event.changeStatus(EventProcessingStatus.ANALYZED);\r\n      event.changeStatus(EventProcessingStatus.RESOLVED);\r\n\r\n      expect(() => event.changeStatus(EventProcessingStatus.NORMALIZING))\r\n        .toThrow('Cannot change status from terminal state: resolved');\r\n    });\r\n\r\n    it('should handle error status with message', () => {\r\n      event.changeStatus(EventProcessingStatus.NORMALIZING);\r\n      event.changeStatus(EventProcessingStatus.FAILED, {\r\n        errorMessage: 'Processing failed due to invalid data',\r\n        processingDuration: 5000,\r\n      });\r\n\r\n      expect(event.status).toBe(EventProcessingStatus.FAILED);\r\n      expect(event.errorMessage).toBe('Processing failed due to invalid data');\r\n      expect(event.processingDuration).toBe(5000);\r\n    });\r\n\r\n    it('should track processing attempts correctly', () => {\r\n      event.changeStatus(EventProcessingStatus.NORMALIZING);\r\n      expect(event.processingAttempts).toBe(1);\r\n\r\n      event.changeStatus(EventProcessingStatus.FAILED);\r\n      expect(event.processingAttempts).toBe(1); // Failed doesn't increment\r\n\r\n      event.changeStatus(EventProcessingStatus.REPROCESSING);\r\n      expect(event.processingAttempts).toBe(2);\r\n    });\r\n  });\r\n\r\n  describe('Status Queries', () => {\r\n    let event: SecurityEvent;\r\n\r\n    beforeEach(() => {\r\n      event = SecurityEvent.create(metadata, rawData, 'Test Event');\r\n    });\r\n\r\n    it('should identify terminal statuses', () => {\r\n      expect(event.isTerminal()).toBe(false);\r\n\r\n      event.changeStatus(EventProcessingStatus.NORMALIZING);\r\n      event.changeStatus(EventProcessingStatus.NORMALIZED);\r\n      event.changeStatus(EventProcessingStatus.ENRICHING);\r\n      event.changeStatus(EventProcessingStatus.ENRICHED);\r\n      event.changeStatus(EventProcessingStatus.ANALYZING);\r\n      event.changeStatus(EventProcessingStatus.ANALYZED);\r\n      event.changeStatus(EventProcessingStatus.RESOLVED);\r\n\r\n      expect(event.isTerminal()).toBe(true);\r\n    });\r\n\r\n    it('should identify in-progress statuses', () => {\r\n      expect(event.isInProgress()).toBe(false);\r\n\r\n      event.changeStatus(EventProcessingStatus.NORMALIZING);\r\n      expect(event.isInProgress()).toBe(true);\r\n\r\n      event.changeStatus(EventProcessingStatus.NORMALIZED);\r\n      expect(event.isInProgress()).toBe(false);\r\n    });\r\n\r\n    it('should identify statuses requiring attention', () => {\r\n      expect(event.requiresAttention()).toBe(false);\r\n\r\n      event.changeStatus(EventProcessingStatus.NORMALIZING);\r\n      event.changeStatus(EventProcessingStatus.FAILED);\r\n      expect(event.requiresAttention()).toBe(true);\r\n    });\r\n\r\n    it('should identify retryable events', () => {\r\n      expect(event.canRetry()).toBe(false);\r\n\r\n      event.changeStatus(EventProcessingStatus.NORMALIZING);\r\n      event.changeStatus(EventProcessingStatus.FAILED);\r\n      expect(event.canRetry()).toBe(true);\r\n\r\n      // Simulate max attempts reached\r\n      event.changeStatus(EventProcessingStatus.REPROCESSING);\r\n      event.changeStatus(EventProcessingStatus.FAILED);\r\n      event.changeStatus(EventProcessingStatus.REPROCESSING);\r\n      event.changeStatus(EventProcessingStatus.FAILED);\r\n      expect(event.canRetry()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Tag Management', () => {\r\n    let event: SecurityEvent;\r\n\r\n    beforeEach(() => {\r\n      event = SecurityEvent.create(metadata, rawData, 'Test Event', {\r\n        tags: ['initial', 'test'],\r\n      });\r\n    });\r\n\r\n    it('should add new tags', () => {\r\n      event.addTags(['new', 'additional']);\r\n\r\n      expect(event.tags).toEqual(['initial', 'test', 'new', 'additional']);\r\n    });\r\n\r\n    it('should not duplicate existing tags', () => {\r\n      event.addTags(['initial', 'new']);\r\n\r\n      expect(event.tags).toEqual(['initial', 'test', 'new']);\r\n    });\r\n\r\n    it('should remove tags', () => {\r\n      event.removeTags(['initial']);\r\n\r\n      expect(event.tags).toEqual(['test']);\r\n    });\r\n\r\n    it('should check tag existence', () => {\r\n      expect(event.hasTag('initial')).toBe(true);\r\n      expect(event.hasTag('nonexistent')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Attribute Management', () => {\r\n    let event: SecurityEvent;\r\n\r\n    beforeEach(() => {\r\n      event = SecurityEvent.create(metadata, rawData, 'Test Event', {\r\n        attributes: { severity: 'medium', priority: 5 },\r\n      });\r\n    });\r\n\r\n    it('should get attribute values', () => {\r\n      expect(event.getAttribute('severity')).toBe('medium');\r\n      expect(event.getAttribute('priority')).toBe(5);\r\n      expect(event.getAttribute('nonexistent')).toBeUndefined();\r\n    });\r\n\r\n    it('should set attribute values', () => {\r\n      event.setAttribute('newAttribute', 'value');\r\n\r\n      expect(event.getAttribute('newAttribute')).toBe('value');\r\n    });\r\n\r\n    it('should remove attributes', () => {\r\n      event.removeAttribute('severity');\r\n\r\n      expect(event.getAttribute('severity')).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('Convenience Methods', () => {\r\n    let event: SecurityEvent;\r\n\r\n    beforeEach(() => {\r\n      event = SecurityEvent.create(metadata, rawData, 'Test Event');\r\n    });\r\n\r\n    it('should mark event as failed', () => {\r\n      event.changeStatus(EventProcessingStatus.NORMALIZING);\r\n      event.markAsFailed('Test error', 1000);\r\n\r\n      expect(event.status).toBe(EventProcessingStatus.FAILED);\r\n      expect(event.errorMessage).toBe('Test error');\r\n      expect(event.processingDuration).toBe(1000);\r\n    });\r\n\r\n    it('should mark event as resolved', () => {\r\n      // Progress through valid statuses to reach resolvable state\r\n      event.changeStatus(EventProcessingStatus.NORMALIZING);\r\n      event.changeStatus(EventProcessingStatus.NORMALIZED);\r\n      event.changeStatus(EventProcessingStatus.ENRICHING);\r\n      event.changeStatus(EventProcessingStatus.ENRICHED);\r\n      event.changeStatus(EventProcessingStatus.ANALYZING);\r\n      event.changeStatus(EventProcessingStatus.ANALYZED);\r\n      \r\n      event.markAsResolved(5000);\r\n\r\n      expect(event.status).toBe(EventProcessingStatus.RESOLVED);\r\n      expect(event.processingDuration).toBe(5000);\r\n    });\r\n\r\n    it('should update description', () => {\r\n      event.updateDescription('Updated description');\r\n\r\n      expect(event.description).toBe('Updated description');\r\n    });\r\n\r\n    it('should validate description length when updating', () => {\r\n      const longDescription = 'a'.repeat(2001);\r\n      \r\n      expect(() => event.updateDescription(longDescription))\r\n        .toThrow('Description cannot exceed 2000 characters');\r\n    });\r\n  });\r\n\r\n  describe('Metadata and Analysis', () => {\r\n    let event: SecurityEvent;\r\n\r\n    beforeEach(() => {\r\n      event = SecurityEvent.create(metadata, rawData, 'Test Event');\r\n    });\r\n\r\n    it('should provide event age', () => {\r\n      const age = event.getAge();\r\n\r\n      expect(age).toBeGreaterThanOrEqual(0);\r\n      expect(typeof age).toBe('number');\r\n    });\r\n\r\n    it('should provide processing summary', () => {\r\n      event.changeStatus(EventProcessingStatus.NORMALIZING);\r\n      event.changeStatus(EventProcessingStatus.FAILED, { errorMessage: 'Test error' });\r\n\r\n      const summary = event.getProcessingSummary();\r\n\r\n      expect(summary.status).toBe(EventProcessingStatus.FAILED);\r\n      expect(summary.attempts).toBe(1);\r\n      expect(summary.maxAttempts).toBe(3);\r\n      expect(summary.canRetry).toBe(true);\r\n      expect(summary.isTerminal).toBe(false);\r\n      expect(summary.requiresAttention).toBe(true);\r\n      expect(summary.errorMessage).toBe('Test error');\r\n    });\r\n\r\n    it('should serialize to JSON', () => {\r\n      const json = event.toJSON();\r\n\r\n      expect(json.id).toBeDefined();\r\n      expect(json.metadata).toBeDefined();\r\n      expect(json.status).toBe(EventProcessingStatus.RAW);\r\n      expect(json.title).toBe('Test Event');\r\n      expect(json.processingSummary).toBeDefined();\r\n      expect(json.age).toBeGreaterThanOrEqual(0);\r\n    });\r\n  });\r\n\r\n  describe('Data Access', () => {\r\n    let event: SecurityEvent;\r\n\r\n    beforeEach(() => {\r\n      event = SecurityEvent.create(metadata, rawData, 'Test Event');\r\n    });\r\n\r\n    it('should provide immutable access to raw data', () => {\r\n      const data = event.rawData;\r\n      data.newField = 'modified';\r\n\r\n      // Original raw data should not be modified\r\n      expect(event.rawData.newField).toBeUndefined();\r\n    });\r\n\r\n    it('should provide immutable access to tags', () => {\r\n      event.addTags(['test']);\r\n      const tags = event.tags;\r\n      tags.push('modified');\r\n\r\n      // Original tags should not be modified\r\n      expect(event.tags).not.toContain('modified');\r\n    });\r\n\r\n    it('should provide immutable access to attributes', () => {\r\n      event.setAttribute('test', 'value');\r\n      const attributes = event.attributes;\r\n      attributes.newField = 'modified';\r\n\r\n      // Original attributes should not be modified\r\n      expect(event.attributes.newField).toBeUndefined();\r\n    });\r\n  });\r\n});\r\n"], "version": 3}