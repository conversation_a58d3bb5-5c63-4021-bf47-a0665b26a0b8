{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\aggregates\\__tests__\\ai-model.aggregate.spec.ts", "mappings": ";;AAAA,4HAA0G;AAC1G,oEAAoF;AACpF,uEAAkE;AAClE,8DAAwG;AAExG,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,IAAI,SAA2B,CAAC;IAEhC,UAAU,CAAC,GAAG,EAAE;QACd,SAAS,GAAG,IAAI,qCAAgB,EAAE,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,OAAO;gBAChB,QAAQ,EAAE,4BAAU,CAAC,MAAM;gBAC3B,SAAS,EAAE,2BAAS,CAAC,cAAc;gBACnC,kBAAkB,EAAE,CAAC,iBAAiB,CAAC;aACxC,CAAC;YAEF,MAAM,KAAK,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAE7C,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,OAAO;gBAChB,QAAQ,EAAE,4BAAU,CAAC,MAAM;gBAC3B,SAAS,EAAE,2BAAS,CAAC,cAAc;gBACnC,kBAAkB,EAAE,CAAC,iBAAiB,CAAC;aACxC,CAAC;YAEF,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAE/B,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAClD,mEAAmE,CACpE,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,KAAK,GAAG,iCAAc,CAAC,gBAAgB,EAAE,CAAC;YAEhD,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAE1B,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,KAAK,GAAG,iCAAc,CAAC,gBAAgB,EAAE,CAAC;YAEhD,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAE1B,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAC7C,kBAAkB,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,+BAA+B,CACrE,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qBAAqB,EAAE,GAAG,EAAE;YAC7B,MAAM,KAAK,GAAG,iCAAc,CAAC,gBAAgB,EAAE,CAAC;YAChD,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAE1B,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAEhC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,6BAAW,CAAC,QAAQ,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,aAAa,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAEhD,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CACxD,kBAAkB,aAAa,CAAC,QAAQ,EAAE,aAAa,CACxD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,UAAU,CAAC,GAAG,EAAE;YACd,qBAAqB;YACrB,MAAM,MAAM,GAAG;gBACb,iCAAc,CAAC,gBAAgB,CAAC;oBAC9B,IAAI,EAAE,YAAY;oBAClB,QAAQ,EAAE,4BAAU,CAAC,MAAM;oBAC3B,SAAS,EAAE,2BAAS,CAAC,cAAc;oBACnC,kBAAkB,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;iBAC1D,CAAC;gBACF,iCAAc,CAAC,gBAAgB,CAAC;oBAC9B,IAAI,EAAE,gBAAgB;oBACtB,QAAQ,EAAE,4BAAU,CAAC,OAAO;oBAC5B,SAAS,EAAE,2BAAS,CAAC,cAAc;oBACnC,kBAAkB,EAAE,CAAC,iBAAiB,CAAC;iBACxC,CAAC;gBACF,iCAAc,CAAC,gBAAgB,CAAC;oBAC9B,IAAI,EAAE,uBAAuB;oBAC7B,QAAQ,EAAE,4BAAU,CAAC,UAAU;oBAC/B,SAAS,EAAE,2BAAS,CAAC,cAAc;oBACnC,kBAAkB,EAAE,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;iBAC5D,CAAC;aACH,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACrB,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC1B,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,uBAAuB;YAC3C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,MAAM,MAAM,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC;YAExC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,MAAM,GAAG,SAAS,CAAC,eAAe,EAAE,CAAC;YAE3C,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,6BAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,QAAQ,GAA2B;gBACvC,QAAQ,EAAE,iBAAiB;aAC5B,CAAC;YAEF,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAE9C,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,QAAQ,GAA2B;gBACvC,QAAQ,EAAE,4BAAU,CAAC,MAAM;aAC5B,CAAC;YAEF,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAE9C,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,4BAAU,CAAC,MAAM,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,QAAQ,GAA2B;gBACvC,SAAS,EAAE,2BAAS,CAAC,cAAc;aACpC,CAAC;YAEF,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAE9C,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,KAAK,2BAAS,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,QAAQ,GAA2B;gBACvC,QAAQ,EAAE,gBAAgB;gBAC1B,SAAS,EAAE,2BAAS,CAAC,cAAc;aACpC,CAAC;YAEF,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAE9C,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,cAAc,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,mCAAmC;YACnC,MAAM,MAAM,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC;YACxC,MAAM,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC,CAAC;YAE5E,MAAM,QAAQ,GAA2B;gBACvC,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,GAAG;aAChB,CAAC;YAEF,MAAM,WAAW,GAAG,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;YACxE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,6BAA6B;YAC7B,MAAM,MAAM,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC;YACxC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC;YAEtD,MAAM,QAAQ,GAA2B;gBACvC,oBAAoB,EAAE,IAAI;aAC3B,CAAC;YAEF,MAAM,eAAe,GAAG,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAEvD,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,UAAU,CAAC,GAAG,EAAE;YACd,gEAAgE;YAChE,MAAM,MAAM,GAAG;gBACb,iCAAc,CAAC,gBAAgB,CAAC;oBAC9B,IAAI,EAAE,kBAAkB;oBACxB,kBAAkB,EAAE,CAAC,gBAAgB,CAAC;oBACtC,QAAQ,EAAE,CAAC;oBACX,MAAM,EAAE,CAAC;iBACV,CAAC;gBACF,iCAAc,CAAC,gBAAgB,CAAC;oBAC9B,IAAI,EAAE,oBAAoB;oBAC1B,kBAAkB,EAAE,CAAC,gBAAgB,CAAC;oBACtC,QAAQ,EAAE,CAAC;oBACX,MAAM,EAAE,CAAC;iBACV,CAAC;gBACF,iCAAc,CAAC,gBAAgB,CAAC;oBAC9B,IAAI,EAAE,iBAAiB;oBACvB,kBAAkB,EAAE,CAAC,gBAAgB,CAAC;oBACtC,QAAQ,EAAE,CAAC;oBACX,MAAM,EAAE,CAAC;iBACV,CAAC;aACH,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACrB,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC1B,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,oCAAoC;YACpC,MAAM,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC,CAAC;YAC5E,MAAM,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC,CAAC;YAC5E,MAAM,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,QAAQ,GAA2B;gBACvC,QAAQ,EAAE,gBAAgB;aAC3B,CAAC;YAEF,MAAM,SAAS,GAAG,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAEtD,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,CAAC,SAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,QAAQ,GAA2B;gBACvC,QAAQ,EAAE,mBAAmB;aAC9B,CAAC;YAEF,MAAM,SAAS,GAAG,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAEtD,MAAM,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,+BAA+B;YAC/B,MAAM,MAAM,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC;YACxC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;YACvB,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;YAEvB,MAAM,QAAQ,GAA2B;gBACvC,QAAQ,EAAE,gBAAgB;gBAC1B,oBAAoB,EAAE,IAAI;aAC3B,CAAC;YAEF,MAAM,SAAS,GAAG,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAEtD,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,CAAC,SAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,MAAM,GAAG;gBACb,iCAAc,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,kBAAkB,EAAE,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC5F,iCAAc,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,kBAAkB,EAAE,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC5F,iCAAc,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,kBAAkB,EAAE,CAAC,gBAAgB,CAAC,EAAE,CAAC;aAC7F,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACrB,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC1B,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,sBAAsB;YACtB,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,QAAQ,GAA2B;gBACvC,QAAQ,EAAE,gBAAgB;aAC3B,CAAC;YAEF,MAAM,QAAQ,GAA0B;gBACtC,IAAI,EAAE,cAAc;aACrB,CAAC;YAEF,MAAM,aAAa,GAAG,SAAS,CAAC,2BAA2B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,aAAc,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,gBAAgB;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,QAAQ,GAA2B;gBACvC,QAAQ,EAAE,gBAAgB;aAC3B,CAAC;YAEF,MAAM,QAAQ,GAA0B;gBACtC,IAAI,EAAE,aAAa;aACpB,CAAC;YAEF,MAAM,aAAa,GAAG,SAAS,CAAC,2BAA2B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,6BAA6B;YAC7B,MAAM,MAAM,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC;YACxC,MAAM,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YACvD,MAAM,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YACvD,MAAM,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAEvD,MAAM,QAAQ,GAA2B;gBACvC,QAAQ,EAAE,gBAAgB;aAC3B,CAAC;YAEF,MAAM,QAAQ,GAA0B;gBACtC,IAAI,EAAE,mBAAmB;aAC1B,CAAC;YAEF,MAAM,aAAa,GAAG,SAAS,CAAC,2BAA2B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,aAAc,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,mBAAmB;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,QAAQ,GAA2B;gBACvC,QAAQ,EAAE,mBAAmB;aAC9B,CAAC;YAEF,MAAM,QAAQ,GAA0B;gBACtC,IAAI,EAAE,cAAc;aACrB,CAAC;YAEF,MAAM,aAAa,GAAG,SAAS,CAAC,2BAA2B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,aAAa,CAAC,CAAC,aAAa,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,MAAM,GAAG;gBACb,iCAAc,CAAC,gBAAgB,CAAC;oBAC9B,IAAI,EAAE,cAAc;oBACpB,QAAQ,EAAE,4BAAU,CAAC,MAAM;oBAC3B,SAAS,EAAE,2BAAS,CAAC,cAAc;iBACpC,CAAC;gBACF,iCAAc,CAAC,gBAAgB,CAAC;oBAC9B,IAAI,EAAE,eAAe;oBACrB,QAAQ,EAAE,4BAAU,CAAC,OAAO;oBAC5B,SAAS,EAAE,2BAAS,CAAC,cAAc;iBACpC,CAAC;gBACF,iCAAc,CAAC,gBAAgB,CAAC;oBAC9B,IAAI,EAAE,kBAAkB;oBACxB,QAAQ,EAAE,4BAAU,CAAC,UAAU;oBAC/B,SAAS,EAAE,2BAAS,CAAC,cAAc;iBACpC,CAAC;aACH,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YACnD,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACrB,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACrB,6BAA6B;YAE7B,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;YAExC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,4BAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,4BAAU,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,4BAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClE,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,2BAAS,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjE,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,2BAAS,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,MAAM,GAAG;gBACb,iCAAc,CAAC,gBAAgB,CAAC;oBAC9B,IAAI,EAAE,cAAc;oBACpB,QAAQ,EAAE,4BAAU,CAAC,MAAM;oBAC3B,kBAAkB,EAAE,CAAC,iBAAiB,CAAC;iBACxC,CAAC;gBACF,iCAAc,CAAC,gBAAgB,CAAC;oBAC9B,IAAI,EAAE,eAAe;oBACrB,QAAQ,EAAE,4BAAU,CAAC,OAAO;oBAC5B,kBAAkB,EAAE,CAAC,iBAAiB,CAAC;iBACxC,CAAC;gBACF,iCAAc,CAAC,gBAAgB,CAAC;oBAC9B,IAAI,EAAE,kBAAkB;oBACxB,QAAQ,EAAE,4BAAU,CAAC,UAAU;oBAC/B,kBAAkB,EAAE,CAAC,gBAAgB,CAAC;iBACvC,CAAC;aACH,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,QAAQ,GAA2B;gBACvC,QAAQ,EAAE,iBAAiB;aAC5B,CAAC;YAEF,MAAM,eAAe,GAAG,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAE3D,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,6BAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,4BAA4B;YAC5B,SAAS,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;YAE5D,MAAM,QAAQ,GAA2B;gBACvC,QAAQ,EAAE,4BAAU,CAAC,MAAM;aAC5B,CAAC;YAEF,MAAM,iBAAiB,GAAG,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAE/D,MAAM,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,6BAAW,CAAC,QAAQ,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,QAAQ,GAA2B;gBACvC,QAAQ,EAAE,4BAAU,CAAC,UAAU;aAChC,CAAC;YAEF,MAAM,cAAc,GAAG,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAEzD,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,6BAAW,CAAC,QAAQ,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,MAAM,GAAG;gBACb,iCAAc,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;gBACzD,iCAAc,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC;gBAC7D,iCAAc,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,uBAAuB,EAAE,CAAC;aACnE,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAEnD,4BAA4B;YAC5B,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,gDAAgD;YAEzE,iCAAiC;YACjC,MAAM,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,gBAAgB,GAAG,SAAS,CAAC,mBAAmB,EAAE,CAAC;YAEzD,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,oBAAoB,GAAG,SAAS,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;YAEpE,MAAM,CAAC,oBAAoB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,wBAAwB;YACxB,SAAS,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;YAE5D,MAAM,gBAAgB,GAAG,SAAS,CAAC,mBAAmB,EAAE,CAAC;YACzD,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzC,SAAS,CAAC,aAAa,EAAE,CAAC;YAE1B,MAAM,eAAe,GAAG,SAAS,CAAC,mBAAmB,EAAE,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,qCAAqC;YACrC,MAAM,MAAM,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC;YACxC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAErB,SAAS,CAAC,aAAa,EAAE,CAAC;YAE1B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,6BAAW,CAAC,MAAM,CAAC,CAAC,CAAC,uBAAuB;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\aggregates\\__tests__\\ai-model.aggregate.spec.ts"], "sourcesContent": ["import { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { AIProvider, ModelType, ModelStatus } from '../../entities/ai-model.entity';\r\nimport { AIModelFactory } from '../../factories/ai-model.factory';\r\nimport { AIModelAggregate, ModelSelectionCriteria, LoadBalancingStrategy } from '../ai-model.aggregate';\r\n\r\ndescribe('AIModelAggregate', () => {\r\n  let aggregate: AIModelAggregate;\r\n\r\n  beforeEach(() => {\r\n    aggregate = new AIModelAggregate();\r\n  });\r\n\r\n  describe('Model Management', () => {\r\n    it('should create and add a new model', () => {\r\n      const request = {\r\n        name: 'Test Model',\r\n        version: '1.0.0',\r\n        provider: AIProvider.OPENAI,\r\n        modelType: ModelType.LANGUAGE_MODEL,\r\n        supportedTaskTypes: ['text-generation'],\r\n      };\r\n\r\n      const model = aggregate.createModel(request);\r\n\r\n      expect(model.name).toBe('Test Model');\r\n      expect(aggregate.getAllModels()).toHaveLength(1);\r\n      expect(aggregate.getModel(model.id)).toBe(model);\r\n    });\r\n\r\n    it('should throw error when creating duplicate model', () => {\r\n      const request = {\r\n        name: 'Test Model',\r\n        version: '1.0.0',\r\n        provider: AIProvider.OPENAI,\r\n        modelType: ModelType.LANGUAGE_MODEL,\r\n        supportedTaskTypes: ['text-generation'],\r\n      };\r\n\r\n      aggregate.createModel(request);\r\n\r\n      expect(() => aggregate.createModel(request)).toThrow(\r\n        \"Model with name 'Test Model' already exists for provider 'openai'\"\r\n      );\r\n    });\r\n\r\n    it('should add existing model', () => {\r\n      const model = AIModelFactory.createForTesting();\r\n\r\n      aggregate.addModel(model);\r\n\r\n      expect(aggregate.getAllModels()).toHaveLength(1);\r\n      expect(aggregate.getModel(model.id)).toBe(model);\r\n    });\r\n\r\n    it('should throw error when adding duplicate model', () => {\r\n      const model = AIModelFactory.createForTesting();\r\n\r\n      aggregate.addModel(model);\r\n\r\n      expect(() => aggregate.addModel(model)).toThrow(\r\n        `Model with ID '${model.id.toString()}' already exists in aggregate`\r\n      );\r\n    });\r\n\r\n    it('should remove model', () => {\r\n      const model = AIModelFactory.createForTesting();\r\n      aggregate.addModel(model);\r\n\r\n      aggregate.removeModel(model.id);\r\n\r\n      expect(aggregate.getAllModels()).toHaveLength(0);\r\n      expect(model.status).toBe(ModelStatus.ARCHIVED);\r\n    });\r\n\r\n    it('should throw error when removing non-existent model', () => {\r\n      const nonExistentId = UniqueEntityId.generate();\r\n\r\n      expect(() => aggregate.removeModel(nonExistentId)).toThrow(\r\n        `Model with ID '${nonExistentId.toString()}' not found`\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('Model Querying', () => {\r\n    beforeEach(() => {\r\n      // Create test models\r\n      const models = [\r\n        AIModelFactory.createForTesting({\r\n          name: 'OpenAI GPT',\r\n          provider: AIProvider.OPENAI,\r\n          modelType: ModelType.LANGUAGE_MODEL,\r\n          supportedTaskTypes: ['text-generation', 'classification'],\r\n        }),\r\n        AIModelFactory.createForTesting({\r\n          name: 'Bedrock Claude',\r\n          provider: AIProvider.BEDROCK,\r\n          modelType: ModelType.LANGUAGE_MODEL,\r\n          supportedTaskTypes: ['text-generation'],\r\n        }),\r\n        AIModelFactory.createForTesting({\r\n          name: 'TensorFlow Classifier',\r\n          provider: AIProvider.TENSORFLOW,\r\n          modelType: ModelType.CLASSIFICATION,\r\n          supportedTaskTypes: ['classification', 'anomaly-detection'],\r\n        }),\r\n      ];\r\n\r\n      models.forEach(model => {\r\n        aggregate.addModel(model);\r\n        model.activate(); // Activate for testing\r\n      });\r\n    });\r\n\r\n    it('should get all models', () => {\r\n      const models = aggregate.getAllModels();\r\n\r\n      expect(models).toHaveLength(3);\r\n    });\r\n\r\n    it('should get active models only', () => {\r\n      const models = aggregate.getActiveModels();\r\n\r\n      expect(models).toHaveLength(3);\r\n      expect(models.every(model => model.status === ModelStatus.ACTIVE)).toBe(true);\r\n    });\r\n\r\n    it('should find models by task type', () => {\r\n      const criteria: ModelSelectionCriteria = {\r\n        taskType: 'text-generation',\r\n      };\r\n\r\n      const models = aggregate.findModels(criteria);\r\n\r\n      expect(models).toHaveLength(2);\r\n      expect(models.every(model => model.supportsTaskType('text-generation'))).toBe(true);\r\n    });\r\n\r\n    it('should find models by provider', () => {\r\n      const criteria: ModelSelectionCriteria = {\r\n        provider: AIProvider.OPENAI,\r\n      };\r\n\r\n      const models = aggregate.findModels(criteria);\r\n\r\n      expect(models).toHaveLength(1);\r\n      expect(models[0].provider).toBe(AIProvider.OPENAI);\r\n    });\r\n\r\n    it('should find models by model type', () => {\r\n      const criteria: ModelSelectionCriteria = {\r\n        modelType: ModelType.LANGUAGE_MODEL,\r\n      };\r\n\r\n      const models = aggregate.findModels(criteria);\r\n\r\n      expect(models).toHaveLength(2);\r\n      expect(models.every(model => model.modelType === ModelType.LANGUAGE_MODEL)).toBe(true);\r\n    });\r\n\r\n    it('should find models by multiple criteria', () => {\r\n      const criteria: ModelSelectionCriteria = {\r\n        taskType: 'classification',\r\n        modelType: ModelType.CLASSIFICATION,\r\n      };\r\n\r\n      const models = aggregate.findModels(criteria);\r\n\r\n      expect(models).toHaveLength(1);\r\n      expect(models[0].modelType).toBe(ModelType.CLASSIFICATION);\r\n      expect(models[0].supportsTaskType('classification')).toBe(true);\r\n    });\r\n\r\n    it('should find models by performance criteria', () => {\r\n      // Update performance for one model\r\n      const models = aggregate.getAllModels();\r\n      models[0].updatePerformanceMetrics({ accuracy: 0.95, averageLatency: 100 });\r\n\r\n      const criteria: ModelSelectionCriteria = {\r\n        minAccuracy: 0.9,\r\n        maxLatency: 200,\r\n      };\r\n\r\n      const foundModels = aggregate.findModels(criteria);\r\n\r\n      expect(foundModels).toHaveLength(1);\r\n      expect(foundModels[0].performance.accuracy).toBeGreaterThanOrEqual(0.9);\r\n      expect(foundModels[0].performance.averageLatency).toBeLessThanOrEqual(200);\r\n    });\r\n\r\n    it('should find models requiring availability', () => {\r\n      // Make one model unavailable\r\n      const models = aggregate.getAllModels();\r\n      models[0].updateLoad(models[0].maxConcurrentRequests);\r\n\r\n      const criteria: ModelSelectionCriteria = {\r\n        requiresAvailability: true,\r\n      };\r\n\r\n      const availableModels = aggregate.findModels(criteria);\r\n\r\n      expect(availableModels).toHaveLength(2);\r\n      expect(availableModels.every(model => model.canHandleRequest())).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('Model Selection', () => {\r\n    beforeEach(() => {\r\n      // Create test models with different performance characteristics\r\n      const models = [\r\n        AIModelFactory.createForTesting({\r\n          name: 'High Performance',\r\n          supportedTaskTypes: ['classification'],\r\n          priority: 8,\r\n          weight: 3,\r\n        }),\r\n        AIModelFactory.createForTesting({\r\n          name: 'Medium Performance',\r\n          supportedTaskTypes: ['classification'],\r\n          priority: 5,\r\n          weight: 2,\r\n        }),\r\n        AIModelFactory.createForTesting({\r\n          name: 'Low Performance',\r\n          supportedTaskTypes: ['classification'],\r\n          priority: 2,\r\n          weight: 1,\r\n        }),\r\n      ];\r\n\r\n      models.forEach(model => {\r\n        aggregate.addModel(model);\r\n        model.activate();\r\n      });\r\n\r\n      // Set different performance metrics\r\n      models[0].updatePerformanceMetrics({ accuracy: 0.95, averageLatency: 100 });\r\n      models[1].updatePerformanceMetrics({ accuracy: 0.85, averageLatency: 200 });\r\n      models[2].updatePerformanceMetrics({ accuracy: 0.75, averageLatency: 300 });\r\n    });\r\n\r\n    it('should select best model based on criteria', () => {\r\n      const criteria: ModelSelectionCriteria = {\r\n        taskType: 'classification',\r\n      };\r\n\r\n      const bestModel = aggregate.selectBestModel(criteria);\r\n\r\n      expect(bestModel).toBeDefined();\r\n      expect(bestModel!.name).toBe('High Performance');\r\n    });\r\n\r\n    it('should return undefined when no models match criteria', () => {\r\n      const criteria: ModelSelectionCriteria = {\r\n        taskType: 'non-existent-task',\r\n      };\r\n\r\n      const bestModel = aggregate.selectBestModel(criteria);\r\n\r\n      expect(bestModel).toBeUndefined();\r\n    });\r\n\r\n    it('should return single model when only one matches', () => {\r\n      // Deactivate all but one model\r\n      const models = aggregate.getAllModels();\r\n      models[1].deactivate();\r\n      models[2].deactivate();\r\n\r\n      const criteria: ModelSelectionCriteria = {\r\n        taskType: 'classification',\r\n        requiresAvailability: true,\r\n      };\r\n\r\n      const bestModel = aggregate.selectBestModel(criteria);\r\n\r\n      expect(bestModel).toBeDefined();\r\n      expect(bestModel!.name).toBe('High Performance');\r\n    });\r\n  });\r\n\r\n  describe('Load Balancing', () => {\r\n    beforeEach(() => {\r\n      const models = [\r\n        AIModelFactory.createForTesting({ name: 'Model 1', supportedTaskTypes: ['classification'] }),\r\n        AIModelFactory.createForTesting({ name: 'Model 2', supportedTaskTypes: ['classification'] }),\r\n        AIModelFactory.createForTesting({ name: 'Model 3', supportedTaskTypes: ['classification'] }),\r\n      ];\r\n\r\n      models.forEach(model => {\r\n        aggregate.addModel(model);\r\n        model.activate();\r\n      });\r\n\r\n      // Set different loads\r\n      models[0].updateLoad(5);\r\n      models[1].updateLoad(2);\r\n      models[2].updateLoad(8);\r\n    });\r\n\r\n    it('should select least loaded model', () => {\r\n      const criteria: ModelSelectionCriteria = {\r\n        taskType: 'classification',\r\n      };\r\n\r\n      const strategy: LoadBalancingStrategy = {\r\n        type: 'least_loaded',\r\n      };\r\n\r\n      const selectedModel = aggregate.selectModelForLoadBalancing(criteria, strategy);\r\n\r\n      expect(selectedModel).toBeDefined();\r\n      expect(selectedModel!.name).toBe('Model 2'); // Has load of 2\r\n    });\r\n\r\n    it('should select model using round robin', () => {\r\n      const criteria: ModelSelectionCriteria = {\r\n        taskType: 'classification',\r\n      };\r\n\r\n      const strategy: LoadBalancingStrategy = {\r\n        type: 'round_robin',\r\n      };\r\n\r\n      const selectedModel = aggregate.selectModelForLoadBalancing(criteria, strategy);\r\n\r\n      expect(selectedModel).toBeDefined();\r\n    });\r\n\r\n    it('should select model using performance-based strategy', () => {\r\n      // Update performance metrics\r\n      const models = aggregate.getAllModels();\r\n      models[0].updatePerformanceMetrics({ accuracy: 0.95 });\r\n      models[1].updatePerformanceMetrics({ accuracy: 0.85 });\r\n      models[2].updatePerformanceMetrics({ accuracy: 0.75 });\r\n\r\n      const criteria: ModelSelectionCriteria = {\r\n        taskType: 'classification',\r\n      };\r\n\r\n      const strategy: LoadBalancingStrategy = {\r\n        type: 'performance_based',\r\n      };\r\n\r\n      const selectedModel = aggregate.selectModelForLoadBalancing(criteria, strategy);\r\n\r\n      expect(selectedModel).toBeDefined();\r\n      expect(selectedModel!.name).toBe('Model 1'); // Highest accuracy\r\n    });\r\n\r\n    it('should return undefined when no models match criteria', () => {\r\n      const criteria: ModelSelectionCriteria = {\r\n        taskType: 'non-existent-task',\r\n      };\r\n\r\n      const strategy: LoadBalancingStrategy = {\r\n        type: 'least_loaded',\r\n      };\r\n\r\n      const selectedModel = aggregate.selectModelForLoadBalancing(criteria, strategy);\r\n\r\n      expect(selectedModel).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('Statistics', () => {\r\n    beforeEach(() => {\r\n      const models = [\r\n        AIModelFactory.createForTesting({\r\n          name: 'OpenAI Model',\r\n          provider: AIProvider.OPENAI,\r\n          modelType: ModelType.LANGUAGE_MODEL,\r\n        }),\r\n        AIModelFactory.createForTesting({\r\n          name: 'Bedrock Model',\r\n          provider: AIProvider.BEDROCK,\r\n          modelType: ModelType.LANGUAGE_MODEL,\r\n        }),\r\n        AIModelFactory.createForTesting({\r\n          name: 'TensorFlow Model',\r\n          provider: AIProvider.TENSORFLOW,\r\n          modelType: ModelType.CLASSIFICATION,\r\n        }),\r\n      ];\r\n\r\n      models.forEach(model => aggregate.addModel(model));\r\n      models[0].activate();\r\n      models[1].activate();\r\n      // models[2] remains inactive\r\n\r\n      models[0].updateLoad(5);\r\n      models[1].updateLoad(3);\r\n    });\r\n\r\n    it('should calculate aggregate statistics', () => {\r\n      const stats = aggregate.getStatistics();\r\n\r\n      expect(stats.totalModels).toBe(3);\r\n      expect(stats.activeModels).toBe(2);\r\n      expect(stats.inactiveModels).toBe(1);\r\n      expect(stats.archivedModels).toBe(0);\r\n      expect(stats.totalLoad).toBe(8);\r\n      expect(stats.averageUtilization).toBeGreaterThan(0);\r\n      expect(stats.providerDistribution[AIProvider.OPENAI]).toBe(1);\r\n      expect(stats.providerDistribution[AIProvider.BEDROCK]).toBe(1);\r\n      expect(stats.providerDistribution[AIProvider.TENSORFLOW]).toBe(1);\r\n      expect(stats.typeDistribution[ModelType.LANGUAGE_MODEL]).toBe(2);\r\n      expect(stats.typeDistribution[ModelType.CLASSIFICATION]).toBe(1);\r\n    });\r\n  });\r\n\r\n  describe('Bulk Operations', () => {\r\n    beforeEach(() => {\r\n      const models = [\r\n        AIModelFactory.createForTesting({\r\n          name: 'OpenAI Model',\r\n          provider: AIProvider.OPENAI,\r\n          supportedTaskTypes: ['text-generation'],\r\n        }),\r\n        AIModelFactory.createForTesting({\r\n          name: 'Bedrock Model',\r\n          provider: AIProvider.BEDROCK,\r\n          supportedTaskTypes: ['text-generation'],\r\n        }),\r\n        AIModelFactory.createForTesting({\r\n          name: 'TensorFlow Model',\r\n          provider: AIProvider.TENSORFLOW,\r\n          supportedTaskTypes: ['classification'],\r\n        }),\r\n      ];\r\n\r\n      models.forEach(model => aggregate.addModel(model));\r\n    });\r\n\r\n    it('should activate models matching criteria', () => {\r\n      const criteria: ModelSelectionCriteria = {\r\n        taskType: 'text-generation',\r\n      };\r\n\r\n      const activatedModels = aggregate.activateModels(criteria);\r\n\r\n      expect(activatedModels).toHaveLength(2);\r\n      expect(activatedModels.every(model => model.status === ModelStatus.ACTIVE)).toBe(true);\r\n    });\r\n\r\n    it('should deactivate models matching criteria', () => {\r\n      // First activate all models\r\n      aggregate.getAllModels().forEach(model => model.activate());\r\n\r\n      const criteria: ModelSelectionCriteria = {\r\n        provider: AIProvider.OPENAI,\r\n      };\r\n\r\n      const deactivatedModels = aggregate.deactivateModels(criteria);\r\n\r\n      expect(deactivatedModels).toHaveLength(1);\r\n      expect(deactivatedModels[0].status).toBe(ModelStatus.INACTIVE);\r\n    });\r\n\r\n    it('should archive models matching criteria', () => {\r\n      const criteria: ModelSelectionCriteria = {\r\n        provider: AIProvider.TENSORFLOW,\r\n      };\r\n\r\n      const archivedModels = aggregate.archiveModels(criteria);\r\n\r\n      expect(archivedModels).toHaveLength(1);\r\n      expect(archivedModels[0].status).toBe(ModelStatus.ARCHIVED);\r\n    });\r\n  });\r\n\r\n  describe('Performance Monitoring', () => {\r\n    beforeEach(() => {\r\n      const models = [\r\n        AIModelFactory.createForTesting({ name: 'Normal Model' }),\r\n        AIModelFactory.createForTesting({ name: 'Overloaded Model' }),\r\n        AIModelFactory.createForTesting({ name: 'Low Performance Model' }),\r\n      ];\r\n\r\n      models.forEach(model => aggregate.addModel(model));\r\n\r\n      // Make one model overloaded\r\n      models[1].updateLoad(9); // 90% utilization (overloaded threshold is 80%)\r\n\r\n      // Make one model low performance\r\n      models[2].updatePerformanceMetrics({ accuracy: 0.3 });\r\n    });\r\n\r\n    it('should identify overloaded models', () => {\r\n      const overloadedModels = aggregate.getOverloadedModels();\r\n\r\n      expect(overloadedModels).toHaveLength(1);\r\n      expect(overloadedModels[0].name).toBe('Overloaded Model');\r\n    });\r\n\r\n    it('should identify low performance models', () => {\r\n      const lowPerformanceModels = aggregate.getLowPerformanceModels(0.5);\r\n\r\n      expect(lowPerformanceModels).toHaveLength(1);\r\n      expect(lowPerformanceModels[0].name).toBe('Low Performance Model');\r\n    });\r\n\r\n    it('should rebalance load', () => {\r\n      // Activate models first\r\n      aggregate.getAllModels().forEach(model => model.activate());\r\n\r\n      const overloadedBefore = aggregate.getOverloadedModels();\r\n      expect(overloadedBefore).toHaveLength(1);\r\n\r\n      aggregate.rebalanceLoad();\r\n\r\n      const overloadedAfter = aggregate.getOverloadedModels();\r\n      expect(overloadedAfter).toHaveLength(0);\r\n    });\r\n\r\n    it('should not deactivate last active model during rebalancing', () => {\r\n      // Only activate the overloaded model\r\n      const models = aggregate.getAllModels();\r\n      models[1].activate();\r\n\r\n      aggregate.rebalanceLoad();\r\n\r\n      expect(models[1].status).toBe(ModelStatus.ACTIVE); // Should remain active\r\n    });\r\n  });\r\n});"], "version": 3}