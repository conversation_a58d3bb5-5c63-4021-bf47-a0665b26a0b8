76268d024a7133ec40a51669fdee16ee
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ioc_value_object_1 = require("../ioc.value-object");
describe('IOC Value Object', () => {
    describe('creation', () => {
        it('should create valid IOC with required properties', () => {
            // Arrange & Act
            const ioc = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            // Assert
            expect(ioc.type).toBe(ioc_value_object_1.IOCType.IP_ADDRESS);
            expect(ioc.value).toBe('***********');
            expect(ioc.confidence).toBe(ioc_value_object_1.IOCConfidence.HIGH);
            expect(ioc.severity).toBe(ioc_value_object_1.IOCSeverity.MEDIUM);
            expect(ioc.firstSeen).toBeInstanceOf(Date);
        });
        it('should create IOC with optional properties', () => {
            // Arrange & Act
            const ioc = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.DOMAIN, 'malicious.example.com', ioc_value_object_1.IOCConfidence.CONFIRMED, ioc_value_object_1.IOCSeverity.CRITICAL, {
                description: 'Known malicious domain',
                source: 'threat-intel-feed',
                tags: ['malware', 'c2'],
                expiresAt: new Date(Date.now() + 86400000), // 24 hours
            });
            // Assert
            expect(ioc.description).toBe('Known malicious domain');
            expect(ioc.source).toBe('threat-intel-feed');
            expect(ioc.tags).toEqual(['malware', 'c2']);
            expect(ioc.expiresAt).toBeInstanceOf(Date);
        });
        it('should create IP address IOC using factory method', () => {
            // Arrange & Act
            const ioc = ioc_value_object_1.IOC.createIPAddress('********', ioc_value_object_1.IOCConfidence.MEDIUM, ioc_value_object_1.IOCSeverity.HIGH);
            // Assert
            expect(ioc.type).toBe(ioc_value_object_1.IOCType.IP_ADDRESS);
            expect(ioc.value).toBe('********');
        });
        it('should create domain IOC using factory method', () => {
            // Arrange & Act
            const ioc = ioc_value_object_1.IOC.createDomain('suspicious.com', ioc_value_object_1.IOCConfidence.LOW, ioc_value_object_1.IOCSeverity.MEDIUM);
            // Assert
            expect(ioc.type).toBe(ioc_value_object_1.IOCType.DOMAIN);
            expect(ioc.value).toBe('suspicious.com');
        });
        it('should create file hash IOC using factory method', () => {
            // Arrange & Act
            const ioc = ioc_value_object_1.IOC.createFileHash('a1b2c3d4e5f6789012345678901234567890abcd', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.CRITICAL);
            // Assert
            expect(ioc.type).toBe(ioc_value_object_1.IOCType.FILE_HASH);
            expect(ioc.value).toBe('a1b2c3d4e5f6789012345678901234567890abcd');
        });
    });
    describe('validation', () => {
        it('should validate IP address format', () => {
            // Arrange & Act & Assert
            expect(() => ioc_value_object_1.IOC.createIPAddress('invalid-ip', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM))
                .toThrow('Invalid IP address format: invalid-ip');
        });
        it('should validate IPv4 addresses', () => {
            // Arrange & Act
            const ioc = ioc_value_object_1.IOC.createIPAddress('***********00', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            // Assert
            expect(ioc.value).toBe('***********00');
        });
        it('should validate IPv6 addresses', () => {
            // Arrange & Act
            const ioc = ioc_value_object_1.IOC.createIPAddress('::1', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            // Assert
            expect(ioc.value).toBe('::1');
        });
        it('should validate domain format', () => {
            // Arrange & Act & Assert
            expect(() => ioc_value_object_1.IOC.createDomain('invalid..domain', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM))
                .toThrow('Invalid domain format: invalid..domain');
        });
        it('should validate file hash formats', () => {
            // MD5
            const md5Hash = 'a1b2c3d4e5f67890123456789012345a';
            expect(() => ioc_value_object_1.IOC.createFileHash(md5Hash, ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM))
                .not.toThrow();
            // SHA1
            const sha1Hash = 'a1b2c3d4e5f6789012345678901234567890abcd';
            expect(() => ioc_value_object_1.IOC.createFileHash(sha1Hash, ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM))
                .not.toThrow();
            // SHA256
            const sha256Hash = 'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678';
            expect(() => ioc_value_object_1.IOC.createFileHash(sha256Hash, ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM))
                .not.toThrow();
            // Invalid hash
            expect(() => ioc_value_object_1.IOC.createFileHash('invalid-hash', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM))
                .toThrow('Invalid file hash format: invalid-hash');
        });
        it('should validate URL format', () => {
            // Valid URL
            const ioc = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.URL, 'https://malicious.com/path', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            expect(ioc.value).toBe('https://malicious.com/path');
            // Invalid URL
            expect(() => ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.URL, 'not-a-url', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM))
                .toThrow('Invalid URL format: not-a-url');
        });
        it('should validate email format', () => {
            // Valid email
            const ioc = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.EMAIL, '<EMAIL>', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            expect(ioc.value).toBe('<EMAIL>');
            // Invalid email
            expect(() => ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.EMAIL, 'invalid-email', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM))
                .toThrow('Invalid email format: invalid-email');
        });
        it('should validate CVE format', () => {
            // Valid CVE
            const ioc = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.CVE, 'CVE-2023-1234', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            expect(ioc.value).toBe('CVE-2023-1234');
            // Invalid CVE
            expect(() => ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.CVE, 'invalid-cve', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM))
                .toThrow('Invalid CVE format: invalid-cve');
        });
        it('should validate temporal relationships', () => {
            const firstSeen = new Date('2023-01-01');
            const lastSeen = new Date('2022-12-31'); // Before first seen
            expect(() => ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM, {
                firstSeen,
                lastSeen,
            })).toThrow('IOC firstSeen cannot be after lastSeen');
        });
        it('should require non-empty value', () => {
            expect(() => ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM))
                .toThrow('IOC must have a non-empty value');
        });
        it('should require valid IOC type', () => {
            expect(() => new ioc_value_object_1.IOC({
                type: 'invalid',
                value: '***********',
                confidence: ioc_value_object_1.IOCConfidence.HIGH,
                severity: ioc_value_object_1.IOCSeverity.MEDIUM,
            })).toThrow('Invalid IOC type: invalid');
        });
        it('should require valid confidence level', () => {
            expect(() => new ioc_value_object_1.IOC({
                type: ioc_value_object_1.IOCType.IP_ADDRESS,
                value: '***********',
                confidence: 'invalid',
                severity: ioc_value_object_1.IOCSeverity.MEDIUM,
            })).toThrow('Invalid IOC confidence: invalid');
        });
        it('should require valid severity level', () => {
            expect(() => new ioc_value_object_1.IOC({
                type: ioc_value_object_1.IOCType.IP_ADDRESS,
                value: '***********',
                confidence: ioc_value_object_1.IOCConfidence.HIGH,
                severity: 'invalid',
            })).toThrow('Invalid IOC severity: invalid');
        });
    });
    describe('business logic', () => {
        it('should check if IOC is expired', () => {
            const expiredIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM, {
                expiresAt: new Date(Date.now() - 1000), // 1 second ago
            });
            const activeIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM, {
                expiresAt: new Date(Date.now() + 86400000), // 24 hours from now
            });
            expect(expiredIOC.isExpired()).toBe(true);
            expect(expiredIOC.isActive()).toBe(false);
            expect(activeIOC.isExpired()).toBe(false);
            expect(activeIOC.isActive()).toBe(true);
        });
        it('should check confidence levels', () => {
            const highConfidenceIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            const lowConfidenceIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.LOW, ioc_value_object_1.IOCSeverity.MEDIUM);
            const confirmedIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.CONFIRMED, ioc_value_object_1.IOCSeverity.MEDIUM);
            expect(highConfidenceIOC.isHighConfidence()).toBe(true);
            expect(lowConfidenceIOC.isHighConfidence()).toBe(false);
            expect(confirmedIOC.isHighConfidence()).toBe(true);
        });
        it('should check severity levels', () => {
            const highSeverityIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.HIGH);
            const criticalIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.CRITICAL);
            const lowSeverityIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.LOW);
            expect(highSeverityIOC.isHighSeverity()).toBe(true);
            expect(criticalIOC.isHighSeverity()).toBe(true);
            expect(criticalIOC.isCritical()).toBe(true);
            expect(lowSeverityIOC.isHighSeverity()).toBe(false);
            expect(lowSeverityIOC.isCritical()).toBe(false);
        });
        it('should check for tags', () => {
            const ioc = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM, {
                tags: ['malware', 'botnet', 'c2'],
            });
            expect(ioc.hasTag('malware')).toBe(true);
            expect(ioc.hasTag('phishing')).toBe(false);
        });
        it('should calculate age correctly', () => {
            const pastDate = new Date(Date.now() - (5 * 24 * 60 * 60 * 1000)); // 5 days ago
            const ioc = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM, {
                firstSeen: pastDate,
            });
            expect(ioc.getAge()).toBe(5);
        });
        it('should calculate days until expiration', () => {
            const futureDate = new Date(Date.now() + (3 * 24 * 60 * 60 * 1000)); // 3 days from now
            const ioc = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM, {
                expiresAt: futureDate,
            });
            expect(ioc.getDaysUntilExpiration()).toBe(3);
        });
        it('should calculate threat score', () => {
            const criticalIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.CONFIRMED, ioc_value_object_1.IOCSeverity.CRITICAL);
            const lowIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.LOW, ioc_value_object_1.IOCSeverity.INFO);
            expect(criticalIOC.getThreatScore()).toBeGreaterThan(lowIOC.getThreatScore());
            expect(criticalIOC.getThreatScore()).toBeGreaterThan(50);
            expect(lowIOC.getThreatScore()).toBeLessThan(20);
        });
        it('should identify hash types for file hashes', () => {
            const md5IOC = ioc_value_object_1.IOC.createFileHash('a1b2c3d4e5f67890123456789012345a', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            const sha1IOC = ioc_value_object_1.IOC.createFileHash('a1b2c3d4e5f6789012345678901234567890abcd', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            const sha256IOC = ioc_value_object_1.IOC.createFileHash('a1b2c3d4e5f67890123456789012345678901234567890123456789012345678', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            const ipIOC = ioc_value_object_1.IOC.createIPAddress('***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            expect(md5IOC.getHashType()).toBe('md5');
            expect(sha1IOC.getHashType()).toBe('sha1');
            expect(sha256IOC.getHashType()).toBe('sha256');
            expect(ipIOC.getHashType()).toBeNull();
        });
    });
    describe('immutability and updates', () => {
        it('should create new IOC when updating last seen', () => {
            const originalIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            const newDate = new Date();
            const updatedIOC = originalIOC.updateLastSeen(newDate);
            expect(updatedIOC).not.toBe(originalIOC);
            expect(updatedIOC.lastSeen).toEqual(newDate);
            expect(originalIOC.lastSeen).toBeUndefined();
        });
        it('should create new IOC when adding tags', () => {
            const originalIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM, {
                tags: ['malware'],
            });
            const updatedIOC = originalIOC.withTags(['botnet', 'c2']);
            expect(updatedIOC).not.toBe(originalIOC);
            expect(updatedIOC.tags).toEqual(['malware', 'botnet', 'c2']);
            expect(originalIOC.tags).toEqual(['malware']);
        });
        it('should create new IOC when adding context', () => {
            const originalIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            const context = { campaign: 'APT-123', actor: 'threat-group-x' };
            const updatedIOC = originalIOC.withContext(context);
            expect(updatedIOC).not.toBe(originalIOC);
            expect(updatedIOC.context).toEqual(context);
            expect(originalIOC.context).toEqual({});
        });
        it('should create new IOC when setting expiration', () => {
            const originalIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            const expirationDate = new Date(Date.now() + 86400000);
            const updatedIOC = originalIOC.withExpiration(expirationDate);
            expect(updatedIOC).not.toBe(originalIOC);
            expect(updatedIOC.expiresAt).toEqual(expirationDate);
            expect(originalIOC.expiresAt).toBeUndefined();
        });
    });
    describe('equality and comparison', () => {
        it('should compare IOCs for equality based on type and value', () => {
            const ioc1 = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            const ioc2 = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.LOW, ioc_value_object_1.IOCSeverity.HIGH);
            const ioc3 = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            const ioc4 = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.DOMAIN, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            expect(ioc1.equals(ioc2)).toBe(true); // Same type and value
            expect(ioc1.equals(ioc3)).toBe(false); // Different value
            expect(ioc1.equals(ioc4)).toBe(false); // Different type
            expect(ioc1.equals(undefined)).toBe(false);
        });
        it('should have consistent string representation', () => {
            const ioc = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM);
            expect(ioc.toString()).toBe('ip_address:***********');
        });
    });
    describe('serialization', () => {
        it('should serialize to JSON correctly', () => {
            const ioc = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.MEDIUM, {
                description: 'Test IOC',
                tags: ['test'],
            });
            const json = ioc.toJSON();
            expect(json.type).toBe(ioc_value_object_1.IOCType.IP_ADDRESS);
            expect(json.value).toBe('***********');
            expect(json.confidence).toBe(ioc_value_object_1.IOCConfidence.HIGH);
            expect(json.severity).toBe(ioc_value_object_1.IOCSeverity.MEDIUM);
            expect(json.description).toBe('Test IOC');
            expect(json.tags).toEqual(['test']);
            expect(json.summary).toBeDefined();
            expect(json.hashType).toBeNull();
        });
        it('should deserialize from JSON correctly', () => {
            const originalIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.DOMAIN, 'malicious.com', ioc_value_object_1.IOCConfidence.CONFIRMED, ioc_value_object_1.IOCSeverity.CRITICAL);
            const json = originalIOC.toJSON();
            const deserializedIOC = ioc_value_object_1.IOC.fromJSON(json);
            expect(deserializedIOC.equals(originalIOC)).toBe(true);
            expect(deserializedIOC.type).toBe(originalIOC.type);
            expect(deserializedIOC.value).toBe(originalIOC.value);
            expect(deserializedIOC.confidence).toBe(originalIOC.confidence);
            expect(deserializedIOC.severity).toBe(originalIOC.severity);
        });
    });
    describe('validation utility', () => {
        it('should validate IOC format without creating instance', () => {
            expect(ioc_value_object_1.IOC.isValid(ioc_value_object_1.IOCType.IP_ADDRESS, '***********')).toBe(true);
            expect(ioc_value_object_1.IOC.isValid(ioc_value_object_1.IOCType.IP_ADDRESS, 'invalid-ip')).toBe(false);
            expect(ioc_value_object_1.IOC.isValid(ioc_value_object_1.IOCType.DOMAIN, 'example.com')).toBe(true);
            expect(ioc_value_object_1.IOC.isValid(ioc_value_object_1.IOCType.DOMAIN, 'invalid..domain')).toBe(false);
        });
    });
    describe('summary and analysis', () => {
        it('should provide comprehensive summary', () => {
            const ioc = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '***********', ioc_value_object_1.IOCConfidence.HIGH, ioc_value_object_1.IOCSeverity.CRITICAL, {
                tags: ['malware', 'botnet'],
            });
            const summary = ioc.getSummary();
            expect(summary.type).toBe(ioc_value_object_1.IOCType.IP_ADDRESS);
            expect(summary.value).toBe('***********');
            expect(summary.confidence).toBe(ioc_value_object_1.IOCConfidence.HIGH);
            expect(summary.severity).toBe(ioc_value_object_1.IOCSeverity.CRITICAL);
            expect(summary.isHighConfidence).toBe(true);
            expect(summary.isHighSeverity).toBe(true);
            expect(summary.tagCount).toBe(2);
            expect(typeof summary.threatScore).toBe('number');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************