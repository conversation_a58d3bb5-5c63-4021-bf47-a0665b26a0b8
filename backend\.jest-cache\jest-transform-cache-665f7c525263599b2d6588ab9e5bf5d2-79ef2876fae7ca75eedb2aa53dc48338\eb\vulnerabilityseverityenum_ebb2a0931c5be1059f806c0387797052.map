{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\vulnerability-severity.enum.ts", "mappings": ";;;AAAA;;;;;;;;;GASG;AACH,IAAY,qBAyDX;AAzDD,WAAY,qBAAqB;IAC/B;;;;;;OAMG;IACH,sCAAa,CAAA;IAEb;;;;;;;OAOG;IACH,oCAAW,CAAA;IAEX;;;;;;;OAOG;IACH,0CAAiB,CAAA;IAEjB;;;;;;;OAOG;IACH,sCAAa,CAAA;IAEb;;;;;;;OAOG;IACH,8CAAqB,CAAA;IAErB;;;;;OAKG;IACH,4CAAmB,CAAA;AACrB,CAAC,EAzDW,qBAAqB,qCAArB,qBAAqB,QAyDhC;AAED;;GAEG;AACH,MAAa,0BAA0B;IACrC;;OAEG;IACH,MAAM,CAAC,gBAAgB;QACrB,OAAO,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iCAAiC;QACtC,OAAO,CAAC,qBAAqB,CAAC,QAAQ,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gCAAgC;QACrC,OAAO,CAAC,qBAAqB,CAAC,QAAQ,EAAE,qBAAqB,CAAC,IAAI,EAAE,qBAAqB,CAAC,MAAM,CAAC,CAAC;IACpG,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,+BAA+B;QACpC,OAAO,CAAC,qBAAqB,CAAC,GAAG,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,4BAA4B,CAAC,QAA+B;QACjE,OAAO,0BAA0B,CAAC,iCAAiC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC3F,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,QAA+B;QAC/C,OAAO,QAAQ,KAAK,qBAAqB,CAAC,QAAQ,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,QAA+B;QACrD,OAAO,QAAQ,KAAK,qBAAqB,CAAC,IAAI,IAAI,QAAQ,KAAK,qBAAqB,CAAC,QAAQ,CAAC;IAChG,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,2BAA2B,CAAC,QAA+B;QAChE,OAAO,0BAA0B,CAAC,gCAAgC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,QAA+B;QACpD,MAAM,MAAM,GAA0C;YACpD,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnC,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SACpC,CAAC;QACF,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,QAA+B;QACjD,MAAM,MAAM,GAAgE;YAC1E,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;YACpD,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;YACnD,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;YACtD,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;YACpD,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE;YACzD,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE;SACzD,CAAC;QACF,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,SAAiB;QACpC,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,EAAE,EAAE,CAAC;YACpC,OAAO,qBAAqB,CAAC,OAAO,CAAC;QACvC,CAAC;QAED,IAAI,SAAS,KAAK,GAAG;YAAE,OAAO,qBAAqB,CAAC,IAAI,CAAC;QACzD,IAAI,SAAS,IAAI,GAAG;YAAE,OAAO,qBAAqB,CAAC,QAAQ,CAAC;QAC5D,IAAI,SAAS,IAAI,GAAG;YAAE,OAAO,qBAAqB,CAAC,IAAI,CAAC;QACxD,IAAI,SAAS,IAAI,GAAG;YAAE,OAAO,qBAAqB,CAAC,MAAM,CAAC;QAC1D,IAAI,SAAS,IAAI,GAAG;YAAE,OAAO,qBAAqB,CAAC,GAAG,CAAC;QACvD,OAAO,qBAAqB,CAAC,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,SAAgC,EAAE,SAAgC;QAC/E,OAAO,0BAA0B,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,0BAA0B,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IACvH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,SAAgC,EAAE,SAAgC;QACjF,OAAO,0BAA0B,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/F,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,SAAgC,EAAE,SAAgC;QAChF,OAAO,0BAA0B,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/F,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,QAA+B;QACtD,MAAM,OAAO,GAA0C;YACrD,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAO,QAAQ;YAClD,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAW,SAAS;YACnD,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,EAAQ,UAAU;YACpD,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,EAAE,EAAW,UAAU;YACpD,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,GAAG,EAAS,yBAAyB;YACnE,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,EAAE,EAAO,oBAAoB;SAC/D,CAAC;QACF,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,QAA+B;QACzD,MAAM,QAAQ,GAA0C;YACtD,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAO,UAAU;YACpD,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAW,UAAU;YACpD,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,EAAQ,WAAW;YACrD,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,EAAE,EAAW,SAAS;YACnD,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,GAAG,EAAS,SAAS;YACnD,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,EAAE,EAAO,WAAW;SACtD,CAAC;QACF,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,QAA+B;QACtD,MAAM,eAAe,GAA0C;YAC7D,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAO,UAAU;YACpD,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE,EAAU,WAAW;YACrD,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,EAAQ,SAAS;YACnD,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,GAAG,EAAU,SAAS;YACnD,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,GAAG,EAAS,UAAU;YACpD,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,EAAE,EAAO,WAAW;SACtD,CAAC;QACF,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,QAA+B;QACrD,MAAM,SAAS,GAA0F;YACvG,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,MAAM;YACxC,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,UAAU;YACxC,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,SAAS;YACzC,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,WAAW;YACxC,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,MAAM;YACpC,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,SAAS;SAC3C,CAAC;QACF,OAAO,SAAS,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAAC,QAA+B;QAC5D,MAAM,QAAQ,GAA4C;YACxD,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC;YAC/E,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC;YAC3D,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;YACpD,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC;YACxC,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE;YAChC,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC;SAC3C,CAAC;QACF,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,QAA+B;QAIzD,MAAM,WAAW,GAA0G;YACzH,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAK,aAAa;YACzF,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAa,gBAAgB;YAC5F,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAQ,QAAQ;YACpF,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAa,SAAS;YACrF,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAY,UAAU;YACtF,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAO,QAAQ;SACrF,CAAC;QACF,OAAO,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,QAA+B;QACjD,MAAM,MAAM,GAA0C;YACpD,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAE,MAAM;YACnD,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,SAAS,EAAM,SAAS;YACtD,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,SAAS,EAAI,QAAQ;YACrD,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,SAAS,EAAO,QAAQ;YACrD,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,SAAS,EAAM,UAAU;YACvD,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,SAAS,EAAG,OAAO;SACrD,CAAC;QACF,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,QAA+B;QAChD,MAAM,KAAK,GAA0C;YACnD,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,sBAAsB;YACxD,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,oBAAoB;YAClD,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,oBAAoB;YACpD,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,cAAc;YAC3C,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,cAAc;YAC5C,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,sBAAsB;SACxD,CAAC;QACF,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,QAA+B;QACnD,MAAM,YAAY,GAA0C;YAC1D,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,wEAAwE;YAC1G,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,0EAA0E;YACxG,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,4EAA4E;YAC5G,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,yEAAyE;YACtG,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,gDAAgD;YAC9E,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,wEAAwE;SAC1G,CAAC;QACF,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,KAAa;QAC7B,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAC9C,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;QACxD,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,UAAU,CAAC,IAAI,IAAI,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,QAAgB;QAC7B,OAAO,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,QAAQ,CAAC,QAAiC,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,QAA+B;QAC1D,MAAM,UAAU,GAA0C;YACxD,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE;YACpC,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,CAAC;SACnC,CAAC;QACF,OAAO,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,QAA+B;QAKtD,MAAM,OAAO,GAIR;YACH,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE,eAAe,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE;YACtG,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE,eAAe,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE;YACtG,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE;YACpG,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,EAAE,eAAe,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE;YAChG,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE,eAAe,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE;YAClG,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,EAAE,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE;SAC5G,CAAC;QACF,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAAC,QAA+B;QAK7D,MAAM,UAAU,GAIX;YACH,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE;YACpG,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE;YACpF,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE;YACzF,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE;YAC/E,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE;YAChF,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE;SAC3F,CAAC;QACF,OAAO,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,yBAAyB,CAAC,UAAmC;QAClE,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,qBAAqB,CAAC,OAAO,CAAC;QACvC,CAAC;QAED,kEAAkE;QAClE,IAAI,UAAU,CAAC,QAAQ,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxD,OAAO,qBAAqB,CAAC,QAAQ,CAAC;QACxC,CAAC;QAED,iDAAiD;QACjD,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACpF,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,qBAAqB,CAAC,OAAO,CAAC;QACvC,CAAC;QAED,MAAM,aAAa,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,0BAA0B,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9F,MAAM,OAAO,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC;QAExF,2BAA2B;QAC3B,IAAI,OAAO,IAAI,GAAG;YAAE,OAAO,qBAAqB,CAAC,QAAQ,CAAC;QAC1D,IAAI,OAAO,IAAI,GAAG;YAAE,OAAO,qBAAqB,CAAC,IAAI,CAAC;QACtD,IAAI,OAAO,IAAI,GAAG;YAAE,OAAO,qBAAqB,CAAC,MAAM,CAAC;QACxD,IAAI,OAAO,IAAI,GAAG;YAAE,OAAO,qBAAqB,CAAC,GAAG,CAAC;QACrD,OAAO,qBAAqB,CAAC,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAC3B,gBAAuC,EACvC,SAAiB;QAEjB,iEAAiE;QACjE,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,wBAAwB;QACpF,MAAM,aAAa,GAAG,0BAA0B,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;QACnF,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC;QAE7E,MAAM,WAAW,GAA0C;YACzD,CAAC,EAAE,qBAAqB,CAAC,IAAI;YAC7B,CAAC,EAAE,qBAAqB,CAAC,GAAG;YAC5B,CAAC,EAAE,qBAAqB,CAAC,MAAM;YAC/B,CAAC,EAAE,qBAAqB,CAAC,IAAI;YAC7B,CAAC,EAAE,qBAAqB,CAAC,QAAQ;SAClC,CAAC;QAEF,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,gBAAgB,CAAC;IACxD,CAAC;CACF;AAvYD,gEAuYC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\vulnerability-severity.enum.ts"], "sourcesContent": ["/**\r\n * Vulnerability Severity Enum\r\n * \r\n * Represents the severity level of identified vulnerabilities based on CVSS scoring,\r\n * potential impact, exploitability, and business risk. Used for vulnerability\r\n * prioritization, patch management, and risk assessment.\r\n * \r\n * Severity Levels (ascending order):\r\n * NONE -> LOW -> MEDIUM -> HIGH -> CRITICAL\r\n */\r\nexport enum VulnerabilitySeverity {\r\n  /**\r\n   * No vulnerability or informational only\r\n   * - CVSS Score: 0.0\r\n   * - No security impact\r\n   * - Informational findings only\r\n   * - No remediation required\r\n   */\r\n  NONE = 'none',\r\n\r\n  /**\r\n   * Low severity vulnerabilities\r\n   * - CVSS Score: 0.1 - 3.9\r\n   * - Minimal security impact\r\n   * - Difficult to exploit\r\n   * - Low business risk\r\n   * - Routine patching schedule\r\n   */\r\n  LOW = 'low',\r\n\r\n  /**\r\n   * Medium severity vulnerabilities\r\n   * - CVSS Score: 4.0 - 6.9\r\n   * - Moderate security impact\r\n   * - Moderate exploitability\r\n   * - Medium business risk\r\n   * - Planned remediation required\r\n   */\r\n  MEDIUM = 'medium',\r\n\r\n  /**\r\n   * High severity vulnerabilities\r\n   * - CVSS Score: 7.0 - 8.9\r\n   * - Significant security impact\r\n   * - High exploitability\r\n   * - High business risk\r\n   * - Priority remediation required\r\n   */\r\n  HIGH = 'high',\r\n\r\n  /**\r\n   * Critical severity vulnerabilities\r\n   * - CVSS Score: 9.0 - 10.0\r\n   * - Severe security impact\r\n   * - Easy to exploit remotely\r\n   * - Critical business risk\r\n   * - Emergency remediation required\r\n   */\r\n  CRITICAL = 'critical',\r\n\r\n  /**\r\n   * Unknown vulnerability severity\r\n   * - Insufficient information for assessment\r\n   * - Requires further analysis\r\n   * - Default state for new vulnerabilities\r\n   */\r\n  UNKNOWN = 'unknown',\r\n}\r\n\r\n/**\r\n * Vulnerability Severity Utilities\r\n */\r\nexport class VulnerabilitySeverityUtils {\r\n  /**\r\n   * Get all vulnerability severity levels\r\n   */\r\n  static getAllSeverities(): VulnerabilitySeverity[] {\r\n    return Object.values(VulnerabilitySeverity);\r\n  }\r\n\r\n  /**\r\n   * Get severity levels requiring immediate remediation\r\n   */\r\n  static getImmediateRemediationSeverities(): VulnerabilitySeverity[] {\r\n    return [VulnerabilitySeverity.CRITICAL, VulnerabilitySeverity.HIGH];\r\n  }\r\n\r\n  /**\r\n   * Get severity levels requiring priority remediation\r\n   */\r\n  static getPriorityRemediationSeverities(): VulnerabilitySeverity[] {\r\n    return [VulnerabilitySeverity.CRITICAL, VulnerabilitySeverity.HIGH, VulnerabilitySeverity.MEDIUM];\r\n  }\r\n\r\n  /**\r\n   * Get severity levels for routine remediation\r\n   */\r\n  static getRoutineRemediationSeverities(): VulnerabilitySeverity[] {\r\n    return [VulnerabilitySeverity.LOW, VulnerabilitySeverity.NONE];\r\n  }\r\n\r\n  /**\r\n   * Check if severity requires immediate remediation\r\n   */\r\n  static requiresImmediateRemediation(severity: VulnerabilitySeverity): boolean {\r\n    return VulnerabilitySeverityUtils.getImmediateRemediationSeverities().includes(severity);\r\n  }\r\n\r\n  /**\r\n   * Check if severity is critical\r\n   */\r\n  static isCritical(severity: VulnerabilitySeverity): boolean {\r\n    return severity === VulnerabilitySeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if severity is high or critical\r\n   */\r\n  static isHighOrCritical(severity: VulnerabilitySeverity): boolean {\r\n    return severity === VulnerabilitySeverity.HIGH || severity === VulnerabilitySeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if severity requires priority remediation\r\n   */\r\n  static requiresPriorityRemediation(severity: VulnerabilitySeverity): boolean {\r\n    return VulnerabilitySeverityUtils.getPriorityRemediationSeverities().includes(severity);\r\n  }\r\n\r\n  /**\r\n   * Get numeric value for severity (for comparison and scoring)\r\n   */\r\n  static getNumericValue(severity: VulnerabilitySeverity): number {\r\n    const values: Record<VulnerabilitySeverity, number> = {\r\n      [VulnerabilitySeverity.NONE]: 0,\r\n      [VulnerabilitySeverity.LOW]: 1,\r\n      [VulnerabilitySeverity.MEDIUM]: 2,\r\n      [VulnerabilitySeverity.HIGH]: 3,\r\n      [VulnerabilitySeverity.CRITICAL]: 4,\r\n      [VulnerabilitySeverity.UNKNOWN]: -1,\r\n    };\r\n    return values[severity];\r\n  }\r\n\r\n  /**\r\n   * Get CVSS score range for severity\r\n   */\r\n  static getCVSSRange(severity: VulnerabilitySeverity): { min: number; max: number } {\r\n    const ranges: Record<VulnerabilitySeverity, { min: number; max: number }> = {\r\n      [VulnerabilitySeverity.NONE]: { min: 0.0, max: 0.0 },\r\n      [VulnerabilitySeverity.LOW]: { min: 0.1, max: 3.9 },\r\n      [VulnerabilitySeverity.MEDIUM]: { min: 4.0, max: 6.9 },\r\n      [VulnerabilitySeverity.HIGH]: { min: 7.0, max: 8.9 },\r\n      [VulnerabilitySeverity.CRITICAL]: { min: 9.0, max: 10.0 },\r\n      [VulnerabilitySeverity.UNKNOWN]: { min: 0.0, max: 10.0 },\r\n    };\r\n    return ranges[severity];\r\n  }\r\n\r\n  /**\r\n   * Get severity from CVSS score\r\n   */\r\n  static fromCVSSScore(cvssScore: number): VulnerabilitySeverity {\r\n    if (cvssScore < 0 || cvssScore > 10) {\r\n      return VulnerabilitySeverity.UNKNOWN;\r\n    }\r\n\r\n    if (cvssScore === 0.0) return VulnerabilitySeverity.NONE;\r\n    if (cvssScore >= 9.0) return VulnerabilitySeverity.CRITICAL;\r\n    if (cvssScore >= 7.0) return VulnerabilitySeverity.HIGH;\r\n    if (cvssScore >= 4.0) return VulnerabilitySeverity.MEDIUM;\r\n    if (cvssScore >= 0.1) return VulnerabilitySeverity.LOW;\r\n    return VulnerabilitySeverity.NONE;\r\n  }\r\n\r\n  /**\r\n   * Compare two vulnerability severities\r\n   */\r\n  static compare(severity1: VulnerabilitySeverity, severity2: VulnerabilitySeverity): number {\r\n    return VulnerabilitySeverityUtils.getNumericValue(severity1) - VulnerabilitySeverityUtils.getNumericValue(severity2);\r\n  }\r\n\r\n  /**\r\n   * Get the higher of two severities\r\n   */\r\n  static getHigher(severity1: VulnerabilitySeverity, severity2: VulnerabilitySeverity): VulnerabilitySeverity {\r\n    return VulnerabilitySeverityUtils.compare(severity1, severity2) >= 0 ? severity1 : severity2;\r\n  }\r\n\r\n  /**\r\n   * Get the lower of two severities\r\n   */\r\n  static getLower(severity1: VulnerabilitySeverity, severity2: VulnerabilitySeverity): VulnerabilitySeverity {\r\n    return VulnerabilitySeverityUtils.compare(severity1, severity2) <= 0 ? severity1 : severity2;\r\n  }\r\n\r\n  /**\r\n   * Get remediation SLA in days based on vulnerability severity\r\n   */\r\n  static getRemediationSLA(severity: VulnerabilitySeverity): number {\r\n    const slaDays: Record<VulnerabilitySeverity, number> = {\r\n      [VulnerabilitySeverity.CRITICAL]: 1,      // 1 day\r\n      [VulnerabilitySeverity.HIGH]: 7,          // 7 days\r\n      [VulnerabilitySeverity.MEDIUM]: 30,       // 30 days\r\n      [VulnerabilitySeverity.LOW]: 90,          // 90 days\r\n      [VulnerabilitySeverity.NONE]: 365,        // 1 year (informational)\r\n      [VulnerabilitySeverity.UNKNOWN]: 30,      // 30 days (default)\r\n    };\r\n    return slaDays[severity];\r\n  }\r\n\r\n  /**\r\n   * Get assessment time SLA in hours based on vulnerability severity\r\n   */\r\n  static getAssessmentTimeSLA(severity: VulnerabilitySeverity): number {\r\n    const slaHours: Record<VulnerabilitySeverity, number> = {\r\n      [VulnerabilitySeverity.CRITICAL]: 2,      // 2 hours\r\n      [VulnerabilitySeverity.HIGH]: 8,          // 8 hours\r\n      [VulnerabilitySeverity.MEDIUM]: 24,       // 24 hours\r\n      [VulnerabilitySeverity.LOW]: 72,          // 3 days\r\n      [VulnerabilitySeverity.NONE]: 168,        // 7 days\r\n      [VulnerabilitySeverity.UNKNOWN]: 24,      // 24 hours\r\n    };\r\n    return slaHours[severity];\r\n  }\r\n\r\n  /**\r\n   * Get escalation time in hours based on vulnerability severity\r\n   */\r\n  static getEscalationTime(severity: VulnerabilitySeverity): number {\r\n    const escalationHours: Record<VulnerabilitySeverity, number> = {\r\n      [VulnerabilitySeverity.CRITICAL]: 4,      // 4 hours\r\n      [VulnerabilitySeverity.HIGH]: 24,         // 24 hours\r\n      [VulnerabilitySeverity.MEDIUM]: 72,       // 3 days\r\n      [VulnerabilitySeverity.LOW]: 168,         // 7 days\r\n      [VulnerabilitySeverity.NONE]: 720,        // 30 days\r\n      [VulnerabilitySeverity.UNKNOWN]: 48,      // 48 hours\r\n    };\r\n    return escalationHours[severity];\r\n  }\r\n\r\n  /**\r\n   * Get required approval level for remediation\r\n   */\r\n  static getApprovalLevel(severity: VulnerabilitySeverity): 'none' | 'team_lead' | 'manager' | 'director' | 'ciso' {\r\n    const approvals: Record<VulnerabilitySeverity, 'none' | 'team_lead' | 'manager' | 'director' | 'ciso'> = {\r\n      [VulnerabilitySeverity.CRITICAL]: 'ciso',\r\n      [VulnerabilitySeverity.HIGH]: 'director',\r\n      [VulnerabilitySeverity.MEDIUM]: 'manager',\r\n      [VulnerabilitySeverity.LOW]: 'team_lead',\r\n      [VulnerabilitySeverity.NONE]: 'none',\r\n      [VulnerabilitySeverity.UNKNOWN]: 'manager',\r\n    };\r\n    return approvals[severity];\r\n  }\r\n\r\n  /**\r\n   * Get notification channels based on vulnerability severity\r\n   */\r\n  static getNotificationChannels(severity: VulnerabilitySeverity): string[] {\r\n    const channels: Record<VulnerabilitySeverity, string[]> = {\r\n      [VulnerabilitySeverity.CRITICAL]: ['email', 'sms', 'slack', 'webhook', 'pager'],\r\n      [VulnerabilitySeverity.HIGH]: ['email', 'slack', 'webhook'],\r\n      [VulnerabilitySeverity.MEDIUM]: ['email', 'webhook'],\r\n      [VulnerabilitySeverity.LOW]: ['webhook'],\r\n      [VulnerabilitySeverity.NONE]: [],\r\n      [VulnerabilitySeverity.UNKNOWN]: ['email'],\r\n    };\r\n    return channels[severity];\r\n  }\r\n\r\n  /**\r\n   * Get scanning frequency based on vulnerability severity\r\n   */\r\n  static getScanningFrequency(severity: VulnerabilitySeverity): {\r\n    interval: number; // in hours\r\n    priority: 'low' | 'medium' | 'high' | 'critical';\r\n  } {\r\n    const frequencies: Record<VulnerabilitySeverity, { interval: number; priority: 'low' | 'medium' | 'high' | 'critical' }> = {\r\n      [VulnerabilitySeverity.CRITICAL]: { interval: 1, priority: 'critical' },    // Every hour\r\n      [VulnerabilitySeverity.HIGH]: { interval: 4, priority: 'high' },            // Every 4 hours\r\n      [VulnerabilitySeverity.MEDIUM]: { interval: 24, priority: 'medium' },       // Daily\r\n      [VulnerabilitySeverity.LOW]: { interval: 168, priority: 'low' },            // Weekly\r\n      [VulnerabilitySeverity.NONE]: { interval: 720, priority: 'low' },           // Monthly\r\n      [VulnerabilitySeverity.UNKNOWN]: { interval: 24, priority: 'medium' },      // Daily\r\n    };\r\n    return frequencies[severity];\r\n  }\r\n\r\n  /**\r\n   * Get color code for UI display\r\n   */\r\n  static getColorCode(severity: VulnerabilitySeverity): string {\r\n    const colors: Record<VulnerabilitySeverity, string> = {\r\n      [VulnerabilitySeverity.CRITICAL]: '#DC2626', // Red\r\n      [VulnerabilitySeverity.HIGH]: '#EA580C',     // Orange\r\n      [VulnerabilitySeverity.MEDIUM]: '#D97706',   // Amber\r\n      [VulnerabilitySeverity.LOW]: '#16A34A',      // Green\r\n      [VulnerabilitySeverity.NONE]: '#10B981',     // Emerald\r\n      [VulnerabilitySeverity.UNKNOWN]: '#6B7280',  // Gray\r\n    };\r\n    return colors[severity];\r\n  }\r\n\r\n  /**\r\n   * Get icon name for UI display\r\n   */\r\n  static getIconName(severity: VulnerabilitySeverity): string {\r\n    const icons: Record<VulnerabilitySeverity, string> = {\r\n      [VulnerabilitySeverity.CRITICAL]: 'exclamation-triangle',\r\n      [VulnerabilitySeverity.HIGH]: 'exclamation-circle',\r\n      [VulnerabilitySeverity.MEDIUM]: 'information-circle',\r\n      [VulnerabilitySeverity.LOW]: 'check-circle',\r\n      [VulnerabilitySeverity.NONE]: 'check-circle',\r\n      [VulnerabilitySeverity.UNKNOWN]: 'question-mark-circle',\r\n    };\r\n    return icons[severity];\r\n  }\r\n\r\n  /**\r\n   * Get human-readable description\r\n   */\r\n  static getDescription(severity: VulnerabilitySeverity): string {\r\n    const descriptions: Record<VulnerabilitySeverity, string> = {\r\n      [VulnerabilitySeverity.CRITICAL]: 'Critical vulnerability requiring emergency remediation within 24 hours',\r\n      [VulnerabilitySeverity.HIGH]: 'High-severity vulnerability requiring priority remediation within 7 days',\r\n      [VulnerabilitySeverity.MEDIUM]: 'Medium-severity vulnerability requiring planned remediation within 30 days',\r\n      [VulnerabilitySeverity.LOW]: 'Low-severity vulnerability requiring routine remediation within 90 days',\r\n      [VulnerabilitySeverity.NONE]: 'No vulnerability or informational finding only',\r\n      [VulnerabilitySeverity.UNKNOWN]: 'Unknown vulnerability severity requiring assessment and classification',\r\n    };\r\n    return descriptions[severity];\r\n  }\r\n\r\n  /**\r\n   * Get severity from string (case-insensitive)\r\n   */\r\n  static fromString(value: string): VulnerabilitySeverity | null {\r\n    const normalized = value.toLowerCase().trim();\r\n    const severities = Object.values(VulnerabilitySeverity);\r\n    return severities.find(s => s === normalized) || null;\r\n  }\r\n\r\n  /**\r\n   * Validate severity value\r\n   */\r\n  static isValid(severity: string): boolean {\r\n    return Object.values(VulnerabilitySeverity).includes(severity as VulnerabilitySeverity);\r\n  }\r\n\r\n  /**\r\n   * Get processing priority (1-10) based on vulnerability severity\r\n   */\r\n  static getProcessingPriority(severity: VulnerabilitySeverity): number {\r\n    const priorities: Record<VulnerabilitySeverity, number> = {\r\n      [VulnerabilitySeverity.CRITICAL]: 10,\r\n      [VulnerabilitySeverity.HIGH]: 8,\r\n      [VulnerabilitySeverity.MEDIUM]: 6,\r\n      [VulnerabilitySeverity.LOW]: 4,\r\n      [VulnerabilitySeverity.NONE]: 2,\r\n      [VulnerabilitySeverity.UNKNOWN]: 5,\r\n    };\r\n    return priorities[severity];\r\n  }\r\n\r\n  /**\r\n   * Get business impact based on vulnerability severity\r\n   */\r\n  static getBusinessImpact(severity: VulnerabilitySeverity): {\r\n    confidentiality: 'none' | 'low' | 'medium' | 'high';\r\n    integrity: 'none' | 'low' | 'medium' | 'high';\r\n    availability: 'none' | 'low' | 'medium' | 'high';\r\n  } {\r\n    const impacts: Record<VulnerabilitySeverity, {\r\n      confidentiality: 'none' | 'low' | 'medium' | 'high';\r\n      integrity: 'none' | 'low' | 'medium' | 'high';\r\n      availability: 'none' | 'low' | 'medium' | 'high';\r\n    }> = {\r\n      [VulnerabilitySeverity.CRITICAL]: { confidentiality: 'high', integrity: 'high', availability: 'high' },\r\n      [VulnerabilitySeverity.HIGH]: { confidentiality: 'high', integrity: 'medium', availability: 'medium' },\r\n      [VulnerabilitySeverity.MEDIUM]: { confidentiality: 'medium', integrity: 'low', availability: 'low' },\r\n      [VulnerabilitySeverity.LOW]: { confidentiality: 'low', integrity: 'none', availability: 'none' },\r\n      [VulnerabilitySeverity.NONE]: { confidentiality: 'none', integrity: 'none', availability: 'none' },\r\n      [VulnerabilitySeverity.UNKNOWN]: { confidentiality: 'medium', integrity: 'medium', availability: 'medium' },\r\n    };\r\n    return impacts[severity];\r\n  }\r\n\r\n  /**\r\n   * Get remediation complexity based on vulnerability severity\r\n   */\r\n  static getRemediationComplexity(severity: VulnerabilitySeverity): {\r\n    effort: 'low' | 'medium' | 'high' | 'critical';\r\n    risk: 'low' | 'medium' | 'high' | 'critical';\r\n    testing: 'minimal' | 'standard' | 'extensive' | 'comprehensive';\r\n  } {\r\n    const complexity: Record<VulnerabilitySeverity, {\r\n      effort: 'low' | 'medium' | 'high' | 'critical';\r\n      risk: 'low' | 'medium' | 'high' | 'critical';\r\n      testing: 'minimal' | 'standard' | 'extensive' | 'comprehensive';\r\n    }> = {\r\n      [VulnerabilitySeverity.CRITICAL]: { effort: 'critical', risk: 'critical', testing: 'comprehensive' },\r\n      [VulnerabilitySeverity.HIGH]: { effort: 'high', risk: 'high', testing: 'extensive' },\r\n      [VulnerabilitySeverity.MEDIUM]: { effort: 'medium', risk: 'medium', testing: 'standard' },\r\n      [VulnerabilitySeverity.LOW]: { effort: 'low', risk: 'low', testing: 'minimal' },\r\n      [VulnerabilitySeverity.NONE]: { effort: 'low', risk: 'low', testing: 'minimal' },\r\n      [VulnerabilitySeverity.UNKNOWN]: { effort: 'medium', risk: 'medium', testing: 'standard' },\r\n    };\r\n    return complexity[severity];\r\n  }\r\n\r\n  /**\r\n   * Calculate combined severity from multiple vulnerabilities\r\n   */\r\n  static calculateCombinedSeverity(severities: VulnerabilitySeverity[]): VulnerabilitySeverity {\r\n    if (severities.length === 0) {\r\n      return VulnerabilitySeverity.UNKNOWN;\r\n    }\r\n\r\n    // If any vulnerability is critical, combined severity is critical\r\n    if (severities.includes(VulnerabilitySeverity.CRITICAL)) {\r\n      return VulnerabilitySeverity.CRITICAL;\r\n    }\r\n\r\n    // Calculate weighted average (excluding unknown)\r\n    const validSeverities = severities.filter(s => s !== VulnerabilitySeverity.UNKNOWN);\r\n    if (validSeverities.length === 0) {\r\n      return VulnerabilitySeverity.UNKNOWN;\r\n    }\r\n\r\n    const numericValues = validSeverities.map(s => VulnerabilitySeverityUtils.getNumericValue(s));\r\n    const average = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length;\r\n\r\n    // Convert back to severity\r\n    if (average >= 3.5) return VulnerabilitySeverity.CRITICAL;\r\n    if (average >= 2.5) return VulnerabilitySeverity.HIGH;\r\n    if (average >= 1.5) return VulnerabilitySeverity.MEDIUM;\r\n    if (average >= 0.5) return VulnerabilitySeverity.LOW;\r\n    return VulnerabilitySeverity.NONE;\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability age impact on severity\r\n   */\r\n  static getAgeAdjustedSeverity(\r\n    originalSeverity: VulnerabilitySeverity,\r\n    ageInDays: number\r\n  ): VulnerabilitySeverity {\r\n    // Vulnerabilities become more severe over time if not remediated\r\n    const ageMultiplier = Math.min(1.5, 1 + (ageInDays / 365)); // Max 1.5x after 1 year\r\n    const originalValue = VulnerabilitySeverityUtils.getNumericValue(originalSeverity);\r\n    const adjustedValue = Math.min(4, Math.floor(originalValue * ageMultiplier));\r\n\r\n    const severityMap: Record<number, VulnerabilitySeverity> = {\r\n      0: VulnerabilitySeverity.NONE,\r\n      1: VulnerabilitySeverity.LOW,\r\n      2: VulnerabilitySeverity.MEDIUM,\r\n      3: VulnerabilitySeverity.HIGH,\r\n      4: VulnerabilitySeverity.CRITICAL,\r\n    };\r\n\r\n    return severityMap[adjustedValue] || originalSeverity;\r\n  }\r\n}"], "version": 3}