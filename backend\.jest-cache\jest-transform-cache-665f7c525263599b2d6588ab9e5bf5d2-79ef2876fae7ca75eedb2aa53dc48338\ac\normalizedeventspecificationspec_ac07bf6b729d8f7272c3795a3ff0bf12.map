{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\normalized-event.specification.spec.ts", "mappings": ";;AAAA,sFAmB2C;AAC3C,oFAAuI;AACvI,gEAA8D;AAC9D,gHAA+F;AAC/F,kHAAiG;AACjG,4GAA2F;AAC3F,iEAAwD;AACxD,yEAAgE;AAChE,qEAA4D;AAC5D,2FAAiF;AACjF,+EAAqE;AAErE,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;IAC9C,IAAI,SAA+B,CAAC;IACpC,IAAI,YAA2B,CAAC;IAChC,IAAI,QAA2B,CAAC;IAEhC,UAAU,CAAC,GAAG,EAAE;QACd,uBAAuB;QACvB,MAAM,SAAS,GAAG,6CAAc,CAAC,MAAM,EAAE,CAAC;QAC1C,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QACrE,YAAY,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAEvD,iCAAiC;QACjC,QAAQ,GAAG;YACT,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,yBAAyB;YACtC,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,KAAK;SAChB,CAAC;QAEF,oBAAoB;QACpB,SAAS,GAAG;YACV,eAAe,EAAE,8BAAc,CAAC,MAAM,EAAE;YACxC,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,2BAAS,CAAC,eAAe;YAC/B,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,MAAM,EAAE,+BAAW,CAAC,MAAM;YAC1B,gBAAgB,EAAE,oDAAqB,CAAC,UAAU;YAClD,mBAAmB,EAAE,6CAAmB,CAAC,SAAS;YAClD,YAAY,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE;YAC7B,cAAc,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;YACtC,KAAK,EAAE,uBAAuB;YAC9B,YAAY,EAAE,CAAC,QAAQ,CAAC;YACxB,aAAa,EAAE,OAAO;YACtB,gBAAgB,EAAE,EAAE;YACpB,qBAAqB,EAAE,CAAC;YACxB,wBAAwB,EAAE,IAAI,IAAI,EAAE;YACpC,mBAAmB,EAAE;gBACnB,OAAO,EAAE,IAAI;gBACb,YAAY,EAAE,CAAC,WAAW,CAAC;gBAC3B,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE,EAAE;gBACV,oBAAoB,EAAE,IAAI;gBAC1B,eAAe,EAAE,EAAE;aACpB;SACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,oEAAmC,EAAE,CAAC;YAEvD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,UAAU;YACV,MAAM,YAAY,GAAG,EAAE,GAAG,SAAS,EAAE,mBAAmB,EAAE,6CAAmB,CAAC,OAAO,EAAE,CAAC;YACxF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC7D,MAAM,IAAI,GAAG,IAAI,oEAAmC,EAAE,CAAC;YAEvD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,UAAU;YACV,MAAM,WAAW,GAAG;gBAClB,GAAG,SAAS;gBACZ,mBAAmB,EAAE,6CAAmB,CAAC,MAAM;gBAC/C,sBAAsB,EAAE,YAAY;gBACpC,wBAAwB,EAAE,SAAS;gBACnC,mBAAmB,EAAE,SAAS;aAC/B,CAAC;YACF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAC5D,MAAM,IAAI,GAAG,IAAI,iEAAgC,EAAE,CAAC;YAEpD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,iEAAgC,EAAE,CAAC;YAEpD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sCAAsC,EAAE,GAAG,EAAE;QACpD,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,UAAU;YACV,MAAM,eAAe,GAAG;gBACtB,GAAG,SAAS;gBACZ,mBAAmB,EAAE,6CAAmB,CAAC,WAAW;gBACpD,sBAAsB,EAAE,IAAI,IAAI,EAAE;gBAClC,wBAAwB,EAAE,SAAS;gBACnC,mBAAmB,EAAE,SAAS;aAC/B,CAAC;YACF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAChE,MAAM,IAAI,GAAG,IAAI,qEAAoC,EAAE,CAAC;YAExD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,qEAAoC,EAAE,CAAC;YAExD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,6DAA4B,EAAE,CAAC;YAEhD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,UAAU;YACV,MAAM,eAAe,GAAG,EAAE,GAAG,SAAS,EAAE,gBAAgB,EAAE,EAAE,EAAE,CAAC;YAC/D,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAChE,MAAM,IAAI,GAAG,IAAI,6DAA4B,EAAE,CAAC;YAEhD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,UAAU;YACV,MAAM,eAAe,GAAG,EAAE,GAAG,SAAS,EAAE,gBAAgB,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC;YACnF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAChE,MAAM,IAAI,GAAG,IAAI,iEAAgC,EAAE,CAAC;YAEpD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,iEAAgC,EAAE,CAAC;YAEpD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,UAAU;YACV,MAAM,WAAW,GAAG,EAAE,GAAG,SAAS,EAAE,oBAAoB,EAAE,IAAI,EAAE,CAAC;YACjE,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAC5D,MAAM,IAAI,GAAG,IAAI,kEAAiC,EAAE,CAAC;YAErD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,kEAAiC,EAAE,CAAC;YAErD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,+DAA8B,EAAE,CAAC;YAElD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,UAAU;YACV,MAAM,aAAa,GAAG;gBACpB,GAAG,SAAS;gBACZ,mBAAmB,EAAE,6CAAmB,CAAC,OAAO;gBAChD,wBAAwB,EAAE,SAAS;gBACnC,mBAAmB,EAAE,SAAS;aAC/B,CAAC;YACF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,IAAI,+DAA8B,EAAE,CAAC;YAElD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,iEAAgC,CAAC,CAAC,6CAAmB,CAAC,SAAS,EAAE,6CAAmB,CAAC,MAAM,CAAC,CAAC,CAAC;YAE/G,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,iEAAgC,CAAC,CAAC,6CAAmB,CAAC,OAAO,EAAE,6CAAmB,CAAC,WAAW,CAAC,CAAC,CAAC;YAElH,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,2DAA0B,CAAC,OAAO,CAAC,CAAC;YAErD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,2DAA0B,CAAC,OAAO,CAAC,CAAC;YAErD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,mEAAkC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAE5D,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QACjG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,mEAAkC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;YAE7D,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,mEAAkC,CAAC,EAAE,CAAC,CAAC;YAExD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,mEAAkC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAEnE,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,yDAAwB,CAAC,WAAW,CAAC,CAAC;YAEvD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,yDAAwB,CAAC,mBAAmB,CAAC,CAAC;YAE/D,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,2DAA0B,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAEvE,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,+CAA+C,SAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAC5H,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uEAAuE,EAAE,GAAG,EAAE;YAC/E,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,WAAW,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAC5C,MAAM,IAAI,GAAG,IAAI,2DAA0B,CAAC,WAAW,CAAC,CAAC;YAEzD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,UAAU;YACV,MAAM,gBAAgB,GAAG,EAAE,GAAG,SAAS,EAAE,qBAAqB,EAAE,CAAC,EAAE,CAAC;YACpE,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACjE,MAAM,IAAI,GAAG,IAAI,iEAAgC,EAAE,CAAC;YAEpD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uEAAuE,EAAE,GAAG,EAAE;YAC/E,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,iEAAgC,EAAE,CAAC;YAEpD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sCAAsC,EAAE,GAAG,EAAE;QACpD,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,UAAU;YACV,MAAM,aAAa,GAAG,EAAE,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;YACtD,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,IAAI,qEAAoC,EAAE,CAAC;YAExD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,UAAU;YACV,MAAM,YAAY,GAAG,EAAE,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;YACrD,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC7D,MAAM,IAAI,GAAG,IAAI,qEAAoC,EAAE,CAAC;YAExD,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,UAAU;YACV,MAAM,aAAa,GAAG;gBACpB,GAAG,SAAS;gBACZ,oBAAoB,EAAE,IAAI;gBAC1B,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,UAAU,EAAE,qBAAqB;aAClC,CAAC;YACF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,IAAI,2DAA0B,EAAE,CAAC;YAE9C,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,2DAA0B,EAAE,CAAC;YAE9C,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,UAAU;YACV,MAAM,kBAAkB,GAAG;gBACzB,GAAG,SAAS;gBACZ,oBAAoB,EAAE,IAAI;gBAC1B,UAAU,EAAE,SAAS;aACtB,CAAC;YACF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;YACnE,MAAM,IAAI,GAAG,IAAI,2DAA0B,EAAE,CAAC;YAE9C,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,2DAA0B,EAAE,CAAC;YAE9C,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,UAAU;YACV,MAAM,aAAa,GAAG;gBACpB,GAAG,SAAS;gBACZ,oBAAoB,EAAE,IAAI;gBAC1B,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,UAAU,EAAE,qBAAqB;aAClC,CAAC;YACF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,IAAI,2DAA0B,EAAE,CAAC;YAE9C,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yCAAyC,EAAE,GAAG,EAAE;QACvD,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,UAAU;YACV,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YACrD,MAAM,aAAa,GAAG;gBACpB,GAAG,SAAS;gBACZ,sBAAsB,EAAE,SAAS;gBACjC,wBAAwB,EAAE,OAAO;aAClC,CAAC;YACF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,IAAI,wEAAuC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAEtE,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,UAAU;YACV,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC;YACtD,MAAM,aAAa,GAAG;gBACpB,GAAG,SAAS;gBACZ,sBAAsB,EAAE,SAAS;gBACjC,wBAAwB,EAAE,OAAO;aAClC,CAAC;YACF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,IAAI,wEAAuC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAEtE,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,UAAU;YACV,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,IAAI,wEAAuC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAEtE,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,gBAAgB;YAChB,MAAM,IAAI,GAAG,oEAAmC;iBAC7C,MAAM,EAAE;iBACR,sBAAsB,EAAE;iBACxB,KAAK,EAAE,CAAC;YAEX,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAE1D,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,gBAAgB;YAChB,MAAM,IAAI,GAAG,oEAAmC;iBAC7C,MAAM,EAAE;iBACR,sBAAsB,EAAE;iBACxB,eAAe,EAAE;iBACjB,iBAAiB,CAAC,OAAO,CAAC;iBAC1B,KAAK,EAAE,CAAC;YAEX,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAE1D,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,gBAAgB;YAChB,MAAM,IAAI,GAAG,oEAAmC;iBAC7C,MAAM,EAAE;iBACR,sBAAsB,EAAE;iBACxB,mBAAmB,EAAE;iBACrB,WAAW,EAAE,CAAC;YAEjB,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAE1D,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,yBAAyB;YACzB,MAAM,CAAC,GAAG,EAAE;gBACV,oEAAmC;qBAChC,MAAM,EAAE;qBACR,KAAK,EAAE,CAAC;YACb,CAAC,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,gBAAgB;YAChB,MAAM,IAAI,GAAG,oEAAmC;iBAC7C,MAAM,EAAE;iBACR,sBAAsB,EAAE;iBACxB,eAAe,EAAE;iBACjB,iBAAiB,EAAE;iBACnB,iBAAiB,CAAC,OAAO,CAAC;iBAC1B,qBAAqB,CAAC,EAAE,EAAE,GAAG,CAAC;iBAC9B,eAAe,CAAC,WAAW,CAAC;iBAC5B,KAAK,EAAE,CAAC;YAEX,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAE1D,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,gBAAgB;YAChB,MAAM,IAAI,GAAG,oEAAmC;iBAC7C,MAAM,EAAE;iBACR,oBAAoB,EAAE;iBACtB,aAAa,EAAE;iBACf,WAAW,EAAE,CAAC;YAEjB,MAAM,kBAAkB,GAAG;gBACzB,GAAG,SAAS;gBACZ,oBAAoB,EAAE,IAAI;gBAC1B,UAAU,EAAE,SAAS;aACtB,CAAC;YACF,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;YAEnE,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,UAAU;YACV,MAAM,KAAK,GAAG,IAAI,oEAAmC,EAAE,CAAC;YACxD,MAAM,KAAK,GAAG,IAAI,6DAA4B,EAAE,CAAC;YACjD,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAEtC,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAE1D,eAAe;YACf,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,UAAU;YACV,MAAM,KAAK,GAAG,IAAI,oEAAmC,EAAE,CAAC;YACxD,MAAM,KAAK,GAAG,IAAI,iEAAgC,EAAE,CAAC;YACrD,MAAM,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;YAErC,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAE1D,eAAe;YACf,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,UAAU;YACV,MAAM,IAAI,GAAG,IAAI,iEAAgC,EAAE,CAAC;YACpD,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE/B,MAAM,eAAe,GAAG,yCAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAE1D,eAAe;YACf,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\normalized-event.specification.spec.ts"], "sourcesContent": ["import {\r\n  NormalizationCompletedSpecification,\r\n  NormalizationFailedSpecification,\r\n  NormalizationInProgressSpecification,\r\n  HighDataQualitySpecification,\r\n  HasValidationErrorsSpecification,\r\n  RequiresManualReviewSpecification,\r\n  ReadyForNextStageSpecification,\r\n  NormalizationStatusSpecification,\r\n  SchemaVersionSpecification,\r\n  DataQualityScoreRangeSpecification,\r\n  AppliedRuleSpecification,\r\n  OriginalEventSpecification,\r\n  ExceededMaxAttemptsSpecification,\r\n  HighRiskNormalizedEventSpecification,\r\n  ReviewedEventSpecification,\r\n  PendingReviewSpecification,\r\n  NormalizationDurationRangeSpecification,\r\n  NormalizedEventSpecificationBuilder,\r\n} from '../normalized-event.specification';\r\nimport { NormalizedEvent, NormalizedEventProps, NormalizationStatus, NormalizationRule } from '../../entities/normalized-event.entity';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { EventMetadata } from '../../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../value-objects/event-metadata/event-source.value-object';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventStatus } from '../../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../../enums/event-source-type.enum';\r\n\r\ndescribe('NormalizedEvent Specifications', () => {\r\n  let baseProps: NormalizedEventProps;\r\n  let mockMetadata: EventMetadata;\r\n  let mockRule: NormalizationRule;\r\n\r\n  beforeEach(() => {\r\n    // Create mock metadata\r\n    const timestamp = EventTimestamp.create();\r\n    const source = EventSource.create(EventSourceType.SIEM, 'test-siem');\r\n    mockMetadata = EventMetadata.create(timestamp, source);\r\n\r\n    // Create mock normalization rule\r\n    mockRule = {\r\n      id: 'test-rule',\r\n      name: 'Test Rule',\r\n      description: 'Test normalization rule',\r\n      priority: 100,\r\n      required: false,\r\n    };\r\n\r\n    // Create base props\r\n    baseProps = {\r\n      originalEventId: UniqueEntityId.create(),\r\n      metadata: mockMetadata,\r\n      type: EventType.THREAT_DETECTED,\r\n      severity: EventSeverity.HIGH,\r\n      status: EventStatus.ACTIVE,\r\n      processingStatus: EventProcessingStatus.NORMALIZED,\r\n      normalizationStatus: NormalizationStatus.COMPLETED,\r\n      originalData: { raw: 'data' },\r\n      normalizedData: { normalized: 'data' },\r\n      title: 'Test Normalized Event',\r\n      appliedRules: [mockRule],\r\n      schemaVersion: '1.0.0',\r\n      dataQualityScore: 85,\r\n      normalizationAttempts: 1,\r\n      normalizationCompletedAt: new Date(),\r\n      normalizationResult: {\r\n        success: true,\r\n        appliedRules: ['test-rule'],\r\n        failedRules: [],\r\n        warnings: [],\r\n        errors: [],\r\n        processingDurationMs: 1000,\r\n        confidenceScore: 90,\r\n      },\r\n    };\r\n  });\r\n\r\n  describe('NormalizationCompletedSpecification', () => {\r\n    it('should be satisfied by completed normalized events', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new NormalizationCompletedSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event has completed normalization');\r\n    });\r\n\r\n    it('should not be satisfied by non-completed normalized events', () => {\r\n      // Arrange\r\n      const pendingProps = { ...baseProps, normalizationStatus: NormalizationStatus.PENDING };\r\n      const normalizedEvent = NormalizedEvent.create(pendingProps);\r\n      const spec = new NormalizationCompletedSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('NormalizationFailedSpecification', () => {\r\n    it('should be satisfied by failed normalized events', () => {\r\n      // Arrange\r\n      const failedProps = {\r\n        ...baseProps,\r\n        normalizationStatus: NormalizationStatus.FAILED,\r\n        lastNormalizationError: 'Test error',\r\n        normalizationCompletedAt: undefined,\r\n        normalizationResult: undefined,\r\n      };\r\n      const normalizedEvent = NormalizedEvent.create(failedProps);\r\n      const spec = new NormalizationFailedSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event has failed normalization');\r\n    });\r\n\r\n    it('should not be satisfied by non-failed normalized events', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new NormalizationFailedSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('NormalizationInProgressSpecification', () => {\r\n    it('should be satisfied by in-progress normalized events', () => {\r\n      // Arrange\r\n      const inProgressProps = {\r\n        ...baseProps,\r\n        normalizationStatus: NormalizationStatus.IN_PROGRESS,\r\n        normalizationStartedAt: new Date(),\r\n        normalizationCompletedAt: undefined,\r\n        normalizationResult: undefined,\r\n      };\r\n      const normalizedEvent = NormalizedEvent.create(inProgressProps);\r\n      const spec = new NormalizationInProgressSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event is currently being normalized');\r\n    });\r\n\r\n    it('should not be satisfied by non-in-progress normalized events', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new NormalizationInProgressSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('HighDataQualitySpecification', () => {\r\n    it('should be satisfied by events with high data quality', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new HighDataQualitySpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event has high data quality (>= 60)');\r\n    });\r\n\r\n    it('should not be satisfied by events with low data quality', () => {\r\n      // Arrange\r\n      const lowQualityProps = { ...baseProps, dataQualityScore: 45 };\r\n      const normalizedEvent = NormalizedEvent.create(lowQualityProps);\r\n      const spec = new HighDataQualitySpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('HasValidationErrorsSpecification', () => {\r\n    it('should be satisfied by events with validation errors', () => {\r\n      // Arrange\r\n      const propsWithErrors = { ...baseProps, validationErrors: ['Error 1', 'Error 2'] };\r\n      const normalizedEvent = NormalizedEvent.create(propsWithErrors);\r\n      const spec = new HasValidationErrorsSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event has validation errors');\r\n    });\r\n\r\n    it('should not be satisfied by events without validation errors', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new HasValidationErrorsSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('RequiresManualReviewSpecification', () => {\r\n    it('should be satisfied by events requiring manual review', () => {\r\n      // Arrange\r\n      const reviewProps = { ...baseProps, requiresManualReview: true };\r\n      const normalizedEvent = NormalizedEvent.create(reviewProps);\r\n      const spec = new RequiresManualReviewSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event requires manual review');\r\n    });\r\n\r\n    it('should not be satisfied by events not requiring manual review', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new RequiresManualReviewSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('ReadyForNextStageSpecification', () => {\r\n    it('should be satisfied by events ready for next stage', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new ReadyForNextStageSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event is ready for next processing stage');\r\n    });\r\n\r\n    it('should not be satisfied by events not ready for next stage', () => {\r\n      // Arrange\r\n      const notReadyProps = {\r\n        ...baseProps,\r\n        normalizationStatus: NormalizationStatus.PENDING,\r\n        normalizationCompletedAt: undefined,\r\n        normalizationResult: undefined,\r\n      };\r\n      const normalizedEvent = NormalizedEvent.create(notReadyProps);\r\n      const spec = new ReadyForNextStageSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('NormalizationStatusSpecification', () => {\r\n    it('should be satisfied by events with matching status', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new NormalizationStatusSpecification([NormalizationStatus.COMPLETED, NormalizationStatus.FAILED]);\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event status is one of: COMPLETED, FAILED');\r\n    });\r\n\r\n    it('should not be satisfied by events with non-matching status', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new NormalizationStatusSpecification([NormalizationStatus.PENDING, NormalizationStatus.IN_PROGRESS]);\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('SchemaVersionSpecification', () => {\r\n    it('should be satisfied by events with matching schema version', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new SchemaVersionSpecification('1.0.0');\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event uses schema version: 1.0.0');\r\n    });\r\n\r\n    it('should not be satisfied by events with non-matching schema version', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new SchemaVersionSpecification('2.0.0');\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('DataQualityScoreRangeSpecification', () => {\r\n    it('should be satisfied by events within score range', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new DataQualityScoreRangeSpecification(80, 90);\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event data quality score is between 80 and 90');\r\n    });\r\n\r\n    it('should not be satisfied by events outside score range', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new DataQualityScoreRangeSpecification(90, 100);\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n\r\n    it('should handle minimum score only', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new DataQualityScoreRangeSpecification(80);\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event data quality score is at least 80');\r\n    });\r\n\r\n    it('should handle maximum score only', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new DataQualityScoreRangeSpecification(undefined, 90);\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event data quality score is at most 90');\r\n    });\r\n  });\r\n\r\n  describe('AppliedRuleSpecification', () => {\r\n    it('should be satisfied by events with applied rule', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new AppliedRuleSpecification('test-rule');\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event has applied rule: test-rule');\r\n    });\r\n\r\n    it('should not be satisfied by events without applied rule', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new AppliedRuleSpecification('non-existent-rule');\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('OriginalEventSpecification', () => {\r\n    it('should be satisfied by events with matching original event ID', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new OriginalEventSpecification(baseProps.originalEventId);\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe(`Normalized event references original event: ${baseProps.originalEventId.toString()}`);\r\n    });\r\n\r\n    it('should not be satisfied by events with non-matching original event ID', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const differentId = UniqueEntityId.create();\r\n      const spec = new OriginalEventSpecification(differentId);\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('ExceededMaxAttemptsSpecification', () => {\r\n    it('should be satisfied by events that exceeded max attempts', () => {\r\n      // Arrange\r\n      const maxAttemptsProps = { ...baseProps, normalizationAttempts: 5 };\r\n      const normalizedEvent = NormalizedEvent.create(maxAttemptsProps);\r\n      const spec = new ExceededMaxAttemptsSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event has exceeded maximum normalization attempts');\r\n    });\r\n\r\n    it('should not be satisfied by events that have not exceeded max attempts', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new ExceededMaxAttemptsSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('HighRiskNormalizedEventSpecification', () => {\r\n    it('should be satisfied by high-risk events', () => {\r\n      // Arrange\r\n      const highRiskProps = { ...baseProps, riskScore: 85 };\r\n      const normalizedEvent = NormalizedEvent.create(highRiskProps);\r\n      const spec = new HighRiskNormalizedEventSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event has high risk score (>= 80)');\r\n    });\r\n\r\n    it('should not be satisfied by low-risk events', () => {\r\n      // Arrange\r\n      const lowRiskProps = { ...baseProps, riskScore: 50 };\r\n      const normalizedEvent = NormalizedEvent.create(lowRiskProps);\r\n      const spec = new HighRiskNormalizedEventSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('ReviewedEventSpecification', () => {\r\n    it('should be satisfied by reviewed events', () => {\r\n      // Arrange\r\n      const reviewedProps = {\r\n        ...baseProps,\r\n        requiresManualReview: true,\r\n        reviewedAt: new Date(),\r\n        reviewedBy: '<EMAIL>',\r\n      };\r\n      const normalizedEvent = NormalizedEvent.create(reviewedProps);\r\n      const spec = new ReviewedEventSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event has been manually reviewed');\r\n    });\r\n\r\n    it('should not be satisfied by non-reviewed events', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new ReviewedEventSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('PendingReviewSpecification', () => {\r\n    it('should be satisfied by events pending review', () => {\r\n      // Arrange\r\n      const pendingReviewProps = {\r\n        ...baseProps,\r\n        requiresManualReview: true,\r\n        reviewedAt: undefined,\r\n      };\r\n      const normalizedEvent = NormalizedEvent.create(pendingReviewProps);\r\n      const spec = new PendingReviewSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event is pending manual review');\r\n    });\r\n\r\n    it('should not be satisfied by events not requiring review', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new PendingReviewSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n\r\n    it('should not be satisfied by already reviewed events', () => {\r\n      // Arrange\r\n      const reviewedProps = {\r\n        ...baseProps,\r\n        requiresManualReview: true,\r\n        reviewedAt: new Date(),\r\n        reviewedBy: '<EMAIL>',\r\n      };\r\n      const normalizedEvent = NormalizedEvent.create(reviewedProps);\r\n      const spec = new PendingReviewSpecification();\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('NormalizationDurationRangeSpecification', () => {\r\n    it('should be satisfied by events within duration range', () => {\r\n      // Arrange\r\n      const startTime = new Date();\r\n      const endTime = new Date(startTime.getTime() + 5000);\r\n      const durationProps = {\r\n        ...baseProps,\r\n        normalizationStartedAt: startTime,\r\n        normalizationCompletedAt: endTime,\r\n      };\r\n      const normalizedEvent = NormalizedEvent.create(durationProps);\r\n      const spec = new NormalizationDurationRangeSpecification(1000, 10000);\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Normalized event duration is between 1000ms and 10000ms');\r\n    });\r\n\r\n    it('should not be satisfied by events outside duration range', () => {\r\n      // Arrange\r\n      const startTime = new Date();\r\n      const endTime = new Date(startTime.getTime() + 15000);\r\n      const durationProps = {\r\n        ...baseProps,\r\n        normalizationStartedAt: startTime,\r\n        normalizationCompletedAt: endTime,\r\n      };\r\n      const normalizedEvent = NormalizedEvent.create(durationProps);\r\n      const spec = new NormalizationDurationRangeSpecification(1000, 10000);\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n\r\n    it('should not be satisfied by events without duration information', () => {\r\n      // Arrange\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n      const spec = new NormalizationDurationRangeSpecification(1000, 10000);\r\n\r\n      // Act & Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('NormalizedEventSpecificationBuilder', () => {\r\n    it('should build specification with single condition', () => {\r\n      // Arrange & Act\r\n      const spec = NormalizedEventSpecificationBuilder\r\n        .create()\r\n        .normalizationCompleted()\r\n        .build();\r\n\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n\r\n      // Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n    });\r\n\r\n    it('should build specification with multiple AND conditions', () => {\r\n      // Arrange & Act\r\n      const spec = NormalizedEventSpecificationBuilder\r\n        .create()\r\n        .normalizationCompleted()\r\n        .highDataQuality()\r\n        .withSchemaVersion('1.0.0')\r\n        .build();\r\n\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n\r\n      // Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n    });\r\n\r\n    it('should build specification with multiple OR conditions', () => {\r\n      // Arrange & Act\r\n      const spec = NormalizedEventSpecificationBuilder\r\n        .create()\r\n        .normalizationCompleted()\r\n        .normalizationFailed()\r\n        .buildWithOr();\r\n\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n\r\n      // Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n    });\r\n\r\n    it('should fail when building without conditions', () => {\r\n      // Arrange & Act & Assert\r\n      expect(() => {\r\n        NormalizedEventSpecificationBuilder\r\n          .create()\r\n          .build();\r\n      }).toThrow('At least one specification must be added');\r\n    });\r\n\r\n    it('should build complex specification with all conditions', () => {\r\n      // Arrange & Act\r\n      const spec = NormalizedEventSpecificationBuilder\r\n        .create()\r\n        .normalizationCompleted()\r\n        .highDataQuality()\r\n        .readyForNextStage()\r\n        .withSchemaVersion('1.0.0')\r\n        .dataQualityScoreRange(80, 100)\r\n        .withAppliedRule('test-rule')\r\n        .build();\r\n\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n\r\n      // Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n    });\r\n\r\n    it('should build specification for events requiring attention', () => {\r\n      // Arrange & Act\r\n      const spec = NormalizedEventSpecificationBuilder\r\n        .create()\r\n        .requiresManualReview()\r\n        .pendingReview()\r\n        .buildWithOr();\r\n\r\n      const pendingReviewProps = {\r\n        ...baseProps,\r\n        requiresManualReview: true,\r\n        reviewedAt: undefined,\r\n      };\r\n      const normalizedEvent = NormalizedEvent.create(pendingReviewProps);\r\n\r\n      // Assert\r\n      expect(spec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('specification combinations', () => {\r\n    it('should combine specifications with AND logic', () => {\r\n      // Arrange\r\n      const spec1 = new NormalizationCompletedSpecification();\r\n      const spec2 = new HighDataQualitySpecification();\r\n      const combinedSpec = spec1.and(spec2);\r\n\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n\r\n      // Act & Assert\r\n      expect(combinedSpec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n    });\r\n\r\n    it('should combine specifications with OR logic', () => {\r\n      // Arrange\r\n      const spec1 = new NormalizationCompletedSpecification();\r\n      const spec2 = new NormalizationFailedSpecification();\r\n      const combinedSpec = spec1.or(spec2);\r\n\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n\r\n      // Act & Assert\r\n      expect(combinedSpec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n    });\r\n\r\n    it('should negate specifications', () => {\r\n      // Arrange\r\n      const spec = new NormalizationFailedSpecification();\r\n      const negatedSpec = spec.not();\r\n\r\n      const normalizedEvent = NormalizedEvent.create(baseProps);\r\n\r\n      // Act & Assert\r\n      expect(negatedSpec.isSatisfiedBy(normalizedEvent)).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}