431cb065141883105de3050725d185fb
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnrichmentSourceUtils = exports.EnrichmentSource = void 0;
/**
 * Enrichment Source Enum
 *
 * Represents the various sources of data used to enrich security events
 * with additional context, threat intelligence, and metadata. Used for
 * tracking data provenance, quality assessment, and source reliability.
 */
var EnrichmentSource;
(function (EnrichmentSource) {
    /**
     * Threat Intelligence Sources
     */
    /** Commercial threat intelligence feeds */
    EnrichmentSource["COMMERCIAL_THREAT_INTEL"] = "commercial_threat_intel";
    /** Open source threat intelligence */
    EnrichmentSource["OSINT"] = "osint";
    /** Government threat intelligence */
    EnrichmentSource["GOVERNMENT_INTEL"] = "government_intel";
    /** Industry sharing groups */
    EnrichmentSource["INDUSTRY_SHARING"] = "industry_sharing";
    /** Internal threat intelligence */
    EnrichmentSource["INTERNAL_INTEL"] = "internal_intel";
    /** Threat intelligence platforms */
    EnrichmentSource["TIP"] = "tip";
    /** MISP (Malware Information Sharing Platform) */
    EnrichmentSource["MISP"] = "misp";
    /** STIX/TAXII feeds */
    EnrichmentSource["STIX_TAXII"] = "stix_taxii";
    /**
     * Reputation Services
     */
    /** IP reputation services */
    EnrichmentSource["IP_REPUTATION"] = "ip_reputation";
    /** Domain reputation services */
    EnrichmentSource["DOMAIN_REPUTATION"] = "domain_reputation";
    /** URL reputation services */
    EnrichmentSource["URL_REPUTATION"] = "url_reputation";
    /** File hash reputation */
    EnrichmentSource["FILE_REPUTATION"] = "file_reputation";
    /** Email reputation services */
    EnrichmentSource["EMAIL_REPUTATION"] = "email_reputation";
    /**
     * Geolocation Services
     */
    /** IP geolocation databases */
    EnrichmentSource["IP_GEOLOCATION"] = "ip_geolocation";
    /** ASN (Autonomous System Number) databases */
    EnrichmentSource["ASN_DATABASE"] = "asn_database";
    /** ISP information services */
    EnrichmentSource["ISP_DATABASE"] = "isp_database";
    /**
     * Vulnerability Databases
     */
    /** National Vulnerability Database */
    EnrichmentSource["NVD"] = "nvd";
    /** CVE databases */
    EnrichmentSource["CVE_DATABASE"] = "cve_database";
    /** CVSS scoring services */
    EnrichmentSource["CVSS_DATABASE"] = "cvss_database";
    /** Exploit databases */
    EnrichmentSource["EXPLOIT_DATABASE"] = "exploit_database";
    /** Vendor security advisories */
    EnrichmentSource["VENDOR_ADVISORIES"] = "vendor_advisories";
    /**
     * Asset and Configuration Sources
     */
    /** Configuration Management Database */
    EnrichmentSource["CMDB"] = "cmdb";
    /** Asset management systems */
    EnrichmentSource["ASSET_MANAGEMENT"] = "asset_management";
    /** Network discovery tools */
    EnrichmentSource["NETWORK_DISCOVERY"] = "network_discovery";
    /** Vulnerability scanners */
    EnrichmentSource["VULNERABILITY_SCANNER"] = "vulnerability_scanner";
    /** Inventory management systems */
    EnrichmentSource["INVENTORY_SYSTEM"] = "inventory_system";
    /**
     * Identity and Access Sources
     */
    /** Active Directory */
    EnrichmentSource["ACTIVE_DIRECTORY"] = "active_directory";
    /** LDAP directories */
    EnrichmentSource["LDAP"] = "ldap";
    /** Identity providers */
    EnrichmentSource["IDENTITY_PROVIDER"] = "identity_provider";
    /** HR systems */
    EnrichmentSource["HR_SYSTEM"] = "hr_system";
    /** Privileged access management */
    EnrichmentSource["PAM_SYSTEM"] = "pam_system";
    /**
     * Network and Infrastructure Sources
     */
    /** DNS resolution services */
    EnrichmentSource["DNS_RESOLUTION"] = "dns_resolution";
    /** WHOIS databases */
    EnrichmentSource["WHOIS"] = "whois";
    /** Network topology discovery */
    EnrichmentSource["NETWORK_TOPOLOGY"] = "network_topology";
    /** Firewall rule databases */
    EnrichmentSource["FIREWALL_RULES"] = "firewall_rules";
    /** Network device configurations */
    EnrichmentSource["NETWORK_CONFIG"] = "network_config";
    /**
     * Behavioral and Analytics Sources
     */
    /** User behavior analytics */
    EnrichmentSource["UBA"] = "uba";
    /** Entity behavior analytics */
    EnrichmentSource["EBA"] = "eba";
    /** Machine learning models */
    EnrichmentSource["ML_MODELS"] = "ml_models";
    /** Statistical baselines */
    EnrichmentSource["STATISTICAL_BASELINE"] = "statistical_baseline";
    /** Anomaly detection systems */
    EnrichmentSource["ANOMALY_DETECTION"] = "anomaly_detection";
    /**
     * Historical and Context Sources
     */
    /** Historical event data */
    EnrichmentSource["HISTORICAL_EVENTS"] = "historical_events";
    /** Previous incident data */
    EnrichmentSource["INCIDENT_HISTORY"] = "incident_history";
    /** Audit logs */
    EnrichmentSource["AUDIT_LOGS"] = "audit_logs";
    /** Compliance databases */
    EnrichmentSource["COMPLIANCE_DATABASE"] = "compliance_database";
    /** Risk assessment data */
    EnrichmentSource["RISK_ASSESSMENT"] = "risk_assessment";
    /**
     * External Services and APIs
     */
    /** VirusTotal */
    EnrichmentSource["VIRUSTOTAL"] = "virustotal";
    /** Shodan */
    EnrichmentSource["SHODAN"] = "shodan";
    /** Censys */
    EnrichmentSource["CENSYS"] = "censys";
    /** PassiveTotal */
    EnrichmentSource["PASSIVETOTAL"] = "passivetotal";
    /** URLVoid */
    EnrichmentSource["URLVOID"] = "urlvoid";
    /** Hybrid Analysis */
    EnrichmentSource["HYBRID_ANALYSIS"] = "hybrid_analysis";
    /**
     * Cloud and SaaS Sources
     */
    /** AWS security services */
    EnrichmentSource["AWS_SECURITY"] = "aws_security";
    /** Azure security services */
    EnrichmentSource["AZURE_SECURITY"] = "azure_security";
    /** Google Cloud security */
    EnrichmentSource["GCP_SECURITY"] = "gcp_security";
    /** Office 365 security */
    EnrichmentSource["O365_SECURITY"] = "o365_security";
    /** Cloud access security brokers */
    EnrichmentSource["CASB"] = "casb";
    /**
     * Sandbox and Analysis Sources
     */
    /** Malware sandboxes */
    EnrichmentSource["SANDBOX"] = "sandbox";
    /** Dynamic analysis platforms */
    EnrichmentSource["DYNAMIC_ANALYSIS"] = "dynamic_analysis";
    /** Static analysis tools */
    EnrichmentSource["STATIC_ANALYSIS"] = "static_analysis";
    /** Reverse engineering tools */
    EnrichmentSource["REVERSE_ENGINEERING"] = "reverse_engineering";
    /**
     * Internal Sources
     */
    /** Internal security tools */
    EnrichmentSource["INTERNAL_SECURITY_TOOLS"] = "internal_security_tools";
    /** Custom enrichment rules */
    EnrichmentSource["CUSTOM_RULES"] = "custom_rules";
    /** Manual analyst input */
    EnrichmentSource["MANUAL_ANALYSIS"] = "manual_analysis";
    /** Automated correlation */
    EnrichmentSource["AUTOMATED_CORRELATION"] = "automated_correlation";
    /** Machine learning inference */
    EnrichmentSource["ML_INFERENCE"] = "ml_inference";
    /**
     * Unknown or Other Sources
     */
    /** Unknown enrichment source */
    EnrichmentSource["UNKNOWN"] = "unknown";
    /** Other sources not categorized */
    EnrichmentSource["OTHER"] = "other";
})(EnrichmentSource || (exports.EnrichmentSource = EnrichmentSource = {}));
/**
 * Enrichment Source Utilities
 */
class EnrichmentSourceUtils {
    /**
     * Get all enrichment sources
     */
    static getAllSources() {
        return Object.values(EnrichmentSource);
    }
    /**
     * Get threat intelligence sources
     */
    static getThreatIntelSources() {
        return [
            EnrichmentSource.COMMERCIAL_THREAT_INTEL,
            EnrichmentSource.OSINT,
            EnrichmentSource.GOVERNMENT_INTEL,
            EnrichmentSource.INDUSTRY_SHARING,
            EnrichmentSource.INTERNAL_INTEL,
            EnrichmentSource.TIP,
            EnrichmentSource.MISP,
            EnrichmentSource.STIX_TAXII,
        ];
    }
    /**
     * Get reputation service sources
     */
    static getReputationSources() {
        return [
            EnrichmentSource.IP_REPUTATION,
            EnrichmentSource.DOMAIN_REPUTATION,
            EnrichmentSource.URL_REPUTATION,
            EnrichmentSource.FILE_REPUTATION,
            EnrichmentSource.EMAIL_REPUTATION,
        ];
    }
    /**
     * Get external API sources
     */
    static getExternalAPISources() {
        return [
            EnrichmentSource.VIRUSTOTAL,
            EnrichmentSource.SHODAN,
            EnrichmentSource.CENSYS,
            EnrichmentSource.PASSIVETOTAL,
            EnrichmentSource.URLVOID,
            EnrichmentSource.HYBRID_ANALYSIS,
        ];
    }
    /**
     * Get internal sources
     */
    static getInternalSources() {
        return [
            EnrichmentSource.INTERNAL_SECURITY_TOOLS,
            EnrichmentSource.CUSTOM_RULES,
            EnrichmentSource.MANUAL_ANALYSIS,
            EnrichmentSource.AUTOMATED_CORRELATION,
            EnrichmentSource.ML_INFERENCE,
            EnrichmentSource.HISTORICAL_EVENTS,
            EnrichmentSource.INCIDENT_HISTORY,
        ];
    }
    /**
     * Get real-time sources (provide immediate enrichment)
     */
    static getRealTimeSources() {
        return [
            EnrichmentSource.IP_REPUTATION,
            EnrichmentSource.DOMAIN_REPUTATION,
            EnrichmentSource.URL_REPUTATION,
            EnrichmentSource.DNS_RESOLUTION,
            EnrichmentSource.IP_GEOLOCATION,
            EnrichmentSource.VIRUSTOTAL,
            EnrichmentSource.AUTOMATED_CORRELATION,
            EnrichmentSource.ML_INFERENCE,
        ];
    }
    /**
     * Get batch processing sources (require scheduled updates)
     */
    static getBatchSources() {
        return [
            EnrichmentSource.COMMERCIAL_THREAT_INTEL,
            EnrichmentSource.NVD,
            EnrichmentSource.CVE_DATABASE,
            EnrichmentSource.CMDB,
            EnrichmentSource.ASSET_MANAGEMENT,
            EnrichmentSource.HISTORICAL_EVENTS,
            EnrichmentSource.COMPLIANCE_DATABASE,
        ];
    }
    /**
     * Check if source is external
     */
    static isExternal(source) {
        const externalSources = [
            ...EnrichmentSourceUtils.getExternalAPISources(),
            ...EnrichmentSourceUtils.getReputationSources(),
            EnrichmentSource.COMMERCIAL_THREAT_INTEL,
            EnrichmentSource.OSINT,
            EnrichmentSource.GOVERNMENT_INTEL,
            EnrichmentSource.NVD,
            EnrichmentSource.CVE_DATABASE,
        ];
        return externalSources.includes(source);
    }
    /**
     * Check if source is internal
     */
    static isInternal(source) {
        return EnrichmentSourceUtils.getInternalSources().includes(source);
    }
    /**
     * Check if source provides real-time data
     */
    static isRealTime(source) {
        return EnrichmentSourceUtils.getRealTimeSources().includes(source);
    }
    /**
     * Check if source requires batch processing
     */
    static isBatchProcessed(source) {
        return EnrichmentSourceUtils.getBatchSources().includes(source);
    }
    /**
     * Get reliability score for source (0-100)
     */
    static getReliabilityScore(source) {
        const scores = {
            // High reliability sources
            [EnrichmentSource.GOVERNMENT_INTEL]: 95,
            [EnrichmentSource.NVD]: 95,
            [EnrichmentSource.CVE_DATABASE]: 95,
            [EnrichmentSource.COMMERCIAL_THREAT_INTEL]: 90,
            [EnrichmentSource.CMDB]: 90,
            [EnrichmentSource.ACTIVE_DIRECTORY]: 90,
            // Medium-high reliability
            [EnrichmentSource.INDUSTRY_SHARING]: 85,
            [EnrichmentSource.VIRUSTOTAL]: 85,
            [EnrichmentSource.IP_REPUTATION]: 80,
            [EnrichmentSource.DOMAIN_REPUTATION]: 80,
            [EnrichmentSource.INTERNAL_INTEL]: 80,
            // Medium reliability
            [EnrichmentSource.OSINT]: 70,
            [EnrichmentSource.SHODAN]: 75,
            [EnrichmentSource.WHOIS]: 75,
            [EnrichmentSource.DNS_RESOLUTION]: 75,
            [EnrichmentSource.ML_MODELS]: 70,
            // Lower reliability
            [EnrichmentSource.MANUAL_ANALYSIS]: 60,
            [EnrichmentSource.CUSTOM_RULES]: 65,
            [EnrichmentSource.UNKNOWN]: 30,
            [EnrichmentSource.OTHER]: 40,
        };
        return scores[source] || 50; // Default medium reliability
    }
    /**
     * Get data freshness requirement in hours
     */
    static getFreshnessRequirement(source) {
        const freshness = {
            // Real-time requirements (< 1 hour)
            [EnrichmentSource.IP_REPUTATION]: 0.25, // 15 minutes
            [EnrichmentSource.DOMAIN_REPUTATION]: 0.25, // 15 minutes
            [EnrichmentSource.DNS_RESOLUTION]: 0.5, // 30 minutes
            [EnrichmentSource.ML_INFERENCE]: 0.1, // 6 minutes
            // Hourly updates
            [EnrichmentSource.COMMERCIAL_THREAT_INTEL]: 1,
            [EnrichmentSource.VIRUSTOTAL]: 1,
            [EnrichmentSource.IP_GEOLOCATION]: 2,
            // Daily updates
            [EnrichmentSource.NVD]: 24,
            [EnrichmentSource.CVE_DATABASE]: 24,
            [EnrichmentSource.OSINT]: 24,
            [EnrichmentSource.WHOIS]: 24,
            // Weekly updates
            [EnrichmentSource.CMDB]: 168,
            [EnrichmentSource.ASSET_MANAGEMENT]: 168,
            [EnrichmentSource.COMPLIANCE_DATABASE]: 168,
            // Monthly updates
            [EnrichmentSource.HR_SYSTEM]: 720,
            [EnrichmentSource.HISTORICAL_EVENTS]: 720,
        };
        return freshness[source] || 24; // Default daily updates
    }
    /**
     * Get cost category for source
     */
    static getCostCategory(source) {
        const costs = {
            // Free sources
            [EnrichmentSource.OSINT]: 'free',
            [EnrichmentSource.NVD]: 'free',
            [EnrichmentSource.CVE_DATABASE]: 'free',
            [EnrichmentSource.DNS_RESOLUTION]: 'free',
            [EnrichmentSource.WHOIS]: 'free',
            // Low cost
            [EnrichmentSource.IP_GEOLOCATION]: 'low',
            [EnrichmentSource.VIRUSTOTAL]: 'low',
            [EnrichmentSource.URLVOID]: 'low',
            // Medium cost
            [EnrichmentSource.IP_REPUTATION]: 'medium',
            [EnrichmentSource.DOMAIN_REPUTATION]: 'medium',
            [EnrichmentSource.SHODAN]: 'medium',
            [EnrichmentSource.CENSYS]: 'medium',
            // High cost
            [EnrichmentSource.COMMERCIAL_THREAT_INTEL]: 'high',
            [EnrichmentSource.PASSIVETOTAL]: 'high',
            [EnrichmentSource.HYBRID_ANALYSIS]: 'high',
            // Enterprise cost
            [EnrichmentSource.GOVERNMENT_INTEL]: 'enterprise',
            [EnrichmentSource.TIP]: 'enterprise',
            [EnrichmentSource.CASB]: 'enterprise',
        };
        return costs[source] || 'medium';
    }
    /**
     * Get API rate limits (requests per minute)
     */
    static getRateLimit(source) {
        const rateLimits = {
            [EnrichmentSource.VIRUSTOTAL]: 4, // Free tier
            [EnrichmentSource.SHODAN]: 1, // Free tier
            [EnrichmentSource.URLVOID]: 1000, // Generous limit
            [EnrichmentSource.DNS_RESOLUTION]: 1000, // Usually unlimited
            [EnrichmentSource.IP_GEOLOCATION]: 1000, // Varies by provider
            [EnrichmentSource.WHOIS]: 100, // Conservative estimate
            [EnrichmentSource.COMMERCIAL_THREAT_INTEL]: 1000, // Paid service
            [EnrichmentSource.IP_REPUTATION]: 1000, // Paid service
            [EnrichmentSource.DOMAIN_REPUTATION]: 1000, // Paid service
        };
        return rateLimits[source] || 100; // Default conservative limit
    }
    /**
     * Get supported data types for source
     */
    static getSupportedDataTypes(source) {
        const dataTypes = {
            [EnrichmentSource.IP_REPUTATION]: ['ip_address', 'reputation_score', 'categories'],
            [EnrichmentSource.DOMAIN_REPUTATION]: ['domain', 'reputation_score', 'categories'],
            [EnrichmentSource.URL_REPUTATION]: ['url', 'reputation_score', 'categories'],
            [EnrichmentSource.FILE_REPUTATION]: ['file_hash', 'reputation_score', 'malware_family'],
            [EnrichmentSource.IP_GEOLOCATION]: ['ip_address', 'country', 'city', 'coordinates'],
            [EnrichmentSource.WHOIS]: ['domain', 'registrar', 'creation_date', 'contacts'],
            [EnrichmentSource.DNS_RESOLUTION]: ['domain', 'ip_addresses', 'record_types'],
            [EnrichmentSource.VIRUSTOTAL]: ['file_hash', 'url', 'domain', 'scan_results'],
            [EnrichmentSource.CVE_DATABASE]: ['cve_id', 'cvss_score', 'description', 'references'],
            [EnrichmentSource.ASSET_MANAGEMENT]: ['asset_id', 'owner', 'location', 'criticality'],
        };
        return dataTypes[source] || ['generic_data'];
    }
    /**
     * Get human-readable description
     */
    static getDescription(source) {
        const descriptions = {
            [EnrichmentSource.COMMERCIAL_THREAT_INTEL]: 'Commercial threat intelligence feeds and services',
            [EnrichmentSource.OSINT]: 'Open source intelligence from public sources',
            [EnrichmentSource.GOVERNMENT_INTEL]: 'Government and law enforcement threat intelligence',
            [EnrichmentSource.IP_REPUTATION]: 'IP address reputation and categorization services',
            [EnrichmentSource.DOMAIN_REPUTATION]: 'Domain reputation and categorization services',
            [EnrichmentSource.VIRUSTOTAL]: 'VirusTotal file and URL analysis service',
            [EnrichmentSource.NVD]: 'National Vulnerability Database',
            [EnrichmentSource.CVE_DATABASE]: 'Common Vulnerabilities and Exposures database',
            [EnrichmentSource.CMDB]: 'Configuration Management Database',
            [EnrichmentSource.ACTIVE_DIRECTORY]: 'Microsoft Active Directory services',
            [EnrichmentSource.DNS_RESOLUTION]: 'DNS resolution and lookup services',
            [EnrichmentSource.WHOIS]: 'WHOIS domain registration information',
            [EnrichmentSource.IP_GEOLOCATION]: 'IP address geolocation services',
            [EnrichmentSource.SHODAN]: 'Shodan internet-connected device search engine',
            [EnrichmentSource.ML_MODELS]: 'Machine learning models and inference engines',
            [EnrichmentSource.MANUAL_ANALYSIS]: 'Manual analysis by security analysts',
            [EnrichmentSource.AUTOMATED_CORRELATION]: 'Automated event correlation and analysis',
            [EnrichmentSource.HISTORICAL_EVENTS]: 'Historical security event data',
            [EnrichmentSource.UNKNOWN]: 'Unknown or unidentified enrichment source',
            [EnrichmentSource.OTHER]: 'Other enrichment source not categorized',
        };
        return descriptions[source] || `${source.replace(/_/g, ' ').toLowerCase()} enrichment source`;
    }
    /**
     * Get category for source
     */
    static getCategory(source) {
        if (EnrichmentSourceUtils.getThreatIntelSources().includes(source))
            return 'Threat Intelligence';
        if (EnrichmentSourceUtils.getReputationSources().includes(source))
            return 'Reputation Services';
        if (EnrichmentSourceUtils.getExternalAPISources().includes(source))
            return 'External APIs';
        const categories = {
            'Geolocation': [EnrichmentSource.IP_GEOLOCATION, EnrichmentSource.ASN_DATABASE, EnrichmentSource.ISP_DATABASE],
            'Vulnerability': [EnrichmentSource.NVD, EnrichmentSource.CVE_DATABASE, EnrichmentSource.CVSS_DATABASE, EnrichmentSource.EXPLOIT_DATABASE],
            'Asset Management': [EnrichmentSource.CMDB, EnrichmentSource.ASSET_MANAGEMENT, EnrichmentSource.INVENTORY_SYSTEM],
            'Identity': [EnrichmentSource.ACTIVE_DIRECTORY, EnrichmentSource.LDAP, EnrichmentSource.HR_SYSTEM],
            'Network': [EnrichmentSource.DNS_RESOLUTION, EnrichmentSource.WHOIS, EnrichmentSource.NETWORK_TOPOLOGY],
            'Analytics': [EnrichmentSource.UBA, EnrichmentSource.ML_MODELS, EnrichmentSource.ANOMALY_DETECTION],
            'Cloud': [EnrichmentSource.AWS_SECURITY, EnrichmentSource.AZURE_SECURITY, EnrichmentSource.GCP_SECURITY],
            'Analysis': [EnrichmentSource.SANDBOX, EnrichmentSource.DYNAMIC_ANALYSIS, EnrichmentSource.STATIC_ANALYSIS],
            'Internal': [EnrichmentSource.INTERNAL_SECURITY_TOOLS, EnrichmentSource.CUSTOM_RULES, EnrichmentSource.MANUAL_ANALYSIS],
        };
        for (const [category, sources] of Object.entries(categories)) {
            if (sources.includes(source))
                return category;
        }
        return 'Other';
    }
    /**
     * Validate enrichment source
     */
    static isValid(source) {
        return Object.values(EnrichmentSource).includes(source);
    }
    /**
     * Get enrichment source from string (case-insensitive)
     */
    static fromString(value) {
        const normalized = value.toLowerCase().trim().replace(/[^a-z]/g, '_');
        const sources = Object.values(EnrichmentSource);
        return sources.find(source => source === normalized) || null;
    }
    /**
     * Get enrichment priority based on source reliability and data type
     */
    static getEnrichmentPriority(source, dataType) {
        const baseReliability = EnrichmentSourceUtils.getReliabilityScore(source);
        const isRealTime = EnrichmentSourceUtils.isRealTime(source);
        const costCategory = EnrichmentSourceUtils.getCostCategory(source);
        let priority = baseReliability;
        // Boost priority for real-time sources
        if (isRealTime)
            priority += 10;
        // Adjust for cost (lower cost = higher priority for equivalent reliability)
        const costAdjustment = {
            'free': 5,
            'low': 3,
            'medium': 0,
            'high': -2,
            'enterprise': -5,
        };
        priority += costAdjustment[costCategory];
        // Ensure priority stays within bounds
        return Math.max(0, Math.min(100, priority));
    }
    /**
     * Get recommended refresh interval in minutes
     */
    static getRefreshInterval(source) {
        const freshnessHours = EnrichmentSourceUtils.getFreshnessRequirement(source);
        return Math.max(1, freshnessHours * 60); // Convert to minutes, minimum 1 minute
    }
}
exports.EnrichmentSourceUtils = EnrichmentSourceUtils;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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