{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\normalized-event.factory.spec.ts", "mappings": ";;AAAA,0EAAwH;AACxH,oFAAiH;AAEjH,gEAA8D;AAC9D,gHAA+F;AAC/F,kHAAiG;AACjG,4GAA2F;AAC3F,iEAAwD;AACxD,yEAAgE;AAChE,qEAA4D;AAC5D,2FAAiF;AACjF,+EAAqE;AAErE,+BAA+B;AAC/B,MAAM,SAAS;IAiBb,YAAY,QAA4B,EAAE;QACxC,MAAM,SAAS,GAAG,6CAAc,CAAC,MAAM,EAAE,CAAC;QAC1C,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QACrE,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAEzD,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,IAAI,8BAAc,CAAC,MAAM,EAAE,CAAC;QAC9C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI,QAAQ,CAAC;QAC3C,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,2BAAS,CAAC,eAAe,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI,mCAAa,CAAC,IAAI,CAAC;QACrD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,+BAAW,CAAC,MAAM,CAAC;QACjD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,gBAAgB,IAAI,oDAAqB,CAAC,GAAG,CAAC;QAC5E,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;QACjD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,YAAY,CAAC;QACzC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;QACrC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;QACjC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,eAAe,CAAC;QAC7C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;IAC3C,CAAC;IAED,UAAU;QACR,OAAO,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,CAAC;IAClD,CAAC;CACF;AAED,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,IAAI,SAAoB,CAAC;IACzB,IAAI,QAA2B,CAAC;IAEhC,UAAU,CAAC,GAAG,EAAE;QACd,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;QAC5B,QAAQ,GAAG;YACT,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,yBAAyB;YACtC,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,KAAK;SAChB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,UAAU;YACV,MAAM,OAAO,GAAiC;gBAC5C,aAAa,EAAE,SAAgB;gBAC/B,cAAc,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;gBACtC,aAAa,EAAE,OAAO;aACvB,CAAC;YAEF,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE/D,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,yCAAe,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAC9D,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC1D,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,UAAU;YACV,MAAM,OAAO,GAAiC;gBAC5C,aAAa,EAAE,SAAgB;gBAC/B,cAAc,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;gBACtC,aAAa,EAAE,OAAO;aACvB,CAAC;YAEF,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE/D,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAW,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,UAAU,CAAC,CAAC;YAChF,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,6CAAmB,CAAC,OAAO,CAAC,CAAC;YAC9E,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,UAAU;YACV,MAAM,OAAO,GAAiC;gBAC5C,aAAa,EAAE,SAAgB;gBAC/B,cAAc,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;gBACtC,aAAa,EAAE,OAAO;gBACtB,IAAI,EAAE,2BAAS,CAAC,gBAAgB;gBAChC,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,KAAK,EAAE,cAAc;gBACrB,YAAY,EAAE,CAAC,QAAQ,CAAC;gBACxB,mBAAmB,EAAE,6CAAmB,CAAC,SAAS;aACnD,CAAC;YAEF,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE/D,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,gBAAgB,CAAC,CAAC;YAC9D,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,QAAQ,CAAC,CAAC;YAC9D,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnD,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACzD,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,6CAAmB,CAAC,SAAS,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,UAAU;YACV,SAAS,CAAC,UAAU,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAiC;gBAC5C,aAAa,EAAE,SAAgB;gBAC/B,cAAc,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;gBACtC,aAAa,EAAE,OAAO;gBACtB,UAAU,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE;aACpC,CAAC;YAEF,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE/D,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC;gBACzC,QAAQ,EAAE,OAAO;gBACjB,UAAU,EAAE,OAAO;aACpB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,UAAU;YACV,MAAM,MAAM,GAAiC;gBAC3C,oBAAoB,EAAE,OAAO;gBAC7B,cAAc,EAAE,CAAC,QAAQ,CAAC;gBAC1B,uBAAuB,EAAE,EAAE;aAC5B,CAAC;YAEF,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,uBAAuB,CAAC,SAAgB,EAAE,MAAM,CAAC,CAAC;YAEjG,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,yCAAe,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,UAAU;YACV,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC;YACzB,MAAM,MAAM,GAAiC;gBAC3C,8BAA8B,EAAE,IAAI;aACrC,CAAC;YAEF,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,uBAAuB,CAAC,SAAgB,EAAE,MAAM,CAAC,CAAC;YAEjG,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,UAAU;YACV,SAAS,CAAC,QAAQ,GAAG,mCAAa,CAAC,QAAQ,CAAC;YAC5C,MAAM,MAAM,GAAiC;gBAC3C,8BAA8B,EAAE,IAAI;aACrC,CAAC;YAEF,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,uBAAuB,CAAC,SAAgB,EAAE,MAAM,CAAC,CAAC;YAEjG,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,UAAU;YACV,MAAM,OAAO,GAAG;gBACd,UAAU,EAAE,iBAAiB;gBAC7B,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,iBAAiB;gBACxB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;YACF,MAAM,eAAe,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAChD,MAAM,aAAa,GAAG,OAAO,CAAC;YAE9B,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;YAEpG,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,yCAAe,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YACjE,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC1D,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,UAAU;YACV,MAAM,OAAO,GAAG;gBACd,UAAU,EAAE,kBAAkB;gBAC9B,QAAQ,EAAE,UAAU;gBACpB,KAAK,EAAE,eAAe;gBACtB,WAAW,EAAE,4BAA4B;gBACzC,IAAI,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC;gBAC1B,UAAU,EAAE,EAAE;aACf,CAAC;YACF,MAAM,eAAe,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAChD,MAAM,aAAa,GAAG,OAAO,CAAC;YAE9B,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;YAEpG,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,gBAAgB,CAAC,CAAC;YAC9D,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,QAAQ,CAAC,CAAC;YAC9D,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACpD,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACvE,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,UAAU;YACV,MAAM,MAAM,GAAG;gBACb,IAAI,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;gBACnC,IAAI,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;gBACnC,IAAI,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;aACpC,CAAC;YACF,MAAM,OAAO,GAAG;gBACd,MAAM,EAAE,MAAe;gBACvB,aAAa,EAAE,OAAO;gBACtB,KAAK,EAAE,CAAC,QAAQ,CAAC;aAClB,CAAC;YAEF,MAAM;YACN,MAAM,MAAM,GAAG,iDAAsB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAE3D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,UAAU;YACV,MAAM,MAAM,GAAG;gBACb,IAAI,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;gBACnC,IAAW,EAAE,2BAA2B;gBACxC,IAAI,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;aACpC,CAAC;YACF,MAAM,OAAO,GAAG;gBACd,MAAM,EAAE,MAAe;gBACvB,aAAa,EAAE,OAAO;gBACtB,KAAK,EAAE,CAAC,QAAQ,CAAC;gBACjB,aAAa,EAAE,KAAK;aACrB,CAAC;YAEF,MAAM;YACN,MAAM,MAAM,GAAG,iDAAsB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAE3D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,UAAU;YACV,MAAM,MAAM,GAAG;gBACb,IAAI,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;gBACnC,IAAW,EAAE,2BAA2B;gBACxC,IAAI,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;aACpC,CAAC;YACF,MAAM,OAAO,GAAG;gBACd,MAAM,EAAE,MAAe;gBACvB,aAAa,EAAE,OAAO;gBACtB,KAAK,EAAE,CAAC,QAAQ,CAAC;gBACjB,aAAa,EAAE,IAAI;aACpB,CAAC;YAEF,MAAM;YACN,MAAM,MAAM,GAAG,iDAAsB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAE3D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,gBAAgB,EAAE,CAAC;YAElE,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,yCAAe,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,6CAAmB,CAAC,SAAS,CAAC,CAAC;YAChF,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClD,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,cAAc,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,UAAU;YACV,MAAM,SAAS,GAAG;gBAChB,aAAa,EAAE,SAAgB;gBAC/B,mBAAmB,EAAE,6CAAmB,CAAC,MAAM;gBAC/C,gBAAgB,EAAE,EAAE;aACrB,CAAC;YAEF,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAE3E,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAC9D,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,6CAAmB,CAAC,MAAM,CAAC,CAAC;YAC7E,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,qDAAqD;YACrD,8DAA8D;YAC9D,0CAA0C;YAC1C,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,qDAAqD;YACrD,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,qDAAqD;YACrD,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,uEAAuE,EAAE,GAAG,EAAE;YAC/E,UAAU;YACV,MAAM,MAAM,GAAiC;gBAC3C,cAAc,EAAE,CAAC,QAAQ,CAAC;aAC3B,CAAC;YAEF,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,uBAAuB,CAAC,SAAgB,EAAE,MAAM,CAAC,CAAC;YAEjG,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,8CAA8C;YAC9C,yEAAyE;YACzE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,UAAU;YACV,MAAM,OAAO,GAAG,EAAE,UAAU,EAAE,iBAAiB,EAAE,CAAC;YAClD,MAAM,eAAe,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAEhD,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;YAE9F,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,UAAU;YACV,MAAM,OAAO,GAAG,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC;YAChD,MAAM,eAAe,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAEhD,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;YAE9F,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,gBAAgB,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,UAAU;YACV,MAAM,OAAO,GAAG,EAAE,UAAU,EAAE,cAAc,EAAE,CAAC;YAC/C,MAAM,eAAe,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAEhD,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;YAE9F,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,MAAM,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,UAAU;YACV,MAAM,OAAO,GAAG,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YACzC,MAAM,eAAe,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAEhD,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;YAE9F,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,QAAQ,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,UAAU;YACV,MAAM,OAAO,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;YACrC,MAAM,eAAe,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAEhD,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;YAE9F,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,UAAU;YACV,MAAM,OAAO,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;YACxC,MAAM,eAAe,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAEhD,MAAM;YACN,MAAM,eAAe,GAAG,iDAAsB,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;YAE9F,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,MAAM,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,UAAU;YACV,MAAM,cAAc,GAAG;gBACrB,wBAAwB;gBACxB,cAAc,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;gBACtC,aAAa,EAAE,OAAO;aAChB,CAAC;YAET,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,iDAAsB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,UAAU;YACV,MAAM,OAAO,GAAiC;gBAC5C,aAAa,EAAE,SAAgB;gBAC/B,cAAc,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;gBACtC,aAAa,EAAE,EAAE,EAAE,uBAAuB;aAC3C,CAAC;YAEF,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\normalized-event.factory.spec.ts"], "sourcesContent": ["import { NormalizedEventFactory, CreateNormalizedEventOptions, NormalizationConfig } from '../normalized-event.factory';\r\nimport { NormalizedEvent, NormalizationStatus, NormalizationRule } from '../../entities/normalized-event.entity';\r\nimport { Event } from '../../entities/event.entity';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { EventMetadata } from '../../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../value-objects/event-metadata/event-source.value-object';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventStatus } from '../../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../../enums/event-source-type.enum';\r\n\r\n// Mock Event class for testing\r\nclass MockEvent {\r\n  public id: UniqueEntityId;\r\n  public metadata: EventMetadata;\r\n  public type: EventType;\r\n  public severity: EventSeverity;\r\n  public status: EventStatus;\r\n  public processingStatus: EventProcessingStatus;\r\n  public rawData: Record<string, any>;\r\n  public title: string;\r\n  public description?: string;\r\n  public tags: string[];\r\n  public riskScore?: number;\r\n  public confidenceLevel?: number;\r\n  public attributes: Record<string, any>;\r\n  public correlationId?: string;\r\n  public parentEventId?: UniqueEntityId;\r\n\r\n  constructor(props: Partial<MockEvent> = {}) {\r\n    const timestamp = EventTimestamp.create();\r\n    const source = EventSource.create(EventSourceType.SIEM, 'test-siem');\r\n    const metadata = EventMetadata.create(timestamp, source);\r\n\r\n    this.id = props.id || UniqueEntityId.create();\r\n    this.metadata = props.metadata || metadata;\r\n    this.type = props.type || EventType.THREAT_DETECTED;\r\n    this.severity = props.severity || EventSeverity.HIGH;\r\n    this.status = props.status || EventStatus.ACTIVE;\r\n    this.processingStatus = props.processingStatus || EventProcessingStatus.RAW;\r\n    this.rawData = props.rawData || { test: 'data' };\r\n    this.title = props.title || 'Test Event';\r\n    this.description = props.description;\r\n    this.tags = props.tags || [];\r\n    this.riskScore = props.riskScore;\r\n    this.confidenceLevel = props.confidenceLevel;\r\n    this.attributes = props.attributes || {};\r\n    this.correlationId = props.correlationId;\r\n    this.parentEventId = props.parentEventId;\r\n  }\r\n\r\n  isHighRisk(): boolean {\r\n    return (this.riskScore || 0) >= 80;\r\n  }\r\n\r\n  isCritical(): boolean {\r\n    return this.severity === EventSeverity.CRITICAL;\r\n  }\r\n}\r\n\r\ndescribe('NormalizedEventFactory', () => {\r\n  let mockEvent: MockEvent;\r\n  let mockRule: NormalizationRule;\r\n\r\n  beforeEach(() => {\r\n    mockEvent = new MockEvent();\r\n    mockRule = {\r\n      id: 'test-rule',\r\n      name: 'Test Rule',\r\n      description: 'Test normalization rule',\r\n      priority: 100,\r\n      required: false,\r\n    };\r\n  });\r\n\r\n  describe('create', () => {\r\n    it('should create a normalized event with required properties', () => {\r\n      // Arrange\r\n      const options: CreateNormalizedEventOptions = {\r\n        originalEvent: mockEvent as any,\r\n        normalizedData: { normalized: 'data' },\r\n        schemaVersion: '1.0.0',\r\n      };\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.create(options);\r\n\r\n      // Assert\r\n      expect(normalizedEvent).toBeInstanceOf(NormalizedEvent);\r\n      expect(normalizedEvent.originalEventId).toEqual(mockEvent.id);\r\n      expect(normalizedEvent.type).toBe(mockEvent.type);\r\n      expect(normalizedEvent.severity).toBe(mockEvent.severity);\r\n      expect(normalizedEvent.schemaVersion).toBe('1.0.0');\r\n      expect(normalizedEvent.normalizedData).toEqual({ normalized: 'data' });\r\n    });\r\n\r\n    it('should use default values when not provided', () => {\r\n      // Arrange\r\n      const options: CreateNormalizedEventOptions = {\r\n        originalEvent: mockEvent as any,\r\n        normalizedData: { normalized: 'data' },\r\n        schemaVersion: '1.0.0',\r\n      };\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.create(options);\r\n\r\n      // Assert\r\n      expect(normalizedEvent.status).toBe(EventStatus.ACTIVE);\r\n      expect(normalizedEvent.processingStatus).toBe(EventProcessingStatus.NORMALIZED);\r\n      expect(normalizedEvent.normalizationStatus).toBe(NormalizationStatus.PENDING);\r\n      expect(normalizedEvent.appliedRules).toEqual([]);\r\n    });\r\n\r\n    it('should override properties when provided', () => {\r\n      // Arrange\r\n      const options: CreateNormalizedEventOptions = {\r\n        originalEvent: mockEvent as any,\r\n        normalizedData: { normalized: 'data' },\r\n        schemaVersion: '1.0.0',\r\n        type: EventType.MALWARE_DETECTED,\r\n        severity: EventSeverity.CRITICAL,\r\n        title: 'Custom Title',\r\n        appliedRules: [mockRule],\r\n        normalizationStatus: NormalizationStatus.COMPLETED,\r\n      };\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.create(options);\r\n\r\n      // Assert\r\n      expect(normalizedEvent.type).toBe(EventType.MALWARE_DETECTED);\r\n      expect(normalizedEvent.severity).toBe(EventSeverity.CRITICAL);\r\n      expect(normalizedEvent.title).toBe('Custom Title');\r\n      expect(normalizedEvent.appliedRules).toEqual([mockRule]);\r\n      expect(normalizedEvent.normalizationStatus).toBe(NormalizationStatus.COMPLETED);\r\n    });\r\n\r\n    it('should merge attributes from original event and options', () => {\r\n      // Arrange\r\n      mockEvent.attributes = { original: 'value' };\r\n      const options: CreateNormalizedEventOptions = {\r\n        originalEvent: mockEvent as any,\r\n        normalizedData: { normalized: 'data' },\r\n        schemaVersion: '1.0.0',\r\n        attributes: { additional: 'value' },\r\n      };\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.create(options);\r\n\r\n      // Assert\r\n      expect(normalizedEvent.attributes).toEqual({\r\n        original: 'value',\r\n        additional: 'value',\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('createWithNormalization', () => {\r\n    it('should create normalized event with automatic normalization', () => {\r\n      // Arrange\r\n      const config: Partial<NormalizationConfig> = {\r\n        defaultSchemaVersion: '2.0.0',\r\n        availableRules: [mockRule],\r\n        minDataQualityThreshold: 70,\r\n      };\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.createWithNormalization(mockEvent as any, config);\r\n\r\n      // Assert\r\n      expect(normalizedEvent).toBeInstanceOf(NormalizedEvent);\r\n      expect(normalizedEvent.schemaVersion).toBe('2.0.0');\r\n      expect(normalizedEvent.dataQualityScore).toBeDefined();\r\n    });\r\n\r\n    it('should determine manual review requirement for high-risk events', () => {\r\n      // Arrange\r\n      mockEvent.riskScore = 85;\r\n      const config: Partial<NormalizationConfig> = {\r\n        requireManualReviewForHighRisk: true,\r\n      };\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.createWithNormalization(mockEvent as any, config);\r\n\r\n      // Assert\r\n      expect(normalizedEvent.requiresManualReview).toBe(true);\r\n    });\r\n\r\n    it('should determine manual review requirement for critical events', () => {\r\n      // Arrange\r\n      mockEvent.severity = EventSeverity.CRITICAL;\r\n      const config: Partial<NormalizationConfig> = {\r\n        requireManualReviewForCritical: true,\r\n      };\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.createWithNormalization(mockEvent as any, config);\r\n\r\n      // Assert\r\n      expect(normalizedEvent.requiresManualReview).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('fromRawData', () => {\r\n    it('should create normalized event from raw data', () => {\r\n      // Arrange\r\n      const rawData = {\r\n        event_type: 'threat_detected',\r\n        severity: 'high',\r\n        title: 'Raw Event Title',\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n      const originalEventId = UniqueEntityId.create();\r\n      const schemaVersion = '1.0.0';\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.fromRawData(rawData, originalEventId, schemaVersion);\r\n\r\n      // Assert\r\n      expect(normalizedEvent).toBeInstanceOf(NormalizedEvent);\r\n      expect(normalizedEvent.originalEventId).toEqual(originalEventId);\r\n      expect(normalizedEvent.schemaVersion).toBe(schemaVersion);\r\n      expect(normalizedEvent.originalData).toEqual(rawData);\r\n    });\r\n\r\n    it('should infer event properties from raw data', () => {\r\n      // Arrange\r\n      const rawData = {\r\n        event_type: 'malware_detected',\r\n        severity: 'critical',\r\n        title: 'Malware Alert',\r\n        description: 'Malware detected on system',\r\n        tags: ['malware', 'alert'],\r\n        risk_score: 90,\r\n      };\r\n      const originalEventId = UniqueEntityId.create();\r\n      const schemaVersion = '1.0.0';\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.fromRawData(rawData, originalEventId, schemaVersion);\r\n\r\n      // Assert\r\n      expect(normalizedEvent.type).toBe(EventType.MALWARE_DETECTED);\r\n      expect(normalizedEvent.severity).toBe(EventSeverity.CRITICAL);\r\n      expect(normalizedEvent.title).toBe('Malware Alert');\r\n      expect(normalizedEvent.description).toBe('Malware detected on system');\r\n      expect(normalizedEvent.tags).toEqual(['malware', 'alert']);\r\n      expect(normalizedEvent.riskScore).toBe(90);\r\n    });\r\n  });\r\n\r\n  describe('createBatch', () => {\r\n    it('should create multiple normalized events successfully', () => {\r\n      // Arrange\r\n      const events = [\r\n        new MockEvent({ title: 'Event 1' }),\r\n        new MockEvent({ title: 'Event 2' }),\r\n        new MockEvent({ title: 'Event 3' }),\r\n      ];\r\n      const options = {\r\n        events: events as any[],\r\n        schemaVersion: '1.0.0',\r\n        rules: [mockRule],\r\n      };\r\n\r\n      // Act\r\n      const result = NormalizedEventFactory.createBatch(options);\r\n\r\n      // Assert\r\n      expect(result.successful).toHaveLength(3);\r\n      expect(result.failed).toHaveLength(0);\r\n      expect(result.summary.total).toBe(3);\r\n      expect(result.summary.successful).toBe(3);\r\n      expect(result.summary.failed).toBe(0);\r\n      expect(result.summary.processingTimeMs).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should handle failures and continue processing', () => {\r\n      // Arrange\r\n      const events = [\r\n        new MockEvent({ title: 'Event 1' }),\r\n        null as any, // This will cause an error\r\n        new MockEvent({ title: 'Event 3' }),\r\n      ];\r\n      const options = {\r\n        events: events as any[],\r\n        schemaVersion: '1.0.0',\r\n        rules: [mockRule],\r\n        stopOnFailure: false,\r\n      };\r\n\r\n      // Act\r\n      const result = NormalizedEventFactory.createBatch(options);\r\n\r\n      // Assert\r\n      expect(result.successful).toHaveLength(2);\r\n      expect(result.failed).toHaveLength(1);\r\n      expect(result.summary.total).toBe(3);\r\n      expect(result.summary.successful).toBe(2);\r\n      expect(result.summary.failed).toBe(1);\r\n    });\r\n\r\n    it('should stop on first failure when configured', () => {\r\n      // Arrange\r\n      const events = [\r\n        new MockEvent({ title: 'Event 1' }),\r\n        null as any, // This will cause an error\r\n        new MockEvent({ title: 'Event 3' }),\r\n      ];\r\n      const options = {\r\n        events: events as any[],\r\n        schemaVersion: '1.0.0',\r\n        rules: [mockRule],\r\n        stopOnFailure: true,\r\n      };\r\n\r\n      // Act\r\n      const result = NormalizedEventFactory.createBatch(options);\r\n\r\n      // Assert\r\n      expect(result.successful).toHaveLength(1);\r\n      expect(result.failed).toHaveLength(1);\r\n      expect(result.summary.successful).toBe(1);\r\n      expect(result.summary.failed).toBe(1);\r\n    });\r\n  });\r\n\r\n  describe('createForTesting', () => {\r\n    it('should create normalized event for testing with defaults', () => {\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.createForTesting();\r\n\r\n      // Assert\r\n      expect(normalizedEvent).toBeInstanceOf(NormalizedEvent);\r\n      expect(normalizedEvent.normalizationStatus).toBe(NormalizationStatus.COMPLETED);\r\n      expect(normalizedEvent.dataQualityScore).toBe(85);\r\n      expect(normalizedEvent.normalizedData).toHaveProperty('normalized', true);\r\n    });\r\n\r\n    it('should create normalized event for testing with overrides', () => {\r\n      // Arrange\r\n      const overrides = {\r\n        originalEvent: mockEvent as any,\r\n        normalizationStatus: NormalizationStatus.FAILED,\r\n        dataQualityScore: 45,\r\n      };\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.createForTesting(overrides);\r\n\r\n      // Assert\r\n      expect(normalizedEvent.originalEventId).toEqual(mockEvent.id);\r\n      expect(normalizedEvent.normalizationStatus).toBe(NormalizationStatus.FAILED);\r\n      expect(normalizedEvent.dataQualityScore).toBe(45);\r\n    });\r\n  });\r\n\r\n  describe('normalization rules application', () => {\r\n    it('should apply timestamp normalization rule', () => {\r\n      // This test would verify the private method behavior\r\n      // In a real implementation, you might expose this for testing\r\n      // or test it through the public interface\r\n      expect(true).toBe(true); // Placeholder\r\n    });\r\n\r\n    it('should apply severity normalization rule', () => {\r\n      // This test would verify the private method behavior\r\n      expect(true).toBe(true); // Placeholder\r\n    });\r\n\r\n    it('should apply field mapping rule', () => {\r\n      // This test would verify the private method behavior\r\n      expect(true).toBe(true); // Placeholder\r\n    });\r\n  });\r\n\r\n  describe('data quality calculation', () => {\r\n    it('should calculate high data quality score for successful normalization', () => {\r\n      // Arrange\r\n      const config: Partial<NormalizationConfig> = {\r\n        availableRules: [mockRule],\r\n      };\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.createWithNormalization(mockEvent as any, config);\r\n\r\n      // Assert\r\n      expect(normalizedEvent.dataQualityScore).toBeGreaterThan(60);\r\n    });\r\n\r\n    it('should calculate lower data quality score for failed normalization', () => {\r\n      // This would test the private method behavior\r\n      // In practice, you might create a scenario that causes validation errors\r\n      expect(true).toBe(true); // Placeholder\r\n    });\r\n  });\r\n\r\n  describe('event type mapping', () => {\r\n    it('should map threat event types correctly', () => {\r\n      // Arrange\r\n      const rawData = { event_type: 'threat_detected' };\r\n      const originalEventId = UniqueEntityId.create();\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.fromRawData(rawData, originalEventId, '1.0.0');\r\n\r\n      // Assert\r\n      expect(normalizedEvent.type).toBe(EventType.THREAT_DETECTED);\r\n    });\r\n\r\n    it('should map malware event types correctly', () => {\r\n      // Arrange\r\n      const rawData = { event_type: 'malware_found' };\r\n      const originalEventId = UniqueEntityId.create();\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.fromRawData(rawData, originalEventId, '1.0.0');\r\n\r\n      // Assert\r\n      expect(normalizedEvent.type).toBe(EventType.MALWARE_DETECTED);\r\n    });\r\n\r\n    it('should default to custom event type for unknown types', () => {\r\n      // Arrange\r\n      const rawData = { event_type: 'unknown_type' };\r\n      const originalEventId = UniqueEntityId.create();\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.fromRawData(rawData, originalEventId, '1.0.0');\r\n\r\n      // Assert\r\n      expect(normalizedEvent.type).toBe(EventType.CUSTOM);\r\n    });\r\n  });\r\n\r\n  describe('severity mapping', () => {\r\n    it('should map critical severity correctly', () => {\r\n      // Arrange\r\n      const rawData = { severity: 'critical' };\r\n      const originalEventId = UniqueEntityId.create();\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.fromRawData(rawData, originalEventId, '1.0.0');\r\n\r\n      // Assert\r\n      expect(normalizedEvent.severity).toBe(EventSeverity.CRITICAL);\r\n    });\r\n\r\n    it('should map high severity correctly', () => {\r\n      // Arrange\r\n      const rawData = { severity: 'high' };\r\n      const originalEventId = UniqueEntityId.create();\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.fromRawData(rawData, originalEventId, '1.0.0');\r\n\r\n      // Assert\r\n      expect(normalizedEvent.severity).toBe(EventSeverity.HIGH);\r\n    });\r\n\r\n    it('should default to medium severity for unknown severities', () => {\r\n      // Arrange\r\n      const rawData = { severity: 'unknown' };\r\n      const originalEventId = UniqueEntityId.create();\r\n\r\n      // Act\r\n      const normalizedEvent = NormalizedEventFactory.fromRawData(rawData, originalEventId, '1.0.0');\r\n\r\n      // Assert\r\n      expect(normalizedEvent.severity).toBe(EventSeverity.MEDIUM);\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle missing required properties gracefully', () => {\r\n      // Arrange\r\n      const invalidOptions = {\r\n        // Missing originalEvent\r\n        normalizedData: { normalized: 'data' },\r\n        schemaVersion: '1.0.0',\r\n      } as any;\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEventFactory.create(invalidOptions)).toThrow();\r\n    });\r\n\r\n    it('should handle invalid schema version', () => {\r\n      // Arrange\r\n      const options: CreateNormalizedEventOptions = {\r\n        originalEvent: mockEvent as any,\r\n        normalizedData: { normalized: 'data' },\r\n        schemaVersion: '', // Empty schema version\r\n      };\r\n\r\n      // Act & Assert\r\n      expect(() => NormalizedEventFactory.create(options)).toThrow();\r\n    });\r\n  });\r\n});"], "version": 3}