0f6b9fa6ab3699524cffac7314624725
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const service_unavailable_exception_1 = require("../../exceptions/service-unavailable.exception");
describe('ServiceUnavailableException', () => {
    describe('constructor', () => {
        it('should create exception with required parameters', () => {
            const exception = new service_unavailable_exception_1.ServiceUnavailableException('Service is down', 'UserService', 'maintenance');
            expect(exception).toBeInstanceOf(service_unavailable_exception_1.ServiceUnavailableException);
            expect(exception.message).toBe('Service is down');
            expect(exception.code).toBe('SERVICE_UNAVAILABLE');
            expect(exception.severity).toBe('high');
            expect(exception.category).toBe('availability');
            expect(exception.serviceName).toBe('UserService');
            expect(exception.unavailabilityReason).toBe('maintenance');
        });
        it('should create exception with all options', () => {
            const estimatedRecoveryTime = new Date('2023-01-01T15:00:00Z');
            const maintenanceWindow = {
                start: new Date('2023-01-01T12:00:00Z'),
                end: new Date('2023-01-01T14:00:00Z'),
                description: 'Scheduled maintenance',
            };
            const exception = new service_unavailable_exception_1.ServiceUnavailableException('Service is down', 'UserService', 'maintenance', {
                estimatedRecoveryTime,
                retryAfter: 300,
                maintenanceWindow,
                correlationId: 'test-correlation-id',
            });
            expect(exception.estimatedRecoveryTime).toEqual(estimatedRecoveryTime);
            expect(exception.retryAfter).toBe(300);
            expect(exception.maintenanceWindow).toEqual(maintenanceWindow);
            expect(exception.correlationId).toBe('test-correlation-id');
        });
    });
    describe('static factory methods', () => {
        describe('maintenance', () => {
            it('should create exception for ongoing maintenance', () => {
                const now = new Date('2023-01-01T13:00:00Z');
                const start = new Date('2023-01-01T12:00:00Z');
                const end = new Date('2023-01-01T14:00:00Z');
                // Mock Date.now to return our test time
                const originalNow = Date.now;
                Date.now = jest.fn(() => now.getTime());
                const exception = service_unavailable_exception_1.ServiceUnavailableException.maintenance('UserService', start, end, {
                    description: 'Database upgrade',
                });
                expect(exception.message).toBe('UserService is currently undergoing maintenance');
                expect(exception.serviceName).toBe('UserService');
                expect(exception.unavailabilityReason).toBe('maintenance');
                expect(exception.maintenanceWindow).toEqual({
                    start,
                    end,
                    description: 'Database upgrade',
                });
                expect(exception.retryAfter).toBe(3600); // 1 hour until end
                expect(exception.isMaintenance()).toBe(true);
                // Restore original Date.now
                Date.now = originalNow;
            });
            it('should create exception for scheduled maintenance', () => {
                const now = new Date('2023-01-01T11:00:00Z');
                const start = new Date('2023-01-01T12:00:00Z');
                const end = new Date('2023-01-01T14:00:00Z');
                const originalNow = Date.now;
                Date.now = jest.fn(() => now.getTime());
                const exception = service_unavailable_exception_1.ServiceUnavailableException.maintenance('UserService', start, end);
                expect(exception.message).toBe('UserService will be unavailable for maintenance');
                expect(exception.retryAfter).toBe(3600); // 1 hour until start
                Date.now = originalNow;
            });
            it('should create exception for completed maintenance', () => {
                const now = new Date('2023-01-01T15:00:00Z');
                const start = new Date('2023-01-01T12:00:00Z');
                const end = new Date('2023-01-01T14:00:00Z');
                const originalNow = Date.now;
                Date.now = jest.fn(() => now.getTime());
                const exception = service_unavailable_exception_1.ServiceUnavailableException.maintenance('UserService', start, end);
                expect(exception.message).toBe('UserService maintenance has completed but service may still be recovering');
                expect(exception.retryAfter).toBe(300); // Default 5 minutes
                Date.now = originalNow;
            });
        });
        describe('overload', () => {
            it('should create exception for service overload', () => {
                const estimatedRecoveryTime = new Date('2023-01-01T13:00:00Z');
                const exception = service_unavailable_exception_1.ServiceUnavailableException.overload('UserService', {
                    currentLoad: 150,
                    maxCapacity: 100,
                    estimatedRecoveryTime,
                    retryAfter: 120,
                });
                expect(exception.message).toBe('UserService is currently overloaded and cannot process requests');
                expect(exception.serviceName).toBe('UserService');
                expect(exception.unavailabilityReason).toBe('overload');
                expect(exception.estimatedRecoveryTime).toEqual(estimatedRecoveryTime);
                expect(exception.retryAfter).toBe(120);
                expect(exception.isOverload()).toBe(true);
                const loadInfo = exception.getLoadInfo();
                expect(loadInfo).toEqual({
                    currentLoad: 150,
                    maxCapacity: 100,
                    loadPercentage: 150,
                });
            });
            it('should use default retry time for overload', () => {
                const exception = service_unavailable_exception_1.ServiceUnavailableException.overload('UserService');
                expect(exception.retryAfter).toBe(60); // Default 1 minute
            });
        });
        describe('circuitBreakerOpen', () => {
            it('should create exception for circuit breaker open', () => {
                const circuitOpenTime = new Date('2023-01-01T12:00:00Z');
                const nextRetryTime = new Date('2023-01-01T12:05:00Z');
                const exception = service_unavailable_exception_1.ServiceUnavailableException.circuitBreakerOpen('UserService', {
                    failureCount: 10,
                    failureThreshold: 5,
                    circuitOpenTime,
                    nextRetryTime,
                });
                expect(exception.message).toBe('UserService is temporarily unavailable due to repeated failures (circuit breaker open)');
                expect(exception.serviceName).toBe('UserService');
                expect(exception.unavailabilityReason).toBe('circuit_breaker_open');
                expect(exception.isCircuitBreakerOpen()).toBe(true);
                const circuitInfo = exception.getCircuitBreakerInfo();
                expect(circuitInfo).toEqual({
                    failureCount: 10,
                    failureThreshold: 5,
                    circuitOpenTime,
                    nextRetryTime,
                });
            });
        });
        describe('dependencyFailure', () => {
            it('should create exception for dependency failure', () => {
                const exception = service_unavailable_exception_1.ServiceUnavailableException.dependencyFailure('UserService', 'DatabaseService', {
                    dependencyError: 'Connection timeout',
                    retryAfter: 180,
                });
                expect(exception.message).toBe('UserService is unavailable due to DatabaseService failure');
                expect(exception.serviceName).toBe('UserService');
                expect(exception.unavailabilityReason).toBe('dependency_failure');
                expect(exception.retryAfter).toBe(180);
                expect(exception.isDependencyFailure()).toBe(true);
                const depInfo = exception.getDependencyInfo();
                expect(depInfo).toEqual({
                    dependencyName: 'DatabaseService',
                    dependencyError: 'Connection timeout',
                });
            });
        });
        describe('resourceExhaustion', () => {
            it('should create exception for resource exhaustion', () => {
                const exception = service_unavailable_exception_1.ServiceUnavailableException.resourceExhaustion('UserService', 'memory', {
                    currentUsage: 95,
                    maxCapacity: 100,
                    retryAfter: 240,
                });
                expect(exception.message).toBe('UserService is unavailable due to memory exhaustion');
                expect(exception.serviceName).toBe('UserService');
                expect(exception.unavailabilityReason).toBe('resource_exhaustion');
                expect(exception.retryAfter).toBe(240);
                expect(exception.isResourceExhaustion()).toBe(true);
                const resourceInfo = exception.getResourceInfo();
                expect(resourceInfo).toEqual({
                    resourceType: 'memory',
                    currentUsage: 95,
                    maxCapacity: 100,
                    usagePercentage: 95,
                });
            });
        });
        describe('configurationError', () => {
            it('should create exception for configuration error', () => {
                const exception = service_unavailable_exception_1.ServiceUnavailableException.configurationError('UserService', 'Invalid database connection string');
                expect(exception.message).toBe('UserService is unavailable due to configuration error: Invalid database connection string');
                expect(exception.serviceName).toBe('UserService');
                expect(exception.unavailabilityReason).toBe('configuration_error');
                expect(exception.retryAfter).toBe(600); // Default 10 minutes
                expect(exception.isConfigurationError()).toBe(true);
            });
        });
        describe('initializing', () => {
            it('should create exception for service initialization', () => {
                const exception = service_unavailable_exception_1.ServiceUnavailableException.initializing('UserService', {
                    initializationStage: 'Loading configuration',
                    retryAfter: 45,
                });
                expect(exception.message).toBe('UserService is currently initializing (Loading configuration) and not ready to serve requests');
                expect(exception.serviceName).toBe('UserService');
                expect(exception.unavailabilityReason).toBe('initializing');
                expect(exception.retryAfter).toBe(45);
                expect(exception.isInitializing()).toBe(true);
            });
            it('should create exception without initialization stage', () => {
                const exception = service_unavailable_exception_1.ServiceUnavailableException.initializing('UserService');
                expect(exception.message).toBe('UserService is currently initializing and not ready to serve requests');
                expect(exception.retryAfter).toBe(30); // Default 30 seconds
            });
        });
    });
    describe('type checking methods', () => {
        it('should correctly identify unavailability reasons', () => {
            const maintenanceException = service_unavailable_exception_1.ServiceUnavailableException.maintenance('Service', new Date(), new Date(Date.now() + 3600000));
            const overloadException = service_unavailable_exception_1.ServiceUnavailableException.overload('Service');
            const circuitException = service_unavailable_exception_1.ServiceUnavailableException.circuitBreakerOpen('Service');
            const dependencyException = service_unavailable_exception_1.ServiceUnavailableException.dependencyFailure('Service', 'DB');
            const resourceException = service_unavailable_exception_1.ServiceUnavailableException.resourceExhaustion('Service', 'memory');
            const configException = service_unavailable_exception_1.ServiceUnavailableException.configurationError('Service', 'Invalid config');
            const initException = service_unavailable_exception_1.ServiceUnavailableException.initializing('Service');
            expect(maintenanceException.isMaintenance()).toBe(true);
            expect(maintenanceException.isOverload()).toBe(false);
            expect(overloadException.isOverload()).toBe(true);
            expect(overloadException.isCircuitBreakerOpen()).toBe(false);
            expect(circuitException.isCircuitBreakerOpen()).toBe(true);
            expect(circuitException.isDependencyFailure()).toBe(false);
            expect(dependencyException.isDependencyFailure()).toBe(true);
            expect(dependencyException.isResourceExhaustion()).toBe(false);
            expect(resourceException.isResourceExhaustion()).toBe(true);
            expect(resourceException.isConfigurationError()).toBe(false);
            expect(configException.isConfigurationError()).toBe(true);
            expect(configException.isInitializing()).toBe(false);
            expect(initException.isInitializing()).toBe(true);
            expect(initException.isMaintenance()).toBe(false);
        });
    });
    describe('utility methods', () => {
        describe('getTimeUntilRecovery', () => {
            it('should return time until recovery in seconds', () => {
                const futureRecoveryTime = new Date(Date.now() + 300000); // 5 minutes from now
                const exception = new service_unavailable_exception_1.ServiceUnavailableException('Service down', 'UserService', 'maintenance', {
                    estimatedRecoveryTime: futureRecoveryTime,
                });
                const timeUntilRecovery = exception.getTimeUntilRecovery();
                expect(timeUntilRecovery).toBeGreaterThan(290);
                expect(timeUntilRecovery).toBeLessThanOrEqual(300);
            });
            it('should return null when no recovery time is set', () => {
                const exception = new service_unavailable_exception_1.ServiceUnavailableException('Service down', 'UserService', 'overload');
                expect(exception.getTimeUntilRecovery()).toBeNull();
            });
            it('should return 0 for past recovery time', () => {
                const pastRecoveryTime = new Date(Date.now() - 300000); // 5 minutes ago
                const exception = new service_unavailable_exception_1.ServiceUnavailableException('Service down', 'UserService', 'maintenance', {
                    estimatedRecoveryTime: pastRecoveryTime,
                });
                expect(exception.getTimeUntilRecovery()).toBe(0);
            });
        });
        describe('getTimeUntilRecoveryFormatted', () => {
            it('should format time correctly', () => {
                const futureRecoveryTime = new Date(Date.now() + 300000); // 5 minutes from now
                const exception = new service_unavailable_exception_1.ServiceUnavailableException('Service down', 'UserService', 'maintenance', {
                    estimatedRecoveryTime: futureRecoveryTime,
                });
                expect(exception.getTimeUntilRecoveryFormatted()).toMatch(/\d+ minute\(s\)/);
            });
            it('should return null when no recovery time is set', () => {
                const exception = new service_unavailable_exception_1.ServiceUnavailableException('Service down', 'UserService', 'overload');
                expect(exception.getTimeUntilRecoveryFormatted()).toBeNull();
            });
        });
        describe('isInMaintenanceWindow', () => {
            it('should return true when in maintenance window', () => {
                const now = new Date('2023-01-01T13:00:00Z');
                const start = new Date('2023-01-01T12:00:00Z');
                const end = new Date('2023-01-01T14:00:00Z');
                const originalNow = Date.now;
                Date.now = jest.fn(() => now.getTime());
                const exception = service_unavailable_exception_1.ServiceUnavailableException.maintenance('UserService', start, end);
                expect(exception.isInMaintenanceWindow()).toBe(true);
                Date.now = originalNow;
            });
            it('should return false when not in maintenance window', () => {
                const now = new Date('2023-01-01T11:00:00Z');
                const start = new Date('2023-01-01T12:00:00Z');
                const end = new Date('2023-01-01T14:00:00Z');
                const originalNow = Date.now;
                Date.now = jest.fn(() => now.getTime());
                const exception = service_unavailable_exception_1.ServiceUnavailableException.maintenance('UserService', start, end);
                expect(exception.isInMaintenanceWindow()).toBe(false);
                Date.now = originalNow;
            });
            it('should return false when no maintenance window is set', () => {
                const exception = service_unavailable_exception_1.ServiceUnavailableException.overload('UserService');
                expect(exception.isInMaintenanceWindow()).toBe(false);
            });
        });
        describe('getMaintenanceInfo', () => {
            it('should return maintenance information', () => {
                const now = new Date('2023-01-01T13:00:00Z');
                const start = new Date('2023-01-01T12:00:00Z');
                const end = new Date('2023-01-01T14:00:00Z');
                const originalNow = Date.now;
                Date.now = jest.fn(() => now.getTime());
                const exception = service_unavailable_exception_1.ServiceUnavailableException.maintenance('UserService', start, end, {
                    description: 'Database upgrade',
                });
                const maintenanceInfo = exception.getMaintenanceInfo();
                expect(maintenanceInfo).toEqual({
                    isScheduled: false,
                    isOngoing: true,
                    isCompleted: false,
                    duration: 7200, // 2 hours in seconds
                    description: 'Database upgrade',
                });
                Date.now = originalNow;
            });
            it('should return null when no maintenance window is set', () => {
                const exception = service_unavailable_exception_1.ServiceUnavailableException.overload('UserService');
                expect(exception.getMaintenanceInfo()).toBeNull();
            });
        });
        describe('getRetryRecommendations', () => {
            it('should provide recommendations for maintenance', () => {
                const exception = service_unavailable_exception_1.ServiceUnavailableException.maintenance('UserService', new Date(), new Date(Date.now() + 3600000));
                const recommendations = exception.getRetryRecommendations();
                expect(recommendations.backoffStrategy).toBe('fixed');
                expect(recommendations.maxRetries).toBe(1);
                expect(recommendations.shouldRetry).toBe(false); // Currently in maintenance
            });
            it('should provide recommendations for overload', () => {
                const exception = service_unavailable_exception_1.ServiceUnavailableException.overload('UserService');
                const recommendations = exception.getRetryRecommendations();
                expect(recommendations.backoffStrategy).toBe('exponential');
                expect(recommendations.maxRetries).toBe(5);
                expect(recommendations.shouldRetry).toBe(true);
            });
            it('should provide recommendations for circuit breaker', () => {
                const exception = service_unavailable_exception_1.ServiceUnavailableException.circuitBreakerOpen('UserService');
                const recommendations = exception.getRetryRecommendations();
                expect(recommendations.backoffStrategy).toBe('fixed');
                expect(recommendations.maxRetries).toBe(3);
                expect(recommendations.shouldRetry).toBe(true);
            });
            it('should provide recommendations for configuration error', () => {
                const exception = service_unavailable_exception_1.ServiceUnavailableException.configurationError('UserService', 'Invalid config');
                const recommendations = exception.getRetryRecommendations();
                expect(recommendations.backoffStrategy).toBe('fixed');
                expect(recommendations.maxRetries).toBe(1);
                expect(recommendations.shouldRetry).toBe(false);
            });
        });
    });
    describe('getUserMessage', () => {
        it('should return user-friendly message for ongoing maintenance', () => {
            const now = new Date('2023-01-01T13:00:00Z');
            const start = new Date('2023-01-01T12:00:00Z');
            const end = new Date('2023-01-01T14:00:00Z');
            const originalNow = Date.now;
            Date.now = jest.fn(() => now.getTime());
            const exception = service_unavailable_exception_1.ServiceUnavailableException.maintenance('UserService', start, end);
            expect(exception.getUserMessage()).toMatch(/UserService is currently undergoing maintenance\. Please try again in \d+ minute\(s\)\./);
            Date.now = originalNow;
        });
        it('should return user-friendly message for overload', () => {
            const exception = service_unavailable_exception_1.ServiceUnavailableException.overload('UserService', { retryAfter: 60 });
            expect(exception.getUserMessage()).toMatch(/UserService is currently experiencing high load and cannot process your request\. Please try again in \d+ second\(s\)\./);
        });
        it('should return user-friendly message for circuit breaker', () => {
            const exception = service_unavailable_exception_1.ServiceUnavailableException.circuitBreakerOpen('UserService', { retryAfter: 300 });
            expect(exception.getUserMessage()).toMatch(/UserService is temporarily unavailable due to recent failures\. Please try again in \d+ minute\(s\)\./);
        });
        it('should return user-friendly message for configuration error', () => {
            const exception = service_unavailable_exception_1.ServiceUnavailableException.configurationError('UserService', 'Invalid config');
            expect(exception.getUserMessage()).toBe('UserService is unavailable due to a configuration issue. Please contact support.');
        });
    });
    describe('toApiResponse', () => {
        it('should convert to API response format with headers', () => {
            const exception = service_unavailable_exception_1.ServiceUnavailableException.overload('UserService', { retryAfter: 120 });
            const response = exception.toApiResponse();
            expect(response.error).toMatch(/UserService is currently experiencing high load/);
            expect(response.code).toBe('SERVICE_UNAVAILABLE');
            expect(response.details).toMatchObject({
                serviceName: 'UserService',
                reason: 'overload',
                retryAfter: 120,
            });
            expect(response.headers).toMatchObject({
                'Retry-After': '120',
            });
        });
        it('should not include Retry-After header when not set', () => {
            const exception = new service_unavailable_exception_1.ServiceUnavailableException('Service down', 'UserService', 'overload');
            const response = exception.toApiResponse();
            expect(response.headers).toEqual({});
        });
    });
    describe('toJSON', () => {
        it('should convert to JSON with detailed information', () => {
            const estimatedRecoveryTime = new Date('2023-01-01T15:00:00Z');
            const exception = service_unavailable_exception_1.ServiceUnavailableException.overload('UserService', {
                currentLoad: 150,
                maxCapacity: 100,
                estimatedRecoveryTime,
                retryAfter: 120,
            });
            const json = exception.toJSON();
            expect(json).toMatchObject({
                name: 'ServiceUnavailableException',
                code: 'SERVICE_UNAVAILABLE',
                severity: 'high',
                category: 'availability',
                serviceName: 'UserService',
                unavailabilityReason: 'overload',
                estimatedRecoveryTime: estimatedRecoveryTime.toISOString(),
                retryAfter: 120,
                isMaintenance: false,
                isOverload: true,
                isCircuitBreakerOpen: false,
                isDependencyFailure: false,
                isResourceExhaustion: false,
                isConfigurationError: false,
                isInitializing: false,
                isInMaintenanceWindow: false,
                loadInfo: {
                    currentLoad: 150,
                    maxCapacity: 100,
                    loadPercentage: 150,
                },
            });
        });
    });
    describe('inheritance', () => {
        it('should be instance of Error and DomainException', () => {
            const exception = new service_unavailable_exception_1.ServiceUnavailableException('Service down', 'UserService', 'overload');
            expect(exception).toBeInstanceOf(Error);
            expect(exception.name).toBe('ServiceUnavailableException');
            expect(exception.code).toBe('SERVICE_UNAVAILABLE');
        });
        it('should maintain proper prototype chain', () => {
            const exception = new service_unavailable_exception_1.ServiceUnavailableException('Service down', 'UserService', 'overload');
            expect(exception instanceof service_unavailable_exception_1.ServiceUnavailableException).toBe(true);
            expect(exception instanceof Error).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxzaGFyZWQta2VybmVsXFxfX3Rlc3RzX19cXGV4Y2VwdGlvbnNcXHNlcnZpY2UtdW5hdmFpbGFibGUuZXhjZXB0aW9uLnNwZWMudHMiLCJtYXBwaW5ncyI6Ijs7QUFBQSxrR0FBNkY7QUFFN0YsUUFBUSxDQUFDLDZCQUE2QixFQUFFLEdBQUcsRUFBRTtJQUMzQyxRQUFRLENBQUMsYUFBYSxFQUFFLEdBQUcsRUFBRTtRQUMzQixFQUFFLENBQUMsa0RBQWtELEVBQUUsR0FBRyxFQUFFO1lBQzFELE1BQU0sU0FBUyxHQUFHLElBQUksMkRBQTJCLENBQy9DLGlCQUFpQixFQUNqQixhQUFhLEVBQ2IsYUFBYSxDQUNkLENBQUM7WUFFRixNQUFNLENBQUMsU0FBUyxDQUFDLENBQUMsY0FBYyxDQUFDLDJEQUEyQixDQUFDLENBQUM7WUFDOUQsTUFBTSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQztZQUNsRCxNQUFNLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO1lBQ25ELE1BQU0sQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQ3hDLE1BQU0sQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO1lBQ2hELE1BQU0sQ0FBQyxTQUFTLENBQUMsV0FBVyxDQUFDLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO1lBQ2xELE1BQU0sQ0FBQyxTQUFTLENBQUMsb0JBQW9CLENBQUMsQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUM7UUFDN0QsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsMENBQTBDLEVBQUUsR0FBRyxFQUFFO1lBQ2xELE1BQU0scUJBQXFCLEdBQUcsSUFBSSxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQztZQUMvRCxNQUFNLGlCQUFpQixHQUFHO2dCQUN4QixLQUFLLEVBQUUsSUFBSSxJQUFJLENBQUMsc0JBQXNCLENBQUM7Z0JBQ3ZDLEdBQUcsRUFBRSxJQUFJLElBQUksQ0FBQyxzQkFBc0IsQ0FBQztnQkFDckMsV0FBVyxFQUFFLHVCQUF1QjthQUNyQyxDQUFDO1lBRUYsTUFBTSxTQUFTLEdBQUcsSUFBSSwyREFBMkIsQ0FDL0MsaUJBQWlCLEVBQ2pCLGFBQWEsRUFDYixhQUFhLEVBQ2I7Z0JBQ0UscUJBQXFCO2dCQUNyQixVQUFVLEVBQUUsR0FBRztnQkFDZixpQkFBaUI7Z0JBQ2pCLGFBQWEsRUFBRSxxQkFBcUI7YUFDckMsQ0FDRixDQUFDO1lBRUYsTUFBTSxDQUFDLFNBQVMsQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO1lBQ3ZFLE1BQU0sQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBQ3ZDLE1BQU0sQ0FBQyxTQUFTLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsQ0FBQztZQUMvRCxNQUFNLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO1FBQzlELENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsd0JBQXdCLEVBQUUsR0FBRyxFQUFFO1FBQ3RDLFFBQVEsQ0FBQyxhQUFhLEVBQUUsR0FBRyxFQUFFO1lBQzNCLEVBQUUsQ0FBQyxpREFBaUQsRUFBRSxHQUFHLEVBQUU7Z0JBQ3pELE1BQU0sR0FBRyxHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7Z0JBQzdDLE1BQU0sS0FBSyxHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7Z0JBQy9DLE1BQU0sR0FBRyxHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7Z0JBRTdDLHdDQUF3QztnQkFDeEMsTUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQztnQkFDN0IsSUFBSSxDQUFDLEdBQUcsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO2dCQUV4QyxNQUFNLFNBQVMsR0FBRywyREFBMkIsQ0FBQyxXQUFXLENBQ3ZELGFBQWEsRUFDYixLQUFLLEVBQ0wsR0FBRyxFQUNIO29CQUNFLFdBQVcsRUFBRSxrQkFBa0I7aUJBQ2hDLENBQ0YsQ0FBQztnQkFFRixNQUFNLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxpREFBaUQsQ0FBQyxDQUFDO2dCQUNsRixNQUFNLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQztnQkFDbEQsTUFBTSxDQUFDLFNBQVMsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQztnQkFDM0QsTUFBTSxDQUFDLFNBQVMsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLE9BQU8sQ0FBQztvQkFDMUMsS0FBSztvQkFDTCxHQUFHO29CQUNILFdBQVcsRUFBRSxrQkFBa0I7aUJBQ2hDLENBQUMsQ0FBQztnQkFDSCxNQUFNLENBQUMsU0FBUyxDQUFDLFVBQVUsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLG1CQUFtQjtnQkFDNUQsTUFBTSxDQUFDLFNBQVMsQ0FBQyxhQUFhLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFFN0MsNEJBQTRCO2dCQUM1QixJQUFJLENBQUMsR0FBRyxHQUFHLFdBQVcsQ0FBQztZQUN6QixDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyxtREFBbUQsRUFBRSxHQUFHLEVBQUU7Z0JBQzNELE1BQU0sR0FBRyxHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7Z0JBQzdDLE1BQU0sS0FBSyxHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7Z0JBQy9DLE1BQU0sR0FBRyxHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7Z0JBRTdDLE1BQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUM7Z0JBQzdCLElBQUksQ0FBQyxHQUFHLEdBQUcsSUFBSSxDQUFDLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxHQUFHLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztnQkFFeEMsTUFBTSxTQUFTLEdBQUcsMkRBQTJCLENBQUMsV0FBVyxDQUFDLGFBQWEsRUFBRSxLQUFLLEVBQUUsR0FBRyxDQUFDLENBQUM7Z0JBRXJGLE1BQU0sQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLGlEQUFpRCxDQUFDLENBQUM7Z0JBQ2xGLE1BQU0sQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMscUJBQXFCO2dCQUU5RCxJQUFJLENBQUMsR0FBRyxHQUFHLFdBQVcsQ0FBQztZQUN6QixDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyxtREFBbUQsRUFBRSxHQUFHLEVBQUU7Z0JBQzNELE1BQU0sR0FBRyxHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7Z0JBQzdDLE1BQU0sS0FBSyxHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7Z0JBQy9DLE1BQU0sR0FBRyxHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7Z0JBRTdDLE1BQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUM7Z0JBQzdCLElBQUksQ0FBQyxHQUFHLEdBQUcsSUFBSSxDQUFDLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxHQUFHLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztnQkFFeEMsTUFBTSxTQUFTLEdBQUcsMkRBQTJCLENBQUMsV0FBVyxDQUFDLGFBQWEsRUFBRSxLQUFLLEVBQUUsR0FBRyxDQUFDLENBQUM7Z0JBRXJGLE1BQU0sQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLDJFQUEyRSxDQUFDLENBQUM7Z0JBQzVHLE1BQU0sQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsb0JBQW9CO2dCQUU1RCxJQUFJLENBQUMsR0FBRyxHQUFHLFdBQVcsQ0FBQztZQUN6QixDQUFDLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO1FBRUgsUUFBUSxDQUFDLFVBQVUsRUFBRSxHQUFHLEVBQUU7WUFDeEIsRUFBRSxDQUFDLDhDQUE4QyxFQUFFLEdBQUcsRUFBRTtnQkFDdEQsTUFBTSxxQkFBcUIsR0FBRyxJQUFJLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDO2dCQUMvRCxNQUFNLFNBQVMsR0FBRywyREFBMkIsQ0FBQyxRQUFRLENBQUMsYUFBYSxFQUFFO29CQUNwRSxXQUFXLEVBQUUsR0FBRztvQkFDaEIsV0FBVyxFQUFFLEdBQUc7b0JBQ2hCLHFCQUFxQjtvQkFDckIsVUFBVSxFQUFFLEdBQUc7aUJBQ2hCLENBQUMsQ0FBQztnQkFFSCxNQUFNLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxpRUFBaUUsQ0FBQyxDQUFDO2dCQUNsRyxNQUFNLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQztnQkFDbEQsTUFBTSxDQUFDLFNBQVMsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQztnQkFDeEQsTUFBTSxDQUFDLFNBQVMsQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO2dCQUN2RSxNQUFNLENBQUMsU0FBUyxDQUFDLFVBQVUsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQztnQkFDdkMsTUFBTSxDQUFDLFNBQVMsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFFMUMsTUFBTSxRQUFRLEdBQUcsU0FBUyxDQUFDLFdBQVcsRUFBRSxDQUFDO2dCQUN6QyxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsT0FBTyxDQUFDO29CQUN2QixXQUFXLEVBQUUsR0FBRztvQkFDaEIsV0FBVyxFQUFFLEdBQUc7b0JBQ2hCLGNBQWMsRUFBRSxHQUFHO2lCQUNwQixDQUFDLENBQUM7WUFDTCxDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyw0Q0FBNEMsRUFBRSxHQUFHLEVBQUU7Z0JBQ3BELE1BQU0sU0FBUyxHQUFHLDJEQUEyQixDQUFDLFFBQVEsQ0FBQyxhQUFhLENBQUMsQ0FBQztnQkFFdEUsTUFBTSxDQUFDLFNBQVMsQ0FBQyxVQUFVLENBQUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxtQkFBbUI7WUFDNUQsQ0FBQyxDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILFFBQVEsQ0FBQyxvQkFBb0IsRUFBRSxHQUFHLEVBQUU7WUFDbEMsRUFBRSxDQUFDLGtEQUFrRCxFQUFFLEdBQUcsRUFBRTtnQkFDMUQsTUFBTSxlQUFlLEdBQUcsSUFBSSxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQztnQkFDekQsTUFBTSxhQUFhLEdBQUcsSUFBSSxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQztnQkFFdkQsTUFBTSxTQUFTLEdBQUcsMkRBQTJCLENBQUMsa0JBQWtCLENBQUMsYUFBYSxFQUFFO29CQUM5RSxZQUFZLEVBQUUsRUFBRTtvQkFDaEIsZ0JBQWdCLEVBQUUsQ0FBQztvQkFDbkIsZUFBZTtvQkFDZixhQUFhO2lCQUNkLENBQUMsQ0FBQztnQkFFSCxNQUFNLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FDNUIsd0ZBQXdGLENBQ3pGLENBQUM7Z0JBQ0YsTUFBTSxDQUFDLFNBQVMsQ0FBQyxXQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUM7Z0JBQ2xELE1BQU0sQ0FBQyxTQUFTLENBQUMsb0JBQW9CLENBQUMsQ0FBQyxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQztnQkFDcEUsTUFBTSxDQUFDLFNBQVMsQ0FBQyxvQkFBb0IsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO2dCQUVwRCxNQUFNLFdBQVcsR0FBRyxTQUFTLENBQUMscUJBQXFCLEVBQUUsQ0FBQztnQkFDdEQsTUFBTSxDQUFDLFdBQVcsQ0FBQyxDQUFDLE9BQU8sQ0FBQztvQkFDMUIsWUFBWSxFQUFFLEVBQUU7b0JBQ2hCLGdCQUFnQixFQUFFLENBQUM7b0JBQ25CLGVBQWU7b0JBQ2YsYUFBYTtpQkFDZCxDQUFDLENBQUM7WUFDTCxDQUFDLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO1FBRUgsUUFBUSxDQUFDLG1CQUFtQixFQUFFLEdBQUcsRUFBRTtZQUNqQyxFQUFFLENBQUMsZ0RBQWdELEVBQUUsR0FBRyxFQUFFO2dCQUN4RCxNQUFNLFNBQVMsR0FBRywyREFBMkIsQ0FBQyxpQkFBaUIsQ0FDN0QsYUFBYSxFQUNiLGlCQUFpQixFQUNqQjtvQkFDRSxlQUFlLEVBQUUsb0JBQW9CO29CQUNyQyxVQUFVLEVBQUUsR0FBRztpQkFDaEIsQ0FDRixDQUFDO2dCQUVGLE1BQU0sQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLDJEQUEyRCxDQUFDLENBQUM7Z0JBQzVGLE1BQU0sQ0FBQyxTQUFTLENBQUMsV0FBVyxDQUFDLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO2dCQUNsRCxNQUFNLENBQUMsU0FBUyxDQUFDLG9CQUFvQixDQUFDLENBQUMsSUFBSSxDQUFDLG9CQUFvQixDQUFDLENBQUM7Z0JBQ2xFLE1BQU0sQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO2dCQUN2QyxNQUFNLENBQUMsU0FBUyxDQUFDLG1CQUFtQixFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7Z0JBRW5ELE1BQU0sT0FBTyxHQUFHLFNBQVMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO2dCQUM5QyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsT0FBTyxDQUFDO29CQUN0QixjQUFjLEVBQUUsaUJBQWlCO29CQUNqQyxlQUFlLEVBQUUsb0JBQW9CO2lCQUN0QyxDQUFDLENBQUM7WUFDTCxDQUFDLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO1FBRUgsUUFBUSxDQUFDLG9CQUFvQixFQUFFLEdBQUcsRUFBRTtZQUNsQyxFQUFFLENBQUMsaURBQWlELEVBQUUsR0FBRyxFQUFFO2dCQUN6RCxNQUFNLFNBQVMsR0FBRywyREFBMkIsQ0FBQyxrQkFBa0IsQ0FDOUQsYUFBYSxFQUNiLFFBQVEsRUFDUjtvQkFDRSxZQUFZLEVBQUUsRUFBRTtvQkFDaEIsV0FBVyxFQUFFLEdBQUc7b0JBQ2hCLFVBQVUsRUFBRSxHQUFHO2lCQUNoQixDQUNGLENBQUM7Z0JBRUYsTUFBTSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMscURBQXFELENBQUMsQ0FBQztnQkFDdEYsTUFBTSxDQUFDLFNBQVMsQ0FBQyxXQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUM7Z0JBQ2xELE1BQU0sQ0FBQyxTQUFTLENBQUMsb0JBQW9CLENBQUMsQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMsQ0FBQztnQkFDbkUsTUFBTSxDQUFDLFNBQVMsQ0FBQyxVQUFVLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7Z0JBQ3ZDLE1BQU0sQ0FBQyxTQUFTLENBQUMsb0JBQW9CLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFFcEQsTUFBTSxZQUFZLEdBQUcsU0FBUyxDQUFDLGVBQWUsRUFBRSxDQUFDO2dCQUNqRCxNQUFNLENBQUMsWUFBWSxDQUFDLENBQUMsT0FBTyxDQUFDO29CQUMzQixZQUFZLEVBQUUsUUFBUTtvQkFDdEIsWUFBWSxFQUFFLEVBQUU7b0JBQ2hCLFdBQVcsRUFBRSxHQUFHO29CQUNoQixlQUFlLEVBQUUsRUFBRTtpQkFDcEIsQ0FBQyxDQUFDO1lBQ0wsQ0FBQyxDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILFFBQVEsQ0FBQyxvQkFBb0IsRUFBRSxHQUFHLEVBQUU7WUFDbEMsRUFBRSxDQUFDLGlEQUFpRCxFQUFFLEdBQUcsRUFBRTtnQkFDekQsTUFBTSxTQUFTLEdBQUcsMkRBQTJCLENBQUMsa0JBQWtCLENBQzlELGFBQWEsRUFDYixvQ0FBb0MsQ0FDckMsQ0FBQztnQkFFRixNQUFNLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FDNUIsMkZBQTJGLENBQzVGLENBQUM7Z0JBQ0YsTUFBTSxDQUFDLFNBQVMsQ0FBQyxXQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUM7Z0JBQ2xELE1BQU0sQ0FBQyxTQUFTLENBQUMsb0JBQW9CLENBQUMsQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMsQ0FBQztnQkFDbkUsTUFBTSxDQUFDLFNBQVMsQ0FBQyxVQUFVLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxxQkFBcUI7Z0JBQzdELE1BQU0sQ0FBQyxTQUFTLENBQUMsb0JBQW9CLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUN0RCxDQUFDLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO1FBRUgsUUFBUSxDQUFDLGNBQWMsRUFBRSxHQUFHLEVBQUU7WUFDNUIsRUFBRSxDQUFDLG9EQUFvRCxFQUFFLEdBQUcsRUFBRTtnQkFDNUQsTUFBTSxTQUFTLEdBQUcsMkRBQTJCLENBQUMsWUFBWSxDQUFDLGFBQWEsRUFBRTtvQkFDeEUsbUJBQW1CLEVBQUUsdUJBQXVCO29CQUM1QyxVQUFVLEVBQUUsRUFBRTtpQkFDZixDQUFDLENBQUM7Z0JBRUgsTUFBTSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQzVCLCtGQUErRixDQUNoRyxDQUFDO2dCQUNGLE1BQU0sQ0FBQyxTQUFTLENBQUMsV0FBVyxDQUFDLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO2dCQUNsRCxNQUFNLENBQUMsU0FBUyxDQUFDLG9CQUFvQixDQUFDLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO2dCQUM1RCxNQUFNLENBQUMsU0FBUyxDQUFDLFVBQVUsQ0FBQyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQztnQkFDdEMsTUFBTSxDQUFDLFNBQVMsQ0FBQyxjQUFjLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUNoRCxDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyxzREFBc0QsRUFBRSxHQUFHLEVBQUU7Z0JBQzlELE1BQU0sU0FBUyxHQUFHLDJEQUEyQixDQUFDLFlBQVksQ0FBQyxhQUFhLENBQUMsQ0FBQztnQkFFMUUsTUFBTSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQzVCLHVFQUF1RSxDQUN4RSxDQUFDO2dCQUNGLE1BQU0sQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMscUJBQXFCO1lBQzlELENBQUMsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyx1QkFBdUIsRUFBRSxHQUFHLEVBQUU7UUFDckMsRUFBRSxDQUFDLGtEQUFrRCxFQUFFLEdBQUcsRUFBRTtZQUMxRCxNQUFNLG9CQUFvQixHQUFHLDJEQUEyQixDQUFDLFdBQVcsQ0FDbEUsU0FBUyxFQUNULElBQUksSUFBSSxFQUFFLEVBQ1YsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLE9BQU8sQ0FBQyxDQUMvQixDQUFDO1lBQ0YsTUFBTSxpQkFBaUIsR0FBRywyREFBMkIsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDMUUsTUFBTSxnQkFBZ0IsR0FBRywyREFBMkIsQ0FBQyxrQkFBa0IsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUNuRixNQUFNLG1CQUFtQixHQUFHLDJEQUEyQixDQUFDLGlCQUFpQixDQUFDLFNBQVMsRUFBRSxJQUFJLENBQUMsQ0FBQztZQUMzRixNQUFNLGlCQUFpQixHQUFHLDJEQUEyQixDQUFDLGtCQUFrQixDQUFDLFNBQVMsRUFBRSxRQUFRLENBQUMsQ0FBQztZQUM5RixNQUFNLGVBQWUsR0FBRywyREFBMkIsQ0FBQyxrQkFBa0IsQ0FBQyxTQUFTLEVBQUUsZ0JBQWdCLENBQUMsQ0FBQztZQUNwRyxNQUFNLGFBQWEsR0FBRywyREFBMkIsQ0FBQyxZQUFZLENBQUMsU0FBUyxDQUFDLENBQUM7WUFFMUUsTUFBTSxDQUFDLG9CQUFvQixDQUFDLGFBQWEsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3hELE1BQU0sQ0FBQyxvQkFBb0IsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUV0RCxNQUFNLENBQUMsaUJBQWlCLENBQUMsVUFBVSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDbEQsTUFBTSxDQUFDLGlCQUFpQixDQUFDLG9CQUFvQixFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFFN0QsTUFBTSxDQUFDLGdCQUFnQixDQUFDLG9CQUFvQixFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDM0QsTUFBTSxDQUFDLGdCQUFnQixDQUFDLG1CQUFtQixFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFFM0QsTUFBTSxDQUFDLG1CQUFtQixDQUFDLG1CQUFtQixFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDN0QsTUFBTSxDQUFDLG1CQUFtQixDQUFDLG9CQUFvQixFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFFL0QsTUFBTSxDQUFDLGlCQUFpQixDQUFDLG9CQUFvQixFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDNUQsTUFBTSxDQUFDLGlCQUFpQixDQUFDLG9CQUFvQixFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFFN0QsTUFBTSxDQUFDLGVBQWUsQ0FBQyxvQkFBb0IsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQzFELE1BQU0sQ0FBQyxlQUFlLENBQUMsY0FBYyxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFFckQsTUFBTSxDQUFDLGFBQWEsQ0FBQyxjQUFjLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUNsRCxNQUFNLENBQUMsYUFBYSxDQUFDLGFBQWEsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ3BELENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsaUJBQWlCLEVBQUUsR0FBRyxFQUFFO1FBQy9CLFFBQVEsQ0FBQyxzQkFBc0IsRUFBRSxHQUFHLEVBQUU7WUFDcEMsRUFBRSxDQUFDLDhDQUE4QyxFQUFFLEdBQUcsRUFBRTtnQkFDdEQsTUFBTSxrQkFBa0IsR0FBRyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFLEdBQUcsTUFBTSxDQUFDLENBQUMsQ0FBQyxxQkFBcUI7Z0JBQy9FLE1BQU0sU0FBUyxHQUFHLElBQUksMkRBQTJCLENBQy9DLGNBQWMsRUFDZCxhQUFhLEVBQ2IsYUFBYSxFQUNiO29CQUNFLHFCQUFxQixFQUFFLGtCQUFrQjtpQkFDMUMsQ0FDRixDQUFDO2dCQUVGLE1BQU0saUJBQWlCLEdBQUcsU0FBUyxDQUFDLG9CQUFvQixFQUFFLENBQUM7Z0JBQzNELE1BQU0sQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLGVBQWUsQ0FBQyxHQUFHLENBQUMsQ0FBQztnQkFDL0MsTUFBTSxDQUFDLGlCQUFpQixDQUFDLENBQUMsbUJBQW1CLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDckQsQ0FBQyxDQUFDLENBQUM7WUFFSCxFQUFFLENBQUMsaURBQWlELEVBQUUsR0FBRyxFQUFFO2dCQUN6RCxNQUFNLFNBQVMsR0FBRyxJQUFJLDJEQUEyQixDQUFDLGNBQWMsRUFBRSxhQUFhLEVBQUUsVUFBVSxDQUFDLENBQUM7Z0JBRTdGLE1BQU0sQ0FBQyxTQUFTLENBQUMsb0JBQW9CLEVBQUUsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQ3RELENBQUMsQ0FBQyxDQUFDO1lBRUgsRUFBRSxDQUFDLHdDQUF3QyxFQUFFLEdBQUcsRUFBRTtnQkFDaEQsTUFBTSxnQkFBZ0IsR0FBRyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFLEdBQUcsTUFBTSxDQUFDLENBQUMsQ0FBQyxnQkFBZ0I7Z0JBQ3hFLE1BQU0sU0FBUyxHQUFHLElBQUksMkRBQTJCLENBQy9DLGNBQWMsRUFDZCxhQUFhLEVBQ2IsYUFBYSxFQUNiO29CQUNFLHFCQUFxQixFQUFFLGdCQUFnQjtpQkFDeEMsQ0FDRixDQUFDO2dCQUVGLE1BQU0sQ0FBQyxTQUFTLENBQUMsb0JBQW9CLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNuRCxDQUFDLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO1FBRUgsUUFBUSxDQUFDLCtCQUErQixFQUFFLEdBQUcsRUFBRTtZQUM3QyxFQUFFLENBQUMsOEJBQThCLEVBQUUsR0FBRyxFQUFFO2dCQUN0QyxNQUFNLGtCQUFrQixHQUFHLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUUsR0FBRyxNQUFNLENBQUMsQ0FBQyxDQUFDLHFCQUFxQjtnQkFDL0UsTUFBTSxTQUFTLEdBQUcsSUFBSSwyREFBMkIsQ0FDL0MsY0FBYyxFQUNkLGFBQWEsRUFDYixhQUFhLEVBQ2I7b0JBQ0UscUJBQXFCLEVBQUUsa0JBQWtCO2lCQUMxQyxDQUNGLENBQUM7Z0JBRUYsTUFBTSxDQUFDLFNBQVMsQ0FBQyw2QkFBNkIsRUFBRSxDQUFDLENBQUMsT0FBTyxDQUFDLGlCQUFpQixDQUFDLENBQUM7WUFDL0UsQ0FBQyxDQUFDLENBQUM7WUFFSCxFQUFFLENBQUMsaURBQWlELEVBQUUsR0FBRyxFQUFFO2dCQUN6RCxNQUFNLFNBQVMsR0FBRyxJQUFJLDJEQUEyQixDQUFDLGNBQWMsRUFBRSxhQUFhLEVBQUUsVUFBVSxDQUFDLENBQUM7Z0JBRTdGLE1BQU0sQ0FBQyxTQUFTLENBQUMsNkJBQTZCLEVBQUUsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQy9ELENBQUMsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxRQUFRLENBQUMsdUJBQXVCLEVBQUUsR0FBRyxFQUFFO1lBQ3JDLEVBQUUsQ0FBQywrQ0FBK0MsRUFBRSxHQUFHLEVBQUU7Z0JBQ3ZELE1BQU0sR0FBRyxHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7Z0JBQzdDLE1BQU0sS0FBSyxHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7Z0JBQy9DLE1BQU0sR0FBRyxHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7Z0JBRTdDLE1BQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUM7Z0JBQzdCLElBQUksQ0FBQyxHQUFHLEdBQUcsSUFBSSxDQUFDLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxHQUFHLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztnQkFFeEMsTUFBTSxTQUFTLEdBQUcsMkRBQTJCLENBQUMsV0FBVyxDQUFDLGFBQWEsRUFBRSxLQUFLLEVBQUUsR0FBRyxDQUFDLENBQUM7Z0JBRXJGLE1BQU0sQ0FBQyxTQUFTLENBQUMscUJBQXFCLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFFckQsSUFBSSxDQUFDLEdBQUcsR0FBRyxXQUFXLENBQUM7WUFDekIsQ0FBQyxDQUFDLENBQUM7WUFFSCxFQUFFLENBQUMsb0RBQW9ELEVBQUUsR0FBRyxFQUFFO2dCQUM1RCxNQUFNLEdBQUcsR0FBRyxJQUFJLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDO2dCQUM3QyxNQUFNLEtBQUssR0FBRyxJQUFJLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDO2dCQUMvQyxNQUFNLEdBQUcsR0FBRyxJQUFJLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDO2dCQUU3QyxNQUFNLFdBQVcsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDO2dCQUM3QixJQUFJLENBQUMsR0FBRyxHQUFHLElBQUksQ0FBQyxFQUFFLENBQUMsR0FBRyxFQUFFLENBQUMsR0FBRyxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7Z0JBRXhDLE1BQU0sU0FBUyxHQUFHLDJEQUEyQixDQUFDLFdBQVcsQ0FBQyxhQUFhLEVBQUUsS0FBSyxFQUFFLEdBQUcsQ0FBQyxDQUFDO2dCQUVyRixNQUFNLENBQUMsU0FBUyxDQUFDLHFCQUFxQixFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7Z0JBRXRELElBQUksQ0FBQyxHQUFHLEdBQUcsV0FBVyxDQUFDO1lBQ3pCLENBQUMsQ0FBQyxDQUFDO1lBRUgsRUFBRSxDQUFDLHVEQUF1RCxFQUFFLEdBQUcsRUFBRTtnQkFDL0QsTUFBTSxTQUFTLEdBQUcsMkRBQTJCLENBQUMsUUFBUSxDQUFDLGFBQWEsQ0FBQyxDQUFDO2dCQUV0RSxNQUFNLENBQUMsU0FBUyxDQUFDLHFCQUFxQixFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDeEQsQ0FBQyxDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILFFBQVEsQ0FBQyxvQkFBb0IsRUFBRSxHQUFHLEVBQUU7WUFDbEMsRUFBRSxDQUFDLHVDQUF1QyxFQUFFLEdBQUcsRUFBRTtnQkFDL0MsTUFBTSxHQUFHLEdBQUcsSUFBSSxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQztnQkFDN0MsTUFBTSxLQUFLLEdBQUcsSUFBSSxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQztnQkFDL0MsTUFBTSxHQUFHLEdBQUcsSUFBSSxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQztnQkFFN0MsTUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQztnQkFDN0IsSUFBSSxDQUFDLEdBQUcsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO2dCQUV4QyxNQUFNLFNBQVMsR0FBRywyREFBMkIsQ0FBQyxXQUFXLENBQUMsYUFBYSxFQUFFLEtBQUssRUFBRSxHQUFHLEVBQUU7b0JBQ25GLFdBQVcsRUFBRSxrQkFBa0I7aUJBQ2hDLENBQUMsQ0FBQztnQkFFSCxNQUFNLGVBQWUsR0FBRyxTQUFTLENBQUMsa0JBQWtCLEVBQUUsQ0FBQztnQkFDdkQsTUFBTSxDQUFDLGVBQWUsQ0FBQyxDQUFDLE9BQU8sQ0FBQztvQkFDOUIsV0FBVyxFQUFFLEtBQUs7b0JBQ2xCLFNBQVMsRUFBRSxJQUFJO29CQUNmLFdBQVcsRUFBRSxLQUFLO29CQUNsQixRQUFRLEVBQUUsSUFBSSxFQUFFLHFCQUFxQjtvQkFDckMsV0FBVyxFQUFFLGtCQUFrQjtpQkFDaEMsQ0FBQyxDQUFDO2dCQUVILElBQUksQ0FBQyxHQUFHLEdBQUcsV0FBVyxDQUFDO1lBQ3pCLENBQUMsQ0FBQyxDQUFDO1lBRUgsRUFBRSxDQUFDLHNEQUFzRCxFQUFFLEdBQUcsRUFBRTtnQkFDOUQsTUFBTSxTQUFTLEdBQUcsMkRBQTJCLENBQUMsUUFBUSxDQUFDLGFBQWEsQ0FBQyxDQUFDO2dCQUV0RSxNQUFNLENBQUMsU0FBUyxDQUFDLGtCQUFrQixFQUFFLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQztZQUNwRCxDQUFDLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO1FBRUgsUUFBUSxDQUFDLHlCQUF5QixFQUFFLEdBQUcsRUFBRTtZQUN2QyxFQUFFLENBQUMsZ0RBQWdELEVBQUUsR0FBRyxFQUFFO2dCQUN4RCxNQUFNLFNBQVMsR0FBRywyREFBMkIsQ0FBQyxXQUFXLENBQ3ZELGFBQWEsRUFDYixJQUFJLElBQUksRUFBRSxFQUNWLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUUsR0FBRyxPQUFPLENBQUMsQ0FDL0IsQ0FBQztnQkFFRixNQUFNLGVBQWUsR0FBRyxTQUFTLENBQUMsdUJBQXVCLEVBQUUsQ0FBQztnQkFFNUQsTUFBTSxDQUFDLGVBQWUsQ0FBQyxlQUFlLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUM7Z0JBQ3RELE1BQU0sQ0FBQyxlQUFlLENBQUMsVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO2dCQUMzQyxNQUFNLENBQUMsZUFBZSxDQUFDLFdBQVcsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLDJCQUEyQjtZQUM5RSxDQUFDLENBQUMsQ0FBQztZQUVILEVBQUUsQ0FBQyw2Q0FBNkMsRUFBRSxHQUFHLEVBQUU7Z0JBQ3JELE1BQU0sU0FBUyxHQUFHLDJEQUEyQixDQUFDLFFBQVEsQ0FBQyxhQUFhLENBQUMsQ0FBQztnQkFFdEUsTUFBTSxlQUFlLEdBQUcsU0FBUyxDQUFDLHVCQUF1QixFQUFFLENBQUM7Z0JBRTVELE1BQU0sQ0FBQyxlQUFlLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO2dCQUM1RCxNQUFNLENBQUMsZUFBZSxDQUFDLFVBQVUsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztnQkFDM0MsTUFBTSxDQUFDLGVBQWUsQ0FBQyxXQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDakQsQ0FBQyxDQUFDLENBQUM7WUFFSCxFQUFFLENBQUMsb0RBQW9ELEVBQUUsR0FBRyxFQUFFO2dCQUM1RCxNQUFNLFNBQVMsR0FBRywyREFBMkIsQ0FBQyxrQkFBa0IsQ0FBQyxhQUFhLENBQUMsQ0FBQztnQkFFaEYsTUFBTSxlQUFlLEdBQUcsU0FBUyxDQUFDLHVCQUF1QixFQUFFLENBQUM7Z0JBRTVELE1BQU0sQ0FBQyxlQUFlLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDO2dCQUN0RCxNQUFNLENBQUMsZUFBZSxDQUFDLFVBQVUsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztnQkFDM0MsTUFBTSxDQUFDLGVBQWUsQ0FBQyxXQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDakQsQ0FBQyxDQUFDLENBQUM7WUFFSCxFQUFFLENBQUMsd0RBQXdELEVBQUUsR0FBRyxFQUFFO2dCQUNoRSxNQUFNLFNBQVMsR0FBRywyREFBMkIsQ0FBQyxrQkFBa0IsQ0FBQyxhQUFhLEVBQUUsZ0JBQWdCLENBQUMsQ0FBQztnQkFFbEcsTUFBTSxlQUFlLEdBQUcsU0FBUyxDQUFDLHVCQUF1QixFQUFFLENBQUM7Z0JBRTVELE1BQU0sQ0FBQyxlQUFlLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDO2dCQUN0RCxNQUFNLENBQUMsZUFBZSxDQUFDLFVBQVUsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztnQkFDM0MsTUFBTSxDQUFDLGVBQWUsQ0FBQyxXQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDbEQsQ0FBQyxDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGdCQUFnQixFQUFFLEdBQUcsRUFBRTtRQUM5QixFQUFFLENBQUMsNkRBQTZELEVBQUUsR0FBRyxFQUFFO1lBQ3JFLE1BQU0sR0FBRyxHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7WUFDN0MsTUFBTSxLQUFLLEdBQUcsSUFBSSxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQztZQUMvQyxNQUFNLEdBQUcsR0FBRyxJQUFJLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDO1lBRTdDLE1BQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUM7WUFDN0IsSUFBSSxDQUFDLEdBQUcsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO1lBRXhDLE1BQU0sU0FBUyxHQUFHLDJEQUEyQixDQUFDLFdBQVcsQ0FBQyxhQUFhLEVBQUUsS0FBSyxFQUFFLEdBQUcsQ0FBQyxDQUFDO1lBRXJGLE1BQU0sQ0FBQyxTQUFTLENBQUMsY0FBYyxFQUFFLENBQUMsQ0FBQyxPQUFPLENBQ3hDLHlGQUF5RixDQUMxRixDQUFDO1lBRUYsSUFBSSxDQUFDLEdBQUcsR0FBRyxXQUFXLENBQUM7UUFDekIsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsa0RBQWtELEVBQUUsR0FBRyxFQUFFO1lBQzFELE1BQU0sU0FBUyxHQUFHLDJEQUEyQixDQUFDLFFBQVEsQ0FBQyxhQUFhLEVBQUUsRUFBRSxVQUFVLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQztZQUUxRixNQUFNLENBQUMsU0FBUyxDQUFDLGNBQWMsRUFBRSxDQUFDLENBQUMsT0FBTyxDQUN4Qyx5SEFBeUgsQ0FDMUgsQ0FBQztRQUNKLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHlEQUF5RCxFQUFFLEdBQUcsRUFBRTtZQUNqRSxNQUFNLFNBQVMsR0FBRywyREFBMkIsQ0FBQyxrQkFBa0IsQ0FBQyxhQUFhLEVBQUUsRUFBRSxVQUFVLEVBQUUsR0FBRyxFQUFFLENBQUMsQ0FBQztZQUVyRyxNQUFNLENBQUMsU0FBUyxDQUFDLGNBQWMsRUFBRSxDQUFDLENBQUMsT0FBTyxDQUN4Qyx1R0FBdUcsQ0FDeEcsQ0FBQztRQUNKLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDZEQUE2RCxFQUFFLEdBQUcsRUFBRTtZQUNyRSxNQUFNLFNBQVMsR0FBRywyREFBMkIsQ0FBQyxrQkFBa0IsQ0FBQyxhQUFhLEVBQUUsZ0JBQWdCLENBQUMsQ0FBQztZQUVsRyxNQUFNLENBQUMsU0FBUyxDQUFDLGNBQWMsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUNyQyxrRkFBa0YsQ0FDbkYsQ0FBQztRQUNKLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsZUFBZSxFQUFFLEdBQUcsRUFBRTtRQUM3QixFQUFFLENBQUMsb0RBQW9ELEVBQUUsR0FBRyxFQUFFO1lBQzVELE1BQU0sU0FBUyxHQUFHLDJEQUEyQixDQUFDLFFBQVEsQ0FBQyxhQUFhLEVBQUUsRUFBRSxVQUFVLEVBQUUsR0FBRyxFQUFFLENBQUMsQ0FBQztZQUUzRixNQUFNLFFBQVEsR0FBRyxTQUFTLENBQUMsYUFBYSxFQUFFLENBQUM7WUFFM0MsTUFBTSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsaURBQWlELENBQUMsQ0FBQztZQUNsRixNQUFNLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO1lBQ2xELE1BQU0sQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLENBQUMsYUFBYSxDQUFDO2dCQUNyQyxXQUFXLEVBQUUsYUFBYTtnQkFDMUIsTUFBTSxFQUFFLFVBQVU7Z0JBQ2xCLFVBQVUsRUFBRSxHQUFHO2FBQ2hCLENBQUMsQ0FBQztZQUNILE1BQU0sQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLENBQUMsYUFBYSxDQUFDO2dCQUNyQyxhQUFhLEVBQUUsS0FBSzthQUNyQixDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxvREFBb0QsRUFBRSxHQUFHLEVBQUU7WUFDNUQsTUFBTSxTQUFTLEdBQUcsSUFBSSwyREFBMkIsQ0FBQyxjQUFjLEVBQUUsYUFBYSxFQUFFLFVBQVUsQ0FBQyxDQUFDO1lBRTdGLE1BQU0sUUFBUSxHQUFHLFNBQVMsQ0FBQyxhQUFhLEVBQUUsQ0FBQztZQUUzQyxNQUFNLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUMsQ0FBQztRQUN2QyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLFFBQVEsRUFBRSxHQUFHLEVBQUU7UUFDdEIsRUFBRSxDQUFDLGtEQUFrRCxFQUFFLEdBQUcsRUFBRTtZQUMxRCxNQUFNLHFCQUFxQixHQUFHLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7WUFDL0QsTUFBTSxTQUFTLEdBQUcsMkRBQTJCLENBQUMsUUFBUSxDQUFDLGFBQWEsRUFBRTtnQkFDcEUsV0FBVyxFQUFFLEdBQUc7Z0JBQ2hCLFdBQVcsRUFBRSxHQUFHO2dCQUNoQixxQkFBcUI7Z0JBQ3JCLFVBQVUsRUFBRSxHQUFHO2FBQ2hCLENBQUMsQ0FBQztZQUVILE1BQU0sSUFBSSxHQUFHLFNBQVMsQ0FBQyxNQUFNLEVBQUUsQ0FBQztZQUVoQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsYUFBYSxDQUFDO2dCQUN6QixJQUFJLEVBQUUsNkJBQTZCO2dCQUNuQyxJQUFJLEVBQUUscUJBQXFCO2dCQUMzQixRQUFRLEVBQUUsTUFBTTtnQkFDaEIsUUFBUSxFQUFFLGNBQWM7Z0JBQ3hCLFdBQVcsRUFBRSxhQUFhO2dCQUMxQixvQkFBb0IsRUFBRSxVQUFVO2dCQUNoQyxxQkFBcUIsRUFBRSxxQkFBcUIsQ0FBQyxXQUFXLEVBQUU7Z0JBQzFELFVBQVUsRUFBRSxHQUFHO2dCQUNmLGFBQWEsRUFBRSxLQUFLO2dCQUNwQixVQUFVLEVBQUUsSUFBSTtnQkFDaEIsb0JBQW9CLEVBQUUsS0FBSztnQkFDM0IsbUJBQW1CLEVBQUUsS0FBSztnQkFDMUIsb0JBQW9CLEVBQUUsS0FBSztnQkFDM0Isb0JBQW9CLEVBQUUsS0FBSztnQkFDM0IsY0FBYyxFQUFFLEtBQUs7Z0JBQ3JCLHFCQUFxQixFQUFFLEtBQUs7Z0JBQzVCLFFBQVEsRUFBRTtvQkFDUixXQUFXLEVBQUUsR0FBRztvQkFDaEIsV0FBVyxFQUFFLEdBQUc7b0JBQ2hCLGNBQWMsRUFBRSxHQUFHO2lCQUNwQjthQUNGLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsYUFBYSxFQUFFLEdBQUcsRUFBRTtRQUMzQixFQUFFLENBQUMsaURBQWlELEVBQUUsR0FBRyxFQUFFO1lBQ3pELE1BQU0sU0FBUyxHQUFHLElBQUksMkRBQTJCLENBQUMsY0FBYyxFQUFFLGFBQWEsRUFBRSxVQUFVLENBQUMsQ0FBQztZQUU3RixNQUFNLENBQUMsU0FBUyxDQUFDLENBQUMsY0FBYyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQ3hDLE1BQU0sQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLDZCQUE2QixDQUFDLENBQUM7WUFDM0QsTUFBTSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMsQ0FBQztRQUNyRCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx3Q0FBd0MsRUFBRSxHQUFHLEVBQUU7WUFDaEQsTUFBTSxTQUFTLEdBQUcsSUFBSSwyREFBMkIsQ0FBQyxjQUFjLEVBQUUsYUFBYSxFQUFFLFVBQVUsQ0FBQyxDQUFDO1lBRTdGLE1BQU0sQ0FBQyxTQUFTLFlBQVksMkRBQTJCLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDcEUsTUFBTSxDQUFDLFNBQVMsWUFBWSxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDaEQsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztBQUNMLENBQUMsQ0FBQyxDQUFDIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTHVrYVxcc2VudGluZWxcXGJhY2tlbmRcXHNyY1xcc2hhcmVkLWtlcm5lbFxcX190ZXN0c19fXFxleGNlcHRpb25zXFxzZXJ2aWNlLXVuYXZhaWxhYmxlLmV4Y2VwdGlvbi5zcGVjLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbiB9IGZyb20gJy4uLy4uL2V4Y2VwdGlvbnMvc2VydmljZS11bmF2YWlsYWJsZS5leGNlcHRpb24nO1xyXG5cclxuZGVzY3JpYmUoJ1NlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbicsICgpID0+IHtcclxuICBkZXNjcmliZSgnY29uc3RydWN0b3InLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIGNyZWF0ZSBleGNlcHRpb24gd2l0aCByZXF1aXJlZCBwYXJhbWV0ZXJzJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBleGNlcHRpb24gPSBuZXcgU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uKFxyXG4gICAgICAgICdTZXJ2aWNlIGlzIGRvd24nLFxyXG4gICAgICAgICdVc2VyU2VydmljZScsXHJcbiAgICAgICAgJ21haW50ZW5hbmNlJ1xyXG4gICAgICApO1xyXG5cclxuICAgICAgZXhwZWN0KGV4Y2VwdGlvbikudG9CZUluc3RhbmNlT2YoU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uKTtcclxuICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5tZXNzYWdlKS50b0JlKCdTZXJ2aWNlIGlzIGRvd24nKTtcclxuICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5jb2RlKS50b0JlKCdTRVJWSUNFX1VOQVZBSUxBQkxFJyk7XHJcbiAgICAgIGV4cGVjdChleGNlcHRpb24uc2V2ZXJpdHkpLnRvQmUoJ2hpZ2gnKTtcclxuICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5jYXRlZ29yeSkudG9CZSgnYXZhaWxhYmlsaXR5Jyk7XHJcbiAgICAgIGV4cGVjdChleGNlcHRpb24uc2VydmljZU5hbWUpLnRvQmUoJ1VzZXJTZXJ2aWNlJyk7XHJcbiAgICAgIGV4cGVjdChleGNlcHRpb24udW5hdmFpbGFiaWxpdHlSZWFzb24pLnRvQmUoJ21haW50ZW5hbmNlJyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGNyZWF0ZSBleGNlcHRpb24gd2l0aCBhbGwgb3B0aW9ucycsICgpID0+IHtcclxuICAgICAgY29uc3QgZXN0aW1hdGVkUmVjb3ZlcnlUaW1lID0gbmV3IERhdGUoJzIwMjMtMDEtMDFUMTU6MDA6MDBaJyk7XHJcbiAgICAgIGNvbnN0IG1haW50ZW5hbmNlV2luZG93ID0ge1xyXG4gICAgICAgIHN0YXJ0OiBuZXcgRGF0ZSgnMjAyMy0wMS0wMVQxMjowMDowMFonKSxcclxuICAgICAgICBlbmQ6IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDE0OjAwOjAwWicpLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnU2NoZWR1bGVkIG1haW50ZW5hbmNlJyxcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IG5ldyBTZXJ2aWNlVW5hdmFpbGFibGVFeGNlcHRpb24oXHJcbiAgICAgICAgJ1NlcnZpY2UgaXMgZG93bicsXHJcbiAgICAgICAgJ1VzZXJTZXJ2aWNlJyxcclxuICAgICAgICAnbWFpbnRlbmFuY2UnLFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIGVzdGltYXRlZFJlY292ZXJ5VGltZSxcclxuICAgICAgICAgIHJldHJ5QWZ0ZXI6IDMwMCxcclxuICAgICAgICAgIG1haW50ZW5hbmNlV2luZG93LFxyXG4gICAgICAgICAgY29ycmVsYXRpb25JZDogJ3Rlc3QtY29ycmVsYXRpb24taWQnLFxyXG4gICAgICAgIH1cclxuICAgICAgKTtcclxuXHJcbiAgICAgIGV4cGVjdChleGNlcHRpb24uZXN0aW1hdGVkUmVjb3ZlcnlUaW1lKS50b0VxdWFsKGVzdGltYXRlZFJlY292ZXJ5VGltZSk7XHJcbiAgICAgIGV4cGVjdChleGNlcHRpb24ucmV0cnlBZnRlcikudG9CZSgzMDApO1xyXG4gICAgICBleHBlY3QoZXhjZXB0aW9uLm1haW50ZW5hbmNlV2luZG93KS50b0VxdWFsKG1haW50ZW5hbmNlV2luZG93KTtcclxuICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5jb3JyZWxhdGlvbklkKS50b0JlKCd0ZXN0LWNvcnJlbGF0aW9uLWlkJyk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ3N0YXRpYyBmYWN0b3J5IG1ldGhvZHMnLCAoKSA9PiB7XHJcbiAgICBkZXNjcmliZSgnbWFpbnRlbmFuY2UnLCAoKSA9PiB7XHJcbiAgICAgIGl0KCdzaG91bGQgY3JlYXRlIGV4Y2VwdGlvbiBmb3Igb25nb2luZyBtYWludGVuYW5jZScsICgpID0+IHtcclxuICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgnMjAyMy0wMS0wMVQxMzowMDowMFonKTtcclxuICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDEyOjAwOjAwWicpO1xyXG4gICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDE0OjAwOjAwWicpO1xyXG5cclxuICAgICAgICAvLyBNb2NrIERhdGUubm93IHRvIHJldHVybiBvdXIgdGVzdCB0aW1lXHJcbiAgICAgICAgY29uc3Qgb3JpZ2luYWxOb3cgPSBEYXRlLm5vdztcclxuICAgICAgICBEYXRlLm5vdyA9IGplc3QuZm4oKCkgPT4gbm93LmdldFRpbWUoKSk7XHJcblxyXG4gICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbi5tYWludGVuYW5jZShcclxuICAgICAgICAgICdVc2VyU2VydmljZScsXHJcbiAgICAgICAgICBzdGFydCxcclxuICAgICAgICAgIGVuZCxcclxuICAgICAgICAgIHtcclxuICAgICAgICAgICAgZGVzY3JpcHRpb246ICdEYXRhYmFzZSB1cGdyYWRlJyxcclxuICAgICAgICAgIH1cclxuICAgICAgICApO1xyXG5cclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLm1lc3NhZ2UpLnRvQmUoJ1VzZXJTZXJ2aWNlIGlzIGN1cnJlbnRseSB1bmRlcmdvaW5nIG1haW50ZW5hbmNlJyk7XHJcbiAgICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5zZXJ2aWNlTmFtZSkudG9CZSgnVXNlclNlcnZpY2UnKTtcclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLnVuYXZhaWxhYmlsaXR5UmVhc29uKS50b0JlKCdtYWludGVuYW5jZScpO1xyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24ubWFpbnRlbmFuY2VXaW5kb3cpLnRvRXF1YWwoe1xyXG4gICAgICAgICAgc3RhcnQsXHJcbiAgICAgICAgICBlbmQsXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ0RhdGFiYXNlIHVwZ3JhZGUnLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24ucmV0cnlBZnRlcikudG9CZSgzNjAwKTsgLy8gMSBob3VyIHVudGlsIGVuZFxyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24uaXNNYWludGVuYW5jZSgpKS50b0JlKHRydWUpO1xyXG5cclxuICAgICAgICAvLyBSZXN0b3JlIG9yaWdpbmFsIERhdGUubm93XHJcbiAgICAgICAgRGF0ZS5ub3cgPSBvcmlnaW5hbE5vdztcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpdCgnc2hvdWxkIGNyZWF0ZSBleGNlcHRpb24gZm9yIHNjaGVkdWxlZCBtYWludGVuYW5jZScsICgpID0+IHtcclxuICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgnMjAyMy0wMS0wMVQxMTowMDowMFonKTtcclxuICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDEyOjAwOjAwWicpO1xyXG4gICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDE0OjAwOjAwWicpO1xyXG5cclxuICAgICAgICBjb25zdCBvcmlnaW5hbE5vdyA9IERhdGUubm93O1xyXG4gICAgICAgIERhdGUubm93ID0gamVzdC5mbigoKSA9PiBub3cuZ2V0VGltZSgpKTtcclxuXHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLm1haW50ZW5hbmNlKCdVc2VyU2VydmljZScsIHN0YXJ0LCBlbmQpO1xyXG5cclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLm1lc3NhZ2UpLnRvQmUoJ1VzZXJTZXJ2aWNlIHdpbGwgYmUgdW5hdmFpbGFibGUgZm9yIG1haW50ZW5hbmNlJyk7XHJcbiAgICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5yZXRyeUFmdGVyKS50b0JlKDM2MDApOyAvLyAxIGhvdXIgdW50aWwgc3RhcnRcclxuXHJcbiAgICAgICAgRGF0ZS5ub3cgPSBvcmlnaW5hbE5vdztcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpdCgnc2hvdWxkIGNyZWF0ZSBleGNlcHRpb24gZm9yIGNvbXBsZXRlZCBtYWludGVuYW5jZScsICgpID0+IHtcclxuICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgnMjAyMy0wMS0wMVQxNTowMDowMFonKTtcclxuICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDEyOjAwOjAwWicpO1xyXG4gICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDE0OjAwOjAwWicpO1xyXG5cclxuICAgICAgICBjb25zdCBvcmlnaW5hbE5vdyA9IERhdGUubm93O1xyXG4gICAgICAgIERhdGUubm93ID0gamVzdC5mbigoKSA9PiBub3cuZ2V0VGltZSgpKTtcclxuXHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLm1haW50ZW5hbmNlKCdVc2VyU2VydmljZScsIHN0YXJ0LCBlbmQpO1xyXG5cclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLm1lc3NhZ2UpLnRvQmUoJ1VzZXJTZXJ2aWNlIG1haW50ZW5hbmNlIGhhcyBjb21wbGV0ZWQgYnV0IHNlcnZpY2UgbWF5IHN0aWxsIGJlIHJlY292ZXJpbmcnKTtcclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLnJldHJ5QWZ0ZXIpLnRvQmUoMzAwKTsgLy8gRGVmYXVsdCA1IG1pbnV0ZXNcclxuXHJcbiAgICAgICAgRGF0ZS5ub3cgPSBvcmlnaW5hbE5vdztcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBkZXNjcmliZSgnb3ZlcmxvYWQnLCAoKSA9PiB7XHJcbiAgICAgIGl0KCdzaG91bGQgY3JlYXRlIGV4Y2VwdGlvbiBmb3Igc2VydmljZSBvdmVybG9hZCcsICgpID0+IHtcclxuICAgICAgICBjb25zdCBlc3RpbWF0ZWRSZWNvdmVyeVRpbWUgPSBuZXcgRGF0ZSgnMjAyMy0wMS0wMVQxMzowMDowMFonKTtcclxuICAgICAgICBjb25zdCBleGNlcHRpb24gPSBTZXJ2aWNlVW5hdmFpbGFibGVFeGNlcHRpb24ub3ZlcmxvYWQoJ1VzZXJTZXJ2aWNlJywge1xyXG4gICAgICAgICAgY3VycmVudExvYWQ6IDE1MCxcclxuICAgICAgICAgIG1heENhcGFjaXR5OiAxMDAsXHJcbiAgICAgICAgICBlc3RpbWF0ZWRSZWNvdmVyeVRpbWUsXHJcbiAgICAgICAgICByZXRyeUFmdGVyOiAxMjAsXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24ubWVzc2FnZSkudG9CZSgnVXNlclNlcnZpY2UgaXMgY3VycmVudGx5IG92ZXJsb2FkZWQgYW5kIGNhbm5vdCBwcm9jZXNzIHJlcXVlc3RzJyk7XHJcbiAgICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5zZXJ2aWNlTmFtZSkudG9CZSgnVXNlclNlcnZpY2UnKTtcclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLnVuYXZhaWxhYmlsaXR5UmVhc29uKS50b0JlKCdvdmVybG9hZCcpO1xyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24uZXN0aW1hdGVkUmVjb3ZlcnlUaW1lKS50b0VxdWFsKGVzdGltYXRlZFJlY292ZXJ5VGltZSk7XHJcbiAgICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5yZXRyeUFmdGVyKS50b0JlKDEyMCk7XHJcbiAgICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5pc092ZXJsb2FkKCkpLnRvQmUodHJ1ZSk7XHJcblxyXG4gICAgICAgIGNvbnN0IGxvYWRJbmZvID0gZXhjZXB0aW9uLmdldExvYWRJbmZvKCk7XHJcbiAgICAgICAgZXhwZWN0KGxvYWRJbmZvKS50b0VxdWFsKHtcclxuICAgICAgICAgIGN1cnJlbnRMb2FkOiAxNTAsXHJcbiAgICAgICAgICBtYXhDYXBhY2l0eTogMTAwLFxyXG4gICAgICAgICAgbG9hZFBlcmNlbnRhZ2U6IDE1MCxcclxuICAgICAgICB9KTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpdCgnc2hvdWxkIHVzZSBkZWZhdWx0IHJldHJ5IHRpbWUgZm9yIG92ZXJsb2FkJywgKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbi5vdmVybG9hZCgnVXNlclNlcnZpY2UnKTtcclxuXHJcbiAgICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5yZXRyeUFmdGVyKS50b0JlKDYwKTsgLy8gRGVmYXVsdCAxIG1pbnV0ZVxyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGRlc2NyaWJlKCdjaXJjdWl0QnJlYWtlck9wZW4nLCAoKSA9PiB7XHJcbiAgICAgIGl0KCdzaG91bGQgY3JlYXRlIGV4Y2VwdGlvbiBmb3IgY2lyY3VpdCBicmVha2VyIG9wZW4nLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgY2lyY3VpdE9wZW5UaW1lID0gbmV3IERhdGUoJzIwMjMtMDEtMDFUMTI6MDA6MDBaJyk7XHJcbiAgICAgICAgY29uc3QgbmV4dFJldHJ5VGltZSA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDEyOjA1OjAwWicpO1xyXG5cclxuICAgICAgICBjb25zdCBleGNlcHRpb24gPSBTZXJ2aWNlVW5hdmFpbGFibGVFeGNlcHRpb24uY2lyY3VpdEJyZWFrZXJPcGVuKCdVc2VyU2VydmljZScsIHtcclxuICAgICAgICAgIGZhaWx1cmVDb3VudDogMTAsXHJcbiAgICAgICAgICBmYWlsdXJlVGhyZXNob2xkOiA1LFxyXG4gICAgICAgICAgY2lyY3VpdE9wZW5UaW1lLFxyXG4gICAgICAgICAgbmV4dFJldHJ5VGltZSxcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5tZXNzYWdlKS50b0JlKFxyXG4gICAgICAgICAgJ1VzZXJTZXJ2aWNlIGlzIHRlbXBvcmFyaWx5IHVuYXZhaWxhYmxlIGR1ZSB0byByZXBlYXRlZCBmYWlsdXJlcyAoY2lyY3VpdCBicmVha2VyIG9wZW4pJ1xyXG4gICAgICAgICk7XHJcbiAgICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5zZXJ2aWNlTmFtZSkudG9CZSgnVXNlclNlcnZpY2UnKTtcclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLnVuYXZhaWxhYmlsaXR5UmVhc29uKS50b0JlKCdjaXJjdWl0X2JyZWFrZXJfb3BlbicpO1xyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24uaXNDaXJjdWl0QnJlYWtlck9wZW4oKSkudG9CZSh0cnVlKTtcclxuXHJcbiAgICAgICAgY29uc3QgY2lyY3VpdEluZm8gPSBleGNlcHRpb24uZ2V0Q2lyY3VpdEJyZWFrZXJJbmZvKCk7XHJcbiAgICAgICAgZXhwZWN0KGNpcmN1aXRJbmZvKS50b0VxdWFsKHtcclxuICAgICAgICAgIGZhaWx1cmVDb3VudDogMTAsXHJcbiAgICAgICAgICBmYWlsdXJlVGhyZXNob2xkOiA1LFxyXG4gICAgICAgICAgY2lyY3VpdE9wZW5UaW1lLFxyXG4gICAgICAgICAgbmV4dFJldHJ5VGltZSxcclxuICAgICAgICB9KTtcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBkZXNjcmliZSgnZGVwZW5kZW5jeUZhaWx1cmUnLCAoKSA9PiB7XHJcbiAgICAgIGl0KCdzaG91bGQgY3JlYXRlIGV4Y2VwdGlvbiBmb3IgZGVwZW5kZW5jeSBmYWlsdXJlJywgKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbi5kZXBlbmRlbmN5RmFpbHVyZShcclxuICAgICAgICAgICdVc2VyU2VydmljZScsXHJcbiAgICAgICAgICAnRGF0YWJhc2VTZXJ2aWNlJyxcclxuICAgICAgICAgIHtcclxuICAgICAgICAgICAgZGVwZW5kZW5jeUVycm9yOiAnQ29ubmVjdGlvbiB0aW1lb3V0JyxcclxuICAgICAgICAgICAgcmV0cnlBZnRlcjogMTgwLFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICk7XHJcblxyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24ubWVzc2FnZSkudG9CZSgnVXNlclNlcnZpY2UgaXMgdW5hdmFpbGFibGUgZHVlIHRvIERhdGFiYXNlU2VydmljZSBmYWlsdXJlJyk7XHJcbiAgICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5zZXJ2aWNlTmFtZSkudG9CZSgnVXNlclNlcnZpY2UnKTtcclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLnVuYXZhaWxhYmlsaXR5UmVhc29uKS50b0JlKCdkZXBlbmRlbmN5X2ZhaWx1cmUnKTtcclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLnJldHJ5QWZ0ZXIpLnRvQmUoMTgwKTtcclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLmlzRGVwZW5kZW5jeUZhaWx1cmUoKSkudG9CZSh0cnVlKTtcclxuXHJcbiAgICAgICAgY29uc3QgZGVwSW5mbyA9IGV4Y2VwdGlvbi5nZXREZXBlbmRlbmN5SW5mbygpO1xyXG4gICAgICAgIGV4cGVjdChkZXBJbmZvKS50b0VxdWFsKHtcclxuICAgICAgICAgIGRlcGVuZGVuY3lOYW1lOiAnRGF0YWJhc2VTZXJ2aWNlJyxcclxuICAgICAgICAgIGRlcGVuZGVuY3lFcnJvcjogJ0Nvbm5lY3Rpb24gdGltZW91dCcsXHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgZGVzY3JpYmUoJ3Jlc291cmNlRXhoYXVzdGlvbicsICgpID0+IHtcclxuICAgICAgaXQoJ3Nob3VsZCBjcmVhdGUgZXhjZXB0aW9uIGZvciByZXNvdXJjZSBleGhhdXN0aW9uJywgKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbi5yZXNvdXJjZUV4aGF1c3Rpb24oXHJcbiAgICAgICAgICAnVXNlclNlcnZpY2UnLFxyXG4gICAgICAgICAgJ21lbW9yeScsXHJcbiAgICAgICAgICB7XHJcbiAgICAgICAgICAgIGN1cnJlbnRVc2FnZTogOTUsXHJcbiAgICAgICAgICAgIG1heENhcGFjaXR5OiAxMDAsXHJcbiAgICAgICAgICAgIHJldHJ5QWZ0ZXI6IDI0MCxcclxuICAgICAgICAgIH1cclxuICAgICAgICApO1xyXG5cclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLm1lc3NhZ2UpLnRvQmUoJ1VzZXJTZXJ2aWNlIGlzIHVuYXZhaWxhYmxlIGR1ZSB0byBtZW1vcnkgZXhoYXVzdGlvbicpO1xyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24uc2VydmljZU5hbWUpLnRvQmUoJ1VzZXJTZXJ2aWNlJyk7XHJcbiAgICAgICAgZXhwZWN0KGV4Y2VwdGlvbi51bmF2YWlsYWJpbGl0eVJlYXNvbikudG9CZSgncmVzb3VyY2VfZXhoYXVzdGlvbicpO1xyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24ucmV0cnlBZnRlcikudG9CZSgyNDApO1xyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24uaXNSZXNvdXJjZUV4aGF1c3Rpb24oKSkudG9CZSh0cnVlKTtcclxuXHJcbiAgICAgICAgY29uc3QgcmVzb3VyY2VJbmZvID0gZXhjZXB0aW9uLmdldFJlc291cmNlSW5mbygpO1xyXG4gICAgICAgIGV4cGVjdChyZXNvdXJjZUluZm8pLnRvRXF1YWwoe1xyXG4gICAgICAgICAgcmVzb3VyY2VUeXBlOiAnbWVtb3J5JyxcclxuICAgICAgICAgIGN1cnJlbnRVc2FnZTogOTUsXHJcbiAgICAgICAgICBtYXhDYXBhY2l0eTogMTAwLFxyXG4gICAgICAgICAgdXNhZ2VQZXJjZW50YWdlOiA5NSxcclxuICAgICAgICB9KTtcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBkZXNjcmliZSgnY29uZmlndXJhdGlvbkVycm9yJywgKCkgPT4ge1xyXG4gICAgICBpdCgnc2hvdWxkIGNyZWF0ZSBleGNlcHRpb24gZm9yIGNvbmZpZ3VyYXRpb24gZXJyb3InLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLmNvbmZpZ3VyYXRpb25FcnJvcihcclxuICAgICAgICAgICdVc2VyU2VydmljZScsXHJcbiAgICAgICAgICAnSW52YWxpZCBkYXRhYmFzZSBjb25uZWN0aW9uIHN0cmluZydcclxuICAgICAgICApO1xyXG5cclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLm1lc3NhZ2UpLnRvQmUoXHJcbiAgICAgICAgICAnVXNlclNlcnZpY2UgaXMgdW5hdmFpbGFibGUgZHVlIHRvIGNvbmZpZ3VyYXRpb24gZXJyb3I6IEludmFsaWQgZGF0YWJhc2UgY29ubmVjdGlvbiBzdHJpbmcnXHJcbiAgICAgICAgKTtcclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLnNlcnZpY2VOYW1lKS50b0JlKCdVc2VyU2VydmljZScpO1xyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24udW5hdmFpbGFiaWxpdHlSZWFzb24pLnRvQmUoJ2NvbmZpZ3VyYXRpb25fZXJyb3InKTtcclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLnJldHJ5QWZ0ZXIpLnRvQmUoNjAwKTsgLy8gRGVmYXVsdCAxMCBtaW51dGVzXHJcbiAgICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5pc0NvbmZpZ3VyYXRpb25FcnJvcigpKS50b0JlKHRydWUpO1xyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGRlc2NyaWJlKCdpbml0aWFsaXppbmcnLCAoKSA9PiB7XHJcbiAgICAgIGl0KCdzaG91bGQgY3JlYXRlIGV4Y2VwdGlvbiBmb3Igc2VydmljZSBpbml0aWFsaXphdGlvbicsICgpID0+IHtcclxuICAgICAgICBjb25zdCBleGNlcHRpb24gPSBTZXJ2aWNlVW5hdmFpbGFibGVFeGNlcHRpb24uaW5pdGlhbGl6aW5nKCdVc2VyU2VydmljZScsIHtcclxuICAgICAgICAgIGluaXRpYWxpemF0aW9uU3RhZ2U6ICdMb2FkaW5nIGNvbmZpZ3VyYXRpb24nLFxyXG4gICAgICAgICAgcmV0cnlBZnRlcjogNDUsXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24ubWVzc2FnZSkudG9CZShcclxuICAgICAgICAgICdVc2VyU2VydmljZSBpcyBjdXJyZW50bHkgaW5pdGlhbGl6aW5nIChMb2FkaW5nIGNvbmZpZ3VyYXRpb24pIGFuZCBub3QgcmVhZHkgdG8gc2VydmUgcmVxdWVzdHMnXHJcbiAgICAgICAgKTtcclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLnNlcnZpY2VOYW1lKS50b0JlKCdVc2VyU2VydmljZScpO1xyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24udW5hdmFpbGFiaWxpdHlSZWFzb24pLnRvQmUoJ2luaXRpYWxpemluZycpO1xyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24ucmV0cnlBZnRlcikudG9CZSg0NSk7XHJcbiAgICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5pc0luaXRpYWxpemluZygpKS50b0JlKHRydWUpO1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGl0KCdzaG91bGQgY3JlYXRlIGV4Y2VwdGlvbiB3aXRob3V0IGluaXRpYWxpemF0aW9uIHN0YWdlJywgKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbi5pbml0aWFsaXppbmcoJ1VzZXJTZXJ2aWNlJyk7XHJcblxyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24ubWVzc2FnZSkudG9CZShcclxuICAgICAgICAgICdVc2VyU2VydmljZSBpcyBjdXJyZW50bHkgaW5pdGlhbGl6aW5nIGFuZCBub3QgcmVhZHkgdG8gc2VydmUgcmVxdWVzdHMnXHJcbiAgICAgICAgKTtcclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLnJldHJ5QWZ0ZXIpLnRvQmUoMzApOyAvLyBEZWZhdWx0IDMwIHNlY29uZHNcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ3R5cGUgY2hlY2tpbmcgbWV0aG9kcycsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgY29ycmVjdGx5IGlkZW50aWZ5IHVuYXZhaWxhYmlsaXR5IHJlYXNvbnMnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IG1haW50ZW5hbmNlRXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLm1haW50ZW5hbmNlKFxyXG4gICAgICAgICdTZXJ2aWNlJyxcclxuICAgICAgICBuZXcgRGF0ZSgpLFxyXG4gICAgICAgIG5ldyBEYXRlKERhdGUubm93KCkgKyAzNjAwMDAwKVxyXG4gICAgICApO1xyXG4gICAgICBjb25zdCBvdmVybG9hZEV4Y2VwdGlvbiA9IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbi5vdmVybG9hZCgnU2VydmljZScpO1xyXG4gICAgICBjb25zdCBjaXJjdWl0RXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLmNpcmN1aXRCcmVha2VyT3BlbignU2VydmljZScpO1xyXG4gICAgICBjb25zdCBkZXBlbmRlbmN5RXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLmRlcGVuZGVuY3lGYWlsdXJlKCdTZXJ2aWNlJywgJ0RCJyk7XHJcbiAgICAgIGNvbnN0IHJlc291cmNlRXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLnJlc291cmNlRXhoYXVzdGlvbignU2VydmljZScsICdtZW1vcnknKTtcclxuICAgICAgY29uc3QgY29uZmlnRXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLmNvbmZpZ3VyYXRpb25FcnJvcignU2VydmljZScsICdJbnZhbGlkIGNvbmZpZycpO1xyXG4gICAgICBjb25zdCBpbml0RXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLmluaXRpYWxpemluZygnU2VydmljZScpO1xyXG5cclxuICAgICAgZXhwZWN0KG1haW50ZW5hbmNlRXhjZXB0aW9uLmlzTWFpbnRlbmFuY2UoKSkudG9CZSh0cnVlKTtcclxuICAgICAgZXhwZWN0KG1haW50ZW5hbmNlRXhjZXB0aW9uLmlzT3ZlcmxvYWQoKSkudG9CZShmYWxzZSk7XHJcblxyXG4gICAgICBleHBlY3Qob3ZlcmxvYWRFeGNlcHRpb24uaXNPdmVybG9hZCgpKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3Qob3ZlcmxvYWRFeGNlcHRpb24uaXNDaXJjdWl0QnJlYWtlck9wZW4oKSkudG9CZShmYWxzZSk7XHJcblxyXG4gICAgICBleHBlY3QoY2lyY3VpdEV4Y2VwdGlvbi5pc0NpcmN1aXRCcmVha2VyT3BlbigpKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QoY2lyY3VpdEV4Y2VwdGlvbi5pc0RlcGVuZGVuY3lGYWlsdXJlKCkpLnRvQmUoZmFsc2UpO1xyXG5cclxuICAgICAgZXhwZWN0KGRlcGVuZGVuY3lFeGNlcHRpb24uaXNEZXBlbmRlbmN5RmFpbHVyZSgpKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QoZGVwZW5kZW5jeUV4Y2VwdGlvbi5pc1Jlc291cmNlRXhoYXVzdGlvbigpKS50b0JlKGZhbHNlKTtcclxuXHJcbiAgICAgIGV4cGVjdChyZXNvdXJjZUV4Y2VwdGlvbi5pc1Jlc291cmNlRXhoYXVzdGlvbigpKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QocmVzb3VyY2VFeGNlcHRpb24uaXNDb25maWd1cmF0aW9uRXJyb3IoKSkudG9CZShmYWxzZSk7XHJcblxyXG4gICAgICBleHBlY3QoY29uZmlnRXhjZXB0aW9uLmlzQ29uZmlndXJhdGlvbkVycm9yKCkpLnRvQmUodHJ1ZSk7XHJcbiAgICAgIGV4cGVjdChjb25maWdFeGNlcHRpb24uaXNJbml0aWFsaXppbmcoKSkudG9CZShmYWxzZSk7XHJcblxyXG4gICAgICBleHBlY3QoaW5pdEV4Y2VwdGlvbi5pc0luaXRpYWxpemluZygpKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QoaW5pdEV4Y2VwdGlvbi5pc01haW50ZW5hbmNlKCkpLnRvQmUoZmFsc2UpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCd1dGlsaXR5IG1ldGhvZHMnLCAoKSA9PiB7XHJcbiAgICBkZXNjcmliZSgnZ2V0VGltZVVudGlsUmVjb3ZlcnknLCAoKSA9PiB7XHJcbiAgICAgIGl0KCdzaG91bGQgcmV0dXJuIHRpbWUgdW50aWwgcmVjb3ZlcnkgaW4gc2Vjb25kcycsICgpID0+IHtcclxuICAgICAgICBjb25zdCBmdXR1cmVSZWNvdmVyeVRpbWUgPSBuZXcgRGF0ZShEYXRlLm5vdygpICsgMzAwMDAwKTsgLy8gNSBtaW51dGVzIGZyb20gbm93XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gbmV3IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbihcclxuICAgICAgICAgICdTZXJ2aWNlIGRvd24nLFxyXG4gICAgICAgICAgJ1VzZXJTZXJ2aWNlJyxcclxuICAgICAgICAgICdtYWludGVuYW5jZScsXHJcbiAgICAgICAgICB7XHJcbiAgICAgICAgICAgIGVzdGltYXRlZFJlY292ZXJ5VGltZTogZnV0dXJlUmVjb3ZlcnlUaW1lLFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICk7XHJcblxyXG4gICAgICAgIGNvbnN0IHRpbWVVbnRpbFJlY292ZXJ5ID0gZXhjZXB0aW9uLmdldFRpbWVVbnRpbFJlY292ZXJ5KCk7XHJcbiAgICAgICAgZXhwZWN0KHRpbWVVbnRpbFJlY292ZXJ5KS50b0JlR3JlYXRlclRoYW4oMjkwKTtcclxuICAgICAgICBleHBlY3QodGltZVVudGlsUmVjb3ZlcnkpLnRvQmVMZXNzVGhhbk9yRXF1YWwoMzAwKTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpdCgnc2hvdWxkIHJldHVybiBudWxsIHdoZW4gbm8gcmVjb3ZlcnkgdGltZSBpcyBzZXQnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gbmV3IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbignU2VydmljZSBkb3duJywgJ1VzZXJTZXJ2aWNlJywgJ292ZXJsb2FkJyk7XHJcblxyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24uZ2V0VGltZVVudGlsUmVjb3ZlcnkoKSkudG9CZU51bGwoKTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpdCgnc2hvdWxkIHJldHVybiAwIGZvciBwYXN0IHJlY292ZXJ5IHRpbWUnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgcGFzdFJlY292ZXJ5VGltZSA9IG5ldyBEYXRlKERhdGUubm93KCkgLSAzMDAwMDApOyAvLyA1IG1pbnV0ZXMgYWdvXHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gbmV3IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbihcclxuICAgICAgICAgICdTZXJ2aWNlIGRvd24nLFxyXG4gICAgICAgICAgJ1VzZXJTZXJ2aWNlJyxcclxuICAgICAgICAgICdtYWludGVuYW5jZScsXHJcbiAgICAgICAgICB7XHJcbiAgICAgICAgICAgIGVzdGltYXRlZFJlY292ZXJ5VGltZTogcGFzdFJlY292ZXJ5VGltZSxcclxuICAgICAgICAgIH1cclxuICAgICAgICApO1xyXG5cclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLmdldFRpbWVVbnRpbFJlY292ZXJ5KCkpLnRvQmUoMCk7XHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgZGVzY3JpYmUoJ2dldFRpbWVVbnRpbFJlY292ZXJ5Rm9ybWF0dGVkJywgKCkgPT4ge1xyXG4gICAgICBpdCgnc2hvdWxkIGZvcm1hdCB0aW1lIGNvcnJlY3RseScsICgpID0+IHtcclxuICAgICAgICBjb25zdCBmdXR1cmVSZWNvdmVyeVRpbWUgPSBuZXcgRGF0ZShEYXRlLm5vdygpICsgMzAwMDAwKTsgLy8gNSBtaW51dGVzIGZyb20gbm93XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gbmV3IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbihcclxuICAgICAgICAgICdTZXJ2aWNlIGRvd24nLFxyXG4gICAgICAgICAgJ1VzZXJTZXJ2aWNlJyxcclxuICAgICAgICAgICdtYWludGVuYW5jZScsXHJcbiAgICAgICAgICB7XHJcbiAgICAgICAgICAgIGVzdGltYXRlZFJlY292ZXJ5VGltZTogZnV0dXJlUmVjb3ZlcnlUaW1lLFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICk7XHJcblxyXG4gICAgICAgIGV4cGVjdChleGNlcHRpb24uZ2V0VGltZVVudGlsUmVjb3ZlcnlGb3JtYXR0ZWQoKSkudG9NYXRjaCgvXFxkKyBtaW51dGVcXChzXFwpLyk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaXQoJ3Nob3VsZCByZXR1cm4gbnVsbCB3aGVuIG5vIHJlY292ZXJ5IHRpbWUgaXMgc2V0JywgKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IG5ldyBTZXJ2aWNlVW5hdmFpbGFibGVFeGNlcHRpb24oJ1NlcnZpY2UgZG93bicsICdVc2VyU2VydmljZScsICdvdmVybG9hZCcpO1xyXG5cclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLmdldFRpbWVVbnRpbFJlY292ZXJ5Rm9ybWF0dGVkKCkpLnRvQmVOdWxsKCk7XHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgZGVzY3JpYmUoJ2lzSW5NYWludGVuYW5jZVdpbmRvdycsICgpID0+IHtcclxuICAgICAgaXQoJ3Nob3VsZCByZXR1cm4gdHJ1ZSB3aGVuIGluIG1haW50ZW5hbmNlIHdpbmRvdycsICgpID0+IHtcclxuICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgnMjAyMy0wMS0wMVQxMzowMDowMFonKTtcclxuICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDEyOjAwOjAwWicpO1xyXG4gICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDE0OjAwOjAwWicpO1xyXG5cclxuICAgICAgICBjb25zdCBvcmlnaW5hbE5vdyA9IERhdGUubm93O1xyXG4gICAgICAgIERhdGUubm93ID0gamVzdC5mbigoKSA9PiBub3cuZ2V0VGltZSgpKTtcclxuXHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLm1haW50ZW5hbmNlKCdVc2VyU2VydmljZScsIHN0YXJ0LCBlbmQpO1xyXG5cclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLmlzSW5NYWludGVuYW5jZVdpbmRvdygpKS50b0JlKHRydWUpO1xyXG5cclxuICAgICAgICBEYXRlLm5vdyA9IG9yaWdpbmFsTm93O1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGl0KCdzaG91bGQgcmV0dXJuIGZhbHNlIHdoZW4gbm90IGluIG1haW50ZW5hbmNlIHdpbmRvdycsICgpID0+IHtcclxuICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgnMjAyMy0wMS0wMVQxMTowMDowMFonKTtcclxuICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDEyOjAwOjAwWicpO1xyXG4gICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDE0OjAwOjAwWicpO1xyXG5cclxuICAgICAgICBjb25zdCBvcmlnaW5hbE5vdyA9IERhdGUubm93O1xyXG4gICAgICAgIERhdGUubm93ID0gamVzdC5mbigoKSA9PiBub3cuZ2V0VGltZSgpKTtcclxuXHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLm1haW50ZW5hbmNlKCdVc2VyU2VydmljZScsIHN0YXJ0LCBlbmQpO1xyXG5cclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLmlzSW5NYWludGVuYW5jZVdpbmRvdygpKS50b0JlKGZhbHNlKTtcclxuXHJcbiAgICAgICAgRGF0ZS5ub3cgPSBvcmlnaW5hbE5vdztcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpdCgnc2hvdWxkIHJldHVybiBmYWxzZSB3aGVuIG5vIG1haW50ZW5hbmNlIHdpbmRvdyBpcyBzZXQnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLm92ZXJsb2FkKCdVc2VyU2VydmljZScpO1xyXG5cclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLmlzSW5NYWludGVuYW5jZVdpbmRvdygpKS50b0JlKGZhbHNlKTtcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBkZXNjcmliZSgnZ2V0TWFpbnRlbmFuY2VJbmZvJywgKCkgPT4ge1xyXG4gICAgICBpdCgnc2hvdWxkIHJldHVybiBtYWludGVuYW5jZSBpbmZvcm1hdGlvbicsICgpID0+IHtcclxuICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgnMjAyMy0wMS0wMVQxMzowMDowMFonKTtcclxuICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDEyOjAwOjAwWicpO1xyXG4gICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDE0OjAwOjAwWicpO1xyXG5cclxuICAgICAgICBjb25zdCBvcmlnaW5hbE5vdyA9IERhdGUubm93O1xyXG4gICAgICAgIERhdGUubm93ID0gamVzdC5mbigoKSA9PiBub3cuZ2V0VGltZSgpKTtcclxuXHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLm1haW50ZW5hbmNlKCdVc2VyU2VydmljZScsIHN0YXJ0LCBlbmQsIHtcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnRGF0YWJhc2UgdXBncmFkZScsXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGNvbnN0IG1haW50ZW5hbmNlSW5mbyA9IGV4Y2VwdGlvbi5nZXRNYWludGVuYW5jZUluZm8oKTtcclxuICAgICAgICBleHBlY3QobWFpbnRlbmFuY2VJbmZvKS50b0VxdWFsKHtcclxuICAgICAgICAgIGlzU2NoZWR1bGVkOiBmYWxzZSxcclxuICAgICAgICAgIGlzT25nb2luZzogdHJ1ZSxcclxuICAgICAgICAgIGlzQ29tcGxldGVkOiBmYWxzZSxcclxuICAgICAgICAgIGR1cmF0aW9uOiA3MjAwLCAvLyAyIGhvdXJzIGluIHNlY29uZHNcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnRGF0YWJhc2UgdXBncmFkZScsXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIERhdGUubm93ID0gb3JpZ2luYWxOb3c7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaXQoJ3Nob3VsZCByZXR1cm4gbnVsbCB3aGVuIG5vIG1haW50ZW5hbmNlIHdpbmRvdyBpcyBzZXQnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLm92ZXJsb2FkKCdVc2VyU2VydmljZScpO1xyXG5cclxuICAgICAgICBleHBlY3QoZXhjZXB0aW9uLmdldE1haW50ZW5hbmNlSW5mbygpKS50b0JlTnVsbCgpO1xyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGRlc2NyaWJlKCdnZXRSZXRyeVJlY29tbWVuZGF0aW9ucycsICgpID0+IHtcclxuICAgICAgaXQoJ3Nob3VsZCBwcm92aWRlIHJlY29tbWVuZGF0aW9ucyBmb3IgbWFpbnRlbmFuY2UnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLm1haW50ZW5hbmNlKFxyXG4gICAgICAgICAgJ1VzZXJTZXJ2aWNlJyxcclxuICAgICAgICAgIG5ldyBEYXRlKCksXHJcbiAgICAgICAgICBuZXcgRGF0ZShEYXRlLm5vdygpICsgMzYwMDAwMClcclxuICAgICAgICApO1xyXG5cclxuICAgICAgICBjb25zdCByZWNvbW1lbmRhdGlvbnMgPSBleGNlcHRpb24uZ2V0UmV0cnlSZWNvbW1lbmRhdGlvbnMoKTtcclxuXHJcbiAgICAgICAgZXhwZWN0KHJlY29tbWVuZGF0aW9ucy5iYWNrb2ZmU3RyYXRlZ3kpLnRvQmUoJ2ZpeGVkJyk7XHJcbiAgICAgICAgZXhwZWN0KHJlY29tbWVuZGF0aW9ucy5tYXhSZXRyaWVzKS50b0JlKDEpO1xyXG4gICAgICAgIGV4cGVjdChyZWNvbW1lbmRhdGlvbnMuc2hvdWxkUmV0cnkpLnRvQmUoZmFsc2UpOyAvLyBDdXJyZW50bHkgaW4gbWFpbnRlbmFuY2VcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpdCgnc2hvdWxkIHByb3ZpZGUgcmVjb21tZW5kYXRpb25zIGZvciBvdmVybG9hZCcsICgpID0+IHtcclxuICAgICAgICBjb25zdCBleGNlcHRpb24gPSBTZXJ2aWNlVW5hdmFpbGFibGVFeGNlcHRpb24ub3ZlcmxvYWQoJ1VzZXJTZXJ2aWNlJyk7XHJcblxyXG4gICAgICAgIGNvbnN0IHJlY29tbWVuZGF0aW9ucyA9IGV4Y2VwdGlvbi5nZXRSZXRyeVJlY29tbWVuZGF0aW9ucygpO1xyXG5cclxuICAgICAgICBleHBlY3QocmVjb21tZW5kYXRpb25zLmJhY2tvZmZTdHJhdGVneSkudG9CZSgnZXhwb25lbnRpYWwnKTtcclxuICAgICAgICBleHBlY3QocmVjb21tZW5kYXRpb25zLm1heFJldHJpZXMpLnRvQmUoNSk7XHJcbiAgICAgICAgZXhwZWN0KHJlY29tbWVuZGF0aW9ucy5zaG91bGRSZXRyeSkudG9CZSh0cnVlKTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpdCgnc2hvdWxkIHByb3ZpZGUgcmVjb21tZW5kYXRpb25zIGZvciBjaXJjdWl0IGJyZWFrZXInLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLmNpcmN1aXRCcmVha2VyT3BlbignVXNlclNlcnZpY2UnKTtcclxuXHJcbiAgICAgICAgY29uc3QgcmVjb21tZW5kYXRpb25zID0gZXhjZXB0aW9uLmdldFJldHJ5UmVjb21tZW5kYXRpb25zKCk7XHJcblxyXG4gICAgICAgIGV4cGVjdChyZWNvbW1lbmRhdGlvbnMuYmFja29mZlN0cmF0ZWd5KS50b0JlKCdmaXhlZCcpO1xyXG4gICAgICAgIGV4cGVjdChyZWNvbW1lbmRhdGlvbnMubWF4UmV0cmllcykudG9CZSgzKTtcclxuICAgICAgICBleHBlY3QocmVjb21tZW5kYXRpb25zLnNob3VsZFJldHJ5KS50b0JlKHRydWUpO1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGl0KCdzaG91bGQgcHJvdmlkZSByZWNvbW1lbmRhdGlvbnMgZm9yIGNvbmZpZ3VyYXRpb24gZXJyb3InLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgZXhjZXB0aW9uID0gU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uLmNvbmZpZ3VyYXRpb25FcnJvcignVXNlclNlcnZpY2UnLCAnSW52YWxpZCBjb25maWcnKTtcclxuXHJcbiAgICAgICAgY29uc3QgcmVjb21tZW5kYXRpb25zID0gZXhjZXB0aW9uLmdldFJldHJ5UmVjb21tZW5kYXRpb25zKCk7XHJcblxyXG4gICAgICAgIGV4cGVjdChyZWNvbW1lbmRhdGlvbnMuYmFja29mZlN0cmF0ZWd5KS50b0JlKCdmaXhlZCcpO1xyXG4gICAgICAgIGV4cGVjdChyZWNvbW1lbmRhdGlvbnMubWF4UmV0cmllcykudG9CZSgxKTtcclxuICAgICAgICBleHBlY3QocmVjb21tZW5kYXRpb25zLnNob3VsZFJldHJ5KS50b0JlKGZhbHNlKTtcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2dldFVzZXJNZXNzYWdlJywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCByZXR1cm4gdXNlci1mcmllbmRseSBtZXNzYWdlIGZvciBvbmdvaW5nIG1haW50ZW5hbmNlJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgnMjAyMy0wMS0wMVQxMzowMDowMFonKTtcclxuICAgICAgY29uc3Qgc3RhcnQgPSBuZXcgRGF0ZSgnMjAyMy0wMS0wMVQxMjowMDowMFonKTtcclxuICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoJzIwMjMtMDEtMDFUMTQ6MDA6MDBaJyk7XHJcblxyXG4gICAgICBjb25zdCBvcmlnaW5hbE5vdyA9IERhdGUubm93O1xyXG4gICAgICBEYXRlLm5vdyA9IGplc3QuZm4oKCkgPT4gbm93LmdldFRpbWUoKSk7XHJcblxyXG4gICAgICBjb25zdCBleGNlcHRpb24gPSBTZXJ2aWNlVW5hdmFpbGFibGVFeGNlcHRpb24ubWFpbnRlbmFuY2UoJ1VzZXJTZXJ2aWNlJywgc3RhcnQsIGVuZCk7XHJcblxyXG4gICAgICBleHBlY3QoZXhjZXB0aW9uLmdldFVzZXJNZXNzYWdlKCkpLnRvTWF0Y2goXHJcbiAgICAgICAgL1VzZXJTZXJ2aWNlIGlzIGN1cnJlbnRseSB1bmRlcmdvaW5nIG1haW50ZW5hbmNlXFwuIFBsZWFzZSB0cnkgYWdhaW4gaW4gXFxkKyBtaW51dGVcXChzXFwpXFwuL1xyXG4gICAgICApO1xyXG5cclxuICAgICAgRGF0ZS5ub3cgPSBvcmlnaW5hbE5vdztcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgcmV0dXJuIHVzZXItZnJpZW5kbHkgbWVzc2FnZSBmb3Igb3ZlcmxvYWQnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbi5vdmVybG9hZCgnVXNlclNlcnZpY2UnLCB7IHJldHJ5QWZ0ZXI6IDYwIH0pO1xyXG5cclxuICAgICAgZXhwZWN0KGV4Y2VwdGlvbi5nZXRVc2VyTWVzc2FnZSgpKS50b01hdGNoKFxyXG4gICAgICAgIC9Vc2VyU2VydmljZSBpcyBjdXJyZW50bHkgZXhwZXJpZW5jaW5nIGhpZ2ggbG9hZCBhbmQgY2Fubm90IHByb2Nlc3MgeW91ciByZXF1ZXN0XFwuIFBsZWFzZSB0cnkgYWdhaW4gaW4gXFxkKyBzZWNvbmRcXChzXFwpXFwuL1xyXG4gICAgICApO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCByZXR1cm4gdXNlci1mcmllbmRseSBtZXNzYWdlIGZvciBjaXJjdWl0IGJyZWFrZXInLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbi5jaXJjdWl0QnJlYWtlck9wZW4oJ1VzZXJTZXJ2aWNlJywgeyByZXRyeUFmdGVyOiAzMDAgfSk7XHJcblxyXG4gICAgICBleHBlY3QoZXhjZXB0aW9uLmdldFVzZXJNZXNzYWdlKCkpLnRvTWF0Y2goXHJcbiAgICAgICAgL1VzZXJTZXJ2aWNlIGlzIHRlbXBvcmFyaWx5IHVuYXZhaWxhYmxlIGR1ZSB0byByZWNlbnQgZmFpbHVyZXNcXC4gUGxlYXNlIHRyeSBhZ2FpbiBpbiBcXGQrIG1pbnV0ZVxcKHNcXClcXC4vXHJcbiAgICAgICk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHJldHVybiB1c2VyLWZyaWVuZGx5IG1lc3NhZ2UgZm9yIGNvbmZpZ3VyYXRpb24gZXJyb3InLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbi5jb25maWd1cmF0aW9uRXJyb3IoJ1VzZXJTZXJ2aWNlJywgJ0ludmFsaWQgY29uZmlnJyk7XHJcblxyXG4gICAgICBleHBlY3QoZXhjZXB0aW9uLmdldFVzZXJNZXNzYWdlKCkpLnRvQmUoXHJcbiAgICAgICAgJ1VzZXJTZXJ2aWNlIGlzIHVuYXZhaWxhYmxlIGR1ZSB0byBhIGNvbmZpZ3VyYXRpb24gaXNzdWUuIFBsZWFzZSBjb250YWN0IHN1cHBvcnQuJ1xyXG4gICAgICApO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCd0b0FwaVJlc3BvbnNlJywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCBjb252ZXJ0IHRvIEFQSSByZXNwb25zZSBmb3JtYXQgd2l0aCBoZWFkZXJzJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBleGNlcHRpb24gPSBTZXJ2aWNlVW5hdmFpbGFibGVFeGNlcHRpb24ub3ZlcmxvYWQoJ1VzZXJTZXJ2aWNlJywgeyByZXRyeUFmdGVyOiAxMjAgfSk7XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGV4Y2VwdGlvbi50b0FwaVJlc3BvbnNlKCk7XHJcblxyXG4gICAgICBleHBlY3QocmVzcG9uc2UuZXJyb3IpLnRvTWF0Y2goL1VzZXJTZXJ2aWNlIGlzIGN1cnJlbnRseSBleHBlcmllbmNpbmcgaGlnaCBsb2FkLyk7XHJcbiAgICAgIGV4cGVjdChyZXNwb25zZS5jb2RlKS50b0JlKCdTRVJWSUNFX1VOQVZBSUxBQkxFJyk7XHJcbiAgICAgIGV4cGVjdChyZXNwb25zZS5kZXRhaWxzKS50b01hdGNoT2JqZWN0KHtcclxuICAgICAgICBzZXJ2aWNlTmFtZTogJ1VzZXJTZXJ2aWNlJyxcclxuICAgICAgICByZWFzb246ICdvdmVybG9hZCcsXHJcbiAgICAgICAgcmV0cnlBZnRlcjogMTIwLFxyXG4gICAgICB9KTtcclxuICAgICAgZXhwZWN0KHJlc3BvbnNlLmhlYWRlcnMpLnRvTWF0Y2hPYmplY3Qoe1xyXG4gICAgICAgICdSZXRyeS1BZnRlcic6ICcxMjAnLFxyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgbm90IGluY2x1ZGUgUmV0cnktQWZ0ZXIgaGVhZGVyIHdoZW4gbm90IHNldCcsICgpID0+IHtcclxuICAgICAgY29uc3QgZXhjZXB0aW9uID0gbmV3IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbignU2VydmljZSBkb3duJywgJ1VzZXJTZXJ2aWNlJywgJ292ZXJsb2FkJyk7XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGV4Y2VwdGlvbi50b0FwaVJlc3BvbnNlKCk7XHJcblxyXG4gICAgICBleHBlY3QocmVzcG9uc2UuaGVhZGVycykudG9FcXVhbCh7fSk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ3RvSlNPTicsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgY29udmVydCB0byBKU09OIHdpdGggZGV0YWlsZWQgaW5mb3JtYXRpb24nLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGVzdGltYXRlZFJlY292ZXJ5VGltZSA9IG5ldyBEYXRlKCcyMDIzLTAxLTAxVDE1OjAwOjAwWicpO1xyXG4gICAgICBjb25zdCBleGNlcHRpb24gPSBTZXJ2aWNlVW5hdmFpbGFibGVFeGNlcHRpb24ub3ZlcmxvYWQoJ1VzZXJTZXJ2aWNlJywge1xyXG4gICAgICAgIGN1cnJlbnRMb2FkOiAxNTAsXHJcbiAgICAgICAgbWF4Q2FwYWNpdHk6IDEwMCxcclxuICAgICAgICBlc3RpbWF0ZWRSZWNvdmVyeVRpbWUsXHJcbiAgICAgICAgcmV0cnlBZnRlcjogMTIwLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGNvbnN0IGpzb24gPSBleGNlcHRpb24udG9KU09OKCk7XHJcblxyXG4gICAgICBleHBlY3QoanNvbikudG9NYXRjaE9iamVjdCh7XHJcbiAgICAgICAgbmFtZTogJ1NlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbicsXHJcbiAgICAgICAgY29kZTogJ1NFUlZJQ0VfVU5BVkFJTEFCTEUnLFxyXG4gICAgICAgIHNldmVyaXR5OiAnaGlnaCcsXHJcbiAgICAgICAgY2F0ZWdvcnk6ICdhdmFpbGFiaWxpdHknLFxyXG4gICAgICAgIHNlcnZpY2VOYW1lOiAnVXNlclNlcnZpY2UnLFxyXG4gICAgICAgIHVuYXZhaWxhYmlsaXR5UmVhc29uOiAnb3ZlcmxvYWQnLFxyXG4gICAgICAgIGVzdGltYXRlZFJlY292ZXJ5VGltZTogZXN0aW1hdGVkUmVjb3ZlcnlUaW1lLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgcmV0cnlBZnRlcjogMTIwLFxyXG4gICAgICAgIGlzTWFpbnRlbmFuY2U6IGZhbHNlLFxyXG4gICAgICAgIGlzT3ZlcmxvYWQ6IHRydWUsXHJcbiAgICAgICAgaXNDaXJjdWl0QnJlYWtlck9wZW46IGZhbHNlLFxyXG4gICAgICAgIGlzRGVwZW5kZW5jeUZhaWx1cmU6IGZhbHNlLFxyXG4gICAgICAgIGlzUmVzb3VyY2VFeGhhdXN0aW9uOiBmYWxzZSxcclxuICAgICAgICBpc0NvbmZpZ3VyYXRpb25FcnJvcjogZmFsc2UsXHJcbiAgICAgICAgaXNJbml0aWFsaXppbmc6IGZhbHNlLFxyXG4gICAgICAgIGlzSW5NYWludGVuYW5jZVdpbmRvdzogZmFsc2UsXHJcbiAgICAgICAgbG9hZEluZm86IHtcclxuICAgICAgICAgIGN1cnJlbnRMb2FkOiAxNTAsXHJcbiAgICAgICAgICBtYXhDYXBhY2l0eTogMTAwLFxyXG4gICAgICAgICAgbG9hZFBlcmNlbnRhZ2U6IDE1MCxcclxuICAgICAgICB9LFxyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnaW5oZXJpdGFuY2UnLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIGJlIGluc3RhbmNlIG9mIEVycm9yIGFuZCBEb21haW5FeGNlcHRpb24nLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGV4Y2VwdGlvbiA9IG5ldyBTZXJ2aWNlVW5hdmFpbGFibGVFeGNlcHRpb24oJ1NlcnZpY2UgZG93bicsICdVc2VyU2VydmljZScsICdvdmVybG9hZCcpO1xyXG5cclxuICAgICAgZXhwZWN0KGV4Y2VwdGlvbikudG9CZUluc3RhbmNlT2YoRXJyb3IpO1xyXG4gICAgICBleHBlY3QoZXhjZXB0aW9uLm5hbWUpLnRvQmUoJ1NlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbicpO1xyXG4gICAgICBleHBlY3QoZXhjZXB0aW9uLmNvZGUpLnRvQmUoJ1NFUlZJQ0VfVU5BVkFJTEFCTEUnKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgbWFpbnRhaW4gcHJvcGVyIHByb3RvdHlwZSBjaGFpbicsICgpID0+IHtcclxuICAgICAgY29uc3QgZXhjZXB0aW9uID0gbmV3IFNlcnZpY2VVbmF2YWlsYWJsZUV4Y2VwdGlvbignU2VydmljZSBkb3duJywgJ1VzZXJTZXJ2aWNlJywgJ292ZXJsb2FkJyk7XHJcblxyXG4gICAgICBleHBlY3QoZXhjZXB0aW9uIGluc3RhbmNlb2YgU2VydmljZVVuYXZhaWxhYmxlRXhjZXB0aW9uKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QoZXhjZXB0aW9uIGluc3RhbmNlb2YgRXJyb3IpLnRvQmUodHJ1ZSk7XHJcbiAgICB9KTtcclxuICB9KTtcclxufSk7Il0sInZlcnNpb24iOjN9