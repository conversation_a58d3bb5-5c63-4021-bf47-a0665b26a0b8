7911a305536f0a88c6601b4116dcfdd6
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const event_factory_1 = require("../../domain/factories/event.factory");
const threat_factory_1 = require("../../domain/factories/threat.factory");
const vulnerability_factory_1 = require("../../domain/factories/vulnerability.factory");
const response_action_factory_1 = require("../../domain/factories/response-action.factory");
const event_repository_1 = require("../../domain/repositories/event.repository");
const threat_repository_1 = require("../../domain/repositories/threat.repository");
const vulnerability_repository_1 = require("../../domain/repositories/vulnerability.repository");
const response_action_repository_1 = require("../../domain/repositories/response-action.repository");
const event_metadata_value_object_1 = require("../../domain/value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../domain/value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../domain/value-objects/event-metadata/event-source.value-object");
const ip_address_value_object_1 = require("../../domain/value-objects/network/ip-address.value-object");
const port_value_object_1 = require("../../domain/value-objects/network/port.value-object");
const cvss_score_value_object_1 = require("../../domain/value-objects/threat-indicators/cvss-score.value-object");
const event_type_enum_1 = require("../../domain/enums/event-type.enum");
const event_severity_enum_1 = require("../../domain/enums/event-severity.enum");
const event_status_enum_1 = require("../../domain/enums/event-status.enum");
const event_source_type_enum_1 = require("../../domain/enums/event-source-type.enum");
const threat_severity_enum_1 = require("../../domain/enums/threat-severity.enum");
const vulnerability_severity_enum_1 = require("../../domain/enums/vulnerability-severity.enum");
const action_type_enum_1 = require("../../domain/enums/action-type.enum");
const action_status_enum_1 = require("../../domain/enums/action-status.enum");
const unique_entity_id_value_object_1 = require("../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const tenant_id_value_object_1 = require("../../../../shared-kernel/value-objects/tenant-id.value-object");
const user_id_value_object_1 = require("../../../../shared-kernel/value-objects/user-id.value-object");
/**
 * In-Memory Repository Implementations for Contract Testing
 */
class InMemoryEventRepository {
    constructor() {
        this.events = new Map();
    }
    async save(event) {
        this.events.set(event.id.toString(), event);
    }
    async findById(id) {
        return this.events.get(id.toString()) || null;
    }
    async findAll() {
        return Array.from(this.events.values());
    }
    async delete(id) {
        this.events.delete(id.toString());
    }
    async findByTimeRange(start, end) {
        return Array.from(this.events.values()).filter(event => {
            const eventTime = event.timestamp.toDate();
            return eventTime >= start && eventTime <= end;
        });
    }
    async findBySource(source) {
        return Array.from(this.events.values()).filter(event => event.source.equals(source));
    }
    async findBySeverity(severity) {
        return Array.from(this.events.values()).filter(event => event.severity === severity);
    }
    async findEventsForCorrelation(timeWindowMs, eventTypes, minSeverity) {
        const cutoffTime = new Date(Date.now() - timeWindowMs);
        return Array.from(this.events.values()).filter(event => {
            const eventTime = event.timestamp.toDate();
            const timeMatch = eventTime >= cutoffTime;
            const typeMatch = !eventTypes || eventTypes.includes(event.type);
            const severityMatch = !minSeverity || this.compareSeverity(event.severity, minSeverity) >= 0;
            return timeMatch && typeMatch && severityMatch;
        });
    }
    compareSeverity(a, b) {
        const severityOrder = [event_severity_enum_1.EventSeverity.LOW, event_severity_enum_1.EventSeverity.MEDIUM, event_severity_enum_1.EventSeverity.HIGH, event_severity_enum_1.EventSeverity.CRITICAL];
        return severityOrder.indexOf(a) - severityOrder.indexOf(b);
    }
    // Additional methods for testing
    clear() {
        this.events.clear();
    }
    size() {
        return this.events.size;
    }
}
class InMemoryThreatRepository {
    constructor() {
        this.threats = new Map();
    }
    async save(threat) {
        this.threats.set(threat.id.toString(), threat);
    }
    async findById(id) {
        return this.threats.get(id.toString()) || null;
    }
    async findAll() {
        return Array.from(this.threats.values());
    }
    async delete(id) {
        this.threats.delete(id.toString());
    }
    async findBySeverity(severity) {
        return Array.from(this.threats.values()).filter(threat => threat.severity === severity);
    }
    async findBySourceIp(sourceIp) {
        return Array.from(this.threats.values()).filter(threat => threat.sourceIp && threat.sourceIp.equals(sourceIp));
    }
    clear() {
        this.threats.clear();
    }
    size() {
        return this.threats.size;
    }
}
class InMemoryVulnerabilityRepository {
    constructor() {
        this.vulnerabilities = new Map();
    }
    async save(vulnerability) {
        this.vulnerabilities.set(vulnerability.id.toString(), vulnerability);
    }
    async findById(id) {
        return this.vulnerabilities.get(id.toString()) || null;
    }
    async findAll() {
        return Array.from(this.vulnerabilities.values());
    }
    async delete(id) {
        this.vulnerabilities.delete(id.toString());
    }
    async findBySeverity(severity) {
        return Array.from(this.vulnerabilities.values()).filter(vuln => vuln.severity === severity);
    }
    async findByPort(port) {
        return Array.from(this.vulnerabilities.values()).filter(vuln => vuln.affectedPort && vuln.affectedPort.equals(port));
    }
    clear() {
        this.vulnerabilities.clear();
    }
    size() {
        return this.vulnerabilities.size;
    }
}
class InMemoryResponseActionRepository {
    constructor() {
        this.actions = new Map();
    }
    async save(action) {
        this.actions.set(action.id.toString(), action);
    }
    async findById(id) {
        return this.actions.get(id.toString()) || null;
    }
    async findAll() {
        return Array.from(this.actions.values());
    }
    async delete(id) {
        this.actions.delete(id.toString());
    }
    async findByStatus(status) {
        return Array.from(this.actions.values()).filter(action => action.status === status);
    }
    async findByType(type) {
        return Array.from(this.actions.values()).filter(action => action.type === type);
    }
    clear() {
        this.actions.clear();
    }
    size() {
        return this.actions.size;
    }
}
describe('Repository Contracts Integration Tests', () => {
    let module;
    let eventRepository;
    let threatRepository;
    let vulnerabilityRepository;
    let responseActionRepository;
    beforeEach(async () => {
        eventRepository = new InMemoryEventRepository();
        threatRepository = new InMemoryThreatRepository();
        vulnerabilityRepository = new InMemoryVulnerabilityRepository();
        responseActionRepository = new InMemoryResponseActionRepository();
        module = await testing_1.Test.createTestingModule({
            providers: [
                { provide: event_repository_1.EventRepository, useValue: eventRepository },
                { provide: threat_repository_1.ThreatRepository, useValue: threatRepository },
                { provide: vulnerability_repository_1.VulnerabilityRepository, useValue: vulnerabilityRepository },
                { provide: response_action_repository_1.ResponseActionRepository, useValue: responseActionRepository },
            ],
        }).compile();
    });
    afterEach(async () => {
        eventRepository.clear();
        threatRepository.clear();
        vulnerabilityRepository.clear();
        responseActionRepository.clear();
        await module.close();
    });
    describe('EventRepository Contract', () => {
        it('should implement complete CRUD operations', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-001',
                    name: 'Test App',
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_SUCCESS,
                severity: event_severity_enum_1.EventSeverity.LOW,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { username: 'testuser' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Act & Assert - Create
            await expect(eventRepository.save(event)).resolves.not.toThrow();
            expect(eventRepository.size()).toBe(1);
            // Act & Assert - Read
            const foundEvent = await eventRepository.findById(event.id);
            expect(foundEvent).toBeDefined();
            expect(foundEvent.id.equals(event.id)).toBe(true);
            expect(foundEvent.type).toBe(event.type);
            expect(foundEvent.severity).toBe(event.severity);
            // Act & Assert - Update (save existing)
            event.markAsProcessing();
            await eventRepository.save(event);
            const updatedEvent = await eventRepository.findById(event.id);
            expect(updatedEvent.status).toBe(event_status_enum_1.EventStatus.PROCESSING);
            // Act & Assert - Delete
            await eventRepository.delete(event.id);
            const deletedEvent = await eventRepository.findById(event.id);
            expect(deletedEvent).toBeNull();
            expect(eventRepository.size()).toBe(0);
        });
        it('should handle specialized query methods', async () => {
            // Arrange
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 3600000);
            const twoHoursAgo = new Date(now.getTime() - 7200000);
            const source1 = event_source_value_object_1.EventSource.create({
                type: event_source_type_enum_1.EventSourceType.FIREWALL,
                identifier: 'fw-001',
                name: 'Firewall 1',
            });
            const source2 = event_source_value_object_1.EventSource.create({
                type: event_source_type_enum_1.EventSourceType.IDS,
                identifier: 'ids-001',
                name: 'IDS 1',
            });
            const events = [
                event_factory_1.EventFactory.create({
                    metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'fw', version: '1.0' }),
                    timestamp: event_timestamp_value_object_1.EventTimestamp.create(oneHourAgo),
                    source: source1,
                    type: event_type_enum_1.EventType.NETWORK_INTRUSION,
                    severity: event_severity_enum_1.EventSeverity.HIGH,
                    status: event_status_enum_1.EventStatus.RECEIVED,
                    payload: {},
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                }),
                event_factory_1.EventFactory.create({
                    metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'ids', version: '1.0' }),
                    timestamp: event_timestamp_value_object_1.EventTimestamp.create(twoHoursAgo),
                    source: source2,
                    type: event_type_enum_1.EventType.MALWARE_DETECTED,
                    severity: event_severity_enum_1.EventSeverity.CRITICAL,
                    status: event_status_enum_1.EventStatus.RECEIVED,
                    payload: {},
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                }),
                event_factory_1.EventFactory.create({
                    metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'fw', version: '1.0' }),
                    timestamp: event_timestamp_value_object_1.EventTimestamp.create(now),
                    source: source1,
                    type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                    severity: event_severity_enum_1.EventSeverity.MEDIUM,
                    status: event_status_enum_1.EventStatus.RECEIVED,
                    payload: {},
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                }),
            ];
            for (const event of events) {
                await eventRepository.save(event);
            }
            // Act & Assert - FindByTimeRange
            const recentEvents = await eventRepository.findByTimeRange(oneHourAgo, now);
            expect(recentEvents).toHaveLength(2);
            const oldEvents = await eventRepository.findByTimeRange(twoHoursAgo, oneHourAgo);
            expect(oldEvents).toHaveLength(1);
            // Act & Assert - FindBySource
            const firewallEvents = await eventRepository.findBySource(source1);
            expect(firewallEvents).toHaveLength(2);
            const idsEvents = await eventRepository.findBySource(source2);
            expect(idsEvents).toHaveLength(1);
            // Act & Assert - FindBySeverity
            const highSeverityEvents = await eventRepository.findBySeverity(event_severity_enum_1.EventSeverity.HIGH);
            expect(highSeverityEvents).toHaveLength(1);
            const criticalEvents = await eventRepository.findBySeverity(event_severity_enum_1.EventSeverity.CRITICAL);
            expect(criticalEvents).toHaveLength(1);
            // Act & Assert - FindEventsForCorrelation
            const correlationCandidates = await eventRepository.findEventsForCorrelation(3600000, // 1 hour
            [event_type_enum_1.EventType.NETWORK_INTRUSION, event_type_enum_1.EventType.MALWARE_DETECTED], event_severity_enum_1.EventSeverity.HIGH);
            expect(correlationCandidates).toHaveLength(2); // HIGH and CRITICAL events
        });
        it('should handle concurrent operations safely', async () => {
            // Arrange
            const events = Array.from({ length: 10 }, (_, i) => event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: `source-${i}`, version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: `app-${i}`,
                    name: `App ${i}`,
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { attempt: i },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            }));
            // Act - Save all events concurrently
            await Promise.all(events.map(event => eventRepository.save(event)));
            // Assert
            expect(eventRepository.size()).toBe(10);
            const allEvents = await eventRepository.findAll();
            expect(allEvents).toHaveLength(10);
            // Verify all events are properly stored
            for (const event of events) {
                const found = await eventRepository.findById(event.id);
                expect(found).toBeDefined();
                expect(found.id.equals(event.id)).toBe(true);
            }
        });
        it('should handle edge cases and error conditions', async () => {
            // Test finding non-existent entity
            const nonExistentId = unique_entity_id_value_object_1.UniqueEntityId.create();
            const notFound = await eventRepository.findById(nonExistentId);
            expect(notFound).toBeNull();
            // Test deleting non-existent entity (should not throw)
            await expect(eventRepository.delete(nonExistentId)).resolves.not.toThrow();
            // Test empty queries
            const emptyTimeRange = await eventRepository.findByTimeRange(new Date('2020-01-01'), new Date('2020-01-02'));
            expect(emptyTimeRange).toHaveLength(0);
            const emptySeverity = await eventRepository.findBySeverity(event_severity_enum_1.EventSeverity.LOW);
            expect(emptySeverity).toHaveLength(0);
        });
    });
    describe('ThreatRepository Contract', () => {
        it('should implement complete CRUD operations', async () => {
            // Arrange
            const threat = threat_factory_1.ThreatFactory.create({
                signature: 'test-threat-001',
                score: cvss_score_value_object_1.CvssScore.create(7.5),
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                confidence: 90,
                sourceIp: ip_address_value_object_1.IpAddress.create('*************'),
                indicators: ['malicious-hash'],
                mitreTechniques: ['T1055'],
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Act & Assert - Create
            await expect(threatRepository.save(threat)).resolves.not.toThrow();
            expect(threatRepository.size()).toBe(1);
            // Act & Assert - Read
            const foundThreat = await threatRepository.findById(threat.id);
            expect(foundThreat).toBeDefined();
            expect(foundThreat.id.equals(threat.id)).toBe(true);
            expect(foundThreat.severity).toBe(threat.severity);
            // Act & Assert - Update
            threat.markAsMitigated();
            await threatRepository.save(threat);
            const updatedThreat = await threatRepository.findById(threat.id);
            expect(updatedThreat.isMitigated()).toBe(true);
            // Act & Assert - Delete
            await threatRepository.delete(threat.id);
            const deletedThreat = await threatRepository.findById(threat.id);
            expect(deletedThreat).toBeNull();
        });
        it('should handle specialized query methods', async () => {
            // Arrange
            const sourceIp1 = ip_address_value_object_1.IpAddress.create('********');
            const sourceIp2 = ip_address_value_object_1.IpAddress.create('********');
            const threats = [
                threat_factory_1.ThreatFactory.create({
                    signature: 'threat-001',
                    score: cvss_score_value_object_1.CvssScore.create(8.0),
                    severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                    confidence: 85,
                    sourceIp: sourceIp1,
                    indicators: ['ioc-001'],
                    mitreTechniques: ['T1001'],
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                }),
                threat_factory_1.ThreatFactory.create({
                    signature: 'threat-002',
                    score: cvss_score_value_object_1.CvssScore.create(9.5),
                    severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
                    confidence: 95,
                    sourceIp: sourceIp2,
                    indicators: ['ioc-002'],
                    mitreTechniques: ['T1002'],
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                }),
                threat_factory_1.ThreatFactory.create({
                    signature: 'threat-003',
                    score: cvss_score_value_object_1.CvssScore.create(6.0),
                    severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                    confidence: 75,
                    sourceIp: sourceIp1,
                    indicators: ['ioc-003'],
                    mitreTechniques: ['T1003'],
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                }),
            ];
            for (const threat of threats) {
                await threatRepository.save(threat);
            }
            // Act & Assert - FindBySeverity
            const highThreats = await threatRepository.findBySeverity(threat_severity_enum_1.ThreatSeverity.HIGH);
            expect(highThreats).toHaveLength(1);
            const criticalThreats = await threatRepository.findBySeverity(threat_severity_enum_1.ThreatSeverity.CRITICAL);
            expect(criticalThreats).toHaveLength(1);
            // Act & Assert - FindBySourceIp
            const threatsFromIp1 = await threatRepository.findBySourceIp(sourceIp1);
            expect(threatsFromIp1).toHaveLength(2);
            const threatsFromIp2 = await threatRepository.findBySourceIp(sourceIp2);
            expect(threatsFromIp2).toHaveLength(1);
        });
    });
    describe('VulnerabilityRepository Contract', () => {
        it('should implement complete CRUD operations', async () => {
            // Arrange
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.create({
                cveId: 'CVE-2023-1234',
                score: cvss_score_value_object_1.CvssScore.create(8.5),
                severity: vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH,
                affectedPort: port_value_object_1.Port.create(80),
                description: 'Critical web server vulnerability',
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Act & Assert - Create
            await expect(vulnerabilityRepository.save(vulnerability)).resolves.not.toThrow();
            expect(vulnerabilityRepository.size()).toBe(1);
            // Act & Assert - Read
            const foundVuln = await vulnerabilityRepository.findById(vulnerability.id);
            expect(foundVuln).toBeDefined();
            expect(foundVuln.id.equals(vulnerability.id)).toBe(true);
            expect(foundVuln.cveId).toBe(vulnerability.cveId);
            // Act & Assert - Update
            vulnerability.markAsPatched();
            await vulnerabilityRepository.save(vulnerability);
            const updatedVuln = await vulnerabilityRepository.findById(vulnerability.id);
            expect(updatedVuln.isPatched()).toBe(true);
            // Act & Assert - Delete
            await vulnerabilityRepository.delete(vulnerability.id);
            const deletedVuln = await vulnerabilityRepository.findById(vulnerability.id);
            expect(deletedVuln).toBeNull();
        });
        it('should handle specialized query methods', async () => {
            // Arrange
            const port80 = port_value_object_1.Port.create(80);
            const port443 = port_value_object_1.Port.create(443);
            const vulnerabilities = [
                vulnerability_factory_1.VulnerabilityFactory.create({
                    cveId: 'CVE-2023-0001',
                    score: cvss_score_value_object_1.CvssScore.create(9.0),
                    severity: vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL,
                    affectedPort: port80,
                    description: 'Critical HTTP vulnerability',
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                }),
                vulnerability_factory_1.VulnerabilityFactory.create({
                    cveId: 'CVE-2023-0002',
                    score: cvss_score_value_object_1.CvssScore.create(7.5),
                    severity: vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH,
                    affectedPort: port443,
                    description: 'High HTTPS vulnerability',
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                }),
                vulnerability_factory_1.VulnerabilityFactory.create({
                    cveId: 'CVE-2023-0003',
                    score: cvss_score_value_object_1.CvssScore.create(5.0),
                    severity: vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM,
                    affectedPort: port80,
                    description: 'Medium HTTP vulnerability',
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                }),
            ];
            for (const vuln of vulnerabilities) {
                await vulnerabilityRepository.save(vuln);
            }
            // Act & Assert - FindBySeverity
            const criticalVulns = await vulnerabilityRepository.findBySeverity(vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL);
            expect(criticalVulns).toHaveLength(1);
            const highVulns = await vulnerabilityRepository.findBySeverity(vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH);
            expect(highVulns).toHaveLength(1);
            // Act & Assert - FindByPort
            const port80Vulns = await vulnerabilityRepository.findByPort(port80);
            expect(port80Vulns).toHaveLength(2);
            const port443Vulns = await vulnerabilityRepository.findByPort(port443);
            expect(port443Vulns).toHaveLength(1);
        });
    });
    describe('ResponseActionRepository Contract', () => {
        it('should implement complete CRUD operations', async () => {
            // Arrange
            const action = response_action_factory_1.ResponseActionFactory.create({
                type: action_type_enum_1.ActionType.BLOCK_IP,
                parameters: { ipAddress: '*************', duration: 3600 },
                priority: 1,
                status: action_status_enum_1.ActionStatus.PENDING,
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Act & Assert - Create
            await expect(responseActionRepository.save(action)).resolves.not.toThrow();
            expect(responseActionRepository.size()).toBe(1);
            // Act & Assert - Read
            const foundAction = await responseActionRepository.findById(action.id);
            expect(foundAction).toBeDefined();
            expect(foundAction.id.equals(action.id)).toBe(true);
            expect(foundAction.type).toBe(action.type);
            // Act & Assert - Update
            action.execute();
            await responseActionRepository.save(action);
            const updatedAction = await responseActionRepository.findById(action.id);
            expect(updatedAction.status).toBe(action_status_enum_1.ActionStatus.IN_PROGRESS);
            // Act & Assert - Delete
            await responseActionRepository.delete(action.id);
            const deletedAction = await responseActionRepository.findById(action.id);
            expect(deletedAction).toBeNull();
        });
        it('should handle specialized query methods', async () => {
            // Arrange
            const actions = [
                response_action_factory_1.ResponseActionFactory.create({
                    type: action_type_enum_1.ActionType.BLOCK_IP,
                    parameters: { ipAddress: '********' },
                    priority: 1,
                    status: action_status_enum_1.ActionStatus.PENDING,
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                }),
                response_action_factory_1.ResponseActionFactory.create({
                    type: action_type_enum_1.ActionType.ISOLATE_HOST,
                    parameters: { hostId: 'host-001' },
                    priority: 2,
                    status: action_status_enum_1.ActionStatus.IN_PROGRESS,
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                }),
                response_action_factory_1.ResponseActionFactory.create({
                    type: action_type_enum_1.ActionType.BLOCK_IP,
                    parameters: { ipAddress: '********' },
                    priority: 3,
                    status: action_status_enum_1.ActionStatus.COMPLETED,
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                }),
            ];
            for (const action of actions) {
                await responseActionRepository.save(action);
            }
            // Act & Assert - FindByStatus
            const pendingActions = await responseActionRepository.findByStatus(action_status_enum_1.ActionStatus.PENDING);
            expect(pendingActions).toHaveLength(1);
            const inProgressActions = await responseActionRepository.findByStatus(action_status_enum_1.ActionStatus.IN_PROGRESS);
            expect(inProgressActions).toHaveLength(1);
            const completedActions = await responseActionRepository.findByStatus(action_status_enum_1.ActionStatus.COMPLETED);
            expect(completedActions).toHaveLength(1);
            // Act & Assert - FindByType
            const blockIpActions = await responseActionRepository.findByType(action_type_enum_1.ActionType.BLOCK_IP);
            expect(blockIpActions).toHaveLength(2);
            const isolateHostActions = await responseActionRepository.findByType(action_type_enum_1.ActionType.ISOLATE_HOST);
            expect(isolateHostActions).toHaveLength(1);
        });
    });
    describe('Cross-Repository Integration', () => {
        it('should maintain referential integrity across repositories', async () => {
            // Arrange
            const tenantId = tenant_id_value_object_1.TenantId.create();
            const userId = user_id_value_object_1.UserId.create();
            const sourceIp = ip_address_value_object_1.IpAddress.create('*************');
            // Create related entities
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'firewall', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.FIREWALL,
                    identifier: 'fw-001',
                    name: 'Firewall',
                }),
                type: event_type_enum_1.EventType.NETWORK_INTRUSION,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { sourceIp: sourceIp.value },
                tenantId,
                userId,
            });
            const threat = threat_factory_1.ThreatFactory.create({
                signature: 'intrusion-threat',
                score: cvss_score_value_object_1.CvssScore.create(8.0),
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                confidence: 90,
                sourceIp,
                indicators: ['network-intrusion'],
                mitreTechniques: ['T1190'],
                tenantId,
                userId,
            });
            const responseAction = response_action_factory_1.ResponseActionFactory.create({
                type: action_type_enum_1.ActionType.BLOCK_IP,
                parameters: { ipAddress: sourceIp.value, duration: 3600 },
                priority: 1,
                status: action_status_enum_1.ActionStatus.PENDING,
                tenantId,
                userId,
            });
            // Act
            await eventRepository.save(event);
            await threatRepository.save(threat);
            await responseActionRepository.save(responseAction);
            // Assert - Verify all entities are saved
            const savedEvent = await eventRepository.findById(event.id);
            const savedThreat = await threatRepository.findById(threat.id);
            const savedAction = await responseActionRepository.findById(responseAction.id);
            expect(savedEvent).toBeDefined();
            expect(savedThreat).toBeDefined();
            expect(savedAction).toBeDefined();
            // Verify relationships through queries
            const threatsByIp = await threatRepository.findBySourceIp(sourceIp);
            expect(threatsByIp).toHaveLength(1);
            expect(threatsByIp[0].id.equals(threat.id)).toBe(true);
            const blockIpActions = await responseActionRepository.findByType(action_type_enum_1.ActionType.BLOCK_IP);
            expect(blockIpActions).toHaveLength(1);
            expect(blockIpActions[0].id.equals(responseAction.id)).toBe(true);
        });
        it('should handle cascading operations correctly', async () => {
            // Arrange
            const tenantId = tenant_id_value_object_1.TenantId.create();
            const userId = user_id_value_object_1.UserId.create();
            // Create multiple related events
            const events = Array.from({ length: 5 }, (_, i) => event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: `source-${i}`, version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date(Date.now() + i * 1000)),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: `app-${i}`,
                    name: `App ${i}`,
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { attempt: i },
                tenantId,
                userId,
            }));
            // Act - Save all events
            for (const event of events) {
                await eventRepository.save(event);
            }
            // Assert - Verify batch operations
            const allEvents = await eventRepository.findAll();
            expect(allEvents).toHaveLength(5);
            const mediumSeverityEvents = await eventRepository.findBySeverity(event_severity_enum_1.EventSeverity.MEDIUM);
            expect(mediumSeverityEvents).toHaveLength(5);
            // Test batch deletion
            for (const event of events) {
                await eventRepository.delete(event.id);
            }
            const remainingEvents = await eventRepository.findAll();
            expect(remainingEvents).toHaveLength(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************