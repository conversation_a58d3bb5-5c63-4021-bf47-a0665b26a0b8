800b55e0244eb8830fb86d7620d582b6
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const supertest_1 = __importDefault(require("supertest"));
const app_module_1 = require("../../app.module");
describe('Authentication Workflow (e2e)', () => {
    let app;
    let authToken;
    let refreshToken;
    let userId;
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                app_module_1.AppModule,
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
    });
    afterAll(async () => {
        await app.close();
    });
    describe('User Registration Flow', () => {
        const testUser = {
            email: '<EMAIL>',
            password: 'SecurePassword123!',
            name: 'Test User',
        };
        it('should register a new user successfully', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/register')
                .send(testUser)
                .expect(201);
            expect(response.body).toEqual({
                success: true,
                data: {
                    user: {
                        id: expect.any(String),
                        email: testUser.email,
                        name: testUser.name,
                        isEmailVerified: false,
                        roles: ['user'],
                        createdAt: expect.any(String),
                    },
                    tokens: {
                        accessToken: expect.any(String),
                        refreshToken: expect.any(String),
                        expiresIn: expect.any(Number),
                    },
                },
                message: 'User registered successfully',
                timestamp: expect.any(String),
            });
            // Store tokens and user ID for subsequent tests
            authToken = response.body.data.tokens.accessToken;
            refreshToken = response.body.data.tokens.refreshToken;
            userId = response.body.data.user.id;
        });
        it('should reject duplicate email registration', async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/register')
                .send(testUser)
                .expect(409);
        });
        it('should validate registration input', async () => {
            const invalidUser = {
                email: 'invalid-email',
                password: '123', // Too short
                name: '', // Empty name
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/register')
                .send(invalidUser)
                .expect(400);
            expect(response.body.message).toContain('email must be an email');
            expect(response.body.message).toContain('password must be longer than');
            expect(response.body.message).toContain('name should not be empty');
        });
    });
    describe('User Login Flow', () => {
        it('should login with valid credentials', async () => {
            const loginData = {
                email: '<EMAIL>',
                password: 'SecurePassword123!',
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send(loginData)
                .expect(200);
            expect(response.body).toEqual({
                success: true,
                data: {
                    user: {
                        id: userId,
                        email: loginData.email,
                        name: 'Test User',
                        roles: ['user'],
                        permissions: expect.any(Array),
                    },
                    tokens: {
                        accessToken: expect.any(String),
                        refreshToken: expect.any(String),
                        expiresIn: expect.any(Number),
                    },
                    session: {
                        id: expect.any(String),
                        expiresAt: expect.any(String),
                    },
                },
                message: 'Login successful',
                timestamp: expect.any(String),
            });
            // Update tokens
            authToken = response.body.data.tokens.accessToken;
            refreshToken = response.body.data.tokens.refreshToken;
        });
        it('should reject invalid credentials', async () => {
            const invalidLogin = {
                email: '<EMAIL>',
                password: 'WrongPassword',
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send(invalidLogin)
                .expect(401);
            expect(response.body.message).toBe('Invalid credentials');
        });
        it('should reject login for non-existent user', async () => {
            const nonExistentLogin = {
                email: '<EMAIL>',
                password: 'SomePassword123!',
            };
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send(nonExistentLogin)
                .expect(401);
        });
    });
    describe('Protected Route Access', () => {
        it('should access protected route with valid token', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.data.user).toEqual({
                id: userId,
                email: '<EMAIL>',
                name: 'Test User',
                roles: ['user'],
                permissions: expect.any(Array),
                isEmailVerified: false,
                createdAt: expect.any(String),
                updatedAt: expect.any(String),
            });
        });
        it('should reject access without token', async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .expect(401);
        });
        it('should reject access with invalid token', async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .set('Authorization', 'Bearer invalid-token')
                .expect(401);
        });
        it('should reject access with expired token', async () => {
            const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid';
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .set('Authorization', `Bearer ${expiredToken}`)
                .expect(401);
        });
    });
    describe('Token Refresh Flow', () => {
        it('should refresh tokens with valid refresh token', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/refresh')
                .send({ refreshToken })
                .expect(200);
            expect(response.body).toEqual({
                success: true,
                data: {
                    tokens: {
                        accessToken: expect.any(String),
                        refreshToken: expect.any(String),
                        expiresIn: expect.any(Number),
                    },
                },
                message: 'Tokens refreshed successfully',
                timestamp: expect.any(String),
            });
            // Update tokens
            authToken = response.body.data.tokens.accessToken;
            refreshToken = response.body.data.tokens.refreshToken;
        });
        it('should reject refresh with invalid refresh token', async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/refresh')
                .send({ refreshToken: 'invalid-refresh-token' })
                .expect(401);
        });
        it('should work with new access token after refresh', async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
        });
    });
    describe('Password Change Flow', () => {
        it('should change password with valid current password', async () => {
            const passwordChangeData = {
                currentPassword: 'SecurePassword123!',
                newPassword: 'NewSecurePassword456!',
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .put('/api/v1/auth/change-password')
                .set('Authorization', `Bearer ${authToken}`)
                .send(passwordChangeData)
                .expect(200);
            expect(response.body.message).toBe('Password changed successfully');
        });
        it('should login with new password', async () => {
            const loginData = {
                email: '<EMAIL>',
                password: 'NewSecurePassword456!',
            };
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send(loginData)
                .expect(200);
        });
        it('should reject old password after change', async () => {
            const loginData = {
                email: '<EMAIL>',
                password: 'SecurePassword123!', // Old password
            };
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send(loginData)
                .expect(401);
        });
        it('should reject password change with wrong current password', async () => {
            const passwordChangeData = {
                currentPassword: 'WrongCurrentPassword',
                newPassword: 'AnotherNewPassword789!',
            };
            await (0, supertest_1.default)(app.getHttpServer())
                .put('/api/v1/auth/change-password')
                .set('Authorization', `Bearer ${authToken}`)
                .send(passwordChangeData)
                .expect(400);
        });
    });
    describe('Session Management', () => {
        it('should list active sessions', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/sessions')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.data.sessions).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    id: expect.any(String),
                    deviceInfo: expect.any(Object),
                    createdAt: expect.any(String),
                    lastAccessedAt: expect.any(String),
                    isCurrentSession: expect.any(Boolean),
                }),
            ]));
        });
        it('should revoke specific session', async () => {
            // First, get sessions to find a session ID
            const sessionsResponse = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/sessions')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            const sessionId = sessionsResponse.body.data.sessions[0].id;
            // Revoke the session
            await (0, supertest_1.default)(app.getHttpServer())
                .delete(`/api/v1/auth/sessions/${sessionId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
        });
        it('should revoke all other sessions', async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .delete('/api/v1/auth/sessions/others')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
        });
    });
    describe('Logout Flow', () => {
        it('should logout successfully', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/logout')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.message).toBe('Logout successful');
        });
        it('should not access protected routes after logout', async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(401);
        });
        it('should not refresh tokens after logout', async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/refresh')
                .send({ refreshToken })
                .expect(401);
        });
    });
    describe('Password Reset Flow', () => {
        it('should initiate password reset', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/forgot-password')
                .send({ email: '<EMAIL>' })
                .expect(200);
            expect(response.body.message).toBe('Password reset email sent');
        });
        it('should handle non-existent email for password reset', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/forgot-password')
                .send({ email: '<EMAIL>' })
                .expect(200); // Should return 200 for security reasons
            expect(response.body.message).toBe('Password reset email sent');
        });
        // Note: In a real implementation, you would test the actual reset flow
        // with a valid reset token, but that requires email integration
    });
    describe('Email Verification Flow', () => {
        it('should resend verification email', async () => {
            // Login first to get a valid token
            const loginResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'NewSecurePassword456!',
            })
                .expect(200);
            const newAuthToken = loginResponse.body.data.tokens.accessToken;
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/resend-verification')
                .set('Authorization', `Bearer ${newAuthToken}`)
                .expect(200);
            expect(response.body.message).toBe('Verification email sent');
        });
        // Note: In a real implementation, you would test the actual verification flow
        // with a valid verification token
    });
    describe('Role-Based Access Control', () => {
        it('should access user-level endpoints', async () => {
            // Login to get fresh token
            const loginResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'NewSecurePassword456!',
            })
                .expect(200);
            const userToken = loginResponse.body.data.tokens.accessToken;
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .set('Authorization', `Bearer ${userToken}`)
                .expect(200);
        });
        it('should reject admin-level endpoints for regular user', async () => {
            // Login to get fresh token
            const loginResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'NewSecurePassword456!',
            })
                .expect(200);
            const userToken = loginResponse.body.data.tokens.accessToken;
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/admin/users')
                .set('Authorization', `Bearer ${userToken}`)
                .expect(403);
        });
    });
    describe('Security Features', () => {
        it('should include security headers in responses', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'NewSecurePassword456!',
            })
                .expect(200);
            // Check for security headers (these would be set by middleware)
            expect(response.headers).toBeDefined();
        });
        it('should handle concurrent login attempts', async () => {
            const loginData = {
                email: '<EMAIL>',
                password: 'NewSecurePassword456!',
            };
            // Make multiple concurrent login requests
            const requests = Array(5).fill(null).map(() => (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send(loginData));
            const responses = await Promise.all(requests);
            // All should succeed (no race conditions)
            responses.forEach(response => {
                expect(response.status).toBe(200);
                expect(response.body.data.tokens.accessToken).toBeDefined();
            });
        });
        it('should validate JWT token format', async () => {
            const malformedTokens = [
                'not-a-jwt-token',
                'header.payload', // Missing signature
                'header.payload.signature.extra', // Too many parts
                '', // Empty token
            ];
            for (const token of malformedTokens) {
                await (0, supertest_1.default)(app.getHttpServer())
                    .get('/api/v1/auth/profile')
                    .set('Authorization', `Bearer ${token}`)
                    .expect(401);
            }
        });
    });
    describe('Error Handling', () => {
        it('should handle database connection errors gracefully', async () => {
            // This would require mocking database failures
            // In a real test, you might temporarily disconnect the database
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'NewSecurePassword456!',
            });
            // Should either succeed or return appropriate error
            expect([200, 503]).toContain(response.status);
        });
        it('should maintain consistent error format', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: 'invalid-email',
                password: 'short',
            })
                .expect(400);
            expect(response.body).toHaveProperty('statusCode');
            expect(response.body).toHaveProperty('message');
            expect(response.body).toHaveProperty('timestamp');
        });
    });
    describe('Performance', () => {
        it('should handle authentication requests efficiently', async () => {
            const startTime = Date.now();
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'NewSecurePassword456!',
            })
                .expect(200);
            const endTime = Date.now();
            const duration = endTime - startTime;
            // Authentication should complete within reasonable time
            expect(duration).toBeLessThan(2000); // 2 seconds
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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