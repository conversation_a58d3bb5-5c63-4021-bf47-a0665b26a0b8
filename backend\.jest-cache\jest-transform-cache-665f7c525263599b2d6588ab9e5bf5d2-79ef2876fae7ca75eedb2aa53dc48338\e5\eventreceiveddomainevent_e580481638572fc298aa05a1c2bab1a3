f797751173a25fa19e6a35958bfe49aa
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventReceivedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const event_severity_enum_1 = require("../enums/event-severity.enum");
/**
 * Event Received Domain Event
 *
 * Raised when a new security event is received by the system from external sources.
 * This is the first event in the security event processing pipeline and triggers
 * the normalization process.
 *
 * Key characteristics:
 * - Marks the entry point of events into the system
 * - Contains raw, unprocessed event data
 * - Triggers downstream processing workflows
 * - Used for intake metrics and monitoring
 *
 * Downstream processes triggered:
 * - Event validation and parsing
 * - Event normalization pipeline
 * - Initial threat assessment
 * - Event routing and classification
 * - Intake metrics collection
 */
class EventReceivedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, {
            eventVersion: 1,
            ...options,
            metadata: {
                eventType: 'EventReceived',
                domain: 'Security',
                aggregateType: 'Event',
                processingStage: 'intake',
                ...options?.metadata,
            },
        });
    }
    /**
     * Get the event type
     */
    get eventType() {
        return this.eventData.eventType;
    }
    /**
     * Get the event severity
     */
    get severity() {
        return this.eventData.severity;
    }
    /**
     * Get the event source
     */
    get source() {
        return this.eventData.source;
    }
    /**
     * Get when the event was received
     */
    get receivedAt() {
        return this.eventData.receivedAt;
    }
    /**
     * Get the original event timestamp
     */
    get originalTimestamp() {
        return this.eventData.originalTimestamp;
    }
    /**
     * Get the raw event data
     */
    get rawData() {
        return this.eventData.rawData;
    }
    /**
     * Get the event title
     */
    get title() {
        return this.eventData.title;
    }
    /**
     * Get the data size in bytes
     */
    get dataSize() {
        return this.eventData.dataSize;
    }
    /**
     * Get the processing priority
     */
    get priority() {
        return this.eventData.priority;
    }
    /**
     * Check if the event is high priority
     */
    isHighPriority() {
        return this.priority === 'high' || this.priority === 'critical';
    }
    /**
     * Check if the event is critical priority
     */
    isCriticalPriority() {
        return this.priority === 'critical';
    }
    /**
     * Check if the event is high severity
     */
    isHighSeverity() {
        return [event_severity_enum_1.EventSeverity.HIGH, event_severity_enum_1.EventSeverity.CRITICAL].includes(this.severity);
    }
    /**
     * Check if the event is critical severity
     */
    isCriticalSeverity() {
        return this.severity === event_severity_enum_1.EventSeverity.CRITICAL;
    }
    /**
     * Check if the event is large (over 1MB)
     */
    isLargeEvent() {
        return (this.dataSize || 0) > 1024 * 1024; // 1MB
    }
    /**
     * Check if the event is stale (received more than 5 minutes after original timestamp)
     */
    isStaleEvent() {
        const originalTime = this.originalTimestamp.toDate().getTime();
        const receivedTime = this.receivedAt.toDate().getTime();
        const fiveMinutes = 5 * 60 * 1000; // 5 minutes in milliseconds
        return (receivedTime - originalTime) > fiveMinutes;
    }
    /**
     * Get processing latency in milliseconds
     */
    getProcessingLatency() {
        const originalTime = this.originalTimestamp.toDate().getTime();
        const receivedTime = this.receivedAt.toDate().getTime();
        return Math.max(0, receivedTime - originalTime);
    }
    /**
     * Get processing latency in seconds
     */
    getProcessingLatencySeconds() {
        return Math.floor(this.getProcessingLatency() / 1000);
    }
    /**
     * Get recommended processing timeout in minutes
     */
    getRecommendedTimeout() {
        if (this.isCriticalPriority())
            return 1; // 1 minute
        if (this.isHighPriority())
            return 5; // 5 minutes
        if (this.isHighSeverity())
            return 15; // 15 minutes
        return 30; // 30 minutes default
    }
    /**
     * Get processing queue name
     */
    getProcessingQueue() {
        if (this.isCriticalPriority())
            return 'critical-events';
        if (this.isHighPriority())
            return 'high-priority-events';
        if (this.isHighSeverity())
            return 'high-severity-events';
        return 'standard-events';
    }
    /**
     * Get required processing resources
     */
    getProcessingResources() {
        const isComplex = this.isLargeEvent() || this.isHighSeverity();
        const isPriority = this.isHighPriority();
        return {
            cpu: isPriority || isComplex ? 'high' : 'medium',
            memory: this.isLargeEvent() ? 'high' : 'medium',
            storage: this.isLargeEvent() ? 'high' : 'low',
        };
    }
    /**
     * Get intake metrics
     */
    getIntakeMetrics() {
        return {
            eventType: this.eventType,
            severity: this.severity,
            sourceType: this.source.type,
            sourceIdentifier: this.source.identifier,
            priority: this.priority || 'normal',
            dataSize: this.dataSize || 0,
            processingLatency: this.getProcessingLatency(),
            isStale: this.isStaleEvent(),
            isLarge: this.isLargeEvent(),
            processingQueue: this.getProcessingQueue(),
        };
    }
    /**
     * Get quality indicators
     */
    getQualityIndicators() {
        const requiredFields = ['eventType', 'severity', 'source', 'title'];
        const hasRequiredFields = requiredFields.every(field => this.rawData[field] !== undefined && this.rawData[field] !== null);
        const hasValidTimestamp = this.originalTimestamp.isValid();
        const hasValidSource = this.source.isValid();
        // Calculate completeness based on available fields
        const totalFields = Object.keys(this.rawData).length;
        const nonEmptyFields = Object.values(this.rawData).filter(value => value !== null && value !== undefined && value !== '').length;
        const completeness = totalFields > 0 ? Math.round((nonEmptyFields / totalFields) * 100) : 0;
        let dataIntegrity = 'good';
        if (!hasRequiredFields || !hasValidTimestamp || !hasValidSource) {
            dataIntegrity = 'poor';
        }
        else if (completeness < 70) {
            dataIntegrity = 'fair';
        }
        return {
            hasRequiredFields,
            hasValidTimestamp,
            hasValidSource,
            dataIntegrity,
            completeness,
        };
    }
    /**
     * Get next processing steps
     */
    getNextProcessingSteps() {
        const steps = ['validate_event_structure'];
        const quality = this.getQualityIndicators();
        if (quality.dataIntegrity === 'poor') {
            steps.push('data_quality_remediation');
        }
        steps.push('normalize_event_format');
        if (this.isHighSeverity()) {
            steps.push('priority_threat_assessment');
        }
        if (this.isStaleEvent()) {
            steps.push('stale_event_handling');
        }
        if (this.isLargeEvent()) {
            steps.push('large_event_processing');
        }
        steps.push('route_to_processing_pipeline');
        return steps;
    }
    /**
     * Convert to integration event for external systems
     */
    toIntegrationEvent() {
        const quality = this.getQualityIndicators();
        return {
            eventType: 'EventReceived',
            version: '1.0',
            timestamp: this.occurredOn.toISOString(),
            data: {
                eventId: this.aggregateId.toString(),
                event: {
                    type: this.eventType,
                    severity: this.severity,
                    title: this.title,
                    receivedAt: this.receivedAt.toISOString(),
                    originalTimestamp: this.originalTimestamp.toISOString(),
                },
                source: {
                    type: this.source.type,
                    identifier: this.source.identifier,
                },
                processing: {
                    priority: this.priority || 'normal',
                    queue: this.getProcessingQueue(),
                    timeout: this.getRecommendedTimeout(),
                    latency: this.getProcessingLatency(),
                },
                quality: {
                    dataIntegrity: quality.dataIntegrity,
                    completeness: quality.completeness,
                    isStale: this.isStaleEvent(),
                    isLarge: this.isLargeEvent(),
                },
            },
            metadata: {
                correlationId: this.correlationId,
                causationId: this.causationId,
                domain: 'Security',
                aggregateType: 'Event',
                processingStage: 'intake',
            },
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventData: this.eventData,
            analysis: {
                isHighPriority: this.isHighPriority(),
                isCriticalPriority: this.isCriticalPriority(),
                isHighSeverity: this.isHighSeverity(),
                isCriticalSeverity: this.isCriticalSeverity(),
                isLargeEvent: this.isLargeEvent(),
                isStaleEvent: this.isStaleEvent(),
                processingLatency: this.getProcessingLatency(),
                processingQueue: this.getProcessingQueue(),
                recommendedTimeout: this.getRecommendedTimeout(),
                processingResources: this.getProcessingResources(),
                qualityIndicators: this.getQualityIndicators(),
                nextProcessingSteps: this.getNextProcessingSteps(),
            },
        };
    }
    /**
     * Create from JSON representation
     */
    static fromJSON(json) {
        return new EventReceivedDomainEvent(shared_kernel_1.UniqueEntityId.fromString(json.aggregateId), json.eventData, {
            eventId: shared_kernel_1.UniqueEntityId.fromString(json.eventId),
            occurredOn: new Date(json.occurredOn),
            eventVersion: json.eventVersion,
            correlationId: json.correlationId,
            causationId: json.causationId,
            metadata: json.metadata,
        });
    }
    /**
     * Get human-readable description
     */
    getDescription() {
        const severityText = this.severity.toLowerCase();
        const priorityText = this.priority ? ` (${this.priority} priority)` : '';
        const staleText = this.isStaleEvent() ? ' [STALE]' : '';
        const largeText = this.isLargeEvent() ? ' [LARGE]' : '';
        return `${severityText} severity ${this.eventType} event received from ${this.source.identifier}${priorityText}${staleText}${largeText}`;
    }
    /**
     * Get event summary for logging
     */
    getSummary() {
        const quality = this.getQualityIndicators();
        return {
            eventType: 'EventReceived',
            eventId: this.aggregateId.toString(),
            severity: this.severity,
            source: this.source.identifier,
            priority: this.priority || 'normal',
            processingLatency: this.getProcessingLatency(),
            qualityScore: quality.completeness,
            timestamp: this.occurredOn.toISOString(),
        };
    }
}
exports.EventReceivedDomainEvent = EventReceivedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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