{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\threat-detected.domain-event.spec.ts", "mappings": ";;AAAA,kFAAqG;AACrG,gEAA8D;AAC9D,2EAAkE;AAClE,kHAAiG;AACjG,uHAAsG;AACtG,2GAA0F;AAC1F,iGAAgF;AAEhF,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;IACzC,IAAI,WAA2B,CAAC;IAChC,IAAI,SAAkC,CAAC;IAEvC,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;QACxC,SAAS,GAAG;YACV,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,qCAAqC;YAC3C,WAAW,EAAE,2DAA2D;YACxE,QAAQ,EAAE,qCAAc,CAAC,QAAQ;YACjC,QAAQ,EAAE,4BAA4B;YACtC,IAAI,EAAE,mBAAmB;YACzB,SAAS,EAAE,+CAAe,CAAC,MAAM,CAAC;gBAChC,EAAE,EAAE,SAAS;gBACb,OAAO,EAAE,oBAAoB;gBAC7B,SAAS,EAAE,MAAM;gBACjB,UAAU,EAAE,EAAE;aACf,CAAC;YACF,SAAS,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAChC,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,6CAAc,CAAC,GAAG,EAAE;YAChC,eAAe,EAAE,qBAAqB;YACtC,eAAe,EAAE,kCAAkC;YACnD,QAAQ,EAAE,mCAAS,CAAC,MAAM,CAAC,eAAe,CAAC;YAC3C,QAAQ,EAAE,mCAAS,CAAC,MAAM,CAAC,WAAW,CAAC;YACvC,cAAc,EAAE;gBACd;oBACE,EAAE,EAAE,WAAW;oBACf,IAAI,EAAE,2BAA2B;oBACjC,IAAI,EAAE,UAAU;oBAChB,WAAW,EAAE,UAAU;iBACxB;gBACD;oBACE,EAAE,EAAE,WAAW;oBACf,IAAI,EAAE,wBAAwB;oBAC9B,IAAI,EAAE,YAAY;oBAClB,WAAW,EAAE,MAAM;iBACpB;aACF;YACD,UAAU,EAAE;gBACV;oBACE,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,eAAe;oBACtB,UAAU,EAAE,EAAE;oBACd,MAAM,EAAE,qBAAqB;iBAC9B;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,KAAK,EAAE,0CAA0C;oBACjD,UAAU,EAAE,EAAE;oBACd,MAAM,EAAE,kBAAkB;iBAC3B;aACF;YACD,aAAa,EAAE,CAAC,UAAU,EAAE,kBAAkB,EAAE,sBAAsB,CAAC;YACvE,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,WAAW;gBAClB,UAAU,EAAE,WAAW;gBACvB,cAAc,EAAE,UAAU;aAC3B;YACD,cAAc,EAAE,oBAAoB;YACpC,eAAe,EAAE;gBACf,eAAe,EAAE,MAAM;gBACvB,SAAS,EAAE,MAAM;gBACjB,YAAY,EAAE,KAAK;gBACnB,KAAK,EAAE,SAAS;gBAChB,cAAc,EAAE,UAAU;aAC3B;YACD,aAAa,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;YACzC,kBAAkB,EAAE;gBAClB,SAAS,EAAE,CAAC,iBAAiB,CAAC;gBAC9B,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;gBAChC,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;gBAClC,WAAW,EAAE;oBACX,OAAO,EAAE,QAAQ;oBACjB,MAAM,EAAE,gBAAgB;oBACxB,WAAW,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE;iBAC5C;aACF;YACD,kBAAkB,EAAE;gBAClB,kBAAkB,EAAE,IAAI;gBACxB,cAAc,EAAE,uBAAuB;gBACvC,aAAa,EAAE,OAAO;gBACtB,aAAa,EAAE,UAAU;aAC1B;SACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAEpE,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1C,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACrE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAEpE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,aAAa,GAAG,qBAAqB,CAAC;YAC5C,MAAM,cAAc,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;YAEtD,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,EAAE;gBAClE,aAAa;gBACb,QAAQ,EAAE,cAAc;aACzB,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAEpE,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,gBAAgB,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,qCAAc,CAAC,IAAI,EAAE,CAAC;YACzE,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;YAE3E,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,kBAAkB,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,qCAAc,CAAC,MAAM,EAAE,CAAC;YAC7E,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;YAE7E,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,iBAAiB,GAAG,EAAE,GAAG,SAAS,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;YAC3D,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;YAE5E,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,oBAAoB,GAAG,EAAE,GAAG,SAAS,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;YAC9D,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC;YAE/E,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,gBAAgB,GAAG,EAAE,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;YACzD,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;YAE3E,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,WAAW,GAAG,EAAE,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;YACpD,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAEtE,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,eAAe,GAAG;gBACtB,GAAG,SAAS;gBACZ,cAAc,EAAE,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;aAC9C,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAE1E,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,mBAAmB,GAAG;gBAC1B,GAAG,SAAS;gBACZ,UAAU,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;aACtC,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;YAE9E,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,WAAW,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YACrC,OAAO,WAAW,CAAC,WAAW,CAAC;YAC/B,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAEtE,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,cAAc,GAAG,EAAE,GAAG,SAAS,EAAE,cAAc,EAAE,gBAAyB,EAAE,CAAC;YACnF,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YAEzE,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,aAAa,GAAG;gBACpB,GAAG,SAAS;gBACZ,eAAe,EAAE;oBACf,GAAG,SAAS,CAAC,eAAe;oBAC5B,cAAc,EAAE,KAAc;iBAC/B;aACF,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YAExE,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,WAAW,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YACrC,OAAO,WAAW,CAAC,kBAAkB,CAAC;YACtC,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAEtE,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,eAAe,GAAG;gBACtB,GAAG,SAAS;gBACZ,QAAQ,EAAE,qCAAc,CAAC,GAAG;gBAC5B,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,EAAE;gBACb,cAAc,EAAE,CAAC;wBACf,EAAE,EAAE,WAAW;wBACf,IAAI,EAAE,aAAa;wBACnB,IAAI,EAAE,MAAM;wBACZ,WAAW,EAAE,KAAK;qBACnB,CAAC;aACH,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAE1E,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,gBAAgB,GAAG;gBACvB,GAAG,SAAS;gBACZ,QAAQ,EAAE,qCAAc,CAAC,IAAI;gBAC7B,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,EAAE;aACd,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;YAE3E,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,kBAAkB,GAAG;gBACzB,GAAG,SAAS;gBACZ,QAAQ,EAAE,qCAAc,CAAC,MAAM;gBAC/B,SAAS,EAAE,EAAE;aACd,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;YAE7E,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,eAAe,GAAG;gBACtB,GAAG,SAAS;gBACZ,QAAQ,EAAE,qCAAc,CAAC,GAAG;gBAC5B,SAAS,EAAE,EAAE;aACd,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAE1E,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,gBAAgB,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,qCAAc,CAAC,IAAI,EAAE,CAAC;YACzE,MAAM,kBAAkB,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,qCAAc,CAAC,MAAM,EAAE,CAAC;YAC7E,MAAM,eAAe,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,qCAAc,CAAC,GAAG,EAAE,CAAC;YAEvE,MAAM,SAAS,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;YAC/E,MAAM,WAAW,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;YACnF,MAAM,QAAQ,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAE7E,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU;YAC/D,MAAM,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU;YACjE,MAAM,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,gBAAgB,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,qCAAc,CAAC,IAAI,EAAE,CAAC;YACzE,MAAM,kBAAkB,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,qCAAc,CAAC,MAAM,EAAE,CAAC;YAC7E,MAAM,eAAe,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,qCAAc,CAAC,GAAG,EAAE,CAAC;YAEvE,MAAM,SAAS,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;YAC/E,MAAM,WAAW,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;YACnF,MAAM,QAAQ,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAE7E,MAAM,CAAC,SAAS,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW;YAClE,MAAM,CAAC,WAAW,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;YAClE,MAAM,CAAC,QAAQ,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,uEAAuE,EAAE,GAAG,EAAE;YAC/E,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,KAAK,CAAC,gCAAgC,EAAE,CAAC;YAEzD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,eAAe,GAAG;gBACtB,GAAG,SAAS;gBACZ,QAAQ,EAAE,qCAAc,CAAC,GAAG;gBAC5B,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,EAAE;gBACb,cAAc,EAAE,CAAC;wBACf,EAAE,EAAE,WAAW;wBACf,IAAI,EAAE,aAAa;wBACnB,IAAI,EAAE,MAAM;wBACZ,WAAW,EAAE,KAAK;qBACnB,CAAC;gBACF,cAAc,EAAE,gBAAyB;aAC1C,CAAC;YACF,OAAO,eAAe,CAAC,QAAQ,CAAC;YAChC,OAAO,eAAe,CAAC,WAAW,CAAC;YAEnC,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAC1E,MAAM,OAAO,GAAG,KAAK,CAAC,gCAAgC,EAAE,CAAC;YAEzD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,wCAAwC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,eAAe,GAAG;gBACtB,GAAG,SAAS;gBACZ,QAAQ,EAAE,qCAAc,CAAC,GAAG;gBAC5B,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,EAAE;gBACb,eAAe,EAAE;oBACf,GAAG,SAAS,CAAC,eAAe;oBAC5B,cAAc,EAAE,KAAc;iBAC/B;gBACD,cAAc,EAAE,CAAC;wBACf,EAAE,EAAE,WAAW;wBACf,IAAI,EAAE,aAAa;wBACnB,IAAI,EAAE,MAAM;wBACZ,WAAW,EAAE,KAAK;qBACnB,CAAC;aACH,CAAC;YACF,OAAO,eAAe,CAAC,WAAW,CAAC;YAEnC,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAC1E,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;YAElD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAEpD,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC1D,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1D,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACtF,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;YAC5E,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC/E,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5E,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAE5B,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAC5B,MAAM,iBAAiB,GAAG,wDAAyB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEnE,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3E,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC5D,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;YAE3C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YACnD,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC;YACrE,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YAC3D,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAC7E,MAAM,WAAW,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YACrC,OAAO,WAAW,CAAC,WAAW,CAAC;YAC/B,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACtE,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;YAE3C,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YAEnC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,UAAU,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YACpC,OAAO,UAAU,CAAC,SAAS,CAAC;YAE5B,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAErE,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,QAAQ,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YAClC,OAAO,QAAQ,CAAC,QAAQ,CAAC;YACzB,OAAO,QAAQ,CAAC,QAAQ,CAAC;YAEzB,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YACnE,MAAM,OAAO,GAAG,KAAK,CAAC,gCAAgC,EAAE,CAAC;YAEzD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,aAAa,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YACvC,OAAO,aAAa,CAAC,aAAa,CAAC;YAEnC,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YAExE,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,aAAa,EAAE,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,gBAAgB,GAAG,EAAE,GAAG,SAAS,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;YAC1D,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;YAE3E,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,YAAY,GAAG,EAAE,GAAG,SAAS,EAAE,cAAc,EAAE,EAAE,EAAE,CAAC;YAC1D,MAAM,KAAK,GAAG,IAAI,wDAAyB,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAEvE,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\threat-detected.domain-event.spec.ts"], "sourcesContent": ["import { ThreatDetectedDomainEvent, ThreatDetectedEventData } from '../threat-detected.domain-event';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { ThreatSeverity } from '../../enums/threat-severity.enum';\r\nimport { EventTimestamp } from '../../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { ThreatSignature } from '../../value-objects/threat-indicators/threat-signature.value-object';\r\nimport { CvssScore } from '../../value-objects/threat-indicators/cvss-score.value-object';\r\nimport { IpAddress } from '../../value-objects/network/ip-address.value-object';\r\n\r\ndescribe('ThreatDetectedDomainEvent', () => {\r\n  let aggregateId: UniqueEntityId;\r\n  let eventData: ThreatDetectedEventData;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.generate();\r\n    eventData = {\r\n      threatId: 'threat-001',\r\n      name: 'Advanced Persistent Threat Campaign',\r\n      description: 'Sophisticated multi-stage attack targeting financial data',\r\n      severity: ThreatSeverity.CRITICAL,\r\n      category: 'Advanced Persistent Threat',\r\n      type: 'Data Exfiltration',\r\n      signature: ThreatSignature.create({\r\n        id: 'sig-001',\r\n        pattern: 'APT-FINANCIAL-2024',\r\n        algorithm: 'yara',\r\n        confidence: 95\r\n      }),\r\n      cvssScore: CvssScore.create(8.5),\r\n      confidence: 92,\r\n      riskScore: 88,\r\n      detectedAt: EventTimestamp.now(),\r\n      detectionMethod: 'behavioral_analysis',\r\n      detectionSource: 'advanced-threat-detection-engine',\r\n      sourceIp: IpAddress.create('*************'),\r\n      targetIp: IpAddress.create('*********'),\r\n      affectedAssets: [\r\n        {\r\n          id: 'asset-001',\r\n          name: 'Financial Database Server',\r\n          type: 'database',\r\n          criticality: 'critical'\r\n        },\r\n        {\r\n          id: 'asset-002',\r\n          name: 'Web Application Server',\r\n          type: 'web_server',\r\n          criticality: 'high'\r\n        }\r\n      ],\r\n      indicators: [\r\n        {\r\n          type: 'ip',\r\n          value: '*************',\r\n          confidence: 90,\r\n          source: 'threat-intelligence'\r\n        },\r\n        {\r\n          type: 'file_hash',\r\n          value: 'a1b2c3d4e5f6789012345678901234567890abcd',\r\n          confidence: 95,\r\n          source: 'malware-analysis'\r\n        }\r\n      ],\r\n      attackVectors: ['phishing', 'lateral_movement', 'privilege_escalation'],\r\n      threatActor: {\r\n        name: 'APT-29',\r\n        group: 'Cozy Bear',\r\n        motivation: 'espionage',\r\n        sophistication: 'advanced'\r\n      },\r\n      killChainStage: 'actions_objectives',\r\n      potentialImpact: {\r\n        confidentiality: 'high',\r\n        integrity: 'high',\r\n        availability: 'low',\r\n        scope: 'changed',\r\n        businessImpact: 'critical'\r\n      },\r\n      relatedEvents: ['event-001', 'event-002'],\r\n      threatIntelligence: {\r\n        campaigns: ['Operation Ghost'],\r\n        ttps: ['T1566.001', 'T1078.004'],\r\n        mitreAttackIds: ['T1566', 'T1078'],\r\n        geolocation: {\r\n          country: 'Russia',\r\n          region: 'Eastern Europe',\r\n          coordinates: { lat: 55.7558, lon: 37.6176 }\r\n        }\r\n      },\r\n      processingMetadata: {\r\n        processingDuration: 2500,\r\n        analysisEngine: 'ThreatDetectionEngine',\r\n        engineVersion: '3.2.1',\r\n        correlationId: 'corr-001'\r\n      }\r\n    };\r\n  });\r\n\r\n  describe('constructor', () => {\r\n    it('should create event with valid data', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.aggregateId).toEqual(aggregateId);\r\n      expect(event.threatId).toBe('threat-001');\r\n      expect(event.threatName).toBe('Advanced Persistent Threat Campaign');\r\n      expect(event.severity).toBe(ThreatSeverity.CRITICAL);\r\n      expect(event.confidence).toBe(92);\r\n      expect(event.riskScore).toBe(88);\r\n    });\r\n\r\n    it('should set correct metadata', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.metadata.eventType).toBe('ThreatDetected');\r\n      expect(event.metadata.domain).toBe('Security');\r\n      expect(event.metadata.aggregateType).toBe('Threat');\r\n      expect(event.metadata.processingStage).toBe('detection');\r\n    });\r\n\r\n    it('should accept custom options', () => {\r\n      const correlationId = 'test-correlation-id';\r\n      const customMetadata = { customField: 'customValue' };\r\n\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData, {\r\n        correlationId,\r\n        metadata: customMetadata\r\n      });\r\n\r\n      expect(event.correlationId).toBe(correlationId);\r\n      expect(event.metadata.customField).toBe('customValue');\r\n    });\r\n  });\r\n\r\n  describe('severity checks', () => {\r\n    it('should identify critical severity threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      \r\n      expect(event.isCriticalSeverity()).toBe(true);\r\n      expect(event.isHighSeverityOrAbove()).toBe(true);\r\n    });\r\n\r\n    it('should identify high severity threats', () => {\r\n      const highSeverityData = { ...eventData, severity: ThreatSeverity.HIGH };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, highSeverityData);\r\n      \r\n      expect(event.isCriticalSeverity()).toBe(false);\r\n      expect(event.isHighSeverityOrAbove()).toBe(true);\r\n    });\r\n\r\n    it('should identify medium severity threats', () => {\r\n      const mediumSeverityData = { ...eventData, severity: ThreatSeverity.MEDIUM };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, mediumSeverityData);\r\n      \r\n      expect(event.isCriticalSeverity()).toBe(false);\r\n      expect(event.isHighSeverityOrAbove()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('confidence checks', () => {\r\n    it('should identify high confidence threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasHighConfidence()).toBe(true);\r\n      expect(event.hasLowConfidence()).toBe(false);\r\n    });\r\n\r\n    it('should identify low confidence threats', () => {\r\n      const lowConfidenceData = { ...eventData, confidence: 45 };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, lowConfidenceData);\r\n      \r\n      expect(event.hasHighConfidence()).toBe(false);\r\n      expect(event.hasLowConfidence()).toBe(true);\r\n    });\r\n\r\n    it('should identify medium confidence threats', () => {\r\n      const mediumConfidenceData = { ...eventData, confidence: 65 };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, mediumConfidenceData);\r\n      \r\n      expect(event.hasHighConfidence()).toBe(false);\r\n      expect(event.hasLowConfidence()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('risk score checks', () => {\r\n    it('should identify high risk threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasHighRiskScore()).toBe(true);\r\n    });\r\n\r\n    it('should identify critical risk threats', () => {\r\n      const criticalRiskData = { ...eventData, riskScore: 95 };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, criticalRiskData);\r\n      \r\n      expect(event.hasCriticalRiskScore()).toBe(true);\r\n      expect(event.hasHighRiskScore()).toBe(true);\r\n    });\r\n\r\n    it('should identify low risk threats', () => {\r\n      const lowRiskData = { ...eventData, riskScore: 35 };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, lowRiskData);\r\n      \r\n      expect(event.hasHighRiskScore()).toBe(false);\r\n      expect(event.hasCriticalRiskScore()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('asset impact checks', () => {\r\n    it('should identify threats affecting critical assets', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.affectsCriticalAssets()).toBe(true);\r\n    });\r\n\r\n    it('should identify threats affecting multiple assets', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.affectsMultipleAssets()).toBe(true);\r\n    });\r\n\r\n    it('should identify threats affecting single asset', () => {\r\n      const singleAssetData = {\r\n        ...eventData,\r\n        affectedAssets: [eventData.affectedAssets[0]]\r\n      };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, singleAssetData);\r\n      \r\n      expect(event.affectsMultipleAssets()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('indicator checks', () => {\r\n    it('should identify threats with multiple indicators', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasMultipleIndicators()).toBe(true);\r\n    });\r\n\r\n    it('should identify threats with high-confidence indicators', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasHighConfidenceIndicators()).toBe(true);\r\n    });\r\n\r\n    it('should identify threats with single indicator', () => {\r\n      const singleIndicatorData = {\r\n        ...eventData,\r\n        indicators: [eventData.indicators[0]]\r\n      };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, singleIndicatorData);\r\n      \r\n      expect(event.hasMultipleIndicators()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('threat actor checks', () => {\r\n    it('should identify threats with known threat actors', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasKnownThreatActor()).toBe(true);\r\n    });\r\n\r\n    it('should identify advanced threat actors', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasAdvancedThreatActor()).toBe(true);\r\n    });\r\n\r\n    it('should identify threats without known actors', () => {\r\n      const noActorData = { ...eventData };\r\n      delete noActorData.threatActor;\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, noActorData);\r\n      \r\n      expect(event.hasKnownThreatActor()).toBe(false);\r\n      expect(event.hasAdvancedThreatActor()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('kill chain analysis', () => {\r\n    it('should identify late kill chain stages', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.isLateKillChainStage()).toBe(true);\r\n    });\r\n\r\n    it('should identify early kill chain stages', () => {\r\n      const earlyStageData = { ...eventData, killChainStage: 'reconnaissance' as const };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, earlyStageData);\r\n      \r\n      expect(event.isLateKillChainStage()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('business impact checks', () => {\r\n    it('should identify high business impact threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasHighBusinessImpact()).toBe(true);\r\n    });\r\n\r\n    it('should identify critical business impact threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasCriticalBusinessImpact()).toBe(true);\r\n    });\r\n\r\n    it('should identify low business impact threats', () => {\r\n      const lowImpactData = {\r\n        ...eventData,\r\n        potentialImpact: {\r\n          ...eventData.potentialImpact,\r\n          businessImpact: 'low' as const\r\n        }\r\n      };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, lowImpactData);\r\n      \r\n      expect(event.hasHighBusinessImpact()).toBe(false);\r\n      expect(event.hasCriticalBusinessImpact()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('threat intelligence checks', () => {\r\n    it('should identify threats with geolocation data', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasGeolocationData()).toBe(true);\r\n    });\r\n\r\n    it('should identify threats part of known campaigns', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.isPartOfKnownCampaign()).toBe(true);\r\n    });\r\n\r\n    it('should identify threats without intelligence context', () => {\r\n      const noIntelData = { ...eventData };\r\n      delete noIntelData.threatIntelligence;\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, noIntelData);\r\n      \r\n      expect(event.hasGeolocationData()).toBe(false);\r\n      expect(event.isPartOfKnownCampaign()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('response requirements', () => {\r\n    it('should require immediate response for critical threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.requiresImmediateResponse()).toBe(true);\r\n    });\r\n\r\n    it('should require executive notification for critical threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.requiresExecutiveNotification()).toBe(true);\r\n    });\r\n\r\n    it('should require SOC escalation for high severity threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.requiresSOCEscalation()).toBe(true);\r\n    });\r\n\r\n    it('should not require immediate response for low severity threats', () => {\r\n      const lowSeverityData = {\r\n        ...eventData,\r\n        severity: ThreatSeverity.LOW,\r\n        confidence: 50,\r\n        riskScore: 30,\r\n        affectedAssets: [{\r\n          id: 'asset-001',\r\n          name: 'Test Server',\r\n          type: 'test',\r\n          criticality: 'low'\r\n        }]\r\n      };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, lowSeverityData);\r\n      \r\n      expect(event.requiresImmediateResponse()).toBe(false);\r\n      expect(event.requiresExecutiveNotification()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('response priority', () => {\r\n    it('should assign critical priority to immediate response threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.getResponsePriority()).toBe('critical');\r\n    });\r\n\r\n    it('should assign high priority to high severity threats', () => {\r\n      const highSeverityData = {\r\n        ...eventData,\r\n        severity: ThreatSeverity.HIGH,\r\n        confidence: 60,\r\n        riskScore: 60\r\n      };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, highSeverityData);\r\n      \r\n      expect(event.getResponsePriority()).toBe('high');\r\n    });\r\n\r\n    it('should assign medium priority to medium severity threats', () => {\r\n      const mediumSeverityData = {\r\n        ...eventData,\r\n        severity: ThreatSeverity.MEDIUM,\r\n        riskScore: 50\r\n      };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, mediumSeverityData);\r\n      \r\n      expect(event.getResponsePriority()).toBe('medium');\r\n    });\r\n\r\n    it('should assign low priority to low severity threats', () => {\r\n      const lowSeverityData = {\r\n        ...eventData,\r\n        severity: ThreatSeverity.LOW,\r\n        riskScore: 30\r\n      };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, lowSeverityData);\r\n      \r\n      expect(event.getResponsePriority()).toBe('low');\r\n    });\r\n  });\r\n\r\n  describe('containment urgency', () => {\r\n    it('should set short containment time for immediate response threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.getContainmentUrgency()).toBe(15); // 15 minutes\r\n    });\r\n\r\n    it('should set appropriate containment times by severity', () => {\r\n      const highSeverityData = { ...eventData, severity: ThreatSeverity.HIGH };\r\n      const mediumSeverityData = { ...eventData, severity: ThreatSeverity.MEDIUM };\r\n      const lowSeverityData = { ...eventData, severity: ThreatSeverity.LOW };\r\n\r\n      const highEvent = new ThreatDetectedDomainEvent(aggregateId, highSeverityData);\r\n      const mediumEvent = new ThreatDetectedDomainEvent(aggregateId, mediumSeverityData);\r\n      const lowEvent = new ThreatDetectedDomainEvent(aggregateId, lowSeverityData);\r\n\r\n      expect(highEvent.getContainmentUrgency()).toBe(120); // 2 hours\r\n      expect(mediumEvent.getContainmentUrgency()).toBe(480); // 8 hours\r\n      expect(lowEvent.getContainmentUrgency()).toBe(1440); // 24 hours\r\n    });\r\n  });\r\n\r\n  describe('investigation timeline', () => {\r\n    it('should set short investigation time for immediate response threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      expect(event.getInvestigationTimeline()).toBe(2); // 2 hours\r\n    });\r\n\r\n    it('should set appropriate investigation times by severity', () => {\r\n      const highSeverityData = { ...eventData, severity: ThreatSeverity.HIGH };\r\n      const mediumSeverityData = { ...eventData, severity: ThreatSeverity.MEDIUM };\r\n      const lowSeverityData = { ...eventData, severity: ThreatSeverity.LOW };\r\n\r\n      const highEvent = new ThreatDetectedDomainEvent(aggregateId, highSeverityData);\r\n      const mediumEvent = new ThreatDetectedDomainEvent(aggregateId, mediumSeverityData);\r\n      const lowEvent = new ThreatDetectedDomainEvent(aggregateId, lowSeverityData);\r\n\r\n      expect(highEvent.getInvestigationTimeline()).toBe(24); // 24 hours\r\n      expect(mediumEvent.getInvestigationTimeline()).toBe(72); // 3 days\r\n      expect(lowEvent.getInvestigationTimeline()).toBe(168); // 7 days\r\n    });\r\n  });\r\n\r\n  describe('containment actions', () => {\r\n    it('should recommend comprehensive actions for immediate response threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      const actions = event.getRecommendedContainmentActions();\r\n      \r\n      expect(actions).toContain('isolate_affected_systems');\r\n      expect(actions).toContain('block_malicious_ips');\r\n      expect(actions).toContain('disable_compromised_accounts');\r\n      expect(actions).toContain('block_source_ip');\r\n      expect(actions).toContain('emergency_asset_isolation');\r\n      expect(actions).toContain('advanced_forensics');\r\n      expect(actions).toContain('damage_assessment');\r\n    });\r\n\r\n    it('should recommend basic actions for low severity threats', () => {\r\n      const lowSeverityData = {\r\n        ...eventData,\r\n        severity: ThreatSeverity.LOW,\r\n        confidence: 50,\r\n        riskScore: 30,\r\n        affectedAssets: [{\r\n          id: 'asset-001',\r\n          name: 'Test Server',\r\n          type: 'test',\r\n          criticality: 'low'\r\n        }],\r\n        killChainStage: 'reconnaissance' as const\r\n      };\r\n      delete lowSeverityData.sourceIp;\r\n      delete lowSeverityData.threatActor;\r\n      \r\n      const event = new ThreatDetectedDomainEvent(aggregateId, lowSeverityData);\r\n      const actions = event.getRecommendedContainmentActions();\r\n      \r\n      expect(actions).toHaveLength(0); // No immediate actions for low severity\r\n    });\r\n  });\r\n\r\n  describe('notification targets', () => {\r\n    it('should include comprehensive targets for critical threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      const targets = event.getNotificationTargets();\r\n      \r\n      expect(targets).toContain('security_operations_center');\r\n      expect(targets).toContain('security_leadership');\r\n      expect(targets).toContain('executive_team');\r\n      expect(targets).toContain('incident_response_team');\r\n      expect(targets).toContain('asset_owners');\r\n      expect(targets).toContain('threat_intelligence_team');\r\n      expect(targets).toContain('business_continuity_team');\r\n    });\r\n\r\n    it('should include basic targets for low severity threats', () => {\r\n      const lowSeverityData = {\r\n        ...eventData,\r\n        severity: ThreatSeverity.LOW,\r\n        confidence: 50,\r\n        riskScore: 30,\r\n        potentialImpact: {\r\n          ...eventData.potentialImpact,\r\n          businessImpact: 'low' as const\r\n        },\r\n        affectedAssets: [{\r\n          id: 'asset-001',\r\n          name: 'Test Server',\r\n          type: 'test',\r\n          criticality: 'low'\r\n        }]\r\n      };\r\n      delete lowSeverityData.threatActor;\r\n      \r\n      const event = new ThreatDetectedDomainEvent(aggregateId, lowSeverityData);\r\n      const targets = event.getNotificationTargets();\r\n      \r\n      expect(targets).toContain('security_operations_center');\r\n      expect(targets).not.toContain('executive_team');\r\n      expect(targets).not.toContain('threat_intelligence_team');\r\n    });\r\n  });\r\n\r\n  describe('threat detection metrics', () => {\r\n    it('should generate comprehensive metrics', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      const metrics = event.getThreatDetectionMetrics();\r\n      \r\n      expect(metrics.threatId).toBe('threat-001');\r\n      expect(metrics.name).toBe('Advanced Persistent Threat Campaign');\r\n      expect(metrics.severity).toBe(ThreatSeverity.CRITICAL);\r\n      expect(metrics.confidence).toBe(92);\r\n      expect(metrics.riskScore).toBe(88);\r\n      expect(metrics.affectedAssetsCount).toBe(2);\r\n      expect(metrics.indicatorsCount).toBe(2);\r\n      expect(metrics.requiresImmediateResponse).toBe(true);\r\n      expect(metrics.affectsCriticalAssets).toBe(true);\r\n      expect(metrics.hasAdvancedThreatActor).toBe(true);\r\n      expect(metrics.processingDuration).toBe(2500);\r\n    });\r\n  });\r\n\r\n  describe('integration event conversion', () => {\r\n    it('should convert to integration event format', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      const integrationEvent = event.toIntegrationEvent();\r\n      \r\n      expect(integrationEvent.eventType).toBe('ThreatDetected');\r\n      expect(integrationEvent.version).toBe('1.0');\r\n      expect(integrationEvent.data.threatId).toBe('threat-001');\r\n      expect(integrationEvent.data.threat.name).toBe('Advanced Persistent Threat Campaign');\r\n      expect(integrationEvent.data.threat.severity).toBe(ThreatSeverity.CRITICAL);\r\n      expect(integrationEvent.data.assets.criticalAssetsAffected).toBe(true);\r\n      expect(integrationEvent.data.indicators.count).toBe(2);\r\n      expect(integrationEvent.data.attack.killChainStage).toBe('actions_objectives');\r\n      expect(integrationEvent.data.response.requiresImmediateResponse).toBe(true);\r\n      expect(integrationEvent.data.intelligence?.campaigns).toContain('Operation Ghost');\r\n    });\r\n  });\r\n\r\n  describe('JSON serialization', () => {\r\n    it('should serialize to JSON correctly', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      const json = event.toJSON();\r\n      \r\n      expect(json.eventData).toEqual(eventData);\r\n      expect(json.analysis.isCriticalSeverity).toBe(true);\r\n      expect(json.analysis.hasHighConfidence).toBe(true);\r\n      expect(json.analysis.requiresImmediateResponse).toBe(true);\r\n      expect(json.analysis.responsePriority).toBe('critical');\r\n    });\r\n\r\n    it('should deserialize from JSON correctly', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      const json = event.toJSON();\r\n      const deserializedEvent = ThreatDetectedDomainEvent.fromJSON(json);\r\n      \r\n      expect(deserializedEvent.aggregateId.equals(event.aggregateId)).toBe(true);\r\n      expect(deserializedEvent.threatId).toBe(event.threatId);\r\n      expect(deserializedEvent.threatName).toBe(event.threatName);\r\n      expect(deserializedEvent.severity).toBe(event.severity);\r\n    });\r\n  });\r\n\r\n  describe('human-readable description', () => {\r\n    it('should generate appropriate description for critical threats', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      const description = event.getDescription();\r\n      \r\n      expect(description).toContain('critical severity');\r\n      expect(description).toContain('Advanced Persistent Threat Campaign');\r\n      expect(description).toContain('high confidence');\r\n      expect(description).toContain('affecting critical assets');\r\n      expect(description).toContain('APT-29');\r\n    });\r\n\r\n    it('should generate description without threat actor for unknown actors', () => {\r\n      const noActorData = { ...eventData };\r\n      delete noActorData.threatActor;\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, noActorData);\r\n      const description = event.getDescription();\r\n      \r\n      expect(description).not.toContain('by ');\r\n      expect(description).toContain('critical severity');\r\n    });\r\n  });\r\n\r\n  describe('event summary', () => {\r\n    it('should generate comprehensive summary', () => {\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, eventData);\r\n      const summary = event.getSummary();\r\n      \r\n      expect(summary.eventType).toBe('ThreatDetected');\r\n      expect(summary.threatId).toBe('threat-001');\r\n      expect(summary.name).toBe('Advanced Persistent Threat Campaign');\r\n      expect(summary.severity).toBe(ThreatSeverity.CRITICAL);\r\n      expect(summary.confidence).toBe(92);\r\n      expect(summary.requiresImmediateResponse).toBe(true);\r\n      expect(summary.responsePriority).toBe('critical');\r\n      expect(summary.killChainStage).toBe('actions_objectives');\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle threats without CVSS score', () => {\r\n      const noCvssData = { ...eventData };\r\n      delete noCvssData.cvssScore;\r\n      \r\n      const event = new ThreatDetectedDomainEvent(aggregateId, noCvssData);\r\n      \r\n      expect(event.cvssScore).toBeUndefined();\r\n    });\r\n\r\n    it('should handle threats without IP addresses', () => {\r\n      const noIpData = { ...eventData };\r\n      delete noIpData.sourceIp;\r\n      delete noIpData.targetIp;\r\n      \r\n      const event = new ThreatDetectedDomainEvent(aggregateId, noIpData);\r\n      const actions = event.getRecommendedContainmentActions();\r\n      \r\n      expect(event.sourceIp).toBeUndefined();\r\n      expect(event.targetIp).toBeUndefined();\r\n      expect(actions).not.toContain('block_source_ip');\r\n    });\r\n\r\n    it('should handle threats without related events', () => {\r\n      const noRelatedData = { ...eventData };\r\n      delete noRelatedData.relatedEvents;\r\n      \r\n      const event = new ThreatDetectedDomainEvent(aggregateId, noRelatedData);\r\n      \r\n      expect(event.relatedEvents).toBeUndefined();\r\n    });\r\n\r\n    it('should handle empty indicators array', () => {\r\n      const noIndicatorsData = { ...eventData, indicators: [] };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, noIndicatorsData);\r\n      \r\n      expect(event.hasMultipleIndicators()).toBe(false);\r\n      expect(event.hasHighConfidenceIndicators()).toBe(false);\r\n    });\r\n\r\n    it('should handle empty affected assets array', () => {\r\n      const noAssetsData = { ...eventData, affectedAssets: [] };\r\n      const event = new ThreatDetectedDomainEvent(aggregateId, noAssetsData);\r\n      \r\n      expect(event.affectsMultipleAssets()).toBe(false);\r\n      expect(event.affectsCriticalAssets()).toBe(false);\r\n    });\r\n  });\r\n});"], "version": 3}