8b3492c714224c9ea68d0846747f5fe5
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetGroup = void 0;
const typeorm_1 = require("typeorm");
const asset_entity_1 = require("./asset.entity");
/**
 * Asset Group entity
 * Represents hierarchical grouping of assets for organization and management
 */
let AssetGroup = class AssetGroup {
    /**
     * Get total asset count including child groups
     */
    get totalAssetCount() {
        return this.metadata?.statistics?.totalAssets || 0;
    }
    /**
     * Check if group is root level
     */
    get isRootGroup() {
        return !this.parentId;
    }
    /**
     * Check if group has children
     */
    get hasChildren() {
        return this.children && this.children.length > 0;
    }
    /**
     * Check if group has assets
     */
    get hasAssets() {
        return this.assets && this.assets.length > 0;
    }
    /**
     * Get group depth in hierarchy
     */
    getDepth() {
        let depth = 0;
        let current = this.parent;
        while (current) {
            depth++;
            current = current.parent;
        }
        return depth;
    }
    /**
     * Get full path from root to this group
     */
    getPath() {
        const path = [];
        let current = this;
        while (current) {
            path.unshift(current.name);
            current = current.parent;
        }
        return path.join(' > ');
    }
    /**
     * Add tag to group
     */
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }
    /**
     * Remove tag from group
     */
    removeTag(tag) {
        this.tags = this.tags.filter(t => t !== tag);
    }
    /**
     * Update group configuration
     */
    updateConfiguration(updates, updatedBy) {
        this.configuration = {
            ...this.configuration,
            ...updates,
        };
        this.updatedBy = updatedBy;
    }
    /**
     * Set custom attribute
     */
    setCustomAttribute(key, value) {
        if (!this.customAttributes) {
            this.customAttributes = {};
        }
        this.customAttributes[key] = value;
    }
    /**
     * Get custom attribute
     */
    getCustomAttribute(key) {
        return this.customAttributes?.[key];
    }
    /**
     * Check if asset matches auto-assignment rules
     */
    matchesAutoAssignmentRules(asset) {
        if (!this.configuration?.autoAssignment?.enabled) {
            return false;
        }
        const rules = this.configuration.autoAssignment.rules || [];
        // Sort rules by priority (higher priority first)
        const sortedRules = rules.sort((a, b) => b.priority - a.priority);
        for (const rule of sortedRules) {
            if (this.evaluateRule(rule.condition, asset)) {
                return true;
            }
        }
        return false;
    }
    /**
     * Evaluate rule condition against asset
     */
    evaluateRule(condition, asset) {
        try {
            // Simple rule evaluation - in production, use a proper expression evaluator
            // This is a simplified implementation
            return true; // Placeholder
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Get inherited policies from parent groups
     */
    getInheritedPolicies() {
        const policies = { ...this.configuration?.policies };
        if (policies?.inheritFromParent && this.parent) {
            const parentPolicies = this.parent.getInheritedPolicies();
            return {
                ...parentPolicies,
                ...policies,
            };
        }
        return policies;
    }
    /**
     * Update statistics cache
     */
    updateStatistics(statistics) {
        if (!this.metadata) {
            this.metadata = {};
        }
        this.metadata.statistics = {
            ...statistics,
            lastUpdated: new Date().toISOString(),
        };
    }
    /**
     * Activate group
     */
    activate(updatedBy) {
        this.isActive = true;
        this.updatedBy = updatedBy;
    }
    /**
     * Deactivate group
     */
    deactivate(updatedBy) {
        this.isActive = false;
        this.updatedBy = updatedBy;
    }
    /**
     * Check if user has access to group
     */
    hasUserAccess(userId, accessType) {
        const access = this.configuration?.access;
        if (!access)
            return false;
        switch (accessType) {
            case 'own':
                return access.owners?.includes(userId) || false;
            case 'edit':
                return access.owners?.includes(userId) || access.editors?.includes(userId) || false;
            case 'view':
                return access.owners?.includes(userId) ||
                    access.editors?.includes(userId) ||
                    access.viewers?.includes(userId) || false;
            default:
                return false;
        }
    }
    /**
     * Get group summary
     */
    getSummary() {
        return {
            id: this.id,
            name: this.name,
            type: this.type,
            isActive: this.isActive,
            parentId: this.parentId,
            path: this.getPath(),
            depth: this.getDepth(),
            isRootGroup: this.isRootGroup,
            hasChildren: this.hasChildren,
            hasAssets: this.hasAssets,
            totalAssetCount: this.totalAssetCount,
            tags: this.tags,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
    /**
     * Export group for reporting
     */
    exportForReporting() {
        return {
            group: this.getSummary(),
            configuration: this.configuration,
            metadata: this.metadata,
            customAttributes: this.customAttributes,
            exportedAt: new Date().toISOString(),
        };
    }
    /**
     * Validate group configuration
     */
    validateConfiguration() {
        const errors = [];
        // Validate auto-assignment rules
        if (this.configuration?.autoAssignment?.enabled) {
            const rules = this.configuration.autoAssignment.rules || [];
            if (rules.length === 0) {
                errors.push('Auto-assignment is enabled but no rules are defined');
            }
            // Check for duplicate rule IDs
            const ruleIds = rules.map(r => r.id);
            const uniqueRuleIds = new Set(ruleIds);
            if (ruleIds.length !== uniqueRuleIds.size) {
                errors.push('Duplicate rule IDs found in auto-assignment rules');
            }
        }
        // Validate compliance settings
        if (this.configuration?.compliance?.frameworks) {
            if (this.configuration.compliance.frameworks.length === 0) {
                errors.push('Compliance frameworks list is empty');
            }
        }
        // Validate access control
        if (this.configuration?.access) {
            const access = this.configuration.access;
            if (!access.owners || access.owners.length === 0) {
                errors.push('At least one owner must be specified');
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * Clone group configuration
     */
    cloneConfiguration() {
        return JSON.parse(JSON.stringify(this.configuration));
    }
};
exports.AssetGroup = AssetGroup;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AssetGroup.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], AssetGroup.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AssetGroup.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'organizational',
            'geographical',
            'functional',
            'technical',
            'environment',
            'criticality',
            'compliance',
            'project',
            'department',
            'location',
            'network_segment',
            'application_tier',
            'custom',
        ],
        default: 'organizational',
    }),
    __metadata("design:type", String)
], AssetGroup.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_active', default: true }),
    __metadata("design:type", Boolean)
], AssetGroup.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AssetGroup.prototype, "configuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AssetGroup.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], AssetGroup.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'custom_attributes', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], AssetGroup.prototype, "customAttributes", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], AssetGroup.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], AssetGroup.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], AssetGroup.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], AssetGroup.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.TreeParent)(),
    __metadata("design:type", AssetGroup)
], AssetGroup.prototype, "parent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'parent_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], AssetGroup.prototype, "parentId", void 0);
__decorate([
    (0, typeorm_1.TreeChildren)(),
    __metadata("design:type", Array)
], AssetGroup.prototype, "children", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => asset_entity_1.Asset, asset => asset.group),
    __metadata("design:type", Array)
], AssetGroup.prototype, "assets", void 0);
exports.AssetGroup = AssetGroup = __decorate([
    (0, typeorm_1.Entity)('asset_groups'),
    (0, typeorm_1.Tree)('closure-table'),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['isActive']),
    (0, typeorm_1.Index)(['parentId'])
], AssetGroup);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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