{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\correlated-event-status-changed.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAC5E,8EAAqE;AAyBrE;;;;;;;;;;;GAWG;AACH,MAAa,uCAAwC,SAAQ,+BAAsD;IACjH,YACE,WAA2B,EAC3B,SAAgD,EAChD,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,IAAI,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,SAAS,KAAK,2CAAiB,CAAC,SAAS,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,SAAS,KAAK,2CAAiB,CAAC,MAAM,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,SAAS,KAAK,2CAAiB,CAAC,WAAW,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,SAAS,KAAK,2CAAiB,CAAC,OAAO,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,SAAS,KAAK,2CAAiB,CAAC,OAAO,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,SAAS,KAAK,2CAAiB,CAAC,OAAO,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,KAAK,IAAI,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,mBAAmB,EAAE;YACvD,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,KAAK,KAAK,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,oDAAoD;QACpD,6DAA6D;QAC7D,OAAO,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,MAAM,aAAa,GAAsC;YACvD,CAAC,2CAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,CAAC,2CAAiB,CAAC,WAAW,CAAC,EAAE,CAAC;YAClC,CAAC,2CAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,CAAC,2CAAiB,CAAC,SAAS,CAAC,EAAE,CAAC;YAChC,CAAC,2CAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC7B,CAAC,2CAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,CAAC,2CAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;SAC/B,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,MAAM,aAAa,GAAsC;YACvD,CAAC,2CAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,CAAC,2CAAiB,CAAC,WAAW,CAAC,EAAE,CAAC;YAClC,CAAC,2CAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,CAAC,2CAAiB,CAAC,SAAS,CAAC,EAAE,CAAC;YAChC,CAAC,2CAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC7B,CAAC,2CAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,CAAC,2CAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;SAC/B,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC;YAC7D,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAClC,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAChC,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAClC,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,IAAI,IAAI,CAAC,sBAAsB,EAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YAClE,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAChC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;YACvB,KAAK,2CAAiB,CAAC,SAAS;gBAC9B,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;oBACzB,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;oBAC3C,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;oBAC7C,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;oBAEjD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAC9B,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;oBACzC,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;oBAC/C,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;gBACjD,CAAC;gBACD,MAAM;YAER,KAAK,2CAAiB,CAAC,MAAM;gBAC3B,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;gBAChD,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBAClD,OAAO,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;gBACxD,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBACpD,MAAM;YAER,KAAK,2CAAiB,CAAC,OAAO;gBAC5B,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACnD,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBAClD,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;gBACtD,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACnD,MAAM;YAER,KAAK,2CAAiB,CAAC,OAAO;gBAC5B,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBACpD,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACnD,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACnD,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;gBAChD,MAAM;YAER,KAAK,2CAAiB,CAAC,WAAW;gBAChC,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAC7C,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBACzC,MAAM;YAER,KAAK,2CAAiB,CAAC,OAAO;gBAC5B,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACnC,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBACvC,MAAM;QACV,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,kBAAkB;QAKhB,MAAM,OAAO,GAIR,EAAE,CAAC;QAER,4BAA4B;QAC5B,OAAO,CAAC,IAAI,CAAC;YACX,MAAM,EAAE,gCAAgC;YACxC,KAAK,EAAE,CAAC;YACR,IAAI,EAAE;gBACJ,WAAW,EAAE,IAAI,CAAC,SAAS;gBAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,eAAe,EAAE,IAAI,CAAC,iBAAiB,EAAE;aAC1C;SACF,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,IAAI,CAAC,uBAAuB,KAAK,SAAS,EAAE,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,2BAA2B;gBACnC,KAAK,EAAE,IAAI,CAAC,uBAAuB;gBACnC,IAAI,EAAE;oBACJ,MAAM,EAAE,IAAI,CAAC,SAAS;iBACvB;aACF,CAAC,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,oCAAoC;gBAC5C,KAAK,EAAE,CAAC;aACT,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,gCAAgC;gBACxC,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE;oBACJ,YAAY,EAAE,IAAI,CAAC,SAAS;iBAC7B;aACF,CAAC,CAAC;QACL,CAAC;QAED,mCAAmC;QACnC,IAAI,IAAI,CAAC,MAAM,EAAE,oBAAoB,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,oCAAoC;gBAC5C,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;gBACvC,IAAI,EAAE;oBACJ,MAAM,EAAE,IAAI,CAAC,SAAS;iBACvB;aACF,CAAC,CAAC;QACL,CAAC;QAED,4BAA4B;QAC5B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,2BAA2B;gBACnC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM;aACvC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,2BAA2B;gBACnC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;aAChC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,iCAAiC;gBACzC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM;aAC7C,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,eAAe;QAqBb,OAAO;YACL,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC9C,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,uBAAuB,EAAE,IAAI,CAAC,uBAAuB;YACrD,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACnD,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7C,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACrD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC/C,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACnD,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACjD,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACrD,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACxC,oBAAoB,EAAE,IAAI,CAAC,uBAAuB,EAAE;YACpD,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YAChD,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;SACrC,CAAC;IACJ,CAAC;CACF;AAhaD,0FAgaC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\correlated-event-status-changed.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { CorrelationStatus } from '../enums/correlation-status.enum';\r\nimport { CorrelationResult } from '../entities/correlated-event.entity';\r\n\r\n/**\r\n * Correlated Event Status Changed Domain Event Data\r\n */\r\nexport interface CorrelatedEventStatusChangedEventData {\r\n  /** Previous correlation status */\r\n  oldStatus: CorrelationStatus;\r\n  /** New correlation status */\r\n  newStatus: CorrelationStatus;\r\n  /** Correlation result details */\r\n  result?: CorrelationResult;\r\n  /** Updated correlation quality score */\r\n  correlationQualityScore?: number;\r\n  /** Whether the event now requires manual review */\r\n  requiresManualReview: boolean;\r\n  /** Status change timestamp */\r\n  changedAt?: Date;\r\n  /** Reason for status change */\r\n  reason?: string;\r\n  /** Additional metadata about the change */\r\n  changeMetadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Correlated Event Status Changed Domain Event\r\n * \r\n * Raised when a correlated event's correlation status changes.\r\n * This event triggers various downstream processes including:\r\n * - Workflow state transitions\r\n * - Notification systems\r\n * - Metrics and monitoring updates\r\n * - Audit trail maintenance\r\n * - Queue management for different processing stages\r\n * - Alert escalation or de-escalation\r\n */\r\nexport class CorrelatedEventStatusChangedDomainEvent extends BaseDomainEvent<CorrelatedEventStatusChangedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: CorrelatedEventStatusChangedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the previous correlation status\r\n   */\r\n  get oldStatus(): CorrelationStatus {\r\n    return this.eventData.oldStatus;\r\n  }\r\n\r\n  /**\r\n   * Get the new correlation status\r\n   */\r\n  get newStatus(): CorrelationStatus {\r\n    return this.eventData.newStatus;\r\n  }\r\n\r\n  /**\r\n   * Get the correlation result\r\n   */\r\n  get result(): CorrelationResult | undefined {\r\n    return this.eventData.result;\r\n  }\r\n\r\n  /**\r\n   * Get the correlation quality score\r\n   */\r\n  get correlationQualityScore(): number | undefined {\r\n    return this.eventData.correlationQualityScore;\r\n  }\r\n\r\n  /**\r\n   * Check if the event requires manual review\r\n   */\r\n  get requiresManualReview(): boolean {\r\n    return this.eventData.requiresManualReview;\r\n  }\r\n\r\n  /**\r\n   * Get the status change timestamp\r\n   */\r\n  get changedAt(): Date {\r\n    return this.eventData.changedAt || this.occurredOn;\r\n  }\r\n\r\n  /**\r\n   * Get the reason for status change\r\n   */\r\n  get reason(): string | undefined {\r\n    return this.eventData.reason;\r\n  }\r\n\r\n  /**\r\n   * Get additional change metadata\r\n   */\r\n  get changeMetadata(): Record<string, any> {\r\n    return this.eventData.changeMetadata || {};\r\n  }\r\n\r\n  /**\r\n   * Check if status changed to completed\r\n   */\r\n  isCompletedTransition(): boolean {\r\n    return this.newStatus === CorrelationStatus.COMPLETED;\r\n  }\r\n\r\n  /**\r\n   * Check if status changed to failed\r\n   */\r\n  isFailedTransition(): boolean {\r\n    return this.newStatus === CorrelationStatus.FAILED;\r\n  }\r\n\r\n  /**\r\n   * Check if status changed to in progress\r\n   */\r\n  isInProgressTransition(): boolean {\r\n    return this.newStatus === CorrelationStatus.IN_PROGRESS;\r\n  }\r\n\r\n  /**\r\n   * Check if status changed to partial\r\n   */\r\n  isPartialTransition(): boolean {\r\n    return this.newStatus === CorrelationStatus.PARTIAL;\r\n  }\r\n\r\n  /**\r\n   * Check if status changed to skipped\r\n   */\r\n  isSkippedTransition(): boolean {\r\n    return this.newStatus === CorrelationStatus.SKIPPED;\r\n  }\r\n\r\n  /**\r\n   * Check if status changed to timeout\r\n   */\r\n  isTimeoutTransition(): boolean {\r\n    return this.newStatus === CorrelationStatus.TIMEOUT;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a successful completion\r\n   */\r\n  isSuccessfulCompletion(): boolean {\r\n    return this.isCompletedTransition() && (this.result?.success === true);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a failure transition\r\n   */\r\n  isFailureTransition(): boolean {\r\n    return this.isFailedTransition() || this.isTimeoutTransition() || \r\n           (this.isCompletedTransition() && this.result?.success === false);\r\n  }\r\n\r\n  /**\r\n   * Check if correlation quality improved\r\n   */\r\n  hasQualityImprovement(): boolean {\r\n    // This would need previous quality score to compare\r\n    // For now, we'll consider any quality score above 70 as good\r\n    return (this.correlationQualityScore || 0) >= 70;\r\n  }\r\n\r\n  /**\r\n   * Check if status represents progress\r\n   */\r\n  isProgressTransition(): boolean {\r\n    const progressOrder: Record<CorrelationStatus, number> = {\r\n      [CorrelationStatus.PENDING]: 1,\r\n      [CorrelationStatus.IN_PROGRESS]: 2,\r\n      [CorrelationStatus.PARTIAL]: 3,\r\n      [CorrelationStatus.COMPLETED]: 4,\r\n      [CorrelationStatus.FAILED]: 0,\r\n      [CorrelationStatus.SKIPPED]: 0,\r\n      [CorrelationStatus.TIMEOUT]: 0,\r\n    };\r\n\r\n    return progressOrder[this.newStatus] > progressOrder[this.oldStatus];\r\n  }\r\n\r\n  /**\r\n   * Check if status represents regression\r\n   */\r\n  isRegressionTransition(): boolean {\r\n    const progressOrder: Record<CorrelationStatus, number> = {\r\n      [CorrelationStatus.PENDING]: 1,\r\n      [CorrelationStatus.IN_PROGRESS]: 2,\r\n      [CorrelationStatus.PARTIAL]: 3,\r\n      [CorrelationStatus.COMPLETED]: 4,\r\n      [CorrelationStatus.FAILED]: 0,\r\n      [CorrelationStatus.SKIPPED]: 0,\r\n      [CorrelationStatus.TIMEOUT]: 0,\r\n    };\r\n\r\n    return progressOrder[this.newStatus] < progressOrder[this.oldStatus] && \r\n           progressOrder[this.oldStatus] > 0;\r\n  }\r\n\r\n  /**\r\n   * Get transition type\r\n   */\r\n  getTransitionType(): 'progress' | 'regression' | 'completion' | 'failure' | 'neutral' {\r\n    if (this.isSuccessfulCompletion()) {\r\n      return 'completion';\r\n    }\r\n    \r\n    if (this.isFailureTransition()) {\r\n      return 'failure';\r\n    }\r\n    \r\n    if (this.isProgressTransition()) {\r\n      return 'progress';\r\n    }\r\n    \r\n    if (this.isRegressionTransition()) {\r\n      return 'regression';\r\n    }\r\n    \r\n    return 'neutral';\r\n  }\r\n\r\n  /**\r\n   * Get notification priority based on transition\r\n   */\r\n  getNotificationPriority(): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (this.isSuccessfulCompletion() && this.hasQualityImprovement()) {\r\n      return 'high';\r\n    }\r\n    \r\n    if (this.isFailureTransition()) {\r\n      return 'high';\r\n    }\r\n    \r\n    if (this.requiresManualReview) {\r\n      return 'medium';\r\n    }\r\n    \r\n    if (this.isProgressTransition()) {\r\n      return 'low';\r\n    }\r\n    \r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get recommended actions based on status change\r\n   */\r\n  getRecommendedActions(): string[] {\r\n    const actions: string[] = [];\r\n\r\n    switch (this.newStatus) {\r\n      case CorrelationStatus.COMPLETED:\r\n        if (this.result?.success) {\r\n          actions.push('Review correlation results');\r\n          actions.push('Validate identified patterns');\r\n          actions.push('Proceed to next processing stage');\r\n          \r\n          if (this.requiresManualReview) {\r\n            actions.push('Schedule manual review');\r\n          }\r\n        } else {\r\n          actions.push('Investigate correlation issues');\r\n          actions.push('Review failed rules and errors');\r\n        }\r\n        break;\r\n\r\n      case CorrelationStatus.FAILED:\r\n        actions.push('Investigate correlation failure');\r\n        actions.push('Review error logs and diagnostics');\r\n        actions.push('Consider retry with adjusted parameters');\r\n        actions.push('Escalate to correlation engine team');\r\n        break;\r\n\r\n      case CorrelationStatus.PARTIAL:\r\n        actions.push('Review partial correlation results');\r\n        actions.push('Identify missing correlation data');\r\n        actions.push('Consider additional correlation rules');\r\n        actions.push('Evaluate if results are sufficient');\r\n        break;\r\n\r\n      case CorrelationStatus.TIMEOUT:\r\n        actions.push('Investigate correlation performance');\r\n        actions.push('Review correlation rule complexity');\r\n        actions.push('Consider increasing timeout limits');\r\n        actions.push('Retry with optimized parameters');\r\n        break;\r\n\r\n      case CorrelationStatus.IN_PROGRESS:\r\n        actions.push('Monitor correlation progress');\r\n        actions.push('Track processing metrics');\r\n        break;\r\n\r\n      case CorrelationStatus.SKIPPED:\r\n        actions.push('Review skip reason');\r\n        actions.push('Validate skip criteria');\r\n        break;\r\n    }\r\n\r\n    return actions;\r\n  }\r\n\r\n  /**\r\n   * Get metrics to update based on status change\r\n   */\r\n  getMetricsToUpdate(): Array<{\r\n    metric: string;\r\n    value: number;\r\n    tags?: Record<string, string>;\r\n  }> {\r\n    const metrics: Array<{\r\n      metric: string;\r\n      value: number;\r\n      tags?: Record<string, string>;\r\n    }> = [];\r\n\r\n    // Status transition counter\r\n    metrics.push({\r\n      metric: 'correlation_status_transitions',\r\n      value: 1,\r\n      tags: {\r\n        from_status: this.oldStatus,\r\n        to_status: this.newStatus,\r\n        transition_type: this.getTransitionType(),\r\n      },\r\n    });\r\n\r\n    // Quality score metric\r\n    if (this.correlationQualityScore !== undefined) {\r\n      metrics.push({\r\n        metric: 'correlation_quality_score',\r\n        value: this.correlationQualityScore,\r\n        tags: {\r\n          status: this.newStatus,\r\n        },\r\n      });\r\n    }\r\n\r\n    // Success/failure counters\r\n    if (this.isSuccessfulCompletion()) {\r\n      metrics.push({\r\n        metric: 'correlation_completions_successful',\r\n        value: 1,\r\n      });\r\n    } else if (this.isFailureTransition()) {\r\n      metrics.push({\r\n        metric: 'correlation_completions_failed',\r\n        value: 1,\r\n        tags: {\r\n          failure_type: this.newStatus,\r\n        },\r\n      });\r\n    }\r\n\r\n    // Processing duration if available\r\n    if (this.result?.processingDurationMs) {\r\n      metrics.push({\r\n        metric: 'correlation_processing_duration_ms',\r\n        value: this.result.processingDurationMs,\r\n        tags: {\r\n          status: this.newStatus,\r\n        },\r\n      });\r\n    }\r\n\r\n    // Rules and matches metrics\r\n    if (this.result) {\r\n      metrics.push({\r\n        metric: 'correlation_rules_applied',\r\n        value: this.result.appliedRules.length,\r\n      });\r\n\r\n      metrics.push({\r\n        metric: 'correlation_matches_found',\r\n        value: this.result.matchesFound,\r\n      });\r\n\r\n      metrics.push({\r\n        metric: 'correlation_patterns_identified',\r\n        value: this.result.patternsIdentified.length,\r\n      });\r\n    }\r\n\r\n    return metrics;\r\n  }\r\n\r\n  /**\r\n   * Get event summary for handlers\r\n   */\r\n  getEventSummary(): {\r\n    correlatedEventId: string;\r\n    oldStatus: CorrelationStatus;\r\n    newStatus: CorrelationStatus;\r\n    correlationQualityScore?: number;\r\n    requiresManualReview: boolean;\r\n    changedAt: Date;\r\n    reason?: string;\r\n    isCompletedTransition: boolean;\r\n    isFailedTransition: boolean;\r\n    isSuccessfulCompletion: boolean;\r\n    isFailureTransition: boolean;\r\n    hasQualityImprovement: boolean;\r\n    isProgressTransition: boolean;\r\n    isRegressionTransition: boolean;\r\n    transitionType: 'progress' | 'regression' | 'completion' | 'failure' | 'neutral';\r\n    notificationPriority: 'low' | 'medium' | 'high' | 'critical';\r\n    recommendedActions: string[];\r\n    metricsToUpdate: ReturnType<typeof this.getMetricsToUpdate>;\r\n    result?: CorrelationResult;\r\n  } {\r\n    return {\r\n      correlatedEventId: this.aggregateId.toString(),\r\n      oldStatus: this.oldStatus,\r\n      newStatus: this.newStatus,\r\n      correlationQualityScore: this.correlationQualityScore,\r\n      requiresManualReview: this.requiresManualReview,\r\n      changedAt: this.changedAt,\r\n      reason: this.reason,\r\n      isCompletedTransition: this.isCompletedTransition(),\r\n      isFailedTransition: this.isFailedTransition(),\r\n      isSuccessfulCompletion: this.isSuccessfulCompletion(),\r\n      isFailureTransition: this.isFailureTransition(),\r\n      hasQualityImprovement: this.hasQualityImprovement(),\r\n      isProgressTransition: this.isProgressTransition(),\r\n      isRegressionTransition: this.isRegressionTransition(),\r\n      transitionType: this.getTransitionType(),\r\n      notificationPriority: this.getNotificationPriority(),\r\n      recommendedActions: this.getRecommendedActions(),\r\n      metricsToUpdate: this.getMetricsToUpdate(),\r\n      result: this.result,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventSummary: this.getEventSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}