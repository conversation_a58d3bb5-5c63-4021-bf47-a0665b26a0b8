2f6af6fa2b59a70b03d8a976605bd3f8
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const enriched_event_specification_1 = require("../enriched-event.specification");
const enriched_event_entity_1 = require("../../entities/enriched-event.entity");
const shared_kernel_1 = require("../../../../../shared-kernel");
const event_metadata_value_object_1 = require("../../value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../value-objects/event-metadata/event-source.value-object");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const event_status_enum_1 = require("../../enums/event-status.enum");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const event_source_type_enum_1 = require("../../enums/event-source-type.enum");
const enrichment_source_enum_1 = require("../../enums/enrichment-source.enum");
describe('EnrichedEvent Specifications', () => {
    let baseProps;
    let mockMetadata;
    beforeEach(() => {
        // Create mock metadata
        const eventSource = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.SIEM, 'test-siem-001');
        const eventTimestamp = event_timestamp_value_object_1.EventTimestamp.create();
        mockMetadata = event_metadata_value_object_1.EventMetadata.create(eventTimestamp, eventSource);
        // Create base props for enriched events
        baseProps = {
            normalizedEventId: shared_kernel_1.UniqueEntityId.create(),
            metadata: mockMetadata,
            type: event_type_enum_1.EventType.THREAT_DETECTED,
            severity: event_severity_enum_1.EventSeverity.MEDIUM,
            status: event_status_enum_1.EventStatus.ACTIVE,
            processingStatus: event_processing_status_enum_1.EventProcessingStatus.ENRICHED,
            enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
            normalizedData: { event_type: 'threat', normalized: true },
            enrichedData: { event_type: 'threat', enriched: true },
            title: 'Test Enriched Event',
            appliedRules: [],
            enrichmentData: [],
        };
    });
    describe('EnrichmentCompletedSpecification', () => {
        it('should match events with completed enrichment', () => {
            const spec = new enriched_event_specification_1.EnrichmentCompletedSpecification();
            const completedEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
            });
            const pendingEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.PENDING,
            });
            expect(spec.isSatisfiedBy(completedEvent)).toBe(true);
            expect(spec.isSatisfiedBy(pendingEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has completed enrichment');
        });
    });
    describe('EnrichmentFailedSpecification', () => {
        it('should match events with failed enrichment', () => {
            const spec = new enriched_event_specification_1.EnrichmentFailedSpecification();
            const failedEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.FAILED,
            });
            const completedEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
            });
            expect(spec.isSatisfiedBy(failedEvent)).toBe(true);
            expect(spec.isSatisfiedBy(completedEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has failed enrichment');
        });
    });
    describe('EnrichmentInProgressSpecification', () => {
        it('should match events with in-progress enrichment', () => {
            const spec = new enriched_event_specification_1.EnrichmentInProgressSpecification();
            const inProgressEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.IN_PROGRESS,
            });
            const completedEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
            });
            expect(spec.isSatisfiedBy(inProgressEvent)).toBe(true);
            expect(spec.isSatisfiedBy(completedEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event is currently being enriched');
        });
    });
    describe('EnrichmentPartialSpecification', () => {
        it('should match events with partial enrichment', () => {
            const spec = new enriched_event_specification_1.EnrichmentPartialSpecification();
            const partialEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.PARTIAL,
            });
            const completedEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
            });
            expect(spec.isSatisfiedBy(partialEvent)).toBe(true);
            expect(spec.isSatisfiedBy(completedEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has partial enrichment results');
        });
    });
    describe('HighEnrichmentQualitySpecification', () => {
        it('should match events with high enrichment quality', () => {
            const spec = new enriched_event_specification_1.HighEnrichmentQualitySpecification();
            const highQualityEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentQualityScore: 85,
            });
            const lowQualityEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentQualityScore: 50,
            });
            expect(spec.isSatisfiedBy(highQualityEvent)).toBe(true);
            expect(spec.isSatisfiedBy(lowQualityEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has high enrichment quality (>= 70)');
        });
    });
    describe('HasValidationErrorsSpecification', () => {
        it('should match events with validation errors', () => {
            const spec = new enriched_event_specification_1.HasValidationErrorsSpecification();
            const eventWithErrors = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                validationErrors: ['Error 1', 'Error 2'],
            });
            const eventWithoutErrors = enriched_event_entity_1.EnrichedEvent.create(baseProps);
            expect(spec.isSatisfiedBy(eventWithErrors)).toBe(true);
            expect(spec.isSatisfiedBy(eventWithoutErrors)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has validation errors');
        });
    });
    describe('RequiresManualReviewSpecification', () => {
        it('should match events requiring manual review', () => {
            const spec = new enriched_event_specification_1.RequiresManualReviewSpecification();
            const reviewRequiredEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                requiresManualReview: true,
            });
            const noReviewEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                requiresManualReview: false,
            });
            expect(spec.isSatisfiedBy(reviewRequiredEvent)).toBe(true);
            expect(spec.isSatisfiedBy(noReviewEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event requires manual review');
        });
    });
    describe('ReadyForNextStageSpecification', () => {
        it('should match events ready for next stage', () => {
            const spec = new enriched_event_specification_1.ReadyForNextStageSpecification();
            const readyEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
                enrichmentQualityScore: 85,
                validationErrors: [],
                requiresManualReview: false,
            });
            const notReadyEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.FAILED,
            });
            expect(spec.isSatisfiedBy(readyEvent)).toBe(true);
            expect(spec.isSatisfiedBy(notReadyEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event is ready for next processing stage');
        });
    });
    describe('HighThreatRiskSpecification', () => {
        it('should match events with high threat risk', () => {
            const spec = new enriched_event_specification_1.HighThreatRiskSpecification();
            const highRiskEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                threatIntelScore: 90,
            });
            const lowRiskEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                threatIntelScore: 30,
            });
            expect(spec.isSatisfiedBy(highRiskEvent)).toBe(true);
            expect(spec.isSatisfiedBy(lowRiskEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has high threat intelligence risk (>= 85)');
        });
    });
    describe('HasThreatIntelligenceSpecification', () => {
        it('should match events with threat intelligence data', () => {
            const spec = new enriched_event_specification_1.HasThreatIntelligenceSpecification();
            const eventWithThreatIntel = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentData: [{
                        source: enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL,
                        type: 'threat_intelligence',
                        data: { score: 75 },
                        confidence: 85,
                        timestamp: new Date(),
                    }],
            });
            const eventWithoutThreatIntel = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentData: [{
                        source: enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION,
                        type: 'reputation',
                        data: { score: 75 },
                        confidence: 85,
                        timestamp: new Date(),
                    }],
            });
            expect(spec.isSatisfiedBy(eventWithThreatIntel)).toBe(true);
            expect(spec.isSatisfiedBy(eventWithoutThreatIntel)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has threat intelligence data');
        });
    });
    describe('HasReputationDataSpecification', () => {
        it('should match events with reputation data', () => {
            const spec = new enriched_event_specification_1.HasReputationDataSpecification();
            const eventWithReputation = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                reputationScores: { virustotal: 85, shodan: 70 },
            });
            const eventWithoutReputation = enriched_event_entity_1.EnrichedEvent.create(baseProps);
            expect(spec.isSatisfiedBy(eventWithReputation)).toBe(true);
            expect(spec.isSatisfiedBy(eventWithoutReputation)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has reputation data');
        });
    });
    describe('HasGeolocationDataSpecification', () => {
        it('should match events with geolocation data', () => {
            const spec = new enriched_event_specification_1.HasGeolocationDataSpecification();
            const eventWithGeo = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                geolocationContext: { country: 'US', city: 'New York' },
            });
            const eventWithoutGeo = enriched_event_entity_1.EnrichedEvent.create(baseProps);
            expect(spec.isSatisfiedBy(eventWithGeo)).toBe(true);
            expect(spec.isSatisfiedBy(eventWithoutGeo)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has geolocation data');
        });
    });
    describe('EnrichmentStatusSpecification', () => {
        it('should match events with specified statuses', () => {
            const spec = new enriched_event_specification_1.EnrichmentStatusSpecification([
                enriched_event_entity_1.EnrichmentStatus.COMPLETED,
                enriched_event_entity_1.EnrichmentStatus.PARTIAL,
            ]);
            const completedEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
            });
            const partialEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.PARTIAL,
            });
            const failedEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.FAILED,
            });
            expect(spec.isSatisfiedBy(completedEvent)).toBe(true);
            expect(spec.isSatisfiedBy(partialEvent)).toBe(true);
            expect(spec.isSatisfiedBy(failedEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event status is one of: COMPLETED, PARTIAL');
        });
    });
    describe('EnrichmentQualityScoreRangeSpecification', () => {
        it('should match events within quality score range', () => {
            const spec = new enriched_event_specification_1.EnrichmentQualityScoreRangeSpecification(70, 90);
            const highQualityEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentQualityScore: 85,
            });
            const lowQualityEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentQualityScore: 50,
            });
            const veryHighQualityEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentQualityScore: 95,
            });
            expect(spec.isSatisfiedBy(highQualityEvent)).toBe(true);
            expect(spec.isSatisfiedBy(lowQualityEvent)).toBe(false);
            expect(spec.isSatisfiedBy(veryHighQualityEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event quality score is between 70 and 90');
        });
        it('should handle min-only range', () => {
            const spec = new enriched_event_specification_1.EnrichmentQualityScoreRangeSpecification(80);
            const highQualityEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentQualityScore: 85,
            });
            const lowQualityEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentQualityScore: 75,
            });
            expect(spec.isSatisfiedBy(highQualityEvent)).toBe(true);
            expect(spec.isSatisfiedBy(lowQualityEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event quality score is at least 80');
        });
        it('should handle max-only range', () => {
            const spec = new enriched_event_specification_1.EnrichmentQualityScoreRangeSpecification(undefined, 80);
            const lowQualityEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentQualityScore: 75,
            });
            const highQualityEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentQualityScore: 85,
            });
            expect(spec.isSatisfiedBy(lowQualityEvent)).toBe(true);
            expect(spec.isSatisfiedBy(highQualityEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event quality score is at most 80');
        });
    });
    describe('ThreatIntelScoreRangeSpecification', () => {
        it('should match events within threat intelligence score range', () => {
            const spec = new enriched_event_specification_1.ThreatIntelScoreRangeSpecification(50, 80);
            const mediumThreatEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                threatIntelScore: 65,
            });
            const lowThreatEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                threatIntelScore: 30,
            });
            const highThreatEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                threatIntelScore: 90,
            });
            expect(spec.isSatisfiedBy(mediumThreatEvent)).toBe(true);
            expect(spec.isSatisfiedBy(lowThreatEvent)).toBe(false);
            expect(spec.isSatisfiedBy(highThreatEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event threat intelligence score is between 50 and 80');
        });
    });
    describe('AppliedRuleSpecification', () => {
        it('should match events with specific applied rule', () => {
            const spec = new enriched_event_specification_1.AppliedRuleSpecification('test-rule-001');
            const eventWithRule = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                appliedRules: [{
                        id: 'test-rule-001',
                        name: 'Test Rule',
                        description: 'A test rule',
                        priority: 100,
                        required: false,
                        sources: [enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION],
                    }],
            });
            const eventWithoutRule = enriched_event_entity_1.EnrichedEvent.create(baseProps);
            expect(spec.isSatisfiedBy(eventWithRule)).toBe(true);
            expect(spec.isSatisfiedBy(eventWithoutRule)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has applied rule: test-rule-001');
        });
    });
    describe('NormalizedEventSpecification', () => {
        it('should match events from specific normalized event', () => {
            const normalizedEventId = shared_kernel_1.UniqueEntityId.create();
            const spec = new enriched_event_specification_1.NormalizedEventSpecification(normalizedEventId);
            const matchingEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                normalizedEventId,
            });
            const nonMatchingEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                normalizedEventId: shared_kernel_1.UniqueEntityId.create(),
            });
            expect(spec.isSatisfiedBy(matchingEvent)).toBe(true);
            expect(spec.isSatisfiedBy(nonMatchingEvent)).toBe(false);
            expect(spec.getDescription()).toBe(`Enriched event references normalized event: ${normalizedEventId.toString()}`);
        });
    });
    describe('ExceededMaxAttemptsSpecification', () => {
        it('should match events that exceeded max attempts', () => {
            const spec = new enriched_event_specification_1.ExceededMaxAttemptsSpecification();
            const exceededEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentAttempts: 5, // Assuming max is 3
            });
            const normalEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentAttempts: 2,
            });
            expect(spec.isSatisfiedBy(exceededEvent)).toBe(true);
            expect(spec.isSatisfiedBy(normalEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has exceeded maximum enrichment attempts');
        });
    });
    describe('ReviewedEventSpecification', () => {
        it('should match events that have been reviewed', () => {
            const spec = new enriched_event_specification_1.ReviewedEventSpecification();
            const reviewedEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                reviewedAt: new Date(),
                reviewedBy: '<EMAIL>',
            });
            const unreviewedEvent = enriched_event_entity_1.EnrichedEvent.create(baseProps);
            expect(spec.isSatisfiedBy(reviewedEvent)).toBe(true);
            expect(spec.isSatisfiedBy(unreviewedEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has been manually reviewed');
        });
    });
    describe('PendingReviewSpecification', () => {
        it('should match events pending manual review', () => {
            const spec = new enriched_event_specification_1.PendingReviewSpecification();
            const pendingEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                requiresManualReview: true,
                reviewedAt: undefined,
            });
            const reviewedEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                requiresManualReview: true,
                reviewedAt: new Date(),
            });
            const noReviewEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                requiresManualReview: false,
            });
            expect(spec.isSatisfiedBy(pendingEvent)).toBe(true);
            expect(spec.isSatisfiedBy(reviewedEvent)).toBe(false);
            expect(spec.isSatisfiedBy(noReviewEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event is pending manual review');
        });
    });
    describe('EnrichmentDurationRangeSpecification', () => {
        it('should match events within duration range', () => {
            const spec = new enriched_event_specification_1.EnrichmentDurationRangeSpecification(1000, 5000);
            const fastEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStartedAt: new Date(Date.now() - 3000),
                enrichmentCompletedAt: new Date(),
            });
            const slowEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStartedAt: new Date(Date.now() - 10000),
                enrichmentCompletedAt: new Date(),
            });
            const veryFastEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStartedAt: new Date(Date.now() - 500),
                enrichmentCompletedAt: new Date(),
            });
            expect(spec.isSatisfiedBy(fastEvent)).toBe(true);
            expect(spec.isSatisfiedBy(slowEvent)).toBe(false);
            expect(spec.isSatisfiedBy(veryFastEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event duration is between 1000ms and 5000ms');
        });
        it('should not match events without duration data', () => {
            const spec = new enriched_event_specification_1.EnrichmentDurationRangeSpecification(1000, 5000);
            const eventWithoutDuration = enriched_event_entity_1.EnrichedEvent.create(baseProps);
            expect(spec.isSatisfiedBy(eventWithoutDuration)).toBe(false);
        });
    });
    describe('EnrichmentSourceSpecification', () => {
        it('should match events with data from specified sources', () => {
            const spec = new enriched_event_specification_1.EnrichmentSourceSpecification([
                enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION,
                enrichment_source_enum_1.EnrichmentSource.THREAT_INTELLIGENCE,
            ]);
            const eventWithSource = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentData: [{
                        source: enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION,
                        type: 'reputation',
                        data: { score: 75 },
                        confidence: 85,
                        timestamp: new Date(),
                    }],
            });
            const eventWithoutSource = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentData: [{
                        source: enrichment_source_enum_1.EnrichmentSource.IP_GEOLOCATION,
                        type: 'geolocation',
                        data: { country: 'US' },
                        confidence: 95,
                        timestamp: new Date(),
                    }],
            });
            expect(spec.isSatisfiedBy(eventWithSource)).toBe(true);
            expect(spec.isSatisfiedBy(eventWithoutSource)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has data from sources: ip_reputation, threat_intelligence');
        });
    });
    describe('EnrichmentDataTypeSpecification', () => {
        it('should match events with data of specified types', () => {
            const spec = new enriched_event_specification_1.EnrichmentDataTypeSpecification(['reputation', 'geolocation']);
            const eventWithType = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentData: [{
                        source: enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION,
                        type: 'reputation',
                        data: { score: 75 },
                        confidence: 85,
                        timestamp: new Date(),
                    }],
            });
            const eventWithoutType = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentData: [{
                        source: enrichment_source_enum_1.EnrichmentSource.THREAT_INTELLIGENCE,
                        type: 'threat_intelligence',
                        data: { score: 60 },
                        confidence: 80,
                        timestamp: new Date(),
                    }],
            });
            expect(spec.isSatisfiedBy(eventWithType)).toBe(true);
            expect(spec.isSatisfiedBy(eventWithoutType)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event has data of types: reputation, geolocation');
        });
    });
    describe('AverageReputationScoreRangeSpecification', () => {
        it('should match events within average reputation score range', () => {
            const spec = new enriched_event_specification_1.AverageReputationScoreRangeSpecification(60, 80);
            const mediumReputationEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                reputationScores: { source1: 70, source2: 75 }, // Average: 72.5
            });
            const lowReputationEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                reputationScores: { source1: 40, source2: 50 }, // Average: 45
            });
            const highReputationEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                reputationScores: { source1: 90, source2: 95 }, // Average: 92.5
            });
            expect(spec.isSatisfiedBy(mediumReputationEvent)).toBe(true);
            expect(spec.isSatisfiedBy(lowReputationEvent)).toBe(false);
            expect(spec.isSatisfiedBy(highReputationEvent)).toBe(false);
            expect(spec.getDescription()).toBe('Enriched event average reputation score is between 60 and 80');
        });
        it('should handle events without reputation scores', () => {
            const spec = new enriched_event_specification_1.AverageReputationScoreRangeSpecification(60, 80);
            const eventWithoutScores = enriched_event_entity_1.EnrichedEvent.create(baseProps);
            expect(spec.isSatisfiedBy(eventWithoutScores)).toBe(true); // No min score required
        });
    });
    describe('EnrichedEventSpecificationBuilder', () => {
        it('should build specification with single condition', () => {
            const spec = enriched_event_specification_1.EnrichedEventSpecificationBuilder
                .create()
                .enrichmentCompleted()
                .build();
            const completedEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
            });
            const pendingEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.PENDING,
            });
            expect(spec.isSatisfiedBy(completedEvent)).toBe(true);
            expect(spec.isSatisfiedBy(pendingEvent)).toBe(false);
        });
        it('should build specification with multiple AND conditions', () => {
            const spec = enriched_event_specification_1.EnrichedEventSpecificationBuilder
                .create()
                .enrichmentCompleted()
                .highEnrichmentQuality()
                .build();
            const goodEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
                enrichmentQualityScore: 85,
            });
            const completedLowQualityEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
                enrichmentQualityScore: 50,
            });
            const pendingHighQualityEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.PENDING,
                enrichmentQualityScore: 85,
            });
            expect(spec.isSatisfiedBy(goodEvent)).toBe(true);
            expect(spec.isSatisfiedBy(completedLowQualityEvent)).toBe(false);
            expect(spec.isSatisfiedBy(pendingHighQualityEvent)).toBe(false);
        });
        it('should build specification with multiple OR conditions', () => {
            const spec = enriched_event_specification_1.EnrichedEventSpecificationBuilder
                .create()
                .enrichmentCompleted()
                .enrichmentPartial()
                .buildWithOr();
            const completedEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
            });
            const partialEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.PARTIAL,
            });
            const failedEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.FAILED,
            });
            expect(spec.isSatisfiedBy(completedEvent)).toBe(true);
            expect(spec.isSatisfiedBy(partialEvent)).toBe(true);
            expect(spec.isSatisfiedBy(failedEvent)).toBe(false);
        });
        it('should build complex specification with various conditions', () => {
            const normalizedEventId = shared_kernel_1.UniqueEntityId.create();
            const spec = enriched_event_specification_1.EnrichedEventSpecificationBuilder
                .create()
                .enrichmentCompleted()
                .highEnrichmentQuality()
                .hasThreatIntelligence()
                .fromNormalizedEvent(normalizedEventId)
                .enrichmentQualityScoreRange(80, 95)
                .withEnrichmentSources(enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL)
                .build();
            const matchingEvent = enriched_event_entity_1.EnrichedEvent.create({
                ...baseProps,
                normalizedEventId,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
                enrichmentQualityScore: 85,
                enrichmentData: [{
                        source: enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL,
                        type: 'threat_intelligence',
                        data: { score: 75 },
                        confidence: 85,
                        timestamp: new Date(),
                    }],
            });
            expect(spec.isSatisfiedBy(matchingEvent)).toBe(true);
        });
        it('should throw error when building without conditions', () => {
            expect(() => {
                enriched_event_specification_1.EnrichedEventSpecificationBuilder.create().build();
            }).toThrow('At least one specification must be added');
        });
        it('should support all builder methods', () => {
            const builder = enriched_event_specification_1.EnrichedEventSpecificationBuilder.create();
            // Test that all methods return the builder for chaining
            expect(builder.enrichmentCompleted()).toBe(builder);
            expect(builder.enrichmentFailed()).toBe(builder);
            expect(builder.enrichmentInProgress()).toBe(builder);
            expect(builder.enrichmentPartial()).toBe(builder);
            expect(builder.highEnrichmentQuality()).toBe(builder);
            expect(builder.hasValidationErrors()).toBe(builder);
            expect(builder.requiresManualReview()).toBe(builder);
            expect(builder.readyForNextStage()).toBe(builder);
            expect(builder.highThreatRisk()).toBe(builder);
            expect(builder.hasThreatIntelligence()).toBe(builder);
            expect(builder.hasReputationData()).toBe(builder);
            expect(builder.hasGeolocationData()).toBe(builder);
            expect(builder.withEnrichmentStatus(enriched_event_entity_1.EnrichmentStatus.COMPLETED)).toBe(builder);
            expect(builder.enrichmentQualityScoreRange(70, 90)).toBe(builder);
            expect(builder.threatIntelScoreRange(50, 80)).toBe(builder);
            expect(builder.withAppliedRule('test-rule')).toBe(builder);
            expect(builder.fromNormalizedEvent(shared_kernel_1.UniqueEntityId.create())).toBe(builder);
            expect(builder.exceededMaxAttempts()).toBe(builder);
            expect(builder.reviewed()).toBe(builder);
            expect(builder.pendingReview()).toBe(builder);
            expect(builder.enrichmentDurationRange(1000, 5000)).toBe(builder);
            expect(builder.withEnrichmentSources(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION)).toBe(builder);
            expect(builder.withEnrichmentDataTypes('reputation')).toBe(builder);
            expect(builder.averageReputationScoreRange(60, 80)).toBe(builder);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************