{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-executed.domain-event.spec.ts", "mappings": ";;AAAA,oGAA8H;AAC9H,gEAA8D;AAC9D,mEAA0D;AAE1D,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;IACjD,IAAI,WAA2B,CAAC;IAChC,IAAI,SAA0C,CAAC;IAC/C,IAAI,KAAwC,CAAC;IAE7C,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;QACxC,SAAS,GAAG;YACV,UAAU,EAAE,6BAAU,CAAC,QAAQ;YAC/B,UAAU,EAAE,mBAAmB;YAC/B,UAAU,EAAE,IAAI,IAAI,EAAE;YACtB,gBAAgB,EAAE;gBAChB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,UAAU;gBAClB,mBAAmB,EAAE,CAAC;aACvB;YACD,kBAAkB,EAAE,IAAI;YACxB,qBAAqB,EAAE,CAAC;SACzB,CAAC;QACF,KAAK,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,yEAAiC,CAAC,CAAC;YAChE,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAE,8BAAc,CAAC,QAAQ,EAAE;gBAClC,UAAU,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBAClC,YAAY,EAAE,CAAC;gBACf,aAAa,EAAE,UAAU;gBACzB,WAAW,EAAE,WAAW;gBACxB,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;aAC7B,CAAC;YAEF,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEjG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACxD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAC9D,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;YAClE,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YACpE,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YAChE,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YACnE,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,kBAAkB,EAAE,KAAK;aAC1B,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,YAAY,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,kBAAkB,EAAE,SAAS;gBAC7B,gBAAgB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;aACpC,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE9C,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,gBAAgB;aAC7B,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtD,MAAM,QAAQ,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAClE,GAAG,SAAS;gBACZ,UAAU,EAAE,cAAc;aAC3B,CAAC,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,mBAAmB;aAChC,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE9C,MAAM,YAAY,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAErD,MAAM,aAAa,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtD,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,WAAW;aACnC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,UAAU,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE/C,MAAM,eAAe,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,eAAe,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEzD,MAAM,gBAAgB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,YAAY;aACpC,CAAC,CAAC;YACH,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1D,MAAM,mBAAmB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7D,MAAM,UAAU,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElD,MAAM,YAAY,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElD,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,SAAS,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,qBAAqB,EAAE,CAAC;aACzB,CAAC,CAAC;YACH,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,SAAS,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,qBAAqB,EAAE,EAAE;aAC1B,CAAC,CAAC;YACH,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY;YAEjE,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,qBAAqB,EAAE,EAAE;aAC1B,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE5D,MAAM,SAAS,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,qBAAqB,EAAE,EAAE;aAC1B,CAAC,CAAC;YACH,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAExD,MAAM,aAAa,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,qBAAqB,EAAE,GAAG;aAC3B,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,eAAe,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,qBAAqB,EAAE,SAAS;aACjC,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ;YAC9D,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;YAChE,MAAM,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,mBAAmB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,kBAAkB,EAAE,KAAK;aAC1B,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,gBAAgB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,sBAAsB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;gBACtC,kBAAkB,EAAE,KAAK;aAC1B,CAAC,CAAC;YACH,MAAM,CAAC,sBAAsB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,aAAa,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,mBAAmB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;gBACrC,kBAAkB,EAAE,KAAK;aAC1B,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,UAAU,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,OAAO,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;YAC3D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,OAAO,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,aAAa,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,aAAa,CAAC,yBAAyB,EAAE,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;YACzD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,kBAAkB,EAAE,KAAK;aAC1B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,WAAW,CAAC,yBAAyB,EAAE,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,mBAAmB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,kBAAkB,EAAE,KAAK;aAC1B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,mBAAmB,CAAC,yBAAyB,EAAE,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,sCAAsC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,SAAS,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,qBAAqB,EAAE,EAAE;aAC1B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,SAAS,CAAC,yBAAyB,EAAE,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,kBAAkB,EAAE,KAAK;aAC1B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,WAAW,CAAC,sBAAsB,EAAE,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,mBAAmB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,kBAAkB,EAAE,KAAK;aAC1B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,mBAAmB,CAAC,sBAAsB,EAAE,CAAC;YAC7D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,SAAS,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,qBAAqB,EAAE,EAAE;aAC1B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,SAAS,CAAC,sBAAsB,EAAE,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,OAAO,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAE5C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,kBAAkB,EAAE,KAAK;gBACzB,UAAU,EAAE,mBAAmB;aAChC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,WAAW,CAAC,mBAAmB,EAAE,CAAC;YAElD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,UAAU,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAE7C,MAAM,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACpD,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,gBAAgB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;YAExD,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;gBACjC,kBAAkB,EAAE,KAAK;aAC1B,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,WAAW,CAAC,iBAAiB,EAAE,CAAC;YAEnD,MAAM,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,UAAU,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,UAAU,CAAC,iBAAiB,EAAE,CAAC;YAElD,MAAM,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5C,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,YAAY,GAAG,KAAK,CAAC,uBAAuB,EAAE,CAAC;YACrD,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;YAC9D,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,aAAa,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,aAAa,CAAC,uBAAuB,EAAE,CAAC;YAC7D,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YAC5D,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,UAAU,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,mBAAmB;aAC3C,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,UAAU,CAAC,uBAAuB,EAAE,CAAC;YAC1D,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YAC3D,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,sBAAsB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAChF,GAAG,SAAS;gBACZ,kBAAkB,EAAE,KAAK;aAC1B,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,sBAAsB,CAAC,uBAAuB,EAAE,CAAC;YACtE,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,yCAAyC,CAAC,CAAC;YAC1E,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,mBAAmB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;gBACrC,kBAAkB,EAAE,KAAK;aAC1B,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,mBAAmB,CAAC,uBAAuB,EAAE,CAAC;YACnE,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,iDAAiD,CAAC,CAAC;YAClF,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mEAAmE,EAAE,GAAG,EAAE;YAC3E,MAAM,mBAAmB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,kBAAkB,EAAE,KAAK;aAC1B,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,mBAAmB,CAAC,uBAAuB,EAAE,CAAC;YACnE,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;YAClE,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAEpD,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAClE,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACjE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACzD,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9C,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBACxC,YAAY,EAAE,IAAI;gBAClB,mBAAmB,EAAE,MAAM;gBAC3B,WAAW,EAAE,MAAM;gBACnB,kBAAkB,EAAE,IAAI;gBACxB,WAAW,EAAE,IAAI;gBACjB,gBAAgB,EAAE;oBAChB,UAAU,EAAE,6BAAU,CAAC,QAAQ;oBAC/B,aAAa,EAAE,CAAC;oBAChB,mBAAmB,EAAE,MAAM;oBAC3B,WAAW,EAAE,MAAM;oBACnB,YAAY,EAAE,IAAI;oBAClB,WAAW,EAAE,IAAI;oBACjB,kBAAkB,EAAE,IAAI;iBACzB;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,cAAc,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,gBAAgB,EAAE,SAAS;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,aAAa,EAAE,CAAC;YACxD,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,cAAc,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,kBAAkB,EAAE,SAAS;aAC9B,CAAC,CAAC;YAEH,MAAM,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,eAAe,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,qBAAqB,EAAE,SAAS;aACjC,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,qBAAqB,CAAC,CAAC,aAAa,EAAE,CAAC;YAC9D,MAAM,CAAC,eAAe,CAAC,mBAAmB,EAAE,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,YAAY,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,gBAA8B;aAC3C,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-executed.domain-event.spec.ts"], "sourcesContent": ["import { ResponseActionExecutedDomainEvent, ResponseActionExecutedEventData } from '../response-action-executed.domain-event';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { ActionType } from '../../enums/action-type.enum';\r\n\r\ndescribe('ResponseActionExecutedDomainEvent', () => {\r\n  let aggregateId: UniqueEntityId;\r\n  let eventData: ResponseActionExecutedEventData;\r\n  let event: ResponseActionExecutedDomainEvent;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.generate();\r\n    eventData = {\r\n      actionType: ActionType.BLOCK_IP,\r\n      executedBy: 'system@automation',\r\n      executedAt: new Date(),\r\n      executionResults: {\r\n        blocked: true,\r\n        ruleId: 'rule-123',\r\n        affectedConnections: 5,\r\n      },\r\n      successCriteriaMet: true,\r\n      actualDurationMinutes: 2,\r\n    };\r\n    event = new ResponseActionExecutedDomainEvent(aggregateId, eventData);\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create event with required data', () => {\r\n      expect(event).toBeInstanceOf(ResponseActionExecutedDomainEvent);\r\n      expect(event.aggregateId).toBe(aggregateId);\r\n      expect(event.eventData).toBe(eventData);\r\n      expect(event.occurredOn).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should create event with custom options', () => {\r\n      const customOptions = {\r\n        eventId: UniqueEntityId.generate(),\r\n        occurredOn: new Date('2023-01-01'),\r\n        eventVersion: 2,\r\n        correlationId: 'corr-123',\r\n        causationId: 'cause-456',\r\n        metadata: { source: 'test' },\r\n      };\r\n\r\n      const customEvent = new ResponseActionExecutedDomainEvent(aggregateId, eventData, customOptions);\r\n\r\n      expect(customEvent.eventId).toBe(customOptions.eventId);\r\n      expect(customEvent.occurredOn).toBe(customOptions.occurredOn);\r\n      expect(customEvent.eventVersion).toBe(customOptions.eventVersion);\r\n      expect(customEvent.correlationId).toBe(customOptions.correlationId);\r\n      expect(customEvent.causationId).toBe(customOptions.causationId);\r\n      expect(customEvent.metadata).toEqual(customOptions.metadata);\r\n    });\r\n  });\r\n\r\n  describe('getters', () => {\r\n    it('should provide access to event data properties', () => {\r\n      expect(event.actionType).toBe(ActionType.BLOCK_IP);\r\n      expect(event.executedBy).toBe('system@automation');\r\n      expect(event.executedAt).toBe(eventData.executedAt);\r\n      expect(event.executionResults).toEqual(eventData.executionResults);\r\n      expect(event.successCriteriaMet).toBe(true);\r\n      expect(event.actualDurationMinutes).toBe(2);\r\n    });\r\n  });\r\n\r\n  describe('execution status analysis', () => {\r\n    it('should identify successful executions', () => {\r\n      expect(event.isSuccessful()).toBe(true);\r\n      expect(event.isFailed()).toBe(false);\r\n      expect(event.isPartial()).toBe(false);\r\n    });\r\n\r\n    it('should identify failed executions', () => {\r\n      const failedEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        successCriteriaMet: false,\r\n      });\r\n\r\n      expect(failedEvent.isSuccessful()).toBe(false);\r\n      expect(failedEvent.isFailed()).toBe(true);\r\n      expect(failedEvent.isPartial()).toBe(false);\r\n    });\r\n\r\n    it('should identify partial executions', () => {\r\n      const partialEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        successCriteriaMet: undefined,\r\n        executionResults: { partial: true },\r\n      });\r\n\r\n      expect(partialEvent.isSuccessful()).toBe(false);\r\n      expect(partialEvent.isFailed()).toBe(false);\r\n      expect(partialEvent.isPartial()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('execution type analysis', () => {\r\n    it('should identify automated executions', () => {\r\n      expect(event.isAutomatedExecution()).toBe(true);\r\n      expect(event.isManualExecution()).toBe(false);\r\n\r\n      const systemEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        executedBy: 'automation-bot',\r\n      });\r\n      expect(systemEvent.isAutomatedExecution()).toBe(true);\r\n\r\n      const botEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        executedBy: 'security-bot',\r\n      });\r\n      expect(botEvent.isAutomatedExecution()).toBe(true);\r\n    });\r\n\r\n    it('should identify manual executions', () => {\r\n      const manualEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        executedBy: '<EMAIL>',\r\n      });\r\n\r\n      expect(manualEvent.isAutomatedExecution()).toBe(false);\r\n      expect(manualEvent.isManualExecution()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('action type classification', () => {\r\n    it('should identify security-critical actions', () => {\r\n      expect(event.isSecurityCritical()).toBe(true);\r\n\r\n      const isolateEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.ISOLATE_SYSTEM,\r\n      });\r\n      expect(isolateEvent.isSecurityCritical()).toBe(true);\r\n\r\n      const shutdownEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SHUTDOWN_SYSTEM,\r\n      });\r\n      expect(shutdownEvent.isSecurityCritical()).toBe(true);\r\n\r\n      const deleteEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.DELETE_FILE,\r\n      });\r\n      expect(deleteEvent.isSecurityCritical()).toBe(true);\r\n\r\n      const emailEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(emailEvent.isSecurityCritical()).toBe(false);\r\n    });\r\n\r\n    it('should identify containment actions', () => {\r\n      expect(event.isContainmentAction()).toBe(true);\r\n\r\n      const quarantineEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.QUARANTINE_FILE,\r\n      });\r\n      expect(quarantineEvent.isContainmentAction()).toBe(true);\r\n\r\n      const blockDomainEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.BLOCK_DOMAIN,\r\n      });\r\n      expect(blockDomainEvent.isContainmentAction()).toBe(true);\r\n\r\n      const disableAccountEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.DISABLE_ACCOUNT,\r\n      });\r\n      expect(disableAccountEvent.isContainmentAction()).toBe(true);\r\n\r\n      const emailEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(emailEvent.isContainmentAction()).toBe(false);\r\n    });\r\n\r\n    it('should identify recovery actions', () => {\r\n      const backupEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n      expect(backupEvent.isRecoveryAction()).toBe(true);\r\n\r\n      const rebuildEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.REBUILD_SYSTEM,\r\n      });\r\n      expect(rebuildEvent.isRecoveryAction()).toBe(true);\r\n\r\n      const enableEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.ENABLE_SERVICE,\r\n      });\r\n      expect(enableEvent.isRecoveryAction()).toBe(true);\r\n\r\n      expect(event.isRecoveryAction()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('performance analysis', () => {\r\n    it('should identify fast executions', () => {\r\n      const fastEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actualDurationMinutes: 3,\r\n      });\r\n      expect(fastEvent.isFastExecution()).toBe(true);\r\n      expect(event.isFastExecution()).toBe(true); // 2 minutes\r\n    });\r\n\r\n    it('should identify slow executions', () => {\r\n      const slowEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actualDurationMinutes: 45,\r\n      });\r\n      expect(slowEvent.isSlowExecution()).toBe(true);\r\n      expect(event.isSlowExecution()).toBe(false);\r\n    });\r\n\r\n    it('should categorize performance correctly', () => {\r\n      expect(event.getPerformanceCategory()).toBe('fast'); // 2 minutes\r\n\r\n      const normalEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actualDurationMinutes: 15,\r\n      });\r\n      expect(normalEvent.getPerformanceCategory()).toBe('normal');\r\n\r\n      const slowEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actualDurationMinutes: 45,\r\n      });\r\n      expect(slowEvent.getPerformanceCategory()).toBe('slow');\r\n\r\n      const verySlowEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actualDurationMinutes: 150,\r\n      });\r\n      expect(verySlowEvent.getPerformanceCategory()).toBe('very_slow');\r\n    });\r\n\r\n    it('should handle missing duration', () => {\r\n      const noDurationEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actualDurationMinutes: undefined,\r\n      });\r\n\r\n      expect(noDurationEvent.isFastExecution()).toBe(true); // 0 < 5\r\n      expect(noDurationEvent.isSlowExecution()).toBe(false); // 0 < 30\r\n      expect(noDurationEvent.getPerformanceCategory()).toBe('fast');\r\n    });\r\n  });\r\n\r\n  describe('impact assessment', () => {\r\n    it('should assess impact for successful security-critical actions', () => {\r\n      expect(event.getImpactLevel()).toBe('high');\r\n    });\r\n\r\n    it('should assess impact for failed security-critical actions', () => {\r\n      const failedCriticalEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        successCriteriaMet: false,\r\n      });\r\n      expect(failedCriticalEvent.getImpactLevel()).toBe('critical');\r\n    });\r\n\r\n    it('should assess impact for successful containment actions', () => {\r\n      const containmentEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.QUARANTINE_FILE,\r\n      });\r\n      expect(containmentEvent.getImpactLevel()).toBe('high');\r\n    });\r\n\r\n    it('should assess impact for failed containment actions', () => {\r\n      const failedContainmentEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.QUARANTINE_FILE,\r\n        successCriteriaMet: false,\r\n      });\r\n      expect(failedContainmentEvent.getImpactLevel()).toBe('critical');\r\n    });\r\n\r\n    it('should assess impact for successful recovery actions', () => {\r\n      const recoveryEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n      expect(recoveryEvent.getImpactLevel()).toBe('medium');\r\n    });\r\n\r\n    it('should assess impact for failed recovery actions', () => {\r\n      const failedRecoveryEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n        successCriteriaMet: false,\r\n      });\r\n      expect(failedRecoveryEvent.getImpactLevel()).toBe('high');\r\n    });\r\n\r\n    it('should assess impact for low-impact actions', () => {\r\n      const emailEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(emailEvent.getImpactLevel()).toBe('low');\r\n    });\r\n  });\r\n\r\n  describe('post-execution recommendations', () => {\r\n    it('should recommend post-actions for successful executions', () => {\r\n      const actions = event.getRecommendedPostActions();\r\n      expect(actions).toContain('Validate action effectiveness');\r\n      expect(actions).toContain('Update related security status');\r\n    });\r\n\r\n    it('should recommend post-actions for successful containment', () => {\r\n      const actions = event.getRecommendedPostActions();\r\n      expect(actions).toContain('Monitor for containment bypass');\r\n      expect(actions).toContain('Assess containment completeness');\r\n    });\r\n\r\n    it('should recommend post-actions for successful recovery', () => {\r\n      const recoveryEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n\r\n      const actions = recoveryEvent.getRecommendedPostActions();\r\n      expect(actions).toContain('Verify system functionality');\r\n      expect(actions).toContain('Test recovered services');\r\n    });\r\n\r\n    it('should recommend post-actions for failed executions', () => {\r\n      const failedEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        successCriteriaMet: false,\r\n      });\r\n\r\n      const actions = failedEvent.getRecommendedPostActions();\r\n      expect(actions).toContain('Analyze execution failure');\r\n      expect(actions).toContain('Assess security impact of failure');\r\n      expect(actions).toContain('Consider alternative actions');\r\n    });\r\n\r\n    it('should recommend post-actions for failed security-critical actions', () => {\r\n      const failedCriticalEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        successCriteriaMet: false,\r\n      });\r\n\r\n      const actions = failedCriticalEvent.getRecommendedPostActions();\r\n      expect(actions).toContain('Escalate to incident response team');\r\n      expect(actions).toContain('Implement manual fallback procedures');\r\n    });\r\n\r\n    it('should recommend post-actions for slow executions', () => {\r\n      const slowEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actualDurationMinutes: 45,\r\n      });\r\n\r\n      const actions = slowEvent.getRecommendedPostActions();\r\n      expect(actions).toContain('Investigate performance issues');\r\n      expect(actions).toContain('Optimize action execution');\r\n    });\r\n  });\r\n\r\n  describe('notification targets', () => {\r\n    it('should identify targets for successful executions', () => {\r\n      const targets = event.getNotificationTargets();\r\n      expect(targets).toContain('action-requestor');\r\n      expect(targets).toContain('security-team');\r\n    });\r\n\r\n    it('should identify targets for failed executions', () => {\r\n      const failedEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        successCriteriaMet: false,\r\n      });\r\n\r\n      const targets = failedEvent.getNotificationTargets();\r\n      expect(targets).toContain('action-requestor');\r\n      expect(targets).toContain('security-team');\r\n    });\r\n\r\n    it('should identify targets for failed security-critical actions', () => {\r\n      const failedCriticalEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        successCriteriaMet: false,\r\n      });\r\n\r\n      const targets = failedCriticalEvent.getNotificationTargets();\r\n      expect(targets).toContain('incident-response-team');\r\n      expect(targets).toContain('security-managers');\r\n    });\r\n\r\n    it('should identify targets for slow executions', () => {\r\n      const slowEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actualDurationMinutes: 45,\r\n      });\r\n\r\n      const targets = slowEvent.getNotificationTargets();\r\n      expect(targets).toContain('performance-team');\r\n    });\r\n  });\r\n\r\n  describe('execution metrics', () => {\r\n    it('should generate comprehensive execution metrics', () => {\r\n      const metrics = event.getExecutionMetrics();\r\n\r\n      expect(metrics.actionType).toBe(ActionType.BLOCK_IP);\r\n      expect(metrics.executionTime).toBe(2);\r\n      expect(metrics.performanceCategory).toBe('fast');\r\n      expect(metrics.impactLevel).toBe('high');\r\n      expect(metrics.isSuccessful).toBe(true);\r\n      expect(metrics.isAutomated).toBe(true);\r\n      expect(metrics.isSecurityCritical).toBe(true);\r\n    });\r\n\r\n    it('should generate metrics for failed executions', () => {\r\n      const failedEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        successCriteriaMet: false,\r\n        executedBy: '<EMAIL>',\r\n      });\r\n\r\n      const metrics = failedEvent.getExecutionMetrics();\r\n\r\n      expect(metrics.isSuccessful).toBe(false);\r\n      expect(metrics.isAutomated).toBe(false);\r\n      expect(metrics.impactLevel).toBe('critical');\r\n    });\r\n  });\r\n\r\n  describe('compliance information', () => {\r\n    it('should provide compliance info for security-critical actions', () => {\r\n      const compliance = event.getComplianceInfo();\r\n\r\n      expect(compliance.requiresDocumentation).toBe(true);\r\n      expect(compliance.auditLevel).toBe('comprehensive');\r\n      expect(compliance.retentionPeriod).toBe('permanent');\r\n    });\r\n\r\n    it('should provide compliance info for containment actions', () => {\r\n      const containmentEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.QUARANTINE_FILE,\r\n      });\r\n\r\n      const compliance = containmentEvent.getComplianceInfo();\r\n\r\n      expect(compliance.auditLevel).toBe('comprehensive');\r\n    });\r\n\r\n    it('should provide compliance info for failed actions', () => {\r\n      const failedEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n        successCriteriaMet: false,\r\n      });\r\n\r\n      const compliance = failedEvent.getComplianceInfo();\r\n\r\n      expect(compliance.requiresDocumentation).toBe(true);\r\n      expect(compliance.retentionPeriod).toBe('extended');\r\n    });\r\n\r\n    it('should provide compliance info for basic actions', () => {\r\n      const emailEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n\r\n      const compliance = emailEvent.getComplianceInfo();\r\n\r\n      expect(compliance.requiresDocumentation).toBe(false);\r\n      expect(compliance.auditLevel).toBe('basic');\r\n      expect(compliance.retentionPeriod).toBe('standard');\r\n    });\r\n  });\r\n\r\n  describe('security implications', () => {\r\n    it('should identify implications for successful containment', () => {\r\n      const implications = event.getSecurityImplications();\r\n      expect(implications).toContain('Threat containment achieved');\r\n      expect(implications).toContain('Risk exposure reduced');\r\n    });\r\n\r\n    it('should identify implications for successful recovery', () => {\r\n      const recoveryEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n\r\n      const implications = recoveryEvent.getSecurityImplications();\r\n      expect(implications).toContain('System recovery completed');\r\n      expect(implications).toContain('Service availability restored');\r\n    });\r\n\r\n    it('should identify implications for successful vulnerability patching', () => {\r\n      const patchEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.PATCH_VULNERABILITY,\r\n      });\r\n\r\n      const implications = patchEvent.getSecurityImplications();\r\n      expect(implications).toContain('Vulnerability remediated');\r\n      expect(implications).toContain('Attack surface reduced');\r\n    });\r\n\r\n    it('should identify implications for failed containment', () => {\r\n      const failedContainmentEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        successCriteriaMet: false,\r\n      });\r\n\r\n      const implications = failedContainmentEvent.getSecurityImplications();\r\n      expect(implications).toContain('Containment failure - threat may spread');\r\n      expect(implications).toContain('Increased risk exposure');\r\n    });\r\n\r\n    it('should identify implications for failed recovery', () => {\r\n      const failedRecoveryEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n        successCriteriaMet: false,\r\n      });\r\n\r\n      const implications = failedRecoveryEvent.getSecurityImplications();\r\n      expect(implications).toContain('Recovery failure - service disruption continues');\r\n      expect(implications).toContain('Business impact ongoing');\r\n    });\r\n\r\n    it('should identify implications for failed security-critical actions', () => {\r\n      const failedCriticalEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        successCriteriaMet: false,\r\n      });\r\n\r\n      const implications = failedCriticalEvent.getSecurityImplications();\r\n      expect(implications).toContain('Critical security action failed');\r\n      expect(implications).toContain('Manual intervention required');\r\n    });\r\n  });\r\n\r\n  describe('integration event conversion', () => {\r\n    it('should convert to integration event format', () => {\r\n      const integrationEvent = event.toIntegrationEvent();\r\n\r\n      expect(integrationEvent.eventType).toBe('ResponseActionExecuted');\r\n      expect(integrationEvent.action).toBe('response_action_executed');\r\n      expect(integrationEvent.resource).toBe('ResponseAction');\r\n      expect(integrationEvent.resourceId).toBe(aggregateId.toString());\r\n      expect(integrationEvent.data).toBe(eventData);\r\n      expect(integrationEvent.metadata).toEqual({\r\n        isSuccessful: true,\r\n        performanceCategory: 'fast',\r\n        impactLevel: 'high',\r\n        isSecurityCritical: true,\r\n        isAutomated: true,\r\n        executionMetrics: {\r\n          actionType: ActionType.BLOCK_IP,\r\n          executionTime: 2,\r\n          performanceCategory: 'fast',\r\n          impactLevel: 'high',\r\n          isSuccessful: true,\r\n          isAutomated: true,\r\n          isSecurityCritical: true,\r\n        },\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle events without execution results', () => {\r\n      const noResultsEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        executionResults: undefined,\r\n      });\r\n\r\n      expect(noResultsEvent.executionResults).toBeUndefined();\r\n      expect(noResultsEvent.isPartial()).toBe(false);\r\n    });\r\n\r\n    it('should handle events without success criteria status', () => {\r\n      const noSuccessEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        successCriteriaMet: undefined,\r\n      });\r\n\r\n      expect(noSuccessEvent.isSuccessful()).toBe(false);\r\n      expect(noSuccessEvent.isFailed()).toBe(false);\r\n      expect(noSuccessEvent.isPartial()).toBe(true);\r\n    });\r\n\r\n    it('should handle events without duration', () => {\r\n      const noDurationEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actualDurationMinutes: undefined,\r\n      });\r\n\r\n      expect(noDurationEvent.actualDurationMinutes).toBeUndefined();\r\n      expect(noDurationEvent.getExecutionMetrics().executionTime).toBe(0);\r\n    });\r\n\r\n    it('should handle unknown action types gracefully', () => {\r\n      const unknownEvent = new ResponseActionExecutedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: 'UNKNOWN_ACTION' as ActionType,\r\n      });\r\n\r\n      expect(unknownEvent.isSecurityCritical()).toBe(false);\r\n      expect(unknownEvent.isContainmentAction()).toBe(false);\r\n      expect(unknownEvent.isRecoveryAction()).toBe(false);\r\n      expect(unknownEvent.getImpactLevel()).toBe('low');\r\n    });\r\n  });\r\n});"], "version": 3}