{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,oEAAgE;AAChE,wFAA8E;AAC9E,8GAAmG;AACnG,4GAAiG;AACjG,4FAAkF;AAClF,yFAAqF;AACrF,6FAAyF;AACzF,0GAAsG;AAEtG,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,IAAI,OAA6B,CAAC;IAClC,IAAI,uBAA+D,CAAC;IACpE,IAAI,oBAAsE,CAAC;IAC3E,IAAI,mBAAoE,CAAC;IACzE,IAAI,eAA+C,CAAC;IACpD,IAAI,aAAyC,CAAC;IAC9C,IAAI,YAAuC,CAAC;IAC5C,IAAI,mBAAqD,CAAC;IAE1D,MAAM,iBAAiB,GAAG;QACxB,EAAE,EAAE,sCAAsC;QAC1C,UAAU,EAAE,eAAe;QAC3B,KAAK,EAAE,oBAAoB;QAC3B,WAAW,EAAE,gCAAgC;QAC7C,QAAQ,EAAE,MAAM;QAChB,SAAS,EAAE,GAAG;QACd,aAAa,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACrC,gBAAgB,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACxC,WAAW,EAAE,KAAK;QAClB,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,KAAK;QAChB,cAAc,EAAE,IAAI;QACpB,gBAAgB,EAAE;YAChB;gBACE,MAAM,EAAE,aAAa;gBACrB,OAAO,EAAE,cAAc;gBACvB,OAAO,EAAE,OAAO;aACjB;SACF;QACD,UAAU,EAAE;YACV,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,UAAU;YAChB,UAAU,EAAE,MAAM;YAClB,WAAW,EAAE,sBAAsB;SACpC;QACD,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC;QAClD,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC9B,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC5B,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;KACrB,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,cAAc,GAAG;YACrB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;YAClB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;YACpB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;YAChB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;gBACjC,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAC7C,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACjC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACnC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACtC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;gBAC1B,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;gBAClB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAClC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACrC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACnC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;aACtB,CAAC,CAAC;SACJ,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,4CAAoB;gBACpB;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,oCAAa,CAAC;oBAC1C,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,yDAAuB,CAAC;oBACpD,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,uDAAsB,CAAC;oBACnD,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,oBAAK,CAAC;oBAClC,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE;wBACR,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;wBAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;wBACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;wBACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;qBACjB;iBACF;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE;wBACR,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;qBACzB;iBACF;gBACD;oBACE,OAAO,EAAE,0CAAmB;oBAC5B,QAAQ,EAAE;wBACR,mCAAmC,EAAE,IAAI,CAAC,EAAE,EAAE;qBAC/C;iBACF;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAuB,4CAAoB,CAAC,CAAC;QACjE,uBAAuB,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,oCAAa,CAAC,CAAC,CAAC;QACxE,oBAAoB,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,yDAAuB,CAAC,CAAC,CAAC;QAC/E,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,uDAAsB,CAAC,CAAC,CAAC;QAC7E,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,oBAAK,CAAC,CAAC,CAAC;QACxD,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,8BAAa,CAAC,CAAC;QAC1C,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,4BAAY,CAAC,CAAC;QACxC,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,0CAAmB,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAC3B,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,kBAAkB,GAAG;gBACzB,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE;gBACpC,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE;gBACjC,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;gBACnC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;aACjC,CAAC;YAEF,uBAAuB,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAC9F,uBAAuB,CAAC,KAAK;iBAC1B,qBAAqB,CAAC,CAAC,CAAC,CAAC,cAAc;iBACvC,qBAAqB,CAAC,CAAC,CAAC,CAAC,gBAAgB;iBACzC,qBAAqB,CAAC,CAAC,CAAC,CAAC,cAAc;iBACvC,qBAAqB,CAAC,CAAC,CAAC,CAAC,SAAS;iBAClC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU;YAExC,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAEpE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,EAAE,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,gBAAgB,GAAG,uBAAuB,CAAC,kBAAkB,EAAE,CAAC;YACtE,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAE7E,MAAM,cAAc,GAAG;gBACrB,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,UAAU,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;gBAChC,WAAW,EAAE,IAAI;gBACjB,UAAU,EAAE,MAAM;aACnB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;YAEnE,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,mCAAmC,EACnC,EAAE,UAAU,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,CACrC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,gBAAgB,GAAG,uBAAuB,CAAC,kBAAkB,EAAE,CAAC;YACtE,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAE5D,MAAM,cAAc,GAAG;gBACrB,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;aACV,CAAC;YAEF,MAAM,OAAO,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;YAEpD,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YACvD,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAErE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,sCAAsC,CAAC,CAAC;YAE7F,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC1C,MAAM,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,sCAAsC,EAAE;gBACrD,SAAS,EAAE;oBACT,aAAa;oBACb,YAAY;oBACZ,gBAAgB;oBAChB,sBAAsB;iBACvB;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;YAC3E,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CACV,OAAO,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CACnD,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,iBAAiB,GAAG;gBACxB,UAAU,EAAE,eAAe;gBAC3B,KAAK,EAAE,mBAAmB;gBAC1B,WAAW,EAAE,+BAA+B;gBAC5C,QAAQ,EAAE,QAAQ;gBAClB,gBAAgB,EAAE;oBAChB;wBACE,MAAM,EAAE,YAAY;wBACpB,OAAO,EAAE,aAAa;wBACtB,OAAO,EAAE,OAAO;qBACjB;iBACF;gBACD,UAAU,EAAE;oBACV,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,UAAU;oBAChB,UAAU,EAAE,MAAM;oBAClB,WAAW,EAAE,sBAAsB;iBACpC;aACF,CAAC;YAEF,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,4BAA4B;YACrF,uBAAuB,CAAC,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;YAClE,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAElE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;YAEhF,MAAM,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CACzD,MAAM,CAAC,gBAAgB,CAAC;gBACtB,GAAG,iBAAiB;gBACpB,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC/B,gBAAgB,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aACnC,CAAC,CACH,CAAC;YACF,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;YAC7E,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,QAAQ,EACR,eAAe,EACf,iBAAiB,CAAC,EAAE,EACpB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,iBAAiB,GAAG;gBACxB,UAAU,EAAE,eAAe;gBAC3B,KAAK,EAAE,wBAAwB;gBAC/B,WAAW,EAAE,aAAa;gBAC1B,QAAQ,EAAE,MAAM;gBAChB,gBAAgB,EAAE,EAAE;gBACpB,UAAU,EAAE;oBACV,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,UAAU;oBAChB,UAAU,EAAE,MAAM;oBAClB,WAAW,EAAE,sBAAsB;iBACpC;aACF,CAAC;YAEF,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAErE,MAAM,MAAM,CACV,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAC3D,CAAC,OAAO,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,OAAO,GAAG;gBACd,QAAQ,EAAE,UAAU;gBACpB,SAAS,EAAE,GAAG;aACf,CAAC;YAEF,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YACrE,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBAC7C,GAAG,iBAAiB;gBACpB,GAAG,OAAO;aACX,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,sCAAsC,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YAE9G,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACxD,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,QAAQ,EACR,eAAe,EACf,sCAAsC,EACtC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,UAAU,EAAE,iBAAiB,CAAC,UAAU;gBACxC,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC5B,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;YAC3E,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CACV,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,EAAE,EAAE,UAAU,CAAC,CAC/D,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YACrE,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAEpE,MAAM,OAAO,CAAC,mBAAmB,CAAC,sCAAsC,EAAE,UAAU,CAAC,CAAC;YAEtF,MAAM,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;YAC/E,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,QAAQ,EACR,eAAe,EACf,sCAAsC,EACtC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,UAAU,EAAE,iBAAiB,CAAC,UAAU;gBACxC,QAAQ,EAAE,iBAAiB,CAAC,QAAQ;gBACpC,KAAK,EAAE,iBAAiB,CAAC,KAAK;aAC/B,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;YAC3E,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CACV,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAC3D,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,sEAAsE,EAAE,KAAK,IAAI,EAAE;YACpF,MAAM,iBAAiB,GAAG;gBACxB,EAAE,GAAG,iBAAiB,EAAE,QAAQ,EAAE,UAAU,EAAE;aAC/C,CAAC;YAEF,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAElE,MAAM,OAAO,CAAC,sBAAsB,EAAE,CAAC;YAEvC,MAAM,CAAC,mBAAmB,CAAC,mCAAmC,CAAC,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAC1G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAE5E,MAAM,MAAM,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACtE,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAC9C,6CAA6C,EAC7C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,gBAAgB;aACxB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { VulnerabilityService } from '../vulnerability.service';\r\nimport { Vulnerability } from '../../../domain/entities/vulnerability.entity';\r\nimport { VulnerabilityAssessment } from '../../../domain/entities/vulnerability-assessment.entity';\r\nimport { VulnerabilityException } from '../../../domain/entities/vulnerability-exception.entity';\r\nimport { Asset } from '../../../../asset-management/domain/entities/asset.entity';\r\nimport { LoggerService } from '../../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../../infrastructure/notification/notification.service';\r\n\r\ndescribe('VulnerabilityService', () => {\r\n  let service: VulnerabilityService;\r\n  let vulnerabilityRepository: jest.Mocked<Repository<Vulnerability>>;\r\n  let assessmentRepository: jest.Mocked<Repository<VulnerabilityAssessment>>;\r\n  let exceptionRepository: jest.Mocked<Repository<VulnerabilityException>>;\r\n  let assetRepository: jest.Mocked<Repository<Asset>>;\r\n  let loggerService: jest.Mocked<LoggerService>;\r\n  let auditService: jest.Mocked<AuditService>;\r\n  let notificationService: jest.Mocked<NotificationService>;\r\n\r\n  const mockVulnerability = {\r\n    id: '123e4567-e89b-12d3-a456-426614174000',\r\n    identifier: 'CVE-2023-1234',\r\n    title: 'Test Vulnerability',\r\n    description: 'Test vulnerability description',\r\n    severity: 'high',\r\n    cvssScore: 7.5,\r\n    publishedDate: new Date('2023-01-01'),\r\n    lastModifiedDate: new Date('2023-01-02'),\r\n    exploitable: false,\r\n    hasExploit: false,\r\n    inTheWild: false,\r\n    patchAvailable: true,\r\n    affectedProducts: [\r\n      {\r\n        vendor: 'Test Vendor',\r\n        product: 'Test Product',\r\n        version: '1.0.0',\r\n      },\r\n    ],\r\n    dataSource: {\r\n      name: 'Test Source',\r\n      type: 'internal',\r\n      confidence: 'high',\r\n      lastUpdated: '2023-01-01T00:00:00Z',\r\n    },\r\n    getSummary: jest.fn(),\r\n    exportForReporting: jest.fn(),\r\n    calculateRiskScore: jest.fn().mockReturnValue(7.5),\r\n    updateExploitStatus: jest.fn(),\r\n    updatePatchStatus: jest.fn(),\r\n    addTag: jest.fn(),\r\n    removeTag: jest.fn(),\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const mockRepository = {\r\n      create: jest.fn(),\r\n      save: jest.fn(),\r\n      find: jest.fn(),\r\n      findOne: jest.fn(),\r\n      findOneBy: jest.fn(),\r\n      count: jest.fn(),\r\n      remove: jest.fn(),\r\n      createQueryBuilder: jest.fn(() => ({\r\n        leftJoinAndSelect: jest.fn().mockReturnThis(),\r\n        where: jest.fn().mockReturnThis(),\r\n        andWhere: jest.fn().mockReturnThis(),\r\n        orderBy: jest.fn().mockReturnThis(),\r\n        addOrderBy: jest.fn().mockReturnThis(),\r\n        skip: jest.fn().mockReturnThis(),\r\n        take: jest.fn().mockReturnThis(),\r\n        getManyAndCount: jest.fn(),\r\n        getMany: jest.fn(),\r\n        select: jest.fn().mockReturnThis(),\r\n        addSelect: jest.fn().mockReturnThis(),\r\n        groupBy: jest.fn().mockReturnThis(),\r\n        getRawMany: jest.fn(),\r\n      })),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        VulnerabilityService,\r\n        {\r\n          provide: getRepositoryToken(Vulnerability),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(VulnerabilityAssessment),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(VulnerabilityException),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(Asset),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: {\r\n            debug: jest.fn(),\r\n            log: jest.fn(),\r\n            warn: jest.fn(),\r\n            error: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: {\r\n            logUserAction: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: NotificationService,\r\n          useValue: {\r\n            sendNewCriticalVulnerabilitiesAlert: jest.fn(),\r\n          },\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<VulnerabilityService>(VulnerabilityService);\r\n    vulnerabilityRepository = module.get(getRepositoryToken(Vulnerability));\r\n    assessmentRepository = module.get(getRepositoryToken(VulnerabilityAssessment));\r\n    exceptionRepository = module.get(getRepositoryToken(VulnerabilityException));\r\n    assetRepository = module.get(getRepositoryToken(Asset));\r\n    loggerService = module.get(LoggerService);\r\n    auditService = module.get(AuditService);\r\n    notificationService = module.get(NotificationService);\r\n  });\r\n\r\n  it('should be defined', () => {\r\n    expect(service).toBeDefined();\r\n  });\r\n\r\n  describe('getVulnerabilityDashboard', () => {\r\n    it('should return vulnerability dashboard data', async () => {\r\n      const mockSeverityCounts = [\r\n        { severity: 'critical', count: '5' },\r\n        { severity: 'high', count: '10' },\r\n        { severity: 'medium', count: '15' },\r\n        { severity: 'low', count: '20' },\r\n      ];\r\n\r\n      vulnerabilityRepository.createQueryBuilder().getRawMany.mockResolvedValue(mockSeverityCounts);\r\n      vulnerabilityRepository.count\r\n        .mockResolvedValueOnce(3) // exploitable\r\n        .mockResolvedValueOnce(2) // with exploits\r\n        .mockResolvedValueOnce(1) // in the wild\r\n        .mockResolvedValueOnce(8) // recent\r\n        .mockResolvedValueOnce(25); // patched\r\n\r\n      vulnerabilityRepository.find.mockResolvedValue([mockVulnerability]);\r\n\r\n      const result = await service.getVulnerabilityDashboard();\r\n\r\n      expect(result).toHaveProperty('summary');\r\n      expect(result).toHaveProperty('breakdown');\r\n      expect(result).toHaveProperty('timestamp');\r\n      expect(result.summary.total).toBe(50);\r\n      expect(result.summary.critical).toBe(5);\r\n      expect(result.summary.exploitable).toBe(3);\r\n    });\r\n  });\r\n\r\n  describe('searchVulnerabilities', () => {\r\n    it('should search vulnerabilities with filters', async () => {\r\n      const mockQueryBuilder = vulnerabilityRepository.createQueryBuilder();\r\n      mockQueryBuilder.getManyAndCount.mockResolvedValue([[mockVulnerability], 1]);\r\n\r\n      const searchCriteria = {\r\n        page: 1,\r\n        limit: 10,\r\n        severities: ['high', 'critical'],\r\n        exploitable: true,\r\n        searchText: 'test',\r\n      };\r\n\r\n      const result = await service.searchVulnerabilities(searchCriteria);\r\n\r\n      expect(result).toHaveProperty('vulnerabilities');\r\n      expect(result).toHaveProperty('total');\r\n      expect(result).toHaveProperty('page');\r\n      expect(result).toHaveProperty('totalPages');\r\n      expect(result.vulnerabilities).toHaveLength(1);\r\n      expect(result.total).toBe(1);\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        'vuln.severity IN (:...severities)',\r\n        { severities: ['high', 'critical'] }\r\n      );\r\n    });\r\n\r\n    it('should handle pagination correctly', async () => {\r\n      const mockQueryBuilder = vulnerabilityRepository.createQueryBuilder();\r\n      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);\r\n\r\n      const searchCriteria = {\r\n        page: 2,\r\n        limit: 25,\r\n      };\r\n\r\n      await service.searchVulnerabilities(searchCriteria);\r\n\r\n      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(25);\r\n      expect(mockQueryBuilder.take).toHaveBeenCalledWith(25);\r\n    });\r\n  });\r\n\r\n  describe('getVulnerabilityDetails', () => {\r\n    it('should return vulnerability details', async () => {\r\n      vulnerabilityRepository.findOne.mockResolvedValue(mockVulnerability);\r\n\r\n      const result = await service.getVulnerabilityDetails('123e4567-e89b-12d3-a456-426614174000');\r\n\r\n      expect(result).toEqual(mockVulnerability);\r\n      expect(vulnerabilityRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: '123e4567-e89b-12d3-a456-426614174000' },\r\n        relations: [\r\n          'assessments',\r\n          'exceptions',\r\n          'affectedAssets',\r\n          'affectedAssets.group',\r\n        ],\r\n      });\r\n    });\r\n\r\n    it('should throw NotFoundException when vulnerability not found', async () => {\r\n      vulnerabilityRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.getVulnerabilityDetails('non-existent-id')\r\n      ).rejects.toThrow('Vulnerability not found');\r\n    });\r\n  });\r\n\r\n  describe('createVulnerability', () => {\r\n    it('should create a new vulnerability', async () => {\r\n      const vulnerabilityData = {\r\n        identifier: 'CVE-2023-5678',\r\n        title: 'New Vulnerability',\r\n        description: 'New vulnerability description',\r\n        severity: 'medium',\r\n        affectedProducts: [\r\n          {\r\n            vendor: 'New Vendor',\r\n            product: 'New Product',\r\n            version: '2.0.0',\r\n          },\r\n        ],\r\n        dataSource: {\r\n          name: 'Manual Entry',\r\n          type: 'internal',\r\n          confidence: 'high',\r\n          lastUpdated: '2023-01-01T00:00:00Z',\r\n        },\r\n      };\r\n\r\n      vulnerabilityRepository.findOne.mockResolvedValue(null); // No existing vulnerability\r\n      vulnerabilityRepository.create.mockReturnValue(mockVulnerability);\r\n      vulnerabilityRepository.save.mockResolvedValue(mockVulnerability);\r\n\r\n      const result = await service.createVulnerability(vulnerabilityData, 'user-123');\r\n\r\n      expect(vulnerabilityRepository.create).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          ...vulnerabilityData,\r\n          publishedDate: expect.any(Date),\r\n          lastModifiedDate: expect.any(Date),\r\n        })\r\n      );\r\n      expect(vulnerabilityRepository.save).toHaveBeenCalledWith(mockVulnerability);\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'create',\r\n        'vulnerability',\r\n        mockVulnerability.id,\r\n        expect.any(Object)\r\n      );\r\n    });\r\n\r\n    it('should throw error when vulnerability already exists', async () => {\r\n      const vulnerabilityData = {\r\n        identifier: 'CVE-2023-1234',\r\n        title: 'Existing Vulnerability',\r\n        description: 'Description',\r\n        severity: 'high',\r\n        affectedProducts: [],\r\n        dataSource: {\r\n          name: 'Test',\r\n          type: 'internal',\r\n          confidence: 'high',\r\n          lastUpdated: '2023-01-01T00:00:00Z',\r\n        },\r\n      };\r\n\r\n      vulnerabilityRepository.findOne.mockResolvedValue(mockVulnerability);\r\n\r\n      await expect(\r\n        service.createVulnerability(vulnerabilityData, 'user-123')\r\n      ).rejects.toThrow('Vulnerability with this identifier already exists');\r\n    });\r\n  });\r\n\r\n  describe('updateVulnerability', () => {\r\n    it('should update an existing vulnerability', async () => {\r\n      const updates = {\r\n        severity: 'critical',\r\n        cvssScore: 9.0,\r\n      };\r\n\r\n      vulnerabilityRepository.findOne.mockResolvedValue(mockVulnerability);\r\n      vulnerabilityRepository.save.mockResolvedValue({\r\n        ...mockVulnerability,\r\n        ...updates,\r\n      });\r\n\r\n      const result = await service.updateVulnerability('123e4567-e89b-12d3-a456-426614174000', updates, 'user-123');\r\n\r\n      expect(vulnerabilityRepository.save).toHaveBeenCalled();\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'update',\r\n        'vulnerability',\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        expect.objectContaining({\r\n          identifier: mockVulnerability.identifier,\r\n          changes: expect.any(Object),\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should throw NotFoundException when vulnerability not found', async () => {\r\n      vulnerabilityRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.updateVulnerability('non-existent-id', {}, 'user-123')\r\n      ).rejects.toThrow('Vulnerability not found');\r\n    });\r\n  });\r\n\r\n  describe('deleteVulnerability', () => {\r\n    it('should delete an existing vulnerability', async () => {\r\n      vulnerabilityRepository.findOne.mockResolvedValue(mockVulnerability);\r\n      vulnerabilityRepository.remove.mockResolvedValue(mockVulnerability);\r\n\r\n      await service.deleteVulnerability('123e4567-e89b-12d3-a456-426614174000', 'user-123');\r\n\r\n      expect(vulnerabilityRepository.remove).toHaveBeenCalledWith(mockVulnerability);\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'delete',\r\n        'vulnerability',\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        expect.objectContaining({\r\n          identifier: mockVulnerability.identifier,\r\n          severity: mockVulnerability.severity,\r\n          title: mockVulnerability.title,\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should throw NotFoundException when vulnerability not found', async () => {\r\n      vulnerabilityRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.deleteVulnerability('non-existent-id', 'user-123')\r\n      ).rejects.toThrow('Vulnerability not found');\r\n    });\r\n  });\r\n\r\n  describe('monitorVulnerabilities', () => {\r\n    it('should monitor vulnerabilities and send alerts for new critical ones', async () => {\r\n      const mockCriticalVulns = [\r\n        { ...mockVulnerability, severity: 'critical' },\r\n      ];\r\n\r\n      vulnerabilityRepository.find.mockResolvedValue(mockCriticalVulns);\r\n\r\n      await service.monitorVulnerabilities();\r\n\r\n      expect(notificationService.sendNewCriticalVulnerabilitiesAlert).toHaveBeenCalledWith(mockCriticalVulns);\r\n    });\r\n\r\n    it('should handle monitoring errors gracefully', async () => {\r\n      vulnerabilityRepository.find.mockRejectedValue(new Error('Database error'));\r\n\r\n      await expect(service.monitorVulnerabilities()).resolves.not.toThrow();\r\n      expect(loggerService.error).toHaveBeenCalledWith(\r\n        'Failed to complete vulnerability monitoring',\r\n        expect.objectContaining({\r\n          error: 'Database error',\r\n        })\r\n      );\r\n    });\r\n  });\r\n});\r\n"], "version": 3}