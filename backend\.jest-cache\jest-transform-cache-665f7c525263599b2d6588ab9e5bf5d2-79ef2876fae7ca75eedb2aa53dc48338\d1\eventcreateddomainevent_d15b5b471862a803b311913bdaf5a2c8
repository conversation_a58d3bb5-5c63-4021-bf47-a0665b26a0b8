5bb7f5c16c654f2332dca0c71d7ba3bc
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventCreatedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const event_severity_enum_1 = require("../enums/event-severity.enum");
/**
 * Event Created Domain Event
 *
 * Raised when a new security event is created in the system.
 * This event triggers various downstream processes including:
 * - Event processing pipeline initiation
 * - Notification systems
 * - Metrics collection
 * - Audit logging
 */
class EventCreatedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the type of the created event
     */
    get eventType() {
        return this.eventData.eventType;
    }
    /**
     * Get the severity of the created event
     */
    get severity() {
        return this.eventData.severity;
    }
    /**
     * Get the source of the created event
     */
    get source() {
        return this.eventData.source;
    }
    /**
     * Get the timestamp of the created event
     */
    get timestamp() {
        return this.eventData.timestamp;
    }
    /**
     * Get the title of the created event
     */
    get title() {
        return this.eventData.title;
    }
    /**
     * Get the risk score of the created event
     */
    get riskScore() {
        return this.eventData.riskScore;
    }
    /**
     * Check if the created event is high severity
     */
    isHighSeverity() {
        return [event_severity_enum_1.EventSeverity.HIGH, event_severity_enum_1.EventSeverity.CRITICAL].includes(this.severity);
    }
    /**
     * Check if the created event is critical
     */
    isCritical() {
        return this.severity === event_severity_enum_1.EventSeverity.CRITICAL;
    }
    /**
     * Check if the created event has high risk score
     */
    isHighRisk() {
        return (this.riskScore || 0) >= 70;
    }
    /**
     * Get event summary for handlers
     */
    getEventSummary() {
        return {
            eventId: this.aggregateId.toString(),
            eventType: this.eventType,
            severity: this.severity,
            sourceType: this.source.type,
            sourceIdentifier: this.source.identifier,
            title: this.title,
            riskScore: this.riskScore,
            isHighSeverity: this.isHighSeverity(),
            isCritical: this.isCritical(),
            isHighRisk: this.isHighRisk(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventSummary: this.getEventSummary(),
        };
    }
}
exports.EventCreatedDomainEvent = EventCreatedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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