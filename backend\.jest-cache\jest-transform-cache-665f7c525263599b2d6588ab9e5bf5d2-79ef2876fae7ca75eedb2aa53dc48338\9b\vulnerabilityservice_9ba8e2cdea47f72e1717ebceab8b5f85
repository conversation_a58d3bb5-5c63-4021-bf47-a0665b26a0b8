c37cf16f08a6b1b62f57b077e57597e3
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var VulnerabilityService_1;
var _a, _b, _c, _d, _e, _f, _g, _h;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_1 = require("@nestjs/schedule");
const vulnerability_entity_1 = require("../../domain/entities/vulnerability.entity");
const vulnerability_assessment_entity_1 = require("../../domain/entities/vulnerability-assessment.entity");
const vulnerability_exception_entity_1 = require("../../domain/entities/vulnerability-exception.entity");
const asset_entity_1 = require("../../../asset-management/domain/entities/asset.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../infrastructure/notification/notification.service");
/**
 * Vulnerability service
 * Handles core vulnerability management operations
 */
let VulnerabilityService = VulnerabilityService_1 = class VulnerabilityService {
    constructor(vulnerabilityRepository, assessmentRepository, exceptionRepository, assetRepository, loggerService, auditService, notificationService) {
        this.vulnerabilityRepository = vulnerabilityRepository;
        this.assessmentRepository = assessmentRepository;
        this.exceptionRepository = exceptionRepository;
        this.assetRepository = assetRepository;
        this.loggerService = loggerService;
        this.auditService = auditService;
        this.notificationService = notificationService;
        this.logger = new common_1.Logger(VulnerabilityService_1.name);
    }
    /**
     * Get vulnerability dashboard data
     */
    async getVulnerabilityDashboard() {
        try {
            this.logger.debug('Generating vulnerability dashboard');
            // Get vulnerability counts by severity
            const severityCounts = await this.vulnerabilityRepository
                .createQueryBuilder('vuln')
                .select('vuln.severity', 'severity')
                .addSelect('COUNT(*)', 'count')
                .groupBy('vuln.severity')
                .getRawMany();
            // Get exploitable vulnerabilities
            const exploitableCount = await this.vulnerabilityRepository.count({
                where: { exploitable: true },
            });
            // Get vulnerabilities with exploits
            const withExploitsCount = await this.vulnerabilityRepository.count({
                where: { hasExploit: true },
            });
            // Get vulnerabilities in the wild
            const inTheWildCount = await this.vulnerabilityRepository.count({
                where: { inTheWild: true },
            });
            // Get recent vulnerabilities (last 30 days)
            const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            const recentCount = await this.vulnerabilityRepository.count({
                where: { publishedDate: (0, typeorm_2.Between)(thirtyDaysAgo, new Date()) },
            });
            // Get vulnerabilities with patches
            const patchedCount = await this.vulnerabilityRepository.count({
                where: { patchAvailable: true },
            });
            // Get top affected products
            const topProducts = await this.vulnerabilityRepository
                .createQueryBuilder('vuln')
                .select("jsonb_array_elements(vuln.affected_products)->>'vendor' || ' ' || jsonb_array_elements(vuln.affected_products)->>'product'", 'product')
                .addSelect('COUNT(*)', 'count')
                .groupBy('product')
                .orderBy('count', 'DESC')
                .limit(10)
                .getRawMany();
            // Get vulnerability trends (last 12 months)
            const trends = await this.getVulnerabilityTrends();
            // Get risk distribution
            const riskDistribution = await this.getRiskDistribution();
            // Get recent high-risk vulnerabilities
            const recentHighRisk = await this.vulnerabilityRepository.find({
                where: [
                    { severity: 'critical' },
                    { severity: 'high', exploitable: true },
                ],
                order: { publishedDate: 'DESC' },
                take: 10,
            });
            const summary = {
                total: severityCounts.reduce((sum, item) => sum + parseInt(item.count), 0),
                critical: severityCounts.find(s => s.severity === 'critical')?.count || 0,
                high: severityCounts.find(s => s.severity === 'high')?.count || 0,
                medium: severityCounts.find(s => s.severity === 'medium')?.count || 0,
                low: severityCounts.find(s => s.severity === 'low')?.count || 0,
                info: severityCounts.find(s => s.severity === 'info')?.count || 0,
                exploitable: exploitableCount,
                withExploits: withExploitsCount,
                inTheWild: inTheWildCount,
                recent: recentCount,
                patched: patchedCount,
                patchRate: severityCounts.length > 0 ? (patchedCount / severityCounts.reduce((sum, item) => sum + parseInt(item.count), 0)) * 100 : 0,
            };
            this.logger.log('Vulnerability dashboard generated successfully', {
                totalVulnerabilities: summary.total,
                criticalCount: summary.critical,
                exploitableCount: summary.exploitable,
            });
            return {
                summary,
                breakdown: {
                    bySeverity: severityCounts.reduce((acc, item) => {
                        acc[item.severity] = parseInt(item.count);
                        return acc;
                    }, {}),
                    byExploitability: {
                        exploitable: exploitableCount,
                        hasExploit: withExploitsCount,
                        inTheWild: inTheWildCount,
                    },
                    byPatchStatus: {
                        patched: patchedCount,
                        unpatched: summary.total - patchedCount,
                    },
                },
                topProducts,
                trends,
                riskDistribution,
                recentHighRisk: recentHighRisk.map(v => v.getSummary()),
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error('Failed to generate vulnerability dashboard', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Search vulnerabilities with advanced filtering
     */
    async searchVulnerabilities(criteria) {
        try {
            const page = criteria.page || 1;
            const limit = Math.min(criteria.limit || 50, 1000);
            const offset = (page - 1) * limit;
            const queryBuilder = this.vulnerabilityRepository
                .createQueryBuilder('vuln')
                .leftJoinAndSelect('vuln.assessments', 'assessments')
                .leftJoinAndSelect('vuln.exceptions', 'exceptions')
                .leftJoinAndSelect('vuln.affectedAssets', 'assets');
            // Apply filters
            if (criteria.severities?.length) {
                queryBuilder.andWhere('vuln.severity IN (:...severities)', { severities: criteria.severities });
            }
            if (criteria.exploitable !== undefined) {
                queryBuilder.andWhere('vuln.exploitable = :exploitable', { exploitable: criteria.exploitable });
            }
            if (criteria.hasExploit !== undefined) {
                queryBuilder.andWhere('vuln.hasExploit = :hasExploit', { hasExploit: criteria.hasExploit });
            }
            if (criteria.inTheWild !== undefined) {
                queryBuilder.andWhere('vuln.inTheWild = :inTheWild', { inTheWild: criteria.inTheWild });
            }
            if (criteria.patchAvailable !== undefined) {
                queryBuilder.andWhere('vuln.patchAvailable = :patchAvailable', { patchAvailable: criteria.patchAvailable });
            }
            if (criteria.publishedAfter) {
                queryBuilder.andWhere('vuln.publishedDate >= :publishedAfter', { publishedAfter: criteria.publishedAfter });
            }
            if (criteria.publishedBefore) {
                queryBuilder.andWhere('vuln.publishedDate <= :publishedBefore', { publishedBefore: criteria.publishedBefore });
            }
            if (criteria.cvssScoreMin !== undefined) {
                queryBuilder.andWhere('vuln.cvssScore >= :cvssScoreMin', { cvssScoreMin: criteria.cvssScoreMin });
            }
            if (criteria.cvssScoreMax !== undefined) {
                queryBuilder.andWhere('vuln.cvssScore <= :cvssScoreMax', { cvssScoreMax: criteria.cvssScoreMax });
            }
            if (criteria.tags?.length) {
                queryBuilder.andWhere('vuln.tags && :tags', { tags: criteria.tags });
            }
            if (criteria.searchText) {
                queryBuilder.andWhere('(vuln.identifier ILIKE :searchText OR vuln.title ILIKE :searchText OR vuln.description ILIKE :searchText)', { searchText: `%${criteria.searchText}%` });
            }
            if (criteria.affectedProducts?.length) {
                const productConditions = criteria.affectedProducts.map((product, index) => `vuln.affected_products @> '[{"vendor": "${product.split(' ')[0]}", "product": "${product.split(' ')[1]}"}]'`).join(' OR ');
                queryBuilder.andWhere(`(${productConditions})`);
            }
            // Apply sorting
            const sortBy = criteria.sortBy || 'publishedDate';
            const sortOrder = criteria.sortOrder || 'DESC';
            queryBuilder.orderBy(`vuln.${sortBy}`, sortOrder);
            // Apply pagination
            queryBuilder.skip(offset).take(limit);
            const [vulnerabilities, total] = await queryBuilder.getManyAndCount();
            this.logger.debug('Vulnerability search completed', {
                total,
                page,
                limit,
                criteriaCount: Object.keys(criteria).length,
            });
            return {
                vulnerabilities,
                total,
                page,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            this.logger.error('Failed to search vulnerabilities', {
                error: error.message,
                criteria,
            });
            throw error;
        }
    }
    /**
     * Get vulnerability details
     */
    async getVulnerabilityDetails(id) {
        try {
            const vulnerability = await this.vulnerabilityRepository.findOne({
                where: { id },
                relations: [
                    'assessments',
                    'exceptions',
                    'affectedAssets',
                    'affectedAssets.group',
                ],
            });
            if (!vulnerability) {
                throw new common_1.NotFoundException('Vulnerability not found');
            }
            this.logger.debug('Vulnerability details retrieved', {
                vulnerabilityId: id,
                identifier: vulnerability.identifier,
                severity: vulnerability.severity,
            });
            return vulnerability;
        }
        catch (error) {
            this.logger.error('Failed to get vulnerability details', {
                vulnerabilityId: id,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Create vulnerability
     */
    async createVulnerability(vulnerabilityData, userId) {
        try {
            this.logger.debug('Creating vulnerability', {
                identifier: vulnerabilityData.identifier,
                severity: vulnerabilityData.severity,
                userId,
            });
            // Check if vulnerability already exists
            const existing = await this.vulnerabilityRepository.findOne({
                where: { identifier: vulnerabilityData.identifier },
            });
            if (existing) {
                throw new Error('Vulnerability with this identifier already exists');
            }
            const vulnerability = this.vulnerabilityRepository.create({
                ...vulnerabilityData,
                publishedDate: vulnerabilityData.publishedDate || new Date(),
                lastModifiedDate: vulnerabilityData.lastModifiedDate || new Date(),
                dataSource: vulnerabilityData.dataSource || {
                    name: 'Manual Entry',
                    type: 'internal',
                    lastUpdated: new Date().toISOString(),
                    confidence: 'high',
                },
            });
            const savedVulnerability = await this.vulnerabilityRepository.save(vulnerability);
            await this.auditService.logUserAction(userId, 'create', 'vulnerability', savedVulnerability.id, {
                identifier: vulnerabilityData.identifier,
                severity: vulnerabilityData.severity,
                title: vulnerabilityData.title,
            });
            this.logger.log('Vulnerability created successfully', {
                vulnerabilityId: savedVulnerability.id,
                identifier: vulnerabilityData.identifier,
                userId,
            });
            return savedVulnerability;
        }
        catch (error) {
            this.logger.error('Failed to create vulnerability', {
                error: error.message,
                vulnerabilityData,
                userId,
            });
            throw error;
        }
    }
    /**
     * Update vulnerability
     */
    async updateVulnerability(id, updates, userId) {
        try {
            const vulnerability = await this.vulnerabilityRepository.findOne({
                where: { id },
            });
            if (!vulnerability) {
                throw new common_1.NotFoundException('Vulnerability not found');
            }
            this.logger.debug('Updating vulnerability', {
                vulnerabilityId: id,
                identifier: vulnerability.identifier,
                userId,
            });
            // Track changes for audit
            const changes = {};
            Object.keys(updates).forEach(key => {
                if (vulnerability[key] !== updates[key]) {
                    changes[key] = {
                        from: vulnerability[key],
                        to: updates[key],
                    };
                }
            });
            Object.assign(vulnerability, updates);
            vulnerability.lastModifiedDate = new Date();
            const savedVulnerability = await this.vulnerabilityRepository.save(vulnerability);
            await this.auditService.logUserAction(userId, 'update', 'vulnerability', id, {
                identifier: vulnerability.identifier,
                changes,
            });
            this.logger.log('Vulnerability updated successfully', {
                vulnerabilityId: id,
                identifier: vulnerability.identifier,
                changesCount: Object.keys(changes).length,
                userId,
            });
            return savedVulnerability;
        }
        catch (error) {
            this.logger.error('Failed to update vulnerability', {
                vulnerabilityId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Delete vulnerability
     */
    async deleteVulnerability(id, userId) {
        try {
            const vulnerability = await this.vulnerabilityRepository.findOne({
                where: { id },
            });
            if (!vulnerability) {
                throw new common_1.NotFoundException('Vulnerability not found');
            }
            this.logger.debug('Deleting vulnerability', {
                vulnerabilityId: id,
                identifier: vulnerability.identifier,
                userId,
            });
            await this.vulnerabilityRepository.remove(vulnerability);
            await this.auditService.logUserAction(userId, 'delete', 'vulnerability', id, {
                identifier: vulnerability.identifier,
                severity: vulnerability.severity,
                title: vulnerability.title,
            });
            this.logger.log('Vulnerability deleted successfully', {
                vulnerabilityId: id,
                identifier: vulnerability.identifier,
                userId,
            });
        }
        catch (error) {
            this.logger.error('Failed to delete vulnerability', {
                vulnerabilityId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Monitor vulnerabilities for critical updates
     */
    async monitorVulnerabilities() {
        try {
            this.logger.debug('Starting vulnerability monitoring');
            // Check for new critical vulnerabilities
            await this.checkNewCriticalVulnerabilities();
            // Check for exploit updates
            await this.checkExploitUpdates();
            // Check for patch updates
            await this.checkPatchUpdates();
            // Check for vulnerabilities in the wild
            await this.checkInTheWildUpdates();
            this.logger.log('Vulnerability monitoring completed');
        }
        catch (error) {
            this.logger.error('Failed to complete vulnerability monitoring', {
                error: error.message,
            });
        }
    }
    // Private helper methods
    async getVulnerabilityTrends() {
        const trends = [];
        const now = new Date();
        for (let i = 11; i >= 0; i--) {
            const startDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
            const endDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
            const count = await this.vulnerabilityRepository.count({
                where: { publishedDate: (0, typeorm_2.Between)(startDate, endDate) },
            });
            trends.push({
                month: startDate.toISOString().substring(0, 7),
                count,
            });
        }
        return trends;
    }
    async getRiskDistribution() {
        const distribution = await this.vulnerabilityRepository
            .createQueryBuilder('vuln')
            .select('CASE WHEN vuln.cvss_score >= 9 THEN \'critical\' WHEN vuln.cvss_score >= 7 THEN \'high\' WHEN vuln.cvss_score >= 4 THEN \'medium\' ELSE \'low\' END', 'risk')
            .addSelect('COUNT(*)', 'count')
            .where('vuln.cvss_score IS NOT NULL')
            .groupBy('risk')
            .getRawMany();
        return distribution.reduce((acc, item) => {
            acc[item.risk] = parseInt(item.count);
            return acc;
        }, {});
    }
    async checkNewCriticalVulnerabilities() {
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const newCritical = await this.vulnerabilityRepository.find({
            where: {
                severity: 'critical',
                publishedDate: (0, typeorm_2.Between)(oneDayAgo, new Date()),
            },
        });
        if (newCritical.length > 0) {
            await this.notificationService.sendNewCriticalVulnerabilitiesAlert(newCritical);
        }
    }
    async checkExploitUpdates() {
        // Implementation would check for exploit updates from threat intelligence feeds
        this.logger.debug('Checking for exploit updates');
    }
    async checkPatchUpdates() {
        // Implementation would check for new patches from vendor feeds
        this.logger.debug('Checking for patch updates');
    }
    async checkInTheWildUpdates() {
        // Implementation would check threat intelligence for in-the-wild exploitation
        this.logger.debug('Checking for in-the-wild updates');
    }
    /**
     * Get vulnerability statistics
     * @returns Vulnerability statistics
     */
    async getStatistics() {
        try {
            const [total, critical, high, medium, low, open, inProgress, resolved,] = await Promise.all([
                this.vulnerabilityRepository.count(),
                this.vulnerabilityRepository.count({ where: { severity: 'critical' } }),
                this.vulnerabilityRepository.count({ where: { severity: 'high' } }),
                this.vulnerabilityRepository.count({ where: { severity: 'medium' } }),
                this.vulnerabilityRepository.count({ where: { severity: 'low' } }),
                this.vulnerabilityRepository.count({ where: { status: 'open' } }),
                this.vulnerabilityRepository.count({ where: { status: 'in_progress' } }),
                this.vulnerabilityRepository.count({ where: { status: 'resolved' } }),
            ]);
            return {
                total,
                bySeverity: {
                    critical,
                    high,
                    medium,
                    low,
                },
                byStatus: {
                    open,
                    inProgress,
                    resolved,
                },
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error('Failed to get vulnerability statistics', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Calculate risk score for a vulnerability
     * @param vulnerability Vulnerability data
     * @returns Calculated risk score
     */
    calculateRiskScore(vulnerability) {
        let score = 0;
        // Base score from CVSS
        if (vulnerability.cvssScore) {
            score = vulnerability.cvssScore;
        }
        else {
            // Fallback to severity-based scoring
            const severityScores = {
                critical: 9.0,
                high: 7.0,
                medium: 5.0,
                low: 3.0,
                info: 1.0,
            };
            score = severityScores[vulnerability.severity] || 5.0;
        }
        // Adjust for exploitability
        if (vulnerability.isExploitable) {
            score += 1.0;
        }
        if (vulnerability.hasPublicExploit) {
            score += 1.5;
        }
        if (vulnerability.isActivelyExploited) {
            score += 2.0;
        }
        // Ensure score is within valid range
        return Math.min(10.0, Math.max(0.0, score));
    }
    /**
     * Sanitize vulnerability data for logging
     * @param data Vulnerability data
     * @returns Sanitized data
     */
    sanitizeVulnerabilityData(data) {
        const sanitized = { ...data };
        // Remove sensitive fields if any
        delete sanitized.metadata;
        return sanitized;
    }
};
exports.VulnerabilityService = VulnerabilityService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_HOUR),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], VulnerabilityService.prototype, "monitorVulnerabilities", null);
exports.VulnerabilityService = VulnerabilityService = VulnerabilityService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(vulnerability_entity_1.Vulnerability)),
    __param(1, (0, typeorm_1.InjectRepository)(vulnerability_assessment_entity_1.VulnerabilityAssessment)),
    __param(2, (0, typeorm_1.InjectRepository)(vulnerability_exception_entity_1.VulnerabilityException)),
    __param(3, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _d : Object, typeof (_e = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _e : Object, typeof (_f = typeof audit_service_1.AuditService !== "undefined" && audit_service_1.AuditService) === "function" ? _f : Object, typeof (_g = typeof notification_service_1.NotificationService !== "undefined" && notification_service_1.NotificationService) === "function" ? _g : Object])
], VulnerabilityService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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