{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\domain\\entities\\policy-definition.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAUiB;AACjB,+EAAoE;AACpE,uEAA4D;AAE5D;;;GAGG;AAOI,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAoT3B;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,CAAC,IAAI,CAAC,cAAc;YAAE,OAAO,KAAK,CAAC;QACvC,OAAO,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC;QACrD,IAAI,CAAC,cAAc;YAAE,OAAO,KAAK,CAAC;QAClC,OAAO,IAAI,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,IAAI,IAAI,CAAC,gBAAgB,KAAK,UAAU,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,MAAc;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,oBAAoB,GAAG,aAAa,CAAC;QACnD,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,MAAc,EAAE,MAAe;QACxC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,MAAM,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG,MAAM,CAAC;QACrC,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAkB,EAAE,MAAc,EAAE,OAAiB;QACjE,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;YACjC,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG,EAAE,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC;YAC/B,OAAO,EAAE,UAAU;YACnB,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YAC9B,MAAM,EAAE,MAAM;YACd,OAAO;YACP,MAAM,EAAE,uBAAuB,UAAU,OAAO,UAAU,EAAE;SAC7D,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAAS;QACf,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC;YAC5B,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;YACxB,GAAG,IAAI;SACR,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,MAAc;QACvB,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;QACzF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,MAAc,EAAE,OAAY;QACrC,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;YACjF,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC;gBACnB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG;oBACpC,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC;oBACtC,GAAG,OAAO;iBACX,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,MAAc;QACxB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvE,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACpD,CAAC;aAAM,CAAC;YACN,4BAA4B;YAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACxD,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;YACvC,IAAI,OAAO,CAAC,MAAM,KAAK,aAAa,CAAC,IAAI,EAAE,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACzC,CAAC;YAED,6BAA6B;YAC7B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gBAC/C,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;oBACzD,MAAM,CAAC,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC,6BAA6B,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;QAC5E,CAAC;QAED,wBAAwB;QACxB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrE,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QAClD,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;QAEtD,kCAAkC;QAClC,MAAM,cAAc,GAAG,UAAU,GAAG,CAAC,CAAC;QACtC,MAAM,mBAAmB,GAAG,cAAc,GAAG,CAAC,CAAC;QAC/C,MAAM,mBAAmB,GAAG,gBAAgB,GAAG,CAAC,CAAC;QACjD,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,cAAc,GAAG,mBAAmB,GAAG,mBAAmB,CAAC,GAAG,kBAAkB,CAAC,CAAC;IACxG,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,UAAU,IAAI,EAAE,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,QAAgB;QAC9B,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,iBAAiB,CAAC;QACtE,IAAI,CAAC,iBAAiB;YAAE,OAAO,IAAI,CAAC,CAAC,uCAAuC;QAC5E,OAAO,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,IAAY;QACxB,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,eAAe,CAAC;QAClE,IAAI,CAAC,eAAe;YAAE,OAAO,IAAI,CAAC,CAAC,uCAAuC;QAC1E,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;YAC1C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM;YAC9C,eAAe,EAAE,IAAI,CAAC,wBAAwB,EAAE;YAChD,oBAAoB,EAAE,IAAI,CAAC,uBAAuB,EAAE;YACpD,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACpC,UAAU,EAAE,mBAAmB;SAChC,CAAC;IACJ,CAAC;CACF,CAAA;AA9kBY,4CAAgB;AAE3B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;4CACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;8CACX;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;qDACL;AA8BpB;IAzBC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,gBAAgB;YAChB,iBAAiB;YACjB,gBAAgB;YAChB,eAAe;YACf,YAAY;YACZ,eAAe;YACf,iBAAiB;YACjB,mBAAmB;YACnB,mBAAmB;YACnB,0BAA0B;YAC1B,kBAAkB;YAClB,mBAAmB;YACnB,oBAAoB;YACpB,qBAAqB;YACrB,iBAAiB;YACjB,uBAAuB;YACvB,4BAA4B;YAC5B,kBAAkB;YAClB,mBAAmB;YACnB,oBAAoB;SACrB;KACF,CAAC;;8CACW;AAUb;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;QAC3C,OAAO,EAAE,QAAQ;KAClB,CAAC;;kDAC+C;AAWjD;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,mBAAmB;QACzB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC;QACrD,OAAO,EAAE,SAAS;KACnB,CAAC;;0DACiE;AAMnE;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;kDAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;iDACX;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;uDAAC;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;wDAAC;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;uDA+GxB;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDA6DxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;8CACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;mDAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;mDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;mDAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,iDAAmB,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzF,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;kDACzB,iDAAmB,oBAAnB,iDAAmB;mDAAC;AAGhC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAC1C;AAGrB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,yCAAe,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;;oDAClC;2BAlTnB,gBAAgB;IAN5B,IAAA,gBAAM,EAAC,oBAAoB,CAAC;IAC5B,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,kBAAkB,CAAC,CAAC;GACf,gBAAgB,CA8kB5B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\domain\\entities\\policy-definition.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n  OneToMany,\r\n} from 'typeorm';\r\nimport { ComplianceFramework } from './compliance-framework.entity';\r\nimport { PolicyViolation } from './policy-violation.entity';\r\n\r\n/**\r\n * Policy Definition entity\r\n * Represents organizational policies and their enforcement rules\r\n */\r\n@Entity('policy_definitions')\r\n@Index(['frameworkId'])\r\n@Index(['type'])\r\n@Index(['isActive'])\r\n@Index(['severity'])\r\n@Index(['enforcementLevel'])\r\nexport class PolicyDefinition {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Policy name\r\n   */\r\n  @Column({ length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Policy description\r\n   */\r\n  @Column({ type: 'text' })\r\n  description: string;\r\n\r\n  /**\r\n   * Policy type/category\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'access_control',\r\n      'data_protection',\r\n      'authentication',\r\n      'authorization',\r\n      'encryption',\r\n      'audit_logging',\r\n      'backup_recovery',\r\n      'incident_response',\r\n      'change_management',\r\n      'vulnerability_management',\r\n      'network_security',\r\n      'physical_security',\r\n      'personnel_security',\r\n      'business_continuity',\r\n      'risk_management',\r\n      'compliance_monitoring',\r\n      'information_classification',\r\n      'asset_management',\r\n      'vendor_management',\r\n      'training_awareness',\r\n    ],\r\n  })\r\n  type: string;\r\n\r\n  /**\r\n   * Policy severity/importance\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high', 'critical'],\r\n    default: 'medium',\r\n  })\r\n  severity: 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  /**\r\n   * Policy enforcement level\r\n   */\r\n  @Column({\r\n    name: 'enforcement_level',\r\n    type: 'enum',\r\n    enum: ['advisory', 'warning', 'blocking', 'critical'],\r\n    default: 'warning',\r\n  })\r\n  enforcementLevel: 'advisory' | 'warning' | 'blocking' | 'critical';\r\n\r\n  /**\r\n   * Whether policy is active\r\n   */\r\n  @Column({ name: 'is_active', default: true })\r\n  isActive: boolean;\r\n\r\n  /**\r\n   * Policy version\r\n   */\r\n  @Column({ default: '1.0' })\r\n  version: string;\r\n\r\n  /**\r\n   * Policy effective date\r\n   */\r\n  @Column({ name: 'effective_date', type: 'timestamp with time zone' })\r\n  effectiveDate: Date;\r\n\r\n  /**\r\n   * Policy expiration date\r\n   */\r\n  @Column({ name: 'expiration_date', type: 'timestamp with time zone', nullable: true })\r\n  expirationDate?: Date;\r\n\r\n  /**\r\n   * Policy configuration and rules\r\n   */\r\n  @Column({ type: 'jsonb' })\r\n  configuration: {\r\n    // Policy scope\r\n    scope?: {\r\n      description: string;\r\n      applicableRoles?: string[];\r\n      applicableSystems?: string[];\r\n      applicableProcesses?: string[];\r\n      applicableDataTypes?: string[];\r\n      exclusions?: string[];\r\n    };\r\n    \r\n    // Policy rules and requirements\r\n    rules: Array<{\r\n      id: string;\r\n      name: string;\r\n      description: string;\r\n      type: 'mandatory' | 'recommended' | 'conditional';\r\n      condition?: string;\r\n      requirement: string;\r\n      implementation: string;\r\n      verification: string;\r\n      frequency?: 'continuous' | 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';\r\n      automatable: boolean;\r\n      exceptions?: Array<{\r\n        condition: string;\r\n        justification: string;\r\n        approvalRequired: boolean;\r\n      }>;\r\n    }>;\r\n    \r\n    // Enforcement configuration\r\n    enforcement?: {\r\n      method: 'automated' | 'manual' | 'hybrid';\r\n      automationRules?: Array<{\r\n        trigger: string;\r\n        condition: string;\r\n        action: 'alert' | 'block' | 'log' | 'escalate';\r\n        parameters?: Record<string, any>;\r\n      }>;\r\n      \r\n      monitoringFrequency: 'real_time' | 'hourly' | 'daily' | 'weekly';\r\n      alerting?: {\r\n        enabled: boolean;\r\n        recipients: string[];\r\n        escalationRules?: Array<{\r\n          condition: string;\r\n          delay: number; // minutes\r\n          recipients: string[];\r\n        }>;\r\n      };\r\n      \r\n      violations?: {\r\n        autoDetection: boolean;\r\n        detectionRules: Array<{\r\n          name: string;\r\n          condition: string;\r\n          severity: 'low' | 'medium' | 'high' | 'critical';\r\n          action: 'log' | 'alert' | 'block' | 'escalate';\r\n        }>;\r\n      };\r\n    };\r\n    \r\n    // Compliance mappings\r\n    compliance?: {\r\n      frameworks: string[];\r\n      controls: string[];\r\n      requirements: string[];\r\n      evidenceRequirements?: string[];\r\n      auditFrequency?: 'monthly' | 'quarterly' | 'semi_annually' | 'annually';\r\n    };\r\n    \r\n    // Implementation guidance\r\n    implementation?: {\r\n      guidelines: string[];\r\n      procedures: string[];\r\n      tools?: string[];\r\n      training?: string[];\r\n      documentation?: string[];\r\n      bestPractices?: string[];\r\n      commonPitfalls?: string[];\r\n    };\r\n    \r\n    // Metrics and KPIs\r\n    metrics?: {\r\n      complianceTarget?: number; // percentage\r\n      kpis: Array<{\r\n        name: string;\r\n        description: string;\r\n        target: number;\r\n        unit: string;\r\n        frequency: string;\r\n      }>;\r\n      reportingRequirements?: string[];\r\n    };\r\n    \r\n    // Risk information\r\n    risk?: {\r\n      riskLevel: 'low' | 'medium' | 'high' | 'critical';\r\n      riskFactors: string[];\r\n      mitigationStrategies: string[];\r\n      residualRisk?: string;\r\n    };\r\n    \r\n    // Dependencies and relationships\r\n    dependencies?: {\r\n      prerequisitePolicies?: string[];\r\n      relatedPolicies?: string[];\r\n      conflictingPolicies?: string[];\r\n      supportingProcedures?: string[];\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Policy metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: {\r\n    // Authorship and approval\r\n    author?: string;\r\n    reviewer?: string;\r\n    approver?: string;\r\n    approvalDate?: string;\r\n    \r\n    // Document management\r\n    documentId?: string;\r\n    documentVersion?: string;\r\n    documentLocation?: string;\r\n    \r\n    // Change history\r\n    changeHistory?: Array<{\r\n      version: string;\r\n      date: string;\r\n      author: string;\r\n      changes: string[];\r\n      reason: string;\r\n    }>;\r\n    \r\n    // Review information\r\n    lastReviewDate?: string;\r\n    nextReviewDate?: string;\r\n    reviewFrequency?: 'quarterly' | 'semi_annually' | 'annually' | 'bi_annually';\r\n    reviewers?: string[];\r\n    \r\n    // Legal and regulatory context\r\n    legalBasis?: string[];\r\n    regulatoryRequirements?: string[];\r\n    industryStandards?: string[];\r\n    \r\n    // Business context\r\n    businessJustification?: string;\r\n    businessOwner?: string;\r\n    technicalOwner?: string;\r\n    stakeholders?: string[];\r\n    \r\n    // Implementation status\r\n    implementationStatus?: 'draft' | 'approved' | 'implemented' | 'under_review' | 'deprecated';\r\n    implementationDate?: string;\r\n    rolloutPlan?: string;\r\n    \r\n    // Training and awareness\r\n    trainingRequired?: boolean;\r\n    trainingMaterials?: string[];\r\n    awarenessProgram?: string;\r\n    \r\n    // Cost and effort\r\n    implementationCost?: number;\r\n    maintenanceCost?: number;\r\n    estimatedEffort?: string;\r\n    \r\n    // External references\r\n    externalReferences?: Array<{\r\n      type: string;\r\n      title: string;\r\n      url?: string;\r\n      description?: string;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Policy tags\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * User who created the policy\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  /**\r\n   * User who last updated the policy\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => ComplianceFramework, framework => framework.policies, { nullable: true })\r\n  @JoinColumn({ name: 'framework_id' })\r\n  framework?: ComplianceFramework;\r\n\r\n  @Column({ name: 'framework_id', type: 'uuid', nullable: true })\r\n  frameworkId?: string;\r\n\r\n  @OneToMany(() => PolicyViolation, violation => violation.policy)\r\n  violations: PolicyViolation[];\r\n\r\n  /**\r\n   * Check if policy is expired\r\n   */\r\n  get isExpired(): boolean {\r\n    if (!this.expirationDate) return false;\r\n    return new Date() > this.expirationDate;\r\n  }\r\n\r\n  /**\r\n   * Check if policy is due for review\r\n   */\r\n  get isDueForReview(): boolean {\r\n    const nextReviewDate = this.metadata?.nextReviewDate;\r\n    if (!nextReviewDate) return false;\r\n    return new Date() > new Date(nextReviewDate);\r\n  }\r\n\r\n  /**\r\n   * Check if policy is critical\r\n   */\r\n  get isCritical(): boolean {\r\n    return this.severity === 'critical' || this.enforcementLevel === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Get total number of rules\r\n   */\r\n  get totalRules(): number {\r\n    return this.configuration.rules.length;\r\n  }\r\n\r\n  /**\r\n   * Get mandatory rules\r\n   */\r\n  get mandatoryRules(): any[] {\r\n    return this.configuration.rules.filter(rule => rule.type === 'mandatory');\r\n  }\r\n\r\n  /**\r\n   * Get automatable rules\r\n   */\r\n  get automatableRules(): any[] {\r\n    return this.configuration.rules.filter(rule => rule.automatable);\r\n  }\r\n\r\n  /**\r\n   * Activate policy\r\n   */\r\n  activate(userId: string): void {\r\n    this.isActive = true;\r\n    this.updatedBy = userId;\r\n    \r\n    if (!this.metadata) {\r\n      this.metadata = {};\r\n    }\r\n    \r\n    this.metadata.implementationStatus = 'implemented';\r\n    this.metadata.implementationDate = new Date().toISOString();\r\n  }\r\n\r\n  /**\r\n   * Deactivate policy\r\n   */\r\n  deactivate(userId: string, reason?: string): void {\r\n    this.isActive = false;\r\n    this.updatedBy = userId;\r\n    \r\n    if (!this.metadata) {\r\n      this.metadata = {};\r\n    }\r\n    \r\n    this.metadata.deactivationReason = reason;\r\n    this.metadata.deactivatedBy = userId;\r\n    this.metadata.deactivatedAt = new Date().toISOString();\r\n  }\r\n\r\n  /**\r\n   * Update version\r\n   */\r\n  updateVersion(newVersion: string, userId: string, changes: string[]): void {\r\n    const oldVersion = this.version;\r\n    this.version = newVersion;\r\n    this.updatedBy = userId;\r\n    \r\n    if (!this.metadata) {\r\n      this.metadata = {};\r\n    }\r\n    \r\n    if (!this.metadata.changeHistory) {\r\n      this.metadata.changeHistory = [];\r\n    }\r\n    \r\n    this.metadata.changeHistory.push({\r\n      version: newVersion,\r\n      date: new Date().toISOString(),\r\n      author: userId,\r\n      changes,\r\n      reason: `Version update from ${oldVersion} to ${newVersion}`,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Add rule to policy\r\n   */\r\n  addRule(rule: any): void {\r\n    if (!this.configuration.rules) {\r\n      this.configuration.rules = [];\r\n    }\r\n    \r\n    this.configuration.rules.push({\r\n      id: `rule-${Date.now()}`,\r\n      ...rule,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Remove rule from policy\r\n   */\r\n  removeRule(ruleId: string): void {\r\n    if (this.configuration.rules) {\r\n      this.configuration.rules = this.configuration.rules.filter(rule => rule.id !== ruleId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update rule\r\n   */\r\n  updateRule(ruleId: string, updates: any): void {\r\n    if (this.configuration.rules) {\r\n      const ruleIndex = this.configuration.rules.findIndex(rule => rule.id === ruleId);\r\n      if (ruleIndex >= 0) {\r\n        this.configuration.rules[ruleIndex] = {\r\n          ...this.configuration.rules[ruleIndex],\r\n          ...updates,\r\n        };\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get rule by ID\r\n   */\r\n  getRuleById(ruleId: string): any | null {\r\n    return this.configuration.rules.find(rule => rule.id === ruleId) || null;\r\n  }\r\n\r\n  /**\r\n   * Validate policy configuration\r\n   */\r\n  validateConfiguration(): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    // Check rules\r\n    if (!this.configuration.rules || this.configuration.rules.length === 0) {\r\n      errors.push('Policy must have at least one rule');\r\n    } else {\r\n      // Check rule IDs are unique\r\n      const ruleIds = this.configuration.rules.map(r => r.id);\r\n      const uniqueRuleIds = new Set(ruleIds);\r\n      if (ruleIds.length !== uniqueRuleIds.size) {\r\n        errors.push('Rule IDs must be unique');\r\n      }\r\n\r\n      // Check required rule fields\r\n      this.configuration.rules.forEach((rule, index) => {\r\n        if (!rule.name || !rule.description || !rule.requirement) {\r\n          errors.push(`Rule ${index + 1} is missing required fields`);\r\n        }\r\n      });\r\n    }\r\n\r\n    // Check effective date\r\n    if (this.effectiveDate > new Date()) {\r\n      errors.push('Effective date cannot be in the future for active policies');\r\n    }\r\n\r\n    // Check expiration date\r\n    if (this.expirationDate && this.expirationDate <= this.effectiveDate) {\r\n      errors.push('Expiration date must be after effective date');\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate policy complexity score\r\n   */\r\n  calculateComplexityScore(): number {\r\n    const totalRules = this.totalRules;\r\n    const mandatoryRules = this.mandatoryRules.length;\r\n    const automatableRules = this.automatableRules.length;\r\n    \r\n    // Weighted complexity calculation\r\n    const ruleComplexity = totalRules * 2;\r\n    const mandatoryComplexity = mandatoryRules * 3;\r\n    const automationReduction = automatableRules * 1;\r\n    const severityMultiplier = this.severity === 'critical' ? 2 : 1;\r\n\r\n    return Math.max(1, (ruleComplexity + mandatoryComplexity - automationReduction) * severityMultiplier);\r\n  }\r\n\r\n  /**\r\n   * Get compliance frameworks\r\n   */\r\n  getComplianceFrameworks(): string[] {\r\n    return this.configuration.compliance?.frameworks || [];\r\n  }\r\n\r\n  /**\r\n   * Get compliance controls\r\n   */\r\n  getComplianceControls(): string[] {\r\n    return this.configuration.compliance?.controls || [];\r\n  }\r\n\r\n  /**\r\n   * Check if policy applies to system\r\n   */\r\n  appliesToSystem(systemId: string): boolean {\r\n    const applicableSystems = this.configuration.scope?.applicableSystems;\r\n    if (!applicableSystems) return true; // No restrictions means applies to all\r\n    return applicableSystems.includes(systemId);\r\n  }\r\n\r\n  /**\r\n   * Check if policy applies to role\r\n   */\r\n  appliesToRole(role: string): boolean {\r\n    const applicableRoles = this.configuration.scope?.applicableRoles;\r\n    if (!applicableRoles) return true; // No restrictions means applies to all\r\n    return applicableRoles.includes(role);\r\n  }\r\n\r\n  /**\r\n   * Generate policy summary\r\n   */\r\n  generateSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      name: this.name,\r\n      type: this.type,\r\n      severity: this.severity,\r\n      enforcementLevel: this.enforcementLevel,\r\n      isActive: this.isActive,\r\n      version: this.version,\r\n      effectiveDate: this.effectiveDate,\r\n      expirationDate: this.expirationDate,\r\n      isExpired: this.isExpired,\r\n      isDueForReview: this.isDueForReview,\r\n      isCritical: this.isCritical,\r\n      totalRules: this.totalRules,\r\n      mandatoryRules: this.mandatoryRules.length,\r\n      automatableRules: this.automatableRules.length,\r\n      complexityScore: this.calculateComplexityScore(),\r\n      complianceFrameworks: this.getComplianceFrameworks(),\r\n      tags: this.tags,\r\n      createdAt: this.createdAt,\r\n      updatedAt: this.updatedAt,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export policy for sharing\r\n   */\r\n  exportPolicy(): any {\r\n    return {\r\n      name: this.name,\r\n      description: this.description,\r\n      type: this.type,\r\n      severity: this.severity,\r\n      enforcementLevel: this.enforcementLevel,\r\n      version: this.version,\r\n      configuration: this.configuration,\r\n      metadata: this.metadata,\r\n      tags: this.tags,\r\n      exportedAt: new Date().toISOString(),\r\n      exportedBy: 'Sentinel Platform',\r\n    };\r\n  }\r\n}\r\n"], "version": 3}