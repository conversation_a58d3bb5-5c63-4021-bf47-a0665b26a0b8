{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\__tests__\\event-metadata.value-object.spec.ts", "mappings": ";;AAAA,gFAA+D;AAC/D,kFAAiE;AACjE,4EAA2D;AAC3D,kFAAwE;AAExE,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;IAC1C,IAAI,SAAyB,CAAC;IAC9B,IAAI,MAAmB,CAAC;IAExB,UAAU,CAAC,GAAG,EAAE;QACd,SAAS,GAAG,6CAAc,CAAC,MAAM,EAAE,CAAC;QACpC,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,MAAM;YACN,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAEzD,SAAS;YACT,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/B,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,aAAa,EAAE,CAAC;YACjD,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,aAAa,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,UAAU;YACV,MAAM,OAAO,GAAG;gBACd,eAAe,EAAE,UAAU;gBAC3B,aAAa,EAAE,OAAO;gBACtB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,cAAc;gBACxB,YAAY,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE;gBAC1D,gBAAgB,EAAE,WAAoB;gBACtC,eAAe,EAAE;oBACf,QAAQ,EAAE,UAAmB;oBAC7B,iBAAiB,EAAE,KAAK;oBACxB,cAAc,EAAE,KAAK;oBACrB,eAAe,EAAE,KAAK;oBACtB,aAAa,EAAE,EAAE;iBAClB;aACF,CAAC;YAEF,MAAM;YACN,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YAElE,SAAS;YACT,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC;YACpF,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC5D,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM;YACN,MAAM,QAAQ,GAAG,2CAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAEjD,SAAS;YACT,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,UAAU;YACV,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAE1D,MAAM;YACN,MAAM,QAAQ,GAAG,2CAAa,CAAC,UAAU,CACvC,wCAAe,CAAC,GAAG,EACnB,SAAS,EACT,YAAY,EACZ,EAAE,eAAe,EAAE,eAAe,EAAE,CACrC,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,GAAG,CAAC,CAAC;YACvD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC5D,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM;YACN,MAAM,QAAQ,GAAG,2CAAa,CAAC,UAAU,CAAC,wCAAe,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAE9E,SAAS;YACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC,EAAE,SAAS,EAAE,IAAW,EAAE,MAAM,EAAE,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,IAAW,EAAE,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC;oBAChB,SAAS;oBACT,MAAM;oBACN,aAAa,EAAE,iBAAiB;iBACjC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,iEAAiE,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC;oBAChB,SAAS;oBACT,MAAM;oBACN,SAAS,EAAE,CAAC,GAAG;iBAChB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC;oBAChB,SAAS;oBACT,MAAM;oBACN,QAAQ,EAAE,mBAAmB;iBAC9B,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,6CAA6C,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,kBAAkB,EAAE,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC;oBAChB,SAAS;oBACT,MAAM;oBACN,eAAe,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE;iBACtC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;YAExD,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC;oBAChB,SAAS;oBACT,MAAM;oBACN,eAAe,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE;iBACzC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC;oBAChB,SAAS;oBACT,MAAM;oBACN,eAAe,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE;iBACtC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC;oBAChB,SAAS;oBACT,MAAM;oBACN,eAAe,EAAE,EAAE,aAAa,EAAE,GAAG,EAAE;iBACxC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,2CAAa,CAAC;oBAChB,SAAS;oBACT,MAAM;oBACN,eAAe,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE;iBACzC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,UAAU;YACV,MAAM,YAAY,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,CAAC;YACjG,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;YAE3E,eAAe;YACf,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/D,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;YACzE,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,UAAU;YACV,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAEzD,eAAe;YACf,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,UAAU;YACV,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;gBACvD,YAAY,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;aACpC,CAAC,CAAC;YACH,MAAM,SAAS,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;YAE/D,MAAM;YACN,MAAM,OAAO,GAAG,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAErD,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,UAAU;YACV,MAAM,eAAe,GAAG;gBACtB,QAAQ,EAAE,UAAmB;gBAC7B,iBAAiB,EAAE,IAAI;gBACvB,cAAc,EAAE,KAAK;gBACrB,eAAe,EAAE,IAAI;gBACrB,aAAa,EAAE,GAAG;aACnB,CAAC;YACF,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;YAE9E,eAAe;YACf,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,UAAU;YACV,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAEzD,eAAe;YACf,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,UAAU;YACV,MAAM,YAAY,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;gBAC3D,eAAe,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;aACtC,CAAC,CAAC;YACH,MAAM,gBAAgB,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;gBAC/D,eAAe,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;aAC1C,CAAC,CAAC;YACH,MAAM,cAAc,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;gBAC7D,eAAe,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;aACxC,CAAC,CAAC;YAEH,eAAe;YACf,MAAM,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,UAAU;YACV,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YACzD,MAAM,QAAQ,GAAG,EAAE,QAAQ,EAAE,MAAe,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC;YAExE,MAAM;YACN,MAAM,OAAO,GAAG,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YAEvD,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,UAAU;YACV,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAEzD,MAAM;YACN,MAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAElD,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,UAAU;YACV,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAEzD,MAAM;YACN,MAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YAEtD,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,UAAU;YACV,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAEzD,MAAM;YACN,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAE7C,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,UAAU;YACV,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;gBACvD,QAAQ,EAAE,cAAc;aACzB,CAAC,CAAC;YAEH,eAAe;YACf,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;YAChF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wEAAwE,EAAE,GAAG,EAAE;YAChF,UAAU;YACV,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAEzD,eAAe;YACf,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,UAAU;YACV,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC9D,MAAM,aAAa,GAAG,6CAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAE7D,MAAM;YACN,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;YAE9B,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB;YACzE,MAAM,eAAe,GAAG,6CAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAC5D,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAE/D,eAAe;YACf,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,UAAU;YACV,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,eAAe;YAC7E,MAAM,cAAc,GAAG,6CAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAE9D,eAAe;YACf,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;QAC/E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,UAAU;YACV,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC9D,MAAM,aAAa,GAAG,6CAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,EAAE;gBAC3D,QAAQ,EAAE,QAAQ;gBAClB,YAAY,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE;gBAC1D,eAAe,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;aAC1C,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC;YAEtC,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,UAAU;YACV,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;gBACvD,eAAe,EAAE,UAAU;gBAC3B,aAAa,EAAE,OAAO;gBACtB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,QAAQ;gBAClB,YAAY,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;gBAClC,gBAAgB,EAAE,WAAW;gBAC7B,eAAe,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,EAAE,EAAE;aAC7D,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;YAE/B,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,EAAE,EAAE,CAAC,CAAC;YAClF,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,UAAU;YACV,MAAM,IAAI,GAAG;gBACX,SAAS,EAAE,SAAS,CAAC,MAAM,EAAE;gBAC7B,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE;gBACvB,eAAe,EAAE,UAAU;gBAC3B,aAAa,EAAE,OAAO;gBACtB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,QAAQ;gBAClB,YAAY,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;gBAClC,gBAAgB,EAAE,WAAW;gBAC7B,eAAe,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,EAAE,EAAE;aAC7D,CAAC;YAEF,MAAM;YACN,MAAM,QAAQ,GAAG,2CAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE9C,SAAS;YACT,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;YAC5D,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC5D,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,UAAU;YACV,MAAM,SAAS,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;gBACxD,eAAe,EAAE,UAAU;gBAC3B,aAAa,EAAE,OAAO;gBACtB,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;YACH,MAAM,SAAS,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;gBACxD,eAAe,EAAE,UAAU;gBAC3B,aAAa,EAAE,OAAO;gBACtB,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;YACH,MAAM,SAAS,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;gBACxD,eAAe,EAAE,UAAU;gBAC3B,aAAa,EAAE,OAAO;gBACtB,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;YAEH,eAAe;YACf,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,UAAU;YACV,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAEzD,MAAM;YACN,MAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAEhC,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACvC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,UAAU;YACV,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAEzD,eAAe;YACf,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,aAAa,EAAE,CAAC;YACjD,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,aAAa,EAAE,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,aAAa,EAAE,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,aAAa,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,UAAU;YACV,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;gBACvD,eAAe,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,oBAAoB;aAC5D,CAAC,CAAC;YAEH,eAAe;YACf,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU;YAClE,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU;YAC/D,MAAM,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU;YAChE,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,yBAAyB;YACzB,MAAM,CAAC,GAAG,EAAE;gBACV,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,QAAQ,GAAG,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;YAC3E,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,yBAAyB;YACzB,MAAM,CAAC,GAAG,EAAE;gBACV,2CAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC,OAAO,CAAC,6CAA6C,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\__tests__\\event-metadata.value-object.spec.ts"], "sourcesContent": ["import { EventMetadata } from '../event-metadata.value-object';\r\nimport { EventTimestamp } from '../event-timestamp.value-object';\r\nimport { EventSource } from '../event-source.value-object';\r\nimport { EventSourceType } from '../../../enums/event-source-type.enum';\r\n\r\ndescribe('EventMetadata Value Object', () => {\r\n  let timestamp: EventTimestamp;\r\n  let source: EventSource;\r\n\r\n  beforeEach(() => {\r\n    timestamp = EventTimestamp.create();\r\n    source = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create valid event metadata with minimal required information', () => {\r\n      // Act\r\n      const metadata = EventMetadata.create(timestamp, source);\r\n\r\n      // Assert\r\n      expect(metadata).toBeDefined();\r\n      expect(metadata.timestamp).toBe(timestamp);\r\n      expect(metadata.source).toBe(source);\r\n      expect(metadata.originalEventId).toBeUndefined();\r\n      expect(metadata.schemaVersion).toBeUndefined();\r\n    });\r\n\r\n    it('should create valid event metadata with all optional properties', () => {\r\n      // Arrange\r\n      const options = {\r\n        originalEventId: 'orig-123',\r\n        schemaVersion: '1.2.0',\r\n        eventSize: 1024,\r\n        checksum: 'abc123def456',\r\n        customFields: { priority: 'high', department: 'security' },\r\n        collectionMethod: 'streaming' as const,\r\n        processingHints: {\r\n          priority: 'critical' as const,\r\n          skipNormalization: false,\r\n          skipEnrichment: false,\r\n          skipCorrelation: false,\r\n          retentionDays: 90,\r\n        },\r\n      };\r\n\r\n      // Act\r\n      const metadata = EventMetadata.create(timestamp, source, options);\r\n\r\n      // Assert\r\n      expect(metadata.originalEventId).toBe('orig-123');\r\n      expect(metadata.schemaVersion).toBe('1.2.0');\r\n      expect(metadata.eventSize).toBe(1024);\r\n      expect(metadata.checksum).toBe('abc123def456');\r\n      expect(metadata.customFields).toEqual({ priority: 'high', department: 'security' });\r\n      expect(metadata.collectionMethod).toBe('streaming');\r\n      expect(metadata.processingHints?.priority).toBe('critical');\r\n      expect(metadata.processingHints?.retentionDays).toBe(90);\r\n    });\r\n\r\n    it('should create event metadata for current time', () => {\r\n      // Act\r\n      const metadata = EventMetadata.createNow(source);\r\n\r\n      // Assert\r\n      expect(metadata.timestamp).toBeDefined();\r\n      expect(metadata.source).toBe(source);\r\n      expect(metadata.timestamp.isRecent()).toBe(true);\r\n    });\r\n\r\n    it('should create event metadata from source system information', () => {\r\n      // Arrange\r\n      const specificDate = new Date('2024-01-15T10:30:00.000Z');\r\n\r\n      // Act\r\n      const metadata = EventMetadata.fromSource(\r\n        EventSourceType.EDR,\r\n        'edr-001',\r\n        specificDate,\r\n        { originalEventId: 'edr-event-123' }\r\n      );\r\n\r\n      // Assert\r\n      expect(metadata.source.type).toBe(EventSourceType.EDR);\r\n      expect(metadata.source.identifier).toBe('edr-001');\r\n      expect(metadata.timestamp.occurredAt).toEqual(specificDate);\r\n      expect(metadata.originalEventId).toBe('edr-event-123');\r\n    });\r\n\r\n    it('should create event metadata from source without timestamp', () => {\r\n      // Act\r\n      const metadata = EventMetadata.fromSource(EventSourceType.SIEM, 'splunk-001');\r\n\r\n      // Assert\r\n      expect(metadata.source.type).toBe(EventSourceType.SIEM);\r\n      expect(metadata.source.identifier).toBe('splunk-001');\r\n      expect(metadata.timestamp.isRecent()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should throw error when timestamp is not provided', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventMetadata({ timestamp: null as any, source });\r\n      }).toThrow('Event metadata must have a timestamp');\r\n    });\r\n\r\n    it('should throw error when source is not provided', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventMetadata({ timestamp, source: null as any });\r\n      }).toThrow('Event metadata must have a source');\r\n    });\r\n\r\n    it('should throw error when schema version format is invalid', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventMetadata({\r\n          timestamp,\r\n          source,\r\n          schemaVersion: 'invalid-version',\r\n        });\r\n      }).toThrow('Schema version must be in semantic version format (e.g., 1.0.0)');\r\n    });\r\n\r\n    it('should accept valid schema version formats', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventMetadata({ timestamp, source, schemaVersion: '1.0.0' });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        new EventMetadata({ timestamp, source, schemaVersion: '2.1' });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        new EventMetadata({ timestamp, source, schemaVersion: '1.0.0-beta' });\r\n      }).not.toThrow();\r\n    });\r\n\r\n    it('should throw error when event size is negative', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventMetadata({\r\n          timestamp,\r\n          source,\r\n          eventSize: -100,\r\n        });\r\n      }).toThrow('Event size cannot be negative');\r\n    });\r\n\r\n    it('should throw error when checksum format is invalid', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventMetadata({\r\n          timestamp,\r\n          source,\r\n          checksum: 'invalid-checksum!',\r\n        });\r\n      }).toThrow('Checksum must be a valid hexadecimal string');\r\n    });\r\n\r\n    it('should accept valid checksum formats', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventMetadata({ timestamp, source, checksum: 'abc123' });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        new EventMetadata({ timestamp, source, checksum: 'ABC123DEF456' });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        new EventMetadata({ timestamp, source, checksum: '0123456789abcdef' });\r\n      }).not.toThrow();\r\n    });\r\n\r\n    it('should throw error when retention days is out of range', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventMetadata({\r\n          timestamp,\r\n          source,\r\n          processingHints: { retentionDays: 0 },\r\n        });\r\n      }).toThrow('Retention days must be between 1 and 3650');\r\n\r\n      expect(() => {\r\n        new EventMetadata({\r\n          timestamp,\r\n          source,\r\n          processingHints: { retentionDays: 4000 },\r\n        });\r\n      }).toThrow('Retention days must be between 1 and 3650');\r\n    });\r\n\r\n    it('should accept valid retention days', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventMetadata({\r\n          timestamp,\r\n          source,\r\n          processingHints: { retentionDays: 1 },\r\n        });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        new EventMetadata({\r\n          timestamp,\r\n          source,\r\n          processingHints: { retentionDays: 365 },\r\n        });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        new EventMetadata({\r\n          timestamp,\r\n          source,\r\n          processingHints: { retentionDays: 3650 },\r\n        });\r\n      }).not.toThrow();\r\n    });\r\n  });\r\n\r\n  describe('custom fields handling', () => {\r\n    it('should handle custom fields correctly', () => {\r\n      // Arrange\r\n      const customFields = { priority: 'high', department: 'security', tags: ['critical', 'network'] };\r\n      const metadata = EventMetadata.create(timestamp, source, { customFields });\r\n\r\n      // Act & Assert\r\n      expect(metadata.hasCustomField('priority')).toBe(true);\r\n      expect(metadata.hasCustomField('nonexistent')).toBe(false);\r\n      expect(metadata.getCustomField('priority')).toBe('high');\r\n      expect(metadata.getCustomField('department')).toBe('security');\r\n      expect(metadata.getCustomField('tags')).toEqual(['critical', 'network']);\r\n      expect(metadata.getCustomField('nonexistent')).toBeUndefined();\r\n    });\r\n\r\n    it('should handle empty custom fields gracefully', () => {\r\n      // Arrange\r\n      const metadata = EventMetadata.create(timestamp, source);\r\n\r\n      // Act & Assert\r\n      expect(metadata.customFields).toEqual({});\r\n      expect(metadata.hasCustomField('anything')).toBe(false);\r\n      expect(metadata.getCustomField('anything')).toBeUndefined();\r\n    });\r\n\r\n    it('should create new instance with updated custom fields', () => {\r\n      // Arrange\r\n      const original = EventMetadata.create(timestamp, source, {\r\n        customFields: { existing: 'value' },\r\n      });\r\n      const newFields = { priority: 'high', department: 'security' };\r\n\r\n      // Act\r\n      const updated = original.withCustomFields(newFields);\r\n\r\n      // Assert\r\n      expect(updated).not.toBe(original);\r\n      expect(updated.hasCustomField('existing')).toBe(true);\r\n      expect(updated.hasCustomField('priority')).toBe(true);\r\n      expect(updated.hasCustomField('department')).toBe(true);\r\n      expect(original.hasCustomField('priority')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('processing hints', () => {\r\n    it('should handle processing hints correctly', () => {\r\n      // Arrange\r\n      const processingHints = {\r\n        priority: 'critical' as const,\r\n        skipNormalization: true,\r\n        skipEnrichment: false,\r\n        skipCorrelation: true,\r\n        retentionDays: 180,\r\n      };\r\n      const metadata = EventMetadata.create(timestamp, source, { processingHints });\r\n\r\n      // Act & Assert\r\n      expect(metadata.getPriority()).toBe('critical');\r\n      expect(metadata.shouldSkipNormalization()).toBe(true);\r\n      expect(metadata.shouldSkipEnrichment()).toBe(false);\r\n      expect(metadata.shouldSkipCorrelation()).toBe(true);\r\n      expect(metadata.getRetentionDays()).toBe(180);\r\n    });\r\n\r\n    it('should use default values when processing hints are not provided', () => {\r\n      // Arrange\r\n      const metadata = EventMetadata.create(timestamp, source);\r\n\r\n      // Act & Assert\r\n      expect(metadata.getPriority()).toBe('normal');\r\n      expect(metadata.shouldSkipNormalization()).toBe(false);\r\n      expect(metadata.shouldSkipEnrichment()).toBe(false);\r\n      expect(metadata.shouldSkipCorrelation()).toBe(false);\r\n      expect(metadata.getRetentionDays()).toBe(365);\r\n    });\r\n\r\n    it('should identify priority levels correctly', () => {\r\n      // Arrange\r\n      const highPriority = EventMetadata.create(timestamp, source, {\r\n        processingHints: { priority: 'high' },\r\n      });\r\n      const criticalPriority = EventMetadata.create(timestamp, source, {\r\n        processingHints: { priority: 'critical' },\r\n      });\r\n      const normalPriority = EventMetadata.create(timestamp, source, {\r\n        processingHints: { priority: 'normal' },\r\n      });\r\n\r\n      // Act & Assert\r\n      expect(highPriority.isHighPriority()).toBe(true);\r\n      expect(highPriority.isCriticalPriority()).toBe(false);\r\n      expect(criticalPriority.isHighPriority()).toBe(true);\r\n      expect(criticalPriority.isCriticalPriority()).toBe(true);\r\n      expect(normalPriority.isHighPriority()).toBe(false);\r\n      expect(normalPriority.isCriticalPriority()).toBe(false);\r\n    });\r\n\r\n    it('should create new instance with updated processing hints', () => {\r\n      // Arrange\r\n      const original = EventMetadata.create(timestamp, source);\r\n      const newHints = { priority: 'high' as const, skipNormalization: true };\r\n\r\n      // Act\r\n      const updated = original.withProcessingHints(newHints);\r\n\r\n      // Assert\r\n      expect(updated).not.toBe(original);\r\n      expect(updated.getPriority()).toBe('high');\r\n      expect(updated.shouldSkipNormalization()).toBe(true);\r\n      expect(original.getPriority()).toBe('normal');\r\n      expect(original.shouldSkipNormalization()).toBe(false);\r\n    });\r\n\r\n    it('should create new instance with updated priority', () => {\r\n      // Arrange\r\n      const original = EventMetadata.create(timestamp, source);\r\n\r\n      // Act\r\n      const updated = original.withPriority('critical');\r\n\r\n      // Assert\r\n      expect(updated).not.toBe(original);\r\n      expect(updated.getPriority()).toBe('critical');\r\n      expect(original.getPriority()).toBe('normal');\r\n    });\r\n  });\r\n\r\n  describe('event properties', () => {\r\n    it('should create new instance with checksum', () => {\r\n      // Arrange\r\n      const original = EventMetadata.create(timestamp, source);\r\n\r\n      // Act\r\n      const updated = original.withChecksum('abc123def456');\r\n\r\n      // Assert\r\n      expect(updated).not.toBe(original);\r\n      expect(updated.checksum).toBe('abc123def456');\r\n      expect(original.checksum).toBeUndefined();\r\n    });\r\n\r\n    it('should create new instance with event size', () => {\r\n      // Arrange\r\n      const original = EventMetadata.create(timestamp, source);\r\n\r\n      // Act\r\n      const updated = original.withEventSize(2048);\r\n\r\n      // Assert\r\n      expect(updated).not.toBe(original);\r\n      expect(updated.eventSize).toBe(2048);\r\n      expect(original.eventSize).toBeUndefined();\r\n    });\r\n\r\n    it('should verify integrity correctly', () => {\r\n      // Arrange\r\n      const metadata = EventMetadata.create(timestamp, source, {\r\n        checksum: 'abc123def456',\r\n      });\r\n\r\n      // Act & Assert\r\n      expect(metadata.verifyIntegrity('abc123def456')).toBe(true);\r\n      expect(metadata.verifyIntegrity('ABC123DEF456')).toBe(true); // Case insensitive\r\n      expect(metadata.verifyIntegrity('different')).toBe(false);\r\n    });\r\n\r\n    it('should return false for integrity verification when no checksum is set', () => {\r\n      // Arrange\r\n      const metadata = EventMetadata.create(timestamp, source);\r\n\r\n      // Act & Assert\r\n      expect(metadata.verifyIntegrity('any-checksum')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('age and freshness', () => {\r\n    it('should delegate age calculations to timestamp', () => {\r\n      // Arrange\r\n      const pastDate = new Date(Date.now() - 5000); // 5 seconds ago\r\n      const pastTimestamp = EventTimestamp.fromDate(pastDate);\r\n      const metadata = EventMetadata.create(pastTimestamp, source);\r\n\r\n      // Act\r\n      const age = metadata.getAge();\r\n\r\n      // Assert\r\n      expect(age).toBeGreaterThanOrEqual(4900);\r\n      expect(age).toBeLessThanOrEqual(5100);\r\n    });\r\n\r\n    it('should delegate freshness checks to timestamp', () => {\r\n      // Arrange\r\n      const recentDate = new Date(Date.now() - 2 * 60 * 1000); // 2 minutes ago\r\n      const recentTimestamp = EventTimestamp.fromDate(recentDate);\r\n      const metadata = EventMetadata.create(recentTimestamp, source);\r\n\r\n      // Act & Assert\r\n      expect(metadata.isRecent()).toBe(true);\r\n      expect(metadata.isRecent(60000)).toBe(false); // Within 1 minute\r\n    });\r\n\r\n    it('should delegate stale checks to timestamp', () => {\r\n      // Arrange\r\n      const staleDate = new Date(Date.now() - 25 * 60 * 60 * 1000); // 25 hours ago\r\n      const staleTimestamp = EventTimestamp.fromDate(staleDate);\r\n      const metadata = EventMetadata.create(staleTimestamp, source);\r\n\r\n      // Act & Assert\r\n      expect(metadata.isStale()).toBe(true);\r\n      expect(metadata.isStale(48 * 60 * 60 * 1000)).toBe(false); // Within 48 hours\r\n    });\r\n  });\r\n\r\n  describe('summary and logging', () => {\r\n    it('should provide comprehensive summary', () => {\r\n      // Arrange\r\n      const pastDate = new Date(Date.now() - 5000); // 5 seconds ago\r\n      const pastTimestamp = EventTimestamp.fromDate(pastDate);\r\n      const metadata = EventMetadata.create(pastTimestamp, source, {\r\n        checksum: 'abc123',\r\n        customFields: { priority: 'high', department: 'security' },\r\n        processingHints: { priority: 'critical' },\r\n      });\r\n\r\n      // Act\r\n      const summary = metadata.getSummary();\r\n\r\n      // Assert\r\n      expect(summary.timestamp).toBe(pastTimestamp.toISOString());\r\n      expect(summary.source).toBe(source.identifier);\r\n      expect(summary.sourceType).toBe(source.type);\r\n      expect(summary.priority).toBe('critical');\r\n      expect(summary.hasChecksum).toBe(true);\r\n      expect(summary.customFieldCount).toBe(2);\r\n      expect(typeof summary.age).toBe('number');\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    it('should convert to JSON correctly', () => {\r\n      // Arrange\r\n      const metadata = EventMetadata.create(timestamp, source, {\r\n        originalEventId: 'orig-123',\r\n        schemaVersion: '1.2.0',\r\n        eventSize: 1024,\r\n        checksum: 'abc123',\r\n        customFields: { priority: 'high' },\r\n        collectionMethod: 'streaming',\r\n        processingHints: { priority: 'critical', retentionDays: 90 },\r\n      });\r\n\r\n      // Act\r\n      const json = metadata.toJSON();\r\n\r\n      // Assert\r\n      expect(json.timestamp).toBeDefined();\r\n      expect(json.source).toBeDefined();\r\n      expect(json.originalEventId).toBe('orig-123');\r\n      expect(json.schemaVersion).toBe('1.2.0');\r\n      expect(json.eventSize).toBe(1024);\r\n      expect(json.checksum).toBe('abc123');\r\n      expect(json.customFields).toEqual({ priority: 'high' });\r\n      expect(json.collectionMethod).toBe('streaming');\r\n      expect(json.processingHints).toEqual({ priority: 'critical', retentionDays: 90 });\r\n      expect(json.summary).toBeDefined();\r\n    });\r\n\r\n    it('should create from JSON correctly', () => {\r\n      // Arrange\r\n      const json = {\r\n        timestamp: timestamp.toJSON(),\r\n        source: source.toJSON(),\r\n        originalEventId: 'orig-123',\r\n        schemaVersion: '1.2.0',\r\n        eventSize: 1024,\r\n        checksum: 'abc123',\r\n        customFields: { priority: 'high' },\r\n        collectionMethod: 'streaming',\r\n        processingHints: { priority: 'critical', retentionDays: 90 },\r\n      };\r\n\r\n      // Act\r\n      const metadata = EventMetadata.fromJSON(json);\r\n\r\n      // Assert\r\n      expect(metadata.originalEventId).toBe('orig-123');\r\n      expect(metadata.schemaVersion).toBe('1.2.0');\r\n      expect(metadata.eventSize).toBe(1024);\r\n      expect(metadata.checksum).toBe('abc123');\r\n      expect(metadata.customFields).toEqual({ priority: 'high' });\r\n      expect(metadata.collectionMethod).toBe('streaming');\r\n      expect(metadata.processingHints?.priority).toBe('critical');\r\n      expect(metadata.processingHints?.retentionDays).toBe(90);\r\n    });\r\n  });\r\n\r\n  describe('equality and comparison', () => {\r\n    it('should compare metadata for equality correctly', () => {\r\n      // Arrange\r\n      const metadata1 = EventMetadata.create(timestamp, source, {\r\n        originalEventId: 'orig-123',\r\n        schemaVersion: '1.0.0',\r\n        checksum: 'abc123',\r\n      });\r\n      const metadata2 = EventMetadata.create(timestamp, source, {\r\n        originalEventId: 'orig-123',\r\n        schemaVersion: '1.0.0',\r\n        checksum: 'abc123',\r\n      });\r\n      const metadata3 = EventMetadata.create(timestamp, source, {\r\n        originalEventId: 'orig-456',\r\n        schemaVersion: '1.0.0',\r\n        checksum: 'abc123',\r\n      });\r\n\r\n      // Act & Assert\r\n      expect(metadata1.equals(metadata2)).toBe(true);\r\n      expect(metadata1.equals(metadata3)).toBe(false);\r\n      expect(metadata1.equals(undefined)).toBe(false);\r\n      expect(metadata1.equals(metadata1)).toBe(true);\r\n    });\r\n\r\n    it('should convert to string correctly', () => {\r\n      // Arrange\r\n      const metadata = EventMetadata.create(timestamp, source);\r\n\r\n      // Act\r\n      const str = metadata.toString();\r\n\r\n      // Assert\r\n      expect(str).toContain('EventMetadata');\r\n      expect(str).toContain(source.toString());\r\n      expect(str).toContain(timestamp.toString());\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle missing optional properties gracefully', () => {\r\n      // Arrange\r\n      const metadata = EventMetadata.create(timestamp, source);\r\n\r\n      // Act & Assert\r\n      expect(metadata.originalEventId).toBeUndefined();\r\n      expect(metadata.schemaVersion).toBeUndefined();\r\n      expect(metadata.eventSize).toBeUndefined();\r\n      expect(metadata.checksum).toBeUndefined();\r\n      expect(metadata.customFields).toEqual({});\r\n      expect(metadata.collectionMethod).toBeUndefined();\r\n      expect(metadata.processingHints).toBeUndefined();\r\n    });\r\n\r\n    it('should handle partial processing hints gracefully', () => {\r\n      // Arrange\r\n      const metadata = EventMetadata.create(timestamp, source, {\r\n        processingHints: { priority: 'high' }, // Only priority set\r\n      });\r\n\r\n      // Act & Assert\r\n      expect(metadata.getPriority()).toBe('high');\r\n      expect(metadata.shouldSkipNormalization()).toBe(false); // Default\r\n      expect(metadata.shouldSkipEnrichment()).toBe(false); // Default\r\n      expect(metadata.shouldSkipCorrelation()).toBe(false); // Default\r\n      expect(metadata.getRetentionDays()).toBe(365); // Default\r\n    });\r\n\r\n    it('should handle zero event size correctly', () => {\r\n      // Arrange & Act & Assert\r\n      expect(() => {\r\n        EventMetadata.create(timestamp, source, { eventSize: 0 });\r\n      }).not.toThrow();\r\n\r\n      const metadata = EventMetadata.create(timestamp, source, { eventSize: 0 });\r\n      expect(metadata.eventSize).toBe(0);\r\n    });\r\n\r\n    it('should handle empty checksum string', () => {\r\n      // Arrange & Act & Assert\r\n      expect(() => {\r\n        EventMetadata.create(timestamp, source, { checksum: '' });\r\n      }).toThrow('Checksum must be a valid hexadecimal string');\r\n    });\r\n  });\r\n});"], "version": 3}