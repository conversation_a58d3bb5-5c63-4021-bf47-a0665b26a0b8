{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\user-management\\application\\services\\user.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAA+G;AAC/G,6CAAmD;AACnD,qCAAwE;AACxE,mEAAyD;AACzD,mEAAyD;AACzD,sFAAkF;AAClF,0FAAsF;AACtF,gGAA4F;AAE5F;;;GAGG;AAEI,IAAM,WAAW,mBAAjB,MAAM,WAAW;IAGtB,YAEE,cAAiD,EAEjD,cAAiD,EAChC,aAA4B,EAC5B,YAA0B,EAC1B,eAAgC;QALhC,mBAAc,GAAd,cAAc,CAAkB;QAEhC,mBAAc,GAAd,cAAc,CAAkB;QAChC,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;QAC1B,oBAAe,GAAf,eAAe,CAAiB;QATlC,WAAM,GAAG,IAAI,eAAM,CAAC,aAAW,CAAC,IAAI,CAAC,CAAC;IAUpD,CAAC;IAEJ;;;;;OAKG;IACH,KAAK,CAAC,MAAM,CAAC,QAAuB,EAAE,SAAiB;QACrD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE;gBACrC,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,SAAS;aACV,CAAC,CAAC;YAEH,2BAA2B;YAC3B,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACjE,MAAM,IAAI,4BAAmB,CAAC,+CAA+C,CAAC,CAAC;YACjF,CAAC;YAED,+BAA+B;YAC/B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBACrD,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE;aAC/C,CAAC,CAAC;YAEH,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,IAAI,0BAAiB,CAAC,qCAAqC,CAAC,CAAC;YACrE,CAAC;YAED,4BAA4B;YAC5B,IAAI,YAAY,GAAG,EAAE,CAAC;YACtB,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;gBAC1B,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAChF,CAAC;YAED,yCAAyC;YACzC,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;YACjC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;gBAChD,IAAI,WAAW,EAAE,CAAC;oBAChB,KAAK,GAAG,CAAC,WAAW,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC;YAED,cAAc;YACd,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;gBACtC,GAAG,QAAQ;gBACX,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE;gBACnC,YAAY;gBACZ,KAAK;gBACL,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,sBAAsB;gBACjD,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,KAAK;aAC/C,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,kBAAkB;YAClB,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,SAAS,EACT,QAAQ,EACR,MAAM,EACN,SAAS,CAAC,EAAE,EACZ;gBACE,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,KAAK,EAAE,SAAS,CAAC,SAAS;aAC3B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,EAAE;gBAC3C,MAAM,EAAE,SAAS,CAAC,EAAE;gBACpB,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,SAAS;aACV,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,SAAS;aACV,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,EAAU;QACvB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC,OAAO,CAAC;aACrB,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC3C,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,EAAE;gBACF,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,WAAW,CAAC,KAAa;QAC7B,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBACvC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,EAAE;gBACrC,SAAS,EAAE,CAAC,OAAO,CAAC;aACrB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,KAAK;gBACL,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,OAQd;QACC,IAAI,CAAC;YACH,MAAM,EACJ,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,GAAG,WAAW,EACpB,SAAS,GAAG,MAAM,GACnB,GAAG,OAAO,CAAC;YAEZ,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,MAAM,CAAC;iBAChE,iBAAiB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAE3C,gBAAgB;YAChB,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YACnE,CAAC;YAED,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,YAAY,CAAC,QAAQ,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC/D,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACX,YAAY,CAAC,QAAQ,CACnB,2FAA2F,EAC3F,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,EAAE,CAC1B,CAAC;YACJ,CAAC;YAED,gBAAgB;YAChB,YAAY,CAAC,OAAO,CAAC,QAAQ,MAAM,EAAE,EAAE,SAAS,CAAC,CAAC;YAElD,mBAAmB;YACnB,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAClC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;YAE5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE;gBACnC,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,UAAU;gBACV,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;aACnC,CAAC,CAAC;YAEH,OAAO;gBACL,KAAK;gBACL,KAAK;gBACL,IAAI;gBACJ,UAAU;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;gBACxC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,OAAO;aACR,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,UAAyB,EAAE,SAAiB;QACnE,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACrC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE;gBACjC,MAAM,EAAE,EAAE;gBACV,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;gBAC7C,SAAS;aACV,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,YAAY,GAAG;gBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,KAAK,EAAE,IAAI,CAAC,SAAS;aACtB,CAAC;YAEF,sDAAsD;YACtD,IAAI,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;gBACxD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBAC9D,IAAI,YAAY,IAAI,YAAY,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;oBAC3C,MAAM,IAAI,0BAAiB,CAAC,qCAAqC,CAAC,CAAC;gBACrE,CAAC;gBACD,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACpD,CAAC;YAED,iCAAiC;YACjC,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;gBAC5B,UAAU,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;gBAC3F,UAAU,CAAC,iBAAiB,GAAG,IAAI,IAAI,EAAE,CAAC;YAC5C,CAAC;YAED,gBAAgB;YAChB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAEhC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEzD,kBAAkB;YAClB,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,SAAS,EACT,QAAQ,EACR,MAAM,EACN,EAAE,EACF;gBACE,YAAY;gBACZ,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;aAC9C,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,EAAE;gBAC3C,MAAM,EAAE,EAAE;gBACV,SAAS;aACV,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS;aACV,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,SAAiB;QACxC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACrC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE;gBACjC,MAAM,EAAE,EAAE;gBACV,SAAS;aACV,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAEvC,kBAAkB;YAClB,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,SAAS,EACT,QAAQ,EACR,MAAM,EACN,EAAE,EACF;gBACE,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,EAAE;gBAC3C,MAAM,EAAE,EAAE;gBACV,SAAS;aACV,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS;aACV,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,WAAW,CAAC,MAAc,EAAE,OAAiB,EAAE,UAAkB;QACrE,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACzC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAC3D,IAAI,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;gBACpC,MAAM,IAAI,4BAAmB,CAAC,6BAA6B,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;YACrC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YAEnB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEzD,kBAAkB;YAClB,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,UAAU,EACV,cAAc,EACd,MAAM,EACN,MAAM,EACN;gBACE,aAAa;gBACb,QAAQ,EAAE,WAAW,CAAC,SAAS;aAChC,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,EAAE;gBACxC,MAAM;gBACN,KAAK,EAAE,WAAW,CAAC,SAAS;gBAC5B,UAAU;aACX,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,MAAM;gBACN,OAAO;gBACP,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,UAAU;aACX,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,MAAM,CACJ,KAAK,EACL,MAAM,EACN,QAAQ,EACR,SAAS,EACT,mBAAmB,EACnB,aAAa,EACb,eAAe,EAChB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;gBAC3B,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC;gBAC1D,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC;gBAC5D,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC;gBAC7D,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,sBAAsB,EAAE,EAAE,CAAC;gBACxE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,EAAE,CAAC;gBAC7D,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,EAAE,CAAC;aAC/D,CAAC,CAAC;YAEH,OAAO;gBACL,KAAK;gBACL,QAAQ,EAAE;oBACR,MAAM;oBACN,QAAQ;oBACR,SAAS;oBACT,mBAAmB;iBACpB;gBACD,mBAAmB,EAAE;oBACnB,QAAQ,EAAE,aAAa;oBACvB,UAAU,EAAE,eAAe;iBAC5B;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBACjD,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBACvC,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC3C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBAC9C,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,gBAAgB,CAAC,IAAS;QAChC,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAE9B,0BAA0B;QAC1B,OAAO,SAAS,CAAC,YAAY,CAAC;QAC9B,OAAO,SAAS,CAAC,eAAe,CAAC;QACjC,OAAO,SAAS,CAAC,kBAAkB,CAAC;QACpC,OAAO,SAAS,CAAC,sBAAsB,CAAC;QAExC,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AA9dY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;yDADU,oBAAU,oBAAV,oBAAU,oDAEV,oBAAU,oBAAV,oBAAU,oDACX,8BAAa,oBAAb,8BAAa,oDACd,4BAAY,oBAAZ,4BAAY,oDACT,kCAAe,oBAAf,kCAAe;GAVxC,WAAW,CA8dvB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\user-management\\application\\services\\user.service.ts"], "sourcesContent": ["import { Injectable, Logger, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, FindManyOptions, FindOptionsWhere } from 'typeorm';\r\nimport { User } from '../../domain/entities/user.entity';\r\nimport { Role } from '../../domain/entities/role.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { PasswordService } from '../../../../infrastructure/auth/services/password.service';\r\n\r\n/**\r\n * User service that handles user management operations\r\n * Provides CRUD operations and business logic for users\r\n */\r\n@Injectable()\r\nexport class UserService {\r\n  private readonly logger = new Logger(UserService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(User)\r\n    private readonly userRepository: Repository<User>,\r\n    @InjectRepository(Role)\r\n    private readonly roleRepository: Repository<Role>,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly auditService: AuditService,\r\n    private readonly passwordService: PasswordService,\r\n  ) {}\r\n\r\n  /**\r\n   * Create a new user\r\n   * @param userData User data\r\n   * @param createdBy User ID of the creator\r\n   * @returns Created user\r\n   */\r\n  async create(userData: Partial<User>, createdBy: string): Promise<User> {\r\n    try {\r\n      this.logger.debug('Creating new user', {\r\n        email: userData.email,\r\n        createdBy,\r\n      });\r\n\r\n      // Validate required fields\r\n      if (!userData.email || !userData.firstName || !userData.lastName) {\r\n        throw new BadRequestException('Email, first name, and last name are required');\r\n      }\r\n\r\n      // Check if user already exists\r\n      const existingUser = await this.userRepository.findOne({\r\n        where: { email: userData.email.toLowerCase() },\r\n      });\r\n\r\n      if (existingUser) {\r\n        throw new ConflictException('User with this email already exists');\r\n      }\r\n\r\n      // Hash password if provided\r\n      let passwordHash = '';\r\n      if (userData.passwordHash) {\r\n        passwordHash = await this.passwordService.hashPassword(userData.passwordHash);\r\n      }\r\n\r\n      // Get default role if no roles specified\r\n      let roles = userData.roles || [];\r\n      if (roles.length === 0) {\r\n        const defaultRole = await this.getDefaultRole();\r\n        if (defaultRole) {\r\n          roles = [defaultRole];\r\n        }\r\n      }\r\n\r\n      // Create user\r\n      const user = this.userRepository.create({\r\n        ...userData,\r\n        email: userData.email.toLowerCase(),\r\n        passwordHash,\r\n        roles,\r\n        status: userData.status || 'pending_verification',\r\n        emailVerified: userData.emailVerified || false,\r\n      });\r\n\r\n      const savedUser = await this.userRepository.save(user);\r\n\r\n      // Log audit event\r\n      await this.auditService.logUserAction(\r\n        createdBy,\r\n        'create',\r\n        'user',\r\n        savedUser.id,\r\n        {\r\n          email: savedUser.email,\r\n          firstName: savedUser.firstName,\r\n          lastName: savedUser.lastName,\r\n          roles: savedUser.roleNames,\r\n        },\r\n      );\r\n\r\n      this.logger.log('User created successfully', {\r\n        userId: savedUser.id,\r\n        email: savedUser.email,\r\n        createdBy,\r\n      });\r\n\r\n      return savedUser;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create user', {\r\n        error: error.message,\r\n        email: userData.email,\r\n        createdBy,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find user by ID\r\n   * @param id User ID\r\n   * @returns User or null\r\n   */\r\n  async findById(id: string): Promise<User | null> {\r\n    try {\r\n      const user = await this.userRepository.findOne({\r\n        where: { id },\r\n        relations: ['roles'],\r\n      });\r\n\r\n      if (!user) {\r\n        this.logger.warn('User not found', { id });\r\n        return null;\r\n      }\r\n\r\n      return user;\r\n    } catch (error) {\r\n      this.logger.error('Failed to find user by ID', {\r\n        id,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find user by email\r\n   * @param email User email\r\n   * @returns User or null\r\n   */\r\n  async findByEmail(email: string): Promise<User | null> {\r\n    try {\r\n      return await this.userRepository.findOne({\r\n        where: { email: email.toLowerCase() },\r\n        relations: ['roles'],\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to find user by email', {\r\n        email,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find users with pagination and filtering\r\n   * @param options Query options\r\n   * @returns Paginated users\r\n   */\r\n  async findMany(options: {\r\n    page?: number;\r\n    limit?: number;\r\n    status?: string[];\r\n    roles?: string[];\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n  }): Promise<{ users: User[]; total: number; page: number; totalPages: number }> {\r\n    try {\r\n      const {\r\n        page = 1,\r\n        limit = 20,\r\n        status,\r\n        roles,\r\n        search,\r\n        sortBy = 'createdAt',\r\n        sortOrder = 'DESC',\r\n      } = options;\r\n\r\n      const queryBuilder = this.userRepository.createQueryBuilder('user')\r\n        .leftJoinAndSelect('user.roles', 'role');\r\n\r\n      // Apply filters\r\n      if (status && status.length > 0) {\r\n        queryBuilder.andWhere('user.status IN (:...status)', { status });\r\n      }\r\n\r\n      if (roles && roles.length > 0) {\r\n        queryBuilder.andWhere('role.name IN (:...roles)', { roles });\r\n      }\r\n\r\n      if (search) {\r\n        queryBuilder.andWhere(\r\n          '(user.firstName ILIKE :search OR user.lastName ILIKE :search OR user.email ILIKE :search)',\r\n          { search: `%${search}%` },\r\n        );\r\n      }\r\n\r\n      // Apply sorting\r\n      queryBuilder.orderBy(`user.${sortBy}`, sortOrder);\r\n\r\n      // Apply pagination\r\n      const offset = (page - 1) * limit;\r\n      queryBuilder.skip(offset).take(limit);\r\n\r\n      const [users, total] = await queryBuilder.getManyAndCount();\r\n      const totalPages = Math.ceil(total / limit);\r\n\r\n      this.logger.debug('Users retrieved', {\r\n        total,\r\n        page,\r\n        limit,\r\n        totalPages,\r\n        filters: { status, roles, search },\r\n      });\r\n\r\n      return {\r\n        users,\r\n        total,\r\n        page,\r\n        totalPages,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to find users', {\r\n        error: error.message,\r\n        options,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update user\r\n   * @param id User ID\r\n   * @param updateData Update data\r\n   * @param updatedBy User performing the update\r\n   * @returns Updated user\r\n   */\r\n  async update(id: string, updateData: Partial<User>, updatedBy: string): Promise<User> {\r\n    try {\r\n      const user = await this.findById(id);\r\n      if (!user) {\r\n        throw new NotFoundException('User not found');\r\n      }\r\n\r\n      this.logger.debug('Updating user', {\r\n        userId: id,\r\n        updateData: this.sanitizeUserData(updateData),\r\n        updatedBy,\r\n      });\r\n\r\n      // Store original data for audit\r\n      const originalData = {\r\n        email: user.email,\r\n        firstName: user.firstName,\r\n        lastName: user.lastName,\r\n        status: user.status,\r\n        roles: user.roleNames,\r\n      };\r\n\r\n      // Check for email conflicts if email is being updated\r\n      if (updateData.email && updateData.email !== user.email) {\r\n        const existingUser = await this.findByEmail(updateData.email);\r\n        if (existingUser && existingUser.id !== id) {\r\n          throw new ConflictException('User with this email already exists');\r\n        }\r\n        updateData.email = updateData.email.toLowerCase();\r\n      }\r\n\r\n      // Hash password if being updated\r\n      if (updateData.passwordHash) {\r\n        updateData.passwordHash = await this.passwordService.hashPassword(updateData.passwordHash);\r\n        updateData.passwordChangedAt = new Date();\r\n      }\r\n\r\n      // Update fields\r\n      Object.assign(user, updateData);\r\n\r\n      const updatedUser = await this.userRepository.save(user);\r\n\r\n      // Log audit event\r\n      await this.auditService.logUserAction(\r\n        updatedBy,\r\n        'update',\r\n        'user',\r\n        id,\r\n        {\r\n          originalData,\r\n          updateData: this.sanitizeUserData(updateData),\r\n        },\r\n      );\r\n\r\n      this.logger.log('User updated successfully', {\r\n        userId: id,\r\n        updatedBy,\r\n      });\r\n\r\n      return updatedUser;\r\n    } catch (error) {\r\n      this.logger.error('Failed to update user', {\r\n        userId: id,\r\n        error: error.message,\r\n        updatedBy,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete user\r\n   * @param id User ID\r\n   * @param deletedBy User performing the deletion\r\n   */\r\n  async delete(id: string, deletedBy: string): Promise<void> {\r\n    try {\r\n      const user = await this.findById(id);\r\n      if (!user) {\r\n        throw new NotFoundException('User not found');\r\n      }\r\n\r\n      this.logger.debug('Deleting user', {\r\n        userId: id,\r\n        deletedBy,\r\n      });\r\n\r\n      await this.userRepository.remove(user);\r\n\r\n      // Log audit event\r\n      await this.auditService.logUserAction(\r\n        deletedBy,\r\n        'delete',\r\n        'user',\r\n        id,\r\n        {\r\n          email: user.email,\r\n          firstName: user.firstName,\r\n          lastName: user.lastName,\r\n        },\r\n      );\r\n\r\n      this.logger.log('User deleted successfully', {\r\n        userId: id,\r\n        deletedBy,\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to delete user', {\r\n        userId: id,\r\n        error: error.message,\r\n        deletedBy,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Assign roles to user\r\n   * @param userId User ID\r\n   * @param roleIds Array of role IDs\r\n   * @param assignedBy User performing the assignment\r\n   * @returns Updated user\r\n   */\r\n  async assignRoles(userId: string, roleIds: string[], assignedBy: string): Promise<User> {\r\n    try {\r\n      const user = await this.findById(userId);\r\n      if (!user) {\r\n        throw new NotFoundException('User not found');\r\n      }\r\n\r\n      const roles = await this.roleRepository.findByIds(roleIds);\r\n      if (roles.length !== roleIds.length) {\r\n        throw new BadRequestException('One or more roles not found');\r\n      }\r\n\r\n      const originalRoles = user.roleNames;\r\n      user.roles = roles;\r\n\r\n      const updatedUser = await this.userRepository.save(user);\r\n\r\n      // Log audit event\r\n      await this.auditService.logUserAction(\r\n        assignedBy,\r\n        'assign_roles',\r\n        'user',\r\n        userId,\r\n        {\r\n          originalRoles,\r\n          newRoles: updatedUser.roleNames,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Roles assigned to user', {\r\n        userId,\r\n        roles: updatedUser.roleNames,\r\n        assignedBy,\r\n      });\r\n\r\n      return updatedUser;\r\n    } catch (error) {\r\n      this.logger.error('Failed to assign roles to user', {\r\n        userId,\r\n        roleIds,\r\n        error: error.message,\r\n        assignedBy,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get user statistics\r\n   * @returns User statistics\r\n   */\r\n  async getStatistics(): Promise<any> {\r\n    try {\r\n      const [\r\n        total,\r\n        active,\r\n        inactive,\r\n        suspended,\r\n        pendingVerification,\r\n        emailVerified,\r\n        emailUnverified,\r\n      ] = await Promise.all([\r\n        this.userRepository.count(),\r\n        this.userRepository.count({ where: { status: 'active' } }),\r\n        this.userRepository.count({ where: { status: 'inactive' } }),\r\n        this.userRepository.count({ where: { status: 'suspended' } }),\r\n        this.userRepository.count({ where: { status: 'pending_verification' } }),\r\n        this.userRepository.count({ where: { emailVerified: true } }),\r\n        this.userRepository.count({ where: { emailVerified: false } }),\r\n      ]);\r\n\r\n      return {\r\n        total,\r\n        byStatus: {\r\n          active,\r\n          inactive,\r\n          suspended,\r\n          pendingVerification,\r\n        },\r\n        byEmailVerification: {\r\n          verified: emailVerified,\r\n          unverified: emailUnverified,\r\n        },\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to get user statistics', {\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get default role for new users\r\n   * @returns Default role or null\r\n   */\r\n  private async getDefaultRole(): Promise<Role | null> {\r\n    try {\r\n      return await this.roleRepository.findOne({\r\n        where: { isDefault: true, isActive: true },\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Error getting default role', {\r\n        error: error.message,\r\n      });\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sanitize user data for logging\r\n   * @param data User data\r\n   * @returns Sanitized data\r\n   */\r\n  private sanitizeUserData(data: any): any {\r\n    const sanitized = { ...data };\r\n    \r\n    // Remove sensitive fields\r\n    delete sanitized.passwordHash;\r\n    delete sanitized.twoFactorSecret;\r\n    delete sanitized.passwordResetToken;\r\n    delete sanitized.emailVerificationToken;\r\n    \r\n    return sanitized;\r\n  }\r\n}\r\n"], "version": 3}