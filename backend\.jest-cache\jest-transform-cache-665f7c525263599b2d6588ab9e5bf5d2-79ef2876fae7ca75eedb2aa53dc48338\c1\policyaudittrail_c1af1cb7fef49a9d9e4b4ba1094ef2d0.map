{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\policy-audit-trail.ts", "mappings": ";;;AAAA,8EAA0E;AAE1E,2GAA2F;AAC3F,uGAAsF;AAEtF,oGAAgG;AAChG,uDAAkF;AAElF,IAAY,cAeX;AAfD,WAAY,cAAc;IACxB,mDAAiC,CAAA;IACjC,mDAAiC,CAAA;IACjC,mDAAiC,CAAA;IACjC,mDAAiC,CAAA;IACjC,qDAAmC,CAAA;IACnC,2CAAyB,CAAA;IACzB,+CAA6B,CAAA;IAC7B,+CAA6B,CAAA;IAC7B,mDAAiC,CAAA;IACjC,qDAAmC,CAAA;IACnC,iDAA+B,CAAA;IAC/B,6DAA2C,CAAA;IAC3C,mDAAiC,CAAA;IACjC,iEAA+C,CAAA;AACjD,CAAC,EAfW,cAAc,8BAAd,cAAc,QAezB;AAED,IAAY,aAKX;AALD,WAAY,aAAa;IACvB,4BAAW,CAAA;IACX,kCAAiB,CAAA;IACjB,8BAAa,CAAA;IACb,sCAAqB,CAAA;AACvB,CAAC,EALW,aAAa,6BAAb,aAAa,QAKxB;AAWD,IAAY,WAOX;AAPD,WAAY,WAAW;IACrB,gCAAiB,CAAA;IACjB,0BAAW,CAAA;IACX,gCAAiB,CAAA;IACjB,gDAAiC,CAAA;IACjC,kCAAmB,CAAA;IACnB,0BAAW,CAAA;AACb,CAAC,EAPW,WAAW,2BAAX,WAAW,QAOtB;AA4BD,MAAa,UAAW,SAAQ,wBAAU;IAaxC,YAAY,KAaX;QACC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAEhB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAE1B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;QAClC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC;QACtC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;IAChC,CAAC;IAEM,MAAM,CAAC,MAAM,CAAC,KAWpB;QACC,OAAO,IAAI,UAAU,CAAC;YACpB,GAAG,KAAK;YACR,SAAS,EAAE,kCAAS,CAAC,GAAG,EAAE;SAC3B,CAAC,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,GAAW;QACvB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAEM,SAAS,CAAC,GAAW;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAEM,MAAM,CAAC,GAAW;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;IAEM,gBAAgB;QACrB,OAAO;YACL,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,SAAS;YACd,GAAG,IAAI,CAAC,KAAK;YACb,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;SAC9B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;IAC5B,CAAC;IAEO,aAAa,CAAC,KAAU;QAC9B,MAAM,MAAM,GAAU,EAAE,CAAC;QAEzB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,KAAK,CAAC,QAAQ;gBACrB,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,uBAAuB;aACjC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,WAAW;gBAClB,KAAK,EAAE,KAAK,CAAC,SAAS;gBACtB,UAAU,EAAE,MAAM;gBAClB,OAAO,EAAE,oCAAoC;aAC9C,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,KAAK,CAAC,QAAQ;gBACrB,UAAU,EAAE,MAAM;gBAClB,OAAO,EAAE,kCAAkC;aAC5C,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChE,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,aAAa;gBACpB,KAAK,EAAE,KAAK,CAAC,WAAW;gBACxB,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,+BAA+B;aACzC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,UAAU,EAAE,MAAM;gBAClB,OAAO,EAAE,iCAAiC;aAC3C,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,2BAA2B;aACrC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACtE,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,gBAAgB;gBACvB,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM;gBAC3B,UAAU,EAAE,MAAM;gBAClB,OAAO,EAAE,gCAAgC;aAC1C,CAAC,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,0CAAmB,CAAC,+BAA+B,EAAE,MAAM,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED,UAAU;IACV,IAAW,QAAQ,KAAe,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC1D,IAAW,SAAS,KAAqB,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IAClE,IAAW,QAAQ,KAAoB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC/D,IAAW,MAAM,KAAyB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAChE,IAAW,QAAQ,KAAiC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC5E,IAAW,MAAM,KAAyB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAChE,IAAW,WAAW,KAAa,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IAC9D,IAAW,OAAO,KAA0B,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC1E,IAAW,OAAO,KAAmB,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IACnE,IAAW,SAAS,KAAgB,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IAC7D,IAAW,IAAI,KAAe,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;CACxD;AA1KD,gCA0KC;AAED,MAAa,gBAAiB,SAAQ,wBAAU;IAM9C,YAAY,KAMX;QACC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAEhB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAE1B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,aAAa,CAAC;QAC1C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;IACpC,CAAC;IAEM,MAAM,CAAC,MAAM,CAAC,KAIpB;QACC,OAAO,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAEM,QAAQ,CAAC,KAAiB;QAC/B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,0CAAmB,CAAC,wDAAwD,EAAE,CAAC;oBACvF,KAAK,EAAE,UAAU;oBACjB,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK;oBAC3B,UAAU,EAAE,OAAO;oBACnB,OAAO,EAAE,wDAAwD;iBAClE,CAAC,CAAC,CAAC;QACN,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEM,qBAAqB,CAC1B,QAAwB,EACxB,UAAkB,EAClB,MAAc,EACd,OAAqB;QAErB,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;YAC9B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,SAAS,EAAE,cAAc,CAAC,cAAc;YACxC,QAAQ,EAAE,aAAa,CAAC,MAAM;YAC9B,MAAM;YACN,QAAQ;YACR,WAAW,EAAE,WAAW,UAAU,eAAe;YACjD,OAAO,EAAE,EAAE,UAAU,EAAE;YACvB,OAAO;YACP,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;SAC7B,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IAEM,qBAAqB,CAC1B,QAAwB,EACxB,MAAc,EACd,gBAAwC,EACxC,OAAqB;QAErB,MAAM,QAAQ,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC;QAEnF,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;YAC9B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,SAAS,EAAE,cAAc,CAAC,cAAc;YACxC,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,WAAW,EAAE,SAAS,MAAM,oBAAoB,gBAAgB,CAAC,MAAM,EAAE;YACzE,OAAO,EAAE;gBACP,OAAO,EAAE,gBAAgB,CAAC,OAAO;gBACjC,UAAU,EAAE,gBAAgB,CAAC,UAAU;gBACvC,MAAM,EAAE,gBAAgB,CAAC,MAAM;gBAC/B,MAAM,EAAE,gBAAgB,CAAC,MAAM;aAChC;YACD,OAAO;YACP,IAAI,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC;SAChF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IAEM,sBAAsB,CAC3B,QAAwB,EACxB,MAAc,EACd,UAAkB,EAClB,eAAsC,EACtC,MAAc,EACd,OAAqB,EACrB,OAA6B;QAE7B,MAAM,QAAQ,GAAG,eAAe,KAAK,uCAAqB,CAAC,MAAM,CAAC,CAAC;YACjE,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC;QAE5C,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;YAC9B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,SAAS,EAAE,cAAc,CAAC,eAAe;YACzC,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,MAAM;YACN,WAAW,EAAE,WAAW,UAAU,SAAS,eAAe,CAAC,WAAW,EAAE,EAAE;YAC1E,OAAO,EAAE;gBACP,UAAU;gBACV,eAAe;gBACf,GAAG,OAAO;aACX;YACD,OAAO;YACP,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,eAAe,CAAC,WAAW,EAAE,CAAC;SAC7D,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IAEM,0BAA0B,CAC/B,QAAwB,EACxB,SAAiB,EACjB,MAAc,EACd,MAAc,EACd,OAAqB,EACrB,QAAgB;QAEhB,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;YAC9B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,SAAS,EAAE,cAAc,CAAC,mBAAmB;YAC7C,QAAQ,EAAE,aAAa,CAAC,MAAM;YAC9B,MAAM;YACN,QAAQ;YACR,MAAM,EAAE,SAAS;YACjB,WAAW,EAAE,uBAAuB,SAAS,+BAA+B,MAAM,EAAE;YACpF,OAAO,EAAE;gBACP,SAAS;gBACT,MAAM;gBACN,aAAa,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;gBACpC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,iDAAiD;aAClF;YACD,OAAO;YACP,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC;SACzD,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IAEM,aAAa,CAAC,QAA6B;QAChD,IAAI,eAAe,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEzC,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC/C,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAO,CAAC,CACtD,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1D,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC/C,QAAQ,CAAC,UAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAC/C,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1D,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC/C,QAAQ,CAAC,UAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAC9C,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;YACvB,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC/C,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,SAAU,CAAC,KAAK,CACnD,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC/C,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,OAAQ,CAAC,KAAK,CACjD,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC/C,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAS,CAAC,CAC5D,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC/C,KAAK,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,CACjC,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC/C,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,CACzC,CAAC;QACJ,CAAC;QAED,wCAAwC;QACxC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAE1F,mBAAmB;QACnB,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,GAAG,CAAC;QAEpC,OAAO,eAAe,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC;IACvD,CAAC;IAEM,eAAe,CAAC,OAAe,EAAE;QACtC,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QAChD,MAAM,eAAe,GAAG,kCAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAErD,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACjD,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,eAAe,CAAC,KAAK,CAC/C,CAAC;QAEF,MAAM,YAAY,GAAmC,EAAS,CAAC;QAC/D,MAAM,gBAAgB,GAAkC,EAAS,CAAC;QAClE,MAAM,cAAc,GAAgC,EAAS,CAAC;QAC9D,MAAM,eAAe,GAAwB,IAAI,GAAG,EAAE,CAAC;QAEvD,sBAAsB;QACtB,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACtE,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QACjF,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QAEzE,eAAe;QACf,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC5B,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;YAChC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnC,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAEvC,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;gBACrC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,gBAAgB;QAChB,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;aACnD,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5B,MAAM,EAAE,6BAAM,CAAC,UAAU,CAAC,SAAS,CAAC;YACpC,UAAU,EAAE,KAAK;SAClB,CAAC,CAAC;aACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;aAC3C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhB,wCAAwC;QACxC,MAAM,cAAc,GAAG,aAAa;aACjC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;aACzE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhB,OAAO;YACL,WAAW,EAAE,aAAa,CAAC,MAAM;YACjC,YAAY;YACZ,gBAAgB;YAChB,cAAc;YACd,QAAQ;YACR,cAAc;SACf,CAAC;IACJ,CAAC;IAEM,aAAa,CAAC,QAA6B,EAAE,SAAyB,MAAM;QACjF,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAE7C,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YACrB,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW;gBACnE,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM;aAC3C,CAAC;YAEF,MAAM,OAAO,GAAG;gBACd,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;gBACjB,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;oBACtB,KAAK,CAAC,EAAE,CAAC,KAAK;oBACd,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE;oBACnC,KAAK,CAAC,SAAS;oBACf,KAAK,CAAC,QAAQ;oBACd,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;oBACzB,KAAK,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;oBAC3B,KAAK,CAAC,MAAM,IAAI,EAAE;oBAClB,IAAI,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG;oBAC5C,KAAK,CAAC,OAAO,CAAC,MAAM;oBACpB,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;iBAC7B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACb,CAAC;YAEF,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC1C,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK;YAClB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE;YAC9C,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK;YAC3B,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK;YAC/B,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,IAAI,EAAE,KAAK,CAAC,IAAI;SACjB,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC;IAEO,qBAAqB;QAC3B,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;QAC/D,MAAM,eAAe,GAAG,kCAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAErD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC3C,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,eAAe,CAAC,KAAK,CAC/C,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,KAAU;QAC9B,MAAM,MAAM,GAAU,EAAE,CAAC;QAEzB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,KAAK,CAAC,QAAQ;gBACrB,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,uBAAuB;aACjC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,WAAW;gBAClB,KAAK,EAAE,KAAK,CAAC,SAAS;gBACtB,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,gCAAgC;aAC1C,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,KAAK,CAAC,aAAa,KAAK,QAAQ,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,EAAE,CAAC;YACxE,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,KAAK,CAAC,aAAa;gBAC1B,UAAU,EAAE,UAAU;gBACtB,OAAO,EAAE,0CAA0C;aACpD,CAAC,CAAC;QACL,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,UAAU,EAAE,OAAO;gBACnB,OAAO,EAAE,0BAA0B;aACpC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,0CAAmB,CAAC,sCAAsC,EAAE,MAAM,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAEO,KAAK;QACX,qCAAqC;QACrC,+DAA+D;IACjE,CAAC;IAED,UAAU;IACV,IAAW,QAAQ,KAAe,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC1D,IAAW,OAAO,KAAmB,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjE,IAAW,aAAa,KAAa,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;IAClE,IAAW,SAAS,KAAa,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IAC1D,IAAW,UAAU,KAAa,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;CACjE;AA5XD,4CA4XC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\policy-audit-trail.ts"], "sourcesContent": ["import { BaseEntity } from '../../../../shared-kernel/domain/base-entity';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { Timestamp } from '../../../../shared-kernel/value-objects/timestamp.value-object';\r\nimport { UserId } from '../../../../shared-kernel/value-objects/user-id.value-object';\r\nimport { TenantId } from '../../../../shared-kernel/value-objects/tenant-id.value-object';\r\nimport { ValidationException } from '../../../../shared-kernel/exceptions/validation.exception';\r\nimport { PolicyEvaluationResult, PolicyExecutionStatus } from './security-policy';\r\n\r\nexport enum AuditEventType {\r\n  POLICY_CREATED = 'POLICY_CREATED',\r\n  POLICY_UPDATED = 'POLICY_UPDATED',\r\n  POLICY_DELETED = 'POLICY_DELETED',\r\n  POLICY_ENABLED = 'POLICY_ENABLED',\r\n  POLICY_DISABLED = 'POLICY_DISABLED',\r\n  RULE_ADDED = 'RULE_ADDED',\r\n  RULE_UPDATED = 'RULE_UPDATED',\r\n  RULE_REMOVED = 'RULE_REMOVED',\r\n  RULE_EVALUATED = 'RULE_EVALUATED',\r\n  ACTION_EXECUTED = 'ACTION_EXECUTED',\r\n  ACTION_FAILED = 'ACTION_FAILED',\r\n  COMPLIANCE_ASSESSED = 'COMPLIANCE_ASSESSED',\r\n  EVIDENCE_ADDED = 'EVIDENCE_ADDED',\r\n  CONFIGURATION_CHANGED = 'CONFIGURATION_CHANGED'\r\n}\r\n\r\nexport enum AuditSeverity {\r\n  LOW = 'LOW',\r\n  MEDIUM = 'MEDIUM',\r\n  HIGH = 'HIGH',\r\n  CRITICAL = 'CRITICAL'\r\n}\r\n\r\nexport interface AuditContext {\r\n  userAgent?: string;\r\n  ipAddress?: string;\r\n  sessionId?: string;\r\n  requestId?: string;\r\n  source: AuditSource;\r\n  additionalData?: Record<string, any>;\r\n}\r\n\r\nexport enum AuditSource {\r\n  WEB_UI = 'WEB_UI',\r\n  API = 'API',\r\n  SYSTEM = 'SYSTEM',\r\n  SCHEDULED_TASK = 'SCHEDULED_TASK',\r\n  WEBHOOK = 'WEBHOOK',\r\n  CLI = 'CLI'\r\n}\r\n\r\nexport interface AuditSearchCriteria {\r\n  tenantId?: TenantId;\r\n  userId?: UserId;\r\n  eventTypes?: AuditEventType[];\r\n  severities?: AuditSeverity[];\r\n  startDate?: Timestamp;\r\n  endDate?: Timestamp;\r\n  policyId?: UniqueEntityId;\r\n  ruleId?: string;\r\n  source?: AuditSource;\r\n  limit?: number;\r\n  offset?: number;\r\n}\r\n\r\nexport interface AuditSummary {\r\n  totalEvents: number;\r\n  eventsByType: Record<AuditEventType, number>;\r\n  eventsBySeverity: Record<AuditSeverity, number>;\r\n  eventsBySource: Record<AuditSource, number>;\r\n  topUsers: Array<{\r\n    userId: UserId;\r\n    eventCount: number;\r\n  }>;\r\n  recentActivity: AuditEntry[];\r\n}\r\n\r\nexport class AuditEntry extends BaseEntity {\r\n  private readonly _tenantId: TenantId;\r\n  private readonly _eventType: AuditEventType;\r\n  private readonly _severity: AuditSeverity;\r\n  private readonly _userId?: UserId;\r\n  private readonly _policyId?: UniqueEntityId;\r\n  private readonly _ruleId?: string;\r\n  private readonly _description: string;\r\n  private readonly _details: Record<string, any>;\r\n  private readonly _context: AuditContext;\r\n  private readonly _timestamp: Timestamp;\r\n  private _tags: string[];\r\n\r\n  constructor(props: {\r\n    id?: UniqueEntityId;\r\n    tenantId: TenantId;\r\n    eventType: AuditEventType;\r\n    severity: AuditSeverity;\r\n    userId?: UserId;\r\n    policyId?: UniqueEntityId;\r\n    ruleId?: string;\r\n    description: string;\r\n    details: Record<string, any>;\r\n    context: AuditContext;\r\n    timestamp: Timestamp;\r\n    tags?: string[];\r\n  }) {\r\n    super(props.id);\r\n    \r\n    this.validateProps(props);\r\n    \r\n    this._tenantId = props.tenantId;\r\n    this._eventType = props.eventType;\r\n    this._severity = props.severity;\r\n    this._userId = props.userId;\r\n    this._policyId = props.policyId;\r\n    this._ruleId = props.ruleId;\r\n    this._description = props.description;\r\n    this._details = props.details;\r\n    this._context = props.context;\r\n    this._timestamp = props.timestamp;\r\n    this._tags = props.tags || [];\r\n  }\r\n\r\n  public static create(props: {\r\n    tenantId: TenantId;\r\n    eventType: AuditEventType;\r\n    severity: AuditSeverity;\r\n    userId?: UserId;\r\n    policyId?: UniqueEntityId;\r\n    ruleId?: string;\r\n    description: string;\r\n    details: Record<string, any>;\r\n    context: AuditContext;\r\n    tags?: string[];\r\n  }): AuditEntry {\r\n    return new AuditEntry({\r\n      ...props,\r\n      timestamp: Timestamp.now()\r\n    });\r\n  }\r\n\r\n  public addTag(tag: string): void {\r\n    if (!this._tags.includes(tag)) {\r\n      this._tags.push(tag);\r\n    }\r\n  }\r\n\r\n  public removeTag(tag: string): void {\r\n    const index = this._tags.indexOf(tag);\r\n    if (index > -1) {\r\n      this._tags.splice(index, 1);\r\n    }\r\n  }\r\n\r\n  public hasTag(tag: string): boolean {\r\n    return this._tags.includes(tag);\r\n  }\r\n\r\n  public toSearchableText(): string {\r\n    return [\r\n      this._description,\r\n      this._eventType,\r\n      this._severity,\r\n      ...this._tags,\r\n      JSON.stringify(this._details)\r\n    ].join(' ').toLowerCase();\r\n  }\r\n\r\n  private validateProps(props: any): void {\r\n    const errors: any[] = [];\r\n\r\n    if (!props.tenantId) {\r\n      errors.push({\r\n        field: 'tenantId',\r\n        value: props.tenantId,\r\n        constraint: 'required',\r\n        message: 'Tenant ID is required'\r\n      });\r\n    }\r\n\r\n    if (!Object.values(AuditEventType).includes(props.eventType)) {\r\n      errors.push({\r\n        field: 'eventType',\r\n        value: props.eventType,\r\n        constraint: 'enum',\r\n        message: 'Valid audit event type is required'\r\n      });\r\n    }\r\n\r\n    if (!Object.values(AuditSeverity).includes(props.severity)) {\r\n      errors.push({\r\n        field: 'severity',\r\n        value: props.severity,\r\n        constraint: 'enum',\r\n        message: 'Valid audit severity is required'\r\n      });\r\n    }\r\n\r\n    if (!props.description || props.description.trim().length === 0) {\r\n      errors.push({\r\n        field: 'description',\r\n        value: props.description,\r\n        constraint: 'required',\r\n        message: 'Audit description is required'\r\n      });\r\n    }\r\n\r\n    if (!props.details || typeof props.details !== 'object') {\r\n      errors.push({\r\n        field: 'details',\r\n        value: props.details,\r\n        constraint: 'type',\r\n        message: 'Audit details must be an object'\r\n      });\r\n    }\r\n\r\n    if (!props.context || typeof props.context !== 'object') {\r\n      errors.push({\r\n        field: 'context',\r\n        value: props.context,\r\n        constraint: 'required',\r\n        message: 'Audit context is required'\r\n      });\r\n    } else if (!Object.values(AuditSource).includes(props.context.source)) {\r\n      errors.push({\r\n        field: 'context.source',\r\n        value: props.context.source,\r\n        constraint: 'enum',\r\n        message: 'Valid audit source is required'\r\n      });\r\n    }\r\n\r\n    if (errors.length > 0) {\r\n      throw new ValidationException('Audit entry validation failed', errors);\r\n    }\r\n  }\r\n\r\n  // Getters\r\n  public get tenantId(): TenantId { return this._tenantId; }\r\n  public get eventType(): AuditEventType { return this._eventType; }\r\n  public get severity(): AuditSeverity { return this._severity; }\r\n  public get userId(): UserId | undefined { return this._userId; }\r\n  public get policyId(): UniqueEntityId | undefined { return this._policyId; }\r\n  public get ruleId(): string | undefined { return this._ruleId; }\r\n  public get description(): string { return this._description; }\r\n  public get details(): Record<string, any> { return { ...this._details }; }\r\n  public get context(): AuditContext { return { ...this._context }; }\r\n  public get timestamp(): Timestamp { return this._timestamp; }\r\n  public get tags(): string[] { return [...this._tags]; }\r\n}\r\n\r\nexport class PolicyAuditTrail extends BaseEntity {\r\n  private readonly _tenantId: TenantId;\r\n  private _entries: AuditEntry[];\r\n  private _retentionDays: number;\r\n  private readonly _createdBy: UserId;\r\n\r\n  constructor(props: {\r\n    id?: UniqueEntityId;\r\n    tenantId: TenantId;\r\n    entries?: AuditEntry[];\r\n    retentionDays: number;\r\n    createdBy: UserId;\r\n  }) {\r\n    super(props.id);\r\n    \r\n    this.validateProps(props);\r\n    \r\n    this._tenantId = props.tenantId;\r\n    this._entries = props.entries || [];\r\n    this._retentionDays = props.retentionDays;\r\n    this._createdBy = props.createdBy;\r\n  }\r\n\r\n  public static create(props: {\r\n    tenantId: TenantId;\r\n    retentionDays: number;\r\n    createdBy: UserId;\r\n  }): PolicyAuditTrail {\r\n    return new PolicyAuditTrail(props);\r\n  }\r\n\r\n  public addEntry(entry: AuditEntry): void {\r\n    if (!entry.tenantId.equals(this._tenantId)) {\r\n      throw new ValidationException('Audit entry tenant ID must match audit trail tenant ID', [{\r\n        field: 'tenantId',\r\n        value: entry.tenantId.value,\r\n        constraint: 'match',\r\n        message: 'Audit entry tenant ID must match audit trail tenant ID'\r\n      }]);\r\n    }\r\n\r\n    this._entries.push(entry);\r\n    this.cleanupExpiredEntries();\r\n    this.touch();\r\n  }\r\n\r\n  public addPolicyCreatedEntry(\r\n    policyId: UniqueEntityId,\r\n    policyName: string,\r\n    userId: UserId,\r\n    context: AuditContext\r\n  ): void {\r\n    const entry = AuditEntry.create({\r\n      tenantId: this._tenantId,\r\n      eventType: AuditEventType.POLICY_CREATED,\r\n      severity: AuditSeverity.MEDIUM,\r\n      userId,\r\n      policyId,\r\n      description: `Policy '${policyName}' was created`,\r\n      details: { policyName },\r\n      context,\r\n      tags: ['policy', 'creation']\r\n    });\r\n\r\n    this.addEntry(entry);\r\n  }\r\n\r\n  public addRuleEvaluatedEntry(\r\n    policyId: UniqueEntityId,\r\n    ruleId: string,\r\n    evaluationResult: PolicyEvaluationResult,\r\n    context: AuditContext\r\n  ): void {\r\n    const severity = evaluationResult.matched ? AuditSeverity.HIGH : AuditSeverity.LOW;\r\n    \r\n    const entry = AuditEntry.create({\r\n      tenantId: this._tenantId,\r\n      eventType: AuditEventType.RULE_EVALUATED,\r\n      severity,\r\n      policyId,\r\n      ruleId,\r\n      description: `Rule '${ruleId}' was evaluated: ${evaluationResult.reason}`,\r\n      details: {\r\n        matched: evaluationResult.matched,\r\n        confidence: evaluationResult.confidence,\r\n        reason: evaluationResult.reason,\r\n        action: evaluationResult.action\r\n      },\r\n      context,\r\n      tags: ['rule', 'evaluation', evaluationResult.matched ? 'matched' : 'no-match']\r\n    });\r\n\r\n    this.addEntry(entry);\r\n  }\r\n\r\n  public addActionExecutedEntry(\r\n    policyId: UniqueEntityId,\r\n    ruleId: string,\r\n    actionType: string,\r\n    executionStatus: PolicyExecutionStatus,\r\n    userId: UserId,\r\n    context: AuditContext,\r\n    details?: Record<string, any>\r\n  ): void {\r\n    const severity = executionStatus === PolicyExecutionStatus.FAILED ? \r\n      AuditSeverity.HIGH : AuditSeverity.MEDIUM;\r\n\r\n    const entry = AuditEntry.create({\r\n      tenantId: this._tenantId,\r\n      eventType: AuditEventType.ACTION_EXECUTED,\r\n      severity,\r\n      userId,\r\n      policyId,\r\n      ruleId,\r\n      description: `Action '${actionType}' was ${executionStatus.toLowerCase()}`,\r\n      details: {\r\n        actionType,\r\n        executionStatus,\r\n        ...details\r\n      },\r\n      context,\r\n      tags: ['action', 'execution', executionStatus.toLowerCase()]\r\n    });\r\n\r\n    this.addEntry(entry);\r\n  }\r\n\r\n  public addComplianceAssessedEntry(\r\n    policyId: UniqueEntityId,\r\n    controlId: string,\r\n    status: string,\r\n    userId: UserId,\r\n    context: AuditContext,\r\n    findings?: any[]\r\n  ): void {\r\n    const entry = AuditEntry.create({\r\n      tenantId: this._tenantId,\r\n      eventType: AuditEventType.COMPLIANCE_ASSESSED,\r\n      severity: AuditSeverity.MEDIUM,\r\n      userId,\r\n      policyId,\r\n      ruleId: controlId,\r\n      description: `Compliance control '${controlId}' was assessed with status: ${status}`,\r\n      details: {\r\n        controlId,\r\n        status,\r\n        findingsCount: findings?.length || 0,\r\n        findings: findings?.slice(0, 5) // Limit findings in audit for storage efficiency\r\n      },\r\n      context,\r\n      tags: ['compliance', 'assessment', status.toLowerCase()]\r\n    });\r\n\r\n    this.addEntry(entry);\r\n  }\r\n\r\n  public searchEntries(criteria: AuditSearchCriteria): AuditEntry[] {\r\n    let filteredEntries = [...this._entries];\r\n\r\n    if (criteria.userId) {\r\n      filteredEntries = filteredEntries.filter(entry => \r\n        entry.userId && entry.userId.equals(criteria.userId!)\r\n      );\r\n    }\r\n\r\n    if (criteria.eventTypes && criteria.eventTypes.length > 0) {\r\n      filteredEntries = filteredEntries.filter(entry => \r\n        criteria.eventTypes!.includes(entry.eventType)\r\n      );\r\n    }\r\n\r\n    if (criteria.severities && criteria.severities.length > 0) {\r\n      filteredEntries = filteredEntries.filter(entry => \r\n        criteria.severities!.includes(entry.severity)\r\n      );\r\n    }\r\n\r\n    if (criteria.startDate) {\r\n      filteredEntries = filteredEntries.filter(entry => \r\n        entry.timestamp.value >= criteria.startDate!.value\r\n      );\r\n    }\r\n\r\n    if (criteria.endDate) {\r\n      filteredEntries = filteredEntries.filter(entry => \r\n        entry.timestamp.value <= criteria.endDate!.value\r\n      );\r\n    }\r\n\r\n    if (criteria.policyId) {\r\n      filteredEntries = filteredEntries.filter(entry => \r\n        entry.policyId && entry.policyId.equals(criteria.policyId!)\r\n      );\r\n    }\r\n\r\n    if (criteria.ruleId) {\r\n      filteredEntries = filteredEntries.filter(entry => \r\n        entry.ruleId === criteria.ruleId\r\n      );\r\n    }\r\n\r\n    if (criteria.source) {\r\n      filteredEntries = filteredEntries.filter(entry => \r\n        entry.context.source === criteria.source\r\n      );\r\n    }\r\n\r\n    // Sort by timestamp (most recent first)\r\n    filteredEntries.sort((a, b) => b.timestamp.value.getTime() - a.timestamp.value.getTime());\r\n\r\n    // Apply pagination\r\n    const offset = criteria.offset || 0;\r\n    const limit = criteria.limit || 100;\r\n    \r\n    return filteredEntries.slice(offset, offset + limit);\r\n  }\r\n\r\n  public generateSummary(days: number = 30): AuditSummary {\r\n    const cutoffDate = new Date();\r\n    cutoffDate.setDate(cutoffDate.getDate() - days);\r\n    const cutoffTimestamp = Timestamp.create(cutoffDate);\r\n\r\n    const recentEntries = this._entries.filter(entry => \r\n      entry.timestamp.value >= cutoffTimestamp.value\r\n    );\r\n\r\n    const eventsByType: Record<AuditEventType, number> = {} as any;\r\n    const eventsBySeverity: Record<AuditSeverity, number> = {} as any;\r\n    const eventsBySource: Record<AuditSource, number> = {} as any;\r\n    const userEventCounts: Map<string, number> = new Map();\r\n\r\n    // Initialize counters\r\n    Object.values(AuditEventType).forEach(type => eventsByType[type] = 0);\r\n    Object.values(AuditSeverity).forEach(severity => eventsBySeverity[severity] = 0);\r\n    Object.values(AuditSource).forEach(source => eventsBySource[source] = 0);\r\n\r\n    // Count events\r\n    recentEntries.forEach(entry => {\r\n      eventsByType[entry.eventType]++;\r\n      eventsBySeverity[entry.severity]++;\r\n      eventsBySource[entry.context.source]++;\r\n\r\n      if (entry.userId) {\r\n        const userIdStr = entry.userId.value;\r\n        userEventCounts.set(userIdStr, (userEventCounts.get(userIdStr) || 0) + 1);\r\n      }\r\n    });\r\n\r\n    // Get top users\r\n    const topUsers = Array.from(userEventCounts.entries())\r\n      .map(([userIdStr, count]) => ({\r\n        userId: UserId.fromString(userIdStr),\r\n        eventCount: count\r\n      }))\r\n      .sort((a, b) => b.eventCount - a.eventCount)\r\n      .slice(0, 10);\r\n\r\n    // Get recent activity (last 10 entries)\r\n    const recentActivity = recentEntries\r\n      .sort((a, b) => b.timestamp.value.getTime() - a.timestamp.value.getTime())\r\n      .slice(0, 10);\r\n\r\n    return {\r\n      totalEvents: recentEntries.length,\r\n      eventsByType,\r\n      eventsBySeverity,\r\n      eventsBySource,\r\n      topUsers,\r\n      recentActivity\r\n    };\r\n  }\r\n\r\n  public exportEntries(criteria: AuditSearchCriteria, format: 'json' | 'csv' = 'json'): string {\r\n    const entries = this.searchEntries(criteria);\r\n\r\n    if (format === 'csv') {\r\n      const headers = [\r\n        'ID', 'Timestamp', 'Event Type', 'Severity', 'User ID', 'Policy ID', \r\n        'Rule ID', 'Description', 'Source', 'Tags'\r\n      ];\r\n      \r\n      const csvRows = [\r\n        headers.join(','),\r\n        ...entries.map(entry => [\r\n          entry.id.value,\r\n          entry.timestamp.value.toISOString(),\r\n          entry.eventType,\r\n          entry.severity,\r\n          entry.userId?.value || '',\r\n          entry.policyId?.value || '',\r\n          entry.ruleId || '',\r\n          `\"${entry.description.replace(/\"/g, '\"\"')}\"`,\r\n          entry.context.source,\r\n          `\"${entry.tags.join(', ')}\"`\r\n        ].join(','))\r\n      ];\r\n\r\n      return csvRows.join('\\n');\r\n    }\r\n\r\n    return JSON.stringify(entries.map(entry => ({\r\n      id: entry.id.value,\r\n      timestamp: entry.timestamp.value.toISOString(),\r\n      eventType: entry.eventType,\r\n      severity: entry.severity,\r\n      userId: entry.userId?.value,\r\n      policyId: entry.policyId?.value,\r\n      ruleId: entry.ruleId,\r\n      description: entry.description,\r\n      details: entry.details,\r\n      context: entry.context,\r\n      tags: entry.tags\r\n    })), null, 2);\r\n  }\r\n\r\n  private cleanupExpiredEntries(): void {\r\n    const cutoffDate = new Date();\r\n    cutoffDate.setDate(cutoffDate.getDate() - this._retentionDays);\r\n    const cutoffTimestamp = Timestamp.create(cutoffDate);\r\n\r\n    this._entries = this._entries.filter(entry => \r\n      entry.timestamp.value >= cutoffTimestamp.value\r\n    );\r\n  }\r\n\r\n  private validateProps(props: any): void {\r\n    const errors: any[] = [];\r\n\r\n    if (!props.tenantId) {\r\n      errors.push({\r\n        field: 'tenantId',\r\n        value: props.tenantId,\r\n        constraint: 'required',\r\n        message: 'Tenant ID is required'\r\n      });\r\n    }\r\n\r\n    if (!props.createdBy) {\r\n      errors.push({\r\n        field: 'createdBy',\r\n        value: props.createdBy,\r\n        constraint: 'required',\r\n        message: 'Created by user ID is required'\r\n      });\r\n    }\r\n\r\n    if (typeof props.retentionDays !== 'number' || props.retentionDays <= 0) {\r\n      errors.push({\r\n        field: 'retentionDays',\r\n        value: props.retentionDays,\r\n        constraint: 'positive',\r\n        message: 'Retention days must be a positive number'\r\n      });\r\n    }\r\n\r\n    if (props.entries && !Array.isArray(props.entries)) {\r\n      errors.push({\r\n        field: 'entries',\r\n        value: props.entries,\r\n        constraint: 'array',\r\n        message: 'Entries must be an array'\r\n      });\r\n    }\r\n\r\n    if (errors.length > 0) {\r\n      throw new ValidationException('Policy audit trail validation failed', errors);\r\n    }\r\n  }\r\n\r\n  private touch(): void {\r\n    // Update the last modified timestamp\r\n    // This would typically update an updatedAt field if we had one\r\n  }\r\n\r\n  // Getters\r\n  public get tenantId(): TenantId { return this._tenantId; }\r\n  public get entries(): AuditEntry[] { return [...this._entries]; }\r\n  public get retentionDays(): number { return this._retentionDays; }\r\n  public get createdBy(): UserId { return this._createdBy; }\r\n  public get entryCount(): number { return this._entries.length; }\r\n}"], "version": 3}